<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 18:05
 */

class Dbsqlplay{
    private $Connection;//定义数据库连接变量
    public $ServerName = WriteServerName;
    public $UserName = WriteUserName;
    public $PassWord = WritePassWord;
    public $DBName = WriteDBName;
    public $errorsql = "";

    /**
     * 功能：构造函数
     **/
    public function __construct($ServerName="",$UserName="",$PassWord="",$DBName=""){
        if($ServerName!==''){
            $this->ServerName = $ServerName;
        }
        if($UserName!==''){
            $this->UserName = $UserName;
        }
        if($PassWord!==''){
            $this->PassWord = $PassWord;
        }
        if($DBName!==''){
            $this->DBName = $DBName;
        }

        try {
            $linkserver = @mysql_connect($this->ServerName,$this->UserName,$this->PassWord);
            if(!$linkserver){
                throw new WebException("Can not contact server");
            }

        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        try {											//捕获数据库选择错误并显示错误
            $linkdb = mysql_select_db($this->DBName,$linkserver);
            if(!$linkdb){
                throw new WebException("Can not find the database");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        $this->Connection = $linkserver;
        mysql_query("SET NAMES ".MHS_CODING,$this->Connection);//设置数据编码*/
    }
    /**
     * 功能：数据库查询函数
     * 参数：$sql SQL语句
     * 返回：二唯数组或false

    $start_time = microtime(true);
    mysql_query($your_sql);
    $sec_time[] = microtime(true) - $start_time;

     */
    public function select($sql = ""){
        if (empty($sql) || empty($this->Connection)) return false;					//如果SQL语句为空或果连接为空则返回FALSE

        $start_time = microtime(true);//97添加

        try{											//捕获数据库选择错误并显示错误文件
            $results = mysql_query($sql,$this->Connection);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        if(microtime(true) - $start_time > 10){//97添加
            $time =array();
            $time['querytime'] = microtime(true) - $start_time;
            $time['querysql'] = mysql_real_escape_string($sql);
            $this->insertData('cms_sqlquerytime',$time);
        }

        if ((!$results) or (empty($results))) {			//如果查询结果为空则释放结果并返回FALSE
            @mysql_free_result($results);
            return false;
        }
        if (mysql_num_rows($results) == 0) {			//如果查询结果数为空
            @mysql_free_result($results);
            return false;
        }
        $count = 0;
        $data = array();
        while(($row = mysql_fetch_array($results)) != false){	//把查询结果重组成一个二维数组
            $data[$count] = $row;
            $count++;
        }
        @mysql_free_result($results);

        return $data;
    }

    /**
     * 功能：数据库查询函数
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function selectClear($sql = ""){
        if (empty($sql) || empty($this->Connection)) return false;					//如果SQL语句为空或果连接为空则返回FALSE

        $start_time = microtime(true);//97添加

        try{											//捕获数据库选择错误并显示错误文件
            $results = mysql_query($sql,$this->Connection);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        if(microtime(true) - $start_time > 10){//97添加
            $time =array();
            $time['querytime'] = microtime(true) - $start_time;
            $time['querysql'] = mysql_real_escape_string($sql);
            $this->insertData('cms_sqlquerytime',$time);
        }

        if ((!$results) or (empty($results))) {			//如果查询结果为空则释放结果并返回FALSE
            @mysql_free_result($results);
            return false;
        }
        if (mysql_num_rows($results) == 0) {			//如果查询结果数为空
            @mysql_free_result($results);
            return false;
        }
        $count = 0;
        $data = array();
        while(($row = mysql_fetch_array($results,MYSQL_ASSOC)) != false){	//把查询结果重组成一个二维数
            $data[$count] = $row;
            $count++;
        }
        @mysql_free_result($results);

        return $data;
    }
    /**
     * 功能：数据库查询过滤模式函数
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function selectspl($sql = ""){
        if(ADAROTEST && !($this->select($sql))){
            die($sql);
        }
        return $this->selectClear($sql);
    }
    /**
     * 功能：数据库查询第一条数据
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function selectOne($sql = ""){
        $selectList = $this->selectClear($sql);
        if($selectList){
            return $selectList[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：数据插入函数
     * 参数：$sql SQL语句
     * 返回：false或新插入数据的ID
     */
    public function insert($sql = ""){
        if (empty($sql) || empty($this->Connection)) return false;					//如果SQL语句为空或果连接为空则返回FALSE
        try{											//捕获数据库选择错误并显示错误文件
            $results = mysql_query($sql,$this->Connection);
        }catch (WebException $e){
            die($e->errorMessage());
        }

        if (!$results){									//如果插入失败返回0，否则返回当前插入数据ID
            return false;
        }else{
            $insetid = @mysql_insert_id($this->Connection);
            if($insetid){
                return $insetid;
            }else{
                return true;
            }
        }
    }
    /**
     * 功能：数据操作函数
     * 参数：$sql SQL语句
     * 返回：false或新插入数据的ID
     */
    public function query($sql = ""){
        if (empty($sql) || empty($this->Connection)) return false;					//如果SQL语句为空或果连接为空则返回FALSE
        try{											//捕获数据库选择错误并显示错误文件
            $results = mysql_query($sql,$this->Connection);
            if(!$results){
                throw new WebException("Failed to query to ".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }
        return $results;
    }
    /**数据空数组处理**/
    public function Dateemtyspl($datearray)
    {
        $date = array();
        if(count($datearray)>0){
            foreach($datearray as $k=>$datevar){
                if($datevar !=='' && count($datevar) > 0){
                    $date[$k] = $datevar;
                }
            }
        }
        return $date;
    }
    /**
     * 功能：查询某表莫字段是否存在
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function queryField($tablename,$fieldname){
        $sqlbak = mysql_query('Describe '.$tablename.' '.$fieldname,$this->Connection);
        $test = mysql_fetch_array($sqlbak);
        if($test[0]){
            return true;
        }else{
            return false;
        }
    }
    /**
     * 功能：查询某表是否存在
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function table_exists($tablename) {
        $database = DBName;
        $sql = mysql_query("SELECT COUNT(*) AS count FROM information_schema.tables WHERE table_schema = '".DBName."' AND table_name = '$tablename'",$this->Connection);
        return mysql_result($sql, 0) == 1;
    }
    /**
     * 功能：分页读取表
     * 参数：$tname表名,,$where 查询条件,$sub_nums开始数,$page_size结束数,$ordering排序方式;
     * 返回：数组
     */
    public function getListpage($tname,$where = '',$sub_nums,$page_size,$ordering = ''){
        $sql = "SELECT * FROM " . $tname;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($ordering)
        {
            $sql .= " ".$ordering;
        }
        $sql .= " LIMIT $sub_nums,$page_size";

        return $this->selectspl($sql);
    }
    /**
     * 功能：读取表
     * 参数：$name 表名 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getList($name,$where,$arrayal=''){
        $sql = "SELECT * FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        return $this->selectspl($sql);
    }
    /**
     * 功能：单个数据查询
     * 参数：$where条件,$name 表名称
     * 返回：值
     */
    public function getOne($name,$where,$arrayal = '')
    {
        $sql = "SELECT * FROM " . $name . " WHERE ".$where.$arrayal;
        //echo $sql;
        $r = $this->selectspl($sql);
        if($r[0]){
            return $r[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：自定字段排序读取表
     * 参数：$name 表名 $fields 查询的字段 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getFieldOne($name,$fields,$where,$arrayal = 'limit 0,1'){
        $sql = "SELECT ".$fields." FROM " . $name;


        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        $r = $this->selectspl($sql);
        if($r){
            return $r[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：自定字段排序读取表
     * 参数：$name 表名 $fields 查询的字段 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getFieldquery($name,$fields,$where,$arrayal = ''){
        $sql = "SELECT ".$fields." FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        return $this->selectspl($sql);
    }
    /**
     *功能:统计记录条数
     *参数:$tname 表名,$where 条件
     *返回:记录条数
     **/

    public function getCount($tname,$where = '')
    {
        $sql = "SELECT COUNT(*) FROM " . $tname;
        if($where){
            $sql .= ' WHERE '.$where;
        }
        $r = $this->selectspl($sql);
        return $r[0][0];
    }
    /**
     * 功能：向指定表中插入数据
     * 参数：$name 表名称,$data 数组(格式：$data['字段名'] = 值)
     * 返回：插入记录ID
     */
    public function insertData($name,$data)
    {
        $data = $this->Dateemtyspl($data);
        $field = implode(',',array_keys($data));					//定义sql语句的字段部分
        $i = 0;
        $value = '';
        foreach($data as $val)
        {
            $value .= "'" . str_replace("'","'",$val) . "'";

            if($i < count($data) - 1)								//判断是否到数组的最后一个值
                $value .= ",";
            $i++;
        }
        $sql = "INSERT INTO " . $name . "(" . $field . ") VALUES(" . $value . ")";
        $inset = $this->insert($sql);
        if(!$inset){
           $this->errorsql = $sql;
        }

        return $inset;
    }
    /**
     * 功能：更新指定表指定表记录
     * 参数：$name 表名称,$data 数组(格式：$data['字段名'] = 值),$where
     * 返回：TRUE OR FALSE
     */
    public function updateData($name,$where,$data){
        $col = array();
        foreach ($data as $key => $value)
        {
            $col[] = $key . "='" . str_replace("'","'",$value) . "'";
        }
        $sql = "UPDATE " . $name . " SET " . implode(',',$col) . " WHERE ". $where;
        $inset = $this->query($sql);
        if(!$inset){
            $this->errorsql = $sql;
        }
        return $inset;
    }
    /**
     * 功能：删除指定条件记录
     * 参数：$name 表名称,$where
     * 返回：TRUE OR FALSE
     */
    public function delData($name,$where)
    {
        $sql = "DELETE FROM " . $name . " WHERE ".$where;
        return $this->query($sql);
    }

    /**
     * 功能：数据表(分页显示)
     * 参数：$sql栏目数据表，$p分页数，$num每页显示数量,$time有无1,0,$url连接,$page_sub每页多少条，$pagecss分页样式（1,2,3）
     * 返回：字符串类型
     */
    public function dbwherePage($sql,$allnum,$num,$url,$p='',$page_sub='10',$pagecss){
        /**分页处理**/
        $db_nums = $allnum;//相关条件下的总记录数
        $page_size=$num;//每页显示的条数
        $sub_pages=$page_sub;//每次显示的页
        if($p=='') {
            $pageCurrent=1;
        }else {
            $pageCurrent=$p;//得到当前是第几页数
        }
        $subPages = new \SetPages($page_size,$db_nums,$pageCurrent,$sub_pages,$url,$pagecss,'');
        $sub_nums = ($pageCurrent-1)*$page_size;
        /**分页处理**/
        $db_list = $this->select($sql." LIMIT $sub_nums,$page_size");
        $db_array['cont'] = $db_list;
        $db_array['cont_num'] = $db_nums;
        $db_array['pages'] = $subPages->show_SubPages();
        return $db_array;
    }

    /**
     * 功能：定义事务
     */
    public function begintransaction()
    {
        mysql_query("SET  AUTOCOMMIT=0");				//设置为不自动提交，因为MYSQL默认立即执行
        mysql_query("BEGIN");							//开始事务定义
    }
    /**
     * 功能：回滚
     */
    public function rollback()
    {
        mysql_query("ROOLBACK");
    }
    /**
     * 功能：提交执行
     */
    public function commit()
    {
        mysql_query("COMMIT");
    }

}
