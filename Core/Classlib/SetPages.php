<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/14
 * Time: 1:09
 */

class SetPages {
    private  $each_disNums;//每页显示的条目数
    private  $nums;//总条目数
    private  $current_page;//当前被选中的页
    private  $sub_pages;//每次显示的页数
    private  $pageNums;//总页数
    private  $page_array = array();//用来构造分页的数组
    private  $subPage_link;//每个分页的链接
    private  $subPage_type;//显示分页的类型
    private  $html_type = 'html';//伪静态后缀

    /*
    __construct是SubPages的构造函数，用来在创建类的时候自动运行.
    @$each_disNums  每页显示的条目数
    @nums    总条目数
    @current_num    当前被选中的页
    @sub_pages      每次显示的页数
    @subPage_link   每个分页的链接
    @subPage_type   显示分页的类型

    当@subPage_type=1的时候为普通分页模式
          example：  共4523条记录,每页显示10条,当前第1/453页 [首页] [上页] [下页] [尾页]
          当@subPage_type=2的时候为经典分页样式
          example：  当前第1/453页 [首页] [上页] 1 2 3 4 5 6 7 8 9 10 [下页] [尾页]
    */
    function __construct($each_disNums,$nums,$current_page,$sub_pages,$subPage_link,$subPage_type,$html_type=''){
        $this->each_disNums=intval($each_disNums);
        $this->nums=intval($nums);
        if(!$current_page){
            $this->current_page=1;
        }else{
            $this->current_page=intval($current_page);
        }
        $this->sub_pages=intval($sub_pages);
        $this->pageNums=ceil($nums/$each_disNums);
        $this->subPage_link=$subPage_link;
        $this->subPage_type = $subPage_type;
        $this->html_type=$html_type;
        //echo $this->pageNums."--".$this->sub_pages;
    }


    /*
     __destruct析构函数，当类不在使用的时候调用，该函数用来释放资源。
    */
    function __destruct(){
        unset($each_disNums);
        unset($nums);
        unset($current_page);
        unset($sub_pages);
        unset($pageNums);
        unset($page_array);
        unset($subPage_link);
        unset($subPage_type);
    }

    /*
     show_SubPages函数用在构造函数里面。而且用来判断显示什么样子的分页
    */
    function show_SubPages()
    {
        $subPage_type = $this->subPage_type;
        if ($subPage_type == 1) {
            return $this->subPageCss1();
        } elseif ($subPage_type == 2) {
            return $this->subPageCss2();
        } elseif ($subPage_type == 3) {
            return $this->subPageCss3();
        } elseif ($subPage_type == 4) {
            return $this->subPageCss4();
        } elseif ($subPage_type == 5) {
            return $this->subPageCss5();
        } elseif ($subPage_type == 6) {
            return $this->subPageCss6();
        }elseif ($subPage_type == 7) {
            return $this->subPageCss7();
        }elseif ($subPage_type == 8) {//帮助中心搜索分页样式
            return $this->subPageCss8();
        }
    }


    /*
     用来给建立分页的数组初始化的函数。
    */
    function initArray(){
        for($i=0;$i<$this->sub_pages;$i++){
            $this->page_array[$i]=$i;
        }
        return $this->page_array;
    }


    /*
     construct_num_Page该函数使用来构造显示的条目
     即使：[1][2][3][4][5][6][7][8][9][10]
    */
    function construct_num_Page(){
        if($this->pageNums < $this->sub_pages){
            $current_array=array();
            for($i=0;$i<$this->pageNums;$i++){
                $current_array[$i]=$i+1;
            }
        }else{
            $current_array=$this->initArray();
            if($this->current_page <= 3){
                for($i=0;$i<count($current_array);$i++){
                    $current_array[$i]=$i+1;
                }
            }elseif ($this->current_page <= $this->pageNums && $this->current_page > $this->pageNums - $this->sub_pages + 1 ){
                for($i=0;$i<count($current_array);$i++){
                    $current_array[$i]=($this->pageNums)-($this->sub_pages)+1+$i;
                }
            }else{
                for($i=0;$i<count($current_array);$i++){
                    $current_array[$i]=$this->current_page-2+$i;
                }
            }
        }

        return $current_array;
    }

    /*
    构造普通模式的分页
    共4523条记录,每页显示10条,当前第1/453页 [首页] [上页] [下页] [尾页]
    */
    function subPageCss1(){
        $subPageCss1Str="";
        $subPageCss1Str.="共".$this->nums."条记录，";
        $subPageCss1Str.="每页显示".$this->each_disNums."条，";
        $subPageCss1Str.="当前第".$this->current_page."/".$this->pageNums."页 ";
        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            $subPageCss1Str.="[<a href='$firstPageUrl'>首页</a>] ";
            $subPageCss1Str.="[<a href='$prewPageUrl'>上一页</a>] ";
        }else {
            $subPageCss1Str.="[首页] ";
            $subPageCss1Str.="[上一页] ";
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss1Str.=" [<a href='$nextPageUrl'>下一页</a>] ";
            $subPageCss1Str.="[<a href='$lastPageUrl'>尾页</a>] ";
        }else {
            $subPageCss1Str.="[下一页] ";
            $subPageCss1Str.="[尾页] ";
        }

        return  $subPageCss1Str;

    }

    /*
   构造经典模式的分页
   当前第1/453页 [首页] [上页] 1 2 3 4 5 6 7 8 9 10 [下页] [尾页]
   */
    function subPageCss2(){
        $subPageCss2Str="";
        $subPageCss2Str.="共".$this->nums."条记录，";
        $subPageCss2Str.="当前第".$this->current_page."/".$this->pageNums."页 ";


        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            $subPageCss2Str.="<a href='$firstPageUrl' i18n-text data-key='pagination::home'>首页</a> ";
            $subPageCss2Str.="<a href='$prewPageUrl' i18n-text data-key='pagination::prev'>上一页</a> ";
        }else {
            $subPageCss2Str.="<span class=\"disabled\" i18n-text data-key='pagination::home'>首页</span>";
            $subPageCss2Str.="<span class=\"disabled\" i18n-text data-key='pagination::prev'>上一页</span>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.="<span class=\"current\">".$s."</span>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;
                $subPageCss2Str.="<a href='$url'>".$s."</a>";
            }
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.=" <a href='$nextPageUrl' i18n-text data-key='pagination::next'>下一页</a> ";
            $subPageCss2Str.="<a href='$lastPageUrl' i18n-text data-key='pagination::end'>尾页</a> ";
        }else {
            $subPageCss2Str.="<span class=\"disabled\" i18n-text data-key='pagination::next'>下一页 </span>";
            $subPageCss2Str.="<span class=\"disabled\" i18n-text data-key='pagination::end'>尾页 </span>";
        }
        return  $subPageCss2Str;
    }
    /*
   构造经典模式的分页
   当前第1/453页 [首页] [上页] 1 2 3 4 5 6 7 8 9 10 [下页] [尾页]
   */
    function subPageCss3(){
        $subPageCss2Str="";
        /*$subPageCss2Str.="Total ".$this->nums;
        $subPageCss2Str.=" Current page".$this->current_page."/".$this->pageNums;*/


        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            $subPageCss2Str.="<a href='$firstPageUrl'><<</a> ";
            $subPageCss2Str.="<a href='$prewPageUrl'><</a> ";
        }else {
            $subPageCss2Str.="<span class=\"disabled\"><<</span>";
            $subPageCss2Str.="<span class=\"disabled\"><</span>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.="<span class=\"current\">".$s."</span>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;
                $subPageCss2Str.="<a href='$url'>".$s."</a>";
            }
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.=" <a href='$nextPageUrl'>></a> ";
            $subPageCss2Str.="<a href='$lastPageUrl'>>></a> ";
        }else {
            $subPageCss2Str.="<span class=\"disabled\">></span>";
            $subPageCss2Str.="<span class=\"disabled\">>></span>";
        }
        return  $subPageCss2Str;
    }

    /* ---- apsoto  供应商后台分页
    构造经典模式的分页
    当前第1/453页 [首页] [上页] 1 2 3 4 5 6 7 8 9 10 [下页] [尾页]
    */
    function subPageCss4(){
        $subPageCss2Str="";
        /*$subPageCss2Str.="共".$this->nums."条记录，";
        $subPageCss2Str.="当前第".$this->current_page."/".$this->pageNums."页 ";*/

        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            /*$subPageCss2Str.="<a href='$firstPageUrl'>首页</a> ";*/
            $subPageCss2Str.="<li class=\"prePage disabled\"><a href=\"$prewPageUrl\">上一页</a></li> ";
        }else {
            /*$subPageCss2Str.="<span class=\"disabled\">首页</span>";*/
            $subPageCss2Str.="<li class=\"prePage disabled\"><a class=\"disabled\">上一页</a></li>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.="<li class=\"normalPage\"><a href=\"#\" class=\"current cur\">".$s."</a></li>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;
                $subPageCss2Str.="<li class=\"normalPage\"><a href=\"$url\" class=\"\">".$s."</a></li>";
            }
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.="<li class=\"nextPage\"><a href=\"$nextPageUrl\">下一页</a></li>";
            /*$subPageCss2Str.="<a href='$lastPageUrl'>尾页</a> ";*/
        }else {
            $subPageCss2Str.="<li class=\"nextPage\"><a class=\"disabled\">下一页</a></li>";
            /*$subPageCss2Str.="<span class=\"disabled\">尾页 </span>";*/
        }
        return  $subPageCss2Str;
    }

    function subPageCss5(){
        $subPageCss2Str="";
        $subPageCss2Str.="<span class=\"jilu\">共计".$this->nums."条</span>";
        $subPageCss2Str.="<span class=\"jilu\">".$this->current_page."/".$this->pageNums."</span>";

        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            $subPageCss2Str.="<a href='$firstPageUrl'>首页</a> ";
            $subPageCss2Str.="<a href=\"$prewPageUrl\">上一页</a>";
        }else {
            /*$subPageCss2Str.="<span class=\"disabled\">首页</span>";*/
//            $subPageCss2Str.="<li class=\"prePage disabled\"><a class=\"disabled\">上一页</a></li>";

            $subPageCss2Str.="<span class=\"disabled\">首页</span> ";
            $subPageCss2Str.="<span class=\"disabled\">上一页</span>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.=" <span class=\"current\">".$s."</span>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;
                $subPageCss2Str.="<a href=\"$url\" >".$s."</a>";
            }
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.="<a href=\"$nextPageUrl\">下一页</a>";
            $subPageCss2Str.="<a href='$lastPageUrl'>尾页</a> ";
        }else {
            $subPageCss2Str.="<a class=\"disabled\">下一页</a>";
            $subPageCss2Str.="<a class=\"disabled\">尾页 </a>";
        }
        return  $subPageCss2Str;
    }

    //接口分页
    function subPageCss6(){
        $subPageCss2Str="";
        /*$subPageCss2Str.="共".$this->nums."条记录，";
        $subPageCss2Str.="当前第".$this->current_page."/".$this->pageNums."页 ";*/

        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            /*$subPageCss2Str.="<a href='$firstPageUrl'>首页</a> ";*/
            $subPageCss2Str.="<li class=\"prePage disabled\"><a href='javascript:;' >上一页</a></li> ";
        }else {
            /*$subPageCss2Str.="<span class=\"disabled\">首页</span>";*/
            $subPageCss2Str.="<li class=\"prePage disabled\"><a class=\"disabled\" href='javascript:;' >上一页</a></li>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.="<li class=\"normalPage\"><a href='javascript:;'  class=\"current cur\">".$s."</a></li>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;

                $subPageCss2Str.="<li class=\"normalPage\"><a href='javascript:;' class=\"\">".$s."</a></li>";
            }
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.="<li class=\"nextPage\"><a href='javascript:;'>下一页</a></li>";
            /*$subPageCss2Str.="<a href='$lastPageUrl'>尾页</a> ";*/
        }else {
            $subPageCss2Str.="<li class=\"nextPage\"><a class=\"disabled\"   href='javascript:;'>下一页</a></li>";
            /*$subPageCss2Str.="<span class=\"disabled\">尾页 </span>";*/
        }
        return  $subPageCss2Str;
    }


    function subPageCss7(){
        $subPageCss2Str="";
        /*$subPageCss2Str.="共".$this->nums."条记录，";
        $subPageCss2Str.="当前第".$this->current_page."/".$this->pageNums."页 ";*/

        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            /*$subPageCss2Str.="<a data='$firstPageUrl'>首页</a> ";*/
            $subPageCss2Str.="<li class=\"prePage \"><span data-url=\"$prewPageUrl\" class=\"nextone\">上一页</span></li> ";
        }else {
            /*$subPageCss2Str.="<span class=\"disabled\">首页</span>";*/
            $subPageCss2Str.="<li class=\"prePage disabled\"><span class=\"disabled\">上一页</span></li>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.="<li class=\"normalPage\"><span data-url=\"#\" class=\"current cur\">".$s."</span></li>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;
                $subPageCss2Str.="<li class=\"normalPage\"><span data-url=\"$url\" class=\"nextone\">".$s."</span></li>";
            }
        }

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.="<li class=\"nextPage\"><span data-url=\"$nextPageUrl\" class=\"nextone\">下一页</span></li>";
            /*$subPageCss2Str.="<a data='$lastPageUrl'>尾页</a> ";*/
        }else {
            $subPageCss2Str.="<li class=\"nextPage\"><span class=\"disabled\">下一页</span></li>";
            /*$subPageCss2Str.="<span class=\"disabled\">尾页 </span>";*/
        }
        return  $subPageCss2Str;
    }


    /*
   构造经典模式的分页
   当前第1/453页 [首页] [上页] 1 2 3 4 5 6 7 8 9 10 [下页] [尾页]
   */
    function subPageCss8(){
        $subPageCss2Str="";
        $subPageCss2Str.="<div class=\"desc\">共".$this->nums."条</div>";

        $subPageCss2Str.="<div class=\"paging-container\">";

        if($this->current_page > 1){
            $firstPageUrl=$this->subPage_link."1".$this->html_type;
            $prewPageUrl=$this->subPage_link.($this->current_page-1).$this->html_type;
            $subPageCss2Str.=" <a href='$prewPageUrl' class=\"paging-link paging-home\">&lt;</a>  ";
        }else {
            $subPageCss2Str.="<a class=\"paging-link paging-home\">&lt;</a>";
        }

        $a=$this->construct_num_Page();
        for($i=0;$i<count($a);$i++){
            $s=$a[$i];
            if($s == $this->current_page ){
                $subPageCss2Str.="<a class=\"paging-link paging-item active\">".$s."</a>";
            }else{
                $url=$this->subPage_link.$s.$this->html_type;
                $subPageCss2Str.="<a href='$url' class=\"paging-link paging-item\">".$s."</a>";
            }
        }

//        $subPageCss2Str.="<a class=\"paging-link\" disabled>...</a>";

        if($this->current_page < $this->pageNums){
            $lastPageUrl=$this->subPage_link.$this->pageNums.$this->html_type;
            $nextPageUrl=$this->subPage_link.($this->current_page+1).$this->html_type;
            $subPageCss2Str.=" <a href='$nextPageUrl' class=\"paging-link paging-end\">&gt;</a> ";
        }else {
            $subPageCss2Str.="<a class=\"paging-link paging-end\">&gt;</a>";
        }

        $subPageCss2Str.="</div>";

        return  $subPageCss2Str;
    }



}