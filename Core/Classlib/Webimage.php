<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 18:08
 */

class Webimage {
    var $images_dir    = "update";//附件存放点，默认为：update
    var $thumbnail     = "smallimg";//缩略图存放路径，注：必须是放在 $images_dir下的子目录，默认为：smallimg
    var $watermark     = "markimg";//水印图片存放处
    var $upfiletype    = "jpg gif png JPG GIF PNG";//上传的类型，默认为：jpg gif png rar zi
    var $upfilemax     = 1024;//上传大小限制，单位是"KB"，默认为：1024KB
    var $maxWidth = 500; //图片最大宽度
    var $maxHeight = 600; //图片最大高度
    var $toFile = true;

    function __construct($upfiletype,$images_dir,$thumbnail,$watermark,$upfilemax){
        $this->upfiletype = $upfiletype;
        $this->images_dir = $images_dir;
        $this->thumbnail = $thumbnail;
        $this->watermark = $watermark;
        $this->upfilemax = $upfilemax;
    }

    /**
     * 图片上传的处理函数
     *
     * @access      public
     * @param       array       upload       包含上传的图片文件信息的数组
     * @param       array       dir          文件要上传在$this->data_dir下的目录名。如果为空图片放在则在$this->images_dir下以当月命名的目录下
     * @param       array       img_name     上传图片名称，为空则随机生成
     * @return      mix         如果成功则返回文件名，否则返回false
     */
    function upload_image($upload, $dir = '', $img_name = '')
    {
        /* 没有指定目录默认为根目录images */
        if (empty($dir))
        {
            $dir = ROOT_PATH . $this->images_dir . '/';
        }
        else
        {
            /* 创建目录 */
            $dir = ROOT_PATH . $this->images_dir . '/' . $dir . '/';
            if ($img_name)
            {
                $img_name = $dir . $img_name; // 将图片定位到正确地址
            }
        }

        /* 如果目标目录不存在，则创建它 */
        if (!file_exists($dir))
        {
            if (!make_dir($dir))
            {
                /* 创建目录失败 */
                die("Unable to create directory");
            }
        }

        if (empty($img_name))
        {
            $img_name = $this->unique_name($dir);
            $img_name = $dir . $img_name . '.' .check_file_type($upload['name']);
        }
        /* 允许上传的图片类型 */
        if (strpos($this->upfiletype,check_file_type($upload["name"])) === false)
        {
            die("File type error , can not upload!");
        }

        if ($this->move_file($upload, $img_name))
        {
            return str_replace(ROOT_PATH, '', $img_name);
        }
        else
        {
            die("Upload Failed!".$upload["tmp_name"].'to'.$img_name);
        }

    }


    /**
     * 图片远程下载函数
     *
     * @access      public
     * @param       array       upload       包含上传的图片文件信息的数组
     * @param       array       dir          文件要上传在$this->data_dir下的目录名。如果为空图片放在则在$this->images_dir下以当月命名的目录下
     * @param       array       img_name     上传图片名称，为空则随机生成
     * @return      mix         如果成功则返回文件名，否则返回false
     */
    function get_urlImage($uri, $dir = '', $img_name = '')
    {
        /* 没有指定目录默认为根目录images */
        if (empty($dir))
        {
            $dir = ROOT_PATH . $this->images_dir . '/';
        }
        else
        {
            /* 创建目录 */
            $dir = ROOT_PATH . $this->images_dir . '/' . $dir . '/';
            if ($img_name)
            {
                $img_name = $dir . $img_name; // 将图片定位到正确地址
            }
        }

        /* 如果目标目录不存在，则创建它 */
        if (!file_exists($dir))
        {
            if (!make_dir($dir))
            {
                /* 创建目录失败 */
                //die("Unable to create directory");
                return false;
            }
        }

        //忽略抓取时间限制
        set_time_limit( 0 );
        //ue_separate_ue  ue用于传递数据分割符号
        $imgUrl =  $uri ;
        $tmpNames = array();
        //http开头验证
        if(strpos($imgUrl,"http")!==0){
            array_push( $tmpNames , "error" );
            //die("File type error , not http!".$imgUrl);
            return false;
        }
        //获取请求头
        $heads = get_headers( $imgUrl );
        //死链检测
        if ( !( stristr( $heads[ 0 ] , "200" ) && stristr( $heads[ 0 ] , "OK" ) ) ) {
            array_push( $tmpNames , "error" );
            //die("File type error , not link!");
            return false;
        }

        if (empty($img_name))
        {
            $img_name = $this->unique_name($dir);
            $img_name = $dir . $img_name . '.' .check_file_type($imgUrl);
        }


        /* 允许上传的图片类型 */
        if (strpos($this->upfiletype,check_file_type($imgUrl)) === false)
        {
            //die("File type error , can not upload!".$imgUrl);
            return false;
        }

        //打开输出缓冲区并获取远程图片
        ob_start();
        $context = stream_context_create(
            array (
                'http' => array (
                    'follow_location' => false // don't follow redirects
                )
            )
        );
        //请确保php.ini中的fopen wrappers已经激活
        readfile( $imgUrl,false,$context);
        $img = ob_get_contents();
        ob_end_clean();

        try {
            $fp2 = @fopen( $img_name , "a" );
            fwrite( $fp2 , $img );
            fclose( $fp2 );
            return str_replace(ROOT_PATH, '', $img_name);
        } catch ( Exception $e ) {
            //die("Upload error!");
            return false;
        }

    }

    /**
     * 图片缩略图生成
     *
     * @access      public
     * @param       array       photo       包含处理图片文件信息的数组
     * @param       array       width       处理宽度
     * @param       array       height      处理高度
     * @return      mix         如果成功则返回文件名，否则返回false
     */
    function smallImg($photo,$width=128,$height=128) {
        $imgInfo = $this->getInfo($photo);
        $newName = substr($imgInfo["name"],0,strrpos($imgInfo["name"], "."))."_thumb.jpg";//新图片名称
        $img = $this->image_create_from_ext($photo);

        $width = ($width > $imgInfo["width"]) ? $imgInfo["width"] : $width;
        $height = ($height > $imgInfo["height"]) ? $imgInfo["height"] : $height;
        $srcW = $imgInfo["width"];
        $srcH = $imgInfo["height"];

        if ($srcW * $width > $srcH * $height) {
            $height = round($srcH * $width / $srcW);
        } else {
            $width = round($srcW * $height / $srcH);
        }
        if (function_exists("imagecreatetruecolor")) {
            $newImg = imagecreatetruecolor($width, $height);
            imagecopyresampled($newImg, $img, 0, 0, 0, 0, $width, $height, $imgInfo["width"], $imgInfo["height"]);
        } else {
            $newImg = imagecreate($width, $height);
            imagecopyresized($newImg, $img, 0, 0, 0, 0, $width, $height, $imgInfo["width"], $imgInfo["height"]);
        }
        $savefile = ROOT_PATH.$this->images_dir."/".$this->thumbnail."/".$newName;
        if ($this->toFile) {
            if (file_exists($savefile)) @unlink($savefile);
            imagejpeg($newImg,$savefile);
            return str_replace(ROOT_PATH, '', $savefile);
        }else{
            imagejpeg($newImg);
        }
        imagedestroy($newImg);
        imagedestroy($img);
        return str_replace(ROOT_PATH, '', $newName);
    }
    /**
     * 图片加水印（适用于png/jpg/gif格式）
     *
     * @access  public
     * @param $srcImg    原图片
     * @param $waterImg  水印图片
     * @param $savepath  保存路径
     * @param $savename  保存名字
     * @param $positon   水印位置  1:顶部居左, 2:顶部居右, 3:居中, 4:底部局左, 5:底部居右
     * @param $alpha     透明度 -- 0:完全透明, 100:完全不透明
     * @return 成功 -- 加水印后的新图片地址
     *      失败 -- -1:原文件不存在, -2:水印图片不存在, -3:原文件图像对象建立失败
     *              -4:水印文件图像对象建立失败 -5:加水印后的新图片保存失败
     */
    function markImg($photo, $waterImg, $positon = 5, $alpha = 30){
        $imgInfo = $this->getInfo($photo);
        if (!$imgInfo) {
            return -1;  //原文件不存在
        }
        $waterinfo = $this->getInfo($waterImg);
        if (!$waterinfo) {
            return -2;  //水印图片不存在
        }

        $newName = substr($imgInfo["name"],0,strrpos($imgInfo["name"], "."))."_mark.jpg";//新图片名称
        $srcImgObj = $this->image_create_from_ext($photo);
        if (!$srcImgObj) {
            return -3;  //原文件图像对象建立失败
        }

        $waterImgObj = $this->image_create_from_ext($waterImg);
        if (!$waterImgObj) {
            return -4;  //水印文件图像对象建立失败
        }

        if($positon == '0'){
            $positon = rand(1,9);
        }

        switch ($positon) {
            //1顶部居左
            case 1: $x=$y=0; break;
            //2顶部居中
            case 2: $x = ($imgInfo['width']-$waterinfo['width'])/2; $y = 0; break;
            //3顶部居右
            case 3: $x = $imgInfo['width']-$waterinfo['width']; $y = 0; break;
            //4中部居左
            case 4: $x = 0; $y = ($imgInfo['height']-$waterinfo['height'])/2; break;
            //5中部居中
            case 5: $x = ($imgInfo['width']-$waterinfo['width'])/2; $y = ($imgInfo['height']-$waterinfo['height'])/2; break;
            //6中部居右
            case 6: $x = $imgInfo['width']-$waterinfo['width']; $y = ($imgInfo['height']-$waterinfo['height'])/2; break;
            //4底部居左
            case 7: $x = 0; $y = $imgInfo['height']-$waterinfo['height']; break;
            //4底部居中
            case 8: $x = ($imgInfo['width']-$waterinfo['width'])/2; $y = $imgInfo['height']-$waterinfo['height']; break;
            //5底部居右
            case 9: $x = $imgInfo['width']-$waterinfo['width']; $y = $imgInfo['height']-$waterinfo['height']; break;
            default: $x=$y=0;
        }

        imagecopymerge($srcImgObj, $waterImgObj, $x, $y, 0, 0, $waterinfo['width'], $waterinfo['height'], $alpha);
        $savefile = ROOT_PATH.$this->images_dir."/".$this->watermark."/".$newName;
        if (file_exists($savefile)) @unlink($savefile);
        switch ($imgInfo['type']) {
            case 1: imagegif($srcImgObj, $savefile); break;
            case 2: imagejpeg($srcImgObj, $savefile); break;
            case 3: imagepng($srcImgObj, $savefile); break;
            default: return -5;  //保存失败
        }
        imagedestroy($srcImgObj);
        imagedestroy($waterImgObj);

        return str_replace(ROOT_PATH, '', $savefile);

    }

    /**
     * 图片信息
     **/
    function getInfo($photo) {
        $photo = ROOT_PATH.$photo;
        $imageInfo = @getimagesize($photo);
        if($imageInfo){
            $imgInfo["width"] = $imageInfo[0];
            $imgInfo["height"] = $imageInfo[1];
            $imgInfo["type"] = $imageInfo[2];
            $imgInfo["name"] = basename($photo);
            return $imgInfo;
        }else{
            return false;
        }
    }


    /**
     *  生成指定目录不重名的文件名
     *
     * @access  public
     * @param   string      $dir        要检查是否有同名文件的目录
     *
     * @return  string      文件名
     */
    function unique_name($dir)
    {
        $filename = '';
        while (empty($filename))
        {
            $filename = $this->random_imgname();
            if (file_exists($dir . $filename . '.jpg') || file_exists($dir . $filename . '.gif') || file_exists($dir . $filename . '.png'))
            {
                $filename = '';
            }
        }

        return $filename;
    }

    /**
     * 生成随机的数字串
     *
     * @author: weber liu
     * @return string
     */
    function random_imgname()
    {
        $str = 'l';
        for($i = 0; $i < 9; $i++)
        {
            $str .= mt_rand(0, 9);
        }

        return gnmetime() . $str;
    }

    /**
     *
     *上传图片
     * @access  public
     * @param
     *
     * @return void
     */
    function move_file($upload, $target)
    {
        if (isset($upload['error']) && $upload['error'] > 0)
        {
            return false;
        }

        if (!move_upload_file($upload['tmp_name'], $target))
        {
            return false;
        }

        return true;
    }
    /**
     *
     *根据类型新建图像
     * @access  public
     * @param
     *
     * @return num
     */
    function image_create_from_ext($photo)
    {
        $imgInfo = $this->getInfo($photo);
        $photo = ROOT_PATH.$photo;
        switch ($imgInfo["type"]) {
            case 1: $img=imagecreatefromgif($photo); break;
            case 2: $img=imagecreatefromjpeg($photo); break;
            case 3: $img=imagecreatefrompng($photo); break;
            default: return false;
        }
        return $img;
    }
}