<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2016/12/13
 * Time: 17:56
 */
class WebException extends Exception{
    public function errorTip($errortip)  {
        $res = array('error' => '1', 'errortip' => $errortip,'serverlbs' => Lbsstite);
        $json_play = new Webjson();
        exit($json_play->encode($res));
    }
    public function errorMessage()
    {
        //error message
        $errorMsg = 'Error on line '.$this->getLine().' in file'.$this->getFile()
            .': <b>'.$this->getMessage();

        return $errorMsg;
    }
}