<?php
/**路由文件
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 20:17
 */

class router
{
    private $route;
    private $controller;
    private $action;
    private $api;
    private $params;
    public function __construct()
    {
        //base64_decode(str)解码
        $routeParts=$_GET;
        //$routeParts=base64_decode($_GET);
        $this->controller=isset($routeParts['u'])? $routeParts['u']:"Index";
        $this->route = isset($routeParts['t'])? $routeParts['t']:"Home";
        $this->action=isset($routeParts['c'])? $routeParts['c']:"Wait";
        $this->api=isset($routeParts['api'])? $routeParts['api']:"Place";
        array_shift($routeParts);
        array_shift($routeParts);
        array_shift($routeParts);
        $this->params=$routeParts;
    }
    public function getController()  {
        return $this->controller;
    }
    public function getUrl()  {
        return $this->route;
    }
    public function getAction() {
        return $this->action;
    }
    public function getApi() {
        return $this->api;
    }
    public function getParams()  {
        return $this->params;
    }
}