<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/8/7
 * Time: 18:46
 */

class Analyzesql {
    private $QueryLink;//定义查询数据链接
    public $QueryServerName = AnalyzeServerName;
    public $QueryUserName = AnalyzeUserName;
    public $QueryPassWord = AnalyzePassWord;
    public $QueryDBName = AnalyzeDBName;
    public $errorsql = "";


    /**
     * 功能：数据库链接构造函数
     **/
    public function __construct(){
        $this->QueryControl();
    }

    /**
     * 功能：查询数据库执行链接
     **/
    function QueryControl(){
        try {
            $linkServer = @mysqli_connect($this->QueryServerName,$this->QueryUserName,$this->QueryPassWord);
            if(!$linkServer){
                throw new WebException("无法链接读数据库");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        try {
            $linkData = mysqli_select_db($linkServer,$this->QueryDBName);
            if(!$linkData){
                throw new WebException("找不到可读数据库{$this->QueryDBName}");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        $this->QueryLink = $linkServer;
        mysqli_query($this->QueryLink,"SET NAMES ".MHS_CODING);
    }


    public function select($sql = ""){
        if (empty($sql) || empty($this->QueryLink)) return false;					//如果SQL语句为空或果连接为空则返回FALSE

        try{
            $results = mysqli_query($this->QueryLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        if ((!$results) or (empty($results))) {			//如果查询结果为空则释放结果并返回FALSE
            @mysqli_free_result($results);
            return false;
        }
        if (mysqli_num_rows($results) == 0) {			//如果查询结果数为空
            @mysqli_free_result($results);
            return false;

        }
        $count = 0;
        $data = array();
        while(($row = mysqli_fetch_array($results)) != false){	//把查询结果重组成一个二维数组
            $data[$count] = $row;
            $count++;
        }
        @mysqli_free_result($results);

        return $data;
    }

    public function selectClear($sql = ""){
        if (empty($sql) || empty($this->QueryLink)) return false;					//如果SQL语句为空或果连接为空则返回FALSE
        try{
            $results = mysqli_query($this->QueryLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        if ((!$results) or (empty($results))) {			//如果查询结果为空则释放结果并返回FALSE
            @mysqli_free_result($results);
            return false;
        }
        if (mysqli_num_rows($results) == 0) {			//如果查询结果数为空
            @mysqli_free_result($results);
            return false;
        }
        $count = 0;
        $data = array();
        while(($row = mysqli_fetch_array($results,MYSQLI_ASSOC)) != false){	//把查询结果重组成一个二维数
            $data[$count] = $row;
            $count++;
        }
        @mysqli_free_result($results);

        return $data;
    }

    public function selectMultiClear($sql = ""){
        if (empty($sql) || empty($this->QueryLink)) return false;					//如果SQL语句为空或果连接为空则返回FALSE
        try{
            $results = mysqli_multi_query($this->QueryLink,$sql);
            if(!$results){
                var_dump($sql);
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }


        if ((!$results)) {			//如果查询结果为空则释放结果并返回FALSE
            @mysqli_free_result($results);
            return false;
        }

        $count = 0;
        $data = array();
        do
        {
            // 存储第一个结果集
            if ($result=mysqli_store_result($this->QueryLink))
            {
                while ($row=mysqli_fetch_array($result,MYSQLI_ASSOC))
                {
                    $data[$count] = $row;
                    $count++;
                }
            }
        } while (mysqli_next_result($this->QueryLink));

        @mysqli_free_result($results);

        return $data;
    }
    /**
     * 功能：数据库查询第一条数据
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function selectOne($sql = ""){
        $selectList = $this->selectClear($sql);
        if($selectList){
            return $selectList[0];
        }else{
            return false;
        }
    }

    /**数据空数组处理**/
    public function Dateemtyspl($datearray)
    {
        $date = array();
        if(count($datearray)>0){
            foreach($datearray as $k=>$datevar){
                if($datevar !=='' && count($datevar) > 0){
                    $date[$k] = $datevar;
                }
            }
        }
        return $date;
    }
    /**
     * 功能：读取表
     * 参数：$name 表名 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getList($name,$where,$arrayal=''){
        $sql = "SELECT * FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        return $this->selectClear($sql);
    }

    /**
     * 功能：单个数据查询
     * 参数：$where条件,$name 表名称
     * 返回：值
     */
    public function getOne($name,$where,$arrayal = ' limit 0,1')
    {
        $sql = "SELECT * FROM " . $name . " WHERE ".$where.$arrayal;
        $r = $this->selectClear($sql);
        if($r[0]){
            return $r[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：自定字段排序读取表
     * 参数：$name 表名 $fields 查询的字段 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getFieldOne($name,$fields,$where,$arrayal = 'limit 0,1'){
        $sql = "SELECT ".$fields." FROM " . $name;

        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        $r = $this->selectClear($sql);
        if($r){
            return $r[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：自定字段排序读取表
     * 参数：$name 表名 $fields 查询的字段 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getFieldquery($name,$fields,$where,$arrayal = ''){
        $sql = "SELECT ".$fields." FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        return $this->selectClear($sql);
    }
    /**
     *功能:统计记录条数
     *参数:$tname 表名,$where 条件
     *返回:记录条数
     **/

    public function getIdCount($tname,$Id,$where = '')
    {
        $sql = "SELECT COUNT({$Id}) FROM " . $tname;
        if($where){
            $sql .= ' WHERE '.$where;
        }
        $r = $this->selectClear($sql);
        return $r[0][0];
    }
}
