<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/12/17
 * Time: 0:29
 */

class Dbmysql {
    private $QueryLink;//定义查询数据链接
    public $QueryServerName = QueryServerName;
    public $QueryUserName = QueryUserName;
    public $QueryPassWord = QueryPassWord;
    public $QueryDBName = QueryDBName;

    private $WriteLink;//定义写数据链接
    public $WriteServerName = WriteServerName;
    public $WriteUserName = WriteUserName;
    public $WritePassWord = WritePassWord;
    public $WriteDBName = WriteDBName;
    public $errorsql = "";


    /**
     * 功能：数据库链接构造函数
     **/
//    public function __construct(){
//        $this->QueryControl();
//    }

    public function __construct($Server=QueryServerName,$UserName=QueryUserName,$PassWord=QueryPassWord,$DataBase=QueryDBName){

        $this->QueryServerName = $Server;
        $this->QueryUserName = $UserName;
        $this->QueryPassWord = $PassWord;
        $this->QueryDBName = $DataBase;

        $this->QueryControl();
    }

    /**
     * 功能：查询数据库执行链接
     **/
    function QueryControl(){
        try {
            $linkServer = @mysqli_connect($this->QueryServerName,$this->QueryUserName,$this->QueryPassWord);
            if(!$linkServer){
                throw new WebException("无法链接读数据库");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        try {
            $linkData = mysqli_select_db($linkServer,$this->QueryDBName);
            if(!$linkData){
                throw new WebException("找不到可读数据库{$this->QueryDBName}");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        $this->QueryLink = $linkServer;
        mysqli_query($this->QueryLink,"SET NAMES ".MHS_CODING);
    }

    /**
     * 功能：执行数据库执行链接
     **/
    function WriteControl(){
        try {
            $linkServer = @mysqli_connect($this->WriteServerName,$this->WriteUserName,$this->WritePassWord);
            if(!$linkServer){
                throw new WebException("无法链接读数据库");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        try {
            $linkData = mysqli_select_db($linkServer,$this->WriteDBName);
            if(!$linkData){
                throw new WebException("找不到可读数据库{$this->WriteDBName}");
            }
        }catch (WebException $e)
        {
            die($e->errorMessage());
        }
        $this->WriteLink = $linkServer;
        mysqli_query($this->WriteLink,"SET NAMES ".MHS_CODING);
    }


    public function select($sql = ""){
        if (empty($sql) || empty($this->QueryLink)) return false;					//如果SQL语句为空或果连接为空则返回FALSE

        $start_time = microtime(true);
        try{
            $results = mysqli_query($this->QueryLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        /*if(microtime(true) - $start_time > 10){//97添加
            $time =array();
            $time['querytime'] = microtime(true) - $start_time;
            $time['querysql'] = mysqli_real_escape_string($this->QueryLink,$sql);
            $this->insertData('cms_sqlquerytime',$time);
        }*/

        if ((!$results) or (empty($results))) {			//如果查询结果为空则释放结果并返回FALSE
            @mysqli_free_result($results);
            return false;
        }
        if (mysqli_num_rows($results) == 0) {			//如果查询结果数为空
            @mysqli_free_result($results);
            return false;

        }
        $count = 0;
        $data = array();
        while(($row = mysqli_fetch_array($results)) != false){	//把查询结果重组成一个二维数组
            $data[$count] = $row;
            $count++;
        }
        @mysqli_free_result($results);

        return $data;
    }

    public function selectClear($sql = ""){
        if (empty($sql) || empty($this->QueryLink)) return false;					//如果SQL语句为空或果连接为空则返回FALSE
        try{
            $results = mysqli_query($this->QueryLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        if ((!$results) or (empty($results)) or $results === false) {			//如果查询结果为空则释放结果并返回FALSE
            @mysqli_free_result($results);
            return false;
        }
        if (mysqli_num_rows($results) == 0) {			//如果查询结果数为空
            @mysqli_free_result($results);
            return false;
        }
        $count = 0;
        $data = array();
        while(($row = mysqli_fetch_array($results,MYSQLI_ASSOC)) != false){	//把查询结果重组成一个二维数
            $data[$count] = $row;
            $count++;
        }
        @mysqli_free_result($results);

        return $data;
    }


    public function selectMultiClear($sql = ""){
        if (empty($sql) || empty($this->QueryLink)) return false;					//如果SQL语句为空或果连接为空则返回FALSE
        $start_time = microtime(true);
        try{
            $results = mysqli_multi_query($this->QueryLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }

        /*if(microtime(true) - $start_time > 10){//97添加
            $time =array();
            $time['querytime'] = microtime(true) - $start_time;
            $time['querysql'] = mysqli_real_escape_string($this->QueryLink,$sql);
            $this->insertData('cms_sqlquerytime',$time);
        }*/

        if ((!$results)) {			//如果查询结果为空则释放结果并返回FALSE
            @mysqli_free_result($results);
            return false;
        }

        $count = 0;
        $data = array();
        do
        {
            // 存储第一个结果集
            if ($result=mysqli_store_result($this->QueryLink))
            {
                while ($row=mysqli_fetch_array($result,MYSQLI_ASSOC))
                {
                    $data[$count] = $row;
                    $count++;
                }
            }
        } while (mysqli_next_result($this->QueryLink));

        @mysqli_free_result($results);

        return $data;
    }
    /**
     * 功能：数据库查询第一条数据
     * 参数：$sql SQL语句
     * 返回：二唯数组或false
     */
    public function selectOne($sql = ""){
        $selectList = $this->selectClear($sql);
        if($selectList){
            return $selectList[0];
        }else{
            return false;
        }
    }

    /**数据空数组处理**/
    public function Dateemtyspl($datearray)
    {
        $date = array();
        if(count($datearray)>0){
            foreach($datearray as $k=>$datevar){
                if($datevar !=='' && count($datevar) > 0){
                    $date[$k] = $datevar;
                }
            }
        }
        return $date;
    }
    /**
     * 功能：读取表
     * 参数：$name 表名 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getList($name,$where,$arrayal=''){
        $sql = "SELECT * FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        return $this->selectClear($sql);
    }

    /**
     * 功能：单个数据查询
     * 参数：$where条件,$name 表名称
     * 返回：值
     */
    public function getOne($name,$where,$arrayal = '')
    {
        $sql = "SELECT * FROM " . $name . " WHERE ".$where.$arrayal;
        $r = $this->selectClear($sql);
        if($r[0]){
            return $r[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：自定字段排序读取表
     * 参数：$name 表名 $fields 查询的字段 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getFieldOne($name,$fields,$where,$arrayal = 'limit 0,1'){
        $sql = "SELECT ".$fields." FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }
        $r = $this->selectClear($sql);
        if($r){
            return $r[0];
        }else{
            return false;
        }
    }
    /**
     * 功能：自定字段排序读取表
     * 参数：$name 表名 $fields 查询的字段 $where 查询条件 $arrayal 排列方式
     * 返回：数组
     */
    public function getFieldquery($name,$fields,$where,$arrayal = ''){
        $sql = "SELECT ".$fields." FROM " . $name;
        if($where)													//如果参数有值，则加入查询条件
        {
            $sql .= ' WHERE ' . $where;
        }
        if($arrayal)													//如果参数有值，则加入查询条件
        {
            $sql .= ' '.$arrayal;
        }

        return $this->selectClear($sql);
    }
    /**
     *功能:统计记录条数
     *参数:$tname 表名,$where 条件
     *返回:记录条数
     **/

    public function getIdCount($tname,$Id,$where = '')
    {
        $sql = "SELECT COUNT({$Id}) FROM " . $tname;
        if($where){
            $sql .= ' WHERE '.$where;
        }
        $r = $this->selectClear($sql);
        return $r[0][0];
    }




    /**
     * 功能：数据操作函数
     * 参数：$sql SQL语句
     * 返回：false或新插入数据的ID
     */
    public function multiquery($sql = ""){
        if(empty($this->WriteLink)){
            $this->WriteControl();
        }
        if (empty($sql)){
            return false;
        }
        try{											//捕获数据库选择错误并显示错误文件
            $results = mysqli_multi_query($this->WriteLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to ".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }
        return $results;
    }

    /**
     * 功能：数据操作函数
     * 参数：$sql SQL语句
     * 返回：false或新插入数据的ID
     */
    public function query($sql = ""){
        if(empty($this->WriteLink)){
            $this->WriteControl();
        }
        if (empty($sql)){
            return false;
        }
        try{											//捕获数据库选择错误并显示错误文件
            $results = mysqli_query($this->WriteLink,$sql);
            if(!$results){
                throw new WebException("Failed to query to ".$sql);
            }
        }catch (WebException $e){
            die($e->errorMessage());
        }
        return $results;
    }
    /**
     * 功能：数据插入函数
     * 参数：$sql SQL语句
     * 返回：false或新插入数据的ID
     */
    public function insert($sql = ""){
        if(empty($this->WriteLink)){
            $this->WriteControl();
        }
        if (empty($sql)){
            return false;
        }
        try{											//捕获数据库选择错误并显示错误文件
            $results = mysqli_query($this->WriteLink,$sql);
        }catch (WebException $e){
            die($e->errorMessage());
        }
        if (!$results){									//如果插入失败返回0，否则返回当前插入数据ID
            return false;
        }else{
            return @mysqli_insert_id($this->WriteLink);
        }
    }


    /**
     * 功能：向指定表中插入数据
     * 参数：$name 表名称,$data 数组(格式：$data['字段名'] = 值)
     * 返回：插入记录ID
     */
    public function insertData($name,$data)
    {
        $data = $this->Dateemtyspl($data);
        $field = implode(',',array_keys($data));					//定义sql语句的字段部分
        $i = 0;
        $value = '';
        foreach($data as $val)
        {
            $value .= "'" . str_replace("'","'",$val) . "'";

            if($i < count($data) - 1)								//判断是否到数组的最后一个值
                $value .= ",";
            $i++;
        }
        $sql = "INSERT INTO " . $name . "(" . $field . ") VALUES(" . $value . ")";
        try{
            $inset = $this->insert($sql);
            if(!$inset){
                $this->errorsql = $sql;
            }
        }catch (WebException $e){
            if(ADAROTEST){
                die($sql);
            }
        }

        return $inset;
    }


    /**
     * 功能：更新指定表指定表记录
     * 参数：$name 表名称,$data 数组(格式：$data['字段名'] = 值),$where
     * 返回：TRUE OR FALSE
     */
    public function updateData($name,$where,$data){
        $col = array();
        foreach ($data as $key => $value)
        {
            $col[] = $key . "='" . str_replace("'","'",$value) . "'";
        }
        $sql = "UPDATE " . $name . " SET " . implode(',',$col) . " WHERE ". $where;


        try{
            $query = $this->query($sql);
        }catch (WebException $e){
            if(ADAROTEST){
                die($sql);
            }
        }

        return $query;
    }

    /**
     * 功能：删除指定条件记录
     * 参数：$name 表名称,$where
     * 返回：TRUE OR FALSE
     */
    public function delData($name,$where)
    {
        $sql = "DELETE FROM " . $name . " WHERE ".$where;
        return $this->query($sql);
    }


    /**
     * 功能：数据表(分页显示)
     * 参数：$sql栏目数据表，$p分页数，$num每页显示数量,$time有无1,0,$url连接,$page_sub每页多少条，$pagecss分页样式（1,2,3）
     * 返回：字符串类型
     */
    public function dbwherePage($sql,$allnum,$num,$url,$p='',$page_sub='10',$pagecss){
        /**分页处理**/
        $db_nums = $allnum;//相关条件下的总记录数
        $page_size=$num;//每页显示的条数
        $sub_pages=$page_sub;//每次显示的页
        if($p=='') {
            $pageCurrent=1;
        }else {
            $pageCurrent=$p;//得到当前是第几页数
        }
        $subPages = new \SetPages($page_size,$db_nums,$pageCurrent,$sub_pages,$url,$pagecss,'');
        $sub_nums = ($pageCurrent-1)*$page_size;
        /**分页处理**/
        $db_list = $this->selectClear($sql." LIMIT $sub_nums,$page_size");
        $db_array['cont'] = $db_list;
        $db_array['cont_num'] = $db_nums;
        $db_array['pages'] = $subPages->show_SubPages();
        return $db_array;
    }


    /**
     * 功能：定义事务
     */
    public function begintransaction()
    {
        if(empty($this->WriteLink)){
            $this->WriteControl();
        }
        mysqli_autocommit($this->WriteLink,false);				//设置为不自动提交，因为MYSQL默认立即执行
        mysqli_query($this->WriteLink,"BEGIN");							//开始事务定义
    }
    /**
     * 功能：回滚
     */
    public function rollback()
    {
        mysqli_rollback($this->WriteLink);
    }
    /**
     * 功能：提交执行
     */
    public function commit()
    {
        mysqli_commit($this->WriteLink);
    }
}
