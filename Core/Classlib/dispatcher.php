<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 20:24
 */

class dispatcher
{
    public static function dispatch($router)
    {
        $thisdomain = explode(".",$_SERVER['HTTP_HOST']);//获取当前域名
      if($thisdomain[0] == 'gmcapi'){
            $WorkPath = "Gmcapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'smcapi'){
            $WorkPath = "Smcapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'crmapi'){
            $WorkPath = "Crmapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'fmcapi'){
            $WorkPath = "Fmcapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'wwwapi' || $thisdomain[0] == 'scloginapi' || $thisdomain[0] == 'sclogin'){
            $WorkPath = "Wwwapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'posapi'){
            $WorkPath = "Posapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'manage' || $thisdomain[0] == 'scmanage'){
            $WorkPath = "Manage";
            self::WorkController($router,$WorkPath);
        }elseif($thisdomain[0] == 'tephone'){
			$WorkPath = "Tephone";
			self::WorkController($router,$WorkPath);
		}elseif($thisdomain[0] == 'schelp'){
			$WorkPath = "Schelp";
			self::WorkController($router,$WorkPath);
		}elseif($thisdomain[0] == 'talkline'){
            $WorkPath = "Talkline";
            self::WorkController($router,$WorkPath);
        }elseif($thisdomain[0] == 'service'){
          $WorkPath = "Service";
          self::WorkController($router,$WorkPath);
        }elseif($thisdomain[0] == 'crmchannel'){
          $WorkPath = "Crmchannel";
          self::WorkController($router,$WorkPath);
        }elseif($thisdomain[0] == 'easxapi'){
            $WorkPath = "Easxapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'scptcapi'){
            $WorkPath = "Scptcapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'imcapi'){
            $WorkPath = "Imcapi";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'api' || $thisdomain[0] == 'scapi'){
            $WorkPath = "Api";
            self::ApiController($router,$WorkPath);
        }elseif($thisdomain[0] == 'scshopapi'){
            $WorkPath = "Scshopapi";
            self::ApiController($router,$WorkPath);
        }
    }

    static function ApiController($router,$WorkPath){
        ob_start();//打开缓冲区
        $controller = ($router->getController()) . 'Controller';//控制器类或者控制器的文件名
        $view = ($router->getUrl()) . 'View';//显示器的动作函数名
        $action = ($router->getAction()) . 'Action';//处理的动作函数名
        $api = ($router->getApi()) . 'Api';//处理的动作函数名
        $params = $router->getParams();//参数

        $controllerfile = "Work/Controller/".$WorkPath."/{$controller}.php";
        if (file_exists($controllerfile)) {
            set_include_path(get_include_path() . PATH_SEPARATOR . "Controller");

            function Controllerautoload($object)
            {
                $object = str_replace('\\', '/', $object);
                $file = BASEDIR . "/{$object}.php";
                if (file_exists($file)) {
                    require_once($file);
                }
            }

            spl_autoload_register('Controllerautoload');

            $controller = "Work\\Controller\\".$WorkPath."\\". $router->getController() . "Controller";
            $app = new $controller();

            if ($api !=='PlaceApi') {
                $app->$api();
            } elseif($action !=='WaitAction'){
                $app->$action();
            } else{
                $app->$view();
            }

            $app->params=$params;

            $output = ob_get_clean();
            echo $output;
        } else {
            WebException::errorTip("未发现任何控制器{$controllerfile}存在1");
        }
    }

    static function WorkController($router,$WorkPath){
        ob_start();//打开缓冲区
        $controller = ($router->getController()) . 'Controller';//控制器类或者控制器的文件名
        $view = ($router->getUrl()) . 'View';//显示器的动作函数名
        $action = ($router->getAction()) . 'Action';//处理的动作函数名
        $params = $router->getParams();//参数

        $controllerfile = "Work/Controller/".$WorkPath."/{$controller}.php";
        if (file_exists($controllerfile)) {
            set_include_path(get_include_path() . PATH_SEPARATOR . "Controller");

            function Controllerautoload($object)
            {
                $object = str_replace('\\', '/', $object);
                $file = BASEDIR . "/{$object}.php";
                if (file_exists($file)) {
                    require_once($file);
                }
            }

            spl_autoload_register('Controllerautoload');

            $controller = "Work\\Controller\\".$WorkPath."\\". $router->getController() . "Controller";
            $app = new $controller();

            if ($action !=='WaitAction') {
                $app->$action();
            } else{
                $app->$view();
            }

            $app->params=$params;

            $output = ob_get_clean();
            echo $output;
        } else {
            WebException::errorTip("未发现任何控制器{$controllerfile}存在2");
        }
    }
}
