<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/13
 * Time: 20:30
 */

class loader
{
    private static $loaded = array();
    public static function load($object){
        $valid = array(
            "library",
            "view",
            "model",
            "helper",
            "router",
            "config",
            "hook",
            "cache",
            "db");
        if (!in_array($object,$valid)){
            ajax_return(array('error'=>"1",'errortip'=>"非法操作类"));
        }
        if (empty(self::$loaded[$object])){
            self::$loaded[$object]= new $object();
        }
        return self::$loaded[$object];
    }
}