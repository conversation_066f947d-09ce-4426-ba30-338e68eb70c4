课程相关设置
1、课程科目（班组）
字段：序号 科目名称 科目代码 描述
操作：增、删、改、查
备注：删除课程需要检查是否拥有下属班种，如果含有禁止删除
数据库：smc_code_coursetype
2、课程分类（班种）
字段：序号 分类名称 分类代码 所属科目 描述
操作：增、删、改、查
备注：删除课程需要检查是否拥有下属课程，如果含有禁止删除
数据库：smc_code_coursecat
3、课程管理（班别）
字段：序号 课程名称 课程编号 所属课程科目（自定义）所述课程分类（自定义）课程类型（课次/期收）
上课方式（自定义）实际课时数 赠送课时数 销售方式（自定义）支付方式（自定义）课程状态 更新时间（自定义）
操作：增、删、改、查、下架
备注：课程已有报名，禁止删除，只可以下架
数据库：smc_course
4、课程期收类型
字段：序号 期收名称 期收编号 期收备注
操作：增、改、查
备注：期收不可以删除，仅限添加修改
数据库：smc_code_courseterm
5、课程收费类型
字段：序号 收费名称 收费编号  收费排序 收费备注
操作：增、改、查
备注：收费类型不可以删除，仅限添加修改
数据库：smc_code_feetype
班务相关设置
1、学生出勤类型
字段：类型名称 类型编号 出勤标志 是否消耗课时 备注
操作：增、改、查（关键词查询）删
备注：被使用，不可以删除
数据库：smc_code_stuchecktype
2、学生异动代码表
字段：异动名称 异动编号 异动类型 学生状态名称
操作：增、改、查（关键词查询）删
备注：被使用，不可以删除
数据库：smc_code_stuchange
3、学生异动原因
字段：原因编号 原因内容
操作：增、改、查（关键词查询）删
备注：直接储存原因
数据库：smc_code_stuchange_reason
3、家长职业信息
字段：职业名称 职业编号
操作：增、改、查（关键词查询）删
备注：直接储存职业名称
数据库：smc_code_parentscareer
人事相关设置
1、职务类型
字段：职务名称 职务编号 默认职等 职务类型（集团/校园） 备注
操作：增、改、查（类型查询、关键词查询）删
备注：如果植物被用户使用，不可以删除，要提醒此职务 此职务已被员工使用
数据库：gmc_company_post
2、职等类型
字段：职等名称 职等编号 备注
操作：增、改、查（类型查询、关键词查询）删
备注：如果职等被用户使用，不可以删除，要提醒此职等 此职等已被员工使用
数据库：gmc_company_postlevel
3、集团角色
字段：角色名称 备注
操作：增、改、查、删
备注：如果角色被用户使用，不可以删除，要提醒此角色 此角色已被员工使用
数据库：gmc_company_postrole
4、校园角色
字段：角色名称 所述校园（共用/校园名称） 备注
操作：增、改、查、删
备注：如果角色被用户使用，不可以删除，要提醒此角色 此角色已被员工使用
数据库：smc_school_postpart
5、职工异动类型
字段：类型名称 类型编号 类型备注
操作：增、改、查、删
备注：如果职工异动类型被用户使用，不可以删除，要提醒此职工异动类型 此职工异动类型已被员工使用
数据库：gmc_code_workchange
6、职工异动原因
字段：原因名称 原因备注
操作：增、改、查、删
数据库：gmc_code_workchange_reason
7、教师类型
字段：类型名称 类型编号 类型备注
操作：增、改、查、删
备注：如果教师类型被用户使用，不可以删除，要提醒此教师类型 此教师类型已被员工使用
数据库：smc_code_teachingtype
8、员工出勤类型
字段：类型名称 类型编号 类型备注
操作：增、改、查、删
备注：如果教师类型被用户使用，不可以删除，要提醒此教师类型 此教师类型已被员工使用
数据库：smc_code_stachecktype
招生相关设置
1、招生来源
字段：来源名称 来源备注
操作：增、改、查删
备注：储存直接储存来源名称
数据库：crm_code_frommedia
2、沟通方式
字段：方式名称 方式备注
操作：增、改、查、删
备注：联动track_linktype
数据库：crm_code_commode
3、来源渠道
字段：渠道名称 联系人 联系电话 渠道编号 录入时间
操作：增、改、查、删（被使用禁止删除）
备注：联动channel_id
数据库：crm_code_channel
4、CRM用户角色
字段：角色名称 角色等级 备注
操作：增、改、查、删
备注：如果角色被用户使用，不可以删除，要提醒此角色 此角色已被员工使用
数据库：crm_code_postrole
5、用户跟踪内容模板
字段：模板编号 模板内容
操作：增、改、查、删
备注：直接将内容选到跟踪内容框
数据库：crm_code_tracenote
库存相关设置
1、仓库管理
字段：仓库编号 仓库名称 仓库位置 联系人 联系电话
操作：增、改、查、删
备注：仓库被使用不可以删除
数据库：smc_code_warehouse
2、货品类别
字段：类别编号 类别名称 类别备注 金蝶代码
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_prodtype
3、出入库类型
字段：类型编号 类型名称 类型备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_warehousetype
4、货品管理（暂时不处理）后面有集团订货模块
财务相关设置
1、会计科目管理
字段：科目名称 科目代码 分组名称 类别名称 科目备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_acct
2、会计科目分组
字段：科目分组名称 科目分组代码 科目分组备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_acctgroup
3、会计科目类型
字段：科目类型名称 科目类型代码 科目分组名称 科目类型备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_accttype
4、银行管理
字段：银行名称 银行代码 银行备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_bank
5、收费类型管理
字段：类型名称 类型代码 类型排序 类型备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_feetype
6、收费支付方式管理
字段：方式名称 方式代码 方式备注
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_paytype
7、零用金代码
字段：零用金名称 零用金代码 零用金备注 会计科目名称 会计科目代码
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_pettycash
8、支出类型
字段：类型名称 类型代码 类型备注 会计科目名称 会计科目代码
操作：增、改、查、删
备注：被使用不可以删除
数据库：smc_code_spending













































































