<?php


namespace Model\Smc;

class OrderModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array(), $order_pid = 0)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
        if ($order_pid !== '0') {
            $this->verdictpayFeeOrder($order_pid);
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictpayFeeOrder($order_pid)
    {
        $this->payfeeorderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid = '{$order_pid}'");
        if (!$this->payfeeorderOne) {
            $this->error = true;
            $this->errortip = "订单信息不存在";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }


    function stuTrading($student_id, $companies_id, $code = "", $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        do {
            $trading_pid = $this->createOrderPid('JY');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = $code;
        $data['trading_status'] = "0";
        $data['trading_createtime'] = $time;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        if ($this->DataControl->insertData("smc_student_trading", $data)) {
            return $trading_pid;
        } else {
            return false;
        }
    }

    //生成订单

    /**
     * @param $request
     * @param $student_id
     * @param $code --交易类型
     * @param $price --订单实际支付金额
     * @param $taglist --标签
     * @param int $order_type --订单缴费类型
     * @param int $coupon_price --优惠券抵扣金额
     * @param int $market_price --营销活动抵扣金额
     * @param string $title --标题
     * @param string $information --信息
     * @return bool|string
     */
    function createOrder($student_id, $code, $price, $taglist = '', $order_type = 0, $coupon_price = 0, $market_price = 0, $title = '', $information = '', $note = '', $iscreatecourse = 0, $order_status = 1, $time = '', $coursepacks_id = 0, $order_from = 1, $parenter_id = 0, $coursetype_id = 0, $coursecat_id = 0, $companies_id = 0,$mergeorder_pid='')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }
        $orderData = array();
        do {
            $orderPid = $this->createOrderPid('SC');
        } while ($this->DataControl->selectOne("select order_id from smc_payfee_order where order_pid='{$orderPid}' and company_id='{$this->company_id}' limit 0,1"));


        $orderData['order_pid'] = $orderPid;
        $orderData['company_id'] = $this->company_id;

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, $coursecat_id, 0,$coursepacks_id);
        if (!$companiesOne) {
            $orderData['companies_id'] = $companies_id > 0 ? $companies_id : $this->schoolOne['companies_id'];
        } else {
            $orderData['companies_id'] = $companiesOne['companies_id'];
        }

        $orderData['trading_pid'] = $this->stuTrading($student_id, $orderData['companies_id'], $code, $time);
        $orderData['mergeorder_pid'] = $mergeorder_pid;
        $orderData['school_id'] = $this->school_id;
        $orderData['staffer_id'] = $this->stafferOne['staffer_id'];
        $orderData['coursepacks_id'] = $coursepacks_id;
        $orderData['student_id'] = $student_id;
        $orderData['order_from'] = $order_from;
        $orderData['order_type'] = $order_type;
        $orderData['order_status'] = $order_status;
        if ($order_status == 0) {
            $orderData['order_isneedaudit'] = 1;
        }
        $orderData['order_iscreatecourse'] = $iscreatecourse;
        $orderData['order_taglist'] = $taglist;
        $orderData['order_allprice'] = $price + $coupon_price + $market_price;
        $orderData['order_coupon_price'] = $coupon_price;
        $orderData['order_market_price'] = $market_price;
        $orderData['order_note'] = $note;
        $orderData['parenter_id'] = $parenter_id ? $parenter_id : 0;
        $orderData['coursetype_id'] = $coursetype_id ? $coursetype_id : 0;

        $orderData['coursecat_id'] = $coursecat_id ? $coursecat_id : 0;
        $orderData['order_paymentprice'] = $price;
        $orderData['order_createtime'] = $time;
        if ($this->DataControl->insertData("smc_payfee_order", $orderData)) {
            $this->verdictpayFeeOrder($orderPid);
            $this->orderTracks($title, $information, $note, $time);
            return $orderPid;
        } else {
            return false;
        }
    }


    function createMergeOrder($student_id, $coursepacks_id, $mergeorder_status = 0, $mergeorder_note = '')
    {

        $orderData = array();

        do {
            $orderPid = $this->createOrderPid('ZH');
        } while ($this->DataControl->selectOne("select mergeorder_id from smc_payfee_mergeorder where mergeorder_pid='{$orderPid}' and company_id='{$this->company_id}' limit 0,1"));

        $orderData['mergeorder_pid'] = $orderPid;
        $orderData['company_id'] = $this->company_id;
        $orderData['school_id'] = $this->school_id;
        $orderData['staffer_id'] = $this->stafferOne['staffer_id'];
        $orderData['student_id'] = $student_id;
        $orderData['coursepacks_id'] = $coursepacks_id;
        $orderData['mergeorder_status'] = $mergeorder_status;
        $orderData['mergeorder_note'] = $mergeorder_note;
        $orderData['mergeorder_createtime'] = time();
        if ($this->DataControl->insertData("smc_payfee_mergeorder", $orderData)) {
            return $orderPid;
        } else {
            return false;
        }
    }


    //生成订单跟踪
    function orderTracks($title = '', $information = '', $ntoe = '', $time = '')
    {
//        if ($time == '') {
//            $time = time();
//        } else {
//            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
//        }
        $orderTracksData = array();

        $orderTracksData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderTracksData['tracks_title'] = $this->LgStringSwitch($title);
        $orderTracksData['tracks_information'] = $this->LgStringSwitch($information);
        $orderTracksData['tracks_note'] = $this->LgStringSwitch($ntoe);
        $orderTracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $orderTracksData['tracks_playname'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname']);
//        $orderTracksData['tracks_time'] = $time;
        $orderTracksData['tracks_time'] = time();//戚总确认日志时间用 系统时间
        $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);
    }

    function stuTradeList($request)
    {//缺少上课时间
        $datawhere = " 1 ";

        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and st.companies_id in(8,78606) ";
        }

//        if ($request['staffer_id'] == '25721') {
//            $datawhere .= " and st.tradingtype_code in ('PayrenewFee','PaynewFee')";
//
//            $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.coursetype_id in (79654))";
//
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or st.trading_pid like '%{$request['keyword']}%'
            or exists(select 1 from smc_payfee_order as x where x.trading_pid=st.trading_pid and x.mergeorder_pid= '{$request['keyword']}'))
            ";
        }

        if (isset($request['tradingtype_code']) && $request['tradingtype_code'] !== '') {
            $datawhere .= " and st.tradingtype_code = '{$request['tradingtype_code']}'";

            if (isset($request['status']) && $request['status'] != '') {
                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge' || $request['tradingtype_code'] == 'DepositCharge') {

                    $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.order_status='{$request['status']}')";


                } elseif ($request['tradingtype_code'] == 'Accountrefund') {

                    $datawhere .= " and exists (select po.trading_pid from smc_refund_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.refund_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'CourseForward') {

                    $datawhere .= " and exists (select po.trading_pid from smc_forward_dealorder as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.dealorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

                    $datawhere .= " and exists (select po.trading_pid from smc_course_reduceorder as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.reduceorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ClassGiving') {

                    $datawhere .= " and exists (select po.trading_pid from smc_freehour_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferIn') {

                    $datawhere .= " and exists (select po.trading_topid from smc_school_trading as po where po.trading_topid=st.trading_pid and po.to_school_id='{$request['school_id']}' and po.trading_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferOut') {

                    $datawhere .= " and exists (select po.trading_frompid from smc_school_trading as po where po.trading_frompid=st.trading_pid and po.from_school_id='{$request['school_id']}' and po.trading_status='{$request['status']}')";

                }
            }
            if (isset($request['coursetype_id']) && $request['coursetype_id'] != '' && $request['coursetype_id'] != '0') {
                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge' || $request['tradingtype_code'] == 'DepositCharge') {
                    $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=st.trading_pid 
                        and po.school_id='{$request['school_id']}' and po.coursetype_id='{$request['coursetype_id']}')";
                } elseif ($request['tradingtype_code'] == 'CourseForward') {
                    $datawhere .= " and exists (select po.trading_pid from smc_forward_dealorder as po,smc_forward_dealorder_course pc,smc_course sc where po.trading_pid=st.trading_pid 
                        and po.school_id='{$request['school_id']}' and po.dealorder_pid=pc.dealorder_pid and pc.course_id=sc.course_id and sc.coursetype_id='{$request['coursetype_id']}')";
                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {
                    $datawhere .= " and exists (select po.trading_pid from smc_course_reduceorder as po,smc_course sc 
                        where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.course_id=sc.course_id and sc.coursetype_id='{$request['coursetype_id']}')";
                } elseif ($request['tradingtype_code'] == 'ClassGiving') {
                    $datawhere .= " and exists (select po.trading_pid from smc_freehour_order as po,smc_course sc 
                        where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.course_id=sc.course_id and sc.coursetype_id='{$request['coursetype_id']}')";
                }
            }

            if (isset($request['coursecat_id']) && $request['coursecat_id'] != '' && $request['coursecat_id'] != '0') {
                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge' || $request['tradingtype_code'] == 'DepositCharge') {
                    $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=st.trading_pid 
                        and po.school_id='{$request['school_id']}' and po.coursecat_id='{$request['coursecat_id']}')";
                } elseif ($request['tradingtype_code'] == 'CourseForward') {
                    $datawhere .= " and exists (select po.trading_pid from smc_forward_dealorder as po,smc_forward_dealorder_course pc,smc_course sc where po.trading_pid=st.trading_pid 
                        and po.school_id='{$request['school_id']}' and po.dealorder_pid=pc.dealorder_pid and pc.course_id=sc.course_id and sc.coursecat_id='{$request['coursecat_id']}')";
                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {
                    $datawhere .= " and exists (select po.trading_pid from smc_course_reduceorder as po,smc_course sc 
                        where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.course_id=sc.course_id and sc.coursecat_id='{$request['coursecat_id']}')";
                } elseif ($request['tradingtype_code'] == 'ClassGiving') {
                    $datawhere .= " and exists (select po.trading_pid from smc_freehour_order as po,smc_course sc 
                        where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.course_id=sc.course_id and sc.coursecat_id='{$request['coursecat_id']}')";
                }
            }
        }

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and s.student_id = '{$request['student_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and st.trading_createtime >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime(($request['endtime'] . ' 23:59:59'));
            $datawhere .= " and st.trading_createtime <= '{$request['endtime']}'";
        }

        $compwhere = '';
        if (isset($request['search_school_id']) && $request['search_school_id'] !== '') {
            $datawhere .= " and st.school_id='{$request['search_school_id']}'";
            $compwhere .= " a.school_id='{$request['search_school_id']}'";
        } else {
            $datawhere .= " and st.school_id='{$this->school_id}'";
            $compwhere .= " a.school_id='{$this->school_id}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and st.companies_id='{$request['companies_id']}'";
            $compwhere .= " and a.companies_id='{$request['companies_id']}'";
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $tradeList = $this->DataControl->selectClear("select st.trading_pid,st.tradingtype_code,s.student_cnname,s.student_enname,s.student_branch
            ,st.trading_createtime,st.tradingtype_code,ct.tradingtype_name,st.student_id,st.trading_status
            ,sc.school_cnname,sc.school_branch,st.school_id
            ,(select staffer_cnname from smc_staffer where staffer_id=st.staffer_id) as staffer_name
            ,sf.family_mobile,sf.family_cnname
	        ,(select y.coursecat_istreaty from smc_payfee_order x,smc_code_coursecat y where x.trading_pid=st.trading_pid and x.order_type=0 and x.coursecat_id=y.coursecat_id) as coursecat_istreaty
            ,(select companies_cnname from gmc_code_companies where companies_id=st.companies_id) as companies_cnname
            from smc_student_trading as st
            left join smc_student as s on s.student_id=st.student_id
            left join smc_code_tradingtype as ct ON ct.tradingtype_code =st.tradingtype_code
            left join smc_school as sc on sc.school_id=st.school_id
            left join smc_student_family as sf on sf.student_id=st.student_id and sf.family_isdefault=1
            where {$datawhere} 
            and st.company_id='{$request['company_id']}' 
            and st.tradingtype_code<>'Subscribed' 
            and st.tradingtype_code<>'MonthlyShare'
            order by st.trading_id desc
            ");
        } else {
            $tradeList = $this->DataControl->selectClear("select st.trading_pid,st.tradingtype_code,s.student_cnname,s.student_enname,s.student_branch
            ,st.trading_createtime,st.tradingtype_code,ct.tradingtype_name,st.student_id,st.trading_status
            ,sc.school_cnname,sc.school_branch,st.school_id
            ,(select staffer_cnname from smc_staffer where staffer_id=st.staffer_id) as staffer_name
            ,sf.family_mobile,sf.family_cnname
	        ,(select y.coursecat_istreaty from smc_payfee_order x,smc_code_coursecat y where x.trading_pid=st.trading_pid and x.order_type=0 and x.coursecat_id=y.coursecat_id) as coursecat_istreaty
            ,(select companies_cnname from gmc_code_companies where companies_id=st.companies_id) as companies_cnname
            from smc_student_trading as st
            left join smc_student as s on s.student_id=st.student_id
            left join smc_code_tradingtype as ct ON ct.tradingtype_code =st.tradingtype_code
            left join smc_school as sc on sc.school_id=st.school_id
            left join smc_student_family as sf on sf.student_id=st.student_id and sf.family_isdefault=1
            where {$datawhere} 
            and st.company_id='{$request['company_id']}' 
            and st.tradingtype_code<>'Subscribed' 
            and st.tradingtype_code<>'MonthlyShare'
            group by st.trading_pid
            order by st.trading_id desc
            limit {$pagestart},{$num}
            ");
        }

        if (!$tradeList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $list = array();

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $order_isinvoice = $this->LgArraySwitch(array("0" => "未开具", "1" => "已开具"));
        $porder_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $rorder_type = $this->LgArraySwitch(array("0" => "银行转账", "1" => "原路返还"));
        $porder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));
        $rorder_status = $this->LgArraySwitch(array('0' => '待审核(校园)', '1' => '待审核(集团)', '2' => '待处理', '3' => '待确定金额', '4' => '已完成', '-1' => '审核拒绝'));
        $dealorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        foreach ($tradeList as $val) {
            $data = array();
            $data['trading_pid'] = $val['trading_pid'];
            $data['coursecat_istreaty'] = $val['coursecat_istreaty'];
            $data['student_id'] = $val['student_id'];
            $data['school_id'] = $val['school_id'];
            $data['school_cnname'] = $val['school_cnname'];
            $data['school_branch'] = $val['school_branch'];
            $data['student_cnname'] = $val['student_cnname'];
            $data['student_enname'] = $val['student_enname'];
            $data['student_branch'] = $val['student_branch'];
            $data['tradingtype_name'] = $val['tradingtype_name'];
            $data['staffer_name'] = $val['staffer_name'];
            $data['family_mobile'] = $val['family_mobile'] ? $val['family_mobile'] : '--';
            $data['family_cnname'] = $val['family_cnname'] ? $val['family_cnname'] : '--';
            $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);
            $data['companies_cnname'] = $val['companies_cnname'];

            if ($val['tradingtype_code'] == 'CourseForward') {
                $ForwardOne = $this->DataControl->selectOne("select dealorder_pid,dealorder_status,dealorder_balanceprice,dealorder_forwardprice from smc_forward_dealorder where trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $ForwardOne['dealorder_pid'];
                $data['order_type'] = $this->LgStringSwitch('课程结转订单');
                $data['order_status'] = $dealorder_status[$ForwardOne['dealorder_status']];
                $data['order_from'] = '--';
                $data['order_allprice'] = $ForwardOne['dealorder_balanceprice'] + $ForwardOne['dealorder_forwardprice'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['catdeposit'] = '--';
                $data['order_paymentprice'] = '--';
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = $ForwardOne['dealorder_balanceprice'];
                $data['dealorder_forwardprice'] = $ForwardOne['dealorder_forwardprice'];
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['can_debt'] = '0';
                $data['item_name'] = '--';
                $data['item_branch'] = '--';
            } elseif ($val['tradingtype_code'] == 'Accountrefund') {
                $refundOne = $this->DataControl->selectOne("select refund_pid,refund_type,refund_payprice,refund_status,refund_specialprice,refund_from,refund_price,refund_class from smc_refund_order where trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $refundOne['refund_pid'];
                $data['order_type'] = $rorder_type[$refundOne['refund_type']];
                $data['order_status'] = $rorder_status[$refundOne['refund_status']];
                $data['order_from'] = $order_from[$refundOne['refund_from']];
                $data['order_allprice'] = $refundOne['refund_payprice'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['catdeposit'] = '--';
                $data['order_paymentprice'] = $refundOne['refund_payprice'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = $refundOne['refund_payprice'];
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['order_refusereason'] = '--';
                $data['can_cancel'] = 0;
                if ($refundOne['refund_class'] == 0 && $refundOne['refund_status'] == 0) {
                    $data['is_should_check'] = 1;
                } else {
                    $data['is_should_check'] = 0;
                }
                $data['can_debt'] = '0';
                $data['can_pay'] = '0';
                $data['item_name'] = '--';
                $data['item_branch'] = '--';
            } elseif ($val['tradingtype_code'] == 'ReduceCourse') {
                $reduceOne = $this->DataControl->selectOne("select reduceorder_pid,reduceorder_status,reduceorder_figure from smc_course_reduceorder where trading_pid='{$val['trading_pid']}'");
                $reduceorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝', "-2" => '已取消'));

                $data['order_pid'] = $reduceOne['reduceorder_pid'];
                $data['order_type'] = '扣课订单';
                $data['order_status'] = $reduceorder_status[$reduceOne['reduceorder_status']];
                $data['order_from'] = '教师下单';
                $data['order_allprice'] = $reduceOne['reduceorder_figure'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['catdeposit'] = '--';
                $data['order_paymentprice'] = $reduceOne['reduceorder_figure'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['can_debt'] = '0';
                $data['item_name'] = '--';
                $data['item_branch'] = '--';
            } elseif ($val['tradingtype_code'] == 'TransferIn') {
                $sql = "select * from smc_school_trading where trading_topid='{$val['trading_pid']}' and company_id='{$request['company_id']}'";

                $schoolTradeOne = $this->DataControl->selectOne($sql);
                $status = $this->LgArraySwitch(array('0' => '待审核', '2' => '已完成', '-1' => '审核拒绝'));
                $data['order_pid'] = '--';
                $data['order_type'] = $this->LgStringSwitch('学员余额转入');
                $data['order_status'] = $status[$schoolTradeOne['trading_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $schoolTradeOne['trading_price'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['catdeposit'] = '--';
                $data['order_paymentprice'] = $schoolTradeOne['trading_price'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['can_debt'] = '0';
                $data['item_name'] = '--';
                $data['item_branch'] = '--';
            } elseif ($val['tradingtype_code'] == 'TransferOut') {
                $sql = "select * from smc_school_trading where trading_frompid='{$val['trading_pid']}' and company_id='{$request['company_id']}'";
                $schoolTradeOne = $this->DataControl->selectOne($sql);
                $status = $this->LgArraySwitch(array('0' => '待审核', '2' => '已完成', '-1' => '审核拒绝'));
                $data['order_pid'] = '--';
                $data['order_type'] = $this->LgStringSwitch('学员余额转出');
                $data['order_status'] = $status[$schoolTradeOne['trading_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $schoolTradeOne['trading_price'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['catdeposit'] = '--';
                $data['order_paymentprice'] = $schoolTradeOne['trading_price'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['can_debt'] = '0';
                $data['item_name'] = '--';
                $data['item_branch'] = '--';
            } elseif ($val['tradingtype_code'] == 'ClassGiving') {
                $orderOne = $this->DataControl->selectOne("select order_pid,order_status,order_alltimes,order_refusereason from smc_freehour_order where trading_pid='{$val['trading_pid']}'");

                $freeorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

                $data['order_pid'] = $orderOne['order_pid'];
                $data['order_type'] = $this->LgStringSwitch('课次赠送订单');
                $data['order_status'] = $freeorder_status[$orderOne['order_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = '--';
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['catdeposit'] = '--';
                $data['order_paymentprice'] = '--';
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['can_debt'] = '0';
                $data['order_refusereason'] = $orderOne['order_refusereason'];
                $data['item_name'] = '--';
                $data['item_branch'] = '--';
            } else {
                $orderOne = $this->DataControl->selectOne("select po.order_pid,po.order_type,po.order_status,po.order_from,po.order_allprice,po.order_coupon_price,po.order_market_price,po.student_id,po.school_id,po.order_paymentprice,po.order_arrearageprice,po.mergeorder_pid
              ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=1 and pop.paytype_code='balance') as balance
              ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=1 and pop.paytype_code='forward') as forward
              ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=1 and pop.paytype_code='catdeposit') as catdeposit
              ,ifnull((select pop.pay_id from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=0 and pop.paytype_code='canceldebts'),0) as pay_id
              ,po.order_paymentprice,po.order_paidprice,po.order_isinvoice
              ,(select count(1) from smc_student_protocol where student_id=po.student_id and order_pid=po.order_pid and protocol_isdel=0) as protocol_num
              ,(select count(1) from smc_student_protocol where student_id=po.student_id and order_pid=po.order_pid and protocol_isdel=0 and protocol_issign=1) as protocol_sign_num
              ,(select school_payaftersignlimit from smc_school where school_id=po.school_id) as school_payaftersignlimit
              ,ifnull((select pop.pay_id from smc_payfee_order_pay as pop,smc_code_paytype as pt where pop.paytype_code=pt.paytype_code and pop.order_pid=po.order_pid and pop.pay_issuccess=1 and pt.paytype_ischarge=1 limit 1),0) as ispay_id
              from smc_payfee_order as po
              where po.trading_pid='{$val['trading_pid']}'");

                $dealorder_balanceprice = "--";
                if ($orderOne['order_type'] == 2 && $orderOne['order_status'] == 4) {
                    $dealorder_balanceprice = $orderOne['order_paymentprice'];
                }
                if ($orderOne['order_status'] == 4 && $orderOne['order_type'] <> 2) {
                    $order_pay = $this->DataControl->selectOne("select sum(pay_price) as price_sum from smc_payfee_order_pay where  paytype_code='balance' and pay_issuccess =1 and  pay_isrefund=0 and order_pid='{$orderOne['order_pid']}' ");

                    $dealorder_balanceprice = $order_pay['price_sum'];
                }

                if ($orderOne['order_from'] == '2' || $orderOne['order_status'] < 0 || $orderOne['ispay_id'] > 0) {
                    $data['can_cancel'] = 0;
                } else {
                    $data['can_cancel'] = 1;
                }

                $data['order_pid'] = $orderOne['order_pid'];
                $data['mergeorder_pid'] = $orderOne['mergeorder_pid'];
                $data['order_type'] = $porder_type[$orderOne['order_type']];
                $data['order_status'] = $porder_status[$orderOne['order_status']];
                $data['order_from'] = $order_from[$orderOne['order_from']];
                $data['order_allprice'] = $orderOne['order_allprice'];
                $data['coupon_price'] = $orderOne['order_coupon_price'] + $orderOne['order_market_price'];
                if ($orderOne['balance']) {
                    $data['balance'] = $orderOne['balance'];
                } else {
                    $data['balance'] = 0;
                }
                if ($orderOne['forward']) {
                    $data['forward'] = $orderOne['forward'];
                } else {
                    $data['forward'] = 0;
                }
                if ($orderOne['catdeposit']) {
                    $data['catdeposit'] = $orderOne['catdeposit'];
                } else {
                    $data['catdeposit'] = 0;
                }

                if ($orderOne['school_payaftersignlimit'] == '1' && $orderOne['order_type'] == 0) {
                    if (($orderOne['protocol_num'] == 0 || $orderOne['protocol_num'] !== $orderOne['protocol_sign_num'])) {
                        $data['all_sign'] = 0;
                    } else {
                        $data['all_sign'] = 1;
                    }
                } else {
                    $data['all_sign'] = 1;
                }

                $data['order_paymentprice'] = $orderOne['order_paymentprice'];
                $data['order_paidprice'] = $orderOne['order_paidprice'];
                $data['order_arrears'] = $orderOne['order_paymentprice'] - $orderOne['order_paidprice'];
                $data['order_isinvoice'] = $order_isinvoice[$orderOne['order_isinvoice']];

                $data['dealorder_balanceprice'] = $dealorder_balanceprice;

                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';

                if ($orderOne['order_status'] > 0 && $orderOne['order_status'] < 4) {
                    if ($orderOne['pay_id'] > 0) {
                        $data['is_should_pay'] = '0';
                    } else {
                        $data['is_should_pay'] = '1';
                    }
                } else {
                    $data['is_should_pay'] = 0;
                }
                if ($orderOne['order_type'] == 0 && $data['order_arrears'] > 0 && $orderOne['order_status'] > 0 && $orderOne['order_arrearageprice'] > 0 && $orderOne['pay_id'] == 0) {
                    $sql = "select scb.coursebalance_id
                          from smc_payfee_order_course as poc 
                          left join smc_payfee_order as po on po.order_pid=poc.order_pid
                          left join smc_student_coursebalance as scb on scb.student_id=po.student_id and scb.course_id=poc.course_id and scb.school_id=po.school_id
                          where scb.coursebalance_figure>0 and poc.order_pid='{$orderOne['order_pid']}'";

                    if ($this->DataControl->selectOne($sql)) {
                        $data['can_debt'] = '0';
                    } else {
                        $data['can_debt'] = '1';
                    }
                } else {
                    $data['can_debt'] = '0';
                }
                if ($orderOne['order_type'] == 0) {
                    $sql = "select group_concat(c.course_branch) as course_branch,group_concat(c.course_cnname) as course_cnname
                            from smc_payfee_order_course as oc 
                            left join smc_course as c ON c.course_id  = oc.course_id
                            where oc.order_pid ='{$orderOne['order_pid']}'";
                    $course = $this->DataControl->selectOne($sql);
                    $data['item_name'] = $course['course_cnname'] ? $course['course_cnname'] : '--';
                    $data['item_branch'] = $course['course_branch'] ? $course['course_branch'] : '--';
                } else {
                    $data['item_name'] = '--';
                    $data['item_branch'] = '--';
                }
            }
            $list[] = $data;
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            if (!$list) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($list) {
                $outexceldate = array();
                foreach ($list as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['item_name'] = $dateexcelvar['item_name'];
                    $datearray['item_branch'] = $dateexcelvar['item_branch'];
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $datearray['order_type'] = $dateexcelvar['order_type'];
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['family_mobile'] = $dateexcelvar['family_mobile'];
                    $datearray['order_status'] = $dateexcelvar['order_status'];
                    $datearray['order_from'] = $dateexcelvar['order_from'];
                    $datearray['order_allprice'] = $dateexcelvar['order_allprice'];
                    $datearray['coupon_price'] = $dateexcelvar['coupon_price'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                    $datearray['order_arrears'] = $dateexcelvar['order_arrears'];
                    $datearray['order_isinvoice'] = $dateexcelvar['order_isinvoice'];
                    $datearray['dealorder_balanceprice'] = $dateexcelvar['dealorder_balanceprice'];
                    $datearray['dealorder_forwardprice'] = $dateexcelvar['dealorder_forwardprice'];
                    $datearray['tradingtype_name'] = $dateexcelvar['tradingtype_name'];
                    $datearray['order_refusereason'] = $dateexcelvar['order_refusereason'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_name'];
                    $datearray['trading_createtime'] = $dateexcelvar['trading_createtime'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    $datearray['balance'] = $dateexcelvar['balance'];
                    $datearray['forward'] = $dateexcelvar['forward'];
                    $datearray['catdeposit'] = $dateexcelvar['catdeposit'];

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("学校名称", "学校编号", "学员中文名", "学员英文名", "学员编号", "项目名称", "项目编号", "交易编号", "订单类型", "订单主体", "下单手机号", "订单状态"
            , "订单来源", "订单总额", "优惠金额", "应付金额", "已付金额", "欠费金额", "开票状态", "账户余额变动", "结转金额", "交易类型", "拒绝原因", "经办人", "创建时间", "家长姓名", "使用账户余额", "使用结转金额", "使用定金金额"));
            $excelfileds = array("school_cnname", "school_branch", "student_cnname", "student_enname", "student_branch", "item_name", "item_branch", "trading_pid", "order_type", "companies_cnname", "family_mobile", "order_status"
            , "order_from", "order_allprice", "coupon_price", "order_paymentprice", "order_paidprice", "order_arrears", "order_isinvoice", "dealorder_balanceprice", "dealorder_forwardprice", "tradingtype_name", "order_refusereason", "staffer_name", "trading_createtime", "family_cnname", "balance", "forward", "catdeposit");
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("学员交易订单明细表{$list[0]['student_cnname']}.xlsx"));
            exit;
        } else {
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select count(distinct st.trading_id) as num
                from smc_student_trading as st
                left join smc_student as s on s.student_id=st.student_id
                where {$datawhere} and st.company_id='{$request['company_id']}' 
                and st.tradingtype_code<>'Subscribed' 
                and st.tradingtype_code<>'MonthlyShare'
                ";
                $db_nums = $this->DataControl->selectOne($count_sql);
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $list;
            return $data;
        }
    }

    function getMergeOrderList($request){

        $datawhere = " a.company_id = '{$this->company_id}' and  a.mergeorder_status<>-2";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.student_cnname like '%{$request['keyword']}%' or c.student_enname like '%{$request['keyword']}%' or c.student_branch like '%{$request['keyword']}%' or a.mergeorder_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and a.school_id ='{$request['school_id']}'";
        }

        if (isset($request['mergeorder_status']) && $request['mergeorder_status'] !== "") {
            $datawhere .= " and a.mergeorder_status ='{$request['mergeorder_status']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.mergeorder_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.mergeorder_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.student_id,a.mergeorder_pid,c.student_cnname,c.student_enname,c.student_branch,d.family_mobile,a.mergeorder_status,a.mergeorder_createtime
                ,ifnull((select count(x.eorders_id) from smc_payfee_mergeorder_eorders as x where x.mergeorder_pid=a.mergeorder_pid),0) as courseNum
                ,ifnull((select sum(x.eorders_payprice) from smc_payfee_mergeorder_eorders as x where x.mergeorder_pid=a.mergeorder_pid),0) as eorders_payprice
                from smc_payfee_mergeorder as a 
                left join smc_student as c on c.student_id=a.student_id
                left join smc_student_family as d on d.student_id=c.student_id and d.family_isdefault=1
                where {$datawhere}
                group by a.mergeorder_pid
                order by a.mergeorder_createtime desc
                ";

        $status = $this->LgArraySwitch(array("0" => "待支付", "1" => "已完成", "-1" => "已取消"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['mergeorder_pid'] = $dateexcelvar['mergeorder_pid'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['courseNum'] = $dateexcelvar['courseNum'];
                    $datearray['companiesNum'] = $dateexcelvar['courseNum'];
                    $datearray['family_mobile'] = $dateexcelvar['family_mobile'];
                    $datearray['mergeorder_status_name'] = $status[$dateexcelvar['mergeorder_status']];
                    $datearray['eorders_payprice'] = $dateexcelvar['eorders_payprice'];
                    $datearray['mergeorder_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['mergeorder_createtime']);

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('组合交易编号', '学员中文名', '学员英文名', '学员编号', '涉及订单数', '涉及主体', '下单手机号', '订单状态', '订单金额', '创建时间'));
            $excelfileds = array('mergeorder_pid', 'student_cnname', 'student_enname', 'student_branch', 'courseNum', 'companiesNum', 'family_mobile', 'mergeorder_status_name', 'eorders_payprice', 'mergeorder_createtime');

            $fielname = $this->LgStringSwitch("组合订单列表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }

        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }

        foreach ($orderList as &$orderOne) {
            $orderOne['mergeorder_status_name'] = $status[$orderOne['mergeorder_status']];
            $orderOne['mergeorder_createtime'] = date("Y-m-d H:i:s", $orderOne['mergeorder_createtime']);
        }


        $all_num = $this->DataControl->selectClear("
        select a.mergeorder_pid,c.student_cnname,c.student_enname,c.student_branch,d.family_mobile,a.mergeorder_status
                ,ifnull((select count(x.eorders_id) from smc_payfee_mergeorder_eorders as x where x.mergeorder_pid=a.mergeorder_pid),0) as courseNum
                ,ifnull((select sum(x.eorders_payprice) from smc_payfee_mergeorder_eorders as x where x.mergeorder_pid=a.mergeorder_pid),0) as eorders_payprice
                from smc_payfee_mergeorder as a 
                left join smc_student as c on c.student_id=a.student_id
                left join smc_student_family as d on d.student_id=c.student_id and d.family_isdefault=1
                where {$datawhere} 
                group by a.mergeorder_pid");

        $data = array();
        $data['allnum'] = $all_num?count($all_num):0;
        $data['list'] = $orderList;
        return $data;
    }

    function orderStatusList($request)
    {
        if (!isset($request['tradingtype_code']) || $request['tradingtype_code'] == '') {
            $this->error = true;
            $this->errortip = "请先选择交易类型";
            return false;
        }

        $data = array();
        if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'CourseMakeUp') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'PayitemFee') {

            $status = $this->LgArraySwitch(array('1' => '待支付', '2' => '支付中', '4' => '已完成', '-1' => '已取消'));

        } elseif ($request['tradingtype_code'] == 'Recharge') {

            $status = $this->LgArraySwitch(array('1' => '待支付', '4' => '已完成', '-1' => '已取消'));

        } elseif ($request['tradingtype_code'] == 'Accountrefund') {

            $status = $this->LgArraySwitch(array('0' => '待审核(校园)', '1' => '待审核(集团)', '2' => '待处理', '3' => '待确定金额', '4' => '已完成', '-1' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'CourseForward') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'ClassGiving') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-2' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'TransferIn' || $request['tradingtype_code'] == 'TransferOut') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '2' => '已完成', '-1' => '审核拒绝'));

        } else {
            $this->error = true;
            $this->errortip = "该类型无状态需求";
            return false;
        }

        if ($status) {
            foreach ($status as $key => $val) {
                $tem_data = array();
                $tem_data['status'] = $key;
                $tem_data['name'] = $val;
                $data[] = $tem_data;
            }
        }

        return $data;
    }

    function refundInfo($request)
    {
        $refundOne = $this->DataControl->getFieldOne("smc_refund_order", "refund_bank,refund_accountname,refund_bankcard", "trading_pid='{$request['trading_pid']}'");
        if ($refundOne) {
            return $refundOne;
        } else {
            $this->error = true;
            $this->errortip = "无对应退费订单";
            return false;
        }
    }

    function orderList($request)
    {

        $datawhere = "po.order_status <> '0' and po.order_status <> '-1'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or sf.family_mobile like '%{$request['keyword']}%' or sf.family_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or po.order_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['order_status']) && $request['order_status'] == 1) {
            $datawhere .= " and po.order_status = '4'";
        } else {
            $datawhere .= " and po.order_status <> '4'";
        }

        $sql = "select po.order_pid,po.order_createtime,s.student_cnname,s.student_enname,s.student_branch
                ,po.order_status,po.order_paymentprice,po.order_paidprice
                from smc_payfee_order as po
                left join smc_student as s on s.student_id=po.student_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                WHERE {$datawhere} and po.school_id='{$request['school_id']}' and po.company_id='{$request['company_id']}'
                order by po.order_createtime desc
                limit {$pagestart},{$num}";
        $orderList = $this->DataControl->selectClear($sql);
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        foreach ($orderList as &$val) {
//          if($request['from'] == 'app'){
//              $val['order_paymentprice']  = $val['order_paymentprice']/10000;
//              $val['arrears']=($val['order_paymentprice']-$val['order_paidprice'])/10000;
//              $val['order_paidprice'] = $val['order_paidprice']/10000;
//          }

            $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
            $val['order_status_name'] = $order_status[$val['order_status']];
            $val['arrears'] = $val['order_paymentprice'] - $val['order_paidprice'];
        }

        return $orderList;
    }


    function orderInfo($request)
    {
        $datawhere = " 1 ";
        if (isset($request['student_id']) && $request['student_id'] != '') {
            $datawhere .= "and b.student_id ='{$request['student_id']}'";
        }

//        if ($request['staffer_id'] == '25721') {
//            $datawhere .= " and b.tradingtype_code in ('PayrenewFee','PaynewFee')";
//
//            $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=b.trading_pid and po.school_id='{$request['school_id']}' and po.coursetype_id in (79654))";
//
//        }

        $orderAll = $this->DataControl->selectOne("select count(a.order_id) as num
            ,sum(a.order_allprice) as allprice
            ,sum(a.order_paymentprice) as paymentprice
            ,sum(a.order_paidprice) as paidprice 
            from smc_payfee_order as a
            left join smc_student_trading as b on b.trading_pid=a.trading_pid
            where {$datawhere} 
            and a.company_id='{$request['company_id']}' 
            and a.school_id='{$request['school_id']}' 
            and a.order_status >= 0");

        $tradeAll = $this->DataControl->selectOne("select count(b.trading_id) as num 
            from smc_student_trading as b
            where {$datawhere} and b.company_id='{$request['company_id']}' 
            and b.school_id='{$request['school_id']}' 
            and b.tradingtype_code<>'Subscribed' 
            and b.tradingtype_code<>'MonthlyShare'");

        $refundALL = $this->DataControl->selectOne("select count(a.refund_id) as num
            ,sum(a.refund_price) as refundprice
            from smc_refund_order as a
            left join smc_student_trading as b on b.trading_pid=a.trading_pid
            where {$datawhere}
            and a.company_id='{$request['company_id']}'
            and a.school_id='{$request['school_id']}'
            and a.refund_status>=0");

        $type_list = $this->DataControl->selectClear("select distinct b.tradingtype_code,
            c.tradingtype_name
            from smc_student_trading b,smc_code_tradingtype c
            where {$datawhere} 
            and b.tradingtype_code=c.tradingtype_code
            and b.company_id='{$request['company_id']}' 
            and b.school_id='{$request['school_id']}' 
            AND b.trading_status>= 0");

        $data = array();
        $data['type_list'] = $type_list ? $type_list : array();
        $data['num'] = $tradeAll['num'] ? $tradeAll['num'] : 0;
        $data['allprice'] = $orderAll['allprice'] ? $orderAll['allprice'] : 0;
        $data['paymentprice'] = $orderAll['paymentprice'] ? $orderAll['paymentprice'] : 0;
        $data['paidprice'] = $orderAll['paidprice'] ? $orderAll['paidprice'] : 0;
        $data['arrears'] = $data['paymentprice'] - $data['paidprice'];
        $data['refundprice'] = $refundALL['refundprice'] ? $refundALL['refundprice'] : 0;
        return $data;
    }

    function orderContractList($request)
    {
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "order_pid,order_createtime", "trading_pid='{$request['trading_pid']}'");

        if (!$orderOne) {
            $this->error = true;
            $this->errortip = "无订单";
            return false;
        }

        $sql = "select oc.course_id,oc.agreement_id,oc.pricing_id,c.course_cnname from smc_payfee_order_course as oc, smc_course as c where oc.order_pid='{$orderOne['order_pid']}'and oc.course_id =c.course_id order by ordercourse_id asc";
        $courseList = $this->DataControl->selectClear($sql);
        if (!$courseList) {
            $this->error = true;
            $this->errortip = "订单无课程";
            return false;
        }

        $num = count($courseList) * 2;
        $list = array();
        for ($i = 1; $i <= $num; $i++) {
            $sort = ceil($i / 2) - 1;
            $data = array();
            $data['order_pid'] = $orderOne['order_pid'] . '_' . $i;
            $data['course_id'] = $courseList[$sort]['course_id'];
            $data['course_cnname'] = $courseList[$sort]['course_cnname'];
            $data['agreement_id'] = $courseList[$sort]['agreement_id'];
            $data['order_createtime'] = date("Y-m-d H:i:s", $orderOne['order_createtime']);
            $data['pricing_id'] = $courseList[$sort]['pricing_id'];
            $list[] = $data;
        }
        return $list;
    }

    function averagePrice($price, $num)
    {
        $number = $price;
        $total = $num;
        $divide_number = bcdiv($number, $total, 2);
        $last_number = bcsub($number, $divide_number * ($total - 1), 2);
        $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
        $numArray = explode("+", $number_str);
        return $numArray;
    }

    function contractItem($request)
    {
        $order_pid = substr($request['order_pid'], 0, strrpos($request['order_pid'], "_"));
        $sort = substr($request['order_pid'], strripos($request['order_pid'], "_") + 1);
        $orderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid='{$order_pid}'");
        if (!$orderOne) {
            $this->error = true;
            $this->errortip = "订单不存在";
            return false;
        }
        $sql = "select c.class_cnname,poc.ordercourse_buynums from smc_payfee_order_course as poc left join smc_class as c on c.class_id=poc.class_id where poc.order_pid='{$order_pid}' and poc.course_id='{$request['course_id']}'";
        $orderCourseOne = $this->DataControl->selectOne($sql);

        $sql = "select course_id from smc_payfee_order_course where order_pid='{$orderOne['order_pid']}' order by ordercourse_id asc";
        $orderCourseList = $this->DataControl->selectClear($sql);
        if (!$orderCourseList) {
            $this->error = true;
            $this->errortip = "订单无课程";
            return false;
        }

        $num = count($orderCourseList) * 2;

        $sql = "SELECT s.student_branch,s.student_cnname,s.student_enname,s.student_sex,s.student_birthday
              ,sf.family_mobile,sf.family_cnname,b.student_balance,sch.school_cnname
                FROM smc_student as s
                LEFT JOIN smc_student_balance as b ON b.student_id = s.student_id and b.school_id='{$this->school_id}' and b.company_id='{$this->company_id}'
                left join smc_student_enrolled as se on se.student_id=s.student_id
                left join smc_school as sch on sch.school_id=se.school_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                WHERE s.student_id='{$orderOne['student_id']}' and se.school_id='{$this->school_id}' limit 0,1";
        $studentOne = $this->DataControl->selectOne($sql);

        $balance = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$order_pid}' and paytype_code='balance' and pay_issuccess=1 and pay_isrefund=0");

        $forward = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$order_pid}' and paytype_code='forward' and pay_issuccess=1 and pay_isrefund=0");


        $stu_data = array();
        $stu_data['student_cnname'] = $studentOne['student_cnname'];
        $stu_data['student_enname'] = $studentOne['student_enname'];
        $stu_data['student_branch'] = $studentOne['student_branch'];
        $stu_data['student_sex'] = $studentOne['student_sex'];
        $stu_data['student_birthday'] = $studentOne['student_birthday'] ? date("Y-m-d", strtotime($studentOne['student_birthday'])) : '';
        $stu_data['school_cnname'] = $studentOne['school_cnname'];
        $stu_data['class_cnname'] = $orderCourseOne['class_cnname'] ? $orderCourseOne['class_cnname'] : '';
        $stu_data['student_balance'] = $studentOne['student_balance'] ? $studentOne['student_balance'] : 0;

        $fam_data = array();
        $fam_data['family_cnname'] = $studentOne['family_cnname'];
        $fam_data['family_mobile'] = $studentOne['family_mobile'];

        $o_data = array();

        $order_price = $this->averagePrice($orderOne['order_allprice'], $num);
        $discount = $this->averagePrice($orderOne['order_coupon_price'] + $orderOne['order_market_price'], $num);
        $order_paymentprice = $this->averagePrice($orderOne['order_paymentprice'], $num);
        $order_paidprice = $this->averagePrice($orderOne['order_paidprice'], $num);
        $order_arrearageprice = $this->averagePrice($orderOne['order_arrearageprice'], $num);

        if ($balance['pay_price'] > 0) {
            $balance_price = $this->averagePrice($balance['pay_price'], $num);
            $o_data['balance_price'] = $balance_price[$sort - 1];
        } else {
            $o_data['balance_price'] = 0;
        }

        if ($forward['pay_price'] > 0) {
            $forward_price = $this->averagePrice($forward['pay_price'], $num);
            $o_data['forward_price'] = $forward_price[$sort - 1];
        } else {
            $o_data['forward_price'] = 0;
        }

        $o_data['order_pid'] = $request['order_pid'];
        $o_data['order_allprice'] = $order_price[$sort - 1];
        $o_data['order_discount'] = $discount[$sort - 1];
        $o_data['order_paymentprice'] = $order_paymentprice[$sort - 1];
        $o_data['order_paidprice'] = $order_paidprice[$sort - 1];
        $o_data['order_arrearageprice'] = $order_arrearageprice[$sort - 1];

        $good_data = array();

        $all_price = 0;
        $courseNum = $this->averagePrice($orderCourseOne['ordercourse_buynums'], 2);
        if ($sort % 2 == 0) {
            $good_data['num'] = $this->LgStringSwitch($courseNum['1'] . '课次/' . ($courseNum['1'] * 2) . '课时');
            $good_data['goodsName'] = array();
            $good_data['price'] = $all_price;
        } else {
            $sql = "select g.goods_cnname as name,fpp.products_sellingprice
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$request['pricing_id']}' and g.company_id='{$this->company_id}'
              ";

            $goodsList = $this->DataControl->selectClear($sql);

            if ($goodsList) {
                foreach ($goodsList as $goodsOne) {
                    $all_price += $goodsOne['products_sellingprice'];
                }
            } else {
                $goodsList = array();
            }

            $sql = "select f.feeitem_cnname as name,fpi.items_sellingprice
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$request['pricing_id']}' and f.company_id='{$this->company_id}'
              group by fpi.feeitem_branch
              ";
            $itemList = $this->DataControl->selectClear($sql);
            if ($itemList) {
                foreach ($itemList as $itemOne) {
                    $all_price += $itemOne['items_sellingprice'];
                }
            } else {
                $itemList = array();
            }

            $goodsName = array_merge($goodsList, $itemList);
            $good_data['num'] = $this->LgStringSwitch($courseNum['0'] . '课次/' . ($courseNum['0'] * 2) . '课时');
            $good_data['goodsName'] = $goodsName;
            $good_data['price'] = $all_price;
        }


        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch", "course_id='{$request['course_id']}'");
        $good_data['course_cnname'] = $courseOne['course_cnname'] . '(' . $courseOne['course_branch'] . ')';

        $data = array();
        $data['stuInfo'] = $stu_data;
        $data['familyInfo'] = $fam_data;
        $data['orderInfo'] = $o_data;
        $data['goodInfo'] = $good_data;

        return $data;

    }


    function orderItem($request)
    {
        $order_tradingOne = $this->DataControl->selectOne(
            "select st.*,ct.tradingtype_name from smc_student_trading as st
                  left join  smc_code_tradingtype as  ct  ON ct.tradingtype_code = st.tradingtype_code
                where  st.trading_pid='{$request['trading_pid']}'");

        if (!$order_tradingOne) {
            $this->error = true;
            $this->errortip = "无订单";
            return false;
        } else {
            $list = array();
            $orderOne['order_type'] = $order_tradingOne['tradingtype_name'];

            if ($order_tradingOne['tradingtype_code'] == 'CourseForward') {
                $ForwardOne = $this->DataControl->selectOne("select dealorder_pid,dealorder_status,dealorder_balanceprice,dealorder_forwardprice,   dealorder_createtime,s.school_cnname,student_cnname
                from smc_forward_dealorder as sfd
                left join smc_school as s ON sfd.school_id = s.school_id
                left join smc_student as st ON st.student_id = sfd.student_id
                where trading_pid='{$order_tradingOne['trading_pid']}'");


                $orderOne['order_pid'] = $ForwardOne['dealorder_pid'];
                $orderOne['from'] = $this->LgStringSwitch("系统—课程结转");
                $orderOne['order_createtime'] = date('Y-m-d H:i:s', $ForwardOne['dealorder_createtime']);
                $orderOne['pay_typename'] = "";
                $orderOne['school_cnname'] = $ForwardOne['school_cnname'];
                $orderOne['paylog_addtime'] = "";
                $orderOne['student_cnname'] = $ForwardOne['student_cnname'];

                //订单跟踪
                $logList = $this->DataControl->selectClear("select * from smc_payfee_order_tracks where order_pid='{$ForwardOne['dealorder_pid']}'");
                $payList = array();
                $info = array();
                $info[0]['order_allprice'] = '';
                $info[0]['order_coupon_price'] = '';
                $info[0]['order_paymentprice'] = '';
                $info[0]['balance'] = '';
                $info[0]['forward'] = '';
            } elseif ($order_tradingOne['tradingtype_code'] == 'Accountrefund') {

                $refundOne = $this->DataControl->selectOne("select refund_pid,refund_type,refund_status,refund_from,refund_price,refund_createtime,s.school_cnname,student_cnname
                      from smc_refund_order as ro
                      left join smc_school as s ON ro.school_id = s.school_id
                      left join smc_student as st ON st.student_id = ro.student_id
                    where trading_pid='{$order_tradingOne['trading_pid']}'");


                $orderOne['order_pid'] = $refundOne['refund_pid'];
                $orderOne['from'] = $this->LgStringSwitch("系统-账户退费");
                $orderOne['order_createtime'] = date('Y-m-d H:i:s', $refundOne['refund_createtime']);
                $orderOne['pay_typename'] = "";
                $orderOne['school_cnname'] = $refundOne['school_cnname'];
                $orderOne['paylog_addtime'] = "";
                $orderOne['student_cnname'] = $refundOne['student_cnname'];

                $logList = $this->DataControl->selectClear("select * from `smc_refund_order_tracks` where refund_pid='{$refundOne['refund_pid']}'");


                $payList = array();
                $info = array();
                $info[0]['order_allprice'] = '';
                $info[0]['order_coupon_price'] = '';
                $info[0]['order_paymentprice'] = '';
                $info[0]['balance'] = '';
                $info[0]['forward'] = '';

            } else {
                $orderOne = $this->DataControl->selectOne("select spo.order_pid,spo.order_from,group_concat(distinct(pop.pay_typename)) as pay_typename,s.student_cnname,sc.school_cnname,spo.order_createtime,s.student_branch
    ,(select spop.paylog_addtime from smc_payfee_order_paylog as spop where spop.order_pid=spo.order_pid order by spop.paylog_addtime desc limit 0,1) as paylog_addtime
                                                  from smc_payfee_order as spo
                                                  left join smc_payfee_order_pay as pop on pop.order_pid=spo.order_pid
                                                  left join smc_student as s on s.student_id=spo.student_id
                                                  left join smc_school as sc on sc.school_id=spo.school_id
                                                  where spo.company_id='{$request['company_id']}' and spo.order_pid='{$request['order_pid']}' and spo.school_id='{$request['school_id']}'
                                                  group by spo.order_pid
                                                  ");;
                if (!$orderOne) {
                    $this->error = true;
                    $this->errortip = "无订单";
                    return false;
                }

                $orderOne['order_type'] = $order_tradingOne['tradingtype_name'];
//              $order_type=array("0"=>"课程缴费订单", "1"=>"普通收费订单","2"=>"充值类订单");
                $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统导入"));
                $orderOne['order_from'] = $order_from[$orderOne['order_from']];
//              $orderOne['order_type']=$order_type[$orderOne['order_type']];
                if ($orderOne['order_createtime']) {
                    $orderOne['order_createtime'] = date("Y-m-d H:i:s", $orderOne['order_createtime']);
                } else {
                    $orderOne['order_createtime'] = '';
                }

                if ($orderOne['paylog_addtime']) {
                    $orderOne['paylog_addtime'] = date("Y-m-d H:i:s", $orderOne['paylog_addtime']);
                } else {
                    $orderOne['paylog_addtime'] = '';
                }

                $courseList = $this->DataControl->selectClear("select poc.ordercourse_id as order_id,sc.course_cnname as order_name,sc.course_branch as order_branch,fpt.tuition_originalprice as originalprice,fpt.tuition_sellingprice as sellingprice,fa.agreement_cnname
                                                  from smc_payfee_order_course as poc
                                                  left join smc_payfee_order as spo on spo.order_pid=poc.order_pid
                                                  left join smc_fee_pricing_tuition as fpt on fpt.pricing_id=poc.pricing_id
                                                  left join smc_fee_agreement as fa on fa.agreement_id=poc.agreement_id
                                                  left join smc_course as sc on sc.course_id=poc.course_id
                                                  left join smc_class as c on c.class_id=poc.class_id
                                                  where spo.company_id='{$request['company_id']}' and poc.order_pid='{$request['order_pid']}' and spo.school_id='{$request['school_id']}' group by poc.ordercourse_id
                                                  ");

                $goodsList = $this->DataControl->selectClear("select pog.ordergoods_id as order_id,g.goods_cnname as order_name,fpp.products_originalprice as originalprice,fpp.products_sellingprice as sellingprice,fa.agreement_cnname,g.goods_unit as unit
                                                  from smc_payfee_order_goods as pog
                                                  left join smc_payfee_order as spo on spo.order_pid=pog.order_pid
                                                  left join smc_fee_pricing_products as fpp on fpp.pricing_id=pog.pricing_id and pog.goods_id=fpp.goods_id
                                                  left join smc_fee_agreement as fa on fa.agreement_id=pog.agreement_id
                                                  left join erp_goods as g on g.goods_id=pog.goods_id
                                                  where spo.company_id='{$request['company_id']}' and pog.order_pid='{$request['order_pid']}' and spo.school_id='{$request['school_id']}' group by pog.ordergoods_id");
                if (!$courseList) {
                    $courseList = array();
                }
                if (!$goodsList) {
                    $goodsList = array();
                }

                $list = array_merge($courseList, $goodsList);

                $info = $this->DataControl->selectClear("select po.order_allprice,(po.order_coupon_price + po.order_market_price) as order_coupon_price ,po.order_paymentprice
                                               ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and (pop.paytype_code='balance' or pop.paytype_code='norebalance') and pop.pay_issuccess=1 limit 0,1) as balance
                                               ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.paytype_code='forward' and pop.pay_issuccess=1 limit 0,1) as forward
                                               from smc_payfee_order as po
                                               where po.order_pid='{$request['order_pid']}' and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}'
                                               ");

                $logList = $this->DataControl->selectClear("select * from smc_payfee_order_tracks where order_pid='{$request['order_pid']}'");

                $payList = $this->DataControl->selectClear("select pl.*,p.pay_typename,p.pay_note from smc_payfee_order_paylog as pl left join smc_payfee_order_pay as p on p.pay_pid=pl.pay_pid where pl.order_pid='{$request['order_pid']}'");

                if (!$info) {
                    $info = array();
                }

                if (!$payList) {
                    $payList = array();
                } else {
                    foreach ($payList as &$val) {
                        $val['paylog_paytime'] = date("Y-m-d H:i:s", $val['paylog_paytime']);
                    }
                }

            }

        }
        if ($logList) {
            foreach ($logList as &$val) {
                $val['tracks_time'] = date("Y-m-d H:i:s", $val['tracks_time']);
            }
        } else {
            $logList = array();
        }


        $list_field = array();
        $list_field[0]["fieldstring"] = "order_id";
        $list_field[0]["fieldname"] = $this->LgStringSwitch("序号");
        $list_field[0]["show"] = 1;
        $list_field[0]["custom"] = 0;

        $list_field[1]["fieldstring"] = "order_name";
        $list_field[1]["fieldname"] = $this->LgStringSwitch("项目名称");
        $list_field[1]["show"] = 1;
        $list_field[1]["custom"] = 0;

        $list_field[2]["fieldstring"] = "order_branch";
        $list_field[2]["fieldname"] = $this->LgStringSwitch("项目编号");
        $list_field[2]["show"] = 1;
        $list_field[2]["custom"] = 0;

        $list_field[3]["fieldstring"] = "originalprice";
        $list_field[3]["fieldname"] = $this->LgStringSwitch("原价");
        $list_field[3]["show"] = 1;
        $list_field[3]["custom"] = 0;

        $list_field[4]["fieldstring"] = "sellingprice";
        $list_field[4]["fieldname"] = $this->LgStringSwitch("销售价");
        $list_field[4]["show"] = 1;
        $list_field[4]["custom"] = 0;

        $list_field[5]["fieldstring"] = "agreement_cnname";
        $list_field[5]["fieldname"] = $this->LgStringSwitch("价格批次");
        $list_field[5]["show"] = 1;
        $list_field[5]["custom"] = 0;

        $list_field[6]["fieldstring"] = "unit";
        $list_field[6]["fieldname"] = $this->LgStringSwitch("单位");
        $list_field[6]["show"] = 1;
        $list_field[6]["custom"] = 0;

        $log_field = array();
        $log_field[0]["fieldstring"] = "tracks_id";
        $log_field[0]["fieldname"] = $this->LgStringSwitch("序号");
        $log_field[0]["show"] = 1;
        $log_field[0]["custom"] = 0;

        $log_field[1]["fieldstring"] = "tracks_title";
        $log_field[1]["fieldname"] = $this->LgStringSwitch("标题");
        $log_field[1]["show"] = 1;
        $log_field[1]["custom"] = 0;

        $log_field[2]["fieldstring"] = "tracks_information";
        $log_field[2]["fieldname"] = $this->LgStringSwitch("信息");
        $log_field[2]["show"] = 1;
        $log_field[2]["custom"] = 0;

        $log_field[3]["fieldstring"] = "tracks_playname";
        $log_field[3]["fieldname"] = $this->LgStringSwitch("操作人");
        $log_field[3]["show"] = 1;
        $log_field[3]["custom"] = 0;

        $log_field[4]["fieldstring"] = "tracks_time";
        $log_field[4]["fieldname"] = $this->LgStringSwitch("时间");
        $log_field[4]["show"] = 1;
        $log_field[4]["custom"] = 0;

        $pay_field = array();
        $pay_field[0]["fieldstring"] = "paylog_id";
        $pay_field[0]["fieldname"] = $this->LgStringSwitch("序号");
        $pay_field[0]["show"] = 0;
        $pay_field[0]["custom"] = 0;

        $pay_field[1]["fieldstring"] = "pay_pid";
        $pay_field[1]["fieldname"] = $this->LgStringSwitch("支付请求号");
        $pay_field[1]["show"] = 1;
        $pay_field[1]["custom"] = 0;

        $pay_field[2]["fieldstring"] = "pay_typename";
        $pay_field[2]["fieldname"] = $this->LgStringSwitch("支付方式");
        $pay_field[2]["show"] = 1;
        $pay_field[2]["custom"] = 0;

        $pay_field[3]["fieldstring"] = "paylog_actualprice";
        $pay_field[3]["fieldname"] = $this->LgStringSwitch("实际支付金额");
        $pay_field[3]["show"] = 1;
        $pay_field[3]["custom"] = 0;

        $pay_field[4]["fieldstring"] = "paylog_paytime";
        $pay_field[4]["fieldname"] = $this->LgStringSwitch("实际实付时间");
        $pay_field[4]["show"] = 1;
        $pay_field[4]["custom"] = 0;

        $pay_field[4]["fieldstring"] = "paylog_tradeno";
        $pay_field[4]["fieldname"] = $this->LgStringSwitch("支付交易号");
        $pay_field[4]["show"] = 1;
        $pay_field[4]["custom"] = 0;

        $pay_field[5]["fieldstring"] = "paylog_ifee";
        $pay_field[5]["fieldname"] = $this->LgStringSwitch("手续费");
        $pay_field[5]["show"] = 1;
        $pay_field[5]["custom"] = 0;

        $pay_field[6]["fieldstring"] = "pay_note";
        $pay_field[6]["fieldname"] = $this->LgStringSwitch("支付备注");
        $pay_field[6]["show"] = 1;
        $pay_field[6]["custom"] = 0;

        $data = array();
        $data['orderOne'] = $orderOne;

        $data['list']['field'] = $list_field;
        if ($list) {
            $data['list']['list'] = $list;
        } else {
            $data['list']['list'] = array();
            $data['list']['errortip'] = "暂无项目信息";
        }

        $data['info'] = $info;

        $data['logList']['field'] = $log_field;
        $data['logList']['logList'] = $logList;
        if ($logList) {
            $data['logList']['logList'] = $logList;
        } else {
            $data['logList']['logList'] = array();
            $data['logList']['errortip'] = "暂无订单跟踪信息";
        }


        $data['payList']['field'] = $pay_field;
        $data['payList']['payList'] = $payList;
        if ($payList) {
            $data['payList']['payList'] = $payList;
        } else {
            $data['payList']['payList'] = array();
            $data['payList']['errortip'] = "暂无订单支付信息";
        }


        return $data;
    }


    function checkConsume()
    {
        if ($this->payfeeorderOne['order_status'] == '-1') {
            $this->error = true;
            $this->errortip = "订单已取消,不可再次取消";
            return false;
        }

        if ($this->payfeeorderOne['order_status'] == '0') {
            $this->error = true;
            $this->errortip = "订单审核中,不可取消";
            return false;
        }

        $sql = "select oi.item_id
              from smc_payfee_order_item as oi
              left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$this->company_id}'
              where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.feeitem_expendtype='2'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "订单存在一次性消耗,不可取消";
            return false;
        }

        if ($this->payfeeorderOne['order_type'] == '2') {

            $pay = $this->DataControl->selectOne("select pay_price from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess='1' and pay_isrefund='0'");
            if ($pay) {
                $this->error = true;
                $this->errortip = "充值订单,不可取消";
                return false;
            }
        } else {
            $orderCourse = $this->DataControl->getList("smc_payfee_order_course", "order_pid='{$this->payfeeorderOne['order_pid']}'");
            if ($orderCourse) {
                foreach ($orderCourse as $one) {
                    if ($this->payfeeorderOne['order_status'] != '0') {
                        $sql = "select scb.coursebalance_id from smc_student_coursebalance as scb where scb.company_id='{$this->company_id}' and scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.school_id='{$this->school_id}' and scb.course_id='{$one['course_id']}' and (scb.coursebalance_time<'{$one['ordercourse_buynums']}' or scb.coursebalance_figure<'{$one['ordercourse_totalprice']}')";

                        if ($this->DataControl->selectOne($sql)) {
                            $this->error = true;
                            $this->errortip = "订单已有消耗,不可取消";
                            return false;
                        }
                    }
                    if ($this->DataControl->getFieldOne("smc_payfee_order_paylog", "paylog_id", "order_pid='{$this->payfeeorderOne['order_pid']}' and paylog_actualprice>0")) {
                        $sql = "select order_pid from smc_student_coursebalance_pricinglog where student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->payfeeorderOne['school_id']}' and course_id={$one['course_id']} and pricing_id='{$one['pricing_id']}' order by pricinglog_id desc limit 0,1";
                        $pricingOne = $this->DataControl->selectOne($sql);
                        if ($pricingOne) {
                            if ($pricingOne['order_pid'] != $this->payfeeorderOne['order_pid']) {
                                $this->error = true;
                                $this->errortip = "不可取消";
                                return false;
                            }
                        }
                    }

                    $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$one['course_id']}'");
                    if ($courseOne['course_inclasstype'] != '1') {
                        $sql = "select oc.ordercoupons_price,sc.coupons_type
                          from smc_payfee_order_coupons as oc
                          left join smc_student_coupons as sc on sc.coupons_pid=oc.coupons_pid
                          where oc.order_pid='{$this->payfeeorderOne['order_pid']}' and oc.course_id='{$one['course_id']}'
                          and sc.coupons_type='0' and sc.coupons_playclass='0'";

                        $couList = $this->DataControl->selectClear($sql);
                        $all_coupons_price = 0;
                        if ($couList) {
                            foreach ($couList as $value) {
                                $all_coupons_price += $value['ordercoupons_price'];
                            }
                        }

                        $stuForwardOne = $this->DataControl->getFieldOne("smc_student_courseforward", "courseforward_price", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$one['course_id']}'");
                        if ($stuForwardOne['courseforward_price'] < $all_coupons_price) {
                            $this->error = true;
                            $this->errortip = "结转金额已有消耗,不可取消";
                            return false;
                        }
                    }
                }
            }


            $sql = "select oi.*,p.course_id from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}'
                  ";

            $itemsCourse = $this->DataControl->selectClear($sql);

            if ($itemsCourse) {
                foreach ($itemsCourse as $one) {
                    $sql = "select scb.itemtimes_id from smc_student_itemtimes as scb
                      left join smc_code_feeitem as cf on cf.feeitem_id=scb.feeitem_id
                      where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.course_id='{$one['course_id']}' and cf.feeitem_branch='{$one['feeitem_branch']}' and (scb.itemtimes_figure<'{$one['item_totalprice']}' or scb.itemtimes_number<'{$one['item_buynums']}')";

                    if ($this->DataControl->selectOne($sql)) {
                        $this->error = true;
                        $this->errortip = "杂费已有消耗,不可取消";
                        return false;
                    }
                }
            }

            $sql = "select erpgoods_id from smc_student_erpgoods where order_pid='{$this->payfeeorderOne['order_pid']}' and student_id='{$this->payfeeorderOne['student_id']}' and erpgoods_isreceive='1'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "已有领用,不可取消";
                return false;
            }
        }

        return true;
    }

    function cancelOrder($request)
    {
        $this->payfeeorderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid = '{$request['order_pid']}'");

        if (!$request['create_time']) {
            $request['create_time'] = date("Y-m-d H:i:s");
        }

        if ($this->payfeeorderOne['order_status'] == '-1') {
            $this->error = true;
            $this->errortip = "订单已取消,不可再次取消";
            return false;
        }

        if ($this->payfeeorderOne['order_status'] == '0') {
            $this->error = true;
            $this->errortip = "订单审核中,不可取消";
            return false;
        }

        if ($this->payfeeorderOne['school_id'] != $request['school_id']) {
            $this->error = true;
            $this->errortip = "非本校订单,不可取消";
            return false;
        }

//        $sql = "select oi.item_id
//              from smc_payfee_order_item as oi
//              left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$this->company_id}'
//              where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.feeitem_expendtype='2'";
//        if ($this->DataControl->selectOne($sql)) {
//            $this->error = true;
//            $this->errortip = "订单存在一次性消耗,不可取消";
//            return false;
//        }

        $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $this->payfeeorderOne['order_pid']);

        if ($this->payfeeorderOne['order_type'] == '2') {

            $pay = $this->DataControl->selectOne("select pay_price from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess='1' and pay_isrefund='0'");
            if ($pay) {
                $this->error = true;
                $this->errortip = "账户已充值,不可取消";
                return false;
            }

//            $OrderHandleModel->orderProcess(strtotime($request['create_time']));

            $this->verdictpayFeeOrder($this->payfeeorderOne['order_pid']);

            if ($this->payfeeorderOne['order_status'] == '-1') {
                $this->error = true;
                $this->errortip = "订单已取消,不可再次取消";
                return false;
            }

            $data = array();
            $data['order_status'] = '-1';
            $data['order_updatatime'] = strtotime($request['create_time']);

            if ($this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'", $data)) {

                $OrderHandleModel->orderProcess(strtotime($request['create_time']));

                $this->orderTracks('取消订单', '取消订单，订单关闭', '', strtotime($request['create_time']));

                $track_data = array();
                $track_data['trading_status'] = '-1';
                $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}' and company_id='{$this->company_id}'", $track_data);

                return true;
            } else {
                $this->error = true;
                $this->errortip = "取消失败";
                return false;
            }

        } else {

            if($this->payfeeorderOne['mergeorder_pid']!=''){
                if($this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepay","mergepay_id","mergeorder_pid='{$this->payfeeorderOne['mergeorder_pid']}' and mergepay_issuccess=1")){
                    $this->error = true;
                    $this->errortip = "已有组合支付,不可取消";
                    return false;
                }


                if($mergePayOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepay","mergepay_id,mergeorder_pid,mergepay_pid","mergeorder_pid='{$this->payfeeorderOne['mergeorder_pid']}' and mergepay_issuccess=1")){

                    $cancelarray = array();
                    $cancelarray['paypid'] = $mergePayOne['mergepay_pid'];
                    request_by_curl("https://scshopapi.kedingdang.com/MergeBoingPay/OrderPayCancel", dataEncode($cancelarray), "GET");
                }

            }

            $orderCourse = $this->DataControl->getList("smc_payfee_order_course", "order_pid='{$this->payfeeorderOne['order_pid']}'");
            if ($orderCourse) {
                foreach ($orderCourse as $one) {
                    if ($this->payfeeorderOne['order_status'] != '0') {
                        $sql = "select scb.coursebalance_id from smc_student_coursebalance as scb where scb.company_id='{$this->company_id}' and scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.school_id='{$this->school_id}' and scb.course_id='{$one['course_id']}' and (scb.coursebalance_time<'{$one['ordercourse_buynums']}' or scb.coursebalance_figure<'{$one['ordercourse_totalprice']}')";

                        if ($this->DataControl->selectOne($sql)) {
                            $this->error = true;
                            $this->errortip = "订单已有消耗,不可取消";
                            return false;
                        }
                    }
                    if ($this->DataControl->getFieldOne("smc_payfee_order_paylog", "paylog_id", "order_pid='{$this->payfeeorderOne['order_pid']}' and paylog_actualprice>0")) {
                        $sql = "select order_pid from smc_student_coursebalance_pricinglog where student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->payfeeorderOne['school_id']}' and course_id={$one['course_id']} and pricing_id='{$one['pricing_id']}' order by pricinglog_id desc limit 0,1";
                        $pricingOne = $this->DataControl->selectOne($sql);
                        if ($pricingOne) {
                            if ($pricingOne['order_pid'] != $this->payfeeorderOne['order_pid']) {
                                $this->error = true;
                                $this->errortip = "不可取消";
                                return false;
                            }
                        }
                    }

                    $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$one['course_id']}'");
                    if ($courseOne['course_inclasstype'] != '1') {
                        $sql = "select oc.ordercoupons_price,sc.coupons_type
                          from smc_payfee_order_coupons as oc
                          left join smc_student_coupons as sc on sc.coupons_pid=oc.coupons_pid
                          where oc.order_pid='{$this->payfeeorderOne['order_pid']}' and oc.course_id='{$one['course_id']}'
                          and sc.coupons_type='0' and sc.coupons_playclass='0'";

                        $couList = $this->DataControl->selectClear($sql);
                        $all_coupons_price = 0;
                        if ($couList) {
                            foreach ($couList as $value) {
                                $all_coupons_price += $value['ordercoupons_price'];
                            }
                        }

                        $stuForwardOne = $this->DataControl->getFieldOne("smc_student_courseforward", "courseforward_price", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$one['course_id']}'");
                        if ($stuForwardOne['courseforward_price'] < $all_coupons_price) {
                            $this->error = true;
                            $this->errortip = "结转金额已有消耗,不可取消";
                            return false;
                        }
                    } else {
                        $monthList = $this->DataControl->getList("smc_student_courseshare", "order_pid='{$this->payfeeorderOne['order_pid']}'");
                        if ($monthList) {
                            foreach ($monthList as $monthOne) {
                                $sql = "select sh.hourstudy_id
                                      from smc_student_hourstudy as sh
                                      left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                                      where sh.student_id='{$this->payfeeorderOne['student_id']}' and sh.class_id='{$monthOne['class_id']}' and substring(ch.hour_day,1,7)='{$monthOne['courseshare_month']}'";
                                if ($this->DataControl->selectOne($sql)) {
                                    $this->error = true;
                                    $this->errortip = "学员本月已有考勤,不可取消";
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            $sql = "select pay_id from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_type=2 and pay_issuccess=1 and pay_price>0 limit 0,1";
            $payItemOne = $this->DataControl->selectOne($sql);
            if ($payItemOne) {
                $sql = "select oi.*,p.course_id from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}'
                  ";
            } else {
                $sql = "select oi.*,p.course_id
                  from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$this->payfeeorderOne['company_id']}'
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.feeitem_expendtype<>'2'
                  ";
            }

            $itemsCourse = $this->DataControl->selectClear($sql);

            if ($itemsCourse) {
                foreach ($itemsCourse as $one) {
                    $sql = "select scb.itemtimes_id from smc_student_itemtimes as scb
                      left join smc_code_feeitem as cf on cf.feeitem_id=scb.feeitem_id
                      where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.course_id='{$one['course_id']}' and cf.feeitem_branch='{$one['feeitem_branch']}'
                      and (scb.itemtimes_figure<'{$one['item_totalprice']}' or scb.itemtimes_number<'{$one['item_buynums']}')";

                    if ($this->DataControl->selectOne($sql)) {
                        $this->error = true;
                        $this->errortip = "杂费已有消耗,不可取消";
                        return false;
                    }
                }
            }


            $sql = "select pop.pay_id from smc_payfee_order_pay as pop
                  left join smc_code_paytype as cp on cp.paytype_code=pop.paytype_code
                  where cp.paytype_ischarge=1 and  pay_issuccess ='1' and pop.order_pid='{$this->payfeeorderOne['order_pid']}'";

            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "订单已有收费,不可取消";
                return false;
            }


            $sql = "select erpgoods_id from smc_student_erpgoods where order_pid='{$this->payfeeorderOne['order_pid']}' and student_id='{$this->payfeeorderOne['student_id']}' and erpgoods_isreceive='1'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "已有领用,不可取消";
                return false;
            }

//            $OrderHandleModel->orderProcess(strtotime($request['create_time']));

            $this->verdictpayFeeOrder($this->payfeeorderOne['order_pid']);

            if ($this->payfeeorderOne['order_status'] == '-1') {
                $this->error = true;
                $this->errortip = "订单已取消,不可再次取消";
                return false;
            }

            $data = array();
            $data['order_status'] = -1;
            $data['order_updatatime'] = strtotime(date("Y-m-d", strtotime($request['create_time']))) + ((time() + 8 * 3600) % 86400);

            if ($this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'", $data)) {

                $OrderHandleModel->orderProcess(strtotime($request['create_time']));

                $this->orderTracks($this->LgStringSwitch('取消订单'), $this->LgStringSwitch('取消订单，订单关闭'), '', strtotime($request['create_time']));
                return true;
            } else {
                $this->error = true;
                $this->errortip = "取消失败";
                return false;
            }
        }
    }

    function cancelMergeOrder($request){

        $mergeOrderOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder","mergeorder_id,mergeorder_status","mergeorder_pid='{$request['mergeorder_pid']}'");
        if($mergeOrderOne['mergeorder_status']!=0){
            $this->error = true;
            $this->errortip = "该订单不可取消";
            return false;
        }

        if($this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepaylog","mergepaylog_id","mergeorder_pid='{$request['mergeorder_pid']}'")){
            $this->error = true;
            $this->errortip = "该订单已支付,不可取消";
            return false;
        }

        $sql = "select a.*
                from smc_payfee_order as a
                where a.mergeorder_pid='{$request['mergeorder_pid']}'
                group by a.order_pid
                ";

        $orderList=$this->DataControl->selectClear($sql);

        if($orderList){
            foreach($orderList as $orderOne){

                $bool=$this->checkCanCancelOrder($orderOne);

                if(!$bool){
                    $this->error = true;
                    return false;
                }
            }

            foreach($orderList as $orderOne) {
                $this->cancelOrder($orderOne);
            }
        }

        $data=array();
        $data['mergeorder_status']=-1;
        $data['mergeorder_createtime']=time();

        if($this->DataControl->updateData("smc_payfee_mergeorder","mergeorder_pid='{$request['mergeorder_pid']}'",$data)){

            $payOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepay","mergepay_pid","mergeorder_pid='{$request['mergeorder_pid']}' and mergepay_issuccess=0");

            if($payOne){
                $data=array();
                $data['mergepay_issuccess']=-1;
                $data['mergepay_tradeState']='D';
                $data['mergepay_updatatime']=time();
                $this->DataControl->updateData("smc_payfee_mergeorder_mergepay","mergeorder_pid='{$request['mergeorder_pid']}' and mergepay_issuccess=0",$data);

                $param=array();
                $param['paypid']=$payOne['mergepay_pid'];
                request_by_curl("https://scshopapi.kedingdang.com/MergeBoingPay/OrderPayCancel", dataEncode($param),"GET");

                return true;

            }else{
                return true;
            }

        }else{
            $this->error = true;
            $this->errortip = "取消失败";
            return false;
        }





    }

    function checkCanCancelOrder($request)
    {
        $this->payfeeorderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid = '{$request['order_pid']}'");

        if ($this->payfeeorderOne['order_status'] == '-1') {
            $this->error = true;
            $this->errortip = "订单已取消,不可再次取消";
            return false;
        }

        if ($this->payfeeorderOne['order_status'] == '0') {
            $this->error = true;
            $this->errortip = "订单审核中,不可取消";
            return false;
        }

        if ($this->payfeeorderOne['school_id'] != $request['school_id']) {
            $this->error = true;
            $this->errortip = "非本校订单,不可取消";
            return false;
        }

        $orderCourse = $this->DataControl->getList("smc_payfee_order_course", "order_pid='{$this->payfeeorderOne['order_pid']}'");
        if ($orderCourse) {
            foreach ($orderCourse as $one) {
                if ($this->payfeeorderOne['order_status'] != '0') {
                    $sql = "select scb.coursebalance_id from smc_student_coursebalance as scb where scb.company_id='{$this->company_id}' and scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.school_id='{$this->school_id}' and scb.course_id='{$one['course_id']}' and (scb.coursebalance_time<'{$one['ordercourse_buynums']}' or scb.coursebalance_figure<'{$one['ordercourse_totalprice']}')";

                    if ($this->DataControl->selectOne($sql)) {
                        $this->error = true;
                        $this->errortip = "订单已有消耗,不可取消";
                        return false;
                    }
                }
                if ($this->DataControl->getFieldOne("smc_payfee_order_paylog", "paylog_id", "order_pid='{$this->payfeeorderOne['order_pid']}' and paylog_actualprice>0")) {
                    $sql = "select order_pid from smc_student_coursebalance_pricinglog where student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->payfeeorderOne['school_id']}' and course_id={$one['course_id']} and pricing_id='{$one['pricing_id']}' order by pricinglog_id desc limit 0,1";
                    $pricingOne = $this->DataControl->selectOne($sql);
                    if ($pricingOne) {
                        if ($pricingOne['order_pid'] != $this->payfeeorderOne['order_pid']) {
                            $this->error = true;
                            $this->errortip = "不可取消";
                            return false;
                        }
                    }
                }

                $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$one['course_id']}'");
                if ($courseOne['course_inclasstype'] != '1') {
                    $sql = "select oc.ordercoupons_price,sc.coupons_type
                          from smc_payfee_order_coupons as oc
                          left join smc_student_coupons as sc on sc.coupons_pid=oc.coupons_pid
                          where oc.order_pid='{$this->payfeeorderOne['order_pid']}' and oc.course_id='{$one['course_id']}'
                          and sc.coupons_type='0' and sc.coupons_playclass='0'";

                    $couList = $this->DataControl->selectClear($sql);
                    $all_coupons_price = 0;
                    if ($couList) {
                        foreach ($couList as $value) {
                            $all_coupons_price += $value['ordercoupons_price'];
                        }
                    }

                    $stuForwardOne = $this->DataControl->getFieldOne("smc_student_courseforward", "courseforward_price", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$one['course_id']}'");
                    if ($stuForwardOne['courseforward_price'] < $all_coupons_price) {
                        $this->error = true;
                        $this->errortip = "结转金额已有消耗,不可取消";
                        return false;
                    }
                } else {
                    $monthList = $this->DataControl->getList("smc_student_courseshare", "order_pid='{$this->payfeeorderOne['order_pid']}'");
                    if ($monthList) {
                        foreach ($monthList as $monthOne) {
                            $sql = "select sh.hourstudy_id
                                      from smc_student_hourstudy as sh
                                      left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                                      where sh.student_id='{$this->payfeeorderOne['student_id']}' and sh.class_id='{$monthOne['class_id']}' and substring(ch.hour_day,1,7)='{$monthOne['courseshare_month']}'";
                            if ($this->DataControl->selectOne($sql)) {
                                $this->error = true;
                                $this->errortip = "学员本月已有考勤,不可取消";
                                return false;
                            }
                        }
                    }
                }
            }
        }

        $sql = "select pay_id from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_type=2 and pay_issuccess=1 and pay_price>0 limit 0,1";
        $payItemOne = $this->DataControl->selectOne($sql);
        if ($payItemOne) {
            $sql = "select oi.*,p.course_id from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}'
                  ";
        } else {
            $sql = "select oi.*,p.course_id
                  from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$this->payfeeorderOne['company_id']}'
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.feeitem_expendtype<>'2'
                  ";
        }

        $itemsCourse = $this->DataControl->selectClear($sql);

        if ($itemsCourse) {
            foreach ($itemsCourse as $one) {
                $sql = "select scb.itemtimes_id from smc_student_itemtimes as scb
                      left join smc_code_feeitem as cf on cf.feeitem_id=scb.feeitem_id
                      where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.course_id='{$one['course_id']}' and cf.feeitem_branch='{$one['feeitem_branch']}'
                      and (scb.itemtimes_figure<'{$one['item_totalprice']}' or scb.itemtimes_number<'{$one['item_buynums']}')";

                if ($this->DataControl->selectOne($sql)) {
                    $this->error = true;
                    $this->errortip = "杂费已有消耗,不可取消";
                    return false;
                }
            }
        }


        $sql = "select pop.pay_id from smc_payfee_order_pay as pop
                  left join smc_code_paytype as cp on cp.paytype_code=pop.paytype_code
                  where cp.paytype_ischarge=1 and  pay_issuccess ='1' and pop.order_pid='{$this->payfeeorderOne['order_pid']}'";

        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "订单已有收费,不可取消";
            return false;
        }


        $sql = "select erpgoods_id from smc_student_erpgoods where order_pid='{$this->payfeeorderOne['order_pid']}' and student_id='{$this->payfeeorderOne['student_id']}' and erpgoods_isreceive='1'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "已有领用,不可取消";
            return false;
        }


        $this->verdictpayFeeOrder($this->payfeeorderOne['order_pid']);

        if ($this->payfeeorderOne['order_status'] == '-1') {
            $this->error = true;
            $this->errortip = "订单已取消,不可再次取消";
            return false;
        }

        return true;
    }


    function schoolVerify($request)
    {
        if ($request['create_time'] != '') {
            $time = strtotime($request['create_time']);
        } else {
            $time = time();
        }

        $sql = "select ro.* from smc_refund_order as ro where ro.refund_pid='{$request['refund_pid']}'";
        $refundOne = $this->DataControl->selectOne($sql);
        if ($refundOne['refund_status'] != '0' || $refundOne['refund_class'] != '0') {
            $this->error = true;
            $this->errortip = "审核对象不符!";
            return false;
        }
        $data = array();
        $data['refund_status'] = '1';
        $data['refund_name'] = $request['refund_name'];
        $data['refund_mobile'] = $request['refund_mobile'];
        $data['refund_bank'] = $request['refund_bank'];
        $data['refund_accountname'] = $request['refund_accountname'];
        $data['refund_bankcard'] = $request['refund_bankcard'];
//        $data['refund_reason'] = $request['refund_reason'];
        $data['refund_updatatime'] = time();
        if ($this->DataControl->updateData("smc_refund_order", "refund_pid='{$request['refund_pid']}'", $data)) {
            $TracksData = array();
            $TracksData['refund_pid'] = $request['refund_pid'];
            $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
            $TracksData['tracks_information'] = $this->LgStringSwitch('校长审核通过，等待财务审核');
            $TracksData['tracks_note'] = $request['refund_reason'];
            $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
            $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $TracksData['tracks_time'] = time();
            $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);
            return true;
        } else {
            $this->error = true;
            $this->errortip = "审核失败!";
            return false;
        }
    }

    function refuseSchoolVerify($request)
    {
        $refundOne = $this->DataControl->getFieldOne("smc_refund_order", "refund_status", "refund_pid='{$request['refund_pid']}' and company_id='{$this->company_id}'");
        if ($refundOne['refund_status'] != '0') {
            $this->error = true;
            $this->errortip = "该状态不可以拒绝!";
            return false;
        }

        $RefundModel = new \Model\Smc\RefundModel($request, $request['refund_pid']);
        $RefundModel->refuseSchoolVerify($request);
        return true;
    }

    function getContractList($request)
    {
        $courseList = $this->DataControl->selectClear("select course_id from smc_payfee_order_course where order_pid='{$this->payfeeorderOne['order_pid']}'");
        $number = $this->payfeeorderOne['order_paymentprice'];
        $total = count($courseList) * 2;
        $divide_number = bcdiv($number, $total, 2);
        $last_number = bcsub($number, $divide_number * ($total - 1), 2);
        $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
        $numArray = explode("+", $number_str);

    }

    //下载收据
    function downReceiptApi($request)
    {
        //下载收据
        $receiptOne = $this->DataControl->selectOne("select p.pay_price,p.pay_successtime,p.pay_typename,p.pay_type,s.student_cnname
        ,s.student_enname,s.student_branch,h.school_cnname,h.school_enname,h.school_branch,h.school_shortname,p.paytype_code,st.staffer_cnname,
        '--' as class_name,h.school_signet as signet
                from smc_payfee_order_pay as p 
                LEFT JOIN smc_payfee_order as o ON p.order_pid = o.order_pid 
                LEFT JOIN smc_student as s ON o.student_id = s.student_id 
                LEFT JOIN smc_school as h ON o.school_id = h.school_id
                left join smc_staffer as st on st.staffer_id=o.staffer_id
                WHERE p.pay_pid='{$request['pay_pid']}' ");
        //(SELECT group_concat(DISTINCT(r.coursecat_cnname)) FROM smc_payfee_order_course as c LEFT JOIN smc_course as u ON c.course_id = u.course_id LEFT JOIN smc_code_coursecat as r ON u.coursecat_id = r.coursecat_id WHERE c.order_pid = p.order_pid )

        $paytypeOne = $this->DataControl->getFieldOne("smc_code_paytype", "paytype_ischarge", "paytype_code='{$receiptOne['paytype_code']}'");

        if ($paytypeOne['paytype_ischarge'] != '1') {
            $this->error = true;
            $this->errortip = "该支付不可下载收据!";
            return false;
        }

        $receiptOne['pay_successtime'] = $receiptOne['pay_successtime'] > 0 ? date("Y-m-d", $receiptOne['pay_successtime']) : '';

        $paytype = $this->LgArraySwitch(array("课程收费", "教材收费", "杂费收费", "账户充值"));
        $receiptOne['payremarks'] = $paytype[$receiptOne['pay_type']] . ' ' . $receiptOne['pay_typename'];

        //数字转大写
        list($qian, $hou) = explode(".", $receiptOne['pay_price'], 2);
        $yuan = str_split($qian);
        $jiao = str_split($hou);
        krsort($yuan);
        krsort($jiao);
        $cnynums = $this->LgArraySwitch(array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"));
        $pricearr = array_merge($jiao, $yuan);
        $pricearray = array();
        if ($pricearr) {
            foreach ($pricearr as &$pricearrvar) {
                $pricearray[] = $cnynums[$pricearrvar];
            }

            //补充七位数据
            $pricenum = count($pricearr);
            $cha = 7 - $pricenum;
            if ($cha > 0) {
                for ($x = 0; $x < $cha; $x++) {
                    $pricearray[] = $this->LgStringSwitch('零');
                }
            }
            krsort($pricearray);
        }
        $receiptOne['pricearray'] = $pricearray;

        $sql = "select p.receipt_id,p.receipt_pid,st.staffer_cnname
              from smc_payfee_order_payreceipt as p
              left join smc_staffer as st on st.staffer_id=p.staffer_id
              where pay_pid='{$request['pay_pid']}' limit 0,1";
        $receipt = $this->DataControl->selectOne($sql);

        if (!$receipt) {
            do {
                $receipt_pid = $this->createReceiptPid('SJ');
            } while ($this->DataControl->selectOne("select receipt_id from smc_payfee_order_payreceipt where receipt_pid='{$receipt_pid}' limit 0,1"));

            $receiptarray = array();
            $receiptarray['receipt_type'] = 0;//支付单
            $receiptarray['staffer_id'] = $this->stafferOne['staffer_id'];//收据操作人
            $receiptarray['receipt_pid'] = $receipt_pid;//收据编号
            $receiptarray['order_pid'] = $this->payfeeorderOne['order_pid'];//订单编号
            $receiptarray['pay_pid'] = $request['pay_pid'];//对应的支付编号
            $receiptarray['receipt_info'] = json_encode($receiptOne);
            $receiptarray['receipt_addtime'] = time();//收据时间
            $receipt_id = $this->DataControl->insertData("smc_payfee_order_payreceipt", $receiptarray);

            $receiptOne['receipt_id'] = $receipt_id;
            $receiptOne['receipt_pid'] = $receipt_pid;
        } else {
            $receiptOne['receipt_id'] = $receipt['receipt_id'];
            $receiptOne['receipt_pid'] = $receipt['receipt_pid'];
        }

        $this->orderTracks($this->LgStringSwitch('下载收据'), $this->LgStringSwitch('下载收据，支付编号' . $request['pay_pid']));

        return $receiptOne;
    }

    //下载收据 --- 针对合同
    function downContractReceiptApi($request)
    {
        $protocolprice = $this->DataControl->selectOne(" SELECT sum(p.protocol_price) as protocol_pricenum from smc_student_protocol AS p
 WHERE p.company_id = '{$request['company_id']}' and p.order_pid = '{$request['order_pid']}' and p.protocol_receiptnum > 0 limit 0,1 ");
        $protocolOne = $this->DataControl->selectOne(" SELECT p.protocol_price as pay_price ,p.order_pid,s.student_cnname,s.student_enname,s.student_branch,h.school_cnname,h.school_enname,h.school_branch,st.staffer_cnname,h.school_shortname, 
    (SELECT sum(op.pay_price) as pay_pricenum from smc_payfee_order_pay as op LEFT JOIN smc_code_paytype as cp ON cp.paytype_code=op.paytype_code WHERE op.pay_issuccess = '1' and op.order_pid = p.order_pid and cp.paytype_ischarge = '1') as pay_pricenum,
    '--' as class_name,h.school_signet as signet
                    from smc_student_protocol AS p  
                    LEFT JOIN smc_payfee_order as o ON p.order_pid = o.order_pid 
                    LEFT JOIN smc_student as s ON o.student_id = s.student_id 
                    LEFT JOIN smc_school as h ON o.school_id = h.school_id
                    left join smc_staffer as st on st.staffer_id=o.staffer_id 
                    WHERE p.company_id = '{$request['company_id']}' 
                    and p.order_pid = '{$request['order_pid']}' 
                    and p.protocol_pid = '{$request['protocol_pid']}'
                    and p.protocol_id = '{$request['protocol_id']}' limit 0,1 ");
//        (SELECT group_concat(DISTINCT(r.coursecat_cnname))
//    FROM smc_payfee_order_course as c
//    LEFT JOIN smc_course as u ON c.course_id = u.course_id
//    LEFT JOIN smc_code_coursecat as r ON u.coursecat_id = r.coursecat_id
//    WHERE c.order_pid = p.order_pid )

        /*if(($protocolprice['protocol_pricenum'] + $protocolOne['protocol_price']) > $protocolOne['pay_pricenum']){
            $this->error = true;
            $this->errortip = "收据金额已超出支付金额，不可下载!";
            return false;
        }*/

        $firstPayOne = $this->DataControl->selectOne("select op.pay_successtime,op.pay_type,op.pay_typename,cp.paytype_name from smc_payfee_order_pay as op 
                        LEFT JOIN smc_code_paytype as cp ON cp.paytype_code=op.paytype_code 
                        WHERE op.pay_issuccess = '1' and op.order_pid = '{$protocolOne['order_pid']}' 
                        ORDER BY op.pay_id ASC limit 0,1");
        $paytype = $this->LgArraySwitch(array("课程收费", "教材收费", "杂费收费", "账户充值"));
        $protocolOne['payremarks'] = $paytype[$firstPayOne['pay_type']] . ' ' . $firstPayOne['pay_typename'];
        $protocolOne['pay_successtime'] = date("Y-m-d", $firstPayOne['pay_successtime']);

        //数字转大写
        list($qian, $hou) = explode(".", $protocolOne['pay_price'], 2);
        $yuan = str_split($qian);
        $jiao = str_split($hou);
        krsort($yuan);
        krsort($jiao);
        $cnynums = $this->LgArraySwitch(array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"));
        $pricearr = array_merge($jiao, $yuan);
        $pricearray = array();
        if ($pricearr) {
            foreach ($pricearr as &$pricearrvar) {
                $pricearray[] = $cnynums[$pricearrvar];
            }

            //补充七位数据
            $pricenum = count($pricearr);
            $cha = 7 - $pricenum;
            if ($cha > 0) {
                for ($x = 0; $x < $cha; $x++) {
                    $pricearray[] = $this->LgStringSwitch('零');
                }
            }
            krsort($pricearray);
        }
        $protocolOne['pricearray'] = $pricearray;

        $sql = "select p.receipt_id,p.receipt_pid,st.staffer_cnname 
              from smc_payfee_order_payreceipt as p
              left join smc_staffer as st on st.staffer_id=p.staffer_id
              where protocol_pid='{$request['protocol_pid']}' limit 0,1";
        $receipt = $this->DataControl->selectOne($sql);

        if (!$receipt) {
            do {
                $receipt_pid = $this->createReceiptPid('SJ');
            } while ($this->DataControl->selectOne("select receipt_id from smc_payfee_order_payreceipt where receipt_pid='{$receipt_pid}' limit 0,1"));

            $receiptarray = array();
            $receiptarray['receipt_type'] = 1;//合同
            $receiptarray['staffer_id'] = $this->stafferOne['staffer_id'];//收据操作人
            $receiptarray['receipt_pid'] = $receipt_pid;//收据编号
            $receiptarray['order_pid'] = $this->payfeeorderOne['order_pid'];//订单编号
            $receiptarray['protocol_pid'] = $request['protocol_pid'];//对应的支付编号
            $receiptarray['receipt_info'] = json_encode($protocolOne);
            $receiptarray['receipt_addtime'] = time();//收据时间
            $receipt_id = $this->DataControl->insertData("smc_payfee_order_payreceipt", $receiptarray);

            //记录收据下载次数
            $this->DataControl->query("update smc_student_protocol set protocol_receiptnum=(protocol_receiptnum+1) where protocol_pid='{$request['protocol_pid']}' and protocol_id = '{$request['protocol_id']}' and order_pid='{$request['order_pid']}' ");

            $protocolOne['receipt_id'] = $receipt_id;
            $protocolOne['receipt_pid'] = $receipt_pid;
        } else {
            $protocolOne['receipt_id'] = $receipt['receipt_id'];
            $protocolOne['receipt_pid'] = $receipt['receipt_pid'];
        }
//        单独的跟踪记录接口来进行记录，这里屏蔽掉
//        $this->orderTracks($this->LgStringSwitch('下载收据'),$this->LgStringSwitch('下载收据，合同编号'.$request['protocol_pid']));

        return $protocolOne;
    }

    /**
     * @param $trading_pid //来自smc_student_trading
     * @return $array
     *   缴费订单 结转订单  退费订单 减少课次订单
     */
    function getOrderOneByTrading($trading_pid)
    {
        $order_tradingOne = $this->DataControl->selectOne(
            "select st.*,ct.tradingtype_name,s.student_cnname,s.student_enname,s.student_branch,st.tradingtype_code,ct.tradingtype_name,
            sch.school_cnname,sch.school_branch,sb.student_balance,sb.student_withholdbalance,sch.school_isprotocol,co.company_protocolupdate,ca.coursecat_istreaty
            from smc_student_trading as st
            left join smc_code_tradingtype as ct ON ct.tradingtype_code = st.tradingtype_code
            left join smc_student as s ON s.student_id = st.student_id
            left join smc_school as sch on sch.school_id=st.school_id
            left join smc_student_balance as sb on sb.student_id=st.student_id and sb.school_id=st.school_id
            left join gmc_company as co on co.company_id = sch.company_id 
            left join smc_payfee_order as o on st.trading_pid = o.trading_pid
	        left join smc_payfee_order_course as oc on oc.order_pid = o.order_pid
	        left join smc_course as r on r.course_id = oc.course_id
	        left join smc_code_coursecat as ca on r.coursecat_id = ca.coursecat_id
            where st.trading_pid='{$trading_pid}' and st.company_id='{$this->company_id}'");

        $studentOne = array();
        $studentOne['student_id'] = $order_tradingOne['student_id'];
        $studentOne['student_cnname'] = $order_tradingOne['student_cnname'];
        $studentOne['student_enname'] = $order_tradingOne['student_enname'] == "" ? '--' : $order_tradingOne['student_enname'];
        $studentOne['student_branch'] = $order_tradingOne['student_branch'];
        $studentOne['school_isprotocol'] = $order_tradingOne['school_isprotocol'];
        $studentOne['coursecat_istreaty'] = $order_tradingOne['coursecat_istreaty'];
        $studentOne['tradingtype_code'] = $order_tradingOne['tradingtype_code'];
        $studentOne['tradingtype_name'] = $order_tradingOne['tradingtype_name'];
        $studentOne['company_protocolupdate'] = $order_tradingOne['company_protocolupdate'];
        $studentOne['school_cnname'] = $order_tradingOne['school_cnname'];
        $studentOne['school_branch'] = $order_tradingOne['school_branch'];
        $studentOne['student_balance'] = $order_tradingOne['student_balance'];
        $studentOne['student_withholdbalance'] = $order_tradingOne['student_withholdbalance'];
        if ($order_tradingOne['school_id'] == $this->school_id) {
            $studentOne['can_do'] = 1;
        } else {
            $studentOne['can_do'] = 0;
        }


        $result = array();
        //$paycode = array('新生缴费','充值订单','老生续费','教材杂费','课程冲销订单','补齐学费','定金充值订单');
        $pay_code = array('PaynewFee', 'Recharge', 'PayrenewFee', 'PayitemFee', 'CourseCatWash', 'CourseMakeUp', 'DepositCharge');
        //$refund_code = array('账户退费');
        $refund_code = array('Accountrefund');
        //$deal_code = array('课程结转订单');
        $deal_code = array("CourseForward");
        //$reduce_code == array('扣课订单');
        $reduce_code = array("ReduceCourse");
        //$reduce_code == array('赠送课次订单');
        $hourFee_code = array("ClassGiving");
        //$schoolChange_code = arrary('学员余额转入','学员余额转出');
        $schoolChange_code = array('TransferIn', 'TransferOut');
        $dataList = array();
        if (in_array($order_tradingOne['tradingtype_code'], $pay_code)) {
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "order_pid", "trading_pid='{$order_tradingOne['trading_pid']}'");
            $this->verdictpayFeeOrder($orderOne['order_pid']);
            $dataList = $this->getPayOrderOne();
            $field = array();
            $field['course'][0]["fieldstring"] = "course_id";
            $field['course'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['course'][0]["show"] = 0;
            $field['course'][0]["custom"] = 1;

            $field['course'][1]["fieldstring"] = "item_name";
            $field['course'][1]["fieldname"] = $this->LgStringSwitch("项目名称");
            $field['course'][1]["show"] = 1;
            $field['course'][1]["custom"] = 1;

            $field['course'][2]["fieldstring"] = "item_branch";
            $field['course'][2]["fieldname"] = $this->LgStringSwitch("项目编号");
            $field['course'][2]["show"] = 1;
            $field['course'][2]["custom"] = 1;

            $field['course'][3]["fieldstring"] = "coursecat_cnname";
            $field['course'][3]["fieldname"] = $this->LgStringSwitch("所属班种");
            $field['course'][3]["show"] = 1;
            $field['course'][3]["custom"] = 1;

            $field['course'][4]["fieldstring"] = "original_price";
            $field['course'][4]["fieldname"] = $this->LgStringSwitch("原价");
            $field['course'][4]["show"] = 1;
            $field['course'][4]["custom"] = 1;

            $field['course'][5]["fieldstring"] = "selling_price";
            $field['course'][5]["fieldname"] = $this->LgStringSwitch("销售价");
            $field['course'][5]["show"] = 1;
            $field['course'][5]["custom"] = 1;

            $field['course'][6]["fieldstring"] = "ordercourse_buynums";
            $field['course'][6]["fieldname"] = $this->LgStringSwitch("购买课次");
            $field['course'][6]["show"] = 1;
            $field['course'][6]["custom"] = 1;

            $field['course'][7]["fieldstring"] = "pricing_name";
            $field['course'][7]["fieldname"] = $this->LgStringSwitch("价格批次");
            $field['course'][7]["show"] = 1;
            $field['course'][7]["custom"] = 1;

            $field['course'][8]["fieldstring"] = "item_unit";
            $field['course'][8]["fieldname"] = $this->LgStringSwitch("单位");
            $field['course'][8]["show"] = 1;
            $field['course'][8]["custom"] = 1;


            $field['items'][0]["fieldstring"] = "item_id";
            $field['items'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['items'][0]["show"] = 0;
            $field['items'][0]["custom"] = 1;

            $field['items'][1]["fieldstring"] = "item_name";
            $field['items'][1]["fieldname"] = $this->LgStringSwitch("项目名称");
            $field['items'][1]["show"] = 1;
            $field['items'][1]["custom"] = 1;

            $field['items'][2]["fieldstring"] = "item_branch";
            $field['items'][2]["fieldname"] = $this->LgStringSwitch("项目编号");
            $field['items'][2]["show"] = 1;
            $field['items'][2]["custom"] = 1;

            $field['items'][3]["fieldstring"] = "item_buynums";
            $field['items'][3]["fieldname"] = $this->LgStringSwitch("购买数量");
            $field['items'][3]["show"] = 1;
            $field['items'][3]["custom"] = 1;

            $field['items'][4]["fieldstring"] = "item_unitprice";
            $field['items'][4]["fieldname"] = $this->LgStringSwitch("单价");
            $field['items'][4]["show"] = 1;
            $field['items'][4]["custom"] = 1;

            $field['items'][5]["fieldstring"] = "item_unit";
            $field['items'][5]["fieldname"] = $this->LgStringSwitch("单位");
            $field['items'][5]["show"] = 1;
            $field['items'][5]["custom"] = 1;

            $field['items'][6]["fieldstring"] = "item_typename";
            $field['items'][6]["fieldname"] = $this->LgStringSwitch("类型");
            $field['items'][6]["show"] = 1;
            $field['items'][6]["custom"] = 1;


            $field['paylist'][0]["fieldstring"] = "pay_pid";
            $field['paylist'][0]["fieldname"] = $this->LgStringSwitch("支付请求号");
            $field['paylist'][0]["show"] = 1;
            $field['paylist'][0]["custom"] = 1;

            $field['paylist'][1]["fieldstring"] = "pay_typename";
            $field['paylist'][1]["fieldname"] = $this->LgStringSwitch("支付方式");
            $field['paylist'][1]["show"] = 1;
            $field['paylist'][1]["custom"] = 1;

            $field['paylist'][2]["fieldstring"] = "pay_price";
            $field['paylist'][2]["fieldname"] = $this->LgStringSwitch("实际支付金额");
            $field['paylist'][2]["show"] = 1;
            $field['paylist'][2]["custom"] = 1;

            $field['paylist'][3]["fieldstring"] = "paylog_tradeno";
            $field['paylist'][3]["fieldname"] = $this->LgStringSwitch("支付交易号");
            $field['paylist'][3]["show"] = 1;
            $field['paylist'][3]["custom"] = 1;

            $field['paylist'][4]["fieldstring"] = "pay_issuccess_name";
            $field['paylist'][4]["fieldname"] = $this->LgStringSwitch("支付状态");
            $field['paylist'][4]["show"] = 1;
            $field['paylist'][4]["custom"] = 1;

            $field['paylist'][5]["fieldstring"] = "paylog_ifee";
            $field['paylist'][5]["fieldname"] = $this->LgStringSwitch("手续费");
            $field['paylist'][5]["show"] = 1;
            $field['paylist'][5]["custom"] = 1;

            $field['paylist'][6]["fieldstring"] = "pay_note";
            $field['paylist'][6]["fieldname"] = $this->LgStringSwitch("支付备注");
            $field['paylist'][6]["show"] = 1;
            $field['paylist'][6]["custom"] = 1;

            $field['paylist'][7]["fieldstring"] = "pay_successtime";
            $field['paylist'][7]["fieldname"] = $this->LgStringSwitch("支付时间");
            $field['paylist'][7]["show"] = 1;
            $field['paylist'][7]["custom"] = 1;


            $field['contract'][0]["fieldstring"] = "order_pid";
            $field['contract'][0]["fieldname"] = $this->LgStringSwitch("合同编号");
            $field['contract'][0]["show"] = 1;
            $field['contract'][0]["custom"] = 1;

            $field['contract'][1]["fieldstring"] = "course_cnname";
            $field['contract'][1]["fieldname"] = $this->LgStringSwitch("项目名称");
            $field['contract'][1]["show"] = 1;
            $field['contract'][1]["custom"] = 1;

            $field['contract'][2]["fieldstring"] = "order_pid";
            $field['contract'][2]["fieldname"] = $this->LgStringSwitch("订单编号");
            $field['contract'][2]["show"] = 1;
            $field['contract'][2]["custom"] = 1;

            $field['contract'][3]["fieldstring"] = "order_createtime";
            $field['contract'][3]["fieldname"] = $this->LgStringSwitch("时间");
            $field['contract'][3]["show"] = 1;
            $field['contract'][3]["custom"] = 1;

            $field['trading'][0]["fieldstring"] = "tracks_id";
            $field['trading'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['trading'][0]["show"] = 0;
            $field['trading'][0]["custom"] = 0;

            $field['trading'][1]["fieldstring"] = "tracks_title";
            $field['trading'][1]["fieldname"] = $this->LgStringSwitch("订单主题");
            $field['trading'][1]["show"] = 1;
            $field['trading'][1]["custom"] = 1;

            $field['trading'][2]["fieldstring"] = "tracks_information";
            $field['trading'][2]["fieldname"] = $this->LgStringSwitch("跟踪信息");
            $field['trading'][2]["show"] = 1;
            $field['trading'][2]["custom"] = 1;

            $field['trading'][3]["fieldstring"] = "tracks_note";
            $field['trading'][3]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['trading'][3]["show"] = 1;
            $field['trading'][3]["custom"] = 1;

            $field['trading'][4]["fieldstring"] = "tracks_url";
            $field['trading'][4]["fieldname"] = $this->LgStringSwitch("附件");
            $field['trading'][4]["show"] = 1;
            $field['trading'][4]["custom"] = 1;
            $field['trading'][4]["isAnnex"] = 1;

            $field['trading'][5]["fieldstring"] = "tracks_playname";
            $field['trading'][5]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['trading'][5]["show"] = 1;
            $field['trading'][5]["custom"] = 1;

            $field['trading'][6]["fieldstring"] = "tracks_time";
            $field['trading'][6]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['trading'][6]["show"] = 1;
            $field['trading'][6]["custom"] = 1;

            $field['info'][0]["fieldstring"] = "school_cnname";
            $field['info'][0]["fieldname"] = $this->LgStringSwitch("经办分校");
            $field['info'][0]["show"] = 1;
            $field['info'][0]["custom"] = 1;

            $field['info'][1]["fieldstring"] = "staffer_cnname";
            $field['info'][1]["fieldname"] = $this->LgStringSwitch("经办人");
            $field['info'][1]["show"] = 1;
            $field['info'][1]["custom"] = 1;

            $field['info'][2]["fieldstring"] = "create_time";
            $field['info'][2]["fieldname"] = $this->LgStringSwitch("经办时间");
            $field['info'][2]["show"] = 1;
            $field['info'][2]["custom"] = 1;


        } elseif (in_array($order_tradingOne['tradingtype_code'], $refund_code)) {
            $dataList = $this->getRefundOrderOne($trading_pid);
            $field = array();
            $field['itemlist'] = $dataList['item_field'];
            $field['order'][0]["fieldstring"] = "trading_pid";
            $field['order'][0]["fieldname"] = $this->LgStringSwitch("交易编号");
            $field['order'][0]["show"] = 1;
            $field['order'][0]["custom"] = 1;

            $field['order'][1]["fieldstring"] = "order_pid";
            $field['order'][1]["fieldname"] = $this->LgStringSwitch("订单编号");
            $field['order'][1]["show"] = 1;
            $field['order'][1]["custom"] = 1;

            $field['order'][2]["fieldstring"] = "order_type";
            $field['order'][2]["fieldname"] = $this->LgStringSwitch("订单类型");
            $field['order'][2]["show"] = 1;
            $field['order'][2]["custom"] = 1;

            $field['order'][3]["fieldstring"] = "order_status";
            $field['order'][3]["fieldname"] = $this->LgStringSwitch("订单状态");
            $field['order'][3]["show"] = 1;
            $field['order'][3]["custom"] = 1;

            $field['order'][4]["fieldstring"] = "order_createtime";
            $field['order'][4]["fieldname"] = $this->LgStringSwitch("下单时间");
            $field['order'][4]["show"] = 1;
            $field['order'][4]["custom"] = 1;


            $field['reduce'][0]["fieldstring"] = "refund_bank";
            $field['reduce'][0]["fieldname"] = $this->LgStringSwitch("开户行");
            $field['reduce'][0]["show"] = 1;
            $field['reduce'][0]["custom"] = 1;

            $field['reduce'][1]["fieldstring"] = "refund_accountname";
            $field['reduce'][1]["fieldname"] = $this->LgStringSwitch("开户名");
            $field['reduce'][1]["show"] = 1;
            $field['reduce'][1]["custom"] = 1;

            $field['reduce'][2]["fieldstring"] = "refund_bankcard";
            $field['reduce'][2]["fieldname"] = $this->LgStringSwitch("银行卡号");
            $field['reduce'][2]["show"] = 1;
            $field['reduce'][2]["custom"] = 1;

            $field['reduce'][3]["fieldstring"] = "refund_payprice";
            $field['reduce'][3]["fieldname"] = $this->LgStringSwitch("应退总计");
            $field['reduce'][3]["show"] = 1;
            $field['reduce'][3]["custom"] = 1;

            $field['reduce'][4]["fieldstring"] = "refund_specialprice";
            $field['reduce'][4]["fieldname"] = $this->LgStringSwitch("意外退款");
            $field['reduce'][4]["show"] = 1;
            $field['reduce'][4]["custom"] = 1;

            $field['reduce'][5]["fieldstring"] = "是否结转余额清零";
            $field['reduce'][5]["fieldname"] = $this->LgStringSwitch("is_refud_forward");
            $field['reduce'][5]["show"] = 1;
            $field['reduce'][5]["custom"] = 1;

            $field['reduce'][6]["fieldstring"] = "实际退款金额";
            $field['reduce'][6]["fieldname"] = $this->LgStringSwitch("refund_payprice");
            $field['reduce'][6]["show"] = 1;
            $field['reduce'][6]["custom"] = 1;

            $field['trading'][0]["fieldstring"] = "tracks_id";
            $field['trading'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['trading'][0]["show"] = 0;
            $field['trading'][0]["custom"] = 0;

            $field['trading'][1]["fieldstring"] = "tracks_title";
            $field['trading'][1]["fieldname"] = $this->LgStringSwitch("订单主题");
            $field['trading'][1]["show"] = 1;
            $field['trading'][1]["custom"] = 1;

            $field['trading'][2]["fieldstring"] = "tracks_information";
            $field['trading'][2]["fieldname"] = $this->LgStringSwitch("跟踪信息");
            $field['trading'][2]["show"] = 1;
            $field['trading'][2]["custom"] = 1;

            $field['trading'][3]["fieldstring"] = "tracks_note";
            $field['trading'][3]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['trading'][3]["show"] = 1;
            $field['trading'][3]["custom"] = 1;

            $field['trading'][4]["fieldstring"] = "tracks_playname";
            $field['trading'][4]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['trading'][4]["show"] = 1;
            $field['trading'][4]["custom"] = 1;

            $field['trading'][5]["fieldstring"] = "tracks_time";
            $field['trading'][5]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['trading'][5]["show"] = 1;
            $field['trading'][5]["custom"] = 1;

            $field['info'][0]["fieldstring"] = "school_cnname";
            $field['info'][0]["fieldname"] = $this->LgStringSwitch("经办分校");
            $field['info'][0]["show"] = 1;
            $field['info'][0]["custom"] = 1;

            $field['info'][1]["fieldstring"] = "staffer_cnname";
            $field['info'][1]["fieldname"] = $this->LgStringSwitch("经办人");
            $field['info'][1]["show"] = 1;
            $field['info'][1]["custom"] = 1;

            $field['info'][2]["fieldstring"] = "createtime";
            $field['info'][2]["fieldname"] = $this->LgStringSwitch("经办时间");
            $field['info'][2]["show"] = 1;
            $field['info'][2]["custom"] = 1;

        } elseif (in_array($order_tradingOne['tradingtype_code'], $deal_code)) {
            $dataList = $this->getDealOrderOne($trading_pid);
            $field = array();

            $field['order'][0]["fieldstring"] = "trading_pid";
            $field['order'][0]["fieldname"] = $this->LgStringSwitch("交易编号");
            $field['order'][0]["show"] = 1;
            $field['order'][0]["custom"] = 1;

            $field['order'][1]["fieldstring"] = "order_pid";
            $field['order'][1]["fieldname"] = $this->LgStringSwitch("订单编号");
            $field['order'][1]["show"] = 1;
            $field['order'][1]["custom"] = 1;

            $field['order'][2]["fieldstring"] = "order_type";
            $field['order'][2]["fieldname"] = $this->LgStringSwitch("订单类型");
            $field['order'][2]["show"] = 1;
            $field['order'][2]["custom"] = 1;

            $field['order'][3]["fieldstring"] = "order_status";
            $field['order'][3]["fieldname"] = $this->LgStringSwitch("订单状态");
            $field['order'][3]["show"] = 1;
            $field['order'][3]["custom"] = 1;

            $field['order'][4]["fieldstring"] = "order_createtime";
            $field['order'][4]["fieldname"] = $this->LgStringSwitch("下单时间");
            $field['order'][4]["show"] = 1;
            $field['order'][4]["custom"] = 1;

            $field['course'][0]["fieldstring"] = "course_id";
            $field['course'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['course'][0]["show"] = 0;
            $field['course'][0]["custom"] = 1;

            $field['course'][1]["fieldstring"] = "item_name";
            $field['course'][1]["fieldname"] = $this->LgStringSwitch("来源课程别名称");
            $field['course'][1]["show"] = 1;
            $field['course'][1]["custom"] = 1;

            $field['course'][2]["fieldstring"] = "item_branch";
            $field['course'][2]["fieldname"] = $this->LgStringSwitch("来源课程别编号");
            $field['course'][2]["show"] = 1;
            $field['course'][2]["custom"] = 1;

            $field['course'][3]["fieldstring"] = "to_course_cnname";
            $field['course'][3]["fieldname"] = $this->LgStringSwitch("结转课程别名称");
            $field['course'][3]["show"] = 1;
            $field['course'][3]["custom"] = 1;

            $field['course'][4]["fieldstring"] = "to_course_branch";
            $field['course'][4]["fieldname"] = $this->LgStringSwitch("结转课程别编号");
            $field['course'][4]["show"] = 1;
            $field['course'][4]["custom"] = 1;

            $field['course'][5]["fieldstring"] = "dealcourse_figure";
            $field['course'][5]["fieldname"] = $this->LgStringSwitch("课程结转金额");
            $field['course'][5]["show"] = 1;
            $field['course'][5]["custom"] = 1;

            $field['course'][6]["fieldstring"] = "dealcourse_time";
            $field['course'][6]["fieldname"] = $this->LgStringSwitch("课程结转课次");
            $field['course'][6]["show"] = 1;
            $field['course'][6]["custom"] = 1;

            $field['contract'][0]["fieldstring"] = "tracks_id";
            $field['contract'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['contract'][0]["show"] = 1;
            $field['contract'][0]["custom"] = 1;

            $field['contract'][1]["fieldstring"] = "tracks_title";
            $field['contract'][1]["fieldname"] = $this->LgStringSwitch("标题");
            $field['contract'][1]["show"] = 1;
            $field['contract'][1]["custom"] = 1;

            $field['contract'][2]["fieldstring"] = "tracks_information";
            $field['contract'][2]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['contract'][2]["show"] = 1;
            $field['contract'][2]["custom"] = 1;

            $field['contract'][3]["fieldstring"] = "tracks_playname";
            $field['contract'][3]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['contract'][3]["show"] = 1;
            $field['contract'][3]["custom"] = 1;

            $field['contract'][4]["fieldstring"] = "tracks_time";
            $field['contract'][4]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['contract'][4]["show"] = 1;
            $field['contract'][4]["custom"] = 1;


            $field['trading'][0]["fieldstring"] = "tracks_id";
            $field['trading'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['trading'][0]["show"] = 0;
            $field['trading'][0]["custom"] = 0;

            $field['trading'][1]["fieldstring"] = "tracks_title";
            $field['trading'][1]["fieldname"] = $this->LgStringSwitch("订单主题");
            $field['trading'][1]["show"] = 1;
            $field['trading'][1]["custom"] = 1;

            $field['trading'][2]["fieldstring"] = "tracks_information";
            $field['trading'][2]["fieldname"] = $this->LgStringSwitch("跟踪信息");
            $field['trading'][2]["show"] = 1;
            $field['trading'][2]["custom"] = 1;

            $field['trading'][3]["fieldstring"] = "tracks_note";
            $field['trading'][3]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['trading'][3]["show"] = 1;
            $field['trading'][3]["custom"] = 1;

            $field['trading'][4]["fieldstring"] = "tracks_playname";
            $field['trading'][4]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['trading'][4]["show"] = 1;
            $field['trading'][4]["custom"] = 1;

            $field['trading'][5]["fieldstring"] = "tracks_time";
            $field['trading'][5]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['trading'][5]["show"] = 1;
            $field['trading'][5]["custom"] = 1;

            $field['info'][0]["fieldstring"] = "school_cnname";
            $field['info'][0]["fieldname"] = $this->LgStringSwitch("经办分校");
            $field['info'][0]["show"] = 1;
            $field['info'][0]["custom"] = 1;

            $field['info'][1]["fieldstring"] = "staffer_cnname";
            $field['info'][1]["fieldname"] = $this->LgStringSwitch("经办人");
            $field['info'][1]["show"] = 1;
            $field['info'][1]["custom"] = 1;

            $field['info'][2]["fieldstring"] = "createtime";
            $field['info'][2]["fieldname"] = $this->LgStringSwitch("经办时间");
            $field['info'][2]["show"] = 1;
            $field['info'][2]["custom"] = 1;

        } elseif (in_array($order_tradingOne['tradingtype_code'], $reduce_code)) {
            $dataList = $this->getReduceOrderOne($trading_pid);

            $field = array();

            $field['course'] = $dataList['field'];

            $field['order'][0]["fieldstring"] = "trading_pid";
            $field['order'][0]["fieldname"] = $this->LgStringSwitch("交易编号");
            $field['order'][0]["show"] = 1;
            $field['order'][0]["custom"] = 1;

            $field['order'][1]["fieldstring"] = "order_pid";
            $field['order'][1]["fieldname"] = $this->LgStringSwitch("订单编号");
            $field['order'][1]["show"] = 1;
            $field['order'][1]["custom"] = 1;

            $field['order'][2]["fieldstring"] = "order_type";
            $field['order'][2]["fieldname"] = $this->LgStringSwitch("订单类型");
            $field['order'][2]["show"] = 1;
            $field['order'][2]["custom"] = 1;

            $field['order'][3]["fieldstring"] = "order_status";
            $field['order'][3]["fieldname"] = $this->LgStringSwitch("订单状态");
            $field['order'][3]["show"] = 1;
            $field['order'][3]["custom"] = 1;

            $field['order'][4]["fieldstring"] = "order_createtime";
            $field['order'][4]["fieldname"] = $this->LgStringSwitch("下单时间");
            $field['order'][4]["show"] = 1;
            $field['order'][4]["custom"] = 1;

            $field['trading'][0]["fieldstring"] = "tracks_id";
            $field['trading'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['trading'][0]["show"] = 0;
            $field['trading'][0]["custom"] = 0;

            $field['trading'][1]["fieldstring"] = "tracks_title";
            $field['trading'][1]["fieldname"] = $this->LgStringSwitch("订单主题");
            $field['trading'][1]["show"] = 1;
            $field['trading'][1]["custom"] = 1;

            $field['trading'][2]["fieldstring"] = "tracks_information";
            $field['trading'][2]["fieldname"] = $this->LgStringSwitch("跟踪信息");
            $field['trading'][2]["show"] = 1;
            $field['trading'][2]["custom"] = 1;

            $field['trading'][3]["fieldstring"] = "tracks_note";
            $field['trading'][3]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['trading'][3]["show"] = 1;
            $field['trading'][3]["custom"] = 1;

            $field['trading'][4]["fieldstring"] = "tracks_playname";
            $field['trading'][4]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['trading'][4]["show"] = 1;
            $field['trading'][4]["custom"] = 1;

            $field['trading'][5]["fieldstring"] = "tracks_time";
            $field['trading'][5]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['trading'][5]["show"] = 1;
            $field['trading'][5]["custom"] = 1;


            $field['info'][0]["fieldstring"] = "school_cnname";
            $field['info'][0]["fieldname"] = $this->LgStringSwitch("经办分校");
            $field['info'][0]["show"] = 1;
            $field['info'][0]["custom"] = 1;

            $field['info'][1]["fieldstring"] = "staffer_cnname";
            $field['info'][1]["fieldname"] = $this->LgStringSwitch("经办人");
            $field['info'][1]["show"] = 1;
            $field['info'][1]["custom"] = 1;

            $field['info'][2]["fieldstring"] = "createtime";
            $field['info'][2]["fieldname"] = $this->LgStringSwitch("经办时间");
            $field['info'][2]["show"] = 1;
            $field['info'][2]["custom"] = 1;


        } elseif (in_array($order_tradingOne['tradingtype_code'], $hourFee_code)) {
            $dataList = $this->getHourFeeOne($trading_pid);
            $field = array();
            $field['order'][0]["fieldstring"] = "trading_pid";
            $field['order'][0]["fieldname"] = $this->LgStringSwitch("交易编号");
            $field['order'][0]["show"] = 1;
            $field['order'][0]["custom"] = 1;

            $field['order'][1]["fieldstring"] = "order_pid";
            $field['order'][1]["fieldname"] = $this->LgStringSwitch("订单编号");
            $field['order'][1]["show"] = 1;
            $field['order'][1]["custom"] = 1;

            $field['order'][2]["fieldstring"] = "order_type";
            $field['order'][2]["fieldname"] = $this->LgStringSwitch("订单类型");
            $field['order'][2]["show"] = 1;
            $field['order'][2]["custom"] = 1;

            $field['order'][3]["fieldstring"] = "order_status";
            $field['order'][3]["fieldname"] = $this->LgStringSwitch("订单状态");
            $field['order'][3]["show"] = 1;
            $field['order'][3]["custom"] = 1;

            $field['order'][4]["fieldstring"] = "order_createtime";
            $field['order'][4]["fieldname"] = $this->LgStringSwitch("下单时间");
            $field['order'][4]["show"] = 1;
            $field['order'][4]["custom"] = 1;

            $field['coursefee'][0]["fieldstring"] = "course_branch";
            $field['coursefee'][0]["fieldname"] = $this->LgStringSwitch("课程编号");
            $field['coursefee'][0]["show"] = 1;
            $field['coursefee'][0]["custom"] = 1;

            $field['coursefee'][1]["fieldstring"] = "course_cnname";
            $field['coursefee'][1]["fieldname"] = $this->LgStringSwitch("课程名");
            $field['coursefee'][1]["show"] = 1;
            $field['coursefee'][1]["custom"] = 1;

            $field['coursefee'][2]["fieldstring"] = "class_cnname";
            $field['coursefee'][2]["fieldname"] = $this->LgStringSwitch("班级名称");
            $field['coursefee'][2]["show"] = 1;
            $field['coursefee'][2]["custom"] = 1;

            $field['coursefee'][3]["fieldstring"] = "class_branch";
            $field['coursefee'][3]["fieldname"] = $this->LgStringSwitch("班级编号");
            $field['coursefee'][3]["show"] = 1;
            $field['coursefee'][3]["custom"] = 1;

            $field['coursefee'][4]["fieldstring"] = "order_alltimes";
            $field['coursefee'][4]["fieldname"] = $this->LgStringSwitch("总计赠送课次");
            $field['coursefee'][4]["show"] = 1;
            $field['coursefee'][4]["custom"] = 1;


            $field['lessontimes'][0]["fieldstring"] = "hour_day";
            $field['lessontimes'][0]["fieldname"] = $this->LgStringSwitch("上课日期");
            $field['lessontimes'][0]["show"] = 1;
            $field['lessontimes'][0]["custom"] = 1;

            $field['lessontimes'][1]["fieldstring"] = "week_name";
            $field['lessontimes'][1]["fieldname"] = $this->LgStringSwitch("上课周次");
            $field['lessontimes'][1]["show"] = 1;
            $field['lessontimes'][1]["custom"] = 1;

            $field['lessontimes'][2]["fieldstring"] = "hour_starttime";
            $field['lessontimes'][2]["fieldname"] = $this->LgStringSwitch("开始时间");
            $field['lessontimes'][2]["show"] = 1;
            $field['lessontimes'][2]["custom"] = 1;

            $field['lessontimes'][3]["fieldstring"] = "hour_endtime";
            $field['lessontimes'][3]["fieldname"] = $this->LgStringSwitch("结束时间");
            $field['lessontimes'][3]["show"] = 1;
            $field['lessontimes'][3]["custom"] = 1;

            $field['lessontimes'][4]["fieldstring"] = "hour_lessontimes";
            $field['lessontimes'][4]["fieldname"] = $this->LgStringSwitch("上课课次");
            $field['lessontimes'][4]["show"] = 1;
            $field['lessontimes'][4]["custom"] = 1;

            $field['lessontimes'][5]["fieldstring"] = "order_lessontimes";
            $field['lessontimes'][5]["fieldname"] = $this->LgStringSwitch("下单课次");
            $field['lessontimes'][5]["show"] = 1;
            $field['lessontimes'][5]["custom"] = 1;

            $field['trading'][0]["fieldstring"] = "tracks_id";
            $field['trading'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['trading'][0]["show"] = 0;
            $field['trading'][0]["custom"] = 0;

            $field['trading'][1]["fieldstring"] = "tracks_title";
            $field['trading'][1]["fieldname"] = $this->LgStringSwitch("订单主题");
            $field['trading'][1]["show"] = 1;
            $field['trading'][1]["custom"] = 1;

            $field['trading'][2]["fieldstring"] = "tracks_information";
            $field['trading'][2]["fieldname"] = $this->LgStringSwitch("跟踪信息");
            $field['trading'][2]["show"] = 1;
            $field['trading'][2]["custom"] = 1;

            $field['trading'][3]["fieldstring"] = "tracks_note";
            $field['trading'][3]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['trading'][3]["show"] = 1;
            $field['trading'][3]["custom"] = 1;

            $field['trading'][4]["fieldstring"] = "tracks_playname";
            $field['trading'][4]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['trading'][4]["show"] = 1;
            $field['trading'][4]["custom"] = 1;

            $field['trading'][5]["fieldstring"] = "tracks_time";
            $field['trading'][5]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['trading'][5]["show"] = 1;
            $field['trading'][5]["custom"] = 1;

            $field['info'][0]["fieldstring"] = "school_cnname";
            $field['info'][0]["fieldname"] = $this->LgStringSwitch("经办分校");
            $field['info'][0]["show"] = 1;
            $field['info'][0]["custom"] = 1;

            $field['info'][1]["fieldstring"] = "staffer_cnname";
            $field['info'][1]["fieldname"] = $this->LgStringSwitch("经办人");
            $field['info'][1]["show"] = 1;
            $field['info'][1]["custom"] = 1;

            $field['info'][2]["fieldstring"] = "createtime";
            $field['info'][2]["fieldname"] = $this->LgStringSwitch("经办时间");
            $field['info'][2]["show"] = 1;
            $field['info'][2]["custom"] = 1;
        } elseif (in_array($order_tradingOne['tradingtype_code'], $schoolChange_code)) {

            $field = array();
            $field['trading'][0]["fieldstring"] = "tracks_id";
            $field['trading'][0]["fieldname"] = $this->LgStringSwitch("序号");
            $field['trading'][0]["show"] = 0;
            $field['trading'][0]["custom"] = 0;

            $field['trading'][1]["fieldstring"] = "tracks_title";
            $field['trading'][1]["fieldname"] = $this->LgStringSwitch("订单主题");
            $field['trading'][1]["show"] = 1;
            $field['trading'][1]["custom"] = 1;

            $field['trading'][2]["fieldstring"] = "tracks_information";
            $field['trading'][2]["fieldname"] = $this->LgStringSwitch("跟踪信息");
            $field['trading'][2]["show"] = 1;
            $field['trading'][2]["custom"] = 1;

            $field['trading'][3]["fieldstring"] = "tracks_note";
            $field['trading'][3]["fieldname"] = $this->LgStringSwitch("备注信息");
            $field['trading'][3]["show"] = 1;
            $field['trading'][3]["custom"] = 1;

            $field['trading'][4]["fieldstring"] = "tracks_playname";
            $field['trading'][4]["fieldname"] = $this->LgStringSwitch("操作人");
            $field['trading'][4]["show"] = 1;
            $field['trading'][4]["custom"] = 1;

            $field['trading'][5]["fieldstring"] = "tracks_time";
            $field['trading'][5]["fieldname"] = $this->LgStringSwitch("操作时间");
            $field['trading'][5]["show"] = 1;
            $field['trading'][5]["custom"] = 1;

            $field['info'][0]["fieldstring"] = "school_cnname";
            $field['info'][0]["fieldname"] = $this->LgStringSwitch("经办分校");
            $field['info'][0]["show"] = 1;
            $field['info'][0]["custom"] = 1;

            $field['info'][1]["fieldstring"] = "staffer_cnname";
            $field['info'][1]["fieldname"] = $this->LgStringSwitch("经办人");
            $field['info'][1]["show"] = 1;
            $field['info'][1]["custom"] = 1;

            $field['info'][2]["fieldstring"] = "createtime";
            $field['info'][2]["fieldname"] = $this->LgStringSwitch("经办时间");
            $field['info'][2]["show"] = 1;
            $field['info'][2]["custom"] = 1;


            $field['course'][0]["fieldstring"] = "from_companies_cnname";
            $field['course'][0]["fieldname"] = $this->LgStringSwitch("来源主体");
            $field['course'][0]["show"] = 1;
            $field['course'][0]["custom"] = 1;

            $field['course'][1]["fieldstring"] = "from_school_cnname";
            $field['course'][1]["fieldname"] = $this->LgStringSwitch("来源学校");
            $field['course'][1]["show"] = 1;
            $field['course'][1]["custom"] = 1;

            $field['course'][2]["fieldstring"] = "to_companies_cnname";
            $field['course'][2]["fieldname"] = $this->LgStringSwitch("去向主体");
            $field['course'][2]["show"] = 1;
            $field['course'][2]["custom"] = 1;

            $field['course'][3]["fieldstring"] = "to_school_cnname";
            $field['course'][3]["fieldname"] = $this->LgStringSwitch("去向学校");
            $field['course'][3]["show"] = 1;
            $field['course'][3]["custom"] = 1;

            $field['course'][4]["fieldstring"] = "trading_price";
            $field['course'][4]["fieldname"] = $this->LgStringSwitch("交易金额");
            $field['course'][4]["show"] = 1;
            $field['course'][4]["custom"] = 1;

            $field['course'][5]["fieldstring"] = "trading_balance";
            $field['course'][5]["fieldname"] = $this->LgStringSwitch("账户可退交易金额");
            $field['course'][5]["show"] = 1;
            $field['course'][5]["custom"] = 1;

            $field['course'][6]["fieldstring"] = "trading_withholdbalance";
            $field['course'][6]["fieldname"] = $this->LgStringSwitch("账户不可退交易金额");
            $field['course'][6]["show"] = 1;
            $field['course'][6]["custom"] = 1;


            if ($order_tradingOne['tradingtype_code'] == 'TransferIn') {
                $schoolTrading = $this->DataControl->selectOne("select t.trading_topid,t.trading_pid,p.tradingtype_name,t.trading_createtime,sc.school_cnname as from_school_cnname,sc.school_branch as from_school_branch,sch.school_cnname as to_school_cnname,sch.school_branch as to_school_branch,t.trading_price,t.trading_balance,t.trading_withholdbalance,A.companies_cnname as from_companies_cnname,B.companies_cnname as to_companies_cnname,t.trading_price,t.trading_withholdbalance,t.trading_balance
            from smc_school_trading as t
            left join smc_student_trading as st ON t.trading_topid =st.trading_pid
            left join smc_code_tradingtype as p ON st.tradingtype_code = p.tradingtype_code
            left join smc_school as sc on sc.school_id=t.from_school_id
            left join smc_school as sch on sch.school_id=t.to_school_id
            left join gmc_code_companies as A on A.companies_id=t.from_companies_id
            left join gmc_code_companies as B on B.companies_id=t.to_companies_id
            where t.trading_topid='{$order_tradingOne['trading_pid']}' ");

                $sql = "select * from smc_school_trading where trading_topid='{$order_tradingOne['trading_pid']}' and company_id='{$this->company_id}'";

                $schoolTradeOne = $this->DataControl->selectOne($sql);

            } else {
                $schoolTrading = $this->DataControl->selectOne("select t.trading_topid,t.trading_pid,p.tradingtype_name,t.trading_createtime,sc.school_cnname as from_school_cnname,sc.school_branch as from_school_branch,sch.school_cnname as to_school_cnname,sch.school_branch as to_school_branch,t.trading_price,t.trading_balance,t.trading_withholdbalance,A.companies_cnname as from_companies_cnname,B.companies_cnname as to_companies_cnname,t.trading_price,t.trading_withholdbalance,t.trading_balance
            from smc_school_trading as t
            left join smc_student_trading as st ON t.trading_frompid =st.trading_pid
            left join smc_code_tradingtype as p ON st.tradingtype_code = p.tradingtype_code
            left join smc_school as sc on sc.school_id=t.from_school_id
            left join smc_school as sch on sch.school_id=t.to_school_id
            left join gmc_code_companies as A on A.companies_id=t.from_companies_id
            left join gmc_code_companies as B on B.companies_id=t.to_companies_id
            where t.trading_frompid='{$order_tradingOne['trading_pid']}' ");

                $sql = "select * from smc_school_trading where trading_frompid='{$order_tradingOne['trading_pid']}' and company_id='{$this->company_id}'";

                $schoolTradeOne = $this->DataControl->selectOne($sql);
            }

            $studentOne['from_school_cnname'] = $schoolTrading['from_school_cnname'];
            $studentOne['from_school_branch'] = $schoolTrading['from_school_branch'];
            $studentOne['to_school_cnname'] = $schoolTrading['to_school_cnname'];
            $studentOne['to_school_branch'] = $schoolTrading['to_school_branch'];
            $studentOne['trading_price'] = $schoolTrading['trading_price'];
            $studentOne['trading_balance'] = $schoolTrading['trading_balance'];
            $studentOne['trading_withholdbalance'] = $schoolTrading['trading_withholdbalance'];

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));
            $orderOne = array();
            $orderOne['trading_pid'] = $order_tradingOne['trading_pid'];
            $orderOne['order_pid'] = '--';
            $orderOne['order_type'] = $schoolTrading['tradingtype_name'];
            $orderOne['order_status'] = $status[$schoolTradeOne['trading_status']];
            $orderOne['order_createtime'] = date("Y-m-d", $schoolTrading['trading_createtime']);
            $order['order'] = $orderOne;
            $order['course'][] = $schoolTrading;

            //缴费订单跟踪记录
            $sql = "select t.*,st.staffer_cnname,st.staffer_enname 
                  from smc_school_trading_tracks as t 
                  left join smc_staffer as st on st.staffer_id=t.staffer_id
                  where t.trading_pid='{$schoolTrading['trading_pid']}'";

            $tradingList = $this->DataControl->selectClear($sql);
            if ($tradingList) {
                foreach ($tradingList as $key => $value) {
                    $tradingList[$key]['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                    $tradingList[$key]['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);
                    $tradingList[$key]['order_pid'] = $value['trading_pid'];
                    $tradingList[$key]['tracks_title'] = $this->LgStringSwitch($value['tracks_title']);
                }
            }
            $order['trading'] = $tradingList;
            $order['info'] = array();
            $info = $this->DataControl->selectClear("
                select s.school_cnname,f.staffer_cnname,f.staffer_enname,t.trading_createtime
                from smc_student_trading  as t 
                left join smc_school as s ON t.school_id = s.school_id
                left join smc_staffer as f ON f.staffer_id=t.staffer_id
                where trading_pid='{$order_tradingOne['trading_pid']}'
             ");
            $info[0]['createtime'] = date('Y-m-d H:i:s', $info[0]['trading_createtime']);
            $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];
            $order['info'] = $info;
            $dataList = $order;
        }

        $dataList['student'] = $studentOne;
        $result['field'] = $field;
        $result['list'] = $dataList;
        if ($order_tradingOne['tradingtype_code'] == 'Accountrefund' || $order_tradingOne['tradingtype_code'] == 'TransferIn' || $order_tradingOne['tradingtype_code'] == 'TransferOut') {
            if ($order_tradingOne['tradingtype_code'] == 'Accountrefund') {
                $refundOne = $this->DataControl->getFieldOne("smc_refund_order", "refund_status", "trading_pid='{$order_tradingOne['trading_pid']}'");
                if ($refundOne['refund_status'] >= '1') {
                    $result['can_print'] = 1;
                } else {
                    $result['can_print'] = 0;
                }
            } else {
                if ($order_tradingOne['trading_status'] == '1') {
                    $result['can_print'] = 1;
                } else {
                    $result['can_print'] = 0;
                }
            }

        } else {
            $result['can_print'] = 0;
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_vouchermode,school_isvoucher,school_isprotocol", "school_id='{$this->school_id}'");
        $data = array();
        $data['school_vouchermode'] = $schoolOne['school_vouchermode'];
        $data['school_isvoucher'] = $schoolOne['school_isvoucher'];
        $data['school_isprotocol'] = $schoolOne['school_isprotocol'];

        $result['schoolInfo'] = $data;
        return $result;
    }

    function getPayOrderOne()
    {
        $order = array();
        //订单信息
        $orderPay = $this->DataControl->selectOne("
            select po.*
                ,(select sum(pay_price) from  smc_payfee_order_pay as op  where  (op.paytype_code='balance' or op.paytype_code='norebalance' )and  op.order_pid=po.order_pid and op.pay_issuccess=1 ) as order_balance_price
                ,(select sum(pay_price) from  smc_payfee_order_pay as op  where  op.paytype_code='forward' and  op.order_pid=po.order_pid and op.pay_issuccess=1 ) as order_forward_price
                ,(select  tradingtype_code from  smc_student_trading as t where t.trading_pid = po.trading_pid and t.student_id = po.student_id  limit 0,1) as  tradingtype_code
                ,ifnull((select op.pay_id from  smc_payfee_order_pay as op  where  op.paytype_code='canceldebts' and  op.order_pid=po.order_pid and op.pay_issuccess=0),0) as pay_id
                ,(select concat(coursetype_branch,'-',coursetype_cnname) from smc_code_coursetype where coursetype_id=po.coursetype_id and coursetype_id>0 limit 0,1) as coursetype_info 
                ,(select concat(coursecat_branch,'-',coursecat_cnname) from smc_code_coursecat where coursecat_id=po.coursecat_id and coursecat_id>0 limit 0,1) as coursecat_info 
                from smc_payfee_order as po  where po.order_pid ='{$this->payfeeorderOne['order_pid']}'
            ");

        $orderOne = array();
        $order_type = $this->LgArraySwitch(array(0 => '课程缴费订单', 1 => '普通收费订单', 2 => "充值类订单"));
        $order_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));
        $orderOne['trading_pid'] = $this->payfeeorderOne['trading_pid'];
        $orderOne['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderOne['coursetype_id'] = $this->payfeeorderOne['coursetype_id'];
        $orderOne['order_type_num'] = $this->payfeeorderOne['order_type'];
        $orderOne['order_type'] = $order_type[$this->payfeeorderOne['order_type']];
        if ($orderPay['tradingtype_code'] == 'CourseMakeUp') {
            $orderOne['order_type'] = $this->LgStringSwitch('课程补费订单');
        }
        if ($orderPay['tradingtype_code'] == 'DepositCharge') {
            $orderOne['order_type'] = $this->LgStringSwitch('定金充值订单');
        }
        $orderOne['order_status'] = $order_status[$this->payfeeorderOne['order_status']];
        $orderOne['order_createtime'] = date("Y-m-d", $this->payfeeorderOne['order_createtime']);
        $orderOne['order_allprice'] = $this->payfeeorderOne['order_allprice'];
        $orderOne['order_coupon_allprice'] = sprintf("%01.2f", ($this->payfeeorderOne['order_coupon_price'] + $this->payfeeorderOne['order_market_price']));
        $orderOne['order_balance_price'] = sprintf("%01.2f", $orderPay['order_balance_price']);
        $orderOne['order_forward_price'] = sprintf("%01.2f", $orderPay['order_forward_price']);
        $orderOne['order_paidprice'] = sprintf("%01.2f", $orderPay['order_paidprice']);
        $orderOne['order_arrearageprice'] = sprintf("%01.2f", $orderPay['order_arrearageprice']);
        if ($this->payfeeorderOne['order_type'] == 0 && $this->payfeeorderOne['order_arrearageprice'] > 0 && $orderPay['pay_id'] == 0) {
            $orderOne['can_debt'] = 1;
        } else {
            $orderOne['can_debt'] = 0;
        }

        $order['order'] = $orderOne;

        $sql = "select a.coupons_name,(case when a.coupons_price>0 then a.coupons_price else sum(b.ordercoupons_price) end) as coupons_price
                    from smc_student_coupons a,smc_payfee_order_coupons b
                    where a.coupons_pid=b.coupons_pid
                    and a.order_pid=b.order_pid
                    and a.student_id={$orderPay['student_id']} 
                    and a.order_pid ='{$this->payfeeorderOne['order_pid']}'
                    and a.coupons_isuse=1
                    group by a.coupons_pid";
        $couponList = $this->DataControl->selectClear($sql);
        if ($orderPay['order_market_price'] > 0) {
            $datearray = array();
            $datearray['coupons_name'] = '活动优惠';
            $datearray['coupons_price'] = $orderPay['order_market_price'];

            $couponList[] = $datearray;
        }
        $order['coupons'] = $couponList ? $couponList : array();

        //购买的课程信息
        if ($this->payfeeorderOne['order_type'] == 0) {
            $sql = "select oc.ordercourse_id,oc.course_id,c.course_branch,c.course_cnname,ct.coursecat_cnname,fp.tuition_originalprice,fp.tuition_sellingprice,oc.pricing_id,p.pricing_name,oc.ordercourse_totalprice,oc.ordercourse_buynums,c.course_img,oc.ordercourse_totalprice
                    ,(select ag.agreement_cnname from smc_fee_agreement as ag where p.agreement_id=ag.agreement_id )  as  agreement_cnname
                    from smc_payfee_order_course as oc 
                    LEFT JOIN smc_course as c ON c.course_id  = oc.course_id
                    LEFT JOIN smc_code_coursecat as ct ON ct.coursecat_id = c.coursecat_id
                    LEFT JOIN smc_fee_pricing_tuition as fp ON fp.pricing_id = oc.pricing_id
                    left JOIN smc_fee_pricing as p On p.pricing_id  = fp.pricing_id
                    where oc.order_pid ='{$this->payfeeorderOne['order_pid']}'";
            $courseList = $this->DataControl->selectClear($sql);

            if ($courseList) {
                foreach ($courseList as $courseOne) {
                    $courseDataOne = array();
                    $courseDataOne['course_id'] = $courseOne['course_id']; //序号
                    $courseDataOne['item_name'] = $courseOne['course_cnname']; //项目名称
                    $courseDataOne['item_branch'] = $courseOne['course_branch']; //项目编号
                    $courseDataOne['coursecat_cnname'] = $courseOne['coursecat_cnname']; //所属班种
                    $courseDataOne['original_price'] = $courseOne['tuition_originalprice'] == "" ? $courseOne['ordercourse_totalprice'] : $courseOne['tuition_originalprice']; //原价
                    $courseDataOne['selling_price'] = $courseOne['tuition_sellingprice'] == "" ? $courseOne['ordercourse_totalprice'] : $courseOne['tuition_sellingprice'];; //销售价
                    $courseDataOne['pricing_name'] = $courseOne['agreement_cnname'] == "" ? '--' : $courseOne['agreement_cnname'];  //价格批次
                    $courseDataOne['item_unit'] = '--';// 单位


                    $courseDataOne['course_img'] = $courseOne['course_img']; //序号
                    $courseDataOne['ordercourse_buynums'] = $courseOne['ordercourse_buynums']; //序号
                    $courseDataOne['ordercourse_totalpricez'] = $courseOne['ordercourse_totalprice']; //序号

                    $goodList = $this->DataControl->selectClear("select pog.goods_id,pog.ordergoods_buynums,pog.ordergoods_unitprice,pog.ordergoods_totalprice,g.goods_cnname,pp.products_donatepiece,g.goods_pid,fp.pricing_name,g.goods_unit,
   (select ag.agreement_cnname from smc_fee_agreement as ag where fp.agreement_id=ag.agreement_id )  as  agreement_cnname
                   from  smc_payfee_order_goods  as pog
                    left join smc_fee_pricing_products as pp ON pp.pricing_id =pog.pricing_id  and pp.goods_id = pog.goods_id
                    left join smc_fee_pricing as fp ON fp.pricing_id =pog.pricing_id 
                    left join erp_goods as g  on  g.goods_id=pog.goods_id
                    where pog.order_pid ='{$this->payfeeorderOne['order_pid']}' and pog.pricing_id ='{$courseOne['pricing_id']}'  ");
                    $goodsData = array();
                    if ($goodList) {
                        foreach ($goodList as $goodsOne) {
                            $goodsDataOne = array();
                            $goodsDataOne['course_id'] = $courseOne['course_id']; //序号
                            $goodsDataOne['item_name'] = $goodsOne['goods_cnname'];  //项目名称
                            $goodsDataOne['item_branch'] = $goodsOne['goods_pid']; //项目编号
                            $goodsDataOne['coursecat_cnname'] = $courseOne['coursecat_cnname']; //所属班种
                            $goodsDataOne['original_price'] = $goodsOne['ordergoods_unitprice'] * $goodsOne['ordergoods_buynums'];//原价
                            $goodsDataOne['selling_price'] = $goodsOne['ordergoods_unitprice']; //销售价
                            $goodsDataOne['ordergoods_buynums'] = $goodsOne['ordergoods_buynums'];
                            $goodsDataOne['pricing_name'] = $goodsOne['agreement_cnname']; //价格批次
                            $goodsDataOne['item_unit'] = $goodsOne['goods_unit'];// 单位
                            $goodsData[] = $goodsDataOne;
                        }
                    }
                    $courseDataOne['goods'] = $goodsData;

                    $orderItems = $this->DataControl->selectClear("select cf.feeitem_id,cf.feeitem_cnname,cf.feeitem_branch,poi.item_buynums,poi.item_unitprice,poi.item_totalprice,cf.feeitem_price,fp.pricing_name,cf.feeitem_unit,poi.item_buynums
  ,(select ag.agreement_cnname from smc_fee_agreement as ag where fp.agreement_id=ag.agreement_id )  as  agreement_cnname
                   from  smc_payfee_order_item  as poi
                    left join smc_fee_pricing_items as pp ON pp.items_id =poi.items_id
                        left join smc_fee_pricing as fp ON pp.pricing_id =fp.pricing_id 
                    left join smc_code_feeitem as cf ON cf.feeitem_branch = poi.feeitem_branch and cf.company_id='{$this->payfeeorderOne['company_id']}'
                    where poi.order_pid ='{$this->payfeeorderOne['order_pid']}' and pp.pricing_id ='{$courseOne['pricing_id']}'  ");

                    $itemsData = array();
                    if ($orderItems) {
                        foreach ($orderItems as $itemsOne) {
                            $orderItemsOne = array();
                            $orderItemsOne['course_id'] = $courseOne['course_id']; //序号
                            $orderItemsOne['item_name'] = $itemsOne['feeitem_cnname'];  //项目名称
                            $orderItemsOne['item_branch'] = $itemsOne['feeitem_branch']; //项目编号
                            $orderItemsOne['coursecat_cnname'] = $courseOne['coursecat_cnname']; //所属班种
                            $orderItemsOne['original_price'] = $itemsOne['item_unitprice'] * $itemsOne['item_buynums'];//原价
                            $orderItemsOne['selling_price'] = $itemsOne['item_totalprice']; //销售价
                            $orderItemsOne['item_buynums'] = $itemsOne['item_buynums']; //销售价
                            $orderItemsOne['pricing_name'] = $itemsOne['agreement_cnname']; //价格批次
                            $orderItemsOne['item_unit'] = $itemsOne['feeitem_unit'];// 单位
                            $itemsData[] = $orderItemsOne;
                        }
                    }
                    $courseDataOne['items'] = $itemsData;
                    $courseData[] = $courseDataOne;
                }
                $order['course'] = $courseData;
            }
            $contractList = $this->orderContractList(array('trading_pid' => $this->payfeeorderOne['trading_pid']));
            $order['contract'] = $contractList;
        } elseif ($this->payfeeorderOne['order_type'] == 1) {
            //其他收费类型
            $order['course'] = array();
            $itemList = $this->DataControl->selectClear("select og.ordergoods_id,g.goods_cnname,og.ordergoods_buynums,og.ordergoods_unitprice,og.ordergoods_totalprice,g.goods_pid,g.goods_unit,cp.prodtype_name
            from smc_payfee_order_goods as og
            left JOIN  erp_goods as g ON g.goods_id = og.goods_id
            Left JOIN smc_code_prodtype  as cp ON cp.prodtype_code =g.prodtype_code and cp.company_id =g.company_id
            where  og.order_pid ='{$this->payfeeorderOne['order_pid']}'");


            $order['items'] = array();
            if ($itemList) {
                $itemsData = array();
                foreach ($itemList as $itemOne) {
                    $itemDateOne = array();
                    $itemDateOne['item_id'] = $itemOne['ordergoods_id']; //序号
                    $itemDateOne['item_name'] = $itemOne['goods_cnname'];  //项目名称
                    $itemDateOne['item_branch'] = $itemOne['goods_pid']; //项目编号
                    $itemDateOne['item_buynums'] = $itemOne['ordergoods_buynums']; //购买数量
                    $itemDateOne['item_unitprice'] = $itemOne['ordergoods_unitprice'];//单价
                    $itemDateOne['item_unit'] = $itemOne['goods_unit']; //单位
                    $itemDateOne['item_typename'] = $itemOne['prodtype_name']; //类型
                    $itemsData[] = $itemDateOne;
                }
                $order['items'] = $itemsData;
            }

            //杂费
            $itemList = $this->DataControl->selectClear("select oi.item_id,oi.item_buynums,oi.item_unitprice,oi.item_totalprice,cf.feeitem_cnname,cf.feeitem_branch,cf.feeitem_unit,cf.feeitem_class from  smc_payfee_order_item as oi
 left join smc_code_feeitem as cf ON cf.feeitem_branch = oi.feeitem_branch
where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.company_id ='{$this->payfeeorderOne['company_id']}' ");
            $order['common_items'] = array();
            if ($itemList) {
                $itemsData = array();
                foreach ($itemList as $itemOne) {
                    $itemDateOne = array();
                    $itemDateOne['item_id'] = $itemOne['item_id']; //序号
                    $itemDateOne['item_name'] = $itemOne['feeitem_cnname'];  //项目名称
                    $itemDateOne['item_branch'] = $itemOne['feeitem_branch']; //项目编号
                    $itemDateOne['item_buynums'] = $itemOne['item_buynums']; //购买数量
                    $itemDateOne['item_unitprice'] = $itemOne['item_unitprice'];//单价
                    $itemDateOne['item_unit'] = $itemOne['feeitem_unit']; //单位
                    $itemDateOne['item_typename'] = $itemOne['feeitem_class'] == 0 ? '课程杂费' : '普通杂费'; //类型
                    $itemsData[] = $itemDateOne;
                }
                $order['common_items'] = $itemsData;
            }
            $order['contract'] = array();
        } elseif ($this->payfeeorderOne['order_type'] == 2) {
            $refill_prcie = array();
            $refill_prcie['refill_price'] = $this->payfeeorderOne['order_paymentprice'];
            $order['refill_price'] = $refill_prcie;
            $order['coursetype_info'] = $orderPay['coursetype_info'];
            $order['coursecat_info'] = $orderPay['coursecat_info'];

            $order['contract'] = array();
        }


        //支付记录
        $payList = $this->DataControl->selectClear("select op.pay_pid,op.pay_typename,op.paytype_code,op.pay_successtime,op.pay_price,op.order_pid,op.paychannel_code,
                pop.paylog_tradeno,op.pay_note,op.pay_outnumber,pop.paylog_ifee,pop.paylog_img,op.pay_issuccess
                from  smc_payfee_order_pay as op
                left join smc_payfee_order_paylog as pop ON pop.order_pid =op.order_pid and op.pay_pid = pop.pay_pid
                left join smc_payfee_order as po ON  po.order_pid =op.order_pid
                left join smc_student as s ON po.student_id = s.student_id
                where  op.order_pid='{$this->payfeeorderOne['order_pid']}' order by op.pay_createtime DESC ");
        if ($payList) {
//            0-未支付 1-已支付-1已失效
            $arrpay = array(0 => '未支付', 1 => '已支付', '-1' => '已失效');
            foreach ($payList as &$payOne) {
                //更新已经支付的支付状态
                if($payOne['pay_outnumber']!='' && $payOne['pay_issuccess']==0 && $payOne['paychannel_code']=='cmb'){
                    $PayModel = new \Model\Scshop\BoingPayModel($this->payfeeorderOne['company_id']);
                    $result=$PayModel->orderPayStatus($payOne['pay_pid']);
                    if($result["result"]["pay_result"] == "succeed"){
                        $payOne['pay_issuccess']=1;
                        $payOne['pay_successtime']=strtotime($result["result"]["pay_time"]);
                    }
                }

                $payOne['pay_issuccess_name'] = $arrpay[$payOne['pay_issuccess']];
                if ($payOne['pay_successtime']) {
                    $payOne['pay_successtime'] = date("Y-m-d", $payOne['pay_successtime']);
                } else {
                    $payOne['pay_successtime'] = '--';
                }



            }
            $order['paylist'] = $payList;
        } else {
            $order['paylist'] = array();
        }

        //缴费订单跟踪记录
        $sql = "select t.*,st.staffer_cnname,st.staffer_enname 
                from smc_payfee_order_tracks as t 
                left join smc_staffer as st on st.staffer_id=t.staffer_id
                where order_pid='{$this->payfeeorderOne['order_pid']}'";

        $tradingList = $this->DataControl->selectClear($sql);
        if ($tradingList) {
            foreach ($tradingList as $key => &$value) {
                $value['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $value['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);
                $value['tracks_title'] = $this->LgStringSwitch($value['tracks_title']);
            }
        }

        $order['trading'] = $tradingList;
        //        经办信息
        $info = $this->DataControl->selectClear("select s.school_cnname,
                (select t.staffer_cnname from  smc_staffer as t where  t.staffer_id ='{$this->payfeeorderOne['staffer_id']}') as staffer_cnname
                ,(select t.staffer_enname from  smc_staffer as t where  t.staffer_id ='{$this->payfeeorderOne['staffer_id']}') as staffer_enname
                from smc_school as  s  where s.school_id ='{$this->payfeeorderOne['school_id']}' ");
        $info[0]['create_time'] = date('Y-m-d H:i:s', $this->payfeeorderOne['order_createtime']);
        $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];
        $order['info'] = $info;
        return $order;
    }

    function getRefundOrderOne($trading_pid)
    {
        $order = array();
        $orderRefund = $this->DataControl->selectOne("SELECT ro.trading_pid,ro.refund_pid,ro.refund_type,ro.refund_status,ro.refund_createtime,ro.refund_bank,ro.refund_bankcard,ro.refund_accountname,ro.refund_specialprice,ro.refund_payprice ,ro.refund_forward,ro.school_id,ro.staffer_id,ro.refund_tradeclass,ro.refund_price,ro.refund_reasontype,ro.refund_bankcardurl,ro.refund_reason,ro.refund_specialreason 
        from  smc_refund_order as ro 
        where ro.trading_pid ='{$trading_pid}' 
        limit 0,1 ");
        $refund = array();
        //0普通退费订单1教材退费订单2杂项退费订单',
        $arr_tradeclass = $this->LgArraySwitch(array('0' => '普通退费订单', '1' => '教材退费订单', "2" => '杂项退费订单', "3" => '定金退费订单'));
        //        退款状态0-申请 1-审核通过 2-处理中 3-确定金额 4-完成退款',
        $refund_status = $this->LgArraySwitch(array('0' => '待审核(校园)', '1' => '待审核(集团)', '2' => '待处理', '3' => '待确定金额', '4' => '已完成', '-1' => '审核拒绝'));
        //1-学习成效原因 2师资原因 3价格原因 4就读同业 5其他原因
        $refund_reasontype = $this->LgArraySwitch(array('1' => '学习成效原因', '2' => '师资原因', '3' => '价格原因', '4' => '就读同业', '5' => '其他原因'));

        $refund['trading_pid'] = $orderRefund['trading_pid'];
        $refund['order_pid'] = $orderRefund['refund_pid'];
        $refund['order_type'] = $this->LgStringSwitch($orderRefund['refund_type'] == 0 ? '银行转账' : '原路返回');
        $refund['order_status'] = $refund_status[$orderRefund['refund_status']];
        $refund['order_createtime'] = date("Y-m-d", $orderRefund['refund_createtime']);
        $refund['refund_bank'] = $orderRefund['refund_bank'];
        $refund['refund_accountname'] = $orderRefund['refund_accountname'];
        $refund['refund_bankcard'] = $orderRefund['refund_bankcard'];
        $refund['refund_specialprice'] = $orderRefund['refund_specialprice'];
        $refund['refund_payprice'] = $orderRefund['refund_payprice'];
        $refund['refund_price'] = $orderRefund['refund_price'];
        $refund['refund_specialreason'] = $orderRefund['refund_specialreason'];
        $refund['is_refud_forward'] = $this->LgStringSwitch($orderRefund['refund_forward'] > 0 ? '是' : '否');
        $refund['refund_tradeclass_name'] = $arr_tradeclass[$orderRefund['refund_tradeclass']];

        $refund['refund_reasontype'] = $this->LgStringSwitch($refund_reasontype[$orderRefund['refund_reasontype']]);
        $refund['refund_bankcardurl'] = $orderRefund['refund_bankcardurl'];
        $refund['refund_reason'] = $orderRefund['refund_reason'];


        $order['order'] = $refund;
        $order['reduce'] = $refund;
        //订单合同管里
        $order['contract'] = array();
        //订单信息
        $itemList = $this->DataControl->selectClear(" 
            select cf.feeitem_cnname,cf.feeitem_branch,co.course_cnname,co.course_branch,oi.itemtimes_number,oi.itemtimes_figure
            from smc_refund_order_itemtimes as  oi
            left join smc_code_feeitem as cf ON cf.feeitem_id = oi.feeitem_id 
            left join smc_course as co ON co.course_id = oi.course_id
            where oi.refund_pid='{$orderRefund['refund_pid']}'");
        if ($itemList) {
            $k = 0;
            $field['items'][$k]["fieldstring"] = "feeitem_cnname";
            $field['items'][$k]["fieldname"] = $this->LgStringSwitch("项目名称");
            $field['items'][$k]["show"] = 1;
            $field['items'][$k]["custom"] = 1;
            $k++;
            $field['items'][$k]["fieldstring"] = "feeitem_branch";
            $field['items'][$k]["fieldname"] = $this->LgStringSwitch("项目编号");
            $field['items'][$k]["show"] = 1;
            $field['items'][$k]["custom"] = 1;
            $k++;
            $field['items'][$k]["fieldstring"] = "course_cnname";
            $field['items'][$k]["fieldname"] = $this->LgStringSwitch("课程别");
            $field['items'][$k]["show"] = 1;
            $field['items'][$k]["custom"] = 1;
            $k++;
            $field['items'][$k]["fieldstring"] = "course_branch";
            $field['items'][$k]["fieldname"] = $this->LgStringSwitch("课程别编号");
            $field['items'][$k]["show"] = 1;
            $field['items'][$k]["custom"] = 1;
            $k++;
            $field['items'][$k]["fieldstring"] = "itemtimes_number";
            $field['items'][$k]["fieldname"] = $this->LgStringSwitch("项目剩余次数");
            $field['items'][$k]["show"] = 1;
            $field['items'][$k]["custom"] = 1;
            $k++;
            $field['items'][$k]["fieldstring"] = "itemtimes_figure";
            $field['items'][$k]["fieldname"] = $this->LgStringSwitch("项目剩余金额");
            $field['items'][$k]["show"] = 1;
            $field['items'][$k]["custom"] = 1;
            $order['itemlist'] = $itemList;
            $order['item_field'] = $field['items'];
        } else {
            $goodList = $this->DataControl->selectClear("
            select oi.refund_pid,se.goods_id,g.goods_cnname,g.goods_pid,se.erpgoods_isreceive,se.beoutorder_pid,se.erpgoods_receivetime,se.order_pid,po.order_paymentprice,pog.ordergoods_buynums,po.order_createtime,se.erpgoods_isrestore,
            (select pos.ordergoods_totalprice from smc_payfee_order_goods as pos where pos.order_pid=se.order_pid and pos.goods_id = g.goods_id  ) as ordergoods_totalprice
            from smc_refund_order_erpgoods as oi
            left join smc_student_erpgoods as se ON  se.erpgoods_id = oi.erpgoods_id 
            left join erp_goods as g ON g.goods_id = se.goods_id 
            left join smc_payfee_order as po ON po.order_pid = se.order_pid
            left join smc_payfee_order_goods as  pog On  pog.order_pid = po.order_pid  and pog.goods_id = g.goods_id
            where oi.refund_pid='{$orderRefund['refund_pid']}'");
            $field = array();
            if ($goodList) {
                $isreceive = $this->LgArraySwitch(array("0" => "未领用", '1' => "已领用", '-1' => "订单作废"));
                $isrestore = $this->LgArraySwitch(array("0" => "未归还", '1' => "已归还"));
                foreach ($goodList as &$value) {
                    $value['order_createtime'] = date("Y-m-d H:i:s", $value['order_createtime']);
                    if ($value['erpgoods_receivetime']) {
                        $value['erpgoods_receivetime'] = date("Y-m-d H:i:s", $value['erpgoods_receivetime']);
                    } else {
                        $value['erpgoods_receivetime'] = '--';
                    }
                    $value['erpgoods_isreceive'] = $isreceive[$value['erpgoods_isreceive']];
                    $value['erpgoods_isrestore'] = $isrestore[$value['erpgoods_isrestore']];
                }

                $k = 0;
                $field['items'][$k]["fieldstring"] = "goods_cnname";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("项目名称");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "goods_pid";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("项目编号");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "ordergoods_buynums";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("购买数量");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "ordergoods_totalprice";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("购买金额");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "order_pid";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("订单编号");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "beoutorder_pid";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("出库编号");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "order_createtime";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("创建时间");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "erpgoods_isreceive";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("是否领用");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $k++;
                $field['items'][$k]["fieldstring"] = "erpgoods_receivetime";
                $field['items'][$k]["fieldname"] = $this->LgStringSwitch("领用时间");
                $field['items'][$k]["show"] = 1;
                $field['items'][$k]["custom"] = 1;
                $order['item_field'] = $field['items'];
                $order['itemlist'] = $goodList;
            } else {
                $order['itemlist'] = array();
                $order['item_field'] = $field;
            }
        }

        //订单跟踪记录
        $sql = "select t.*,st.staffer_cnname,st.staffer_enname from smc_refund_order_tracks as t 
              left join smc_staffer as st on st.staffer_id=t.staffer_id
              where refund_pid='{$orderRefund['refund_pid']}'";

        $tradingList = $this->DataControl->selectClear($sql);
        if ($tradingList) {
            foreach ($tradingList as $key => &$value) {
                $value['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $value['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);
            }
        }
        $order['trading'] = $tradingList;

        //经办信息
        $info = $this->DataControl->selectClear("select s.school_cnname,
                (select t.staffer_cnname from  smc_staffer as t where  t.staffer_id ='{$orderRefund['staffer_id']}') as staffer_cnname,(select t.staffer_enname from  smc_staffer as t where  t.staffer_id ='{$orderRefund['staffer_id']}') as staffer_enname
                from smc_school as  s  where s.school_id ='{$orderRefund['school_id']}' ");

        $info[0]['createtime'] = date("Y-m-d H:i:s", $orderRefund['refund_createtime']);
        $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];
        $order['info'] = $info;
        return $order;
    }

    function getDealOrderOne($trading_pid)
    {
        $order = array();
        $orderDeal = $this->DataControl->selectOne("select * from smc_forward_dealorder  where trading_pid='{$trading_pid}' limit 0,1");

        $deal = array();
        //退款状态0-待生效 1-已生效-1已取消
        $redecue_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));
        $deal['trading_pid'] = $orderDeal['trading_pid'];
        $deal['order_pid'] = $orderDeal['dealorder_pid'];
        $deal['order_status'] = $redecue_status[$orderDeal['dealorder_status']];
        $deal['order_type'] = $this->LgStringSwitch('课程结转订单');
        $deal['order_createtime'] = date("Y-m-d H:i:s", $orderDeal['dealorder_createtime']);
        $deal['dealorder_balanceprice'] = $orderDeal['dealorder_balanceprice'];
        $deal['dealorder_forwardprice'] = $orderDeal['dealorder_forwardprice'];


        $order['deal'] = $deal;
        $order['order'] = $deal;


        //结转的课程信息

        $sql = "
          select dc.course_id,c.course_cnname as item_name, c.course_branch as item_branch,cc.coursetype_cnname as coursetype_branch,dc.dealcourse_time,dc.dealcourse_figure,co.course_cnname as to_course_cnname,co.course_branch as to_course_branch,
          (select c.class_cnname from  smc_class as c,smc_student_study as ss  where ss.class_id =c.class_id and ss.student_id='{$orderDeal['student_id']}' and c.course_id =dc.course_id Limit 0,1 ) as class_cnname,
          (select scl.timelog_finaltimes from smc_student_coursebalance_timelog as  scl where scl.timelog_playclass ='+' and scl.student_id='{$orderDeal['student_id']}' and scl.course_id =dc.course_id order by scl.timelog_time DESC Limit 0,1 ) as timelog_finaltimes
          from  smc_forward_dealorder_course as dc 
          left join smc_course as  c ON  c.course_id =dc.course_id
          left join smc_fee_pricing as  p ON  p.pricing_id =dc.dealcourse_topricing_id
          left join smc_course as  co ON  co.course_id =p.course_id
          left join smc_code_coursetype as cc ON cc.coursetype_id = c.coursetype_id
          left  join  smc_student_coursebalance as sc ON sc.course_id = dc.course_id and sc.student_id='{$orderDeal['student_id']}'
          where dc.dealorder_pid ='{$orderDeal['dealorder_pid']}' ";


        $dealCourse = $this->DataControl->selectClear($sql);

        $order['course'] = $dealCourse;

        if($orderDeal['application_id']>0){
            $sql = "SELECT a.application_type,a.out_class_date,a.back_class_date,a.forward_reason,a.attachment_url
                    from smc_forward_application as a where a.application_id='{$orderDeal['application_id']}'";
            $applicationOne = $this->DataControl->selectOne($sql);

            if($applicationOne){

                $application_type = $this->LgArraySwitch(array('1' => '申请退费', '2' => '申请转班', '3' => '申请暑期长期保留', '4' => '申请拆并班'));
                $applicationOne['application_type_name'] = $application_type[$applicationOne['application_type']];

                $order['application'] = $applicationOne;
            }else{
                $order['application'] = null;
            }
        }else{
            $order['application'] = null;
        }

        $sql = "select t.*,st.staffer_cnname,st.staffer_enname from smc_forward_dealorder_tracks as t left join smc_staffer as st on t.staffer_id=st.staffer_id
              where t.dealorder_pid='{$orderDeal['dealorder_pid']}'";

        $trading = $this->DataControl->selectClear($sql);
        if ($trading) {
            foreach ($trading as $key => $value) {
                $trading[$key]['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $trading[$key]['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);
            }
        }
        $order['trading'] = $trading;
        //经办信息
        $info = $this->DataControl->selectClear("select s.school_cnname,
                (select t.staffer_cnname from  smc_staffer as t where  t.staffer_id ='{$orderDeal['staffer_id']}') as staffer_cnname,(select t.staffer_enname from  smc_staffer as t where  t.staffer_id ='{$orderDeal['staffer_id']}') as staffer_enname
                from smc_school as  s  where s.school_id ='{$orderDeal['school_id']}' ");
        $info[0]['createtime'] = date("Y-m-d H:i:s", $orderDeal['dealorder_createtime']);
        $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];
        $order['info'] = $info;

        return $order;
    }

    function getReduceOrderOne($trading_pid)
    {
        $order = array();
        $orderReduce = $this->DataControl->selectOne("select *  from smc_course_reduceorder as cr  where cr.trading_pid='{$trading_pid}' ");

        $courseOne = $this->DataControl->selectOne("select co.course_id,co.course_cnname,co.course_branch,co.course_inclasstype from smc_course as co where co.course_id='{$orderReduce['course_id']}' limit 0,1 ");
        $reduce = array();
        $redecue_status = $this->LgArraySwitch(array(0 => "申请中", 1 => "通过申请", -1 => '已拒绝', -2 => '已取消'));
        $reduce['trading_pid'] = $orderReduce['trading_pid'];
        $reduce['reduceorder_type'] = $orderReduce['reduceorder_type'];
        $reduce['order_pid'] = $orderReduce['reduceorder_pid'];
        $reduce['order_type'] = $this->LgStringSwitch("扣课订单");
        $reduce['order_status'] = $redecue_status[$orderReduce['reduceorder_status']];
        $reduce['order_createtime'] = date("Y-m-d H:i:s", $orderReduce['reduceorder_createtime']);
        $reduce['reduceorder_figure'] = $orderReduce['reduceorder_figure'];
        $reduce['course_cnname'] = $courseOne['course_cnname'];
        $reduce['course_branch'] = $courseOne['course_branch'];
        $reduce['course_inclasstype'] = $courseOne['course_inclasstype'];

        $order['order'] = $reduce;

        $courseOne['reduceorder_time'] = $orderReduce['reduceorder_time'];
        $courseOne['reduceorder_figure'] = $orderReduce['reduceorder_figure'];
        $courseOne['reduceorder_unitexpend'] = $orderReduce['reduceorder_unitexpend'];
        $courseOne['reduceorder_note'] = $orderReduce['reduceorder_note'];
        $order['course'][] = $courseOne;

        $sql = "select t.*,st.staffer_cnname,st.staffer_enname
              from smc_course_reduceorder_tracks as t
              left join smc_staffer as st on st.staffer_id=t.staffer_id 
              where reduceorder_pid='{$orderReduce['reduceorder_pid']}' ";
        $trading = $this->DataControl->selectClear($sql);
        if ($trading) {
            foreach ($trading as $key => $value) {
                $trading[$key]['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $trading[$key]['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);
            }
        }
        $order['trading'] = $trading;

        $info = $this->DataControl->selectClear("select s.school_cnname,
                (select t.staffer_cnname from  smc_staffer as t where  t.staffer_id ='{$orderReduce['staffer_id']}') as staffer_cnname,(select t.staffer_enname from  smc_staffer as t where  t.staffer_id ='{$orderReduce['staffer_id']}') as staffer_enname
                from smc_school as  s  where s.school_id ='{$orderReduce['school_id']}' ");
        $info[0]['createtime'] = date("Y-m-d H:i:s", $orderReduce['reduceorder_createtime']);
        $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];
        $order['info'] = $info;


        if ($orderReduce['reduceorder_type'] == 0) {
            $field = array();
            $field[0]["fieldstring"] = "course_cnname";
            $field[0]["fieldname"] = $this->LgStringSwitch("课程名称");
            $field[0]["show"] = 1;
            $field[0]["custom"] = 1;

            $field[1]["fieldstring"] = "course_branch";
            $field[1]["fieldname"] = $this->LgStringSwitch("课程编号");
            $field[1]["show"] = 1;
            $field[1]["custom"] = 1;

            $field[2]["fieldstring"] = "reduceorder_time";
            $field[2]["fieldname"] = $this->LgStringSwitch("抵扣课次数量");
            $field[2]["show"] = 1;
            $field[2]["custom"] = 1;

            $field[3]["fieldstring"] = "reduceorder_figure";
            $field[3]["fieldname"] = $this->LgStringSwitch("抵扣金额");
            $field[3]["show"] = 1;
            $field[3]["custom"] = 1;

            $field[4]["fieldstring"] = "reduceorder_unitexpend";
            $field[4]["fieldname"] = $this->LgStringSwitch("抵扣单价");
            $field[4]["show"] = 1;
            $field[4]["custom"] = 1;

            $field[5]["fieldstring"] = "reduceorder_note";
            $field[5]["fieldname"] = $this->LgStringSwitch("备注");
            $field[5]["show"] = 1;
            $field[5]["custom"] = 1;

        } else {

            if ($courseOne['course_inclasstype'] != 1) {
                $field = array();
                $field[0]["fieldstring"] = "course_cnname";
                $field[0]["fieldname"] = $this->LgStringSwitch("课程名称");
                $field[0]["show"] = 1;
                $field[0]["custom"] = 1;

                $field[1]["fieldstring"] = "course_branch";
                $field[1]["fieldname"] = $this->LgStringSwitch("课程编号");
                $field[1]["show"] = 1;
                $field[1]["custom"] = 1;

                $field[2]["fieldstring"] = "reduceorder_figure";
                $field[2]["fieldname"] = $this->LgStringSwitch("扣除课程余额");
                $field[2]["show"] = 1;
                $field[2]["custom"] = 1;
            } else {
                $sql = "select *
              from smc_course_reduceorder_share
              where reduceorder_pid='{$orderReduce['reduceorder_pid']}' order by share_month asc";
                $share = $this->DataControl->selectClear($sql);
                $order['course'] = $share ? $share : array();

                $field = array();
                $field[0]["fieldstring"] = "share_month";
                $field[0]["fieldname"] = $this->LgStringSwitch("扣除月份");
                $field[0]["show"] = 1;
                $field[0]["custom"] = 1;

                $field[1]["fieldstring"] = "share_price";
                $field[1]["fieldname"] = $this->LgStringSwitch("扣除课程余额");
                $field[1]["show"] = 1;
                $field[1]["custom"] = 1;
            }

        }

        $order['field'] = $field;

        return $order;
    }

    function getHourFeeOne($trading_pid)
    {

        $orderHour = $this->DataControl->selectOne("select cr.*,co.course_cnname  from smc_freehour_order as cr
                                                        left join smc_course as co On co.course_id =cr.course_id
                                                        where cr.trading_pid='{$trading_pid}' ");
        $hour = array();
        $hour_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-2' => '审核拒绝'));
        $hour['trading_pid'] = $orderHour['trading_pid'];
        $hour['order_pid'] = $orderHour['order_pid'];
        $hour['order_type'] = $this->LgStringSwitch("赠送课次订单");
        $hour['order_status'] = $hour_status[$orderHour['order_status']];
        $hour['order_createtime'] = date("Y-m-d H:i:s", $orderHour['order_createtime']);
        $order['order'] = $hour;
        //课次赠送信息
        $course = array();
        $classOne = $this->DataControl->selectOne("select class_cnname,class_branch from smc_class where  class_id='{$orderHour['class_id']}' ");
        $course['course_branch'] = $orderHour['course_branch'];
        $course['course_cnname'] = $orderHour['course_cnname'];
        $course['class_cnname'] = $classOne['class_cnname'];
        $course['class_branch'] = $classOne['class_branch'];
        $course['order_alltimes'] = $orderHour['order_alltimes'];
        $order['coursefee'][] = $course;

        //赠送课次详情
        $feeHourList = $this->DataControl->selectClear("
            select  ch.hour_id,hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,fo.hour_lessontimes AS order_lessontimes from smc_freehour_ordertimes as fo
            left join smc_class_hour as  ch On ch.hour_lessontimes = fo.hour_lessontimes and ch.hour_iswarming=0
            where fo.order_pid= '{$orderHour['order_pid']}' and ch.class_id='{$orderHour['class_id']}' and course_id='{$orderHour['course_id']}' ");
        $coursefee = array();
        if ($feeHourList) {
            $temCourse = array();
            foreach ($feeHourList as $key => $value) {
                $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
                $feeHourList[$key]['week_name'] =
                $coursefee['hour_id'] = $value['hour_id'];
                $coursefee['hour_day'] = $value['hour_day'];
                $coursefee['week_name'] = $this->LgStringSwitch('周' . $weekarray[date("w", strtotime($value['hour_day']))]);
                $coursefee['hour_starttime'] = $value['hour_starttime'];
                $coursefee['hour_endtime'] = $value['hour_endtime'];
                $coursefee['hour_lessontimes'] = $value['hour_lessontimes'];
                $coursefee['order_lessontimes'] = $value['order_lessontimes'];
                $temCourse[] = $coursefee;
            }
            $order['lessontimes'] = $temCourse;
        } else {
            $order['lessontimes'] = array();
        }

        $info = $this->DataControl->selectClear("select s.school_cnname,
                (select t.staffer_cnname from  smc_staffer as t where  t.staffer_id ='{$orderHour['staffer_id']}') as staffer_cnname,(select t.staffer_enname from  smc_staffer as t where  t.staffer_id ='{$orderHour['staffer_id']}') as staffer_enname
                from smc_school as  s  where s.school_id ='{$orderHour['school_id']}' ");
        $info[0]['createtime'] = date("Y-m-d H:i:s", $orderHour['order_createtime']);
        $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];
        $order['info'] = $info;

        $sql = "select t.*,st.staffer_cnname,st.staffer_enname 
              from smc_freehour_order_tracks as t 
              left join smc_staffer as st on st.staffer_id=t.staffer_id
              where t.order_pid='{$orderHour['order_pid']}'";
        $tradingList = $this->DataControl->selectClear($sql);
        if ($tradingList) {
            foreach ($tradingList as $key => $value) {
                $tradingList[$key]['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $tradingList[$key]['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);
                $tradingList[$key]['tracks_title'] = $this->LgStringSwitch($value['tracks_title']);
            }
        }
        $order['trading'] = $tradingList;

        return $order;


    }

    function getProtocolList($paramArray)
    {
        $datawhere = " p.company_id = '{$paramArray['company_id']}' AND p.order_pid = '{$paramArray['order_pid']}' AND p.protocol_isdel = '0' ";
        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_pid,
                p.order_pid,
                p.protocol_istable,
                c.course_cnname,
                protocol_createtime,
                p.protocol_nums,
                p.protocol_price,
                p.protocol_issign,
                p.protocol_issign as protocol_issign_name,
                p.protocol_isaudit,
                p.protocol_isaudit AS protocol_isaudit_name,
                p.protocol_isinvoice,
                p.protocol_isinvoice AS protocol_isinvoice_name,
                i.invoice_id,
                i.invoice_pdfurl,
                i.invoice_voucher,
                i.invoice_type,
                i.invoice_status,
	            co.companies_signet
            FROM
                smc_student_protocol AS p
                LEFT JOIN smc_course AS c ON p.course_id = c.course_id
                LEFT JOIN shop_invoice AS i ON i.protocol_id = p.protocol_id 
                left join smc_payfee_order as o on p.order_pid = o.order_pid
	            left join gmc_code_companies as co on co.companies_id = o.companies_id
            WHERE {$datawhere} GROUP BY p.protocol_id";


        $districtList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "未生效", "1" => "已生效"));
        $statuss = $this->LgArraySwitch(array("0" => "未开票", "1" => "已开票"));
        $statusss = $this->LgArraySwitch(array("0" => "否", "1" => "是"));

        $sign = $this->DataControl->getFieldOne("gmc_company", "company_issign", "company_id = '{$paramArray['company_id']}'");

        if ($districtList) {
            foreach ($districtList as &$val) {
                $val['protocol_isaudit_name'] = $status[$val['protocol_isaudit_name']];
                $val['protocol_isinvoice_name'] = $statuss[$val['protocol_isinvoice_name']];
                if ($val['invoice_status'] == '1') {
                    $val['invoice_status_name'] = '已开票';
                } else {
                    $val['invoice_status_name'] = '未开票';
                }
                if ($val['invoice_type'] == '1') {
                    $val['invoice_pdfurl'] = $val['invoice_voucher'];
                }
                if ($sign['company_issign'] == '1') {
                    $val['protocol_issign_name'] = $statusss[$val['protocol_issign_name']];
                }
                $val['invoice_pdfurl'] = $this->str_insert($val['invoice_pdfurl'], 4, "s");

                if($paramArray['company_id'] == '8888'){
                    if($val['protocol_issign'] == '0'){
                        $val['qrcode'] = "https://smcapi.kedingdang.com/Order/protocolPrint?protocol_id={$val['protocol_id']}";//小程序二维码;
                    }
                }else{
                    $val['qrcode'] = "https://smcapi.kedingdang.com/Order/goalActivityshowimg?imgurl=" .
                        base64_encode("https://{$paramArray['company_id']}.scshop.kedingdang.com/MeCenter/agreementDetail?id=" . $val['protocol_id']);//学校二维码
                }
            }
        }

        if ($sign['company_issign'] == '1') {
            $fieldstring = array('protocol_pid', 'course_cnname', 'order_pid', 'protocol_nums', 'protocol_price', 'protocol_isaudit_name', 'protocol_issign_name', 'invoice_status_name');
            $fieldname = array('合同编号', '课程名称', '订单编号', '合同课程数', '合同价格', '是否生效', '是否签字', '是否开票');
            $fieldcustom = array("1", "1", "0", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "0", "1", "1", "1", "1", "1");
        } else {
            $fieldstring = array('protocol_pid', 'course_cnname', 'order_pid', 'protocol_nums', 'protocol_price', 'protocol_isaudit_name', 'invoice_status_name');
            $fieldname = array('合同编号', '课程名称', '订单编号', '合同课程数', '合同价格', '是否生效', '是否开票');
            $fieldcustom = array("1", "1", "0", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "0", "1", "1", "1", "1");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $a = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id", "order_pid = '{$paramArray['order_pid']}' and protocol_isinvoice = '1'");
        if ($a) {
            $isinvoice = '1';
        } else {
            $isinvoice = '0';
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['isinvoice'] = $isinvoice;

        if ($districtList) {
            $result['list'] = $districtList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无合同", 'result' => $result);
        }

        return $res;
    }


    function str_insert($str, $i, $substr)
    {
        for ($j = 0; $j < $i; $j++) {
            $startstr .= $str[$j];
        }
        for ($j = $i; $j < strlen($str); $j++) {
            $laststr .= $str[$j];
        }
        $str = ($startstr . $substr . $laststr);
        return $str;
    }


    function createProtocolAction($paramArray)
    {
        $course = $this->DataControl->selectClear("select course_id,ordercourse_buynums,ordercourse_totalprice from smc_payfee_order_course where order_pid = '{$paramArray['order_pid']}'");
        if ($course) {
            foreach ($course as $item) {
                $coursetype = $this->DataControl->getFieldOne("smc_course", "coursetype_id,coursecat_id", "course_id = '{$item['course_id']}'");
                $agreement = $this->DataControl->getFieldOne("smc_fee_agreement", "agreement_id", "agreement_status = '1' and company_id = '{$paramArray['company_id']}'");

                $a = $this->DataControl->selectOne("select t.treaty_id from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$paramArray['school_id']}'");

                $b = $this->DataControl->selectOne("select t.treaty_id from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and t.treaty_applytype = '0'");

                if (!$a && !$b) {
                    ajax_return(array('error' => '1', 'errortip' => '购买的课程没有协议，无法生成合同！'), $this->companyOne['company_language']);
                }
            }
        }


        $student_id = $this->DataControl->getFieldOne("smc_payfee_order", "student_id,order_paidprice,order_paymentprice", "order_pid = '{$paramArray['order_pid']}'");
        $status = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id", "order_pid = '{$paramArray['order_pid']}' and protocol_isdel = '0'");
        if ($status) {
            ajax_return(array('error' => '1', 'errortip' => '该订单已生成过合同！!'), $this->companyOne['company_language']);
        }
        $type = $this->DataControl->getFieldOne("smc_payfee_order", "order_type,coursecat_id,order_status", "order_pid = '{$paramArray['order_pid']}'");
        if ($type['order_type'] != '0') {
            ajax_return(array('error' => '1', 'errortip' => '只有课程订单能生成合同！'), $this->companyOne['company_language']);
        }
        $a = $this->DataControl->getFieldOne("smc_payfee_order", "order_status", "order_pid = '{$paramArray['order_pid']}'");
        if ($a['order_status'] == '0' || $a['order_status'] == '-1' || $a['order_status'] == '-2') {
            ajax_return(array('error' => '1', 'errortip' => '无效订单不能生成合同'), $this->companyOne['company_language']);
        }

        if ($type['order_status'] == '4') {
            $isaudit = '1';
        } else {
            $isaudit = '0';
        }

        $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and coursecat_id = '{$coursetype['coursecat_id']}'");

        if ($isprocat) {
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and coursecat_id = '{$coursetype['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$paramArray['school_id']}'");
            if (!$protocolOne) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and coursecat_id = '{$coursetype['coursecat_id']}' and t.treaty_applytype = '0'");

                if (!$protocolOne) {

                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$paramArray['school_id']}'");
                    }
                }
            }
        } else {
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$paramArray['school_id']}' and t.coursecat_id = '0'");
            if (!$protocolOne) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$coursetype['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
            }
        }

        if ($protocolOne['treaty_tabletip']) {
            $table = '1';
        } else {
            $table = '0';
        }


//
//        $treatyOne = $this->DataControl->selectOne("
//                SELECT t.treaty_id, t.treaty_tabletip, t.treaty_protocol, t.treaty_refundinfo, t.treaty_buyinfo
//                FROM smc_fee_treaty AS t
//                    LEFT JOIN smc_payfee_order_course AS o ON o.agreement_id = t.agreement_id
//                    INNER JOIN smc_course AS co ON co.course_id = o.course_id and co.coursetype_id = t.coursetype_id
//                WHERE o.order_pid = '{$paramArray['order_pid']}' GROUP BY t.treaty_id limit 0,1");
//
//        if($type['coursecat_id'] == '135'){
//            $treatyOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//        }


        foreach ($course as $item) {
            $course = $this->DataControl->getFieldOne("smc_course", "course_protocolnums,course_classnum,course_inclasstype", "course_id = '{$item['course_id']}'");
            $order = $this->DataControl->getFieldOne("smc_payfee_order_course", "ordercourse_unitprice,ordercourse_totalprice", "order_pid = '{$paramArray['order_pid']}' and course_id = '{$item['course_id']}'");
            if ($course['course_inclasstype'] == '0') {
                if ($course['course_classnum'] == $item['ordercourse_buynums']) {
                    $num = intval($course['course_classnum'] / $course['course_protocolnums']);
                    $nums = $course['course_classnum'] % $course['course_protocolnums'];
                    for ($i = 1; $i <= $course['course_protocolnums']; $i++) {
                        do {
                            $propid = $this->createOrderPid('XY');
                        } while ($this->DataControl->selectOne("select protocol_id from smc_student_protocol where protocol_pid='{$propid}' limit 0,1"));
                        if ($i < $course['course_protocolnums']) {
                            $datas = array();
                            $datas['protocol_pid'] = $propid;
                            $datas['company_id'] = $paramArray['company_id'];
                            $datas['school_id'] = $paramArray['school_id'];
                            $datas['student_id'] = $student_id['student_id'];
                            $datas['order_pid'] = $paramArray['order_pid'];
                            $datas['course_id'] = $item['course_id'];
                            $datas['protocol_istable'] = $table;
                            $datas['protocol_nums'] = $num;
//                            $datas['protocol_isaudit'] = $isaudit;

                            $datas['protocol_price'] = $order['ordercourse_unitprice'] * $num;;
                            $datas['protocol_createtime'] = time();
                            $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                            if ($isaudit == '1') {
                                $paramArray['protocol_id'] = $pid;
                                $this->createtip($paramArray);
                            }
                        } else {
                            $datas = array();
                            $datas['protocol_pid'] = $propid;
                            $datas['company_id'] = $paramArray['company_id'];
                            $datas['school_id'] = $paramArray['school_id'];
                            $datas['student_id'] = $student_id['student_id'];
                            $datas['order_pid'] = $paramArray['order_pid'];
                            $datas['course_id'] = $item['course_id'];
//                            $datas['protocol_isaudit'] = $isaudit;
                            $datas['protocol_istable'] = $table;

                            $datas['protocol_nums'] = $nums + $num;

                            $datas['protocol_price'] = $item['ordercourse_totalprice'] - ($order['ordercourse_unitprice'] * $num * ($course['course_protocolnums'] - 1));
                            $datas['protocol_createtime'] = time();
                            $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                            if ($isaudit == '1') {
                                $paramArray['protocol_id'] = $pid;
                                $this->createtip($paramArray);
                            }
                        }
                    }
                } elseif ($course['course_classnum'] > $item['ordercourse_buynums']) {
                    do {
                        $propid = $this->createOrderPid('XY');
                    } while ($this->DataControl->selectOne("select protocol_id from smc_student_protocol where protocol_pid='{$propid}' limit 0,1"));

                    $num = intval($course['course_classnum'] / $course['course_protocolnums']);

                    if ($course['course_protocolnums'] == '1') {
                        $datas = array();

                        $datas['protocol_pid'] = $propid;
                        $datas['company_id'] = $paramArray['company_id'];
                        $datas['school_id'] = $paramArray['school_id'];
                        $datas['student_id'] = $student_id['student_id'];
                        $datas['order_pid'] = $paramArray['order_pid'];
                        $datas['course_id'] = $item['course_id'];
                        $datas['protocol_nums'] = $item['ordercourse_buynums'];
//                        $datas['protocol_isaudit'] = $isaudit;
                        $datas['protocol_istable'] = $table;

                        $datas['protocol_price'] = $student_id['order_paymentprice'];
                        $datas['protocol_createtime'] = time();
                        $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                        if ($isaudit == '1') {
                            $paramArray['protocol_id'] = $pid;
                            $this->createtip($paramArray);
                        }
                    } else {


                        if ($item['ordercourse_buynums'] <= $num) {
                            $datas = array();
                            $datas['protocol_pid'] = $propid;
                            $datas['company_id'] = $paramArray['company_id'];
                            $datas['school_id'] = $paramArray['school_id'];
                            $datas['student_id'] = $student_id['student_id'];
                            $datas['order_pid'] = $paramArray['order_pid'];
                            $datas['course_id'] = $item['course_id'];
                            $datas['protocol_nums'] = $item['ordercourse_buynums'];
//                            $datas['protocol_isaudit'] = $isaudit;
                            $datas['protocol_istable'] = $table;

                            $datas['protocol_price'] = $item['ordercourse_totalprice'];
                            $datas['protocol_createtime'] = time();
                            $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                            if ($isaudit == '1') {
                                $paramArray['protocol_id'] = $pid;
                                $this->createtip($paramArray);
                            }
                        } else {
                            $a = intval($item['ordercourse_buynums'] / $num);

                            if ($a == $item['ordercourse_buynums'] / $num) {
                                for ($i = 1; $i <= $a; $i++) {
                                    do {
                                        $propid = $this->createOrderPid('XY');
                                    } while ($this->DataControl->selectOne("select protocol_id from smc_student_protocol where protocol_pid='{$propid}' limit 0,1"));
                                    if ($i < $course['course_protocolnums']) {

                                        $datas = array();
                                        $datas['protocol_pid'] = $propid;
                                        $datas['company_id'] = $paramArray['company_id'];
                                        $datas['school_id'] = $paramArray['school_id'];
                                        $datas['student_id'] = $student_id['student_id'];
                                        $datas['order_pid'] = $paramArray['order_pid'];
                                        $datas['course_id'] = $item['course_id'];
                                        $datas['protocol_nums'] = $num;
//                                        $datas['protocol_isaudit'] = $isaudit;
                                        $datas['protocol_istable'] = $table;

                                        $datas['protocol_price'] = $order['ordercourse_unitprice'] * $num;;
                                        $datas['protocol_createtime'] = time();
                                        $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                                        if ($isaudit == '1') {
                                            $paramArray['protocol_id'] = $pid;
                                            $this->createtip($paramArray);
                                        }
                                    } else {
                                        $datas = array();

                                        $datas['protocol_pid'] = $propid;
                                        $datas['company_id'] = $paramArray['company_id'];
                                        $datas['school_id'] = $paramArray['school_id'];
                                        $datas['student_id'] = $student_id['student_id'];
                                        $datas['order_pid'] = $paramArray['order_pid'];
                                        $datas['course_id'] = $item['course_id'];
//                                        $datas['protocol_isaudit'] = $isaudit;
                                        $datas['protocol_istable'] = $table;

                                        $datas['protocol_nums'] = $item['ordercourse_buynums'] - (($a - 1) * $num);
                                        $datas['protocol_price'] = $item['ordercourse_totalprice'] - ($order['ordercourse_unitprice'] * $num * ($a - 1));
                                        $datas['protocol_createtime'] = time();
                                        $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                                        if ($isaudit == '1') {
                                            $paramArray['protocol_id'] = $pid;
                                            $this->createtip($paramArray);
                                        }
                                    }
                                }
                            } else {
                                for ($i = 1; $i <= $a + 1; $i++) {
                                    do {
                                        $propid = $this->createOrderPid('XY');
                                    } while ($this->DataControl->selectOne("select protocol_id from smc_student_protocol where protocol_pid='{$propid}' limit 0,1"));
                                    if ($i <= $a) {

                                        $datas = array();
                                        $datas['protocol_pid'] = $propid;
                                        $datas['company_id'] = $paramArray['company_id'];
                                        $datas['school_id'] = $paramArray['school_id'];
                                        $datas['student_id'] = $student_id['student_id'];
                                        $datas['order_pid'] = $paramArray['order_pid'];
                                        $datas['course_id'] = $item['course_id'];
                                        $datas['protocol_nums'] = $num;
//                                        $datas['protocol_isaudit'] = $isaudit;
                                        $datas['protocol_istable'] = $table;

                                        $datas['protocol_price'] = $order['ordercourse_unitprice'] * $num;;
                                        $datas['protocol_createtime'] = time();
                                        $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                                        if ($isaudit == '1') {
                                            $paramArray['protocol_id'] = $pid;
                                            $this->createtip($paramArray);
                                        }
                                    } else {

                                        $datas = array();
                                        $datas['protocol_pid'] = $propid;
                                        $datas['company_id'] = $paramArray['company_id'];
                                        $datas['school_id'] = $paramArray['school_id'];
                                        $datas['student_id'] = $student_id['student_id'];
                                        $datas['order_pid'] = $paramArray['order_pid'];
                                        $datas['course_id'] = $item['course_id'];
//                                        $datas['protocol_isaudit'] = $isaudit;
                                        $datas['protocol_istable'] = $table;

                                        $datas['protocol_nums'] = $item['ordercourse_buynums'] - ($a * $num);
                                        $datas['protocol_price'] = $item['ordercourse_totalprice'] - ($order['ordercourse_unitprice'] * $num * $a);
                                        $datas['protocol_createtime'] = time();
                                        $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                                        if ($isaudit == '1') {
                                            $paramArray['protocol_id'] = $pid;
                                            $this->createtip($paramArray);
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
            } else {
                do {
                    $propid = $this->createOrderPid('XY');
                } while ($this->DataControl->selectOne("select protocol_id from smc_student_protocol where protocol_pid='{$propid}' limit 0,1"));

                $datas = array();
                $datas['protocol_pid'] = $propid;
                $datas['company_id'] = $paramArray['company_id'];
                $datas['school_id'] = $paramArray['school_id'];
                $datas['student_id'] = $student_id['student_id'];
                $datas['order_pid'] = $paramArray['order_pid'];
                $datas['course_id'] = $item['course_id'];
//                $datas['protocol_isaudit'] = $isaudit;
                $datas['protocol_istable'] = $table;

                $datas['protocol_nums'] = $item['ordercourse_buynums'];
                $datas['protocol_price'] = $item['ordercourse_totalprice'];
                $datas['protocol_createtime'] = time();
                $pid = $this->DataControl->insertData("smc_student_protocol", $datas);
                if ($isaudit == '1') {
                    $paramArray['protocol_id'] = $pid;
                    $this->createtip($paramArray);
                }
            }
            //购买全部课时

        }

        $a = $this->DataControl->selectOne("select count(protocol_id) as num from smc_student_protocol where order_pid = '{$paramArray['order_pid']}' and protocol_isdel = '0' limit 0,1");

        $this->orderTracks($this->LgStringSwitch('生成合同'), $this->LgStringSwitch('订单共生成' . $a['num'] . '份合同，请及时查看'));

        $res = array('error' => '0', 'errortip' => '创建合同成功');
        return $res;

    }

    //编辑合同
    function updateProtocolAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id,protocol_pid,order_pid", "protocol_id = '{$paramArray['protocol_id']}'");
        $allprice = $this->DataControl->getFieldOne("smc_payfee_order", "order_paymentprice", "order_pid = '{$stafferOne['order_pid']}'");
        $allnum = $this->DataControl->selectOne("select sum(ordercourse_buynums) as ordercourse_buynums from smc_payfee_order_course where order_pid = '{$stafferOne['order_pid']}'");
        if ($stafferOne) {
            $data = array();
            $data['protocol_price'] = $paramArray['protocol_price'];
            $data['protocol_nums'] = $paramArray['protocol_nums'];

            $rprice = $this->DataControl->selectOne("select sum(protocol_price) as price from smc_student_protocol where order_pid = '{$stafferOne['order_pid']}' and protocol_id <> '{$stafferOne['protocol_id']}' and protocol_isdel = '0'");
            $nprice = $paramArray['protocol_price'] + $rprice['price'];

            if ($nprice > $allprice['order_paymentprice']) {
                ajax_return(array('error' => '1', 'errortip' => '合同总金额大于订单实际金额：' . $allprice['order_paymentprice'] . ',不可编辑！'), $this->companyOne['company_language']);

            }

            $rnum = $this->DataControl->selectOne("select sum(protocol_nums) as num from smc_student_protocol where order_pid = '{$stafferOne['order_pid']}' and protocol_id <> '{$stafferOne['protocol_id']}' and protocol_isdel = '0'");
            $nnum = $paramArray['protocol_nums'] + $rnum['num'];

            if ($nnum > $allnum['ordercourse_buynums']) {
                ajax_return(array('error' => '1', 'errortip' => '合同课程数大于订单购买课程数：' . $allnum['ordercourse_buynums'] . ',不可编辑！'), $this->companyOne['company_language']);

            }
            $field = array();
            $field['protocol_nums'] = $this->LgStringSwitch("合同数量");
            $field['protocol_price'] = $this->LgStringSwitch("合同金额");

            if ($this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$paramArray['protocol_id']}'", $data)) {

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;

                $this->orderTracks($this->LgStringSwitch('编辑合同'), $this->LgStringSwitch('编辑合同，合同编号' . $stafferOne['protocol_pid']));

                $res = array('error' => '0', 'errortip' => "修改合同成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '修改合同失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }


        return $res;
    }

    //审核合同
    function isProtocolAction($paramArray)
    {

        $policyOne = $this->DataControl->getFieldOne("smc_student_protocol", "protocol_id,protocol_pid,order_pid,protocol_price,course_id", "protocol_id = '{$paramArray['protocol_id']}'");
        $cid = $this->DataControl->getFieldOne("smc_course", "coursecat_id,coursetype_id", "course_id = '{$policyOne['course_id']}'");
        $aid = $this->DataControl->getFieldOne("smc_payfee_order_course", "agreement_id", "order_pid = '{$policyOne['order_pid']}'");
        if ($policyOne) {


//            $a = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,treaty_refundinfo,treaty_buyinfo,treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$paramArray['school_id']}'");
//
//            $b = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,treaty_refundinfo,treaty_buyinfo,treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and t.treaty_applytype = '0'");
//

            $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and coursecat_id = '{$cid['coursecat_id']}'");
            if ($isprocat) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and coursecat_id = '{$cid['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$paramArray['school_id']}'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and coursecat_id = '{$cid['coursecat_id']}' and t.treaty_applytype = '0'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                    }
                }
            } else {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$paramArray['school_id']}'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$aid['agreement_id']}' and coursetype_id = '{$cid['coursetype_id']}' and t.treaty_applytype = '0'");
                }
            }


            $data = array();

            $data['protocol_isaudit'] = 1;
            $data['treaty_id'] = $protocolOne['treaty_id'];
//            $data['treaty_refundinfo'] = $protocolOne['treaty_refundinfo'];
//            $data['treaty_buyinfo'] = $protocolOne['treaty_buyinfo'];

            $tip = $this->gettip($paramArray);

//            if ($a) {
//                $data['protocol_isaudit'] = 1;
//                $data['treaty_id'] = $a['treaty_id'];
//                $data['treaty_protocol'] = $a['treaty_protocol'];
//                $data['treaty_refundinfo'] = $a['treaty_refundinfo'];
//                $data['treaty_buyinfo'] = $a['treaty_buyinfo'];
//            }else{
//                $data['protocol_isaudit'] = 1;
//                $data['treaty_id'] = $b['treaty_id'];
//                $data['treaty_protocol'] = $b['treaty_protocol'];
//                $data['treaty_refundinfo'] = $b['treaty_refundinfo'];
//                $data['treaty_buyinfo'] = $b['treaty_buyinfo'];
//            }

//            if($cid['coursecat_id'] == '135'){
//                $treatyOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//                $data['treaty_id'] = $treatyOne['treaty_id'];
//                $data['treaty_protocol'] = $treatyOne['treaty_protocol'];
//            }

            $data['treaty_tabletip'] = $tip['treaty_tabletip'];
            $data['treaty_protocol'] = $tip['treaty_protocol'];

            $field = array();
            $field['protocol_isaudit'] = $this->LgStringSwitch("是否启用");

            $paidprice = $this->DataControl->getFieldOne("smc_payfee_order", "order_paidprice", "order_pid = '{$policyOne['order_pid']}'");
            $rprice = $this->DataControl->selectOne("select sum(protocol_price) as price from smc_student_protocol where order_pid = '{$policyOne['order_pid']}' and protocol_id <> '{$paramArray['protocol_id']}' and protocol_isaudit = '1'  and protocol_isdel = '0'");

            $nprice = $policyOne['protocol_price'] + $rprice['price'];

            if ($nprice > $paidprice['order_paidprice']) {
//                ajax_return(array('error' => '1', 'errortip' => '生效合同总金额大于实际支付金额：' . $paidprice['order_paidprice'] . '！'), $this->companyOne['company_language']);

            }

            if ($this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$paramArray['protocol_id']}'", $data)) {

                $this->orderTracks($this->LgStringSwitch('生效合同'), $this->LgStringSwitch('生效合同成功，合同编号' . $policyOne['protocol_pid']));

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;

                $res = array('error' => '0', 'errortip' => "审核成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '审核失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }


        return $res;
    }

    function createtip($paramArray)
    {
        $tip = $this->gettip($paramArray);

        $data = array();
        $data['treaty_tabletip'] = $tip['treaty_tabletip'];
        $data['treaty_protocol'] = $tip['treaty_protocol'];
        $data['treaty_id'] = $tip['treaty_id'];
        if ($data['treaty_tabletip']) {
            $data['protocol_istable'] = '1';
        }

        $field = array();
        $field['protocol_isaudit'] = $this->LgStringSwitch("是否启用");

        $this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$paramArray['protocol_id']}'", $data);

        return true;
    }

    function gettip($request)
    {
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$request['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");
        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject", "companies_id", "school_id = '{$request['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'], strripos($companies['companies_permitstday'], "至") + 3);
        $PartyA['companies_licensestday'] = substr($companies['companies_licensestday'], strripos($companies['companies_licensestday'], "至") + 3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if ($school['school_phone']) {
            $PartyA['school_phone'] = $school['school_phone'];
        } else {
            $PartyA['school_phone'] = '--';
        }
        if ($companies['companies_liaison']) {
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        } else {
            $PartyA['school_liaison'] = '--';
        }
        if ($companies['companies_examine']) {
            $PartyA['school_examine'] = $companies['companies_examine'];
        } else {
            $PartyA['school_examine'] = '--';
        }
        if ($companies['companies_register']) {
            $PartyA['school_register'] = $companies['companies_register'];
        } else {
            $PartyA['school_register'] = '--';
        }
        if ($companies['companies_permitbranch']) {
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        } else {
            $PartyA['school_permitbranch'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_permitstday']) {
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        } else {
            $PartyA['school_permitstday'] = '--';
        }
        if ($companies['companies_licensestday']) {
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        } else {
            $PartyA['school_licensestday'] = '--';
        }
        if ($companies['companies_society']) {
            $PartyA['school_society'] = $companies['companies_society'];
        } else {
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
        $PartyB['phone'] = $parenter['parenter_mobile'];
        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id,class_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

        $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        if ($isprocat) {
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
            if (!$protocolOne) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                if (!$protocolOne) {
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                    if (!$protocolOne) {
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$request['school_id']}'");
                    }
                }
            }
        } else {
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}' and t.coursecat_id = '0'");
            if (!$protocolOne) {
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
            }
        }

        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $order['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];

        }

        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';


        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.changelog_day as hour_day
FROM
	smc_student_changelog AS h
WHERE
	h.student_id = '{$protocol['student_id']}' 
	AND h.class_id = '{$pricing_id['class_id']}'
	AND h.stuchange_code = 'A02'");
        $date = $startday['changelog_day'];

        $endday['hour_day'] = date('Y-m-d',strtotime("$date +90 day"));



        if ($pricing_id['class_id'] == '0') {
            $endday['hour_day'] = '';
            $startday['hour_day'] = '';
        }
        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'], $treatyArray);
        }

        if ($protocolOne) {
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['hour_day'];
            $treatyArray['endday'] = $endday['hour_day'];
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'], $treatyArray);
        }


        return $protocolOne;
    }

    function test(){

//        $a = $this->DataControl->selectClear("SELECT
//	t.*,pr.protocol_sign
//FROM
//	temp_protrol as t
//	left join smc_student as s on t.学生编号 = s.student_branch
//	left join smc_student_protocol as pr on pr.student_id = s.student_id
//	GROUP BY s.student_id");
//
//        foreach($a as &$val){
//            $data = array();
//            $data['sign'] = $val['protocol_sign'];
//            $this->DataControl->updateData("temp_protrol","学生编号 = '{$val['学生编号']}'",$data);
//        }
//die;
        $a = $this->DataControl->selectClear("select * from temp_protrol as p");

//        foreach ($a as &$val) {
//            $cn = $this->DataControl->selectOne("select course_cnname from smc_course where course_branch = '{$val['课程英文名']}'");
//            $data = array();
//            $data['课程中文名'] = $cn['course_cnname'];
//            $this->DataControl->updateData("temp_protrol", "交易编号 = '{$val['交易编号']}'", $data);
//        }
//        die;


        $treaty = '<p style="text-indent: 320px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 21px;">合同编号：</span>
</p><p style="text-align: center; line-height: 40px;"><span style="font-family: 方正小标宋简体; font-size: 29px;">&nbsp;</span>
</p><p style="text-align: center;"><span style="font-family: 方正小标宋简体; font-size: 32px;">中小学生校外培训服务合同示范文本（</span><span
            style="font-family: 方正小标宋简体; font-size: 32px;">2022</span><span
            style="font-family: 方正小标宋简体; font-size: 32px;">年上海版）</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 楷体_GB2312; font-size: 21px;">&nbsp;</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 楷体_GB2312; font-size: 20px;">甲方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">（提供培训方）：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#school_cname#</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">机构名称（与民非登记证/营业执照或办学许可证一致）：</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#school_cname#</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">办学地址：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#address#</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">审批机关：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">上海市浦东新区教育局</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">登记注册机关：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">上海市浦东新区民政局</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">办学许可证编号：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">教民131011571000288号</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">办学许可证有效期：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">2023年12月31日</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">线上机构ICP备案号：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;"></span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">统一社会信用代码：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">52310115779320699X</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">民非登记证/营业执照有效期：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">2023年12月31日</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">联系人：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">--</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;联系电话：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">021-59991137</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 楷体_GB2312; font-size: 20px;">乙方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">（接受培训方监护人）：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">M</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">学员姓名：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#student_cnname#</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;性别：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#student_sex#</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">出生日期：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#birthday#</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">身份证件类型及号码：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;"></span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">就读学校：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">就读年级：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">联系电话：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#phone#</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">监护人姓名：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">M</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">与学员关系：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;"></span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">联系电话：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;"></span>#phone#</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">联系地址：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">--</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">身份证件类型及号码：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">根据《中华人民共和国民法典》《中华人民共和国教育法》《中华人民共和国民办教育促进法》《中华人民共和国民办教育促进法实施条例》《中华人民共和国未成年人保护法》等有关法律、法规的规定，甲乙双方遵循平等、自愿、公平、诚实、守信的原则，遵循教育规律和青少年健康成长规律，经协商一致，签订本合同。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第一条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">&nbsp;培训服务</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">课程类型：</span><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">义务教育阶段学科类（含语言类）培训&nbsp;□义务教育阶段非学科类培训 □普通高中学生学科类（含语言类）培训 □普通高中学生非学科类培训 □学龄前儿童非学科类培训</span>
</p><p style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（一）培训项目</span></strong>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">课程名称：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#course_cnname#</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;班级编号：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">#course_branch#</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">课程顾问（经办人）：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">高梦晴</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">总课时数（节）：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">52</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">每次培训课时（节）：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">4</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">开课日期：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span>#date#</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">预计结课日期：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span></p><p
        style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（二）培训要求</span></strong>
</p><p style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">1.培训形式：</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">线下培训&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">线上培训</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">2. 培训方式：</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">一对一（或一对</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">）面授&nbsp;&nbsp;&nbsp;</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">大班额面授课（标准：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">人--</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">人）</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">小班额面授课（班级限额≤</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">25</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">人）</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">其他方式：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">最低开班人数</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">人，低于此人数可不开班；</span><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">本班开班不受最低人数限制</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">3.是否指定授课教学人员：</span><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">否&nbsp;&nbsp;☐是（指定教学人员姓名：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">，指定教学人员未经乙方书面同意不得更换）；是否具备相应的教师资格或资质&nbsp;&nbsp;</span><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">有&nbsp;&nbsp;☐没有</span></p><p
        style="margin-left: 28px; text-indent: 10px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">4.实际授课地点（线上培训机构无需填写）：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">/</span></span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="margin-left: 28px; text-indent: 10px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">5.学员接送方式（线上培训机构无需填写）：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">家长自行接送</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第二条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">培训收费</span>
</p><p style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（一）收费标准（人民币）</span></strong>
</p><p style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训费用合计：（大写）</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;"></span>#bigprice#元整</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">（小写）</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">¥#courseprice#</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元，其中：</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">课时</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">费：共计</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">¥#courseprice#</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元（</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;"></span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元/课次）</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训资料费：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训资料包括：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">其他费用：</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">名称：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">金额：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元，收费依据：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">名称：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">金额：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元，收费依据：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">名称：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">金额：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元，收费依据：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（二）收费方式（人民币）</span></strong>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">经甲乙双方协商，甲方采取以下第</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">1</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">种方式收费：</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">1.在培训服务开始前付费（预收费），并采用第二条第（三）（四）款约定的预收费安全保障机制。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">2.在培训服务结束后收费（后收费）。培训结束后</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">天内收取培训费用。</span></p><p
        style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（三）预收费安全保障机制</span></strong>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">甲乙双方共同委托（商业银行）</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">招商银行</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">执行银行定期划扣机制，约定由商业银行按课次将预收费从预收费专用银行账户划扣至培训机构自有资金银行账户。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">1.甲方预收费专用账户（预收费须全额存入）</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">开户银行：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">招商银行古北支行</span></span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">户名：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">上海圆桌进修学院</span></span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">账号：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">121912518410603</span></span>
</p><p style="text-indent: 10px; line-height: 40px;"><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">年</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">月</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">日</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">之前乙方支付培训费用的[ &nbsp;]%，计</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">；</span></p><p
        style="text-indent: 10px; line-height: 40px;"><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">年</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">月</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">日</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">之前乙方支付培训费用的[ &nbsp;]%，计</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">；</span></p><p
        style="text-indent: 10px; line-height: 40px;"><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">年</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">月</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">日</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">之前乙方支付剩余[ &nbsp;]%，计</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">；</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">2.乙方个人银行账户（可选措施，账户资金不属于校外培训机构所有）</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">开户银行：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">户名：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">账号：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（四）定期划扣时间、金额、账户</span></strong>
</p><h2 style="text-indent: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">1.定期划扣时间：对应培训服务开始前的</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">个工作日（不超过5个工作日）。</span></h2><h2 style="text-indent: 40px;">
    <span style="font-family: 仿宋_GB2312; font-size: 20px;">2.定期划扣金额：</span>
    
    <span style="font-family: 仿宋_GB2312; font-size: 20px; text-decoration-line: underline;">#oneprice#</span>
    
   
 

            
            
            
            <span
            style="font-family: 仿宋_GB2312; font-size: 20px;">元
            
            （1课次费用）。实施每一笔资金划扣，均应当取得乙方确认。</span></h2><h2
        style="text-indent: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">3.甲方自有资金银行账户信息</span></h2><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">开户银行：</span><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">招商银行古北支行</span></span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">户名：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">上海圆桌进修学院</span></span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">账号：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">121912518410802</span></span>
</p><p style="text-indent: 35px; line-height: 40px;"><strong><span style="font-family: 楷体_GB2312; font-size: 20px;">（五）收费渠道</span></strong>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方采取</span><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">银行卡&nbsp;&nbsp;</span><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">其他方式</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">收取</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训费用。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第三条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">甲方的权利和义务</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（一）甲方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">有权按照</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">国家有关政策规定和</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">合同约定收取</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">费用</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">。甲方收取培训费用后应当及时向乙方提供以培训机构名义开具的正规发票等消费凭证（按照国家有关政策要求，甲方不得一次性向</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">乙方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">收取或变相收取时间跨度超过</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">3个月的费用</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">；按课时收费的，每科不得一次性收取超过</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">60课时的费用</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">且计划培训时间跨度不超过</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">3个月）</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">。</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（二）甲方应当向乙方明示培训机构有效证明文件、收费项目、收费标准、收退费办法、培训范围、培训时间、教学人员资格和服务承诺等内容，</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">公开透明</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训，</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">接受社会监督</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">，</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">不得在公示的项目和标准外向</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">乙方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">收取其他费用。</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（三）甲方可以依照相关法律法规制定适合其机构自身的培训管理制度并在甲方培训场所醒目位置进行公示，甲方有权要求乙方遵照执行，以确保培训活动顺利进行。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（四）甲方开设培训项目须符合国家及培训场所所在地有关规定。甲方须选用与其培训项目及培训计划相匹配的培训材料，培训材料应当符合《校外培训机构培训材料管理办法（试行）》和本市有关规定。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（五）甲方保证，按照国家有关政策要求，配备与培训内容及</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">规模</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">相适应的培训场所和设施设备，配备充足的教学人员、教研人员、培训管理人员、安全管理人员、助教、带班人员等辅助人员。同时，根据《校外培训机构从业人员管理办法（试行）》规定，加强对所聘用人员的管理，确保不出现打骂、体罚、猥亵、虐待等损害学员身心健康或合法权益的行为。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（六）甲方应做好消防、抗震、食品、公共卫生等安全管理，配备安全技术防范系统，建立健全安全管理制度和应急预警处理机制，防范各类安全责任事故发生。每次培训课程结束后，甲方应确保学员被乙方安全接走，双方另有约定的除外。甲方如使用校车接送培训学员，须按《校车安全管理条例》管理，审批时须提供校车使用许可。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（七）甲方若改变培训方式，须双方协商一致。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（八）甲方应当保护乙方个人信息，确保在收集、存储、使用、加工、公开等个人信息处理活动中严格遵守《中华人民共和国个人信息保护法》《中华人民共和国未成年人保护法》的规定。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（九）未经乙方书面同意，甲方不得将本合同约定的培训服务转让给第三方，不得擅自将学员转交给第三方机构进行培训。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（十）甲方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">应当设置处理合同</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">和</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">服务争议的</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">内设</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">部门</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">或者专员</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">，</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方的</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">客服电话为：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">4000-920-999</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第四条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">乙方的权利和义务</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（一）乙方有按照本合同的约定接受甲方培训服务的权利。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（二）乙方对培训过程以及培训人员的从业背景和执教信息享有知情权。乙方可以通过公开课、学习报告等适当方式了解学员的学习状况，甲方应当为乙方提供方便，接受乙方监督。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（三）乙方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">应当按时</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">足额向</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">支付</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训费用。</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">如甲方采用银行定期划扣等方式实施预收费安全保障，乙方应在规定的时间内对甲方授课完成和资金拨付予以确认或提出异议；超过规定时限未确认或提出异议的，视为确认同意。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（四）乙方及学员应当自觉遵守甲方的各项培训管理制度和课堂纪律，不得妨碍其他学员的正常学习活动。乙方应当自觉遵守培训场所的各种安全规定，不从事危害自身或者他人人身、财产安全的不当行为。培训期间如因乙方或学员的原因造成甲方及甲方工作人员或他人人身、财产损害的，乙方应根据其过错依法承担相应的损害赔偿责任。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（五）乙方及学员应当尊重甲方的知识产权，不得擅自对培训过程进行录音、录像。对于甲方拥有知识产权的培训材料、课件或者课程视频，乙方及学员除在正常学习过程中合理使用外，不得私自复制、散发、销售，不得通过互联网进行分享</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">、</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">扩散和传播。</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（六）未经甲方书面同意，乙方不得擅自将本合同课程转让给第三方，或者将听课凭证转让、出借给他人使用，否则甲方有权拒绝提供培训服务。</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（七）如学员身体健康状况有特殊情形不再适合参与培训的，乙方应及时书面通知甲方，甲乙双方一致同意按如下方式处理（单选）：</span>
</p><p style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">按照实际消耗课时结算培训费用</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">调整</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训时间或内容</span></p><p
        style="text-indent: 35px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">其他</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第五条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">培训退费</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（一）乙方在培训班正式开班前[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">1</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]天或开班后[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]</span><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">天</span><span
            style="font-family: &quot;MS Mincho&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">课时前提出退学的，有权要求全额退费。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（二）由于乙方的原因申请提前退学的，双方同意按照如下方式办理退费。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">退还乙方未消耗课时所对应的培训费余额。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">参加课程培训未达[ &nbsp;&nbsp;]%者，退还乙方未消耗课时所对应的培训费余额；参加课程培训超过[ &nbsp;&nbsp;]%者，退还乙方未消耗课时所对应培训费余额的[ &nbsp;&nbsp;]%。</span>
</p><h2 style="text-indent: 40px;"><span style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲乙双方共同委托（商业银行）执行银行定期划扣机制的，乙方提出退学的，乙方应当告知商业银行停止将预收费从预收费专用账户划扣至培训机构自有资金账户。甲乙双方协商确认退费金额，并通知商业银行按协商结果处理。</span>
</h2><p style="text-indent: 43px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">其</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">他方式：</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（三）甲乙双方共同委托（商业银行）执行银行定期划扣机制的，甲方因自身原因，无法继续提供培训服务的，甲方应及时告知商业银行退还预收费专用账户中未消耗课时所对应的培训费余额。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（四）在办理退费时，对于已发放给乙方的培训资料的费用、已转付给第三方并无法索回的代收代支费用以及已向银行（第三方）支付的合理手续费用等，由甲方出示相关证明材料后，经协商，由乙方承担。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（五）乙方所报班次低于最低开班人数不能开班的，甲方应退还乙方已缴付的全部费用。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（六）甲方应在收到乙方退费申请后</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">20</span></span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">（≤20）个工作日内，将相应退费款项支付给乙方。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（七）退费方式：按乙方缴费原路径或双方协商一致路径退回。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第六条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">违约责任</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（一）甲方未达到合同约定的场所、教师等培训条件的，或甲方未经乙方书面同意，擅自变更培训方式或培训教师的，或甲方实施预收费且不执行预收费安全保障机制的，乙方有权要求解除合同，要求甲方退还剩余培训费并支付剩余培训费</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]%金额的违约金</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（二）由于甲方的原因，包括但不限于甲方办学许可证过期，被吊销办学许可证、营业执照（或事业单位法人证书、民办非企业单位登记证书），被责令停业整顿、撤销登记等原因，无法继续向乙方提供培训服务的，乙方有权要求解除合同，</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">要求甲方退还剩余</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">费并支付剩余</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">培训</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">费</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]%金额的违约金。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（三）甲方招生简章或者宣传材料中对培训师资和效果等所作的说明和允诺具体确定，并对培训合同的订立以及课程价格的确定有重大影响的，应当视为要约。相关说明和允诺即使未载入本合同，亦应当视为合同内容，甲方所提供服务与上述相关说明和允诺不相符的，乙方有权要求解除合同，要求甲方退还剩余培训费并支付剩余培训费[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]%金额的违约金。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（四）未经乙方书面同意，甲方擅自将本合同约定的服务转给第三方或将学员转交给第三方机构进行培训的，乙方有权要求解除合同，要求甲方退还剩余培训费并支付剩余培训费[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]%金额的违约金。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（五）因甲方违约，双方就退费事宜书面达成一致后，</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方应于</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">20</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">（≤20）个工作日内将各项相关费用支付给乙方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">，每逾期一日应按逾期金额[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]%的标准（不超过万分之六点五）向乙方支付违约金。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（六）乙方逾期未支付培训费用的，甲方有权中止培训服务，经书面催告后仍不支付的，甲方有权终止培训服务，乙方须支付实际已培训天数的课时费，每逾期一日应按逾期金额[&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">0</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;]%的标准（不超过万分之六点五）向甲方支付违约金。</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（七）由于乙方的原因，无法继续接受培训服务的，甲方不承担违约责任。</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（八）因战争、自然灾害、传染性疾病等不可抗力致使本合同无法继续履行的，双方互不承担违约责任，受不可抗力影响的一方应及时书面通知对方，双方一致同意按如下方式处理（单选）：</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">按照实际消耗课时结算培训费用</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">调整培训时间或内容</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">其他</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: 黑体; font-size: 20px;">第七条&nbsp;</span><span style="font-family: 黑体; font-size: 20px;">争议处理</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">本合同在履行过程中发生争议，双方可协商解决，协商不成的，一方可以向具有调解职能的组织申请调解，仍无法解决的，双方一致同意按如下方式处理（单选）：</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Segoe UI Symbol&quot;; font-size: 20px;">☐</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">依法向</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">仲裁委员会申请仲裁</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span
            style="font-family: &quot;Wingdings 2&quot;; font-size: 20px;">R</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">依法向人民法院提起诉讼</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 黑体; font-size: 20px;">第八条&nbsp;&nbsp;其他约定</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">本合同未尽事宜，由下列条款进行约定。</span>
</p><p style="margin-left: 60px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">1.</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; letter-spacing: 0px; font-size: 20px;">乙方申请退费时须同时退还甲方有偿赠送的未拆封的培训资料及礼品，如已拆封使用致使无法退还的，则甲方可按市场价扣除相应费用。</span></span>
</p><p style="margin-left: 60px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">2</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">.</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; letter-spacing: 0px; font-size: 20px;">甲方如有赠送课时给乙方的，在乙方申请退费时赠送课时均不计入退费范围，且所有赠送课时视为乙方自动放弃。</span></span>
</p><p style="margin-left: 60px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">3</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">.</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; letter-spacing: 0px; font-size: 20px;">乙方申请退费时若已收到开具的纸质发票的，则需退还纸质发票，如无法退还纸质发票原件的，甲方须从退款中扣除发票票面金额的3%，以支付当地发票税赋（电子发票不在此限）。</span></span>
</p><p style="margin-left: 60px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">4</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">.</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; letter-spacing: 0px; font-size: 20px;">乙方在开班后提出申请退学退费的，甲方须退还乙方剩余课时的学费，但有权扣除乙方缴费金额5%的提前退学手续费。</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
</p><p><span style="font-family: &quot;Times New Roman&quot;; font-size: 13px;">&nbsp;</span></p><p
        style="text-indent: 40px; line-height: 40px;"><span style="font-family: 黑体; font-size: 20px;">第九条&nbsp;&nbsp;生效方式</span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">本合同自甲方盖章乙方签字或双方采用合法有效的电子签名方式签署之日起生效。</span>
</p><p style="margin-left: 20px; text-indent: 20px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">合同正本连同补充条款共</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">12</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">页，一式</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">二</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">份，甲乙双方各执</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">一</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">份，各份具有同等法律效力。</span></p><p
        style="text-indent: 20px; line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">乙方：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="line-height: 40px;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">（盖章）</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">（接受培训方监护人签字）</span></p><p style="line-height: 40px;"><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">甲方代表（经办人签字）：</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span>
</p><p style="text-indent: 40px; line-height: 40px;"><span style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">年</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">月</span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="text-decoration-line: underline;"><span
                style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">日</span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">年</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">月</span><span
            style="text-decoration-line: underline;"><span style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">日</span></p><p><span
            style="font-family: 仿宋_GB2312; font-size: 20px;">&nbsp;</span></p><p><br/></p><p><br/></p>';

        foreach($a as &$val){
            $treatyArray = array();
            $treatyArray['school_cname'] = $val['教学点名称'];
            $treatyArray['student_cnname'] = $val['学生姓名'];
            $treatyArray['student_sex'] = $val['性别'];
            $treatyArray['course_cnname'] = $val['课程中文名'];
            $treatyArray['course_branch'] = $val['课程英文名'];
            $treatyArray['courseprice'] = $val['合同金额'];
            $treatyArray['address'] = $val['办学地址'];
            $treatyArray['birthday'] = $val['出生日期'];
            $treatyArray['phone'] = $val['联系电话'];
            $treatyArray['date'] = $val['开课时间'];
            $treatyArray['oneprice'] = $val['单课次金额'];
            $treatyArray['bigprice'] = $this->convert_2_cn($val['合同金额']);
            $result = $this->contractTable($treaty, $treatyArray);

            $data = array();
            $data['treaty_tabletip'] = $result;
            $data['sign'] = $val['sign'];
            $data['img'] = $val['img'];
            $this->DataControl->insertData("temp_result", $data);
        }


    }

    function convertAmountToCn($amount, $type = 1)
    {

        if ($amount == 0) {

            return "零元整";

        }

        if (strlen($amount) > 12) {

            return "不支持万亿及更高金额";

        }


        // 预定义中文转换的数组

        $digital = array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');

        // 预定义单位转换的数组

        $position = array('仟', '佰', '拾', '亿', '仟', '佰', '拾', '万', '仟', '佰', '拾', '元');


        // 将金额的数值字符串拆分成数组

        $amountArr = explode('.', $amount);


        // 将整数位的数值字符串拆分成数组

        $integerArr = str_split($amountArr[0], 1);

        // 将整数部分替换成大写汉字

        $result = '';

        $integerArrLength = count($integerArr);

        $positionLength = count($position);

        for ($i = 0; $i < $integerArrLength; $i++) {

            $result = $result . $digital[$integerArr[$i]] . $position[$positionLength - $integerArrLength + $i];

        }


        $result = $result . '整';
        return $result;

    }

    function convert_2_cn($num)
    {
        $convert_cn = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $repair_number = array('零仟零佰零拾零', '万万', '零仟', '零佰', '零拾');
        $unit_cn = array("拾", "佰", "仟", "万", "亿");
        $exp_cn = array("", "万", "亿");
        $max_len = 12;
        $len = strlen($num);
        if ($len > $max_len) {
            return 'outnumber';
        }
        $num = str_pad($num, 12, '-', STR_PAD_LEFT);
        $exp_num = array();
        $k = 0;
        for ($i = 12; $i > 0; $i--) {
            if ($i % 4 == 0) {
                $k++;
            }
            $exp_num[$k][] = substr($num, $i - 1, 1);
        }
        $str = '';
        foreach ($exp_num as $key => $nums) {
            if (array_sum($nums)) {
                $str = array_shift($exp_cn) . $str;
            }
            foreach ($nums as $nk => $nv) {
                if ($nv == '-') {
                    continue;
                }
                if ($nk == 0) {
                    $str = $convert_cn[$nv] . $str;
                } else {
                    $str = $convert_cn[$nv] . $unit_cn[$nk - 1] . $str;
                }
            }
        }
        $str = str_replace($repair_number, array('万', '亿', '-'), $str);
        $str = preg_replace("/-{2,}/", "", $str);
        $str = str_replace(array('零', '-'), array('', '零'), $str);
        return $str;
    }


    function contractTable($tabletip, $treatyArray)
    {
        $tableNote = $tabletip;
        foreach ($treatyArray as $key => $treatyOne) {

            $tableNote = str_replace("#" . $key . "#", $treatyOne, $tableNote);
        }
        return $tableNote;
    }

    function getTradeList($request)
    {
        $sql = "select st.trading_pid,st.student_id,st.tradingtype_code,ct.tradingtype_name,st.trading_status,st.trading_updatatime
              from smc_student_trading as st
              left join smc_code_tradingtype as ct on ct.tradingtype_code=st.tradingtype_code
              where st.trading_pid='{$request['trading_pid']}'";
        $tradeOne = $this->DataControl->selectOne($sql);
        if ($tradeOne['tradingtype_code'] != 'Accountrefund' && $tradeOne['tradingtype_code'] != 'TransferIn' && $tradeOne['tradingtype_code'] != 'TransferOut') {
            $this->error = true;
            $this->errortip = "该类型法打印";
            return false;
        }


        $data = array();

        $sql = "select st.student_cnname,st.student_enname,st.student_branch,st.student_birthday,p.parenter_mobile
              from smc_student as st
              left join smc_student_family as sf on sf.student_id=st.student_id and sf.family_isdefault=1
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where st.student_id='{$tradeOne['student_id']}' 
              ";
        $studentOne = $this->DataControl->selectOne($sql);

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_logo,company_shortname,company_cnname", "company_id='{$this->company_id}'");

        if ($tradeOne['tradingtype_code'] == 'Accountrefund') {
            $data['is_refund'] = 1;
        } else {
            $data['is_refund'] = 0;
        }
        $data['trading_pid'] = $tradeOne['trading_pid'];
        $data['tradingtype_name'] = $tradeOne['tradingtype_name'];
        $data['successTime'] = $tradeOne['trading_updatatime'] > 0 ? date("Y-m-d", $tradeOne['trading_updatatime']) : '';
        $data['student_cnname'] = $studentOne['student_cnname'];
        $data['student_enname'] = $studentOne['student_enname'];
        $data['student_branch'] = $studentOne['student_branch'];
        $data['student_birthday'] = $studentOne['student_birthday'];
        $data['parenter_mobile'] = $studentOne['parenter_mobile'];
        $data['staffer_cnname'] = $this->stafferOne['staffer_cnname'];
        $data['logo'] = $companyOne['company_logo'];
        $data['shortname'] = $companyOne['company_shortname'];
        $data['day'] = date("Y-m-d");

        if ($tradeOne['tradingtype_code'] == 'Accountrefund') {
            $sql = "select ro.refund_payprice,ro.refund_reason
                  from smc_refund_order as ro
                  where ro.trading_pid='{$request['trading_pid']}'";
            $refundOne = $this->DataControl->selectOne($sql);
            $data['price'] = $refundOne['refund_payprice'];
            $data['reason'] = $refundOne['refund_reason'];

        } elseif ($tradeOne['tradingtype_code'] == 'TransferIn') {
            $sql = "select trading_balance,trading_note,trading_withholdbalance
                  from smc_school_trading
                  where trading_topid='{$request['trading_pid']}'
                  ";
            $schTradeOne = $this->DataControl->selectOne($sql);
            $data['price'] = $schTradeOne['trading_balance'] + $schTradeOne['trading_withholdbalance'];
            $data['reason'] = $schTradeOne['trading_note'];

        } elseif ($tradeOne['tradingtype_code'] == 'TransferOut') {
            $sql = "select trading_balance,trading_note,trading_withholdbalance
                  from smc_school_trading
                  where trading_frompid='{$request['trading_pid']}'
                  ";
            $schTradeOne = $this->DataControl->selectOne($sql);
            $data['price'] = $schTradeOne['trading_balance'] + $schTradeOne['trading_withholdbalance'];
            $data['reason'] = $schTradeOne['trading_note'];
        }

        return $data;

    }

    function canceldebtsPay($request)
    {
        $datawhere = " 1 ";
        $datawhere .= " and o.company_id = '{$this->company_id}' and  po.paytype_code='canceldebts'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%'  or s.student_enname like '%{$request['keyword']}%'  or s.student_branch like '%{$request['keyword']}%'  or o.order_pid like '%{$request['keyword']}%' or po.pay_pid like '%{$request['keyword']}%' or o.trading_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$request['school_id']}'";
        }
        if (isset($request['status']) && $request['status'] !== "") {
            $datawhere .= " and po.pay_issuccess ='{$request['status']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,po.order_pid,po.pay_pid,po.pay_note,po.pay_price,po.pay_issuccess,po.pay_createtime,sl.school_cnname,sl.school_branch,o.trading_pid,o.order_paidprice,o.order_paymentprice,o.order_arrearageprice,o.order_allprice
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_student as s ON o.student_id = s.student_id
            left join smc_school as sl On sl.school_id = o.school_id
            WHERE
                {$datawhere}
            ORDER BY
                po.pay_createtime DESC";

        $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已拒绝"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);


            foreach ($dateexcelarray as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                $val['pay_createtime'] = date("Y-m-d H:i:s", $val['pay_createtime']);
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $datearray['order_allprice'] = $dateexcelvar['order_allprice'];
                    $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                    $datearray['order_arrearageprice'] = $dateexcelvar['order_arrearageprice'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    $datearray['pay_note'] = $dateexcelvar['pay_note'];
                    $datearray['pay_issuccess_name'] = $dateexcelvar['pay_issuccess_name'];
                    $datearray['pay_createtime'] = $dateexcelvar['pay_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '交易编号', '订单总额', '已付金额', '欠费金额', '实付金额', '坏账处理金额', '坏账处理原因', '审核状态', '创建时间'));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'trading_pid', 'order_allprice', 'order_paidprice', 'order_arrearageprice', 'order_paymentprice', 'pay_price', 'pay_note', 'pay_issuccess_name', 'pay_createtime');

            $fielname = $this->LgStringSwitch("坏账处理审核");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }

        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }
        if ($orderList) {

            foreach ($orderList as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                $val['pay_createtime'] = date("Y-m-d H:i:s", $val['pay_createtime']);
            }
        }

        $all_num = $this->DataControl->selectOne("
        select count(po.pay_id) as  all_num
        from smc_payfee_order_pay as  po
        left join smc_payfee_order as o ON po.order_pid = o.order_pid
        left join smc_student as s ON o.student_id = s.student_id
        left join smc_school as sl On sl.school_id = o.school_id
        WHERE {$datawhere}");

        if ($all_num) {
            $allnum = $all_num['all_num'];
        } else {
            $allnum = 0;
        }
        $data = array();
        $data['allnum'] = $allnum;
        $data['list'] = $orderList;
        return $data;
    }


    //合同列表
    function protocolList($paramArray)
    {
        $datawhere = " p.company_id = '{$paramArray['company_id']}' and p.school_id = '{$paramArray['school_id']}' and p.protocol_isdel = ''";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.protocol_pid like '%{$paramArray['keyword']}%' or p.order_pid like '%{$paramArray['keyword']}%' or st.student_cnname like '%{$paramArray['keyword']}%' or st.student_enname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ='{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id ='{$paramArray['course_id']}'";
        }
        if (isset($paramArray['protocol_isaudit']) && $paramArray['protocol_isaudit'] !== "") {
            $datawhere .= " and p.protocol_isaudit ='{$paramArray['protocol_isaudit']}'";
        }
        if (isset($paramArray['protocol_issign']) && $paramArray['protocol_issign'] !== "") {
            $datawhere .= " and p.protocol_issign ='{$paramArray['protocol_issign']}'";
        }
        if (isset($paramArray['protocol_isinvoice']) && $paramArray['protocol_isinvoice'] !== "") {
            $datawhere .= " and i.invoice_status ='{$paramArray['protocol_isinvoice']}'";
        }

        if ($paramArray['staffer_id'] == '27550') {
            $datawhere .= " and c.coursecat_id in(133,141,11352) ";
        }

//        if ($paramArray['staffer_id'] == '25721') {
//
//            $datawhere .= " and c.coursetype_id not in(66,79661,96,61) ";
//            $datawhere .= " and ((c.coursecat_id in(133) and c.course_classnum='26') or c.coursecat_id not in (133,135))";
//        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_pid,
                p.student_id,
                p.protocol_isaudit,
                p.protocol_isinvoice,
                p.protocol_issign,
                p.order_pid,
                p.protocol_price,
                sc.school_cnname,
                st.student_cnname,
                st.student_enname,
                st.student_branch,
                c.course_cnname,
                c.course_branch,
                p.protocol_nums,
                c.course_classnum,
                c.course_perhour * c.course_classnum AS time,
                p.protocol_nums * c.course_perhour AS times,
                o.trading_pid,
                oc.ordercourse_unitprice,
                i.invoice_status,
                co.company_protocolupdate,c.coursecat_id
            FROM
                smc_student_protocol AS p
                LEFT JOIN smc_school AS sc ON p.school_id = sc.school_id
                LEFT JOIN smc_student AS st ON st.student_id = p.student_id
                LEFT JOIN smc_course AS c ON c.course_id = p.course_id
                LEFT JOIN smc_payfee_order as o on o.order_pid = p.order_pid
                LEFT JOIN smc_payfee_order_course AS oc ON oc.order_pid = p.order_pid 
                LEFT JOIN shop_invoice AS i ON p.protocol_id = i.protocol_id 
                left join gmc_company as co on co.company_id = sc.company_id
            WHERE
                {$datawhere}
            GROUP BY
                p.protocol_id
            ORDER BY
                p.protocol_id DESC    
            LIMIT {$pagestart},{$num}";

        $tradeList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "未生效", "1" => "已生效"));
        $sign = $this->LgArraySwitch(array("0" => "未签字", "1" => "已签字"));
        if ($tradeList) {
            foreach ($tradeList as &$val) {

//                if ($paramArray['staffer_id'] == '25721' && $val['coursecat_id']=='133') {
//
//                    if($val['protocol_nums']==26){
//                        $val['protocol_price']=$val['protocol_price']*0.64;
//                        $val['ordercourse_unitprice']=$val['ordercourse_unitprice']*0.32;
//
//                        $val['protocol_nums']=$val['protocol_nums']*2;
//                        $val['times']=$val['times']*2;
//
//                    }else{
//                        $val['protocol_price']=$val['protocol_price']*0.5;
//                        $val['ordercourse_unitprice']=$val['ordercourse_unitprice']*0.5;
//                    }
//
//
//                    $val['course_classnum']=$val['course_classnum']*2;
//                    $val['time']=$val['time']*2;
//
//
//                }

                $val['time'] = $val['course_classnum'] . '/' . $val['time'];
                $val['times'] = $val['protocol_nums'] . '/' . $val['times'];
                $val['protocol_isaudit_name'] = $status[$val['protocol_isaudit']];
                if ($val['invoice_status'] == '1') {
                    $val['invoice_status_name'] = $this->LgStringSwitch('已开票');
                } else {
                    $val['invoice_status_name'] = $this->LgStringSwitch('未开票');
                }
                $val['protocol_issign_name'] = $sign[$val['protocol_issign']];

                if($paramArray['company_id'] == '8888'){
                    if($val['protocol_issign'] == '0'){
                        $time = time();
                        $val['qrcode'] = "https://smcapi.kedingdang.com/Order/protocolPrint?protocol_id={$val['protocol_id']}&time={$time}";//小程序二维码
                    }
                }else{
                    $val['qrcode'] = "https://smcapi.kedingdang.com/Order/goalActivityshowimg?imgurl=" .
                        base64_encode("https://{$paramArray['company_id']}.scshop.kedingdang.com/MeCenter/agreementDetail?id=" . $val['protocol_id']);//学校二维码
                }
            }
        }

        $all_num = $this->DataControl->select("
            select count(*) from (SELECT
                p.protocol_id
            FROM
                smc_student_protocol AS p
                LEFT JOIN smc_school AS sc ON p.school_id = sc.school_id
                LEFT JOIN smc_student AS st ON st.student_id = p.student_id
                LEFT JOIN smc_course AS c ON c.course_id = p.course_id
                LEFT JOIN smc_payfee_order as o on o.order_pid = p.order_pid
                LEFT JOIN smc_payfee_order_course AS oc ON oc.order_pid = p.order_pid 
                LEFT JOIN shop_invoice AS i ON p.protocol_id = i.protocol_id 
            WHERE
                {$datawhere}
            GROUP BY
                p.protocol_id) as a
            ");
        $allnums = $all_num[0][0];

        $fieldstring = array('protocol_pid', 'order_pid', 'student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'protocol_nums', 'protocol_price', 'time', 'times', 'ordercourse_unitprice', 'protocol_isaudit_name', 'invoice_status_name', 'protocol_issign_name');
        $fieldname = $this->LgArraySwitch(array('合同编号', '订单编号', '学员中文名', '学员英文名', '学员编号', '课程名称', '课程编号', '合同课程数', '合同金额', '标准课次/课时', '购买课次/课时', '课程单价', '是否生效', '是否开票', '是否签字'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($tradeList) {
            $result['list'] = $tradeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $status = $this->DataControl->getFieldOne("gmc_company", "company_isinvoice", "company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'status' => $status['company_isinvoice']);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无合同管理信息", 'result' => $result);
        }

        return $res;
    }

    function goalActivityitemimg($pid)
    {
        $tokenOnestr = request_by_curl("https://bptapi.kidcastle.cn/Wechat/getBbtWxToken",'',"POST",array());
        $tokenOne = json_decode($tokenOnestr,true);
        $tokenOne = $tokenOne['result'];
        $access_token = $tokenOne['access_token'];

        //构建请求二维码参数
        //path是扫描二维码跳转的小程序路径，可以带参数?id=xxx
        //width是二维码宽度
        $qcode = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=$access_token";

        //页面地址 "pages/share/main?activity_id=156&school_id=670&marketer_id=0"
        //$pathStr = "pages/share/main?activity_id=".$request['activity_id']."&school_id=".$request['school_id']."&marketer_id=".$request['marketer_id'];
//        $pathStr = "subPages/contract/index";
        $pathStr = "subPages/contract_detail/index";
        $sceneStr = "id=" . $pid;
        $param = json_encode(array("page" => $pathStr,"scene" => $sceneStr, "width" => 1500));

        //var_dump($param);
        //exit;
        //POST参数
        $result = $this->httpRequest($qcode, $param, "POST");
        /*//生成图片 -- 加上头部 header
        $base64 = base64_encode($result);
        //echo $base64;
        $strImg = "data:image/png" . ";base64," . $base64;*/
        return $result;
    }

    function httpRequest($url, $data = '', $method = 'GET')
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
        if ($method == 'POST') {
            curl_setopt($curl, CURLOPT_POST, 1);
            if ($data != '') {
                curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            }
        }

        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }


    //发票管理
    function invoiceList($paramArray)
    {
        $datawhere = " i.company_id = '{$paramArray['company_id']}' and i.school_id = '{$paramArray['school_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (i.order_pid like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_cnname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['invoice_status']) && $paramArray['invoice_status'] !== "") {
            $datawhere .= " and i.invoice_status ='{$paramArray['invoice_status']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and pr.course_id ='{$paramArray['course_id']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( i.invoice_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( i.invoice_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $sql = "
            SELECT i.*,c.course_cnname,c.course_branch,op.pay_pid,i.invoice_status as invoice_status_name,from_unixtime(i.invoice_createtime,'%Y-%m-%d') as invoice_createtime,(SELECT SUM(r.refund_price) FROM smc_refund_order as r WHERE r.from_order_pid = i.order_pid) as refundPrice,o.trading_pid
            ,p.parenter_cnname, s.student_branch, s.student_cnname, s.student_enname,sc.school_cnname, ( SELECT p.companies_cnname FROM gmc_code_companies as p WHERE p.companies_id = i.companies_id ) AS companies_cnname,pr.protocol_price
            FROM
                shop_invoice AS i
                LEFT JOIN smc_parenter AS p ON i.parenter_id = p.parenter_id
                LEFT JOIN smc_payfee_order as o on i.order_pid = o.order_pid
                LEFT JOIN smc_payfee_order_pay as op on i.order_pid = op.order_pid
                LEFT JOIN smc_student as s on i.student_id = s.student_id
                LEFT JOIN smc_school as sc on i.school_id = sc.school_id
                LEFT JOIN smc_student_protocol as pr on i.protocol_id = pr.protocol_id
                LEFT JOIN smc_course as c on c.course_id = pr.course_id
            WHERE {$datawhere}
            GROUP BY i.invoice_id ORDER BY invoice_createtime DESC";
        } else {
            $sql = "
            SELECT i.*,c.course_cnname,c.course_branch,op.pay_pid,i.invoice_status as invoice_status_name,from_unixtime(i.invoice_createtime,'%Y-%m-%d') as invoice_createtime,(SELECT SUM(r.refund_price) FROM smc_refund_order as r WHERE r.from_order_pid = i.order_pid) as refundPrice,o.trading_pid
            ,p.parenter_cnname, s.student_branch, s.student_cnname, s.student_enname,sc.school_cnname, ( SELECT p.companies_cnname FROM gmc_code_companies as p WHERE p.companies_id = i.companies_id ) AS companies_cnname,pr.protocol_price,co.companies_isInvoice
            FROM
                shop_invoice AS i
                LEFT JOIN smc_parenter AS p ON i.parenter_id = p.parenter_id
                LEFT JOIN smc_payfee_order as o on i.order_pid = o.order_pid
                LEFT JOIN smc_payfee_order_pay as op on i.order_pid = op.order_pid
                LEFT JOIN smc_student as s on i.student_id = s.student_id
                LEFT JOIN smc_school as sc on i.school_id = sc.school_id
                LEFT JOIN smc_student_protocol as pr on i.protocol_id = pr.protocol_id
                LEFT JOIN gmc_code_companies as co on i.companies_id = co.companies_id
                LEFT JOIN smc_course as c on c.course_id = pr.course_id
            WHERE {$datawhere}
            GROUP BY i.invoice_id ORDER BY invoice_createtime DESC  
            LIMIT {$pagestart},{$num}";
        }


        $invoiceList = $this->DataControl->selectClear($sql);

        if ($invoiceList) {
            $status = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "已申请", "1" => "已开票"));

            foreach ($invoiceList as &$val) {
                $val['invoice_status_name'] = $status[$val['invoice_status_name']];
                if ($val['staffer_id'] > 0) {
                    $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_enname", "staffer_id = '{$val['staffer_id']}'");
                    $val['name'] = $name['staffer_cnname'] . ((isset($name['staffer_enname']) && $name['staffer_enname'] != '') ? '-' . $name['staffer_enname'] : '');
                } else {
                    $name = $this->DataControl->getFieldOne("smc_parenter", "parenter_cnname,parenter_enname", "parenter_id = '{$val['parenter_id']}'");
                    $val['name'] = $name['parenter_cnname'] . ((isset($name['parenter_enname']) && $name['parenter_enname'] != '') ? '-' . $name['parenter_enname'] : '');
                }
                if ($val['invoice_taxpayernum'] == '') {
                    $val['invoice_taxpayernum'] = '--';
                }
                if ($val['invoice_code'] == '') {
                    $val['invoice_code'] = '--';
                }
                if ($val['invoice_number'] == '') {
                    $val['invoice_number'] = '--';
                }
                if ($val['companies_isInvoice'] == '0') {
                    $val['invoice_pdfurl'] = $val['invoice_voucher'];
                }
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $invoiceList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无发票数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['protocol_price'] = $dateexcelvar['protocol_price'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['invoice_title'] = $dateexcelvar['invoice_title'];
                    $datearray['invoice_code'] = $dateexcelvar['invoice_code'];
                    $datearray['invoice_number'] = $dateexcelvar['invoice_number'];
                    $datearray['invoice_email'] = $dateexcelvar['invoice_email'];
                    $datearray['invoice_status_name'] = $dateexcelvar['invoice_status_name'];
                    $datearray['invoice_createtime'] = $dateexcelvar['invoice_createtime'];
                    $datearray['name'] = $dateexcelvar['name'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学员中文名', '学员英文名', '学员编号', '订单编号', '订单金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '发票代码', '发票编号', '邮箱', '状态', '申请时间', '申请人'));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'order_pid', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_code', 'invoice_number', 'invoice_email', 'invoice_status_name', 'invoice_createtime', 'name');
            $tem_name = $this->LgStringSwitch('发票明细表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(i.invoice_id)
            FROM
                shop_invoice AS i
                LEFT JOIN smc_parenter AS p ON i.parenter_id = p.parenter_id
                LEFT JOIN smc_payfee_order as o on i.order_pid = o.order_pid
                LEFT JOIN smc_student as s on i.student_id = s.student_id
                LEFT JOIN smc_school as sc on i.school_id = sc.school_id
                LEFT JOIN smc_student_protocol as pr on i.protocol_id = pr.protocol_id
                LEFT JOIN smc_course as c on c.course_id = pr.course_id
            WHERE {$datawhere}");
        $allnums = $all_num[0][0];

        $fieldstring = array('student_cnname', 'student_enname', 'student_branch', 'order_pid', 'protocol_price', 'course_cnname', 'course_branch', 'invoice_title', 'invoice_email', 'invoice_status_name', 'invoice_createtime', 'name');
        $fieldname = $this->LgArraySwitch(array('学员中文名', '学员英文名', '学员编号', '订单编号', '订单金额', '课程别名称', '课程别编号', '发票抬头', '邮箱', '状态', '申请时间', '申请人'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($invoiceList) {
            $result['list'] = $invoiceList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无发票信息", 'result' => $result);
        }

        return $res;
    }

    //撤销申请
    function cancelApplyAction($paramArray)
    {
        $invoiceOne = $this->DataControl->getFieldOne("shop_invoice", "invoice_id,protocol_id", "invoice_id = '{$paramArray['invoice_id']}'");
        if ($invoiceOne) {
            $data = array();
            $data['protocol_isinvoice'] = 0;

            $field = array();
            $field['protocol_isaudit'] = $this->LgStringSwitch("是否开票");

            if ($this->DataControl->updateData("smc_student_protocol", "protocol_id = '{$invoiceOne['protocol_id']}'", $data)) {
                $this->DataControl->delData("shop_invoice", "invoice_id = '{$paramArray['invoice_id']}'");
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "撤销成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '撤销失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //支付记录
    function payList($paramArray)
    {
        $sql = "
            SELECT
                op.pay_pid,
                op.pay_typename,
                op.paytype_code,
                op.pay_price,
                op.order_pid,
                pop.paylog_tradeno,
                op.pay_note,
                op.pay_outnumber,
                pop.paylog_ifee,
                pop.paylog_img,
                op.pay_issuccess,
                from_unixtime(op.pay_successtime,'%Y-%m-%d') as pay_successtime
            FROM
                smc_payfee_order_pay AS op
                LEFT JOIN smc_payfee_order_paylog AS pop ON pop.order_pid = op.order_pid 
                AND op.pay_pid = pop.pay_pid
                LEFT JOIN smc_payfee_order AS po ON po.order_pid = op.order_pid
                LEFT JOIN smc_student AS s ON po.student_id = s.student_id 
            WHERE
                op.order_pid = '{$paramArray['order_pid']}' 
            ORDER BY
                op.pay_createtime DESC";

        $invoiceList = $this->DataControl->selectClear($sql);

        $status = array("0" => "未支付", "1" => "已支付", "-1" => "已失效");
        if ($invoiceList) {
            foreach ($invoiceList as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                if ($val['paylog_img'] == '') {
                    $val['paylog_img'] = '[]';
                }
            }
        }

        $fieldstring = array('pay_pid', 'pay_typename', 'pay_price', 'paylog_tradeno', 'pay_issuccess_name', 'paylog_ifee', 'pay_note', 'pay_successtime');
        $fieldname = $this->LgArraySwitch(array('支付请求号', '支付方式', '实际支付金额', '支付交易号', '支付状态', '手续费', '支付备注', '支付时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($invoiceList) {
            $result['list'] = $invoiceList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无支付记录", 'result' => $result);
        }

        return $res;
    }

    //发票合同列表
    function getProtocolsList($paramArray)
    {
        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_pid,
                p.order_pid,
                protocol_createtime,
                p.protocol_nums,
                p.protocol_price,
                p.protocol_isaudit,
                p.protocol_isaudit AS protocol_isaudit_name,
                c.course_classnum,
                c.course_classtimes,
                o.ordercourse_unitprice
            FROM
                smc_student_protocol AS p
                LEFT JOIN shop_invoice AS i ON i.protocol_id = p.protocol_id
                LEFT JOIN smc_course as c on p.course_id = c.course_id
                LEFT JOIN smc_payfee_order_course as o on o.order_pid = p.order_pid
            WHERE
                i.protocol_id = '{$paramArray['protocol_id']}'
             GROUP BY p.protocol_id";

        $districtList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "未审核", "1" => "已审核"));
        if ($districtList) {
            foreach ($districtList as &$val) {
                $val['protocol_isaudit_name'] = $status[$val['protocol_isaudit_name']];
                $val['standard'] = $val['course_classnum'] . '/' . ($val['course_classnum'] * $val['course_classtimes']);
                $val['buy'] = $val['protocol_nums'] . '/' . ($val['protocol_nums'] * $val['course_classtimes']);
            }
        }

        $fieldstring = array('protocol_pid', 'protocol_nums', 'protocol_price', 'standard', 'buy', 'ordercourse_unitprice');
        $fieldname = array('合同编号', '合同课程数', '合同金额', '标准课次/课时', '购买课次/课时', '课程单价');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldstring[$i]));
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($districtList) {
            $result['list'] = $districtList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无合同", 'result' => $result);
        }

        return $res;
    }

    //作废合同
    function DelProtocolAction($paramArray)
    {
        $ProtocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "order_pid", "order_pid = '{$paramArray['order_pid']}' and protocol_isinvoice = '1'");

        if ($ProtocolOne) {
            ajax_return(array('error' => '1', 'errortip' => '该订单有已开过发票的合同，无法作废！'), $this->companyOne['company_language']);
        }

        $data = array();
        $data['protocol_isdel'] = '1';
        $a = $this->DataControl->selectOne("select count(protocol_id) as num from smc_student_protocol where order_pid = '{$paramArray['order_pid']}' and protocol_isdel = '0'");
        if ($this->DataControl->updateData("smc_student_protocol", "order_pid = '{$paramArray['order_pid']}'", $data)) {

            $this->orderTracks($this->LgStringSwitch('作废合同'), $this->LgStringSwitch('订单' . $a['num'] . '份合同已作废'));

            $result = array();
            $res = array('error' => '0', 'errortip' => "作废合同成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '作废合同失败', 'result' => $result);
        }
        return $res;
    }

    function getSanOrderList($request)
    {
        $datawhere = " a.company_id='{$this->company_id}' and a.trading_status=1 and a.tradingtype_code in ('PaynewFee','PayrenewFee','Recharge') and b.info_iscontinuepay=1";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname like '%{$request['keyword']}%' or d.student_enname like '%{$request['keyword']}%' or d.student_idcard like '%{$request['keyword']}%' or d.student_branch like '%{$request['keyword']}%' or a.trading_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['tradingtype_code']) && $request['tradingtype_code'] !== '') {
            $datawhere .= " and a.tradingtype_code in ('" . implode("','", explode(',', $request['tradingtype_code'])) . "')";

            if (isset($request['status']) && $request['status'] != '') {

                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge') {

                    $datawhere .= " and exists (select x.trading_pid from smc_payfee_order as x where x.trading_pid=a.trading_pid and x.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'Accountrefund') {

                    $datawhere .= " and exists (select x.trading_pid from smc_refund_order as x where x.trading_pid=a.trading_pid and x.refund_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'CourseForward') {

                    $datawhere .= " and exists (select x.trading_pid from smc_forward_dealorder as po where x.trading_pid=a.trading_pid and x.dealorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

                    $datawhere .= " and exists (select x.trading_pid from smc_course_reduceorder as po where x.trading_pid=a.trading_pid and x.reduceorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ClassGiving') {

                    $datawhere .= " and exists (select x.trading_pid from smc_freehour_order as po where x.trading_pid=a.trading_pid and x.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferIn') {

                    $datawhere .= " and exists (select x.trading_topid from smc_school_trading as po where x.trading_topid=a.trading_pid and x.trading_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferOut') {

                    $datawhere .= " and exists (select x.trading_frompid from smc_school_trading as po where x.trading_frompid=a.trading_pid and x.trading_status='{$request['status']}')";

                }
            }
        }

        if (isset($request['order_status']) && $request['order_status'] !== '') {
            $datawhere .= " and b.order_status = '{$request['order_status']}'";
        }

        if (isset($request['is_merge']) && $request['is_merge'] !== '') {
            if($request['is_merge']==1){
                $datawhere .= " and b.mergeorder_pid <> ''";
            }else{
                $datawhere .= " and b.mergeorder_pid = ''";
            }

        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id = '{$request['school_id']}'";
        }

        if (isset($request['info_iscontinuepay']) && $request['info_iscontinuepay'] !== '') {
            $datawhere .= " and b.info_iscontinuepay = '{$request['info_iscontinuepay']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.trading_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.trading_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
        }

        $sql = "select b.order_id as info_id,c.school_cnname,c.school_branch,b.trading_pid,d.student_cnname,d.student_enname,d.student_branch,b.order_status,b.order_from,b.order_allprice,b.order_paymentprice,b.order_paidprice,b.order_arrearageprice,FROM_UNIXTIME(b.order_createtime,'%Y-%m-%d %H:%i') as order_createtime,b.info_iscontinuepay
            from smc_student_trading as a 
            inner join smc_payfee_order as b on b.trading_pid=a.trading_pid
            left join smc_school as c on c.school_id=a.school_id
            left join smc_student as d on d.student_id=a.student_id
            where {$datawhere}
            order by b.order_id desc
            ";

        if ($request['is_export'] && $request['is_export'] == '1') {
            $tradeList = $this->DataControl->selectClear($sql);
        } else {
            $tradeList = $this->DataControl->selectClear($sql . " limit {$pagestart},{$num}");
        }

        if (!$tradeList) {
            $tradeList = array();
        }

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        $porder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

        $outexceldata = array();

        $field = array();
        $k = 0;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["ismethod"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "trading_pid";
        $field[$k]["fieldname"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_status_name";
        $field[$k]["fieldname"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_from_name";
        $field[$k]["fieldname"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_allprice";
        $field[$k]["fieldname"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paymentprice";
        $field[$k]["fieldname"] = "实付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_paidprice";
        $field[$k]["fieldname"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_arrearageprice";
        $field[$k]["fieldname"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "order_createtime";
        $field[$k]["fieldname"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['is_export'] && $request['is_export'] == '1') {
            foreach ($tradeList as $val) {
                $data = array();
                $data['trading_pid'] = $val['trading_pid'];
                $data['student_cnname'] = $val['student_cnname'];
                $data['student_enname'] = $val['student_enname'];
                $data['student_branch'] = $val['student_branch'];
                $data['order_status_name'] = $porder_status[$val['order_status']];
                $data['order_from_name'] = $order_from[$val['order_from']];
                $data['order_allprice'] = $val['order_allprice'];
                $data['order_paymentprice'] = $val['order_paymentprice'];
                $data['order_paidprice'] = $val['order_paidprice'];
                $data['order_arrearageprice'] = $val['order_arrearageprice'];
                $data['order_createtime'] = $val['order_createtime'];
                $data['info_iscontinuepay'] = $status[$val['info_iscontinuepay']];

                $outexceldata[] = $data;
            }

            $excelheader = $this->LgArraySwitch(array('交易编号', '中文名', '英文名', '学员编号', '订单状态', '订单来源', '订单总额', '实付金额', '已付金额', '欠费金额', '下单时间', '是否三期连缴'));
            $excelfileds = array('trading_pid', 'student_cnname', 'student_enname', 'student_branch', 'order_status_name', 'order_from_name', 'order_allprice', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'order_createtime', 'info_iscontinuepay');
            query_to_excel($excelheader, $outexceldata, $excelfileds, '三期连缴名单.xlsx');
            exit;
        }

        foreach($tradeList as &$tradeOne){

            $tradeOne['order_status_name'] = $porder_status[$tradeOne['order_status']];
            $tradeOne['order_from_name'] = $order_from[$tradeOne['order_from']];
        }

        $data = array();
        $count_sql = "select b.order_id
            from smc_student_trading as a 
            inner join smc_payfee_order as b on b.trading_pid=a.trading_pid
            left join smc_school as c on c.school_id=a.school_id
            left join smc_student as d on d.student_id=a.student_id
            where {$datawhere}";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $tradeList;
        $data['field'] = $field;

        return $data;
    }


}
