<?php


namespace Model\Smc;

class SettingModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //获取学校机构信息
    function getSchoolInfoList($paramArray)
    {
        $school = $this->DataControl->selectClear("
            SELECT
                school_cnname,
                school_shortname,
                school_remark,
                ( SELECT r.region_name FROM smc_school AS s LEFT JOIN smc_code_region AS r ON s.school_province = r.region_id WHERE s.school_id = '{$paramArray['school_id']}' ) AS province,
                ( SELECT r.region_name FROM smc_school AS s LEFT JOIN smc_code_region AS r ON s.school_city = r.region_id WHERE s.school_id = '{$paramArray['school_id']}' ) AS city,
                ( SELECT r.region_name FROM smc_school AS s LEFT JOIN smc_code_region AS r ON s.school_area = r.region_id WHERE s.school_id = '{$paramArray['school_id']}' ) AS area,
                school_province,
                school_city,
                school_area
            FROM
                smc_school 
            WHERE
                school_id = '{$paramArray['school_id']}'
");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }


        return $res;
    }

    //编辑校园机构
    function updateSchoolAction($paramArray)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_id = '{$paramArray['school_id']}'");
        if ($schoolOne) {
            $data = array();
            $data['school_cnname'] = $paramArray['school_cnname'];
            $data['school_shortname'] = $paramArray['school_shortname'];
            $data['school_remark'] = $paramArray['school_remark'];
            $data['school_province'] = $paramArray['school_province'];
            $data['school_city'] = $paramArray['school_city'];
            $data['school_area'] = $paramArray['school_area'];
            $data['school_updatatime'] = time();

            $field = array();
            $field['school_cnname'] = $this->LgStringSwitch("校区名称");
            $field['school_shortname'] = $this->LgStringSwitch("学校简称");
            $field['school_remark'] = $this->LgStringSwitch("机构简介");
            $field['school_province'] = $this->LgStringSwitch("所在省");
            $field['school_city'] = $this->LgStringSwitch("所在市");
            $field['school_area'] = $this->LgStringSwitch("所在区域");
            $field['school_updatatime'] = $this->LgStringSwitch("修改时间");
            if ($this->DataControl->updateData("smc_school", "school_id = '{$paramArray['school_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑校园机构成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑校园机构失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getSchAlerttime($request)
    {
        $one = $this->DataControl->getFieldOne("smc_school_arrears_alert", "alert_id,alert_time,alert_status", "school_id='{$this->school_id}'");
        $schoolone = $this->DataControl->getFieldOne("smc_school", "school_coursewarning,school_classtimeswarning", "school_id='{$this->school_id}'");

        if (!$one) {
            $one['alert_id'] = 0;
            $one['alert_time'] = 3;
            $one['alert_status'] = 0;
        }

        $data = $one;
        $data['school_coursewarning'] = $schoolone['school_coursewarning'];
        $data['school_classtimeswarning'] = $schoolone['school_classtimeswarning'];
        return $data;
    }

    function editSchAlerttime($request)
    {
        if ($request['alert_status'] == 0 && $request['school_coursewarning'] == 0 && $request['school_classtimeswarning'] == 0) {
            $this->error = 1;
            $this->errortip = "编辑失败，至少开启一个预警维度";
            return false;
        }
        $data = array();
        $data['alert_time'] = $request['alert_time'];
        $data['alert_status'] = $request['alert_status'];
        if (isset($request['alert_id']) && $request['alert_id'] != '' && $request['alert_id'] > 0) {
            $alert = $this->DataControl->updateData("smc_school_arrears_alert", "alert_id='{$request['alert_id']}'", $data);
        } else {
            $data['company_id'] = $this->company_id;
            $data['school_id'] = $this->school_id;
            $alert = $this->DataControl->insertData("smc_school_arrears_alert", $data);
        }

        $schooldata = array();
        $schooldata['school_coursewarning'] = $request['school_coursewarning'];
        $schooldata['school_classtimeswarning'] = $request['school_classtimeswarning'];
        $school = $this->DataControl->updateData("smc_school", "school_id='{$request['school_id']}'", $schooldata);


        if ($alert | $school) {
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "编辑失败";
            return false;
        }
    }

    //获取操作日志列表
    function getWorklogList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$paramArray['keyword']}%' or w.worklog_ip like '%{$paramArray['keyword']}%' or w.worklog_type like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['time1']) && $paramArray['time1'] !== "") {
            $paramArray['time1'] = strtotime($paramArray['time1']);

            $datawhere .= " and w.worklog_time > {$paramArray['time1']}";
        }
        if (isset($paramArray['time2']) && $paramArray['time2'] !== "") {
            $paramArray['time2'] = strtotime($paramArray['time2']) + 24 * 60 * 60 - 1;
            $datawhere .= " and w.worklog_time < {$paramArray['time2']}";

        }
        if (isset($paramArray['stafferOne_id']) && $paramArray['stafferOne_id'] !== "") {
            $datawhere .= " and s.staffer_id ={$paramArray['stafferOne_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                FROM_UNIXTIME( w.worklog_time, '%Y-%m-%d %H:%i:%s' ) AS worklog_time,
                s.staffer_cnname,
                s.staffer_branch,
                w.worklog_module,
                w.worklog_content,
                w.worklog_ip,
                w.worklog_type,
                worklog_time
            FROM
                smc_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id 
            WHERE
                {$datawhere} AND w.school_id = '{$paramArray['school_id']}' AND w.company_id = '{$paramArray['company_id']}'
            ORDER BY
	            w.worklog_id DESC
            LIMIT {$pagestart},{$num}";

        $StafferList = $this->DataControl->selectClear($sql);
        if ($StafferList) {
            foreach ($StafferList as $key => $value) {
                $StafferList[$key]['worklog_time'] = date('Y-m-d H:i:s', $value['worklog_time']);
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(w.worklog_id) as a
            FROM
                smc_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id 
            WHERE
               {$datawhere} AND w.school_id = '{$paramArray['school_id']}' AND w.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('worklog_time', 'staffer_cnname', 'staffer_branch ', 'worklog_module', 'worklog_type', 'worklog_content', 'worklog_ip');
        $fieldname = array('时间', '操作用户', '工号', '操作', "类型", '内容', 'IP');
        $fieldcustom = array("1", "1", "1", "1", "1", "0", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        $result['staffer'] = $this->DataControl->selectClear("select s.staffer_id AS stafferOne_id,s.staffer_cnname from smc_staffer as s left join gmc_staffer_postbe as p on p.staffer_id = s.staffer_id WHERE p.school_id = '{$paramArray['school_id']}' GROUP BY s.staffer_id");

        if ($StafferList) {
            $result['list'] = $StafferList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无操作日志信息", 'result' => $result);
        }
        return $res;
    }

    //获取教室设置列表
    function getDiseaseList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (d.diseasetype_name like '%{$paramArray['keyword']}%')";
        }
        $sql = " SELECT d.* FROM gmc_code_diseasetype AS d WHERE {$datawhere}  ";

        $diseasetypeList = $this->DataControl->selectClear($sql);

        $field = array();
        $field['diseasetype_id'] = 'ID';
        $field['diseasetype_name'] = '疾病名称';

        $result = array();
        $result['field'] = $field;

        if ($diseasetypeList) {
            $result['list'] = $diseasetypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无疾病信息", 'result' => $result);
        }

        return $res;
    }

    //获取教室设置列表
    function getClassroomList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.classroom_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['classroom_status']) && $paramArray['classroom_status'] !== "") {
            $datawhere .= " and c.classroom_status ={$paramArray['classroom_status']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.classroom_id,
                c.gclassroom_id,
                c.classroom_cnname,
                c.classroom_maxnums,
                c.classroom_remark,
                c.classroom_status
            FROM
                smc_classroom AS c 
            WHERE
                {$datawhere} AND c.school_id = '{$paramArray['school_id']}'
            ORDER BY
	            c.classroom_id DESC
            LIMIT {$pagestart},{$num}";

        $ClassroomList = $this->DataControl->selectClear($sql);

        if ($ClassroomList) {
            $status = array("0" => "0", "1" => "100");
            foreach ($ClassroomList as &$val) {
                $val['classroom_status'] = $status[$val['classroom_status']];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(c.classroom_id) as a 
            FROM
                smc_classroom AS c
            WHERE
               c.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('classroom_cnname', 'classroom_maxnums', 'classroom_status', 'classroom_remark');
        $fieldname = array('教室名称', '可容纳人数', '是否启用', '备注');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");
        $isswitch = array(0, 0, 1, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($isswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ClassroomList) {
            $result['list'] = $ClassroomList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无教室", 'result' => $result);
        }

        return $res;
    }

    //编辑教室
    function updateClassroomAction($paramArray)
    {
        $classroomOne = $this->DataControl->getFieldOne("smc_classroom", "classroom_id", "classroom_id = '{$paramArray['classroom_id']}'");
        if ($classroomOne) {
            $data = array();
            $data['classroom_cnname'] = $paramArray['classroom_cnname'];
            $data['classroom_maxnums'] = $paramArray['classroom_maxnums'];
            $data['classroom_remark'] = $paramArray['classroom_remark'];
            $data['gclassroom_id'] = $paramArray['gclassroom_id'];
            $data['classroom_updatatime'] = time();

            $field = array();
            $field['classroom_cnname'] = $this->LgStringSwitch("教室名称");
            $field['classroom_maxnums'] = $this->LgStringSwitch("可容纳人数");
            $field['classroom_updatatime'] = $this->LgStringSwitch("修改时间");
            $field['classroom_remark'] = $this->LgStringSwitch("备注");

            if ($this->DataControl->getFieldOne("smc_classroom", "classroom_id", "classroom_id<>'{$paramArray['classroom_id']}' and school_id='{$this->school_id}' and classroom_cnname='{$paramArray['classroom_cnname']}'")) {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '教室名称已存在', 'result' => $result);
            } else {
                if ($this->DataControl->updateData("smc_classroom", "classroom_id = '{$paramArray['classroom_id']}'", $data)) {
                    $result = array();
                    $result["field"] = $field;
                    $result["data"] = $data;
                    $res = array('error' => '0', 'errortip' => "编辑教室成功", 'result' => $result);
                } else {
                    $result = array();
                    $result["data"] = array();
                    $res = array('error' => '1', 'errortip' => '编辑教室失败', 'result' => $result);
                }
            }

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //添加教室
    function addClassroomAction($paramArray)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_iscreateroom", "school_id='{$this->school_id}'");

        $data = array();
        if (isset($paramArray['gclassroom_id']) && $paramArray['gclassroom_id'] != '') {
            $data['gclassroom_id'] = $paramArray['gclassroom_id'];

        } else {

            if ($schoolOne['school_iscreateroom'] != 1) {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '校区无权限添加教室', 'result' => $result);
                return $res;
            }
        }
        if ($this->DataControl->getFieldOne("smc_classroom", "classroom_id", "classroom_id<>'{$paramArray['classroom_id']}' and school_id='{$this->school_id}' and classroom_cnname='{$paramArray['classroom_cnname']}'")) {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '教室名称已存在', 'result' => $result);
        } else {
            $data['classroom_cnname'] = $paramArray['classroom_cnname'];
            $data['classroom_maxnums'] = $paramArray['classroom_maxnums'];
            $data['classroom_remark'] = $paramArray['classroom_remark'];
            $data['school_id'] = $paramArray['school_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['classroom_createtime'] = time();


            if ($this->DataControl->insertData('smc_classroom', $data)) {
                $result = array();
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "添加教室成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '添加教室失败', 'result' => $result);
            }
        }

        return $res;
    }

    //删除教室
    function delClassroomAction($paramArray)
    {
        $ClassroomOne = $this->DataControl->getFieldOne("smc_classroom", "classroom_id", "classroom_id = '{$paramArray['classroom_id']}'");
        if ($ClassroomOne) {

            $a = $this->DataControl->getFieldOne("smc_class_hour", "classroom_id", "classroom_id = '{$ClassroomOne['classroom_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该教室已被使用，不可删除"));
            }

            if ($this->DataControl->delData("smc_classroom", "classroom_id = '{$paramArray['classroom_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除教室成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除教室失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取角色设置列表
    function getPostpartList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.postpart_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.postpart_id,
                p.postpart_name,
                p.postpart_remark,
                (SELECT count(b.postbe_id) FROM gmc_staffer_postbe as b WHERE b.school_id = '{$paramArray['school_id']}' and p.postpart_id = b.postpart_id) as staffernum
            FROM
                smc_school_postpart AS p
            WHERE {$datawhere} AND p.school_id = '{$paramArray['school_id']}'
            ORDER BY
	            p.postpart_id DESC
            LIMIT {$pagestart},{$num}";

        $ClassroomList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.postpart_id) as a
            FROM
               smc_school_postpart AS p
            WHERE
               {$datawhere} AND p.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('postpart_name', 'num', 'postpart_remark');
        $fieldname = array('角色名称', '角色数量', '描述');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ClassroomList) {
            $result['list'] = $ClassroomList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无角色信息", 'result' => $result);
        }

        return $res;
    }

    //获取学校省
    function getProvinceApi($paramArray)
    {
        $school = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = 1");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    //获取学校市
    function getCityApi($paramArray)
    {
        $school = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$paramArray['region_id']}'");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    //编辑校园角色
    function updatePostpartAction($paramArray)
    {
        $classroomOne = $this->DataControl->getFieldOne("smc_school_postpart", "postpart_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($classroomOne) {
            $data = array();
            $data['postpart_name'] = $paramArray['postpart_name'];
            $data['postpart_remark'] = $paramArray['postpart_remark'];

            $field = array();
            $field['postpart_name'] = $this->LgStringSwitch("角色名称");
            $field['postpart_remark'] = $this->LgStringSwitch("描述");

            $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_name', "postpart_id = '{$paramArray['postpart_id']}'");
            if ($paramArray['postpart_name'] != $postpart_name['postpart_name']) {
                $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_id', "postpart_name = '{$paramArray['postpart_name']}'");
                if ($postpart_name) {
                    ajax_return(array('error' => 1, 'errortip' => "校园角色已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_school_postpart", "postpart_id = '{$paramArray['postpart_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑校园角色成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑校园角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //添加校园角色
    function addPostpartAction($paramArray)
    {
        $data = array();
        $data['postpart_name'] = $paramArray['postpart_name'];
        $data['postpart_remark'] = $paramArray['postpart_remark'];
        $data['school_id'] = $paramArray['school_id'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['postpart_name'] = $this->LgStringSwitch("角色名称");
        $field['postpart_remark'] = $this->LgStringSwitch("描述");
        $field['school_id'] = $this->LgStringSwitch("所属学校");
        $field['company_id'] = $this->LgStringSwitch("所属公司");

        $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_id', "postpart_name = '{$paramArray['postpart_name']}'");
        if ($postpart_name) {
            ajax_return(array('error' => 1, 'errortip' => "校园角色已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_school_postpart', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加角色成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加角色失败', 'result' => $result);
        }
        return $res;
    }

    //删除校园角色
    function delPostpartAction($paramArray)
    {
        $PostpartOne = $this->DataControl->getFieldOne("smc_school_postpart", "postpart_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($PostpartOne) {
            if ($this->DataControl->delData("smc_school_postpart", "postpart_id = '{$paramArray['postpart_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除校园角色成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除校园角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取取消上课原因列表
    function getReasonList($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                r.reason_id,
                r.reason_content,
                r.reason_remark
            FROM
                smc_code_hourcancel_reason AS r
            WHERE r.company_id = '{$paramArray['company_id']}'
            ORDER BY
	            r.reason_id DESC
            LIMIT {$pagestart},{$num}";

        $ReasonList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(r.reason_id) as a
            FROM
               smc_code_hourcancel_reason AS r
            WHERE r.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('reason_content', 'reason_remark');
        $fieldname = array('取消原因', '描述');
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ReasonList) {
            $result['list'] = $ReasonList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无取消上课原因", 'result' => $result);
        }

        return $res;
    }

    //添加取消上课原因
    function addReasonAction($paramArray)
    {
        $data = array();
        $data['reason_content'] = $paramArray['reason_content'];
        $data['reason_remark'] = $paramArray['reason_remark'];
        $data['school_id'] = $paramArray['school_id'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['reason_content'] = $this->LgStringSwitch("取消原因");
        $field['reason_remark'] = $this->LgStringSwitch("描述");
        $field['school_id'] = $this->LgStringSwitch("所属学校");
        $field['company_id'] = $this->LgStringSwitch("所属公司");

        $Reason = $this->DataControl->getFieldOne('smc_code_hourcancel_reason', 'reason_id', "reason_content = '{$paramArray['reason_content']}'");
        if ($Reason) {
            ajax_return(array('error' => 1, 'errortip' => "取消原因已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_hourcancel_reason', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加取消上课原因成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加取消上课原因失败', 'result' => $result);
        }
        return $res;
    }

    //编辑取消上课原因
    function updateReasonAction($paramArray)
    {
        $classroomOne = $this->DataControl->getFieldOne("smc_code_hourcancel_reason", "reason_id", "reason_id = '{$paramArray['reason_id']}'");
        if ($classroomOne) {
            $data = array();
            $data['reason_content'] = $paramArray['reason_content'];
            $data['reason_remark'] = $paramArray['reason_remark'];

            $field = array();
            $field['reason_content'] = $this->LgStringSwitch("取消原因");
            $field['reason_remark'] = $this->LgStringSwitch("描述");

            $reason_content = $this->DataControl->getFieldOne('smc_code_hourcancel_reason', 'reason_content', "reason_id = '{$paramArray['reason_id']}'");
            if ($paramArray['reason_content'] != $reason_content['reason_content']) {
                $reason_content = $this->DataControl->getFieldOne('smc_code_hourcancel_reason', 'reason_id', "reason_content = '{$paramArray['reason_content']}'");
                if ($reason_content) {
                    ajax_return(array('error' => 1, 'errortip' => "取消原因已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_code_hourcancel_reason", "reason_id = '{$paramArray['reason_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑取消原因成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑取消原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除取消上课原因
    function delReasonAction($paramArray)
    {
        $ReasonOne = $this->DataControl->getFieldOne("smc_code_hourcancel_reason", "reason_id", "reason_id = '{$paramArray['reason_id']}'");
        if ($ReasonOne) {
            if ($this->DataControl->delData("smc_code_hourcancel_reason", "reason_id = '{$paramArray['reason_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除取消上课原因成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除取消上课原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function RoomStatusAction($paramArray)
    {
        $classroomOne = $this->DataControl->getFieldOne("smc_classroom", "classroom_id", "classroom_id = '{$paramArray['classroom_id']}'");

        if ($classroomOne) {
            $data = array();
            $data['classroom_status'] = $paramArray['classroom_status'];

            $field = array();
            $field['classroom_status'] = $this->LgStringSwitch('是否启用');

            if ($this->DataControl->updateData("smc_classroom", "classroom_id = '{$paramArray['classroom_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //节假日管理列表
    function getHolidaysList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.holidays_name like '%{$paramArray['keyword']}%' or s.holidays_day like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['day']) && $paramArray['day'] !== "") {
            $datawhere .= " and s.holidays_day like '%{$paramArray['day']}%'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.holidays_id,
                s.school_id,
                s.holidays_name,
                s.holidays_day,
                s.holidays_status,
                s.holidays_status as holidays_status_note,
                s.holidays_note
            FROM
                smc_code_holidays AS s
            WHERE
                {$datawhere} AND (s.school_id = '{$paramArray['school_id']}' or (company_id = '{$paramArray['company_id']}' and s.school_id = '0'))
            ORDER BY
                s.holidays_id DESC
            LIMIT {$pagestart},{$num}";

        $spendingList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "放假", "1" => "上课"));
        if ($spendingList) {
            foreach ($spendingList as &$val) {
                $val['holidays_status_note'] = $status[$val['holidays_status_note']];
                if ($val['school_id'] == '0') {
                    $val['type'] = $this->LgStringSwitch('集团设置');
                } else {
                    $val['type'] = $this->LgStringSwitch('校园设置');
                }
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(s.holidays_id) as a 
            FROM
                smc_code_holidays AS s
            WHERE
                {$datawhere} AND (s.school_id = '{$paramArray['school_id']}'  or (company_id = '{$paramArray['company_id']}' and s.school_id = '0'))");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('holidays_day', 'holidays_name', 'holidays_status_note', 'holidays_note', 'type', 'holidays_status');
        $fieldname = array('日期', '假日名称', '假日类型', '备注', '备注', '假日类型');
        $fieldcustom = array("1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($spendingList) {
            $result['list'] = $spendingList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无节假日信息", 'result' => $result);
        }

        return $res;
    }

    //新增节假日
    function addHolidaysAction($paramArray)
    {
        $data = array();
        $data['holidays_name'] = $paramArray['holidays_name'];
        $data['holidays_day'] = $paramArray['holidays_day'];
        $data['holidays_status'] = $paramArray['holidays_status'];
        $data['holidays_note'] = $paramArray['holidays_note'];
        $data['school_id'] = $paramArray['school_id'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['holidays_name'] = $this->LgStringSwitch("假日名称");
        $field['holidays_day'] = $this->LgStringSwitch("假日日期");
        $field['holidays_status'] = $this->LgStringSwitch("假日类型");
        $field['holidays_note'] = $this->LgStringSwitch("备注");
        $field['school_id'] = $this->LgStringSwitch("所属学校");

        $holidays_day = $this->DataControl->getFieldOne('smc_code_holidays', 'holidays_id', "holidays_day = '{$paramArray['holidays_day']}' and school_id = '{$paramArray['school_id']}'");
        if ($holidays_day) {
            ajax_return(array('error' => 1, 'errortip' => "假日日期已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_holidays', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加假日日期成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加假日日期失败', 'result' => $result);
        }
        return $res;
    }

    //编辑节假日
    function updateHolidaysAction($paramArray)
    {
        $holidaysOne = $this->DataControl->getFieldOne("smc_code_holidays", "holidays_id", "holidays_id = '{$paramArray['holidays_id']}'");
        if ($holidaysOne) {
            $data = array();
            $data['holidays_name'] = $paramArray['holidays_name'];
            $data['holidays_day'] = $paramArray['holidays_day'];
            $data['holidays_status'] = $paramArray['holidays_status'];
            $data['holidays_note'] = $paramArray['holidays_note'];

            $field = array();
            $field['holidays_name'] = $this->LgStringSwitch("假日名称");
            $field['holidays_day'] = $this->LgStringSwitch("假日日期");
            $field['holidays_status'] = $this->LgStringSwitch("假日类型");
            $field['holidays_note'] = $this->LgStringSwitch("备注");

            $holidays_day = $this->DataControl->getFieldOne('smc_code_holidays', 'holidays_day', "holidays_id = '{$paramArray['holidays_id']}'");
            if ($paramArray['holidays_day'] != $holidays_day['holidays_day']) {
                $holidays_day = $this->DataControl->getFieldOne('smc_code_holidays', 'holidays_id', "holidays_day = '{$paramArray['holidays_day']}' and school_id = '{$paramArray['school_id']}'");
                if ($holidays_day) {
                    ajax_return(array('error' => 1, 'errortip' => "假日日期已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_code_holidays", "holidays_id = '{$paramArray['holidays_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "假日日期修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '假日日期修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除节假日
    function delHolidaysAction($paramArray)
    {
        $holidaysOne = $this->DataControl->getFieldOne("smc_code_holidays", "holidays_id", "holidays_id = '{$paramArray['holidays_id']}'");
        if ($holidaysOne) {
            if ($this->DataControl->delData("smc_code_holidays", "holidays_id = '{$paramArray['holidays_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除节假日成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除节假日失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getMonthList($paramArray)
    {
        $date = getthemonth($paramArray['day']);

        $sql = "select a.holidays_day,a.holidays_id,a.holidays_status,a.holidays_name,a.holidays_note from (select * from smc_code_holidays where (school_id = '{$paramArray['school_id']}'	or ( company_id = '{$paramArray['company_id']}' and school_id = '0' ))  and holidays_day between '{$date[0]}' and '{$date[1]}' order by school_id DESC) as a
group by a.holidays_day
";

        $mothListArray = $this->DataControl->selectClear($sql);
        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['holidays_day']));
                $v['month'] = date('m', strtotime($v['holidays_day']));
                $v['day'] = date('d', strtotime($v['holidays_day']));
                unset($mothListArray[$k]['holidays_day']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));

//        var_dump($mothListArray);die();
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = $i;
                    $data['holidays_status'] = strval(-1);
                    array_push($mothListArray, $data);
                }
            }
            usort($mothListArray, function ($a, $b) {
                if ($a['day'] == $b['day']) return 0;
                return $a['day'] > $b['day'] ? 1 : -1;
            });
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = $i;
                $data['holidays_status'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $mothListArray), $this->companyOne['company_language']);
    }

    //附近学校管理列表
    function getnearschoolList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (n.nearschool_name like '%{$paramArray['keyword']}%' or n.nearschool_address like '%{$paramArray['keyword']}%' or n.nearschool_shortname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['nearschool_status']) && $paramArray['nearschool_status'] !== "") {
            $datawhere .= " and n.nearschool_status ='{$paramArray['nearschool_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                n.nearschool_id,
                n.nearschool_name,
                n.nearschool_address,
                n.nearschool_shortname,
                n.nearschool_status,
                n.nearschool_type
            FROM
                crm_code_nearschool AS n
            WHERE
                {$datawhere} and n.school_id = '{$paramArray['school_id']}'
            ORDER BY
                n.nearschool_id DESC    
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        if ($postList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($postList as &$val) {
                $val['nearschool_status'] = $status[$val['nearschool_status']];
                if ($val['nearschool_type'] == '0') {
                    $val['type'] = $this->LgStringSwitch('集团创建');
                } else {
                    $val['type'] = $this->LgStringSwitch('学校自建');
                }
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(n.nearschool_id) as a
            FROM
                crm_code_nearschool AS n
            WHERE
                {$datawhere} and n.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('nearschool_name', 'nearschool_shortname', 'nearschool_address', 'nearschool_status', 'type');
        $fieldname = array('附近校区名称', '简称', '学校地址', '开启状态', '来源');
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 1, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldstring[$i]));
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无附近学校信息", 'result' => $result);
        }

        return $res;
    }

    //添加附近学校
    function addnearschoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['near']), true);
        foreach ($schoolList as $item) {
            $data['nearschool_name'] = $item['nearschool_name'];
            $data['nearschool_shortname'] = $item['nearschool_shortname'];
            $data['nearschool_address'] = $item['nearschool_address'];
            $data['school_id'] = $paramArray['school_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['nearschool_type'] = '1';

            $a = $this->DataControl->getFieldOne('crm_code_nearschool', 'nearschool_id', "nearschool_name = '{$item['nearschool_name']}' and school_id = '{$paramArray['school_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
            }
            $this->DataControl->insertData('crm_code_nearschool', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加附近学校成功", 'result' => array());

        return $res;
    }

    //编辑附近学校
    function updatenearschoolAction($paramArray)
    {
        $classroomOne = $this->DataControl->getFieldOne("crm_code_nearschool", "nearschool_id", "nearschool_id = '{$paramArray['nearschool_id']}'");
        if ($classroomOne) {
            $data = array();
            $data['nearschool_name'] = $paramArray['nearschool_name'];
            $data['nearschool_address'] = $paramArray['nearschool_address'];
            $data['nearschool_shortname'] = $paramArray['nearschool_shortname'];

            $field = array();
            $field['nearschool_name'] = $this->LgStringSwitch("校区名称");
            $field['nearschool_shortname'] = $this->LgStringSwitch("学校简称");
            $field['nearschool_address'] = $this->LgStringSwitch("地址");
            if ($this->DataControl->updateData("crm_code_nearschool", "nearschool_id = '{$paramArray['nearschool_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑附近学校成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑附近学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除附近学校
    function delnearschoolAction($paramArray)
    {
        $CommodeOne = $this->DataControl->getFieldOne("crm_code_nearschool", "nearschool_id", "nearschool_id = '{$paramArray['nearschool_id']}'");
        if ($CommodeOne) {
            if ($this->DataControl->delData("crm_code_nearschool", "nearschool_id = '{$paramArray['nearschool_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除附近学校成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除附近学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function nearschoolStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_code_nearschool", "nearschool_id", "nearschool_id = '{$paramArray['nearschool_id']}'");

        if ($activityOne) {
            $data = array();
            $data['nearschool_status'] = $paramArray['nearschool_status'];

            $field = array();
            $field['nearschool_status'] = $this->LgStringSwitch('是否启用');

            if ($this->DataControl->updateData("crm_code_nearschool", "nearschool_id = '{$paramArray['nearschool_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    /**
     * 获取常用的上课时间
     * author: ling
     * 对应接口文档 0001
     */
    function getHourTimeList($request)
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $list = $this->DataControl->selectClear("select hourtime_id,hourtime_starttime,hourtime_endtime from smc_code_hourtime where school_id='{$request['school_id']}' limit {$pagestart},{$num} ");
        if (!$list) {
            $list = array();
        }
        $allnum = $this->DataControl->selectOne("select hourtime_id from  smc_code_hourtime where  school_id='{$request['school_id']}' ");
        $datalist['list'] = $list;
        $datalist['num'] = $allnum == false ? 0 : count($allnum);
        return $datalist;
    }

    function addHourTime($request)
    {
        $request['hourtime_list'] = stripslashes($request['hourtime_list']);
        if ($hourtimeList = json_decode($request['hourtime_list'], true)) {
            foreach ($hourtimeList as $key => $value) {
                if ($value['hourtime_starttime'] && $value['hourtime_endtime']) {
                    $data = array();
                    $data['hourtime_starttime'] = strlen($value["hourtime_starttime"]) == 4 ? '0' . $value["hourtime_starttime"] : $value["hourtime_starttime"];
                    $data['hourtime_endtime'] = strlen($value["hourtime_endtime"]) == 4 ? '0' . $value["hourtime_endtime"] : $value["hourtime_endtime"];
                    $data['school_id'] = $request["school_id"];
                    $this->DataControl->insertData("smc_code_hourtime", $data);
                }
            }
        }
        return true;
    }

    function editHourTime($request)
    {
        $data = array();
        $data['hourtime_starttime'] = strlen($request["hourtime_starttime"]) == 4 ? '0' . $request["hourtime_starttime"] : $request["hourtime_starttime"];
        $data['hourtime_endtime'] = strlen($request["hourtime_endtime"]) == 4 ? '0' . $request["hourtime_endtime"] : $request["hourtime_endtime"];
        $bool = $this->DataControl->updateData("smc_code_hourtime", "hourtime_id='{$request['hourtime_id']}'", $data);
        return $bool;
    }

    function delHourTime($request)
    {
        $bool = $this->DataControl->delData("smc_code_hourtime", "hourtime_id='{$request['hourtime_id']}'");
        return $bool;
    }

    //获取集团教室
    function getCompanyClassroomApi($paramArray)
    {
        $school = $this->DataControl->selectClear("SELECT g.classroom_cnname,g.gclassroom_id from gmc_code_classroom as g WHERE g.company_id = '{$paramArray['company_id']}' and g.classroom_status = '1' AND g.gclassroom_id not in (select gclassroom_id from smc_classroom as s where s.school_id = '{$paramArray['school_id']}')");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    function getStafferFieldSet($request)
    {
        $setOne = $this->DataControl->selectOne("SELECT fieldset_text 
          from gmc_user_fieldset 
          where company_id = '{$request['company_id']}' 
          and staffer_id='{$request['staffer_id']}' 
          and fieldset_url='{$request['fieldset_url']}' 
          order by fieldset_createtime desc limit 0,1");
        if ($setOne) {
            $result = array();
            $result["data"] = $setOne;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }
        return $res;
    }

    function saveStafferFieldSet($request)
    {
        $setOne = $this->DataControl->selectOne("SELECT fieldset_id 
          from gmc_user_fieldset 
          where company_id = '{$request['company_id']}' 
          and staffer_id='{$request['staffer_id']}' 
          and fieldset_url='{$request['fieldset_url']}' 
          order by fieldset_createtime desc limit 0,1");
        if ($setOne) {
            $data = array();
            $data['fieldset_text'] = $request["fieldset_text"];
            $data['fieldset_createtime'] = time();
            $bool = $this->DataControl->updateData("gmc_user_fieldset", "fieldset_id='{$setOne['fieldset_id']}'", $data);
        } else {
            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['staffer_id'] = $request['staffer_id'];
            $data['fieldset_url'] = $request["fieldset_url"];
            $data['fieldset_text'] = $request["fieldset_text"];
            $data['fieldset_createtime'] = time();
            $bool = $this->DataControl->insertData("gmc_user_fieldset", $data);
        }
        return $bool;
    }

    function getAchieveTarget($request)
    {
        $datawhere = " 1 ";

        $datawhere .= " and A.company_id='{$request['company_id']}'";

        $datawhere .= " and A.school_id='{$request['school_id']}'";

        if (isset($request['start_year']) && $request['start_year'] !== '') {
            $datawhere .= " AND A.target_year >= '{$request['start_year']}'";
        }

        if (isset($request['end_year']) && $request['end_year'] !== '') {
            $datawhere .= " AND A.target_year <= '{$request['end_year']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['school_settle']) && $request['school_settle'] !== '') {
            $datawhere .= " and A.school_settle='{$request['school_settle']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.school_id
                ,(case when b.school_shortname='' then b.school_cnname else b.school_shortname end) as school_name
                ,b.school_branch
                ,a.coursetype_id
                ,c.coursetype_cnname
                ,a.target_year
                ,a.school_settle
                ,(select count(1) from smc_achieve_monthlytarget where school_id=a.school_id and coursetype_id=a.coursetype_id and target_year=a.target_year) as target_settle
                ,a.register_num
                ,a.reading_num
                ,a.losing_num
                from  smc_achieve_target a
                left join smc_school b on a.school_id=b.school_id
                left join smc_code_coursetype c on a.coursetype_id=c.coursetype_id
                where {$datawhere}
                and b.school_istest<>'1'
                order by a.target_year desc,c.coursetype_branch
                ";

        $settle_status = $this->LgArraySwitch(array("-1" => "未设置", "0" => "无需设置", "1" => "已设置"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无目标设定数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['target_year'] = $dateexcelvar['target_year'];
                    $datearray['school_settle_name'] = $settle_status[$dateexcelvar['school_settle']];
                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $sql = "SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                    FROM smc_student_registerinfo AS a
                    WHERE 1
                    and a.info_status=1
                    and a.school_id = '{$dateexcelvar['school_id']}'
                    and a.coursetype_id = '{$dateexcelvar['coursetype_id']}'
                    and FROM_UNIXTIME(a.pay_successtime,'%Y')='{$dateexcelvar['target_year']}'  ";

                    $regiList = $this->DataControl->selectClear($sql);
                    if ($regiList) {
                        $regi_num = count($regiList);
                    } else {
                        $regi_num = 0;
                    }
                    if ($dateexcelvar['target_year'] > date('Y')) {
                        $datearray['register_rate'] = '--';
                    } else {
                        $datearray['register_rate'] = ($dateexcelvar['register_num'] > 0) ? ceil($regi_num / $dateexcelvar['register_num'] * 100) . '%' : '--';
                    }

                    $datearray['reading_num'] = $dateexcelvar['reading_num'];
                    $today = $dateexcelvar['target_year'] . '-12-31';
                    if ($today > date("Y-m-d")) {
                        $today = date("Y-m-d");
                    }
                    $sql = "SELECT A.school_id,
                    A.student_id,
                    C.coursetype_id,
                    MIN(A.study_beginday) AS beginday,
                    MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                    FROM smc_student_study A
                    LEFT JOIN smc_class B ON A.class_id=B.class_id 
                    LEFT JOIN smc_course C ON B.course_id=C.course_id 
                    LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                    WHERE A.company_id='{$request['company_id']}'
                    and A.school_id='{$dateexcelvar['school_id']}'
                    and C.coursetype_id='{$dateexcelvar['coursetype_id']}'
                    AND B.class_type='0' 
                    AND B.class_status>'-2'
                    AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                    GROUP BY A.school_id,A.student_id,C.coursetype_id
                    HAVING beginday<='{$today}' and endday>='{$today}'  ";

                    $readList = $this->DataControl->selectClear($sql);
                    if ($readList) {
                        $read_num = count($readList);
                    } else {
                        $read_num = 0;
                    }

                    if ($dateexcelvar['target_year'] > date('Y')) {
                        $datearray['reading_rate'] = '--';
                    } else {
                        $datearray['reading_rate'] = ($dateexcelvar['reading_num'] > 0) ? ceil($read_num / $dateexcelvar['reading_num'] * 100) . '%' : '--';
                    }

                    $datearray['losing_num'] = $dateexcelvar['losing_num'];
                    $sql = "
                    select A.student_id
                    ,min(A.changelog_day) as lost_date
                    from smc_student_changelog A
                    left join smc_student_change B on B.change_pid=A.change_pid
                    where A.company_id='{$request['company_id']}'
                    and A.school_id='{$dateexcelvar['school_id']}'
                    and A.stuchange_code='C04'
                    and A.coursetype_id='{$dateexcelvar['coursetype_id']}'
                    and year(A.changelog_day)='{$dateexcelvar['target_year']}' 
                    group by a.student_id ";
                    $lostList = $this->DataControl->selectClear($sql);
                    if ($lostList) {
                        $lost_num = count($lostList);
                    } else {
                        $lost_num = 0;
                    }
                    if ($dateexcelvar['target_year'] > date('Y')) {
                        $datearray['losing_rate'] = '--';
                    } else {
                        $datearray['losing_rate'] = ($dateexcelvar['losing_num'] > 0) ? ceil($lost_num / $dateexcelvar['losing_num'] * 100) . '%' : '--';
                    }
                    $datearray['losing_rate'] = $dateexcelvar['losing_num'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学校名称", "学校编号", "目标所属班组", "目标年度", "目标状态", "年度招生目标", "年度招生目标达成率", "年度在读目标", "年度在读目标达成率", "年度流失限额", "年度流失限额率"));
            $excelfileds = array('school_name', 'school_branch', 'coursetype_cnname', "target_year", 'school_settle_name', "register_num", "register_rate", "reading_num", "reading_rate", 'losing_num', 'losing_rate');

            $tem_name = $this->LgStringSwitch('校园目标管理表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $targetList = $this->DataControl->selectClear($sql);

            if (!$targetList) {
                $this->error = true;
                $this->errortip = "无目标设定数据";
                return false;
            }

            foreach ($targetList as &$var) {
                $var['school_settle_name'] = $settle_status[$var['school_settle']];

                $sql = "SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                FROM smc_student_registerinfo AS a
                WHERE 1
                and a.info_status=1
                and a.school_id = '{$var['school_id']}'
                and a.coursetype_id = '{$var['coursetype_id']}'
                and FROM_UNIXTIME(a.pay_successtime,'%Y')='{$var['target_year']}'  ";

                $regiList = $this->DataControl->selectClear($sql);
                if ($regiList) {
                    $regi_num = count($regiList);
                } else {
                    $regi_num = 0;
                }

                $today = $var['target_year'] . '-12-31';
                if ($today > date("Y-m-d")) {
                    $today = date("Y-m-d");
                }
                $sql = "SELECT A.school_id,
                A.student_id,
                C.coursetype_id,
                MIN(A.study_beginday) AS beginday,
                MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                WHERE A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and C.coursetype_id='{$var['coursetype_id']}'
                AND B.class_type='0' 
                AND B.class_status>'-2'
                AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                GROUP BY A.school_id,A.student_id,C.coursetype_id
                HAVING beginday<='{$today}' and endday>='{$today}'  ";

                $readList = $this->DataControl->selectClear($sql);
                if ($readList) {
                    $read_num = count($readList);
                } else {
                    $read_num = 0;
                }

                $sql = "
                select A.student_id
                ,min(A.changelog_day) as lost_date
                from smc_student_changelog A
                left join smc_student_change B on B.change_pid=A.change_pid
                where A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and A.stuchange_code='C04'
                and A.coursetype_id='{$var['coursetype_id']}'
                and year(A.changelog_day)='{$var['target_year']}' 
                group by a.student_id ";
                $lostList = $this->DataControl->selectClear($sql);
                if ($lostList) {
                    $lost_num = count($lostList);
                } else {
                    $lost_num = 0;
                }
                if ($var['target_year'] > date('Y')) {
                    $var['register_rate'] = '--';
                    $var['reading_rate'] = '--';
                    $var['losing_rate'] = '--';
                } else {
                    $var['register_rate'] = ($var['register_num'] > 0) ? ceil($regi_num / $var['register_num'] * 100) . '%' : '--';
                    $var['reading_rate'] = ($var['reading_num'] > 0) ? ceil($read_num / $var['reading_num'] * 100) . '%' : '--';
                    $var['losing_rate'] = ($var['losing_num'] > 0) ? ceil($lost_num / $var['losing_num'] * 100) . '%' : '--';
                }
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select 
                a.school_id
                ,a.coursetype_id
                ,a.target_year
                ,(select count(1) from smc_achieve_monthlytarget where school_id=a.school_id and coursetype_id=a.coursetype_id and target_year=a.target_year) as target_settle
                from  smc_achieve_target a
                left join smc_school b on a.school_id=b.school_id
                left join smc_code_coursetype c on a.coursetype_id=c.coursetype_id
                where {$datawhere}
                and b.school_istest<>'1'
                ");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $targetList;

            return $data;
        }
    }

    function getMonthAchieveTarget($request)
    {
        $datawhere = " 1 ";

        $datawhere .= " and A.company_id='{$request['company_id']}'";

        $datawhere .= " and A.school_id='{$request['school_id']}'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['target_year']) && $request['target_year'] !== '') {
            $datawhere .= " AND A.target_year = '{$request['target_year']}'";
        }

        $sql = "select MONTH(concat(a.target_month,'-01')) as month_sort,a.target_month,a.register_num,a.reading_num,a.losing_num,a.school_id,a.coursetype_id
                from smc_achieve_monthlytarget a
                where {$datawhere}
                order by a.target_month
                ";

        $monthList = $this->DataControl->selectClear($sql);

        if (!$monthList) {
            $this->error = true;
            $this->errortip = "无目标数据";
            return false;
        }

        foreach ($monthList as &$var) {
            if ($var['target_month'] > date('Y-m')) {
                $var['register_rate'] = '--';
                $var['reading_rate'] = '--';
                $var['losing_rate'] = '--';
                continue;
            }

            $sql = "SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                FROM smc_student_registerinfo AS a
                WHERE 1
                and a.info_status=1
                and a.school_id = '{$var['school_id']}'
                and a.coursetype_id = '{$var['coursetype_id']}'
                and FROM_UNIXTIME(a.pay_successtime,'%Y-%m')='{$var['target_month']}'  ";

            $regiList = $this->DataControl->selectClear($sql);
//            var_dump($sql);
            if ($regiList) {
                $regi_num = count($regiList);
            } else {
                $regi_num = 0;
            }

//            $var['regi_num'] =$regi_num;
            $today = $var['target_month'] . '-01';
            $today = date('Y-m-t', strtotime($today));
            if ($today > date("Y-m-d")) {
                $today = date("Y-m-d");
            }
            $sql = "SELECT A.school_id,
                A.student_id,
                C.coursetype_id,
                MIN(A.study_beginday) AS beginday,
                MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                WHERE A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and C.coursetype_id='{$var['coursetype_id']}'
                AND B.class_type='0' 
                AND B.class_status>'-2'
                AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                GROUP BY A.school_id,A.student_id,C.coursetype_id
                HAVING beginday<='{$today}' and endday>='{$today}'  ";

            $readList = $this->DataControl->selectClear($sql);
            if ($readList) {
                $read_num = count($readList);
            } else {
                $read_num = 0;
            }

            $sql = "select A.student_id
                ,min(A.changelog_day) as lost_date
                from smc_student_changelog A
                left join smc_student_change B on B.change_pid=A.change_pid
                where A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and A.coursetype_id='{$var['coursetype_id']}'
                and A.stuchange_code='C04'
                and DATE_FORMAT((A.changelog_day),'%Y-%m')='{$var['target_month']}' 
                group by a.student_id ";
            $lostList = $this->DataControl->selectClear($sql);
            if ($lostList) {
                $lost_num = count($lostList);
            } else {
                $lost_num = 0;
            }
            $var['register_rate'] = ($var['register_num'] > 0) ? ceil($regi_num / $var['register_num'] * 100) . '%' : '--';
            $var['reading_rate'] = ($var['reading_num'] > 0) ? ceil($read_num / $var['reading_num'] * 100) . '%' : '--';
            $var['losing_rate'] = ($var['losing_num'] > 0) ? ceil($lost_num / $var['losing_num'] * 100) . '%' : '--';
        }

        $data = array();
        $data['list'] = $monthList;

        return $data;
    }

    function getMonthTarget($request)
    {
        $yearTarget = $this->DataControl->selectOne("SELECT a.register_num,a.reading_num,a.losing_num 
          from smc_achieve_target a
          where company_id = '{$request['company_id']}' 
          and school_id='{$request['school_id']}' 
          and coursetype_id='{$request['coursetype_id']}' 
          and target_year='{$request['target_year']}' 
          order by school_settle desc limit 0,1");
        if (!$yearTarget) {
            $this->error = true;
            $this->errortip = "获取月度目标失败";
            return false;
        }

        $register_num = $yearTarget['register_num'];
        $reading_num = $yearTarget['reading_num'];
        $losing_num = $yearTarget['losing_num'];

        $sql = "select MONTH(concat(a.target_month,'-01')) as month_sort,a.target_month,a.register_num,a.reading_num,a.losing_num
                from smc_achieve_monthlytarget a
                where A.company_id='{$request['company_id']}'
                and A.school_id='{$request['school_id']}'
                and A.coursetype_id='{$request['coursetype_id']}'
                AND A.target_year = '{$request['target_year']}'
                order by a.target_month
                ";

        $monthList = $this->DataControl->selectClear($sql);

        if (!$monthList) {
            $monthList = array();
            for ($i = 1; $i <= 12; $i++) {
                $monthone = array();
                $monthone['month_sort'] = $i;
                $monthone['target_month'] = date('Y-m', strtotime($request['target_year'] . '-' . $i . '-01'));
                $monthone['register_num'] = '';
                $monthone['reading_num'] = $reading_num;
                $monthone['losing_num'] = '';
                $monthList[] = $monthone;
            }
        }

        $fieldstring = array('month_sort', 'target_month', 'register_num', 'reading_num', 'losing_num');
        $fieldname = array("月度", "月份", "招生目标\r\n" . "总" . $register_num . "人", "在读目标\r\n" . "总" . $reading_num . "人", "流失限额\r\n" . "总" . $losing_num . "人");
        $fieldcustom = array("1", "0", "1", "1", "1");
        $fieldshow = array("1", "0", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $data = array();
        $data['field'] = $field;
        $data['list'] = $monthList;
        $data['allnum'] = count($monthList);

        return $data;
    }

    function editMonthTarget($request)
    {
        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $newlist = array();

        $sql = "select a.monthlytarget_id
                ,MONTH(concat(a.target_month,'-01')) as month_sort
                ,a.target_month 
                ,a.register_num 
                ,a.reading_num 
                ,a.losing_num 
                from smc_achieve_monthlytarget a 
                where a.target_year='{$request['target_year']}' 
                and a.coursetype_id='{$request['coursetype_id']}' 
                and a.school_id='{$request['school_id']}' 
                order by a.target_month 
                ";

        $oldarray = $this->DataControl->selectClear($sql);

        $this->DataControl->begintransaction();
        foreach ($List as $item) {
            if (!is_numeric($item['register_num'])) {
                $item['register_num'] = 0;
            }
            if (!is_numeric($item['reading_num'])) {
                $item['reading_num'] = 0;
            }
            if (!is_numeric($item['losing_num'])) {
                $item['losing_num'] = 0;
            }
            if (!is_int($item['register_num'] * 1) || !is_int($item['reading_num'] * 1) || !is_int($item['losing_num'] * 1)) {
                $fal++;
                continue;
            }

            if ($oldarray) {
                foreach ($oldarray as $olditem) {
                    if ($olditem['month_sort'] == $item['month_sort']) {
                        if ($olditem['register_num'] !== $item['register_num']
                            || $olditem['reading_num'] !== $item['reading_num']
                            || $olditem['losing_num'] !== $item['losing_num']) {
                            $monthdata = array();
                            $monthdata['register_num'] = $item['register_num'];
                            $monthdata['reading_num'] = $item['reading_num'];
                            $monthdata['losing_num'] = $item['losing_num'];
                            $res = $this->DataControl->updateData('smc_achieve_monthlytarget', "monthlytarget_id='{$olditem['monthlytarget_id']}'", $monthdata);

                            if ($res) {
                                $suc++;
                            } else {
                                $fal++;
                            }
                        }
                    }
                }
            } else {
                $monthdata = array();
                $monthdata['company_id'] = $request['company_id'];
                $monthdata['school_id'] = $request['school_id'];
                $monthdata['coursetype_id'] = $request['coursetype_id'];
                $monthdata['target_year'] = $request['target_year'];
                $monthdata['target_month'] = $item['target_month'];
                $monthdata['register_num'] = $item['register_num'];
                $monthdata['reading_num'] = $item['reading_num'];
                $monthdata['losing_num'] = $item['losing_num'];
                $res = $this->DataControl->insertData('smc_achieve_monthlytarget', $monthdata);

                if ($res) {
                    $suc++;
                } else {
                    $fal++;
                }
            }
        }

        if ($fal == 0) {
            $this->errortip = "设定成功";
            $datas = array();
            $datas['school_settle'] = '1';
            $this->DataControl->updateData("smc_achieve_target", "target_year='{$request['target_year']}' and coursetype_id='{$request['coursetype_id']}' and school_id='{$request['school_id']}' ", $datas);

            $this->DataControl->commit();
        } else {
            $this->errortip = "设定失败";
            $this->DataControl->rollback();
        }
        return ($fal == 0);
    }

}

