<?php

namespace Model\Smc;


class AutoHandleModel extends modelTpl
{

    function autoHandleForward()
    {

        $sql = "select a.config_value from smc_forward_application_config as a where a.config_key='auto_cancel_days'";
        $autoCancelDays = $this->DataControl->selectOne($sql);

        if($autoCancelDays){
            $autoCancelDays = $autoCancelDays['config_value'];
        }else{
            $autoCancelDays = 0;
        }

        $sql = "SELECT a.application_id,a.task_id,a.task_status,a.execute_time,a.task_type,b.application_status,b.application_id,b.student_id,b.course_id,b.class_id,b.company_id,b.school_id,b.apply_staffer_id,b.forward_reason
                from smc_forward_application_task as a 
                inner join smc_forward_application as b on b.application_id=a.application_id
                where a.task_status=0 and b.application_status=1
                and a.execute_time<".time();
        $taskList = $this->DataControl->selectClear($sql);

        if($taskList){
            foreach($taskList as $taskOne){
                $this->autoHandleForwardOne($taskOne);
            }
        }

        return true;
    }

    function autoHandleForwardOne($taskOne)
    {

        switch($taskOne['task_type']){
            case 'auto_forward':
                $this->auto_forwardOne($taskOne);
                break;
            case 'auto_cancel':
                $this->auto_cancelOne($taskOne);
                break;
            default:
                break;
        }


    }

    function auto_forwardOne($taskOne){

        $publicArr=array();
        $publicArr['company_id']=$taskOne['company_id'];
        $publicArr['school_id']=$taskOne['school_id'];
        $publicArr['staffer_id']=$taskOne['apply_staffer_id'];

        $BalanceModel = new \Model\Smc\BalanceModel($publicArr);

        $bool=$BalanceModel->carryOver($taskOne['student_id'], $taskOne['course_id'], $taskOne['class_id'], '', strtotime($taskOne['out_class_date']),'', $taskOne['forward_reason'],1,$taskOne['application_id']);

        if($bool){
            $this->DataControl->updateData("smc_forward_application_task","task_id='{$taskOne['task_id']}'",array('task_status'=>1));
        }
    }

    function auto_cancelOne($taskOne){
        $publicArr=array();
        $publicArr['company_id']=$taskOne['company_id'];
        $publicArr['school_id']=$taskOne['school_id'];
        $publicArr['staffer_id']=$taskOne['apply_staffer_id'];

        $data = array();
        $data['application_status'] = 3;
        $data['cancel_reason'] = '自动取消';
        $data['cancel_time'] = time();
        $data['update_time'] = time();
        if($this->DataControl->updateData("smc_forward_application", "application_id='{$taskOne['application_id']}'", $data)){
            $ForwardModel = new \Model\Smc\ForwardModel($publicArr);

            $this->DataControl->updateData("smc_forward_application_task","task_id='{$taskOne['task_id']}'",array('task_status'=>1));

            $ForwardModel->createApplicationTrack($taskOne['application_id'], '自动取消', '自动取消结转申请', 
                '超过设置的自动取消时间', $publicArr['staffer_id'], 'cancel');
        }
    }



}