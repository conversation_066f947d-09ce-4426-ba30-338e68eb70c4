<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Smc;

class KsMonitorModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();
    //旷视 摄像头参数
    const KsHost = 'https://www.kuangshi.com';

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //put
    public function curlPut($url,$data){
        //将数据转换为JSON格式
        $data_string = json_encode($data);
        //初始化curl请求
        $curl = curl_init();
        //设置curl选项
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data_string))
        );
        //执行curl请求
        $response = curl_exec($curl);
        //关闭curl请求
        curl_close($curl);
        //输出响应信息
        return $response;
    }
    //get
    public function curlGet($url,$Authorization,$json=0,$options=array()){
        if($Authorization) {
            $header = array(
                'Authorization:' . $Authorization
            );
        }
        if($json == '1') {
            $headers[] = 'Content-Type: application/json';
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    //post
    public function curlPost($url,$post_data = array(),$Authorization){
        $header  = array(
            'Authorization:'.$Authorization,
        );
        if (is_array($post_data))
        {
            $post_data = http_build_query($post_data, null, '&');
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch , CURLOPT_URL , $url);
        curl_setopt($ch , CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch , CURLOPT_POST, 1);
        curl_setopt($ch , CURLOPT_POSTFIELDS, $post_data);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
    //post curl raw json
    public function curlRawJsonPost($url,$post_data = array(),$Authorization){
        $httph = curl_init($url);
        curl_setopt($httph, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($httph, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($httph, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($httph, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)");

        $postdatajson = json_encode($post_data);

        $headers = array();
        if($Authorization) {
            $headers[] = 'Authorization:' . $Authorization;
        }
        $headers[] = 'Content-Type: application/json';

        curl_setopt($httph, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($httph, CURLOPT_POST, 1);//设置为POST方式
        curl_setopt($httph, CURLOPT_POSTFIELDS, $postdatajson);

        curl_setopt($httph, CURLOPT_CONNECTTIMEOUT, 3);//设置超时时间
        $rst = curl_exec($httph);

        //检查是否404（网页找不到）
        $httpCode = curl_getinfo($httph, CURLINFO_HTTP_CODE);
        // var_dump($httpCode);
        if ($httpCode == 404) {
            return false;
        } else {
//            return $rst;
            return true;
        }
        curl_close($httph);
    }

    //-------------- 旷视  我格广场校测试 ----------------- 开始
    //旷世 -- 获取RSA密钥对
    function getRsaOne(){
        $config = [
            'default_md' => "sha256",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];
        if (PHP_OS === 'WINNT') {
            if($_SERVER['SERVER_NAME'] == 'smcapi.schoolapi102.com'){
                $config['config'] = 'E:\phpStudyV8\Extensions\php\php5.6.27nts\extras\ssl\openssl.cnf';
            }elseif($_SERVER['SERVER_NAME'] == "smcapi.kcclassin.com"){
                $config['config'] = 'D:\xampp\php\extras\ssl\openssl.cnf';
            }
        }
        $res = openssl_pkey_new($config);

        $result = array();

        if ($res === false) {
            $this->error = 1;
            $this->errortip = "生成RSA密钥对时出错!";
            $this->result = array();
            return false;
        } else {
            // 提取私钥
            openssl_pkey_export($res, $privateKey,NULL,$config);
            // 提取公钥
            $publicKeyDetails = openssl_pkey_get_details($res);
            $publicKey = $publicKeyDetails["key"];

            // 打印私钥和公钥
            echo "Private Key:\n";
            echo $privateKey;
            echo "\nPublic Key:\n";
            echo $publicKey;

            $result['Private'] = $privateKey;//私钥
            $result['Public'] = $publicKey;//公钥

            $this->error = 0;
            $this->errortip = "生成RSA密钥对成功!";
            $this->result = $result;
            return true;
        }
    }
    //旷世 -- 获取RSA密钥对
    function getHashPass($pwd='JDB240325',$salt='',$challenge=''){
        if($pwd && $salt && $challenge){
            $passcode = $pwd.$salt.$challenge;
            return hash("sha256", $passcode);
        }else{
            return false;
        }
        //echo hash("sha256", 'admin1236923qx5z9yv3d2k5zx915apze209iyp7rhdjf6l25t3t47om9xqd6353vjy3831lmnnq3w0s10d3ig6x2h10xnk9x2esg2');
    }

    //测试 设备激活操作
    function ceshi($request){
        //获取设备是否已激活
        $qcode = "https://www.kuangshi.com/auth/activation";
        $datajson = $this->curlGet($qcode,'',1);
        $dataArray = json_decode($datajson, true);
        if($dataArray['data']['activated']){
            //生成RSA密钥对
            $RsaOne = $this->getRsaOne();
            if($RsaOne){
                if($RsaOne['Public'] && $RsaOne['Private']){
                    //客户端生成长度1024bit的RSA密钥对（公钥的指数默认为65537），将公钥的模数通过两次base64编码得到public_key，通过激活鉴权请求将public_key发送给设备端
                    $public_key = base64_encode(base64_encode($RsaOne['Public']));
                    //设备端会生成AES对称密钥（AES填充方式为"AES/CBC/PKCS7Padding"，初始化向量iv固定为"0123456789abcdef"），用RSA公钥加密AES对称密钥得到challenge，返回给客户端。
                    $url = "https://www.kuangshi.com/auth/activation";
                    $postdata = array();
                    $postdata['public_key'] = $public_key;
                    $challengejson = $this->curlRawJsonPost($url, $postdata);
                    //客户端对challenge进行base64解码以及RSA私钥解密后得到对称密钥，然后用对称密钥加密用户密码明文得到password，通过激活请求将username和加密后的password发送给设备端，设备端激活成功后返回code=0
                    //解码base64， challenge 鉴权挑战接口返回的字段
                    $challengeArray = base64_decode($challengejson);
                    $challenge = $challengeArray['data']['challenge'];

                    if($challenge) {
                        //使用私钥解密数据，并将结果存储在$decrypted中  //rsa解密，得到对称密钥dec
                        openssl_private_decrypt($challenge, $decrypted, $RsaOne['Private']);
                        //用对称密钥按照AES算法加密用户密码原文，再base64后，就得到了password。
                        $adminpass = 'JDB240325';
                        $aes = new \Aesencdec($decrypted, '0123456789abcdef', 'AES-128-CBC');
                        $aesjmpass = $aes->encrypt($adminpass);//加密
                        $password = base64_encode($aesjmpass);
                        if ($password) {
                            $username = 'admin';//用户名
                            $putdata = array();
                            $putdata['username'] = $username;//用户名
                            $putdata['password'] = $password;//密码

                            $url = "https://www.kuangshi.com/auth/activation";
                            $putResultJson = $this->curlPut($url, $putdata);
                            if($putResultJson){
                                //判断状态码给到 提示

                            }else{
                                //判断状态码给到 提示

                            }
                        }else{
                            $this->error = 1;
                            $this->errortip = "密码加密失败!";
                            $this->result = array();
                            return false;
                        }
                    }else{
                        $this->error = 1;
                        $this->errortip = "对称密钥获取时出错!";
                        $this->result = array();
                        return false;
                    }
                }else{
                    $this->error = 1;
                    $this->errortip = "生成RSA公钥、私钥对时出错!";
                    $this->result = array();
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "生成RSA密钥对时出错!";
                $this->result = array();
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "设备是否已激活获取失败!";
            $this->result = array();
            return false;
        }
    }
    //旷世 设备登录
    function loginKs($request){
        //登录用户名
        $username = "admin";

        //获取设备是否已激活
        $OneUrl = self::KsHost."/auth/login/challenge";
        $options = ["username"=>$username];//登录用户名
        $datajson = $this->curlGet($OneUrl,'',1,$options);
        $dataArray = json_decode($datajson, true);
        if($dataArray['code'] == '0'){

            //登录设备
            $session_id = $dataArray['data']['session_id'];
            $challenge = $dataArray['data']['challenge'];
            $salt = $dataArray['data']['salt'];
            $password = "JDB240325";

            //计算password字段:遵循算法: hash(pwd+salt+ challenge)，hash采用SHA2-256 算法，pwd为实际密码，salt、challenge为鉴权挑战接口返回的相应值。
            $HasPassword = $this->getHashPass($password,$salt,$challenge);
            $TwoUrl = self::KsHost."/auth/login";
            $postdata = array();
            $postdata['session_id'] = $session_id;
            $postdata['username'] = $username;
            $postdata['password'] = $HasPassword;
            //$postdata['ciphertext'] = "";//经过AES加密的明文密码，提供给设备做密码比对，暂时只有W5K使用。
            $loginjson = $this->curlRawJsonPost($TwoUrl, $postdata);
            $loginArray = base64_decode($loginjson);
            if($loginArray['code'] == '0'){
                //xxxxxxxxxxxxxxxxxxxxxxxxxxxx


            }
        }

    }








    //-------------- 旷视  我格广场校测试 ----------------- 结束






}
