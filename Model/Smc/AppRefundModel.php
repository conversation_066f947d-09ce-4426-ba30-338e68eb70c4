<?php


namespace Model\Smc;

class AppRefundModel extends modelTpl
{
	public $error = false;
	public $errortip = false;
	public $oktip = false;//正确提示
	public $bakerrorfuc = "errormotify";
	public $result = array();
	public $stafferOne = array();//操作人
	public $company_id = 0;//操作公司
	public $school_id = 0;//操作学校
	public $staffer_id = 0;//操作人
	public $publicarray = array();

	function __construct($publicarray=array()) {
		parent::__construct ();
		if(is_array($publicarray)){
			$this->setPublic($publicarray);
			$this->publicarray = $publicarray;
		}
	}

	function setPublic($publicarray){
		if(isset($publicarray['company_id'])){
			$this->company_id = $publicarray['company_id'];
		}else{
			$this->error = true;
			$this->errortip = "企业ID必须传入";
			return false;
		}
		if(isset($publicarray['school_id'])){
			$this->school_id = $publicarray['school_id'];
		}else{
			$this->error = true;
			$this->errortip = "学校ID必须传入";
			return false;
		}
		if(isset($publicarray['staffer_id'])){
			$this->verdictStaffer($publicarray['staffer_id']);
			$this->staffer_id=$publicarray['staffer_id'];
		}else{
			$this->error = true;
			$this->errortip = "操作ID必须传入";
			return false;
		}
	}
	//验证订单信息
	function verdictStaffer($staffer_id){

		$this->stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname,staffer_enname,staffer_mobile","staffer_id = '{$staffer_id}'");

		if(!$this->stafferOne){
			$this->error = true;
			$this->errortip = "教师信息不存在";
			return false;
		}else{
			return true;
		}
	}
	
	
	//查询当天app退款订单 //app 必须先搜所关键字
	function   getRefundList($request)
	{
		$datawehre = "1";
		if(isset($request['keyword']) && $request['keyword']  !=""  )
		{
			$datawehre .=" and (po.order_pid like '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')   ";
		}
		$startDay = strtotime(date("Y-m-d"),time()) ;
		$endDay = $startDay + 60*60*24;
		
		$un_sql  = "select po.order_pid,
 				(select sc.coursebalance_time from  smc_student_coursebalance as sc where sc.student_id = po.student_id and sc.course_id = oc.course_id limit 0,1 ) as  coursebalance_time,
 				(select scp.pricinglog_buytimes  from  smc_student_coursebalance_pricinglog as  scp where  scp.student_id = po.student_id and scp.course_id = oc.course_id  order by pricinglog_createtime Desc limit 0,1) as  pricinglog_buytimes
 				from  smc_payfee_order as po
				left join smc_payfee_order_course as oc On oc.order_pid =po.order_pid
				left join smc_student as s On s.student_id = po.student_id
				where
				 {$datawehre} and po.order_type = 0 and po.school_id ='{$request['school_id']}' and po.company_id = '{$request['company_id']}'  and po.order_createtime >='{$startDay}' and  po.order_createtime <='{$endDay}' and po.order_status>0
				group by po.order_pid
				Having  pricinglog_buytimes <> coursebalance_time
	";
		
		 $un_order= $this->DataControl->selectClear($un_sql);
		 
		
		 if($un_order)
		 {
			 $arr_order_pid = array_column($un_order, 'order_pid');
			 $str_order_pid = trim(implode(',', $arr_order_pid), ',');
		 }else{
			 $str_order_pid = "0";
		 }
		
		$sql  = "select po.order_id,po.order_pid,s.student_cnname,s.student_enname,s.student_branch,po.order_createtime from smc_payfee_order as po
			 	 left join smc_student as s On s.student_id = po.student_id
				where {$datawehre} and po.order_type = 0 and po.school_id ='{$request['school_id']}' and po.company_id = '{$request['company_id']}'  and po.order_createtime >='{$startDay}' and  po.order_createtime <='{$endDay}' and order_status>0 and  po.order_pid  not in ('{$str_order_pid}') ";
		
		 $orderList  = $this->DataControl->selectClear($sql);
		
		  if(!$orderList)
		  {
			  $orderList = array();
		  }else{
		  	 foreach($orderList as $key => $value)
			 {
				 $orderHandleModel = new  \Model\Smc\OrderPayModel($request, $value['order_pid']);
				 $payList = $orderHandleModel->OrderPayList($request,"",1,0);
				
				 $orderList[$key]['payList'] = $payList;
				 
			 }
		  }
		 
		  return  $orderList;
			
	}
	
	
	 //退款成功以后
	function  reFundSuccess($request)
	{
		$orderOne = $this->DataControl->selectOne("select po.* from smc_payfee_order as po where po.order_pid ='{$request['order_pid']}' and company_id='{$request['company_id']}' ");
		if(!$orderOne){
			$this->error = 1;
			$this->errortip = "未查询到订单";
			return false;
		}else{
			 $payOne = $this->DataControl->selectOne("select pop.* from smc_payfee_order_pay as pop where pop.pay_pid ='{$request['pay_pid']}' and  pop.order_pid ='{$request['order_pid']}' ");
            if(!$payOne){
				 $this->error = 1;
				 $this->errortip = "未查询到支付记录";
				 return false;
			 }else{
				 $data = array();
				 $data['pay_isrefund'] = 1;
				 $data['pay_issuccess'] = -1;

			 	 $this->DataControl->updateData("smc_payfee_order_pay","pay_pid ='{$request['pay_pid']}' and  order_pid ='{$request['order_pid']}'",$data );
				 $publicarray = array();
				 $publicarray['school_id'] = $request['school_id'];
				 $publicarray['company_id'] = $request['company_id'];
				 $publicarray['staffer_id'] = $request['staffer_id'];

                 $order_pid=$this->creatPid();
				 $inspect_sql="select refund_pid from smc_refund_order where refund_pid='{$order_pid}' limit 0,1";
				 do{
					 $order_pid=$this->creatPid();
				 }while($this->DataControl->selectOne($inspect_sql));
				
				 $memberOne=$this->DataControl->getFieldOne("smc_student_family","family_cnname,family_mobile","student_id='{$orderOne['student_id']}' and family_isdefault=1");
				 $data = array();
				 $balanceModel = new \Model\Smc\BalanceModel($publicarray);
				 $data['trading_pid']=$balanceModel->stuTrading($orderOne['student_id'],'Accountrefund',$orderOne['companies_id']);
				 $data['refund_pid']=$order_pid;
				 $data['from_order_pid']=$orderOne['order_pid'];
				 $data['company_id']=$request['company_id'];
				 $data['school_id']=$request['school_id'];
				 $data['companies_id'] = $orderOne['companies_id'];
				 $data['staffer_id']=$request['staffer_id'];
				 $data['student_id']=$orderOne['student_id'];
				 $data['refund_from']=1;
				 $data['refund_name']=$memberOne['family_cnname'];
				 $data['refund_mobile']=$memberOne['family_mobile'];
				 $data['refund_reason']=$this->LgStringSwitch("当日退款");
				 $data['refund_price'] = $payOne['pay_price'];
				 $data['refund_payprice'] = $payOne['pay_price'];
				 $data['refund_createtime']=time();
				 $data['refund_type']=1;
				 $data['refund_class']=1;
			     $fund_order = $this->DataControl->getOne("smc_refund_order","from_order_pid='{$orderOne['order_pid']}'");
			     if($fund_order){
			     	$updata = array();
			     	$updata['refund_payprice'] =$fund_order['refund_payprice']  + $payOne['pay_price'];
//			     	$sum_price = $this->DataControl->selectOne("select sum(pay_price) as sum_price from  smc_payfee_order_pay where order_pid='{$orderOne['order_pid']}' and pay_issuccess =1  and pay_isrefund =1");
			     	$paylist = $this->DataControl->selectClear("select * from smc_payfee_order_pay  where order_pid='{$orderOne['order_pid']}' and pay_issuccess =1  and pay_isrefund =0");
			     	if(!$paylist){
						$updata['refund_status'] = 4;
						$this->editOrder($publicarray,$orderOne['order_pid']);
						
					}
			     	$this->DataControl->updateData("smc_refund_order","refund_pid='{$fund_order['refund_pid']}'" ,$updata);
				 }else{
			     	if($orderOne['order_paidprice'] == $payOne['pay_price']){
						$data['refund_status'] = 4;
						$this->editOrder($publicarray,$orderOne['order_pid']);
					}
					
					 $this->DataControl->insertData("smc_refund_order",$data);
				 }
				
				 $TracksData=array();
				 $TracksData['refund_pid']=$order_pid;
				 $TracksData['tracks_title']=$this->LgStringSwitch("当日退款");
				 $TracksData['tracks_information']=$this->LgStringSwitch("当日退款");
				 $TracksData['staffer_id']=$request['staffer_id'];
				 $TracksData['tracks_playname']=$request['staffer_cnname'];
				 $TracksData['tracks_time']=time();
				 $this->DataControl->insertData("smc_refund_order_tracks",$TracksData);
				
				 
				 $tradeData = array();
			 	 $tradeData['refund_pid'] = $order_pid;
			 	 $tradeData['pay_pid'] = $payOne['pay_pid'];
			 	 $tradeData['pay_outnumber'] = $payOne['pay_outnumber'];
			 	 $tradeData['trade_price'] = $payOne['pay_price'];
			 	 $tradeData['trade_outnumber'] = $request['trade_outnumber'];
			 	 $tradeData['trade_successtime'] =time();
			 	 $tradeData['trade_createtime'] =time();
			 	 $tradeData['trade_note'] =$this->LgStringSwitch("当日退款");
				 $this->DataControl->insertData('smc_refund_order_trade' , $tradeData);
				 
				 $tradelogData = array();
				 $tradelogData['refund_pid'] = $order_pid;
				 $tradelogData['tradelog_outnumber'] = $request['trade_outnumber'];
				 $tradelogData['tradelog_price'] = $payOne['pay_price'];
				 $tradelogData['tradelog_successtime'] = time();
				 $tradelogData['tradelog_note'] = $this->LgStringSwitch("当日退款");
				 $tradelogData['tradelog_createtime'] = time();
				 $this->DataControl->insertData('smc_refund_order_tradelog' , $tradelogData);
				 
				 
				 
				 $this->stuCourseBalacne($orderOne['student_id'],$orderOne['order_pid']);
//
				 $this->error = 0;
				 $this->errortip = "退款成功";
				 return true;
			 }
			
		}
		
	}
	
	function  editOrder($publicarray,$order_pid){
		$data=array();
		$data['order_status']='-1';
		$data['order_updatatime']=time();
		$this->DataControl->updateData("smc_payfee_order","order_pid='{$order_pid}'",$data);
		$Model = new \Model\Smc\OrderModel($publicarray,$order_pid);
		$Model->orderTracks($this->LgStringSwitch("当日退款"),$this->LgStringSwitch("当日退款"));
		
	}
	
	
	 function stuCourseBalacne($student_id,$order_pid){
		 
		  $payOne = $this->DataControl->selectOne("select po.pay_pid from smc_payfee_order_pay as po where po.order_pid ='{$order_pid}' and po.pay_isrefund =0   and po.pay_issuccess= 1");
		 
		  if($payOne){
		      return false;
		  }
		
		 $pidData = array();
		 $pidData['order_status'] = -1;
		 $this->DataControl->updateData("smc_payfee_order","order_pid='{$order_pid}'",$pidData);
		 
		 $sql  = "select oc.course_id from smc_payfee_order_course as oc where  oc.order_pid ='{$order_pid}' ";
		    $courseOrderList = $this->DataControl->selectClear($sql);

		    if($courseOrderList){
		    	  foreach($courseOrderList as $key => $value)
				  {
					  $data = array();
					  $data['coursebalance_figure'] = "0";
					  $data['coursebalance_time'] = "0";
					  $data['coursebalance_status'] = "0";
					  $data['coursebalance_updatatime'] = time();
					  $this->DataControl->updateData("smc_student_coursebalance","student_id='{$student_id}' and course_id='{$value['course_id']}'",$data );
					
					  $study  = $this->DataControl->selectOne("
						select ss.study_id from smc_student_study as ss
						left join  smc_class as c ON  c.class_id = ss.class_id
						where ss.student_id = '{$student_id}' and c.course_id ='{$value['course_id']}' ");
					  
					  if($study){
					  	$studyData = array();
					  	$studyData['study_isreading'] = 0;
					  	$studyData['study_endday'] = date("Y-m-d",time());
					  	$studyData['study_updatetime'] = time();
					  	$this->DataControl->updateData("smc_student_study","study_id='{$study['study_id']}'",$studyData);
					  }
				  }
			}
	 }
	
	
	
	
	
	

}