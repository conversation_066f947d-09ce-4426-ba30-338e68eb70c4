<?php


namespace Model\Smc;

class AffairsModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作校区
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->verdictCompany($publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->verdictSchool($publicarray['school_id']);
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证校园信息
    function verdictSchool($school_id)
    {
        $this->schoolOne = $this->DataControl->getFieldOne("smc_school",
            "school_id,company_id,school_branch,school_shortname,school_cnname,school_openclass,school_isclose,school_istemporaryclose,school_temporaryclosetip"
            , "school_id = '{$school_id}'");
        if (!$this->schoolOne) {
            $this->error = true;
            $this->errortip = "校园信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,staffer_ismanage", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //获取职工列表
    function getStafferList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$paramArray['keyword']}%' or s.staffer_branch like '%{$paramArray['keyword']}%' or s.staffer_employeepid like '%{$paramArray['keyword']}%' or s.staffer_enname like '%{$paramArray['keyword']}%' or s.staffer_mobile like '%{$paramArray['keyword']}%')";
        }

//        if (isset($paramArray['post_id']) && $paramArray['post_id'] !== "") {
//            $str = implode(',',$paramArray['post_id']);
//            $datawhere .= " and p.post_id in ($str)";
//        }

        if (isset($paramArray['post_id']) && $paramArray['post_id'] !== '' && $paramArray['post_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['post_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and p.post_id in ({$channelstr}) ";
            }
        }

        if (isset($paramArray['staffer_branch']) && $paramArray['staffer_branch'] !== "") {
            $datawhere .= " and s.staffer_branch ='{$paramArray['staffer_branch']}'";
        }
        if (isset($paramArray['staffer_isparttime']) && $paramArray['staffer_isparttime'] !== "") {
            $datawhere .= " and s.staffer_isparttime ='{$paramArray['staffer_isparttime']}'";
        }
        if (isset($paramArray['staffer_native']) && $paramArray['staffer_native'] !== "") {
            $datawhere .= " and s.staffer_native ='{$paramArray['staffer_native']}'";
        }
        if (isset($paramArray['staffer_leave']) && $paramArray['staffer_leave'] !== "") {
            $datawhere .= " and s.staffer_leave ='{$paramArray['staffer_leave']}'";
        }
        if (isset($paramArray['postlevel_id']) && $paramArray['postlevel_id'] !== "") {
            $datawhere .= " and p.postlevel_id ='{$paramArray['postlevel_id']}'";
        }
        if (isset($paramArray['postbe_ismianjob']) && $paramArray['postbe_ismianjob'] !== "") {
            $datawhere .= " and p.postbe_ismianjob ='{$paramArray['postbe_ismianjob']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.staffer_id as worker_id,
                s.staffer_cnname,
                s.staffer_enname,
                s.company_id,
                s.staffer_sex,
                s.staffer_branch,
                s.staffer_wxtoken,
                s.staffer_mobile,
                i.info_birthday as staffer_birthday,
                s.staffer_pass,
                s.staffer_leave,
                s.staffer_isparttime,
                s.staffer_isparttime as staffer_isparttime_status,
                s.staffer_native,
                s.staffer_native as staffer_native_name,
                s.staffer_leave as staffer_leave_status,
                sc.school_cnname,
                c.company_iscrmoperateleave,
                (select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.main_staffer_id = s.staffer_id order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg,
            (SELECT
                p.postbe_stride
            FROM
                gmc_staffer_postbe AS p
            WHERE
                p.staffer_id = s.staffer_id and p.school_id = '{$paramArray['school_id']}'
            ORDER BY
                p.postbe_createtime DESC
                LIMIT 1
                ) AS postbe_stride,
            (SELECT
                c.post_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
            WHERE
                p.staffer_id = s.staffer_id and c.post_type = 1 and p.school_id = '{$paramArray['school_id']}'
            ORDER BY
                p.postbe_createtime DESC
                LIMIT 1
                ) AS post_name,
            (SELECT
                c.post_stride
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
            WHERE
                p.staffer_id = s.staffer_id and c.post_type = 1 and p.school_id = '{$paramArray['school_id']}'
            ORDER BY
                p.postbe_createtime DESC
                LIMIT 1
                ) AS post_stride,
            (SELECT
                c.post_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
            WHERE
                p.staffer_id = s.staffer_id and c.post_type = 1 and p.school_id = '{$paramArray['school_id']}'
            ORDER BY
                p.postbe_createtime DESC
                LIMIT 1
                ) AS post_id,
            (SELECT
                w.postlevel_cnname
            FROM
                gmc_staffer_postbe AS a
                LEFT JOIN gmc_company_postlevel AS w ON a.postlevel_id = w.postlevel_id
            WHERE
                a.staffer_id = s.staffer_id and a.school_id = '{$paramArray['school_id']}'
            ORDER BY
                a.postbe_createtime DESC 
                LIMIT 1 
                ) as postlevel_cnname,
            (SELECT
                s.postpart_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN smc_school_postpart AS s ON p.postpart_id = s.postpart_id 
            WHERE
                p.staffer_id = s.staffer_id 
                AND p.school_id = '{$paramArray['school_id']}'   
                limit 1
                ) as postpart_id,
            (SELECT
                s.postpart_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN smc_school_postpart AS s ON p.postpart_id = s.postpart_id 
            WHERE
                p.staffer_id = s.staffer_id 
                AND p.school_id = '{$paramArray['school_id']}'   
                limit 1
                ) as postpart_name,
            (SELECT
                p.postbe_ismianjob
            FROM
                gmc_staffer_postbe AS p 
            WHERE
                p.staffer_id = s.staffer_id 
                AND p.school_id = '{$paramArray['school_id']}'   
                limit 1
                ) as postbe_ismianjob,
            (SELECT
                p.postbe_ismianjob
            FROM
                gmc_staffer_postbe AS p
            WHERE
                p.staffer_id = s.staffer_id 
                AND p.school_id = '{$paramArray['school_id']}'   
                limit 1
                ) as postbe_ismianjob_num
                
            FROM
                smc_staffer AS s
                LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id
                left join smc_staffer_info as i on s.staffer_id = i.staffer_id
                left join smc_school as sc on sc.school_id = p.school_id
                left join gmc_company as c on c.company_id = sc.company_id
            WHERE
                {$datawhere} and p.school_id = '{$paramArray['school_id']}'
            GROUP BY
                s.staffer_id
            ORDER BY s.staffer_id DESC
            LIMIT {$pagestart},{$num}";

        $StafferList = $this->DataControl->selectClear($sql);

        if ($StafferList) {
            $status = $this->LgArraySwitch(array('0' => '在职', '1' => '已离职'));
            $statuss = $this->LgArraySwitch(array('0' => '全职', '1' => '兼职'));
            $statusss = $this->LgArraySwitch(array('0' => '否', '1' => '是'));
            $mianjobstatusss = $this->LgArraySwitch(array('0' => '跨校', '1' => '主职'));
            $statussss = $this->LgArraySwitch(array("0" => "陆籍", "1" => "外籍", "2" => "港澳籍", "3" => "台籍"));

            foreach ($StafferList as &$val) {
                $val['stuportrait_faceimg'] = is_null($val['stuportrait_faceimg']) ? '' : $val['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90';
                //人像采集二维码
                if($val['staffer_isparttime'] == '1'){
                    $val['ishaveportrait'] = 2;
                }else{
                    $val['ishaveportrait'] = $val['stuportrait_faceimg'] ? 1 : 0;
                }
                //人像采集二维码  isstaff   0 学生  1 校职工  2 集团职工
                $nowtime = ceil(microtime(true) * 1000);
                $urlparam = base64_encode("main_staffer_branch={$val['staffer_branch']}&company_id={$paramArray['company_id']}&school_id={$paramArray['school_id']}&staffer_id={$paramArray['staffer_id']}&isstaff=1&opentime={$nowtime}");
//                $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
                if ($_SERVER['SERVER_NAME'] != 'smcapi.kedingdang.com') {
                    $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
                } else {
                    $portraiturl = "https://faceentry.kedingdang.com/?" . $urlparam;
                }
                $val['portraitqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));

                if ($val['postbe_ismianjob'] == '1') {
                    $val['status'] = '1';
                } else {
                    $val['status'] = '0';
                }
                $val['staffer_leave'] = $status[$val['staffer_leave']];
                $val['staffer_isparttime_status'] = $statuss[$val['staffer_isparttime_status']];
                $val['postbe_ismianjob'] = $mianjobstatusss[$val['postbe_ismianjob']];
                $val['staffer_native_name'] = $statussss[$val['staffer_native_name']];
                if ($val['staffer_wxtoken']) {
                    $val['isWx'] = '是';
                } else {
                    $val['isWx'] = '否';
                }
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(*) as a
            FROM
                (SELECT
	s.staffer_id AS worker_id,
	s.staffer_cnname,
	s.staffer_enname,
	s.company_id,
	s.staffer_sex,
	s.staffer_branch,
	s.staffer_mobile,
	i.info_birthday AS staffer_birthday,
	s.staffer_pass,
	s.staffer_leave,
	s.staffer_isparttime,
	s.staffer_isparttime AS staffer_isparttime_status,
	s.staffer_leave AS staffer_leave_status
FROM smc_staffer AS s
	LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id
	LEFT JOIN smc_staffer_info AS i ON s.staffer_id = i.staffer_id 
WHERE {$datawhere} and p.school_id = '{$paramArray['school_id']}'
	 GROUP BY s.staffer_id ORDER BY s.staffer_id DESC) as a");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('staffer_cnname','ishaveportrait', 'staffer_enname', 'staffer_sex', 'staffer_branch ', 'staffer_mobile', 'isWx', 'staffer_birthday', 'staffer_native_name', 'staffer_pass', 'post_name', 'post_id', 'postbe_ismianjob', 'staffer_leave', 'postpart_name', 'postpart_id', 'staffer_isparttime_status');
        $fieldname = array('中文名','是否有人像 1有 0没有', '英文名', '性别', '教师编号', '手机号', '是否绑定微信', '生日', '籍贯', '初始密码', '职位', '职位id', '是否主职', '在职状态', '角色', '角色', '是否兼职');
        $fieldcustom = array("1","1", "1", "1", "1", "1", "1", "0", "1", "0", "1", "0", "1", "1", "1", "0", "1");
        $fieldshow = array("1","0", "1", "1", "1", "1", "1", "0", "1", "0", "1", "0", "1", "1", "1", "0", "1");
        $ismethod = array("1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($ismethod[$i]);
            if ($field[$i]["fieldstring"] == "staffer_cnname") {
                $field[$i]["isportrait"] = 1;
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($StafferList) {
            $result['list'] = $StafferList;
        } else {
            $result['list'] = array();
        }


        $result['post'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$this->schoolOne['company_id']}' and post_type = 1");

        $result['postnext'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where post_isrecrparttime = '1' and company_id = '{$this->schoolOne['company_id']}'");

        if (!$result['postnext']) {
            $result['postnext'] = array();
        }

        $postlevelList = $this->DataControl->selectClear("select postlevel_id,postlevel_cnname from gmc_company_postlevel where company_id = '{$this->schoolOne['company_id']}'");

        $result['postlevel'] = $postlevelList ? $postlevelList : array();

        $result['postpart'] = $this->DataControl->selectClear("select postpart_id,postpart_name from smc_school_postpart where company_id = '{$this->schoolOne['company_id']}' and (school_id = '{$paramArray['school_id']}' or school_id = '0')");

        $result['all_num'] = $allnums;

        if (!$StafferList) {
            $res = array('error' => '1', 'errortip' => "暂无人事资料", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }


        return $res;
    }

    //带班记录列表
    function ClassList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['class_type']) && $paramArray['class_type'] !== "") {
            if ($paramArray['class_type'] == 2) {
                $datawhere .= " and ct.coursetype_isopenclass = '1'";
            } else {
                $datawhere .= " and c.class_type = '{$paramArray['class_type']}'";
            }
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== "") {
            $datawhere .= " and c.class_status = '{$paramArray['class_status']}'";
        }
        if (isset($paramArray['teaching_type']) && $paramArray['teaching_type'] !== "") {
            $datawhere .= " and t.teaching_type = '{$paramArray['teaching_type']}'";
        }

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }

        if (isset($paramArray['search_school']) && $paramArray['search_school'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['search_school']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $contractOne = $this->getContract($paramArray['company_id']);

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                c.class_enname,
                c.class_branch,
                co.course_cnname,
                co.course_branch,
                s.staffer_cnname,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = t.class_id) as classNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id) as planNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id and h.hour_ischecking = '1' and h.hour_iswarming = '0') as alreadyNum,
                c.class_stdate,
                c.class_enddate,
                c.class_status,sc.school_id,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch
            FROM
                smc_class_teach AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_code_coursetype as ct On ct.coursetype_id=co.coursetype_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
                left join smc_school as sc on sc.school_id=c.school_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['worker_id']}' and class_status > '-2' and c.company_id = '{$paramArray['company_id']}'
                group by c.class_id
            Limit {$pagestart},{$num}";


        } else {
            $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                c.class_enname,
                c.class_branch,
                co.course_cnname,
                co.course_branch,
                s.staffer_cnname,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = t.class_id) as classNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id) as planNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id and h.hour_ischecking = '1' and h.hour_iswarming = '0') as alreadyNum,
                c.class_stdate,
                c.class_enddate,
                c.class_status,sc.school_id,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_code_coursetype as ct On ct.coursetype_id=co.coursetype_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
                left join smc_school as sc on sc.school_id=c.school_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['worker_id']}' and class_status > '-2' and c.company_id = '{$paramArray['company_id']}'
                group by c.class_id
            Limit {$pagestart},{$num}";
        }


        $ClassList = $this->DataControl->selectClear($sql);

        if ($ClassList) {
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束'));
            foreach ($ClassList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
            }
        }
        if ($contractOne && $contractOne['edition_id'] == '2') {
            $all_num = $this->DataControl->selectClear("
            SELECT
                c.class_id
            FROM
                smc_class_teach AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_code_coursetype as ct On ct.coursetype_id=co.coursetype_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['worker_id']}' and class_status > '-2' and c.company_id = '{$paramArray['company_id']}'
                group by c.class_id");
        } else {
            $all_num = $this->DataControl->selectClear("
            SELECT
                c.class_id
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_code_coursetype as ct On ct.coursetype_id=co.coursetype_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['worker_id']}' and class_status > '-2' and c.company_id = '{$paramArray['company_id']}'
                group by c.class_id");
        }

        $allnums = $all_num ? count($all_num) : 0;

        $fieldstring = array('school_cnname', 'school_branch', 'class_cnname', 'class_branch', 'course_cnname', 'course_branch', 'class_status', 'planNum', 'alreadyNum', 'alreadyNum');
        $fieldname = array('校区名称', '校区编号', '班级名称', '班级编号', '课程别名称', '课程别编号', '班级状态', '全部课次', '班级上课次数', '学员考勤次数');

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "0", "0", "0", "0");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "0", "0", "0", "0");
        } else {
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ClassList) {
            $result['list'] = $ClassList;
        } else {
            $result['list'] = array();
        }


        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取带班记录列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何带班信息', 'result' => $result);
        }

        return $res;
    }

    //教师课表列表
    function TeaTableList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] != '') {
            $datawhere .= " and ch.hour_way='{$paramArray['hour_way']}'";
        }
        if (isset($paramArray['hour_ischecking']) && $paramArray['hour_ischecking'] != '') {
            $datawhere .= " and ch.hour_ischecking='{$paramArray['hour_ischecking']}'";
        }

        if (isset($paramArray['class_id']) && $paramArray['class_id'] != '') {
            $datawhere .= " and c.class_id='{$paramArray['class_id']}'";
        }

        if (isset($paramArray['search_school']) && $paramArray['search_school'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['search_school']}'";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] != '') {
            $datawhere .= " and ch.hour_day>='{$paramArray['start_time']}'";
        }

        if (isset($paramArray['teaching_type']) && $paramArray['teaching_type'] != '') {
            $datawhere .= " and ht.teaching_type='{$paramArray['teaching_type']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] != '') {
            $datawhere .= " and ch.hour_day<='{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ch.hour_id,ch.hour_day,c.class_id,c.class_branch,c.class_cnname,c.class_enname,sch.school_id,sch.school_cnname,sch.school_branch,cl.classroom_cnname,c.class_fullnums,ch.hour_way,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,ch.hour_name,ch.hour_number,co.course_cnname,co.course_branch,ht.teaching_type,ct.teachtype_name
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hourstudy_id) from smc_student_hourstudy as ho where ho.hour_id=ch.hour_id and hourstudy_checkin = '1' ) as tureNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum,
              (SELECT count(cs.sturemark_id) FROM eas_classhour_sturemark AS cs WHERE cs.hour_id = ch.hour_id ) as alreadyNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co on co.course_id = c.course_id
              left join smc_code_teachtype as ct on ct.teachtype_code=ht.teachtype_code and ct.company_id='{$paramArray['company_id']}'
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$paramArray['company_id']}'
              order by sch.school_id,ch.hour_day ASC,ch.hour_starttime asc ";
        $sql .= " limit {$pagestart},{$num}";

        $ClassList = $this->DataControl->selectClear($sql);

        if ($ClassList) {
            $status = $this->LgArraySwitch(array('0' => '未上课', '1' => '已上课', '-1' => '已取消'));
            $statuss = $this->LgArraySwitch(array('0' => '主教', '1' => '助教'));
            $way = $this->LgArraySwitch(array('0' => '实体课', '1' => '线上课'));
            $week = $this->LgArraySwitch(array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'));
            foreach ($ClassList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
                $val['teaching_type'] = $statuss[$val['teaching_type']];
                $val['date'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                $val['hour_ischecking_name'] = $status[$val['hour_ischecking']];
                $val['hour_way_name'] = $way[$val['hour_way']];
                $val['week'] = $week[date('w', strtotime($val['hour_day']))];
                $val['hour_evaluate'] = 1 / 20;
                $val['score'] = $val['commentNum'] . '/' . $val['stuNum'];
                if ($val['hour_way'] == '0') {
                    $val['room'] = $val['classroom_cnname'];
                } else {
                    $val['room'] = $val['hour_number'];
                }
            }

        }

        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$paramArray['company_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnums = count($db_nums);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('school_cnname', 'school_branch', 'date', 'hour_way_name', 'teachtype_name', 'teaching_type', 'class_cnname', 'class_branch', 'course_cnname', 'course_branch', 'room', 'hour_ischecking_name');
        $fieldname = array('校区名称', '校区编号', '上课时间', '上课方式', '教师类型', '教学类型', '班级名称', '班级编号', '课程别名称', '课程别编号', '教室', '上课状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldisShowWay = array("", "", "", "", "", "", "", "", "", "", "1", "");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isShowWay"] = trim($fieldisShowWay[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ClassList) {
            $result['list'] = $ClassList;
        } else {
            $result['list'] = array();
        }


        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取教师课表列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何教师课表', 'result' => $result);
        }

        return $res;
    }

    //获取学校
    function getSchoolApi($paramArray)
    {
        $result = array();
        $result['school'] = $this->DataControl->selectClear("select s.school_id,s.school_cnname from gmc_company_organizeschool as o left join smc_school as s on o.school_id = s.school_id WHERE o.organize_id = '{$paramArray['organize_id']}' and s.school_id <> '' and s.school_id <> '{$paramArray['school_id']}'");
        if ($result['school']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['school'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //获取组织
    function getOrganizeApi($paramArray)
    {
        $result = array();
        $result['organize'] = $this->DataControl->selectClear("select organize_id,organize_cnname from gmc_company_organize where company_id = '{$paramArray['company_id']}'");

        if ($result['organize']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['organize'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //获取校园职务
    function getPostApi($paramArray)
    {
        $result = array();
        $result['postSchool'] = $this->DataControl->selectClear("select post_id,post_name,postpart_id from gmc_company_post where company_id = '{$paramArray['company_id']}' AND post_type = 1 and post_stride = '1'");

        if ($result['postSchool']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['postSchool'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //获取校园角色
    function getPostpartApi($paramArray)
    {
        $result = array();
        $result['postpart'] = $this->DataControl->selectClear("select postpart_id,postpart_name from smc_school_postpart where company_id = '{$paramArray['company_id']}'");

        if ($result['postpart']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['postpart'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //职工管理 -- 删除职务
    function delPostAction($paramArray)
    {
        $PostOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postbe_id = '{$paramArray['postbe_id']}'");
        if ($PostOne) {
            $a = $this->DataControl->selectOne("
                SELECT
                    p.post_istopjob
                FROM
                    gmc_staffer_postbe AS sp
                LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
                WHERE sp.postbe_id = '{$paramArray['re_postbe_id']}'");

            if ($a['post_istopjob'] != '1') {
                ajax_return(array('error' => 1, 'errortip' => "您没有权限！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("gmc_staffer_postbe", "postbe_id = '{$paramArray['postbe_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工职务成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工职务失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //校园跨校职务
    function getStridePostList($paramArray)
    {
        $sql = "select p.postbe_id,s.school_id,p.organize_id,p.post_id,p.postpart_id,s.school_cnname,s.school_shortname,c.post_name,sp.postpart_name,postbe_ismianjob from gmc_staffer_postbe as p left join smc_school as s on p.school_id = s.school_id left join gmc_company_post as c on c.post_id = p.post_id left join smc_school_postpart as sp on sp.postpart_id = p.postpart_id where p.staffer_id = '{$paramArray['worker_id']}' and p.school_id > '0'";

        $PostList = $this->DataControl->selectClear($sql);

        $fieldstring = array('post_name', 'school_shortname', 'postpart_name');
        $fieldname = array('职务名称', '校区名称', '学校角色');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "school_id", "postbe_ismianjob = '1' and staffer_id = '{$paramArray['worker_id']}' and school_id > '0'");

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PostList) {
            $result['list'] = $PostList;
        } else {
            $result['list'] = array();
        }


        if ($PostList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $PostList;

            if ($a['school_id'] == $paramArray['school_id']) {
                $result['status'] = '1';
            } else {
                $result['status'] = '0';
            }
            $res = array('error' => '0', 'errortip' => '获取校园跨校职务成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无校园跨校职务', 'result' => $result);
        }

        return $res;
    }

    //教师上课记录列表
    function ClassTeaching($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] != '') {
            $datawhere .= " and ch.hour_way='{$paramArray['hour_way']}'";
        }

        if (isset($paramArray['class_id']) && $paramArray['class_id'] != '') {
            $datawhere .= " and c.class_id='{$paramArray['class_id']}'";
        }


        if (isset($paramArray['search_school']) && $paramArray['search_school'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['search_school']}'";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] != '') {
            $datawhere .= " and ch.hour_day>='{$paramArray['start_time']}'";
        }

        if (isset($paramArray['end_time']) && $paramArray['end_time'] != '') {
            $datawhere .= " and ch.hour_day<='{$paramArray['end_time']}'";
        }


        $sql = "select ch.hour_id,ch.hour_day,c.class_id,c.class_branch,c.class_cnname,c.class_enname,sch.school_cnname,sch.school_branch,cl.classroom_cnname,c.class_fullnums,ch.hour_way,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,ch.hour_name,ch.hour_number,co.course_cnname,co.course_branch
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hourstudy_id) from smc_student_hourstudy as ho where ho.hour_id=ch.hour_id and hourstudy_checkin = '1' ) as tureNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum,
              (SELECT count(cs.sturemark_id) FROM eas_classhour_sturemark AS cs WHERE cs.hour_id = ch.hour_id ) as alreadyNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co on co.course_id = c.course_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$paramArray['company_id']}' and ch.hour_ischecking = '1'
              order by sch.school_id,ch.hour_day ASC,ch.hour_starttime asc ";
        $sql .= " limit {$pagestart},{$num}";

        $ClassList = $this->DataControl->selectClear($sql);

        if ($ClassList) {
            $status = $this->LgArraySwitch(array('0' => '未上课', '1' => '已上课', '-1' => '已取消'));
            $way = $this->LgArraySwitch(array('0' => '实体课', '1' => '线上课'));
            $week = $this->LgArraySwitch(array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'));
            foreach ($ClassList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
                $val['date'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                $val['hour_ischecking_name'] = $status[$val['hour_ischecking']];
                $val['hour_way_name'] = $way[$val['hour_way']];
                $val['week'] = $week[date('w', strtotime($val['hour_day']))];
                $val['hour_evaluate'] = 1 / 20;
                $val['score'] = $val['commentNum'] . '/' . $val['stuNum'];
                if ($val['hour_way'] == '0') {
                    $val['room'] = $val['classroom_cnname'];
                } else {
                    $val['room'] = $val['hour_number'];
                }
                $val['arrive'] = $val['tureNum'] . '/' . $val['stuNum'];
                $val['percent'] = round($val['tureNum'] / $val['stuNum'] * 100, 2) . '%';
                $val['count'] = '1';
            }

        }

        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$paramArray['company_id']}' and ch.hour_ischecking = '1'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnums = count($db_nums);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('school_cnname', 'school_branch', 'date', 'hour_way_name', 'class_cnname', 'class_branch', 'room', 'arrive', 'percent', 'count');
        $fieldname = array('校区名称', '校区编号', '上课时间', '上课方式', '班级名称', '班级编号', '上课教室', '实到/应到', '出勤率', '抵消次数');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldisShowWay = array("", "", "", "", "", "", "1", "", "", "");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isShowWay"] = trim($fieldisShowWay[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ClassList) {
            $result['list'] = $ClassList;
        } else {
            $result['list'] = array();
        }


        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取教师上课记录列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何教师上课记录', 'result' => $result);
        }

        return $res;
    }


    //添加跨校职务
    function addStafferSchoolPostAction($paramArray)
    {
        $a = $this->DataControl->selectOne("
                SELECT
                    p.post_istopjob
                FROM
                    gmc_staffer_postbe AS sp
                LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
                WHERE sp.postbe_id = '{$paramArray['re_postbe_id']}'");

        if ($a['post_istopjob'] != '1' && $paramArray['re_postbe_id'] != '0') {
            ajax_return(array('error' => 1, 'errortip' => "您没有权限！"), $this->companyOne['company_language']);
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_id'] = $paramArray['worker_id'];
        $data['post_id'] = $paramArray['post_id'];
        $data['organize_id'] = $paramArray['organize_id'];
        $data['school_id'] = $paramArray['schoolOne_id'];
        $data['postpart_id'] = $paramArray['postpart_id'];
        $data['postbe_stride'] = '1';
        $data['postbe_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属集团";
        $field['staffer_id'] = "职工id";
        $field['post_id'] = "职务id";
        $field['organize_id'] = "组织id";
        $field['school_id'] = "学校id";
        $field['postpart_id'] = "角色id";
        $field['postbe_createtime'] = "创建时间";

        $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "school_id = '{$paramArray['schoolOne_id']}' and staffer_id = '{$paramArray['worker_id']}'");

        if ($a) {
            ajax_return(array('error' => 1, 'errortip' => "职工在同一学校内只能担任一个职务!"));
        }

        if ($this->DataControl->insertData('gmc_staffer_postbe', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工跨校职务成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工校园职务失败', 'result' => $result);
        }
        return $res;
    }

    //获取单个职工
    function getStafferOneList($paramArray)
    {
        $sql = "
            SELECT
                s.staffer_id as worker_id,
                s.staffer_cnname,
                s.staffer_enname,
                s.company_id,
                s.staffer_sex,
                s.staffer_img,
                s.staffer_branch,s.staffer_employeepid,
                s.staffer_mobile,
                i.info_birthday as staffer_birthday,
                s.staffer_pass,
                s.staffer_leave,
                s.staffer_isparttime,
                s.staffer_leavetime,
                s.staffer_isparttime as staffer_isparttime_status,
                s.staffer_leave as staffer_leave_status,
                (select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.main_staffer_id = s.staffer_id order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg,
            (SELECT
                c.post_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
            WHERE
                p.staffer_id = s.staffer_id and c.post_type = 1 and p.school_id = '{$paramArray['school_id']}'
            ORDER BY
                p.postbe_createtime DESC
                LIMIT 1
                ) AS postpart_name,
            (SELECT
                c.post_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
            WHERE
                p.staffer_id = s.staffer_id and c.post_type = 1
            ORDER BY
                p.postbe_createtime DESC
                LIMIT 1
                ) AS post_id,
            (SELECT
                w.postlevel_cnname
            FROM
                gmc_staffer_postbe AS a
                LEFT JOIN gmc_company_postlevel AS w ON a.postlevel_id = w.postlevel_id
            WHERE
                a.staffer_id = s.staffer_id and a.school_id = '{$paramArray['school_id']}'
            ORDER BY
                a.postbe_createtime DESC 
                LIMIT 1 
                ) as postlevel_cnname,
            (SELECT
                s.postpart_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN smc_school_postpart AS s ON p.postpart_id = s.postpart_id 
            WHERE
                p.staffer_id = s.staffer_id 
                AND p.school_id = '{$paramArray['school_id']}'   
                limit 1
                ) as postpart_id
                
            FROM
                smc_staffer AS s
                LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id
                left join smc_staffer_info as i on s.staffer_id = i.staffer_id
            WHERE
                p.school_id = '{$paramArray['school_id']}' and s.staffer_id = '{$paramArray['worker_id']}'";

        $StafferList = $this->DataControl->selectClear($sql);

        if ($StafferList) {
            $status = $this->LgArraySwitch(array('0' => '在职', '1' => '已离职'));
            $statuss = $this->LgArraySwitch(array('0' => '全职', '1' => '兼职'));
            foreach ($StafferList as &$val) {
                $val['stuportrait_faceimg'] = is_null($val['stuportrait_faceimg']) ? '' : $val['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_200,limit_0/auto-orient,1/quality,q_90';
                $val['staffer_img'] = $val['staffer_img'] ? $val['staffer_img'].'?x-oss-process=image/resize,m_lfit,w_200,limit_0/auto-orient,1/quality,q_90':'';
                //人像采集二维码
                $val['ishaveportrait'] = $val['stuportrait_faceimg'] ? 1 : 0;

                $val['staffer_leave'] = $status[$val['staffer_leave']];
                $val['staffer_isparttime_status'] = $statuss[$val['staffer_isparttime_status']];
                if ($val['staffer_birthday']) {
                    $val['age'] = $this->birthday2($val['staffer_birthday']);
                } else {
                    $val['age'] = '0';
                }
            }

        }

        $result = array();
        $result['list'] = $StafferList;


        $result['post'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$paramArray['company_id']}' and post_type = 1");

        $result['postnext'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where post_isrecrparttime = '1' and company_id = '{$paramArray['company_id']}'");

        if (!$result['postnext']) {
            $result['postnext'] = array();
        }

        $result['postlevel'] = $this->DataControl->selectClear("select postlevel_id,postlevel_cnname from gmc_company_postlevel where company_id = '{$paramArray['company_id']}'");

        $result['postpart'] = $this->DataControl->selectClear("select postpart_id,postpart_name from smc_school_postpart where company_id = '{$paramArray['company_id']}' and (school_id = '{$paramArray['school_id']}' or school_id = '0')");

        if (!$StafferList) {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }

        $res["staffismanage"] = $this->stafferOne['staffer_ismanage'];//是否是 技术账号

        return $res;
    }

    //人脸采集记录 -- 97
    function getStafferPortraitApi($paramArray)
    {
        $datawhere = " m.company_id = '{$paramArray['company_id']}' and m.main_staffer_id = '{$paramArray['main_staffer_id']}' ";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " SELECT m.stuportrait_faceimg,s.school_shortname,m.stuportrait_creattime,f.staffer_cnname,f.staffer_enname,f.staffer_branch 
                FROM gmc_machine_stuportrait as m 
                left join smc_school as s ON s.school_id = m.school_id 
                left join smc_staffer as f ON m.staffer_id = f.staffer_id 
                WHERE {$datawhere} 
                ORDER BY m.stuportrait_creattime desc    
                LIMIT {$pagestart},{$num}";
        $MachineList = $this->DataControl->selectClear($sql);

        //统计总数
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(m.stuportrait_id) as datanum
                FROM gmc_machine_stuportrait as m 
                left join smc_school as s ON s.school_id = m.school_id 
                left join smc_staffer as f ON m.staffer_id = f.staffer_id
                WHERE {$datawhere}  ");
            $allnum = $all_num['datanum'] + 0;
        } else {
            $allnum = 0;
        }
        $fieldstring = array('stuportrait_faceimg', 'school_shortname', 'stuportrait_creattime', 'staffer_cnname');
        $fieldname = array('人脸信息', '录入学校', '录入时间', '操作人');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldstring"] == 'stuportrait_faceimg') {
                $field[$i]["isqrcode"] = 1;
            }
        }

        if ($MachineList) {
            foreach ($MachineList as &$MachineVar) {
                $MachineVar['stuportrait_faceimg'] = $MachineVar['stuportrait_faceimg']?$MachineVar['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_200,limit_0/auto-orient,1/quality,q_90':'';
                $MachineVar['staffer_cnname'] = $MachineVar['staffer_enname'] ? $MachineVar['staffer_cnname'] . '/' . $MachineVar['staffer_enname'] : $MachineVar['staffer_cnname'];
                $MachineVar['stuportrait_creattime'] = date("Y-m-d H:i", $MachineVar['stuportrait_creattime']);
            }

            $result = array();
            $result["field"] = $field;
            $result['allnum'] = $allnum;
            $result['list'] = $MachineList;

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        } else {
            $result = array();
            $result["field"] = $field;
            $result['allnum'] = 0;
            $result['list'] = array();

            $this->error = 1;
            $this->errortip = "暂未获取到人像记录";
            $this->result = $result;
            return false;
        }
    }

    function birthday2($birthday)
    {
        list($year, $month, $day) = explode("-", $birthday);
        $year_diff = date("Y") - $year;
        $month_diff = date("m") - $month;
        $day_diff = date("d") - $day;
        if ($day_diff < 0 || $month_diff < 0)
            $year_diff--;
        return $year_diff;
    }

    //添加职工
    function addStafferAction($paramArray)
    {
        $data = array();
        $data['staffer_cnname'] = $paramArray['staffer_cnname'];
        $data['staffer_sex'] = $paramArray['staffer_sex'];
        $data['staffer_branch'] = $paramArray['staffer_branch'];
        $data['staffer_mobile'] = $paramArray['staffer_mobile'];
        $data['staffer_pass'] = $paramArray['staffer_pass'];
        $data['staffer_birthday'] = $paramArray['staffer_birthday'];
        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_createtime'] = time();

        $field = array();
        $field['staffer_cnname'] = $this->LgStringSwitch("姓名");
        $field['staffer_sex'] = $this->LgStringSwitch("性别");
        $field['staffer_branch'] = $this->LgStringSwitch("编号");
        $field['staffer_mobile'] = $this->LgStringSwitch("手机号");
        $field['staffer_pass'] = $this->LgStringSwitch("初始密码");
        $field['staffer_birthday'] = $this->LgStringSwitch("出生日期");
        $field['company_id'] = $this->LgStringSwitch("所属公司");

        $staffer_branch = $this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_branch = '{$paramArray['staffer_branch']}'");
        if ($staffer_branch) {
            ajax_return(array('error' => 1, 'errortip' => "编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData("smc_staffer", $data)) {
            $data2 = array();
            $data2['company_id'] = $paramArray['company_id'];
            $data2['staffer_id'] = mysql_insert_id();
            $data2['school_id'] = $paramArray['school_id'];
            $data2['post_id'] = $paramArray['post_id'];
            $data2['postbe_createtime'] = time();
            $this->DataControl->insertData('gmc_staffer_postbe', $data2);
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $result["data2"] = $data2;
            $res = array('error' => '0', 'errortip' => "添加职工成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工失败', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 修改职工信息
    function updateStafferAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['staffer_mobile'] = $paramArray['staffer_mobile'];
            $data['staffer_cnname'] = $paramArray['staffer_cnname'];
            $data['staffer_birthday'] = $paramArray['staffer_birthday'];
            $data['staffer_pass'] = $paramArray['staffer_pass'];
            $data['staffer_sex'] = $paramArray['staffer_sex'];
            $data['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_mobile'] = $this->LgStringSwitch("手机号");
            $field['staffer_cnname'] = $this->LgStringSwitch("中文名称");
            $field['staffer_birthday'] = $this->LgStringSwitch("出生日期");
            $field['staffer_pass'] = $this->LgStringSwitch("初始密码");
            $field['staffer_sex'] = $this->LgStringSwitch("性别");
            $field['staffer_updatetime'] = $this->LgStringSwitch("修改时间");
            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工信息修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工信息修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 删除职工信息
    function delStafferAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['worker_id']}'");
        if ($stafferOne) {
            if ($this->DataControl->delData("smc_staffer", "staffer_id = '{$paramArray['worker_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工信息成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工信息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //如果职工不存在 CRM 账号就创建 CRM 账号
    function addStaffMarketerOne($staffer_id){
        $stafferOne = $this->DataControl->selectOne("select company_id,staffer_id,staffer_istest,staffer_cnname,staffer_img,staffer_mobile
from smc_staffer WHERE staffer_id = '{$staffer_id}'");

        $datas = array();
        $datas['company_id'] = $stafferOne['company_id'];
        $datas['staffer_id'] = $stafferOne['staffer_id'];
        $datas['marketer_istest'] = $stafferOne['staffer_istest'];
        $datas['marketer_name'] = $stafferOne['staffer_cnname'];
        $datas['marketer_img'] = $stafferOne['staffer_img'];
        $datas['marketer_mobile'] = $stafferOne['staffer_mobile'];
        $datas['marketer_lasttime'] = time();
        $datas['marketer_lastip'] = real_ip();
        $datas['marketer_createtime'] =  time();
        $datas['marketer_status'] = '1';
        $datas['marketer_id'] = $this->DataControl->insertData('crm_marketer', $datas);

        return $datas;
    }

    //获取登陆个人信息
    function getStafferInfoApi($paramArray)
    {
        //判断职工是否存在CRM账号，没有话创建一个  --- 开始
        $marketerOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer
        where staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
        if (!$marketerOne) {
            $this->addStaffMarketerOne($paramArray['staffer_id']);
        }
        //判断职工是否存在CRM账号，没有话创建一个  --- 结束

        $stafferOne = $this->DataControl->selectOne("select s.staffer_id,s.account_class,s.staffer_cnname,s.staffer_enname,s.staffer_mobile,
s.staffer_branch,i.info_birthday as staffer_birthday,s.staffer_img,s.staffer_sex,
c.company_id,c.company_isaddstudent,c.company_logo,c.company_ismajor,c.company_isvoucher,c.company_isinvoice,c.company_iseditstu,c.company_canbatchespay,c.company_isdelay,c.company_canadjustcourse
,c.company_canshiftinclass,s.staffer_ismanage,c.company_isopenspecialnumber
from smc_staffer as s LEFT JOIN smc_staffer_info AS i ON s.staffer_id = i.staffer_id
LEFT JOIN gmc_company as c ON s.company_id=c.company_id where s.staffer_id = '{$paramArray['staffer_id']}' limit 0,1");

        $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id,organize_id,school_id,postbe_iscrmuser", "postbe_id = '{$paramArray['re_postbe_id']}'");
        if ($paramArray['re_postbe_id'] == '0') {
            $status = '1';
        } else {
            if ($postbeOne['school_id'] == '0') {
                $postroleOne = $this->DataControl->getFieldOne("gmc_company_postrole", "postpart_iscrmuser", "postrole_id = '{$postbeOne['postrole_id']}'");
                if ($postroleOne['postpart_iscrmuser'] == '1') {
                    $status = '1';
                } else {
                    $status = '0';
                }
            } else {
                if ($postbeOne['postbe_iscrmuser'] == '1') {
                    $status = '1';
                } else {
                    $status = '0';
                }
            }
        }

        if ($stafferOne['account_class'] == '0') {
            if (isset($paramArray['school_id']) && $paramArray['school_id']) {
                $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_isvoucher,s.school_cnname,s.school_openclass,s.school_shortname,s.school_shortname,s.school_branch,s.school_renewtime
,s.school_changestime,s.school_renewsttime,s.school_renewentime,s.school_changessttime,s.school_changesentime,s.school_istwocode,s.school_isscan,s.school_opclass,s.school_inclass,s.school_isprotocol,s.school_istemporaryclose,s.school_temporaryclosetip,s.school_payaftersignlimit FROM smc_school as s
WHERE s.school_id = '{$paramArray['school_id']}' limit 0,1");
            } else {
                $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_isvoucher,s.school_cnname,s.school_openclass,s.school_shortname,s.school_shortname,s.school_branch,s.school_renewtime
,s.school_changestime,s.school_renewsttime,s.school_renewentime,s.school_changessttime,s.school_changesentime,s.school_istwocode,s.school_isscan,s.school_opclass,s.school_inclass,s.school_isprotocol,s.school_istemporaryclose,s.school_temporaryclosetip,s.school_payaftersignlimit  FROM smc_school as s
WHERE s.school_id in (
SELECT p.school_id FROM gmc_staffer_postbe AS p WHERE p.staffer_id = '{$stafferOne['staffer_id']}' AND p.school_id <> '0'
UNION ALL
SELECT p.school_id FROM gmc_company_organizeschool AS p, gmc_staffer_postbe AS b WHERE p.organize_id = b.organize_id AND b.staffer_id = '{$stafferOne['staffer_id']}' AND b.school_id = '0')
ORDER BY s.school_istemp DESC limit 0,1");
            }
        } else {
            $schoolOne = $this->DataControl->selectOne("select s.school_id,s.school_isvoucher,s.school_cnname,s.school_openclass,s.school_shortname,s.school_shortname,s.school_branch,s.school_renewtime
,s.school_changestime,s.school_renewsttime,s.school_renewentime,s.school_changessttime,s.school_changesentime,s.school_istwocode,s.school_isscan,s.school_opclass,s.school_inclass,s.school_isprotocol,s.school_istemporaryclose,s.school_temporaryclosetip,s.school_payaftersignlimit  FROM smc_school as s
WHERE s.school_id = '{$paramArray['school_id']}' ORDER BY s.school_id ASC,s.school_istemp DESC limit 0,1");
        }
        $qrcodeurl = "https://ptc.kidcastle.com.cn/AddStudent?branch={$schoolOne['school_branch']}&schoolname={$schoolOne['school_shortname']}";
        $stafferOne['newstujoinqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($qrcodeurl));


        if ($schoolOne) {
            $result = array();
            $result['staffer_id'] = $stafferOne['staffer_id'];
            $result['staffer_ismanage'] = $stafferOne['staffer_ismanage'];
            $result['company_id'] = $stafferOne['company_id'];
            $result['school_id'] = $schoolOne['school_id'];
            $result['school_cnname'] = $schoolOne['school_shortname'];
            $result['school_renewtime'] = $schoolOne['school_renewtime'];
            $result['school_changestime'] = $schoolOne['school_changestime'];
            $result['school_isvoucher'] = $schoolOne['school_isvoucher'];
            $result['company_isaddstudent'] = $stafferOne['company_isaddstudent'];
            $result['company_canbatchespay'] = $stafferOne['company_canbatchespay'];
            $result['company_isopenspecialnumber'] = $stafferOne['company_isopenspecialnumber'];
            $result['company_canshiftinclass'] = $stafferOne['company_canshiftinclass'];
            $result['company_canadjustcourse'] = $stafferOne['company_canadjustcourse'];
            $result['company_iseditstu'] = $stafferOne['company_iseditstu'];
            $result['company_isdelay'] = $stafferOne['company_isdelay'];
            $result['company_ismajor'] = $stafferOne['company_ismajor'];
            $result['company_isvoucher'] = $stafferOne['company_isvoucher'];
            $result['company_isinvoice'] = $stafferOne['company_isinvoice'];
            $result['school_renewsttime'] = $schoolOne['school_renewsttime'];
            $result['school_renewentime'] = $schoolOne['school_renewentime'];
            $result['school_changessttime'] = $schoolOne['school_changessttime'];
            $result['school_changesentime'] = $schoolOne['school_changesentime'];
            $result['school_isscan'] = $schoolOne['school_isscan'];
            $result['school_istwocode'] = $schoolOne['school_istwocode'];
            $result['school_branch'] = $schoolOne['school_branch'];
            $result['school_opclass'] = $schoolOne['school_opclass'];
            $result['school_inclass'] = $schoolOne['school_inclass'];
            $result['school_isprotocol'] = $schoolOne['school_isprotocol'];
            $result['school_istemporaryclose'] = $schoolOne['school_istemporaryclose'];
            $result['school_temporaryclosetip'] = $schoolOne['school_temporaryclosetip'];
            $result['school_payaftersignlimit'] = $schoolOne['school_payaftersignlimit'];
            $result['school_payaftersignlimit'] = $schoolOne['school_payaftersignlimit'];
            $result['today'] = date("Y-m-d", time());
            $result['newstujoinqrcode'] = $stafferOne['newstujoinqrcode'];//20205月暂用新生录入二维码

            $stafferArray = $this->DataControl->selectClear("SELECT c.post_name from gmc_staffer_postbe as p left join gmc_company_post as c on p.post_id = c.post_id
where p.staffer_id = '{$paramArray['staffer_id']}'");
            if ($stafferArray) {
                foreach ($stafferArray as &$InfoOne) {
                    $InfoOne['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
                    $InfoOne['staffer_enname'] = $stafferOne['staffer_enname'];
                    $InfoOne['staffer_mobile'] = $stafferOne['staffer_mobile'];
                    $InfoOne['staffer_branch'] = $stafferOne['staffer_branch'];
                    $InfoOne['staffer_birthday'] = $stafferOne['staffer_birthday'];
                    $InfoOne['staffer_img'] = $stafferOne['staffer_img'];
                    $InfoOne['staffer_sex'] = $stafferOne['staffer_sex'];
                    $InfoOne['account_class'] = $stafferOne['account_class'];
                }
            } else {
                $data = array();
                $data['post_name'] = '';
                $data['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
                $data['staffer_enname'] = $stafferOne['staffer_enname'];
                $data['staffer_mobile'] = $stafferOne['staffer_mobile'];
                $data['staffer_branch'] = $stafferOne['staffer_branch'];
                $data['staffer_birthday'] = $stafferOne['staffer_birthday'];
                $data['staffer_img'] = $stafferOne['staffer_img'];
                $data['staffer_sex'] = $stafferOne['staffer_sex'];
                $data['account_class'] = $stafferOne['account_class'];

                $stafferArray[] = $data;
            }

            $result['stafferInfo'] = $stafferArray ? $stafferArray : array();

            if ($postbeOne['school_id'] == '0') {

                if ($this->DataControl->getFieldOne("gmc_company_organizeschool", "school_id", "school_id='{$paramArray['school_id']}' and organize_id = '{$postbeOne['organize_id']}'")) {
                    $school_id['school_id'] = $paramArray['school_id'];
                } else {
                    $school_id = $this->DataControl->getFieldOne("gmc_company_organizeschool", "school_id", "organize_id = '{$postbeOne['organize_id']}'");
                }


                $sql = "SELECT
                    p.postbe_id,
                    p.postpart_id,
                    o.post_name,
                    o.post_istopjob,
                    (select school_shortname from smc_school WHERE school_id = '{$school_id['school_id']}') as school_cnname,
                    (select school_address from smc_school WHERE school_id = '{$school_id['school_id']}') as school_address,
                    (select school_id from smc_school WHERE school_id = '{$school_id['school_id']}') as school_id
                FROM gmc_staffer_postbe AS p
                    LEFT JOIN gmc_company_post AS o ON p.post_id = o.post_id
                    LEFT JOIN smc_school AS c ON p.school_id = c.school_id
                WHERE p.postbe_id = '{$paramArray['re_postbe_id']}'
                ORDER BY p.postbe_ismianjob DESC";
            } else {
                $sql = "SELECT p.postbe_id, p.postpart_id, o.post_name, o.post_istopjob, c.school_id, c.school_shortname as  school_cnname, c.school_address
                FROM gmc_staffer_postbe AS p,gmc_company_post AS o,smc_school AS c
                WHERE p.post_id = o.post_id AND p.school_id = c.school_id AND p.postbe_id = '{$paramArray['re_postbe_id']}'
                ORDER BY p.postbe_ismianjob DESC";
            }


            $postpartArray = $this->DataControl->selectClear($sql);
            if (!$postpartArray) {
                $postpartArray = array();
            } else {
                foreach ($postpartArray as &$postpartOne) {
                    $postpartOne['staffer_cnname'] = $stafferOne['staffer_cnname'];
                    $postpartOne['staffer_enname'] = $stafferOne['staffer_enname'];
                    $postpartOne['staffer_mobile'] = $stafferOne['staffer_mobile'];
                    $postpartOne['staffer_branch'] = $stafferOne['staffer_branch'];
                    $postpartOne['staffer_birthday'] = $stafferOne['staffer_birthday'];
                    $postpartOne['staffer_img'] = $stafferOne['staffer_img'];
                    $postpartOne['staffer_sex'] = $stafferOne['staffer_sex'];
                    $postpartOne['account_class'] = $stafferOne['account_class'];
                }
            }
            if ($stafferOne['account_class'] == '1') {
                $temp = array();
                $temp['post_istopjob'] = 1;
                $postpartArray[] = $temp;
            }


            $result['postpart'] = $postpartArray;

            $datawhere = " 1 ";

            if ($schoolOne && $schoolOne['school_openclass'] == 0) {
                $datawhere .= " and m.module_id not in (619,620)";
            }

            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isachieve", "company_id='{$paramArray['company_id']}'");
            if ($companyOne && $companyOne['company_isachieve'] == 0) {
                $datawhere .= " and m.module_id not in (634,635,636)";
            }

            $contractOne = $this->getContract($paramArray['company_id']);

            if ($contractOne) {
                $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id=2");

                if (!$promoduleList) {
                    ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"), $this->companyOne['company_language']);
                }

                $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_icon as icon,m.father_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$paramArray['company_id']}' and ed.module_id=m.module_id),1) as status,m.module_isset
                from imc_module as m 
                where {$datawhere} and m.product_id=2 and module_isset = '0'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_weight asc,m.module_id asc";
                $moduleList = $this->DataControl->selectClear($sql);

                $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_icon as icon,m.father_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$paramArray['company_id']}' and ed.module_id=m.module_id),1) as status,m.module_isset
                from imc_module as m 
                where {$datawhere} and m.product_id=2 and module_isset = '1'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_weight asc,m.module_id asc";
                $settingList = $this->DataControl->selectClear($sql);

                $model = new \Model\Smc\ModuleModel($paramArray);

                $moduleArray = $model->getModuleTree($moduleList, 'id', 'url');
                $settingArray = $model->getModuleTree($settingList, 'id', 'url');

            } else {
                if ($paramArray['company_id'] == '1001') {
                    $moduleArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_isset
from imc_module as m where {$datawhere} and module_class = '2' and module_isset = '0' and module_ismajor <= '{$stafferOne['company_ismajor']}' order by module_weight ASC");                   $settingArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_isset
from imc_module as m where {$datawhere} and module_class = '2' and module_isset = '1' and module_ismajor <= '{$stafferOne['company_ismajor']}' order by module_weight ASC");
                } elseif ($paramArray['company_id'] == '8888') {
                    $moduleArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_isset
from imc_module as m where {$datawhere} and module_class = '2' and module_isset = '0' and module_ismajor <= '{$stafferOne['company_ismajor']}' and module_isshow = '1' order by module_weight ASC");
                    $settingArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_isset
from imc_module as m where {$datawhere} and module_class = '2' and module_isset = '1' and module_ismajor <= '{$stafferOne['company_ismajor']}' and module_isshow = '1' order by module_weight ASC");
                } else {
                    $moduleArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_isset
from imc_module as m where {$datawhere} and module_class = '2' and module_isset = '0' and module_ismajor <= '{$stafferOne['company_ismajor']}' and module_isshow = '1' and module_super = '0' order by module_weight ASC");
                    $settingArray = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_isset
from imc_module as m where {$datawhere} and module_class = '2' and module_isset = '1' and module_ismajor <= '{$stafferOne['company_ismajor']}' and module_isshow = '1' and module_super = '0' order by module_weight ASC");
                }
                $moduleArray = $this->trees($moduleArray);
                $settingArray = $this->trees($settingArray);
            }
            $result['children'] = $moduleArray;
            $result['setting'] = $settingArray;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'status' => $status, 'img' => $stafferOne['company_logo']);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "学校信息未获取成功", 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 修改职工建档
    function updateStafferDataAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer_info", "staffer_id", "staffer_id = '{$paramArray['worker_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['staffer_id'] = $paramArray['worker_id'];
            $data['info_birthday'] = $paramArray['info_birthday'];
            $data['info_idcardtype'] = $paramArray['info_idcardtype'];
            $data['info_idcard'] = $paramArray['info_idcard'];
            $data['info_emergcontactname'] = $paramArray['info_emergcontactname'];
            $data['info_emergcontactphone'] = $paramArray['info_emergcontactphone'];
            $data['info_nation'] = $paramArray['info_nation'];
            $data['info_nativeplace'] = $paramArray['info_nativeplace'];
            $data['info_education'] = $paramArray['info_education'];
            $data['info_schooltag'] = $paramArray['info_schooltag'];
            $data['info_major'] = $paramArray['info_major'];
            $data['info_marital'] = $paramArray['info_marital'];
            $data['info_address'] = $paramArray['info_address'];
            $data['info_updatetime'] = time();

            $datas = array();
            $datas['staffer_img'] = $paramArray['staffer_img'];
            $datas['staffer_enname'] = $paramArray['info_enname'];
            $datas['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_id'] = $this->LgStringSwitch("所属教师ID");
            $field['info_birthday'] = $this->LgStringSwitch("生日");
            $field['info_enname'] = $this->LgStringSwitch("英文名");
            $field['info_idcardtype'] = $this->LgStringSwitch("证件类型");
            $field['info_idcard'] = $this->LgStringSwitch("证件号码");
            $field['info_emergcontactname'] = $this->LgStringSwitch("紧急联系人");
            $field['info_emergcontactphone'] = $this->LgStringSwitch("紧急联系电话");
            $field['info_nation'] = $this->LgStringSwitch("民族");
            $field['info_nativeplace'] = $this->LgStringSwitch("籍贯");
            $field['info_education'] = $this->LgStringSwitch("最高学历");
            $field['info_schooltag'] = $this->LgStringSwitch("毕业学校");
            $field['info_major'] = $this->LgStringSwitch("所属专业");
            $field['info_marital'] = $this->LgStringSwitch("婚姻状况");
            $field['info_address'] = $this->LgStringSwitch("居住地址");
            $field['info_updatetime'] = $this->LgStringSwitch("修改时间");
            $field['staffer_img'] = $this->LgStringSwitch("教师头像");

            if ($this->DataControl->updateData("smc_staffer_info", "staffer_id = '{$paramArray['worker_id']}'", $data) && $this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['worker_id']}'", $datas)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工资料建档修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工资料建档修改失败', 'result' => $result);
            }
        } else {
            $data = array();
            $data['staffer_id'] = $paramArray['worker_id'];
            $data['info_birthday'] = $paramArray['info_birthday'];
            $data['info_idcardtype'] = $paramArray['info_idcardtype'];
            $data['info_idcard'] = $paramArray['info_idcard'];
            $data['info_emergcontactname'] = $paramArray['info_emergcontactname'];
            $data['info_emergcontactphone'] = $paramArray['info_emergcontactphone'];
            $data['info_nation'] = $paramArray['info_nation'];
            $data['info_nativeplace'] = $paramArray['info_nativeplace'];
            $data['info_education'] = $paramArray['info_education'];
            $data['info_schooltag'] = $paramArray['info_schooltag'];
            $data['info_major'] = $paramArray['info_major'];
            $data['info_marital'] = $paramArray['info_marital'];
            $data['info_address'] = $paramArray['info_address'];
            $data['info_createtime'] = time();

            $datas = array();
            $datas['staffer_img'] = $paramArray['staffer_img'];
            $datas['staffer_enname'] = $paramArray['info_enname'];
            $datas['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_id'] = $this->LgStringSwitch("所属教师ID");
            $field['info_birthday'] = $this->LgStringSwitch("生日");
            $field['info_enname'] = $this->LgStringSwitch("英文名");
            $field['info_idcardtype'] = $this->LgStringSwitch("证件类型");
            $field['info_idcard'] = $this->LgStringSwitch("证件号码");
            $field['info_emergcontactname'] = $this->LgStringSwitch("紧急联系人");
            $field['info_emergcontactphone'] = $this->LgStringSwitch("紧急联系电话");
            $field['info_nation'] = $this->LgStringSwitch("民族");
            $field['info_nativeplace'] = $this->LgStringSwitch("籍贯");
            $field['info_education'] = $this->LgStringSwitch("最高学历");
            $field['info_schooltag'] = $this->LgStringSwitch("毕业学校");
            $field['info_major'] = $this->LgStringSwitch("所属专业");
            $field['info_marital'] = $this->LgStringSwitch("婚姻状况");
            $field['info_address'] = $this->LgStringSwitch("居住地址");
            $field['info_createtime'] = $this->LgStringSwitch("创建时间");
            $field['staffer_img'] = $this->LgStringSwitch("教师头像");

            $this->DataControl->insertData('smc_staffer_info', $data);
            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['worker_id']}'", $datas);
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工资料建档成功", 'result' => $result);
            $this->addSmcWorkLog($paramArray['company_id'], $paramArray['school_id'], $paramArray['staffer_id'], "人事管理->职工管理", '编辑档案', dataEncode($paramArray));
        }
        return $res;
    }

    function getContect($paramArray)
    {
        $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_enname,
                s.staffer_mobile,
                s.staffer_sex,
                s.staffer_img,
                c.post_name
            FROM
                smc_staffer AS s
                LEFT JOIN gmc_staffer_postbe AS p ON s.staffer_id = p.staffer_id
	            left join gmc_company_post as c on p.post_id = c.post_id 
            WHERE p.school_id = '{$paramArray['school_id']}'
            GROUP BY s.staffer_id";
        $contect = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_cnname"] = $this->LgStringSwitch("中文名");
        $field["staffer_enname"] = $this->LgStringSwitch("英文名");
        $field["staffer_mobile"] = $this->LgStringSwitch("手机号");
        $field["staffer_sex"] = $this->LgStringSwitch("性别");
        $field["staffer_img"] = $this->LgStringSwitch("头像");
        $field["post_name"] = $this->LgStringSwitch("职务");

        $result = array();
        if ($contect) {
            $result["field"] = $field;
            $result["data"] = $contect;
            $res = array('error' => '0', 'errortip' => '获取联系人成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取联系人失败', 'result' => $result);
        }
        return $res;
    }

    function getStafferDataAction($paramArray)
    {
        $sql = "
            SELECT
               s.info_idcardtype,
               s.info_idcard,
               s.info_emergcontactname,
               s.info_emergcontactphone,
               s.info_nation,
               s.info_nativeplace,
               s.info_education,
               s.info_schooltag,
               s.info_major,
               s.info_marital,
               s.info_address,
               a.staffer_img,
               s.info_birthday,
               a.staffer_enname as info_enname
            FROM
                smc_staffer_info AS s left join smc_staffer as a on s.staffer_id = a.staffer_id
            WHERE s.staffer_id = '{$paramArray['worker_id']}'";
        $Data = $this->DataControl->selectClear($sql);

        $field = array();
//        $field['staffer_id'] = "所属教师ID";
//        $field['info_birthday'] = "生日";
        $field['info_idcardtype'] = $this->LgStringSwitch("证件类型");
        $field['info_idcard'] = $this->LgStringSwitch("证件号码");
        $field['info_emergcontactname'] = $this->LgStringSwitch("紧急联系人");
        $field['info_emergcontactphone'] = $this->LgStringSwitch("紧急联系电话");
        $field['info_nation'] = $this->LgStringSwitch("民族");
        $field['info_nativeplace'] = $this->LgStringSwitch("籍贯");
        $field['info_education'] = $this->LgStringSwitch("最高学历");
        $field['info_schooltag'] = $this->LgStringSwitch("毕业学校");
        $field['info_major'] = $this->LgStringSwitch("所属专业");
        $field['info_marital'] = $this->LgStringSwitch("婚姻状况");
        $field['info_address'] = $this->LgStringSwitch("居住地址");
        $field['staffer_img'] = $this->LgStringSwitch("教师头像");
        $field['info_birthday'] = $this->LgStringSwitch("生日");
        $field['info_enname'] = $this->LgStringSwitch("英文名");

        $result = array();
        if ($Data) {
            $result["field"] = $field;
            $result["data"] = $Data;
            $res = array('error' => '0', 'errortip' => '获取联系人成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '0', 'errortip' => '获取联系人失败', 'result' => $result);
        }
        return $res;
    }

    //获取校园角色管理列表
    function getSchoolRoleList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.postpart_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.postpart_id,
                p.postpart_name,
                p.postpart_remark,
                (select count(b.postbe_id) from gmc_staffer_postbe as b WHERE b.postpart_id = p.postpart_id and b.school_id = '{$paramArray['school_id']}') as num
            FROM
                smc_school_postpart AS p
            WHERE
                {$datawhere} and p.school_id = '{$paramArray['school_id']}'
            order by postpart_id DESC
            LIMIT {$pagestart},{$num}";

        $StafferList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.postpart_id) as a 
            FROM
                smc_school_postpart AS p
            WHERE
                {$datawhere} and p.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('postpart_name', 'postpart_remark', 'num ');
        $fieldname = $this->LgArraySwitch(array('角色', '权限描述', '角色人员数量'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($StafferList) {
            $result['list'] = $StafferList;
        } else {
            $result['list'] = array();
        }

        $result['allnum'] = $allnums;
        if (!$StafferList) {
            $res = array('error' => '1', 'errortip' => "暂无校园角色记录", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }


        return $res;
    }

    //删除校园角色
    function delSchoolRoleAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_school_postpart", "postpart_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($stafferOne) {
            if ($this->DataControl->delData("smc_school_postpart", "postpart_id = '{$paramArray['postpart_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工信息成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工信息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取校园通知
    function getNotice($paramArray)
    {
        $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
        if ($paramArray['pid'] == '') {
            $sql = "
           SELECT
                t.noticetype_name,
                n.notice_id,
                n.notice_connet,
                n.notice_title,
                n.notice_createtime,
                 st.staffer_cnname,
                FROM_UNIXTIME(n.notice_createtime,'%m-%d') as todaytime,
                (select count(r.read_id) from gmc_company_notice_read as r WHERE staffer_id = '{$paramArray['staffer_id']}' and r.notice_id = n.notice_id) as isread
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_schoolapply AS a ON a.notice_id = n.notice_id
                left join smc_staffer as st ON st.staffer_id = n.staffer_id
            WHERE n.notice_class = 1 and n.notice_status = 1 and a.school_id = '{$paramArray['school_id']}' order by notice_createtime DESC";
            $NoticeDetail = $this->DataControl->selectClear($sql);

            if ($NoticeDetail) {
                foreach ($NoticeDetail as $key => &$value) {
                    $value['notice_createtime'] = $this->ChangeTime($value['notice_createtime']);
                    $value['noticetype_name'] = $this->LgStringSwitch($value['noticetype_name']);
                }
            }
        } else {
            $sql = "
           SELECT
                t.noticetype_name,
                n.notice_id,
                n.notice_connet,
                n.notice_title,
                n.notice_createtime,
                 st.staffer_cnname,
                FROM_UNIXTIME(n.notice_createtime,'%m-%d') as todaytime,
                (select count(r.read_id) from gmc_company_notice_read as r WHERE staffer_id = '{$paramArray['staffer_id']}' and r.notice_id = n.notice_id) as isread
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_schoolapply AS a ON a.notice_id = n.notice_id
                left join gmc_company_notice_postapply as p on p.notice_id = n.notice_id
                left join smc_staffer as st ON st.staffer_id = n.staffer_id
            WHERE n.notice_class = 1 and n.notice_status = 1 and a.school_id = '{$paramArray['school_id']}' and p.post_id = '{$post['post_id']}' order by notice_createtime DESC";
            $NoticeDetail = $this->DataControl->selectClear($sql);

            if ($NoticeDetail) {
                foreach ($NoticeDetail as $key => &$value) {
                    $value['notice_createtime'] = $this->ChangeTime($value['notice_createtime']);
                    $value['noticetype_name'] = $this->LgStringSwitch($value['noticetype_name']);
                }
            }

        }


        $field = array();
        $field["noticetype_name"] = $this->LgStringSwitch("通知类型");
        $field["notice_title"] = $this->LgStringSwitch("通知标题");
        $field["notice_createtime"] = $this->LgStringSwitch("创建时间");

        $img = $this->DataControl->getFieldOne("gmc_company", "company_logo", "company_id = '{$paramArray['company_id']}'");

        $result = array();
        if ($NoticeDetail) {

            if ($paramArray['pid'] == '') {
                $messageCount = $this->DataControl->selectOne("
            SELECT
                (
	count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a WHERE a.staffer_id = '{$paramArray['staffer_id']}' and a.notice_class = 1 ) 
	) as a
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_schoolapply AS a ON a.notice_id = n.notice_id
            WHERE n.notice_class = 1 and n.notice_status = 1 and a.school_id = '{$paramArray['school_id']}' limit 0,1");

                $result['mescount'] = $messageCount['a'];

                $result["field"] = $field;
                $result["data"] = $NoticeDetail;
                $res = array('error' => '0', 'errortip' => '获取校园通知成功', 'result' => $result);
            } else {
                $messageCount = $this->DataControl->selectOne("
            SELECT
                (
	count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a WHERE a.staffer_id = '{$paramArray['staffer_id']}' and a.notice_class = 1 ) 
	) as a
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_schoolapply AS a ON a.notice_id = n.notice_id
                left join gmc_company_notice_postapply as p on p.notice_id = n.notice_id
            WHERE n.notice_class = 1 and n.notice_status = 1 and a.school_id = '{$paramArray['school_id']}' and p.post_id = '{$post['post_id']}' limit 0,1");

                $result['mescount'] = $messageCount['a'];

                $result["field"] = $field;
                $result["data"] = $NoticeDetail;
                $result["img"] = $img['company_logo'];
                $res = array('error' => '0', 'errortip' => '获取校园通知成功', 'result' => $result);
            }

        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取校园通知失败', 'result' => $result);
        }
        return $res;
    }

    //查看通知详情
    function getNoticeDetail($paramArray)
    {
        $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "postbe_id = '{$paramArray['re_postbe_id']}'");

        $sql = "
            SELECT
                s.staffer_cnname,
                n.notice_title,
                n.notice_filelist,
                s.staffer_cnname as notice_author,
                n.notice_connet,
                FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d %H:%i' ) AS notice_createtime,
                o.organize_cnname
            FROM
                gmc_company_notice AS n left join gmc_company_organize as o on n.notice_authority = o.organize_id
                left join smc_staffer as s On s.staffer_id = n.staffer_id
            where n.notice_id = '{$paramArray['notice_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        $read = $this->DataControl->getFieldOne('gmc_company_notice_read', 'read_id', "notice_id = '{$paramArray['notice_id']}' and staffer_id = '{$paramArray['staffer_id']}'");

        if (!$read) {
            $data['notice_id'] = $paramArray['notice_id'];
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['school_id'] = $paramArray['school_id'];
            $data['notice_class'] = '1';
            $this->DataControl->insertData('gmc_company_notice_read', $data);
        }


        $field = array();
        $field["notice_title"] = $this->LgStringSwitch("标题");
        $field["notice_author"] = $this->LgStringSwitch("发布人");
        $field["notice_connet"] = $this->LgStringSwitch("内容");
        $field["notice_createtime"] = $this->LgStringSwitch("发布时间");
        $field["organize_cnname"] = $this->LgStringSwitch("所属部门");
        $field["staffer_cnname"] = "--";
        $field["notice_filelist"] = "--";

        $result = array();

        if ($paramArray['re_postbe_id'] == '0') {
            if ($NoticeDetail) {

                $messageCount = $this->DataControl->selectOne("
            SELECT
                (
	count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a WHERE a.staffer_id = '{$paramArray['staffer_id']}' and a.notice_class = 1 ) 
	) as a
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_schoolapply AS a ON a.notice_id = n.notice_id
                left join gmc_company_notice_postapply as p on p.notice_id = n.notice_id
            WHERE n.notice_class = 1 and n.notice_status = 1 and a.school_id = '{$paramArray['school_id']}' and p.post_id = '{$post['post_id']}' limit 0,1");

                $result['mescount'] = $messageCount['a'];


                $result["field"] = $field;
                $result["data"] = $NoticeDetail;
                $res = array('error' => '0', 'errortip' => '查看消息详情成功', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '查看消息详情失败', 'result' => $result);
            }
        } else {
            if ($NoticeDetail) {
                $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "postbe_id = '{$paramArray['re_postbe_id']}'");

                $messageCount = $this->DataControl->selectOne("
            SELECT
                (
	count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a WHERE a.staffer_id = '{$paramArray['staffer_id']}' and a.notice_class = 1) 
	) as a
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_schoolapply AS s ON n.notice_id = s.notice_id
                LEFT JOIN gmc_company_notice_postapply AS p ON n.notice_id = p.notice_id
            WHERE n.notice_class = 1 and n.notice_status = 1 and s.school_id = '{$paramArray['school_id']}' and p.post_id = '{$post['post_id']}' limit 0,1");

                $result['mescount'] = $messageCount['a'];


                $result["field"] = $field;
                $result["data"] = $NoticeDetail;
                $res = array('error' => '0', 'errortip' => '查看消息详情成功', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '查看消息详情失败', 'result' => $result);
            }
        }

        return $res;
    }

    //添加校园角色
    function addSchoolRoleAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['postpart_name'] = $paramArray['postpart_name'];
        $data['postpart_remark'] = $paramArray['postpart_remark'];

        $field = array();
        $field['company_id'] = $this->LgStringSwitch("所属公司");
        $field['school_id'] = $this->LgStringSwitch("学校id");
        $field['postpart_name'] = $this->LgStringSwitch("角色名称");
        $field['postpart_remark'] = $this->LgStringSwitch("角色备注");

        $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_id', "postpart_name = '{$paramArray['postpart_name']}'");
        if ($postpart_name) {
            ajax_return(array('error' => 1, 'errortip' => "角色名称已存在!"), $this->companyOne['company_language']);
        }

        if ($a = $this->DataControl->insertData('smc_school_postpart', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $result["id"] = $a;
            $res = array('error' => '0', 'errortip' => "添加角色成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加角色失败', 'result' => $result);
        }
        return $res;
    }

    //编辑校园角色
    function updateSchoolRoleAction($paramArray)
    {
        $SchoolRoleOne = $this->DataControl->getFieldOne("smc_school_postpart", "postpart_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($SchoolRoleOne) {
            $data = array();
            $data['postpart_name'] = $paramArray['postpart_name'];
            $data['postpart_remark'] = $paramArray['postpart_remark'];

            $field = array();
            $field['postpart_name'] = $this->LgStringSwitch("角色名称");
            $field['postpart_remark'] = $this->LgStringSwitch("角色备注");

            $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_name', "postpart_id = '{$paramArray['postpart_id']}'");
            if ($paramArray['postpart_name'] != $postpart_name['postpart_name']) {
                $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_id', "postpart_name = '{$paramArray['postpart_name']}'");
                if ($postpart_name) {
                    ajax_return(array('error' => 1, 'errortip' => "角色名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_school_postpart", "postpart_id = '{$paramArray['postpart_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑校园角色成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑校园角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getpostList($paramArray)
    {
        $result = array();
//        $company_name = $this->DataControl->getFieldOne('gmc_company','company_cnname',"company_id = '{$paramArray['company_id']}'");
//        $result['title'] = $company_name['company_cnname'];
        $data = $this->DataControl->selectClear("
            SELECT
                p.post_name,b.staffer_id,s.staffer_cnname,p.post_id,s.staffer_branch
            FROM
                gmc_company_post AS p
                LEFT JOIN gmc_staffer_postbe AS b ON p.post_id = b.post_id
                left join smc_staffer as s on b.staffer_id = s.staffer_id
            WHERE
                p.company_id = '{$paramArray['company_id']}' 
                AND post_type = 1 
                AND b.school_id = '{$paramArray['school_id']}'
                AND s.staffer_cnname != ''
                group by b.staffer_id
                ");

        $datas = $this->DataControl->selectClear("select p.post_name from
                gmc_company_post AS p
                LEFT JOIN gmc_staffer_postbe AS b ON p.post_id = b.post_id
                left join smc_staffer as s on b.staffer_id = s.staffer_id
                 WHERE
                p.company_id = '{$paramArray['company_id']}' 
                AND post_type = 1 
                AND b.school_id = '{$paramArray['school_id']}'
                group by p.post_name");

        $a = array();
        $a['data'] = $data;
        $a['datas'] = $datas;

        $data = $this->tree($a);

        $result['children'] = $data;
        return $result;
    }

    function tree($items)
    {
        $son = array();
        foreach ($items['datas'] as $k => $v) {
            $son[$k]['post_name'] = $v['post_name'];
            foreach ($items['data'] as $key => $value) {
                if ($v['post_name'] == $value['post_name']) {
                    $son[$k]['children'][$key]['post_name'] = $value['staffer_cnname'];
                    $son[$k]['children'][$key]['staffer_id'] = $value['staffer_id'];
                    $son[$k]['children'][$key]['staffer_branch'] = $value['staffer_branch'];
                }
            }
        }
        return $son;
    }

    function trees($items)
    {
        $son = array();
        $count = 0;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
//                    $son[$k]['url'] = $v['module_markurl'].'?module_id='.$v['module_id'];
                    $son[$k]['icon'] = $v['module_icon'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $markurlOne = $this->DataControl->selectOne("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' order by module_weight ASC limit 0,1");
                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . $count;
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($markurlOne) {
                                $son[$k]['children'][$key]['url'] = $markurlOne['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];

                                }
                            }
                        }
                    }
                    $count = 0;
                }
            }
        }
        return $son;
    }

    //添加职工角色
    function addRoleStafferAction($paramArray)
    {
        $data = array();

        $roleList = json_decode(stripslashes($paramArray['role']), true);
        foreach ($roleList as $item) {
            $data['postpart_id'] = $item['postpart_id'];

            $this->DataControl->updateData('gmc_staffer_postbe', "company_id = '{$item['company_id']}' and school_id ='{$item['school_id']}' and staffer_id ='{$item['staffer_id']}'", $data);

        }

        $res = array('error' => '0', 'errortip' => "添加职工角色成功", 'result' => array());

        return $res;
    }

    //校园角色成员列表
    function getRoleList($paramArray)
    {
        $sql = "
            SELECT 
                s.staffer_cnname,
                s.staffer_enname,
                s.staffer_branch,
                b.postbe_id
            FROM
                gmc_staffer_postbe AS b
                LEFT JOIN smc_staffer AS s ON b.staffer_id = s.staffer_id 
            WHERE
                b.company_id = '{$paramArray['company_id']}' and b.school_id = '{$paramArray['school_id']}' and postpart_id = '{$paramArray['postpart_id']}'";

        $StafferList = $this->DataControl->selectClear($sql);

        $fieldstring = array('staffer_cnname', 'staffer_enname', 'staffer_branch');
        $fieldname = array('中文名', '英文名', '编号');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($StafferList) {
            $result['list'] = $StafferList;
        } else {
            $result['list'] = array();
        }

        if (!$StafferList) {
            $res = array('error' => '1', 'errortip' => "暂无列表成员", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }


        return $res;
    }

    //删除职工校园角色
    function delStafferRoleAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['postpart_id'] = '0';
            if ($this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$paramArray['postbe_id']}'", $data)) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工校园角色成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工校园角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑学校职工角色
    function updatePostPartAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['worker_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['postpart_id'] = $paramArray['postpart_id'];

            $field = array();
            $field['postpart_id'] = $this->LgStringSwitch("角色id");
            if ($this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$paramArray['worker_id']}' and school_id = '{$paramArray['school_id']}'", $data)) {
                $data = array();
                $data['staffer_leavetime'] = $paramArray['staffer_leavetime'];
                $data['staffer_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['worker_id']}'", $data);
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "学校职工角色修改成功", 'result' => $result);
                $this->addSmcWorkLog($paramArray['company_id'], $paramArray['school_id'], $paramArray['staffer_id'], "人事管理->职工管理", '改变角色', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '学校职工角色修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //修改密码
    function updatePassAction($paramArray)
    {
        $data = array();
        if ($paramArray['staffer_pass1'] == $paramArray['staffer_pass2']) {
            $data['staffer_pass'] = md5($paramArray['staffer_pass1']);
            $data['staffer_bakpass'] = $paramArray['staffer_pass1'];
            $data['staffer_updatetime'] = time();
        } else {
            ajax_return(array('error' => 1, 'errortip' => "两次输入的密码不相同！"), $this->companyOne['company_language']);
        }


        $field = array();
        $field["staffer_pass"] = $this->LgStringSwitch("md5密码");
        $field["staffer_bakpass"] = $this->LgStringSwitch("密码备注");
        if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['staffer_id']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改密码成功", 'result' => $result);
            $this->addSmcWorkLog($paramArray['company_id'], $paramArray['school_id'], $paramArray['staffer_id'], "修改密码", '修改密码', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '修改密码失败', 'result' => $result);
        }
        return $res;
    }


    function ChangeUrl($request)
    {
        $contractOne = $this->getContract($request['company_id']);
        $url = $request['module_url'];
        $domain = strstr($url, '?');
        if ($domain) {
            $urls = substr($url, 0, strpos($url, '?'));
        } else {
            $urls = $url;
        }


        if (!$contractOne) {

            $res = $this->ChangeUrlbak($request, $urls);
            return $res;
//            ajax_return(array('error' => 1, 'errortip' => "无可用合同!"),$this->companyOne['company_language']);
        }

        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id=2");

        if (!$promoduleList) {
            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"), $this->companyOne['company_language']);
        }

        $module_id = $this->DataControl->getFieldOne("imc_module", "module_id,father_id,module_markurl", "module_markurl = '{$urls}' and module_class = '2'");

        $next_id = $this->DataControl->getFieldOne("imc_module", "father_id", "module_id = '{$module_id['module_id']}'");


        if ($request['re_postbe_id'] == '0') {
            $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_icon as icon,m.father_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$request['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m 
                where m.product_id=2
                having status=1
                order by m.module_weight asc,m.module_id asc";
        } else {
            $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'school_id,postrole_id,postpart_id', "postbe_id = '{$request['re_postbe_id']}'");

            if ($postbeOne['school_id'] !== '0') {
                $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id
                        ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$request['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                where m.product_id=2 and u.postpart_id = '{$postbeOne['postpart_id']}'
                having status=1
                order by m.module_weight asc,m.module_id asc";

            } else {
                $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id,u.postpart_id
                        ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$request['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                left join gmc_company_postrole as p on p.postpart_id=u.postpart_id
                where m.product_id=2 and p.postrole_id = '{$postbeOne['postrole_id']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_weight asc,m.module_id asc";
            }
        }

        $moduleList = $this->DataControl->selectClear($sql);

        $model = new \Model\Smc\ModuleModel($request);

        $tree = $model->getModuleTree($moduleList, 'id', 'url');
        $model->getArray($tree, 'id', 'children');
        if($request['status'] == '1' && $request['re_postbe_id'] !== '0'){
            $a = $this->DataControl->selectClear("select a.module_id from imc_module as a where a.father_id = '{$module_id['module_id']}'");
            if($a){
                $mid = $this->DataControl->selectClear("select m.module_id from imc_module as m where m.father_id = '{$module_id['module_id']}'");
            }else{
                $mid = $this->DataControl->selectClear("select m.module_id from imc_module as m where m.module_id = '{$module_id['module_id']}'");

            }

            $array = array_column($mid,'module_id');
            $implode = implode(",",$array);

            $module = $this->DataControl->selectOne("select u.module_id,i.module_markurl,i.father_id from smc_staffer_usermodule as u left join imc_module as i on i.module_id = u.module_id where u.module_id in ($implode) and u.postpart_id ='{$postbeOne['postpart_id']}' and i.module_id not in (select a.father_id from imc_module as a where a.father_id <> '0' )");
            $result = array();
            $result['children'] = $tree;
            $result['module_id'] = $module['module_id'];
            $result['father_id'] = $module['father_id'];
            $result['url'] = $module['module_markurl'];
            $result['Sefatehr_id'] = $next_id['father_id'];

        }else{
            if (!in_array($module_id['module_id'], $model->moduleOne)) {
                $res = array('error' => '1', 'errortip' => "您没有权限", 'result' => array());
                return $res;
            }

            $result = array();
            $result['children'] = $tree;
            $result['module_id'] = $module_id['module_id'];
            $result['father_id'] = $module_id['father_id'];
            $result['url'] = $module_id['module_markurl'];
            $result['Sefatehr_id'] = $next_id['father_id'];
        }






        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        return $res;
    }


    //更换地址
    function ChangeUrlbak($paramArray, $urls)
    {
        $module_id = $this->DataControl->getFieldOne("imc_module", "module_id,father_id", "module_markurl = '{$urls}'");

        $next_id = $this->DataControl->getFieldOne("imc_module", "father_id", "module_id = '{$module_id['module_id']}'");

        if ($paramArray['re_postbe_id'] == '0') {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '2'  order by module_weight ASC");
        } else {
            $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'school_id,postrole_id,postpart_id', "postbe_id = '{$paramArray['re_postbe_id']}'");
            if ($postbeOne['school_id'] !== '0') {
                $data = $this->DataControl->selectClear("
            SELECT
                u.module_id,
                u.postpart_id,
                m.father_id,
                m.module_markurl,
                m.module_icon,
                m.module_name
            FROM
                smc_staffer_usermodule AS u
                LEFT JOIN imc_module AS m ON u.module_id = m.module_id
            WHERE
                u.postpart_id = '{$postbeOne['postpart_id']}' and m.module_class = '2'
            order by m.module_weight ASC");
            } else {
                $data = $this->DataControl->selectClear("
            SELECT
                u.module_id,
                u.postpart_id,
                m.father_id,
                m.module_markurl,
                m.module_icon,
                m.module_name
            FROM
                smc_staffer_usermodule AS u
                LEFT JOIN imc_module AS m ON u.module_id = m.module_id
                LEFT JOIN gmc_company_postrole AS p ON u.postpart_id = p.postpart_id
            WHERE
                p.postrole_id = '{$postbeOne['postrole_id']}' and m.module_class = '2'
            order by m.module_weight ASC");
            }

        }


        $data = $this->trees($data);

        $result['children'] = $data;
        $result['module_id'] = $module_id['module_id'];
        $result['father_id'] = $module_id['father_id'];
        $result['Sefatehr_id'] = $next_id['father_id'];

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        return $res;

    }

    //职工离职
    function updateLeaveAction($paramArray)
    {
        $TeachingtypeOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['staffer_leave'] = '1';
            $data['staffer_leavetime'] = $paramArray['staffer_leavetime'];
            $data['staffer_leavecause'] = $paramArray['staffer_leavecause'];
            $data['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_leavetime'] = $this->LgStringSwitch("离职时间");
            $field['staffer_leavecause'] = $this->LgStringSwitch("离职原因");
            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'", $data)) {
//                暂时关闭该功能
//                $Model = new \Model\Crm\IntentionClientModel();
//                $Model ->IntentionToClientApi($TeachingtypeOne['staffer_id'],$paramArray['company_id']);

                $postbeList = $this->DataControl->selectClear("SELECT postbe_id FROM gmc_staffer_postbe WHERE staffer_id = '{$paramArray['stafferOne_id']}'");
                if($postbeList){
                    foreach ($postbeList as $val) {
                        $this->DataControl->delData("gmc_staffer_postbe", "postbe_id = '{$val['postbe_id']}'");
                    }
                }

                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['staffer_id'] = $paramArray['stafferOne_id'];
                $data['workchange_code'] = 'Z02';
                $data['postchangeslog_note'] = $this->LgStringSwitch("集团给教师做离职操作，进行离职清算操作");
                $data['postchangeslog_day'] = date("Y-m-d", time());
                $data['postchangeslog_createtime'] = time();
                $this->DataControl->insertData("gmc_staffer_postchangeslog", $data);

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "离职成功", 'result' => $result);
//                $this->addSmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'离职',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '离职失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //解除职务
    function relieveLeaveAction($paramArray)
    {
        $TeachingtypeOne = $this->DataControl->selectOne("SELECT sf.staffer_leave,sf.staffer_isparttime,p.postbe_id,p.postbe_ismianjob FROM smc_staffer as sf LEFT JOIN gmc_staffer_postbe as p ON p.staffer_id = sf.staffer_id WHERE p.company_id = '{$paramArray['company_id']}' and p.school_id = '{$paramArray['school_id']}' and sf.staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($TeachingtypeOne) {
            if($TeachingtypeOne['staffer_leave'] == 0 && $TeachingtypeOne['staffer_isparttime'] == 0 && $TeachingtypeOne['postbe_ismianjob'] == 1 && $paramArray['company_iscrmoperateleave'] == 0){
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '主职职务解除需联系管理员', 'result' => $result);
                return $res;
            }

            if ($this->DataControl->delData("gmc_staffer_postbe", "postbe_id = '{$TeachingtypeOne['postbe_id']}'")) {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '0', 'errortip' => "解除成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '解除失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工复职
    function updateJoinAction($paramArray)
    {
        $postbe = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "staffer_id = '{$paramArray['staffer_id']}' and school_id = '{$paramArray['school_id']}'");
        $topjob = $this->DataControl->getFieldOne("gmc_company_post", "post_istopjob", "post_id = '{$postbe['post_id']}'");
        if ($topjob['post_istopjob'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => "您没有权限，请联系校长操作!"), $this->companyOne['company_language']);
        }
        $TeachingtypeOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['staffer_leave'] = '0';
            $data['staffer_jointime'] = $paramArray['staffer_jointime'];
            $data['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_jointime'] = $this->LgStringSwitch("入职时间");

            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "复职成功", 'result' => $result);
//                $this->addSmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'复职',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '复职失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //新增兼职职工
    function addPartStafferAction($paramArray)
    {
        $data = array();

        $like = date("Ymd", time());

        $stuInfo = $this->DataControl->selectOne("select staffer_branch from smc_staffer where staffer_branch like '{$like}%' AND LENGTH(staffer_branch) = '14' order by staffer_branch DESC limit 0,1");
        if ($stuInfo) {
            $data['staffer_branch'] = $stuInfo['staffer_branch'] + 1;
        } else {
            $data['staffer_branch'] = $like . '000001';
        }
        if ($paramArray['staffer_mobile'] == '') {
            $data['staffer_mobile'] = $data['staffer_branch'];
        } else {
            $data['staffer_mobile'] = $paramArray['staffer_mobile'];
            $data['staffer_pass'] = md5(substr($paramArray['staffer_mobile'], -6));
            $data['staffer_bakpass'] = substr($paramArray['staffer_mobile'], -6);
        }

        $data['staffer_cnname'] = $paramArray['staffer_cnname'];
        $data['company_id'] = $paramArray['company_id'];
        $data['post_id'] = $paramArray['post_id'];

        $data['staffer_enname'] = $paramArray['staffer_enname'];
        $data['staffer_native'] = $paramArray['staffer_native'];

        $contractOne = $this->getContract($paramArray['company_id']);

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $data['staffer_isparttime'] = '0';
        } else {
            $data['staffer_isparttime'] = '1';
        }

        $data['staffer_employeepid'] = $paramArray['staffer_employeepid'];
        $data['staffer_email'] = $paramArray['staffer_email'];
        $data['staffer_sex'] = $paramArray['staffer_sex'];
        $data['staffer_jointime'] = date('Y-m-d', time());
        $data['staffer_createtime'] = time();

        $staffer_branch = $this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_branch = '{$data['staffer_branch']}'");
        if ($staffer_branch) {
            ajax_return(array('error' => 1, 'errortip' => "编号已存在!添加职工失败!"), $this->companyOne['company_language']);
        }
        if ($paramArray['staffer_mobile'] != '') {
            $staffer_mobile = $this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_mobile = '{$paramArray['staffer_mobile']}' and company_id='{$paramArray['company_id']}' and company_id = '{$paramArray['company_id']}'");
            if ($staffer_mobile) {
                ajax_return(array('error' => 1, 'errortip' => "手机号已存在!添加职工失败！"), $this->companyOne['company_language']);
            }
        }

        $staffer_id = $this->DataControl->insertData("smc_staffer", $data);

        $datas = array();
        $datas['staffer_id'] = $staffer_id;
        $datas['info_birthday'] = $paramArray['staffer_birthday'];
        $datas['info_createtime'] = time();

        $this->DataControl->insertData('smc_staffer_info', $datas);

        $datass = array();
        $datass['post_id'] = $paramArray['post_id'];
        $datass['school_id'] = $paramArray['school_id'];
        $datass['company_id'] = $paramArray['company_id'];
        $postpart_id = $this->DataControl->getFieldOne("gmc_company_post", "postpart_id", "post_id = '{$paramArray['post_id']}'");
        $datass['postpart_id'] = $postpart_id['postpart_id'];
        $datass['staffer_id'] = $staffer_id;
        $datass['postbe_ismianjob'] = '1';
        $datass['postbe_createtime'] = time();

        $this->DataControl->insertData('gmc_staffer_postbe', $datass);

        $res = array('error' => '0', 'errortip' => "添加职工成功", 'id' => $staffer_id);

        return $res;
    }


    //编辑兼职职工
    function updatePartStafferAction($paramArray)
    {
        $data = array();

        $data['staffer_mobile'] = $paramArray['staffer_mobile'];
        $data['staffer_cnname'] = $paramArray['staffer_cnname'];
        $data['staffer_enname'] = $paramArray['staffer_enname'];
        $data['staffer_native'] = $paramArray['staffer_native'];
        $contractOne = $this->getContract($paramArray['company_id']);

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $data['staffer_isparttime'] = '0';

        } else {
            $data['staffer_isparttime'] = '1';

        }
        $data['staffer_employeepid'] = $paramArray['staffer_employeepid'];
        $data['staffer_email'] = $paramArray['staffer_email'];
        $data['staffer_sex'] = $paramArray['staffer_sex'];
        $data['staffer_updatetime'] = time();

        if ($paramArray['staffer_mobile'] != '') {
            $staffer_id = $this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_mobile = '{$paramArray['staffer_mobile']}' and staffer_id != '{$paramArray['worker_id']}' and company_id = '{$paramArray['company_id']}'");
            if ($staffer_id) {
                ajax_return(array('error' => 1, 'errortip' => "手机号已存在!修改职工信息失败!"), $this->companyOne['company_language']);
            }
        }


        $this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['worker_id']}'", $data);

        $datas = array();
        $datas['info_birthday'] = $paramArray['staffer_birthday'];
        $datas['info_updatetime'] = time();

        $this->DataControl->updateData('smc_staffer_info', "staffer_id = '{$paramArray['worker_id']}'", $datas);

        $datass = array();
        $datass['post_id'] = $paramArray['post_id'];
        $postpart_id = $this->DataControl->getFieldOne("gmc_company_post", "postpart_id", "post_id = '{$paramArray['post_id']}'");
        $datass['postpart_id'] = $postpart_id['postpart_id'];

        $this->DataControl->updateData('gmc_staffer_postbe', "staffer_id = '{$paramArray['worker_id']}'", $datass);

        $res = array('error' => '0', 'errortip' => "编辑成功");

        return $res;
    }


    //职工管理 -- 编辑职工校园职务
    function updateStafferSchoolPostAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postbe_id = '{$paramArray['postbe_id']}'");
        if ($stafferOne) {

            $a = $this->DataControl->selectOne("
                SELECT
                    p.post_istopjob
                FROM
                    gmc_staffer_postbe AS sp
                LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
                WHERE sp.postbe_id = '{$paramArray['re_postbe_id']}'");

            if ($a['post_istopjob'] != '1') {
                ajax_return(array('error' => 1, 'errortip' => "您没有权限！"), $this->companyOne['company_language']);
            }

            $data = array();
            $data['post_id'] = $paramArray['post_id'];
            $data['organize_id'] = $paramArray['organize_id'];
            $data['school_id'] = $paramArray['schoolOne_id'];
            $data['postpart_id'] = $paramArray['postpart_id'];

            $field = array();
            $field['post_id'] = "职务id";
            $field['organize_id'] = "组织id";
            $field['school_id'] = "学校id";
            $field['postpart_id'] = "角色id";
            $field['postbe_iscrmuser'] = "是否拥有CRM权限1可使用0不使用";
            $field['postbe_isreceptionuser'] = "接待权限";
            $field['postbe_crmuserlevel'] = "0普通权限1高管权限";

            if ($this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$paramArray['postbe_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工跨校职务修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工跨校职务修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //添加班外课时
    function classScheduleAction($paramArray)
    {
        $data = array();
        $data['outclasstype_id'] = $paramArray['outclasstype_id'];
        $data['hour_scname'] = $paramArray['hour_scname'];
        $data['hour_remk'] = $paramArray['hour_remk'];
        $data['staffer_id'] = $paramArray['worker_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['company_id'] = $paramArray['company_id'];
        $data['hour_createtime'] = time();

        $rate = $this->DataControl->getFieldOne("smc_code_outclasstype", "outclasstype_rate", "outclasstype_id = '{$paramArray['outclasstype_id']}'");

        if ($paramArray['type'] == '1') {
            $data['hour_day'] = $paramArray['hour_day'];
            $data['hour_starttime'] = $paramArray['hour_starttime'];
            $data['hour_endtime'] = $paramArray['hour_endtime'];
//            $data['hour_createtime'] = $paramArray['hour_createtime'];
            $data['school_id'] = $paramArray['school_id'];

            $data['hour_classtimes'] = round((strtotime($paramArray['hour_endtime']) - strtotime($paramArray['hour_starttime'])) / 3600 * $rate['outclasstype_rate'], 2);

            $a = $this->DataControl->getFieldOne("smc_outclass_hour", "hour_id", "staffer_id = '{$paramArray['worker_id']}' and hour_day = '{$paramArray['hour_day']}' and hour_iscancel = '0' and ((hour_starttime > '{$paramArray['hour_starttime']}' and hour_starttime < '{$paramArray['hour_endtime']}') or (hour_endtime > '{$paramArray['hour_starttime']}' and hour_endtime < '{$paramArray['hour_endtime']}') or (hour_endtime > '{$paramArray['hour_endtime']}' and hour_starttime < '{$paramArray['hour_starttime']}') or (hour_endtime < '{$paramArray['hour_endtime']}' and hour_starttime > '{$paramArray['hour_starttime']}'))");

            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "上课时间冲突！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_outclass_hour', $data);
        } else {

            $time_start = strtotime($paramArray['start_day']);
            $time_end = strtotime($paramArray['end_day']);
            $date = array();

            while ($time_start <= $time_end) {
                $dateOne = date('Y-m-d', $time_start);

                if (in_array(date('N', strtotime($dateOne)), json_decode(stripslashes($paramArray['json_week']), 1))) {

                    $date[] = $dateOne;
                }

                $time_start = strtotime('+1 day', $time_start);
            }

            if ($paramArray['is_skipweek'] == 0) {
                for ($i = 0; $i < count($date); $i++) {
                    $addnum = $i;
                    if ($addnum > count($date)) {
                        continue;
                    }
                    $data['hour_day'] = $date[$addnum];
                    $data['hour_starttime'] = $paramArray['hour_starttime'];
                    $data['hour_endtime'] = $paramArray['hour_endtime'];
//                    $data['hour_createtime'] = $paramArray['hour_createtime'];
                    $data['staffer_id'] = $paramArray['worker_id'];
                    $data['school_id'] = $paramArray['school_id'];

                    $data['hour_classtimes'] = round((strtotime($paramArray['hour_endtime']) - strtotime($paramArray['hour_starttime'])) / 3600 * $rate['outclasstype_rate'], 2);


                    $a = $this->DataControl->getFieldOne("smc_outclass_hour", "hour_id", "staffer_id = '{$paramArray['worker_id']}' and hour_day = '{$data['hour_day']}' and hour_iscancel = '0' and ((hour_starttime > '{$paramArray['hour_starttime']}' and hour_starttime < '{$paramArray['hour_endtime']}') or (hour_endtime > '{$paramArray['hour_starttime']}' and hour_endtime < '{$paramArray['hour_endtime']}') or (hour_endtime > '{$paramArray['hour_endtime']}' and hour_starttime < '{$paramArray['hour_starttime']}') or (hour_endtime < '{$paramArray['hour_endtime']}' and hour_starttime > '{$paramArray['hour_starttime']}'))");

                    if ($a) {
                        ajax_return(array('error' => 1, 'errortip' => "上课时间冲突！"), $this->companyOne['company_language']);
                    }
                    $this->DataControl->insertData('smc_outclass_hour', $data);
                }
            } else {
                for ($i = 0; $i < count($date); $i += 2) {
                    $addnum = $i;
                    if ($addnum > count($date)) {
                        continue;
                    }
                    $data['hour_day'] = $date[$addnum];
                    $data['hour_starttime'] = $paramArray['hour_starttime'];
                    $data['hour_endtime'] = $paramArray['hour_endtime'];
//                    $data['hour_createtime'] = $paramArray['hour_createtime'];
                    $data['staffer_id'] = $paramArray['worker_id'];
                    $data['school_id'] = $paramArray['school_id'];

                    $data['hour_classtimes'] = round((strtotime($paramArray['hour_endtime']) - strtotime($paramArray['hour_starttime'])) / 3600 * $rate['outclasstype_rate'], 2);


                    $a = $this->DataControl->getFieldOne("smc_outclass_hour", "hour_id", "staffer_id = '{$paramArray['worker_id']}' and hour_day = '{$data['hour_day']}' and hour_iscancel = '0' and ((hour_starttime > '{$paramArray['hour_starttime']}' and hour_starttime < '{$paramArray['hour_endtime']}') or (hour_endtime > '{$paramArray['hour_starttime']}' and hour_endtime < '{$paramArray['hour_endtime']}') or (hour_endtime > '{$paramArray['hour_endtime']}' and hour_starttime < '{$paramArray['hour_starttime']}') or (hour_endtime < '{$paramArray['hour_endtime']}' and hour_starttime > '{$paramArray['hour_starttime']}'))");

                    if ($a) {
                        ajax_return(array('error' => 1, 'errortip' => "上课时间冲突！"), $this->companyOne['company_language']);
                    }
                    $this->DataControl->insertData('smc_outclass_hour', $data);
                }
            }

        }

        $result = array();
        $result["data"] = $data;
        $res = array('error' => '0', 'errortip' => "添加班外课时成功", 'result' => $result);
        return $res;
    }

    function addCourse($request)
    {
        if ($this->DataControl->getFieldOne("kmc_hobbycourse", "hobbycourse_id", "company_id='{$request['company_id']}' and school_id ='{$this->schoolOne['school_id']}' and hobbycourse_cnname='{$request['hobbycourse_cnname']}' and hobbycourse_status>='0'")) {
            $this->error = true;
            $this->errortip = "兴趣班名称已存在";
            return false;
        }

        if (isset($request['plan_list']) && $request['plan_list'] != '') {
            $list = json_decode(stripslashes($request['plan_list']), 1);
            foreach ($list as $key1 => $val) {
                foreach ($list as $key2 => $value) {
                    if ($key1 != $key2) {
                        if ($val['lessonplan_week'] == $value['lessonplan_week']) {
                            $bool = $this->is_time_cross($val['lessonplan_starttime'], $val['lessonplan_endtime'], $value['lessonplan_starttime'], $value['lessonplan_endtime']);

                            if ($bool) {
                                $this->error = true;
                                $this->errortip = "同一天时间不可交叉";
                                return false;
                            }
                        }
                    }
                }
            }
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $this->schoolOne['school_id'];
        $data['hobbytype_id'] = $request['hobbytype_id'];
        $data['hobbycourse_cnname'] = $request['hobbycourse_cnname'];
        $data['classroom_id'] = $request['classroom_id'];
        $data['hobbycourse_fullnums'] = $request['hobbycourse_fullnums'];
        $data['hobbycourse_stdate'] = $request['hobbycourse_stdate'];
        $data['hobbycourse_enddate'] = $request['hobbycourse_enddate'];
        $data['hobbycourse_status'] = '1';
        $data['hobbycourse_createtime'] = time();
        $hobbycourse_id = $this->DataControl->insertData("kmc_hobbycourse", $data);

        if (isset($request['teacher_list']) && $request['teacher_list'] != '') {
            $list = json_decode(stripslashes($request['teacher_list']), 1);
            foreach ($list as $val) {
                $data = array();
                $data['hobbycourse_id'] = $hobbycourse_id;
                $data['staffer_id'] = $val['staffer_id'];
                $data['teach_createtime'] = time();
                $this->DataControl->insertData("kmc_hobbycourse_teach", $data);
            }
        }

        if (isset($request['plan_list']) && $request['plan_list'] != '') {
            $list = json_decode(stripslashes($request['plan_list']), 1);
            foreach ($list as $val) {
                $data = array();
                $data['hobbycourse_id'] = $hobbycourse_id;
                $data['lessonplan_week'] = $val['lessonplan_week'];
                $data['lessonplan_starttime'] = $val['lessonplan_starttime'];
                $data['lessonplan_endtime'] = $val['lessonplan_endtime'];
                $data['lessonplan_createtime'] = time();
                $this->DataControl->insertData("kmc_hobbycourse_lessonplan", $data);
            }
        }
        return true;
    }


    //班外课时列表
    function getClassScheduleList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['outclasstype_id']) && $paramArray['outclasstype_id'] !== "") {
            $datawhere .= " and h.outclasstype_id ={$paramArray['outclasstype_id']}";
        }
        if (isset($paramArray['outclasstype_code']) && $paramArray['outclasstype_code'] !== "") {
            $datawhere .= " and t.outclasstype_code ={$paramArray['outclasstype_code']}";
        }
        if (isset($paramArray['start_day']) && $paramArray['start_day'] !== "") {
            $datawhere .= " and h.hour_day >= '{$paramArray['start_day']}'";
        }

        if (isset($paramArray['end_day']) && $paramArray['end_day'] !== "") {
            $datawhere .= " and h.hour_day <= '{$paramArray['end_day']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                h.hour_id,
                t.outclasstype_name,
                t.outclasstype_code,
                h.hour_day,
                h.hour_starttime,
                h.hour_endtime,
                h.hour_scname,
                h.hour_remk
            FROM
                smc_outclass_hour AS h
                LEFT JOIN smc_code_outclasstype AS t ON h.outclasstype_id = t.outclasstype_id
            WHERE
                {$datawhere} and h.staffer_id = '{$paramArray['worker_id']}' and h.hour_iscancel = '0'
                ORDER BY h.hour_day ASC
                LIMIT {$pagestart},{$num}";

        $StafferList = $this->DataControl->selectClear($sql);
        $outclasstype_code = $this->LgArraySwitch(array("0" => "教学时数", "1" => "其它时数"));

        if ($StafferList) {
            foreach ($StafferList as &$val) {
                $val['time'] = $val['hour_starttime'] . '-' . $val['hour_endtime'];
                if (!$val['hour_remk']) {
                    $val['hour_remk'] = '--';
                }
                $val['outclasstype_code'] = $outclasstype_code[$val['outclasstype_code']];
                if ($this->DataControl->getOne("smc_outclass_hourcheck", "hour_id = '{$val['hour_id']}'")) {
                    $val['is_cancel'] = '0';
                } else {
                    $val['is_cancel'] = '1';
                }
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(h.hour_id)
            FROM
               smc_outclass_hour AS h
               LEFT JOIN smc_code_outclasstype AS t ON h.outclasstype_id = t.outclasstype_id
           WHERE
                {$datawhere} and h.staffer_id = '{$paramArray['worker_id']}' and h.hour_iscancel = '0'");
        $allnums = $all_num[0][0];


        $fieldstring = array('outclasstype_name', 'outclasstype_code', 'hour_day', 'time', 'hour_scname', 'hour_remk');
        $fieldname = array('课时类型', '课时分类', '课时日期', '课时时间', '所在校点', '备注');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($StafferList) {
            $result['list'] = $StafferList;
        } else {
            $result['list'] = array();
        }

        if (!$StafferList) {
            $res = array('error' => '1', 'errortip' => "暂无班外课时列表", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }

        return $res;
    }


    //取消班外课时
    function delClassScheduleAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_outclass_hour", "hour_id", "hour_id = '{$paramArray['hour_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['hour_iscancel'] = '1';
            $data['hour_updatetime'] = time();
            if ($this->DataControl->updateData("smc_outclass_hour", "hour_id = '{$paramArray['hour_id']}'", $data)) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "取消班外课时成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '取消班外课时失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //首页统计的数量
    function getIndexCountAction($paramArray)
    {
        //新生
        $newstudentnum = $this->DataControl->selectOne("");
        //待入班
        $bringinclassnum = $this->DataControl->selectOne("");
        //欠费
        $arrearsnum = $this->DataControl->selectOne("");
        //流失
        $losstnum = $this->DataControl->selectOne("");
        //拆并班
        $joinclasstnum = $this->DataControl->selectOne("");
        //新开班
        $offerclasstnum = $this->DataControl->selectOne("");
        //待升班
        $promotiontnum = $this->DataControl->selectOne("");
        //结业
        $graduationtnum = $this->DataControl->selectOne("");
    }

    function logoutStu($request)
    {
        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $this->error = true;
            $this->errortip = "学生ID参数必须传";
            return false;
        }

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_logoutday", "company_id='{$request['company_id']}'");

        if (!$companyOne) {
            $this->error = true;
            $this->errortip = "无该集团";
            return false;
        }

        $logOne = $this->DataControl->getFieldOne("smc_student_outlog", "outlog_id", "student_id='{$request['student_id']}'");
        if ($logOne) {
            $this->error = true;
            $this->errortip = "学员已注销,不可重复操作";
            return false;
        }

        if (!isset($companyOne['company_logoutday']) || $companyOne['company_logoutday'] <= 0) {
            $this->error = true;
            $this->errortip = "暂未设置可注销时间，暂不可注销哦~";
            return false;
        }

        $enrolledOne = $this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "student_id='{$request['student_id']}' and enrolled_status in ('0','1','3')");

        if ($enrolledOne) {
            $this->error = true;
            $this->errortip = "该学员在其他学校就读无法注销学籍";
            return false;
        }

        $addtime = $companyOne['company_logoutday'] * 3600 * 24;

        $sql = "select student_id from smc_student_enrolled where student_id='{$request['student_id']}' and (enrolled_leavetime+{$addtime}>unix_timestamp(now()))";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "此学员未达到集团设定的注销学籍时间,暂不可注销！";
            return false;
        }

        $familyList = $this->DataControl->getList("smc_student_family", "student_id='{$request['student_id']}'");

        if (!$familyList) {
            $this->error = true;
            $this->errortip = "无绑定家长信息，无需注销学籍";
            return false;
        }

        $studentOne=$this->DataControl->getFieldOne("smc_student","from_client_id","student_id='{$request['student_id']}'");

        if($this->DataControl->getFieldOne("crm_student_principal","principal_id","student_id='{$request['student_id']}' and principal_leave=0")){
            $this->error = true;
            $this->errortip = "当前有老师正在跟踪，取消跟踪后才可注销成功";
            return false;
        }

        foreach ($familyList as $familyOne) {
            if (isset($familyOne['parenter_id']) && $familyOne['parenter_id'] > 0) {
                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['staffer_id'] = $this->stafferOne['staffer_id'];
                $data['student_id'] = $request['student_id'];
                $data['parenter_id'] = $familyOne['parenter_id'];
                $data['outlog_createtime'] = time();
                $this->DataControl->insertData("smc_student_outlog", $data);
            }
        }

        $this->DataControl->delData("smc_student_family", "student_id='{$request['student_id']}'");

        if($studentOne['from_client_id']>0){
            if($this->DataControl->getFieldOne("crm_client","client_id","client_id='{$studentOne['from_client_id']}'")){
                $data=array();
                $data['client_mobile']='';
                $data['client_updatetime']=time();
                $this->DataControl->updateData("crm_client","client_id='{$studentOne['from_client_id']}'",$data);

            }
        }


        return true;
    }

}
