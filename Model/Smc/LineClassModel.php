<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/6/22
 * Time: 18:09
 */

namespace Model\Smc;


class LineClassModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
    }

    function getLineThreenumber($hour_id)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_day,hour_starttime,hour_endtime", "hour_id='{$hour_id}'");
        $lineroomsOne = $this->DataControl->getOne("smc_linerooms", "hour_id='{$hourOne['hour_id']}'");
        if ($lineroomsOne['linerooms_threenumber'] == '') {
            $linerooms = $this->CreateLineThreenumber($lineroomsOne['linerooms_id']);
            return $linerooms;
        } else {
            $hournumOne = array();
            $hournumOne['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
            $hournumOne['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
            if ($hournumOne['linerooms_endtime'] < time()) {
                $linerooms = array();
                $linerooms['threenumber'] = $lineroomsOne['linerooms_threenumber'];
                $linerooms['chairmanpwd'] = $lineroomsOne['linerooms_chairmanpwd'];
                $linerooms['confuserpwd'] = $lineroomsOne['linerooms_confuserpwd'];
                return $linerooms;
            } elseif ($hournumOne['linerooms_starttime'] > time() + 3600 * 24 * 7) {
                $linerooms = array();
                $linerooms['threenumber'] = '';
                $linerooms['chairmanpwd'] = '';
                $linerooms['confuserpwd'] = '';
                return $linerooms;
            } else {
                if ($hournumOne['linerooms_starttime'] !== $lineroomsOne['linerooms_starttime']
                    || $hournumOne['linerooms_endtime'] !== $lineroomsOne['linerooms_endtime']) {
                    $linerooms = $this->EditLineThreenumber($lineroomsOne['linerooms_id']);
                    return $linerooms;
                } else {
                    $linerooms = array();
                    $linerooms['threenumber'] = $lineroomsOne['linerooms_threenumber'];
                    $linerooms['chairmanpwd'] = $lineroomsOne['linerooms_chairmanpwd'];
                    $linerooms['confuserpwd'] = $lineroomsOne['linerooms_confuserpwd'];
                    return $linerooms;
                }
            }
        }
    }

    /**
     * 创建第三方的教室
     * author: ling
     * 对应接口文档 0001
     * @param $linerooms_id
     * @return array|bool
     */
    function CreateLineThreenumber($linerooms_id)
    {
        $lineroomsOne = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$linerooms_id}'");
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_code", "company_id='{$lineroomsOne['company_id']}'");
        $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$lineroomsOne['hour_id']}'");
        if ($lineroomsOne['linerooms_threenumber'] !== '') {
            $this->error = 1;
            $this->errortip = "请勿重复生成";
            return false;
        }
        $data_hour_num = array();
        $data_hour_num['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
        $data_hour_num['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
        if ($this->DataControl->updateData("smc_linerooms", "linerooms_id='{$lineroomsOne['linerooms_id']}'", $data_hour_num)) {
            $lineroomsOne = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$linerooms_id}'");
        }
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
        $putArray = array();
        $putArray['roomname'] = $lineroomsOne['linerooms_name'] ? $lineroomsOne['linerooms_name'] : $hourOne['hour_name'];
        $putArray['roomtype'] = '3';
        $putArray['fromclass'] = '0';
        $putArray['starttime'] = strtotime(date("Ymd", $lineroomsOne['linerooms_starttime']));
        $putArray['endtime'] = strtotime(date("Ymd", $lineroomsOne['linerooms_endtime'])) + 3600 * 24 - 1;

        if ($companyOne['company_code'] == 'kctw') {
            $putArray['chairmanpwd'] = 't22185996';
            $putArray['assistantpwd'] = 'z22185996';
            $putArray['patrolpwd'] = ' 22185996';
            $putArray['passwordrequired'] = 0;
        } else {
            $putArray['chairmanpwd'] = rand(11111, 999999);
            $putArray['assistantpwd'] = rand(11111, 999999);
            $putArray['patrolpwd'] = rand(11111, 999999);
            $putArray['passwordrequired'] = 1;
        }

        $putArray['confuserpwd'] = rand(11111, 999999);
        $putArray['autoopenav'] = '1';
        $putArray['maxvideo'] = '13';
        $putArray['sharedesk'] = '1';
        $putArray['sidelineuserpwd'] = rand(11111, 999999);

        $modelArray = $TalkcloudModel->exroomCreate($putArray);
        if ($modelArray['result'] == '0') {
            // 匹配课件
            $wordFiledId = $this->DataControl->selectClear("
              select  DISTINCT cd.line_fileid
             from eas_coursepackage_lessonword as cd    
             left join eas_coursepackage_lesson as cn On cn.lesson_id = cd.lesson_id and cn.package_id = cd.package_id          
             left join eas_coursepackage_apply as cy ON cy.package_id= cn.package_id
             left join smc_class as c ON cy.course_id =c.course_id
             left join smc_class_hour as ch On ch.class_id =c.class_id and cn.lesson_sort = ch.hour_lessontimes
             where c.class_id='{$lineroomsOne['class_id']}' and ch.hour_id = '{$lineroomsOne['hour_id']}' and cn.is_synchro = 1");
            if (is_array($wordFiledId)) {
                $array_field = array_column($wordFiledId, 'line_fileid');
                $file_putArray = array();
                $file_putArray['serial'] = $modelArray['serial'];
                $file_putArray['fileidarr'] = $array_field;
                $TalkcloudModel->ex_roombindfile($file_putArray);
            }
            $data = array();
            $data['linerooms_threenumber'] = $modelArray['serial'];
            $data['linerooms_fromclass'] = '0';
            $data['linerooms_chairmanpwd'] = $putArray['chairmanpwd'];
            $data['linerooms_type'] = '3';
            $data['linerooms_assistantpwd'] = $putArray['assistantpwd'];
            $data['linerooms_patrolpwd'] = $putArray['patrolpwd'];
            $data['linerooms_passwordrequired'] = 1;
            $data['linerooms_confuserpwd'] = $putArray['confuserpwd'];
            $data['linerooms_autoopenav'] = '1';
            $data['linerooms_maxvideo'] = '13';
            $data['linerooms_sharedesk'] = '1';
            $data['linerooms_updatatime'] = time();
            $data['linerooms_issync'] = "1";
            if ($this->DataControl->updateData("smc_linerooms", "linerooms_id = '{$linerooms_id}'", $data)) {
                $this->oktiptip = "课时网课信息创建成功!";
                $linerooms = array();
                $linerooms['threenumber'] = $modelArray['serial'];
                $linerooms['chairmanpwd'] = $putArray['chairmanpwd'];
                $linerooms['confuserpwd'] = $putArray['confuserpwd'];
                return $linerooms;
            } else {
                $this->error = true;
                $this->errortip = "课时网课信息创建教室失败，数据库创建失败!";
                return false;
            }
        } else {
            $errortipOne = $this->DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            $this->error = true;
            $this->errortip = "网课教室创建失败，错误码：{$errortipOne['errortip_txt']}!";
            return false;
        }
    }


    /**
     * 编辑第三方的教室
     * author: ling
     * 对应接口文档 0001
     * @param $linerooms_id
     * @return array|bool
     */
    function EditLineThreenumber($linerooms_id)
    {
        $lineroomsOne = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$linerooms_id}'");
        $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$lineroomsOne['hour_id']}'");
        if ($lineroomsOne['linerooms_threenumber'] == '') {
            $this->error = 1;
            $this->errortip = "未生成教室号";
            return false;
        }
        $data_hour_num = array();
        $data_hour_num['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
        $data_hour_num['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
        if ($this->DataControl->updateData("smc_linerooms", "linerooms_id='{$lineroomsOne['linerooms_id']}'", $data_hour_num)) {
            $lineroomsOne['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
            $lineroomsOne['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
        }

        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
        $putArray = array();
        $putArray['serial'] = $lineroomsOne['linerooms_threenumber'];
        $putArray['roomname'] = $lineroomsOne['linerooms_name'] ? $lineroomsOne['linerooms_name'] : $hourOne['hour_name'];
        $putArray['starttime'] = strtotime(date("Ymd", $lineroomsOne['linerooms_starttime']));
        $putArray['endtime'] = strtotime(date("Ymd", $lineroomsOne['linerooms_endtime'])) + 3600 * 24 - 1;
        $putArray['fromclass'] = '0';
        $putArray['chairmanpwd'] = $lineroomsOne['linerooms_chairmanpwd'];
        $putArray['assistantpwd'] = $lineroomsOne['linerooms_assistantpwd'];
        $putArray['patrolpwd'] = $lineroomsOne['linerooms_patrolpwd'];
        $putArray['passwordrequired'] = 1;
        $putArray['confuserpwd'] = $lineroomsOne['linerooms_confuserpwd'];
        $putArray['sidelineuserpwd'] = rand(11111, 999999);
        $putArray['autoopenav'] = '1';
        $putArray['maxvideo'] = '6';
        $putArray['sharedesk'] = '1';
        $modelArray = $TalkcloudModel->exroomModify($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['linerooms_updatatime'] = time();
            $data['linerooms_issync'] = "1";
            if ($this->DataControl->updateData("smc_linerooms", "linerooms_id = '{$linerooms_id}'", $data)) {
                $this->oktiptip = "课时网课信息更新成功!";
                $linerooms = array();
                $linerooms['threenumber'] = $modelArray['serial'];
                $linerooms['chairmanpwd'] = $putArray['chairmanpwd'];
                $linerooms['confuserpwd'] = $putArray['confuserpwd'];
                return $linerooms;
            } else {
                $this->error = true;
                $this->errortip = "课时网课信息修改教室失败，数据库修改失败!";
                return false;
            }
        } else {
            $errortipOne = $this->DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            $this->error = true;
            $this->errortip = "网课教室修改失败，错误码：{$errortipOne['errortip_txt']}!";
            return false;
        }
    }

    /**
     * 更新第三方教室上课时间
     * author: ling
     * 对应接口文档 0001
     * @param $linerooms_id
     * @return array|bool
     */
    function EditLinTimes($linerooms_id)
    {
        $lineroomsOne = $this->DataControl->getFieldOne("smc_linerooms","linerooms_threenumber,linerooms_name,linerooms_starttime,linerooms_endtime"
            , "linerooms_id='{$linerooms_id}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
        $putArray = array();
        $putArray['serial'] = $lineroomsOne['linerooms_threenumber'];
        $putArray['roomname'] = $lineroomsOne['linerooms_name'] ;
        $putArray['starttime'] = strtotime(date("Ymd", $lineroomsOne['linerooms_starttime']));
        $putArray['endtime'] = strtotime(date("Ymd", $lineroomsOne['linerooms_endtime'])) + 3600 * 24 - 1;
        $putArray['fromclass'] = '0';
        $putArray['sharedesk'] = '1';
        $modelArray = $TalkcloudModel->exroomTimesModify($putArray);
        if ($modelArray['result'] == '0') {
            $data = array();
            $data['linerooms_updatatime'] = time();
            $data['linerooms_issync'] = "1";
            if ($this->DataControl->updateData("smc_linerooms", "linerooms_id = '{$linerooms_id}'", $data)) {
                $this->oktiptip = "课时网课时间信息更新成功!";
                $linerooms = array();
                $linerooms['threenumber'] = $modelArray['serial'];
                $linerooms['chairmanpwd'] = $putArray['chairmanpwd'];
                $linerooms['confuserpwd'] = $putArray['confuserpwd'];
                return $linerooms;
            } else {
                $this->error = true;
                $this->errortip = "课时网课时间信息修改教室失败，数据库修改失败!";
                return false;
            }
        } else {
            $errortipOne = $this->DataControl->getOne("tkl_errortip", "errortip_code='{$modelArray['result']}'");
            $this->error = true;
            $this->errortip = "网课教室时间修改失败，错误码：{$errortipOne['errortip_txt']}!";
            return false;
        }
    }

    //获第三方的上课视屏记录
    function GetLineThreeRecord($linerooms_id, $starttime, $endtime)
    {
        $lineroomsOne = $this->DataControl->getOne("smc_linerooms", "linerooms_id='{$linerooms_id}'");
        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($lineroomsOne['company_id']);
        $postdata['serial'] = $lineroomsOne['linerooms_threenumber'];
        $postdata['starttime'] = $starttime;
        $postdata['endtime'] = $endtime;
        $postdata['fromclass'] = $lineroomsOne['linerooms_fromclass'];
        $modelArray = $TalkcloudModel->exroomonGetvideolist($postdata);

        if ($modelArray['result'] == '0') {
            if ($lineroomsOne['linerooms_fromclass'] == '0') {
                $recordList = $modelArray['recordlist'];
            } else {
                $recordList = $modelArray['list'];
            }
            if ($recordList) {
                foreach ($recordList as &$roomOne) {
                    if ($lineroomsOne['linerooms_fromclass'] == '0') {
                        $roomOne['playurl'] = $roomOne['https_playpath_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    } else {
                        $roomOne['playpath'] = $roomOne['videoplayback_mp4'];
                        $roomOne['duration'] = round(($roomOne['duration']) / 60, 2);
                    }
                }
            }
        } elseif ($modelArray['result'] == '-1') {
            $recordList = array();
        }
        return $recordList;
    }


}