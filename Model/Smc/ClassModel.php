<?php


namespace Model\Smc;

class ClassModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $schoolOne = array();//操作校区
    public $publicarray = array();
    public $stafferOne = array();
    public $classArray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->verdictCompany($publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->verdictSchool($publicarray['school_id']);
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证校园信息
    function verdictSchool($school_id)
    {
        $this->schoolOne = $this->DataControl->getFieldOne("smc_school",
            "school_id,company_id,school_branch,school_shortname,school_cnname,school_openclass,school_isclose,school_istemporaryclose,school_temporaryclosetip"
            , "school_id = '{$school_id}'");
        if (!$this->schoolOne) {
            $this->error = true;
            $this->errortip = "校园信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,account_class,staffer_ismanage", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function classList($request)
    {
        //缺少上课时间
        $contractOne = $this->getContract($this->companyOne['company_id']);

        $datawhere = "c.class_status <> '-2'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id = '{$request['coursetype_id']}'";
        }
        if (isset($request['class_type']) && $request['class_type'] !== '') {
            if ($request['class_type'] == '2') {
                $datawhere .= " and cc.coursetype_isopenclass = '1'";
            } else {
                $datawhere .= " and c.class_type = '{$request['class_type']}'";
            }
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id = '{$request['coursecat_id']}'";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }
        if (isset($request['class_isprepare']) && $request['class_isprepare'] !== '') {
            $datawhere .= " and c.class_isprepare = '{$request['class_isprepare']}'";
        }

        if (isset($request['teacher_id']) && $request['teacher_id'] !== '') {
            if ($contractOne && $contractOne['edition_id'] == '2') {
                $datawhere .= " and exists(select 1 from smc_class_teach as te left join smc_staffer as st on te.staffer_id=st.staffer_id where te.class_id=c.class_id and te.staffer_id='{$request['teacher_id']}' and te.teach_status=0)";
            } else {
                $datawhere .= " and exists(select 1 from smc_class_hour_teaching as te left join smc_staffer as st on te.staffer_id=st.staffer_id where te.class_id=c.class_id and te.staffer_id='{$request['teacher_id']}' and te.teaching_isdel=0)";
            }
        }

        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and sc.coursecat_id in(133,141,11352) ";
        }

        if (isset($request['class_status']) && $request['class_status'] !== '') {
            $datawhere .= " and c.class_status = '{$request['class_status']}'";
        }

        if (isset($request['class_isfictitious']) && $request['class_isfictitious'] !== '') {
            $datawhere .= " and c.class_isfictitious = '{$request['class_isfictitious']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "SELECT c.class_id,c.course_id,c.class_cnname,c.class_branch,c.class_enname,c.class_timestr,sc.course_cnname,sc.course_branch,c.class_status,c.class_stdate,c.class_fullnums,sc.course_presentednums,cc.coursetype_isopenclass,course_inclasstype,c.class_enddate,c.class_type,c.father_id as p_class_id,sc.course_isopensonclass,sc.course_classnum,sc.course_nextid,sc.course_limitnum,c.class_isprepare
                ,sc.course_schedule,(select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id and ss.study_isreading='1' limit 0,1) as num
                ,(SELECT count(DISTINCT(b.student_id)) as aa FROM smc_class_hour as a ,smc_student_hourstudy as b WHERE a.class_id = c.class_id and b.class_id = a.class_id and a.hour_id = b.hour_id GROUP BY a.hour_id ORDER BY a.hour_lessontimes DESC limit 0,1 ) as endnum
                ,(select 1 from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as agohavestu  
                ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1 and ssch.hour_iswarming = 0 ) as hournum
                ,(select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id and hour_ischecking <> -1 and hou.hour_isfree=0) as is_schedule,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0 and ssch.hour_isfree=0) as no_hournum,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking <> '-1') as is_hour,
                ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=1 limit 0,1),0) as breakoff_id
                ,ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=0 limit 0,1),0) as hasbreakoff
                ,ifnull((select cl.class_id from smc_class as cl where cl.from_class_id=c.class_id limit 0,1),0) as hasupgrade 
                ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_hour_teaching as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teaching_isdel=0) as teacherstr
                ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_teach as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teach_status=0) as jteacherstr
                ,ifnull((select 1 from smc_student_hourstudy as x,smc_class_hour as y where x.hour_id=y.hour_id and x.class_id=c.class_id and y.hour_isfree=0 limit 0,1),0) as is_atte
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
                WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype =0 and c.class_status>=-1 
                group by c.class_id
                order by c.class_stdate DESC limit {$pagestart},{$num}";

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $rel_classList = array();
        foreach ($classList as $classOne) {
            $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
FROM smc_class_lessonplan AS l
LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
WHERE l.class_id = '{$classOne['class_id']}' ");
            $cnteacher = array();
            $classroom = array();
            $classtimestr = "";
            if ($contractOne && $contractOne['edition_id'] == '2') {
                $cnteacher = $classOne['jteacherstr'];
            } else {
                if ($classlessonList) {
                    foreach ($classlessonList as $classlessonOne) {

                        $cnteacher[] = $classlessonOne['staffer_enname'] ? $classlessonOne['staffer_cnname'] . '-' . $classlessonOne['staffer_enname'] : $classlessonOne['staffer_cnname'];
                        $classroom[] = $classlessonOne['classroom_cnname'];
                        $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                    }

                    $cnteacher = implode("<br>", array_unique($cnteacher));
                    $classroom = implode("<br>", array_unique($classroom));
                }
            }

            //班级人像采集二维码
            $nowtime = ceil(microtime(true) * 1000);
            $urlparam = base64_encode("class_branch={$classOne['class_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}&opentime={$nowtime}");
            if ($_SERVER['SERVER_NAME'] != 'smcapi.kedingdang.com' && $_SERVER['SERVER_NAME'] != 'gmcapi.kedingdang.com') {
                $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
            } else {
                $portraiturl = "https://faceentry.kedingdang.com/?" . $urlparam;
            }
            $portraitclassqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));

            //周四 10:00—10:50,周五 15:00—15:50
            $data = array();
            $data['class_id'] = $classOne['class_id'];
            $data['is_atte'] = $classOne['is_atte'];
            $data['coursetype_isopenclass'] = $classOne['coursetype_isopenclass'];
            $data['course_id'] = $classOne['course_id'];
            $data['course_nextid'] = $classOne['course_nextid'];
            $data['class_status'] = $classOne['class_status'];
            $data['class_type'] = $classOne['class_type'];
            $data['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
            $data['is_schedule'] = $classOne['is_schedule'];
            $data['class_cnname'] = $classOne['class_cnname'];
            $data['p_class_id'] = $classOne['p_class_id'];
            $data['class_enname'] = $classOne['class_enname'];
            $data['class_branch'] = $classOne['class_branch'];
            if ($classOne['class_status'] == '-1') {
                $data['number'] = $classOne['endnum'] . '/' . $classOne['class_fullnums'];
            } else {
                $data['number'] = $classOne['num'] . '/' . $classOne['class_fullnums'];
            }
            $data['class_fullnums'] = $classOne['class_fullnums'];
            $data['course_limitnum'] = $classOne['course_limitnum'];
            $data['hasNum'] = $classOne['num'];
            $data['course_cnname'] = $classOne['course_cnname'];
            $data['course_branch'] = $classOne['course_branch'];
            $data['course_inclasstype'] = $classOne['course_inclasstype'];
            $data['class_enddate'] = $classOne['class_enddate'];
            $data['course_isopensonclass'] = $classOne['course_isopensonclass'];
            $data['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
            $data['cnteacher'] = $cnteacher ? $cnteacher : $classOne['teacherstr'];

            $data['class_isprepare'] = $classOne['class_isprepare'];
            $data['portraitclassqrcode'] = $portraitclassqrcode;
            $data['class_timestr'] = $classtimestr;
            if ($classroom) {
                $data['classroom_cnname'] = $classroom;
            } else {
                $data['classroom_cnname'] = '';
            }
            if ($classOne['hournum'] > 0) {
                if ($classOne['is_schedule']) {
                    $data['info'] = $classOne['hournum'] . '/' . $classOne['is_schedule'];
                } else {
                    $data['info'] = $classOne['hournum'] . '/' . ($classOne['course_classnum'] + $classOne['course_presentednums']);
                }

            } else {
                $data['info'] = '0/' . $classOne['is_schedule'];
            }

            //是否可以升班
            if ($contractOne && $contractOne['edition_id'] == '2') {
                if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                    $data['can_promotion'] = 1;
                } else {
                    $data['can_promotion'] = 0;
                }
            } else {
                if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                    $data['can_promotion'] = 1;
                } else {
                    $data['can_promotion'] = 0;
                }
            }

            //是否可以结班
            if ($contractOne && $contractOne['edition_id'] == '2') {
                if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0) {
                    $data['can_endclass'] = 1;   //是否可以入班
                } else {
                    $data['can_endclass'] = 0;
                }
            } else {
                if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0) {
                    $data['can_endclass'] = 1;
                } else {
                    $data['can_endclass'] = 0;
                }
            }

            if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                $data['is_schedule'] = 1;
            }

            if ($classOne['is_schedule'] > 0 && $classOne['no_hournum'] > 0 && $classOne['course_schedule'] == 0) {
                $data['edit_schedule'] = 1;  //可以修改排课
            } else {
                $data['edit_schedule'] = 0;
            }

            if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                $data['add_schedule'] = 1;  //是否增加排课
            } else {
                $data['add_schedule'] = 0;
            }

            if ($classOne['breakoff_id'] > 0 && $classOne['class_status'] == '1' && $classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1) {
                $data['can_break'] = 1;
            } else {
                $data['can_break'] = 0;
            }

            if ($classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1 && $classOne['breakoff_id'] == 0) {
                $sql = "select hourstudy_id from smc_student_hourstudy where class_id='{$classOne['class_id']}' limit 0,1";
                if ($this->DataControl->selectOne($sql)) {
                    $data['can_applybreak'] = 1;
                } else {
                    $data['can_applybreak'] = 0;
                }
            } else {
                $data['can_applybreak'] = 0;
            }

            $classStatus = $this->LgArraySwitch(array("0" => "待开班", '1' => "进行中", "-1" => "已结束", "-2" => "已删除"));
            $data['class_staus_namme'] = $classStatus[$classOne['class_status']];

            $data['agohavestu'] = $classOne['agohavestu']?$classOne['agohavestu']:'0';//以前是否有学生入过班

            if($classOne['class_isprepare'] == '1'){
                $data['class_isprepare_name'] = '是';

                $data['class_timestr'] = '';
                $data['classroom_cnname'] = '';
                $data['info'] = '--';
                $data['class_stdate'] = '';
                $data['class_staus_namme'] = '进行中';
            }else{
                $data['class_isprepare_name'] = '否';
            }

            $rel_classList[] = $data;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "SELECT c.class_id
                FROM smc_class as c
                left join smc_course as sc on sc.course_id = c.course_id
                left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
                WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype = '0' and c.class_status>=-1
                group by c.class_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $rel_classList;

        return $data;
    }

    function coursetermClass($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }
        if (isset($request['class_status']) && $request['class_status'] !== '') {
            $datawhere .= " and c.class_status = '{$request['class_status']}'";
        }

        if (isset($request['class_type']) && $request['class_type'] !== '') {
            $datawhere .= " and c.class_type = '{$request['class_type']}'";
        }

        if (isset($request['class_isfictitious']) && $request['class_isfictitious'] !== '') {
            $datawhere .= " and c.class_isfictitious = '{$request['class_isfictitious']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//		 (select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as num,

        $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,sc.course_cnname,sc.course_branch,c.class_status,sc.course_id,sc.course_classnum,sc.course_schedule,course_inclasstype,c.class_status,c.class_fullnums,sc.course_limitnum,c.class_type,sc.course_isopensonclass,
 			(select count(cb.booking_id) from smc_class_booking as cb where cb.class_id=c.class_id and  cb.booking_status =0) as num,
            group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ))) as cnteacher,
            (select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id and ss.study_isreading='1' limit 0,1) as stuNum,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1) as hournum,
            (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id ) as is_schedule,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0) as no_hournum,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking<>'-1' and hour_iswarming = 0 ) as is_hour_num,
            (select count(d.study_id) from smc_student_study as d where d.class_id=c.class_id and study_isreading=1 ) as study_num
            ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_hour_teaching as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teaching_isdel=0) as sonTeacher
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                left join smc_class_hour as sch on sch.class_id=c.class_id
                WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype ='2' and c.class_status<>'-2'
                group by c.class_id
                order by DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                limit {$pagestart},{$num}";


        //join smc_code_courseterm as ccm on ccm.courseterm_id=sc.courseterm_id
        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $rel_classList = array();
        $arr_class_status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));
        foreach ($classList as $classOne) {
            //班级人像采集二维码
            $urlparam = base64_encode("class_branch={$classOne['class_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
            $portraiturl = "http://faceentry.kcclassin.com/?{$urlparam}";
            $portraitclassqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));

            $data = array();
            $data['class_id'] = $classOne['class_id'];
            $data['course_id'] = $classOne['course_id'];
            $data['class_status'] = $classOne['class_status'];
            $data['class_status_name'] = $arr_class_status[$classOne['class_status']];
            $data['is_schedule'] = $classOne['is_schedule'];
            $data['class_fullnums'] = $classOne['class_fullnums'];
            $data['course_limitnum'] = $classOne['course_limitnum'];
            $data['hasNum'] = $classOne['stuNum'];
            $data['class_cnname'] = $classOne['class_cnname'];
            $data['class_enname'] = $classOne['class_enname'];
            $data['class_branch'] = $classOne['class_branch'];
            $data['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
            $data['number'] = $classOne['stuNum'] . '/' . $classOne['class_fullnums'];
            $data['num'] = $classOne['num'];
            $data['course_cnname'] = $classOne['course_cnname'];
            $data['course_branch'] = $classOne['course_branch'];
            $data['cnteacher'] = $classOne['class_type'] == 1 ? $classOne['sonTeacher'] : $classOne['cnteacher'];
            $data['course_inclasstype'] = $classOne['course_inclasstype'];
            $data['is_hour_num'] = intval($classOne['is_hour_num']);
            $data['class_type'] = intval($classOne['class_type']);
            $data['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
            $data['course_isopensonclass'] = $classOne['course_isopensonclass'];
            $data['portraitclassqrcode'] = $portraitclassqrcode;
            if ($classOne['is_schedule'] > 0) {
                $data['info'] = $classOne['hournum'] . '/' . $classOne['is_hour_num'];
                if ($classOne['is_hour_num'] > 0) {

                    $data['percentageNum'] = ($classOne['hournum'] / $classOne['is_hour_num']) * 100;
                } else {
                    $data['percentageNum'] = 0;
                }
            } else {
                $data['info'] = '0/0';
                $data['percentageNum'] = 0;
            }
            if ($classOne['course_classnum'] >= $classOne['study_num']) {
                $data['can_promotion'] = 1;  //是否可以入班
            } else {
                $data['can_promotion'] = 0;
            }
            if ($classOne['is_schedule'] > 0 and $classOne['no_hournum'] > 0 and $classOne['course_schedule'] == 0) {
                $data['edit_schedule'] = 1;  //可以修改排课
            } else {
                $data['edit_schedule'] = 0;
            }
            $rel_classList[] = $data;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "SELECT c.class_id
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                left join smc_class_hour as sch on sch.class_id=c.class_id
                WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype = '2' and c.class_status<>'-2'
                group by c.class_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $rel_classList;

        return $data;

    }

    function mothClass($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['class_type']) && $request['class_type'] !== '') {
            $datawhere .= " and c.class_type = '{$request['class_type']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }
        if (isset($request['class_status']) && $request['class_status'] !== '') {
            $datawhere .= " and c.class_status = '{$request['class_status']}'";
        }
        if (isset($request['class_isfictitious']) && $request['class_isfictitious'] !== '') {
            $datawhere .= " and c.class_isfictitious = '{$request['class_isfictitious']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//		 (select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as num,

        $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,sc.course_cnname,sc.course_branch,c.class_status,sc.course_id,sc.course_classnum,sc.course_schedule,course_inclasstype,c.class_fullnums,sc.course_limitnum,sc.course_schedule,c.class_type,c.father_id,
            group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher
            ,course_isopensonclass,c.class_type,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1) as hournum,
            (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id ) as is_schedule,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0) as no_hournum,
            (select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as num,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_isfree='0' and sch.hour_ischecking<>'-1') as hour_num,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_iswarming = '0' and sch.hour_ischecking = '0') as is_hour_num
                ,(select substring(sch.hour_day,1,7) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_iswarming = '0' order by sch.hour_day desc,sch.hour_starttime desc limit 0,1) as lastMonth
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype ='1' and  class_status <> '-2'
                group by c.class_id
                order by DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                limit {$pagestart},{$num}";


        //join smc_code_courseterm as ccm on ccm.courseterm_id=sc.courseterm_id
        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $rel_classList = array();
        $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));

        foreach ($classList as $classOne) {
            //班级人像采集二维码
            $urlparam = base64_encode("class_branch={$classOne['class_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
            $portraiturl = "http://faceentry.kcclassin.com/?{$urlparam}";
            $portraitclassqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));

            $data = array();
            $data['class_id'] = $classOne['class_id'];
            $data['course_id'] = $classOne['course_id'];
            $data['class_status'] = $classOne['class_status'];
            $data['class_status_name'] = $status[$classOne['class_status']];
            $data['is_schedule'] = $classOne['is_schedule'];
            $data['class_cnname'] = $classOne['class_cnname'];
            $data['class_enname'] = $classOne['class_enname'];
            $data['class_branch'] = $classOne['class_branch'];
            $data['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
            $data['number'] = $classOne['num'] . '/' . $classOne['class_fullnums'];
            $data['class_fullnums'] = $classOne['class_fullnums'];
            $data['course_limitnum'] = $classOne['course_limitnum'];
            $data['hasNum'] = $classOne['num'];
            $data['course_cnname'] = $classOne['course_cnname'];
            $data['course_branch'] = $classOne['course_branch'];
            if ($classOne['class_type'] == 1) {
                $class_teach = $this->DataControl->selectOne("select   group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher  from smc_class_teach as th,smc_staffer as s where s.staffer_id=th.staffer_id and class_id='{$classOne['father_id']}' limit 0,1 ");
                $data['cnteacher'] = $class_teach['cnteacher'];
            } else {
                $data['cnteacher'] = $classOne['cnteacher'];
            }
            $data['course_isopensonclass'] = $classOne['course_isopensonclass'];
            $data['course_inclasstype'] = $classOne['course_inclasstype'];
            $data['class_type'] = $classOne['class_type'];
            $data['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
            if ($classOne['course_schedule'] == 1) {
                $data['is_hour_num'] = 0;
            } else {
                $data['is_hour_num'] = intval($classOne['is_hour_num']);   //大于0 可以修改排课
            }
            if ($classOne['is_schedule'] > 0) {
                $data['info'] = $classOne['hournum'] . '/' . $classOne['hour_num'];
                if ($classOne['hour_num'] > 0) {
                    $data['percentageNum'] = ($classOne['hournum'] / $classOne['hour_num']) * 100;
                } else {
                    $data['percentageNum'] = 0;
                }
            } else {
                $data['info'] = '0/0';
                $data['percentageNum'] = 0;
            }

            if ($classOne['hournum'] == $classOne['course_classnum'] || $classOne['num'] == 0) {
                $data['can_promotion'] = 1;
            } else {
                $data['can_promotion'] = 0;
            }

            $data['edit_schedule'] = 0;

            if ($classOne['is_hour_num'] == 0 && $classOne['class_status'] != '-1' and $classOne['lastMonth'] < date("Y-m")) {
                $data['can_endclass'] = 1;
            } else {
                $data['can_endclass'] = 0;
            }
            $data['portraitclassqrcode'] = $portraitclassqrcode;

            $rel_classList[] = $data;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "SELECT c.class_id
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype = 1 and  class_status <> '-2'
                group by c.class_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $rel_classList;

        return $data;

    }

    /**
     * 班级管理-公开课列表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/19 0019
     * @param $request
     */
    function getOpenClass($request)
    {
        $datawhere = " c.school_id='{$request['school_id']}' and c.class_status <> '-2' and ty.coursetype_isopenclass =1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['class_type']) && $request['class_type'] !== '') {
            $datawhere .= " and c.class_type = '{$request['class_type']}'";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }
        if (isset($request['class_status']) && $request['class_status'] !== '') {
            $datawhere .= " and c.class_status = '{$request['class_status']}'";
        }
        if (isset($request['teacher_id']) && $request['teacher_id'] !== '') {
            $datawhere .= " and s.staffer_id = '{$request['teacher_id']}'";
        }
        if (isset($request['course_openclasstype']) && $request['course_openclasstype'] !== '') {
            $datawhere .= " and sc.course_openclasstype = '{$request['course_openclasstype']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;
        $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,c.class_appointnum,sc.course_cnname,sc.course_branch,c.class_status,c.class_timestr,sc.course_id,sc.course_classnum,sc.course_schedule,sc.course_inclasstype,sc.course_openclasstype,c.class_fullnums,sc.course_limitnum,class_appointnum,sc.course_schedule,
            group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher
            ,course_isopensonclass,c.class_type,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1) as hournum,
            (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id ) as is_schedule,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0) as no_hournum,
            (select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as num,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking<>'-1') as hour_num,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking = '0') as is_hour_num,
            (select hour_id from smc_class_hour as hd where hd.class_id=c.class_id  and hd.hour_ischecking =1 limit 0,1 ) as is_hourstudy,
            (select audition_id from smc_class_hour_audition as au  where au.class_id= c.class_id  limit 0,1) as audition_id
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                left join smc_class_hour as sch on sch.class_id=c.class_id
                left join smc_code_coursetype as ty ON ty.coursetype_id=sc.coursetype_id
                WHERE {$datawhere}  
                group by c.class_id
                order by field(c.class_status,'-1') asc,DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                limit {$pagestart},{$num}";

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $rel_classList = array();
        $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));
        $status1 = $this->LgArraySwitch(array('0' => '普通公开课', '1' => '试读公开课'));
        foreach ($classList as &$classOne) {
            $classOne['class_appointnum'] = ($classOne['class_appointnum'] == '0') ? "不限" : $classOne['class_appointnum'];
            $classOne['course_openclasstype_name'] = $status1[$classOne['course_openclasstype']];

            $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
FROM smc_class_lessonplan AS l
LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
WHERE l.class_id = '{$classOne['class_id']}' ");
            $cnteacher = array();
            $classroom = array();
            $classtimestr = "";

            if ($classlessonList) {
                foreach ($classlessonList as $classlessonOne) {

                    $cnteacher[] = $classlessonOne['staffer_enname'] ? $classlessonOne['staffer_cnname'] . '-' . $classlessonOne['staffer_enname'] : $classlessonOne['staffer_cnname'];
                    $classroom[] = $classlessonOne['classroom_cnname'];
                    $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                }

                $cnteacher = implode(" ", array_unique($cnteacher));
                $classroom = implode(" ", array_unique($classroom));
            }

            if ($cnteacher) {
                $classOne['cnteacher'] = $cnteacher;
            } else {
                $classOne['cnteacher'] = '';
            }
//            $classOne['class_timestr'] = $classtimestr;

            if ($classroom) {
                $classOne['classroom_cnname'] = $classroom;
            } else {
                $classOne['classroom_cnname'] = '';
            }


            $classOne['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));

            $classOne['class_status_name'] = $status[$classOne['class_status']];
            $classOne['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
            $classOne['is_hour_num'] = intval($classOne['is_hour_num']);
            if ($classOne['is_schedule'] > 0) {
                $classOne['info'] = $classOne['hournum'] . '/' . $classOne['hour_num'];
                $classOne['percentageNum'] = $classOne['hour_num'] > 0 ? ($classOne['hournum'] / $classOne['hour_num']) * 100 : 0;
            } else {
                $classOne['info'] = '0/0';
                $classOne['percentageNum'] = 0;
            }
            $temp_schedule = $classOne['is_schedule'];
            $classOne['is_editclass'] = $classOne['class_status'] > -1 ? 1 : 0; //是否可以编辑班级
            $classOne['is_schedule'] = intval($temp_schedule) > 0 ? 0 : 1; // 是否可以排課
            if ($classOne['course_schedule'] == 1) {
                $classOne['edit_schedule'] = 0;
            } else {
                $classOne['edit_schedule'] = intval($temp_schedule) > 0 ? 1 : 0; // 是否可以编辑排課
            }

            if ($temp_schedule > 0 and $classOne['class_status'] >= 0) {
                $classOne['add_schedule'] = '1';
            } else {
                $classOne['add_schedule'] = '0';
            }

            if ($classOne['course_openclasstype'] == 1) {
                if ($classOne['class_status'] >= 0) {
                    $classOne['is_endclass'] = '1';
                } else {
                    $classOne['is_endclass'] = '0';
                }
            } else {
                if ($classOne['is_hourstudy'] > 0 && $classOne['is_hour_num'] == 0 && $classOne['class_status'] >= 0) {
                    $classOne['is_endclass'] = '1';
                } else {
                    $classOne['is_endclass'] = '0';
                }

            }


            $classOne['is_del'] = 1;  // 是否可以删除
            $rel_classList[] = $classOne;
        }

        $data = array();

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "SELECT c.class_id
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                left join smc_staffer as s on s.staffer_id=ct.staffer_id
                left join smc_class_hour as sch on sch.class_id=c.class_id
                left join smc_code_coursetype as ty ON ty.coursetype_id=sc.coursetype_id
                WHERE {$datawhere}
                group by c.class_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        $courseList = $this->DataControl->selectClear("SELECT sc.course_id,sc.course_branch,sc.course_cnname
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_code_coursetype as ty ON ty.coursetype_id=sc.coursetype_id
                where c.school_id='{$request['school_id']}' and c.class_status <> '-2' and ty.coursetype_isopenclass =1
                ");
        $data['list'] = $rel_classList;
        $data['course_list'] = is_array($courseList) ? $courseList : array();

        return $data;
    }

    function getClassMenu($request)
    {
        $sql = "select c.class_status,sc.course_nextid,ct.coursetype_isopenclass
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id='{$request['class_id']}' and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}' and ss.study_isreading='1') as study_num
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}') as all_hour_num
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='1') as has_hour_num
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0') as no_hour_num
              ,(select ch.hour_ischecking from smc_class_hour as ch where ch.class_id='{$request['class_id']}' order by ch.hour_lessontimes desc limit 0,1) as last_hour_status
              ,(select scl.changelog_id from smc_student_changelog as scl where scl.company_id='{$request['company_id']}' and scl.class_id='{$request['class_id']}' and scl.school_id='{$request['school_id']}') as change_num
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursetype as ct on ct.coursetype_id=sc.coursetype_id
              where c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}'";
        $classOne = $this->DataControl->selectOne($sql);
        $data = array();
        $data['can_classEntry'] = 1;
        $data['can_edit'] = 1;
        $data['can_class_payment'] = 1;
        if ($classOne['class_status'] >= 0 && $classOne['study_num'] > 0) {
            $data['can_dismantle_classes'] = 1;
        } else {
            $data['can_dismantle_classes'] = 0;
        }

        if ($classOne['change_num'] > 0 || $classOne['study_num'] > 0) {
            $data['can_del'] = 0;
        } else {
            $data['can_del'] = 1;
        }

        $audition = $this->DataControl->selectClear("select ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking from smc_class_hour_audition as cha left join smc_class_hour as ch on ch.hour_id=cha.hour_id where class_id='{$request['class_id']}'
                                                  ");

        if ($audition) {
            foreach ($audition as $val) {
                $now = date("Y-m-d H:i:s", time());
                $auditionTime = $val['hour_day'] . " " . $val['hour_endtime'];
                if ($val['hour_ischecking'] == 0 && ($now <= $auditionTime)) {
                    $data['can_del'] = 0;
                }
            }
        }

        if ($classOne['all_hour_num'] > 0) {
            $data['can_schedule'] = 0;
            $data['can_edit_schedule'] = 1;
        } else {
            $data['can_schedule'] = 1;
            $data['can_edit_schedule'] = 0;
        }

        if ($classOne['no_hour_num'] == 0 && $classOne['course_nextid'] != '') {
            $data['can_knot'] = 1;
            $data['can_promotion'] = 1;
        } else {
            $data['can_knot'] = 0;
            $data['can_promotion'] = 0;
        }

        if ($classOne['class_status'] >= 0 && $classOne['course_nextid'] != '') {
            $data['can_prefabrication'] = 1;
        } else {
            $data['can_prefabrication'] = 0;
        }

        if ($classOne['no_hour_num'] == 0 && $classOne['course_nextid'] == '') {
            $data['can_graduation'] = 1;
        } else {
            $data['can_graduation'] = 0;
        }

        if ($classOne['all_hour_num'] > 0 && ($classOne['coursetype_isopenclass'] == '1' || $classOne['course_inclasstype'] == '2')) {
            $data['can_add_schedule'] = 1;
        } else {
            $data['can_add_schedule'] = 0;
        }

        return $data;

    }

    function classOne($request)
    {

        
        $contractOne = $this->getContract($this->companyOne['company_id']);

        $sql = "SELECT  c.class_id,c.class_cnname,c.class_enname,c.class_branch,sc.course_cnname,sc.course_branch,c.class_stdate,c.class_enddate,sc.course_id,c.class_fullnums,ct.coursetype_isopenclass,sc.course_inclasstype,c.class_type,sc.course_isopensonclass,sc.course_checkingintype,c.father_id,sc.course_isrenew,sc.course_isrenewprice,course_checkingminday,course_minabsencenum,c.class_isfictitious,c.class_isupgrade,c.from_class_id,sc.coursetype_id,sc.course_opensonmode,sc.course_openclasstype,c.class_isprepare,c.class_hourwarmnums,c.class_hourwarmapplynums,c.class_hourreviewnums,c.class_hourreviewapplynum,sc.course_isopenwarm,sc.course_isopenreview,sc.course_canapplywarm,sc.course_canapplyreview,sc.course_islimitwarmtime,sc.course_warmtimerange,sc.course_islimitreviewtime,sc.course_reviewtimerange,sc.course_nextid,sc.course_limitnum,c.class_status
             ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking <> -1 and ssch.hour_iswarming =0 ) as course_classnum
               ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and  ssch.hour_isfree = 1 and ssch.hour_ischecking =1 ) as free_hournum,
               (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and  ssch.hour_isfree = 1 and ssch.hour_ischecking <> -1 ) as free_alrhournum,
            (select count(ss.study_id) from smc_student_study as ss where ss.class_id = c.class_id and study_isreading = 1 ) as study_num,
            (select count(cb.booking_id) from smc_class_booking as cb where cb.class_id = c.class_id and cb.booking_status = 0) as booking_num,
            (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1 and  ssch.hour_isfree=0  and ssch.hour_iswarming =0) as hournum,
            ifnull((select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking<>-1 and  ssch.hour_isfree=1  and ssch.hour_iswarming =1),0) as warmnum,
            ifnull((select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking<>-1 and  ssch.hour_isfree=1  and ssch.hour_iswarming =2),0) as reviewnum,
            ifnull(cl.class_cnname,'') as class_parent_cnname,
            ifnull(cl.class_branch,'') as class_parent_branch,
            ifnull(cl.class_id,0) as class_parent_classid,
            (select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) from smc_class_teach as t left join smc_staffer as s on s.staffer_id=t.staffer_id where (t.class_id=c.class_id or t.class_id = c.father_id) and t.teach_type=0 and t.teach_status =0 and s.staffer_leave=0) as cnteacher,
            (select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ) ) ) from smc_class_teach as te left join smc_staffer as st on st.staffer_id=te.staffer_id where (te.class_id=c.class_id or te.class_id = c.father_id) and te.teach_status =0 and te.teach_type=1 and st.staffer_leave=0) as enteacher,
            (select group_concat(distinct(ro.classroom_cnname)) from smc_class_hour as cl left join smc_classroom as ro on ro.classroom_id=cl.classroom_id where cl.class_id=c.class_id) as classroom_cnname
            ,ifnull((select 1 from smc_student_hourstudy as x,smc_class_hour as y where x.hour_id=y.hour_id and x.class_id=c.class_id and y.hour_isfree=0 limit 0,1),0) as is_atte
            ,if(sc.course_islimitopencycle=1,sc.course_limitopencyclenum,0) as course_limitopencyclenum,a.school_isskipFw
            ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking <> '-1') as is_hour
            ,(select 1 from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as agohavestu  
            ,ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=1 limit 0,1),0) as breakoff_id
            ,ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=0 limit 0,1),0) as hasbreakoff
            ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0 and ssch.hour_isfree=0) as no_hournum
            ,ifnull((select cl.class_id from smc_class as cl where cl.from_class_id=c.class_id limit 0,1),0) as hasupgrade 
            ,(select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id and ss.study_isreading='1' limit 0,1) as num
            ,(SELECT count(DISTINCT(b.student_id)) as aa FROM smc_class_hour as a ,smc_student_hourstudy as b WHERE a.class_id = c.class_id and b.class_id = a.class_id and a.hour_id = b.hour_id GROUP BY a.hour_id ORDER BY a.hour_lessontimes DESC limit 0,1 ) as endnum
            FROM smc_class as c
            left join smc_course as sc on sc.course_id=c.course_id
            left join smc_code_coursetype as ct on ct.coursetype_id = sc.coursetype_id
            left join smc_class as cl on c.father_id=cl.class_id
            left join smc_school as a on a.school_id=c.school_id
            WHERE c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}'
            order by c.class_createtime DESC";


        $classOne = $this->DataControl->selectOne($sql);

        if($classOne['school_isskipFw']==1 && $classOne['course_limitopencyclenum']>0){
            $classOne['course_limitopencyclenum']+=100;
        }


        $inttype = $this->LgArraySwitch(array('0' => '课次考勤(缺勤计费)', '1' => '连续缺勤', '2' => '自然周考勤', '3' => '月度考勤', '4' => '累计缺勤', '5' => '课次考勤(缺勤免费)'));
        $number = '';
        if ($classOne['course_checkingintype'] == 1) {
            $number .= '_' . $classOne['course_checkingminday'] . '次';
        } elseif ($classOne['course_checkingintype'] == 4) {
            $number .= '_' . $classOne['course_minabsencenum'] . '次';
        }
        $classOne['course_checkingintype_name'] = $inttype[$classOne['course_checkingintype']] . $number;
        $teachList = $this->DataControl->selectClear(
            "select st.staffer_id,st.staffer_cnname,st.staffer_enname,t.teach_type
            from smc_class_teach as t 
            left join smc_class as c On t.class_id=c.class_id
            left join smc_staffer as st ON st.staffer_id = t.staffer_id
            where (c.class_id='{$request['class_id']}'  or c.class_id ='{$classOne['father_id']}') and t.teach_status =0 and st.staffer_id > 0 group by st.staffer_id");

        if (!$classOne['classroom_cnname']) {
            $classOne['classroom_cnname'] = '--';
        }

        if (!$classOne['free_hournum']) {
            $classOne['free_hournum'] = '0';
        }
        if (!$classOne['free_alrhournum']) {
            $classOne['free_alrhournum'] = '0';
        }

        $classOne['comapny_isclocking'] = $this->companyOne['comapny_isclocking'];

        

        $classOne['left_warmnum'] = $classOne['class_hourwarmnums']+$classOne['class_hourwarmapplynums']-$classOne['warmnum'];
        $classOne['left_reviewnum'] = $classOne['class_hourreviewnums']+$classOne['class_hourreviewapplynum']-$classOne['reviewnum'];

        if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
            $classOne['add_schedule'] = 1;  //是否增加排课
        } else {
            $classOne['add_schedule'] = 0;
        }
        
        $classOne['agohavestu'] = $classOne['agohavestu']?$classOne['agohavestu']:'0';//以前是否有学生入过班

        if ($classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1 && $classOne['breakoff_id'] == 0) {
            $sql = "select hourstudy_id from smc_student_hourstudy where class_id='{$classOne['class_id']}' limit 0,1";
            if ($this->DataControl->selectOne($sql)) {
                $classOne['can_applybreak'] = 1;
            } else {
                $classOne['can_applybreak'] = 0;
            }
        } else {
            $classOne['can_applybreak'] = 0;
        }
        

        if ($classOne['breakoff_id'] > 0 && $classOne['class_status'] == '1' && $classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1) {
            $classOne['can_break'] = 1;
        } else {
            $classOne['can_break'] = 0;
        }

        if ($contractOne && $contractOne['edition_id'] == '2') {
            if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                $classOne['can_promotion'] = 1;
            } else {
                $classOne['can_promotion'] = 0;
            }
        } else {
            if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                $classOne['can_promotion'] = 1;
            } else {
                $classOne['can_promotion'] = 0;
            }
        }

        if ($contractOne && $contractOne['edition_id'] == '2') {
            if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0) {
                $classOne['can_endclass'] = 1;   //是否可以入班
            } else {
                $classOne['can_endclass'] = 0;
            }
        } else {
            if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0) {
                $classOne['can_endclass'] = 1;
            } else {
                $classOne['can_endclass'] = 0;
            }
        }

        if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
            $classOne['is_schedule'] = 1;
        }

        if ($classOne['is_schedule'] > 0 && $classOne['no_hournum'] > 0 && $classOne['course_schedule'] == 0) {
            $classOne['edit_schedule'] = 1;  //可以修改排课
        } else {
            $classOne['edit_schedule'] = 0;
        }

        if ($classOne['is_schedule'] > 0 && $classOne['no_hournum'] > 0 && $classOne['course_schedule'] == 0) {
            $classOne['edit_schedule'] = 1;  //可以修改排课
        } else {
            $classOne['edit_schedule'] = 0;
        }

        if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
            $classOne['add_schedule'] = 1;  //是否增加排课
        } else {
            $classOne['add_schedule'] = 0;
        }

        if ($classOne['breakoff_id'] > 0 && $classOne['class_status'] == '1' && $classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1) {
            $classOne['can_break'] = 1;
        } else {
            $classOne['can_break'] = 0;
        }

        if ($classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1 && $classOne['breakoff_id'] == 0) {
            $sql = "select hourstudy_id from smc_student_hourstudy where class_id='{$classOne['class_id']}' limit 0,1";
            if ($this->DataControl->selectOne($sql)) {
                $classOne['can_applybreak'] = 1;
            } else {
                $classOne['can_applybreak'] = 0;
            }
        } else {
            $classOne['can_applybreak'] = 0;
        }


        $data = array();
        $data['list'] = $classOne;
        $tempinfo = array();
        $tempinfo ['fu_staffer'] = array();
        $tempinfo ['mian_staffer'] = array();
        if ($teachList) {
            foreach ($teachList as $key => $value) {
                if ($value['teach_type'] == 0) {
                    if (trim($value['staffer_enname']) !== '') {
                        if ($value['staffer_enname'] == $value['staffer_cnname']) {
                            $main_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                        } else {
                            $main_tempinfo['staffer_cnname'] = $value['staffer_cnname'] . '-' . $value['staffer_enname'];
                        }
                    } else {
                        $main_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                    }
                    //$main_tempinfo['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                    $main_tempinfo['staffer_id'] = $value['staffer_id'];
                    $tempinfo ['mian_staffer'][] = $main_tempinfo;
                } else {
                    if (trim($value['staffer_enname']) !== '') {
                        if ($value['staffer_enname'] == $value['staffer_cnname']) {
                            $fu_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                        } else {
                            $fu_tempinfo['staffer_cnname'] = $value['staffer_cnname'] . '-' . $value['staffer_enname'];
                        }
                    } else {
                        $fu_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                    }
                    //$fu_tempinfo['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                    $fu_tempinfo['staffer_id'] = $value['staffer_id'];
                    $tempinfo ['fu_staffer'][] = $fu_tempinfo;
                }
            }

        }
        $data['info'] = $tempinfo;
        return $data;
    }

    function classStudent($request)
    {

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%'  )";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }

        //过滤已经其他子班的学员
        if (isset($request['is_in_childclass']) && $request['is_in_childclass'] == '1') {
            $datawhere .= " and ss.student_id not in ( select std.student_id from smc_student_study as std where std.student_id = s.student_id and std.class_id in (select scs.class_id from smc_class as scs where scs.father_id = c.class_id ))";
        }

//        下拉时使用
        if (isset($request['from']) && $request['from'] == 'drop') {

            $pagestart = 0;
            $num = 100;
        }


//        $classNum = $this->DataControl->selectOne("select count(ch.hour_id) as  num from smc_class_hour as ch where ch.class_id= '{$request['class_id']}' and ch.hour_ischecking=1 and ch.hour_iswarming = 0 ");
//        if($classNum){
//            $hour_num = $classNum['num'];
//        }else{
//            $hour_num = 0;
//        }


        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,sf.family_mobile,sc.course_branch,sc.course_cnname,ss.study_beginday,sc.course_id,ss.study_isreading,sc.course_id,sc.course_inclasstype,c.class_type,s.student_img,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_unitexpend,scb.coursebalance_id,c.class_isprepare
			 ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_ischecking=0 and ch.hour_isfree=0) as hourNoNum,
			 (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id= '{$request['class_id']}' and ch.hour_ischecking=1 and ch.hour_iswarming = 0 and ch.hour_day >= ss.study_beginday ) as hour_num
                ,(select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.student_id = s.student_id   order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg 
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_student_coursebalance as scb on scb.student_id=ss.student_id and scb.course_id=c.course_id and scb.school_id=ss.school_id
              where {$datawhere} and ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and ss.study_isreading=1
              group by s.student_id
              order by s.student_id ASC,ss.study_beginday DESC 
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
//            if ($dateexcelarray) {
//                foreach ($dateexcelarray as $key => $value) {
//                    $dateexcelarray[$key]['conversionlog_time'] = date("Y-m-d", $value['conversionlog_time']);
//                }
//            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];// 学员中文名
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];// 学员英文名
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];// 学员编号
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];// 性别
                    $datearray['family_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['family_mobile']);//联系电话
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];// 入班时间
//                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];// 课程别名称
//                    $datearray['course_branch'] = $dateexcelvar['course_branch'];// 课程别编号
                    $datearray['coursebalance_unitexpend'] = $dateexcelvar['coursebalance_unitexpend'];// 消耗单价
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];// 课程余额
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];// 剩余课次
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学员中文名','学员英文名','学员编号','性别','联系电话','入班时间',"消耗单价","课程余额","剩余课次"));
            $excelfileds = array('student_cnname','student_enname','student_branch','student_sex','family_mobile','study_beginday',"coursebalance_unitexpend","coursebalance_figure","coursebalance_time");
            $classOne = $this->DataControl->selectOne("select * from smc_class where school_id = '{$request['school_id']}' and class_id= '{$request['class_id']}' limit 0,1");
            $fielname = $this->LgStringSwitch($classOne['class_cnname']."学生信息");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $sql .= " limit {$pagestart},{$num} ";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }


        foreach ($studentList as &$val) {

            $val['stuportrait_faceimg'] = is_null($val['stuportrait_faceimg']) ? '' : $val['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90';
            //人像采集二维码
            $val['ishaveportrait'] = $val['stuportrait_faceimg'] ? 1 : 0;
            //人像采集二维码
            $urlparam = base64_encode("student_branch={$val['student_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
            $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
            $val['portraitqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));


            $this->updateStuCourseBalance($request['company_id'], $request['school_id'], $val['student_id'], $val['course_id'], $request['class_id']);


            $stuhourstudyOne = $this->DataControl->selectOne("
            select count(c.hourstudy_id) as  num
            from  smc_student_hourstudy as c 
            left join smc_class_hour as ch ON c.hour_id = ch.hour_id
            where c.student_id='{$val['student_id']}' and c.class_id='{$request['class_id']}'  
            and ch.hour_day >= (select study_beginday from smc_student_study as s where s.student_id=c.student_id  and class_id = ch.class_id and  ch.hour_iswarming = 0  limit 0,1) 
          ");


            if ($stuhourstudyOne) {
                $stuhourstudy_num = $stuhourstudyOne['num'];
            } else {
                $stuhourstudy_num = 0;
            }

            //是否可以补考勤  1-可以
            if ($stuhourstudy_num < $val['hour_num']) {
                $val['is_replenish'] = 1;
            } else {
                $val['is_replenish'] = 0;
            }

            if ($val['course_inclasstype'] == 1) {
                $sql = "select substring(sch.hour_day,1,7) as mon
                  from smc_class_hour as sch
                  where sch.class_id='{$request['class_id']}' and sch.hour_ischecking=0 and sch.hour_isfree=0
                  group by substring(sch.hour_day,1,7)
                  ";
                $hourNoNumList = $this->DataControl->selectClear($sql);
                if ($hourNoNumList) {
                    $hourNoNum = count($hourNoNumList);
                } else {
                    $hourNoNum = 0;
                }
                if ($val['coursebalance_time'] < $hourNoNum) {
                    $val['is_need_supplement'] = 1;
                } else {
                    $val['is_need_supplement'] = 0;
                }

            } elseif ($val['course_inclasstype'] == 2) {
                $val['is_need_supplement'] = 1;
            } else {
                if ($val['coursebalance_time'] < $val['hourNoNum']) {
                    $val['is_need_supplement'] = 1;
                } else {
                    $val['is_need_supplement'] = 0;
                }
            }


            $difference = $val['hourNoNum'] - $val['coursebalance_time'];
            if ($difference > 0) {
                $val['is_need_give'] = 1;
            } else {
                $val['is_need_give'] = 0;
            }
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select s.student_id
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              where {$datawhere} and ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and ss.study_isreading=1
              group by s.student_id
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        $data['list'] = $studentList;
        return $data;

    }

    function updateStuCourseBalance($company_id, $school_id, $student_id, $course_id, $class_id = 0)
    {
        $companiesOne = $this->getSchoolCourseCompanies($school_id, 0, $course_id);
        $sql = "select scb.coursebalance_id from smc_student_coursebalance as scb where scb.company_id='{$company_id}' and scb.school_id='{$school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$course_id}'";
        if (!$this->DataControl->selectOne($sql)) {
            $student_coursebalance_data = array();
            $student_coursebalance_data['student_id'] = $student_id;
            $student_coursebalance_data['course_id'] = $course_id;
            $student_coursebalance_data['school_id'] = $school_id;
            $student_coursebalance_data['companies_id'] = $companiesOne['companies_id'];
            $student_coursebalance_data['company_id'] = $company_id;
            $student_coursebalance_data['coursebalance_status'] = '1';
            $student_coursebalance_data['coursebalance_createtime'] = time();
            $student_coursebalance_data['coursebalance_updatatime'] = time();
            $this->DataControl->insertData("smc_student_coursebalance", $student_coursebalance_data);

            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $school_id;
            $courselog_data['companies_id'] = $companiesOne['companies_id'];
            $courselog_data['class_id'] = $class_id;
            $courselog_data['course_id'] = $course_id;
            $courselog_data['log_playname'] = $this->LgStringSwitch('班级课程余额占位');
            $courselog_data['log_playclass'] = '+';
            $courselog_data['log_fromamount'] = 0;
            $courselog_data['log_playamount'] = 0;
            $courselog_data['log_finalamount'] = 0;
            $courselog_data['log_reason'] = $this->LgStringSwitch('班级课程余额占位');
            $courselog_data['log_time'] = strtotime("2019-01-01");
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

            $time_data = array();
            $time_data['student_id'] = $student_id;
            $time_data['school_id'] = $school_id;
            $time_data['companies_id'] = $companiesOne['companies_id'];
            $time_data['course_id'] = $course_id;
            $time_data['class_id'] = $class_id;
            $time_data['timelog_playname'] = $this->LgStringSwitch('班级课程余额占位');
            $time_data['timelog_playclass'] = '+';
            $time_data['timelog_fromtimes'] = 0;
            $time_data['timelog_playtimes'] = 0;
            $time_data['timelog_finaltimes'] = 0;
            $time_data['timelog_reason'] = $this->LgStringSwitch('班级课程余额占位');
            $time_data['timelog_time'] = strtotime("2019-01-01");
            $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);
        }
    }

    function classStuOptionalTimesList($request)
    {

        if (isset($request['type']) && $request['type'] == '1') {
            //班外赠送课时
            $sql = "select st.student_cnname,st.student_branch
                    ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0' and ch.hour_isfree='0') as hourNoNum
                    from smc_student as st
                    where st.student_id='{$request['student_id']}'
              ";
            $info = $this->DataControl->selectOne($sql);
            $info['coursebalance_time'] = 0;
        } else {
            $sql = "select st.student_cnname,st.student_branch,scb.coursebalance_time,scb.course_id
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_ischecking='0' and ch.hour_isfree='0') as hourNoNum
              from smc_student_study as ss
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_student_coursebalance as scb on scb.course_id=c.course_id and scb.student_id=ss.student_id
              left join smc_student as st on st.student_id=ss.student_id
              where ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}'
              and ss.study_isreading='1' and ss.class_id='{$request['class_id']}' limit 0,1
              ";
            $info = $this->DataControl->selectOne($sql);
        }

        $info['difference'] = $info['hourNoNum'] - $info['coursebalance_time'];
        if ($info['difference'] < 0) {
            $info['difference'] = 0;
        }

        $sql = "select ch.hour_lessontimes,ch.hour_name,ch.hour_day,ch.hour_starttime,ch.hour_endtime
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0'
              and ch.hour_isfree='0'
              and ch.hour_lessontimes not in (select fc.hour_lessontimes from smc_student_free_coursetimes as fc where fc.course_id=c.course_id and fc.student_id='{$request['student_id']}' and fc.is_use='0' )
              ";
        $hourList = $this->DataControl->selectClear($sql);

        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        if (!$hourList) {
            $hourList = array();
        }
        if ($hourList) {
            foreach ($hourList as &$hourOne) {
                $week = date('w', strtotime($hourOne['hour_day']));
                $hourOne['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);
                $hourOne['lessontimes'] = $this->LgStringSwitch('第' . $hourOne['hour_lessontimes'] . '课次');
            }

        }

        $field = array();
        $field[0]["fieldstring"] = "hour_lessontimes";
        $field[0]["fieldname"] = $this->LgStringSwitch("课次");
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "hour_name";
        $field[1]["fieldname"] = $this->LgStringSwitch("课次名称");
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "hour_day";
        $field[2]["fieldname"] = $this->LgStringSwitch("上课日期");
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "week_day";
        $field[3]["fieldname"] = $this->LgStringSwitch("上课周次");
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "hour_starttime";
        $field[4]["fieldname"] = $this->LgStringSwitch("开始时间");
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "hour_endtime";
        $field[5]["fieldname"] = $this->LgStringSwitch("结束时间");
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "lessontimes";
        $field[6]["fieldname"] = $this->LgStringSwitch("课次");
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $data = array();
        $data['hourList'] = $hourList;
        $data['info'] = $info;
        $data['field'] = $field;

        return $data;
    }

    function giveAwayLessons($request)
    {
        $sql = "select fo.order_id from smc_freehour_order as fo where fo.student_id='{$request['student_id']}' and fo.order_status='0'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "存在未审核的同类订单,不可申请";
            return false;
        }

        if (isset($request['type']) && $request['type'] == '1') {
            //班外赠送课时
            $sql = "select st.student_cnname,st.student_branch
                    ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0' and ch.hour_isfree='0' and ch.hour_iswarming=0) as hourNoNum
                    from smc_student as st
                    where st.student_id='{$request['student_id']}'
              ";
            $info = $this->DataControl->selectOne($sql);
            $info['coursebalance_time'] = 0;
        } else {
            $sql = "select scb.coursebalance_time,scb.course_id
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_ischecking='0' and ch.hour_isfree='0' and ch.hour_iswarming=0) as hourNoNum
              from smc_student_study as ss
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_student_coursebalance as scb on scb.course_id=c.course_id and scb.student_id=ss.student_id and scb.school_id='{$this->schoolOne['school_id']}'
              where ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}'
              and ss.study_isreading='1' and ss.class_id='{$request['class_id']}' limit 0,1
              ";
            $info = $this->DataControl->selectOne($sql);
        }


//        $sql = "select fc.coursetimes_id from smc_student_free_coursetimes as fc where fc.student_id='{$request['student_id']}' and fc.school_id='{$request['school_id']}' and fc.course_id='{$info['course_id']}' and fc.is_use='0'";
//        $coursetimes = $this->DataControl->selectClear($sql);
//        if ($coursetimes) {
//            $num = count($coursetimes);
//        } else {
//            $num = 0;
//        }
        $difference = $info['hourNoNum'] - $info['coursebalance_time'];
        if ($difference < 0) {
            $difference = 0;
        }

        $list = json_decode(stripslashes($request['list']), true);
        $neednum = count($list);

        if (($difference < $neednum) || $difference == 0) {
            $this->error = true;
            $this->errortip = "选择课次不可超出" . $difference . '次';
            return false;
        }

        $sql = "select c.course_id,sc.course_branch,sc.coursetype_id 
                from smc_class as c 
                left join smc_course as sc on sc.course_id=c.course_id 
                where c.class_id='{$request['class_id']}'";
        $classOne = $this->DataControl->selectOne($sql);

        $left_num=0;

        if($classOne['coursetype_id']==65){
            $sql = "SELECT a.student_id,s.student_cnname,s.student_enname,s.student_branch,g.guildstutype_name
                    ,sum(a.apply_toclasstimes) as apply_toclasstimes
                    FROM  smc_guildstu_apply as a
                    LEFT JOIN smc_student as s ON s.student_id = a.student_id
                    LEFT JOIN smc_code_guildstutype as g ON a.guildstutype_id = g.guildstutype_id
                    where a.student_id='{$request['student_id']}' and a.company_id='{$request['company_id']}'
                    ";

            $getGuildstuOne = $this->DataControl->selectOne($sql);

            if($getGuildstuOne){
                $sqlold = " select student_branch,used_times_old
                            from smc_student_policy_old
                            where student_branch='{$getGuildstuOne['student_branch']}'";
                $oldone = $this->DataControl->selectOne($sqlold);

                $sqlnew = "SELECT A.student_id,count(A.hourstudy_id) AS USED_COUNT
                            FROM smc_student_hourstudy A
                            LEFT JOIN smc_class_hour C ON A.class_id=C.class_id AND A.hour_id=C.hour_id
                            LEFT JOIN smc_class S ON A.class_id = S.class_id
                            LEFT JOIN smc_course H ON C.course_id=H.course_id
                            WHERE 1
                            AND A.student_id='{$getGuildstuOne['student_id']}'
                            AND H.coursetype_id='65'
                            AND C.hour_day>='2020-05-01'
                            AND C.hour_isfree=0
                            AND S.class_type = '0'
                            AND C.hour_iswarming=0
                            AND NOT EXISTS(SELECT 1 FROM smc_school_income
                            WHERE student_id='{$getGuildstuOne['student_id']}'
                            AND hourstudy_id=A.hourstudy_id AND income_price>0)
                            GROUP BY A.student_id ";
                $newone = $this->DataControl->selectOne($sqlnew);

                $sql = "select count(a.coursetimes_id) as leftFreeTimes
                        from smc_student_free_coursetimes as a 
                        left join smc_course as b on b.course_id=a.course_id
                        where a.student_id='{$getGuildstuOne['student_id']}' and b.coursetype_id=65 and a.is_use=0
                        ";

                $leftFreeTimesOne=$this->DataControl->selectOne($sql);

                $apply_toclasstimes = $getGuildstuOne ? $getGuildstuOne['apply_toclasstimes'] : 0 ;
                $TIMES_COUNT = $oldone ?$oldone['used_times_old'] : 0 ;
                $USED_COUNT = $newone ? $newone['USED_COUNT'] : 0;
                $left_COUNT = $leftFreeTimesOne ? $leftFreeTimesOne['leftFreeTimes'] : 0;

                $left_num = $apply_toclasstimes - $TIMES_COUNT - $USED_COUNT - $left_COUNT;

                if($left_num>0 && $left_num<$neednum){
                    $this->error = 1;
                    $this->errortip = "当前申请的赠课课时数不可超过剩余课时数" . $left_num . '次';
                    return false;
                }
            }
        }



        $OrderHandleModel = new \Model\Smc\OrderHandleModel($request);
        $order_pid = $OrderHandleModel->createFreeHourOrder($request['student_id'], $classOne['course_id'], $classOne['course_branch'], $request['class_id'], '', 0, $request['order_img'], $request['reason']);

        $num = 0;

        foreach ($list as $val) {
            if (!$this->DataControl->getFieldOne("smc_student_free_coursetimes", "coursetimes_id", "student_id='{$request['student_id']}' and course_id='{$classOne['course_id']}' and hour_lessontimes='{$val['hour_lessontimes']}' and is_use=0")) {
                $times_data = array();
                $times_data['order_pid'] = $order_pid;
                $times_data['class_id'] = $request['class_id'];
                $times_data['hour_lessontimes'] = $val['hour_lessontimes'];
                $this->DataControl->insertData("smc_freehour_ordertimes", $times_data);

                $num++;
            }
        }

        $order_data = array();
        $order_data['order_alltimes'] = $num;
        $this->DataControl->updateData("smc_freehour_order", "order_pid='{$order_pid}' and company_id='{$request['company_id']}'", $order_data);

        if($left_num>0){
            $param = array();
            $param['is_adopt'] = 1;
            $param['order_pid'] = $order_pid;
            $param['reason'] = $this->LgStringSwitch('公益学员赠课自动审核');
            $Model = new \Model\Gmc\OrderModel($request);
            $Model->examineHourFreeOrder($param);
        }else{

            $fanweiModel = new \Model\Smc\FanweiModel();
            $bool=$fanweiModel->createFreeTimesApply($order_pid);
            if(!$bool){
                $param = array();
                $param['is_adopt'] = 0;
                $param['order_pid'] = $order_pid;
                $param['reason'] = $this->LgStringSwitch('泛微流程生成失败,请联系技术查看问题');
                $Model = new \Model\Gmc\OrderModel($request);
                $Model->examineHourFreeOrder($param);
            }

        }

        return true;

//        $school = $this->DataControl->selectClear("
//SELECT
//	s.school_id,
//	s.school_province
//FROM
//	smc_school AS s
//WHERE
//	company_id = 8888 and s.school_province in (10,8) and school_id = '{$request['school_id']}'");
//
//        $pay = $this->DataControl->selectClear("
//SELECT
//	COUNT(pay_id) as num
//FROM
//	smc_payfee_order AS o
//	LEFT JOIN smc_payfee_order_pay AS p ON o.order_pid = p.order_pid
//	LEFT JOIN smc_code_paytype AS t ON p.paytype_code = t.paytype_code
//	LEFT JOIN smc_payfee_order_course AS c ON o.order_pid = c.order_pid
//	LEFT JOIN smc_course AS sc ON c.course_id = sc.course_id
//WHERE
//	student_id = '{$request['student_id']}'
//	AND t.paytype_ischarge = 1
//	AND sc.coursecat_id = 135
//	AND p.pay_issuccess = 1
//	AND FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d' ) >= '2022-03-10'");
//
//        $pay2 = $this->DataControl->selectClear("
//SELECT
//	COUNT(pay_id) as num
//FROM
//	smc_payfee_order AS o
//	LEFT JOIN smc_payfee_order_pay AS p ON o.order_pid = p.order_pid
//	LEFT JOIN smc_code_paytype AS t ON p.paytype_code = t.paytype_code
//	LEFT JOIN smc_payfee_order_course AS c ON o.order_pid = c.order_pid
//	LEFT JOIN smc_course AS sc ON c.course_id = sc.course_id
//WHERE
//	student_id = '{$request['student_id']}'
//	AND t.paytype_ischarge = 1
//	AND sc.coursecat_id = 133
//	AND p.pay_issuccess = 1
//	AND FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d' ) >= '2021-03-10'");


//        var_dump($request['company_id']);
//        var_dump($request['school_id']);
//        var_dump($classOne['coursetype_id']);
//        var_dump($school);
//        var_dump($pay[0]['num']);
//        var_dump($neednum);
//        var_dump($classOne['coursetype_id']);

//        $sql = "select a.info_id
//                from smc_student_registerinfo as a
//                where a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.coursetype_id=65 and FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d')>='2023-06-01'  and FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d')<='2023-06-30'
//                and not exists(select 1 from smc_freehour_order as x where x.student_id=a.student_id and x.order_status=1)
//                limit 0,1";
//
//
//        if ($request['company_id'] == '8888' && ($request['school_id'] == '2323' || $request['school_id'] == '2322') && ($classOne['coursetype_id'] == '79654' || $classOne['coursetype_id'] == '79653' || $classOne['coursetype_id'] == '79660')) {
//            $request['coursetype_id'] = $classOne['coursetype_id'];
//            $request['num'] = $num;
//            $request['order_pid'] = $order_pid;
//            $this->automaticAudit($request);
//        } elseif ($request['company_id'] == '8888' && $school && $pay[0]['num'] > 0 && ($classOne['coursetype_id'] == '79654' || $classOne['coursetype_id'] == '79653' || $classOne['coursetype_id'] == '79660' || $classOne['coursetype_id'] == '79661') && $neednum == 4 * $pay[0]['num']) {
//            $request['coursetype_id'] = $classOne['coursetype_id'];
//            $request['num'] = $num;
//            $request['order_pid'] = $order_pid;
//            $this->automaticAudit($request, 2);
//        } elseif ($request['company_id'] == '8888' && $school && $pay2[0]['num'] > 0 && $classOne['coursetype_id'] == '61' && $neednum == 3 * $pay2[0]['num']) {
//            $request['coursetype_id'] = $classOne['coursetype_id'];
//            $request['num'] = $num;
//            $request['order_pid'] = $order_pid;
//            $this->automaticAudit($request, 2);
//        } elseif ($this->DataControl->selectOne($sql) && (($classOne['coursetype_id'] == '79674' && $neednum == '3') or (in_array($classOne['coursetype_id'], array('79654', '79653', '79660')) && $neednum == '2'))) {
//            $request['coursetype_id'] = $classOne['coursetype_id'];
//            $request['num'] = $num;
//            $request['order_pid'] = $order_pid;
//            $this->automaticAudit($request, 2);
//        }

//        return true;
    }

    function automaticAudit($request, $status)
    {
        if ($status <> '2') {
            $sql = "select sum(c.pricinglog_buyprice) as buyprice from smc_student_coursebalance_pricinglog as c 
                left join smc_payfee_order as o on o.order_pid=c.order_pid 
                left join smc_course as sc on sc.course_id=c.course_id 
                where c.school_id='{$request['school_id']}' and c.student_id='{$request['student_id']}' and sc.coursetype_id='65' and o.order_status = 4";
            $pricinglog = $this->DataControl->selectOne($sql);
            if ($pricinglog['buyprice'] < 3200) {
                $this->error = true;
                $this->errortip = "课程购买金额需在3200元以上";
                return false;
            }
            $sql = "select sum(c.coursebalance_figure) as figure_num from smc_student_coursebalance as c left join smc_course as sc on sc.course_id=c.course_id where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and c.student_id='{$request['student_id']}' and sc.coursetype_id='65'";
            $coursebalance = $this->DataControl->selectOne($sql);
            if (!$coursebalance['figure_num']) {
                $this->error = true;
                $this->errortip = "课程金额不足";
                return false;
            }

            $order = $this->DataControl->getOne("smc_freehour_order", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and order_status='1' and order_alltimes = 12");
            if ($order) {
                $this->error = true;
                $this->errortip = "已存在审核通过12次的赠课申请";
                return false;
            }

            if ($request['num'] != 12) {
                if ($request['coursetype_id'] == '79654') {
                    $tip = "美语";
                } elseif ($request['coursetype_id'] == '79653') {
                    $tip = "Steam";
                } else {
                    $tip = "中文绘本";
                }
                $this->error = true;
                $this->errortip = $tip . "赠送课次申请不等于12";
                return false;
            }

            $sql = "select st.study_id from smc_student_study as st left join smc_class as c on c.class_id=st.class_id left join smc_course as sc on sc.course_id=c.course_id where st.company_id='{$request['company_id']}' and st.school_id='{$request['school_id']}' and st.student_id='{$request['student_id']}' and st.study_isreading='1' and sc.coursetype_id='{$request['coursetype_id']}'";
            $class = $this->DataControl->selectOne($sql);
            if ($class) {
                $this->error = true;
                $this->errortip = "赠送课需非在读";
                return false;
            }
        }


        $param = array();
        $param['is_adopt'] = 1;
        $param['order_pid'] = $request['order_pid'];
        $Model = new \Model\Gmc\OrderModel($request);
        $Model->examineHourFreeOrder($param);

        return true;
    }

    function endClassStudent($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%'  )";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ch.hour_id from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=1 order by ch.hour_lessontimes desc limit 0,1";

        $hourOne = $this->DataControl->selectOne($sql);
        if (!$hourOne) {
            $this->error = true;
            $this->errortip = "无学生信息";
            return false;
        }

        $sql = "select st.student_id,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,p.parenter_mobile,ss.study_beginday,ss.study_endday
                ,(select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.student_id = sh.student_id   order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg
                ,(select l.class_isprepare from smc_class as l where sh.class_id = l.class_id limit 0,1) as class_isprepare 
              from smc_student_hourstudy as sh,smc_student as st,smc_student_family as sf,smc_student_study as ss,smc_parenter as p 
              where {$datawhere} and sh.student_id=st.student_id and st.student_id=sf.student_id and sf.family_isdefault=1 and ss.student_id=sh.student_id and ss.class_id=sh.class_id and p.parenter_id=sf.parenter_id 
              and sh.class_id='{$request['class_id']}' and sh.hour_id ='{$hourOne['hour_id']}'
              order by ss.study_beginday asc 
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
//            if ($dateexcelarray) {
//                foreach ($dateexcelarray as $key => $value) {
//                    $dateexcelarray[$key]['conversionlog_time'] = date("Y-m-d", $value['conversionlog_time']);
//                }
//            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];// 学员中文名
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];// 学员英文名
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];// 学员编号
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];// 性别
                    $datearray['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['parenter_mobile']);//联系电话
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];// 入班时间
                    $datearray['study_endday'] = $dateexcelvar['study_endday'];// 出班时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学员中文名','学员英文名','学员编号','性别','联系电话','入班时间',"出班时间"));
            $excelfileds = array('student_cnname','student_enname','student_branch','student_sex','parenter_mobile','study_beginday',"study_endday");
            $classOne = $this->DataControl->selectOne("select * from smc_class where school_id = '{$request['school_id']}' and class_id= '{$request['class_id']}' limit 0,1");
            $fielname = $this->LgStringSwitch($classOne['class_cnname']."学生信息");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $sql .= " limit {$pagestart},{$num} ";
        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学生信息";
            return false;
        }
        foreach ($studentList as &$val) {
            $val['stuportrait_faceimg'] = is_null($val['stuportrait_faceimg']) ? '' : $val['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90';
            //人像采集二维码
            $val['ishaveportrait'] = $val['stuportrait_faceimg'] ? 1 : 0;
            //人像采集二维码
            $urlparam = base64_encode("student_branch={$val['student_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
            $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
            $val['portraitqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));
        }

        $count_sql = "select st.student_id
              from smc_student_hourstudy as sh,smc_student as st,smc_student_family as sf,smc_student_study as ss,smc_parenter as p 
              where {$datawhere} and sh.student_id=st.student_id and st.student_id=sf.student_id and sf.family_isdefault=1 and ss.student_id=sh.student_id and ss.class_id=sh.class_id and p.parenter_id=sf.parenter_id 
              and sh.class_id='{$request['class_id']}' and sh.hour_id ='{$hourOne['hour_id']}'
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        $allnum = $db_nums ? count($db_nums) : 0;
        $data['allnum'] = $allnum;
        $data['list'] = $studentList;
        return $data;
    }

    function openClassStudent($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cl.client_cnname like '%{$request['keyword']}%' or cl.client_enname like '%{$request['keyword']}%' or cl.client_mobile like '%{$request['keyword']}%'  )";
        }
        if (isset($request['hour_day']) && $request['hour_day'] !== '') {
            $datawhere .= " and ch.hour_day  ='{$request['hour_day']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
                select  ha.audition_id,cl.client_id ,cl.client_cnname,cl.client_enname,cl.client_sex,cl.client_mobile,ch.hour_day,ha.audition_isvisit,(case when IFNULL(ha.audition_novisitreason,'')='' then '--' else ha.audition_novisitreason end) as audition_novisitreason ,d.audition_visittime  
                from  smc_class_hour_audition  as ha
                left join smc_class_hour as ch ON ha.hour_id=ch.hour_id
                left join  crm_client as cl ON cl.client_id = ha.client_id 
                left join crm_client_audition as d ON d.client_id = cl.client_id and d.hour_id = ha.hour_id 
                where ch.class_id= '{$request['class_id']}'  and {$datawhere}  
                ";

        $arr_isvisit = $this->LgArraySwitch(array('0' => '待确认', '1' => '已试听', '-1' => '未试听'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return array();
            }
            if ($dateexcelarray) {
                foreach ($dateexcelarray as $key => $value) {
                    $dateexcelarray[$key]['audition_isvisitname'] = $arr_isvisit[$value['audition_isvisit']];
                    $dateexcelarray[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];// 姓名
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];// 性别
                    $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);//主要联系电话
                    if($dateexcelvar['audition_visittime']){
                        $datearray['hour_day'] = date('Y-m-d',strtotime($dateexcelvar['audition_visittime']));// 试听日期
                    }else{
                        $datearray['hour_day'] = $dateexcelvar['hour_day'];// 试听日期
                    }
                    $datearray['audition_isvisitname'] = $dateexcelvar['audition_isvisitname'];// 是否试听
                    $datearray['audition_novisitreason'] = $dateexcelvar['audition_novisitreason'];// 未试听原因
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('姓名','性别','主要联系电话','试听日期','是否试听','未试听原因'));
            $excelfileds = array('client_cnname','client_sex','client_mobile','hour_day','audition_isvisitname','audition_novisitreason');
            $classOne = $this->DataControl->selectOne("select * from smc_class where school_id = '{$request['school_id']}' and class_id= '{$request['class_id']}' limit 0,1");
            $fielname = $this->LgStringSwitch($classOne['class_cnname']."学生信息");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }

        $sql .= " limit {$pagestart},{$num} ";
        $dataList = $this->DataControl->selectClear($sql);
        $allnum = 0;
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select count(ha.audition_id)  as aud_num
                from  smc_class_hour_audition  as ha
                left join smc_class_hour as ch ON ha.hour_id=ch.hour_id
                left join  crm_client as cl ON cl.client_id = ha.client_id 
                where ch.class_id= '{$request['class_id']}' and  {$datawhere}         
              ";
            $db_nums = $this->DataControl->selectOne($count_sql);

            if ($db_nums) {
                $allnum = $db_nums['aud_num'];
            } else {
                $allnum = 0;
            }
        }
        $data['allnum'] = $allnum;

        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$value) {
                //crm试听和单校对不上 单校调课了
                if($value['audition_visittime']){
                    $value['hour_day'] = date('Y-m-d',strtotime($value['audition_visittime']));
                }
                $value['audition_isvisitname'] = $arr_isvisit[$value['audition_isvisit']];
                $value['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
            }
        }
        $data['list'] = $dataList;
        return $data;

    }

    function classTimetable($request)
    {

        $datawhere = " 1 and ch.hour_ischecking <> 1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or ch.hour_starttime like '%{$request['keyword']}%' or ch.hour_endtime like '%{$request['keyword']}%' or ch.hour_number like '%{$request['keyword']}%' )";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

        $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_id,ch.hour_way,ch.hour_number
,cl.classroom_cnname,hour_noon,ch.hour_ischecking,c.class_cnname,co.course_cnname,s.staffer_cnname,co.course_inclasstype,ch.hour_way
,ch.hour_lessontimes,co.course_classnum,ch.hour_isfree,c.class_id,c.class_enname,s.staffer_id,ch.hour_iswarming,hour_cancelnote,c.class_type,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ch.class_id and sch.hour_iswarming='0') as hour_num,
			(select sf.staffer_cnname from smc_class_hour_teaching  as ht
			  left join  smc_staffer as sf ON sf.staffer_id = ht.staffer_id
			 	where  ht.hour_id = ch.hour_id and ht.teaching_type = 1 limit 0,1) as re_staffer_cnname
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type = 0
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}'and c.company_id='{$request['company_id']}'
              GROUP BY ch.hour_id
              order by ch.hour_lessontimes ASC 
        ";


        $hourList = $this->DataControl->selectClear($sql);

        $last_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking = 0", "order by hour_lessontimes DESC");
        $first_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id={$request['class_id']}  and  hour_ischecking <>-1", "order by hour_lessontimes ASC");


        $data = array();
        $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
        $ischeck = $this->LgArraySwitch(array("0" => "待考勤", "1" => "已考勤", "-1" => "已取消"));
        $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {

            if (!$hourList) {
                $this->error = true;
                $this->errortip = "暂无排课信息";
                return false;
            }

            $outexceldate = array();
            foreach ($hourList as $dateexcelvar) {

                $week = date('w', strtotime($dateexcelvar['hour_day']));

                $datearray = array();
                $datearray['hour_day'] = $dateexcelvar['hour_day'];
                $datearray['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);
                $datearray['hour_way_name'] = $this->LgStringSwitch($dateexcelvar['hour_way'] == 0 ? "实体课" : "线上课");
                $datearray['hour_starttime'] = $dateexcelvar['hour_starttime'];
                $datearray['hour_endtime'] = $dateexcelvar['hour_endtime'];

                if ($dateexcelvar['hour_way'] == 1) {
                    $datearray['classroom_cnname'] = $dateexcelvar['hour_number'];
                } else {
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                }

                $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                $datearray['re_staffer_cnname'] = $dateexcelvar['re_staffer_cnname'];
                $datearray['hour_lessontimes'] = $dateexcelvar['hour_lessontimes'];
                $datearray['hour_ischecking_name'] = $ischeck[$dateexcelvar['hour_ischecking']];
                $datearray['hour_isfree'] = $this->LgStringSwitch($dateexcelvar['hour_isfree'] == 0 ? '是' : '否');
                $datearray['hour_iswarming_name'] = $iswarming[$dateexcelvar['hour_iswarming']];
                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("上课日期", "上课周次", "上课方式", "开始时间", "结束时间", "上课教室", "上课教师", "助教教师", "上课周次", "是否考勤", "是否计费", "课次类型"));
            $excelfileds = array('hour_day', 'week_day', 'hour_way_name', 'hour_starttime', 'hour_endtime', 'classroom_cnname', 'staffer_cnname', 're_staffer_cnname', "hour_lessontimes", 'hour_ischecking_name', 'hour_isfree', "hour_iswarming_name");

            $tem_name = '班级排课信息表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;


        } else {
            if ($request['type'] == 1) {
                if ($hourList) {
                    foreach ($hourList as &$hourOne) {
                        if (($last_hourOne['hour_id'] == $hourOne['hour_id'] || $first_hourOne['hour_id'] == $hourOne['hour_id']) && $hourOne['course_inclasstype'] == 0) {

                            $hourOne['is_cancel'] = '1';
                        } else {
                            $hourOne['is_cancel'] = '0';
                        }
                        if ($hourOne['course_inclasstype'] == 2 && $hourOne['hour_ischecking'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        }
                        if ($hourOne['course_inclasstype'] == 1 && $hourOne['hour_ischecking'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        }

                        if ($hourOne['hour_ischecking'] == -1) {
                            $hourOne['is_cancel'] = '0';
                        }
                        if ($hourOne['hour_iswarming'] != 0 && $hourOne['hour_ischecking'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        }

                        $hourOne['hour_ischecking_name'] = $ischeck[$hourOne['hour_ischecking']];
                        $hourOne['hour_lessontimes'] = $hourOne['hour_lessontimes'] . '/' . $hourOne['hour_num'];
                        if ($hourOne['hour_isfree'] == 0) {
                            $hourOne['hour_isfree'] = $this->LgStringSwitch("是");
                        } else {
                            $hourOne['hour_isfree'] = $this->LgStringSwitch("否");
                        }
                        $hourOne['hour_iswarming_name'] = $iswarming[$hourOne['hour_iswarming']];

                        $week = date('w', strtotime($hourOne['hour_day']));
                        $hourOne['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);

                        if ($hourOne['hour_day'] > date("Y-m-d") || $hourOne['hour_ischecking'] == -1) {
                            $hourOne['is_outdate'] = 1;
                        } else {
                            $hourOne['is_outdate'] = 0;
                        }
                        $hourOne['today'] = date("Y-m-d");
                        if ($hourOne['hour_way'] == 1) {
                            $hourOne['classroom_cnname'] = $this->LgStringSwitch("云教室");
                            $hourOne['classroom_cnname'] = $hourOne['classroom_branch'] = $hourOne['hour_number'];
                            $hourOne['classroom_iscloud'] = "1";
                        }
                        $hourOne['hour_way_name'] = $this->LgStringSwitch($hourOne['hour_way'] == 0 ? "实体课" : "线上课");
                    }
                } else {
                    $hourList = array();
                }
                //$companyOne = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");
                $data['list'] = $hourList;
                $data['info'] = $this->companyOne;
                $data['class_type'] = $classOne['class_type'];
            } else {
                if ($hourList) {
                    foreach ($hourList as $key => $val) {
                        $hourList[$key]['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");
                        $hourList[$key]['classroom_cnname'] = $hourOne['classroom_branch'] = $val['hour_number'];
                    }
                    foreach ($hourList as $key => $val) {

                        $week = date('w', strtotime($val['hour_day']));
                        if ($week == 0) {
                            $week = 7;
                        }
                        foreach ($noon as $noonkey => $noonval) {
                            foreach (self::$WORK_DAY as $daykey => $day) {
                                if ($val['hour_noon'] == $noonkey) {
                                    $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                    if ($week == $daykey) {
                                        $data['a' . $noonkey][$day['en']][] = $val;
                                    } else {
                                        $data['a' . $noonkey][$day['en']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
                    if (count($data) < 3) {
                        $tem_data = array();
                        foreach ($data as $k => $v) {
                            $tem_data[] = $k;
                        }
                        $tem_noon = array_diff($noon, $tem_data);

                        if ($tem_noon) {
                            foreach ($tem_noon as $key => $val) {
                                foreach (self::$WORK_DAY as $daykey => $day) {
                                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                                    $data['a' . $key][$day['en']]['-1'] = '';
                                }
                            }
                        }
                    }
                } else {
                    foreach ($noon as $key => $val) {
                        foreach (self::$WORK_DAY as $daykey => $day) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val;
                            $data['a' . $key][$day['en']]['-1'] = '';
                        }
                    }
                }
                $data = $this->get_arr($data);
                $data = array_values($data);
                $tem_data = array();
                foreach ($data as $val) {
                    if ($val['noon_name']['noon_name'] == 'morning') {
                        $tem_data[0] = $val;
                        $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                    } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                        $tem_data[1] = $val;
                        $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                    } elseif ($val['noon_name']['noon_name'] == 'night') {
                        $tem_data[2] = $val;
                        $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                    }
                }
                asort($tem_data);

                //$companyOne = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");

                $data['list'] = $tem_data;
                $data['info'] = $this->companyOne;
                $data['class_type'] = $classOne['class_type'];

            }
            return $data;
        }
    }

    function IntToChr($index, $start = 65)
    {
        $str = '';
        if (floor($index / 26) > 0) {
            $str .= self::IntToChr(floor($index / 26) - 1);
        }
        return $str . chr($index % 26 + $start);
    }

    function classTimes($request)
    {
        $a = $this->DataControl->getFieldOne("smc_school", "school_inclass", "school_id = '{$request['school_id']}'");
        $date = date("Y-m-d");

        if ($a['school_inclass'] == '1') {
            $sql = "select ch.hour_name,ch.hour_day 
              from smc_class_hour as ch 
              where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=0 and ch.hour_day >= '{$date}'
              order by ch.hour_day,ch.hour_starttime asc
              ";
        } else {
            $sql = "select ch.hour_name,ch.hour_day 
              from smc_class_hour as ch 
              where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=0
              order by ch.hour_day,ch.hour_starttime asc
              ";
        }


        $hourList = $this->DataControl->selectClear($sql);

        if (!$hourList) {
            $hourList = array();
        }
        return $hourList;

    }


    function classhourtable($request)
    {

        $datawhere = "c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}'and c.company_id='{$request['company_id']}'";
        if (isset($request['hour_ischecking']) && $request['hour_ischecking'] !== '') {
            $datawhere .= " and ch.hour_ischecking  = '{$request['hour_ischecking']}' ";
        } else {
            $datawhere .= "  AND ch.hour_ischecking <> '-1' ";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' or st.staffer_cnname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or st.staffer_branch like '%{$request['keyword']}%' or ch.hour_starttime like '%{$request['keyword']}%' or ch.hour_endtime like '%{$request['keyword']}%' or ch.hour_number like '%{$request['keyword']}%' )";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
        }
        if (isset($request['hour_day']) && $request['hour_day'] !== '') {
            $datawhere .= " and ch.hour_day = '{$request['hour_day']}'";
        }
        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }
        if (isset($request['week_num']) && $request['week_num'] !== '') {
            $datawhere .= " and DATE_FORMAT(ch.hour_day,'%w') = '{$request['week_num']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT ch.hour_id,ch.hour_name,ch.hour_day,CONCAT(ch.hour_starttime,'~',ch.hour_endtime) AS hour_timesection,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_id,ch.hour_way,ch.hour_number,ch.hour_lessontimes
,cl.classroom_cnname,hour_noon,ch.hour_ischecking,c.class_cnname,co.course_cnname,s.staffer_cnname,s.staffer_enname,co.course_inclasstype,ch.hour_way,cc.teachtype_name,tt.teachtype_name as re_teachtype_name
,ch.hour_lessontimes,co.course_classnum,ch.hour_isfree,c.class_id,c.class_enname,s.staffer_id,ch.hour_iswarming,hour_cancelnote,c.class_type,st.staffer_id as re_staffer_id,st.staffer_cnname as re_staffer_cnname,st.staffer_enname as re_staffer_enname,co.course_opensonmode,co.course_openclasstype,
            ifnull((select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ch.class_id and sch.hour_iswarming='0' and sch.hour_ischecking <> -1),0) as hour_num,
            ifnull((select 1 from crm_client_audition as aud where aud.hour_id=ch.hour_id and aud.company_id = '{$request['company_id']}' and aud.audition_isvisit in (0,1) limit 0,1),0) as ishaveaudition,
            (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ch.class_id and sch.hour_iswarming=ch.hour_iswarming  and sch.hour_ischecking <> -1) as hourwarm_num
            ,ifnull((select 0 from smc_class_hour as x where x.class_id=ch.class_id and x.hour_lessontimes>ch.hour_lessontimes and x.hour_ischecking<>-1 limit 0,1),1) as is_last
            ,ifnull((select x.adjustapply_id from smc_class_hour_adjustapply as x where x.class_id=ch.class_id and (x.hour_id=ch.hour_id or (x.adjustapply_type=1 and x.hour_day=ch.hour_day)) and x.adjustapply_status=0 limit 0,1),0) as has_adjustapply
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type = 0 and cht.teaching_isdel=0
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type = 1 and ht.teaching_isdel=0
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_code_teachtype as cc on cc.teachtype_code=cht.teachtype_code and cc.company_id='{$request['company_id']}'
              left join smc_code_teachtype as tt on tt.teachtype_code=ht.teachtype_code and tt.company_id='{$request['company_id']}'
              where {$datawhere}
              GROUP BY ch.hour_id
              order by ch.hour_day ASC,ch.hour_starttime ASC,ch.hour_endtime ASC,ch.hour_lessontimes ";

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] !== '') {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        if (isset($request['is_page']) && $request['is_page'] !== '') {
            $sql .= " LIMIT {$pagestart},{$num}";
        }

        $hourList = $this->DataControl->selectClear($sql);

        $last_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking = 0", "order by hour_lessontimes DESC");
        $first_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id={$request['class_id']}  and  hour_ischecking <>-1", "order by hour_lessontimes ASC");


        $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
        $ischeck = $this->LgArraySwitch(array("0" => "待考勤", "1" => "已考勤", "-1" => "已取消"));
        $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $sqlclassmain = "SELECT B.school_id,B.school_branch,B.school_cnname,A.class_id,A.class_branch,A.class_cnname,A.class_enname,A.class_stdate,A.class_enddate,A.class_timestr,
                            (select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=0) as main_teacher,
                            (select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=1) as sub_teacher
                            FROM smc_class A 
                            LEFT JOIN smc_school B ON A.school_id=B.school_id
                            WHERE A.company_id='{$request['company_id']}'
                            AND A.school_id='{$request['school_id']}'
                            AND A.class_id='{$request['class_id']}' ";
            $mainList = $this->DataControl->selectOne($sqlclassmain);

            $sqlclasshour = "SELECT A.class_id,A.hour_id,A.hour_lessontimes,A.hour_iswarming,
                            DATE_FORMAT(A.hour_day,'%m/%d') AS hour_day,A.hour_ischecking,A.hour_starttime,A.hour_endtime
                            FROM smc_class_hour A
                            WHERE hour_ischecking>-1
                            AND A.class_id='{$request['class_id']}'
                            ORDER BY A.hour_iswarming,A.hour_day,A.hour_starttime ";
            $classhourList = $this->DataControl->selectClear($sqlclasshour);

            $sqlstudstudy = "SELECT A.student_id,B.student_branch,B.student_cnname,B.student_enname,C.family_mobile 
                            FROM smc_student_study A 
                            LEFT JOIN smc_student B  ON A.company_id=B.company_id AND A.student_id=B.student_id 
                            LEFT JOIN smc_student_family C on C.student_id=A.student_id and C.family_isdefault=1 
                            LEFT JOIN smc_class D on D.class_id=A.class_id 
                            WHERE A.class_id='{$request['class_id']}' 
                            and A.study_endday>=D.class_stdate
                            ORDER BY A.student_id ";
            $studstudyList = $this->DataControl->selectClear($sqlstudstudy);

            $sqlhourstudy = "SELECT A.student_id,A.hour_id,B.stuchecktype_code 
                            FROM smc_student_hourstudy A 
                            LEFT JOIN smc_student_clockinginlog B ON A.hourstudy_id=B.hourstudy_id 
                            WHERE A.class_id='{$request['class_id']}'";
            $hourstudyList = $this->DataControl->selectClear($sqlhourstudy);

            $stucheck = array("" => "", "101" => "√", "107" => "×");

            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");

            if (!$hourList || !$mainList || !$classhourList) {
                $this->error = true;
                $this->errortip = "暂无排课信息";
                return false;
            }

            // 引入类库
//            import('phpexcel.PHPExcel', EXTEND_PATH);

            // 文件名和文件类型
            $fileName = $this->LgStringSwitch("{$schoolOne['school_cnname']}班级考勤表-{$mainList['class_enname']}");
            $fileType = ".xlsx";

            query_to_excel_only($schoolOne, $mainList, $classhourList, $studstudyList, $hourstudyList, $stucheck, $fileName, $fileType);
            exit;
        } else {
            if ($request['type'] == 1) {
                if ($hourList) {
                    foreach ($hourList as &$hourOne) {
                        if (trim($hourOne['staffer_enname']) !== '') {
                            if ($hourOne['staffer_enname'] != $hourOne['staffer_cnname']) {
                                $hourOne['staffer_cnname'] = $hourOne['staffer_cnname'] . '-' . $hourOne['staffer_enname'];
                            }
                        }

                        if (trim($hourOne['re_staffer_enname']) !== '') {
                            if ($hourOne['re_staffer_enname'] != $hourOne['re_staffer_cnname']) {
                                $hourOne['re_staffer_cnname'] = $hourOne['re_staffer_cnname'] . '-' . $hourOne['re_staffer_enname'];
                            }
                        }

                        if ($hourOne['teachtype_name'] != '') {
                            $hourOne['staffer_cnname'] = $hourOne['staffer_cnname'] ? $hourOne['staffer_cnname'] . '(' . $hourOne['teachtype_name'] . ')' : '';
                        }

                        if ($hourOne['re_teachtype_name'] != '') {
                            $hourOne['re_staffer_cnname'] = $hourOne['re_staffer_cnname'] ? $hourOne['re_staffer_cnname'] . '(' . $hourOne['re_teachtype_name'] . ')' : '';
                        }

                        if (($last_hourOne['hour_id'] == $hourOne['hour_id'] || $first_hourOne['hour_id'] == $hourOne['hour_id']) && $hourOne['course_inclasstype'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        } else {
                            $hourOne['is_cancel'] = '0';
                        }
                        if ($hourOne['course_inclasstype'] == 2 && $hourOne['hour_ischecking'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        }
                        if ($hourOne['course_inclasstype'] == 1 && $hourOne['hour_ischecking'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        }

                        if ($hourOne['hour_ischecking'] == -1) {
                            $hourOne['is_cancel'] = '0';
                        }
                        if ($hourOne['hour_iswarming'] != 0 && $hourOne['hour_ischecking'] == 0) {
                            $hourOne['is_cancel'] = '1';
                        }

                        $hourOne['lessontimes']=$hourOne['hour_lessontimes'];

                        $hourOne['hour_ischecking_name'] = $ischeck[$hourOne['hour_ischecking']];
                        if ($hourOne['hour_iswarming'] == 0) {
                            $hourOne['hour_lessontimes'] = $hourOne['hour_lessontimes'] . '/' . $hourOne['hour_num'];
                        } else {
                            $hourOne['hour_lessontimes'] = $hourOne['hour_lessontimes'] . '/' . $hourOne['hourwarm_num'];
                        }
                        if ($hourOne['hour_isfree'] == 0) {
                            $hourOne['hour_isfree'] = $this->LgStringSwitch("是");
                        } else {
                            $hourOne['hour_isfree'] = $this->LgStringSwitch("否");
                        }
                        $hourOne['hour_iswarming_name'] = $iswarming[$hourOne['hour_iswarming']];

                        $week = date('w', strtotime($hourOne['hour_day']));
                        $hourOne['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);

                        if ($hourOne['hour_day'] > date("Y-m-d") || $hourOne['hour_ischecking'] == -1) {
                            $hourOne['is_outdate'] = 1;
                        } else {
                            $hourOne['is_outdate'] = 0;
                        }
                        $hourOne['today'] = date("Y-m-d");
                        if ($hourOne['hour_way'] == 1) {
                            $hourOne['classroom_cnname'] = $this->LgStringSwitch("云教室");
                            $hourOne['classroom_cnname'] = $hourOne['classroom_branch'] = $hourOne['hour_number'];
                            $hourOne['classroom_iscloud'] = "1";
                        }
                        $hourOne['hour_way_name'] = $this->LgStringSwitch($hourOne['hour_way'] == 0 ? "实体课" : "线上课");
                    }
                } else {
                    $hourList = array();
                }
                //$companyOne = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");
                $data['list'] = $hourList;
                $data['info'] = $this->companyOne;
                $data['class_type'] = $classOne['class_type'];
            } else {
                if ($hourList) {
                    foreach ($hourList as $key => $val) {
                        $hourList[$key]['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");
                        if ($val['hour_way'] == 1) {
                            $hourList[$key]['classroom_cnname'] = $this->LgStringSwitch("云教室");
                            $hourList[$key]['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                            $hourList[$key]['classroom_iscloud'] = "1";
                        }
                    }
                    foreach ($hourList as $key => $val) {

                        $week = date('w', strtotime($val['hour_day']));
                        if ($week == 0) {
                            $week = 7;
                        }
                        foreach ($noon as $noonkey => $noonval) {
                            foreach (self::$WORK_DAY as $daykey => $day) {
                                if ($val['hour_noon'] == $noonkey) {
                                    $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                    if ($week == $daykey) {
                                        $data['a' . $noonkey][$day['en']][] = $val;
                                    } else {
                                        $data['a' . $noonkey][$day['en']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
                    if (count($data) < 3) {
                        $tem_data = array();
                        if ($data) {
                            foreach ($data as $k => $v) {
                                $tem_data[] = $k;
                            }
                        }

                        $tem_noon = array_diff($noon, $tem_data);

                        if ($tem_noon) {
                            foreach ($tem_noon as $key => $val) {
                                foreach (self::$WORK_DAY as $daykey => $day) {
                                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                                    $data['a' . $key][$day['en']]['-1'] = '';
                                }
                            }
                        }
                    }
                } else {
                    foreach ($noon as $key => $val) {
                        foreach (self::$WORK_DAY as $daykey => $day) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val;
                            $data['a' . $key][$day['en']]['-1'] = '';
                        }
                    }
                }
                $data = $this->get_arr($data);
                $data = array_values($data);
                $tem_data = array();
                if ($data) {
                    foreach ($data as $val) {
                        if ($val['noon_name']['noon_name'] == 'morning') {
                            $tem_data[0] = $val;
                            $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                        } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                            $tem_data[1] = $val;
                            $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                        } elseif ($val['noon_name']['noon_name'] == 'night') {
                            $tem_data[2] = $val;
                            $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                        }
                    }
                    asort($tem_data);
                }


                // = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");

                $data['list'] = $tem_data;
                $data['info'] = $this->companyOne;
                $data['class_type'] = $classOne['class_type'];

            }
            return $data;
        }
    }

    function classRollCall($request)
    {
        $today = date("Y-m-d", time());
        $datawhere = " 1 ";
        if (isset($request['today']) && $request['today'] !== '') {
            $datawhere .= " and ch.hour_day = '{$request['today']}'";
        } else {
            $datawhere .= " and ch.hour_day = '{$today}'";
        }
        $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,hour_noon
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}'
              order by ch.hour_starttime DESC
        ";
        $hourList = $this->DataControl->selectClear($sql);
        $classroomArray = array();
        foreach ($hourList as $k => $v) {
            $classroomArray[] = $v['classroom_branch'];
        }

        $classroomArray = array_unique($classroomArray);
        $data = array();
        $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
        if ($hourList) {
            foreach ($hourList as $key => $val) {
                foreach ($noon as $noonkey => $noonval) {
                    if ($val['hour_noon'] == $noonkey) {
                        foreach ($classroomArray as $roomkey => $room) {
                            if ($val['classroom_branch'] == $room) {
                                $data[$noonval]["a" . $room][] = $val;
                            } else {
                                $data[$noonval]["a" . $room]['-1'] = '';
                            }
                        }
                    }
                }
            }
            if (count($data) < 3) {
                $tem_data = array();
                foreach ($data as $k => $v) {
                    $tem_data[] = $k;
                }
                $tem_noon = array_diff($noon, $tem_data);
                if ($tem_noon) {
                    foreach ($tem_noon as $key => $val) {
                        foreach ($classroomArray as $roomkey => $room) {
                            $data[$val]["a" . $room]['-1'] = '';
                        }
                    }
                }
            }
        } else {
            foreach ($noon as $key => $val) {
                foreach ($classroomArray as $roomkey => $room) {
                    $data[$val]["a" . $room]['-1'] = '';
                }
            }
        }
        $data = $this->get_arr($data);
        return $data;
    }

    function classAttendance($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and sch.hour_day>='{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and sch.hour_day<='{$request['endtime']}'";
        }
        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and sch.hour_way='{$request['hour_way']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


//        ,(select s.staffer_cnname from smc_class_hour_teaching as ho left join smc_staffer as s on s.staffer_id=ho.staffer_id where ho.teaching_type=0 and ho.hour_id=ch.hour_id limit 0,1) as mainteacher
//              ,(select st.staffer_cnname from smc_class_hour_teaching as hou left join smc_staffer as st on st.staffer_id=hou.staffer_id where hou.teaching_type=1 and hou.hour_id=ch.hour_id limit 0,1) as auxiliaryteacher

        $sql = "select sch.hour_id,sch.hour_day,s.staffer_cnname as mainteacher,s.staffer_enname as mainenname
              ,sch.hour_way
              ,(select count(hourstudy_id) from smc_student_hourstudy as ssh where ssh.class_id=c.class_id and ssh.hour_id=sch.hour_id) as totalnum
              ,(select count(hourstudy_id) from smc_student_hourstudy as ssh where ssh.class_id=c.class_id and ssh.hour_id=sch.hour_id and ssh.hourstudy_checkin=1) as relnum
              ,(select st.staffer_cnname from smc_class_hour_teaching as hou left join smc_staffer as st on st.staffer_id=hou.staffer_id where hou.teaching_type=1 and hou.hour_id=ch.hour_id limit 0,1) as auxiliaryteacher
              ,(select st.staffer_enname from smc_class_hour_teaching as hou left join smc_staffer as st on st.staffer_id=hou.staffer_id where hou.teaching_type=1 and hou.hour_id=ch.hour_id limit 0,1) as auxiliaryenname
              from smc_class_hour as sch
              left join smc_class_hour_teaching as ch on sch.hour_id=ch.hour_id and teaching_type=0
              left join smc_class as c on c.class_id=sch.class_id
              left join smc_staffer as s on s.staffer_id=ch.staffer_id and ch.teaching_type=0
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}' and sch.hour_ischecking=1
              limit {$pagestart},{$num}
        ";
        $attendanceList = $this->DataControl->selectClear($sql);
        if (!$attendanceList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $way = $this->LgArraySwitch(array('0' => '实体课', '1' => '线上课'));

        foreach ($attendanceList as &$attendanceOne) {

            $attendanceOne['mainteacher'] = $attendanceOne['mainenname'] ? $attendanceOne['mainteacher'] . '-' . $attendanceOne['mainenname'] : $attendanceOne['mainteacher'];

            $attendanceOne['auxiliaryteacher'] = $attendanceOne['auxiliaryenname'] ? $attendanceOne['auxiliaryteacher'] . '-' . $attendanceOne['auxiliaryenname'] : $attendanceOne['auxiliaryteacher'];

            $attendanceOne['hour_way_name'] = $way[$attendanceOne['hour_way']];

            if ($attendanceOne['totalnum']) {
                $attendanceOne['ratio'] = round(($attendanceOne['relnum'] / $attendanceOne['totalnum']) * 100, 2) . '%';
            } else {
                $attendanceOne['ratio'] = '0' . '%';
            }
            $attendanceOne['statue'] = $attendanceOne['relnum'] . '/' . $attendanceOne['totalnum'];

            $attendanceOne['number'] = 1;
        }

        $data = array();
        $count_sql = "select sch.hour_id
          from smc_class_hour as sch
          left join smc_class_hour_teaching as ch on sch.hour_id=ch.hour_id and ch.teaching_type=0
          left join smc_class as c on c.class_id=sch.class_id
          left join smc_staffer as s on s.staffer_id=ch.staffer_id and ch.teaching_type=0
          where {$datawhere} and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}' and sch.hour_ischecking=1";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $attendanceList;
        return $data;
    }

    function classChange($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%'  )";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and scl.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and scl.changelog_day>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and scl.changelog_day<='{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select scl.changelog_id,cs.stuchange_name,scl.changelog_day,s.student_branch,s.student_cnname,s.student_enname,sch.school_cnname
              from  smc_student_changelog as scl
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_school as sch on sch.school_id=scl.school_id
              left join smc_code_stuchange as cs on cs.stuchange_code=scl.stuchange_code
              where {$datawhere} and scl.school_id='{$request['school_id']}' and scl.class_id='{$request['class_id']}'
              group by scl.changelog_id  ORDER BY scl.changelog_day DESC
              limit {$pagestart},{$num}
              ";
        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        foreach ($classList as &$val) {
            $val['changelog_day'] = date("Y-m-d", strtotime($val['changelog_day']));
        }
        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select scl.changelog_id
              from  smc_student_changelog as scl
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_school as sch on sch.school_id=scl.school_id
              left join smc_code_stuchange as cs on cs.stuchange_code=scl.stuchange_code
              where {$datawhere} and scl.school_id='{$request['school_id']}' and scl.class_id='{$request['class_id']}'
              group by scl.changelog_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        $data['list'] = $classList;
        return $data;
    }

    function classSettlementList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_branch,s.student_sex,ss.study_beginday,ss.study_endday
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where {$datawhere} and ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}' and substring(ss.study_beginday,1,7)<='{$request['year_moth']}' and substring(ss.study_endday,1,7)>='{$request['year_moth']}'
              group by s.student_id
              order by s.student_id ASC,ss.study_beginday DESC
              limit {$pagestart},{$num}
        ";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        foreach ($studentList as &$studentOne) {
            $sql = "select sh.hourstudy_id
                  from smc_student_hourstudy as sh
                  left join smc_class_hour as sch on sch.hour_id=sh.hour_id
                  where sch.hour_day>='{$studentOne['study_beginday']}' and sch.hour_day<='{$studentOne['study_endday']}'
                  and substring(sch.hour_day,1,7)='{$request['year_moth']}' and sh.student_id='{$studentOne['student_id']}' and sh.hourstudy_checkin='1' and sh.class_id='{$request['class_id']}'";
            $attendanceList = $this->DataControl->selectClear($sql);

            if ($attendanceList) {
                $studentOne['attendanceNum'] = count($attendanceList);
            } else {
                $studentOne['attendanceNum'] = 0;
            }
            $sql = "select sch.hour_id
                  from smc_class_hour as sch
                  where sch.hour_day>='{$studentOne['study_beginday']}' and sch.hour_day<='{$studentOne['study_endday']}'
                  and substring(sch.hour_day,1,7)='{$request['year_moth']}' and sch.class_id='{$request['class_id']}'";

            $hourList = $this->DataControl->selectClear($sql);
            if ($hourList) {
                $studentOne['hourNum'] = count($hourList);
            } else {
                $studentOne['hourNum'] = 0;
            }

            $studentOne['hourstudy_nochecknum'] = $studentOne['hourNum'] - $studentOne['attendanceNum'];
        }

        $data = array();
        $count_sql = "select s.student_id,s.student_cnname,s.student_branch,s.student_sex,ss.study_beginday,ss.study_endday
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where {$datawhere} and ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}' and substring(ss.study_beginday,1,7)<='{$request['year_moth']}' and substring(ss.study_endday,1,7)>='{$request['year_moth']}'
              group by s.student_id";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $sql = "select sc.courseshare_id from smc_student_courseshare as sc
              left join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id
              left join smc_student_study as ss on ss.student_id=scb.student_id and ss.class_id=sc.class_id
              where ss.class_id='{$request['class_id']}' and sc.courseshare_month='{$request['year_moth']}' and sc.courseshare_status='1'
              ";

        if ($this->DataControl->selectOne($sql)) {
            $data['can_settle'] = 0;
        } else {
            $data['can_settle'] = 1;
        }

        $data['list'] = $studentList;

        return $data;
    }

    function classTeacherInfo($request)
    {

        $datawhere = "s.staffer_id = t.staffer_id AND t.class_id = '{$request['class_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or s.staffer_mobile like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.staffer_id, s.staffer_cnname, s.staffer_enname, s.staffer_branch, s.staffer_sex, s.staffer_mobile,min(t.teach_status) as teach_status,s.staffer_leave
                ,(SELECT ct.teachtype_name FROM smc_code_teachtype as ct,smc_class_hour_teaching as ht 
                    WHERE ct.teachtype_code = ht.teachtype_code AND ht.class_id = t.class_id AND ht.staffer_id = s.staffer_id and ct.company_id='{$request['company_id']}' limit 0,1) as teachtype_name
                ,(SELECT cp.post_name FROM gmc_company_post as cp,gmc_staffer_postbe as sp 
                    WHERE cp.post_id = sp.post_id AND sp.school_id = '{$request['school_id']}' AND sp.staffer_id = s.staffer_id LIMIT 0,1) as post_name
                ,( SELECT count(te.teaching_id) FROM smc_class_hour_teaching AS te,smc_class_hour as y WHERE te.hour_id=y.hour_id and te.staffer_id = s.staffer_id AND te.class_id = t.class_id and y.hour_ischecking>=0 and y.hour_iswarming=0) AS classNum 
                ,ifnull(( SELECT count(x.hour_id) FROM smc_class_hour AS x WHERE x.class_id = t.class_id AND x.hour_iswarming=0 and x.hour_ischecking>=0),0) AS allHourNum 
                ,ifnull(( SELECT count(x.hour_id) FROM smc_class_hour AS x WHERE x.class_id = t.class_id AND x.hour_iswarming=0  and x.hour_ischecking=1 and x.hour_staffer_id=s.staffer_id),0) AS realCheckNum 
                ,ifnull(( SELECT count(x.hour_id) FROM smc_class_hour_teaching as x,smc_class_hour AS y WHERE x.hour_id=y.hour_id and x.teaching_type=0 and x.class_id = t.class_id AND y.hour_iswarming=0  and y.hour_ischecking=0 and x.staffer_id=s.staffer_id),0) AS planCheckNum 
                FROM smc_staffer AS s,smc_class_teach AS t 
                WHERE {$datawhere}
                GROUP BY t.class_id,t.staffer_id,t.teach_type 
                ORDER BY t.teach_status ASC,s.staffer_leave ASC 
                limit {$pagestart},{$num}";

        $infoList = $this->DataControl->selectClear($sql);
        if (!$infoList) {
            $this->error = true;
            $this->errortip = "无教师数据";
            return false;
        } else {
            foreach ($infoList as &$infoOne) {

                if ($infoOne['staffer_leave'] == '1') {
                    $infoOne['staffer_leave'] = '离职';
                    $infoOne['teach_status']=1;
                } else {
                    $infoOne['staffer_leave'] = '在职';
                }

                if ($infoOne['teach_status'] == '1') {
                    $infoOne['teach_status'] = '已解除';
                } else {
                    $infoOne['teach_status'] = '带班中';
                }

                if($infoOne['allHourNum']>0){
                    $infoOne['classNum'] = $infoOne['classNum'].'('.round((($infoOne['realCheckNum']+$infoOne['planCheckNum']) / $infoOne['allHourNum']) * 100, 2) . '%)';

                }else{
                    $infoOne['classNum'] = $infoOne['classNum'].'(--)';
                }

            }
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {

            $sql = "SELECT s.staffer_id 
                    FROM smc_staffer AS s,smc_class_teach AS t 
                    WHERE {$datawhere} 
                    GROUP BY t.class_id,t.staffer_id,t.teach_type ";

            $dbNums = $this->DataControl->selectClear($sql);
            $allnum=$dbNums?count($dbNums):0;
            $data['allnum'] = $allnum;
        }
        $data['list'] = $infoList;

        return $data;
    }

    function classRefreshSort($request)
    {
        if ($request['class_id']) {
            $sql = "call RefreshClass('{$request['class_id']}')";

//            mysqli_query($sql);
            $this->DataControl->query($sql);
            $this->error = 0;
            $this->errortip = "刷新成功！";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "刷新失败！";
            return false;
        }

    }

    function classCourseInfo($request)
    {
        $coursetypeOne = $this->DataControl->selectOne("select ct.coursetype_isopenclass,course_inclasstype from smc_course as co,smc_code_coursetype as ct where co.coursetype_id = ct.coursetype_id and  co.course_id ='{$request['course_id']}'");
        if ($coursetypeOne['coursetype_isopenclass'] == 0) {
            $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);
            if (!$pricingOne) {
                $this->error = true;
                $this->errortip = "无效课程";
                return false;
            }
        }
        $pricingOne['course_inclasstype'] = $coursetypeOne['course_inclasstype'];
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_branch,course_presentednums,course_classnum,course_inclasstype,course_limitamout,course_islimitamout,course_islimitweeks,course_earlynum,course_laternum", "company_id='{$request['company_id']}' and course_id='{$request['course_id']}'");

        $pricingOne['pricing_addtime'] = date("Y-m-d H:i:s", $pricingOne['tuition_addtime']);
        $pricingOne['course_branch'] = $courseOne['course_branch'];
        $pricingOne['course_classnum'] = $courseOne['course_classnum'];
        $pricingOne['course_presentednums'] = $courseOne['course_presentednums'];
        $pricingOne['course_allnums'] = $courseOne['course_presentednums'] + $courseOne['course_classnum'];
        $pricingOne['coursetype_isopenclass'] = $coursetypeOne['coursetype_isopenclass'];
        $pricingOne['course_limitamout'] = $courseOne['course_limitamout'];
        $pricingOne['course_islimitamout'] = $courseOne['course_islimitamout'];
        $pricingOne['course_earlynum'] = $courseOne['course_earlynum'];
        $pricingOne['course_laternum'] = $courseOne['course_laternum'];
        $pricingOne['course_islimitweeks'] = $courseOne['course_islimitweeks'];

        if ($courseOne['course_islimitweeks'] == 1) {
            $weeksList = $this->DataControl->getList("smc_course_weeks", "course_id='{$request['course_id']}' order by weeks_id asc");

            $pricingOne['weeksList'] = $weeksList ? $weeksList : array();
        } else {

            $data = array();
            $k = 0;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周一');
            $k++;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周二');
            $k++;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周三');
            $k++;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周四');
            $k++;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周五');
            $k++;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周六');
            $k++;
            $data[$k]['weeks_branch'] = $this->LgStringSwitch('周日');
            $k++;

            $pricingOne['weeksList'] = $data;
        }


        if ($courseOne['course_inclasstype'] == 2) {
            if ($fitOne = $this->DataControl->getFieldOne("smc_fee_pricing_fit", "fit_buypiece", "tuition_id='{$pricingOne['tuition_id']}' and fit_isdefault =1")) {
                $pricingOne['tuition_buypiece'] = $fitOne['fit_buypiece'];
                $pricingOne['course_classnum'] = $fitOne['fit_buypiece'];
            }
        }

        $pricingOne['isskipweek'] = 0;
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $classlesson = $this->DataControl->selectOne("select x.lessonplan_isskipweek,y.class_appointnum from  smc_class_lessonplan x,smc_class y where x.class_id=y.class_id and x.class_id='{$request['class_id']}' limit 0,1 ");
            if ($classlesson) {
                $pricingOne['isskipweek'] = $classlesson['lessonplan_isskipweek'];
                if ($courseOne['course_inclasstype'] == 2) {
                    $pricingOne['class_appointnum'] = $classlesson['class_appointnum'];
                }
            }
        }
        $courseOne = $this->DataControl->selectOne("select course_opensonmode,course_islimittime,course_limittime,course_offline_main_percentage,course_offline_sub_percentage,course_online_main_percentage,course_online_sub_percentage  from smc_course where course_id='{$request['course_id']}' ");
        if ($courseOne) {
            $pricingOne['course_opensonmode'] = $courseOne['course_opensonmode'];
            $pricingOne['course_islimittime'] = $courseOne['course_islimittime'];
            $pricingOne['course_limittime'] = $courseOne['course_limittime'];
            $pricingOne['course_offline_main_percentage'] = $courseOne['course_offline_main_percentage'];
            $pricingOne['course_offline_sub_percentage'] = $courseOne['course_offline_sub_percentage'];
            $pricingOne['course_online_main_percentage'] = $courseOne['course_online_main_percentage'];
            $pricingOne['course_online_sub_percentage'] = $courseOne['course_online_sub_percentage'];
        }

        //这个班 最后一个被邀约的课是什么时间
        $lastAuditionHourTime = $this->DataControl->selectOne("SELECT a.hour_day
            FROM smc_class_hour as a,crm_client_audition as b
            WHERE a.class_id = '{$request['class_id']}' and a.hour_id = b.hour_id 
            ORDER BY UNIX_TIMESTAMP(a.hour_day) desc limit 0,1 ");
        $pricingOne['lastAuditionHourTime'] = $lastAuditionHourTime['hour_day']?$lastAuditionHourTime['hour_day']:'';

        return $pricingOne;
    }

    function classAdd($request)
    {
//        if(!isset($request['class_isprepare'])){
//            $this->error = true;
//            $this->errortip = "是否预备班必须选择";
//            return false;
//        }

        $data = array();
        if ($request['create_time'] != '') {
            $time = strtotime($request['create_time']);
        } else {
            $time = time();
        }
        $like = date("Ymd", $time);
        $classInfo = $this->DataControl->selectOne("select class_branch from smc_class where class_branch like '{$like}%' AND LENGTH(class_branch) = '14' order by class_branch DESC limit 0,1");

        if ($classInfo) {
            $data['class_branch'] = $classInfo['class_branch'] + 1;
        } else {
            $data['class_branch'] = $like . '000001';
        }

        if ($this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$request['school_id']}' and class_cnname='{$request['class_cnname']}' and class_status>=0")) {
            $this->error = true;
            $this->errortip = "该校已存在相同名称的有效班级,不可重复创建";
            return false;
        }

        if ($request['class_isprepare'] == '1' && $this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$request['school_id']}' and course_id='{$request['course_id']}' and class_isprepare = '1' and class_status>=0 ")) {
            $this->error = true;
            $this->errortip = "该校此班别下已存在预备班,不可重复创建";
            return false;
        }

        $courseOne=$this->DataControl->getFieldOne("smc_course","course_isopenwarm,course_warmnum,course_isopenreview,course_reviewnum,course_ismustreview","course_id='{$request['course_id']}'");

        if(!$courseOne){
            $this->error = true;
            $this->errortip = "课程不存在";
            return false;
        }

        do {
            $classInfo = $this->DataControl->selectOne("select class_branch from smc_class where class_branch like '{$like}%' AND LENGTH(class_branch) = '14' order by class_branch DESC limit 0,1");
            if ($classInfo) {
                $data['class_branch'] = $classInfo['class_branch'] + 1;
            } else {
                $data['class_branch'] = $like . '000001';
            }
        } while ($this->DataControl->getFieldOne("smc_class", "class_id", "class_branch='{$data['class_branch']}'"));
//        if($a = $this->DataControl->getFieldOne("smc_class","class_id","class_branch='{$data['class_branch']}' and company_id='{$request['company_id']}'")){
//        	var_dump($a);
//            $this->error = true;
//            $this->errortip = "编号创建错误";
//            return false;
//        }
        $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_branch", "school_id='{$request['school_id']}'");



        $data['school_id'] = $request['school_id'];
        $data['school_branch'] = $schoolOne['school_branch'];
        $data['company_id'] = $request['company_id'];
        $data['course_id'] = $request['course_id'];
        $data['class_fullnums'] = $request['class_fullnums'];
        $data['class_stdate'] = $request['class_stdate'];
        $data['class_appointnum'] = $request['class_appointnum'];

        if (isset($request['class_id']) && $request['class_id'] != '' && $request['class_id'] > 0) {
            $data['class_isupgrade'] = 1;
            $data['from_class_id'] = $request['class_id'];
        }

        if (isset($request['class_enddate']) && $request['class_enddate'] != '') {
            $data['class_enddate'] = $request['class_enddate'];
        }

        if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
            if ($request['class_stdate'] <= date("Y-m-d", time())) {
                $data['class_status'] = 1;
            }
        }


        $data['class_ismustreview'] = $courseOne['course_ismustreview'];

        if($courseOne['course_isopenwarm']==1){
            $data['class_hourwarmnums'] = $courseOne['course_warmnum'];
        }

        if($courseOne['course_isopenreview']==1){
            $data['class_hourreviewnums'] = $courseOne['course_reviewnum'];
        }

        $data['class_isprepare'] = $request['class_isprepare'];//是否预备班  0 正常 1 预备班

        $data['class_createtime'] = $time;
        $data['class_cnname'] = $request['class_cnname'];
        $data['class_enname'] = $request['class_enname'];
        $class_id = $this->DataControl->insertData("smc_class", $data);
        if ($class_id) {
            if (isset($request['mainteacher_list']) && $request['mainteacher_list'] != '') {
                //1,2
                $mainteacherList = explode(",", $request['mainteacher_list']);
                foreach ($mainteacherList as $val) {
                    $mainTeachData = array();
                    $mainTeachData['class_id'] = $class_id;
                    $mainTeachData['staffer_id'] = $val;
                    $mainTeachData['teach_type'] = 0;
                    $mainTeachData['teach_createtime'] = time();
                    $this->DataControl->insertData("smc_class_teach", $mainTeachData);
                }
            }

            if (isset($request['secondaryteacher_list']) && $request['secondaryteacher_list'] != '') {
                $secondaryteacherList = explode(",", $request['secondaryteacher_list']);
                foreach ($secondaryteacherList as $val) {
                    $secondaryTeacherData = array();
                    $secondaryTeacherData['class_id'] = $class_id;
                    $secondaryTeacherData['staffer_id'] = $val;
                    $secondaryTeacherData['teach_type'] = 1;
                    $secondaryTeacherData['teach_createtime'] = time();
                    $this->DataControl->insertData("smc_class_teach", $secondaryTeacherData);
                }
            }
            $tem_data = array();
            $tem_data['class_id'] = $class_id;
            $result = $tem_data;

        } else {
            $result = false;
        }
        return $result;
    }

    function classManage($request)
    {
        $sql = "SELECT c.class_id,c.class_branch,sc.course_id,sc.course_branch,sc.course_cnname,c.class_cnname,c.class_enname,c.class_stdate,c.class_fullnums,sc.coursetype_id,sc.coursecat_id,cc.coursecat_cnname,scc.coursetype_cnname,sc.course_islimitamout,sc.course_limitamout,c.class_enddate,c.class_appointnum,sc.course_openclasstype,c.class_lastscheduledate,c.class_isprepare,c.class_ismustreview,sc.course_isopenreview,
			  (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking = 1 and ch.hour_isfree=0 ) as is_checking_num,
			  (select hour_day from  smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking<>'-1' order by ch.hour_day DESC limit 0,1) as last_hourday,
			  (select hour_day from  smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking <>'-1'  order by ch.hour_day DESC limit 0,1) as last_checkday,
			  (select count(ch.hour_id) from  smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking=0) as hour_allnum
              ,ifnull((select a.hour_day from smc_class_hour as a where a.class_id = c.class_id and a.hour_ischecking=1 and a.hour_iswarming=2 order by a.hour_lessontimes desc limit 0,1),'') as last_review_hour_day
              ,(c.class_hourreviewnums+c.class_hourreviewapplynum -ifnull((select count(a.hour_id) from smc_class_hour as a where a.class_id = c.class_id and a.hour_ischecking=1 and a.hour_iswarming=2),0)) as can_plan_review_nums
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              left join smc_code_coursetype as scc on scc.coursetype_id=sc.coursetype_id
              where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' limit 0,1";
        $classOne = $this->DataControl->selectOne($sql);
        if ($classOne['last_hourday'] == "") {
            $classOne['last_hourday'] = $classOne['class_stdate'];
        }


        if ($classOne['last_checkday'] == "") {
            $classOne['last_checkday'] = $classOne['class_stdate'];
        } else {
            $classOne['last_checkday'] = date('Y-m-d', strtotime("{$classOne['last_checkday'] } + 1 day"));
        }
        $mainList = $this->DataControl->selectClear("select ct.staffer_id,s.staffer_cnname,s.staffer_enname from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where ct.class_id='{$classOne['class_id']}' and ct.teach_type=0 and ct.teach_status=0 and s.company_id='{$request['company_id']}'");
        $secondaryList = $this->DataControl->selectClear("select ct.staffer_id,s.staffer_cnname,s.staffer_enname from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where ct.class_id='{$classOne['class_id']}' and ct.teach_type=1 and ct.teach_status=0 and s.company_id='{$request['company_id']}'");
        if (!$mainList) {
            $mainList = array();
        } else {
            foreach ($mainList as &$mainOne) {
                $mainOne['staffer_cnname'] = $mainOne['staffer_enname'] ? $mainOne['staffer_cnname'] . '-' . $mainOne['staffer_enname'] : $mainOne['staffer_cnname'];
            }
        }
        if (!$secondaryList) {
            $secondaryList = array();
        } else {
            foreach ($secondaryList as &$secondaryOne) {
                $secondaryOne['staffer_cnname'] = $secondaryOne['staffer_enname'] ? $secondaryOne['staffer_cnname'] . '-' . $secondaryOne['staffer_enname'] : $secondaryOne['staffer_cnname'];
            }
        }

        $lastHourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$request['class_id']}' and hour_ischecking=1 order by hour_lessontimes desc");
        if ($lastHourOne) {
            $classOne['class_lastscheduledate'] = date("Y-m-d", strtotime("+1 day", strtotime($lastHourOne['hour_day'])));
        } else {
            $classOne['class_lastscheduledate'] = $classOne['class_lastscheduledate'] != '' ? $classOne['class_lastscheduledate'] : $classOne['class_stdate'];
        }

        $classOne['mainteacher'] = $mainList;
        $classOne['secondaryteacher'] = $secondaryList;
        $classOne['arrangeList'] = array();

        return $classOne;
    }

    function classEdit($request)
    {
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_cnname,course_id", "class_id='{$request['class_id']}'");

        $classOne['class_cnname'] = addslashes($classOne['class_cnname']);

        if ($this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$request['school_id']}' and class_cnname='{$request['class_cnname']}' and class_cnname<>'{$classOne['class_cnname']}' and class_status>=0")) {
            $this->error = true;
            $this->errortip = "该校已存在相同名称的有效班级,不可重复创建";
            return false;
        }

        if (isset($request['course_id']) && $request['course_id'] <> $classOne['course_id']) {
            $this->error = true;
            $this->errortip = "暂不支持修改课程别编号";
            return false;
        }

        $data = array();
        $data['class_fullnums'] = $request['class_fullnums'];
        $data['class_appointnum'] = $request['class_appointnum'];
        $data['class_cnname'] = $request['class_cnname'];
        $data['class_enname'] = $request['class_enname'];
        $data['class_stdate'] = $request['class_stdate'];
        if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
            if ($request['class_stdate'] <= date("Y-m-d", time())) {
                $data['class_status'] = 1;
            }
        }
        if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
            if ($request['class_stdate'] <= date("Y-m-d", time())) {
                $data['class_status'] = 1;
            } else {
                $data['class_status'] = 0;
            }
        }
        if (isset($request['class_enddate']) && $request['class_enddate'] != '') {
            $data['class_enddate'] = $request['class_enddate'];
        }

        $data['class_updatatime'] = time();
        $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $data);
//        $this->DataControl->delData("smc_class_teach","class_id='{$request['class_id']}'");
        $mainList = $this->DataControl->selectClear("select staffer_id from smc_class_teach where class_id='{$request['class_id']}' and teach_type=0 and teach_status=0");

        $secondaryList = $this->DataControl->selectClear("select staffer_id from smc_class_teach where class_id='{$request['class_id']}' and teach_type=1 and teach_status=0");

        if ($mainList) {
            if (isset($request['mainteacher_list']) && $request['mainteacher_list'] != '') {
                $mainteacherList = explode(",", $request['mainteacher_list']);
                $temMainArray = array();
                foreach ($mainList as $value) {
                    $temMainArray[] = $value['staffer_id'];
                }
                $s = array_intersect(array_diff($temMainArray, $mainteacherList), $temMainArray);
                foreach ($mainteacherList as $val) {
                    $mainteacherOne = $this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=0 and teach_status=0 and staffer_id='{$val}'");
                    if (!$mainteacherOne) {
                        if($this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=0 and staffer_id='{$val}'")){
                            $mainTeachData = array();
                            $mainTeachData['teach_status'] = 0;
                            $mainTeachData['teach_createtime'] = time();
                            $this->DataControl->updateData("smc_class_teach","class_id='{$request['class_id']}' and teach_type=0 and staffer_id='{$val}'", $mainTeachData);
                        }else{
                            $mainTeachData = array();
                            $mainTeachData['class_id'] = $request['class_id'];
                            $mainTeachData['staffer_id'] = $val;
                            $mainTeachData['teach_type'] = 0;
                            $mainTeachData['teach_createtime'] = time();
                            $this->DataControl->insertData("smc_class_teach", $mainTeachData);
                        }
                    }
                }
                foreach ($s as $val) {
                    $sTeachData = array();
                    $sTeachData['teach_status'] = 1;
                    $sTeachData['teach_relievetime'] = time();
                    $this->DataControl->updateData("smc_class_teach", "class_id='{$request['class_id']}' and teach_type=0 and teach_status=0 and staffer_id='{$val}'", $sTeachData);
                }
            }
        } else {
            if (isset($request['mainteacher_list']) && $request['mainteacher_list'] != '') {
                $mainteacherList = explode(",", $request['mainteacher_list']);
                foreach ($mainteacherList as $val) {
                    $mainTeachData = array();
                    $mainTeachData['class_id'] = $request['class_id'];
                    $mainTeachData['staffer_id'] = $val;
                    $mainTeachData['teach_type'] = 0;
                    $mainTeachData['teach_createtime'] = time();
                    $this->DataControl->insertData("smc_class_teach", $mainTeachData);

                }
            }
        }

        if ($secondaryList) {
            $secondaryteacherList = explode(",", $request['secondaryteacher_list']);
            if ($secondaryteacherList) {
                $temSecondaryArray = array();
                foreach ($secondaryList as $value) {
                    $temSecondaryArray[] = $value['staffer_id'];
                }
                $s2 = array_intersect(array_diff($temSecondaryArray, $secondaryteacherList), $temSecondaryArray);
                foreach ($secondaryteacherList as $val) {
                    $secondaryTeacherOne = $this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=1 and teach_status=0 and staffer_id='{$val}'");
                    if (!$secondaryTeacherOne && $val > 0) {
                        if($this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=1 and staffer_id='{$val}'")){
                            $secondaryTeacherData = array();
                            $secondaryTeacherData['teach_status'] = 0;
                            $secondaryTeacherData['teach_createtime'] = time();
                            $this->DataControl->updateData("smc_class_teach","class_id='{$request['class_id']}' and teach_type=1 and staffer_id='{$val}'", $secondaryTeacherData);
                        }else{
                            $secondaryTeacherData = array();
                            $secondaryTeacherData['class_id'] = $request['class_id'];
                            $secondaryTeacherData['staffer_id'] = $val;
                            $secondaryTeacherData['teach_type'] = 1;
                            $secondaryTeacherData['teach_createtime'] = time();
                            $this->DataControl->insertData("smc_class_teach", $secondaryTeacherData);
                        }
                    }
                }
                foreach ($s2 as $val) {
                    $sTeachData = array();
                    $sTeachData['teach_status'] = 1;
                    $sTeachData['teach_relievetime'] = time();
                    $this->DataControl->updateData("smc_class_teach", "class_id='{$request['class_id']}' and teach_type=1 and teach_status=0 and staffer_id='{$val}'", $sTeachData);
                }
            }
        } else {
            if (isset($request['secondaryteacher_list']) && $request['secondaryteacher_list'] != '') {
                $secondaryteacherList = explode(",", $request['secondaryteacher_list']);
                foreach ($secondaryteacherList as $val) {
                    $secondaryTeacherData = array();
                    $secondaryTeacherData['class_id'] = $request['class_id'];
                    $secondaryTeacherData['staffer_id'] = $val;
                    $secondaryTeacherData['teach_type'] = 1;
                    $secondaryTeacherData['teach_createtime'] = time();
                    $this->DataControl->insertData("smc_class_teach", $secondaryTeacherData);
                }
            }
        }
        $array = array();
        $array['class_id'] = $request['class_id'];
        return $array;
    }

    function classDel($request)
    {
        $hourstudyOne = $this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "class_id='{$request['class_id']}'");
        if ($hourstudyOne) {
            $this->error = true;
            $this->errortip = "该班级已存在考勤，不可删除！";
            return false;
        }

        $sql = "select sh.hourstudy_id from smc_student_hourstudy as sh left join smc_class as c on c.class_id=sh.class_id where c.father_id='{$request['class_id']}' limit 0,1";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "该班级子班已存在考勤，不可删除！";
            return false;
        }

        $studyone = $this->DataControl->getFieldOne("smc_student_study", "study_id", "class_id='{$request['class_id']}' and study_isreading=1");
        if ($studyone) {
            $this->error = true;
            $this->errortip = "该班级存在在读学员，不可删除！";
            return false;
        }
        $auditionOne = $this->DataControl->getFieldOne("smc_class_hour_audition", "audition_id", "class_id='{$request['class_id']}'");
        $smc_auditionOne = $this->DataControl->getFieldOne("crm_student_audition", "audition_id", "class_id='{$request['class_id']}'");
        $crm_auditionOne = $this->DataControl->getFieldOne("crm_client_audition", "audition_id", "class_id='{$request['class_id']}'");
        if ($auditionOne || $smc_auditionOne || $crm_auditionOne) {
            $this->error = true;
            $this->errortip = "该班级存在试听学员，不可删除！";
            return false;
        }


        $sql = "select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.father_id='{$request['class_id']}' limit 0,1";

        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "该班级子班存在在读学员，不可删除！";
            return false;
        }

//        $num = $this->DataControl->selectOne("select count(study_id) as num from smc_student_study where company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and class_id='{$request['class_id']}'");
//
//        $classOne = $this->DataControl->getFieldOne("smc_student_changelog", "changelog_id", "company_id='{$request['company_id']}' and class_id='{$request['class_id']}' and school_id='{$request['school_id']}'");
//
//        if ($classOne) {
//            $this->error = true;
//            $this->errortip = "该班级存在学员异动记录，不可删除！";
//            return false;
//        }
//
//        if ($num['num'] > 0 && $num) {
//            $this->error = true;
//            $this->errortip = "该班级存在学员，不可删除！";
//            return false;
//        }

        $audition = $this->DataControl->selectClear("select ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking
                                                  from smc_class_hour_audition as cha
                                                  left join smc_class_hour as ch on ch.hour_id=cha.hour_id and ch.class_id=cha.class_id
                                                  where cha.class_id='{$request['class_id']}'
                                                  ");

        if ($audition) {
            foreach ($audition as $val) {
                $now = date("Y-m-d H:i:s", time());
                $auditionTime = $val['hour_day'] . " " . $val['hour_endtime'];
                if ($val['hour_ischecking'] == 0 && ($now <= $auditionTime)) {
                    $this->error = true;
                    $this->errortip = "该班级有试听不可删除！";
                    return false;
                }
            }
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "from_class_id", "class_id='{$request['class_id']}'");

        if ($classOne && $classOne['from_class_id'] > 0) {
            $this->error = true;
            $this->errortip = "该班级为升班班级不可删除！";
            return false;
        }

        $data = array();
        $data['class_status'] = '-2';
        $data['class_updatatime'] = time();
        if ($this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $data)) {

            $data = array();
            $data['class_status'] = '-2';
            $data['class_updatatime'] = time();
            $this->DataControl->updateData("smc_class", "father_id='{$request['class_id']}' and father_id<>0", $data);

            $hour_data = array();
            $hour_data['hour_ischecking'] = -1;
            $hour_data['hour_updatatime'] = time();
            $this->DataControl->updateData("smc_class_hour", "class_id='{$request['class_id']}'", $hour_data);
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误！";
            return false;
        }

    }

    /**
     *
     * author: ling
     * 对应接口文档 0001
     * @param string $start
     * @param string $end
     * @param $weekday
     * @param string $num
     * @param int $is_frequency 0-按时间  1-按次数
     * @param array $holidayArray
     * @param int $iskipweek 隔一周传1
     * @param int $fixednum 过滤掉几轮循环
     * @return array
     */
    function weedDayList($start = '', $end = '', $weekday, $num = '', $is_frequency = 0, $holidayArray = array(), $iskipweek = 0, $fixednum = 0)
    {
        $begin = empty($start) ? date('Y-m-d') : $start;
        $startTime = strtotime($begin);
        $startDay = date('N', $startTime);

        $list = [];
        if ($startDay <= $weekday) {
            $startTime = strtotime(self::$WORK_DAY[$weekday]['en'], strtotime($start));
        } else {
            $startTime = strtotime('next ' . self::$WORK_DAY[$weekday]['en'], strtotime($start));
        }

        if ($is_frequency == 1) {
            for ($i = 0; ; $i++) {

                if ($iskipweek == 1 && ($i > $fixednum)) {
                    $addnum = $i * ($iskipweek + 1);

                } else {
                    $addnum = $i;
                }

                $dayOfWeek = strtotime("+{$addnum} week", $startTime);
                $dateOne = date('Y-m-d', $dayOfWeek);
                if (is_array($holidayArray)) {
                    if (in_array($dateOne, $holidayArray)) {
                        continue;
                    }
                }
                $list[] = $dateOne;
                if (count($list) == $num) {
                    break;
                }
            }
        } else {
            $endTime = strtotime($end);
            for ($i = 0; ; $i++) {
                if ($iskipweek == 1 && $i > 1) {
                    $addnum = $i * ($iskipweek + 1);
                } else {
                    $addnum = $i;
                }
                $dayOfWeek = strtotime("+{$addnum} week", $startTime);
                if ($dayOfWeek > $endTime) {
                    break;
                }
                if (count($list) >= $num) {
                    break;
                }
                $list[] = date('Y-m-d', $dayOfWeek);
            }
        }
        return $list;
    }

    function newWeedDayList($start = '', $end = '', $num = '', $is_frequency = 0, $weekArray = array(), $holidayArray = array(), $iskipweek = 0, $fixednum = 0)
    {
        $begin = empty($start) ? date('Y-m-d') : $start;
        $startTime = strtotime($begin);

        $list = [];

        if ($is_frequency == 1) {
            for ($i = 0; ; $i++) {

                if ($iskipweek == 1 && ($i > $fixednum)) {
                    $addnum = $i * ($iskipweek + 1);
                } else {
                    $addnum = $i;
                }

                $dayOfWeek = strtotime("+{$addnum} week", $startTime);

                $dateOne = date('Y-m-d', $dayOfWeek);

                $week=date("w",$dateOne)==0?7:date("w",$dateOne);

                if(!in_array($dateOne, $holidayArray) && in_array($week, $weekArray)){
                    $list[] = $dateOne;
                    if (count($list) == $num) {
                        break;
                    }
                }
            }
        } else {
            $endTime = strtotime($end);
            for ($i = 0; ; $i++) {
                if ($iskipweek == 1 && $i > 1) {
                    $addnum = $i * ($iskipweek + 1);
                } else {
                    $addnum = $i;
                }
                $dayOfWeek = strtotime("+{$addnum} week", $startTime);

                $dateOne = date('Y-m-d', $dayOfWeek);

                $week=date("w",$dateOne)==0?7:date("w",$dateOne);

                if(!in_array($dateOne, $holidayArray) && in_array($week, $weekArray)){
                    $list[] = $dateOne;
                }

                if ($dayOfWeek > $endTime) {
                    break;
                }
                if (count($list) >= $num) {
                    break;
                }
            }
        }
        return $list;
    }

    function newArrangeDayList($start = '', $end = '', $num = '', $is_frequency = 0, $arrangeList = array(), $holidayArray = array(), $iskipweek = 0, $fixednum = 0,$hour_iswarming=0)
    {
        $begin = empty($start) ? date('Y-m-d') : $start;
        $startTime = strtotime($begin);

        $planArray=array();
        foreach($arrangeList as $arrageOne){
            $planArray[$arrageOne['weekday_id']][]=$arrageOne;
        }

        if ($is_frequency == 1) {
            for ($i = 0; ; $i++) {

                if ($iskipweek == 1 && ($i > $fixednum)) {
                    $addnum = $i * ($iskipweek + 1);
                } else {
                    $addnum = $i;
                }
                $weekdays=$this->getWeekDates($startTime+$addnum*3600*24*7);
                if($weekdays){
                    foreach($weekdays as $dateOne){

                        $week=date("w",strtotime($dateOne))==0?7:date("w",strtotime($dateOne));

                        if(!in_array($dateOne, $holidayArray) && $planArray[$week] && $dateOne>=$start){
                            foreach($planArray[$week] as $planOne){
                                $data=array();
                                $data['planStartTime']=$dateOne.' '.$planOne['hour_starttime'];
                                $data['planEndTime']=$dateOne.' '.$planOne['hour_endtime'];
                                $data['planOne']=$planOne;
                                $data['day']=$dateOne;
                                $data['hour_iswarming']=$hour_iswarming;
                                $list[] = $data;

                                if (count($list) >= $num) {
                                    return $list;
                                }
                            }
                        }
                    }
                }
            }
        } else {
            $endTime = strtotime($end);
            for ($i = 0; ; $i++) {
                if ($iskipweek == 1 && $i > 1) {
                    $addnum = $i * ($iskipweek + 1);
                } else {
                    $addnum = $i;
                }
                $weekdays=$this->getWeekDates($startTime+$addnum*3600*24*7);

                if($weekdays){
                    foreach($weekdays as $dateOne){

                        $week=date("w",strtotime($dateOne))==0?7:date("w",strtotime($dateOne));

                        if(!in_array($dateOne, $holidayArray) && $planArray[$week] && $dateOne>=$start){
                            foreach($planArray[$week] as $planOne){
                                $data=array();
                                $data['planStartTime']=$dateOne.' '.$planOne['hour_starttime'];
                                $data['planEndTime']=$dateOne.' '.$planOne['hour_endtime'];
                                $data['planOne']=$planOne;
                                $data['day']=$dateOne;
                                $data['hour_iswarming']=$hour_iswarming;
                                $list[] = $data;

                                if (count($list) >= $num) {
                                    return $list;
                                }

                                if ($dateOne >= $end) {
                                    return $list;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    function getWeekDates($date) {
        $weekday = date('w',$date);

        // 周一是第一天 获取本周第一天（如果今天是周日，则为下周的周一）
        $firstDayOfWeek = date('Y-m-d', strtotime('-'.(($weekday==0?7:$weekday)-1).' days',$date));

        // 生成数组，包含本周所有日期
        $dates = [];

        for ($i = 0; $i < 7; $i++) {
            $dates[] = date('Y-m-d', strtotime("+$i days", strtotime($firstDayOfWeek)));
        }
        return $dates;
    }

    function classChoiceTeacher($request)
    {
        $datawhere = " 1 ";
        if (isset($request['class_id']) && $request['class_id'] != '') {
            $datawhere .= " and ch.class_id <> '{$request['class_id']}'";
        }
        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        if (isset($request['maxnum']) && $request['maxnum'] != "") {
            $number = $request['maxnum'];
            $total = count($arrangeList);
            $divide_number = bcdiv($number, $total, 0);
            $last_number = bcsub($number, $divide_number * ($total - 1), 0);
            $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
            $numArray = explode("+", $number_str);
        }
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}'");
            $holidayArray = array();
            foreach ($holidayList as $val) {
                $holidayArray[] = $val['holidays_day'];
            }
        }
        $teacherList = array();
        foreach ($arrangeList as $key => $arrangeOne) {
            if (isset($request['maxnum']) && $request['maxnum'] != "") {
                $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key]);
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    $intersectDay = array_intersect($holidayArray, $weekList);
                    $addNum = count($intersectDay) + $numArray[$key];
                    $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $addNum);
                    $weekList = array_diff($weekList, $intersectDay);
                }
            } else {
                $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id']);
            }

            foreach ($weekList as $day) {
                $sql = "select ch.hour_starttime,ch.hour_endtime,s.staffer_cnname,s.staffer_branch
                  from smc_class_hour as ch
                  left join smc_class_hour_teaching as ct on ct.hour_id=ch.hour_id and ct.teaching_type=0
                  left join smc_staffer as s on s.staffer_id=ct.staffer_id
                  where {$datawhere} and ct.teaching_type=0 and ch.hour_day='{$day}' and ct.staffer_id='{$arrangeOne['staffer_id']}' limit 0,1
                  ";
                $arrange = $this->DataControl->selectOne($sql);
                $is_time_cross = $this->is_time_cross(strtotime($day . " " . $arrange['hour_starttime']), strtotime($day . " " . $arrange['hour_endtime']), strtotime($day . " " . $arrangeOne['hour_starttime']), strtotime($day . " " . $arrangeOne['hour_endtime']));

                if ($is_time_cross) {
                    $teacherList[$key]['staffer_id'] = $arrangeOne['staffer_id'];
                    $teacherList[$key]['staffer_cnname'] = $arrange['staffer_cnname'];
                    $teacherList[$key]['staffer_branch'] = $arrange['staffer_branch'];
                    $teacherList[$key]['state'] = 1;
                    break;
                } else {
                    $teacherList[$key]['staffer_id'] = $arrangeOne['staffer_id'];
                    $teacherList[$key]['staffer_cnname'] = $arrange['staffer_cnname'];
                    $teacherList[$key]['staffer_branch'] = $arrange['staffer_branch'];
                    $teacherList[$key]['state'] = 0;
                }
            }
        }

        return $teacherList;
    }

    function classChoiceClassroom($request)
    {
        $datawhere = " 1 ";
        if (isset($request['class_id']) && $request['class_id'] != '') {
            $datawhere .= " and ch.class_id <> '{$request['class_id']}'";
        }
        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        if (isset($request['maxnum']) && $request['maxnum'] != "") {
            $number = $request['maxnum'];
            $total = count($arrangeList);
            $divide_number = bcdiv($number, $total, 0);
            $last_number = bcsub($number, $divide_number * ($total - 1), 0);
            $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
            $numArray = explode("+", $number_str);
        }
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}'");
            $holidayArray = array();
            foreach ($holidayList as $val) {
                $holidayArray[] = $val['holidays_day'];
            }
        }
        $classroomArray = array();
        foreach ($arrangeList as $key => $arrangeOne) {
            if (isset($request['maxnum']) && $request['maxnum'] != "") {
                $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key]);
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    $intersectDay = array_intersect($holidayArray, $weekList);
                    $addNum = count($intersectDay) + $numArray[$key];
                    $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $addNum);
                    $weekList = array_diff($weekList, $intersectDay);
                }
            } else {
                $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id']);
            }

//
//            $sql="select c.classroom_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_day from smc_class_hour as ch
//                  left join smc_classroom as c on c.classroom_id=ch.classroom_id";
//            $classroomList=$this->DataControl->selectClear($sql);

            foreach ($weekList as $day) {

                $sql = "select ch.hour_starttime,ch.hour_endtime,c.classroom_cnname,c.classroom_branch
                  from smc_classroom as c
                  left join smc_class_hour as ch on c.classroom_id=ch.classroom_id
                  where {$datawhere} and ch.hour_day='{$day}' and ch.classroom_id='{$arrangeOne['classroom_id']}' limit 0,1
                  ";
                $arrange = $this->DataControl->selectOne($sql);

                $is_time_cross = $this->is_time_cross(strtotime($day . " " . $arrange['hour_starttime']), strtotime($day . " " . $arrange['hour_endtime']), strtotime($day . " " . $arrangeOne['hour_starttime']), strtotime($day . " " . $arrangeOne['hour_endtime']));
                if ($is_time_cross) {
                    $classroomArray[$key]['classroom_id'] = $arrangeOne['classroom_id'];
                    $classroomArray[$key]['classroom_cnname'] = $arrange['classroom_cnname'];
                    $classroomArray[$key]['classroom_branch'] = $arrange['classroom_branch'];
                    $classroomArray[$key]['state'] = 1;
                    break;
                } else {
                    $classroomArray[$key]['classroom_id'] = $arrangeOne['classroom_id'];
                    $classroomArray[$key]['classroom_cnname'] = $arrange['classroom_cnname'];
                    $classroomArray[$key]['classroom_branch'] = $arrange['classroom_branch'];
                    $classroomArray[$key]['state'] = 0;
                }
            }
        }

        return $classroomArray;
    }

    function customsort($arr, $orderby = 'desc')
    {
        $new_array = array();
        $new_sort = array();
        foreach ($arr as $key => $value) {
            $new_array[] = $value;
        }
        if ($orderby == 'asc') {
            asort($new_array);
        } else {
            arsort($new_array);
        }
        foreach ($new_array as $k => $v) {
            foreach ($arr as $key => $value) {
                if ($v == $value) {
                    $new_sort[$key] = $value;
                    unset($arr[$key]);
                    break;
                }
            }
        }
        return $new_sort;
    }

    function gettimeKey($ranks, $rank_point)
    {
//        $ranks = array(1900=>'32',1800=>'31',1700=>'30',1600=>'29',1500=>'28',1400=>'27',1300=>'26',1400=>'25',1100=>'24',1000=>'23',900 =>'22',800 =>'21',700 =>'20',600 =>'19',500 =>'18',400 =>'17',300 =>'16',200 =>'15',100 =>'14',0=>'13');
//        $rank_point = -1;

        $value = '';
        $k = 0;
        foreach ($ranks as $key => $val) {
            $k++;
            $value = $val;
            if ($rank_point >= $key) {
                break;
            } else {
                if ($k == count($ranks)) {
                    return -1;
                }
            }
        }
        return $value;
    }


    function allClassTable($request)
    {
        $datawhere = " 1 ";

        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $datawhere .= " and ch.hour_day = '{$request['fixedtime']}'";
        } else {
            $fixedtime = date("Y-m-d", time());
            $datawhere .= " and ch.hour_day = '{$fixedtime}'";
        }

        if (!isset($request['course_inclasstype']) || $request['course_inclasstype'] == '') {
            $this->error = true;
            $this->errortip = "请选择课程类型！";
            return false;
        }

        $datawhere .= " and co.course_inclasstype='{$request['course_inclasstype']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }

        $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_cnname,hour_noon,ch.hour_ischecking,ch.hour_way,c.class_id,c.class_cnname,co.course_cnname,concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname,cl.classroom_id,ch.hour_ischecking,co.course_inclasstype,co.course_openclasstype
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_code_coursetype as ct ON ct.coursetype_id=co.coursetype_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and c.class_status <> '-2' and ch.hour_ischecking <> '-1'
              order by ch.hour_starttime DESC
        ";
        $hourList = $this->DataControl->selectClear($sql);


        $room_sql = "select cl.classroom_id,cl.classroom_branch,cl.classroom_cnname 
                  from smc_classroom as cl
                  left join smc_class_hour as ch on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  where {$datawhere} and cl.school_id='{$request['school_id']}' and cl.company_id='{$request['company_id']}'
                  group by ch.classroom_id";
        $roomList = $this->DataControl->selectClear($room_sql);
        $data = array();

        if (!$roomList) {
            $room_sql = "select c.classroom_id,c.classroom_branch,c.classroom_cnname from smc_classroom as c where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' limit 0,7";
            $roomList = $this->DataControl->selectClear($room_sql);
            if (!$roomList) {
                $this->error = true;
                $this->errortip = "无数据！";
                return false;
            }
        } else {
            foreach ($roomList as $key => $roomOne) {
                $roomList[$key]['classroom_cnname'] = $this->LgStringSwitch('教室名称:' . $roomOne['classroom_cnname']);
            }
        }

        $timeArray = json_decode(stripslashes($request['noon_list']), JSON_UNESCAPED_UNICODE);

        foreach ($timeArray as &$one) {
            $one['time'] = str_replace(".", ":", $one['time']) * 100;
        }

        $noon = array_column($timeArray, 'time');
        $noon_name = array_column($timeArray, 'name');
        $tem_noon = $this->customsort(array_flip($noon));

        if (is_array($timeArray) && count($timeArray) > 0) {

            if ($hourList) {
                foreach ($hourList as $key => $val) {
                    foreach ($timeArray as $noonkey => $noonval) {
                        foreach ($roomList as $room) {

                            $hour_starttime = str_replace(".", ":", $val['hour_starttime']) * 100;

                            $num = (int)$this->gettimeKey($tem_noon, $hour_starttime);

                            if ($num < 0) {
                                continue;
                            }

                            $value = $noon[$num];

                            if ($noonval['time'] == $value) {
                                $data['a' . $noonkey]['noon_name']['noon_name'] = $noon_name[$num];
                                if ($val['classroom_id'] == $room['classroom_id']) {
                                    $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                } else {
                                    $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                }


                if (count($data) < count($noon)) {
                    $noon = json_decode(stripslashes($request['noon_list']), JSON_UNESCAPED_UNICODE);

                    $tem_noon = array_column($noon, 'name');

                    if ($tem_noon) {
                        foreach ($tem_noon as $key => $val) {
                            foreach ($roomList as $room) {
                                if (!$data['a' . $key]['noon_name']['noon_name']) {
                                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                                    $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                }
            } else {
                foreach ($noon as $key => $val) {
                    foreach ($roomList as $room) {
                        $data['a' . $key]['noon_name']['noon_name'] = $val['name'];
                        $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                    }
                }
            }
            ksort($data);

            $data = $this->get_arr($data);
            $tem_data = array_values($data);


        } else {
            $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
            if ($hourList) {
                foreach ($hourList as $key => $val) {
                    foreach ($noon as $noonkey => $noonval) {
                        foreach ($roomList as $room) {
                            if ($val['hour_noon'] == $noonkey) {
                                $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                if ($val['classroom_id'] == $room['classroom_id']) {
                                    $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                } else {
                                    $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                }

                if (count($data) < 3) {
                    $tem_data = array();
                    foreach ($data as $k => $v) {
                        $tem_data[] = $k;
                    }
                    $tem_noon = array_diff($noon, $tem_data);

                    if ($tem_noon) {
                        foreach ($tem_noon as $key => $val) {
                            foreach ($roomList as $room) {
                                $data['a' . $key]['noon_name']['noon_name'] = $val;
                                $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                            }
                        }
                    }
                }
            } else {
                foreach ($noon as $key => $val) {
                    foreach ($roomList as $room) {
                        $data['a' . $key]['noon_name']['noon_name'] = $val;
                        $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                    }
                }
            }
            $data = $this->get_arr($data);
            $data = array_values($data);


            $tem_data = array();
            if (!$data) {
                $this->error = true;
                $this->errortip = "无数据！";
                return false;
            }
            foreach ($data as $val) {
                if ($val['noon_name']['noon_name'] == 'morning') {
                    $tem_data[0] = $val;
                    $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                    $tem_data[1] = $val;
                    $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                } elseif ($val['noon_name']['noon_name'] == 'night') {
                    $tem_data[2] = $val;
                    $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                }
            }
            asort($tem_data);
        }
        $rel_data = array();
        $rel_data['roomList'] = $roomList;
        $rel_data['data'] = $tem_data;
        return $rel_data;
    }

    function classCourseTable($request)
    {
        $datawhere = " 1 ";

        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $datawhere .= " and ch.hour_day = '{$request['fixedtime']}'";
        } else {
            $fixedtime = date("Y-m-d", time());
            $datawhere .= " and ch.hour_day = '{$fixedtime}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }

        $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_cnname,hour_noon,ch.hour_ischecking,ch.hour_way,c.class_id,c.class_cnname,co.course_cnname,s.staffer_cnname,cl.classroom_id,ch.hour_ischecking
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_code_coursetype as ct ON ct.coursetype_id=co.coursetype_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
               where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}'
               and co.course_inclasstype<>2 and c.class_status <> '-2'  
               and ch.hour_ischecking <> '-1'  and ct.coursetype_isopenclass =0 and co.course_inclasstype =0
              order by ch.hour_starttime DESC
        ";
        $hourList = $this->DataControl->selectClear($sql);

        $room_sql = "select cl.classroom_id,cl.classroom_branch,cl.classroom_cnname 
                  from smc_classroom as cl
                  left join smc_class_hour as ch on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
                  left join smc_class as c on c.class_id=ch.class_id
                  where {$datawhere} and cl.school_id='{$request['school_id']}' and cl.company_id='{$request['company_id']}'
                  group by ch.classroom_id";
        $roomList = $this->DataControl->selectClear($room_sql);
        $data = array();

        if (!$roomList) {
            $room_sql = "select c.classroom_id,c.classroom_branch,c.classroom_cnname from smc_classroom as c where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' limit 0,7";
            $roomList = $this->DataControl->selectClear($room_sql);
            if (!$roomList) {
                $this->error = true;
                $this->errortip = "无数据！";
                return false;
            }
        } else {
            foreach ($roomList as $key => $roomOne) {
                $roomList[$key]['classroom_cnname'] = $this->LgStringSwitch('教室名称:' . $roomOne['classroom_cnname']);
            }
        }
        $noon = json_decode(stripslashes($request['noon_list']), JSON_UNESCAPED_UNICODE);

        if (is_array($noon) && count($noon) > 0) {

            if ($hourList) {
                foreach ($hourList as $key => $val) {
                    foreach ($noon as $noonkey => $noonval) {
                        foreach ($roomList as $room) {
                            if ($val['hour_starttime'] >= $noonval['time']) {
                                $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval['name'];
                                if ($val['classroom_id'] == $room['classroom_id']) {
                                    $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                } else {
                                    $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                }

                if (count($data) < 3) {
                    $tem_data = array();
                    foreach ($data as $k => $v) {
                        $tem_data[] = $k;
                    }
                    $tem_noon = array_diff(array_column($noon, 'name'), $tem_data);

                    if ($tem_noon) {
                        foreach ($tem_noon as $key => $val) {
                            foreach ($roomList as $room) {
                                $data['a' . $key]['noon_name']['noon_name'] = $val;
                                $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                            }
                        }
                    }
                }
            } else {
                foreach ($noon as $key => $val) {
                    foreach ($roomList as $room) {
                        $data['a' . $key]['noon_name']['noon_name'] = $val['name'];
                        $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                    }
                }
            }
            $data = $this->get_arr($data);
            $tem_data = array_values($data);


//            $tem_data = array();
//            if (!$data) {
//                $this->error = true;
//                $this->errortip = "无数据！";
//                return false;
//            }
//            foreach ($data as $val) {
//                if ($val['noon_name']['noon_name'] == 'morning') {
//                    $tem_data[0] = $val;
//                    $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
//                } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
//                    $tem_data[1] = $val;
//                    $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
//                } elseif ($val['noon_name']['noon_name'] == 'night') {
//                    $tem_data[2] = $val;
//                    $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
//                }
//            }
//            asort($tem_data);


        } else {
            $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
            if ($hourList) {
                foreach ($hourList as $key => $val) {
                    foreach ($noon as $noonkey => $noonval) {
                        foreach ($roomList as $room) {
                            if ($val['hour_noon'] == $noonkey) {
                                $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                if ($val['classroom_id'] == $room['classroom_id']) {
                                    $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                } else {
                                    $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                }

                if (count($data) < 3) {
                    $tem_data = array();
                    foreach ($data as $k => $v) {
                        $tem_data[] = $k;
                    }
                    $tem_noon = array_diff($noon, $tem_data);

                    if ($tem_noon) {
                        foreach ($tem_noon as $key => $val) {
                            foreach ($roomList as $room) {
                                $data['a' . $key]['noon_name']['noon_name'] = $val;
                                $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                            }
                        }
                    }
                }
            } else {
                foreach ($noon as $key => $val) {
                    foreach ($roomList as $room) {
                        $data['a' . $key]['noon_name']['noon_name'] = $val;
                        $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                    }
                }
            }
            $data = $this->get_arr($data);
            $data = array_values($data);


            $tem_data = array();
            if (!$data) {
                $this->error = true;
                $this->errortip = "无数据！";
                return false;
            }
            foreach ($data as $val) {
                if ($val['noon_name']['noon_name'] == 'morning') {
                    $tem_data[0] = $val;
                    $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                    $tem_data[1] = $val;
                    $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                } elseif ($val['noon_name']['noon_name'] == 'night') {
                    $tem_data[2] = $val;
                    $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                }
            }
            asort($tem_data);
        }
        $rel_data = array();
        $rel_data['roomList'] = $roomList;
        $rel_data['data'] = $tem_data;
        return $rel_data;
    }

    function classPercentage($request)
    {

        $sql = "select c.class_fullnums,
				  (select count(s.study_id)  from  smc_student_study as s where s.class_id=c.class_id and study_isreading =1 ) as study_in_num,
				  (select count(ch.hour_id)  from smc_class_hour as ch where ch.class_id =c.class_id and hour_ischecking =1 ) as class_checkinnum,
				  (select  count(ss.study_id) from smc_student_study as ss  where ss.study_isreading = 0 and ss.class_id =c.class_id ) as  study_out_num,
				  (select  count(ss.study_id) from smc_student_study as ss  where  ss.class_id =c.class_id ) as  study_allnum
 				  from smc_class as c where  c.class_id='{$request['class_id']}'";

        $classOne = $this->DataControl->selectOne($sql);

        $sql = "SELECT count(hourstudy_id) as hourstudy_num FROM smc_student_hourstudy AS ss
				WHERE ss.class_id = '{$request['class_id']}'  group by ss.student_id  HAVING hourstudy_num ='{$classOne['class_checkinnum']}'";

        $studentHour = $this->DataControl->selectClear($sql);
        if ($studentHour) {
            $studentNum = count($studentHour);
        } else {
            $studentNum = 0;
        }


        $data = array();
        if ($classOne) {
            if ($classOne['class_fullnums']) {
                $data['full_percent'] = (round($classOne['study_in_num'] / $classOne['class_fullnums'], 2) * 100) . '%';
            } else {
                $data['full_percent'] = '0.00' . '%';
            }

            if ($classOne['class_checkinnum'] && $classOne['study_in_num']) {
                $data['new_percent'] = (round($studentNum / $classOne['study_in_num'], 2) * 100) . '%';
            } else {
                $data['new_percent'] = '0.00' . '%';
            }
            if ($classOne['study_allnum'] && $classOne['study_in_num']) {
                $data['out_percent'] = (round($classOne['study_out_num'] / $classOne['study_allnum'], 2) * 100) . '%';
            } else {
                $data['out_percent'] = '0.00' . '%';
            }

            $data['class_fullnums'] = $classOne['class_fullnums'];
            $data['study_in_num'] = $classOne['study_in_num'];
        }

        return $data;
    }

    function classStuBooking($request)
    {

        $datawhere = "1";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or ch.hour_name like '%{$request['keyword']}%')";
        }

        if (isset($request['booking_checkin']) && $request['booking_checkin'] !== '') {
            $datawhere .= " and  ch.hour_ischecking = '{$request['booking_checkin']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select  DISTINCT cb.booking_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,cb.booking_checkin,staffer_cnname,staffer_enname,ch.hour_day as booking_day,ch.hour_name,concat(ch.hour_starttime,'-',ch.hour_endtime) as hour_during,ch.hour_ischecking,s.student_id,ch.hour_id,ch.hour_ischecking,cb.booking_status
				 from smc_class_booking as cb
				 left join smc_class_hour as ch ON cb.hour_id = ch.hour_id and ch.class_id =cb.class_id
				 left join smc_student as s ON s.student_id = cb.student_id
				 left join smc_class_hour_teaching as ct on ct.class_id=cb.class_id and ct.hour_id =cb.hour_id and ct.teaching_type=0
                 left join smc_staffer as sf on sf.staffer_id=ct.staffer_id
				  where  {$datawhere} and cb.class_id='{$request['class_id']}' and  cb.booking_status =0 order by hour_day DESC limit {$pagestart},{$num}";


        $BookingList = $this->DataControl->selectClear($sql);

        if (isset($request['is_count']) && $request['is_count'] !== '') {
            $num = $this->DataControl->selectClear("select cb.booking_id as num
				 from smc_class_booking as cb
				 left join smc_class_hour as ch ON cb.hour_id = ch.hour_id and ch.class_id =cb.class_id
				 left join smc_student as s ON s.student_id = cb.student_id
				 left join smc_class_hour_teaching as ct on ct.class_id=cb.class_id and ct.hour_id =cb.hour_id and  ct.teaching_type=0
                  left join smc_staffer as sf on sf.staffer_id=ct.staffer_id
				  where {$datawhere}  and cb.class_id='{$request['class_id']}' and cb.booking_status =0 GROUP  by booking_id ");
            $num = count($num);

            if ($num) {
                $data['allnum'] = $num;
            } else {
                $data['allnum'] = 0;
            }
        }

        $array_booking_status = $this->LgArraySwitch(array('-1' => '已取消', '0' => '正常', '1' => '正常'));
        if (!$BookingList) {
            return array();
        } else {
            foreach ($BookingList as &$bookingOne) {
                $bookingOne['staffer_cnname'] = $bookingOne['staffer_enname'] ? $bookingOne['staffer_cnname'] . '-' . $bookingOne['staffer_enname'] : $bookingOne['staffer_cnname'];

                if ($bookingOne['hour_ischecking'] == 1) {
                    $bookingOne['hour_ischecking_name'] = $this->LgStringSwitch("是");
                } else {
                    $bookingOne['hour_ischecking_name'] = $this->LgStringSwitch("否");
                }
                if ($bookingOne['booking_status'] == 0) {
                    $bookingOne['booking_status'] = 1;
                }
                $bookingOne['booking_status_name'] = $array_booking_status[$bookingOne['booking_status']];
            }
            $data['list'] = $BookingList;
        }
        return $data;
    }

    function addBooking($request)
    {
        $data = array();
        $data['hour_id'] = $request['hour_id'];
        $data['class_id'] = $request['class_id'];
        $data['student_id'] = $request['student_id'];
        $data['booking_createtime'] = time();
        if ($this->DataControl->getOne('smc_class_booking', "hour_id='{$request['hour_id']}' and  student_id='{$request['student_id']}' and booking_status =0 ")) {
            $this->errortip = "该学员已经预约过该课时";
            $this->error = 1;
            return false;
        }

        $sql = "select a.class_appointnum
                ,(select count(booking_id) from smc_class_booking where class_id=a.class_id and hour_id='{$request['hour_id']}'and booking_status =0) as booking_num
                from smc_class a
                where a.class_id='{$request['class_id']}' 
                and a.class_status>=0";
        $hourOne = $this->DataControl->selectOne($sql);
        if ($hourOne && $hourOne['class_appointnum'] > 0 && $hourOne['booking_num'] >= $hourOne['class_appointnum']) {
            $this->error = 1;
            $this->errortip = "该课时学员已约满，请预约其他课时";
            return false;
        }

        if ($this->DataControl->insertData("smc_class_booking", $data)) {
            $this->errortip = "新增成功";
            $this->error = 0;
            return true;
        } else {
            $this->errortip = "新增失败";
            $this->error = 1;
            return false;
        }

    }

//	多人预约
    function addStuBooking($request)
    {

        $bookinglist = json_decode(stripslashes($request['bookinglist']), true);

        $stuBoList = $this->DataControl->selectClear("select  student_id from smc_class_booking where hour_id='{$request['hour_id']}' and  class_id='{$request['class_id']}' and  booking_status=0");
        if ($stuBoList) {
            $arr_student_id = array_column($stuBoList, 'student_id');
        } else {
            $arr_student_id = array();
        }

        if ($bookinglist && is_array($bookinglist)) {
            foreach ($bookinglist as $value) {
                if (in_array($value['student_id'], $arr_student_id)) {
                    continue;
                } else {
                    $data = array();
                    $data['hour_id'] = $value['hour_id'];
                    $data['class_id'] = $value['class_id'];
                    $data['student_id'] = $value['student_id'];
                    $data['booking_createtime'] = time();
                    $this->DataControl->insertData("smc_class_booking", $data);
                }
            }
        }

        return true;
    }

//    批量预约记录
    function batchCancelBooking($request)
    {
        $request['booking_id'] = json_decode(stripslashes($request['booking_id']), true);
        if (isset($request['booking_id']) && !is_array($request['booking_id'])) {
            $request['booking_id'] = [$request['booking_id']];
        }
        foreach ($request['booking_id'] as $bookingId) {
            $bookingOne = $this->DataControl->getFieldOne("smc_class_booking", "class_id,hour_id,student_id", "booking_id='{$bookingId}'");
            $data = array();
            $data['student_id'] = $bookingOne['student_id'];
            $data['class_id'] = $bookingOne['class_id'];
            $data['hour_id'] = $bookingOne['hour_id'];
            $this->cancelBooking($data);
        }
        return true;
    }

    function cancelBooking($request)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", 'hour_ischecking', "hour_id='{$request['hour_id']}'");
        if ($hourOne['hour_ischecking'] == 1) {
            return false;
        }

        if ($booking = $this->DataControl->getOne('smc_class_booking', "class_id='{$request['class_id']}' and  hour_id ='{$request['hour_id']}' and  student_id ='{$request['student_id']}' and booking_status = 0")) {
            $booking_data = array();
            $booking_data['booking_status'] = '-1';
            $booking_data['booking_updatatime'] = time();
            if ($this->DataControl->updateData('smc_class_booking', "booking_id='{$booking['booking_id']}'", $booking_data)) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    function getClassHourName($request)
    {
        $date = $this->DataControl->selectOne("select study_beginday 
            from smc_student_study 
            where class_id = '{$request['class_id']}' 
            and student_id='{$request['student_id']}'
            order by study_beginday DESC limit 0,1");

        //当月未考勤的课时
        $sql = "select hour_id,hour_name,hour_lessontimes,concat(hour_day,'        ',hour_name) as hour_day
              from smc_class_hour 
              where hour_day >= DATE_ADD(curdate(),interval -day(curdate())+1 day) 
              and hour_day <= last_day(curdate()) 
              and hour_day >= '{$date['study_beginday']}' 
              and class_id='{$request['class_id']}' 
              and hour_ischecking = 0 
              order by hour_lessontimes";

        $list = $this->DataControl->selectClear($sql);

        return $list;
    }


    //	 获取班级排课计划列表
    function getPlanHourList($request)
    {
        $class_sql = "select c.class_stdate,co.course_classnum,c.class_type,c.father_id,
					  (select count(ch.hour_id) from  smc_class_hour  as ch where ch.class_id=c.class_id and  ch.hour_ischecking =0 ) as checking_num
					  from  smc_class as c
 					  left join smc_course as co ON co.course_id= c.course_id
 					  where c.class_id='{$request['class_id']}' ";
        $classOne = $this->DataControl->selectOne($class_sql);

        $datawhere = "ch.class_id ='{$request['class_id']}'";

        $sql = "SELECT ch.*,sf.staffer_cnname,sf.staffer_enname 
                from  smc_class_hour  as ch
				left join  smc_class_hour_teaching as ht ON ht.hour_id = ch.hour_id and ht.teaching_type=0
				left join  smc_staffer as sf ON sf.staffer_id = ht.staffer_id
				where  {$datawhere} and  ch.hour_ischecking ='0' 
                order by ch.hour_day asc,ch.hour_starttime asc";
        $hourList = $this->DataControl->selectClear($sql);

        $today = date('Y-m-d');
        if ($classOne['class_stdate'] > $today) {
            $hour_day = $classOne['class_stdate'];
        } else {
            $hour_day = $today;
        }

        $data = array();
        if ($hourList) {
            $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $data = array();
            foreach ($hourList as $key => $hourOne) {
                $data[$key]['hour_id'] = $hourOne['hour_id'];
                $data[$key]['hour_formerday'] = $hourOne['hour_formerday'];
                $data[$key]['hour_formertimes'] = $hourOne['hour_formertimes'];
                $week = date('w', strtotime($hourOne['hour_day']));
                $data[$key]['hour_formerweek'] = $this->LgStringSwitch('周' . $weekarray[$week]);
                $data[$key]['hour_day'] = $hourOne['hour_day'];
                $data[$key]['hour_starttime'] = $hourOne['hour_starttime'];
                $data[$key]['hour_endtime'] = $hourOne['hour_endtime'];
                $data[$key]['hour_iswarming_name'] = $iswarming[$hourOne['hour_iswarming']];

                if ($classOne['class_type'] == 1) {
                    $class_teach = $this->DataControl->selectOne("select   group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher  from smc_class_teach as th,smc_staffer as s where s.staffer_id=th.staffer_id and class_id='{$classOne['father_id']}' limit 0,1 ");
                    $data[$key]['staffer_cnname'] = $class_teach['cnteacher'];
                } else {
                    $data[$key]['staffer_cnname'] = $hourOne['staffer_cnname'] . ((isset($hourOne['staffer_enname']) && $hourOne['staffer_enname'] != '') ? '-' . $hourOne['staffer_enname'] : '');
                }
            }

            $hour['list'] = $data;
            $hour['info']['plan_day'] = $hour_day;
            $hour['info']['checking_num'] = $classOne['checking_num'];
        } else {
            $hour['list'] = array();
            $hour['info']['plan_day'] = "--";
            $hour['info']['checking_num'] = 0;
        }
        return $hour;
    }

    // 获取排课安排
    function getLessonPlanList($request)
    {
        $templan = array();

        //获取班级的上课时间
        if(!isset($request['plan_type']) || $request['plan_type'] == '' || $request['plan_type'] == '0'){
            $sql = "select c.*,st.staffer_cnname,st.staffer_enname,t.staffer_cnname as assistant_staffer_cnname,t.staffer_enname as assistant_staffer_enname,t.staffer_branch as assistant_staffer_branch,sc.classroom_cnname,cp.post_name
              from smc_class_lessonplan as c
              left join smc_staffer as st on st.staffer_id=c.staffer_id
              left join smc_staffer as t on t.staffer_id=c.poll_staffer_id
              left join smc_classroom as sc on sc.classroom_id = c.classroom_id
              left join gmc_staffer_postbe as sp on st.staffer_id=sp.staffer_id and sp.school_id='{$this->schoolOne['school_id']}'
              left join gmc_company_post as cp on cp.post_id=sp.post_id
              where class_id='{$request['class_id']}' and lessonplan_play =1";

        }else{
            $sql = "select c.reviewplan_way as lessonplan_way,c.reviewplan_weekno as lessonplan_weekno,c.reviewplan_week as lessonplan_week,c.reviewplan_starttime as lessonplan_starttime,c.reviewplan_endtime as lessonplan_endtime,c.reviewplan_isskipweek as lessonplan_isskipweek,c.reviewplan_isskipholidays as lessonplan_isskipholidays,c.reviewplan_createtime as lessonplan_createtime,c.staffer_id,c.classroom_id,c.poll_staffer_id,c.poll_teachtype_code,c.teachtype_code
                    ,st.staffer_cnname,st.staffer_enname,t.staffer_cnname as assistant_staffer_cnname,t.staffer_enname as assistant_staffer_enname,t.staffer_branch as assistant_staffer_branch,sc.classroom_cnname,cp.post_name
              from smc_class_reviewplan as c
              left join smc_staffer as st on st.staffer_id=c.staffer_id
              left join smc_staffer as t on t.staffer_id=c.poll_staffer_id
              left join smc_classroom as sc on sc.classroom_id = c.classroom_id
              left join gmc_staffer_postbe as sp on st.staffer_id=sp.staffer_id and sp.school_id='{$this->schoolOne['school_id']}'
              left join gmc_company_post as cp on cp.post_id=sp.post_id
              where c.class_id='{$request['class_id']}' ";
        }

        

        $classOne = $this->DataControl->getFieldOne("smc_class", "class_stdate", "class_id='{$request['class_id']}'");
        $lesson = $this->DataControl->selectClear($sql);

        if ($lesson) {
            foreach ($lesson as $value) {
                $data['weekday_id'] = $value['lessonplan_weekno'];
                $data['staffer_id'] = $value['staffer_id'];
                $data['lessonplan_week'] = $value['lessonplan_week'];
                $data['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $data['hour_way_name'] = $this->LgStringSwitch($value['lessonplan_way'] == 1 ? '线上课' : '实体课');
                $data['hour_way'] = $value['lessonplan_way'];
                if ($data['hour_way'] == 0) {
                    $data['classroom_id'] = $value['classroom_id'];
                    $data['classroom_cnname'] = $value['classroom_cnname'];

                } else {
                    $data['classroom_id'] = '';
                    $data['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $data['classroom_iscloud'] = "1";
                }
                $data['hour_starttime'] = $value['lessonplan_starttime'];
                $data['hour_endtime'] = $value['lessonplan_endtime'];
                $data['assistant_staffer_id'] = $value['poll_staffer_id'] == 0 ? '' : $value['poll_staffer_id'];
                $data['assistant_staffer_cnname'] = $value['assistant_staffer_enname'] ? $value['assistant_staffer_cnname'] . '-' . $value['assistant_staffer_enname'] : $value['assistant_staffer_cnname'];
                $data['post_name'] = $value['post_name'];
                $data['assistant_staffer_branch'] = $value['assistant_staffer_branch'];
                $data['teachtype_code'] = $value['teachtype_code'];
                $data['poll_teachtype_code'] = $value['poll_teachtype_code'];
                $data['assistant_teachtype_code'] = $value['poll_teachtype_code'];
                $data['time'] = $value['lessonplan_starttime'] . '-' . $value['lessonplan_endtime'];
                $templan[] = $data;
            }
        } else {
            $templan = array();
        }

        $data = array();
        $data['list'] = $templan;
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$request['class_id']}' and hour_ischecking<> -1", " order  by  hour_day DESC");
        $data['hour_last_day'] = $hourOne['hour_day'] == '' ? $classOne['class_stdate'] : date("Y-m-d", strtotime("+1 day", strtotime($hourOne['hour_day'])));
        $hour_num = $this->DataControl->selectOne("select count(hour_id) as  hour_num from  smc_class_hour where class_id ='{$request['class_id']}' ");
        $data['child_hournum'] = $hour_num['hour_num'];
        return $data;
    }

    function updateHour($request)
    {
        $json_note = json_decode(stripslashes($request['json_note']), true);
        $CourseModel = new CourseModel();
        if ($json_note) {
            foreach ($json_note as $key => $value) {
                $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' limit 0,1");
                $CourseModel->cancelAbsenceHour($value['hour_id'], '调整排课,取消对应的请假记录');
                $data = array();
                if ($value['classroom_id'] != 0) {
                    $data['classroom_id'] = $value['classroom_id'];
                    $data['hour_updatatime'] = time();
                    $this->DataControl->updateData("smc_class_hour", "hour_id = '{$value['hour_id']}' and class_id='{$request['class_id']}'", $data);
                }

                $teaData = array();
                $teaData['teaching_updatatime'] = time();
                if ($value['staffer_id'] != 0) {
                    //主教调整
                    $teaData['staffer_id'] = $value['staffer_id'];
                    $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type = 0");
                    if ($teaingData) {
                        $this->DataControl->updateData("smc_class_hour_teaching", "hour_id = '{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =0", $teaData);
                    } else {
                        $data = array();
                        $data['class_id'] = $hourOne['class_id'];
                        $data['hour_id'] = $hourOne['hour_id'];
                        $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                        $data['staffer_id'] = $value['staffer_id'];
                        $data['teaching_type'] = 0;
                        $data['teaching_createtime'] = time();
                        $this->DataControl->insertData("smc_class_hour_teaching", $data);
                    }
                }
                if ($value['assistant_staffer_id'] != 0) {
                    //主教调整
                    if ($value['assistant_staffer_id'] == -1) {
                        $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1  limit 0,1");
                        if ($teaingData) {
                            $teaData = array();
                            $teaData['teaching_isdel'] = '1';
                            $teaData['teaching_updatatime'] = time();
                            $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1 and teaching_id='{$teaingData['teaching_id']}'", $teaData);
                        }
                    } else {
                        $teaData = array();
                        $teaData['staffer_id'] = $value['assistant_staffer_id'];
                        $teaData['teaching_isdel'] = 0;
                        $teaData['teaching_updatatime'] = time();

                        $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1  limit 0,1");
                        if ($teaingData) {
                            $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1", $teaData);
                        } else {
                            $data = array();
                            $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' limit 0,1");
                            $data['class_id'] = $hourOne['class_id'];
                            $data['hour_id'] = $hourOne['hour_id'];
                            $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                            $data['staffer_id'] = $value['assistant_staffer_id'];
                            $data['teaching_type'] = 1;
                            $data['teaching_createtime'] = time();
                            $this->DataControl->insertData("smc_class_hour_teaching", $data);
                        }
                    }
                }
                if ($value['is_hourway'] == 1) {
                    if ($hourOne['hour_way'] == 0 && $hourOne['hour_ischecking'] == 0) {
                        $data = array();
                        $data['hour_way'] = '1';
                        $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $value['hour_id']);
                        $data['hour_updatatime'] = time();
                        $data['classroom_id'] = '0';
                        $this->DataControl->updateData("smc_class_hour", "hour_id='{$value['hour_id']}'", $data);
                    }
                }
            }
        }
        return true;
    }

    function updateClassHourAction($request)
    {
        $json_note = json_decode(stripslashes($request['json_note']), true);
        if ($json_note) {
            foreach ($json_note as $key => $value) {
                $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' limit 0,1");
                $data = array();
                if ($value['classroom_id'] != 0) {
                    $data['classroom_id'] = $value['classroom_id'];
                    $data['hour_updatatime'] = time();
                    $this->DataControl->updateData("smc_class_hour", "hour_id = '{$value['hour_id']}' and class_id='{$value['class_id']}'", $data);
                }

                $teaData = array();
                if ($value['staffer_id'] != 0) {
                    $teaData['staffer_id'] = $value['staffer_id'];
                    $teaData['teaching_updatatime'] = time();

                    $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =0");
                    if ($teaingData) {
                        $this->DataControl->updateData("smc_class_hour_teaching", "hour_id = '{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =0", $teaData);
                    } else {
                        $data = array();
                        $data['class_id'] = $hourOne['class_id'];
                        $data['hour_id'] = $hourOne['hour_id'];
                        $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                        $data['staffer_id'] = $value['staffer_id'];
                        $data['teaching_type'] = 0;
                        $data['teaching_createtime'] = time();
                        $this->DataControl->insertData("smc_class_hour_teaching", $data);
                    }
                }
                if ($value['assistant_staffer_id'] != 0) {
                    $teaData = array();
                    $teaData['staffer_id'] = $value['assistant_staffer_id'];
                    $teaData['teaching_updatatime'] = time();

                    $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =1  limit 0,1");
                    if ($teaingData) {
                        $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =1", $teaData);
                    } else {
                        $data = array();
                        $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' limit 0,1");
                        $data['class_id'] = $hourOne['class_id'];
                        $data['hour_id'] = $hourOne['hour_id'];
                        $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                        $data['staffer_id'] = $value['assistant_staffer_id'];
                        $data['teaching_type'] = 1;
                        $data['teaching_createtime'] = time();
                        $this->DataControl->insertData("smc_class_hour_teaching", $data);
                    }
                }
                if ($value['is_hourway'] == 1) {
                    if ($hourOne['hour_way'] == 0 && $hourOne['hour_ischecking'] == 0) {
                        $data = array();
                        $data['hour_way'] = '1';
                        $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $value['hour_id']);
                        $data['hour_updatatime'] = time();
                        $data['classroom_id'] = '0';
                        $this->DataControl->updateData("smc_class_hour", "hour_id='{$value['hour_id']}'", $data);
                    }
                }
            }
        }
        return true;
    }

    function endClass($request)
    {

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking ='0' and hour_iswarming='0'");

        if ($hourOne) {
            $this->error = 1;
            $this->errortip = "该班级存在未考勤的日期";
            return false;
        }

        $sql = "select courseshare_id from smc_student_courseshare where class_id='{$request['class_id']}' and courseshare_status=0 limit 0,1";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = 1;
            $this->errortip = "该班级存在未结算的学生";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne('smc_class', 'class_status,class_enddate', "class_id='{$request['class_id']}'");
        if ($classOne['class_status'] == '-1') {
            $this->error = 1;
            $this->errortip = "该班级已结班";
            return false;
        } elseif ($classOne['class_status'] == '-2') {
            $this->error = 1;
            $this->errortip = "该班级已删除";
            return false;
        }

        $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);

        $sql = "select student_id
              from smc_student_study
              where class_id='{$request['class_id']}' and study_isreading='1'";

        $studentList = $this->DataControl->selectClear($sql);

        if ($studentList) {
            foreach ($studentList as $studentOne) {
                $TransactionModel->outClass($studentOne['student_id'], $request['class_id'], 2, strtotime($classOne['class_enddate']));
            }
        }
        $data = array();
        $data['class_status'] = "-1";
//        if (isset($request['create_time']) && $request['create_time'] !== '') {
//            $data['class_enddate'] = $request['create_time'];
//        } else {
//            $data['class_enddate'] = date("Y-m-d");
//        }

        $data['class_updatatime'] = $request['create_time'] ? strtotime($request['create_time']) : time();
        if ($this->DataControl->updateData('smc_class', "class_id='{$request['class_id']}'", $data)) {
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "结班失败";
            return false;
        }
    }

    function halfEndClass($request)
    {

        $breakOne = $this->DataControl->getFieldOne("smc_class_breakoff", "breakoff_id,breakoff_status", "class_id='{$request['class_id']}'");

        $type = $this->LgArraySwitch(array("0" => "中途拆班", "1" => "期末拆班"));
        if ($breakOne) {
            if ($breakOne['breakoff_status'] >= 0) {
                $this->error = 1;
                $this->errortip = "已存在申请，不可重复申请";
                return false;
            }
            $data = array();
            $data['breakoff_type'] = $request['breakoff_type'];
            $data['breakoff_status'] = 0;
            $data['breakoff_apply_staffer_id'] = $this->stafferOne['staffer_id'];
            $data['breakoff_apply_time'] = time();

            if ($this->DataControl->updateData("smc_class_breakoff", "class_id='{$request['class_id']}'", $data)) {
                $t_data = array();
                $t_data['breakoff_id'] = $breakOne['breakoff_id'];
                $t_data['tracks_title'] = $this->LgStringSwitch("再次申请" . $type[$request['breakoff_type']]);
                $t_data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师申请' . $type[$request['breakoff_type']]);
                $t_data['tracks_note'] = $this->LgStringSwitch($request['reason']);
                $t_data['staffer_id'] = $this->stafferOne['staffer_id'];
                $t_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $t_data['tracks_time'] = time();
                $this->DataControl->insertData("smc_class_breakoff_track", $t_data);

                return true;
            } else {
                $this->error = 1;
                $this->errortip = "申请失败";
                return false;
            }
        } else {
            $data = array();
            $data['company_id'] = $this->companyOne['company_id'];
            $data['school_id'] = $this->schoolOne['school_id'];
            $data['class_id'] = $request['class_id'];
            $data['breakoff_type'] = $request['breakoff_type'];
            $data['breakoff_status'] = 0;
            $data['breakoff_apply_staffer_id'] = $this->stafferOne['staffer_id'];
            $data['breakoff_apply_time'] = time();

            $breakoff_id = $this->DataControl->insertData("smc_class_breakoff", $data);

            if ($breakoff_id) {
                $t_data = array();
                $t_data['breakoff_id'] = $breakoff_id;
                $t_data['tracks_title'] = $this->LgStringSwitch("申请" . $type[$request['breakoff_type']]);
                $t_data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师申请' . $type[$request['breakoff_type']]);
                $t_data['tracks_note'] = $this->LgStringSwitch($request['reason']);
                $t_data['staffer_id'] = $this->stafferOne['staffer_id'];
                $t_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $t_data['tracks_time'] = time();
                $this->DataControl->insertData("smc_class_breakoff_track", $t_data);

                return true;
            } else {
                $this->error = 1;
                $this->errortip = "申请失败";
                return false;
            }
        }
    }

    function classNewTimes($request)
    {

        $sql = "select hour_lessontimes from smc_class_hour where class_id='{$request['class_id']}' and hour_ischecking='0' order by hour_day asc,hour_lessontimes asc";
        $hourOne = $this->DataControl->selectOne($sql);
        if (!$hourOne) {
            $this->error = 1;
            if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking='1'")) {
                $this->errortip = "班级课程已结束";
            } else {
                $this->errortip = "班级需先排课";
            }
            return false;
        }

        return $hourOne['hour_lessontimes'];
    }

    function getClassTimes($request)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_ischecking", "class_id='{$request['class_id']}' and hour_lessontimes='{$request['hour_lessontimes']}'");

        if (!$hourOne) {
            $this->error = 1;
            $this->errortip = "请填写有效课次";
            return false;
        }
        return $hourOne['hour_day'];
    }

    function advanceClass($request)
    {
        if (!isset($request['stu_list']) || $request['stu_list'] == '') {
            $this->error = 1;
            $this->errortip = "请先选择学员";
            return false;
        }

        $stuList = json_decode(stripslashes($request['stu_list']), 1);

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_ischecking", "class_id='{$request['class_id']}' and hour_lessontimes='{$request['hour_lessontimes']}' and hour_isfree=0");
        if (!$hourOne) {
            $this->error = 1;
            $this->errortip = "请填写有效课次";
            return false;
        }
        if ($hourOne['hour_ischecking'] == '1') {
            $this->error = 1;
            $this->errortip = "提前入班无法调整到已考勤课次";
            return false;
        }
        if ($hourOne['hour_ischecking'] != '0') {
            $this->error = 1;
            $this->errortip = "请填写有效课次";
            return false;
        }

//        if ($hourOne['hour_day'] > date("Y-m-d", time())) {
//            $this->error = 1;
//            $this->errortip = "请选择今日之前的课次";
//            return false;
//        }

        foreach ($stuList as $stuOne) {
            if (!$this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "class_id='{$request['class_id']}' and student_id='{$stuOne['student_id']}'")) {
                $data = array();
                $data['study_beginday'] = $hourOne['hour_day'];
                $data['study_updatetime'] = time();
                $this->DataControl->updateData("smc_student_study", "class_id='{$request['class_id']}' and student_id='{$stuOne['student_id']}'", $data);
            }
        }
        return true;
    }

    /**
     *  确认补考勤 - 获取补考勤页面
     */
    function getStuClassCheckApi($request)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_synchro_day", "school_id='{$this->schoolOne['school_id']}'");
        $stuhourstudyList = $this->DataControl->selectClear("
            select c.hourstudy_id,ch.hour_id
            from  smc_student_hourstudy as c
            left join smc_class_hour as ch ON c.hour_id = ch.hour_id
            where c.student_id='{$request['student_id']}' and c.class_id='{$request['class_id']}'
            and ch.hour_day >= (select study_beginday from smc_student_study as s where s.student_id=c.student_id  and class_id = ch.class_id and ch.hour_iswarming = 0  limit 0,1)
          ");

        if ($stuhourstudyList) {
            $arr_hour_id = array_column($stuhourstudyList, "hour_id");
            $str_hour_id = implode(',', $arr_hour_id);
        } else {
            $str_hour_id = '0';
        }
        $datawhere = '1';
        if ($schoolOne['school_synchro_day']) {
            $schoolOne['school_synchro_day'] = date("Y-m-d", strtotime($schoolOne['school_synchro_day']));
            $datawhere .= " and  ch.hour_day > '{$schoolOne['school_synchro_day']}' ";
        }

        $classhourList = $this->DataControl->selectClear("
        select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_isfree,hour_name,
        (select hourstudy_id from smc_student_hourstudy as shy where shy.hour_id=ch.hour_id and shy.student_id ='{$request['student_id']}') as hourstudy_id,
        (select count(fc.coursetimes_id) from smc_student_free_coursetimes as fc where fc.hour_lessontimes=ch.hour_lessontimes and fc.class_id=ch.class_id and fc.student_id='{$request['student_id']}' and fc.is_use=0) as is_coursetimes_id
        from smc_class_hour  as ch
        where  ch.hour_ischecking =1 and   ch.class_id  ='{$request['class_id']}' 
        and ch.hour_day >= (select study_beginday from smc_student_study as s where s.student_id='{$request['student_id']}'  and class_id = ch.class_id  limit 0,1)
        and  {$datawhere}
        and ch.hour_day < (select study_endday from smc_student_study as s where s.student_id='{$request['student_id']}'  and class_id = ch.class_id  limit 0,1)
        and ch.hour_id  not in ({$str_hour_id})  and ch.hour_iswarming = 0
          ");

        if ($classhourList) {
            $num = count($classhourList);
            $data['num'] = $num;

            foreach ($classhourList as &$value) {
                $value['hour_ischecking_name'] = $this->LgStringSwitch($value['hourstudy_id'] == Null ? '待考勤' : '已考勤');
                $value['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $value['hour_isfree_name'] = $this->LgStringSwitch($value['hour_isfree'] == '0' ? '计费' : '免费');
                if (intval($value['is_coursetimes_id']) > 0) {
                    $value['hour_isfree_name'] = $this->LgStringSwitch('免费');
                }
            }
        } else {
            $classhourList = array();
            $data['num'] = 0;
        }

        $data['list'] = $classhourList;
        return $data;
    }

    /**
     * 期度类班级排课
     *  每天排一节
     * author: ling
     * 对应接口文档 0001
     */
    function scheduleDurationClass($request)
    {
        if (!$request['class_id']) {
            $this->error = 1;
            $this->errortip = "请选择班级!";
            return false;
        }
        $startday = date("Y-m-d", strtotime($request['startday']));
        $endday = date("Y-m-d", strtotime($request['endday']));
        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id,class_hournums,class_stdate", "class_id='{$request['class_id']}'");
        if ($classOne['class_stdate'] > $startday) {
            $this->error = 1;
            $this->errortip = "请选择开班时间之后的日期";
            return false;
        }

        if (isset($request['is_edit']) && $request['is_edit'] == 1) {
            $ischeckingOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking =1");
            if ($ischeckingOne) {
                $this->error = 1;
                $this->errortip = "存在已考勤的课次,不支持修改!";
                return false;
            } else {
                $this->DataControl->delData("smc_class_hour ", "class_id='{$request['class_id']}' and hour_ischecking <>1  ");
            }
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", 'course_id,class_enddate,class_type,father_id', "class_id='{$request['class_id']}'");

        $hourOne = $this->DataControl->selectOne("select ch.hour_day,ch.hour_id,ch.hour_lessontimes
		    from  smc_class_hour  as ch where ch.class_id='{$request['class_id']}' and hour_iswarming = 0   order by hour_day DESC  limit 0,1");
        if ($hourOne && ($hourOne['hour_day'] >= $startday)) {
            $this->error = 1;
            $this->errortip = "请从{$hourOne['hour_day']}以后的时间开始排课!";
            return false;
        }

        $courseOne = $this->DataControl->selectOne("
					select  ct.coursetype_isopenclass,course_inclasstype from  smc_class as c
					left join  smc_course as  co on c.course_id = co.course_id
				    left join  smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
 				    where c.class_id='{$request['class_id']}' ");

        if ($courseOne['course_inclasstype'] != 1) {
            $this->error = 1;
            $this->errortip = "非期度类班级,不适用该排课";
            return false;
        }
        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }
        if (count($date) < 0) {
            $this->error = 1;
            $this->errortip = "请选择正确的日期";
            return false;
        }

        // 跳过节假日

        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $holidayArray = array();
            $sql = "select ch.holidays_day from smc_code_holidays as ch
                  where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$startday}' and ch.holidays_day<='{$endday}'
                  and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='0')
                       or
                       (ch.school_id='0' and ch.holidays_status='0' and ch.company_id = '{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='1'))
                  )";

            $holidayList = $this->DataControl->selectClear($sql);
            if ($holidayList) {
                foreach ($holidayList as $holidayOne) {
                    $holidayArray[] = $holidayOne['holidays_day'];
                }
            }
            //排除假期
            $date = array_diff($date, $holidayArray);
            sort($date);
        }
        if (count($date) < 0) {
            $this->error = 1;
            $this->errortip = "请选择正确的日期";
            return false;
        }

        //设定的上课周末
        $lesson_sql = "select ch.holidays_day from smc_code_holidays as ch
                  where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$startday}' and ch.holidays_day<='{$endday}'
                  and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='1')
                       or
                       (ch.school_id='0' and ch.holidays_status='1'  and ch.company_id = '{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='0'))
                  )
                  ";
        $normalHoliday = $this->DataControl->selectClear($lesson_sql);
        if ($normalHoliday) {
            $arr_weekend = array_column($normalHoliday, 'holidays_day');
        } else {
            $arr_weekend = array();
        }
        //跳过周末
        $re_date = array();
        if (isset($request['skip_weekend']) && $request['skip_weekend'] == 1) {
            for ($i = 0; $i < count($date); $i++) {
                if ((date('w', strtotime($date[$i])) != 0 && date('w', strtotime($date[$i])) != 6) || in_array($date[$i], $arr_weekend)) {
                    $re_date[] = $date[$i];
                }
            }
        } else {
            $re_date = $date;
        }

        if ($classOne['class_type'] == 1) {
            $zhu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$classOne['father_id']}' and teach_type=0 and teach_status =0  ");
            $fu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$classOne['father_id']}' and teach_type=1 and teach_status =0  ");
        } else {
            $zhu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=0 and teach_status =0  ");
            $fu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=1 and teach_status =0  ");
        }

        $techData = array();
        $techData['class_id'] = $request['class_id'];
        $last_day = array();

        $num = 1;
        if ($hourOne['hour_lessontimes'] && $hourOne['hour_lessontimes'] > 0) {
            $num += $hourOne['hour_lessontimes'];
        }

        foreach ($re_date as $dateOne) {
            $hourData = array();
            $hourData['course_id'] = $classOne['course_id'];
            $hourData['class_id'] = $request['class_id'];
            $hourData['classroom_id'] = $request['classroom_id'];
            $hourData['hour_isfree'] = 0;
            $hourData['hour_iswarming'] = 0;
            $hourData['hour_starttime'] = $request['hour_starttime'];
            $hourData['hour_endtime'] = $request['hour_endtime'];
            $hourData['hour_createtime'] = time();
            $hourData['hour_updatatime'] = time();
            $hourData['hour_lessontimes'] = $num;
            $hourData['hour_name'] = 'Lesson' . ' ' . $hourData['hour_lessontimes'];
            $hourData['hour_day'] = $dateOne;
            $hourData['hour_formerday'] = $dateOne;
            $hourData['hour_formertimes'] = $request['hour_starttime'] . '-' . $request['hour_endtime'];
            $hour_id = $this->DataControl->insertData("smc_class_hour", $hourData);

            if ($zhu_classTeachOne) {
                $techData['hour_id'] = $hour_id;
                $techData['hour_lessontimes'] = $hourData['hour_lessontimes'];
                $techData['staffer_id'] = $zhu_classTeachOne['staffer_id'];
                $techData['teaching_type'] = '0';
                $techData['teachtype_code'] = $zhu_classTeachOne['teachtype_code'];
                $techData['teaching_createtime'] = time();
                $this->DataControl->insertData("smc_class_hour_teaching", $techData);

            }
            if ($fu_classTeachOne) {
                $techData['hour_id'] = $hour_id;
                $techData['hour_lessontimes'] = $hourData['hour_lessontimes'];
                $techData['staffer_id'] = $fu_classTeachOne['staffer_id'];
                $techData['teaching_type'] = '1';
                $techData['teachtype_code'] = $fu_classTeachOne['teachtype_code'];
                $techData['teaching_createtime'] = time();
                $this->DataControl->insertData("smc_class_hour_teaching", $techData);

            }
            $last_day = $dateOne;

            $num++;
        }

        $classData = array();
        $classData['class_enddate'] = $last_day;
        $classData['class_updatatime'] = time();
        $classData['class_hournums'] = intval(count($re_date));
        $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $classData);
        $this->error = 0;
        $this->errortip = "排课成功";
        return true;
    }

    /**
     * 增加期度类课次
     * author: ling
     * 对应接口文档 0001
     */
    function addDurationHour($request)
    {

        if (!$request['class_id']) {
            $this->error = 1;
            $this->errortip = "请选择班级!";
            return false;
        }
        if (!$request['hour_day']) {
            $this->error = 1;
            $this->errortip = "请选择日期!";
            return false;
        }
        $startday = date('Y-m-d', strtotime($request['hour_day']));
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "hour_day='{$startday}' and class_id='{$request['class_id']}' and hour_ischecking <>-1");
        if ($hourOne) {
            $this->error = 1;
            $this->errortip = "该时间已有课次!";
            return false;
        }

        $hour = $this->DataControl->getOne("smc_class_hour", "class_id='{$request['class_id']}'", "order by hour_day DESC");
        if (!$hour) {
            $this->error = 1;
            $this->errortip = "请确认是否排课";
            return false;
        } elseif ($startday <= $hour['hour_day']) {
            $this->error = 1;
            $this->errortip = "请选择{$hour['hour_day']}之后的时间";
            return false;
        }

        $hour = $this->DataControl->getOne("smc_class_hour", "class_id='{$request['class_id']}'", "order by hour_lessontimes DESC");

        $classOne = $this->DataControl->getFieldOne("smc_class", "class_hournums", "class_id='{$request['class_id']}'");
        $zhu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=0 and teach_status =0  ");
        $fu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=1 and teach_status =0  ");

        $data = array();
        $data['course_id'] = $hour['course_id'];
        $data['class_id'] = $hour['class_id'];
        $data['hour_lessontimes'] = $hour['hour_lessontimes'] + 1;
        $data['classroom_id'] = $hour['classroom_id'];
        $data['hour_name'] = 'Lesson' . ' ' . $data['hour_lessontimes'];
        $data['hour_isfree'] = 0;
        $data['hour_iswarming'] = 0;
        $data['hour_day'] = $startday;
        $data['hour_ischecking'] = 0;
        $data['hour_starttime'] = $hour['hour_starttime'];
        $data['hour_endtime'] = $hour['hour_endtime'];
        $data['hour_createtime'] = time();
        $data['hour_updatatime'] = time();
        if ($hour_id = $this->DataControl->insertData("smc_class_hour", $data)) {
            if ($zhu_classTeachOne) {
                $teachData = array();
                $teachData['class_id'] = $hour['class_id'];
                $teachData['hour_id'] = $hour_id;
                $teachData['hour_lessontimes'] = $data['hour_lessontimes'];
                $teachData['staffer_id'] = $zhu_classTeachOne['staffer_id'];
                $teachData['teaching_type'] = '0';
                $teachData['teachtype_code'] = $zhu_classTeachOne['teachtype_code'];
                $teachData['teaching_createtime'] = time();
                $this->DataControl->insertData("smc_class_hour_teaching", $teachData);
            }
            if ($fu_classTeachOne) {
                $teachData = array();
                $teachData['class_id'] = $hour['class_id'];
                $teachData['hour_id'] = $hour_id;
                $teachData['hour_lessontimes'] = $data['hour_lessontimes'];
                $teachData['staffer_id'] = $fu_classTeachOne['staffer_id'];
                $teachData['teaching_type'] = '1';
                $teachData['teachtype_code'] = $fu_classTeachOne['teachtype_code'];
                $teachData['teaching_createtime'] = time();
                $this->DataControl->insertData("smc_class_hour_teaching", $teachData);
            }


            $classData = array();
            $classData['class_enddate'] = $startday;
            $classData['class_hournums'] = $classOne['class_hournums'] + 1;
            $classData['class_updatatime'] = time();
            $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $classData);

            $this->error = 0;
            $this->errortip = "增加课次成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "增加失败";
            return false;
        }

    }

    /**
     * 获取课次详情
     * author: ling
     * 对应接口文档 0001
     */
    function getHourOneApi($request)
    {
        $hourOne = $this->DataControl->selectOne("
            select c.class_cnname,c.class_branch,ch.hour_name,m.classroom_cnname,ch.hour_id,ch.hour_name,ch.hour_cancelnote,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,
            (select group_concat(staffer_cnname) from smc_class_hour_teaching as cht,smc_staffer as st  where st.staffer_id = cht.staffer_id and  ch.hour_id=cht.hour_id) as staffer_cnname
            from smc_class_hour as ch
            left join smc_class as c On c.class_id = ch.class_id
            left join smc_classroom as m ON ch.classroom_id= m.classroom_id
            left join smc_course as co On co.course_id = ch.course_id
            where hour_id='{$request['hour_id']}'
        ");

        if (!$hourOne) {
            $hourOne = array();
        }
        return $hourOne;
    }

    /**
     *
     * author: ling
     * 对应接口文档 0001
     */
    function durationSettleList($reqeust)
    {

        if (!$reqeust['year_moth']) {
            $reqeust['year_moth'] = date('Y-m');
        }
        $datawhere = '1';
        if (isset($reqeust['keyword']) && $reqeust['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$reqeust['keyword']}%'  or s.student_branch like '%{$reqeust['keyword']}%')";
        }


        $firstday = date('Y-m-01', strtotime($reqeust['year_moth']));
        $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));


        $datawhere .= " and ss.study_isreading =1 and ss.class_id='{$reqeust['class_id']}' and ss.study_beginday <='{$lastday}' and ss.study_endday >='{$lastday}'";

        $sql = "
            select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,
            (select count(hourstudy_id) 
            from smc_class_hour as ch 
            left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id
            where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}' and shy.student_id = s.student_id
            ) as hourstudy_allnum, 
               (select count(hourstudy_id) 
            from smc_class_hour as ch 
            left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id
            where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}' and shy.student_id = s.student_id and shy.hourstudy_checkin=1
            ) as hourstudy_checknum,
            (select count(hourstudy_id) 
            from smc_class_hour as ch 
            left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id
            where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}' and shy.student_id = s.student_id and shy.hourstudy_checkin=1
            ) as hourstudy_nochecknum
            from smc_student as s 
            left join smc_student_study as ss ON ss.student_id = s.student_id
            where {$datawhere}";
        $stuList = $this->DataControl->selectClear($sql);


        if (!$stuList) {
            $data['list'] = array();
            $data['allnum'] = 0;
        } else {
            foreach ($stuList as &$value) {
                $value['hourstudy_nochecknum'] = intval($value['hourstudy_nochecknum']);
                $value['hourstudy_checknum'] = intval($value['hourstudy_checknum']);
                $value['hourstudy_allnum'] = intval($value['hourstudy_allnum']);
            }

            $all_num = $this->DataControl->selectOne(" select count(s.student_id) as stu_num from smc_student as s 
            left join smc_student_study as ss ON ss.student_id = s.student_id
            where {$datawhere}");
            $data['list'] = $stuList;
            $data['allnum'] = $all_num['stu_num'];
        }


        return $data;


    }

    /**
     *
     * author: ling
     * 对应接口文档 0001
     * @param $reqeust
     */
    function getDurationStuHourstudy($reqeust)
    {
        $firstday = date('Y-m-01', strtotime($reqeust['year_moth']));
        $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
        $info = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname,student_img,student_enname,student_sex", "student_id='{$reqeust['student_id']}'");
        $info['year_moth'] = $reqeust['year_moth'];

        $datawhere = '1';
        if (isset($reqeust['hourstudy_checkin']) && $reqeust['hourstudy_checkin'] == 1) {
            $datawhere .= " and shy.hourstudy_checkin ='{$reqeust['hourstudy_checkin']}'";
        }
        $hourList = $this->DataControl->selectClear(" 
            select ch.hour_id,shy.hourstudy_id,shy.hourstudy_checkin,scg.clockinginlog_note,ch.hour_day,ch.hour_ischecking
            from smc_class_hour as ch
            left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id and shy.student_id = '{$reqeust['student_id']}'
            left join smc_student_clockinginlog as scg ON scg.hourstudy_id = shy.hourstudy_id and shy.student_id = scg.student_id 
            where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}'  and ch.class_id='{$reqeust['class_id']}'  and {$datawhere} order by hour_day ASC
             ");
        $hour_checkinnum = 0;
        $hour_nocheckinnum = 0;
        $no_checkingnnum = 0;
        $hour_allnum = 0;
        if ($hourList) {
            foreach ($hourList as &$value) {
                $hour_allnum++;
                if ($value['hourstudy_checkin'] == 1) {
                    $hour_checkinnum++;
                } elseif ($value['hourstudy_checkin'] == 0 && $value['hour_ischecking'] == 1) {
                    $hour_nocheckinnum++;
                }
                if ($value['hour_ischecking'] == 0) {
                    $no_checkingnnum++;
                }
                if (!$value['clockinginlog_note']) {
                    $value['clockinginlog_note'] = '--';
                }
                if ($value['hourstudy_checkin'] == 1 && $value['hour_ischecking'] == 1) {
                    $value['hourstudy_checkin_name'] = $this->LgStringSwitch('出勤');
                } elseif ($value['hourstudy_checkin'] == 0 && $value['hour_ischecking'] == 1) {
                    $value['hourstudy_checkin_name'] = $this->LgStringSwitch('缺勤');
                } else {
                    $value['hourstudy_checkin_name'] = '--';
                }
            }
        } else {
            $hourList = array();
        }
        $info['hour_allnum'] = $hour_allnum;
        $info['hour_checkinnum'] = $hour_checkinnum;
        $info['hour_nocheckinnum'] = $hour_nocheckinnum;
        $info['no_checkingnnum'] = $no_checkingnnum;
        $data = array();
        $data['info'] = $info;
        $data['list'] = $hourList;
        return $data;
    }


    function settlement($request)
    {

//        $this->error = true;
//        $this->errortip = "该功能暂时关闭";
//        return false;

        $sql = "select sc.courseshare_id from smc_student_courseshare as sc
              left join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id
              left join smc_student_study as ss on ss.student_id=scb.student_id
              where ss.class_id='{$request['class_id']}' and sc.courseshare_month='{$request['month']}' and sc.courseshare_status='1'
              ";

        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "该月已经结算";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "班级不存在";
            return false;
        }

        $lastday = date('Y-m-t', time());
        if (date("Y-m-d") != $lastday && $request['month'] >= date("Y-m")) {
            $this->error = true;
            $this->errortip = "该月不可结算";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and substring(hour_day,1,7)='{$request['month']}' and hour_ischecking='0' and hour_iswarming='0'")) {
            $this->error = true;
            $this->errortip = "当月存在未考勤课次，不可结算";
            return false;
        }

        $sql = "select s.student_id,s.student_cnname,s.student_branch
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              where ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}'
              and substring(ss.study_beginday,1,7)<='{$request['month']}' and substring(ss.study_endday,1,7)>='{$request['month']}'
              group by s.student_id
              order by s.student_id ASC,ss.study_beginday DESC
        ";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        foreach ($studentList as $studentOne) {
            $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->schoolOne['school_id']}' and scb.student_id='{$studentOne['student_id']}' and scb.course_id='{$classOne['course_id']}'
              ";

            $stuCourseOne = $this->DataControl->selectOne($sql);

            $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$request['class_id']}' and courseshare_month='{$request['month']}'");//学员当月分摊金额

            if (!$monthOne) {
                $this->error = true;
                $this->errortip = $studentOne['student_cnname'] . "无分摊数据,不可结算";
                return false;
            }

            $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$request['class_id']}' and courseshare_month='{$request['month']}' and courseshare_status='1'");//学员当月分摊金额
            if ($monthOne) {
                $this->error = true;
                $this->errortip = $studentOne['student_cnname'] . "已分摊,不可结算";
                return false;
            }
        }

        $BalanceModel = new \Model\Smc\BalanceModel($request);

        foreach ($studentList as $studentOne) {
            $bool = $BalanceModel->settlement($studentOne['student_id'], $request['class_id'], $request['month']);
            if (!$bool) {
                $this->error = true;
                $this->errortip = $BalanceModel->errortip;
                return false;
            }
        }

        return true;
    }

    function getSettlementMonthList($request)
    {

        $datahaving = " 1 ";
        $datawhere = "l.school_id='{$this->schoolOne['school_id']}'";


        if (isset($request['keyword']) && $request['keyword'] != "") {
            $datawhere .= " and (l.class_cnname like '%{$request['keyword']}%' or l.class_enname like '%{$request['keyword']}%' or l.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['month']) && $request['month'] != '') {
            $datawhere .= " and substring(ch.hour_day,1,7)='{$request['month']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] != '') {
            $datawhere .= " and ch.class_id='{$request['class_id']}'";
        }

        if (isset($request['status']) && $request['status'] != '') {
            if ($request['status'] == 0) {
                $datahaving .= " and ((status1=0 and status2=0) or (notsettleNum>0))";
            } elseif ($request['status'] == 1) {
                $datahaving .= " and status1>0";
            } elseif ($request['status'] == 2) {
                $datahaving .= " and ((status1<>0 or status2<>0) and status1=0)";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select m.class_id,m.class_cnname,m.class_enname,m.class_branch
              ,(select count(h.hour_id) from smc_class_hour as h where substring(h.hour_day,1,7)=m.mon and h.hour_ischecking<>'-1' and h.class_id=m.class_id) as hourNum
              ,ifnull((select sum(cs.share_settle_price) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon limit 0,1),0) as share_settle_price
              ,ifnull((select sum(cs.share_confirm_price) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon limit 0,1),0) as share_confirm_price
              ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=0 and cs.share_from=0 limit 0,1) as status1
              ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and cs.share_from=0 limit 0,1) as status2
              ,ifnull((select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and not exists(select 1 from smc_student_courseshare as sc inner join smc_student_coursebalance as scb where sc.coursebalance_id=scb.coursebalance_id and scb.student_id=cs.student_id and sc.class_id=cs.class_id and sc.courseshare_month=cs.share_month and sc.courseshare_status=0)),0) as settleNum
              ,ifnull((select count(sc.courseshare_id) from smc_student_courseshare as sc inner join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id where sc.class_id=m.class_id and sc.courseshare_status=0 and sc.courseshare_month=m.mon),0) as notsettleNum
              ,m.mon
              from (select substring(ch.hour_day,1,7) as mon,ch.class_id,l.class_cnname,l.class_enname,l.class_branch from smc_class_hour as ch left join smc_class as l on ch.class_id=l.class_id left join smc_course as sc on l.course_id=sc.course_id where {$datawhere} and sc.course_inclasstype=1 and ch.hour_day<>'' group by l.class_id,substring(ch.hour_day,1,7)) as m
              having {$datahaving}
              order by m.class_id,m.mon asc
              ";

        if (isset($request['from']) && $request['from'] == 1) {
            $sql .= ' limit ' . $pagestart . ',' . $num;
        }

        $list = $this->DataControl->selectClear($sql);

        if (!$list) {
            $this->error = true;
            $this->errortip = "班级未排课";
            return false;
        }

        foreach ($list as &$val) {
            $val['surplus_price'] = $val['share_settle_price'] - $val['share_confirm_price'];

            if ($val['status1'] == 0 && $val['status2'] == 0) {
                $val['status_name'] = $this->LgStringSwitch('待结算');
                $val['month_status'] = 0;
            } else {
                if ($val['status1'] > 0) {
                    $val['status_name'] = $this->LgStringSwitch('结算中');
                    $val['month_status'] = 1;
                } else {
                    if ($val['notsettleNum'] > 0) {
                        $val['status_name'] = $this->LgStringSwitch('待结算');
                        $val['month_status'] = 0;
                    } else {
                        $val['status_name'] = $this->LgStringSwitch('已结算');
                        $val['month_status'] = 2;
                    }
                }
            }
        }

        $data = array();
        if (isset($request['from']) && $request['from'] == 1) {
            $count_sql = "select m.class_id
              ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=0 and cs.share_from=0 limit 0,1) as status1
              ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and cs.share_from=0 limit 0,1) as status2
              ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and not exists(select 1 from smc_student_courseshare as sc inner join smc_student_coursebalance as scb where sc.coursebalance_id=scb.coursebalance_id and scb.student_id=cs.student_id and sc.class_id=cs.class_id and sc.courseshare_month=cs.share_month and sc.courseshare_status=0)) as settleNum
              ,(select count(sc.courseshare_id) from smc_student_courseshare as sc inner join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id where sc.class_id=m.class_id and sc.courseshare_status=0 and sc.courseshare_month=m.mon) as notsettleNum
              from (select substring(ch.hour_day,1,7) as mon,ch.class_id,l.class_cnname,l.class_enname,l.class_branch from smc_class_hour as ch left join smc_class as l on ch.class_id=l.class_id left join smc_course as sc on l.course_id=sc.course_id where {$datawhere} and sc.course_inclasstype=1 and ch.hour_day<>'' group by l.class_id,substring(ch.hour_day,1,7)) as m
              having {$datahaving}
				";

            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $list;
        return $data;
    }

    function getSettlementInfo($request)
    {

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id,class_cnname,class_enname,class_branch", "class_id='{$request['class_id']}'");

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "班级不存在";
            return false;
        }

        $lastday = date('Y-m-t', time());
        if (date("Y-m-d") != $lastday && $request['month'] >= date("Y-m")) {
            $this->error = true;
            $this->errortip = "该月不可结算";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and substring(hour_day,1,7)='{$request['month']}' and hour_ischecking='0' and hour_iswarming='0'")) {
            $this->error = true;
            $this->errortip = "当月存在未考勤课次，不可结算";
            return false;
        }

        $sql = "select (select ct.stustatus_isenclass from smc_student_changelog as sc left join smc_code_stuchange as ct on ct.stuchange_code=sc.stuchange_code
                  where sc.class_id=ss.class_id and sc.student_id=ss.student_id  and ((substring(sc.changelog_day,1,7)<='{$request['month']}' and ct.stustatus_isenclass=1) or (substring(sc.changelog_day,1,7)<'{$request['month']}' and ct.stustatus_isenclass=0))
                  order by sc.changelog_day desc,sc.changelog_id desc limit 0,1) as stustatus_isenclass
              ,ifnull((select sh.hourstudy_id from smc_student_hourstudy as sh,smc_class_hour as ch where sh.hour_id=ch.hour_id and sh.student_id=ss.student_id and sh.class_id=ss.class_id and substring(ch.hour_day,1,7)='{$request['month']}' limit 0,1),0) as hourstudy_id
              ,s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,cl.course_id
              ,ifnull((select scb.coursebalance_figure from smc_student_coursebalance as scb where scb.student_id=ss.student_id and scb.school_id=ss.school_id and scb.course_id=cl.course_id limit 0,1),0) as coursebalance_figure
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_class as cl on cl.class_id=ss.class_id
              where ss.class_id='{$request['class_id']}'
              having stustatus_isenclass=1 or hourstudy_id>0
              ";


        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }

        $sql = "select
              (select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=0 and cs.share_from=0 limit 0,1) as status1
              ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and cs.share_from=0 limit 0,1) as status2
              from (select substring(ch.hour_day,1,7) as mon,ch.class_id from smc_class_hour as ch
              where substring(ch.hour_day,1,7)='{$request['month']}' and ch.class_id='{$request['class_id']}' group by substring(ch.hour_day,1,7)) as m
              order by m.mon asc
              ";

        $listOne = $this->DataControl->selectOne($sql);

        if (!$listOne) {
            $this->error = true;
            $this->errortip = "班级未排课";
            return false;
        }

        if (isset($request['is_check']) && $request['is_check'] == 1) {
            return true;
        }

        if ($listOne['status1'] == 0 && $listOne['status2'] == 0) {
            $listOne['status_name'] = $this->LgStringSwitch('待结算');
            $listOne['month_status'] = 0;
        } else {
            $sql = "select sc.courseshare_id 
                    from smc_student_courseshare as sc
                    inner join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id
                    where sc.class_id='{$request['class_id']}' and sc.courseshare_status=0 
                    and sc.courseshare_month='{$request['month']}'                        
                    group by sc.coursebalance_id";

            if ($listOne['status1'] > 0) {
                $listOne['status_name'] = $this->LgStringSwitch('结算中');
                $listOne['month_status'] = 1;
            } else {
                if ($this->DataControl->selectClear($sql)) {
                    $listOne['status_name'] = $this->LgStringSwitch('待结算');
                    $listOne['month_status'] = 0;
                } else {
                    $listOne['status_name'] = $this->LgStringSwitch('已结算');
                    $listOne['month_status'] = 2;
                }
            }
        }


        $status = $this->LgArraySwitch(array('0' => '处理中', '1' => '已完成'));
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $tem_data = array();
        foreach ($studentList as $studentOne) {
            $data = array();

            $data['student_id'] = $studentOne['student_id'];
            $data['student_cnname'] = $studentOne['student_cnname'];
            $data['student_enname'] = $studentOne['student_enname'];
            $data['student_branch'] = $studentOne['student_branch'];
            $data['student_sex'] = $studentOne['student_sex'];
            $data['coursebalance_figure'] = $studentOne['coursebalance_figure'];

            $sql = "select cs.share_id,cs.share_attend_times,cs.share_absent_times,cs.share_calc_times,cs.share_settle_price,cs.share_calc_price,cs.share_apply_price,cs.share_confirm_price,cs.share_status
                      from smc_student_class_share as cs
                      where cs.student_id='{$studentOne['student_id']}' and cs.class_id='{$request['class_id']}' and cs.share_month='{$request['month']}' 
                      and not exists(select 1 from smc_student_courseshare as sc inner join smc_student_coursebalance as scb where sc.coursebalance_id=scb.coursebalance_id and scb.student_id='{$studentOne['student_id']}' and sc.class_id='{$request['class_id']}' and sc.courseshare_month='{$request['month']}' and sc.courseshare_status=0)
                      order by cs.share_id desc
                      limit 0,1
                      ";
            $shareOne = $this->DataControl->selectOne($sql);

            if ($shareOne) {
                $data['share_id'] = $shareOne['share_id'];
                $data['attendanceNum'] = $shareOne['share_attend_times'];
                $data['absenceNum'] = $shareOne['share_absent_times'];
                $data['hourNum'] = $shareOne['share_attend_times'] + $shareOne['share_absent_times'];
                $data['price'] = $shareOne['share_confirm_price'] > 0 ? $shareOne['share_confirm_price'] : $shareOne['share_apply_price'];
                $data['num'] = $shareOne['share_calc_times'];
                $data['courseshare_price'] = $shareOne['share_settle_price'];
                $data['surplus_price'] = $shareOne['share_settle_price'] - $data['price'];
                $data['status_name'] = $status[$shareOne['share_status']];

                $data['can_settle'] = 2;
                $data['tip'] = '';
            } else {

                $bool = $BalanceModel->settlementInfo($studentOne['student_id'], $request['class_id'], $request['month']);

                if ($bool) {

                    $data['share_id'] = 0;
                    $data['attendanceNum'] = $bool['attendNum'];
                    $data['hourNum'] = $bool['hourNum'];
                    $data['absenceNum'] = $bool['absenceNum'];
                    $data['price'] = $bool['settle_price'];
                    $data['num'] = $bool['num'];
                    $data['courseshare_price'] = $bool['courseshare_price'];
                    $data['surplus_price'] = $bool['courseshare_price'] - $data['price'];
                    $data['status_name'] = $this->LgStringSwitch('待结算');

                    $data['can_settle'] = 1;
                    $data['tip'] = '';
                } else {


                    $data['share_id'] = 0;
                    $data['attendanceNum'] = '--';
                    $data['hourNum'] = '--';
                    $data['absenceNum'] = '--';
                    $data['price'] = '--';
                    $data['num'] = '--';
                    $data['courseshare_price'] = '--';
                    $data['surplus_price'] = '--';
                    $data['status_name'] = '--';

                    $data['can_settle'] = 2;
                    $data['tip'] = $BalanceModel->errortip;
                }

            }

            $tem_data[] = $data;
        }

        $classOne['month'] = $request['month'];

        $data = array();
        $data['list'] = $tem_data;
        $data['status'] = $listOne['month_status'];
        $data['classOne'] = $classOne;
        return $data;
    }

    function submitSettlement($request)
    {


        $list = json_decode(stripslashes($request['list']), 1);

        $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
        foreach ($list as $listOne) {
            $settlementInfoOne = $BalanceModel->settlementInfo($listOne['student_id'], $listOne['class_id'], $listOne['month']);

            $sql = "select co.course_monthlyset,co.course_id 
              from smc_class as cl,smc_course as co 
              where cl.course_id=co.course_id and cl.class_id='{$listOne['class_id']}'";

            $courseOne = $this->DataControl->selectOne($sql);

            if (!$courseOne) {
                $this->error = true;
                $this->errortip = "无相关课程";
                return false;
            }

            if ($settlementInfoOne) {

                $data = array();
                $data['company_id'] = $this->companyOne['company_id'];
                $data['school_id'] = $this->schoolOne['school_id'];
                $data['student_id'] = $listOne['student_id'];
                $data['class_id'] = $listOne['class_id'];
                $data['courseshare_id'] = $settlementInfoOne['courseshare_id'];
                $data['share_month'] = $listOne['month'];
                $data['share_attend_times'] = $settlementInfoOne['attendNum'];
                $data['share_absent_times'] = $settlementInfoOne['absenceNum'];
                $data['share_calc_times'] = $settlementInfoOne['num'];
                $data['share_settle_price'] = $settlementInfoOne['courseshare_price'];
                $data['share_calc_price'] = $settlementInfoOne['settle_price'];

                $data['share_apply_price'] = $listOne['apply_price'];
                $data['share_apply_staffer_id'] = $this->stafferOne['staffer_id'];
                $data['share_apply_time'] = time();

                $data['share_from'] = 0;
                $data['share_status'] = 0;

//                if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
//                    $data['share_confirm_price'] = $settlementInfoOne['settle_price'];
//                    $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
//                    $data['share_confirm_time'] = time();
//                    $data['share_status'] = 1;
//                }

                if ($courseOne['course_monthlyset'] == 0) {

                    $data['share_confirm_price'] = $listOne['apply_price'];
                    $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                    $data['share_confirm_time'] = time();
                    $data['share_status'] = 1;

                } elseif ($courseOne['course_monthlyset'] == 1) {
                    if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
                        $data['share_confirm_price'] = $listOne['apply_price'];
                        $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                        $data['share_confirm_time'] = time();
                        $data['share_status'] = 1;
                    }
                }

                $share_id = $this->DataControl->insertData("smc_student_class_share", $data);

                if ($courseOne['course_monthlyset'] == 0) {

                    $data['share_confirm_price'] = $listOne['apply_price'];
                    $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                    $data['share_confirm_time'] = time();
                    $data['share_status'] = 1;

                    $BalanceModel->settleAction($listOne['student_id'], $listOne['class_id'], $listOne['month'], $settlementInfoOne['courseshare_price'] - $listOne['apply_price'], 1, $listOne['apply_price']);

                } elseif ($courseOne['course_monthlyset'] == 1) {
                    if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
                        $data['share_confirm_price'] = $listOne['apply_price'];
                        $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                        $data['share_confirm_time'] = time();
                        $data['share_status'] = 1;
                        $BalanceModel->settleAction($listOne['student_id'], $listOne['class_id'], $listOne['month'], $settlementInfoOne['courseshare_price'] - $listOne['apply_price'], 1, $listOne['apply_price']);
                    }
                }

                $data = array();
                $data['share_id'] = $share_id;
                $data['tracks_title'] = $this->LgStringSwitch('月度结算');
                $data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师提交结算信息');
                $data['tracks_note'] = $this->LgStringSwitch($listOne['tip']);
                $data['staffer_id'] = $this->stafferOne['staffer_id'];
                $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $data['tracks_time'] = time();

                $this->DataControl->insertData("smc_student_class_share_tracks", $data);
            }
        }

        return true;
    }

    function checkSettlement($request)
    {

        $sql = "select ch.hour_day from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_isfree='0' and ch.hour_ischecking='0' and substring(ch.hour_day,1,7)='{$request['month']}' group by ch.hour_day order by ch.hour_day asc ";
        $hourList = $this->DataControl->selectClear($sql);
        if (!$hourList) {
            $hourList = array();
        }
        return $hourList;
    }

    /**
     *更新课时计划
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function updateHourPlan($request)
    {
        if (!$request['hour_id']) {
            $this->error = 1;
            $this->errortip = "请选择课次";
            return false;
        }
        $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$request['hour_id']}'");
        $zhu_teching_hour = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and  teaching_type =0");
        if (intval($zhu_teching_hour['staffer_id']) <= 0) {
            $this->error = 1;
            $this->errortip = "请先补全主教信息";
            return false;
        }
        $fu_teching_hour = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and  teaching_type =1");
        if ($hourOne) {
            $arr_week = $this->LgArraySwitch(array('1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'));
            $data = array();
            $data['class_id'] = $hourOne['class_id'];
            $data['lessonplan_play'] = 1;
            $data['lessonplan_weekno'] = date('w', strtotime($hourOne['hour_day']));
            if ($data['lessonplan_weekno'] == 0) {
                $data['lessonplan_weekno'] = 7;
            }
            $planOne = $this->DataControl->selectOne("SELECT lessonplan_id FROM smc_class_lessonplan
WHERE class_id = '{$hourOne['class_id']}' and lessonplan_weekno = '{$data['lessonplan_weekno']}' and lessonplan_starttime='{$hourOne['hour_starttime']}' and lessonplan_play =1");
            if ($planOne) {
                $this->error = 1;
                $this->errortip = "已存在该课时计划";
                return false;
            }
            $data['lessonplan_week'] = $arr_week[$data['lessonplan_weekno']];
            $data['lessonplan_play'] = 1;
            $data['staffer_id'] = $zhu_teching_hour['staffer_id'];
            $data['teachtype_code'] = $zhu_teching_hour['teachtype_code'];
            $data['poll_staffer_id'] = $fu_teching_hour['staffer_id'];
            $data['poll_teachtype_code'] = $fu_teching_hour['teachtype_code'];
            $data['classroom_id'] = $hourOne['classroom_id'];
            $data['lessonplan_way'] = $hourOne['hour_way'];
            $data['lessonplan_starttime'] = $hourOne['hour_starttime'];
            $data['lessonplan_endtime'] = $hourOne['hour_endtime'];
            $data['lessonplan_createtime'] = time();
            $id = $this->DataControl->insertData("smc_class_lessonplan", $data);
            if ($id) {
                $this->error = 0;
                $this->errortip = "更新成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "更新失败";
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = "未查询到课时";
            return false;
        }
    }

    /*
     *  创建子班
     * lujing
     */
    function createChildClass($request)
    {
//        if ($request['class_fullnums'] == '' || !isset($request['class_fullnums'])) {
//            $this->error = 1;
//            $this->errortip = "满班人数为必填项，请填写！";
//            return false;
//        }

        $p_classOne = $this->DataControl->selectOne(
            "select c.*,co.course_isopensonclass
            from smc_class as c 
            left join smc_course as co ON c.course_id = co.course_id
            where c.class_id='{$request['class_pid']}' limit 0,1");
        if (!$p_classOne) {
            $this->error = 1;
            $this->errortip = "请选择父级班级";
            return false;
        } elseif ($p_classOne['class_type'] <> 0) {
            $this->error = 1;
            $this->errortip = "请勿选择非父级班级";
            return false;
        } elseif ($p_classOne['course_isopensonclass'] == 0) {
            $this->error = 1;
            $this->errortip = "该课程不允许创建子班级";
            return false;
        }
        if ($p_classOne['class_stdate'] > $request['class_stdate']) {
            $this->error = 1;
            $this->errortip = "请勿将子班的开班时间,设置在父班之前";
            return false;
        }
        if ($p_classOne['class_fullnums'] < $request['class_fullnums']) {
            $this->error = 1;
            $this->errortip = "子班的满班人数不可以大于父班的满班人数！";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$this->schoolOne['school_id']}' and class_cnname='{$request['class_cnname']}' and class_status>=0")) {
            $this->error = 1;
            $this->errortip = "班级名称已存在";
            return false;
        }


        $child_data = array();
        if ($request['create_time'] != '') {
            $time = strtotime($request['create_time']);
        } else {
            $time = time();
        }
        $like = date("Ymd", $time);
        $classInfo = $this->DataControl->selectOne("select class_branch from smc_class where class_branch like '{$like}%' AND LENGTH(class_branch) = '14' order by class_branch DESC limit 0,1");

        if ($classInfo) {
            $child_data['class_branch'] = $classInfo['class_branch'] + 1;
        } else {
            $child_data['class_branch'] = $like . '000001';
        }
        do {
            $classInfo = $this->DataControl->selectOne("select class_branch from smc_class where class_branch like '{$like}%' AND LENGTH(class_branch) = '14' order by class_branch DESC limit 0,1");
            if ($classInfo) {
                $child_data['class_branch'] = $classInfo['class_branch'] + 1;
            } else {
                $child_data['class_branch'] = $like . '000001';
            }
        } while ($this->DataControl->getFieldOne("smc_class", "class_id", "class_branch='{$child_data['class_branch']}'"));

        $child_data['class_fullnums'] = $request['class_fullnums'];//满班人数 20201216
        $child_data['company_id'] = $p_classOne['company_id'];
        $child_data['school_id'] = $p_classOne['school_id'];
        $child_data['school_branch'] = $p_classOne['school_branch'];
        $child_data['class_type'] = '1';
        $child_data['father_id'] = $p_classOne['class_id'];
        $child_data['course_id'] = $p_classOne['course_id'];
        $child_data['from_class_id'] = $p_classOne['from_class_id'];
        $child_data['class_cnname'] = $request['class_cnname'];
        $child_data['class_enname'] = $request['class_enname'];
        $child_data['class_appointnum'] = $request['class_appointnum'];
        $child_data['class_stdate'] = $request['class_stdate'];
        if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
            if ($request['class_stdate'] <= date("Y-m-d", time())) {
                $child_data['class_status'] = 1;
            }
        }
        $child_data['class_enddate'] = $p_classOne['class_enddate'];
        $child_data['class_createtime'] = time();
        if ($id = $this->DataControl->insertData('smc_class', $child_data)) {
            $this->error = 0;
            $this->errortip = "创建子班级成功";
            return $id;
        } else {
            $this->error = 1;
            $this->errortip = "创建失败";
            return false;
        }
    }

    /**
     * 编辑子班级
     * author: ling
     * 对应接口文档 0001
     * @param $request
     *
     */
    function editChildClass($request)
    {
//        if ($request['class_fullnums'] == '' || !isset($request['class_fullnums'])) {
//            $this->error = 1;
//            $this->errortip = "满班人数为必填项，请填写！";
//            return false;
//        }
        if ($this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$this->schoolOne['school_id']}' and class_cnname='{$request['class_cnname']}' and class_status>=0 and class_id<>'{$request['class_id']}'")) {
            $this->error = 1;
            $this->errortip = "班级名称已存在";
            return false;
        }

        $p_classOne = $this->DataControl->selectOne(
            "select t.class_stdate,t.class_fullnums  
            from smc_class as c 
            LEFT JOIN smc_class as t ON c.father_id = t.class_id 
            where c.class_id='{$request['class_id']}' limit 0,1");
        if ($p_classOne['class_stdate'] > $request['class_stdate']) {
            $this->error = 1;
            $this->errortip = "请勿将子班的开班时间,设置在父班之前";
            return false;
        }
        if ($p_classOne['class_fullnums'] < $request['class_fullnums']) {
            $this->error = 1;
            $this->errortip = "子班的满班人数不可以大于父班的满班人数！";
            return false;
        }

        $child_data = array();
        $child_data['class_fullnums'] = $request['class_fullnums'];
        $child_data['class_cnname'] = $request['class_cnname'];
        $child_data['class_enname'] = $request['class_enname'];
        $child_data['class_stdate'] = $request['class_stdate'];
        $child_data['class_appointnum'] = $request['class_appointnum'];
        if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
            if ($request['class_stdate'] <= date("Y-m-d", time())) {
                $child_data['class_status'] = 1;
            } else {
                $child_data['class_status'] = 0;
            }
        }
        $child_data['class_updatatime'] = time();
        $this->DataControl->updateData('smc_class', "class_id='{$request['class_id']}'", $child_data);
        $this->error = 0;
        $this->errortip = '编辑成功';
        return true;

    }


    /**
     * 获取子班级的可以带课的教师
     *  可以获取到整个学校的人
     * author: ling
     * 对应接口文档 0001
     */
    function getChildClassTeacher($request)
    {

        $datawhere = '1';
        if (isset($request['main_staffer_id']) && $request['main_staffer_id'] !== '') {
            $datawhere .= " and  s.staffer_id<>{$request['main_staffer_id']}";
        }
        $teacherList = $this->DataControl->selectClear(
            " 
                select s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch,cp.post_name
                from smc_staffer as s
                left join gmc_staffer_postbe as sp on s.staffer_id=sp.staffer_id
                left join gmc_company_post as cp on cp.post_id=sp.post_id
                where 1 and sp.company_id='{$request['company_id']}' and sp.school_id='{$request['school_id']}' and cp.post_isteaching=1 and s.staffer_leave = 0 and {$datawhere}
                group by s.staffer_id
               ");
        if (!$teacherList) {
            $teacherList = array();
        } else {
            foreach ($teacherList as &$teacherOne) {
                $teacherOne['staffer_cnname'] = $teacherOne['staffer_enname'] ? $teacherOne['staffer_cnname'] . '-' . $teacherOne['staffer_enname'] : $teacherOne['staffer_cnname'];
            }
        }
        return $teacherList;
    }

    /**
     * 获取子班的上课方式
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/25 0025
     */
    function getChildClassWayApi($request)
    {
        $classOne = $this->DataControl->selectOne("select co.course_opensonmode, cl.class_type from smc_course as co,smc_class as cl where co.course_id=cl.course_id and cl.class_id='{$request['class_id']}' ");
        $arr_opensonmode = array();
        if ($classOne['class_type'] == 1) {
            if ($classOne['course_opensonmode'] == 1) {
                $arr_opensonmode[0]['label'] = "实体课";
                $arr_opensonmode[0]['value'] = "0";
            } elseif ($classOne['course_opensonmode'] == 2) {
                $arr_opensonmode[0]['label'] = "线上课";
                $arr_opensonmode[0]['value'] = "1";
            } else {
                $arr_opensonmode[0]['label'] = "实体课";
                $arr_opensonmode[0]['value'] = "0";
                $arr_opensonmode[1]['label'] = "线上课";
                $arr_opensonmode[1]['value'] = "1";
            }
        } else {
            $arr_opensonmode[0]['label'] = "实体课";
            $arr_opensonmode[0]['value'] = "0";
            $arr_opensonmode[1]['label'] = "线上课";
            $arr_opensonmode[1]['value'] = "1";
        }
        $data = array();
        $data['data'] = $classOne;
        $data['list'] = $arr_opensonmode;
        return $data;
    }

    /**
     * 获取父级班级的子班列表
     *  可以获取到整个学校的人
     * author: ling
     * 对应接口文档 0001
     */
    function getClassChildCLass($request)
    {
        $datawhere = "cs.class_status <> '-2'";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cs.class_cnname like '%{$request['keyword']}%' or cs.class_enname like '%{$request['keyword']}%' or cs.class_branch like '%{$request['keyword']}%')  ";
        }
        $having = '1=1';
        if (isset($request['is_addhour']) && $request['is_addhour'] == '1') {
            $having .= " and hour_count > 0 ";
        } elseif (isset($request['is_addhour']) && $request['is_addhour'] == '0') {
            $having .= " and hour_count = 0 ";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $classList = $this->DataControl->selectClear(
            "
                select cs.*,c.course_opensonmode,
                 (select count(hour_id) from smc_class_hour as h where h.class_id = cs.class_id ) as  hour_count,
                 (select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=cs.class_id and ss.school_id=cs.school_id and ss.study_isreading='1' limit 0,1) as student_num,
                 (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=cs.class_id and ssch.hour_ischecking=1 and ssch.hour_iswarming = 0 ) as hournum,
                 (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=cs.class_id and  hou.hour_iswarming = 0   ) as is_schedule,
                 (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=cs.class_id and ssch.hour_ischecking=0 and ssch.hour_iswarming = 0 ) as no_hournum,
                 (select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) from smc_class_teach as t left join smc_staffer as s on s.staffer_id=t.staffer_id where (t.class_id=cs.class_id or t.class_id = cs.father_id) and t.teach_type=0 and t.teach_status =0 and s.staffer_leave=0) as cnteacher
                 from smc_class as cs 
                 left join smc_course as c ON cs.course_id=c.course_id
                 where cs.father_id = '{$request['class_id']}'  and {$datawhere}  
                 Having  {$having}
                 order by cs.class_createtime DESC 
                 limit {$pagestart},{$num}
                
            ");

        if (!$classList || $request['class_id'] <= 0) {
            $classList = array();
            $all_num['all_num'] = 0;
        } else {

            foreach ($classList as &$classOne) {
                $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname
FROM smc_class_lessonplan AS l
LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
WHERE l.class_id = '{$classOne['class_id']}'  limit 0,2");
                $cnteacher = array();
                $classroom = array();
                $classtimestr = "";
                if ($classlessonList) {
                    foreach ($classlessonList as $classlessonOne) {

                        $cnteacher[] = $classlessonOne['staffer_cnname'];
                        $classroom[] = $classlessonOne['classroom_cnname'];
                        $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                    }
                    $cnteacher = implode(" ", array_unique($cnteacher));
//                    $classroom = implode(" ", array_unique($classroom));
                }
                if ($cnteacher) {
                    $data['cnteacher'] = $cnteacher;
                } else {
                    $data['cnteacher'] = '';
                }
                $data['class_timestr'] = $classtimestr;
                if ($classOne['hournum'] > 0) {
                    if ($classOne['is_schedule']) {
                        $classOne['info'] = $classOne['hournum'] . '/' . $classOne['is_schedule'];
                    } else {
                        $classOne['info'] = $classOne['hournum'] . '/' . ($classOne['course_classnum'] + $classOne['course_presentednums']);
                    }
                } else {
                    $classOne['info'] = '0/' . $classOne['is_schedule'];
                }

                if ($classOne['hournum'] >= $classOne['is_schedule'] and $classOne['is_schedule'] > 0) {
                    $classOne['can_promotion'] = 1;  //已排课且   考勤的课次大于排课课次
                } else {
                    $classOne['can_promotion'] = 0;
                }
                if ($classOne['is_schedule'] > 0 and $classOne['no_hournum'] > 0 and $classOne['course_schedule'] == 0) {
                    $classOne['edit_schedule'] = 1;  //可以
                } else {
                    $classOne['edit_schedule'] = 0;
                }

                if ($classOne['is_schedule'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                    $classOne['add_schedule'] = 1;  //是否增加排课
                } else {
                    $classOne['add_schedule'] = 0;
                }

                $classOne['is_schedule'] = intval($classOne['is_schedule']);

                $classStatus = $this->LgArraySwitch(array("0" => "待开班", '1' => "进行中", "-1" => "已结束", "-2" => "已删除"));
                $classOne['class_staus_namme'] = $classStatus[$classOne['class_status']];

                //是否可以清除排课
                if (intval($classOne['hournum']) == 0 && $classOne['is_schedule'] > 0) {
                    $classOne['is_del_hour'] = 1;
                } else {
                    $classOne['is_del_hour'] = 0;
                }
            }
            $count_ClassList = $this->DataControl->selectClear(" 
                select cs.*,
                 (select count(hour_id) from smc_class_hour as h where h.class_id = cs.class_id ) as  hour_count
                 from smc_class as cs where cs.father_id = '{$request['class_id']}'  and {$datawhere}  
                 Having  {$having}
                 order by cs.class_createtime DESC 
                  ");
            $all_num['all_num'] = count($count_ClassList);
        }
        $data = array();
        $data['list'] = $classList;
        $data['allnum'] = $all_num['all_num'];
        return $data;
    }

    function setFictitious($request)
    {

        $classOne = $this->DataControl->getFieldOne("smc_class", "class_isfictitious", "class_id='{$request['class_id']}'");

        if (!$classOne) {
            $this->error = 1;
            $this->errortip = "班级不存在";
            return false;
        }
        $data = array();
        $data['class_isfictitious'] = $classOne['class_isfictitious'] == 0 ? 1 : 0;
        $data['class_updatatime'] = time();
        $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $data);

        return true;
    }

    /**
     *  获取可以转入的班级
     */

    function getClassChildCLassApi($request)
    {
        $childClassOne = $this->DataControl->getFieldOne('smc_class', "father_id", "class_id='{$request['class_id']}'");

        if ($childClassOne && $childClassOne['father_id'] > 0) {
            $classList = $this->DataControl->selectClear("select class_id ,class_cnname,class_branch from smc_class where class_id <> '{$request['class_id']}' and  father_id='{$childClassOne['father_id']}' and class_status>=0");
        }
        if (!$classList) {
            $classList = array();
        }
        return $classList;
    }

    /***
     *  创建云教室号
     * author: ling
     * 对应接口文档 000
     *
     */
    function createHourNumber($class_id, $hour_id = 0)
    {
        if ($hour_id !== 0) {
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_starttime,hour_endtime,hour_name,hour_lessontimes", "hour_id='{$hour_id}'");
        } else {
            $hourOne = array();
        }

        $arr_initials = range('A', 'Z');
        $initials = $arr_initials[mt_rand(0, count($arr_initials) - 1)];
        $hour_num = date("ymd");
        $hourNumberOne = $this->DataControl->selectOne("select linerooms_hourno from smc_linerooms where linerooms_hourno like '{$hour_num}%'  order by linerooms_hourno DESC limit 0,1");
        if ($hourNumberOne) {
            $hour_num = $hourNumberOne['linerooms_hourno'] + 1;
        } else {
            $hour_num = $hour_num . '00000001';
        }
        do {
            $hourNumberOne = $this->DataControl->selectOne("select linerooms_hourno from smc_linerooms where linerooms_hourno like '{$hour_num}%'  order by linerooms_hourno DESC limit 0,1");
            if ($hourNumberOne) {
                $hour_num = $hourNumberOne['linerooms_hourno'] + 1;
            }
        } while ($this->DataControl->getFieldOne("smc_linerooms", "linerooms_hourno", "linerooms_hourno='{$hour_num}'"));

        if ($hour_num) {
            $data_hour_num = array();
            $data_hour_num['linerooms_hourno'] = $hour_num;
            $data_hour_num['linerooms_number'] = $initials . $hour_num;
            $data_hour_num['company_id'] = $this->companyOne['company_id'];
            $data_hour_num['school_id'] = $this->schoolOne['school_id'];
            $data_hour_num['class_id'] = $class_id;
            $data_hour_num['hour_id'] = $hour_id;
            $data_hour_num['hour_lessontimes'] = $hourOne['hour_lessontimes'];
            $data_hour_num['linerooms_name'] = $hourOne['hour_name'];
            $data_hour_num['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
            $data_hour_num['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
            $data_hour_num['linerooms_createtime'] = time();
            $this->DataControl->insertData("smc_linerooms", $data_hour_num);
            return $data_hour_num['linerooms_number'];
        } else {
            return '';
        }
    }


    /**
     *  清除子班排课
     */
    public function delClassHour($request)
    {
        if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking =1")) {
            $this->error = 1;
            $this->errortip = "存在考勤的课次.不支持删除";
            return false;
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_type", "class_id='{$request['class_id']}'");

        if ($classOne['class_type'] == 1) {
            $hour_data = array();
            $hour_data['hour_ischecking'] = '-1';
            $hour_data['hour_updatatime'] = time();
            $this->DataControl->updateData("smc_class_hour", "class_id='{$request['class_id']}'", $hour_data);
            $teach_data = array();
            $teach_data['teaching_isdel'] = '1';
            $teach_data['teaching_updatatime'] = time();
            $this->DataControl->updateData("smc_class_hour_teaching", "class_id='{$request['class_id']}'", $teach_data);
            $this->DataControl->delData("smc_class_lessonplan", "class_id='{$request['class_id']}'");
            $this->error = 0;
            $this->errortip = "清除成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "此功能暂时只能子班使用";
            return false;
        }
    }

    /**
     *  实体课转为线上课
     */
    function toHourWay($request)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_way,class_id,hour_ischecking", "hour_id='{$request['hour_id']}'");
//        if ($hourOne['hour_ischecking'] == 1) {
//            $this->error = 1;
//            $this->errortip = "该课时已考勤";
//            return false;
//        }
        if ($hourOne['hour_way'] == 0) {
            $data['hour_way'] = '1';
            $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $request['hour_id']);
            $data['hour_updatatime'] = time();
            $data['classroom_id'] = '0';
            $this->DataControl->updateData("smc_class_hour", "hour_id='{$request['hour_id']}'", $data);
            $this->error = 0;
            $this->errortip = "修改成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "该课时已是线上课";
            return false;
        }
    }

    function toClassHourWayAction($request)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($request['json_note']), true);
        foreach ($schoolList as $item) {
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_way,class_id,hour_ischecking", "hour_id='{$item['hour_id']}'");
            if ($hourOne['hour_ischecking'] == 1) {
                ajax_return(array('error' => 1, 'errortip' => "该课时已考勤"), $this->companyOne['company_language']);
            }
            if ($hourOne['hour_way'] == 0) {
                $data['hour_way'] = '1';
                $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $item['hour_id']);
                $data['hour_updatatime'] = time();
                $data['classroom_id'] = '0';
                $this->DataControl->updateData("smc_class_hour", "hour_id='{$item['hour_id']}'", $data);
            }
        }
        $res = array('error' => '0', 'errortip' => "实体课转为线上课成功", 'result' => array());
        return $res;
    }


    //学校班级名称预设 -- 列表
    function getCoursePresupList($request)
    {
        $datawhere = " p.company_id = '{$request['company_id']}' and p.school_id = '{$request['school_id']}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (p.presup_name like '%{$request['keyword']}%')  ";
        }
        if (isset($request['presup_type']) && $request['presup_type'] != '') {
            $datawhere .= " and p.presup_type = '{$request['presup_type']}' ";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $dataList = $this->DataControl->selectClear("select p.presup_id,p.presup_type,p.presup_name 
                      from smc_course_presup as p  
                      WHERE {$datawhere} 
                      order by p.presup_id DESC 
                      limit {$pagestart},{$num} ");

        if ($dataList) {
            foreach ($dataList as &$dataVar) {
                $presup_type = $this->LgArraySwitch(array("0" => "未知", '1' => "班级名称", "2" => "班级别名"));
                $dataVar['presup_type_namme'] = $presup_type[$dataVar['presup_type']];
            }
        }

        if (isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(p.presup_id) as datanum 
                      from smc_course_presup as p  
                      WHERE {$datawhere} ");
            $count = $all_num['datanum'] + 0;
        } else {
            $count = 0;
        }

        $result = array();
        $result['list'] = $dataList;
        $result['allnum'] = $count;

        if ($dataList) {
            $this->error = 0;
            $this->errortip = "预设班级获取成功";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无预设班级信息";
            $this->result = $result;
            return false;
        }
    }

    //学校班级名称预设 -- 添加
    function addCoursePresupAction($request)
    {
        if (!isset($request['presup_type']) || $request['presup_type'] == '') {
            $this->error = 1;
            $this->errortip = "名称类型不能为空！";
            $this->result = array();
            return false;
        }

        $presup_name = array();
        if (!isset($request['presup_namestr']) || $request['presup_namestr'] == '') {
            $this->error = 1;
            $this->errortip = "班级名称不能为空！";
            $this->result = array();
            return false;
        } else {
            $presup_name = json_decode(stripslashes($request['presup_namestr']), true);
        }
        $presupid = 0;
        if ($presup_name) {
            foreach ($presup_name as $presup_namevar) {
                if (!$this->DataControl->getFieldOne("smc_course_presup", "presup_id", "company_id='{$this->companyOne['company_id']}' and school_id='{$this->schoolOne['school_id']}' and presup_type='{$request['presup_type']}' and presup_name='{$presup_namevar['presup_name']}'")) {
                    $data = array();
                    $data['company_id'] = $request['company_id'];
                    $data['school_id'] = $request['school_id'];
                    $data['presup_type'] = $request['presup_type'];
                    $data['presup_name'] = $presup_namevar['presup_name'];
                    $data['presup_createtime'] = time();
                    $this->DataControl->insertData('smc_course_presup', $data);
                } else {
                    $presupid++;
                }
            }
        }
        if ($presupid == 0) {
            $this->error = 0;
            $this->errortip = "添加成功！";
            $this->result = array();
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "失败{$presupid}条，名称已存在！";
            $this->result = array();
            return false;
        }
    }

    //学校班级名称预设 -- 查看
    function getCoursePresupOneApi($request)
    {
        if (!isset($request['presup_id']) || $request['presup_id'] == '') {
            $this->error = 1;
            $this->errortip = "学校课程预设ID不能为空！";
            $this->result = array();
            return false;
        }

        $presupOne = $this->DataControl->selectOne("select presup_type,presup_name from smc_course_presup WHERE company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and presup_id='{$request['presup_id']}' ");

        $presup_type = $this->LgArraySwitch(array("0" => "未知", '1' => "班级名称", "2" => "班级别名"));
        $presupOne['presup_typename'] = $presup_type[$presupOne['presup_type']];

        if ($presupOne) {
            $this->error = 0;
            $this->errortip = "预设班级名称获取成功！";
            $this->result = $presupOne;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "预设班级名称获取失败！";
            $this->result = array();
            return false;
        }
    }

    //学校班级名称预设 -- 编辑
    function editCoursePresupAction($request)
    {
        if (!isset($request['presup_id']) || $request['presup_id'] == '') {
            $this->error = 1;
            $this->errortip = "学校课程预设ID不能为空！";
            $this->result = array();
            return false;
        }

        $presupOne = $this->DataControl->getFieldOne("smc_course_presup", "presup_id", "company_id='{$this->companyOne['company_id']}' and school_id='{$this->schoolOne['school_id']}' and presup_type='{$request['presup_type']}' and presup_name='{$request['presup_name']}' and presup_id<>'{$request['presup_id']}'");

        if ($presupOne) {
            $this->error = 1;
            $this->errortip = "名称已存在！";
            $this->result = array();
            return false;
        }

        $data = array();
        if (isset($request['presup_type']) && $request['presup_type'] != '') {
            $data['presup_type'] = $request['presup_type'];
        }
        if (isset($request['presup_name']) && $request['presup_name'] != '') {
            $data['presup_name'] = $request['presup_name'];
        }

        $presupid = $this->DataControl->updateData('smc_course_presup', "presup_id='{$request['presup_id']}' and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' ", $data);
        $data['presup_id'] = $request['presup_id'];

        if ($presupid) {
            $this->error = 0;
            $this->errortip = "编辑成功！";
            $this->result = $data;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "编辑失败！";
            $this->result = array();
            return false;
        }
    }

    //学校班级名称预设 -- 删除
    function delCoursePresupAction($request)
    {
        if (!isset($request['presup_id']) || $request['presup_id'] == '') {
            $this->error = 1;
            $this->errortip = "学校课程预设ID不能为空！";
            $this->result = array();
            return false;
        }
        $presupid = $this->DataControl->delData('smc_course_presup', "presup_id='{$request['presup_id']}' and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' ");

        if ($presupid) {
            $this->error = 0;
            $this->errortip = "删除成功！";
            $this->result = array();
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "删除失败！";
            $this->result = array();
            return false;
        }
    }


    function ImportStudent($request, $sqlarray)
    {
        if (!$sqlarray) {
            $this->error = true;
            $this->errortip = "请确认文件是否存在数据";
            return false;
        }
        $t_num = 0;
        $f_num = 0;
        $tem_array = array();

        foreach ($sqlarray as &$one) {
            $one['student_cnname'] = addslashes(trim($one['student_cnname']));
            $one['student_enname'] = addslashes(trim($one['student_enname']));
            $one['student_sex'] = addslashes(trim($one['student_sex']));
            if ($one['student_birthday']) {
                $one['student_birthday'] = addslashes(trim(date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($one['student_birthday']))));
            }

            $one['parenter_cnname'] = addslashes(trim($one['parenter_cnname']));
            $one['parenter_mobile'] = addslashes(trim($one['parenter_mobile']));
            $one['family_relation'] = addslashes(trim($one['family_relation']));
            $one['student_idcard'] = addslashes(trim($one['student_idcard']));
            $one['class_branch'] = addslashes(trim($one['class_branch']));
            if ($one['study_beginday']) {
                $one['study_beginday'] = addslashes(trim(date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($one['study_beginday']))));
            }

        }

        foreach ($sqlarray as $sqlone) {
            $bool = false;
            $data = $sqlone;
            if ($sqlone['student_cnname'] && $sqlone['student_sex'] && $sqlone['parenter_mobile'] && $sqlone['student_birthday']) {

                if ($sqlone['student_sex'] != '男' && $sqlone['student_sex'] != '女') {
                    $bool = true;
                    $data['reason'] = $this->LgStringSwitch('性别必须是男或女');
                    $f_num += 1;
                } else {

                    if ($this->checkMobile($sqlone['parenter_mobile'])) {
                        if (preg_match("/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/", $sqlone['student_birthday'], $parts)) {
                            if (checkdate($parts[2], $parts[3], $parts[1])) {

                                $sql = "select s.student_id 
                                      from smc_student_family as sf,smc_parenter as p,smc_student as s 
                                      where sf.parenter_id=p.parenter_id and sf.student_id=s.student_id
                                      and s.student_cnname='{$sqlone['student_cnname']}' and p.parenter_mobile='{$sqlone['parenter_mobile']}'
                                        ";
                                $studentOne = $this->DataControl->selectOne($sql);

                                if (!$studentOne) {
                                    $like = date("Ymd", time());
                                    $stuInfo = $this->DataControl->selectOne("select student_branch from smc_student where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
                                    if ($stuInfo) {
                                        $student_branch = number_format($stuInfo['student_branch'] + 1, 0, '', '');
                                    } else {
                                        $student_branch = $like . '000001';
                                    }

                                    $s_data = array();
                                    $s_data['company_id'] = $this->companyOne['company_id'];
                                    $s_data['student_cnname'] = $sqlone['student_cnname'];
                                    $s_data['student_enname'] = $sqlone['student_enname'];
                                    $s_data['student_birthday'] = $sqlone['student_birthday'];
                                    $s_data['student_idcard'] = $sqlone['student_idcard'];
                                    $s_data['student_sex'] = $sqlone['student_sex'];
                                    $s_data['student_branch'] = $student_branch;
                                    $s_data['student_createtime'] = time();
                                    $s_data['student_updatatime'] = time();
                                    $student_id = $this->DataControl->insertData("smc_student", $s_data);

                                } else {
                                    $student_id = $studentOne['student_id'];
                                }

                                if ($student_id > 0) {
                                    if ($sqlone['parenter_cnname'] || $sqlone['parenter_mobile']) {
                                        $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$sqlone['parenter_mobile']}'");

                                        if ($parenterOne) {
                                            $p_data = array();
                                            $p_data['parenter_cnname'] = $sqlone['parenter_cnname'];
                                            $this->DataControl->updateData("smc_parenter", "parenter_id='{$parenterOne['parenter_id']}'", $p_data);
                                            $parenter_id = $parenterOne['parenter_id'];
                                        } else {
                                            $p_data = array();
                                            $p_data['parenter_cnname'] = $sqlone['parenter_cnname'];
                                            $p_data['parenter_mobile'] = $sqlone['parenter_mobile'];
                                            $p_data['parenter_addtime'] = time();
                                            $parenter_id = $this->DataControl->insertData("smc_parenter", $p_data);
                                        }

                                        if (!$this->DataControl->getFieldOne("smc_student_family", "family_id", "student_id='{$student_id}' and parenter_id='{$parenter_id}'")) {

                                            $p_data = array();
                                            $p_data['student_id'] = $student_id;
                                            $p_data['parenter_id'] = $parenter_id;
                                            $p_data['family_relation'] = $sqlone['family_relation'];
                                            $p_data['family_mobile'] = $sqlone['parenter_mobile'];
                                            $p_data['family_cnname'] = $sqlone['parenter_cnname'];
                                            $p_data['family_isdefault'] = 1;
                                            $this->DataControl->insertData("smc_student_family", $p_data);

                                        }

                                    }

                                    $Model = new \Model\Smc\TransactionModel($request);

                                    if (!$this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "student_id='{$student_id}' and school_id='{$this->schoolOne['school_id']}' and enrolled_status>=0 and enrolled_status<2")) {
                                        $Model->entrySchool($student_id);
                                    }

                                    if (isset($sqlone['class_branch']) && $sqlone['class_branch'] != '') {
                                        $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,course_id", "class_branch='{$sqlone['class_branch']}' and school_id='{$this->schoolOne['school_id']}'");

                                        if ($classOne) {
                                            $sql = "select ss.study_id from smc_student_study as ss,smc_class as c 
                                                  where ss.class_id=c.class_id and c.course_id='{$classOne['course_id']}' 
                                                  and ss.student_id='{$student_id}' and ss.study_isreading='1' ";

                                            if ($this->DataControl->selectOne($sql)) {
                                                $bool = true;
                                                $data['reason'] = $this->LgStringSwitch('学生已在该课程就读');
                                                $f_num += 1;
                                            } else {
                                                $Model->entryClass($student_id, '', $classOne['class_id'], $sqlone['study_beginday']);

                                                $t_num += 1;
                                            }


                                        } else {
                                            $bool = true;
                                            $data['reason'] = $this->LgStringSwitch('该班级不存在');
                                            $f_num += 1;
                                        }
                                    } else {
                                        $t_num += 1;
                                    }
                                } else {
                                    $bool = true;
                                    $data['reason'] = $this->LgStringSwitch('数据库错误');
                                    $f_num += 1;
                                }
                            } else {
                                $bool = true;
                                $data['reason'] = $this->LgStringSwitch('出生日期格式不正确,格式如2018-01-01');
                                $f_num += 1;
                            }
                        } else {
                            $bool = true;
                            $data['reason'] = $this->LgStringSwitch('出生日期格式不正确,格式如2018-01-01');
                            $f_num += 1;
                        }
                    } else {
                        $bool = true;
                        $data['reason'] = $this->LgStringSwitch('家长联系手机号码格式错误');
                        $f_num += 1;
                    }
                }
            } else {
                if (!$sqlone['student_cnname']) {
                    $data['reason'] = $this->LgStringSwitch('学生姓名必填');
                }

                if (!$sqlone['student_sex']) {
                    if ($data['reason']) {
                        $data['reason'] .= $this->LgStringSwitch('学生性别必填');
                    } else {
                        $data['reason'] = $this->LgStringSwitch('学生性别必填');
                    }
                }

                if (!$sqlone['parenter_mobile']) {
                    if ($data['reason']) {
                        $data['reason'] .= $this->LgStringSwitch(',联系电话必填');
                    } else {
                        $data['reason'] = $this->LgStringSwitch('联系电话必填');
                    }
                }

                if (!$sqlone['student_birthday']) {
                    if ($data['reason']) {
                        $data['reason'] .= $this->LgStringSwitch(',出生日期必填');
                    } else {
                        $data['reason'] = $this->LgStringSwitch('出生日期必填');
                    }
                }

                $bool = true;
                $f_num += 1;
            }
            if ($bool) {
                $tem_array[] = $data;
            }
        }

        $array = array();
        $array['errorlog_id'] = 0;
        if ($tem_array) {
            $data = array();
            $data['errorlog_json'] = json_encode($tem_array, JSON_UNESCAPED_UNICODE);
            $data['errorlog_createtime'] = time();
            $errorlog_id = $this->DataControl->insertData("smc_tolead_errorlog", $data);
            $array['errorlog_id'] = $errorlog_id;
        }

        $array['t_num'] = $t_num;
        $array['f_num'] = $f_num;
        $array['list'] = $tem_array;
        return $array;
    }

    function exportErrorStudent($request)
    {

        $sql = "select te.errorlog_json from smc_tolead_errorlog as te where te.errorlog_id='{$request['errorlog_id']}'";

        $logOne = $this->DataControl->selectOne($sql);
        if (!$logOne) {
            $this->error = true;
            $this->errortip = "无错误记录";
            return false;
        }

        $dateexcelarray = json_decode($logOne['errorlog_json'], 1);

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_enname'] = $dateexcelvar['student_enname'];
                $datearray['student_sex'] = $dateexcelvar['student_sex'];
                $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                $datearray['family_relation'] = $dateexcelvar['family_relation'];
                $datearray['student_idcard'] = $dateexcelvar['student_idcard'];
                $datearray['class_branch'] = $dateexcelvar['class_branch'];
                $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                $datearray['reason'] = $dateexcelvar['reason'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "性别", "民族", "出生日期", "家长姓名", "家长手机号", "家长与幼儿关系", "证件号码", "班级编号", "入班日期", "原因"));
        $excelfileds = array('student_cnname', 'student_enname', 'student_sex', 'student_birthday', 'parenter_cnname', 'parenter_mobile', 'family_relation', 'student_idcard', 'class_branch', 'study_beginday', 'reason');
        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("幼儿导入错误记录.xlsx"));

    }

    function getSourceClassList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and co.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and co.course_id='{$request['course_id']}'";
        }

        if (isset($request['course_branch']) && $request['course_branch'] != '') {
            $datawhere .= " and co.course_branch='{$request['course_branch']}'";
        }
        if (isset($request['coursecat_branch']) && $request['coursecat_branch'] != '') {
            $datawhere .= " and b.coursecat_branch='{$request['coursecat_branch']}'";
        }

        if (isset($request['coursetype_branch']) && $request['coursetype_branch'] != '') {
            $datawhere .= " and d.coursetype_branch='{$request['coursetype_branch']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and co.course_id='{$request['course_id']}'";
        }

        $sql = "select cl.class_stdate,cl.class_isupgrade,co.coursetype_id,co.course_id from smc_class as cl,smc_course as co where cl.course_id=co.course_id and cl.class_id='{$request['class_id']}'";

        $classOne = $this->DataControl->selectOne($sql);

        if ($classOne && $classOne['class_isupgrade'] == 1) {
            $this->error = true;
            $this->errortip = "请选择未升班班级";
            return false;
        }

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,co.course_branch,c.class_stdate,c.class_enddate 
              from smc_class as c,smc_course as co,smc_code_coursecat as b,smc_code_coursetype as d
              where {$datawhere} and c.course_id=co.course_id and b.coursecat_id=co.coursecat_id and d.coursetype_id=co.coursetype_id and c.school_id='{$this->schoolOne['school_id']}' and c.class_status='-1' and c.class_stdate<'{$classOne['class_stdate']}' and co.coursetype_id='{$classOne['coursetype_id']}' and c.course_id<>'{$classOne['course_id']}' and c.class_type=0 and co.course_inclasstype=0
              and not exists(select 1 from smc_class as cl where cl.from_class_id=c.class_id) 
              ";

        $classList = $this->DataControl->selectClear($sql);

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无相关班级";
            return false;
        }

        return $classList;

    }

    function addSourceClass($request)
    {

        $sql = "select cl.class_stdate,cl.class_isupgrade,co.coursetype_id from smc_class as cl,smc_course as co where cl.course_id=co.course_id and cl.class_id='{$request['class_id']}'";

        $classOne = $this->DataControl->selectOne($sql);

        if ($classOne && $classOne['class_isupgrade'] == 1) {
            $this->error = true;
            $this->errortip = "请选择未升班班级";
            return false;
        }

        if (!isset($request['from_class_id']) || $request['from_class_id'] == '') {
            $this->error = true;
            $this->errortip = "来源班级ID必须传";
            return false;
        }

        if (!isset($request['class_id']) || $request['class_id'] == '') {
            $this->error = true;
            $this->errortip = "班级ID必须传";
            return false;
        }

        $data = array();
        $data['from_class_id'] = $request['from_class_id'];
        $data['class_isupgrade'] = 1;
        $data['class_upgradetime'] = time();
        $data['class_updatatime'] = time();
        if ($this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "添加失败";
            return false;
        }

    }

    function getClassPromotionList($request)
    {
        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,co.course_branch,c.from_class_id,c.class_isupgrade,c.class_stdate,if(c.class_upgradetime>0, FROM_UNIXTIME(c.class_upgradetime, '%Y-%m-%d %H:%i' ),'--') as class_upgradetime
              ,ifnull((select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=0),'') as mainTeacher
              ,ifnull((select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=1),'') as auxiTeacher
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as stuNum
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_endday>=c.class_enddate and ss.study_beginday<c.class_enddate) as lastNum
              from smc_class as c,smc_course as co 
              where c.course_id=co.course_id and c.class_id='{$request['class_id']}'";

        $classOne = $this->DataControl->selectOne($sql);
        if ($classOne) {
            $this->classArray[] = $classOne;
            $this->ClassFrom($classOne['from_class_id']);
            $this->ClassGo($classOne['class_id']);

        }

        $classList = $this->arraySort($this->classArray, 'class_stdate', 'desc');
        if ($classList) {
            foreach ($classList as &$val) {
                $val['lastNum'] = $val['stuNum'] > 0 ? $val['stuNum'] : $val['lastNum'];
            }
        }

        return $classList;
    }


    function ClassFrom($class_id)
    {
        $sql = "select c.class_id,c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,co.course_branch,c.from_class_id,c.class_isupgrade,c.class_stdate,if(c.class_upgradetime>0, FROM_UNIXTIME(c.class_upgradetime, '%Y-%m-%d %H:%i' ),'--') as class_upgradetime
              ,ifnull((select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=0),'') as mainTeacher
              ,ifnull((select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=1),'') as auxiTeacher
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as stuNum
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_endday>=c.class_enddate and ss.study_beginday<c.class_enddate) as lastNum
              from smc_class as c,smc_course as co 
              where c.course_id=co.course_id and c.class_id='{$class_id}'";

        $classOne = $this->DataControl->selectOne($sql);
        if ($classOne) {
            $this->classArray[] = $classOne;
            if ($classOne['from_class_id'] > 0 && $classOne['class_isupgrade'] == 1) {
                $this->ClassFrom($classOne['from_class_id']);
            }
        }
    }

    function ClassGo($class_id)
    {
        $sql = "select c.class_id,c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,co.course_branch,c.from_class_id,c.class_isupgrade,c.class_stdate,if(c.class_upgradetime>0, FROM_UNIXTIME(c.class_upgradetime, '%Y-%m-%d %H:%i' ),'--') as class_upgradetime
              ,ifnull((select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=0),'') as mainTeacher
              ,ifnull((select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=1),'') as auxiTeacher
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as stuNum
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_endday>=c.class_enddate and ss.study_beginday<c.class_enddate) as lastNum
              from smc_class as c,smc_course as co 
              where c.course_id=co.course_id and c.from_class_id='{$class_id}'";

        $classOne = $this->DataControl->selectOne($sql);
        if ($classOne) {
            $this->classArray[] = $classOne;
            $this->ClassGo($classOne['class_id']);
        }
    }

    function getClassUpgradeList($request)
    {
        $this->UpgradeClassInfo($request['class_id'], 0);
        return $this->classArray;
    }

    function UpgradeClassInfo($class_id, $cursor)
    {
        $sql = "select c.class_id,c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,co.course_branch,c.from_class_id,c.class_isupgrade,c.class_stdate
              ,ifnull((select group_concat(distinct st.staffer_cnname) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=0),'') as mainTeacher
              ,ifnull((select group_concat(distinct st.staffer_cnname) from smc_class_teach as ct,smc_staffer as st where ct.staffer_id=st.staffer_id and ct.class_id=c.class_id and ct.teach_type=1),'') as auxiTeacher
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as stuNum
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_endday>=c.class_enddate and ss.study_beginday<c.class_enddate) as lastNum
              from smc_class as c,smc_course as co 
              where c.course_id=co.course_id 
              and c.class_id='{$class_id}'";
        $classOne = $this->DataControl->selectOne($sql);

        if ($cursor == 0) {
            if ($classOne) {
                $classOne['lastNum'] = $classOne['stuNum'] > 0 ? $classOne['stuNum'] : $classOne['lastNum'];
            }
            $this->classArray[] = $classOne;

            if ($classOne['from_class_id'] && $classOne['from_class_id'] > 0) {
                $this->UpgradeClassInfo($classOne['from_class_id'], -1);
            }

            if ($classOne['class_id'] && $classOne['class_id'] > 0) {
                $sql = "select class_id from smc_class where from_class_id='{$classOne['class_id']}'";
                $classNextOne = $this->DataControl->selectOne($sql);
                if ($classNextOne) {
                    $this->UpgradeClassInfo($classNextOne['class_id'], 1);
                }
            }
        } elseif ($cursor == -1) {
            $this->classArray[] = $classOne;

            if ($classOne['from_class_id'] && $classOne['from_class_id'] > 0) {
                $this->UpgradeClassInfo($classOne['from_class_id'], -1);
            }
        } else {
            $data = array();
            $data[] = $classOne;
            $this->classArray = array_merge($data, $this->classArray);

            if ($classOne['class_id'] && $classOne['class_id'] > 0) {
                $sql = "select class_id from smc_class where from_class_id='{$classOne['class_id']}'";
                $classNextOne = $this->DataControl->selectOne($sql);
                if ($classNextOne) {
                    $this->UpgradeClassInfo($classNextOne['class_id'], 1);
                }
            }
        }
    }

    function arraySort($arr, $keys, $type = 'asc')
    {
        $keysvalue = $new_array = array();
        foreach ($arr as $k => $v) {
            $keysvalue[$k] = $v[$keys];
        }
        $type == 'asc' ? asort($keysvalue) : arsort($keysvalue);
        reset($keysvalue);
        foreach ($keysvalue as $k => $v) {
            $new_array[$k] = $arr[$k];
        }
        return $new_array;
    }

    function submitOpenApply($request)
    {


        if ($this->DataControl->getFieldOne("smc_class_openapply", "openapply_id", "class_id='{$request['class_id']}' and openapply_status>=0")) {
            $this->error = 1;
            $this->errortip = "已提交开班申请，请勿重复提交";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");

        $pricingOne = $this->getCoursePricing($classOne['course_id'], $request['company_id'], $request['school_id']);
        if (!$pricingOne) {
            $this->error = 1;
            $this->errortip = "该课程无定价";
            return false;
        }

        $sql = "select count(study_id) as num from smc_student_study where class_id='{$request['class_id']}' and study_beginday<=CURDATE() and study_endday>=CURDATE() and study_isreading=1";

        $studyOne = $this->DataControl->selectOne($sql);

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['school_id'];
        $data['class_id'] = $request['class_id'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['openapply_studynum'] = $studyOne ? $studyOne['num'] : 0;
        $data['openapply_minclassnum'] = $pricingOne['tuition_minclassnum'];
        $data['openapply_checkpicurl'] = $request['checkpicurl'];
        $data['openapply_note'] = $request['note'];
        $data['openapply_createtime'] = time();
        if ($this->DataControl->insertData("smc_class_openapply", $data)) {
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "申请失败";
            return false;
        }

    }

    function applyFreeHour($request)
    {

        $sql = "select a.course_id,b.course_isopenwarm,b.course_isopenwarm,b.course_canapplywarm,b.course_isopenreview,b.course_reviewnum,b.course_canapplyreview,a.class_hourwarmapplynums,a.class_hourreviewapplynum,a.class_hourwarmnums,a.class_hourreviewnums
                ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=a.class_id and x.hour_iswarming=1 and hour_ischecking<>-1),0) as warmnum
                ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=a.class_id and x.hour_iswarming=2 and hour_ischecking<>-1),0) as reviewnum
                from smc_class as a 
                left join smc_course as b on b.course_id=a.course_id
                where a.class_id = '{$request['class_id']}'
                ";

        $classOne = $this->DataControl->selectOne($sql);

        if(!isset($request['is_skip']) || $request['is_skip']==0){

            if ($this->DataControl->getFieldOne("smc_class_freeapply", "freeapply_id", "class_id='{$request['class_id']}' and freeapply_status=0")) {
                $this->error = 1;
                $this->errortip = "已提交申请，请勿重复提交";
                return false;
            }
            if($request['freeapply_type']==0){
                $leftnum=$classOne['class_hourwarmnums']+$classOne['class_hourwarmapplynums']-$classOne['warmnum'];
                if($leftnum>0){
                    $this->error = '1';
                    $this->errortip = "暖身课默认每次只可开设1小时，该班级剩余{$leftnum}次暖身课次数，请知悉！";
                    return false;
                }

                if($classOne['course_canapplywarm']==0){
                    $this->error = '1';
                    $this->errortip = '该班级无可创建暖身课次数，暂无法创建暖身课';
                    return false;
                }

            }
            elseif($request['freeapply_type']==1){
                $leftnum=$classOne['class_hourreviewnums']+$classOne['class_hourreviewapplynum']-$classOne['reviewnum'];
                if($leftnum>0){
                    $this->error = '1';
                    $this->errortip = "复习课默认每次只可开设1小时，该班级剩余{$leftnum}次复习课次数，请知悉！";
                    return false;
                }

                if($classOne['course_canapplyreview']==0){
                    $this->error = '1';
                    $this->errortip = '该班级无可创建暖身课次数，暂无法创建暖身课';
                    return false;
                }

            }else{
                $this->error = '1';
                $this->errortip = '请选择申请类型';
                return false;
            }
        }

        $data = array();
        $data['class_id'] = $request['class_id'];
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['school_id'];
        $data['classroom_id'] = $request['classroom_id'];
        $data['freeapply_type'] = $request['freeapply_type'];
        $data['freeapply_day'] = $request['hour_day'];
        $data['freeapply_starttime'] = $request['hour_starttime'];
        $data['freeapply_endtime'] = $request['hour_endtime'];
        $data['main_staffer_id'] = $request['main_staffer_id'];
        $data['main_teachtype_code'] = $request['main_teachtype_code'];
        $data['fu_staffer_id'] = $request['fu_staffer_id'];
        $data['fu_teachtype_code'] = $request['fu_teachtype_code'];
        $data['freeapply_reason'] = $request['freeapply_reason'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['freeapply_createtime'] = time();
        if ($this->DataControl->insertData("smc_class_freeapply", $data)) {
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "申请失败";
            return false;
        }

    }

    function getNeedConfirmOpenClassList($request)
    {
        $datawhere = " c.company_id='{$request['company_id']}' and not exists(select 1 from smc_student_hourstudy as x,smc_class_hour as y where x.hour_id=y.hour_id and x.class_id=c.class_id and y.hour_isfree=0)";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and f.school_branch = '{$request['school_branch']}'";
        }


        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }

        if (isset($request['course_branch']) && $request['course_branch'] !== '') {
            $datawhere .= " and sc.course_branch = '{$request['course_branch']}'";
        }

        if (isset($request['coursetype_branch']) && $request['coursetype_branch'] !== '') {
            $datawhere .= " and d.coursetype_branch = '{$request['coursetype_branch']}'";
        }

        if (isset($request['coursecat_branch']) && $request['coursecat_branch'] !== '') {
            $datawhere .= " and e.coursecat_branch = '{$request['coursecat_branch']}'";
        }

        if (isset($request['staffer_branch']) && $request['staffer_branch'] !== '') {
            $datawhere .= " and exists(select 1 from smc_class_teach as x,smc_staffer as y where x.staffer_id=y.staffer_id and x.class_id=c.class_id and y.staffer_branch='{$request['staffer_branch']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,sc.course_cnname,sc.course_branch,c.class_stdate,c.class_enddate,c.from_class_id,d.coursetype_branch,g.class_cnname as from_class_cnname,g.class_branch as from_class_branch
            ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_teach as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teach_status=0) as sonTeacher
                ,ifnull((select count(x.study_id) from smc_student_study as x where x.class_id=c.class_id and x.study_isreading=1),0) as stuNum
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_code_coursetype as d on d.coursetype_id=sc.coursetype_id
                left join smc_code_coursecat as e on e.coursecat_id=sc.coursecat_id
                left join smc_school as f on f.school_id=c.school_id
                left join smc_class as g on g.class_id=c.from_class_id
                WHERE {$datawhere} and c.class_status>=0 and sc.course_isneedconfirm=1 and c.class_isconfirmopen=0
                order by DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                limit {$pagestart},{$num}";

        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        foreach ($classList as &$classOne) {

            $sql = "SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
                    FROM smc_class_lessonplan AS l
                    LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
                    LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
                    WHERE l.class_id = '{$classOne['class_id']}'";

            $classlessonList = $this->DataControl->selectClear($sql);
            $classtimestr = "";
            if ($classlessonList) {
                foreach ($classlessonList as $classlessonOne) {

                    $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                }
            }

            $classOne['class_timestr'] = $classtimestr;
            $classOne['timerange'] = $classOne['class_stdate'].'-'.$classOne['class_enddate'];


        }

        $count_sql = "SELECT c.class_id
            FROM smc_class as c
            left join smc_course as sc on sc.course_id=c.course_id
            left join smc_code_coursetype as d on d.coursetype_id=sc.coursetype_id
            left join smc_code_coursecat as e on e.coursecat_id=sc.coursecat_id
            left join smc_school as f on f.school_id=c.school_id
            WHERE {$datawhere} and c.class_status>=0 and sc.course_isneedconfirm=1 and c.class_isconfirmopen=0";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $classList;

        return $data;

    }

    function batchConfirmOpenClass($request){

        $classList=json_decode(stripslashes($request['class_list']), true);

        if(!$classList){
            $this->error = true;
            $this->errortip = "请选择开班班级";
            return false;
        }


        foreach ($classList as $classOne){
            $data=array();
            $data['class_isconfirmopen']=1;
            $data['class_updatatime']=time();
            $this->DataControl->updateData("smc_class","class_id='{$classOne['class_id']}'",$data);
        }

        return true;



    }



}




