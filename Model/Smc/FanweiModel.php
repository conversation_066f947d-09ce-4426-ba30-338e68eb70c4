<?php
namespace Model\Smc;

class FanweiModel extends modelTpl
{

//    public $host='https://fanwei.kidcastle.com.cn';//正式
//    public $host='http://fanweidev.kidcastle.com.cn:8001';//测试
    public $host='http://fanwei.kidcastle.com.cn';//测试
    public $appid='d5ca4b2a-664a-4260-9423-186635c0ba03';
    public $secrit='';
    public $secret='';
    public $spk='';
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示

    function __construct()
    {
        parent::__construct();
//        if(!$this->getSecret()){
//            $this->error = 1 ;
//            $this->errortip = "请求失败" ;
//            return false;
//        }
    }


// 获取AccessToken
//    public function getSecret()
//    {
//
//        $url = $this->host . "/api/ec/dev/auth/regist";
//
//        $response = request_by_curl($url,'',"POST",['appid' => $this->appid]);
//
//        if(!$response || $response==''){
//            $this->error = 1 ;
//            $this->errortip = "请求失败" ;
//            return false;
//        }
//
//        $content = json_decode($response, true);
//
//        $this->secrit=$content['secrit'];
//        $this->secret=$content['secret'];
//        $this->spk=$content['spk'];
//
//        return [
//            'secrit' => $content['secrit'],
//            'secret' => $content['secret'],
//            'spk' => $content['spk'],
//        ];
//    }


    // 获取token用于第三方系统调用
//    public function getToken()
//    {
//        $secrit = $this->secrit;
//        $secret = $this->secret;
//        $spk = $this->spk;
//
//        $puKey=openssl_pkey_get_public("-----BEGIN PUBLIC KEY-----\n" . $spk . "\n-----END PUBLIC KEY-----");//这个函数可用来判断公钥是否是可用的
//
//        $encrypted = "";
//
//        openssl_public_encrypt($secret,$encrypted,$puKey);
//
//        $encrypted = base64_encode($encrypted);
//
//        $url = $this->host . "/api/ec/dev/auth/applytoken";
//
//        $response = request_by_curl($url,'',"POST",[
//            'appid' => $this->appid,
//            'secret' => $encrypted,
//            'time' => 3600,
//        ]);
//
//        if(!$response || $response==''){
//            $this->error = 1 ;
//            $this->errortip = "请求失败" ;
//            return false;
//        }
//
//        $content = json_decode($response, true);
//
//        if($content['status']){
//            return $content['token'];
//        }else{
//            $this->error = 1 ;
//            $this->errortip = $content['msg'] ;
//            return false;
//        }
//    }

    public function getToken()
    {

        $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/getFanweiToken",'',"GET");

        if($response){
            $datainfo=json_decode($response,1);
            if($datainfo){
                $this->secrit=$datainfo['result']['list']['secrit'];
                $this->secret=$datainfo['result']['list']['secret'];
                $this->spk=$datainfo['result']['list']['spk'];
                return $datainfo['result']['list']['token'];
            }else{
                $this->error = 1 ;
                $this->errortip = "获取失败" ;
                return false;
            }
        }else{
            $this->error = 1 ;
            $this->errortip = "请求失败" ;
            return false;
        }

    }


    function createNewAdjustApplyBak($adjustapply_id){

        if(!$token=$this->getToken()){
            $this->error = 1 ;
            return false;
        }

        $sql = "select a.*,b.school_cnname,c.staffer_employeepid,ifnull(d.hour_day,a.adjustapply_day) as hour_day,ifnull(g.coursetype_cnname,'') as coursetype_cnname,e.class_cnname,e.class_branch,ifnull(h.staffer_employeepid,'') as main_apply_staffer_employeepid,g.coursetype_branch,b.school_branch,ifnull(h.staffer_cnname,'') as main_apply_staffer_cnname
                ,if(a.adjustapply_type=0,1,(select count(distinct x.class_id) from smc_class_hour as x,smc_class as y where x.class_id=y.class_id and y.school_id=a.school_id and x.hour_day=a.adjustapply_day and y.class_status>=0 and x.hour_ischecking=0)) as classNum
                ,ifnull((select y.staffer_employeepid from smc_class_hour_teaching as x,smc_staffer as y where x.staffer_id=y.staffer_id and x.hour_id=d.hour_id and x.teaching_type=0 and x.teaching_isdel=0 limit 0,1),'') as apply_staffer_employeepid 
                from smc_class_hour_adjustapply as a
                inner join smc_school as b on b.school_id=a.school_id
                inner join smc_staffer as c on c.staffer_id=a.staffer_id
                left join smc_class_hour as d on d.hour_id=a.hour_id
                left join smc_class as e on e.class_id=d.class_id
                left join smc_course as f on f.course_id=e.course_id
                left join smc_code_coursetype as g on g.coursetype_id=f.coursetype_id
                left join smc_staffer as h on h.staffer_id=a.adjustapply_main_staffer_id
                where a.adjustapply_id='{$adjustapply_id}' and a.company_id=8888";

        $applyOne=$this->DataControl->selectOne($sql);

        if(!$applyOne){
            $this->error = 1 ;
            $this->errortip = "申请不存在" ;
            return false;
        }
        if($applyOne['staffer_employeepid']==''){
            $this->error = 1 ;
            $this->errortip = "员工编号不存在" ;
            return false;
        }

        $schoolOne=$this->getSchoolInfo($applyOne['school_branch']);
        if(!$schoolOne){
            $this->error = 1 ;
            return false;
        }

        $courseTypeOne=$this->getCourseTypeInfo($applyOne['coursetype_branch']);

        if(!$courseTypeOne){
            $this->error = 1 ;
            return false;
        }

        $UseDataRequest=$this->getStafferInfo($applyOne['staffer_employeepid']);

        if(!$UseDataRequest){
            $this->error = 1 ;
            return false;
        }

//        $userid = $this->encrypt($UseDataRequest['fanweiUserId']);
        $userid=$UseDataRequest['fanweiUserIdJiami'];

        $url = $this->host . "/api/workflow/paService/doCreateRequest";

//        $classArray=$this->LgArraySwitch(array('0'=>'政策调课','1'=>'其他调课'));

        if($applyOne['apply_staffer_employeepid']!=''){
            $applyRequest=$this->getStafferInfo($applyOne['apply_staffer_employeepid']);
            $apply_staffer_employeepid=$applyRequest['fanweiUserId'];
        }

//        if($applyOne['main_apply_staffer_employeepid']!=''){
//            $mainRequest=$this->getStafferInfo($applyOne['main_apply_staffer_employeepid']);
//            $main_apply_staffer_employeepid=$mainRequest['fanweiUserId'];
//        }

        $mainData=[
            [
                'fieldName'=>'tjr',
                'fieldValue'=>(string)$UseDataRequest['fanweiUserId'],//提交人  YES
            ],
            [
                'fieldName'=>'szbm',
                'fieldValue'=>(string)$UseDataRequest['department'],//所在部门  YES
            ],
            [
                'fieldName'=>'tjrzw',
                'fieldValue'=>(string)$UseDataRequest['jobtitle'],//提交人职位  YES
            ],
            [
                'fieldName'=>'sqrq',
                'fieldValue'=>date("Y-m-d",$applyOne['adjustapply_createtime']),//申请日期  YES
            ],
            [
                'fieldName'=>'dzsm',
                'fieldValue'=>(string)$applyOne['adjustapply_note'],//调整说明  YES
            ],
            [
                'fieldName'=>'fj',
                'fieldValue'=>[
                    [
                        'filePath'=>$applyOne['adjustapply_fileurl'],
                        'fileName'=>parse_url($applyOne['adjustapply_fileurl'], PHP_URL_PATH),
                    ],//图片  NO
                ]
            ],
            [
                'fieldName'=>'xxmc',
                'fieldValue'=>(string)$schoolOne['id'],//学校名称  YES
            ],
            [
                'fieldName'=>'dklx',
                'fieldValue'=>(string)(1-$applyOne['adjustapply_type']),//调整类型  YES
            ],
            [
                'fieldName'=>'ypkrq',
                'fieldValue'=>$applyOne['hour_day'],//原排课日期  YES
            ],
            [
                'fieldName'=>'sjbjs',
                'fieldValue'=>(string)$applyOne['classNum'],//涉及班级数  YES
            ],
            [
                'fieldName'=>'banzu',
                'fieldValue'=>(string)$courseTypeOne['id'],//班组  YES
            ],
        ];

        $detailData=[
            [
                'fieldName'=>'bjbh',
                'fieldValue'=>$applyOne['class_cnname'],//班级编号
            ],
            [
                'fieldName'=>'bjmc',
                'fieldValue'=>$applyOne['class_branch'],//班级名称
            ],
            [
                'fieldName'=>'ypkrq',
                'fieldValue'=>$applyOne['hour_day'],//原排课日期
            ],
            [
                'fieldName'=>'dbls',
                'fieldValue'=>(string)($apply_staffer_employeepid?$apply_staffer_employeepid:$UseDataRequest['fanweiUserId']),//带班老师
            ],
            [
                'fieldName'=>'pkkss',
                'fieldValue'=>"1",//排课课时数
            ],
            [
                'fieldName'=>'dkyy1',
                'fieldValue'=>(string)$applyOne['adjustapply_class'],//调整原因
            ],
            [
                'fieldName'=>'beizhu',
                'fieldValue'=>$applyOne['adjustapply_reason'],//备注
            ],
            [
                'fieldName'=>'jhbkrq',
                'fieldValue'=>$applyOne['adjustapply_day'],//计划补课日期
            ],
            [
                'fieldName'=>'jhbkkss',
                'fieldValue'=>"1",//计划补课课时数
            ],
            [
                'fieldName'=>'bkskls2',
                'fieldValue'=>(string)$applyOne['main_apply_staffer_cnname'],//补课授课老师
            ],
            [
                'fieldName'=>'dzlx',
                'fieldValue'=>(string)(1-$applyOne['adjustapply_type']),//调课类型
            ],
        ];


        $detailjson=[
                [
                    'tableDBName'=>'formtable_main_17_dt1',
                    'workflowRequestTableRecords'=>[
                        [
                            'recordOrder'=>0,
                            'workflowRequestTableFields'=>$detailData,
                        ],
                    ]
                ]
            ];

        $pama=[
            'detailData'=>json_encode($detailjson,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
            'mainData'=>json_encode($mainData,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
            'requestName'=>'申请调课',
            'workflowId'=>259,
        ];

        $header=['appid' => $this->appid,'token' => $token,'userid' => $userid];

        $response = request_by_curl($url,http_build_query($pama),"POST",$header);

        if(!$response){
            $this->error = 1 ;
            $this->errortip = "创建流程请求失败" ;
            return false;
        }

        $responseArray=json_decode($response,1);

        if($responseArray['code']=='SUCCESS'){
            $data=[
                'requestid'=>$responseArray['data']['requestid']
            ];

            $this->DataControl->updateData("smc_class_hour_adjustapply","adjustapply_id='{$adjustapply_id}'",$data);

        }else{
            $this->error = 1 ;
            $this->errortip = $responseArray['msg'];
            return false;
        }

    }

    function createNewAdjustApply($adjustapply_id){

        $sql = "select a.*,b.school_cnname,c.staffer_employeepid,ifnull(d.hour_day,a.adjustapply_day) as hour_day,ifnull(g.coursetype_cnname,'') as coursetype_cnname,e.class_cnname,e.class_branch,ifnull(h.staffer_employeepid,'') as main_apply_staffer_employeepid,g.coursetype_branch,b.school_branch,ifnull(h.staffer_cnname,'') as main_apply_staffer_cnname
                ,if(a.adjustapply_type=0,1,(select count(distinct x.class_id) from smc_class_hour as x,smc_class as y where x.class_id=y.class_id and y.school_id=a.school_id and x.hour_day=a.adjustapply_day and y.class_status>=0 and x.hour_ischecking=0)) as classNum
                ,ifnull((select y.staffer_employeepid from smc_class_hour_teaching as x,smc_staffer as y where x.staffer_id=y.staffer_id and x.hour_id=d.hour_id and x.teaching_type=0 and x.teaching_isdel=0 limit 0,1),'') as apply_staffer_employeepid 
                from smc_class_hour_adjustapply as a
                inner join smc_school as b on b.school_id=a.school_id
                inner join smc_staffer as c on c.staffer_id=a.staffer_id
                left join smc_class_hour as d on d.hour_id=a.hour_id
                left join smc_class as e on e.class_id=d.class_id
                left join smc_course as f on f.course_id=e.course_id
                left join smc_code_coursetype as g on g.coursetype_id=f.coursetype_id
                left join smc_staffer as h on h.staffer_id=a.adjustapply_main_staffer_id
                where a.adjustapply_id='{$adjustapply_id}' and a.company_id=8888";

        $applyOne=$this->DataControl->selectOne($sql);

        if(!$applyOne){
            $this->error = 1 ;
            $this->errortip = "申请不存在" ;
            return false;
        }
        if($applyOne['staffer_employeepid']==''){
            $this->error = 1 ;
            $this->errortip = "员工编号不存在" ;
            return false;
        }


        $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/CreateFanweiProcess",dataEncode($applyOne),"GET");

        if($response){
            $datainfo=json_decode($response,1);

            if($datainfo['error']==0){
                $responseArray=$datainfo['result']['list'];
                if($responseArray['code']=='SUCCESS'){
                    $data=[
                        'requestid'=>$responseArray['data']['requestid']
                    ];

                    $this->DataControl->updateData("smc_class_hour_adjustapply","adjustapply_id='{$adjustapply_id}'",$data);

                    $this->recedeGenerate($responseArray['data']['requestid'],'adjustApply');

                }else{
                    $this->error = 1 ;
                    $this->errortip = $responseArray['msg'];
                    return false;
                }
            }else{
                $this->error = 1 ;
                $this->errortip = $datainfo['errortip'] ;
                return false;
            }
        }else{
            $this->error = 1 ;
            $this->errortip = "请求失败" ;
            return false;
        }

    }

    function cancelAdjustApply($adjustapply_id){


        $sql = "select a.adjustapply_id,a.requestid,c.staffer_employeepid,a.company_id,a.school_id,c.staffer_cnname 
                from smc_class_hour_adjustapply as a 
                inner join smc_staffer as c on c.staffer_id=a.staffer_id
                where a.requestid>0 and a.adjustapply_status=0 and a.company_id=8888 and a.adjustapply_id='{$adjustapply_id}'
                ";

        $applyOne=$this->DataControl->selectOne($sql);

        if($applyOne){

            $applyOne['remark'] = $applyOne['staffer_cnname'].'在'.date("Y-m-d H:i:s").'课叮铛操作取消流程';
            $applyOne['submitNodeId']='1860';
            $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/cancelAdjustApply",dataEncode($applyOne),"GET");

            if($response){

                $responseInfo=json_decode($response,1);
                $responseInfo=$responseInfo['result']['list'];
                if($responseInfo['code']=='SUCCESS'){
                    return true;
                }elseif($responseInfo['code']=='NO_PERMISSION'){
                    return true;
                }else{

                    return false;
                }

            }else{
                return false;
            }
        }else{
            return false;
        }

    }



    function autoCreateAdjustApply(){

        $sql = "select a.*,b.school_cnname,c.staffer_employeepid,ifnull(d.hour_day,a.adjustapply_day) as hour_day,ifnull(g.coursetype_cnname,'') as coursetype_cnname,e.class_cnname,e.class_branch,ifnull(h.staffer_employeepid,'') as main_apply_staffer_employeepid,g.coursetype_branch,b.school_branch,ifnull(h.staffer_cnname,'') as main_apply_staffer_cnname
                ,if(a.adjustapply_type=0,1,(select count(distinct x.class_id) from smc_class_hour as x,smc_class as y where x.class_id=y.class_id and y.school_id=a.school_id and x.hour_day=a.adjustapply_day and y.class_status>=0 and x.hour_ischecking=0)) as classNum
                ,ifnull((select y.staffer_employeepid from smc_class_hour_teaching as x,smc_staffer as y where x.staffer_id=y.staffer_id and x.hour_id=d.hour_id and x.teaching_type=0 and x.teaching_isdel=0 limit 0,1),'') as apply_staffer_employeepid 
                from smc_class_hour_adjustapply as a
                inner join smc_school as b on b.school_id=a.school_id
                inner join smc_staffer as c on c.staffer_id=a.staffer_id
                left join smc_class_hour as d on d.hour_id=a.hour_id
                left join smc_class as e on e.class_id=d.class_id
                left join smc_course as f on f.course_id=e.course_id
                left join smc_code_coursetype as g on g.coursetype_id=f.coursetype_id
                left join smc_staffer as h on h.staffer_id=a.adjustapply_main_staffer_id
                where a.company_id=8888 and a.adjustapply_status=0 and a.is_need_process=1 and requestid=0 and b.school_istest=0 and b.school_isclose=0 and b.school_isskipFw=0
                having staffer_employeepid<>''
                ";

        $applyList=$this->DataControl->selectClear($sql);

        if(!$applyList){
            $this->error = 1 ;
            $this->errortip = "申请不存在" ;
            return false;
        }

        $temArray=[];
        foreach ($applyList as $applyOne){

            if($applyOne['adjustapply_type']==0){
                $str=$applyOne['school_id'].$applyOne['adjustapply_type'].$applyOne['class_id'].$applyOne['adjustapply_class'].$applyOne['hour_day'];
            }else{
                $str=$applyOne['school_id'].$applyOne['adjustapply_type'].$applyOne['class_id'].$applyOne['adjustapply_class'];
            }

        
            $temArray[$str]['applyList'][]=$applyOne;
            $temArray[$str]['adjustapply_createtime']=$applyOne['adjustapply_createtime'];
            $temArray[$str]['adjustapply_note']=$applyOne['adjustapply_note'];
            $temArray[$str]['adjustapply_reason']=$applyOne['adjustapply_reason'];
            $temArray[$str]['adjustapply_fileurl']=$applyOne['adjustapply_fileurl'];
            $temArray[$str]['adjustapply_type']=$applyOne['adjustapply_type'];
            $temArray[$str]['apply_staffer_employeepid']=$applyOne['apply_staffer_employeepid'];
            $temArray[$str]['coursetype_branch']=$applyOne['coursetype_branch'];
            $temArray[$str]['school_branch']=$applyOne['school_branch'];
            $temArray[$str]['adjustapply_class']=$applyOne['adjustapply_class'];
            $temArray[$str]['hour_day']=$applyOne['hour_day'];
            $temArray[$str]['classNum']=1;
            $temArray[$str]['class_id']=$applyOne['class_id'];
            $temArray[$str]['school_id']=$applyOne['school_id'];
            $temArray[$str]['staffer_employeepid']=$applyOne['staffer_employeepid'];

        }
        if(!$temArray){
            $this->error = 1 ;
            $this->errortip = "无申请" ;
            return false;
        }
        
        sort($temArray);
        
        foreach($temArray as $temOne){

            $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/CreateFanweiProcessNew",dataEncode($temOne),"POST");
            if($response){
                $datainfo=json_decode($response,1);

                if($datainfo['error']==0){
                    $responseArray=$datainfo['result']['list'];
                    if($responseArray['code']=='SUCCESS'){
                        $data=[
                            'requestid'=>$responseArray['data']['requestid']
                        ];

                        if($temOne['adjustapply_type']==0){
                            $this->DataControl->updateData("smc_class_hour_adjustapply","school_id='{$temOne['school_id']}' and adjustapply_type='{$temOne['adjustapply_type']}' and class_id='{$temOne['class_id']}' and adjustapply_class='{$temOne['adjustapply_class']}' and hour_day='{$temOne['hour_day']}' and is_need_process=1 and requestid=0",$data);
                        }else{
                            $this->DataControl->updateData("smc_class_hour_adjustapply","school_id='{$temOne['school_id']}' and adjustapply_type='{$temOne['adjustapply_type']}' and class_id='{$temOne['class_id']}' and adjustapply_class='{$temOne['adjustapply_class']}' and is_need_process=1 and requestid=0",$data);
                        }

                        

                        $this->recedeGenerate($responseArray['data']['requestid'],'adjustApply');

                    }else{
                        $this->error = 1 ;
                        $this->errortip = $responseArray['msg'];
                        continue;
                    }
                }else{
                    $this->error = 1 ;
                    $this->errortip = $datainfo['errortip'] ;
                    continue;
                }
            }
        }

        return true;

    }

    function fanWeiAutoUpdate($branch='adjustApply'){

        $processOne=$this->DataControl->getFieldOne("smc_fanwei_process","process_cycletime,process_function","process_branch='{$branch}'");

        if(!$processOne){
            $this->error = 1 ;
            $this->errortip = "无该流程" ;
            return false;
        }

        $functionName=$processOne['process_function'];

        $bool=$this->$functionName($processOne['process_cycletime']);

        if($bool){
            return true;
        }else{
            $this->error = 1 ;
            $this->errortip = "更新失败" ;
            return false;
        }

    }

    function recedeGenerate($requestid=0,$process_branch=''){

        if($this->DataControl->getFieldOne("smc_fanwei_process_generate","generate_id","requestid='{$requestid}'")){

            $data=array();
            $data['generate_updatatime']=time();
            $this->DataControl->updateData("smc_fanwei_process_generate","requestid='{$requestid}'",$data);

        }else{

            $data=array();
            $data['requestid']=$requestid;
            $data['process_branch']=$process_branch;
            $data['generate_createtime']=time();
            $this->DataControl->insertData("smc_fanwei_process_generate",$data);
        }

    }

    function createFreeTimesApply($order_pid){

        $sql = "select b.school_branch,b.school_cnname,d.student_branch,d.student_cnname,e.staffer_employeepid,c.class_cnname,c.class_branch
                ,(select count(x.ordertimes_id) from smc_freehour_ordertimes as x where x.order_pid=a.order_pid) as freeNum
                ,(select x.hour_lessontimes from smc_freehour_ordertimes as x where x.order_pid=a.order_pid order by x.hour_lessontimes asc limit 0,1) as firsttimes
                ,(select x.hour_lessontimes from smc_freehour_ordertimes as x where x.order_pid=a.order_pid order by x.hour_lessontimes desc limit 0,1) as lasttimes
                ,a.order_img,a.order_refusereason,a.order_createtime
                from smc_freehour_order as a
                left join smc_school as b on b.school_id=a.school_id
                left join smc_class as c on c.class_id=a.class_id
                left join smc_student as d on d.student_id=a.student_id
                left join smc_staffer as e on e.staffer_id=a.staffer_id
                where a.order_pid='{$order_pid}'";

        $orderOne=$this->DataControl->selectOne($sql);

        if(!$orderOne){
            $this->error = 1 ;
            $this->errortip = "赠送订单不存在" ;
            return false;
        }

        if($orderOne['staffer_employeepid']==''){
            $this->error = 1 ;
            $this->errortip = "员工编号不存在" ;
            return false;
        }

        $orderOne['type']='4';//赠课原因类型  0-拆并班 1-重读赠课 2-已考勤课时返还 3-暖身课 4-特殊赠课 5-搬校 6-发生意外赔偿 7-其他

        $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/createFreeTimesApply",dataEncode($orderOne),"POST");

        if($response){

            $datainfo=json_decode($response,1);

            if($datainfo['error']==0){
                $responseArray=$datainfo['result']['list'];
                if($responseArray['code']=='SUCCESS'){
                    $data=[
                        'requestid'=>$responseArray['data']['requestid']
                    ];

                    $this->DataControl->updateData("smc_freehour_order","order_pid='{$order_pid}'",$data);

                    $this->recedeGenerate($responseArray['data']['requestid'],'adjustFreeTimesApply');

                    return true;

                }else{
                    $this->error = 1 ;
                    $this->errortip = $responseArray['msg'];
                    return false;
                }
            }else{
                $this->error = 1 ;
                $this->errortip = $datainfo['errortip'] ;
                return false;
            }
        }else{
            $this->error = 1 ;
            $this->errortip = "请求失败" ;
            return false;
        }
    }

    function createRefundApply($order_pid){

        $sql = "SELECT b.school_branch,b.school_cnname,d.student_branch,d.student_cnname,e.staffer_employeepid,a.student_id,a.refund_payprice,a.refund_reason,a.refund_createtime as order_createtime,a.refund_bankcardurl,(a.refund_reasontype-1) as refund_reasontype
                ,a.refund_bank,a.refund_accountname,a.refund_bankcard,a.refund_specialprice,a.refund_specialreason 
                from smc_refund_order as a
                left join smc_school as b on b.school_id=a.school_id
                left join smc_student as d on d.student_id=a.student_id
                left join smc_staffer as e on e.staffer_id=a.staffer_id
                where a.refund_pid='{$order_pid}' and a.refund_status=0";

        $orderOne=$this->DataControl->selectOne($sql);

        if(!$orderOne){
            $this->error = 1 ;
            $this->errortip = "退费订单不存在" ;
            return false;
        }

        if($orderOne['staffer_employeepid']==''){
            $this->error = 1 ;
            $this->errortip = "员工编号不存在" ;
            return false;
        }

        $sql = "SELECT c.coursetype_id
                from smc_payfee_order_course as a
                inner join smc_payfee_order as b on b.order_pid=a.order_pid
                inner join smc_course as c on c.course_id=a.course_id
                inner join smc_code_coursetype as d on d.coursetype_id=c.coursetype_id
                where b.student_id='{$orderOne['student_id']}' and d.coursetype_branch='E' and b.order_status>=0
                ";
        $meiyuOrderOne=$this->DataControl->selectOne($sql);

        if($meiyuOrderOne){

            $orderOne['refund_type']=0;
            $orderOne['coursetype_class']=0;

            $sql = "select z.coursecat_id,x.class_id,u.coursecat_cnname,u.coursecat_branch,z.course_shortname,y.class_cnname,v.hour_day,y.class_enname
                    ,ifnull((select x.staffer_id from smc_class_teach as x where x.class_id=y.class_id and x.teach_status=0 and x.teach_type=0 limit 0,1),0) as staffer_id 
                    from smc_student_hourstudy as x,smc_class as y,smc_course as z,smc_code_coursecat as u,smc_class_hour as v 
                    where x.class_id=y.class_id and y.course_id=z.course_id and u.coursecat_id=z.coursecat_id and x.hour_id=v.hour_id and x.student_id='{$orderOne['student_id']}' and z.coursetype_id='{$meiyuOrderOne['coursetype_id']}' 
                    order by x.hourstudy_id desc 
                    limit 0,1 ";
            $classOne=$this->DataControl->selectOne($sql);

            if($classOne){
                $orderOne['refund_type']=1;
                $orderOne['coursetype_class']=0;
                $orderOne['coursecat_cnname']=$classOne['coursecat_cnname'];
                $orderOne['hour_day']=$classOne['hour_day'];
                $orderOne['class_cnname']=$classOne['class_cnname'];
                $orderOne['class_enname']=$classOne['class_enname'];
                $orderOne['course_shortname']=$classOne['course_shortname'];

            }

        }else{

            $sql = "SELECT c.coursetype_id
            from smc_payfee_order_course as a
            inner join smc_payfee_order as b on b.order_pid=a.order_pid
            inner join smc_course as c on c.course_id=a.course_id
            inner join smc_code_coursetype as d on d.coursetype_id=c.coursetype_id
            where b.student_id='{$orderOne['student_id']}' and d.coursetype_branch='T' and b.order_status>=0
            ";
            $kefuOrderOne=$this->DataControl->selectOne($sql);

            if($kefuOrderOne){

                $orderOne['refund_type']=0;
                $orderOne['coursetype_class']=1;

                $sql = "select z.coursecat_id,x.class_id,u.coursecat_cnname,u.coursecat_branch,z.course_shortname,y.class_cnname,v.hour_day,y.class_enname
                        ,ifnull((select x.staffer_id from smc_class_teach as x where x.class_id=y.class_id and x.teach_status=0 and x.teach_type=0 limit 0,1),0) as staffer_id 
                        from smc_student_hourstudy as x,smc_class as y,smc_course as z,smc_code_coursecat as u,smc_class_hour as v 
                        where x.class_id=y.class_id and y.course_id=z.course_id and u.coursecat_id=z.coursecat_id and x.hour_id=v.hour_id and x.student_id='{$orderOne['student_id']}' and z.coursetype_id='{$kefuOrderOne['coursetype_id']}' 
                        order by x.hourstudy_id desc 
                        limit 0,1 ";
                $classOne=$this->DataControl->selectOne($sql);

                if($classOne){
                    $orderOne['refund_type']=1;
                    $orderOne['coursetype_class']=1;
                    $orderOne['coursecat_cnname']=$classOne['coursecat_cnname'];
                    $orderOne['hour_day']=$classOne['hour_day'];
                    $orderOne['class_cnname']=$classOne['class_cnname'];
                    $orderOne['class_enname']=$classOne['class_enname'];
                    $orderOne['course_shortname']=$classOne['course_shortname'];

                }

            }else{

                $orderOne['refund_type']=0;
                $orderOne['coursetype_class']=2;

                $sql = "select z.coursecat_id,x.class_id,u.coursecat_cnname,u.coursecat_branch,z.course_shortname,y.class_cnname,v.hour_day,y.class_enname
                        ,ifnull((select x.staffer_id from smc_class_teach as x where x.class_id=y.class_id and x.teach_status=0 and x.teach_type=0 limit 0,1),0) as staffer_id 
                        from smc_student_hourstudy as x,smc_class as y,smc_course as z,smc_code_coursecat as u,smc_class_hour as v 
                        where x.class_id=y.class_id and y.course_id=z.course_id and u.coursecat_id=z.coursecat_id and x.hour_id=v.hour_id and x.student_id='{$orderOne['student_id']}' and z.coursetype_id not in (65,79655)
                        order by x.hourstudy_id desc 
                        limit 0,1 ";
                $classOne=$this->DataControl->selectOne($sql);

                if($classOne){
                    $orderOne['refund_type']=1;
                    $orderOne['coursetype_class']=2;
                    $orderOne['coursecat_cnname']=$classOne['coursecat_cnname'];
                    $orderOne['hour_day']=$classOne['hour_day'];
                    $orderOne['class_cnname']=$classOne['class_cnname'];
                    $orderOne['class_enname']=$classOne['class_enname'];
                    $orderOne['course_shortname']=$classOne['course_shortname'];

                }
            }

        }

        if($classOne['staffer_id']>0 && SITE_URL=='kedingdang.com'){

            $sql = "select a.staffer_cnname,a.staffer_jointime
                    from smc_staffer as a 
                    where a.staffer_id='{$classOne['staffer_id']}'
                    ";

            $stafferOne=$this->DataControl->selectOne($sql);
            if($stafferOne){

                $orderOne['staffer_cnname']=$stafferOne['staffer_cnname'];

                if($stafferOne['staffer_jointime']){
                    $dataInfo=$this->calculateSeniority($stafferOne['staffer_jointime']);

                    $str="";
    
                    if($dataInfo['years']>0){
                        $str.=$dataInfo['years'].'年';
                    }
    
                    if($dataInfo['months']>0){
                        $str.=$dataInfo['months'].'个月';
                    }
    
                    if($str!=''){
                        $orderOne['nanzi']=$str;
                    }
                }else{
                    $orderOne['nanzi']='';
                }
                $Bipark = new \Dbsqlplay("rm-bp1t24ym8tb3b798mlo.mysql.rds.aliyuncs.com", "appuser", "Jbd2022!new", "bak_bi_dw_data");

                $sql = "select a.近半年留班率 as halfrate,a.历史留班率 as allrate
                        from ads_databack_jslb as a 
                        where a.教师序号='{$classOne['staffer_id']}'";

                $qiquStafferOne=$Bipark->selectOne($sql);
                if($qiquStafferOne){
                    $orderOne['halfrate']=sprintf('%.2f%%', $qiquStafferOne['halfrate'] * 100);
                    $orderOne['allrate']=sprintf('%.2f%%', $qiquStafferOne['allrate'] * 100);
                }

            }

        }

        $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/createRefundApply",dataEncode($orderOne),"POST");
        if($response){
            
            $datainfo=json_decode($response,1);

            if($datainfo['error']==0){
                $responseArray=$datainfo['result']['list'];
                if($responseArray['code']=='SUCCESS'){
                    $data=[
                        'requestid'=>$responseArray['data']['requestid']
                    ];

                    $this->DataControl->updateData("smc_refund_order","refund_pid='{$order_pid}'",$data);

                    $this->recedeGenerate($responseArray['data']['requestid'],'adjustRefundApply');

                    return true;

                }else{
                    $this->error = 1 ;
                    $this->errortip = $responseArray['msg'];
                    return false;
                }
            }else{
                $this->error = 1 ;
                $this->errortip = $datainfo['errortip'] ;
                return false;
            }
        }else{
            $this->error = 1 ;
            $this->errortip = "请求失败" ;
            return false;
        }

    }

    function calculateSeniority($entryDate) {
        // 解析入职日期和当前日期的年、月、日
        list($joinYear, $joinMonth, $joinDay) = explode('-', $entryDate);
        $currentYear = date('Y');
        $currentMonth = date('m');
        $currentDay = date('d');
    
        // 计算总月份差
        $totalMonths = ($currentYear - $joinYear) * 12 + ($currentMonth - $joinMonth);
    
        // 处理日的影响：若当前日小于入职日，月份减1
        if ($currentDay < $joinDay) {
            $totalMonths--;
        }
    
        // 处理月份为负数的情况
        if ($totalMonths < 0) {
            return "0年0个月";
        }
    
        // 计算年、月结果
        $years = floor($totalMonths / 12);
        $months = $totalMonths % 12;
    
        $data=array(
            'years' => $years,
            'months' => $months
        );

        return $data;

    }

    function createForwardApply($application_id){
        $sql = "SELECT a.student_id,a.course_id,a.class_id,a.apply_staffer_id,a.apply_time,(a.application_type - 1) as application_type,a.out_class_date,a.back_class_date,a.forward_reason,a.attachment_url,a.forward_amount,a.remaining_times,a.remaining_amount,a.application_status,a.create_time,a.update_time,b.student_cnname,b.student_branch,c.course_cnname,c.course_branch,d.class_cnname,d.class_branch,e.staffer_cnname,e.staffer_employeepid,f.school_branch,f.school_cnname,g.coursecat_cnname,h.coursetype_cnname,c.coursecat_id,c.coursetype_id,g.coursecat_branch,h.coursetype_branch
                ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=a.class_id and x.hour_ischecking=1),0) as hourNumber
                ,(select group_concat(distinct concat(y.staffer_cnname,(CASE WHEN ifnull( y.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', y.staffer_enname ) END ))) from smc_class_teach as x,smc_staffer as y where x.staffer_id=y.staffer_id and x.class_id=a.class_id and x.teach_status=0) as teacherstr
                from smc_forward_application as a
                inner join smc_student as b on b.student_id=a.student_id
                inner join smc_course as c on c.course_id=a.course_id
                inner join smc_class as d on d.class_id=a.class_id
                inner join smc_staffer as e on e.staffer_id=a.apply_staffer_id
                inner join smc_school as f on f.school_id=a.school_id
                inner join smc_code_coursecat as g on g.coursecat_id=c.coursecat_id
                inner join smc_code_coursetype as h on h.coursetype_id=c.coursetype_id
                where a.application_id='{$application_id}' and a.application_status=0";

        $applicationInfo=$this->DataControl->selectOne($sql);

        if(!$applicationInfo){
            $this->error = 1 ;
            $this->errortip = "结转申请不存在" ;
            return false;
        }

        if($applicationInfo['staffer_employeepid']==''){
            $this->error = 1 ;
            $this->errortip = "员工编号不存在" ;
            return false;
        }

        $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/createForwardApply",dataEncode($applicationInfo),"POST");

        if($response){

            $datainfo=json_decode($response,1);

            if($datainfo['error']==0){
                $responseArray=$datainfo['result']['list'];
                if($responseArray['code']=='SUCCESS'){
                    $data=[
                        'requestid'=>$responseArray['data']['requestid'],
                        'fanwei_status'=>1,
                        'update_time'=>time()
                    ];

                    $this->DataControl->updateData("smc_forward_application","application_id='{$application_id}'",$data);

                    $this->recedeGenerate($responseArray['data']['requestid'],'adjustForwardApply');

                    return true;

                }else{
                    $this->error = 1 ;
                    $this->errortip = $responseArray['msg'];
                    return false;
                }
            }else{
                $this->error = 1 ;
                $this->errortip = $datainfo['errortip'] ;
                return false;
            }
        }else{
            $this->error = 1 ;
            $this->errortip = "请求失败" ;
            return false;
        }



    }

    function cancelApplication($requestid){


        $sql = "select a.requestid,c.staffer_employeepid,a.company_id,a.school_id,c.staffer_cnname 
                from smc_forward_application as a 
                inner join smc_staffer as c on c.staffer_id=a.apply_staffer_id
                where a.requestid>0 and a.application_status=0 and a.requestid='{$requestid}'
                ";

        $applyOne=$this->DataControl->selectOne($sql);

        if($applyOne){

            $applyOne['remark'] = $applyOne['staffer_cnname'].'在'.date("Y-m-d H:i:s").'课叮铛操作取消流程';

            $applyOne['submitNodeId']='2098';

            $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/cancelAdjustApply",dataEncode($applyOne),"GET");

            if($response){

                $responseInfo=json_decode($response,1);
                $responseInfo=$responseInfo['result']['list'];
                if($responseInfo['code']=='SUCCESS'){
                    return true;
                }elseif($responseInfo['code']=='NO_PERMISSION'){
                    return true;
                }else{

                    return false;
                }

            }else{
                return false;
            }
        }else{
            return false;
        }

    }


//-----------------------------------------------------------------------
//更新泛微流程状态

    function updateAdjustApplyStatus($process_cycletime){

        $time=time()-$process_cycletime*60;

        $sql = "select b.adjustapply_id,a.requestid,c.staffer_employeepid,b.company_id,b.school_id 
                from smc_fanwei_process_generate as a 
                inner join smc_class_hour_adjustapply as b on b.requestid=a.requestid
                inner join smc_staffer as c on c.staffer_id=b.staffer_id
                where b.adjustapply_status=0 and c.staffer_employeepid<>'' 
                and (a.generate_updatatime=0 or a.generate_updatatime<'{$time}')
                group by a.requestid
                order by a.requestid asc
                limit 0,20
                ";

        $applyList=$this->DataControl->selectClear($sql);

        if($applyList){

            foreach($applyList as $applyOne){

                if($applyOne['staffer_employeepid']==''){
                    continue;
                }
//
//                $this->getStafferInfo($applyOne['staffer_employeepid']);
//
//                if($this->error=='10002'){
//
//                    $sql = "select a.*
//                            from smc_class_hour_adjustapply as a
//                            where a.requestid='{$applyOne['requestid']}' and a.adjustapply_status=0
//                            order by a.adjustapply_id asc
//                            ";
//                    $willList=$this->DataControl->selectClear($sql);
//
//                    if($willList){
//                        foreach($willList as $willOne){
//
//                            $publicArray=[
//                                'company_id'=>$willOne['company_id'],
//                                'school_id'=>$willOne['school_id'],
//                                'staffer_id'=>''
//                            ];
//
//                            $ChangeModel = new \Model\Gmc\ChangeModel($publicArray);
//
//                            $data=[
//                                'adjustapply_id'=>$willOne['adjustapply_id'],
//                                'adjustapply_note'=>'员工不存在',
//                                'staffer_id'=>'',
//                                'is_adopt'=>'0',
//                            ];
//
//                            $res = $ChangeModel->examineAdjustapply($data,1);
//
//                            $this->recedeGenerate($willOne['requestid']);
//
//                        }
//
//                    }
//
//
//                    continue;
//
//                }


                $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/getAdjustApplyStatus",dataEncode($applyOne),"GET");

                if($response){

                    $responseInfo=json_decode($response,1);
                    $responseInfo=$responseInfo['result']['list'];

                    $sql = "select a.* 
                            from smc_class_hour_adjustapply as a 
                            where a.requestid='{$applyOne['requestid']}' and a.adjustapply_status=0
                            order by a.adjustapply_id asc
                            ";
                    $willList=$this->DataControl->selectClear($sql);

                    if($willList){
                        foreach($willList as $willOne){
                            if($responseInfo['code']=='SUCCESS'){

                                $publicArray=[
                                    'company_id'=>$willOne['company_id'],
                                    'school_id'=>$willOne['school_id'],
                                    'staffer_id'=>''
                                ];

                                $ChangeModel = new \Model\Gmc\ChangeModel($publicArray);

                                if($responseInfo['data']){
                                    if($responseInfo['data']['currentNodeType']==3){
                                        //通过

                                        $data=[
                                            'adjustapply_id'=>$willOne['adjustapply_id'],
                                            'adjustapply_note'=>'',
                                            'staffer_id'=>'',
                                            'is_adopt'=>'1',
                                        ];

                                        $res = $ChangeModel->examineAdjustapply($data,1);

                                        if(!$res){
                                            $data=[
                                                'adjustapply_id'=>$willOne['adjustapply_id'],
                                                'adjustapply_note'=>'泛微流程同意,'.$ChangeModel->errortip,
                                                'staffer_id'=>'',
                                                'is_adopt'=>'0',
                                            ];

                                            $ChangeModel->examineAdjustapply($data,1);
                                        }


                                    }elseif($responseInfo['data']['currentNodeType']==0){
                                        //拒绝

                                        $data=[
                                            'adjustapply_id'=>$willOne['adjustapply_id'],
                                            'adjustapply_note'=>'泛微流程拒绝',
                                            'staffer_id'=>'',
                                            'is_adopt'=>'0',
                                        ];

                                        $res = $ChangeModel->examineAdjustapply($data,1);

                                    }elseif($responseInfo['data']['currentNodeType']==1){
                                        //原始状态
                                    }

                                    $this->recedeGenerate($willOne['requestid']);
                                }else{
                                    $this->recedeGenerate($willOne['requestid']);
                                }
                            }
                            elseif($responseInfo['code']=='NO_PERMISSION'){
                                $publicArray=[
                                    'company_id'=>$willOne['company_id'],
                                    'school_id'=>$willOne['school_id'],
                                    'staffer_id'=>''
                                ];

                                $ChangeModel = new \Model\Gmc\ChangeModel($publicArray);

                                $data=[
                                    'adjustapply_id'=>$willOne['adjustapply_id'],
                                    'adjustapply_note'=>'无权限拒绝',
                                    'staffer_id'=>'',
                                    'is_adopt'=>'0',
                                ];

                                $res = $ChangeModel->examineAdjustapply($data,1);
                            }else{

                                $this->recedeGenerate($willOne['requestid']);
                            }
                        }

                    }


                }else{
                    $this->recedeGenerate($applyOne['requestid']);
                }
            }
        }

        return true;
    }



    function updateFreeTimesApplyStatus($process_cycletime){

        $time=time()-$process_cycletime*60;

        $sql = "select b.order_pid,a.requestid,c.staffer_employeepid,b.company_id,b.school_id 
                from smc_fanwei_process_generate as a 
                inner join smc_freehour_order as b on b.requestid=a.requestid
                inner join smc_staffer as c on c.staffer_id=b.staffer_id
                where b.order_status=0 and (a.generate_updatatime=0 or a.generate_updatatime<'{$time}')
                group by a.requestid
                order by a.requestid asc
                limit 0,20
                ";

        $applyList=$this->DataControl->selectClear($sql);

        if($applyList){

            foreach($applyList as $applyOne){

                if($applyOne['staffer_employeepid']==''){
                    continue;
                }

//                $this->getStafferInfo($applyOne['staffer_employeepid']);

//                if($this->error=='10002'){
//                    $data=[
//                        'order_pid'=>$applyOne['order_pid'],
//                        'reason'=>'员工不存在',
//                        'staffer_id'=>'',
//                        'is_adopt'=>'0',
//                    ];
//
//                    $res =  $Model->examineHourFreeOrder($data);
//
//                    $this->recedeGenerate($applyOne['requestid']);
//                }

                $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/getAdjustApplyStatus",dataEncode($applyOne),"GET");

                if($response){

                    $responseInfo=json_decode($response,1);
                    $responseInfo=$responseInfo['result']['list'];

                    if($responseInfo['code']=='SUCCESS'){

                        $publicArray=[
                            'company_id'=>$applyOne['company_id'],
                            'school_id'=>$applyOne['school_id'],
                            'staffer_id'=>'',
                        ];

                        $Model = new \Model\Gmc\OrderModel($publicArray);

                        if($responseInfo['data']){
                            if($responseInfo['data']['currentNodeType']==3){
                                //通过

                                $data=[
                                    'order_pid'=>$applyOne['order_pid'],
                                    'reason'=>'泛微流程通过',
                                    'staffer_id'=>'',
                                    'is_adopt'=>'1',
                                    'from'=>'1',
                                ];

                                $res = $Model->examineHourFreeOrder($data);

                                if(!$res){
                                    $data=[
                                        'order_pid'=>$applyOne['order_pid'],
                                        'reason'=> '泛微流程同意,'.$Model->errortip,
                                        'staffer_id'=>'',
                                        'is_adopt'=>'0',
                                    ];
    
                                    $res =  $Model->examineHourFreeOrder($data);
                                }


                            }elseif($responseInfo['data']['currentNodeType']==0){
                                //拒绝

                                $data=[
                                    'order_pid'=>$applyOne['order_pid'],
                                    'reason'=>'泛微流程拒绝',
                                    'staffer_id'=>'',
                                    'is_adopt'=>'0',
                                ];

                                $res =  $Model->examineHourFreeOrder($data);

                            }elseif($responseInfo['data']['currentNodeType']==1){
                                //原始状态
                            }

                            $this->recedeGenerate($applyOne['requestid']);
                        }
                    }
                    elseif($responseInfo['code']=='NO_PERMISSION'){
                        $publicArray=[
                            'company_id'=>$applyOne['company_id'],
                            'school_id'=>$applyOne['school_id'],
                            'staffer_id'=>'',
                        ];

                        $Model = new \Model\Gmc\OrderModel($publicArray);

                        $data=[
                            'order_pid'=>$applyOne['order_pid'],
                            'reason'=>'无权限拒绝',
                            'staffer_id'=>'',
                            'is_adopt'=>'0',
                        ];

                        $res =  $Model->examineHourFreeOrder($data);

                        $this->recedeGenerate($applyOne['requestid']);
                    }


                }else{
                    $this->recedeGenerate($applyOne['requestid']);
                }
            }
        }

        return true;
    }

    function updateRefundApplyStatus($process_cycletime){

        $time=time()-$process_cycletime*60;

        $sql = "select b.refund_pid,a.requestid,c.staffer_employeepid,b.company_id,b.school_id,b.staffer_id 
                from smc_fanwei_process_generate as a 
                inner join smc_refund_order as b on b.requestid=a.requestid
                inner join smc_staffer as c on c.staffer_id=b.staffer_id
                where b.refund_status in (0,1) and (a.generate_updatatime=0 or a.generate_updatatime<'{$time}')
                group by a.requestid
                order by a.requestid asc
                limit 0,20
                ";

        $applyList=$this->DataControl->selectClear($sql);

        if($applyList){

            foreach($applyList as $applyOne){

                if($applyOne['staffer_employeepid']==''){
                    continue;
                }


                $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/getAdjustApplyStatus",dataEncode($applyOne),"GET");

                if($response){

                    $responseInfo=json_decode($response,1);
                    $responseInfo=$responseInfo['result']['list'];

                    if($responseInfo['code']=='SUCCESS'){

                        $publicArray=[
                            'company_id'=>$applyOne['company_id'],
                            'school_id'=>$applyOne['school_id'],
                            'staffer_id'=>$applyOne['staffer_id']
                        ];

                        $Model = new \Model\Gmc\OrderModel($publicArray);

                        if($responseInfo['data']){
                            if($responseInfo['data']['currentNodeType']==3){
                                //通过
                                $data=[
                                    'refund_pid'=>$applyOne['refund_pid'],
                                    'reason'=>'泛微审核通过',
                                    'staffer_id'=>$applyOne['staffer_id'],
                                    'is_adopt'=>'1',
                                    'is_skip'=>'1',
                                ];

                                $res = $Model->examineRefundOrder($data);
                                
                                if(!$res){
                                    $data=[
                                        'refund_pid'=>$applyOne['refund_pid'],
                                        'reason'=> '泛微流程同意,'.$Model->errortip,
                                        'staffer_id'=>$applyOne['staffer_id'],
                                        'is_adopt'=>'0',
                                        'is_skip'=>'1',
                                    ];
    
                                    $res =  $Model->examineRefundOrder($data);
                                }else{
                                    $JindieModel = new \Model\Smc\JindieModel();

                                    $data=array();
                                    $data['refund_pid']=$applyOne['refund_pid'];
                                    $bool=$JindieModel->saveExpenseReimbursement($data);
                                    if($bool){
                                        $data=[
                                            'refund_pid'=>$applyOne['refund_pid'],
                                            'reason'=>'金蝶报销提交成功',
                                            'staffer_id'=>$applyOne['staffer_id'],
                                            'is_adopt'=>'1',
                                            'is_skip'=>'1',
                                        ];

                                        $res = $Model->examineRefundOrder($data);
                                    }

                                }


                            }elseif($responseInfo['data']['currentNodeType']==0){
                                //拒绝

                                $data=[
                                    'refund_pid'=>$applyOne['refund_pid'],
                                    'reason'=>'泛微审核拒绝',
                                    'staffer_id'=>$applyOne['staffer_id'],
                                    'is_adopt'=>'0',
                                    'is_skip'=>'1',
                                ];

                                $res =  $Model->examineRefundOrder($data);

                            }elseif($responseInfo['data']['currentNodeType']==1){
                                //原始状态
                            }

                            $this->recedeGenerate($applyOne['requestid']);
                        }
                    }


                }else{
                    $this->recedeGenerate($applyOne['requestid']);
                }
            }
        }

        return true;
    }

    function updateForwardApplyStatus($process_cycletime){

        $time=time()-$process_cycletime*60;

        $sql = "select b.application_id,a.requestid,c.staffer_employeepid,b.company_id,b.school_id,c.staffer_id 
                from smc_fanwei_process_generate as a 
                inner join smc_forward_application as b on b.requestid=a.requestid
                inner join smc_staffer as c on c.staffer_id=b.apply_staffer_id
                where b.application_status=0 and (a.generate_updatatime=0 or a.generate_updatatime<'{$time}')
                group by a.requestid
                order by a.requestid asc
                limit 0,20
                ";

        $applyList=$this->DataControl->selectClear($sql);

        if($applyList){

            foreach($applyList as $applyOne){

                if($applyOne['staffer_employeepid']==''){
                    continue;
                }


                $response = request_by_curl("https://cmbtestapi.chevady.cn/Fanwei/getAdjustApplyStatus",dataEncode($applyOne),"GET");

                if($response){

                    $responseInfo=json_decode($response,1);
                    $responseInfo=$responseInfo['result']['list'];

                    if($responseInfo['code']=='SUCCESS'){

                        $publicArray=[
                            'company_id'=>$applyOne['company_id'],
                            'school_id'=>$applyOne['school_id'],
                            'staffer_id'=>$applyOne['staffer_id'],
                        ];

                        $Model = new \Model\Gmc\OrderModel($publicArray);

                        if($responseInfo['data']){
                            if($responseInfo['data']['currentNodeType']==3){
                                //通过

                                $data=[
                                    'application_id'=>$applyOne['application_id'],
                                    'reason'=>'泛微审核通过',
                                    'staffer_id'=>$applyOne['staffer_id'],
                                    'is_adopt'=>'1',
                                ];

                                $res = $Model->examineForwardApplication($data);

                                if(!$res){
                                    $data=[
                                        'refund_pid'=>$applyOne['refund_pid'],
                                        'reason'=> '泛微流程同意,'.$Model->errortip,
                                        'staffer_id'=>$applyOne['staffer_id'],
                                        'is_adopt'=>'0',
                                    ];
    
                                    $res =  $Model->examineForwardApplication($data);
                                }


                            }elseif($responseInfo['data']['currentNodeType']==0){
                                //拒绝

                                $data=[
                                    'application_id'=>$applyOne['application_id'],
                                    'reason'=>'泛微审核拒绝',
                                    'staffer_id'=>$applyOne['staffer_id'],
                                    'is_adopt'=>'0',
                                ];

                                $res =  $Model->examineForwardApplication($data);

                            }elseif($responseInfo['data']['currentNodeType']==1){
                                //原始状态
                            }

                            $this->recedeGenerate($applyOne['requestid']);
                        }
                    }


                }else{
                    $this->recedeGenerate($applyOne['requestid']);
                }
            }
        }

        return true;
    }


}