<?php

namespace Model\Smc\Optimized;

/**
 * 班级管理功能 Trait
 * 包含: 班级列表、班级操作、班级信息等
 */
trait ClassManagementTrait
{
/**
     * classList
     */
    function classList($request)
        {
            //缺少上课时间
            $contractOne = $this->getContract($this->companyOne['company_id']);
    
            $datawhere = "c.class_status <> '-2'";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
            }
    
            if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
                $datawhere .= " and sc.coursetype_id = '{$request['coursetype_id']}'";
            }
            if (isset($request['class_type']) && $request['class_type'] !== '') {
                if ($request['class_type'] == '2') {
                    $datawhere .= " and cc.coursetype_isopenclass = '1'";
                } else {
                    $datawhere .= " and c.class_type = '{$request['class_type']}'";
                }
            }
    
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
                $datawhere .= " and sc.coursecat_id = '{$request['coursecat_id']}'";
            }
            if (isset($request['course_id']) && $request['course_id'] !== '') {
                $datawhere .= " and sc.course_id = '{$request['course_id']}'";
            }
            if (isset($request['class_isprepare']) && $request['class_isprepare'] !== '') {
                $datawhere .= " and c.class_isprepare = '{$request['class_isprepare']}'";
            }
    
            if (isset($request['teacher_id']) && $request['teacher_id'] !== '') {
                if ($contractOne && $contractOne['edition_id'] == '2') {
                    $datawhere .= " and exists(select 1 from smc_class_teach as te left join smc_staffer as st on te.staffer_id=st.staffer_id where te.class_id=c.class_id and te.staffer_id='{$request['teacher_id']}' and te.teach_status=0)";
                } else {
                    $datawhere .= " and exists(select 1 from smc_class_hour_teaching as te left join smc_staffer as st on te.staffer_id=st.staffer_id where te.class_id=c.class_id and te.staffer_id='{$request['teacher_id']}' and te.teaching_isdel=0)";
                }
            }
    
            if ($request['staffer_id'] == '27550') {
                $datawhere .= " and sc.coursecat_id in(133,141,11352) ";
            }
    
            if (isset($request['class_status']) && $request['class_status'] !== '') {
                $datawhere .= " and c.class_status = '{$request['class_status']}'";
            }
    
            if (isset($request['class_isfictitious']) && $request['class_isfictitious'] !== '') {
                $datawhere .= " and c.class_isfictitious = '{$request['class_isfictitious']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
            $sql = "SELECT c.class_id,c.course_id,c.class_cnname,c.class_branch,c.class_enname,c.class_timestr,sc.course_cnname,sc.course_branch,c.class_status,c.class_stdate,c.class_fullnums,sc.course_presentednums,cc.coursetype_isopenclass,course_inclasstype,c.class_enddate,c.class_type,c.father_id as p_class_id,sc.course_isopensonclass,sc.course_classnum,sc.course_nextid,sc.course_limitnum,c.class_isprepare
                    ,sc.course_schedule,(select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id and ss.study_isreading='1' limit 0,1) as num
                    ,(SELECT count(DISTINCT(b.student_id)) as aa FROM smc_class_hour as a ,smc_student_hourstudy as b WHERE a.class_id = c.class_id and b.class_id = a.class_id and a.hour_id = b.hour_id GROUP BY a.hour_id ORDER BY a.hour_lessontimes DESC limit 0,1 ) as endnum
                    ,(select 1 from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as agohavestu  
                    ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1 and ssch.hour_iswarming = 0 ) as hournum
                    ,(select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id and hour_ischecking <> -1 and hou.hour_isfree=0) as is_schedule,
                    (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0 and ssch.hour_isfree=0) as no_hournum,
                    (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking <> '-1') as is_hour,
                    ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=1 limit 0,1),0) as breakoff_id
                    ,ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=0 limit 0,1),0) as hasbreakoff
                    ,ifnull((select cl.class_id from smc_class as cl where cl.from_class_id=c.class_id limit 0,1),0) as hasupgrade 
                    ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_hour_teaching as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teaching_isdel=0) as teacherstr
                    ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_teach as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teach_status=0) as jteacherstr
                    ,ifnull((select 1 from smc_student_hourstudy as x,smc_class_hour as y where x.hour_id=y.hour_id and x.class_id=c.class_id and y.hour_isfree=0 limit 0,1),0) as is_atte
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
                    WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype =0 and c.class_status>=-1 
                    group by c.class_id
                    order by c.class_stdate DESC limit {$pagestart},{$num}";
    
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
    
            $rel_classList = array();
            foreach ($classList as $classOne) {
                $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
    FROM smc_class_lessonplan AS l
    LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
    LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
    WHERE l.class_id = '{$classOne['class_id']}' ");
                $cnteacher = array();
                $classroom = array();
                $classtimestr = "";
                if ($contractOne && $contractOne['edition_id'] == '2') {
                    $cnteacher = $classOne['jteacherstr'];
                } else {
                    if ($classlessonList) {
                        foreach ($classlessonList as $classlessonOne) {
    
                            $cnteacher[] = $classlessonOne['staffer_enname'] ? $classlessonOne['staffer_cnname'] . '-' . $classlessonOne['staffer_enname'] : $classlessonOne['staffer_cnname'];
                            $classroom[] = $classlessonOne['classroom_cnname'];
                            $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                        }
    
                        $cnteacher = implode("<br>", array_unique($cnteacher));
                        $classroom = implode("<br>", array_unique($classroom));
                    }
                }
    
                //班级人像采集二维码
                $nowtime = ceil(microtime(true) * 1000);
                $urlparam = base64_encode("class_branch={$classOne['class_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}&opentime={$nowtime}");
                if ($_SERVER['SERVER_NAME'] != 'smcapi.kedingdang.com' && $_SERVER['SERVER_NAME'] != 'gmcapi.kedingdang.com') {
                    $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
                } else {
                    $portraiturl = "https://faceentry.kedingdang.com/?" . $urlparam;
                }
                $portraitclassqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));
    
                //周四 10:00—10:50,周五 15:00—15:50
                $data = array();
                $data['class_id'] = $classOne['class_id'];
                $data['is_atte'] = $classOne['is_atte'];
                $data['coursetype_isopenclass'] = $classOne['coursetype_isopenclass'];
                $data['course_id'] = $classOne['course_id'];
                $data['course_nextid'] = $classOne['course_nextid'];
                $data['class_status'] = $classOne['class_status'];
                $data['class_type'] = $classOne['class_type'];
                $data['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
                $data['is_schedule'] = $classOne['is_schedule'];
                $data['class_cnname'] = $classOne['class_cnname'];
                $data['p_class_id'] = $classOne['p_class_id'];
                $data['class_enname'] = $classOne['class_enname'];
                $data['class_branch'] = $classOne['class_branch'];
                if ($classOne['class_status'] == '-1') {
                    $data['number'] = $classOne['endnum'] . '/' . $classOne['class_fullnums'];
                } else {
                    $data['number'] = $classOne['num'] . '/' . $classOne['class_fullnums'];
                }
                $data['class_fullnums'] = $classOne['class_fullnums'];
                $data['course_limitnum'] = $classOne['course_limitnum'];
                $data['hasNum'] = $classOne['num'];
                $data['course_cnname'] = $classOne['course_cnname'];
                $data['course_branch'] = $classOne['course_branch'];
                $data['course_inclasstype'] = $classOne['course_inclasstype'];
                $data['class_enddate'] = $classOne['class_enddate'];
                $data['course_isopensonclass'] = $classOne['course_isopensonclass'];
                $data['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
                $data['cnteacher'] = $cnteacher ? $cnteacher : $classOne['teacherstr'];
    
                $data['class_isprepare'] = $classOne['class_isprepare'];
                $data['portraitclassqrcode'] = $portraitclassqrcode;
                $data['class_timestr'] = $classtimestr;
                if ($classroom) {
                    $data['classroom_cnname'] = $classroom;
                } else {
                    $data['classroom_cnname'] = '';
                }
                if ($classOne['hournum'] > 0) {
                    if ($classOne['is_schedule']) {
                        $data['info'] = $classOne['hournum'] . '/' . $classOne['is_schedule'];
                    } else {
                        $data['info'] = $classOne['hournum'] . '/' . ($classOne['course_classnum'] + $classOne['course_presentednums']);
                    }
    
                } else {
                    $data['info'] = '0/' . $classOne['is_schedule'];
                }
    
                //是否可以升班
                if ($contractOne && $contractOne['edition_id'] == '2') {
                    if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                        $data['can_promotion'] = 1;
                    } else {
                        $data['can_promotion'] = 0;
                    }
                } else {
                    if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                        $data['can_promotion'] = 1;
                    } else {
                        $data['can_promotion'] = 0;
                    }
                }
    
                //是否可以结班
                if ($contractOne && $contractOne['edition_id'] == '2') {
                    if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0) {
                        $data['can_endclass'] = 1;   //是否可以入班
                    } else {
                        $data['can_endclass'] = 0;
                    }
                } else {
                    if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0) {
                        $data['can_endclass'] = 1;
                    } else {
                        $data['can_endclass'] = 0;
                    }
                }
    
                if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                    $data['is_schedule'] = 1;
                }
    
                if ($classOne['is_schedule'] > 0 && $classOne['no_hournum'] > 0 && $classOne['course_schedule'] == 0) {
                    $data['edit_schedule'] = 1;  //可以修改排课
                } else {
                    $data['edit_schedule'] = 0;
                }
    
                if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                    $data['add_schedule'] = 1;  //是否增加排课
                } else {
                    $data['add_schedule'] = 0;
                }
    
                if ($classOne['breakoff_id'] > 0 && $classOne['class_status'] == '1' && $classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1) {
                    $data['can_break'] = 1;
                } else {
                    $data['can_break'] = 0;
                }
    
                if ($classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1 && $classOne['breakoff_id'] == 0) {
                    $sql = "select hourstudy_id from smc_student_hourstudy where class_id='{$classOne['class_id']}' limit 0,1";
                    if ($this->DataControl->selectOne($sql)) {
                        $data['can_applybreak'] = 1;
                    } else {
                        $data['can_applybreak'] = 0;
                    }
                } else {
                    $data['can_applybreak'] = 0;
                }
    
                $classStatus = $this->LgArraySwitch(array("0" => "待开班", '1' => "进行中", "-1" => "已结束", "-2" => "已删除"));
                $data['class_staus_namme'] = $classStatus[$classOne['class_status']];
    
                $data['agohavestu'] = $classOne['agohavestu']?$classOne['agohavestu']:'0';//以前是否有学生入过班
    
                if($classOne['class_isprepare'] == '1'){
                    $data['class_isprepare_name'] = '是';
    
                    $data['class_timestr'] = '';
                    $data['classroom_cnname'] = '';
                    $data['info'] = '--';
                    $data['class_stdate'] = '';
                    $data['class_staus_namme'] = '进行中';
                }else{
                    $data['class_isprepare_name'] = '否';
                }
    
                $rel_classList[] = $data;
            }
    
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT c.class_id
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id = c.course_id
                    left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
                    WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype = '0' and c.class_status>=-1
                    group by c.class_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
    
            $data['list'] = $rel_classList;
    
            return $data;
        }

/**
     * coursetermClass
     */
    function coursetermClass($request)
        {
            $datawhere = " 1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
            }
    
    
            if (isset($request['course_id']) && $request['course_id'] !== '') {
                $datawhere .= " and sc.course_id = '{$request['course_id']}'";
            }
            if (isset($request['class_status']) && $request['class_status'] !== '') {
                $datawhere .= " and c.class_status = '{$request['class_status']}'";
            }
    
            if (isset($request['class_type']) && $request['class_type'] !== '') {
                $datawhere .= " and c.class_type = '{$request['class_type']}'";
            }
    
            if (isset($request['class_isfictitious']) && $request['class_isfictitious'] !== '') {
                $datawhere .= " and c.class_isfictitious = '{$request['class_isfictitious']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
    //		 (select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as num,
    
            $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,sc.course_cnname,sc.course_branch,c.class_status,sc.course_id,sc.course_classnum,sc.course_schedule,course_inclasstype,c.class_status,c.class_fullnums,sc.course_limitnum,c.class_type,sc.course_isopensonclass,
     			(select count(cb.booking_id) from smc_class_booking as cb where cb.class_id=c.class_id and  cb.booking_status =0) as num,
                group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ))) as cnteacher,
                (select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id and ss.study_isreading='1' limit 0,1) as stuNum,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1) as hournum,
                (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id ) as is_schedule,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0) as no_hournum,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking<>'-1' and hour_iswarming = 0 ) as is_hour_num,
                (select count(d.study_id) from smc_student_study as d where d.class_id=c.class_id and study_isreading=1 ) as study_num
                ,(select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( B.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', B.staffer_enname ) END ))) from smc_class_hour_teaching as A,smc_staffer as B where A.staffer_id=b.staffer_id and A.class_id=c.class_id and A.teaching_isdel=0) as sonTeacher
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                    left join smc_staffer as s on s.staffer_id=ct.staffer_id
                    left join smc_class_hour as sch on sch.class_id=c.class_id
                    WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype ='2' and c.class_status<>'-2'
                    group by c.class_id
                    order by DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                    limit {$pagestart},{$num}";
    
    
            //join smc_code_courseterm as ccm on ccm.courseterm_id=sc.courseterm_id
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $rel_classList = array();
            $arr_class_status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));
            foreach ($classList as $classOne) {
                //班级人像采集二维码
                $urlparam = base64_encode("class_branch={$classOne['class_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
                $portraiturl = "http://faceentry.kcclassin.com/?{$urlparam}";
                $portraitclassqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));
    
                $data = array();
                $data['class_id'] = $classOne['class_id'];
                $data['course_id'] = $classOne['course_id'];
                $data['class_status'] = $classOne['class_status'];
                $data['class_status_name'] = $arr_class_status[$classOne['class_status']];
                $data['is_schedule'] = $classOne['is_schedule'];
                $data['class_fullnums'] = $classOne['class_fullnums'];
                $data['course_limitnum'] = $classOne['course_limitnum'];
                $data['hasNum'] = $classOne['stuNum'];
                $data['class_cnname'] = $classOne['class_cnname'];
                $data['class_enname'] = $classOne['class_enname'];
                $data['class_branch'] = $classOne['class_branch'];
                $data['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
                $data['number'] = $classOne['stuNum'] . '/' . $classOne['class_fullnums'];
                $data['num'] = $classOne['num'];
                $data['course_cnname'] = $classOne['course_cnname'];
                $data['course_branch'] = $classOne['course_branch'];
                $data['cnteacher'] = $classOne['class_type'] == 1 ? $classOne['sonTeacher'] : $classOne['cnteacher'];
                $data['course_inclasstype'] = $classOne['course_inclasstype'];
                $data['is_hour_num'] = intval($classOne['is_hour_num']);
                $data['class_type'] = intval($classOne['class_type']);
                $data['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
                $data['course_isopensonclass'] = $classOne['course_isopensonclass'];
                $data['portraitclassqrcode'] = $portraitclassqrcode;
                if ($classOne['is_schedule'] > 0) {
                    $data['info'] = $classOne['hournum'] . '/' . $classOne['is_hour_num'];
                    if ($classOne['is_hour_num'] > 0) {
    
                        $data['percentageNum'] = ($classOne['hournum'] / $classOne['is_hour_num']) * 100;
                    } else {
                        $data['percentageNum'] = 0;
                    }
                } else {
                    $data['info'] = '0/0';
                    $data['percentageNum'] = 0;
                }
                if ($classOne['course_classnum'] >= $classOne['study_num']) {
                    $data['can_promotion'] = 1;  //是否可以入班
                } else {
                    $data['can_promotion'] = 0;
                }
                if ($classOne['is_schedule'] > 0 and $classOne['no_hournum'] > 0 and $classOne['course_schedule'] == 0) {
                    $data['edit_schedule'] = 1;  //可以修改排课
                } else {
                    $data['edit_schedule'] = 0;
                }
                $rel_classList[] = $data;
            }
    
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT c.class_id
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                    left join smc_staffer as s on s.staffer_id=ct.staffer_id
                    left join smc_class_hour as sch on sch.class_id=c.class_id
                    WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype = '2' and c.class_status<>'-2'
                    group by c.class_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
    
            $data['list'] = $rel_classList;
    
            return $data;
    
        }

/**
     * mothClass
     */
    function mothClass($request)
        {
            $datawhere = " 1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
            }
            if (isset($request['class_type']) && $request['class_type'] !== '') {
                $datawhere .= " and c.class_type = '{$request['class_type']}'";
            }
    
            if (isset($request['course_id']) && $request['course_id'] !== '') {
                $datawhere .= " and sc.course_id = '{$request['course_id']}'";
            }
            if (isset($request['class_status']) && $request['class_status'] !== '') {
                $datawhere .= " and c.class_status = '{$request['class_status']}'";
            }
            if (isset($request['class_isfictitious']) && $request['class_isfictitious'] !== '') {
                $datawhere .= " and c.class_isfictitious = '{$request['class_isfictitious']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
    //		 (select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as num,
    
            $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,sc.course_cnname,sc.course_branch,c.class_status,sc.course_id,sc.course_classnum,sc.course_schedule,course_inclasstype,c.class_fullnums,sc.course_limitnum,sc.course_schedule,c.class_type,c.father_id,
                group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher
                ,course_isopensonclass,c.class_type,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1) as hournum,
                (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id ) as is_schedule,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0) as no_hournum,
                (select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as num,
                (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_isfree='0' and sch.hour_ischecking<>'-1') as hour_num,
                (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_iswarming = '0' and sch.hour_ischecking = '0') as is_hour_num
                    ,(select substring(sch.hour_day,1,7) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_iswarming = '0' order by sch.hour_day desc,sch.hour_starttime desc limit 0,1) as lastMonth
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                    left join smc_staffer as s on s.staffer_id=ct.staffer_id
                    WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype ='1' and  class_status <> '-2'
                    group by c.class_id
                    order by DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                    limit {$pagestart},{$num}";
    
    
            //join smc_code_courseterm as ccm on ccm.courseterm_id=sc.courseterm_id
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $rel_classList = array();
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));
    
            foreach ($classList as $classOne) {
                //班级人像采集二维码
                $urlparam = base64_encode("class_branch={$classOne['class_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
                $portraiturl = "http://faceentry.kcclassin.com/?{$urlparam}";
                $portraitclassqrcode = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));
    
                $data = array();
                $data['class_id'] = $classOne['class_id'];
                $data['course_id'] = $classOne['course_id'];
                $data['class_status'] = $classOne['class_status'];
                $data['class_status_name'] = $status[$classOne['class_status']];
                $data['is_schedule'] = $classOne['is_schedule'];
                $data['class_cnname'] = $classOne['class_cnname'];
                $data['class_enname'] = $classOne['class_enname'];
                $data['class_branch'] = $classOne['class_branch'];
                $data['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
                $data['number'] = $classOne['num'] . '/' . $classOne['class_fullnums'];
                $data['class_fullnums'] = $classOne['class_fullnums'];
                $data['course_limitnum'] = $classOne['course_limitnum'];
                $data['hasNum'] = $classOne['num'];
                $data['course_cnname'] = $classOne['course_cnname'];
                $data['course_branch'] = $classOne['course_branch'];
                if ($classOne['class_type'] == 1) {
                    $class_teach = $this->DataControl->selectOne("select   group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher  from smc_class_teach as th,smc_staffer as s where s.staffer_id=th.staffer_id and class_id='{$classOne['father_id']}' limit 0,1 ");
                    $data['cnteacher'] = $class_teach['cnteacher'];
                } else {
                    $data['cnteacher'] = $classOne['cnteacher'];
                }
                $data['course_isopensonclass'] = $classOne['course_isopensonclass'];
                $data['course_inclasstype'] = $classOne['course_inclasstype'];
                $data['class_type'] = $classOne['class_type'];
                $data['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
                if ($classOne['course_schedule'] == 1) {
                    $data['is_hour_num'] = 0;
                } else {
                    $data['is_hour_num'] = intval($classOne['is_hour_num']);   //大于0 可以修改排课
                }
                if ($classOne['is_schedule'] > 0) {
                    $data['info'] = $classOne['hournum'] . '/' . $classOne['hour_num'];
                    if ($classOne['hour_num'] > 0) {
                        $data['percentageNum'] = ($classOne['hournum'] / $classOne['hour_num']) * 100;
                    } else {
                        $data['percentageNum'] = 0;
                    }
                } else {
                    $data['info'] = '0/0';
                    $data['percentageNum'] = 0;
                }
    
                if ($classOne['hournum'] == $classOne['course_classnum'] || $classOne['num'] == 0) {
                    $data['can_promotion'] = 1;
                } else {
                    $data['can_promotion'] = 0;
                }
    
                $data['edit_schedule'] = 0;
    
                if ($classOne['is_hour_num'] == 0 && $classOne['class_status'] != '-1' and $classOne['lastMonth'] < date("Y-m")) {
                    $data['can_endclass'] = 1;
                } else {
                    $data['can_endclass'] = 0;
                }
                $data['portraitclassqrcode'] = $portraitclassqrcode;
    
                $rel_classList[] = $data;
            }
    
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT c.class_id
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                    left join smc_staffer as s on s.staffer_id=ct.staffer_id
                    WHERE {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.course_inclasstype = 1 and  class_status <> '-2'
                    group by c.class_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
    
            $data['list'] = $rel_classList;
    
            return $data;
    
        }
    
        /**
         * 班级管理-公开课列表
         * author: ling
         * 对应接口文档 0001
         * Date 2021/1/19 0019
         * @param $request
         */

/**
     * getOpenClass
     */
    function getOpenClass($request)
        {
            $datawhere = " c.school_id='{$request['school_id']}' and c.class_status <> '-2' and ty.coursetype_isopenclass =1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
            }
            if (isset($request['class_type']) && $request['class_type'] !== '') {
                $datawhere .= " and c.class_type = '{$request['class_type']}'";
            }
            if (isset($request['course_id']) && $request['course_id'] !== '') {
                $datawhere .= " and sc.course_id = '{$request['course_id']}'";
            }
            if (isset($request['class_status']) && $request['class_status'] !== '') {
                $datawhere .= " and c.class_status = '{$request['class_status']}'";
            }
            if (isset($request['teacher_id']) && $request['teacher_id'] !== '') {
                $datawhere .= " and s.staffer_id = '{$request['teacher_id']}'";
            }
            if (isset($request['course_openclasstype']) && $request['course_openclasstype'] !== '') {
                $datawhere .= " and sc.course_openclasstype = '{$request['course_openclasstype']}'";
            }
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
    
            $pagestart = ($page - 1) * $num;
            $sql = "SELECT c.class_id,c.class_cnname,c.class_branch,c.class_enname,c.class_stdate,c.class_appointnum,sc.course_cnname,sc.course_branch,c.class_status,c.class_timestr,sc.course_id,sc.course_classnum,sc.course_schedule,sc.course_inclasstype,sc.course_openclasstype,c.class_fullnums,sc.course_limitnum,class_appointnum,sc.course_schedule,
                group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher
                ,course_isopensonclass,c.class_type,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1) as hournum,
                (select count(hou.hour_id) from smc_class_hour as hou where hou.class_id=c.class_id ) as is_schedule,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0) as no_hournum,
                (select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as num,
                (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking<>'-1') as hour_num,
                (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking = '0') as is_hour_num,
                (select hour_id from smc_class_hour as hd where hd.class_id=c.class_id  and hd.hour_ischecking =1 limit 0,1 ) as is_hourstudy,
                (select audition_id from smc_class_hour_audition as au  where au.class_id= c.class_id  limit 0,1) as audition_id
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                    left join smc_staffer as s on s.staffer_id=ct.staffer_id
                    left join smc_class_hour as sch on sch.class_id=c.class_id
                    left join smc_code_coursetype as ty ON ty.coursetype_id=sc.coursetype_id
                    WHERE {$datawhere}  
                    group by c.class_id
                    order by field(c.class_status,'-1') asc,DATE_FORMAT(c.class_stdate, '%Y-%m-%d') DESC
                    limit {$pagestart},{$num}";
    
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $rel_classList = array();
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));
            $status1 = $this->LgArraySwitch(array('0' => '普通公开课', '1' => '试读公开课'));
            foreach ($classList as &$classOne) {
                $classOne['class_appointnum'] = ($classOne['class_appointnum'] == '0') ? "不限" : $classOne['class_appointnum'];
                $classOne['course_openclasstype_name'] = $status1[$classOne['course_openclasstype']];
    
                $classlessonList = $this->DataControl->selectClear("SELECT l.lessonplan_week, l.lessonplan_starttime, l.lessonplan_endtime,c.classroom_cnname,s.staffer_cnname,s.staffer_enname
    FROM smc_class_lessonplan AS l
    LEFT JOIN smc_staffer AS s ON s.staffer_id = l.staffer_id
    LEFT JOIN smc_classroom AS c ON c.classroom_id = l.classroom_id
    WHERE l.class_id = '{$classOne['class_id']}' ");
                $cnteacher = array();
                $classroom = array();
                $classtimestr = "";
    
                if ($classlessonList) {
                    foreach ($classlessonList as $classlessonOne) {
    
                        $cnteacher[] = $classlessonOne['staffer_enname'] ? $classlessonOne['staffer_cnname'] . '-' . $classlessonOne['staffer_enname'] : $classlessonOne['staffer_cnname'];
                        $classroom[] = $classlessonOne['classroom_cnname'];
                        $classtimestr .= $classlessonOne['lessonplan_week'] . " " . $classlessonOne['lessonplan_starttime'] . "~" . $classlessonOne['lessonplan_endtime'] . "<br>";
                    }
    
                    $cnteacher = implode(" ", array_unique($cnteacher));
                    $classroom = implode(" ", array_unique($classroom));
                }
    
                if ($cnteacher) {
                    $classOne['cnteacher'] = $cnteacher;
                } else {
                    $classOne['cnteacher'] = '';
                }
    //            $classOne['class_timestr'] = $classtimestr;
    
                if ($classroom) {
                    $classOne['classroom_cnname'] = $classroom;
                } else {
                    $classOne['classroom_cnname'] = '';
                }
    
    
                $classOne['class_stdate'] = date("Y-m-d", strtotime($classOne['class_stdate']));
    
                $classOne['class_status_name'] = $status[$classOne['class_status']];
                $classOne['class_type_name'] = $classOne['class_type'] == 1 ? '子班' : '父班';
                $classOne['is_hour_num'] = intval($classOne['is_hour_num']);
                if ($classOne['is_schedule'] > 0) {
                    $classOne['info'] = $classOne['hournum'] . '/' . $classOne['hour_num'];
                    $classOne['percentageNum'] = $classOne['hour_num'] > 0 ? ($classOne['hournum'] / $classOne['hour_num']) * 100 : 0;
                } else {
                    $classOne['info'] = '0/0';
                    $classOne['percentageNum'] = 0;
                }
                $temp_schedule = $classOne['is_schedule'];
                $classOne['is_editclass'] = $classOne['class_status'] > -1 ? 1 : 0; //是否可以编辑班级
                $classOne['is_schedule'] = intval($temp_schedule) > 0 ? 0 : 1; // 是否可以排課
                if ($classOne['course_schedule'] == 1) {
                    $classOne['edit_schedule'] = 0;
                } else {
                    $classOne['edit_schedule'] = intval($temp_schedule) > 0 ? 1 : 0; // 是否可以编辑排課
                }
    
                if ($temp_schedule > 0 and $classOne['class_status'] >= 0) {
                    $classOne['add_schedule'] = '1';
                } else {
                    $classOne['add_schedule'] = '0';
                }
    
                if ($classOne['course_openclasstype'] == 1) {
                    if ($classOne['class_status'] >= 0) {
                        $classOne['is_endclass'] = '1';
                    } else {
                        $classOne['is_endclass'] = '0';
                    }
                } else {
                    if ($classOne['is_hourstudy'] > 0 && $classOne['is_hour_num'] == 0 && $classOne['class_status'] >= 0) {
                        $classOne['is_endclass'] = '1';
                    } else {
                        $classOne['is_endclass'] = '0';
                    }
    
                }
    
    
                $classOne['is_del'] = 1;  // 是否可以删除
                $rel_classList[] = $classOne;
            }
    
            $data = array();
    
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT c.class_id
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_class_teach as ct on ct.class_id = c.class_id and teach_status = 0 and teach_type =0
                    left join smc_staffer as s on s.staffer_id=ct.staffer_id
                    left join smc_class_hour as sch on sch.class_id=c.class_id
                    left join smc_code_coursetype as ty ON ty.coursetype_id=sc.coursetype_id
                    WHERE {$datawhere}
                    group by c.class_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $courseList = $this->DataControl->selectClear("SELECT sc.course_id,sc.course_branch,sc.course_cnname
                    FROM smc_class as c
                    left join smc_course as sc on sc.course_id=c.course_id
                    left join smc_code_coursetype as ty ON ty.coursetype_id=sc.coursetype_id
                    where c.school_id='{$request['school_id']}' and c.class_status <> '-2' and ty.coursetype_isopenclass =1
                    ");
            $data['list'] = $rel_classList;
            $data['course_list'] = is_array($courseList) ? $courseList : array();
    
            return $data;
        }

/**
     * getClassMenu
     */
    function getClassMenu($request)
        {
            $sql = "select c.class_status,sc.course_nextid,ct.coursetype_isopenclass
                  ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id='{$request['class_id']}' and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}' and ss.study_isreading='1') as study_num
                  ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}') as all_hour_num
                  ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='1') as has_hour_num
                  ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0') as no_hour_num
                  ,(select ch.hour_ischecking from smc_class_hour as ch where ch.class_id='{$request['class_id']}' order by ch.hour_lessontimes desc limit 0,1) as last_hour_status
                  ,(select scl.changelog_id from smc_student_changelog as scl where scl.company_id='{$request['company_id']}' and scl.class_id='{$request['class_id']}' and scl.school_id='{$request['school_id']}') as change_num
                  from smc_class as c
                  left join smc_course as sc on sc.course_id=c.course_id
                  left join smc_code_coursetype as ct on ct.coursetype_id=sc.coursetype_id
                  where c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}'";
            $classOne = $this->DataControl->selectOne($sql);
            $data = array();
            $data['can_classEntry'] = 1;
            $data['can_edit'] = 1;
            $data['can_class_payment'] = 1;
            if ($classOne['class_status'] >= 0 && $classOne['study_num'] > 0) {
                $data['can_dismantle_classes'] = 1;
            } else {
                $data['can_dismantle_classes'] = 0;
            }
    
            if ($classOne['change_num'] > 0 || $classOne['study_num'] > 0) {
                $data['can_del'] = 0;
            } else {
                $data['can_del'] = 1;
            }
    
            $audition = $this->DataControl->selectClear("select ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking from smc_class_hour_audition as cha left join smc_class_hour as ch on ch.hour_id=cha.hour_id where class_id='{$request['class_id']}'
                                                      ");
    
            if ($audition) {
                foreach ($audition as $val) {
                    $now = date("Y-m-d H:i:s", time());
                    $auditionTime = $val['hour_day'] . " " . $val['hour_endtime'];
                    if ($val['hour_ischecking'] == 0 && ($now <= $auditionTime)) {
                        $data['can_del'] = 0;
                    }
                }
            }
    
            if ($classOne['all_hour_num'] > 0) {
                $data['can_schedule'] = 0;
                $data['can_edit_schedule'] = 1;
            } else {
                $data['can_schedule'] = 1;
                $data['can_edit_schedule'] = 0;
            }
    
            if ($classOne['no_hour_num'] == 0 && $classOne['course_nextid'] != '') {
                $data['can_knot'] = 1;
                $data['can_promotion'] = 1;
            } else {
                $data['can_knot'] = 0;
                $data['can_promotion'] = 0;
            }
    
            if ($classOne['class_status'] >= 0 && $classOne['course_nextid'] != '') {
                $data['can_prefabrication'] = 1;
            } else {
                $data['can_prefabrication'] = 0;
            }
    
            if ($classOne['no_hour_num'] == 0 && $classOne['course_nextid'] == '') {
                $data['can_graduation'] = 1;
            } else {
                $data['can_graduation'] = 0;
            }
    
            if ($classOne['all_hour_num'] > 0 && ($classOne['coursetype_isopenclass'] == '1' || $classOne['course_inclasstype'] == '2')) {
                $data['can_add_schedule'] = 1;
            } else {
                $data['can_add_schedule'] = 0;
            }
    
            return $data;
    
        }

/**
     * classOne
     */
    function classOne($request)
        {
    
            
            $contractOne = $this->getContract($this->companyOne['company_id']);
    
            $sql = "SELECT  c.class_id,c.class_cnname,c.class_enname,c.class_branch,sc.course_cnname,sc.course_branch,c.class_stdate,c.class_enddate,sc.course_id,c.class_fullnums,ct.coursetype_isopenclass,sc.course_inclasstype,c.class_type,sc.course_isopensonclass,sc.course_checkingintype,c.father_id,sc.course_isrenew,sc.course_isrenewprice,course_checkingminday,course_minabsencenum,c.class_isfictitious,c.class_isupgrade,c.from_class_id,sc.coursetype_id,sc.course_opensonmode,sc.course_openclasstype,c.class_isprepare,c.class_hourwarmnums,c.class_hourwarmapplynums,c.class_hourreviewnums,c.class_hourreviewapplynum,sc.course_isopenwarm,sc.course_isopenreview,sc.course_canapplywarm,sc.course_canapplyreview,sc.course_islimitwarmtime,sc.course_warmtimerange,sc.course_islimitreviewtime,sc.course_reviewtimerange,sc.course_nextid,sc.course_limitnum,c.class_status
                 ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking <> -1 and ssch.hour_iswarming =0 ) as course_classnum
                   ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and  ssch.hour_isfree = 1 and ssch.hour_ischecking =1 ) as free_hournum,
                   (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and  ssch.hour_isfree = 1 and ssch.hour_ischecking <> -1 ) as free_alrhournum,
                (select count(ss.study_id) from smc_student_study as ss where ss.class_id = c.class_id and study_isreading = 1 ) as study_num,
                (select count(cb.booking_id) from smc_class_booking as cb where cb.class_id = c.class_id and cb.booking_status = 0) as booking_num,
                (select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=1 and  ssch.hour_isfree=0  and ssch.hour_iswarming =0) as hournum,
                ifnull((select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking<>-1 and  ssch.hour_isfree=1  and ssch.hour_iswarming =1),0) as warmnum,
                ifnull((select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking<>-1 and  ssch.hour_isfree=1  and ssch.hour_iswarming =2),0) as reviewnum,
                ifnull(cl.class_cnname,'') as class_parent_cnname,
                ifnull(cl.class_branch,'') as class_parent_branch,
                ifnull(cl.class_id,0) as class_parent_classid,
                (select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) from smc_class_teach as t left join smc_staffer as s on s.staffer_id=t.staffer_id where (t.class_id=c.class_id or t.class_id = c.father_id) and t.teach_type=0 and t.teach_status =0 and s.staffer_leave=0) as cnteacher,
                (select group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ) ) ) from smc_class_teach as te left join smc_staffer as st on st.staffer_id=te.staffer_id where (te.class_id=c.class_id or te.class_id = c.father_id) and te.teach_status =0 and te.teach_type=1 and st.staffer_leave=0) as enteacher,
                (select group_concat(distinct(ro.classroom_cnname)) from smc_class_hour as cl left join smc_classroom as ro on ro.classroom_id=cl.classroom_id where cl.class_id=c.class_id) as classroom_cnname
                ,ifnull((select 1 from smc_student_hourstudy as x,smc_class_hour as y where x.hour_id=y.hour_id and x.class_id=c.class_id and y.hour_isfree=0 limit 0,1),0) as is_atte
                ,if(sc.course_islimitopencycle=1,sc.course_limitopencyclenum,0) as course_limitopencyclenum,a.school_isskipFw
                ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking <> '-1') as is_hour
                ,(select 1 from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id limit 0,1) as agohavestu  
                ,ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=1 limit 0,1),0) as breakoff_id
                ,ifnull((select cb.breakoff_id from smc_class_breakoff as cb where cb.class_id=c.class_id and cb.breakoff_status>=0 limit 0,1),0) as hasbreakoff
                ,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=c.class_id and ssch.hour_ischecking=0 and ssch.hour_isfree=0) as no_hournum
                ,ifnull((select cl.class_id from smc_class as cl where cl.from_class_id=c.class_id limit 0,1),0) as hasupgrade 
                ,(select count(distinct ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.school_id=c.school_id and ss.study_isreading='1' limit 0,1) as num
                ,(SELECT count(DISTINCT(b.student_id)) as aa FROM smc_class_hour as a ,smc_student_hourstudy as b WHERE a.class_id = c.class_id and b.class_id = a.class_id and a.hour_id = b.hour_id GROUP BY a.hour_id ORDER BY a.hour_lessontimes DESC limit 0,1 ) as endnum
                FROM smc_class as c
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_code_coursetype as ct on ct.coursetype_id = sc.coursetype_id
                left join smc_class as cl on c.father_id=cl.class_id
                left join smc_school as a on a.school_id=c.school_id
                WHERE c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}'
                order by c.class_createtime DESC";
    
    
            $classOne = $this->DataControl->selectOne($sql);
    
            if($classOne['school_isskipFw']==1 && $classOne['course_limitopencyclenum']>0){
                $classOne['course_limitopencyclenum']+=100;
            }
    
    
            $inttype = $this->LgArraySwitch(array('0' => '课次考勤(缺勤计费)', '1' => '连续缺勤', '2' => '自然周考勤', '3' => '月度考勤', '4' => '累计缺勤', '5' => '课次考勤(缺勤免费)'));
            $number = '';
            if ($classOne['course_checkingintype'] == 1) {
                $number .= '_' . $classOne['course_checkingminday'] . '次';
            } elseif ($classOne['course_checkingintype'] == 4) {
                $number .= '_' . $classOne['course_minabsencenum'] . '次';
            }
            $classOne['course_checkingintype_name'] = $inttype[$classOne['course_checkingintype']] . $number;
            $teachList = $this->DataControl->selectClear(
                "select st.staffer_id,st.staffer_cnname,st.staffer_enname,t.teach_type
                from smc_class_teach as t 
                left join smc_class as c On t.class_id=c.class_id
                left join smc_staffer as st ON st.staffer_id = t.staffer_id
                where (c.class_id='{$request['class_id']}'  or c.class_id ='{$classOne['father_id']}') and t.teach_status =0 and st.staffer_id > 0 group by st.staffer_id");
    
            if (!$classOne['classroom_cnname']) {
                $classOne['classroom_cnname'] = '--';
            }
    
            if (!$classOne['free_hournum']) {
                $classOne['free_hournum'] = '0';
            }
            if (!$classOne['free_alrhournum']) {
                $classOne['free_alrhournum'] = '0';
            }
    
            $classOne['comapny_isclocking'] = $this->companyOne['comapny_isclocking'];
    
            
    
            $classOne['left_warmnum'] = $classOne['class_hourwarmnums']+$classOne['class_hourwarmapplynums']-$classOne['warmnum'];
            $classOne['left_reviewnum'] = $classOne['class_hourreviewnums']+$classOne['class_hourreviewapplynum']-$classOne['reviewnum'];
    
            if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                $classOne['add_schedule'] = 1;  //是否增加排课
            } else {
                $classOne['add_schedule'] = 0;
            }
            
            $classOne['agohavestu'] = $classOne['agohavestu']?$classOne['agohavestu']:'0';//以前是否有学生入过班
    
            if ($classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1 && $classOne['breakoff_id'] == 0) {
                $sql = "select hourstudy_id from smc_student_hourstudy where class_id='{$classOne['class_id']}' limit 0,1";
                if ($this->DataControl->selectOne($sql)) {
                    $classOne['can_applybreak'] = 1;
                } else {
                    $classOne['can_applybreak'] = 0;
                }
            } else {
                $classOne['can_applybreak'] = 0;
            }
            
    
            if ($classOne['breakoff_id'] > 0 && $classOne['class_status'] == '1' && $classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1) {
                $classOne['can_break'] = 1;
            } else {
                $classOne['can_break'] = 0;
            }
    
            if ($contractOne && $contractOne['edition_id'] == '2') {
                if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                    $classOne['can_promotion'] = 1;
                } else {
                    $classOne['can_promotion'] = 0;
                }
            } else {
                if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0 && $classOne['hasupgrade'] == 0 && $classOne['course_nextid'] != '') {
                    $classOne['can_promotion'] = 1;
                } else {
                    $classOne['can_promotion'] = 0;
                }
            }
    
            if ($contractOne && $contractOne['edition_id'] == '2') {
                if ($classOne['class_enddate'] <= date("Y-m-d") && $classOne['num'] > 0) {
                    $classOne['can_endclass'] = 1;   //是否可以入班
                } else {
                    $classOne['can_endclass'] = 0;
                }
            } else {
                if (($classOne['hournum'] >= $classOne['is_schedule'] || $classOne['num'] == 0) && $classOne['hasbreakoff'] == 0) {
                    $classOne['can_endclass'] = 1;
                } else {
                    $classOne['can_endclass'] = 0;
                }
            }
    
            if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                $classOne['is_schedule'] = 1;
            }
    
            if ($classOne['is_schedule'] > 0 && $classOne['no_hournum'] > 0 && $classOne['course_schedule'] == 0) {
                $classOne['edit_schedule'] = 1;  //可以修改排课
            } else {
                $classOne['edit_schedule'] = 0;
            }
    
            if ($classOne['is_schedule'] > 0 && $classOne['no_hournum'] > 0 && $classOne['course_schedule'] == 0) {
                $classOne['edit_schedule'] = 1;  //可以修改排课
            } else {
                $classOne['edit_schedule'] = 0;
            }
    
            if ($classOne['is_hour'] > 0 && $classOne['coursetype_isopenclass'] == 1) {
                $classOne['add_schedule'] = 1;  //是否增加排课
            } else {
                $classOne['add_schedule'] = 0;
            }
    
            if ($classOne['breakoff_id'] > 0 && $classOne['class_status'] == '1' && $classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1) {
                $classOne['can_break'] = 1;
            } else {
                $classOne['can_break'] = 0;
            }
    
            if ($classOne['coursetype_isopenclass'] != 1 && $classOne['class_type'] != 1 && $classOne['breakoff_id'] == 0) {
                $sql = "select hourstudy_id from smc_student_hourstudy where class_id='{$classOne['class_id']}' limit 0,1";
                if ($this->DataControl->selectOne($sql)) {
                    $classOne['can_applybreak'] = 1;
                } else {
                    $classOne['can_applybreak'] = 0;
                }
            } else {
                $classOne['can_applybreak'] = 0;
            }
    
    
            $data = array();
            $data['list'] = $classOne;
            $tempinfo = array();
            $tempinfo ['fu_staffer'] = array();
            $tempinfo ['mian_staffer'] = array();
            if ($teachList) {
                foreach ($teachList as $key => $value) {
                    if ($value['teach_type'] == 0) {
                        if (trim($value['staffer_enname']) !== '') {
                            if ($value['staffer_enname'] == $value['staffer_cnname']) {
                                $main_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                            } else {
                                $main_tempinfo['staffer_cnname'] = $value['staffer_cnname'] . '-' . $value['staffer_enname'];
                            }
                        } else {
                            $main_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                        }
                        //$main_tempinfo['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                        $main_tempinfo['staffer_id'] = $value['staffer_id'];
                        $tempinfo ['mian_staffer'][] = $main_tempinfo;
                    } else {
                        if (trim($value['staffer_enname']) !== '') {
                            if ($value['staffer_enname'] == $value['staffer_cnname']) {
                                $fu_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                            } else {
                                $fu_tempinfo['staffer_cnname'] = $value['staffer_cnname'] . '-' . $value['staffer_enname'];
                            }
                        } else {
                            $fu_tempinfo['staffer_cnname'] = $value['staffer_cnname'];
                        }
                        //$fu_tempinfo['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                        $fu_tempinfo['staffer_id'] = $value['staffer_id'];
                        $tempinfo ['fu_staffer'][] = $fu_tempinfo;
                    }
                }
    
            }
            $data['info'] = $tempinfo;
            return $data;
        }

/**
     * classTeacherInfo
     */
    function classTeacherInfo($request)
        {
    
            $datawhere = "s.staffer_id = t.staffer_id AND t.class_id = '{$request['class_id']}'";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or s.staffer_mobile like '%{$request['keyword']}%')";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "SELECT s.staffer_id, s.staffer_cnname, s.staffer_enname, s.staffer_branch, s.staffer_sex, s.staffer_mobile,min(t.teach_status) as teach_status,s.staffer_leave
                    ,(SELECT ct.teachtype_name FROM smc_code_teachtype as ct,smc_class_hour_teaching as ht 
                        WHERE ct.teachtype_code = ht.teachtype_code AND ht.class_id = t.class_id AND ht.staffer_id = s.staffer_id and ct.company_id='{$request['company_id']}' limit 0,1) as teachtype_name
                    ,(SELECT cp.post_name FROM gmc_company_post as cp,gmc_staffer_postbe as sp 
                        WHERE cp.post_id = sp.post_id AND sp.school_id = '{$request['school_id']}' AND sp.staffer_id = s.staffer_id LIMIT 0,1) as post_name
                    ,( SELECT count(te.teaching_id) FROM smc_class_hour_teaching AS te,smc_class_hour as y WHERE te.hour_id=y.hour_id and te.staffer_id = s.staffer_id AND te.class_id = t.class_id and y.hour_ischecking>=0 and y.hour_iswarming=0) AS classNum 
                    ,ifnull(( SELECT count(x.hour_id) FROM smc_class_hour AS x WHERE x.class_id = t.class_id AND x.hour_iswarming=0 and x.hour_ischecking>=0),0) AS allHourNum 
                    ,ifnull(( SELECT count(x.hour_id) FROM smc_class_hour AS x WHERE x.class_id = t.class_id AND x.hour_iswarming=0  and x.hour_ischecking=1 and x.hour_staffer_id=s.staffer_id),0) AS realCheckNum 
                    ,ifnull(( SELECT count(x.hour_id) FROM smc_class_hour_teaching as x,smc_class_hour AS y WHERE x.hour_id=y.hour_id and x.teaching_type=0 and x.class_id = t.class_id AND y.hour_iswarming=0  and y.hour_ischecking=0 and x.staffer_id=s.staffer_id),0) AS planCheckNum 
                    FROM smc_staffer AS s,smc_class_teach AS t 
                    WHERE {$datawhere}
                    GROUP BY t.class_id,t.staffer_id,t.teach_type 
                    ORDER BY t.teach_status ASC,s.staffer_leave ASC 
                    limit {$pagestart},{$num}";
    
            $infoList = $this->DataControl->selectClear($sql);
            if (!$infoList) {
                $this->error = true;
                $this->errortip = "无教师数据";
                return false;
            } else {
                foreach ($infoList as &$infoOne) {
    
                    if ($infoOne['staffer_leave'] == '1') {
                        $infoOne['staffer_leave'] = '离职';
                        $infoOne['teach_status']=1;
                    } else {
                        $infoOne['staffer_leave'] = '在职';
                    }
    
                    if ($infoOne['teach_status'] == '1') {
                        $infoOne['teach_status'] = '已解除';
                    } else {
                        $infoOne['teach_status'] = '带班中';
                    }
    
                    if($infoOne['allHourNum']>0){
                        $infoOne['classNum'] = $infoOne['classNum'].'('.round((($infoOne['realCheckNum']+$infoOne['planCheckNum']) / $infoOne['allHourNum']) * 100, 2) . '%)';
    
                    }else{
                        $infoOne['classNum'] = $infoOne['classNum'].'(--)';
                    }
    
                }
            }
    
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
    
                $sql = "SELECT s.staffer_id 
                        FROM smc_staffer AS s,smc_class_teach AS t 
                        WHERE {$datawhere} 
                        GROUP BY t.class_id,t.staffer_id,t.teach_type ";
    
                $dbNums = $this->DataControl->selectClear($sql);
                $allnum=$dbNums?count($dbNums):0;
                $data['allnum'] = $allnum;
            }
            $data['list'] = $infoList;
    
            return $data;
        }

/**
     * classRefreshSort
     */
    function classRefreshSort($request)
        {
            if ($request['class_id']) {
                $sql = "call RefreshClass('{$request['class_id']}')";
    
    //            mysqli_query($sql);
                $this->DataControl->query($sql);
                $this->error = 0;
                $this->errortip = "刷新成功！";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "刷新失败！";
                return false;
            }
    
        }

/**
     * classCourseInfo
     */
    function classCourseInfo($request)
        {
            $coursetypeOne = $this->DataControl->selectOne("select ct.coursetype_isopenclass,course_inclasstype from smc_course as co,smc_code_coursetype as ct where co.coursetype_id = ct.coursetype_id and  co.course_id ='{$request['course_id']}'");
            if ($coursetypeOne['coursetype_isopenclass'] == 0) {
                $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);
                if (!$pricingOne) {
                    $this->error = true;
                    $this->errortip = "无效课程";
                    return false;
                }
            }
            $pricingOne['course_inclasstype'] = $coursetypeOne['course_inclasstype'];
            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_branch,course_presentednums,course_classnum,course_inclasstype,course_limitamout,course_islimitamout,course_islimitweeks,course_earlynum,course_laternum", "company_id='{$request['company_id']}' and course_id='{$request['course_id']}'");
    
            $pricingOne['pricing_addtime'] = date("Y-m-d H:i:s", $pricingOne['tuition_addtime']);
            $pricingOne['course_branch'] = $courseOne['course_branch'];
            $pricingOne['course_classnum'] = $courseOne['course_classnum'];
            $pricingOne['course_presentednums'] = $courseOne['course_presentednums'];
            $pricingOne['course_allnums'] = $courseOne['course_presentednums'] + $courseOne['course_classnum'];
            $pricingOne['coursetype_isopenclass'] = $coursetypeOne['coursetype_isopenclass'];
            $pricingOne['course_limitamout'] = $courseOne['course_limitamout'];
            $pricingOne['course_islimitamout'] = $courseOne['course_islimitamout'];
            $pricingOne['course_earlynum'] = $courseOne['course_earlynum'];
            $pricingOne['course_laternum'] = $courseOne['course_laternum'];
            $pricingOne['course_islimitweeks'] = $courseOne['course_islimitweeks'];
    
            if ($courseOne['course_islimitweeks'] == 1) {
                $weeksList = $this->DataControl->getList("smc_course_weeks", "course_id='{$request['course_id']}' order by weeks_id asc");
    
                $pricingOne['weeksList'] = $weeksList ? $weeksList : array();
            } else {
    
                $data = array();
                $k = 0;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周一');
                $k++;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周二');
                $k++;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周三');
                $k++;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周四');
                $k++;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周五');
                $k++;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周六');
                $k++;
                $data[$k]['weeks_branch'] = $this->LgStringSwitch('周日');
                $k++;
    
                $pricingOne['weeksList'] = $data;
            }
    
    
            if ($courseOne['course_inclasstype'] == 2) {
                if ($fitOne = $this->DataControl->getFieldOne("smc_fee_pricing_fit", "fit_buypiece", "tuition_id='{$pricingOne['tuition_id']}' and fit_isdefault =1")) {
                    $pricingOne['tuition_buypiece'] = $fitOne['fit_buypiece'];
                    $pricingOne['course_classnum'] = $fitOne['fit_buypiece'];
                }
            }
    
            $pricingOne['isskipweek'] = 0;
            if (isset($request['class_id']) && $request['class_id'] !== '') {
                $classlesson = $this->DataControl->selectOne("select x.lessonplan_isskipweek,y.class_appointnum from  smc_class_lessonplan x,smc_class y where x.class_id=y.class_id and x.class_id='{$request['class_id']}' limit 0,1 ");
                if ($classlesson) {
                    $pricingOne['isskipweek'] = $classlesson['lessonplan_isskipweek'];
                    if ($courseOne['course_inclasstype'] == 2) {
                        $pricingOne['class_appointnum'] = $classlesson['class_appointnum'];
                    }
                }
            }
            $courseOne = $this->DataControl->selectOne("select course_opensonmode,course_islimittime,course_limittime,course_offline_main_percentage,course_offline_sub_percentage,course_online_main_percentage,course_online_sub_percentage  from smc_course where course_id='{$request['course_id']}' ");
            if ($courseOne) {
                $pricingOne['course_opensonmode'] = $courseOne['course_opensonmode'];
                $pricingOne['course_islimittime'] = $courseOne['course_islimittime'];
                $pricingOne['course_limittime'] = $courseOne['course_limittime'];
                $pricingOne['course_offline_main_percentage'] = $courseOne['course_offline_main_percentage'];
                $pricingOne['course_offline_sub_percentage'] = $courseOne['course_offline_sub_percentage'];
                $pricingOne['course_online_main_percentage'] = $courseOne['course_online_main_percentage'];
                $pricingOne['course_online_sub_percentage'] = $courseOne['course_online_sub_percentage'];
            }
    
            //这个班 最后一个被邀约的课是什么时间
            $lastAuditionHourTime = $this->DataControl->selectOne("SELECT a.hour_day
                FROM smc_class_hour as a,crm_client_audition as b
                WHERE a.class_id = '{$request['class_id']}' and a.hour_id = b.hour_id 
                ORDER BY UNIX_TIMESTAMP(a.hour_day) desc limit 0,1 ");
            $pricingOne['lastAuditionHourTime'] = $lastAuditionHourTime['hour_day']?$lastAuditionHourTime['hour_day']:'';
    
            return $pricingOne;
        }

/**
     * classAdd
     */
    function classAdd($request)
        {
    //        if(!isset($request['class_isprepare'])){
    //            $this->error = true;
    //            $this->errortip = "是否预备班必须选择";
    //            return false;
    //        }
    
            $data = array();
            if ($request['create_time'] != '') {
                $time = strtotime($request['create_time']);
            } else {
                $time = time();
            }
            $like = date("Ymd", $time);
            $classInfo = $this->DataControl->selectOne("select class_branch from smc_class where class_branch like '{$like}%' AND LENGTH(class_branch) = '14' order by class_branch DESC limit 0,1");
    
            if ($classInfo) {
                $data['class_branch'] = $classInfo['class_branch'] + 1;
            } else {
                $data['class_branch'] = $like . '000001';
            }
    
            if ($this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$request['school_id']}' and class_cnname='{$request['class_cnname']}' and class_status>=0")) {
                $this->error = true;
                $this->errortip = "该校已存在相同名称的有效班级,不可重复创建";
                return false;
            }
    
            if ($request['class_isprepare'] == '1' && $this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$request['school_id']}' and course_id='{$request['course_id']}' and class_isprepare = '1' and class_status>=0 ")) {
                $this->error = true;
                $this->errortip = "该校此班别下已存在预备班,不可重复创建";
                return false;
            }
    
            $courseOne=$this->DataControl->getFieldOne("smc_course","course_isopenwarm,course_warmnum,course_isopenreview,course_reviewnum,course_ismustreview","course_id='{$request['course_id']}'");
    
            if(!$courseOne){
                $this->error = true;
                $this->errortip = "课程不存在";
                return false;
            }
    
            do {
                $classInfo = $this->DataControl->selectOne("select class_branch from smc_class where class_branch like '{$like}%' AND LENGTH(class_branch) = '14' order by class_branch DESC limit 0,1");
                if ($classInfo) {
                    $data['class_branch'] = $classInfo['class_branch'] + 1;
                } else {
                    $data['class_branch'] = $like . '000001';
                }
            } while ($this->DataControl->getFieldOne("smc_class", "class_id", "class_branch='{$data['class_branch']}'"));
    //        if($a = $this->DataControl->getFieldOne("smc_class","class_id","class_branch='{$data['class_branch']}' and company_id='{$request['company_id']}'")){
    //        	var_dump($a);
    //            $this->error = true;
    //            $this->errortip = "编号创建错误";
    //            return false;
    //        }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_branch", "school_id='{$request['school_id']}'");
    
    
    
            $data['school_id'] = $request['school_id'];
            $data['school_branch'] = $schoolOne['school_branch'];
            $data['company_id'] = $request['company_id'];
            $data['course_id'] = $request['course_id'];
            $data['class_fullnums'] = $request['class_fullnums'];
            $data['class_stdate'] = $request['class_stdate'];
            $data['class_appointnum'] = $request['class_appointnum'];
    
            if (isset($request['class_id']) && $request['class_id'] != '' && $request['class_id'] > 0) {
                $data['class_isupgrade'] = 1;
                $data['from_class_id'] = $request['class_id'];
            }
    
            if (isset($request['class_enddate']) && $request['class_enddate'] != '') {
                $data['class_enddate'] = $request['class_enddate'];
            }
    
            if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
                if ($request['class_stdate'] <= date("Y-m-d", time())) {
                    $data['class_status'] = 1;
                }
            }
    
    
            $data['class_ismustreview'] = $courseOne['course_ismustreview'];
    
            if($courseOne['course_isopenwarm']==1){
                $data['class_hourwarmnums'] = $courseOne['course_warmnum'];
            }
    
            if($courseOne['course_isopenreview']==1){
                $data['class_hourreviewnums'] = $courseOne['course_reviewnum'];
            }
    
            $data['class_isprepare'] = $request['class_isprepare'];//是否预备班  0 正常 1 预备班
    
            $data['class_createtime'] = $time;
            $data['class_cnname'] = $request['class_cnname'];
            $data['class_enname'] = $request['class_enname'];
            $class_id = $this->DataControl->insertData("smc_class", $data);
            if ($class_id) {
                if (isset($request['mainteacher_list']) && $request['mainteacher_list'] != '') {
                    //1,2
                    $mainteacherList = explode(",", $request['mainteacher_list']);
                    foreach ($mainteacherList as $val) {
                        $mainTeachData = array();
                        $mainTeachData['class_id'] = $class_id;
                        $mainTeachData['staffer_id'] = $val;
                        $mainTeachData['teach_type'] = 0;
                        $mainTeachData['teach_createtime'] = time();
                        $this->DataControl->insertData("smc_class_teach", $mainTeachData);
                    }
                }
    
                if (isset($request['secondaryteacher_list']) && $request['secondaryteacher_list'] != '') {
                    $secondaryteacherList = explode(",", $request['secondaryteacher_list']);
                    foreach ($secondaryteacherList as $val) {
                        $secondaryTeacherData = array();
                        $secondaryTeacherData['class_id'] = $class_id;
                        $secondaryTeacherData['staffer_id'] = $val;
                        $secondaryTeacherData['teach_type'] = 1;
                        $secondaryTeacherData['teach_createtime'] = time();
                        $this->DataControl->insertData("smc_class_teach", $secondaryTeacherData);
                    }
                }
                $tem_data = array();
                $tem_data['class_id'] = $class_id;
                $result = $tem_data;
    
            } else {
                $result = false;
            }
            return $result;
        }

/**
     * classManage
     */
    function classManage($request)
        {
            $sql = "SELECT c.class_id,c.class_branch,sc.course_id,sc.course_branch,sc.course_cnname,c.class_cnname,c.class_enname,c.class_stdate,c.class_fullnums,sc.coursetype_id,sc.coursecat_id,cc.coursecat_cnname,scc.coursetype_cnname,sc.course_islimitamout,sc.course_limitamout,c.class_enddate,c.class_appointnum,sc.course_openclasstype,c.class_lastscheduledate,c.class_isprepare,c.class_ismustreview,sc.course_isopenreview,
    			  (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking = 1 and ch.hour_isfree=0 ) as is_checking_num,
    			  (select hour_day from  smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking<>'-1' order by ch.hour_day DESC limit 0,1) as last_hourday,
    			  (select hour_day from  smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking <>'-1'  order by ch.hour_day DESC limit 0,1) as last_checkday,
    			  (select count(ch.hour_id) from  smc_class_hour as ch where ch.class_id = c.class_id and ch.hour_ischecking=0) as hour_allnum
                  ,ifnull((select a.hour_day from smc_class_hour as a where a.class_id = c.class_id and a.hour_ischecking=1 and a.hour_iswarming=2 order by a.hour_lessontimes desc limit 0,1),'') as last_review_hour_day
                  ,(c.class_hourreviewnums+c.class_hourreviewapplynum -ifnull((select count(a.hour_id) from smc_class_hour as a where a.class_id = c.class_id and a.hour_ischecking=1 and a.hour_iswarming=2),0)) as can_plan_review_nums
                  from smc_class as c
                  left join smc_course as sc on sc.course_id=c.course_id
                  left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
                  left join smc_code_coursetype as scc on scc.coursetype_id=sc.coursetype_id
                  where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' limit 0,1";
            $classOne = $this->DataControl->selectOne($sql);
            if ($classOne['last_hourday'] == "") {
                $classOne['last_hourday'] = $classOne['class_stdate'];
            }
    
    
            if ($classOne['last_checkday'] == "") {
                $classOne['last_checkday'] = $classOne['class_stdate'];
            } else {
                $classOne['last_checkday'] = date('Y-m-d', strtotime("{$classOne['last_checkday'] } + 1 day"));
            }
            $mainList = $this->DataControl->selectClear("select ct.staffer_id,s.staffer_cnname,s.staffer_enname from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where ct.class_id='{$classOne['class_id']}' and ct.teach_type=0 and ct.teach_status=0 and s.company_id='{$request['company_id']}'");
            $secondaryList = $this->DataControl->selectClear("select ct.staffer_id,s.staffer_cnname,s.staffer_enname from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where ct.class_id='{$classOne['class_id']}' and ct.teach_type=1 and ct.teach_status=0 and s.company_id='{$request['company_id']}'");
            if (!$mainList) {
                $mainList = array();
            } else {
                foreach ($mainList as &$mainOne) {
                    $mainOne['staffer_cnname'] = $mainOne['staffer_enname'] ? $mainOne['staffer_cnname'] . '-' . $mainOne['staffer_enname'] : $mainOne['staffer_cnname'];
                }
            }
            if (!$secondaryList) {
                $secondaryList = array();
            } else {
                foreach ($secondaryList as &$secondaryOne) {
                    $secondaryOne['staffer_cnname'] = $secondaryOne['staffer_enname'] ? $secondaryOne['staffer_cnname'] . '-' . $secondaryOne['staffer_enname'] : $secondaryOne['staffer_cnname'];
                }
            }
    
            $lastHourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$request['class_id']}' and hour_ischecking=1 order by hour_lessontimes desc");
            if ($lastHourOne) {
                $classOne['class_lastscheduledate'] = date("Y-m-d", strtotime("+1 day", strtotime($lastHourOne['hour_day'])));
            } else {
                $classOne['class_lastscheduledate'] = $classOne['class_lastscheduledate'] != '' ? $classOne['class_lastscheduledate'] : $classOne['class_stdate'];
            }
    
            $classOne['mainteacher'] = $mainList;
            $classOne['secondaryteacher'] = $secondaryList;
            $classOne['arrangeList'] = array();
    
            return $classOne;
        }

/**
     * classEdit
     */
    function classEdit($request)
        {
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_cnname,course_id", "class_id='{$request['class_id']}'");
    
            $classOne['class_cnname'] = addslashes($classOne['class_cnname']);
    
            if ($this->DataControl->getFieldOne("smc_class", "class_id", "school_id='{$request['school_id']}' and class_cnname='{$request['class_cnname']}' and class_cnname<>'{$classOne['class_cnname']}' and class_status>=0")) {
                $this->error = true;
                $this->errortip = "该校已存在相同名称的有效班级,不可重复创建";
                return false;
            }
    
            if (isset($request['course_id']) && $request['course_id'] <> $classOne['course_id']) {
                $this->error = true;
                $this->errortip = "暂不支持修改课程别编号";
                return false;
            }
    
            $data = array();
            $data['class_fullnums'] = $request['class_fullnums'];
            $data['class_appointnum'] = $request['class_appointnum'];
            $data['class_cnname'] = $request['class_cnname'];
            $data['class_enname'] = $request['class_enname'];
            $data['class_stdate'] = $request['class_stdate'];
            if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
                if ($request['class_stdate'] <= date("Y-m-d", time())) {
                    $data['class_status'] = 1;
                }
            }
            if (isset($request['class_stdate']) && $request['class_stdate'] != '') {
                if ($request['class_stdate'] <= date("Y-m-d", time())) {
                    $data['class_status'] = 1;
                } else {
                    $data['class_status'] = 0;
                }
            }
            if (isset($request['class_enddate']) && $request['class_enddate'] != '') {
                $data['class_enddate'] = $request['class_enddate'];
            }
    
            $data['class_updatatime'] = time();
            $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $data);
    //        $this->DataControl->delData("smc_class_teach","class_id='{$request['class_id']}'");
            $mainList = $this->DataControl->selectClear("select staffer_id from smc_class_teach where class_id='{$request['class_id']}' and teach_type=0 and teach_status=0");
    
            $secondaryList = $this->DataControl->selectClear("select staffer_id from smc_class_teach where class_id='{$request['class_id']}' and teach_type=1 and teach_status=0");
    
            if ($mainList) {
                if (isset($request['mainteacher_list']) && $request['mainteacher_list'] != '') {
                    $mainteacherList = explode(",", $request['mainteacher_list']);
                    $temMainArray = array();
                    foreach ($mainList as $value) {
                        $temMainArray[] = $value['staffer_id'];
                    }
                    $s = array_intersect(array_diff($temMainArray, $mainteacherList), $temMainArray);
                    foreach ($mainteacherList as $val) {
                        $mainteacherOne = $this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=0 and teach_status=0 and staffer_id='{$val}'");
                        if (!$mainteacherOne) {
                            if($this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=0 and staffer_id='{$val}'")){
                                $mainTeachData = array();
                                $mainTeachData['teach_status'] = 0;
                                $mainTeachData['teach_createtime'] = time();
                                $this->DataControl->updateData("smc_class_teach","class_id='{$request['class_id']}' and teach_type=0 and staffer_id='{$val}'", $mainTeachData);
                            }else{
                                $mainTeachData = array();
                                $mainTeachData['class_id'] = $request['class_id'];
                                $mainTeachData['staffer_id'] = $val;
                                $mainTeachData['teach_type'] = 0;
                                $mainTeachData['teach_createtime'] = time();
                                $this->DataControl->insertData("smc_class_teach", $mainTeachData);
                            }
                        }
                    }
                    foreach ($s as $val) {
                        $sTeachData = array();
                        $sTeachData['teach_status'] = 1;
                        $sTeachData['teach_relievetime'] = time();
                        $this->DataControl->updateData("smc_class_teach", "class_id='{$request['class_id']}' and teach_type=0 and teach_status=0 and staffer_id='{$val}'", $sTeachData);
                    }
                }
            } else {
                if (isset($request['mainteacher_list']) && $request['mainteacher_list'] != '') {
                    $mainteacherList = explode(",", $request['mainteacher_list']);
                    foreach ($mainteacherList as $val) {
                        $mainTeachData = array();
                        $mainTeachData['class_id'] = $request['class_id'];
                        $mainTeachData['staffer_id'] = $val;
                        $mainTeachData['teach_type'] = 0;
                        $mainTeachData['teach_createtime'] = time();
                        $this->DataControl->insertData("smc_class_teach", $mainTeachData);
    
                    }
                }
            }
    
            if ($secondaryList) {
                $secondaryteacherList = explode(",", $request['secondaryteacher_list']);
                if ($secondaryteacherList) {
                    $temSecondaryArray = array();
                    foreach ($secondaryList as $value) {
                        $temSecondaryArray[] = $value['staffer_id'];
                    }
                    $s2 = array_intersect(array_diff($temSecondaryArray, $secondaryteacherList), $temSecondaryArray);
                    foreach ($secondaryteacherList as $val) {
                        $secondaryTeacherOne = $this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=1 and teach_status=0 and staffer_id='{$val}'");
                        if (!$secondaryTeacherOne && $val > 0) {
                            if($this->DataControl->getFieldOne("smc_class_teach", "teach_id", "class_id='{$request['class_id']}' and teach_type=1 and staffer_id='{$val}'")){
                                $secondaryTeacherData = array();
                                $secondaryTeacherData['teach_status'] = 0;
                                $secondaryTeacherData['teach_createtime'] = time();
                                $this->DataControl->updateData("smc_class_teach","class_id='{$request['class_id']}' and teach_type=1 and staffer_id='{$val}'", $secondaryTeacherData);
                            }else{
                                $secondaryTeacherData = array();
                                $secondaryTeacherData['class_id'] = $request['class_id'];
                                $secondaryTeacherData['staffer_id'] = $val;
                                $secondaryTeacherData['teach_type'] = 1;
                                $secondaryTeacherData['teach_createtime'] = time();
                                $this->DataControl->insertData("smc_class_teach", $secondaryTeacherData);
                            }
                        }
                    }
                    foreach ($s2 as $val) {
                        $sTeachData = array();
                        $sTeachData['teach_status'] = 1;
                        $sTeachData['teach_relievetime'] = time();
                        $this->DataControl->updateData("smc_class_teach", "class_id='{$request['class_id']}' and teach_type=1 and teach_status=0 and staffer_id='{$val}'", $sTeachData);
                    }
                }
            } else {
                if (isset($request['secondaryteacher_list']) && $request['secondaryteacher_list'] != '') {
                    $secondaryteacherList = explode(",", $request['secondaryteacher_list']);
                    foreach ($secondaryteacherList as $val) {
                        $secondaryTeacherData = array();
                        $secondaryTeacherData['class_id'] = $request['class_id'];
                        $secondaryTeacherData['staffer_id'] = $val;
                        $secondaryTeacherData['teach_type'] = 1;
                        $secondaryTeacherData['teach_createtime'] = time();
                        $this->DataControl->insertData("smc_class_teach", $secondaryTeacherData);
                    }
                }
            }
            $array = array();
            $array['class_id'] = $request['class_id'];
            return $array;
        }

/**
     * classDel
     */
    function classDel($request)
        {
            $hourstudyOne = $this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "class_id='{$request['class_id']}'");
            if ($hourstudyOne) {
                $this->error = true;
                $this->errortip = "该班级已存在考勤，不可删除！";
                return false;
            }
    
            $sql = "select sh.hourstudy_id from smc_student_hourstudy as sh left join smc_class as c on c.class_id=sh.class_id where c.father_id='{$request['class_id']}' limit 0,1";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "该班级子班已存在考勤，不可删除！";
                return false;
            }
    
            $studyone = $this->DataControl->getFieldOne("smc_student_study", "study_id", "class_id='{$request['class_id']}' and study_isreading=1");
            if ($studyone) {
                $this->error = true;
                $this->errortip = "该班级存在在读学员，不可删除！";
                return false;
            }
            $auditionOne = $this->DataControl->getFieldOne("smc_class_hour_audition", "audition_id", "class_id='{$request['class_id']}'");
            $smc_auditionOne = $this->DataControl->getFieldOne("crm_student_audition", "audition_id", "class_id='{$request['class_id']}'");
            $crm_auditionOne = $this->DataControl->getFieldOne("crm_client_audition", "audition_id", "class_id='{$request['class_id']}'");
            if ($auditionOne || $smc_auditionOne || $crm_auditionOne) {
                $this->error = true;
                $this->errortip = "该班级存在试听学员，不可删除！";
                return false;
            }
    
    
            $sql = "select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.father_id='{$request['class_id']}' limit 0,1";
    
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "该班级子班存在在读学员，不可删除！";
                return false;
            }
    
    //        $num = $this->DataControl->selectOne("select count(study_id) as num from smc_student_study where company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and class_id='{$request['class_id']}'");
    //
    //        $classOne = $this->DataControl->getFieldOne("smc_student_changelog", "changelog_id", "company_id='{$request['company_id']}' and class_id='{$request['class_id']}' and school_id='{$request['school_id']}'");
    //
    //        if ($classOne) {
    //            $this->error = true;
    //            $this->errortip = "该班级存在学员异动记录，不可删除！";
    //            return false;
    //        }
    //
    //        if ($num['num'] > 0 && $num) {
    //            $this->error = true;
    //            $this->errortip = "该班级存在学员，不可删除！";
    //            return false;
    //        }
    
            $audition = $this->DataControl->selectClear("select ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking
                                                      from smc_class_hour_audition as cha
                                                      left join smc_class_hour as ch on ch.hour_id=cha.hour_id and ch.class_id=cha.class_id
                                                      where cha.class_id='{$request['class_id']}'
                                                      ");
    
            if ($audition) {
                foreach ($audition as $val) {
                    $now = date("Y-m-d H:i:s", time());
                    $auditionTime = $val['hour_day'] . " " . $val['hour_endtime'];
                    if ($val['hour_ischecking'] == 0 && ($now <= $auditionTime)) {
                        $this->error = true;
                        $this->errortip = "该班级有试听不可删除！";
                        return false;
                    }
                }
            }
    
            $classOne = $this->DataControl->getFieldOne("smc_class", "from_class_id", "class_id='{$request['class_id']}'");
    
            if ($classOne && $classOne['from_class_id'] > 0) {
                $this->error = true;
                $this->errortip = "该班级为升班班级不可删除！";
                return false;
            }
    
            $data = array();
            $data['class_status'] = '-2';
            $data['class_updatatime'] = time();
            if ($this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $data)) {
    
                $data = array();
                $data['class_status'] = '-2';
                $data['class_updatatime'] = time();
                $this->DataControl->updateData("smc_class", "father_id='{$request['class_id']}' and father_id<>0", $data);
    
                $hour_data = array();
                $hour_data['hour_ischecking'] = -1;
                $hour_data['hour_updatatime'] = time();
                $this->DataControl->updateData("smc_class_hour", "class_id='{$request['class_id']}'", $hour_data);
                return true;
            } else {
                $this->error = true;
                $this->errortip = "数据库错误！";
                return false;
            }
    
        }
    
        /**
         *
         * author: ling
         * 对应接口文档 0001
         * @param string $start
         * @param string $end
         * @param $weekday
         * @param string $num
         * @param int $is_frequency 0-按时间  1-按次数
         * @param array $holidayArray
         * @param int $iskipweek 隔一周传1
         * @param int $fixednum 过滤掉几轮循环
         * @return array
         */

/**
     * classChange
     */
    function classChange($request)
        {
    
            $datawhere = " 1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%'  )";
            }
    
            if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
                $datawhere .= " and scl.stuchange_code='{$request['stuchange_code']}'";
            }
    
            if (isset($request['starttime']) && $request['starttime'] !== '') {
                $datawhere .= " and scl.changelog_day>='{$request['starttime']}'";
            }
    
            if (isset($request['endtime']) && $request['endtime'] !== '') {
                $datawhere .= " and scl.changelog_day<='{$request['endtime']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
            $sql = "select scl.changelog_id,cs.stuchange_name,scl.changelog_day,s.student_branch,s.student_cnname,s.student_enname,sch.school_cnname
                  from  smc_student_changelog as scl
                  left join smc_student as s on s.student_id=scl.student_id
                  left join smc_school as sch on sch.school_id=scl.school_id
                  left join smc_code_stuchange as cs on cs.stuchange_code=scl.stuchange_code
                  where {$datawhere} and scl.school_id='{$request['school_id']}' and scl.class_id='{$request['class_id']}'
                  group by scl.changelog_id  ORDER BY scl.changelog_day DESC
                  limit {$pagestart},{$num}
                  ";
            $classList = $this->DataControl->selectClear($sql);
            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            foreach ($classList as &$val) {
                $val['changelog_day'] = date("Y-m-d", strtotime($val['changelog_day']));
            }
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select scl.changelog_id
                  from  smc_student_changelog as scl
                  left join smc_student as s on s.student_id=scl.student_id
                  left join smc_school as sch on sch.school_id=scl.school_id
                  left join smc_code_stuchange as cs on cs.stuchange_code=scl.stuchange_code
                  where {$datawhere} and scl.school_id='{$request['school_id']}' and scl.class_id='{$request['class_id']}'
                  group by scl.changelog_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $classList;
            return $data;
        }

    // 方法将在这里添加
}