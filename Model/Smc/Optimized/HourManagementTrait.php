<?php

namespace Model\Smc\Optimized;

/**
 * 课时管理功能 Trait
 * 包含: 课时表、课时操作、课时计划等
 */
trait HourManagementTrait
{
/**
     * classTimetable
     */
    function classTimetable($request)
        {
    
            $datawhere = " 1 and ch.hour_ischecking <> 1";
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or ch.hour_starttime like '%{$request['keyword']}%' or ch.hour_endtime like '%{$request['keyword']}%' or ch.hour_number like '%{$request['keyword']}%' )";
            }
            if (isset($request['starttime']) && $request['starttime'] !== '') {
                $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
            }
            if (isset($request['endtime']) && $request['endtime'] !== '') {
                $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
            }
    
            if (isset($request['hour_way']) && $request['hour_way'] !== '') {
                $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
            }
    
            $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_id,ch.hour_way,ch.hour_number
    ,cl.classroom_cnname,hour_noon,ch.hour_ischecking,c.class_cnname,co.course_cnname,s.staffer_cnname,co.course_inclasstype,ch.hour_way
    ,ch.hour_lessontimes,co.course_classnum,ch.hour_isfree,c.class_id,c.class_enname,s.staffer_id,ch.hour_iswarming,hour_cancelnote,c.class_type,
                (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ch.class_id and sch.hour_iswarming='0') as hour_num,
    			(select sf.staffer_cnname from smc_class_hour_teaching  as ht
    			  left join  smc_staffer as sf ON sf.staffer_id = ht.staffer_id
    			 	where  ht.hour_id = ch.hour_id and ht.teaching_type = 1 limit 0,1) as re_staffer_cnname
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type = 0
                  left join smc_staffer as s on s.staffer_id=cht.staffer_id
                  where {$datawhere} and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}'and c.company_id='{$request['company_id']}'
                  GROUP BY ch.hour_id
                  order by ch.hour_lessontimes ASC 
            ";
    
    
            $hourList = $this->DataControl->selectClear($sql);
    
            $last_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking = 0", "order by hour_lessontimes DESC");
            $first_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id={$request['class_id']}  and  hour_ischecking <>-1", "order by hour_lessontimes ASC");
    
    
            $data = array();
            $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
            $ischeck = $this->LgArraySwitch(array("0" => "待考勤", "1" => "已考勤", "-1" => "已取消"));
            $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            if (isset($request['is_export']) && $request['is_export'] == 1) {
    
                if (!$hourList) {
                    $this->error = true;
                    $this->errortip = "暂无排课信息";
                    return false;
                }
    
                $outexceldate = array();
                foreach ($hourList as $dateexcelvar) {
    
                    $week = date('w', strtotime($dateexcelvar['hour_day']));
    
                    $datearray = array();
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);
                    $datearray['hour_way_name'] = $this->LgStringSwitch($dateexcelvar['hour_way'] == 0 ? "实体课" : "线上课");
                    $datearray['hour_starttime'] = $dateexcelvar['hour_starttime'];
                    $datearray['hour_endtime'] = $dateexcelvar['hour_endtime'];
    
                    if ($dateexcelvar['hour_way'] == 1) {
                        $datearray['classroom_cnname'] = $dateexcelvar['hour_number'];
                    } else {
                        $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    }
    
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['re_staffer_cnname'] = $dateexcelvar['re_staffer_cnname'];
                    $datearray['hour_lessontimes'] = $dateexcelvar['hour_lessontimes'];
                    $datearray['hour_ischecking_name'] = $ischeck[$dateexcelvar['hour_ischecking']];
                    $datearray['hour_isfree'] = $this->LgStringSwitch($dateexcelvar['hour_isfree'] == 0 ? '是' : '否');
                    $datearray['hour_iswarming_name'] = $iswarming[$dateexcelvar['hour_iswarming']];
                    $outexceldate[] = $datearray;
                }
    
                $excelheader = $this->LgArraySwitch(array("上课日期", "上课周次", "上课方式", "开始时间", "结束时间", "上课教室", "上课教师", "助教教师", "上课周次", "是否考勤", "是否计费", "课次类型"));
                $excelfileds = array('hour_day', 'week_day', 'hour_way_name', 'hour_starttime', 'hour_endtime', 'classroom_cnname', 'staffer_cnname', 're_staffer_cnname', "hour_lessontimes", 'hour_ischecking_name', 'hour_isfree', "hour_iswarming_name");
    
                $tem_name = '班级排课信息表.xls';
                query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
                exit;
    
    
            } else {
                if ($request['type'] == 1) {
                    if ($hourList) {
                        foreach ($hourList as &$hourOne) {
                            if (($last_hourOne['hour_id'] == $hourOne['hour_id'] || $first_hourOne['hour_id'] == $hourOne['hour_id']) && $hourOne['course_inclasstype'] == 0) {
    
                                $hourOne['is_cancel'] = '1';
                            } else {
                                $hourOne['is_cancel'] = '0';
                            }
                            if ($hourOne['course_inclasstype'] == 2 && $hourOne['hour_ischecking'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            }
                            if ($hourOne['course_inclasstype'] == 1 && $hourOne['hour_ischecking'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            }
    
                            if ($hourOne['hour_ischecking'] == -1) {
                                $hourOne['is_cancel'] = '0';
                            }
                            if ($hourOne['hour_iswarming'] != 0 && $hourOne['hour_ischecking'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            }
    
                            $hourOne['hour_ischecking_name'] = $ischeck[$hourOne['hour_ischecking']];
                            $hourOne['hour_lessontimes'] = $hourOne['hour_lessontimes'] . '/' . $hourOne['hour_num'];
                            if ($hourOne['hour_isfree'] == 0) {
                                $hourOne['hour_isfree'] = $this->LgStringSwitch("是");
                            } else {
                                $hourOne['hour_isfree'] = $this->LgStringSwitch("否");
                            }
                            $hourOne['hour_iswarming_name'] = $iswarming[$hourOne['hour_iswarming']];
    
                            $week = date('w', strtotime($hourOne['hour_day']));
                            $hourOne['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);
    
                            if ($hourOne['hour_day'] > date("Y-m-d") || $hourOne['hour_ischecking'] == -1) {
                                $hourOne['is_outdate'] = 1;
                            } else {
                                $hourOne['is_outdate'] = 0;
                            }
                            $hourOne['today'] = date("Y-m-d");
                            if ($hourOne['hour_way'] == 1) {
                                $hourOne['classroom_cnname'] = $this->LgStringSwitch("云教室");
                                $hourOne['classroom_cnname'] = $hourOne['classroom_branch'] = $hourOne['hour_number'];
                                $hourOne['classroom_iscloud'] = "1";
                            }
                            $hourOne['hour_way_name'] = $this->LgStringSwitch($hourOne['hour_way'] == 0 ? "实体课" : "线上课");
                        }
                    } else {
                        $hourList = array();
                    }
                    //$companyOne = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                    $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");
                    $data['list'] = $hourList;
                    $data['info'] = $this->companyOne;
                    $data['class_type'] = $classOne['class_type'];
                } else {
                    if ($hourList) {
                        foreach ($hourList as $key => $val) {
                            $hourList[$key]['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");
                            $hourList[$key]['classroom_cnname'] = $hourOne['classroom_branch'] = $val['hour_number'];
                        }
                        foreach ($hourList as $key => $val) {
    
                            $week = date('w', strtotime($val['hour_day']));
                            if ($week == 0) {
                                $week = 7;
                            }
                            foreach ($noon as $noonkey => $noonval) {
                                foreach (self::$WORK_DAY as $daykey => $day) {
                                    if ($val['hour_noon'] == $noonkey) {
                                        $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                        if ($week == $daykey) {
                                            $data['a' . $noonkey][$day['en']][] = $val;
                                        } else {
                                            $data['a' . $noonkey][$day['en']]['-1'] = '';
                                        }
                                    }
                                }
                            }
                        }
                        if (count($data) < 3) {
                            $tem_data = array();
                            foreach ($data as $k => $v) {
                                $tem_data[] = $k;
                            }
                            $tem_noon = array_diff($noon, $tem_data);
    
                            if ($tem_noon) {
                                foreach ($tem_noon as $key => $val) {
                                    foreach (self::$WORK_DAY as $daykey => $day) {
                                        $data['a' . $key]['noon_name']['noon_name'] = $val;
                                        $data['a' . $key][$day['en']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    } else {
                        foreach ($noon as $key => $val) {
                            foreach (self::$WORK_DAY as $daykey => $day) {
                                $data['a' . $key]['noon_name']['noon_name'] = $val;
                                $data['a' . $key][$day['en']]['-1'] = '';
                            }
                        }
                    }
                    $data = $this->get_arr($data);
                    $data = array_values($data);
                    $tem_data = array();
                    foreach ($data as $val) {
                        if ($val['noon_name']['noon_name'] == 'morning') {
                            $tem_data[0] = $val;
                            $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                        } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                            $tem_data[1] = $val;
                            $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                        } elseif ($val['noon_name']['noon_name'] == 'night') {
                            $tem_data[2] = $val;
                            $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                        }
                    }
                    asort($tem_data);
    
                    //$companyOne = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                    $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");
    
                    $data['list'] = $tem_data;
                    $data['info'] = $this->companyOne;
                    $data['class_type'] = $classOne['class_type'];
    
                }
                return $data;
            }
        }

/**
     * classTimes
     */
    function classTimes($request)
        {
            $a = $this->DataControl->getFieldOne("smc_school", "school_inclass", "school_id = '{$request['school_id']}'");
            $date = date("Y-m-d");
    
            if ($a['school_inclass'] == '1') {
                $sql = "select ch.hour_name,ch.hour_day 
                  from smc_class_hour as ch 
                  where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=0 and ch.hour_day >= '{$date}'
                  order by ch.hour_day,ch.hour_starttime asc
                  ";
            } else {
                $sql = "select ch.hour_name,ch.hour_day 
                  from smc_class_hour as ch 
                  where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=0
                  order by ch.hour_day,ch.hour_starttime asc
                  ";
            }
    
    
            $hourList = $this->DataControl->selectClear($sql);
    
            if (!$hourList) {
                $hourList = array();
            }
            return $hourList;
    
        }

/**
     * classhourtable
     */
    function classhourtable($request)
        {
    
            $datawhere = "c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}'and c.company_id='{$request['company_id']}'";
            if (isset($request['hour_ischecking']) && $request['hour_ischecking'] !== '') {
                $datawhere .= " and ch.hour_ischecking  = '{$request['hour_ischecking']}' ";
            } else {
                $datawhere .= "  AND ch.hour_ischecking <> '-1' ";
            }
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' or st.staffer_cnname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%' or st.staffer_branch like '%{$request['keyword']}%' or ch.hour_starttime like '%{$request['keyword']}%' or ch.hour_endtime like '%{$request['keyword']}%' or ch.hour_number like '%{$request['keyword']}%' )";
            }
    
            if (isset($request['starttime']) && $request['starttime'] !== '') {
                $datawhere .= " and ch.hour_day >= '{$request['starttime']}'";
            }
            if (isset($request['endtime']) && $request['endtime'] !== '') {
                $datawhere .= " and ch.hour_day <= '{$request['endtime']}'";
            }
            if (isset($request['hour_day']) && $request['hour_day'] !== '') {
                $datawhere .= " and ch.hour_day = '{$request['hour_day']}'";
            }
            if (isset($request['hour_way']) && $request['hour_way'] !== '') {
                $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
            }
            if (isset($request['week_num']) && $request['week_num'] !== '') {
                $datawhere .= " and DATE_FORMAT(ch.hour_day,'%w') = '{$request['week_num']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "SELECT ch.hour_id,ch.hour_name,ch.hour_day,CONCAT(ch.hour_starttime,'~',ch.hour_endtime) AS hour_timesection,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_id,ch.hour_way,ch.hour_number,ch.hour_lessontimes
    ,cl.classroom_cnname,hour_noon,ch.hour_ischecking,c.class_cnname,co.course_cnname,s.staffer_cnname,s.staffer_enname,co.course_inclasstype,ch.hour_way,cc.teachtype_name,tt.teachtype_name as re_teachtype_name
    ,ch.hour_lessontimes,co.course_classnum,ch.hour_isfree,c.class_id,c.class_enname,s.staffer_id,ch.hour_iswarming,hour_cancelnote,c.class_type,st.staffer_id as re_staffer_id,st.staffer_cnname as re_staffer_cnname,st.staffer_enname as re_staffer_enname,co.course_opensonmode,co.course_openclasstype,
                ifnull((select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ch.class_id and sch.hour_iswarming='0' and sch.hour_ischecking <> -1),0) as hour_num,
                ifnull((select 1 from crm_client_audition as aud where aud.hour_id=ch.hour_id and aud.company_id = '{$request['company_id']}' and aud.audition_isvisit in (0,1) limit 0,1),0) as ishaveaudition,
                (select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ch.class_id and sch.hour_iswarming=ch.hour_iswarming  and sch.hour_ischecking <> -1) as hourwarm_num
                ,ifnull((select 0 from smc_class_hour as x where x.class_id=ch.class_id and x.hour_lessontimes>ch.hour_lessontimes and x.hour_ischecking<>-1 limit 0,1),1) as is_last
                ,ifnull((select x.adjustapply_id from smc_class_hour_adjustapply as x where x.class_id=ch.class_id and (x.hour_id=ch.hour_id or (x.adjustapply_type=1 and x.hour_day=ch.hour_day)) and x.adjustapply_status=0 limit 0,1),0) as has_adjustapply
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type = 0 and cht.teaching_isdel=0
                  left join smc_staffer as s on s.staffer_id=cht.staffer_id
                  left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type = 1 and ht.teaching_isdel=0
                  left join smc_staffer as st on st.staffer_id=ht.staffer_id
                  left join smc_code_teachtype as cc on cc.teachtype_code=cht.teachtype_code and cc.company_id='{$request['company_id']}'
                  left join smc_code_teachtype as tt on tt.teachtype_code=ht.teachtype_code and tt.company_id='{$request['company_id']}'
                  where {$datawhere}
                  GROUP BY ch.hour_id
                  order by ch.hour_day ASC,ch.hour_starttime ASC,ch.hour_endtime ASC,ch.hour_lessontimes ";
    
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] !== '') {
                $db_nums = $this->DataControl->selectClear($sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            if (isset($request['is_page']) && $request['is_page'] !== '') {
                $sql .= " LIMIT {$pagestart},{$num}";
            }
    
            $hourList = $this->DataControl->selectClear($sql);
    
            $last_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking = 0", "order by hour_lessontimes DESC");
            $first_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id={$request['class_id']}  and  hour_ischecking <>-1", "order by hour_lessontimes ASC");
    
    
            $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
            $ischeck = $this->LgArraySwitch(array("0" => "待考勤", "1" => "已考勤", "-1" => "已取消"));
            $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
    
            if (isset($request['is_export']) && $request['is_export'] == 1) {
                $sqlclassmain = "SELECT B.school_id,B.school_branch,B.school_cnname,A.class_id,A.class_branch,A.class_cnname,A.class_enname,A.class_stdate,A.class_enddate,A.class_timestr,
                                (select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=0) as main_teacher,
                                (select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=1) as sub_teacher
                                FROM smc_class A 
                                LEFT JOIN smc_school B ON A.school_id=B.school_id
                                WHERE A.company_id='{$request['company_id']}'
                                AND A.school_id='{$request['school_id']}'
                                AND A.class_id='{$request['class_id']}' ";
                $mainList = $this->DataControl->selectOne($sqlclassmain);
    
                $sqlclasshour = "SELECT A.class_id,A.hour_id,A.hour_lessontimes,A.hour_iswarming,
                                DATE_FORMAT(A.hour_day,'%m/%d') AS hour_day,A.hour_ischecking,A.hour_starttime,A.hour_endtime
                                FROM smc_class_hour A
                                WHERE hour_ischecking>-1
                                AND A.class_id='{$request['class_id']}'
                                ORDER BY A.hour_iswarming,A.hour_day,A.hour_starttime ";
                $classhourList = $this->DataControl->selectClear($sqlclasshour);
    
                $sqlstudstudy = "SELECT A.student_id,B.student_branch,B.student_cnname,B.student_enname,C.family_mobile 
                                FROM smc_student_study A 
                                LEFT JOIN smc_student B  ON A.company_id=B.company_id AND A.student_id=B.student_id 
                                LEFT JOIN smc_student_family C on C.student_id=A.student_id and C.family_isdefault=1 
                                LEFT JOIN smc_class D on D.class_id=A.class_id 
                                WHERE A.class_id='{$request['class_id']}' 
                                and A.study_endday>=D.class_stdate
                                ORDER BY A.student_id ";
                $studstudyList = $this->DataControl->selectClear($sqlstudstudy);
    
                $sqlhourstudy = "SELECT A.student_id,A.hour_id,B.stuchecktype_code 
                                FROM smc_student_hourstudy A 
                                LEFT JOIN smc_student_clockinginlog B ON A.hourstudy_id=B.hourstudy_id 
                                WHERE A.class_id='{$request['class_id']}'";
                $hourstudyList = $this->DataControl->selectClear($sqlhourstudy);
    
                $stucheck = array("" => "", "101" => "√", "107" => "×");
    
                $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
    
                if (!$hourList || !$mainList || !$classhourList) {
                    $this->error = true;
                    $this->errortip = "暂无排课信息";
                    return false;
                }
    
                // 引入类库
    //            import('phpexcel.PHPExcel', EXTEND_PATH);
    
                // 文件名和文件类型
                $fileName = $this->LgStringSwitch("{$schoolOne['school_cnname']}班级考勤表-{$mainList['class_enname']}");
                $fileType = ".xlsx";
    
                query_to_excel_only($schoolOne, $mainList, $classhourList, $studstudyList, $hourstudyList, $stucheck, $fileName, $fileType);
                exit;
            } else {
                if ($request['type'] == 1) {
                    if ($hourList) {
                        foreach ($hourList as &$hourOne) {
                            if (trim($hourOne['staffer_enname']) !== '') {
                                if ($hourOne['staffer_enname'] != $hourOne['staffer_cnname']) {
                                    $hourOne['staffer_cnname'] = $hourOne['staffer_cnname'] . '-' . $hourOne['staffer_enname'];
                                }
                            }
    
                            if (trim($hourOne['re_staffer_enname']) !== '') {
                                if ($hourOne['re_staffer_enname'] != $hourOne['re_staffer_cnname']) {
                                    $hourOne['re_staffer_cnname'] = $hourOne['re_staffer_cnname'] . '-' . $hourOne['re_staffer_enname'];
                                }
                            }
    
                            if ($hourOne['teachtype_name'] != '') {
                                $hourOne['staffer_cnname'] = $hourOne['staffer_cnname'] ? $hourOne['staffer_cnname'] . '(' . $hourOne['teachtype_name'] . ')' : '';
                            }
    
                            if ($hourOne['re_teachtype_name'] != '') {
                                $hourOne['re_staffer_cnname'] = $hourOne['re_staffer_cnname'] ? $hourOne['re_staffer_cnname'] . '(' . $hourOne['re_teachtype_name'] . ')' : '';
                            }
    
                            if (($last_hourOne['hour_id'] == $hourOne['hour_id'] || $first_hourOne['hour_id'] == $hourOne['hour_id']) && $hourOne['course_inclasstype'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            } else {
                                $hourOne['is_cancel'] = '0';
                            }
                            if ($hourOne['course_inclasstype'] == 2 && $hourOne['hour_ischecking'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            }
                            if ($hourOne['course_inclasstype'] == 1 && $hourOne['hour_ischecking'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            }
    
                            if ($hourOne['hour_ischecking'] == -1) {
                                $hourOne['is_cancel'] = '0';
                            }
                            if ($hourOne['hour_iswarming'] != 0 && $hourOne['hour_ischecking'] == 0) {
                                $hourOne['is_cancel'] = '1';
                            }
    
                            $hourOne['lessontimes']=$hourOne['hour_lessontimes'];
    
                            $hourOne['hour_ischecking_name'] = $ischeck[$hourOne['hour_ischecking']];
                            if ($hourOne['hour_iswarming'] == 0) {
                                $hourOne['hour_lessontimes'] = $hourOne['hour_lessontimes'] . '/' . $hourOne['hour_num'];
                            } else {
                                $hourOne['hour_lessontimes'] = $hourOne['hour_lessontimes'] . '/' . $hourOne['hourwarm_num'];
                            }
                            if ($hourOne['hour_isfree'] == 0) {
                                $hourOne['hour_isfree'] = $this->LgStringSwitch("是");
                            } else {
                                $hourOne['hour_isfree'] = $this->LgStringSwitch("否");
                            }
                            $hourOne['hour_iswarming_name'] = $iswarming[$hourOne['hour_iswarming']];
    
                            $week = date('w', strtotime($hourOne['hour_day']));
                            $hourOne['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);
    
                            if ($hourOne['hour_day'] > date("Y-m-d") || $hourOne['hour_ischecking'] == -1) {
                                $hourOne['is_outdate'] = 1;
                            } else {
                                $hourOne['is_outdate'] = 0;
                            }
                            $hourOne['today'] = date("Y-m-d");
                            if ($hourOne['hour_way'] == 1) {
                                $hourOne['classroom_cnname'] = $this->LgStringSwitch("云教室");
                                $hourOne['classroom_cnname'] = $hourOne['classroom_branch'] = $hourOne['hour_number'];
                                $hourOne['classroom_iscloud'] = "1";
                            }
                            $hourOne['hour_way_name'] = $this->LgStringSwitch($hourOne['hour_way'] == 0 ? "实体课" : "线上课");
                        }
                    } else {
                        $hourList = array();
                    }
                    //$companyOne = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                    $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");
                    $data['list'] = $hourList;
                    $data['info'] = $this->companyOne;
                    $data['class_type'] = $classOne['class_type'];
                } else {
                    if ($hourList) {
                        foreach ($hourList as $key => $val) {
                            $hourList[$key]['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");
                            if ($val['hour_way'] == 1) {
                                $hourList[$key]['classroom_cnname'] = $this->LgStringSwitch("云教室");
                                $hourList[$key]['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                                $hourList[$key]['classroom_iscloud'] = "1";
                            }
                        }
                        foreach ($hourList as $key => $val) {
    
                            $week = date('w', strtotime($val['hour_day']));
                            if ($week == 0) {
                                $week = 7;
                            }
                            foreach ($noon as $noonkey => $noonval) {
                                foreach (self::$WORK_DAY as $daykey => $day) {
                                    if ($val['hour_noon'] == $noonkey) {
                                        $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                        if ($week == $daykey) {
                                            $data['a' . $noonkey][$day['en']][] = $val;
                                        } else {
                                            $data['a' . $noonkey][$day['en']]['-1'] = '';
                                        }
                                    }
                                }
                            }
                        }
                        if (count($data) < 3) {
                            $tem_data = array();
                            if ($data) {
                                foreach ($data as $k => $v) {
                                    $tem_data[] = $k;
                                }
                            }
    
                            $tem_noon = array_diff($noon, $tem_data);
    
                            if ($tem_noon) {
                                foreach ($tem_noon as $key => $val) {
                                    foreach (self::$WORK_DAY as $daykey => $day) {
                                        $data['a' . $key]['noon_name']['noon_name'] = $val;
                                        $data['a' . $key][$day['en']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    } else {
                        foreach ($noon as $key => $val) {
                            foreach (self::$WORK_DAY as $daykey => $day) {
                                $data['a' . $key]['noon_name']['noon_name'] = $val;
                                $data['a' . $key][$day['en']]['-1'] = '';
                            }
                        }
                    }
                    $data = $this->get_arr($data);
                    $data = array_values($data);
                    $tem_data = array();
                    if ($data) {
                        foreach ($data as $val) {
                            if ($val['noon_name']['noon_name'] == 'morning') {
                                $tem_data[0] = $val;
                                $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                            } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                                $tem_data[1] = $val;
                                $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                            } elseif ($val['noon_name']['noon_name'] == 'night') {
                                $tem_data[2] = $val;
                                $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                            }
                        }
                        asort($tem_data);
                    }
    
    
                    // = $this->DataControl->getFieldOne('gmc_company', "comapny_isclocking", "company_id='{$request['company_id']}'");
                    $classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$request['class_id']}'");
    
                    $data['list'] = $tem_data;
                    $data['info'] = $this->companyOne;
                    $data['class_type'] = $classOne['class_type'];
    
                }
                return $data;
            }
        }

/**
     * classNewTimes
     */
    function classNewTimes($request)
        {
    
            $sql = "select hour_lessontimes from smc_class_hour where class_id='{$request['class_id']}' and hour_ischecking='0' order by hour_day asc,hour_lessontimes asc";
            $hourOne = $this->DataControl->selectOne($sql);
            if (!$hourOne) {
                $this->error = 1;
                if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking='1'")) {
                    $this->errortip = "班级课程已结束";
                } else {
                    $this->errortip = "班级需先排课";
                }
                return false;
            }
    
            return $hourOne['hour_lessontimes'];
        }

/**
     * getClassTimes
     */
    function getClassTimes($request)
        {
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_ischecking", "class_id='{$request['class_id']}' and hour_lessontimes='{$request['hour_lessontimes']}'");
    
            if (!$hourOne) {
                $this->error = 1;
                $this->errortip = "请填写有效课次";
                return false;
            }
            return $hourOne['hour_day'];
        }

/**
     * advanceClass
     */
    function advanceClass($request)
        {
            if (!isset($request['stu_list']) || $request['stu_list'] == '') {
                $this->error = 1;
                $this->errortip = "请先选择学员";
                return false;
            }
    
            $stuList = json_decode(stripslashes($request['stu_list']), 1);
    
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_ischecking", "class_id='{$request['class_id']}' and hour_lessontimes='{$request['hour_lessontimes']}' and hour_isfree=0");
            if (!$hourOne) {
                $this->error = 1;
                $this->errortip = "请填写有效课次";
                return false;
            }
            if ($hourOne['hour_ischecking'] == '1') {
                $this->error = 1;
                $this->errortip = "提前入班无法调整到已考勤课次";
                return false;
            }
            if ($hourOne['hour_ischecking'] != '0') {
                $this->error = 1;
                $this->errortip = "请填写有效课次";
                return false;
            }
    
    //        if ($hourOne['hour_day'] > date("Y-m-d", time())) {
    //            $this->error = 1;
    //            $this->errortip = "请选择今日之前的课次";
    //            return false;
    //        }
    
            foreach ($stuList as $stuOne) {
                if (!$this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "class_id='{$request['class_id']}' and student_id='{$stuOne['student_id']}'")) {
                    $data = array();
                    $data['study_beginday'] = $hourOne['hour_day'];
                    $data['study_updatetime'] = time();
                    $this->DataControl->updateData("smc_student_study", "class_id='{$request['class_id']}' and student_id='{$stuOne['student_id']}'", $data);
                }
            }
            return true;
        }
    
        /**
         *  确认补考勤 - 获取补考勤页面
         */

/**
     * scheduleDurationClass
     */
    function scheduleDurationClass($request)
        {
            if (!$request['class_id']) {
                $this->error = 1;
                $this->errortip = "请选择班级!";
                return false;
            }
            $startday = date("Y-m-d", strtotime($request['startday']));
            $endday = date("Y-m-d", strtotime($request['endday']));
            $classOne = $this->DataControl->getFieldOne("smc_class", "course_id,class_hournums,class_stdate", "class_id='{$request['class_id']}'");
            if ($classOne['class_stdate'] > $startday) {
                $this->error = 1;
                $this->errortip = "请选择开班时间之后的日期";
                return false;
            }
    
            if (isset($request['is_edit']) && $request['is_edit'] == 1) {
                $ischeckingOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking =1");
                if ($ischeckingOne) {
                    $this->error = 1;
                    $this->errortip = "存在已考勤的课次,不支持修改!";
                    return false;
                } else {
                    $this->DataControl->delData("smc_class_hour ", "class_id='{$request['class_id']}' and hour_ischecking <>1  ");
                }
            }
    
            $classOne = $this->DataControl->getFieldOne("smc_class", 'course_id,class_enddate,class_type,father_id', "class_id='{$request['class_id']}'");
    
            $hourOne = $this->DataControl->selectOne("select ch.hour_day,ch.hour_id,ch.hour_lessontimes
    		    from  smc_class_hour  as ch where ch.class_id='{$request['class_id']}' and hour_iswarming = 0   order by hour_day DESC  limit 0,1");
            if ($hourOne && ($hourOne['hour_day'] >= $startday)) {
                $this->error = 1;
                $this->errortip = "请从{$hourOne['hour_day']}以后的时间开始排课!";
                return false;
            }
    
            $courseOne = $this->DataControl->selectOne("
    					select  ct.coursetype_isopenclass,course_inclasstype from  smc_class as c
    					left join  smc_course as  co on c.course_id = co.course_id
    				    left join  smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
     				    where c.class_id='{$request['class_id']}' ");
    
            if ($courseOne['course_inclasstype'] != 1) {
                $this->error = 1;
                $this->errortip = "非期度类班级,不适用该排课";
                return false;
            }
            $time_start = strtotime($startday);
            $time_end = strtotime($endday);
            $date = array();
            while ($time_start <= $time_end) {
                $date[] = date('Y-m-d', $time_start);
                $time_start = strtotime('+1 day', $time_start);
            }
            if (count($date) < 0) {
                $this->error = 1;
                $this->errortip = "请选择正确的日期";
                return false;
            }
    
            // 跳过节假日
    
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $holidayArray = array();
                $sql = "select ch.holidays_day from smc_code_holidays as ch
                      where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$startday}' and ch.holidays_day<='{$endday}'
                      and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='0')
                           or
                           (ch.school_id='0' and ch.holidays_status='0' and ch.company_id = '{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='1'))
                      )";
    
                $holidayList = $this->DataControl->selectClear($sql);
                if ($holidayList) {
                    foreach ($holidayList as $holidayOne) {
                        $holidayArray[] = $holidayOne['holidays_day'];
                    }
                }
                //排除假期
                $date = array_diff($date, $holidayArray);
                sort($date);
            }
            if (count($date) < 0) {
                $this->error = 1;
                $this->errortip = "请选择正确的日期";
                return false;
            }
    
            //设定的上课周末
            $lesson_sql = "select ch.holidays_day from smc_code_holidays as ch
                      where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$startday}' and ch.holidays_day<='{$endday}'
                      and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='1')
                           or
                           (ch.school_id='0' and ch.holidays_status='1'  and ch.company_id = '{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='0'))
                      )
                      ";
            $normalHoliday = $this->DataControl->selectClear($lesson_sql);
            if ($normalHoliday) {
                $arr_weekend = array_column($normalHoliday, 'holidays_day');
            } else {
                $arr_weekend = array();
            }
            //跳过周末
            $re_date = array();
            if (isset($request['skip_weekend']) && $request['skip_weekend'] == 1) {
                for ($i = 0; $i < count($date); $i++) {
                    if ((date('w', strtotime($date[$i])) != 0 && date('w', strtotime($date[$i])) != 6) || in_array($date[$i], $arr_weekend)) {
                        $re_date[] = $date[$i];
                    }
                }
            } else {
                $re_date = $date;
            }
    
            if ($classOne['class_type'] == 1) {
                $zhu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$classOne['father_id']}' and teach_type=0 and teach_status =0  ");
                $fu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$classOne['father_id']}' and teach_type=1 and teach_status =0  ");
            } else {
                $zhu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=0 and teach_status =0  ");
                $fu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=1 and teach_status =0  ");
            }
    
            $techData = array();
            $techData['class_id'] = $request['class_id'];
            $last_day = array();
    
            $num = 1;
            if ($hourOne['hour_lessontimes'] && $hourOne['hour_lessontimes'] > 0) {
                $num += $hourOne['hour_lessontimes'];
            }
    
            foreach ($re_date as $dateOne) {
                $hourData = array();
                $hourData['course_id'] = $classOne['course_id'];
                $hourData['class_id'] = $request['class_id'];
                $hourData['classroom_id'] = $request['classroom_id'];
                $hourData['hour_isfree'] = 0;
                $hourData['hour_iswarming'] = 0;
                $hourData['hour_starttime'] = $request['hour_starttime'];
                $hourData['hour_endtime'] = $request['hour_endtime'];
                $hourData['hour_createtime'] = time();
                $hourData['hour_updatatime'] = time();
                $hourData['hour_lessontimes'] = $num;
                $hourData['hour_name'] = 'Lesson' . ' ' . $hourData['hour_lessontimes'];
                $hourData['hour_day'] = $dateOne;
                $hourData['hour_formerday'] = $dateOne;
                $hourData['hour_formertimes'] = $request['hour_starttime'] . '-' . $request['hour_endtime'];
                $hour_id = $this->DataControl->insertData("smc_class_hour", $hourData);
    
                if ($zhu_classTeachOne) {
                    $techData['hour_id'] = $hour_id;
                    $techData['hour_lessontimes'] = $hourData['hour_lessontimes'];
                    $techData['staffer_id'] = $zhu_classTeachOne['staffer_id'];
                    $techData['teaching_type'] = '0';
                    $techData['teachtype_code'] = $zhu_classTeachOne['teachtype_code'];
                    $techData['teaching_createtime'] = time();
                    $this->DataControl->insertData("smc_class_hour_teaching", $techData);
    
                }
                if ($fu_classTeachOne) {
                    $techData['hour_id'] = $hour_id;
                    $techData['hour_lessontimes'] = $hourData['hour_lessontimes'];
                    $techData['staffer_id'] = $fu_classTeachOne['staffer_id'];
                    $techData['teaching_type'] = '1';
                    $techData['teachtype_code'] = $fu_classTeachOne['teachtype_code'];
                    $techData['teaching_createtime'] = time();
                    $this->DataControl->insertData("smc_class_hour_teaching", $techData);
    
                }
                $last_day = $dateOne;
    
                $num++;
            }
    
            $classData = array();
            $classData['class_enddate'] = $last_day;
            $classData['class_updatatime'] = time();
            $classData['class_hournums'] = intval(count($re_date));
            $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $classData);
            $this->error = 0;
            $this->errortip = "排课成功";
            return true;
        }
    
        /**
         * 增加期度类课次
         * author: ling
         * 对应接口文档 0001
         */

/**
     * addDurationHour
     */
    function addDurationHour($request)
        {
    
            if (!$request['class_id']) {
                $this->error = 1;
                $this->errortip = "请选择班级!";
                return false;
            }
            if (!$request['hour_day']) {
                $this->error = 1;
                $this->errortip = "请选择日期!";
                return false;
            }
            $startday = date('Y-m-d', strtotime($request['hour_day']));
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "hour_day='{$startday}' and class_id='{$request['class_id']}' and hour_ischecking <>-1");
            if ($hourOne) {
                $this->error = 1;
                $this->errortip = "该时间已有课次!";
                return false;
            }
    
            $hour = $this->DataControl->getOne("smc_class_hour", "class_id='{$request['class_id']}'", "order by hour_day DESC");
            if (!$hour) {
                $this->error = 1;
                $this->errortip = "请确认是否排课";
                return false;
            } elseif ($startday <= $hour['hour_day']) {
                $this->error = 1;
                $this->errortip = "请选择{$hour['hour_day']}之后的时间";
                return false;
            }
    
            $hour = $this->DataControl->getOne("smc_class_hour", "class_id='{$request['class_id']}'", "order by hour_lessontimes DESC");
    
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_hournums", "class_id='{$request['class_id']}'");
            $zhu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=0 and teach_status =0  ");
            $fu_classTeachOne = $this->DataControl->getFieldOne("smc_class_teach", "teachtype_code,staffer_id", "class_id='{$request['class_id']}' and teach_type=1 and teach_status =0  ");
    
            $data = array();
            $data['course_id'] = $hour['course_id'];
            $data['class_id'] = $hour['class_id'];
            $data['hour_lessontimes'] = $hour['hour_lessontimes'] + 1;
            $data['classroom_id'] = $hour['classroom_id'];
            $data['hour_name'] = 'Lesson' . ' ' . $data['hour_lessontimes'];
            $data['hour_isfree'] = 0;
            $data['hour_iswarming'] = 0;
            $data['hour_day'] = $startday;
            $data['hour_ischecking'] = 0;
            $data['hour_starttime'] = $hour['hour_starttime'];
            $data['hour_endtime'] = $hour['hour_endtime'];
            $data['hour_createtime'] = time();
            $data['hour_updatatime'] = time();
            if ($hour_id = $this->DataControl->insertData("smc_class_hour", $data)) {
                if ($zhu_classTeachOne) {
                    $teachData = array();
                    $teachData['class_id'] = $hour['class_id'];
                    $teachData['hour_id'] = $hour_id;
                    $teachData['hour_lessontimes'] = $data['hour_lessontimes'];
                    $teachData['staffer_id'] = $zhu_classTeachOne['staffer_id'];
                    $teachData['teaching_type'] = '0';
                    $teachData['teachtype_code'] = $zhu_classTeachOne['teachtype_code'];
                    $teachData['teaching_createtime'] = time();
                    $this->DataControl->insertData("smc_class_hour_teaching", $teachData);
                }
                if ($fu_classTeachOne) {
                    $teachData = array();
                    $teachData['class_id'] = $hour['class_id'];
                    $teachData['hour_id'] = $hour_id;
                    $teachData['hour_lessontimes'] = $data['hour_lessontimes'];
                    $teachData['staffer_id'] = $fu_classTeachOne['staffer_id'];
                    $teachData['teaching_type'] = '1';
                    $teachData['teachtype_code'] = $fu_classTeachOne['teachtype_code'];
                    $teachData['teaching_createtime'] = time();
                    $this->DataControl->insertData("smc_class_hour_teaching", $teachData);
                }
    
    
                $classData = array();
                $classData['class_enddate'] = $startday;
                $classData['class_hournums'] = $classOne['class_hournums'] + 1;
                $classData['class_updatatime'] = time();
                $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $classData);
    
                $this->error = 0;
                $this->errortip = "增加课次成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "增加失败";
                return false;
            }
    
        }
    
        /**
         * 获取课次详情
         * author: ling
         * 对应接口文档 0001
         */

/**
     * getHourOneApi
     */
    function getHourOneApi($request)
        {
            $hourOne = $this->DataControl->selectOne("
                select c.class_cnname,c.class_branch,ch.hour_name,m.classroom_cnname,ch.hour_id,ch.hour_name,ch.hour_cancelnote,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,
                (select group_concat(staffer_cnname) from smc_class_hour_teaching as cht,smc_staffer as st  where st.staffer_id = cht.staffer_id and  ch.hour_id=cht.hour_id) as staffer_cnname
                from smc_class_hour as ch
                left join smc_class as c On c.class_id = ch.class_id
                left join smc_classroom as m ON ch.classroom_id= m.classroom_id
                left join smc_course as co On co.course_id = ch.course_id
                where hour_id='{$request['hour_id']}'
            ");
    
            if (!$hourOne) {
                $hourOne = array();
            }
            return $hourOne;
        }
    
        /**
         *
         * author: ling
         * 对应接口文档 0001
         */

/**
     * getClassHourName
     */
    function getClassHourName($request)
        {
            $date = $this->DataControl->selectOne("select study_beginday 
                from smc_student_study 
                where class_id = '{$request['class_id']}' 
                and student_id='{$request['student_id']}'
                order by study_beginday DESC limit 0,1");
    
            //当月未考勤的课时
            $sql = "select hour_id,hour_name,hour_lessontimes,concat(hour_day,'        ',hour_name) as hour_day
                  from smc_class_hour 
                  where hour_day >= DATE_ADD(curdate(),interval -day(curdate())+1 day) 
                  and hour_day <= last_day(curdate()) 
                  and hour_day >= '{$date['study_beginday']}' 
                  and class_id='{$request['class_id']}' 
                  and hour_ischecking = 0 
                  order by hour_lessontimes";
    
            $list = $this->DataControl->selectClear($sql);
    
            return $list;
        }
    
    
        //	 获取班级排课计划列表

/**
     * getPlanHourList
     */
    function getPlanHourList($request)
        {
            $class_sql = "select c.class_stdate,co.course_classnum,c.class_type,c.father_id,
    					  (select count(ch.hour_id) from  smc_class_hour  as ch where ch.class_id=c.class_id and  ch.hour_ischecking =0 ) as checking_num
    					  from  smc_class as c
     					  left join smc_course as co ON co.course_id= c.course_id
     					  where c.class_id='{$request['class_id']}' ";
            $classOne = $this->DataControl->selectOne($class_sql);
    
            $datawhere = "ch.class_id ='{$request['class_id']}'";
    
            $sql = "SELECT ch.*,sf.staffer_cnname,sf.staffer_enname 
                    from  smc_class_hour  as ch
    				left join  smc_class_hour_teaching as ht ON ht.hour_id = ch.hour_id and ht.teaching_type=0
    				left join  smc_staffer as sf ON sf.staffer_id = ht.staffer_id
    				where  {$datawhere} and  ch.hour_ischecking ='0' 
                    order by ch.hour_day asc,ch.hour_starttime asc";
            $hourList = $this->DataControl->selectClear($sql);
    
            $today = date('Y-m-d');
            if ($classOne['class_stdate'] > $today) {
                $hour_day = $classOne['class_stdate'];
            } else {
                $hour_day = $today;
            }
    
            $data = array();
            if ($hourList) {
                $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
                $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
                $data = array();
                foreach ($hourList as $key => $hourOne) {
                    $data[$key]['hour_id'] = $hourOne['hour_id'];
                    $data[$key]['hour_formerday'] = $hourOne['hour_formerday'];
                    $data[$key]['hour_formertimes'] = $hourOne['hour_formertimes'];
                    $week = date('w', strtotime($hourOne['hour_day']));
                    $data[$key]['hour_formerweek'] = $this->LgStringSwitch('周' . $weekarray[$week]);
                    $data[$key]['hour_day'] = $hourOne['hour_day'];
                    $data[$key]['hour_starttime'] = $hourOne['hour_starttime'];
                    $data[$key]['hour_endtime'] = $hourOne['hour_endtime'];
                    $data[$key]['hour_iswarming_name'] = $iswarming[$hourOne['hour_iswarming']];
    
                    if ($classOne['class_type'] == 1) {
                        $class_teach = $this->DataControl->selectOne("select   group_concat(distinct concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) ) as cnteacher  from smc_class_teach as th,smc_staffer as s where s.staffer_id=th.staffer_id and class_id='{$classOne['father_id']}' limit 0,1 ");
                        $data[$key]['staffer_cnname'] = $class_teach['cnteacher'];
                    } else {
                        $data[$key]['staffer_cnname'] = $hourOne['staffer_cnname'] . ((isset($hourOne['staffer_enname']) && $hourOne['staffer_enname'] != '') ? '-' . $hourOne['staffer_enname'] : '');
                    }
                }
    
                $hour['list'] = $data;
                $hour['info']['plan_day'] = $hour_day;
                $hour['info']['checking_num'] = $classOne['checking_num'];
            } else {
                $hour['list'] = array();
                $hour['info']['plan_day'] = "--";
                $hour['info']['checking_num'] = 0;
            }
            return $hour;
        }
    
        // 获取排课安排

/**
     * getLessonPlanList
     */
    function getLessonPlanList($request)
        {
            $templan = array();
    
            //获取班级的上课时间
            if(!isset($request['plan_type']) || $request['plan_type'] == '' || $request['plan_type'] == '0'){
                $sql = "select c.*,st.staffer_cnname,st.staffer_enname,t.staffer_cnname as assistant_staffer_cnname,t.staffer_enname as assistant_staffer_enname,t.staffer_branch as assistant_staffer_branch,sc.classroom_cnname,cp.post_name
                  from smc_class_lessonplan as c
                  left join smc_staffer as st on st.staffer_id=c.staffer_id
                  left join smc_staffer as t on t.staffer_id=c.poll_staffer_id
                  left join smc_classroom as sc on sc.classroom_id = c.classroom_id
                  left join gmc_staffer_postbe as sp on st.staffer_id=sp.staffer_id and sp.school_id='{$this->schoolOne['school_id']}'
                  left join gmc_company_post as cp on cp.post_id=sp.post_id
                  where class_id='{$request['class_id']}' and lessonplan_play =1";
    
            }else{
                $sql = "select c.reviewplan_way as lessonplan_way,c.reviewplan_weekno as lessonplan_weekno,c.reviewplan_week as lessonplan_week,c.reviewplan_starttime as lessonplan_starttime,c.reviewplan_endtime as lessonplan_endtime,c.reviewplan_isskipweek as lessonplan_isskipweek,c.reviewplan_isskipholidays as lessonplan_isskipholidays,c.reviewplan_createtime as lessonplan_createtime,c.staffer_id,c.classroom_id,c.poll_staffer_id,c.poll_teachtype_code,c.teachtype_code
                        ,st.staffer_cnname,st.staffer_enname,t.staffer_cnname as assistant_staffer_cnname,t.staffer_enname as assistant_staffer_enname,t.staffer_branch as assistant_staffer_branch,sc.classroom_cnname,cp.post_name
                  from smc_class_reviewplan as c
                  left join smc_staffer as st on st.staffer_id=c.staffer_id
                  left join smc_staffer as t on t.staffer_id=c.poll_staffer_id
                  left join smc_classroom as sc on sc.classroom_id = c.classroom_id
                  left join gmc_staffer_postbe as sp on st.staffer_id=sp.staffer_id and sp.school_id='{$this->schoolOne['school_id']}'
                  left join gmc_company_post as cp on cp.post_id=sp.post_id
                  where c.class_id='{$request['class_id']}' ";
            }
    
            
    
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_stdate", "class_id='{$request['class_id']}'");
            $lesson = $this->DataControl->selectClear($sql);
    
            if ($lesson) {
                foreach ($lesson as $value) {
                    $data['weekday_id'] = $value['lessonplan_weekno'];
                    $data['staffer_id'] = $value['staffer_id'];
                    $data['lessonplan_week'] = $value['lessonplan_week'];
                    $data['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                    $data['hour_way_name'] = $this->LgStringSwitch($value['lessonplan_way'] == 1 ? '线上课' : '实体课');
                    $data['hour_way'] = $value['lessonplan_way'];
                    if ($data['hour_way'] == 0) {
                        $data['classroom_id'] = $value['classroom_id'];
                        $data['classroom_cnname'] = $value['classroom_cnname'];
    
                    } else {
                        $data['classroom_id'] = '';
                        $data['classroom_cnname'] = $this->LgStringSwitch("云教室");
                        $data['classroom_iscloud'] = "1";
                    }
                    $data['hour_starttime'] = $value['lessonplan_starttime'];
                    $data['hour_endtime'] = $value['lessonplan_endtime'];
                    $data['assistant_staffer_id'] = $value['poll_staffer_id'] == 0 ? '' : $value['poll_staffer_id'];
                    $data['assistant_staffer_cnname'] = $value['assistant_staffer_enname'] ? $value['assistant_staffer_cnname'] . '-' . $value['assistant_staffer_enname'] : $value['assistant_staffer_cnname'];
                    $data['post_name'] = $value['post_name'];
                    $data['assistant_staffer_branch'] = $value['assistant_staffer_branch'];
                    $data['teachtype_code'] = $value['teachtype_code'];
                    $data['poll_teachtype_code'] = $value['poll_teachtype_code'];
                    $data['assistant_teachtype_code'] = $value['poll_teachtype_code'];
                    $data['time'] = $value['lessonplan_starttime'] . '-' . $value['lessonplan_endtime'];
                    $templan[] = $data;
                }
            } else {
                $templan = array();
            }
    
            $data = array();
            $data['list'] = $templan;
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$request['class_id']}' and hour_ischecking<> -1", " order  by  hour_day DESC");
            $data['hour_last_day'] = $hourOne['hour_day'] == '' ? $classOne['class_stdate'] : date("Y-m-d", strtotime("+1 day", strtotime($hourOne['hour_day'])));
            $hour_num = $this->DataControl->selectOne("select count(hour_id) as  hour_num from  smc_class_hour where class_id ='{$request['class_id']}' ");
            $data['child_hournum'] = $hour_num['hour_num'];
            return $data;
        }

/**
     * updateHour
     */
    function updateHour($request)
        {
            $json_note = json_decode(stripslashes($request['json_note']), true);
            $CourseModel = new CourseModel();
            if ($json_note) {
                foreach ($json_note as $key => $value) {
                    $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' limit 0,1");
                    $CourseModel->cancelAbsenceHour($value['hour_id'], '调整排课,取消对应的请假记录');
                    $data = array();
                    if ($value['classroom_id'] != 0) {
                        $data['classroom_id'] = $value['classroom_id'];
                        $data['hour_updatatime'] = time();
                        $this->DataControl->updateData("smc_class_hour", "hour_id = '{$value['hour_id']}' and class_id='{$request['class_id']}'", $data);
                    }
    
                    $teaData = array();
                    $teaData['teaching_updatatime'] = time();
                    if ($value['staffer_id'] != 0) {
                        //主教调整
                        $teaData['staffer_id'] = $value['staffer_id'];
                        $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type = 0");
                        if ($teaingData) {
                            $this->DataControl->updateData("smc_class_hour_teaching", "hour_id = '{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =0", $teaData);
                        } else {
                            $data = array();
                            $data['class_id'] = $hourOne['class_id'];
                            $data['hour_id'] = $hourOne['hour_id'];
                            $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                            $data['staffer_id'] = $value['staffer_id'];
                            $data['teaching_type'] = 0;
                            $data['teaching_createtime'] = time();
                            $this->DataControl->insertData("smc_class_hour_teaching", $data);
                        }
                    }
                    if ($value['assistant_staffer_id'] != 0) {
                        //主教调整
                        if ($value['assistant_staffer_id'] == -1) {
                            $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1  limit 0,1");
                            if ($teaingData) {
                                $teaData = array();
                                $teaData['teaching_isdel'] = '1';
                                $teaData['teaching_updatatime'] = time();
                                $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1 and teaching_id='{$teaingData['teaching_id']}'", $teaData);
                            }
                        } else {
                            $teaData = array();
                            $teaData['staffer_id'] = $value['assistant_staffer_id'];
                            $teaData['teaching_isdel'] = 0;
                            $teaData['teaching_updatatime'] = time();
    
                            $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1  limit 0,1");
                            if ($teaingData) {
                                $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1", $teaData);
                            } else {
                                $data = array();
                                $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$request['class_id']}' limit 0,1");
                                $data['class_id'] = $hourOne['class_id'];
                                $data['hour_id'] = $hourOne['hour_id'];
                                $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                                $data['staffer_id'] = $value['assistant_staffer_id'];
                                $data['teaching_type'] = 1;
                                $data['teaching_createtime'] = time();
                                $this->DataControl->insertData("smc_class_hour_teaching", $data);
                            }
                        }
                    }
                    if ($value['is_hourway'] == 1) {
                        if ($hourOne['hour_way'] == 0 && $hourOne['hour_ischecking'] == 0) {
                            $data = array();
                            $data['hour_way'] = '1';
                            $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $value['hour_id']);
                            $data['hour_updatatime'] = time();
                            $data['classroom_id'] = '0';
                            $this->DataControl->updateData("smc_class_hour", "hour_id='{$value['hour_id']}'", $data);
                        }
                    }
                }
            }
            return true;
        }

/**
     * updateClassHourAction
     */
    function updateClassHourAction($request)
        {
            $json_note = json_decode(stripslashes($request['json_note']), true);
            if ($json_note) {
                foreach ($json_note as $key => $value) {
                    $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' limit 0,1");
                    $data = array();
                    if ($value['classroom_id'] != 0) {
                        $data['classroom_id'] = $value['classroom_id'];
                        $data['hour_updatatime'] = time();
                        $this->DataControl->updateData("smc_class_hour", "hour_id = '{$value['hour_id']}' and class_id='{$value['class_id']}'", $data);
                    }
    
                    $teaData = array();
                    if ($value['staffer_id'] != 0) {
                        $teaData['staffer_id'] = $value['staffer_id'];
                        $teaData['teaching_updatatime'] = time();
    
                        $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =0");
                        if ($teaingData) {
                            $this->DataControl->updateData("smc_class_hour_teaching", "hour_id = '{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =0", $teaData);
                        } else {
                            $data = array();
                            $data['class_id'] = $hourOne['class_id'];
                            $data['hour_id'] = $hourOne['hour_id'];
                            $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                            $data['staffer_id'] = $value['staffer_id'];
                            $data['teaching_type'] = 0;
                            $data['teaching_createtime'] = time();
                            $this->DataControl->insertData("smc_class_hour_teaching", $data);
                        }
                    }
                    if ($value['assistant_staffer_id'] != 0) {
                        $teaData = array();
                        $teaData['staffer_id'] = $value['assistant_staffer_id'];
                        $teaData['teaching_updatatime'] = time();
    
                        $teaingData = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =1  limit 0,1");
                        if ($teaingData) {
                            $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' and teaching_type =1", $teaData);
                        } else {
                            $data = array();
                            $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$value['hour_id']}' and class_id='{$value['class_id']}' limit 0,1");
                            $data['class_id'] = $hourOne['class_id'];
                            $data['hour_id'] = $hourOne['hour_id'];
                            $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                            $data['staffer_id'] = $value['assistant_staffer_id'];
                            $data['teaching_type'] = 1;
                            $data['teaching_createtime'] = time();
                            $this->DataControl->insertData("smc_class_hour_teaching", $data);
                        }
                    }
                    if ($value['is_hourway'] == 1) {
                        if ($hourOne['hour_way'] == 0 && $hourOne['hour_ischecking'] == 0) {
                            $data = array();
                            $data['hour_way'] = '1';
                            $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $value['hour_id']);
                            $data['hour_updatatime'] = time();
                            $data['classroom_id'] = '0';
                            $this->DataControl->updateData("smc_class_hour", "hour_id='{$value['hour_id']}'", $data);
                        }
                    }
                }
            }
            return true;
        }

/**
     * endClass
     */
    function endClass($request)
        {
    
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking ='0' and hour_iswarming='0'");
    
            if ($hourOne) {
                $this->error = 1;
                $this->errortip = "该班级存在未考勤的日期";
                return false;
            }
    
            $sql = "select courseshare_id from smc_student_courseshare where class_id='{$request['class_id']}' and courseshare_status=0 limit 0,1";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = 1;
                $this->errortip = "该班级存在未结算的学生";
                return false;
            }
    
            $classOne = $this->DataControl->getFieldOne('smc_class', 'class_status,class_enddate', "class_id='{$request['class_id']}'");
            if ($classOne['class_status'] == '-1') {
                $this->error = 1;
                $this->errortip = "该班级已结班";
                return false;
            } elseif ($classOne['class_status'] == '-2') {
                $this->error = 1;
                $this->errortip = "该班级已删除";
                return false;
            }
    
            $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
    
            $sql = "select student_id
                  from smc_student_study
                  where class_id='{$request['class_id']}' and study_isreading='1'";
    
            $studentList = $this->DataControl->selectClear($sql);
    
            if ($studentList) {
                foreach ($studentList as $studentOne) {
                    $TransactionModel->outClass($studentOne['student_id'], $request['class_id'], 2, strtotime($classOne['class_enddate']));
                }
            }
            $data = array();
            $data['class_status'] = "-1";
    //        if (isset($request['create_time']) && $request['create_time'] !== '') {
    //            $data['class_enddate'] = $request['create_time'];
    //        } else {
    //            $data['class_enddate'] = date("Y-m-d");
    //        }
    
            $data['class_updatatime'] = $request['create_time'] ? strtotime($request['create_time']) : time();
            if ($this->DataControl->updateData('smc_class', "class_id='{$request['class_id']}'", $data)) {
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "结班失败";
                return false;
            }
        }

/**
     * halfEndClass
     */
    function halfEndClass($request)
        {
    
            $breakOne = $this->DataControl->getFieldOne("smc_class_breakoff", "breakoff_id,breakoff_status", "class_id='{$request['class_id']}'");
    
            $type = $this->LgArraySwitch(array("0" => "中途拆班", "1" => "期末拆班"));
            if ($breakOne) {
                if ($breakOne['breakoff_status'] >= 0) {
                    $this->error = 1;
                    $this->errortip = "已存在申请，不可重复申请";
                    return false;
                }
                $data = array();
                $data['breakoff_type'] = $request['breakoff_type'];
                $data['breakoff_status'] = 0;
                $data['breakoff_apply_staffer_id'] = $this->stafferOne['staffer_id'];
                $data['breakoff_apply_time'] = time();
    
                if ($this->DataControl->updateData("smc_class_breakoff", "class_id='{$request['class_id']}'", $data)) {
                    $t_data = array();
                    $t_data['breakoff_id'] = $breakOne['breakoff_id'];
                    $t_data['tracks_title'] = $this->LgStringSwitch("再次申请" . $type[$request['breakoff_type']]);
                    $t_data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师申请' . $type[$request['breakoff_type']]);
                    $t_data['tracks_note'] = $this->LgStringSwitch($request['reason']);
                    $t_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $t_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                    $t_data['tracks_time'] = time();
                    $this->DataControl->insertData("smc_class_breakoff_track", $t_data);
    
                    return true;
                } else {
                    $this->error = 1;
                    $this->errortip = "申请失败";
                    return false;
                }
            } else {
                $data = array();
                $data['company_id'] = $this->companyOne['company_id'];
                $data['school_id'] = $this->schoolOne['school_id'];
                $data['class_id'] = $request['class_id'];
                $data['breakoff_type'] = $request['breakoff_type'];
                $data['breakoff_status'] = 0;
                $data['breakoff_apply_staffer_id'] = $this->stafferOne['staffer_id'];
                $data['breakoff_apply_time'] = time();
    
                $breakoff_id = $this->DataControl->insertData("smc_class_breakoff", $data);
    
                if ($breakoff_id) {
                    $t_data = array();
                    $t_data['breakoff_id'] = $breakoff_id;
                    $t_data['tracks_title'] = $this->LgStringSwitch("申请" . $type[$request['breakoff_type']]);
                    $t_data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师申请' . $type[$request['breakoff_type']]);
                    $t_data['tracks_note'] = $this->LgStringSwitch($request['reason']);
                    $t_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $t_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                    $t_data['tracks_time'] = time();
                    $this->DataControl->insertData("smc_class_breakoff_track", $t_data);
    
                    return true;
                } else {
                    $this->error = 1;
                    $this->errortip = "申请失败";
                    return false;
                }
            }
        }

/**
     * updateHourPlan
     */
    function updateHourPlan($request)
        {
            if (!$request['hour_id']) {
                $this->error = 1;
                $this->errortip = "请选择课次";
                return false;
            }
            $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$request['hour_id']}'");
            $zhu_teching_hour = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and  teaching_type =0");
            if (intval($zhu_teching_hour['staffer_id']) <= 0) {
                $this->error = 1;
                $this->errortip = "请先补全主教信息";
                return false;
            }
            $fu_teching_hour = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and  teaching_type =1");
            if ($hourOne) {
                $arr_week = $this->LgArraySwitch(array('1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'));
                $data = array();
                $data['class_id'] = $hourOne['class_id'];
                $data['lessonplan_play'] = 1;
                $data['lessonplan_weekno'] = date('w', strtotime($hourOne['hour_day']));
                if ($data['lessonplan_weekno'] == 0) {
                    $data['lessonplan_weekno'] = 7;
                }
                $planOne = $this->DataControl->selectOne("SELECT lessonplan_id FROM smc_class_lessonplan
    WHERE class_id = '{$hourOne['class_id']}' and lessonplan_weekno = '{$data['lessonplan_weekno']}' and lessonplan_starttime='{$hourOne['hour_starttime']}' and lessonplan_play =1");
                if ($planOne) {
                    $this->error = 1;
                    $this->errortip = "已存在该课时计划";
                    return false;
                }
                $data['lessonplan_week'] = $arr_week[$data['lessonplan_weekno']];
                $data['lessonplan_play'] = 1;
                $data['staffer_id'] = $zhu_teching_hour['staffer_id'];
                $data['teachtype_code'] = $zhu_teching_hour['teachtype_code'];
                $data['poll_staffer_id'] = $fu_teching_hour['staffer_id'];
                $data['poll_teachtype_code'] = $fu_teching_hour['teachtype_code'];
                $data['classroom_id'] = $hourOne['classroom_id'];
                $data['lessonplan_way'] = $hourOne['hour_way'];
                $data['lessonplan_starttime'] = $hourOne['hour_starttime'];
                $data['lessonplan_endtime'] = $hourOne['hour_endtime'];
                $data['lessonplan_createtime'] = time();
                $id = $this->DataControl->insertData("smc_class_lessonplan", $data);
                if ($id) {
                    $this->error = 0;
                    $this->errortip = "更新成功";
                    return true;
                } else {
                    $this->error = 1;
                    $this->errortip = "更新失败";
                    return false;
                }
            } else {
                $this->error = 1;
                $this->errortip = "未查询到课时";
                return false;
            }
        }
    
        /*
         *  创建子班
         * lujing
         */

/**
     * createHourNumber
     */
    function createHourNumber($class_id, $hour_id = 0)
        {
            if ($hour_id !== 0) {
                $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_starttime,hour_endtime,hour_name,hour_lessontimes", "hour_id='{$hour_id}'");
            } else {
                $hourOne = array();
            }
    
            $arr_initials = range('A', 'Z');
            $initials = $arr_initials[mt_rand(0, count($arr_initials) - 1)];
            $hour_num = date("ymd");
            $hourNumberOne = $this->DataControl->selectOne("select linerooms_hourno from smc_linerooms where linerooms_hourno like '{$hour_num}%'  order by linerooms_hourno DESC limit 0,1");
            if ($hourNumberOne) {
                $hour_num = $hourNumberOne['linerooms_hourno'] + 1;
            } else {
                $hour_num = $hour_num . '00000001';
            }
            do {
                $hourNumberOne = $this->DataControl->selectOne("select linerooms_hourno from smc_linerooms where linerooms_hourno like '{$hour_num}%'  order by linerooms_hourno DESC limit 0,1");
                if ($hourNumberOne) {
                    $hour_num = $hourNumberOne['linerooms_hourno'] + 1;
                }
            } while ($this->DataControl->getFieldOne("smc_linerooms", "linerooms_hourno", "linerooms_hourno='{$hour_num}'"));
    
            if ($hour_num) {
                $data_hour_num = array();
                $data_hour_num['linerooms_hourno'] = $hour_num;
                $data_hour_num['linerooms_number'] = $initials . $hour_num;
                $data_hour_num['company_id'] = $this->companyOne['company_id'];
                $data_hour_num['school_id'] = $this->schoolOne['school_id'];
                $data_hour_num['class_id'] = $class_id;
                $data_hour_num['hour_id'] = $hour_id;
                $data_hour_num['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                $data_hour_num['linerooms_name'] = $hourOne['hour_name'];
                $data_hour_num['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
                $data_hour_num['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
                $data_hour_num['linerooms_createtime'] = time();
                $this->DataControl->insertData("smc_linerooms", $data_hour_num);
                return $data_hour_num['linerooms_number'];
            } else {
                return '';
            }
        }
    
    
        /**
         *  清除子班排课
         */

/**
     * delClassHour
     */
    public function delClassHour($request)
        {
            if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking =1")) {
                $this->error = 1;
                $this->errortip = "存在考勤的课次.不支持删除";
                return false;
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_type", "class_id='{$request['class_id']}'");
    
            if ($classOne['class_type'] == 1) {
                $hour_data = array();
                $hour_data['hour_ischecking'] = '-1';
                $hour_data['hour_updatatime'] = time();
                $this->DataControl->updateData("smc_class_hour", "class_id='{$request['class_id']}'", $hour_data);
                $teach_data = array();
                $teach_data['teaching_isdel'] = '1';
                $teach_data['teaching_updatatime'] = time();
                $this->DataControl->updateData("smc_class_hour_teaching", "class_id='{$request['class_id']}'", $teach_data);
                $this->DataControl->delData("smc_class_lessonplan", "class_id='{$request['class_id']}'");
                $this->error = 0;
                $this->errortip = "清除成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "此功能暂时只能子班使用";
                return false;
            }
        }
    
        /**
         *  实体课转为线上课
         */

/**
     * toHourWay
     */
    function toHourWay($request)
        {
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_way,class_id,hour_ischecking", "hour_id='{$request['hour_id']}'");
    //        if ($hourOne['hour_ischecking'] == 1) {
    //            $this->error = 1;
    //            $this->errortip = "该课时已考勤";
    //            return false;
    //        }
            if ($hourOne['hour_way'] == 0) {
                $data['hour_way'] = '1';
                $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $request['hour_id']);
                $data['hour_updatatime'] = time();
                $data['classroom_id'] = '0';
                $this->DataControl->updateData("smc_class_hour", "hour_id='{$request['hour_id']}'", $data);
                $this->error = 0;
                $this->errortip = "修改成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "该课时已是线上课";
                return false;
            }
        }

/**
     * toClassHourWayAction
     */
    function toClassHourWayAction($request)
        {
            $data = array();
    
            $schoolList = json_decode(stripslashes($request['json_note']), true);
            foreach ($schoolList as $item) {
                $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_way,class_id,hour_ischecking", "hour_id='{$item['hour_id']}'");
                if ($hourOne['hour_ischecking'] == 1) {
                    ajax_return(array('error' => 1, 'errortip' => "该课时已考勤"), $this->companyOne['company_language']);
                }
                if ($hourOne['hour_way'] == 0) {
                    $data['hour_way'] = '1';
                    $data['hour_number'] = $this->createHourNumber($hourOne['class_id'], $item['hour_id']);
                    $data['hour_updatatime'] = time();
                    $data['classroom_id'] = '0';
                    $this->DataControl->updateData("smc_class_hour", "hour_id='{$item['hour_id']}'", $data);
                }
            }
            $res = array('error' => '0', 'errortip' => "实体课转为线上课成功", 'result' => array());
            return $res;
        }
    
    
        //学校班级名称预设 -- 列表

    // 方法将在这里添加
}