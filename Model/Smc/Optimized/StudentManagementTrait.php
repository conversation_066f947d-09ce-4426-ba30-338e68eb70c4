<?php

namespace Model\Smc\Optimized;

/**
 * 学生管理功能 Trait
 * 包含: 学生信息、学生操作、学生导入等
 */
trait StudentManagementTrait
{
/**
     * classStudent
     */
    function classStudent($request)
        {
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
    
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
            $datawhere = " 1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%'  )";
            }
    
            if (isset($request['course_id']) && $request['course_id'] !== '') {
                $datawhere .= " and sc.course_id = '{$request['course_id']}'";
            }
    
            //过滤已经其他子班的学员
            if (isset($request['is_in_childclass']) && $request['is_in_childclass'] == '1') {
                $datawhere .= " and ss.student_id not in ( select std.student_id from smc_student_study as std where std.student_id = s.student_id and std.class_id in (select scs.class_id from smc_class as scs where scs.father_id = c.class_id ))";
            }
    
    //        下拉时使用
            if (isset($request['from']) && $request['from'] == 'drop') {
    
                $pagestart = 0;
                $num = 100;
            }
    
    
    //        $classNum = $this->DataControl->selectOne("select count(ch.hour_id) as  num from smc_class_hour as ch where ch.class_id= '{$request['class_id']}' and ch.hour_ischecking=1 and ch.hour_iswarming = 0 ");
    //        if($classNum){
    //            $hour_num = $classNum['num'];
    //        }else{
    //            $hour_num = 0;
    //        }
    
    
            $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,sf.family_mobile,sc.course_branch,sc.course_cnname,ss.study_beginday,sc.course_id,ss.study_isreading,sc.course_id,sc.course_inclasstype,c.class_type,s.student_img,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_unitexpend,scb.coursebalance_id,c.class_isprepare
    			 ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_ischecking=0 and ch.hour_isfree=0) as hourNoNum,
    			 (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id= '{$request['class_id']}' and ch.hour_ischecking=1 and ch.hour_iswarming = 0 and ch.hour_day >= ss.study_beginday ) as hour_num
                    ,(select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.student_id = s.student_id   order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg 
                  from smc_student_study as ss
                  left join smc_student as s on s.student_id=ss.student_id
                  left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                  left join smc_class as c on c.class_id=ss.class_id
                  left join smc_course as sc on sc.course_id=c.course_id
                  left join smc_student_coursebalance as scb on scb.student_id=ss.student_id and scb.course_id=c.course_id and scb.school_id=ss.school_id
                  where {$datawhere} and ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and ss.study_isreading=1
                  group by s.student_id
                  order by s.student_id ASC,ss.study_beginday DESC 
                  ";
    
            if (isset($request['is_export']) && $request['is_export'] == 1) {
                $dateexcelarray = $this->DataControl->selectClear($sql);
                if (!$dateexcelarray) {
                    $this->error = true;
                    $this->errortip = "无数据";
                    return array();
                }
    //            if ($dateexcelarray) {
    //                foreach ($dateexcelarray as $key => $value) {
    //                    $dateexcelarray[$key]['conversionlog_time'] = date("Y-m-d", $value['conversionlog_time']);
    //                }
    //            }
                $outexceldate = array();
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];// 学员中文名
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];// 学员英文名
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];// 学员编号
                        $datearray['student_sex'] = $dateexcelvar['student_sex'];// 性别
                        $datearray['family_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['family_mobile']);//联系电话
                        $datearray['study_beginday'] = $dateexcelvar['study_beginday'];// 入班时间
    //                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];// 课程别名称
    //                    $datearray['course_branch'] = $dateexcelvar['course_branch'];// 课程别编号
                        $datearray['coursebalance_unitexpend'] = $dateexcelvar['coursebalance_unitexpend'];// 消耗单价
                        $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];// 课程余额
                        $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];// 剩余课次
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = $this->LgArraySwitch(array('学员中文名','学员英文名','学员编号','性别','联系电话','入班时间',"消耗单价","课程余额","剩余课次"));
                $excelfileds = array('student_cnname','student_enname','student_branch','student_sex','family_mobile','study_beginday',"coursebalance_unitexpend","coursebalance_figure","coursebalance_time");
                $classOne = $this->DataControl->selectOne("select * from smc_class where school_id = '{$request['school_id']}' and class_id= '{$request['class_id']}' limit 0,1");
                $fielname = $this->LgStringSwitch($classOne['class_cnname']."学生信息");
                query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
                exit;
            }
    
            $sql .= " limit {$pagestart},{$num} ";
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
    
    
            foreach ($studentList as &$val) {
    
                $val['stuportrait_faceimg'] = is_null($val['stuportrait_faceimg']) ? '' : $val['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90';
                //人像采集二维码
                $val['ishaveportrait'] = $val['stuportrait_faceimg'] ? 1 : 0;
                //人像采集二维码
                $urlparam = base64_encode("student_branch={$val['student_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
                $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
                $val['portraitqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));
    
    
                $this->updateStuCourseBalance($request['company_id'], $request['school_id'], $val['student_id'], $val['course_id'], $request['class_id']);
    
    
                $stuhourstudyOne = $this->DataControl->selectOne("
                select count(c.hourstudy_id) as  num
                from  smc_student_hourstudy as c 
                left join smc_class_hour as ch ON c.hour_id = ch.hour_id
                where c.student_id='{$val['student_id']}' and c.class_id='{$request['class_id']}'  
                and ch.hour_day >= (select study_beginday from smc_student_study as s where s.student_id=c.student_id  and class_id = ch.class_id and  ch.hour_iswarming = 0  limit 0,1) 
              ");
    
    
                if ($stuhourstudyOne) {
                    $stuhourstudy_num = $stuhourstudyOne['num'];
                } else {
                    $stuhourstudy_num = 0;
                }
    
                //是否可以补考勤  1-可以
                if ($stuhourstudy_num < $val['hour_num']) {
                    $val['is_replenish'] = 1;
                } else {
                    $val['is_replenish'] = 0;
                }
    
                if ($val['course_inclasstype'] == 1) {
                    $sql = "select substring(sch.hour_day,1,7) as mon
                      from smc_class_hour as sch
                      where sch.class_id='{$request['class_id']}' and sch.hour_ischecking=0 and sch.hour_isfree=0
                      group by substring(sch.hour_day,1,7)
                      ";
                    $hourNoNumList = $this->DataControl->selectClear($sql);
                    if ($hourNoNumList) {
                        $hourNoNum = count($hourNoNumList);
                    } else {
                        $hourNoNum = 0;
                    }
                    if ($val['coursebalance_time'] < $hourNoNum) {
                        $val['is_need_supplement'] = 1;
                    } else {
                        $val['is_need_supplement'] = 0;
                    }
    
                } elseif ($val['course_inclasstype'] == 2) {
                    $val['is_need_supplement'] = 1;
                } else {
                    if ($val['coursebalance_time'] < $val['hourNoNum']) {
                        $val['is_need_supplement'] = 1;
                    } else {
                        $val['is_need_supplement'] = 0;
                    }
                }
    
    
                $difference = $val['hourNoNum'] - $val['coursebalance_time'];
                if ($difference > 0) {
                    $val['is_need_give'] = 1;
                } else {
                    $val['is_need_give'] = 0;
                }
            }
    
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select s.student_id
                  from smc_student_study as ss
                  left join smc_student as s on s.student_id=ss.student_id
                  left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                  left join smc_class as c on c.class_id=ss.class_id
                  left join smc_course as sc on sc.course_id=c.course_id
                  where {$datawhere} and ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and ss.study_isreading=1
                  group by s.student_id
                  ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;
            return $data;
    
        }

/**
     * updateStuCourseBalance
     */
    function updateStuCourseBalance($company_id, $school_id, $student_id, $course_id, $class_id = 0)
        {
            $companiesOne = $this->getSchoolCourseCompanies($school_id, 0, $course_id);
            $sql = "select scb.coursebalance_id from smc_student_coursebalance as scb where scb.company_id='{$company_id}' and scb.school_id='{$school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$course_id}'";
            if (!$this->DataControl->selectOne($sql)) {
                $student_coursebalance_data = array();
                $student_coursebalance_data['student_id'] = $student_id;
                $student_coursebalance_data['course_id'] = $course_id;
                $student_coursebalance_data['school_id'] = $school_id;
                $student_coursebalance_data['companies_id'] = $companiesOne['companies_id'];
                $student_coursebalance_data['company_id'] = $company_id;
                $student_coursebalance_data['coursebalance_status'] = '1';
                $student_coursebalance_data['coursebalance_createtime'] = time();
                $student_coursebalance_data['coursebalance_updatatime'] = time();
                $this->DataControl->insertData("smc_student_coursebalance", $student_coursebalance_data);
    
                $courselog_data = array();
                $courselog_data['student_id'] = $student_id;
                $courselog_data['log_class'] = 0;
                $courselog_data['school_id'] = $school_id;
                $courselog_data['companies_id'] = $companiesOne['companies_id'];
                $courselog_data['class_id'] = $class_id;
                $courselog_data['course_id'] = $course_id;
                $courselog_data['log_playname'] = $this->LgStringSwitch('班级课程余额占位');
                $courselog_data['log_playclass'] = '+';
                $courselog_data['log_fromamount'] = 0;
                $courselog_data['log_playamount'] = 0;
                $courselog_data['log_finalamount'] = 0;
                $courselog_data['log_reason'] = $this->LgStringSwitch('班级课程余额占位');
                $courselog_data['log_time'] = strtotime("2019-01-01");
                $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);
    
                $time_data = array();
                $time_data['student_id'] = $student_id;
                $time_data['school_id'] = $school_id;
                $time_data['companies_id'] = $companiesOne['companies_id'];
                $time_data['course_id'] = $course_id;
                $time_data['class_id'] = $class_id;
                $time_data['timelog_playname'] = $this->LgStringSwitch('班级课程余额占位');
                $time_data['timelog_playclass'] = '+';
                $time_data['timelog_fromtimes'] = 0;
                $time_data['timelog_playtimes'] = 0;
                $time_data['timelog_finaltimes'] = 0;
                $time_data['timelog_reason'] = $this->LgStringSwitch('班级课程余额占位');
                $time_data['timelog_time'] = strtotime("2019-01-01");
                $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);
            }
        }

/**
     * classStuOptionalTimesList
     */
    function classStuOptionalTimesList($request)
        {
    
            if (isset($request['type']) && $request['type'] == '1') {
                //班外赠送课时
                $sql = "select st.student_cnname,st.student_branch
                        ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0' and ch.hour_isfree='0') as hourNoNum
                        from smc_student as st
                        where st.student_id='{$request['student_id']}'
                  ";
                $info = $this->DataControl->selectOne($sql);
                $info['coursebalance_time'] = 0;
            } else {
                $sql = "select st.student_cnname,st.student_branch,scb.coursebalance_time,scb.course_id
                  ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_ischecking='0' and ch.hour_isfree='0') as hourNoNum
                  from smc_student_study as ss
                  left join smc_class as c on c.class_id=ss.class_id
                  left join smc_student_coursebalance as scb on scb.course_id=c.course_id and scb.student_id=ss.student_id
                  left join smc_student as st on st.student_id=ss.student_id
                  where ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}'
                  and ss.study_isreading='1' and ss.class_id='{$request['class_id']}' limit 0,1
                  ";
                $info = $this->DataControl->selectOne($sql);
            }
    
            $info['difference'] = $info['hourNoNum'] - $info['coursebalance_time'];
            if ($info['difference'] < 0) {
                $info['difference'] = 0;
            }
    
            $sql = "select ch.hour_lessontimes,ch.hour_name,ch.hour_day,ch.hour_starttime,ch.hour_endtime
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0'
                  and ch.hour_isfree='0'
                  and ch.hour_lessontimes not in (select fc.hour_lessontimes from smc_student_free_coursetimes as fc where fc.course_id=c.course_id and fc.student_id='{$request['student_id']}' and fc.is_use='0' )
                  ";
            $hourList = $this->DataControl->selectClear($sql);
    
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            if (!$hourList) {
                $hourList = array();
            }
            if ($hourList) {
                foreach ($hourList as &$hourOne) {
                    $week = date('w', strtotime($hourOne['hour_day']));
                    $hourOne['week_day'] = $this->LgStringSwitch('周' . $weekarray[$week]);
                    $hourOne['lessontimes'] = $this->LgStringSwitch('第' . $hourOne['hour_lessontimes'] . '课次');
                }
    
            }
    
            $field = array();
            $field[0]["fieldstring"] = "hour_lessontimes";
            $field[0]["fieldname"] = $this->LgStringSwitch("课次");
            $field[0]["show"] = 0;
            $field[0]["custom"] = 0;
    
            $field[1]["fieldstring"] = "hour_name";
            $field[1]["fieldname"] = $this->LgStringSwitch("课次名称");
            $field[1]["show"] = 0;
            $field[1]["custom"] = 0;
    
            $field[2]["fieldstring"] = "hour_day";
            $field[2]["fieldname"] = $this->LgStringSwitch("上课日期");
            $field[2]["show"] = 1;
            $field[2]["custom"] = 0;
    
            $field[3]["fieldstring"] = "week_day";
            $field[3]["fieldname"] = $this->LgStringSwitch("上课周次");
            $field[3]["show"] = 1;
            $field[3]["custom"] = 0;
    
            $field[4]["fieldstring"] = "hour_starttime";
            $field[4]["fieldname"] = $this->LgStringSwitch("开始时间");
            $field[4]["show"] = 1;
            $field[4]["custom"] = 0;
    
            $field[5]["fieldstring"] = "hour_endtime";
            $field[5]["fieldname"] = $this->LgStringSwitch("结束时间");
            $field[5]["show"] = 1;
            $field[5]["custom"] = 0;
    
            $field[6]["fieldstring"] = "lessontimes";
            $field[6]["fieldname"] = $this->LgStringSwitch("课次");
            $field[6]["show"] = 1;
            $field[6]["custom"] = 0;
    
            $data = array();
            $data['hourList'] = $hourList;
            $data['info'] = $info;
            $data['field'] = $field;
    
            return $data;
        }

/**
     * giveAwayLessons
     */
    function giveAwayLessons($request)
        {
            $sql = "select fo.order_id from smc_freehour_order as fo where fo.student_id='{$request['student_id']}' and fo.order_status='0'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "存在未审核的同类订单,不可申请";
                return false;
            }
    
            if (isset($request['type']) && $request['type'] == '1') {
                //班外赠送课时
                $sql = "select st.student_cnname,st.student_branch
                        ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking='0' and ch.hour_isfree='0' and ch.hour_iswarming=0) as hourNoNum
                        from smc_student as st
                        where st.student_id='{$request['student_id']}'
                  ";
                $info = $this->DataControl->selectOne($sql);
                $info['coursebalance_time'] = 0;
            } else {
                $sql = "select scb.coursebalance_time,scb.course_id
                  ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_ischecking='0' and ch.hour_isfree='0' and ch.hour_iswarming=0) as hourNoNum
                  from smc_student_study as ss
                  left join smc_class as c on c.class_id=ss.class_id
                  left join smc_student_coursebalance as scb on scb.course_id=c.course_id and scb.student_id=ss.student_id and scb.school_id='{$this->schoolOne['school_id']}'
                  where ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}'
                  and ss.study_isreading='1' and ss.class_id='{$request['class_id']}' limit 0,1
                  ";
                $info = $this->DataControl->selectOne($sql);
            }
    
    
    //        $sql = "select fc.coursetimes_id from smc_student_free_coursetimes as fc where fc.student_id='{$request['student_id']}' and fc.school_id='{$request['school_id']}' and fc.course_id='{$info['course_id']}' and fc.is_use='0'";
    //        $coursetimes = $this->DataControl->selectClear($sql);
    //        if ($coursetimes) {
    //            $num = count($coursetimes);
    //        } else {
    //            $num = 0;
    //        }
            $difference = $info['hourNoNum'] - $info['coursebalance_time'];
            if ($difference < 0) {
                $difference = 0;
            }
    
            $list = json_decode(stripslashes($request['list']), true);
            $neednum = count($list);
    
            if (($difference < $neednum) || $difference == 0) {
                $this->error = true;
                $this->errortip = "选择课次不可超出" . $difference . '次';
                return false;
            }
    
            $sql = "select c.course_id,sc.course_branch,sc.coursetype_id 
                    from smc_class as c 
                    left join smc_course as sc on sc.course_id=c.course_id 
                    where c.class_id='{$request['class_id']}'";
            $classOne = $this->DataControl->selectOne($sql);
    
            $left_num=0;
    
            if($classOne['coursetype_id']==65){
                $sql = "SELECT a.student_id,s.student_cnname,s.student_enname,s.student_branch,g.guildstutype_name
                        ,sum(a.apply_toclasstimes) as apply_toclasstimes
                        FROM  smc_guildstu_apply as a
                        LEFT JOIN smc_student as s ON s.student_id = a.student_id
                        LEFT JOIN smc_code_guildstutype as g ON a.guildstutype_id = g.guildstutype_id
                        where a.student_id='{$request['student_id']}' and a.company_id='{$request['company_id']}'
                        ";
    
                $getGuildstuOne = $this->DataControl->selectOne($sql);
    
                if($getGuildstuOne){
                    $sqlold = " select student_branch,used_times_old
                                from smc_student_policy_old
                                where student_branch='{$getGuildstuOne['student_branch']}'";
                    $oldone = $this->DataControl->selectOne($sqlold);
    
                    $sqlnew = "SELECT A.student_id,count(A.hourstudy_id) AS USED_COUNT
                                FROM smc_student_hourstudy A
                                LEFT JOIN smc_class_hour C ON A.class_id=C.class_id AND A.hour_id=C.hour_id
                                LEFT JOIN smc_class S ON A.class_id = S.class_id
                                LEFT JOIN smc_course H ON C.course_id=H.course_id
                                WHERE 1
                                AND A.student_id='{$getGuildstuOne['student_id']}'
                                AND H.coursetype_id='65'
                                AND C.hour_day>='2020-05-01'
                                AND C.hour_isfree=0
                                AND S.class_type = '0'
                                AND C.hour_iswarming=0
                                AND NOT EXISTS(SELECT 1 FROM smc_school_income
                                WHERE student_id='{$getGuildstuOne['student_id']}'
                                AND hourstudy_id=A.hourstudy_id AND income_price>0)
                                GROUP BY A.student_id ";
                    $newone = $this->DataControl->selectOne($sqlnew);
    
                    $sql = "select count(a.coursetimes_id) as leftFreeTimes
                            from smc_student_free_coursetimes as a 
                            left join smc_course as b on b.course_id=a.course_id
                            where a.student_id='{$getGuildstuOne['student_id']}' and b.coursetype_id=65 and a.is_use=0
                            ";
    
                    $leftFreeTimesOne=$this->DataControl->selectOne($sql);
    
                    $apply_toclasstimes = $getGuildstuOne ? $getGuildstuOne['apply_toclasstimes'] : 0 ;
                    $TIMES_COUNT = $oldone ?$oldone['used_times_old'] : 0 ;
                    $USED_COUNT = $newone ? $newone['USED_COUNT'] : 0;
                    $left_COUNT = $leftFreeTimesOne ? $leftFreeTimesOne['leftFreeTimes'] : 0;
    
                    $left_num = $apply_toclasstimes - $TIMES_COUNT - $USED_COUNT - $left_COUNT;
    
                    if($left_num>0 && $left_num<$neednum){
                        $this->error = 1;
                        $this->errortip = "当前申请的赠课课时数不可超过剩余课时数" . $left_num . '次';
                        return false;
                    }
                }
            }
    
    
    
            $OrderHandleModel = new \Model\Smc\OrderHandleModel($request);
            $order_pid = $OrderHandleModel->createFreeHourOrder($request['student_id'], $classOne['course_id'], $classOne['course_branch'], $request['class_id'], '', 0, $request['order_img'], $request['reason']);
    
            $num = 0;
    
            foreach ($list as $val) {
                if (!$this->DataControl->getFieldOne("smc_student_free_coursetimes", "coursetimes_id", "student_id='{$request['student_id']}' and course_id='{$classOne['course_id']}' and hour_lessontimes='{$val['hour_lessontimes']}' and is_use=0")) {
                    $times_data = array();
                    $times_data['order_pid'] = $order_pid;
                    $times_data['class_id'] = $request['class_id'];
                    $times_data['hour_lessontimes'] = $val['hour_lessontimes'];
                    $this->DataControl->insertData("smc_freehour_ordertimes", $times_data);
    
                    $num++;
                }
            }
    
            $order_data = array();
            $order_data['order_alltimes'] = $num;
            $this->DataControl->updateData("smc_freehour_order", "order_pid='{$order_pid}' and company_id='{$request['company_id']}'", $order_data);
    
            if($left_num>0){
                $param = array();
                $param['is_adopt'] = 1;
                $param['order_pid'] = $order_pid;
                $param['reason'] = $this->LgStringSwitch('公益学员赠课自动审核');
                $Model = new \Model\Gmc\OrderModel($request);
                $Model->examineHourFreeOrder($param);
            }else{
    
                $fanweiModel = new \Model\Smc\FanweiModel();
                $bool=$fanweiModel->createFreeTimesApply($order_pid);
                if(!$bool){
                    $param = array();
                    $param['is_adopt'] = 0;
                    $param['order_pid'] = $order_pid;
                    $param['reason'] = $this->LgStringSwitch('泛微流程生成失败,请联系技术查看问题');
                    $Model = new \Model\Gmc\OrderModel($request);
                    $Model->examineHourFreeOrder($param);
                }
    
            }
    
            return true;
    
    //        $school = $this->DataControl->selectClear("
    //SELECT
    //	s.school_id,
    //	s.school_province
    //FROM
    //	smc_school AS s
    //WHERE
    //	company_id = 8888 and s.school_province in (10,8) and school_id = '{$request['school_id']}'");
    //
    //        $pay = $this->DataControl->selectClear("
    //SELECT
    //	COUNT(pay_id) as num
    //FROM
    //	smc_payfee_order AS o
    //	LEFT JOIN smc_payfee_order_pay AS p ON o.order_pid = p.order_pid
    //	LEFT JOIN smc_code_paytype AS t ON p.paytype_code = t.paytype_code
    //	LEFT JOIN smc_payfee_order_course AS c ON o.order_pid = c.order_pid
    //	LEFT JOIN smc_course AS sc ON c.course_id = sc.course_id
    //WHERE
    //	student_id = '{$request['student_id']}'
    //	AND t.paytype_ischarge = 1
    //	AND sc.coursecat_id = 135
    //	AND p.pay_issuccess = 1
    //	AND FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d' ) >= '2022-03-10'");
    //
    //        $pay2 = $this->DataControl->selectClear("
    //SELECT
    //	COUNT(pay_id) as num
    //FROM
    //	smc_payfee_order AS o
    //	LEFT JOIN smc_payfee_order_pay AS p ON o.order_pid = p.order_pid
    //	LEFT JOIN smc_code_paytype AS t ON p.paytype_code = t.paytype_code
    //	LEFT JOIN smc_payfee_order_course AS c ON o.order_pid = c.order_pid
    //	LEFT JOIN smc_course AS sc ON c.course_id = sc.course_id
    //WHERE
    //	student_id = '{$request['student_id']}'
    //	AND t.paytype_ischarge = 1
    //	AND sc.coursecat_id = 133
    //	AND p.pay_issuccess = 1
    //	AND FROM_UNIXTIME( p.pay_successtime, '%Y-%m-%d' ) >= '2021-03-10'");
    
    
    //        var_dump($request['company_id']);
    //        var_dump($request['school_id']);
    //        var_dump($classOne['coursetype_id']);
    //        var_dump($school);
    //        var_dump($pay[0]['num']);
    //        var_dump($neednum);
    //        var_dump($classOne['coursetype_id']);
    
    //        $sql = "select a.info_id
    //                from smc_student_registerinfo as a
    //                where a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.coursetype_id=65 and FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d')>='2023-06-01'  and FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d')<='2023-06-30'
    //                and not exists(select 1 from smc_freehour_order as x where x.student_id=a.student_id and x.order_status=1)
    //                limit 0,1";
    //
    //
    //        if ($request['company_id'] == '8888' && ($request['school_id'] == '2323' || $request['school_id'] == '2322') && ($classOne['coursetype_id'] == '79654' || $classOne['coursetype_id'] == '79653' || $classOne['coursetype_id'] == '79660')) {
    //            $request['coursetype_id'] = $classOne['coursetype_id'];
    //            $request['num'] = $num;
    //            $request['order_pid'] = $order_pid;
    //            $this->automaticAudit($request);
    //        } elseif ($request['company_id'] == '8888' && $school && $pay[0]['num'] > 0 && ($classOne['coursetype_id'] == '79654' || $classOne['coursetype_id'] == '79653' || $classOne['coursetype_id'] == '79660' || $classOne['coursetype_id'] == '79661') && $neednum == 4 * $pay[0]['num']) {
    //            $request['coursetype_id'] = $classOne['coursetype_id'];
    //            $request['num'] = $num;
    //            $request['order_pid'] = $order_pid;
    //            $this->automaticAudit($request, 2);
    //        } elseif ($request['company_id'] == '8888' && $school && $pay2[0]['num'] > 0 && $classOne['coursetype_id'] == '61' && $neednum == 3 * $pay2[0]['num']) {
    //            $request['coursetype_id'] = $classOne['coursetype_id'];
    //            $request['num'] = $num;
    //            $request['order_pid'] = $order_pid;
    //            $this->automaticAudit($request, 2);
    //        } elseif ($this->DataControl->selectOne($sql) && (($classOne['coursetype_id'] == '79674' && $neednum == '3') or (in_array($classOne['coursetype_id'], array('79654', '79653', '79660')) && $neednum == '2'))) {
    //            $request['coursetype_id'] = $classOne['coursetype_id'];
    //            $request['num'] = $num;
    //            $request['order_pid'] = $order_pid;
    //            $this->automaticAudit($request, 2);
    //        }
    
    //        return true;
        }

/**
     * automaticAudit
     */
    function automaticAudit($request, $status)
        {
            if ($status <> '2') {
                $sql = "select sum(c.pricinglog_buyprice) as buyprice from smc_student_coursebalance_pricinglog as c 
                    left join smc_payfee_order as o on o.order_pid=c.order_pid 
                    left join smc_course as sc on sc.course_id=c.course_id 
                    where c.school_id='{$request['school_id']}' and c.student_id='{$request['student_id']}' and sc.coursetype_id='65' and o.order_status = 4";
                $pricinglog = $this->DataControl->selectOne($sql);
                if ($pricinglog['buyprice'] < 3200) {
                    $this->error = true;
                    $this->errortip = "课程购买金额需在3200元以上";
                    return false;
                }
                $sql = "select sum(c.coursebalance_figure) as figure_num from smc_student_coursebalance as c left join smc_course as sc on sc.course_id=c.course_id where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and c.student_id='{$request['student_id']}' and sc.coursetype_id='65'";
                $coursebalance = $this->DataControl->selectOne($sql);
                if (!$coursebalance['figure_num']) {
                    $this->error = true;
                    $this->errortip = "课程金额不足";
                    return false;
                }
    
                $order = $this->DataControl->getOne("smc_freehour_order", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and order_status='1' and order_alltimes = 12");
                if ($order) {
                    $this->error = true;
                    $this->errortip = "已存在审核通过12次的赠课申请";
                    return false;
                }
    
                if ($request['num'] != 12) {
                    if ($request['coursetype_id'] == '79654') {
                        $tip = "美语";
                    } elseif ($request['coursetype_id'] == '79653') {
                        $tip = "Steam";
                    } else {
                        $tip = "中文绘本";
                    }
                    $this->error = true;
                    $this->errortip = $tip . "赠送课次申请不等于12";
                    return false;
                }
    
                $sql = "select st.study_id from smc_student_study as st left join smc_class as c on c.class_id=st.class_id left join smc_course as sc on sc.course_id=c.course_id where st.company_id='{$request['company_id']}' and st.school_id='{$request['school_id']}' and st.student_id='{$request['student_id']}' and st.study_isreading='1' and sc.coursetype_id='{$request['coursetype_id']}'";
                $class = $this->DataControl->selectOne($sql);
                if ($class) {
                    $this->error = true;
                    $this->errortip = "赠送课需非在读";
                    return false;
                }
            }
    
    
            $param = array();
            $param['is_adopt'] = 1;
            $param['order_pid'] = $request['order_pid'];
            $Model = new \Model\Gmc\OrderModel($request);
            $Model->examineHourFreeOrder($param);
    
            return true;
        }

/**
     * endClassStudent
     */
    function endClassStudent($request)
        {
            $datawhere = '1';
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%'  )";
            }
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "select ch.hour_id from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=1 order by ch.hour_lessontimes desc limit 0,1";
    
            $hourOne = $this->DataControl->selectOne($sql);
            if (!$hourOne) {
                $this->error = true;
                $this->errortip = "无学生信息";
                return false;
            }
    
            $sql = "select st.student_id,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,p.parenter_mobile,ss.study_beginday,ss.study_endday
                    ,(select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.student_id = sh.student_id   order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg
                    ,(select l.class_isprepare from smc_class as l where sh.class_id = l.class_id limit 0,1) as class_isprepare 
                  from smc_student_hourstudy as sh,smc_student as st,smc_student_family as sf,smc_student_study as ss,smc_parenter as p 
                  where {$datawhere} and sh.student_id=st.student_id and st.student_id=sf.student_id and sf.family_isdefault=1 and ss.student_id=sh.student_id and ss.class_id=sh.class_id and p.parenter_id=sf.parenter_id 
                  and sh.class_id='{$request['class_id']}' and sh.hour_id ='{$hourOne['hour_id']}'
                  order by ss.study_beginday asc 
                  ";
    
            if (isset($request['is_export']) && $request['is_export'] == 1) {
                $dateexcelarray = $this->DataControl->selectClear($sql);
                if (!$dateexcelarray) {
                    $this->error = true;
                    $this->errortip = "无数据";
                    return array();
                }
    //            if ($dateexcelarray) {
    //                foreach ($dateexcelarray as $key => $value) {
    //                    $dateexcelarray[$key]['conversionlog_time'] = date("Y-m-d", $value['conversionlog_time']);
    //                }
    //            }
                $outexceldate = array();
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];// 学员中文名
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];// 学员英文名
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];// 学员编号
                        $datearray['student_sex'] = $dateexcelvar['student_sex'];// 性别
                        $datearray['parenter_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['parenter_mobile']);//联系电话
                        $datearray['study_beginday'] = $dateexcelvar['study_beginday'];// 入班时间
                        $datearray['study_endday'] = $dateexcelvar['study_endday'];// 出班时间
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = $this->LgArraySwitch(array('学员中文名','学员英文名','学员编号','性别','联系电话','入班时间',"出班时间"));
                $excelfileds = array('student_cnname','student_enname','student_branch','student_sex','parenter_mobile','study_beginday',"study_endday");
                $classOne = $this->DataControl->selectOne("select * from smc_class where school_id = '{$request['school_id']}' and class_id= '{$request['class_id']}' limit 0,1");
                $fielname = $this->LgStringSwitch($classOne['class_cnname']."学生信息");
                query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
                exit;
            }
    
            $sql .= " limit {$pagestart},{$num} ";
            $studentList = $this->DataControl->selectClear($sql);
    
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学生信息";
                return false;
            }
            foreach ($studentList as &$val) {
                $val['stuportrait_faceimg'] = is_null($val['stuportrait_faceimg']) ? '' : $val['stuportrait_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90';
                //人像采集二维码
                $val['ishaveportrait'] = $val['stuportrait_faceimg'] ? 1 : 0;
                //人像采集二维码
                $urlparam = base64_encode("student_branch={$val['student_branch']}&company_id={$request['company_id']}&school_id={$request['school_id']}&staffer_id={$request['staffer_id']}");
                $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
                $val['portraitqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));
            }
    
            $count_sql = "select st.student_id
                  from smc_student_hourstudy as sh,smc_student as st,smc_student_family as sf,smc_student_study as ss,smc_parenter as p 
                  where {$datawhere} and sh.student_id=st.student_id and st.student_id=sf.student_id and sf.family_isdefault=1 and ss.student_id=sh.student_id and ss.class_id=sh.class_id and p.parenter_id=sf.parenter_id 
                  and sh.class_id='{$request['class_id']}' and sh.hour_id ='{$hourOne['hour_id']}'
                  ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;
            $data['list'] = $studentList;
            return $data;
        }

/**
     * openClassStudent
     */
    function openClassStudent($request)
        {
            $datawhere = '1';
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (cl.client_cnname like '%{$request['keyword']}%' or cl.client_enname like '%{$request['keyword']}%' or cl.client_mobile like '%{$request['keyword']}%'  )";
            }
            if (isset($request['hour_day']) && $request['hour_day'] !== '') {
                $datawhere .= " and ch.hour_day  ='{$request['hour_day']}'";
            }
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "
                    select  ha.audition_id,cl.client_id ,cl.client_cnname,cl.client_enname,cl.client_sex,cl.client_mobile,ch.hour_day,ha.audition_isvisit,(case when IFNULL(ha.audition_novisitreason,'')='' then '--' else ha.audition_novisitreason end) as audition_novisitreason ,d.audition_visittime  
                    from  smc_class_hour_audition  as ha
                    left join smc_class_hour as ch ON ha.hour_id=ch.hour_id
                    left join  crm_client as cl ON cl.client_id = ha.client_id 
                    left join crm_client_audition as d ON d.client_id = cl.client_id and d.hour_id = ha.hour_id 
                    where ch.class_id= '{$request['class_id']}'  and {$datawhere}  
                    ";
    
            $arr_isvisit = $this->LgArraySwitch(array('0' => '待确认', '1' => '已试听', '-1' => '未试听'));
    
            if (isset($request['is_export']) && $request['is_export'] == 1) {
                $dateexcelarray = $this->DataControl->selectClear($sql);
                if (!$dateexcelarray) {
                    $this->error = true;
                    $this->errortip = "无数据";
                    return array();
                }
                if ($dateexcelarray) {
                    foreach ($dateexcelarray as $key => $value) {
                        $dateexcelarray[$key]['audition_isvisitname'] = $arr_isvisit[$value['audition_isvisit']];
                        $dateexcelarray[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                    }
                }
                $outexceldate = array();
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['client_cnname'] = $dateexcelvar['client_cnname'];// 姓名
                        $datearray['client_sex'] = $dateexcelvar['client_sex'];// 性别
                        $datearray['client_mobile'] = preg_replace("/(\d{3})\d\d(\d{2})/", "\$1****\$3", $dateexcelvar['client_mobile']);//主要联系电话
                        if($dateexcelvar['audition_visittime']){
                            $datearray['hour_day'] = date('Y-m-d',strtotime($dateexcelvar['audition_visittime']));// 试听日期
                        }else{
                            $datearray['hour_day'] = $dateexcelvar['hour_day'];// 试听日期
                        }
                        $datearray['audition_isvisitname'] = $dateexcelvar['audition_isvisitname'];// 是否试听
                        $datearray['audition_novisitreason'] = $dateexcelvar['audition_novisitreason'];// 未试听原因
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = $this->LgArraySwitch(array('姓名','性别','主要联系电话','试听日期','是否试听','未试听原因'));
                $excelfileds = array('client_cnname','client_sex','client_mobile','hour_day','audition_isvisitname','audition_novisitreason');
                $classOne = $this->DataControl->selectOne("select * from smc_class where school_id = '{$request['school_id']}' and class_id= '{$request['class_id']}' limit 0,1");
                $fielname = $this->LgStringSwitch($classOne['class_cnname']."学生信息");
                query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
                exit;
            }
    
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
            $allnum = 0;
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select count(ha.audition_id)  as aud_num
                    from  smc_class_hour_audition  as ha
                    left join smc_class_hour as ch ON ha.hour_id=ch.hour_id
                    left join  crm_client as cl ON cl.client_id = ha.client_id 
                    where ch.class_id= '{$request['class_id']}' and  {$datawhere}         
                  ";
                $db_nums = $this->DataControl->selectOne($count_sql);
    
                if ($db_nums) {
                    $allnum = $db_nums['aud_num'];
                } else {
                    $allnum = 0;
                }
            }
            $data['allnum'] = $allnum;
    
            if (!$dataList) {
                $dataList = array();
            } else {
                foreach ($dataList as &$value) {
                    //crm试听和单校对不上 单校调课了
                    if($value['audition_visittime']){
                        $value['hour_day'] = date('Y-m-d',strtotime($value['audition_visittime']));
                    }
                    $value['audition_isvisitname'] = $arr_isvisit[$value['audition_isvisit']];
                    $value['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                }
            }
            $data['list'] = $dataList;
            return $data;
    
        }

/**
     * ImportStudent
     */
    function ImportStudent($request, $sqlarray)
        {
            if (!$sqlarray) {
                $this->error = true;
                $this->errortip = "请确认文件是否存在数据";
                return false;
            }
            $t_num = 0;
            $f_num = 0;
            $tem_array = array();
    
            foreach ($sqlarray as &$one) {
                $one['student_cnname'] = addslashes(trim($one['student_cnname']));
                $one['student_enname'] = addslashes(trim($one['student_enname']));
                $one['student_sex'] = addslashes(trim($one['student_sex']));
                if ($one['student_birthday']) {
                    $one['student_birthday'] = addslashes(trim(date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($one['student_birthday']))));
                }
    
                $one['parenter_cnname'] = addslashes(trim($one['parenter_cnname']));
                $one['parenter_mobile'] = addslashes(trim($one['parenter_mobile']));
                $one['family_relation'] = addslashes(trim($one['family_relation']));
                $one['student_idcard'] = addslashes(trim($one['student_idcard']));
                $one['class_branch'] = addslashes(trim($one['class_branch']));
                if ($one['study_beginday']) {
                    $one['study_beginday'] = addslashes(trim(date("Y-m-d", \PHPExcel_Shared_Date::ExcelToPHP($one['study_beginday']))));
                }
    
            }
    
            foreach ($sqlarray as $sqlone) {
                $bool = false;
                $data = $sqlone;
                if ($sqlone['student_cnname'] && $sqlone['student_sex'] && $sqlone['parenter_mobile'] && $sqlone['student_birthday']) {
    
                    if ($sqlone['student_sex'] != '男' && $sqlone['student_sex'] != '女') {
                        $bool = true;
                        $data['reason'] = $this->LgStringSwitch('性别必须是男或女');
                        $f_num += 1;
                    } else {
    
                        if ($this->checkMobile($sqlone['parenter_mobile'])) {
                            if (preg_match("/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/", $sqlone['student_birthday'], $parts)) {
                                if (checkdate($parts[2], $parts[3], $parts[1])) {
    
                                    $sql = "select s.student_id 
                                          from smc_student_family as sf,smc_parenter as p,smc_student as s 
                                          where sf.parenter_id=p.parenter_id and sf.student_id=s.student_id
                                          and s.student_cnname='{$sqlone['student_cnname']}' and p.parenter_mobile='{$sqlone['parenter_mobile']}'
                                            ";
                                    $studentOne = $this->DataControl->selectOne($sql);
    
                                    if (!$studentOne) {
                                        $like = date("Ymd", time());
                                        $stuInfo = $this->DataControl->selectOne("select student_branch from smc_student where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
                                        if ($stuInfo) {
                                            $student_branch = number_format($stuInfo['student_branch'] + 1, 0, '', '');
                                        } else {
                                            $student_branch = $like . '000001';
                                        }
    
                                        $s_data = array();
                                        $s_data['company_id'] = $this->companyOne['company_id'];
                                        $s_data['student_cnname'] = $sqlone['student_cnname'];
                                        $s_data['student_enname'] = $sqlone['student_enname'];
                                        $s_data['student_birthday'] = $sqlone['student_birthday'];
                                        $s_data['student_idcard'] = $sqlone['student_idcard'];
                                        $s_data['student_sex'] = $sqlone['student_sex'];
                                        $s_data['student_branch'] = $student_branch;
                                        $s_data['student_createtime'] = time();
                                        $s_data['student_updatatime'] = time();
                                        $student_id = $this->DataControl->insertData("smc_student", $s_data);
    
                                    } else {
                                        $student_id = $studentOne['student_id'];
                                    }
    
                                    if ($student_id > 0) {
                                        if ($sqlone['parenter_cnname'] || $sqlone['parenter_mobile']) {
                                            $parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$sqlone['parenter_mobile']}'");
    
                                            if ($parenterOne) {
                                                $p_data = array();
                                                $p_data['parenter_cnname'] = $sqlone['parenter_cnname'];
                                                $this->DataControl->updateData("smc_parenter", "parenter_id='{$parenterOne['parenter_id']}'", $p_data);
                                                $parenter_id = $parenterOne['parenter_id'];
                                            } else {
                                                $p_data = array();
                                                $p_data['parenter_cnname'] = $sqlone['parenter_cnname'];
                                                $p_data['parenter_mobile'] = $sqlone['parenter_mobile'];
                                                $p_data['parenter_addtime'] = time();
                                                $parenter_id = $this->DataControl->insertData("smc_parenter", $p_data);
                                            }
    
                                            if (!$this->DataControl->getFieldOne("smc_student_family", "family_id", "student_id='{$student_id}' and parenter_id='{$parenter_id}'")) {
    
                                                $p_data = array();
                                                $p_data['student_id'] = $student_id;
                                                $p_data['parenter_id'] = $parenter_id;
                                                $p_data['family_relation'] = $sqlone['family_relation'];
                                                $p_data['family_mobile'] = $sqlone['parenter_mobile'];
                                                $p_data['family_cnname'] = $sqlone['parenter_cnname'];
                                                $p_data['family_isdefault'] = 1;
                                                $this->DataControl->insertData("smc_student_family", $p_data);
    
                                            }
    
                                        }
    
                                        $Model = new \Model\Smc\TransactionModel($request);
    
                                        if (!$this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "student_id='{$student_id}' and school_id='{$this->schoolOne['school_id']}' and enrolled_status>=0 and enrolled_status<2")) {
                                            $Model->entrySchool($student_id);
                                        }
    
                                        if (isset($sqlone['class_branch']) && $sqlone['class_branch'] != '') {
                                            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,course_id", "class_branch='{$sqlone['class_branch']}' and school_id='{$this->schoolOne['school_id']}'");
    
                                            if ($classOne) {
                                                $sql = "select ss.study_id from smc_student_study as ss,smc_class as c 
                                                      where ss.class_id=c.class_id and c.course_id='{$classOne['course_id']}' 
                                                      and ss.student_id='{$student_id}' and ss.study_isreading='1' ";
    
                                                if ($this->DataControl->selectOne($sql)) {
                                                    $bool = true;
                                                    $data['reason'] = $this->LgStringSwitch('学生已在该课程就读');
                                                    $f_num += 1;
                                                } else {
                                                    $Model->entryClass($student_id, '', $classOne['class_id'], $sqlone['study_beginday']);
    
                                                    $t_num += 1;
                                                }
    
    
                                            } else {
                                                $bool = true;
                                                $data['reason'] = $this->LgStringSwitch('该班级不存在');
                                                $f_num += 1;
                                            }
                                        } else {
                                            $t_num += 1;
                                        }
                                    } else {
                                        $bool = true;
                                        $data['reason'] = $this->LgStringSwitch('数据库错误');
                                        $f_num += 1;
                                    }
                                } else {
                                    $bool = true;
                                    $data['reason'] = $this->LgStringSwitch('出生日期格式不正确,格式如2018-01-01');
                                    $f_num += 1;
                                }
                            } else {
                                $bool = true;
                                $data['reason'] = $this->LgStringSwitch('出生日期格式不正确,格式如2018-01-01');
                                $f_num += 1;
                            }
                        } else {
                            $bool = true;
                            $data['reason'] = $this->LgStringSwitch('家长联系手机号码格式错误');
                            $f_num += 1;
                        }
                    }
                } else {
                    if (!$sqlone['student_cnname']) {
                        $data['reason'] = $this->LgStringSwitch('学生姓名必填');
                    }
    
                    if (!$sqlone['student_sex']) {
                        if ($data['reason']) {
                            $data['reason'] .= $this->LgStringSwitch('学生性别必填');
                        } else {
                            $data['reason'] = $this->LgStringSwitch('学生性别必填');
                        }
                    }
    
                    if (!$sqlone['parenter_mobile']) {
                        if ($data['reason']) {
                            $data['reason'] .= $this->LgStringSwitch(',联系电话必填');
                        } else {
                            $data['reason'] = $this->LgStringSwitch('联系电话必填');
                        }
                    }
    
                    if (!$sqlone['student_birthday']) {
                        if ($data['reason']) {
                            $data['reason'] .= $this->LgStringSwitch(',出生日期必填');
                        } else {
                            $data['reason'] = $this->LgStringSwitch('出生日期必填');
                        }
                    }
    
                    $bool = true;
                    $f_num += 1;
                }
                if ($bool) {
                    $tem_array[] = $data;
                }
            }
    
            $array = array();
            $array['errorlog_id'] = 0;
            if ($tem_array) {
                $data = array();
                $data['errorlog_json'] = json_encode($tem_array, JSON_UNESCAPED_UNICODE);
                $data['errorlog_createtime'] = time();
                $errorlog_id = $this->DataControl->insertData("smc_tolead_errorlog", $data);
                $array['errorlog_id'] = $errorlog_id;
            }
    
            $array['t_num'] = $t_num;
            $array['f_num'] = $f_num;
            $array['list'] = $tem_array;
            return $array;
        }

/**
     * exportErrorStudent
     */
    function exportErrorStudent($request)
        {
    
            $sql = "select te.errorlog_json from smc_tolead_errorlog as te where te.errorlog_id='{$request['errorlog_id']}'";
    
            $logOne = $this->DataControl->selectOne($sql);
            if (!$logOne) {
                $this->error = true;
                $this->errortip = "无错误记录";
                return false;
            }
    
            $dateexcelarray = json_decode($logOne['errorlog_json'], 1);
    
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    $datearray['family_relation'] = $dateexcelvar['family_relation'];
                    $datearray['student_idcard'] = $dateexcelvar['student_idcard'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    $datearray['reason'] = $dateexcelvar['reason'];
                    $outexceldate[] = $datearray;
                }
            }
    
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "性别", "民族", "出生日期", "家长姓名", "家长手机号", "家长与幼儿关系", "证件号码", "班级编号", "入班日期", "原因"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_sex', 'student_birthday', 'parenter_cnname', 'parenter_mobile', 'family_relation', 'student_idcard', 'class_branch', 'study_beginday', 'reason');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("幼儿导入错误记录.xlsx"));
    
            }

/**
     * getStuClassCheckApi
     */
    function getStuClassCheckApi($request)
        {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_synchro_day", "school_id='{$this->schoolOne['school_id']}'");
            $stuhourstudyList = $this->DataControl->selectClear("
                select c.hourstudy_id,ch.hour_id
                from  smc_student_hourstudy as c
                left join smc_class_hour as ch ON c.hour_id = ch.hour_id
                where c.student_id='{$request['student_id']}' and c.class_id='{$request['class_id']}'
                and ch.hour_day >= (select study_beginday from smc_student_study as s where s.student_id=c.student_id  and class_id = ch.class_id and ch.hour_iswarming = 0  limit 0,1)
              ");
    
            if ($stuhourstudyList) {
                $arr_hour_id = array_column($stuhourstudyList, "hour_id");
                $str_hour_id = implode(',', $arr_hour_id);
            } else {
                $str_hour_id = '0';
            }
            $datawhere = '1';
            if ($schoolOne['school_synchro_day']) {
                $schoolOne['school_synchro_day'] = date("Y-m-d", strtotime($schoolOne['school_synchro_day']));
                $datawhere .= " and  ch.hour_day > '{$schoolOne['school_synchro_day']}' ";
            }
    
            $classhourList = $this->DataControl->selectClear("
            select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_isfree,hour_name,
            (select hourstudy_id from smc_student_hourstudy as shy where shy.hour_id=ch.hour_id and shy.student_id ='{$request['student_id']}') as hourstudy_id,
            (select count(fc.coursetimes_id) from smc_student_free_coursetimes as fc where fc.hour_lessontimes=ch.hour_lessontimes and fc.class_id=ch.class_id and fc.student_id='{$request['student_id']}' and fc.is_use=0) as is_coursetimes_id
            from smc_class_hour  as ch
            where  ch.hour_ischecking =1 and   ch.class_id  ='{$request['class_id']}' 
            and ch.hour_day >= (select study_beginday from smc_student_study as s where s.student_id='{$request['student_id']}'  and class_id = ch.class_id  limit 0,1)
            and  {$datawhere}
            and ch.hour_day < (select study_endday from smc_student_study as s where s.student_id='{$request['student_id']}'  and class_id = ch.class_id  limit 0,1)
            and ch.hour_id  not in ({$str_hour_id})  and ch.hour_iswarming = 0
              ");
    
            if ($classhourList) {
                $num = count($classhourList);
                $data['num'] = $num;
    
                foreach ($classhourList as &$value) {
                    $value['hour_ischecking_name'] = $this->LgStringSwitch($value['hourstudy_id'] == Null ? '待考勤' : '已考勤');
                    $value['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                    $value['hour_isfree_name'] = $this->LgStringSwitch($value['hour_isfree'] == '0' ? '计费' : '免费');
                    if (intval($value['is_coursetimes_id']) > 0) {
                        $value['hour_isfree_name'] = $this->LgStringSwitch('免费');
                    }
                }
            } else {
                $classhourList = array();
                $data['num'] = 0;
            }
    
            $data['list'] = $classhourList;
            return $data;
        }
    
        /**
         * 期度类班级排课
         *  每天排一节
         * author: ling
         * 对应接口文档 0001
         */

/**
     * getDurationStuHourstudy
     */
    function getDurationStuHourstudy($reqeust)
        {
            $firstday = date('Y-m-01', strtotime($reqeust['year_moth']));
            $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
            $info = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname,student_img,student_enname,student_sex", "student_id='{$reqeust['student_id']}'");
            $info['year_moth'] = $reqeust['year_moth'];
    
            $datawhere = '1';
            if (isset($reqeust['hourstudy_checkin']) && $reqeust['hourstudy_checkin'] == 1) {
                $datawhere .= " and shy.hourstudy_checkin ='{$reqeust['hourstudy_checkin']}'";
            }
            $hourList = $this->DataControl->selectClear(" 
                select ch.hour_id,shy.hourstudy_id,shy.hourstudy_checkin,scg.clockinginlog_note,ch.hour_day,ch.hour_ischecking
                from smc_class_hour as ch
                left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id and shy.student_id = '{$reqeust['student_id']}'
                left join smc_student_clockinginlog as scg ON scg.hourstudy_id = shy.hourstudy_id and shy.student_id = scg.student_id 
                where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}'  and ch.class_id='{$reqeust['class_id']}'  and {$datawhere} order by hour_day ASC
                 ");
            $hour_checkinnum = 0;
            $hour_nocheckinnum = 0;
            $no_checkingnnum = 0;
            $hour_allnum = 0;
            if ($hourList) {
                foreach ($hourList as &$value) {
                    $hour_allnum++;
                    if ($value['hourstudy_checkin'] == 1) {
                        $hour_checkinnum++;
                    } elseif ($value['hourstudy_checkin'] == 0 && $value['hour_ischecking'] == 1) {
                        $hour_nocheckinnum++;
                    }
                    if ($value['hour_ischecking'] == 0) {
                        $no_checkingnnum++;
                    }
                    if (!$value['clockinginlog_note']) {
                        $value['clockinginlog_note'] = '--';
                    }
                    if ($value['hourstudy_checkin'] == 1 && $value['hour_ischecking'] == 1) {
                        $value['hourstudy_checkin_name'] = $this->LgStringSwitch('出勤');
                    } elseif ($value['hourstudy_checkin'] == 0 && $value['hour_ischecking'] == 1) {
                        $value['hourstudy_checkin_name'] = $this->LgStringSwitch('缺勤');
                    } else {
                        $value['hourstudy_checkin_name'] = '--';
                    }
                }
            } else {
                $hourList = array();
            }
            $info['hour_allnum'] = $hour_allnum;
            $info['hour_checkinnum'] = $hour_checkinnum;
            $info['hour_nocheckinnum'] = $hour_nocheckinnum;
            $info['no_checkingnnum'] = $no_checkingnnum;
            $data = array();
            $data['info'] = $info;
            $data['list'] = $hourList;
            return $data;
        }

    // 方法将在这里添加
}