<?php

namespace Model\Smc\Optimized;

/**
 * 基础功能 Trait
 * 包含: 构造函数、权限验证等
 */
trait ClassBaseTrait
{
/**
     * __construct
     */
    function __construct($publicarray = array())
        {
            parent::__construct();
            if (is_array($publicarray)) {
                $this->setPublic($publicarray);
                $this->publicarray = $publicarray;
            }
        }

/**
     * setPublic
     */
    function setPublic($publicarray)
        {
            if (isset($publicarray['company_id'])) {
                $this->verdictCompany($publicarray['company_id']);
            } else {
                $this->error = true;
                $this->errortip = "企业ID必须传入";
                return false;
            }
            if (isset($publicarray['school_id'])) {
                $this->verdictSchool($publicarray['school_id']);
            } else {
                $this->error = true;
                $this->errortip = "学校ID必须传入";
                return false;
            }
            if (isset($publicarray['staffer_id'])) {
                $this->verdictStaffer($publicarray['staffer_id']);
            } else {
                $this->error = 0;
                $this->errortip = "操作ID必须传入";
                return false;
            }
        }
    
        //验证校园信息

/**
     * verdictSchool
     */
    function verdictSchool($school_id)
        {
            $this->schoolOne = $this->DataControl->getFieldOne("smc_school",
                "school_id,company_id,school_branch,school_shortname,school_cnname,school_openclass,school_isclose,school_istemporaryclose,school_temporaryclosetip"
                , "school_id = '{$school_id}'");
            if (!$this->schoolOne) {
                $this->error = true;
                $this->errortip = "校园信息不存在";
                return false;
            } else {
                return true;
            }
        }
    
        //验证教师信息

/**
     * verdictStaffer
     */
    function verdictStaffer($staffer_id)
        {
            $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,account_class,staffer_ismanage", "staffer_id = '{$staffer_id}'");
            if (!$this->stafferOne) {
                $this->error = 0;
                $this->errortip = "教师信息不存在";
                return false;
            } else {
                return true;
            }
        }

    // 方法将在这里添加
}