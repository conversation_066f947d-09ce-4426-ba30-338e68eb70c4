<?php

namespace Model\Smc\Optimized;

/**
 * 考勤管理功能 Trait
 * 包含: 点名、预约系统等
 */
trait AttendanceTrait
{
/**
     * classRollCall
     */
    function classRollCall($request)
        {
            $today = date("Y-m-d", time());
            $datawhere = " 1 ";
            if (isset($request['today']) && $request['today'] !== '') {
                $datawhere .= " and ch.hour_day = '{$request['today']}'";
            } else {
                $datawhere .= " and ch.hour_day = '{$today}'";
            }
            $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,hour_noon
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                  where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}'
                  order by ch.hour_starttime DESC
            ";
            $hourList = $this->DataControl->selectClear($sql);
            $classroomArray = array();
            foreach ($hourList as $k => $v) {
                $classroomArray[] = $v['classroom_branch'];
            }
    
            $classroomArray = array_unique($classroomArray);
            $data = array();
            $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
            if ($hourList) {
                foreach ($hourList as $key => $val) {
                    foreach ($noon as $noonkey => $noonval) {
                        if ($val['hour_noon'] == $noonkey) {
                            foreach ($classroomArray as $roomkey => $room) {
                                if ($val['classroom_branch'] == $room) {
                                    $data[$noonval]["a" . $room][] = $val;
                                } else {
                                    $data[$noonval]["a" . $room]['-1'] = '';
                                }
                            }
                        }
                    }
                }
                if (count($data) < 3) {
                    $tem_data = array();
                    foreach ($data as $k => $v) {
                        $tem_data[] = $k;
                    }
                    $tem_noon = array_diff($noon, $tem_data);
                    if ($tem_noon) {
                        foreach ($tem_noon as $key => $val) {
                            foreach ($classroomArray as $roomkey => $room) {
                                $data[$val]["a" . $room]['-1'] = '';
                            }
                        }
                    }
                }
            } else {
                foreach ($noon as $key => $val) {
                    foreach ($classroomArray as $roomkey => $room) {
                        $data[$val]["a" . $room]['-1'] = '';
                    }
                }
            }
            $data = $this->get_arr($data);
            return $data;
        }

/**
     * classAttendance
     */
    function classAttendance($request)
        {
    
            $datawhere = " 1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
            }
    
            if (isset($request['starttime']) && $request['starttime'] !== '') {
                $datawhere .= " and sch.hour_day>='{$request['starttime']}'";
            }
            if (isset($request['endtime']) && $request['endtime'] !== '') {
                $datawhere .= " and sch.hour_day<='{$request['endtime']}'";
            }
            if (isset($request['hour_way']) && $request['hour_way'] !== '') {
                $datawhere .= " and sch.hour_way='{$request['hour_way']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
    
    //        ,(select s.staffer_cnname from smc_class_hour_teaching as ho left join smc_staffer as s on s.staffer_id=ho.staffer_id where ho.teaching_type=0 and ho.hour_id=ch.hour_id limit 0,1) as mainteacher
    //              ,(select st.staffer_cnname from smc_class_hour_teaching as hou left join smc_staffer as st on st.staffer_id=hou.staffer_id where hou.teaching_type=1 and hou.hour_id=ch.hour_id limit 0,1) as auxiliaryteacher
    
            $sql = "select sch.hour_id,sch.hour_day,s.staffer_cnname as mainteacher,s.staffer_enname as mainenname
                  ,sch.hour_way
                  ,(select count(hourstudy_id) from smc_student_hourstudy as ssh where ssh.class_id=c.class_id and ssh.hour_id=sch.hour_id) as totalnum
                  ,(select count(hourstudy_id) from smc_student_hourstudy as ssh where ssh.class_id=c.class_id and ssh.hour_id=sch.hour_id and ssh.hourstudy_checkin=1) as relnum
                  ,(select st.staffer_cnname from smc_class_hour_teaching as hou left join smc_staffer as st on st.staffer_id=hou.staffer_id where hou.teaching_type=1 and hou.hour_id=ch.hour_id limit 0,1) as auxiliaryteacher
                  ,(select st.staffer_enname from smc_class_hour_teaching as hou left join smc_staffer as st on st.staffer_id=hou.staffer_id where hou.teaching_type=1 and hou.hour_id=ch.hour_id limit 0,1) as auxiliaryenname
                  from smc_class_hour as sch
                  left join smc_class_hour_teaching as ch on sch.hour_id=ch.hour_id and teaching_type=0
                  left join smc_class as c on c.class_id=sch.class_id
                  left join smc_staffer as s on s.staffer_id=ch.staffer_id and ch.teaching_type=0
                  where {$datawhere} and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}' and sch.hour_ischecking=1
                  limit {$pagestart},{$num}
            ";
            $attendanceList = $this->DataControl->selectClear($sql);
            if (!$attendanceList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $way = $this->LgArraySwitch(array('0' => '实体课', '1' => '线上课'));
    
            foreach ($attendanceList as &$attendanceOne) {
    
                $attendanceOne['mainteacher'] = $attendanceOne['mainenname'] ? $attendanceOne['mainteacher'] . '-' . $attendanceOne['mainenname'] : $attendanceOne['mainteacher'];
    
                $attendanceOne['auxiliaryteacher'] = $attendanceOne['auxiliaryenname'] ? $attendanceOne['auxiliaryteacher'] . '-' . $attendanceOne['auxiliaryenname'] : $attendanceOne['auxiliaryteacher'];
    
                $attendanceOne['hour_way_name'] = $way[$attendanceOne['hour_way']];
    
                if ($attendanceOne['totalnum']) {
                    $attendanceOne['ratio'] = round(($attendanceOne['relnum'] / $attendanceOne['totalnum']) * 100, 2) . '%';
                } else {
                    $attendanceOne['ratio'] = '0' . '%';
                }
                $attendanceOne['statue'] = $attendanceOne['relnum'] . '/' . $attendanceOne['totalnum'];
    
                $attendanceOne['number'] = 1;
            }
    
            $data = array();
            $count_sql = "select sch.hour_id
              from smc_class_hour as sch
              left join smc_class_hour_teaching as ch on sch.hour_id=ch.hour_id and ch.teaching_type=0
              left join smc_class as c on c.class_id=sch.class_id
              left join smc_staffer as s on s.staffer_id=ch.staffer_id and ch.teaching_type=0
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and c.company_id='{$request['company_id']}' and sch.hour_ischecking=1";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
    
            $data['list'] = $attendanceList;
            return $data;
        }

/**
     * classStuBooking
     */
    function classStuBooking($request)
        {
    
            $datawhere = "1";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or ch.hour_name like '%{$request['keyword']}%')";
            }
    
            if (isset($request['booking_checkin']) && $request['booking_checkin'] !== '') {
                $datawhere .= " and  ch.hour_ischecking = '{$request['booking_checkin']}'";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "select  DISTINCT cb.booking_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,cb.booking_checkin,staffer_cnname,staffer_enname,ch.hour_day as booking_day,ch.hour_name,concat(ch.hour_starttime,'-',ch.hour_endtime) as hour_during,ch.hour_ischecking,s.student_id,ch.hour_id,ch.hour_ischecking,cb.booking_status
    				 from smc_class_booking as cb
    				 left join smc_class_hour as ch ON cb.hour_id = ch.hour_id and ch.class_id =cb.class_id
    				 left join smc_student as s ON s.student_id = cb.student_id
    				 left join smc_class_hour_teaching as ct on ct.class_id=cb.class_id and ct.hour_id =cb.hour_id and ct.teaching_type=0
                     left join smc_staffer as sf on sf.staffer_id=ct.staffer_id
    				  where  {$datawhere} and cb.class_id='{$request['class_id']}' and  cb.booking_status =0 order by hour_day DESC limit {$pagestart},{$num}";
    
    
            $BookingList = $this->DataControl->selectClear($sql);
    
            if (isset($request['is_count']) && $request['is_count'] !== '') {
                $num = $this->DataControl->selectClear("select cb.booking_id as num
    				 from smc_class_booking as cb
    				 left join smc_class_hour as ch ON cb.hour_id = ch.hour_id and ch.class_id =cb.class_id
    				 left join smc_student as s ON s.student_id = cb.student_id
    				 left join smc_class_hour_teaching as ct on ct.class_id=cb.class_id and ct.hour_id =cb.hour_id and  ct.teaching_type=0
                      left join smc_staffer as sf on sf.staffer_id=ct.staffer_id
    				  where {$datawhere}  and cb.class_id='{$request['class_id']}' and cb.booking_status =0 GROUP  by booking_id ");
                $num = count($num);
    
                if ($num) {
                    $data['allnum'] = $num;
                } else {
                    $data['allnum'] = 0;
                }
            }
    
            $array_booking_status = $this->LgArraySwitch(array('-1' => '已取消', '0' => '正常', '1' => '正常'));
            if (!$BookingList) {
                return array();
            } else {
                foreach ($BookingList as &$bookingOne) {
                    $bookingOne['staffer_cnname'] = $bookingOne['staffer_enname'] ? $bookingOne['staffer_cnname'] . '-' . $bookingOne['staffer_enname'] : $bookingOne['staffer_cnname'];
    
                    if ($bookingOne['hour_ischecking'] == 1) {
                        $bookingOne['hour_ischecking_name'] = $this->LgStringSwitch("是");
                    } else {
                        $bookingOne['hour_ischecking_name'] = $this->LgStringSwitch("否");
                    }
                    if ($bookingOne['booking_status'] == 0) {
                        $bookingOne['booking_status'] = 1;
                    }
                    $bookingOne['booking_status_name'] = $array_booking_status[$bookingOne['booking_status']];
                }
                $data['list'] = $BookingList;
            }
            return $data;
        }

/**
     * addBooking
     */
    function addBooking($request)
        {
            $data = array();
            $data['hour_id'] = $request['hour_id'];
            $data['class_id'] = $request['class_id'];
            $data['student_id'] = $request['student_id'];
            $data['booking_createtime'] = time();
            if ($this->DataControl->getOne('smc_class_booking', "hour_id='{$request['hour_id']}' and  student_id='{$request['student_id']}' and booking_status =0 ")) {
                $this->errortip = "该学员已经预约过该课时";
                $this->error = 1;
                return false;
            }
    
            $sql = "select a.class_appointnum
                    ,(select count(booking_id) from smc_class_booking where class_id=a.class_id and hour_id='{$request['hour_id']}'and booking_status =0) as booking_num
                    from smc_class a
                    where a.class_id='{$request['class_id']}' 
                    and a.class_status>=0";
            $hourOne = $this->DataControl->selectOne($sql);
            if ($hourOne && $hourOne['class_appointnum'] > 0 && $hourOne['booking_num'] >= $hourOne['class_appointnum']) {
                $this->error = 1;
                $this->errortip = "该课时学员已约满，请预约其他课时";
                return false;
            }
    
            if ($this->DataControl->insertData("smc_class_booking", $data)) {
                $this->errortip = "新增成功";
                $this->error = 0;
                return true;
            } else {
                $this->errortip = "新增失败";
                $this->error = 1;
                return false;
            }
    
        }
    
    //	多人预约

/**
     * addStuBooking
     */
    function addStuBooking($request)
        {
    
            $bookinglist = json_decode(stripslashes($request['bookinglist']), true);
    
            $stuBoList = $this->DataControl->selectClear("select  student_id from smc_class_booking where hour_id='{$request['hour_id']}' and  class_id='{$request['class_id']}' and  booking_status=0");
            if ($stuBoList) {
                $arr_student_id = array_column($stuBoList, 'student_id');
            } else {
                $arr_student_id = array();
            }
    
            if ($bookinglist && is_array($bookinglist)) {
                foreach ($bookinglist as $value) {
                    if (in_array($value['student_id'], $arr_student_id)) {
                        continue;
                    } else {
                        $data = array();
                        $data['hour_id'] = $value['hour_id'];
                        $data['class_id'] = $value['class_id'];
                        $data['student_id'] = $value['student_id'];
                        $data['booking_createtime'] = time();
                        $this->DataControl->insertData("smc_class_booking", $data);
                    }
                }
            }
    
            return true;
        }
    
    //    批量预约记录

/**
     * batchCancelBooking
     */
    function batchCancelBooking($request)
        {
            $request['booking_id'] = json_decode(stripslashes($request['booking_id']), true);
            if (isset($request['booking_id']) && !is_array($request['booking_id'])) {
                $request['booking_id'] = [$request['booking_id']];
            }
            foreach ($request['booking_id'] as $bookingId) {
                $bookingOne = $this->DataControl->getFieldOne("smc_class_booking", "class_id,hour_id,student_id", "booking_id='{$bookingId}'");
                $data = array();
                $data['student_id'] = $bookingOne['student_id'];
                $data['class_id'] = $bookingOne['class_id'];
                $data['hour_id'] = $bookingOne['hour_id'];
                $this->cancelBooking($data);
            }
            return true;
        }

/**
     * cancelBooking
     */
    function cancelBooking($request)
        {
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", 'hour_ischecking', "hour_id='{$request['hour_id']}'");
            if ($hourOne['hour_ischecking'] == 1) {
                return false;
            }
    
            if ($booking = $this->DataControl->getOne('smc_class_booking', "class_id='{$request['class_id']}' and  hour_id ='{$request['hour_id']}' and  student_id ='{$request['student_id']}' and booking_status = 0")) {
                $booking_data = array();
                $booking_data['booking_status'] = '-1';
                $booking_data['booking_updatatime'] = time();
                if ($this->DataControl->updateData('smc_class_booking', "booking_id='{$booking['booking_id']}'", $booking_data)) {
                    return true;
                } else {
                    return false;
                }
            }
            return false;
        }

    // 方法将在这里添加
}