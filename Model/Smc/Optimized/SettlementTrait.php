<?php

namespace Model\Smc\Optimized;

/**
 * 结算系统功能 Trait
 * 包含: 结算列表、结算操作等
 */
trait SettlementTrait
{
/**
     * classSettlementList
     */
    function classSettlementList($request)
        {
            $datawhere = " 1 ";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "select s.student_id,s.student_cnname,s.student_branch,s.student_sex,ss.study_beginday,ss.study_endday
                  from smc_student_study as ss
                  left join smc_student as s on s.student_id=ss.student_id
                  where {$datawhere} and ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}' and substring(ss.study_beginday,1,7)<='{$request['year_moth']}' and substring(ss.study_endday,1,7)>='{$request['year_moth']}'
                  group by s.student_id
                  order by s.student_id ASC,ss.study_beginday DESC
                  limit {$pagestart},{$num}
            ";
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
    
            foreach ($studentList as &$studentOne) {
                $sql = "select sh.hourstudy_id
                      from smc_student_hourstudy as sh
                      left join smc_class_hour as sch on sch.hour_id=sh.hour_id
                      where sch.hour_day>='{$studentOne['study_beginday']}' and sch.hour_day<='{$studentOne['study_endday']}'
                      and substring(sch.hour_day,1,7)='{$request['year_moth']}' and sh.student_id='{$studentOne['student_id']}' and sh.hourstudy_checkin='1' and sh.class_id='{$request['class_id']}'";
                $attendanceList = $this->DataControl->selectClear($sql);
    
                if ($attendanceList) {
                    $studentOne['attendanceNum'] = count($attendanceList);
                } else {
                    $studentOne['attendanceNum'] = 0;
                }
                $sql = "select sch.hour_id
                      from smc_class_hour as sch
                      where sch.hour_day>='{$studentOne['study_beginday']}' and sch.hour_day<='{$studentOne['study_endday']}'
                      and substring(sch.hour_day,1,7)='{$request['year_moth']}' and sch.class_id='{$request['class_id']}'";
    
                $hourList = $this->DataControl->selectClear($sql);
                if ($hourList) {
                    $studentOne['hourNum'] = count($hourList);
                } else {
                    $studentOne['hourNum'] = 0;
                }
    
                $studentOne['hourstudy_nochecknum'] = $studentOne['hourNum'] - $studentOne['attendanceNum'];
            }
    
            $data = array();
            $count_sql = "select s.student_id,s.student_cnname,s.student_branch,s.student_sex,ss.study_beginday,ss.study_endday
                  from smc_student_study as ss
                  left join smc_student as s on s.student_id=ss.student_id
                  where {$datawhere} and ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}' and substring(ss.study_beginday,1,7)<='{$request['year_moth']}' and substring(ss.study_endday,1,7)>='{$request['year_moth']}'
                  group by s.student_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
    
            $sql = "select sc.courseshare_id from smc_student_courseshare as sc
                  left join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id
                  left join smc_student_study as ss on ss.student_id=scb.student_id and ss.class_id=sc.class_id
                  where ss.class_id='{$request['class_id']}' and sc.courseshare_month='{$request['year_moth']}' and sc.courseshare_status='1'
                  ";
    
            if ($this->DataControl->selectOne($sql)) {
                $data['can_settle'] = 0;
            } else {
                $data['can_settle'] = 1;
            }
    
            $data['list'] = $studentList;
    
            return $data;
        }

/**
     * durationSettleList
     */
    function durationSettleList($reqeust)
        {
    
            if (!$reqeust['year_moth']) {
                $reqeust['year_moth'] = date('Y-m');
            }
            $datawhere = '1';
            if (isset($reqeust['keyword']) && $reqeust['keyword'] !== '') {
                $datawhere .= " and (s.student_cnname like '%{$reqeust['keyword']}%'  or s.student_branch like '%{$reqeust['keyword']}%')";
            }
    
    
            $firstday = date('Y-m-01', strtotime($reqeust['year_moth']));
            $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
    
    
            $datawhere .= " and ss.study_isreading =1 and ss.class_id='{$reqeust['class_id']}' and ss.study_beginday <='{$lastday}' and ss.study_endday >='{$lastday}'";
    
            $sql = "
                select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,
                (select count(hourstudy_id) 
                from smc_class_hour as ch 
                left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id
                where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}' and shy.student_id = s.student_id
                ) as hourstudy_allnum, 
                   (select count(hourstudy_id) 
                from smc_class_hour as ch 
                left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id
                where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}' and shy.student_id = s.student_id and shy.hourstudy_checkin=1
                ) as hourstudy_checknum,
                (select count(hourstudy_id) 
                from smc_class_hour as ch 
                left join smc_student_hourstudy as shy ON ch.hour_id=shy.hour_id and shy.class_id=ch.class_id
                where ch.hour_day >= '{$firstday}' and ch.hour_day<='{$lastday}' and shy.student_id = s.student_id and shy.hourstudy_checkin=1
                ) as hourstudy_nochecknum
                from smc_student as s 
                left join smc_student_study as ss ON ss.student_id = s.student_id
                where {$datawhere}";
            $stuList = $this->DataControl->selectClear($sql);
    
    
            if (!$stuList) {
                $data['list'] = array();
                $data['allnum'] = 0;
            } else {
                foreach ($stuList as &$value) {
                    $value['hourstudy_nochecknum'] = intval($value['hourstudy_nochecknum']);
                    $value['hourstudy_checknum'] = intval($value['hourstudy_checknum']);
                    $value['hourstudy_allnum'] = intval($value['hourstudy_allnum']);
                }
    
                $all_num = $this->DataControl->selectOne(" select count(s.student_id) as stu_num from smc_student as s 
                left join smc_student_study as ss ON ss.student_id = s.student_id
                where {$datawhere}");
                $data['list'] = $stuList;
                $data['allnum'] = $all_num['stu_num'];
            }
    
    
            return $data;
    
    
        }
    
        /**
         *
         * author: ling
         * 对应接口文档 0001
         * @param $reqeust
         */

/**
     * settlement
     */
    function settlement($request)
        {
    
    //        $this->error = true;
    //        $this->errortip = "该功能暂时关闭";
    //        return false;
    
            $sql = "select sc.courseshare_id from smc_student_courseshare as sc
                  left join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id
                  left join smc_student_study as ss on ss.student_id=scb.student_id
                  where ss.class_id='{$request['class_id']}' and sc.courseshare_month='{$request['month']}' and sc.courseshare_status='1'
                  ";
    
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "该月已经结算";
                return false;
            }
    
            $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");
    
            if (!$classOne) {
                $this->error = true;
                $this->errortip = "班级不存在";
                return false;
            }
    
            $lastday = date('Y-m-t', time());
            if (date("Y-m-d") != $lastday && $request['month'] >= date("Y-m")) {
                $this->error = true;
                $this->errortip = "该月不可结算";
                return false;
            }
    
            if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and substring(hour_day,1,7)='{$request['month']}' and hour_ischecking='0' and hour_iswarming='0'")) {
                $this->error = true;
                $this->errortip = "当月存在未考勤课次，不可结算";
                return false;
            }
    
            $sql = "select s.student_id,s.student_cnname,s.student_branch
                  from smc_student_study as ss
                  left join smc_student as s on s.student_id=ss.student_id
                  where ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}'
                  and substring(ss.study_beginday,1,7)<='{$request['month']}' and substring(ss.study_endday,1,7)>='{$request['month']}'
                  group by s.student_id
                  order by s.student_id ASC,ss.study_beginday DESC
            ";
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            foreach ($studentList as $studentOne) {
                $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
                  from smc_student_coursebalance as scb
                  where scb.school_id='{$this->schoolOne['school_id']}' and scb.student_id='{$studentOne['student_id']}' and scb.course_id='{$classOne['course_id']}'
                  ";
    
                $stuCourseOne = $this->DataControl->selectOne($sql);
    
                $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$request['class_id']}' and courseshare_month='{$request['month']}'");//学员当月分摊金额
    
                if (!$monthOne) {
                    $this->error = true;
                    $this->errortip = $studentOne['student_cnname'] . "无分摊数据,不可结算";
                    return false;
                }
    
                $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$request['class_id']}' and courseshare_month='{$request['month']}' and courseshare_status='1'");//学员当月分摊金额
                if ($monthOne) {
                    $this->error = true;
                    $this->errortip = $studentOne['student_cnname'] . "已分摊,不可结算";
                    return false;
                }
            }
    
            $BalanceModel = new \Model\Smc\BalanceModel($request);
    
            foreach ($studentList as $studentOne) {
                $bool = $BalanceModel->settlement($studentOne['student_id'], $request['class_id'], $request['month']);
                if (!$bool) {
                    $this->error = true;
                    $this->errortip = $BalanceModel->errortip;
                    return false;
                }
            }
    
            return true;
        }

/**
     * getSettlementMonthList
     */
    function getSettlementMonthList($request)
        {
    
            $datahaving = " 1 ";
            $datawhere = "l.school_id='{$this->schoolOne['school_id']}'";
    
    
            if (isset($request['keyword']) && $request['keyword'] != "") {
                $datawhere .= " and (l.class_cnname like '%{$request['keyword']}%' or l.class_enname like '%{$request['keyword']}%' or l.class_branch like '%{$request['keyword']}%')";
            }
    
            if (isset($request['month']) && $request['month'] != '') {
                $datawhere .= " and substring(ch.hour_day,1,7)='{$request['month']}'";
            }
    
            if (isset($request['class_id']) && $request['class_id'] != '') {
                $datawhere .= " and ch.class_id='{$request['class_id']}'";
            }
    
            if (isset($request['status']) && $request['status'] != '') {
                if ($request['status'] == 0) {
                    $datahaving .= " and ((status1=0 and status2=0) or (notsettleNum>0))";
                } elseif ($request['status'] == 1) {
                    $datahaving .= " and status1>0";
                } elseif ($request['status'] == 2) {
                    $datahaving .= " and ((status1<>0 or status2<>0) and status1=0)";
                }
            }
    
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;
    
            $sql = "select m.class_id,m.class_cnname,m.class_enname,m.class_branch
                  ,(select count(h.hour_id) from smc_class_hour as h where substring(h.hour_day,1,7)=m.mon and h.hour_ischecking<>'-1' and h.class_id=m.class_id) as hourNum
                  ,ifnull((select sum(cs.share_settle_price) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon limit 0,1),0) as share_settle_price
                  ,ifnull((select sum(cs.share_confirm_price) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon limit 0,1),0) as share_confirm_price
                  ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=0 and cs.share_from=0 limit 0,1) as status1
                  ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and cs.share_from=0 limit 0,1) as status2
                  ,ifnull((select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and not exists(select 1 from smc_student_courseshare as sc inner join smc_student_coursebalance as scb where sc.coursebalance_id=scb.coursebalance_id and scb.student_id=cs.student_id and sc.class_id=cs.class_id and sc.courseshare_month=cs.share_month and sc.courseshare_status=0)),0) as settleNum
                  ,ifnull((select count(sc.courseshare_id) from smc_student_courseshare as sc inner join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id where sc.class_id=m.class_id and sc.courseshare_status=0 and sc.courseshare_month=m.mon),0) as notsettleNum
                  ,m.mon
                  from (select substring(ch.hour_day,1,7) as mon,ch.class_id,l.class_cnname,l.class_enname,l.class_branch from smc_class_hour as ch left join smc_class as l on ch.class_id=l.class_id left join smc_course as sc on l.course_id=sc.course_id where {$datawhere} and sc.course_inclasstype=1 and ch.hour_day<>'' group by l.class_id,substring(ch.hour_day,1,7)) as m
                  having {$datahaving}
                  order by m.class_id,m.mon asc
                  ";
    
            if (isset($request['from']) && $request['from'] == 1) {
                $sql .= ' limit ' . $pagestart . ',' . $num;
            }
    
            $list = $this->DataControl->selectClear($sql);
    
            if (!$list) {
                $this->error = true;
                $this->errortip = "班级未排课";
                return false;
            }
    
            foreach ($list as &$val) {
                $val['surplus_price'] = $val['share_settle_price'] - $val['share_confirm_price'];
    
                if ($val['status1'] == 0 && $val['status2'] == 0) {
                    $val['status_name'] = $this->LgStringSwitch('待结算');
                    $val['month_status'] = 0;
                } else {
                    if ($val['status1'] > 0) {
                        $val['status_name'] = $this->LgStringSwitch('结算中');
                        $val['month_status'] = 1;
                    } else {
                        if ($val['notsettleNum'] > 0) {
                            $val['status_name'] = $this->LgStringSwitch('待结算');
                            $val['month_status'] = 0;
                        } else {
                            $val['status_name'] = $this->LgStringSwitch('已结算');
                            $val['month_status'] = 2;
                        }
                    }
                }
            }
    
            $data = array();
            if (isset($request['from']) && $request['from'] == 1) {
                $count_sql = "select m.class_id
                  ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=0 and cs.share_from=0 limit 0,1) as status1
                  ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and cs.share_from=0 limit 0,1) as status2
                  ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and not exists(select 1 from smc_student_courseshare as sc inner join smc_student_coursebalance as scb where sc.coursebalance_id=scb.coursebalance_id and scb.student_id=cs.student_id and sc.class_id=cs.class_id and sc.courseshare_month=cs.share_month and sc.courseshare_status=0)) as settleNum
                  ,(select count(sc.courseshare_id) from smc_student_courseshare as sc inner join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id where sc.class_id=m.class_id and sc.courseshare_status=0 and sc.courseshare_month=m.mon) as notsettleNum
                  from (select substring(ch.hour_day,1,7) as mon,ch.class_id,l.class_cnname,l.class_enname,l.class_branch from smc_class_hour as ch left join smc_class as l on ch.class_id=l.class_id left join smc_course as sc on l.course_id=sc.course_id where {$datawhere} and sc.course_inclasstype=1 and ch.hour_day<>'' group by l.class_id,substring(ch.hour_day,1,7)) as m
                  having {$datahaving}
    				";
    
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            } else {
                $data['allnum'] = 0;
            }
    
            $data['list'] = $list;
            return $data;
        }

/**
     * getSettlementInfo
     */
    function getSettlementInfo($request)
        {
    
            $classOne = $this->DataControl->getFieldOne("smc_class", "course_id,class_cnname,class_enname,class_branch", "class_id='{$request['class_id']}'");
    
            if (!$classOne) {
                $this->error = true;
                $this->errortip = "班级不存在";
                return false;
            }
    
            $lastday = date('Y-m-t', time());
            if (date("Y-m-d") != $lastday && $request['month'] >= date("Y-m")) {
                $this->error = true;
                $this->errortip = "该月不可结算";
                return false;
            }
    
            if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and substring(hour_day,1,7)='{$request['month']}' and hour_ischecking='0' and hour_iswarming='0'")) {
                $this->error = true;
                $this->errortip = "当月存在未考勤课次，不可结算";
                return false;
            }
    
            $sql = "select (select ct.stustatus_isenclass from smc_student_changelog as sc left join smc_code_stuchange as ct on ct.stuchange_code=sc.stuchange_code
                      where sc.class_id=ss.class_id and sc.student_id=ss.student_id  and ((substring(sc.changelog_day,1,7)<='{$request['month']}' and ct.stustatus_isenclass=1) or (substring(sc.changelog_day,1,7)<'{$request['month']}' and ct.stustatus_isenclass=0))
                      order by sc.changelog_day desc,sc.changelog_id desc limit 0,1) as stustatus_isenclass
                  ,ifnull((select sh.hourstudy_id from smc_student_hourstudy as sh,smc_class_hour as ch where sh.hour_id=ch.hour_id and sh.student_id=ss.student_id and sh.class_id=ss.class_id and substring(ch.hour_day,1,7)='{$request['month']}' limit 0,1),0) as hourstudy_id
                  ,s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,cl.course_id
                  ,ifnull((select scb.coursebalance_figure from smc_student_coursebalance as scb where scb.student_id=ss.student_id and scb.school_id=ss.school_id and scb.course_id=cl.course_id limit 0,1),0) as coursebalance_figure
                  from smc_student_study as ss
                  left join smc_student as s on s.student_id=ss.student_id
                  left join smc_class as cl on cl.class_id=ss.class_id
                  where ss.class_id='{$request['class_id']}'
                  having stustatus_isenclass=1 or hourstudy_id>0
                  ";
    
    
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
    
            $sql = "select
                  (select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=0 and cs.share_from=0 limit 0,1) as status1
                  ,(select count(cs.share_id) from smc_student_class_share as cs where cs.class_id=m.class_id and cs.share_month=m.mon and cs.share_status=1 and cs.share_from=0 limit 0,1) as status2
                  from (select substring(ch.hour_day,1,7) as mon,ch.class_id from smc_class_hour as ch
                  where substring(ch.hour_day,1,7)='{$request['month']}' and ch.class_id='{$request['class_id']}' group by substring(ch.hour_day,1,7)) as m
                  order by m.mon asc
                  ";
    
            $listOne = $this->DataControl->selectOne($sql);
    
            if (!$listOne) {
                $this->error = true;
                $this->errortip = "班级未排课";
                return false;
            }
    
            if (isset($request['is_check']) && $request['is_check'] == 1) {
                return true;
            }
    
            if ($listOne['status1'] == 0 && $listOne['status2'] == 0) {
                $listOne['status_name'] = $this->LgStringSwitch('待结算');
                $listOne['month_status'] = 0;
            } else {
                $sql = "select sc.courseshare_id 
                        from smc_student_courseshare as sc
                        inner join smc_student_coursebalance as scb on scb.coursebalance_id=sc.coursebalance_id
                        where sc.class_id='{$request['class_id']}' and sc.courseshare_status=0 
                        and sc.courseshare_month='{$request['month']}'                        
                        group by sc.coursebalance_id";
    
                if ($listOne['status1'] > 0) {
                    $listOne['status_name'] = $this->LgStringSwitch('结算中');
                    $listOne['month_status'] = 1;
                } else {
                    if ($this->DataControl->selectClear($sql)) {
                        $listOne['status_name'] = $this->LgStringSwitch('待结算');
                        $listOne['month_status'] = 0;
                    } else {
                        $listOne['status_name'] = $this->LgStringSwitch('已结算');
                        $listOne['month_status'] = 2;
                    }
                }
            }
    
    
            $status = $this->LgArraySwitch(array('0' => '处理中', '1' => '已完成'));
            $BalanceModel = new \Model\Smc\BalanceModel($request);
    
            $tem_data = array();
            foreach ($studentList as $studentOne) {
                $data = array();
    
                $data['student_id'] = $studentOne['student_id'];
                $data['student_cnname'] = $studentOne['student_cnname'];
                $data['student_enname'] = $studentOne['student_enname'];
                $data['student_branch'] = $studentOne['student_branch'];
                $data['student_sex'] = $studentOne['student_sex'];
                $data['coursebalance_figure'] = $studentOne['coursebalance_figure'];
    
                $sql = "select cs.share_id,cs.share_attend_times,cs.share_absent_times,cs.share_calc_times,cs.share_settle_price,cs.share_calc_price,cs.share_apply_price,cs.share_confirm_price,cs.share_status
                          from smc_student_class_share as cs
                          where cs.student_id='{$studentOne['student_id']}' and cs.class_id='{$request['class_id']}' and cs.share_month='{$request['month']}' 
                          and not exists(select 1 from smc_student_courseshare as sc inner join smc_student_coursebalance as scb where sc.coursebalance_id=scb.coursebalance_id and scb.student_id='{$studentOne['student_id']}' and sc.class_id='{$request['class_id']}' and sc.courseshare_month='{$request['month']}' and sc.courseshare_status=0)
                          order by cs.share_id desc
                          limit 0,1
                          ";
                $shareOne = $this->DataControl->selectOne($sql);
    
                if ($shareOne) {
                    $data['share_id'] = $shareOne['share_id'];
                    $data['attendanceNum'] = $shareOne['share_attend_times'];
                    $data['absenceNum'] = $shareOne['share_absent_times'];
                    $data['hourNum'] = $shareOne['share_attend_times'] + $shareOne['share_absent_times'];
                    $data['price'] = $shareOne['share_confirm_price'] > 0 ? $shareOne['share_confirm_price'] : $shareOne['share_apply_price'];
                    $data['num'] = $shareOne['share_calc_times'];
                    $data['courseshare_price'] = $shareOne['share_settle_price'];
                    $data['surplus_price'] = $shareOne['share_settle_price'] - $data['price'];
                    $data['status_name'] = $status[$shareOne['share_status']];
    
                    $data['can_settle'] = 2;
                    $data['tip'] = '';
                } else {
    
                    $bool = $BalanceModel->settlementInfo($studentOne['student_id'], $request['class_id'], $request['month']);
    
                    if ($bool) {
    
                        $data['share_id'] = 0;
                        $data['attendanceNum'] = $bool['attendNum'];
                        $data['hourNum'] = $bool['hourNum'];
                        $data['absenceNum'] = $bool['absenceNum'];
                        $data['price'] = $bool['settle_price'];
                        $data['num'] = $bool['num'];
                        $data['courseshare_price'] = $bool['courseshare_price'];
                        $data['surplus_price'] = $bool['courseshare_price'] - $data['price'];
                        $data['status_name'] = $this->LgStringSwitch('待结算');
    
                        $data['can_settle'] = 1;
                        $data['tip'] = '';
                    } else {
    
    
                        $data['share_id'] = 0;
                        $data['attendanceNum'] = '--';
                        $data['hourNum'] = '--';
                        $data['absenceNum'] = '--';
                        $data['price'] = '--';
                        $data['num'] = '--';
                        $data['courseshare_price'] = '--';
                        $data['surplus_price'] = '--';
                        $data['status_name'] = '--';
    
                        $data['can_settle'] = 2;
                        $data['tip'] = $BalanceModel->errortip;
                    }
    
                }
    
                $tem_data[] = $data;
            }
    
            $classOne['month'] = $request['month'];
    
            $data = array();
            $data['list'] = $tem_data;
            $data['status'] = $listOne['month_status'];
            $data['classOne'] = $classOne;
            return $data;
        }

/**
     * submitSettlement
     */
    function submitSettlement($request)
        {
    
    
            $list = json_decode(stripslashes($request['list']), 1);
    
            $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
            foreach ($list as $listOne) {
                $settlementInfoOne = $BalanceModel->settlementInfo($listOne['student_id'], $listOne['class_id'], $listOne['month']);
    
                $sql = "select co.course_monthlyset,co.course_id 
                  from smc_class as cl,smc_course as co 
                  where cl.course_id=co.course_id and cl.class_id='{$listOne['class_id']}'";
    
                $courseOne = $this->DataControl->selectOne($sql);
    
                if (!$courseOne) {
                    $this->error = true;
                    $this->errortip = "无相关课程";
                    return false;
                }
    
                if ($settlementInfoOne) {
    
                    $data = array();
                    $data['company_id'] = $this->companyOne['company_id'];
                    $data['school_id'] = $this->schoolOne['school_id'];
                    $data['student_id'] = $listOne['student_id'];
                    $data['class_id'] = $listOne['class_id'];
                    $data['courseshare_id'] = $settlementInfoOne['courseshare_id'];
                    $data['share_month'] = $listOne['month'];
                    $data['share_attend_times'] = $settlementInfoOne['attendNum'];
                    $data['share_absent_times'] = $settlementInfoOne['absenceNum'];
                    $data['share_calc_times'] = $settlementInfoOne['num'];
                    $data['share_settle_price'] = $settlementInfoOne['courseshare_price'];
                    $data['share_calc_price'] = $settlementInfoOne['settle_price'];
    
                    $data['share_apply_price'] = $listOne['apply_price'];
                    $data['share_apply_staffer_id'] = $this->stafferOne['staffer_id'];
                    $data['share_apply_time'] = time();
    
                    $data['share_from'] = 0;
                    $data['share_status'] = 0;
    
    //                if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
    //                    $data['share_confirm_price'] = $settlementInfoOne['settle_price'];
    //                    $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
    //                    $data['share_confirm_time'] = time();
    //                    $data['share_status'] = 1;
    //                }
    
                    if ($courseOne['course_monthlyset'] == 0) {
    
                        $data['share_confirm_price'] = $listOne['apply_price'];
                        $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                        $data['share_confirm_time'] = time();
                        $data['share_status'] = 1;
    
                    } elseif ($courseOne['course_monthlyset'] == 1) {
                        if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
                            $data['share_confirm_price'] = $listOne['apply_price'];
                            $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                            $data['share_confirm_time'] = time();
                            $data['share_status'] = 1;
                        }
                    }
    
                    $share_id = $this->DataControl->insertData("smc_student_class_share", $data);
    
                    if ($courseOne['course_monthlyset'] == 0) {
    
                        $data['share_confirm_price'] = $listOne['apply_price'];
                        $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                        $data['share_confirm_time'] = time();
                        $data['share_status'] = 1;
    
                        $BalanceModel->settleAction($listOne['student_id'], $listOne['class_id'], $listOne['month'], $settlementInfoOne['courseshare_price'] - $listOne['apply_price'], 1, $listOne['apply_price']);
    
                    } elseif ($courseOne['course_monthlyset'] == 1) {
                        if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
                            $data['share_confirm_price'] = $listOne['apply_price'];
                            $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                            $data['share_confirm_time'] = time();
                            $data['share_status'] = 1;
                            $BalanceModel->settleAction($listOne['student_id'], $listOne['class_id'], $listOne['month'], $settlementInfoOne['courseshare_price'] - $listOne['apply_price'], 1, $listOne['apply_price']);
                        }
                    }
    
                    $data = array();
                    $data['share_id'] = $share_id;
                    $data['tracks_title'] = $this->LgStringSwitch('月度结算');
                    $data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师提交结算信息');
                    $data['tracks_note'] = $this->LgStringSwitch($listOne['tip']);
                    $data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                    $data['tracks_time'] = time();
    
                    $this->DataControl->insertData("smc_student_class_share_tracks", $data);
                }
            }
    
            return true;
        }

/**
     * checkSettlement
     */
    function checkSettlement($request)
        {
    
            $sql = "select ch.hour_day from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_isfree='0' and ch.hour_ischecking='0' and substring(ch.hour_day,1,7)='{$request['month']}' group by ch.hour_day order by ch.hour_day asc ";
            $hourList = $this->DataControl->selectClear($sql);
            if (!$hourList) {
                $hourList = array();
            }
            return $hourList;
        }
    
        /**
         *更新课时计划
         * author: ling
         * 对应接口文档 0001
         * @param $request
         * @return bool
         */

    // 方法将在这里添加
}