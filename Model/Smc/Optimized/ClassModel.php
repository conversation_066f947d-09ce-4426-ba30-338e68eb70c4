<?php

namespace Model\Smc\Optimized;

use Model\Smc\modelTpl;

/**
 * 优化版本的班级管理模型
 * 
 * 通过 Trait 模块化组织，将原本的单一大文件拆分为功能明确的模块：
 * 
 * 1. ClassBaseTrait - 基础功能模块
 * 2. ClassManagementTrait - 班级管理模块  
 * 3. StudentManagementTrait - 学生管理模块
 * 4. HourManagementTrait - 课时管理模块
 * 5. AttendanceTrait - 考勤管理模块
 * 6. SchedulingTrait - 排课系统模块
 * 7. SettlementTrait - 结算系统模块
 * 8. SpecialFeaturesTrait - 特殊功能模块
 * 9. UtilityTrait - 工具函数模块
 * 
 * 优势：
 * - 代码结构清晰，按功能模块组织
 * - 便于维护和扩展
 * - 减少单个文件的复杂度
 * - 提高代码的可读性和可测试性
 * - 保持所有原有方法名称不变，确保向后兼容
 * 
 * <AUTHOR> Structure
 * @version 2.0
 */
class ClassModel extends modelTpl
{
    // 引入各个功能模块的 Trait
    use ClassBaseTrait;           // 基础功能：构造函数、权限验证等
    use ClassManagementTrait;     // 班级管理：班级列表、增删改查等  
    use StudentManagementTrait;   // 学生管理：学生信息、操作、导入等
    use HourManagementTrait;      // 课时管理：课时表、操作、计划等
    use AttendanceTrait;          // 考勤管理：点名、预约系统等
    use SchedulingTrait;          // 排课系统：时间安排、资源分配等
    use SettlementTrait;          // 结算系统：结算列表、操作等
    use SpecialFeaturesTrait;     // 特殊功能：子班级、申请审核等
    use UtilityTrait;             // 工具函数：数据处理、排序等

    /**
     * 模型版本信息
     */
    const MODEL_VERSION = '2.0.0';
    const OPTIMIZATION_DATE = '2024-12-19';
    
    /**
     * 获取模型信息
     * 
     * @return array 包含版本和优化信息的数组
     */
    public function getModelInfo()
    {
        return [
            'version' => self::MODEL_VERSION,
            'optimization_date' => self::OPTIMIZATION_DATE,
            'structure' => 'Trait-based modular architecture',
            'modules' => [
                'ClassBaseTrait' => '基础功能模块',
                'ClassManagementTrait' => '班级管理模块',
                'StudentManagementTrait' => '学生管理模块', 
                'HourManagementTrait' => '课时管理模块',
                'AttendanceTrait' => '考勤管理模块',
                'SchedulingTrait' => '排课系统模块',
                'SettlementTrait' => '结算系统模块',
                'SpecialFeaturesTrait' => '特殊功能模块',
                'UtilityTrait' => '工具函数模块'
            ],
            'benefits' => [
                '模块化架构，代码结构清晰',
                '便于维护和单元测试',
                '保持向后兼容性',
                '提高开发效率'
            ]
        ];
    }

    /**
     * 获取所有可用方法列表（按模块分类）
     * 
     * @return array 按模块分类的方法列表
     */
    public function getMethodsByModule()
    {
        return [
            '基础功能' => [
                '__construct', 'setPublic', 'verdictSchool', 'verdictStaffer'
            ],
            '班级管理' => [
                'classList', 'coursetermClass', 'mothClass', 'getOpenClass', 'getClassMenu', 'classOne',
                'classCourseInfo', 'classTeacherInfo', 'classRefreshSort', 'classAdd',
                'classManage', 'classEdit', 'classDel', 'classChange'
            ],
            '学生管理' => [
                'classStudent', 'updateStuCourseBalance', 'classStuOptionalTimesList',
                'giveAwayLessons', 'automaticAudit', 'endClassStudent', 'openClassStudent',
                'ImportStudent', 'exportErrorStudent', 'getStuClassCheckApi', 
                'getDurationStuHourstudy'
            ],
            '课时管理' => [
                'coursetermClass', 'classTimetable', 'classTimes', 'classhourtable',
                'classNewTimes', 'getClassTimes', 'getClassHourName', 'getPlanHourList',
                'getLessonPlanList', 'updateHour', 'updateClassHourAction', 'endClass',
                'halfEndClass', 'advanceClass', 'delClassHour', 'updateHourPlan',
                'scheduleDurationClass', 'addDurationHour', 'getHourOneApi', 
                'createHourNumber', 'toHourWay', 'toClassHourWayAction'
            ],
            '考勤管理' => [
                'classRollCall', 'classAttendance', 'classStuBooking', 'addBooking',
                'addStuBooking', 'batchCancelBooking', 'cancelBooking'
            ],
            '排课系统' => [
                'weedDayList', 'newWeedDayList', 'newArrangeDayList', 'getWeekDates',
                'classChoiceTeacher', 'classChoiceClassroom', 'allClassTable',
                'classCourseTable', 'classPercentage'
            ],
            '结算系统' => [
                'classSettlementList', 'durationSettleList', 'getSettlementMonthList',
                'settlement', 'getSettlementInfo', 'submitSettlement', 'checkSettlement'
            ],
            '特殊功能' => [
                'createChildClass', 'editChildClass', 'getChildClassTeacher',
                'getChildClassWayApi', 'getClassChildCLass', 'setFictitious',
                'getClassChildCLassApi', 'submitOpenApply', 'applyFreeHour',
                'getNeedConfirmOpenClassList', 'batchConfirmOpenClass', 'getCoursePresupList',
                'addCoursePresupAction', 'getCoursePresupOneApi', 'editCoursePresupAction',
                'delCoursePresupAction', 'getSourceClassList', 'addSourceClass', 
                'getClassPromotionList', 'getClassUpgradeList', 'ClassFrom', 'ClassGo', 'UpgradeClassInfo'
            ],
            '工具函数' => [
                'IntToChr', 'customsort', 'gettimeKey', 'arraySort'
            ]
        ];
    }
} 