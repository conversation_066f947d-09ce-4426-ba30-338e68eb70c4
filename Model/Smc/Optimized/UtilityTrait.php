<?php

namespace Model\Smc\Optimized;

/**
 * 工具函数 Trait
 * 包含: 数据处理、排序等工具方法
 */
trait UtilityTrait
{
/**
     * IntToChr
     */
    function IntToChr($index, $start = 65)
        {
            $str = '';
            if (floor($index / 26) > 0) {
                $str .= self::IntToChr(floor($index / 26) - 1);
            }
            return $str . chr($index % 26 + $start);
        }

/**
     * customsort
     */
    function customsort($arr, $orderby = 'desc')
        {
            $new_array = array();
            $new_sort = array();
            foreach ($arr as $key => $value) {
                $new_array[] = $value;
            }
            if ($orderby == 'asc') {
                asort($new_array);
            } else {
                arsort($new_array);
            }
            foreach ($new_array as $k => $v) {
                foreach ($arr as $key => $value) {
                    if ($v == $value) {
                        $new_sort[$key] = $value;
                        unset($arr[$key]);
                        break;
                    }
                }
            }
            return $new_sort;
        }

/**
     * gettimeKey
     */
    function gettimeKey($ranks, $rank_point)
        {
    //        $ranks = array(1900=>'32',1800=>'31',1700=>'30',1600=>'29',1500=>'28',1400=>'27',1300=>'26',1400=>'25',1100=>'24',1000=>'23',900 =>'22',800 =>'21',700 =>'20',600 =>'19',500 =>'18',400 =>'17',300 =>'16',200 =>'15',100 =>'14',0=>'13');
    //        $rank_point = -1;
    
            $value = '';
            $k = 0;
            foreach ($ranks as $key => $val) {
                $k++;
                $value = $val;
                if ($rank_point >= $key) {
                    break;
                } else {
                    if ($k == count($ranks)) {
                        return -1;
                    }
                }
            }
            return $value;
        }

/**
     * arraySort
     */
    function arraySort($arr, $keys, $type = 'asc')
        {
            $keysvalue = $new_array = array();
            foreach ($arr as $k => $v) {
                $keysvalue[$k] = $v[$keys];
            }
            $type == 'asc' ? asort($keysvalue) : arsort($keysvalue);
            reset($keysvalue);
            foreach ($keysvalue as $k => $v) {
                $new_array[$k] = $arr[$k];
            }
            return $new_array;
        }

    // 方法将在这里添加
}