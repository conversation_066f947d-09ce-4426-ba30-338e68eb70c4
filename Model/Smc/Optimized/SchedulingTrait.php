<?php

namespace Model\Smc\Optimized;

/**
 * 排课系统功能 Trait
 * 包含: 时间安排、资源分配、课表生成等
 */
trait SchedulingTrait
{
/**
     * weedDayList
     */
    function weedDayList($start = '', $end = '', $weekday, $num = '', $is_frequency = 0, $holidayArray = array(), $iskipweek = 0, $fixednum = 0)
        {
            $begin = empty($start) ? date('Y-m-d') : $start;
            $startTime = strtotime($begin);
            $startDay = date('N', $startTime);
    
            $list = [];
            if ($startDay <= $weekday) {
                $startTime = strtotime(self::$WORK_DAY[$weekday]['en'], strtotime($start));
            } else {
                $startTime = strtotime('next ' . self::$WORK_DAY[$weekday]['en'], strtotime($start));
            }
    
            if ($is_frequency == 1) {
                for ($i = 0; ; $i++) {
    
                    if ($iskipweek == 1 && ($i > $fixednum)) {
                        $addnum = $i * ($iskipweek + 1);
    
                    } else {
                        $addnum = $i;
                    }
    
                    $dayOfWeek = strtotime("+{$addnum} week", $startTime);
                    $dateOne = date('Y-m-d', $dayOfWeek);
                    if (is_array($holidayArray)) {
                        if (in_array($dateOne, $holidayArray)) {
                            continue;
                        }
                    }
                    $list[] = $dateOne;
                    if (count($list) == $num) {
                        break;
                    }
                }
            } else {
                $endTime = strtotime($end);
                for ($i = 0; ; $i++) {
                    if ($iskipweek == 1 && $i > 1) {
                        $addnum = $i * ($iskipweek + 1);
                    } else {
                        $addnum = $i;
                    }
                    $dayOfWeek = strtotime("+{$addnum} week", $startTime);
                    if ($dayOfWeek > $endTime) {
                        break;
                    }
                    if (count($list) >= $num) {
                        break;
                    }
                    $list[] = date('Y-m-d', $dayOfWeek);
                }
            }
            return $list;
        }

/**
     * newWeedDayList
     */
    function newWeedDayList($start = '', $end = '', $num = '', $is_frequency = 0, $weekArray = array(), $holidayArray = array(), $iskipweek = 0, $fixednum = 0)
        {
            $begin = empty($start) ? date('Y-m-d') : $start;
            $startTime = strtotime($begin);
    
            $list = [];
    
            if ($is_frequency == 1) {
                for ($i = 0; ; $i++) {
    
                    if ($iskipweek == 1 && ($i > $fixednum)) {
                        $addnum = $i * ($iskipweek + 1);
                    } else {
                        $addnum = $i;
                    }
    
                    $dayOfWeek = strtotime("+{$addnum} week", $startTime);
    
                    $dateOne = date('Y-m-d', $dayOfWeek);
    
                    $week=date("w",$dateOne)==0?7:date("w",$dateOne);
    
                    if(!in_array($dateOne, $holidayArray) && in_array($week, $weekArray)){
                        $list[] = $dateOne;
                        if (count($list) == $num) {
                            break;
                        }
                    }
                }
            } else {
                $endTime = strtotime($end);
                for ($i = 0; ; $i++) {
                    if ($iskipweek == 1 && $i > 1) {
                        $addnum = $i * ($iskipweek + 1);
                    } else {
                        $addnum = $i;
                    }
                    $dayOfWeek = strtotime("+{$addnum} week", $startTime);
    
                    $dateOne = date('Y-m-d', $dayOfWeek);
    
                    $week=date("w",$dateOne)==0?7:date("w",$dateOne);
    
                    if(!in_array($dateOne, $holidayArray) && in_array($week, $weekArray)){
                        $list[] = $dateOne;
                    }
    
                    if ($dayOfWeek > $endTime) {
                        break;
                    }
                    if (count($list) >= $num) {
                        break;
                    }
                }
            }
            return $list;
        }

/**
     * newArrangeDayList
     */
    function newArrangeDayList($start = '', $end = '', $num = '', $is_frequency = 0, $arrangeList = array(), $holidayArray = array(), $iskipweek = 0, $fixednum = 0,$hour_iswarming=0)
        {
            $begin = empty($start) ? date('Y-m-d') : $start;
            $startTime = strtotime($begin);
    
            $planArray=array();
            foreach($arrangeList as $arrageOne){
                $planArray[$arrageOne['weekday_id']][]=$arrageOne;
            }
    
            if ($is_frequency == 1) {
                for ($i = 0; ; $i++) {
    
                    if ($iskipweek == 1 && ($i > $fixednum)) {
                        $addnum = $i * ($iskipweek + 1);
                    } else {
                        $addnum = $i;
                    }
                    $weekdays=$this->getWeekDates($startTime+$addnum*3600*24*7);
                    if($weekdays){
                        foreach($weekdays as $dateOne){
    
                            $week=date("w",strtotime($dateOne))==0?7:date("w",strtotime($dateOne));
    
                            if(!in_array($dateOne, $holidayArray) && $planArray[$week] && $dateOne>=$start){
                                foreach($planArray[$week] as $planOne){
                                    $data=array();
                                    $data['planStartTime']=$dateOne.' '.$planOne['hour_starttime'];
                                    $data['planEndTime']=$dateOne.' '.$planOne['hour_endtime'];
                                    $data['planOne']=$planOne;
                                    $data['day']=$dateOne;
                                    $data['hour_iswarming']=$hour_iswarming;
                                    $list[] = $data;
    
                                    if (count($list) >= $num) {
                                        return $list;
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                $endTime = strtotime($end);
                for ($i = 0; ; $i++) {
                    if ($iskipweek == 1 && $i > 1) {
                        $addnum = $i * ($iskipweek + 1);
                    } else {
                        $addnum = $i;
                    }
                    $weekdays=$this->getWeekDates($startTime+$addnum*3600*24*7);
    
                    if($weekdays){
                        foreach($weekdays as $dateOne){
    
                            $week=date("w",strtotime($dateOne))==0?7:date("w",strtotime($dateOne));
    
                            if(!in_array($dateOne, $holidayArray) && $planArray[$week] && $dateOne>=$start){
                                foreach($planArray[$week] as $planOne){
                                    $data=array();
                                    $data['planStartTime']=$dateOne.' '.$planOne['hour_starttime'];
                                    $data['planEndTime']=$dateOne.' '.$planOne['hour_endtime'];
                                    $data['planOne']=$planOne;
                                    $data['day']=$dateOne;
                                    $data['hour_iswarming']=$hour_iswarming;
                                    $list[] = $data;
    
                                    if (count($list) >= $num) {
                                        return $list;
                                    }
    
                                    if ($dateOne >= $end) {
                                        return $list;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

/**
     * getWeekDates
     */
    function getWeekDates($date) {
            $weekday = date('w',$date);
    
            // 周一是第一天 获取本周第一天（如果今天是周日，则为下周的周一）
            $firstDayOfWeek = date('Y-m-d', strtotime('-'.(($weekday==0?7:$weekday)-1).' days',$date));
    
            // 生成数组，包含本周所有日期
            $dates = [];
    
            for ($i = 0; $i < 7; $i++) {
                $dates[] = date('Y-m-d', strtotime("+$i days", strtotime($firstDayOfWeek)));
            }
            return $dates;
        }

/**
     * classChoiceTeacher
     */
    function classChoiceTeacher($request)
        {
            $datawhere = " 1 ";
            if (isset($request['class_id']) && $request['class_id'] != '') {
                $datawhere .= " and ch.class_id <> '{$request['class_id']}'";
            }
            $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
            if (isset($request['maxnum']) && $request['maxnum'] != "") {
                $number = $request['maxnum'];
                $total = count($arrangeList);
                $divide_number = bcdiv($number, $total, 0);
                $last_number = bcsub($number, $divide_number * ($total - 1), 0);
                $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
                $numArray = explode("+", $number_str);
            }
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}'");
                $holidayArray = array();
                foreach ($holidayList as $val) {
                    $holidayArray[] = $val['holidays_day'];
                }
            }
            $teacherList = array();
            foreach ($arrangeList as $key => $arrangeOne) {
                if (isset($request['maxnum']) && $request['maxnum'] != "") {
                    $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key]);
                    if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                        $intersectDay = array_intersect($holidayArray, $weekList);
                        $addNum = count($intersectDay) + $numArray[$key];
                        $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $addNum);
                        $weekList = array_diff($weekList, $intersectDay);
                    }
                } else {
                    $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id']);
                }
    
                foreach ($weekList as $day) {
                    $sql = "select ch.hour_starttime,ch.hour_endtime,s.staffer_cnname,s.staffer_branch
                      from smc_class_hour as ch
                      left join smc_class_hour_teaching as ct on ct.hour_id=ch.hour_id and ct.teaching_type=0
                      left join smc_staffer as s on s.staffer_id=ct.staffer_id
                      where {$datawhere} and ct.teaching_type=0 and ch.hour_day='{$day}' and ct.staffer_id='{$arrangeOne['staffer_id']}' limit 0,1
                      ";
                    $arrange = $this->DataControl->selectOne($sql);
                    $is_time_cross = $this->is_time_cross(strtotime($day . " " . $arrange['hour_starttime']), strtotime($day . " " . $arrange['hour_endtime']), strtotime($day . " " . $arrangeOne['hour_starttime']), strtotime($day . " " . $arrangeOne['hour_endtime']));
    
                    if ($is_time_cross) {
                        $teacherList[$key]['staffer_id'] = $arrangeOne['staffer_id'];
                        $teacherList[$key]['staffer_cnname'] = $arrange['staffer_cnname'];
                        $teacherList[$key]['staffer_branch'] = $arrange['staffer_branch'];
                        $teacherList[$key]['state'] = 1;
                        break;
                    } else {
                        $teacherList[$key]['staffer_id'] = $arrangeOne['staffer_id'];
                        $teacherList[$key]['staffer_cnname'] = $arrange['staffer_cnname'];
                        $teacherList[$key]['staffer_branch'] = $arrange['staffer_branch'];
                        $teacherList[$key]['state'] = 0;
                    }
                }
            }
    
            return $teacherList;
        }

/**
     * classChoiceClassroom
     */
    function classChoiceClassroom($request)
        {
            $datawhere = " 1 ";
            if (isset($request['class_id']) && $request['class_id'] != '') {
                $datawhere .= " and ch.class_id <> '{$request['class_id']}'";
            }
            $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
            if (isset($request['maxnum']) && $request['maxnum'] != "") {
                $number = $request['maxnum'];
                $total = count($arrangeList);
                $divide_number = bcdiv($number, $total, 0);
                $last_number = bcsub($number, $divide_number * ($total - 1), 0);
                $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
                $numArray = explode("+", $number_str);
            }
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}'");
                $holidayArray = array();
                foreach ($holidayList as $val) {
                    $holidayArray[] = $val['holidays_day'];
                }
            }
            $classroomArray = array();
            foreach ($arrangeList as $key => $arrangeOne) {
                if (isset($request['maxnum']) && $request['maxnum'] != "") {
                    $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key]);
                    if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                        $intersectDay = array_intersect($holidayArray, $weekList);
                        $addNum = count($intersectDay) + $numArray[$key];
                        $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $addNum);
                        $weekList = array_diff($weekList, $intersectDay);
                    }
                } else {
                    $weekList = $this->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id']);
                }
    
    //
    //            $sql="select c.classroom_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_day from smc_class_hour as ch
    //                  left join smc_classroom as c on c.classroom_id=ch.classroom_id";
    //            $classroomList=$this->DataControl->selectClear($sql);
    
                foreach ($weekList as $day) {
    
                    $sql = "select ch.hour_starttime,ch.hour_endtime,c.classroom_cnname,c.classroom_branch
                      from smc_classroom as c
                      left join smc_class_hour as ch on c.classroom_id=ch.classroom_id
                      where {$datawhere} and ch.hour_day='{$day}' and ch.classroom_id='{$arrangeOne['classroom_id']}' limit 0,1
                      ";
                    $arrange = $this->DataControl->selectOne($sql);
    
                    $is_time_cross = $this->is_time_cross(strtotime($day . " " . $arrange['hour_starttime']), strtotime($day . " " . $arrange['hour_endtime']), strtotime($day . " " . $arrangeOne['hour_starttime']), strtotime($day . " " . $arrangeOne['hour_endtime']));
                    if ($is_time_cross) {
                        $classroomArray[$key]['classroom_id'] = $arrangeOne['classroom_id'];
                        $classroomArray[$key]['classroom_cnname'] = $arrange['classroom_cnname'];
                        $classroomArray[$key]['classroom_branch'] = $arrange['classroom_branch'];
                        $classroomArray[$key]['state'] = 1;
                        break;
                    } else {
                        $classroomArray[$key]['classroom_id'] = $arrangeOne['classroom_id'];
                        $classroomArray[$key]['classroom_cnname'] = $arrange['classroom_cnname'];
                        $classroomArray[$key]['classroom_branch'] = $arrange['classroom_branch'];
                        $classroomArray[$key]['state'] = 0;
                    }
                }
            }
    
            return $classroomArray;
        }

/**
     * allClassTable
     */
    function allClassTable($request)
        {
            $datawhere = " 1 ";
    
            if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
                $datawhere .= " and ch.hour_day = '{$request['fixedtime']}'";
            } else {
                $fixedtime = date("Y-m-d", time());
                $datawhere .= " and ch.hour_day = '{$fixedtime}'";
            }
    
            if (!isset($request['course_inclasstype']) || $request['course_inclasstype'] == '') {
                $this->error = true;
                $this->errortip = "请选择课程类型！";
                return false;
            }
    
            $datawhere .= " and co.course_inclasstype='{$request['course_inclasstype']}'";
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
            }
    
            $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_cnname,hour_noon,ch.hour_ischecking,ch.hour_way,c.class_id,c.class_cnname,co.course_cnname,concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname,cl.classroom_id,ch.hour_ischecking,co.course_inclasstype,co.course_openclasstype
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  left join smc_code_coursetype as ct ON ct.coursetype_id=co.coursetype_id
                  left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
                  left join smc_staffer as s on s.staffer_id=cht.staffer_id
                  where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and c.class_status <> '-2' and ch.hour_ischecking <> '-1'
                  order by ch.hour_starttime DESC
            ";
            $hourList = $this->DataControl->selectClear($sql);
    
    
            $room_sql = "select cl.classroom_id,cl.classroom_branch,cl.classroom_cnname 
                      from smc_classroom as cl
                      left join smc_class_hour as ch on cl.classroom_id=ch.classroom_id
                      left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
                      left join smc_class as c on c.class_id=ch.class_id
                      left join smc_course as co on co.course_id=c.course_id
                      where {$datawhere} and cl.school_id='{$request['school_id']}' and cl.company_id='{$request['company_id']}'
                      group by ch.classroom_id";
            $roomList = $this->DataControl->selectClear($room_sql);
            $data = array();
    
            if (!$roomList) {
                $room_sql = "select c.classroom_id,c.classroom_branch,c.classroom_cnname from smc_classroom as c where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' limit 0,7";
                $roomList = $this->DataControl->selectClear($room_sql);
                if (!$roomList) {
                    $this->error = true;
                    $this->errortip = "无数据！";
                    return false;
                }
            } else {
                foreach ($roomList as $key => $roomOne) {
                    $roomList[$key]['classroom_cnname'] = $this->LgStringSwitch('教室名称:' . $roomOne['classroom_cnname']);
                }
            }
    
            $timeArray = json_decode(stripslashes($request['noon_list']), JSON_UNESCAPED_UNICODE);
    
            foreach ($timeArray as &$one) {
                $one['time'] = str_replace(".", ":", $one['time']) * 100;
            }
    
            $noon = array_column($timeArray, 'time');
            $noon_name = array_column($timeArray, 'name');
            $tem_noon = $this->customsort(array_flip($noon));
    
            if (is_array($timeArray) && count($timeArray) > 0) {
    
                if ($hourList) {
                    foreach ($hourList as $key => $val) {
                        foreach ($timeArray as $noonkey => $noonval) {
                            foreach ($roomList as $room) {
    
                                $hour_starttime = str_replace(".", ":", $val['hour_starttime']) * 100;
    
                                $num = (int)$this->gettimeKey($tem_noon, $hour_starttime);
    
                                if ($num < 0) {
                                    continue;
                                }
    
                                $value = $noon[$num];
    
                                if ($noonval['time'] == $value) {
                                    $data['a' . $noonkey]['noon_name']['noon_name'] = $noon_name[$num];
                                    if ($val['classroom_id'] == $room['classroom_id']) {
                                        $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                    } else {
                                        $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
    
    
                    if (count($data) < count($noon)) {
                        $noon = json_decode(stripslashes($request['noon_list']), JSON_UNESCAPED_UNICODE);
    
                        $tem_noon = array_column($noon, 'name');
    
                        if ($tem_noon) {
                            foreach ($tem_noon as $key => $val) {
                                foreach ($roomList as $room) {
                                    if (!$data['a' . $key]['noon_name']['noon_name']) {
                                        $data['a' . $key]['noon_name']['noon_name'] = $val;
                                        $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
                } else {
                    foreach ($noon as $key => $val) {
                        foreach ($roomList as $room) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val['name'];
                            $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                        }
                    }
                }
                ksort($data);
    
                $data = $this->get_arr($data);
                $tem_data = array_values($data);
    
    
            } else {
                $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
                if ($hourList) {
                    foreach ($hourList as $key => $val) {
                        foreach ($noon as $noonkey => $noonval) {
                            foreach ($roomList as $room) {
                                if ($val['hour_noon'] == $noonkey) {
                                    $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                    if ($val['classroom_id'] == $room['classroom_id']) {
                                        $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                    } else {
                                        $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
    
                    if (count($data) < 3) {
                        $tem_data = array();
                        foreach ($data as $k => $v) {
                            $tem_data[] = $k;
                        }
                        $tem_noon = array_diff($noon, $tem_data);
    
                        if ($tem_noon) {
                            foreach ($tem_noon as $key => $val) {
                                foreach ($roomList as $room) {
                                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                                    $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                } else {
                    foreach ($noon as $key => $val) {
                        foreach ($roomList as $room) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val;
                            $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                        }
                    }
                }
                $data = $this->get_arr($data);
                $data = array_values($data);
    
    
                $tem_data = array();
                if (!$data) {
                    $this->error = true;
                    $this->errortip = "无数据！";
                    return false;
                }
                foreach ($data as $val) {
                    if ($val['noon_name']['noon_name'] == 'morning') {
                        $tem_data[0] = $val;
                        $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                    } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                        $tem_data[1] = $val;
                        $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                    } elseif ($val['noon_name']['noon_name'] == 'night') {
                        $tem_data[2] = $val;
                        $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                    }
                }
                asort($tem_data);
            }
            $rel_data = array();
            $rel_data['roomList'] = $roomList;
            $rel_data['data'] = $tem_data;
            return $rel_data;
        }

/**
     * classCourseTable
     */
    function classCourseTable($request)
        {
            $datawhere = " 1 ";
    
            if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
                $datawhere .= " and ch.hour_day = '{$request['fixedtime']}'";
            } else {
                $fixedtime = date("Y-m-d", time());
                $datawhere .= " and ch.hour_day = '{$fixedtime}'";
            }
    
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
            }
    
            $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,co.course_branch,cl.classroom_branch,cl.classroom_cnname,hour_noon,ch.hour_ischecking,ch.hour_way,c.class_id,c.class_cnname,co.course_cnname,s.staffer_cnname,cl.classroom_id,ch.hour_ischecking
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  left join smc_code_coursetype as ct ON ct.coursetype_id=co.coursetype_id
                  left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
                  left join smc_staffer as s on s.staffer_id=cht.staffer_id
                   where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}'
                   and co.course_inclasstype<>2 and c.class_status <> '-2'  
                   and ch.hour_ischecking <> '-1'  and ct.coursetype_isopenclass =0 and co.course_inclasstype =0
                  order by ch.hour_starttime DESC
            ";
            $hourList = $this->DataControl->selectClear($sql);
    
            $room_sql = "select cl.classroom_id,cl.classroom_branch,cl.classroom_cnname 
                      from smc_classroom as cl
                      left join smc_class_hour as ch on cl.classroom_id=ch.classroom_id
                      left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
                      left join smc_class as c on c.class_id=ch.class_id
                      where {$datawhere} and cl.school_id='{$request['school_id']}' and cl.company_id='{$request['company_id']}'
                      group by ch.classroom_id";
            $roomList = $this->DataControl->selectClear($room_sql);
            $data = array();
    
            if (!$roomList) {
                $room_sql = "select c.classroom_id,c.classroom_branch,c.classroom_cnname from smc_classroom as c where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' limit 0,7";
                $roomList = $this->DataControl->selectClear($room_sql);
                if (!$roomList) {
                    $this->error = true;
                    $this->errortip = "无数据！";
                    return false;
                }
            } else {
                foreach ($roomList as $key => $roomOne) {
                    $roomList[$key]['classroom_cnname'] = $this->LgStringSwitch('教室名称:' . $roomOne['classroom_cnname']);
                }
            }
            $noon = json_decode(stripslashes($request['noon_list']), JSON_UNESCAPED_UNICODE);
    
            if (is_array($noon) && count($noon) > 0) {
    
                if ($hourList) {
                    foreach ($hourList as $key => $val) {
                        foreach ($noon as $noonkey => $noonval) {
                            foreach ($roomList as $room) {
                                if ($val['hour_starttime'] >= $noonval['time']) {
                                    $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval['name'];
                                    if ($val['classroom_id'] == $room['classroom_id']) {
                                        $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                    } else {
                                        $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
    
                    if (count($data) < 3) {
                        $tem_data = array();
                        foreach ($data as $k => $v) {
                            $tem_data[] = $k;
                        }
                        $tem_noon = array_diff(array_column($noon, 'name'), $tem_data);
    
                        if ($tem_noon) {
                            foreach ($tem_noon as $key => $val) {
                                foreach ($roomList as $room) {
                                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                                    $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                } else {
                    foreach ($noon as $key => $val) {
                        foreach ($roomList as $room) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val['name'];
                            $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                        }
                    }
                }
                $data = $this->get_arr($data);
                $tem_data = array_values($data);
    
    
    //            $tem_data = array();
    //            if (!$data) {
    //                $this->error = true;
    //                $this->errortip = "无数据！";
    //                return false;
    //            }
    //            foreach ($data as $val) {
    //                if ($val['noon_name']['noon_name'] == 'morning') {
    //                    $tem_data[0] = $val;
    //                    $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
    //                } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
    //                    $tem_data[1] = $val;
    //                    $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
    //                } elseif ($val['noon_name']['noon_name'] == 'night') {
    //                    $tem_data[2] = $val;
    //                    $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
    //                }
    //            }
    //            asort($tem_data);
    
    
            } else {
                $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");
                if ($hourList) {
                    foreach ($hourList as $key => $val) {
                        foreach ($noon as $noonkey => $noonval) {
                            foreach ($roomList as $room) {
                                if ($val['hour_noon'] == $noonkey) {
                                    $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                                    if ($val['classroom_id'] == $room['classroom_id']) {
                                        $data['a' . $noonkey][$room['classroom_cnname']][] = $val;
                                    } else {
                                        $data['a' . $noonkey][$room['classroom_cnname']]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
    
                    if (count($data) < 3) {
                        $tem_data = array();
                        foreach ($data as $k => $v) {
                            $tem_data[] = $k;
                        }
                        $tem_noon = array_diff($noon, $tem_data);
    
                        if ($tem_noon) {
                            foreach ($tem_noon as $key => $val) {
                                foreach ($roomList as $room) {
                                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                                    $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                                }
                            }
                        }
                    }
                } else {
                    foreach ($noon as $key => $val) {
                        foreach ($roomList as $room) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val;
                            $data['a' . $key][$room['classroom_cnname']]['-1'] = '';
                        }
                    }
                }
                $data = $this->get_arr($data);
                $data = array_values($data);
    
    
                $tem_data = array();
                if (!$data) {
                    $this->error = true;
                    $this->errortip = "无数据！";
                    return false;
                }
                foreach ($data as $val) {
                    if ($val['noon_name']['noon_name'] == 'morning') {
                        $tem_data[0] = $val;
                        $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
                    } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                        $tem_data[1] = $val;
                        $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
                    } elseif ($val['noon_name']['noon_name'] == 'night') {
                        $tem_data[2] = $val;
                        $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
                    }
                }
                asort($tem_data);
            }
            $rel_data = array();
            $rel_data['roomList'] = $roomList;
            $rel_data['data'] = $tem_data;
            return $rel_data;
        }

/**
     * classPercentage
     */
    function classPercentage($request)
        {
    
            $sql = "select c.class_fullnums,
    				  (select count(s.study_id)  from  smc_student_study as s where s.class_id=c.class_id and study_isreading =1 ) as study_in_num,
    				  (select count(ch.hour_id)  from smc_class_hour as ch where ch.class_id =c.class_id and hour_ischecking =1 ) as class_checkinnum,
    				  (select  count(ss.study_id) from smc_student_study as ss  where ss.study_isreading = 0 and ss.class_id =c.class_id ) as  study_out_num,
    				  (select  count(ss.study_id) from smc_student_study as ss  where  ss.class_id =c.class_id ) as  study_allnum
     				  from smc_class as c where  c.class_id='{$request['class_id']}'";
    
            $classOne = $this->DataControl->selectOne($sql);
    
            $sql = "SELECT count(hourstudy_id) as hourstudy_num FROM smc_student_hourstudy AS ss
    				WHERE ss.class_id = '{$request['class_id']}'  group by ss.student_id  HAVING hourstudy_num ='{$classOne['class_checkinnum']}'";
    
            $studentHour = $this->DataControl->selectClear($sql);
            if ($studentHour) {
                $studentNum = count($studentHour);
            } else {
                $studentNum = 0;
            }
    
    
            $data = array();
            if ($classOne) {
                if ($classOne['class_fullnums']) {
                    $data['full_percent'] = (round($classOne['study_in_num'] / $classOne['class_fullnums'], 2) * 100) . '%';
                } else {
                    $data['full_percent'] = '0.00' . '%';
                }
    
                if ($classOne['class_checkinnum'] && $classOne['study_in_num']) {
                    $data['new_percent'] = (round($studentNum / $classOne['study_in_num'], 2) * 100) . '%';
                } else {
                    $data['new_percent'] = '0.00' . '%';
                }
                if ($classOne['study_allnum'] && $classOne['study_in_num']) {
                    $data['out_percent'] = (round($classOne['study_out_num'] / $classOne['study_allnum'], 2) * 100) . '%';
                } else {
                    $data['out_percent'] = '0.00' . '%';
                }
    
                $data['class_fullnums'] = $classOne['class_fullnums'];
                $data['study_in_num'] = $classOne['study_in_num'];
            }
    
            return $data;
        }

    // 方法将在这里添加
}