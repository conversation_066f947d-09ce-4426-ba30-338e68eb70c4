<?php


namespace Model\Smc;

class MergeOrderPayModel extends OrderModel
{
    public $payfeemergeorderOne = false;
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array(),$mergeorder_pid='')
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

        if($mergeorder_pid!=''){
            $this->verdictMergeOrder($mergeorder_pid);
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    function verdictMergeOrder($mergeorder_pid)
    {
        $this->payfeemergeorderOne = $this->DataControl->getOne("smc_payfee_mergeorder", "mergeorder_pid = '{$mergeorder_pid}'");
        if (!$this->payfeemergeorderOne) {
            $this->error = true;
            $this->errortip = "订单信息不存在";
            return false;
        }
    }

    function mergeOrderPay($request){

        if($this->payfeemergeorderOne['mergeorder_status']!=0){
            $this->error = true;
            $this->errortip = "订单不可支付";
            return false;
        }

        $sql="select a.order_id from smc_payfee_order as a,smc_payfee_order_pay as b,smc_code_paytype as c where a.order_pid=b.order_pid and c.paytype_code=b.paytype_code and c.paytype_ischarge=1 and b.pay_issuccess=1 and a.mergeorder_pid='{$request['mergeorder_pid']}' limit 0,1";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "订单支付数据错误";
            return false;
        }

        $sql="select a.mergepay_pid from smc_payfee_mergeorder_mergepay as a where a.mergeorder_pid='{$request['mergeorder_pid']}' and a.mergepay_issuccess=1 limit 0,1";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "订单已完成支付";
            return false;
        }

        $sql="select a.order_id from smc_payfee_order as a,smc_payfee_order_pay as b where a.order_pid=b.order_pid and b.pay_issuccess=0 and b.paytype_code='feewaiver' and a.mergeorder_pid='{$request['mergeorder_pid']}' limit 0,1";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "订单存在减免未审核";
            return false;
        }

        if($this->school_id!='1175' && $this->school_id!='1199'){

            $sql="select a.mergepay_pid from smc_payfee_mergeorder_mergepay as a where a.mergeorder_pid='{$request['mergeorder_pid']}' and a.mergepay_issuccess=0 limit 0,1";

            if($payOne=$this->DataControl->selectOne($sql)){

                $param=array();
                $param['paypid']=$payOne['mergepay_pid'];
                $getBackData = request_by_curl("https://scshopapi.kedingdang.com/MergeBoingPay/mergeOrderStatusQuery", dataEncode($param),"GET");
                $getBackArray=json_decode($getBackData,1);
                if($getBackArray['error']==0){
                    $this->error = true;
                    $this->errortip = "订单已完成支付";
                    return false;
                }else{
                    $data=array();
                    $data['mergepay_pid']=$payOne['mergepay_pid'];
                    $data['payqrcode'] = "https://scshopapi.kedingdang.com/MergeBoingPay/OrderPay?paypid={$payOne['mergepay_pid']}";

                    return $data;
                }


            }else{
                $sql = "select sum(a.order_arrearageprice) as all_price
                    from smc_payfee_order as a 
                    where a.mergeorder_pid='{$request['mergeorder_pid']}'
                    ";

                $mergeOrderOne=$this->DataControl->selectOne($sql);

                do {
                    $paypid = $this->createOrderPid('ZHZF');
                } while ($this->DataControl->selectOne("select mergepay_id from smc_payfee_mergeorder_mergepay where mergepay_pid='{$paypid}' limit 0,1"));

                $data=array();
                $data['mergeorder_pid']=$request['mergeorder_pid'];
                $data['mergepay_pid']=$paypid;
                $data['paychannel_code']='hdcmb';
                $data['mergepay_price']=$mergeOrderOne['all_price'];
                $data['mergepay_createtime']=time();

                if($this->DataControl->insertData("smc_payfee_mergeorder_mergepay",$data)){

                    $data=array();
                    $data['mergepay_pid']=$paypid;
                    $data['payqrcode'] = "https://scshopapi.kedingdang.com/MergeBoingPay/OrderPay?paypid={$paypid}";

                    return $data;

                }else{
                    $this->error = true;
                    $this->errortip = "订单支付创建失败";
                    return false;
                }
            }
        }else{

            // 生成支付链接
            $payUrl = "https://faceentry.kedingdang.com/Pay/ChargeChannelLoading?order_pid=".$request['mergeorder_pid'];
            
            // 生成二维码图片（返回base64编码）
            $qrcodeBase64 = $this->generateQRCode($payUrl, 'base64', 8, 'H');
            
            $data=array();
            $data['mergepay_pid']='';
            $data['payqrcode'] = $qrcodeBase64;

            return $data;

        }

    }

    function successPayOrder($request){

        $sql = "SELECT b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid,a.mergepay_pid
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_issuccess=1 and a.mergepay_pid='{$request['mergepay_pid']}'";

        $orderPay=$this->DataControl->selectOne($sql);

        if(!$orderPay){
            $this->error = true;
            $this->errortip = "支付不存在";
            return false;
        }

        
        $eventArray=$request['eventArray'];

        if($eventArray['payType'] == 'ZF'){
            $paytype_code = 'alipay';
            $mergepay_typename = '支付宝';
        }elseif($eventArray['payType'] == 'WX'){
            $paytype_code = 'wechat';
            $mergepay_typename = '微信';
        }elseif($eventArray['payType'] == 'YL'){
            $paytype_code = 'yinlian';
            $mergepay_typename = '银联';
        }elseif($eventArray['payType'] == 'EC'){
            $paytype_code = 'dcep';
            $mergepay_typename = '数字人民币';
        }else{
            $paytype_code='';
            $mergepay_typename='';
        }

        $successTime=strtotime($eventArray['endDate'].$eventArray['endTime']);

        if($orderPay['mergepay_issuccess']==1){

            $sql = "SELECT a.*
                    ,ifnull((select x.pay_pid from smc_payfee_order_pay as x where x.order_pid=a.order_pid and x.paytype_code in ('alipay','wechat') and x.pay_issuccess=0 limit 0,1),'') as pay_pid
                    ,ifnull((select x.pay_pid from smc_payfee_order_pay as x where x.order_pid=a.order_pid and x.paytype_code in ('alipay','wechat') and x.pay_issuccess=1 limit 0,1),'') as success_pay_pid
                    from smc_payfee_order as a
                    where a.mergeorder_pid='{$orderPay['mergeorder_pid']}' and a.order_arrearageprice>0 and a.order_status>=0 and a.order_status<4
                    group by a.order_pid
                    ";

            $orderList=$this->DataControl->selectClear($sql);

            if($orderList){
                foreach($orderList as $orderOne){

                    $payArray = array();
                    $payArray['order_pid'] = $orderOne['order_pid'];
                    $payArray['paytype'] = $paytype_code;
                    $payArray['paytimes'] = 1;
                    $payArray['pay_price'] = $orderOne['order_arrearageprice'];
                    $payArray['pay_note'] = '组合支付';

                    if($orderOne['pay_pid']=='' && $orderOne['success_pay_pid']!=''){
                        continue;

                    }elseif($orderOne['pay_pid']!='' && $orderOne['success_pay_pid']==''){
                        $paydata=array();
                        $paydata['pay_pid'] = $orderOne['pay_pid'];
                    }else{
                        $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                        $paydata = $Model->createOrderPay($payArray);
                    }

                    if($paydata){

                        $publiclist = array();
                        $publiclist['company_id'] = $orderOne['company_id'];
                        $publiclist['school_id'] = $orderOne['school_id'];
                        $publiclist['staffer_id'] = $orderOne['staffer_id'];
                        $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);

                        $createtime = date("Y-m-d H:i:s",$successTime);

                        $bakjson=addslashes(json_encode($eventArray,JSON_UNESCAPED_UNICODE));

                        if($itemOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepayitem","mergepayitem_pid","mergeorder_pid='{$orderPay['mergeorder_pid']}' and order_pid='{$orderOne['order_pid']}'")){
                            $eventArray['thirdOrderId']=$itemOne['mergepayitem_pid'];
                        }

                        $orderPayModel->orderPaylog( $paydata['pay_pid'],$eventArray['thirdOrderId'],$eventArray['serviceFee'],'',$paytype_code,$createtime,'0',$bakjson,'','hdcmb');
                    }
                }
            }
        }


    }

    function successPayMergeOrder($request){

        $sql = "select b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid,a.mergepay_pid
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_issuccess=0 and a.mergepay_pid='{$request['mergepay_pid']}'";

        $orderPay=$this->DataControl->selectOne($sql);

        if(!$orderPay){
            $this->error = true;
            $this->errortip = "支付不存在";
            return false;
        }

        $eventArray=$request['eventArray'];

        $successTime=strtotime($eventArray['endDate'].$eventArray['endTime']);


        $data=array();
        $data['mergeorder_pid']=$request['mergeorder_pid'];
        $data['mergepay_pid']=$request['mergepay_pid'];
        $data['mergepaylog_actualprice']=$eventArray['txnAmt']/100;
        $data['mergepaylog_paytime']=$successTime;
        $data['mergepaylog_tradeno']=$eventArray['thirdOrderId'];
        $data['mergepaylog_ifee']=$eventArray['serviceFee'];
        $data['mergepaylog_bakjson']=addslashes(json_encode($eventArray,JSON_UNESCAPED_UNICODE));
        $data['mergepaylog_addtime']=time();
        if($this->DataControl->insertData("smc_payfee_mergeorder_mergepaylog",$data)){
            if($eventArray['payType'] == 'ZF'){
                $paytype_code = 'alipay';
                $mergepay_typename = '支付宝';
            }elseif($eventArray['payType'] == 'WX'){
                $paytype_code = 'wechat';
                $mergepay_typename = '微信';
            }elseif($eventArray['payType'] == 'YL'){
                $paytype_code = 'yinlian';
                $mergepay_typename = '银联';
            }elseif($eventArray['payType'] == 'EC'){
                $paytype_code = 'dcep';
                $mergepay_typename = '数字人民币';
            }else{
                $paytype_code='';
                $mergepay_typename='';
            }

            $data=array();
            $data['paytype_code']=$paytype_code;
            $data['mergepay_typename']=$mergepay_typename;
            $data['mergepay_ifee']=$eventArray['serviceFee'];
            $data['mergepay_issuccess']=1;
            $data['mergepay_tradeState']='S';
            $data['mergepay_successtime']=$successTime;

            $this->DataControl->updateData("smc_payfee_mergeorder_mergepay","mergepay_pid='{$orderPay['mergepay_pid']}'",$data);

            $sql = "select a.*
                    from smc_payfee_order as a
                    where a.mergeorder_pid='{$orderPay['mergeorder_pid']}' and a.order_arrearageprice>0
                    group by a.order_pid
                    ";

            $orderList=$this->DataControl->selectClear($sql);

            if($orderList){
                foreach($orderList as $orderOne){

                    $payArray = array();
                    $payArray['order_pid'] = $orderOne['order_pid'];
                    $payArray['paytype'] = $paytype_code;
                    $payArray['paytimes'] = 1;
                    $payArray['pay_price'] = $orderOne['order_arrearageprice'];
                    $payArray['pay_note'] = '组合支付';

                    $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                    $paydata = $Model->createOrderPay($payArray);

                    if($paydata){

                        $publiclist = array();
                        $publiclist['company_id'] = $orderOne['company_id'];
                        $publiclist['school_id'] = $orderOne['school_id'];
                        $publiclist['staffer_id'] = $orderOne['staffer_id'];
                        $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);

                        $createtime = date("Y-m-d H:i:s",$successTime);

                        $bakjson=addslashes(json_encode($eventArray,JSON_UNESCAPED_UNICODE));

                        if($itemOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepayitem","mergepayitem_pid","mergeorder_pid='{$orderPay['mergeorder_pid']}' and order_pid='{$orderOne['order_pid']}'")){
                            $eventArray['thirdOrderId']=$itemOne['mergepayitem_pid'];
                        }

                        $orderPayModel->orderPaylog( $paydata['pay_pid'],$eventArray['thirdOrderId'],$eventArray['serviceFee'],'',$paytype_code,$createtime,'0',$bakjson,'','hdcmb');
                    }
                }
            }

            $data=array();
            $data['mergeorder_status']=1;
            $data['mergeorder_updatatime']=time();
            $this->DataControl->updateData("smc_payfee_mergeorder","mergeorder_pid='{$orderPay['mergeorder_pid']}'",$data);

        }
    }


    function mergeOrderPayStatus($request){

        $payOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepay","mergepay_pid","mergeorder_pid='{$request['mergeorder_pid']}' and (mergepay_issuccess=0 or mergepay_issuccess=1)");


        if(!$payOne){
            $this->error = true;
            $this->errortip = "支付不存在";
            return false;
        }

        $PayModel = new \Model\Api\MergeboingPayModel($this->company_id);
        $bool = $PayModel->mergeOrderStatusQuery($payOne['mergepay_pid']);

        return $bool;

    }

    function mergeOrderPayH5($request){

        if($this->payfeemergeorderOne['mergeorder_status']!=0){
            $this->error = true;
            $this->errortip = "订单不可支付";
            return false;
        }

        $sql="select a.order_id from smc_payfee_order as a,smc_payfee_order_pay as b,smc_code_paytype as c where a.order_pid=b.order_pid and c.paytype_code=b.paytype_code and c.paytype_ischarge=1 and b.pay_issuccess=1 and a.mergeorder_pid='{$request['mergeorder_pid']}' limit 0,1";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "订单支付数据错误";
            return false;
        }

        $sql="select a.mergepay_pid from smc_payfee_mergeorder_mergepay as a where a.mergeorder_pid='{$request['mergeorder_pid']}' and a.mergepay_issuccess=1 limit 0,1";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "订单已完成支付";
            return false;
        }

        $sql="select a.order_id from smc_payfee_order as a,smc_payfee_order_pay as b where a.order_pid=b.order_pid and b.pay_issuccess=0 and b.paytype_code='feewaiver' and a.mergeorder_pid='{$request['mergeorder_pid']}' limit 0,1";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "订单存在减免未审核";
            return false;
        }

        $sql="select a.mergepay_pid from smc_payfee_mergeorder_mergepay as a where a.mergeorder_pid='{$request['mergeorder_pid']}' and a.mergepay_issuccess=0 limit 0,1";

        if($payOne=$this->DataControl->selectOne($sql)){

            $param=array();
            $param['mergepay_pid']=$payOne['mergepay_pid'];
            return $param;

        }else{
            $sql = "select sum(a.order_arrearageprice) as all_price
                from smc_payfee_order as a 
                where a.mergeorder_pid='{$request['mergeorder_pid']}'
                ";

            $mergeOrderOne=$this->DataControl->selectOne($sql);

            do {
                $paypid = $this->createOrderPid('ZHZF');
            } while ($this->DataControl->selectOne("select mergepay_id from smc_payfee_mergeorder_mergepay where mergepay_pid='{$paypid}' limit 0,1"));

            $data=array();
            $data['mergeorder_pid']=$request['mergeorder_pid'];
            $data['mergepay_pid']=$paypid;
            $data['paychannel_code']='hdcmb';
            $data['paytype_code'] = $request['paymenttype'];
            $data['mergepay_price']=$mergeOrderOne['all_price'];
            $data['mergepay_createtime']=time();

            if($this->DataControl->insertData("smc_payfee_mergeorder_mergepay",$data)){

                $data=array();
                $data['mergepay_pid']=$paypid;

                return $data;

            }else{
                $this->error = true;
                $this->errortip = "订单支付创建失败";
                return false;
            }
        }
    }

}