<?php


namespace Model\Smc;

class StatisticsCRMModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = '0';//操作公司
    public $school_id = '0';//操作学校
    public $staffer_id = '0';//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id=$publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }


    /*
     * MySQL 8 性能优化说明:
     * 1. 建议创建以下索引提高查询性能:
     *    CREATE INDEX idx_client_school_create ON crm_client(school_id, client_createtime);
     *    CREATE INDEX idx_principal_school_create ON crm_client_principal(school_id, principal_createtime);
     *    CREATE INDEX idx_track_school_create ON crm_client_track(school_id, track_createtime);
     *    CREATE INDEX idx_invite_school_visit ON crm_client_invite(school_id, invite_visittime);
     *    CREATE INDEX idx_audition_school_visit ON crm_client_audition(school_id, audition_visittime);
     *    CREATE INDEX idx_conversion_school_time ON crm_client_conversionlog(school_id, conversionlog_time);
     * 
     * 2. 使用时间戳范围比较代替FROM_UNIXTIME函数调用
     * 3. 优化JOIN操作，使用INNER JOIN替代LEFT JOIN
     */

    /**
     * 时间的问题
     * author: ling
     * 对应接口文档 0001
     */
    function CountTimeOverCRM($paramArray)
    {
        $datawhere = '1';
        $pr_datawhere = '1';
        $in_datawhere = '1';
        $au_datawhere = '1';
        $track_where = '1';
        $positive_where = '1';
        
        if (isset($paramArray['client_starttime']) && $paramArray['client_starttime'] != '') {
            $startTimeStamp = strtotime($paramArray['client_starttime']);
            $datawhere .= " AND c.client_createtime >= '{$startTimeStamp}'";
            $pr_datawhere .= " AND p.principal_createtime >= '{$startTimeStamp}'";
            $in_datawhere .= " AND invite_visittime >= '{$paramArray['client_starttime']} 00:00:00'";
            $au_datawhere .= " AND audition_visittime >= '{$paramArray['client_starttime']} 00:00:00'";
            $track_where .= " AND t.track_createtime >= '{$startTimeStamp}'";
            $positive_where .= " AND g.positivelog_time >= '{$paramArray['client_starttime']}'";
        }
        if (isset($paramArray['client_endtime']) && $paramArray['client_endtime'] != '') {
            $endTimeStamp = strtotime($paramArray['client_endtime'] . ' 23:59:59');
            $datawhere .= " AND c.client_createtime <= '{$endTimeStamp}'";
            $pr_datawhere .= " AND p.principal_createtime <= '{$endTimeStamp}'";
            $in_datawhere .= " AND invite_visittime <= '{$paramArray['client_endtime']} 23:59:59'";
            $au_datawhere .= " AND audition_visittime <= '{$paramArray['client_endtime']} 23:59:59'";
            $track_where .= " AND t.track_createtime <= '{$endTimeStamp}'";
            $positive_where .= " AND g.positivelog_time <= '{$paramArray['client_endtime']}'";
        }

        // 优化查询 - 使用时间戳范围比较
        $addNewClient = $this->DataControl->selectOne("SELECT COUNT(c.client_id) as client_num FROM crm_client c INNER JOIN crm_client_schoolenter r ON c.client_id = r.client_id WHERE r.school_id = '{$paramArray['school_id']}' AND {$datawhere} AND c.client_distributionstatus = 0 AND c.client_tracestatus = 0");
        $addNewPriClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.client_id) as client_num FROM crm_client_principal p WHERE p.school_id = '{$paramArray['school_id']}' AND {$pr_datawhere}");
        $inv_addNewClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT i.client_id) as client_num FROM crm_client_invite i WHERE i.school_id = '{$paramArray['school_id']}' AND {$in_datawhere} AND invite_isvisit = 1");
        $aud_addNewClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT client_id) as client_num FROM crm_client_audition WHERE school_id = '{$paramArray['school_id']}' AND {$au_datawhere} AND audition_isvisit = 1");
        $loss_addNewClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT t.client_id) as client_num FROM crm_client_track t INNER JOIN crm_client c ON c.client_id = t.client_id WHERE t.track_state = -1 AND t.school_id = '{$paramArray['school_id']}' AND c.client_ischaserlapsed = 1 AND {$track_where}");
        $positive_addNewClient = $this->DataControl->selectOne("SELECT COUNT(client_id) as client_num FROM crm_client_positivelog g WHERE g.school_id = '{$paramArray['school_id']}' AND {$positive_where}");

        $dataArray = array();
        $dataArray["addClient"] = $addNewClient['client_num'] + 0;
        $dataArray["intentionClient"] = $addNewPriClient['client_num'] + 0;
        $dataArray["inviteClient"] = $inv_addNewClient['client_num'] + 0;
        $dataArray["auditionClient"] = $aud_addNewClient['client_num'] + 0;
        $dataArray["loseClient"] = $loss_addNewClient['client_num'] + 0;
        $dataArray["officialClient"] = $positive_addNewClient['client_num'] + 0;

        //以下为漏斗图的数据
        $funnel_addNewPriClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT p.client_id) as client_num FROM crm_client_principal p INNER JOIN crm_client c ON c.client_id = p.client_id WHERE p.school_id = '{$paramArray['school_id']}' AND {$pr_datawhere} AND {$datawhere}");
        $funnel_inv_addNewClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT i.client_id) as client_num FROM crm_client_invite i INNER JOIN crm_client c ON c.client_id = i.client_id WHERE i.school_id = '{$paramArray['school_id']}' AND {$in_datawhere} AND invite_isvisit = 1 AND {$datawhere}");
        $funnel_aud_addNewClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT a.client_id) as client_num FROM crm_client_audition a INNER JOIN crm_client c ON c.client_id = a.client_id WHERE a.school_id = '{$paramArray['school_id']}' AND {$au_datawhere} AND a.audition_isvisit = 1 AND {$datawhere}");
        $funnel_loss_addNewClient = $this->DataControl->selectOne("SELECT COUNT(DISTINCT t.client_id) as client_num FROM crm_client_track t INNER JOIN crm_client c ON c.client_id = t.client_id WHERE t.track_state = -1 AND t.school_id = '{$paramArray['school_id']}' AND c.client_ischaserlapsed = 1 AND {$track_where} AND {$datawhere}");
        $funnel_positive_addNewClient = $this->DataControl->selectOne("SELECT COUNT(g.client_id) as client_num FROM crm_client_positivelog g INNER JOIN crm_client c ON g.client_id = c.client_id WHERE g.school_id = '{$paramArray['school_id']}' AND {$positive_where} AND {$datawhere}");
        
        $dataArray['funnel_addClient'] = $addNewClient['client_num'] + 0;
        $dataArray['funnel_intentionClient'] = $funnel_addNewPriClient['client_num'] + 0;
        $dataArray['funnel_inviteClient'] = $funnel_inv_addNewClient['client_num'] + 0;
        $dataArray['funnel_auditionClient'] = $funnel_aud_addNewClient['client_num'] + 0;
        $dataArray['funnel_loseClient'] = $funnel_loss_addNewClient['client_num'] + 0;
        $dataArray['funnel_officialClient'] = $funnel_positive_addNewClient['client_num'] + 0;
        return $dataArray;
    }


    /**
     * 统计最近10天的招生-折线图
     * author: ling
     * 对应接口文档 0001
     */
    function CountTenDaysCRM($request)
    {
        $datawhere = '1';
        $prin_datawhere = '1';
        $invitewhere = '1';
        $audwhere = '1';
        $positivewhere = '1';
        $trackwhere = '1';
        if (isset($request['school_id']) && $request['school_id']) {
            $datawhere .= " AND cs.school_id = '{$request['school_id']}'";
            $prin_datawhere .= " AND p.school_id = '{$request['school_id']}'";
            $invitewhere .= " AND i.school_id = '{$request['school_id']}'";
            $audwhere .= " AND a.school_id = '{$request['school_id']}'";
            $positivewhere .= " AND p.school_id = '{$request['school_id']}'";
            $trackwhere .= " AND t.school_id = '{$request['school_id']}'";
        }

        $allList = array();
        //获取最近10天
        $time_end = strtotime(date('Ymd', time() + 24 * 60 * 60 - 1));
        $time_start = strtotime(date('Ymd', time() - 10 * 24 * 60 * 60));
        $date_end = date("Y-m-d", $time_end);
        $date_start = date("Y-m-d", $time_start);
        $datawhere .= " AND client_createtime >= '{$time_start}' AND client_createtime <= '{$time_end}' ";
        $prin_datawhere .= " AND principal_createtime >= '{$time_start}' AND principal_createtime <= '{$time_end}' ";
        $trackwhere .= " AND track_createtime >= '{$time_start}' AND track_createtime <= '{$time_end}' ";
        $invitewhere .= " AND invite_visittime >= '{$date_start}' AND invite_visittime <= '{$date_end} 59:59:59' ";
        $audwhere .= " AND audition_visittime >= '{$date_start}' AND audition_visittime <= '{$date_end} 59:59:59'";
        $positivewhere .= " AND positivelog_time >= '{$date_start}' AND positivelog_time <= '{$date_end}'";
        while (date('Ymd', $time_start) < date('Ymd', $time_end)) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        if (!$date) {
            $this->error = 1;
            $this->error = '计算出错';
            return array();
        }
        
        //10天的新增有效名单 - 优化后的SQL
        $sql = "SELECT 
                DATE(FROM_UNIXTIME(client_createtime)) AS current_day,
                COUNT(c.client_id) AS client_num 
                FROM crm_client c 
                INNER JOIN crm_client_schoolenter cs ON cs.client_id = c.client_id
                WHERE {$datawhere} AND c.company_id = '{$this->company_id}'
                GROUP BY DATE(FROM_UNIXTIME(client_createtime))
                ORDER BY current_day ASC LIMIT 0,10";
        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            $arr_client_day = array_column($clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_client_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_client_num[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_client_num[$key] = '0';
            }
        }
        $allList[0]['name'] = $this->LgStringSwitch('新增有效名单');
        $allList[0]['data'] = $arr_client_num;

        // 新增意向客户 - 优化后的SQL
        $intention_sql = "SELECT 
                DATE(FROM_UNIXTIME(principal_createtime)) AS current_day,
                COUNT(p.client_id) AS client_num 
                FROM crm_client_principal p 
                INNER JOIN crm_client c ON c.client_id = p.client_id 
                WHERE {$prin_datawhere} AND c.company_id = '{$this->company_id}'
                GROUP BY DATE(FROM_UNIXTIME(principal_createtime))
                ORDER BY current_day ASC LIMIT 0,10";

        $intent_clientList = $this->DataControl->selectClear($intention_sql);
        if ($intent_clientList) {
            $arr_client_day = array_column($intent_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_intentclient_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_intentclient_num[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_intentclient_num[$key] = '0';
            }
        }

        $allList[1]['name'] = $this->LgStringSwitch('新增意向客户');
        $allList[1]['data'] = $arr_intentclient_num;

        // 新增柜询客户 - 优化后的SQL
        $invite_sql = "SELECT 
                DATE(i.invite_visittime) AS current_day,
                COUNT(DISTINCT i.client_id) AS client_num 
                FROM crm_client_invite i  
                WHERE {$invitewhere} 
                  AND i.company_id = '{$this->company_id}' 
                  AND i.invite_isvisit = 1
                GROUP BY DATE(i.invite_visittime)
                ORDER BY current_day ASC LIMIT 0,10";
        $invite_clientList = $this->DataControl->selectClear($invite_sql);
        if ($invite_clientList) {
            $arr_client_day = array_column($invite_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_inviteclient_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_inviteclient_num[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_inviteclient_num[$key] = '0';
            }
        }
        $allList[2]['name'] = $this->LgStringSwitch('新增柜询客户');
        $allList[2]['data'] = $arr_inviteclient_num;

        // 新增试听客户 - 优化后的SQL
        $audition_sql = "SELECT 
                DATE(a.audition_visittime) AS current_day,
                COUNT(DISTINCT a.client_id) AS client_num 
                FROM crm_client_audition a 
                WHERE {$audwhere}
                  AND a.company_id = '{$this->company_id}'  
                  AND a.audition_isvisit = 1
                GROUP BY DATE(a.audition_visittime)
                ORDER BY current_day ASC LIMIT 0,10";

        $visit_clientList = $this->DataControl->selectClear($audition_sql);
        if ($visit_clientList) {
            $arr_client_day = array_column($visit_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_visit_clientList = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_visit_clientList[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_visit_clientList[$key] = '0';
            }
        }

        $allList[3]['name'] = $this->LgStringSwitch('新增试听客户');
        $allList[3]['data'] = $arr_visit_clientList;

        // 新增无意向客户 - 优化后的SQL
        $lost_sql = "SELECT 
                DATE(FROM_UNIXTIME(track_createtime)) AS current_day,
                COUNT(DISTINCT t.client_id) AS client_num 
                FROM crm_client c 
                INNER JOIN crm_client_track t ON t.client_id = c.client_id
                WHERE {$trackwhere} 
                  AND t.track_state = -1 
                  AND c.company_id = '{$this->company_id}' 
                  AND c.client_ischaserlapsed = 1
                GROUP BY DATE(FROM_UNIXTIME(track_createtime))
                ORDER BY current_day ASC LIMIT 0,10";

        $lost_clientList = $this->DataControl->selectClear($lost_sql);
        if ($lost_clientList) {
            $arr_client_day = array_column($lost_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_lost_clientList = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_lost_clientList[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_lost_clientList[$key] = '0';
            }
        }
        $allList[4]['name'] = $this->LgStringSwitch('新增无意向客户');
        $allList[4]['data'] = $arr_lost_clientList;

        // 新增转正人数 - 优化后的SQL
        $postive_sql = "SELECT 
                positivelog_time AS current_day,
                COUNT(DISTINCT p.client_id) AS client_num 
                FROM crm_client c 
                INNER JOIN crm_client_positivelog p ON p.client_id = c.client_id
                WHERE {$positivewhere} AND c.company_id = '{$this->company_id}'
                GROUP BY current_day
                ORDER BY current_day ASC LIMIT 0,10";

        $postive_clientList = $this->DataControl->selectClear($postive_sql);
        if ($postive_clientList) {
            $arr_client_day = array_column($postive_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_postivet_clientList = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_postivet_clientList[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_postivet_clientList[$key] = '0';
            }
        }
        $allList[5]['name'] = $this->LgStringSwitch('新增转正客户');
        $allList[5]['data'] = $arr_postivet_clientList;
        $data = array();
        $data['allList'] = $allList;
        $data['legendData'] = $this->LgArraySwitch(array('新增有效名单', '新增意向客户', '新增柜询客户', '新增试听客户', '新增无意向客户', '新增转正客户'));
        $data['xAxisData'] = $date;
        return $data;
    }

    /**
     * 活动转化排行
     * author: ling
     * 对应接口文档 0001
     */
    function CountActivity($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ca.activity_name like '%{$request['keyword']}%'  ";
        }
        if (isset($request['activity_type']) && $request['activity_type'] !== '') {
            $datawhere .= " and ca.activity_type = '{$request['activity_type']}'  ";
        }

        if (isset($request['order_by']) && $request['order_by'] !== '') {
            if ($request['order_by'] == 0) {
                $order = "ASC";
            } else {
                $order = "DESC";
            }
        } else {
            $order = "DESC";
        }
        $month_first = date('Y-m-01', time());
        $month_end = date('Y-m-t', time());
//        $month_firsttime = strtotime($month_first);
//        $month_endtime = strtotime($month_end);
        $positivewhere = " clg.positivelog_time>='{$month_first}' and  clg.positivelog_time<='{$month_end}'  ";


        $sql = "
           select ca.activity_name,
           (select count(ct.client_id) from crm_client as ct,crm_client_schoolenter as csl where  ct.client_id=csl.client_id and csl.school_id = '{$this->school_id}' and ct.activity_id = ca.activity_id ) as client_all_num,
            (select count(ct.client_id) from crm_client as ct,crm_client_positivelog as clg  where  ct.client_id=clg.client_id and clg.school_id = '{$this->school_id}' and ct.activity_id = ca.activity_id  and {$positivewhere}) as client_postive_num
           from crm_sell_activity as ca
           where ca.activity_id in (select DISTINCT c.activity_id from crm_client as c, crm_client_schoolenter as cs where c.client_id = cs.client_id and cs.school_id ='{$this->school_id}') and {$datawhere} 
           order  by  client_postive_num/client_all_num  {$order}   
           limit 0,5
        ";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => $dataOne) {
                $dataList[$key]['number'] = $key + 1;
                $dataList[$key]['rate'] = $dataOne['client_all_num'] > 0 ? round($dataOne['client_postive_num'] / $dataOne['client_all_num'], 4) * 100 . '%' : '0.00%';
            }
            return $dataList;
        }
    }

    /**
     * 转正业绩排行
     * author: ling
     * 对应接口文档 0001
     */
    function CountStafferPostive($request)
    {
        $datawhere = 'm.marketer_status =1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and m.marketer_name like '%{$request['keyword']}%'  ";
        }
        if (isset($request['order_by']) && $request['order_by'] !== '') {
            if ($request['order_by'] == 1) {
                $order = "ASC";
            } else {
                $order = "DESC";
            }
        } else {
            $order = "DESC";
        }
        $month_first = date('Y-m-01', time());
        $month_end = date('Y-m-t', time());
        $month_firsttime = strtotime($month_first);
        $month_endtime = strtotime($month_end);
        $positivewhere = "  clg.conversionlog_time>='{$month_firsttime}' and  clg.conversionlog_time<='{$month_endtime}'  ";
//        $sql = "
//            select m.marketer_name,count(DISTINCT cg.client_id) as client_postive_num,
//            (select count(DISTINCT cp.client_id) from  crm_client_principal as cp where cp.school_id = cg.school_id and cg.marketer_id =cp.marketer_id and principal_leave =0 ) as client_all_num,
//            from crm_marketer as m
//            left join crm_client_conversionlog as cg On cg.marketer_id = m.marketer_id
//            left  join crm_client_positivelog as clg ON clg.client_id = cg.client_id and clg.school_id = cg.school_id
//            where  cg.school_id='{$this->school_id}' and {$datawhere}
//            group by cg.marketer_id,cg.school_id
//            order by (count(DISTINCT cg.client_id)/client_all_num) {$order}
//        ";
        $sql = "
            select m.marketer_name,(select s.staffer_enname from smc_staffer as s where m.staffer_id = s.staffer_id) as staffer_enname,
               count(DISTINCT p.client_id) as client_all_num,
              (select count(DISTINCT client_id) from crm_client_conversionlog as clg where clg.marketer_id= m.marketer_id and clg.school_id=p.school_id and {$positivewhere}) as client_postive_num
            from crm_marketer as m
            left join crm_client_principal as p On m.marketer_id = p.marketer_id
            where p.school_id='{$this->school_id}' and p.principal_leave =0 and {$datawhere}
            group by  m.marketer_id,p.school_id
            order by (client_postive_num/count(DISTINCT p.client_id)) {$order}
        ";
        $dataList = $this->DataControl->selectClear($sql);

        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as $key => $dataOne) {
                $dataList[$key]['marketer_name'] = $dataOne['marketer_name'].((isset($dataOne['staffer_enname']) && $dataOne['staffer_enname'] != '')?'-'.$dataOne['staffer_enname']:'');
                $dataList[$key]['number'] = $key + 1;
                $dataList[$key]['rate'] = $dataOne['client_all_num'] > 0 ? round($dataOne['client_postive_num'] / $dataOne['client_all_num'], 4) * 100 . '%' : '0.00%';
            }
        }
        return $dataList;
    }

    /**
     * 转正来源渠道类型分析
     * author: ling
     * 对应接口文档 0001
     */
    function CountChannel($request)
    {
        if (isset($request['order_by']) && $request['order_by'] !== '') {
            if ($request['order_by'] == 0) {
                $order = "ASC";
            } else {
                $order = "DESC";
            }
        } else {
            $order = "DESC";
        }
        $sql = "SELECT 
                    DISTINCT c.frommedia_name,
                    count( DISTINCT b.client_id ) AS client_all_num,
                    count( IF ( b.client_tracestatus = 4, TRUE, NULL ) ) AS client_postive_num 
                FROM
                    crm_client_schoolenter a
                    LEFT JOIN crm_client b ON a.client_id = b.client_id
                    LEFT JOIN crm_code_frommedia c ON c.frommedia_name = b.client_source 
                WHERE
                    a.company_id = '{$this->company_id}' 
                    AND a.school_id = '{$this->school_id}' 
                    AND c.frommedia_id IS NOT NULL 
                GROUP BY
                    c.frommedia_id 
                ORDER BY
                    count( IF ( b.client_tracestatus = 4, TRUE, NULL ) ) / count( DISTINCT b.client_id ) {$order}
               ";

        $dataList = $this->DataControl->selectClear($sql);
        $y_data = array();
        if (!$dataList) {
            $channel_name = array();
            $rate = array();
        } else {
            foreach ($dataList as &$dataOne) {
                $dataOne['rate'] = $dataOne['client_all_num'] > 0 ? round($dataOne['client_postive_num'] / $dataOne['client_all_num'], 4) * 100 : '0';
            }

            $channel_name = array_column($dataList, "frommedia_name");
            $rate = array_column($dataList, "rate");
        }

        $y_data[0]['name'] = $this->LgStringSwitch("渠道转正统计");
        $y_data[0]['data'] = $rate;
        $data = array();
        $data['x_data'] = $channel_name;
        $data['y_data'] = $y_data;

        return $data;
    }

    /**
     *年龄转化率排行
     * author: ling
     * 对应接口文档 0001
     * @return array
     */
    function CountAgePostive($request)
    {


        $moth_startday = date("Y-m-01");
        $moth_endday = date("Y-m-t");
        $datawhere = '1';
        $datawhere .= " AND cs.school_id='{$this->school_id}' AND c.company_id='{$this->company_id}'";
        $datawhere .= " AND pg.positivelog_time>='{$moth_startday}' AND pg.positivelog_time<='{$moth_endday}' ";

        if (isset($request['order_by']) && $request['order_by'] !== '') {
            if ($request['order_by'] == 0) {
                $order = "ASC";
            } else {
                $order = "DESC";
            }
        } else {
            $order = "DESC";
        }

        $age_sql = "select 
                q.school_id,q.client_newage,
                count(1) as client_all_num,
                sum( CASE WHEN q.new_client_sex = '女' and  positivelog_id > 0 THEN 1 ELSE 0 END ) AS female_postivenum,
	            sum( CASE WHEN q.new_client_sex = '男' and  positivelog_id > 0 THEN 1 ELSE 0 END ) AS male_postivenum ,
	             sum( CASE WHEN positivelog_id > 0 THEN 1 ELSE 0 END ) AS all_male_postivenum 
            from 
            (
            SELECT
                cs.school_id, pg.positivelog_id,
                ( CASE WHEN c.client_sex = '女' THEN '女' ELSE '男' END ) AS new_client_sex,
                    (
            CASE
                WHEN IFNULL( TIMESTAMPDIFF( YEAR, c.client_birthday, CURDATE( ) ), 0 ) < 0 THEN
                0 ELSE IFNULL( TIMESTAMPDIFF( YEAR, c.client_birthday, CURDATE( ) ), 0 ) 
            END 
                ) AS client_newage 
            FROM
                crm_client AS c
                LEFT JOIN crm_client_schoolenter AS cs ON cs.client_id = c.client_id
                LEFT JOIN crm_client_positivelog AS pg ON pg.client_id = c.client_id 
                AND cs.school_id = pg.school_id 
            WHERE
                {$datawhere}
            ) as q
            GROUP BY
                q.client_newage,
                q.school_id
            order by  sum( CASE WHEN positivelog_id > 0 THEN 1 ELSE 0 END )/ count(1)  {$order}
                        ";

        $dataList = $this->DataControl->selectClear($age_sql);

        if (!$dataList) {
            $newage_name =$this->LgArraySwitch(array(0=>'5岁',1=>'6岁'));
            $rate = array();
        } else {
            foreach ($dataList as &$dataOne) {
                $dataOne['male_rate'] = $dataOne['client_all_num'] > 0 ? round($dataOne['male_postivenum'] / $dataOne['client_all_num'], 4) * 100 : '0';
                $dataOne['female_rate'] = $dataOne['client_all_num'] > 0 ? round($dataOne['female_postivenum'] / $dataOne['client_all_num'], 4) * 100 : '0';
            }

            $newage_name = array_unique(array_column($dataList, "client_newage"));
            if ($newage_name) {
                for ($i = 0; $i < count($newage_name); $i++) {
                    $newage_name[$i] = $this->LgStringSwitch($newage_name[$i] . '岁');
                }
            }

            $male_rate = array_column($dataList, "male_rate");
            $female_rate = array_column($dataList, "female_rate");
            $rate[0]['name'] = $this->LgStringSwitch('男');
            $rate[0]['data'] = $male_rate;
            $rate[1]['name'] = $this->LgStringSwitch('女');
            $rate[1]['data'] = $female_rate;
        }

        $data = array();
        $data['x_data'] = $newage_name;
        $data['y_data'] = $rate;
        $data['legendData'] = $this->LgArraySwitch(array('男', '女'));
        return $data;
    }

}
