<?php


namespace Model\Smc;

class HomeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作校区
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->verdictCompany($publicarray['company_id']);
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->verdictSchool($publicarray['school_id']);
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证校园信息
    function verdictSchool($school_id)
    {
        $this->schoolOne = $this->DataControl->getFieldOne("smc_school",
            "school_id,company_id,school_branch,school_shortname,school_cnname,school_openclass,school_isclose,school_istemporaryclose,school_periodauthority,school_temporaryclosetip,school_coursetime,school_appointment,school_duration,school_openclass"
            , "school_id = '{$school_id}'");
        if (!$this->schoolOne) {
            $this->error = true;
            $this->errortip = "校园信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function warning($request)
    {
        $sql = "SELECT COUNT(*) as count FROM (SELECT 1
            FROM smc_student_coursebalance A
            LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id
            LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id
            LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id
            LEFT JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
            WHERE A.school_id='{$this->schoolOne['school_id']}'
            AND A.company_id='{$this->companyOne['company_id']}'
            AND B.course_inclasstype in (0,1)
            AND A.coursebalance_time>0
            -- AND A.coursebalance_status=0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id
            AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1
            AND X.study_endday>=CURDATE())
            GROUP BY E.school_id,C.student_id,D.coursecat_id) as subquery";

        $substitution = $this->DataControl->selectOne($sql);
        $substitutionNum = $substitution['count'] ? $substitution['count'] : 0;

        $sql = "select COUNT(*) as count from (select po.order_id
				  from smc_payfee_order as po
                  where po.company_id='{$this->companyOne['company_id']}' and po.school_id='{$this->schoolOne['school_id']}' and (po.order_status between 1 and 3)
                  GROUP BY po.order_pid) as subquery";
        $arrears = $this->DataControl->selectOne($sql);
        $arrearsNum = $arrears['count'] ? $arrears['count'] : 0;

        $sql = "select count(sc.student_id) as count
            from smc_student_coursebalance as sc
            LEFT JOIN smc_student_study AS ss ON ss.student_id = sc.student_id
            LEFT JOIN smc_class AS cs ON cs.class_id = ss.class_id and cs.course_id=sc.course_id and cs.school_id=sc.school_id
            LEFT JOIN smc_course AS co ON co.course_id = cs.course_id
            where cs.company_id ='{$this->companyOne['company_id']}' and cs.school_id ='{$this->schoolOne['school_id']}' and ss.study_isreading = 1 and co.course_warningnums > 0  and  co.course_inclasstype = 0 and sc.coursebalance_time <=co.course_warningnums
              ";

        $courseWarning = $this->DataControl->selectOne($sql);
        $courseWarningNum = $courseWarning['count'] ? $courseWarning['count'] : 0;

        //升班预警
        $sql = "select count(*) as count
              from smc_class as c
              left join smc_course as ce ON ce.course_id = c.course_id
              where c.company_id ='{$this->companyOne['company_id']}' and c.school_id ='{$this->schoolOne['school_id']}' and c.class_status=1  and ce.course_nextid > 0 and ce.course_inclasstype =0
              and EXISTS(select 1 from smc_class_hour as ch WHERE ch.class_id = c.class_id)
              and (select count(ch.hour_id) from smc_class_hour as ch WHERE ch.course_id = c.course_id and ch.class_id = c.class_id and ch.hour_ischecking = 0) <= ce.course_warningnums
              ";
        $classWarning = $this->DataControl->selectOne($sql);
        $classWarningNum = $classWarning['count'] ? $classWarning['count'] : 0;


        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_coursewarning,school_classtimeswarning", "school_id='{$this->schoolOne['school_id']}'");

        $datawhere = ' 1 ';
        $datawhere .= " and( ";
        $before = false;
//        1.按照集团设置的课程预警次数预警
        if ($schoolOne && $schoolOne['school_coursewarning'] == 1) {
            $datawhere .= " scb.coursebalance_time<=(case when co.course_warningnums>0 then co.course_warningnums else -1 end) ";
            $before = true;
        }

//        2.学员剩余课次≤设置课次
        $arrearsalertOne = $this->DataControl->getFieldOne("smc_school_arrears_alert", "alert_time,alert_status", "school_id='{$this->schoolOne['school_id']}'");
        if ($arrearsalertOne && $arrearsalertOne['alert_status'] == 1) {
            if ($before) {
                $datawhere .= " or scb.coursebalance_time<='{$arrearsalertOne['alert_time']}' ";
            } else {
                $datawhere .= " scb.coursebalance_time<='{$arrearsalertOne['alert_time']}' ";
                $before = true;
            }
        }

//        3.学员剩余课次小于班级剩余课次
        if ($schoolOne && $schoolOne['school_classtimeswarning'] == 1) {
            if ($before) {
                $datawhere .= " or (scb.coursebalance_time<(select count(ch.hour_id) from smc_class_hour as ch 
                where ch.class_id = ss.class_id AND ch.hour_isfree = '0' and ch.hour_ischecking='0' and ch.hour_iswarming='0') and co.course_sellclass=0) ";
            } else {
                $datawhere .= " (scb.coursebalance_time<(select count(ch.hour_id) from smc_class_hour as ch 
                where ch.class_id = ss.class_id AND ch.hour_isfree = '0' and ch.hour_ischecking='0' and ch.hour_iswarming='0') and co.course_sellclass=0) ";
                $before = false;
            }
        }
        $datawhere .= " ) ";

        $sql = "select count(ss.study_id) as count
				FROM  smc_student_study  as ss
	 			inner JOIN smc_class AS c  ON c.class_id = ss.class_id
				inner JOIN smc_student AS s ON s.student_id = ss.student_id
				inner JOIN smc_course as co on co.course_id=c.course_id
				left join smc_student_coursebalance as scb on scb.student_id=ss.student_id and scb.school_id=ss.school_id and scb.course_id=c.course_id
				where {$datawhere} 
				and ss.school_id = '{$request['school_id']}' 
				and ss.company_id = '{$request['company_id']}' 
				and co.course_inclasstype IN (0,2)
				and c.class_type=0
				and ss.study_isreading=1 
				and c.class_status=1
				";

        $wastsWarning = $this->DataControl->selectOne($sql);
        $wastsWarningNum = $wastsWarning['count'] ? $wastsWarning['count'] : 0;

        $sql = "select count(*) as count from (select * from (
					select a.school_id
					,a.student_id
					,d.coursetype_id
					,FROM_UNIXTIME(max(a.coursebalance_updatatime),'%Y-%m-%d') as last_log_time
					from smc_student_coursebalance a
					left join smc_student_enrolled b on a.student_id=b.student_id and a.school_id=b.school_id
					left join smc_course c on a.course_id=c.course_id
					left join smc_code_coursetype d on c.coursetype_id=d.coursetype_id
					left join smc_student e on a.student_id=e.student_id
					where a.company_id ='{$request['company_id']}' and a.school_id ='{$request['school_id']}'
					and b.enrolled_status in(0,1)
					and d.coursetype_isregistercalc=1
					group by a.student_id,d.coursetype_id
				)ta 
				where 1
				and not exists(select 1 from smc_student_changelog x where x.student_id=ta.student_id and x.school_id=ta.school_id 
				and ((x.stuchange_code='C04' and coursetype_id=ta.coursetype_id) or x.stuchange_code in('C02','B05'))
				and changelog_day>=last_log_time )
				and not exists(select 1 from smc_student_coursebalance x,smc_course y where x.course_id=y.course_id 
				and y.coursetype_id=ta.coursetype_id and x.school_id=ta.school_id and x.student_id=ta.student_id and x.coursebalance_time>y.course_warningnums)
				) as subquery";
        $lostWarning = $this->DataControl->selectOne($sql);
        $lostWarningNum = $lostWarning['count'] ? $lostWarning['count'] : 0;

        if ($request['re_postbe_id'] == '0') {
            $sql = "select count(m.module_id) as count
                from imc_module as m 
                where m.product_id=2
                AND (m.MODULE_NAME='学员自动流失预警' or m.module_markurl='/ClassManagement/studentManagement/lossWarning')
                and ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) = 1";
        } else {
            $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'postrole_id,postpart_id', "school_id='{$this->schoolOne['school_id']}' and postbe_id = '{$request['re_postbe_id']}'");

            $sql = "select count(m.module_id) as count
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                where m.product_id=2 and u.postpart_id = '{$postbeOne['postpart_id']}'
                AND (m.MODULE_NAME='学员自动流失预警' or m.module_markurl='/ClassManagement/studentManagement/lossWarning')
                and ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) = 1";
        }
        $db_nums = $this->DataControl->selectOne($sql);
        $lostStatus = ($db_nums && $db_nums['count'] > 0) ? 1 : 0;

        $data = array();
        $data['substitution'] = $substitutionNum;
        $data['arrears'] = $arrearsNum;
        $data['courseWarning'] = $courseWarningNum;
        $data['classWarning'] = $classWarningNum;
        $data['wastsWarning'] = $wastsWarningNum;
        $data['lostWarning'] = $lostWarningNum;

        $pizza = implode(' ', $data);
        $pieces = explode(" ", $pizza);
        $son = array();

        foreach ($pieces as $key => $value) {
            $son[$key]['id'] = $key + 1;
            $son[$key]['number'] = $value;
            $son[$key]['status'] = '1';
            if ($key == '4' && $this->schoolOne['school_coursetime'] == '0') {
                $son[$key]['status'] = '0';
            }
            if ($key == '5' && $lostStatus == 0) {
                $son[$key]['status'] = '0';
            }
        }

        return $son;
    }

    function toDoInfo($request)
    {
        $sql = "SELECT COUNT(a.apply_id) as a FROM smc_student_coupons_apply AS a LEFT JOIN smc_school AS s ON s.school_id = a.school_id LEFT JOIN smc_student AS d ON d.student_id = a.student_id LEFT JOIN smc_code_couponsapplytype AS c ON c.applytype_branch = a.applytype_branch and c.company_id = a.company_id left join smc_student_coupons as co on co.apply_id = a.apply_id WHERE 1 and a.apply_status ='0' and a.company_id = '{$request['company_id']}' and a.school_id='{$request['school_id']}' and c.applytype_port='0' and c.applytype_isschool ='1'";

        $coupons = $this->DataControl->selectClear($sql);
        $couponsNum = $coupons[0]['a'];

        $sql = "select b.breakoff_id from smc_class_breakoff as b left join smc_class as c on c.class_id=b.class_id left join smc_staffer as st on st.staffer_id=b.breakoff_apply_staffer_id left join smc_course as co on co.course_id=c.course_id where 1 and b.school_id = '{$this->schoolOne['school_id']}' and b.breakoff_status = '0' and b.company_id='{$this->companyOne['company_id']}'";

        $db_nums = $this->DataControl->selectClear($sql);
        $dismantleNum = $db_nums ? count($db_nums) : 0;


        $sql = "select cs.share_id from smc_student_class_share as cs left join smc_student as st on st.student_id=cs.student_id left join smc_class as cl on cl.class_id=cs.class_id where 1 and cs.share_status = '0' and cs.school_id = '{$this->schoolOne['school_id']}' and cs.company_id='{$this->companyOne['company_id']}'";

        $db_nums = $this->DataControl->selectClear($sql);
        $monthNum = $db_nums ? count($db_nums) : 0;

        $sql = "select po.refund_id from smc_refund_order as po where po.school_id='{$this->schoolOne['school_id']}' and po.refund_status='0' ";

        $db_nums = $this->DataControl->selectClear($sql);
        $refundNum = $db_nums ? count($db_nums) : 0;

        if ($request['re_postbe_id'] == '0') {
            $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_icon as icon,m.father_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m 
                where m.product_id=2
                AND (m.MODULE_NAME='缴费中心' or m.module_markurl='/PaymentManagement/paymentOrder')
                having status=1
                order by m.module_weight asc,m.module_id asc";
        } else {
            $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'postrole_id,postpart_id', "school_id='{$this->schoolOne['school_id']}' and postbe_id = '{$request['re_postbe_id']}'");

            $sql = "select m.module_id as id,m.module_name as title,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id
                        ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                where m.product_id=2 and u.postpart_id = '{$postbeOne['postpart_id']}'
                AND (m.MODULE_NAME='缴费中心' or m.module_markurl='/PaymentManagement/paymentOrder')
                having status=1
                order by m.module_weight asc,m.module_id asc";
        }

        $db_nums = $this->DataControl->selectClear($sql);
        $refundStatus = $db_nums ? count($db_nums) : 0;


        $sql = "select cs.erpgoods_id from smc_student_erpgoods as cs where 1 and cs.erpgoods_isreceive = '0' and cs.school_id = '{$this->schoolOne['school_id']}'";

        $db_nums = $this->DataControl->selectClear($sql);
        $restoreNum = $db_nums ? count($db_nums) : 0;


        $sql = "select ht.teaching_id
                from smc_class_hour_teaching as ht
                left join smc_staffer as s on s.staffer_id=ht.staffer_id
                left join smc_class as c on c.class_id=ht.class_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_hour as ch on ch.hour_id=ht.hour_id
                where c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and s.staffer_leave = '1' and ch.hour_ischecking = '0'";

        $db_nums = $this->DataControl->selectClear($sql);
        $leaveNum = $db_nums ? count($db_nums) : 0;


//        if($this->schoolOne['school_periodauthority'] == '0'){
//            $pizza = $couponsNum . ' ' . $dismantleNum . ' ' . $protocolNum;
//        }else{
//            $pizza = $couponsNum . ' ' . $dismantleNum . ' ' . $monthNum . ' ' . $protocolNum;
//        }

        $pizza = $couponsNum . ' ' . $dismantleNum . ' ' . $monthNum . ' ' . $refundNum . ' ' . $restoreNum . ' ' . $leaveNum;

        $pieces = explode(" ", $pizza);
        $son = array();

        foreach ($pieces as $key => $value) {
            $son[$key]['id'] = $key + 1;
            $son[$key]['number'] = $value;
            if ($this->schoolOne['school_periodauthority'] == '0' && $key == '2') {
                $son[$key]['status'] = '0';
                $son[$key]['number'] = 0;//230808 不显示的 统计数字也不显示
            } else {
                $son[$key]['status'] = '1';
            }

            if ($refundStatus == 0 && $key == '3') {
                $son[$key]['status'] = '0';
                $son[$key]['number'] = 0;//230808 不显示的 统计数字也不显示
            } else {
                $son[$key]['status'] = '1';
            }
        }

        return $son;
    }


    function statisticsInfo($request)
    {
        if ($request['starttime'] && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-d");
        }

        if ($request['endtime'] && $request['endtime'] != '') {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");;
        }

        $datawhere = " 1 ";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '' && $request['coursetype_id'] > 0) {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
        }

//        if ($request['staffer_id'] == '25721') {
//            $datawhere .= " and C.coursetype_id='79654'";
//        }

        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . ' 23:59:59');


        if ($request['is_all'] != '1') {
            $sql = "SELECT
                        F.school_id,
                        E.coursetype_id,
                        B.student_id,
                        MIN( B.order_createtime ) AS order_createtime,
                        (SELECT MIN(TB.order_createtime) FROM smc_payfee_order_course AS TA
                        LEFT JOIN smc_payfee_order AS TB ON TA.order_pid = TB.order_pid
                        LEFT JOIN smc_course AS TC ON TA.course_id = TC.course_id
                        WHERE 1 AND TB.order_status>0 AND TB.order_paidprice>0 AND TB.student_id = B.student_id AND TC.coursetype_id = E.coursetype_id
                        AND TC.company_id = B.company_id) AS order_createtime_all
                    FROM smc_payfee_order_course AS A
                    LEFT JOIN smc_payfee_order AS B ON A.order_pid = B.order_pid
                    LEFT JOIN smc_course AS C ON A.course_id = C.course_id AND C.company_id = B.company_id
                    LEFT JOIN smc_code_coursecat D ON C.coursecat_id = D.coursecat_id AND D.company_id = C.company_id
                    LEFT JOIN smc_code_coursetype E ON C.coursetype_id = E.coursetype_id AND E.company_id = C.company_id
                    LEFT JOIN smc_school F ON B.school_id=F.school_id AND F.company_id = B.company_id
                    LEFT JOIN smc_student G ON G.student_id = B.student_id AND G.company_id = B.company_id
                    WHERE {$datawhere} and B.company_id='{$request['company_id']}' AND B.school_id='{$request['school_id']}'  AND B.order_status>0
                    AND B.order_paidprice>0
                    AND NOT EXISTS(SELECT 1 FROM smc_student_coursebalance_log X,smc_course Y WHERE X.course_id=Y.course_id and Y.company_id=b.company_id
                    AND X.school_id=B.school_id AND X.student_id=B.student_id AND Y.coursetype_id=E.coursetype_id AND log_playname LIKE '2.0%')

                  ";
            $sql .= " and B.order_createtime >= '{$starttime}' and B.order_createtime <= '{$endtime}'";

            $sql .= " GROUP BY E.coursetype_id, B.student_id
                    HAVING order_createtime=order_createtime_all";
            $newStudentList = $this->DataControl->selectClear($sql);//新生数量
            if ($newStudentList) {
                $data['newStudent'] = count($newStudentList);
            } else {
                $data['newStudent'] = 0;
            }
        }

        $sql = "SELECT E.school_id
            ,D.coursecat_id
            ,A.student_id
            FROM smc_student_coursebalance A
            LEFT JOIN smc_course C ON A.course_id=C.course_id AND A.company_id=C.company_id
            LEFT JOIN smc_code_coursecat D ON C.coursecat_id=D.coursecat_id AND A.company_id=D.company_id
            LEFT JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
            WHERE A.school_id='{$request['school_id']}'
            AND A.company_id='{$this->companyOne['company_id']}'
            AND C.course_inclasstype in (0,1)
            AND A.coursebalance_time>0
            AND A.coursebalance_status=0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z
            WHERE {$datawhere} and X.student_id=A.student_id AND X.company_id=A.company_id
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id
            AND Z.coursecat_id=C.coursecat_id AND X.study_isreading=1
            AND X.study_endday>=CURDATE())
            GROUP BY E.school_id,A.student_id,D.coursecat_id
            ORDER BY E.school_id,D.coursecat_id,A.student_id";

//        if($request['is_all']!='1'){
//            $sql.=" and pricinglog_createtime>='{$starttime}'";
//        }

        $stayInClassList = $this->DataControl->selectClear($sql);//待入班数量

        if ($stayInClassList) {
            $data['stayInClass'] = count($stayInClassList);
        } else {
            $data['stayInClass'] = 0;
        }

        $sql = "select po.student_id 
              from smc_payfee_order as po
              where po.company_id='{$this->companyOne['company_id']}' and po.school_id='{$this->schoolOne['school_id']}'
              and po.order_status>0 and po.order_status<>4 and po.order_arrearageprice>'0'";
        $sql .= "group by po.student_id";

        $arrearsStudentList = $this->DataControl->selectClear($sql);//欠费学员数量

        if ($arrearsStudentList) {
            $data['arrearsStudent'] = count($arrearsStudentList);
        } else {
            $data['arrearsStudent'] = 0;
        }

        $sql = "select scl.student_id
              from smc_student_changelog as scl
              left join smc_class as cl on cl.class_id=scl.class_id
              left join smc_course as c on c.course_id=cl.course_id
              where {$datawhere} and scl.company_id='{$this->companyOne['company_id']}' and scl.school_id='{$this->schoolOne['school_id']}'
              and scl.stuchange_code in ('C02','B05')";

        $sql .= " and scl.changelog_createtime<='{$endtime}'";
        if ($request['is_all'] != '1') {
            $sql .= " and scl.changelog_createtime>='{$starttime}'";
        }
        $sql .= "group by scl.student_id";


        $lossStudentList = $this->DataControl->selectClear($sql);//流失人数

        if ($lossStudentList) {
            $data['lossStudent'] = count($lossStudentList);
        } else {
            $data['lossStudent'] = 0;
        }

        $sql = "select sc.class_id 
              from smc_class as sc
              left join smc_course as c on c.course_id=sc.course_id
              where {$datawhere} and sc.company_id='{$this->companyOne['company_id']}' and sc.school_id='{$this->schoolOne['school_id']}'
              and sc.class_createtime <='{$endtime}' and sc.class_stdate<='{$endDay}'";
        if ($request['is_all'] != '1') {
            $sql .= " and sc.class_createtime >='{$starttime}' and sc.class_stdate>='{$startDay}'";
        }

        $sql .= " group by sc.class_id";

        $newClassList = $this->DataControl->selectClear($sql);//新开班班级数
        if ($newClassList) {
            $data['newClass'] = count($newClassList);
        } else {
            $data['newClass'] = 0;
        }

        $sql = "select sc.class_id 
              from smc_class as sc
              left join smc_course as c on c.course_id=sc.course_id
              where sc.company_id='{$this->companyOne['company_id']}' and sc.school_id='{$this->schoolOne['school_id']}'
              and {$datawhere} and sc.class_status ='-1' and sc.class_updatatime <='{$endtime}'";
        if ($request['is_all'] != '1') {
            $sql .= " and sc.class_updatatime >='{$starttime}'";
        }

        $sql .= " group by sc.class_id";

        $closeClassList = $this->DataControl->selectClear($sql);//结班数量
        if ($closeClassList) {
            $data['closeClass'] = count($closeClassList);
        } else {
            $data['closeClass'] = 0;
        }

        $sql = "select scl.class_id 
              from smc_student_changelog as scl
              left join smc_class as cl on cl.class_id=scl.class_id
              left join smc_course as c on c.course_id=cl.course_id
              where {$datawhere} and scl.company_id='{$this->companyOne['company_id']}' and scl.school_id='{$this->schoolOne['school_id']}'
              and scl.stuchange_code='C03'";

        $sql .= " and scl.changelog_createtime<='{$endtime}'";
        if ($request['is_all'] != '1') {
            $sql .= " and scl.changelog_createtime>='{$starttime}'";
        }
        $sql .= "group by scl.student_id";

        $graduationList = $this->DataControl->selectClear($sql);//结业人数

        if ($graduationList) {
            $data['graduation'] = count($graduationList);
        } else {
            $data['graduation'] = 0;
        }

        return $data;
    }

    function gardenIncome($request)
    {

        $now = time();
        $time = strtotime('-2 month', $now);
        $starttime = date('Y-m-d 00:00:00', mktime(0, 0, 0, date('m', $time), 1, date('Y', $time)));
        $endtime = date('Y-m-d 23:39:59', mktime(0, 0, 0, date('m', $now), date('t', $now), date('Y', $now)));;
        $monarr = array();
        $time1 = $starttime;
        $time2 = $endtime;

        while (date("Y-m", strtotime($time1)) <= date("Y-m", strtotime($time2))) {
            $monarr[] = date('Y-m', strtotime($time1));

            $time1 = date("Y-m-d", strtotime("+1 month", strtotime($time1)));
        }

        if (!$monarr) {
            $this->error = true;
            $this->errortip = "计算错误";
            return false;
        }

        $starttime = strtotime($starttime);
        $endtime = strtotime($endtime);

        //认缴
        $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m') as income_confirmtime,si.income_price
              from smc_school_income as si
              where si.school_id='{$this->schoolOne['school_id']}' and si.income_type='1'
              and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
              ";
        $list = $this->DataControl->selectClear($sql);

        $tem_subscriptionArray = array();
        $subscriptionArray = array();
        $tem_sub = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_subscriptionArray[$val['income_confirmtime']]) {
                    $tem_subscriptionArray[$val['income_confirmtime']] += $val['income_price'] / 10000;
                } else {
                    $tem_subscriptionArray[$val['income_confirmtime']] = $val['income_price'] / 10000;
                }
            }
            foreach ($monarr as $monOne) {
                if ($tem_subscriptionArray[$monOne]) {
                    $tem_sub[$monOne] = $tem_subscriptionArray[$monOne];
                } else {
                    $tem_sub[$monOne] = 0;
                }
            }

            foreach ($tem_sub as $val) {
                $subscriptionArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_subscriptionArray[$monOne]) {
                    $tem_sub[$monOne] = $tem_subscriptionArray[$monOne];
                } else {
                    $tem_sub[$monOne] = 0;
                }
            }

            foreach ($tem_sub as $val) {
                $subscriptionArray[] = $val;
            }
        }

        //杂费
        $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m') as income_confirmtime,si.income_price
              from smc_school_income as si
              where si.school_id='{$this->schoolOne['school_id']}' and si.income_type='3' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
              ";

        $list = $this->DataControl->selectClear($sql);

        $tem_incidentalArray = array();
        $incidentalArray = array();
        $tem_inc = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_incidentalArray[$val['income_confirmtime']]) {
                    $tem_incidentalArray[$val['income_confirmtime']] += $val['income_price'] / 10000;
                } else {
                    $tem_incidentalArray[$val['income_confirmtime']] = $val['income_price'] / 10000;
                }
            }
            foreach ($monarr as $monOne) {
                if ($tem_incidentalArray[$monOne]) {
                    $tem_inc[$monOne] = $tem_incidentalArray[$monOne];
                } else {
                    $tem_inc[$monOne] = 0;
                }
            }

            foreach ($tem_inc as $val) {
                $incidentalArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_incidentalArray[$monOne]) {
                    $tem_inc[$monOne] = $tem_incidentalArray[$monOne];
                } else {
                    $tem_inc[$monOne] = 0;
                }
            }

            foreach ($tem_inc as $val) {
                $incidentalArray[] = $val;
            }
        }

        //教材
        $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m') as income_confirmtime,si.income_price
              from smc_school_income as si
              where si.school_id='{$this->schoolOne['school_id']}' and si.income_type='2' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
              ";

        $list = $this->DataControl->selectClear($sql);

        $tem_materialArray = array();
        $materialArray = array();
        $tem_mat = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_materialArray[$val['income_confirmtime']]) {
                    $tem_materialArray[$val['income_confirmtime']] += $val['income_price'] / 10000;
                } else {
                    $tem_materialArray[$val['income_confirmtime']] = $val['income_price'] / 10000;
                }
            }
            foreach ($monarr as $monOne) {
                if ($tem_materialArray[$monOne]) {
                    $tem_mat[$monOne] = $tem_materialArray[$monOne];
                } else {
                    $tem_mat[$monOne] = 0;
                }
            }

            foreach ($tem_mat as $val) {
                $materialArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_materialArray[$monOne]) {
                    $tem_mat[$monOne] = $tem_materialArray[$monOne];
                } else {
                    $tem_mat[$monOne] = 0;
                }
            }

            foreach ($tem_mat as $val) {
                $materialArray[] = $val;
            }
        }

        //课程
        $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m') as income_confirmtime,si.income_price
              from smc_school_income as si
              where si.school_id='{$this->schoolOne['school_id']}' and si.income_type='0' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
              ";

        $list = $this->DataControl->selectClear($sql);

        $tem_courseArray = array();
        $courseArray = array();
        $tem_cou = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_courseArray[$val['income_confirmtime']]) {
                    $tem_courseArray[$val['income_confirmtime']] += $val['income_price'] / 10000;
                } else {
                    $tem_courseArray[$val['income_confirmtime']] = $val['income_price'] / 10000;
                }
            }
            foreach ($monarr as $monOne) {
                if ($tem_courseArray[$monOne]) {
                    $tem_cou[$monOne] = $tem_courseArray[$monOne];
                } else {
                    $tem_cou[$monOne] = 0;
                }
            }

            foreach ($tem_cou as $val) {
                $courseArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_courseArray[$monOne]) {
                    $tem_cou[$monOne] = $tem_courseArray[$monOne];
                } else {
                    $tem_cou[$monOne] = 0;
                }
            }

            foreach ($tem_cou as $val) {
                $courseArray[] = $val;
            }
        }

        $data = array();
        $data['monthArray'] = $monarr;
        $data['subscriptionArray'] = $subscriptionArray;
        $data['incidentalArray'] = $incidentalArray;
        $data['materialArray'] = $materialArray;
        $data['courseArray'] = $courseArray;

        return $data;

    }

    function chargeInfo($request)
    {
        $monarr = array();
        if ($request['type'] == '0') {
            //本周
            $WeekAll = GetWeekAll(date("Y-m-d", time()));
            //本周
            $starttime = strtotime($WeekAll['nowweek_start']);
            $endtime = time();

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);

                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select pop.pay_price,FROM_UNIXTIME(pop.pay_successtime, '%Y-%m-%d' ) as pay_successtime
                  from smc_payfee_order_pay as pop
                  left join smc_payfee_order as po on po.order_pid=pop.order_pid
              ";
        } elseif ($request['type'] == '1') {
            //本月
            $starttime = strtotime(date("Y-m", time()) . '-01');
            $endtime = strtotime(date('Y-m-t', time()));

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);

                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select pop.pay_price,FROM_UNIXTIME(pop.pay_successtime, '%Y-%m-%d' ) as pay_successtime
                  from smc_payfee_order_pay as pop
                  left join smc_payfee_order as po on po.order_pid=pop.order_pid
              ";
        } elseif ($request['type'] == '2') {
            //本年
            $starttime = strtotime(date("Y", time()) . '-01-01');
            $endtime = strtotime(date("Y-m-d", time()));
            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m", $time1) <= date("Y-m", $time2)) {
                $monarr[] = date('Y-m', $time1);

                $time1 = strtotime("+1 month", $time1);
            }

            $sql = "select pop.pay_price,FROM_UNIXTIME(pop.pay_successtime, '%Y-%m' ) as pay_successtime
                  from smc_payfee_order_pay as pop
                  left join smc_payfee_order as po on po.order_pid=pop.order_pid
              ";
        } else {
            $this->error = true;
            $this->errortip = "无该选项";
            return false;
        }
        $sql .= "where po.company_id='{$this->companyOne['company_id']}' and po.school_id='{$this->schoolOne['school_id']}' and po.order_status>='0' and pop.pay_issuccess='1'
               and pop.pay_successtime>='{$starttime}' and pop.pay_successtime<='{$endtime}'
               and pop.paytype_code in ('wechat','alipay','pos','cash','bankcard') ";

        $list = $this->DataControl->selectClear($sql);

        $tem_chargeArray = array();
        $chargeArray = array();
        $tem_data = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_chargeArray[$val['pay_successtime']]) {
                    $tem_chargeArray[$val['pay_successtime']] += $val['pay_price'] / 10000;
                } else {
                    $tem_chargeArray[$val['pay_successtime']] = $val['pay_price'] / 10000;
                }
            }

            foreach ($monarr as $monOne) {
                if ($tem_chargeArray[$monOne]) {
                    $tem_data[$monOne] = $tem_chargeArray[$monOne];
                } else {
                    $tem_data[$monOne] = 0;
                }

            }

            foreach ($tem_data as $val) {
                $chargeArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_chargeArray[$monOne]) {
                    $tem_data[$monOne] = $tem_chargeArray[$monOne];
                } else {
                    $tem_data[$monOne] = 0;
                }

            }

            foreach ($tem_data as $val) {
                $chargeArray[] = $val;
            }
        }

        $data = array();
        $data['dayList'] = $monarr;
        $data['chargeArray'] = $chargeArray;

        return $data;

    }

    function enrolmentNumber($request)
    {
        $monarr = array();
        if ($request['type'] == '0') {
            $WeekAll = GetWeekAll(date("Y-m-d", time()));
            //本周
            $starttime = strtotime($WeekAll['nowweek_start']);
            $endtime = time();

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select FROM_UNIXTIME(st.trading_createtime, '%Y-%m-%d' ) as trading_createtime
                  from smc_student_trading as st
              ";
        } elseif ($request['type'] == '1') {
            //本月
            $starttime = strtotime(date("Y-m", time()) . '-01');
            $endtime = strtotime(date('Y-m-t', time()));

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);

                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select FROM_UNIXTIME(st.trading_createtime, '%Y-%m-%d' ) as trading_createtime
                  from smc_student_trading as st
              ";
        } elseif ($request['type'] == '2') {
            //本年
            $starttime = strtotime(date("Y", time()) . '-01-01');
            $endtime = strtotime(date("Y-m-d", time()));
            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m", $time1) <= date("Y-m", $time2)) {
                $monarr[] = date('Y-m', $time1);

                $time1 = strtotime("+1 month", $time1);
            }

            $sql = "select FROM_UNIXTIME(st.trading_createtime, '%Y-%m' ) as trading_createtime
                  from smc_student_trading as st
              ";
        } else {
            $this->error = true;
            $this->errortip = "无该选项";
            return false;
        }

        $sql .= "where st.company_id='{$this->companyOne['company_id']}' and st.school_id='{$this->schoolOne['school_id']}' and st.trading_status<>'-1' and st.trading_createtime>='{$starttime}' and st.trading_createtime<='{$endtime}'";

        $newSql = $sql . " and st.tradingtype_code='PaynewFee'";
        $list = $this->DataControl->selectClear($newSql);
        $tem_newArray = array();
        $newArray = array();
        $new_data = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_newArray[$val['trading_createtime']]) {
                    $tem_newArray[$val['trading_createtime']] += 1;
                } else {
                    $tem_newArray[$val['trading_createtime']] = 1;
                }
            }

            foreach ($monarr as $monOne) {
                if ($tem_newArray[$monOne]) {
                    $new_data[$monOne] = $tem_newArray[$monOne];
                } else {
                    $new_data[$monOne] = 0;
                }
            }

            foreach ($new_data as $val) {
                $newArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_newArray[$monOne]) {
                    $new_data[$monOne] = $tem_newArray[$monOne];
                } else {
                    $new_data[$monOne] = 0;
                }
            }

            foreach ($new_data as $val) {
                $newArray[] = $val;
            }
        }

        $oldSql = $sql . " and st.tradingtype_code='PayrenewFee'";
        $list = $this->DataControl->selectClear($oldSql);
        $tem_oldArray = array();
        $oldArray = array();
        $old_data = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_oldArray[$val['trading_createtime']]) {
                    $tem_oldArray[$val['trading_createtime']] += 1;
                } else {
                    $tem_oldArray[$val['trading_createtime']] = 1;
                }
            }
            foreach ($monarr as $monOne) {
                if ($tem_oldArray[$monOne]) {
                    $old_data[$monOne] = $tem_oldArray[$monOne];
                } else {
                    $old_data[$monOne] = 0;
                }
            }

            foreach ($old_data as $val) {
                $oldArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_oldArray[$monOne]) {
                    $old_data[$monOne] = $tem_oldArray[$monOne];
                } else {
                    $old_data[$monOne] = 0;
                }
            }

            foreach ($old_data as $val) {
                $oldArray[] = $val;
            }
        }

        $data = array();
        $data['monthArray'] = $monarr;
        $data['oldArray'] = $oldArray;
        $data['newArray'] = $newArray;

        return $data;

    }

    function hourIncome($request)
    {
        $monarr = array();
        if ($request['type'] == '0') {
            $WeekAll = GetWeekAll(date("Y-m-d", time()));
            //本周
            $starttime = strtotime($WeekAll['nowweek_start']);
            $endtime = time();

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m-%d') as income_confirmtime,si.income_price
              from smc_school_income as si";

        } elseif ($request['type'] == '1') {
            //本月
            $starttime = strtotime(date("Y-m", time()) . '-01');
            $endtime = strtotime(date('Y-m-t', time()));

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);

                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m-%d') as income_confirmtime,si.income_price
              from smc_school_income as si";
        } elseif ($request['type'] == '2') {
            //本年
            $starttime = strtotime(date("Y", time()) . '-01-01');
            $endtime = strtotime(date("Y-m-d", time()));
            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m", $time1) <= date("Y-m", $time2)) {
                $monarr[] = date('Y-m', $time1);

                $time1 = strtotime("+1 month", $time1);
            }

            $sql = "select FROM_UNIXTIME(si.income_confirmtime, '%Y-%m') as income_confirmtime,si.income_price
              from smc_school_income as si";
        } else {
            $this->error = true;
            $this->errortip = "无该选项";
            return false;
        }

        $sql .= " where si.school_id='{$this->schoolOne['school_id']}' and si.income_type='0' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'";
        $list = $this->DataControl->selectClear($sql);
        $tem_courseArray = array();
        $courseArray = array();
        $tem_cou = array();
        if ($list) {
            foreach ($list as $val) {
                if ($tem_courseArray[$val['income_confirmtime']]) {
                    $tem_courseArray[$val['income_confirmtime']] += $val['income_price'] / 10000;
                } else {
                    $tem_courseArray[$val['income_confirmtime']] = $val['income_price'] / 10000;
                }
            }
            foreach ($monarr as $monOne) {
                if ($tem_courseArray[$monOne]) {
                    $tem_cou[$monOne] = $tem_courseArray[$monOne];
                } else {
                    $tem_cou[$monOne] = 0;
                }
            }

            foreach ($tem_cou as $val) {
                $courseArray[] = $val;
            }
        } else {
            foreach ($monarr as $monOne) {
                if ($tem_courseArray[$monOne]) {
                    $tem_cou[$monOne] = $tem_courseArray[$monOne];
                } else {
                    $tem_cou[$monOne] = 0;
                }
            }

            foreach ($tem_cou as $val) {
                $courseArray[] = $val;
            }
        }

        $data = array();
        $data['monthArray'] = $monarr;
        $data['courseArray'] = $courseArray;

        return $data;
    }

    function attendance($request)
    {
        $monarr = array();
        if ($request['type'] == '0') {
            $WeekAll = GetWeekAll(date("Y-m-d", time()));
            //本周
            $starttime = strtotime($WeekAll['nowweek_start']);
            $endtime = time();

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }
        } elseif ($request['type'] == '1') {
            //本月
            $starttime = strtotime(date("Y-m", time()) . '-01');
            $endtime = strtotime(date('Y-m-t', time()));

            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $monarr[] = date('Y-m-d', $time1);

                $time1 = strtotime("+1 day", $time1);
            }
        } elseif ($request['type'] == '2') {
            //本年
            $starttime = strtotime(date("Y", time()) . '-01-01');
            $endtime = strtotime(date("Y-m-d", time()));
            $time1 = $starttime;
            $time2 = $endtime;

            while (date("Y-m", $time1) <= date("Y-m", $time2)) {
                $monarr[] = date('Y-m', $time1);

                $time1 = strtotime("+1 month", $time1);
            }
        } else {
            $this->error = true;
            $this->errortip = "无该选项";
            return false;
        }

        if (!$monarr) {
            $this->error = true;
            $this->errortip = "时间计算错误";
            return false;
        }

        $atArray = array();
        $abArray = array();
        $tem_atArray = array();
        $tem_abArray = array();
        if ($request['type'] == '0' || $request['type'] == '1') {
            $sql = "select sh.student_id,FROM_UNIXTIME(sc.clockinginlog_createtime, '%Y-%m-%d') as clockinginlog_createtime
              from smc_student_hourstudy as sh
              left join smc_student_clockinginlog as sc on sc.hourstudy_id=sh.hourstudy_id
              where sc.school_id='{$this->schoolOne['school_id']}' and sh.hourstudy_checkin='1'
              and sc.clockinginlog_createtime>='{$starttime}' and sc.clockinginlog_createtime<='{$endtime}' group by sh.hourstudy_id";
            $attendance = $this->DataControl->selectClear($sql);

            $sql = "select sh.student_id,FROM_UNIXTIME(sc.clockinginlog_createtime, '%Y-%m-%d') as clockinginlog_createtime
              from smc_student_hourstudy as sh
              left join smc_student_clockinginlog as sc on sc.hourstudy_id=sh.hourstudy_id
              where sc.school_id='{$this->schoolOne['school_id']}' and sh.hourstudy_checkin='0'
              and sc.clockinginlog_createtime>='{$starttime}' and sc.clockinginlog_createtime<='{$endtime}' group by sh.hourstudy_id";
            $absence = $this->DataControl->selectClear($sql);

            foreach ($monarr as $monOne) {
                $atNum = 0;
                $abNum = 0;
                if ($attendance) {
                    foreach ($attendance as $val) {
                        if ($val['clockinginlog_createtime'] == $monOne) {
                            $atNum += 1;
                        }
                    }
                }

                if ($absence) {
                    foreach ($absence as $val) {
                        if ($val['clockinginlog_createtime'] == $monOne) {
                            $abNum += 1;
                        }
                    }
                }

                $tem_atArray[$monOne] = $atNum;
                $tem_abArray[$monOne] = $abNum;
            }
        } elseif ($request['type'] == '2') {
            $sql = "select sh.student_id,FROM_UNIXTIME(sc.clockinginlog_createtime, '%Y-%m') as clockinginlog_createtime
              from smc_student_hourstudy as sh
              left join smc_student_clockinginlog as sc on sc.hourstudy_id=sh.hourstudy_id
              where sc.school_id='{$this->schoolOne['school_id']}' and sh.hourstudy_checkin='1'
              and sc.clockinginlog_createtime>='{$starttime}' and sc.clockinginlog_createtime<='{$endtime}' group by sh.hourstudy_id";
            $attendance = $this->DataControl->selectClear($sql);

            $sql = "select sh.student_id,FROM_UNIXTIME(sc.clockinginlog_createtime, '%Y-%m') as clockinginlog_createtime
              from smc_student_hourstudy as sh
              left join smc_student_clockinginlog as sc on sc.hourstudy_id=sh.hourstudy_id
              where sc.school_id='{$this->schoolOne['school_id']}' and sh.hourstudy_checkin='0'
              and sc.clockinginlog_createtime>='{$starttime}' and sc.clockinginlog_createtime<='{$endtime}' group by sh.hourstudy_id";
            $absence = $this->DataControl->selectClear($sql);

            foreach ($monarr as $monOne) {
                $atNum = 0;
                $abNum = 0;
                if ($attendance) {
                    foreach ($attendance as $val) {
                        if ($val['clockinginlog_createtime'] == $monOne) {
                            $atNum += 1;
                        }
                    }
                }

                if ($absence) {
                    foreach ($absence as $val) {
                        if ($val['clockinginlog_createtime'] == $monOne) {
                            $abNum += 1;
                        }
                    }
                }
                $tem_atArray[$monOne] = $atNum;
                $tem_abArray[$monOne] = $abNum;
            }
        } else {
            $this->error = true;
            $this->errortip = "无该选项";
            return false;
        }

        foreach ($tem_atArray as $val) {
            $atArray[] = $val;
        }
        foreach ($tem_abArray as $val) {
            $abArray[] = $val;
        }
        $data = array();
        $data['monthArray'] = $monarr;
        $data['attendanceArray'] = $atArray;
        $data['absenceArray'] = $abArray;

        return $data;
    }

    function reminderNotice($request)
    {
        if (date("d") >= 25) {
            return $this->LgStringSwitch('请将下月的OH、试读以及插班课设置完毕，以免影响学员试听');
        } elseif (date("d") <= 5) {
            return $this->LgStringSwitch('请将本月的OH、试读以及插班课设置完毕，以免影响学员试听');
        } else {
            $this->error = true;
            $this->errortip = "无需提醒";
            return false;
        }
    }

    function setCompanies($company_id, $school_id)
    {

        if ($company_id == '8888' || $company_id == '1001') {
            return false;
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$school_id}'");

        if (!$this->DataControl->getFieldOne("smc_school_coursecat_subject", "subject_id", "school_id='{$school_id}'")) {

            $catList = $this->DataControl->getList("smc_code_coursecat", "company_id='{$company_id}'");

            if ($catList && $schoolOne['companies_id'] > 0) {
                foreach ($catList as $catOne) {
                    $data = array();
                    $data['school_id'] = $school_id;
                    $data['coursecat_id'] = $catOne['coursecat_id'];
                    $data['companies_id'] = $schoolOne['companies_id'];
                    $this->DataControl->insertData("smc_school_coursecat_subject", $data);
                }
            }
        }

        if (!$this->DataControl->getFieldOne("smc_school_companies", "school_id", "school_id='{$school_id}'")) {
            $companiesList = $this->DataControl->getList("gmc_code_companies", "company_id='{$company_id}'");

            if ($companiesList) {
                foreach ($companiesList as $companiesOne) {
                    $data = array();
                    $data['school_id'] = $school_id;
                    $data['companies_id'] = $companiesOne['companies_id'];
                    $data['companies_storenumber'] = '暂无';
                    $this->DataControl->insertData("smc_school_companies", $data);
                }
            }
        }
    }

    function schoolInfo($request)
    {
        $this->setCompanies($request['company_id'], $request['school_id']);

        $datawhere = " 1 ";
        $today = date("Y-m-d");
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and cc.coursetype_id='{$request['coursetype_id']}'";
        }

//        if ($request['staffer_id'] == '25721') {
//            $datawhere .= " and cc.coursetype_id='79654'";
//        }

        $contractOne = $this->getContract($this->companyOne['company_id']);
        if ($contractOne && $contractOne['edition_id'] == '2') {
            //在籍学员

            $sql = "select count(distinct se.student_id) as count from smc_student_enrolled as se 
                    where se.school_id='{$this->schoolOne['school_id']}' and se.enrolled_status in (0,1)";

            $residenceStudentList = $this->DataControl->selectOne($sql);
            $residenceNum = $residenceStudentList['count'] ? $residenceStudentList['count'] : 0;

            $sql = "select count(distinct se.student_id) as count from smc_student_enrolled as se 
                    where se.school_id='{$this->schoolOne['school_id']}' and se.enrolled_status=1";

            $studyStudentList = $this->DataControl->selectOne($sql);
            $studyNum = $studyStudentList['count'] ? $studyStudentList['count'] : 0;

            //职工人数
            $sql = "select count(distinct staffer_id) as count
              from gmc_staffer_postbe
              where company_id='{$this->companyOne['company_id']}' and school_id='{$this->schoolOne['school_id']}' and postbe_status='1' and postbe_ismianjob='1'";
            $stafferList = $this->DataControl->selectOne($sql);
            $stafferNum = $stafferList['count'] ? $stafferList['count'] : 0;

            //班种待入班
            $sql = "select count(distinct se.student_id) as count from smc_student_enrolled as se 
                    where se.school_id='{$this->schoolOne['school_id']}' and se.enrolled_status=0";

            $stayInClassList = $this->DataControl->selectOne($sql);//待入班数量
            $stayInClassNum = $stayInClassList['count'] ? $stayInClassList['count'] : 0;

            //在开班级
            $sql = "select count(distinct cl.class_id) as count
              from smc_class as cl
              left join smc_course as cc on cc.course_id=cl.course_id
              where {$datawhere} and cl.company_id='{$this->companyOne['company_id']}' and cl.school_id='{$this->schoolOne['school_id']}' and cl.class_status>='0' and cl.class_stdate<=CURDATE() and (cl.class_enddate>=CURDATE() or cl.class_enddate='')";
            $classList = $this->DataControl->selectOne($sql);
            $classNum = $classList['count'] ? $classList['count'] : 0;

            //待开班班级
            $sql = "select count(distinct cl.class_id) as count
              from smc_class as cl
              left join smc_course as cc on cc.course_id=cl.course_id
              where {$datawhere} and cl.company_id='{$this->companyOne['company_id']}' and cl.school_id='{$this->schoolOne['school_id']}' and cl.class_status>='0' and cl.class_stdate>CURDATE()";
            $stayClassList = $this->DataControl->selectOne($sql);
            $stayClassNum = $stayClassList['count'] ? $stayClassList['count']:0;
        } else {
//在籍学员

            $sql = "SELECT count(*) as count FROM (SELECT a.student_id
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                LEFT JOIN smc_code_coursetype cc ON cc.coursetype_id = b.coursetype_id
                left join smc_student as st on st.student_id=a.student_id
                WHERE {$datawhere} and a.school_id='{$this->schoolOne['school_id']}' and a.coursebalance_time > 0
                GROUP BY a.school_id, a.student_id, b.coursetype_id) as subquery";

            $residenceStudentList = $this->DataControl->selectOne($sql);
            $residenceNum = $residenceStudentList['count'] ? $residenceStudentList['count'] : 0;

            $sql = "SELECT count(*) as count FROM (SELECT A.school_id
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                WHERE {$datawhere}
                AND B.class_type='0' AND B.class_status>'-2'
                AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) >= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                AND A.school_id='{$this->schoolOne['school_id']}'
                GROUP BY A.school_id,A.student_id,C.coursetype_id
                HAVING MIN(A.study_beginday)<='{$today}' and MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>='{$today}'
            ) as subquery";

            $studyStudentList = $this->DataControl->selectOne($sql);
            $studyNum = $studyStudentList['count'] ? $studyStudentList['count'] : 0;

            //职工人数
            $sql = "select count(distinct staffer_id) as count
              from gmc_staffer_postbe
              where company_id='{$this->companyOne['company_id']}' and school_id='{$this->schoolOne['school_id']}' and postbe_status='1' and postbe_ismianjob='1'";
            $stafferList = $this->DataControl->selectOne($sql);
            $stafferNum = $stafferList['count'] ? $stafferList['count'] : 0;

            //班种待入班
            $sql = "SELECT count(*) as count FROM (SELECT C.student_id
            FROM smc_student_coursebalance A
            LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id
            LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id
            LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id
            LEFT JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
            left join smc_code_coursetype as cc on cc.coursetype_id = B.coursetype_id
            WHERE {$datawhere} and A.school_id='{$request['school_id']}'
            AND A.company_id='{$this->companyOne['company_id']}'
            AND B.course_inclasstype in (0,1)
            AND A.coursebalance_time>0
            -- AND A.coursebalance_status=0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id
            AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1
            AND X.study_endday>=CURDATE())
            GROUP BY E.school_id,C.student_id,D.coursecat_id) as subquery";

            $stayInClassList = $this->DataControl->selectOne($sql);//待入班数量
            $stayInClassNum = $stayInClassList['count'] ? $stayInClassList['count'] : 0;

            //在开班级
            $sql = "select count(distinct cl.class_id) as count
              from smc_class as cl
              left join smc_course as cc on cc.course_id=cl.course_id
              where {$datawhere} and cl.company_id='{$this->companyOne['company_id']}' and cl.school_id='{$this->schoolOne['school_id']}' and cl.class_status>='0' and cl.class_stdate<=CURDATE() and (cl.class_enddate>=CURDATE() or cl.class_enddate='')";
            $classList = $this->DataControl->selectOne($sql);
            $classNum = $classList['count'] ? $classList['count'] : 0;

            //待开班班级
            $sql = "select count(distinct cl.class_id) as count
              from smc_class as cl
              left join smc_course as cc on cc.course_id=cl.course_id
              where {$datawhere} and cl.company_id='{$this->companyOne['company_id']}' and cl.school_id='{$this->schoolOne['school_id']}' and cl.class_status>='0' and cl.class_stdate>CURDATE()";
            $stayClassList = $this->DataControl->selectOne($sql);
            $stayClassNum = $stayClassList['count'] ? $stayClassList['count'] : 0;
        }

     
        //最新未读消息
        $sql = "select nt.notice_id,FROM_UNIXTIME(nt.notice_createtime, '%Y-%m-%d') as notice_createtime,nt.notice_title,nt.notice_connet
              ,(select count(nr.read_id) from gmc_company_notice_read as nr where nr.notice_id=nt.notice_id and nr.staffer_id=sp.staffer_id) as readNum
              from gmc_staffer_postbe as sp
              left join gmc_company_notice_postapply as np on np.post_id=sp.post_id
              left join gmc_company_notice as nt on nt.notice_id=np.notice_id
              where sp.staffer_id='{$this->stafferOne['staffer_id']}' and sp.school_id='{$this->schoolOne['school_id']}'
              HAVING readNum='0'
              order by nt.notice_createtime desc
              ";
        $noticeOne = $this->DataControl->selectOne($sql);
        if (!$noticeOne) {
            $noticeOne = array();
        }

        $today = date("Y-m-d");
        $yesterday = date("Y-m-d", strtotime("-1 day"));

        $sql = "select SUM(income_price) as income_price from smc_school_income where school_id='{$this->schoolOne['school_id']}' and FROM_UNIXTIME(income_confirmtime, '%Y-%m-%d')='{$today}'";
        $todayIncome = $this->DataControl->selectOne($sql);
        $sql = "select SUM(income_price) as income_price from smc_school_income where school_id='{$this->schoolOne['school_id']}' and FROM_UNIXTIME(income_confirmtime, '%Y-%m-%d')='{$yesterday}'";
        $yesterdayIncome = $this->DataControl->selectOne($sql);

        // 计算今天的时间戳范围，避免在WHERE子句中使用函数
        $todayStart = strtotime($today . ' 00:00:00');
        $todayEnd = strtotime($today . ' 23:59:59');
        
        /*
         * MySQL 8 性能优化建议:
         * 1. 创建复合索引提高查询性能:
         *    CREATE INDEX idx_pay_school_type_success_time ON smc_payfee_order_pay(school_id, paytype_code, pay_issuccess, pay_successtime);
         *    CREATE INDEX idx_order_school_student ON smc_payfee_order(school_id, student_id, order_pid);
         *    CREATE INDEX idx_paytype_code_charge ON smc_code_paytype(paytype_code, paytype_ischarge);
         * 
         * 2. 如果pay_successtime经常用于范围查询，考虑按时间分区表
         * 3. 定期分析表统计信息: ANALYZE TABLE table_name;
         * 4. 使用EXPLAIN分析查询计划，确保索引被正确使用
         */
        
        // 优化后的SQL - 使用窗口函数替代NOT EXISTS，提高MySQL 8性能
        $sql = "SELECT SUM(pay_price) as pay_price
                FROM (
                    SELECT A.pay_price,
                           ROW_NUMBER() OVER (
                               PARTITION BY B.student_id 
                               ORDER BY A.pay_successtime ASC
                           ) as rn
                    FROM smc_payfee_order_pay A
                    INNER JOIN smc_payfee_order B ON A.order_pid = B.order_pid
                    INNER JOIN smc_code_paytype C ON C.paytype_code = A.paytype_code
                    WHERE B.school_id = '{$this->schoolOne['school_id']}' 
                      AND C.paytype_ischarge = '1' 
                      AND A.pay_issuccess = '1'
                      AND A.pay_successtime >= {$todayStart}
                      AND A.pay_successtime <= {$todayEnd}
                ) AS ranked_payments
                WHERE rn = 1";
        $newPay = $this->DataControl->selectOne($sql);
        // 优化后的SQL - 计算老用户付款（今天之前有付款记录的学生）
        $sql = "SELECT SUM(today_pay.pay_price) as pay_price
                FROM (
                    SELECT B.student_id, SUM(A.pay_price) as pay_price
                    FROM smc_payfee_order_pay A
                    INNER JOIN smc_payfee_order B ON A.order_pid = B.order_pid  
                    INNER JOIN smc_code_paytype C ON C.paytype_code = A.paytype_code
                    WHERE B.school_id = '{$this->schoolOne['school_id']}' 
                      AND C.paytype_ischarge = '1' 
                      AND A.pay_issuccess = '1'
                      AND A.pay_successtime >= {$todayStart}
                      AND A.pay_successtime <= {$todayEnd}
                    GROUP BY B.student_id
                ) today_pay
                WHERE EXISTS (
                    SELECT 1 
                    FROM smc_payfee_order_pay A1
                    INNER JOIN smc_payfee_order B1 ON A1.order_pid = B1.order_pid
                    INNER JOIN smc_code_paytype C1 ON C1.paytype_code = A1.paytype_code
                    WHERE B1.school_id = '{$this->schoolOne['school_id']}' 
                      AND C1.paytype_ischarge = '1' 
                      AND A1.pay_issuccess = '1'
                      AND B1.student_id = today_pay.student_id
                      AND A1.pay_successtime < {$todayStart}
                )";
        $oldPay = $this->DataControl->selectOne($sql);

        // 优化后的SQL - 计算今天所有付款人数
        $sql = "SELECT count(distinct po.student_id) as count
                FROM smc_payfee_order_pay pop
                INNER JOIN smc_payfee_order po ON po.order_pid = pop.order_pid
                INNER JOIN smc_code_paytype C ON C.paytype_code = pop.paytype_code
                WHERE pop.pay_issuccess = '1' 
                  AND C.paytype_ischarge = '1' 
                  AND pop.pay_successtime >= {$todayStart}
                  AND pop.pay_successtime <= {$todayEnd}
                  AND po.school_id = '{$this->schoolOne['school_id']}'";
        $todayNumList = $this->DataControl->selectOne($sql);
        $todayNum = $todayNumList['count'] ? $todayNumList['count'] : 0;

        // 计算昨天的时间戳范围
        $yesterdayStart = strtotime($yesterday . ' 00:00:00');
        $yesterdayEnd = strtotime($yesterday . ' 23:59:59');
        
        // 优化后的SQL - 计算昨天所有付款人数
        $sql = "SELECT count(distinct po.student_id) as count
                FROM smc_payfee_order_pay pop
                INNER JOIN smc_payfee_order po ON po.order_pid = pop.order_pid
                INNER JOIN smc_code_paytype C ON C.paytype_code = pop.paytype_code
                WHERE pop.pay_issuccess = '1' 
                  AND C.paytype_ischarge = '1' 
                  AND pop.pay_successtime >= {$yesterdayStart}
                  AND pop.pay_successtime <= {$yesterdayEnd}
                  AND po.school_id = '{$this->schoolOne['school_id']}'";
        $yesterdayNumList = $this->DataControl->selectOne($sql);
        $yesterdayNum = $yesterdayNumList['count'] ? $yesterdayNumList['count'] : 0;

        // 优化后的SQL - 计算今天新用户付款人数
        $sql = "SELECT count(distinct B.student_id) as count
                FROM smc_payfee_order_pay A
                INNER JOIN smc_payfee_order B ON A.order_pid = B.order_pid
                INNER JOIN smc_code_paytype C ON C.paytype_code = A.paytype_code
                WHERE B.school_id = '{$this->schoolOne['school_id']}' 
                  AND C.paytype_ischarge = '1' 
                  AND A.pay_issuccess = '1'
                  AND A.pay_successtime >= {$todayStart}
                  AND A.pay_successtime <= {$todayEnd}
                  AND NOT EXISTS (
                      SELECT 1 
                      FROM smc_payfee_order_pay A1
                      INNER JOIN smc_payfee_order B1 ON A1.order_pid = B1.order_pid
                      INNER JOIN smc_code_paytype C1 ON C1.paytype_code = A1.paytype_code
                      WHERE B1.school_id = '{$this->schoolOne['school_id']}' 
                        AND C1.paytype_ischarge = '1' 
                        AND A1.pay_issuccess = '1'
                        AND B1.student_id = B.student_id 
                        AND A1.pay_successtime < {$todayStart}
                  )";
        $newNumList = $this->DataControl->selectOne($sql);
        $newNum = $newNumList['count'] ? $newNumList['count'] : 0;

        // 优化后的SQL - 计算今天老用户付款人数
        $sql = "SELECT count(distinct B.student_id) as count
                FROM smc_payfee_order_pay A
                INNER JOIN smc_payfee_order B ON A.order_pid = B.order_pid
                INNER JOIN smc_code_paytype C ON C.paytype_code = A.paytype_code
                WHERE B.school_id = '{$this->schoolOne['school_id']}' 
                  AND C.paytype_ischarge = '1' 
                  AND A.pay_issuccess = '1'
                  AND A.pay_successtime >= {$todayStart}
                  AND A.pay_successtime <= {$todayEnd}
                  AND EXISTS (
                      SELECT 1 
                      FROM smc_payfee_order_pay A1
                      INNER JOIN smc_payfee_order B1 ON A1.order_pid = B1.order_pid
                      INNER JOIN smc_code_paytype C1 ON C1.paytype_code = A1.paytype_code
                      WHERE B1.school_id = '{$this->schoolOne['school_id']}' 
                        AND C1.paytype_ischarge = '1' 
                        AND A1.pay_issuccess = '1'
                        AND B1.student_id = B.student_id 
                        AND A1.pay_successtime < {$todayStart}
                  )";
        $oldNumList = $this->DataControl->selectOne($sql);
        $oldNum = $oldNumList['count'] ? $oldNumList['count'] : 0;

        $sql = "select count(*) as count
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_student_study as ss on ss.class_id=ch.class_id
              where ss.school_id='{$this->schoolOne['school_id']}' and ch.hour_day='{$today}' and ss.study_endday>='{$today}' and ss.study_beginday<='{$today}' and ch.hour_iswarming='0' and c.class_type=0";
        $shouldList = $this->DataControl->selectOne($sql);
        $shouldNum = $shouldList['count'] ? $shouldList['count'] : 0;

        $sql = "select count(*) as count
              from smc_student_hourstudy as sh
              left join smc_class_hour as ch on ch.hour_id=sh.hour_id
              left join smc_class as c on c.class_id=ch.class_id
              where ch.hour_day='{$today}' and c.school_id='{$this->schoolOne['school_id']}' and sh.hourstudy_checkin='1' and ch.hour_iswarming='0' and c.class_type=0
              ";
        $attendanceList = $this->DataControl->selectOne($sql);
        $attendanceNum = $attendanceList['count'] ? $attendanceList['count'] : 0;

        $WeekAll = GetWeekAll(date("Y-m-d", time()));
        //本周
        $starttime = $WeekAll['nowweek_start'];
        $endtime = date("Y-m-d");

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.class_id,c.class_cnname,ch.hour_day,ch.hour_starttime,sc.course_inclasstype
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
              where c.school_id='{$this->schoolOne['school_id']}' and ch.hour_day<='{$endtime}'
              and ch.hour_ischecking=0 and c.class_status>='0' and cc.coursetype_isopenclass=0 and c.class_type=0
              order by ch.hour_day desc,ch.hour_starttime desc
              limit {$pagestart},{$num}
              ";
        $hourList = $this->DataControl->selectClear($sql);
        if (!$hourList) {
            $hourList = array();
        }

        $sql = "select count(*) as count
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
              where c.school_id='{$this->schoolOne['school_id']}' and ch.hour_day<='{$endtime}'
              and ch.hour_ischecking=0 and c.class_status>='0' and cc.coursetype_isopenclass=0 and c.class_type=0
              ";
        $allHourList = $this->DataControl->selectOne($sql);
        $allNum = $allHourList['count'] ? $allHourList['count'] : 0;

        $phoneOne = $this->DataControl->getFieldOne("gmc_company", "company_phone", "company_id='{$this->companyOne['company_id']}'");

        $data = array();
        $data['residenceNum'] = $residenceNum;
        $data['studyNum'] = $studyNum;
        $data['stafferNum'] = $stafferNum;
        $data['classNum'] = $classNum;
        $data['stayInClassNum'] = $stayInClassNum;
        $data['stayClassNum'] = $stayClassNum;
        $data['noticeOne'] = $noticeOne;


//        if ($request['staffer_id'] == '25721') {
//            $incomeArray = array();
//            $incomeArray['todayIncome'] = 0.00;
//            $incomeArray['yesterdayIncome'] = 0.00;
//            $incomeArray['newPay'] = 0.00;
//            $incomeArray['oldPay'] = 0.00;
//            $incomeArray['todayNum'] = 0;
//            $incomeArray['yesterdayNum'] = 0;
//            $incomeArray['newNum'] = 0;
//            $incomeArray['oldNum'] = 0;
//        } else {
            $incomeArray = array();
            $incomeArray['todayIncome'] = sprintf("%.2f", $todayIncome['income_price']) ? sprintf("%.2f", $todayIncome['income_price']) : 0.00;
            $incomeArray['yesterdayIncome'] = sprintf("%.2f", $yesterdayIncome['income_price']) ? sprintf("%.2f", $yesterdayIncome['income_price']) : 0.00;
            $incomeArray['newPay'] = sprintf("%.2f", $newPay['pay_price']) ? sprintf("%.2f", $newPay['pay_price']) : 0.00;
            $incomeArray['oldPay'] = sprintf("%.2f", $oldPay['pay_price']) ? sprintf("%.2f", $oldPay['pay_price']) : 0.00;
            $incomeArray['todayNum'] = $todayNum;
            $incomeArray['yesterdayNum'] = $yesterdayNum;
            $incomeArray['newNum'] = $newNum;
            $incomeArray['oldNum'] = $oldNum;
//        }


        $data['income'] = $incomeArray;

        $data['telephone'] = $phoneOne['company_phone'];

        $attArray = array();
        $attArray['shouldNum'] = $shouldNum;
        $attArray['attendanceNum'] = $attendanceNum;
        if ($shouldNum == 0) {
            $attArray['attendanceRate'] = 0;
        } else {
            $attArray['attendanceRate'] = (round($attendanceNum / $shouldNum, 2) ? round($attendanceNum / $shouldNum, 2) : 0) * 100;
        }

        $data['att'] = $attArray;
        $data['hourList'] = $hourList;
        $data['allNum'] = $allNum;
        return $data;
    }


    //首页 -- 月份列表日程展示
    function monthEventApi($paramArray)
    {
        $date = getthemonth($paramArray['yearMonth']);
        //当前日期
        $sdefaultDate = date("Y-m-d");

        $sql = "select event_id,event_time
                from smc_event 
                where staffer_id='{$paramArray['staffer_id']}'  and event_time between '{$date[0]}' and '{$date[1]}' and school_id='{$paramArray['school_id']}'
                GROUP BY event_time";
        $mothListArray = $this->DataControl->selectClear($sql);

        $monthArr = array();
        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['event_time']));
                $v['month'] = date('m', strtotime($v['event_time']));
                $v['day'] = date('d', strtotime($v['event_time']));
                $temp = $v['year'] . "-" . $v['month'] . "-" . $v['day'];

                $v['week'] = date('w', strtotime($temp));
                unset($mothListArray[$k]['event_time']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }


        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = $i;
                    $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                    if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                        $dateWeek[$i]['isnow'] = 1;
                    } else {
                        $dateWeek[$i]['isnow'] = 0;
                    }

                    $data['is_have'] = strval(-1);

                    array_push($mothListArray, $data);
                }

                usort($mothListArray, function ($a, $b) {
                    if ($a['day'] == $b['day']) return 0;
                    return $a['day'] > $b['day'] ? 1 : -1;
                });
            }
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = $i;
                $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                    $dateWeek[$i]['isnow'] = 1;
                } else {
                    $dateWeek[$i]['isnow'] = 0;
                }

                $data['is_have'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first = 0;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w = date('w', strtotime($sdefaultDate));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start = date('Y-m-d', strtotime("$sdefaultDate -" . ($w ? $w - $first : 6) . ' days'));
        //本周结束日期
        $week_end = date('Y-m-d', strtotime("$week_start +6 days"));
        //echo "$week_start"."$week_end";
        //组合数据
        $dateWeek = [];
        for ($i = 0; $i <= 6; $i++) {
            $dateWeek[$i]['yearMonth'] = date('Y-m', strtotime("$week_start + $i days"));
            $dateWeek[$i]['daytime'] = date('Y-m-d', strtotime("$week_start + $i days"));
            $dateWeek[$i]['day'] = date('d', strtotime("$week_start + $i days"));
            $dateWeek[$i]['week'] = date('w', strtotime("$week_start + $i days"));
            if ($dateWeek[$i]['daytime'] == $sdefaultDate) {
                $dateWeek[$i]['isnow'] = 1;
            } else {
                $dateWeek[$i]['isnow'] = 0;
            }

            $sql = "select event_id,event_time
                from smc_event 
                where staffer_id='{$paramArray['staffer_id']}'  and event_time = '{$dateWeek[$i]['daytime']}' and school_id='{$paramArray['school_id']}'
                GROUP BY event_time";
            $eventOne = $this->DataControl->selectOne($sql);

            $dateWeek[$i]['event_id'] = $eventOne['event_id'] == '' ? 0 : $eventOne['event_id'];
        }
        $data = array();
        $data['week'] = $dateWeek;
        $data['mothList'] = $mothListArray;
        return $data;
    }

    //首页 -- 某日的日程安排
    function eventOneApi($paramArray)
    {
        $datawhere = " 1 and e.event_time = '{$paramArray['event_time']}' and e.staffer_id = '{$paramArray['staffer_id']}' ";
        $sql = "SELECT  e.event_tag,e.event_remark
                FROM smc_event as e
                WHERE {$datawhere} and  school_id='{$paramArray['school_id']}'";
        $dataList = $this->DataControl->selectClear($sql);
        return $dataList;
    }

    //首页 -- 新增日程安排
    function addEventAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['event_tag'] = $paramArray['event_tag'];
        $data['event_time'] = $paramArray['event_time'];
        $data['event_remark'] = $paramArray['event_remark'];
        $data['event_createtime'] = time();

        if ($this->DataControl->insertData('smc_event', $data)) {
            return true;
        } else {
            return false;
        }
    }

    function test($to, $datas, $tempId)
    {
        $Model = new \Model\Api\SmsqmModel();
        $Model->setAccount();
        $Model->setAppId();

        $result = $Model->sendTemplateSMS($to, $datas, $tempId);
        if ($result == NULL) {
            $this->error = true;
            $this->errortip = "result error!";
            return false;
        }
        if ($result->statusCode != 0) {
            $this->error = true;
            $this->errortip = "error code :" . $result->statusCode . "error msg :" . $result->statusMsg;
            return false;
        } else {
            return true;
        }
    }

}
