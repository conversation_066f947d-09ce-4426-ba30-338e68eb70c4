<?php
namespace Model\Smc;

class ForwardModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,staffer_ismanage", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }


    function getForwardList($request)
    {
        $datawhere = " sc.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_time>0 or scb.coursebalance_figure>0) and sse.enrolled_status>=0 and sc.main_course_id=0";

        if (isset($request['exclude_inclasstype']) && $request['exclude_inclasstype'] != '') {
            $datawhere .= " and sc.course_inclasstype<>'{$request['exclude_inclasstype']}'";
        }

        $datawhere .= " and not exists(select 1 from smc_forward_application as x where x.student_id=scb.student_id and x.course_id=scb.course_id and x.school_id=scb.school_id and (x.application_status=0 or (x.application_status=1 and x.out_class_date>=curdate())))";

        $having = " 1 ";

        if($this->stafferOne['staffer_ismanage']!=1){
            $having.=" and order_status=4 " ;
        }


        $time = date("Y-m-d", time());
        $sql = "SELECT scb.coursebalance_id,scb.course_id,sc.course_cnname,sc.course_branch,scb.coursebalance_figure,scb.coursebalance_time,ifnull(scf.courseforward_price,0) as courseforward_price,sc.coursecat_id,sc.course_inclasstype,sc.course_isforward_examine
              ,(select cp.pricinglog_refunddeadline from smc_student_coursebalance_pricinglog as cp where cp.student_id=scb.student_id and cp.school_id=scb.school_id and cp.course_id=scb.course_id and cp.pricing_id=scb.pricing_id order by pricinglog_id desc limit 0,1) as refunddeadline
              ,(select SUM(poc.ordercourse_buynums) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=scb.course_id and po.student_id=scb.student_id and po.school_id=scb.school_id and po.order_status>0 limit 0,1) as ordercourse_buynums
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=scb.student_id and sf.course_id=scb.course_id) as allNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=scb.student_id and sf.course_id=scb.course_id and sf.is_use=0) as freeNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=scb.student_id and sf.course_id=scb.course_id and sf.is_use=1) as useNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=scb.student_id and sf.course_id=scb.course_id and sf.is_use='-1') as cancelNum
              ,ifnull((select z.coursepacks_id from smc_payfee_order as x,smc_payfee_order_course as y,smc_payfee_mergeorder z where y.order_pid=x.order_pid and x.mergeorder_pid=z.mergeorder_pid and x.school_id=scb.school_id and x.student_id=scb.student_id and y.course_id=scb.course_id and x.order_status>0 order by x.order_id desc limit 0,1),'') as mergeorder_pid
              ,ifnull((select x.order_status from smc_payfee_order as x,smc_payfee_order_course as y where y.order_pid=x.order_pid and x.school_id=scb.school_id and x.student_id=scb.student_id and y.course_id=scb.course_id and x.order_status>0 order by x.order_id desc limit 0,1),'') as order_status,sc.course_isfollow,sc.main_course_id
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scb.student_id=scf.student_id
              left join smc_course as sc on scb.course_id=sc.course_id
              left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
              where {$datawhere} and sc.course_isforward=1
              group by scb.coursebalance_id
              having {$having} 
              order by scb.course_id DESC
        ";
//        HAVING order_status=4
        //-- HAVING (refunddeadline='' or refunddeadline>='{$time}' or refunddeadline is null)

        $classList = $this->DataControl->selectClear($sql);

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无课程数据";
            return false;
        }

        $schoolOne=$this->DataControl->getFieldOne("smc_school","school_isforwardapply","school_id='{$this->school_id}'");

        foreach ($classList as &$val) {
            $sql = "select c.class_id,c.class_cnname,c.class_branch
                  ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking='1') as classNum
                  ,(select count(ssh.hourstudy_id) from smc_student_hourstudy as ssh where ssh.class_id=c.class_id and ssh.student_id=sss.student_id and ssh.hourstudy_checkin='1') as stuNum
                  ,ifnull((select 1 from smc_student_hourstudy as x,smc_school_income as y where x.hourstudy_id=y.hourstudy_id and x.student_id=sss.student_id and x.class_id=c.class_id and y.income_type=0 and y.income_price>0 limit 0,1),0) as is_income
                  from smc_student_study as sss
                  left join smc_class as c on sss.class_id=c.class_id
                  where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and sss.student_id='{$request['student_id']}' and c.course_id='{$val['course_id']}' and sss.study_isreading='1' and c.class_type='0'
                  order by sss.study_id desc
                  limit 0,1
                  ";
            $studentOne = $this->DataControl->selectOne($sql);
            if ($studentOne) {
                $val['class_id'] = $studentOne['class_id'];
                $val['class_cnname'] = $studentOne['class_cnname'];
                $val['class_branch'] = $studentOne['class_branch'];
                $val['classNum'] = $studentOne['classNum'];
                $val['stuNum'] = $studentOne['stuNum'];
                $val['is_income'] = $studentOne['is_income'];

                if($val['course_isforward_examine']==1 && $val['is_income']==1 && $schoolOne['school_isforwardapply']==1){
                    $val['is_need_examine'] = 1;
                }else{
                    $val['is_need_examine'] = 0;
                }

            } else {
                $val['class_id'] = '--';
                $val['class_cnname'] = '--';
                $val['class_branch'] = '--';
                $val['classNum'] = '--';
                $val['stuNum'] = '--';
                $val['is_income'] = '';

                $val['is_need_examine'] = 0;
            }

            
        }

        return $classList;
    }

    function carryForward($request)
    {
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $list = json_decode(stripslashes($request['list']), true);

        foreach ($list as $val) {
            if ($val['num'] && $val['num'] > 0) {
                $a = $this->DataControl->selectOne("
                SELECT
                    p.post_istopjob
                FROM
                    gmc_staffer_postbe AS sp
                LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
                WHERE sp.postbe_id = '{$request['re_postbe_id']}'");

                if ($a['post_istopjob'] != '1') {
                    $this->error = true;
                    $this->errortip = "仅校园最高权限可操作部分结转！";
                    return false;
                }

//                $sql = "select po.order_pid
//                      from smc_payfee_order as po
//                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
//                      where poc.course_id='{$val['course_id']}' and po.student_id='{$request['student_id']}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
//                      and po.order_status>=0
//                      ";
//                if ($this->DataControl->selectOne($sql)) {
//                    $this->error = true;
//                    $this->errortip = "课程存在欠费,不可部分结转";
//                    return false;
//                }

                $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$val['course_id']}'");
                if ($courseOne['course_inclasstype'] == 1) {
                    $this->error = true;
                    $this->errortip = "期度课程不可部分结转";
                    return false;
                }

                if ($val['class_id'] > 0) {

                    $sql = "select coursebalance_time from smc_student_coursebalance where student_id='{$request['student_id']}' and course_id='{$val['course_id']}' and school_id='{$this->school_id}'";

                    $stuCourseBalanceOne = $this->DataControl->selectOne($sql);

                    $sql = "select hour_id from smc_class_hour where class_id='{$val['class_id']}' and hour_ischecking=0 and hour_isfree=0 and hour_iswarming=0";
                    $hourList = $this->DataControl->selectClear($sql);

                    $hourNum = $hourList ? count($hourList) : 0;
//                    if($stuCourseBalanceOne['coursebalance_time']<=$hourNum){
//                        $this->error = true;
//                        $this->errortip = "剩余课次必须大于班级剩余课次";
//                        return false;
//                    }

//                    if($val['num']>($stuCourseBalanceOne['coursebalance_time']-$hourNum)){
//                        $this->error = true;
//                        $this->errortip = "该学生部分结转最多只可结转".($stuCourseBalanceOne['coursebalance_time']-$hourNum).'节课';
//                        return false;
//                    }

                }
            }

//            $sql = "select a.company_id
//                    ,a.companies_id
//                    ,a.school_id
//                    ,b.coursetype_id
//                    ,b.coursecat_id
//                    ,b.course_id
//                    ,a.class_id
//                    ,a.student_id
//                    ,a.hourstudy_id
//                    ,a.income_id
//                    ,FROM_UNIXTIME(a.income_confirmtime,'%Y-%m-%d') as income_date
//                    from smc_school_income a
//                    left join smc_course b on a.course_id=b.course_id
//                    left join gmc_code_companies c on a.companies_id=c.companies_id
//                    left join smc_student_coursebalance d on a.school_id=d.school_id and a.course_id=d.course_id and a.student_id=d.student_id
//                    left join cmb_trans_transfer e on a.student_id=e.student_id and a.hourstudy_id=e.hourstudy_id
//                    where 1
//                    and a.school_id='{$this->school_id}'
//                    and a.student_id='{$request['student_id']}'
//                    and a.course_id='{$val['course_id']}'
//                    and a.income_type=0
//                    and a.hourstudy_id>0
//                    and d.coursebalance_issupervise=1
//                    and c.companies_issupervise=1
//                    and b.course_issupervise=1
//                    and a.income_confirmtime>='1645027200'
//                    and ifnull(e.income_isconfirm,0)=0";
//            $confirmList = $this->DataControl->selectClear($sql);
//
//            if ($confirmList) {
//                $this->error = true;
//                $this->errortip = "该学员有待确认课程，请确认后结转！";
//                return false;
//            }



            $res = $BalanceModel->carryOver($request['student_id'], $val['course_id'], $val['class_id'], '', strtotime($request['create_time']), $val['num'], $request['change_reason']);
            if (!$res) {
                $this->error = true;
                $this->errortip = $BalanceModel->errortip;
                return false;
            }
        }

        return true;
    }

    function getForwardPrice($request)
    {
        $list = json_decode(stripslashes($request['list']), true);
        $refundArray = array();
        $balance = 0;
        $forward = 0;
        $refundprice = 0;
        $all_coursebalance_figure = 0;
        $all_courseforward_price = 0;

        $day = date("Y-m-d");
        $stublcOne = $this->DataControl->selectOne("select s.student_forwardprice,sum(b.student_balance) as student_balance from smc_student as s,smc_student_balance as b WHERE b.student_id = s.student_id and b.school_id = '{$request['school_id']}' and s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}'");

        $sql = "select sb.*,cc.companies_cnname 
              from smc_student_balance as sb,gmc_code_companies as cc 
              where sb.companies_id=cc.companies_id and sb.school_id = '{$request['school_id']}' and sb.student_id='{$request['student_id']}' and sb.company_id='{$request['company_id']}'";

        $balanceList = $this->DataControl->selectClear($sql);
        $balanceList = $balanceList ? $balanceList : array();

        $refundList = json_decode(stripslashes($request['refundList']), true);

        if ($list) {
            foreach ($list as $val) {

                $tem_withholdbalance = 0;

                $sql = "select s.student_id,s.student_cnname,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.coursebalance_unitexpend,ssc.coursebalance_unitrefund,sc.course_classnum,scf.courseforward_price,ssc.pricing_id,fpt.tuition_refundprice,sc.course_freenums,sc.course_cnname,sc.course_refundprice,ssc.coursebalance_unitearning,ssc.companies_id
              ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh
                left join smc_class_hour as ch ON ch.hour_id = sh.hour_id
                where sh.student_id=ssc.student_id and sh.class_id='{$val['class_id']}' and ch.hour_isfree =0) as num
              ,(select cp.pricinglog_buytimes from smc_student_coursebalance_pricinglog as cp where cp.student_id=ssc.student_id and cp.course_id=ssc.course_id order by pricinglog_id desc limit 0,1) as buynum
              from smc_student_coursebalance as ssc
              left join smc_student as s on s.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and scf.course_id=ssc.course_id
              left join smc_fee_pricing_tuition as fpt on fpt.pricing_id=ssc.pricing_id
              left join gmc_code_companies as cc on cc.companies_id=ssc.companies_id
              where sc.company_id='{$request['company_id']}' and ssc.student_id='{$request['student_id']}' and ssc.course_id='{$val['course_id']}' and ssc.school_id='{$request['school_id']}'
              limit 0,1
             ";
                $studentOne = $this->DataControl->selectOne($sql);

                $tem_balance = $studentOne['coursebalance_figure'];

                $balance += $studentOne['coursebalance_figure'];
                $forward += $studentOne['courseforward_price'];
                if ($studentOne['pricing_id'] > '0') {
                    if ($studentOne['num'] < $studentOne['course_freenums']) {
                        if ($studentOne['tuition_refundprice'] > 0) {

                            $refundprice += $studentOne['tuition_refundprice'];
                            $tem_data = array();
                            $tem_data['course_name'] = $studentOne['course_cnname'];
                            $tem_data['tuition_refundprice'] = $studentOne['tuition_refundprice'];
                            $refundArray[] = $tem_data;
                            $tem_balance -= $refundprice;
                            $tem_withholdbalance += $refundprice;
                        }
                    }

                } else {
                    if ($val['class_id'] > '0') {
                        $sql = "select (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_day>=ss.study_beginday and ch.hour_day<=ss.study_endday and ch.hour_day<='{$day}' and ch.hour_isfree='0' and ch.hour_ischecking='1') as num
              from smc_student_study as ss
              where ss.class_id='{$val['class_id']}' and ss.student_id='{$request['student_id']}'";
                        $hourOne = $this->DataControl->selectOne($sql);

                        $pricingOne = $this->getCoursePricing($val['course_id'], $request['company_id'], $request['school_id']);
                        if ($hourOne && $pricingOne) {
                            if ($hourOne['num'] < $studentOne['course_freenums']) {
                                $refundprice += $pricingOne['tuition_refundprice'];
                                $tem_data = array();
                                $tem_data['course_name'] = $studentOne['course_cnname'];
                                $tem_data['tuition_refundprice'] = $pricingOne['tuition_refundprice'];
                                $refundArray[] = $tem_data;
                                $tem_balance -= $refundprice;
                                $tem_withholdbalance += $refundprice;
                            }
                        }
                    }
                }


                if ($balanceList) {
                    $tem_balancelist = $balanceList;
                    $tem_balancelist = array_column($tem_balancelist, null, 'companies_id');
                    if ($tem_balancelist[$studentOne['companies_id']]) {
                        foreach ($balanceList as &$bOne) {
                            if ($studentOne['companies_id'] == $bOne['companies_id']) {
                                $bOne['student_balance'] += ($tem_balance > 0 ? $tem_balance : 0);
                                $bOne['student_withholdbalance'] += $tem_withholdbalance;
                            }
                        }
                    } else {
                        $data = array();
                        $data['company_id'] = $request['company_id'];
                        $data['school_id'] = $request['school_id'];
                        $data['student_id'] = $request['student_id'];
                        $data['companies_id'] = $studentOne['companies_id'];
                        $data['companies_cnname'] = '';  // 这里需要从companies表获取名称
                        $data['student_balance'] = ($tem_balance > 0 ? $tem_balance : 0);
                        $data['student_withholdbalance'] = $tem_withholdbalance;
                        $balanceList[] = $data;
                    }
                }

                if (isset($val['num']) && $val['num'] > 0) {
                    $courseforward_price = 0;
                    $coursebalance_figure = 0;

                    if ($studentOne['courseforward_price'] > 0) {
                        if ($studentOne['coursebalance_unitearning'] > 0 && $studentOne['coursebalance_unitexpend'] > $studentOne['coursebalance_unitearning']) {
                            $courseforward_price = $studentOne['courseforward_price'] - (($studentOne['coursebalance_time'] - $val['num']) * ($studentOne['coursebalance_unitexpend'] - $studentOne['coursebalance_unitearning']));
                        }
                    }

                    if ($studentOne['coursebalance_unitearning'] > 0) {
                        $coursebalance_figure = $studentOne['coursebalance_figure'] - (($studentOne['coursebalance_time'] - $val['num']) * $studentOne['coursebalance_unitearning']);
                    } else {
                        $coursebalance_figure = $studentOne['coursebalance_figure'] - (($studentOne['coursebalance_time'] - $val['num']) * $studentOne['coursebalance_unitexpend']);
                    }

                    $all_coursebalance_figure += $coursebalance_figure >= 0 ? $coursebalance_figure : 0;
                    $all_courseforward_price += $courseforward_price >= 0 ? $courseforward_price : 0;
                }

            }
        }

        if ($balanceList && $refundList) {
            foreach ($balanceList as &$one) {
                $one['student_balance'] = sprintf("%.2f", $one['student_balance']);
                foreach ($refundList as $refundOne) {
                    if ($one['companies_id'] == $refundOne['companies_id']) {
                        $one['refund_price'] = sprintf("%.2f", $refundOne['refund_price']);
                        $one['specialprice'] = sprintf("%.2f", $refundOne['specialprice']);
                        $one['refund_specialreason'] = $refundOne['refund_specialreason'];
                        $one['isSpecial'] = $refundOne['isSpecial'];
                    }
                }
            }
        }


        if ($balance - $refundprice < 0) {
            $refundprice = $balance;
        }

        $data = array();
        $data['balance'] = $balance - $refundprice;
        $data['forward'] = $forward;
        $data['refundprice'] = $refundprice;

        $data['list'] = $refundArray;

        if (!isset($request['refund_price']) && !isset($request['specialprice'])) {

            if ($all_coursebalance_figure > 0 || $all_courseforward_price > 0) {
                $data['all_balance'] = $stublcOne['student_balance'] + $all_coursebalance_figure + $all_courseforward_price;
            } else {
                $data['all_balance'] = $stublcOne['student_balance'] + $balance;
            }

        } else {
            if (isset($request['specialprice']) && $request['specialprice'] > 0) {
                $data['all_balance'] = ($request['refund_price'] + $request['specialprice']);
            } else {
                $data['all_balance'] = $request['refund_price'];
            }
        }

        $data['all_balance'] = $data['all_balance'] ? $data['all_balance'] : 0;


        if (($stublcOne['student_balance'] + $balance) < $request['refund_price'] || $request['refund_price'] < 0) {
            $this->error = true;
            $this->errortip = "请填写正确金额";
            return false;
        }

        $data['all_forward'] = $forward + $stublcOne['student_forwardprice'];
        $data['balanceList'] = $balanceList;
        return $data;

    }

    function checkForward($request)
    {
        header('Content-Type: text/html; charset=UTF-8');

        if($this->stafferOne['staffer_ismanage']==1){
            return true;
        }

        $list = json_decode(stripslashes($request['list']), true);
        if (!$list) {
            $this->error = true;
            $this->errortip = "请选择结转的课程";
            return false;
        }

        $str = '';
        foreach ($list as $val) {
            $sql = "select po.order_pid,sc.course_cnname
                      from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      left join smc_course as sc on sc.course_id=poc.course_id
                      where poc.course_id='{$val['course_id']}' 
                      and po.student_id='{$request['student_id']}' 
                      and po.school_id='{$this->school_id}' 
                      and po.order_arrearageprice>0 
                      and po.order_status>=0 and po.order_status<>4
                      ";

            $orderOne = $this->DataControl->selectOne($sql);
            if ($orderOne) {
                $str .= '、' . $orderOne['course_cnname'];
            }
        }
        if (strlen($str) > 0) {
            $this->error = true;
            $this->errortip = $this->LgStringSwitch(ltrim($str,'、'));
            return false;
        }

        return true;
    }

    function getWaitClass($request)
    {

        $sql = "select c.class_id,c.class_cnname,c.class_branch,sc.course_id,sc.course_cnname,sc.course_branch,ssc.coursebalance_figure,ssc.coursebalance_time,scf.courseforward_price
              from smc_student_study as sss
              left join smc_class as c on c.class_id=sss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_student_coursebalance as ssc on ssc.course_id=sc.course_id and ssc.student_id=sss.student_id
              left join smc_student_courseforward as scf on scf.course_id=sc.course_id and scf.student_id=sss.student_id
              left join smc_school_coursecat_subject as scs on scs.coursecat_id = sc.coursecat_id and scs.school_id = '{$request['school_id']}'
              left join gmc_code_companies as co on co.companies_id = scs.companies_id
              where sss.company_id='{$request['company_id']}' 
              and sss.school_id='{$request['school_id']}' 
              and sss.student_id='{$request['student_id']}' 
              and sss.study_isreading=1 
              and sc.course_inclasstype<>'1' 
              and co.companies_issupervise = '0'  
              and c.class_type='0'
              
              group by c.class_id
        ";
        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        return $classList;
    }

    function getStuSettleInfo($request)
    {
        $sql = "select sc.class_id,sc.courseshare_month,sc.courseshare_id,sc.courseshare_price
              ,ifnull((select cs.share_status from smc_student_class_share as cs where cs.courseshare_id=sc.courseshare_id limit 0,1),2) as share_status
              ,ifnull((select scb.coursebalance_figure from smc_student_coursebalance as scb where scb.coursebalance_id=sc.coursebalance_id limit 0,1),0) as coursebalance_figure
              from smc_student_courseshare as sc
              where sc.coursebalance_id='{$request['coursebalance_id']}' and sc.class_id='{$request['class_id']}' and sc.courseshare_status=0 ";

        $shareList = $this->DataControl->selectClear($sql);
        if (!$shareList) {
            $this->error = true;
            $this->errortip = "无期度数据";
            return false;
        }

        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $status = $this->LgArraySwitch(array('0' => '待处理', '1' => '已完成', '2' => '待结算'));

        foreach ($shareList as &$shareOne) {
            $shareOne['share_status_name'] = $status[$shareOne['share_status']];
            if ($shareOne['share_status'] == 2) {
                $bool = $BalanceModel->settlementInfo($request['student_id'], $shareOne['class_id'], $shareOne['courseshare_month']);

                if ($bool) {
                    $shareOne['attendanceNum'] = $bool['attendNum'];
                    $shareOne['hourNum'] = $bool['hourNum'];
                    $shareOne['absenceNum'] = $bool['absenceNum'];
                    $shareOne['price'] = $bool['settle_price'];
                    $shareOne['surplus_price'] = $bool['courseshare_price'] - $bool['settle_price'];

                    $shareOne['can_settle'] = 1;
                    $shareOne['tip'] = '';

                } else {
                    $shareOne['attendanceNum'] = '--';
                    $shareOne['hourNum'] = '--';
                    $shareOne['absenceNum'] = '--';
                    $shareOne['price'] = '--';
                    $shareOne['courseshare_price'] = '--';
                    $shareOne['surplus_price'] = '--';

                    $shareOne['can_settle'] = 0;
                    $shareOne['tip'] = $BalanceModel->errortip;
                }
            } else {
                $sql = "select * from smc_student_class_share as cs where cs.courseshare_id='{$shareOne['courseshare_id']}' limit 0,1";
                $one = $this->DataControl->selectOne($sql);
                if ($one) {
                    $shareOne['attendanceNum'] = $one['share_attend_times'];
                    $shareOne['absenceNum'] = $one['share_absent_times'];
                    $shareOne['hourNum'] = $one['share_attend_times'] + $one['share_absent_times'];
                    $shareOne['courseshare_price'] = $one['share_settle_price'];
                    if ($shareOne['share_status'] == 0) {
                        $shareOne['price'] = $one['share_apply_price'];
                    } else {
                        $shareOne['price'] = $one['share_confirm_price'];
                    }

                    $shareOne['surplus_price'] = $one['share_settle_price'] - $shareOne['price'];

                    $shareOne['can_settle'] = 0;
                    $shareOne['tip'] = $status[$shareOne['share_status']];
                } else {
                    $shareOne['attendanceNum'] = '--';
                    $shareOne['hourNum'] = '--';
                    $shareOne['absenceNum'] = '--';
                    $shareOne['price'] = '--';
                    $shareOne['surplus_price'] = '--';

                    $shareOne['can_settle'] = 0;
                    $shareOne['tip'] = '结算数据错误';
                }
            }
        }

        return $shareList;
    }

    function carryForwardPeriodicity($request)
    {

        $sql = "select co.course_monthlyset,cl.school_id,co.course_id 
              from smc_class as cl,smc_course as co 
              where cl.course_id=co.course_id and cl.class_id='{$request['class_id']}'";

        $courseOne = $this->DataControl->selectOne($sql);

        if (!$courseOne) {
            $this->error = true;
            $this->errortip = "无相关课程";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_student_class_share", "share_id", "student_id='{$request['student_id']}' and class_id='{$request['class_id']}' and share_status=0")) {
            $this->error = true;
            $this->errortip = "学生在该班级存在期度审核,不可再次结转";
            return false;
        }

        $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure", "school_id='{$this->school_id}' and student_id='{$request['student_id']}' and course_id='{$courseOne['course_id']}'");

        if (!$courseBalanceOne) {
            $this->error = true;
            $this->errortip = "无课程余额";
            return false;
        }

        $list = json_decode(stripslashes($request['list']), 1);

        $allPrice = 0;
        foreach ($list as $one) {
            $allPrice += $one['apply_price'];
        }

        if ($allPrice > $courseBalanceOne['coursebalance_figure']) {
            $this->error = true;
            $this->errortip = "结算金额不可大于课程剩余金额";
            return false;
        }


        if (!$list) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);

        foreach ($list as $listOne) {
            $settlementInfoOne = $BalanceModel->settlementInfo($request['student_id'], $request['class_id'], $listOne['month']);

            if ($settlementInfoOne) {

                $data = array();
                $data['company_id'] = $this->company_id;
                $data['school_id'] = $this->school_id;
                $data['student_id'] = $request['student_id'];
                $data['class_id'] = $request['class_id'];
                $data['courseshare_id'] = $settlementInfoOne['courseshare_id'];
                $data['share_month'] = $listOne['month'];
                $data['share_attend_times'] = $settlementInfoOne['attendNum'];
                $data['share_absent_times'] = $settlementInfoOne['absenceNum'];
                $data['share_calc_times'] = $settlementInfoOne['num'];
                $data['share_settle_price'] = $settlementInfoOne['courseshare_price'];
                $data['share_calc_price'] = $settlementInfoOne['settle_price'];

                $data['share_apply_price'] = $listOne['apply_price'];
                $data['share_apply_staffer_id'] = $this->stafferOne['staffer_id'];
                $data['share_from'] = 1;
                $data['share_apply_time'] = time();

                $data['share_status'] = 0;

//                if($listOne['apply_price']==$settlementInfoOne['settle_price']){
//                    $data['share_confirm_price']=$settlementInfoOne['settle_price'];
//                    $data['share_confirm_staffer_id']=$this->stafferOne['staffer_id'];
//                    $data['share_confirm_time']=time();
//                    $data['share_status']=1;
//                }

                if ($courseOne['course_monthlyset'] == 0) {
                    $data['share_confirm_price'] = $listOne['apply_price'];
                    $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                    $data['share_confirm_time'] = time();
                    $data['share_status'] = 1;
                } elseif ($courseOne['course_monthlyset'] == 1) {
                    if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
                        $data['share_confirm_price'] = $listOne['apply_price'];
                        $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
                        $data['share_confirm_time'] = time();
                        $data['share_status'] = 1;
                    }
                }

                $share_id = $this->DataControl->insertData("smc_student_class_share", $data);

                if ($courseOne['course_monthlyset'] == 0) {
                    $BalanceModel->settleAction($request['student_id'], $request['class_id'], $listOne['month'], $settlementInfoOne['courseshare_price'] - $listOne['apply_price'], 2, $listOne['apply_price']);
                } elseif ($courseOne['course_monthlyset'] == 1) {
                    if ($listOne['apply_price'] == $settlementInfoOne['settle_price']) {
                        $BalanceModel->settleAction($request['student_id'], $request['class_id'], $listOne['month'], $settlementInfoOne['courseshare_price'] - $listOne['apply_price'], 2, $listOne['apply_price']);
                    }
                }

                $data = array();
                $data['share_id'] = $share_id;
                $data['tracks_title'] = $this->LgStringSwitch('月度结算');
                $data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师提交结算信息');
                $data['tracks_note'] = $this->LgStringSwitch($listOne['tip']);
                $data['staffer_id'] = $this->stafferOne['staffer_id'];
                $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $data['tracks_time'] = time();

                $this->DataControl->insertData("smc_student_class_share_tracks", $data);
            }
        }

        if (isset($request['class_id']) && $request['class_id'] != '' && $request['class_id'] > 0) {
            $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
            $TransactionModel->outClass($request['student_id'], $request['class_id'], 0, strtotime($request['create_time']));
        }

        return true;

    }

    function waitClass($request)
    {

        $Model = new \Model\Smc\TransactionModel($request);

        $list = explode(",", $request['classIds']);
        if (isset($request['create_time']) && $request['create_time'] != '') {
            $day = date("Y-m-d", strtotime($request['create_time']));
        } else {
            $day = date("Y-m-d");
        }

        foreach ($list as $val) {

            $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$val}'");

            $a = $this->DataControl->selectClear("SELECT 
	cc.companies_issupervise
FROM
	smc_course AS c
	LEFT JOIN smc_school_coursecat_subject AS s ON c.coursecat_id = s.coursecat_id 
	AND school_id = '{$request['school_id']}'
	LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = s.companies_id
	where c.course_id = '{$classOne['course_id']}' and cc.companies_issupervise = '1'");

            if ($a) {
                $this->error = true;
                $this->errortip = "你选择的课程已被监管，不可延班！";
                return false;
            }

            $sql = "select coursebalance_figure from smc_student_coursebalance where student_id='{$request['student_id']}' and school_id='{$this->school_id}' and course_id='{$classOne['course_id']}'";
            $coursebalanceOne = $this->DataControl->selectOne($sql);

            if (!$coursebalanceOne || $coursebalanceOne['coursebalance_figure'] <= 0) {
                $this->error = true;
                $this->errortip = "学生无课程余额不可延班！";
                return false;
            }

            $sql = "select count(hour_id) as num from smc_class_hour where class_id='{$val}' and hour_ischecking=0";
            $hourOne = $this->DataControl->selectOne($sql);

            if ($hourOne['num'] <= 1) {
                $this->error = true;
                $this->errortip = "班级最后一节课不可延班！";
                return false;
            }

            $sql = "select hour_id from smc_class_hour where class_id='{$val}' and hour_day<'{$day}' and hour_ischecking=0 and hour_iswarming='0'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "选择班级存在考勤未完成！";
                return false;
            }
        }

        foreach ($list as $val) {

            $Model->waitClass($request['student_id'], $val, $request['staffer_id'], $reason_code = $request['reason_code'], $reason = $request['reason'], strtotime($request['create_time']));
        }

        return true;
    }

    function getAvailableCourse($request)
    {
        $datawhere = "sc.company_id='{$request['company_id']}' and ssc.student_id='{$request['student_id']}'
            and ssc.course_id not in (select c.course_id from smc_student_study as sss 
            left join smc_class as c on c.class_id=sss.class_id
            where sss.student_id=ssc.student_id 
            and ssc.course_id=c.course_id 
            and sss.study_isreading='1' 
            and c.class_type='0')";

        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and ssc.companies_id in(8,78606) ";
            $datawhere .= " and sc.coursecat_id in(133,141,11352) ";
        }

        $datawhere .= " and (ssc.coursebalance_time > 0 or ssc.coursebalance_figure > 0)";

        $sql = "select sc.course_id,sc.course_cnname,sc.course_branch,ssc.coursebalance_status,ssc.coursebalance_time,ssc.coursebalance_figure
              from smc_student_coursebalance as ssc
              left join smc_course as sc on sc.course_id=ssc.course_id
              where {$datawhere} and ssc.school_id='{$this->school_id}'";
        $courseList = $this->DataControl->selectClear($sql);
        if (!$courseList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $status = $this->LgArraySwitch(array("0" => "未入班", "1" => "已入班", "2" => "已延班", "3" => "已结转", "4" => "已结束"));
        foreach ($courseList as &$val) {
            $val['coursebalance_status'] = $status[$val['coursebalance_status']];
        }

        return $courseList;

    }

    function enterClass($request)
    {
        $Model = new \Model\Smc\TransactionModel($request);
        $BalanceModel = new \Model\Smc\BalanceModel($request);
//        $sql="select sc.stuchange_code
//              from smc_student_changelog as sc
//              where sc.class_id='{$request['class_id']}' and sc.student_id='{$request['student_id']}' and sc.school_id='{$request['school_id']}'
//              order by changelog_id desc
//              limit 0,1";
//        $one=$this->DataControl->selectOne($sql);
//        if($one['stuchange_code']=='A07'){
//            $this->error = true;
//            $this->errortip = "不可进入上次延班的班级";
//            return false;
//        }

        if (!$BalanceModel->checkStuTimes($request['student_id'], $request['class_id'], $request['starttime'])) {
            $this->error = true;
            $this->errortip = $BalanceModel->errortip;
            return false;
        }


        $bool = $Model->entryClass($request['student_id'], $request['course_id'], $request['class_id'], $request['starttime'], 0, strtotime($request['create_time']), '编班', '编班', 1);
        if ($bool) {
            $this->error = true;
            $this->oktip = "编班成功";
            return true;
        } else {
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }
    }

    function transferCourse($request)
    {

        $a = $this->DataControl->selectOne("
                SELECT
                    p.post_istopjob
                FROM
                    gmc_staffer_postbe AS sp
                LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
                WHERE sp.postbe_id = '{$request['re_postbe_id']}'");

        if ($a['post_istopjob'] != '1') {
            $sql = "select po.order_pid
                      from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      where poc.course_id='{$request['course_id']}' and po.student_id='{$request['student_id']}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
                      and po.order_status>=0
                      ";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "课程存在欠费,不可结转";
                return false;
            }
        }

        $sql = "select scb.coursebalance_figure,scb.coursebalance_time
              from smc_student_coursebalance as scb
              where scb.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and scb.course_id='{$request['to_course_id']}'
              and (scb.coursebalance_figure>'0' or scb.coursebalance_time>'0')
              ";

        $stuCourseOne = $this->DataControl->selectOne($sql);


        if ($stuCourseOne) {
            $this->error = true;
            $this->errortip = "学员存在课程余额,不可结转";
            return false;
        }

        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$request['course_id']}'");
        if ($courseOne['course_inclasstype'] == '1') {
            $this->error = true;
            $this->errortip = "期度类课程,不可结转";
            return false;
        }

        $sql = "select scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price,scf.courseforward_deductionmethod,scb.companies_id
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.student_id=scb.student_id and scf.course_id=scb.course_id
              where scb.student_id='{$request['student_id']}' and scb.course_id='{$request['course_id']}' and scb.school_id='{$this->school_id}'";
        $coursebalance = $this->DataControl->selectOne($sql);

        $companiesOne = $this->getSchoolCourseCompanies($request['school_id'], 0, $request['to_course_id']);

        if ($coursebalance['companies_id'] != $companiesOne['companies_id']) {
            $this->error = true;
            $this->errortip = "主体变化,不可结转,请使用结转课程";
            return false;
        }


        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $dealorder_pid = $BalanceModel->transferCourse($request['student_id'], $request['course_id'], $request['to_course_id'], $request['pricing_id'], $request['class_id'], $request['to_class_id'], $request['change_reason'], strtotime($request['create_time']));

        return $dealorder_pid;
    }

    function getDealInfo($request)
    {
        $sql = "select s.student_cnname,s.student_branch,s.student_enname,fd.dealorder_type,sc.course_cnname as old_course_cnname,c.course_cnname as new_course_cnname,fd.dealorder_createtime,dc.dealcourse_time
              from smc_forward_dealorder as fd
              left join smc_forward_dealorder_course as dc on fd.dealorder_pid=dc.dealorder_pid
              left join smc_student as s on s.student_id=fd.student_id
              left join smc_course as sc on sc.course_id=dc.course_id
              left join smc_fee_pricing as fp on fp.pricing_id=dc.dealcourse_topricing_id
              left join smc_course as c on c.course_id=fp.course_id
              where fd.dealorder_pid='{$request['dealorder_pid']}'
              ";
        $dealOne = $this->DataControl->selectOne($sql);

        if ($dealOne) {
            $dealOne['dealorder_createtime'] = date("Y-m-d H:i:s", $dealOne['dealorder_createtime']);
        } else {
            $dealOne = array();
        }
        return $dealOne;

    }

    function forwardClassStu($request)
    {

        $sql = "select ch.hour_id 
                from smc_class_hour as ch 
                where ch.class_id='{$request['class_id']}' and ch.hour_isfree=0 and ch.hour_ischecking=0
                ";
        $hourList = $this->DataControl->selectClear($sql);

        $hourNum = $hourList ? count($hourList) : 0;


        $sql = "select scb.coursebalance_time,cl.course_id 
                from smc_student_study as ss
                left join smc_class as cl on cl.class_id=ss.class_id
                left join smc_student_coursebalance as scb on scb.student_id=ss.student_id and scb.course_id=cl.course_id and scb.school_id='{$this->school_id}'
                where ss.student_id='{$request['student_id']}' and ss.class_id='{$request['class_id']}' and ss.study_isreading=1
                limit 0,1
                ";

        $stuBalanceOne = $this->DataControl->selectOne($sql);

        if (!$stuBalanceOne) {
            $this->error = true;
            $this->errortip = "该学生不在该班";
            return false;
        }

        if ($stuBalanceOne['coursebalance_time'] <= $hourNum) {
            $this->error = true;
            $this->errortip = "该学生不需要结转";
            return false;
        }

        $data = array();
        $data['course_id'] = $stuBalanceOne['course_id'];
        $data['class_id'] = $request['class_id'];
        $data['num'] = $stuBalanceOne['coursebalance_time'] - $hourNum;
        $tem_data[] = $data;

        $request['change_reason'] = $this->LgStringSwitch('班级批量结转学员多余课次');
        $request['list'] = json_encode($tem_data, JSON_UNESCAPED_UNICODE);

        $bool = $this->carryForward($request);

        if ($bool) {
            return true;
        } else {
            return false;
        }

    }

    function getStuSubCourseInfo($request){

        if(!isset($request['student_id']) || !isset($request['course_id'])){
            $this->error = true;
            $this->errortip = "学生ID和课程ID必须传入";
            return false;
        }


        $sql="SELECT a.coursebalance_id,a.course_id,b.course_cnname,b.course_branch,a.coursebalance_figure,a.coursebalance_time,ifnull(c.courseforward_price,0) as courseforward_price,b.coursecat_id,b.course_inclasstype,b.course_isforward_examine
              ,(select cp.pricinglog_refunddeadline from smc_student_coursebalance_pricinglog as cp where cp.student_id=a.student_id and cp.school_id=a.school_id and cp.course_id=a.course_id and cp.pricing_id=a.pricing_id order by pricinglog_id desc limit 0,1) as refunddeadline
              ,(select SUM(poc.ordercourse_buynums) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=a.course_id and po.student_id=a.student_id and po.school_id=a.school_id and po.order_status>0 limit 0,1) as ordercourse_buynums
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=a.student_id and sf.course_id=a.course_id) as allNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=a.student_id and sf.course_id=a.course_id and sf.is_use=0) as freeNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=a.student_id and sf.course_id=a.course_id and sf.is_use=1) as useNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=a.student_id and sf.course_id=a.course_id and sf.is_use='-1') as cancelNum
              ,ifnull((select z.coursepacks_id from smc_payfee_order as x,smc_payfee_order_course as y,smc_payfee_mergeorder z where y.order_pid=x.order_pid and x.mergeorder_pid=z.mergeorder_pid and x.school_id=a.school_id and x.student_id=a.student_id and y.course_id=a.course_id and x.order_status>0 order by x.order_id desc limit 0,1),'') as mergeorder_pid
              ,ifnull((select x.order_status from smc_payfee_order as x,smc_payfee_order_course as y where y.order_pid=x.order_pid and x.school_id=a.school_id and x.student_id=a.student_id and y.course_id=a.course_id and x.order_status>0 order by x.order_id desc limit 0,1),'') as order_status,b.course_isfollow,b.main_course_id
              from smc_student_coursebalance as a
              inner join smc_course as b on b.course_id=a.course_id
              left join smc_student_courseforward as c on c.student_id=a.student_id and c.course_id=a.course_id
              where a.student_id='{$request['student_id']}' and b.main_course_id='{$request['course_id']}' and a.school_id='{$this->school_id}' and a.coursebalance_figure>0";
        $stuCourseOne=$this->DataControl->selectOne($sql);

        return $stuCourseOne?$stuCourseOne:array();
    }

    function applicateForward($request){

        $sql="select application_id from smc_forward_application where student_id='{$request['student_id']}' and course_id='{$request['course_id']}' and class_id='{$request['class_id']}' and application_status=0";
        $applicationOne=$this->DataControl->selectOne($sql);
        if($applicationOne){
            $this->error = true;
            $this->errortip = "存在未审核的结转申请，不可重复申请";
            return false;
        }

        $applicationInfo=$this->createForwardApplication($request);

        if($applicationInfo){
            return $applicationInfo;
        }else{
            return false;
        }


    }




    function getApplicationList($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.student_cnname like '%{$request['keyword']}%' or b.student_enname like '%{$request['keyword']}%' or b.student_idcard like '%{$request['keyword']}%' or b.student_branch like '%{$request['keyword']}%')
            ";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(a.apply_time,'%Y-%m-%d')>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and  FROM_UNIXTIME(a.apply_time,'%Y-%m-%d')<='{$request['endtime']}'";
        }

        if (isset($request['application_status']) && $request['application_status'] != '') {
            $datawhere .= " and a.application_status='{$request['application_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT a.application_id,a.student_id,b.student_cnname,b.student_branch,c.course_cnname,c.course_branch,d.class_cnname,d.class_branch,e.staffer_cnname,a.application_type,a.out_class_date,a.back_class_date,a.forward_reason,a.attachment_url,a.application_status,a.apply_time,a.approval_note,a.remaining_amount,a.forward_amount
                ,ifnull((select x.course_name from smc_forward_application_course as x where x.application_id=a.application_id and x.course_type=2),'--') as sub_course_name
                from smc_forward_application as a
                inner join smc_student as b on b.student_id=a.student_id
                inner join smc_course as c on c.course_id=a.course_id
                inner join smc_class as d on d.class_id=a.class_id
                inner join smc_staffer as e on e.staffer_id=a.apply_staffer_id
                where {$datawhere}
                order by a.application_id DESC
                limit {$pagestart},{$num}
        ";

        $applicationList = $this->DataControl->selectClear($sql);

        if (!$applicationList) {
            $this->error = true;
            $this->errortip = "无课程结转申请";
            return false;
        }

        $application_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '审核通过', '2' => '审核拒绝', '3' => '已取消', '4' => '已完成'));
        $application_type = $this->LgArraySwitch(array('1' => '申请退费', '2' => '申请转班', '3' => '申请暑期长期保留', '4' => '申请拆并班'));
        foreach ($applicationList as &$val) {
            $val['apply_time'] = date("Y-m-d", $val['apply_time']);
            $val['application_status_name'] = $application_status[$val['application_status']];
            $val['application_type_name'] = $application_type[$val['application_type']];
            $val['attachment_url_name'] = $val['attachment_url']!=''?$this->LgStringSwitch('查看附件'):'';
        }

        $count_sql = "select a.application_id
                from smc_forward_application as a
                inner join smc_student as b on b.student_id=a.student_id
                where {$datawhere}
                ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
        $data['allnum'] = $allnum;
        $data['list'] = $applicationList;
        return $data;

    }

    function cancelApplication($request){

        $applicationOne=$this->DataControl->selectOne("select application_id,application_status,requestid from smc_forward_application where application_id='{$request['application_id']}' and school_id='{$this->school_id}'");
        if(!$applicationOne){
            $this->error = true;
            $this->errortip = "结转申请不存在";
            return false;
        }

        if($applicationOne['application_status']!=0){
            $this->error = true;
            $this->errortip = "结转申请状态不正确";
            return false;
        }

        $FanweiModel=new \Model\Smc\FanweiModel();
        $FanweiModel->cancelApplication($applicationOne['requestid']);

        $data = array();
        $data['application_status'] = 3;
        $data['cancel_reason'] = '手动取消';
        $data['cancel_time'] = time();
        $data['update_time'] = time();

        if($this->DataControl->updateData("smc_forward_application","application_id='{$request['application_id']}'",$data)){
            $this->createApplicationTrack($request['application_id'], '取消申请', '用户取消结转申请', 
                '', $this->stafferOne['staffer_id'], 'cancel');

            $this->createApplicationTask($request['application_id'], 'cancel', time());

            
            return true;
        }else{
            $this->error = true;
            $this->errortip = "取消失败";
            return false;
        }

    }

    /**
     * 结转申请模型
     * 
     * ======================================================================
     * 返回值结构说明 - getPreForwardInfo() 方法
     * ======================================================================
     * 
     * 该方法返回学员课程结转预测信息的核心数据（已简化，只包含参与计算的重要金额）：
     * 
     * 重要平衡关系：
     * - current_balance = to_forward_amount + forward_amount
     * - current_times = to_forward_times + forward_times
     * 
     * 业务逻辑：
     * 1. 只结转指定日期之前的收费未考勤课程
     * 2. 已考勤课程不参与结转，但需要补差价（退费单价 - 收入单价）
     * 3. 免费课程不参与结转
     * 4. 指定日期之后的课程不参与结转
     * 5. 补差价加到结转金额中（学生需要补这个差价）
     * 6. 已考勤课程已消耗，不算作剩余课次
     * 7. 平衡关系：current_times = to_forward_times + forward_times
     * 8. 平衡关系：current_balance = to_forward_amount + forward_amount
     * 
     * {
     *     "student_id": "学员ID",
     *     "forward_date": "结转日期 (YYYY-MM-DD)",
     *     "forward_type": "结转类型 (1:全部结转 2:部分结转)",
     *     "forward_times": "结转课次数",
     *     "can_forward": "是否可以结转 (true/false)",
     *     "main_course": {
     *         "course_id": "课程ID",
     *         "course_name": "课程名称",
     *         "course_branch": "课程编号",
     *         "current_balance": "当前余额 = to_forward_amount + forward_amount",
     *         "current_times": "当前有效课次（排除已考勤）= to_forward_times + forward_times",
     *         "to_forward_amount": "需要结转的金额（包括补差价）",
     *         "to_forward_times": "需要结转的课次（指定日期之前的收费未考勤课次）",
     *         "forward_amount": "结转完成后剩余的金额",
     *         "forward_times": "结转完成后剩余的课次（免费+指定日期之后）",
     *         "deduction_amount": "已考勤课次的补差价（退费单价 - 收入单价）",
     *         "unit_refund": "退费单价"
     *     },
     *     "binding_courses": [
     *         {
     *             "course_id": "绑定课程ID",
     *             "course_name": "绑定课程名称", 
     *             "course_branch": "绑定课程编号",
     *             "current_balance": "当前余额 = to_forward_amount + forward_amount",
     *             "current_times": "当前有效课次（排除已考勤）= to_forward_times + forward_times",
     *             "to_forward_amount": "需要结转的金额（包括补差价）",
     *             "to_forward_times": "需要结转的课次（指定日期之前的收费未考勤课次）",
     *             "forward_amount": "结转完成后剩余的金额",
     *             "forward_times": "结转完成后剩余的课次（免费+指定日期之后）",
     *             "deduction_amount": "已考勤课次的补差价（退费单价 - 收入单价）",
     *             "unit_refund": "退费单价"
     *         }
     *     ],
     *     "summary": {
     *         "total_current_balance": "总的当前余额 = total_to_forward_amount + total_forward_amount",
     *         "total_current_times": "总的当前有效课次（排除已考勤）= total_to_forward_times + total_forward_times",
     *         "total_to_forward_amount": "总的需要结转金额（包括补差价）",
     *         "total_to_forward_times": "总的需要结转课次（指定日期之前的收费未考勤课次）",
     *         "total_forward_amount": "总的结转后剩余金额",
     *         "total_forward_times": "总的结转后剩余课次（免费+指定日期之后）",
     *         "total_deduction_amount": "总的补差价金额"
     *     }
     * }
     * 
     * ======================================================================
     */
    function getPreForwardInfo($request)
    {
        // 1. 参数验证
        if (!$this->validateRequestParams($request)) {
            return false;
        }

        $student_id = $request['student_id'];
        $course_id = $request['course_id'];
        $forward_date = (isset($request['forward_date']) && !empty($request['forward_date'])) ? $request['forward_date'] : date('Y-m-d');
        $forward_type = (isset($request['forward_type']) && !empty($request['forward_type'])) ? $request['forward_type'] : 1; // 1-全部结转，2-部分结转
        $forward_times = (isset($request['forward_times']) && !empty($request['forward_times'])) ? intval($request['forward_times']) : 0;

        // 2. 基础数据验证
        if (!$this->validateBasicData($student_id, $course_id)) {
            return false;
        }

        // 3. 业务规则验证
        if (!$this->validateBusinessRules($student_id, $course_id, $forward_date)) {
            return false;
        }

        // 4. 获取主课程结转信息
        $mainCourseInfo = $this->getMainCourseForwardInfo($student_id, $course_id, $forward_date, $forward_type, $forward_times);
        if (!$mainCourseInfo) {
            return false;
        }
        // 5. 获取绑定课程结转信息（基于主课程的消耗情况）
        $bindingCourseInfo = $this->getBindingCoursesForwardInfo($student_id, $course_id, $forward_type, $forward_times, $mainCourseInfo);

        // 6. 计算汇总信息
        $summaryInfo = $this->calculateSummaryInfo($mainCourseInfo, $bindingCourseInfo);

        // 7. 组装返回结果
        $result = array(
            'student_id' => $student_id,
            'forward_date' => $forward_date,
            'forward_type' => $forward_type,
            'forward_times' => $forward_times,
            'can_forward' => true,
            'main_course' => $mainCourseInfo,
            'binding_course' => $bindingCourseInfo,
            'summary' => $summaryInfo
        );
        
        return $result;
    }

    /**
     * 验证请求参数
     */
    private function validateRequestParams($request)
    {
        if (!isset($request['student_id']) || empty($request['student_id'])) {
            $this->error = true;
            $this->errortip = "学生ID必须传入";
            return false;
        }

        if (!isset($request['course_id']) || empty($request['course_id'])) {
            $this->error = true;
            $this->errortip = "课程ID必须传入";
            return false;
        }

        // 验证结转日期格式
        if (isset($request['forward_date']) && !empty($request['forward_date'])) {
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $request['forward_date'])) {
                $this->error = true;
                $this->errortip = "结转日期格式错误，请使用YYYY-MM-DD格式";
                return false;
            }
        }

        // 验证结转类型
        if (isset($request['forward_type']) && !in_array($request['forward_type'], [1, 2])) {
            $this->error = true;
            $this->errortip = "结转类型参数错误，1-全部结转，2-部分结转";
            return false;
        }

        // 验证结转课次数
        if (isset($request['forward_times'])) {
            if ($request['forward_times'] < 0) {
                $this->error = true;
                $this->errortip = "结转课次数不能为负数";
                return false;
            }
            
            // 如果是部分结转，需要验证课次数量是否合理
            if (isset($request['forward_type']) && $request['forward_type'] == 2 && $request['forward_times'] == 0) {
                $this->error = true;
                $this->errortip = "部分结转时，结转课次数必须大于0";
                return false;
            }
        }

        return true;
    }

    /**
     * 验证基础数据
     */
    private function validateBasicData($student_id, $course_id)
    {
        // 验证课程信息
        $courseInfo = $this->getCourseInfo($course_id);
        if (!$courseInfo) {
            $this->error = true;
            $this->errortip = "课程信息不存在";
            return false;
        }

        // 验证学生课程余额信息
        $balanceInfo = $this->getStudentCourseBalance($student_id, $course_id);
        if (!$balanceInfo) {
            $this->error = true;
            $this->errortip = "学生课程余额信息不存在";
            return false;
        }

        // 验证学生当前班级信息
        $classInfo = $this->getStudentCurrentClass($student_id, $course_id);
        if (!$classInfo) {
            $this->error = true;
            $this->errortip = "学生当前未在该课程的班级中，无法进行结转";
            return false;
        }

        return true;
    }

    /**
     * 验证业务规则
     */
    private function validateBusinessRules($student_id, $course_id, $forward_date)
    {
        // 1. 验证学生是否在班且班级状态正常
        $classInfo = $this->getStudentCurrentClass($student_id, $course_id);
        if ($classInfo['study_isreading'] != '1') {
            $this->error = true;
            $this->errortip = "学生当前未在班级中（已退班），无法进行结转";
            return false;
        }

        if ($classInfo['class_type'] != '0') {
            $this->error = true;
            $this->errortip = "学生当前不在正常班级中，无法进行结转";
            return false;
        }

        // 2. 检查课程是否允许结转
        $courseInfo = $this->getCourseInfo($course_id);
        if ($courseInfo['course_isforward'] != 1) {
            $this->error = true;
            $this->errortip = "该课程不允许结转";
            return false;
        }

        // 3. 检查是否存在未处理的结转申请
        $existingApplication = $this->DataControl->selectOne("
            SELECT application_id FROM smc_forward_application 
            WHERE student_id = '{$student_id}' AND course_id = '{$course_id}' 
            AND school_id = '{$this->school_id}' AND application_status = 0
        ");
        if ($existingApplication) {
            $this->error = true;
            $this->errortip = "存在未审核的结转申请，不可重复申请";
            return false;
        }

        // 4. 检查是否存在欠费订单（非管理员）
        if ($this->stafferOne['staffer_ismanage'] != 1) {
            $arrearageOrder = $this->DataControl->selectOne("
                SELECT po.order_pid 
                FROM smc_payfee_order po
                LEFT JOIN smc_payfee_order_course poc ON po.order_pid = poc.order_pid
                WHERE poc.course_id = '{$course_id}' AND po.student_id = '{$student_id}' 
                AND po.school_id = '{$this->school_id}' AND po.order_arrearageprice > 0
                AND po.order_status >= 0 AND po.order_status <> 4
            ");
            if ($arrearageOrder) {
                $this->error = true;
                $this->errortip = "课程存在欠费，不可结转";
                return false;
            }
        }

        // 5. 验证结转日期不能早于今天
        if (strtotime($forward_date) < strtotime(date('Y-m-d'))) {
            $this->error = true;
            $this->errortip = "结转日期不能早于今天";
            return false;
        }
        
        // 6. 验证学生课程余额和课次
        $balanceInfo = $this->getStudentCourseBalance($student_id, $course_id);
        if (!$balanceInfo || ($balanceInfo['coursebalance_figure'] <= 0 && $balanceInfo['coursebalance_time'] <= 0)) {
            $this->error = true;
            $this->errortip = "学生在该课程中没有可结转的余额或课次";
            return false;
        }

        return true;
    }

    /**
     * 获取主课程结转信息
     */
    private function getMainCourseForwardInfo($student_id, $course_id, $forward_date, $forward_type, $forward_times)
    {
        $courseInfo = $this->getCourseInfo($course_id);
        $balanceInfo = $this->getStudentCourseBalance($student_id, $course_id);
        $forwardPriceInfo = $this->getForwardPriceInfo($student_id, $course_id);

        // 计算结转信息
        $forwardInfo = $this->calculateForwardInfo($balanceInfo, $forwardPriceInfo, $forward_type, $forward_times, $student_id, $course_id, $forward_date);
        
        if (!$forwardInfo) {
            $this->error = true;
            $this->errortip = "主课程结转信息计算失败";
            return false;
        }

        // 特殊逻辑：如果剩余金额是0，说明全部结转，课次也应该全部结转
        if ($forwardInfo['forward_amount'] <= 0) {
            $forwardInfo['forward_amount'] = 0;
            $forwardInfo['forward_times'] = 0;
            $forwardInfo['to_forward_times'] = $forwardInfo['current_times'];
        }

        return array(
            'course_id' => $course_id,
            'course_name' => $courseInfo['course_cnname'],
            'course_branch' => $courseInfo['course_branch'],
            'current_balance' => $forwardInfo['current_balance'],
            'current_times' => $forwardInfo['current_times'],
            'consume_times' => $forwardInfo['consume_times'],
            'to_forward_amount' => $forwardInfo['to_forward_amount'],
            'to_forward_times' => $forwardInfo['to_forward_times'],
            'forward_amount' => $forwardInfo['forward_amount'],      // 结转后剩余金额（即预估剩余）
            'forward_times' => $forwardInfo['forward_times'],        // 结转后剩余课次（即预估剩余）
            'deduction_amount' => $forwardInfo['deduction_amount'],  // 已考勤课次的补差价
            'unit_refund' => $forwardInfo['unit_refund']
        );
    }

    /**
     * 获取绑定课程结转信息
     */
    private function getBindingCoursesForwardInfo($student_id, $course_id, $forward_type, $forward_times, $mainCourseInfo)
    {
        $bindingCoursesOne = $this->getBindingCourses($course_id);

        if ($bindingCoursesOne) {
            return $this->processBindingCourse($student_id, $bindingCoursesOne['course_id'], $forward_type, $forward_times, $mainCourseInfo);
        }

        return null;
    }

    /**
     * 处理单个绑定课程
     */
    private function processBindingCourse($student_id, $binding_course_id, $forward_type, $forward_times, $mainCourseInfo)
    {
        // 获取绑定课程基础信息
        $bindingBalance = $this->getStudentCourseBalance($student_id, $binding_course_id);
        if (!$bindingBalance) {
            return null; // 没有余额，跳过
        }

        $bindingCourse = $this->getCourseInfo($binding_course_id);
        if (!$bindingCourse) {
            return null; // 课程信息不存在，跳过
        }

        // 检查绑定课程是否允许结转
        if ($bindingCourse['course_isforward'] != 1) {
            return null; // 不允许结转，跳过
        }

        // 计算绑定课程结转信息（基于主课程的消耗逻辑，但使用绑定课程的价格）
        $bindingForwardInfo = $this->calculateBindingCourseForwardInfo($bindingBalance, $mainCourseInfo, $forward_type, $forward_times);
        
        if (!$bindingForwardInfo) {
            // 添加调试信息
            error_log("绑定课程计算失败，绑定课程ID: {$binding_course_id}");
            return null; // 计算失败，跳过
        }

        // 特殊逻辑：如果剩余金额是0，说明全部结转，课次也应该全部结转
        if ($bindingForwardInfo['forward_amount'] <= 0) {
            $bindingForwardInfo['forward_amount'] = 0;
            $bindingForwardInfo['forward_times'] = 0;
            $bindingForwardInfo['to_forward_times'] = $bindingForwardInfo['current_times'];
        }

        return array(
            'course_id' => $binding_course_id,
            'course_name' => $bindingCourse['course_cnname'],
            'course_branch' => $bindingCourse['course_branch'],
            'current_balance' => $bindingForwardInfo['current_balance'],
            'current_times' => $bindingForwardInfo['current_times'],
            'consume_times' => $bindingForwardInfo['consume_times'],
            'to_forward_amount' => $bindingForwardInfo['to_forward_amount'],
            'to_forward_times' => $bindingForwardInfo['to_forward_times'],
            'forward_amount' => $bindingForwardInfo['forward_amount'],      // 结转后剩余金额（即预估剩余）
            'forward_times' => $bindingForwardInfo['forward_times'],        // 结转后剩余课次（即预估剩余）
            'deduction_amount' => $bindingForwardInfo['deduction_amount'],  // 已考勤课次的补差价
            'unit_refund' => $bindingForwardInfo['unit_refund']
        );
    }

    /**
     * 计算绑定课程结转信息
     * 绑定课程使用主课程的消耗比例，但用自己的金额和价格计算
     */
    private function calculateBindingCourseForwardInfo($bindingBalance, $mainCourseInfo, $forward_type, $forward_times)
    {
        // 当前余额和课次
        $current_balance = floatval($bindingBalance['coursebalance_figure']);
        $current_times = intval($bindingBalance['coursebalance_time']);
        $unit_expend = floatval($bindingBalance['coursebalance_unitexpend']);
        $unit_earning = floatval($bindingBalance['coursebalance_unitearning']);
        $unit_refund = floatval($bindingBalance['coursebalance_unitrefund']);
        
        // 绑定课程通常没有courseforward_price，设为0
        $courseforward_price = 0;
        
        // 绑定课程（随堂课程）与主课程在同一堂课中进行，消耗的课次应该完全相同
        // 直接使用主课程的消耗课次数，而不是按比例计算
        $main_consume_times = isset($mainCourseInfo['consume_times']) ? intval($mainCourseInfo['consume_times']) : 0;
        
        // 绑定课程消耗的课次数与主课程相同，但不能超过绑定课程的当前课次
        $consume_times = min($main_consume_times, $current_times);
        $estimated_times = max(0, $current_times - $consume_times);
        
        // 调试信息：确保消耗课次正确传递
        if ($main_consume_times > 0) {
            error_log("绑定课程消耗计算 - 主课程消耗: {$main_consume_times}, 绑定课程当前课次: {$current_times}, 最终消耗: {$consume_times}");
        }
        
        // 计算预估剩余余额
        $consume_unit_price = ($unit_earning > 0) ? $unit_earning : $unit_expend;
        $consume_amount = $consume_times * $consume_unit_price;
        $estimated_balance = max(0, $current_balance - $consume_amount);
        
        // 基于预估的剩余余额和课次计算结转金额
        $to_forward_amount = 0;
        $to_forward_times = 0;
        
        if ($forward_type == 1) {
            // 全部结转：使用预估的剩余余额
            $to_forward_amount = $estimated_balance;
            $to_forward_times = $estimated_times;
        } else if ($forward_type == 2 && $forward_times > 0) {
            // 部分结转：基于预估剩余进行计算
            if ($estimated_times <= 0) {
                // 如果预估剩余课次为0或负数，无法进行部分结转
                $to_forward_times = 0;
                $to_forward_amount = 0;
            } else {
                $to_forward_times = min($forward_times, $estimated_times);
                
                // 计算课程余额部分
                $forward_coursebalance_figure = 0;
                if ($unit_earning > 0) {
                    // 使用收益单价计算
                    $forward_coursebalance_figure = $estimated_balance - (($estimated_times - $to_forward_times) * $unit_earning);
                } else {
                    // 使用支出单价计算
                    $forward_coursebalance_figure = $estimated_balance - (($estimated_times - $to_forward_times) * $unit_expend);
                }
                
                $to_forward_amount = max(0, $forward_coursebalance_figure);
            }
        }
        
        // 确保结转金额不超过总可用金额
        if ($to_forward_amount > $estimated_balance) {
            $to_forward_amount = $estimated_balance;
        }
        
        // 确保结转金额不为负数
        $to_forward_amount = max(0, $to_forward_amount);
        $to_forward_times = max(0, $to_forward_times);
        
        // 计算结转后的剩余信息
        $forward_times = max(0, $estimated_times - $to_forward_times);

        // 计算剩余金额：参考 carryOver 的逻辑
        if ($forward_type == 1) {
            // 全部结转，剩余金额为0
            $forward_amount = 0;
        } else {
            // 部分结转，剩余金额 = 剩余课次对应的金额
            if ($estimated_times > 0 && $forward_times > 0) {
                $unit_price = ($unit_earning > 0) ? $unit_earning : $unit_expend;
                $forward_amount = $forward_times * $unit_price;
            } else {
                $forward_amount = 0;
            }
        }
        
        return array(
            'current_balance' => $current_balance,
            'current_times' => $current_times,
            'estimated_balance' => $estimated_balance,
            'estimated_times' => $estimated_times,
            'consume_times' => $consume_times,
            'to_forward_amount' => $to_forward_amount,
            'to_forward_times' => $to_forward_times,
            'forward_amount' => $estimated_balance,
            'forward_times' => $estimated_times,
            'deduction_amount' => 0,
            'unit_refund' => $unit_refund
        );
    }

    /**
     * 计算汇总信息
     */
    private function calculateSummaryInfo($mainCourseInfo, $bindingCourseInfo)
    {
        // 累计所有课程的核心信息
        $totalCurrentBalance = $mainCourseInfo['current_balance'];
        $totalCurrentTimes = $mainCourseInfo['current_times'];
        $totalConsumeTimes = $mainCourseInfo['consume_times'];
        $totalToForwardAmount = $mainCourseInfo['to_forward_amount'];
        $totalToForwardTimes = $mainCourseInfo['to_forward_times'];
        $totalForwardAmount = $mainCourseInfo['forward_amount'];
        $totalForwardTimes = $mainCourseInfo['forward_times'];
        $totalDeductionAmount = $mainCourseInfo['deduction_amount'];

        // 累计绑定课程信息（如果存在）
        if ($bindingCourseInfo) {
            $totalCurrentBalance += $bindingCourseInfo['current_balance'];
            $totalCurrentTimes += $bindingCourseInfo['current_times'];
            $totalConsumeTimes += $bindingCourseInfo['consume_times'];
            $totalToForwardAmount += $bindingCourseInfo['to_forward_amount'];
            $totalToForwardTimes += $bindingCourseInfo['to_forward_times'];
            $totalForwardAmount += $bindingCourseInfo['forward_amount'];
            $totalForwardTimes += $bindingCourseInfo['forward_times'];
            $totalDeductionAmount += $bindingCourseInfo['deduction_amount'];
        }

        return array(
            'total_current_balance' => $totalCurrentBalance,        // 总的当前余额
            'total_current_times' => $totalCurrentTimes,            // 总的当前课次
            'total_consume_times' => $totalConsumeTimes,            // 总的消耗课次
            'total_to_forward_amount' => $totalToForwardAmount,     // 总的需要结转金额
            'total_to_forward_times' => $totalToForwardTimes,       // 总的需要结转课次
            'total_forward_amount' => $totalForwardAmount,          // 总的结转后剩余金额（即预估剩余）
            'total_forward_times' => $totalForwardTimes,            // 总的结转后剩余课次（即预估剩余）
            'total_deduction_amount' => $totalDeductionAmount       // 总的补差价金额（当前为0）
        );
    }

    /**
     * 获取课程基本信息
     */
    private function getCourseInfo($course_id)
    {
        $sql = "SELECT c.course_id, c.course_cnname, c.course_branch, c.course_inclasstype, 
                       c.course_isforward, c.course_isforward_examine, c.course_freenums,
                       c.course_refundprice, c.coursecat_id, c.main_course_id, c.course_isfollow
                FROM smc_course c 
                WHERE c.course_id = '{$course_id}'";
        
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 获取学生课程余额信息
     */
    private function getStudentCourseBalance($student_id, $course_id)
    {
        $sql = "SELECT scb.coursebalance_id, scb.course_id, scb.student_id, scb.school_id,
                       scb.coursebalance_figure, scb.coursebalance_time, scb.coursebalance_unitexpend,
                       scb.coursebalance_unitrefund, scb.coursebalance_unitearning, scb.pricing_id,
                       scb.companies_id, scb.coursebalance_status
                FROM smc_student_coursebalance scb
                WHERE scb.student_id = '{$student_id}' AND scb.course_id = '{$course_id}' 
                AND scb.school_id = '{$this->school_id}'";
        
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 获取学生当前班级信息
     */
    private function getStudentCurrentClass($student_id, $course_id)
    {
        $sql = "SELECT c.class_id, c.class_cnname, c.class_branch, c.class_type,
                       ss.study_id, ss.study_beginday, ss.study_endday, ss.study_isreading,
                       (SELECT COUNT(ch.hour_id) FROM smc_class_hour ch WHERE ch.class_id = c.class_id AND ch.hour_ischecking = '1') as total_hours,
                       (SELECT COUNT(ch.hour_id) FROM smc_class_hour ch WHERE ch.class_id = c.class_id AND ch.hour_ischecking = '0') as remaining_hours
                FROM smc_student_study ss
                LEFT JOIN smc_class c ON ss.class_id = c.class_id
                WHERE ss.student_id = '{$student_id}' AND c.course_id = '{$course_id}' 
                AND ss.study_isreading = '1' AND c.class_type = '0'
                ORDER BY ss.study_id DESC
                LIMIT 1";
        
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 获取结转价格信息
     */
    private function getForwardPriceInfo($student_id, $course_id)
    {
        $sql = "SELECT scf.courseforward_price, scf.courseforward_deductionmethod
                FROM smc_student_courseforward scf
                WHERE scf.student_id = '{$student_id}' AND scf.course_id = '{$course_id}'";
        
        $result = $this->DataControl->selectOne($sql);
        if (!$result) {
            $result = array(
                'courseforward_price' => 0,
                'courseforward_deductionmethod' => 0
            );
        }
        
        return $result;
    }

    /**
     * 计算结转信息
     * 
     * 核心改进：计算到结转日期前所有未考勤课程的消耗（包括今天之前和之后的）
     * 这样确保预估金额更准确，时间更连贯，避免遗漏已安排但未考勤的课程
     */
    private function calculateForwardInfo($balanceInfo, $forwardPriceInfo, $forward_type, $forward_times, $student_id = '', $course_id = '', $forward_date = '')
    {
        // 当前余额和课次
        $current_balance = floatval($balanceInfo['coursebalance_figure']);
        $current_times = intval($balanceInfo['coursebalance_time']);
        $unit_expend = floatval($balanceInfo['coursebalance_unitexpend']);
        $unit_earning = floatval($balanceInfo['coursebalance_unitearning']);
        $unit_refund = floatval($balanceInfo['coursebalance_unitrefund']);
        $courseforward_price = floatval($forwardPriceInfo['courseforward_price']);
        
        // 如果没有指定结转日期，默认为今天
        if (empty($forward_date)) {
            $forward_date = date('Y-m-d');
        }
        
        // 计算到结转日期之前所有未考勤的课次（包括今天之前和之后的未考勤课程）
        $consumeInfo = $this->getPendingAttendanceBeforeDate($student_id, $course_id, $forward_date);
        $consume_times = intval($consumeInfo['consume_times'] ?: 0);
        
        // 计算结转日期时的预估剩余余额和课次
        // 基于当前余额和课次，减去到结转日期前所有未考勤但会消耗的课程
        $estimated_balance = $current_balance;
        $estimated_times = $current_times;
        
        if ($consume_times > 0) {
            // 扣除期间会消耗的课次
            $estimated_times = max(0, $current_times - $consume_times);
            
            // 扣除期间会消耗的金额（优先使用收益单价，其次支出单价）
            $consume_unit_price = ($unit_earning > 0) ? $unit_earning : $unit_expend;
            $consume_amount = $consume_times * $consume_unit_price;
            $estimated_balance = max(0, $current_balance - $consume_amount);
        }
        
        // 基于预估的剩余余额和课次计算结转金额
        $to_forward_amount = 0;
        $to_forward_times = 0;
        
        if ($forward_type == 1) {
            // 全部结转：使用预估的剩余余额和结转价格
            $to_forward_amount = $estimated_balance + $courseforward_price;
            $to_forward_times = $estimated_times;
        } else if ($forward_type == 2 && $forward_times > 0) {
            // 部分结转：基于预估剩余进行计算
            if ($estimated_times <= 0) {
                // 如果预估剩余课次为0或负数，无法进行部分结转
                $to_forward_times = 0;
                $to_forward_amount = 0;
            } else {
                $to_forward_times = min($forward_times, $estimated_times);
                
                // 计算结转价格部分
                $forward_courseforward_price = 0;
                if ($courseforward_price > 0) {
                    if ($unit_earning > 0 && $unit_expend > $unit_earning) {
                        // 扣除未结转课次的差价
                        $forward_courseforward_price = $courseforward_price - (($estimated_times - $to_forward_times) * ($unit_expend - $unit_earning));
                    } else {
                        // 按比例分配结转价格
                        $forward_courseforward_price = $courseforward_price * ($to_forward_times / $estimated_times);
                    }
                }
                
                // 计算课程余额部分
                $forward_coursebalance_figure = 0;
                if ($unit_earning > 0) {
                    // 使用收益单价计算
                    $forward_coursebalance_figure = $estimated_balance - (($estimated_times - $to_forward_times) * $unit_earning);
                } else {
                    // 使用支出单价计算
                    $forward_coursebalance_figure = $estimated_balance - (($estimated_times - $to_forward_times) * $unit_expend);
                }
                
                // 结转总金额
                $to_forward_amount = max(0, $forward_coursebalance_figure) + max(0, $forward_courseforward_price);
            }
        }
        
        // 确保结转金额不超过总可用金额
        $total_available_amount = $estimated_balance + $courseforward_price;
        if ($to_forward_amount > $total_available_amount) {
            $to_forward_amount = $total_available_amount;
        }
        
        // 确保结转金额不为负数
        $to_forward_amount = max(0, $to_forward_amount);
        $to_forward_times = max(0, $to_forward_times);
        
        // 计算结转后的剩余信息
        $forward_times = max(0, $estimated_times - $to_forward_times);

        // 计算剩余金额：总可用金额 - 结转金额 = 流回客户账户的钱
        $total_available_amount = $estimated_balance + $courseforward_price;
        $forward_amount = max(0, $total_available_amount - $to_forward_amount);

        $result = array(
            'current_balance' => $current_balance,                     // 当前课程余额
            'current_times' => $current_times,                         // 当前课次
            'estimated_balance' => $estimated_balance,                 // 预估余额（到结转日期时的余额）
            'estimated_times' => $estimated_times,                     // 预估课次（到结转日期时的课次）
            'consume_times' => $consume_times,                         // 期间消耗的课次
            'to_forward_amount' => $to_forward_amount,                 // 需要结转的金额
            'to_forward_times' => $to_forward_times,                   // 需要结转的课次
            'forward_amount' => $estimated_balance,                       // 结转完成后剩余的金额（流回客户账户的钱）
            'forward_times' => $estimated_times,                         // 结转完成后剩余的课次
            'deduction_amount' => 0,                                   // 差价不考虑扣除
            'unit_refund' => $unit_refund,                             // 退费单价
            'courseforward_price' => $courseforward_price              // 当前结转价格
        );
        
        return $result;
    }
    
    /**
     * 计算到指定日期之前所有未考勤的课次数（包括今天之前和之后的）
     * 
     * 这种方法比只计算从今天开始的未考勤课程更准确，因为：
     * 1. 今天之前可能有已排课但未考勤的课程
     * 2. 确保时间连贯性，避免漏算已安排的课程
     * 3. 更真实地反映学生在结转日期时的实际剩余情况
     */
    private function getPendingAttendanceBeforeDate($student_id, $course_id, $end_date)
    {
        // 获取学生当前就读的班级
        $classInfo = $this->getStudentCurrentClass($student_id, $course_id);
        if (!$classInfo || empty($classInfo['class_id'])) {
            return array('consume_times' => 0);
        }
        
        // 确保日期格式正确
        $end_date = date('Y-m-d', strtotime($end_date));
        
        // 查询到指定日期之前所有未考勤的收费课次（不限制开始日期）
        $sql = "SELECT COUNT(ch.hour_id) as consume_times
                FROM smc_class_hour ch
                WHERE ch.class_id = '{$classInfo['class_id']}'
                AND ch.hour_day < '{$end_date}'
                AND ch.hour_ischecking = '0'
                AND ch.hour_isfree = '0'
                AND ch.hour_iswarming = '0'";
        
        $result = $this->DataControl->selectOne($sql);
        
        return array(
            'consume_times' => intval($result['consume_times'] ?: 0)
        );
    }
    

    
    /**
     * 按照指定结转日期重新计算课次划分
     */
    private function getTimesByForwardDate($student_id, $course_id, $forward_date)
    {
        $forward_date_format = date('Y-m-d', strtotime($forward_date));
        
        // 获取学生当前就读的班级
        $classSql = "SELECT ss.class_id
                     FROM smc_student_study ss
                     LEFT JOIN smc_class c ON ss.class_id = c.class_id
                     WHERE ss.student_id = '{$student_id}' AND c.course_id = '{$course_id}'
                     AND ss.study_isreading = '1'";
        
        $classResult = $this->DataControl->selectOne($classSql);
        
        if (!$classResult) {
            return array(
                'before_date_times' => 0,
                'before_date_forward_times' => 0,
                'before_date_unchecked_times' => 0,
                'after_date_times' => 0,
                'attended_times' => 0,
                'absent_times' => 0,
                'free_times' => 0
            );
        }
        
        // 获取指定日期之前已考勤的课次（区分收费和免费）
        $beforeConsumedSql = "SELECT 
                                COUNT(CASE WHEN ch.hour_isfree = '0' THEN sh.hourstudy_id END) as before_date_forward_times,
                                SUM(CASE WHEN sh.hourstudy_checkin = '1' and ch.hour_isfree = '0' THEN 1 ELSE 0 END) as attended_times,
                                SUM(CASE WHEN sh.hourstudy_checkin = '0' and ch.hour_isfree = '0' THEN 1 ELSE 0 END) as absent_times,
                                SUM(CASE WHEN ch.hour_isfree = '1' THEN 1 ELSE 0 END) as free_times
                              FROM smc_student_hourstudy sh
                              LEFT JOIN smc_class_hour ch ON sh.hour_id = ch.hour_id
                              LEFT JOIN smc_class c ON sh.class_id = c.class_id
                              WHERE sh.student_id = '{$student_id}' AND c.course_id = '{$course_id}'
                              AND ch.hour_day < '{$forward_date_format}' AND ch.hour_ischecking = '1'";
        
        $beforeConsumedResult = $this->DataControl->selectOne($beforeConsumedSql);
        
        // 获取指定日期之前未考勤的课次（只计算收费课次）
        $beforeUncheckedSql = "SELECT 
                                COUNT(ch.hour_id) as before_date_unchecked_times
                               FROM smc_class_hour ch
                               LEFT JOIN smc_class c ON ch.class_id = c.class_id
                               WHERE ch.class_id = '{$classResult['class_id']}' 
                               AND c.course_id = '{$course_id}'
                               AND ch.hour_day < '{$forward_date_format}' 
                               AND ch.hour_ischecking = '0'
                               AND ch.hour_isfree = '0'";
        
        $beforeUncheckedResult = $this->DataControl->selectOne($beforeUncheckedSql);
        
        // 获取指定日期之后的课次
        $afterDateSql = "SELECT 
                          COUNT(ch.hour_id) as after_date_times
                         FROM smc_class_hour ch
                         LEFT JOIN smc_class c ON ch.class_id = c.class_id
                         WHERE ch.class_id = '{$classResult['class_id']}' 
                         AND c.course_id = '{$course_id}'
                         AND ch.hour_day >= '{$forward_date_format}'";
        
        $afterDateResult = $this->DataControl->selectOne($afterDateSql);
        
        $before_date_forward_times = intval($beforeConsumedResult['before_date_forward_times'] ?: 0);
        $before_date_unchecked_times = intval($beforeUncheckedResult['before_date_unchecked_times'] ?: 0);
        $after_date_times = intval($afterDateResult['after_date_times'] ?: 0);
        
        return array(
            'before_date_times' => $before_date_forward_times + $before_date_unchecked_times,
            'before_date_forward_times' => $before_date_forward_times,
            'before_date_unchecked_times' => $before_date_unchecked_times,
            'after_date_times' => $after_date_times,
            'attended_times' => intval($beforeConsumedResult['attended_times'] ?: 0),
            'absent_times' => intval($beforeConsumedResult['absent_times'] ?: 0),
            'free_times' => intval($beforeConsumedResult['free_times'] ?: 0)
        );
    }

    /**
     * 获取绑定的随堂课程信息
     */
    private function getBindingCourses($course_id)
    {
        // 根据smc_course表的main_course_id字段来查询绑定的课程
        // 如果main_course_id等于传入的course_id，说明这是一个绑定课程
        $sql = "SELECT course_id, course_cnname, course_branch, course_inclasstype, course_isfollow
                FROM smc_course 
                WHERE main_course_id = '{$course_id}' 
                AND company_id = '{$this->company_id}'
                AND course_status = 1";
        
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 创建结转申请
     * @param array $request 申请参数
     * @return array|false 创建结果
     */
    function createForwardApplication($request)
    {
        // 参数验证
        if (!$this->validateForwardApplicationRequest($request)) {
            return false;
        }
        
        $student_id = $request['student_id'];
        $course_id = $request['course_id'];
        $class_id = $request['class_id'];
        $apply_staffer_id = $request['staffer_id'];
        $application_type = $request['application_type'];
        $out_class_date = $request['out_class_date'];
        $back_class_date = isset($request['back_class_date']) ? $request['back_class_date'] : '';
        $forward_reason = $request['forward_reason'];
        $attachment_url = $request['attachment_url'];
        
        // 数据预处理
        $forward_reason = addslashes($forward_reason);
        $attachment_url = addslashes($attachment_url);
        
        // 获取预结转信息
        $preForwardInfo = $this->getPreForwardInfo(array(
            'student_id' => $student_id,
            'class_id' => $class_id,
            'course_id' => $course_id,
            'forward_date' => $request['out_class_date'],
            'forward_type' => 1 // 全部结转
        ));
        
        if (!$preForwardInfo) {
            $this->error = true;
            $this->errortip = "获取预结转信息失败";
            return false;
        }
        
        // 验证预结转信息的有效性
        if (!$this->validatePreForwardInfo($preForwardInfo)) {
            $this->error = true;
            $this->errortip = "结转信息无效：" . $this->errortip;
            return false;
        }
        
        // 开始事务
        $this->DataControl->beginTransaction();
        
        try {
            // 1. 创建结转申请记录
            $application_id = $this->createApplicationRecord($student_id, $course_id, $class_id, $apply_staffer_id, 
                $application_type, $out_class_date, $back_class_date, $forward_reason, $attachment_url, $preForwardInfo);

            if (!$application_id) {
                throw new \Exception("创建申请记录失败");
            }
            
            // 2. 创建申请课程详情记录
            $this->createApplicationCourseDetails($application_id, $preForwardInfo);
            
            // 3. 创建申请跟踪记录
            $this->createApplicationTrack($application_id, '提交申请', '用户提交结转申请', 
                $forward_reason, $apply_staffer_id, 'apply');
            
            // 4. 创建定时任务（如果需要自动取消）
            $this->createApplicationTask($application_id, 'auto_cancel', $this->getAutoCancelTime());
            
            // // 5. 处理申请后续操作
            // $this->handlePostApplicationTasks($application_id, $student_id, $course_id, $apply_staffer_id);

            // 提交事务
            $this->DataControl->commit();

            $FanweiModel = new \Model\Smc\FanweiModel();
            $bool=$FanweiModel->createForwardApply($application_id);

            if(!$bool){
                $this->DataControl->rollback();
                $this->error = true;
                $this->errortip = "泛微创建结转申请失败:".$FanweiModel->errortip;
                return false;
            }

            return true;
            
        } catch (\Exception $e) {
            // 回滚事务
            $this->DataControl->rollback();
            $this->error = true;
            $this->errortip = $e->getMessage();
            return false;
        }
    }

    /**
     * 验证结转申请请求参数
     */
    private function validateForwardApplicationRequest($request)
    {
        $required_fields = array('student_id', 'course_id', 'class_id', 'staffer_id', 'application_type', 
            'out_class_date', 'forward_reason');
        
        foreach ($required_fields as $field) {
            if (!isset($request[$field]) || empty($request[$field])) {
                $this->error = true;
                $this->errortip = "缺少必要参数：{$field}";
                return false;
            }
        }
        
        // 验证日期格式
        if (!strtotime($request['out_class_date'])) {
            $this->error = true;
            $this->errortip = "出班日期格式不正确";
            return false;
        }
        
        // 验证附件URL格式
        if (!empty($request['attachment_url'])) {
            // 简单的URL格式验证
            if (!filter_var($request['attachment_url'], FILTER_VALIDATE_URL)) {
                $this->error = true;
                $this->errortip = "附件URL格式不正确";
                return false;
            }
        }
        
        // 验证申请类型
        if (!in_array($request['application_type'], array(1, 2, 3, 4))) {
            $this->error = true;
            $this->errortip = "申请类型参数错误";
            return false;
        }

        if($request['application_type']!=1 && (!isset($request['back_class_date']) || $request['back_class_date']=='')){
            $this->error = true;
            $this->errortip = "必须传入回班日期";
            return false;
        }
        
        // 验证学员和课程存在
        if (!$this->verifyStudentCourseExists($request['student_id'], $request['course_id'])) {
            $this->error = true;
            $this->errortip = "学员课程不存在";
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证学员课程是否存在
     */
    private function verifyStudentCourseExists($student_id, $course_id)
    {
        $sql = "SELECT coursebalance_id, coursebalance_figure, coursebalance_time 
                FROM smc_student_coursebalance 
                WHERE student_id = '{$student_id}' AND course_id = '{$course_id}' 
                AND school_id = '{$this->school_id}' and (coursebalance_figure>0 or coursebalance_time>0)";
        $result = $this->DataControl->selectOne($sql);
        
        if (!$result) {
            return false;
        }
        
        // 检查是否有可用的余额或课时
        if ($result['coursebalance_figure'] <= 0 && $result['coursebalance_time'] <= 0) {
            $this->errortip = "该学员在此课程中没有可用的余额或课时";
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证预结转信息的有效性
     */
    private function validatePreForwardInfo($preForwardInfo)
    {
        // 验证必要字段存在
        if (!isset($preForwardInfo['summary'])) {
            $this->errortip = "缺少预结转汇总信息";
            return false;
        }
        
        if (!isset($preForwardInfo['main_course'])) {
            $this->errortip = "缺少主课程结转信息";
            return false;
        }
        
        $summary = $preForwardInfo['summary'];
        $required_fields = array(
            'total_current_balance', 'total_current_times', 'total_to_forward_amount', 
            'total_to_forward_times', 'total_forward_amount', 'total_forward_times'
        );
        
        foreach ($required_fields as $field) {
            if (!isset($summary[$field])) {
                $this->errortip = "缺少必要的结转字段：{$field}";
                return false;
            }
        }
        
        // 验证数值合理性
        if ($summary['total_current_balance'] < 0) {
            $this->errortip = "当前余额不能为负数";
            return false;
        }
        
        if ($summary['total_current_times'] < 0) {
            $this->errortip = "当前课时不能为负数";
            return false;
        }
        
        if ($summary['total_to_forward_amount'] < 0) {
            $this->errortip = "结转金额不能为负数";
            return false;
        }
        
        if ($summary['total_to_forward_times'] < 0) {
            $this->errortip = "结转课时不能为负数";
            return false;
        }
        
        // 验证预估余额平衡（基于预估值）
        if (isset($summary['total_estimated_balance'])) {
            $balance_diff = abs($summary['total_estimated_balance'] - 
                               ($summary['total_to_forward_amount'] + $summary['total_forward_amount']));
            if ($balance_diff > 0.01) {
                $this->errortip = "结转金额余额不平衡";
                return false;
            }
        }
        
        // 验证预估课时平衡（基于预估值）
        if (isset($summary['total_estimated_times'])) {
            $times_diff = abs($summary['total_estimated_times'] - 
                             ($summary['total_to_forward_times'] + $summary['total_forward_times']));
            if ($times_diff > 0.01) {
                $this->errortip = "结转课时余额不平衡";
                return false;
            }
        }
        
        // 验证是否有可结转的内容
        if (!isset($preForwardInfo['can_forward']) || !$preForwardInfo['can_forward']) {
            $this->errortip = "当前状态不允许结转";
            return false;
        }
        
        return true;
    }

    /**
     * 创建申请记录
     */
    private function createApplicationRecord($student_id, $course_id, $class_id, $apply_staffer_id, 
        $application_type, $out_class_date, $back_class_date, $forward_reason, $attachment_url, $preForwardInfo)
    {
        $current_time = time();
        
        $data = array(
            'company_id' => $this->company_id,
            'school_id' => $this->school_id,
            'student_id' => $student_id,
            'course_id' => $course_id,
            'class_id' => $class_id,
            'apply_staffer_id' => $apply_staffer_id,
            'apply_time' => $current_time,
            'application_type' => $application_type,
            'out_class_date' => $out_class_date,
            'back_class_date' => $back_class_date ?: null,
            'forward_reason' => $forward_reason,
            'attachment_url' => $attachment_url,
            'forward_amount' => $preForwardInfo['summary']['total_to_forward_amount'],
            'forward_times' => $preForwardInfo['summary']['total_to_forward_times'],
            'remaining_times' => $preForwardInfo['summary']['total_forward_times'],
            'remaining_amount' => $preForwardInfo['summary']['total_forward_amount'],
            'application_status' => '0',
            'create_time' => $current_time,
            'update_time' => $current_time
        );
        
        return $this->DataControl->insertData('smc_forward_application', $data);
    }

    /**
     * 创建申请课程详情记录
     */
    private function createApplicationCourseDetails($application_id, $preForwardInfo)
    {
        $current_time = time();
        
        // 处理主课程
        if (isset($preForwardInfo['main_course']) && !empty($preForwardInfo['main_course'])) {
            $mainCourse = $preForwardInfo['main_course'];
            
            // 从主课程数据中获取课程ID
            $mainCourseId = isset($mainCourse['course_id']) ? $mainCourse['course_id'] : '';
            
            if (empty($mainCourseId)) {
                throw new \Exception("主课程ID不存在");
            }
            
            // 获取主课程信息
            $sql = "SELECT course_cnname, course_branch FROM smc_course WHERE course_id = '{$mainCourseId}'";
            $courseInfo = $this->DataControl->selectOne($sql);
            
            if (!$courseInfo) {
                throw new \Exception("主课程信息不存在，课程ID：{$mainCourseId}");
            }
            
            $mainCourseData = array(
                'application_id' => $application_id,
                'course_id' => $mainCourseId,
                'course_type' => '1', // 主课程
                'course_name' => $courseInfo['course_cnname'],
                'course_branch' => $courseInfo['course_branch'],
                'forward_times' => isset($mainCourse['to_forward_times']) ? $mainCourse['to_forward_times'] : 0,
                'forward_amount' => isset($mainCourse['to_forward_amount']) ? $mainCourse['to_forward_amount'] : 0,
                'balance_before' => isset($mainCourse['current_balance']) ? $mainCourse['current_balance'] : 0,
                'balance_after' => isset($mainCourse['forward_amount']) ? $mainCourse['forward_amount'] : 0,
                'times_before' => isset($mainCourse['current_times']) ? $mainCourse['current_times'] : 0,
                'times_after' => isset($mainCourse['forward_times']) ? $mainCourse['forward_times'] : 0,
                'create_time' => $current_time
            );
            
            $this->DataControl->insertData('smc_forward_application_course', $mainCourseData);
        }
        
        // 处理绑定课程（一对一关系）
        if (isset($preForwardInfo['binding_course']) && !empty($preForwardInfo['binding_course'])) {
            $bindingCourse = $preForwardInfo['binding_course'];
            
            if (!empty($bindingCourse['course_id'])) {
                // 获取绑定课程信息
                $sql = "SELECT course_cnname, course_branch FROM smc_course WHERE course_id = '{$bindingCourse['course_id']}'";
                $courseInfo = $this->DataControl->selectOne($sql);
                
                if (!$courseInfo) {
                    // 记录错误但不中断流程
                    error_log("绑定课程信息不存在，课程ID：{$bindingCourse['course_id']}");
                } else {
                    $bindingCourseData = array(
                        'application_id' => $application_id,
                        'course_id' => $bindingCourse['course_id'],
                        'course_type' => '2', // 绑定课程
                        'course_name' => $courseInfo['course_cnname'],
                        'course_branch' => $courseInfo['course_branch'],
                        'forward_times' => isset($bindingCourse['to_forward_times']) ? $bindingCourse['to_forward_times'] : 0,
                        'forward_amount' => isset($bindingCourse['to_forward_amount']) ? $bindingCourse['to_forward_amount'] : 0,
                        'balance_before' => isset($bindingCourse['current_balance']) ? $bindingCourse['current_balance'] : 0,
                        'balance_after' => isset($bindingCourse['forward_amount']) ? $bindingCourse['forward_amount'] : 0,
                        'times_before' => isset($bindingCourse['current_times']) ? $bindingCourse['current_times'] : 0,
                        'times_after' => isset($bindingCourse['forward_times']) ? $bindingCourse['forward_times'] : 0,
                        'create_time' => $current_time
                    );
                    
                    $this->DataControl->insertData('smc_forward_application_course', $bindingCourseData);
                }
            }
        }
    }

    /**
     * 创建申请跟踪记录
     */
    public function createApplicationTrack($application_id, $track_title, $track_content, 
        $track_note, $staffer_id, $track_type)
    {
        // 获取员工姓名
        $stafferInfo = $this->getStafferInfo($staffer_id);
        $staffer_cnname = $stafferInfo ? $stafferInfo['staffer_cnname'] : '未知';
        
        $current_time = time();
        
        $data = array(
            'application_id' => $application_id,
            'track_title' => $track_title,
            'track_content' => $track_content,
            'track_note' => $track_note,
            'staffer_id' => $staffer_id,
            'staffer_cnname' => $staffer_cnname,
            'track_time' => $current_time,
            'track_type' => $track_type
        );
        
        return $this->DataControl->insertData('smc_forward_application_track', $data);
    }

    /**
     * 创建申请任务
     */
    public function createApplicationTask($application_id, $task_type, $execute_time)
    {
        $current_time = time();

        $applicationOne = $this->getForwardApplication($application_id);


        $data = array(
            'application_id' => $application_id,
            'task_type' => $task_type,
            'update_time' => $current_time
        );

        if($applicationOne){
            if($task_type=='refuse'){
                $data['task_status'] = 2;
            }
            
            return $this->DataControl->updateData('smc_forward_application_task', "application_id='{$application_id}'", $data);
        }else{
            $data['create_time'] = $current_time;
            $data['execute_time'] = $execute_time;
            return $this->DataControl->insertData('smc_forward_application_task', $data);
        }
        
        
        
        
    }

    /**
     * 获取自动取消时间
     */
    private function getAutoCancelTime()
    {
        $config = $this->getApplicationConfig('auto_cancel_days');
        $days = $config ? intval($config) : 7;
        return time() + ($days * 24 * 3600);
    }

    /**
     * 获取申请配置
     */
    private function getApplicationConfig($config_key)
    {
        $sql = "SELECT config_value FROM smc_forward_application_config 
                WHERE (school_id = '{$this->school_id}' OR school_id = 0) 
                AND config_key = '{$config_key}' 
                ORDER BY school_id DESC LIMIT 1";
        
        $result = $this->DataControl->selectOne($sql);
        return $result ? $result['config_value'] : null;
    }

    /**
     * 获取员工信息
     */
    private function getStafferInfo($staffer_id)
    {
        $sql = "SELECT staffer_cnname FROM smc_staffer WHERE staffer_id = '{$staffer_id}'";
        return $this->DataControl->selectOne($sql);
    }
    
    
    /**
     * 获取申请状态常量
     */
    private function getApplicationStatusConstants()
    {
        return array(
            'PENDING' => 0,   // 待审核
            'APPROVED' => 1,  // 已通过
            'REJECTED' => 2,  // 已拒绝
            'CANCELLED' => 3, // 已取消
            'PROCESSING' => 4 // 处理中
        );
    }
    
    /**
     * 获取申请类型常量
     */
    private function getApplicationTypeConstants()
    {
        return array(
            'PERMANENT' => 1,  // 永久结转
            'TEMPORARY' => 2,  // 临时结转
            'SUSPENSION' => 3, // 停课结转
            'ADJUSTMENT' => 4  // 课程调整
        );
    }

    /**
     * 获取结转申请信息
     * @param int $application_id 申请ID
     * @return array|false 申请信息
     */
    function getForwardApplication($application_id)
    {
        $sql = "SELECT fa.*
                FROM smc_forward_application fa
                WHERE fa.application_id = '{$application_id}'";
        
        $application = $this->DataControl->selectOne($sql);
        
        if (!$application) {
            $this->error = true;
            $this->errortip = "申请记录不存在";
            return false;
        }
        
        return $application;
    }

    /**
     * 审批结转申请
     * @param int $application_id 申请ID
     * @param int $staffer_id 审批人ID
     * @param int $action 审批动作：1-通过，2-拒绝
     * @param string $note 审批备注
     * @return bool 审批结果
     */
    function approveForwardApplication($application_id, $staffer_id, $action, $note = '')
    {
        $application = $this->getForwardApplication($application_id);
        if (!$application) {
            return false;
        }
        
        if ($application['application_status'] != 0) {
            $this->error = true;
            $this->errortip = "申请状态不正确，无法审批";
            return false;
        }
        
        $current_time = time();
        $new_status = $action == 1 ? 1 : 2; // 1-通过，2-拒绝
        
        // 开始事务
        $this->DataControl->beginTransaction();
        
        try {
            // 更新申请状态
            $where = "application_id = '{$application_id}'";
            $data = array(
                'application_status' => $new_status,
                'approval_time' => $current_time,
                'approval_note' => $note,
                'update_time' => $current_time
            );
            
            $this->DataControl->updateData('smc_forward_application', $where, $data);
            
            // 创建跟踪记录
            $track_title = $action == 1 ? '审批通过' : '审批拒绝';
            $track_content = $action == 1 ? '申请已审批通过' : '申请已被拒绝';
            $track_type = $action == 1 ? 'approve' : 'reject';
            
            $this->createApplicationTrack($application_id, $track_title, $track_content, 
                $note, $staffer_id, $track_type);
            
            // 如果审批通过，创建自动结转任务
            if ($action == 1) {
                $this->createApplicationTask($application_id, 'auto_forward', $application['out_class_date']);
            }
            
            // 提交事务
            $this->DataControl->commit();
            
            return true;
            
        } catch (\Exception $e) {
            // 回滚事务
            $this->DataControl->rollback();
            $this->error = true;
            $this->errortip = $e->getMessage();
            return false;
        }
    }

    /**
     * 执行结转申请（自动或手动）
     * @param int $application_id 申请ID
     * @param int $staffer_id 执行人ID（自动执行时为0）
     * @return bool 执行结果
     */
    function executeForwardApplication($application_id, $staffer_id = 0)
    {
        $application = $this->getForwardApplication($application_id);
        if (!$application) {
            return false;
        }
        
        if ($application['application_status'] != 1) {
            $this->error = true;
            $this->errortip = "申请状态不正确，无法执行";
            return false;
        }
        
        // 开始事务
        $this->DataControl->beginTransaction();
        
        try {
            // 执行实际的结转操作
            $forwardResult = $this->carryForward($application['student_id'], $application['course_id'], 
                $application['class_id'], $staffer_id);
            
            if (!$forwardResult) {
                throw new \Exception("结转执行失败：" . $this->errortip);
            }
            
            // 更新申请状态
            $current_time = time();
            $where = "application_id = '{$application_id}'";
            $data = array(
                'application_status' => 4,
                'execute_time' => $current_time,
                'update_time' => $current_time
            );
            
            $this->DataControl->updateData('smc_forward_application', $where, $data);
            
            // 创建跟踪记录
            $this->createApplicationTrack($application_id, '结转完成', '结转申请已执行完成', 
                '系统自动执行结转', $staffer_id, 'complete');
            
            // 提交事务
            $this->DataControl->commit();
            
            return true;
            
        } catch (\Exception $e) {
            // 回滚事务
            $this->DataControl->rollback();
            $this->error = true;
            $this->errortip = $e->getMessage();
            return false;
        }
    }


}