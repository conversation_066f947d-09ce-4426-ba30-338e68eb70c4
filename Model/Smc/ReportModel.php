<?php


namespace Model\Smc;

class ReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname,school_shortname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function studentBalance($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or c.course_cnname like '%{$request['keyword']}%' or c.course_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursebalance_status']) && $request['coursebalance_status'] !== '') {
            $datawhere .= " and ssc.coursebalance_status = '{$request['coursebalance_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.course_cnname,c.course_branch,ssc.coursebalance_figure,ssc.coursebalance_time,scf.courseforward_price,b.student_balance
              from smc_student_coursebalance as ssc
              left join smc_student as s on ssc.student_id=s.student_id
              LEFT JOIN smc_student_balance as b ON b.student_id = s.student_id and b.school_id='{$request['school_id']}' and b.company_id='{$request['company_id']}'
              left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and ssc.course_id=scf.course_id
              left join smc_course as c on c.course_id=ssc.course_id
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where {$datawhere} and sse.school_id='{$request['school_id']}' and s.company_id='{$request['company_id']}'
              order by sse.enrolled_createtime desc
              limit {$pagestart},{$num}";

        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select s.student_id
              from smc_student_coursebalance as ssc
              left join smc_student as s on ssc.student_id=s.student_id
              left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and ssc.course_id=scf.course_id
              left join smc_course as c on c.course_id=ssc.course_id
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where {$datawhere} and sse.school_id='{$request['school_id']}' and s.company_id='{$request['company_id']}'";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $studentList;

        return $data;
    }

    function a($request)
    {
        $sql = "select sc.course_branch,sc.course_cnname,ca.coursecat_branch,ca.coursecat_cnname,cc.coursetype_branch,cc.coursetype_cnname,course_classnum
                from smc_course as sc
                left join smc_code_coursetype as cc on sc.coursetype_id=cc.coursetype_id
                left join smc_code_coursecat as ca on ca.coursecat_id=sc.coursecat_id
                where sc.company_id='8888'
                ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_classnum'] = $dateexcelvar['course_classnum'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("班组名称", "班组编号", "班种名称", "班种编号", "班别名称", "班别编号", "班别课次"));
            $excelfileds = array('coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'course_classnum');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("学校课程列表.xlsx"));
            exit;
        }
    }

    function stuOrderReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or sf.family_mobile like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sf.family_cnname like '%{$request['keyword']}%' or po.order_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $endtime = strtotime($request['fixedtime'] . ' 23:59:59');
            $datawhere .= " and po.order_createtime <= '{$endtime}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select po.order_pid,s.student_branch,s.student_cnname,s.student_enname,sf.family_cnname,po.order_paymentprice,po.order_status,po.order_from,po.order_type,po.order_createtime
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_student_enrolled as se on se.student_id=s.student_id
              where {$datawhere} and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<>'-1' and se.enrolled_status<>'-1'
              order by po.order_id desc
              ";
        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统导入"));
        $order_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['order_status'] = $order_status[$val['order_status']];
                $val['order_from'] = $order_from[$val['order_from']];
                $val['order_type'] = $order_type[$val['order_type']];
                $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
            }

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['order_status'] = $dateexcelvar['order_status'];
                    $datearray['order_from'] = $dateexcelvar['order_from'];
                    $datearray['order_type'] = $dateexcelvar['order_type'];
                    $datearray['order_createtime'] = $dateexcelvar['order_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("订单编号", "学员编号", "学员中文名", "学员英文名", "家长姓名", "订单金额", "订单状态", "订单来源", "收费类型", "下单日期"));
            $excelfileds = array('order_pid', 'student_branch', 'student_cnname', 'student_enname', 'family_cnname', 'order_paymentprice', 'order_status', 'order_from', 'order_type', 'order_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}收费日记账报表{$request['fixedtime']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $orderList = $this->DataControl->selectClear($sql);
            if (!$orderList) {
                $this->error = true;
                $this->errortip = "无学生数据";
                return false;
            }
            $data = array();
            foreach ($orderList as &$val) {
                $val['order_status'] = $order_status[$val['order_status']];
                $val['order_from'] = $order_from[$val['order_from']];
                $val['order_type'] = $order_type[$val['order_type']];
                $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select po.order_id
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_student_enrolled as se on se.student_id=s.student_id
              where {$datawhere} and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<>'-1' and se.enrolled_status<>'-1'";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $orderList;

            return $data;
        }
    }

    function stuRefundReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' or B.student_enname like '%{$request['keyword']}%' or B.student_idcard like '%{$request['keyword']}%'  or B.student_branch like '%{$request['keyword']}%'
            or C.family_mobile like '%{$request['keyword']}%' or C.family_cnname like '%{$request['keyword']}%'
            or A.refund_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and A.companies_id='{$request['companies_id']}' ";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and E.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND E.school_istest <> '1' AND E.school_isclose<> '1' ";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] != '') {
            $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        }
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        //创建时间
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $start_time = strtotime($request['start_time']);
            $datawhere .= " and A.refund_createtime >= '{$start_time}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $end_time = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.refund_createtime<= '{$end_time}'";
        }

        //完成时间
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and D.trading_updatatime >= '{$starttime}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and D.trading_updatatime<= '{$endtime}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select A.school_id,E.school_branch,E.school_cnname,
                A.refund_pid, 
                A.from_order_pid, 
                G.companies_cnname,
                B.student_branch, 
                B.student_cnname, 
                B.student_enname, 
                F.enrolled_status, 
                F.enrolled_leavetime, 
                C.family_cnname, 
                A.refund_payprice as refund_price, 
                A.refund_status, 
                A.refund_from, 
                A.refund_type, 
                D.trading_createtime, 
                D.trading_updatatime
                ,(select r.region_name from smc_code_region as r where r.region_id = E.school_province ) as province_name
                ,(select max(y.clockinginlog_day) from smc_student_clockinginlog y where y.school_id=F.school_id and y.student_id=F.student_id) as last_atte_date
                from smc_refund_order A 
                left join smc_student B on B.student_id=A.student_id 
                left join smc_student_family C on C.student_id=B.student_id and C.family_isdefault=1 
                left join smc_student_trading D on D.trading_pid=A.trading_pid 
                left join smc_school E ON A.school_id=E.school_id 
                left join smc_student_enrolled F ON A.school_id=F.school_id and A.student_id=F.student_id
                left join gmc_code_companies G on G.companies_id=D.companies_id
                where {$datawhere} 
                and A.company_id='{$request['company_id']}' 
                and (IFNULL(A.from_order_pid,'')='' OR refund_status<>'4') 
                and A.refund_status>='-1' 
                order by (case when E.school_istest=0 and E.school_isclose=0 then 1 when E.school_isclose=0 then 2 when E.school_istest=0 then 3 else 4 end),E.school_istest asc,field(E.school_sort,0),E.school_sort asc,E.school_createtime asc,E.school_branch,A.refund_createtime desc 
              ";

        $enrolled_status = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "新生", "-1" => "已离校"));
        $refund_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统导入"));
        $refund_type = $this->LgArraySwitch(array("0" => "银行转账", "1" => "原路返还"));
        $refund_status = $this->LgArraySwitch(array("0" => "申请", "1" => "审核通过", "2" => "处理中", "3" => "确定金额", "4" => "完成退款", "-1" => "拒绝"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['refund_status'] = $refund_status[$val['refund_status']];
                $val['refund_from'] = $refund_from[$val['refund_from']];
                $val['refund_type'] = $refund_type[$val['refund_type']];
                $val['trading_createtime'] = date("Y-m-d H:i:s", $val['trading_createtime']);
                $val['trading_updatatime'] = $val['trading_updatatime'] ? date("Y-m-d H:i:s", $val['trading_updatatime']) : '--';
                $val['enrolled_status'] = $enrolled_status[$val['enrolled_status']];
                $val['enrolled_leavetime'] = $val['enrolled_leavetime'] ? date("Y-m-d H:i:s", $val['enrolled_leavetime']) : '--';
            }

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['refund_pid'] = $dateexcelvar['refund_pid'];
                    $datearray['from_order_pid'] = $dateexcelvar['from_order_pid'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['enrolled_status'] = $dateexcelvar['enrolled_status'];
                    $datearray['enrolled_leavetime'] = $dateexcelvar['enrolled_leavetime'];
                    $datearray['last_atte_date'] = $dateexcelvar['last_atte_date'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    $datearray['refund_price'] = $dateexcelvar['refund_price'];
                    $datearray['refund_status'] = $dateexcelvar['refund_status'];
                    $datearray['refund_from'] = $dateexcelvar['refund_from'];
                    $datearray['refund_type'] = $dateexcelvar['refund_type'];
                    $datearray['trading_createtime'] = $dateexcelvar['trading_createtime'];
                    $datearray['trading_updatatime'] = $dateexcelvar['trading_updatatime'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "退费支付主体", "订单编号", "来源订单号", "学员编号", "学员中文名", "学员英文名", "学员状态", "流失日期", "最后上课日期", "家长姓名", "退费金额总计", "退款状态", "订单来源", "退款类型", "制单日期", "完成日期"));
            $excelfileds = array("province_name", "school_branch", "school_cnname", "companies_cnname", 'refund_pid', 'from_order_pid', 'student_branch', 'student_cnname', 'student_enname', 'enrolled_status', 'enrolled_leavetime', 'last_atte_date', 'family_cnname', 'refund_price', 'refund_status', 'refund_from', 'refund_type', 'trading_createtime', 'trading_updatatime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}退费日记账报表{$request['fixedtime']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无学生退款数据";
                return false;
            }
            $data = array();
            foreach ($refundList as &$val) {
                $val['refund_status'] = $refund_status[$val['refund_status']];
                $val['refund_from'] = $refund_from[$val['refund_from']];
                $val['refund_type'] = $refund_type[$val['refund_type']];
                $val['trading_createtime'] = date("Y-m-d H:i:s", $val['trading_createtime']);
                $val['trading_updatatime'] = $val['trading_updatatime'] ? date("Y-m-d H:i:s", $val['trading_updatatime']) : '--';
                $val['enrolled_status'] = $enrolled_status[$val['enrolled_status']];
                $val['enrolled_leavetime'] = $val['enrolled_leavetime'] ? date("Y-m-d H:i:s", $val['enrolled_leavetime']) : '--';
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                    select A.refund_id
                    from smc_refund_order A 
                    left join smc_student B on B.student_id=A.student_id 
                    left join smc_student_family C on C.student_id=B.student_id and C.family_isdefault=1 
                    left join smc_student_trading D on D.trading_pid=A.trading_pid 
                    left join smc_school E ON A.school_id=E.school_id 
                    where {$datawhere} 
                    and A.company_id='{$request['company_id']}' 
                    and (IFNULL(A.from_order_pid,'')='' OR refund_status<>'4') 
                    and A.refund_status>='-1'  
                    order by A.refund_createtime desc 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

//            if (isset($request['query_type']) && $request['query_type'] == 'school') {
//                $sql = "select a.companies_id,b.companies_cnname
//                from smc_refund_order a
//                left join gmc_code_companies b on a.companies_id=b.companies_id
//                WHERE 1
//                and a.company_id = '{$request['company_id']}'
//                and a.school_id='{$this->school_id}'
//                group by a.companies_id ";
//                $companiesList = $this->DataControl->selectClear($sql);
//                $data['companieslist'] = $companiesList ? $companiesList : array();
//            } else {
//                $sql = "select a.companies_id,b.companies_cnname
//                from smc_refund_order a
//                left join gmc_code_companies b on a.companies_id=b.companies_id
//                WHERE 1
//                and a.company_id = '{$request['company_id']}'
//                group by a.companies_id ";
//                $companiesList = $this->DataControl->selectClear($sql);
//                $data['companieslist'] = $companiesList ? $companiesList : array();
//            }

            $data['list'] = $refundList;

            return $data;
        }
    }

    function monthlyIncomeReport($request, $isjindie = 0)
    {
        $datawhere = " 1 AND B.school_isclose=0 ";
        if ($isjindie == '1') {
            $datawhere .= "  AND B.school_province='10' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (C.student_cnname like '%{$request['keyword']}%' or C.student_enname like '%{$request['keyword']}%' 
            or C.student_idcard like '%{$request['keyword']}%' or C.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and B.school_branch='{$request['school_branch']}' ";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND B.school_istest <> '1' AND B.school_isclose<> '1' ";
        }
        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.income_confirmtime <='{$endtime}'";
        } else {
            $endtime = time();
            $datawhere .= " and A.income_confirmtime <='{$endtime}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and A.companies_id= '{$request['companies_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and e.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and e.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['region_id']) && $request['region_id'] !== '') {
            $datawhere .= " and b.school_province= '{$request['region_id']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
        } else {
            $starttime = strtotime(date('Y-m-01') . ' 0:00:00');
        }
        $datawhere .= " and A.income_confirmtime >= '{$starttime}'";

        $incomeOne=$this->DataControl->getFieldOne('smc_school_income', "MIN(income_id) as income_id", " income_confirmtime >= '{$starttime}'");
        if($incomeOne){
            $datawhere.= " and A.income_id >= '{$incomeOne['income_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.school_id,B.school_cnname,B.school_branch,
                A.student_id,C.student_branch,C.student_cnname,C.student_enname,H.channel_name,
                G.coursetype_cnname,G.coursetype_branch,F.coursecat_cnname,F.coursecat_branch,
                D.class_id,D.class_branch,D.class_cnname,D.class_enname,
                A.income_id,A.income_confirmtime,A.income_type,A.income_price,A.income_audittime,
                J.companies_cnname,J.companies_kidbranch,
                A.income_note,ifnull(U.subtype_name,'--')  as subtype_name,
                (select r.region_name from smc_code_region as r where r.region_id = B.school_province ) as province_name
                from smc_school_income A 
                left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id 
                left join smc_student C on A.company_id=C.company_id and A.student_id=C.student_id 
                left join smc_class D on A.class_id=D.class_id and A.company_id=D.company_id 
                left join smc_course E ON D.course_id=E.course_id and A.company_id=E.company_id 
                left join smc_code_coursecat F ON E.coursecat_id=F.coursecat_id and A.company_id=F.company_id 
                left join smc_code_coursetype G ON F.coursetype_id=G.coursetype_id and A.company_id=G.company_id 
                LEFT JOIN smc_student_guildpolicy H ON A.student_id=H.student_id 
                LEFT JOIN smc_code_subtype U ON U.subtype_id=A.subtype_id 
                left join gmc_code_companies J ON A.companies_id=J.companies_id
                where {$datawhere} 
                and A.company_id='{$request['company_id']}' 
                order by (case when B.school_istest=0 and B.school_isclose=0 then 1 when B.school_isclose=0 then 2 when B.school_istest=0 then 3 else 4 end),B.school_istest asc,field(B.school_sort,0),B.school_sort asc,B.school_createtime asc,B.school_branch,D.class_branch,A.income_confirmtime desc,A.income_type,A.student_id
              ";

        $income_type = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "红冲收入"));

//        var_dump($sql);die;

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无分摊收入";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['income_id'] = $dateexcelvar['income_id'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['income_type'] = $income_type[$dateexcelvar['income_type']];
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['income_price'] = $dateexcelvar['income_price'];
                    $datearray['subtype_name'] = $dateexcelvar['subtype_name'];
                    $datearray['income_note'] = $dateexcelvar['income_note'];
                    $datearray['income_confirmtime'] = date("Y-m-d H:i:s", $dateexcelvar['income_confirmtime']);
                    $datearray['income_audittime'] = $dateexcelvar['income_audittime'] > 0 ? date("Y-m-d", $dateexcelvar['income_audittime']) : date("Y-m-d", $dateexcelvar['income_confirmtime']);
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "分摊编号", "学员编号", "学员中文名", "学员英文名", "专案名称", "班组名称", "班组代码", "班种名称", "班种代码", "班级编号", "班级别名", "收入类型", "收入主体", "本期收入", "认缴类别", '备注', '分摊时间', '上课时间'));
            $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'income_id', 'student_branch', 'student_cnname', 'student_enname', 'channel_name', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'class_branch', 'class_enname', 'income_type', 'companies_cnname', 'income_price', 'subtype_name', 'income_note', 'income_confirmtime', 'income_audittime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}月收入明细表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $incomeList = $this->DataControl->selectClear($sql);
            if (!$incomeList) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            foreach ($incomeList as &$var) {
                $var['income_type'] = $income_type[$var['income_type']];
                $var['income_confirmtime'] = date("Y-m-d H:i:s", $var['income_confirmtime']);
                $var['income_audittime'] = $var['income_audittime'] > 0 ? date("Y-m-d", $var['income_audittime']) : date("Y-m-d", $var['income_confirmtime']);
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.income_id
                from smc_school_income A
                left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id 
                left join smc_student C on A.company_id=C.company_id and A.student_id=C.student_id 
                left join smc_class D on A.class_id=D.class_id 
                left join smc_course E ON D.course_id=E.course_id 
                where {$datawhere} 
                and A.company_id='{$request['company_id']}' 
                order by A.school_id,A.income_type,A.student_id 
                ";//and A.school_id='{$request['school_id']}'
                $dbCount = $this->DataControl->selectClear($count_sql);
                if ($dbCount) {
                    $allnum = count($dbCount);;
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

//            if (isset($request['query_type']) && $request['query_type'] == 'school') {
//                $sql = "select a.companies_id,b.companies_cnname
//                from smc_school_income a
//                left join gmc_code_companies b on a.companies_id=b.companies_id
//                WHERE 1
//                and a.company_id = '{$request['company_id']}'
//                and a.school_id='{$this->school_id}'
//                group by a.companies_id ";
//                $companiesList = $this->DataControl->selectClear($sql);
//                $data['companieslist'] = $companiesList ? $companiesList : array();
//            } else {
//                $sql = "select a.region_id,a.region_name
//                from smc_code_region a
//                WHERE region_level=2
//                order by region_id";
//                $regionList = $this->DataControl->selectClear($sql);
//                $data['regionlist'] = $regionList ? $regionList : array();
//            }
            $data['list'] = $incomeList;

            return $data;
        }
    }

    function monthlyIncomeReportToJindie($request, $isjindie = 0)
    {
        $datawhere = " 1 AND B.school_isclose=0 ";
        if ($isjindie == '1') {
            $datawhere .= "  AND B.school_province='10' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (C.student_cnname like '%{$request['keyword']}%' or C.student_enname like '%{$request['keyword']}%' 
            or C.student_idcard like '%{$request['keyword']}%' or C.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_branch']) && $request['school_branch'] !== '') {
            $datawhere .= " and B.school_branch='{$request['school_branch']}' ";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND B.school_istest <> '1' AND B.school_isclose<> '1' ";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.income_confirmtime <='{$endtime}'";
        } else {
            $endtime = time();
            $datawhere .= " and A.income_confirmtime <='{$endtime}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and A.companies_id= '{$request['companies_id']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
        } else {
            $starttime = strtotime(date('Y-m-01') . ' 0:00:00');
        }
        $datawhere .= " and A.income_confirmtime >= '{$starttime}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select concat(ifnull(B.school_branch,'aa'),ifnull(A.companies_id,'bb'),ifnull(G.coursetype_branch,'cc'),ifnull(A.income_type,'dd'),ifnull(FROM_UNIXTIME(A.income_confirmtime,'%Y-%m-%d'),'ee')) as serialnoid,
                A.school_id,B.school_cnname,B.school_branch, 
                G.coursetype_cnname,G.coursetype_branch, 
                A.income_confirmtime,
                FROM_UNIXTIME(A.income_confirmtime,'%Y-%m-%d') as confirmdate,
                A.income_type,
                sum(A.income_price) as income_price,
                J.companies_cnname,J.companies_kidbranch 
                from smc_school_income A 
                left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id   
                left join smc_class D on A.class_id=D.class_id and A.company_id=D.company_id 
                left join smc_course E ON D.course_id=E.course_id and A.company_id=E.company_id 
                left join smc_code_coursecat F ON E.coursecat_id=F.coursecat_id and A.company_id=F.company_id 
                left join smc_code_coursetype G ON F.coursetype_id=G.coursetype_id and A.company_id=G.company_id  
                left join gmc_code_companies J ON A.companies_id=J.companies_id
                where {$datawhere} 
                and A.company_id='{$request['company_id']}' 
                group by B.school_branch,A.companies_id,G.coursetype_branch,A.income_type,confirmdate  
                order by (case when B.school_istest=0 and B.school_isclose=0 then 1 when B.school_isclose=0 then 2 when B.school_istest=0 then 3 else 4 end),B.school_istest asc,field(B.school_sort,0),B.school_sort asc,B.school_createtime asc,B.school_branch,D.class_branch,A.income_confirmtime desc,A.income_type,A.student_id
              ";

        $income_type = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "红冲收入"));

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $incomeList = $this->DataControl->selectClear($sql);
        if (!$incomeList) {
            $this->error = true;
            $this->errortip = "无收入数据";
            return false;
        }

        foreach ($incomeList as &$var) {
            $var['income_confirmtime'] = date("Y-m-d H:i:s", $var['income_confirmtime']);
            if ($var['income_type'] == '0' || $var['income_type'] == '1') {
                $var['coursetype_branch'] = ($var['coursetype_branch'] ? $var['coursetype_branch'] : 'QiTa');
                $var['coursetype_cnname'] = ($var['coursetype_cnname'] ? $var['coursetype_cnname'] : '其他');
            } elseif ($var['income_type'] == '2') {
                $var['coursetype_branch'] = ($var['coursetype_branch'] ? $var['coursetype_branch'] : 'JiaoCai');
                $var['coursetype_cnname'] = ($var['coursetype_cnname'] ? $var['coursetype_cnname'] : '教材');
            } elseif ($var['income_type'] == '3') {
                $var['coursetype_branch'] = ($var['coursetype_branch'] ? $var['coursetype_branch'] : 'ZaFei');
                $var['coursetype_cnname'] = ($var['coursetype_cnname'] ? $var['coursetype_cnname'] : '杂费');
            }
            $var['income_type'] = $income_type[$var['income_type']];
        }

        $data = array();

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select A.income_id,
                FROM_UNIXTIME(A.income_confirmtime,'%Y-%m-%d') as confirmdate from smc_school_income A 
                left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id   
                left join smc_class D on A.class_id=D.class_id and A.company_id=D.company_id 
                left join smc_course E ON D.course_id=E.course_id and A.company_id=E.company_id 
                left join smc_code_coursecat F ON E.coursecat_id=F.coursecat_id and A.company_id=F.company_id 
                left join smc_code_coursetype G ON F.coursetype_id=G.coursetype_id and A.company_id=G.company_id 
                LEFT JOIN smc_student_guildpolicy H ON A.student_id=H.student_id  
                left join gmc_code_companies J ON A.companies_id=J.companies_id
                where {$datawhere} 
                and A.company_id='{$request['company_id']}' 
                group by B.school_branch,A.companies_id,G.coursetype_branch,A.income_type,confirmdate  
            ";
            $dbCount = $this->DataControl->selectClear($count_sql);
            if ($dbCount) {
                $allnum = count($dbCount);;
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $incomeList;

        return $data;
    }

//    function monthlyExpendReport($request)
//    {
//        $datawhere = " 1 ";
//
//        if (isset($request['keyword']) && $request['keyword'] !== '') {
//            $datawhere .= " and (C.student_cnname like '%{$request['keyword']}%' or C.student_enname like '%{$request['keyword']}%'
//            or C.student_idcard like '%{$request['keyword']}%' or C.student_branch like '%{$request['keyword']}%')";
//        }
//
//        if (isset($request['end_time']) && $request['end_time'] !== '') {
//            $endtime = strtotime($request['end_time'] . ' 23:59:59');
//            $datawhere .= " and A.expend_confirmtime <='{$endtime}'";
//        } else {
//            $endtime = time();
//            $datawhere .= " and A.expend_confirmtime <='{$endtime}'";
//        }
//
//        if (isset($request['start_time']) && $request['start_time'] !== '') {
//            $starttime = strtotime($request['start_time'] . ' 0:00:00');
//        } else {
//            $starttime = strtotime(date('Y-m-01') . ' 0:00:00');
//        }
//        $datawhere .= " and A.expend_confirmtime >= '{$starttime}'";
//
//        if (isset($request['p']) && $request['p'] !== '') {
//            $page = $request['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($request['num']) && $request['num'] !== '') {
//            $num = $request['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;
//
//        $sql = "select  A.expend_id,A.trading_pid,
//                    A.school_id,B.school_cnname,B.school_branch,
//                    A.student_id,C.student_branch,C.student_cnname,C.student_enname,
//                    A.expend_type,
//                    A.expend_price,
//                    A.expend_note
//                  from smc_school_expend A
//                  left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id
//                  left join smc_student C on A.company_id=C.company_id and A.student_id=C.student_id
//                  where {$datawhere}
//                  and A.company_id='{$request['company_id']}'
//                  and A.school_id='{$request['school_id']}'
//                  order by A.school_id,A.expend_type,A.student_id
//              ";
//
//
//        $expend_type = $this->LgArraySwitch(array("0" => "签呈报损", "1" => "认缴支出", "2" => "坏账核销"));
//
//
//        if (isset($request['is_export']) && $request['is_export'] == 1) {
//            $dateexcelarray = $this->DataControl->selectClear($sql);
//
//            if (!$dateexcelarray) {
//                $this->error = true;
//                $this->errortip = "无支出数据";
//                return false;
//            }
//
//            $outexceldate = array();
//            if ($dateexcelarray) {
//                $outexceldate = array();
//                foreach ($dateexcelarray as $dateexcelvar) {
//                    $datearray = array();
//
//                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
//                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
//                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
//                    $datearray['expend_type'] = $expend_type[$dateexcelvar['expend_type']];
//                    $datearray['expend_price'] = $dateexcelvar['expend_price'];
//                    $datearray['expend_note'] = $dateexcelvar['expend_note'];
//                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
//                    $outexceldate[] = $datearray;
//                }
//            }
//            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
//            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", "支出类型", "支出金额", '备注', '关联交易单号'));
//            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'expend_type', 'expend_price', 'expend_note', 'trading_pid');
//            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}月支出明细表.xlsx"));
//            exit;
//        } else {
//            $sql .= ' limit ' . $pagestart . ',' . $num;
//            $incomeList = $this->DataControl->selectClear($sql);
//            if (!$incomeList) {
//                $this->error = true;
//                $this->errortip = "无支出数据";
//                return false;
//            }
//
//            foreach ($incomeList as &$var) {
//                $var['expend_type'] = $expend_type[$var['expend_type']];
//            }
//
//            $data = array();
//
//            if (isset($request['is_count']) && $request['is_count'] == 1) {
//                $count_sql = "select A.expend_id
//                  from smc_school_expend A
//                  left join smc_school B on A.company_id=B.company_id and A.school_id=B.school_id
//                  left join smc_student C on A.company_id=C.company_id and A.student_id=C.student_id
//                  where {$datawhere}
//                  and A.company_id='{$request['company_id']}'
//                  and A.school_id='{$request['school_id']}'
//                  order by A.school_id,A.expend_type,A.student_id
//";
//                $dbCount = $this->DataControl->selectClear($count_sql);
//                if ($dbCount) {
//                    $allnum = count($dbCount);;
//                } else {
//                    $allnum = 0;
//                }
//                $data['allnum'] = $allnum;
//            }
//
//            $data['list'] = $incomeList;
//
//            return $data;
//        }
//    }

//    function gainsByChannel($request)
//    {
//        $datawhere = " c.company_id = '{$request['company_id']}' ";
//        if (isset($request['school_id']) && $request['school_id'] !== '') {
//            $datawhere .= " and l.school_id='{$request['school_id']}' ";
//        }
//        if (isset($request['frommedia_name']) && $request['frommedia_name'] !== '') {
//            $datawhere .= " and c.client_source='{$request['frommedia_name']}' ";
//        }
//        if (isset($request['channel_id']) && $request['channel_id'] !== '') {
//            $datawhere .= " and cl.channel_id='{$request['channel_id']}' ";
//        }
//        if (isset($request['start_time']) && $request['start_time'] !== '') {
//            $starttime = strtotime($request['start_time'] . ' 0:00:00');
//            $datawhere .= " and c.client_createtime >='{$starttime}'";
//        }
//        if (isset($request['end_time']) && $request['end_time'] !== '') {
//            $endtime = strtotime($request['end_time'] . ' 23:59:59');
//            $datawhere .= " and c.client_createtime <='{$endtime}'";
//        }
//        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== '') {
//            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}'";
//        }
//
//        if (isset($request['p']) && $request['p'] !== '') {
//            $page = $request['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($request['num']) && $request['num'] !== '') {
//            $num = $request['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;
//
//        $sql = "select l.school_cnname,l.school_branch,c.client_id,c.client_cnname,c.client_enname,c.client_sex,p.parenter_cnname,c.client_mobile,c.client_tracestatus,c.client_distributionstatus,c.client_source,c.client_intention_level,cl.channel_name,c.client_createtime,c.client_updatetime,
//            (select group_concat(m.marketer_name) from crm_client_principal as p,crm_marketer as m where p.marketer_id = m.marketer_id and p.client_id = c.client_id and p.school_id= r.school_id and p.principal_ismajor = 1 and p.principal_leave =0 ) as mian_marketer_name,
//             (select group_concat(m.marketer_name) from crm_client_principal as p,crm_marketer as m where p.marketer_id = m.marketer_id and p.client_id = c.client_id and p.school_id= r.school_id and p.principal_ismajor = 0 and p.principal_leave =0 ) as fu_marketer_name,
//             (select track_createtime  from crm_client_track as t where t.client_id = c.client_id and t.school_id = r.school_id and t.track_isactive =1 order by t.track_createtime DESC limit 0,1 ) as track_createtime,
//             (select group_concat(t.track_note)  from crm_client_track as t where t.client_id = c.client_id and t.school_id = r.school_id and t.track_isactive =1 order by t.track_createtime DESC limit 0,5 ) as track_note
//            from crm_client as c
//            left join crm_code_channel as cl ON cl.channel_id = c.channel_id
//            left join crm_client_family as f ON c.client_id = f.client_id and family_isdefault =1
//            left join smc_parenter as p ON f.parenter_id = p.parenter_id
//            left join crm_client_schoolenter as r ON c.client_id = r.client_id
//            left join smc_school as l On l.school_id=r.school_id
//            where {$datawhere}  order by c.client_createtime DESC";
//
//        //0待跟踪1持续跟踪2已柜询3已试听4已转正-1无意向-2无效名单
//         $tracestatus_array = array(0=>'待跟踪',1=>'持续跟踪',2=>'已柜询',3=>'已试听',4=>'已转正',-1=>'无意向',-2=>'无效名单');
//        if (isset($request['is_export']) && $request['is_export'] == 1) {
//            $dateexcelarray = $this->DataControl->selectClear($sql);
//            if (!$dateexcelarray) {
//                $this->error = true;
//                $this->errortip = "无招生数据";
//                return false;
//            }
//            $outexceldate = array();
//            if ($dateexcelarray) {
//                $outexceldate = array();
//                foreach ($dateexcelarray as $dateexcelvar) {
//                    $datearray = array();
//                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
//                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
//                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
//                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
//                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
//                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
//                    $datearray['client_tracestatus_name'] = $tracestatus_array[$dateexcelvar['client_tracestatus']];
//                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
//                    $datearray['client_source'] = $dateexcelvar['client_source'];
//                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
//                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
//                    $datearray['client_updatetime'] = date("Y-m-d", $dateexcelvar['client_updatetime']);
//                    $datearray['mian_marketer_name'] = date("Y-m-d", $dateexcelvar['mian_marketer_name']);
//                    $datearray['fu_marketer_name'] = date("Y-m-d", $dateexcelvar['fu_marketer_name']);
//                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'] == '' ? '--' : date("Y-m-d", $dateexcelvar['track_createtime']);
//                    $datearray['track_note'] = $dateexcelvar['track_note'];
//                    $outexceldate[] = $datearray;
//                }
//            }
//            if(isset($request['school_id']) &&  $request['school_id'] !==''){
//                $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
//            }else{
//                $schoolOne = array();
//                $schoolOne['school_cnname'] = '';
//            }
//            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "中文名", "英文名", "性别", '年龄', '主要联系人','主要联系电话','名单状态','意向星级','渠道类型','渠道明细','创建日期','更新时间','主要负责人','协助负责人','最后跟踪时间','最后5次跟踪记录'));
//            $excelfileds = array('school_cnname', 'school_branch', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'parenter_cnname','client_mobile','状态','client_intention_level','client_source','channel_name','client_createtime','client_updatetime','mian_marketer_name','fu_marketer_name','track_createtime','track_note');
//            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}渠道招生明细表.xlsx"));
//            exit;
//
//        } else {
//            $sql .= " limit {$pagestart},{$num}";
//        }
//
//        $dataList = $this->DataControl->selectClear($sql);
//        if ($dataList) {
//            foreach ($dataList as &$value) {
//                $value['client_createtime'] = date("Y-m-d", $value['client_createtime']);
//                $value['client_updatetime'] = date("Y-m-d", $value['client_updatetime']);
//                $value['track_createtime'] = date("Y-m-d", $value['track_createtime']);
//                $value['client_tracestatus_name'] = $tracestatus_array[$value['client_tracestatus']];
//            }
//        }
//
//        if (isset($request['is_count']) && $request['is_count'] == 1) {
//            $all_nums = $this->DataControl->selectOne(" select count(c.client_id) as num from crm_client as c
//            left join crm_code_channel as cl ON cl.channel_id = c.channel_id
//            left join crm_client_family as f ON c.client_id = f.client_id and family_isdefault =1
//            left join smc_parenter as p ON f.parenter_id = p.parenter_id
//            left join crm_client_schoolenter as r ON c.client_id = r.client_id
//            left join smc_school as l On l.school_id=r.school_id
//            where {$datawhere}");
//            $data['allnum'] = $all_nums['num'];
//        } else {
//            $data['allnum'] = 0;
//        }
//        $data['list'] = $dataList;
//        return $data;
//    }


    function orderUnpaidReport($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.order_createtime <='{$endtime}'";
        } else {
            $endtime = time();
            $datawhere .= " and A.order_createtime <='{$endtime}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
        } else {
            $starttime = 0;
        }
        $datawhere .= " and A.order_createtime >= '{$starttime}'";

        $wherecondition = "";
        $havingcondition = " having 1 ";

        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
            switch ($request['pay_type']) {
                case "0":
                    $wherecondition .= " ifnull((select sum(ordercourse_totalprice) from smc_payfee_order_course where order_pid=A.order_pid) ,0) as ordercourse_totalprice, 
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='0'),0) as ordercourse_paidprice,";
                    $havingcondition .= " and ordercourse_totalprice-ordercourse_paidprice>0 ";
                    break;
                case "1":
                    $wherecondition .= " ifnull((select sum(ordergoods_totalprice) from smc_payfee_order_goods where order_pid=A.order_pid) ,0) as ordergoods_totalprice, 
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='1'),0) as ordergoods_paidprice,";
                    $havingcondition .= " and ordergoods_totalprice-ordergoods_paidprice>0 ";
                    break;
                case "2":
                    $wherecondition .= " ifnull((select sum(item_totalprice) from smc_payfee_order_item where order_pid=A.order_pid) ,0) as orderitem_totalprice, 
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='2'),0) as orderitem_paidprice,";
                    $havingcondition .= " and orderitem_totalprice-orderitem_paidprice>0 ";
                    break;
                default:
                    break;
            }
        }

        $wherecondition .= " A.order_createtime,A.order_note ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
 select  C.school_id,C.school_branch,C.school_cnname,C.school_enname,
 B.student_id,B.student_branch,B.student_cnname,B.student_enname,
 A.order_pid,A.order_from,A.order_type,A.order_status,
 A.order_allprice,A.order_coupon_price,A.order_market_price,
 A.order_paymentprice,A.order_paidprice,A.order_arrearageprice, 
 {$wherecondition}
 from smc_payfee_order A
 left join smc_student B on A.student_id=B.student_id and A.company_id=B.company_id
 left join smc_school C on A.school_id=C.school_id and A.company_id=C.company_id
 where {$datawhere} 
 and A.company_id='{$request['company_id']}'
 and A.order_arrearageprice>0
 and A.order_status>'0'
 {$havingcondition}
 order by A.order_createtime desc
              ";//  and A.school_id='{$request['school_id']}'

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $order_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无订单欠费数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_enname'] = $dateexcelvar['school_enname'];

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];

                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['order_from'] = $order_from[$dateexcelvar['order_from']];
                    $datearray['order_type'] = $order_type[$dateexcelvar['order_type']];
                    $datearray['order_status'] = $order_status[$dateexcelvar['order_status']];
                    $datearray['order_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['order_createtime']);
                    $datearray['order_note'] = $dateexcelvar['order_note'];

                    $datearray['order_allprice'] = $dateexcelvar['order_allprice'];
                    $datearray['order_coupon_price'] = $dateexcelvar['order_coupon_price'];
                    $datearray['order_market_price'] = $dateexcelvar['order_market_price'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                    $datearray['order_arrearageprice'] = $dateexcelvar['order_arrearageprice'];

                    if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                        switch ($request['pay_type']) {
                            case "0":
                                $datearray['ordercourse_totalprice'] = $dateexcelvar['ordercourse_totalprice'];
                                $datearray['ordercourse_paidprice'] = $dateexcelvar['ordercourse_paidprice'];
                                $datearray['ordercourse_ownprice'] = $dateexcelvar['ordercourse_totalprice'] - $dateexcelvar['ordercourse_paidprice'];
                                break;
                            case "1":
                                $datearray['ordergoods_totalprice'] = $dateexcelvar['ordergoods_totalprice'];
                                $datearray['ordergoods_paidprice'] = $dateexcelvar['ordergoods_paidprice'];
                                $datearray['ordergoods_ownprice'] = $dateexcelvar['ordergoods_totalprice'] - $dateexcelvar['ordergoods_paidprice'];
                                break;
                            case "2":
                                $datearray['orderitem_totalprice'] = $dateexcelvar['orderitem_totalprice'];
                                $datearray['orderitem_paidprice'] = $dateexcelvar['orderitem_paidprice'];
                                $datearray['orderitem_ownprice'] = $dateexcelvar['orderitem_totalprice'] - $dateexcelvar['orderitem_paidprice'];
                                break;
                            default:
                                break;
                        }
                    }

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");


            if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                switch ($request['pay_type']) {
                    case "0":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '课程应收金额', '课程已付金额', '课程欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'ordercourse_totalprice', 'ordercourse_paidprice', 'ordercourse_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}订单欠费明细表-课程.xlsx"));
                        break;
                    case "1":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '教材应收金额', '教材已付金额', '教材欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'ordergoods_totalprice', 'ordergoods_paidprice', 'ordergoods_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费明细表-教材.xlsx");
                        break;
                    case "2":
                        $excelheader = array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '杂费应收金额', '杂费已付金额', '杂费欠费金额');
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'orderitem_totalprice', 'orderitem_paidprice', 'orderitem_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}订单欠费明细表-杂费.xlsx"));
                        break;
                    default:
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}订单欠费总表.xlsx"));
                        break;
                }
            } else {
                $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额'));
                $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice');
                query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}订单欠费总表.xlsx"));
            }
//            $excelheader = array("学员编号", "学员中文名", "学员英文名", "支出类型", "支出金额", '备注', '关联交易单号');
//            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'expend_type', 'expend_price', 'expend_note', 'trading_pid');
//            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费表.xlsx");
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $unpaidList = $this->DataControl->selectClear($sql);
            if (!$unpaidList) {
                $this->error = true;
                $this->errortip = "无订单欠费数据";
                return false;
            }

            foreach ($unpaidList as &$var) {
                $var['order_from'] = $order_from[$var['order_from']];
                $var['order_type'] = $order_type[$var['order_type']];
                $var['order_status'] = $order_status[$var['order_status']];
                $var['order_createtime'] = date("Y-m-d H:i:s", $var['order_createtime']);

                if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                    switch ($request['pay_type']) {
                        case "0":
                            $var['ordercourse_ownprice'] = $var['ordercourse_totalprice'] - $var['ordercourse_paidprice'];
                            break;
                        case "1":
                            $var['ordergoods_ownprice'] = $var['ordergoods_totalprice'] - $var['ordergoods_paidprice'];
                            break;
                        case "2":
                            $var['orderitem_ownprice'] = $var['orderitem_totalprice'] - $var['orderitem_paidprice'];
                            break;
                        default:
                            break;
                    }
                }
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
 select A.order_pid,{$wherecondition}
 from smc_payfee_order A
 left join smc_student B on A.student_id=B.student_id and A.company_id=B.company_id
 where {$datawhere} 
 and A.company_id='{$request['company_id']}' 
 and A.order_arrearageprice>0
 and A.order_status>'0'
 {$havingcondition}
 order by A.order_createtime desc
"; //and A.school_id='{$request['school_id']}'
                $dbCount = $this->DataControl->selectClear($count_sql);
                if ($dbCount) {
                    $allnum = count($dbCount);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $unpaidList;

            return $data;
        }
    }

    function stuAdvanceReport($request)
    {

        $datawhere = "scb.company_id='{$request['company_id']}'";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or scb.coursecat_branch like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or cf.feetype_name like '%{$request['keyword']}%')";
        }

        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND scb.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and scb.coursecatbalance_createtime <= '{$endtime}'";
        } else {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and scb.school_id='{$request['school_id']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and l.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND l.school_istest <> '1' AND l.school_isclose<> '1' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and cct.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and scb.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and co.course_id = '{$request['course_id']}'";
        }

        if (isset($request['feetype_code']) && $request['feetype_code'] !== '') {
            $datawhere .= " and cf.feetype_code = '{$request['feetype_code']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select l.school_id,l.school_branch,l.school_cnname,s.student_branch, s.student_cnname, s.student_enname, cct.coursetype_branch,cct.coursetype_cnname,
                scb.coursecat_branch,scb.coursecatbalance_month,scb.coursecatbalance_unitexpend,scb.coursecatbalance_unitrefund,
                (select sum(case when log_playclass='+' then log_playamount else -log_playamount end) from smc_student_coursecatbalance_log where student_id=scb.student_id and school_id=scb.school_id and feetype_code=scb.feetype_code and coursecat_id=scb.coursecat_id and log_time>'{$endtime}') as plus_figure, coursecatbalance_figure,
                (select sum(case when log_playclass='+' then log_playme else -log_playme end) from smc_student_coursecatbalance_log where student_id=scb.student_id and school_id=scb.school_id and feetype_code=scb.feetype_code and coursecat_id=scb.coursecat_id and log_time>'{$endtime}') as plus_time,coursecatbalance_time,
                cf.feetype_name,sse.enrolled_status,(select r.region_name from smc_code_region as r where r.region_id = l.school_province ) as province_name
              from smc_student_coursecatbalance as scb 
              left join smc_course as co on co.course_branch=scb.course_branch  and scb.company_id=co.company_id 
              inner join smc_student as s on s.student_id=scb.student_id
              left join smc_code_feetype as cf on cf.feetype_code=scb.feetype_code
              left join smc_student_enrolled  as sse on scb.school_id=sse.school_id and scb.student_id=sse.student_id
              left join smc_school as l on scb.school_id=l.school_id
              left join smc_code_coursecat ccc on scb.coursecat_id=ccc.coursecat_id
              left join smc_code_coursetype cct on scb.coursetype_id=cct.coursetype_id
              where {$datawhere} 
              having (coursecatbalance_figure-ifnull(plus_figure,0)>0 or coursecatbalance_time-ifnull(plus_time,0)>0)
              order by (case when l.school_istest=0 and l.school_isclose=0 then 1 when l.school_isclose=0 then 2 when l.school_istest=0 then 3 else 4 end),l.school_istest asc,field(l.school_sort,0),l.school_sort asc,l.school_createtime asc,s.student_branch,scb.coursecat_branch,cf.feetype_name";


        $status_type = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "已毕业", "3" => "保留学籍", "-1" => "已离校"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预收余额";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['enrolled_status'] = $status_type[$dateexcelvar['enrolled_status']];
                    $datearray['feetype_name'] = $dateexcelvar['feetype_name'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecatbalance_month'] = $dateexcelvar['coursecatbalance_month'];
                    $datearray['coursecatbalance_figure'] = $dateexcelvar['coursecatbalance_figure'] - ($dateexcelvar['plus_figure'] ? $dateexcelvar['plus_figure'] : 0);
                    $datearray['coursecatbalance_time'] = $dateexcelvar['coursecatbalance_time'] - ($dateexcelvar['plus_time'] ? $dateexcelvar['plus_time'] : 0);
                    $datearray['coursecatbalance_unitexpend'] = $dateexcelvar['coursecatbalance_unitexpend'];
                    $datearray['coursecatbalance_unitrefund'] = $dateexcelvar['coursecatbalance_unitrefund'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "学员编号", "学员中文名", "学员英文名", "在校状态", "收费类型", "班组名称", "班组编号", "班种", '管理费月份', "课程剩余余额", "课程剩余次数", "消耗单价", "退费单价"));
            $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'student_branch', 'student_cnname', 'student_enname', 'enrolled_status', 'feetype_name', 'coursetype_cnname', 'coursetype_branch', 'coursecat_branch', 'coursecatbalance_month', 'coursecatbalance_figure', 'coursecatbalance_time', 'coursecatbalance_unitexpend', 'coursecatbalance_unitrefund');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学生预收余额表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $leftAdvance = $this->DataControl->selectClear($sql);
            if (!$leftAdvance) {
                $this->error = true;
                $this->errortip = "无预收余额";
                return false;
            }


            foreach ($leftAdvance as &$var) {
                $var['enrolled_status'] = $status_type[$var['enrolled_status']];
                $var['coursecatbalance_figure'] = $var['coursecatbalance_figure'] - ($var['plus_figure'] ? $var['plus_figure'] : 0);
                $var['coursecatbalance_time'] = $var['coursecatbalance_time'] - ($var['plus_time'] ? $var['plus_time'] : 0);
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select s.student_branch, s.student_cnname, s.student_enname,
                scb.coursecat_branch,scb.coursecatbalance_month,scb.coursecatbalance_unitexpend,scb.coursecatbalance_unitrefund,
                (select sum(case when log_playclass='+' then log_playamount else -log_playamount end) from smc_student_coursecatbalance_log where student_id=scb.student_id and school_id=scb.school_id and feetype_code=scb.feetype_code and coursecat_id=scb.coursecat_id and log_time>'{$endtime}') as plus_figure, coursecatbalance_figure,
                (select sum(case when log_playclass='+' then log_playme else -log_playme end) from smc_student_coursecatbalance_log where student_id=scb.student_id and school_id=scb.school_id and feetype_code=scb.feetype_code and coursecat_id=scb.coursecat_id and log_time>'{$endtime}') as plus_time,coursecatbalance_time,
                cf.feetype_name,sse.enrolled_status
              from smc_student_coursecatbalance as scb
              left join smc_course as co on co.course_branch=scb.course_branch  and scb.company_id=co.company_id 
              inner join smc_student as s on s.student_id=scb.student_id
              left join smc_code_feetype as cf on cf.feetype_code=scb.feetype_code
              left join smc_student_enrolled  as sse on scb.school_id=sse.school_id and scb.student_id=sse.student_id
              left join smc_code_coursecat ccc on scb.coursecat_id=ccc.coursecat_id
              left join smc_code_coursetype cct on scb.coursetype_id=cct.coursetype_id 
              left join smc_school as l on scb.school_id=l.school_id
              where {$datawhere}
              having (coursecatbalance_figure-ifnull(plus_figure,0)>0 or coursecatbalance_time-ifnull(plus_time,0)>0)
              order by s.student_branch,scb.coursecat_branch,cf.feetype_name";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $leftAdvance;

            return $data;
        }
    }

    function stuEnrolledReport($request)
    {

        $where = "1";
        $where = "sc.school_id ='{$request['school_id']}' ";
        if (isset($request['start_time']) && $request['start_time']) {
            $where .= " and  sc.changelog_day >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time']) {
            $where .= " and  sc.changelog_day <='{$request['end_time']}'";
        }

        if (isset($request['keyword']) && $request['keyword']) {
            $where .= " and  (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select s.student_cnname,s.student_enname,s.student_branch,
				(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code,
				(select sc.changelog_day from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  changelog_day,
				(select sc.changelog_note from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  changelog_note
			   from smc_student  as s
			  left join smc_student_enrolled as  se ON  se.student_id = s.student_id
			  where  se.school_id = '{$request['school_id']}'
			  Having  stuchange_code IN ('C02','B05') ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无学院流失记录";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_note'];
                    $datearray['changelog_day'] = $dateexcelvar['stuchange_code'];

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", '异动代码', '异动备注', '异动日期'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'stuchange_code', 'changelog_note', 'changelog_day');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学生异动学员表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
            $changeList = $this->DataControl->selectClear($sql);

            $num = 0;
            if ($request['is_count'] && $request['is_count'] == '1') {
                $sql = "select s.student_cnname,
				(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code

			   from smc_student  as s

			  left join smc_student_enrolled as  se ON  se.student_id = s.student_id
			  where  se.school_id = '{$request['school_id']}'
			  Having  stuchange_code IN ('C02','B05')  ";
                $countList = $this->DataControl->selectClear($sql);


                $num = count($countList);

            }


            $data['allnum'] = $num + 0;


            $data['list'] = $changeList == false ? array() : $changeList;
            return $data;
        }
    }

    function stuEnrolledClass($request)
    {
        $datawhere = " 1 ";
        $datahaving = " 1 ";
        $endqueryday = date("Y-m-d");
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
            $endqueryTimes = strtotime($endqueryday) + (3600 * 24);
            $datahaving .= " and pricinglog_createtime<'{$endqueryTimes}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id='{$request['coursecat_id']}'";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id='{$request['course_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,s.student_id,s.student_cnname,s.student_enname,s.student_branch,sc.course_cnname,sc.course_branch
              ,(select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.course_id=scb.course_id and ss.student_id=scb.student_id and ss.company_id='{$this->company_id}' and ss.school_id='{$this->school_id}' limit 0,1) as study_id
              ,(select scp.pricinglog_createtime from smc_student_coursebalance_pricinglog as scp where scp.student_id=scb.student_id and scp.pricing_id=scb.pricing_id and scp.course_id=scb.course_id and scb.school_id='{$this->school_id}' order by scp.pricinglog_createtime desc limit 0,1) as pricinglog_createtime
              from smc_student_coursebalance as scb
              left join smc_course as sc on sc.course_id=scb.course_id and sc.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scb.student_id
              where {$datawhere} and scb.company_id='{$this->company_id}' and scb.school_id='{$this->school_id}'
              HAVING (study_id=0 or study_id is null) and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) and {$datahaving}
              order by scb.coursebalance_figure desc,s.student_id desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学生课程数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "课程别名称", "课程别编号", "课程余额", "课程剩余次数"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '待入班明细报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无学生课程数据";
                return false;
            }

            $data = array();
            $count_sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
              ,(select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.course_id=scb.course_id and ss.student_id=scb.student_id and ss.company_id='{$this->company_id}' and ss.school_id='{$this->school_id}' limit 0,1) as study_id
              ,(select scp.pricinglog_createtime from smc_student_coursebalance_pricinglog as scp where scp.student_id=scb.student_id and scp.pricing_id=scb.pricing_id and scp.course_id=scb.course_id and scb.school_id='{$this->school_id}' order by scp.pricinglog_createtime desc limit 0,1) as pricinglog_createtime
              from smc_student_coursebalance as scb
              left join smc_course as sc on sc.course_id=scb.course_id and sc.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scb.student_id
              where {$datawhere} and scb.company_id='{$this->company_id}' and scb.school_id='{$this->school_id}'
              HAVING (study_id=0 or study_id is null) and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) and {$datahaving}
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }

    }

    //班级结算学生收入明细报表
    function classEndStudentIncome($request)
    {
        $datawhere = "A.company_id='{$request['company_id']}'";//A.school_id='{$request['school_id']}' and

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        }
        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
            or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' 
            or B.student_branch like '%{$request['keyword']}%' 
            or C.CLASS_BRANCH like '%{$request['keyword']}%')";
        }


        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and C.class_enddate <= '{$request['end_time']}'";
        }
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and C.class_enddate >= '{$request['start_time']}'";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT G.school_id,G.school_branch,G.school_cnname 
                ,C.class_id,C.class_branch,C.class_cnname,C.class_enname,C.class_stdate,C.class_enddate 
                ,B.student_id,B.student_branch,B.student_cnname,B.student_enname,A.study_beginday,A.study_endday 
                ,(select sum(X.timelog_playtimes) from smc_student_coursebalance_timelog X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.timelog_playclass=Y.code_playclass and X.timelog_playname=Y.code_playname AND Y.code_group='冲销') as rece_times 
                ,(select sum(X.timelog_playtimes) from smc_student_coursebalance_timelog X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.timelog_playclass=Y.code_playclass and X.timelog_playname=Y.code_playname AND Y.code_group='结转') as back_times 
                ,(select sum(X.timelog_playtimes) from smc_student_coursebalance_timelog X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.timelog_playclass=Y.code_playclass and X.timelog_playname=Y.code_playname AND Y.code_group='认列') as used_times 
                ,(select sum(log_playamount) from smc_student_coursebalance_log X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.log_class='0' and X.log_playclass=Y.code_playclass and X.log_playname=Y.code_playname AND Y.code_group='冲销') as rece_amount 
                ,(select sum(log_playamount) from smc_student_coursebalance_log X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.log_class='0' and X.log_playclass=Y.code_playclass and X.log_playname=Y.code_playname AND Y.code_group='结转') as back_amount 
                ,(select sum(log_playamount) from smc_student_coursebalance_log X,smc_code_playgroup Y where X.school_id=F.school_id AND X.student_id=F.student_id 
                AND X.course_id=F.course_id and X.log_class='0' and X.log_playclass=Y.code_playclass and X.log_playname=Y.code_playname AND Y.code_group='认列') as used_amount 
                ,F.coursebalance_figure as left_amount,F.coursebalance_time as left_times
                FROM smc_student_study A 
                LEFT JOIN smc_student B ON A.company_id=B.company_id AND A.student_id=B.student_id 
                LEFT JOIN smc_class C ON A.company_id=C.company_id AND A.class_id=C.class_id 
                LEFT JOIN smc_course D ON A.company_id=D.company_id AND C.course_id=D.course_id 
                LEFT JOIN smc_code_coursecat E ON A.company_id=E.company_id AND D.coursecat_id=E.coursecat_id 
                LEFT JOIN smc_student_coursebalance F ON A.company_id=F.company_id AND A.school_id=F.school_id AND A.student_id=F.student_id AND C.course_id=F.course_id 
                LEFT JOIN smc_school G ON A.company_id=G.company_id AND A.school_id=G.school_id 
                WHERE {$datawhere}
                AND C.class_status<>'-2' 
                order BY G.school_branch,C.class_branch
              ";


        $status_type = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "已毕业", "3" => "保留学籍", "-1" => "已离校"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预收余额";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

//                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['class_id'] = $dateexcelvar['class_id'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_period'] = $dateexcelvar['class_stdate'] . '~' . $dateexcelvar['class_enddate'];
//                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_period'] = $dateexcelvar['study_beginday'] . '~' . $dateexcelvar['study_endday'];
                    $datearray['rece_times'] = $dateexcelvar['rece_times'];
                    $datearray['rece_amount'] = $dateexcelvar['rece_amount'];
                    $datearray['back_times'] = $dateexcelvar['back_times'];
                    $datearray['back_amount'] = $dateexcelvar['back_amount'];
                    $datearray['used_times'] = $dateexcelvar['used_times'];
                    $datearray['used_amount'] = $dateexcelvar['used_amount'];
                    $datearray['left_times'] = $dateexcelvar['left_times'];
                    $datearray['left_amount'] = $dateexcelvar['left_amount'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "班级编号", "班级名称", "班级别名", "开班时间", "学生编号", "学员中文名", '学员英文名', "就读时间", "冲销课次", "冲销金额", "结算课次", "结算金额", "认列课次", "认列金额", "剩余课次", "剩余金额"));
            $excelfileds = array("school_branch", "school_cnname", 'class_branch', 'class_cnname', 'class_enname', 'class_period', 'student_branch', 'student_cnname', 'student_enname', 'student_period', 'rece_times', 'rece_amount', 'back_times', 'back_amount', 'used_times', 'used_amount', 'left_times', 'left_amount');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学生预收余额表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无预收余额";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['class_period'] = $var['class_stdate'] . '~' . $var['class_enddate'];
                $var['student_period'] = $var['study_beginday'] . '~' . $var['study_endday'];
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT A.school_id
                ,C.class_id
                ,B.student_id
                FROM smc_student_study A 
                LEFT JOIN smc_student B ON A.company_id=B.company_id AND A.student_id=B.student_id 
                LEFT JOIN smc_class C ON A.company_id=C.company_id AND A.class_id=C.class_id 
                WHERE {$datawhere}
                AND C.class_status<>'-2' 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $ClassCalc;

            return $data;
        }
    }

    function studCourseEstimate($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' AND d.school_istest <> '1' AND d.school_isclose<>'1' AND e.course_classnum > 10";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id='{$request['school_id']}' ";
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.student_cnname like '%{$request['keyword']}%' 
            or c.student_enname like '%{$request['keyword']}%' 
            or c.student_idcard like '%{$request['keyword']}%' 
            or c.student_branch like '%{$request['keyword']}%' 
            or b.class_enname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and b.class_enddate<= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and b.class_enddate>= '{$request['start_time']}'";
            $datawhere .= " and a.study_endday>= '{$request['start_time']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and e.course_id= '{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and e.coursecat_id= '{$request['coursecat_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and e.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['course_isrenew']) && $request['course_isrenew'] !== '') {
            $datawhere .= " and e.course_isrenew= '{$request['course_isrenew']}'";
            $datawhere .= " and b.class_isnotrenew <>'{$request['course_isrenew']}'";
        }

        if (isset($request['is_renewal']) && $request['is_renewal'] !== '') {
            if ($request['is_renewal'] == "1") {
//                $datawhere .= " having ifnull(renewal_times,0)>0";
                $datawhere .= " having ((renewal_amount-unpaid_price+spend_price)>=2500 or ifnull(connect_times,'')='续费') ";
            } else {
//                $datawhere .= " having ifnull(renewal_times,0)=0";
                $datawhere .= " having ((renewal_amount-unpaid_price+spend_price)<2500 and ifnull(connect_times,'')<>'续费')";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select d.school_id,d.school_branch,d.school_cnname,
                c.student_id,c.student_branch,c.student_cnname,c.student_enname,j.channel_name,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id AND st.student_id = c.student_id AND st.track_classname = '续费电访'
                ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times,
                b.class_id,b.class_branch,b.class_cnname,b.class_enname,b.class_enddate,
                (select GROUP_CONCAT(staffer_cnname) from smc_staffer x,smc_class_teach y where x.staffer_id=y.staffer_id and y.class_id=a.class_id and y.teach_type=0) as main_teacher,
                e.course_branch,e.course_cnname,f.coursecat_branch,f.coursecat_cnname,g.coursetype_branch,g.coursetype_cnname,
                ifnull((select sum(X.coursebalance_figure) from smc_student_coursebalance X,smc_course Z 
                where X.school_id=a.school_id AND X.student_id=a.student_id AND X.course_id=Z.course_id AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_amount,
                ifnull((select sum(coursebalance_time) from smc_student_coursebalance X,smc_course Z 
                where X.school_id=a.school_id AND X.student_id=a.student_id AND X.course_id=Z.course_id AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_times ,
                ifnull((select sum(order_arrearageprice) from smc_payfee_order x where x.order_status>0 and x.school_id=a.school_id and x.student_id=a.student_id
                and exists(select 1 from smc_payfee_order_course y,smc_course z where y.course_id=z.course_id and y.order_pid=x.order_pid and z.company_id=x.company_id
                and z.coursetype_id=e.coursetype_id) ),0) as unpaid_price,
                ifnull((select sum(income_price) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.school_id=a.school_id and x.student_id=a.student_id
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_price 
                from 
                smc_student_study a
                left join smc_class b on a.class_id=b.class_id
                left join smc_student c on a.student_id=c.student_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_course e on b.course_id=e.course_id and a.company_id=e.company_id 
                LEFT JOIN smc_code_coursecat f ON e.coursecat_id = f.coursecat_id  AND a.company_id = f.company_id 
                LEFT JOIN smc_code_coursetype g ON f.coursetype_id = g.coursetype_id  AND a.company_id = g.company_id 
                left join smc_student_guildpolicy j on c.student_id=j.student_id and j.guildpolicy_enddate>=a.study_endday
                where b.class_status<>'-2' 
                and {$datawhere} 
                order by d.school_id,b.class_enddate,b.class_branch 
              ";

        //$status_type = array("0" => "待入班", "1" => "已入班", "2" => "已毕业", "3" => "保留学籍", "-1" => "已离校");

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

//                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
//                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];

                    $datearray['is_renewal'] = $this->LgStringSwitch((($dateexcelvar['renewal_amount'] ? $dateexcelvar['renewal_amount'] : 0) - ($dateexcelvar['unpaid_price'] ? $dateexcelvar['unpaid_price'] : 0) + ($dateexcelvar['spend_price'] ? $dateexcelvar['spend_price'] : 0) >= 2500 || $dateexcelvar['connect_times'] == '续费') ? "是" : "否");
                    $datearray['renewal_times'] = $dateexcelvar['renewal_times'];
                    $datearray['renewal_amount'] = $dateexcelvar['renewal_amount'];
                    $datearray['unpaid_price'] = $dateexcelvar['unpaid_price'];
                    $datearray['spend_price'] = $dateexcelvar['spend_price'];
                    $datearray['estimate_price'] = ($dateexcelvar['renewal_amount'] ? $dateexcelvar['renewal_amount'] : 0) - ($dateexcelvar['unpaid_price'] ? $dateexcelvar['unpaid_price'] : 0) + ($dateexcelvar['spend_price'] ? $dateexcelvar['spend_price'] : 0);

                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['connect_times'] = trim($dateexcelvar['connect_times']) == '' ? '---' : $dateexcelvar['connect_times'];
//                    $datearray['class_id'] = $dateexcelvar['class_id'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_enddate'] = $dateexcelvar['class_enddate'];
                    $datearray['main_teacher'] = $dateexcelvar['main_teacher'];
                    /*$datearray['sub_teacher'] = $dateexcelvar['sub_teacher'];*/

                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "学生编号", "学员中文名", '学员英文名', "是否续费", "已续费课次", "已续费金额", "班组欠费金额", "后续耗课金额", "续费评估金额", "专案名称", "电访结果", "班级编号", "班级名称", "班级别名", "班级结束日期", "主教教师", "课程别编号", "课程别名称", "班种编号", "班种名称", "班组编号", "班组名称"));
            $excelfileds = array('school_branch', 'school_cnname', 'student_branch', 'student_cnname', 'student_enname', 'is_renewal', 'renewal_times', 'renewal_amount', 'unpaid_price', 'spend_price', 'estimate_price', 'channel_name', "connect_times", 'class_branch', 'class_cnname', 'class_enname', 'class_enddate', 'main_teacher', 'course_branch', 'course_cnname', "coursecat_branch", "coursecat_cnname", "coursetype_branch", "coursetype_cnname");

            if (isset($request['school_id']) && $request['school_id'] !== '') {
                $excelName = $this->LgStringSwitch("{$schoolOne['school_cnname']}学员续费预估统计表.xlsx");
            } else {
                $excelName = $this->LgStringSwitch("学员续费预估统计表.xlsx");
            }

            query_to_excel($excelheader, $outexceldate, $excelfileds, $excelName);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ClassCalc = $this->DataControl->selectClear($sql);
            if (!$ClassCalc) {
                $this->error = true;
                $this->errortip = "无预估信息";
                return false;
            }

            foreach ($ClassCalc as &$var) {
                $var['connect_times'] = trim($var['connect_times']) == '' ? '---' : $var['connect_times'];
                $var['estimate_price'] = $var['renewal_amount'] - $var['unpaid_price'] + $var['spend_price'];
                $var['is_renewal'] = $this->LgStringSwitch(($var['renewal_amount'] >= 2500 || $var['connect_times'] == '续费') ? "是" : "否");
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select d.school_id,
                c.student_id,
                b.class_id,
                ifnull((select sum(X.coursebalance_figure) from smc_student_coursebalance X,smc_course Z 
                where X.school_id=a.school_id AND X.student_id=a.student_id AND X.course_id=Z.course_id AND Z.coursetype_id=e.coursetype_id and X.course_id<>b.course_id),0) as renewal_amount,
                ifnull((select sum(order_arrearageprice) from smc_payfee_order x where x.order_status>0 and x.school_id=a.school_id and x.student_id=a.student_id
                and exists(select 1 from smc_payfee_order_course y,smc_course z where y.course_id=z.course_id and y.order_pid=x.order_pid and z.company_id=x.company_id
                and z.coursetype_id=e.coursetype_id) ),0) as unpaid_price,
                ifnull((select sum(income_price) from smc_school_income x,smc_course Y where x.course_id=Y.course_id and x.school_id=a.school_id and x.student_id=a.student_id
                and X.income_type='0' AND income_confirmtime>=UNIX_TIMESTAMP(a.study_endday) and y.coursetype_id=e.coursetype_id and x.class_id<>a.class_id),0) as spend_price ,
                ifnull((SELECT rs.trackresult_name FROM smc_student_track AS st, smc_code_trackresult AS rs WHERE rs.trackresult_id = st.result_id AND st.student_id = c.student_id AND st.track_classname = '续费电访'
                ORDER BY st.track_id DESC LIMIT 0, 1 ),'') AS connect_times
                from 
                smc_student_study a
                left join smc_class b on a.class_id=b.class_id
                left join smc_student c on a.student_id=c.student_id
                left join smc_school d on a.school_id=d.school_id
                left join smc_course e on b.course_id=e.course_id and a.company_id=e.company_id 
                where b.class_status<>'-2'  
                and {$datawhere} 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $ClassCalc;

            return $data;
        }
    }

    //班级信息统计表
    function classInfo()
    {
        $sql = "select event_id as student_id, event_id as class_cnname, event_id as class_enname, event_id as class_branch, event_id as count1, event_id as count2, event_id as count3, event_id as count4, event_id as percent1, event_id as count5, event_id as count6, event_id as count7, event_id as count8, event_id as count9, event_id as count10, event_id as percent2 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //班级升班明细表
    function classLevelUp()
    {
        $sql = "select event_id as from_class_cnname, event_id as from_class_branch, event_id as from_course_cnname, event_id as from_staffer_cnname, event_id as count1, event_id as date1, event_id as to_class_cnname, event_id as to_class_branch, event_id as to_staffer_cnname, event_id as count2, event_id as percent from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //班级转班明细报表
    function changeClassDetail()
    {
        $sql = "select event_id as student_cnname, event_id as student_enname, event_id as student_branch, event_id as from_class_cnname, event_id as from_class_branch, event_id as to_class_cnname, event_id as to_class_branch, event_id as change_day, event_id as reason_note from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    function teacherClass($request)
    {
        $datawhere = " so.school_istest <> '1' AND so.school_isclose<> '1' ";
        $study_where = '1';
        $hour_where = '1';
        $change_where = '1';
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
//            $study_where .= " and d.study_beginday <= '{$request['start_time']}' ";
            $hour_where .= " and sch.hour_day >= '{$request['start_time']}' ";
            $change_where .= " and l.changelog_day >= '{$request['start_time']}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $study_where .= " and d.study_endday >= '{$request['end_time']}'";
            $hour_where .= " and sch.hour_day <= '{$request['end_time']}' ";
            $change_where .= " and l.changelog_day <= '{$request['end_time']}' ";
            $datawhere .= " and class_enddate >= '{$request['end_time']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (s.staffer_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or s.staffer_enname like '%{$request['keyword']}%')";
        }


        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        $file_name_add = '';
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and sc.coursetype_id = '{$request['coursetype_id']}'";
            $coursetypeOne = $this->DataControl->getFieldOne('smc_code_coursetype', "coursetype_cnname", " coursetype_id= '{$request['coursetype_id']}'");
            $file_name_add .= '_' . $coursetypeOne['coursetype_cnname'];
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and c.coursecat_id = '{$request['coursecat_id']}'";
            $coursecatOne = $this->DataControl->getFieldOne('smc_code_coursecat', "coursecat_branch", " coursecat_id= '{$request['coursecat_id']}'");
            $file_name_add .= '_' . $coursecatOne['coursecat_branch'];
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $to = "'A07','B03','B04','B06','E01','B07'";
        $leave = "'B01','C02'";

        $sql = "
            select so.school_id,so.school_branch,so.school_cnname,so.school_shortname,ch.staffer_id,s.staffer_branch,s.staffer_cnname,s.staffer_enname,count(ch.class_id) as teaching_class_num,
            (select count( d.student_id) from smc_student_study as d, smc_class_teach as cth where cth.class_id=d.class_id and cth.staffer_id=s.staffer_id and {$study_where} ) as student_all_num,
            (select count(h.hourstudy_id) from smc_student_hourstudy as h,smc_class_hour as sch,smc_class_teach as cth  where cth.class_id=sch.class_id  and  cth.staffer_id=s.staffer_id and  h.hour_id=sch.hour_id   and {$hour_where} ) as student_all_arrive_num,
            (select count( d.hourstudy_id) from smc_student_hourstudy as d,smc_class_hour as sch,smc_class_teach as cth  where cth.class_id=sch.class_id  and  cth.staffer_id=s.staffer_id  and d.hour_id=sch.hour_id   and sch.hour_ischecking =1 and d.hourstudy_checkin =1  and {$hour_where} ) as student_is_checking_num,
            (select count(DISTINCT l.student_id) from smc_student_changelog as l,smc_student_change as g,smc_class_teach as cth where  cth.class_id=l.class_id  and l.change_pid = g.change_pid and l.changelog_type =1 and l.stuchange_code = 'A05' and cth.class_id = c.class_id  and {$change_where} and g.from_class_id not in (select class_id from smc_class_teach as cth where cth.staffer_id =s.staffer_id )  and cth.staffer_id =s.staffer_id  ) as to_class_num,
            (select count(DISTINCT l.student_id) from smc_student_changelog as l,smc_student_change as g,smc_class_teach as cth where  cth.class_id=l.class_id and  l.change_pid = g.change_pid  and changelog_type =1  and cth.class_id = c.class_id and stuchange_code in ({$to}) and {$change_where} and g.from_class_id not in (select class_id from smc_class_teach as cth where cth.staffer_id =s.staffer_id ) and cth.staffer_id =s.staffer_id ) as out_class_num,
            (select count(DISTINCT l.student_id) from smc_student_changelog as l ,smc_class_teach as cth where  cth.class_id=l.class_id and changelog_type =0 and stuchange_code ='B02' and {$change_where}  and  cth.staffer_id =s.staffer_id  ) as up_class_num,
            (select count(DISTINCT l.student_id) from smc_student_changelog as l  ,smc_class_teach as cth where  cth.class_id=l.class_id and changelog_type =1 and stuchange_code  in ({$leave}) and {$change_where}   and cth.staffer_id =s.staffer_id  ) as leave_class_num,
            (select count(d.student_id) from smc_student_study as d,smc_class_teach as cth where cth.class_id=d.class_id   and d.study_beginday > (select sch.hour_day from smc_class_hour as sch where sch.class_id=d.class_id  and sch.hour_isfree = 0 and sch.hour_iswarming =0 order by sch.hour_day ASC limit 0,1) and  cth.staffer_id =s.staffer_id   and {$study_where}  ) as  student_insert_num
            FROM  smc_staffer as s
            LEFT JOIN smc_class_teach as ch ON s.staffer_id=ch.staffer_id
            LEFT JOIN smc_class as c ON c.class_id = ch.class_id
            LEFT JOIN smc_school as so ON c.school_id = so.school_id
            Left JOIN smc_course as sc ON sc.course_id=c.course_id
            where ch.teach_type = '0'  and s.company_id='{$this->company_id}' and  {$datawhere}
            group by so.school_id,s.staffer_id
            order by so.school_id,s.staffer_id";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无带课数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {

            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['teaching_class_num'] = $dateexcelvar['teaching_class_num'];
                    $datearray['student_all_num'] = $dateexcelvar['student_all_num'];
                    $datearray['student_all_arrive_num'] = $dateexcelvar['student_all_arrive_num'];
                    $datearray['student_no_checking_num'] = $dateexcelvar['student_all_arrive_num'] - $dateexcelvar['student_is_checking_num'];
                    $datearray['student_is_checking_num'] = $dateexcelvar['student_is_checking_num'];
                    $datearray['student_all_arrive_rate'] = $dateexcelvar['student_all_arrive_num'] > 0 ? round($dateexcelvar['student_is_checking_num'] / $dateexcelvar['student_all_arrive_num'], 4) * 100 . '%' : '0%';
                    $datearray['to_class_num'] = $dateexcelvar['to_class_num'];
                    $datearray['out_class_num'] = $dateexcelvar['out_class_num'];
                    $datearray['up_class_num'] = $dateexcelvar['up_class_num'];
                    $datearray['leave_class_num'] = $dateexcelvar['leave_class_num'];
                    $datearray['student_insert_num'] = $dateexcelvar['student_insert_num'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "教师中文名", "教师英文名", "教师编号", "带班数", "带班总人数", "应到人次", "未考勤人次", "实到人次", "出勤率", "转入人数", "转出人数", "升班人数", "班内流失人数", "插班人数"));
            $excelfileds = array("school_branch", "school_cnname", 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'teaching_class_num', 'student_all_num', 'student_all_arrive_num', 'student_no_checking_num', 'student_is_checking_num', 'student_all_arrive_rate', 'to_class_num', 'out_class_num', 'up_class_num', 'leave_class_num', 'student_insert_num');
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_shortname'] . '教师带课明细表' . $file_name_add . '.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if ($list) {
                foreach ($list as &$one) {
                    $one['student_no_checking_num'] = $one['student_all_arrive_num'] - $one['student_is_checking_num'];
                    $one['student_all_arrive_rate'] = $one['student_all_arrive_num'] > 0 ? round($one['student_is_checking_num'] / $one['student_all_arrive_num'], 4) * 100 . '%' : '0%';
                }
            }
            $data = array();
            $count_sql = "SELECT COUNT(DISTINCT ch.staffer_id) AS teachnum FROM  smc_staffer as s
            left join smc_class_teach as ch ON s.staffer_id=ch.staffer_id
            left join smc_class as c ON c.class_id = ch.class_id
            left join smc_school as so ON c.school_id = so.school_id
            Left JOIN smc_course as sc ON sc.course_id=c.course_id
            where  ch.teach_type = '0' and s.company_id = '{$this->company_id}' and  {$datawhere}";
            $dbNums = $this->DataControl->selectOne($count_sql);
            if ($dbNums) {
                $allnum = $dbNums['teachnum'];
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;
            return $data;
        }
    }

    //学生考勤报表
    function studentCheckList($request)
    {
        $datawhere = "s.company_id = '{$this->company_id}'";

        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and h.hour_day >= '{$request['start_time']}' ";
            $week_start_day = $request['start_time'];
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and h.hour_day <= '{$request['end_time']}' ";
            $week_end_day = $request['end_time'];
        } else {
            $week_start_day = date("Y-m-d");
        }
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '{$request['keyword']}' or sc.class_cnname like '{$request['keyword']}' or sc.class_enname like '{$request['keyword']}' or sc.class_branch like '{$request['keyword']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        }

        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT ss.school_cnname,ss.school_shortname,ss.school_branch,s.student_cnname,s.student_enname,s.student_branch,sc.class_cnname,sc.class_enname,sc.class_branch,h.hour_day,sh.hourstudy_checkin,c.clockinginlog_note,
                (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = ss.school_province ) as province_name,
                (SELECT f.family_mobile FROM smc_student_family as f WHERE f.student_id = c.student_id AND f.family_isdefault = '1' AND f.family_iserror = '0') as family_mobile
                FROM smc_student_clockinginlog as c
                LEFT JOIN smc_student as s ON s.student_id = c.student_id
                LEFT JOIN smc_student_hourstudy as sh ON sh.hourstudy_id = c.hourstudy_id
                LEFT JOIN smc_class as sc ON sc.class_id = sh.class_id
                LEFT JOIN smc_class_hour as h ON h.hour_id = sh.hour_id
                LEFT JOIN smc_school as ss ON ss.school_id = sc.school_id
                WHERE {$datawhere} 
                ORDER BY  (case when ss.school_istest=0 and ss.school_isclose=0 then 1 when ss.school_isclose=0 then 2 when ss.school_istest=0 then 3 else 4 end),ss.school_istest asc,field(ss.school_sort,0),ss.school_sort asc,ss.school_createtime asc,ss.school_branch,s.student_id ASC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无考勤数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['family_mobile'] = $dateexcelvar['family_mobile'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    if ($dateexcelvar['hourstudy_checkin']) {
                        $datearray['hourstudy_checkin_name'] = $this->LgStringSwitch('出勤');
                    } else {
                        $datearray['hourstudy_checkin_name'] = $this->LgStringSwitch('缺勤');
                    }
                    $datearray['clockinginlog_note'] = $dateexcelvar['clockinginlog_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区名称", "校区编号", "学员中文名", "学员英文名", "学员编号", "班级名称", "班级别名", "班级编号", "主要联系方式", "上课日期", "出勤状态", "缺勤原因"));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'family_mobile', 'hour_day', 'hourstudy_checkin_name', 'clockinginlog_note');
            if (isset($request['end_time']) && $request['end_time'] != '') {
                $tem_name = "{$this->schoolOne['school_shortname']}学员考勤报表{$week_start_day}-{$week_end_day}.xls";
            } else {
                $tem_name = "{$this->schoolOne['school_shortname']}学员考勤报表{$week_start_day}.xls";
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }
            foreach ($dataList as &$v) {
                if ($v['hourstudy_checkin']) {
                    $v['hourstudy_checkin_name'] = $this->LgStringSwitch('出勤');
                } else {
                    $v['hourstudy_checkin_name'] = $this->LgStringSwitch('缺勤');
                }
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(c.clockinginlog_id) as num
                                                                FROM smc_student_clockinginlog as c
                                                                LEFT JOIN smc_student as s ON s.student_id = c.student_id
                                                                LEFT JOIN smc_student_hourstudy as sh ON sh.hourstudy_id = c.hourstudy_id
                                                                LEFT JOIN smc_class as sc ON sc.class_id = sh.class_id
                                                                LEFT JOIN smc_class_hour as h ON h.hour_id = sh.hour_id
                                                                WHERE {$datawhere}");
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $dataList;
            return $data;
        }
    }

    //电访明细表
    function catitrack($request)
    {
        $datawhere = "s.company_id = '{$this->company_id}' 
        AND t.student_id = s.student_id 
        AND t.result_id = cs.trackresult_id 
        AND t.staffer_id = f.staffer_id 
        AND c.commode_id = t.track_linktype 
        AND ct.tracktype_id = t.tracktype_id";

        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and t.track_day >= '{$request['start_time']}' ";
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and t.track_day <= '{$request['end_time']}' ";
        }
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%'
            or f.staffer_branch like '%{$request['keyword']}%'
            or f.staffer_cnname like '%{$request['keyword']}%'
            or f.staffer_enname like '%{$request['keyword']}%')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and t.coursetype_id = '{$request['coursetype_id']}' ";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and t.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['tracktype_id']) && $request['tracktype_id'] != '') {
            $datawhere .= " and t.tracktype_id = '{$request['tracktype_id']}'";
        }

        if (isset($request['result_id']) && $request['result_id'] != '') {
            $datawhere .= " and cs.trackresult_id = '{$request['result_id']}'";
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and t.school_id = '{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "SELECT s.student_id
                , s.student_cnname
                , s.student_branch
                , ct.tracktype_name
                , t.track_day
                , t.track_from
                , cs.trackresult_name as result_name
                , c.commode_name
                , t.track_note
                , t.track_createtime
                , f.staffer_branch
                , CONCAT(f.staffer_cnname,'-',f.staffer_enname) as staffer_cnname
                , ( SELECT p.post_name FROM gmc_company_post AS p, gmc_staffer_postbe AS b
                WHERE p.post_id = b.post_id AND b.staffer_id = f.staffer_id AND b.school_id = t.school_id ORDER BY b.postbe_ismianjob DESC LIMIT 0, 1 ) AS post_name
                ,(select concat(coursetype_branch,'-',coursetype_cnname) from smc_code_coursetype where coursetype_id=t.coursetype_id) as coursetype_name
                ,(select concat(coursecat_branch,'-',coursecat_cnname) from smc_code_coursecat where coursecat_id=t.coursecat_id) as coursecat_name
                ,(SELECT o.object_name FROM crm_code_object AS o WHERE o.object_code = t.track_code) as object_name
                FROM smc_student AS s, smc_student_track AS t, crm_code_commode AS c, smc_code_trackresult AS cs, smc_staffer AS f,smc_code_tracktype AS ct
                WHERE {$datawhere}
                ORDER BY track_createtime DESC";

        $track_from = $this->LgArraySwitch(array("0" => "校务中心", "1" => "教务中心"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无电访数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['track_from'] = $track_from[$dateexcelvar['track_from']];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['coursetype_name'] = $dateexcelvar['coursetype_name'];
                    $datearray['coursecat_name'] = $dateexcelvar['coursecat_name'];
                    $datearray['object_name'] = $dateexcelvar['object_name'];
                    $datearray['tracktype_name'] = $dateexcelvar['tracktype_name'];
                    $datearray['track_day'] = $dateexcelvar['track_day'];
                    $datearray['commode_name'] = $dateexcelvar['commode_name'];
                    $datearray['result_name'] = $dateexcelvar['result_name'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("数据来源", "学生名称", "学生编号", "沟通班组", "沟通班种", "沟通对象", "电访类型", "电访日期", "电访方式", "电访结果", "电访内容", "电访老师编号", "电访老师姓名", "电访人职务"));
            $excelfileds = array('track_from', 'student_cnname', 'student_branch', 'coursetype_name', 'coursecat_name', 'object_name', 'tracktype_name', 'track_day', 'commode_name', 'result_name', 'track_note', 'staffer_branch', 'staffer_cnname', 'post_name');
            $tem_name = "{$this->schoolOne['school_cnname']}电访明细表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }
            foreach ($dataList as &$var) {
                $var['track_from'] = $track_from[$var['track_from']];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.track_id) as num FROM
                                                                (SELECT t.track_id
                                                                FROM smc_student AS s, smc_student_track AS t, crm_code_commode AS c, smc_code_trackresult AS cs, smc_staffer AS f,smc_code_tracktype AS ct
                                                                WHERE {$datawhere}) AS q");
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $dataList;
            return $data;
        }
    }

    //在读新生统计表
    function readingStudent($request)
    {
        $datawhere = "1";
        $havingwhere = " ";
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (b.student_cnname like '%{$request['keyword']}%' 
            or b.student_enname like '%{$request['keyword']}%' 
            or b.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        } else {
            $datawhere .= " AND c.school_istest = '0' AND c.school_isclose = '0' ";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and c.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $datawhere .= " AND c.school_istest = '0' AND c.school_isclose = '0' ";
        }

        //班级筛选
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $havingwhere .= " and class_id='{$request['class_id']}'";
        }

        //课程别筛选
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and a.course_id='{$request['course_id']}'";
        }

        //班种筛选
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and a.coursecat_id='{$request['coursecat_id']}'";
        }

        //班组筛选
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}'";
        }

        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $starttime = strtotime($request['start_time']);
            $week_start_day = $request['start_time'];
        } else {
            $week_start_day = date("Y-m-d");
        }
//        $datawhere .= " and H.study_endday>='{$week_start_day}' ";
        $choose_begin_day = date('Y-m-d', strtotime("-1 year", strtotime($week_start_day)));

        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $week_end_day = $request['end_time'];
        } else {
            $week_end_day = date("Y-m-d");
        }
//        $datawhere .= " and H.study_beginday<='{$week_end_day}' ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.school_cnname,
            c.school_shortname,
            c.school_branch,
            ( SELECT r.region_name FROM smc_code_region AS r WHERE r.region_id = c.school_province ) AS province_name,
            ( SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = c.district_id ) AS district_cnname,
            b.student_cnname,
            b.student_enname,
            b.student_branch,
            e.channel_name,
            a.coursetype_cnname,
            a.coursetype_branch,
            a.coursecat_cnname,
            a.coursecat_branch,
            a.course_cnname,
            a.course_branch,
            (SELECT y.class_cnname FROM smc_student_study x,smc_class y,smc_course z 
                WHERE  x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id AND x.student_id = a.student_id  
                and x.study_endday >= '{$week_start_day}' and x.study_beginday <= '{$week_end_day}'
                order by study_id desc limit 0,1) as class_cnname,
            (SELECT y.class_enname FROM smc_student_study x,smc_class y,smc_course z 
                WHERE  x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id AND x.student_id = a.student_id  
                and x.study_endday >= '{$week_start_day}' and x.study_beginday <= '{$week_end_day}'
                order by study_id desc limit 0,1) as class_enname,
            (SELECT y.class_branch FROM smc_student_study x,smc_class y,smc_course z 
                WHERE  x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id AND x.student_id = a.student_id  
                and x.study_endday >= '{$week_start_day}' and x.study_beginday <= '{$week_end_day}'
                order by study_id desc limit 0,1) as class_branch,
            (SELECT y.class_id FROM smc_student_study x,smc_class y,smc_course z 
                WHERE  x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id AND x.student_id = a.student_id  
                and x.study_endday >= '{$week_start_day}' and x.study_beginday <= '{$week_end_day}'
                order by study_id desc limit 0,1) as class_id,
            d.coursebalance_figure,
            d.coursebalance_time,
            (SELECT x.study_beginday FROM smc_student_study x,smc_class y,smc_course z 
                WHERE  x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id AND x.student_id = a.student_id  
                and x.study_endday >= '{$week_start_day}' and x.study_beginday <= '{$week_end_day}'
                order by study_id desc limit 0,1) as study_beginday,
            (SELECT min(w.hour_day) FROM smc_class_hour w,smc_student_hourstudy x,smc_class y,smc_course z 
            WHERE w.hour_id = x.hour_id AND x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id
            AND x.student_id = a.student_id ) as hour_day
            from smc_student_registerinfo a
            left join smc_student b on a.student_id=b.student_id
            left join smc_school c on c.school_id=a.school_id
            LEFT JOIN smc_student_coursebalance d ON d.school_id = a.school_id 	AND d.student_id = a.student_id AND d.course_id = a.course_id 
            LEFT JOIN smc_student_guildpolicy e ON e.student_id = b.student_id
            where {$datawhere}
            and c.company_id='{$this->company_id}'
            and a.info_status=1
            and FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d') >= '{$choose_begin_day}'
            HAVING hour_day >= '{$week_start_day}' AND hour_day <= '{$week_end_day}' {$havingwhere}
            ORDER BY (case when c.school_istest=0 and c.school_isclose=0 then 1 when c.school_isclose=0 then 2 when c.school_istest=0 then 3 else 4 end)
              ,c.school_istest asc,field(c.school_sort,0),c.school_sort asc,c.school_createtime asc,c.school_branch,a.coursetype_id,a.coursecat_id,a.course_id";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无在读数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区名称", "校区编号", "学员中文名", "学员英文名", "学员编号", "专案名称", "入班日期", "第一次就读日期", "班组名称", "班种名称", "课程别名称", "课程别编号", "课程别剩余金额", "课程别剩余课次", "班级名称", "班级别名", "班级编号"));
            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', "channel_name", 'study_beginday', 'hour_day', 'coursetype_cnname', 'coursecat_cnname', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time', 'class_cnname', 'class_enname', 'class_branch');
            if (isset($request['end_time']) && $request['end_time'] != '') {
                $tem_name = "{$this->schoolOne['school_shortname']}在读新生统计报表{$week_start_day}-{$week_end_day}.xlsx";
            } else {
                $tem_name = "{$this->schoolOne['school_shortname']}在读新生统计报表{$week_start_day}.xlsx";
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.info_id) as num FROM
                    (select a.info_id,
                    (SELECT y.class_id FROM smc_student_study x,smc_class y,smc_course z 
                        WHERE  x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id AND x.student_id = a.student_id  
                        and x.study_endday >= '{$week_start_day}' and x.study_beginday <= '{$week_end_day}'
                        order by study_id desc limit 0,1) as class_id,
                    (SELECT min(w.hour_day) FROM smc_class_hour w,smc_student_hourstudy x,smc_class y,smc_course z 
                    WHERE w.hour_id = x.hour_id AND x.class_id = y.class_id AND y.course_id = z.course_id AND z.coursetype_id = a.coursetype_id
                    AND x.student_id = a.student_id ) as hour_day
                    from smc_student_registerinfo a
                    left join smc_student b on a.student_id=b.student_id
                    left join smc_school c on c.school_id=a.school_id
                    where {$datawhere}
                    and c.company_id='{$this->company_id}'
                    and a.info_status=1
                    and FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d') >= '{$choose_begin_day}'
                    HAVING hour_day >= '{$week_start_day}' AND hour_day <= '{$week_end_day}' {$havingwhere}
                    ) as q");
                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $dataList;
            return $data;
        }
    }

    //课时数据明细报表
    function classTimeDetail()
    {
        $sql = "select event_id as needtype_cnname, event_id as needtype_branch, event_id as count1, event_id as count2, event_id as percent, event_id as count3, event_id as count4, event_id as price1, event_id as price2 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //学费消耗明细报表
    function studentCourseConsumeDetl($request)
    {
        $datawhere = "B.company_id = '{$request['company_id']}' AND ( A.timelog_playname = '扣除课时次数' OR A.timelog_playname = '点名扣除课次' )";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (G.student_cnname like '%{$request['keyword']}%' 
            or G.student_enname like '%{$request['keyword']}%' 
            or G.student_idcard like '%{$request['keyword']}%' 
            or F.class_branch like '%{$request['keyword']}%' 
            or F.class_enname like '%{$request['keyword']}%' 
            or G.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and A.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and D.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
        } else {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
        }
        $datawhere .= " AND A.timelog_time <= '{$endtime}'";

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . '0:00:00');
        } else {
            $starttime = strtotime($request['start_time'] . '0:00:00');
        }
        $datawhere .= " AND  A.timelog_time >= '{$starttime}'";


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT H.school_id,H.school_branch,H.school_cnname,
			D.coursecat_id,
			A.course_id,
			A.student_id,
			G.student_branch,
			G.student_cnname,
			G.student_enname,
			E.coursetype_cnname,
			E.coursetype_branch,
			D.coursecat_cnname,
			D.coursecat_branch,
			C.course_cnname,
			C.course_branch,
			F.class_cnname,
			F.class_enname,
			F.class_branch,
			A.timelog_playtimes,
			(select r.region_name from smc_code_region as r where r.region_id = H.school_province ) as province_name,
			(SELECT SUM(l.log_playamount) FROM smc_student_coursebalance_log AS l
				WHERE l.class_id=a.class_id and l.course_id=a.course_id and l.school_id=a.school_id and l.student_id=a.student_id
				AND l.hourstudy_id = A.hourstudy_id AND ( l.log_playname = '点名扣除课程余额' OR l.log_playname = '扣除课程余额' )
				AND l.log_time <= '{$endtime}' AND  l.log_time >= '{$starttime}' AND l.log_playclass='-'
			) AS log_playamount,
			date_format(FROM_UNIXTIME(A.timelog_time),'%Y-%m-%d') as log_time
		FROM smc_student_coursebalance_timelog AS A
		LEFT JOIN smc_student_coursebalance AS B ON A.school_id = B.school_id AND A.student_id = B.student_id AND A.course_id = B.course_id
		LEFT JOIN smc_course AS C ON B.course_id = C.course_id AND C.company_id = B.company_id
		LEFT JOIN smc_code_coursecat D ON C.coursecat_id = D.coursecat_id AND D.company_id = C.company_id
		LEFT JOIN smc_code_coursetype E ON C.coursetype_id = E.coursetype_id AND E.company_id = C.company_id
		LEFT JOIN smc_class AS F ON F.class_id = A.class_id AND F.company_id = B.company_id
		LEFT JOIN smc_student G ON G.student_id=A.student_id AND G.company_id = B.company_id
		LEFT JOIN SMC_SCHOOL H ON A.SCHOOL_ID=H.SCHOOL_ID AND H.company_id = B.company_id
		WHERE {$datawhere}
		ORDER by (case when H.school_istest=0 and H.school_isclose=0 then 1 when H.school_isclose=0 then 2 when H.school_istest=0 then 3 else 4 end),H.school_istest asc,field(H.school_sort,0),H.school_sort asc,H.school_createtime asc,H.school_branch,E.coursetype_id,D.coursecat_id,A.course_id,F.class_id,A.student_id,A.timelog_time";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学生数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
//                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];//课程别编号

                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['timelog_playtimes'] = $dateexcelvar['timelog_playtimes'];
                    $datearray['log_playamount'] = $dateexcelvar['log_playamount'];
                    $datearray['log_time'] = $dateexcelvar['log_time'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "校区名称", "学生ID", "学员中文名", "学员英文名", "学员编号", "班组名称", "班组编号", "班种名称", "班种编号", "课程别名称", "课程别编号", "班级名称", "班级别名", "班级编号", "消耗课次", "消耗金额", "消耗时间"));
            $excelfileds = array('province_name', 'school_branch', 'school_cnname', 'student_id', 'student_cnname', 'student_enname', 'student_branch', "coursetype_cnname", "coursetype_branch", 'coursecat_cnname', 'coursecat_branch', 'course_cnname', 'course_branch', 'class_cnname', 'class_enname', 'class_branch', 'timelog_playtimes', 'log_playamount', 'log_time');

            $tem_name = $schoolOne['school_cnname'] . '学费消耗明细表' . $request['start_time'] . '~' . $request['end_time'] . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);

            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学生数据";
                return false;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select A.timelog_id
		FROM smc_student_coursebalance_timelog AS A
		LEFT JOIN smc_student_coursebalance AS B ON A.school_id = B.school_id AND A.student_id = B.student_id AND A.course_id = B.course_id
		LEFT JOIN smc_course AS C ON B.course_id = C.course_id AND C.company_id = B.company_id
		LEFT JOIN smc_code_coursecat D ON C.coursecat_id = D.coursecat_id AND D.company_id = C.company_id
		LEFT JOIN smc_code_coursetype E ON C.coursetype_id = E.coursetype_id AND E.company_id = C.company_id
		LEFT JOIN smc_class AS F ON F.class_id = A.class_id AND F.company_id = B.company_id
		LEFT JOIN smc_student G ON G.student_id=A.student_id AND G.company_id = B.company_id
		WHERE {$datawhere}");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;

            return $data;
        }
    }

    //学员课程余额统计报表
    function studentCourceSurplus()
    {
        $sql = "select event_id as needtype_cnname, event_id as needtype_branch, event_id as price1, event_id as price2, event_id as count1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //学员充值明细报表
    function studentInvest()
    {
        $sql = "select event_id as order_pid, event_id as student_cnname, event_id as student_enname, event_id as student_branch, event_id as order_allprice , event_id as order_status , event_id as order_createtime from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //课程冲销明细报表
    function courseCharge()
    {
        $sql = "select event_id as order_pid, event_id as student_cnname, event_id as student_enname, event_id as student_branch, event_id as order_allprice , event_id as order_status , event_id as count1 , event_id as course_cnname , event_id as date1, event_id as date2 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //班级上课报表
    function classCourse()
    {
        $sql = "select event_id as class_cnname, event_id as class_enname, event_id as class_branch, event_id as course_cnname, event_id as course_branch , event_id as date1, event_id as main_teacher, event_id as assistant_teacher , event_id as classroom_cnname , event_id as classroom_cnname, event_id as count1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //教师上课报表
    function teacherCourse()
    {
        $sql = "select event_id as staffer_cnname, event_id as staffer_enname, event_id as staffer_branch, event_id as type1, event_id as date1 , event_id as class_cnname, event_id as class_enname, event_id as class_branch from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //学生考勤报表
    function studentCheck()
    {
        $sql = "select event_id as staffer_cnname, event_id as staffer_enname, event_id as staffer_branch, event_id as type1, event_id as date1 , event_id as status, event_id as reason from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //领用相关报表
    function receiveGoods()
    {
        $sql = "select event_id as proorder_pid, event_id as staffer_cnname, event_id as staffer_enname, event_id as staffer_branch, event_id as goods_cnname , event_id as goods_pid, event_id as prodtype_name, event_id as count1, event_id as count2, event_id as remark, event_id as date1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //活动采购报表
    function activityGoods()
    {
        $sql = "select event_id as activity_name, event_id as activity_branch, event_id as goods_cnname, event_id as goods_branch, event_id as prodtype_code , event_id as count1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    function teacherClassCalc($request)
    {
        $where = "1";
        $where = "sc.school_id ='{$request['school_id']}' ";
        if (isset($request['start_time']) && $request['start_time']) {
            $where .= " and  sc.changelog_day >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time']) {
            $where .= " and  sc.changelog_day <='{$request['end_time']}'";
        }

        if (isset($request['keyword']) && $request['keyword']) {
            $where .= " and  (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select  s.student_id,s.student_cnname,s.student_enname,s.student_branch,
 			  	(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code
			  from smc_student as s

			  left join smc_student_enrolled as  sc ON  sc.student_id = s.student_id
			  where   {$where}    Having stuchange_code ='A07'  ";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无学员延班记录";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", '异动日期', '异动原因'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'changelog_day', 'changelog_note');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员延班报表.xlsx"));
            exit;
        } else {

            $sql .= " limit {$pagestart},{$num} ";
            $changeList = $this->DataControl->selectClear($sql);
            $data['allnum'] = 0;
            if ($request['is_count'] && $request['is_count'] == '1') {
                $all_num = $this->DataControl->selectClear("select  s.student_id,s.student_cnname,s.student_enname,s.student_branch,
 			  	(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code
			  from smc_student as s
			 
			  left join smc_student_enrolled as  sc ON  sc.student_id = s.student_id
			  where   {$where}    Having stuchange_code ='A07' limit {$pagestart},{$num}
			  ");

                $data['allnum'] = count($all_num) + 0;
            }

            $data['list'] = $changeList == false ? array() : $changeList;
            return $data;
        }
    }

    //在籍新生统计表报表 - 李（0325）
    function newStuEnrollment($request)
    {
        $datawhere = "  ";
        //关键词
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or t.student_enname like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%' )";
        }
        //开始时间
        if (isset($request['start_time']) && $request['start_time'] != '') {
            $querystartday = $request['end_time'];
        } else {
            $querystartday = date("Y-m-01");
        }
        //结束时间
        if (isset($request['end_time']) && $request['end_time'] != '') {
            $queryendday = $request['end_time'];
        } else {
            $queryendday = date("Y-m-d", time());
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        $starttime = strtotime($querystartday);
        $endtime = strtotime($queryendday . ' 23:59:59');

        //班种筛选
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and u.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        $sql = "select (select l.change_createtime from smc_student_changelog as l where l.student_id=se.student_id and l.school_id=se.school_id and l.stuchange_code in ('A01','A06','D02','F01') order by l.change_createtime desc limit 0,1) as inTime
//                ,(select l.change_createtime from smc_student_changelog as l where l.student_id=se.student_id and l.school_id=se.school_id and l.stuchange_code in ('B05','C02','C03','E02') order by l.change_createtime desc limit 0,1) as outTime
//                ,(select * from smc_payfee_order as po left join smc_payfee_order_course as oc on oc.order_pid=po.order_pid where oc.course_id>'0')
//                from smc_student_enrolled as se
//                left join smc_student as s on s.student_id=se.student_id
//                where {$datawhere} and
//                HAVING inTime<='{$endtime}' and outTime>='{$endtime}'
//                ";

        $sql = "select (select * from )
              from smc_student_trading as st
              left join smc_payfee_order as po on po.trading_pid=st.trading_pid
              where st.tradingtype_code in ('PayrenewFee','PaynewFee','CourseCatWash','CourseMakeUp') and st.company_id='{$this->company_id}'
              and po.order_createtime>='{$starttime}' and po.order_createtime<='{$endtime}'

             ";

        $sql = "select (select ss.study_beginday from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.course_id=scb.course_id and scb.school_id=ss.school_id and scb.student_id=ss.student_id)
              from smc_student_coursebalance as scb
              ";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无升班数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                $one['upgradelog_createtime'] = date("Y-m-d H:i:s", $one['upgradelog_createtime']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['from_class_cnname'] = $dateexcelvar['from_class_cnname'];
                    $datearray['from_class_enname'] = $dateexcelvar['from_class_enname'];
                    $datearray['from_class_branch'] = $dateexcelvar['from_class_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['to_class_cnname'] = $dateexcelvar['to_class_cnname'];
                    $datearray['to_class_enname'] = $dateexcelvar['to_class_enname'];
                    $datearray['to_class_branch'] = $dateexcelvar['to_class_branch'];
                    $datearray['upgradelog_createtime'] = $dateexcelvar['upgradelog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "来源班级名称", "来源班级别名", "来源班级编号", "班级教师", "升班班级名称", "升班班级别名", "升班班级编号", "升班时间"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'from_class_cnname', 'from_class_enname', 'from_class_branch', 'staffer_cnname', 'to_class_cnname', 'to_class_enname', 'to_class_branch', 'upgradelog_createtime');
            $tem_name = $this->schoolOne['school_cnname'] . '学员升班明细报表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无异动数据";
            return false;
        }
        foreach ($list as &$one) {
            $one['upgradelog_createtime'] = date("Y-m-d H:i:s", $one['upgradelog_createtime']);
        }
        $data = array();
        $count_sql = "SELECT  COUNT(s.student_id) as datanum
                FROM smc_class_upgradelog as s 
            LEFT JOIN smc_class as c ON c.class_id = s.from_class_id
            LEFT JOIN smc_class as a ON a.class_id = s.to_class_id
            LEFT JOIN smc_student as t ON t.student_id = s.student_id
            LEFT JOIN smc_staffer as f ON f.staffer_id = s.staffer_id 
            WHERE s.school_id = '{$request['school_id']}' 
            and s.company_id = '{$request['company_id']}'
            and s.upgradelog_status <> '0'
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $list;

        return $data;
    }

    function schoolCourseConsume($request)
    {

        $field = array();
        $k = 0;
        $where = " 1 ";

        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $startqueryday = $request['start_time'];
        } else {
            $startqueryday = date("Y-m-01");
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endqueryday = $request['end_time'];
        } else {
            $endqueryday = date("Y-m-t");
        }

        $startqueryTimes = strtotime($startqueryday);

        $endqueryTimes = strtotime($endqueryday) + (3600 * 24) - 1;

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $where .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $where .= " and A.school_id = '{$this->school_id}'";
        } else {
            $where .= " and sch.school_istest = '0'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $where .= " and sch.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $where .= " AND sch.school_istest <> '1' AND sch.school_isclose<> '1' ";
        }

        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
            $where .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        $wheresql = "SELECT
			D.coursecat_id,
			E.coursetype_id,
			A.course_id,
			A.school_id,
			A.student_id,
			C.course_cnname,
			C.course_branch,
			D.coursecat_cnname,
			D.coursecat_branch,
			E.coursetype_cnname,
			E.coursetype_branch,sch.school_cnname,sch.school_branch,sch.school_istest,sch.school_isclose,sch.school_sort,sch.school_createtime,
			(SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = sch.school_province ) as province_name,
			SUM(A.timelog_playtimes) as timelog_playtimes,
			(
				SELECT
					SUM(l.log_playamount)
				FROM
					smc_student_coursebalance_log AS l
				WHERE
					1
				AND l.log_time >= '{$startqueryTimes}'
				AND l.log_time <= '{$endqueryTimes}'
				AND l.school_id = A.school_id
				AND l.course_id = A.course_id
				AND l.student_id = A.student_id
				AND (
					l.log_playname = '点名扣除课程余额'
					OR l.log_playname = '扣除课程余额'
				)
			) AS log_playamount
		FROM
			smc_student_coursebalance_timelog AS A
		LEFT JOIN smc_student_coursebalance AS B ON A.school_id = B.school_id
		AND A.student_id = B.student_id
		AND A.course_id = B.course_id
		LEFT JOIN smc_course AS C ON B.course_id = C.course_id
		AND C.company_id = B.company_id
		LEFT JOIN smc_code_coursecat D ON C.coursecat_id = D.coursecat_id
		AND D.company_id = C.company_id
		LEFT JOIN smc_code_coursetype E ON C.coursetype_id = E.coursetype_id
		AND E.company_id = C.company_id
		LEFT JOIN smc_school as sch on sch.school_id=A.school_id
		WHERE
			{$where}
		AND A.timelog_time >= '{$startqueryTimes}'
		AND A.timelog_time <= '{$endqueryTimes}'
		AND B.company_id = '{$this->company_id}'
		AND (
			A.timelog_playname = '扣除课时次数'
			OR A.timelog_playname = '点名扣除课次'
		)
		group by sch.school_id,E.coursetype_id,D.coursecat_id,A.course_id,A.student_id
		";


        if (isset($request['type']) && $request['type'] == '3') {
            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = $this->LgStringSwitch("省份");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = $this->LgStringSwitch("校区名称");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = $this->LgStringSwitch("校区编号");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "cnname";
            $field[$k]["fieldname"] = $this->LgStringSwitch("课程别名称");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "branch";
            $field[$k]["fieldname"] = $this->LgStringSwitch("课程别编号");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (TA.course_cnname like '%{$request['keyword']}%' or TA.course_branch like '%{$request['keyword']}%')";
            }

            $sql = "select TA.course_id as id,TA.course_cnname as cnname,TA.course_branch as branch,SUM(TA.log_playamount) AS log_playamount,SUM(TA.timelog_playtimes) AS timelog_playtimes,TA.school_cnname,TA.school_branch
                    from ($wheresql) as TA
                    where {$datawhere}
                    GROUP BY
	                TA.school_id,TA.course_id
	                order by (case when TA.school_istest=0 and TA.school_isclose=0 then 1 when TA.school_isclose=0 then 2 when TA.school_istest=0 then 3 else 4 end),TA.school_istest asc,field(TA.school_sort,0),TA.school_sort asc,TA.school_createtime asc
              ";
        } elseif (isset($request['type']) && $request['type'] == '2') {

            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = $this->LgStringSwitch("省份");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = $this->LgStringSwitch("校区名称");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = $this->LgStringSwitch("校区编号");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "cnname";
            $field[$k]["fieldname"] = $this->LgStringSwitch("班种名称");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "branch";
            $field[$k]["fieldname"] = $this->LgStringSwitch("班种编号");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (TA.coursecat_cnname like '%{$request['keyword']}%' or TA.coursecat_branch like '%{$request['keyword']}%')";
            }
            $sql = "select TA.coursecat_id as id,TA.coursecat_cnname as cnname,TA.coursecat_branch as branch,SUM(TA.log_playamount) AS log_playamount,SUM(TA.timelog_playtimes) AS timelog_playtimes,TA.school_cnname,TA.school_branch
                    from ($wheresql) as TA
                    where {$datawhere}
                    GROUP BY
	                TA.school_id,TA.coursecat_id
	                order by (case when TA.school_istest=0 and TA.school_isclose=0 then 1 when TA.school_isclose=0 then 2 when TA.school_istest=0 then 3 else 4 end),TA.school_istest asc,field(TA.school_sort,0),TA.school_sort asc,TA.school_createtime asc
              ";
        } elseif (isset($request['type']) && $request['type'] == '1') {
            $field[$k]["fieldstring"] = "province_name";
            $field[$k]["fieldname"] = $this->LgStringSwitch("省份");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = $this->LgStringSwitch("校区名称");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = $this->LgStringSwitch("校区编号");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "cnname";
            $field[$k]["fieldname"] = $this->LgStringSwitch("班组名称");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "branch";
            $field[$k]["fieldname"] = $this->LgStringSwitch("班组编号");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            if (isset($request['keyword']) && $request['keyword'] !== '') {
                $datawhere .= " and (TA.coursetype_cnname like '%{$request['keyword']}%' or TA.coursetype_branch like '%{$request['keyword']}%')";
            }

            $sql = "select TA.coursetype_id as id,TA.coursetype_cnname as cnname,TA.coursetype_branch as branch,SUM(TA.log_playamount) AS log_playamount,SUM(TA.timelog_playtimes) AS timelog_playtimes,TA.school_cnname,TA.school_branch
                    from ($wheresql) as TA
                    where {$datawhere}
                    GROUP BY
	                TA.school_id,TA.coursetype_id
	                order by (case when TA.school_istest=0 and TA.school_isclose=0 then 1 when TA.school_isclose=0 then 2 when TA.school_istest=0 then 3 else 4 end),TA.school_istest asc,field(TA.school_sort,0),TA.school_sort asc,TA.school_createtime asc
              ";
        } else {
            $this->error = true;
            $this->errortip = "请选择对应课程别或班种或班组";
            return false;
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无消耗数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['cnname'] = $dateexcelvar['cnname'];
                    $datearray['branch'] = $dateexcelvar['branch'];
                    $datearray['log_playamount'] = $dateexcelvar['log_playamount'];
                    $datearray['timelog_playtimes'] = $dateexcelvar['timelog_playtimes'];
                    $outexceldate[] = $datearray;
                }
            }

            if (isset($request['type']) && $request['type'] == '3') {
                $excelheader = array("省份", "校区名称", "校区编号", "课程别名称", "课程别编号", "总收入金额", "总收入课次");
            } elseif (isset($request['type']) && $request['type'] == '2') {
                $excelheader = array("省份", "校区名称", "校区编号", "班种名称", "班种编号", "总收入金额", "总收入课次");
            } elseif (isset($request['type']) && $request['type'] == '1') {
                $excelheader = array("省份", "校区名称", "校区编号", "班组名称", "班组编号", "总收入金额", "总收入课次");
            } else {
                $this->error = true;
                $this->errortip = "请选择对应课程别或班种或班组";
                return false;
            }

            $excelfileds = array('province_name', 'school_cnname', 'school_branch', 'cnname', 'branch', 'log_playamount', 'timelog_playtimes');
            query_to_excel($this->LgArraySwitch($excelheader), $outexceldate, $excelfileds, $this->LgStringSwitch('学员课程消耗统计表.xlsx'));
            exit;
        } else {

            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data = array();
            $data['allnum'] = $allnum;

            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无消耗数据";
                return false;
            }

            $field[$k]["fieldstring"] = "log_playamount";
            $field[$k]["fieldname"] = $this->LgStringSwitch("总收入金额");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "timelog_playtimes";
            $field[$k]["fieldname"] = $this->LgStringSwitch("总收入课次");
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;


            $data['field'] = $field;
            $data['list'] = $list;

            return $data;

        }


    }


    //借调相关报表
    function transferGoods($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$request['keyword']}%' or g.goods_pid like '%{$request['keyword']}%' or p.proorder_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['prodtype_code']) && $request['prodtype_code'] !== '') {
            $datawhere .= " and g.prodtype_code='{$request['prodtype_code']}'";
        }
        if (isset($request['from_school_id']) && $request['from_school_id'] !== "") {
            $datawhere .= " and p.from_school_id ='{$request['from_school_id']}'";
        }
//        if (isset($request['to_school_id']) && $request['to_school_id'] !== "") {
//            $datawhere .= " and p.to_school_id ='{$request['to_school_id']}'";
//        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and p.proorder_createtime <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and p.proorder_createtime >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
              SELECT
                s.school_cnname as to_school_cnname,
                sc.school_cnname as from_school_cnname,
                p.proorder_pid,
                g.goods_cnname,
                g.goods_pid,
                pr.prodtype_name,
                g.goods_unit,
                pg.proogoods_buynums,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                FROM_UNIXTIME( p.proorder_updatetime, '%Y-%m-%d' ) AS proorder_updatetime
            FROM
                erp_proorder_goods AS pg 
                left join erp_proorder as p on pg.proorder_pid = p.proorder_pid
                left join smc_school as s on s.school_id = p.to_school_id
                left join smc_school as sc on sc.school_id = p.from_school_id
                left join erp_goods as g on pg.goods_id = g.goods_id
                left join smc_code_prodtype as pr on pr.prodtype_code = g.prodtype_code
            WHERE
                p.proorder_from = '4' and {$datawhere}
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['to_school_cnname'] = $dateexcelvar['to_school_cnname'];
                    $datearray['from_school_cnname'] = $dateexcelvar['from_school_cnname'];
                    $datearray['proorder_pid'] = $dateexcelvar['proorder_pid'];
                    $datearray['goods_cnname'] = $dateexcelvar['goods_cnname'];
                    $datearray['goods_pid'] = $dateexcelvar['goods_pid'];
                    $datearray['prodtype_name'] = $dateexcelvar['prodtype_name'];
                    $datearray['goods_unit'] = $dateexcelvar['goods_unit'];
                    $datearray['proogoods_buynums'] = $dateexcelvar['proogoods_buynums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("借入分校", "借出分校", "采购单号", "货品名称", "货品编号", '货品类型', '单位', "数量"));
            $excelfileds = array('to_school_cnname', 'from_school_cnname', 'proorder_pid', 'goods_cnname', 'goods_pid', 'prodtype_name', 'goods_unit', 'proogoods_buynums');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("借调相关报表统计报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                    SELECT
                s.school_cnname as to_school_cnname,
                sc.school_cnname as from_school_cnname,
                p.proorder_pid,
                g.goods_cnname,
                g.goods_pid,
                pr.prodtype_name,
                g.goods_unit,
                pg.proogoods_buynums,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                FROM_UNIXTIME( p.proorder_updatetime, '%Y-%m-%d' ) AS proorder_updatetime
            FROM
                erp_proorder_goods AS pg 
                left join erp_proorder as p on pg.proorder_pid = p.proorder_pid
                left join smc_school as s on s.school_id = p.to_school_id
                left join smc_school as sc on sc.school_id = p.from_school_id
                left join erp_goods as g on pg.goods_id = g.goods_id
                left join smc_code_prodtype as pr on pr.prodtype_code = g.prodtype_code
            WHERE
                p.proorder_from = '4' and {$datawhere}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }
    }

    //学员教材购买报表
    function StuMaterialReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sg.beoutorder_pid like '%{$request['keyword']}%' or g.goods_cnname like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and sg.school_id='{$request['school_id']}'";
        }
        if (isset($request['prodtype_code']) && $request['prodtype_code'] !== '') {
            $datawhere .= " and g.prodtype_code='{$request['prodtype_code']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and  FROM_UNIXTIME( sg.erpgoods_createtime, '%Y-%m-%d' ) <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and FROM_UNIXTIME( sg.erpgoods_createtime, '%Y-%m-%d' ) >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
              SELECT
                    s.student_cnname,
                    s.student_enname,
                    s.student_branch,
                    sg.beoutorder_pid,
                    g.goods_cnname,
                    g.goods_pid,
                    p.prodtype_name,
                    g.goods_unit,
                    g.goods_vipprice,
                    og.ordergoods_buynums,
                    g.goods_vipprice * og.ordergoods_buynums as allprice
                FROM
                    smc_student_erpgoods AS sg
                    LEFT JOIN smc_student AS s ON sg.student_id = s.student_id
                    LEFT JOIN erp_goods AS g ON g.goods_id = sg.goods_id
                    LEFT JOIN smc_code_prodtype AS p ON p.prodtype_code = g.prodtype_code 
                    AND g.company_id = p.company_id 
                    left join smc_payfee_order_goods as og on og.order_pid = sg.order_pid
                WHERE
                    {$datawhere}
                    	GROUP BY erpgoods_id
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['beoutorder_pid'] = $dateexcelvar['beoutorder_pid'];
                    $datearray['goods_cnname'] = $dateexcelvar['goods_cnname'];
                    $datearray['goods_pid'] = $dateexcelvar['goods_pid'];
                    $datearray['prodtype_name'] = $dateexcelvar['prodtype_name'];
                    $datearray['goods_unit'] = $dateexcelvar['goods_unit'];
                    $datearray['goods_vipprice'] = $dateexcelvar['goods_vipprice'];
                    $datearray['ordergoods_buynums'] = $dateexcelvar['ordergoods_buynums'];
                    $datearray['allprice'] = $dateexcelvar['allprice'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "出库编号", "货品名称", '货品编号', '货品类别', "单位", "销售价格", "购买数量", "销售金额"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'beoutorder_pid', 'goods_cnname', 'goods_pid', 'prodtype_name', 'goods_unit', 'goods_vipprice', 'ordergoods_buynums', 'allprice');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("学员教材购买统计报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                    SELECT
                    s.student_cnname,
                    s.student_enname,
                    s.student_branch,
                    sg.beoutorder_pid,
                    g.goods_cnname,
                    g.goods_pid,
                    p.prodtype_name,
                    g.goods_unit,
                    g.goods_vipprice,
                    og.ordergoods_buynums,
                    g.goods_vipprice * og.ordergoods_buynums as allprice
                FROM
                    smc_student_erpgoods AS sg
                    LEFT JOIN smc_student AS s ON sg.student_id = s.student_id
                    LEFT JOIN erp_goods AS g ON g.goods_id = sg.goods_id
                    LEFT JOIN smc_code_prodtype AS p ON p.prodtype_code = g.prodtype_code 
                    AND g.company_id = p.company_id 
                    left join smc_payfee_order_goods as og on og.order_pid = sg.order_pid
                WHERE
                    {$datawhere}
                    	GROUP BY erpgoods_id";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }


    }

    //进销存汇总报表
    function SalesOrderReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sg.beoutorder_pid like '%{$request['keyword']}%' or g.goods_cnname like '%{$request['keyword']}%')";
        }
        if (isset($request['prodtype_code']) && $request['prodtype_code'] !== '') {
            $datawhere .= " and g.prodtype_code='{$request['prodtype_code']}'";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and s.salesorder_createtime <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and s.salesorder_createtime >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
              SELECT
	g.goods_cnname,
	g.goods_pid,
	p.prodtype_name,
	g.goods_unit,
	r.goods_repertory,
	g.goods_vipprice,
	r.goods_repertory * g.goods_vipprice as allprice
FROM
	smc_erp_salesorder AS s
	LEFT JOIN smc_erp_salesorder_goods AS sg ON sg.salesorder_pid = s.salesorder_pid
	LEFT JOIN erp_goods AS g ON sg.goods_id = g.goods_id
	LEFT JOIN smc_code_prodtype AS p ON p.prodtype_code = g.prodtype_code
	LEFT JOIN smc_erp_goods_repertory AS r ON r.school_id = s.school_id 
	AND r.goods_id = sg.goods_id 
WHERE
    {$datawhere}
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['goods_cnname'] = $dateexcelvar['goods_cnname'];
                    $datearray['goods_pid'] = $dateexcelvar['goods_pid'];
                    $datearray['prodtype_name'] = $dateexcelvar['prodtype_name'];
                    $datearray['goods_unit'] = $dateexcelvar['goods_unit'];
                    $datearray['goods_repertory'] = $dateexcelvar['goods_repertory'];
                    $datearray['goods_vipprice'] = $dateexcelvar['goods_vipprice'];
                    $datearray['allprice'] = $dateexcelvar['allprice'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("货品名称", "货品编号", "货品类别", "单位", "库存数量", '货品价格', '总金额'));
            $excelfileds = array('goods_cnname', 'goods_pid', 'prodtype_name', 'goods_unit', 'goods_repertory', 'goods_vipprice', 'allprice');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("进销存汇总统计报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                   SELECT
	g.goods_cnname,
	g.goods_pid,
	p.prodtype_name,
	g.goods_unit,
	r.goods_repertory,
	g.goods_vipprice,
	r.goods_repertory * g.goods_vipprice as allprice
FROM
	smc_erp_salesorder AS s
	LEFT JOIN smc_erp_salesorder_goods AS sg ON sg.salesorder_pid = s.salesorder_pid
	LEFT JOIN erp_goods AS g ON sg.goods_id = g.goods_id
	LEFT JOIN smc_code_prodtype AS p ON p.prodtype_code = g.prodtype_code
	LEFT JOIN smc_erp_goods_repertory AS r ON r.school_id = s.school_id 
	AND r.goods_id = sg.goods_id 
WHERE
    {$datawhere}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }


    }

    //活动采购报表
    function ActivityBuyReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (go.goods_cnname like '%{$request['keyword']}%' or go.goods_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['prodtype_code']) && $request['prodtype_code'] !== '') {
            $datawhere .= " and go.prodtype_code='{$request['prodtype_code']}'";
        }
        if (isset($request['activitybuy_id']) && $request['activitybuy_id'] !== '') {
            $datawhere .= " and a.activitybuy_id='{$request['activitybuy_id']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and p.proorder_updatetime <= '{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and p.proorder_updatetime >= '{$request['start_time']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
              SELECT
	a.activitybuy_name,
	go.goods_cnname,
	go.goods_pid,
	pr.prodtype_name,
	go.goods_unit,
	g.proogoods_buynums
FROM
	erp_proorder AS p
	LEFT JOIN gmc_company_activitybuy AS a ON p.activitybuy_id = a.activitybuy_id 
	left join erp_proorder_goods as g on g.proorder_pid = p.proorder_pid
	left join erp_goods as go on go.goods_id = g.goods_id
	left join smc_code_prodtype as pr on pr.prodtype_code = go.prodtype_code
WHERE
	p.to_school_id = '{$request['school_id']}' 
	AND p.proorder_from = '2' and {$datawhere}
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['goods_cnname'] = $dateexcelvar['goods_cnname'];
                    $datearray['goods_pid'] = $dateexcelvar['goods_pid'];
                    $datearray['prodtype_name'] = $dateexcelvar['prodtype_name'];
                    $datearray['goods_unit'] = $dateexcelvar['goods_unit'];
                    $datearray['goods_repertory'] = $dateexcelvar['goods_repertory'];
                    $datearray['goods_vipprice'] = $dateexcelvar['goods_vipprice'];
                    $datearray['allprice'] = $dateexcelvar['allprice'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("活动名称", "货品名称", "货品编号", "单位", "库存数量", '货品价格', '总金额'));
            $excelfileds = array('goods_cnname', 'goods_pid', 'prodtype_name', 'goods_unit', 'goods_repertory', 'goods_vipprice', 'allprice');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("活动采购统计报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "
                   SELECT
	a.activitybuy_name,
	go.goods_cnname,
	go.goods_pid,
	pr.prodtype_name,
	go.goods_unit,
	g.proogoods_buynums
FROM
	erp_proorder AS p
	LEFT JOIN gmc_company_activitybuy AS a ON p.activitybuy_id = a.activitybuy_id 
	left join erp_proorder_goods as g on g.proorder_pid = p.proorder_pid
	left join erp_goods as go on go.goods_id = g.goods_id
	left join smc_code_prodtype as pr on pr.prodtype_code = go.prodtype_code
WHERE
	p.to_school_id = '{$request['school_id']}' 
	AND p.proorder_from = '2' and {$datawhere}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }


    }

    function registeredStu($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and b.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        /*,(select s.stuchange_name from smc_student_changelog as cl left join smc_code_stuchange as s on s.stuchange_code=cl.stuchange_code
                    where cl.student_id=a.student_id and cl.school_id=a.school_id order by cl.changelog_day desc,cl.changelog_id desc limit 0,1) as stuchange_name
                    ,(select cl.changelog_day from smc_student_changelog as cl left join smc_code_stuchange as s on s.stuchange_code=cl.stuchange_code
                    where cl.student_id=a.student_id and cl.school_id=a.school_id order by cl.changelog_day desc,cl.changelog_id desc limit 0,1) as changelog_day*/

        $sql = "SELECT c.coursetype_cnname,st.student_cnname,st.student_enname,st.student_branch,ifnull(sp.channel_name,'') as channel_name,
                    sum(a.coursebalance_figure) as coursebalance_figure
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                LEFT JOIN smc_code_coursetype c ON c.coursetype_id = b.coursetype_id
                left join smc_student as st on st.student_id=a.student_id
                left join smc_student_guildpolicy as sp on sp.student_id=st.student_id and sp.guildpolicy_enddate>=CURDATE()
                WHERE {$datawhere} and a.school_id='{$this->school_id}' and a.coursebalance_figure > 0
                GROUP BY a.school_id, a.student_id, b.coursetype_id ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    /*$datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];*/
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("班组名称", "学生中文名", "学生英文名", "学生编号", "专案名称", '课组剩余金额'));
            $excelfileds = array('coursetype_cnname', 'student_cnname', 'student_enname', 'student_branch', 'channel_name', 'coursebalance_figure');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("在籍学生明细表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            $count_sql = "
               SELECT a.coursebalance_id
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                left join smc_student as st on st.student_id=a.student_id
                WHERE {$datawhere} and a.school_id='{$this->school_id}' and a.coursebalance_figure > 0
                GROUP BY a.school_id, a.student_id, b.coursetype_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $data['allnum'] = $db_nums ? count($db_nums) : 0;

            $data['list'] = $studentList;

            return $data;
        }
    }

    function studyingStu($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%')";
        }
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $datawhere .= " and d.study_beginday<='{$request['fixedtime']}'";
            $datawhere .= " and d.study_endday>='{$request['fixedtime']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and r.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.class_branch, c.class_enname, s.student_branch, s.student_cnname, s.student_enname, d.study_beginday, d.study_endday,
                     (SELECT ifnull(sp.channel_name,'---')  FROM smc_student_guildpolicy as sp on sp.student_id=s.student_id and sp.guildpolicy_enddate>=CURDATE()) as channel_name,
                     cc.coursetype_cnname
                    FROM smc_student AS s, smc_school AS l,
                     smc_student_study AS d, smc_class AS c,
                     smc_course AS r, smc_code_coursetype as cc
                    WHERE {$datawhere}
                    and s.student_id = d.student_id
                    AND d.school_id = l.school_id
                    AND d.class_id = c.class_id
                    AND c.course_id = r.course_id
                    AND cc.coursetype_id = r.coursetype_id
                    AND d.school_id = '{$this->school_id}'
                    GROUP BY d.student_id
                    ORDER BY c.class_id DESC ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['study_beginday'] = $dateexcelvar['study_beginday'];
                    $datearray['study_endday'] = $dateexcelvar['study_endday'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学生编号", '学生中文名', '学生英文名', "专案名称", "班组名称", "班级编号", "班级别名", '入班时间', '离班时间'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'channel_name', 'coursetype_cnname', 'class_branch', 'class_enname', 'study_beginday', 'study_endday');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("在读学生明细表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $data = array();

            $count_sql = "SELECT l.school_id FROM smc_student AS s, smc_school AS l, smc_student_study AS d, smc_class AS c, smc_course AS r
                    WHERE {$datawhere} AND s.student_id = d.student_id AND d.school_id = l.school_id
                    AND d.class_id = c.class_id AND c.course_id = r.course_id AND d.school_id = '{$this->school_id}'
                    GROUP BY d.student_id ORDER BY c.class_id DESC";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $data['allnum'] = $db_nums ? count($db_nums) : 0;

            $data['list'] = $studentList;

            return $data;
        }
    }


    //教师类型下拉
    function getTeachtypeApi($paramArray)
    {
        $sql = "select teachtype_code,teachtype_name from smc_code_teachtype where company_id = '{$paramArray['company_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["teachtype_code"] = $this->LgStringSwitch("教师类型编号");
        $field["teachtype_name"] = $this->LgStringSwitch("教师类型名称");
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '教师类型下拉查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '教师类型下拉查看失败', 'result' => $result);
        }
        return $res;
    }


}
