<?php


namespace Model\Smc;

class RefundmentModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray=array()) {
        parent::__construct ();
        if(is_array($publicarray)){
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray){
        if(isset($publicarray['company_id'])){
            $this->company_id = $publicarray['company_id'];
        }else{
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if(isset($publicarray['school_id'])){
            $this->school_id = $publicarray['school_id'];
        }else{
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if(isset($publicarray['staffer_id'])){
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id=$publicarray['staffer_id'];
        }else{
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($staffer_id){

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname,staffer_enname,staffer_mobile","staffer_id = '{$staffer_id}'");

        if(!$this->stafferOne){
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }else{
            return true;
        }
    }


    function getRefundList($request){
        $time=date("Y-m-d",time());
        $datawhere=" 1 ";
        if(isset($request['coursetype_list']) && $request['coursetype_list']!=''){
            $coursetypeList=explode(",",$request['coursetype_list']);
            $like=" (1<0) ";
            foreach($coursetypeList as $courseOne){
                $like.=" or (sc.coursetype_id ='{$courseOne}') ";
            }
            $datawhere.=" and ($like)";
        }

        $sql="select sc.course_id,sc.course_cnname,sc.course_branch,scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price
              ,(select cp.pricinglog_refunddeadline from smc_student_coursebalance_pricinglog as cp where cp.student_id=scb.student_id and cp.school_id=scb.school_id and cp.course_id=scb.course_id and cp.pricing_id=scb.pricing_id order by pricinglog_id desc limit 0,1) as refunddeadline
              ,ifnull((select x.mergeorder_pid from smc_payfee_order as x,smc_payfee_order_course as y where y.order_pid=x.order_pid and x.school_id=scb.school_id and x.student_id=scb.student_id and y.course_id=scb.course_id and x.order_status>0 order by x.order_id desc limit 0,1),'') as mergeorder_pid
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scb.student_id=scf.student_id
              left join smc_course as sc on scb.course_id=sc.course_id
              left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
              where {$datawhere} and sc.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_time>0 or scb.coursebalance_figure>0) and sse.enrolled_status>=0 and sc.course_inclasstype<>'1'
              HAVING (refunddeadline='' or refunddeadline>='{$time}' or refunddeadline is null) and mergeorder_pid=''
              order by scb.coursebalance_createtime DESC
        ";
//        HAVING order_status=4
        $classList=$this->DataControl->selectClear($sql);

        if(!$classList){
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        foreach($classList as &$val){
        	$val['refund_price'] = $val['coursebalance_figure'];
            $sql="select c.class_id,c.class_cnname,c.class_branch
                  ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=1) as classNum
                  ,(select count(ssh.hourstudy_id) from smc_student_hourstudy as ssh where ssh.class_id=c.class_id and ssh.hourstudy_checkin=1 and ssh.student_id=sss.student_id) as stuNum
                  from smc_student_study as sss
                  left join smc_class as c on sss.class_id=c.class_id
                  where c.company_id='{$request['company_id']}' and c.school_id='{$request['school_id']}' and sss.student_id='{$request['student_id']}' and c.course_id='{$val['course_id']}'
                  ";
            $studentOne=$this->DataControl->selectOne($sql);
            if($studentOne){
                $val['class_id']=$studentOne['class_id'];
                $val['class_cnname']=$studentOne['class_cnname'];
                $val['class_branch']=$studentOne['class_branch'];
                $val['classNum']=$studentOne['classNum'];
                $val['stuNum']=$studentOne['stuNum'];
            }else{
                $val['class_id']='--';
                $val['class_cnname']='--';
                $val['class_branch']='--';
                $val['classNum']='--';
                $val['stuNum']='--';
            }
        }

        return $classList;
    }

    function refund($request){

        if(!isset($request['remark']) || $request['remark']==''){
            $this->error = true;
            $this->errortip = "订单备注必填";
            return false;
        }


        $RefundModel = new \Model\Smc\RefundModel($request);
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $sql="select po.order_id from smc_payfee_order as po where po.student_id='{$request['student_id']}' and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<'4' and po.order_status>='0'";

        $orderOne=$this->DataControl->selectOne($sql);

        if($orderOne){
            $this->error = true;
            $this->errortip = "学员存在未完成订单,不可退费！";
            return false;
        }

        $refundList=json_decode(stripslashes($request['refundList']),true);
        if(!$refundList){
            $this->error = true;
            $this->errortip = "无退款信息";
            return false;
        }

        $list=json_decode(stripslashes($request['list']),true);

        if($list){
            foreach($list as $val){
                $res=$BalanceModel->carryOver($request['student_id'],$val['course_id'],$val['class_id']);
                if(!$res){
                    $this->error = true;
                    $this->errortip = $BalanceModel->errortip;
                    return false;
                }
            }
        }

        if($refundList){

            foreach($refundList as $refundOne){
                $stublcOne = $this->getStuBalance($request['student_id'],$this->company_id,$this->school_id,$refundOne['companies_id']);

                if($stublcOne['student_balance']<$refundOne['refund_price']){
                    $this->error = true;
                    $this->errortip = "退费金额不可超过已有金额";
                    return false;
                }
            }

            foreach($refundList as $refundOne){
                $RefundModel->refund($request['student_id'],$request['bank'],$request['bankcard'],$request['accountname'],$this->LgStringSwitch('申请退款'),$this->LgStringSwitch('订单提交成功，等待校长审核'),$this->LgStringSwitch($request['remark']),strtotime($request['create_time']),$request['is_clear'],$refundOne['refund_price'],$refundOne['specialprice'],$refundOne['refund_specialreason'],$refundOne['companies_id'],$request['refund_reasontype'],$request['refund_bankcardurl']);
            }
        }

        return true;

    }

    function stuCourseType($request){
        $time=date("Y-m-d",time());
        $sql="select cc.coursetype_id,cc.coursetype_cnname,cc.coursetype_branch
,(select cp.pricinglog_refunddeadline from smc_student_coursebalance_pricinglog as cp where cp.student_id=scb.student_id and cp.school_id=scb.school_id and cp.course_id=scb.course_id and cp.pricing_id=scb.pricing_id order by pricinglog_id desc limit 0,1) as refunddeadline
              from smc_student_coursebalance as scb
              left join smc_course as sc on scb.course_id=sc.course_id
              left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
              left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
              where sc.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_time>0 or scb.coursebalance_figure>0) and sse.enrolled_status>=0 AND sc.course_inclasstype <> '1'
              group by sc.coursetype_id
              HAVING (refunddeadline='' or refunddeadline>='{$time}' or refunddeadline is null)
              order by scb.coursebalance_id DESC
        ";
        $courseList=$this->DataControl->selectClear($sql);
        if(!$courseList){
            $this->error = true;
            $this->errortip = "学员无可结转课程";
            return false;
        }

        return $courseList;

    }
}