<?php


namespace Model\Smc;

class SuperviseModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getTransferBill($request){


        if(!isset($request['agencyId']) || $request['agencyId']==''){
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        $sql = "select A.batch_pid
                from cmb_trans_transfer_batch as A,cmb_trans_transfer as B 
                where A.batch_pid=B.batch_pid and A.agency_id='{$request['agencyId']}' and A.batch_pid<>'' and B.subTransferId<>''
                group by A.batch_pid
                ";

        $batchList=$this->DataControl->selectClear($sql);
        if(!$batchList){
            $this->error = true;
            $this->errortip = "无划拨数据";
            return false;
        }

        $tem_array=array();
        foreach($batchList as $batchOne){
            $Model = new \Model\Api\CmbTransModel($request['agencyId']);

            $listOne = $Model->queryTransferBill($batchOne['batch_pid']);

            if($listOne){

                $companiesOne=$this->DataControl->getFieldOne("gmc_code_companies","companies_cnname","companies_agencyid='{$request['agencyId']}'");
                $data=array();
                $data['companies_cnname']=$companiesOne['companies_cnname'];
                $data['transferId']=$listOne['transferId'];
                $data['transferClassNum']=$listOne['transferClassNum'];
                $data['sumTransferAmt']=$listOne['sumTransferAmt'];
                $data['transferDate']=$listOne['transferDate'];
                $data['tranSerial']=$listOne['tranSerial'];
                $tem_array[]=$data;

//                debug($tem_array);
            }

        }

        if(!$tem_array){
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }

        return $tem_array;

    }


    function getHostDetail($request){

        if(!isset($request['agencyId']) || $request['agencyId']==''){
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        if(!isset($request['start_time']) || $request['start_time']=='' || !isset($request['end_time']) || $request['end_time']==''){
            $this->error = true;
            $this->errortip = "请选择时间";
            return false;
        }

        $Model = new \Model\Api\CmbTransModel($request['agencyId']);

        $list = $Model->queryHostDetail($request['start_time'],$request['end_time']);
        if(!$list){
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }

        $companiesOne=$this->DataControl->getFieldOne("gmc_code_companies","companies_cnname","companies_agencyid='{$request['agencyId']}'");

        foreach($list as &$listOne){
            $listOne['companies_cnname']=$companiesOne['companies_cnname'];
            $listOne['currency']='人民币';
        }

        return $list;

    }

    function getTransferInfo($request){

        $datawhere = " tr.company_id='{$this->company_id}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and tr.income_date >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and tr.income_date <='{$request['end_time']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and tr.school_id ='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and tr.course_id ='{$request['course_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and tr.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and tr.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['income_isconfirm']) && $request['income_isconfirm'] !== '') {
            $datawhere .= " and tr.income_isconfirm ='{$request['income_isconfirm']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tr.*,sch.school_cnname,sch.school_branch,cl.class_cnname,cl.class_branch,st.student_cnname,st.student_branch,cho.hour_name 
                from cmb_trans_transfer as tr
                left join smc_school as sch on tr.school_id=sch.school_id 
                left join smc_class as cl on cl.class_id=tr.class_id
                left join smc_student as st on st.student_id=tr.student_id
                left join smc_course as co on co.course_id=cl.course_id
                left join smc_student_hourstudy as sh on sh.hourstudy_id=tr.hourstudy_id
                left join smc_class_hour as cho on cho.hour_id=sh.hour_id
                where {$datawhere} and tr.income_id>0";
        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['confirm_phone'] = $dateexcelvar['confirm_phone'];
                    $datearray['batch_pid'] = $dateexcelvar['batch_pid'];
                    $datearray['income_price'] = $dateexcelvar['income_price'];
                    $datearray['hour_name'] = $dateexcelvar['hour_name'];
                    $datearray['income_times'] = $dateexcelvar['income_times'];
                    $datearray['income_isconfirm'] = $dateexcelvar['income_isconfirm']== 1 ? '已确认' : '未确认';

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "班级编号", "学员名称", "学员编号", "订单编号", "手机号码", "用户销课流水号", "销课金额", "销课课时", "销课课时数", "销课状态"));
            $excelfileds = array('school_cnname', 'school_branch', 'class_cnname', 'class_branch', 'student_cnname', 'student_branch', 'order_pid', 'confirm_phone', 'batch_pid', 'income_price', 'hour_name', 'income_times', 'income_isconfirm');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('销课状态学生确认明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $transList = $this->DataControl->selectClear($sql);
            if (!$transList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($transList as &$transOne) {
                $transOne['income_isconfirm'] = $transOne['income_isconfirm'] == 1 ? '已确认' : '未确认';
            }


            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select tr.transfer_id
                        from cmb_trans_transfer as tr
                        left join smc_school as sch on tr.school_id=sch.school_id 
                        left join smc_class as cl on cl.class_id=tr.class_id
                        left join smc_student as st on st.student_id=tr.student_id
                        left join smc_course as co on co.course_id=cl.course_id
                        left join smc_student_hourstudy as sh on sh.hourstudy_id=tr.hourstudy_id
                        left join smc_class_hour as cho on cho.hour_id=sh.hour_id
                        where {$datawhere} and tr.income_id>0";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $transList;
            return $data;
        }
    }

    function getStuCourseBalance($request){
        $datawhere = " 1 ";
        $having = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and scb.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and scb.school_id ='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id ='{$request['course_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['booking_status']) && $request['booking_status'] !== '') {
            $having .= " and booking_status ='{$request['booking_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select scb.coursebalance_id,cs.companies_cnname,sch.school_cnname,sch.school_branch,st.student_cnname,st.student_branch,sc.course_cnname,sc.course_branch,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_issupervise 
                ,ifnull((select cl.class_cnname from smc_student_study as ss,smc_class as cl where ss.class_id=cl.class_id and ss.school_id=scb.school_id and cl.course_id=scb.course_id and ss.student_id=scb.student_id and ss.study_isreading=1 limit 0,1),'') as class_cnname
                ,ifnull((select cl.class_branch from smc_student_study as ss,smc_class as cl where ss.class_id=cl.class_id and ss.school_id=scb.school_id and cl.course_id=scb.course_id and ss.student_id=scb.student_id and ss.study_isreading=1 limit 0,1),'') as class_branch      
                ,ifnull((select 1 from smc_student_family as x,smc_parenter_wxchattoken as y where x.student_id=scb.student_id and x.parenter_id=y.parenter_id and y.wxchatnumber_id=6 and y.booking_status=1 limit 0,1),0) as booking_status
                from smc_student_coursebalance as scb
                inner join gmc_code_companies as cs on cs.companies_id=scb.companies_id and cs.companies_issupervise=1
                left join smc_school as sch on sch.school_id=scb.school_id
                left join smc_student as st on st.student_id=scb.student_id
                left join smc_course as sc on sc.course_id=scb.course_id
                where {$datawhere} and scb.company_id='{$this->company_id}' and scb.coursebalance_figure>0 and sc.course_issupervise=1
                having {$having}
                ";


        $status = $this->LgArraySwitch(array('0' => '未监管', '1' => '监管中'));
        $b_status = $this->LgArraySwitch(array('0' => '否', '1' => '是'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_issupervise_name'] = $status[$dateexcelvar['coursebalance_issupervise']];
                    $datearray['booking_status_name'] = $b_status[$dateexcelvar['booking_status']];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "校区名称", "校区编号", "学员名称", "学员编号", "课程名称", "课程编号", "班级名称", "班级编号", "课程余额", "剩余课次", "监管状态", "是否绑定小程序"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'course_cnname', 'course_branch', 'class_cnname', 'class_branch', 'coursebalance_figure', 'coursebalance_time', 'coursebalance_issupervise_name', 'booking_status_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('学员课程监管明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;

            $transList = $this->DataControl->selectClear($sql);
            if (!$transList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }


            foreach ($transList as &$transOne) {
                $transOne['coursebalance_issupervise_name'] = $status[$transOne['coursebalance_issupervise']];
                $transOne['booking_status_name'] = $b_status[$transOne['booking_status']];
            }


            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select scb.coursebalance_id
                                ,ifnull((select 1 from smc_student_family as x,smc_parenter_wxchattoken as y where x.student_id=scb.student_id and x.parenter_id=y.parenter_id and y.wxchatnumber_id=6 and y.booking_status=1 limit 0,1),0) as booking_status
                from smc_student_coursebalance as scb
                inner join gmc_code_companies as cs on cs.companies_id=scb.companies_id and cs.companies_issupervise=1
                left join smc_school as sch on sch.school_id=scb.school_id
                left join smc_student as st on st.student_id=scb.student_id
                left join smc_course as sc on sc.course_id=scb.course_id
                where {$datawhere} and scb.company_id='{$this->company_id}' and scb.coursebalance_figure>0 and sc.course_issupervise=1
                having {$having}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $transList;
            return $data;
        }
    }

    function changeSuperviseStatus($request){

        $stuCourseBalanceOne=$this->DataControl->getFieldOne("smc_student_coursebalance","coursebalance_issupervise","coursebalance_id='{$request['coursebalance_id']}'");


        if(!$stuCourseBalanceOne || $stuCourseBalanceOne['coursebalance_issupervise']==1){
            $this->error = true;
            $this->errortip = "该状态不可监管";
            return false;
        }

        $data=array();
        $data['coursebalance_issupervise']=1;
        if($this->DataControl->updateData("smc_student_coursebalance","coursebalance_id='{$request['coursebalance_id']}'",$data)){

            return true;
        }else{
            $this->error = true;
            $this->errortip = "监管失败";
            return false;
        }

    }

    function getAccountBalance($request){

        $datawhere=" 1 ";

        if(isset($request['agencyId']) && $request['agencyId'] !=''){
            $datawhere.=" and A.companies_agencyid='{$request['agencyId']}' " ;
        }

        $sql = "select A.companies_agencyid,A.companies_cnname 
                from gmc_code_companies as A
                where {$datawhere} and A.company_id='{$this->company_id}' and A.companies_agencyid<>'' and A.companies_issupervise=1
                and exists(select 1 from cmb_trans_order as X where X.agency_id=A.companies_agencyid and order_status=1)
                ";

        $companiesList=$this->DataControl->selectClear($sql);

        if(!$companiesList){
            $this->error = true;
            $this->errortip = "监管户不存在";
            return false;
        }

        $tem_array=array();

        foreach ($companiesList as &$companiesOne){

            $Model = new \Model\Api\CmbTransModel($companiesOne['companies_agencyid']);

            $listOne = $Model->queryAccountBalance();

            if($listOne){
                $data=array();
                $data['companies_cnname']=$companiesOne['companies_cnname'];
                $data['regAccNo']=$listOne['regAccNo'];
                $data['frozenAmt']=$listOne['frozenAmt']/100;
                $data['transferredAmt']=$listOne['transferredAmt']/100;
                $data['lastEndBalance']=$listOne['lastEndBalance']/100;
                $tem_array[]=$data;
            }
        }

        if(!$tem_array){
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }

        return $tem_array;

    }

    function getClassBalance($request){

        if(!isset($request['agencyId']) || $request['agencyId']==''){
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        //班级编号组成的json字符串
        if(!isset($request['class_list']) || $request['class_list']==''){
            $this->error = 1;
            $this->errortip = "请选择需要查询的班级";
            return false;
        }

        $Model = new \Model\Api\CmbTransModel($request['agencyId']);

        $listOne = $Model->queryClassBalance($request['class_list']);

        if(!$listOne){
            $this->error = 1;
            $this->errortip = $Model->errortip;
            return false;
        }

        $classList=$listOne['classList'];

        $companiesOne=$this->DataControl->getFieldOne("gmc_code_companies","companies_cnname","companies_agencyid='{$request['agencyId']}'");

        foreach($classList as &$classOne){

            $sql = "select sch.school_cnname,sch.school_branch,cl.class_cnname,cl.class_branch
                    from smc_class as cl,smc_school as sch  
                    where cl.school_id=sch.school_id and cl.class_branch='{$classOne['classId']}'";

            $one=$this->DataControl->selectOne($sql);
            $classOne['transferredAmt']=$classOne['transferredAmt']/100;
            $classOne['surplusAmt']=$classOne['surplusAmt']/100;
            $classOne['totalAmt']=$classOne['totalAmt']/100;
            $classOne['companies_cnname']=$companiesOne['companies_cnname'];
            $classOne['school_cnname']=$one['school_cnname'];
            $classOne['school_branch']=$one['school_branch'];
            $classOne['class_cnname']=$one['class_cnname'];
            $classOne['class_branch']=$one['class_branch'];

        }

        return $classList;
    }

    function getCmbClass($request){

        $sql = "select b.class_id,b.class_cnname,b.class_branch 
                from cmb_trans_class as A,smc_class as B 
                where A.class_id=B.class_id and B.company_id='{$this->company_id}'";


        $classList=$this->DataControl->selectClear($sql);
        if(!$classList){
            $this->error = 1;
            $this->errortip = "无可查询班级";
            return false;
        }

        return $classList;

    }


}