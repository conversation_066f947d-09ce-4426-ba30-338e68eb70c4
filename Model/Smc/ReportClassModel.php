<?php


namespace Model\Smc;

class ReportClassModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作学校
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
            $this->schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$publicarray['school_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    /**
     * 班级信息统计表
     * author: ling
     * 对应接口文档 0001
     * @return mixed
     */
    function classInfo($request)
    {
        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or class_branch like '%{$request['keyword']}%') ";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and c.course_id='{$request['course_id']}' ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id='{$request['school_id']} ' ";
        }
//        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$request['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$request['re_postbe_id']}' limit 0,1");
//        if ($postbeOne && $postbeOne['postrole_dataequity'] == '0') {
//            $datawhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
//        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $today = $request['end_time'];
            $datawhere .= " and c.class_stdate <= '{$request['end_time']}'  and c.class_enddate >= '{$request['end_time']}' ";
        } else {
            $today = date("Y-m-d");
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $in = "'A02','A03','A04','A05'";
        $to = "'A07','B01','B02','B03','B04','B06','E01','B07'";
        $sql = "
            select ss.school_cnname,ss.school_branch,c.class_cnname,c.class_enname,c.class_branch,c.class_fullnums,
            (select count(DISTINCT staffer_id) from  smc_class_hour_teaching as t,smc_class_hour as ch  where t.hour_id=ch.hour_id and t.class_id = c.class_id and ch.hour_day <='{$today}') as all_teacher_num,
            (select count(DISTINCT staffer_id) from  smc_class_teach as te where te.class_id = c.class_id  ) as teaching_num,
            (select count(student_id) from smc_student_study as s where s.class_id=c.class_id and s.study_beginday<='{$today}' and s.study_endday >='{$today}') as study_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =1 and stuchange_code in ({$in}) and changelog_day<='{$today}' ) as to_class_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =0 and stuchange_code in ({$to}) and changelog_day<='{$today}' ) as out_class_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =0 and stuchange_code in ({$to}) and changelog_day<='{$today}' ) as leave_class_num,
            (select count(DISTINCT student_id) from smc_student_changelog as l where l.class_id=c.class_id and changelog_type =0 and stuchange_code ='A07'  and changelog_day<='{$today}' ) as wait_class_num,
            (select count(h.hourstudy_id) from smc_student_hourstudy as h,smc_class_hour as ch  where h.hour_id=ch.hour_id and  h.class_id =c.class_id and ch.hour_day <='{$today}' ) as stu_all_checking_num,
            (select count(h.hourstudy_id) from smc_student_hourstudy as h,smc_class_hour as ch  where h.hour_id=ch.hour_id and h.class_id =c.class_id and h.hourstudy_checkin =1 and ch.hour_day <='{$today}') as stu_right_checking_num
            from smc_class as c left join smc_school as ss on c.school_id = ss.school_id
            where   c.company_id='{$this->company_id}' and c.class_status <> -2  and  {$datawhere} order by class_createtime DESC ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无班级信息";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['all_teacher_num'] = $dateexcelvar['all_teacher_num'];
                    $datearray['teaching_num'] = $dateexcelvar['teaching_num'];
                    $datearray['class_fullnums'] = $dateexcelvar['class_fullnums'];
                    $datearray['study_num'] = $dateexcelvar['study_num'];
                    $datearray['fill_class_rate'] = $dateexcelvar['class_fullnums'] ? round($dateexcelvar['study_num'] / $dateexcelvar['class_fullnums'], 4) * 100 . '%' : '0%';
                    $datearray['to_class_num'] = $dateexcelvar['to_class_num'];
                    $datearray['out_class_num'] = $dateexcelvar['out_class_num'];
                    $datearray['leave_class_num'] = $dateexcelvar['leave_class_num'];
                    $datearray['wait_class_num'] = $dateexcelvar['wait_class_num'];
                    $datearray['stu_all_checking_num'] = $dateexcelvar['stu_all_checking_num'];
                    $datearray['stu_right_checking_num'] = $dateexcelvar['stu_right_checking_num'];
                    $datearray['stu_checking_rate'] = $dateexcelvar['stu_all_checking_num'] ? round($dateexcelvar['stu_right_checking_num'] / $dateexcelvar['stu_all_checking_num'], 4) * 100 . '%' : '0%';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "班级别名", "班级编号", "班级教师累计数", "教师人数", "满班人数", "在读人数", "满班率", "转入人数", "转出人数", "班级流失人数", "延班人数", "应到人次", "实到人次", "出勤率"));
            $excelfileds = array('school_cnname', 'school_branch', 'class_cnname', 'class_enname', 'class_branch', 'all_teacher_num', 'teaching_num', 'class_fullnums', "study_num", 'fill_class_rate', 'to_class_num', "out_class_num", "leave_class_num", "wait_class_num", "stu_all_checking_num", "stu_right_checking_num", "stu_checking_rate");
            $tem_name = $this->LgStringSwitch($this->schoolOne['school_cnname'] . '班级信息统计表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "班级信息信息";
                return false;
            }
            foreach ($list as &$one) {
                $one['fill_class_rate'] = $one['class_fullnums'] ? round($one['study_num'] / $one['class_fullnums'], 4) * 100 . '%' : '0%';
                $one['stu_checking_rate'] = $one['stu_all_checking_num'] ? round($one['stu_right_checking_num'] / $one['stu_all_checking_num'], 4) * 100 . '%' : '0%';
            }
            $data = array();
            $count_sql = "select c.class_id
                 from smc_class as c
            where c.company_id='{$this->company_id}' and c.class_status <> -2  and  {$datawhere}
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;
            return $data;
        }
    }

}