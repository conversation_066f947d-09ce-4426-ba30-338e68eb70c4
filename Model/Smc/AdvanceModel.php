<?php


namespace Model\Smc;

class AdvanceModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function timesAdvancePrice($request)
    {

        $sql = "select (select ss.class_id
              from smc_student_study as ss
              left join smc_class as c on c.class_id=ss.class_id
              where c.course_id=scb.course_id and ss.school_id='{$this->school_id}' and ss.study_isreading=1 limit 0,1) as class_id
              ,scb.coursebalance_time,scb.coursebalance_figure
              from smc_student_coursebalance as scb
              where scb.company_id='{$this->company_id}' and scb.school_id='{$this->school_id}' and scb.student_id='{$request['student_id']}' and scb.course_id='{$request['course_id']}' and scb.coursebalance_time>0 and scb.coursebalance_figure>0
        ";
        $classOne = $this->DataControl->selectOne($sql);
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum,course_cnname,course_branch", "course_id='{$request['course_id']}'");
        $buyNum = $courseOne['course_classnum'];
        $data = array();
        $data['noNum'] = 0;
        $data['status'] = $this->LgStringSwitch('不在读');
        if ($classOne) {
            if ($classOne['class_id'] > 0) {
                $sql = "select count(hour_id) as noNum
                  from smc_class_hour as sch
                  where sch.class_id='{$classOne['class_id']}' and sch.hour_ischecking=0 limit 0,1
                  ";
                $hourOne = $this->DataControl->selectOne($sql);
                $buyNum = $buyNum - $classOne['coursebalance_time'];
                if ($buyNum <= 0) {
                    $this->error = true;
                    $this->errortip = "课程有购买完整,不可重复购买";
                    return false;
                } else {
                    $data['noNum'] = $hourOne['noNum'];
                }
                $data['status'] = $this->LgStringSwitch('在读');
            }
            $buyNum = $courseOne['course_classnum'] - $classOne['coursebalance_time'];
            if ($buyNum <= 0) {
                $this->error = true;
                $this->errortip = "课程有购买完整,不可重复购买";
                return false;
            }
        }
        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursecat_id,feetype_code", "coursecatbalance_id='{$request['coursecatbalance_id']}'");

        $data['course_cnname'] = $courseOne['course_cnname'];
        $data['course_branch'] = $courseOne['course_branch'];
        $data['course_classnum'] = $courseOne['course_classnum'];
        $data['coursebalance_time'] = $classOne['coursebalance_time'];
        $data['buyNum'] = $buyNum;

        $list_field = array();
        $list_field[0]["fieldstring"] = "course_cnname";
        $list_field[0]["fieldname"] = $this->LgStringSwitch("课程别名称");
        $list_field[0]["show"] = 1;
        $list_field[0]["custom"] = 0;

        $list_field[1]["fieldstring"] = "course_branch";
        $list_field[1]["fieldname"] = $this->LgStringSwitch("课程别编号");
        $list_field[1]["show"] = 1;
        $list_field[1]["custom"] = 0;

        $list_field[2]["fieldstring"] = "course_classnum";
        $list_field[2]["fieldname"] = $this->LgStringSwitch("课程总次数");
        $list_field[2]["show"] = 1;
        $list_field[2]["custom"] = 0;

        $list_field[3]["fieldstring"] = "status";
        $list_field[3]["fieldname"] = $this->LgStringSwitch("在读状态");
        $list_field[3]["show"] = 1;
        $list_field[3]["custom"] = 0;

        $list_field[4]["fieldstring"] = "noNum";
        $list_field[4]["fieldname"] = $this->LgStringSwitch("班级剩余课次数");
        $list_field[4]["show"] = 1;
        $list_field[4]["custom"] = 0;

        $list_field[5]["fieldstring"] = "coursebalance_time";
        $list_field[5]["fieldname"] = $this->LgStringSwitch("账户剩余课次");
        $list_field[5]["show"] = 1;
        $list_field[5]["custom"] = 0;

        $list_field[6]["fieldstring"] = "buyNum";
        $list_field[6]["fieldname"] = $this->LgStringSwitch("冲销课次");
        $list_field[6]["show"] = 1;
        $list_field[6]["custom"] = 0;

        $data['should_repair'] = 0;

        $return_data = array();
        $return_data['list'][0] = $data;
        $return_data['field'] = $list_field;
        $return_data['coursecatbalance_figure'] = $catOne['coursecatbalance_figure'];
//        $pcOne=$this->DataControl->getFieldOne("smc_fee_policy_course","norm_unitprice,sell_unitprice,full_price","pc_id='{$request['pc_id']}'");

//        if($buyNum==$courseOne['course_classnum']){
//            $price=$pcOne['full_price'];
//        }else{
//            $price=$buyNum*$pcOne['sell_unitprice'];
//        }

        if ($request['pc_id'] > 0) {
            $pcOne = $this->DataControl->getFieldOne("smc_fee_policy_course", "sell_unitprice,norm_unitprice,full_price", "pc_id='{$request['pc_id']}'");
            if ($buyNum == $courseOne['course_classnum']) {
                $price = $pcOne['full_price'];
            } else {
                $price = $buyNum * $pcOne['sell_unitprice'];
            }
        } else {
            $sql = "select fpt.tuition_unitprice as norm_unitprice,fpt.tuition_sellingprice,fpt.tuition_buypiece from smc_fee_pricing_tuition as fpt where fpt.pricing_id='{$request['pricing_id']}' and fpt.course_id='{$request['course_id']}'";
            $pcOne = $this->DataControl->selectOne($sql);
            if ($pcOne) {
                $pcOne['sell_unitprice'] = ceil($pcOne['tuition_sellingprice'] / $pcOne['tuition_buypiece']);
                $price = $buyNum * $pcOne['sell_unitprice'];
            } else {
                $this->error = true;
                $this->errortip = "传值错误";
                return false;
            }
        }

        if ($catOne['coursecatbalance_figure'] >= $price) {
            $buyPrice = $price;
        } else {
            $buyPrice = $catOne['coursecatbalance_figure'];
            $data['should_repair'] = 1;
        }
        $return_data['buyPrice'] = $buyPrice;

        $return_data['surplusPrice'] = $catOne['coursecatbalance_figure'] - $buyPrice;

        return $return_data;
    }

    function itemsAdvancePrice($request)
    {
        $sql = "select fpi.items_unitprice,fpi.items_buypiece,cf.feeitem_cnname from smc_fee_pricing_items as fpi left join smc_code_feeitem as cf on cf.feeitem_branch=fpi.feeitem_branch where fpi.items_id='{$request['items_id']}'";
        $pricingItemOne = $this->DataControl->selectOne($sql);
        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursecat_id,feetype_code", "coursecatbalance_id='{$request['coursecatbalance_id']}'");
        $data = array();
        $return_data = array();

        $list_field = array();
        $list_field[0]["fieldstring"] = "feeitem_cnname";
        $list_field[0]["fieldname"] = $this->LgStringSwitch("收费项目名称");
        $list_field[0]["show"] = 1;
        $list_field[0]["custom"] = 0;

        $list_field[1]["fieldstring"] = "items_buypiece";
        $list_field[1]["fieldname"] = $this->LgStringSwitch("销售件数");
        $list_field[1]["show"] = 1;
        $list_field[1]["custom"] = 0;

        $list_field[2]["fieldstring"] = "items_unitprice";
        $list_field[2]["fieldname"] = $this->LgStringSwitch("销售单价");
        $list_field[2]["show"] = 1;
        $list_field[2]["custom"] = 0;

        $list_field[3]["fieldstring"] = "buyNum";
        $list_field[3]["fieldname"] = $this->LgStringSwitch("冲销件数");
        $list_field[3]["show"] = 1;
        $list_field[3]["custom"] = 0;
        $list_field[3]["isposenter"] = true;


        $data['feeitem_cnname'] = $pricingItemOne['feeitem_cnname'];
        $data['items_buypiece'] = $pricingItemOne['items_buypiece'];
        $data['items_unitprice'] = $pricingItemOne['items_unitprice'];

        if (isset($request['buyNum']) && $request['buyNum'] != '') {
            if ($request['buyNum'] * $pricingItemOne['items_unitprice'] <= $catOne['coursecatbalance_figure']) {
                $return_data['buyPrice'] = ceil($request['buyNum'] * $pricingItemOne['items_unitprice']);
                $data['buyNum'] = $request['buyNum'];
            } else {
                $data['buyNum'] = floor($catOne['coursecatbalance_figure'] / $pricingItemOne['items_unitprice']);
                $return_data['buyPrice'] = ceil(floor($catOne['coursecatbalance_figure'] / $pricingItemOne['items_unitprice']) * $pricingItemOne['items_unitprice']);
            }
        } else {
            if ($pricingItemOne['items_buypiece'] * $pricingItemOne['items_unitprice'] <= $catOne['coursecatbalance_figure']) {
                $return_data['buyPrice'] = ceil($pricingItemOne['items_buypiece'] * $pricingItemOne['items_unitprice']);
                $data['buyNum'] = $pricingItemOne['items_buypiece'];
            } else {
                $data['buyNum'] = floor($catOne['coursecatbalance_figure'] / $pricingItemOne['items_unitprice']);
                $return_data['buyPrice'] = ceil(floor($catOne['coursecatbalance_figure'] / $pricingItemOne['items_unitprice']) * $pricingItemOne['items_unitprice']);
            }
        }

        $return_data['coursecatbalance_figure'] = $catOne['coursecatbalance_figure'];
        $return_data['surplusPrice'] = ($catOne['coursecatbalance_figure'] * 100 - $return_data['buyPrice'] * 100) / 100;
        $return_data['list'][0] = $data;
        $return_data['field'] = $list_field;
        return $return_data;
    }

    function manageAdvancePrice($request)
    {

        $monthNum = $request['monthNum'];
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_cnname,course_branch", "course_id='{$request['course_id']}' and company_id='{$this->company_id}'");
        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursecat_id,feetype_code", "coursecatbalance_id='{$request['coursecatbalance_id']}'");

        $number = $catOne['coursecatbalance_figure'];  ///优惠活动
        $total = $monthNum;
        $divide_number = bcdiv($number, $total, 2);
        $last_number = bcsub($number, $divide_number * ($total - 1), 2);
        $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
        $numArray = explode("+", $number_str);
        $day = date("Y-m-d", time());
        $tem_array = array();
        for ($i = 1; $i <= $monthNum; $i++) {
            $month = date('Y-m', strtotime("$day +$i month"));
            $num = GetMonthWorkday($month . "01");
            $data = array();
            $data['month'] = $month;
            $data['course_id'] = $request['course_id'];
            $data['course_cnname'] = $courseOne['course_cnname'];
            $data['course_branch'] = $courseOne['course_branch'];
            $data['num'] = $num;
            $data['price'] = $numArray[$i - 1];
            $tem_array[] = $data;
        }

        $list_field = array();
        $list_field[0]["fieldstring"] = "course_id";
        $list_field[0]["fieldname"] = $this->LgStringSwitch("课程ID");
        $list_field[0]["show"] = 0;
        $list_field[0]["custom"] = 0;

        $list_field[1]["fieldstring"] = "month";
        $list_field[1]["fieldname"] = $this->LgStringSwitch("冲销月份");
        $list_field[1]["show"] = 1;
        $list_field[1]["custom"] = 0;

        $list_field[2]["fieldstring"] = "course_cnname";
        $list_field[2]["fieldname"] = $this->LgStringSwitch("课程名称");
        $list_field[2]["show"] = 1;
        $list_field[2]["custom"] = 0;

        $list_field[3]["fieldstring"] = "course_branch";
        $list_field[3]["fieldname"] = $this->LgStringSwitch("课程别");
        $list_field[3]["show"] = 1;
        $list_field[3]["custom"] = 0;

        $list_field[4]["fieldstring"] = "num";
        $list_field[4]["fieldname"] = $this->LgStringSwitch("冲销课次");
        $list_field[4]["show"] = 1;
        $list_field[4]["custom"] = 0;

        $list_field[5]["fieldstring"] = "price";
        $list_field[5]["fieldname"] = $this->LgStringSwitch("冲销金额");
        $list_field[5]["show"] = 1;
        $list_field[5]["custom"] = 0;

        $return_data['coursecatbalance_figure'] = $catOne['coursecatbalance_figure'];
        $return_data['buyPrice'] = $catOne['coursecatbalance_figure'];
        $return_data['surplusPrice'] = ($catOne['coursecatbalance_figure'] * 100 - $return_data['buyPrice'] * 100) / 100;
        $return_data['list'] = $tem_array;
        $return_data['field'] = $list_field;
        return $return_data;
    }

    function courseList($request)
    {
        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursecat_id,feetype_code", "coursecatbalance_id='{$request['coursecatbalance_id']}'");
        $sql = "select cct.coursetype_id,cct.coursetype_cnname,cct.coursetype_branch,cc.coursecat_id,cc.coursecat_cnname,cc.coursecat_branch
              from smc_code_coursecat as cc
              left join smc_code_coursetype as cct on cc.coursetype_id=cct.coursetype_id
              where cc.company_id='{$this->company_id}' and cc.coursecat_id='{$catOne['coursecat_id']}'
              ";
        $info = $this->DataControl->selectOne($sql);

        $policy_id = 127;
        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_branch", "student_id='{$request['student_id']}'");

        if ($studentOne['student_branch'] >= '2018070300001' && $studentOne['student_branch'] < '2019060300001') {
            $policy_id = '57';
        } elseif ($studentOne['student_branch'] < '2018070300001') {
            $policy_id = '56';
        }
        if ($policy_id == 0) {
            $this->error = true;
            $this->errortip = "不可冲销";
            return false;
        }

        if ($catOne['feetype_code'] == 'Times') {
            $datawhere = " 1 ";
            if (isset($request['keyword']) && $request['keyword'] != '') {
                $datawhere .= " and (sc.course_cnname like '%{$request['keyword']}%' or sc.course_branch like '%{$request['keyword']}%') ";
            }
            $sql = "select fpc.pc_id,fpc.to_course_id as course_id,sc.course_cnname,sc.course_branch,fpc.sell_unitprice,fpc.norm_unitprice
                from smc_fee_policy_course as fpc
                left join smc_course as sc on sc.course_id=fpc.to_course_id
                where {$datawhere} and sc.coursecat_id='{$catOne['coursecat_id']}' and fpc.policy_id='{$policy_id}' and fpc.company_id='{$this->company_id}'
                group by fpc.to_course_id,fpc.norm_unitprice,fpc.sell_unitprice
                ";

            $list = $this->DataControl->selectClear($sql);

            $list_field = array();
            $list_field[0]["fieldstring"] = "pc_id";
            $list_field[0]["fieldname"] = $this->LgStringSwitch("售价明细ID");
            $list_field[0]["show"] = 0;
            $list_field[0]["custom"] = 0;

            $list_field[1]["fieldstring"] = "course_id";
            $list_field[1]["fieldname"] = $this->LgStringSwitch("课程ID");
            $list_field[1]["show"] = 0;
            $list_field[1]["custom"] = 0;

            $list_field[2]["fieldstring"] = "course_cnname";
            $list_field[2]["fieldname"] = $this->LgStringSwitch("课程别名称");
            $list_field[2]["show"] = 1;
            $list_field[2]["custom"] = 0;

            $list_field[3]["fieldstring"] = "course_branch";
            $list_field[3]["fieldname"] = $this->LgStringSwitch("课程别编号");
            $list_field[3]["show"] = 1;
            $list_field[3]["custom"] = 0;

            $list_field[4]["fieldstring"] = "sell_unitprice";
            $list_field[4]["fieldname"] = $this->LgStringSwitch("销售价");
            $list_field[4]["show"] = 1;
            $list_field[4]["custom"] = 0;

            $list_field[5]["fieldstring"] = "norm_unitprice";
            $list_field[5]["fieldname"] = $this->LgStringSwitch("原价");
            $list_field[5]["show"] = 1;
            $list_field[5]["custom"] = 0;
            $data['field'] = $list_field;
            if (!$list) {
                $day = date("Y-m-d", time());
                $sql = "SELECT
                    t.course_id,
                    c.course_cnname,
                    c.course_branch,
                    p.pricing_id,
                    t.tuition_originalprice,
                    t.tuition_sellingprice,
                    t.tuition_buypiece,
                    t.tuition_unitprice as norm_unitprice
                FROM
                    smc_fee_pricing_tuition AS t,
                    smc_fee_pricing AS p,
                    smc_fee_agreement AS a,
                    smc_course as c,
                    smc_code_coursetype as ccp
                WHERE
                   t.pricing_id = p.pricing_id
                AND p.agreement_id = a.agreement_id
                AND c.course_id = p.course_id
                AND ccp.coursetype_id = c.coursetype_id
                AND ccp.coursetype_isopenclass = '0'
                AND c.coursecat_id='{$catOne['coursecat_id']}'
                AND (
                    (
                        p.pricing_applytype = '1'
                        AND p.pricing_id IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$request['school_id']}'
                        )
                    )
                    OR (
                        p.pricing_applytype = '-1'
                        AND p.pricing_id NOT IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$request['school_id']}'
                        )
                    )
                    OR (p.pricing_applytype = '0')
                )
                AND a.agreement_startday <= '{$day}'
                AND a.agreement_endday >= '{$day}'
                AND a.agreement_status = '1'
                AND a.company_id = '{$request['company_id']}'
                AND c.course_status<>'-1'
                GROUP BY
                    t.course_id";

                $list = $this->DataControl->selectClear($sql);

                if ($list) {
                    foreach ($list as &$one) {
                        $one['pc_id'] = '0';
                        $one['sell_unitprice'] = ceil($one['tuition_sellingprice'] / $one['tuition_buypiece']);
                    }
                    $data['list'] = $list;
                } else {
                    $this->errortip = "无冲销数据";
                    $data['list'] = array();
                }

            } else {
                foreach ($list as &$one) {
                    $one['pricing_id'] = '0';
                }
                $data['list'] = $list;
            }

        } elseif ($catOne['feetype_code'] == 'Bus' || $catOne['feetype_code'] == 'Food') {

            $sql = "select cf.feeitem_cnname,fpi.items_unitprice,fp.course_id,fpi.items_id,sc.course_cnname
                from smc_fee_pricing_items as fpi
                left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
                left join smc_code_feeitem as cf on cf.feeitem_branch=fpi.feeitem_branch and cf.company_id='{$this->company_id}'
                left join smc_course as sc on sc.course_id=fp.course_id
                where sc.coursecat_id='{$catOne['coursecat_id']}' and cf.feeitem_branch='{$catOne['feetype_code']}' and fpi.company_id='{$this->company_id}'
                group by fpi.items_id
                ";

            $list = $this->DataControl->selectClear($sql);


            $list_field = array();
            $list_field[0]["fieldstring"] = "course_id";
            $list_field[0]["fieldname"] = $this->LgStringSwitch("课程ID");
            $list_field[0]["show"] = 0;
            $list_field[0]["custom"] = 0;

            $list_field[1]["fieldstring"] = "items_id";
            $list_field[1]["fieldname"] = $this->LgStringSwitch("杂项ID");
            $list_field[1]["show"] = 0;
            $list_field[1]["custom"] = 0;


            $list_field[2]["fieldstring"] = "feeitem_cnname";
            $list_field[2]["fieldname"] = $this->LgStringSwitch("杂费名称");
            $list_field[2]["show"] = 1;
            $list_field[2]["custom"] = 0;

            $list_field[3]["fieldstring"] = "items_unitprice";
            $list_field[3]["fieldname"] = $this->LgStringSwitch("销售单价");
            $list_field[3]["show"] = 1;
            $list_field[3]["custom"] = 0;
            if (!$list) {
                $this->errortip = "无冲销数据";
                $data['field'] = $list_field;
                $data['list'] = array();
            } else {

                foreach ($list as &$one) {
                    $one['feeitem_cnname'] = $one['course_cnname'] . '的' . $one['feeitem_cnname'];
                }

                $data['field'] = $list_field;
                $data['list'] = $list;
            }

        } elseif ($catOne['feetype_code'] == 'Manage') {
            $sql = "select sc.course_id,sc.course_cnname,sc.course_branch
                  from smc_course as sc
                  where sc.coursecat_id='{$catOne['coursecat_id']}' and sc.company_id='{$this->company_id}'
                  ";

            $list = $this->DataControl->selectClear($sql);


            $list_field = array();
            $list_field[0]["fieldstring"] = "course_id";
            $list_field[0]["fieldname"] = $this->LgStringSwitch("课程ID");
            $list_field[0]["show"] = 0;
            $list_field[0]["custom"] = 0;

            $list_field[1]["fieldstring"] = "course_cnname";
            $list_field[1]["fieldname"] = $this->LgStringSwitch("课程别名称");
            $list_field[1]["show"] = 1;
            $list_field[1]["custom"] = 0;

            $list_field[2]["fieldstring"] = "course_branch";
            $list_field[2]["fieldname"] = $this->LgStringSwitch("课程别编号");
            $list_field[2]["show"] = 1;
            $list_field[2]["custom"] = 0;

            if (!$list) {
                $this->errortip = "无冲销数据";
                $data['field'] = $list_field;
                $data['list'] = array();
            } else {
                $data['field'] = $list_field;
                $data['list'] = $list;
            }

        } else {
            $this->error = true;
            $this->errortip = "不可冲销";
            return false;
        }

        $data['info'] = $info;
        return $data;

    }

    function advanceHistory($request)
    {
        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursetype_id,coursecat_id,feetype_code", "coursecatbalance_id='{$request['coursecatbalance_id']}'");
        $sql = "select scl.log_playname,scl.log_playamount,scl.log_playme,scl.log_reason,st.staffer_cnname,scl.log_time,scl.log_playclass
              from smc_student_coursecatbalance_log as scl
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              where scl.student_id='{$request['student_id']}' 
              and scl.coursecat_id='{$catOne['coursecat_id']}' 
              and scl.coursetype_id='{$catOne['coursetype_id']}' 
              and scl.feetype_code='{$catOne['feetype_code']}' 
              and scl.school_id='{$this->school_id}' 
              order by scl.log_time desc,scl.log_id desc
        ";
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无交易记录";
            return false;
        }

        foreach ($list as &$one) {
            $one['log_time'] = date("Y-m-d H:i:s", $one['log_time']);
        }

        return $list;
    }

    function catSubscription($request)
    {


        if (!$request['create_time']) {
            $request['create_time'] = date("Y-m-d");
        }
        $code = $this->LgArraySwitch(array("Deposit" => "定金", "Manage" => "管理费", "Times" => "课次", "Sales" => "教材", "Bus" => "校车", "Food" => "伙食", "Other" => "其他", "Member" => "会员", "OtLv" => "口语评测", "Forward" => "结转金额"));

        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursetype_id,coursecat_id,feetype_code,companies_id", "coursecatbalance_id='{$request['coursecatbalance_id']}'");

//        if ($catOne['feetype_code'] == 'Sales') {
//            $a = $this->DataControl->selectOne("
//            SELECT
//                p.post_istopjob
//            FROM
//                gmc_staffer_postbe AS sp
//            LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
//            WHERE sp.postbe_id = '{$request['re_postbe_id']}'");
//            if ($a['post_istopjob'] != '1' || $request['re_postbe_id'] == '0') {
//                ajax_return(array('error' => 1, 'errortip' => "您没有权限操作教材预收转出或认缴，请联系财务操作"), $this->companyOne['company_language']);
//            }
//        }
        $price = 0;
        if (isset($request['price']) && $request['price'] != '' && $request['price'] > 0) {
            $price = $request['price'];
            if ($price > $catOne['coursecatbalance_figure']) {
                $this->error = true;
                $this->errortip = "请填写正确的金额";
                return false;
            }
        } else {
            $price = $catOne['coursecatbalance_figure'];
        }

        $data = array();
        do {
            $trading_pid = $this->createOrderPid('CRJ');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $catOne['companies_id'];
        $data['student_id'] = $request['student_id'];
        $data['tradingtype_code'] = 'Subscribed';
        $data['trading_status'] = "1";
        $data['trading_createtime'] = strtotime($request['create_time']);
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $this->DataControl->insertData("smc_student_trading", $data);

        $in_data = array();
        $in_data['company_id'] = $this->company_id;
        $in_data['companies_id'] = $catOne['companies_id'];
        $in_data['school_id'] = $this->school_id;
        $in_data['income_type'] = '1';
        $in_data['student_id'] = $request['student_id'];
        $in_data['coursecat_id'] = $catOne['coursecat_id'];
        $in_data['trading_pid'] = $trading_pid;
        $in_data['income_price'] = $price;
        $in_data['income_note'] = $this->LgStringSwitch('学员预收余额认缴收入');
        $in_data['income_confirmtime'] = strtotime($request['create_time']);
        $in_data['income_audittime'] = strtotime($request['create_time']);
        $in_data['income_createtime'] = time();
        $this->DataControl->insertData("smc_school_income", $in_data);


        $log = array();
        $log['student_id'] = $request['student_id'];
        $log['trading_pid'] = $trading_pid;
        $log['coursetype_id'] = $catOne['coursetype_id'];
        $log['coursecat_id'] = $catOne['coursecat_id'];
        $log['feetype_code'] = $catOne['feetype_code'];
        $log['school_id'] = $this->school_id;
        $log['companies_id'] = $catOne['companies_id'];
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $catOne['coursecatbalance_figure'];
        $log['log_playamount'] = $price;
        $log['log_finalamount'] = $catOne['coursecatbalance_figure'] - $price;
        $log['log_fromme'] = $catOne['coursecatbalance_time'];

        if ($catOne['coursecatbalance_figure'] == 0 || $catOne['coursecatbalance_time'] == 0) {
            $log['log_playme'] = 0;
            $log['log_finaltime'] = $catOne['coursecatbalance_time'];
        } else {
            $log['log_playme'] = ceil($price / ($catOne['coursecatbalance_figure'] / $catOne['coursecatbalance_time']));
            $log['log_finaltime'] = $catOne['coursecatbalance_time'] - ceil($price / ($catOne['coursecatbalance_figure'] / $catOne['coursecatbalance_time']));
        }

        $log['log_reason'] = $request['reason'];
        $log['log_time'] = strtotime($request['create_time']);
        $log['log_playname'] = $this->LgStringSwitch('认缴' . $code[$catOne['feetype_code']] . '预收');
        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

        $data = array();
        $data['coursecatbalance_figure'] = $catOne['coursecatbalance_figure'] - $price;
        $data['coursecatbalance_time'] = $log['log_finaltime'];
        $data['coursecatbalance_updatatime'] = strtotime($request['create_time']);
        $this->DataControl->updateData("smc_student_coursecatbalance", "coursecatbalance_id='{$request['coursecatbalance_id']}'", $data);
        return true;

    }


    function turnOut($request)
    {
        if (!$request['create_time']) {
            $request['create_time'] = time();
        } else {
            $request['create_time'] = strtotime($request['create_time']);
        }

        do {
            $trading_pid = $this->createOrderPid('ZC');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursetype_id,coursecat_id,feetype_code,companies_id", "coursecatbalance_id='{$request['coursecatbalance_id']}' and school_id='{$request['school_id']}'");
        if (!$catOne) {
            $this->error = true;
            $this->errortip = "该预收不存在预收";
            return false;
        }

        $conpanyOne = $this->DataControl->getFieldOne("gmc_company", "company_isturnoutlimit", "company_id='{$request['company_id']}'");

        if (!isset($request['re_postbe_id']) || $request['re_postbe_id'] != '0') {
            if ($catOne['coursecat_id'] == '0' && $catOne['feetype_code'] == 'Deposit' && $conpanyOne['company_isturnoutlimit'] == '1') {

                $this->error = true;
                $this->errortip = "班组充值定金只有超级管理员可以转出！";
                return false;
            }
        }

        $price = 0;
        if (isset($request['price']) && $request['price'] != '' && $request['price'] > 0) {
            $price = $request['price'];
            if ($price > $catOne['coursecatbalance_figure']) {
                $this->error = true;
                $this->errortip = "请填写正确的金额";
                return false;
            }
        } else {
            $price = $catOne['coursecatbalance_figure'];
        }


        if ($catOne['feetype_code'] != 'Deposit' && $catOne['feetype_code'] != 'Other' && $catOne['feetype_code'] != 'Food' && $catOne['feetype_code'] != 'Bus') {
            $this->error = true;
            $this->errortip = "非可转换预收";
            return false;
        }
        $code = $this->LgArraySwitch(array("Deposit" => "定金", "Manage" => "管理费", "Times" => "课次", "Sales" => "教材", "Bus" => "校车", "Food" => "伙食", "Other" => "其他", "Member" => "会员", "OtLv" => "口语评测", "Forward" => "结转金额"));

        $stublcOne = $this->getStuBalance($request['student_id'], $this->company_id, $this->school_id, $catOne['companies_id']);

        if ($catOne['feetype_code'] == 'Deposit') {
            $note = $this->LgStringSwitch($code[$catOne['feetype_code']] . '预收转账户金额');
        } else {
            $note = $this->LgStringSwitch($code[$catOne['feetype_code']] . '预收转账户不可退金额');
        }

        $balancelog_data = array();
        $balancelog_data['company_id'] = $this->company_id;
        $balancelog_data['companies_id'] = $catOne['companies_id'];
        $balancelog_data['school_id'] = $this->school_id;
        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $balancelog_data['student_id'] = $request['student_id'];
        $balancelog_data['trading_pid'] = $trading_pid;
        $balancelog_data['balancelog_playname'] = $note;
        $balancelog_data['balancelog_playclass'] = '+';
        if ($catOne['feetype_code'] == 'Deposit') {
            $balancelog_data['balancelog_class'] = 0;
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $price;
        } else {
            $balancelog_data['balancelog_class'] = 2;
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $price;
        }
        $balancelog_data['balancelog_playamount'] = $price;
        $balancelog_data['balancelog_reason'] = $request['reason'];
        $balancelog_data['balancelog_time'] = $request['create_time'];
        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

        $log = array();
        $log['student_id'] = $request['student_id'];
        $log['trading_pid'] = $trading_pid;
        $log['coursetype_id'] = $catOne['coursetype_id'];
        $log['coursecat_id'] = $catOne['coursecat_id'];
        $log['feetype_code'] = $catOne['feetype_code'];
        $log['school_id'] = $this->school_id;
        $log['companies_id'] = $catOne['companies_id'];
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $catOne['coursecatbalance_figure'];
        $log['log_playamount'] = $price;
        $log['log_finalamount'] = $catOne['coursecatbalance_figure'] - $price;
        $log['log_fromme'] = $catOne['coursecatbalance_time'];

        if ($catOne['coursecatbalance_figure'] == 0 || $catOne['coursecatbalance_time'] == 0) {
            $log['log_playme'] = 0;
            $log['log_finaltime'] = $catOne['coursecatbalance_time'];
        } else {
            $log['log_playme'] = ceil($price / ($catOne['coursecatbalance_figure'] / $catOne['coursecatbalance_time']));
            $log['log_finaltime'] = $catOne['coursecatbalance_time'] - ceil($price / ($catOne['coursecatbalance_figure'] / $catOne['coursecatbalance_time']));
        }
        $log['log_time'] = $request['create_time'];
        $log['log_playname'] = $note;
        $log['log_reason'] = $request['reason'];

        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

        $data = array();
        if ($catOne['coursecat_id'] == '0' && $catOne['feetype_code'] == 'Deposit') {
            $data['student_balance'] = $stublcOne['student_balance'] + $price;
        } else {
            $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'] + $price;
        }
        $this->DataControl->updateData("smc_student_balance", "student_id='{$request['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$catOne['companies_id']}'", $data);

        $data = array();
        $data['coursecatbalance_figure'] = $catOne['coursecatbalance_figure'] - $price;
        $data['coursecatbalance_time'] = $log['log_finaltime'];
        $data['coursecatbalance_updatatime'] = $request['create_time'];
        $this->DataControl->updateData("smc_student_coursecatbalance", "coursecatbalance_id='{$request['coursecatbalance_id']}'", $data);
        return true;
    }


    function turnOutTimes($request)
    {
        if ($request['feetype_code'] == 'Sales') {
            $a = $this->DataControl->selectOne("SELECT p.post_istopjob FROM gmc_staffer_postbe AS sp
            LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
            WHERE sp.postbe_id = '{$request['re_postbe_id']}'");
            if ($a['post_istopjob'] != '1' || $request['re_postbe_id'] == '0') {
                ajax_return(array('error' => 1, 'errortip' => "您没有权限操作教材预收转出或认缴，请联系财务操作"), $this->companyOne['company_language']);
            }
        }

        if (!$request['create_time']) {
            $request['create_time'] = date("Y-m-d");
        }
        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursetype_id,coursecat_id,feetype_code,companies_id", "coursecatbalance_id='{$request['coursecatbalance_id']}' and school_id='{$request['school_id']}'");
        if (!$catOne) {
            $this->error = true;
            $this->errortip = "该预收不存在预收";
            return false;
        }

        if ($request['price'] || $request['price'] == 0) {
            if ($request['price'] > $catOne['coursecatbalance_figure'] || $request['price'] == 0) {
                $this->error = true;
                $this->errortip = "请填写正确金额";
                return false;
            } else {
                $price = $request['price'];
            }
        } else {
            $price = $catOne['coursecatbalance_figure'];
        }

        if ($catOne['feetype_code'] != 'Times' && $catOne['feetype_code'] != 'Manage' && $catOne['feetype_code'] != 'Forward' && $catOne['feetype_code'] != 'Sales' && $catOne['feetype_code'] != 'Food' && $catOne['feetype_code'] != 'Bus') {
            $this->error = true;
            $this->errortip = "非可转换预收";
            return false;
        }

        do {
            $trading_pid = $this->createOrderPid('ZC');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

        $stublcOne = $this->getStuBalance($request['student_id'], $this->company_id, $this->school_id, $catOne['companies_id']);

        $code = $this->LgArraySwitch(array("Times" => "课程", "Manage" => "管理费", "Forward" => "结转金额", "Sales" => "教材", "Food" => "伙食", "Bus" => "校车"));

        $balancelog_data = array();
        $balancelog_data['company_id'] = $this->company_id;
        $balancelog_data['companies_id'] = $catOne['companies_id'];
        $balancelog_data['school_id'] = $this->school_id;
        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $balancelog_data['student_id'] = $request['student_id'];
        $balancelog_data['trading_pid'] = $trading_pid;
        $balancelog_data['balancelog_class'] = 0;
        $balancelog_data['balancelog_playname'] = $this->LgStringSwitch($code[$catOne['feetype_code']] . '预收转账户金额');
        $balancelog_data['balancelog_playclass'] = '+';
        $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
        $balancelog_data['balancelog_playamount'] = $price;
        $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $price;
        $balancelog_data['balancelog_reason'] = $request['reason'];
        $balancelog_data['balancelog_time'] = strtotime($request['create_time']);
        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

        $log = array();
        $log['student_id'] = $request['student_id'];
        $log['trading_pid'] = $trading_pid;
        $log['coursetype_id'] = $catOne['coursetype_id'];
        $log['coursecat_id'] = $catOne['coursecat_id'];
        $log['feetype_code'] = $catOne['feetype_code'];
        $log['school_id'] = $this->school_id;
        $log['companies_id'] = $catOne['companies_id'];
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $catOne['coursecatbalance_figure'];
        $log['log_playamount'] = $price;
        $log['log_finalamount'] = $catOne['coursecatbalance_figure'] - $price;
        $log['log_fromme'] = $catOne['coursecatbalance_time'];
        $log['log_playme'] = $catOne['coursecatbalance_time'] * ceil($price / $catOne['coursecatbalance_figure']);
        $log['log_finaltime'] = $catOne['coursecatbalance_time'] - $catOne['coursecatbalance_time'] * ceil($price / $catOne['coursecatbalance_figure']);
        $log['log_time'] = strtotime($request['create_time']);
        $log['log_playname'] = $this->LgStringSwitch($code[$catOne['feetype_code']] . '预收转账户金额');
        $log['log_reason'] = $request['reason'];

        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

        $data = array();
        $data['student_balance'] = $stublcOne['student_balance'] + $price;
        $this->DataControl->updateData("smc_student_balance", "student_id='{$request['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$catOne['companies_id']}'", $data);

        $data = array();
        $data['coursecatbalance_figure'] = $catOne['coursecatbalance_figure'] - $price;
        $data['coursecatbalance_time'] = $catOne['coursecatbalance_time'] - $catOne['coursecatbalance_time'] * ceil($price / $catOne['coursecatbalance_figure']);
        $data['coursecatbalance_updatatime'] = strtotime($request['create_time']);
        $this->DataControl->updateData("smc_student_coursecatbalance", "coursecatbalance_id='{$request['coursecatbalance_id']}'", $data);
        return true;
    }

    function writeOffTimes($request)
    {

        $sql = "select (select ss.class_id
              from smc_student_study as ss
              left join smc_class as c on c.class_id=ss.class_id
              where c.course_id=scb.course_id and ss.school_id='{$this->school_id}' and ss.student_id=scb.student_id and ss.study_isreading=1 limit 0,1) as class_id
              ,scb.coursebalance_time,scb.coursebalance_figure
              from smc_student_coursebalance as scb
              where scb.company_id='{$this->company_id}' and scb.school_id='{$this->school_id}' and scb.student_id='{$request['student_id']}' and scb.course_id='{$request['course_id']}'";
        $classOne = $this->DataControl->selectOne($sql);
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$request['course_id']}'");
        $buyNum = $courseOne['course_classnum'];
        if ($classOne['class_id'] > 0) {
            $sql = "select count(hour_id) as noNum
                  from smc_class_hour as sch
                  where sch.class_id='{$classOne['class_id']}' and sch.hour_ischecking=0 limit 0,1
                  ";
            $hourOne = $this->DataControl->selectOne($sql);
            $buyNum = $hourOne['noNum'] - $classOne['coursebalance_time'];
            if ($buyNum <= 0) {
                $this->error = true;
                $this->errortip = "课程有购买完整,不可重复购买";
                return false;
            }
        }

        if ($classOne['coursebalance_time'] > 0) {
            $buyNum = $courseOne['course_classnum'] - $classOne['coursebalance_time'];
            if ($buyNum <= 0) {
                $this->error = true;
                $this->errortip = "课程有购买完整,不可重复购买";
                return false;
            }
        }

        if ($request['pc_id'] > 0) {
            $pcOne = $this->DataControl->getFieldOne("smc_fee_policy_course", "sell_unitprice,norm_unitprice,full_price", "pc_id='{$request['pc_id']}'");
            if ($buyNum == $courseOne['course_classnum']) {
                $price = $pcOne['full_price'];
            } else {
                $price = $buyNum * $pcOne['sell_unitprice'];
            }
        } else {
            $sql = "select fpt.tuition_unitprice as norm_unitprice,fpt.tuition_sellingprice,fpt.tuition_buypiece from smc_fee_pricing_tuition as fpt where fpt.pricing_id='{$request['pricing_id']}' and fpt.course_id='{$request['course_id']}'";
            $pcOne = $this->DataControl->selectOne($sql);
            if ($pcOne) {
                $pcOne['sell_unitprice'] = ceil($pcOne['tuition_sellingprice'] / $pcOne['tuition_buypiece']);
                $price = $buyNum * $pcOne['sell_unitprice'];
            } else {
                $this->error = true;
                $this->errortip = "传值错误";
                return false;
            }
        }


        $courseData = array();
        $courseData['agreement_id'] = 0;
        $courseData['pricing_id'] = $request['pricing_id'];
        $courseData['course_id'] = $request['course_id'];
        $courseData['sellingprice'] = $price;
        $courseData['num'] = $buyNum;
        $courseData['class_id'] = 0;
        $courseData['starttime'] = "";
        $courseData['discount_id'] = 0;
        $courseData['market_price'] = 0;
        $courseData['deductionmethod'] = 0;
        $courseData['is_discount'] = 0;
        $courseData['couponList'] = 0;
        $courseData['norm_unitprice'] = $pcOne['norm_unitprice'];
        $courseData['goodsList'] = array();
        $courseData['itemsList'] = array();
        $courseData['couponList'] = '';

        $data = array();
        $data['is_forward'] = 1;
        $data['company_id'] = $this->company_id;
        $data['create_time'] = $request['create_time'];
        $data['school_id'] = $this->school_id;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $request['student_id'];

        $data['coupon_price'] = 0;
        $data['market_price'] = 0;
        $data['giveforwardprice'] = 0;
        $data['all_market_price'] = 0;

        $data['price'] = $price;

        $tem_data = array();
        $tem_data[] = $courseData;
        $data['list'] = json_encode($tem_data, JSON_UNESCAPED_UNICODE);
        $RegistrationModel = new \Model\Smc\RegistrationModel($this->publicarray);
        $order_pid = $RegistrationModel->createOrder($data, 1);

        if (!$order_pid) {
            $this->error = true;
            $this->errortip = $RegistrationModel->errortip;
            return false;
        }


        return $order_pid;
    }


    function writeOffItems($request)
    {

        $sql = "select fpi.*,fp.course_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              where fpi.company_id='{$this->company_id}' and fpi.items_id='{$request['items_id']}'
              ";
        $itemsOne = $this->DataControl->selectOne($sql);
        $price = ceil($itemsOne['items_unitprice'] * $request['buyNum']);
        $itemsData = array();
        $itemsData['class'] = 0;
        $itemsData['items_id'] = $request['items_id'];
        $itemsData['course_id'] = $itemsOne['course_id'];
        $itemsData['feeitem_branch'] = $itemsOne['feeitem_branch'];
        $itemsData['buypiece'] = $request['buyNum'];
        $itemsData['buynums'] = "1";
        $itemsData['unitprice'] = $itemsOne['items_unitprice'];

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['create_time'] = $request['create_time'];
        $data['school_id'] = $this->school_id;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $request['student_id'];
        $data['price'] = $price;
        $tem_data = array();
        $tem_data[] = $itemsData;
        $data['list'] = json_encode($tem_data, JSON_UNESCAPED_UNICODE);

        $GoodsFeeModel = new \Model\Smc\GoodsFeeModel($this->publicarray);
        $order_pid = $GoodsFeeModel->buyGoods($data, 1, $itemsOne['feeitem_branch']);

        return $order_pid;

    }

    function writeOffManage($request)
    {
        $monthNum = $request['monthNum'];
        if ($request['create_time']) {
            $day = $request['create_time'];
        } else {
            $day = date("Y-m-d", time());
        }
        $buyNum = 0;
        for ($i = 1; $i <= $monthNum; $i++) {
            $month = date('Y-m', strtotime("$day +$i month"));
            $buyNum += GetMonthWorkday($month . "01");
        }

        $catOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,coursecat_id,feetype_code", "coursecatbalance_id='{$request['coursecatbalance_id']}'");

        $courseData = array();
        $courseData['agreement_id'] = 0;
        $courseData['pricing_id'] = 0;
        $courseData['course_id'] = $request['course_id'];
        $courseData['sellingprice'] = $catOne['coursecatbalance_figure'];
        $courseData['num'] = $buyNum;
        $courseData['class_id'] = "";
        $courseData['starttime'] = "";
        $courseData['discount_id'] = "";
        $courseData['market_price'] = "";
        $courseData['deductionmethod'] = 0;

        $data = array();
        $data['is_forward'] = 1;
        $data['company_id'] = $this->company_id;
        $data['create_time'] = $request['create_time'];
        $data['school_id'] = $this->school_id;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $request['student_id'];

        $data['coupon_price'] = 0;
        $data['market_price'] = 0;
        $data['giveforwardprice'] = 0;
        $data['all_market_price'] = 0;

        $data['price'] = $catOne['coursecatbalance_figure'];
        $tem_data = array();
        $tem_data[] = $courseData;
        $data['list'] = json_encode($tem_data, JSON_UNESCAPED_UNICODE);

        $RegistrationModel = new \Model\Smc\RegistrationModel($this->publicarray);
        $order_pid = $RegistrationModel->createOrder($data, 2);
        return $order_pid;
    }


}