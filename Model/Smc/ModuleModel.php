<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Smc;

class  ModuleModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $schoolOne = array();//操作校区
    public $publicarray = array();
    public $moduleOne = array();

    function __construct($publicarray=array()) {
        parent::__construct ();
        if(is_array($publicarray)){
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray){
        if(isset($publicarray['company_id'])){
            $this->verdictCompany($publicarray['company_id']);
        }else{
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if(isset($publicarray['school_id'])){
            $this->verdictSchool($publicarray['school_id']);
        }else{
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if(isset($publicarray['staffer_id'])){
            $this->verdictStaffer($publicarray['staffer_id']);
        }else{
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证校园信息
    function verdictSchool($school_id)
    {
        $this->schoolOne = $this->DataControl->getFieldOne("smc_school",
            "school_id,company_id,school_branch,school_shortname,school_cnname,school_openclass,school_isclose,school_istemporaryclose,school_temporaryclosetip"
            , "school_id = '{$school_id}'");
        if (!$this->schoolOne) {
            $this->error = true;
            $this->errortip = "校园信息不存在";
            return false;
        } else {
            return true;
        }
    }
    //验证订单信息
    function verdictStaffer($staffer_id){

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname,staffer_enname,staffer_mobile","staffer_id = '{$staffer_id}'");

        if(!$this->stafferOne){
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }else{
            return true;
        }
    }

    //机构管理 -- 首页
    function getModuleList()
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module','module_name',"module_class = '2'");
        $result['title'] = $module_name['module_name'];
        $data = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '2'");
        $data = $this->tree($data);

        $result['children'] = $data;
        return $result;
    }

    //机构管理 -- 首页
    function getModuleListUrl()
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module','module_name',"module_class = '2'");
        $result['title'] = $module_name['module_name'];
        $data = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '2'");
        $data = $this->trees($data);
        $result['children'] = $data;
        return $result;

    }

    function tree($items)
    {
        $son = array();
        if(is_array($items)){
            foreach($items as $k=>&$v) {
                if($v['father_id'] == 0) {
                    $son[$k]['label'] = $v['module_name'];
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['module_markurl'] = $v['module_markurl'].'?module_id='.$v['module_id'];
                    $son[$k]['module_icon'] = $v['module_icon'];
                    foreach ($items as $key=>$value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $son[$k]['children'][$key]['label'] = $value['module_name'];
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            $son[$k]['children'][$key]['module_markurl'] = $value['module_markurl'].'?module_id='.$value['module_id'];
                            $son[$k]['children'][$key]['module_icon'] = $value['module_icon'];
                        }
                    }
                }
            }
        }
        return $son;
    }

    function getPowerList($request){

        $contractOne = $this->getContract($this->companyOne['company_id']);
        if (!$contractOne) {
            $this->getPowerListbak($request);
        }

        //619 620 公开课
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$request['module_class']}'");
        if (!$promoduleList) {
            return array();
        }

        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $datawhere = " 1 ";

        if($this->schoolOne['school_openclass']==0){
            $datawhere.=" and m.module_id not in (619,620)";
        }

        if ($this->companyOne && $this->companyOne['company_isachieve'] == 0) {
            $datawhere .= " and m.module_id not in (634,635,636)";
        }

        if($postbeOne['school_id'] !=='0'){
            $sql = "select m.module_id as id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                where {$datawhere} and m.product_id='{$request['module_class']}' and u.postpart_id = '{$postbeOne['postpart_id']}' and module_isset = '0'
                having status=1
                order by m.module_weight asc,m.module_id asc";

            $tsql = "select m.module_id as id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                where {$datawhere} and m.product_id='{$request['module_class']}' and u.postpart_id = '{$postbeOne['postpart_id']}' and module_isset = '1'
                having status=1
                order by m.module_weight asc,m.module_id asc";

        }else {
            $sql = "select m.module_id as id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id,u.postpart_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                left join gmc_company_postrole as p on p.postpart_id=u.postpart_id
                where {$datawhere} and m.product_id='{$request['module_class']}' and p.postrole_id = '{$postbeOne['postrole_id']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}') and module_isset = '0'
                having status=1
                order by m.module_weight asc,m.module_id asc";
            $tsql = "select m.module_id as id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id,u.postpart_id,u.postpart_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->companyOne['company_id']}' and ed.module_id=m.module_id),1) as status
                from imc_module as m
                inner join smc_staffer_usermodule as u on u.module_id=m.module_id
                left join gmc_company_postrole as p on p.postpart_id=u.postpart_id
                where {$datawhere} and m.product_id='{$request['module_class']}' and p.postrole_id = '{$postbeOne['postrole_id']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}') and module_isset = '1'
                having status=1
                order by m.module_weight asc,m.module_id asc";
        }

        $moduleList = $this->DataControl->selectClear($sql);
        $settingList = $this->DataControl->selectClear($tsql);

        $tree = $this->getModuleTree($moduleList,'id','url');
        $settingtree = $this->getModuleTree($settingList,'id','url');

        $result = array();
        $result['children'] = $tree;
        $result['setting'] = $settingtree;
        return $result;
    }

    function getPowerListbak($paramArray)
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module','module_name',"module_class = '{$paramArray['module_class']}'");
        $result['title'] = $module_name['module_name'];

        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$paramArray['re_postbe_id']}'");

        $company_ismajor = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$paramArray['company_id']}'");

        $datawhere=" 1 ";

        if($this->schoolOne['school_openclass']==0){
            $datawhere.=" and m.module_id not in (619,620)";
        }

        if ($this->companyOne && $this->companyOne['company_isachieve'] == 0) {
            $datawhere .= " and m.module_id not in (634,635,636)";
        }


        if($postbeOne['school_id'] !=='0'){
            $data = $this-> DataControl->selectClear(" SELECT
                u.module_id, u.postpart_id, m.father_id, m.module_markurl, m.module_icon, m.module_name
            FROM smc_staffer_usermodule AS u 
            LEFT JOIN imc_module AS m ON u.module_id = m.module_id
            WHERE {$datawhere} and u.postpart_id = '{$postbeOne['postpart_id']}' and m.module_ismajor <= '{$company_ismajor['company_ismajor']}' and m.module_class = '{$paramArray['module_class']}' and module_isset = '0' order by module_weight ASC");
            $setting = $this-> DataControl->selectClear(" SELECT
                u.module_id, u.postpart_id, m.father_id, m.module_markurl, m.module_icon, m.module_name
            FROM smc_staffer_usermodule AS u 
            LEFT JOIN imc_module AS m ON u.module_id = m.module_id
            WHERE {$datawhere} and u.postpart_id = '{$postbeOne['postpart_id']}' and m.module_ismajor <= '{$company_ismajor['company_ismajor']}' and m.module_class = '{$paramArray['module_class']}' and module_isset = '1' order by module_weight ASC");
        }else {
            $data = $this->DataControl->selectClear("
            SELECT u.module_id, u.postpart_id,  m.father_id, m.module_markurl, m.module_icon, m.module_name
            FROM smc_staffer_usermodule AS u 
            LEFT JOIN imc_module AS m ON u.module_id = m.module_id 
            LEFT JOIN gmc_company_postrole AS p ON u.postpart_id = p.postpart_id
            WHERE {$datawhere} and p.postrole_id = '{$postbeOne['postrole_id']}' and m.module_ismajor <= '{$company_ismajor['company_ismajor']}' and m.module_class = '{$paramArray['module_class']}' and module_isset = '0' order by module_weight ASC");
            $setting = $this->DataControl->selectClear("
            SELECT u.module_id, u.postpart_id,  m.father_id, m.module_markurl, m.module_icon, m.module_name
            FROM smc_staffer_usermodule AS u 
            LEFT JOIN imc_module AS m ON u.module_id = m.module_id 
            LEFT JOIN gmc_company_postrole AS p ON u.postpart_id = p.postpart_id
            WHERE {$datawhere} and p.postrole_id = '{$postbeOne['postrole_id']}' and m.module_ismajor <= '{$company_ismajor['company_ismajor']}' and m.module_class = '{$paramArray['module_class']}' and module_isset = '1' order by module_weight ASC");
        }

        $data = $this->powerTree($data,$paramArray);
        $setting = $this->powerTree($setting,$paramArray);

        $result['children'] = $data;
        $result['setting'] = $setting;
        ajax_return(array('error' => 0,'errortip' => "获取成功!", 'result' => $result),$this->companyOne['company_language'],1);
    }

    function getModuleTree($moduleList,$field='id',$url='url'){
        $tree=$this->getTree($moduleList,$field,'father_id','children');

        foreach($tree as $k=>$v){
            if($v['father_id']!=0){
                unset($tree[$k]);
            }
        }

        if($tree){
            $mum=0;
            foreach($tree as $key=>$val){
                if (!isset($val['children'])) {
                    unset($tree[$key]);
                }else{
                    if(!$val[$url] || $val['module_level']==2){
                        $tree[$key][$url]=$val['children'][0][$url];
                    }
                    $tree[$key]['index']=$mum;

                    $m=0;
                    if($val['children']){
                        foreach($val['children'] as $k=>$v){
                            if(!$v[$url] || $v['module_level']==2){
                                if($v['children'][0][$url] && isset($v['children'][0][$url])){
                                    $tree[$key]['children'][$k][$url]=$v['children'][0][$url];
                                }
                            }
                            $tree[$key]['children'][$k]['activeIndex']=$mum.'-'.($m+1);
                            $m++;
                        }

                        $mum++;
                    }
                }
            }
        }

        return $tree;
    }

    function getArray($arr,$field,$child){
        foreach ($arr as $v) {
            if ($v[$field]) {
                $this->moduleOne[]=$v[$field];
                if($v[$child]){
                    $this->getArray($v[$child],$field,$child);
                }
            }
        }
    }

    function powerTree($items,$paramArray)
    {
        $son = array();
        $count = 0;
        $counts = -1;
        if(is_array($items)){
            foreach($items as $k=>&$v) {
                if($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'].'?module_id='.$v['module_id'];
                    $son[$k]['postpart_id'] = $v['postpart_id'];
                    $son[$k]['icon'] = $v['module_icon'];
                    foreach ($items as $key=>$value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $postpart_id = $this->DataControl->getFieldOne("gmc_staffer_postbe","postpart_id","postbe_id = '{$paramArray['postbe_id']}'");
                            $url = $this->DataControl->selectClear("SELECT
                                    m.module_markurl
                                FROM
                                    imc_module AS m
                                    LEFT JOIN smc_staffer_usermodule AS u ON m.module_id = u.module_id 
                                WHERE
                                    m.father_id = '{$value['module_id']}' and u.postpart_id = '{$postpart_id['postpart_id']}'
                                GROUP BY
                                    m.module_id
                                    order by module_weight ASC");

                            $a = $this->DataControl->getFieldOne("smc_school","school_coursetime,school_appointment,school_duration","school_id = '{$paramArray['school_id']}'");

                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts.'-'.$count;
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            $son[$k]['children'][$key]['postpart_id'] = $value['postpart_id'];
                            if($url){
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            }else{
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }

                            if($url && $value['module_id'] == '66'){
                                if($a['school_coursetime'] == '1'){
                                    $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                                }elseif($a['school_coursetime'] == '0' && $a['school_appointment'] == '1'){
                                    $son[$k]['children'][$key]['url'] = $url[1]['module_markurl'];
                                }elseif($a['school_coursetime'] == '0' && $a['school_appointment'] == '0' && $a['school_duration'] == '1'){
                                    $son[$k]['children'][$key]['url'] = $url[2]['module_markurl'];
                                }
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys=>$values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    if($values['module_id'] == '307' || $values['module_id'] == '308' || $values['module_id'] == '309' || $values['module_id'] == '169'){
                                        if($a['school_coursetime'] == '1' && $values['module_id'] == '307'){
                                            $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                            $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                            $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                        }elseif($a['school_coursetime'] == '1' && $values['module_id'] == '169'){
                                            $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                            $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                            $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                        }elseif($a['school_appointment'] == '1' && $values['module_id'] == '309'){
                                            $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                            $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                            $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                        }elseif($a['school_duration'] == '1' && $values['module_id'] == '308'){
                                            $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                            $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                            $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                            $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                        }
                                    }else{
                                        $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                        $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                        $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                        $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                        $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                    }


                                }
                            }
                        }
                    }
                    $count = 0;
                }
            }
        }
        return $son;
    }


    function getPostrolePowerApi($paramArray){
        $sql="SELECT u.module_id AS id, m.module_name AS label FROM gmc_staffer_usermodule u
LEFT JOIN imc_module m ON m.module_id = u.module_id WHERE u.postrole_id = '{$paramArray['postrole_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["module_id"] = $this->LgStringSwitch("模块id");
        $field["module_name"] = $this->LgStringSwitch("模块名称");
        $result = array();
        if($postroleDetail){
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
        }else{
            $result["data"] = array();
        }
        return $result;
    }

    function getPostpartPowerApi($paramArray){
        $sql="SELECT u.module_id as id, m.module_name as label
            FROM
                smc_staffer_usermodule AS u
                LEFT JOIN imc_module AS m ON m.module_id = u.module_id 
            WHERE
                u.postpart_id = '{$paramArray['postpart_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["module_id"] = $this->LgStringSwitch("模块id");
        $field["module_name"] = $this->LgStringSwitch("模块名称");
        $result = array();
        if($postroleDetail){
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
        }else{
            $result["data"] = array();
        }
        return $result;
    }


    function trees($items)
    {
        $son = array();
        $count = 0;
        $counts = -1;
        if(is_array($items)){
            foreach($items as $k=>&$v) {
                if($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
//                    $son[$k]['url'] = $v['module_markurl'].'?module_id='.$v['module_id'];
                    $son[$k]['icon'] = $v['module_icon'];
                    foreach ($items as $key=>$value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $urls = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts.'-'.$count;
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if($urls){
                                $son[$k]['children'][$key]['url'] = $urls[0]['module_markurl'];
                            }else{
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys=>$values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];

                                }
                            }
                        }
                    }
                    $count = 0;
                }
            }
        }
        return $son;
    }




}
