<?php


namespace Model\Smc;

class DealModel extends modelTpl
{
    public $payfeeorderOne=false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray=array(),$order_pid=0) {
        parent::__construct ();
        if(is_array($publicarray)){
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
        if($order_pid !== '0'){
            $this->verdictpayFeeOrder($order_pid);
        }
    }

    function setPublic($publicarray){
        if(isset($publicarray['company_id'])){
            $this->company_id = $publicarray['company_id'];
        }else{
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if(isset($publicarray['school_id'])){
            $this->school_id = $publicarray['school_id'];
        }else{
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if(isset($publicarray['staffer_id'])){
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id=$publicarray['staffer_id'];
        }else{
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictpayFeeOrder($order_pid){
        $this->payfeeorderOne = $this->DataControl->getOne("smc_forward_dealorder","dealorder_pid = '{$order_pid}'");
        if(!$this->payfeeorderOne){
            $this->error = true;
            $this->errortip = "退费订单信息不存在";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($staffer_id){
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname,staffer_enname,staffer_mobile","staffer_id = '{$staffer_id}'");
        if(!$this->stafferOne){
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    function stuTrading($student_id,$code="",$companies_id,$time=''){
        if($time==''){
            $time=time();
        }
        $data= array();
        do{
            $trading_pid=$this->createOrderPid('JY');
        }while($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $data['trading_pid'] = $trading_pid ;
        $data['company_id'] = $this->company_id ;
        $data['school_id'] = $this->school_id ;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = $code;
        $data['trading_status'] = "1";
        $data['trading_createtime'] = $time;
        $data['staffer_id'] =$this->stafferOne['staffer_id'] ;
        if($this->DataControl->insertData("smc_student_trading" ,$data)){
            return $trading_pid;
        }else{
            return false;
        }
    }

    function dealorder($student_id,$balanceprice,$forwardprice,$course_id,$title='',$information='',$note='',$time='',$application_id=0){
        if($time==''){
            $time=time();
        }
        $dealorder =array();
        do{
            $dealorder_pid=$this->createOrderPid('JZ');
        }while($this->DataControl->selectOne("select dealorder_id from smc_forward_dealorder where dealorder_pid='{$dealorder_pid}' and company_id='{$this->company_id}' limit 0,1"));

        $courseBalanceOne=$this->DataControl->getFieldOne("smc_student_coursebalance","companies_id","student_id='{$student_id}' and school_id='{$this->school_id}' and course_id='{$course_id}'");

        $code='CourseForward';
        $dealorder['trading_pid'] = $this->stuTrading($student_id,$code,$courseBalanceOne['companies_id'],$time);
        $dealorder['dealorder_pid'] = $dealorder_pid ;
        $dealorder['company_id'] = $this->company_id;
        $dealorder['school_id'] = $this->school_id;
        $dealorder['companies_id'] = $courseBalanceOne['companies_id'];
        $dealorder['staffer_id'] = $this->stafferOne['staffer_id'];
        $dealorder['student_id'] = $student_id;
        $dealorder['dealorder_balanceprice'] = $balanceprice>0?$balanceprice:0;
        $dealorder['dealorder_forwardprice'] = $forwardprice>0?$forwardprice:0;
        $dealorder['dealorder_status'] = 1;
        $dealorder['application_id'] = $application_id;
        $dealorder['dealorder_createtime'] = $time;
        if($this->DataControl->insertData("smc_forward_dealorder" ,$dealorder)){
            $this->verdictpayFeeOrder($dealorder_pid);
            $this->dealorderTracks($title,$information,$note,$time);
            return $dealorder_pid;
        }else{
            return false;
        }
    }

    function dealtimeorder($student_id,$course_id,$to_course_id,$class_id,$to_class_id,$balanceprice,$forwardprice,$change_reason='',$title='',$information='',$time=''){
        if($time==''){
            $time=time();
        }
        $dealorder =array();

        $companiesOne=$this->getSchoolCourseCompanies($this->school_id,0,$course_id);

        do{
            $dealorder_pid=$this->createOrderPid('JZ');
        }while($this->DataControl->selectOne("select dealorder_id from smc_forward_dealorder where dealorder_pid='{$dealorder_pid}' and company_id='{$this->company_id}' limit 0,1"));
        $code='CourseForward';
        $dealorder['trading_pid'] = $this->stuTrading($student_id,$code,$companiesOne['companies_id'],$time);
        $dealorder['dealorder_pid'] = $dealorder_pid ;
        $dealorder['dealorder_type'] = '1' ;
        $dealorder['company_id'] = $this->company_id;
        $dealorder['school_id'] = $this->school_id;
        $dealorder['companies_id'] = $companiesOne['companies_id'];
        $dealorder['staffer_id'] = $this->stafferOne['staffer_id'];
        $dealorder['student_id'] = $student_id;
        $dealorder['class_id'] = $class_id;
        $dealorder['to_class_id'] = $to_class_id;
        $dealorder['dealorder_balanceprice'] = $balanceprice;
        $dealorder['dealorder_forwardprice'] = $forwardprice;
        $dealorder['dealorder_reason'] = $change_reason;
        $dealorder['dealorder_status'] = 0;
        $dealorder['dealorder_createtime'] = $time;
        if($this->DataControl->insertData("smc_forward_dealorder" ,$dealorder)){
            $this->verdictpayFeeOrder($dealorder_pid);
            $this->dealorderTracks($title,$information,$change_reason,$time);
            return $dealorder_pid;
        }else{
            return false;
        }
    }

    //生成订单跟踪
    function dealorderTracks($title='',$information='',$note='',$time=''){
        if($time==''){
            $time=time();
        }
        $orderTracksData=array();
        $orderTracksData['dealorder_pid']=$this->payfeeorderOne['dealorder_pid'];
        $orderTracksData['tracks_title']=$title;
        $orderTracksData['tracks_information']=$information;
        $orderTracksData['tracks_note']=$note;
        $orderTracksData['staffer_id']=$this->stafferOne['staffer_id'];
        $orderTracksData['tracks_playname']=$this->stafferOne['staffer_cnname'];
        $orderTracksData['tracks_time']=time();
        $this->DataControl->insertData("smc_forward_dealorder_tracks",$orderTracksData);
    }




}