<?php


namespace Model\Smc;

class ClockingModel extends modelTpl
{
	public $error = false;
	public $errortip = false;
	public $oktip = false;//正确提示
	public $bakerrorfuc = "errormotify";
	public $result = array();
	public $stafferOne = array();//操作人
	public $company_id = 0;//操作公司
	public $school_id = 0;//操作学校
	public $staffer_id = 0;//操作人
	public $publicarray = array();

	function __construct($publicarray=array()) {
		parent::__construct ();
		if(is_array($publicarray)){
			$this->setPublic($publicarray);
			$this->publicarray = $publicarray;
		}
	}

	function setPublic($publicarray){
		if(isset($publicarray['company_id'])){
			$this->company_id = $publicarray['company_id'];
		}else{
			$this->error = true;
			$this->errortip = "企业ID必须传入";
			return false;
		}
		if(isset($publicarray['school_id'])){
			$this->school_id = $publicarray['school_id'];
		}else{
			$this->error = true;
			$this->errortip = "学校ID必须传入";
			return false;
		}
		if(isset($publicarray['staffer_id'])){
			$this->verdictStaffer($publicarray['staffer_id']);
			$this->staffer_id=$publicarray['staffer_id'];
		}else{
			$this->error = true;
			$this->errortip = "操作ID必须传入";
			return false;
		}
	}
	//验证订单信息
	function verdictStaffer($staffer_id){

		$this->stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname,staffer_enname,staffer_mobile","staffer_id = '{$staffer_id}'");

		if(!$this->stafferOne){
			$this->error = true;
			$this->errortip = "教师信息不存在";
			return false;
		}else{
			return true;
		}
	}
	
	function getWeekClocking($request)
	{
		
		if (isset($request['start_day']) && $request['start_day'] != "") {
			$start_day = $request['start_day'];
		} else {
			$start_day = date("Y-m-d");
		}
		if (isset($request['end_day']) && $request['start_day'] != "") {
			$end_day  =$request['end_day'];
		}else{
			$end_day = date('Y-m-d', strtotime("+ 7 day"));
		}
		
		if (isset($request['p']) && $request['p'] !== '') {
			$page = $request['p'];
		} else {
			$page = '1';
		}
		if (isset($request['num']) && $request['num'] !== '') {
			$num = $request['num'];
		} else {
			$num = '10';
		}
		$pagestart = ($page - 1) * $num;
		
		
		$sql = "select ht.teaching_id,ch.hour_id,ch.hour_day,ch.class_id,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,cs.class_cnname,cs.class_branch,cm.classroom_cnname,ch.hour_inarrivenums,
				(select s.school_cnname from smc_school as s where s.school_id = cs.school_id ) as school_cnname,
				(select count(ssh.hourstudy_id) from smc_student_hourstudy as ssh where ssh.hour_id=ch.hour_id and ssh.class_id=ch.class_id and ssh.hourstudy_checkin =1 ) as hourstudy_num
      			from smc_class_hour as ch
				left join smc_class_hour_teaching as ht ON ht.hour_id = ch.hour_id
				left join smc_class as cs ON cs.class_id = ch.class_id
				left join smc_classroom as cm ON cm.classroom_id = ch.classroom_id
				where cs.school_id ='{$request['school_id']}' and  ht.staffer_id = '{$request['staffer_id']}' and  ch.hour_day>='{$start_day}'  and ch.hour_day<='{$end_day}' order by  ch.hour_day ASC limit {$pagestart},{$num} ";
		
		$teachList = $this->DataControl->selectClear($sql);
		
		if (!$teachList) {
			$teachList = array();
		} else {
			foreach ($teachList as $key => &$value) {
				if ($value['hour_ischecking'] == 1) {
					$value['hour_checkname'] = $this->LgStringSwitch('已考勤');
				} elseif ($value['hour_ischecking'] == -1) {
					$value['hour_checkname'] = $this->LgStringSwitch('已取消');
				} elseif ($value['hour_ischecking'] == 0) {
					$value['hour_checkname'] = $this->LgStringSwitch('待考勤');
				}
			}
		}
		
		$teaching_class = $this->DataControl->selectClear("
			select ht.class_id from smc_class_hour_teaching as ht
			left join  smc_class_hour as ch ON ch.hour_id = ht.hour_id
			left join smc_class as cs ON cs.class_id = ch.class_id
			where cs.school_id = '{$request['school_id']}' and ht.staffer_id = '{$request['staffer_id']}' and  ch.hour_day>='{$start_day}'  and ch.hour_day<='{$end_day}' group by ht.class_id
											   ");
		
		$clocking = array();
		if (!$teaching_class) {
			$teaching_class = array();
			$clocking['clock_percent'] = '0%';
			$clocking['class_num'] = '0';
			$clocking['hour_inarrivenums'] = '0';
			$clocking['hourstudy_num'] =   '0';
		} else {
			$arr_class =  array_column($teaching_class,'class_id');
			$str_class = implode(",", $arr_class);
			
			$sql = "select sum(ch.hour_inarrivenums) as hour_inarrivenums
				from  smc_class_hour as ch
                where  ch.hour_ischecking = 1 and ch.hour_day>='{$start_day}'  and ch.hour_day<='{$end_day}' and  ch.class_id  in ({$str_class}) limit 0,1 ";
			
			
			$clock = $this->DataControl->selectOne($sql);
			
			 $hourStudy = $this->DataControl->selectOne("select count(shy.hourstudy_id) as hourstudy_num  from  smc_class_hour as ch , smc_student_hourstudy as shy  where shy.hour_id = ch.hour_id and shy.hourstudy_checkin =1 and ch.hour_ischecking = 1 and ch.hour_day>='{$start_day}'  and ch.hour_day<='{$end_day}' and  ch.class_id  in ({$str_class}) limit 0,1" );
			$hourstudy_num = $hourStudy['hourstudy_num'] +0;
			
			if($clock['hour_inarrivenums'] > 0){
				$clocking['clock_percent'] = round($hourstudy_num/$clock['hour_inarrivenums'],2) * 100 .'%';
			}else{
				$clocking['clock_percent'] =  '0%';
			}
			
			$clocking['class_num'] = count($teaching_class);
			$clocking['hour_inarrivenums'] = $clock['hour_inarrivenums']+0 ;
			$clocking['hourstudy_num'] = $hourstudy_num +0;
		}
		
		$data['list'] = $teachList;
		$data['clocking'] = $clocking;
		
		return $data;
		
	}
	
}