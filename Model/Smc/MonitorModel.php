<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Smc;

class MonitorModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();
    //监控的参数  账号：ym18521530218
    const clientId = '0ab63e22a9d54e4a8620528f8cc64218';
    const clientSecret = 'b2605c8b4d444619b0af5830ed16bbce';
    const grantType = 'client_credentials';
    const scope = 'app';

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }
    //get
    public function curlGet($url,$Authorization){
        $header  = array(
            'Authorization:'.$Authorization
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
    //post
    public function curlPost($url,$post_data = array(),$Authorization){
        $header  = array(
            'Authorization:'.$Authorization,
        );
        if (is_array($post_data))
        {
            $post_data = http_build_query($post_data, null, '&');
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch , CURLOPT_URL , $url);
        curl_setopt($ch , CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch , CURLOPT_POST, 1);
        curl_setopt($ch , CURLOPT_POSTFIELDS, $post_data);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
    //post curl raw json
    public function curlRawJsonPost($url,$post_data = array(),$Authorization){
        $httph = curl_init($url);
        curl_setopt($httph, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($httph, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($httph, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($httph, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)");

        $postdatajson = json_encode($post_data);

        $headers = array();
        $headers[] = 'Authorization:'.$Authorization;
        $headers[] = 'Content-Type: application/json';

        curl_setopt($httph, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($httph, CURLOPT_POST, 1);//设置为POST方式
        curl_setopt($httph, CURLOPT_POSTFIELDS, $postdatajson);

        curl_setopt($httph, CURLOPT_CONNECTTIMEOUT, 3);//设置超时时间
        $rst = curl_exec($httph);

        //检查是否404（网页找不到）
        $httpCode = curl_getinfo($httph, CURLINFO_HTTP_CODE);
        // var_dump($httpCode);
        if ($httpCode == 404) {
            return false;
        } else {
//            return $rst;
            return true;
        }
        curl_close($httph);
    }


    //获取监控的 token
    function getMonitorToken(){
        $paramarray = array(
            'client_id' => self::clientId,
            'client_secret' => self::clientSecret,
            'grant_type' => self::grantType,
            'scope' => self::scope
        );
        $resultJson = request_by_curl("https://api2.hik-cloud.com/oauth/token",dataEncode($paramarray),"POST",array());
        $resultArray = json_decode($resultJson,true);
        return $resultArray;
    }

    //999024->监控->监控位置管理列表
    function getMonitorPositionList($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "学校ID不能为空！";
            $this->result = array();
            return false;
        }
        $datawhere=" 1 and d.company_id = '{$request['company_id']}' and d.school_id = '{$request['school_id']}' and m.position_id > 0 ";
        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (p.position_name like '%{$request['keyword']}%' )";
        }

        $sql = " SELECT p.* 
                FROM gmc_monitor_device AS d 
                LEFT JOIN gmc_monitor_device_mate as m ON (d.device_id = m.device_id and d.company_id = m.company_id) 
                LEFT JOIN gmc_monitor_position as p ON (m.position_id = p.position_id and m.company_id = p.company_id)
                WHERE {$datawhere} 
                GROUP BY p.position_id  ";

        $dataList = $this->DataControl->selectClear($sql);

        if($dataList){
            foreach ($dataList as &$dataVar){
                if($this->DataControl->selectOne("select staffer_id from smc_staffer WHERE account_class = '1' and staffer_id = '{$request['staffer_id']}' limit 0,1 ")) {
                    $dataVar['userfunc_videotype'] = array(0,1);
                }else{
                    $postpart = $this->DataControl->selectOne("select u.userfunc_videotype from gmc_staffer_postbe as p 
                            LEFT JOIN gmc_monitor_position_userfunc as u ON p.postpart_id = u.postpart_id 
                            WHERE p.company_id = '{$request['company_id']}' and p.school_id = '{$request['school_id']}' and p.staffer_id = '{$request['staffer_id']}' and u.position_id = '{$dataVar['position_id']}' and u.company_id = p.company_id limit 0,1 ");

                    $postpartTwo = $this->DataControl->selectOne("SELECT u.userfunc_videotype 
                                FROM gmc_staffer_postbe  as p 
                                LEFT JOIN smc_school_postpart as t ON p.postpart_id = t.postpart_id 
                                LEFT JOIN gmc_monitor_position_userfunc as u ON t.postpart_id = u.postpart_id 
                                WHERE p.company_id = '{$request['company_id']}' 
                                AND p.staffer_id = '{$request['staffer_id']}' 
                                AND u.position_id = '{$dataVar['position_id']}' 
                                AND u.company_id = p.company_id 
                                LIMIT 0,1");

                    if($postpart['userfunc_videotype'] || $postpart['userfunc_videotype'] == '0') {
                        $dataVar['userfunc_videotype'] = explode(",", $postpart['userfunc_videotype']);
                    }elseif($postpartTwo['userfunc_videotype'] || $postpartTwo['userfunc_videotype'] == '0'){
                        $dataVar['userfunc_videotype'] = explode(",", $postpartTwo['userfunc_videotype']);
                    }else{
                        $dataVar['userfunc_videotype'] = array();
                    }
                }
            }
        }
        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();

        if($dataList){
            $this->error = 0;
            $this->errortip = "位置信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无位置信息信息";
            $this->result = $result;
            return false;
        }
    }

    //999024->监控->学校位置 设备实时预览
    function getTimelyPositionVideo($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "学校ID不能为空！";
            $this->result = array();
            return false;
        }
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

//        $stafffunc = $this->DataControl->selectOne("select * from smc_school_postpart as p
//                    WHERE 1 and p.company_id = '{$request['company_id']}' and p.school_id = '0'
//                    ORDER BY p.postpart_id DESC ");

        $schoolOne = $this->DataControl->selectOne("select school_monitorcode from smc_school WHERE school_id = '{$request['school_id']}' and company_id = '{$request['company_id']}' limit 0,1 ");
        if($schoolOne['school_monitorcode'] == ''){
            $this->error = 1;
            $this->errortip = "学校监控设备码不能为空！";
            $this->result = array();
            return false;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $deviceList = $this->DataControl->selectClear("select d.device_deviceserial,d.device_channelname,d.device_channelno,d.device_channelid,d.device_remarks from gmc_monitor_device_mate as m 
                      LEFT JOIN gmc_monitor_device as d ON (m.device_id = d.device_id and m.company_id = d.company_id) 
                      WHERE m.company_id = '{$request['company_id']}' and m.position_id = '{$request['position_id']}' and d.school_id = '{$request['school_id']}' 
                      limit {$pagestart},{$num} ");

        if($request['isrealtime'] == '1') {
            if (is_array($deviceList)) {
                //获取监控的 token
                $resultArray = $this->getMonitorToken($request);
                $Authorization = $resultArray['token_type'] . $resultArray['access_token'];

                foreach ($deviceList as &$deviceVar) {
                    /*针对的直播的一种方式目前页面上用的是  UIKit  用不到这个些信息
                     *
                     * //开通标准流预览功能
                    $url = "https://api2.hik-cloud.com/api/v1/open/basic/channels/actions/live/video/open";
                    $postdata = array();
                    $postdata['deviceSerial'] = $deviceVar['device_deviceserial'];
                    $postdata['channelNos'] = array($deviceVar['device_channelno']);
                    $channelsjson = $this->curlRawJsonPost($url, $postdata, $Authorization);
                    //$channelsArray = json_decode($channelsjson,true);*/

                    /*//POST参数  //获取设备下所有 通道（即摄像头）
                    $qcode = "https://api2.hik-cloud.com/api/v1/open/basic/channels/actions/live/limitedAddress/get?deviceSerial={$deviceVar['device_deviceserial']}&channelNo={$deviceVar['device_channelno']}&expireTime=3600";//获取有效期标准流预览地址  有时效性
//                $qcode = "https://api2.hik-cloud.com/api/v1/open/basic/channels/actions/live/address/get?deviceSerial={$deviceVar['device_deviceserial']}&channelNo={$deviceVar['device_channelno']}";//获取标准流预览地址
                    $channelsjson = $this->curlGet($qcode, $Authorization);
                    $channelsArray = json_decode($channelsjson, true);

                    $deviceVar['hls'] = $channelsArray['data']['hls'];//HLS流畅标准流预览地址
                    $deviceVar['hlsHd'] = $channelsArray['data']['hlsHd'];//HLS高清标准流预览地址
                    $deviceVar['rtmp'] = $channelsArray['data']['rtmp'];//RTMP流畅标准流预览地址
                    $deviceVar['rtmpHd'] = $channelsArray['data']['rtmpHd'];//RTMP高清标准流预览地址
                    $deviceVar['beginTime'] = $channelsArray['data']['beginTime'];//开始时间
                    $deviceVar['endTime'] = $channelsArray['data']['endTime'];//结束时间*/
                    //清晰度
                    $deviceVar['definition'] = '.hd';//清晰度   .hd 高清
                    //播放类型
                    $deviceVar['playbacktype'] = '.live';//播放类型
                }
            }
        }else{
            if (is_array($deviceList)) {
                foreach ($deviceList as &$deviceVar) {
                    //清晰度
                    $deviceVar['definition'] = '';//清晰度   .hd对应高清，不传对应流畅
                    //播放类型
                    $deviceVar['playbacktype'] = '.rec';//播放类型
                }
            }
        }

        if(isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(d.device_channelid) as datanum from gmc_monitor_device_mate as m 
                      LEFT JOIN gmc_monitor_device as d ON (m.device_id = d.device_id and m.company_id = d.company_id) 
                      WHERE m.company_id = '{$request['company_id']}' and m.position_id = '{$request['position_id']}' and d.school_id = '{$request['school_id']}'   ");
            $count = $all_num['datanum'] + 0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = $deviceList;
        $result["count"] = $count;

        if($deviceList){
            $this->error = 0;
            $this->errortip = "设备获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无设备信息";
            $this->result = $result;
            return false;
        }
    }

    //999024->监控->获取视频取流时需要的认证信息
    function getVideoAuthenInfoApi($request){
        //获取监控的 token
        $resultArray = $this->getMonitorToken($request);
        $Authorization = $resultArray['token_type'] . $resultArray['access_token'];

        //获取视频取流时需要的认证信息
        $qcode = "https://api2.hik-cloud.com/v1/ezviz/account/info";
        $datajson = $this->curlGet($qcode, $Authorization);
        $dataArray = json_decode($datajson, true);

        if($dataArray['data'] && $dataArray['code'] == '200'){
            $this->error = 0;
            $this->errortip = "获取视频取流时需要的认证信息";
            $this->result = $dataArray['data'];
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无视频取流时需要的认证信息";
            $this->result = $dataArray['data'];
            return false;
        }
    }



}
