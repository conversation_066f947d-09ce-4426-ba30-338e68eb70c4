<?php


namespace Model\Smc;

use function Sodium\add;

class OrderHandleModel extends OrderModel
{
    function __construct($publicarray = array(), $order_pid = 0)
    {
        parent::__construct($publicarray, $order_pid);
    }

    //补费
    function supplementFee($student_id)
    {
        $surplusPrice = $this->surplusPrice();
        if ($surplusPrice == 0) {
            $data = array();
            $data['order_status'] = 4;
            $data['order_updatatime'] = time();
            if ($this->DataControl->updateData("smc_payfee_order", "company_id='{$this->company_id}' and school_id='{$this->school_id}' and student_id='{$student_id}' and order_pid='{$this->payfeeorderOne['order_pid']}'", $data)) {
                $this->error = true;
                $this->oktip = "补费成功";
                return true;
            } else {
                $this->error = true;
                $this->errortip = "补费记录错误";
                return false;
            }
        } elseif ($surplusPrice < 0) {
            $this->error = true;
            $this->errortip = "订单金额错误";
            return false;
        } else {
            $this->error = true;
            $this->oktip = "补费成功";
            return false;
        }
    }

    //
    function surplusPrice()
    {
        $surplusOne = $this->DataControl->selectOne("select sum(pay_price) as surplusPrice from smc_payfee_order_pay as p
where p.order_pid='{$this->payfeeorderOne['order_pid']}' and p.pay_issuccess <> '-1' and  p.paytype_code <> 'feewaiver'");
//        $surplusOne = $this->DataControl->selectOne("select sum(pay_price) as surplusPrice from smc_payfee_order_pay as p
//where p.order_pid='{$this->payfeeorderOne['order_pid']}' and p.pay_issuccess = '1' and  p.paytype_code <> 'feewaiver'" );
        if ($surplusOne) {
            $surplusPrice = $this->payfeeorderOne['order_paymentprice'] - $surplusOne['surplusPrice'];
            return $surplusPrice;
        } else {
            return false;
        }
    }

    function unSurplusPrice()
    {
        $surplusOne = $this->DataControl->selectOne("select sum(pay_price) as surplusPrice from smc_payfee_order_pay as p
where p.order_pid='{$this->payfeeorderOne['order_pid']}' and p.pay_issuccess =0");
        if ($surplusOne) {
            $surplusPrice = $this->payfeeorderOne['order_paymentprice'] - $surplusOne['surplusPrice'];
            return $surplusPrice;
        } else {
            return false;
        }
    }

    function stuTrading($student_id,$companies_id, $code = "", $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        do {
            $trading_pid = $this->createOrderPid('JY');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = $code;
        $data['trading_status'] = 0;
        $data['trading_createtime'] = $time;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        if ($this->DataControl->insertData("smc_student_trading", $data)) {
            return $trading_pid;
        } else {
            return false;
        }
    }

    function reAdvance($logOne, $one, $note)
    {
        $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursetype_id,coursecatbalance_figure,coursecatbalance_time,companies_id", "student_id='{$this->payfeeorderOne['student_id']}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursetype_id='{$logOne['coursetype_id']}' and coursecat_id='{$logOne['coursecat_id']}' and feetype_code='{$logOne['feetype_code']}'");
        $log = array();
        $log['student_id'] = $this->payfeeorderOne['student_id'];
        $log['trading_pid'] = $this->payfeeorderOne['trading_pid'];
        $log['coursetype_id'] = $stuCatOne['coursetype_id'];
        $log['coursecat_id'] = $logOne['coursecat_id'];
        $log['companies_id'] = $stuCatOne['companies_id'];
        $log['feetype_code'] = $logOne['feetype_code'];
        $log['school_id'] = $this->school_id;
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['log_playclass'] = '+';
        $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
        $log['log_playamount'] = $one['pay_price'];
        $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] + $one['pay_price'];
        $log['log_fromme'] = $stuCatOne['coursecatbalance_time'];
        $log['log_playme'] = $logOne['log_playme'];
        $log['log_finaltime'] = $stuCatOne['coursecatbalance_time'] + $logOne['log_playme'];
        $log['log_time'] = time();
        $log['log_playname'] = $note;
        $log['log_reason'] = $note;
        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);
        $cat = array();
        $cat['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] + $one['pay_price'];
        $cat['coursecatbalance_time'] = $stuCatOne['coursecatbalance_time'] + $logOne['log_playme'];
        $cat['coursecatbalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursecatbalance", "student_id='{$this->payfeeorderOne['student_id']}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$logOne['coursecat_id']}' and feetype_code='{$logOne['feetype_code']}'", $cat);

    }

    //取消订单之后的账户操作
    function orderProcess($time = '')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }

        $this->school_id = $this->payfeeorderOne['school_id'];

        $stublcOne = $this->getStuBalance($this->payfeeorderOne['student_id'],$this->company_id,$this->school_id,$this->payfeeorderOne['companies_id']);

        $balance = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='balance' and pay_issuccess=1 and pay_isrefund=0");

        $forward = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='forward' and pay_issuccess=1 and pay_isrefund=0");

        $norebalance = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='norebalance' and pay_issuccess=1 and pay_isrefund=0");

        $times = $this->DataControl->selectOne("select pay_price,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='cattimes' and pay_issuccess=1 and pay_isrefund=0");

        $deposit = $this->DataControl->selectOne("select pay_price,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='catdeposit' and pay_issuccess=1 and pay_isrefund=0");

        $sales = $this->DataControl->selectOne("select pay_price,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='catsales' and pay_issuccess=1 and pay_isrefund=0");

        $manage = $this->DataControl->selectOne("select pay_price,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='catmanage' and pay_issuccess=1 and pay_isrefund=0");

        $bus = $this->DataControl->selectOne("select pay_price,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='catbus' and pay_issuccess=1 and pay_isrefund=0");

        $food = $this->DataControl->selectOne("select pay_price,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code='catfood' and pay_issuccess=1 and pay_isrefund=0");

        if ($balance) {
            if ($balance['pay_price'] > 0) {
                $balancelog_data = array();
                $balancelog_data['company_id'] = $this->company_id;
                $balancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                $balancelog_data['school_id'] = $this->school_id;
                $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                $balancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
                $balancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $balancelog_data['balancelog_class'] = 0;
                $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('使用的账户余额转账户余额');
                $balancelog_data['balancelog_playclass'] = '+';
                $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
                $balancelog_data['balancelog_playamount'] = $balance['pay_price'];
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $balance['pay_price'];
                $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('使用的账户余额转账户余额');
                $balancelog_data['balancelog_time'] = $time;
                $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
                //结算账户余额
                $data = array();
                $data['student_balance'] = $stublcOne['student_balance'] + ($balance['pay_price']);
                $this->DataControl->updateData("smc_student_balance", "student_id='{$this->payfeeorderOne['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$this->payfeeorderOne['companies_id']}'", $data);
            }

        }

        if ($norebalance) {
            if ($norebalance['pay_price'] > 0) {
                $balancelog_data = array();
                $balancelog_data['company_id'] = $this->company_id;
                $balancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                $balancelog_data['school_id'] = $this->school_id;
                $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                $balancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
                $balancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $balancelog_data['balancelog_class'] = 2;
                $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('使用的不可退账户余额转账户不可退余额');
                $balancelog_data['balancelog_playclass'] = '+';
                $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
                $balancelog_data['balancelog_playamount'] = $norebalance['pay_price'];
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $norebalance['pay_price'];
                $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('使用的不可退账户余额转账户不可退余额');
                $balancelog_data['balancelog_time'] = $time;
                $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

                $data = array();
                $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'] + ($norebalance['pay_price']);
                $this->DataControl->updateData("smc_student_balance", "student_id='{$this->payfeeorderOne['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$this->payfeeorderOne['companies_id']}'", $data);
            }


        }

        if ($deposit) {
            $logOne = $this->DataControl->getOne("smc_student_coursecatbalance_log", "pay_pid='{$deposit['pay_pid']}'");
            if ($logOne) {
                $this->reAdvance($logOne, $deposit, $this->LgStringSwitch('定金预收'));
            }
        }

        if ($sales) {
            $logOne = $this->DataControl->getOne("smc_student_coursecatbalance_log", "pay_pid='{$sales['pay_pid']}'");
            if ($logOne) {
                $this->reAdvance($logOne, $sales, $this->LgStringSwitch('教材预收'));
            }
        }

        if ($times) {
            $logOne = $this->DataControl->getOne("smc_student_coursecatbalance_log", "pay_pid='{$times['pay_pid']}'");
            if ($logOne) {
                $this->reAdvance($logOne, $times, $this->LgStringSwitch('课次预收'));
            }
        }

        if ($manage) {
            $logOne = $this->DataControl->getOne("smc_student_coursecatbalance_log", "pay_pid='{$manage['pay_pid']}'");
            if ($logOne) {
                $this->reAdvance($logOne, $manage, $this->LgStringSwitch('管理费预收'));
            }
        }

        if ($bus) {
            $logOne = $this->DataControl->getOne("smc_student_coursecatbalance_log", "pay_pid='{$bus['pay_pid']}'");
            if ($logOne) {
                $this->reAdvance($logOne, $bus, $this->LgStringSwitch('校车预收'));
            }
        }

        if ($food) {
            $logOne = $this->DataControl->getOne("smc_student_coursecatbalance_log", "pay_pid='{$food['pay_pid']}'");
            if ($logOne) {
                $this->reAdvance($logOne, $food, $this->LgStringSwitch('伙食费预收'));
            }
        }

        //$this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_id", "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess=0 and (paytype_code='canceldebts' or paytype_code='feewaiver')")
        $payfee_order_pay_array = $this->DataControl->selectClear("select pay_id,pay_order_no,pay_pid from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess=0 and (paytype_code='canceldebts' or paytype_code='feewaiver' or paytype_code='qrcode') ");
        if ($payfee_order_pay_array) {
            $data = array();
            $data['pay_issuccess'] = '-1';
            $data['pay_updatatime'] = time();
            $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess=0 and (paytype_code='canceldebts' or paytype_code='feewaiver'  or paytype_code='qrcode')", $data);

            foreach ($payfee_order_pay_array as $payfee_order_pay_arrayvar) {
                if($payfee_order_pay_arrayvar['pay_order_no'] != '') {
                    //同步取消招行的订单
                    $this->cancelPayOrderzhaohang($payfee_order_pay_arrayvar['pay_pid']);
                }
            }
        }

        if ($forward) {
            if ($forward['pay_price'] > 0) {
                $balancelog = array();
                $balancelog['company_id'] = $this->company_id;
                $balancelog['school_id'] = $this->school_id;
                $balancelog['companies_id'] = $this->payfeeorderOne['companies_id'];
                $balancelog['staffer_id'] = $this->stafferOne['staffer_id'];
                $balancelog['student_id'] = $this->payfeeorderOne['student_id'];
                $balancelog['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $balancelog['balancelog_class'] = 1;
                $balancelog['balancelog_playname'] = $this->LgStringSwitch('课程结转余额转账户结转余额');
                $balancelog['balancelog_playclass'] = '+';
                $balancelog['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
                $balancelog['balancelog_playamount'] = $forward['pay_price'];
                $balancelog['balancelog_finalamount'] = $stublcOne['student_forwardprice'] + $forward['pay_price'];
                $balancelog['balancelog_reason'] = $this->LgStringSwitch('课程结转余额转账户结转余额');
                $balancelog['balancelog_time'] = $time;
                $this->DataControl->insertData("smc_student_balancelog", $balancelog);

                //结算余额变更
                $data = array();
                $data['student_forwardprice'] = $stublcOne['student_forwardprice'] + $forward['pay_price'];
                $data['student_updatatime'] = $time;
                $this->DataControl->updateData("smc_student", "student_id='{$this->payfeeorderOne['student_id']}' and company_id='{$this->company_id}'", $data);
            }
        }

        $refundList = $this->DataControl->selectClear("select pay_price,pay_pid,pay_outnumber from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and paytype_code in ('wechat','alipay','pos','cash','bankcard') and pay_issuccess=1 and pay_isrefund=0");

        $couponsList = $this->DataControl->selectClear("select coupons_pid from smc_payfee_order_coupons where order_pid='{$this->payfeeorderOne['order_pid']}'");
        if ($couponsList) {
            foreach ($couponsList as $couponsOne) {
                $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,coupons_class,coupons_pid", "student_id='{$this->payfeeorderOne['student_id']}' and coupons_pid='{$couponsOne['coupons_pid']}'");
                if ($couponsOne) {

                    $coupons_data = array();
                    if (strpos($couponsOne['coupons_pid'], 'copy')!==false){
                        $coupons_data['coupons_isuse'] = -1;
                    }else{
                        $coupons_data['coupons_isuse'] = 0;
                    }

                    $coupons_data['order_pid'] = '';
                    $coupons_data['coupons_usetime'] = '0';
                    $this->DataControl->updateData("smc_student_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $coupons_data);

                    if ($couponsOne['coupons_class'] == 1) {
                        $data = array();
                        $data['card_status'] = 1;
                        $this->DataControl->updateData("smc_activity_ticket_card", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                    }
                }
            }
        }

        if ($refundList) {
            do {
                $refund_pid = $this->createOrderPid('SC');
            } while ($this->DataControl->selectOne("select refund_id from smc_refund_order where refund_pid='{$refund_pid}' and company_id='{$this->company_id}' limit 0,1"));
            $all_price = 0;
            foreach ($refundList as $refundOne) {
                $trade_log_data = array();
                $trade_log_data['refund_pid'] = $refund_pid;
                $trade_log_data['pay_pid'] = $refundOne['pay_pid'];
                $trade_log_data['pay_outnumber'] = $refundOne['pay_outnumber'];
                $trade_log_data['trade_price'] = $refundOne['pay_price'];
                $trade_log_data['trade_note'] = $this->LgStringSwitch('取消订单');
                $trade_log_data['trade_createtime'] = $time;
                $this->DataControl->insertData("smc_refund_order_trade", $trade_log_data);
                $all_price += $refundOne['pay_price'];
            }

            $refund_data = array();
            $refund_data['trading_pid'] = $this->stuTrading($this->payfeeorderOne['student_id'], $this->payfeeorderOne['companies_id'],'Accountrefund');
            $refund_data['refund_pid'] = $refund_pid;
            $refund_data['from_order_pid'] = $this->payfeeorderOne['order_pid'];
            $refund_data['company_id'] = $this->company_id;
            $refund_data['school_id'] = $this->school_id;
            $refund_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $refund_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $refund_data['student_id'] = $this->payfeeorderOne['student_id'];
            $refund_data['refund_from'] = 1;
            $refund_data['refund_price'] = $all_price;
            $refund_data['refund_payprice'] = $all_price;
            $refund_data['refund_createtime'] = $time;
            $this->DataControl->insertData("smc_refund_order", $refund_data);


            $TracksData = array();
            $TracksData['refund_pid'] = $refund_pid;
            $TracksData['tracks_title'] = $this->LgStringSwitch('取消订单');
            $TracksData['tracks_information'] = $this->LgStringSwitch('取消订单');
            $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
            $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $TracksData['tracks_time'] = time();
            $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);
        }

        $courseList = $this->DataControl->selectClear("select * from smc_payfee_order_course where order_pid='{$this->payfeeorderOne['order_pid']}' ");
        if ($courseList) {
            $today = date("Y-m-d", $time);
            foreach ($courseList as $courseOne) {
                $study = $this->DataControl->selectOne("select c.class_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id='{$this->payfeeorderOne['student_id']}' and c.course_id='{$courseOne['course_id']}' and ss.study_isreading='1' and ss.school_id='{$this->school_id}' and ss.company_id='{$this->company_id}'");

                $tradeOne = $this->DataControl->getFieldOne("smc_student_trading", "tradingtype_code", "trading_pid='{$this->payfeeorderOne['trading_pid']}' and company_id='{$this->company_id}'");

                if ($study && $tradeOne['tradingtype_code'] != 'CourseMakeUp') {

                    $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
                    $TransactionModel->outClass($this->payfeeorderOne['student_id'], $study['class_id'], 4, $time, $this->payfeeorderOne['trading_pid']);

//                    $data = array();
//                    $data['study_isreading'] = '-1';
//                    $data['study_endday'] = date("Y-m-d", $time);
//                    if ($this->DataControl->updateData("smc_student_study", "student_id='{$this->payfeeorderOne['student_id']}' and class_id='{$study['class_id']}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'", $data)) {
//
//                        $studyOne = $this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id='{$this->payfeeorderOne['student_id']}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and study_isreading='1'");
//                        if (!$studyOne) {
//                            $enrolled_data = array();
//                            $enrolled_data['enrolled_status'] = 0;
//                            $enrolled_data['enrolled_updatatime'] = $time;
//                            $this->DataControl->updateData("smc_student_enrolled", "student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}'", $enrolled_data);
//                        }
//
//                        $like = date("Ymd", $time);
//                        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
//                        $data = array();
//                        $data['company_id'] = $this->company_id;
//                        $data['student_id'] = $this->payfeeorderOne['student_id'];
//                        if ($changeInfo) {
//                            $data['change_pid'] = $changeInfo['change_pid'] + 1;
//                        } else {
//                            $data['change_pid'] = $like . '000001';
//                        }
//                        $data['from_stuchange_code'] = 'B01';
//                        $data['from_school_id'] = $this->school_id;
//                        $data['from_class_id'] = $study['class_id'];
//                        $data['change_status'] = 1;
//                        $data['change_day'] = date("Y-m-d", $time);
//                        $data['change_reason'] = $this->LgStringSwitch('取消订单离开班级');
//                        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
//                        $data['change_createtime'] = $time;
//                        $this->DataControl->insertData("smc_student_change", $data);
//
//                        $log_data = array();
//                        $log_data['change_pid'] = $data['change_pid'];
//                        $log_data['company_id'] = $this->company_id;
//                        $log_data['student_id'] = $this->payfeeorderOne['student_id'];
//                        $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
//                        $log_data['changelog_type'] = 0;
//                        $log_data['stuchange_code'] = 'B01';
//                        $log_data['trading_pid'] = $this->payfeeorderOne[''];
//                        $log_data['school_id'] = $this->school_id;
//                        $log_data['class_id'] = $study['class_id'];
//                        $log_data['changelog_note'] = $this->LgStringSwitch('取消订单离开班级');
//                        $log_data['changelog_day'] = date("Y-m-d", $time);
//                        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];;
//                        $log_data['changelog_createtime'] = $time;
//                        $this->DataControl->insertData("smc_student_changelog", $log_data);
//                    }
                }

                $course_sql = "select scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price,s.student_branch
                              from smc_student_coursebalance as scb
                              left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scf.student_id=scb.student_id
                              left join smc_student as s on s.student_id=scb.student_id
                              where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.course_id='{$courseOne['course_id']}' and scb.school_id='{$this->school_id}'
                              ";
                $studentOne = $this->DataControl->selectOne($course_sql);

                $random = $this->create_guid();

                if ($studentOne) {
                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
                    $coursebalancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $coursebalancelog_data['log_class'] = 0;
                    $coursebalancelog_data['course_id'] = $courseOne['course_id'];
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    if ($study) {
                        $coursebalancelog_data['class_id'] = $study['class_id'];
                    }
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程余额结转');
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['log_playclass'] = '-';
                    $coursebalancelog_data['log_fromamount'] = $studentOne['coursebalance_figure'];

                    if (($studentOne['coursebalance_figure'] - $courseOne['ordercourse_totalprice']) > 0) {
                        $coursebalancelog_data['log_playamount'] = $courseOne['ordercourse_totalprice'];
                        $coursebalancelog_data['log_finalamount'] = $studentOne['coursebalance_figure'] - $courseOne['ordercourse_totalprice'];
                    } else {
                        $coursebalancelog_data['log_playamount'] = $studentOne['coursebalance_figure'];
                        $coursebalancelog_data['log_finalamount'] = 0;
                    }

                    $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];

                    if (($studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums']) > 0) {
                        $coursebalancelog_data['log_playtimes'] = $courseOne['ordercourse_buynums'];
                        $coursebalancelog_data['log_finaltimes'] = $studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums'];
                    } else {
                        $coursebalancelog_data['log_playtimes'] = $studentOne['coursebalance_time'];
                        $coursebalancelog_data['log_finaltimes'] = 0;
                    }

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('取消订单');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

                    $courselog_data = array();
                    $courselog_data['student_id'] = $this->payfeeorderOne['student_id'];
                    $courselog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $courselog_data['school_id'] = $this->school_id;
                    if ($study) {
                        $courselog_data['class_id'] = $study['class_id'];
                    }

                    $sql = "select oc.ordercoupons_price,sc.coupons_type
                          from smc_payfee_order_coupons as oc
                          left join smc_student_coupons as sc on sc.coupons_pid=oc.coupons_pid
                          where oc.order_pid='{$this->payfeeorderOne['order_pid']}' and oc.course_id='{$courseOne['course_id']}'
                          and sc.coupons_type='0'";

                    $couList = $this->DataControl->selectClear($sql);
                    $all_coupons_price = 0;
                    if ($couList) {
                        foreach ($couList as $value) {
                            $all_coupons_price += $value['ordercoupons_price'];
                        }
                    }

                    if ($studentOne['courseforward_price'] > 0 && $all_coupons_price > 0) {
                        $courselog_data['log_class'] = 1;
                        $courselog_data['course_id'] = $courseOne['course_id'];
                        $courselog_data['school_id'] = $this->school_id;
                        $courselog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                        $courselog_data['log_playname'] = $this->LgStringSwitch('取消订单');
                        $courselog_data['log_playclass'] = '-';
                        $courselog_data['log_random'] = $random;
                        $courselog_data['log_fromamount'] = $studentOne['courseforward_price'];
                        if ($studentOne['courseforward_price'] > $all_coupons_price) {
                            $courselog_data['log_playamount'] = $all_coupons_price;
                            $courselog_data['log_finalamount'] = $studentOne['courseforward_price'] - $all_coupons_price;
                        } else {
                            $courselog_data['log_playamount'] = $studentOne['courseforward_price'];
                            $courselog_data['log_finalamount'] = 0;
                        }

                        $courselog_data['log_fromtimes'] = $studentOne['coursebalance_time'];

                        if (($studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums']) > 0) {
                            $courselog_data['log_playtimes'] = $courseOne['ordercourse_buynums'];
                            $courselog_data['log_finaltimes'] = $studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums'];
                        } else {
                            $courselog_data['log_playtimes'] = $studentOne['coursebalance_time'];
                            $courselog_data['log_finaltimes'] = 0;
                        }

                        $courselog_data['log_reason'] = $this->LgStringSwitch('取消订单');
                        $courselog_data['log_time'] = $time;
                        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

                        $student_courseforward_data = array();
                        $student_courseforward_data['courseforward_price'] = $courselog_data['log_finalamount'];
                        $student_courseforward_data['courseforward_updatatime'] = $time;
                        $this->DataControl->updateData("smc_student_courseforward", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$courseOne['course_id']}'", $student_courseforward_data);
                        $studentOne['courseforward_price'] = $student_courseforward_data['courseforward_price'];
                    }

                    $student_coursebalance_data = array();
                    if (($studentOne['coursebalance_figure'] - $courseOne['ordercourse_totalprice']) > 0) {
                        $student_coursebalance_data['coursebalance_figure'] = $studentOne['coursebalance_figure'] - $courseOne['ordercourse_totalprice'];
                    } else {
                        $student_coursebalance_data['coursebalance_figure'] = 0;
                    }
                    if (($studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums']) > 0) {
                        $student_coursebalance_data['coursebalance_time'] = $studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums'];
                    } else {
                        $student_coursebalance_data['coursebalance_time'] = 0;
                    }
                    if (($studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums']) > 0) {
                        $student_coursebalance_data['coursebalance_status'] = 1;
                    } else {
                        $student_coursebalance_data['coursebalance_status'] = 3;
                    }

                    $sql = "select sf.coursetimes_id from smc_student_free_coursetimes as sf
                          where sf.student_id='{$this->payfeeorderOne['student_id']}'
                          and sf.course_id='{$courseOne['course_id']}' and sf.is_use='0'";
                    $freeTimes = $this->DataControl->selectClear($sql);

                    $num = $freeTimes ? count($freeTimes) : 0;

                    if (($student_coursebalance_data['coursebalance_time'] - $num) > 0) {
                        $student_coursebalance_data['coursebalance_unitexpend'] = ceil(($student_coursebalance_data['coursebalance_figure'] + $studentOne['courseforward_price']) / ($student_coursebalance_data['coursebalance_time'] - $num));

                        $student_coursebalance_data['coursebalance_unitearning'] = ceil($student_coursebalance_data['coursebalance_figure'] / ($student_coursebalance_data['coursebalance_time'] - $num));
                    }

                    $student_coursebalance_data['coursebalance_updatatime'] = time();
                    $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$courseOne['course_id']}' and school_id='{$this->school_id}'", $student_coursebalance_data);

                    $time_data = array();
                    $time_data['student_id'] = $this->payfeeorderOne['student_id'];
                    $time_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $time_data['course_id'] = $courseOne['course_id'];
                    $time_data['school_id'] = $this->school_id;
                    $time_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    if ($study) {
                        $time_data['class_id'] = $study['class_id'];
                    }
                    $time_data['timelog_playname'] = $this->LgStringSwitch('取消订单');
                    $time_data['timelog_playclass'] = '-';
                    $time_data['log_random'] = $random;
                    $time_data['timelog_fromtimes'] = $studentOne['coursebalance_time'];

                    if (($studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums']) > 0) {
                        $time_data['timelog_playtimes'] = $courseOne['ordercourse_buynums'];
                        $time_data['timelog_finaltimes'] = $studentOne['coursebalance_time'] - $courseOne['ordercourse_buynums'];
                    } else {
                        $time_data['timelog_playtimes'] = $studentOne['coursebalance_time'];
                        $time_data['timelog_finaltimes'] = 0;
                    }

                    $time_data['timelog_reason'] = $this->LgStringSwitch('取消订单');
                    $time_data['timelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

                    $data = array();
                    $data['consumelog_nums'] = $time_data['timelog_finaltimes'];
                    $this->DataControl->updateData("smc_student_coursebalance_consumelog", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);
                }
            }
        }


        $sql = "select pay_id from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_type=2 and pay_issuccess=1 and pay_price>0 limit 0,1";
        $payItemOne = $this->DataControl->selectOne($sql);
        if ($payItemOne) {
            $sql = "select oi.*,ifnull(p.course_id,0) as course_id,cf.feeitem_id
                  from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$this->payfeeorderOne['company_id']}'
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}'
                  ";
        } else {
            $sql = "select oi.*,ifnull(p.course_id,0) as course_id,cf.feeitem_id
                  from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$this->payfeeorderOne['company_id']}'
                  where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.feeitem_expendtype<>'2'
                  ";
        }

//        $sql="select poi.*,cf.feeitem_id,ifnull(fp.course_id,0) as course_id
//                                                    from smc_payfee_order_item as poi
//                                                    left join smc_code_feeitem as cf on cf.feeitem_branch=poi.feeitem_branch and cf.company_id='{$this->company_id}'
//                                                    left join smc_fee_pricing_items as fpi on fpi.items_id=poi.items_id
//                                                    left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
//                                                    where poi.order_pid='{$this->payfeeorderOne['order_pid']}' ";

        $itemsList = $this->DataControl->selectClear($sql);
        if ($itemsList || $bus || $food) {
            foreach ($itemsList as $itemsOne) {
                if (isset($itemsOne['course_id']) && $itemsOne['course_id'] > 0) {
                    $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and feeitem_id='{$itemsOne['feeitem_id']}' and school_id='{$this->payfeeorderOne['school_id']}' order by itemtimes_createtime desc");
                } else {
                    $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and feeitem_id='{$itemsOne['feeitem_id']}' and school_id='{$this->payfeeorderOne['school_id']}' order by itemtimes_createtime desc");
                }

                if ($studentItemOne) {
                    $log_data = array();
                    $log_data['student_id'] = $this->payfeeorderOne['student_id'];
                    $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                    $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $log_data['feeitem_id'] = $itemsOne['feeitem_id'];
                    $log_data['log_playname'] = $this->LgStringSwitch('取消订单减少课程杂费');
                    $log_data['log_playclass'] = '-';
                    $log_data['log_fromamount'] = $studentItemOne['itemtimes_figure'];
                    $log_data['log_playamount'] = $itemsOne['item_totalprice'];
                    $log_data['log_finalamount'] = $studentItemOne['itemtimes_figure'] - $itemsOne['item_totalprice'];
                    $log_data['log_reason'] = $this->LgStringSwitch('取消订单减少课程杂费');
                    $log_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);
//                $items_price+=$itemsOne['itemtimes_figure'];

                    $data = array();
                    $data['itemtimes_number'] = $studentItemOne['itemtimes_number'] - $itemsOne['item_buynums'];
                    $data['itemtimes_figure'] = $studentItemOne['itemtimes_figure'] - $itemsOne['item_totalprice'];
                    $data['itemtimes_updatatime'] = $time;
                    if (isset($itemsOne['course_id']) && $itemsOne['course_id'] > 0) {
                        $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and feeitem_id='{$itemsOne['feeitem_id']}' and school_id='{$this->school_id}'", $data);
                    } else {
                        $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and feeitem_id='{$itemsOne['feeitem_id']}' and school_id='{$this->school_id}'", $data);
                    }
                }
            }
        }

        if ($this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_id", "order_pid='{$this->payfeeorderOne['order_pid']}'")) {

            $data = array();
            $data['courseshare_status'] = '-1';
            $this->DataControl->updateData("smc_student_courseshare", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);

        }

        $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$this->payfeeorderOne['order_pid']}'");

        if ($achieveList) {
            $data = array();
            $data['achieve_status'] = -1;
            $data['achieve_updatatime'] = time();

            $this->DataControl->updateData("smc_staffer_achieve", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);

            $str = '订单已取消,业绩失效';

            foreach ($achieveList as $achieveOne) {
                $data = array();
                $data['achieve_id'] = $achieveOne['achieve_id'];
                $data['tracks_title'] = $this->LgStringSwitch('取消订单');
                $data['tracks_information'] = $this->LgStringSwitch($str);
                $data['staffer_id'] = $this->stafferOne['staffer_id'];
                $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $data['tracks_time'] = time();
                $this->DataControl->insertData("smc_staffer_achieve_track", $data);
            }
        }

        $good_data = array();
        $good_data['erpgoods_isreceive'] = '-1';
        $this->DataControl->updateData("smc_student_erpgoods", "order_pid='{$this->payfeeorderOne['order_pid']}'", $good_data);

        $track_data = array();

        $track_data['trading_status'] = '-1';
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}' and company_id='{$this->company_id}'", $track_data);


    }

    //生成缴费订单课程明细
    function orderCourse($agreement_id, $pricing_id, $course_id, $price, $num = 1, $class_id = 0, $starttime, $discount_id = 0, $market_price = 0, $unitrefund, $monthArray = array(), $sellgoods_id = 0)
    {

        if ($monthArray) {
            foreach ($monthArray as $monthOne) {
                $data = array();
                $data['order_pid'] = $this->payfeeorderOne['order_pid'];
                $data['class_id'] = $class_id;
                $data['course_id'] = $course_id;
                $data['ordershare_month'] = $monthOne['month'];
                $data['ordershare_price'] = $monthOne['price'];
                $this->DataControl->insertData("smc_payfee_order_share", $data);
            }
        }

        if ($starttime == '') {
            $starttime = date("Y-m-d", time());
        }
        $orderCourseData = array();
        $orderCourseData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderCourseData['agreement_id'] = $agreement_id;
        $orderCourseData['pricing_id'] = $pricing_id;
        $orderCourseData['course_id'] = $course_id;
        $orderCourseData['class_id'] = $class_id;
        $orderCourseData['discount_id'] = $discount_id;
        $orderCourseData['ordercourse_market_price'] = $market_price;
        $orderCourseData['ordercourse_enterclassdate'] = $starttime;
        $orderCourseData['ordercourse_buynums'] = $num;
        if ($num <= 0) {
            $orderCourseData['ordercourse_unitprice'] = 0;
        } else {
            $orderCourseData['ordercourse_unitprice'] = ceil($price / $num);
        }

        $orderCourseData['ordercourse_unitrefund'] = $unitrefund;
        $orderCourseData['ordercourse_totalprice'] = $price;
        $orderCourseData['sellgoods_id'] = $sellgoods_id;
        if ($this->DataControl->insertData('smc_payfee_order_course', $orderCourseData)) {
            return true;
        } else {
            return false;
        }
    }

    //生成缴费订单商品明细
    function orderGoods($agreement_id, $pricing_id, $goods_id, $sellingprice, $num = 1, $sellgoods_id = 0)
    {
        $orderGoodsData = array();
        $orderGoodsData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderGoodsData['agreement_id'] = $agreement_id;
        $orderGoodsData['pricing_id'] = $pricing_id;
        $orderGoodsData['goods_id'] = $goods_id;
        $orderGoodsData['ordergoods_buynums'] = $num;
        $orderGoodsData['ordergoods_unitprice'] = $sellingprice / $num;
//        $orderGoodsData['ordergoods_unitprice'] = $sellingprice;
        $orderGoodsData['ordergoods_totalprice'] = ceil($sellingprice);
        $orderGoodsData['sellgoods_id'] = $sellgoods_id;
        if ($this->DataControl->insertData('smc_payfee_order_goods', $orderGoodsData)) {
            return true;
        } else {
            return false;
        }
    }

    //生成优惠活动触发明细
    function marketabate($discount_id, $activity_id, $marketabate_price)
    {
        $activityOne = $this->DataControl->getFieldOne("smc_market_activity", "activity_deductionmethod", "activity_id='{$activity_id}'");
        $data = array();
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['discount_id'] = $discount_id;
        $data['activity_id'] = $activity_id;
        $data['marketabate_price'] = $marketabate_price;
        $data['marketabate_deductionmethod'] = $activityOne['activity_deductionmethod'];
        if ($this->DataControl->insertData('smc_market_activity', $data)) {
            return true;
        } else {
            return false;
        }
    }

    function createFreeOrder($student_id, $course_id, $course_branch, $class_id, $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $companiesOne=$this->getSchoolCourseCompanies($this->school_id,0,$course_id);

        do {
            $order_pid = $this->createOrderPid('CF');
        } while ($this->DataControl->selectOne("select order_id from smc_freehour_order where order_pid='{$order_pid}' and company_id='{$this->company_id}' limit 0,1"));

        $order_data = array();
        $order_data['trading_pid'] = $this->stuTrading($student_id, $companiesOne['companies_id'],'ClassGiving', $time);
        $order_data['order_pid'] = $order_pid;
        $order_data['company_id'] = $this->company_id;
        $order_data['school_id'] = $this->school_id;
        $order_data['class_id'] = $class_id;
        $order_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $order_data['student_id'] = $student_id;
        $order_data['order_isneedaudit'] = 0;
        $order_data['order_status'] = 1;
        $order_data['course_id'] = $course_id;
        $order_data['course_branch'] = $course_branch;
        $order_data['order_createtime'] = $time;
        $this->DataControl->insertData("smc_freehour_order", $order_data);


        $TracksData = array();
        $TracksData['refund_pid'] = $order_pid;
        $TracksData['tracks_title'] = $this->LgStringSwitch('拆班生成免审课程赠送订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('拆班生成免审课程赠送订单');
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_freehour_order_tracks", $TracksData);

        return $order_pid;
    }

    function createFreeHourOrder($student_id, $course_id, $course_branch, $class_id, $time = '', $has = 0, $order_img, $reason = '')
    {
        if ($time == '') {
            $time = time();
        }

        $companiesOne=$this->getSchoolCourseCompanies($this->school_id,0,$course_id);

        do {
            $order_pid = $this->createOrderPid('CF');
        } while ($this->DataControl->selectOne("select order_id from smc_freehour_order where order_pid='{$order_pid}' and company_id='{$this->company_id}' limit 0,1"));

        $order_data = array();
        $order_data['trading_pid'] = $this->stuTrading($student_id,$companiesOne['companies_id'], 'ClassGiving', $time);
        $order_data['order_pid'] = $order_pid;
        $order_data['company_id'] = $this->company_id;
        $order_data['school_id'] = $this->school_id;
        $order_data['class_id'] = $class_id;
        $order_data['order_img'] = $order_img;
        $order_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $order_data['student_id'] = $student_id;
        $order_data['order_isneedaudit'] = 1;
        $order_data['order_status'] = 0;
        $order_data['order_hastimes'] = $has;
        $order_data['course_id'] = $course_id;
        $order_data['order_refusereason'] = $reason;
        $order_data['course_branch'] = $course_branch;
        $order_data['order_createtime'] = $time;
        $this->DataControl->insertData("smc_freehour_order", $order_data);

        $TracksData = array();
        $TracksData['order_pid'] = $order_pid;
        $TracksData['tracks_title'] = $this->LgStringSwitch('创建课程赠送订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('订单提交成功，等待审核');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_freehour_order_tracks", $TracksData);

        return $order_pid;
    }

    function receiveGoods($goods_id, $student_id, $num, $is_receive = 0, $out_pid, $isfree = 0, $time = '')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }
        $repertoryOne = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "company_id='{$this->company_id}' and school_id='{$this->school_id}' and goods_id='{$goods_id}'");
        $goods_data = array();
        if ($is_receive == '1') {
            $order_goods_data = array();
            $order_goods_data['beoutorder_pid'] = $out_pid;
            $order_goods_data['goods_id'] = $goods_id;
            $order_goods_data['beoutorder_buynums'] = $num;

            $log_data = array();
            $log_data['company_id'] = $this->company_id;
            $log_data['school_id'] = $this->school_id;
            $log_data['goods_id'] = $goods_id;
            $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $log_data['changelog_class'] = '1';
            $log_data['changelog_playname'] = $this->LgStringSwitch('商品领用');
            $log_data['changelog_playclass'] = '-';
            $log_data['changelog_fromnums'] = $repertoryOne['goods_repertory'];
            $log_data['changelog_playnums'] = $num;
            $log_data['changelog_finalnums'] = $repertoryOne['goods_repertory'] - $num;
            $log_data['changelog_reason'] = $this->LgStringSwitch('商品领用');
            $log_data['changelog_createtime'] = $time;
            $this->DataControl->insertData("smc_erp_goods_changelog", $log_data);

            $repertory_data = array();
            $repertory_data['goods_repertory'] = $repertoryOne['goods_repertory'] - $num;
            $this->DataControl->updateData("smc_erp_goods_repertory", "company_id='{$this->company_id}' and school_id='{$this->school_id}' and goods_id='{$goods_id}'", $repertory_data);

            $goods_data['erpgoods_receivetime'] = $time;
            $goods_data['beoutorder_pid'] = $out_pid;
        }

        $goodsOne = $this->DataControl->getFieldOne("erp_proorder_goods", "proogoods_id", "goods_id='{$goods_id}'");
        $goods_data['student_id'] = $student_id;
        $goods_data['school_id'] = $this->school_id;
        $goods_data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $goods_data['ordergoods_id'] = $goodsOne['proogoods_id'];
        $goods_data['goods_id'] = $goods_id;
        $goods_data['erpgoods_isfree'] = $isfree;
        $goods_data['erpgoods_isreceive'] = $is_receive;
        $goods_data['erpgoods_createtime'] = $time;
        $this->DataControl->insertData("smc_student_erpgoods", $goods_data);
        if ($is_receive == '1') {
            $orderCourse = $this->DataControl->getFieldOne("smc_payfee_order_goods", "ordergoods_buynums,ordergoods_unitprice,ordergoods_totalprice", "order_pid='{$this->payfeeorderOne['order_pid']}' and goods_id='{$goods_id}'");
            $data = array();
            do {
                $trading_pid = $this->createOrderPid('JC');
            } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

            $data['trading_pid'] = $trading_pid;
            $data['company_id'] = $this->company_id;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $data['student_id'] = $student_id;
            $data['tradingtype_code'] = 'Subscribed';
            $data['trading_status'] = "1";
            $data['trading_createtime'] = $time;
            $data['staffer_id'] = $this->stafferOne['staffer_id'];
            $this->DataControl->insertData("smc_student_trading", $data);

            $in_data = array();
            $in_data['company_id'] = $this->company_id;
            $in_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $in_data['school_id'] = $this->school_id;
            $in_data['income_type'] = '2';
            $in_data['student_id'] = $student_id;
            $in_data['trading_pid'] = $trading_pid;
            $in_data['income_price'] = $orderCourse['ordergoods_totalprice'];
            $in_data['income_note'] = $this->LgStringSwitch('教材领用收入');
            $in_data['income_confirmtime'] = $time;
            $in_data['income_audittime'] = $time;
            $in_data['income_createtime'] = time();
            $this->DataControl->insertData("smc_school_income", $in_data);
        }
    }

    //生成出库单
    function beoutorder($beoutorder_from)
    {
        do {
            $out_pid = $this->createOutPid();
        } while ($this->DataControl->selectOne("select beoutorder_id from smc_erp_beoutorder where beoutorder_pid='{$out_pid}' limit 0,1"));
        $out_data = array();
        $out_data['beoutorder_pid'] = $out_pid;
        $out_data['company_id'] = $this->company_id;
        $out_data['school_id'] = $this->school_id;
        $out_data['beoutorder_from'] = $beoutorder_from;
        $out_data['beoutorder_status'] = '2';
        $out_data['beoutorder_storagetime'] = time();
        $out_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $out_data['beoutorder_createtime'] = time();

        $this->DataControl->insertData("smc_erp_beoutorder", $out_data);

        return $out_pid;
    }


    //生成普通课程杂费订单明细
    function orderCourseOrdinaryGoods($goods_id, $sellingprice, $num = 1, $couponsPrice = 0, $sellgoods_id = 0)
    {
        $orderGoodsData = array();
        $orderGoodsData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderGoodsData['goods_id'] = $goods_id;
        $orderGoodsData['ordergoods_buynums'] = $num;
        $orderGoodsData['sellgoods_id'] = $sellgoods_id;

        if ($couponsPrice > 0) {
            $orderGoodsData['ordergoods_unitprice'] = ceil($sellingprice - ($couponsPrice / $num));
            $orderGoodsData['ordergoods_totalprice'] = ceil($num * $sellingprice - $couponsPrice);
        } else {
            $orderGoodsData['ordergoods_unitprice'] = $sellingprice;
            $orderGoodsData['ordergoods_totalprice'] = ceil($num * $sellingprice);
        }

        if ($this->DataControl->insertData('smc_payfee_order_goods', $orderGoodsData)) {
            return true;
        } else {
            return false;
        }
    }

    //生成普通杂费订单明细
    function orderOrdinaryGoods($student_id, $unitprice, $num = 1, $feeitem_branch, $isconsume = 1, $time = '', $sellgoods_id = 0)
    {
        if ($time == '') {
            $time = time();
        }

        $feeitemOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_id,feeitem_expendtype,feeitem_price", "feeitem_branch='{$feeitem_branch}' and company_id='{$this->company_id}'");
        if ($unitprice <= 0) {
            $unitprice = $feeitemOne['feeitem_price'];
        }

        $orderGoodsData = array();
        $orderGoodsData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderGoodsData['feeitem_branch'] = $feeitem_branch;
        $orderGoodsData['item_buynums'] = $num;
        $orderGoodsData['item_unitprice'] = $unitprice;
        $orderGoodsData['item_totalprice'] = ceil($num * $unitprice);
        $orderGoodsData['sellgoods_id'] = $sellgoods_id;
        $item_id = $this->DataControl->insertData('smc_payfee_order_item', $orderGoodsData);

        if ($feeitemOne['feeitem_expendtype'] != '2' || ($feeitemOne['feeitem_expendtype'] == '2' && $isconsume == 1)) {
            if ($item_id) {
                $data = array();
                $data['student_id'] = $student_id;
                $data['school_id'] = $this->school_id;
                $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                $data['company_id'] = $this->company_id;
                $data['feeitem_id'] = $feeitemOne['feeitem_id'];
                $data['itemtimes_number'] = $num;
                $data['itemtimes_figure'] = ceil($num * $unitprice);
                $data['itemtimes_createtime'] = $time;
                $id = $this->DataControl->insertData("smc_student_itemtimes", $data);

                $log_data = array();
                $log_data['itemtimes_id'] = $id;
                $log_data['student_id'] = $student_id;
                $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $log_data['log_playname'] = $this->LgStringSwitch('普通杂费');
                $log_data['log_playclass'] = '+';
                $log_data['log_fromamount'] = '0';
                $log_data['log_playamount'] = ceil($num * $unitprice);
                $log_data['log_finalamount'] = ceil($num * $unitprice);
                $log_data['log_reason'] = $this->LgStringSwitch('普通杂费');
                $log_data['log_time'] = $time;
                $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);

                $data = array();
                $data['itemtimes_number'] = $num;
                $data['itemtimes_figure'] = ceil($num * $unitprice);
                $data['itemtimes_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$student_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' and itemtimes_id='{$id}'", $data);

                if ($feeitemOne['feeitem_expendtype'] == '2') {
                    $log_data = array();
                    $log_data['itemtimes_id'] = $id;
                    $log_data['student_id'] = $student_id;
                    $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                    $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $log_data['log_playname'] = $this->LgStringSwitch('普通杂费一次性消耗');
                    $log_data['log_playclass'] = '-';
                    $log_data['log_fromamount'] = ceil($num * $unitprice);
                    $log_data['log_playamount'] = ceil($num * $unitprice);
                    $log_data['log_finalamount'] = '0';
                    $log_data['log_reason'] = $this->LgStringSwitch('普通杂费一次性消耗');
                    $log_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);


                    $in_data = array();
                    $in_data['company_id'] = $this->company_id;
                    $in_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $in_data['school_id'] = $this->school_id;
                    $in_data['income_type'] = '3';
                    $in_data['student_id'] = $student_id;
                    $in_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $in_data['income_price'] = ceil($num * $unitprice);
                    $in_data['income_note'] = $this->LgStringSwitch('普通杂费一次性消耗收入');
                    $in_data['income_confirmtime'] = $time;
                    $in_data['income_audittime'] = $time;
                    $in_data['income_createtime'] = time();
                    $this->DataControl->insertData("smc_school_income", $in_data);

                    $data = array();
                    $data['itemtimes_number'] = 0;
                    $data['itemtimes_figure'] = 0;
                    $data['itemtimes_updatatime'] = $time;
                    $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$student_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' and itemtimes_id='{$id}'", $data);
                }
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    //生成课程杂费订单明细
    function orderCourseGoods($student_id, $course_id, $items_id, $feeitem_branch, $buypiece, $buynums = 1, $unitprice, $isconsume = 1, $time = '', $sellgoods_id = 0)
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }
        if ($buynums < 1) {
            $buynums = 1;
        }

        $feeitemOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_id,feeitem_expendtype", "feeitem_branch='{$feeitem_branch}' and company_id='{$this->company_id}'");


        $orderCourseData = array();
        $orderCourseData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderCourseData['feeitem_branch'] = $feeitem_branch;
        $orderCourseData['items_id'] = $items_id;
        $orderCourseData['item_buynums'] = $buynums * $buypiece;
        $orderCourseData['item_unitprice'] = $unitprice;
        $orderCourseData['sellgoods_id'] = $sellgoods_id;
        $one = $this->DataControl->getFieldOne("smc_fee_pricing_items", "items_sellingprice,items_unitprice,items_buypiece", "items_id='{$items_id}'");
        if ($unitprice == 0) {
            $orderCourseData['item_totalprice'] = 0;
        } else {
            if ($one['items_buypiece'] == $buypiece) {

                $orderCourseData['item_totalprice'] = $one['items_sellingprice'] * $buynums;
            } else {

                $orderCourseData['item_totalprice'] = ceil($buynums * $buypiece * $unitprice);
            }
        }

        $item_id = $this->DataControl->insertData('smc_payfee_order_item', $orderCourseData);

        if ($item_id) {
            if ($feeitemOne['feeitem_expendtype'] != '2' || ($feeitemOne['feeitem_expendtype'] == '2' && $isconsume == 1)) {
                $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");

                if ($studentItemOne) {
                    $log_data = array();
                    $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                    $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $log_data['student_id'] = $student_id;
                    $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                    $log_data['log_playname'] = $this->LgStringSwitch('课程杂费');
                    $log_data['log_playclass'] = '+';
                    $log_data['log_fromamount'] = $studentItemOne['itemtimes_figure'];
                    $log_data['log_playamount'] = $orderCourseData['item_totalprice'];
                    $log_data['log_finalamount'] = $studentItemOne['itemtimes_figure'] + $orderCourseData['item_totalprice'];
                    $log_data['log_reason'] = $this->LgStringSwitch('课程杂费');
                    $log_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);

                    $data = array();
                    $data['itemtimes_number'] = $studentItemOne['itemtimes_number'] + $buypiece * $buynums;
                    $data['itemtimes_figure'] = $studentItemOne['itemtimes_figure'] + $orderCourseData['item_totalprice'];
                    $data['itemtimes_createtime'] = $time;
                    $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}'", $data);

                    $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                } else {
                    $data = array();
                    $data['student_id'] = $student_id;
                    $data['course_id'] = $course_id;
                    $data['school_id'] = $this->school_id;
                    $data['company_id'] = $this->company_id;
                    $data['course_id'] = $course_id;
                    $data['feeitem_id'] = $feeitemOne['feeitem_id'];
                    $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $data['itemtimes_number'] = $buypiece * $buynums;
                    $data['itemtimes_figure'] = $orderCourseData['item_totalprice'];
                    $data['itemtimes_createtime'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes", $data);

                    $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");

                    $log_data = array();
                    $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                    $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $log_data['student_id'] = $student_id;
                    $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                    $log_data['log_playname'] = $this->LgStringSwitch('课程杂费');
                    $log_data['log_playclass'] = '+';
                    $log_data['log_fromamount'] = 0;
                    $log_data['log_playamount'] = $orderCourseData['item_totalprice'];
                    $log_data['log_finalamount'] = $orderCourseData['item_totalprice'];
                    $log_data['log_reason'] = $this->LgStringSwitch('课程杂费');
                    $log_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);
                }

                if ($feeitemOne['feeitem_expendtype'] == '2') {
                    $log_data = array();
                    $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                    $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $log_data['student_id'] = $student_id;
                    $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                    $log_data['log_playname'] = $this->LgStringSwitch('课程杂费一次性消耗');
                    $log_data['log_playclass'] = '-';
                    $log_data['log_fromamount'] = $studentItemOne['itemtimes_figure'];
                    $log_data['log_playamount'] = $orderCourseData['item_totalprice'];
                    $log_data['log_finalamount'] = ($studentItemOne['itemtimes_figure'] - $orderCourseData['item_totalprice']) > 0 ? ($studentItemOne['itemtimes_figure'] - $orderCourseData['item_totalprice']) : 0;
                    $log_data['log_reason'] = $this->LgStringSwitch('课程杂费一次性消耗');
                    $log_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);

                    $in_data = array();
                    $in_data['company_id'] = $this->company_id;
                    $in_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $in_data['school_id'] = $this->school_id;
                    $in_data['income_type'] = '3';
                    $in_data['student_id'] = $student_id;
                    $in_data['course_id'] = $course_id;
                    $in_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $in_data['income_price'] = $orderCourseData['item_totalprice'];
                    $in_data['income_note'] = $this->LgStringSwitch('课程杂费一次性消耗');
                    $in_data['income_confirmtime'] = $time;
                    $in_data['income_audittime'] = $time;
                    $in_data['income_createtime'] = time();
                    $this->DataControl->insertData("smc_school_income", $in_data);

                    $data = array();
                    $data['itemtimes_number'] = ($studentItemOne['itemtimes_number'] - $buypiece * $buynums) > 0 ? $studentItemOne['itemtimes_number'] - $buypiece * $buynums : 0;
                    $data['itemtimes_figure'] = ($studentItemOne['itemtimes_figure'] - $orderCourseData['item_totalprice']) > 0 ? ($studentItemOne['itemtimes_figure'] - $orderCourseData['item_totalprice']) : 0;
                    $data['itemtimes_updatatime'] = $time;
                    $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}'", $data);
                }
            }

            return true;
        } else {
            return false;
        }
    }

    function stuTimesExpend()
    {
        $time = time();
        $sql = "select poi.*,fp.course_id,cf.feeitem_id
              from smc_payfee_order_item as poi
              left join smc_code_feeitem as cf on cf.feeitem_branch=poi.feeitem_branch and cf.company_id='{$this->company_id}'
              left join smc_fee_pricing_items as pi on pi.items_id=poi.items_id
              left join smc_fee_pricing as fp on fp.pricing_id=pi.pricing_id
              where poi.order_pid='{$this->payfeeorderOne['order_pid']}'";

        $itemsList = $this->DataControl->selectClear($sql);

        if ($itemsList) {
            foreach ($itemsList as $itemsOne) {
                $feeitemOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_id,feeitem_expendtype", "feeitem_branch='{$itemsOne['feeitem_branch']}' and company_id='{$this->company_id}'");

                if ($feeitemOne['feeitem_expendtype'] == '2') {
                    if (isset($itemsOne['course_id']) && $itemsOne['course_id'] > 0) {
                        $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                    } else {
                        $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                    }

                    if ($studentItemOne) {
                        $log_data = array();
                        $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                        $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                        $log_data['student_id'] = $this->payfeeorderOne['student_id'];
                        $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                        $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                        $log_data['log_playname'] = $this->LgStringSwitch('课程杂费');
                        $log_data['log_playclass'] = '+';
                        $log_data['log_fromamount'] = $studentItemOne['itemtimes_figure'];
                        $log_data['log_playamount'] = $itemsOne['item_totalprice'];
                        $log_data['log_finalamount'] = $studentItemOne['itemtimes_figure'] + $itemsOne['item_totalprice'];
                        $log_data['log_reason'] = $this->LgStringSwitch('课程杂费');
                        $log_data['log_time'] = $time;
                        $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);

                        $data = array();
                        $data['itemtimes_number'] = $studentItemOne['itemtimes_number'] + $itemsOne['item_buynums'];
                        $data['itemtimes_figure'] = $studentItemOne['itemtimes_figure'] + $itemsOne['item_totalprice'];
                        $data['itemtimes_createtime'] = $time;
                        if (isset($itemsOne['course_id']) && $itemsOne['course_id'] > 0) {
                            $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}'", $data);
                            $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                        } else {
                            $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}'", $data);
                            $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                        }
                    } else {
                        $data = array();
                        $data['student_id'] = $this->payfeeorderOne['student_id'];
                        $data['course_id'] = $itemsOne['course_id'];
                        $data['school_id'] = $this->school_id;
                        $data['company_id'] = $this->company_id;
                        $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                        $data['feeitem_id'] = $feeitemOne['feeitem_id'];
                        $data['itemtimes_number'] = $itemsOne['item_buynums'];
                        $data['itemtimes_figure'] = $itemsOne['item_totalprice'];
                        $data['itemtimes_createtime'] = $time;
                        $this->DataControl->insertData("smc_student_itemtimes", $data);

                        if (isset($itemsOne['course_id']) && $itemsOne['course_id'] > 0) {
                            $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                        } else {
                            $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}' order by itemtimes_createtime desc");
                        }

                        $log_data = array();
                        $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                        $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                        $log_data['student_id'] = $this->payfeeorderOne['student_id'];
                        $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                        $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                        $log_data['log_playname'] = $this->LgStringSwitch('课程杂费');
                        $log_data['log_playclass'] = '+';
                        $log_data['log_fromamount'] = 0;
                        $log_data['log_playamount'] = $itemsOne['item_totalprice'];
                        $log_data['log_finalamount'] = $itemsOne['item_totalprice'];
                        $log_data['log_reason'] = $this->LgStringSwitch('课程杂费');
                        $log_data['log_time'] = $time;
                        $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);
                    }

                    $log_data = array();
                    $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
                    $log_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $log_data['student_id'] = $this->payfeeorderOne['student_id'];
                    $log_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $log_data['feeitem_id'] = $feeitemOne['feeitem_id'];
                    $log_data['log_playname'] = $this->LgStringSwitch('课程杂费一次性消耗');
                    $log_data['log_playclass'] = '-';
                    $log_data['log_fromamount'] = $studentItemOne['itemtimes_figure'];
                    $log_data['log_playamount'] = $itemsOne['item_totalprice'];
                    $log_data['log_finalamount'] = ($studentItemOne['itemtimes_figure'] - $itemsOne['item_totalprice']) > 0 ? ($studentItemOne['itemtimes_figure'] - $itemsOne['item_totalprice']) : 0;
                    $log_data['log_reason'] = $this->LgStringSwitch('课程杂费一次性消耗');
                    $log_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);

                    $in_data = array();
                    $in_data['company_id'] = $this->company_id;
                    $in_data['companies_id'] = $this->payfeeorderOne['companies_id'];
                    $in_data['school_id'] = $this->school_id;
                    $in_data['income_type'] = '3';
                    $in_data['student_id'] = $this->payfeeorderOne['student_id'];
                    $in_data['course_id'] = $itemsOne['course_id'];
                    $in_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                    $in_data['income_price'] = $itemsOne['item_totalprice'];
                    $in_data['income_note'] = $this->LgStringSwitch('课程杂费一次性消耗');
                    $in_data['income_confirmtime'] = $time;
                    $in_data['income_audittime'] = $time;
                    $in_data['income_createtime'] = time();
                    $this->DataControl->insertData("smc_school_income", $in_data);

                    $data = array();
                    $data['itemtimes_number'] = ($studentItemOne['itemtimes_number'] - $itemsOne['item_buynums']) > 0 ? $studentItemOne['itemtimes_number'] - $itemsOne['item_buynums'] : 0;
                    $data['itemtimes_figure'] = ($studentItemOne['itemtimes_figure'] - $itemsOne['item_totalprice']) > 0 ? ($studentItemOne['itemtimes_figure'] - $itemsOne['item_totalprice']) : 0;
                    $data['itemtimes_updatatime'] = $time;

                    if (isset($itemsOne['course_id']) && $itemsOne['course_id'] > 0) {
                        $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$itemsOne['course_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}'", $data);
                    } else {
                        $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}' and feeitem_id='{$feeitemOne['feeitem_id']}'", $data);
                    }
                }
            }
        }

        return true;

    }

    function useCoupons($student_id, $coupons_id)
    {
        $data = array();
        $data['coupons_isuse'] = 1;
        $data['coupons_usetime'] = time();
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $this->DataControl->updateData("smc_student_coupons", "company_id='{$this->company_id}' and student_id='{$student_id}' and coupons_id='{$coupons_id}'", $data);


        $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_class", "coupons_id='{$coupons_id}'");

        if ($couponsOne['coupons_class'] == 1) {
            $data = array();
            $data['card_status'] = 2;
            $this->DataControl->updateData("smc_activity_ticket_card", "coupons_id='{$coupons_id}'", $data);
        }

    }

    function addOrderCouponsLog($order_pid, $course_id, $goods_id, $coupons_pid, $ordercoupons_price)
    {
        $data = array();
        $data['order_pid'] = $order_pid;
        $data['course_id'] = $course_id;
        $data['goods_id'] = $goods_id;
        $data['coupons_pid'] = $coupons_pid;
        $data['ordercoupons_price'] = $ordercoupons_price;
        $this->DataControl->insertData('smc_payfee_order_coupons', $data);
    }

    /**
     * @param $student_id
     * @param $course_id
     * @param $items_id --教学用品定价ID
     * @param $number --项目剩余次数
     * @param $itemtimes_id --项目剩余余额
     * @param $playamount --操作金额
     */
//    function buyGoods($student_id, $course_id, $items_id, $number, $figure, $playamount)
//    {
//        $itemtimes_data = array();
//        $itemtimes_data['student_id'] = $student_id;
//        $itemtimes_data['course_id'] = $course_id;
//        $itemtimes_data['items_id'] = $items_id;
//        $itemtimes_data['itemtimes_number'] = $number;
//        $itemtimes_data['itemtimes_figure'] = $figure;
//        $itemtimes_data['itemtimes_createtime'] = time();
//        $itemtimes_id = $this->DataControl->insertData("smc_student_itemtimes", $itemtimes_data);
//
//        if ($itemtimes_id) {
//            $itemtimeslog_data = array();
//            $itemtimeslog_data['student_id'] = $student_id;
//            $itemtimeslog_data['itemtimes_id'] = $itemtimes_id;
//            $itemtimeslog_data['log_playname'] = $this->LgStringSwitch('购买课程杂费');
//            $itemtimeslog_data['log_playclass'] = '+';
//            $itemtimeslog_data['log_fromamount'] = 0;
//            $itemtimeslog_data['log_playamount'] = $playamount;
//            $itemtimeslog_data['log_finalamount'] = $playamount;
//            $itemtimeslog_data['log_reason'] = $this->LgStringSwitch('购买课程杂费');
//            $itemtimeslog_data['log_time'] = time();
//            $this->DataControl->insertData("smc_student_itemtimes_log", $itemtimeslog_data);
//            return true;
//        } else {
//            return false;
//        }
//    }

    //生成支付订单记录
    function orderPay($typename, $issuccess, $note = '', $paytype_code = "", $pay_price, $create_time = "", $from = 0, $paychannel_code = '', $img = '')
    {
        if ($pay_price < 0) {
            return false;
        }
        $balance_array = array('balance', 'forward', 'norebalance', 'cattimes', 'catdeposit', 'catsales', 'catmanage', 'catbus', 'catfood');
        $cash_array = array('cash');
        $system_array = array('feewaiver');
        if (!$paychannel_code) {
            if (in_array($paytype_code, $balance_array)) {
                $paychannel_code = 'balance';
            } elseif (in_array($paytype_code, $cash_array)) {
                $paychannel_code = 'cash';
            } elseif (in_array($paytype_code, $system_array)) {
                $paychannel_code = 'system';
            }
        }

        $data = array();
        do {
            $paypid = $this->createOrderPid('ZF');
        } while ($this->DataControl->selectOne("select pay_id from smc_payfee_order_pay where pay_pid='{$paypid}' limit 0,1"));
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $data['pay_pid'] = $paypid;
        $data['pay_typename'] = $typename;
        $data['pay_price'] = $pay_price;
        if ($this->payfeeorderOne['order_type'] == 2) {
            $from = 3;
        }
        $data['pay_type'] = $from;
        $data['paytype_code'] = $paytype_code;
        $data['paychannel_code'] = $paychannel_code;
        $data['pay_issuccess'] = $issuccess;
        $data['pay_note'] = $note;
        if ($img) {
            $data['pay_img'] = $img;
        }
        if (empty($create_time)) {
            $data['pay_createtime'] = time();
        } else {
            $data['pay_createtime'] = strtotime(date("Y-m-d", strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
        }

        if ($this->DataControl->insertData("smc_payfee_order_pay", $data)) {
            if($paytype_code == 'coursebalance'){
                $data = array();
                $data['protocol_isdel'] = '1';
                $this->DataControl->updateData("smc_student_protocol","order_pid = '{$this->payfeeorderOne['order_pid']}'",$data);
            }
            return $paypid;
        } else {
            return false;
        }
    }

    function orderPayFee($issuccess, $note = '', $pay_price, $create_time = "")
    {
        if ($pay_price < 0) {
            return false;
        }
        $data = array();
        do {
            $paypid = $this->createOrderPid('ZF');
        } while ($this->DataControl->selectOne("select pay_id from smc_payfee_order_pay where pay_pid='{$paypid}' limit 0,1"));
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $data['pay_pid'] = $paypid;
        $data['pay_price'] = $pay_price;
        $data['pay_issuccess'] = $issuccess;
        $data['pay_note'] = $note;
        if (empty($create_time)) {
            $data['pay_createtime'] = time();
        } else {
            $data['pay_createtime'] = strtotime($create_time);
        }

        if ($this->DataControl->insertData("smc_payfee_order_pay", $data)) {
            return $paypid;
        } else {
            return false;
        }
    }


    function addIncharge($pay_pid, $tradeno, $type_name, $price, $note)
    {

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $data['school_id'] = $this->school_id;
        $data['student_id'] = $this->payfeeorderOne['student_id'];
        $data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
//		$Model = new \Model\Smc\BalanceModel($this->publicarray,$this->payfeeorderOne['order_pid']);
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['pay_pid'] = $pay_pid;
        $data['incharge_typename'] = $type_name;
        $data['incharge_outnumber'] = $tradeno;
        $data['incharge_price'] = $price;
        $data['incharge_note'] = $note;
        $data['incharge_createtime'] = time();
        $this->DataControl->insertData("smc_school_incharge", $data);
    }

    //订单支付成功日志
    function orderPaylog($paypid, $actualprice, $tradeno, $ifee = 0, $pay_note = "", $paytype_code = "", $create_time = "", $bakjson = '', $img = '', $paychannel_code = '')
    {
        if ($actualprice < 0) {
            return false;
        }
        $data = array();
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['pay_pid'] = $paypid;
        $data['paylog_actualprice'] = $actualprice;
        $data['paylog_img'] = $img;
        if (empty($create_time)) {
            $data['paylog_paytime'] = time();
        } else {
            $data['paylog_paytime'] = strtotime(date("Y-m-d", strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
        }

        $data['paylog_tradeno'] = $tradeno;
        $data['paylog_ifee'] = $ifee;
        $data['paylog_bakjson'] = $bakjson;

        if (empty($create_time)) {
            $data['paylog_addtime'] = time();
        } else {
            $data['paylog_addtime'] = strtotime(date("Y-m-d", strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
        }

        if($actualprice>0){
            $this->autoTransCompaniesBalance($this->payfeeorderOne['company_id'],$this->payfeeorderOne['school_id'],$this->payfeeorderOne['student_id'],$this->payfeeorderOne['companies_id'],$actualprice,$paytype_code);
        }

        if ($this->DataControl->insertData("smc_payfee_order_paylog", $data)) {
            if (!$this->updateOrderPay($paypid, 1, $tradeno, $pay_note, $paytype_code, $create_time, $paychannel_code)) {
                return false;
            } else {
                return true;
            }
        } else {

            return false;
        }
    }

    function autoTransCompaniesBalance($company_id,$school_id,$student_id,$companies_id,$actualprice,$paytype_code){

        if($paytype_code=='norebalance'){
            $sql = "select * 
                from smc_student_balance as a 
                where a.school_id='{$school_id}' and a.student_id='{$student_id}' and a.student_withholdbalance>0
                ";
        }elseif($paytype_code=='balance'){
            $sql = "select * 
                from smc_student_balance as a 
                where a.school_id='{$school_id}' and a.student_id='{$student_id}' and a.student_balance>0
                ";
        }else{
            return false;
        }


        $balanceList=$this->DataControl->selectClear($sql);

        if($balanceList){

            $balanceArray=array_column($balanceList,null,'companies_id');

            $data=array();
            $data['company_id']=$company_id;
            $data['school_id']=$school_id;
            $data['student_id']=$student_id;
            $data['from_school_id']=$school_id;
            $data['to_school_id']=$school_id;
            $data['to_companies_id']=$companies_id;
            $data['note']=$this->LgStringSwitch("组合支付自动转移余额");

            if($paytype_code=='norebalance'){
                $student_withholdbalance=$balanceArray[$companies_id]['student_withholdbalance']>0?$balanceArray[$companies_id]['student_withholdbalance']:0;

                if($student_withholdbalance>0 && $student_withholdbalance>=$actualprice){
                    return true;
                }else{
                    $actualprice-=$student_withholdbalance;
                }

                foreach($balanceList as $balanceOne){
                    if($balanceOne['companies_id']!=$companies_id){

                        $data['from_companies_id']=$balanceOne['companies_id'];

                        if($balanceOne['student_withholdbalance']>=$actualprice){

                            $data['withholdbalance']=$actualprice;
                            $this->autoTrans($data);
                            return true;

                        }else{
                            $data['withholdbalance']=$balanceOne['student_withholdbalance'];
                            $this->autoTrans($data);
                            $actualprice-=$balanceOne['student_withholdbalance'];
                        }
                    }
                }
            }elseif($paytype_code=='balance'){
                $student_balance=$balanceArray[$companies_id]['student_balance']>0?$balanceArray[$companies_id]['student_balance']:0;
                if($student_balance>0 && $student_balance>=$actualprice){
                    return true;
                }else{
                    $actualprice-=$student_balance;
                }
                foreach($balanceList as $balanceOne){
                    if($balanceOne['companies_id']!=$companies_id){

                        $data['from_companies_id']=$balanceOne['companies_id'];

                        if($balanceOne['student_balance']>=$actualprice){

                            $data['balance']=$actualprice;
                            $this->autoTrans($data);
                            return true;

                        }else{
                            $data['balance']=$balanceOne['student_balance'];
                            $this->autoTrans($data);
                            $actualprice-=$balanceOne['student_balance'];
                        }
                    }
                }
            }
        }
    }

    function autoTrans($request){

        $TransactionModel = new \Model\Smc\TransactionModel($request);

        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $oldTrading_pid = $TransactionModel->stuTrading($request['student_id'], $request['from_school_id'], 'TransferOut', $request['from_companies_id']);

        $trading_pid = $TransactionModel->schoolTrade($request['student_id'], $request['from_school_id'], $request['to_school_id'], $oldTrading_pid, '', 0, 1, $request['balance'], $request['withholdbalance'], $request['from_companies_id'], $request['to_companies_id'], $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功，请耐心等待财务审核'), $request['note']);

        $request['note'] = $this->LgStringSwitch('余额转移待审核,备注:') . $request['note'];

        $BalanceModel->consumeStuBalance($request['student_id'], $request['from_school_id'], $oldTrading_pid, $request['balance'], $request['withholdbalance'], $request['from_companies_id'], $this->LgStringSwitch('资产转移'), $request['note']);

        $tradeOne = $this->DataControl->getOne("smc_school_trading", "trading_pid='{$trading_pid}' and company_id='{$request['company_id']}'");

        if ($tradeOne) {

            $request['note'] = $this->LgStringSwitch('自动审核');

            $Trading_pid = $TransactionModel->stuTrading($tradeOne['student_id'], $tradeOne['to_school_id'], 'TransferIn', $tradeOne['to_companies_id']);

            $BalanceModel->addStuAllBalance($tradeOne['student_id'], $tradeOne['to_school_id'], $Trading_pid, $tradeOne['trading_balance'], $tradeOne['trading_withholdbalance'], $tradeOne['to_companies_id'], $this->LgStringSwitch('资产转移'), $this->LgStringSwitch("余额转移成功,备注:") . $request['note']);

            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname", "company_id='{$request['company_id']}' and account_class=1");

            $data = array();
            $data['trading_pid'] = $tradeOne['trading_pid'];
            $data['tracks_title'] = $this->LgStringSwitch('审核订单');
            $data['tracks_information'] = $this->LgStringSwitch('订单审核通过，订单完成');
            $data['tracks_note'] = $request['note'];
            $data['staffer_id'] = $stafferOne['staffer_id'];
            $data['tracks_playname'] = $stafferOne['staffer_cnname'];
            $data['tracks_time'] = time();
            $this->DataControl->insertData("smc_school_trading_tracks", $data);

            $data = array();
            $data['trading_status'] = '2';
            $data['trading_note'] = $request['note'];
            $data['trading_topid'] = $Trading_pid;
            $data['trading_updatatime'] = time();

            $this->DataControl->updateData("smc_school_trading", "trading_pid='{$tradeOne['trading_pid']}'", $data);
        }



    }

    function makeUpFees($student_id, $course_id, $pricing_id, $needNum, $unitexpend, $unitrefund, $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $CompaniesOne = $this->getSchoolCourseCompanies($this->school_id,0,$course_id);
        $orderData = array();
        do {
            $orderPid = $this->createOrderPid('BF');
        } while ($this->DataControl->selectOne("select order_id from smc_payfee_order where order_pid='{$orderPid}' and company_id='{$this->company_id}' limit 0,1"));
        $orderData['trading_pid'] = $this->stuTrading($student_id, $CompaniesOne['companies_id'],'CourseMakeUp', $time);
        $orderData['order_pid'] = $orderPid;
        $orderData['company_id'] = $this->company_id;
        $orderData['school_id'] = $this->school_id;
        $orderData['companies_id'] = $CompaniesOne['companies_id'];
        $orderData['staffer_id'] = $this->stafferOne['staffer_id'];
        $orderData['student_id'] = $student_id;
        $orderData['order_from'] = 1;
        $orderData['order_type'] = 0;
        $orderData['order_status'] = 1;
        $orderData['order_iscreatecourse'] = 1;
        $orderData['order_allprice'] = $needNum * $unitexpend;
        $orderData['order_paymentprice'] = $needNum * $unitexpend;
        $orderData['order_note'] = '课程补费';
        $orderData['order_createtime'] = $time;
        if ($this->DataControl->insertData("smc_payfee_order", $orderData)) {
            $pricingOne = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id='{$pricing_id}'");
            $orderCourseData = array();
            $orderCourseData['order_pid'] = $orderPid;
            $orderCourseData['agreement_id'] = $pricingOne['agreement_id'];
            $orderCourseData['pricing_id'] = $pricing_id;
            $orderCourseData['course_id'] = $course_id;
            $orderCourseData['ordercourse_buynums'] = $needNum;
            $orderCourseData['ordercourse_unitprice'] = $unitexpend;
            $orderCourseData['ordercourse_unitrefund'] = $unitrefund;
            $orderCourseData['ordercourse_totalprice'] = $needNum * $unitexpend;
            $this->DataControl->insertData('smc_payfee_order_course', $orderCourseData);

            $orderTracksData = array();
            $orderTracksData['order_pid'] = $orderPid;
            $orderTracksData['tracks_title'] = $this->LgStringSwitch('课程补费');
            $orderTracksData['tracks_information'] = $this->LgStringSwitch('课程补费');
            $orderTracksData['staffer_id'] = $this->stafferOne['staffer_id'];
            $orderTracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
//            $orderTracksData['tracks_time'] = $time;//戚总确认日志时间用 系统时间
            $orderTracksData['tracks_time'] = time();
            $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);

            return $orderPid;
        } else {
            return false;
        }
    }


    //修改订单状态
    function updateOrder($actualprice, $order_status = 1, $create_time = "", $paychannel_code = "", $paytype_code = '', $pay_pid = '')
    {
        $title = '';
        if ($this->payfeeorderOne['order_status'] == '0') {
            $order_status = 0;
            $title = $this->LgStringSwitch("订单状态为待审核");
        } else {

            if (!$paytype_code) {
                $payOne = $this->DataControl->getFieldOne("smc_payfee_order_pay", "paytype_code", "pay_pid='{$pay_pid}'");
                $paytype_code = $payOne['paytype_code'];
            }

            $payname = $this->DataControl->getFieldOne('smc_code_paytype', "paytype_name", "paytype_code='{$paytype_code}'");
            $paytypename = $payname['paytype_name'];

            if ($actualprice == $this->payfeeorderOne['order_paymentprice']) {
                $title .= "一次性支付";
            } else {
                $title .= "分批支付";
            }

            $title .= $actualprice . '元，支付方式为' . $paytypename . '支付，支付编号' . $pay_pid;
            $title = $this->LgStringSwitch($title);
        }

        if ($order_status == 4) {
            $this->orderTracks($this->LgStringSwitch('已完成'), $title);
        } else {
            $this->orderTracks($this->LgStringSwitch('支付中'), $title);
        }

        $orderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}' and company_id='{$this->company_id}' and school_id ='{$this->school_id}' ");

        $orderData = array();
        $orderData['order_paidprice'] = $orderOne['order_paidprice'] + $actualprice;
        $orderData['order_status'] = $order_status;
        if (empty($create_time)) {
            $orderData['order_updatatime'] = time();
        } else {
            $orderData['order_updatatime'] = strtotime(date("Y-m-d", strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
        }
        $orderData['staffer_id'] = $this->stafferOne['staffer_id'];

        $tradingOne = $this->DataControl->getOne("smc_student_trading", "trading_pid='{$orderOne['trading_pid']}'");
        $companies_id = $tradingOne['companies_id'];
        if ($orderOne['order_type'] == 2) {
            if ($tradingOne['tradingtype_code'] == 'DepositCharge') {
                $stuccblcOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure", "feetype_code='Deposit' and coursetype_id='{$orderOne['coursetype_id']}' and coursecat_id='{$orderOne['coursecat_id']}' and student_id='{$orderOne['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}'");

                if ($stuccblcOne) {
                    $log = array();
                    $log['student_id'] = $orderOne['student_id'];
                    $log['coursetype_id'] = $orderOne['coursetype_id'];
                    $log['coursecat_id'] = $orderOne['coursecat_id'];
                    $log['feetype_code'] = 'Deposit';
                    $log['school_id'] = $this->school_id;
                    $log['companies_id'] = $companies_id;
                    $log['staffer_id'] = $this->staffer_id;
                    $log['trading_pid'] = $orderOne['trading_pid'];
                    $log['log_playclass'] = '+';
                    $log['log_fromamount'] = $stuccblcOne['coursecatbalance_figure'];
                    $log['log_playamount'] = $orderOne['order_paymentprice'];
                    $log['log_finalamount'] = $stuccblcOne['coursecatbalance_figure'] + $orderOne['order_paymentprice'];
                    $log['log_fromme'] = 0;
                    $log['log_playme'] = 0;
                    $log['log_finaltime'] = 0;
                    $log['log_time'] = time();
                    $log['log_playname'] = $this->LgStringSwitch('定金充值');
                    $log['log_reason'] = $this->LgStringSwitch('定金充值');
                    $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                    $data = array();
                    $data['coursecatbalance_figure'] = $stuccblcOne['coursecatbalance_figure'] + $orderOne['order_paymentprice'];
                    $data['coursecatbalance_updatatime'] = time();
                    $this->DataControl->updateData("smc_student_coursecatbalance", "feetype_code='Deposit' and coursecat_id='{$orderOne['coursecat_id']}' and coursetype_id='{$orderOne['coursetype_id']}' and student_id='{$orderOne['student_id']}' and school_id='{$this->school_id}' and company_id='{$this->company_id}'", $data);
                } else {
                    $log = array();
                    $log['school_id'] = $this->school_id;
                    $log['companies_id'] = $companies_id;
                    $log['student_id'] = $orderOne['student_id'];
                    $log['coursetype_id'] = $orderOne['coursetype_id'];
                    $log['coursecat_id'] = $orderOne['coursecat_id'];
                    $log['feetype_code'] = 'Deposit';
                    $log['staffer_id'] = $this->staffer_id;
                    $log['trading_pid'] = $orderOne['trading_pid'];
                    $log['log_playclass'] = '+';
                    $log['log_fromamount'] = 0;
                    $log['log_playamount'] = $orderOne['order_paymentprice'];
                    $log['log_finalamount'] = $orderOne['order_paymentprice'];
                    $log['log_fromme'] = 0;
                    $log['log_playme'] = 0;
                    $log['log_finaltime'] = 0;
                    $log['log_time'] = time();
                    $log['log_playname'] = $this->LgStringSwitch('定金充值');
                    $log['log_reason'] = $this->LgStringSwitch('定金充值');
                    $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                    $data = array();
                    $data['company_id'] = $this->company_id;
                    $data['school_id'] = $this->school_id;
                    $data['companies_id'] = $companies_id;
                    $data['student_id'] = $orderOne['student_id'];
                    $data['coursetype_id'] = $orderOne['coursetype_id'];
                    $data['coursecat_id'] = $orderOne['coursecat_id'];
                    $data['feetype_code'] = 'Deposit';
                    $data['coursecatbalance_figure'] = $orderOne['order_paymentprice'];
                    $data['coursecatbalance_createtime'] = time();
                    $this->DataControl->insertData("smc_student_coursecatbalance", $data);
                }
            } else {
                $stublcOne = $this->getStuBalance($orderOne['student_id'],$this->company_id,$this->school_id,$companies_id);
                $finalamount = ($stublcOne['student_balance'] ? $stublcOne['student_balance'] : 0) + $orderOne['order_paymentprice'];
                $balancedata = array();
                $balancedata['company_id'] = $this->company_id;
                $balancedata['companies_id'] = $companies_id;
                $balancedata['school_id'] = $this->school_id;
                $balancedata['staffer_id'] = $this->stafferOne['staffer_id'];
                $balancedata['student_id'] = $orderOne['student_id'];
                $balancedata['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $balancedata['balancelog_class'] = 0;
                $balancedata['balancelog_playclass'] = "+";
                $balancedata['balancelog_playname'] = $this->LgStringSwitch("账户充值");
                $balancedata['balancelog_fromamount'] = $stublcOne['student_balance'];
                $balancedata['balancelog_playamount'] = $orderOne['order_paymentprice'];
                $balancedata['balancelog_finalamount'] = $finalamount;
                $balancedata['balancelog_reason'] = $this->LgStringSwitch("账户充值");
                if (empty($create_time)) {
                    $balancedata['balancelog_time'] = time();
                } else {
                    $balancedata['balancelog_time'] = strtotime(date('Y-m-d', strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
                }
                $this->DataControl->insertData("smc_student_balancelog", $balancedata);
                if ($stublcOne) {
                    //结算账户余额
                    $data = array();
                    $data['student_balance'] = $finalamount;
                    $this->DataControl->updateData("smc_student_balance", "student_id='{$orderOne['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companies_id}'", $data);
                } else {
                    //结算账户余额
                    $data = array();
                    $data['company_id'] = $this->company_id;
                    $data['companies_id'] = $companies_id;
                    $data['school_id'] = $this->school_id;
                    $data['student_id'] = $orderOne['student_id'];
                    $data['student_balance'] = $finalamount;
                    $this->DataControl->insertData("smc_student_balance", $data);
                }
            }
        }
        $this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}' and company_id='{$this->company_id}'", $orderData);

        return true;
    }

    //修改支付订单状态--修改货品订单
    function updateOrderPay($pay_pid, $pay_issuccess, $pay_outnumber, $pay_note, $paytype_code = "", $create_time = "", $paychannel_code = '')
    {
        $data = array();
        $data['pay_issuccess'] = $pay_issuccess;

        if (empty($create_time)) {
            $data['pay_updatatime'] = time();
            $data['pay_successtime'] = time();
        } else {
            if ($paytype_code == 'balance') {
                $data['pay_updatatime'] = time();
                $data['pay_successtime'] = strtotime(date("Y-m-d", strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
            } else {
                $data['pay_updatatime'] = time();
                $data['pay_successtime'] = strtotime(date("Y-m-d", strtotime($create_time))) + ((time() + 8 * 3600) % 86400);
            }
        }

        $balance_array = array('balance', 'forward', 'norebalance', 'cattimes', 'catdeposit', 'catsales', 'catmanage', 'catbus', 'catfood');
        $cash_array = array('cash');
        $system_array = array('feewaiver', 'canceldebts');
        if (!$paychannel_code) {
            if (in_array($paytype_code, $balance_array)) {
                $paychannel_code = 'balance';
            } elseif (in_array($paytype_code, $cash_array)) {
                $paychannel_code = 'cash';
            } elseif (in_array($paytype_code, $system_array)) {
                $paychannel_code = 'system';
            }
        }
        $data['paychannel_code'] = $paychannel_code;
        $data['pay_outnumber'] = $pay_outnumber;
        if (!empty($pay_note)) {
            $data['pay_note'] = $pay_note;
        }

        if ($paytype_code) {
            $payname = $this->DataControl->getFieldOne('smc_code_paytype', "paytype_name", "paytype_code='{$paytype_code}'");
            if ($payname['paytype_name']) {
                $data['pay_typename'] = $payname['paytype_name'];
            }
            $data['paytype_code'] = $paytype_code;
        } else {
            $payname = array();
        }

        $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_pid='{$pay_pid}'", $data);

        $payTime = date("Y-m-d H:i:s",time());
        $title = "支付编号{$pay_pid}支付成功,支付时间{$payTime}";
        $this->orderTracks($this->LgStringSwitch('支付日志'), $title);

        if ($pay_issuccess == 1) {
//            $surplusPrice =  $this->unSurplusPrice();
            $surplusPrice = $this->payfeeorderOne['order_paymentprice'];

            $orderPayMentPrice = $this->DataControl->selectOne("select paylog_actualprice from smc_payfee_order_paylog  where pay_pid ='{$pay_pid}'");

            $orderPay = $this->DataControl->selectOne("select sum(pay_price) as pay_actualprice  from smc_payfee_order_pay  where order_pid ='{$this->payfeeorderOne['order_pid']}' and  pay_issuccess =1 and  paytype_code<>'feewaiver'");
            if ($surplusPrice == $orderPay['pay_actualprice']) {
                $order_status = 4;
                $this->updateOrder($orderPayMentPrice['paylog_actualprice'], $order_status, $create_time, $paychannel_code, $paytype_code, $pay_pid);
                $traData = array();
                $traData['trading_status'] = 1;
                $traData['trading_updatatime'] = time();
                $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $traData);

                $sql = "select A.course_id,B.student_id,B.school_id,B.companies_id
                        from smc_payfee_order_course as A,smc_payfee_order as B,gmc_code_companies as C,smc_course D
                        where A.order_pid=B.order_pid 
                        and B.companies_id=C.companies_id 
                        and D.course_id=A.course_id 
                        and A.order_pid ='{$this->payfeeorderOne['order_pid']}' 
                        and D.course_issupervise=1
                        and C.companies_issupervise=1
                        and exists(select 1 from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=A.order_pid and y.paytype_ischarge=1)
                        ";

                $courseList=$this->DataControl->selectClear($sql);

                if($courseList){
                    foreach($courseList as $courseOne){
                        $data=array();
                        $data['coursebalance_issupervise']=1;
                        $this->DataControl->updateData("smc_student_coursebalance","student_id='{$courseOne['student_id']}' and course_id='{$courseOne['course_id']}' and school_id='{$courseOne['school_id']}' and companies_id='{$courseOne['companies_id']}'",$data);
                    }
                }else{

                    $sql = "select ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y,smc_payfee_order as z where x.paytype_code=y.paytype_code and x.order_pid=z.order_pid and z.student_id='{$this->payfeeorderOne['student_id']}' and z.companies_id=a.companies_id and y.paytype_ischarge=1 and x.pay_issuccess=1 and x.pay_successtime>=a.companies_supervisetime),0) as all_amt
                    ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.student_id='{$this->payfeeorderOne['student_id']}' and x.companies_id=a.companies_id and x.is_confirm=1 and x.order_type='P'),0) as orderPrice
                    ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.student_id='{$this->payfeeorderOne['student_id']}' and x.companies_id=a.companies_id and x.is_confirm=1 and x.order_type='R'),0) as refundPrice
                    from gmc_code_companies as a 
                    where a.companies_id='{$this->payfeeorderOne['companies_id']}'";
                    $infoOne=$this->DataControl->selectOne($sql);

                    if($infoOne){

                        if($infoOne['all_amt'] > 0){
                            //已确认监管
                            $configOkPrice = $infoOne['orderPrice']-$infoOne['refundPrice'];
                            if($infoOne['all_amt'] > $configOkPrice){
                                //剩余监管余额
                                $shengyuOkPrice = $infoOne['all_amt']-$configOkPrice;
                                if( $shengyuOkPrice >0){

                                    $sql = "select A.course_id,B.student_id,B.school_id,B.companies_id
                        from smc_payfee_order_course as A,smc_payfee_order as B,gmc_code_companies as C,smc_course D
                        where A.order_pid=B.order_pid 
                        and B.companies_id=C.companies_id 
                        and D.course_id=A.course_id 
                        and A.order_pid ='{$this->payfeeorderOne['order_pid']}' 
                        and D.course_issupervise=1
                        and C.companies_issupervise=1
                        ";

                                    $courseList=$this->DataControl->selectClear($sql);

                                    if($courseList){
                                        foreach($courseList as $courseOne){
                                            $data=array();
                                            $data['coursebalance_issupervise']=1;
                                            $this->DataControl->updateData("smc_student_coursebalance","student_id='{$courseOne['student_id']}' and course_id='{$courseOne['course_id']}' and school_id='{$courseOne['school_id']}' and companies_id='{$courseOne['companies_id']}'",$data);
                                        }
                                    }

                                }
                            }
                        }
                    }
                }

                //订单完成业绩管理相关操作
                $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$this->payfeeorderOne['order_pid']}'");
                if ($achieveList) {
                    foreach ($achieveList as $achieveOne) {
                        $performanceOne = $this->DataControl->getFieldOne("gmc_code_performance", "performance_iscalculated", "performance_id='{$achieveOne['performance_id']}'");
                        if ($performanceOne['performance_iscalculated'] == $achieveOne['achieve_iscalculated'] && $achieveOne['achieve_from'] == 0) {
                            $data = array();
                            $data['achieve_status'] = 2;
                            $data['achieve_updatatime'] = time();
                            $this->DataControl->updateData("smc_staffer_achieve", "achieve_id='{$achieveOne['achieve_id']}'", $data);

                            $data = array();
                            $data['achieve_id'] = $achieveOne['achieve_id'];
                            $data['tracks_title'] = $this->LgStringSwitch('订单已支付完成');
                            $data['tracks_information'] = $this->LgStringSwitch('订单已支付完成，系统自动审核通过');
                            $data['staffer_id'] = $this->stafferOne['staffer_id'];
                            $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                            $data['tracks_time'] = time();
                            $this->DataControl->insertData("smc_staffer_achieve_track", $data);
                        }
                    }
                }

                $this->autoUseGoods($this->payfeeorderOne['order_pid']);


            } elseif ($surplusPrice > $orderPayMentPrice['paylog_actualprice']) {
                $order_status = 2;
                $this->updateOrder($orderPayMentPrice['paylog_actualprice'], $order_status, $create_time, $paychannel_code, $paytype_code, $pay_pid);
            }
        }
        return true;
    }

    function autoUseGoods($order_pid){

        $sql = "select a.erpgoods_id 
                from smc_student_erpgoods as a 
                left join erp_goods as b on b.goods_id=a.goods_id
                where a.order_pid='{$order_pid}' and a.erpgoods_isreceive=0 and b.goods_isautouse=1 and a.erpgoods_isrefund=0
                ";

        $goodsList=$this->DataControl->selectClear($sql);

        $StudentModel = new \Model\Smc\StudentModel($this->publicarray);

        if($goodsList){
            foreach($goodsList as $goodsOne){

                $data=array();
                $data['company_id']=$this->company_id;
                $data['school_id']=$this->school_id;
                $data['staffer_id']=$this->staffer_id;
                $data['erpgoods_id']=$goodsOne['erpgoods_id'];

                $StudentModel->receiveGoods($data);

            }


        }

    }


    //取消支付订单
    function cancelPayOrderzhaohang($pay_pid)
    {
        $sql = "select c.companies_chargchannel
                from smc_payfee_order_pay as a,smc_payfee_order as b,gmc_code_companies as c 
                where a.order_pid=b.order_pid and b.companies_id=c.companies_id and a.pay_pid='{$pay_pid}'";
        $cmbcompaniesOne=$this->DataControl->selectOne($sql);
        if($cmbcompaniesOne['companies_chargchannel']=='cmbheadbank'){
            //同步取消招行总行的订单
            $cancelarray = array();
            $cancelarray['paypid'] = $pay_pid;
            request_by_curl("https://scshopapi.kedingdang.com/HeadBoingPay/OrderPayCancel", dataEncode($cancelarray), "GET");
        }elseif($cmbcompaniesOne['companies_chargchannel']=='cmbbank'){
            //同步取消招行的订单
            $cancelarray = array();
            $cancelarray['paypid'] = $pay_pid;
            request_by_curl("https://scshopapi.kedingdang.com/BoingPay/OrderPayCancel", dataEncode($cancelarray), "GET");
        }elseif($cmbcompaniesOne['companies_chargchannel']=='cmbmergebank'){
            //同步取消招行的订单
            $cancelarray = array();
            $cancelarray['paypid'] = $pay_pid;
            request_by_curl("https://scshopapi.kedingdang.com/MergeBoingPay/OrderPayCancel", dataEncode($cancelarray), "GET");
        }

    }

    //取消支付订单
    function cancelPayOrder($pay_pid)
    {
        $data['pay_issuccess'] = -1;

        $payOne = $this->DataControl->getOne("smc_payfee_order_pay", "order_pid='{$this->payfeeorderOne['order_pid']}' and  pay_pid='{$pay_pid}'");
        if ($payOne['pay_issuccess'] == 1) {
            $this->error = 1;
            $this->errortip = "该支付订单已支付";
            return false;
        } elseif ($payOne['pay_issuccess'] == -1) {
            $this->error = 1;
            $this->errortip = "该订单已经取消";
            return false;
        }

        $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$this->payfeeorderOne['order_pid']}' and  pay_pid='{$pay_pid}' ", $data);

        //同步取消招行的订单
        $this->cancelPayOrderzhaohang($pay_pid);
//        $cancelarray = array();
//        $cancelarray['paypid'] = $pay_pid;
//        request_by_curl("https://scshopapi.kedingdang.com/BoingPay/OrderPayCancel", dataEncode($cancelarray), "GET");

        if ($this->payfeeorderOne['order_status'] == '0' && $this->payfeeorderOne['order_isneedaudit'] == '1' && $this->payfeeorderOne['order_iscreatecourse'] == '0') {

            $couponsList = $this->DataControl->selectClear("select coupons_pid from smc_payfee_order_coupons where order_pid='{$this->payfeeorderOne['order_pid']}'");
            if ($couponsList) {
                foreach ($couponsList as $couponsOne) {
                    $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,coupons_class", "student_id='{$this->payfeeorderOne['student_id']}' and coupons_pid='{$couponsOne['coupons_pid']}'");
                    if ($couponsOne) {
                        $coupons_data = array();
                        $coupons_data['coupons_isuse'] = 0;
                        $coupons_data['order_pid'] = '';
                        $coupons_data['coupons_usetime'] = '0';
                        $this->DataControl->updateData("smc_student_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $coupons_data);

                        if ($couponsOne['coupons_class'] == 1) {
                            $data = array();
                            $data['card_status'] = 1;
                            $this->DataControl->updateData("smc_activity_ticket_card", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                        }
                    }
                }
            }

            $data = array();
            $data['order_status'] = '-1';
            $data['order_updatatime'] = time();
            $this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);

            $data = array();
            $data['trading_status'] = '-1';
            $data['trading_updatatime'] = time();
            $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);
        }

        $dataTrack = array();
        $dataTrack['order_pid'] = $this->payfeeorderOne['order_pid'];
        $dataTrack['tracks_title'] = $this->LgStringSwitch("取消支付");
        $dataTrack['tracks_information'] = $this->LgStringSwitch("取消支付,支付编号" . $pay_pid);
        $dataTrack['staffer_id'] = $this->stafferOne['staffer_id'];
        $dataTrack['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $dataTrack['tracks_time'] = time();

        $this->DataControl->insertData("smc_payfee_order_tracks", $dataTrack);

        $this->error = 0;
        $this->errortip = "取消成功";
        return true;
    }

    function totalPerformance($staffer_id, $coursetype_id, $performance_id, $achieve_from, $achieve_price, $achieve_iscalculated,$status=0, $title='', $note='', $create_time = '')
    {
        if (!$create_time) {
            $time = time();
        }

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['coursetype_id'] = $coursetype_id;
        $data['staffer_id'] = $staffer_id;
        $data['performance_id'] = $performance_id;
        $data['achieve_price'] = $achieve_price;
        $data['achieve_from'] = $achieve_from;
        $data['achieve_status'] = $status;
        $data['achieve_iscalculated'] = $achieve_iscalculated;
        $data['achieve_note'] = $note;
        $data['achieve_confirmmonth'] = date("Y-m", $time);
        $data['achieve_createtime'] = time();

        $achieve_id = $this->DataControl->insertData("smc_staffer_achieve", $data);
        $data = array();
        $data['achieve_id'] = $achieve_id;
        $data['tracks_title'] = $this->LgStringSwitch($title);

        $str = ($this->stafferOne['staffer_enname'] ? $this->stafferOne['staffer_cnname'] . '-' . $this->stafferOne['staffer_enname'] : $this->stafferOne['staffer_cnname']) . '提交业绩信息,交易编号:' . $this->payfeeorderOne['trading_pid'];

        $data['tracks_information'] = $this->LgStringSwitch($str);
        $data['tracks_note'] = $note;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $data['tracks_time'] = time();
        $this->DataControl->insertData("smc_staffer_achieve_track", $data);

    }


//	//更新 订单标签 --暂不使用
//	 function updateTag($request){
//
//		 $data= array();
//		 $data['order_taglist']  = trim($request['order_taglist'],',');
//		 $this->DataControl->updateData("smc_payfee_order","order_pid='{$request['order_pid']}' and company_id='{$this->company_id}' ",$data);
//
//		 $this->error= 0;
//		 $this->errortip = "更新标签成功";
//		 return  true;
//	}


}