<?php


namespace Model\Smc;

class OrderTagModel extends OrderModel
{
    function __construct($publicarray=array(),$order_pid=0) {
        parent::__construct ($publicarray,$order_pid);
    }
    function getChooseTagList()
    {
        $sql = "select o.ordertag_name,ordertag_id from smc_code_ordertag  as o where  company_id='{$this->company_id}' and school_id='{$this->school_id}}' and ordertag_isopen = 1 ";
        $tagList = $this->DataControl->selectClear($sql);

        if (!$tagList) {
            $tagList = array();
        }

        return $tagList ;
    }

    function getOrderTagList($request)
    {

        $datawhere = "1";
        $datawhere .= " and company_id='{$this->company_id}' and school_id='{$this->school_id}' ";

        if(isset($request['keyword']) && $request['keyword'] !=='' ){
            $datawhere .= " and ordertag_name like %{$request['keyword']}%";
        }
        $sql  = "select ordertag_name,ordertag_id,ordertag_isopen,
					(select count(ordertag_id) from smc_code_ordertag  where  ordertag_isopen = 1 and company_id='{$this->company_id}' and school_id='{$this->school_id}') as isopen_num,
					(select count(ordertag_id) from smc_code_ordertag where  company_id='{$this->company_id}' and school_id='{$this->school_id}') as is_num
					from smc_code_ordertag where  {$datawhere} ";

        $tagList = $this->DataControl->selectClear($sql);
        if (!$tagList) {
            $tagList = array();
        }

        return $tagList ;

    }

    function  insertTag($ordertag_name)
    {		$data = array();
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['ordertag_name'] = $ordertag_name;
        $data['ordertag_isopen'] = 1;

        if( $id =$this->DataControl->insertData('smc_code_ordertag',$data))
        {
            $this->error = 0 ;
            $this->errortip = "新增成功" ;
            return  true;
        }else{
            $this->error = 1 ;
            $this->errortip = "新增失败" ;
            return false;
        }

    }

    function updateTagStatus($orertag_id)
    {
        $tagOne = $this->DataControl->getOne('smc_code_ordertag',"ordertag_id='{$orertag_id}'");
        if($tagOne) {
            $data= array();
            if($tagOne['ordertag_isopen'] ==1){
                $data['ordertag_isopen'] =0;
            }else{
                $data['ordertag_isopen'] =1;
            }
            $this->DataControl->updateData('smc_code_ordertag',"ordertag_id='{$orertag_id}'",$data);
            $this->error = 0;
            $this->errortip = "更新成功";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "查无数据";
            return false;
        }
    }
}