<?php


namespace Model\Smc;

use Model\Api\DataScreenModel;

class AdjustmentModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getDateHourList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['hour_day']) && $request['hour_day'] != '') {
            $datawhere .= " and h.hour_day='{$request['hour_day']}'";
        }

        if (isset($request['classroom_id']) && $request['classroom_id'] != '') {
            $datawhere .= " and h.classroom_id='{$request['classroom_id']}'";
        }

        if (isset($request['teacher_staffer_id']) && $request['teacher_staffer_id'] != '') {
            $datawhere .= " and t.staffer_id='{$request['teacher_staffer_id']}'";
        }

        $sql = "select h.hour_starttime,h.hour_endtime,c.class_cnname
                ,concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) as staffer_cnname
                ,cl.classroom_cnname
                from smc_class_hour_teaching as t
                left join smc_class_hour as h on h.hour_id = t.hour_id
                left join smc_class as c on c.class_id = t.class_id
                left join smc_staffer as s on s.staffer_id = t.staffer_id
                left join smc_classroom as cl on cl.classroom_id=h.classroom_id
                where {$datawhere} 
                and t.teaching_isdel=0
                and t.teaching_type=0 
                and h.hour_id<>'{$request['hour_id']}' 
                and c.school_id='{$this->school_id}'";

        $infoList = $this->DataControl->selectClear($sql);

        if (!$infoList) {
            $this->error = true;
            $this->errortip = "无上课课时";
            return false;
        }

        foreach ($infoList as &$infoOne) {
            $infoOne['time'] = $infoOne['hour_starttime'] . '-' . $infoOne['hour_endtime'];
        }

        return $infoList;

    }

    function getHourStafferList($request)
    {
        $datawhere = "p.school_id ='{$this->school_id}' and cp.post_isteaching = 1 and p.postbe_status = 1 and cp.post_type = 1 and s.staffer_leave = 0";
        if (isset($request['teachtype_code']) && $request['teachtype_code'] != '') {
            $teachtype = $this->DataControl->getFieldOne("smc_code_teachtype", "teachtype_native", "company_id='{$request['company_id']}' and teachtype_code='{$request['teachtype_code']}'");
            if (isset($teachtype['teachtype_native']) && $teachtype['teachtype_native'] !== '') {
                $datawhere .= " and s.staffer_native in ({$teachtype['teachtype_native']})";
            }
        }

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "hour_id='{$request['hour_id']}'");

        $hour_day = date('Y-m-d', strtotime($hourOne['hour_day']));

        $hour_starttime = $request['hour_starttime'];
        $hour_endtime = $request['hour_endtime'];
        $week_month = (date("m", strtotime($hour_day)));
        $week_day = (date("d", strtotime($hour_day)));
        $week_week = (date("w", strtotime($hour_day)));
        $startTime = mktime(0, 0, 0, $week_month, $week_day - $week_week + 7 - 6, date("Y"));
        $endTime = mktime(0, 0, 0, $week_month, $week_day - $week_week + 7, date("Y"));

        $startWeekDay = date('Y-m-d', $startTime);
        $endWeekDay = date('Y-m-d', $endTime);

        $sql = "select s.staffer_id,cp.post_name,si.info_isforeign,concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) as staffer_cnname
	            ,(select count(h.hour_id) from smc_class_hour_teaching as ht left join smc_class_hour as h on ht.hour_id = h.hour_id left join smc_class as c on c.class_id = h.class_id 
	                where ht.staffer_id = s.staffer_id and ht.teaching_isdel=0 and h.hour_day >='{$startWeekDay}' and h.hour_day <='{$endWeekDay}' and c.school_id ='{$request['school_id']}' limit 0,1 ) as hour_num
	            ,ifnull((select h.hour_id from smc_class_hour_teaching as ht left join smc_class_hour as h on ht.hour_id = h.hour_id left join smc_class as c on c.class_id = h.class_id 
	                where s.staffer_id = ht.staffer_id and ht.teaching_isdel=0 and h.hour_day = '{$hour_day}' and h.hour_starttime <='{$hour_endtime}' and h.hour_endtime >='{$hour_starttime}' and c.school_id ='{$request['school_id']}' limit 0,1 ),0) as hour_isbusy
                from gmc_staffer_postbe as p 
			    left join gmc_company_post as cp on cp.post_id =p.post_id
			    left join smc_staffer_info as si on si.staffer_id = p.staffer_id
                left join smc_staffer as s on s.staffer_id=p.staffer_id
			    where {$datawhere}
			    group by s.staffer_id";

        $stafferList = $this->DataControl->selectClear($sql);

        if (!$stafferList) {
            $this->error = true;
            $this->errortip = "无上课课时";
            return false;
        }

        foreach ($stafferList as &$value) {
            $value['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];

            $value['staffer_status'] = $value['hour_isbusy'] ? 1 : 0;

            if ($value['info_isforeign'] != 0) {
                $value['info_isforeign'] = $this->LgStringSwitch('[外籍]' . "教师");
            } else {
                $value['info_isforeign'] = $this->LgStringSwitch('[中籍]' . "教师");
            }
        }

        return $stafferList;

    }

    function getHourRoomList($request)
    {

        $datawhere = '1';
        $hourOne = $this->DataControl->getOne("smc_class_hour", "hour_id='{$request['hour_id']}'");
        if ($hourOne) {
            $datawhere .= " and c.classroom_id <>'{$hourOne['classroom_id']}' ";
        }

        $week_month = (date("m", strtotime($hourOne['hour_day'])));
        $week_day = (date("d", strtotime($hourOne['hour_day'])));
        $week_week = (date("w", strtotime($hourOne['hour_day'])));
        $startTime = mktime(0, 0, 0, $week_month, $week_day - $week_week + 7 - 6, date("Y"));
        $endTime = mktime(0, 0, 0, $week_month, $week_day - $week_week + 7, date("Y"));

        $startWeekDay = date('Y-m-d', $startTime);
        $endWeekDay = date('Y-m-d', $endTime);

        $sql = "select c.classroom_id,c.classroom_cnname,c.classroom_maxnums
			    ,(select  count(h.hour_id) from smc_class_hour as h where h.classroom_id = c.classroom_id and h.hour_day >= '{$startWeekDay}' and h.hour_day<='{$endWeekDay}' limit 0,1 ) as hour_num
	  			,(select h.hour_id from smc_class_hour as h,smc_class as cs where h.classroom_id = c.classroom_id  and h.hour_day = '{$hourOne['hour_day']}' and h.hour_starttime <='{$hourOne['hour_endtime']}' and h.hour_endtime >='{$hourOne['hour_starttime']}' and cs.class_id=h.class_id and cs.class_status <> '-2' and h.hour_ischecking <> '-1' limit 0,1) as hour_isbusy
			    from smc_classroom as c
			    where {$datawhere} and c.school_id ='{$this->school_id}' and c.classroom_status='1'";

        $classroomList = $this->DataControl->selectClear($sql);

        if (!$classroomList) {
            $this->error = true;
            $this->errortip = "无上课课时";
            return false;
        }

        foreach ($classroomList as &$value) {
            $value['classroom_status'] = $value['hour_isbusy'] ? 1 : 0;
        }

        return $classroomList;
    }


    function getConflictList($request)
    {

        //0-教师  教室调整   1-时间调整

//        $hourOne=$this->DataControl->getOne("smc_class_hour","hour_id='{$request['hour_id']}'");

        if ($request['type'] == 1) {
            $hourList = $this->checkConflict($request['hour_id'], $request['z_staffer_id'], $request['f_staffer_id'], $request['classroom_id']);
        } else {
            $hourList = $this->checkConflict($request['hour_id'], 0, 0, 0, $request['hour_day'], $request['starttime'], $request['endtime']);
        }

        if (!$hourList) {
            $this->error = true;
            $this->errortip = "无冲突课时";
            return false;
        }

        return $hourList;
    }

    function checkConflict($hour_id, $z_staffer_id = 0, $f_staffer_id = 0, $classroom_id = 0, $hour_day = '', $starttime = '', $endtime = '')
    {
        $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.classroom_id
              ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),0) as z_staffer_id
              ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=1 and t.teaching_isdel=0 limit 0,1),0) as f_staffer_id
              from smc_class_hour as ch 
              where ch.hour_id='{$hour_id}' limit 0,1";
        $yhourOne = $this->DataControl->selectOne($sql);

        if ($hour_day != '' && $starttime != '' && $endtime != '') {

            $sql = "select c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,cl.classroom_id,cl.classroom_cnname,co.course_branch,co.course_cnname
                ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),0) as z_staffer_id
                ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=1 and t.teaching_isdel=0 limit 0,1),0) as f_staffer_id
                ,ifnull((select concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) from smc_class_hour_teaching as t,smc_staffer as s where t.staffer_id=s.staffer_id and t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),'--') as z_staffer_cnname
                ,ifnull((select concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) from smc_class_hour_teaching as t,smc_staffer as s where t.staffer_id=s.staffer_id and t.hour_id=ch.hour_id and t.teaching_type=1 and t.teaching_isdel=0 limit 0,1),'--') as f_staffer_cnname
 				from smc_class_hour as ch
				left join smc_class as c on c.class_id =ch.class_id
 				left join smc_course as co on co.course_id=c.course_id    
				left join smc_classroom as cl on cl.classroom_id = ch.classroom_id
				where ch.hour_day='{$hour_day}' and ch.hour_id <>'{$hour_id}' and c.school_id ='{$this->school_id}'
                having (z_staffer_id='{$yhourOne['z_staffer_id']}' or f_staffer_id='{$yhourOne['f_staffer_id']}' or classroom_id='{$yhourOne['classroom_id']}')
				";
        } else {
            $sql = "select c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,cl.classroom_id,cl.classroom_cnname,co.course_branch,co.course_cnname
                ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),0) as z_staffer_id
                ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=1 and t.teaching_isdel=0 limit 0,1),0) as f_staffer_id
                ,ifnull((select concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) from smc_class_hour_teaching as t,smc_staffer as s where t.staffer_id=s.staffer_id and t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),'--') as z_staffer_cnname
                ,ifnull((select concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) from smc_class_hour_teaching as t,smc_staffer as s where t.staffer_id=s.staffer_id and t.hour_id=ch.hour_id and t.teaching_type=1 and t.teaching_isdel=0 limit 0,1),'--') as f_staffer_cnname
 				from smc_class_hour as ch
				left join smc_class as c on c.class_id =ch.class_id
 				left join smc_course as co on co.course_id=c.course_id  
				left join smc_classroom as cl on cl.classroom_id = ch.classroom_id
				where ch.hour_day='{$yhourOne['hour_day']}' and ch.hour_id <>'{$hour_id}' and c.school_id ='{$this->school_id}' 
				having (z_staffer_id='{$z_staffer_id}' or f_staffer_id='{$f_staffer_id}' or classroom_id='{$classroom_id}') 
				";
        }

        $hourList = $this->DataControl->selectClear($sql);
        $data = array();

        if ($hourList) {
            foreach ($hourList as $hourOne) {
                $hourOne['time'] = $hourOne['hour_starttime'] . '~' . $hourOne['hour_endtime'];
                if ($hour_day != '' && $starttime != '' && $endtime != '') {
                    $is_cross = $this->is_time_cross(strtotime($hourOne['hour_day'] . ' ' . $starttime), strtotime($hourOne['hour_day'] . ' ' . $endtime), strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']), strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']));
                } else {
                    $is_cross = $this->is_time_cross(strtotime($hourOne['hour_day'] . ' ' . $yhourOne['hour_starttime']), strtotime($hourOne['hour_day'] . ' ' . $yhourOne['hour_endtime']), strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']), strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']));
                }
                $status = 0;
                if ($is_cross) {
                    if ($hour_day != '' && $starttime != '' && $endtime != '') {
                        if ($yhourOne['classroom_id'] == $hourOne['classroom_id'] && $yhourOne['classroom_id'] > 0) {
                            $hourOne['c_cross'] = 1;//教室冲突
                            $status = 1;
                        }

                        if ($yhourOne['z_staffer_id'] == $hourOne['z_staffer_id'] && $hourOne['z_staffer_id'] > 0) {
                            $hourOne['z_cross'] = 1;//主教冲突
                            $status = 1;
                        }

                        if ($yhourOne['f_staffer_id'] == $hourOne['f_staffer_id'] && $hourOne['f_staffer_id'] > 0) {
                            $hourOne['f_cross'] = 1;//助教冲突
                            $status = 1;
                        }
                    } else {
                        if ($classroom_id == $hourOne['classroom_id'] && $yhourOne['classroom_id'] > 0) {
                            $hourOne['c_cross'] = 1;//教室冲突
                            $status = 1;
                        }

                        if ($z_staffer_id == $hourOne['z_staffer_id'] && $hourOne['z_staffer_id'] > 0) {
                            $hourOne['z_cross'] = 1;//主教冲突
                            $status = 1;
                        }

                        if ($f_staffer_id == $hourOne['f_staffer_id'] && $hourOne['f_staffer_id'] > 0) {
                            $hourOne['f_cross'] = 1;//助教冲突
                            $status = 1;
                        }
                    }

                    if ($status == 1) {
                        $data[] = $hourOne;
                    }
                }
            }
        }

        return $data ? $data : array();
    }

    function adjustCourse($request)
    {
        $hourOne = $this->DataControl->getOne('smc_class_hour', "hour_id='{$request['hour_id']}'");
        $classOne = $this->DataControl->getOne('smc_class', "class_id='{$hourOne['class_id']}'");

        $sql = "select a.application_id from smc_forward_application as a where a.class_id='{$classOne['class_id']}' and a.application_status=0 and a.out_class_date>=curdate() limit 0,1";
        $forwardOne = $this->DataControl->selectOne($sql);
        if($forwardOne){
            $this->error = 1;
            $this->errortip = "班级有学员在此期间申请结转，不可操作调课";
            return false;
        }

        
        $sql = "select a.task_id from smc_forward_application_task as a,smc_forward_application as b where a.application_id=b.application_id and b.class_id='{$classOne['class_id']}' and a.task_type in ('auto_forward','auto_cancel') and a.task_status=0 and b.out_class_date>=curdate() limit 0,1";
        $forwardTaskOne = $this->DataControl->selectOne($sql);
        if($forwardTaskOne){
            $this->error = 1;
            $this->errortip = "班级有学员在此期间申请结转，不可操作调课";
            return false;
        }

        

        $courseOne = $this->DataControl->selectOne("select course_weeklimittime*course_islimittime as course_weeklimittime,course_limitopencyclenum,course_islimitopencycle,course_inclasstype,course_islimitopencycle,course_limitopencyclenum  from smc_course where course_id = '{$classOne['course_id']}' limit 0,1");

        if ($hourOne['hour_ischecking'] == -1) {
            $this->error = 1;
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "该课时已上课";
            return false;
        }

        if (isset($request['z_staffer_id']) && $request['z_staffer_id'] > 0 && isset($request['f_staffer_id']) && $request['f_staffer_id'] > 0) {
            if ($request['z_staffer_id'] == $request['f_staffer_id']) {
                $this->error = 1;
                $this->errortip = "主教与助教不能相同";
                return false;
            }
        }

        // $fanweiModel = new \Model\Smc\FanweiModel();

        $schoolOne = $this->DataControl->getFieldOne('smc_school','school_isskipFw', "school_id='{$classOne['school_id']}'");

        if ($request['type'] == 0) {
            if (!empty($request['hour_day'])) {
                $hour_day = date('Y-m-d', strtotime($request['hour_day']));
            } else {
                $this->error = 1;
                $this->errortip = "请选择调课日期";
                return false;
            }
            if ($classOne['class_stdate'] > $hour_day) {
                $this->error = 1;
                $this->errortip = "请勿调整到开班时间之前";
                return false;
            }

            if($courseOne['course_islimitopencycle']==1 && $schoolOne['school_isskipFw']==0){

                $sql = "select a.hour_day 
                    from smc_class_hour as a 
                    where a.class_id='{$hourOne['class_id']}' and a.hour_ischecking<>-1 and a.hour_isfree=0
                    order by a.hour_day asc,a.hour_starttime asc
                    limit 0,1
                    ";
                $hourstudyOne=$this->DataControl->selectOne($sql);

                if($hourstudyOne){
                    $startTimestamp = strtotime($hourstudyOne['hour_day']);
                }else{
                    $startTimestamp = strtotime($classOne['class_stdate']);
                }

                $endTimestamp = strtotime($hour_day);

                $betweendays = abs(($endTimestamp - $startTimestamp) / (60 * 60 * 24));

                if($betweendays>$courseOne['course_limitopencyclenum']){
                    $this->errortip = '排课周期不能大于'.$courseOne['course_limitopencyclenum'].'天';
                    $this->error = "1";
                    return false;
                }
            }
            //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
            $first = 1;
            //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
            $hour_day = $request['hour_day'];
            $w = date('w', strtotime($request['hour_day']));
            //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
            $week_start = date('Y-m-d', strtotime("$hour_day -" . ($w ? $w - $first : 6) . ' days'));
            //本周结束日期
            $week_end = date('Y-m-d', strtotime("$week_start +6 days"));

            $weekTotal = 0;
            $sql = "select hour_starttime,hour_endtime
            from smc_class_hour 
            where class_id='{$hourOne['class_id']}'
            and hour_day>='{$week_start}'
            and hour_day<='{$week_end}'
            and hour_ischecking>=0
            and hour_isfree=0
            and hour_id<>'{$request['hour_id']}'";
            $hourWeek = $this->DataControl->selectClear($sql);
            if ($hourWeek) {
                foreach ($hourWeek as $eachHour) {
                    $weekTotal += (strtotime($eachHour['hour_endtime']) - strtotime($eachHour['hour_starttime'])) / 60;
                }
            }

            $weekTotal += (strtotime($request['endtime']) - strtotime($request['starttime'])) / 60;

            if ($courseOne['course_weeklimittime'] > 0 && $classOne['class_type'] == '0' && $weekTotal > $courseOne['course_weeklimittime']) {
                $this->errortip = "周排课总分钟数大于课程设置的最大值" . $courseOne['course_weeklimittime'] . "min！";
                $this->error = "1";
                return false;
            }

            //不可以调整到已考勤的课次前面
            $checkHourOne = $this->DataControl->selectOne("select  hour_day,hour_endtime,hour_starttime from smc_class_hour as ch where ch.class_id = '{$hourOne['class_id']}' and hour_ischecking =1  order by hour_lessontimes  DESC limit 0,1");

            if ($checkHourOne) {
                if ($request['hour_day'] < $checkHourOne['hour_day']) {
                    $this->error = 1;
                    $this->errortip = "请勿调整到已考勤的课次之前";
                    return false;
                }
                if ($request['hour_day'] == $checkHourOne['hour_day']) {
                    if (($request['hour_day'] . ' ' . $request['starttime']) < ($request['hour_day'] . ' ' . $checkHourOne['hour_endtime'])) {
                        $this->error = 1;
                        $this->errortip = "上课时间与该天已考勤的上课时间冲突";
                        return false;
                    }
                }
            }

            $existsOne = $this->DataControl->selectOne("select hour_id,hour_lessontimes 
                from smc_class_hour  
                where class_id = '{$hourOne['class_id']}' 
                and hour_ischecking>=0 
                and hour_day='{$hour_day}'
                and hour_id<>'{$request['hour_id']}' 
                and ((hour_starttime >= replace('{$request['starttime']}',' ','') and hour_starttime < replace('{$request['endtime']}',' ',''))
                or (hour_starttime <= replace('{$request['starttime']}',' ','') and hour_endtime > replace('{$request['starttime']}',' ','')))
                order by hour_lessontimes  DESC limit 0,1");

            if ($existsOne) {
                $this->error = 1;
                $this->errortip = "上课时间已与已设定好的课程时间重复，请重新设定";
                return false;
            }

            $dataOne = $this->DataControl->selectOne("
			    select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day
			    ,h.hour_ischecking,h.hour_starttime,h.hour_endtime,h.hour_isfree
			    ,cl.classroom_id,cl.classroom_cnname,s.staffer_id,h.hour_way,h.hour_number
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}' 
				limit 0,1 ");

            if (!$dataOne) {
                $this->error = 1;
                $this->errortip = "查无数据";
                $this->conflict = 0;
                return false;
            }

            $data = array();
            $data['hour_day'] = $hour_day;
            $data['hour_starttime'] = $request['starttime'];
            $data['hour_endtime'] = $request['endtime'];
            if (!$data['hour_day']) {
                $this->error = 1;
                $this->errortip = "请选择日期";
                $this->conflict = 0;
                return false;
            }

            if($courseOne['course_islimitopencycle']==1 && $schoolOne['school_isskipFw']==0){
                $sql = "select a.hour_day 
                    from smc_class_hour as a 
                    where a.class_id='{$dataOne['class_id']}' and a.hour_ischecking<>-1
                    order by a.hour_day asc,a.hour_starttime asc
                    limit 0,1
                    ";
                $hourstudyOne=$this->DataControl->selectOne($sql);

                if($hourstudyOne && date('Y-m-d', strtotime('+'.$courseOne['course_limitopencyclenum'].' day',strtotime($hourstudyOne['hour_day'])))<$hour_day){
                    $this->error = 1;
                    $this->errortip = "排课周期不可高于".$courseOne['course_limitopencyclenum']."天";
                    return false;
                }

            }

           $sql = "select x.hourstudy_id
                   from smc_student_hourstudy as x,smc_class_hour as y
                   where x.hour_id=y.hour_id and x.class_id='{$hourOne['class_id']}' and y.hour_isfree=0 limit 0,1";

            if((!isset($request['is_skip']) || $request['is_skip']=='' || $request['is_skip']==0) && $hour_day!=$dataOne['hour_day'] && $schoolOne['school_isskipFw']==0 && $this->DataControl->selectOne($sql)){

                $companyOne=$this->DataControl->getFieldOne("gmc_company","company_canadjustcourse","company_id='{$request['company_id']}'");

                if($companyOne['company_canadjustcourse']==0 && $courseOne['course_inclasstype']==0){

                    if(!$this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$request['school_id']}' and adjustapply_day='{$dataOne['hour_day']}' and class_id=0 and hour_id=0 and adjustapply_status=1")){

                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and adjustapply_type=0 and class_id='{$dataOne['class_id']}' and hour_day<>'{$dataOne['hour_day']}' and adjustapply_status=0")){
                            $this->error = true;
                            $this->errortip = "班级存在不同日期未完成调课申请,不可再次申请";
                            return false;
                        }

                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and adjustapply_type=0 and class_id='{$dataOne['class_id']}' and hour_id='{$request['hour_id']}' and adjustapply_status=0")){
                            $this->error = true;
                            $this->errortip = "存在未完成班级调课申请,不可再次申请";
                            return false;
                        }

                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and adjustapply_type=1 and adjustapply_day='{$dataOne['hour_day']}' and adjustapply_status=0")){
                            $this->error = true;
                            $this->errortip = "存在未完成全校调课申请,不可再次申请";
                            return false;
                        }


                        $data=array();
                        $data['company_id']=$request['company_id'];
                        $data['school_id']=$request['school_id'];
                        $data['class_id']=$dataOne['class_id'];
                        $data['hour_id']=$request['hour_id'];
                        $data['hour_day']=$dataOne['hour_day'];
                        $data['adjustapply_class']=$request['adjustapply_class'];
                        $data['adjustapply_fileurl']=$request['adjustapply_fileurl'];
                        $data['adjustapply_day']=$hour_day;
                        $data['adjustapply_starttime']=$request['starttime'];
                        $data['adjustapply_endtime']=$request['endtime'];
                        $data['adjustapply_reason']=$request['adjustapply_reason'];
                        $data['is_need_process']=1;
                        
                        $data['staffer_id']=$request['staffer_id'];
                        $data['adjustapply_createtime']=time();

                        if($adjustapply_id=$this->DataControl->insertData("smc_class_hour_adjustapply",$data)){

                            //调用泛微创建流

//                            $fanweiModel->createNewAdjustApply($adjustapply_id);

                            $this->oktip="申请成功";
                            return true;
                        }else{
                            $this->error = 1;
                            $this->errortip = "申请失败";
                            return false;
                        }
                    }
                }
            }

            $data['hour_updatatime'] = time();
            if ($this->DataControl->updateData('smc_class_hour', "hour_id='{$request['hour_id']}'", $data)) {
                // 同时更改网课的时间
                if ($dataOne['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = strtotime($data['hour_day'] . ' ' . $data['hour_starttime']);
                    $lineData['linerooms_endtime'] = strtotime($data['hour_day'] . ' ' . $data['hour_endtime']);
                    $lineData['linerooms_issync'] = "0";
                    $this->DataControl->updateData("smc_linerooms", "class_id='{$dataOne['class_id']}' and hour_id='{$dataOne['hour_id']}'", $lineData);
                }

                $this->addClientTrack($request['hour_id'], '调整上课日期,取消试听');

                $classEnd = $this->DataControl->getFieldOne('smc_class', "class_id,class_enddate,course_id", "class_id='{$hourOne['class_id']}'");
                $course = $this->DataControl->getFieldOne('smc_course', "course_cnname", "course_id='{$classOne['course_id']}'");
                $arr_last_day = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}'", "order by hour_day DESC");
                $last_day = $arr_last_day['hour_day'];
                if ($classEnd['class_enddate'] <> $last_day) {
                    $json = array();
                    $json['class_enddate'] = $last_day;
                    $json['class_updatatime'] = time();
                    if ($this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $json)) {
                        $studyData = array();
                        $studyData['study_endday'] = $last_day;
                        $studyData['study_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_study", "study_isreading =1 and class_id='{$hourOne['class_id']}'", $studyData);
                    }
                }
                if ($hourOne['hour_day'] > $hour_day) {
                    $tempyday = $hourOne['hour_day'];
                    $hourOne['hour_day'] = $hour_day;
                    $hour_day = $tempyday;
                }
                if ($hourOne['hour_iswarming'] == 0) {
                    if (isset($request['is_clear']) && $request['is_clear'] == 0) {
                        $clear = 0;
                    } else {
                        $clear = 1;
                    }
                    if ($hourOne['hour_way'] == 0) {
                        $hourOne['hour_number'] = 0;
                    }
                    $this->adjustLessonDay($hourOne['class_id'], $request['hour_id'], $clear, $hourOne['hour_number']);
                }


                $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss 
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    left join smc_parenter_wxchattoken as w on w.parenter_id = f.parenter_id and w.company_id = s.company_id and w.parenter_wxtoken is NOT NULL
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}' 
                        AND ss.study_isreading = '1'");

                $class_cnname = $this->DataControl->selectOne("
                    SELECT
                        h.class_id,
                        c.class_cnname
                    FROM
                        smc_class_hour AS h
                        left join smc_class as c on h.class_id= c.class_id
                        WHERE h.hour_id = '{$request['hour_id']}'");

                foreach ($parenter as &$val) {
                    $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");
                    $school_cnname = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$request['school_id']}'");

                    $coursetime = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_time", "course_id =     '{$classOne['course_id']}' and student_id = '{$val['student_id']}' and school_id = '{$request['school_id']}'");

                    $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程变动提醒'");
                    if ($isset) {
                        $wxid = $isset['masterplate_wxid'];
                    } else {
                        $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程变动提醒'");
                        $wxid = $masterplate['masterplate_wxid'];
                    }

                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，本课程的课表有所调整，请及时查看。');
                    $b = $school_cnname['school_cnname'];
                    $c = $class_cnname['class_cnname'];
                    $d = '您' . $course['course_cnname'] . '课程剩余' . $coursetime['coursebalance_time'] . '次未上课信息有调整';
                    $e = '点击可查看课表详情哦~';
                    $f = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->ChangeHour($a, $b, $c, $d, $e, $f, $wxid);
                }

                $this->error = 0;
                $this->conflict = 0;
                $this->oktip = "调整成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "调整失败";
                $this->conflict = 0;
                return false;
            }
        }
        elseif($request['type'] == 1) {
            $dataOne = $this->DataControl->selectOne("
			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type =0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}'");

            if (!$dataOne) {
                $this->error = 1;
                $this->errortip = "查无数据";
                $this->conflict = 0;
                return false;
            }

            if (isset($request['z_staffer_id']) && $request['z_staffer_id'] != '') {
                $updatedata = array();
                $updatedata['staffer_id'] = $request['z_staffer_id'];
                $updatedata['teaching_updatatime'] = time();
                $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching 
                    WHERE hour_id='{$request['hour_id']}' and teaching_type='0' limit 0,1 ");

                if ($teachingOne) {
                    $updatedata['teachtype_code'] = $request['z_teachtype_code'];
                    $teachingID = $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='0' ", $updatedata);
                } else {
                    $classhourOne = $this->DataControl->selectOne("select h.class_id,h.hour_lessontimes
                        from smc_class_hour as h WHERE hour_id='{$request['hour_id']}' limit 0,1 ");

                    $datatea = array();
                    $datatea['class_id'] = $classhourOne['class_id'];
                    $datatea['hour_lessontimes'] = $classhourOne['hour_lessontimes'];

                    $datatea['teachtype_code'] = $request['z_teachtype_code'];
                    $datatea['hour_id'] = $request['hour_id'];
                    $datatea['staffer_id'] = $request['z_staffer_id'];
                    $datatea['hour_former_staffer_id'] = 0;
                    $datatea['teaching_ischecking'] = 0;
                    $datatea['teaching_type'] = 0;
                    $datatea['teaching_isdel'] = 0;
                    $datatea['teaching_createtime'] = time();
                    $teachingID = $this->DataControl->insertData("smc_class_hour_teaching", $datatea);
                }
            }

            if (isset($request['f_staffer_id']) && $request['f_staffer_id'] != '') {
                $updatedata = array();
                $updatedata['staffer_id'] = $request['f_staffer_id'];
                $updatedata['teaching_updatatime'] = time();
                $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching WHERE hour_id='{$request['hour_id']}' and teaching_type='1' limit 0,1 ");

                if ($teachingOne) {
                    $updatedata['teaching_isdel'] = 0;
                    $updatedata['teachtype_code'] = $request['f_teachtype_code'];

                    $teachingID = $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='1' and teaching_isdel=0", $updatedata);
                } else {
                    $classhourOne = $this->DataControl->selectOne("select h.class_id,h.hour_lessontimes  from smc_class_hour as h  
                        WHERE hour_id='{$request['hour_id']}' limit 0,1 ");

                    $datatea = array();
                    $datatea['class_id'] = $classhourOne['class_id'];
                    $datatea['hour_lessontimes'] = $classhourOne['hour_lessontimes'];
                    $datatea['teachtype_code'] = $request['f_teachtype_code'];
                    $datatea['hour_id'] = $request['hour_id'];
                    $datatea['staffer_id'] = $request['f_staffer_id'];
                    $datatea['hour_former_staffer_id'] = 0;
                    $datatea['teaching_ischecking'] = 0;
                    $datatea['teaching_type'] = 1;
                    $datatea['teaching_isdel'] = 0;
                    $datatea['teaching_createtime'] = time();
                    $teachingID = $this->DataControl->insertData("smc_class_hour_teaching", $datatea);
                }
            } else {
                $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching WHERE hour_id='{$request['hour_id']}' and teaching_type='1' limit 0,1 ");

                if ($teachingOne) {
                    $data = array();
                    $data['teaching_isdel'] = 1;
                    $data['teaching_updatatime'] = time();
                    $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='1' and teaching_isdel=0", $data);
                }
            }

            if (isset($request['classroom_id']) && $request['classroom_id'] != '') {
                $data = array();
                $data['classroom_id'] = $request['classroom_id'];
                $data['hour_updatatime'] = time();
                $this->DataControl->updateData("smc_class_hour", "hour_id='{$request['hour_id']}'", $data);
            }

            if ($teachingID) {
                $this->error = 0;
                $this->oktip = "调整成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "调整失败";
                return false;
            }
        }
        elseif($request['type'] == 2){


            $dataOne = $this->DataControl->selectOne("
			    select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day
			    ,h.hour_ischecking,h.hour_starttime,h.hour_endtime
			    ,cl.classroom_id,cl.classroom_cnname,s.staffer_id,h.hour_way,h.hour_number
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}' 
				limit 0,1 ");

            if (!$dataOne) {
                $this->error = 1;
                $this->errortip = "查无数据";
                $this->conflict = 0;
                return false;
            }

            $hour_day = date('Y-m-d', strtotime($request['hour_day']?$request['hour_day']:$dataOne['hour_day']));

            if ($classOne['class_stdate'] > $hour_day) {
                $this->error = 1;
                $this->errortip = "请勿调整到开班时间之前";
                return false;
            }

            if($courseOne['course_islimitopencycle']==1 && $schoolOne['school_isskipFw']==0){

                $sql = "select a.hour_day 
                    from smc_class_hour as a 
                    where a.class_id='{$hourOne['class_id']}' and a.hour_ischecking<>-1 and a.hour_isfree=0
                    order by a.hour_day asc,a.hour_starttime asc
                    limit 0,1
                    ";
                $hourstudyOne=$this->DataControl->selectOne($sql);

                if($hourstudyOne){
                    $startTimestamp = strtotime($hourstudyOne['hour_day']);
                }else{
                    $startTimestamp = strtotime($classOne['class_stdate']);
                }


                $endTimestamp = strtotime($hour_day);

                $betweendays = abs(($endTimestamp - $startTimestamp) / (60 * 60 * 24));

                if($betweendays>$courseOne['course_limitopencyclenum']){
                    $this->errortip = '排课周期不能大于'.$courseOne['course_limitopencyclenum'].'天';
                    $this->error = "1";
                    return false;
                }
            }


            //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
            $first = 1;
            //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
            $hour_day = $request['hour_day'];
            $w = date('w', strtotime($request['hour_day']));
            //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
            $week_start = date('Y-m-d', strtotime("$hour_day -" . ($w ? $w - $first : 6) . ' days'));
            //本周结束日期
            $week_end = date('Y-m-d', strtotime("$week_start +6 days"));

            $weekTotal = 0;
            $sql = "select hour_starttime,hour_endtime
            from smc_class_hour 
            where class_id='{$hourOne['class_id']}'
            and hour_day>='{$week_start}'
            and hour_day<='{$week_end}'
            and hour_ischecking>=0
            and hour_isfree=0
            and hour_id<>'{$request['hour_id']}'";
            $hourWeek = $this->DataControl->selectClear($sql);
            if ($hourWeek) {
                foreach ($hourWeek as $eachHour) {
                    $weekTotal += (strtotime($eachHour['hour_endtime']) - strtotime($eachHour['hour_starttime'])) / 60;
                }
            }

            $weekTotal += (strtotime($request['endtime']) - strtotime($request['starttime'])) / 60;

            if ($courseOne['course_weeklimittime'] > 0 && $classOne['class_type'] == '0' && $weekTotal > $courseOne['course_weeklimittime']) {
                $this->errortip = "周排课总分钟数大于课程设置的最大值" . $courseOne['course_weeklimittime'] . "min！";
                $this->error = "1";
                return false;
            }

            //不可以调整到已考勤的课次前面
            $checkHourOne = $this->DataControl->selectOne("select  hour_day,hour_endtime,hour_starttime from smc_class_hour as ch where ch.class_id = '{$hourOne['class_id']}' and hour_ischecking =1  order by hour_lessontimes  DESC limit 0,1");

            if ($checkHourOne) {
                if ($request['hour_day'] < $checkHourOne['hour_day']) {
                    $this->error = 1;
                    $this->errortip = "请勿调整到已考勤的课次之前";
                    return false;
                }
                if ($request['hour_day'] == $checkHourOne['hour_day']) {
                    if (($request['hour_day'] . ' ' . $request['starttime']) < ($request['hour_day'] . ' ' . $checkHourOne['hour_endtime'])) {
                        $this->error = 1;
                        $this->errortip = "上课时间与该天已考勤的上课时间冲突";
                        return false;
                    }
                }
            }

            $existsOne = $this->DataControl->selectOne("select hour_id,hour_lessontimes 
                from smc_class_hour  
                where class_id = '{$hourOne['class_id']}' 
                and hour_ischecking>=0 
                and hour_day='{$hour_day}'
                and hour_id<>'{$request['hour_id']}' 
                and ((hour_starttime >= replace('{$request['starttime']}',' ','') and hour_starttime < replace('{$request['endtime']}',' ',''))
                or (hour_starttime <= replace('{$request['starttime']}',' ','') and hour_endtime > replace('{$request['starttime']}',' ','')))
                order by hour_lessontimes  DESC limit 0,1");

            if ($existsOne) {
                $this->error = 1;
                $this->errortip = "上课时间已与已设定好的课程时间重复，请重新设定";
                return false;
            }

            $data = array();
            $data['hour_day'] = $hour_day;
            $data['hour_starttime'] = $request['starttime'];
            $data['hour_endtime'] = $request['endtime'];
            if (!$data['hour_day']) {
                $this->error = 1;
                $this->errortip = "请选择日期";
                $this->conflict = 0;
                return false;
            }

//            $sql = "select x.hourstudy_id
//                    from smc_student_hourstudy as x,smc_class_hour as y
//                    where x.hour_id=y.hour_id and x.class_id='{$hourOne['class_id']}' and y.hour_isfree=0 limit 0,1";

            if((!isset($request['is_skip']) || $request['is_skip']=='' || $request['is_skip']==0) && $hour_day!=$dataOne['hour_day'] && $schoolOne['school_isskipFw']==0){

                $companyOne=$this->DataControl->getFieldOne("gmc_company","company_canadjustcourse","company_id='{$request['company_id']}'");

                if($companyOne['company_canadjustcourse']==0 && $courseOne['course_inclasstype']==0){

                    if(!$this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$request['school_id']}' and adjustapply_day='{$dataOne['hour_day']}' and class_id=0 and hour_id=0 and adjustapply_status=1")){

//                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$request['school_id']}' and adjustapply_type=0 and class_id='{$dataOne['class_id']}' and adjustapply_status=0")){
//                            $this->error = true;
//                            $this->errortip = "班级存在未完成调课申请,不可再次申请";
//                            return false;
//                        }

                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and adjustapply_type=0 and class_id='{$hourOne['class_id']}' and hour_day<>'{$dataOne['hour_day']}' and adjustapply_status=0")){
                            $this->error = true;
                            $this->errortip = "班级存在不同日期未完成调课申请,不可再次申请";
                            return false;
                        }

                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and adjustapply_type=0 and class_id='{$hourOne['class_id']}' and hour_id='{$request['hour_id']}' and adjustapply_status=0")){
                            $this->error = true;
                            $this->errortip = "存在未完成班级调课申请,不可再次申请";
                            return false;
                        }

                        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and ((adjustapply_type=0 and class_id='{$hourOne['class_id']}' and hour_id='{$request['hour_id']}') or (adjustapply_type=1 and adjustapply_day='{$dataOne['hour_day']}')) and adjustapply_status=0")){
                            $this->error = true;
                            $this->errortip = "班级/校区存在未完成调课申请,不可再次申请";
                            return false;
                        }


                        $data=array();
                        $data['company_id']=$request['company_id'];
                        $data['school_id']=$request['school_id'];
                        $data['class_id']=$dataOne['class_id'];
                        $data['hour_id']=$request['hour_id'];
                        $data['hour_day']=$dataOne['hour_day'];
                        $data['adjustapply_class']=$request['adjustapply_class'];
                        $data['adjustapply_fileurl']=$request['adjustapply_fileurl'];
                        $data['adjustapply_day']=$hour_day;
                        $data['adjustapply_starttime']=$request['starttime'];
                        $data['adjustapply_endtime']=$request['endtime'];

                        $data['adjustapply_main_staffer_id']=$request['z_staffer_id'];
                        $data['adjustapply_main_teachtype_code']=$request['z_teachtype_code'];
                        $data['adjustapply_sub_staffer_id']=$request['f_staffer_id'];
                        $data['adjustapply_sub_teachtype_code']=$request['f_teachtype_code'];
                        $data['adjustapply_classroom_id']=$request['classroom_id'];
                        $data['is_need_process']=1;

                        $data['adjustapply_reason']=$request['adjustapply_reason'];
                        $data['staffer_id']=$request['staffer_id'];
                        $data['adjustapply_createtime']=time();

                        if($adjustapply_id=$this->DataControl->insertData("smc_class_hour_adjustapply",$data)){

                            //调用泛微创建流

//                            $fanweiModel->createNewAdjustApply($adjustapply_id);

                            $this->oktip="申请成功";
                            return true;
                        }else{
                            $this->error = 1;
                            $this->errortip = "申请失败";
                            return false;
                        }
                    }
                }
            }

            if (isset($request['classroom_id']) && $request['classroom_id'] != '' && $request['classroom_id']>0) {

                $data['classroom_id'] = $request['classroom_id'];
            }

            $data['hour_updatatime'] = time();


            if ($this->DataControl->updateData('smc_class_hour', "hour_id='{$request['hour_id']}'", $data)) {

                if (isset($request['z_staffer_id']) && $request['z_staffer_id'] != '' && $request['z_staffer_id']>0) {
                    $updatedata = array();
                    $updatedata['staffer_id'] = $request['z_staffer_id'];
                    $updatedata['teaching_updatatime'] = time();
                    $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching 
                    WHERE hour_id='{$request['hour_id']}' and teaching_type='0' limit 0,1 ");

                    if ($teachingOne) {
                        $updatedata['teachtype_code'] = $request['z_teachtype_code'];
                        $teachingID = $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='0' ", $updatedata);
                    } else {
                        $classhourOne = $this->DataControl->selectOne("select h.class_id,h.hour_lessontimes
                        from smc_class_hour as h WHERE hour_id='{$request['hour_id']}' limit 0,1 ");

                        $datatea = array();
                        $datatea['class_id'] = $classhourOne['class_id'];
                        $datatea['hour_lessontimes'] = $classhourOne['hour_lessontimes'];

                        $datatea['teachtype_code'] = $request['z_teachtype_code'];
                        $datatea['hour_id'] = $request['hour_id'];
                        $datatea['staffer_id'] = $request['z_staffer_id'];
                        $datatea['hour_former_staffer_id'] = 0;
                        $datatea['teaching_ischecking'] = 0;
                        $datatea['teaching_type'] = 0;
                        $datatea['teaching_isdel'] = 0;
                        $datatea['teaching_createtime'] = time();
                        $teachingID = $this->DataControl->insertData("smc_class_hour_teaching", $datatea);
                    }
                }

                if (isset($request['f_staffer_id']) && $request['f_staffer_id'] != '' && $request['f_staffer_id']>0) {
                    $updatedata = array();
                    $updatedata['staffer_id'] = $request['f_staffer_id'];
                    $updatedata['teaching_updatatime'] = time();
                    $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching WHERE hour_id='{$request['hour_id']}' and teaching_type='1' limit 0,1 ");

                    if ($teachingOne) {
                        $updatedata['teaching_isdel'] = 0;
                        $updatedata['teachtype_code'] = $request['f_teachtype_code'];

                        $teachingID = $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='1' and teaching_isdel=0", $updatedata);
                    } else {
                        $classhourOne = $this->DataControl->selectOne("select h.class_id,h.hour_lessontimes  from smc_class_hour as h  
                        WHERE hour_id='{$request['hour_id']}' limit 0,1 ");

                        $datatea = array();
                        $datatea['class_id'] = $classhourOne['class_id'];
                        $datatea['hour_lessontimes'] = $classhourOne['hour_lessontimes'];
                        $datatea['teachtype_code'] = $request['f_teachtype_code'];
                        $datatea['hour_id'] = $request['hour_id'];
                        $datatea['staffer_id'] = $request['f_staffer_id'];
                        $datatea['hour_former_staffer_id'] = 0;
                        $datatea['teaching_ischecking'] = 0;
                        $datatea['teaching_type'] = 1;
                        $datatea['teaching_isdel'] = 0;
                        $datatea['teaching_createtime'] = time();
                        $teachingID = $this->DataControl->insertData("smc_class_hour_teaching", $datatea);
                    }
                } else {
                    $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching WHERE hour_id='{$request['hour_id']}' and teaching_type='1' limit 0,1 ");

                    if ($teachingOne) {
                        $data = array();
                        $data['teaching_isdel'] = 1;
                        $data['teaching_updatatime'] = time();
                        $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='1' and teaching_isdel=0", $data);
                    }
                }

                // 同时更改网课的时间
                if ($dataOne['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = strtotime($data['hour_day'] . ' ' . $data['hour_starttime']);
                    $lineData['linerooms_endtime'] = strtotime($data['hour_day'] . ' ' . $data['hour_endtime']);
                    $lineData['linerooms_issync'] = "0";
                    $this->DataControl->updateData("smc_linerooms", "class_id='{$dataOne['class_id']}' and hour_id='{$dataOne['hour_id']}'", $lineData);
                }

                $this->addClientTrack($request['hour_id'], '调整上课日期,取消试听');

                $classEnd = $this->DataControl->getFieldOne('smc_class', "class_id,class_enddate,course_id", "class_id='{$hourOne['class_id']}'");
                $course = $this->DataControl->getFieldOne('smc_course', "course_cnname", "course_id='{$classOne['course_id']}'");
                $arr_last_day = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}'", "order by hour_day DESC");
                $last_day = $arr_last_day['hour_day'];
                if ($classEnd['class_enddate'] <> $last_day) {
                    $json = array();
                    $json['class_enddate'] = $last_day;
                    $json['class_updatatime'] = time();
                    if ($this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $json)) {
                        $studyData = array();
                        $studyData['study_endday'] = $last_day;
                        $studyData['study_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_study", "study_isreading =1 and class_id='{$hourOne['class_id']}'", $studyData);
                    }
                }
                if ($hourOne['hour_day'] > $hour_day) {
                    $tempyday = $hourOne['hour_day'];
                    $hourOne['hour_day'] = $hour_day;
                    $hour_day = $tempyday;
                }
                if ($hourOne['hour_iswarming'] == 0) {
                    if (isset($request['is_clear']) && $request['is_clear'] == 0) {
                        $clear = 0;
                    } else {
                        $clear = 1;
                    }
                    if ($hourOne['hour_way'] == 0) {
                        $hourOne['hour_number'] = 0;
                    }
                    $this->adjustLessonDay($hourOne['class_id'], $request['hour_id'], $clear, $hourOne['hour_number']);
                }


                $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss 
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    left join smc_parenter_wxchattoken as w on w.parenter_id = f.parenter_id and w.company_id = s.company_id and w.parenter_wxtoken is NOT NULL
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}' 
                        AND ss.study_isreading = '1'");

                $class_cnname = $this->DataControl->selectOne("
                    SELECT
                        h.class_id,
                        c.class_cnname
                    FROM
                        smc_class_hour AS h
                        left join smc_class as c on h.class_id= c.class_id
                        WHERE h.hour_id = '{$request['hour_id']}'");

                foreach ($parenter as &$val) {
                    $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");
                    $school_cnname = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$request['school_id']}'");

                    $coursetime = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_time", "course_id =     '{$classOne['course_id']}' and student_id = '{$val['student_id']}' and school_id = '{$request['school_id']}'");

                    $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程变动提醒'");
                    if ($isset) {
                        $wxid = $isset['masterplate_wxid'];
                    } else {
                        $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程变动提醒'");
                        $wxid = $masterplate['masterplate_wxid'];
                    }

                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，本课程的课表有所调整，请及时查看。');
                    $b = $school_cnname['school_cnname'];
                    $c = $class_cnname['class_cnname'];
                    $d = '您' . $course['course_cnname'] . '课程剩余' . $coursetime['coursebalance_time'] . '次未上课信息有调整';
                    $e = '点击可查看课表详情哦~';
                    $f = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->ChangeHour($a, $b, $c, $d, $e, $f, $wxid);
                }

                $this->error = 0;
                $this->conflict = 0;
                $this->oktip = "调整成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "调整失败";
                $this->conflict = 0;
                return false;
            }

        }else{
            $this->error = 1;
            $this->errortip = "请选择调课模式";
            return false;
        }
    }

    function postponeCourse($request)
    {

        $sql = "select ch.class_id,ch.hour_day,cl.class_type,ch.hour_id,ch.hour_lessontimes 
              from smc_class_hour as ch,smc_class as cl 
              where ch.class_id=cl.class_id and ch.hour_id='{$request['hour_id']}'";

        $hourOne = $this->DataControl->selectOne($sql);
        if (!$hourOne) {
            $this->error = true;
            $this->errortip = "无此课时";
            return false;
        }

        $planList = $this->DataControl->getList("smc_class_lessonplan", "class_id='{$hourOne['class_id']}'");

        if (!$planList) {
            $this->error = true;
            $this->errortip = "无排课安排";
            return false;
        }

        $tem_data = array();

        $isskipweek = 0;
        $skip_holidays = 0;

        foreach ($planList as $planOne) {
            $data = array();
            $data['weekday_id'] = $planOne['lessonplan_weekno'];
            $data['staffer_id'] = $planOne['staffer_id'];
            $data['classroom_id'] = $planOne['classroom_id'];
            $data['hour_starttime'] = $planOne['lessonplan_starttime'];
            $data['hour_endtime'] = $planOne['lessonplan_endtime'];
            $data['teachtype_code'] = $planOne['teachtype_code'];
            $data['assistant_staffer_id'] = $planOne['poll_staffer_id'];
            $data['assistant_teachtype_code'] = $planOne['poll_teachtype_code'];
            $tem_data[] = $data;

            $isskipweek = $planOne['lessonplan_isskipweek'];
            $skip_holidays = $planOne['lessonplan_isskipholidays'];
        }


        $request['arrangeList'] = json_encode($tem_data);

        $request['start'] = date("Y-m-d", strtotime("+1 day", strtotime($hourOne['hour_day'])));
//        $request['start']= $hourOne['hour_day'];
        $request['class_id'] = $hourOne['class_id'];
        $request['hour_lessontimes'] = $hourOne['hour_lessontimes'];

        $request['isskipweek'] = $isskipweek;

        $request['skip_holidays'] = $skip_holidays;

        $request['is_frequency'] = 1;

        $classModel = new \Model\Smc\ScheduleClassHourModel($request);

        $bool = $classModel->modifyLessonPlan($request, 1);

        if (!$bool) {
            $this->error = true;
            $this->errortip = $classModel->errortip;
            return false;
        }

        return true;

    }

    private function addeditClientTrack($hour_id, $track_note = '', $oldtime = '')
    {
        $HourOne = $this->DataControl->selectOne("select hour_day,hour_starttime from smc_class_hour where hour_id='{$hour_id}' and hour_id >0 limit 0,1 ");
        //更新CRM预约该课次的课
        $crm_audition = array();
        $crm_audition['audition_visittime'] = $HourOne['hour_day'] . ' ' . $HourOne['hour_starttime'] . ':00';
        $crm_audition['audition_updatetime'] = time();
        $this->DataControl->updateData("crm_client_audition", "hour_id='{$hour_id}' and hour_id>0 and audition_isvisit =0", $crm_audition);

        //时间调整添加一条无效记录
        $audtionList = $this->DataControl->selectClear("select au.client_id,au.school_id from crm_client_audition as au where au.hour_id='{$hour_id}' and hour_id >0  and audition_isvisit =0");
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name,marketer_id", "staffer_id='{$this->staffer_id}'");
        if (!$marketerOne) {
            $stafferOne = $this->stafferOne;
            $marketer_data = array();
            $marketer_data['staffer_id'] = $stafferOne['staffer_id'];
            $marketer_data['company_id'] = $stafferOne['company_id'];
            $marketer_data['marketer_name'] = $stafferOne['staffer_cnname'];
            $marketer_data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $marketer_data['marketer_img'] = $stafferOne['staffer_img'];
            $marketer_data['marketer_createtime'] = time();
            $id = $this->DataControl->insertData("crm_marketer", $marketer_data);

            $marketerOne = array();
            $marketerOne['marketer_id'] = $id;
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        if ($audtionList) {
            foreach ($audtionList as $key => $value) {
                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['school_id'] = $value['school_id'];
                $trackData['marketer_name'] = $marketerOne['marketer_name'];
                $trackData['marketer_id'] = $marketerOne['marketer_id'];
                $trackData['track_validinc'] = '0';
                $trackData['track_linktype'] = '系统新增';
                $trackData['track_note'] = $track_note;
                $trackData['track_followmode'] = '2';
                $trackData['track_visitingtime'] = $crm_audition['audition_visittime'];
                $trackData['track_isactive'] = '0';
                $trackData['track_createtime'] = time();
                $this->DataControl->insertData("crm_client_track", $trackData);
            }
        }

    }


    private function addClientTrack($hour_id, $track_note = '')
    {

        $audtionList = $this->DataControl->selectClear("select au.client_id,au.school_id from crm_client_audition as au where au.hour_id='{$hour_id}' and hour_id >0  and audition_isvisit =0");
        //更新CRM预约该课次的课
        $crm_audition = array();
        $crm_audition['audition_isvisit'] = '-1';
        $crm_audition['audition_novisitreason'] = $track_note;
        $crm_audition['audition_updatetime'] = time();
        $this->DataControl->updateData("crm_client_audition", "hour_id='{$hour_id}' and hour_id>0 and audition_isvisit =0", $crm_audition);
        $class_audition = array();
        $class_audition['audition_isvisit'] = '-1';
        $class_audition['audition_novisitreason'] = $track_note;
        $this->DataControl->updateData("smc_class_hour_audition", "hour_id='{$hour_id}' and hour_id>0 and audition_isvisit =0", $class_audition);


        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name,marketer_id", "staffer_id='{$this->staffer_id}'");
        if (!$marketerOne) {
            $stafferOne = $this->stafferOne;
            $marketer_data = array();
            $marketer_data['staffer_id'] = $stafferOne['staffer_id'];
            $marketer_data['company_id'] = $stafferOne['company_id'];
            $marketer_data['marketer_name'] = $stafferOne['staffer_cnname'];
            $marketer_data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $marketer_data['marketer_img'] = $stafferOne['staffer_img'];
            $marketer_data['marketer_createtime'] = time();
            $id = $this->DataControl->insertData("crm_marketer", $marketer_data);

            $marketerOne = array();
            $marketerOne['marketer_id'] = $id;
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        if ($audtionList) {
            foreach ($audtionList as $key => $value) {
                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['school_id'] = $value['school_id'];
                $trackData['marketer_name'] = $marketerOne['marketer_name'];
                $trackData['marketer_id'] = $marketerOne['marketer_id'];
                $trackData['track_validinc'] = '1';
                $trackData['track_linktype'] = '取消试听';
                $trackData['track_note'] = $track_note;
                $trackData['track_isactive'] = '0';
                $trackData['track_createtime'] = time();
                $this->DataControl->insertData("crm_client_track", $trackData);
            }
        }
    }

    function adjustLessonDay($class_id, $hour_id = 0, $is_clear = 0, $hour_number = 0)
    {
        $courseOne = $this->DataControl->selectOne("select c.course_id,c.course_inclasstype from smc_course as c, smc_class as s where c.course_id=s.course_id and s.class_id='{$class_id}' ");
        if ($courseOne['course_inclasstype'] == 2 && $hour_id !== 0 && $is_clear == 1) {
            $data = array();
            $data['booking_status'] = '-1';
            $data['booking_updatatime'] = time();
            $this->DataControl->updateData("smc_class_booking", "hour_id='{$hour_id}' and class_id='{$class_id}'", $data);
        }
        if ($hour_number !== 0) {
            $this->DataControl->query(" UPDATE smc_linerooms AS l, smc_class_hour AS h SET l.linerooms_name = h.hour_name WHERE h.hour_number = l.linerooms_number AND l.linerooms_number = '{$hour_number}'");
        }
        $this->cancelAbsenceHour($hour_id, '调整排课,取消对应的请假记录');

        $sql = "call RefreshClass('{$class_id}')";
        $this->DataControl->query($sql);

        $hourList = $this->DataControl->selectClear("select hour_id,hour_name,hour_lessontimes from smc_class_hour where class_id='{$class_id}' and hour_ischecking <>'-2' and hour_way =1 ");
        if ($hourList) {
            foreach ($hourList as $key => $value) {
                $linerooms_data = array();
                $linerooms_data['linerooms_name'] = $value['hour_name'];
                $linerooms_data['hour_lessontimes'] = $value['hour_lessontimes'];
                $this->DataControl->updateData("smc_linerooms", "hour_id='{$value['hour_id']}'", $linerooms_data);
            }
        }
    }

//调课时取消请假记录
    function cancelAbsenceHour($hour_id, $note)
    {
        $absenceData = $this->DataControl->selectClear("select h . absence_hour_id,sa . student_id,h . absence_id
              from smc_student_absence_hour as h,smc_student_absence as sa 
              where h . absence_id = sa . absence_id and h . hour_id = '{$hour_id}' and h . absence_hour_status <> '-1' ");
        if ($absenceData) {
            foreach ($absenceData as $absenceOne) {
                $trackData = array();
                $trackData['absence_id'] = $absenceOne['absence_id'];
                $trackData['track_title'] = '取消请假';
                $trackData['track_applynote'] = $note;
                $trackData['track_status'] = '-2';
                $trackData['track_applytime'] = time();
                $this->DataControl->insertData("smc_student_absence_track", $trackData);
            }
        }
        $this->DataControl->query("update smc_student_absence_hour set absence_hour_status = -1 where hour_id = '{$hour_id}' ");
    }

    function getConflictCourseList($request)
    {
        $list = json_decode(stripslashes($request['hour_list']), 1);

        if (!$list) {
            $this->error = true;
            $this->errortip = "请选择课时";
            return false;
        }

        if (!isset($request['hour_day']) || $request['hour_day'] == '') {
            $this->error = true;
            $this->errortip = "请选择上课日期";
            return false;
        }

        $tem_data = array();

        foreach ($list as $one) {
            $sql = "select ch.hour_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.classroom_id,c.class_id,c.class_cnname,c.class_enname,cl.classroom_cnname,co.course_cnname
              ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),0) as z_staffer_id
              ,ifnull((select concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) 
                from smc_class_hour_teaching as t,smc_staffer as s where t.staffer_id=s.staffer_id and t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),'--') as z_staffer_cnname
              from smc_class_hour as ch 
			  left join smc_class as c on c.class_id =ch.class_id
 			  left join smc_course as co on co.course_id=c.course_id      
			  left join smc_classroom as cl on cl.classroom_id = ch.classroom_id
              where ch.hour_id='{$one['hour_id']}' limit 0,1";

            $yhourOne = $this->DataControl->selectOne($sql);

            $sql = "select ch.hour_starttime,ch.hour_endtime,ch.classroom_id
                ,ifnull((select t.staffer_id from smc_class_hour_teaching as t where t.hour_id=ch.hour_id and t.teaching_type=0 and t.teaching_isdel=0 limit 0,1),0) as z_staffer_id
 				from smc_class_hour as ch
                left join smc_class as c on c.class_id=ch.class_id
				where ch.hour_day='{$request['hour_day']}' and ch.hour_id <>'{$one['hour_id']}' and c.school_id ='{$this->school_id}'
                having (z_staffer_id='{$yhourOne['z_staffer_id']}' or classroom_id='{$yhourOne['classroom_id']}')
				";
            $hourList = $this->DataControl->selectClear($sql);

            $status = 0;

            $yhourOne['time'] = $yhourOne['hour_starttime'] . '~' . $yhourOne['hour_endtime'];

            foreach ($hourList as $hourOne) {

                $is_cross = $this->is_time_cross(strtotime($hourOne['hour_day'] . ' ' . $yhourOne['hour_starttime']), strtotime($hourOne['hour_day'] . ' ' . $yhourOne['hour_endtime']), strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']), strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']));

                if ($is_cross) {
                    if ($yhourOne['classroom_id'] == $hourOne['classroom_id']) {
                        $yhourOne['c_cross'] = 1;//教室冲突
                        $status = 1;
                    }

                    if ($yhourOne['z_staffer_id'] == $hourOne['z_staffer_id'] && $hourOne['z_staffer_id'] > 0) {
                        $yhourOne['z_cross'] = 1;//主教冲突
                        $status = 1;
                    }
                }
            }
            if ($status == 1) {
                $tem_data[] = $yhourOne;
            }
        }

        if (!$tem_data) {
            $this->error = true;
            $this->errortip = "无冲突课时";
            return false;
        }
        return $tem_data;
    }
}