<?php


namespace Model\Smc;

use Model\Api\SmcModel;

class ScheduleClassHourModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();
    public $classModel = false;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
        $this->classModel = new \Model\Smc\ClassModel($publicarray);
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }
    //	检查时间端冲突
    // $from  1-只能一天排一节课
    function checkflictTime($timeList, $from = 0)
    {
        if (count($timeList) > 1) {
            for ($i = 0; $i < count($timeList); $i++) {
                for ($j = $i + 1; $j < count($timeList); $j++) {

                    if (($timeList[$i]['weekday_id'] == $timeList[$j]['weekday_id'])) {
                        if ($from == 1) {
                            return $timeList[$i]['weekday_id'];
                        }

                        $iend = date('Y-m-d') . " " . $timeList[$i]['hour_endtime'];
                        $istart = date('Y-m-d') . " " . $timeList[$i]['hour_starttime'];
                        $jend = date('Y-m-d') . " " . $timeList[$j]['hour_endtime'];
                        $jstart = date('Y-m-d') . " " . $timeList[$j]['hour_starttime'];

                        $iend = date('Y-m-d H:i:s', strtotime($iend));
                        $istart = date('Y-m-d H:i:s', strtotime($istart));
                        $jend = date('Y-m-d H:i:s', strtotime($jend));
                        $jstart = date('Y-m-d H:i:s', strtotime($jstart));

                        if (!($iend <= $jstart) && !($jend <= $istart)) {

                            return $timeList[$i]['weekday_id'];
                        }
                    }
                }
            }
        } else {
            return true;
        }
        return true;
    }

    //根据云教室号更新hour_id
    private function updateHourNumber($hour_number, $hour_id, $paramArray = array())
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day,hour_starttime,hour_endtime,hour_name,hour_lessontimes", "hour_id='{$hour_id}'");
        $data = array();
        $data['hour_id'] = $hour_id;
        $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
        $data['linerooms_starttime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']);
        $data['linerooms_endtime'] = strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_endtime']);
        $data['linerooms_name'] = $hourOne['hour_name'];
        $data['linerooms_issync'] = "0";
        $this->DataControl->updateData("smc_linerooms", "linerooms_number='{$hour_number}'", $data);
    }

    function scheduleClass($request)
    {
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_type", "class_id='{$request['class_id']}'");

        $courseOne = $this->DataControl->selectOne("
					select  ct.coursetype_isopenclass,course_inclasstype,course_islimitamout,course_limitamout from  smc_class as c
					left join  smc_course as  co on c.course_id = co.course_id
				    left join  smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
 				    where c.class_id='{$request['class_id']}' ");

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_iswarming = 0");
        if ($hourOne) {
            $this->error = true;
            $this->errortip = "已存在排课课次！";
            return false;
        }

        if ($classOne['class_type'] == 1 && $courseOne['course_islimitamout'] == 1) {
            if ($request['maxnum'] != $courseOne['course_limitamout']) {
                $this->error = true;
                $this->errortip = "该课程的子班已经限制排课次数为:" . $courseOne['course_limitamout'];
                return false;
            }
        }

        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        // 将更靠近排课开始时间的课程往前排
        $day_weeknum = date('w', strtotime($request['start']));
        $day_weeknum = $day_weeknum == 0 ? '7' : $day_weeknum;

        if (is_array($arrangeList) && $arrangeList) {
            foreach ($arrangeList as $key => $arrOne) {
                if ($arrOne['weekday_id'] < $day_weeknum) {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'] + 7;
                } else {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'];
                }
            }
            $week_id_sort = array_column($arrangeList, 'week_id_sort');
            array_multisort($week_id_sort, SORT_ASC, $arrangeList);
        } else {
            $this->error = true;
            $this->errortip = "请设置正确的时间！";
            return false;
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id,class_hournums,class_stdate", "class_id='{$request['class_id']}'");
        if ($classOne['class_stdate'] > $request['start']) {
            $this->error = 1;
            $this->errortip = "请选择开班时间之后的日期";
            return false;
        }
        $request['carrynum'] = $request['carrynum'] <= 0 ? 0 : $request['carrynum'];
        $request['last_carrynum'] = $request['last_carrynum'] <= 0 ? 0 : $request['last_carrynum'];
        if (intval($request['maxnum']) <= 0) {
            $this->error = 1;
            $this->errortip = "请确认课程别的排课次数";
            return false;
        }

        $number = $request['maxnum'] + $request['carrynum'] + $request['last_carrynum'];
        $total = count($arrangeList);


    }


    /**
     * 班级新增排课 - 排课
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function classSchedule($request)
    {
        if (isset($request['isskipweek']) && $request['isskipweek'] !== '') {
            $isskipweek = intval($request['isskipweek']);
        } else {
            $isskipweek = 0;
        }

        $sql = "select a.class_id,a.class_type,a.course_id,b.course_isopenwarm,a.class_hourwarmnums,a.class_hourwarmapplynums,b.course_isopenreview,a.class_hourreviewnums,a.class_hourreviewapplynum,b.course_canapplywarm,b.course_canapplyreview,b.course_reviewnum,a.class_ismustreview,a.class_hournums,a.class_stdate
               ,(a.class_hourreviewnums+a.class_hourreviewapplynum -ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id = a.class_id and x.hour_ischecking=1 and x.hour_iswarming=2),0)) as can_plan_review_nums
                from smc_class as a 
                left join smc_course as b on b.course_id=a.course_id
                where a.class_id = '{$request['class_id']}'
                ";

        $classOne = $this->DataControl->selectOne($sql);


        $sql = "SELECT ct.coursetype_isopenclass,course_inclasstype,course_islimitamout,course_limitamout,co.course_weeklimittime*co.course_islimittime as course_weeklimittime,co.course_islimitopencycle,co.course_limitopencyclenum,co.course_weekstandardnum,c.course_id
                from  smc_class as c
                left join  smc_course as  co on c.course_id = co.course_id
                left join  smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
                where c.class_id='{$request['class_id']}'";

        $courseOne = $this->DataControl->selectOne($sql);
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_iswarming = 0");
        if ($hourOne) {
            $this->error = true;
            $this->errortip = "已存在排课课次！";
            return false;
        }
        if ($classOne['class_type'] == 1 && $courseOne['course_islimitamout'] == 1) {
            if ($request['maxnum'] != $courseOne['course_limitamout']) {
                $this->error = true;
                $this->errortip = "该课程的子班已经限制排课次数为:" . $courseOne['course_limitamout'];
                return false;
            }
        }

        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        $reArrangeList = json_decode(stripslashes($request['re_arrangeList']), true);
        // 将更靠近排课开始时间的课程往前排
        $day_weeknum = date('w', strtotime($request['start']));
        $day_weeknum = $day_weeknum == 0 ? '7' : $day_weeknum;
        if (is_array($arrangeList) && $arrangeList) {
            foreach ($arrangeList as $key => $arrOne) {
                if ($arrOne['weekday_id'] < $day_weeknum) {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'] + 7;
                } else {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'];
                }
            }
            $week_id_sort = array_column($arrangeList, 'week_id_sort');
            array_multisort($week_id_sort, SORT_ASC, $arrangeList);
        } else {
            $this->error = true;
            $this->errortip = "请设置正确的时间！";
            return false;
        }

        if($courseOne['course_weekstandardnum']>0 && count($arrangeList)<$courseOne['course_weekstandardnum']){
            $this->error = true;
            $this->errortip = "周排课计划不可少于{$courseOne['course_weekstandardnum']}次！";
            return false;
        }

        if ($classOne['class_stdate'] > $request['start']) {
            $this->error = 1;
            $this->errortip = "请选择开班时间之后的日期";
            return false;
        }

        $request['carrynum'] = $request['carrynum'] <= 0 ? 0 : $request['carrynum'];
        $request['last_carrynum'] = $request['last_carrynum'] <= 0 ? 0 : $request['last_carrynum'];

        if (intval($request['maxnum']) <= 0) {
            $this->error = 1;
            $this->errortip = "请确认课程别的排课次数";
            return false;
        }

        $number = $request['maxnum'] + $request['carrynum'] + $request['last_carrynum'];

        $holidayArray = array();
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $sql = "select ch.holidays_day from smc_code_holidays as ch
                  where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$request['start']}'
                  and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='0')
                       or
                       (ch.school_id='0' and ch.holidays_status='0' and  ch.company_id='{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='1'))
                  )";

            $holidayList = $this->DataControl->selectClear($sql);
            if ($holidayList) {
                foreach ($holidayList as $holidayOne) {
                    $holidayArray[] = $holidayOne['holidays_day'];
                }
            }
            //排除假期
            sort($holidayArray);
        }

        $dayListArray = $this->classModel->newArrangeDayList($request['start'], $request['end'], $number, $request['is_frequency'],$arrangeList,$holidayArray, $isskipweek, 0);

        $tem_plan = array();
        
        sort($dayListArray);

        if($courseOne['course_islimitopencycle']==1){
            $startTimestamp = strtotime($dayListArray[0]['day']);
            $endTimestamp = strtotime(date("Y-m-d",strtotime($dayListArray[count($dayListArray)-1]['day'])));

            $betweendays = abs(($endTimestamp - $startTimestamp) / (60 * 60 * 24));

            if($betweendays>$courseOne['course_limitopencyclenum']){
                $this->errortip = '排课周期不能大于'.$courseOne['course_limitopencyclenum'].'天';
                $this->error = "1";
                return false;
            }
        }

        //排课时间列表  用来检查时间冲突
        $timeListArray = array();
        

        //巩固复习课判断
        $re_dayListArray=array();
        if($reArrangeList){

            if (isset($request['re_isskipweek']) && $request['re_isskipweek'] !== '') {
                $re_isskipweek = intval($request['re_isskipweek']);
            } else {
                $re_isskipweek = 0;
            }

            if($classOne['course_isopenreview']==0){
                $this->error = '1';
                $this->errortip = '该课程无法创建复习课';
                return false;
            }

            foreach ($reArrangeList as $key => $reArrangeOne) {
                $timeListArray[$key+count($arrangeList)]['weekday_id'] = $reArrangeOne['weekday_id'];
                $timeListArray[$key+count($arrangeList)]['hour_starttime'] = $reArrangeOne['hour_starttime'];
                $timeListArray[$key+count($arrangeList)]['hour_endtime'] = $reArrangeOne['hour_endtime'];
            }

            $re_holidayArray = array();

            if (isset($request['re_skip_holidays']) && $request['re_skip_holidays'] == 1) {
                $sql = "select ch.holidays_day from smc_code_holidays as ch
                    where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$request['re_start']}'
                    and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='0')
                        or
                        (ch.school_id='0' and ch.holidays_status='0' and  ch.company_id='{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='1'))
                    )";

                $re_holidayList = $this->DataControl->selectClear($sql);
                if ($re_holidayList) {
                    foreach ($re_holidayList as $holidayOne) {
                        $re_holidayArray[] = $holidayOne['holidays_day'];
                    }
                }
                //排除假期
                sort($re_holidayArray);
            }

            $re_dayListArray = $this->classModel->newArrangeDayList($request['re_start'], $request['re_end'], $classOne['class_hourreviewnums'], $request['is_frequency'],$reArrangeList,$re_holidayArray, $re_isskipweek, 0,2);

            if($classOne['class_hourreviewnums']>count($re_dayListArray)){
                $this->error = '1';
                $this->errortip = '复习课次数不足,请重新设置';
                return false;
            }

            sort($re_dayListArray);

            if($re_dayListArray[count($re_dayListArray)-1]['day']>$dayListArray[count($dayListArray)-1]['day']){
                $this->error = '1';
                $this->errortip = '复习课次结束时间不能大于排课结束时间';
                return false;   
            }
        
        }else{
            if($classOne['class_ismustreview']==1 && $classOne['can_plan_review_nums']>0){
                $this->error = '1';
                $this->errortip = '该课程必须创建复习课';
                return false;
            }
        }


        $weektotal = 0;

        foreach ($arrangeList as $key => $arrangeOne) {
            $timeListArray[$key]['weekday_id'] = $arrangeOne['weekday_id'];
            $timeListArray[$key]['hour_starttime'] = $arrangeOne['hour_starttime'];
            $timeListArray[$key]['hour_endtime'] = $arrangeOne['hour_endtime'];
            $weektotal += (strtotime($timeListArray[$key]['hour_endtime']) - strtotime($timeListArray[$key]['hour_starttime'])) / 60;
        }

        if ($weektotal > $courseOne['course_weeklimittime'] && $courseOne['course_weeklimittime'] > 0) {
            $this->errortip = "周排课总分钟数大于课程设置的最大值" . $courseOne['course_weeklimittime'] . "min！";
            $this->error = "1";
            return false;
        }

        if ($courseOne['coursetype_isopenclass'] == 2) {
            $time_day = $this->checkflictTime($timeListArray, 1);
            if ($time_day !== true) {
                $time_week = self::$WORK_DAY[$time_day]['cn'];
                $this->errortip = $time_week . "有两次排课,请删除其中一个";
                $this->error = "1";
                return false;
            }
        } else {
            //检查时间冲突
            $time_day = $this->checkflictTime($timeListArray);
            if ($time_day !== true) {
                $time_week = self::$WORK_DAY[$time_day]['cn'];
                $this->errortip = $time_week . "所选时间段冲突";
                $this->error = "1";
                return false;
            }
        }

        $temHour = array();

        foreach ($dayListArray as $key=>$day) {
            $hourData = array();
            $hourData['course_id'] = $classOne['course_id'];
            $hourData['class_id'] = $request['class_id'];
            $hourData['classroom_id'] = $day['planOne']['classroom_id'];
            $hourData['hour_iswarming'] = $day['hour_iswarming'];
            $hourData['hour_lessontimes'] = $key + 1;

            if ($courseOne['coursetype_isopenclass'] == 1) {
                $request['carrynum'] = 0;
                $request['last_carrynum'] = 0;
            }

            //双重模式 同时支持前期赠送 后期赠送
            if (isset($request['carrynum']) && $request['carrynum'] >= 0 && isset($request['last_carrynum']) && $request['last_carrynum'] >= 0) {

                if ($request['carrynum'] >= ($key + 1)) {
                    $hourData['hour_name'] = 'Lesson ' . ($key + 1);
                    $hourData['hour_isfree'] = 1;
                } elseif (($request['maxnum'] + $request['carrynum']) < ($key + 1)) {
                    $hourData['hour_name'] = 'Lesson ' . ($key + 1);
                    $hourData['hour_isfree'] = 1;
                } else {
                    if ($courseOne['coursetype_isopenclass'] == 1) {
                        $hourData['hour_name'] = 'Lesson ' . ($key + 1);
                        $hourData['hour_isfree'] = 1;
                    } else {
                        $hourData['hour_name'] = 'Lesson ' . ($key + 1);
                        $hourData['hour_isfree'] = 0;
                    }
                }
            }

            $hourData['hour_day'] = $day['day'];
            $hourData['hour_starttime'] = $day['planOne']['hour_starttime'];
            $hourData['hour_endtime'] = $day['planOne']['hour_endtime'];
            $hourData['hour_createtime'] = time();
            $hourData['hour_updatatime'] = time();
            $hourData['hour_formerday'] = $day['day'];
            //hour表不需要的字段
            $hourData['staffer_id'] = $day['planOne']['staffer_id'];
            $hourData['teachtype_code'] = $day['planOne']['teachtype_code'];
            $hourData['assistant_staffer_id'] = $day['planOne']['assistant_staffer_id'];
            $hourData['assistant_teachtype_code'] = $day['planOne']['assistant_teachtype_code'];
            $hourData['hour_way'] = $day['planOne']['hour_way'];
            if ($hourData['hour_way'] == 1) {
                $hourData['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
            }
            $hourData['hour_formertimes'] = $day['planOne']['hour_starttime'] . '-' . $day['planOne']['hour_endtime'];
            if (strtotime($day . " " . $day['planOne']['hour_starttime']) < strtotime($day . " 12:00")) {
                $hourData['hour_noon'] = 1;
            } elseif (strtotime($day . " " . $day['planOne']['hour_starttime']) > strtotime($day . " 18:00")) {
                $hourData['hour_noon'] = 3;
            } else {
                $hourData['hour_noon'] = 2;
            }
            $temHour[] = $hourData;
        }

        $num = 0;

        if ($temHour) {


            foreach ($arrangeList as $key => $arrangeOne) {
                //排课计划
                $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];
    
                $data = array();
                $data['class_id'] = $request['class_id'];
                $data['lessonplan_week'] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'];
                $data['lessonplan_weekno'] = $arrangeOne['weekday_id'];
                $data['classroom_id'] = $arrangeOne['classroom_id'];
                $data['staffer_id'] = $arrangeOne['staffer_id'];
                $data['teachtype_code'] = $arrangeOne['teachtype_code'];
                $data['poll_staffer_id'] = $arrangeOne['assistant_staffer_id'];
                $data['poll_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];
                $data['lessonplan_play'] = '1';
                $data['lessonplan_starttime'] = $arrangeOne['hour_starttime'];
                $data['lessonplan_endtime'] = $arrangeOne['hour_endtime'];
                $data['lessonplan_isskipweek'] = $request['isskipweek'];
                $data['lessonplan_way'] = $arrangeOne['hour_way'];
                $data['lessonplan_createtime'] = time();
                $this->DataControl->insertData("smc_class_lessonplan", $data);
            }

//            将赠送的课变为暖身课
            $arr_lessontimes = array_column($temHour, 'hour_lessontimes');

            array_multisort($arr_lessontimes, $temHour, SORT_ASC);

            foreach ($temHour as $knum => $temOne) {
                if ($courseOne['coursetype_isopenclass'] == 1) {
                    $temHour[$knum]['hour_iswarming'] = 1;
                    $temHour[$knum]['hour_isfree'] = 1;
                    $temHour[$knum]['hour_lessontimes'] = $knum + 1;
                    $temHour[$knum]['hour_name'] = 'Warm ' . ($knum + 1);
                } else {
                    if ($knum + 1 <= $request['carrynum']) {
                        $temHour[$knum]['hour_iswarming'] = 1;
                        $temHour[$knum]['hour_isfree'] = 1;
                        $temHour[$knum]['hour_lessontimes'] = $knum + 1;
                        $temHour[$knum]['hour_name'] = 'Warm ' . ($knum + 1);
                    }
                    if ($request['carrynum'] > 0 && $knum + 1 > $request['carrynum']) {
                        $temHour[$knum]['hour_iswarming'] = 0;
                        $temHour[$knum]['hour_isfree'] = 0;
                        $temHour[$knum]['hour_lessontimes'] = $temOne['hour_lessontimes'] - $request['carrynum'];
                        $temHour[$knum]['hour_name'] = 'Lesson ' . ($temOne['hour_lessontimes'] - $request['carrynum']);
                    }
                    if ($knum + 1 > $request['carrynum'] + $request['maxnum']) {
                        $temHour[$knum]['hour_iswarming'] = 1;
                        $temHour[$knum]['hour_isfree'] = 1;
                        $temHour[$knum]['hour_name'] = 'Warm ' . ($knum + 1 - $request['maxnum']);
                        $temHour[$knum]['hour_lessontimes'] = $knum + 1 - $request['maxnum'];
                    }
                }
            }

            foreach ($temHour as $key => $value) {
                

                $hourArray=array();
                $hourArray['course_id']=$value['course_id'];
                $hourArray['class_id']=$value['class_id'];
                $hourArray['classroom_id']=$value['classroom_id'];
                $hourArray['hour_day']=$value['hour_day'];
                $hourArray['hour_starttime']=$value['hour_starttime'];
                $hourArray['hour_endtime']=$value['hour_endtime'];
                $hourArray['hour_createtime']=$value['hour_createtime'];
                $hourArray['hour_updatatime']=$value['hour_updatatime'];
                $hourArray['hour_formerday']=$value['hour_formerday'];
                $hourArray['hour_way']=$value['hour_way'];
                $hourArray['hour_formertimes']=$value['hour_formertimes'];
                $hourArray['hour_noon']=$value['hour_noon'];
                $hourArray['hour_iswarming']=$value['hour_iswarming'];
                $hourArray['hour_isfree']=$value['hour_isfree'];
                $hourArray['hour_name']=$value['hour_name'];
                $hourArray['hour_lessontimes']=$value['hour_lessontimes'];

                $hour_id = $this->DataControl->insertData("smc_class_hour", $hourArray);

                $num++;

                if ($value['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = $value['hour_starttime'];
                    $lineData['linerooms_endtime'] = $value['hour_endtime'];
                    $lineData['linerooms_name'] = $value['hour_name'];
                    $this->updateHourNumber($value['hour_number'], $hour_id, $lineData);
                }

                if($value['staffer_id']>0) {
                    //主教
                    $techData = array();
                    $techData['staffer_id'] = $value['staffer_id'];
                    $techData['hour_lessontimes'] = $value['hour_lessontimes'];
                    $techData['teaching_createtime'] = time();
                    $techData['teaching_type'] = 0;
                    $techData['class_id'] = $value['class_id'];
                    $techData['teachtype_code'] = $value['teachtype_code'];
                    $techData['hour_former_staffer_id'] = $value['staffer_id'];
                    $techData['hour_id'] = $hour_id;
                    $this->DataControl->insertData("smc_class_hour_teaching", $techData);
                }
                

                if ($value['assistant_staffer_id']>0) {
                    //助教
                    $re_techData = array();
                    $re_techData['staffer_id'] = $value['assistant_staffer_id'];
                    $re_techData['teachtype_code'] = $value['assistant_teachtype_code'];
                    $re_techData['hour_former_staffer_id'] = $value['assistant_staffer_id'];
                    $re_techData['hour_lessontimes'] = $value['hour_lessontimes'];
                    $re_techData['teaching_createtime'] = time();
                    $re_techData['teaching_type'] = 1;
                    $re_techData['class_id'] = $value['class_id'];
                    $re_techData['hour_id'] = $hour_id;

                    $this->DataControl->insertData("smc_class_hour_teaching", $re_techData);
                }
                $last_day = $value['hour_day'];
            }
        }

        if($re_dayListArray){

            foreach ($reArrangeList as $key => $re_arrangeOne) {
                //排课计划
    
                $data = array();
                $data['class_id'] = $request['class_id'];
                $data['reviewplan_week'] = self::$WORK_DAY[$re_arrangeOne['weekday_id']]['cn'];
                $data['reviewplan_weekno'] = $re_arrangeOne['weekday_id'];
                $data['classroom_id'] = $re_arrangeOne['classroom_id'];
                $data['staffer_id'] = $re_arrangeOne['staffer_id'];
                $data['teachtype_code'] = $re_arrangeOne['teachtype_code'];
                $data['poll_staffer_id'] = $re_arrangeOne['assistant_staffer_id'];
                $data['poll_teachtype_code'] = $re_arrangeOne['assistant_teachtype_code'];
                $data['reviewplan_starttime'] = $re_arrangeOne['hour_starttime'];
                $data['reviewplan_endtime'] = $re_arrangeOne['hour_endtime'];
                $data['reviewplan_isskipweek'] = $request['re_isskipweek'];
                $data['reviewplan_way'] = $re_arrangeOne['hour_way'];
                $data['reviewplan_createtime'] = time();
                $this->DataControl->insertData("smc_class_reviewplan", $data);
            }

            foreach($re_dayListArray as $rekey=>$re_hourOne){

                $data = array();
                $data['hour_lessontimes'] = $rekey+1;
                $data['class_id'] = $request['class_id'];
                $data['course_id'] = $courseOne['course_id'];
                $data['classroom_id'] = $re_hourOne['planOne']['classroom_id'];
                $data['hour_name'] = 'Review  ' . $data['hour_lessontimes'];
                $data['hour_isfree'] = '1';
                $data['hour_day'] = $re_hourOne['day'];

                
                $data['hour_way'] = $re_hourOne['planOne']['hour_way'];
                if ($re_hourOne['planOne']['hour_way'] == 1) {
                    $data['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
                }

                $data['hour_iswarming'] = 2;
                $data['hour_starttime'] = $re_hourOne['planOne']['hour_starttime'];
                $data['hour_endtime'] = $re_hourOne['planOne']['hour_endtime'];
                $data['hour_createtime'] = time();
                $data['hour_updatatime'] = time();

                if ($id = $this->DataControl->insertData("smc_class_hour", $data)) {
                    $num++;
                    if($re_hourOne['planOne']['staffer_id']>0){

                        $teachingData = array();
                        $teachingData['class_id'] = $request['class_id'];
                        $teachingData['hour_id'] = $id;
                        $teachingData['hour_lessontimes'] = $data['hour_lessontimes'];
                        $teachingData['staffer_id'] = $re_hourOne['planOne']['staffer_id'];
                        $teachingData['teaching_type'] = 0;
                        $teachingData['teachtype_code'] = $re_hourOne['planOne']['teachtype_code'];
                        $teachingData['teaching_createtime'] = time();
                        $this->DataControl->insertData("smc_class_hour_teaching", $teachingData);

                    }
                    

                    if($re_hourOne['planOne']['assistant_staffer_id']>0){

                        $fu_teachingData = array();
                        $fu_teachingData['class_id'] = $request['class_id'];
                        $fu_teachingData['hour_id'] = $id;
                        $fu_teachingData['hour_lessontimes'] = $data['hour_lessontimes'];
                        $fu_teachingData['staffer_id'] = $re_hourOne['planOne']['assistant_staffer_id'];
                        $fu_teachingData['teaching_type'] = 1;
                        $fu_teachingData['teachtype_code'] = $re_hourOne['planOne']['assistant_teachtype_code'];
                        $fu_teachingData['teaching_createtime'] = time();
                        $this->DataControl->insertData("smc_class_hour_teaching", $fu_teachingData);
                        
                    }
                    
        
                    $classOne = $this->DataControl->getFieldOne("smc_class", "class_enddate", "class_id = '{$request['class_id']}'");
                    if ($last_day < $re_hourOne['day']) {
                        $classData = array();
                        $classData['class_enddate'] = $re_hourOne['day'];
                        $classData['class_updatatime'] = time();
                        $this->DataControl->updateData("smc_class", "class_id = '{$request['class_id']}'", $classData);
                        $studyData = array();
                        $studyData['study_endday'] = $re_hourOne['day'];
                        $studyData['study_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_study", "study_isreading = 1 and class_id = '{$request['class_id']}'", $studyData);

                        $last_day=$re_hourOne['day'];
                    }

                }
            }
        }


        $json = array();
        $json['class_timestr'] = implode(",", $tem_plan);
        $json['class_enddate'] = date('Y-m-d', strtotime("$last_day"));
        $json['class_hournums'] = $classOne['class_hournums'] + intval(count($temHour));
        $json['class_updatatime'] = time();
        $this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $json);
        $studyData = array();
        $studyData['study_endday'] = $last_day;
        $studyData['study_updatetime'] = time();
        $this->DataControl->updateData("smc_student_study", "  study_isreading =1 and class_id='{$request['class_id']}'", $studyData);
        $this->error = 0;
        $this->errortip = "共排课{$num}节！";
        return true;
    }


    /**
     * 修改排课
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function modifyLessonPlan($request, $from = 0)
    {
        if (isset($request['isskipweek']) && $request['isskipweek'] !== '') {
            $isskipweek = intval($request['isskipweek']);
        } else {
            $isskipweek = 0;
        }

        
        $courseOne = $this->DataControl->selectOne("select c.class_cnname,s.course_inclasstype,s.course_cnname,s.course_id,s.course_openclasstype,s.course_weeklimittime*s.course_islimittime as course_weeklimittime,s.course_islimitopencycle,s.course_limitopencyclenum,s.course_weekstandardnum
        from smc_class as c, smc_course as s 
        where s.course_id = c.course_id 
        and c.class_id='{$request['class_id']}'");

        if ($courseOne['course_inclasstype'] == 3 && $courseOne['course_openclasstype'] == 1) {
            $sql = "select audition_id from view_crm_audition where class_id='{$request['class_id']}' and audition_isvisit>='0'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = 1;
                $this->errortip = "班级存在学员预约,不可执行此操作";
                return false;
            }
        }

        $sql = "select a.class_id,a.course_id,b.course_isopenwarm,a.class_hourwarmnums,a.class_hourwarmapplynums,b.course_isopenreview,a.class_hourreviewnums,a.class_hourreviewapplynum,b.course_canapplywarm,b.course_canapplyreview,b.course_reviewnum,a.class_ismustreview,a.class_hournums,a.class_stdate
                from smc_class as a 
                left join smc_course as b on b.course_id=a.course_id
                where a.class_id = '{$request['class_id']}'
                ";

        $classOne = $this->DataControl->selectOne($sql);

        if ($from == 1) {
            $sql = "select ch.*
		 from  smc_class_hour  as ch 
		 where ch.class_id='{$request['class_id']}' and ch.hour_lessontimes>='{$request['hour_lessontimes']}' and ch.hour_ischecking =0 order by hour_id ASC";

        } else {
            //公开课修改排课，有过预约记录的不能修改
            $couOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$classOne['course_id']}'");
            if($couOne['course_inclasstype'] == '3'){
                $sql = "select ch.*
                         from  smc_class_hour  as ch 
                         where ch.class_id='{$request['class_id']}' and ch.hour_ischecking =0 
                            and not exists (select 1 from crm_client_audition as au where au.hour_id=ch.hour_id limit 0,1)
                         order by hour_id ASC";
            }else{
                $sql = "select ch.*
                         from  smc_class_hour  as ch 
                         where ch.class_id='{$request['class_id']}' and ch.hour_ischecking =0 
                         and ch.hour_isfree=0
                         order by hour_id ASC";
            }
        }

        $hourList = $this->DataControl->selectClear($sql);

        $hour_num = count($hourList);
        if ($hour_num < 1) {
            $this->error = true;
            $this->errortip = "该班级不可修改排课！";
            return false;
        }

        if ($classOne['class_stdate'] > $request['start']) {
            $this->error = 1;
            $this->errortip = "请选择开班时间之后的日期";
            return false;
        }

        $hourOne = $this->DataControl->selectOne("select ch.hour_day
		    from  smc_class_hour  as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking =1 and  hour_day<'{$hourList[0]['hour_day']}'  order by hour_day DESC  limit 0,1");
        if ($hourOne && ($hourOne['hour_day'] >= $request['start'])) {
            $this->error = true;
            $this->errortip = "已有{$hourOne['hour_day']}之前点过名的课,请从{$hourOne['hour_day']}以后的时间开始排课!";
            return false;
        }

        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);

        $reArrangeList = json_decode(stripslashes($request['re_arrangeList']), true);

//        获取今天是周几  将最靠近日期往前排
        $day_weeknum = date('w', strtotime($request['start']));
        $day_weeknum = $day_weeknum == 0 ? '7' : $day_weeknum;

        if (is_array($arrangeList) && $arrangeList) {
            foreach ($arrangeList as $key => $arrOne) {
                if ($arrOne['weekday_id'] < $day_weeknum) {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'] + 7;
                } else {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'];
                }
            }
            $week_id_sort = array_column($arrangeList, 'week_id_sort');
            array_multisort($week_id_sort, SORT_ASC, $arrangeList);
        } else {
            $this->error = true;
            $this->errortip = "请设置正确的时间！";
            return false;
        }
        if($courseOne['course_weekstandardnum']>0 && count($arrangeList)<$courseOne['course_weekstandardnum']){
            $this->error = true;
            $this->errortip = "周排课计划不可少于{$courseOne['course_weekstandardnum']}次！";
            return false;
        }

        $holidayArray = array();
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $sql = "select ch.holidays_day from smc_code_holidays as ch
                  where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$request['start']}'
                  and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='0')
                       or
                       (ch.school_id='0' and ch.holidays_status='0' and  ch.company_id='{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='1')
                       )
                  )";

            $holidayList = $this->DataControl->selectClear($sql);
            if ($holidayList) {
                foreach ($holidayList as $holidayOne) {
                    $holidayArray[] = $holidayOne['holidays_day'];
                }
            }
            //排除假期
            sort($holidayArray);
        }

        $number = $hour_num;

        $dayListArray = $this->classModel->newArrangeDayList($request['start'], $request['end'], $number, $request['is_frequency'],$arrangeList,$holidayArray, $isskipweek, 0);

        $tem_plan = array();
        $timeListArray = array();

        $weektotal = 0;
        foreach ($arrangeList as $key => $arrangeOne) {

            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];
            $timeListArray[$key]['weekday_id'] = $arrangeOne['weekday_id'];
            $timeListArray[$key]['hour_starttime'] = $arrangeOne['hour_starttime'];
            $timeListArray[$key]['hour_endtime'] = $arrangeOne['hour_endtime'];
            $weektotal += (strtotime($timeListArray[$key]['hour_endtime']) - strtotime($timeListArray[$key]['hour_starttime'])) / 60;
        }


        if ($weektotal > $courseOne['course_weeklimittime'] and $courseOne['course_weeklimittime'] > 0) {
            $this->errortip = "周排课总分钟数大于课程设置的最大值" . $courseOne['course_weeklimittime'] . "min！";
            $this->error = "1";
            return false;
        }


        $temHour = array();

        if($courseOne['course_islimitopencycle']==1){
            $startTimestamp = strtotime($dayListArray[0]['day']);
            $endTimestamp = strtotime(date("Y-m-d",strtotime($dayListArray[count($dayListArray)-1]['day'])));

            $betweendays = abs(($endTimestamp - $startTimestamp) / (60 * 60 * 24));

            if($betweendays>$courseOne['course_limitopencyclenum']){
                $this->errortip = '排课周期不能大于'.$courseOne['course_limitopencyclenum'].'天';
                $this->error = "1";
                return false;
            }
        }

        //巩固复习课判断
        $re_dayListArray=array();
        if($request['is_skip']!='1'){
            if($reArrangeList){

                if (isset($request['re_isskipweek']) && $request['re_isskipweek'] !== '') {
                    $re_isskipweek = intval($request['re_isskipweek']);
                } else {
                    $re_isskipweek = 0;
                }
    
                if($classOne['course_isopenreview']==0){
                    $this->error = '1';
                    $this->errortip = '该课程无法创建复习课';
                    return false;
                }
    
                foreach ($reArrangeList as $key => $reArrangeOne) {
                    $timeListArray[$key+count($arrangeList)]['weekday_id'] = $reArrangeOne['weekday_id'];
                    $timeListArray[$key+count($arrangeList)]['hour_starttime'] = $reArrangeOne['hour_starttime'];
                    $timeListArray[$key+count($arrangeList)]['hour_endtime'] = $reArrangeOne['hour_endtime'];
                }
    
                //获取复习课列表    
                $sql = "SELECT b.hour_day,b.hour_lessontimes 
                        from smc_student_hourstudy as a
                        inner join smc_class_hour as b on b.hour_id=a.hour_id
                        where a.class_id='{$request['class_id']}' and b.hour_iswarming=2 and b.hour_ischecking=1
                        order by b.hour_lessontimes desc 
                        ";
                $reHourList = $this->DataControl->selectClear($sql);
    
                if($reHourList){
                    if($reHourList[0]['hour_day']>=$request['re_start']){
                        $request['re_start'] = date('Y-m-d', strtotime($reHourList[0]['hour_day'] . ' +1 day'));
                    }
    
                    // $needNum = $classOne['class_hourreviewnums'] + $classOne['class_hourreviewapplynum'] - count($reHourList);
                    
                }else{
                    // $needNum = $classOne['class_hourreviewnums'] + $classOne['class_hourreviewapplynum'];
                }
    
    
                $sql = "select a.hour_lessontimes,a.hour_id 
                        from smc_class_hour as a
                        where a.class_id='{$request['class_id']}' and a.hour_iswarming='2' and a.hour_ischecking=0
                        order by a.hour_lessontimes asc 
                        ";
    
                $re_minHourList = $this->DataControl->selectClear($sql);
    
                if($re_minHourList){
                    $re_holidayArray = array();
    
                    if (isset($request['re_skip_holidays']) && $request['re_skip_holidays'] == 1) {
                        
                        $sql = "select ch.holidays_day 
                                from smc_code_holidays as ch
                                where ch.company_id='{$request['company_id']}' and ch.holidays_day>='{$request['re_start']}'
                                and ((ch.school_id='{$request['school_id']}' and ch.holidays_status='0')
                                or
                                (ch.school_id='0' and ch.holidays_status='0' and  ch.company_id='{$request['company_id']}' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h where h.company_id='{$request['company_id']}' and h.school_id='{$request['school_id']}' and h.holidays_status='1'))
                                )";
        
                        $re_holidayList = $this->DataControl->selectClear($sql);
                        if ($re_holidayList) {
                            foreach ($re_holidayList as $holidayOne) {
                                $re_holidayArray[] = $holidayOne['holidays_day'];
                            }
                        }
                        //排除假期
                        sort($re_holidayArray);
                    }
        
                    $re_dayListArray = $this->classModel->newArrangeDayList($request['re_start'], $request['re_end'], count($re_minHourList), $request['is_frequency'],$reArrangeList,$re_holidayArray, $re_isskipweek, 0,2);
        
                    sort($re_dayListArray);
    
                    if($re_dayListArray[count($re_dayListArray)-1]['day']>$dayListArray[count($dayListArray)-1]['day']){
                        $this->error = '1';
                        $this->errortip = '复习课次结束时间不能大于排课结束时间';
                        return false;   
                    }
                }
    
            }else{
                if($classOne['class_ismustreview']==1){
                    $this->error = '1';
                    $this->errortip = '该课程必须创建复习课';
                    return false;
                }
            }
        }

        

        
        //检查时间冲突
        $time_day = $this->checkflictTime($timeListArray);
        if ($time_day !== true) {
            $time_week = self::$WORK_DAY[$time_day]['cn'];
            $this->errortip = $time_week . "所选时间段冲突";
            $this->error = "1";
            return false;
        }

        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$classOne['course_id']}'");

        if ($courseOne['course_inclasstype'] == 3) {
            $minHourOne = $this->DataControl->selectOne("select  h.hour_lessontimes from smc_class_hour as h where h.class_id='{$request['class_id']}' and h.hour_iswarming = 1 and hour_ischecking =0  order by  h.hour_lessontimes ASC  limit 0,1");
        } else {
            $minHourOne = $this->DataControl->selectOne("select  h.hour_lessontimes from smc_class_hour as h where h.class_id='{$request['class_id']}' and h.hour_iswarming = 0 and hour_ischecking =0  order by  h.hour_lessontimes ASC  limit 0,1");
        }

        if ($hourList) {
            $this->DataControl->delData("smc_class_lessonplan", "class_id='{$request['class_id']}'");
        }

        foreach ($arrangeList as $key => $arrangeOne) {
            $data = array();
            $data['class_id'] = $request['class_id'];
            $data['lessonplan_week'] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'];
            $data['lessonplan_weekno'] = $arrangeOne['weekday_id'];
            $data['classroom_id'] = $arrangeOne['classroom_id'];
            $data['staffer_id'] = $arrangeOne['staffer_id'];
            $data['teachtype_code'] = $arrangeOne['teachtype_code'];
            $data['poll_staffer_id'] = $arrangeOne['assistant_staffer_id'];
            $data['poll_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];
            $data['lessonplan_play'] = '1';
            $data['lessonplan_way'] = $arrangeOne['hour_way'];
            $data['lessonplan_starttime'] = $arrangeOne['hour_starttime'];
            $data['lessonplan_endtime'] = $arrangeOne['hour_endtime'];
            $data['lessonplan_isskipweek'] = $request['isskipweek'];

            if (isset($request['skip_holidays']) && $request['skip_holidays'] != '') {
                $data['lessonplan_isskipholidays'] = $request['skip_holidays'];
            }
            $data['lessonplan_createtime'] = time();

            $this->DataControl->insertData("smc_class_lessonplan", $data);
        }


        foreach ($dayListArray as $key=>$day) {
            $hourData = array();
            $hourData['course_id'] = $classOne['course_id'];
            $hourData['class_id'] = $request['class_id'];
            $hourData['classroom_id'] = $day['planOne']['classroom_id'];
            $hourData['hour_lessontimes'] = $key + $minHourOne['hour_lessontimes'];

            if ($courseOne['coursetype_isopenclass'] == 1) {
                $request['carrynum'] = 0;
                $request['last_carrynum'] = 0;
            }
            //双重模式 同时支持前期赠送 后期赠送
            if (isset($request['carrynum']) && $request['carrynum'] >= 0 && isset($request['last_carrynum']) && $request['last_carrynum'] >= 0) {

                if ($request['carrynum'] >= ($key + $minHourOne['hour_lessontimes'])) {
                    $hourData['hour_name'] = 'Lesson ' . ($key + $minHourOne['hour_lessontimes']);
                    $hourData['hour_isfree'] = 1;
                } elseif (($request['maxnum'] + $request['carrynum']) < ($key + $minHourOne['hour_lessontimes'])) {
                    $hourData['hour_name'] = 'Lesson ' . ($key + $minHourOne['hour_lessontimes']);
                    $hourData['hour_isfree'] = 1;
                } else {
                    if ($courseOne['coursetype_isopenclass'] == 1) {
                        $hourData['hour_name'] = 'Lesson ' . ($key + $minHourOne['hour_lessontimes']);
                        $hourData['hour_isfree'] = 1;
                    } else {
                        $hourData['hour_name'] = 'Lesson ' . ($key + $minHourOne['hour_lessontimes']);
                        $hourData['hour_isfree'] = 0;
                    }
                }
            }

            $hourData['hour_day'] = $day['day'];
            $hourData['hour_starttime'] = $day['planOne']['hour_starttime'];
            $hourData['hour_endtime'] = $day['planOne']['hour_endtime'];
            $hourData['hour_createtime'] = time();
            $hourData['hour_updatatime'] = time();
            $hourData['hour_formerday'] = $day['day'];
            //hour表不需要的字段
            $hourData['staffer_id'] = $day['planOne']['staffer_id'];
            $hourData['teachtype_code'] = $day['planOne']['teachtype_code'];
            $hourData['assistant_staffer_id'] = $day['planOne']['assistant_staffer_id'];
            $hourData['assistant_teachtype_code'] = $day['planOne']['assistant_teachtype_code'];
            $hourData['hour_way'] = $day['planOne']['hour_way'];
            if ($hourData['hour_way'] == 1) {
                $hourData['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
            }
            $hourData['hour_formertimes'] = $day['planOne']['hour_starttime'] . '-' . $day['planOne']['hour_endtime'];
            if (strtotime($day . " " . $day['planOne']['hour_starttime']) < strtotime($day . " 12:00")) {
                $hourData['hour_noon'] = 1;
            } elseif (strtotime($day . " " . $day['planOne']['hour_starttime']) > strtotime($day . " 18:00")) {
                $hourData['hour_noon'] = 3;
            } else {
                $hourData['hour_noon'] = 2;
            }
            $temHour[] = $hourData;
        }

        if ($hourList) {
            if ($temHour) {
                $arr_lessontimes = array_column($temHour, 'hour_lessontimes');
                array_multisort($arr_lessontimes, $temHour, SORT_ASC);
            }
            $hourtimes = 0;
            
            foreach ($hourList as $h_key => $hourOne) {
                if ($h_key == $hourtimes) {
//                if (($h_key + 1) == $temHour[$hourtimes]['hour_lessontimes']) {
                    $temHour[$hourtimes]['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                    if ($hourOne['hour_isfree'] == 1) {
                        $hour_name = 'Warm ' . $temHour[$hourtimes]['hour_lessontimes'];
                    } else {
                        $hour_name = 'Lesson ' . $temHour[$hourtimes]['hour_lessontimes'];
                    }
                    $temHour[$hourtimes]['hour_name'] = $hour_name;
                    $techData = array();
                    $techData['staffer_id'] = $temHour[$hourtimes]['staffer_id'];
                    $techData['hour_lessontimes'] = $temHour[$hourtimes]['hour_lessontimes'];
                    $techData['teaching_updatatime'] = time();
                    $techData['teachtype_code'] = $temHour[$hourtimes]['teachtype_code'];
                    //助教
                    $re_techData['staffer_id'] = $temHour[$hourtimes]['assistant_staffer_id'];
                    $re_techData['teachtype_code'] = $temHour[$hourtimes]['assistant_teachtype_code'];
                    $re_techData['hour_lessontimes'] = $temHour[$hourtimes]['hour_lessontimes'];
                    $re_techData['teaching_updatatime'] = time();
                    unset($temHour[$hourtimes]['staffer_id']);
                    unset($temHour[$hourtimes]['teachtype_code']);
                    unset($temHour[$hourtimes]['assistant_staffer_id']);
                    unset($temHour[$hourtimes]['assistant_teachtype_code']);
                    unset($temHour[$hourtimes]['hour_updatatime']);
                    $lineroomsOne = $this->DataControl->selectOne("select linerooms_number from  smc_linerooms where class_id='{$request['class_id']}' and hour_id='{$hourOne['hour_id']}'");
                    if ($temHour[$hourtimes]['hour_way'] == 1) {
                        if ($lineroomsOne && $lineroomsOne['linerooms_number'] !== '') {
                            $temHour[$hourtimes]['hour_number'] = $lineroomsOne['linerooms_number'];
                        } else {
                            $temHour[$hourtimes]['hour_number'] = $this->classModel->createHourNumber($request['class_id'], $hourOne['hour_id']);
                        }
                    } else {
                        $temHour[$hourtimes]['hour_number'] = '';
                    }
                    $temHour[$hourtimes]['hour_updatatime'] = time();
                    $boolCheck = false;
                    $oldhourOne = $this->DataControl->selectOne("select hour_day,hour_starttime,hour_endtime from  smc_class_hour where class_id='{$request['class_id']}' and hour_id='{$hourOne['hour_id']}'");
                    if (!$oldhourOne || $oldhourOne['hour_day'] !== $temHour[$hourtimes]['hour_day'] || $oldhourOne['hour_starttime'] !== $temHour[$hourtimes]['hour_starttime']|| $oldhourOne['hour_endtime'] !== $temHour[$hourtimes]['hour_endtime']) {
                        $boolCheck = true;
                    }
                    $bool = $this->DataControl->updateData("smc_class_hour", "hour_id='{$hourOne['hour_id']}' and  class_id='{$request['class_id']}'", $temHour[$hourtimes]);
                    if ($bool && $boolCheck) {
                        //改了排课，才取消预约
                        if ($hourOne['hour_ischecking'] == 0 && $this->DataControl->getOne('smc_class_booking', "hour_id ='{$hourOne['hour_id']}' and booking_status = 0")) {
                            $b_array = array();
                            $b_array['booking_status'] = '-1';
                            $b_array['booking_updatatime'] = time();
                            $this->DataControl->updateData('smc_class_booking', "hour_id='{$hourOne['hour_id']}'", $b_array);
                        }

                        $absence_houe = $this->DataControl->selectClear("SELECT absence_hour_id FROM smc_student_absence_hour WHERE hour_id = '{$hourOne['hour_id']}' AND absence_hour_status = '1'");
                        if ($absence_houe) {
                            foreach ($absence_houe as $item) {
                                $this->DataControl->updateData("smc_student_absence_hour", "absence_hour_id = '{$item['absence_hour_id']}'", array("absence_hour_status" => '-1'));
                            }
                        }
                        //更新CRM预约该课次的课 //增加跟踪记录
                        $this->addClientTrack($hourOne['hour_id'], '课程调整时间,取消试听');
                    }

                    $line_array = array();
                    $line_array['linerooms_starttime'] = strtotime($temHour[$hourtimes]['hour_day'] . ' ' . $temHour[$hourtimes]['hour_starttime']);
                    $line_array['linerooms_endtime'] = strtotime($temHour[$hourtimes]['hour_day'] . ' ' . $temHour[$hourtimes]['hour_endtime']);
                    $line_array['linerooms_name'] = $hour_name;
                    $line_array['linerooms_issync'] = "0";
                    $this->DataControl->updateData("smc_linerooms", "class_id='{$request['class_id']}' and hour_id='{$hourOne['hour_id']}'", $line_array);

                    $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$hourOne['hour_id']}'  and  class_id='{$request['class_id']}' and  teaching_type =0", $techData);

                    if ($re_techData['staffer_id']) {
                        $hourteach = $this->DataControl->getOne("smc_class_hour_teaching", "hour_id='{$hourOne['hour_id']}'  and  class_id='{$request['class_id']}' and  teaching_type =1");
                        if ($hourteach) {
                            $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$hourOne['hour_id']}'  and  class_id='{$request['class_id']}' and  teaching_type =1", $re_techData);
                        } else {
                            $tech_data = array();
                            $tech_data['hour_id'] = $hourOne['hour_id'];
                            $tech_data['class_id'] = $hourOne['class_id'];
                            $tech_data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                            $tech_data['staffer_id'] = $re_techData['staffer_id'];
                            $tech_data['teaching_type'] = 1;
                            $tech_data['teachtype_code'] = $re_techData['teachtype_code'];
                            $tech_data['teaching_createtime'] = time();
                            $this->DataControl->insertData("smc_class_hour_teaching", $tech_data);
                        }
                    } else {
                        $tech_data = array();
                        $tech_data['teaching_isdel'] = '1';
                        $tech_data['teaching_updatatime'] = time();
                        $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$hourOne['hour_id']}' and class_id='{$request['class_id']}' and teaching_type =1", $tech_data);
                    }
                }
                $hourtimes++;
            }
        }


        if($request['is_skip']!='1'){
            if($re_dayListArray){

                $this->DataControl->delData("smc_class_reviewplan", "class_id='{$request['class_id']}' ");
    
                foreach ($reArrangeList as $key => $re_arrangeOne) {
                    //排课计划
        
                    $data = array();
                    $data['class_id'] = $request['class_id'];
                    $data['reviewplan_week'] = self::$WORK_DAY[$re_arrangeOne['weekday_id']]['cn'];
                    $data['reviewplan_weekno'] = $re_arrangeOne['weekday_id'];
                    $data['classroom_id'] = $re_arrangeOne['classroom_id'];
                    $data['staffer_id'] = $re_arrangeOne['staffer_id'];
                    $data['teachtype_code'] = $re_arrangeOne['teachtype_code'];
                    $data['poll_staffer_id'] = $re_arrangeOne['assistant_staffer_id'];
                    $data['poll_teachtype_code'] = $re_arrangeOne['assistant_teachtype_code'];
                    $data['reviewplan_starttime'] = $re_arrangeOne['hour_starttime'];
                    $data['reviewplan_endtime'] = $re_arrangeOne['hour_endtime'];
                    $data['reviewplan_isskipweek'] = $request['re_isskipweek'];
                    $data['reviewplan_way'] = $re_arrangeOne['hour_way'];
                    $data['reviewplan_createtime'] = time();
                    $this->DataControl->insertData("smc_class_reviewplan", $data);
                }
    
                foreach($re_minHourList as $rekey=>$re_hourOne){
    
                    $data = array();
                    $data['hour_lessontimes'] = $re_hourOne['hour_lessontimes'];
                    $data['class_id'] = $request['class_id'];
                    $data['course_id'] = $classOne['course_id'];
                    $data['classroom_id'] = $re_dayListArray[$rekey]['planOne']['classroom_id'];
                    $data['hour_name'] = 'Review  ' . $data['hour_lessontimes'];
                    $data['hour_isfree'] = '1';
                    $data['hour_day'] = $re_dayListArray[$rekey]['day'];

                    $data['hour_way'] = $re_dayListArray[$rekey]['planOne']['hour_way'];
                    if ($re_dayListArray[$rekey]['planOne']['hour_way'] == 1) {
                        $data['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
                    }


                    $data['hour_iswarming'] = 2;
                    $data['hour_starttime'] = $re_dayListArray[$rekey]['planOne']['hour_starttime'];
                    $data['hour_endtime'] = $re_dayListArray[$rekey]['planOne']['hour_endtime'];
                    $data['hour_createtime'] = time();
                    $data['hour_updatatime'] = time();
    
                    if ($id = $this->DataControl->updateData("smc_class_hour","class_id='{$request['class_id']}' and hour_lessontimes='{$re_hourOne['hour_lessontimes']}' and hour_iswarming=2 and hour_ischecking=0", $data)) {
                        if($re_dayListArray[$rekey]['planOne']['staffer_id']>0){
    
                            $teachingData = array();
                            $teachingData['class_id'] = $request['class_id'];
                            $teachingData['hour_id'] = $id;
                            $teachingData['hour_lessontimes'] = $data['hour_lessontimes'];
                            $teachingData['staffer_id'] = $re_dayListArray[$rekey]['planOne']['staffer_id'];
                            $teachingData['teaching_type'] = 0;
                            $teachingData['teachtype_code'] = $re_dayListArray[$rekey]['planOne']['teachtype_code'];
    
                            if($this->DataControl->getOne("smc_class_hour_teaching","hour_id='{$id}' and teaching_type=0")){
                                $teachingData['teaching_updatatime'] = time();
                                $this->DataControl->updateData("smc_class_hour_teaching","hour_id='{$id}' and teaching_type=0", $teachingData);
                            }else{
                                $teachingData['teaching_createtime'] = time();
                                $this->DataControl->insertData("smc_class_hour_teaching", $teachingData);
                            }
                            
                        }
                        
    
                        if($re_dayListArray[$rekey]['planOne']['assistant_staffer_id']>0){
    
                            $fu_teachingData = array();
                            $fu_teachingData['class_id'] = $request['class_id'];
                            $fu_teachingData['hour_id'] = $id;
                            $fu_teachingData['hour_lessontimes'] = $data['hour_lessontimes'];
                            $fu_teachingData['staffer_id'] = $re_dayListArray[$rekey]['planOne']['assistant_staffer_id'];
                            $fu_teachingData['teaching_type'] = 1;
                            $fu_teachingData['teachtype_code'] = $re_dayListArray[$rekey]['planOne']['assistant_teachtype_code'];
                            if($this->DataControl->getOne("smc_class_hour_teaching","hour_id='{$id}' and teaching_type=1")){
                                $fu_teachingData['teaching_updatatime'] = time();
                                $this->DataControl->updateData("smc_class_hour_teaching","hour_id='{$id}' and teaching_type=1", $fu_teachingData);
                            }else{
                                $fu_teachingData['teaching_createtime'] = time();
                                $this->DataControl->insertData("smc_class_hour_teaching", $fu_teachingData);
                            }
                            
                        }
                    }
                }
            }
        }
        
        $arr_last_day = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$request['class_id']}'", "order by hour_day DESC");
        $last_day = $arr_last_day['hour_day'];
        $json = array();
        $json['class_timestr'] = implode(",", $tem_plan);
        $json['class_enddate'] = date('Y-m-d', strtotime("$last_day"));
        $json['class_lastscheduledate'] = $request['start'];
        $json['class_updatatime'] = time();
        if ($courseOne['course_inclasstype'] == 2) {
            $json['class_appointnum'] = $request['class_appointnum'];
        }

        if ($this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $json)) {
            $studyData = array();
            $studyData['study_endday'] = $last_day;
            $studyData['study_updatetime'] = time();
            $this->DataControl->updateData("smc_student_study", "study_isreading =1 and class_id='{$request['class_id']}'", $studyData);
        }
        return true;
    }

    function againLessonPlan($request){

        $classOne=$this->DataControl->getFieldOne("smc_class","class_id,class_status,class_stdate","class_id='{$request['class_id']}'");

        if(!$classOne){
            $this->error = true;
            $this->errortip = "班级不存在";
            return false;
        }

        if($classOne['class_status']<0){
            $this->error = true;
            $this->errortip = "班级未开班";
            return false;
        }

        $sql = "select a.lessonplan_weekno as weekday_id,a.staffer_id,a.classroom_id,a.lessonplan_starttime as hour_starttime,a.lessonplan_endtime as hour_endtime,a.lessonplan_way as hour_way,a.teachtype_code as teachtype_code,a.poll_staffer_id as assistant_staffer_id,a.poll_teachtype_code as assistant_teachtype_code,a.lessonplan_isskipweek
                from smc_class_lessonplan as a 
                where a.class_id='{$request['class_id']}'
                order by a.lessonplan_id asc 
                ";
        $planList=$this->DataControl->selectClear($sql);

        if(!$planList){
            $this->error = true;
            $this->errortip = "班级无排课计划,无法重新排课";
            return false;
        }

        $sql = "select b.hour_day 
                from smc_student_hourstudy as a 
                inner join smc_class_hour as b on b.hour_id=a.hour_id
                where a.class_id='{$request['class_id']}'
                order by b.hour_day desc,b.hour_starttime desc
                limit 0,1
                ";

        $hourstudyOne=$this->DataControl->selectOne($sql);

        $data=array();
        $data['company_id']=$this->company_id;
        $data['school_id']=$this->school_id;
        $data['staffer_id']=$this->staffer_id;
        $data['start']=$hourstudyOne?date("Y-m-d", strtotime('+1 day',strtotime($hourstudyOne['hour_day']))):$classOne['class_stdate'];
        $data['class_id']=$request['class_id'];
        $data['isskipweek']=$planList[0]['lessonplan_isskipweek'];
        $data['skip_holidays']=1;
        $data['is_frequency']=1;
        $data['is_skip']=1;
        $data['arrangeList']=json_encode($planList,JSON_UNESCAPED_UNICODE);
        $bool=$this->modifyLessonPlan($data);

        if($bool){
            return true;
        }else{
            $this->error = true;
            return false;
        }

    }

    /**
     * 增加跟踪记录
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/1 0001
     */
    private function addClientTrack($hour_id, $track_note = '')
    {
        $audtionList = $this->DataControl->selectClear("select au.client_id,au.school_id from crm_client_audition as au where au.hour_id='{$hour_id}' and au.hour_id>0 and audition_isvisit =0 ");
        $crm_audition = array();
        $crm_audition['audition_isvisit'] = '-1';
        $crm_audition['audition_novisitreason'] = '课程调整时间,取消试听';
        $crm_audition['audition_updatetime'] = time();
        $this->DataControl->updateData("crm_client_audition", "hour_id='{$hour_id}' and hour_id >0 and audition_isvisit =0", $crm_audition);
        $class_audition = array();
        $class_audition['audition_isvisit'] = '-1';
        $class_audition['audition_novisitreason'] = '课程调整时间,取消试听';
        $this->DataControl->updateData("smc_class_hour_audition", "hour_id='{$hour_id}' and hour_id >0 and audition_isvisit =0", $class_audition);

        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name,marketer_id", "staffer_id='{$this->staffer_id}'");
        if (!$marketerOne) {
            $stafferOne = $this->stafferOne;
            $marketer_data = array();
            $marketer_data['staffer_id'] = $stafferOne['staffer_id'];
            $marketer_data['company_id'] = $stafferOne['company_id'];
            $marketer_data['marketer_name'] = $stafferOne['staffer_cnname'];
            $marketer_data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $marketer_data['marketer_img'] = $stafferOne['staffer_img'];
            $marketer_data['marketer_createtime'] = time();
            $id = $this->DataControl->insertData("crm_marketer", $marketer_data);

            $marketerOne = array();
            $marketerOne['marketer_id'] = $id;
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        if ($audtionList) {
            foreach ($audtionList as $key => $value) {
                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['school_id'] = $value['school_id'];
                $trackData['marketer_name'] = $marketerOne['marketer_name'];
                $trackData['marketer_id'] = $marketerOne['marketer_id'];
                $trackData['track_validinc'] = '1';
                $trackData['track_linktype'] = '取消试听';
                $trackData['track_note'] = $track_note;
                $trackData['track_isactive'] = '0';
                $trackData['track_createtime'] = time();
                $this->DataControl->insertData("crm_client_track", $trackData);
            }
        }
    }

    /**
     * 无限排课
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function InfiniteClassSchedule($request)
    {
        if (isset($request['isskipweek']) && $request['isskipweek'] !== '') {
            $isskipweek = 0;
        } else {
            $isskipweek = 0;
        }
        $hourOne = $this->DataControl->selectOne("select ch.hour_day
		    from  smc_class_hour  as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking =1  order by hour_day DESC  limit 0,1");
        if ($hourOne && ($hourOne['hour_day'] >= $request['start'])) {
            $this->error = true;
            $this->errortip = "请从{$hourOne['hour_day']}以后的时间开始排课!";
            return false;
        }

        $courseOne = $this->DataControl->selectOne("
					select  ct.coursetype_isopenclass,course_inclasstype,co.course_islimitopencycle,co.course_limitopencyclenum,co.course_weekstandardnum 
					from  smc_class as c
					left join  smc_course as  co on c.course_id = co.course_id
				    left join  smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
 				    where c.class_id='{$request['class_id']}' ");


        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_lessontimes,hour_day", "class_id='{$request['class_id']}'", 'Order
		 by hour_lessontimes DESC Limit 0,1');
        if ($hourOne && ($hourOne['hour_day'] >= $request['start'])) {
            $this->error = 1;
            $this->errortip = "请选择'{$hourOne['hour_day']}'以后的日期";
            return false;
        }


        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        //        获取今天是周几  将最靠近日期往前排
        $day_weeknum = date('w', strtotime($request['start']));
        $day_weeknum = $day_weeknum == 0 ? '7' : $day_weeknum;

        if (is_array($arrangeList) && $arrangeList) {
            foreach ($arrangeList as $key => $arrOne) {
                if ($arrOne['weekday_id'] < $day_weeknum) {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'] + 7;
                } else {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'];
                }
            }
            $week_id_sort = array_column($arrangeList, 'week_id_sort');
            array_multisort($week_id_sort, SORT_ASC, $arrangeList);
        } else {
            $this->error = true;
            $this->errortip = "请设置正确的时间！";
            return false;
        }

        if($courseOne['course_weekstandardnum']>0 && count($arrangeList)<$courseOne['course_weekstandardnum']){
            $this->error = true;
            $this->errortip = "周排课计划不可少于{$courseOne['course_weekstandardnum']}次！";
            return false;
        }


        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");
        if ($request['carrynum'] <= 0) {
            $request['carrynum'] = 0;
        }
        if ($request['last_carrynum'] <= 0) {
            $request['last_carrynum'] = 0;
        }
        $number = $request['maxnum'] + $request['carrynum'] + $request['last_carrynum'];
        $total = count($arrangeList);
        $numArray = $this->AverageDivisionNumber($number, $total);
        if (!$numArray) {
            $this->error = 1;
            $this->errortip = '请检查排课';
            return false;
        }

        $holidayArray = array();
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
//			获取放假的日期 ,排除掉上课的日期
            $schoolHolidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 and holidays_status=0  and holidays_day >='{$request['start']}'");     //学校放假
            $un_schoolHolidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 and holidays_status=1  and holidays_day >='{$request['start']}'"); //学校上课

            $holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}' and school_id=0 and holidays_status=0  and holidays_day >='{$request['start']}' and holidays_day not in (select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 )");    //集团放假 过滤 掉学校设置过的时间
            $un_holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}' and school_id=0 and holidays_status=1  and holidays_day >='{$request['start']}' and  holidays_day not in (select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 ) ");  //集团上课滤 掉学校设置过的时间


            $holidayArray = array();   //放假的
            if ($schoolHolidayList) {
                foreach ($schoolHolidayList as $val) {
                    $holidayArray[] = $val['holidays_day'];
                }
            }
            if ($holidayList) {
                foreach ($holidayList as $val) {
                    $holidayArray[] = $val['holidays_day'];
                }
            }
            $un_holidayArray = array();  //上课的
            if ($un_schoolHolidayList) {
                foreach ($un_schoolHolidayList as $val) {
                    $un_holidayArray[] = $val['holidays_day'];
                }
            }
            if ($un_holidayList) {
                foreach ($un_holidayList as $val) {
                    $un_holidayArray[] = $val['holidays_day'];
                }
            }
            $holidayArray = array_diff($holidayArray, $un_holidayArray);
        }
        $tem_plan = array();
        $dayListArray = array();
        $timeListArray = array();
        foreach ($arrangeList as $key => $arrangeOne) {

            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];

            $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], array(), $isskipweek, 0);

            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
//                $intersectDay = array_intersect($holidayArray, $weekList);
//                $addNum = count($intersectDay) + $numArray[$key];
                $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], $holidayArray, $isskipweek, 0);
//                $weekList = array_diff($weekList, $intersectDay);
            }

            foreach ($weekList as $day) {
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    if (in_array($day, $holidayArray)) {
                        continue;
                    }
                }
                $dayListArray[] = $day . ' ' . $arrangeOne['hour_starttime'];
            }
            $timeListArray[$key]['weekday_id'] = $arrangeOne['weekday_id'];
            $timeListArray[$key]['hour_starttime'] = $arrangeOne['hour_starttime'];
            $timeListArray[$key]['hour_endtime'] = $arrangeOne['hour_endtime'];
        }

        //检查时间冲突  6/16 原本一天只能排一节课
        $time_day = $this->checkflictTime($timeListArray, 0);
        if ($time_day !== true) {
            $time_week = self::$WORK_DAY[$time_day]['cn'];
            $this->errortip = $time_week . "排课时间有交叉,请修改";
            $this->error = "1";
            return false;
        }

        sort($dayListArray);
        $array = array_flip($dayListArray);

        if($courseOne['course_islimitopencycle']==1){
            $startTimestamp = strtotime($dayListArray[0]['day']);
            $endTimestamp = strtotime(date("Y-m-d",strtotime($dayListArray[count($dayListArray)-1]['day'])));

            $betweendays = abs(($endTimestamp - $startTimestamp) / (60 * 60 * 24));

            if($betweendays>$courseOne['course_limitopencyclenum']){
                $this->errortip = '排课周期不能大于'.$courseOne['course_limitopencyclenum'].'天';
                $this->error = "1";
                return false;
            }
        }


        $temHour = array();
        if ($hourOne) {
            $this->DataControl->delData("smc_class_lessonplan", "class_id='{$request['class_id']}'");
        }
        foreach ($arrangeList as $key => $arrangeOne) {

            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];

            $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], array(), $isskipweek, 0);
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
//                $intersectDay = array_intersect($holidayArray, $weekList);
//                $addNum = count($intersectDay) + $numArray[$key];
                $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], $holidayArray, $isskipweek, 0);
//                $weekList = array_diff($weekList, $intersectDay);
            }

            foreach ($weekList as $h_key => $day) {
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    if (in_array($day, $holidayArray)) {
                        continue;
                    }
                }
                $day_time = $day . ' ' . $arrangeOne['hour_starttime'];

                $hourData = array();
                $hourData['course_id'] = $classOne['course_id'];
                $hourData['class_id'] = $request['class_id'];
                $hourData['classroom_id'] = $arrangeOne['classroom_id'];
                $hourData['hour_way'] = $arrangeOne['hour_way'];
                if ($hourData['hour_way'] == 1) {
                    $hourData['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
                }
                $hourData['hour_lessontimes'] = $array[$day_time] + 1 + $hourOne['hour_lessontimes'];

                if ($courseOne['coursetype_isopenclass'] == 1) {
                    $request['carrynum'] = 0;
                    $request['last_carrynum'] = 0;
                }

                //双重模式 同时支持前期赠送 后期赠送
                if (isset($request['carrynum']) && $request['carrynum'] >= 0 && isset($request['last_carrynum']) && $request['last_carrynum'] >= 0) {

                    if ($request['carrynum'] >= ($array[$day_time] + 1)) {
                        $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1 + $hourOne['hour_lessontimes']);
                        $hourData['hour_isfree'] = 1;
                    } elseif (($request['maxnum'] + $request['carrynum']) < ($array[$day_time] + 1)) {
                        $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1 + $hourOne['hour_lessontimes']);
                        $hourData['hour_isfree'] = 1;
                    } else {
                        if ($courseOne['coursetype_isopenclass'] == 1) {
                            $hourData['hour_name'] = 'Warm ' . ($array[$day_time] + 1 + $hourOne['hour_lessontimes']);
                            $hourData['hour_isfree'] = 1;
                            $hourData['hour_iswarming'] = 1;
                        } else {
                            $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1 + $hourOne['hour_lessontimes']);
                            $hourData['hour_isfree'] = 0;
                            $hourData['hour_iswarming'] = 0;
                        }
                    }
                }

                $hourData['hour_day'] = $day;
                $hourData['hour_starttime'] = $arrangeOne['hour_starttime'];
                $hourData['hour_endtime'] = $arrangeOne['hour_endtime'];
                $hourData['hour_createtime'] = time();
                $hourData['hour_updatatime'] = time();
                $hourData['hour_formerday'] = $day;
                //hour表不需要的字段
                $hourData['staffer_id'] = $arrangeOne['staffer_id'];
                $hourData['teachtype_code'] = $arrangeOne['teachtype_code'];
                $hourData['assistant_staffer_id'] = $arrangeOne['assistant_staffer_id'];
                $hourData['assistant_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];

                $hourData['hour_formertimes'] = $arrangeOne['hour_starttime'] . '-' . $arrangeOne['hour_endtime'];
                if (strtotime($day . " " . $arrangeOne['hour_starttime']) < strtotime($day . " 12:00")) {
                    $hourData['hour_noon'] = 1;
                } elseif (strtotime($day . " " . $arrangeOne['hour_starttime']) > strtotime($day . " 18:00")) {
                    $hourData['hour_noon'] = 3;
                } else {
                    $hourData['hour_noon'] = 2;
                }
                $temHour[] = $hourData;
            }


            $data = array();
            $data['class_id'] = $request['class_id'];
            $data['lessonplan_week'] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'];
            $data['lessonplan_weekno'] = $arrangeOne['weekday_id'];
            $data['classroom_id'] = $arrangeOne['classroom_id'];
            $data['staffer_id'] = $arrangeOne['staffer_id'];
            $data['teachtype_code'] = $arrangeOne['teachtype_code'];
            $data['poll_staffer_id'] = $arrangeOne['assistant_staffer_id'];
            $data['poll_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];
            $data['lessonplan_play'] = '1';
            $data['lessonplan_starttime'] = $arrangeOne['hour_starttime'];
            $data['lessonplan_endtime'] = $arrangeOne['hour_endtime'];
            $data['lessonplan_isskipweek'] = $request['isskipweek'];
            $data['lessonplan_createtime'] = time();
            $this->DataControl->insertData("smc_class_lessonplan", $data);

        }
        if ($temHour) {
            $arr_lessontimes = array_column($temHour, 'hour_lessontimes');
            array_multisort($arr_lessontimes, $temHour, SORT_ASC);

            foreach ($temHour as $key => $value) {
                $techData = array();
                $re_techData = array();
                $techData['staffer_id'] = $value['staffer_id'];
                $techData['hour_lessontimes'] = $value['hour_lessontimes'];
                $techData['teaching_createtime'] = time();
                $techData['teaching_type'] = 0;
                $techData['class_id'] = $value['class_id'];
                $techData['teachtype_code'] = $value['teachtype_code'];
                //助教
                $re_techData['staffer_id'] = $value['assistant_staffer_id'];
                $re_techData['teachtype_code'] = $value['assistant_teachtype_code'];

                unset($value['staffer_id']);
                unset($value['teachtype_code']);
                unset($value['assistant_staffer_id']);
                unset($value['assistant_teachtype_code']);

                $hour_id = $this->DataControl->insertData("smc_class_hour", $value);

                if ($value['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = $value['hour_starttime'];
                    $lineData['linerooms_endtime'] = $value['hour_endtime'];
                    $lineData['linerooms_name'] = $value['hour_name'];
                    $this->updateHourNumber($value['hour_number'], $hour_id, $lineData);
                }

                $techData['hour_id'] = $hour_id;

                $this->DataControl->insertData("smc_class_hour_teaching", $techData);
                if ($re_techData['staffer_id']) {

                    $re_techData['hour_lessontimes'] = $value['hour_lessontimes'];
                    $re_techData['teaching_createtime'] = time();
                    $re_techData['teaching_type'] = 1;
                    $re_techData['class_id'] = $value['class_id'];
                    $re_techData['hour_id'] = $hour_id;

                    $this->DataControl->insertData("smc_class_hour_teaching", $re_techData);
                }
                $last_day = $value['hour_day'];
            }
        }
        $json = array();
        $json['class_timestr'] = implode(",", $tem_plan);
        $json['class_enddate'] = date('Y-m-d', strtotime("$last_day"));
        $json['class_updatatime'] = time();
        if ($courseOne['course_inclasstype'] == 2) {
            $json['class_appointnum'] = $request['class_appointnum'];
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_enddate", "class_id='{$request['class_id']}'");
        if (($classOne['class_enddate'] < $last_day) or !$classOne['class_enddate']) {
            if ($this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $json)) {
                $studyData = array();
                $studyData['study_endday'] = $last_day;
                $studyData['study_updatetime'] = time();
                $this->DataControl->updateData("smc_student_study", "  study_isreading =1 and class_id='{$request['class_id']}'", $studyData);
            }
        }
        return true;
    }

    /**
     * 新增公开课排课
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/22 0022
     * @param $request
     */
    function addOpenClassSchedule($request)
    {


        $sql = "call RefreshClass('{$request['class_id']}')";
        $this->DataControl->selectClear($sql);

        if (isset($request['isskipweek']) && $request['isskipweek'] !== '') {
            $isskipweek = 0;
        } else {
            $isskipweek = 0;
        }
        $hourOne = $this->DataControl->selectOne("select ch.hour_day
		    from  smc_class_hour  as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking =1  order by hour_day DESC  limit 0,1");
        if ($hourOne && ($hourOne['hour_day'] > $request['start'])) {
            $this->error = true;
            $this->errortip = "请从{$hourOne['hour_day']}以后的时间开始排课!";
            return false;
        }
        $courseOne = $this->DataControl->selectOne("
					select  ct.coursetype_isopenclass,course_inclasstype,co.course_weekstandardnum from  smc_class as c
					left join  smc_course as  co on c.course_id = co.course_id
				    left join  smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
 				    where c.class_id='{$request['class_id']}' ");
        if ($courseOne['coursetype_isopenclass'] == 0) {
            $this->error = 1;
            $this->errortip = "非公开课不适用此功能";
            return false;
        }

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_lessontimes,hour_day", "class_id='{$request['class_id']}' and hour_ischecking<>'-1'", 'Order
		 by hour_lessontimes DESC Limit 0,1');
        if ($hourOne && ($hourOne['hour_day'] > $request['start'])) {
            $this->error = 1;
            $this->errortip = "请选择'{$hourOne['hour_day']}'以后的日期";
            return false;
        }
        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        //        获取今天是周几  将最靠近日期往前排
        $day_weeknum = date('w', strtotime($request['start']));
        $day_weeknum = $day_weeknum == 0 ? '7' : $day_weeknum;

        if (is_array($arrangeList) && $arrangeList) {
            foreach ($arrangeList as $key => $arrOne) {
                if ($arrOne['weekday_id'] < $day_weeknum) {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'] + 7;
                } else {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'];
                }
            }
            $week_id_sort = array_column($arrangeList, 'week_id_sort');
            array_multisort($week_id_sort, SORT_ASC, $arrangeList);
        } else {
            $this->error = true;
            $this->errortip = "请设置正确的时间！";
            return false;
        }

        if($courseOne['course_weekstandardnum']>0 && count($arrangeList)<$courseOne['course_weekstandardnum']){
            $this->error = true;
            $this->errortip = "周排课计划不可少于{$courseOne['course_weekstandardnum']}次！";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");
        if ($request['carrynum'] <= 0) {
            $request['carrynum'] = 0;
        }
        if ($request['last_carrynum'] <= 0) {
            $request['last_carrynum'] = 0;
        }
        $number = $request['maxnum'] + $request['carrynum'] + $request['last_carrynum'];
        $total = count($arrangeList);
        $numArray = $this->AverageDivisionNumber($number, $total);
        if (!$numArray) {
            $this->error = 1;
            $this->errortip = '请检查排课';
            return false;
        }
        if ($hourOne) {
            $this->DataControl->delData("smc_class_lessonplan", "class_id='{$request['class_id']}'");
        }

        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_lessontimes,hour_day", "class_id='{$request['class_id']}'", 'Order
		 by hour_lessontimes DESC Limit 0,1');

        $holidayArray = array();
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $schoolHolidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 and holidays_status=0  and holidays_day >='{$request['start']}'");     //学校放假
            $un_schoolHolidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 and holidays_status=1  and holidays_day >='{$request['start']}'"); //学校上课
            $holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}' and school_id=0 and holidays_status=0  and holidays_day >='{$request['start']}' and holidays_day not in (select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 )");    //集团放假 过滤 掉学校设置过的时间
            $un_holidayList = $this->DataControl->selectClear("select holidays_day from smc_code_holidays where company_id='{$request['company_id']}' and school_id=0 and holidays_status=1  and holidays_day >='{$request['start']}' and  holidays_day not in (select holidays_day from smc_code_holidays where school_id='{$request['school_id']}' and company_id=0 ) ");  //集团上课滤 掉学校设置过的时间

            $holidayArray = array();   //放假的
            if ($schoolHolidayList) {
                foreach ($schoolHolidayList as $val) {
                    $holidayArray[] = $val['holidays_day'];
                }
            }
            if ($holidayList) {
                foreach ($holidayList as $val) {
                    $holidayArray[] = $val['holidays_day'];
                }
            }
            $un_holidayArray = array();  //上课的
            if ($un_schoolHolidayList) {
                foreach ($un_schoolHolidayList as $val) {
                    $un_holidayArray[] = $val['holidays_day'];
                }
            }
            if ($un_holidayList) {
                foreach ($un_holidayList as $val) {
                    $un_holidayArray[] = $val['holidays_day'];
                }
            }
            $holidayArray = array_diff($holidayArray, $un_holidayArray);
        }
        $tem_plan = array();
        $dayListArray = array();
        $timeListArray = array();
        foreach ($arrangeList as $key => $arrangeOne) {
            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];
            $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], array(), $isskipweek, 0);
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], $holidayArray, $isskipweek, 0);
            }
            foreach ($weekList as $day) {
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    if (in_array($day, $holidayArray)) {
                        continue;
                    }
                }
                $dayListArray[] = $day . ' ' . $arrangeOne['hour_starttime'];
            }
            $timeListArray[$key]['weekday_id'] = $arrangeOne['weekday_id'];
            $timeListArray[$key]['hour_starttime'] = $arrangeOne['hour_starttime'];
            $timeListArray[$key]['hour_endtime'] = $arrangeOne['hour_endtime'];
        }

        //检查时间冲突  6/16 原本一天只能排一节课
        $time_day = $this->checkflictTime($timeListArray, 0);
        if ($time_day !== true) {
            $time_week = self::$WORK_DAY[$time_day]['cn'];
            $this->errortip = $time_week . "排课时间有交叉,请修改";
            $this->error = "1";
            return false;
        }
        sort($dayListArray);
        $array = array_flip($dayListArray);
        $temHour = array();
        foreach ($arrangeList as $key => $arrangeOne) {
            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];
            $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], array(), $isskipweek, 0);
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], $holidayArray, $isskipweek, 0);
            }

            foreach ($weekList as $h_key => $day) {

                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    if (in_array($day, $holidayArray)) {
                        continue;
                    }
                }

                $day_time = $day . ' ' . $arrangeOne['hour_starttime'];

                $hourData = array();
                $hourData['course_id'] = $classOne['course_id'];
                $hourData['class_id'] = $request['class_id'];
                $hourData['classroom_id'] = $arrangeOne['classroom_id'];
                $hourData['hour_way'] = $arrangeOne['hour_way'];
                if ($hourData['hour_way'] == 1) {
                    $hourData['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
                }
                $hourData['hour_lessontimes'] = $array[$day_time] + 1 + $hourOne['hour_lessontimes'];

                if ($courseOne['coursetype_isopenclass'] == 1) {
                    $request['carrynum'] = 0;
                    $request['last_carrynum'] = 0;
                }
                //这里只有公开课
                $hourData['hour_name'] = 'Warm ' . ($array[$day_time] + 1 + $hourOne['hour_lessontimes']);
                $hourData['hour_isfree'] = 1;
                $hourData['hour_iswarming'] = 1;


                $hourData['hour_day'] = $day;
                $hourData['hour_starttime'] = $arrangeOne['hour_starttime'];
                $hourData['hour_endtime'] = $arrangeOne['hour_endtime'];
                $hourData['hour_createtime'] = time();
                $hourData['hour_updatatime'] = time();
                $hourData['hour_formerday'] = $day;
                //hour表不需要的字段
                $hourData['staffer_id'] = $arrangeOne['staffer_id'];
                $hourData['teachtype_code'] = $arrangeOne['teachtype_code'];
                $hourData['assistant_staffer_id'] = $arrangeOne['assistant_staffer_id'];
                $hourData['assistant_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];

                $hourData['hour_formertimes'] = $arrangeOne['hour_starttime'] . '-' . $arrangeOne['hour_endtime'];
                if (strtotime($day . " " . $arrangeOne['hour_starttime']) < strtotime($day . " 12:00")) {
                    $hourData['hour_noon'] = 1;
                } elseif (strtotime($day . " " . $arrangeOne['hour_starttime']) > strtotime($day . " 18:00")) {
                    $hourData['hour_noon'] = 3;
                } else {
                    $hourData['hour_noon'] = 2;
                }
                $temHour[] = $hourData;
            }

            $data = array();
            $data['class_id'] = $request['class_id'];
            $data['lessonplan_week'] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'];
            $data['lessonplan_weekno'] = $arrangeOne['weekday_id'];
            $data['classroom_id'] = $arrangeOne['classroom_id'];
            $data['staffer_id'] = $arrangeOne['staffer_id'];
            $data['teachtype_code'] = $arrangeOne['teachtype_code'];
            $data['poll_staffer_id'] = $arrangeOne['assistant_staffer_id'];
            $data['poll_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];
            $data['lessonplan_play'] = '1';
            $data['lessonplan_starttime'] = $arrangeOne['hour_starttime'];
            $data['lessonplan_endtime'] = $arrangeOne['hour_endtime'];
            $data['lessonplan_isskipweek'] = $request['isskipweek'];
            $data['lessonplan_createtime'] = time();
            $this->DataControl->insertData("smc_class_lessonplan", $data);

        }


        if ($temHour) {
            $arr_lessontimes = array_column($temHour, 'hour_lessontimes');
            array_multisort($arr_lessontimes, $temHour, SORT_ASC);

            foreach ($temHour as $key => $value) {
                $techData = array();
                $re_techData = array();
                $techData['staffer_id'] = $value['staffer_id'];
                $techData['hour_lessontimes'] = $value['hour_lessontimes'];
                $techData['teaching_createtime'] = time();
                $techData['teaching_type'] = 0;
                $techData['class_id'] = $value['class_id'];
                $techData['teachtype_code'] = $value['teachtype_code'];
                //助教
                $re_techData['staffer_id'] = $value['assistant_staffer_id'];
                $re_techData['teachtype_code'] = $value['assistant_teachtype_code'];

                unset($value['staffer_id']);
                unset($value['teachtype_code']);
                unset($value['assistant_staffer_id']);
                unset($value['assistant_teachtype_code']);

                $hour_id = $this->DataControl->insertData("smc_class_hour", $value);

                if ($value['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = $value['hour_starttime'];
                    $lineData['linerooms_endtime'] = $value['hour_endtime'];
                    $lineData['linerooms_name'] = $value['hour_name'];
                    $this->updateHourNumber($value['hour_number'], $hour_id, $lineData);
                }

                $techData['hour_id'] = $hour_id;

                $this->DataControl->insertData("smc_class_hour_teaching", $techData);
                if ($re_techData['staffer_id']) {

                    $re_techData['hour_lessontimes'] = $value['hour_lessontimes'];
                    $re_techData['teaching_createtime'] = time();
                    $re_techData['teaching_type'] = 1;
                    $re_techData['class_id'] = $value['class_id'];
                    $re_techData['hour_id'] = $hour_id;

                    $this->DataControl->insertData("smc_class_hour_teaching", $re_techData);
                }
                $last_day = $value['hour_day'];
            }
        }

        $sql = "call RefreshClass('{$request['class_id']}')";
        $this->DataControl->selectClear($sql);

        $json = array();
        $json['class_timestr'] = implode(",", $tem_plan);
        $json['class_enddate'] = date('Y-m-d', strtotime("$last_day"));
        $json['class_updatatime'] = time();
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_enddate", "class_id='{$request['class_id']}'");
        if (($classOne['class_enddate'] < $last_day) or !$classOne['class_enddate']) {
            if ($this->DataControl->updateData("smc_class", "class_id='{$request['class_id']}'", $json)) {
                $studyData = array();
                $studyData['study_endday'] = $last_day;
                $studyData['study_updatetime'] = time();
                $this->DataControl->updateData("smc_student_study", "  study_isreading =1 and class_id='{$request['class_id']}'", $studyData);
            }
        }
        return true;
    }

    /**
     * 一键延后 课时
     * author: ling
     * 对应接口文档 0001
     */
    function DelayClassHour($request)
    {
        $courseOne = $this->DataControl->selectOne("select c.class_cnname,s.course_inclasstype,s.course_cnname,s.course_id,s.course_openclasstype from smc_class as c, smc_course as s where s.course_id = c.course_id and c.class_id='{$request['class_id']}'");
        if ($courseOne['course_inclasstype'] != 0 && ($courseOne['course_inclasstype'] != 3 || $courseOne['course_openclasstype'] != 1)) {
            $this->error = 1;
            $this->errortip = "该功能只适用于课次类班级及试读公开课";
            return false;
        }

        if ($courseOne['course_inclasstype'] == 3 && $courseOne['course_openclasstype'] == 1) {
            $sql = "select audition_id from view_crm_audition where class_id='{$request['class_id']}' and audition_isvisit>='0'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = 1;
                $this->errortip = "班级存在学员预约,不可执行此操作";
                return false;
            }
        }

        if (!$request['fixeddate']) {
            $this->error = 1;
            $this->errortip = "请选择开始排课的日期";
            return false;
        }
        $fixeddate = date("Y-m-d", strtotime($request['fixeddate']));
        $paramArray = array();
        $paramArray['start'] = $fixeddate;
        $paramArray['school_id'] = $request['school_id'];
        $paramArray['company_id'] = $request['company_id'];
        $paramArray['staffer_id'] = $request['staffer_id'];
        $paramArray['class_id'] = $request['class_id'];
        $paramArray['end'] = '';
        $paramArray['maxnum'] = '';
        $paramArray['skip_holidays'] = '1';
        $paramArray['is_frequency'] = '1';

        $sql = "select c.* from smc_class_lessonplan as c where class_id='{$request['class_id']}' and lessonplan_play =1 order by lessonplan_weekno ASC ";
        $lessonPlan = $this->DataControl->selectClear($sql);
        $arrangeList = array();
        if (!$lessonPlan) {
            $this->error = 1;
            $this->errortip = "暂无可用的排课计划";
            return false;
        } else {
            foreach ($lessonPlan as $val) {
                $arrangeOne['weekday_id'] = $val['lessonplan_weekno'];
                $arrangeOne['staffer_id'] = $val['staffer_id'];
                $arrangeOne['classroom_id'] = $val['classroom_id'];
                $arrangeOne['hour_starttime'] = $val['lessonplan_starttime'];
                $arrangeOne['hour_endtime'] = $val['lessonplan_endtime'];
                $arrangeOne['hour_way'] = $val['lessonplan_way'];
                $arrangeOne['assistant_staffer_id'] = $val['poll_staffer_id'];
                $arrangeOne['teachtype_code'] = $val['teachtype_code'];
                $arrangeOne['assistant_teachtype_code'] = $val['poll_teachtype_code'];
                $arrangeList[] = $arrangeOne;
            }
        }
        $paramArray['arrangeList'] = json_encode($arrangeList, JSON_UNESCAPED_UNICODE);
        $bool = $this->modifyLessonPlan($paramArray);
        if ($bool) {
            $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
                        p.parenter_cnname 
                    FROM
                        smc_student_study AS ss
                        LEFT JOIN smc_student_family AS f ON f.student_id = ss.student_id
                        LEFT JOIN smc_student AS s ON s.student_id = ss.student_id
                        LEFT JOIN smc_parenter AS p ON p.parenter_id = f.parenter_id
                        JOIN smc_parenter_wxchattoken AS w ON w.parenter_id = f.parenter_id 
                        AND w.company_id = s.company_id 
                        AND w.parenter_wxtoken IS NOT NULL 
                    WHERE
                        ss.class_id = '{$request['class_id']}' 
                        AND ss.study_isreading = '1' 
                    GROUP BY
                        s.student_id
                    ");
            if ($parenter) {
                foreach ($parenter as $val) {
                    $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");
                    $school_cnname = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$request['school_id']}'");
                    $coursetime = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_time", "course_id =     '{$courseOne['course_id']}' and student_id = '{$val['student_id']}' and school_id = '{$request['school_id']}'");

                    $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程变动提醒'");
                    if ($isset) {
                        $wxid = $isset['masterplate_wxid'];
                    } else {
                        $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程变动提醒'");
                        $wxid = $masterplate['masterplate_wxid'];
                    }

                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，本课程的课表有所调整，请及时查看。');
                    $b = $school_cnname['school_cnname'];
                    $c = $courseOne['class_cnname'];
                    $d = '您' . $courseOne['course_cnname'] . '课程剩余' . $coursetime['coursebalance_time'] . '次未上课信息有调整';
                    $e = '点击可查看课表详情哦~';
                    $f = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->ChangeHour($a, $b, $c, $d, $e, $f, $wxid);
                }
            }
            $this->error = 0;
            $this->errortip = "延后成功";
            return true;
        } else {

            return false;
        }
    }

    /**
     * 增加排课->增加子班的排课
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function addChildClassHour($request)
    {
        if (isset($request['isskipweek']) && $request['isskipweek'] !== '') {
            $isskipweek = intval($request['isskipweek']);
        } else {
            $isskipweek = 0;
        }
        $arrangeList = json_decode(stripslashes($request['arrangeList']), true);
        if (!$arrangeList) {
            $this->error = 1;
            $this->errortip = '请设置排课排课信息';
            return false;
        }

        $lastHourOne = $this->DataControl->selectOne("select hour_id,hour_lessontimes from smc_class_hour as h where h.class_id='{$request['class_id']}' order by h.hour_lessontimes DESC limit 0,1 ");
        $lastHourDay = $this->DataControl->selectOne("select hour_id,hour_day from smc_class_hour as h where h.class_id='{$request['class_id']}' and  h.hour_ischecking <> '-1' order by h.hour_day DESC limit 0,1 ");
        if ($lastHourDay && $lastHourDay['hour_day'] >= $request['start']) {
            $this->error = 1;
            $this->errortip = '请选择' . $lastHourDay['hour_day'] . '之后的日期';
            return false;
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id,class_stdate", "class_id='{$request['class_id']}'");
        if ($classOne['class_stdate'] > $request['start']) {
            $this->error = 1;
            $this->errortip = '请选择班级开班后的时间';
            return false;
        }
        $lessontimes = $lastHourOne['hour_lessontimes'] + 1;

        $arrangeList = $this->sortWeekdayByWeekId($request['start'], $arrangeList);
        $total = count($arrangeList);
        $numArray = $this->AverageDivisionNumber($request['maxnum'], $total);
        if (!$numArray) {
            $this->error = 1;
            $this->errortip = '请检查排课';
            return false;
        }

        $holidayArray = array();
        if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
            $holidayArray = $this->getSchHolidayArray($request['start'], $request['school_id'], $request['company_id']);
        }
        if ($holidayArray) {
            $holiday = array_column($holidayArray, 'holidays_day');
        } else {
            $holiday = false;
        }
        $courseOne = $this->DataControl->selectOne("
					select ct.coursetype_isopenclass,course_inclasstype 
					from smc_class as c
					left join smc_course as  co on c.course_id = co.course_id
				    left join smc_code_coursetype as  ct ON ct.coursetype_id = co.coursetype_id
 				    where c.class_id='{$request['class_id']}' ");

        $tem_plan = array();
        $dayListArray = array();
        $timeListArray = array();
        foreach ($arrangeList as $key => $arrangeOne) {
            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];
            $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], array(), $isskipweek, 0);
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], $holiday, $isskipweek, 0);
            }
            foreach ($weekList as $day) {
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    if (is_array($holiday)) {
                        if (in_array($day, $holiday)) {
                            continue;
                        }
                    }
                }
                $dayListArray[] = $day . ' ' . $arrangeOne['hour_starttime'];
            }
            $timeListArray[$key]['weekday_id'] = $arrangeOne['weekday_id'];
            $timeListArray[$key]['hour_starttime'] = $arrangeOne['hour_starttime'];
            $timeListArray[$key]['hour_endtime'] = $arrangeOne['hour_endtime'];
        }
        if ($courseOne['coursetype_isopenclass'] == 2) {
            $time_day = $this->checkflictTime($timeListArray, 1);
            if ($time_day !== true) {
                $time_week = self::$WORK_DAY[$time_day]['cn'];
                $this->errortip = $time_week . "有两次排课,请删除其中一个";
                $this->error = "1";
                return false;
            }
        } else {
            //检查时间冲突
            $time_day = $this->checkflictTime($timeListArray);
            if ($time_day !== true) {
                $time_week = self::$WORK_DAY[$time_day]['cn'];
                $this->errortip = $time_week . "所选时间段冲突";
                $this->error = "1";
                return false;
            }
        }
        sort($dayListArray);
        $array = array_flip($dayListArray);
        $temHour = array();
        foreach ($arrangeList as $key => $arrangeOne) {
            $tem_plan[$key] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'] . ' ' . $arrangeOne['hour_starttime'] . '—' . $arrangeOne['hour_endtime'];
            $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], array(), $isskipweek, 0);
            if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                $weekList = $this->classModel->weedDayList($request['start'], $request['end'], $arrangeOne['weekday_id'], $numArray[$key], $request['is_frequency'], $holiday, $isskipweek, 0);
            }
            foreach ($weekList as $h_key => $day) {
                if (isset($request['skip_holidays']) && $request['skip_holidays'] == 1) {
                    if (is_array($holiday)) {
                        if (in_array($day, $holiday)) {
                            continue;
                        }
                    }
                }

                $day_time = $day . ' ' . $arrangeOne['hour_starttime'];
                $hourData = array();
                $hourData['course_id'] = $classOne['course_id'];
                $hourData['class_id'] = $request['class_id'];
                $hourData['classroom_id'] = $arrangeOne['classroom_id'];
                $hourData['hour_lessontimes'] = $array[$day_time] + $lessontimes;

                if ($courseOne['coursetype_isopenclass'] == 1) {
                    $request['carrynum'] = 0;
                    $request['last_carrynum'] = 0;
                }
                //双重模式 同时支持前期赠送 后期赠送
                if (isset($request['carrynum']) && $request['carrynum'] >= 0 && isset($request['last_carrynum']) && $request['last_carrynum'] >= 0) {

                    if ($request['carrynum'] >= ($array[$day_time] + 1)) {
                        $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1);
                        $hourData['hour_isfree'] = 1;
                    } elseif (($request['maxnum'] + $request['carrynum']) < ($array[$day_time] + 1)) {
                        $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1);
                        $hourData['hour_isfree'] = 1;
                    } else {
                        if ($courseOne['coursetype_isopenclass'] == 1) {
                            $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1);
                            $hourData['hour_isfree'] = 1;
                        } else {
                            $hourData['hour_name'] = 'Lesson ' . ($array[$day_time] + 1);
                            $hourData['hour_isfree'] = 0;
                        }
                    }
                }

                $hourData['hour_day'] = $day;
                $hourData['hour_starttime'] = $arrangeOne['hour_starttime'];
                $hourData['hour_endtime'] = $arrangeOne['hour_endtime'];
                $hourData['hour_createtime'] = time();
                $hourData['hour_updatatime'] = time();
                $hourData['hour_formerday'] = $day;
                //hour表不需要的字段
                $hourData['staffer_id'] = $arrangeOne['staffer_id'];
                $hourData['teachtype_code'] = $arrangeOne['teachtype_code'];
                $hourData['assistant_staffer_id'] = $arrangeOne['assistant_staffer_id'];
                $hourData['assistant_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];
                $hourData['hour_way'] = $arrangeOne['hour_way'];
                if ($hourData['hour_way'] == 1) {
                    $hourData['hour_number'] = $this->classModel->createHourNumber($request['class_id'], 0);
                }
                $hourData['hour_formertimes'] = $arrangeOne['hour_starttime'] . '-' . $arrangeOne['hour_endtime'];
                if (strtotime($day . " " . $arrangeOne['hour_starttime']) < strtotime($day . " 12:00")) {
                    $hourData['hour_noon'] = 1;
                } elseif (strtotime($day . " " . $arrangeOne['hour_starttime']) > strtotime($day . " 18:00")) {
                    $hourData['hour_noon'] = 3;
                } else {
                    $hourData['hour_noon'] = 2;
                }
                $temHour[] = $hourData;
            }

            $data = array();
            $data['class_id'] = $request['class_id'];
            $data['lessonplan_week'] = self::$WORK_DAY[$arrangeOne['weekday_id']]['cn'];
            $data['lessonplan_weekno'] = $arrangeOne['weekday_id'];
            $data['classroom_id'] = $arrangeOne['classroom_id'];
            $data['staffer_id'] = $arrangeOne['staffer_id'];
            $data['teachtype_code'] = $arrangeOne['teachtype_code'];
            $data['poll_staffer_id'] = $arrangeOne['assistant_staffer_id'];
            $data['poll_teachtype_code'] = $arrangeOne['assistant_teachtype_code'];
            $data['lessonplan_play'] = '1';
            $data['lessonplan_starttime'] = $arrangeOne['hour_starttime'];
            $data['lessonplan_endtime'] = $arrangeOne['hour_endtime'];
            $data['lessonplan_isskipweek'] = $request['isskipweek'];
            $data['lessonplan_way'] = $arrangeOne['hour_way'];
            $data['lessonplan_createtime'] = time();
            $this->DataControl->insertData("smc_class_lessonplan", $data);
        }
        $num = 0;
        if ($temHour) {
            $arr_lessontimes = array_column($temHour, 'hour_lessontimes');
            array_multisort($arr_lessontimes, $temHour, SORT_ASC);

            foreach ($temHour as $key => $value) {
                $techData = array();
                $re_techData = array();
                $techData['staffer_id'] = $value['staffer_id'];
                $techData['hour_lessontimes'] = $value['hour_lessontimes'];
                $techData['teaching_createtime'] = time();
                $techData['teaching_type'] = 0;
                $techData['class_id'] = $value['class_id'];
                $techData['teachtype_code'] = $value['teachtype_code'];
                $techData['hour_former_staffer_id'] = $value['staffer_id'];
                //助教
                $re_techData['staffer_id'] = $value['assistant_staffer_id'];
                $re_techData['teachtype_code'] = $value['assistant_teachtype_code'];
                $re_techData['hour_former_staffer_id'] = $value['assistant_staffer_id'];

                unset($value['staffer_id']);
                unset($value['teachtype_code']);
                unset($value['assistant_staffer_id']);
                unset($value['assistant_teachtype_code']);

                $hour_id = $this->DataControl->insertData("smc_class_hour", $value);
                if ($value['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = $value['hour_starttime'];
                    $lineData['linerooms_endtime'] = $value['hour_endtime'];
                    $lineData['linerooms_name'] = $value['hour_name'];
                    $this->updateHourNumber($value['hour_number'], $hour_id, $lineData);
                }

                $techData['hour_id'] = $hour_id;
                $num++;
                $this->DataControl->insertData("smc_class_hour_teaching", $techData);
                if ($re_techData['staffer_id']) {

                    $re_techData['hour_lessontimes'] = $value['hour_lessontimes'];
                    $re_techData['teaching_createtime'] = time();
                    $re_techData['teaching_type'] = 1;
                    $re_techData['class_id'] = $value['class_id'];
                    $re_techData['hour_id'] = $hour_id;

                    $this->DataControl->insertData("smc_class_hour_teaching", $re_techData);
                }

            }
        }
        $this->error = 0;
        $this->errortip = "新增排课{$num}节！";
        return true;
    }

    /**
     * 时间排序 将更靠近现在的时间往前排
     * author: ling
     * 对应接口文档 0001
     */
    private function sortWeekdayByWeekId($start, $arrangeList)
    {
        $day_weeknum = date('w', strtotime($start));
        $day_weeknum = $day_weeknum == 0 ? '7' : $day_weeknum;

        if (is_array($arrangeList) && $arrangeList) {
            foreach ($arrangeList as $key => $arrOne) {
                if ($arrOne['weekday_id'] < $day_weeknum) {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'] + 7;
                } else {
                    $arrangeList[$key]['week_id_sort'] = $arrOne['weekday_id'];
                }
            }
            $week_id_sort = array_column($arrangeList, 'week_id_sort');
            array_multisort($week_id_sort, SORT_ASC, $arrangeList);
        }
        return $arrangeList;
    }

    /**
     * 平均划分数字 余数从头到尾均分
     * author: ling
     * 对应接口文档 0001
     * @param $number --被划分的数字
     * @param $total --划分组数
     * @return array|bool
     */
    private function AverageDivisionNumber($allnumber, $total)
    {
        if ($allnumber <= 0 || $total <= 0 || ($allnumber < $total)) {
            $this->error = 1;
            $this->error = '请检查排课设置';
            return false;
        }

        $divide_number = bcdiv($allnumber, $total, 0);
        $mod_number = bcsub($allnumber, $divide_number * $total, 0);
        $number_str = trim(str_repeat($divide_number . '+', $total), '+');
        $numArray = explode("+", $number_str);
        if ($mod_number > 0) {
            for ($i = 0; $i < $mod_number; $i++) {
                $numArray[$i] = $numArray[$i] + 1;
            }
        }
        return $numArray;
    }

    /**
     * 获取学校的假期安排
     * author: ling
     * 对应接口文档 0001
     */
    private function getSchHolidayArray($start, $school_id, $company_id)
    {
        $holidayList = $this->DataControl->selectClear("select ch.holidays_day from smc_code_holidays as ch
                  where ch.company_id='{$company_id}' and ch.holidays_day>='{$start}'
                  and ((ch.school_id='{$school_id}' and ch.holidays_status='0')
                       or(ch.school_id='0' and ch.holidays_status='0' and ch.holidays_day not in (select h.holidays_day from smc_code_holidays as h
                       where h.company_id='{$company_id}' and h.school_id='{$school_id}' and h.holidays_status='1')))");
        if ($holidayList) {
            $holidayArray = array();
            foreach ($holidayList as $holidayOne) {
                $holidayArray[] = $holidayOne['holidays_day'];
            }
            sort($holidayArray);
        }
        return $holidayList;
    }
}