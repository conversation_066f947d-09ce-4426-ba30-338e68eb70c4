<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/7/14 0014
 * Time: 15:23
 */

namespace Model\Smc;

class CheckingModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];

        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function addStudentIntegral($company_id, $school_id, $student_id, $course_id, $integral = 0, $integrallog_rule, $staffer_id = 0, $playname = '', $note = '', $time = '', $class_id)
    {
        if (!$time || $time == '') {
            $time = time();
        }

        $integral_new = $integral;

        $stuIntOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'");
        $stuIntOne['property_integralbalance'] = $stuIntOne['property_integralbalance'] ? $stuIntOne['property_integralbalance'] : 0;

//        $integral_new = 0;
//        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_isintegral,course_maxintegral", "course_id='{$course_id}'");
//        $oldOne = $this->DataControl->getFieldOne("smc_student_integrallog", "sum(integrallog_playamount) as old_integral", "integrallog_playclass='+' and student_id='{$student_id}' and course_id='{$course_id}'");
//
//        if ($courseOne['course_isintegral'] == '1') {
//            if ($oldOne['old_integral'] >= $courseOne['course_maxintegral']) {
//                return false;
//            }
//            if ($oldOne['old_integral'] + $integral >= $courseOne['course_maxintegral']) {
//                $integral_new = $courseOne['course_maxintegral'] - $oldOne['old_integral'];
//            } else {
//                $integral_new = $integral;
//            }
//        } else {
//            $integral_new = $integral;
//        }

        if ($integral_new <= 0) {
            return false;
        }

        $integrallog_data = array();
        $integrallog_data['student_id'] = $student_id;
        $integrallog_data['company_id'] = $company_id;
        $integrallog_data['school_id'] = $school_id;
        $integrallog_data['staffer_id'] = $staffer_id;
        $integrallog_data['course_id'] = $course_id;
        $integrallog_data['class_id'] = $class_id;

        $integrallog_data['integrallog_rule'] = $integrallog_rule;
        $integrallog_data['integrallog_playname'] = $playname ? $playname : $this->LgStringSwitch('积分增加');
        $integrallog_data['integrallog_playclass'] = '+';
        $integrallog_data['integrallog_fromamount'] = $stuIntOne['property_integralbalance'];
        $integrallog_data['integrallog_playamount'] = $integral_new;
        $integrallog_data['integrallog_finalamount'] = $stuIntOne['property_integralbalance'] + $integral_new;

        $integrallog_data['integrallog_remark'] = $note;
        $integrallog_data['integrallog_reason'] = $integrallog_rule;
        $integrallog_data['integrallog_time'] = $time;
        $this->DataControl->insertData("smc_student_integrallog", $integrallog_data);

        //积分余额
        if ($this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'")) {
            $data = array();
            $data['property_integralbalance'] = $stuIntOne['property_integralbalance'] + $integral_new;
            $this->DataControl->updateData("smc_student_virtual_property", "student_id='{$student_id}'", $data);
        } else {
            $data = array();
            $data['student_id'] = $student_id;
            $data['property_integralbalance'] = $integral_new;
            $this->DataControl->insertData("smc_student_virtual_property", $data);
        }
        return true;
    }

    /**
     *  设置考勤  班级考勤
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function setClassChecken($request)
    {
        $teachingHourOne = $this->DataControl->getFieldOne("smc_class_hour_teaching", "staffer_id", "hour_id='{$request['hour_id']}' and teaching_type=0 and teaching_isdel=0");
        if (!$teachingHourOne || $teachingHourOne['staffer_id'] == 0) {
            $this->error = 1;
            $this->errortip = "您考勤的课时未设置主教教师，请先设置主教教师后考勤！";
            return false;
        }

        if ($request['from_R'] != '1') {
            $stafferteahingOne = $this->DataControl->getFieldOne('smc_staffer', "concat(staffer_cnname,(CASE WHEN ifnull(staffer_enname, '' ) = '' THEN '' ELSE concat( '-',staffer_enname ) END )) as staffer_cnname,staffer_leave", "staffer_id='{$teachingHourOne['staffer_id']}'");
            if ($stafferteahingOne['staffer_leave'] == 1) {
                $this->error = 1;
                $this->errortip = "该课时" . $stafferteahingOne['staffer_cnname'] . "代课教师已离职,请调整上课教师后考勤！";
                return false;
            }
            $stafferteahingTwo = $this->DataControl->selectOne("select concat(staffer_cnname,(CASE WHEN ifnull(f.staffer_enname, '' ) = '' THEN '' ELSE concat( '-',f.staffer_enname ) END )) as staffer_cnname,f.staffer_leave from smc_class_hour_teaching as t,smc_staffer as f where f.staffer_id = t.staffer_id and t.hour_id='{$request['hour_id']}' and t.teaching_type=1 and t.teaching_isdel=0");
            if ($stafferteahingTwo && $stafferteahingTwo['staffer_leave'] == 1) {
                $this->error = 1;
                $this->errortip = "该课时" . $stafferteahingTwo['staffer_cnname'] . "助教教师已离职,请调整助教教师后考勤！";
                return false;
            }
        }

        $hourOne = $this->DataControl->selectOne(
            " select ch.hour_id,ch.hour_ischecking,c.class_status,c.class_id,ch.course_id,ch.hour_lessontimes,ch.hour_day,ch.hour_isfree,cs.course_inclasstype,ch.hour_day,cs.course_checkingintype,cs.course_checkingminday,ch.class_id,c.school_id,ch.hour_iswarming,ch.hour_inarrivenums,cs.course_minclassnum,cs.course_isneedconfirm,c.class_isconfirmopen,c.class_ismustreview,c.class_hourreviewnums
                  ,(select hour_day from smc_class_hour as sch where  sch.hour_day <ch.hour_day and sch.hour_ischecking =0  and sch.hour_iswarming = 0 and sch.class_id =ch.class_id order by  sch.hour_day Desc limit 0,1) as before_day
                    ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=ch.class_id and x.hour_iswarming=2 and x.hour_ischecking<>-1),0) as reviewNum
 				  from smc_class_hour as ch
 			 	  left JOIN  smc_class as c ON  ch.class_id =c.class_id
 			 	  left JOIN  smc_course as cs ON cs.course_id = c.course_id
 			 	  left join  smc_school as s On c.school_id = s.school_id
 		  		  where hour_id='{$request['hour_id']}' limit 0,1 ");


        $hourStudyOne=$this->DataControl->selectOne("SELECT a.hourstudy_id from smc_student_hourstudy as a,smc_class_hour as b where a.hour_id=b.hour_id and a.class_id='{$hourOne['class_id']}' and b.hour_isfree=0 limit 0,1");      

        if ($hourOne) {

            $sql = "select a.application_id from smc_forward_application as a where a.class_id='{$hourOne['class_id']}' and a.application_status=0 and a.out_class_date<='{$hourOne['hour_day']}' limit 0,1";
            $forwardOne = $this->DataControl->selectOne($sql);
            if($forwardOne){
                $this->error = 1;
                $this->errortip = "该次考勤有未审核的课时余额，请先审核";
                return false;
            }

            if($hourOne['hour_isfree']==0 && $hourOne['course_isneedconfirm']==1 && $hourOne['class_isconfirmopen']==0 && !$hourStudyOne){
                $this->error = true;
                $this->errortip = "该班级暂未确认开班,无法考勤!请联系教学主管或校长在教务系统进行开班确认后再操作";
                return false;
            }

            if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and ((adjustapply_type=0 and class_id='{$request['class_id']}' and hour_id='{$request['hour_id']}') or (adjustapply_type=1 and adjustapply_day='{$hourOne['hour_day']}')) and adjustapply_status=0")){
                $this->error = true;
                $this->errortip = "班级/校区存在待审核调课,不可操作考勤";
                return false;
            }

//            if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$request['school_id']}' and adjustapply_type=0 and class_id='{$request['class_id']}' and adjustapply_status=0")){
//                $this->error = true;
//                $this->errortip = "班级存在未完成调课申请,不可操作考勤";
//                return false;
//            }

            if($hourOne['class_ismustreview']==1 && $hourOne['class_hourreviewnums']>$hourOne['reviewNum']){
                $this->error = true;
                $this->errortip = "该课程班级复习课次数必须≥".$hourOne['class_hourreviewnums'];
                return false;
            }

            if ($hourOne['hour_ischecking'] == -1) {
                $this->error = 1;
                $this->errortip = "该课时已经取消，无法进行考勤！";
                return false;
            } elseif ($hourOne['class_status'] == -1) {
                $this->error = 1;
                $this->errortip = "该班级已经结班，无法进行考勤！";
                return false;
            } elseif ($hourOne['hour_day'] > date('Y-m-d')) {
                $this->error = 1;
                $this->errortip = "无法考勤今日以后的课时，无法进行考勤！";
                return false;
            } elseif ($hourOne['hour_ischecking'] == 1 && $hourOne['hour_day'] < date('Y-m-d') && $request['from_R'] != '1') {
                $this->error = 1;
                $this->errortip = "无法修改今日之前的考勤，无法进行考勤！";
                return false;
            } elseif ($hourOne['course_inclasstype'] == 2 && $hourOne['hour_ischecking'] == 1) {
                $this->error = 1;
                $this->errortip = "预约类班级不支持修改考勤，无法进行考勤！";
                return false;
            } elseif ($hourOne['before_day']) {
                $this->error = 1;
                $this->errortip = "有 {$hourOne['before_day']} 的课程未点名，无法进行考勤！";
                return false;
            }

            if ($hourOne['course_checkingintype'] == 3) {
                $this->error = 1;
                $this->errortip = "该收费模式未启用";
                return false;
            }
            if ($hourOne['hour_iswarming'] != 0) {

                $hourOneischecking = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_day", "class_id='{$hourOne['class_id']}' and hour_lessontimes < '{$hourOne['hour_lessontimes']}' and hour_day < '{$hourOne['hour_day']}' and hour_ischecking ='0' order by hour_lessontimes ASC ");
                if ($hourOneischecking) {
                    $this->error = 1;
                    $this->errortip = "有 {$hourOneischecking['hour_day']} 的课程未点名，无法进行考勤！";
                    return false;
                }

                $this->CheckingFreeHour($request, $hourOne);
                $this->error = 0;
                $this->errortip = "免费课时考勤成功！";
                return true;
            }
        } else {
            $this->error = 1;
            $this->errortip = "课时信息不存在，请检查登录信息是否有效！";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "class_type,course_id", "class_id='{$request['class_id']}'");
        if ($request['from_R'] != '1') {
            if ($hourOne['course_checkingintype'] == 2 && $hourOne['hour_ischecking'] == 1) {
                $this->error = 1;
                $this->errortip = "自然周考勤模式,暂不支持修改考勤！";
                return false;
            }
            if ($hourOne['course_checkingintype'] == 1 && $hourOne['hour_ischecking'] == 1) {
                $this->error = 1;
                $this->errortip = "连续缺勤模式,暂不支持修改考勤！";
                return false;
            }
            if ($hourOne['course_checkingintype'] == 5 && $hourOne['hour_ischecking'] == 1) {
                $this->error = 1;
                $this->errortip = "课次考勤(缺勤免费),暂不支持修改考勤！";
                return false;
            }
        }

        $student_checkin_list = json_decode(stripslashes($request['student_checkin_list']), true);
        //20240320 点名上课在以前的逻辑上修改，删除新展示的禁用的crm考勤过的名单 --- 开始
        if($student_checkin_list){
            foreach ($student_checkin_list as $key=>$student_checkin_list_var){
                if($student_checkin_list_var['isprohibit'] == 1){
                    unset($student_checkin_list[$key]);
                }
            }
        }
        //20240320 点名上课在以前的逻辑上修改，删除新展示的禁用的crm考勤过的名单 --- 结束
        $studentCount = count($student_checkin_list);

        if (!$this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$request['class_id']}' and hour_ischecking=1 and hour_isfree=0")) {

            $pricingOne = $this->getCoursePricing($hourOne['course_id'], $request['company_id'], $request['school_id']);
            if (!$pricingOne) {
                $this->error = 1;
                $this->errortip = "该课程无定价";
                return false;
            }

            if ($pricingOne['tuition_minclassnum'] > 0) {
                if ($studentCount < $pricingOne['tuition_minclassnum'] && !$this->DataControl->getFieldOne("smc_class_openapply", "openapply_id", "class_id='{$request['class_id']}' and openapply_status=1")) {
                    if ($this->DataControl->getFieldOne("smc_class_openapply", "openapply_id", "class_id='{$request['class_id']}' and openapply_status=0")) {
                        $this->error = 1;
                        $this->errortip = "已提交开班申请，请勿重复提交，请耐心等待审核~";
                        return false;
                    } else {
                        $this->error = 2;
                        $this->errortip = "最低开班人数为{$pricingOne['tuition_minclassnum']}，未达到开班标准无法考勤";
                        return false;
                    }
                }
            }
        }

        if (is_array($student_checkin_list) && $studentCount > 0) {
            $arr_studentId = array_column($student_checkin_list, 'student_id');
            $str_studentId = trim(implode(',', $arr_studentId), ',');
            if (!$str_studentId) {
                $str_studentId = '0';
            }
            $sql = "select c.student_id
                    ,c.coursebalance_figure
                    ,co.courseforward_price
                    ,c.companies_id
                    ,c.coursebalance_time,
					(select s.student_cnname from smc_student as  s where s.student_id = c.student_id limit 0,1 ) as student_cnname
					from  smc_student_coursebalance  as c
  					left join smc_student_courseforward as co ON c.student_id = co.student_id and c.course_id = co.course_id
  					where c.student_id in ({$str_studentId}) 
  					and c.course_id='{$hourOne['course_id']}' 
  					and c.coursebalance_time<= 0  
  					and school_id = '{$this->school_id}'  
  					and c.student_id not in (select fc.student_id 
  					    from smc_student_free_coursetimes as fc 
  					    where fc.hour_lessontimes='{$hourOne['hour_lessontimes']}' 
  					    and class_id='{$hourOne['class_id']}' 
  					    and fc.course_id='{$hourOne['course_id']}' 
  					    and fc.school_id='{$this->school_id}' 
  					    and fc.is_use='0' )
  					limit 0,1   ";

            $free_sql = "SELECT	 s.student_cnname,
                        (SELECT ( c.coursebalance_time - count( fc.coursetimes_id ) ) FROM smc_student_free_coursetimes AS fc 
                            WHERE fc.student_id = c.student_id AND fc.course_id = c.course_id AND fc.is_use = 0 
                            AND fc.class_id = '{$hourOne['class_id']}' 
                            ) AS coursetimes_num,
                        
                        (SELECT count( fc.coursetimes_id ) FROM smc_student_free_coursetimes AS fc 
                            WHERE fc.student_id = c.student_id AND fc.course_id = c.course_id 
                            AND fc.hour_lessontimes = '{$hourOne['hour_lessontimes']}' 
                            AND fc.class_id = '{$hourOne['class_id']}' 
                            AND fc.is_use = 0 
                            ) AS hour_lessontimes 
                        
                        FROM smc_student_coursebalance AS c 
                        left join smc_student as s ON  s.student_id = c.student_id
                        WHERE c.student_id in ({$str_studentId})
                            AND c.course_id = '{$classOne['course_id']}' 
                            AND c.school_id = '{$hourOne['school_id']}'
                        having coursetimes_num <= 0 
                            and hour_lessontimes<=0    ";

            $balance = $this->DataControl->selectOne($sql);
            $free_balance = $this->DataControl->selectOne($free_sql);
            if ($classOne['class_type'] == 0 && $hourOne['hour_isfree'] == 0 && $hourOne['hour_ischecking'] == '0') {
                if ($balance) {
                    $this->error = 1;
                    $this->errortip = "{$balance['student_cnname']} 学员剩余课次不足,无法考勤1！";
                    return false;
                }
                if ($free_balance) {
                    $this->error = 1;
                    $this->errortip = "{$free_balance['student_cnname']}  学员剩余课次不足,无法考勤2！";
                    return false;
                }
            }

            $Feewaiver = $this->DataControl->selectOne("
            select st.student_cnname
            from smc_payfee_order_pay as op
             left join smc_payfee_order as  po  ON op.order_pid = po.order_pid
             left join smc_payfee_order_course as  poc  ON poc.order_pid = po.order_pid
             left join smc_student as st On st.student_id = po.student_id
            where  po.school_id='{$this->school_id}'  
            and  op.paytype_code='feewaiver' and op.pay_issuccess ='0' and po.student_id in  ({$str_studentId}) and poc.course_id='{$hourOne['course_id']}'  
            and  po.order_status <> '-1' and  po.order_status <> '-2'
            limit 0,1");
            if ($Feewaiver) {
                $this->error = 1;
                $this->errortip = $Feewaiver['student_cnname'] . '存在未审核的减免的订单';
                return false;
            }

            $unpaidList = $this->DataControl->selectOne("
            select c.student_cnname
            from  smc_payfee_order a
            left join smc_payfee_order_course b on a.order_pid = b.order_pid
            left join smc_student c on c.student_id = a.student_id
            left JOIN smc_course as co ON co.course_id = b.course_id
            left join smc_student_coursebalance d on d.school_id=a.school_id and d.student_id=a.student_id and d.course_id=b.course_id
            left join gmc_code_companies f on f.companies_id=d.companies_id
            where a.school_id='{$this->school_id}'  
            and b.course_id='{$hourOne['course_id']}'  
            and a.order_status<>'4'
            and a.order_status>=0
            and (d.coursebalance_issupervise='1' or f.companies_issupervise='1' or co.course_islimittimes = '1')
            and a.student_id in ({$str_studentId}) 
            limit 0,1");

//            left join smc_course e on b.course_id=e.course_id  e.course_issupervise='1' or
//            $arr_studentname = array_column($unpaidList, 'student_cnname');
//            $str_studentname = trim(implode(',', $arr_studentname), ',');
            if ($unpaidList) {
                $this->error = 1;
                $this->errortip = '学员[' . $unpaidList['student_cnname'] . ']课程存在未完成的订单';
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = '本课时未查询到可以考勤的学员,请调整排课,或者将学员提前入班';
            return false;
        }

        // 更新smc_class_hour 上课状态
        $data = array();
        $data['hour_ischecking'] = '1';
        $data['hour_staffer_id'] = intval($request['staffer_id']);
        $data['hour_inarrivenums'] = $studentCount + $hourOne['hour_inarrivenums'];
        $data['hour_updatatime'] = time();
        if (!$this->DataControl->updateData("smc_class_hour", "hour_id='{$request['hour_id']}'", $data)) {
            $this->error = 1;
            $this->errortip = "课程点名失败，请联系技术支持！";
            return false;
        }
        $hourdata = $this->DataControl->selectOne("select hour_id,hour_lessontimes from smc_class_hour where class_id='{$request['class_id']}' and hour_ischecking =1 and hour_id<>'{$request['hour_id']}' ");
        if (!$hourdata['hour_id']) {
            $classdata = array();
            $classdata['class_status'] = 1;
            $classdata['class_updatatime'] = strtotime($hourOne['hour_day']);
            if (!$this->DataControl->updateData('smc_class', "class_id='{$hourOne['class_id']}'", $classdata)) {
                $this->error = 1;
                $this->errortip = "更新班级状态失败，请联系技术支持！";
                return false;
            }
        }
        $count_attendance = 0;
        //期度类班级考勤
        if ($hourOne['course_inclasstype'] == 1) {
            if ($student_checkin_list) {
                foreach ($student_checkin_list as $key => $value) {
                    if ($value['checkin'] == 0) {
                        $stuchecktype_code = '107';
                    } else {
                        $stuchecktype_code = '101';
                    }
                    if ($value['hourstudy_id']) {
//
                        $this->updateOneChecken($request['hour_id'], $value['student_id'], $value['type'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code);
                    } else {
                        $comArray = array();
                        $comArray['company_id'] = $request['company_id'];
                        $comArray['school_id'] = $request['school_id'];
                        $comArray['companies_id'] = $balance['companies_id'];
                        $comArray['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                        $this->setInOneChechen($value['student_id'], $request['hour_id'], $request['class_id'], $value['checkin'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray);
                    }

                    if ($value['checkin'] == 1) {
                        $count_attendance++;
                    }
                }
                if (!$rollOne = $this->DataControl->selectOne("select checkroll_id from  smc_class_hour_checkroll where hour_id='{$request['hour_id']}'")) {
                    $rollData = array();
                    $rollData['company_id'] = $request['company_id'];
                    $rollData['school_id'] = $request['school_id'];
                    $rollData['class_id'] = $request['class_id'];
                    $rollData['hour_id'] = $request['hour_id'];
                    $rollData['checkroll_duenums'] = $studentCount;
                    $rollData['checkroll_attendance'] = $count_attendance;
                    $rollData['staffer_id'] = $request['staffer_id'];
                    $rollData['checkroll_createtime'] = time();
                    $this->DataControl->insertData('smc_class_hour_checkroll', $rollData);
                } else {
                    $rollData = array();
                    $rollData['checkroll_duenums'] = $studentCount;
                    $rollData['checkroll_attendance'] = $count_attendance;
                    $rollData['staffer_id'] = $request['staffer_id'];
                    $rollData['checkroll_updatatime'] = time();
                    $this->DataControl->updateData('smc_class_hour_checkroll', "checkroll_id='{$rollOne['checkroll_id']}'", $rollData);
                }
                $teachData = array();
                $teachData['teaching_ischecking'] = 1;
                $teachData['teaching_updatatime'] = time();

                if (!$this->DataControl->updateData("smc_class_hour_teaching", "class_id='{$hourOne['class_id']}' and hour_id='{$request['hour_id']}'", $teachData)) {
                    $this->error = 1;
                    $this->errortip = "更新教师考勤状态失败，请联系技术支持！";
                    return false;
                }
                $this->error = 0;
                $this->errortip = "考勤成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "学员列表缺失";
                return false;
            }
        }

        //课次类与预约类
        if ($student_checkin_list) {
            foreach ($student_checkin_list as $key => $value) {
                if ($hourOne['course_checkingintype'] == 0) {
                    //0课次考勤(缺勤计费)
                    if ($value['checkin'] == 0) {
                        $stuchecktype_code = '107';
                    } else {
                        $stuchecktype_code = '101';
                    }
                    if ($value['hourstudy_id'] > 0) {
                        $bool = $this->updateOneChecken($request['hour_id'], $value['student_id'], $value['type'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code);
                    } else {
                        $comArray = array();
                        $comArray['company_id'] = $request['company_id'];
                        $comArray['school_id'] = $request['school_id'];
                        $comArray['companies_id'] = $balance['companies_id'];
                        $comArray['hour_lessontimes'] = $hourOne['hour_lessontimes'];

                        $bool = $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $request['hour_id'], $value['student_id'], $value['type'], $hourOne['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], 0);
                    }
                    if (!$bool) {
                        return false;
                    }
                }
                elseif ($hourOne['course_checkingintype'] == 2) {
                    //2自然周考勤
                    $weekdayList = GetWeekAll($hourOne['hour_day']);
                    $weekstartday = $weekdayList['nowweek_start'];
                    $weekendday = $weekdayList['nowweek_end'];
                    //一整周的课表
                    $weekdaywhere = " 1 and ch.hour_day>='{$weekstartday}' and  ch.hour_day<='{$weekendday}'";
                    $weekList = $this->DataControl->selectClear("
                            select ch.*
                            from smc_class_hour as ch
                            where {$weekdaywhere} and class_id='{$hourOne['class_id']}'
                            order by ch.hour_day ASC
                         ");
                    if ($weekList) {
                        $arr_hour_id = array_column($weekList, "hour_id");
                        $str_hour_id = implode(',', $arr_hour_id);
                        if (!$str_hour_id) {
                            $str_hour_id = '0';
                        }
                    } else {
                        $str_hour_id = '0';
                    }
                    if ($value['checkin'] == 0) {
                        $stuchecktype_code = '107';
                    } else {
                        $stuchecktype_code = '101';
                    }
                    $comArray = array();
                    $comArray['company_id'] = $request['company_id'];
                    $comArray['school_id'] = $request['school_id'];
                    $comArray['companies_id'] = $balance['companies_id'];
                    $comArray['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                    if ($value['checkin'] == 1) {
                        $al_checkinList = $this->DataControl->selectClear("
                            select ch.* 
                            from smc_class_hour  as  ch 
                            left join smc_student_hourstudy as  sh ON ch.hour_id = sh.hour_id
                            where ch.hour_id = sh.hour_id  
                            and sh.hourstudy_checkin = '1'  
                            and ch.hour_lessontimes < '{$hourOne['hour_lessontimes']}' 
                            and  sh.student_id = '{$value['student_id']}' 
                            and ch.hour_id  in ({$str_hour_id})
                         ");
                        //本周未出勤的课次
                        $no_checkinList = $this->DataControl->selectClear("
                            select ch.* ,sh.hourstudy_checkin,sh.hourstudy_id
                            from smc_class_hour  as  ch 
                            left join smc_student_hourstudy as  sh ON ch.hour_id = sh.hour_id
                            where ch.hour_id = sh.hour_id  and sh.hourstudy_checkin = '0'  
                            and ch.hour_lessontimes < '{$hourOne['hour_lessontimes']}' 
                            and sh.student_id= '{$value['student_id']}' 
                            and ch.hour_id  in ({$str_hour_id})
                         ");
                        if ($this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $request['hour_id'], $value['student_id'], $value['type'], $hourOne['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], 0)) {
                            if ($no_checkinList && !$al_checkinList) {
                                foreach ($no_checkinList as $k => $val) {
                                    $comArray['hour_lessontimes'] = $val['hour_lessontimes'];
                                    $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $val['hour_id'], $value['student_id'], $value['type'], $val['hour_isfree'], 0, $val['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $val['hour_day'], 1);
                                }
                            }
                        }
                    } else {
                        $al_checkinList = $this->DataControl->selectClear("
                            select ch.* 
                            from smc_class_hour  as  ch 
                            left join smc_student_hourstudy as  sh ON ch.hour_id = sh.hour_id
                            where ch.hour_id = sh.hour_id  and sh.hourstudy_checkin = '1'  
                            and ch.hour_lessontimes < '{$hourOne['hour_lessontimes']}' 
                            and  sh.student_id = '{$value['student_id']}' 
                            and ch.hour_id  in ({$str_hour_id})
                         ");

                        if ($al_checkinList) {
                            $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $request['hour_id'], $value['student_id'], $value['type'], $hourOne['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], 0);
                        } else {
                            $data = array();
                            $data['student_id'] = $value['student_id'];
                            $data['class_id'] = $request['class_id'];
                            $data['hour_id'] = $request['hour_id'];
                            $data['hourstudy_checkin'] = '0';
                            $hour_study_id = $this->DataControl->insertData('smc_student_hourstudy', $data);

                            $logData = array();
                            $logData['student_id'] = $value['student_id'];
                            $logData['school_id'] = $request['school_id'];
                            $logData['hourstudy_id'] = $hour_study_id;
                            $logData['clockinginlog_type'] = 0;
                            $logData['clockinginlog_note'] = $value['note'];
                            $logData['checktype_code'] = $request['checktype_code'];
                            $logData['clockinginlog_price'] = '';
                            $logData['stuchecktype_code'] = $stuchecktype_code;
                            $logData['clockinginlog_checktypename'] = $this->LgStringSwitch("校务系统考勤");
                            $logData['clockinginlog_day'] = $hourOne['hour_day'];
                            $logData['clockinginlog_createtime'] = time();
                            if (!$this->DataControl->insertData('smc_student_clockinginlog', $logData)) {
                                $this->error = 1;
                                $this->errortip = "增加考勤记录失败2";
                                return false;
                            }
                        }
                    }
                }
                elseif ($hourOne['course_checkingintype'] == 1) {
                    //1连续缺勤
                    $comArray = array();
                    $comArray['company_id'] = $request['company_id'];
                    $comArray['school_id'] = $request['school_id'];
                    $comArray['companies_id'] = $balance['companies_id'];
                    $comArray['hour_lessontimes'] = $hourOne['hour_lessontimes'];

                    if ($value['checkin'] == 0) {
                        $stuchecktype_code = '107';
                    } else {
                        $stuchecktype_code = '101';
                    }
                    //出勤
                    if ($value['checkin'] == 1) {
                        $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $request['hour_id'], $value['student_id'], $value['type'], $hourOne['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], 0);

                        //获取前N 节课的课次
                        $before_checkinList = $this->DataControl->selectClear("
                            select ch.*,sh.hourstudy_checkin 
                            ,ifnull((select log_playtimes from smc_student_coursebalance_log 
                where student_id=sh.student_id and class_id=sh.class_id and hourstudy_id=sh.hourstudy_id 
                and log_class=0 order by log_playtimes desc limit 0,1),0) as is_income
                            from smc_class_hour as ch 
                            left join smc_student_hourstudy as sh ON ch.hour_id = sh.hour_id 
                            where hour_isfree=0
                            and ch.hour_lessontimes < '{$hourOne['hour_lessontimes']}' 
                            and sh.student_id = '{$value['student_id']}' 
                            and ch.class_id='{$request['class_id']}' 
                            order by ch.hour_lessontimes DESC 
                            limit 0,{$hourOne['course_checkingminday']} 
                            ");

                        if ($before_checkinList) {
                            $arr_checkin = array_column($before_checkinList, "hourstudy_checkin");
                            if (count($arr_checkin) >= $hourOne['course_checkingminday']) {
                                if (in_array('1', $arr_checkin)) {
                                    foreach ($before_checkinList as $k => $before_one) {
                                        $comArray['hour_lessontimes'] = $before_one['hour_lessontimes'];
                                        if ($before_one['hourstudy_checkin'] == 1) {
                                            break; //遇到已考勤的直接终止循环
                                        } else {
                                            //补_未考勤的扣钱
                                            if ($before_one['is_income'] == 0) {
                                                $bool = $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $before_one['hour_id'], $value['student_id'], $value['type'], $before_one['hour_isfree'], 1, $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $before_one['hour_day'], 1);
                                            }
                                        }
                                    }
                                }
                            } else {
                                //最初的几节课
                                foreach ($before_checkinList as $k => $before_one) {
                                    $comArray['hour_lessontimes'] = $before_one['hour_lessontimes'];
                                    if ($before_one['hourstudy_checkin'] == 1) {
                                        break; //遇到已考勤的直接终止循环
                                    } else {
                                        //补_未考勤的扣钱
                                        $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $before_one['hour_id'], $value['student_id'], $value['type'], $before_one['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $before_one['hour_day'], 1);
                                    }
                                }
                            }
                        }
                    } else {
                        if ($value['type'] == "audition") { //20210129 97添加
                            $data = array();
                            $crm_auData = array();
                            if ($value['checkin'] == 0) {
                                $data['audition_isvisit'] = -1;
                                $crm_auData['audition_isvisit'] = -1;
                            } else {
                                $data['audition_isvisit'] = $value['checkin'];
                                $crm_auData['audition_isvisit'] = $value['checkin'];
                            }
                            $data['audition_novisitreason'] = $value['note'];
                            $crm_auData['audition_novisitreason'] = $value['note'];
                            $data['audition_updatatime'] = strtotime($hourOne['hour_day']);
                            $crm_auData['audition_updatetime'] = time();

                            $this->DataControl->updateData('smc_class_hour_audition', "hour_id='{$request['hour_id']}' and client_id='{$value['student_id']}' and  audition_isvisit=0", $data);

                            $this->DataControl->updateData("crm_client_audition", "hour_id='{$request['hour_id']}' and client_id='{$value['student_id']}'  and  audition_isvisit=0", $crm_auData);

                            //添加试听记录
                            $this->addCrmAuditionTrack($hourOne['school_id'], $value['student_id'], $crm_auData['audition_isvisit']);

                            return true;
                        } elseif ($value['type'] == "student") {
                            $after_hourList = $this->DataControl->selectClear("
                            select a.*
                            from smc_class_hour a
                            where hour_isfree=0
                            and a.hour_lessontimes>='{$hourOne['hour_lessontimes']}' 
                            and a.class_id='{$request['class_id']}' 
                            order by a.hour_lessontimes 
                            limit 0,{$hourOne['course_checkingminday']} 
                            ");
                            $left_hours = count($after_hourList);//M
                            if ($left_hours < $hourOne['course_checkingminday']) {
                                //当前及后续剩余课次小于连续缺勤次数N，查询前N-M条考勤
                                $before_times = $hourOne['course_checkingminday'] - $left_hours;

                                $before_checkinList = $this->DataControl->selectClear("
                                select ch.*,sh.hourstudy_checkin 
                                from smc_class_hour as ch 
                                left join smc_student_hourstudy as sh ON ch.hour_id = sh.hour_id 
                                where hour_isfree=0 
                                and ch.hour_lessontimes < '{$hourOne['hour_lessontimes']}' 
                                and sh.student_id = '{$value['student_id']}' 
                                and ch.class_id='{$request['class_id']}' 
                                order by ch.hour_lessontimes DESC 
                                limit 0,{$before_times} ");
                                $arr_checkin = array_column($before_checkinList, "hourstudy_checkin");

                                //如果前N-M次中有出勤的记录（或者总课次不足N次），则后续不可能有>=N的连续缺勤，则每次缺勤均需扣钱
                                if (count($arr_checkin) < $before_times || in_array('1', $arr_checkin)) {
                                    $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $request['hour_id'], $value['student_id'], $value['type'], $hourOne['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], 0);
                                } else {
                                    //如果前N-M次中无出勤的记录，未出勤,只记录考勤不扣课次
                                    $data = array();
                                    $data['student_id'] = $value['student_id'];
                                    $data['class_id'] = $request['class_id'];
                                    $data['hour_id'] = $request['hour_id'];
                                    $data['hourstudy_checkin'] = '0';
                                    $hour_study_id = $this->DataControl->insertData('smc_student_hourstudy', $data);

                                    $logData = array();
                                    $logData['student_id'] = $value['student_id'];
                                    $logData['school_id'] = $request['school_id'];
                                    $logData['hourstudy_id'] = $hour_study_id;
                                    $logData['clockinginlog_type'] = 0;
                                    $logData['clockinginlog_note'] = $value['note'];
                                    $logData['checktype_code'] = $request['checktype_code'];
                                    $logData['clockinginlog_price'] = '';
                                    $logData['stuchecktype_code'] = $stuchecktype_code;
                                    $logData['clockinginlog_checktypename'] = $this->LgStringSwitch("校务系统考勤");
                                    $logData['clockinginlog_day'] = $hourOne['hour_day'];
                                    $logData['clockinginlog_createtime'] = time();
                                    if (!$this->DataControl->insertData('smc_student_clockinginlog', $logData)) {
                                        $this->error = 1;
                                        $this->errortip = "增加考勤记录失败1-1";
                                        return false;
                                    }
                                }
                            } else {
                                //当前及后续剩余课次>=连续缺勤次数N，未出勤,只记录考勤不扣课次
                                $data = array();
                                $data['student_id'] = $value['student_id'];
                                $data['class_id'] = $request['class_id'];
                                $data['hour_id'] = $request['hour_id'];
                                $data['hourstudy_checkin'] = '0';
                                $hour_study_id = $this->DataControl->insertData('smc_student_hourstudy', $data);

                                $logData = array();
                                $logData['student_id'] = $value['student_id'];
                                $logData['school_id'] = $request['school_id'];
                                $logData['hourstudy_id'] = $hour_study_id;
                                $logData['clockinginlog_type'] = 0;
                                $logData['clockinginlog_note'] = $value['note'];
                                $logData['checktype_code'] = $request['checktype_code'];
                                $logData['clockinginlog_price'] = '';
                                $logData['stuchecktype_code'] = $stuchecktype_code;
                                $logData['clockinginlog_checktypename'] = $this->LgStringSwitch("校务系统考勤");
                                $logData['clockinginlog_day'] = $hourOne['hour_day'];
                                $logData['clockinginlog_createtime'] = time();
                                if (!$this->DataControl->insertData('smc_student_clockinginlog', $logData)) {
                                    $this->error = 1;
                                    $this->errortip = "增加考勤记录失败1-2";
                                    return false;
                                }
                            }
                        }
                    }
                }
                elseif ($hourOne['course_checkingintype'] == 5) {
                    //课次考勤(缺勤免费)
                    if ($value['checkin'] == 0) {
                        $stuchecktype_code = '107';
                    } else {
                        $stuchecktype_code = '101';
                    }
                    if ($value['hourstudy_id']) {
                        $bool = $this->updateOneChecken($request['hour_id'], $value['student_id'], $value['type'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code);
                    } else {
                        $comArray = array();
                        $comArray['company_id'] = $request['company_id'];
                        $comArray['school_id'] = $request['school_id'];
                        $comArray['companies_id'] = $balance['companies_id'];
                        $comArray['hour_lessontimes'] = $hourOne['hour_lessontimes'];

                        $bool = $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $request['hour_id'], $value['student_id'], $value['type'], $hourOne['hour_isfree'], $value['checkin'], $value['hourstudy_id'], $value['note'], $request['checktype_code'], $stuchecktype_code, $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], 0, $hourOne['course_checkingintype']);
                    }
                    if (!$bool) {
                        return false;
                    }
                } else {
                    //4累计缺勤
                    $this->error = 1;
                    $this->errortip = '该考勤方式未启用';
                    return false;
                }
                if ($value['checkin'] == 1) {
                    $count_attendance++;
                }

                if ($hourOne['course_inclasstype'] == 2) {
                    $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure,coursebalance_time", "company_id='{$request['company_id']}' and student_id='{$value['student_id']}' and school_id='{$request['school_id']}' and course_id='{$hourOne['course_id']}'");

                    if ($courseBalanceOne['coursebalance_figure'] == 0 && $courseBalanceOne['coursebalance_time'] == 0) {
                        $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
                        $TransactionModel->outClass($value['student_id'], $request['class_id'], 5);
                    }
                }
            }

            $this->error = 0;
            $this->errortip = "考勤成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "请选择学员";
            return false;
        }
    }

    /**
     * 设置单个学生的考勤
     * author: ling
     * 对应接口文档 0001
     * @param $course_id
     * @param $class_id
     * @param $hour_id
     * @param $student_id
     * @param $type
     * @param $hour_isfree
     * @param $checkin
     * @param $hourstudy_id
     * @param $note
     * @param $checktype_code
     * @param $stuchecktype_code
     * @param $comArray
     * @param $course_inclasstype
     * @param $hourDay
     * @param int $is_skip
     * @return bool
     */
    function setOneChecken($paramArray, $course_id, $class_id, $hour_id, $student_id, $type, $hour_isfree, $checkin, $hourstudy_id, $note, $checktype_code, $stuchecktype_code, $comArray, $course_inclasstype, $hourDay, $is_skip = 0, $course_checkingintype = 0)
    {
        $p_classOne = $this->DataControl->getFieldOne('smc_class', "class_type", "class_id='{$class_id}'");
        $random = $this->create_guid();


        $stuCourseBalance = $this->DataControl->getFieldOne("smc_student_coursebalance", "companies_id", "student_id='{$student_id}' and school_id='{$comArray['school_id']}' and course_id='{$course_id}'");

        $comArray['companies_id'] = $stuCourseBalance['companies_id'];

        if ($type == "student") {
            if ($course_inclasstype == 2) {
                if (!$this->DataControl->getFieldOne("smc_class_booking", "booking_id", "student_id='{$student_id}' and hour_id='{$hour_id}'")) {
                    $bookingData = array();
                    $bookingData['class_id'] = $class_id;
                    $bookingData['hour_id'] = $hour_id;
                    $bookingData['student_id'] = $student_id;
                    $bookingData['booking_createtime'] = time();
                    $booking_id = $this->DataControl->insertData("smc_class_booking", $bookingData);
                } else {
                    $bookingOne = $this->DataControl->getFieldOne('smc_class_booking', "booking_id", "student_id='{$student_id}' and hour_id='{$hour_id}'");
                    $booking_id = $bookingOne['booking_id'];
                }
            }

            if ($course_inclasstype == 2 && $checkin == 0) {
//				预约类班级,缺勤不耗课时
                $this->setInOneChechen($student_id, $hour_id, $class_id, $checkin, $note, $checktype_code, $stuchecktype_code, $comArray, $course_inclasstype, $course_checkingintype, $booking_id);
                return true;
            }
            if ($course_inclasstype == 0 && $course_checkingintype == 5 && $checkin == 0) {
//				缺勤不耗课时
                $this->setInOneChechen($student_id, $hour_id, $class_id, $checkin, $note, $checktype_code, $stuchecktype_code, $comArray, $course_inclasstype, $course_checkingintype, 0);
                return true;
            }
            $hourdata = array();
            $hourdata['hourstudy_checkin'] = $checkin;
            $hourdata['hourstudy_makeup'] = 0;
            $hourdata['student_id'] = $student_id;
            $hourdata['hour_id'] = $hour_id;
            $hourdata['class_id'] = $class_id;

            if ($is_skip == 0) {
                if (!$hour_study_id = $this->DataControl->insertData('smc_student_hourstudy', $hourdata)) {
                    $this->error = 1;
                    $this->errortip = "该学员已在该课次存在考勤记录";
                    return false;
                } else {
                    $hourstudy_id = $hour_study_id;
                }
            }

            if (!$hourstudy_id || $hourstudy_id == 0) {
                $hourstudyOne = $this->DataControl->selectOne("select hourstudy_id from smc_student_hourstudy where student_id='{$student_id}' and  class_id='{$class_id}' and  hour_id='{$hour_id}'");

                if ($hourstudyOne) {
                    $hourstudy_id = $hourstudyOne['hourstudy_id'];
                }
            }

            $lesstimeOne = $this->DataControl->selectOne("select coursetimes_id from smc_student_free_coursetimes where student_id='{$student_id}' and  class_id='{$class_id}' and  school_id='{$comArray['school_id']}' and hour_lessontimes='{$comArray['hour_lessontimes']}' and is_use ='0'");

            //查询学员课时账户
            $stuBalance = $this->DataControl->selectOne("select * from smc_student_coursebalance where student_id ='{$student_id}' and  course_id='{$course_id}' and  school_id = '{$comArray['school_id']}' limit 0,1");

            $stuBalance['coursebalance_unitexpend'] = ceil($stuBalance['coursebalance_unitexpend']);
            $stuFigure = $stuBalance['coursebalance_unitexpend'];  //课时消耗单价
            $logData = array();
            $logData['student_id'] = $student_id;
            $logData['school_id'] = $comArray['school_id'];
            $logData['hourstudy_id'] = $hourstudy_id;
            $logData['clockinginlog_type'] = '0';
            $logData['clockinginlog_note'] = $note;
            $logData['checktype_code'] = $checktype_code;
            $logData['clockinginlog_price'] = $stuBalance['coursebalance_figure'] < $stuFigure ? $stuBalance['coursebalance_figure'] : ceil($stuFigure);
            if ($lesstimeOne || $hour_isfree == 1) {
                $logData['clockinginlog_price'] = '0';
            }
            $logData['stuchecktype_code'] = $stuchecktype_code;
            $logData['clockinginlog_checktypename'] = $this->LgStringSwitch("校务系统考勤");
            $logData['clockinginlog_day'] = $hourDay;
            $logData['booking_id'] = $booking_id;
            $logData['clockinginlog_createtime'] = time();
            //此处为子班级的考勤,不计算钱的与课次的部分
            if ($p_classOne['class_type'] == 1) {
                $logData['clockinginlog_price'] = 0;
                if ($this->DataControl->insertData('smc_student_clockinginlog', $logData)) {
                    $this->error = 0;
                    $this->errortip = "增加考勤记录成功";
                    return true;
                } else {
                    $this->error = 1;
                    $this->errortip = "增加考勤记录失败s-子班";
                    return false;
                }
            }

            if ($is_skip == 0) {
                if (!$this->DataControl->insertData('smc_student_clockinginlog', $logData)) {
                    $this->error = 1;
                    $this->errortip = "增加考勤记录失败s-clockinginlog";
                    return false;
                }
            } else {
                //补考勤时变更smc_student_clockinginlog中clockinginlog_price对应的值为实际值
                $clockinginlogData = array();
                $clockinginlogData['clockinginlog_price'] = $stuBalance['coursebalance_figure'] < $stuFigure ? $stuBalance['coursebalance_figure'] : ceil($stuFigure);
                $this->DataControl->updateData("smc_student_clockinginlog", "hourstudy_id='{$hourstudy_id}' ", $clockinginlogData);
            }

            $finalamount = $stuBalance['coursebalance_figure'] - $stuFigure;
//
            if ($lesstimeOne) {
                $this->stuConsumeCourse($stuBalance['coursebalance_figure'], $stuBalance['coursebalance_time'], $student_id, $course_id, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                $this->stuCourseLog($student_id, $course_id, 0, $stuBalance['coursebalance_time'], $stuBalance['coursebalance_figure'], 0, $stuBalance['coursebalance_figure'], $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
            }

            if ($hour_isfree == 0 && $stuBalance && !$lesstimeOne) {
//				课程结转余额
                $stuforwordOne = $this->DataControl->selectOne("select * from  smc_student_courseforward  where student_id = '{$student_id}' and  course_id ='{$course_id}' limit 0,1");

                if ($stuforwordOne && $stuforwordOne['courseforward_price'] > 0) {
                    if ($stuforwordOne['courseforward_deductionmethod'] == 0) {
//
//						$balance_figure = $stuFigure - $forword_price;  //课程余额扣的钱
                        $forword_price = $stuBalance['coursebalance_unitexpend'] - $stuBalance['coursebalance_unitearning'];  //结转余额的扣钱
                        $innote_price = $stuBalance['coursebalance_figure'] - $stuBalance['coursebalance_unitearning'];
                        if ($innote_price < 0) {
                            $innote_price = $stuBalance['coursebalance_figure'];
                            $forword_price += $stuBalance['coursebalance_unitearning'] - $stuBalance['coursebalance_figure'];
                        } else {
                            $innote_price = $stuBalance['coursebalance_unitearning'];
                        }
                        // 平摊抵扣-算钱-结转余额

                        $this->stuCourseLog($student_id, $course_id, 1, $stuBalance['coursebalance_time'], $stuforwordOne['courseforward_price'], $forword_price, $stuforwordOne['courseforward_price'] - $forword_price, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);

                        $innote = $this->LgStringSwitch("平摊抵扣时,耗课收入");
                        if ($innote_price > 0) {
                            $this->addSchoolIncome($comArray['company_id'], $comArray['companies_id'], $comArray['school_id'], $student_id, $innote_price, $innote, $hourDay, $class_id, $course_id, $hourstudy_id);
                        }
                        //
                        $finalamount = $stuBalance['coursebalance_figure'] - $innote_price;
                        if ($innote_price <= 0) {
                            $innote_price = 0;
                            $finalamount = 0;
                        }
                        $this->stuCourseLog($student_id, $course_id, 0, $stuBalance['coursebalance_time'], $stuBalance['coursebalance_figure'], $innote_price, $finalamount, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                        $this->stuConsumeCourse($finalamount, $stuBalance['coursebalance_time'], $student_id, $course_id, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                        $forwordData = array();
                        $forwordData['courseforward_price'] = $stuforwordOne['courseforward_price'] - $forword_price;
                        if ($forwordData['courseforward_price'] < 0) {
                            $forwordData['courseforward_price'] = 0;
                        }
//						$forwordData['courseforward_updatatime'] = time();
                        $forwordData['courseforward_updatatime'] = strtotime($hourDay);
                        $this->DataControl->updateData('smc_student_courseforward', "student_id='{$student_id}' and course_id='{$course_id}' ", $forwordData);

                    } elseif ($stuforwordOne['courseforward_deductionmethod'] == 1) {
                        //先抵优惠算钱

                        $forword_price = $stuforwordOne['courseforward_price'] - $stuBalance['coursebalance_unitexpend'];  //结转余额的扣钱

                        if ($forword_price < 0) {
                            //扣账户余额
                            $finalamount = $stuBalance['coursebalance_figure'] + $forword_price;
                            $balance_figure = $stuBalance['coursebalance_unitexpend'] - $stuforwordOne['courseforward_price']; // 账户余额消耗的钱
                            $forword_figure = $stuforwordOne['courseforward_price'];

                            if ($forword_figure > 0) {
                                $this->stuCourseLog($student_id, $course_id, 1, $stuBalance['coursebalance_time'], $stuforwordOne['courseforward_price'], $forword_figure, 0, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                            }

                            $this->stuCourseLog($student_id, $course_id, 0, $stuBalance['coursebalance_time'], $stuBalance['coursebalance_figure'], $balance_figure, $finalamount, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);


                            $innote = $this->LgStringSwitch("先抵优惠,耗课收入");
                            if ($balance_figure > 0) {
                                $this->addSchoolIncome($comArray['company_id'], $comArray['companies_id'], $comArray['school_id'], $student_id, $balance_figure, $innote, $hourDay, $class_id, $course_id, $hourstudy_id);
                            }
                        } else {
                            //扣账结转余额
                            $this->stuCourseLog($student_id, $course_id, 1, $stuBalance['coursebalance_time'], $stuforwordOne['courseforward_price'], $forword_price, $stuforwordOne['courseforward_price'] - $forword_price, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                            $finalamount = $stuBalance['coursebalance_figure'];
                        }
                        $this->stuConsumeCourse($finalamount, $stuBalance['coursebalance_time'], $student_id, $course_id, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                        $forwordData = array();
                        $forwordData['courseforward_price'] = $stuforwordOne['courseforward_price'] - $forword_price;
//						$forwordData['courseforward_updatatime'] = time();
                        $forwordData['courseforward_updatatime'] = strtotime($hourDay);
                        $this->DataControl->updateData('smc_student_courseforward', "student_id='{$student_id}' and course_id='{$course_id}' ", $forwordData);
                    } elseif ($stuforwordOne['courseforward_deductionmethod'] == 2) {
                        //后抵优惠算钱
                        if ($stuBalance['coursebalance_figure'] - $stuBalance['coursebalance_unitexpend'] < 0) {
                            //扣结转余额
                            $forword_price = $stuBalance['coursebalance_unitexpend'] - $stuBalance['coursebalance_figure'];  //结转金额的操作金额

                            //先清除账户余额
                            if ($stuBalance['coursebalance_figure'] > 0) {
                                $this->stuCourseLog($student_id, $course_id, 0, $stuBalance['coursebalance_time'], $stuBalance['coursebalance_figure'], $stuBalance['coursebalance_figure'], 0, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                            }

                            $this->stuCourseLog($student_id, $course_id, 1, $stuBalance['coursebalance_time'], $stuforwordOne['courseforward_price'], $forword_price, $stuforwordOne['courseforward_price'] - $forword_price, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                            $finalamount = 0;
                            $forwordData = array();
                            $forwordData['courseforward_price'] = $stuforwordOne['courseforward_price'] - $forword_price;
//							$forwordData['courseforward_updatatime'] = time();
                            $forwordData['courseforward_updatatime'] = strtotime($hourDay);
                            $this->DataControl->updateData('smc_student_courseforward', "student_id='{$student_id}' and course_id='{$course_id}' ", $forwordData);
                        } else {

                            $finalamount = $stuBalance['coursebalance_figure'] - $stuBalance['coursebalance_unitexpend'];
                            $income_price = $stuBalance['coursebalance_unitexpend'];

                            $this->stuCourseLog($student_id, $course_id, 0, $stuBalance['coursebalance_time'], $stuBalance['coursebalance_figure'], $stuBalance['coursebalance_unitexpend'], $finalamount, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                            $innote = $this->LgStringSwitch("后抵优惠,耗课收入");
                            if ($income_price > 0) {
                                $this->addSchoolIncome($comArray['company_id'], $comArray['companies_id'], $comArray['school_id'], $student_id, $income_price, $innote, $hourDay, $class_id, $course_id, $hourstudy_id);
                            }

                        }
                        $this->stuConsumeCourse($finalamount, $stuBalance['coursebalance_time'], $student_id, $course_id, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                    }
                } else {
                    $this->stuCourseLog($student_id, $course_id, 0, $stuBalance['coursebalance_time'], $stuBalance['coursebalance_figure'], $stuBalance['coursebalance_unitexpend'], $finalamount, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                    $this->stuConsumeCourse($finalamount, $stuBalance['coursebalance_time'], $student_id, $course_id, $class_id, $hourstudy_id, $comArray['school_id'], $hourDay, $random);
                    $innote = $this->LgStringSwitch("耗课收入");
                    if ($logData['clockinginlog_price'] > 0) {
                        $this->addSchoolIncome($comArray['company_id'], $comArray['companies_id'], $comArray['school_id'], $student_id, $logData['clockinginlog_price'], $innote, $hourDay, $class_id, $course_id, $hourstudy_id);
                    }
                }
            }
            //消购买的项目
            $stuItemtime = $this->DataControl->selectClear("select si.*,cf.feeitem_cnname,cf.feeitem_expendtype
			from smc_student_itemtimes as si
			left JOIN smc_code_feeitem as cf On cf.feeitem_id = si.feeitem_id
			where si.student_id='{$student_id}' and si.course_id='{$course_id}' and si.school_id='{$this->school_id}' and   (cf.feeitem_expendtype ='0' or  cf.feeitem_expendtype ='1')    group by si.itemtimes_id");
            if ($stuItemtime && $p_classOne['class_type'] == 0 && $hour_isfree == 0) {
                foreach ($stuItemtime as $key => $value) {
                    if (($value['itemtimes_number'] > 0) && $value['feeitem_expendtype'] == 0 && $checkin == 1) {
                        $itemData = array();
                        if ($value['itemtimes_number'] > 0) {
                            $pirce = ceil($value['itemtimes_figure'] / $value['itemtimes_number']);
                        } else {
                            $pirce = 0;
                        }
                        $itemData['itemtimes_number'] = $value['itemtimes_number'] - 1;
                        $itemData['itemtimes_figure'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
                        $itemData['itemtimes_updatatime'] = strtotime($hourDay);
                        $this->DataControl->updateData("smc_student_itemtimes", "itemtimes_id='{$value['itemtimes_id']}'", $itemData);

                        $itLogData = array();
                        $itLogData['student_id'] = $student_id;
                        $itLogData['feeitem_id'] = $value['feeitem_id'];
                        $itLogData['companies_id'] = $value['companies_id'];
                        $itLogData['itemtimes_id'] = $value['itemtimes_id'];
                        $itLogData['hourstudy_id'] = $hourstudy_id;
                        $itLogData['log_playname'] = $this->LgStringSwitch("点名上课消耗项目");
                        $itLogData['log_playclass'] = "-";
                        $itLogData['log_fromamount'] = $value['itemtimes_figure'];
                        $itLogData['log_playamount'] = $pirce;
                        $itLogData['log_finalamount'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
                        $itLogData['log_reason'] = $this->LgStringSwitch("点名上课消耗项目" . $value['feeitem_cnname']);
                        $itLogData['log_time'] = strtotime($hourDay);
                        $this->DataControl->insertData("smc_student_itemtimes_log", $itLogData);

                        $data = array();
                        if ($pirce > 0) {
                            $data['company_id'] = $comArray['company_id'];
                            $data['school_id'] = $comArray['school_id'];
                            $data['companies_id'] = $value['companies_id'];
                            $data['student_id'] = $student_id;
                            $data['course_id'] = $course_id;
                            $data['class_id'] = $class_id;
                            $data['hourstudy_id'] = $hourstudy_id;
                            $data['income_price'] = $pirce;
                            $data['income_note'] = $this->LgStringSwitch($value['feeitem_cnname'] . "杂费,耗课收入");
                            $data['income_confirmtime'] = strtotime($hourDay);
                            $data['income_audittime'] = strtotime($hourDay);
                            $data['income_createtime'] = time();
                            $data['income_type'] = 3;
                            $this->DataControl->insertData("smc_school_income", $data);
                        }
                    } elseif (($value['itemtimes_number'] > 0) && $value['feeitem_expendtype'] == 1) {
                        $itemLogOne = $this->DataControl->selectOne("select log_time from smc_student_itemtimes_log where student_id ='{$student_id}' and feeitem_id='{$value['feeitem_id']}' order by log_time DESC limit 0,1  ");
                        if ($itemLogOne && date('Y-m') == date('Y-m', $itemLogOne['log_time'])) {
                            continue;
                        } else {
                            $itemData = array();
                            if ($value['itemtimes_number'] > 0) {
                                $pirce = ceil($value['itemtimes_figure'] / $value['itemtimes_number']);
                            } else {
                                $pirce = 0;
                            }
                            $itemData['itemtimes_number'] = $value['itemtimes_number'] - 1;
                            $itemData['itemtimes_figure'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
//                            $itemData['itemtimes_updatatime'] = time();
                            $itemData['itemtimes_updatatime'] = strtotime($hourDay);
                            $this->DataControl->updateData("smc_student_itemtimes", "itemtimes_id='{$value['itemtimes_id']}'", $itemData);

                            $itLogData = array();
                            $itLogData['student_id'] = $student_id;
                            $itLogData['feeitem_id'] = $value['feeitem_id'];
                            $itLogData['companies_id'] = $value['companies_id'];
                            $itLogData['itemtimes_id'] = $value['itemtimes_id'];
                            $itLogData['hourstudy_id'] = $hourstudy_id;
                            $itLogData['log_playname'] = $this->LgStringSwitch("点名上课消耗项目");
                            $itLogData['log_playclass'] = "-";
                            $itLogData['log_fromamount'] = $value['itemtimes_figure'];
                            $itLogData['log_playamount'] = $pirce;
                            $itLogData['log_finalamount'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
                            $itLogData['log_reason'] = $this->LgStringSwitch("点名上课消耗项目" . $value['feeitem_cnname']);
//                            $itLogData['log_time'] = time();
                            $itLogData['log_time'] = strtotime($hourDay);
                            $this->DataControl->insertData("smc_student_itemtimes_log", $itLogData);

                            $data = array();
                            if ($pirce > 0) {
                                $data['company_id'] = $comArray['company_id'];
                                $data['school_id'] = $comArray['school_id'];
                                $data['companies_id'] = $value['companies_id'];
                                $data['student_id'] = $student_id;
                                $data['income_price'] = $pirce;
                                $data['income_note'] = $this->LgStringSwitch($value['feeitem_cnname'] . "杂费,耗课收入");
                                $data['income_confirmtime'] = strtotime($hourDay);
                                $data['income_audittime'] = strtotime($hourDay);
                                $data['income_createtime'] = time();
                                $data['income_type'] = 3;
                                $this->DataControl->insertData("smc_school_income", $data);
                            }
                        }
                    }
                }
            }
            if ($lesstimeOne && $lesstimeOne['coursetimes_id'] > 0) {
                $lessData = array();
                $lessData['is_use'] = '1';
                $this->DataControl->updateData('smc_student_free_coursetimes', "coursetimes_id='{$lesstimeOne['coursetimes_id']}'", $lessData);
            }

            $integralrule = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_way,integralrule_integral,integralrule_name", "company_id='{$comArray['company_id']}' and integralrule_status=1 and integralrule_class=1");
            if ($integralrule) {
                $addintegral = true;
            } else {
                $addintegral = false;
            }

            //考勤加分-仅出勤添加设置的固定积分
            if ($checkin == 1 && $addintegral && $integralrule['integralrule_way'] == 'num') {
                $integral = $integralrule['integralrule_integral'];
                if ($integral > 0) {
                    $this->addStudentIntegral($comArray['company_id'], $comArray['school_id'], $student_id, $course_id, $integral, $integralrule['integralrule_name'], $this->staffer_id, $hourDay . $integralrule['integralrule_name'], $hour_study_id, time(), $class_id);
                }
            }


            //随主课程一起消耗逻辑

            $sql = "select a.* 
                    from smc_student_coursebalance as a 
                    left join smc_course as b on b.course_id=a.course_id
                    where a.school_id='{$comArray['school_id']}' and a.student_id='{$student_id}' and a.course_id<>'{$course_id}' and b.main_course_id='{$course_id}' and b.course_isfollow=1
                    and (a.coursebalance_figure>0 or a.coursebalance_time>0)
                    "; 

            $followCourseOne=$this->DataControl->selectOne($sql);
            if($followCourseOne && $hour_isfree == 0 && $stuBalance && !$lesstimeOne){

                if($followCourseOne['coursebalance_unitexpend']>$followCourseOne['coursebalance_figure']){
                    $followCourseOne['coursebalance_unitexpend']=$followCourseOne['coursebalance_figure'];
                }
                $finalamount = $followCourseOne['coursebalance_figure'] - $followCourseOne['coursebalance_unitexpend'];

                if($followCourseOne['coursebalance_time']>=1){
                    $this->stuConsumeCourse($finalamount, $followCourseOne['coursebalance_time'], $student_id, $followCourseOne['course_id'], $class_id, $hourstudy_id, $followCourseOne['school_id'], $hourDay, $random.'_1');
                }


                if($followCourseOne['coursebalance_unitexpend']>0){
                    $this->stuCourseLog($student_id, $followCourseOne['course_id'], 0, $followCourseOne['coursebalance_time'], $followCourseOne['coursebalance_figure'], $followCourseOne['coursebalance_unitexpend'], $finalamount, $class_id, $hourstudy_id, $followCourseOne['school_id'], $hourDay, $random.'_1');
                }


                $income_price = $followCourseOne['coursebalance_unitexpend'];

                if($income_price>0){
                    $this->addSchoolIncome($followCourseOne['company_id'], $followCourseOne['companies_id'], $followCourseOne['school_id'], $student_id, $income_price, '随堂耗课', $hourDay, $class_id, $followCourseOne['course_id'], $hourstudy_id);
                }

            }



            return true;
        } elseif ($type == "audition") {
            $data = array();
            $crm_auData = array();
            if ($checkin == 0) {
                $data['audition_isvisit'] = -1;
                $crm_auData['audition_isvisit'] = -1;
            } else {
                $data['audition_isvisit'] = $checkin;
                $crm_auData['audition_isvisit'] = $checkin;
            }
            $data['audition_novisitreason'] = $note;
            $crm_auData['audition_novisitreason'] = $note;
//			$data['audition_updatatime'] = time();
            $data['audition_updatatime'] = strtotime($hourDay);
            $crm_auData['audition_updatetime'] = time();

            $this->DataControl->updateData('smc_class_hour_audition', "hour_id='{$hour_id}' and client_id='{$student_id}' and  audition_isvisit=0", $data);

            $this->DataControl->updateData("crm_client_audition", "hour_id='{$hour_id}' and client_id='{$student_id}'  and  audition_isvisit=0", $crm_auData);

            //添加试听记录

            $this->addCrmAuditionTrack($paramArray['school_id'], $student_id, $crm_auData['audition_isvisit']);

            return true;
        } else {
            $this->error = 1;
            $this->errortip = "学员为空";
            return false;
        }
    }

    /**
     *  修改单个学员的考勤
     * @param $request
     * @return bool
     *
     */
    function updateOneChecken($hour_id, $student_id, $type, $checkin, $hourstudy_id, $note, $checktype_code, $stuchecktype_code)
    {
        if ($type == "student") {
            $hourdata = array();
            $hourdata['hourstudy_checkin'] = $checkin;
            $hourdata['hourstudy_makeup'] = 0;

            if (!$this->DataControl->updateData('smc_student_hourstudy', "hourstudy_id='{$hourstudy_id}'", $hourdata)) {
                $this->error = 1;
                $this->errortip = "更新正式学员明细表失败";
                return false;
            }
            $logData = array();
            $logData['clockinginlog_type'] = 0;
            $logData['clockinginlog_note'] = $note;
            $logData['checktype_code'] = $checktype_code;
            $logData['stuchecktype_code'] = $stuchecktype_code;
            $logData['clockinginlog_checktypename'] = $this->LgStringSwitch("校务系统考勤");

            if (!$this->DataControl->updateData('smc_student_clockinginlog', "hourstudy_id='{$hourstudy_id}' and student_id ='{$student_id}'", $logData)) {
                $this->error = 1;
                $this->errortip = "更新考勤记录失败";
                return false;
            }
            if ($this->DataControl->getFieldOne('smc_class_booking', 'booking_id', "hour_id='{$hour_id}' and student_id ='{$student_id}' and booking_status=0 ")) {
                $bookingData = array();
                $bookingData['booking_checkin'] = $checkin;
                $bookingData['booking_updatatime'] = time();
                $this->DataControl->updateData('smc_class_booking', "hour_id='{$hour_id}' and student_id ='{$student_id}' and booking_status =0", $bookingData);
            }
            return true;

        } elseif ($type == "audition") {
            $data = array();
            if ($checkin == 0) {
                $data['audition_isvisit'] = -1;
            } else {
                $data['audition_isvisit'] = $checkin;
            }
            $data['audition_novisitreason'] = $note;
            $data['audition_updatatime'] = time();
            $this->DataControl->updateData('smc_class_hour_audition', "hour_id='{$hour_id}' and client_id='{$student_id}'", $data);
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "学员为空";
            return false;
        }
    }

    //期度类考勤
    function setInOneChechen($student_id, $hour_id, $class_id, $checkin, $note, $checktype_code, $stuchecktype_code, $comArray, $course_inclasstype = 1, $course_checkingintype = 0, $booking_id = 0)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "hour_id='{$hour_id}'");
        $hourdata = array();
        $hourdata['hourstudy_checkin'] = $checkin;
        $hourdata['hourstudy_makeup'] = 0;
        $hourdata['student_id'] = $student_id;
        $hourdata['hour_id'] = $hour_id;
        $hourdata['class_id'] = $class_id;
        if (!$hour_study_id = $this->DataControl->insertData('smc_student_hourstudy', $hourdata)) {
            $this->error = 1;
            $this->errortip = "更新正式学员明细表失败";
            return false;
        }

        $logData = array();
        $logData['student_id'] = $student_id;
        $logData['booking_id'] = $booking_id;
        $logData['hourstudy_id'] = $hour_study_id;
        $logData['school_id'] = $comArray['school_id'];
        $logData['clockinginlog_type'] = 1;
        $logData['clockinginlog_note'] = $note;
        $logData['checktype_code'] = $checktype_code;
        $logData['clockinginlog_price'] = '0';
        $logData['stuchecktype_code'] = $stuchecktype_code;
        $logData['clockinginlog_checktypename'] = $this->LgStringSwitch("校务系统考勤");
        $logData['clockinginlog_day'] = $hourOne['hour_day'];
        $logData['clockinginlog_createtime'] = strtotime($hourOne['hour_day']);
//        $logData['clockinginlog_createtime'] = time();
        if (!$this->DataControl->insertData('smc_student_clockinginlog', $logData)) {
            $this->error = 1;
            $this->errortip = "增加考勤记录失败3";
            return false;
        }
        $hourOne = $this->DataControl->selectOne(
            " select ch.hour_id,cs.course_inclasstype,ch.course_id,cl.class_type,ch.hour_day
 				  from smc_class_hour as ch
 			 	  left JOIN  smc_course as cs ON cs.course_id = ch.course_id
 			 	  left JOIN smc_class as  cl ON cl.class_id=ch.class_id
 		  		  where hour_id='{$hour_id}' limit 0,1 ");

        if (!($course_inclasstype == 0 && $course_checkingintype == 5 && $checkin == 0)) {
            //消购买的项目
            $stuItemtime = $this->DataControl->selectClear("select si.*,cf.feeitem_cnname,cf.feeitem_expendtype
			from smc_student_itemtimes as si
			left JOIN smc_code_feeitem as cf On cf.feeitem_id = si.feeitem_id
			where si.student_id='{$student_id}' and si.course_id='{$hourOne['course_id']}'  and si.school_id='{$this->school_id}'  and  (cf.feeitem_expendtype ='0' or  cf.feeitem_expendtype ='1')    group by si.itemtimes_id");
            if ($stuItemtime && $hourOne['class_type'] == 0 && $hourOne['hour_isfree'] == 0) {
                foreach ($stuItemtime as $key => $value) {
                    if (($value['itemtimes_number'] > 0) && $value['feeitem_expendtype'] == 0 && $checkin == 1) {
                        $itemData = array();
                        if ($value['itemtimes_number'] > 0) {
                            $pirce = ceil($value['itemtimes_figure'] / $value['itemtimes_number']);
                        } else {
                            $pirce = 0;
                        }
                        $itemData['itemtimes_number'] = $value['itemtimes_number'] - 1;
                        $itemData['itemtimes_figure'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
//                    $itemData['itemtimes_updatatime'] = time();
                        $itemData['itemtimes_updatatime'] = strtotime($hourOne['hour_day']);
                        $this->DataControl->updateData("smc_student_itemtimes", "itemtimes_id='{$value['itemtimes_id']}'", $itemData);

                        $itLogData = array();
                        $itLogData['student_id'] = $student_id;
                        $itLogData['feeitem_id'] = $value['feeitem_id'];
                        $itLogData['companies_id'] = $value['companies_id'];
                        $itLogData['itemtimes_id'] = $value['itemtimes_id'];
                        $itLogData['hourstudy_id'] = $hour_study_id;
                        $itLogData['log_playname'] = $this->LgStringSwitch("点名上课消耗项目");
                        $itLogData['log_playclass'] = "-";
                        $itLogData['log_fromamount'] = $value['itemtimes_figure'];
                        $itLogData['log_playamount'] = $pirce;
                        $itLogData['log_finalamount'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
                        $itLogData['log_reason'] = $this->LgStringSwitch("点名上课消耗项目" . $value['feeitem_cnname']);
//                    $itLogData['log_time'] = time();
                        $itLogData['log_time'] = strtotime($hourOne['hour_day']);
                        $this->DataControl->insertData("smc_student_itemtimes_log", $itLogData);
                        $data = array();
//					$comArray['company_id'],$comArray['companies_id'],$comArray['school_id'],$student_id,$stuFigure,$innote,$hourDay
                        if ($pirce > 0) {
                            $data['company_id'] = $comArray['company_id'];
                            $data['school_id'] = $comArray['school_id'];
                            $data['companies_id'] = $value['companies_id'];
                            $data['student_id'] = $student_id;
                            $data['class_id'] = $class_id;
                            $data['course_id'] = $hourOne['course_id'];
                            $data['hourstudy_id'] = $hour_study_id;
                            $data['income_price'] = $pirce;
                            $data['income_note'] = $this->LgStringSwitch($value['feeitem_cnname'] . "杂费,耗课收入");
                            $data['income_confirmtime'] = strtotime($hourOne['hour_day']);
                            $data['income_audittime'] = strtotime($hourOne['hour_day']);
                            $data['income_createtime'] = time();
                            $data['income_type'] = 3;
                            $this->DataControl->insertData("smc_school_income", $data);
                        }
                    } elseif (($value['itemtimes_number'] > 0) && $value['feeitem_expendtype'] == 1) {
                        $itemLogOne = $this->DataControl->selectOne("select log_time from smc_student_itemtimes_log where student_id ='{$student_id}' and feeitem_id='{$value['feeitem_id']}' order by log_time DESC limit 0,1  ");
                        if ($itemLogOne && date('Y-m') == date('Y-m', $itemLogOne['log_time'])) {
                            continue;
                        } else {
                            $itemData = array();
                            if ($value['itemtimes_number'] > 0) {
                                $pirce = ceil($value['itemtimes_figure'] / $value['itemtimes_number']);
                            } else {
                                $pirce = 0;
                            }
                            $itemData['itemtimes_number'] = $value['itemtimes_number'] - 1;
                            $itemData['itemtimes_figure'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
//                        $itemData['itemtimes_updatatime'] = time();
                            $itemData['itemtimes_updatatime'] = strtotime($hourOne['hour_day']);
                            $this->DataControl->updateData("smc_student_itemtimes", "itemtimes_id='{$value['itemtimes_id']}'", $itemData);

                            $itLogData = array();
                            $itLogData['student_id'] = $student_id;
                            $itLogData['feeitem_id'] = $value['feeitem_id'];
                            $itLogData['companies_id'] = $value['companies_id'];
                            $itLogData['itemtimes_id'] = $value['itemtimes_id'];
                            $itLogData['hourstudy_id'] = $hour_study_id;
                            $itLogData['log_playname'] = $this->LgStringSwitch("点名上课消耗项目");
                            $itLogData['log_playclass'] = "-";
                            $itLogData['log_fromamount'] = $value['itemtimes_figure'];
                            $itLogData['log_playamount'] = $pirce;
                            $itLogData['log_finalamount'] = ($value['itemtimes_figure'] - $pirce) >= 0 ? ($value['itemtimes_figure'] - $pirce) : '0';
                            $itLogData['log_reason'] = $this->LgStringSwitch("点名上课消耗项目" . $value['feeitem_cnname']);
//                        $itLogData['log_time'] = time();
                            $itLogData['log_time'] = strtotime($hourOne['hour_day']);
                            $this->DataControl->insertData("smc_student_itemtimes_log", $itLogData);

                            $data = array();
                            if ($pirce > 0) {
                                $data['company_id'] = $comArray['company_id'];
                                $data['school_id'] = $comArray['school_id'];
                                $data['companies_id'] = $value['companies_id'];
                                $data['student_id'] = $student_id;
                                $data['class_id'] = $class_id;
                                $data['course_id'] = $hourOne['course_id'];
                                $data['hourstudy_id'] = $hour_study_id;
                                $data['income_price'] = $pirce;
                                $data['income_note'] = $this->LgStringSwitch($value['feeitem_cnname'] . "杂费,耗课收入");
                                $data['income_confirmtime'] = strtotime($hourOne['hour_day']);
                                $data['income_audittime'] = strtotime($hourOne['hour_day']);
                                $data['income_createtime'] = time();
                                $data['income_type'] = 3;
                                $this->DataControl->insertData("smc_school_income", $data);
                            }
                        }
                    }
                }
            }
        }
        if ($hourOne['course_inclasstype'] == 2) {
            $bookingData = array();
            $bookingData['booking_checkin'] = $checkin;
            $bookingData['booking_updatatime'] = strtotime($hourOne['hour_day']);
            $this->DataControl->updateData('smc_class_booking', "hour_id='{$hour_id}' and student_id ='{$student_id}' and booking_status =0", $bookingData);
        }

        $integralrule = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_way,integralrule_integral,integralrule_name", "company_id='{$comArray['company_id']}' and integralrule_status=1 and integralrule_class=1");
        if ($integralrule) {
            $addintegral = true;
        } else {
            $addintegral = false;
        }

        //考勤加分-仅出勤添加设置的固定积分
        if ($checkin == 1 && $addintegral && $integralrule['integralrule_way'] == 'num') {
            $integral = $integralrule['integralrule_integral'];
            if ($integral > 0) {
                $this->addStudentIntegral($comArray['company_id'], $comArray['school_id'], $student_id, $hourOne['course_id'], $integral, $integralrule['integralrule_name'], $this->staffer_id, $hourOne['hour_day'] . $integralrule['integralrule_name'], $hour_study_id, time(), $class_id);
            }
        }

        return true;
    }

    /**
     * 新增学校收益交易记录
     *company_id
     * companies_id
     * school_id
     * student_id
     * income_price
     * income_note
     * income_confirmtime
     */
    function addSchoolIncome($company_id, $companies_id, $school_id, $student_id, $income_price, $income_note, $hourDay, $class_id = 0, $course_id = 0, $hourstudy_id = 0)
    {
        $data = array();
        $data['company_id'] = $company_id;
        $data['school_id'] = $school_id;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['class_id'] = $class_id;
        $data['course_id'] = $course_id;
        $data['hourstudy_id'] = $hourstudy_id;
        $data['income_price'] = $income_price;
        $data['income_note'] = $income_note;
        $data['income_confirmtime'] = time();// (date('Y-m', strtotime($hourDay)) == date('Y-m')) ? strtotime($hourDay) : time()20230721natasha-cz-lh
        $data['income_audittime'] = strtotime($hourDay);
        $data['income_createtime'] = time();
        $data['income_type'] = 0;
        $this->DataControl->insertData("smc_school_income", $data);

        $integralrule = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_way,integralrule_integral,integralrule_name", "company_id='{$company_id}' and integralrule_status=1 and integralrule_class=1");
        if ($integralrule) {
            $addintegral = true;
        } else {
            $addintegral = false;
        }

        //课消加分-按比例对消耗金额发放积分
        if ($data['income_type'] == '0' && $addintegral && $integralrule['integralrule_way'] == 'rate') {
            $integral = ceil($income_price * $integralrule['integralrule_integral'] / 100);
            if ($integral > 0) {
                $this->addStudentIntegral($company_id, $school_id, $student_id, $course_id, $integral, $integralrule['integralrule_name'], $this->staffer_id, $hourDay . $integralrule['integralrule_name'], $hourstudy_id, time(), $class_id);
            }
        }
    }

    /**
     *点名考勤消耗课程
     * @return bool
     * $finalamount 最终的金额
     * $coursebalance_time  原本课程次数
     * $student_id 学员id
     *$course_id 课程id
     */
    function stuConsumeCourse($finalamount, $coursebalance_time, $student_id, $course_id, $class_id, $hourstudy_id, $school_id, $hourDay, $random)
    {

        $stuBalanceOne = $this->DataControl->selectOne("select companies_id from smc_student_coursebalance where student_id ='{$student_id}' and  course_id='{$course_id}' and  school_id = '{$school_id}' limit 0,1");

        $stuBalance['coursebalance_time'] = $coursebalance_time;
        $balanceData = array();
        $balanceData['coursebalance_figure'] = $finalamount <= '0' ? 0 : $finalamount;
        $balanceData['coursebalance_time'] = ($stuBalance['coursebalance_time'] - 1) <= '0' ? 0 : ($stuBalance['coursebalance_time'] - 1);
        $balanceData['coursebalance_updatatime'] = time();

//        $balanceData['log_random'] = $random;
//		$balanceData['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$school_id}' ", $balanceData);

        $timeData = array();
        $timeData['student_id'] = $student_id;
        $timeData['course_id'] = $course_id;
        $timeData['companies_id'] = $stuBalanceOne['companies_id'];
        $timeData['class_id'] = $class_id;
        $timeData['hourstudy_id'] = $hourstudy_id;
        $timeData['timelog_playname'] = $this->LgStringSwitch("点名扣除课次");
        $timeData['timelog_playclass'] = "-";
        $timeData['log_random'] = $random;
        $timeData['timelog_fromtimes'] = $stuBalance['coursebalance_time'];
        $timeData['timelog_playtimes'] = 1;
        $timeData['timelog_finaltimes'] = ($stuBalance['coursebalance_time'] - 1) <= '0' ? 0 : ($stuBalance['coursebalance_time'] - 1);
        $timeData['timelog_reason'] = $this->LgStringSwitch("点名扣除课次");
        $timeData['school_id'] = $school_id;

//		$timeData['timelog_time'] = time();
        if ($hourDay) {
            $timeData['timelog_time'] = strtotime($hourDay);
        } else {
            $timeData['timelog_time'] = time();
        }


        if (!$this->DataControl->insertData('smc_student_coursebalance_timelog', $timeData)) {

            $this->error = 1;
            $this->errortip = "课程课时消费记录错误";
            return false;
        }

    }

    /**
     * 点名扣除课程余额
     * author: ling
     * 对应接口文档 0001
     * @param $student_id
     * @param $course_id
     * @param $log_class
     * @param $coursebalance_time
     * @param $coursebalance_figure
     * @param $stuFigure
     * @param $finalamount
     * @param $class_id
     * @param $hourstudy_id
     * @param $school_id
     * @param $hourDay
     * @param $random
     * @param int $is_consume
     * @return bool
     */
    function stuCourseLog($student_id, $course_id, $log_class, $coursebalance_time, $coursebalance_figure, $stuFigure, $finalamount, $class_id, $hourstudy_id, $school_id, $hourDay, $random, $is_consume = 1)
    {
        $inclasstype = $this->DataControl->getFieldOne('smc_course', 'course_inclasstype', "course_id='{$course_id}'");
        if ($is_consume == 1) {
            $num = 1;
        } else {
            $num = 0;
        }

        $stuBalanceOne = $this->DataControl->selectOne("select companies_id from smc_student_coursebalance where student_id ='{$student_id}' and  course_id='{$course_id}' and  school_id = '{$school_id}' limit 0,1");

//        if ($inclasstype['course_inclasstype'] == 2 && $finalamount <= 0) {
//            $Model = new \Model\Smc\TransactionModel($this->publicarray);
//            $Model->outClass($student_id, $class_id, 0);
//        }
        $stuBalance['coursebalance_figure'] = $coursebalance_figure;
        $courseData = array();
        $courseData['student_id'] = $student_id;
        $courseData['course_id'] = $course_id;
        $courseData['school_id'] = $school_id;
        $courseData['class_id'] = $class_id;
        $courseData['companies_id'] = $stuBalanceOne['companies_id'];
        $courseData['hourstudy_id'] = $hourstudy_id;
        $courseData['log_random'] = $random;
        $courseData['log_class'] = $log_class;
        $courseData['log_playname'] = $this->LgStringSwitch('点名扣除课程余额');
        $courseData['log_playclass'] = '-';
        $courseData['log_fromamount'] = $stuBalance['coursebalance_figure'];
        $courseData['log_playamount'] = $stuBalance['coursebalance_figure'] <= $stuFigure ? $stuBalance['coursebalance_figure'] : $stuFigure;
        $courseData['log_finalamount'] = $finalamount <= '0' ? 0 : $finalamount;

        $courseData['log_fromtimes'] = $coursebalance_time;
        $courseData['log_playtimes'] = $num;
        $courseData['log_finaltimes'] = ($coursebalance_time - $num) <= '0' ? 0 : ($coursebalance_time - $num);

        $courseData['log_reason'] = $this->LgStringSwitch('点名扣除课程余额');
        if ($log_class == 1) {
            $courseData['log_reason'] = $this->LgStringSwitch('点名扣除结转余额');
            $courseData['log_playname'] = $this->LgStringSwitch('点名扣除结转余额');
        }
		$courseData['log_time'] = time();
//        if ($hourDay) {
//            $courseData['log_time'] = strtotime($hourDay);
//        } else {
//            $courseData['log_time'] = time();
//        }
        if (!$this->DataControl->insertData("smc_student_coursebalance_log", $courseData)) {

            $this->error = 1;
            $this->errortip = "课程消费记录错误";
            return false;
        }
    }

    /**
     * 单个学生补考勤
     * author: ling
     * 对应接口文档 0001
     */
    function stuReplenishCheckinAction($request)
    {
        $arr_hour_id = json_decode(stripslashes($request['arr_hour_id']), true);
        if (is_array($arr_hour_id) && count($arr_hour_id) > 0) {
            $str_hour_id = implode(',', $arr_hour_id);
        } else {
            $this->error = '1';
            $this->errortip = '请选择课次';
            return false;
        }

        if (!$request['student_id']) {
            $this->error = '1';
            $this->errortip = '请选择学生';
            return false;
        }

        $hour_List = $this->DataControl->selectClear("select  hour_id from smc_class_hour  where hour_id in ({$str_hour_id}) and  hour_iswarming = 0 and hour_isfree = 0");
        if ($hour_List) {
            $count_hour = count($hour_List);
        } else {
            $count_hour = 0;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");
        if (!$classOne) {
            $this->error = '1';
            $this->errortip = '请选择班级';
            return false;
        }

        $companies = $this->DataControl->selectClear("
SELECT 
	cc.companies_issupervise
FROM
	smc_course AS c
	LEFT JOIN smc_school_coursecat_subject AS s ON c.coursecat_id = s.coursecat_id 
	AND school_id = '{$request['school_id']}'
	LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = s.companies_id
	where c.course_id = '{$classOne['course_id']}' and cc.companies_issupervise = '1'");

        if ($companies) {
            $this->error = '1';
            $this->errortip = '你选择的课程主体已被监管，无法补考勤！';
            return false;
        }


        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$classOne['course_id']}'");
        $sql = "select c.student_id,c.coursebalance_figure,c.coursebalance_time,c.companies_id
					from smc_student_coursebalance  as c
  					where c.student_id = '{$request['student_id']}' and c.course_id = '{$classOne['course_id']}'  and school_id = '{$request['school_id']}' limit 0,1";
        $balanceOne = $this->DataControl->selectOne($sql);
        if ($courseOne['course_inclasstype'] <> 1) {
            if ($count_hour > $balanceOne['coursebalance_time']) {
                $this->error = 1;
                $this->errortip = "课次不足,无法点名";
                return false;
            }
        }

        $Feewaiver = $this->DataControl->selectOne("
            select st.student_cnname
            from smc_payfee_order_pay as op
             left join smc_payfee_order as  po  ON op.order_pid = po.order_pid
             left join smc_payfee_order_course as  poc  ON poc.order_pid = po.order_pid
             left join smc_student as st On st.student_id = po.student_id
            where  po.school_id='{$request['school_id']}'  
            and  op.paytype_code='feewaiver' and op.pay_issuccess ='0' and po.student_id ='{$request['student_id']}' and poc.course_id='{$classOne['course_id']}' limit 0,1");
        if ($Feewaiver) {
            $this->error = 1;
            $this->errortip = $Feewaiver['student_cnname'] . '存在未审核的减免的订单';
            return false;
        }
        foreach ($arr_hour_id as $value) {
            $hourOne = $this->DataControl->selectOne(
                "select ch.hour_id,ch.hour_ischecking,c.class_status,c.class_id,ch.course_id,ch.hour_lessontimes,ch.hour_day,ch.hour_isfree,cs.course_inclasstype,ch.hour_day,cs.course_checkingintype,cs.course_checkingminday,ch.hour_inarrivenums
 				  from smc_class_hour as ch
 			 	  left JOIN  smc_class as c ON  ch.class_id =c.class_id
 			 	  left JOIN  smc_course as cs ON cs.course_id = c.course_id
 			 	  left join  smc_school as s On c.school_id = s.school_id
 		  		  where hour_id='{$value}' limit 0,1 ");

            $comArray = array();
            $comArray['company_id'] = $request['company_id'];
            $comArray['school_id'] = $request['school_id'];
            $comArray['companies_id'] = $balanceOne['companies_id'];
            $comArray['hour_lessontimes'] = $hourOne['hour_lessontimes'];

            if ($hourOne['course_inclasstype'] == 0) {
                $this->setOneChecken($request, $hourOne['course_id'], $request['class_id'], $hourOne['hour_id'], $request['student_id'], 'student', $hourOne['hour_isfree'], '1', '0', '补考勤', 'sys', '101', $comArray, $hourOne['course_inclasstype'], $hourOne['hour_day'], '0');
            } else {
                if ($hourOne['course_inclasstype'] == 2) {
                    if (!$this->DataControl->getFieldOne("smc_class_booking", "booking_id", "student_id='{$request['student_id']}' and hour_id='{$hourOne['hour_id']}'")) {
                        $bookingData = array();
                        $bookingData['class_id'] = $request['class_id'];
                        $bookingData['hour_id'] = $hourOne['hour_id'];
                        $bookingData['student_id'] = $request['student_id'];
                        $bookingData['booking_createtime'] = time();
                        $booking_id = $this->DataControl->insertData("smc_class_booking", $bookingData);
                    } else {
                        $bookingOne = $this->DataControl->getFieldOne('smc_class_booking', "booking_id", "student_id='{$request['student_id']}' and hour_id='{$hourOne['hour_id']}'");
                        $booking_id = $bookingOne['booking_id'];
                    }
                } else {
                    $booking_id = 0;
                }
                $this->setInOneChechen($request['student_id'], $hourOne['hour_id'], $request['class_id'], 1, '补考勤', 'sys', '101', $comArray, $hourOne['course_inclasstype'], $hourOne['course_checkingintype'], $booking_id);
            }

            if ($hourOne['course_inclasstype'] == 2) {
                $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure,coursebalance_time", "company_id='{$request['company_id']}' and student_id='{$request['student_id']}' and school_id='{$request['school_id']}' and course_id='{$hourOne['course_id']}'");

                if ($courseBalanceOne['coursebalance_figure'] == 0 && $courseBalanceOne['coursebalance_time'] == 0) {
                    $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
                    $TransactionModel->outClass($request['student_id'], $request['class_id'], 5);
                }
            }
            $hourData = array();
            $hourData['hour_inarrivenums'] = $hourOne['hour_inarrivenums'] + 1;
            $hourData['hour_updatatime'] = time();
            $this->DataControl->updateData("smc_class_hour", "hour_id='{$hourOne['hour_id']}'", $hourData);
        }
        $this->error = '0';
        $this->errortip = '考勤成功';
        return true;
    }

    /**
     * 加盟校一键考勤
     * author: ling
     * 对应接口文档 0001
     */
    function autoCheckenAction($request)
    {
        if (!$request['class_id']) {
            $this->error = '1';
            $this->errortip = '班级id为空';
            return false;
        }
        if (!$request['hour_id']) {
            $this->error = '1';
            $this->errortip = '课时id为空';
            return false;
        }
        if ($request['company_id'] == '8888') {
            $this->error = '1';
            $this->errortip = '该功能暂时关闭';
            return false;
        }

        $comapnyOne = $this->DataControl->getFieldOne('gmc_company', 'comapny_isclocking', "company_id='{$request['company_id']}'");
        if ($comapnyOne['comapny_isclocking'] != 1) {
            $this->error = '1';
            $this->errortip = '此功能不适用该班级';
            return false;
        }

        $hourOne = $this->DataControl->selectOne(
            "select ch.hour_ischecking,ch.class_id,ch.hour_day,cs.course_inclasstype,ch.hour_starttime,ch.hour_endtime,c.course_id,ch.hour_iswarming
				  from smc_class_hour as ch
 			 	  left JOIN  smc_class as c ON  ch.class_id =c.class_id
 			 	  left JOIN  smc_course as cs ON cs.course_id = c.course_id
 				  where hour_id='{$request['hour_id']}' limit 0,1 ");
        if (!$hourOne) {
            $this->error = '1';
            $this->errortip = '一键考勤未查询到课时';
            return false;
        }
        if ($hourOne['course_inclasstype'] != 0 && $hourOne['course_inclasstype'] != 1) {
            $this->error = '1';
            $this->errortip = '该功能只适用于课次类班级和期度类班级';
            return false;
        }

        if ($hourOne['hour_iswarming'] != 0) {
            $this->error = '0';
            $this->errortip = '暖身课';
            return true;

        }

        $hour_day = date('Y-m-d', strtotime($hourOne['hour_day']));


        $sqlField = "ss.student_id,s.student_branch,s.student_cnname,s.student_enname,ss.study_beginday";
        $sql = "
			select  {$sqlField},
			(select h.hour_isfree from smc_class_hour as  h where  h.hour_id='{$request['hour_id']}') as hour_isfree,
                (select coursetimes_id from smc_student_free_coursetimes as fc 
                left join smc_class_hour as ch ON ch.hour_lessontimes = fc.hour_lessontimes 
			    where fc.student_id=ss.student_id and fc.school_id='{$request['school_id']}' and fc.is_use=0 and fc.course_id ='{$hourOne['course_id']}'and ch.hour_id='{$request['hour_id']}' limit 0,1) as coursetimes_id
			from smc_student_study as ss
			left join smc_student as s ON ss.student_id = s.student_id
			where (ss.class_id ='{$request['class_id']}' and ss.study_isreading ='1' and  ss.study_beginday <='{$hour_day}') or (ss.class_id ='{$request['class_id']}' and ss.study_endday >'{$hour_day}' and ss.study_beginday <='{$hour_day}')
			";

        $studentList = $this->DataControl->selectClear($sql);


        if ($studentList) {
            foreach ($studentList as $key => $value) {
                $absenceOne = $this->DataControl->selectOne("select sa.absence_id from smc_student_absence as sa, smc_student_absence_hour as sah where sa.absence_id=sah.absence_id and sa.school_id='{$request['school_id']}' and sa.student_id='{$value['student_id']}' and sah.hour_id ='{$request['hour_id']}' and absence_hour_status <>'-1'  limit 0,1");

                if ($absenceOne) {
                    $studentList[$key]['checkin'] = 0;
                } else {
                    $studentList[$key]['checkin'] = 1;
                }
                $studentList[$key]['type'] = 'student';
            }

            $re_array['student_checkin_list'] = json_encode($studentList, JSON_UNESCAPED_UNICODE);
            $re_array['hour_id'] = $request['hour_id'];
            $re_array['staffer_id'] = $request['staffer_id'];
            $re_array['class_id'] = $request['class_id'];
            $re_array['checktype_code'] = $request['checktype_code'];
            $re_array['company_id'] = $request['company_id'];
            $re_array['school_id'] = $request['school_id'];

            if (!$this->setClassChecken($re_array)) {
                $this->error = '1';
                return false;
            } else {
                $this->error = '0';
                $this->errortip = '设置成功';
                return true;
            }
        } else {
            $this->error = '1';
            $this->errortip = '未查询到可以考勤的学生,请调整排课,或者将学生提前入班';
            return false;
        }
    }

    /**
     * 考勤免费课时
     * author: ling
     * 对应接口文档 0001
     */
    function CheckingFreeHour($paramArray, $hourOne)
    {
        $student_checkin_list = json_decode(stripslashes($paramArray['student_checkin_list']), true);
        //20240320 点名上课在以前的逻辑上修改，删除新展示的禁用的crm考勤过的名单 --- 开始
        if($student_checkin_list){
            foreach ($student_checkin_list as $key=>$student_checkin_list_var){
                if($student_checkin_list_var['isprohibit'] == 1){
                    unset($student_checkin_list[$key]);
                }
            }
        }
        //20240320 点名上课在以前的逻辑上修改，删除新展示的禁用的crm考勤过的名单 --- 结束
        $studentCount = count($student_checkin_list);
        if ($student_checkin_list) {
            foreach ($student_checkin_list as $key => $value) {
                if ($value['checkin'] == 0) {
                    $stuchecktype_code = '107';
                } else {
                    $stuchecktype_code = '101';
                }
                if ($value['hourstudy_id'] > 0) {
                    $data = array();
                    $data['hourstudy_checkin'] = $value['checkin'];
                    $this->DataControl->updateData('smc_student_hourstudy', "hourstudy_id='{$value['hourstudy_id']}'", $data);
                    $clockingData = array();
                    $clockingData['clockinginlog_note'] = $value['note'];
                    $clockingData['stuchecktype_code'] = $stuchecktype_code;
                    $this->DataControl->updateData('smc_student_clockinginlog', "hourstudy_id='{$value['hourstudy_id']}' and student_id='{$value['student_id']}'", $data);

                } else {
                    if ($value['type'] == 'audition') {

                        $data = array();
                        $crm_auData = array();
                        if ($value['checkin'] == 0) {
                            $data['audition_isvisit'] = -1;
                            $crm_auData['audition_isvisit'] = -1;
                        } else {
                            $data['audition_isvisit'] = $value['checkin'];
                            $crm_auData['audition_isvisit'] = $value['checkin'];
                        }
                        $data['audition_novisitreason'] = $value['note'];
                        $crm_auData['audition_novisitreason'] = $value['note'];
                        $data['audition_updatatime'] = strtotime($hourOne['hour_day']);
                        $crm_auData['audition_updatetime'] = time();

                        $this->DataControl->updateData('smc_class_hour_audition', "hour_id='{$hourOne['hour_id']}' and client_id='{$value['student_id']}' and  audition_isvisit=0", $data);

                        $this->DataControl->updateData("crm_client_audition", "hour_id='{$hourOne['hour_id']}' and client_id='{$value['student_id']}'  and  audition_isvisit=0", $crm_auData);

                        //添加试听记录

                        $this->addCrmAuditionTrack($hourOne['school_id'], $value['student_id'], $crm_auData['audition_isvisit']);
                    } else {
                        $data = array();
                        $data['hour_id'] = $hourOne['hour_id'];
                        $data['class_id'] = $hourOne['class_id'];
                        $data['student_id'] = $value['student_id'];
                        $data['hourstudy_checkin'] = $value['checkin'];
                        if ($hourstudy_id = $this->DataControl->insertData('smc_student_hourstudy', $data)) {
                            $clockingData = array();
                            $clockingData['student_id'] = $value['student_id'];
                            $clockingData['school_id'] = $this->school_id;
                            $clockingData['hourstudy_id'] = $hourstudy_id;
                            $clockingData['clockinginlog_type'] = 0;
                            if (isset($value['booking_id'])) {
                                $clockingData['booking_id'] = $value['booking_id'];
                                $clockingData['clockinginlog_type'] = 1;
                            }
                            $clockingData['stuchecktype_code'] = $stuchecktype_code;
                            $clockingData['checktype_code'] = 'sys';
                            $clockingData['clockinginlog_checktypename'] = '校务系统考勤';
                            $clockingData['clockinginlog_day'] = $hourOne['hour_day'];
                            $clockingData['clockinginlog_note'] = $value['note'];
                            $clockingData['clockinginlog_price'] = 0;
                            $clockingData['clockinginlog_createtime'] = time();
                            $this->DataControl->insertData("smc_student_clockinginlog", $clockingData);
                        }
                    }
                }
            }
        }
        $free_data = array();
        $free_data['hour_ischecking'] = '1';
        $free_data['hour_staffer_id'] = intval($paramArray['staffer_id']);
        $free_data['hour_inarrivenums'] = $studentCount + $hourOne['hour_inarrivenums'];
        $free_data['hour_updatatime'] = time();
        $this->DataControl->updateData("smc_class_hour", "hour_id='{$hourOne['hour_id']}'", $free_data);
        return true;
    }

    function addCrmAuditionTrack($school_id, $client_id, $audition_isvisit)
    {

        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name,marketer_id", "staffer_id='{$this->staffer_id}'");
        if (!$marketerOne) {
            $stafferOne = $this->stafferOne;
            $marketer_data = array();
            $marketer_data['staffer_id'] = $stafferOne['staffer_id'];
            $marketer_data['company_id'] = $stafferOne['company_id'];
            $marketer_data['marketer_name'] = $stafferOne['staffer_cnname'];
            $marketer_data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $marketer_data['marketer_img'] = $stafferOne['staffer_img'];
            $marketer_data['marketer_createtime'] = time();
            $id = $this->DataControl->insertData("crm_marketer", $marketer_data);

            $marketerOne = array();
            $marketerOne['marketer_id'] = $id;
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        $dataTrack = array();
        $dataTrack['school_id'] = $school_id;
        $dataTrack['client_id'] = $client_id;
        $dataTrack['marketer_id'] = $marketerOne['marketer_id'];
        $dataTrack['marketer_name'] = $marketerOne['marketer_name'];
        $dataTrack['track_validinc'] = 1;
        $dataTrack['track_createtime'] = time();

        if ($audition_isvisit == 1) {
            $dataTrack['track_linktype'] = $this->LgStringSwitch("确认试听");
            $dataTrack['track_note'] = $this->LgStringSwitch("确认试听成功");
        } else {
            $dataTrack['track_linktype'] = $this->LgStringSwitch("取消试听");
            $dataTrack['track_note'] = $this->LgStringSwitch("取消试听");
        }
        $this->DataControl->insertData("crm_client_track", $dataTrack);
    }

}
