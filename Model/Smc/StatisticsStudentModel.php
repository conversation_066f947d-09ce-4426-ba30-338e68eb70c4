<?php


namespace Model\Smc;

class StatisticsStudentModel extends modelTpl
{
    /*
     * MySQL 8 性能优化说明:
     * 1. 建议创建以下索引提高查询性能:
     *    CREATE INDEX idx_student_school_changelog ON smc_student_changelog(school_id, changelog_day, stuchange_code);
     *    CREATE INDEX idx_student_enrolled_school ON smc_student_enrolled(school_id, enrolled_status);
     *    CREATE INDEX idx_student_study_school_time ON smc_student_study(school_id, study_beginday, study_endday);
     *    CREATE INDEX idx_class_hour_school_day ON smc_class_hour(school_id, hour_day, hour_ischecking);
     *    CREATE INDEX idx_student_hourstudy ON smc_student_hourstudy(hour_id, student_id, hourstudy_checkin);
     *    CREATE INDEX idx_clockinginlog_day ON smc_student_clockinginlog(clockinginlog_day, student_id);
     * 
     * 2. 优化复杂的子查询，使用适当的JOIN和索引
     * 3. 避免在WHERE子句中使用函数，使用预计算的值
     * 4. 使用GROUP BY代替复杂的子查询统计
     */

    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = '0';//操作公司
    public $school_id = '0';//操作学校
    public $staffer_id = '0';//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id=$publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    /**
     * 校务学员统计 - 学员概览
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function StatisticsStudentOver($request)
    {
        $datawhere = '1';
        $studywhere = '1';
        $changewhere = '1';
        if (isset($request['starttime']) && $request['starttime']) {
            $starttime = strtotime($request['starttime']);
//            $datawhere .= " and enrolled_leavetime >= '{$starttime}'";
//            $studywhere .= " and study_endday >= '{$request['starttime']}'";
            $changewhere .= " and changelog_day >= '{$request['starttime']}'";
        } else {
            $starttime = strtotime(date("Y-m-d"));
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endtime = strtotime($request['endtime']) + 24 * 60 * 60 - 1;
            if ($request['endtime'] == date("Y-m-d")) {
                $endtime = time();
            }
            $request['endtime'] =   $request['endtime'] > date("Y-m-d") ? date("Y-m-d") : $request['endtime'];
            $datawhere .= " and changelog_day <= '{$request['endtime']}'";
            $studywhere .= " and study_beginday <= '{$request['endtime']}' and study_endday >= '{$request['endtime']}'";
            $changewhere .= " and changelog_day <='{$request['endtime']}'";
        } else {
            $endtime = strtotime(date("Y-m-d")) + 24 * 60 * 60 - 1;
        }
        if ($request['type'] == 0) {
            $seconds = 24 * 60 * 60;
            $before_starttime = $starttime - $seconds;
            $before_endtime = $endtime - $seconds;
        } elseif ($request['type'] == 1) {
            $before_starttime = strtotime(date('Y-m-01 00:00:00', strtotime('-1 month', $starttime)));
            $before_endtime = strtotime(date('Y-m-01 00:00:00', strtotime('-1 month', $endtime)));
        } elseif ($request['type'] == 2) {
            $before_starttime = strtotime(date('Y-m-01 00:00:00', strtotime('-3 month', $starttime)));
            $before_endtime = strtotime(date('Y-m-t 23:59:59', strtotime('-3 month', $endtime)));
        } elseif ($request['type'] == 3) {
            $before_starttime = strtotime(date('Y-m-01 00:00:00', strtotime('-1 year', $starttime)));
            $before_endtime = strtotime(date('Y-m-t 23:59:59', strtotime('-1 year', $endtime)));
        }

        $beofore_startday = date("Y-m-d", $before_starttime);
        $beofore_endday = date("Y-m-d", $before_endtime);
        $beofore_endday = $beofore_endday > date("Y-m-d") ? date("Y-m-d") : $beofore_endday;
        $before_endtime = $before_endtime > time() ? time() : $before_endtime;
        $before_datawhere = " changelog_day <= '{$request['endtime']}' ";
        $before_studywhere = "  study_endday >= '{$beofore_endday}' ";
        $before_changewhere = "  changelog_day >= '{$beofore_startday}' and  changelog_day <= '{$beofore_endday}' ";

        //在籍学员
        $sql = "SELECT
            d.student_id,
                (
            SELECT
            s.stustatus_isenschool
            FROM
                smc_student_changelog AS g,
                smc_code_stuchange AS s 
            WHERE
                g.stuchange_code = s.stuchange_code 
                AND s.stuchange_type = 1
                AND g.student_id = d.student_id 
                and g.school_id = d.school_id
                AND {$datawhere} 
                order by  changelog_id DESC limit 0,1
                ) AS stustatus_isenschool 
            FROM
                smc_student_enrolled AS d 
            WHERE
                d.school_id = '{$this->school_id}'   
                HAVING  stustatus_isenschool = 1   ";
        $residenceStudentList = $this->DataControl->selectClear($sql);
        if ($residenceStudentList) {
            $residenceNum = count($residenceStudentList);
        } else {
            $residenceNum = 0;
        }
        if ($request['type'] <> 4) {
            $sql = "SELECT
            d.student_id,
                (
            SELECT
            s.stustatus_isenschool
            FROM
                smc_student_changelog AS g,
                smc_code_stuchange AS s 
            WHERE
                g.stuchange_code = s.stuchange_code 
                AND s.stuchange_type = 1
                AND g.student_id = d.student_id 
                and g.school_id = d.school_id
                AND {$before_datawhere} 
                order by  changelog_id DESC limit 0,1
                ) AS stustatus_isenschool 
            FROM
                smc_student_enrolled AS d 
            WHERE
                d.school_id = '{$this->school_id}'   
                HAVING  stustatus_isenschool = 1   ";
            $last_residenceStudentList = $this->DataControl->selectClear($sql);
            if ($last_residenceStudentList) {
                $last_residenceNum = count($last_residenceStudentList);
            }
        } else {
            $last_residenceNum = 0;
        }

        //在读学员
        $sql = "select study_id from smc_student_study where school_id='{$this->school_id}'  and {$studywhere} group by student_id";

//        $sql="SELECT B.student_id
//                FROM
//                    (
//                        SELECT
//                            A.student_id,A.study_beginday,
//                            IFNULL(
//                                (
//                                    SELECT
//                                        Y.stustatus_inclass
//                                    FROM
//                                        smc_student_changelog AS X
//                                    LEFT JOIN smc_code_stuchange AS Y ON X.stuchange_code = Y.stuchange_code
//                                    WHERE
//                                        X.class_id = A.class_id
//                                    AND X.student_id = A.student_id and X.changelog_day<=CURDATE()
//                                    ORDER BY
//                                        X.changelog_day DESC,
//                                        X.changelog_id DESC
//                                    LIMIT 0,
//                                    1
//                                ),
//                                1
//                            ) AS stustatus_inclass
//                        FROM
//                            smc_student_study AS A
//                        WHERE
//                            A.study_beginday <= CURDATE()
//                        AND A.study_endday >= CURDATE()
//                        AND A.school_id = '{$this->school_id}'
//                        HAVING
//                            stustatus_inclass = '1'
//                    ) AS B
//                GROUP BY
//                    B.student_id";

        $studyStudentList = $this->DataControl->selectClear($sql);

        if ($studyStudentList) {
            $studyNum = count($studyStudentList);
        } else {
            $studyNum = 0;
        }

        if ($request['type'] <> 4) {
            $sql = "select study_id from smc_student_study where school_id='{$this->school_id}' and {$before_studywhere} group by student_id ";
            $last_studyStudentList = $this->DataControl->selectClear($sql);

            if ($last_studyStudentList) {
                $last_studyNum = count($last_studyStudentList);

            }
        } else {
            $last_studyNum = 0;
        }

        //新增学员数
        $sql = "select student_id from smc_student_changelog where school_id='{$this->school_id}' and  stuchange_code in ('A01','F01') and {$changewhere} ";

        $addStudentList = $this->DataControl->selectClear($sql);
        if ($addStudentList) {
            $addStuNum = count($addStudentList);

        } else {
            $addStuNum = 0;
        }
        if ($request['type'] <> 4) {
            $sql = "select DISTINCT student_id from smc_student_changelog where school_id='{$this->school_id}' and   stuchange_code in ('A01','F01') and {$before_changewhere} ";

            $last_addStudentList = $this->DataControl->selectClear($sql);
            if ($last_addStudentList) {
                $last_addStuNum = count($last_addStudentList);

            }
        } else {
            $last_addStuNum = 0;
        }

        //流失学员数
        $sql = "select  DISTINCT student_id from smc_student_changelog where school_id='{$this->school_id}' and  stuchange_code in ('C02','B05')  and {$changewhere} ";


        $lossStudentList = $this->DataControl->selectClear($sql);
        if ($lossStudentList) {
            $lossStuNum = count($lossStudentList);
        } else {
            $lossStuNum = 0;
        }

        if ($request['type'] <> 4) {
            $sql = "select student_id from smc_student_changelog where school_id='{$this->school_id}' and  stuchange_code in ('C02','B05')  and {$before_changewhere} ";
            $last_lossStudentList = $this->DataControl->selectClear($sql);
            if ($last_lossStudentList) {
                $last_lossStuNum = count($last_lossStudentList);
            }
        } else {
            $last_lossStuNum = 0;
        }

        $data = array();
        $data['residenceNum'] = $residenceNum;
        $data['compar_residenceNum'] = $residenceNum - $last_residenceNum;
        $data['studyNum'] = $studyNum;
        $data['compar_studyNum'] = $studyNum - $last_studyNum;
        $data['addStuNum'] = $addStuNum;
        $data['compar_addStuNum'] = $addStuNum - $last_addStuNum;
        $data['lossStuNum'] = $lossStuNum;
        $data['compar_lossStuNum'] = $lossStuNum - $last_lossStuNum;
        return $data;
    }

    /**
     * 校务学员统计 - 最近10天的统计情况
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function StatisticsStudentTenDays($request)
    {

        $datawhere = '1';
        $studywhere = '1';
        $chagewhere = '1';
        if (isset($request['school_id']) && $request['school_id']) {
            $datawhere .= " and c.school_id ='{$request['school_id']}'";
            $studywhere .= " and c.school_id ='{$request['school_id']}'";
            $chagewhere .= " and c.school_id ='{$request['school_id']}' ";
        }

        $allList = array();
        //获取最近10天
        $today = date('Y-m-d');
        $time_end = strtotime(date('Ymd', time() + 24 * 60 * 60 - 1));
        $time_start = strtotime(date('Ymd', time() - 9 * 24 * 60 * 60));
//        $datawhere .= "  AND enrolled_leavetime <= '{$time_end}' ";
//        $studywhere .= "  AND study_endday >= '{$today}' ";
        $chagewhere .= "  AND c.changelog_day <='{$today}' ";
        while (date('Ymd', $time_start) < date('Ymd', $time_end)) {
            $date[] = date('Y-m-d', $time_start);

            $time_start = strtotime('+1 day', $time_start);
            $dateTime[] = $time_start + 24 * 60 * 60 - 1;
        }

        if (!$date) {
            $this->error = 1;
            $this->error = '计算出错';
            return array();
        }
        //10天的在籍的人数

//        SELECT d.student_id, ( SELECT s.stustatus_isenschool FROM smc_student_changelog AS g, smc_code_stuchange AS s WHERE g.stuchange_code = s.stuchange_code AND s.stuchange_type = 1 AND g.student_id = d.student_id and g.school_id = d.school_id AND { $ datawhere } order by changelog_id DESC limit 0, 1 ) AS stustatus_isenschool FROM smc_student_enrolled AS d WHERE d.school_id = '{$this->school_id}' HAVING stustatus_isenschool = 1


        // 优化后的SQL - 使用窗口函数替代复杂的子查询
        $sqlParts = array();
        for ($i = 0; $i < 10; $i++) {
            $sqlParts[] = "
                (SELECT COUNT(DISTINCT d.student_id) 
                 FROM smc_student_enrolled d
                 WHERE d.school_id = '{$this->school_id}'
                   AND EXISTS (
                       SELECT 1 
                       FROM smc_student_changelog g
                       INNER JOIN smc_code_stuchange s ON g.stuchange_code = s.stuchange_code
                       WHERE s.stuchange_type = 1 
                         AND g.student_id = d.student_id 
                         AND g.school_id = d.school_id
                         AND g.changelog_day <= '{$date[$i]}'
                         AND s.stustatus_isenschool = 1
                       ORDER BY g.changelog_id DESC
                       LIMIT 1
                   )
                ) as student_num$i";
        }
        $sql = "SELECT " . implode(',', $sqlParts) . " FROM smc_school s WHERE s.school_id = '{$this->school_id}' LIMIT 1";

        $studentList = $this->DataControl->selectOne($sql);
        if ($studentList) {
            $studentList = array_values($studentList);
            for ($i = 0; $i < count($studentList); $i++) {
                $arr_student_num[$i] = $studentList[$i];
            }
        } else {
            for ($i = 0; $i < 10; $i++) {
                $arr_student_num[$i] = 0;
            }
        }
        $allList[0]['name'] = $this->LgStringSwitch('在籍学员');
        $allList[0]['data'] = $arr_student_num;

        // 10天的在读学员
        $sql = "SELECT 
        study_endday AS current_day,COUNT(c.student_id ) AS student_num0 
        FROM smc_student_study as c 
        WHERE {$studywhere}
        GROUP BY current_day
        ORDER BY current_day ASC  LIMIT 0,10 
    ";

        // 优化后的SQL - 在读学员统计
        $studySqlParts = array();
        for ($i = 0; $i < 10; $i++) {
            $studySqlParts[] = "
                (SELECT COUNT(DISTINCT ss.student_id) 
                 FROM smc_student_study ss 
                 WHERE ss.school_id = '{$this->school_id}' 
                   AND ss.study_endday >= '{$date[$i]}' 
                   AND ss.study_beginday <= '{$date[$i]}'
                ) as student_num$i";
        }
        $sql = "SELECT " . implode(',', $studySqlParts) . " FROM smc_school s WHERE s.school_id = '{$this->school_id}' LIMIT 1";

        $study_studentList = $this->DataControl->selectOne($sql);
        if ($study_studentList) {
            $study_studentList = array_values($study_studentList);
            for ($i = 0; $i < count($study_studentList); $i++) {
                $arr_studystudent_num[$i] = $study_studentList[$i];
            }
        } else {
            for ($i = 0; $i < 10; $i++) {
                $arr_studystudent_num[$i] = 0;
            }
        }
        $allList[1]['name'] = $this->LgStringSwitch('在读学员');
        $allList[1]['data'] = $arr_studystudent_num;

        // 10天的新增学员数
        $sql = "SELECT 
        changelog_day AS current_day,COUNT(c.student_id ) AS student_num 
        FROM smc_student_changelog as c 
        WHERE {$chagewhere} and  stuchange_code ='A01'
        GROUP BY current_day
        ORDER BY current_day DESC  LIMIT 0,10  
    ";


        $chage_studentList = $this->DataControl->selectClear($sql);
        if ($chage_studentList) {
            $arr_chagestudent_day = array_column($chage_studentList, 'student_num', 'current_day');
        } else {
            $arr_chagestudent_day = array();
        }
        $arr_changestudent_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_chagestudent_day[$dateOne]) {
                $arr_changestudent_num[$key] = $arr_chagestudent_day[$dateOne];
            } else {
                $arr_changestudent_num[$key] = '0';
            }
        }
        $allList[2]['name'] = $this->LgStringSwitch('新增学员');
        $allList[2]['data'] = $arr_changestudent_num;

        //10天流失的人数
        $sql = "SELECT 
        changelog_day AS current_day,COUNT(c.student_id ) AS student_num 
        FROM smc_student_changelog as c 
        WHERE {$chagewhere} and  stuchange_code in ('CO2','B05','C03')
        GROUP BY current_day
        ORDER BY current_day DESC  LIMIT 0,10 
    ";
        $loss_studentList = $this->DataControl->selectClear($sql);
        if ($loss_studentList) {
            $arr_losssstudent_day = array_column($loss_studentList, 'student_num', 'current_day');
        } else {
            $arr_losssstudent_day = array();
        }
        $arr_lossstudent_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_losssstudent_day[$dateOne]) {
                $arr_lossstudent_num[$key] = $arr_losssstudent_day[$dateOne];
            } else {
                $arr_lossstudent_num[$key] = '0';
            }
        }
        $allList[3]['name'] = $this->LgStringSwitch('新增流失');
        $allList[3]['data'] = $arr_lossstudent_num;

        $data = array();
        $data['allList'] = $allList;
        $data['legendData'] = $this->LgArraySwitch(array('在籍学员', '在读学员', '新增学员', '新增流失'));
        $data['xAxisData'] = $date;
        return $data;
    }

    /**
     * 校务学员统计 - 学员年龄分布
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function StatisticsStudentAges($request)
    {

        $datawhere = "1 and school_id='{$request['school_id']}'";

        $all_student = $this->DataControl->selectOne("select count(student_id) as student_all_num from smc_student_enrolled  where school_id = '{$request['school_id']}' ");
        $age_sql = "SELECT  q.school_id, q.student_newage,sum( CASE WHEN q.new_client_sex = '女' THEN 1 ELSE 0 END ) AS female_num,
	        sum( CASE WHEN q.new_client_sex = '男' THEN 1 ELSE 0 END ) AS male_num 
        FROM (
            SELECT
                se.school_id,
                s.student_id,
                ( CASE WHEN s.student_sex = '女' THEN '女' ELSE '男' END ) AS new_client_sex,
                ( CASE WHEN IFNULL( TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ), 0 ) < 0 THEN 0 ELSE IFNULL( TIMESTAMPDIFF( YEAR, s.student_birthday, CURDATE( ) ), 0 ) END ) AS student_newage
            FROM smc_student AS s
                LEFT JOIN smc_student_enrolled AS se ON se.student_id = s.student_id 
            WHERE 1 AND enrolled_status <> 2 AND enrolled_status <> '-1' 
                ) AS q 
        WHERE
            1 and {$datawhere}
        GROUP BY
            q.school_id,
            q.student_newage 
        ORDER BY
            q.student_newage ASC
                                ";
        $dataList = $this->DataControl->selectClear($age_sql);

        if (!$dataList) {
            $newage_name = array();
            $rate = array();
        } else {
            foreach ($dataList as $key => &$dataOne) {
                $dataOne['male_rate'] = $all_student['student_all_num'] > 0 ? round($dataOne['male_num'] / $all_student['student_all_num'], 4) * 100 : '0';
                $dataOne['female_rate'] = $all_student['student_all_num'] > 0 ? round($dataOne['female_num'] / $all_student['student_all_num'], 4) * 100 : '0';

            }
            $newage_name = array_column($dataList, "student_newage");
            $male_rate = array_column($dataList, "male_rate");
            $female_rate = array_column($dataList, "female_rate");
            $rate[0]['name'] = $this->LgStringSwitch('男');
            $rate[0]['data'] = $male_rate;
            $rate[1]['name'] = $this->LgStringSwitch('女');
            $rate[1]['data'] = $female_rate;
        }

        $data = array();
        $data['x_data'] = $newage_name;
        $data['y_data'] = $rate;
        $data['legendData'] = $this->LgArraySwitch(array('男', '女'));
        return $data;

    }

    /**
     * 校务学员统计 - 学员性别分布
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function StatisticsStudentSex($request)
    {

        $datawhere = "1 and se.school_id='{$request['school_id']}'";

        $all_student = $this->DataControl->selectOne("select count(student_id) as student_all_num from smc_student_enrolled  where school_id = '{$request['school_id']}' ");

        $age_sql = "SELECT se.school_id, (case when s.student_sex ='女' then  '女' else '男' end) as  new_client_sex,
                count(1) as student_sex_num
            FROM
                smc_student AS s
                LEFT JOIN smc_student_enrolled AS se ON se.student_id = s.student_id 
            WHERE
                {$datawhere} and enrolled_status <> 2 and enrolled_status <> '-1'
            GROUP BY
                 se.school_id,new_client_sex 
                        ";
        $dataList = $this->DataControl->selectClear($age_sql);

        if (!$dataList) {
            $newage_name = array();
            $rate = array();
        } else {
            foreach ($dataList as &$dataOne) {
                $dataOne['rate'] = $all_student['student_all_num'] > 0 ? round($dataOne['student_sex_num'] / $all_student['student_all_num'], 4) * 100 : '0';
            }

            $newage_name = array_column($dataList, "new_client_sex");
            $male_rate = array_column($dataList, "rate");
            $rate[0]['name'] = $this->LgStringSwitch('性别');
            $rate[0]['data'] = $male_rate;

        }

        $data = array();
        $data['x_data'] = $newage_name;
        $data['y_data'] = $rate;

        return $data;

    }


    //统计中心-考勤概况
    function CheckingApi($paramArray)
    {
        $datawhere = "c.company_id='{$paramArray['company_id']}'";

        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$paramArray['school_id']}'";
        }
        $data = array();
//        $sql = "SELECT COUNT(sh.student_id) as num
//                FROM smc_class as c
//                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
//                LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id
//                LEFT JOIN smc_student_clockinginlog as cl ON cl.hourstudy_id = sh.hourstudy_id AND cl.student_id = sh.student_id
//                WHERE {$datawhere}";
//
//        $nowday_attendance_all_num = $this->DataControl->selectOne($sql . " AND cl.clockinginlog_day = CURDATE()");
//        $nowday_attendance_num = $this->DataControl->selectOne($sql . " AND sh.hourstudy_checkin = '1' AND cl.clockinginlog_day = CURDATE()");
//        $data['total']['nowday_attendance_all_num'] = $nowday_attendance_all_num['num'];//当天应出勤人次
//        $data['total']['nowday_attendance_num'] = $nowday_attendance_num['num'];//当天出勤人次
//        if ($nowday_attendance_num['num']) {
//            $data['total']['nowday_attendance_rate'] = sprintf("%.4f", ($nowday_attendance_num['num'] / $nowday_attendance_all_num['num'])) * 100;//当天出勤率
//        } else {
//            $data['total']['nowday_attendance_rate'] = 0;
//        }
//
//        $week = GetWeekAll(date("Y-m-d"));
//        $lastweek_attendance_all_num = $this->DataControl->selectOne($sql . " AND cl.clockinginlog_day >= '{$week['lastweek_start']}' AND cl.clockinginlog_day <= '{$week['lastweek_end']}'");
//        $lastweek_attendance_num = $this->DataControl->selectOne($sql . " AND sh.hourstudy_checkin = '1' AND cl.clockinginlog_day >= '{$week['lastweek_start']}' AND cl.clockinginlog_day <= '{$week['lastweek_end']}'");
//        $data['total']['lastweek_attendance_all_num'] = $lastweek_attendance_all_num['num'];//上周应出勤人次
//        $data['total']['lastweek_attendance_num'] = $lastweek_attendance_num['num'];//上周出勤人次
//        if ($lastweek_attendance_num['num']) {
//            $data['total']['lastweek_attendance_rate'] = sprintf("%.4f", ($lastweek_attendance_num['num'] / $lastweek_attendance_all_num['num'])) * 100;//上周出勤率
//        } else {
//            $data['total']['lastweek_attendance_rate'] = 0;
//        }
//
//        $month = GetTheMonth(date("Y-m"));
//        $month_attendance_all_num = $this->DataControl->selectOne($sql . " AND cl.clockinginlog_day >= '{$month[0]}' AND cl.clockinginlog_day <= '{$month[1]}'");
//        $month_attendance_num = $this->DataControl->selectOne($sql . " AND sh.hourstudy_checkin = '1' AND cl.clockinginlog_day >= '{$month[0]}' AND cl.clockinginlog_day <= '{$month[1]}'");
//        $data['total']['month_attendance_all_num'] = $month_attendance_all_num['num'];//本月应出勤人次
//        $data['total']['month_attendance_num'] = $month_attendance_num['num'];//本月出勤人次
//        if ($month_attendance_num['num']) {
//            $data['total']['month_attendance_rate'] = sprintf("%.4f", ($month_attendance_num['num'] / $month_attendance_all_num['num'])) * 100;//本月出勤率
//        } else {
//            $data['total']['month_attendance_rate'] = 0;
//        }

        $today = date("Y-m-d");
        $sql = "SELECT COUNT(ss.student_id) as num
                FROM smc_class_hour as ch
                LEFT JOIN smc_student_study as ss on ss.class_id=ch.class_id
                WHERE ss.school_id='{$this->school_id}'";

        $sqls = "SELECT COUNT(sh.student_id) as num
                 FROM smc_student_hourstudy as sh
                 LEFT JOIN smc_class_hour as ch on ch.hour_id=sh.hour_id
                 LEFT JOIN smc_class as c on c.class_id=ch.class_id
                 WHERE c.school_id='{$this->school_id}' and sh.hourstudy_checkin='1' and ch.hour_iswarming='0'";

        $nowday_attendance_all_num = $this->DataControl->selectOne($sql . " AND ch.hour_day='{$today}' AND ss.study_endday>='{$today}' AND ss.study_beginday<='{$today}'");
        $nowday_attendance_num = $this->DataControl->selectOne($sqls . " AND ch.hour_day='{$today}'");
        $data['total']['nowday_attendance_all_num'] = $nowday_attendance_all_num['num'];//当天应出勤人次
        $data['total']['nowday_attendance_num'] = $nowday_attendance_num['num'];//当天出勤人次
        if ($nowday_attendance_num['num']) {
            $data['total']['nowday_attendance_rate'] = sprintf("%.4f", ($nowday_attendance_num['num'] / $nowday_attendance_all_num['num'])) * 100;//当天出勤率
        } else {
            $data['total']['nowday_attendance_rate'] = 0;
        }

        $week = GetWeekAll(date("Y-m-d"));
        $lastweek_attendance_all_num = $this->DataControl->selectOne($sql . " AND ch.hour_day>='{$week['lastweek_start']}' AND ch.hour_day<='{$week['lastweek_end']}' AND ss.study_endday>='{$week['lastweek_start']}' AND ss.study_beginday<='{$week['lastweek_end']}'");
        $lastweek_attendance_num = $this->DataControl->selectOne($sqls . " AND ch.hour_day>='{$week['lastweek_start']}' AND ch.hour_day<='{$week['lastweek_end']}'");
        $data['total']['lastweek_attendance_all_num'] = $lastweek_attendance_all_num['num'];//上周应出勤人次
        $data['total']['lastweek_attendance_num'] = $lastweek_attendance_num['num'];//上周出勤人次
        if ($lastweek_attendance_num['num']) {
            $data['total']['lastweek_attendance_rate'] = sprintf("%.4f", ($lastweek_attendance_num['num'] / $lastweek_attendance_all_num['num'])) * 100;//上周出勤率
        } else {
            $data['total']['lastweek_attendance_rate'] = 0;
        }

        $month = GetTheMonth(date("Y-m"));
        $month_attendance_all_num = $this->DataControl->selectOne($sql . " AND ch.hour_day>='{$month[0]}' AND ch.hour_day<='{$month[1]}' AND ss.study_endday>='{$month[0]}' AND ss.study_beginday<='{$month[1]}'");
        $month_attendance_num = $this->DataControl->selectOne($sqls . " AND ch.hour_day>='{$month[0]}' AND ch.hour_day<='{$month[1]}'");
        $data['total']['month_attendance_all_num'] = $month_attendance_all_num['num'];//本月应出勤人次
        $data['total']['month_attendance_num'] = $month_attendance_num['num'];//本月出勤人次
        if ($month_attendance_num['num']) {
            $data['total']['month_attendance_rate'] = sprintf("%.4f", ($month_attendance_num['num'] / $month_attendance_all_num['num'])) * 100;//本月出勤率
        } else {
            $data['total']['month_attendance_rate'] = 0;
        }

        //开始时间
        if (isset($paramArray['start_time']) && $paramArray['start_time'] != '') {
            $datawhere .= " AND cl.clockinginlog_day >= '{$paramArray['start_time']}'";
        }
        //结束时间
        if (isset($paramArray['end_time']) && $paramArray['end_time'] != '') {
            $datawhere .= " AND cl.clockinginlog_day <= '{$paramArray['end_time']}'";
        }

        $orderby = "";
        if (isset($paramArray['orderby']) && $paramArray['orderby'] == '1') {
            $orderby .= " ORDER BY attendance_num ASC";
        } elseif (isset($paramArray['orderby']) && $paramArray['orderby'] == '2') {
            $orderby .= " ORDER BY attendance_num DESC";
        } elseif (isset($paramArray['orderby']) && $paramArray['orderby'] == '3') {
            $orderby .= " ORDER BY attendance_not_num ASC";
        } elseif (isset($paramArray['orderby']) && $paramArray['orderby'] == '4') {
            $orderby .= " ORDER BY attendance_not_num DESC";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.class_id,c.class_cnname,c.class_enname,c.class_branch,
                (SELECT COUNT(sh.student_id) FROM smc_class_hour as ch LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = ch.hour_id WHERE ch.hour_id = h.hour_id) as attendance_all_num,
                (SELECT COUNT(sh.student_id) FROM smc_class_hour as ch LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = ch.hour_id WHERE ch.hour_id = h.hour_id AND ch.hour_ischecking = '1' AND sh.hourstudy_checkin = '1') as attendance_num,
                (SELECT COUNT(sh.student_id) FROM smc_class_hour as ch LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = ch.hour_id WHERE ch.hour_id = h.hour_id AND ch.hour_ischecking = '1' AND sh.hourstudy_checkin = '0') as attendance_not_num
                FROM smc_class as c
                LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id
                LEFT JOIN smc_student_clockinginlog as cl ON cl.hourstudy_id = sh.hourstudy_id AND cl.student_id = sh.student_id
                WHERE {$datawhere} GROUP BY c.class_id {$orderby}";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$paramArray['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];//班级中文名
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];//班级别名
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];//班级编号
                    $datearray['attendance_all_num'] = $dateexcelvar['attendance_all_num'];//应出勤人次
                    $datearray['attendance_num'] = $dateexcelvar['attendance_num'];//出勤人次
                    $datearray['attendance_not_num'] = $dateexcelvar['attendance_not_num'];//缺勤人数
                    if ($dateexcelvar['attendance_num']) {
                        $datearray['attendance_rate'] = sprintf("%.4f", ($dateexcelvar['attendance_num'] / $dateexcelvar['attendance_all_num'])) * 100 . '%';//出勤率
                    } else {
                        $datearray['attendance_rate'] = '0%';
                    }
                    if ($dateexcelvar['attendance_not_num']) {
                        $datearray['attendance_not_rate'] = sprintf("%.4f", ($dateexcelvar['attendance_not_num'] / $dateexcelvar['attendance_all_num'])) * 100 . '%';//缺勤率
                    } else {
                        $datearray['attendance_not_rate'] = '0%';
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('班级中文名', '班级别名', '班级编号', "应出勤人次", '出勤人次', "缺勤人数", "出勤率", "缺勤率"));
            $excelfileds = array('class_cnname', 'class_enname', 'class_branch', 'attendance_all_num', 'attendance_num', 'attendance_not_num', 'attendance_rate', 'attendance_not_rate');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员考勤统计表.xlsx"));
            exit;

        } else {
            $dataList = $this->DataControl->selectClear($sql);
            if (!$dataList) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return $data;
            }

            foreach ($dataList as &$v) {
                if ($v['attendance_num']) {
                    $v['attendance_rate'] = sprintf("%.4f", ($v['attendance_num'] / $v['attendance_all_num'])) * 100 . '%';
                } else {
                    $v['attendance_rate'] = '0%';
                }
                if ($v['attendance_not_num']) {
                    $v['attendance_not_rate'] = sprintf("%.4f", ($v['attendance_not_num'] / $v['attendance_all_num'])) * 100 . '%';
                } else {
                    $v['attendance_not_rate'] = '0%';
                }
            }
            if (isset($paramArray['orderby']) && $paramArray['orderby'] == '5') {
                $sort_arr = [];
                foreach ($dataList as $val) {
                    $sort_arr[] = trim($val['attendance_rate'], '%');
                }
                array_multisort($sort_arr, SORT_ASC, $dataList);
            } elseif (isset($paramArray['orderby']) && $paramArray['orderby'] == '6') {
                $sort_arr = [];
                foreach ($dataList as $val) {
                    $sort_arr[] = trim($val['attendance_rate'], '%');
                }
                array_multisort($sort_arr, SORT_DESC, $dataList);
            }
            if (isset($paramArray['orderby']) && $paramArray['orderby'] == '7') {
                $sort_arr = [];
                foreach ($dataList as $val) {
                    $sort_arr[] = trim($val['attendance_not_rate'], '%');
                }
                array_multisort($sort_arr, SORT_ASC, $dataList);
            } elseif (isset($paramArray['orderby']) && $paramArray['orderby'] == '8') {
                $sort_arr = [];
                foreach ($dataList as $val) {
                    $sort_arr[] = trim($val['attendance_not_rate'], '%');
                }
                array_multisort($sort_arr, SORT_DESC, $dataList);
            }

            $dataList = array_slice($dataList, $pagestart, $num);
            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.class_id) as num FROM
                                                            (SELECT c.class_id
                                                            FROM smc_class as c
                                                            LEFT JOIN smc_class_hour as h ON h.class_id = c.class_id
                                                            LEFT JOIN smc_student_hourstudy as sh ON sh.hour_id = h.hour_id
                                                            LEFT JOIN smc_student_clockinginlog as cl ON cl.hourstudy_id = sh.hourstudy_id AND cl.student_id = sh.student_id
                                                            WHERE {$datawhere} GROUP BY c.class_id) as q");

                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $dataList;
            return $data;
        }
    }
}
