<?php


namespace Model\Smc;

class TimetableModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    function get_arr($arr)
    {
        foreach ($arr as $k => $v) {
            if (is_array($arr[$k])) {
                $arr[$k] = $this->get_arr($arr[$k]);
            } else {
                if ($k == -1) {
                    unset($arr[$k]);
                }
            }
        }
        return $arr;
    }


    /**
     * @param $request
     * @return $array
     * 获取 单个教师的排课信息
     */
    function getTeacherOneTimeTable($request)
    {

        if (isset($request['startday']) and $request['startday'] !== "") {
            $startday = $request['startday'];
        } else {
            $startday = date('Y-m-d');
        }
        $startday = date('Y-m-d', strtotime($startday));
        if (isset($request['endday']) and $request['endday'] !== "") {
            $endday = $request['endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        $sql = " select  s.staffer_id,s.staffer_cnname,c.class_id,c.class_cnname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,cl.classroom_id,cl.classroom_cnname,ch.hour_way,ch.hour_number
		 from  smc_staffer as s
		 LEFT JOIN  smc_class_hour_teaching as t  On s.staffer_id = t.staffer_id
		 LEFT JOIN smc_class_hour AS ch ON  ch.hour_id = t.hour_id
		 LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		 left join smc_course as co on co.course_id=c.course_id
         left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
		 where  ch.hour_day >= '{$startday}'  and   ch.hour_day <= '{$endday}' and t.staffer_id='{$request['te_staffer_id']}' and c.school_id='{$request['school_id']}' and c.class_status <> '-2'
		 order by ch.hour_day ASC, ch.hour_starttime ASC,ch.hour_endtime ASC
		";

        $weekList = $this->DataControl->selectClear($sql);

        $data = array();
        if ($weekList) {
            foreach ($weekList as $key => &$value) {
                if ($value['hour_way'] == 1) {
                    $value['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $value['classroom_cnname'] = $value['classroom_branch'] = $value['hour_number'];
                    $value['classroom_iscloud'] = "1";
                }
                $value['hour_way_name'] = $this->LgStringSwitch($value['hour_way'] == 0 ? "实体课" : "线上课");
                foreach ($date as $k => $v) {
                    if ($value['hour_day'] == $v) {
                        $week = date('w', strtotime($v));
                        $data[$k]['time'] = $this->LgStringSwitch($v . " 周" . $weekarray[$week]);
                        $data[$k]['course'][] = $value;
                    } else {
                        $week = date('w', strtotime($v));
                        $data[$k]['time'] = $this->LgStringSwitch($v . " 周" . $weekarray[$week]);
                        $data[$k]['course']['-1'] = "";
                    }

                }

            }
        }
        $data = $this->get_arr($data);


        if (!$data) {
            $data = array();
        }

        return $data;
    }

    /**
     * @param $request
     * @return $array
     * 获取 单个教室的排课信息
     */
    function getClassOneTimeTable($request)
    {

        if (isset($request['startday']) and $request['startday'] !== "") {
            $startday = $request['startday'];
        } else {
            $startday = date('Y-m-d');
        }

        $startday = date('Y-m-d', strtotime($startday));

        if (isset($request['endday']) and $request['endday'] !== "") {
            $endday = $request['endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }


        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,cl.classroom_id,cl.classroom_cnname,ch.hour_way,ch.hour_number
            from  smc_classroom AS cl
			LEFT JOIN smc_class_hour AS ch ON cl.classroom_id = ch.classroom_id
			LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
			LEFT JOIN smc_course AS co ON co.course_id = c.course_id
			LEFT JOIN smc_class_hour_teaching AS t ON t.class_id = c.class_id and t.teaching_type =0
			AND t.hour_id = ch.hour_id
			LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
            where  ch.hour_day >= '{$startday}'  and ch.hour_day <= '{$endday}' and cl.classroom_id='{$request['classroom_id']}' and c.class_status <> '-2'  and ch.hour_ischecking <> '-1'
             order by ch.hour_day ASC,ch.hour_starttime ASC
            ";
        $weekList = $this->DataControl->selectClear($sql);
        $data = array();
        if ($weekList) {
            foreach ($weekList as $key => &$value) {
                if ($value['hour_way'] == 1) {
                    $value['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $value['classroom_cnname'] = $value['classroom_branch'] = $value['hour_number'];
                    $value['classroom_iscloud'] = "1";
                }
                $value['hour_way_name'] = $this->LgStringSwitch($value['hour_way'] == 0 ? "实体课" : "线上课");
                foreach ($date as $k => $v) {
                    if ($value['hour_day'] == $v) {
                        $week = date('w', strtotime($v));
                        $data[$k]['time'] = $this->LgStringSwitch($v . " 周" . $weekarray[$week]);
                        $data[$k]['course'][] = $value;
                    } else {
                        $week = date('w', strtotime($v));
                        $data[$k]['time'] = $this->LgStringSwitch($v . " 周" . $weekarray[$week]);
                        $data[$k]['course']['-1'] = "";
                    }

                }

            }
        }

        $data = $this->get_arr($data);

        if (!$data) {
            $data = array();
        }

        return $data;

    }

    //------------------------------------------------------------------------

    /**
     * @param $request
     * @return $array
     * 获取 班级一周课表
     */
    function getClassWeekTimetable($request)
    {

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and  (c.class_cnname like '%{$request['keyword']}%'  or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' ) ";

        }
        if (isset($request['coursetype_id']) and $request['coursetype_id'] != "") {
            $where .= "  and co.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] != "") {
            $where .= "  and co.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) and $request['course_id'] != "") {
            $where .= "  and co.course_id ='{$request['course_id']}'";
        }

        $datawhere = '1';
        if (isset($request['hour_way']) and $request['hour_way'] !== "") {
            $datawhere .= "  and  ch.hour_way ='{$request['hour_way']}'";

        }

//		获取一周所有课程安排的班级

        $arr_class = $this->DataControl->selectClear("
				select c.class_id ,class_cnname
				from  smc_class_hour as h
 				left JOIN smc_class as c ON  c.class_id = h.class_id
                left join smc_course as co on co.course_id=c.course_id
 				left JOIN smc_classroom as cl ON cl.classroom_id  = h.classroom_id
 				left JOIN smc_class_hour_teaching as t ON t.class_id = h.class_id and t.hour_id=h.hour_id and  t.teaching_type =0
                left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
				where c.school_id='{$this->school_id}' and h.hour_ischecking <> '-1'
				 and h.hour_day >='{$startday}'  and h.hour_day <= '{$endday}' and {$where} and c.class_status <> '-2'
				group by c.class_id
				ORDER BY c.class_createtime DESC,h.hour_endtime ASC
				");

        if ($arr_class) {
            $arr_class_id = array_column($arr_class, 'class_id');
            $str_class_id = trim(implode(',', $arr_class_id), ',');
            $arr_classcnname = array_column($arr_class, 'class_cnname', 'class_id');

        } else {
            return array();
        }

        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname, s.staffer_enname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype
              from  smc_class as c
              left join smc_class_hour as ch on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left JOIN smc_class_hour_teaching as t ON t.class_id = c.class_id and t.hour_id=ch.hour_id and  t.teaching_type =0
              left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
              where ch.class_id in ({$str_class_id}) 
              and ch.hour_ischecking <> '-1'
              AND ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where} and c.school_id='{$this->school_id}' and c.class_status <> '-2' and {$datawhere}
              ORDER BY c.class_createtime DESC,ch.hour_endtime ASC
    		";

        $weekList = $this->DataControl->selectClear($sql);
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $weekList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据数据";
                return false;
            }
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['hour_weekday'] = '周' . $weekarray[date('w', strtotime($dateexcelvar['hour_day']))] . '(' . $dateexcelvar['hour_day'] . ')';
                    $datearray['time'] = $dateexcelvar['hour_starttime'] . '-' . $dateexcelvar['hour_endtime'];
                    $datearray['hour_way_name'] = $dateexcelvar['hour_way'] == 1 ? '线上课' : '实体课';
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'] . '-' . $dateexcelvar['staffer_enname'];
                    $datearray['hour_ischecking'] = $dateexcelvar['hour_ischecking'] == 1 ? '已考勤' : '未考勤';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("班级名称", '上课周次', "上课时间", "上课方式", "教室", "教师", "考勤状态");
            $excelfileds = array('class_cnname', 'hour_weekday', 'time', 'hour_way_name', 'classroom_cnname', 'staffer_cnname', 'hour_ischecking');
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$this->school_id}'");
            $tem_name = $schoolOne['school_cnname'] . $startday . '~' . $endday . '班级课表.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        if ($weekList) {
            foreach ($weekList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $val['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");

                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }
            }
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {


            //只检测未上课的课时冲突
//			if (isset($request['conflict']) && $request['conflict'] == 1) {
//				foreach($weekList as $key => $value){
//					if($value['hour_ischecking'] == 0){
//						$dataWeekList1[$key] =  $value;
//
//				}else{
//						$dataWeekList2[$key.'a'] =  $value;
//					 }
//				}
//				$dataWeekList1 = $this->checkClassroomConflict($dataWeekList1);
//				$dataWeekList1 = $this->checkStafferConflict($dataWeekList1);
//
//				$weekList =array_values(array_merge($dataWeekList1, $dataWeekList2));
//			}


            //检测全局的冲突
            if (isset($request['conflict']) && $request['conflict'] == 1) {

                $weekList = $this->checkClassroomConflict($weekList);

                $weekList = $this->checkStafferConflict($weekList);
            }


            foreach ($weekList as $key => &$val) {

                foreach ($arr_class_id as $k => $v) {
                    $data[$v]['class_cnname']['class_cnname'] = $arr_classcnname[$v];

                    if ($val['class_id'] == $v) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];

                            if ($val['hour_day'] == $datevalue) {

                                $data[$v][$enweekarray[$week]][] = $val;

                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    }
                }
            }
        }
        $data = $this->get_arr($data);
        //以下代码别删--- 第二套数据处理逻辑
//	$enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
//	$re_weekList = array();
//	if($weekList){
//		$tempArray  =  array();
//		foreach($weekList as $key => $value ){
//			$tempArray[$value['class_id']]['class_cnname']['class_cnname'] = $value['class_cnname'];
//			$tempArray[$value['class_id']][$enweekarray['1']] = array();
//			$tempArray[$value['class_id']][$enweekarray['2']] = array();
//			$tempArray[$value['class_id']][$enweekarray['3']] = array();
//			$tempArray[$value['class_id']][$enweekarray['4']] = array();
//			$tempArray[$value['class_id']][$enweekarray['5']] = array();
//			$tempArray[$value['class_id']][$enweekarray['6']] = array();
//		    $tempArray[$value['class_id']][$enweekarray['0']] = array();
//			$tempArray[$value['class_id']][$enweekarray[date('w',strtotime($value['hour_day']))]][] = $value;
//		}
//		$data = $tempArray;
//	}

        return $data;
    }

    function gettimeKey($ranks, $rank_point)
    {
//        $ranks = array(1900=>'32',1800=>'31',1700=>'30',1600=>'29',1500=>'28',1400=>'27',1300=>'26',1400=>'25',1100=>'24',1000=>'23',900 =>'22',800 =>'21',700 =>'20',600 =>'19',500 =>'18',400 =>'17',300 =>'16',200 =>'15',100 =>'14',0=>'13');
//        $rank_point = -1;

        $value = '';
        $k = 0;
        foreach ($ranks as $key => $val) {
            $k++;
            $value = $val;
            if ($rank_point >= $key) {
                break;
            } else {
                if ($k == count($ranks)) {
                    return -1;
                }
            }
        }
        return $value;
    }

    function customsort($arr, $orderby = 'desc')
    {
        $new_array = array();
        $new_sort = array();
        foreach ($arr as $key => $value) {
            $new_array[] = $value;
        }
        if ($orderby == 'asc') {
            asort($new_array);
        } else {
            arsort($new_array);
        }
        foreach ($new_array as $k => $v) {
            foreach ($arr as $key => $value) {
                if ($v == $value) {
                    $new_sort[$key] = $value;
                    unset($arr[$key]);
                    break;
                }
            }
        }
        return $new_sort;
    }

    /**
     * @param $request
     * @return $array
     * 获取 教师一周的课表
     */
    function getTeacherWeekTimetable($request)
    {

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $enweekarray = array("0", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");

            $datawhere = '1';
            if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
                $startday = $request['hour_startday'];
            } else {
                $startday = date('Y-m-d', $startTime);
            }
            $datawhere .= " and ch.hour_day >='{$startday}'";

            if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
                $endday = $request['hour_endday'];
            } else {
                $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
            }
            $datawhere .= " and ch.hour_day <= '{$endday}'";

            if (isset($request['keyword']) and $request['keyword'] !== "") {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
            }
            if (isset($request['classroom_id']) and $request['classroom_id'] !== "") {
                $datawhere .= " and ch.classroom_id = '{$request['classroom_id']}' ";
            }

            if (isset($request['hour_way']) and $request['hour_way'] !== "") {
                $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
            }

            if (isset($request['course_id']) and $request['course_id'] !== "") {
                $datawhere .= " and co.course_id ='{$request['course_id']}'";
            }

            if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
                $datawhere .= " and co.coursecat_id ='{$request['coursecat_id']}'";
            }

            if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
                $datawhere .= " and co.coursetype_id ='{$request['coursetype_id']}'";
            }

            $sql = "select s.staffer_id,s.staffer_cnname,s.staffer_enname,c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,t.teaching_type,cp.post_name,sp.postbe_ismianjob,co.course_openclasstype
                  ,ifnull((select po.post_id from gmc_staffer_postbe as po where po.staffer_id=s.staffer_id and po.school_id<>c.school_id limit 0,1),0) as is_cross
                  from smc_class_hour as ch
                  left join smc_class as c on c.class_id=ch.class_id
                  left join smc_course as co on co.course_id=c.course_id
                  left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                  left join smc_class_hour_teaching as t on t.hour_id=ch.hour_id
                  left join smc_staffer as s on s.staffer_id=t.staffer_id
                  left join gmc_staffer_postbe as sp on sp.school_id=c.school_id and sp.staffer_id=s.staffer_id
                  left join gmc_company_post as cp on cp.post_id=sp.post_id
                  where {$datawhere} and c.school_id='{$this->school_id}' and ch.hour_ischecking <> '-1' and c.class_status <> '-2' and s.staffer_leave = 0
                  order by ch.hour_day ASC ,ch.hour_starttime ASC
                  ";

            $hourList = $this->DataControl->selectClear($sql);

            if (!$hourList) {
                $this->error = true;
                $this->errortip = "无考勤列表数据";
                return false;
            }
            $type = $this->LgArraySwitch(array("0" => "主教", "1" => "助教"));
            $way = $this->LgArraySwitch(array("0" => "实体课", "1" => "线上课"));

            foreach ($hourList as &$val) {
                if (isset($request['staffer_pattern']) and $request['staffer_pattern'] !== "") {
                    if ($request['staffer_pattern'] == 0) {
                        $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                    } elseif ($request['staffer_pattern'] == 2) {
                        $val['staffer_cnname'] = $val['staffer_enname'];
                    }
                } else {
                    $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                }

                if (isset($request['class_pattern']) and $request['class_pattern'] == 1) {
                    $val['class_cnname'] = $val['class_enname'];
                }

                $val['teaching_type_name'] = $type[$val['teaching_type']];
                $val['hour_way_name'] = $way[$val['hour_way']];
            }

            if (!isset($request['time_list']) || $request['time_list'] == '') {
                $this->error = true;
                $this->errortip = "时间规划必须传";
                return false;
            }

            $timeArray = json_decode(stripslashes($request['time_list']), 1);

            if (!$timeArray) {
                $this->error = true;
                $this->errortip = "时间规划必须传";
                return false;
            }

//            $timeArray=array(['name'=>'上午','time'=>'09:00'],['name'=>'下午','time'=>'12:00'],['name'=>'晚上','time'=>'18:00']);
            foreach ($timeArray as &$one) {
                $one['time'] = str_replace(".", ":", $one['time']) * 100;
            }

            $noon = array_column($timeArray, 'time');
            $noon_name = array_column($timeArray, 'name');
            $tem_noon = $this->customsort(array_flip($noon));
            $data = array();
            if ($hourList) {
                foreach ($hourList as $hourOne) {
                    $week = date('w', strtotime($hourOne['hour_day']));
                    if ($week == 0) {
                        $week = 7;
                    }
                    $hour_starttime = str_replace(".", ":", $hourOne['hour_starttime']) * 100;

                    $num = (int)$this->gettimeKey($tem_noon, $hour_starttime);

                    if ($num < 0) {
                        continue;
                    }

                    $value = $noon[$num];
                    $data[$hourOne['staffer_id']]['name'] = $hourOne['staffer_cnname'];
                    $data[$hourOne['staffer_id']]['en_name'] = $hourOne['staffer_enname'];
                    $data[$hourOne['staffer_id']]['post_name'] = $hourOne['post_name'];
                    $data[$hourOne['staffer_id']]['postbe_ismianjob'] = $hourOne['postbe_ismianjob'];
                    $data[$hourOne['staffer_id']]['is_cross'] = $hourOne['is_cross'];
                    foreach ($timeArray as $timeOne) {

                        foreach ($enweekarray as $daykey => $day) {
//
                            if ($week == $daykey) {
                                if ($timeOne['time'] == $value) {
                                    if ($daykey == 0) {
                                        $data[$hourOne['staffer_id']]['list']['a' . $num]['jiedian'] = $noon_name[$num];
                                    } else {
                                        $data[$hourOne['staffer_id']]['list']['a' . $num][$day][] = $hourOne;
                                    }
                                }
                            } else {
                                if ($daykey == 0) {
                                    $data[$hourOne['staffer_id']]['list']['a' . $num]['jiedian'] = $noon_name[$num];
                                } else {
                                    $data[$hourOne['staffer_id']]['list']['a' . $num][$day]['-1'] = '';
                                }
                            }
                        }
                    }
                }
            }

            if ($data) {
                foreach ($data as $k => $dataArray) {
                    foreach ($dataArray['list'] as $key => $dataOne) {
                        foreach ($timeArray as $timeOne) {
                            if ($key != 'a' . $tem_noon[$timeOne['time']]) {
                                foreach ($enweekarray as $daykey => $day) {
                                    if ($daykey == 0) {
                                        $data[$k]['list']['a' . $tem_noon[$timeOne['time']]]['jiedian'] = $timeOne['name'];
                                    } else {
                                        $data[$k]['list']['a' . $tem_noon[$timeOne['time']]][$day]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $data = $this->get_arr($data);
            $data = array_values($data);

            foreach ($data as &$dataOne) {
                ksort($dataOne['list']);
            }

            $field = array();

            $k = 0;
            $field[$k]["fieldstring"] = 'jiedian';
            $field[$k]["fieldname"] = '日期/节点';
            $field[$k]["custom"] = 1;
            $field[$k]["show"] = 1;
            $k++;

            foreach (self::$WORK_DAY as $week) {
                $field[$k]["fieldstring"] = $week['en'];
                $field[$k]["fieldname"] = $week['cn'];
                $field[$k]["custom"] = 1;
                $field[$k]["show"] = 1;
                $k++;
            }

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_shortname", "school_id='{$this->school_id}'");
            $tem_data = array();
            $tem_data['list'] = $data;
            $tem_data['field'] = $field;

            $schoolName = $schoolOne['school_shortname'] ? $schoolOne['school_shortname'] : $schoolOne['school_cnname'];

            $tem_data['name'] = $schoolName . ' ' . $startday . '至' . $endday . ' 教师课程表';

            return $tem_data;

        } else {

            $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
            if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
                $startday = $request['hour_startday'];
            } else {
                $startday = date('Y-m-d', $startTime);
            }
            if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
                $endday = $request['hour_endday'];
            } else {
                $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
            }
            $where = 1;
            if (isset($request['keyword']) and $request['keyword'] !== "") {
                $where .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
            }
            if (isset($request['classroom_id']) and $request['classroom_id'] !== "") {
                $where .= " and ch.classroom_id = '{$request['classroom_id']}' ";
            }
            if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
                $where .= " and co.coursetype_id = '{$request['coursetype_id']}' ";
            }

            if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
                $where .= " and co.coursecat_id = '{$request['coursecat_id']}' ";
            }

            if (isset($request['course_id']) and $request['course_id'] !== "") {
                $where .= " and co.course_id = '{$request['course_id']}' ";
            }

            $datawhere = '1';
            if (isset($request['hour_way']) and $request['hour_way'] !== "") {
                $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
            }
            $arr_staffer = $this->DataControl->selectClear("
				  select t.staffer_id,s.staffer_cnname,s.staffer_enname
				  from smc_staffer as s
				  left JOIN smc_class_hour_teaching as t  ON t.staffer_id  = s.staffer_id and t.teaching_type =0
				  left JOIN smc_class_hour as ch  ON ch.class_id = t.class_id and t.hour_id = ch.hour_id
				  left JOIN smc_class as c ON c.class_id  = t.class_id
		          left join smc_course as co on co.course_id=c.course_id
				  left JOIN smc_classroom as cl ON cl.classroom_id  = ch.classroom_id
				  where c.school_id='{$this->school_id}' and ch.hour_ischecking <> '-1'
				  and ch.hour_day >='{$startday}' and ch.hour_day <= '{$endday}' and {$where} and s.staffer_leave = 0 and c.class_status <> '-2'
				   group by  t.staffer_id ");
            if ($arr_staffer) {

                foreach ($arr_staffer as &$arrOne) {
                    $arrOne['staffer_cnname'] = $arrOne['staffer_enname'] ? $arrOne['staffer_cnname'] . '-' . $arrOne['staffer_enname'] : $arrOne['staffer_cnname'];
                }


                $arr_staffer_id = array_column($arr_staffer, 'staffer_id');
                $str_staffer_id = trim(implode(',', $arr_staffer_id), ',');
                $arr_staffercnname = array_column($arr_staffer, 'staffer_cnname', 'staffer_id');
                $arr_stafferenname = array_column($arr_staffer, 'staffer_enname', 'staffer_id');

            } else {
                return array();
            }
            $sql = " select  s.staffer_id,s.staffer_cnname,s.staffer_enname,c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,t.teaching_type
		 from  smc_staffer as s
		 LEFT  JOIN  smc_class_hour_teaching as t  On s.staffer_id = t.staffer_id
		 LEFT JOIN smc_class_hour AS ch ON  ch.hour_id = t.hour_id
		 LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		 left join smc_course as co on co.course_id=c.course_id
         left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
		 where c.school_id='{$this->school_id}'
		 and ch.hour_ischecking <> '-1'
		 and  ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where}  and c.class_status <> '-2' and t.staffer_id in ($str_staffer_id) and {$datawhere}
		 order by ch.hour_day asc,ch.hour_starttime asc
		";
            $weekList = $this->DataControl->selectClear($sql);
            if ($weekList) {
                foreach ($weekList as &$val) {
                    $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                    if ($val['hour_way'] == 1) {
                        $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                        $val['classroom_cnname'] = $val['classroom_brnach'] = $val['hour_number'];
                        $val['classroom_iscloud'] = "1";
                    }
                    $val['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");
                    if ($val['hour_ischecking'] == 0) {
                        $val['hour_clocking'] = '0';
                    } else {
                        $val['hour_clocking'] = '1';
                    }
                }
            }
            $time_start = strtotime($startday);
            $time_end = strtotime($endday);
            $date = array();
            while ($time_start <= $time_end) {
                $date[] = date('Y-m-d', $time_start);
                $time_start = strtotime('+1 day', $time_start);
            }

            $data = array();
            if ($weekList) {

//			只检测未上课的课时冲突
//			if (isset($request['conflict']) && $request['conflict'] == 1) {
//				foreach($weekList as $key => $value){
//					if($value['hour_ischecking'] == 0){
//						$dataWeekList1[$key] =  $value;
//
//				}else{
//						$dataWeekList2[$key.'a'] =  $value;
//					 }
//				}
//				$dataWeekList1 = $this->checkClassroomConflict($dataWeekList1);
//				$dataWeekList1 = $this->checkStafferConflict($dataWeekList1);
//
//				$weekList =array_values(array_merge($dataWeekList1, $dataWeekList2));
//			}
//			全局检测冲突
                if (isset($request['conflict']) && $request['conflict'] == 1) {
                    $weekList = $this->checkClassroomConflict($weekList);
                    $weekList = $this->checkStafferConflict($weekList);
                }
                foreach ($weekList as $key => &$val) {
                    foreach ($arr_staffer_id as $k => $v) {
                        $data[$v]['staffer_cnname']['staffer_cnname'] = $arr_staffercnname[$v];
                        $data[$v]['staffer_cnname']['staffer_enname'] = $arr_stafferenname[$v];

                        if ($val['staffer_id'] == $v) {
                            foreach ($date as $datekey => $datevalue) {
                                $week = date('w', strtotime($datevalue));
                                $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                                $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                                if ($val['hour_day'] == $datevalue) {
                                    $data[$v][$enweekarray[$week]][] = $val;
                                } else {
                                    $data[$v][$enweekarray[$week]]['-1'] = '';
                                }
                            }
                        }
                    }
                }
            }
            $data = $this->get_arr($data);
            return $data;
        }


    }

    /**
     * @param $request
     * @return $array
     * 获取 教室一周的课表
     */
    function getRoomWeekTimetable($request)
    {

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and (c.class_cnname like '%{$request['keyword']}%'  or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%') ";
        }

        if (isset($request['re_staffer_id']) and $request['re_staffer_id'] !== "") {
            $where .= " and s.staffer_id = '{$request['re_staffer_id']}'";
        }

        $arr_classroom = $this->DataControl->selectClear("
				select  h.classroom_id ,cl.classroom_cnname
				from  smc_class_hour as h
 				left JOIN  smc_classroom as cl ON  cl.classroom_id = h.classroom_id
 				left JOIN  smc_class as c ON  c.class_id  = h.class_id
 				LEFT JOIN smc_class_hour_teaching AS t ON t.hour_id = h.hour_id and  t.teaching_type =0
				LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
				where c.school_id='{$this->school_id}' and h.hour_ischecking <> '-1'
				and h.hour_day >='{$startday}'  and h.hour_day <= '{$endday}'  and {$where}  and c.class_status <> '-2' and  h.classroom_id >0 and cl.classroom_id > 0
				group by h.classroom_id ");


        if ($arr_classroom) {
            $arr_classroom_id = array_column($arr_classroom, 'classroom_id');
            $str_classroom_id = trim(implode(',', $arr_classroom_id), ',');
            $arr_classroomcnname = array_column($arr_classroom, 'classroom_cnname', 'classroom_id');


        } else {
            return array();
        }

        $sql = "select c.class_id,c.class_enname as class_cnname,ch.hour_id, s.staffer_cnname, s.staffer_enname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,cl.classroom_id,cl.classroom_cnname,t.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype,(select count(ss.study_id) from smc_student_study as ss where ss.study_beginday <= ch.hour_day and (ss.study_endday >= ch.hour_day  or ss.study_endday = '') and ss.class_id = c.class_id) as num
            from  smc_classroom AS cl
			LEFT JOIN smc_class_hour AS ch ON cl.classroom_id = ch.classroom_id
			LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
			LEFT JOIN smc_course AS co ON co.course_id = c.course_id
			LEFT JOIN smc_class_hour_teaching AS t ON t.hour_id = ch.hour_id and  t.teaching_type =0
			LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
            where {$where} and ch.hour_ischecking <> '-1'
              and  c.school_id='{$this->school_id}' AND ch.hour_day >='{$startday}'  and ch.hour_day <='{$endday}' and c.class_status <> '-2' and cl.classroom_id in ($str_classroom_id)
           order by ch.hour_day ASC ,ch.hour_starttime ASC
    		";

        $weekList = $this->DataControl->selectClear($sql);
        if ($weekList) {
            foreach ($weekList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $val['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");
                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }

                $val['class_cnname'] = $val['class_cnname'].'（在课'.$val['num'].'人）';
            }
        }
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $weekList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据数据";
                return false;
            }
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    $datearray['hour_weekday'] = '周' . $weekarray[date('w', strtotime($dateexcelvar['hour_day']))] . '(' . $dateexcelvar['hour_day'] . ')';
                    $datearray['time'] = $dateexcelvar['hour_starttime'] . '-' . $dateexcelvar['hour_endtime'];
                    $datearray['hour_way_name'] = $dateexcelvar['hour_way'] == 1 ? '线上课' : '实体课';
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("教室名称", '上课周次', "上课时间", "上课方式", "班级名称", "教师");
            $excelfileds = array('classroom_cnname', 'hour_weekday', 'time', 'hour_way_name', 'class_cnname', 'staffer_cnname');
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$this->school_id}'");
            $tem_name = $schoolOne['school_cnname'] . $startday . '~' . $endday . '教室课表.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {

            //只检测未上课的课时冲突
//			if (isset($request['conflict']) && $request['conflict'] == 1) {
//				foreach($weekList as $key => $value){
//					if($value['hour_ischecking'] == 0){
//						$dataWeekList1[$key] =  $value;
//
//				}else{
//						$dataWeekList2[$key.'a'] =  $value;
//					 }
//				}
//				$dataWeekList1 = $this->checkClassroomConflict($dataWeekList1);
//				$dataWeekList1 = $this->checkStafferConflict($dataWeekList1);
//
//				$weekList =array_values(array_merge($dataWeekList1, $dataWeekList2));
//			}

//			检测全局课时
            if (isset($request['conflict']) && $request['conflict'] == 1) {

                $weekList = $this->checkClassroomConflict($weekList);

                $weekList = $this->checkStafferConflict($weekList);

            }
            foreach ($weekList as $key => &$val) {

                foreach ($arr_classroom_id as $k => $v) {
                    $data[$v]['classroom_cnname']['classroom_cnname'] = $arr_classroomcnname[$v];
                    if ($val['classroom_id'] == $v) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];


                            if ($val['hour_day'] == $datevalue) {


                                $data[$v][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    }
                }
            }
        }


        $data = $this->get_arr($data);

        return $data;


    }

    function inviteTimetable($request){

        if(!isset($request['month']) || $request['month']==''){
            $request['month']=date("Y-m");
        }

        $start=date("Y-m-",strtotime($request['month'])).'01';

        $end=date("Y-m-t",strtotime($request['month']));

        $weekstart=date("w",strtotime($start));
        $weekend=date("w",strtotime($end));

        $start_num=$weekstart-1;
        $end_num=7-$weekend;

        $starttime=date("Y-m-d",strtotime('-'.$start_num.' day',strtotime($start)));
        $endtime=date("Y-m-d",strtotime('+'.$end_num.' day',strtotime($end)));

        $enweekarray = array("Sunday","Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");

        $datawhere = " 1 ";
        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
        }

        if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}' ";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['course_id']) and $request['course_id'] !== "") {
            $datawhere .= " and co.course_id = '{$request['course_id']}' ";
        }

        if (isset($request['hour_way']) and $request['hour_way'] !== "") {
            $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
        }

        if (isset($request['school_id']) and $request['school_id'] !== "") {
            $datawhere .= " and c.school_id ='{$request['school_id']}'";
        }else{
            $this->error = true;
            $this->errortip = "请选择学校";
            return false;
        }


        $sql = " select c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype,c.class_appointnum,ca.coursecat_branch  
                ,if(co.course_inclasstype=3 and co.course_openclasstype=1,ifnull((select count(cb.audition_id) from view_crm_audition as cb where cb.audition_isvisit>=0 and cb.hour_id=(select h.hour_id from smc_class_hour as h where h.class_id=c.class_id and h.hour_ischecking <> '-1' order by h.hour_day asc,h.hour_lessontimes asc limit 0,1)),0),ifnull((select count(cb.audition_id) from view_crm_audition as cb where cb.hour_id=ch.hour_id and cb.audition_isvisit>=0),0)) as bookingNum
     from smc_class_hour AS ch
     LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
     left join smc_course as co on co.course_id=c.course_id
     left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
     left join smc_code_coursecat as ca on ca.coursecat_id=co.coursecat_id
     where {$datawhere}
     and ch.hour_ischecking <> '-1'
     and  ch.hour_day >='{$starttime}'  and ch.hour_day <= '{$endtime}' and c.class_status <> '-2' 
     order by ch.hour_day asc,ch.hour_starttime asc
    ";
        $hourList = $this->DataControl->selectClear($sql);

        $time_start = strtotime($starttime);
        $time_end = strtotime($endtime);
        $dateList = array();
        while ($time_start <= $time_end) {
            $dateList[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $data = array();
        if ($hourList) {
            foreach($dateList as $date){
                if($date<$start || $date>$end){
                    if($date<$start){
                        $weeknum='1';
                    }else{
                        $weeknum=ceil((date("d",strtotime($end))+$start_num+1)/7);
                    }
                }else{
                    $weeknum=ceil((date("d",strtotime($date))+$start_num)/7);
                }

                foreach ($hourList as $hourOne) {
                    if($date==$hourOne['hour_day']){
                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['day']=date('d',strtotime($date)).'日';
                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['month']=date('Y',strtotime($date)).'年'.date('m',strtotime($date)).'月'.date('d',strtotime($date));

                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['is_this_month']=date('Y-m',strtotime($date))==$request['month']?1:0;

                        $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['info'][]=$hourOne;
                    }else{
                        if(!isset($data[$weeknum][$enweekarray[date("w",strtotime($date))]]['info'])){
                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['day']=date('d',strtotime($date)).'日';
                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['month']=date('Y',strtotime($date)).'年'.date('m',strtotime($date)).'月'.date('d',strtotime($date));

                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['is_this_month']=date('Y-m',strtotime($date))==$request['month']?1:0;

                            $data[$weeknum][$enweekarray[date("w",strtotime($date))]]['info']=array();
                        }
                    }
                }
            }
        }

        return $data;

    }




    /**
     * @param $request
     * @return $array
     * 获取 教室一周的课表的教师
     */
    function getRoomTableStaffer($request)
    {
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and (c.class_cnname like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%') ";
        }

        if (isset($request['re_staffer_id']) and $request['re_staffer_id'] !== "") {
            $where .= " and s.staffer_id = '{$request['re_staffer_id']}'";
        }

        $arr_staffer = $this->DataControl->selectClear("
				  select t.staffer_id,s.staffer_cnname,s.staffer_enname
				  from smc_staffer as s
				  left JOIN smc_class_hour_teaching as t  ON t.staffer_id  = s.staffer_id  and  t.teaching_type =0
				  left JOIN smc_class_hour as ch  ON ch.class_id = t.class_id and t.hour_id = ch.hour_id
				  left JOIN smc_class as c ON c.class_id  = t.class_id
				  left JOIN smc_classroom as cl ON cl.classroom_id  = ch.classroom_id
				  where  c.school_id='{$this->school_id}'  and ch.hour_day >='{$startday}' and c.class_status <> '-2' and ch.hour_day <= '{$endday}' and {$where}
				  group by  t.staffer_id ");

        if ($arr_staffer) {
            foreach ($arr_staffer as &$stafferOne) {
                $stafferOne['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
            }


            return $arr_staffer;

        } else {
            return array();
        }

    }

    //----------------------------------------------------------------

    /**
     * @param $weekList
     * @return mixed
     * 检测教室冲突
     */
    function checkClassroomConflict($weekList)
    {
        $week_date = date('Y-m-d');
        if ($weekList) {
            $weekList = array_values($weekList);
            //检查冲突  //时间冲突
            if (count($weekList) > 1) {
                for ($i = 0; $i < count($weekList); $i++) {
                    for ($j = $i + 1; $j < count($weekList); $j++) {

                        if (($weekList[$i]['hour_day'] == $weekList[$j]['hour_day']) && ($weekList[$i]['classroom_id'] == $weekList[$j]['classroom_id'])) {
                            $iend = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_endtime'];
                            $istart = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_starttime'];
                            $jend = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_endtime'];
                            $jstart = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_starttime'];

                            $iend = date('Y-m-d H:i:s', strtotime($iend));
                            $istart = date('Y-m-d H:i:s', strtotime($istart));
                            $jend = date('Y-m-d H:i:s', strtotime($jend));
                            $jstart = date('Y-m-d H:i:s', strtotime($jstart));

                            if (!($iend <= $jstart) && !($jend <= $istart) && $weekList[$i]['hour_ischecking'] <> -1 && $weekList[$j]['hour_ischecking'] <> -1) {

                                $weekList[$i]['hour_ischecking'] = -2;
                                $weekList[$j]['hour_ischecking'] = -2;
                                $weekList[$j]['classroom_confict'] = -2;
                                $weekList[$i]['classroom_confict'] = -2;
                            }
                        }
                    }
                }
            }
        }
        return $weekList;
    }

    /**
     * @param $weekList
     * @return mixed
     * 检测教师冲突
     */
    function checkStafferConflict($weekList)
    {

        $week_date = date('Y-m-d');
        if ($weekList) {
            //检查冲突  //时间冲突
            if (count($weekList) > 1) {
                for ($i = 0; $i < count($weekList); $i++) {
                    for ($j = $i + 1; $j < count($weekList); $j++) {
                        if (($weekList[$i]['hour_day'] == $weekList[$j]['hour_day']) && ($weekList[$i]['staffer_id'] == $weekList[$j]['staffer_id'])) {
                            $iend = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_endtime'];
                            $istart = $weekList[$i]['hour_day'] . " " . $weekList[$i]['hour_starttime'];
                            $jend = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_endtime'];
                            $jstart = $weekList[$j]['hour_day'] . " " . $weekList[$j]['hour_starttime'];

                            $iend = date('Y-m-d H:i:s', strtotime($iend));
                            $istart = date('Y-m-d H:i:s', strtotime($istart));
                            $jend = date('Y-m-d H:i:s', strtotime($jend));
                            $jstart = date('Y-m-d H:i:s', strtotime($jstart));
                            if (!($iend <= $jstart) && !($jend <= $istart) && $weekList[$i]['hour_ischecking'] <> -1 && $weekList[$j]['hour_ischecking'] <> -1) {
                                $weekList[$i]['hour_ischecking'] = -2;
                                $weekList[$j]['hour_ischecking'] = -2;
                                $weekList[$j]['staffer_confict'] = -2;
                                $weekList[$i]['staffer_confict'] = -2;
                            }
                        }
                    }
                }
            }
        }
        return $weekList;
    }


    // pad 点名--------------------------------------------------------------------------

    /**
     * @param $request
     * 教师一天的课表
     */
    function teaTodayTimeTable($request)
    {

        if (isset($request['hour_day']) && $request['hour_day'] !== "") {
            $hour_day = date("Y-m-d", strtotime($request['hour_day']));
        } else {
            $hour_day = date("Y-m-d");
        }

        $datawhere = "co.course_inclasstype IN (0,1)";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }

        $sql = "select c.class_id,c.class_cnname,c.class_enname,h.hour_id,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,t.staffer_id,h.hour_noon,
                concat(s.staffer_cnname ,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname
 				from smc_class_hour as h
 				left JOIn smc_course as co ON co.course_id = h.course_id
				left JOIN smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class as c ON c.class_id = h.class_id
				LEFT JOIN smc_class_hour_teaching AS t ON t.hour_id = h.hour_id and t.teaching_type =0
				LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
				where {$datawhere} and c.school_id='{$this->school_id}' and h.hour_day='{$hour_day}' and c.class_status <> '-2' and s.staffer_id > 0 and h.hour_ischecking<>'-1'";
        $hourList = $this->DataControl->selectClear($sql);

        $arr_staffer = $this->DataControl->selectClear("
				  select t.staffer_id,
				   concat(s.staffer_cnname ,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname
				  from smc_staffer as s
				  left JOIN smc_class_hour_teaching as t ON t.staffer_id = s.staffer_id and t.teaching_type =0
				  left JOIN smc_class_hour as ch ON ch.class_id = t.class_id and t.hour_id = ch.hour_id
				  left JOIN smc_course as co ON co.course_id = ch.course_id
				  left JOIN smc_class as c ON c.class_id = t.class_id
				  left JOIN smc_classroom as cl ON cl.classroom_id = ch.classroom_id
				  where {$datawhere} and c.school_id='{$this->school_id}' and ch.hour_day='{$hour_day}' and c.class_status <> '-2' and ch.hour_ischecking<>'-1'
				  group by t.staffer_id ");


        $data = array();
        $noon = array("1" => "morning", "2" => "afternoon", "3" => "night");

        if (!$arr_staffer) {
            $arr_staffer = $this->DataControl->selectClear("
				  select t.staffer_id,s.staffer_cnname
				  from smc_staffer as s
				  left JOIN smc_class_hour_teaching as t ON t.staffer_id = s.staffer_id and t.teaching_type =0
				  left JOIN smc_class_hour as ch ON ch.class_id = t.class_id and t.hour_id = ch.hour_id
				  left JOIN smc_course as co ON co.course_id = ch.course_id
				  left JOIN smc_class as c ON c.class_id = t.class_id
				  where {$datawhere} and c.school_id='{$this->school_id}' and ch.hour_ischecking<>'-1'
				  group by t.staffer_id limit 0,7 ");

            $this->error = true;
            $this->errortip = "无数据！";
            return false;
        }


        //检查冲突
        if (isset($request['conflict']) && $request['conflict'] == 1 && $hourList) {
            if (count($hourList) > 1) {
                for ($i = 0; $i < count($hourList); $i++) {
                    for ($j = $i + 1; $j < count($hourList); $j++) {
                        if (($hourList[$i]['hour_day'] == $hourList[$j]['hour_day']) && ($hourList[$i]['staffer_id'] == $hourList[$j]['staffer_id'])) {
                            $iend = $hourList[$i]['hour_day'] . " " . $hourList[$i]['hour_endtime'];
                            $istart = $hourList[$i]['hour_day'] . " " . $hourList[$i]['hour_starttime'];
                            $jend = $hourList[$j]['hour_day'] . " " . $hourList[$j]['hour_endtime'];
                            $jstart = $hourList[$j]['hour_day'] . " " . $hourList[$j]['hour_starttime'];

                            $iend = date('Y-m-d H:i:s', strtotime($iend));
                            $istart = date('Y-m-d H:i:s', strtotime($istart));
                            $jend = date('Y-m-d H:i:s', strtotime($jend));
                            $jstart = date('Y-m-d H:i:s', strtotime($jstart));

                            if (!($iend < $jstart) && !($jend < $istart) && $hourList[$i]['hour_ischecking'] == 0 && $hourList[$j]['hour_ischecking'] == 0) {
                                $hourList[$i]['hour_ischecking'] = -2;
                                $hourList[$j]['hour_ischecking'] = -2;
                            }
                        }
                    }
                }
            }
        }

        if ($hourList) {
            foreach ($hourList as $key => $val) {
                foreach ($noon as $noonkey => $noonval) {
                    foreach ($arr_staffer as $room) {
                        if ($val['hour_noon'] == $noonkey) {
                            $data['a' . $noonkey]['noon_name']['noon_name'] = $noonval;
                            if ($val['staffer_id'] == $room['staffer_id']) {
                                $data['a' . $noonkey][(string)$room['staffer_cnname']][] = $val;
                            } else {
                                $data['a' . $noonkey][(string)$room['staffer_cnname']]['-1'] = '';
                            }
                        }
                    }
                }
            }

            if (count($data) < 3) {
                $tem_data = array();
                foreach ($data as $k => $v) {
                    $tem_data[] = $k;
                }
                $tem_noon = array_diff($noon, $tem_data);

                if ($tem_noon) {
                    foreach ($tem_noon as $key => $val) {
                        foreach ($arr_staffer as $room) {
                            $data['a' . $key]['noon_name']['noon_name'] = $val;
                            $data['a' . $key][(string)$room['staffer_cnname']]['-1'] = '';
                        }
                    }
                }
            }
        } else {
            foreach ($noon as $key => $val) {
                foreach ($arr_staffer as $room) {
                    $data['a' . $key]['noon_name']['noon_name'] = $val;
                    $data['a' . $key][(string)$room['staffer_cnname']]['-1'] = '';
                }
            }
        }


        $data = $this->get_arr($data);

        $data = array_values($data);
        $tem_data = array();
        if (!$data) {
            $this->error = true;
            $this->errortip = "无数据！";
            return false;
        }
        foreach ($data as $val) {
            if ($val['noon_name']['noon_name'] == 'morning') {
                $tem_data[0] = $val;
                $tem_data[0]['noon_name']['noon_name'] = $this->LgStringSwitch('上午');
            } elseif ($val['noon_name']['noon_name'] == 'afternoon') {
                $tem_data[1] = $val;
                $tem_data[1]['noon_name']['noon_name'] = $this->LgStringSwitch('下午');
            } elseif ($val['noon_name']['noon_name'] == 'night') {
                $tem_data[2] = $val;
                $tem_data[2]['noon_name']['noon_name'] = $this->LgStringSwitch('晚上');
            }
        }
        asort($tem_data);
        $rel_data = array();
        $rel_data['roomList'] = $arr_staffer;
        $rel_data['data'] = $tem_data;
        return $rel_data;

    }


    //最新3个时间段课表

    function roomTimeTableList($request)
    {
        if (!isset($request['classroom_id']) || $request['classroom_id'] == "") {
            $request['classroom_id'] = 0;
        }

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }


        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = date("H:i", strtotime($request['starttime']));
        } else {
            $starttime = "06:00";
        }

        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = date("H:i", strtotime($request['endtime']));
        } else {
            $endtime = "23:59";
        }

        if (isset($request['lengthtime']) && $request['lengthtime'] !== "") {
            $lengthtime = intval($request['lengthtime']);
        } else {
            $lengthtime = '60';
        }

//		$time_length = "60 minute";
//		$time_quantum = date('H:i',strtotime( " + $time_length",strtotime($starttime)));

        $time_quantum = array();

        for ($i = strtotime($starttime); $i <= strtotime($endtime); $i = $i + 60 * $lengthtime) {

            $str_endtime = date("H:i", $i + 60 * $lengthtime);

            if ($str_endtime == "00:00") {
                $str_endtime = $endtime;
            }
            $time_quantum[] = date("H:i", $i) . '-' . $str_endtime;
        }

        $time_quantum = array_unique($time_quantum);

        $where = "1 and ch.hour_ischecking <> '-1'";

        $sql = "select c.class_id,class_branch,c.class_cnname,ch.hour_id, s.staffer_cnname,s.staffer_enname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,t.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype
            from  smc_classroom AS cl
			LEFT JOIN smc_class_hour AS ch ON cl.classroom_id = ch.classroom_id
			LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
			LEFT JOIN smc_course AS co ON co.course_id = c.course_id
			LEFT JOIN smc_class_hour_teaching AS t ON t.hour_id = ch.hour_id and  t.teaching_type =0
			LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
            where {$where}  and  c.school_id='{$this->school_id}' AND ch.hour_day >='{$startday}'  and ch.hour_day <='{$endday}' and cl.classroom_id ='{$request['classroom_id']}'  and c.class_status <> '-2'
    		";

        $weekList = $this->DataControl->selectClear($sql);
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $weekList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据数据";
                return false;
            }
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    $datearray['hour_weekday'] = '周' . $weekarray[date('w', strtotime($dateexcelvar['hour_day']))] . '(' . $dateexcelvar['hour_day'] . ')';
                    $datearray['time'] = $dateexcelvar['hour_starttime'] . '-' . $dateexcelvar['hour_endtime'];
                    $datearray['hour_way_name'] = $dateexcelvar['hour_way'] == 1 ? '线上课' : '实体课';
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'] . '-' . $dateexcelvar['staffer_enname'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("教室名称", "上课周次", "上课时间", "上课方式", "班级名称", "教师");
            $excelfileds = array('classroom_cnname', 'hour_weekday', 'time', 'hour_way_name', 'class_cnname', 'staffer_cnname');
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$this->school_id}'");
            $tem_name = $schoolOne['school_cnname'] . $startday . '~' . $endday . '教室详情课表.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }


        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {

            foreach ($weekList as $key => $val) {
                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $val['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");

                foreach ($time_quantum as $k => $v) {
                    $data[$k]['time_quantum']['time_quantum'] = $v;


                    $iend = $weekList[$key]['hour_endtime'];
                    $istart = $weekList[$key]['hour_starttime'];
                    $jstart = substr($v, 0, 5);
                    $jend = substr($v, 6, 5);
                    $i = 0;

                    if ($istart >= $jstart && $istart < $jend) {

                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];

                            if ($val['hour_day'] == $datevalue) {
                                $i++;
                                $data[$k][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$k][$enweekarray[$week]] = array();
                            }
                        }
                    } else {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                        }

                    }


                }
            }
        }


//
//		$data = $this->get_arr($data);


        return $data;
    }

    function stafferTimeTableList($request)
    {
        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d');
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }


        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = date("H:i", strtotime($request['starttime']));
        } else {
            $starttime = "08:00";
        }

        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = date("H:i", strtotime($request['endtime']));
        } else {
            $endtime = "21:00";
        }

        if (isset($request['lengthtime']) && $request['lengthtime'] !== "") {
            $lengthtime = intval($request['lengthtime']);
        } else {
            $lengthtime = '60';
        }

//		$time_length = "60 minute";
//		$time_quantum = date('H:i',strtotime( " + $time_length",strtotime($starttime)));

        $time_quantum = array();

        for ($i = strtotime($starttime); $i < strtotime($endtime); $i = $i + 60 * $lengthtime) {

            $str_endtime = date("H:i", $i + 60 * $lengthtime);

            if ($str_endtime == "00:00") {
                $str_endtime = $endtime;
            }
            $time_quantum[] = date("H:i", $i) . '-' . $str_endtime;
        }

        $time_quantum = array_unique($time_quantum);

        $where = '1';
        $datawhere = "1 and ch.hour_ischecking <>'-1'";
        if (isset($request['hour_way']) and $request['hour_way'] !== "") {
            $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
        }
        if (!isset($request['re_staffer_id']) || $request['re_staffer_id'] == "") {
            $request['re_staffer_id'] = 0;
        }

        $sql = " select s.staffer_id,s.staffer_cnname,s.staffer_enname,c.class_id,c.class_cnname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype
		 from  smc_staffer as s
		 LEFT  JOIN  smc_class_hour_teaching as t  On s.staffer_id = t.staffer_id and  t.teaching_type =0
		 LEFT JOIN smc_class_hour AS ch ON  ch.hour_id = t.hour_id
		 LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		 left join smc_course as co on co.course_id=c.course_id
         left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
		 where c.school_id='{$this->school_id}' and  ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where}  and t.staffer_id ='{$request['re_staffer_id']}' and c.class_status <> '-2' and {$datawhere}
		";

        $weekList = $this->DataControl->selectClear($sql);

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $weekList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据数据";
                return false;
            }
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'] . '-' . $dateexcelvar['staffer_enname'];
                    $datearray['hour_weekday'] = '周' . $weekarray[date('w', strtotime($dateexcelvar['hour_day']))] . '(' . $dateexcelvar['hour_day'] . ')';
                    $datearray['time'] = $dateexcelvar['hour_starttime'] . '-' . $dateexcelvar['hour_endtime'];
                    $datearray['hour_way_name'] = $dateexcelvar['hour_way'] == 1 ? '线上课' : '实体课';
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = array("教师名称", '上课周次', "上课时间", "上课方式", "班级名称", "教室");
            $excelfileds = array('staffer_cnname', 'hour_weekday', 'time', 'hour_way_name', 'class_cnname', 'classroom_cnname');
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$this->school_id}'");
            $tem_name = $schoolOne['school_cnname'] . $startday . '~' . $endday . '教室详情课表.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {
            foreach ($weekList as $key => &$val) {

                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $val['classroom_cnname'] = $val['classroom_branch'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 0 ? "实体课" : "线上课");

                foreach ($time_quantum as $k => $v) {
                    $data[$v]['time_quantum']['time_quantum'] = $v;
                    $iend = $weekList[$key]['hour_endtime'];
                    $istart = $weekList[$key]['hour_starttime'];
                    $jstart = substr($v, 0, 5);
                    $jend = substr($v, 6, 5);

                    if ($istart >= $jstart && $istart < $jend) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];

                            if ($val['hour_day'] == $datevalue) {
                                $data[$v][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    } else {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                        }

                    }
                }
            }
        }
        $data = $this->get_arr($data);
//        $data = array_values($data);

//        foreach ($data as &$dataOne) {
//            ksort($dataOne['list']);
//        }
        return $data;

    }

    function weekTimeList($request)
    {

        $where = '1';
        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and (c.class_cnname like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or sc.classroom_cnname like '%{$request['keyword']}%') ";


        }
        $today = date("Y-m-d");
        if (isset($request['class_id']) and $request['class_id'] !== "") {
            $where .= " and cl.class_id='{$request['class_id']}'";
        }
        if (isset($request['hour_way']) and $request['hour_way'] !== "" && $request['hour_way'] != 'undefined') {
            $where .= "  and  cl.lessonplan_way ='{$request['hour_way']}'";
        }

        $sql = "select cl.*,c.class_cnname,s.staffer_cnname,s.staffer_enname,sc.classroom_cnname,lessonplan_way,c.class_branch,c.class_enname
              from smc_class_lessonplan  as cl
 			   left join  smc_class as  c ON cl.class_id= c.class_id
 			   left join smc_staffer as s ON  s.staffer_id = cl.staffer_id
 			   left join smc_classroom as sc ON sc.classroom_id = cl.classroom_id
 			   where  c.school_id='{$this->school_id}' and {$where} and c.class_status <> '-2' and c.class_status <> '-1'
 			    and c.class_enddate >='{$today}'
 			    order by cl.lessonplan_weekno ASC ,cl.lessonplan_starttime ASC,cl.lessonplan_endtime Asc ";

        $list = $this->DataControl->selectClear($sql);

        if ($list) {
            foreach ($list as $key => &$value) {

                $value['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];

                $value['type'] = $value['lessonplan_weekno'];

                $value['time'] = $value['lessonplan_starttime'] . '-' . $value['lessonplan_endtime'];
                $value['note'] = "";
                $value['hour_way_name'] = $value['lessonplan_way'] == 0 ? "实体课" : "线上课";
            }

            if (isset($request['is_export']) && $request['is_export'] == 1) {
                $dateexcelarray = $list;
                if (!$dateexcelarray) {
                    $this->error = true;
                    $this->errortip = "暂无数据数据";
                    return false;
                }
                $weeklist = array_column($dateexcelarray, 'lessonplan_weekno');
                $weekNOvalues = array_count_values($weeklist);
                $outexceldate = array();
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['lessonplan_week'] = $dateexcelvar['lessonplan_week'];
                        $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                        $datearray['class_enname'] = $dateexcelvar['class_enname'];
                        $datearray['class_branch'] = $dateexcelvar['class_branch'];
                        $datearray['time'] = $dateexcelvar['time'];
                        $datearray['hour_way_name'] = $dateexcelvar['hour_way_name'];
                        $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                        $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = array("时间", "班级名称", '班级别名', "班级编号", "上课时间", "上课方式", "教室", "教师");
                $excelfileds = array('lessonplan_week', 'class_cnname', 'class_enname', 'class_branch', 'time', 'hour_way_name', 'classroom_cnname', 'staffer_cnname');
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$this->school_id}'");
                $tem_name = $schoolOne['school_cnname'] . '周次教师一览表.xls';

                query_to_excel_weektimelist($excelheader, $outexceldate, $excelfileds, $tem_name, $weekNOvalues);
                exit;
            }
            return $list;

        } else {
            return array();
        }
    }

//	预约类班级课表
    function appointTimeTable($request)
    {
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and  (c.class_cnname like '%{$request['keyword']}%'  or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' ) ";
        }
//		获取一周所有有课程安排的班级
        $arr_class = $this->DataControl->selectClear("
				select c.class_id ,class_cnname
				from  smc_class_hour as h
 				left JOIN smc_class as c ON  c.class_id = h.class_id
 				left JOIN smc_course as co ON  co.course_id =c.course_id
 				left JOIN smc_classroom as cl ON cl.classroom_id  = h.classroom_id
 				left JOIN smc_class_hour_teaching as t ON t.class_id = h.class_id and t.hour_id=h.hour_id and  t.teaching_type =0
                left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
				where c.school_id='{$this->school_id}'  and h.hour_day >='{$startday}'  and h.hour_day <= '{$endday}' and {$where}  and course_inclasstype = '2' and c.class_status <> '-2'
				group by c.class_id
				");

        if ($arr_class) {
            $arr_class_id = array_column($arr_class, 'class_id');
            $str_class_id = trim(implode(',', $arr_class_id), ',');
            $arr_classcnname = array_column($arr_class, 'class_cnname', 'class_id');

        } else {
            return array();
        }

        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname, s.staffer_enname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,ch.hour_way,co.course_cnname,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype
              from  smc_class as c
              left join smc_class_hour as ch on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left JOIN smc_class_hour_teaching as t ON t.class_id = c.class_id and t.hour_id=ch.hour_id and  t.teaching_type =0
              left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
              where ch.class_id in ({$str_class_id}) AND ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where} and c.school_id='{$this->school_id}' and course_inclasstype = '2' and c.class_status <> '-2' and ch.hour_ischecking <> '-1'
              ORDER BY c.class_createtime DESC
    		";

        $weekList = $this->DataControl->selectClear($sql);

        if ($weekList) {
            foreach ($weekList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }
            }
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {
            //检测全局的冲突
            if (isset($request['conflict']) && $request['conflict'] == 1) {
                $weekList = $this->checkClassroomConflict($weekList);
                $weekList = $this->checkStafferConflict($weekList);
            }
            foreach ($weekList as $key => &$val) {
                foreach ($arr_class_id as $k => $v) {
                    $data[$v]['class_cnname']['class_cnname'] = $arr_classcnname[$v];
                    if ($val['class_id'] == $v) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                            if ($val['hour_day'] == $datevalue) {
                                $data[$v][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    }
                }
            }
        }
        $data = $this->get_arr($data);

        return $data;
    }

    //期度类班级课表
    function periodTimeTable($request)
    {
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        if (isset($request['fixedtime']) && $request['fixedtime'] != '') {
            $request['hour_startday'] = $request['fixedtime'];
        }

        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and  (c.class_cnname like '%{$request['keyword']}%'  or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' ) ";
        }
//		获取一周所有有课程安排的班级
        $arr_class = $this->DataControl->selectClear("
				select c.class_id ,class_cnname
				from  smc_class_hour as h
 				left JOIN smc_class as c ON  c.class_id = h.class_id
 				left JOIN smc_course as co ON  co.course_id =c.course_id
 				left JOIN smc_classroom as cl ON cl.classroom_id  = h.classroom_id
 				left JOIN smc_class_hour_teaching as t ON t.class_id = h.class_id and t.hour_id=h.hour_id and  t.teaching_type =0
                left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
				where c.school_id='{$this->school_id}'  and h.hour_day >='{$startday}'  and h.hour_day <= '{$endday}' and {$where}  and course_inclasstype = '1' and c.class_status <> '-2'
				group by c.class_id
				");

        if ($arr_class) {
            $arr_class_id = array_column($arr_class, 'class_id');
            $str_class_id = trim(implode(',', $arr_class_id), ',');
            $arr_classcnname = array_column($arr_class, 'class_cnname', 'class_id');

        } else {
            return array();
        }

        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname, s.staffer_enname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,ch.hour_way,co.course_cnname,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype
              from  smc_class as c
              left join smc_class_hour as ch on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left JOIN smc_class_hour_teaching as t ON t.class_id = c.class_id and t.hour_id=ch.hour_id and  t.teaching_type =0
              left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
              where ch.class_id in ({$str_class_id}) AND ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where} and c.school_id='{$this->school_id}' and course_inclasstype = '1' and c.class_status <> '-2' and ch.hour_ischecking <> '-1'
              ORDER BY c.class_createtime DESC
    		";

        $weekList = $this->DataControl->selectClear($sql);

        if ($weekList) {
            foreach ($weekList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }
            }
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {
            //检测全局的冲突
            if (isset($request['conflict']) && $request['conflict'] == 1) {
                $weekList = $this->checkClassroomConflict($weekList);
                $weekList = $this->checkStafferConflict($weekList);
            }
            foreach ($weekList as $key => &$val) {
                foreach ($arr_class_id as $k => $v) {
                    $data[$v]['class_cnname']['class_cnname'] = $arr_classcnname[$v];
                    if ($val['class_id'] == $v) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                            if ($val['hour_day'] == $datevalue) {
                                $data[$v][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    }
                }
            }
        }
        $data = $this->get_arr($data);

        return $data;

    }

    /**
     * 公开课班级
     * author: ling
     * 对应接口文档 0001
     * Date 2021/1/19 0019
     * @param $request
     */
    function openClassTimeTable($request)
    {
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
            $startday = $request['hour_startday'];
        } else {
            $startday = date('Y-m-d', $startTime);
        }
        if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
            $endday = $request['hour_endday'];
        } else {
            $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
        }
        $where = 1;

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $where .= " and  (c.class_cnname like '%{$request['keyword']}%'  or c.class_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' ) ";
        }
//		获取一周所有有课程安排的班级
        $arr_class = $this->DataControl->selectClear("
				select c.class_id ,class_cnname
				from  smc_class_hour as h
 				left JOIN smc_class as c ON  c.class_id = h.class_id
 				left join smc_course as co On co.course_id=c.course_id
 				left JOIN smc_code_coursetype as ty ON  ty.coursetype_id =co.coursetype_id
 				left JOIN smc_classroom as cl ON cl.classroom_id  = h.classroom_id
 				left JOIN smc_class_hour_teaching as t ON t.class_id = h.class_id and t.hour_id=h.hour_id and  t.teaching_type =0
                left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
				where c.school_id='{$this->school_id}'  and h.hour_day >='{$startday}'  and h.hour_day <= '{$endday}' and {$where}  and ty.coursetype_isopenclass = '1' and c.class_status <> '-2'
				group by c.class_id
				");

        if ($arr_class) {
            $arr_class_id = array_column($arr_class, 'class_id');
            $str_class_id = trim(implode(',', $arr_class_id), ',');
            $arr_classcnname = array_column($arr_class, 'class_cnname', 'class_id');

        } else {
            return array();
        }

        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname, s.staffer_enname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,ch.hour_way,co.course_cnname,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype
              from  smc_class as c
              left join smc_class_hour as ch on c.class_id=ch.class_id
              left join smc_course as co on co.course_id=c.course_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left JOIN smc_class_hour_teaching as t ON t.class_id = c.class_id and t.hour_id=ch.hour_id and  t.teaching_type =0
              left JOIN smc_staffer as s ON s.staffer_id  = t.staffer_id
              where ch.class_id in ({$str_class_id}) AND ch.hour_day >='{$startday}'  and ch.hour_day <= '{$endday}' and {$where} and c.school_id='{$this->school_id}'  and c.class_status <> '-2' and ch.hour_ischecking <> '-1'
              ORDER BY c.class_createtime DESC
    		";

        $weekList = $this->DataControl->selectClear($sql);

        if ($weekList) {
            foreach ($weekList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }
            }
        }

        $time_start = strtotime($startday);
        $time_end = strtotime($endday);
        $date = array();
        while ($time_start <= $time_end) {
            $date[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $data = array();
        if ($weekList) {
            //检测全局的冲突
            if (isset($request['conflict']) && $request['conflict'] == 1) {
                $weekList = $this->checkClassroomConflict($weekList);
                $weekList = $this->checkStafferConflict($weekList);
            }
            foreach ($weekList as $key => &$val) {
                foreach ($arr_class_id as $k => $v) {
                    $data[$v]['class_cnname']['class_cnname'] = $arr_classcnname[$v];
                    if ($val['class_id'] == $v) {
                        foreach ($date as $datekey => $datevalue) {
                            $week = date('w', strtotime($datevalue));
                            $data['week_date'][$datekey]['weekday'] = $this->LgStringSwitch($datevalue . " 周" . $weekarray[$week]);
                            $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                            if ($val['hour_day'] == $datevalue) {
                                $data[$v][$enweekarray[$week]][] = $val;
                            } else {
                                $data[$v][$enweekarray[$week]]['-1'] = '';
                            }
                        }
                    }
                }
            }
        }
        $data = $this->get_arr($data);
        return $data;
    }
}
