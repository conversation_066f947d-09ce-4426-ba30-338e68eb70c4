<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Smc;


class modelTpl extends \Model\modelTpl{
    public $companyOne = array();//操作公司

    public function __construct(){
        parent::__construct();
    }

    //验证集团信息
    function verdictCompany($company_id)
    {
        $this->companyOne = $this->DataControl->getFieldOne("gmc_company",
            "company_id,company_code,company_cnname,company_language,company_logo,company_ismajor,comapny_isclocking,company_isachieve,company_isopenspecialnumber"
            , "company_id = '{$company_id}'");
        if (!$this->companyOne) {
            $this->error = true;
            $this->errortip = "集团信息不存在";
            return false;
        } else {
            return true;
        }
    }

    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;
    }

    function get_arr($arr)
    {
        foreach ($arr as $k => $v) {
            if (is_array($arr[$k])) {
                $arr[$k] = $this->get_arr($arr[$k]);
            } else {
                if ($k==-1) {
                    unset($arr[$k]);
                }
            }
        }
        return $arr;
    }

    public static $WORK_DAY = [
        1 => ['en' => 'Monday','cn'=>'周一'],
        2 => ['en' => 'Tuesday','cn'=>'周二'],
        3 => ['en' => 'Wednesday','cn'=>'周三'],
        4 => ['en' => 'Thursday','cn'=>'周四'],
        5 => ['en' => 'Friday','cn'=>'周五'],
        6 => ['en' => 'Saturday','cn'=>'周六'],
        7 => ['en' => 'Sunday','cn'=>'周日']
    ];

    function creatBranch(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $Str_num="1234567890";
        $randStr= $Str[rand(0,26)].$Str[rand(0,26)].date("YmdHis",time()).$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)];
        return $randStr;
    }

    function is_time_cross($beginTime1 = '', $endTime1 = '', $beginTime2 = '', $endTime2 = '')
    {
        $status = $beginTime2 - $beginTime1;
        if ($status > 0) {
            $status2 = $beginTime2 - $endTime1;
            if ($status2 >= 0) {
                return false;
            } else {
                return true;
            }
        } else {
            $status2 = $endTime2 - $beginTime1;
            if ($status2 > 0) {
                return true;
            } else {
                return false;
            }
        }
    }

    function ChangeTime($time){
        $time = time() - $time;
        if(is_numeric($time)){
            $value = array(
                "years" => 0, "days" => 0, "hours" => 0,
                "minutes" => 0, "seconds" => 0,
            );
            if($time >= 31556926){
                $value["years"] = floor($time/31556926);
                $time = ($time%31556926);
                $t = $value["years"].'年前';
            }
            elseif(31556926 >$time && $time >= 86400){
                $value["days"] = floor($time/86400);
                $time = ($time%86400);
                $t = $value["days"].'天前';
            }
            elseif(86400 > $time && $time >= 3600){
                $value["hours"] = floor($time/3600);
                $time = ($time%3600);
                $t = $value["hours"].'小时前';
            }
            elseif(3600 > $time && $time >= 60){
                $value["minutes"] = floor($time/60);
                $time = ($time%60);
                $t = $value["minutes"].'分钟前';
            }else{
                $t = $time.'秒前';
            }
            return $t;
        }else{
            return date('Y-m-d H:i:s',time());
        }
    }

    function creatPid(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $Str_num="1234567890";
        $date=date("Ymd",time());
        $randStr= $Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].substr($date,2).$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)];
        return $randStr;
    }


    function createOutPid(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000000,99999999);
        $OutPID = $rangtr.$rangtime.$rangnum;
        return $OutPID;
    }

    //生成收据序列号
    function createReceiptPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    function createStuRandom($student_branch){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = substr(date("ymdHis",time()),2);
        $Random = $student_branch.$rangtime.$rangtr;
        return $Random;
    }

    function createRandomBranch(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $Str_num="1234567890";
        $date=date("Ymd",time());
        $randStr= $Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].substr($date,2).$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)];
        return $randStr;
    }

    function createRandom($initial){
        $Str = "0123456789";
        $rangtr = $Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)].$Str[rand(0,9)];
        $Random = $initial.$rangtr;
        return $Random;
    }

    function getDate($startdate, $enddate)
    {
        $stimestamp = strtotime($startdate);
        $etimestamp = strtotime($enddate);
        $days = ($etimestamp - $stimestamp) / 86400 + 1;
        $date = array();
        for ($i = 0; $i < $days; $i++) {
            $date[] = date('Y-m-d', $stimestamp + (86400 * $i));
        }
        return $date;
    }


    function getCoursePricing($course_id,$company_id,$school_id){
        $day=date("Y-m-d",time());
        $sql="SELECT
                    t.course_id,
                    p.pricing_id,
                    t.tuition_id,
                    t.tuition_originalprice,
                    t.tuition_sellingprice,
                    t.tuition_buypiece,
                    t.tuition_unitprice,
                    a.agreement_id,
                    t.tuition_addtime,
                    t.tuition_refundprice,sc.coursetype_id,t.tuition_minclassnum
                FROM
                    smc_fee_pricing_tuition AS t,
                    smc_fee_pricing AS p,
                    smc_fee_agreement AS a,
                    smc_course as sc    
                WHERE
                    t.pricing_id = p.pricing_id
                AND p.agreement_id = a.agreement_id
                AND sc.course_id = p.course_id
                AND t.course_id = '{$course_id}'
                AND (
                    (
                        p.pricing_applytype = '1'
                        AND p.pricing_id IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$school_id}'
                        )
                    )
                    OR (
                        p.pricing_applytype = '-1'
                        AND p.pricing_id NOT IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$school_id}'
                        )
                    )
                    OR (p.pricing_applytype = '0')
                )
                AND a.agreement_startday <= '{$day}'
                AND a.agreement_endday >= '{$day}'
                AND a.agreement_status = '1'
                AND a.company_id = '{$company_id}'
                GROUP BY
                    t.course_id";
        $pricingOne=$this->DataControl->selectOne($sql);
        if($pricingOne){
            return $pricingOne;
        }else{
            return array();
        }
    }

    function getStuDiscountPrice($request,$student_id,$course_id){
        $day=date("Y-m-d",time());
        $sql="SELECT
                    fpc.*
                FROM
                    smc_fee_policy_course AS fpc
                LEFT JOIN smc_fee_policy_student AS fps ON fps.policy_id = fpc.policy_id
                AND fps.student_id = '{$student_id}'
                LEFT JOIN smc_fee_policy AS fp ON fp.policy_id = fpc.policy_id
                WHERE
                    fp.policy_status = '1'
                AND fp.policy_startday<='{$day}'
                AND fp.policy_endday>='{$day}'
                AND fp.company_id = '{$request['company_id']}'
                AND fpc.to_course_id = '{$course_id}'
                AND fps.student_id='{$student_id}'
                AND fpc.from_course_id IN (
                    SELECT
                        c.course_id
                    FROM
                        smc_student_study AS ss
                    LEFT JOIN smc_class AS c ON c.class_id = ss.class_id
                    where ss.student_id='{$student_id}' and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}'
                )
                order by sell_unitprice asc limit 0,1
        ";
        $policyOne=$this->DataControl->selectOne($sql);


        if(!$policyOne){
            $this->error = true;
            $this->errortip = "无优惠政策";
            return false;
        }
        $courseOne=$this->DataControl->getFieldOne("smc_course","course_classnum","course_id='{$course_id}'");
        $data=array();
        $data['tuition_originalprice']=ceil($courseOne['course_classnum']*$policyOne['norm_unitprice']);
        $data['tuition_sellingprice']=ceil($courseOne['course_classnum']*$policyOne['sell_unitprice']);
        $data['unitexpend']=$policyOne['sell_unitprice'];
        $data['unitrefund']=$policyOne['norm_unitprice'];
        $data['full_price']=$policyOne['full_price'];
        $data['policy_id']=$policyOne['policy_id'];
        return $data;
    }

    function getStuLabelPolicy($request,$student_id,$applytype_branch,$price){
        $day=date("Y-m-d",time());
        $sql="select fp.*
              from smc_fee_policy as fp
              left join smc_fee_policy_student as fps on fps.policy_id=fp.policy_id
              where fps.student_id='{$student_id}' and fp.applytype_branch='{$applytype_branch}'
              and fp.company_id='{$request['company_id']}' and fp.policy_status='1' and fp.policy_class='1'
              and fp.policy_price>='{$price}' and fp.policy_startday<='{$day}' and fp.policy_endday>='{$day}' and (fp.policy_trial_num='0' or (fp.policy_trial_num>fps.trial_free_num))
              order by (case when fp.policy_trial_num='0' then 1 else 2 end),fp.policy_trial_num desc,fps.trial_free_num desc,fp.policy_createtime asc limit 0,1
        ";
        $policyOne=$this->DataControl->selectOne($sql);

        if(!$policyOne){
            $this->error = true;
            $this->errortip = "学员无可用标签政策";
            return false;
        }

        return $policyOne;
    }

    public function addSmcWorkLog($company_id,$school_id,$staffer_id,$module,$type,$content,$module_id=0)
    {
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");

        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['school_id'] = $school_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('smc_staffer_worklog', $logData);
    }

    function getParentToken($params=array()){
        $parenterOne = $this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_tokencode,parenter_tokenencrypt","parenter_id='{$params['parenter_id']}'");
        if(!$parenterOne)
        {
            return false;
        }
        $md5tokenbar = base64_encode(md5($parenterOne["parenter_tokencode"].date("Y-m-d")));
        if($md5tokenbar == $parenterOne["parenter_tokenencrypt"]){
            $token = $parenterOne["parenter_tokenencrypt"];
        }else{
            $tokencode = rand(111111,999999);
            $md5tokenbar = base64_encode(md5($tokencode.date("Y-m-d")));
            $this->DataControl->query("UPDATE smc_parenter SET parenter_tokencode = '{$tokencode}',parenter_tokenencrypt = '{$md5tokenbar}' WHERE parenter_id ='{$parenterOne['parenter_id']}'");
            $token = $md5tokenbar;
        }
        return $token;
    }

    function getTree($arr = array(), $pk = 'id', $upid = 'pid', $child = 'child')
    {
        $items = array();
        if(is_array($arr)){
            foreach ($arr as $val) {
                $items[$val[$pk]] = $val;
            }
        }
        $tree = array();
        if(is_array($items)) {
            foreach ($items as $k => $val) {
                if (isset($items[$val[$upid]])) {
                    $items[$val[$upid]][$child][] =& $items[$k];
                } else {
                    $tree[] = &$items[$k];
                }
            }
        }
        return $tree;
    }

    function checkMobile($strMobile)
    {
        $pattern = "/^(1(\d{10}))|(09(\d{8}))$/";

        if (preg_match($pattern, $strMobile)){
            return true;
        }else{
            return false;
        }
    }

    function getAge($birthday){
        //格式化出生时间年月日
        $byear=date('Y',strtotime($birthday));
        $bmonth=date('m',strtotime($birthday));
        $bday=date('d',strtotime($birthday));

        //格式化当前时间年月日
        $tyear=date('Y');
        $tmonth=date('m');
        $tday=date('d');

        //开始计算年龄
        $age=$tyear-$byear;
        if($bmonth>$tmonth || $bmonth==$tmonth && $bday>$tday){
            $age--;
        }
        return $age;
    }

    function getContract($company_id){
        $sql = "select sc.edition_id,ie.edition_code
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$company_id}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

        $contractOne = $this->DataControl->selectOne($sql);

        return $contractOne;
    }

    //获取 奇趣 接口授权秘钥
    function getQiquAuthpriv(){
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'KddGmcUser';
        $parameter['company_id'] = '8888';

        $aeskey = 'KddGmc2QiQu%15tH';
        $aesiv = 'jdb2GmcWGsdffp3g';

        $aes = new \Aesencdec($aeskey, $aesiv);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;
        return $result;
    }

}
