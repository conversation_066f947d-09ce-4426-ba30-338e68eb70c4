<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Smc;

class  StockModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //获取货品列表
    function getGoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                r.repertory_id,
                g.goods_cnname,
                g.goods_pid,
                g.goods_id,
                r.goods_repertory,
                g.goods_vipprice,
                g.goods_originalprice
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
            WHERE
                {$datawhere} and r.school_id = '{$paramArray['school_id']}'
            ORDER BY
                r.repertory_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(r.repertory_id) as a
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
            WHERE
                {$datawhere} and r.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_originalprice', 'goods_vipprice', 'goods_repertory');
        $fieldname = array('K3货号', '中文名称', '成本价', '协议价', '库存数');
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['allnum'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无库存信息", 'result' => $result);
        }

        return $res;
    }

    //查看货品详情
    function getGoodsDetail($paramArray)
    {
//        g.pathways_id,
        $sql = "
            SELECT
                g.goods_id,
                g.goods_cnname,
                g.goods_enname,
                g.goods_pid,
                g.goods_specifica,
                g.goods_suppliername,
                g.goods_unit,
                g.prodtype_code,
                g.goods_barcode,
                g.goods_issale,
                g.goods_originalprice,
                g.goods_vipprice,
                r.goods_repertory,
                p.prodtype_name
            FROM
                erp_goods AS g left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
                left join smc_erp_goods_repertory as r on r.goods_id = g.goods_id and r.school_id = '{$paramArray['school_id']}'
            WHERE
                g.goods_id = '{$paramArray['goods_id']}'";
        $goodsDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field['company_id'] = $this->LgStringSwitch("所属公司");
        $field['goods_cnname'] = $this->LgStringSwitch("中文名");
        $field['goods_enname'] = $this->LgStringSwitch("英文名");
        $field['goods_pid'] = $this->LgStringSwitch("编号");
        $field['goods_specifica'] = $this->LgStringSwitch("规格");
        $field['goods_suppliername'] = $this->LgStringSwitch("品牌");
        $field['goods_unit'] = $this->LgStringSwitch("单位");
        $field['prodtype_id'] = $this->LgStringSwitch("类别");
        $field['goods_barcode'] = $this->LgStringSwitch("条形码");
        $field['goods_issale'] = $this->LgStringSwitch("是否上架");
        $field['goods_originalprice'] = $this->LgStringSwitch("市场价");
        $field['goods_vipprice'] = $this->LgStringSwitch("协议价");
        $field['goods_createtime'] = $this->LgStringSwitch("创建时间");

        $result = array();
        if ($goodsDetail) {
            $result["field"] = $field;
            $result["data"] = $goodsDetail;
            $res = array('error' => '0', 'errortip' => '获取货品详情成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取账号资料失败', 'result' => $result);
        }
        return $res;
    }

    //获取货品价格变更列表
    function getGoodsPriceList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and from_unixtime(g.pricelog_updatatime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and from_unixtime(g.pricelog_updatatime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }

        $sql = "
            SELECT
              g.pricelog_id,
              g.pricelog_oriprice,
              g.pricelog_price,
              from_unixtime(g.pricelog_updatatime,'%Y-%m-%d') as pricelog_updatatime
            FROM
              erp_goods_pricelog AS g
            WHERE
                {$datawhere} and g.company_id = '{$paramArray['company_id']}' and goods_id = '{$paramArray['goods_id']}'
            ORDER BY
                g.goods_id DESC";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('pricelog_updatatime ', 'pricelog_oriprice', 'pricelog_price');
        $fieldname = array('生效时间', '市场价', '协议价');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无价格变更信息", 'result' => $result);
        }

        return $res;
    }

    //获取新增采购商品列表
    function getBuyGoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_enname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%' or g.goods_suppliername like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code = '{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_id,
                g.goods_outpid,
                g.goods_cnname,
                g.goods_enname,
                g.goods_originalprice,
                g.goods_vipprice,
                p.prodtype_name
            FROM
                erp_goods AS g left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
            WHERE
                {$datawhere} and g.company_id = '{$paramArray['company_id']}' and g.goods_issale = '1'
            ORDER BY
                g.goods_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['goods_number'] = '1';
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(g.goods_id) as a
            FROM
                erp_goods AS g
            WHERE
                {$datawhere} and g.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_outpid ', 'goods_cnname', 'goods_enname', 'prodtype_name', 'goods_vipprice', 'goods_originalprice');
        $fieldname = array('K3货号', '中文名称', '英文名称', '货品类别', '采购价格', '学员售价');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_id,prodtype_name,prodtype_code from smc_code_prodtype where company_id = '{$paramArray['company_id']}'");


        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        if (!$goodsList) {

        }

        return $res;
    }

    //创建订单
    function CreateProorderAction($paramArray)
    {
        $postbe = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "staffer_id = '{$paramArray['staffer_id']}' and school_id = '{$paramArray['school_id']}'");
        $topjob = $this->DataControl->getFieldOne("gmc_company_post", "post_istopjob", "post_id = '{$postbe['post_id']}'");
        if ($topjob['post_istopjob'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => "没有权限!"), $this->companyOne['company_language']);
        }
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['proorder_pid'] = $this->createOrderPid('CG');
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['to_school_id'] = $paramArray['school_id'];
//        $data['orderclass_id'] = '4';
//        $data['erpstatus_id'] = '21';
        $data['proorder_status'] = '0';
        if ($paramArray['status'] == '0') {
            $data['proorder_outpid'] = $paramArray['pid'];
            $data['proorder_from'] = '0';
        } elseif ($paramArray['status'] == '1') {
            $data['proorder_outpid'] = $paramArray['pid'];
            $data['proorder_from'] = '1';
            $data['proorder_orderbuydate'] = $paramArray['proorder_orderbuydate'];
        } elseif ($paramArray['status'] == '2') {
            $data['proorder_outpid'] = $paramArray['pid'];
            $data['activitybuy_id'] = $paramArray['activitybuy_id'];
            $data['proorder_from'] = '2';
        }

        $data['proorder_ispay'] = '0';
        $data['proorder_allprice'] = $paramArray['allprice'];
        $data['proorder_createtime'] = time();

        $field = array();
        $field['company_id'] = $this->LgStringSwitch("所属公司");
        $field['proorder_pid'] = $this->LgStringSwitch("订单号");
        $field['staffer_id'] = $this->LgStringSwitch("下单操作员工ID");
        $field['proorder_allprice'] = $this->LgStringSwitch("订单价格");
        $field['proorder_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('erp_proorder', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['proogoods_buynums'] = $item['proogoods_buynums'];
                $datas['proogoods_unitprice'] = $item['goods_vipprice'];
                $datas['proogoods_payprice'] = $item['goods_vipprice'] * $item['proogoods_buynums'];
                $datas['proorder_pid'] = $data['proorder_pid'];

                $this->DataControl->insertData('erp_proorder_goods', $datas);
            }

            $dataSale = array();
            $dataSale['company_id'] = $paramArray['company_id'];
            $dataSale['salesorder_pid'] = $this->createOrderPid('XH');
            $dataSale['to_school_id'] = $paramArray['school_id'];
            $dataSale['proorder_pid'] = $data['proorder_pid'];
            if ($paramArray['status'] == '0') {
                $dataSale['salesorder_from'] = '4';
            } elseif ($paramArray['status'] == '1') {
                $dataSale['salesorder_from'] = '6';
            } elseif ($paramArray['status'] == '2') {
                $dataSale['salesorder_from'] = '5';
            }
            $dataSale['salesorder_createtime'] = time();

            $this->DataControl->insertData('smc_erp_salesorder', $dataSale);

            $datas = array();

            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['salesorder_pid'] = $dataSale['salesorder_pid'];
                $datas['salesordergoods_buynums'] = $item['proogoods_buynums'];
                $datas['proorder_pid'] = $data['proorder_pid'];
                $this->DataControl->insertData('smc_erp_salesorder_goods', $datas);
            }

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成订单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('采购生成订单成功');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['proorder_pid'] = $data['proorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('审核订单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('采购订单审核通过');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['proorder_pid'] = $data['proorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成销货单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('--');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['salesorder_pid'] = $dataSale['salesorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

            $res = array('error' => '0', 'errortip' => "创建订单成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "创建订单失败", 'result' => $result);
        }
        return $res;
    }

    //创建订单(旧)
    function CreateProorderOldAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['proorder_pid'] = $this->createOrderPid('CG');
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['to_school_id'] = $paramArray['school_id'];
//        $data['orderclass_id'] = '4';
//        $data['erpstatus_id'] = '21';
        $data['proorder_status'] = '0';
        if ($paramArray['status'] == '0') {
            $data['proorder_outpid'] = $paramArray['pid'];
            $data['proorder_from'] = '0';
        } elseif ($paramArray['status'] == '1') {
            $data['proorder_outpid'] = $paramArray['pid'];
            $data['proorder_from'] = '1';
            $data['proorder_orderbuydate'] = $paramArray['proorder_orderbuydate'];
        } elseif ($paramArray['status'] == '2') {
            $data['proorder_outpid'] = $paramArray['pid'];
            $data['activitybuy_id'] = $paramArray['activitybuy_id'];
            $data['proorder_from'] = '2';
        }

        $data['proorder_ispay'] = '0';
        $data['proorder_allprice'] = $paramArray['allprice'];
        $data['proorder_createtime'] = time();

        $field = array();
        $field['company_id'] = $this->LgStringSwitch("所属公司");
        $field['proorder_pid'] = $this->LgStringSwitch("订单号");
        $field['staffer_id'] = $this->LgStringSwitch("下单操作员工ID");
        $field['proorder_allprice'] = $this->LgStringSwitch("订单价格");
        $field['proorder_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('erp_proorder', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['proogoods_buynums'] = $item['proogoods_buynums'];
                $datas['proogoods_unitprice'] = $item['goods_vipprice'];
                $datas['proogoods_payprice'] = $item['goods_vipprice'] * $item['proogoods_buynums'];
                $datas['proorder_pid'] = $data['proorder_pid'];

                $this->DataControl->insertData('erp_proorder_goods', $datas);
            }

            $info = "<?xml version='1.0'?>
                     <ROOT>
                         <BILL>
                             <FBrNo>01102</FBrNo>
                             <FBillNo>{$data['proorder_pid']}</FBillNo>
                             <FCustNumber>D.0007001</FCustNumber>
                             <FCustName>上海圆桌福山校</FCustName>
                             <FBillDate>2019-07-09</FBillDate>
                             <FNote>订单备注</FNote>";

            $str = '';
            foreach ($goodsList as $val) {
                $pid = $this->DataControl->getFieldOne("erp_goods", "goods_outpid", "goods_id = '{$val['goods_id']}'");
                $str .= "<BILLDETAIL>
                                 <FItemNumber>{$pid['goods_outpid']}</FItemNumber>
                                 <FItemName>1</FItemName>
                                 <FQty>{$val['proogoods_buynums']}</FQty>
                                 <FPrice>1</FPrice>
                                 <FAuxPrice>1</FAuxPrice>
                                 <FAmount>1</FAmount>
                                 <FAmountIncludeTax>1</FAmountIncludeTax>
                                 <FTaxRate>1</FTaxRate>
                                 <FTaxAmount>1</FTaxAmount>
                             </BILLDETAIL>";
            }

            $info .= $str;
            $info .= "</BILL>
                 </ROOT>";

            request_by_curl("http://47.96.36.32:55520/jdbbillwebservicetest/webservice1.asmx/MakeSaleBills", "info={$this->LgStringSwitch($info)}", "POST", array());

            $datass = array();
            $datass['company_id'] = $paramArray['company_id'];
            $datass['school_id'] = $paramArray['school_id'];
            $datass['proorder_pid'] = $data['proorder_pid'];

            $this->DataControl->insertData('erp_proorder_to', $datass);

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成订单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('采购生成订单成功');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['proorder_pid'] = $data['proorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

            $res = array('error' => '0', 'errortip' => "创建订单成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "创建订单失败", 'result' => $result);
        }
        return $res;
    }

    //借调审核
    function transferbuyAction($paramArray)
    {
        $postbe = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "staffer_id = '{$paramArray['staffer_id']}' and school_id = '{$paramArray['school_id']}'");
        $topjob = $this->DataControl->getFieldOne("gmc_company_post", "post_istopjob", "post_id = '{$postbe['post_id']}'");
        if ($topjob['post_istopjob'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => "没有权限!"), $this->companyOne['company_language']);
        }
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['proorder_pid'] = $this->createOrderPid('JD');
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['to_school_id'] = $paramArray['school_id'];
        $data['from_school_id'] = $paramArray['from_school_id'];
        $data['proorder_outpid'] = $paramArray['pid'];
        $data['proorder_status'] = '0';
        $data['proorder_from'] = '4';
        $data['proorder_ispay'] = '0';
        $data['proorder_allprice'] = $paramArray['allprice'];
        $data['proorder_returndate'] = $paramArray['proorder_returndate'];
        $data['proorder_createtime'] = time();

        if ($id = $this->DataControl->insertData('erp_proorder', $data)) {
            $result = array();
            $result["data"] = $data;

            $datas = array();

            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['proogoods_buynums'] = $item['proogoods_buynums'];
                $datas['proogoods_unitprice'] = $item['goods_vipprice'];
                $datas['proogoods_payprice'] = $item['goods_vipprice'] * $item['proogoods_buynums'];
                $datas['proorder_pid'] = $data['proorder_pid'];

                $this->DataControl->insertData('erp_proorder_goods', $datas);
            }

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

            $dataTracks = array();
            $dataTracks['tracks_title'] = '生成订单';
            $dataTracks['tracks_information'] = '借调生成订单成功';
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['proorder_pid'] = $data['proorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);


            $dataSale = array();
            $dataSale['company_id'] = $paramArray['company_id'];
            $dataSale['salesorder_pid'] = $this->createOrderPid('XH');
            $dataSale['school_id'] = $paramArray['from_school_id'];
            $dataSale['to_school_id'] = $paramArray['school_id'];
            $dataSale['proorder_pid'] = $data['proorder_pid'];
            $dataSale['salesorder_from'] = '1';
            $dataSale['salesorder_createtime'] = time();

            $this->DataControl->insertData('smc_erp_salesorder', $dataSale);

            $datas = array();

            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['salesorder_pid'] = $dataSale['salesorder_pid'];
                $datas['salesordergoods_buynums'] = $item['proogoods_buynums'];
                $datas['proorder_pid'] = $data['proorder_pid'];
                $this->DataControl->insertData('smc_erp_salesorder_goods', $datas);
            }

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成销货单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('--');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['salesorder_pid'] = $dataSale['salesorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

            $res = array('error' => '0', 'errortip' => "创建借调订单成功");
        } else {
            $res = array('error' => '1', 'errortip' => "创建借调订单失败");
        }
        return $res;
    }

    //采购管理列表
    function getProGoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.proorder_pid like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['month']) && $paramArray['month'] !== "") {
            $datawhere .= " and p.proorder_orderbuydate = '{$paramArray['month']}'";
        }
        if (isset($paramArray['proorder_from']) && $paramArray['proorder_from'] !== "") {
            $datawhere .= " and p.proorder_from ={$paramArray['proorder_from']}";
        }
        if (isset($paramArray['from_school_id']) && $paramArray['from_school_id'] !== "") {
            $datawhere .= " and p.from_school_id ={$paramArray['from_school_id']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['proorder_status']) && $paramArray['proorder_status'] !== "") {
            $datawhere .= " and p.proorder_status ='{$paramArray['proorder_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.proorder_id,
                p.proorder_pid,
                p.proorder_from,
                ( SELECT sum( proogoods_buynums ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = p.proorder_pid ) AS nums,
                ( SELECT sum( proogoods_payprice ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = p.proorder_pid ) AS price,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END)) as staffer_cnname,
                sc.school_cnname,
                sc.school_shortname,
                p.proorder_status,
                p.proorder_status as proorder_status_num,
                a.activitybuy_name,
                p.proorder_orderbuydate,
                p.proorder_returndate,
                scc.school_shortname as from_school_shortname
            FROM
                erp_proorder AS p
                LEFT JOIN smc_school AS sc ON sc.school_id = p.to_school_id
                left join smc_school as scc on scc.school_id = p.from_school_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id 
                left join gmc_company_activitybuy as a on a.activitybuy_id = p.activitybuy_id
            WHERE {$datawhere} and p.company_id = '{$paramArray['company_id']}' and p.to_school_id = '{$paramArray['school_id']}'
            ORDER BY
                p.proorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购", "3" => "调拨", "4" => "借调"));
            $statuss = $this->LgArraySwitch(array("-1" => "已取消", "0" => "待发货", "1" => "待出库", "2" => "待入库", "3" => "已完成", "4" => "待归还", "5" => "归还待出库", "6" => "归还待入库"));
            foreach ($goodsList as &$val) {
                $val['proorder_from'] = $status[$val['proorder_from']];
                $val['proorder_status'] = $statuss[$val['proorder_status']];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.proorder_id) as a
            FROM
                erp_proorder AS p
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']} and p.to_school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        if ($paramArray['proorder_from'] == '2') {
            $fieldstring = array('proorder_pid', 'proorder_from', 'activitybuy_name', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'proorder_status');
            $fieldname = array('采购编号', '采购类型', '活动名称', '申请采购数量', '采购金额总计', '采购申请日期', '采购人', '状态');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        } elseif ($paramArray['proorder_from'] == '1') {
            $fieldstring = array('proorder_pid', 'proorder_from', 'proorder_orderbuydate', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'proorder_status');
            $fieldname = array('采购编号', '采购类型', '预估采购月份', '申请采购数量', '采购金额总计', '采购申请日期', '采购人', '状态');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        } elseif ($paramArray['proorder_from'] == '4') {
            $fieldstring = array('proorder_pid', 'proorder_from', 'school_shortname', 'from_school_shortname', 'nums', 'price', 'proorder_createtime', 'proorder_returndate', 'staffer_cnname', 'proorder_status');
            $fieldname = array('采购编号', '采购类型', '借入校', '借出校', '借调数量', '采购金额总计', '借调申请日期', '预计归还日期', '采购人', '状态');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        } elseif ($paramArray['proorder_from'] == '3') {
            $fieldstring = array('proorder_pid', 'proorder_from', 'school_shortname', 'from_school_shortname', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'proorder_status');
            $fieldname = array('采购编号', '采购类型', '调入校', '调出校', '调拨数量', '采购金额总计', '调拨申请日期', '采购人', '状态');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        } else {
            $fieldstring = array('proorder_pid', 'proorder_from', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'proorder_status');
            $fieldname = array('采购编号', '采购类型', '申请采购数量', '采购金额总计', '采购申请日期', '采购人', '状态');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['allnum'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无采购信息", 'result' => $result);
        }

        return $res;
    }

    //学校采购管理审核
    function ExamineScAction($paramArray)
    {
        $proorderOne = $this->DataControl->getFieldOne("erp_proorder", "proorder_pid,erpstatus_id", "proorder_id = '{$paramArray['proorder_id']}'");
        if ($proorderOne) {
            $data = array();
            if ($proorderOne['erpstatus_id'] == '24') {
                $data['erpstatus_id'] = '25';
            } elseif ($proorderOne['erpstatus_id'] == '25') {
                $data['erpstatus_id'] = '27';
            } elseif ($proorderOne['erpstatus_id'] == '28') {
                $data['erpstatus_id'] = '29';
            }

            $field = array();
            $field['erpstatus_id'] = $this->LgStringSwitch("ERP订单最新状态ID");

            if ($this->DataControl->updateData("erp_proorder", "proorder_id = '{$paramArray['proorder_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;

                if ($proorderOne['erpstatus_id'] == '25' || $proorderOne['erpstatus_id'] == '28') {
                    $goods = $this->DataControl->getFieldOne("erp_proorder_goods", "goods_id,proogoods_buynums", "proorder_pid = '{$proorderOne['proorder_pid']}'");
                    $repertory = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$goods['goods_id']}'");
                    $data = array();
                    $data['goods_repertory'] = $repertory['goods_repertory'] - $goods['proogoods_buynums'];
                    $this->DataControl->updateData("smc_erp_goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$goods['goods_id']}'", $data);
                }

                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

                $dataTracks = array();
                $dataTracks['tracks_title'] = $this->LgStringSwitch('审核订单');
                $dataTracks['tracks_information'] = '';
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['proorder_pid'] = $proorderOne['proorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

                $res = array('error' => '0', 'errortip' => "订单最新状态修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '订单最新状态修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //采购货品明细
    function getGoodsDetailList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.goods_pid like '%{$paramArray['keyword']}%' or s.goods_cnname like '%{$paramArray['keyword']}%' or s.goods_enname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and s.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.goods_id,
                s.goods_outpid,
                s.goods_pid,
                s.goods_unit,
                s.goods_vipprice,
                s.goods_cnname,
                s.goods_enname,
                p.prodtype_name,
                g.proogoods_buynums,
                g.proogoods_unitprice,
                g.proogoods_id,
                r.erpstatus_id,
                (select sum(bg.beinordergoods_buynums) from smc_erp_beinorder as b left join smc_erp_beinorder_goods as bg on bg.beinorder_pid = b.beinorder_pid where b.proorder_pid = r.proorder_pid and b.beinorder_status = '2' and bg.goods_id = s.goods_id) as num
            FROM
                erp_proorder_goods AS g
                LEFT JOIN erp_proorder_to AS t ON g.proorder_pid = t.proorder_pid
                left join erp_goods as s on g.goods_id = s.goods_id
                left join smc_code_prodtype as p on s.prodtype_code = p.prodtype_code and p.company_id = '{$paramArray['company_id']}'
                left join erp_proorder as r on r.proorder_pid = g.proorder_pid
                left join smc_erp_beinorder_goods as bg on bg.proorder_pid = r.proorder_pid
            WHERE {$datawhere} and g.proorder_pid = '{$paramArray['proorder_pid']}'
            ORDER BY
                s.goods_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        foreach ($goodsList as &$val) {
            $val['allprice'] = $val['proogoods_buynums'] * $val['goods_vipprice'];
            if (!$val['num']) {
                $val['num'] = '0';
            }
        }


        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(g.proogoods_id) as a
            FROM
                erp_proorder_goods AS g
                LEFT JOIN erp_proorder_to AS t ON g.proorder_pid = t.proorder_pid
                left join erp_goods as s on g.goods_id = s.goods_id
                left join smc_code_prodtype as p on s.prodtype_code = p.prodtype_code
            WHERE {$datawhere} and g.proorder_pid = '{$paramArray['proorder_pid']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'prodtype_name', 'goods_unit', 'goods_vipprice', 'proogoods_buynums', 'allprice', 'num');
        $fieldname = array('K3货号', '货品名称', '货品类别', '单位', '价格', '采购数量', '金额小计', '入库数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        $isInput = array("0", "0", "0", "0", "1", "0");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isInput"] = trim($isInput[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }


    //采购单跟踪记录
    function getGoodsTracksList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.tracks_title like '%{$paramArray['keyword']}%' or s.tracks_information like '%{$paramArray['keyword']}%' or s.tracks_playname like '%{$paramArray['keyword']}%')";
        }
        $sql = "
            SELECT
                s.tracks_title,
                s.tracks_information,
                s.tracks_playname,
                FROM_UNIXTIME(s.tracks_createtime,'%Y-%m-%d %H:%i:%s') as tracks_createtime
            FROM
                erp_proorder_tracks AS s
            WHERE {$datawhere} and s.proorder_pid = '{$paramArray['proorder_pid']}'
            ORDER BY
                s.tracks_id DESC";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT count(tracks_title) as a
            FROM erp_proorder_tracks as s
            WHERE {$datawhere} and proorder_pid = '{$paramArray['proorder_pid']}'
            ORDER BY tracks_id DESC");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('tracks_title ', 'tracks_playname', 'tracks_information', 'tracks_createtime');
        $fieldname = array('操作内容', '操作人', '备注', '操作时间');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }
        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //采购货品明细
    function getGoodsDetailOne($paramArray)
    {
        $sql = "
            SELECT
                p.proorder_pid,
                p.proorder_from,
                p.proorder_from as proorder_from_num,
                sum( g.proogoods_buynums ) AS proogoods_buynums,
                sum( g.proogoods_payprice ) AS proogoods_payprice,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ) ) as staffer_cnname,
                s.school_shortname as from_school_cnname,
                sc.school_shortname as to_school_cnname,
                a.activitybuy_name,
                p.proorder_orderbuydate,
                (select sum(bg.beinordergoods_buynums) from smc_erp_beinorder as b left join smc_erp_beinorder_goods as bg on b.beinorder_pid = bg.beinorder_pid where b.proorder_pid = p.proorder_pid and b.beinorder_status = '2') as innum
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_goods AS g ON p.proorder_pid = g.proorder_pid 
                left join smc_staffer as st on st.staffer_id = p.staffer_id
                left join smc_school as s on p.from_school_id = s.school_id
                left join smc_school as sc on p.to_school_id = sc.school_id
                left join gmc_company_activitybuy as a on a.activitybuy_id = p.activitybuy_id
            WHERE
                p.proorder_pid = '{$paramArray['proorder_pid']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        if ($stafferDetail) {
            $status = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购", "3" => "调拨", "4" => "借调"));
            foreach ($stafferDetail as &$val) {
                $val['proorder_from'] = $status[$val['proorder_from']];
            }

        }


        $field = array();
        $field["proorder_pid"] = $this->LgStringSwitch("采购编号");
        $field["proorder_from"] = $this->LgStringSwitch("采购类型");
        $field["proogoods_buynums"] = $this->LgStringSwitch("申请采购货品数量");
        $field["proogoods_payprice"] = $this->LgStringSwitch("申请采购金额");
        $field["proorder_createtime"] = $this->LgStringSwitch("采购日期");
        $field["proorder_orderbuydate"] = $this->LgStringSwitch("预估采购申请日期");
        $field["from_school_cnname"] = $this->LgStringSwitch("借调来源学校");
        $field["to_school_cnname"] = $this->LgStringSwitch("借调去向学校");
        $field["activitybuy_name"] = $this->LgStringSwitch("活动名称");
        $field["staffer_cnname"] = $this->LgStringSwitch("采购人");

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取采购货品明细成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取采购货品明细失败', 'result' => $result);
        }
        return $res;
    }

    //删除采购货品
    function DelGoodsAction($paramArray)
    {
        $GoodsOne = $this->DataControl->getFieldOne("erp_proorder_goods", "proogoods_id", "proogoods_id = '{$paramArray['proogoods_id']}'");
        if ($GoodsOne) {
            if ($this->DataControl->delData("erp_proorder_goods", "proogoods_id = '{$paramArray['proogoods_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除采购货品成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除采购货品失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //新增采购物品
    function addGoodsAction($paramArray)
    {
        $data = array();

        $goodsList = json_decode(stripslashes($paramArray['goods']), true);
        foreach ($goodsList as $item) {
            $data['goods_id'] = $item['goods_id'];
            $data['proogoods_buynums'] = '1';
            $data['proogoods_unitprice'] = $item['goods_vipprice'];
            $data['proogoods_payprice'] = $item['goods_vipprice'] * $data['proogoods_buynums'];
            $data['proorder_pid'] = $item['proorder_pid'];

            $a = $this->DataControl->getFieldOne('erp_proorder_goods', 'goods_id', "goods_id = '{$item['goods_id']}' and proorder_pid = '{$item['proorder_pid']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
            }
            $this->DataControl->insertData('erp_proorder_goods', $data);
        }

        $res = array('error' => '0', 'errortip' => "新增采购物品成功", 'result' => array());

        return $res;
    }

    //入库单
    function getBeinorderList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (b.beinorder_pid like '%{$paramArray['keyword']}%' or b.proorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['proorder_from']) && $paramArray['proorder_from'] !== "") {
            $datawhere .= " and p.proorder_from ={$paramArray['proorder_from']}";
        }
        if (isset($paramArray['beinorder_status']) && $paramArray['beinorder_status'] !== "") {
            $datawhere .= " and b.beinorder_status ={$paramArray['beinorder_status']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( b.beinorder_storagetime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( b.beinorder_storagetime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                b.beinorder_pid,
                b.beinorder_from,
                b.beinorder_outpid,
                b.beinorder_status,
                b.beoutorder_pid,
                b.beinorder_status as beinorder_status_name,
                t.tracks_playname,
	            b.proorder_pid,
	            (select sum(beinordergoods_buynums) from smc_erp_beinorder_goods as bg where bg.beinorder_pid = b.beinorder_pid) as innum,
	            (select sum(proogoods_buynums) from erp_proorder_goods as pg where pg.proorder_pid = b.proorder_pid) as buynum,
	            p.proorder_from,
	            FROM_UNIXTIME( b.beinorder_storagetime, '%Y-%m-%d' ) as beinorder_storagetime
            FROM
                smc_erp_beinorder AS b 
                left join smc_erp_beinorder_tracks as t on t.beinorder_pid = b.beinorder_pid
               	left join smc_erp_beinorder_goods as g on g.beinorder_pid = b.beinorder_pid
               	left join erp_proorder as p on p.proorder_pid = b.proorder_pid
            WHERE {$datawhere} and b.school_id = {$paramArray['school_id']}
            GROUP BY b.beinorder_pid
            ORDER BY
                b.beinorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $beinorderList = $this->DataControl->selectClear($sql);

        if ($beinorderList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待入库", "2" => "已入库"));
            $statuss = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购", "3" => "调拨", "4" => "借调"));
            foreach ($beinorderList as &$val) {
                $val['beinorder_status_name'] = $status[$val['beinorder_status_name']];
                $val['proorder_from'] = $statuss[$val['proorder_from']];
                if ($val['beinorder_storagetime'] == '1970-01-01') {
                    $val['beinorder_storagetime'] = '--';
                }
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(*) as a
            FROM
                (SELECT
                b.beinorder_pid,
                b.beinorder_from,
                b.beinorder_outpid,
                b.beinorder_status,
                b.beoutorder_pid,
                b.beinorder_status as beinorder_status_name,
                t.tracks_playname,
	            b.proorder_pid,
	            (select sum(beinordergoods_buynums) from smc_erp_beinorder_goods as bg where bg.beinorder_pid = b.beinorder_pid) as innum,
	            (select sum(proogoods_buynums) from erp_proorder_goods as pg where pg.proorder_pid = b.proorder_pid) as buynum,
	            p.proorder_from,
	            FROM_UNIXTIME( b.beinorder_storagetime, '%Y-%m-%d' ) as beinorder_storagetime
            FROM
                smc_erp_beinorder AS b 
                left join smc_erp_beinorder_tracks as t on t.beinorder_pid = b.beinorder_pid
               	left join smc_erp_beinorder_goods as g on g.beinorder_pid = b.beinorder_pid
               	left join erp_proorder as p on p.proorder_pid = b.proorder_pid
            WHERE {$datawhere} and b.school_id = {$paramArray['school_id']}
            GROUP BY b.beinorder_pid) as aa");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('beinorder_pid ', 'innum ', 'proorder_pid', 'proorder_from', 'buynum', 'beinorder_status_name', 'beinorder_outpid', 'beinorder_storagetime');
        $fieldname = array('入库单号', '入库货品数量', '采购单号', '采购单类型', '采购货品数量', '状态', '外部对接编号', '入库日期');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($beinorderList) {
            $result['list'] = $beinorderList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        if (!$beinorderList) {
            $res = array('error' => '1', 'errortip' => "暂无入库单信息", 'result' => $result);
        }

        return $res;
    }

    //入库单商品明细
    function getbeinorderGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.goods_cnname like '%{$paramArray['keyword']}%' or s.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and s.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                g.beinorder_pid,
                g.beinordergoods_buynums,
                s.goods_cnname,
                s.goods_outpid,
                s.goods_pid,
                s.goods_unit,
                s.goods_vipprice,
                p.prodtype_name,
                (select proogoods_buynums from erp_proorder_goods as pg where pg.proorder_pid = b.proorder_pid and pg.goods_id = g.goods_id) as proogoods_buynums
            FROM
                smc_erp_beinorder_goods AS g
                left join erp_goods as s on g.goods_id = s.goods_id
                LEFT JOIN smc_code_prodtype as p on p.prodtype_code = s.prodtype_code and p.company_id = '{$paramArray['company_id']}'
                left join smc_erp_beinorder as b on b.beinorder_pid = g.beinorder_pid
            WHERE {$datawhere} and g.beinorder_pid = '{$paramArray['beinorder_pid']}'
            ORDER BY
                g.beinordergoods_id DESC 
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = count($goodsList);


        $fieldstring = array('goods_pid ', 'goods_cnname', 'prodtype_name', 'goods_unit', 'goods_vipprice', 'proogoods_buynums', 'beinordergoods_buynums');
        $fieldname = array('货品编号', '货品名称', '货品类别', '单位', '价格', '采购数量', '本次入库数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
            $result['all_num'] = $all_num;
        } else {
            $result['list'] = array();
            $result['all_num'] = 0;
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //入库单资料
    function getBeinorderInfo($paramArray)
    {
        $sql = "
            SELECT
                b.beinorder_pid,
                b.beinorder_from,
                b.beinorder_status,
                b.proorder_pid,
                b.beinorder_status AS beinorder_status_name,
                FROM_UNIXTIME( b.beinorder_createtime, '%Y-%m-%d' ) AS beinorder_createtime,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                ( SELECT sum( sg.salesordergoods_buynums ) FROM smc_erp_salesorder_goods AS sg WHERE sg.salesorder_pid = o.salesorder_pid ) AS buynums,
                ( SELECT sum( g.beinordergoods_buynums ) FROM smc_erp_beinorder_goods AS g WHERE g.beinorder_pid = b.beinorder_pid ) AS nums 
            FROM
                smc_erp_beinorder AS b
                LEFT JOIN erp_proorder AS p ON b.proorder_pid = p.proorder_pid 
                left join smc_erp_beoutorder as o on b.beoutorder_pid = o.beoutorder_pid
            WHERE
                b.beinorder_pid = '{$paramArray['beinorder_pid']}'";
        $beinorderList = $this->DataControl->selectClear($sql);

        if ($beinorderList) {
            $status = $this->LgArraySwitch(array("0" => "校园采购", "1" => "集团统购"));
            foreach ($beinorderList as &$val) {
                $val['beinorder_from'] = $status[$val['beinorder_from']];
            }
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待入库", "2" => "已入库"));
            foreach ($beinorderList as &$val) {
                $val['beinorder_status_name'] = $status[$val['beinorder_status_name']];
            }

        }

        $field = array();
        $field["beinorder_pid"] = $this->LgStringSwitch("入库单号");
        $field["beinorder_from"] = $this->LgStringSwitch("购买方式");
        $field["beinorder_status"] = $this->LgStringSwitch("订单状态");
        $field["staffer_cnname"] = $this->LgStringSwitch("执行人");
        $field["nums"] = $this->LgStringSwitch("总数量");
        $field["beinorder_createtime"] = $this->LgStringSwitch("生成时间");

        $result = array();
        if ($beinorderList) {
            $result["field"] = $field;
            $result["data"] = $beinorderList;
            $res = array('error' => '0', 'errortip' => '获取入库单资料成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取入库单资料失败', 'result' => $result);
        }
        return $res;
    }


    //审核入库单
    function ExamineInorderAction($paramArray)
    {
        $proorderOne = $this->DataControl->getFieldOne("smc_erp_beinorder", "beinorder_pid,proorder_pid,beinorder_outpid", "beinorder_pid = '{$paramArray['beinorder_pid']}'");
        if ($proorderOne) {
            $data = array();
            $data['beinorder_status'] = '1';

            if ($paramArray['beinorder_status'] == '0') {
                $data['beinorder_status'] = '1';
            }
            if ($paramArray['beinorder_status'] == '1') {
                $data['beinorder_status'] = '2';
                $data['beinorder_storagetime'] = time();
                $datas = array();
                $datas['proorder_status'] = '3';
                $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$proorderOne['$proorderOne']}'", $datas);
            }

            if ($this->DataControl->updateData("smc_erp_beinorder", "beinorder_pid = '{$paramArray['beinorder_pid']}'", $data)) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "入库单最新状态修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '入库单最新状态修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //入库货品明细
    function getInGoodsList($paramArray)
    {
        $sql = "
            SELECT
                g.beinorder_pid,
                s.goods_cnname,
                s.goods_outpid,
                g.beinordergoods_buynums
            FROM
                smc_erp_beinorder_goods AS g
                left join erp_goods as s on g.goods_id = s.goods_id
            WHERE g.beinorder_pid = {$paramArray['beinorder_pid']}
            ORDER BY
                g.beinordergoods_id DESC";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_outpid ', 'goods_cnname', 'beinordergoods_buynums');
        $fieldname = array('K3货号', '中文名称', '入库数量');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //出库单
    function getBeoutorderList($paramArray)
    {

        $datawhere = " 1 and b.proorder_pid <> ''";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (b.beoutorder_pid like '%{$paramArray['keyword']}%' or b.salesorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['beoutorder_from']) && $paramArray['beoutorder_from'] !== "") {
            $datawhere .= " and b.beoutorder_from ={$paramArray['beoutorder_from']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['beoutorder_status']) && $paramArray['beoutorder_status'] !== "") {
            $datawhere .= " and b.beoutorder_status ={$paramArray['beoutorder_status']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                b.beoutorder_pid,
                b.beoutorder_from,
                b.beoutorder_status,
                b.salesorder_pid,
                b.beoutorder_status as beoutorder_status_name,
                s.staffer_cnname,
                (select sum(bg.beoutorder_buynums) from smc_erp_beoutorder_goods as bg where bg.beoutorder_pid = b.beoutorder_pid) as outnum,
                (select sum(sg.salesordergoods_buynums) from smc_erp_salesorder_goods as sg where sg.salesorder_pid = b.salesorder_pid) as salenum,
                sa.salesorder_from,
                FROM_UNIXTIME( sa.salesorder_createtime, '%Y-%m-%d' ) as salesorder_createtime,
                FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) as beoutorder_storagetime
            FROM
                smc_erp_beoutorder AS b
                left join smc_staffer as s on b.staffer_id = s.staffer_id
                left join smc_erp_salesorder as sa on sa.salesorder_pid = b.salesorder_pid
            WHERE {$datawhere} and b.school_id = {$paramArray['school_id']}
            ORDER BY
                b.beoutorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $beinorderList = $this->DataControl->selectClear($sql);

        if ($beinorderList) {
            $status = $this->LgArraySwitch(array("0" => "系统自订", "1" => "教师下单"));
            $statuss = $this->LgArraySwitch(array("0" => "调拨", "1" => "借调", "2" => "领用", "3" => "报损"));

            foreach ($beinorderList as &$val) {
                $val['beoutorder_from'] = $status[$val['beoutorder_from']];
            }
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待出库", "2" => "已出库"));
            foreach ($beinorderList as &$val) {
                $val['beoutorder_status_name'] = $status[$val['beoutorder_status_name']];
                $val['salesorder_from'] = $statuss[$val['salesorder_from']];
                if ($val['beoutorder_storagetime'] == '1970-01-01') {
                    $val['beoutorder_storagetime'] = '--';
                }
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(b.beoutorder_id) as a
            FROM
                smc_erp_beoutorder AS b
                left join smc_staffer as s on b.staffer_id = s.staffer_id
           WHERE {$datawhere} and b.school_id = {$paramArray['school_id']}");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('beoutorder_pid ', 'outnum', 'salesorder_pid', 'salesorder_from', 'salenum', 'beoutorder_status_name', 'salesorder_createtime', 'beoutorder_storagetime');
        $fieldname = array('出库单号', '出库货品数量', '销货单号', '销货类型', '销货货品数量', '状态', '销货申请日期', '出库日期');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($beinorderList) {
            $result['list'] = $beinorderList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        if (!$beinorderList) {
            $res = array('error' => '1', 'errortip' => "暂无出库单信息", 'result' => $result);
        }

        return $res;
    }

    //添加校园库存
    function addScRepertoryAction($paramArray)
    {
        $data = array();
        $goodsList = $this->DataControl->selectClear("select b.goods_id,b.beinordergoods_buynums,g.goods_originalprice,g.goods_vipprice from smc_erp_beinorder_goods as b left join erp_goods as g on b.goods_id = g.goods_id WHERE b.beinorder_pid = '{$paramArray['beinorder_pid']}'");
        foreach ($goodsList as $item) {
            $data['goods_id'] = $item['goods_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['school_id'] = $paramArray['school_id'];
            $data['goods_originalprice'] = $item['goods_originalprice'];
            $data['goods_price'] = $item['goods_vipprice'];
            $a = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_id,goods_repertory", "goods_id = '{$item['goods_id']}' and school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}' ");
            if ($a) {
                $data['goods_repertory'] = $item['beinordergoods_buynums'] + $a['goods_repertory'];
                $this->DataControl->updateData("smc_erp_goods_repertory", "goods_id = '{$item['goods_id']}' and school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}'", $data);
            } else {
                $data['goods_repertory'] = $item['beinordergoods_buynums'];
                $this->DataControl->insertData('smc_erp_goods_repertory', $data);
            }
        }

        $res = array('error' => '0', 'errortip' => "校园库存入库成功", 'result' => array());

        return $res;
    }


    //出库单资料
    function getBeoutorderInfo($paramArray)
    {
        $sql = "
            SELECT
                b.beoutorder_pid,
                b.beoutorder_from,
                b.beoutorder_status,
                b.salesorder_pid,
                b.beoutorder_status AS beoutorder_status_name,
                s.staffer_cnname,
                FROM_UNIXTIME( b.beoutorder_createtime, '%Y-%m-%d' ) AS beoutorder_createtime,
                FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) AS beoutorder_storagetime,
                (select sum(sg.salesordergoods_buynums) from smc_erp_salesorder_goods as sg where sg.salesorder_pid = b.salesorder_pid) as salenums,
                (select sum(g.beoutorder_buynums) from smc_erp_beoutorder_goods as g where g.beoutorder_pid = b.beoutorder_pid) as nums
            FROM
                smc_erp_beoutorder AS b
                LEFT JOIN smc_staffer AS s ON s.staffer_id = b.staffer_id
            WHERE
                b.beoutorder_pid = '{$paramArray['beoutorder_pid']}'";
        $beinorderList = $this->DataControl->selectClear($sql);

        if ($beinorderList) {
            $status = $this->LgArraySwitch(array("0" => "系统自订", "1" => "教师下单"));
            foreach ($beinorderList as &$val) {
                $val['beoutorder_from'] = $status[$val['beoutorder_from']];
            }
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待出库", "2" => "已出库"));
            foreach ($beinorderList as &$val) {
                $val['beoutorder_status_name'] = $status[$val['beoutorder_status_name']];
                if ($val['beoutorder_createtime'] == '1970-01-01') {
                    $val['beoutorder_createtime'] = '--';
                }
                if ($val['beoutorder_storagetime'] == '1970-01-01') {
                    $val['beoutorder_storagetime'] = '--';
                }
            }

        }

        $field = array();
        $field["beoutorder_pid"] = $this->LgStringSwitch("出库单号");
        $field["beoutorder_from"] = $this->LgStringSwitch("购买方式");
        $field["beoutorder_status"] = $this->LgStringSwitch("订单状态");
        $field["staffer_cnname"] = $this->LgStringSwitch("执行人");
        $field["nums"] = $this->LgStringSwitch("总数量");
        $field["beoutorder_createtime"] = $this->LgStringSwitch("生成时间");

        $result = array();
        if ($beinorderList) {
            $result["field"] = $field;
            $result["data"] = $beinorderList;
            $res = array('error' => '0', 'errortip' => '获取出库单资料成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取出库单资料失败', 'result' => $result);
        }
        return $res;
    }

    //审核出库单
    function ExamineOutorderAction($paramArray)
    {
        $proorderOne = $this->DataControl->getFieldOne("smc_erp_beoutorder", "proorder_pid,salesorder_pid,school_id,to_school_id,beoutorder_pid,beoutorder_outpid", "beoutorder_pid = '{$paramArray['beoutorder_pid']}'");

        $pro = $this->DataControl->getFieldOne("erp_proorder", "proorder_status,from_school_id", "proorder_pid = '{$proorderOne['proorder_pid']}'");

        if ($proorderOne) {
            $data = array();
            if ($paramArray['beoutorder_status'] == '0') {
                $data['beoutorder_status'] = '1';
            }
            if ($paramArray['beoutorder_status'] == '1') {
                $data['beoutorder_status'] = '2';
                $data['beoutorder_storagetime'] = time();
            }

            $datass = array();
            $datass['company_id'] = $paramArray['company_id'];

            if ($pro['proorder_status'] == '5') {
                $datass['school_id'] = $proorderOne['to_school_id'];
                $datass['from_school_id'] = $proorderOne['school_id'];
            } else {
                if ($proorderOne['to_school_id'] == $paramArray['school_id']) {
                    $datass['from_school_id'] = $proorderOne['school_id'];
                } else {
                    $datass['from_school_id'] = $paramArray['school_id'];
                }
                $datass['school_id'] = $proorderOne['to_school_id'];
            }


            $datass['proorder_pid'] = $proorderOne['proorder_pid'];
            $datass['beinorder_from'] = '0';
            $datass['beoutorder_pid'] = $paramArray['beoutorder_pid'];
            $datass['beinorder_pid'] = $this->createOrderPid('RK');
            $datass['beinorder_status'] = '0';
            $datass['staffer_id'] = $paramArray['staffer_id'];
            $datass['beinorder_createtime'] = time();

            $field = array();
            $field['company_id'] = $this->LgStringSwitch("所属公司");
            $field['school_id'] = $this->LgStringSwitch("校园ID");

            $this->DataControl->insertData('smc_erp_beinorder', $datass);
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $goodsList = $this->DataControl->selectClear("select * from smc_erp_beoutorder_goods WHERE beoutorder_pid = '{$proorderOne['beoutorder_pid']}'");
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['beinorder_pid'] = $datass['beinorder_pid'];
                $datas['beinordergoods_buynums'] = $item['beoutorder_buynums'];

                $this->DataControl->insertData('smc_erp_beinorder_goods', $datas);
            }

            foreach ($goodsList as $item) {
                $datare = array();
                $re = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$item['goods_id']}'");
                $datare['goods_repertory'] = $re['goods_repertory'] - $item['beoutorder_buynums'];
                $this->DataControl->updateData("smc_erp_goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$item['goods_id']}'", $datare);

                $changedata = array();
                $changedata['company_id'] = $paramArray['company_id'];
                $changedata['school_id'] = $paramArray['school_id'];
                $changedata['goods_id'] = $item['goods_id'];
                $changedata['staffer_id'] = $paramArray['staffer_id'];
                $changedata['changelog_class'] = '1';
                $changedata['changelog_playname'] = '审核出库';
                $changedata['changelog_reason'] = '审核出库';
                $changedata['changelog_playclass'] = '-';
                $changedata['changelog_fromnums'] = $re['goods_repertory'];
                $changedata['changelog_playnums'] = $item['beoutorder_buynums'];
                $changedata['changelog_finalnums'] = $re['goods_repertory'] - $item['beoutorder_buynums'];
                $changedata['changelog_createtime'] = time();
                $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
            }


            $field = array();
            $field['beoutorder_status'] = $this->LgStringSwitch("ERP订单最新状态ID");

            if ($this->DataControl->updateData("smc_erp_beoutorder", "beoutorder_pid = '{$paramArray['beoutorder_pid']}'", $data)) {
                $data = array();
                if ($pro['proorder_status'] == '5') {
                    $data['proorder_status'] = 6;
                } else {
                    $data['proorder_status'] = 2;
                }
                $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$proorderOne['proorder_pid']}'", $data);
                $data = array();
                $num = $this->DataControl->selectOne("select sum(sg.salesordergoods_buynums - sg.salesordergoods_sendnums) as num from smc_erp_salesorder_goods as sg where sg.salesorder_pid = '{$proorderOne['salesorder_pid']}'");
                if ($num['num'] == '0') {
                    $data['salesorder_status'] = 3;
                    $this->DataControl->updateData("smc_erp_salesorder", "salesorder_pid = '{$proorderOne['salesorder_pid']}'", $data);
                }
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "出库单最新状态修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '出库单最新状态修改失败', 'result' => $result);
            }

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //出库货品明细
    function getOutGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.goods_cnname like '%{$paramArray['keyword']}%' or s.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and s.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                g.beoutorder_pid,
                s.goods_cnname,
                s.goods_pid,
                s.goods_unit,
                s.goods_vipprice,
                g.beoutorder_buynums,
                p.prodtype_name,
                (select proogoods_buynums from erp_proorder_goods as pg where pg.proorder_pid = b.proorder_pid and pg.goods_id = g.goods_id) as buynum
            FROM
                smc_erp_beoutorder_goods AS g
                left join erp_goods as s on g.goods_id = s.goods_id
                left join smc_erp_beoutorder as b on b.beoutorder_pid = g.beoutorder_pid
                left join smc_code_prodtype as p on p.prodtype_code = s.prodtype_code and p.company_id = '{$paramArray['company_id']}'
            WHERE {$datawhere} and g.beoutorder_pid = '{$paramArray['beoutorder_pid']}'
            ORDER BY
                g.beoutordergoods_id DESC
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = count($goodsList);

        $fieldstring = array('goods_pid ', 'goods_cnname', 'prodtype_name', 'goods_unit', 'goods_vipprice', 'buynum', 'beoutorder_buynums');
        $fieldname = array('货品编号', '货品名称', '货品类别', '单位', '价格', '采购数量', '本次出库数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
            $result['all_num'] = $all_num;
        } else {
            $result['list'] = array();
            $result['all_num'] = 0;
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //减少校园库存
    function DelScRepertoryAction($paramArray)
    {
        $data = array();
        $goodsList = $this->DataControl->selectClear("select b.goods_id,b.beoutorder_buynums,g.goods_originalprice,g.goods_vipprice from smc_erp_beoutorder_goods as b left join erp_goods as g on b.goods_id = g.goods_id WHERE b.beoutorder_pid = '{$paramArray['beoutorder_pid']}'");
        foreach ($goodsList as $item) {
            $data['goods_id'] = $item['goods_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['school_id'] = $paramArray['school_id'];

            $a = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_id,goods_repertory", "goods_id = '{$item['goods_id']}' and school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}' ");

            $data['goods_repertory'] = $a['goods_repertory'] - $item['beoutorder_buynums'];
            $this->DataControl->updateData("smc_erp_goods_repertory", "goods_id = '{$item['goods_id']}' and school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}'", $data);
        }

        $res = array('error' => '0', 'errortip' => "校园库存出库成功", 'result' => array());

        return $res;
    }

    //获取库存变动日志列表
    function getChangelogList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( c.changelog_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( c.changelog_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }

        $sql = "
            SELECT
                c.changelog_class,
                c.changelog_fromnums,
                c.changelog_playnums,
                c.changelog_finalnums,
                FROM_UNIXTIME( c.changelog_createtime, '%Y-%m-%d' ) AS changelog_createtime 
            FROM
                smc_erp_goods_changelog AS c
            WHERE
                {$datawhere} and c.goods_id = '{$paramArray['goods_id']}' and c.school_id = '{$paramArray['school_id']}'
            ORDER BY
                c.changelog_id DESC";

        $goodsList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "入库", "1" => "出库", "2" => "盘点入库", "3" => "盘点出库"));
        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['changelog_class'] = $status[$val['changelog_class']];
            }
        }

        $fieldstring = array('changelog_class', 'changelog_fromnums', 'changelog_playnums', 'changelog_finalnums', 'changelog_createtime');
        $fieldname = array('变更类型', '原数量', '操作数量', '操作后数量', '记录时间');
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无库存变动日志信息", 'result' => $result);
        }

        return $res;
    }

    //编辑采购货品明细
    function updateGoodsDetailAction($paramArray)
    {

        $this->DataControl->delData("erp_proorder_goods", "proorder_pid = '{$paramArray['proorder_pid']}'");
        $data = array();

        $goodsList = json_decode(stripslashes($paramArray['goods']), true);
        foreach ($goodsList as $item) {
            $data['goods_id'] = $item['goods_id'];
            $data['proogoods_buynums'] = $item['proogoods_buynums'];
            $data['proogoods_unitprice'] = $item['proogoods_unitprice'];
            $data['proogoods_payprice'] = $item['proogoods_unitprice'] * $data['proogoods_buynums'];
            $data['proorder_pid'] = $item['proorder_pid'];
            $this->DataControl->insertData('erp_proorder_goods', $data);
        }

        $res = array('error' => '0', 'errortip' => "编辑采购物品成功", 'result' => array());
        return $res;

    }

    //生成借调订单
    function CreateLendorderAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['proorder_pid'] = $this->createOrderPid('JD');
        $data['orderclass_id'] = '1';
        $data['erpstatus_id'] = '15';
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['proorder_from'] = '1';
        $data['proorder_status'] = '0';
        $data['proorder_ispay'] = '0';
        $data['proorder_createtime'] = time();

        $field = array();
        $field['company_id'] = $this->LgStringSwitch("所属公司");
        $field['proorder_pid'] = $this->LgStringSwitch("订单号");
        $field['staffer_id'] = $this->LgStringSwitch("下单操作员工ID");
        $field['proorder_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('erp_proorder', $data)) {

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datasss = array();
            $datasss['company_id'] = $paramArray['company_id'];
            $datasss['school_id'] = $paramArray['school_id'];
            $datasss['outorder_pid'] = $data['proorder_pid'];
            $datasss['beinorder_createtime'] = time();
            $datasss['beinorder_status'] = '0';
            $datasss['beinorder_from'] = '1';
            $datasss['beinorder_pid'] = $this->createOrderPid('RC');

            $this->DataControl->insertData('smc_erp_beinorder', $datasss);

            $datass = array();
            $datass['company_id'] = $paramArray['company_id'];
            $datass['school_id'] = $paramArray['from_school_id'];
            $datass['staffer_id'] = $paramArray['staffer_id'];
            $datass['beoutorder_createtime'] = time();
            $datass['beoutorder_status'] = '0';
            $datass['beoutorder_from'] = '1';
            $datass['beoutorder_pid'] = $this->createOrderPid('CK');

            $this->DataControl->insertData('smc_erp_beoutorder', $datass);


            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas = array();
                $datas['goods_id'] = $item['goods_id'];
                $datas['beoutorder_buynums'] = $item['beoutorder_buynums'];
                $datas['beoutorder_pid'] = $datass['beoutorder_pid'];

                $this->DataControl->insertData('smc_erp_beoutorder_goods', $datas);

                $datas = array();

                $datas['goods_id'] = $item['goods_id'];
                $datas['beinordergoods_buynums'] = $item['beoutorder_buynums'];
                $datas['beinorder_pid'] = $datasss['beinorder_pid'];
                $datas['proorder_pid'] = $data['proorder_pid'];

                $this->DataControl->insertData('smc_erp_beinorder_goods', $datas);
            }

            $data1 = array();
            $data1['beinorder_pid'] = $datasss['beinorder_pid'];
            $data1['tracks_title'] = $this->LgStringSwitch('借调入库单');
            $data1['tracks_information'] = $this->LgStringSwitch('借调生成入库单');
            $data1['staffer_id'] = $paramArray['staffer_id'];
            $tracks_playname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
            $data1['tracks_playname'] = $tracks_playname['staffer_cnname'];
            $data1['tracks_createtime'] = time();
            $this->DataControl->insertData('smc_erp_beinorder_tracks', $data1);

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成订单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('借调生成订单成功');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['proorder_pid'] = $data['proorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

            $res = array('error' => '0', 'errortip' => "创建订单成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "创建订单失败", 'result' => $result);
        }
        return $res;
    }

    //调拨分校列表
    function getApplytypeApi($paramArray)
    {
        $sql = "select school_id,school_shortname as school_cnname from smc_school where company_id = '{$paramArray['company_id']}' and school_id <> '{$paramArray['school_id']}'";
        $ApplytypeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["school_cnname"] = $this->LgStringSwitch("校区名称");
        $result = array();
        if ($ApplytypeDetail) {
            $result["field"] = $field;
            $result["data"] = $ApplytypeDetail;
            $res = array('error' => '0', 'errortip' => '调拨分校列表查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '调拨分校列表查看失败', 'result' => $result);
        }
        return $res;
    }

    //销货管理
    function getSalesList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.proorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['proorder_from']) && $paramArray['proorder_from'] !== "") {
            $datawhere .= " and p.proorder_from ={$paramArray['proorder_from']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['orderclass_id']) && $paramArray['orderclass_id'] !== "") {
            $datawhere .= " and p.orderclass_id ='{$paramArray['orderclass_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.proorder_id,
                p.erpstatus_id,
                p.proorder_pid,
                p.proorder_from,
                p.proorder_note,
                p.orderclass_id,
                ( SELECT sum( proogoods_buynums ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = t.proorder_pid ) AS nums,
                ( SELECT sum( proogoods_payprice ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = t.proorder_pid ) AS price,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                e.erpstatus_fromsmcname,
                e.erpstatus_gmcname
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_from AS t ON t.proorder_pid = p.proorder_pid
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']} and t.school_id = {$paramArray['school_id']} and (p.orderclass_id = '6' or p.orderclass_id = '5')
            ORDER BY
                p.proorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("5" => "领用", "6" => "报损"));
            foreach ($goodsList as &$val) {
                $val['orderclass_id'] = $status[$val['orderclass_id']];
                if ($val['erpstatus_fromsmcname'] == '') {
                    $val['status'] = $val['erpstatus_gmcname'];
                } else {
                    $val['status'] = $val['erpstatus_fromsmcname'];
                }
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.proorder_id) as a
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_to AS t ON t.proorder_pid = p.proorder_pid
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']} and t.school_id = {$paramArray['school_id']}");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('proorder_pid ', 'orderclass_id', 'nums', 'price', 'proorder_note', 'proorder_createtime', 'status');
        $fieldname = array('销货单号', '销货类型', '货品数量总计', '销货金额', '备注', '申请时间', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无销货信息", 'result' => $result);
        }

        return $res;
    }

    //创建领用/报损订单
    function CreateGetOrderAction($paramArray)
    {
        $data = array();
        $data['salesorder_pid'] = $this->createOrderPid('XH');
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        if ($paramArray['from'] == '1') {
            $data['salesorder_from'] = '2';
        } else {
            $data['salesorder_from'] = '3';
        }
        $data['salesorder_status'] = '4';
        $data['salesorder_createtime'] = time();


        if ($id = $this->DataControl->insertData('smc_erp_salesorder', $data)) {

            $goodsList = json_decode(stripslashes($paramArray['goods']), true);

            foreach ($goodsList as $item) {
                $datas = array();
                $datas['goods_id'] = $item['goods_id'];
                $datas['salesordergoods_buynums'] = $item['proogoods_buynums'];
                $datas['salesorder_pid'] = $data['salesorder_pid'];

                $this->DataControl->insertData('smc_erp_salesorder_goods', $datas);
            }


            foreach ($goodsList as $item) {
                $datare = array();
                $re = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$item['goods_id']}'");
                $datare['goods_repertory'] = $re['goods_repertory'] - $item['proogoods_buynums'];
                $this->DataControl->updateData("smc_erp_goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$item['goods_id']}'", $datare);

                $changedata = array();
                $changedata['company_id'] = $paramArray['company_id'];
                $changedata['school_id'] = $paramArray['school_id'];
                $changedata['goods_id'] = $item['goods_id'];
                $changedata['staffer_id'] = $paramArray['staffer_id'];
                $changedata['changelog_class'] = '1';
                if ($paramArray['from'] == '1') {
                    $changedata['changelog_playname'] = '领用出库';
                    $changedata['changelog_reason'] = '领用出库';
                } else {
                    $changedata['changelog_playname'] = '报损出库';
                    $changedata['changelog_reason'] = '报损出库';
                }
                $changedata['changelog_playclass'] = '-';
                $changedata['changelog_fromnums'] = $re['goods_repertory'];
                $changedata['changelog_playnums'] = $item['proogoods_buynums'];
                $changedata['changelog_finalnums'] = $re['goods_repertory'] - $item['proogoods_buynums'];
                $changedata['changelog_createtime'] = time();
                $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
            }


            if ($paramArray['from'] == '1') {
                $res = array('error' => '0', 'errortip' => "领用成功");
            } else {
                $res = array('error' => '0', 'errortip' => "报损成功");
            }

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "创建领用失败", 'result' => $result);
        }
        return $res;
    }

    //学校库存列表
    function getChangeGoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or  g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and p.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_cnname,
                g.goods_pid,
                g.goods_vipprice,
                g.goods_unit,
                g.goods_originalprice,
                r.goods_repertory,
                p.prodtype_name,
                r.goods_id
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code and p.company_id = '{$paramArray['company_id']}'
                WHERE {$datawhere} and r.school_id = '{$paramArray['school_id']}'
            ORDER BY
                r.repertory_id DESC
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['number'] = '1';
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(r.repertory_id) as a
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code and p.company_id = '{$paramArray['company_id']}'
            WHERE
                {$datawhere} and r.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_unit', 'prodtype_name', 'goods_originalprice', 'goods_vipprice', 'goods_repertory', 'number');
        $fieldname = array('货品编号', '货品名称', '单位', '类别', '成本价', '协议价', '本校库存', '采购数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "0");
        $fieldisInputNumber = array(false, false, false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isInputNumber"] = trim($fieldisInputNumber[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_id,prodtype_name from smc_code_prodtype where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无调拨货品信息", 'result' => $result);
        }

        return $res;
    }

    //创建报损订单
    function CreateBadOrderAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['proorder_pid'] = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);;
        $data['orderclass_id'] = '6';
        $data['erpstatus_id'] = '24';
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['proorder_from'] = '1';
        $data['proorder_status'] = '0';
        $data['proorder_ispay'] = '0';
//        $data['proorder_allprice'] = $paramArray['allprice'];
        $data['proorder_createtime'] = time();

        $field = array();
        $field['company_id'] = $this->LgStringSwitch("所属公司");
        $field['proorder_pid'] = $this->LgStringSwitch("订单号");
        $field['staffer_id'] = $this->LgStringSwitch("下单操作员工ID");
        $field['proorder_allprice'] = $this->LgStringSwitch("订单价格");
        $field['proorder_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('erp_proorder', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['proogoods_buynums'] = $item['proogoods_buynums'];
//                $datas['proogoods_unitprice'] = $item['goods_vipprice'];
//                $datas['proogoods_payprice'] = $item['goods_vipprice'] * $item['proogoods_buynums'];
                $datas['proorder_pid'] = $data['proorder_pid'];

                $this->DataControl->insertData('erp_proorder_goods', $datas);
            }

            $datass = array();
            $datass['company_id'] = $paramArray['company_id'];
            $datass['school_id'] = $paramArray['school_id'];
            $datass['proorder_pid'] = $data['proorder_pid'];

            $this->DataControl->insertData('erp_proorder_from', $datass);

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成订单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('集团零用订单生成成功');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['proorder_pid'] = $data['proorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

            $res = array('error' => '0', 'errortip' => "创建领用订单成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "创建领用订单失败", 'result' => $result);
        }
        return $res;
    }


    //班组选项
    function getCourseTypeApi($paramArray)
    {
        $sql = "
            SELECT
                c.coursetype_id,
                c.coursetype_branch,
                c.coursetype_cnname
            FROM
                smc_code_coursetype AS c
            WHERE
                c.company_id = '{$paramArray['company_id']}'";
        $CourseType = $this->DataControl->selectClear($sql);

        $field = array();
        $field["coursetype_cnname"] = $this->LgStringSwitch("班组名称");
        $field["coursetype_branch"] = $this->LgStringSwitch("班组编号");

        $result = array();
        if ($CourseType) {
            $result["field"] = $field;
            $result["data"] = $CourseType;
            $res = array('error' => '0', 'errortip' => '获取班组成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班组失败', 'result' => $result);
        }
        return $res;
    }

    //班种选项
    function getCourseCatApi($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ={$paramArray['coursetype_id']}";
        }
        $sql = "
            SELECT
                c.coursecat_id,
                c.coursecat_branch,
                c.coursecat_cnname
            FROM
                smc_code_coursecat AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'";
        $CourseType = $this->DataControl->selectClear($sql);

        $field = array();
        $field["coursecat_cnname"] = $this->LgStringSwitch("班种名称");
        $field["coursecat_branch"] = $this->LgStringSwitch("班种编号");

        $result = array();
        if ($CourseType) {
            $result["field"] = $field;
            $result["data"] = $CourseType;
            $res = array('error' => '0', 'errortip' => '获取班种成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班种失败', 'result' => $result);
        }
        return $res;
    }

    //安全数量列表
    function getCourseSafeList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ={$paramArray['coursetype_id']}";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ={$paramArray['coursecat_id']}";
        }
        $sql = "
            SELECT
                c.course_id,
                c.course_cnname,
                c.course_branch,
                s.safestock_count 
            FROM
                smc_course AS c
                LEFT JOIN smc_erp_safestock AS s ON c.course_id = s.course_id
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'
                group by c.course_id";
        $CourseType = $this->DataControl->selectClear($sql);

        $fieldstring = array('course_cnname ', 'safestock_count');
        $fieldname = array('课程名称', '安全数量');
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($CourseType) {
            $result["field"] = $field;
            $result["data"] = $CourseType;
            $res = array('error' => '0', 'errortip' => '获取班别成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班别失败', 'result' => $result);
        }
        return $res;
    }

    //增加/改变安全库存
    function UpdateSafeAction($paramArray)
    {
        $data = array();

        $StockList = json_decode(stripslashes($paramArray['stock']), true);
        foreach ($StockList as $item) {
            $data['company_id'] = $item['company_id'];
            $data['school_id'] = $item['school_id'];
            $data['course_id'] = $item['course_id'];
            $data['safestock_count'] = $item['safestock_count'];

            $a = $this->DataControl->getFieldOne('smc_erp_safestock', 'safestock_id', "course_id = '{$item['course_id']}' and school_id = '{$item['school_id']}'");
            if ($a) {
                $data['safestock_updatetime'] = time();
                $this->DataControl->updateData('smc_erp_safestock', "course_id = '{$item['course_id']}' and school_id = '{$item['school_id']}'", $data);
            } else {
                $data['safestock_createtime'] = time();
                $this->DataControl->insertData('smc_erp_safestock', $data);
            }
        }

        $res = array('error' => '0', 'errortip' => "保存设置成功", 'result' => array());

        return $res;
    }

    //预估采购班级列表
    function getSafeBuyClassList($paramArray)
    {
        $a = $this->DataControl->getFieldOne("smc_erp_orderbuy", "orderbuy_id", "orderbuy_date = '{$paramArray['class_enddate']}' and orderbuy_status > '-1' and school_id = '{$paramArray['school_id']}'");
        $b = $this->DataControl->getFieldOne("erp_proorder", "proorder_id", "proorder_from = '1' and proorder_orderbuydate = '{$paramArray['class_enddate']}' and proorder_status > '-1'");
        if ($b) {
            $status = '1';
        } else {
            $status = '0';
        }

        $datawhere = " 1 ";

        if (isset($paramArray['class_enddate']) && $paramArray['class_enddate'] !== '') {
            $datawhere .= " and (DATE_FORMAT( c.class_enddate, '%Y-%m' ) like '%{$paramArray['class_enddate']}%')";
        }
        $sql = "
            SELECT
                c.class_id,
                c.class_enname,
                c.class_branch,
                s.course_cnname,
                c.course_id,
                ss.course_cnname as cnname,
                s.course_branch,
                ss.course_branch as branch,
                c.class_cnname,
                DATE_FORMAT( c.class_enddate, '%Y-%m' ) AS endtime,
                k.safestock_count,
                ( SELECT count( d.study_id ) FROM smc_student_study AS d WHERE d.class_id = c.class_id and d.study_isreading = '1' ) AS count 
            FROM
                smc_class AS c
                LEFT JOIN smc_course AS s ON c.course_id = s.course_id
                LEFT JOIN smc_course AS ss ON s.course_nextid = ss.course_id
                LEFT JOIN smc_erp_safestock AS k ON k.course_id = s.course_id 
            WHERE
                {$datawhere}
                and c.school_id = '{$paramArray['school_id']}'
                group by c.class_id";
        $ClassList = $this->DataControl->selectClear($sql);

        $a = '';
        if ($ClassList) {
            foreach ($ClassList as $val) {
                $a .= $val['branch'];
            }
        }

        if ($a == '') {
            $type = '0';
        } else {
            $type = '1';
        }

        $fieldstring = array('class_cnname', 'class_enname', 'class_branch', 'count', 'endtime', 'course_cnname', 'course_branch', 'cnname', 'branch');
        $fieldname = array('班级中文名', '班级别名', '班级编号', '班级人数', '结束日期', '课程别', '课程别编号', '下一课程别', '下一课程别编号');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($ClassList) {
            $result["field"] = $field;
            $result["data"] = $ClassList;
            $res = array('error' => '0', 'errortip' => '获取预估采购成功', 'result' => $result, 'status' => $status, 'type' => $type);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取预估采购失败', 'result' => $result, 'status' => $status, 'type' => $type);
        }
        return $res;
    }

    //下一级别班别列表
    function getNextCourseList($paramArray)
    {
        $sql = "
            SELECT
                s.course_nextid
            FROM
                smc_class AS c
	            LEFT JOIN smc_course AS s ON c.course_id = s.course_id
            WHERE
                c.school_id = '{$paramArray['school_id']}'
                and DATE_FORMAT( c.class_enddate, '%Y-%m' ) = '{$paramArray['class_enddate']}'
                and s.course_nextid > 0
                GROUP BY s.course_nextid";
        $NextCourse = $this->DataControl->selectClear($sql);

        foreach ($NextCourse as $val) {
            $val = join(",", $val);
            $temp_array[] = $val;
        }

        $str = implode(",", $temp_array);

        $sql = "SELECT
                    s.course_nextid,
                    ss.course_cnname,
                    ss.course_branch,
                    count( c.class_id ) AS classnum,
	                a.safestock_count
                FROM
                    smc_course AS s
                    LEFT JOIN smc_class AS c ON c.course_id = s.course_id 
                    left join smc_course as ss on s.course_nextid = ss.course_id
                    left join smc_erp_safestock as a on a.course_id = s.course_nextid
                WHERE
                    s.course_nextid IN ({$str}) 
                    AND c.school_id = '{$paramArray['school_id']}' 
                    AND DATE_FORMAT( c.class_enddate, '%Y-%m' ) = '{$paramArray['class_enddate']}'
                    GROUP BY s.course_nextid";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $count = $this->DataControl->selectClear("
                SELECT
                    count(y.study_id) as count
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS s ON c.course_id = s.course_id 
                    left join smc_student_study as y on c.class_id = y.class_id and y.study_isreading = '1'
                WHERE
                    c.school_id = '{$paramArray['school_id']}' 
                    AND DATE_FORMAT( c.class_enddate, '%Y-%m' ) = '{$paramArray['class_enddate']}' 
                    AND s.course_nextid > 0 GROUP BY s.course_nextid");

        if ($NextCourseList) {
            foreach ($NextCourseList as $k => &$v) {
                $v['num'] = $count[$k]['count'];
                $v['allnum'] = $v['safestock_count'] + $v['num'];
            }
        }

        $fieldstring = array('course_cnname', 'course_branch', 'classnum', 'num', 'safestock_count', 'allnum');
        $fieldname = array('下一课程别', '下一课程别编号', '包含班级数', '班级总人数', '安全库存量', '需求总量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '获取下单申请列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取下单申请列表失败', 'result' => $result);
        }
        return $res;
    }

    //班级查看列表
    function getClassDetailList($paramArray)
    {
        $sql = "
               SELECT 
                   count(c.class_id) as count
               FROM
                   smc_class AS c left join smc_course as s on c.course_id = s.course_id
               WHERE
                   c.school_id = '{$paramArray['school_id']}' 
                   AND DATE_FORMAT( c.class_enddate, '%Y-%m' ) = '{$paramArray['class_enddate']}'
                   and s.course_nextid = '{$paramArray['course_nextid']}'";
        $Count = $this->DataControl->selectClear($sql);

        $sql = "SELECT
                    c.class_cnname,
                    c.class_enname,
                    c.class_branch,
                    count( y.study_id ) AS count 
                FROM
                    smc_class AS c
                    LEFT JOIN smc_course AS s ON c.course_id = s.course_id
                    LEFT JOIN smc_student_study AS y ON c.class_id = y.class_id and y.study_isreading = '1'
                WHERE
                    c.school_id = '{$paramArray['school_id']}' 
                    AND DATE_FORMAT( c.class_enddate, '%Y-%m' ) = '{$paramArray['class_enddate']}' 
                    AND s.course_nextid = '{$paramArray['course_nextid']}' 
                GROUP BY
                    c.class_id";
        $ClassList = $this->DataControl->selectClear($sql);

        $fieldstring = array('class_cnname', 'class_enname', 'class_branch', 'count');
        $fieldname = array('班级中文名', '班级别名', '班级编号', '班级人数');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($ClassList) {
            $result["field"] = $field;
            $result["data"] = $ClassList;
            $result["count"] = $Count;
            $res = array('error' => '0', 'errortip' => '班级查看列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '班级查看列表失败', 'result' => $result);
        }
        return $res;
    }

    //预约采购下单申请
    function ApplyOrderBuyAction($paramArray)
    {
        $data = array();
        $data['orderbuy_pid'] = $this->createOrderPid('YG');
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['orderbuy_date'] = $paramArray['date'];
        $data['orderbuy_createtime'] = time();

        $field = array();
        $field['orderbuy_pid'] = $this->LgStringSwitch("预约单名称");
        $field['company_id'] = $this->LgStringSwitch("所属集团");
        $field['school_id'] = $this->LgStringSwitch("所属学校");
        $field['staffer_id'] = $this->LgStringSwitch("申请人id");
        $field['orderbuy_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('smc_erp_orderbuy', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $StockList = json_decode(stripslashes($paramArray['set']), true);
            foreach ($StockList as $item) {
                $datas['orderbuy_pid'] = $data['orderbuy_pid'];
                $datas['course_id'] = $item['course_id'];
                $datas['orderbuysets_ordernums'] = $item['orderbuysets_ordernums'];

                $this->DataControl->insertData('smc_erp_orderbuy_sets', $datas);

            }

            $res = array('error' => '0', 'errortip' => "预约采购下单申请成功", 'result' => $result, 'orderbuy_id' => $id);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '预约采购下单申请失败', 'result' => $result);
        }
        return $res;
    }

    //申请预约采购提交查看
    function getOrderBuyOne($paramArray)
    {
        $sql = "
            SELECT
                o.orderbuy_pid,
                o.orderbuy_status,
                o.orderbuy_date,
                s.staffer_cnname,
	            FROM_UNIXTIME(o.orderbuy_createtime, '%Y-%m-%d') as time
            FROM
                smc_erp_orderbuy AS o
                LEFT JOIN smc_staffer AS s ON s.staffer_id = o.staffer_id 
            WHERE
                o.orderbuy_id = '{$paramArray['orderbuy_id']}'";
        $OrderBuyOne = $this->DataControl->selectClear($sql);

        if ($OrderBuyOne) {
            $status = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "待审核", "1" => "已审核"));
            foreach ($OrderBuyOne as &$val) {
                $val['orderbuy_status'] = $status[$val['agreement_status']];
            }

        }

        $fieldstring = array('orderbuy_pid', 'orderbuy_date', 'staffer_cnname', 'time');
        $fieldname = array('预估采购申请编号', '采购月份', '提交人', '提交时间');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($OrderBuyOne) {
            $result["field"] = $field;
            $result["data"] = $OrderBuyOne;
            $res = array('error' => '0', 'errortip' => '申请预约采购提交查看成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '申请预约采购提交查看失败', 'result' => $result);
        }
        return $res;
    }

    //采购活动选项列表
    function getActivityBuyApi($paramArray)
    {
        $sql = "
            SELECT
                a.activitybuy_id, 
                a.activitybuy_name,
                a.activitybuy_stdate,
                a.activitybuy_enddate,
                a.activitybuy_change
            FROM
                gmc_company_activitybuy AS a
                LEFT JOIN gmc_activitybuy_schoolapply AS s ON a.activitybuy_id = s.activitybuy_id 
            WHERE
                (a.activitybuy_apply = 0 or s.school_id = '{$paramArray['school_id']}') and a.activitybuy_enddate >= curdate() 	and a.activitybuy_stdate <= curdate() and a.company_id = '{$paramArray['company_id']}'
            GROUP BY
                a.activitybuy_id";
        $ActivityDetail = $this->DataControl->selectClear($sql);

        foreach ($ActivityDetail as &$val) {
            $val['hour'] = intval((strtotime($val['activitybuy_enddate']) - time()) / 60 / 60);
        }

        $field = array();
        $field["activitybuy_name"] = $this->LgStringSwitch("采购活动名称");
        $result = array();
        if ($ActivityDetail) {
            $result["field"] = $field;
            $result["data"] = $ActivityDetail;
            $res = array('error' => '0', 'errortip' => '采购活动选项查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '采购活动选项查看失败', 'result' => $result);
        }
        return $res;
    }

    //采购活动商品列表
    function getActivityBuyGoodsList($paramArray)
    {
        $a = $this->DataControl->getFieldOne("smc_erp_activity", "activity_id", "activitybuy_id = '{$paramArray['activitybuy_id']}' and activity_status > '-1'");
        if ($a) {
            $status = '1';
        } else {
            $status = '0';
        }

        $sql = "
            SELECT
                g.goods_id,
                g.goods_pid,
                g.goods_cnname,
                g.goods_unit,
                g.goods_originalprice,
                g.goods_vipprice
            FROM
                erp_goods AS g
            LEFT JOIN gmc_activitybuy_goodsapply AS a ON g.goods_id = a.goods_id
            WHERE a.activitybuy_id = '{$paramArray['activitybuy_id']}'";
        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['applications'] = '1';
            }
        }

        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_unit', 'goods_originalprice', 'goods_vipprice', 'applications');
        $fieldname = array('货品编号', '货品名称', '单位', '成本价', '协议价', '申请数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $isInputNumber = array(false, false, false, false, false, true);
        $isSelectJuan = array(false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isSelectJuan"] = trim($isSelectJuan[$i]);
            $field[$i]["isInputNumber"] = trim($isInputNumber[$i]);
        }

        $result = array();
        if ($goodsList) {
            $result["field"] = $field;
            $result["data"] = $goodsList;
            $res = array('error' => '0', 'errortip' => '获取采购活动商品列表成功', 'result' => $result, 'status' => $status);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '该活动暂无商品', 'result' => $result, 'status' => $status);
        }
        return $res;
    }

    //活动采购下单申请
    function ApplyActivityBuyAction($paramArray)
    {
        $data = array();
        $data['activity_pid'] = $this->createOrderPid('HD');
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['activitybuy_id'] = $paramArray['activitybuy_id'];
        $data['activity_createtime'] = time();

        $field = array();
        $field['activity_pid'] = $this->LgStringSwitch("预约单名称");
        $field['company_id'] = $this->LgStringSwitch("所属集团");
        $field['school_id'] = $this->LgStringSwitch("所属学校");
        $field['staffer_id'] = $this->LgStringSwitch("申请人id");
        $field['activitybuy_id'] = $this->LgStringSwitch("活动id");
        $field['activity_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('smc_erp_activity', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $StockList = json_decode(stripslashes($paramArray['set']), true);
            foreach ($StockList as $item) {
                $datas['activity_pid'] = $data['activity_pid'];
                $datas['goods_id'] = $item['goods_id'];
                $datas['activitygoods_buynums'] = $item['activitygoods_buynums'];

                $this->DataControl->insertData('smc_erp_activity_goods', $datas);

            }

            $res = array('error' => '0', 'errortip' => "活动采购下单申请成功", 'result' => $result, 'orderbuy_id' => $id);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '活动采购下单申请失败', 'result' => $result);
        }
        return $res;
    }

    //预估采购确认列表
    function getOrderBuyConfirmList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (o.orderbuy_pid like '%{$paramArray['keyword']}%' or r.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( o.orderbuy_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( o.orderbuy_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['state']) && $paramArray['state'] !== "") {
            $datawhere .= " and o.orderbuy_status ='{$paramArray['state']}'";
        }
        if (isset($paramArray['month']) && $paramArray['month'] !== "") {
            $datawhere .= " and o.orderbuy_date ='{$paramArray['month']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                o.orderbuy_pid,
                sum( s.orderbuysets_ordernums ) AS count,
                o.orderbuy_date AS date,
                concat(staffer_cnname,(CASE WHEN ifnull( r.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', r.staffer_enname ) END ) ) as staffer_cnname,
                o.orderbuy_status,
                o.orderbuy_status as status,
                o.orderbuy_status as orderbuy_status_name,
                sum( ( g.orderbuygoods_buynums * e.goods_vipprice ) ) AS price,
                FROM_UNIXTIME( o.orderbuy_createtime, '%Y-%m-%d' ) AS orderbuy_createtime
            FROM
                smc_erp_orderbuy AS o
                LEFT JOIN smc_erp_orderbuy_goods AS g ON o.orderbuy_pid = g.orderbuy_pid
                LEFT JOIN smc_erp_orderbuy_sets AS s ON o.orderbuy_pid = s.orderbuy_pid
                LEFT JOIN smc_staffer AS r ON r.staffer_id = o.staffer_id 
                left join erp_goods as e on e.goods_id = g.goods_id
            WHERE
                {$datawhere} and o.school_id = '{$paramArray['school_id']}'
            GROUP BY
                o.orderbuy_id
                order by orderbuy_id DESC
                limit {$pagestart},{$num}";
        $OrderBuyList = $this->DataControl->selectClear($sql);

        $allnum = count($OrderBuyList);

        if ($OrderBuyList) {
            $status = $this->LgArraySwitch(array("1" => "已审核", "-1" => "已拒绝", "0" => "待确认"));
            foreach ($OrderBuyList as &$val) {
                $val['orderbuy_status_name'] = $status[$val['orderbuy_status_name']];
                $val['type'] = $this->LgStringSwitch('预估采购');
            }
        }

        $fieldstring = array('orderbuy_pid', 'type', 'date', 'count', 'orderbuy_createtime', 'staffer_cnname', 'orderbuy_status_name');
        $fieldname = array('申请采购编号', '采购类型', '预估采购月份', '申请采购套数', '申请时间', '采购人', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($OrderBuyList) {
            $result["field"] = $field;
            $result["data"] = $OrderBuyList;
            $result["all_num"] = $allnum;
            $res = array('error' => '0', 'errortip' => '获取预估采购确认列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取预估采购确认列表失败', 'result' => $result);
        }
        return $res;
    }

    //活动采购确认列表
    function getActivityBuyConfirmList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.activity_pid like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( a.activity_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( a.activity_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['state']) && $paramArray['state'] !== "") {
            $datawhere .= " and a.activity_status ='{$paramArray['state']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                a.activitybuy_id,
                a.activity_pid,
                sum( g.activitygoods_buynums ) AS count,
                FROM_UNIXTIME( a.activity_createtime, '%Y-%m-%d' ) AS date,
                concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname,
                a.activity_status,
                a.activity_status as status,
                a.activity_status AS activity_status_name ,
                sum( ( g.activitygoods_buynums * e.goods_vipprice ) ) AS price,
                ac.activitybuy_name
            FROM
                smc_erp_activity AS a
                LEFT JOIN smc_erp_activity_goods AS g ON a.activity_pid = g.activity_pid
                LEFT JOIN smc_staffer AS s ON s.staffer_id = a.staffer_id 
                left join erp_goods as e on e.goods_id = g.goods_id
                left join gmc_company_activitybuy as ac on ac.activitybuy_id = a.activitybuy_id
            WHERE {$datawhere} and a.school_id = '{$paramArray['school_id']}'
            GROUP BY
                a.activity_id
                order by activity_id DESC
                limit {$pagestart},{$num}";
        $ActivityBuyList = $this->DataControl->selectClear($sql);

        $allnum = count($ActivityBuyList);

        if ($ActivityBuyList) {
            $status = $this->LgArraySwitch(array("1" => "已审核", "-1" => "已拒绝", "0" => "待确认"));
            foreach ($ActivityBuyList as &$val) {
                $val['activity_status_name'] = $status[$val['activity_status_name']];
                $val['type'] = $this->LgStringSwitch('活动采购');
            }
        }

        $fieldstring = array('activity_pid', 'type', 'activitybuy_name', 'count', 'price', 'date', 'staffer_cnname', 'activity_status_name');
        $fieldname = array('申请采购编号', '采购类型', '活动名称', '申请采购数量', '采购金额总计', '申请时间', '采购人', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($ActivityBuyList) {
            $result["field"] = $field;
            $result["data"] = $ActivityBuyList;
            $result["all_num"] = $allnum;
            $res = array('error' => '0', 'errortip' => '获取活动采购确认列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取活动采购确认列表失败', 'result' => $result);
        }
        return $res;
    }

    //自主采购确认列表
    function getOwnBuyConfirmList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.ownbuy_pid like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( a.ownbuy_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( a.ownbuy_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['state']) && $paramArray['state'] !== "") {
            $datawhere .= " and a.ownbuy_status ='{$paramArray['state']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                a.ownbuy_pid,
                sum( g.ownbuygoods_buynums ) AS count,
                FROM_UNIXTIME( a.ownbuy_createtime, '%Y-%m-%d' ) AS date,
                concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname,
                a.ownbuy_status,
                a.ownbuy_status as status,
                a.ownbuy_status AS ownbuy_status_name ,
                sum( ( g.ownbuygoods_buynums * e.goods_vipprice ) ) AS price
            FROM
                smc_erp_ownbuy AS a
                LEFT JOIN smc_erp_ownbuy_goods AS g ON a.ownbuy_pid = g.ownbuy_pid
                LEFT JOIN smc_staffer AS s ON s.staffer_id = a.staffer_id 
                left join erp_goods as e on e.goods_id = g.goods_id
            WHERE {$datawhere} and a.school_id = '{$paramArray['school_id']}'
            GROUP BY
                a.ownbuy_id
                order by ownbuy_id DESC
                limit {$pagestart},{$num}";
        $OwnbuyList = $this->DataControl->selectClear($sql);

        $allnum = count($OwnbuyList);

        if ($OwnbuyList) {
            $status = $this->LgArraySwitch(array("1" => "已审核", "-1" => "已拒绝", "0" => "待确认"));
            foreach ($OwnbuyList as &$val) {
                $val['ownbuy_status_name'] = $status[$val['ownbuy_status_name']];
                $val['type'] = $this->LgStringSwitch('自主采购');
            }
        }

        $fieldstring = array('ownbuy_pid', 'type', 'count', 'price', 'date', 'staffer_cnname', 'ownbuy_status_name');
        $fieldname = array('申请采购编号', '采购类型', '申请采购数量', '采购金额总计', '申请时间', '采购人', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($OwnbuyList) {
            $result["field"] = $field;
            $result["data"] = $OwnbuyList;
            $result["all_num"] = $allnum;
            $res = array('error' => '0', 'errortip' => '获取活动采购确认列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取活动采购确认列表失败', 'result' => $result);
        }
        return $res;
    }

    //借调确认列表
    function getTransferbuyConfirmList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.transferbuy_pid like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( a.transferbuy_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( a.transferbuy_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['state']) && $paramArray['state'] !== "") {
            $datawhere .= " and a.transferbuy_status ='{$paramArray['state']}'";
        }
        if (isset($paramArray['from_school_id']) && $paramArray['from_school_id'] != '') {
            $datawhere .= " and a.from_school_id = '{$paramArray['from_school_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                a.transferbuy_pid,
                sum( g.transferbuygoods_buynums ) AS count,
                FROM_UNIXTIME( a.transferbuy_createtime, '%Y-%m-%d' ) AS date,
                concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname,
                a.transferbuy_status,
                a.transferbuy_status as status,
                a.transferbuy_status AS transferbuy_status_name ,
                sum( ( g.transferbuygoods_buynums * e.goods_vipprice ) ) AS price,
                sc.school_shortname as school_cnname,
                a.transferbuy_returndate,
            	scc.school_shortname as from_school_cnname,
            	scc.school_id as from_school_id
            FROM
                smc_erp_transferbuy AS a
                LEFT JOIN smc_erp_transferbuy_goods AS g ON a.transferbuy_pid = g.transferbuy_pid
                LEFT JOIN smc_staffer AS s ON s.staffer_id = a.staffer_id 
                left join erp_goods as e on e.goods_id = g.goods_id
                left join smc_school as sc on sc.school_id = a.school_id
	            left join smc_school as scc on scc.school_id = a.from_school_id
            WHERE {$datawhere} and a.school_id = '{$paramArray['school_id']}'
            GROUP BY
                a.transferbuy_id
                order by transferbuy_id DESC
                 limit {$pagestart},{$num}";
        $OwnbuyList = $this->DataControl->selectClear($sql);

        $allnum = count($OwnbuyList);

        if ($OwnbuyList) {
            $status = $this->LgArraySwitch(array("1" => "已审核", "-1" => "已拒绝", "0" => "待确认"));
            foreach ($OwnbuyList as &$val) {
                $val['transferbuy_status_name'] = $status[$val['transferbuy_status_name']];
                $val['type'] = $this->LgStringSwitch('借调');
            }
        }

        $fieldstring = array('transferbuy_pid', 'type', 'school_cnname', 'from_school_cnname', 'count', 'price', 'date', 'transferbuy_returndate', 'staffer_cnname', 'transferbuy_status_name');
        $fieldname = array('申请采购编号', '采购类型', '借入校', '借出校', '借调数量', '采购金额总计', '借调申请日期', '预计归还日期', '采购人', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($OwnbuyList) {
            $result["field"] = $field;
            $result["data"] = $OwnbuyList;
            $result["all_num"] = $allnum;
            $res = array('error' => '0', 'errortip' => '获取借调确认列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '获取借调确认列表失败', 'result' => $result);
        }
        return $res;
    }

    //预估采购单信息
    function getOrderBuyInfoApi($paramArray)
    {
        $sql = "
            SELECT
                sum( s.orderbuysets_ordernums ) as count,
                s.orderbuy_pid,
                FROM_UNIXTIME( o.orderbuy_createtime, '%Y-%m-%d' ) AS date,
                o.orderbuy_date
            FROM
                smc_erp_orderbuy_sets AS s
                LEFT JOIN smc_erp_orderbuy AS o ON o.orderbuy_pid = s.orderbuy_pid 
            WHERE
                o.orderbuy_id = '{$paramArray['orderbuy_id']}'";
        $OrderBuyDetail = $this->DataControl->selectClear($sql);

        if ($OrderBuyDetail) {
            foreach ($OrderBuyDetail as &$val) {
                $val['type'] = $this->LgStringSwitch('预估采购');
            }
        }

        $field = array();
        $field["orderbuy_pid"] = $this->LgStringSwitch("申请采购编号");
        $field["type"] = $this->LgStringSwitch("采购类型");
        $field["count"] = $this->LgStringSwitch("申请采购套数");
        $field["date"] = $this->LgStringSwitch("申请日期");
        $field["orderbuy_date"] = $this->LgStringSwitch("申请年月");
        $result = array();
        if ($OrderBuyDetail) {
            $result["field"] = $field;
            $result["data"] = $OrderBuyDetail;
            $res = array('error' => '0', 'errortip' => '预估采购单信息查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '预估采购单信息查看失败', 'result' => $result);
        }
        return $res;
    }

    //预估采购项目列表
    function getOwnBuySetsList($paramArray)
    {
        $sql = "SELECT
                    s.orderbuysets_id,
                    s.orderbuy_pid,
                    s.course_id,
                    c.course_cnname,
                    c.course_branch,
                    s.orderbuysets_ordernums,
                    IFNULL(a.safestock_count,0) as safestock_count,
                    ( s.orderbuysets_ordernums - IFNULL(a.safestock_count,0)) AS count 
                FROM
                    smc_erp_orderbuy_sets AS s
                    LEFT JOIN smc_erp_safestock AS a ON s.course_id = a.course_id
                    LEFT JOIN smc_course AS c ON s.course_id = c.course_id 
                WHERE
                    s.orderbuy_pid = '{$paramArray['orderbuy_pid']}'
                GROUP BY s.orderbuysets_id";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('course_cnname', 'course_branch', 'count', 'safestock_count', 'orderbuysets_ordernums', 'class_shenhe');
        $fieldname = array('课程别', '课程编号', '班级总人数', '安全库存量', '申请数量', '确认数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $isSelectJuan = array(false, false, false, false, false, true);
        $isInputText = array(false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isSelectJuan"] = trim($isSelectJuan[$i]);
            $field[$i]["isInputText"] = trim($isInputText[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '预估采购项目列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '预估采购项目列表失败', 'result' => $result);
        }
        return $res;
    }

    //预估采购下一步
    function addSetsAction($paramArray)
    {
        $data = array();

        $setList = json_decode(stripslashes($paramArray['set']), true);
        foreach ($setList as $item) {
            $data['orderbuysets_ordernums'] = $item['orderbuysets_ordernums'];

            $this->DataControl->updateData('smc_erp_orderbuy_sets', "orderbuysets_id = '{$item['orderbuysets_id']}'", $data);
        }
        $res = array('error' => '0', 'errortip' => "提交成功成功", 'result' => array());
        return $res;
    }

    //新增课程别选项列表
    function getCourseApi($paramArray)
    {
        $sql = "
            SELECT
                c.course_id,
                c.course_cnname,
                c.course_branch
            FROM
                smc_course AS c
            WHERE
                c.company_id = '{$paramArray['company_id']}'";
        $CourseList = $this->DataControl->selectClear($sql);

        $field = array();
        $field["course_cnname"] = $this->LgStringSwitch("课程名称");
        $field["course_branch"] = $this->LgStringSwitch("课程编号");
        $result = array();
        if ($CourseList) {
            $result["field"] = $field;
            $result["data"] = $CourseList;
            $res = array('error' => '0', 'errortip' => '新增课程别选项列表查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新增课程别选项列表查看失败', 'result' => $result);
        }
        return $res;
    }

    //获取安全库存数
    function getSafeApi($paramArray)
    {
        $sql = "
            SELECT
                s.safestock_count
            FROM
                smc_erp_safestock AS s
            WHERE
                s.course_id = '{$paramArray['course_id']}' and school_id = '{$paramArray['school_id']}'";
        $CourseList = $this->DataControl->selectOne($sql);

        $field = array();
        $field["safestock_count"] = $this->LgStringSwitch("安全库存数");
        $result = array();
        if ($CourseList) {
            $result["field"] = $field;
            $result["data"] = $CourseList;
            $res = array('error' => '0', 'errortip' => '获取安全库存数成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取安全库存数失败', 'result' => $result);
        }
        return $res;
    }

    //新增库存预购
    function CreateSetAction($paramArray)
    {
        $data = array();

        $data['orderbuy_pid'] = $paramArray['orderbuy_pid'];
        $data['course_id'] = $paramArray['course_id'];
        $data['orderbuysets_ordernums'] = $paramArray['orderbuysets_ordernums'];

        $field = array();
        $field['orderbuy_pid'] = $this->LgStringSwitch("预约申请pid");
        $field['course_id'] = $this->LgStringSwitch("课程id");
        $field['orderbuysets_ordernums'] = $this->LgStringSwitch("申请数量");

        if ($this->DataControl->insertData('smc_erp_orderbuy_sets', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增库存预购成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新增库存预购失败', 'result' => $result);
        }
        return $res;
    }

    //预估采购商品列表
    function getOrderGoodsList($paramArray)
    {
        $sql = "SELECT
                    a.goods_id,a.goods_cnname,a.goods_pid,sum( a.orderbuysets_ordernums ) as count,a.goods_vipprice
                FROM
                    (
                SELECT
                    t.goods_id,
                    g.goods_cnname,
                    g.goods_vipprice,
                    g.goods_pid,
                    s.course_id,
                    s.orderbuysets_ordernums
                FROM
                    smc_erp_orderbuy_sets AS s
                    LEFT JOIN smc_fee_pricing AS p ON s.course_id = p.course_id
                    LEFT JOIN smc_fee_pricing_apply AS a ON p.pricing_id = a.pricing_id
                    LEFT JOIN smc_fee_pricing_products AS t ON t.pricing_id = p.pricing_id
                    left join erp_goods as g on g.goods_id = t.goods_id
                    left join smc_erp_goods_repertory as r on r.goods_id = g.goods_id
                WHERE
                    s.orderbuy_pid = '{$paramArray['orderbuy_pid']}' 
                GROUP BY
                    s.course_id,
                    t.goods_id) as a GROUP BY a.goods_id
                ";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_vipprice', 'count');
        $fieldname = array('K3货号', '名称', '价格', '采购数量');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '预估采购商品列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '预估采购商品列表失败', 'result' => $result);
        }
        return $res;
    }

    //改变申请状态
    function UpdateApplyAction($paramArray)
    {
        if ($paramArray['status'] == '0') {
            $data = array();
            $data['ownbuy_status'] = $paramArray['applystatus'];
            $data['ownbuy_remark'] = $paramArray['remark'];
            $data['ownbuy_updatetime'] = time();

            $field = array();
            $field['ownbuy_status'] = $this->LgStringSwitch("状态");

            $this->DataControl->updateData("smc_erp_ownbuy", "ownbuy_pid = '{$paramArray['pid']}'", $data);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
        }

        if ($paramArray['status'] == '1') {
            $data = array();
            $data['orderbuy_status'] = $paramArray['applystatus'];
            $data['orderbuy_remark'] = $paramArray['remark'];
            $data['orderbuy_updatetime'] = time();

            $field = array();
            $field['orderbuy_status'] = $this->LgStringSwitch("状态");

            $this->DataControl->updateData("smc_erp_orderbuy", "orderbuy_pid = '{$paramArray['pid']}'", $data);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
        }

        if ($paramArray['status'] == '2') {
            $data = array();
            $data['activity_status'] = $paramArray['applystatus'];
            $data['activity_remark'] = $paramArray['remark'];
            $data['activity_updatetime'] = time();

            $field = array();
            $field['activity_status'] = "状态";

            $this->DataControl->updateData("smc_erp_activity", "activity_pid = '{$paramArray['pid']}'", $data);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
        }

        if ($paramArray['status'] == '3') {
            $data = array();
            $data['transferbuy_status'] = $paramArray['applystatus'];
            $data['transferbuy_remark'] = $paramArray['remark'];
            $data['transferbuy_updatetime'] = time();

            $field = array();
            $field['transferbuy_status'] = $this->LgStringSwitch("状态");

            $this->DataControl->updateData("smc_erp_transferbuy", "transferbuy_pid = '{$paramArray['pid']}'", $data);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
        }

        return $res;
    }

    //取消采购
    function CancelProorderAction($paramArray)
    {
        $data = array();
        $data['proorder_status'] = '-1';
        $data['proorder_updatetime'] = time();

        $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$paramArray['proorder_pid']}'", $data);

        $this->DataControl->delData("smc_erp_salesorder", "proorder_pid = '{$paramArray['proorder_pid']}'");
        $this->DataControl->delData("smc_erp_salesorder_goods", "proorder_pid = '{$paramArray['proorder_pid']}'");

        $result = array();
        $result["data"] = $data;
        $res = array('error' => '0', 'errortip' => "取消成功", 'result' => $result);

        return $res;
    }

    //删除预估采购项目
    function DelOrderSetAction($paramArray)
    {
        $GoodsOne = $this->DataControl->getFieldOne("smc_erp_orderbuy_sets", "orderbuysets_id", "orderbuysets_id = '{$paramArray['orderbuysets_id']}'");
        if ($GoodsOne) {
            if ($this->DataControl->delData("smc_erp_orderbuy_sets", "orderbuysets_id = '{$paramArray['orderbuysets_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除预估采购项目成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除预估采购项目失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //自主采购单信息
    function getOwnBuyInfoApi($paramArray)
    {
        $sql = "
            SELECT
                sum( s.ownbuygoods_buynums ) AS count,
                s.ownbuy_pid,
                FROM_UNIXTIME( o.ownbuy_createtime, '%Y-%m-%d' ) AS date,
                sum(s.ownbuygoods_buynums * g.goods_vipprice) as price
            FROM
                smc_erp_ownbuy_goods AS s
                LEFT JOIN smc_erp_ownbuy AS o ON o.ownbuy_pid = s.ownbuy_pid 
                LEFT JOIN erp_goods AS g ON s.goods_id = g.goods_id
            WHERE
                o.ownbuy_id = '{$paramArray['ownbuy_id']}'";
        $OrderBuyDetail = $this->DataControl->selectClear($sql);

        if ($OrderBuyDetail) {
            foreach ($OrderBuyDetail as &$val) {
                $val['type'] = $this->LgStringSwitch('自主采购');
            }
        }

        $field = array();
        $field["orderbuy_pid"] = $this->LgStringSwitch("申请采购编号");
        $field["type"] = $this->LgStringSwitch("采购类型");
        $field["count"] = $this->LgStringSwitch("申请采购数量");
        $field["date"] = $this->LgStringSwitch("申请日期");
        $field["price"] = $this->LgStringSwitch("申请采购金额");
        $result = array();
        if ($OrderBuyDetail) {
            $result["field"] = $field;
            $result["data"] = $OrderBuyDetail;
            $res = array('error' => '0', 'errortip' => '自主采购单信息查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '自主采购单信息查看失败', 'result' => $result);
        }
        return $res;
    }

    //自主采购商品列表
    function getOwnBuyGoodsList($paramArray)
    {
        $sql = "SELECT
                    o.ownbuygoods_id,
                    g.goods_pid,
                    g.goods_id,
                    g.goods_unit,
                    g.goods_vipprice,
                    g.goods_cnname,
                    o.ownbuygoods_buynums
                FROM
                    smc_erp_ownbuy_goods AS o
                    LEFT JOIN erp_goods AS g ON g.goods_id = o.goods_id 
                where o.ownbuy_pid = '{$paramArray['ownbuy_pid']}'";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_unit', 'goods_vipprice', 'ownbuygoods_buynums', 'class_shenhe');
        $fieldname = array('货品编号', '货品名称', '单位', '协议价', '申请采购数量', '确认数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $isSelectJuan = array(false, false, false, false, false, true);
        $isInputText = array(false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isSelectJuan"] = trim($isSelectJuan[$i]);
            $field[$i]["isInputText"] = trim($isInputText[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '自主采购商品列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '自主采购商品列表失败', 'result' => $result);
        }
        return $res;
    }

    //借调商品列表
    function getTransferBuyGoodsList($paramArray)
    {
        $sql = "SELECT
                    o.transferbuygoods_id,
                    g.goods_pid,
                    g.goods_id,
                    g.goods_unit,
                    g.goods_vipprice,
                    g.goods_cnname,
                    o.transferbuygoods_buynums
                FROM
                    smc_erp_transferbuy_goods AS o
                    LEFT JOIN erp_goods AS g ON g.goods_id = o.goods_id 
                where o.transferbuy_pid = '{$paramArray['transferbuy_pid']}'";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_unit', 'goods_vipprice', 'transferbuygoods_buynums', 'class_shenhe');
        $fieldname = array('货品编号', '名称', '单位', '协议价', '申请借调数量', '确认数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $isSelectJuan = array(false, false, false, false, false, true);
        $isInputText = array(false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isSelectJuan"] = trim($isSelectJuan[$i]);
            $field[$i]["isInputText"] = trim($isInputText[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '获取借调商品列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取借调商品列表失败', 'result' => $result);
        }
        return $res;
    }

    //活动采购信息
    function getActivityBuyInfoApi($paramArray)
    {
        $sql = "
            SELECT
                sum( s.activitygoods_buynums ) AS count,
                s.activity_pid,
                FROM_UNIXTIME( o.activity_createtime, '%Y-%m-%d' ) AS date,
                sum(s.activitygoods_buynums * g.goods_vipprice) as price
            FROM
                smc_erp_activity_goods AS s
                LEFT JOIN smc_erp_activity AS o ON o.activity_pid = s.activity_pid 
                LEFT JOIN erp_goods AS g ON s.goods_id = g.goods_id
            WHERE
                o.activity_id = '{$paramArray['activity_id']}'";
        $OrderBuyDetail = $this->DataControl->selectClear($sql);

        if ($OrderBuyDetail) {
            foreach ($OrderBuyDetail as &$val) {
                $val['type'] = '活动采购';
            }
        }

        $field = array();
        $field["orderbuy_pid"] = $this->LgStringSwitch("申请采购编号");
        $field["type"] = $this->LgStringSwitch("采购类型");
        $field["count"] = $this->LgStringSwitch("申请采购数量");
        $field["date"] = $this->LgStringSwitch("申请日期");
        $field["price"] = $this->LgStringSwitch("申请采购金额");
        $result = array();
        if ($OrderBuyDetail) {
            $result["field"] = $field;
            $result["data"] = $OrderBuyDetail;
            $res = array('error' => '0', 'errortip' => '活动采购单信息查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '活动采购单信息查看失败', 'result' => $result);
        }
        return $res;
    }

    //活动采购商品列表
    function getActivityGoodsList($paramArray)
    {
        $sql = "SELECT
                    o.activitygoods_id,
                    g.goods_id,
                    g.goods_pid,
                    g.goods_vipprice,
                    g.goods_unit,
                    g.goods_cnname,
                    o.activitygoods_buynums
                FROM
                    smc_erp_activity_goods AS o
                    LEFT JOIN erp_goods AS g ON g.goods_id = o.goods_id 
                where o.activity_pid = '{$paramArray['activity_pid']}'";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_unit', 'goods_vipprice', 'activitygoods_buynums', 'class_shenhe');
        $fieldname = array('货品编号', '货品名称', '单位', '协议价', '申请采购数量', '确认数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $isSelectJuan = array(false, false, false, false, false, true);
        $isInputText = array(false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isSelectJuan"] = trim($isSelectJuan[$i]);
            $field[$i]["isInputText"] = trim($isInputText[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '活动采购商品列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '活动采购商品列表失败', 'result' => $result);
        }
        return $res;
    }

    //删除自主采购项目
    function DelOwnBuyAction($paramArray)
    {
        $GoodsOne = $this->DataControl->getFieldOne("smc_erp_ownbuy_goods", "ownbuygoods_id", "ownbuygoods_id = '{$paramArray['ownbuygoods_id']}'");
        if ($GoodsOne) {
            if ($this->DataControl->delData("smc_erp_ownbuy_goods", "ownbuygoods_id = '{$paramArray['ownbuygoods_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除自主采购项目成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除自主采购项目失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除活动采购项目
    function DelActivityBuyAction($paramArray)
    {
        $GoodsOne = $this->DataControl->getFieldOne("smc_erp_activity_goods", "activitygoods_id", "activitygoods_id = '{$paramArray['activitygoods_id']}'");
        if ($GoodsOne) {
            if ($this->DataControl->delData("smc_erp_activity_goods", "activitygoods_id = '{$paramArray['activitygoods_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除活动采购项目成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除活动采购项目失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //库存盘点列表
    function getCheckList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and c.check_date >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and c.check_date <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['check_class']) && $paramArray['check_class'] !== "") {
            $datawhere .= " and c.check_class ='{$paramArray['check_class']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "SELECT
                    c.check_id,
                    c.check_difference,
                    c.check_class,
                    c.check_checknum,
                    c.check_accounts,
                    concat(staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END ) ) as staffer_cnname,
                    c.check_date
                FROM
                    erp_goods_check AS c left join smc_staffer as s on s.staffer_id = c.staffer_id
                where {$datawhere} and  c.school_id = '{$paramArray['school_id']}' and check_checknum > 0
                order by c.check_id DESC
                LIMIT {$pagestart},{$num}";
        $NextCourseList = $this->DataControl->selectClear($sql);

        if ($NextCourseList) {
            $status = $this->LgArraySwitch(array("0" => "手动盘点", "1" => "POS机盘点"));
            foreach ($NextCourseList as &$val) {
                $val['check_class'] = $status[$val['check_class']];
            }
        }

        $all_num = $this->DataControl->selectOne("
            SELECT
               COUNT(c.check_id) as a
            FROM
                erp_goods_check AS c left join smc_staffer as s on s.staffer_id = c.staffer_id
            where {$datawhere} and  c.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num['a'];

        $fieldstring = array('check_date', 'check_accounts', 'check_checknum', 'check_difference', 'staffer_cnname', 'check_class');
        $fieldname = array('盘点日期', '帐列数', '盘点数量', '差异数', '盘点人员', '盘点方式');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $result['all_num'] = $allnums;

            $res = array('error' => '0', 'errortip' => '获取库存盘点列表列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $result['all_num'] = $allnums;
            $res = array('error' => '1', 'errortip' => '获取库存盘点列表列表失败', 'result' => $result);
        }
        return $res;
    }

    //盘点明细
    function getCheckInfoApi($paramArray)
    {
        $sql = "
            SELECT
                c.check_date,
                c.check_class,
                s.staffer_cnname,
                c.check_accounts,
                c.check_checknum,
                c.check_difference
            FROM
                erp_goods_check AS c left join smc_staffer as s on s.staffer_id = c.staffer_id
            WHERE
                c.check_id = '{$paramArray['check_id']}'";
        $OrderBuyDetail = $this->DataControl->selectClear($sql);

        if ($OrderBuyDetail) {
            $status = $this->LgArraySwitch(array("0" => "手动盘点", "1" => "POS机盘点"));
            foreach ($OrderBuyDetail as &$val) {
                $val['check_class'] = $status[$val['check_class']];
            }
        }


        $field = array();
        $field["check_date"] = $this->LgStringSwitch("盘点日期");
        $field["check_class"] = $this->LgStringSwitch("盘点方式");
        $field["staffer_cnname"] = $this->LgStringSwitch("盘点人");
        $field["check_accounts"] = $this->LgStringSwitch("帐上合计");
        $field["check_checknum"] = $this->LgStringSwitch("盘点合计");
        $field["check_difference"] = $this->LgStringSwitch("差异合计");
        $result = array();
        if ($OrderBuyDetail) {
            $result["field"] = $field;
            $result["data"] = $OrderBuyDetail;
            $res = array('error' => '0', 'errortip' => '盘点明细查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '盘点明细查看失败', 'result' => $result);
        }
        return $res;
    }

    //盘点明细列表
    function getCheckInfoList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_pid like '%{$paramArray['keyword']}%' or g.goods_cnname like '%{$paramArray['keyword']}%')";
        }

        $sql = "SELECT
                    g.goods_pid,
                    g.goods_cnname,
                    d.checkdetail_accounts,
                    d.checkdetail_checknum,
                    d.checkdetail_reason 
                FROM
                    erp_goods_check_detail AS d
                    LEFT JOIN erp_goods AS g ON d.goods_pid = g.goods_pid 
                WHERE
                    {$datawhere} and d.check_id = '{$paramArray['check_id']}'
                group by d.checkdetail_id";
        $NextCourseList = $this->DataControl->selectClear($sql);

        if ($NextCourseList) {
            foreach ($NextCourseList as &$val) {
                $val['checkdetail_difference'] = abs($val['checkdetail_accounts'] - $val['checkdetail_checknum']);
            }
        }


        $fieldstring = array('goods_pid', 'goods_cnname', 'checkdetail_accounts', 'checkdetail_checknum', 'checkdetail_difference', 'checkdetail_reason');
        $fieldname = array('货品编号', '货品名称', '帐列数', '盘点数', '差异数', '差异原因');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;

            $res = array('error' => '0', 'errortip' => '获取盘点明细列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取盘点明细列表失败', 'result' => $result);
        }
        return $res;
    }

    //新建盘点
    function addCheckAction($paramArray)
    {
        $data = array();
        $data['check_date'] = $paramArray['check_date'];
        $data['check_class'] = $paramArray['check_class'];
        $data['school_id'] = $paramArray['school_id'];
        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['check_createtime'] = time();

        $field = array();
        $field['check_date'] = $this->LgStringSwitch("职务类型");
        $field['check_class'] = $this->LgStringSwitch("所属集团");

        if ($this->DataControl->insertData('erp_goods_check', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新建盘点成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新建盘点失败', 'result' => $result);
        }
        return $res;
    }

    //下载导入模版
    function getImportApi($paramArray)
    {
        $result = "http://gmcapi.kcclassin.com/importexcel/inventorydemo.xlsx";

        $res = array('error' => '0', 'errortip' => '盘点明细查看成功', 'result' => $result);

        return $res;
    }

    //改变盘点库存
    function UpdateCheckAction($paramArray)
    {

        $goodsList = json_decode(stripslashes($paramArray['goods']), true);
        $diff = 0;
        $account = 0;
        $check = 0;
        foreach ($goodsList as $item) {
            $data = array();
            $check_id = $item['check_id'];
            $data['checkdetail_checknum'] = $item['checkdetail_checknum'];
            $data['checkdetail_reason'] = $item['checkdetail_reason'];
            $data['checkdetail_accounts'] = $item['goods_repertory'];
            $this->DataControl->updateData('erp_goods_check_detail', "checkdetail_id = '{$item['checkdetail_id']}'", $data);

            $diff += $item['diff'];
            $account += $item['goods_repertory'];
            $check += $item['checkdetail_checknum'];

            $goods_pid = $this->DataControl->getFieldOne("erp_goods_check_detail", "goods_pid", "checkdetail_id = '{$item['checkdetail_id']}'");
            $goods_id = $this->DataControl->getFieldOne("erp_goods", "goods_id", "goods_pid = '{$goods_pid['goods_pid']}' and company_id = '{$paramArray['company_id']}'");
            $a = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "goods_id = '{$goods_id['goods_id']}' and school_id = '{$paramArray['school_id']}'");
            if ($a) {
                $data = array();
                $data['goods_repertory'] = $item['checkdetail_checknum'];
                $this->DataControl->updateData('smc_erp_goods_repertory', "goods_id = '{$goods_id['goods_id']}' and school_id = '{$paramArray['school_id']}'", $data);

                if ($a['goods_repertory'] > $item['checkdetail_checknum']) {
                    $changedata = array();
                    $changedata['company_id'] = $paramArray['company_id'];
                    $changedata['school_id'] = $paramArray['school_id'];
                    $changedata['goods_id'] = $goods_id['goods_id'];
                    $changedata['staffer_id'] = $paramArray['staffer_id'];
                    $changedata['changelog_class'] = '3';
                    $changedata['changelog_playname'] = '盘点出库';
                    $changedata['changelog_reason'] = '盘点出库';
                    $changedata['changelog_playclass'] = '-';
                    $changedata['changelog_fromnums'] = $a['goods_repertory'];
                    $changedata['changelog_playnums'] = $a['goods_repertory'] - $item['checkdetail_checknum'];
                    $changedata['changelog_finalnums'] = $item['checkdetail_checknum'];
                    $changedata['changelog_createtime'] = time();
                    $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
                } elseif ($a['goods_repertory'] < $item['checkdetail_checknum']) {

                    $changedata = array();
                    $changedata['company_id'] = $paramArray['company_id'];
                    $changedata['school_id'] = $paramArray['school_id'];
                    $changedata['goods_id'] = $goods_id['goods_id'];
                    $changedata['staffer_id'] = $paramArray['staffer_id'];
                    $changedata['changelog_class'] = '2';
                    $changedata['changelog_playname'] = '盘点入库';
                    $changedata['changelog_reason'] = '盘点入库';
                    $changedata['changelog_playclass'] = '+';
                    $changedata['changelog_fromnums'] = $a['goods_repertory'];
                    $changedata['changelog_playnums'] = $item['checkdetail_checknum'] - $a['goods_repertory'];
                    $changedata['changelog_finalnums'] = $item['checkdetail_checknum'];
                    $changedata['changelog_createtime'] = time();
                    $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
                }
            } else {
                $data = array();
                $data['goods_repertory'] = $item['checkdetail_checknum'];
                $data['company_id'] = $paramArray['company_id'];
                $data['school_id'] = $paramArray['school_id'];
                $data['goods_id'] = $goods_id['goods_id'];
                $data['goods_repertory'] = $item['checkdetail_checknum'];
                $this->DataControl->insertData("smc_erp_goods_repertory", $data);

                $changedata = array();
                $changedata['company_id'] = $paramArray['company_id'];
                $changedata['school_id'] = $paramArray['school_id'];
                $changedata['goods_id'] = $goods_id['goods_id'];
                $changedata['staffer_id'] = $paramArray['staffer_id'];
                $changedata['changelog_class'] = '2';
                $changedata['changelog_playname'] = '盘点入库';
                $changedata['changelog_reason'] = '盘点入库';
                $changedata['changelog_playclass'] = '+';
                $changedata['changelog_fromnums'] = '0';
                $changedata['changelog_playnums'] = $data['checkdetail_checknum'];
                $changedata['changelog_finalnums'] = $item['checkdetail_checknum'];
                $changedata['changelog_createtime'] = time();
                $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
            }
        }


        $data = array();
        $data['check_checknum'] = $check;
        $data['check_accounts'] = $account;
        $data['check_difference'] = $diff;
        $this->DataControl->updateData('erp_goods_check', "check_id = '{$check_id}'", $data);

        $res = array('error' => '0', 'errortip' => "改变盘点库存成功", 'result' => array());
        return $res;
    }

    //改变学校库存
    function changeRepertoryAction($paramArray)
    {
        $data = array();
        $goodsList = json_decode(stripslashes($paramArray['goods']), true);
        foreach ($goodsList as $item) {
            $goods = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "");
            $data['school_id'] = $item['school_id'];
            $data['organize_id'] = $item['organize_id'];
            $this->DataControl->insertData('gmc_company_organizeschool', $data);
        }

        $res = array('error' => '0', 'errortip' => "改变盘点库存成功", 'result' => array());
        return $res;
    }

    //POS机盘点
    function posCheckAction($paramArray)
    {
        $erp = $this->DataControl->getFieldOne("erp_goods", "goods_id", "goods_pid = '{$paramArray['goods_pid']}' and company_id = '{$paramArray['company_id']}'");
        if (!$erp) {
            ajax_return(array('error' => 1, 'errortip' => "集团不存在该商品!"), $this->companyOne['company_language']);
        }

//        $pos = $this->DataControl->getFieldOne("erp_goods_check","check_id","check_date = '{$paramArray['check_date']}'");

        if (!$paramArray['check_id']) {
            $data = array();
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['check_class'] = 1;
            $data['school_id'] = $paramArray['school_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['check_date'] = $paramArray['check_date'];
            $data['check_createtime'] = time();
            $check_id = $this->DataControl->insertData("erp_goods_check", $data);

            $data = array();
            $data['check_id'] = $check_id;
            $data['goods_pid'] = $paramArray['goods_pid'];
            $goods_id = $this->DataControl->getFieldOne("erp_goods", "goods_id", "goods_pid = '{$paramArray['goods_pid']}'");
            $accounts = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "goods_id = '{$goods_id['goods_id']}' and school_id = '{$paramArray['schol_id']}'");
            $data['checkdetail_accounts'] = $accounts['goods_repertory'];
            $data['checkdetail_checknum'] = '1';
            $data['check_createtime'] = time();
            $this->DataControl->insertData("erp_goods_check_detail", $data);

            $res = array('error' => '0', 'errortip' => "盘点成功", 'result' => $check_id);
        } else {
            $data = array();
            $data['check_id'] = $paramArray['check_id'];
            $data['goods_pid'] = $paramArray['goods_pid'];
            $goods_id = $this->DataControl->getFieldOne("erp_goods", "goods_id", "goods_pid = '{$paramArray['goods_pid']}'");
            $accounts = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "goods_id = '{$goods_id['goods_id']}' and school_id = '{$paramArray['school_id']}'");
            $data['checkdetail_accounts'] = $accounts['goods_repertory'];

            $goods = $this->DataControl->getFieldOne("erp_goods_check_detail", "checkdetail_id,checkdetail_checknum", "check_id = '{$paramArray['check_id']}' and goods_pid = '{$paramArray['goods_pid']}'");
            $data['check_createtime'] = time();
            if ($goods) {
                $data['checkdetail_checknum'] = $goods['checkdetail_checknum'] + 1;
                $this->DataControl->updateData("erp_goods_check_detail", "check_id = '{$paramArray['check_id']}' and goods_pid = '{$paramArray['goods_pid']}'", $data);

            } else {
                $data['checkdetail_checknum'] = '1';
                $this->DataControl->insertData("erp_goods_check_detail", $data);

            }

            $res = array('error' => '0', 'errortip' => "盘点成功", 'result' => $paramArray['check_id']);
        }

        return $res;
    }

    function changeCheckAction($paramArray)
    {
        $data = array();
        $data['checkdetail_checknum'] = $paramArray['checkdetail_checknum'];
        $this->DataControl->updateData("erp_goods_check_detail", "check_id = '{$paramArray['check_id']}' and goods_pid = '{$paramArray['goods_pid']}'", $data);
        $res = array('error' => '0', 'errortip' => "修改盘点数成功");
        return $res;

    }


    //盘点明细列表
    function getCheckGoodsList($paramArray)
    {
        if ($paramArray['check_id'] == '') {
            $NextCourseList = '';
        } else {
            $sql = "
                SELECT
                    g.goods_pid,
                    g.goods_cnname,
                    d.check_id,
                    d.checkdetail_id,
                    d.checkdetail_accounts,
                    d.checkdetail_checknum,
                    d.checkdetail_reason,
                    IFNULL(r.goods_repertory,0) as goods_repertory
                FROM
                    erp_goods_check_detail AS d
                    LEFT JOIN erp_goods AS g ON d.goods_pid = g.goods_pid
                    left join smc_erp_goods_repertory as r on r.goods_id = g.goods_id and r.school_id = '{$paramArray['school_id']}'
                WHERE
                    d.check_id = '{$paramArray['check_id']}' 
                GROUP BY
                    d.checkdetail_id";

            $NextCourseList = $this->DataControl->selectClear($sql);

            if ($NextCourseList) {
                foreach ($NextCourseList as &$val) {
                    $val['diff'] = abs($val['checkdetail_checknum'] - $val['goods_repertory']);
                }
            }
        }


        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_repertory', 'checkdetail_checknum', 'diff', 'checkdetail_reason');
        $fieldname = array('货品编号', '货品名称', '帐列数', '盘点数', '差异数', '差异原因');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $isinputnomal = array(false, false, false, true, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isinputnomal"] = trim($isinputnomal[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;

            $res = array('error' => '0', 'errortip' => '获取盘点明细列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无盘点明细列表', 'result' => $result);
        }
        return $res;
    }

    //自主采购下单申请
    function ApplyOwnBuyAction($paramArray)
    {
        $data = array();
        $data['ownbuy_pid'] = $this->createOutPid();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['ownbuy_createtime'] = time();

        $field = array();
        $field['ownbuy_pid'] = $this->LgStringSwitch("预约单名称");
        $field['company_id'] = $this->LgStringSwitch("所属集团");
        $field['school_id'] = $this->LgStringSwitch("所属学校");
        $field['staffer_id'] = $this->LgStringSwitch("申请人id");
        $field['ownbuy_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('smc_erp_ownbuy', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $StockList = json_decode(stripslashes($paramArray['set']), true);
            foreach ($StockList as $item) {
                $datas['ownbuy_pid'] = $data['ownbuy_pid'];
                $datas['goods_id'] = $item['goods_id'];
                $datas['ownbuygoods_buynums'] = $item['ownbuygoods_buynums'];

                $this->DataControl->insertData('smc_erp_ownbuy_goods', $datas);

            }

            $res = array('error' => '0', 'errortip' => "自主采购下单申请成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '自主采购下单申请失败', 'result' => $result);
        }
        return $res;
    }

    //借调下单申请
    function ApplytransferbuyAction($paramArray)
    {
        $data = array();
        $data['transferbuy_pid'] = $this->createOutPid();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['from_school_id'] = $paramArray['from_school_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['transferbuy_returndate'] = $paramArray['date'];
        $data['transferbuy_createtime'] = time();

        $field = array();
        $field['ownbuy_pid'] = $this->LgStringSwitch("预约单名称");
        $field['company_id'] = $this->LgStringSwitch("所属集团");
        $field['school_id'] = $this->LgStringSwitch("所属学校");
        $field['staffer_id'] = $this->LgStringSwitch("申请人id");
        $field['transferbuy_returndate'] = $this->LgStringSwitch("预计归还日期");
        $field['transferbuy_createtime'] = $this->LgStringSwitch("创建时间");

        if ($id = $this->DataControl->insertData('smc_erp_transferbuy', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $StockList = json_decode(stripslashes($paramArray['set']), true);
            foreach ($StockList as $item) {
                $datas['transferbuy_pid'] = $data['transferbuy_pid'];
                $datas['goods_id'] = $item['goods_id'];
                $datas['transferbuygoods_buynums'] = $item['transferbuygoods_buynums'];

                $this->DataControl->insertData('smc_erp_transferbuy_goods', $datas);

            }

            $res = array('error' => '0', 'errortip' => "借调申请成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '借调申请失败', 'result' => $result);
        }
        return $res;
    }

    //查看采购商品列表
    function getOwnBuyGoods($paramArray)
    {
        $sql = "SELECT
                    s.goods_pid,s.goods_cnname,g.proogoods_buynums,s.goods_vipprice
                FROM
                    erp_proorder_goods AS g
                    LEFT JOIN erp_proorder AS p ON g.proorder_pid = p.proorder_pid 
                    left join erp_goods as s on g.goods_id = s.goods_id
                WHERE
                    p.proorder_outpid = '{$paramArray['proorder_outpid']}'";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid', 'goods_cnname', 'goods_vipprice', 'proogoods_buynums');
        $fieldname = array('K3货号', '名称', '价格', '数量');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '查看采购商品列表成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看采购商品列表失败', 'result' => $result);
        }
        return $res;
    }

    //载入入库单列表
    function getErpinorderList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.proorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and o.outorder_time >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and o.outorder_time <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                o.outorder_id,
                o.outorder_pid,
                o.outorder_time,
                sum( g.outordergoods_buynums ) AS count,
	            g.proorder_pid
            FROM
                smc_erp_outorder AS o
                LEFT JOIN smc_erp_outorder_goods AS g ON o.outorder_pid = g.outorder_pid 
            where {$datawhere} and o.school_id = '{$paramArray['school_id']}' and o.company_id = '{$paramArray['company_id']}'
            GROUP BY
                o.outorder_pid 
            ORDER BY
                o.outorder_id    
            LIMIT {$pagestart},{$num}";

        $beinorderList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
           select count(*) as a from (SELECT
                o.outorder_id,
                o.outorder_pid,
                o.outorder_time,
                sum( g.outordergoods_buynums ) AS count,
	            g.proorder_pid
            FROM
                smc_erp_outorder AS o
                LEFT JOIN smc_erp_outorder_goods AS g ON o.outorder_pid = g.outorder_pid 
            where {$datawhere} and o.school_id = '{$paramArray['school_id']}' and o.company_id = '{$paramArray['company_id']}'
            GROUP BY
                o.outorder_pid 
            ORDER BY
                o.outorder_id) as b");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('outorder_pid ', 'proorder_pid ', 'outorder_time', 'count');
        $fieldname = array('ERP出库单号', '采购单号', '采购日期', '商品数量');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($beinorderList) {
            $result['list'] = $beinorderList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        if (!$beinorderList) {
            $res = array('error' => '1', 'errortip' => "暂无入库单信息", 'result' => $result);
        }

        return $res;
    }

    //erp出库单信息
    function getRrpOutorderInfo($paramArray)
    {
        $sql = "
             SELECT
                o.outorder_id,
                o.outorder_pid,
                o.outorder_time,
                sum( g.outordergoods_buynums ) AS count,
	            g.proorder_pid,
	            s.school_cnname
            FROM
                smc_erp_outorder AS o
                LEFT JOIN smc_erp_outorder_goods AS g ON o.outorder_pid = g.outorder_pid
                left join smc_school as s on s.school_id = o.school_id
            where o.outorder_id = '{$paramArray['outorder_id']}'
            GROUP BY
                o.outorder_pid 
            ORDER BY
                o.outorder_id";
        $beinorderList = $this->DataControl->selectClear($sql);

        $field["outorder_pid"] = $this->LgStringSwitch("ERP出库单号");
        $field["proorder_pid"] = $this->LgStringSwitch("采购单号");
        $field["outorder_time"] = $this->LgStringSwitch("采购日期");
        $field["count"] = $this->LgStringSwitch("出库单数量");
        $field["school_cnname"] = $this->LgStringSwitch("采购分校");

        $result = array();
        if ($beinorderList) {
            $result["field"] = $field;
            $result["data"] = $beinorderList;
            $res = array('error' => '0', 'errortip' => '获取erp出库单信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取erp出库单信息失败', 'result' => $result);
        }
        return $res;
    }

    //查看ERP出库单商品明细
    function getErpOutGoods($paramArray)
    {
        $sql = "SELECT
                    s.goods_pid,s.goods_cnname,g.outordergoods_buynums,s.goods_vipprice
                FROM
                    smc_erp_outorder_goods AS g
                    left join erp_goods as s on g.goods_id = s.goods_id
                WHERE
                    g.outorder_pid = '{$paramArray['outorder_pid']}'";
        $NextCourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid', 'goods_cnname', 'outordergoods_buynums', 'goods_vipprice');
        $fieldname = array('K3货号', '名称', '数量', '单价');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $count = $this->DataControl->selectOne("
            SELECT
                sum( outordergoods_buynums ) AS count 
            FROM
                smc_erp_outorder_goods 
            WHERE
                outorder_pid = '{$paramArray['outorder_pid']}'");

        $price = $this->DataControl->selectOne("
            SELECT
                sum( g.outordergoods_buynums * s.goods_vipprice ) AS price 
            FROM
                smc_erp_outorder_goods as g left join erp_goods as s on g.goods_id = s.goods_id
            WHERE
                g.outorder_pid = '{$paramArray['outorder_pid']}'");

        $result = array();
        $result['count'] = $count['count'];
        $result['price'] = $price['price'];
        if ($NextCourseList) {
            $result["field"] = $field;
            $result["data"] = $NextCourseList;
            $res = array('error' => '0', 'errortip' => '查看ERP出库单商品明细成功', 'result' => $result);
        } else {
            $result["field"] = $field;
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看ERP出库单商品明细失败', 'result' => $result);
        }
        return $res;
    }

    //添加至入库单
    function addBeinorderAction($paramArray)
    {

        $beinorderList = json_decode(stripslashes($paramArray['beinorder']), true);
        foreach ($beinorderList as $item) {
            $data = array();
            $data['beinorder_pid'] = $this->createOrderPid('RC');
            $data['school_id'] = $paramArray['school_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['outorder_pid'] = $item['outorder_pid'];
            $data['beinorder_createtime'] = time();
            $this->DataControl->insertData('smc_erp_beinorder', $data);

            $datass = array();
            $datass['beinorder_pid'] = $data['beinorder_pid'];
            $datass['tracks_title'] = $this->LgStringSwitch('同步入库单');
            $datass['tracks_information'] = $this->LgStringSwitch('从ERP同步入库单');
            $datass['staffer_id'] = $paramArray['staffer_id'];
            $tracks_playname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
            $datass['tracks_playname'] = $tracks_playname['staffer_cnname'];
            $datass['tracks_createtime'] = time();
            $this->DataControl->insertData('smc_erp_beinorder_tracks', $datass);

            $goodsList = $this->DataControl->selectClear("select * from smc_erp_outorder_goods where outorder_pid = '{$item['outorder_pid']}'");
            foreach ($goodsList as $items) {
                $datas = array();
                $datas['beinorder_pid'] = $data['beinorder_pid'];
                $datas['proorder_pid'] = $items['proorder_pid'];
                $datas['goods_id'] = $items['goods_id'];
                $datas['beinordergoods_buynums'] = $items['outordergoods_buynums'];
                $this->DataControl->insertData('smc_erp_beinorder_goods', $datas);

            }
        }

        $res = array('error' => '0', 'errortip' => "添加至入库单成功", 'result' => array());

        return $res;
    }

    //入库
    function InorderAction($paramArray)
    {

        $data = array();
        $data['beinorder_status'] = 2;
        $data['beinorder_updatetime'] = time();
        $data['beinorder_storagetime'] = time();

        $this->DataControl->updateData("smc_erp_beinorder", "beinorder_pid = '{$paramArray['beinorder_pid']}'", $data);

        $pid = $this->DataControl->getFieldOne("smc_erp_beinorder", "proorder_pid,from_school_id", "beinorder_pid = '{$paramArray['beinorder_pid']}'");

        $status = $this->DataControl->getFieldOne("erp_proorder", "proorder_status,proorder_from,to_school_id", "proorder_pid = '{$pid['proorder_pid']}'");

        $goodsList = $this->DataControl->selectClear("select b.goods_id,b.beinordergoods_buynums,g.goods_originalprice,g.goods_vipprice from smc_erp_beinorder_goods as b left join erp_goods as g on b.goods_id = g.goods_id WHERE b.beinorder_pid = '{$paramArray['beinorder_pid']}'");

        if ($status['proorder_status'] == '2' && $status['proorder_from'] == '4' && $status['to_school_id'] == $paramArray['school_id']) {

            $dataSale = array();
            $dataSale['company_id'] = $paramArray['company_id'];
            $dataSale['salesorder_pid'] = $this->createOrderPid('XH');
            $dataSale['school_id'] = $paramArray['school_id'];
            $dataSale['to_school_id'] = $pid['from_school_id'];
            $dataSale['proorder_pid'] = $pid['proorder_pid'];
            $dataSale['salesorder_from'] = '1';
            $dataSale['salesorder_createtime'] = time();

            $this->DataControl->insertData('smc_erp_salesorder', $dataSale);

            $datas = array();

            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['salesorder_pid'] = $dataSale['salesorder_pid'];
                $datas['salesordergoods_buynums'] = $item['beinordergoods_buynums'];
                $datas['proorder_pid'] = $pid['proorder_pid'];
                $this->DataControl->insertData('smc_erp_salesorder_goods', $datas);
            }

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
            $dataTracks = array();
            $dataTracks['tracks_title'] = $this->LgStringSwitch('生成销货单');
            $dataTracks['tracks_information'] = $this->LgStringSwitch('归还借调的物品');
            $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
            $dataTracks['salesorder_pid'] = $dataSale['salesorder_pid'];
            $dataTracks['tracks_createtime'] = time();

            $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);
        }

        foreach ($goodsList as $item) {
            $data = array();
            $data['goods_id'] = $item['goods_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['school_id'] = $paramArray['school_id'];
            $a = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_id,goods_repertory", "goods_id = '{$item['goods_id']}' and school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}' ");
            if ($a) {
                $data['goods_repertory'] = $item['beinordergoods_buynums'] + $a['goods_repertory'];
                $this->DataControl->updateData("smc_erp_goods_repertory", "goods_id = '{$item['goods_id']}' and school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}'", $data);
                $changedata = array();
                $changedata['company_id'] = $paramArray['company_id'];
                $changedata['school_id'] = $paramArray['school_id'];
                $changedata['goods_id'] = $item['goods_id'];
                $changedata['staffer_id'] = $paramArray['staffer_id'];
                $changedata['changelog_class'] = '0';
                $changedata['changelog_playname'] = '审核入库';
                $changedata['changelog_reason'] = '审核入库';
                $changedata['changelog_playclass'] = '+';
                $changedata['changelog_fromnums'] = $a['goods_repertory'];
                $changedata['changelog_playnums'] = $item['beinordergoods_buynums'];
                $changedata['changelog_finalnums'] = $item['beinordergoods_buynums'] + $a['goods_repertory'];
                $changedata['changelog_createtime'] = time();
                $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
            } else {
                $data['goods_repertory'] = $item['beinordergoods_buynums'];
                $this->DataControl->insertData('smc_erp_goods_repertory', $data);
                $changedata = array();
                $changedata['company_id'] = $paramArray['company_id'];
                $changedata['school_id'] = $paramArray['school_id'];
                $changedata['goods_id'] = $item['goods_id'];
                $changedata['staffer_id'] = $paramArray['staffer_id'];
                $changedata['changelog_class'] = '0';
                $changedata['changelog_playname'] = '审核入库';
                $changedata['changelog_reason'] = '审核入库';
                $changedata['changelog_playclass'] = '+';
                $changedata['changelog_fromnums'] = '0';
                $changedata['changelog_playnums'] = $item['beinordergoods_buynums'];
                $changedata['changelog_finalnums'] = $item['beinordergoods_buynums'];
                $changedata['changelog_createtime'] = time();
                $this->DataControl->insertData('smc_erp_goods_changelog', $changedata);
            }
        }

        $pid = $this->DataControl->getFieldOne("smc_erp_beinorder", "proorder_pid", "beinorder_pid = '{$paramArray['beinorder_pid']}'");
        $from = $this->DataControl->getFieldOne("erp_proorder", "proorder_from", "proorder_pid = '{$pid['proorder_pid']}'");

        if ($from['proorder_from'] == '4' && $status['to_school_id'] == $paramArray['school_id']) {
            $data = array();
            $data['proorder_status'] = 4;
            $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$pid['proorder_pid']}'", $data);
        } else {
            $data = array();
            $data['proorder_status'] = 3;
            $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$pid['proorder_pid']}'", $data);
        }

        $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
        $dataTracks = array();
        $dataTracks['tracks_title'] = $this->LgStringSwitch('采购单货品入库');
        $dataTracks['tracks_information'] = $this->LgStringSwitch("采购单货品入库，入库单号:" . $paramArray['beinorder_pid']);
        $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
        $dataTracks['proorder_pid'] = $pid['proorder_pid'];
        $dataTracks['tracks_createtime'] = time();

        $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

        $res = array('error' => '0', 'errortip' => "入库成功");

        return $res;
    }

    //借出商品列表
    function getChangeGoodsLists($paramArray)
    {

        $datawhere = "1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or  g.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_cnname,
                g.goods_vipprice as goods_price,
                g.goods_originalprice,
                g.goods_unit,
                g.goods_pid,
                r.goods_repertory,
                p.prodtype_name,
                r.goods_id,
                (select re.goods_repertory from smc_erp_goods_repertory as re where re.goods_id = r.goods_id and re.school_id = '{$paramArray['school_id']}') as renum
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code and p.company_id = '{$paramArray['company_id']}'
                WHERE {$datawhere} and r.school_id = '{$paramArray['from_school_id']}'
            ORDER BY
                r.repertory_id DESC 
                LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*)
            FROM
               smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code and p.company_id = '{$paramArray['company_id']}'
                WHERE {$datawhere} and r.school_id = '{$paramArray['from_school_id']}'");
        $allnums = $all_num[0][0];

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['number'] = 1;
                if (!$val['renum']) {
                    $val['renum'] = '0';
                }
            }
        }

        $fieldstring = array('goods_cnname', 'goods_pid', 'goods_unit', 'prodtype_name', 'goods_originalprice', 'goods_price', 'renum', 'goods_repertory', 'number');
        $fieldname = array('货品名称', '货品编号', '单位', '类别', '成本价', '协议价', '本校库存', '借出校库存', '采购数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "0");
        $isInputNumber = array(false, false, false, false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isInputNumber"] = trim($isInputNumber[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无借调货品信息", 'result' => $result);
        }

        return $res;
    }

    //自主采购可选商品列表
    function getOwnBuyoodsList($paramArray)
    {

        $datawhere = "1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or  g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['goods_isintegral']) && $paramArray['goods_isintegral'] !== "") {
            $datawhere .= " and g.goods_isintegral ='{$paramArray['goods_isintegral']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_cnname,
                g.goods_vipprice as goods_price,
                g.goods_originalprice,
                g.goods_isintegral,
                g.goods_integral,
                g.goods_unit,
                g.goods_pid,
                p.prodtype_name,
                g.goods_id,
                r.goods_repertory
            FROM
                erp_goods AS g
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
                left join smc_erp_goods_repertory as r on r.goods_id = g.goods_id and r.school_id = '{$paramArray['school_id']}'
                WHERE {$datawhere} and g.company_id = '{$paramArray['company_id']}'
	        GROUP BY g.goods_id 
            ORDER BY
                g.goods_id DESC
                LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(DISTINCT g.goods_id)
            FROM
                 erp_goods AS g
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
                WHERE {$datawhere} and g.company_id = '{$paramArray['company_id']}'");
        $allnum = $all_num[0][0];

        if ($goodsList) {
            $status = array("0" => "否", "1" => "是");

            foreach ($goodsList as &$val) {
                $val['number'] = 1;
                if (!$val['goods_repertory']) {
                    $val['goods_repertory'] = '0';
                }
                $val['goods_isintegral'] = $status[$val['goods_isintegral']];
                if ($val['goods_integral']) {
                    $val['goods_integral'] = $val['goods_integral'];
                } else {
                    $val['goods_integral'] = '--';
                }

            }
        }

        $fieldstring = array('goods_cnname', 'goods_pid', 'goods_unit', 'prodtype_name', 'goods_isintegral', 'goods_originalprice', 'goods_price', 'goods_integral', 'goods_repertory', 'number');
        $fieldname = array('货品名称', '货品编号', '单位', '类别', '是否积分商品', '成本价', '协议价', '兑换积分', '本校库存', '采购数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $isInputNumber = array(false, false, false, false, false, false, false, false, false, true);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isInputNumber"] = trim($isInputNumber[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnum;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无货品信息", 'result' => $result);
        }

        return $res;
    }

    //销货单列表 by:qyh
    function getSalesorderList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.salesorder_pid like '%{$paramArray['keyword']}%' or s.proorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['salesorder_from']) && $paramArray['salesorder_from'] !== "") {
            $datawhere .= " and s.salesorder_from ={$paramArray['salesorder_from']}";
        }
        if (isset($paramArray['salesorder_status']) && $paramArray['salesorder_status'] !== "") {
            $datawhere .= " and s.salesorder_status ={$paramArray['salesorder_status']}";
        }
        if (isset($paramArray['to_school_id']) && $paramArray['to_school_id'] !== "") {
            $datawhere .= " and s.to_school_id ={$paramArray['to_school_id']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( salesorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( salesorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.salesorder_id,
                s.salesorder_pid,
                s.salesorder_from,
                s.salesorder_from as salesorder_from_num,
                s.to_school_id,
                s.proorder_pid,
                ( SELECT sum( pg.proogoods_buynums ) FROM erp_proorder_goods AS pg WHERE pg.proorder_pid = s.proorder_pid ) AS num,
                ( SELECT sum( sg.salesordergoods_buynums ) FROM smc_erp_salesorder_goods AS sg WHERE sg.salesorder_pid = s.salesorder_pid ) AS salenum,
                p.proorder_allprice,
                p.proorder_status,
                p.to_school_id,
                p.from_school_id,
                FROM_UNIXTIME( s.salesorder_createtime, '%Y-%m-%d' ) AS salesorder_createtime,
                s.salesorder_status,
                sc.school_shortname as to_school_cnname,
                scc.school_shortname as from_school_cnname,
                s.salesorder_status as salesorder_status_name
            FROM
                smc_erp_salesorder AS s
                LEFT JOIN erp_proorder AS p ON s.proorder_pid = p.proorder_pid
                left join smc_school as sc on sc.school_id = s.to_school_id
                left join smc_school as scc on scc.school_id = s.school_id
            where {$datawhere} and s.school_id = '{$paramArray['school_id']}'
            order by s.salesorder_id desc
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "调拨", "1" => "借调", "2" => "领用", "3" => "报损"));
            $statuss = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "待审核", "1" => "待发货", "2" => "待出库", "3" => "已出库", "4" => "已完成"));
            foreach ($goodsList as &$val) {
                $val['salesorder_from'] = $status[$val['salesorder_from']];
                $val['salesorder_status_name'] = $statuss[$val['salesorder_status_name']];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(s.salesorder_id) as a
            FROM
                smc_erp_salesorder AS s
            where {$datawhere} and s.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无销货单数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['all_num'] = $allnums;
        return $data;
    }


    /**
     * 库存管理 -- 获取单个销货单信息
     * @by qyh
     * @param $paramArray
     * @return array
     */
    function getSalesorderOneApi($paramArray)
    {
        $sql = "
            SELECT
                s.salesorder_id,
                s.salesorder_pid,
                s.salesorder_from,
                s.salesorder_from as salesorder_from_name,
                s.proorder_pid,
                ( SELECT sum( pg.salesordergoods_buynums ) FROM smc_erp_salesorder_goods AS pg WHERE pg.salesorder_pid = s.salesorder_pid ) AS num,
                p.proorder_allprice,
                FROM_UNIXTIME( s.salesorder_createtime, '%Y-%m-%d' ) AS salesorder_createtime,
                s.salesorder_status,
                s.salesorder_status as salesorder_status_num,
                c.school_shortname as to_school_cnname,
                cc.school_shortname as from_school_cnname,
                t.staffer_cnname,
                sum(beoutorder_buynums) as outnum,
                p.proorder_returndate as salesorder_reviewtime
            FROM
                smc_erp_salesorder AS s
                LEFT JOIN erp_proorder AS p ON s.proorder_pid = p.proorder_pid 
                left join smc_school as c on p.to_school_id = c.school_id
                left join smc_school as cc on p.from_school_id = cc.school_id
                left join smc_staffer as t on p.staffer_id = t.staffer_id
                left join smc_erp_beoutorder as b on b.salesorder_pid = s.salesorder_pid and b.beoutorder_status = '2'
                left join smc_erp_beoutorder_goods as bg on bg.beoutorder_pid = b.beoutorder_pid
            WHERE
                s.salesorder_pid = '{$paramArray['salesorder_pid']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "调拨", "1" => "借调", "2" => "领用", "3" => "报损"));
            $statuss = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "待审核", "1" => "待发货", "2" => "待出库", "3" => "已出库", "4" => "已完成"));
            foreach ($goodsList as &$val) {
                $val['salesorder_from_name'] = $status[$val['salesorder_from_name']];
                $val['salesorder_status'] = $statuss[$val['salesorder_status']];
            }
        }

        $field = array();
        $field["salesorder_pid"] = $this->LgStringSwitch("销货单号");
        $field["salesorder_from_name"] = $this->LgStringSwitch("销货类型");
        $field["salesorder_from"] = $this->LgStringSwitch("销货类型的数字");
        $field["proorder_pid"] = $this->LgStringSwitch("采购单号");
        $field["num"] = $this->LgStringSwitch("销货货品数量");
        $field["proorder_allprice"] = $this->LgStringSwitch("销货金额");
        $field["to_school_cnname"] = $this->LgStringSwitch("调入园所");
        $field["from_school_cnname"] = $this->LgStringSwitch("调出园所");
        $field["salesorder_createtime"] = $this->LgStringSwitch("申请日期");
        $field["salesorder_reviewtime"] = $this->LgStringSwitch("审核日期");
        $field["staffer_cnname"] = $this->LgStringSwitch("申请人");
        $field["salesorder_status"] = $this->LgStringSwitch("状态");

        $result = array();
        if ($goodsList) {
            $result["field"] = $field;
            $result["data"] = $goodsList;
            $res = array('error' => '0', 'errortip' => '获取单个销货单信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取单个销货单信息失败', 'result' => $result);
        }
        return $res;
    }

    /**
     * 库存管理 -- 获取单个领用报损销货单信息
     * @by qyh
     * @param $paramArray
     * @return array
     */
    function getOutSalesorderOneApi($paramArray)
    {
        $sql = "
            SELECT
                s.salesorder_id,
                s.salesorder_pid,
                s.salesorder_from,
                s.salesorder_from AS salesorder_from_name,
                ( SELECT sum( pg.salesordergoods_buynums ) FROM smc_erp_salesorder_goods AS pg WHERE pg.salesorder_pid = s.salesorder_pid ) AS num,
                FROM_UNIXTIME( s.salesorder_createtime, '%Y-%m-%d' ) AS salesorder_createtime,
                s.salesorder_status,
                s.salesorder_status AS salesorder_status_num
            FROM
                smc_erp_salesorder AS s
            WHERE
                s.salesorder_pid = '{$paramArray['salesorder_pid']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "调拨", "1" => "借调", "2" => "领用", "3" => "报损"));
            $statuss = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "待审核", "1" => "待发货", "2" => "待出库", "3" => "已出库", "4" => "已完成"));
            foreach ($goodsList as &$val) {
                $val['salesorder_from_name'] = $status[$val['salesorder_from_name']];
                $val['salesorder_status'] = $statuss[$val['salesorder_status']];
            }
        }

        $field = array();
        $field["salesorder_pid"] = $this->LgStringSwitch("销货单号");
        $field["salesorder_from_name"] = $this->LgStringSwitch("销货类型");
        $field["salesorder_from"] = $this->LgStringSwitch("销货类型的数字");
        $field["proorder_pid"] = $this->LgStringSwitch("采购单号");
        $field["num"] = $this->LgStringSwitch("销货货品数量");
        $field["proorder_allprice"] = $this->LgStringSwitch("销货金额");
        $field["to_school_cnname"] = $this->LgStringSwitch("调入园所");
        $field["from_school_cnname"] = $this->LgStringSwitch("调出园所");
        $field["salesorder_createtime"] = $this->LgStringSwitch("申请日期");
        $field["salesorder_reviewtime"] = $this->LgStringSwitch("审核日期");
        $field["staffer_cnname"] = $this->LgStringSwitch("申请人");
        $field["salesorder_status"] = $this->LgStringSwitch("状态");

        $result = array();
        if ($goodsList) {
            $result["field"] = $field;
            $result["data"] = $goodsList;
            $res = array('error' => '0', 'errortip' => '获取单个销货单信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取单个销货单信息失败', 'result' => $result);
        }
        return $res;
    }

    //销货货品明细 by:qyh
    function getSalesorderGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                g.goods_id,
                g.goods_pid,
                g.goods_cnname,
                g.goods_unit,
                g.goods_vipprice,
                p.prodtype_name,
                sg.salesordergoods_id,
                sg.salesordergoods_buynums,
                sg.salesordergoods_sendnums,
                (select r.goods_repertory from smc_erp_goods_repertory as r where r.goods_id = sg.goods_id and r.school_id = '{$paramArray['school_id']}') as repnum
            FROM
                smc_erp_salesorder_goods AS sg
                LEFT JOIN erp_goods AS g ON sg.goods_id = g.goods_id
                LEFT JOIN smc_code_prodtype AS p ON g.prodtype_code = p.prodtype_code
            where {$datawhere} and  sg.salesorder_pid = '{$paramArray['salesorder_pid']}'
            group by g.goods_pid
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = count($goodsList);

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['price'] = $val['salesordergoods_buynums'] * $val['goods_vipprice'];
                $val['restnum'] = $val['salesordergoods_buynums'] - $val['salesordergoods_sendnums'];
                $val['goodsNum'] = 0;
            }
        }


        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无货品数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['all_num'] = $all_num;
        return $data;
    }

    function getSalesorderTracks($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.tracks_title like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                t.tracks_id,
                t.tracks_title,
                t.tracks_playname,
                t.tracks_information,
                FROM_UNIXTIME( t.tracks_createtime, '%Y-%m-%d' ) AS tracks_createtime
            FROM
                erp_salesorder_tracks AS t
            where {$datawhere} and t.salesorder_pid = '{$paramArray['salesorder_pid']}'
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $allnum = count($goodsList);

        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无跟踪数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['all_num'] = $allnum;
        return $data;
    }

    /**
     * 库存管理->审核销货单（借调，调拨） by:qyh
     * @by qyh
     * @param $paramArray
     * @return array
     */
    function salesorderStatusAction($paramArray)
    {
        $postbe = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "staffer_id = '{$paramArray['staffer_id']}' and school_id = '{$paramArray['school_id']}'");
        $topjob = $this->DataControl->getFieldOne("gmc_company_post", "post_istopjob", "post_id = '{$postbe['post_id']}'");
        if ($topjob['post_istopjob'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => "您没有权限，请联系校长操作!"), $this->companyOne['company_language']);
        }
        $salesorderOne = $this->DataControl->getFieldOne("smc_erp_salesorder", "salesorder_pid,proorder_pid", "salesorder_pid = '{$paramArray['salesorder_pid']}'");

        if ($salesorderOne) {
            $data = array();
            $data['salesorder_status'] = $paramArray['salesorder_status'];
            $data['salesorder_remark'] = $paramArray['salesorder_remark'];
            $data['salesorder_updatetime'] = time();
            $data['salesorder_reviewtime'] = time();

            if ($paramArray['salesorder_status'] == '-1') {
                $school = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$paramArray['school_id']}'");
                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $dataTracks = array();
                $dataTracks['tracks_title'] = $this->LgStringSwitch($school['school_cnname'] . '审核拒绝');
                $dataTracks['tracks_information'] = $this->LgStringSwitch('拒绝原因：' . $paramArray['salesorder_remark']);
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['proorder_pid'] = $salesorderOne['proorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);
            }

            $field = array();
            $field['salesorder_status'] = $this->LgStringSwitch('状态');
            $field['salesorder_remark'] = $this->LgStringSwitch('备注');

            if ($this->DataControl->updateData("smc_erp_salesorder", "salesorder_pid = '{$paramArray['salesorder_pid']}'", $data)) {

                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $dataTracks = array();
                if ($paramArray['salesorder_status'] == '1') {
                    $dataTracks['tracks_title'] = $this->LgStringSwitch('销货单审核通过');
                } else {
                    $dataTracks['tracks_title'] = $this->LgStringSwitch('销货单审核拒绝');
                }
                $dataTracks['tracks_information'] = $this->LgStringSwitch($paramArray['salesorder_remark']);
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['salesorder_pid'] = $paramArray['salesorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "审核销货单成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '审核销货单失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    /**
     * 库存管理->发货 by:qyh
     * @by qyh
     * @param $paramArray
     * @return array
     */
    function sendGoodsAction($paramArray)
    {
        $ClassTypeOne = $this->DataControl->getFieldOne("smc_erp_salesorder", "salesorder_pid,salesorder_from,proorder_pid", "salesorder_pid = '{$paramArray['salesorder_pid']}'");

        $pro = $this->DataControl->getFieldOne("erp_proorder", "proorder_status,from_school_id", "proorder_pid = '{$paramArray['proorder_pid']}'");

        if ($ClassTypeOne) {

            $datas = array();
            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas['salesordergoods_sendnums'] = $item['salesordergoods_sendnums'] + $item['sendnum'];
                $this->DataControl->updateData("smc_erp_salesorder_goods", "salesordergoods_id = '{$item['salesordergoods_id']}'", $datas);
            }
            $data = array();

            $num = $this->DataControl->selectOne("select sum(sg.salesordergoods_buynums - sg.salesordergoods_sendnums) as num from smc_erp_salesorder_goods as sg WHERE sg.salesorder_pid = '{$paramArray['salesorder_pid']}'");
            if ($num['num'] == '0') {
                $data['salesorder_status'] = '2';
            } else {
                $data['salesorder_status'] = '1';
            }

            $data['salesorder_updatetime'] = time();

            $field = array();
            $field['salesorder_status'] = $this->LgStringSwitch('状态');

            if ($this->DataControl->updateData("smc_erp_salesorder", "salesorder_pid = '{$paramArray['salesorder_pid']}'", $data)) {


                $datass = array();
                $datass['beoutorder_pid'] = $this->createOrderPid('CK');
                $datass['company_id'] = $paramArray['company_id'];
                $datass['school_id'] = $paramArray['school_id'];
                if ($pro['proorder_status'] == '4') {
                    $datass['to_school_id'] = $pro['from_school_id'];
                } else {
                    $datass['to_school_id'] = $paramArray['to_school_id'];
                }


                $datass['staffer_id'] = $paramArray['staffer_id'];
                $datass['salesorder_pid'] = $paramArray['salesorder_pid'];
                $datass['proorder_pid'] = $paramArray['proorder_pid'];
                $datass['beoutorder_createtime'] = time();
                $this->DataControl->insertData("smc_erp_beoutorder", $datass);

                $datasss = array();
                $goodsList = json_decode(stripslashes($paramArray['goods']), true);
                foreach ($goodsList as $items) {
                    $datasss['beoutorder_pid'] = $datass['beoutorder_pid'];
                    $datasss['goods_id'] = $items['goods_id'];
                    $datasss['beoutorder_buynums'] = $items['sendnum'];
                    $this->DataControl->insertData("smc_erp_beoutorder_goods", $datasss);
                }

                $data = array();
                $data['proorder_status'] = '1';
                $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$paramArray['proorder_pid']}'", $data);

                if ($pro['proorder_status'] == '4') {
                    $data = array();
                    $data['proorder_status'] = '5';
                    $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$paramArray['proorder_pid']}'", $data);
                }

                $data = array();
                $data['beoutorder_pid'] = $datass['beoutorder_pid'];
                $data['staffer_id'] = $paramArray['staffer_id'];
                $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $data['tracks_playname'] = $name['staffer_cnname'];
                $data['tracks_information'] = $this->LgStringSwitch('生成出库单');
                $data['tracks_createtime'] = time();
                $this->DataControl->insertData("smc_erp_beoutorder_tracks", $data);

                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $dataTracks = array();
                $dataTracks['tracks_title'] = $this->LgStringSwitch('销货单货品发货');
                $dataTracks['tracks_information'] = $this->LgStringSwitch('销货单货品发货，出库单号：' . $datass['beoutorder_pid']);
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['salesorder_pid'] = $paramArray['salesorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "发货成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '发货失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //采购单详情
    function getProorderDetail($paramArray)
    {
        $sql = "
        SELECT
            s.staffer_cnname,
            s.staffer_sex,
            s.staffer_mobile,
            s.staffer_img,
            s.staffer_enname,
            c.post_name
        FROM
            smc_staffer AS s
            LEFT JOIN gmc_staffer_postbe AS b ON s.staffer_id = b.staffer_id 
            left join gmc_company_post as c on c.post_id = b.post_id
        WHERE
            s.company_id = '{$paramArray['company_id']}'
        GROUP BY
            s.staffer_id";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_cnname"] = $this->LgStringSwitch("中文名");
        $field["staffer_sex"] = $this->LgStringSwitch("性别");
        $field["staffer_mobile"] = $this->LgStringSwitch("手机号");
        $field["post_name"] = $this->LgStringSwitch("职务");
        $field["staffer_img"] = $this->LgStringSwitch("头像");
        $field["staffer_enname"] = $this->LgStringSwitch("英文名");

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取集团联系人成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取集团联系人失败', 'result' => $result);
        }
        return $res;
    }

    //积分兑换明细列表
    function getIntegralgoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%' or g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%' or g.prodtype_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['integralgoods_status']) && $paramArray['integralgoods_status'] !== "") {
            $datawhere .= " and i.integralgoods_status ='{$paramArray['integralgoods_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.student_cnname,
                s.student_enname,
                s.student_branch,
                g.goods_cnname,
                g.goods_pid,
                g.prodtype_code,
                g.goods_vipprice,
                i.student_id,
                i.goods_id,
                i.integralgoods_id,
                i.integralgoods_score,
                i.integralgoods_number,
                FROM_UNIXTIME( i.integralgoods_createtime, '%Y-%m-%d %H:%i:%s' ) AS integralgoods_createtime,
                i.integralgoods_status,
                i.integralgoods_status as integralgoods_status_name
            FROM
                smc_student_integralgoods AS i
                LEFT JOIN smc_student AS s ON s.student_id = i.student_id
                left join erp_goods as g on g.goods_id = i.goods_id
            WHERE
                {$datawhere} and i.school_id = '{$paramArray['school_id']}' and i.integralgoods_isreceive<>'-1'
            ORDER BY
                i.integralgoods_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = array("0" => "待处理", "1" => "无需采购", "2" => "已申请采购");
            foreach ($goodsList as &$val) {
                $val['integralgoods_status_name'] = $status[$val['integralgoods_status_name']];
                $val['integralgoods_all'] = $val['integralgoods_score'] * $val['integralgoods_number'];
                $repertory = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$val['goods_id']}'");
                if ($repertory) {
                    $val['repertory'] = $repertory['goods_repertory'];
                } else {
                    $val['repertory'] = '0';
                }
                $way = $this->DataControl->selectOne("
                    SELECT
                        sum( proogoods_buynums ) as num
                    FROM 
                        erp_proorder_goods AS g
                        LEFT JOIN erp_proorder AS p ON g.proorder_pid = p.proorder_pid 
                    WHERE
                        p.to_school_id = '{$paramArray['school_id']}' 
                        AND g.goods_id = '{$val['goods_id']}' 
                        AND ( p.proorder_status = '0' OR p.proorder_status = '1' OR p.proorder_status = '2' )
                        limit 0,1");

                if ($way['num']) {
                    $val['way'] = $way['num'];
                } else {
                    $val['way'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(i.integralgoods_id) as a
            FROM
                smc_student_integralgoods AS i
                LEFT JOIN smc_student AS s ON s.student_id = i.student_id
                left join erp_goods as g on g.goods_id = i.goods_id
            WHERE
                {$datawhere} and i.school_id = '{$paramArray['school_id']}' and i.integralgoods_isreceive<>'-1' ");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('student_cnname ', 'student_enname', 'student_branch', 'goods_cnname', 'goods_pid', 'prodtype_code', 'goods_vipprice', 'integralgoods_score', 'integralgoods_number', 'integralgoods_all', 'way', 'repertory', 'integralgoods_createtime', 'integralgoods_status_name');
        $fieldname = array('学员中文名', '学员英文名', '学员编号', '商品名称', '商品编号', '商品类型', '价格', '兑换积分', '兑换数量', '消耗积分', '在途库存', '本校库存', '兑换时间', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "0", "1", "1", "1", "1", "0", "0", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "0", "1", "1", "1", "1", "0", "0", "1", "1");
        $fieldshowDialog = array("0", "0", "0", "1", "1", "0", "1", "1", "1", "1", "1", "1", "0", "0");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["showDialog"] = trim($fieldshowDialog[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['allnum'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无积分兑换明细哦～", 'result' => $result);
        }

        return $res;
    }

    //根据学生id获取学生积分信息
    function getStudentIntegral($paramArray)
    {
        $sql = "select s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex,p.property_integralbalance from smc_student_virtual_property as p left join smc_student as s on p.student_id = s.student_id where p.student_id = '{$paramArray['student_id']}'";
        $StudentDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["student_cnname"] = "中文名";
        $field["student_enname"] = "英文名";
        $field["student_img"] = "头像";
        $field["student_sex"] = "性别";
        $field["property_integralbalance"] = "积分";
        $result = array();
        if ($StudentDetail) {
            $result["field"] = $field;
            $result["data"] = $StudentDetail;
            $res = array('error' => '0', 'errortip' => '根据学生id获取学生积分信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '根据学生id获取学生积分信息失败', 'result' => $result);
        }
        return $res;
    }

    //根据学生id获取学生积分信息
    function getStudentInfo($paramArray)
    {
        $sql = "select s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex from smc_student as s where s.student_id = '{$paramArray['student_id']}'";
        $StudentDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["student_cnname"] = "中文名";
        $field["student_enname"] = "英文名";
        $field["student_img"] = "头像";
        $field["student_sex"] = "性别";
        $result = array();
        if ($StudentDetail) {
            $result["field"] = $field;
            $result["data"] = $StudentDetail;
            $res = array('error' => '0', 'errortip' => '根据学生id获取学生信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '根据学生id获取学生信息失败', 'result' => $result);
        }
        return $res;
    }

    //积分兑换商品明细
    function getIntegralgoodsDetail($paramArray)
    {
        $sql = "
            SELECT
                g.goods_cnname,
                g.prodtype_code,
                g.goods_vipprice,
                i.student_id,
                i.integralgoods_score,
                i.integralgoods_number,
                i.integralgoods_score*i.integralgoods_number as integralgoods_all,
                FROM_UNIXTIME( i.integralgoods_createtime, '%Y-%m-%d %H:%i:%s' ) AS integralgoods_createtime
            FROM
                smc_student_integralgoods AS i
                left join erp_goods as g on g.goods_id = i.goods_id
            WHERE
                i.integralgoods_id = '{$paramArray['integralgoods_id']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_cnname', 'prodtype_code', 'goods_vipprice', 'integralgoods_number', 'integralgoods_score', 'integralgoods_all', 'integralgoods_createtime');
        $fieldname = array('商品名称', '商品编号', '价格', '兑换数量', '兑换积分', '消耗积分', '兑换时间');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //改变积分兑换明细状态
    function UpdateIntegralStatusAction($paramArray)
    {
        $IntegralList = json_decode(stripslashes($paramArray['status']), true);
        foreach ($IntegralList as $item) {
            $data['integralgoods_status'] = $item['integralgoods_status'];
            $data['integralgoods_updatetime'] = time();

            $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

            if ($item['integralgoods_status'] == '2') {
                $trackdate = array();
                $trackdate['integralgoods_id'] = $item['integralgoods_id'];
                $trackdate['tracks_title'] = '申请采购';
                $trackdate['tracks_information'] = $staffer['staffer_cnname'] . '老师已申请生成采购订单';
                $trackdate['staffer_id'] = $paramArray['staffer_id'];
                $trackdate['tracks_time'] = time();
                $this->DataControl->insertData("smc_integral_tracks", $trackdate);
            } else {
                $trackdate = array();
                $trackdate['integralgoods_id'] = $item['integralgoods_id'];
                $trackdate['tracks_title'] = '无需采购';
                $trackdate['tracks_information'] = $staffer['staffer_cnname'] . '老师确认无需采购';
                $trackdate['staffer_id'] = $paramArray['staffer_id'];
                $trackdate['tracks_time'] = time();
                $this->DataControl->insertData("smc_integral_tracks", $trackdate);
            }

            $this->DataControl->updateData("smc_student_integralgoods", "integralgoods_id = '{$item['integralgoods_id']}'", $data);
        }
        $res = array('error' => '0', 'errortip' => "修改成功");

        return $res;
    }

    //积分交易明细列表
    function getIntegrallogList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['integrallog_playclass']) && $paramArray['integrallog_playclass'] !== "") {
            $datawhere .= " and i.integrallog_playclass ='{$paramArray['integrallog_playclass']}'";
        }
//        if (isset($paramArray['integraltype_id']) && $paramArray['integraltype_id'] !== "") {
//            $datawhere .= " and i.integraltype_id ='{$paramArray['integraltype_id']}'";
//        }


        if (isset($paramArray['integralrule_name']) && $paramArray['integralrule_name'] !== '') {
            $datawhere .= " and i.integrallog_rule = '{$paramArray['integralrule_name']}'";
        }


        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.student_cnname,
                s.student_enname,
                s.student_branch,
                i.integrallog_playclass,
                i.integrallog_rule,
                i.integrallog_playclass as integrallog_playclass_name,
                i.student_id,
                i.integrallog_playname,
                i.integrallog_rule,
                i.integraltype_id,
                i.integrallog_playamount,
                i.integrallog_reason,
                FROM_UNIXTIME( i.integrallog_time, '%Y-%m-%d %H:%i:%s' ) AS integrallog_time,
                concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ) ) as staffer_cnname,
                cl.class_cnname
            FROM
                smc_student_integrallog AS i
                LEFT JOIN smc_student AS s ON i.student_id = s.student_id
                left join smc_staffer as st on st.staffer_id = i.staffer_id
                left join smc_class as cl on cl.class_id = i.class_id
            WHERE
                {$datawhere} and i.school_id = '{$paramArray['school_id']}'
            group by i.integrallog_id
            ORDER BY
                i.integrallog_time DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = array("+" => "积分增加", "-" => "积分扣除");
            foreach ($goodsList as &$val) {
                $val['integrallog_playclass_name'] = $status[$val['integrallog_playclass_name']];
                $val['status'] = '已完成';
                $val['integrallog_playamount'] = $val['integrallog_playclass'] . $val['integrallog_playamount'];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(i.integrallog_id) as a
            FROM
                smc_student_integrallog AS i
                LEFT JOIN smc_student AS s ON i.student_id = s.student_id
            WHERE
                {$datawhere} and i.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('student_cnname ', 'student_enname', 'student_branch', 'integrallog_playclass_name', 'integrallog_rule', 'class_cnname', 'integrallog_playamount', 'integrallog_reason', 'integrallog_time', 'status');
        $fieldname = array('学员中文名', '学员英文名', '学员编号', '所属类别', '积分规则名称', '班级名称', '交易积分', '交易原因', '完成时间', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['allnum'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无积分交易明细哦～", 'result' => $result);
        }

        return $res;
    }

    //积分类型下拉
    function getIntegraltypeApi($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['integrallog_playclass']) && $paramArray['integrallog_playclass'] !== "") {
            $datawhere .= " and integraltype_class ='{$paramArray['integrallog_playclass']}'";
        }
        $sql = "select integraltype_id,integraltype_name from smc_code_integraltype where {$datawhere} and (company_id = '{$paramArray['company_id']}' or company_id = '0')";
        $Integraltype = $this->DataControl->selectClear($sql);

        foreach ($Integraltype as &$val) {
            $val['integraltype_name'] = $this->LgStringSwitch($val['integraltype_name']);
        }

        $field = array();
        $field["integraltype_name"] = $this->LgStringSwitch("类型名称");
        $result = array();
        if ($Integraltype) {
            $result["field"] = $field;
            $result["data"] = $Integraltype;
            $res = array('error' => '0', 'errortip' => '积分类型下拉查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '积分类型下拉查看失败', 'result' => $result);
        }
        return $res;
    }

    //货物订单管理列表
    function getGeneralgoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%' or d.goods_cnname like '%{$paramArray['keyword']}%' or d.goods_pid like '%{$paramArray['keyword']}%' or d.prodtype_code like '%{$paramArray['keyword']}%' or g.order_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['ordergoods_status']) && $paramArray['ordergoods_status'] !== "") {
            $datawhere .= " and g.ordergoods_status ='{$paramArray['ordergoods_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.order_pid,
                g.goods_id,
                s.student_cnname,
                s.student_enname,
                student_branch,
                d.goods_cnname,
                d.goods_pid,
                d.goods_vipprice,
                g.ordergoods_buynums,
                g.ordergoods_id,
                g.ordergoods_totalprice,
                o.student_id,
                FROM_UNIXTIME( o.order_createtime, '%Y-%m-%d %H:%i:%s' ) AS order_createtime,
                g.ordergoods_status,
                g.ordergoods_status as ordergoods_status_name
            FROM
                smc_payfee_order_goods AS g
                LEFT JOIN smc_payfee_order AS o ON g.order_pid = o.order_pid
                LEFT JOIN smc_student AS s ON o.student_id = s.student_id
                LEFT JOIN erp_goods AS d ON g.goods_id = d.goods_id
            WHERE
                {$datawhere} and o.school_id = '{$paramArray['school_id']}' and o.order_status = '4'
            ORDER BY
                g.ordergoods_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = array("0" => "待处理", "1" => "无需采购", "2" => "已申请采购");
            foreach ($goodsList as &$val) {
                $val['ordergoods_status_name'] = $status[$val['ordergoods_status_name']];
                $repertory = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "school_id = '{$paramArray['school_id']}' and goods_id = '{$val['goods_id']}'");
                if ($repertory) {
                    $val['repertory'] = $repertory['goods_repertory'];
                } else {
                    $val['repertory'] = '0';
                }
                $way = $this->DataControl->selectOne("
                    SELECT
                        sum( proogoods_buynums ) as num
                    FROM 
                        erp_proorder_goods AS g
                        LEFT JOIN erp_proorder AS p ON g.proorder_pid = p.proorder_pid 
                    WHERE
                        p.to_school_id = '{$paramArray['school_id']}' 
                        AND g.goods_id = '{$val['goods_id']}' 
                        AND ( p.proorder_status = '0' OR p.proorder_status = '1' OR p.proorder_status = '2' )
                        limit 0,1");

                if ($way['num']) {
                    $val['way'] = $way['num'];
                } else {
                    $val['way'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(g.ordergoods_id) as a
            FROM
                smc_payfee_order_goods AS g
                LEFT JOIN smc_payfee_order AS o ON g.order_pid = o.order_pid
                LEFT JOIN smc_student AS s ON o.student_id = s.student_id
                LEFT JOIN erp_goods AS d ON g.goods_id = d.goods_id
            WHERE
                {$datawhere} and o.school_id = '{$paramArray['school_id']}' and o.order_status = '4'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('order_pid', 'student_cnname', 'student_enname', 'student_branch', 'goods_cnname', 'goods_pid', 'goods_vipprice', 'ordergoods_buynums', 'ordergoods_totalprice', 'order_createtime', 'ordergoods_status_name', 'repertory');
        $fieldname = array('订单编号', '学员中文名', '学员英文名', '学员编号', '商品名称', '商品编号', '销售价', '购买数量', '金额总计', '购买时间', '处理状态', '本校库存');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshowDialog = array("0", "0", "0", "0", "1", "1", "1", "1", "1", "0", "0", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["showDialog"] = trim($fieldshowDialog[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['allnum'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无货物订单哦～", 'result' => $result);
        }

        return $res;
    }

    //货物订单商品明细
    function getGeneralgoodsDetail($paramArray)
    {
        $sql = "
            SELECT
                g.goods_cnname,
                g.prodtype_code,
                g.goods_vipprice,
                o.ordergoods_buynums,
                o.ordergoods_totalprice,
                FROM_UNIXTIME( p.order_createtime, '%Y-%m-%d %H:%i:%s' ) AS order_createtime
            FROM
                smc_payfee_order_goods AS o
                LEFT JOIN erp_goods as g on g.goods_id = o.goods_id
                LEFT JOIN smc_payfee_order AS p ON p.order_pid = o.order_pid
            WHERE
                o.ordergoods_id = '{$paramArray['ordergoods_id']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_cnname', 'prodtype_code', 'goods_vipprice', 'ordergoods_buynums', 'ordergoods_totalprice', 'order_createtime');
        $fieldname = array('商品名称', '商品编号', '销售价', '购买数量', '金额总计', '购买时间');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //改变货物订单状态
    function UpdateGeneralStatusAction($paramArray)
    {
        $IntegralList = json_decode(stripslashes($paramArray['status']), true);
        foreach ($IntegralList as $item) {
            $data['ordergoods_status'] = $item['ordergoods_status'];

            $this->DataControl->updateData("smc_payfee_order_goods", "ordergoods_id = '{$item['ordergoods_id']}'", $data);
        }
        $res = array('error' => '0', 'errortip' => "修改成功");

        return $res;
    }


}
