<?php


namespace Model\Smc;


class CourseModel extends modelTpl
{

    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $conflictData = array();
    public $conflict = 1;
    public $adjust_error = 0;

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];

        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }


    function courseList($request)
    {
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        $endTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 + 1, date("Y"));
        $startWeekDay = date('Y-m-d', $startTime);
        $endWeekDay = date('Y-m-d', $endTime);
        $datawhere = " 1 and ch.hour_ischecking <> -1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or sc.course_cnname  like '%{$request['keyword']}%' )";
        }
        if (isset($request['classroom_id']) && $request['classroom_id'] !== '') {
            $datawhere .= " and cr.classroom_id = '{$request['classroom_id']}'";
        }
        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }
        if (isset($request['hour_noon']) && (count($request['hour_noon']) > 0)) {
            $datawhere_noon = "";
            for ($i = 0; $i < count($request['hour_noon']); $i++) {
                if ($i == (count($request['hour_noon']) - 1)) {
                    $datawhere_noon .= " (ch.hour_noon ='{$request['hour_noon'][$i]}') ";
                } else {
                    $datawhere_noon .= " (ch.hour_noon ='{$request['hour_noon'][$i]}') or ";
                }
            }
            $datawhere .= " and ($datawhere_noon) ";
        }

        if (isset($request['weekstartday']) && $request['weekstartday'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['weekstartday']}'";
        } else {
            $datawhere .= " and ch.hour_day >= '{$startWeekDay}'";
        }
        if (isset($request['weekendday']) && $request['weekendday'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['weekendday']}'";
        } else {
            $datawhere .= " and ch.hour_day <= '{$endWeekDay}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['is_count']) && $request['is_count'] == 1) {

            $all_num = $this->DataControl->selectOne("select count(distinct ch.hour_id) as hour_num
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as sc on sc.course_id=ch.course_id
              left join smc_classroom as cr on cr.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as t on t.hour_id=ch.hour_id  and  t.teaching_type =0
              left join smc_staffer as s on s.staffer_id=t.staffer_id
              left join smc_class_lessonplan as  l on l.class_id = ch.class_id and l.classroom_id = cr.classroom_id
              where {$datawhere} and c.school_id='{$this->school_id}' and class_status <> '-2'
              ");

            $list['allnum'] = $all_num['hour_num'];

        } else {
            $list['allnum'] = 0;
        }


        $sql = "select ch.hour_id,c.class_id,c.class_cnname,c.class_enname,sc.course_cnname,sc.course_branch,s.staffer_cnname,s.staffer_enname,ch.hour_day,ch.hour_starttime,ch.hour_endtime,cr.classroom_cnname ,l.lessonplan_starttime,l.lessonplan_endtime,sc.course_classnum,s.staffer_native,c.class_branch,ch.hour_ischecking,sc.course_inclasstype,ch.hour_way
              ,(select count(sch.hour_id) from smc_class_hour as sch where sch.course_id=ch.course_id and sch.class_id=ch.class_id and sch.hour_ischecking=1) as num
                ,ifnull((select 1 from smc_student_hourstudy as x,smc_class_hour as y where x.hour_id=y.hour_id and x.class_id=c.class_id and y.hour_isfree=0 limit 0,1),0) as is_atte
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as sc on sc.course_id=ch.course_id
              left join smc_classroom as cr on cr.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as t on t.hour_id=ch.hour_id and t.teaching_type =0
              left join smc_staffer as s on s.staffer_id=t.staffer_id
              left join smc_class_lessonplan as  l on l.class_id = ch.class_id and l.classroom_id = cr.classroom_id
              where {$datawhere} and c.school_id='{$this->school_id}'  and class_status <> '-2'
              group by ch.hour_id order by ch.hour_day ASC,ch.hour_starttime ASC, ch.hour_endtime ASC
              limit {$pagestart},{$num}";
        $courseList = $this->DataControl->selectClear($sql);

        $rel_courseList = array();
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        if ($courseList) {
            foreach ($courseList as $hourOne) {
                $data = array();
                $data['hour_id'] = $hourOne['hour_id'];
                $data['class_id'] = $hourOne['class_id'];
                $data['class_cnname'] = $hourOne['class_cnname'];
                $data['class_enname'] = $hourOne['class_enname'];
                $data['course_cnname'] = $hourOne['course_cnname'];
                $data['course_branch'] = $hourOne['course_branch'];
                $data['class_branch'] = $hourOne['class_branch'];
                $data['course_inclasstype'] = $hourOne['course_inclasstype'];
                $data['hour_ischecking'] = $hourOne['hour_ischecking'];
                $data['hour_starttime'] = $hourOne['hour_starttime'];
                $data['hour_endtime'] = $hourOne['hour_endtime'];
                $data['is_atte'] = $hourOne['is_atte'];
                $data['hour_way'] = $this->LgStringSwitch($hourOne['hour_way'] == 0 ? '实体课' : "线上课");
                if ($hourOne['hour_ischecking'] == 1) {
                    $data['hour_ischecking_name'] = $this->LgStringSwitch("已考勤");
                } elseif ($hourOne['hour_ischecking'] == -1) {
                    $data['hour_ischecking_name'] = $this->LgStringSwitch("已取消");
                } else {
                    $data['hour_ischecking_name'] = $this->LgStringSwitch("待考勤");
                }

                if ($hourOne['hour_day'] > date('Y-m-d')) {
                    $data['is_outdate'] = 1;
                } else {
                    $data['is_outdate'] = 0;
                }

                $hourOne['staffer_cnname'] = $hourOne['staffer_enname'] ? $hourOne['staffer_cnname'] . '-' . $hourOne['staffer_enname'] : $hourOne['staffer_cnname'];
                if ($hourOne['staffer_native'] == 1) {
                    $data['staffer'] = $this->LgStringSwitch("[外籍]" . $hourOne['staffer_cnname']);
                } else {
                    $data['staffer'] = $this->LgStringSwitch("[中籍]" . $hourOne['staffer_cnname']);
                }

                $data['time'] = $this->LgStringSwitch('[' . "星期" . $weekarray[date("w", strtotime($hourOne['hour_day']))] . ']' . $hourOne['hour_day'] . " " . $hourOne['hour_starttime'] . '-' . $hourOne['hour_endtime']);
                $data['classroom_cnname'] = $hourOne['classroom_cnname'];
                $data['status'] = $hourOne['num'] . '/' . $hourOne['course_classnum'];


                $rel_courseList[] = $data;
            }
        } else {
            $rel_courseList = array();
        }

        $list['rel_courseList'] = $rel_courseList;

        return $list;
    }

    function courseClassRecord($request)
    {
        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
        $endTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 + 1, date("Y"));

        $startWeekDay = date('Y-m-d', $startTime);
        $endWeekDay = date('Y-m-d', $endTime);

        $datawhere = " ch.hour_ischecking  = 1 and ct.coursetype_isopenclass <> '1' and sc.course_inclasstype IN (0,1) ";

        if (isset($request['weekstartday']) && $request['weekstartday'] !== '') {
            $request['weekstartday'] = date("Y-m-d", strtotime($request['weekstartday']));
            $datawhere .= " and ch.hour_day >= '{$request['weekstartday']}'";
        }

        if (isset($request['weekendday']) && $request['weekendday'] !== '') {
            $request['weekendday'] = date("Y-m-d", strtotime($request['weekendday']));
            $datawhere .= " and ch.hour_day <= '{$request['weekendday']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$request['class_id']}'";
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

//		if (isset($request['pad']) && $request['pad'] !== '') {
//			$datawhere .= " and t.staffer_id = '{$request['staffer_id']}'";
//		}

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }


        if (isset($request['hour_noon']) && (count($request['hour_noon']) > 0)) {
            $datawhere_noon = "";
            for ($i = 0; $i < count($request['hour_noon']); $i++) {
                if ($i == (count($request['hour_noon']) - 1)) {
                    $datawhere_noon .= " (ch.hour_noon ='{$request['hour_noon'][$i]}') ";
                } else {
                    $datawhere_noon .= " (ch.hour_noon ='{$request['hour_noon'][$i]}') or ";
                }
            }
            $datawhere .= " and ($datawhere_noon) ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {

            $all_num = $this->DataControl->selectOne("select COUNT(ch.hour_id) as hour_num
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as sc on sc.course_id=ch.course_id 
              LEFT JOIN smc_code_coursetype as ct ON sc.coursetype_id = ct.coursetype_id 
              left join smc_class_hour_teaching as t on t.hour_id=ch.hour_id and  t.teaching_type =0
              left join smc_staffer as s on s.staffer_id=t.staffer_id
              left join smc_staffer_info as si on si.staffer_id=s.staffer_id
              where {$datawhere} and c.school_id='{$this->school_id}' and sc.course_inclasstype in ('0','1')
              ");

            $list['allnum'] = $all_num['hour_num'];
        } else {
            $list['allnum'] = 0;
        }

        $pagestart = ($page - 1) * $num;
        $now = time();

        if (isset($request['order']) && $request['order'] !== '') {
            $order = "  order by ch.hour_day ";
        } else {
            $order = "  order by ch.hour_day DESC ";
        }

//		(select count(sss.study_id) from smc_student_study as sss where sss.school_id=c.school_id and sss.class_id=ch.class_id and  sss.study_isreading =1) as b,
//		(select  count(h.audition_id) from smc_class_hour_audition as h  where  h.hour_id = ch.hour_id   ) as bb


        $sql = "select sc.course_inclasstype,ch.hour_id,ch.class_id,ch.hour_day,ch.hour_starttime,ch.hour_endtime,c.class_cnname,c.class_enname,sc.course_cnname,sc.course_branch,s.staffer_cnname,s.staffer_enname,si.info_isforeign,c.class_branch,ch.hour_ischecking,ch.hour_inarrivenums,ch.hour_way,
				(select count(ssh.hourstudy_id) from smc_student_hourstudy as ssh where ssh.hour_id=ch.hour_id and ssh.class_id=ch.class_id and ssh.hourstudy_checkin =1 ) as a,
				(select count(h.audition_id) from smc_class_hour_audition as h  where h.hour_id = ch.hour_id and   h.audition_isvisit =1 ) as aa
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_course as sc on sc.course_id=ch.course_id 
              LEFT JOIN smc_code_coursetype as ct ON sc.coursetype_id = ct.coursetype_id 
              left join smc_class_hour_teaching as t on t.hour_id=ch.hour_id and t.teaching_type =0
              left join smc_staffer as s on s.staffer_id=t.staffer_id
              left join smc_staffer_info as si on si.staffer_id=s.staffer_id
              where {$datawhere} and c.school_id='{$this->school_id}' and sc.course_inclasstype in ('0','1')
             {$order}
              limit {$pagestart},{$num}";
        $recordList = $this->DataControl->selectClear($sql);

        $rel_recordList = array();

        if ($recordList) {

            foreach ($recordList as $recordOne) {
                $data = array();
                $data['course_inclasstype'] = $recordOne['course_inclasstype'];
                $data['hour_id'] = $recordOne['hour_id'];
                $data['class_id'] = $recordOne['class_id'];
                $data['class_id'] = $recordOne['class_id'];
                $data['hour_day'] = $recordOne['hour_day'] . " " . $recordOne['hour_starttime'] . '-' . $recordOne['hour_endtime'];
                $data['class_cnname'] = $recordOne['class_cnname'];
                $data['class_enname'] = $recordOne['class_enname'];
                $data['class_branch'] = $recordOne['class_branch'];
                $data['course_cnname'] = $recordOne['course_cnname'];
                $data['course_branch'] = $recordOne['course_branch'];
                $data['hour_ischecking'] = $recordOne['hour_ischecking'];
                $data['hour_way_name'] = $this->LgStringSwitch($recordOne['hour_way'] == 0 ? "实体课" : "线上课");

                $recordOne['staffer_cnname'] = $recordOne['staffer_enname'] ? $recordOne['staffer_cnname'] . '-' . $recordOne['staffer_enname'] : $recordOne['staffer_cnname'];
                if ($recordOne['info_isforeign'] == 0) {
                    $data['staffer'] = $this->LgStringSwitch("[中籍]" . $recordOne['staffer_cnname']);
                } else {
                    $data['staffer'] = $this->LgStringSwitch("[外籍]" . $recordOne['staffer_cnname']);
                }
//				$data['status'] = ($recordOne['a'] + $recordOne['aa']) . '/' . ($recordOne['b']+$recordOne['bb']);

//                $data['status'] = ($recordOne['a'] + $recordOne['aa']) . '/' . $recordOne['hour_inarrivenums'];
                $data['status'] = ($recordOne['a']) . '/' . $recordOne['hour_inarrivenums'];

                if ($recordOne['hour_ischecking'] == 1) {
//                    $crmaudition = $this->DataControl->selectOne("select count(audition_id) as num from crm_client_audition WHERE company_id = '{$request['company_id']}' and hour_id = '{$recordOne['hour_id']}' ");
//                    $recordOne['hour_inarrivenums'] = $recordOne['hour_inarrivenums'] + $crmaudition['num'];
//					$data['check_status'] = $recordOne['b']+$recordOne['bb'] - $recordOne['a'] + $recordOne['aa'];

//                    $data['check_status'] = $recordOne['hour_inarrivenums'] - ($recordOne['a'] + $recordOne['aa']);
                    $data['check_status'] = $recordOne['hour_inarrivenums'] - ($recordOne['a']);
                } else {
                    $data['check_status'] = 0;
                }

                $rel_recordList[] = $data;
            }
        } else {
            $rel_recordList = array();
        }

        $list['rel_recordList'] = $rel_recordList;
        $list['allnum'] = $list['allnum'];
        return $list;

    }


    function padstudRecord($request)
    {

//		$startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
//		$endTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 + 1, date("Y"));
//
//		$startWeekDay = date('Y-m-d', $startTime);
//		$endWeekDay = date('Y-m-d', $endTime);
        $datawhere = " 1 ";

        if (isset($request['weekstartday']) && $request['weekstartday'] !== '') {
//            $request['starttime']=strtotime($request['starttime']);
            $datawhere .= " and sch.hour_day>='{$request['weekstartday']}'";
        }
        if (isset($request['weekendday']) && $request['weekendday'] !== '') {
//            $request['endtime']=strtotime($request['endtime']);
            $datawhere .= " and sch.hour_day<='{$request['weekendday']}'";
        }


        if (isset($request['hourstudy_checkin']) && $request['hourstudy_checkin'] !== '') {
            $datawhere .= " and sh.hourstudy_checkin='{$request['hourstudy_checkin']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and sch.class_id='{$request['class_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sco.course_id = '{$request['course_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sst.student_cnname  like '%{$request['keyword']}%' or sst.student_enname like '%{$request['keyword']}%' or sst.student_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select sch.hour_id,sh.student_id,sch.hour_day,sco.course_branch,sco.course_cnname,c.class_cnname,c.class_enname,c.class_branch,sh.hourstudy_checkin,sc.clockinginlog_note,concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )) as staffer_cnname,sst.student_cnname,sst.student_enname,sst.student_branch,sch.hour_isfree,sc.clockinginlog_note,sch.class_id,
			  (select ssf.family_mobile from smc_student_family as ssf where ssf.student_id = sc.student_id and ssf.family_isdefault =1 limit 0,1) as family_mobile
              from smc_student_clockinginlog as sc
              left join smc_student_hourstudy as sh on sh.hourstudy_id=sc.hourstudy_id
              left join  smc_student as sst ON sst.student_id = sh.student_id
              left join smc_class_hour as sch on sch.hour_id=sh.hour_id
              left  join smc_student_study as ss ON ss.student_id = sh.student_id and ss.class_id=sch.class_id
              left join smc_class_hour_teaching as sct on sct.class_id=ss.class_id and sct.hour_id =sch.hour_id and sct.teaching_type=0
              left join smc_staffer as st on st.staffer_id=sct.staffer_id
              left join smc_class as c on c.class_id=sch.class_id
              left join smc_course as sco on sco.course_id=c.course_id
              where {$datawhere} and c.school_id='{$this->school_id}' and c.company_id='{$this->company_id}' and sco.course_inclasstype in ('0','1')
              group by sc.clockinginlog_id
              order by sch.class_id DESC,sh.student_id DESC
              limit {$pagestart},{$num}
        ";

//		and sch.class_id in (select class_id from  smc_class_hour_teaching where  staffer_id ='{$request['staffer_id']}' )
        $attendanceList = $this->DataControl->selectClear($sql);
        if ($attendanceList) {
            foreach ($attendanceList as $key => $value) {
                $attendanceList[$key]['type'] = "student";
            }
        }

        if (!$attendanceList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $checkinArray = $this->LgArraySwitch(array("0" => "缺勤", "1" => "出勤"));
        $freeArray = $this->LgArraySwitch(array("0" => "是", "1" => "否"));
        foreach ($attendanceList as &$val) {
            $val['hourstudy_checkinname'] = $checkinArray[$val['hourstudy_checkin']];
            $val['hour_isfree'] = $freeArray[$val['hour_isfree']];
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select sc.clockinginlog_id
              from smc_student_clockinginlog as sc
              left join smc_student_hourstudy as sh on sh.hourstudy_id=sc.hourstudy_id
              left join  smc_student as sst ON sst.student_id = sh.student_id
              left join smc_class_hour as sch on sch.hour_id=sh.hour_id
              left  join smc_student_study as ss ON ss.student_id = sh.student_id and ss.class_id=sch.class_id
              left join smc_class_hour_teaching as sct on sct.class_id=sch.class_id and sct.teaching_type =0
              left join smc_staffer as st on st.staffer_id=sct.staffer_id
              left join smc_class as c on c.class_id=sch.class_id
              left join smc_course as sco on sco.course_id=c.course_id
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sco.course_inclasstype in ('0','1')
              group by sc.clockinginlog_id";

//			and sch.class_id in (select class_id from  smc_class_hour_teaching where  staffer_id ='{$request['staffer_id']}'  )
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        } else {
            $data['allnum'] = 0;
        }

        $data['list'] = $attendanceList;
        return $data;

    }

    function courseAuditionRecord($request)
    {
//		$startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));
//		$endTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 + 1, date("Y"));
//
//		$startWeekDay = date('Y-m-d', $startTime);
//		$endWeekDay = date('Y-m-d', $endTime);    //默认本周时段

        $datawhere = " 1 ";
        $endqueryday = '';
        if (isset($request['weekstartday']) && $request['weekstartday'] !== '') {
            $request['weekstartday'] = date("Y-m-d", strtotime($request['weekstartday']));
            $datawhere .= " and ch.hour_day >= '{$request['weekstartday']}'";
            $endqueryday .= $request['weekstartday'];
        }


        if (isset($request['weekendday']) && $request['weekendday'] !== '') {
            $request['weekendday'] = date("Y-m-d", strtotime($request['weekendday']));
            $datawhere .= " and ch.hour_day <= '{$request['weekendday']}'";
            $endqueryday .= '-' . $request['weekstartday'];
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

        if (isset($request['hour_noon']) && (count($request['hour_noon']) > 0)) {
            $datawhere_noon = "";
            for ($i = 0; $i < count($request['hour_noon']); $i++) {
                if ($i == (count($request['hour_noon']) - 1)) {
                    $datawhere_noon .= " (ch.hour_noon ='{$request['hour_noon'][$i]}') ";
                } else {
                    $datawhere_noon .= " (ch.hour_noon ='{$request['hour_noon'][$i]}') or ";
                }

            }
            $datawhere .= " and ($datawhere_noon) ";
        }


        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cha.audition_cnname like '%{$request['keyword']}%' or cha.audition_enname like '%{$request['keyword']}%'  or  sc.course_cnname like '%{$request['keyword']}%' or  s.staffer_cnname like '%{$request['keyword']}%' )";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $all_num = $this->DataControl->selectOne("
			 select  count(cha.audition_id) as  audition_id_num
              from smc_class_hour_audition as cha
              left join smc_class_hour as ch on ch.hour_id=cha.hour_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type=0
              left join smc_course as sc on sc.course_id=ch.course_id
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              where {$datawhere} and cha.school_id='{$request['school_id']}' and cha.company_id='{$request['company_id']}'

			 ");

            $allnum = $all_num['audition_id_num'];
        } else {
            $allnum = 0;
        }

        if (isset($request['order']) && $request['order'] !== '') {
            $order = "  order by ch.hour_day DESC ";
        } else {
            $order = "  order by ch.hour_day  ";
        }

        $sql = "select cha.audition_id,ch.hour_day,cha.audition_cnname,cha.audition_enname,c.class_cnname,c.class_branch,sc.course_cnname,sc.course_branch,s.staffer_cnname,s.staffer_enname,cha.audition_isvisit,ch.hour_way,
				(select   cl.conversionlog_id from crm_client_conversionlog as cl where cl.client_id = cha.client_id  limit 0,1) as is_formal
              from smc_class_hour_audition as cha
              left join smc_class_hour as ch on ch.hour_id=cha.hour_id
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type =0
              left join smc_course as sc on sc.course_id=ch.course_id
              left join smc_staffer as s on s.staffer_id=cht.staffer_id
              where {$datawhere} and cha.school_id='{$request['school_id']}' and cha.company_id='{$request['company_id']}'
             {$order}
              ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['audition_cnname'] = $dateexcelvar['audition_cnname'];
                    $datearray['audition_enname'] = $dateexcelvar['audition_enname'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_enname'] ? $dateexcelvar['staffer_cnname'] . '-' . $dateexcelvar['staffer_enname'] : $dateexcelvar['staffer_cnname'];
                    if ($dateexcelvar['is_formal']) {
                        $datearray['is_formal'] = $this->LgStringSwitch("是");
                    } else {
                        $datearray['is_formal'] = $this->LgStringSwitch("否");
                    }
                    if ($dateexcelvar['audition_isvisit'] == 1) {
                        $datearray['audition_isvisit'] = $this->LgStringSwitch("是");
                    } elseif ($dateexcelvar['audition_isvisit'] == -1) {
                        $datearray['audition_isvisit'] = $this->LgStringSwitch("否");
                    } else {
                        $datearray['audition_isvisit'] = $this->LgStringSwitch("待确认");
                    }
                    $datearray['hour_way_name'] = $this->LgStringSwitch($dateexcelvar['hour_way'] == 0 ? "实体课" : "线上课");

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("试听日期", "中文名", "英文名", "班级名称", "班级编号", "课程名称", '课程编号', '上课教师', '是否转正', '是否试听', '上课方式'));
            $excelfileds = array('hour_day', 'audition_cnname', 'audition_enname', 'class_cnname', 'class_branch', 'course_cnname', 'course_branch', 'staffer_cnname', 'is_formal', 'audition_isvisit', 'hour_way_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员试听表{$endqueryday}.xlsx"));
            exit;

        } else {
            $sql .= " limit {$pagestart},{$num}";
        }

        $auditionList = $this->DataControl->selectClear($sql);

        $rel_auditionList = array();
        if ($auditionList) {
            foreach ($auditionList as $auditionOne) {
                $data = array();
                $data['audition_id'] = $auditionOne['audition_id'];
                $data['hour_day'] = $auditionOne['hour_day'];
                $data['audition_cnname'] = $auditionOne['audition_cnname'];
                $data['audition_enname'] = $auditionOne['audition_enname'];
                $data['class_cnname'] = $auditionOne['class_cnname'];
                $data['class_branch'] = $auditionOne['class_branch'];
                $data['course_cnname'] = $auditionOne['course_cnname'];
                $data['course_branch'] = $auditionOne['course_branch'];
                $data['staffer_cnname'] = $auditionOne['staffer_enname'] ? $auditionOne['staffer_cnname'] . '-' . $auditionOne['staffer_enname'] : $auditionOne['staffer_cnname'];
                $data['audition_isvisit'] = $auditionOne['audition_isvisit'];
                $data['hour_way_name'] = $this->LgStringSwitch($auditionOne['hour_way'] == 0 ? "实体课" : "线上课");
                if ($auditionOne['is_formal']) {
                    $data['is_formal'] = $this->LgStringSwitch("是");
                } else {
                    $data['is_formal'] = $this->LgStringSwitch("否");
                }
                if ($auditionOne['audition_isvisit'] == 1) {
                    $data['audition_isvisit'] = $this->LgStringSwitch("是");
                } elseif ($auditionOne['audition_isvisit'] == -1) {
                    $data['audition_isvisit'] = $this->LgStringSwitch("否");
                } else {
                    $data['audition_isvisit'] = $this->LgStringSwitch("待确认");
                }
                $rel_auditionList[] = $data;
            }
        }

        $list['num'] = $allnum;
        $list['rel_auditionList'] = $rel_auditionList;
        return $list;

    }

    /**
     * @param $request
     * @return array|bool
     *  获取教室
     */
    function getClassroom($request)
    {
        $roomList = $this->DataControl->getFieldquery('smc_classroom', 'classroom_id,classroom_cnname', "school_id ='{$request['school_id']}'");
        if ($roomList) {
            $list = $roomList;
        } else {
            $list = array();
        }
        return $list;
    }


//	预约类班级点名学员列表

    function inRollCallClass($paramArray)
    {
        $datawhere = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        $hourOne = $this->DataControl->selectOne(
            "select ch.hour_ischecking,ch.class_id,ch.hour_day,cs.course_inclasstype,ch.hour_starttime,ch.hour_endtime,c.course_id,hour_isfree 
				  from smc_class_hour as ch
 			 	  left JOIN  smc_class as c ON  ch.class_id =c.class_id
 			 	  left JOIN  smc_course as cs ON cs.course_id = c.course_id
 				  where hour_id='{$paramArray['hour_id']}' limit 0,1 ");
        $sql =
            "select  b.hour_id,b.student_id,s.student_cnname,s.student_enname,s.student_branch,b.booking_id,
            (select f.family_mobile from smc_student_family as f where f.student_id = b.student_id Order by family_isdefault Desc limit 0,1) as  family_mobile,
            (select count(hourstudy_id) from smc_student_hourstudy as h where h.hourstudy_checkin = 0 and h.hourstudy_makeup = 0  and h.class_id = b.class_id order by hourstudy_id DESC  limit 0,1) as  check_num,
            (select sh.hourstudy_checkin from smc_student_hourstudy as sh where sh.student_id = b.student_id and sh.class_id ='{$paramArray['class_id']}' and sh.hour_id='{$paramArray['hour_id']}' limit 0,1) as hourstudy_checkin,
            (select count(sh.hourstudy_id)  from smc_student_hourstudy as sh where sh.student_id = b.student_id and sh.class_id ='{$paramArray['class_id']}' and sh.hourstudy_checkin ='0' and sh.hourstudy_makeup = '0' limit 0,1) as hour_checkinnum,
            (select sc.clockinginlog_note from smc_student_clockinginlog as sc
	    left join smc_student_hourstudy as sh ON sh.hourstudy_id = sc.hourstudy_id
	    where sc.student_id = s.student_id and sh.class_id = '{$paramArray['class_id']}'  and sh.hour_id='{$paramArray['hour_id']}'  order by sc.clockinginlog_id limit 0,1) as clockinginlog_note,
	        (select sh.hourstudy_id from smc_student_hourstudy as sh where sh.student_id = b.student_id  and  sh.class_id ='{$paramArray['class_id']}'  and sh.hour_id='{$paramArray['hour_id']}' limit 0,1 ) as hourstudy_id,
	        (select f.coursetimes_id from smc_student_free_coursetimes as f,smc_class_hour as h where f.class_id=h.class_id and h.hour_lessontimes = f.hour_lessontimes and h.hour_iswarming =0 and f.student_id =b.student_id and h.hour_id =b.hour_id and f.is_use =0 ) as coursetimes_id,
	        (select ah.absence_hour_status from  smc_student_absence as ab, smc_student_absence_hour as ah where ab.absence_id =ah.absence_id and ab.student_id=s.student_id and ah.hour_id='{$paramArray['hour_id']}' and ah.hour_day ='{$hourOne['hour_day']}'  and ah.hour_starttime = '{$hourOne['hour_starttime']}' and ah.hour_endtime ='{$hourOne['hour_endtime']}'and ab.absence_status <>-2  and ah.absence_hour_status <> -1 limit 0,1 ) as absence_hour_status,
				(select ab.absence_reasonnote from  smc_student_absence as ab, smc_student_absence_hour as ah where ab.absence_id =ah.absence_id and ab.student_id=s.student_id  and ah.hour_id='{$paramArray['hour_id']}'and ah.hour_day ='{$hourOne['hour_day']}'  and ah.hour_starttime = '{$hourOne['hour_starttime']}' and ah.hour_endtime ='{$hourOne['hour_endtime']}'and ab.absence_status <>-2 and ah.absence_hour_status <> -1 limit 0,1 ) as absence_reasonnote
				 from smc_class_booking as b
				 left join  smc_student as s ON s.student_id = b.student_id
				 where b.hour_id ='{$paramArray['hour_id']}' and  b.booking_status ='0' and {$datawhere} ";
        $bookingList = $this->DataControl->selectClear($sql);
        if ($bookingList) {
            foreach ($bookingList as $key => &$val) {
                $val['key_num'] = $key + 1;
                if ($paramArray['hour_isfree'] == 0) {
                    $val['fee'] = $this->LgStringSwitch("是");
                } else {
                    $val['fee'] = $this->LgStringSwitch("否");
                }
                if ($val['coursetimes_id']) {
                    $val['fee'] = $this->LgStringSwitch("否");
                }

                $val['type'] = "student";
                $val['isbooking_name'] = "已预约";
                $val['isbooking'] = 1;
                $val['student_type'] = $this->LgStringSwitch("正式学员");
                if (is_null($val['hourstudy_checkin'])) {
                    $val['hourstudy_checkin'] = "1";
                }

                if (isset($val['absence_hour_status']) && $val['absence_hour_status'] == 0 && $val['absence_hour_status'] != '') {
                    $val['stuchecktype_code'] = '301';
                    $val['stuchecktype_name'] = '请假待审批';
                    $val['clockinginlog_note'] = $val['absence_reasonnote'];
                } elseif (isset($val['absence_hour_status']) && $val['absence_hour_status'] == 1) {
                    $val['stuchecktype_code'] = '102';
                    $val['stuchecktype_name'] = '请假已通过';
                    $val['clockinginlog_note'] = $val['absence_reasonnote'];
                    $val['hourstudy_checkin'] = '0';
                } elseif (!isset($val['hourstudy_checkin']) || $val['hourstudy_checkin'] == '' || $val['hourstudy_checkin'] == 1) {
                    $val['stuchecktype_code'] = '101';
                    $val['stuchecktype_name'] = '出勤';
                } else {
                    $val['stuchecktype_code'] = '107';
                    $val['stuchecktype_name'] = '缺勤';
                }

            }

            return $bookingList;
        } else {
            return array();
        }

    }

    //拥有课次的预约课学员列表
    function inRollCallCourseStudent($paramArray, $num = 0)
    {
        $datawhere = '1';
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "hour_id='{$paramArray['hour_id']}'");
        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$paramArray['class_id']}'");
        $sql = "
             select t.student_id,t.student_cnname,t.student_enname,t.student_branch,
                 (select f.family_mobile from smc_student_family as f where f.student_id = t.student_id Order by family_isdefault Desc limit 0,1) as  family_mobile,
             (select sh.hourstudy_id from smc_student_hourstudy as sh where sh.student_id = t.student_id  and  sh.class_id ='{$paramArray['class_id']}'  and sh.hour_id='{$paramArray['hour_id']}' limit 0,1 ) as hourstudy_id,
               (select sh.hourstudy_checkin from smc_student_hourstudy as sh where sh.student_id = t.student_id  and  sh.class_id ='{$paramArray['class_id']}' and sh.hour_id='{$paramArray['hour_id']}' limit 0,1) as hourstudy_checkin,
            (select count(sh.hourstudy_id)  from smc_student_hourstudy as sh where sh.student_id = t.student_id  and  sh.class_id ='{$paramArray['class_id']}' and sh.hourstudy_checkin ='0' and sh.hourstudy_makeup = '0'   limit 0,1) as hour_checkinnum,
               (select sc.clockinginlog_note from smc_student_clockinginlog as sc
            left join smc_student_hourstudy as sh ON sh.hourstudy_id = sc.hourstudy_id
            where sc.student_id = t.student_id and sh.class_id = '{$paramArray['class_id']}'  and sh.hour_id='{$paramArray['hour_id']}'  order by sc.clockinginlog_id limit 0,1) as clockinginlog_note,
            (select sh.hourstudy_id from smc_student_hourstudy as sh where sh.student_id = b.student_id  and  sh.class_id ='{$paramArray['class_id']}'  and sh.hour_id='{$paramArray['hour_id']}' limit 0,1 ) as hourstudy_id,
	        (select f.coursetimes_id from smc_student_free_coursetimes as f,smc_class_hour as h where f.class_id=h.class_id and h.hour_lessontimes = f.hour_lessontimes and h.hour_iswarming =0 and f.student_id =t.student_id and h.hour_id ='{$paramArray['hour_id']}' and f.is_use =0 ) as coursetimes_id,
	         (select ah.absence_hour_status from  smc_student_absence as ab, smc_student_absence_hour as ah where ab.absence_id =ah.absence_id and ab.student_id=t.student_id and ah.hour_id='{$paramArray['hour_id']}' and ah.hour_day ='{$hourOne['hour_day']}'  and ah.hour_starttime = '{$hourOne['hour_starttime']}' and ah.hour_endtime ='{$hourOne['hour_endtime']}'and ab.absence_status <>-2  and ah.absence_hour_status <> -1 limit 0,1 ) as absence_hour_status,
				(select ab.absence_reasonnote from  smc_student_absence as ab, smc_student_absence_hour as ah where ab.absence_id =ah.absence_id and ab.student_id=t.student_id  and ah.hour_id='{$paramArray['hour_id']}'and ah.hour_day ='{$hourOne['hour_day']}'  and ah.hour_starttime = '{$hourOne['hour_starttime']}' and ah.hour_endtime ='{$hourOne['hour_endtime']}'and ab.absence_status <>-2 and ah.absence_hour_status <> -1 limit 0,1 ) as absence_reasonnote
             from  smc_student as t
             left join smc_student_study as sd ON t.student_id = sd.student_id
             left join smc_student_coursebalance as b On sd.school_id = sd.school_id and sd.student_id = b.student_id  
             where sd.class_id = '{$paramArray['class_id']}' and  b.course_id = '{$classOne['course_id']}' 
             and sd.study_beginday<='{$hourOne['hour_day']}' and  sd.study_endday>='{$hourOne['hour_day']}'
             and  {$datawhere}
             and t.student_id not in (select student_id from smc_class_booking as k where k.class_id='{$paramArray['class_id']}' and  hour_id='{$paramArray['hour_id']}' and  booking_status ='0' )
        ";
        $studentList = $this->DataControl->selectClear($sql);
        if ($studentList) {
            foreach ($studentList as $key => &$studentOne) {
                $studentOne['hour_id'] = $paramArray['hour_id'];
                $studentOne['key_num'] = $num + 1 + $key;
                if ($paramArray['hour_isfree'] == 0) {
                    $studentOne['fee'] = $this->LgStringSwitch("是");
                } else {
                    $studentOne['fee'] = $this->LgStringSwitch("否");
                }
                if ($studentOne['coursetimes_id']) {
                    $studentOne['fee'] = $this->LgStringSwitch("否");
                }
                $studentOne['type'] = "student";
                $studentOne['isbooking_name'] = "未预约";
                $studentOne['isbooking'] = 0;
                $studentOne['booking_id'] = 0;
                $studentOne['student_type'] = $this->LgStringSwitch("正式学员");
                if (is_null($studentOne['hourstudy_checkin'])) {
                    $studentOne['hourstudy_checkin'] = "";
                }
                if (isset($studentOne['absence_hour_status']) && $studentOne['absence_hour_status'] == 0 && $studentOne['absence_hour_status'] != '') {
                    $studentOne['stuchecktype_code'] = '301';
                    $studentOne['stuchecktype_name'] = '请假待审批';
                    $studentOne['clockinginlog_note'] = $studentOne['absence_reasonnote'];
                } elseif (isset($studentOne['absence_hour_status']) && $studentOne['absence_hour_status'] == 1) {
                    $studentOne['stuchecktype_code'] = '102';
                    $studentOne['stuchecktype_name'] = '请假已通过';
                    $studentOne['clockinginlog_note'] = $studentOne['absence_reasonnote'];
                    $studentOne['hourstudy_checkin'] = '0';
                } elseif (!isset($studentOne['hourstudy_checkin']) || $studentOne['hourstudy_checkin'] == '' || $studentOne['hourstudy_checkin'] == 1) {
                    $studentOne['stuchecktype_code'] = '101';
                    $studentOne['stuchecktype_name'] = '出勤';
                } else {
                    $studentOne['stuchecktype_code'] = '107';
                    $studentOne['stuchecktype_name'] = '缺勤';
                }
            }
            return $studentList;
        } else {
            return array();
        }
    }


    //预约类在班学员列表
    function inClassStudentList($paramArray)
    {
        $datawhere = '1';
        $Having = "and 1";
        if (isset($paramArray['is_booking']) && $paramArray['is_booking'] !== "") {
            if ($paramArray['is_booking'] == 0) {
                $Having = "Having booking_id >0 ";      //接口传值写错了.
            } else {
                $Having = "Having booking_id <1 ";
            }
        }
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "hour_id='{$paramArray['hour_id']}'");

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or  s.student_branch like '%{$paramArray['keyword']}%')";
        }
        $today = date("Ymd");
        $sql = "select ss.study_id,s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,
 				(select IF(AVG(booking_id) is null,0,booking_id)  from smc_class_booking as b where b.hour_id='{$paramArray['hour_id']}' and b.student_id=ss.student_id and booking_status =0 ) as booking_id,
 				(select sc.coursebalance_time from smc_student_coursebalance as sc where sc.student_id = ss.student_id  and sc.course_id = c.course_id  and sc.school_id = c.school_id ) as coursebalance_time
 				from smc_student_study as ss
 				LEFT join smc_class as c ON  c.class_id = ss.class_id
				left join smc_student as s ON s.student_id = ss.student_id
				where ss.study_isreading = 1 and  ss.study_beginday <= '{$today}' and ss.class_id = '{$paramArray['class_id']}' and {$datawhere}
				and ss.study_beginday <='{$hourOne['hour_day']}' 
				 {$Having} ";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $studentList = array();
        } else {
            foreach ($studentList as &$value) {
                if ($value['booking_id'] > 0) {
                    $value['booking_name'] = $this->LgStringSwitch('是');
                } else {
                    $value['booking_name'] = $this->LgStringSwitch('否');
                }
            }
        }
        return $studentList;

    }

    /**
     * @param $request
     * @return mixed
     *  点名上课列表
     */
    function rollCallClass($request)
    {

        if(!isset($request['class_id']) || $request['class_id'] == ''){
            $this->error = 1;
            $this->errortip = "班级id必须传";
            return false;
        }

        if(!isset($request['hour_id']) || $request['hour_id'] == ''){
            $this->error = 1;
            $this->errortip = "课时id必须传";
            return false;
        }

        $datawhere = '1';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or (select f.family_mobile from smc_student_family as f where f.student_id = s.student_id Order by family_isdefault Desc limit 0,1) like '%{$request['keyword']}%')";
        }

        $hourOne = $this->DataControl->selectOne(
            "select ch.hour_ischecking,ch.class_id,ch.hour_day,cs.course_inclasstype,ch.hour_starttime,ch.hour_endtime,c.course_id,hour_isfree,cs.course_inclasstype,c.class_isconfirmopen,cs.course_isneedconfirm 
				  from smc_class_hour as ch
 			 	  left JOIN  smc_class as c ON  ch.class_id =c.class_id
 			 	  left JOIN  smc_course as cs ON cs.course_id = c.course_id
 				  where hour_id='{$request['hour_id']}' limit 0,1 ");


        $hourStudyOne=$this->DataControl->selectOne("SELECT a.hourstudy_id from smc_student_hourstudy as a,smc_class_hour as b where a.hour_id=b.hour_id and a.class_id='{$hourOne['class_id']}' and b.hour_isfree=0 limit 0,1");          

        if($hourOne['hour_isfree']==0 && $hourOne['course_isneedconfirm']==1 && $hourOne['class_isconfirmopen']==0 && !$hourStudyOne){
            $this->error = 2;
            $this->errortip = "该班级暂未确认开班,无法考勤!请联系教学主管或校长在教务系统进行开班确认后再操作";
            return false;
        }


        $hour_day = date('Y-m-d', strtotime($hourOne['hour_day']));

        $class = $this->DataControl->selectOne("
		select c.class_cnname,c.class_enname,h.hour_day,h.hour_starttime,h.hour_endtime,c.course_id,h.hour_iswarming,h.hour_lessontimes,h.hour_ischecking,c.class_type,
		(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_iswarming =0 and ho.hour_ischecking <> -1 ) as hour_num,
		(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_iswarming =1 and ho.hour_ischecking <> -1 ) as hour_warmnum
		from smc_class_hour as h
		left JOIN smc_class as c On c.class_id = h.class_id
		where hour_id='{$request['hour_id']}' limit 0,1");

        if ($class['hour_day']) {
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $class['week'] = $weekarray[date('w', strtotime($class['hour_day']))];
        }
        $iswarming = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));

        $class['hour_iswarming_name'] = $iswarming[$class['hour_iswarming']];
        $class['hour_times'] = $class['hour_iswarming'] == 0 ? $class['hour_lessontimes'] . '/' . $class['hour_num'] : $class['hour_lessontimes'] . '/' . $class['hour_warmnum'];
        $list['class'] = $class;
        //预约类班级(阅读班)的点名学员
        if ($hourOne['course_inclasstype'] == 2 && $hourOne['hour_ischecking'] == 0) {
            $request['hour_isfree'] = $hourOne['hour_isfree'];
            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_issubscribe,course_inclasstype", "course_id='{$hourOne['course_id']}'");

            $stuList = $this->inRollCallClass($request);
            if ($courseOne['course_issubscribe'] == 1) {
                //有课次的学员
                $studentList = $this->inRollCallCourseStudent($request, count($stuList));
                $stuList = array_merge($stuList, $studentList);
            }
            $list['student'] = $stuList;
            $studet_checkin = $this->DataControl->selectOne(
                " select 
                    count(distinct student_id) as is_checkin,
   					(select count(distinct student_id) from  smc_student_hourstudy as h where h.hour_id = '{$request['hour_id']}'  and h.hourstudy_checkin =0 and h.class_id='{$request['class_id']}') as  no_checkin
  				from  smc_student_hourstudy as h where h.hour_id = '{$request['hour_id']}' and   h.hourstudy_checkin =1  and h.class_id='{$request['class_id']}' "
            );
            $checkin = array();
            $checkin['is_checking'] = $hourOne['hour_ischecking'];
            $checkin['is_checkin'] = $studet_checkin['is_checkin'];
            $checkin['no_checkin'] = $studet_checkin['no_checkin'];
            $checkin['all_checkin'] = count($stuList);
            $checkin['student_num'] = count($studet_checkin);
            $checkin['student_austu'] = 0;
            $checkin['hour_starttime'] = $hourOne['hour_starttime'];
            $checkin['hour_endtime'] = $hourOne['hour_endtime'];
            $checkin['hour_day'] = $hourOne['hour_day'];
            $checkin['course_issubscribe'] = $courseOne['course_issubscribe'];
            $checkin['course_inclasstype'] = $courseOne['course_inclasstype'];
            $checkin['hour_id'] = $request['hour_id'];
            $list['is_checkin'] = $checkin;
            return $list;
        }
        if ($hourOne['hour_ischecking'] == 1) {
            $sqlField = "sh.student_id,sh.hourstudy_id,sh.hourstudy_checkin,s.student_branch,s.student_cnname,s.student_enname,ss.study_beginday,ch.hour_day,ch.hour_isfree,s.student_sex,s.student_img";
            $sql = "select {$sqlField},
			  (select count(sh.hourstudy_id)  from smc_student_hourstudy as sh where sh.student_id = ss.student_id  and sh.hourstudy_checkin ='0' and sh.hourstudy_makeup = '0' and sh.class_id='{$request['class_id']}'  limit 0,1) as hour_checkinnum,
			  (select sc.clockinginlog_note from smc_student_clockinginlog as sc where sc.hourstudy_id = sh.hourstudy_id and sc.student_id =sh.student_id limit 0,1) as clockinginlog_note,
			(select coursetimes_id from smc_student_free_coursetimes as fc 
			left join smc_class_hour as ch ON ch.hour_lessontimes = fc.hour_lessontimes  and  ch.class_id=fc.class_id
			where fc.student_id=ss.student_id and fc.school_id='{$request['school_id']}' and fc.is_use=1 and fc.course_id ='{$hourOne['course_id']}'and ch.hour_id='{$request['hour_id']}' limit 0,1) as coursetimes_id,
			  (select f.family_mobile from smc_student_family as f where f.student_id = s.student_id Order by family_isdefault Desc limit 0,1) as  family_mobile
			 from smc_student_hourstudy as sh
			 left join smc_student as s ON sh.student_id=s.student_id
			 left join smc_student_study as ss ON ss.student_id =sh.student_id and ss.class_id = sh.class_id
			 left join smc_class_hour as ch On sh.hour_id = ch.hour_id and sh.class_id =ch.class_id
			 where sh.hour_id ='{$request['hour_id']}' and sh.class_id ='{$request['class_id']}' and {$datawhere} ";
            $studentList = $this->DataControl->selectClear($sql);
        } else {
            $sqlField = "ss.student_id,s.student_branch,s.student_cnname,s.student_enname,ss.study_beginday,s.student_sex,s.student_img";
            $sql = "
			select  {$sqlField},
			(select ah.absence_hour_status from  smc_student_absence as ab, smc_student_absence_hour as ah where ab.absence_id =ah.absence_id and ab.student_id=ss.student_id and ah.hour_id='{$request['hour_id']}' and ah.hour_day ='{$hourOne['hour_day']}'  and ah.hour_starttime = '{$hourOne['hour_starttime']}' and ah.hour_endtime ='{$hourOne['hour_endtime']}'and ab.absence_status <>-2  and ah.absence_hour_status <> -1 limit 0,1 ) as absence_hour_status,
				(select ab.absence_reasonnote from  smc_student_absence as ab, smc_student_absence_hour as ah where ab.absence_id =ah.absence_id and ab.student_id=ss.student_id  and ah.hour_id='{$request['hour_id']}'and ah.hour_day ='{$hourOne['hour_day']}'  and ah.hour_starttime = '{$hourOne['hour_starttime']}' and ah.hour_endtime ='{$hourOne['hour_endtime']}'and ab.absence_status <>-2 and ah.absence_hour_status <> -1 limit 0,1 ) as absence_reasonnote,
			(select ch.hour_day from smc_class_hour as ch  where ch.hour_id='{$request['hour_id']}'  ) as hour_day,
			(select f.family_mobile from smc_student_family as f where f.student_id = s.student_id Order by family_isdefault Desc limit 0,1) as  family_mobile,
			(select h.hour_isfree from smc_class_hour as  h where  h.hour_id='{$request['hour_id']}') as hour_isfree,
			(select coursetimes_id from smc_student_free_coursetimes as fc 
			left join smc_class_hour as ch ON ch.hour_lessontimes = fc.hour_lessontimes and ch.class_id=fc.class_id
			where fc.student_id=ss.student_id and fc.school_id='{$request['school_id']}' and fc.is_use=0 and fc.course_id ='{$hourOne['course_id']}'and ch.hour_id='{$request['hour_id']}' limit 0,1) as coursetimes_id,
			(select count(sh.hourstudy_id)  from smc_student_hourstudy as sh where sh.student_id = ss.student_id  and  sh.class_id ='{$request['class_id']}' and sh.hourstudy_checkin ='0' and sh.hourstudy_makeup = '0'  limit 0,1) as hour_checkinnum
			from smc_student_study as ss
			left join smc_student as s ON ss.student_id = s.student_id
			where ss.class_id = '{$request['class_id']}'  and ss.study_beginday <= '{$hour_day}'
	    and ( ss.study_isreading = '1' OR ss.study_endday > '{$hour_day}')  and {$datawhere} 
			";

            $studentList = $this->DataControl->selectClear($sql);
            //clockinginlog_note ,//hourstudy_id,	//hourstudy_checkin  这3个字段不能少
        }
        if ($studentList) {
            foreach ($studentList as $key => $value) {
                $studentList[$key]['student_name'] = $value['student_enname'] ? $value['student_cnname'] . ' / ' . $value['student_enname'] : $value['student_cnname'];
                $studentList[$key]['type'] = "student";
                $studentList[$key]['student_type'] = $this->LgStringSwitch("正式学员");
                $studentList[$key]['fee'] = $this->LgStringSwitch(($value['hour_isfree'] == "0" && $class['class_type'] == "0") ? '是' : '否');
                if ($value['coursetimes_id']) {
                    $studentList[$key]['fee'] = $this->LgStringSwitch('否');
                }
                if ($hourOne['course_inclasstype'] == 2) {
                    $studentList[$key]['isbooking_name'] = "是";
                } else {
                    $studentList[$key]['isbooking_name'] = "否";
                }
                $studentList[$key]['clockinginlog_note'] = $value['clockinginlog_note'] == "" ? '--' : $value['clockinginlog_note'];
                $studentList[$key]['hourstudy_id'] = $value['hourstudy_id'] == "" ? '0' : $value['hourstudy_id'];
                $studentList[$key]['hourstudy_checkin'] = $value['hourstudy_checkin'] == "" ? '1' : $value['hourstudy_checkin'];
//				$studentList[$key]['check_num'] =$value['check_num'] ==""?'0':$value['hourstudy_checkin'];
                if (isset($value['absence_hour_status']) && $value['absence_hour_status'] == 0 && $value['absence_hour_status'] != '') {
                    $studentList[$key]['stuchecktype_code'] = '301';
                    $studentList[$key]['stuchecktype_name'] = '请假待审批';
                    $studentList[$key]['clockinginlog_note'] = $value['absence_reasonnote'];
                } elseif (isset($value['absence_hour_status']) && $value['absence_hour_status'] == 1) {
                    $studentList[$key]['stuchecktype_code'] = '102';
                    $studentList[$key]['stuchecktype_name'] = '请假已通过';
                    $studentList[$key]['clockinginlog_note'] = $value['absence_reasonnote'];
                    $studentList[$key]['hourstudy_checkin'] = '0';
                } elseif (!isset($value['hourstudy_checkin']) || $value['hourstudy_checkin'] == '' || $value['hourstudy_checkin'] == 1) {
                    $studentList[$key]['stuchecktype_code'] = '101';
                    $studentList[$key]['stuchecktype_name'] = '出勤';
                } else {
                    $studentList[$key]['stuchecktype_code'] = '107';
                    $studentList[$key]['stuchecktype_name'] = '缺勤';
                }
            }
        } else {
            $studentList = array();
        }
        $checkin['student_num'] = count($studentList);

        if ($request['isLookAttendance'] == '1') {
            //试听学员的查询
            $austudentList = $this->DataControl->selectClear("
            SELECT *  FROM (
                select ha.audition_id,c.client_id,c.client_cnname,c.client_enname,ha.audition_isvisit,ha.audition_novisitreason,c.client_mobile,ha.audition_createtime 
                from smc_class_hour_audition  as ha
                left JOIN  crm_client as c  ON ha.client_id = c.client_id
                left JOIN  smc_class_hour as h  ON h.hour_id = ha.hour_id
                where h.class_id ='{$request['class_id']}' and ha.hour_id ='{$request['hour_id']}' and (ha.audition_isvisit = 1 or ha.audition_isvisit = '-1') 
                ORDER BY ha.audition_createtime DESC 
                ) as t GROUP BY t.client_id,t.audition_isvisit ORDER BY t.audition_isvisit DESC ,t.audition_createtime DESC
               ");
//            //试听学员的查询 -- 20240320 前的数据
//            $austudentList = $this->DataControl->selectClear("
//            SELECT *  FROM (
//                select ha.audition_id,c.client_id,c.client_cnname,c.client_enname,ha.audition_isvisit,ha.audition_novisitreason,c.client_mobile,ha.audition_createtime
//                from smc_class_hour_audition  as ha
//                left JOIN  crm_client as c  ON ha.client_id = c.client_id
//                left JOIN  smc_class_hour as h  ON h.hour_id = ha.hour_id
//                where h.class_id ='{$request['class_id']}' and ha.hour_id ='{$request['hour_id']}' and (ha.audition_isvisit = 1 or ha.audition_isvisit = '-1')
//                ORDER BY ha.audition_createtime DESC
//                ) as t GROUP BY t.client_id ORDER BY t.audition_isvisit DESC ,t.audition_createtime DESC
//               ");
        } else {
            //试听学员的查询
            $austudentList = $this->DataControl->selectClear("
                select ha.audition_id,c.client_id,c.client_cnname,c.client_enname,ha.audition_isvisit,ha.audition_novisitreason,c.client_mobile,
                        if(ha.audition_isvisit = '0',0,1) as isprohibit 
                from smc_class_hour_audition  as ha
                left JOIN  crm_client as c  ON ha.client_id = c.client_id
                left JOIN  smc_class_hour as h  ON h.hour_id = ha.hour_id
                 where h.class_id ='{$request['class_id']}' and ha.hour_id ='{$request['hour_id']}' 
               ");
//            //试听学员的查询 -- 20240320 前的数据
//            $austudentList = $this->DataControl->selectClear("
//                select ha.audition_id,c.client_id,c.client_cnname,c.client_enname,ha.audition_isvisit,ha.audition_novisitreason,c.client_mobile
//                from smc_class_hour_audition  as ha
//                left JOIN  crm_client as c  ON ha.client_id = c.client_id
//                left JOIN  smc_class_hour as h  ON h.hour_id = ha.hour_id
//                 where h.class_id ='{$request['class_id']}' and ha.hour_id ='{$request['hour_id']}' and ha.audition_isvisit = 0
//               ");
        }

        if (!$austudentList) {
            $austudentList = array();
        }
        $checkin['student_austu'] = count($austudentList);
        if ($austudentList) {
            foreach ($austudentList as $key => $value) {
                $studentList['a' . $key]['hourstudy_id'] = $value['audition_isvisit'] == 0 ? '0' : '1';
                $studentList['a' . $key]['student_id'] = $value['client_id'];
                $studentList['a' . $key]['audition_id'] = $value['audition_id'];
                $studentList['a' . $key]['student_branch'] = '--';
                $studentList['a' . $key]['student_cnname'] = $value['client_cnname'];
                $studentList['a' . $key]['student_enname'] = $value['client_enname'];
                $studentList['a' . $key]['hourstudy_checkin'] = $value['audition_isvisit'] == '-1' ? '0' : '1';
                $studentList['a' . $key]['clockinginlog_note'] = $value['audition_novisitreason'];
                $studentList['a' . $key]['family_mobile'] = $value['client_mobile'];
                $studentList['a' . $key]['check_num'] = '0';
                $studentList['a' . $key]['type'] = 'audition';
                $studentList['a' . $key]['student_type'] = $this->LgStringSwitch("试听学员");
                $studentList['a' . $key]['hour_checkinnum'] = "--";
                $studentList['a' . $key]['fee'] = $this->LgStringSwitch("否");
                $studentList['a' . $key]['isprohibit'] = $value['isprohibit']?$value['isprohibit']:'';

                if ($value['audition_isvisit'] == -1) {
                    $studentList['a' . $key]['stuchecktype_code'] = '107';
                    $studentList['a' . $key]['stuchecktype_name'] = '缺勤';
                } else {
                    $studentList['a' . $key]['stuchecktype_code'] = '101';
                    $studentList['a' . $key]['stuchecktype_name'] = '出勤';
                }
            }
        }
        $studentList = array_values($studentList);


        $studet_checkin = $this->DataControl->selectOne(
            " select count(distinct student_id) as is_checkin,
   					(select count(distinct student_id) from  smc_student_hourstudy as h where h.hour_id = '{$request['hour_id']}'  and h.hourstudy_checkin =0 and   h.class_id='{$request['class_id']}') as  no_checkin
  				from  smc_student_hourstudy as h where h.hour_id = '{$request['hour_id']}' and  h.hourstudy_checkin =1   and h.class_id='{$request['class_id']}' "
        );
        $aution_checkin = $this->DataControl->selectOne(
            " select count(audition_id) as is_checkin,
   					(select count(audition_id) from  smc_class_hour_audition as h where h.hour_id = '{$request['hour_id']}'  and  h.audition_isvisit = -1 ) as  no_checkin
  				from  smc_class_hour_audition as h where h.hour_id ='{$request['hour_id']}' and   h.audition_isvisit =1 "
        );

        //是否上课
        $is_checking = $this->DataControl->selectOne("select  h.hour_ischecking from smc_class_hour as h where h.hour_id = '{$request['hour_id']}' order by h.hour_createtime");


        $checkin['is_checking'] = $is_checking['hour_ischecking'];
        $checkin['is_checkin'] = $studet_checkin['is_checkin'] + $aution_checkin['is_checkin'];
        $checkin['no_checkin'] = $studet_checkin['no_checkin'] + $aution_checkin['no_checkin'];
        $checkin['all_checkin'] = count($studentList);
        $checkin['hour_starttime'] = $hourOne['hour_starttime'];
        $checkin['hour_endtime'] = $hourOne['hour_endtime'];
        $checkin['hour_day'] = $hourOne['hour_day'];
        $checkin['course_inclasstype'] = $hourOne['course_inclasstype'];
        $checkin['hour_id'] = $request['hour_id'];
        $list['is_checkin'] = $checkin;
        if ($studentList) {
            foreach ($studentList as $key => $value) {
                $studentList[$key]['key_num'] = $key + 1;
            }

            $list['student'] = $studentList;
        } else {
            $list['student'] = array();
            $this->errortip = "暂无需要点名上课的学员信息";
        }
        return $list;
    }


    //--------------------------------------------------------------------------------lujing

    function getRoomOneTimetable($hour_day, $where, $school_id, $conflict = 0)
    {
        if ($hour_day == "") {
            return array();
        }

        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,cl.classroom_id,cl.classroom_cnname
            from  smc_classroom AS cl
			LEFT JOIN smc_class_hour AS ch ON cl.classroom_id = ch.classroom_id  AND ch.hour_day = '{$hour_day}'
			LEFT JOIN smc_class AS c ON c.class_id = ch.class_id 	AND c.school_id = {$school_id}
			LEFT JOIN smc_course AS co ON co.course_id = c.course_id
			LEFT JOIN smc_class_hour_teaching AS t ON t.class_id = c.class_id and t.teaching_type=0
			AND t.hour_id = ch.hour_id
			LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
            where {$where}

    		";
        $dayList = $this->DataControl->selectClear($sql);
        $data = array();
        if ($dayList) {
            $classroom_id = array_unique(array_column($dayList, 'classroom_id'));
            if ($dayList) {
                $data = array();
                foreach ($classroom_id as $classroom) {
                    foreach ($dayList as $key => $value) {
                        if ($value['classroom_id'] == $classroom) {
                            $data[$classroom]['classroom_cnname'] = $value['classroom_cnname'];
                            $data[$classroom]['classroom_id'] = $value['classroom_id'];

                            if ($value['hour_id']) {
                                $data[$classroom]['hour_list'][] = $value;
                                //检查冲突  //时间冲突
                                if ($conflict == 1 and (count($data[$classroom]['hour_list']) > 1)) {

                                    for ($i = 0; $i < count($data[$classroom]['hour_list']); $i++) {
                                        for ($j = $i + 1; $j < count($data[$classroom]['hour_list']); $j++) {

                                            $iend = $hour_day . " " . $data[$classroom]['hour_list'][$i]['hour_endtime'];
                                            $istart = $hour_day . " " . $data[$classroom]['hour_list'][$i]['hour_starttime'];
                                            $jend = $hour_day . " " . $data[$classroom]['hour_list'][$j]['hour_endtime'];
                                            $jstart = $hour_day . " " . $data[$classroom]['hour_list'][$j]['hour_starttime'];

                                            $iend = date('Y-m-d', strtotime($iend));
                                            $istart = date('Y-m-d', strtotime($istart));
                                            $jend = date('Y-m-d', strtotime($jend));
                                            $jstart = date('Y-m-d', strtotime($jstart));

                                            if (!($iend < $jstart) || !($jend < $istart)) {

                                                $data[$classroom]['hour_list'][$i]['hour_ischecking'] = -2;
                                                $data[$classroom]['hour_list'][$j]['hour_ischecking'] = -2;
                                            }
                                        }
                                    }

                                }

                            }

                        }
                    }
                }
            }
        } else {
            $data = array();
        }

        if ($data) {
            $list = $data;
        } else {
            $list = array();
        }
        return $list;

    }

    /**
     * @param $request
     * @return mixed
     *获取课程详情--课程
     */
    function getCourseOne($hour_id)
    {
        $course = $this->DataControl->selectOne(
            " select h.hour_id,cs.course_cnname,cs.course_branch,classroom_cnname,concat(s.staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) as staffer_cnname,s.staffer_enname,c.class_cnname,c.class_enname,c.class_branch,h.hour_day,h.hour_starttime,h.hour_endtime,h.class_id,s.staffer_id,co.classroom_id,cs.course_id,t.teaching_id,rt.teaching_id as re_teaching_id,ct.coursecat_cnname,h.hour_ischecking,h.hour_inarrivenums,cs.course_inclasstype,hour_iswarming,h.hour_way,h.hour_number,cs.course_opensonmode,cs.course_islimittime,cs.course_limittime,h.hour_isfree,concat(st.staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )) as re_staffer_cnname,rt.staffer_id as re_staffer_id,cc.teachtype_name,tt.teachtype_name as re_teachtype_name,cc.teachtype_code,tt.teachtype_code as re_teachtype_code,cs.course_openclasstype,
 				(select count(ssh.hourstudy_id) from smc_student_hourstudy as ssh where ssh.hour_id=h.hour_id and ssh.class_id=h.class_id and ssh.hourstudy_checkin =1 ) as a,
				(select count(ch.audition_id) from smc_class_hour_audition as ch  where h.hour_id = ch.hour_id and   ch.audition_isvisit =1 ) as aa,
				(select count(booking_id) from smc_class_booking as b where b.hour_id = h.hour_id  and booking_status =0) as  booking_num
  					from smc_class_hour as h
 					left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0 and t.teaching_isdel=0
 					left JOin  smc_staffer as s ON s.staffer_id = t.staffer_id
 					left JOIN smc_class_hour_teaching as rt ON rt.hour_id = h.hour_id and rt.teaching_type=1 and rt.teaching_isdel=0
 					left JOin  smc_staffer as st ON st.staffer_id = rt.staffer_id
 					left JOin  smc_classroom as co ON h.classroom_id = co.classroom_id
  					left join smc_course as cs ON cs.course_id = h.course_id
  				    left join smc_class as c ON c.class_id = h.class_id
  				    left join smc_code_coursecat as ct On ct.coursecat_id = cs.coursecat_id
                    left join smc_code_teachtype as cc on cc.teachtype_code=t.teachtype_code and cc.company_id='{$this->company_id}'
                    left join smc_code_teachtype as tt on tt.teachtype_code=rt.teachtype_code and tt.company_id='{$this->company_id}'
   					where h.hour_id='{$hour_id}'  limit 0,1"
        );

        $ischeck = array("0" => "待上课", "1" => "已上课", "-1" => "已取消");

        if ($course) {

            $course['hour_checking_name'] = $this->LgStringSwitch($ischeck[$course['hour_ischecking']]);

            if ($course['hour_way'] == 1) {
                $course['classroom_cnname'] = $this->LgStringSwitch("云教室");
                $course['classroom_cnname'] = $course['classroom_branch'] = $course['hour_number'];
                $course['classroom_iscloud'] = "1";
            }

            $Model = new \Model\Smc\AdjustmentModel($this->publicarray);
            if ($Model->checkConflict($hour_id, 0, 0, 0, $course['hour_day'], $course['hour_starttime'], $course['hour_endtime'])) {
                $course['hour_ischecking'] = '-2';
            }

            $companyOne=$this->DataControl->getFieldOne("gmc_company","company_canadjustcourse","company_id='{$this->company_id}'");

            $course['can_adjusthour']=1;

            if($companyOne['company_canadjustcourse']==0){

                if(!$this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and adjustapply_day='{$course['hour_day']}' and class_id=0 and hour_id=0 and adjustapply_status=1")){

                    $course['can_adjusthour']=0;

                }
            }


            $course['hour_way_name'] = $this->LgStringSwitch($course['hour_way'] == 1 ? '线上课' : '实体课');
            //时间未到今天不能点名  暖身课不需要点名
            if (($course['hour_day'] > date('Y-m-d'))) {
                $course['is_outdate'] = 1;
            } else {
                $course['is_outdate'] = 0;
            }
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            $course['week_day'] = $this->LgStringSwitch('周' . $weekarray[date("w", strtotime($course['hour_day']))]);
            $course['status'] = ($course['a'] + $course['aa']) . '/' . $course['hour_inarrivenums'];


            $course['time'] = floor((strtotime($course['hour_day'] . ' ' . $course['hour_endtime']) - strtotime($course['hour_day'] . ' ' . $course['hour_starttime'])) / 60);

            $list = $course;
        } else {
            $list = array();
        }
        return $list;
    }


    /**
     * @param $hour_day
     * @param $class_id
     * @return array
     * 获取班级课表的冲突列表
     */
    function classCourseConflict($hour_day, $school_id, $hour_id, $staffer_id, $classroom_id)
    {
        $hour_day = date("Y-m-d", strtotime($hour_day));
        $conflictList = $this->DataControl->selectClear("select h.hour_id,c.class_cnname,co.classroom_cnname,cs.course_cnname,s.staffer_cnname,h.hour_ischecking,h.hour_starttime,h.hour_endtime,s.staffer_id,co.classroom_id,h.hour_way,hour_number
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as co  ON co.classroom_id = h.classroom_id
				left JOIN  smc_course as cs ON cs.course_id = h.course_id
				left JOIN  smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where hour_day='{$hour_day}'  and c.school_id ='{$school_id}' and (s.staffer_id='{$staffer_id}' or h.classroom_id='{$classroom_id}') and  h.hour_ischecking = 0 ");
        if ($conflictList) {
            $arr_hour_id = array_column($conflictList, 'hour_id');
            if (!in_array($hour_id, $arr_hour_id)) {
                $list = array();
                return $list;
            }
        }
        $TimetalbeModel = new \Model\Smc\TimetableModel($this->publicarray);
        $conflictList = $TimetalbeModel->checkClassroomConflict($conflictList);
        $conflictList = $TimetalbeModel->checkStafferConflict($conflictList);
        if ($conflictList) {
            $list = array();
            foreach ($conflictList as $key => $value) {
                if ($value['hour_ischecking'] == -2) {

                    $list[$key] = $value;
                    $list[$key]['hour_time'] = $value['hour_starttime'] . "--" . $value['hour_endtime'];
                    if ($value['hour_way'] == 1) {
                        $list[$key]['classroom_cnname'] = $value['hour_number'];
                    }
                }
            }
            $arr_hour_id = array_column($list, 'hour_id');

            if (!in_array($hour_id, $arr_hour_id)) {

                $list = array();
                return $list;
            }
        } else {
            $list = array();
        }
        return $list;
    }

    /**
     * @param $request
     * @return array|bool|mixed
     *
     * 获取教师课表的课程冲突--暂不启用
     */
    function teacherCourseConflict($hour_day)
    {
        $sql = "select ch.hour_id,c.class_cnname,cl.classroom_cnname,co.course_cnname,s.staffer_cnname,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime
            from  smc_staffer as s
            LEFT  JOIN  smc_class_hour_teaching as t  On s.staffer_id = t.staffer_id and t.teaching_type =0
            LEFT JOIN smc_class_hour AS ch ON  ch.hour_id = t.hour_id
            LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
            left join smc_course as co on co.course_id=c.course_id
            left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
            where ch.hour_day = '{$hour_day}'  ";
        $conflictList = $this->DataControl->selectClear($sql);
        $TimetalbeModel = new \Model\Smc\TimetableModel($this->publicarray);
        $conflictList = $TimetalbeModel->checkClassroomConflict($conflictList);
        $conflictList = $TimetalbeModel->checkStafferConflict($conflictList);
        $list = array();
        if ($conflictList) {
            foreach ($conflictList as $key => $value) {
                if ($value['hour_ischecking'] == -2) {

                    $list[$key] = $value;
                    $list[$key]['hour_time'] = $value['hour_starttime'] . "--" . $value['hour_endtime'];
                }
            }
        } else {
            $list = array();
        }
        return $list;
    }

    /**
     * @param $request
     * @return array|bool|mixed
     *
     * 获取教室课表课程冲突 --暂不启用
     */
    function classroomCourseConflict($hour_day)
    {
        $sql = "select c.class_id,c.class_cnname,ch.hour_id, s.staffer_cnname,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,cl.classroom_id,cl.classroom_cnname
            from  smc_classroom AS cl
			LEFT JOIN smc_class_hour AS ch ON cl.classroom_id = ch.classroom_id
			LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
			LEFT JOIN smc_course AS co ON co.course_id = c.course_id
			LEFT JOIN smc_class_hour_teaching AS t ON t.class_id = c.class_id and t.teaching_type=0
			AND t.hour_id = ch.hour_id
			LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
             where ch.hour_day = '{$hour_day}'";
        $conflictList = $this->DataControl->selectClear($sql);
        $TimetalbeModel = new \Model\Smc\TimetableModel($this->publicarray);
        $conflictList = $TimetalbeModel->checkClassroomConflict($conflictList);
        $conflictList = $TimetalbeModel->checkStafferConflict($conflictList);
        $list = array();
        if ($conflictList) {
            foreach ($conflictList as $key => $value) {
                if ($value['hour_ischecking'] == -2) {

                    $list[$key] = $value;
                    $list[$key]['hour_time'] = $value['hour_starttime'] . "--" . $value['hour_endtime'];
                }
            }
        } else {
            $list = array();
        }
        return $list;
    }

    /**
     * @param $request
     * @return array|bool|mixed
     *  获取取消上课页面
     */
    function viewCancel($request)
    {

        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$request['school_id']}' and adjustapply_type=0 and class_id='{$request['class_id']}' and adjustapply_status=0")){
            $this->error = true;
            $this->errortip = "班级存在未完成调课申请,不可操作取消上课";
            return false;
        }

        $classHour = $this->DataControl->selectOne("
		   select h.hour_day,h.hour_starttime,h.hour_endtime,c.class_cnname
		   from smc_class_hour as h
  		    left JOIN smc_class as c ON c.class_id = h.class_id
  		    where h.hour_id = {$request['hour_id']}
		   ");

        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");
        if ($classHour['hour_day']) {
            $classHour['week_day'] = $this->LgStringSwitch("周" . $weekarray[date('w', strtotime($classHour['hour_day']))]);
        }

        if ($classHour) {
            $data = $classHour;
        } else {
            $data = array();
        }
        return $data;
    }

    /**
     * @param $request
     * @return array|bool
     * 获取取消原因
     */
    function getCancelReason($request)
    {
        $datalist = $this->DataControl->getFieldquery('smc_code_hourcancel_reason', "reason_id,reason_content", "company_id='{$request['company_id']}'");

        if ($datalist) {
            return $datalist;
        } else {
            return array();
        }

    }

    function getCancelHourList($request)
    {
        //公开课限制 有过试听记录的不能取消 20240725
        $courseOne = $this->DataControl->selectOne(" SELECT b.course_inclasstype
                FROM smc_class as a,smc_course as b 
                WHERE a.class_id = '{$request['class_id']}' and a.course_id = b.course_id   ");
        if($courseOne['course_inclasstype'] == '3'){
            $course = $this->DataControl->selectClear(
                " 
                select a.hour_id,a.hour_day,a.hour_starttime,a.hour_endtime
                from smc_class_hour as a 
                where a.class_id='{$request['class_id']}'
                and a.hour_day>='{$request['hour_day']}'
                and a.hour_ischecking=0 
                and not exists (select 1 from crm_client_audition as aud where aud.hour_id=a.hour_id limit 0,1)
                order by hour_day desc,hour_starttime desc
                "
            );
        }else{
            $course = $this->DataControl->selectClear(
                "select hour_id,hour_day,hour_starttime,hour_endtime
            from smc_class_hour 
            where class_id='{$request['class_id']}'
            and hour_day>='{$request['hour_day']}'
            and hour_ischecking=0
            order by hour_day desc,hour_starttime desc"
            );
        }
        if ($course) {
            $list = $course;
        } else {
            $list = array();
        }
        return $list;
    }

    /**
     * @param $request
     * @return bool
     * 提交取消上课
     */
    function cancelClass($request)
    {
        //公开课限制 有过试听记录的不能取消 20240725
        $courseOne = $this->DataControl->selectOne(" SELECT b.course_inclasstype
                FROM smc_class as a,smc_course as b 
                WHERE a.class_id = '{$request['class_id']}' and a.course_id = b.course_id   ");
        if($courseOne['course_inclasstype'] == '3'){
            $audOne = $this->DataControl->selectOne(" select 1 from crm_client_audition as aud where aud.hour_id = '{$request['hour_id']}' limit 0,1 ");
            if($audOne) {
                $this->errortip = "本节公开课存在过试听记录，禁止取消！";
                return false;
            }
        }

        $hourOne = $this->DataControl->getFieldOne('smc_class_hour', "hour_id,hour_ischecking,class_id,course_id,hour_iswarming,hour_day,hour_starttime,hour_endtime", "hour_id={$request['hour_id']}");

        if (isset($request['is_batch']) && $request['is_batch'] == 1) {
            $date = date("Y-m-d");
            if ($hourOne['hour_day'] < $date) {
                $this->errortip = "已过上课时间,请单独取消";
                return false;
            } elseif ($hourOne['hour_day'] == $date && date("H:i") >= $hourOne['hour_starttime']) {
                $this->errortip = "已过上课时间,请单独取消";
                return false;
            }
        }

        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$this->school_id}' and ((adjustapply_type=0 and class_id='{$request['class_id']}' and hour_id='{$request['hour_id']}') or (adjustapply_type=1 and adjustapply_day='{$hourOne['hour_day']}')) and adjustapply_status=0")){
            $this->error = true;
            $this->errortip = "班级/校区存在待审核调课,不可取消";
            return false;
        }


        $data = array();
        $data['hour_ischecking'] = -1;
        $data['hour_updatatime'] = time();
        $data['hour_cancelnote'] = $request['hour_cancelnote'];
        $classOne = $this->DataControl->getFieldOne('smc_class', "class_id,class_enddate,class_cnname,class_hournums", "class_id='{$hourOne['class_id']}'");

        if ($hourOne['hour_ischecking'] == -1) {
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->errortip = "该课时已经上课";
            return false;
        }

        $courseOne = $this->DataControl->getFieldOne('smc_course', "course_inclasstype", "course_id='{$hourOne['course_id']}'");
//        if ($courseOne['course_inclasstype'] == 1) {
//            $this->errortip = "期度类班级暂不支持";
//            return false;
//        }
        if ($courseOne['course_inclasstype'] == 0 && $hourOne['hour_iswarming'] == 0) {
            $last_hourOne = $this->DataControl->getFieldOne('smc_class_hour', "hour_id", "class_id={$hourOne['class_id']} and hour_ischecking = 0 and hour_iswarming = 0", "order by hour_lessontimes DESC");
            $first_hourOne = $this->DataControl->getFieldOne('smc_class_hour', "hour_id", "class_id={$hourOne['class_id']} and hour_iswarming = 0 and  hour_ischecking <>-1", "order by hour_lessontimes ASC");
            if ($last_hourOne || $first_hourOne) {
                if ($last_hourOne['hour_id'] !== $request['hour_id'] && $first_hourOne['hour_id'] !== $request['hour_id']) {
                    $this->errortip = "只可以取消最后一节课次,或者第一节课次";
                    return false;
                }
            }


        }

        if ($courseOne['course_inclasstype'] == 2) {
            if ($this->DataControl->getFieldOne('smc_class_booking', "booking_id", "hour_id='{$request['hour_id']}' and  booking_status ='0'")) {
                $this->errortip = "请先取消学生的预约";
                return false;
            }
            $bool = $this->DataControl->updateData('smc_class_hour', "hour_id={$request['hour_id']}", $data);
            if ($bool) {
                $last_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}' and  hour_iswarming = 0 and  hour_ischecking <> '-1' ", "order by hour_day DESC");
                if ($last_hourOne && ($last_hourOne['hour_day'] <> $classOne['class_enddate'])) {
                    $class_data = array();
                    $class_data['class_enddate'] = $last_hourOne['hour_day'];
                    $class_data['class_updatatime'] = time();
                    if ($this->DataControl->updateData("smc_class", "class_id = '{$hourOne['class_id']}'", $class_data)) {
                        $study_data = array();
                        $study_data['booking_status'] = '-1';
                        $study_data['booking_updatatime'] = time();
                        $this->DataControl->updateData("smc_class_booking", "hour_id='{$hourOne['hour_id']}' and class_id = '{$hourOne['class_id']}' and  booking_status =0 ", $study_data);
                    }
                }
                $this->cancelAbsenceHour($request['hour_id'], '取消课次,取消对应的请假记录');
                $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], $this->LgStringSwitch("班务管理->班级管理"), $this->LgStringSwitch('取消课次'), dataEncode($request));
                $this->errortip = "取消成功";

                $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    JOIN smc_parenter_wxchattoken AS w ON w.parenter_id = f.parenter_id 
                        AND w.company_id = s.company_id 
                        AND w.parenter_wxtoken IS NOT NULL 
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}'
                        AND ss.study_isreading = '1'");

                $time = strtotime($hourOne['hour_day']);
                $week = date("w", $time);
                $array = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];

                $course_id = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id = '{$hourOne['class_id']}'");
                $course_cnname = $this->DataControl->getFieldOne("smc_course", "course_cnname", "course_id = '{$course_id['course_id']}'");

                if ($parenter) {
                    foreach ($parenter as &$val) {
                        $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");

                        $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程取消通知'");
                        if ($isset) {
                            $wxid = $isset['masterplate_wxid'];
                        } else {
                            $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程取消通知'");
                            $wxid = $masterplate['masterplate_wxid'];
                        }

                        $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，您的该节课已取消，请注意查收~');
                        $b = $course_cnname['course_cnname'];
                        $c = $student_cnname['student_cnname'];
                        $d = $request['hour_cancelnote'];
                        $e = $hourOne['hour_day'] . ' ' . $hourOne['hour_starttime'] . '-' . $hourOne['hour_endtime'] . '「' . $array[$week] . ' 」';
                        $f = $this->LgStringSwitch('给您带来的不便请您谅解，有问题请联系教师咨询');
                        $g = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?day={$hourOne['hour_day']}&cid={$request['company_id']}&s_id={$val['student_id']}";
                        $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                        $wxteModel->CancelHour($a, $b, $c, $d, $e, $f, $g, $wxid);
                    }
                }

                //更新CRM预约该课次的课

                $this->addClientTrack($request['hour_id'], '取消课次,取消试听');


                return true;
            } else {
                $this->error = 1;
                $this->errortip = "取消失败";
                return false;
            }
        } elseif ($courseOne['course_inclasstype'] == 0) {
            $bool = $this->DataControl->updateData('smc_class_hour', "hour_id={$request['hour_id']}", $data);
            if ($bool) {
                $last_hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}' and  hour_iswarming = 0 and  hour_ischecking <> '-1' ", "order by hour_day DESC");
                if ($last_hourOne && ($last_hourOne['hour_day'] <> $classOne['class_enddate'])) {
                    $class_data = array();
                    $class_data['class_enddate'] = $last_hourOne['hour_day'];
                    $class_data['class_updatatime'] = time();
                    if ($this->DataControl->updateData("smc_class", "class_id = '{$hourOne['class_id']}'", $class_data)) {
                        $study_data = array();
                        $study_data['study_endday'] = $last_hourOne['hour_day'];
                        $study_data['study_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_study", "class_id='{$hourOne['class_id']}' and study_isreading =1", $study_data);
                    }
                }
                $this->cancelAbsenceHour($request['hour_id'], '取消课次,取消对应的请假记录');
                //   $this->addClientTrack($request['hour_id'],'取消课次,取消试听');
                $this->addSmcWorkLog($request['company_id'], $request['school_id'], $request['staffer_id'], $this->LgStringSwitch("班务管理->班级管理"), $this->LgStringSwitch('取消课次'), dataEncode($request));
                $this->errortip = "取消成功";


                $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    JOIN smc_parenter_wxchattoken AS w ON w.parenter_id = f.parenter_id 
                        AND w.company_id = s.company_id 
                        AND w.parenter_wxtoken IS NOT NULL 
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}'
                        AND ss.study_isreading = '1'");

                $course_id = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id = '{$hourOne['class_id']}'");
                $course_cnname = $this->DataControl->getFieldOne("smc_course", "course_cnname", "course_id = '{$course_id['course_id']}'");

                if ($parenter) {
                    foreach ($parenter as &$val) {
                        $time = strtotime($hourOne['hour_day']);
                        $week = date("w", $time);
                        $array = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
                        $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");

                        $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程取消通知'");
                        if ($isset) {
                            $wxid = $isset['masterplate_wxid'];
                        } else {
                            $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程取消通知'");
                            $wxid = $masterplate['masterplate_wxid'];
                        }

                        $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，您的该节课已取消，请注意查收~');
                        $b = $course_cnname['course_cnname'];
                        $c = $student_cnname['student_cnname'];
                        $d = $request['hour_cancelnote'];
                        $e = $hourOne['hour_day'] . ' ' . $hourOne['hour_starttime'] . '-' . $hourOne['hour_endtime'] . '「' . $array[$week] . ' 」';
                        $f = $this->LgStringSwitch('给您带来的不便请您谅解，有问题请联系教师咨询');
                        $g = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?day={$hourOne['hour_day']}&cid={$request['company_id']}&s_id={$val['student_id']}";
                        $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                        $wxteModel->CancelHour($a, $b, $c, $d, $e, $f, $g, $wxid);
                    }
                }


                $this->addClientTrack($request['hour_id'], '取消课次,取消试听');

                return true;
            } else {
                $this->errortip = "取消失败";
                return false;
            }
        } elseif ($courseOne['course_inclasstype'] == 1) {

            $this->DataControl->updateData('smc_class_hour', "hour_id={$request['hour_id']}", $data);
            $class_data = array();
            $class_data['class_updatatime'] = time();
            $class_data['class_hournums'] = $classOne['class_hournums'] - 1 > 0 ? $classOne['class_hournums'] - 1 : 0;
            $this->cancelAbsenceHour($request['hour_id'], '取消课次,取消对应的请假记录');
            $this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $class_data);
            $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    JOIN smc_parenter_wxchattoken AS w ON w.parenter_id = f.parenter_id 
                        AND w.company_id = s.company_id 
                        AND w.parenter_wxtoken IS NOT NULL 
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}'
                        AND ss.study_isreading = '1'");

            $course_id = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id = '{$hourOne['class_id']}'");
            $course_cnname = $this->DataControl->getFieldOne("smc_course", "course_cnname", "course_id = '{$course_id['course_id']}'");

            if ($parenter) {
                foreach ($parenter as &$val) {
                    $time = strtotime($hourOne['hour_day']);
                    $week = date("w", $time);
                    $array = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
                    $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");

                    $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程取消通知'");
                    if ($isset) {
                        $wxid = $isset['masterplate_wxid'];
                    } else {
                        $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程取消通知'");
                        $wxid = $masterplate['masterplate_wxid'];
                    }

                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，您的该节课已取消，请注意查收~');
                    $b = $course_cnname['course_cnname'];
                    $c = $student_cnname['student_cnname'];
                    $d = $request['hour_cancelnote'];
                    $e = $hourOne['hour_day'] . ' ' . $hourOne['hour_starttime'] . '-' . $hourOne['hour_endtime'] . '「' . $array[$week] . ' 」';
                    $f = $this->LgStringSwitch('给您带来的不便请您谅解，有问题请联系教师咨询');
                    $g = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?day={$hourOne['hour_day']}&cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->CancelHour($a, $b, $c, $d, $e, $f, $g, $wxid);
                }
            }


            $this->addClientTrack($request['hour_id'], '取消课次,取消试听');
            $this->error = 0;
            $this->errortip = "取消成功";
            return true;

//            $studyOne = $this->DataControl->getFieldOne("smc_student_study", "study_id", "class_id='{$hourOne['class_id']}' and study_isreading=1 ");
//            if ($studyOne) {
//                $this->error = 1;
//                $this->errortip = "该班级存在学员,不可取消";
//                return false;
//            } else {
//                $this->DataControl->updateData('smc_class_hour', "hour_id={$request['hour_id']}", $data);
//                $class_data = array();
//                $class_data['class_hournums'] = $classOne['class_hournums'] - 1 > 0 ? $classOne['class_hournums'] - 1 : 0;
//                $this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $class_data);
//                $this->error = 0;
//                $this->errortip = "取消成功";
//                return true;
//            }
        } elseif ($courseOne['course_inclasstype'] == 3) {
            $this->DataControl->updateData('smc_class_hour', "hour_id={$request['hour_id']}", $data);
            //更新CRM预约该课次的课
            $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    JOIN smc_parenter_wxchattoken AS w ON w.parenter_id = f.parenter_id 
                        AND w.company_id = s.company_id 
                        AND w.parenter_wxtoken IS NOT NULL 
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}'
                        AND ss.study_isreading = '1'");

            $course_id = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id = '{$hourOne['class_id']}'");
            $course_cnname = $this->DataControl->getFieldOne("smc_course", "course_cnname", "course_id = '{$course_id['course_id']}'");

            if ($parenter) {
                foreach ($parenter as &$val) {
                    $time = strtotime($hourOne['hour_day']);
                    $week = date("w", $time);
                    $array = ["星期天", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
                    $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");

                    $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程取消通知'");
                    if ($isset) {
                        $wxid = $isset['masterplate_wxid'];
                    } else {
                        $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程取消通知'");
                        $wxid = $masterplate['masterplate_wxid'];
                    }

                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，您的该节课已取消，请注意查收~');
                    $b = $course_cnname['course_cnname'];
                    $c = $student_cnname['student_cnname'];
                    $d = $request['hour_cancelnote'];
                    $e = $hourOne['hour_day'] . ' ' . $hourOne['hour_starttime'] . '-' . $hourOne['hour_endtime'] . '「' . $array[$week] . ' 」';
                    $f = $this->LgStringSwitch('给您带来的不便请您谅解，有问题请联系教师咨询');
                    $g = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?day={$hourOne['hour_day']}&cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->CancelHour($a, $b, $c, $d, $e, $f, $g, $wxid);
                }
            }


            $this->addClientTrack($request['hour_id'], '取消课次,取消试听');
            $this->error = 0;
            $this->errortip = "取消成功";
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "取消失败";
            return false;
        }
    }

    /**
     * 增加跟踪记录
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/1 0001
     */
    private function addClientTrack($hour_id, $track_note = '')
    {

        $audtionList = $this->DataControl->selectClear("select au.client_id,au.school_id from crm_client_audition as au where au.hour_id='{$hour_id}' and hour_id >0  and audition_isvisit =0");
        //更新CRM预约该课次的课
        $crm_audition = array();
        $crm_audition['audition_isvisit'] = '-1';
        $crm_audition['audition_novisitreason'] = $track_note;
        $crm_audition['audition_updatetime'] = time();
        $this->DataControl->updateData("crm_client_audition", "hour_id='{$hour_id}' and hour_id>0 and audition_isvisit =0", $crm_audition);
        $class_audition = array();
        $class_audition['audition_isvisit'] = '-1';
        $class_audition['audition_novisitreason'] = $track_note;
        $this->DataControl->updateData("smc_class_hour_audition", "hour_id='{$hour_id}' and hour_id>0 and audition_isvisit =0", $class_audition);


        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name,marketer_id", "staffer_id='{$this->staffer_id}'");
        if (!$marketerOne) {
            $stafferOne = $this->stafferOne;
            $marketer_data = array();
            $marketer_data['staffer_id'] = $stafferOne['staffer_id'];
            $marketer_data['company_id'] = $stafferOne['company_id'];
            $marketer_data['marketer_name'] = $stafferOne['staffer_cnname'];
            $marketer_data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $marketer_data['marketer_img'] = $stafferOne['staffer_img'];
            $marketer_data['marketer_createtime'] = time();
            $id = $this->DataControl->insertData("crm_marketer", $marketer_data);

            $marketerOne = array();
            $marketerOne['marketer_id'] = $id;
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        if ($audtionList) {
            foreach ($audtionList as $key => $value) {
                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['school_id'] = $value['school_id'];
                $trackData['marketer_name'] = $marketerOne['marketer_name'];
                $trackData['marketer_id'] = $marketerOne['marketer_id'];
                $trackData['track_validinc'] = '1';
                $trackData['track_linktype'] = '取消试听';
                $trackData['track_note'] = $track_note;
                $trackData['track_isactive'] = '0';
                $trackData['track_createtime'] = time();
                $this->DataControl->insertData("crm_client_track", $trackData);
            }
        }
    }

    /**
     * 增加跟踪记录  --- 因课次调整改变时间（日期不变）
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/1 0001
     */
    private function addeditClientTrack($hour_id, $track_note = '', $oldtime = '')
    {
        $HourOne = $this->DataControl->selectOne("select hour_day,hour_starttime from smc_class_hour where hour_id='{$hour_id}' and hour_id >0 limit 0,1 ");
        //更新CRM预约该课次的课
        $crm_audition = array();
        $crm_audition['audition_visittime'] = $HourOne['hour_day'] . ' ' . $HourOne['hour_starttime'] . ':00';
        $crm_audition['audition_updatetime'] = time();
        $this->DataControl->updateData("crm_client_audition", "hour_id='{$hour_id}' and hour_id>0 and audition_isvisit =0", $crm_audition);

        //时间调整添加一条无效记录
        $audtionList = $this->DataControl->selectClear("select au.client_id,au.school_id from crm_client_audition as au where au.hour_id='{$hour_id}' and hour_id >0  and audition_isvisit =0");
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_name,marketer_id", "staffer_id='{$this->staffer_id}'");
        if (!$marketerOne) {
            $stafferOne = $this->stafferOne;
            $marketer_data = array();
            $marketer_data['staffer_id'] = $stafferOne['staffer_id'];
            $marketer_data['company_id'] = $stafferOne['company_id'];
            $marketer_data['marketer_name'] = $stafferOne['staffer_cnname'];
            $marketer_data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $marketer_data['marketer_img'] = $stafferOne['staffer_img'];
            $marketer_data['marketer_createtime'] = time();
            $id = $this->DataControl->insertData("crm_marketer", $marketer_data);

            $marketerOne = array();
            $marketerOne['marketer_id'] = $id;
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        if ($audtionList) {
            foreach ($audtionList as $key => $value) {
                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['school_id'] = $value['school_id'];
                $trackData['marketer_name'] = $marketerOne['marketer_name'];
                $trackData['marketer_id'] = $marketerOne['marketer_id'];
                $trackData['track_validinc'] = '0';
                $trackData['track_linktype'] = '系统新增';
                $trackData['track_note'] = $track_note;
                $trackData['track_followmode'] = '2';
                $trackData['track_visitingtime'] = $crm_audition['audition_visittime'];
                $trackData['track_isactive'] = '0';
                $trackData['track_createtime'] = time();
                $this->DataControl->insertData("crm_client_track", $trackData);
            }
        }


//        $audtionList = $this->DataControl->selectClear("select au.client_id,au.school_id from crm_client_audition as au where au.hour_id='{$hour_id}' and hour_id >0  and audition_isvisit =0");
//        if ($audtionList) {
//            foreach ($audtionList as $key => $value) {
//                $trackData = array(); 
//                $trackData['track_visitingtime'] = $oldtime;
//                $this->DataControl->updateData("crm_client_track", "client_id='{$value['client_id']}' and school_id='{$value['school_id']}' and track_validinc = '1' and track_followmode = '2' ",$trackData);
//            }
//        }
    }

    /**
     * @param $request
     * @return bool
     * 调整上课日期
     */
    function adjustCourseDay($request)
    {
        $hourOne = $this->DataControl->getOne('smc_class_hour', "hour_id='{$request['hour_id']}'");
        $classOne = $this->DataControl->getOne('smc_class', "class_id='{$hourOne['class_id']}'");
        if ($hourOne['hour_ischecking'] == -1) {
            $this->error = 1;
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "该课时已上课";
            return false;
        }
        if (!empty($request['hour_day'])) {
            $hour_day = date('Y-m-d', strtotime($request['hour_day']));
        } else {
            $this->error = 1;
            $this->errortip = "请选择调课日期";
            return false;
        }
        if ($classOne['class_stdate'] > $hour_day) {
            $this->error = 1;
            $this->errortip = "请勿调整到开班时间之前";
            return false;
        }
//        if ($request['hour_day'] < date("Y-m-d")) {
//            $this->error = 1;
//            $this->errortip = "请选择今日及以后的日期";
//            return false;
//        }
        if ($request['hour_day'] == $hourOne['hour_day']) {
            $this->error = 1;
            $this->errortip = "请选择以后的日期";
            return false;
        }
//        $courseOne = $this->DataControl->getFieldOne('smc_course', "course_inclasstype", "course_id='{$hourOne['course_id']}'");
//        if ($courseOne['course_inclasstype'] == 1) {
//            $this->error = 1;
//            $this->errortip = "暂不支持期度类班级调课";
//            return false;
//        }
        //不可以调整到已考勤的课次前面
        $checkHourOne = $this->DataControl->selectOne("select  hour_day,hour_endtime,hour_starttime from smc_class_hour as ch where ch.class_id = '{$hourOne['class_id']}' and hour_ischecking =1  order by hour_lessontimes  DESC limit 0,1");
        if ($checkHourOne) {
            if ($request['hour_day'] < $checkHourOne['hour_day']) {
                $this->error = 1;
                $this->errortip = "请勿调整到已考勤的课次之前";
                return false;
            }
            if ($request['hour_day'] == $checkHourOne['hour_day']) {
                if ($hourOne['hour_starttime'] < $checkHourOne['hour_endtime']) {
                    $this->error = 1;
                    $this->errortip = "上课时间与该天已考勤的上课时间冲突";
                    return false;
                }
            }
        }
        $dataOne = $this->DataControl->selectOne("
			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id,h.hour_way,h.hour_number
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}' limit 0,1 ");

        if (!$dataOne) {
            $this->error = 1;
            $this->errortip = "查无数据";
            $this->conflict = 0;
            return false;
        }
        if (isset($request['conflict']) && $request['conflict'] == 1) {
            $hourDayList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type =0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_day='{$hour_day}' and h.hour_id <>{$request['hour_id']}  and c.school_id ='{$request['school_id']}' and (s.staffer_id='{$request['re_staffer_id']}' or h.classroom_id='{$request['classroom_id']}') ");


            $dataOne['hour_day'] = $hour_day;
            $hourDayList['one'] = $dataOne;

            $dataList = array_values($hourDayList);
            if (count($dataList) > 1) {
                $TimetableModel = new \Model\Smc\TimetableModel($this->publicarray);
                $dataList = $TimetableModel->checkClassroomConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['classroom_confict'] == -2) {
                            $this->error = 1;
                            $this->errortip = "冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
                $dataList = $TimetableModel->checkStafferConflict($dataList);

                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
            }
        }
        $data = array();
        $data['hour_day'] = $hour_day;
        if (!$data['hour_day']) {
            $this->error = 1;
            $this->errortip = "请选择日期";
            $this->conflict = 0;
            return false;
        }

        $data['hour_updatatime'] = time();
        if ($this->DataControl->updateData('smc_class_hour', "hour_id='{$request['hour_id']}'", $data)) {
            // 同时更改网课的时间
            if ($dataOne['hour_way'] == 1) {
                $lineData = array();
                $lineData['linerooms_starttime'] = strtotime($data['hour_day'] . ' ' . $dataOne['hour_starttime']);
                $lineData['linerooms_endtime'] = strtotime($data['hour_day'] . ' ' . $dataOne['hour_endtime']);
                $lineData['linerooms_issync'] = "0";
                $this->DataControl->updateData("smc_linerooms", "class_id='{$dataOne['class_id']}' and hour_id='{$dataOne['hour_id']}'", $lineData);
            }


            $this->addClientTrack($request['hour_id'], '调整上课日期,取消试听');

            $classEnd = $this->DataControl->getFieldOne('smc_class', "class_id,class_enddate,course_id", "class_id='{$hourOne['class_id']}'");
            $course = $this->DataControl->getFieldOne('smc_course', "course_cnname", "course_id='{$classOne['course_id']}'");
            $arr_last_day = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}'", "order by hour_day DESC");
            $last_day = $arr_last_day['hour_day'];
            if ($classEnd['class_enddate'] <> $last_day) {
                $json = array();
                $json['class_enddate'] = $last_day;
                $json['class_updatatime'] = time();
                if ($this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $json)) {
                    $studyData = array();
                    $studyData['study_endday'] = $last_day;
                    $studyData['study_updatetime'] = time();
                    $this->DataControl->updateData("smc_student_study", "study_isreading =1 and class_id='{$hourOne['class_id']}'", $studyData);
                }
            }
            if ($hourOne['hour_day'] > $hour_day) {
                $tempyday = $hourOne['hour_day'];
                $hourOne['hour_day'] = $hour_day;
                $hour_day = $tempyday;
            }
            if ($hourOne['hour_iswarming'] == 0) {
                if (isset($request['is_clear']) && $request['is_clear'] == 0) {
                    $clear = 0;
                } else {
                    $clear = 1;
                }
                if ($hourOne['hour_way'] == 0) {
                    $hourOne['hour_number'] = 0;
                }
                $this->adjustLessonDay($hourOne['class_id'], $request['hour_id'], $clear, $hourOne['hour_number']);
            }


            $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss 
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    left join smc_parenter_wxchattoken as w on w.parenter_id = f.parenter_id and w.company_id = s.company_id and w.parenter_wxtoken is NOT NULL
                    WHERE
                        ss.class_id = '{$hourOne['class_id']}' 
                        AND ss.study_isreading = '1'");

            $class_cnname = $this->DataControl->selectOne("
                    SELECT
                        h.class_id,
                        c.class_cnname
                    FROM
                        smc_class_hour AS h
                        left join smc_class as c on h.class_id= c.class_id
                        WHERE h.hour_id = '{$request['hour_id']}'");

            foreach ($parenter as &$val) {
                $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");
                $school_cnname = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$request['school_id']}'");
                $coursetime = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_time", "course_id =     '{$classOne['course_id']}' and student_id = '{$val['student_id']}' and school_id = '{$request['school_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程变动提醒'");
                if ($isset) {
                    $wxid = $isset['masterplate_wxid'];
                } else {
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程变动提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，本课程的课表有所调整，请及时查看。');
                $b = $school_cnname['school_cnname'];
                $c = $class_cnname['class_cnname'];
                $d = '您' . $course['course_cnname'] . '课程剩余' . $coursetime['coursebalance_time'] . '次未上课信息有调整';
                $e = '点击可查看课表详情哦~';
                $f = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?cid={$request['company_id']}&s_id={$val['student_id']}";
                $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                $wxteModel->ChangeHour($a, $b, $c, $d, $e, $f, $wxid);
            }

            $this->error = 0;
            $this->conflict = 0;
            $this->errortip = "调整成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "调整失败";
            $this->conflict = 0;
            return false;
        }

    }

    /**
     *  批量调整上课日期
     * author: ling
     * 对应接口文档 0001
     * @param $class_id
     */
    function adjustChooseCourseDay($request)
    {
        $arr_hour_id = json_decode(stripslashes($request['arr_hour_id']), true);
        if (is_array($arr_hour_id) && count($arr_hour_id) > 0) {
            $str_hour_id = implode(',', $arr_hour_id);
        } else {
            $this->error = 1;
            $this->errortip = "课次错误";
            return false;
        }
        $hourList = $this->DataControl->selectClear(
            "select ch.* ,c.course_inclasstype
                  from smc_class_hour as ch  
                  left join  smc_course as c ON ch.course_id = c.course_id 
                  where ch.hour_id  in  ({$str_hour_id})");
        if (!$hourList) {
            $this->error = 1;
            $this->errortip = "未查询到课次";
            return false;
        } else {
            $arr_hour_ischecking = array_column($hourList, "hour_ischecking");
            $arr_hour_course = array_column($hourList, "course_inclasstype");
            if (in_array('2', $arr_hour_course)) {
                $this->error = 1;
                $this->adjust_error = 1;
                $this->errortip = "存在预约类班级,无法调课";
                return false;
            }
            if (in_array('1', $arr_hour_course)) {
                $this->error = 1;
                $this->adjust_error = 1;
                $this->errortip = "存在期度类班级,无法调课";
                return false;
            }
            if (in_array('-1', $arr_hour_ischecking)) {
                $this->error = 1;
                $this->errortip = "该课时已经取消";
                return false;
            } elseif (in_array('1', $arr_hour_ischecking)) {
                $this->error = 1;
                $this->errortip = "该课时已上课";
                return false;
            }
            if (!empty($request['hour_day'])) {
                $hour_day = date('Y-m-d', strtotime($request['hour_day']));
            } else {
                $this->error = 1;
                $this->errortip = "请选择调课日期";
                return false;
            }
            if ($request['hour_day'] < date("Y-m-d")) {
                $this->error = 1;
                $this->errortip = "请选择今日以后的日期";
                return false;
            }
        }
        if (isset($request['conflict']) && $request['conflict'] == 1) {
            $hourDayList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,c.class_enname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type =0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_day='{$hour_day}' and c.school_id ='{$request['school_id']}' ");

            $dataList = array_values($hourDayList);
            $conflict_array = array();
            if (count($dataList) > 1) {
                $TimetableModel = new \Model\Smc\TimetableModel($this->publicarray);
                $dataList = $TimetableModel->checkClassroomConflict($dataList);
                $dataList = $TimetableModel->checkStafferConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_ischecking'] == -2) {
                            $conflict_array[] = $value;
                        }
                    }
                }
                if ($conflict_array) {
                    foreach ($conflict_array as $k => $v) {

                        $conflict_array[$k]['time'] = $v['hour_starttime'] . '-' . $v['hour_endtime'];
                    }
                    $this->error = 1;
                    $this->errortip = "冲突";
                    $this->conflict = -2;
                    $this->conflictData = $conflict_array;
                    return false;
                }
            }
        }
        $data = array();
        $data['hour_day'] = $hour_day;
        if (!$data['hour_day']) {
            $this->error = 1;
            $this->errortip = "请选择日期";
            $this->conflict = 0;
            return false;
        }
        if ($arr_hour_id) {
            foreach ($arr_hour_id as $value) {

                $classEnd = $this->DataControl->selectOne("
                    select ss.class_id,class_enddate,ch.hour_way,ch.hour_number,ch.hour_id
                    from smc_class_hour as ch  
                    left join smc_class as  ss ON ch.class_id =ss.class_id
                    where ch.hour_id = '{$value}'                    
                    ");

                //不可以调整到已考勤的课次前面
                $checkHourOne = $this->DataControl->selectOne("select  hour_day,hour_endtime,hour_starttime from smc_class_hour as ch where ch.class_id = '{$classEnd['class_id']}' and hour_ischecking =1  order by hour_lessontimes  DESC limit 0,1");
                if ($checkHourOne) {
                    if ($request['hour_day'] < $checkHourOne['hour_day']) {
                        $this->error = 1;
                        $this->errortip = "请勿调整到已考勤的课次之前";
                        return false;
                    }
                }
                $data['hour_updatatime'] = time();
                if (!$this->DataControl->updateData('smc_class_hour', "hour_id='{$value}'", $data)) {
                    $this->error = 0;
                    $this->conflict = 0;
                    $this->errortip = "更新失败";
                    return false;
                }
                // 同时更改网课的时间
                if ($classEnd['hour_way'] == 1) {
                    $lineData = array();
                    $lineData['linerooms_starttime'] = strtotime($data['hour_day'] . ' ' . $classEnd['hour_starttime']);
                    $lineData['linerooms_endtime'] = strtotime($data['hour_day'] . ' ' . $classEnd['hour_endtime']);
                    $lineData['linerooms_issync'] = "0";
                    $this->DataControl->updateData("smc_linerooms", "class_id='{$classEnd['class_id']}' and hour_id='{$classEnd['hour_id']}'", $lineData);
                }
                //更新CRM预约该课次的课

                $this->addClientTrack($request['hour_id'], '调整上课日期,取消试听');

                if ($request['hour_day'] > $classEnd['class_enddate']) {
                    $endData = array();
                    $endData['class_enddate'] = $request['hour_day'];
                    $endData['class_updatatime'] = time();
                    $this->DataControl->updateData('smc_class', "class_id='{$classEnd['class_id']}'", $endData);
                }
                if ($classEnd['hour_way'] == 0) {
                    $classEnd['hour_number'] = 0;
                }
                $this->adjustLessonDay($classEnd['class_id'], 0, 0, $classEnd['hour_number']);
            }
            $this->error = 0;
            $this->conflict = 0;
            $this->errortip = "调整成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "调整失败";
            $this->conflict = 0;
            return false;
        }
    }

    function adjustLessonDay($class_id, $hour_id = 0, $is_clear = 0, $hour_number = 0)
    {
        $courseOne = $this->DataControl->selectOne("select c.course_id,c.course_inclasstype from smc_course as c, smc_class as s where c.course_id=s.course_id and s.class_id='{$class_id}' ");
        if ($courseOne['course_inclasstype'] == 2 && $hour_id !== 0 && $is_clear == 1) {
            $data = array();
            $data['booking_status'] = '-1';
            $data['booking_updatatime'] = time();
            $this->DataControl->updateData("smc_class_booking", "hour_id='{$hour_id}' and class_id='{$class_id}'", $data);
        }
        if ($hour_number !== 0) {
            $this->DataControl->query(" UPDATE smc_linerooms AS l, smc_class_hour AS h SET l.linerooms_name = h.hour_name WHERE h.hour_number = l.linerooms_number AND l.linerooms_number = '{$hour_number}'");
        }
        $this->cancelAbsenceHour($hour_id, '调整排课,取消对应的请假记录');

        $sql = "call RefreshClass('{$class_id}')";
        $this->DataControl->query($sql);

        $hourList = $this->DataControl->selectClear("select hour_id,hour_name,hour_lessontimes from smc_class_hour where class_id='{$class_id}' and hour_ischecking <>'-2' and hour_way =1 ");
        if ($hourList) {
            foreach ($hourList as $key => $value) {
                $linerooms_data = array();
                $linerooms_data['linerooms_name'] = $value['hour_name'];
                $linerooms_data['hour_lessontimes'] = $value['hour_lessontimes'];
                $this->DataControl->updateData("smc_linerooms", "hour_id='{$value['hour_id']}'", $linerooms_data);
            }
        }
    }


    function getTeacherClass($request)
    {
        $hour_day = date('Y-m-d', strtotime($request['hour_day']));
        $teaClass = $this->DataControl->selectClear(
            "select h.hour_starttime,h.hour_endtime,c.class_cnname,r.classroom_cnname from  smc_class_hour_teaching as t
					left JOIN   smc_class_hour as h On h.hour_id = t.hour_id
					left JOIN   smc_class as c On c.class_id = t.class_id
					left JOIN   smc_classroom as r On r.classroom_id = h.classroom_id
					where t.staffer_id = '{$request['teacher_staffer_id']}' and  h.hour_day='{$hour_day}' and t.teaching_type =0");
        $data = array();
        if ($teaClass) {
            foreach ($teaClass as $key => $value) {
                $data[$key]['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $data[$key]['class_cnname'] = $value['class_cnname'];
                $data[$key]['classroom_cnname'] = $value['classroom_cnname'];
            }
        }

        if (!$data) {
            $data = array();
        }

        return $data;
    }

    /**
     * @param $request
     * @return bool
     * 根据教师调整时间
     */
    function adjustCourseTimeByStaffer($request)
    {
        if (!$request['hour_starttime'] || !$request['hour_endtime']) {
            $this->error = 1;
            $this->errortip = "请选择需要调整的时间";
            return false;
        }

        $hourOne = $this->DataControl->getOne('smc_class_hour', "hour_id='{$request['hour_id']}' ");
        $classOne = $this->DataControl->getOne('smc_class', "class_id='{$hourOne['class_id']}'");

        if ($hourOne['hour_ischecking'] == -1) {
            $this->error = 1;
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "该课时已上课";
            return false;
        }

//        $courseOne = $this->DataControl->getFieldOne('smc_course', "course_inclasstype", "course_id='{$hourOne['course_id']}'");
//        if ($courseOne['course_inclasstype'] == 1) {
//            $this->error = 1;
//            $this->errortip = "暂不支持期度类班级调课";
//            return false;
//        }
        $dataOne = $this->DataControl->selectOne("
			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id,h.hour_way,h.hour_number
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type =0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}'");
        if (!$dataOne) {
            $this->error = 1;
            $this->errortip = "查无数据";
            $this->conflict = 0;
            return false;
        }
        $hour_day = date("Y-m-d", strtotime($dataOne['hour_day']));
        $temOne = array();
        $temOne['hour_starttime'] = $dataOne['hour_starttime'];
        $temOne['hour_endtime'] = $dataOne['hour_endtime'];
        $dataOne['hour_starttime'] = $request['hour_starttime'];
        $dataOne['hour_endtime'] = $request['hour_endtime'];

        if ($classOne['class_stdate'] > $hour_day) {
            $this->error = 1;
            $this->errortip = "请勿调整到开班时间之前";
            return false;
        }

        //不可以调整到已考勤的课次前面
        $checkHourOne = $this->DataControl->selectOne("select  hour_day,hour_endtime,hour_starttime from smc_class_hour as ch where ch.class_id = '{$hourOne['class_id']}' and hour_ischecking =1  order by hour_lessontimes  DESC limit 0,1");

        if ($checkHourOne) {
            if ($request['hour_day']) {
                if ($request['hour_day'] < $checkHourOne['hour_day']) {
                    $this->error = 1;
                    $this->errortip = "请勿调整到已考勤的课次之前";
                    return false;
                }
            }
            if ($hourOne['hour_day'] == $checkHourOne['hour_day']) {
                if ($request['hour_starttime'] < $checkHourOne['hour_endtime']) {
                    $this->error = 1;
                    $this->errortip = "上课时间与该天已考勤的上课时间冲突";
                    return false;
                }
            }
        }
        if ($request['hour_day']) {
            $hourOne['hour_day'] = $request['hour_day'];
        }
        if (isset($request['conflict']) && $request['conflict'] == 1) {
            $hourDayList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type =0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_day='{$hour_day}' and h.hour_id <>'{$request['hour_id']}' and c.school_id ='{$request['school_id']}'");

            $hourDayList['one'] = $dataOne;

            $dataList = array_values($hourDayList);
            if (count($dataList) > 1) {
                $TimetableModel = new \Model\Smc\TimetableModel($this->publicarray);
                $dataList = $TimetableModel->checkClassroomConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['classroom_confict'] == -2) {
                            $this->error = 1;
                            $this->errortip = "冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
                $dataList = $TimetableModel->checkStafferConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
            }
        }

        $data = array();
        $data['hour_starttime'] = $request['hour_starttime'];
        $data['hour_endtime'] = $request['hour_endtime'];
        $data['hour_day'] = $hourOne['hour_day'];
        $data['hour_updatatime'] = time();

//		if($hourOne['hour_starttime'] < $request['hour_starttime'] ){
//			$starttime = $hourOne['hour_starttime'] ;
//			$endtime = $request['hour_starttime'];
//		}else{
//			$starttime =$request['hour_starttime'] ;
//			$endtime = $hourOne['hour_starttime'] ;
//		}


        if ($this->DataControl->updateData('smc_class_hour', "hour_id='{$request['hour_id']}' ", $data)) {

            // 同时更改网课的时间
            if ($dataOne['hour_way'] == 1) {
                $lineData = array();
                $lineData['linerooms_starttime'] = strtotime($data['hour_day'] . ' ' . $data['hour_starttime']);
                $lineData['linerooms_endtime'] = strtotime($data['hour_day'] . ' ' . $data['hour_endtime']);
                $lineData['linerooms_issync'] = "0";
                $this->DataControl->updateData("smc_linerooms", "class_id='{$dataOne['class_id']}' and hour_id='{$dataOne['hour_id']}'", $lineData);
            }
            if ($temOne['hour_starttime'] <> $data['hour_starttime'] || $temOne['hour_endtime'] <> $data['hour_endtime']) {
                //更新CRM预约该课次的课

                $this->addClientTrack($request['hour_id'], '因课次调整,试听取消');
            }


            if ($hourOne['hour_iswarming'] == 0) {
                if (isset($request['is_clear']) && $request['is_clear'] == 0) {
                    $clear = 0;
                } else {
                    $clear = 1;
                }
                if ($hourOne['hour_way'] == 0) {
                    $hourOne['hour_number'] = 0;
                }
                $this->adjustLessonDay($hourOne['class_id'], $request['hour_id'], $clear, $hourOne['hour_number']);
                $classEnd = $this->DataControl->getFieldOne('smc_class', "class_id,class_enddate", "class_id='{$hourOne['class_id']}'");
                $arr_last_day = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}'", "order by hour_day DESC");
                $last_day = $arr_last_day['hour_day'];
                if ($classEnd['class_enddate'] <> $last_day) {
                    $json = array();
                    $json['class_enddate'] = $last_day;
                    $json['class_updatatime'] = time();
                    if ($this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $json)) {
                        $studyData = array();
                        $studyData['study_endday'] = $last_day;
                        $studyData['study_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_study", "study_isreading =1 and class_id='{$hourOne['class_id']}'", $studyData);
                    }
                }
            }

            $this->error = 0;
            $this->errortip = "调整成功";
            $this->conflict = 0;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "调整失败";
            $this->conflict = 0;
            return false;
        }
    }

    /**
     * @return array
     *
     */
    function getClassroomClass($request)
    {
        $hour_day = date('Y-m-d', strtotime($request['hour_day']));
        $teaClass = $this->DataControl->selectClear(
            "select h.hour_starttime,h.hour_endtime,c.class_cnname,s.staffer_cnname
					from  smc_class_hour_teaching as t
					left JOIN   smc_class_hour as h On h.hour_id = t.hour_id
					left JOIN   smc_class as c On c.class_id = t.class_id
					left JOIN   smc_staffer as s On s.staffer_id = t.staffer_id
					where h.classroom_id = '{$request['classroom_id']}' and  h.hour_day='{$hour_day}'  and t.teaching_type=0");

        $data = array();
        if ($teaClass) {
            foreach ($teaClass as $key => $value) {

                $data[$key]['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $data[$key]['class_cnname'] = $value['class_cnname'];
                $data[$key]['staffer_cnname'] = $value['staffer_cnname'];
            }
        }
        if (!$data) {
            $data = array();
        }
        return $data;
    }

    /**
     * @param $request
     * @return bool
     * 调整上课时间通过教室
     */
    function adjustCourseTimeByRoom($request)
    {
        if (!$request['hour_starttime'] || !$request['hour_endtime']) {
            $this->error = 1;
            $this->errortip = "请选择需要调整的时间";
            return false;
        }

        $hourOne = $this->DataControl->getOne('smc_class_hour', "hour_id='{$request['hour_id']}' ");
        $classOne = $this->DataControl->getOne('smc_class', "class_id='{$hourOne['class_id']}'");
        if ($hourOne['hour_ischecking'] == -1) {
            $this->error = 1;
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "该课时已上课";
            return false;
        }
//        if (($hourOne['hour_day'] . "" . $hourOne['hour_starttime']) < date('Y-m-d H:i:s')) {
//            $this->error = 1;
//            $this->errortip = "该课程已过时间";
//            return false;
//        }
//        $courseOne = $this->DataControl->getFieldOne('smc_course', "course_inclasstype", "course_id='{$hourOne['course_id']}'");
//        if ($courseOne['course_inclasstype'] == 1) {
//            $this->error = 1;
//            $this->errortip = "暂不支持期度类班级调课";
//            return false;
//        }
//        $dataOne1 = $this->DataControl->selectOne("
//			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id,h.hour_way
// 				from smc_class_hour as h
//				left JOIN  smc_class as c ON  c.class_id =h.class_id
//				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
//				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id  and t.teaching_type=0
//				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
//				where h.hour_id='{$request['hour_id']}'");

        $dataOne = $this->DataControl->selectOne("
			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id,h.hour_way,h.hour_number
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id  and t.teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}'");
        if (!$dataOne) {
            $this->error = 1;
            $this->errortip = "查无数据";
            $this->conflict = 0;
            return false;
        }
        $temOne = array();
        $temOne['oldhour_day'] = $dataOne['hour_day'];
        $temOne['hour_starttime'] = $dataOne['hour_starttime'];
        $temOne['hour_endtime'] = $dataOne['hour_endtime'];
        $hour_day = date("Y-m-d", strtotime($dataOne['hour_day']));
        $dataOne['hour_starttime'] = $request['hour_starttime'];
        $dataOne['hour_endtime'] = $request['hour_endtime'];
        if ($classOne['class_stdate'] > $hour_day) {
            $this->error = 1;
            $this->errortip = "请勿调整到开班时间之前";
            return false;
        }
        //不可以调整到已考勤的课次前面
        $checkHourOne = $this->DataControl->selectOne("select  hour_day,hour_endtime,hour_starttime from smc_class_hour as ch where ch.class_id = '{$hourOne['class_id']}' and hour_ischecking =1  order by hour_lessontimes  DESC limit 0,1");

        if ($checkHourOne) {
            if ($request['hour_day']) {
                if ($request['hour_day'] < $checkHourOne['hour_day']) {
                    $this->error = 1;
                    $this->errortip = "请勿调整到已考勤的课次之前";
                    return false;
                }
            }
            if ($hourOne['hour_day'] == $checkHourOne['hour_day']) {
                if ($request['hour_starttime'] < $checkHourOne['hour_endtime']) {
                    $this->error = 1;
                    $this->errortip = "上课时间与该天已考勤的上课时间冲突";
                    return false;
                }
            }
        }
        if ($request['hour_day']) {
            $hourOne['hour_day'] = $request['hour_day'];
        }
        if (isset($request['conflict']) && $request['conflict'] == 1) {
            $hourDayList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_day='{$hour_day}' and h.hour_id <>'{$request['hour_id']}' and c.school_id ='{$request['school_id']}' ");
            $hourDayList['one'] = $dataOne;
            $dataList = array_values($hourDayList);
            if (count($dataList) > 1) {
                $TimetableModel = new \Model\Smc\TimetableModel($this->publicarray);
                $dataList = $TimetableModel->checkClassroomConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
                $dataList = $TimetableModel->checkStafferConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
            }
        }
        $data = array();
        $data['hour_starttime'] = $request['hour_starttime'];
        $data['hour_endtime'] = $request['hour_endtime'];
        $data['hour_day'] = $hourOne['hour_day'];
        $data['hour_updatatime'] = time();

//		if($hourOne['hour_starttime'] < $request['hour_starttime'] ){
//			$starttime = $hourOne['hour_starttime'] ;
//			$endtime = $request['hour_starttime'];
//		}else{
//			$starttime =$request['hour_starttime'] ;
//			$endtime = $hourOne['hour_starttime'] ;
//		}
        if (!$this->DataControl->updateData('smc_class_hour', "hour_id='{$request['hour_id']}' ", $data)) {

            $this->error = 1;
            $this->errortip = "调整失败";
            $this->conflict = 0;
            return false;
        } else {
            // 同时更改网课的时间
            if ($dataOne['hour_way'] == 1) {
                $lineData = array();
                $lineData['linerooms_starttime'] = strtotime($data['hour_day'] . ' ' . $data['hour_starttime']);
                $lineData['linerooms_endtime'] = strtotime($data['hour_day'] . ' ' . $data['hour_endtime']);
                $lineData['linerooms_issync'] = "0";
                $this->DataControl->updateData("smc_linerooms", "class_id='{$dataOne['class_id']}' and hour_id='{$dataOne['hour_id']}'", $lineData);
            }

            if ($temOne['oldhour_day'] != $request['hour_day'] && isset($request['hour_day']) && $request['hour_day'] != '') {//当日起发生改变是自动取消
                $this->addClientTrack($request['hour_id'], '因课次调整,日期改变,试听取消');
            } elseif (($temOne['oldhour_day'] == $request['hour_day'] || $request['hour_day'] == '') && ($temOne['hour_starttime'] <> $data['hour_starttime'] || $temOne['hour_endtime'] <> $data['hour_endtime'])) {
                $this->addeditClientTrack($request['hour_id'], '因课次调整,日期不变，时间改变，调整试听时间！', $temOne['oldhour_day'] . ' ' . $temOne['hour_starttime'] . ':00');  //当天不需要跟踪记录
            }

            if ($hourOne['hour_iswarming'] == 0) {
                if (isset($request['is_clear']) && $request['is_clear'] == 0) {
                    $clear = 0;
                } else {
                    $clear = 1;
                }
                if ($hourOne['hour_way'] == 0) {
                    $hourOne['hour_number'] = 0;
                }
                $this->adjustLessonDay($hourOne['class_id'], $request['hour_id'], $clear, $hourOne['hour_number']);

                $classEnd = $this->DataControl->getFieldOne('smc_class', "class_id,class_enddate", "class_id='{$hourOne['class_id']}'");
                $arr_last_day = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$hourOne['class_id']}'", "order by hour_day DESC");
                $last_day = $arr_last_day['hour_day'];
                if ($classEnd['class_enddate'] <> $last_day) {
                    $json = array();
                    $json['class_enddate'] = $last_day;
                    $json['class_updatatime'] = time();
                    if ($this->DataControl->updateData("smc_class", "class_id='{$hourOne['class_id']}'", $json)) {
                        $studyData = array();
                        $studyData['study_endday'] = $last_day;
                        $studyData['study_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_study", "study_isreading =1 and class_id='{$hourOne['class_id']}'", $studyData);
                    }
                }
            }
            $parenter = $this->DataControl->selectClear("
                    SELECT
                        ss.student_id,
                        f.parenter_id,
                        s.student_cnname,
	                    p.parenter_cnname
                    FROM
                        smc_student_study AS ss 
                        left join smc_student_family as f on f.student_id = ss.student_id
                    	left join smc_student as s on s.student_id = ss.student_id
	                    left join smc_parenter as p on p.parenter_id = f.parenter_id
	                    left join smc_parenter_wxchattoken as w on w.parenter_id = f.parenter_id and w.company_id = s.company_id and w.parenter_wxtoken is NOT NULL

                    WHERE
                        ss.class_id = '{$hourOne['class_id']}' 
                        AND ss.study_isreading = '1'");

            $class_cnname = $this->DataControl->selectOne("
                    SELECT
                        h.class_id,
                        c.class_cnname
                    FROM
                        smc_class_hour AS h
                        left join smc_class as c on h.class_id= c.class_id
                        WHERE h.hour_id = '{$request['hour_id']}'");

            foreach ($parenter as &$val) {
                $student_cnname = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id = '{$val['student_id']}'");
                $coursetime = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_time", "course_id =     '{$classOne['course_id']}' and student_id = '{$val['student_id']}' and school_id = '{$request['school_id']}'");
                $course = $this->DataControl->getFieldOne('smc_course', "course_cnname", "course_id='{$classOne['course_id']}'");

                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$request['company_id']}' and masterplate_name = '课程变动提醒'");
                if ($isset) {
                    $wxid = $isset['masterplate_wxid'];
                } else {
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '课程变动提醒'");
                    $wxid = $masterplate['masterplate_wxid'];
                }

                if ($request['hour_day']) {
                    $school_cnname = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$request['school_id']}'");
                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，本课程的课表有所调整，请及时查看。');
                    $b = $school_cnname['school_cnname'];
                    $c = $class_cnname['class_cnname'];
                    $d = '您' . $course['course_cnname'] . '课程剩余' . $coursetime['coursebalance_time'] . '次未上课信息有调整';
                    $e = '点击可查看课表详情哦~';
                    $f = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->ChangeHour($a, $b, $c, $d, $e, $f, $wxid);
                } else {
                    $school_cnname = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id = '{$request['school_id']}'");
                    $a = $this->LgStringSwitch($student_cnname['student_cnname'] . '学员您好，本课程的课表有所调整，请及时查看。');
                    $b = $school_cnname['school_cnname'];
                    $c = $class_cnname['class_cnname'];
                    $d = '您' . $course['course_cnname'] . '课程剩余' . $coursetime['coursebalance_time'] . '次未上课信息有调整';
                    $e = '点击可查看课表详情哦~';
                    $f = "https://scptc.kedingdang.com/LookTimetable/lookTimetable?cid={$request['company_id']}&s_id={$val['student_id']}";
                    $wxteModel = new \Model\Api\ZxwxChatModel($val['parenter_id'], $val['student_id']);
                    $wxteModel->ChangeHour($a, $b, $c, $d, $e, $f, $wxid);
                }
            }
            $this->error = 0;
            $this->errortip = "调整成功";
            $this->conflict = 0;
            return true;
        }
    }

    /**
     * @param $request
     * @return array|bool
     *   调整教师 --- 获取班级主教
     */

    function getMainStafferList($request)
    {
        $hour_day = date('Y-m-d', strtotime($request['hour_day']));

        $hour_starttime = $request['hour_starttime'];
        $hour_endtime = $request['hour_endtime'];
        $week_month = (date("m", strtotime($hour_day)));
        $week_day = (date("d", strtotime($hour_day)));
        $week_week = (date("w", strtotime($hour_day)));
        $startTime = mktime(0, 0, 0, $week_month, $week_day - $week_week + 7 - 6, date("Y"));
        $endTime = mktime(0, 0, 0, $week_month, $week_day - $week_week + 7, date("Y"));

        $startWeekDay = date('Y-m-d', $startTime);
        $endWeekDay = date('Y-m-d', $endTime);

        $stafferList = $this->DataControl->selectClear("
			 select s.staffer_id,s.staffer_cnname,cp.post_name,si.info_isforeign,s.staffer_enname,
	    (select count(h.hour_id) from smc_class_hour_teaching as  ht
	    left join smc_class_hour as h ON ht.hour_id = h.hour_id
	    left join smc_class as  c ON c.class_id = h.class_id
	    where ht.staffer_id = s.staffer_id  and h.hour_day >='{$startWeekDay}' and h.hour_day <='{$endWeekDay}' and c.school_id ='{$request['school_id']}' limit 0,1 ) as  hour_num,
	     (select h.hour_id from smc_class_hour_teaching as ht
	    left join smc_class_hour as h ON ht.hour_id = h.hour_id
	    left join smc_class as  c ON c.class_id = h.class_id
	    where s.staffer_id = ht.staffer_id and h.hour_day = '{$hour_day}' and h.hour_starttime <='{$hour_endtime}' and h.hour_endtime >='{$hour_starttime}' and c.school_id ='{$request['school_id']}' limit 0,1 ) as  hour_isbusy
			 from  smc_staffer as s
			 left JOIN gmc_staffer_postbe as p ON p.staffer_id = s.staffer_id
			 left JOIN gmc_company_post as cp ON cp.post_id =p.post_id
			 left JOIN smc_staffer_info as si ON si.staffer_id = s.staffer_id
			 where  p.school_id ='{$request['school_id']}' and cp.post_isteaching = 1 and p.postbe_status = 1 and cp.post_type = 1 and s.staffer_leave = 0
			  and s.staffer_id not in (select staffer_id from smc_class_hour_teaching as ht where ht.hour_id='{$request['hour_id']}' and teaching_isdel=0)
			  group by s.staffer_id

			 ");
        if ($stafferList) {
            foreach ($stafferList as $key => $value) {
                $stafferList[$key]['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                if ($value['hour_isbusy']) {
                    $stafferList[$key]['staffer_status'] = 1;
                } else {
                    $stafferList[$key]['staffer_status'] = 0;
                }
                if ($value['info_isforeign'] != 0) {
                    $stafferList[$key]['info_isforeign'] = $this->LgStringSwitch('[外籍]' . "教师");
                } else {
                    $stafferList[$key]['info_isforeign'] = $this->LgStringSwitch('[中籍]' . "教师");
                }
            }
        } else {
            $stafferList = array();
        }
        return $stafferList;

    }

    /**
     * @param $request
     * @return array|bool
     *  调整教室 --- 获取课程教室
     */
    function getClassroomList($request)
    {
        $datawhere = '1';
        if (isset($request['hour_id']) && $request['hour_id'] > 0) {
            $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "classroom_id", "hour_id='{$request['hour_id']}'");
            if ($hourOne) {
                $datawhere .= " and c.classroom_id <>'{$hourOne['classroom_id']}' ";
            }
        }

        $hour_day = date('Y-m-d', strtotime($request['hour_day']));

        $classroomList = $this->DataControl->selectClear("
			 select c.classroom_id,c.classroom_cnname,c.classroom_maxnums,
			 (select  count(h.hour_id) from  smc_class_hour as h  where  h.classroom_id = c.classroom_id and h.hour_day = '{$hour_day}'  limit 0,1 ) as hour_num,
	  			(select h.hour_id from smc_class_hour as h,smc_class as cs 
	  			where h.classroom_id = c.classroom_id  and h.hour_day = '{$hour_day}' and h.hour_starttime <='{$request['hour_endtime']}' and h.hour_endtime >='{$request['hour_starttime']}' and cs.class_id=h.class_id and cs.class_status <> '-2' and h.hour_ischecking <> '-1'  limit 0,1) as  hour_isbusy
			    from  smc_classroom as c
			 where  c.school_id ='{$request['school_id']}' and {$datawhere} ;
			 ");

        if ($classroomList) {
            foreach ($classroomList as $key => $value) {
                if ($value['hour_isbusy']) {
                    $classroomList[$key]['classroom_status'] = 1;
                } else {
                    $classroomList[$key]['classroom_status'] = 0;
                }
            }
        } else {
            $classroomList = array();
        }

        return $classroomList;
    }

    /**
     * @param $request
     * @return bool
     * 调整上课教师
     */
    function adjustClassTeacher($request)
    {
        $hourOne = $this->DataControl->getOne('smc_class_hour', "hour_id={$request['hour_id']} ");
        if ($hourOne['hour_ischecking'] == -1) {
            $this->error = 1;
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "该课时已上课";
            return false;
        }
        $dataOne = $this->DataControl->selectOne("
			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type =0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}'");


        if (!$dataOne) {
            $this->error = 1;
            $this->errortip = "查无数据";
            $this->conflict = 0;
            return false;
        }

        $hour_day = date("Y-m-d", strtotime($dataOne['hour_day']));
        $dataOne['staffer_id'] = $request['re_staffer_id'];

        if (isset($request['conflict']) && $request['conflict'] == 1) {

            $hourDayList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_day='{$hour_day}' and h.hour_id <>'{$request['hour_id']}' and c.school_id ='{$request['school_id']}' and h.hour_ischecking <> -1");
            array_unshift($hourDayList, $dataOne);
            $dataList = array_values($hourDayList);
            if (count($dataList) > 1) {
                $TimetableModel = new \Model\Smc\TimetableModel($this->publicarray);
                $dataList = $TimetableModel->checkClassroomConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "教室冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
                $dataList = $TimetableModel->checkStafferConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "教师冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
            }
        }

        if (!isset($request['teaching_type']) || $request['teaching_type'] === '') {
            $this->error = 1;
            $this->errortip = "请传入教学类型：主教/助教";
            return false;
        }

        $updatedata = array();
        $updatedata['staffer_id'] = $request['re_staffer_id'];
        $updatedata['teaching_updatatime'] = time();

        $teachingOne = $this->DataControl->selectOne("select teaching_id from smc_class_hour_teaching WHERE hour_id='{$request['hour_id']}' and teaching_type='{$request['teaching_type']}' limit 0,1 ");
        if ($teachingOne) {
            if ($request['teaching_type'] == '0' && $teachingOne) {
                $teachingID = $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='{$request['teaching_type']}' ", $updatedata);
            } elseif ($request['teaching_type'] == '1' && $teachingOne) {
                $teachingID = $this->DataControl->updateData("smc_class_hour_teaching", "hour_id='{$request['hour_id']}' and teaching_type='{$request['teaching_type']}' ", $updatedata);
            }
        } else {
            if ($request['teaching_type'] == '0' || $request['teaching_type'] == '1') {
                //课时的基本信息
                $classhourOne = $this->DataControl->selectOne("select h.class_id,h.hour_lessontimes  from smc_class_hour as h  
                            WHERE hour_id='{$request['hour_id']}' limit 0,1 ");

                $datatea = array();
                $datatea['class_id'] = $classhourOne['class_id'];
                $datatea['hour_lessontimes'] = $classhourOne['hour_lessontimes'];

                $datatea['hour_id'] = $request['hour_id'];//ok
                $datatea['staffer_id'] = $request['re_staffer_id'];//ok
                $datatea['hour_former_staffer_id'] = 0;//ok  没有
                $datatea['teaching_ischecking'] = 0;//ok  未
                $datatea['teaching_type'] = $request['teaching_type'];//ok 类型
                $datatea['teachtype_code'] = '';
                $datatea['teaching_isdel'] = 0;//ok
                $datatea['teaching_createtime'] = time();//ok
                $teachingID = $this->DataControl->insertData("smc_class_hour_teaching", $datatea);
            }
        }

        if ($teachingID) {
            $this->error = 0;
            $this->errortip = "调整成功";
            $this->conflict = 0;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "调整失败";
            $this->conflict = 0;
            return false;
        }

    }

    /**
     * @param $request
     * @return bool
     * 调整上课教室
     */
    function adjustClassRoomApi($request)
    {
        $hourOne = $this->DataControl->getOne('smc_class_hour', "hour_id='{$request['hour_id']}' ");
        if ($hourOne['hour_ischecking'] == -1) {
            $this->error = 1;
            $this->errortip = "该课时已经取消";
            return false;
        } elseif ($hourOne['hour_ischecking'] == 1) {
            $this->error = 1;
            $this->errortip = "该课时已上课";
            return false;
        }
        $dataOne = $this->DataControl->selectOne("
			select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl  ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type= 0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_id='{$request['hour_id']}' ");


        if (!$dataOne) {
            $this->error = 1;
            $this->errortip = "查无数据";
            $this->conflict = 0;
            return false;
        }

        $hour_day = date("Y-m-d", strtotime($dataOne['hour_day']));
        $dataOne['classroom_id'] = $request['classroom_id'];

        if (isset($request['conflict']) && $request['conflict'] == 1) {
            $hourDayList = $this->DataControl->selectClear("select c.class_id,c.class_cnname,h.hour_id, s.staffer_cnname,h.hour_day,h.hour_ischecking,h.hour_starttime,h.hour_endtime,cl.classroom_id,cl.classroom_cnname,s.staffer_id
 				from smc_class_hour as h
				left JOIN  smc_class as c ON  c.class_id =h.class_id
				left JOIN  smc_classroom as cl ON cl.classroom_id = h.classroom_id
				left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0
				left JOIN  smc_staffer as s ON s.staffer_id = t.staffer_id
				where h.hour_day='{$hour_day}' and h.hour_id <>'{$request['hour_id']}' and c.school_id ='{$request['school_id']}'");

            $hourDayList['one'] = $dataOne;

            $dataList = array_values($hourDayList);
            if (count($dataList) > 1) {
                $TimetableModel = new \Model\Smc\TimetableModel($this->publicarray);
                $dataList = $TimetableModel->checkClassroomConflict($dataList);

                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['classroom_confict'] == -2) {
                            $this->error = 1;
                            $this->errortip = "教室冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }

                $dataList = $TimetableModel->checkStafferConflict($dataList);
                if ($dataList) {
                    foreach ($dataList as $key => $value) {
                        if ($value['hour_id'] == $request['hour_id'] && $value['hour_ischecking'] == -2) {
                            $this->error = 1;
                            $this->errortip = "教师冲突";
                            $this->conflict = -2;
                            return false;
                        }
                    }
                }
            }
        }
        $data = array();
        $data['classroom_id'] = $request['classroom_id'];
        $data['hour_updatatime'] = time();

        if ($this->DataControl->updateData('smc_class_hour', "hour_id={$request['hour_id']}", $data)) {
            $this->error = 0;
            $this->errortip = "调整成功";
            $this->conflict = 1;
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "调整失败";
            $this->conflict = 1;
            return false;
        }

    }


    function getStudentReason($request)
    {
//		$dataList = $this->DataControl->getFieldquery('smc_code_stuchange_reason', 'reason_id,reason_note', "company_id={$request['company_id']}");

        $dataList = array(
            $this->LgArraySwitch(array('reason_id' => '1', 'reason_note' => '事假')),
            $this->LgArraySwitch(array('reason_id' => '2', 'reason_note' => '病假')),
            $this->LgArraySwitch(array('reason_id' => '3', 'reason_note' => '线上补课')),
            $this->LgArraySwitch(array('reason_id' => '4', 'reason_note' => '线下补课')),
            $this->LgArraySwitch(array('reason_id' => '5', 'reason_note' => '其它'))
        );
        if ($dataList) {
            return $dataList;
        } else {
            return array();
        }

    }

    //设置试听状态
    function setIsaudition($paramArray)
    {
        // 是否到访   // id   //到访原因   -1-未到访 1-到访
        if ($paramArray['audition_isvisit'] == 1) {
            $data['audition_isvisit'] = $paramArray['audition_isvisit'];
        } elseif ($paramArray['audition_isvisit'] == -1) {
            $data['audition_isvisit'] = $paramArray['audition_isvisit'];
            $data['audition_novisitreason'] = $paramArray['audition_novisitreason'];
        } else {
            $data = array();
        }
        $data['audition_updatetime'] = time();

        $audHouritionOne = $this->DataControl->selectOne("select *  from smc_class_hour_audition  where  audition_id='{$paramArray['audition_id']}'");
        $audHourOne = $this->DataControl->selectOne("select *  from crm_client_audition  where  class_id='{$audHouritionOne['class_id']}' and  hour_id='{$audHouritionOne['hour_id']}' and client_id='{$audHouritionOne['client_id']}'");

        if ($audHourOne) {
            $this->DataControl->updateData("crm_client_audition", "audition_id={$audHourOne['audition_id']}", $data);
        }

        if ($this->DataControl->updateData('smc_class_hour_audition', "audition_id={$paramArray['audition_id']}", $data)) {
            $dataTrack = array();
            $dataTrack['school_id'] = $paramArray['school_id'];
            $dataTrack['client_id'] = $audHourOne['client_id'];
            $dataTrack['track_validinc'] = 1;
            $dataTrack['track_createtime'] = time();
            if ($paramArray['audition_isvisit'] == 1) {
                $dataTrack['track_note'] = $this->LgStringSwitch("在校务系统确认试听,操作人为{$this->stafferOne['staffer_cnname']}");
            } else {
                $dataTrack['track_note'] = $this->LgStringSwitch("在校务系统取消试听,操作人为{$this->stafferOne['staffer_cnname']}");
            }
            $this->DataControl->insertData("crm_client_track", $dataTrack);

            return true;
        } else {
            return false;
        }
    }

    //获取试听班级
    function getAuditionHour($request, $port = '')
    {

        if (isset($request['hour_day']) && $request['hour_day'] != "") {
            $hour_day = date("Y-m-d", strtotime($request['hour_day']));
            $hourwhere = " h.hour_day = '{$hour_day}' ";
            $limit = " limit 0,100";
        } else {
            $hour_day = date("Y-m-d");
            $hourwhere = " h.hour_day >'{$hour_day}' ";
            $limit = " limit 0,30";
        }
        $dataCourseWhere = '';
        $datawhere = "1";

//        if($port == 'gmc'){
//            $nowtime = date("Y-m-d H:i");
//            $datawhere .= " AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}' ";
//        }
        //戚总说都需要精确到时间点了。。过了上课时间点就不能约了，因为现在准备发短信，之前有的没限制，现在统一到时间点，插班/普通公开课/试读公开课都需要
        $nowtime = date("Y-m-d H:i");
        $datawhere .= " AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}' ";

        if (isset($request['school_id']) && $request['school_id'] != "") {
            $datawhere .= " and cs.school_id = '{$request['school_id']}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "" && $request['coursetype_id'] != "0") {
            $datawhere .= " and scc.coursetype_id = '{$request['coursetype_id']}' ";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "" && $request['coursecat_id'] != "0") {
            $datawhere .= " and sc.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['course_cnname']) && $request['course_cnname'] != "") {
            $datawhere .= " and  sc.course_cnname like'%{$request['course_cnname']}%' ";
        }

        if (isset($request['re_staffer_id']) && $request['re_staffer_id'] != "") {
            $datawhere .= "  and sf.staffer_id = '{$request['re_staffer_id']}' ";
        }


        $dataCourseWhere = $datawhere; //请注意这里是为了下边 课程的筛选特意排除 course
        if (isset($request['course_id']) && $request['course_id'] != "") {
            $datawhere .= "  and h.course_id = '{$request['course_id']}' ";
        }

        $datahour = $this->DataControl->selectClear("
		  select h.hour_id,h.class_id,sc.course_id,cs.class_cnname,cs.class_enname,cs.class_branch,sf.staffer_id,sc.course_classnum,sf.staffer_cnname, cs.class_fullnums,h.hour_starttime,hour_endtime,h.hour_day,sc.course_cnname,sc.course_branch,h.hour_number,cs.class_appointnum,sc.coursetype_id,sc.coursecat_id,scc.coursetype_cnname,cct.coursecat_cnname,
		  (select count(sch.hour_id) from smc_class_hour as sch where sch.course_id=h.course_id and sch.class_id=h.class_id and sch.hour_ischecking=1 limit 0,1) as class_existnum,
		  (select count(sss.study_id) from smc_student_study as sss where sss.class_id=h.class_id  and sss.study_isreading =1 limit 0,1) as study_num,
		  (select cm.classroom_cnname  from smc_classroom as cm where  cm.classroom_id = h.classroom_id limit 0,1) as  classroom_cnname
		  from smc_class_hour  as h
		  left join smc_class_hour_teaching  as ht ON ht.hour_id = h.hour_id  and ht.teaching_type = 0
		  left join smc_course as sc on sc.course_id=h.course_id
		  left join smc_code_coursetype as scc ON scc.coursetype_id = sc.coursetype_id
		  left Join smc_staffer as sf ON  sf.staffer_id = ht.staffer_id
		  left Join smc_class as cs ON   cs.class_id=h.class_id
		  left Join smc_code_coursecat as cct  ON cct.coursecat_id = sc.coursecat_id
		  where {$hourwhere} and  {$datawhere} and scc.coursetype_isopenclass =0  and cs.class_status <> -2 and cs.class_status <> -1 and  h.hour_ischecking =0 AND cs.class_type=0 
		  and (cs.class_appointnum >(select count(ha.audition_id) from crm_client_audition as ha WHERE (audition_isvisit = '0' or audition_isvisit = '1') and ha.hour_id = h .hour_id and ha.class_id =h.class_id  ) or cs.class_appointnum =0  )
		  {$limit}");//and sc.course_inclasstype = 0  20211114戚总确认让拿掉

        $data = array();
        if ($datahour) {
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            foreach ($datahour as $key => $value) {
                $fu_staffer_cnname = $this->DataControl->selectOne("select group_concat(f.staffer_cnname) as staffer_cnname from  smc_class_hour_teaching as t 
                        left join smc_staffer as f ON f.staffer_id =t.staffer_id
                        where t.hour_id ='{$value['hour_id']}' and t.teaching_type=1");
                $data[$key]['hour_id'] = $value['hour_id'];
                $data[$key]['class_cnname'] = $value['class_cnname'] . '(' . $value['class_enname'] . ')';
                $data[$key]['class_branch'] = $value['class_branch'];
                $data[$key]['coursetype_id'] = $value['coursetype_id'];
                $data[$key]['coursecat_id'] = $value['coursecat_id'];
                $data[$key]['coursetype_cnname'] = $value['coursetype_cnname'];
                $data[$key]['coursecat_cnname'] = $value['coursecat_cnname'];
                if ($value['classroom_cnname']) {
                    $data[$key]['classroom_cnname'] = $value['classroom_cnname'];
                } else {
                    $data[$key]['classroom_cnname'] = $value['hour_number'];
                }
                if ($fu_staffer_cnname['staffer_cnname'] != '') {
                    $data[$key]['staffer_cnname'] = $value['staffer_cnname'] . "," . $fu_staffer_cnname['staffer_cnname'];
                } else {
                    $data[$key]['staffer_cnname'] = $value['staffer_cnname'];
                }
                $data[$key]['class_fullnums'] = $value['class_fullnums'];
                $data[$key]['study_num'] = $value['study_num'];
                $data[$key]['course_cnname'] = $value['course_cnname'];
                $data[$key]['course_branch'] = $value['course_branch'];
                $data[$key]['class_id'] = $value['class_id'];
                $data[$key]['course_id'] = $value['course_id'];
                $data[$key]['hour_day'] = $value['hour_day'];
                $data[$key]['hour_day'] = $value['hour_day'];
                $data[$key]['hour_starttime'] = $value['hour_starttime'];

                if ($value['class_appointnum'] == 0) {
                    $data[$key]['class_appointnum'] = '不限';
                } else {
                    $smchouraudition = $this->DataControl->selectOne("select count(audition_id) as anum from smc_class_hour_audition WHERE (audition_isvisit = '0' or audition_isvisit = '1') and hour_id = '{$value['hour_id']}' and class_id = '{$value['class_id']}' limit 0,1 ");
                    if ($smchouraudition['anum'] > 0) {
                        $data[$key]['class_appointnum'] = intval($value['class_appointnum'] - $smchouraudition['anum']);
                    } else {
                        $data[$key]['class_appointnum'] = $value['class_appointnum'];
                    }
                }
//                $data[$key]['class_appointnum'] = $value['class_appointnum'] == 0 ? '不限' : $value['class_appointnum'];
                $data[$key]['week'] = $weekarray[date('w', strtotime($value['hour_day']))];
                $data[$key]['audition_genre'] = 1;
                if (!$data[$key]['class_fullnums']) {
                    $data[$key]['class_fullnums'] = 0;
                    $data[$key]['percentage'] = 0;
                } else {
                    $data[$key]['class_fullnums'] = $value['class_fullnums'];
                    $data[$key]['percentage'] = $data[$key]['study_num'] / $value['class_fullnums'] * 100;
                }
//				$data[$key]['class_num']['study_num'] = intval($data[$key]['study_num']);
//				$data[$key]['class_num']['class_fullnums'] = intval($data[$key]['class_fullnums']);
//				$data[$key]['class_num']['percentage'] = intval($data[$key]['percentage']);
                $data[$key]['class_num'] = intval($data[$key]['study_num']);
                $data[$key]['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $data[$key]['hour_num'] = $value['class_existnum'] . '/' . $value['course_classnum'];

            }
        }

        $courseList = $this->DataControl->selectClear(" select DISTINCT sc.course_id,sc.course_cnname,sc.course_branch
        from smc_class_hour  as h
		  left join smc_class_hour_teaching  as ht ON ht.hour_id = h.hour_id  and ht.teaching_type = 0
		  left Join smc_staffer as sf ON  sf.staffer_id = ht.staffer_id
		  left join smc_course as sc on sc.course_id=h.course_id
		  left join smc_code_coursetype as scc ON scc.coursetype_id = sc.coursetype_id 
		  left Join smc_class as cs ON   cs.class_id=h.class_id
		  where {$hourwhere} and  {$dataCourseWhere} and scc.coursetype_isopenclass =0 and sc.course_inclasstype = 0 and cs.class_status <> -2 and cs.class_status <> -1 and  h.hour_ischecking =0 AND cs.class_type=0 
		  and (cs.class_appointnum >(select count(ha.audition_id) from crm_client_audition as ha WHERE (audition_isvisit = '0' or audition_isvisit = '1') and ha.hour_id = h .hour_id and ha.class_id =h.class_id  ) or cs.class_appointnum =0  )");//
        if (!$courseList) {
            $courseList = array();
        }

        $result = array();
        $result['data'] = $data;
        $result['courselist'] = $courseList;
        return $result;

    }

    //获取公开课
    function getPucList($request, $port)
    {
        $datawhere = "cs.class_id = h.class_id AND ht.hour_id = h.hour_id AND ht.teaching_type = 0 AND sf.staffer_id = ht.staffer_id
AND sc.course_id = h.course_id AND scc.coursetype_id = sc.coursetype_id AND co.coursecat_id = sc.coursecat_id AND scc.coursetype_isopenclass = 1 AND h.hour_ischecking = 0
AND cs.class_status <> '-2' AND cs.class_status <> '-1'  AND cs.class_type = 0 ";
        if (isset($request['hour_day']) && $request['hour_day'] != "") {
            $hour_day = date("Y-m-d", strtotime($request['hour_day']));
        } else {
            $hour_day = date("Y-m-d");
        }
        if (isset($request['audition_genre']) && $request['audition_genre'] != "2") {
            $datawhere .= " AND h.hour_day >='{$hour_day}' ";
        }elseif($port == 'crm' || $port == 'gmc'){
            $datawhere .= " AND h.hour_day >='{$hour_day}' ";
        }
        $hour_endday = date("Y-m-d", strtotime($hour_day) + 3600 * 24 * 30);
        $datawhere .= " AND h.hour_day <='{$hour_endday}'";

//        if(isset($request['audition_genre']) && $request['audition_genre'] == "2"){
////            if ($port == 'gmc') {
////                $nowtime = date("Y-m-d H:i");
////                $datawhere .= " AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}' ";
////            }
//        }else {
//            if ($port == 'gmc') {
//                $nowtime = date("Y-m-d H:i");
//                $datawhere .= " AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}' ";
//            }
//        }

        //戚总说都需要精确到时间点了。。过了上课时间点就不能约了，因为现在准备发短信，之前有的没限制，现在统一到时间点，插班/普通公开课/试读公开课都需要
        $nowtime = date("Y-m-d H:i");
        if (isset($request['audition_genre']) && $request['audition_genre'] != "2") {
            $datawhere .= " AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}' ";
        }

        if (isset($request['school_id']) && $request['school_id'] != "") {
            $datawhere .= " and cs.school_id = '{$request['school_id']}' ";
        }

        $groupby = ' ';
        $groupby2 = ' ';
        if (isset($request['audition_genre']) && $request['audition_genre'] == "0") {
            $datawhere .= " and sc.course_openclasstype = '0' 
AND (cs.class_appointnum = 0 OR cs.class_appointnum > ( SELECT count(h.hour_id) FROM crm_client_audition au WHERE au.hour_id = h.hour_id AND au.audition_isvisit <> '-1' ))";
        } elseif (isset($request['audition_genre']) && $request['audition_genre'] == "2") {
            $datawhere .= " and sc.course_openclasstype = '1' ";
            $groupby .= " group by cs.class_id";
            $groupby2 .= " group by t.class_id";
        }
        if (isset($request['course_cnname']) && $request['course_cnname'] != "") {
            $datawhere .= " and sc.course_cnname like'%{$request['course_cnname']}%' ";
        }
//        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
//            $datawhere .= " and scc.coursetype_id = '{$request['coursetype_id']}' ";
//        }
        if (isset($request['re_staffer_id']) && $request['re_staffer_id'] != "") {
            $datawhere .= "  and sf.staffer_id = '{$request['re_staffer_id']}' ";
        }
        if (isset($request['starttime']) && $request['starttime'] != "") {
            $datawhere .= "  and h.hour_day >= '{$request['starttime']}' ";
        }
        if (isset($request['endtime']) && $request['endtime'] != "") {
            $datawhere .= "  and h.hour_day >= '{$request['endtime']}' ";
        }

        $dataCourseWhere = $datawhere; //请注意这里是为了下边 课程的筛选特意排除 course
        if (isset($request['course_id']) && $request['course_id'] != "") {
            $datawhere .= "  and h.course_id = '{$request['course_id']}' ";
        }
        if (isset($request['audition_genre']) && $request['audition_genre'] != "2") {
            $datahour = $this->DataControl->selectClear("SELECT h.hour_id, h.class_id, h.hour_starttime, h.hour_endtime, h.hour_day, h.hour_number
, sf.staffer_id, sf.staffer_cnname
, sc.course_id, sc.course_cnname, sc.course_classnum, sc.course_branch,sc.coursetype_id,sc.course_openclasstype
, cs.class_cnname, cs.class_enname , cs.class_branch, cs.class_fullnums , cs.class_appointnum
, ( SELECT count(ch.hour_id) FROM smc_class_hour ch WHERE ch.course_id = h.course_id AND ch.class_id = h.class_id AND ch.hour_ischecking = 1) AS class_existnum
, ( SELECT count(d.study_id) FROM smc_student_study d WHERE d.class_id = h.class_id AND d.study_isreading = 1) AS study_num
, ( SELECT cm.classroom_cnname FROM smc_classroom cm WHERE cm.classroom_id = h.classroom_id LIMIT 0, 1 ) AS classroom_cnname
FROM smc_class_hour h, smc_class cs, smc_class_hour_teaching ht, smc_staffer sf, smc_course sc, smc_code_coursetype scc, smc_code_coursecat co
WHERE {$datawhere} {$groupby} ORDER BY h.hour_day ASC");
        } else {

            if($port == 'crm' || $port == 'gmc'){
                $datahour = $this->DataControl->selectClear("select * from (
SELECT h.hour_id, h.class_id, h.hour_starttime, h.hour_endtime, h.hour_day, h.hour_number
, sf.staffer_id, sf.staffer_cnname,ifnull(( SELECT count(au.hour_id) FROM crm_client_audition au WHERE au.hour_id = h.hour_id AND au.audition_isvisit <> '-1' limit 0,1),0) as usetime
, sc.course_id, sc.course_cnname, sc.course_classnum, sc.course_branch,sc.coursetype_id,sc.course_openclasstype
, cs.class_cnname, cs.class_enname , cs.class_branch, cs.class_fullnums , cs.class_appointnum
, ( SELECT count(ch.hour_id) FROM smc_class_hour ch WHERE ch.course_id = h.course_id AND ch.class_id = h.class_id AND ch.hour_ischecking = 1) AS class_existnum
, ( SELECT count(d.study_id) FROM smc_student_study d WHERE d.class_id = h.class_id AND d.study_isreading = 1) AS study_num
, ( SELECT cm.classroom_cnname FROM smc_classroom cm WHERE cm.classroom_id = h.classroom_id LIMIT 0, 1 ) AS classroom_cnname
FROM smc_class_hour h, smc_class cs, smc_class_hour_teaching ht, smc_staffer sf, smc_course sc, smc_code_coursetype scc, smc_code_coursecat co
WHERE {$datawhere}
AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}'
HAVING  (cs.class_appointnum = 0 or cs.class_appointnum > usetime)  
ORDER BY h.hour_day ASC 
) as t  {$groupby2}  ");
            }else {
                $nowtime1 = date("Y-m-d");
                $datahour = $this->DataControl->selectClear("select * from (SELECT h.hour_id, h.class_id, h.hour_starttime, h.hour_endtime, h.hour_day, h.hour_number
, sf.staffer_id, sf.staffer_cnname,ifnull(( SELECT count(au.hour_id) FROM crm_client_audition au WHERE au.hour_id = h.hour_id AND au.audition_isvisit <> '-1' limit 0,1),0) as usetime
, sc.course_id, sc.course_cnname, sc.course_classnum, sc.course_branch,sc.coursetype_id,sc.course_openclasstype
, cs.class_cnname, cs.class_enname , cs.class_branch, cs.class_fullnums , cs.class_appointnum
, ( SELECT count(ch.hour_id) FROM smc_class_hour ch WHERE ch.course_id = h.course_id AND ch.class_id = h.class_id AND ch.hour_ischecking = 1) AS class_existnum
, ( SELECT count(d.study_id) FROM smc_student_study d WHERE d.class_id = h.class_id AND d.study_isreading = 1) AS study_num
, ( SELECT cm.classroom_cnname FROM smc_classroom cm WHERE cm.classroom_id = h.classroom_id LIMIT 0, 1 ) AS classroom_cnname
FROM smc_class_hour h, smc_class cs, smc_class_hour_teaching ht, smc_staffer sf, smc_course sc, smc_code_coursetype scc, smc_code_coursecat co
WHERE {$datawhere} {$groupby} 
-- AND CONCAT(h.hour_day,' ',h.hour_endtime) > '{$nowtime}'
ORDER BY h.hour_day ASC) as t where t.hour_day >= '{$nowtime1}' AND CONCAT(t.hour_day,' ',t.hour_endtime) > '{$nowtime}' and (t.class_appointnum = 0 or t.class_appointnum > usetime) ");
            }
        }
        $data = array();
        if ($datahour) {
            $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));
            foreach ($datahour as $key => $value) {
                $fu_staffer_cnname = $this->DataControl->selectOne("select group_concat(f.staffer_cnname) as staffer_cnname from  smc_class_hour_teaching as t 
                        left join smc_staffer as f ON f.staffer_id =t.staffer_id
                        where t.hour_id ='{$value['hour_id']}' and t.teaching_type=1");

                $data[$key]['hour_id'] = $value['hour_id'];
                $data[$key]['class_cnname'] = $value['class_cnname'] . '(' . $value['class_enname'] . ')';
                $data[$key]['class_branch'] = $value['class_branch'];
                $data[$key]['coursetype_id'] = $value['coursetype_id'];

                if ($value['classroom_cnname']) {

                    $data[$key]['classroom_cnname'] = $value['classroom_cnname'];
                } else {
                    $data[$key]['classroom_cnname'] = $value['hour_number'];
                }
                if ($fu_staffer_cnname['staffer_cnname'] != '') {
                    $data[$key]['staffer_cnname'] = $value['staffer_cnname'] . "," . $fu_staffer_cnname['staffer_cnname'];
                } else {
                    $data[$key]['staffer_cnname'] = $value['staffer_cnname'];
                }

                $data[$key]['class_fullnums'] = $value['class_fullnums'];
                $data[$key]['study_num'] = $value['study_num'];
                $data[$key]['course_cnname'] = $value['course_cnname'];
                $data[$key]['course_branch'] = $value['course_branch'];
                $data[$key]['class_id'] = $value['class_id'];
                $data[$key]['course_id'] = $value['course_id'];
                $data[$key]['hour_day'] = $value['hour_day'];
                $data[$key]['hour_starttime'] = $value['hour_starttime'];

                if ($value['class_appointnum'] == 0) {
                    $data[$key]['class_appointnum'] = '不限';
                } else {
                    $smchouraudition = $this->DataControl->selectOne("select count(audition_id) as anum from smc_class_hour_audition WHERE (audition_isvisit = '0' or audition_isvisit = '1') and hour_id = '{$value['hour_id']}' and class_id = '{$value['class_id']}' limit 0,1 ");
                    if ($smchouraudition['anum'] > 0) {
                        $data[$key]['class_appointnum'] = $value['class_appointnum'] - $smchouraudition['anum'];
                    } else {
                        $data[$key]['class_appointnum'] = $value['class_appointnum'];
                    }
                }
//                $data[$key]['class_appointnum'] = $value['class_appointnum'] ==0?'不限':$value['class_appointnum'] ;
                $data[$key]['week'] = $weekarray[date('w', strtotime($value['hour_day']))];
                $data[$key]['audition_genre'] = 0;
                if (!$data[$key]['class_fullnums']) {
                    $data[$key]['class_fullnums'] = 0;
                    $data[$key]['percentage'] = 0;
                } else {
                    $data[$key]['class_fullnums'] = $value['class_fullnums'];
                    $data[$key]['percentage'] = $data[$key]['study_num'] / $value['class_fullnums'] * 100;
                }
//				$data[$key]['class_num']['study_num'] = intval($data[$key]['study_num']);
//				$data[$key]['class_num']['class_fullnums'] = intval($data[$key]['class_fullnums']);
//				$data[$key]['class_num']['percentage'] = intval($data[$key]['percentage']);
                $data[$key]['class_num'] = intval($data[$key]['study_num']);
                $data[$key]['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $data[$key]['hour_num'] = $value['class_existnum'] . '/' . $value['course_classnum'];

            }
        }

        $courseList = $this->DataControl->selectClear(" select DISTINCT sc.course_id,sc.course_cnname,sc.course_branch
          FROM smc_class_hour h, smc_class cs, smc_class_hour_teaching ht, smc_staffer sf, smc_course sc, smc_code_coursetype scc, smc_code_coursecat co
          WHERE {$dataCourseWhere}");
        if (!$courseList) {
            $courseList = array();
        }

        if ($port == 'crm' || $port == 'gmc') {
            $result = array();
            $result['data'] = $data;
            $result['courselist'] = $courseList;

            return $result;
        } else {
            return $data;
        }

    }

    //获取试听教师
    function getAuditionTeacher($request)
    {
        $date = date('Y-m-d');
        $teaList = $this->DataControl->selectClear("SELECT ht . staffer_id, sf . staffer_cnname,sf . staffer_enname
FROM smc_class_hour_teaching AS ht, smc_staffer AS sf, smc_class_hour AS ch, smc_class AS cl, smc_course AS co
WHERE ht . staffer_id = sf . staffer_id AND ch . hour_id = ht . hour_id AND cl . class_id = ht . class_id AND co . course_id = cl . course_id
    AND ch . hour_day > '{$date}' and cl . school_id = '{$request['school_id']}' and ht . teaching_isdel = '0' and course_inclasstype = '0' and ht . teaching_type = '0'
group by ht . staffer_id");
        if (!$teaList) {
            $teaList = array();
        } else {
            foreach ($teaList as &$value) {
                $value['staffer_cnname'] = $value['staffer_cnname'] . '(' . $value['staffer_enname'] . ')';
            }
        }
        return $teaList;
    }

    /**
     * @param $company_id
     * @return array
     *获取课程
     */
    function getIntentCourse($company_id)
    {
        $courseList = $this->DataControl->getFieldquery('smc_code_coursecat', 'coursecat_id,coursecat_cnname,coursecat_branch', "company_id = '{$company_id}' and coursecat_iscrmadded = 1");

        if ($courseList) {
            $list = $courseList;
        } else {
            $list = array();
        }

        return $list;
    }


    /**
     * 新增暖身课
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function addWarmHourOne($request)
    {
        if (!$request['class_id']) {
            $this->error = '1';
            $this->errortip = '请选择班级';
            return false;
        }

        if (!$request['main_staffer_id']) {
            $this->error = '1';
            $this->errortip = '请选择主教教师';
            return false;
        }
        if (!$request['main_teachtype_code']) {
            $this->error = '1';
            $this->errortip = '请选择主教教师类型';
            return false;
        }

        if (!$request['hour_day']) {
            $this->error = '1';
            $this->errortip = '请选择上课日期';
            return false;
        }

        if (!$request['hour_starttime'] || !$request['hour_endtime']) {
            $this->error = '1';
            $this->errortip = '请选择上课的开始时间和结束时间';
            return false;
        }

        $request['hour_iswarming']=isset($request['hour_iswarming'])?$request['hour_iswarming']:1;

        $request_hour_day = date("Y-m-d", strtotime($request['hour_day']));

        $warmOne = $this->DataControl->selectOne("select hour_id,hour_lessontimes from smc_class_hour where class_id = '{$request['class_id']}' and hour_iswarming = '{$request['hour_iswarming']}' order by hour_lessontimes DESC limit 0,1 ");

//        $warmHourOne = $this->DataControl->selectOne("select hour_id,hour_lessontimes,hour_day from smc_class_hour where class_id = '{$request['class_id']}' and hour_iswarming = 1 and hour_day = '{$request_hour_day}' and hour_ischecking <> '-1'  limit 0,1  ");
//
//        if ($warmHourOne['hour_day'] == $request_hour_day) {
//            $this->error = '1';
//            $this->errortip = $request_hour_day . '已设置过暖身课,请选择其他日期';
//            return false;
//        }


        $sql = "select a.course_id,b.course_isopenwarm,a.class_hourwarmnums,a.class_hourwarmapplynums,b.course_isopenreview,a.class_hourreviewnums,a.class_hourreviewapplynum,b.course_canapplywarm,b.course_canapplyreview
                ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=a.class_id and x.hour_iswarming=1 and hour_ischecking<>-1),0) as warmnum
                ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=a.class_id and x.hour_iswarming=2 and hour_ischecking<>-1),0) as reviewnum
                from smc_class as a 
                left join smc_course as b on b.course_id=a.course_id
                where a.class_id = '{$request['class_id']}'
                ";

        $classOne = $this->DataControl->selectOne($sql);

        $ClassModel = new \Model\Smc\ClassModel($request);

        if(!isset($request['is_skip']) || $request['is_skip']==0){
            if(isset($request['hour_iswarming']) && $request['hour_iswarming']==1){

                if($classOne['course_isopenwarm']==0){
                    $this->error = '1';
                    $this->errortip = '该课程无法创建暖身课';
                    return false;
                }

                $leftNum=$classOne['class_hourwarmnums']+$classOne['class_hourwarmapplynums']-$classOne['warmnum'];
                if($leftNum<1){

                    if($classOne['course_canapplywarm']==1){
                        $request['freeapply_type']=0;
                        $bool = $ClassModel->applyFreeHour($request);

                        if($bool){
                            $this->errortip = "申请成功";
                            return true;
                        }else{
                            $this->error = '1';
                            $this->errortip = $ClassModel->errortip;
                            return false;
                        }

                    }else{
                        $this->error = '1';
                        $this->errortip = '该班级无可创建暖身课次数,暂无法创建暖身课';
                        return false;
                    }
                }

            }
            else{

                if($classOne['course_isopenreview']==0){
                    $this->error = '1';
                    $this->errortip = '该课程无法创建复习课';
                    return false;
                }

                $leftNum=$classOne['class_hourreviewnums']+$classOne['class_hourreviewapplynum']-$classOne['reviewnum'];

                if($leftNum<1){

                    if($classOne['course_canapplyreview']==1){
                        $request['freeapply_type']=1;
                        $bool = $ClassModel->applyFreeHour($request);

                        if($bool){
                            $this->errortip = "申请成功";
                            return true;
                        }else{
                            $this->error = '1';
                            $this->errortip = $ClassModel->errortip;
                            return false;
                        }

                    }else{
                        $this->error = '1';
                        $this->errortip = '该班级无可创建复习课次数,暂无法创建复习课';
                        return false;
                    }
                }
            }
        }



        $data = array();
        if ($warmOne) {
            $data['hour_lessontimes'] = $warmOne['hour_lessontimes'] + 1;
        } else {
            $data['hour_lessontimes'] = 1;
        }
        $data['class_id'] = $request['class_id'];
        $data['course_id'] = $classOne['course_id'];
        $data['classroom_id'] = $request['classroom_id'];
        if($request['hour_iswarming']==2){
            $data['hour_name'] = 'Review  ' . $data['hour_lessontimes'];
        }else{
            $data['hour_name'] = 'Warm ' . $data['hour_lessontimes'];
        }
        $data['hour_isfree'] = '1';
        $data['hour_day'] = $request_hour_day;
        $data['hour_iswarming'] = isset($request['hour_iswarming']) ? $request['hour_iswarming'] : 1;
        $data['hour_starttime'] = $request['hour_starttime'];
        $data['hour_endtime'] = $request['hour_endtime'];
        $data['hour_createtime'] = time();
        $data['hour_updatatime'] = time();

        if ($id = $this->DataControl->insertData("smc_class_hour", $data)) {
            $teachingData = array();
            $teachingData['class_id'] = $request['class_id'];
            $teachingData['hour_id'] = $id;
            $teachingData['hour_lessontimes'] = $data['hour_lessontimes'];
            $teachingData['staffer_id'] = $request['main_staffer_id'];
            $teachingData['teaching_type'] = 0;
            $teachingData['teachtype_code'] = $request['main_teachtype_code'];
            $teachingData['teaching_createtime'] = time();
            if (!$this->DataControl->insertData("smc_class_hour_teaching", $teachingData)) {
                $this->error = '1';
                $this->errortip = '新增主教失败';

                return false;
            }
            $fu_teachingData = array();
            $fu_teachingData['class_id'] = $request['class_id'];
            $fu_teachingData['hour_id'] = $id;
            $fu_teachingData['hour_lessontimes'] = $data['hour_lessontimes'];
            $fu_teachingData['staffer_id'] = $request['fu_staffer_id'];
            $fu_teachingData['teaching_type'] = 1;
            $fu_teachingData['teachtype_code'] = $request['fu_teachtype_code'];
            $fu_teachingData['teaching_createtime'] = time();
            if ($request['fu_staffer_id'] != '' && $request['fu_staffer_id'] > 0 && !$this->DataControl->insertData("smc_class_hour_teaching", $fu_teachingData)) {
                $this->error = '1';
                $this->errortip = '新增助教失败';

                return false;
            }

            $classOne = $this->DataControl->getFieldOne("smc_class", "class_enddate", "class_id = '{$request['class_id']}'");
            if ($classOne['class_enddate'] < $request_hour_day) {
                $classData = array();
                $classData['class_enddate'] = $request_hour_day;
                $classData['class_updatatime'] = time();
                $this->DataControl->updateData("smc_class", "class_id = '{$request['class_id']}'", $classData);
                $studyData = array();
                $studyData['study_endday'] = $request_hour_day;
                $studyData['study_updatetime'] = time();
                $this->DataControl->updateData("smc_student_study", "study_isreading = 1 and class_id = '{$request['class_id']}'", $studyData);
            }

            $sql = "call RefreshClass('{$request['class_id']}')";
            $this->DataControl->query($sql);

            $this->error = '0';
            $this->errortip = '新增暖身课成功';

            return true;

        } else {
            $this->error = '1';
            $this->errortip = '新增暖身课失败';

            return false;
        }
    }

    //调课时取消请假记录
    function cancelAbsenceHour($hour_id, $note)
    {
        $absenceData = $this->DataControl->selectClear("select h . absence_hour_id,sa . student_id,h . absence_id
              from smc_student_absence_hour as h,smc_student_absence as sa 
              where h . absence_id = sa . absence_id and h . hour_id = '{$hour_id}' and h . absence_hour_status <> '-1' ");
        if ($absenceData) {
            foreach ($absenceData as $absenceOne) {
                $trackData = array();
                $trackData['absence_id'] = $absenceOne['absence_id'];
                $trackData['track_title'] = '取消请假';
                $trackData['track_applynote'] = $note;
                $trackData['track_status'] = '-2';
                $trackData['track_applytime'] = time();
                $this->DataControl->insertData("smc_student_absence_track", $trackData);
            }
        }
        $this->DataControl->query("update smc_student_absence_hour set absence_hour_status = -1 where hour_id = '{$hour_id}' ");

    }


    //教师带课明细
    function courseStafferRecord($request)
    {
        $datawhere = " c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' ";
        $endqueryday = '';
        if (isset($request['weekstartday']) && $request['weekstartday'] !== '') {
            $datawhere .= " and ch.hour_day >= '{$request['weekstartday']}'";
            $endqueryday .= $request['weekstartday'];
        }


        if (isset($request['weekendday']) && $request['weekendday'] !== '') {
            $datawhere .= " and ch.hour_day <= '{$request['weekendday']}'";
            $endqueryday .= '-' . $request['weekstartday'];
        }

        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and ch.hour_way = '{$request['hour_way']}'";
        }

        if (isset($request['status']) && $request['status'] == '-1') {
            $datawhere .= " and s.staffer_leave = '1' and ch.hour_ischecking = '0'";
        }

        if (isset($request['post_id']) && $request['post_id'] !== '') {
            $datawhere .= " and (SELECT cp.post_id FROM gmc_staffer_postbe as sp LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id WHERE sp.staffer_id = ht.staffer_id ORDER BY sp.postbe_createtime DESC LIMIT 1) = '{$request['post_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%'
                            or sc.course_cnname like '%{$request['keyword']}%' or sc.course_branch like '%{$request['keyword']}%' or s.staffer_cnname like '%{$request['keyword']}%'
                            or s.staffer_enname like '%{$request['keyword']}%' or s.staffer_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ht.teaching_id,ht.hour_id,ht.teaching_type,s.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,sc.course_cnname,sc.course_branch,ch.hour_way,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,
				(SELECT cp.post_name FROM gmc_staffer_postbe as sp LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id WHERE sp.staffer_id = ht.staffer_id ORDER BY sp.postbe_createtime DESC LIMIT 1) as post_name,
				(select cr.classroom_cnname from smc_classroom as cr where cr.classroom_id=ch.classroom_id and cr.classroom_status=1) as classroom_cnname
                from smc_class_hour_teaching as ht
                left join smc_staffer as s on s.staffer_id=ht.staffer_id
                left join smc_class as c on c.class_id=ht.class_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_hour as ch on ch.hour_id=ht.hour_id
                where {$datawhere}
                ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['teaching_type_name'] = $this->LgStringSwitch($dateexcelvar['teaching_type'] == 0 ? "主教" : "助教");
                    if ($dateexcelvar['staffer_enname']) {
                        $datearray['staffer_cnname'] = "{$dateexcelvar['staffer_cnname']}/{$dateexcelvar['staffer_enname']} 【{$dateexcelvar['teaching_type_name']}】";
                    } else {
                        $datearray['staffer_cnname'] = "{$dateexcelvar['staffer_cnname']} 【{$dateexcelvar['teaching_type_name']}】";
                    }
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['post_name'] = $dateexcelvar['post_name'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['hour_way_name'] = $this->LgStringSwitch($dateexcelvar['hour_way'] == 0 ? "实体课" : "线上课");
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['hour_time'] = $dateexcelvar['hour_starttime'] . '-' . $dateexcelvar['hour_endtime'];
                    $datearray['classroom_cnname'] = $dateexcelvar['classroom_cnname'];
                    $datearray['hour_ischecking_name'] = $this->LgStringSwitch($dateexcelvar['hour_ischecking'] == 0 ? "待考勤" : "已考勤");
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("教师姓名", "教师编号", "职务", "班级名称", "班级别名", "班级编号", "课程别名称", '课程别编号', '上课方式', '上课日期', '上课时间', '上课教室', '是否考勤'));
            $excelfileds = array('staffer_cnname', 'staffer_branch', 'post_name', 'class_cnname', 'class_enname', 'class_branch', 'course_cnname', 'course_branch', 'hour_way_name', 'hour_day', 'hour_time', 'classroom_cnname', 'hour_ischecking_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}教师带课明细表{$endqueryday}.xlsx"));
            exit;

        } else {
            $sql .= " limit {$pagestart},{$num}";
        }

        $stafferList = $this->DataControl->selectClear($sql);

        if ($stafferList) {
            foreach ($stafferList as &$value) {
                $value['teaching_type_name'] = $this->LgStringSwitch($value['teaching_type'] == 0 ? "主教" : "助教");
                if ($value['staffer_enname']) {
                    $value['staffer_cnname'] = "{$value['staffer_cnname']}/{$value['staffer_enname']} 【{$value['teaching_type_name']}】";
                } else {
                    $value['staffer_cnname'] = "{$value['staffer_cnname']} 【{$value['teaching_type_name']}】";
                }
                $value['hour_way_name'] = $this->LgStringSwitch($value['hour_way'] == 0 ? "实体课" : "线上课");
                $value['hour_time'] = $value['hour_starttime'] . '-' . $value['hour_endtime'];
                $value['hour_ischecking_name'] = $this->LgStringSwitch($value['hour_ischecking'] == 0 ? "待考勤" : "已考勤");
                $value['fromTeacherSubstituteDetails'] = true;
            }
        } else {
            $stafferList = array();
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $all_num = $this->DataControl->selectOne("
			    select count(ht.staffer_id) as num
                from smc_class_hour_teaching as ht
                left join smc_staffer as s on s.staffer_id=ht.staffer_id
                left join smc_class as c on c.class_id=ht.class_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_class_hour as ch on ch.hour_id=ht.hour_id
                where {$datawhere}
			 ");

            $allnum = $all_num['num'];
        } else {
            $allnum = 0;
        }

        $post = $this->DataControl->selectClear("SELECT q.post_id,q.post_name FROM
                                                    (select  
                                                    (SELECT cp.post_id FROM gmc_staffer_postbe as sp LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id WHERE sp.staffer_id = ht.staffer_id ORDER BY sp.postbe_createtime DESC LIMIT 1) as post_id,
                                                    (SELECT cp.post_name FROM gmc_staffer_postbe as sp LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id WHERE sp.staffer_id = ht.staffer_id ORDER BY sp.postbe_createtime DESC LIMIT 1) as post_name
                                                    from smc_class_hour_teaching as ht
                                                    left join smc_staffer as s on s.staffer_id=ht.staffer_id
                                                    left join smc_class as c on c.class_id=ht.class_id
                                                    left join smc_course as sc on sc.course_id=c.course_id
                                                    left join smc_class_hour as ch on ch.hour_id=ht.hour_id
                                                    where {$datawhere}) as q WHERE q.post_id > 0 GROUP BY q.post_id");

        $data['num'] = $allnum;
        $data['list'] = $stafferList;
        $data['post'] = $post ? $post : array();

        return $data;
    }


    /**
     * @param $request
     * @return bool
     * 调整上课教师
     */
    function adjustClassStaffer($request)
    {
        $teaching_list = json_decode(stripslashes($request['teaching_list']), true);
        if ($teaching_list) {
            foreach ($teaching_list as $value) {
                $hourOne = $this->DataControl->selectOne("SELECT t.staffer_id,ch.hour_ischecking,ch.hour_day,ch.hour_starttime,ch.hour_endtime FROM smc_class_hour_teaching as t,smc_class_hour as ch WHERE ch.hour_id=t.hour_id AND t.teaching_id='{$value['teaching_id']}'");
                if ($hourOne) {
                    if ($hourOne['hour_ischecking'] == -1) {
                        $this->error = 1;
                        $this->errortip = "该课时已经取消";
                        return false;
                    } elseif ($hourOne['hour_ischecking'] == 1) {
                        $this->error = 1;
                        $this->errortip = "该课时已上课";
                        return false;
                    }
                } else {
                    $this->error = 1;
                    $this->errortip = "查无数据";
                    return false;
                }

                $hourOne['hour_starttime'] = $hourOne['hour_day'] . ' ' . $hourOne['hour_starttime'];
                $hourOne['hour_endtime'] = $hourOne['hour_day'] . ' ' . $hourOne['hour_endtime'];
                $sql = "SELECT ch.hour_id FROM smc_class_hour_teaching as t
                        LEFT JOIN smc_class_hour as ch ON ch.hour_id = t.hour_id 
                        WHERE t.staffer_id='{$request['re_staffer_id']}' AND ch.hour_day = '{$hourOne['hour_day']}' AND ch.hour_ischecking = '0'
                        AND ((ch.hour_starttime >= '{$hourOne['hour_starttime']}' and ch.hour_endtime <= '{$hourOne['hour_endtime']}')
                        OR (ch.hour_starttime <= '{$hourOne['hour_starttime']}' and ch.hour_endtime >= '{$hourOne['hour_endtime']}')
                        OR (ch.hour_starttime <= '{$hourOne['hour_starttime']}' and ch.hour_endtime >= '{$hourOne['hour_starttime']}')
                        OR (ch.hour_starttime >= '{$hourOne['hour_starttime']}' and ch.hour_starttime <= '{$hourOne['hour_endtime']}'))";
                if ($this->DataControl->selectClear($sql)) {
                    $this->error = 1;
                    $this->errortip = "教师冲突";
                    return false;
                }

                $data = array();
                $data['staffer_id'] = $request['re_staffer_id'];
                $data['hour_former_staffer_id'] = $hourOne['staffer_id'];
                $data['teaching_updatatime'] = time();
                $dataid = $this->DataControl->updateData("smc_class_hour_teaching", "teaching_id='{$value['teaching_id']}'", $data);
            }

            if ($dataid) {
                $this->error = 0;
                $this->errortip = "调整成功";
                $this->conflict = 0;
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "调整失败";
                $this->conflict = 0;
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = "请选择明细ID";
            return false;
        }


    }
}
