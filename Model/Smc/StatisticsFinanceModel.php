<?php


namespace Model\Smc;

class StatisticsFinanceModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id=$publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function headInfo($request){
        if($request['type']==0){
            //本日
            $oldStartDay=date("Y-m-d",strtotime("-1 day"));
            $oldEndDay=date("Y-m-d",strtotime("-1 day"));
        }elseif($request['type']==1){
            //本月
            $oldStartDay=date('Y-m-01',strtotime('-1 month'));
            $oldEndDay=date('Y-m-d',strtotime(date('Y-m-01').'-1 day'));


        }elseif($request['type']==2){
            //本季度
            $oldStartDay=date("Y-m-d",strtotime("-1 day"));
            $oldEndDay=date("Y-m-d",strtotime("-1 day"));


        }elseif($request['type']==3){
            //本年
            $oldStartDay=date("Y-01-01",strtotime("-1 year"));
            $oldEndDay=date('Y-m-d',strtotime(date('Y-1-1').'-1 day'));

        }elseif($request['type']==4){
            //按时间

        }else{
            $this->error = true;
            $this->errortip = "该模式不存在";
            return false;
        }

        $oldstarttime=strtotime($oldStartDay);
        $oldendtime=strtotime($oldEndDay." 23:59:59");

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }
        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        $sql="select SUM(income_price) as income_price
              from smc_school_income
              where school_id='{$this->school_id}' and income_confirmtime>='{$starttime}' and income_confirmtime<='{$endtime}'";
        $newIncomeOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(income_price) as income_price
              from smc_school_income
              where school_id='{$this->school_id}' and income_confirmtime>='{$oldstarttime}' and income_confirmtime<='{$oldendtime}'";
        $oldIncomeOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(expend_price) as expend_price
              from smc_school_expend
              where school_id='{$this->school_id}' and expend_confirmtime>='{$starttime}' and expend_confirmtime<='{$endtime}'";
        $newExpendOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(expend_price) as expend_price
              from smc_school_expend
              where school_id='{$this->school_id}' and expend_confirmtime>='{$oldstarttime}' and expend_confirmtime<='{$oldendtime}'";
        $oldExpendOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(pop.pay_price) as pay_price
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as po on po.order_pid=pop.order_pid
              where po.school_id='{$this->school_id}' and pop.pay_issuccess='1' and pop.paytype_code in (select cp.paytype_code from smc_code_paytype as cp where cp.paytype_ischarge='1') and pop.pay_successtime>='{$starttime}' and pop.pay_successtime<='{$endtime}'";
        $newPayOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(pop.pay_price) as pay_price
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as po on po.order_pid=pop.order_pid
              where po.school_id='{$this->school_id}' and pop.pay_issuccess='1' and pop.paytype_code in (select cp.paytype_code from smc_code_paytype as cp where cp.paytype_ischarge='1') and pop.pay_successtime>='{$oldstarttime}' and pop.pay_successtime<='{$oldendtime}'";
        $oldPayOne=$this->DataControl->selectOne($sql);

        $sql="select sum(refund_payprice) as refund_payprice
        from smc_refund_order
        where school_id='{$this->school_id}' and refund_status='4' and refund_updatatime>='{$starttime}' and refund_updatatime<='{$endtime}'";
        $newRefundOne=$this->DataControl->selectOne($sql);

        $sql="select sum(refund_payprice) as refund_payprice
        from smc_refund_order
        where school_id='{$this->school_id}' and refund_status='4' and refund_updatatime>='{$oldstarttime}' and refund_updatatime<='{$oldendtime}'";
        $oldRefundOne=$this->DataControl->selectOne($sql);

        $sql="select coursetimes_id
        from smc_student_free_coursetimes
        where school_id='{$this->school_id}' and is_use<>'-1'";
        $allFreeTimes=$this->DataControl->selectClear($sql);


        $sql="select (select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_successtime<='{$endtime}' and pop.pay_issuccess='1' limit 0,1) as pay_price,po.order_paymentprice
        from smc_payfee_order as po
        where po.school_id='{$this->school_id}' and po.order_status>='0' and po.order_createtime<='{$endtime}'
        having (po.order_paymentprice>pay_price) or (pay_price is NULL and po.order_paymentprice>0)
        ";
        $newOrderList=$this->DataControl->selectClear($sql);
        $newArrearagePrice=0;
        if($newOrderList){
            foreach($newOrderList as $val){
                $newArrearagePrice+=$val['order_paymentprice']-($val['pay_price']?$val['pay_price']:0);
            }
        }

        $sql="select (select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_successtime<='{$oldendtime}' and pop.pay_issuccess='1' limit 0,1) as pay_price,po.order_paymentprice
        from smc_payfee_order as po
        where po.school_id='{$this->school_id}' and po.order_status>='0' and po.order_createtime<='{$oldendtime}'
        having (po.order_paymentprice>pay_price) or (pay_price is NULL and po.order_paymentprice>0)
        ";
        $oldOrderList=$this->DataControl->selectClear($sql);
        $oldArrearagePrice=0;
        if($oldOrderList){
            foreach($oldOrderList as $val){
                $oldArrearagePrice+=$val['order_paymentprice']-($val['pay_price']?$val['pay_price']:0);
            }
        }



        $data=array();
        $data['newIncome']=sprintf("%.2f",$newIncomeOne['income_price'])?sprintf("%.2f",$newIncomeOne['income_price']):0.00;
        $data['oldIncome']=sprintf("%.2f",$oldIncomeOne['income_price'])?sprintf("%.2f",$oldIncomeOne['income_price']):0.00;
        $data['incomeDif']=sprintf("%.2f",$data['newIncome']-$data['oldIncome']);

        $data['newExpend']=sprintf("%.2f",$newExpendOne['expend_price'])?sprintf("%.2f",$newExpendOne['expend_price']):0.00;
        $data['oldExpend']=sprintf("%.2f",$oldExpendOne['expend_price'])?sprintf("%.2f",$oldExpendOne['expend_price']):0.00;
        $data['expendDif']=sprintf("%.2f",$data['newExpend']-$data['oldExpend']);

        $data['newPay']=sprintf("%.2f",$newPayOne['pay_price'])?sprintf("%.2f",$newPayOne['pay_price']):0.00;
        $data['oldPay']=sprintf("%.2f",$oldPayOne['pay_price'])?sprintf("%.2f",$oldPayOne['pay_price']):0.00;
        $data['payDif']=sprintf("%.2f",$data['newPay']-$data['oldPay']);

        $data['newRefund']=sprintf("%.2f",$newRefundOne['refund_payprice'])?sprintf("%.2f",$newRefundOne['refund_payprice']):0.00;
        $data['oldRefund']=sprintf("%.2f",$oldRefundOne['refund_payprice'])?sprintf("%.2f",$oldRefundOne['refund_payprice']):0.00;
        $data['refundDif']=sprintf("%.2f",$data['newRefund']-$data['oldRefund']);

        $data['allFreeTimes']=$allFreeTimes?count($allFreeTimes):0;

        $data['newArrearagePrice']=sprintf("%.2f",$newArrearagePrice)?sprintf("%.2f",$newArrearagePrice):0.00;
        $data['oldArrearagePrice']=sprintf("%.2f",$oldArrearagePrice)?sprintf("%.2f",$oldArrearagePrice):0.00;
        $data['arrearageDif']=sprintf("%.2f",$data['newArrearagePrice']-$data['oldArrearagePrice']);

        return $data;

    }

    function schoolBudget($request){

        $starttime=strtotime(date('Y-m-d').'-9 day');
        $endtime=time();

        $time1=$starttime;
        $time2=$endtime;
        $dayArr=array();

        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $dayArr[] = date('Y-m-d',$time1);
            $time1=strtotime("+1 day",$time1);
        }

        $sql="select SUM(income_price) as income_price,FROM_UNIXTIME(income_confirmtime, '%Y-%m-%d') as income_createday
              from smc_school_income
              where school_id='{$this->school_id}' and income_confirmtime>='{$starttime}' and income_confirmtime<='{$endtime}'
              group by income_createday
              order by income_createday asc
              ";
        $newIncomeList=$this->DataControl->selectClear($sql);

        $sql="select SUM(expend_price) as expend_price,FROM_UNIXTIME(expend_confirmtime, '%Y-%m-%d') as expend_createday
              from smc_school_expend
              where school_id='{$this->school_id}' and expend_confirmtime>='{$starttime}' and expend_confirmtime<='{$endtime}'
              group by expend_createday
              order by expend_createday asc
              ";
        $newExpendList=$this->DataControl->selectClear($sql);

        $sql="select SUM(pop.pay_price) as pay_price,FROM_UNIXTIME(pop.pay_successtime, '%Y-%m-%d') as pay_successday
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as po on po.order_pid=pop.order_pid
              where po.school_id='{$this->school_id}' and pop.pay_issuccess='1' and pop.paytype_code in (select cp.paytype_code from smc_code_paytype as cp where cp.paytype_ischarge='1') and pay_successtime>='{$starttime}' and pay_successtime<='{$endtime}'
              group by pay_successday
              order by pay_successday asc
              ";
        $newPayList=$this->DataControl->selectClear($sql);

        $sql="select sum(refund_payprice) as refund_payprice,FROM_UNIXTIME(refund_updatatime, '%Y-%m-%d') as refund_updataday
        from smc_refund_order
        where school_id='{$this->school_id}' and refund_status='4' and refund_updatatime>='{$starttime}' and refund_updatatime<='{$endtime}'
        group by refund_updataday
        order by refund_updataday asc
        ";
        $newRefundList=$this->DataControl->selectClear($sql);

        $tem_newIncome=array();
        if($newIncomeList){
            foreach($dayArr as $dayOne){
                foreach($newIncomeList as $one){
                    if($one['income_createday']==$dayOne){
                        $tem_newIncome[$dayOne]=sprintf("%.2f",$one['income_price']);
                    }else{
                        if(!$tem_newIncome[$dayOne]){
                            $tem_newIncome[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newIncome=array_values($tem_newIncome);
        }else{
            $tem_newIncome=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_newExpend=array();
        if($newExpendList){
            foreach($dayArr as $dayOne){
                foreach($newExpendList as $one){
                    if($one['expend_createday']==$dayOne){
                        $tem_newExpend[$dayOne]=sprintf("%.2f",$one['expend_price']);
                    }else{
                        if(!$tem_newExpend[$dayOne]){
                            $tem_newExpend[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newExpend=array_values($tem_newExpend);
        }else{
            $tem_newExpend=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_newPay=array();
        if($newPayList){
            foreach($dayArr as $dayOne){
                foreach($newPayList as $one){
                    if($one['pay_successday']==$dayOne){
                        $tem_newPay[$dayOne]=sprintf("%.2f",$one['pay_price']);
                    }else{
                        if(!$tem_newPay[$dayOne]){
                            $tem_newPay[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newPay=array_values($tem_newPay);
        }else{
            $tem_newPay=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_newRefund=array();
        if($newRefundList){
            foreach($dayArr as $dayOne){
                foreach($newRefundList as $one){
                    if($one['refund_updataday']==$dayOne){
                        $tem_newRefund[$dayOne]=sprintf("%.2f",$one['refund_payprice']);
                    }else{
                        if(!$tem_newRefund[$dayOne]){
                            $tem_newRefund[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newRefund=array_values($tem_newRefund);
        }else{
            $tem_newRefund=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_newArrearage=array();
        foreach($dayArr as $dayOne){
            $dayOneTimestamp = strtotime($dayOne . ' 23:59:59');
            $sql="select (select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_successtime<='{$dayOneTimestamp}' and pop.pay_issuccess='1' limit 0,1) as pay_price,po.order_paymentprice
                  from smc_payfee_order as po
                  where po.school_id='{$this->school_id}' and po.order_status>='0' and po.order_createtime<='{$dayOneTimestamp}'
                  having (po.order_paymentprice>pay_price) or (pay_price is NULL and po.order_paymentprice>0)
                  ";
            $newOrderList=$this->DataControl->selectClear($sql);
            $newArrearagePrice=sprintf("%.2f",0.00);
            if($newOrderList){
                foreach($newOrderList as $val){
                    $newArrearagePrice+=$val['order_paymentprice']-($val['pay_price']?$val['pay_price']:0);
                }
            }
            $tem_newArrearage[]=sprintf("%.2f",$newArrearagePrice);
        }

        $data=array();
        $k=0;
        $data[$k]['name']=$this->LgStringSwitch('总收入');
        $data[$k]['data']=$tem_newIncome;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('总支出');
        $data[$k]['data']=$tem_newExpend;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('总收费');
        $data[$k]['data']=$tem_newPay;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('总退费');
        $data[$k]['data']=$tem_newRefund;
        $k++;

//        $data[$k]['name']='累计总欠费';
//        $data[$k]['data']=$tem_newArrearage;
//        $k++;

        $legendData=$this->LgArraySwitch(array("总收入","总支出","总收费","总退费","累计总欠费"));

        $tem_data=array();
        $tem_data['allList']=$data;
        $tem_data['legendData']=$legendData;
        $tem_data['xAxisData']=$dayArr;
        return $tem_data;

    }

    function incomeConstitute($request){

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-m-d");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }
        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        $sql="select sum(income_price) as income_price from smc_school_income where school_id='{$this->school_id}' and income_confirmtime>='{$starttime}' and income_confirmtime<='{$endtime}' and income_type>='0'";
        $incomeOne=$this->DataControl->selectOne($sql);
        if($incomeOne){
            $allIncome=$incomeOne['income_price'];
        }else{
            $allIncome=0;
        }

        $sql="select sum(income_price) as income_price from smc_school_income
              where school_id='{$this->school_id}' and income_confirmtime>='{$starttime}' and income_confirmtime<='{$endtime}'";

        $courseIncome=$this->DataControl->selectOne($sql." and income_type='0'");
        $materialIncome=$this->DataControl->selectOne($sql." and income_type='2'");
        $itemIncome=$this->DataControl->selectOne($sql." and income_type='3'");
        $confiscateIncome=$this->DataControl->selectOne($sql." and income_type='1'");

        $allRate=100;
        $data=array();
        if($allIncome){
            $data['courseRate']=sprintf("%.2f",($courseIncome['income_price']?$courseIncome['income_price']:0)/$allIncome)*100;
            $data['courseIncome']=$courseIncome['income_price']?$courseIncome['income_price']:0;;
            $allRate=bcsub($allRate,$data['courseRate'],2);

            $data['materialRate']=sprintf("%.2f",($materialIncome['income_price']?$materialIncome['income_price']:0)/$allIncome)*100;
            $data['materialIncome']=$materialIncome['income_price']?$materialIncome['income_price']:0;;
            $allRate=bcsub($allRate,$data['materialRate'],2);

            $data['itemRate']=sprintf("%.2f",($itemIncome['income_price']?$itemIncome['income_price']:0)/$allIncome)*100;
            $data['itemIncome']=$itemIncome['income_price']?$itemIncome['income_price']:0;
            $allRate=bcsub($allRate,$data['itemRate'],0);

            $data['confiscateRate']=sprintf("%.2f",$allRate/100)*100;
            $data['confiscateIncome']=$confiscateIncome['income_price']?$confiscateIncome['income_price']:0;


        }else{
            $data['courseIncome']=0;
            $data['courseRate']=0;

            $data['materialRate']=0;
            $data['materialIncome']=0;

            $data['itemRate']=0;
            $data['itemIncome']=0;

            $data['confiscateRate']=0;
            $data['confiscateIncome']=0;

        }

        return $data;
    }

    function incomeTrend($request){
        $starttime=strtotime(date('Y-m-d').'-9 day');
        $endtime=time();

        $time1=$starttime;
        $time2=$endtime;
        $dayArr=array();

        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $dayArr[] = date('Y-m-d',$time1);
            $time1=strtotime("+1 day",$time1);
        }

        $sql="select sum(income_price) as income_price,FROM_UNIXTIME(income_confirmtime,'%Y-%m-%d') AS income_createday
              from smc_school_income
              where school_id='{$this->school_id}' and income_confirmtime>='{$starttime}' and income_confirmtime<='{$endtime}'";
        $group=" group by income_createday order by income_createday asc";

        $courseIncomeList=$this->DataControl->selectClear($sql." and income_type='0'".$group);
        $materialIncomeList=$this->DataControl->selectClear($sql." and income_type='2'".$group);
        $itemIncomeList=$this->DataControl->selectClear($sql." and income_type='3'".$group);
        $confiscateIncomeList=$this->DataControl->selectClear($sql." and income_type='1'".$group);

        $tem_courseIncome=array();
        if($courseIncomeList){
            foreach($dayArr as $dayOne){
                foreach($courseIncomeList as $one){
                    if($one['income_createday']==$dayOne){
                        $tem_courseIncome[$dayOne]=sprintf("%.2f",$one['income_price']);
                    }else{
                        if(!$tem_courseIncome[$dayOne]){
                            $tem_courseIncome[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_courseIncome=array_values($tem_courseIncome);
        }else{
            $tem_courseIncome=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_materialIncome=array();
        if($materialIncomeList){
            foreach($dayArr as $dayOne){
                foreach($materialIncomeList as $one){
                    if($one['income_createday']==$dayOne){
                        $tem_materialIncome[$dayOne]=sprintf("%.2f",$one['income_price']);
                    }else{
                        if(!$tem_materialIncome[$dayOne]){
                            $tem_materialIncome[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_materialIncome=array_values($tem_materialIncome);
        }else{
            $tem_materialIncome=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_itemIncome=array();
        if($itemIncomeList){
            foreach($dayArr as $dayOne){
                foreach($itemIncomeList as $one){
                    if($one['income_createday']==$dayOne){
                        $tem_itemIncome[$dayOne]=sprintf("%.2f",$one['income_price']);
                    }else{
                        if(!$tem_itemIncome[$dayOne]){
                            $tem_itemIncome[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_itemIncome=array_values($tem_itemIncome);
        }else{
            $tem_itemIncome=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }

        $tem_confiscateIncome=array();
        if($confiscateIncomeList){
            foreach($dayArr as $dayOne){
                foreach($confiscateIncomeList as $one){
                    if($one['income_createday']==$dayOne){
                        $tem_confiscateIncome[$dayOne]=sprintf("%.2f",$one['income_price']);
                    }else{
                        if(!$tem_confiscateIncome[$dayOne]){
                            $tem_confiscateIncome[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_confiscateIncome=array_values($tem_confiscateIncome);
        }else{
            $tem_confiscateIncome=array('0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00','0.00');
        }


        $data=array();
        $k=0;
        $data[$k]['name']=$this->LgStringSwitch('课程收入');
        $data[$k]['data']=$tem_courseIncome;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('教材收入');
        $data[$k]['data']=$tem_materialIncome;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('杂费收入');
        $data[$k]['data']=$tem_itemIncome;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('认缴收入');
        $data[$k]['data']=$tem_confiscateIncome;
        $k++;

        $legendData=$this->LgArraySwitch(array("课程收入","教材收入","杂费收入","认缴收入"));

        $tem_data=array();
        $tem_data['allList']=$data;
        $tem_data['legendData']=$legendData;
        $tem_data['xAxisData']=$dayArr;
        return $tem_data;

    }

    function studentChargeAnalysis($request){

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }
        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");


        $sql="select sum(T.pay_price) as price
              from (select A.pay_price
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  where B.school_id='{$this->school_id}' and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and not exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id='{$this->school_id}' and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
              ) AS T";
        $newPayPrice=$this->DataControl->selectOne($sql);

        $sql="select sum(T.pay_price) as price
              from (select A.pay_price
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  where B.school_id='{$this->school_id}' and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id='{$this->school_id}' and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
              ) AS T";
        $oldPayPrice=$this->DataControl->selectOne($sql);

        $sql="select B.student_id
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  where B.school_id='{$this->school_id}' and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and not exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id='{$this->school_id}' and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
                  group by B.student_id
              ";
        $newPayList=$this->DataControl->selectClear($sql);

        $sql="select B.student_id
              from smc_payfee_order_pay as A
              left join smc_payfee_order as B on A.order_pid=B.order_pid
              left join smc_code_paytype as C on C.paytype_code=A.paytype_code
              where B.school_id='{$this->school_id}' and C.paytype_ischarge='1' and A.pay_issuccess='1'
              and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
              and exists(select 1 from smc_payfee_order_pay as A1
                              left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                              left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                              where B1.school_id='{$this->school_id}' and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                              and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
              group by B.student_id
              ";
        $oldPayList=$this->DataControl->selectClear($sql);

        $price_array=array();
        $data=array();
        $data['value']=$newPayPrice['price']?$newPayPrice['price']:0;
        $data['name']=$this->LgStringSwitch('新用户交易金额');
        $price_array[]=$data;

        $data=array();
        $data['value']=$oldPayPrice['price']?$oldPayPrice['price']:0;
        $data['name']=$this->LgStringSwitch('老用户交易金额');
        $price_array[]=$data;

        $pay_array=array();
        $data=array();
        $data['value']=$newPayList?count($newPayList):0;
        $data['name']=$this->LgStringSwitch('新用户交易人数');
        $pay_array[]=$data;

        $data=array();
        $data['value']=$oldPayList?count($oldPayList):0;
        $data['name']=$this->LgStringSwitch('老用户交易人数');
        $pay_array[]=$data;

        $data=array();
        $data['payPrice']['allList']=$price_array;
        $data['payPrice']['legendData']=$this->LgArraySwitch(array('新用户交易金额','老用户交易金额'));

        $data['payNum']['allList']=$pay_array;
        $data['payNum']['legendData']=$this->LgArraySwitch(array('新用户交易人数','老用户交易人数'));

        return $data;
    }





}