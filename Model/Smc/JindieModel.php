<?php
namespace Model\Smc;

// 引入金蝶SDK
$pharPath = __DIR__ . '/../../Core/Tools/Jindie/kingdee-webapi-sdk-v8.0.5.phar';
if (file_exists($pharPath)) {
    // 引入SDK的入口文件
    require_once 'phar://' . $pharPath . '/index.php';
} else {
    throw new \Exception('金蝶SDK文件不存在: ' . $pharPath);
}



/**
 * 金蝶云苍穹费用报销单接口模型
 * 用于对接金蝶云苍穹系统的费用报销单相关接口
 *
 * 使用金蝶官方SDK v8.0.5 和配置文件进行连接
 *
 * 主要功能：
 * 1. 保存费用报销单 - saveExpenseReimbursement()
 * 2. 提交费用报销单 - submitExpenseReimbursement()
 * 3. 获取报销单模板 - getExpenseReimbursementTemplate()
 * 4. 检查连接状态 - getK3CloudLoginStatus()
 *
 * 使用示例：
 * $jindieModel = new JindieModel($DataControl);
 *
 * // 保存报销单
 * $data = array(
 *     'Model' => array(
 *         'FDate' => '2023-12-01',
 *         'FCausa' => '差旅费报销',
 *         'FProposerID' => array('FSTAFFNUMBER' => 'EMP001'),
 *         // ... 其他字段
 *     )
 * );
 * $result = $jindieModel->saveExpenseReimbursement($data);
 *
 * // 提交报销单
 * $submitData = array(
 *     'Numbers' => array('FYBS001', 'FYBS002'),
 *     'Ids' => '123,456'
 * );
 * $result = $jindieModel->submitExpenseReimbursement($submitData);
 *
 * 注意事项：
 * 1. 示例Model数据包中字段顺序不建议改变，否则可能会有相互影响
 * 2. 如果出现字段值被覆盖或丢失，可以尝试把字段顺序向后调整
 * 3. 示例Model数据包默认包含允许引入的字段，实际按需构建即可
 * 4. 必填字段必须提供有效值，否则会导致保存失败
 * 5. 使用官方SDK，配置信息从 Core/Tools/Jindie/conf.ini 读取
 */
class JindieModel extends modelTpl
{

    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    // 金蝶云苍穹API客户端
    private $apiClient = null;

    // 配置信息
    private $config = array();

    // 配置文件路径
    private $configPath = '';

    // 是否已登录
    private $isLoggedIn = false;

    // 日志记录相关属性
    private $logStartTime;
    private $logApiName;
    private $logRequestParams;
    private $logUserId;
    private $logPid;

    /**
     * 构造函数
     * 初始化金蝶SDK客户端和配置信息
     *
     */
    public function __construct()
    {
        parent::__construct();

        // 设置配置文件路径
        $this->configPath = __DIR__ . '/../../Core/Tools/Jindie/conf.ini';

        // 加载配置文件
        $this->loadConfig();

        // 初始化API客户端
        $this->initApiClient();
    }

    /**
     * 加载配置文件
     * 从 Core/Tools/Jindie/conf.ini 读取配置信息
     *
     * @return bool 是否加载成功
     */
    private function loadConfig()
    {
        if (!file_exists($this->configPath)) {
            $this->error = true;
            $this->errortip = '配置文件不存在: ' . $this->configPath;
            return false;
        }

        $this->config = parse_ini_file($this->configPath, true);

        if (!$this->config || !isset($this->config['config'])) {
            $this->error = true;
            $this->errortip = '配置文件格式错误';
            return false;
        }

        return true;
    }

    /**
     * 初始化API客户端
     * 使用配置文件中的信息创建金蝶SDK客户端
     *
     * @return bool 是否初始化成功
     */
    private function initApiClient()
    {
        if (empty($this->config['config'])) {
            return false;
        }

        try {
            // 创建API客户端实例（使用配置文件路径）
            $configPath = __DIR__ . '/../../Core/Tools/Jindie/conf.ini';
            $this->apiClient = new \kingdee_cdp_webapi_sdk\sdk\K3CloudApi($configPath);

            return true;
        } catch (\Exception $e) {
            $this->error = true;
            $this->errortip = 'API客户端初始化失败: ' . $e->getMessage();
            return false;
        }
    }

    /**
     * 登录金蝶云苍穹系统
     * 使用HTTP请求进行登录认证
     *
     * @return bool 登录是否成功
     */
    private function login()
    {
        if ($this->isLoggedIn) {
            return true;
        }

        if (!$this->apiClient) {
            $this->error = true;
            $this->errortip = 'API客户端未初始化';
            return false;
        }

        // SDK内部处理认证，直接标记为已登录
        $this->isLoggedIn = true;
        return true;
        
    }

    /**
     * 保存费用报销单
     * 使用金蝶SDK进行保存操作
     *
     * @param array $data 报销单数据，包含以下参数：
     *   - NeedUpDateFields: array 需要更新的字段，数组类型，格式：[key1,key2,...] （非必录）注（更新单据体字段得加上单据体key）
     *   - NeedReturnFields: array 需返回结果的字段集合，数组类型，格式：[key,entitykey.key,...]（非必录） 注（返回单据体字段格式：entitykey.key）
     *   - IsDeleteEntry: bool 是否删除已存在的分录，布尔类型，默认true（非必录）
     *   - SubSystemId: string 表单所在的子系统内码，字符串类型（非必录）
     *   - IsVerifyBaseDataField: bool 是否验证所有的基础资料有效性，布尔类，默认false（非必录）
     *   - IsEntryBatchFill: bool 是否批量填充分录，默认true（非必录）
     *   - ValidateFlag: bool 是否验证标志，布尔类型，默认true（非必录）
     *   - NumberSearch: bool 是否用编码搜索基础资料，布尔类型，默认true（非必录）
     *   - IsAutoAdjustField: bool 是否自动调整JSON字段顺序，布尔类型，默认false（非必录）
     *   - InterationFlags: string 交互标志集合，字符串类型，分号分隔，格式："flag1;flag2;..."（非必录） 例如（允许负库存标识：STK_InvCheckResult）
     *   - IgnoreInterationFlag: bool 是否允许忽略交互，布尔类型，默认true（非必录）
     *   - Model: array 表单数据包，JSON类型（必录）- 包含报销单的具体业务数据
     * @return array 返回结果，包含success状态、data数据、message信息等
     */
    public function saveExpenseReimbursement($request)
    {


        // 检查登录状态
        if (!$this->login()) {
            $this->error = true;
            $this->errortip=$this->errortip ?: '登录失败';
            return false;
        }

        try {
            $formId = "ER_ExpReimbursement"; // 费用报销单表单ID

            $requestData=$this->getSaveTemplate($request['refund_pid']);
            // 构建请求参数
            if(!$requestData){
                $this->error = true;
                return false;
            }

            // 开始记录日志
            $this->startApiLog('jindie_save_expense', $requestData, $request['refund_pid']);

            // 使用SDK调用保存接口
            $response = $this->apiClient->save($formId, $requestData);

            if($response){
                $responseArray=json_decode($response,1);

                if($responseArray['Result']['ResponseStatus']['IsSuccess']){
                    // 记录成功日志
                    $this->endApiLog($responseArray, true);

                    return $this->submitExpenseReimbursement($request);

                }else{
                    // 记录失败日志
                    $errorMsg = isset($responseArray['Result']['ResponseStatus']['Errors'][0]['Message'])
                        ? $responseArray['Result']['ResponseStatus']['Errors'][0]['Message']
                        : '保存失败';
                    $this->endApiLog($responseArray, false, $errorMsg);
                    
                    // 发送钉钉失败通知
                    $this->sendDingTalkNotice($request['refund_pid'], $errorMsg);

                    $this->error = true;
                    $this->errortip = $errorMsg;
                    return false;
                }
            } else {
                // 记录无响应日志
                $this->endApiLog(array(), false, '未收到响应');

                $this->error = true;
                $this->errortip = '未收到响应';
                return false;
            }

        } catch (\Exception $e) {
            // 记录异常日志
            $this->logApiException($e);

            $this->error = true;
            $this->errortip='保存异常: ' . $e->getMessage();
            return false;
        }
    }



    /**
     * 提交费用报销单（将已保存的报销单提交到工作流）
     * 使用金蝶SDK进行提交操作
     *
     * @param array $data 提交数据，包含以下参数：
     *   - CreateOrgId: int 创建者组织内码（非必录）
     *   - Numbers: array 单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）
     *   - Ids: string 单据内码集合，字符串类型，格式："Id1,Id2,..."（使用内码时必录）
     *   - SelectedPostId: int 工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）
     *   - NetworkCtrl: bool 是否启用网控，布尔类型，默认false（非必录）
     *   - IgnoreInterationFlag: bool 是否允许忽略交互，布尔类型，默认true（非必录）
     * @return array 返回结果，包含success状态、data数据、message信息等
     */
    public function submitExpenseReimbursement($request)
    {
        // 检查登录状态
        if (!$this->login()) {

            $this->error = true;
            $this->errortip=$this->errortip ?: '登录失败';
            return false;
        }

        try {
            $formId = "ER_ExpReimbursement"; // 费用报销单表单ID

            // 构建请求参数
            $requestData = array(
                // 单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）
                'Numbers' => array($request['refund_pid'])
            );

            // 开始记录日志
            $this->startApiLog('jindie_submit_expense', $requestData, $request['refund_pid']);

            // 使用SDK调用提交接口
            $response = $this->apiClient->submit($formId, $requestData);

            if($response){
                $responseArray=json_decode($response,1);

                if($responseArray['Result']['ResponseStatus']['IsSuccess']){
                    
                    $this->endApiLog($responseArray, true);

                    return true;

                }else{

                    $errorMsg = isset($responseArray['Result']['ResponseStatus']['Errors'][0]['Message'])
                        ? $responseArray['Result']['ResponseStatus']['Errors'][0]['Message']
                        : '提交失败';
                    $this->endApiLog($responseArray, false, $errorMsg);

                    $this->error = true;
                    $this->errortip=$responseArray['Result']['ResponseStatus']['Errors'][0]['Message'];
                    return false;
                }
            }

        } catch (\Exception $e) {

            $this->error = true;
            $this->errortip='提交异常: ' . $e->getMessage();
            return false;
        }
    }

    function queryExpenseReimbursement($request){

        $sql = "select a.refund_pid 
                from smc_refund_order as a 
                where a.requestid>0 and a.refund_status=3
                order by a.refund_id desc 
                limit 0,100
                ";

        $refundList=$this->DataControl->select($sql);

        foreach($refundList as $refund){

            $this->queryJindie($refund);
        }

        return true;

    }

    function queryJindie($request){

        // 检查登录状态
        if (!$this->login()) {

            $this->error = true;
            $this->errortip=$this->errortip ?: '登录失败';
            return false;
        }

        try {

            // 构建请求参数
            $requestData = array(
                "FormId" => "ER_ExpReimbursement",
                "FieldKeys" => "FPayedAmount",
                "FilterString" => array(
                    array(
                        "FieldName" => "FBillNo",
                        "Compare" => "67",
                        "Value" => $request['refund_pid'],
                        "Left" => "",
                        "Right" => "",
                        "Logic" => 0
                    )
                ),
                "OrderString" => "",
                "TopRowCount" => 0,
                "StartRow" => 0,
                "Limit" => 0
            );

            $this->startApiLog('jindie_query_expense', $requestData, $request['refund_pid']);

            $response = $this->apiClient->execute_bill_query($requestData);

            if($response){
                $responseArray=json_decode($response,1);

                if($responseArray){

                    $this->endApiLog($responseArray, true);

                    $refundOne=$this->DataControl->getFieldOne("smc_refund_order","company_id,school_id,staffer_id,refund_payprice,refund_pid","refund_pid='{$request['refund_pid']}'");

                    if($refundOne['refund_payprice']==$responseArray[0][0]){

                        $publicArray=[
                            'company_id'=>$refundOne['company_id'],
                            'school_id'=>$refundOne['school_id'],
                            'staffer_id'=>$refundOne['staffer_id'],
                        ];

                        $Model = new \Model\Gmc\OrderModel($publicArray);

                        $data=[
                            'refund_pid'=>$refundOne['refund_pid'],
                            'reason'=>'金蝶报销成功',
                            'staffer_id'=>$refundOne['staffer_id'],
                            'is_adopt'=>'1',
                            'is_skip'=>'1',
                        ];

                        $res = $Model->examineRefundOrder($data);
                        return true;
                    }else{
                        return false;
                    }
                }else{
                    $this->endApiLog($responseArray, false, '获取错误');
                    return false;
                }
            }

        } catch (\Exception $e) {

            $this->error = true;
            $this->errortip='提交异常: ' . $e->getMessage();
            return false;
        }

    }

    /**
     * 获取查询模板数据
     * @return array 查询模板数组
     */
    public function getQueryTemplate()
    {
        $data = array(
            "FormId" => "ER_ExpReimbursement",
            "FieldKeys" => "FPayedAmount",
            "FilterString" => array(
                array(
                    "FieldName" => "FBillNo",
                    "Compare" => "67",
                    "Value" => "07204TTNBVS250723YKPWXU",
                    "Left" => "",
                    "Right" => "",
                    "Logic" => 0
                )
            ),
            "OrderString" => "",
            "TopRowCount" => 0,
            "StartRow" => 0,
            "Limit" => 0
        );

        return $data;
    }

    /**
     * 获取报销单模板数据
     * @return array 报销单模板，包含以下主要字段：
     *   单据头字段：
     *   - FID: 实体主键
     *   - FBillNo: 单据编号
     *   - FDate: 申请日期（必填项）
     *   - FCurrencyID: 币别（必填项）
     *   - FOrgID: 申请组织（必填项）
     *   - FCausa: 事由（必填项）
     *   - FProposerID: 申请人（必填项）
     *   - FRequestDeptID: 申请部门（必填项）
     *   - FContactPhoneNo: 联系电话
     *   - FBillTypeID: 单据类型（必填项）
     *   - FExpenseOrgId: 费用承担组织（必填项）
     *   - FExpenseDeptID: 费用承担部门（必填项）
     *   - FCONTACTUNITTYPE: 往来单位类型（必填项）
     *   - FCONTACTUNIT: 往来单位（必填项）
     *   - FExchangeTypeID: 汇率类型（必填项）
     *   - FExchangeRate: 汇率
     *   报销明细字段（FEntity）：
     *   - FEntryID: 实体主键
     *   - FExpID: 费用项目（必填项）
     *   - FInvoiceType: 发票类型（必填项）
     *   - FExpenseAmount: 申请报销金额
     *   - FRequestAmount: 申请退/付款金额
     *   - FRemark: 备注
     *   - FExpenseDeptEntryID: 费用承担部门（必填项）
     *   - F_JDB_FDFYXM: 法定费用项目（必填项）
     *   - F_JDB_GLSQRQ1: 关联申请日期
     *   - F_JDB_XXHD: 行销活动
     *   - F_PAEZ_Integer: 有源单为1，无源单为0
     *   - F_PAEZ_Assistant: 陆军行销
     */
    public function getExpenseReimbursementTemplate()
    {
        return array(
            // 单据头字段
            'FID' => 0, // 实体主键
            'FBillNo' => '', // 单据编号
            'FDate' => date('Y-m-d'), // 申请日期（必填项）
            'FCurrencyID' => array('FNUMBER' => ''), // 币别（必填项）
            'FOrgID' => array('FNumber' => ''), // 申请组织（必填项）
            'FCausa' => '', // 事由（必填项）
            'FProposerID' => array('FSTAFFNUMBER' => ''), // 申请人（必填项）
            'FRequestDeptID' => array('FNUMBER' => ''), // 申请部门（必填项）
            'FContactPhoneNo' => '', // 联系电话
            'FBillTypeID' => array('FNUMBER' => ''), // 单据类型（必填项）
            'FExpenseOrgId' => array('FNumber' => ''), // 费用承担组织（必填项）
            'FExpenseDeptID' => array('FNUMBER' => ''), // 费用承担部门（必填项）
            'FCONTACTUNITTYPE' => '', // 往来单位类型（必填项）
            'FCONTACTUNIT' => array('FNumber' => ''), // 往来单位（必填项）
            'FExchangeTypeID' => array('FNUMBER' => ''), // 汇率类型（必填项）
            'FExchangeRate' => 0, // 汇率
            // 报销明细（FEntity）
            'FEntity' => array(
                array(
                    'FEntryID' => 0, // 实体主键
                    'FExpID' => array('FNUMBER' => ''), // 费用项目（必填项）
                    'FInvoiceType' => '', // 发票类型（必填项）
                    'FExpenseAmount' => 0, // 申请报销金额
                    'FRequestAmount' => 0, // 申请退/付款金额
                    'FRemark' => '', // 备注
                    'FExpenseDeptEntryID' => array('FNUMBER' => ''), // 费用承担部门（必填项）
                    'F_JDB_FDFYXM' => array('FNUMBER' => ''), // 法定费用项目（必填项）
                    'F_JDB_GLSQRQ1' => date('Y-m-d'), // 关联申请日期
                    'F_JDB_XXHD' => array('FNumber' => ''), // 行销活动
                    'F_PAEZ_Integer' => 0, // 有源单为1，无源单为0
                    'F_PAEZ_Assistant' => array('FNumber' => '') // 陆军行销
                )
            )
        );
    }

    /**
     * 获取金蝶云苍穹登录状态
     * 使用SDK检查连接和登录状态
     *
     * @return array 登录状态信息
     */
    public function getK3CloudLoginStatus()
    {
        try {
            if ($this->login()) {
                $config = $this->config['config'];
                return array(
                    'success' => true,
                    'message' => '连接成功',
                    'server' => $config['X-KDApi-ServerUrl'],
                    'acctID' => $config['X-KDApi-AcctID'],
                    'userName' => $config['X-KDApi-UserName'],
                    'appId' => $config['X-KDApi-AppID'],
                    'lcid' => isset($config['X-KDApi-LCID']) ? $config['X-KDApi-LCID'] : 2052,
                    'sdk_version' => 'v8.0.5'
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $this->errortip ?: '连接失败',
                    'server' => isset($this->config['config']['X-KDApi-ServerUrl']) ? $this->config['config']['X-KDApi-ServerUrl'] : '',
                    'error_code' => 1
                );
            }
        } catch (\Exception $e) {
            return array(
                'success' => false,
                'message' => '连接异常: ' . $e->getMessage(),
                'error_code' => 4
            );
        }
    }


    function getSaveTemplate($order_pid){


        $sql = "SELECT a.refund_pid,b.staffer_employeepid,c.school_branch,a.refund_bank,a.refund_accountname,a.refund_bankcard,a.refund_reason,a.refund_payprice,d.companies_kidbranch,a.staffer_id,c.school_kddeptcode 
                from smc_refund_order as a 
                inner join smc_staffer as b on b.staffer_id=a.staffer_id
                inner join smc_school as c on c.school_id=a.school_id
                inner join gmc_code_companies as d on d.companies_id=a.companies_id
                where a.refund_pid='{$order_pid}' 
                -- and a.refund_status=1
                limit 0,1
                ";

        $orderOne=$this->DataControl->selectOne($sql);

        if(!$orderOne){
            $this->error = true;
            $this->errortip='退费订单不存在';
            return false;
        }

        if($orderOne['staffer_employeepid']=='' || $orderOne['school_branch']==''){
            $this->error = true;
            $this->errortip='员工编号或校区编号不存在';
            return false;
        }

        if($orderOne['school_kddeptcode']==''){
            $this->error = true;
            $this->errortip='校区对应的金蝶部门不存在';
            return false;
        }

        $data = array(
            "NeedUpDateFields" => array(),
            "NeedReturnFields" => array(),
            "IsDeleteEntry" => "true",
            "SubSystemId" => "",
            "IsVerifyBaseDataField" => "false",
            "IsEntryBatchFill" => "true",
            "ValidateFlag" => "true",
            "NumberSearch" => "true",
            "IsAutoAdjustField" => "false",
            "InterationFlags" => "",
            "IgnoreInterationFlag" => "",
            "Model" => array(
                "FID" => 0,
                "FBillNo" => $order_pid,
                "F_PAEZ_Combo2" => 'kdd',
                "FDate" => date("Y-m-d H:i:s"),
                "FCurrencyID" => array(
                    "FNUMBER" => "PRE001"
                ),
                "FOrgID" => array(
                    "FNumber" => $orderOne['school_branch']
                ),
                "FCausa" => $orderOne['refund_reason'],
                "FProposerID" => array(
                    "FSTAFFNUMBER" => $orderOne['staffer_employeepid']
                ),
                "FRequestDeptID" => array(
                    "FNUMBER" => "BM000003"//申请部门
                ),
                "FBillTypeID" => array(
                    "FNUMBER" => "FYBXD001_SYS"
                ),
                "FExpenseOrgId" => array(
                    "FNumber" => $orderOne['school_branch']
                ),
                "FExpenseDeptID" => array(
                    "FNUMBER" => $orderOne['school_kddeptcode']//费用承担部门
                ),
                "FCONTACTUNITTYPE" => "BD_Empinfo",
                "FCONTACTUNIT" => array(
                    "FNumber" => $orderOne['staffer_employeepid']
                ),
                "FPayOrgId" => array(
                    "FNumber" => $orderOne['companies_kidbranch']
                ),
                "FPaySettlleTypeID" => array(
                    "FNUMBER" => "JSFS03_SYS"
                ),
                "FBankBranchT" => $orderOne['refund_bank'],
                "FBankAccountNameT" => $orderOne['refund_accountname'],
                "FBankAccountT" => $orderOne['refund_bankcard'],
                "FLocCurrencyID" => array(
                    "FNUMBER" => "PRE001"
                ),
                "FExchangeTypeID" => array(
                    "FNUMBER" => "HLTX01_SYS"
                ),
                "FExchangeRate" => 1.0,
                "FSplitEntry" => false,
                "FCombinedPay" => true,
                "FLocExpAmountSum" => $orderOne['refund_payprice'],
                "FLocReqAmountSum" => $orderOne['refund_payprice'],
                "FExpAmountSum" => $orderOne['refund_payprice'],
                "FReqAmountSum" => $orderOne['refund_payprice'],
                "FCreatorId" => array(
                    "FUserID" => 16394
                ),
                "FCreateDate" => date("Y-m-d H:i:s"),
                "FRequestType" => "1",
                "FReqReimbAmountSum" => $orderOne['refund_payprice'],
                "FReqPayReFoundAmountSum" => $orderOne['refund_payprice'],
                "FRealPay" => false,
                "FMultiPayee" => false,
                "FillByBeimAmount" => false,
                "F_JDB_FYSQRQ" => date("Y-m-d H:i:s"),
                "FIsNegtiv" => false,
                "F_JDB_FRBZ" => $orderOne['refund_reason'],
                "F_JDB_FRZ" => true,
                "F_JDB_GLZ" => true,
                "F_PAEZ_SQZZZZLX" => array(
                    "FNumber" => "02"
                ),
                "F_PAEZ_CheckBox" => false,
                "F_PAEZ_Combo1" => "1",
                "FEntity" => array(
                    array(
                        "FExpID" => array(
                            "FNUMBER" => "FYXM2079"
                        ),
                        "FLocExpSubmitAmount" => $orderOne['refund_payprice'],
                        "FLocReqSubmitAmount" => $orderOne['refund_payprice'],
                        "FLOCNOTAXAMOUNT" => $orderOne['refund_payprice'],
                        "FTaxSubmitAmt" => $orderOne['refund_payprice'],
                        "FExpenseAmount" => $orderOne['refund_payprice'],
                        "FExpenseDeptEntryID" => array(
                            "FNUMBER" => $orderOne['school_kddeptcode']//费用承担部门
                        ),
                        "FRequestAmount" => $orderOne['refund_payprice'],
                        "FExpSubmitAmount" => $orderOne['refund_payprice'],
                        "FReqSubmitAmount" => $orderOne['refund_payprice'],
                        "FOnlineBank" => false,
                        "F_JDB_FDFYXM" => array(
                            "FNUMBER" => "FYXM2079"
                        ),
                        "F_JDB_GLSQRQ1" => date("Y-m-d H:i:s")
                    )
                )
            )
        );

        return $data;
    }

    // ========== API日志记录方法 ==========

    /**
     * 开始记录API调用日志
     * @param string $apiName API名称
     * @param array $requestParams 请求参数
     * @param string $userId 用户ID
     * @param string $pid 单据号
     */
    private function startApiLog($apiName, $requestParams = array(), $pid = '')
    {

        $orderOne=$this->DataControl->getFieldOne("smc_refund_order","staffer_id","refund_pid='{$pid}'");

        $this->logStartTime = microtime(true);
        $this->logApiName = $apiName;
        $this->logRequestParams = $requestParams;
        $this->logUserId = $orderOne['staffer_id'];
        $this->logPid = $pid;
    }

    /**
     * 结束记录并保存API调用日志
     * @param mixed $responseData 响应数据
     * @param bool $isSuccess 是否成功
     * @param string $errorMessage 错误信息
     */
    private function endApiLog($responseData = null, $isSuccess = true, $errorMessage = '')
    {
        if (!$this->DataControl || !$this->logStartTime) {
            return false;
        }

        $endTime = microtime(true);
        $responseTime = round(($endTime - $this->logStartTime) * 1000); // 转换为毫秒

        $logData = array(
            'pid' => $this->logPid,
            'api_name' => $this->logApiName,
            'request_params' => json_encode($this->logRequestParams, JSON_UNESCAPED_UNICODE),
            'response_data' => json_encode($responseData, JSON_UNESCAPED_UNICODE),
            'is_success' => $isSuccess ? 1 : 0,
            'error_message' => $errorMessage,
            'response_time' => $responseTime,
            'user_id' => $this->logUserId,
            'ip_address' => $this->getClientIp()
        );

        return $this->saveApiLogToDatabase($logData);
    }

    /**
     * 记录API调用异常
     * @param Exception $exception 异常对象
     */
    private function logApiException($exception)
    {
        $this->endApiLog(
            array('exception' => $exception->getMessage()),
            false,
            $exception->getMessage()
        );
    }

    /**
     * 快速记录API调用日志（静态方法风格的实例方法）
     * @param string $apiName API名称
     * @param array $requestParams 请求参数
     * @param mixed $responseData 响应数据
     * @param bool $isSuccess 是否成功
     * @param string $errorMessage 错误信息
     * @param string $userId 用户ID
     * @param string $pid 单据号
     */
    public function quickApiLog($apiName, $requestParams, $responseData, $isSuccess, $errorMessage = '', $userId = '', $pid = '')
    {
        $this->startApiLog($apiName, $requestParams, $pid);
        $this->endApiLog($responseData, $isSuccess, $errorMessage);
    }

    /**
     * 保存API日志到数据库
     * @param array $logData 日志数据
     * @return bool
     */
    private function saveApiLogToDatabase($logData)
    {
        try {
            // insertData 方法通常直接接受表名和数据数组
            return $this->DataControl->insertData('api_call_log', $logData);
        } catch (\Exception $e) {
            // 记录日志失败时，写入错误日志
            error_log("API日志保存失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取客户端IP地址
     * @return string
     */
    private function getClientIp()
    {
        $ipKeys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');

        foreach ($ipKeys as $key) {
            if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }

        return '127.0.0.1';
    }

    /**
     * 发送钉钉通知（KISS原则 - 只保留卡片发送）
     *
     * @param string $refundPid 退费单号
     * @param string $errorMessage 错误信息
     */
    private function sendDingTalkNotice($refundPid, $errorMessage)
    {
        try {
            // 获取退费单信息

            $sql = "select a.*,b.school_cnname from smc_refund_order as a 
                    left join smc_school as b on b.school_id=a.school_id
                    where a.refund_pid='{$refundPid}' 
                    limit 0,1
                    ";


            $orderOne = $this->DataControl->selectOne($sql);
            if (!$orderOne) {
                // 即使找不到退费单信息，也要发送通知
                $orderOne = array(
                    'school_cnname' => '未知校区',
                    'refund_pid' => $refundPid
                );
                error_log("警告：找不到退费单信息 - 退费单号: {$refundPid}，仍然发送钉钉通知");
            }

            // 获取接收人 - 使用正确的格式
            $recipients = array(
                array('dd_user_id' => '619268940'),
                array('dd_user_id' => '619268929'),
                array('dd_user_id' => '619268935')
            );

            // 构建详情URL - 跳转到退费单详情页面
            $detailUrl = "https://www.baidu.com/s?wd=" . $refundPid;

            // 使用新的事件驱动钉钉消息系统
            require_once dirname(__FILE__) . '/../Public/DingTalkEventsModel.php';
            $dingTalkEvents = new \Model\PublicModel\DingTalkEventsModel();

            // 构建接收人数据（新格式）
            $eventRecipients = array();
            foreach ($recipients as $recipient) {
                $eventRecipients[] = array(
                    'dd_user_id' => $recipient['dd_user_id'], // 提取实际的用户ID字符串
                    'staffer_id' => isset($orderOne['staffer_id']) ? $orderOne['staffer_id'] : 0,
                    'name' => '系统管理员'
                );
            }

            // 构建事件数据
            $eventData = array(
                'refund_pid' => $refundPid,
                'school_name' => $orderOne['school_cnname'],
                'error_message' => $errorMessage,
                'detail_url' => $detailUrl,
                'business_type' => 'jindie_refund_error',
                'business_id' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'config' => array(
                    'jump_mode' => 'external_browser'  // 强制外部浏览器打开
                )
            );

            // 触发金蝶异常通知事件（用于日志记录）
            $result = $dingTalkEvents->triggerEvent('jindie_error', $eventData, $eventRecipients);
            
            // 记录日志
            if ($result) {
                error_log("钉钉通知发送成功 - 退费单: {$refundPid}");
            } else {
                $errorInfo = $dingTalkEvents->getLastError();
                error_log("钉钉通知发送失败 - 退费单: {$refundPid}, 错误: " . $errorInfo['errortip']);
            }

        } catch (\Exception $e) {
            error_log("钉钉通知异常 - 退费单: {$refundPid}, 异常: " . $e->getMessage());
        }
    }


}

