<?php


namespace Model\Smc;

class GoodsFeeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function goodsList($request)
    {
        $datawhere = "1";
        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and 1=2";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (eg.goods_cnname  like '%{$request['keyword']}%' or eg.goods_pid like '%{$request['keyword']}%' OR eg.goods_pid IN (SELECT sc.goods_pid FROM smc_erp_course_goods as sc WHERE sc.course_branch = '{$request['keyword']}') )";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select eg.goods_id,eg.goods_cnname,eg.goods_pid,cp.prodtype_name,eg.goods_vipprice,eg.goods_unit
				  ,(select  gr.goods_repertory from  smc_erp_goods_repertory as gr where eg.goods_id =gr.goods_id and school_id = '{$request['school_id']}'  limit 0,1 ) as  goods_repertory
				  ,SC.companies_id,gcc.companies_cnname
                  from erp_goods as eg
				  LEFT JOIN smc_code_prodtype as cp on cp.prodtype_code = eg.prodtype_code and cp.company_id=eg.company_id
				  LEFT JOIN SMC_SCHOOL AS SC ON SC.SCHOOL_ID='{$request['school_id']}'
				  left join gmc_code_companies as gcc on SC.companies_id=gcc.companies_id
				  where  {$datawhere} and  eg.company_id ='{$request['company_id']}' and eg.goods_issale='1'
				  and eg.goods_vipprice>0 and eg.goods_originalprice>0
				  order by eg.goods_id desc
				  limit {$pagestart},{$num}";

        $list = $this->DataControl->selectClear($sql);

        if ($list) {
            foreach ($list as &$val) {
                if (!$val['goods_repertory']) {
                    $val['goods_repertory'] = '0';
                }
            }
        }

        if (!$list) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        foreach ($list as &$goodOne) {
            $goodOne['purchase_quantity'] = 1;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select eg.goods_id
                   from erp_goods as eg
				  where  {$datawhere} and  eg.company_id ='{$request['company_id']}' and eg.goods_issale='1'
				  and eg.goods_vipprice>0 and eg.goods_originalprice>0
                      ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        $data['list'] = $list;
        return $data;
    }

    function getItemList($request)
    {
        $datawhere = "1";
        if ($request['staffer_id'] == '27550') {
            $datawhere .= ' and 1=2 ';
        }
        $list = $this->DataControl->selectClear("select a.*,b.companies_id,c.companies_cnname 
        from smc_code_feeitem a,smc_school b,gmc_code_companies c
        where a.company_id='{$request['company_id']}'  
        {$datawhere}
        and feeitem_class='1'
        and feeitem_expendtype='2' 
        and b.school_id='{$request['school_id']}' 
        and b.companies_id=c.companies_id");
        if (!$list) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        return $list;
    }

    function getCourseItemList($request)
    {
        $datawhere = "1";
        $where = " 1 ";
        if ($request['staffer_id'] == '27550') {
            $datawhere .= ' and 1=2 ';
            $where .= ' and 1=2 ';
        }
        $day = date("Y-m-d", time());
        $course_sql = "SELECT p.pricing_id FROM smc_fee_pricing AS p,smc_fee_agreement AS a
					WHERE p.agreement_id = a.agreement_id
					AND (
							(
								p.pricing_applytype = '1'
								AND p.pricing_id IN (
									SELECT
										pricing_id
									FROM
										smc_fee_pricing_apply AS a
									WHERE
										a.school_id = '{$request['school_id']}'
								)
							)
							OR (
								p.pricing_applytype = '-1'
								AND p.pricing_id NOT IN (
									SELECT
										pricing_id
									FROM
										smc_fee_pricing_apply AS a
									WHERE
										a.school_id = '{$request['school_id']}'
								)
							)
							OR (p.pricing_applytype = '0')
					)
					AND a.agreement_startday <= '{$day}'
					AND a.agreement_endday >= '{$day}'
					AND a.agreement_status = '1'
					AND a.company_id = '{$request['company_id']}'
					GROUP BY
						p.course_id";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cf.feeitem_cnname like '%{$request['keyword']}%' or sc.course_cnname like '%{$request['keyword']}%')";
            $where .= " and (fpp.products_name like '%{$request['keyword']}%' or sc.course_cnname like '%{$request['keyword']}%')";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and fp.course_id = '{$request['course_id']}'";
            $where .= " and fp.course_id = '{$request['course_id']}'";
        }
        $sql = "select fpi.*,cf.feeitem_cnname,fp.course_id,sc.course_cnname,sscs.companies_id,gcc.companies_cnname
			  from smc_fee_pricing as fp
			  left join smc_fee_pricing_items as fpi on fpi.pricing_id=fp.pricing_id
			  left join smc_code_feeitem as cf on cf.feeitem_branch=fpi.feeitem_branch and cf.company_id='{$request['company_id']}'
			  left join smc_course as sc on sc.course_id=fp.course_id
			  left join smc_school_coursecat_subject as sscs on sscs.school_id = '{$request['school_id']}' and sscs.coursecat_id=sc.coursecat_id
			  left join gmc_code_companies as gcc on sscs.companies_id=gcc.companies_id
			  where {$datawhere} 
			  and cf.company_id='{$request['company_id']}' 
			  and cf.feeitem_class=0 
			  and fp.pricing_id in ($course_sql)
              AND sc.course_status = 1 
              ";
        $itemsList = $this->DataControl->selectClear($sql);
        if (!$itemsList) {
            $itemsList = array();
        }
        foreach ($itemsList as &$itemsOne) {
            $itemsOne['feeitem_cnname'] = $itemsOne['course_cnname'] . '的' . $itemsOne['feeitem_cnname'];
            $itemsOne['is_product'] = 0;
            $itemsOne['purchase_quantity'] = 1;
        }

        $tem_sql = "select fpp.*,fp.course_id,sc.course_cnname
                    ,ifnull(gr.goods_repertory,0) as goods_repertory
                    ,eg.goods_unit
                    ,ifnull(cp.prodtype_name,'') as prodtype_name
                    ,sscs.companies_id
                    ,gcc.companies_cnname
				    from smc_fee_pricing_products as fpp
				    left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
				    left join smc_course as sc on sc.course_id=fp.course_id
				    left join smc_erp_goods_repertory as gr on gr.goods_id=fpp.goods_id and gr.school_id='{$this->school_id}'
				    left join erp_goods as eg on eg.goods_id=fpp.goods_id
				    left join smc_code_prodtype as cp on cp.prodtype_code=eg.prodtype_code and cp.company_id='{$this->company_id}'
				    left join smc_school_coursecat_subject as sscs on sscs.school_id = '{$request['school_id']}' and sscs.coursecat_id=sc.coursecat_id
				    left join gmc_code_companies as gcc on sscs.companies_id=gcc.companies_id
				    where {$where} 
				    and fp.pricing_id in ($course_sql)  
				    and sc.company_id='{$request['company_id']}' 
				    group by fpp.goods_id
				    ";
        $productList = $this->DataControl->selectClear($tem_sql);
        if (!$productList) {
            $productList = array();
        }
        foreach ($productList as &$productOne) {
            $productOne['products_name'] = $productOne['course_cnname'] . '的' . $productOne['products_name'];
            $productOne['is_product'] = 1;
            $productOne['purchase_quantity'] = 1;
        }

        $list_field = array();
        $list_field[0]["fieldstring"] = "items_id";
        $list_field[0]["fieldname"] = $this->LgStringSwitch("收费项目ID");
        $list_field[0]["show"] = 0;
        $list_field[0]["custom"] = 0;

        $list_field[1]["fieldstring"] = "feeitem_cnname";
        $list_field[1]["fieldname"] = $this->LgStringSwitch("收费项目名称");
        $list_field[1]["show"] = 1;
        $list_field[1]["custom"] = 0;

        $list_field[2]["fieldstring"] = "feeitem_branch";
        $list_field[2]["fieldname"] = $this->LgStringSwitch("收费项目编号");
        $list_field[2]["show"] = 1;
        $list_field[2]["custom"] = 0;

        $list_field[3]["fieldstring"] = "items_sellingprice";
        $list_field[3]["fieldname"] = $this->LgStringSwitch("销售价");
        $list_field[3]["show"] = 1;
        $list_field[3]["custom"] = 0;

        $list_field[4]["fieldstring"] = "items_unitprice";
        $list_field[4]["fieldname"] = $this->LgStringSwitch("标准单价");
        $list_field[4]["show"] = 1;
        $list_field[4]["custom"] = 0;

        $list_field[5]["fieldstring"] = "items_buypiece";
        $list_field[5]["fieldname"] = $this->LgStringSwitch("销售件数");
        $list_field[5]["show"] = 1;
        $list_field[5]["custom"] = 0;

        $list_field[6]["fieldstring"] = "items_ismustbuy";
        $list_field[6]["fieldname"] = $this->LgStringSwitch("是否必买");
        $list_field[6]["show"] = 0;
        $list_field[6]["custom"] = 0;

        $list_field[7]["fieldstring"] = "items_donatepiece";
        $list_field[7]["fieldname"] = $this->LgStringSwitch("赠送件数");
        $list_field[7]["show"] = 0;
        $list_field[7]["custom"] = 0;

        $list_field[8]["fieldstring"] = "items_isfree";
        $list_field[8]["fieldname"] = $this->LgStringSwitch("是否免费");
        $list_field[8]["show"] = 0;
        $list_field[8]["custom"] = 0;

        $list_field[9]["fieldstring"] = "course_id";
        $list_field[9]["fieldname"] = $this->LgStringSwitch("课程ID");
        $list_field[9]["show"] = 0;
        $list_field[9]["custom"] = 0;

        $list_field[10]["fieldstring"] = "companies_id";
        $list_field[10]["fieldname"] = $this->LgStringSwitch("主体id");
        $list_field[10]["show"] = 0;
        $list_field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "companies_cnname";
        $field[11]["fieldname"] = $this->LgStringSwitch("主体名称");
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $field = array();
        $field[0]["fieldstring"] = "products_id";
        $field[0]["fieldname"] = $this->LgStringSwitch("教学用品ID");
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "goods_id";
        $field[1]["fieldname"] = $this->LgStringSwitch("教学商品ID");
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "products_name";
        $field[2]["fieldname"] = $this->LgStringSwitch("教学用品名称");
        $field[2]["show"] = 1;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "goods_pid";
        $field[3]["fieldname"] = $this->LgStringSwitch("教学商品编号");
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "products_sellingprice";
        $field[4]["fieldname"] = $this->LgStringSwitch("销售价");
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "products_unitprice";
        $field[5]["fieldname"] = $this->LgStringSwitch("标准单价");
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "products_buypiece";
        $field[6]["fieldname"] = $this->LgStringSwitch("销售件数");
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "products_ismustbuy";
        $field[7]["fieldname"] = $this->LgStringSwitch("是否必买");
        $field[7]["show"] = 0;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "products_donatepiece";
        $field[8]["fieldname"] = $this->LgStringSwitch("赠送件数");
        $field[8]["show"] = 0;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "products_isfree";
        $field[9]["fieldname"] = $this->LgStringSwitch("是否免费");
        $field[9]["show"] = 0;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "companies_id";
        $field[10]["fieldname"] = $this->LgStringSwitch("主体id");
        $field[10]["show"] = 0;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "companies_cnname";
        $field[11]["fieldname"] = $this->LgStringSwitch("主体名称");
        $field[11]["show"] = 0;
        $field[11]["custom"] = 0;

        $data = array();

        $data['list']['field'] = $list_field;
        $data['list']['list'] = $itemsList;
        if (!$itemsList) {
            $data['list']['errortip'] = "暂无收费项目";
        }

        $data['productList']['field'] = $field;
        $data['productList']['productList'] = $productList;
        if (!$productList) {
            $data['productList']['errortip'] = "暂无教学用品";
        }

        return $data;
    }

    function buyGoods($request, $from = 0, $feetype_code = '')
    {
        if (!$request['companies_id'] || $request['companies_id'] == '' || $request['companies_id'] == '0') {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$request['school_id']}'");
            if ($schoolOne) {
                $request['companies_id'] = $schoolOne['companies_id'];
            }
        }

        $stublcOne = $this->getStuBalance($request['student_id'], $request['company_id'], $request['school_id'], $request['companies_id']);

        if (!isset($request['is_balance']) || $request['is_balance'] == '0') {
            //不使用账户余额
            $stublcOne['student_balance'] = 0;
            $stublcOne['student_withholdbalance'] = 0;
        }
        if ($from == 1) {
            $order_status = 0;
        } else {
            $order_status = 1;
        }
        $request['price'] = ceil($request['price']);

        if (!isset($request['order_from'])) {
            $order_from = 1;
        } else {
            $order_from = 0;
        }
        $goodsList = json_decode(stripslashes($request['list']), true);
        $all_coupon_list = json_decode(stripslashes($request['all_coupon_price_list']), 1);
        $order_price = $request['price'] + $request['all_coupon_price'];
        $all_coupon_price = $request['all_coupon_price'];

        $couponsPrice = 0;
        $samecompanies = true;
        foreach ($goodsList as $goodsOne) {
            if (isset($goodsOne['couponList']) && $goodsOne['couponList'] != '' && $goodsOne['couponList']) {
                foreach ($goodsOne['couponList'] as $value) {
                    if ($goodsOne['sellingprice'] < $value['derateprice']) {
                        $goodsOne['sellingprice'] = 0;
                        $value['derateprice'] = $goodsOne['sellingprice'];
                    } else {
                        $goodsOne['sellingprice'] -= $value['derateprice'];
                    }
                    $couponsPrice += $value['derateprice'];
                }
            }
            if (!$goodsOne['companies_id'] || $goodsOne['companies_id'] == '' || $goodsOne['companies_id'] == '0') {
                $goodsOne['companies_id'] = $schoolOne['companies_id'];
            }
            if ($request['companies_id'] !== $goodsOne['companies_id']) {
                $samecompanies = false;
                break;
            }
        }
        if (!$samecompanies) {
            $this->error = true;
            $this->errortip = "下单商品主体错误";
            return false;
        }

        if ($all_coupon_price) {
            $couponsPrice += $all_coupon_price;
        }

        $OrderModel = new \Model\Smc\OrderModel($request);
        $order_pid = $OrderModel->createOrder($request['student_id'], 'PayitemFee', $request['price'], '', 1, $couponsPrice, 0, $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功,请尽快支付~'), $request['order_remarks'], 0, $order_status, strtotime($request['create_time']), 0, $order_from, $request['parenter_id'], 0, 0, $stublcOne['companies_id']);

        $OrderHandleModel = new \Model\Smc\OrderHandleModel($request, $order_pid);
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        if ($request['price'] == 0) {
            $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣金额记录'), $paytype_code = "balance", $request['price'], $request['create_time']);
            $OrderHandleModel->orderPaylog($paypid, $request['price'], '', 0, $this->LgStringSwitch('余额抵扣金额记录'), 'balance', $request['create_time']);
        }

        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "trading_pid", "order_pid='{$order_pid}'");

        $goodsprice = 0;
        $itemprice = 0;
        foreach ($goodsList as $key => $goodsOne) {
            if ($goodsOne['class'] == 0) {//课程杂费
                $one = $this->DataControl->getFieldOne("smc_fee_pricing_items", "items_sellingprice,items_unitprice,items_buypiece", "items_id='{$goodsOne['items_id']}'");
                if ($one['items_buypiece'] == $goodsOne['buypiece']) {
                    $itemprice += $one['items_sellingprice'] * $goodsOne['buynums'];
                } else {
                    $itemprice += ceil($goodsOne['buynums'] * $goodsOne['buypiece'] * $goodsOne['unitprice']);
                }

            } elseif ($goodsOne['class'] == 1) {//普通杂费
                $goodsOne['feeitem_branch'] = addslashes($goodsOne['feeitem_branch']);
                $feeitemOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_id,feeitem_expendtype,feeitem_price", "feeitem_branch='{$goodsOne['feeitem_branch']}' and company_id='{$request['company_id']}'");

                if (!isset($goodsOne['unitprice']) || $goodsOne['unitprice'] <= 0) {
                    $goodsOne['unitprice'] = $feeitemOne['feeitem_price'];
                }

                $itemprice += ceil($goodsOne['num'] * $goodsOne['unitprice']);
            } elseif ($goodsOne['class'] == 2) {//普通课程杂费  如 笔 橡皮
                $couponsOnePrice = 0;
                $allprice = $goodsOne['sellingprice'] * $goodsOne['num'];

                if (isset($goodsOne['couponList']) && $goodsOne['couponList'] != '' && $goodsOne['couponList']) {
                    foreach ($goodsOne['couponList'] as $value) {

                        if ($allprice < $value['derateprice']) {
                            $value['derateprice'] = $allprice;

                        }
                        $couponsOnePrice += $value['derateprice'];
                    }
                }

                $playamount = $allprice;
                if (isset($request['all_coupon_price_list']) && $all_coupon_list) {
                    if (count($goodsList) == ($key + 1)) {
                        $tem_all = $all_coupon_price;
                    } else {
                        $tem_all = sprintf("%.0f", ($playamount / $order_price) * $request['all_coupon_price']);
                        if ($tem_all > $playamount) {
                            $tem_all = $playamount;
                        }
                        $all_coupon_price -= $tem_all;
                    }
                    $couponsOnePrice += (int)$tem_all;
                }

                if ($couponsOnePrice > 0) {
                    $goodsprice += ceil($goodsOne['num'] * $goodsOne['sellingprice'] - $couponsOnePrice);
                } else {
                    $goodsprice += ceil($goodsOne['num'] * $goodsOne['sellingprice']);
                }
            }
        }


        if ($goodsprice > 0) {
            if ($stublcOne['student_balance'] > 0) {
                if ($stublcOne['student_balance'] >= $goodsprice) {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣教材金额记录'), $paytype_code = "balance", $goodsprice, $request['create_time'], 1);
                    $OrderHandleModel->orderPaylog($paypid, $goodsprice, '', 0, $this->LgStringSwitch('余额抵扣教材金额记录'), 'balance', $request['create_time']);
                    $BalanceModel->reduceStuBalance($request['student_id'], $orderOne['trading_pid'], $goodsprice, '', $request['companies_id'], strtotime($request['create_time']));
                    $stublcOne['student_balance'] -= $goodsprice;
                } else {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣教材金额记录'), $paytype_code = "balance", $stublcOne['student_balance'], $request['create_time'], 1);
                    $OrderHandleModel->orderPaylog($paypid, $stublcOne['student_balance'], '', 0, $this->LgStringSwitch('余额抵扣教材金额记录'), 'balance', $request['create_time']);
                    $BalanceModel->reduceStuBalance($request['student_id'], $orderOne['trading_pid'], $stublcOne['student_balance'], '', $request['companies_id'], strtotime($request['create_time']));
                    $OrderHandleModel->orderPay('', '0', $note = $this->LgStringSwitch('剩余教材费用'), $paytype_code = "", $goodsprice - $stublcOne['student_balance'], $request['create_time'], 1);
                    $stublcOne['student_balance'] = 0;
                }
            } else {
                $OrderHandleModel->orderPay('', '0', $note = $this->LgStringSwitch('教材费用'), $paytype_code = "", $goodsprice, $request['create_time'], 1);
            }
        }
        $isconsume = 1;

        if ($itemprice > 0) {
            $lastPrice = $stublcOne['student_balance'];
            if ($lastPrice > 0) {
                if ($lastPrice >= $itemprice) {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣杂费费用记录'), $paytype_code = "balance", $itemprice, $request['create_time'], 2);
                    $OrderHandleModel->orderPaylog($paypid, $itemprice, '', 0, $this->LgStringSwitch('余额抵扣杂费费用记录'), 'balance', $request['create_time']);
                    $BalanceModel->reduceStuBalance($request['student_id'], $orderOne['trading_pid'], $itemprice, '', $request['companies_id'], strtotime($request['create_time']));
                } else {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣杂费费用记录'), $paytype_code = "balance", $lastPrice, $request['create_time'], 2);
                    $OrderHandleModel->orderPaylog($paypid, $lastPrice, '', 0, $this->LgStringSwitch('余额抵扣杂费费用记录'), 'balance', $request['create_time']);
                    $BalanceModel->reduceStuBalance($request['student_id'], $orderOne['trading_pid'], $lastPrice, '', $request['companies_id'], strtotime($request['create_time']));
                    $OrderHandleModel->orderPay('', '0', $note = $this->LgStringSwitch('剩余杂费费用'), $paytype_code = "", $itemprice - $lastPrice, $request['create_time'], 2);
                }
            } else {
                $OrderHandleModel->orderPay('', '0', $note = $this->LgStringSwitch('杂费费用'), $paytype_code = "", $itemprice, $request['create_time'], 2);
                $isconsume = 0;
            }
        }

        $out_pid = $OrderHandleModel->beoutorder('0');

        $all_coupon_price = $request['all_coupon_price'];

        foreach ($goodsList as $key => $goodsOne) {
            $goodsOne['feeitem_branch'] = addslashes($goodsOne['feeitem_branch']);
            if ($goodsOne['class'] == 0) {//课程杂费
                $OrderHandleModel->orderCourseGoods($request['student_id'], $goodsOne['course_id'], $goodsOne['items_id'], $goodsOne['feeitem_branch'], $goodsOne['buypiece'], $goodsOne['buynums'], $goodsOne['unitprice'], $isconsume, strtotime($request['create_time']), $goodsOne['sellgoods_id']);
            } elseif ($goodsOne['class'] == 1) {//普通杂费
                $OrderHandleModel->orderOrdinaryGoods($request['student_id'], $goodsOne['unitprice'], $goodsOne['num'], $goodsOne['feeitem_branch'], $isconsume, strtotime($request['create_time']));
            } elseif ($goodsOne['class'] == 2) {//普通课程杂费  如 笔 橡皮
                $couponsOnePrice = 0;
                $allprice = $goodsOne['sellingprice'] * $goodsOne['num'];

                if (isset($goodsOne['couponList']) && $goodsOne['couponList'] != '' && $goodsOne['couponList']) {
                    foreach ($goodsOne['couponList'] as $value) {
                        $OrderHandleModel->useCoupons($request['student_id'], $value['coupons_id']);
                        $couponsOne = $this->DataControl->getFieldOne('smc_student_coupons', 'coupons_pid,coupons_type', "coupons_id ='{$value['coupons_id']}' and  student_id='{$request['student_id']}'");

                        if ($allprice < $value['derateprice']) {
                            $value['derateprice'] = $allprice;

                        }
                        $allprice -= $value['derateprice'];
                        $OrderHandleModel->addOrderCouponsLog($order_pid, 0, $goodsOne['goods_id'], $couponsOne['coupons_pid'], $value['derateprice']);
                        $couponsOnePrice += $value['derateprice'];
                    }
                }

                $playamount = $allprice;
                if (isset($request['all_coupon_price_list']) && $all_coupon_list) {
                    if (count($goodsList) == ($key + 1)) {
                        $tem_all = $all_coupon_price;
                    } else {
                        $tem_all = sprintf("%.0f", ($playamount / $order_price) * $request['all_coupon_price']);
                        if ($tem_all > $playamount) {
                            $tem_all = $playamount;
                        }
                        $all_coupon_price -= $tem_all;
                    }
                    foreach ($all_coupon_list as $list_one) {
                        $OrderHandleModel->useCoupons($request['student_id'], $list_one['coupon_id']);
                        $couponsOne = $this->DataControl->getFieldOne('smc_student_coupons', 'coupons_pid,coupons_type', "coupons_id ='{$list_one['coupon_id']}' and  student_id='{$request['student_id']}'");

                        $OrderHandleModel->addOrderCouponsLog($order_pid, 0, $goodsOne['goods_id'], $couponsOne['coupons_pid'], $tem_all);
                    }

                    $couponsOnePrice += (int)$tem_all;
                }

                $OrderHandleModel->orderCourseOrdinaryGoods($goodsOne['goods_id'], $goodsOne['sellingprice'], $goodsOne['num'], $couponsOnePrice, $goodsOne['sellgoods_id']);
                //领用商品
                $OrderHandleModel->receiveGoods($goodsOne['goods_id'], $request['student_id'], $goodsOne['num'], 0, $out_pid, 0, strtotime($request['create_time']));
            }
        }

        if ($from == 1) {
            $data = array();
            $data['order_pid'] = $order_pid;
            $data['is_adopt'] = '1';
            $data['reason'] = '自动审核';

            $OrderModel = new \Model\Gmc\OrderModel($request);
            $OrderModel->examineRenewOrder($data);
        }
        return $order_pid;
    }
}