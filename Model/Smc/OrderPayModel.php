<?php


namespace Model\Smc;

class OrderPayModel extends OrderModel
{
    function __construct($publicarray = array(), $order_pid = 0)
    {
        parent::__construct($publicarray, $order_pid);
    }


    //查询订单信息
    function stuOrderOne()
    {
        $sql = "select po.trading_pid,po.order_pid, po.order_createtime,po.order_allprice,po.order_market_price,po.order_paymentprice,po.coursepacks_id,po.student_id
,s.student_cnname,s.student_enname,s.student_branch,po.order_taglist,po.order_paidprice,b.student_balance,s.student_forwardprice, po.order_coupon_price,po.order_type,po.order_status,c.companies_cnname,c.companies_supervisebank,c.companies_agencyid,c.companies_superviseaccount,c.companies_issupervise,c.companies_supervisetime,c.companies_signet,
			  (select sum(pay_price) from  smc_payfee_order_pay as op  where  (op.paytype_code='balance' or op.paytype_code='norebalance' )and  op.order_pid=po.order_pid and op.pay_issuccess=1 ) as order_balance_price,
			   (select sum(pay_price) from  smc_payfee_order_pay as op  where  op.paytype_code='forward' and  op.order_pid=po.order_pid and op.pay_issuccess=1 ) as order_forward_price
			  from smc_payfee_order as po
			  left join smc_student as s on po.student_id=s.student_id
			  left join smc_student_balance as b on b.student_id = s.student_id and b.school_id = po.school_id 
			  left join gmc_code_companies as c on po.companies_id = c.companies_id 
			  WHERE po.order_pid = '{$this->payfeeorderOne['order_pid']}'  and  po.student_id > 0";

        $orderOne = $this->DataControl->selectOne($sql);
//        if($orderOne['companies_superviseaccount']){
//            $orderOne['companies_superviseaccount'] = substr_replace($orderOne['companies_superviseaccount'], '****', 5, 4);
//        }

//        ,
//        (select u.course_issupervise from smc_payfee_order_course as oc,smc_course as u where oc.order_pid = po.order_pid and oc.course_id = u.course_id order by u.course_issupervise DESC limit 0,1 ) as course_issupervise

//        if($orderOne['course_issupervise'] == '1' && $orderOne['companies_issupervise'] == '1' && $orderOne['companies_issupervise'] > time()){
//            $orderOne['companies_issupervise'] = '1';
//        }else{
//            $orderOne['companies_issupervise'] = '0';
//        }


        $companyinfo = $this->DataControl->selectOne("select company_issupportpos,company_issupportptc,company_issupportqr,company_issupportcash from gmc_company WHERE company_id = '{$this->payfeeorderOne['company_id']}' ");

        if ($orderOne) {
            $orderOne['order_allprice'] = sprintf("%01.2f", $orderOne['order_allprice']);
            $orderOne['order_createtime'] = date('Y-m-d H:i:s', $orderOne['order_createtime']);
            $orderOne['order_surplusprice'] = sprintf("%01.2f", ($orderOne['order_paymentprice'] - $orderOne['order_paidprice']));
            $orderOne['order_forward_price'] = sprintf("%01.2f", $orderOne['order_forward_price']);
            $orderOne['order_balance_price'] = sprintf("%01.2f", $orderOne['order_balance_price']);
            $orderOne['order_paidprice'] = sprintf("%01.2f", $orderOne['order_paidprice']);
            $orderOne['order_coupon_price'] = sprintf("%01.2f", $orderOne['order_coupon_price'] + $orderOne['order_market_price']);
            $orderOne['order_activity_price'] = sprintf("%01.2f", $orderOne['order_allprice'] - $orderOne['order_coupon_price']);

//			if($from=="app"){
//				$orderOne['order_allprice'] =sprintf("%01.2f", $orderOne['order_allprice'])/10000;
//				$orderOne['order_surplusprice'] = sprintf("%01.2f",($orderOne['order_paymentprice'] - $orderOne['order_paidprice']))/10000;
//				$orderOne['order_forward_price'] = sprintf("%01.2f",$orderOne['order_forward_price'])/10000;
//				$orderOne['order_balance_price'] = sprintf("%01.2f",$orderOne['order_balance_price'])/10000;
//				$orderOne['order_paidprice'] = sprintf("%01.2f",$orderOne['order_paidprice'])/10000;
//				$orderOne['order_coupon_price']  = sprintf("%01.2f",$orderOne['order_coupon_price'] + $orderOne['order_market_price'])/10000;
//			}

            $createtime = time();
            $balanceLogOne = $this->DataControl->selectOne("SELECT coursecatbalance_figure-IFNULL((
                    SELECT SUM(CASE WHEN log_playclass = '+' THEN l.log_playamount ELSE - log_playamount END )
                    FROM smc_student_coursecatbalance_log AS l WHERE l.log_time >'{$createtime}' AND l.student_id = M.student_id
                    AND l.school_id = M.school_id AND l.coursetype_id='{$this->payfeeorderOne['coursetype_id']}' and l.feetype_code='Deposit'),0) AS log_finalamount
                    FROM smc_student_coursecatbalance M
                    WHERE 1
                    AND student_id = '{$this->payfeeorderOne['student_id']}'
                    AND school_id = '{$this->school_id}' 
                    and M.coursetype_id='{$this->payfeeorderOne['coursetype_id']}' 
                    and M.coursecat_id='{$this->payfeeorderOne['coursecat_id']}' 
                    and M.feetype_code='Deposit'
                    ");

            //充值类订单
            if ($orderOne['order_type'] == 2) {
                $orderOne['order_surplusprice'] = sprintf("%01.2f", $orderOne['order_paymentprice'] - $orderOne['order_paidprice']);  //剩余支付金额
                $orderOne['order_balance_price'] = '0.00';     //余额抵扣
                $orderOne['order_forward_price'] = '0.00';   //结转余额抵扣
            }

            $order_protocol = $this->DataControl->selectOne("select po.order_type,c.school_payaftersignlimit
                ,(select count(1) from smc_student_protocol where student_id=po.student_id and order_pid=po.order_pid and protocol_isdel=0) as protocol_num
                ,(select count(1) from smc_student_protocol where student_id=po.student_id and order_pid=po.order_pid and protocol_isdel=0 and protocol_issign=1) as protocol_sign_num 
                from smc_payfee_order as po 
                left join smc_school  as c on po.school_id=c.school_id
                where po.order_pid = '{$this->payfeeorderOne['order_pid']}'");
            if ($order_protocol && $order_protocol['school_payaftersignlimit'] == 1) {
                if ($order_protocol['order_type'] == 0 && ($order_protocol['protocol_num'] == 0 || $order_protocol['protocol_num'] !== $order_protocol['protocol_sign_num'])) {
                    $order['all_sign'] = 0;
                } else {
                    $order['all_sign'] = 1;
                }
            } else {
                $order['all_sign'] = 1;
            }

            $order['list'] = $orderOne;
            $order['deposit_price'] = $balanceLogOne ? $balanceLogOne['log_finalamount'] : 0;
            $order['companyinfo'] = $companyinfo;
            $order['order_goods'] = $this->OrderItemList();
            return $order;
        } else {
            return array();
        }
    }

    //获取订单商品明细
    function OrderItemList()
    {
        $orderCourseList = $this->DataControl->selectClear("select pc.pricing_id,c.course_cnname,c.course_branch,c.course_presentednums,pc.ordercourse_buynums,pc.ordercourse_unitprice,pc.ordercourse_totalprice,fp.tuition_sellingprice,fp.tuition_originalprice from  smc_payfee_order_course as pc
 				Left JOIN  smc_course as c ON c.course_id = pc.course_id
 				LEFT  JOIN  smc_fee_pricing_tuition as fp ON fp.pricing_id = pc.pricing_id
 				where pc.order_pid ='{$this->payfeeorderOne['order_pid']}'");

        $orderData = array();
        if ($orderCourseList) {
            foreach ($orderCourseList as $orderCourse) {
                $orderDataOne = array();
                $orderDataOne['item_name'] = $orderCourse['course_cnname'];
                $orderDataOne['course_branch'] = $orderCourse['course_branch'];
                $orderDataOne['items_donatepiece'] = $orderCourse['course_presentednums'];
                $orderDataOne['item_buynums'] = $orderCourse['ordercourse_buynums'];
                $orderDataOne['item_reprice'] = $orderCourse['tuition_originalprice'];
                $orderDataOne['items_sellingprice'] = $orderCourse['ordercourse_unitprice'];
                $orderDataOne['item_totalprice'] = $orderCourse['ordercourse_totalprice']; // 订单总价


                $orderGoods = $this->DataControl->selectClear("select pog.goods_id,pog.ordergoods_buynums,pog.ordergoods_unitprice,pog.ordergoods_totalprice,g.goods_cnname,pp.products_donatepiece,pp.products_isfree,pp.products_originalprice,pp.products_unitprice,
(select erpgoods_isreceive from smc_student_erpgoods as sed  where pog.order_pid = sed.order_pid and sed.goods_id =pog.goods_id  limit 0,1 ) as erpgoods_isreceive
                   from  smc_payfee_order_goods  as pog
					left join smc_fee_pricing_products as pp ON pp.pricing_id =pog.pricing_id  and pp.goods_id = pog.goods_id
					left join erp_goods as g  on  g.goods_id=pog.goods_id
					where pog.order_pid ='{$this->payfeeorderOne['order_pid']}' and pog.pricing_id ='{$orderCourse['pricing_id']}'  ");

                $goodsData = array();
                if ($orderGoods) {
                    foreach ($orderGoods as $orderGoodsOne) {
                        $goodsDataOne = array();
                        $goodsDataOne['item_name'] = $orderGoodsOne['goods_cnname'];
                        $goodsDataOne['items_donatepiece'] = $orderGoodsOne['products_donatepiece']; //赠送
                        $goodsDataOne['item_buynums'] = $orderGoodsOne['ordergoods_buynums'];
                        $goodsDataOne['item_reprice'] = round($orderGoodsOne['products_originalprice'] * $orderGoodsOne['ordergoods_buynums'], 2);
                        $goodsDataOne['items_sellingprice'] = round($orderGoodsOne['products_unitprice'], 2); //
                        $goodsDataOne['products_isfree'] = round($orderGoodsOne['products_isfree'], 2); //
                        $goodsDataOne['erpgoods_isreceive'] = $orderGoodsOne['erpgoods_isreceive'] == 1 ? '1' : "0"; //

                        if ($orderGoodsOne['ordergoods_unitprice']) {
                            $goodsDataOne['item_totalprice'] = ceil($orderGoodsOne['ordergoods_unitprice'] * $orderGoodsOne['ordergoods_buynums']);
                        } else {
                            $goodsDataOne['item_totalprice'] = '0.00';
                        }
                        $goodsData[] = $goodsDataOne;
                    }
                }

                $orderDataOne['goods'] = $goodsData;
                $orderItems = $this->DataControl->selectClear("select cf.feeitem_id,cf.feeitem_cnname,poi.item_buynums,poi.item_unitprice,poi.item_totalprice,cf.feeitem_price
                   from  smc_payfee_order_item  as poi
					left join smc_fee_pricing_items as pp ON pp.items_id =poi.items_id
					left join smc_code_feeitem as cf ON cf.feeitem_branch = poi.feeitem_branch and cf.company_id='{$this->payfeeorderOne['company_id']}'
					where poi.order_pid ='{$this->payfeeorderOne['order_pid']}' and pp.pricing_id ='{$orderCourse['pricing_id']}'  ");;

                $itemsData = array();
                if ($orderItems) {
                    foreach ($orderItems as $orderItemsOne) {
                        $itemsDataOne = array();
                        $itemsDataOne['item_name'] = $orderItemsOne['feeitem_cnname'];
                        $itemsDataOne['items_donatepiece'] = '0';
                        $itemsDataOne['item_buynums'] = $orderItemsOne['item_buynums'];
                        $itemsDataOne['item_reprice'] = round($orderItemsOne['item_unitprice'] * $orderItemsOne['item_buynums'], 2);
                        $itemsDataOne['items_sellingprice'] = round($orderItemsOne['item_unitprice'], 2); //
//						$itemsDataOne['products_isfree'] = round($orderItemsOne['products_isfree'],2) ; //
//						$itemsDataOne['erpgoods_isreceive'] = $orderItemsOne['erpgoods_isreceive'] ==1?'1':"0"; //

                        $itemsDataOne['item_totalprice'] = round($orderItemsOne['item_totalprice'], 2);

                        $itemsData[] = $itemsDataOne;
                    }
                }

                $orderDataOne['items'] = $itemsData;
                $orderData[] = $orderDataOne;
            }
            $order['course'] = $orderData;

        } else {
            $order['course'] = array();
            $itemList = $this->DataControl->selectClear("select g.goods_cnname,og.ordergoods_buynums,og.ordergoods_unitprice,og.ordergoods_totalprice
        	from smc_payfee_order_goods as og
        	left JOIN  erp_goods as g ON g.goods_id = og.goods_id
        	where  og.order_pid ='{$this->payfeeorderOne['order_pid']} 'and g.company_id='{$this->company_id}'");

            $itemData = array();
            if ($itemList) {
                foreach ($itemList as $itemOne) {
                    $itemDateOne = array();
                    $itemDateOne['item_name'] = $itemOne['goods_cnname'];
                    $itemDateOne['items_donatepiece'] = 0;
                    $itemDateOne['item_buynums'] = $itemOne['ordergoods_buynums'];
                    $itemDateOne['item_reprice'] = round($itemOne['ordergoods_unitprice'] * $itemOne['ordergoods_buynums'], 2);
                    $itemDateOne['items_sellingprice'] = round($itemOne['ordergoods_unitprice'], 2); //销售单价
                    $itemDateOne['item_totalprice'] = round($itemOne['ordergoods_totalprice'], 2); // 订单总价
                    $itemData[] = $itemDateOne;
                }
                $order['items'] = $itemData;
            } else {
                $order['items'] = array();
            }

            //普通杂费
            $itemList = $this->DataControl->selectClear("select oi.item_buynums,oi.item_unitprice,oi.item_totalprice,cf.feeitem_cnname from  smc_payfee_order_item as oi
 left join smc_code_feeitem as cf ON cf.feeitem_branch = oi.feeitem_branch
where oi.order_pid='{$this->payfeeorderOne['order_pid']}' and cf.company_id='{$this->company_id}'");
            $itemData = array();
            if ($itemList) {
                foreach ($itemList as $itemOne) {
                    $itemDateOne = array();
                    $itemDateOne['item_name'] = $itemOne['feeitem_cnname'];
                    $itemDateOne['items_donatepiece'] = '0';
                    $itemDateOne['item_buynums'] = $itemOne['item_buynums'];
                    $itemDateOne['item_reprice'] = round($itemOne['item_unitprice'] * $itemOne['item_buynums'], 2);
                    $itemDateOne['items_sellingprice'] = round($itemOne['item_unitprice'], 2); //销售单价
                    $itemDateOne['item_totalprice'] = round($itemOne['item_totalprice'], 2); // 订单总价
                    $itemData[] = $itemDateOne;
                }
                $order['common_items'] = $itemData;
            } else {
                $order['common_items'] = array();
            }
        }

        return $order;
    }


    //获取订单的支付记录
    function OrderPayList($request, $app_school_id, $pay_status, $from)
    {
        $dataWhere = 1;
        $data1['order_surplusprice'] = sprintf("%01.2f", $this->payfeeorderOne['order_paymentprice'] - $this->payfeeorderOne['order_paidprice']);
        $data1['order_paymentprice'] = sprintf("%01.2f", $this->payfeeorderOne['order_paymentprice']);
        $data1['order_paidprice'] = sprintf("%01.2f", $this->payfeeorderOne['order_paidprice']);
//
//		if($from=='app'){
//			$data1['order_surplusprice'] = sprintf("%01.2f",$this->payfeeorderOne['order_paymentprice'] - $this->payfeeorderOne['order_paidprice'])/10000;
//			$data1['order_paymentprice'] = sprintf("%01.2f",$this->payfeeorderOne['order_paymentprice'])/10000;
//			$data1['order_paidprice'] =sprintf("%01.2f",$this->payfeeorderOne['order_paidprice'])/10000 ;
//		}

        $data['order'] = $data1;

        if (isset($pay_status) and $pay_status != "") {
            $dataWhere .= " and  op.pay_issuccess ='{$pay_status}'  ";
        }

        if (isset($request['keyword']) and $request['keyword'] != "") {
            $dataWhere .= " and  (op.order_pid  like '%{$request['keyword']}%' or s.student_cnname  like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%')";
        }

//		if(isset($request['pay_type']) and $request['pay_type']!= "")
//		{
//
//            $pay_type = urldecode($request['pay_type']);
//            $paytype_code = json_decode($pay_type,TRUE);
//
//			$arr_paytype_code = array();
//			 if($paytype_code){
//				 foreach($paytype_code as $key => $val){
//				 	 $arr_paytype_code[] = $val['pay_code'];
//				 }
//			 }
//
//			  if($arr_paytype_code){
//				  $datawhere_type = " and ";
//				  for ($i = 0; $i < count($arr_paytype_code); $i++) {
//
//					  if ($i == (count($arr_paytype_code) - 1)) {
//					  $datawhere_type .= "  (op.paytype_code ='{$arr_paytype_code[$i]}') ";
//					  } else {
//					  	$datawhere_type .= "  (op.paytype_code ='{$arr_paytype_code[$i]}') or ";
//				      }
//			 	 }
//				  $dataWhere .= $datawhere_type;
//			 }
//
//		}

        if ($request['pay_alipay'] != "" || $request['pay_wechat'] != "" || $request['pay_bankcard'] != "") {
            $data_where = " and ";
            if (isset($request['pay_alipay']) and $request['pay_alipay'] != "") {
                $data_where .= " ( op.paytype_code='{$request['pay_alipay']}' ";
            } else {
                $data_where .= "(  op.paytype_code='1'";
            }
            if (isset($request['pay_wechat']) and $request['pay_wechat'] != "") {
                $data_where .= "  or  op.paytype_code='{$request['pay_wechat']}' ";
            } else {
                $data_where .= "or op.paytype_code='2'";
            }
            if (isset($request['pay_bankcard']) and $request['pay_bankcard'] != "") {
                $data_where .= "  or  op.paytype_code='{$request['pay_bankcard']})' ";
            } else {
                $data_where .= " or op.paytype_code='3') ";
            }
            $dataWhere .= $data_where;
        }

        if (isset($request['starttime']) and $request['endtime'] != "") {
            $starttime = strtotime($request['starttime']);
            $endtime = strtotime($request['endtime']);
            $dataWhere .= " and op.pay_createtime >= '{$starttime}' and op.pay_createtime <= '{$endtime}'";
        }

        if (isset($app_school_id) and $app_school_id != "") {
            $payList = $this->DataControl->selectClear("select op.pay_id,op.pay_pid,op.paytype_code,op.pay_typename,op.pay_successtime,op.pay_issuccess, op.pay_price,po.order_pid,s.student_cnname,s.student_enname,pop.paylog_tradeno,pop.paylog_img,op.pay_isrefund,op.paytype_code,po.order_from,op.pay_type,co.companies_signet
				from  smc_payfee_order_pay as op
				left join smc_payfee_order_paylog as pop ON pop.order_pid =op.order_pid and op.pay_pid = pop.pay_pid
				left join smc_payfee_order as po ON po.order_pid =op.order_pid
				left join smc_student as s ON po.student_id = s.student_id
                left join gmc_code_companies as co on co.companies_id = po.companies_id
				 where {$dataWhere} and po.company_id='{$this->company_id}' and  po.school_id='{$this->school_id}' order by op.pay_createtime DESC ");
        } else {
            $payList = $this->DataControl->selectClear("select op.pay_id,op.pay_pid,op.paytype_code,op.pay_typename,op.pay_successtime,op.pay_issuccess, op.pay_price,po.order_pid,s.student_cnname,s.student_enname,pop.paylog_tradeno,op.pay_isrefund,op.pay_note,op.pay_outnumber,op.paytype_code,pop.paylog_img,po.order_from,op.pay_type,co.companies_signet
				from  smc_payfee_order_pay as op
				left join smc_payfee_order_paylog as pop ON pop.order_pid =op.order_pid and op.pay_pid = pop.pay_pid
				left join smc_payfee_order as po ON  po.order_pid =op.order_pid
				left join smc_student as s ON po.student_id = s.student_id
                left join gmc_code_companies as co on co.companies_id = po.companies_id
				where {$dataWhere} and  op.order_pid='{$this->payfeeorderOne['order_pid']}' order by op.pay_createtime DESC ");

        }

        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_branch", "student_id='{$this->payfeeorderOne['student_id']}'");
        $schoolOne = $this->DataControl->getOne("smc_school", "school_id='{$this->payfeeorderOne['school_id']}'");

        if ($payList) {
            foreach ($payList as &$payOne) {

                if($this->school_id!='1175' && $this->school_id!='1199'){
                    
                    if ($payOne['paytype_code'] == 'parent') {
                        $payurl = "https://{$this->payfeeorderOne['company_id']}.scshop.kedingdang.com/Shop/payMethod?paypid={$payOne['pay_pid']}";
                        $payOne['payqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimg?imgurl=" . base64_encode($payurl);
                    }
                    elseif ($payOne['paytype_code'] == 'qrcode') {
    
                        $paycompaniesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_chargchannel", "companies_id='{$this->payfeeorderOne['companies_id']}'");
    
                        if($paycompaniesOne['companies_chargchannel'] == 'cmbbank'){
                            $payOne['payqrcode'] = "https://scshopapi.kedingdang.com/BoingPay/OrderPay?paypid={$payOne['pay_pid']}&paymenttype=ewmpay";
                        }elseif($paycompaniesOne['companies_chargchannel'] == 'cmbheadbank'){
                            $payOne['payqrcode'] = "https://scshopapi.kedingdang.com/HeadBoingPay/OrderPay?paypid={$payOne['pay_pid']}&paymenttype=ewmpay";
                        }elseif($paycompaniesOne['companies_chargchannel'] == 'cmbmergebank'){
                            $payOne['payqrcode'] = "https://scshopapi.kedingdang.com/MergeBoingPay/OrderPay?paypid={$payOne['pay_pid']}&paymenttype=ewmpay";
                        }
                    }
                    elseif ($payOne['paytype_code'] == 'pos') {
                        $ewmCodeArray = array();
                        $ewmCodeArray['orderNo'] = $payOne['pay_pid'];
                        $ewmCodeArray['paymentprice'] = $payOne['pay_price'];
                        $ewmCodeArray['school_id'] = $schoolOne['school_id'];
                        $ewmCodeArray['school_cnname'] = $schoolOne['school_cnname'];
                        $ewmCodeArray['tokencode'] = $schoolOne['school_id'] . $schoolOne['school_branch'];
                        $ewmCodeArray['cnname'] = $studentOne['student_cnname'];
                        $ewmCodeArray['enname'] = $studentOne['student_enname'];
                        $ewmCodeArray['branch'] = $studentOne['student_branch'];
                        $ewmCodeString = json_encode($ewmCodeArray, JSON_UNESCAPED_UNICODE);
    
                        $payOne['payqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($ewmCodeString));
    
                    }
                    else {
                        $payOne['payqrcode'] = '';
                    }

                }else{

                    // 生成支付链接
                    $payUrl = "https://faceentry.kedingdang.com/Pay/ChargeChannelLoading?order_pid=".$this->payfeeorderOne['order_pid'];
        
                    // 生成二维码图片（返回base64编码）
                    // $qrcodeBase64 = $this->generateQRCode($payUrl, 'base64', 8, 'H');
      
                    $payOne['payqrcode']='';
                    $payOne['pay_url']=$payUrl;
                    // $payOne['payqrcode']="https://smcapi.kedingdang.com/OrderPay/urlshowimg?imgurl=" . $payUrl;
                }
                
                
                $payOne['pay_typename'] = $this->LgStringSwitch($payOne['pay_typename']);

//				if($from=='app'){
//
//
//					$payOne['pay_price'] = $payOne['pay_price']/10000;
//				}

                if ($payOne['pay_successtime']) {
                    $payOne['pay_successtime'] = date('Y-m-d H:i:s', $payOne['pay_successtime']);
                } else {
                    $payOne['pay_successtime'] = "--";
                }

                if ($payOne['paytype_code'] == "feewaiver") {
                    if ($payOne['pay_issuccess'] == 0) {
                        $payOne['pay_status'] = 0;
                        $payOne['pay_issuccess_name'] = $this->LgStringSwitch("待审核");
                    } elseif ($payOne['pay_issuccess'] == 1) {
                        $payOne['pay_status'] = 1;
                        $payOne['pay_issuccess_name'] = $this->LgStringSwitch("已完成");
                    } elseif ($payOne['pay_issuccess'] == -1) {
                        $payOne['pay_issuccess_name'] = $this->LgStringSwitch("已拒绝");
                        $payOne['pay_status'] = -1;
                    }
                } else {
                    if ($payOne['pay_issuccess'] == 0) {
                        $payOne['pay_status'] = 0;
                        $payOne['pay_issuccess_name'] = $this->LgStringSwitch("未支付");
                    } elseif ($payOne['pay_issuccess'] == 1) {
                        $payOne['pay_status'] = 1;
                        $payOne['pay_issuccess_name'] = $this->LgStringSwitch("已支付");
                    } elseif ($payOne['pay_issuccess'] == -1) {
                        $payOne['pay_issuccess_name'] = $this->LgStringSwitch("已失效");
                        $payOne['pay_status'] = -1;
                    }
                }


            }
            $data['list'] = $payList;
        }
        return $data;
    }


    //支付
    function createOrderPay($payArray, $balanceArray = array())
    {
//        if (isset($payArray['create_time']) && $payArray['create_time'] <> '') {
//            $payArray['create_time'] = date('Y-m-d', strtotime($payArray['create_time']) + 24 * 60 * 60 - 1);
//        }

        if ($this->payfeeorderOne['order_status'] == 0) {
            $this->error = 1;
            $this->errortip = "订单审核中不可支付";
            return false;
        }

        if ($this->payfeeorderOne['order_type'] == 2 && $payArray['paytimes'] == 0) {
            $this->error = 1;
            $this->errortip = "充值订单不支持分批支付";
            return false;
        }
        $stublcOne = $this->getStuBalance($this->payfeeorderOne['student_id'],$this->company_id,$this->school_id,$this->payfeeorderOne['companies_id']);


        //$student = $this->DataControl->getOne('smc_student', "student_id='{$this->payfeeorderOne['student_id']}'");
        $orderHandleModel = new OrderHandleModel($this->publicarray, $this->payfeeorderOne['order_pid']);
        if (!$stublcOne) {
            $this->error = 1;
            $this->errortip = "学员数据不存在";
            return false;
        }
        //一次性支付
        $surplusPrice = $orderHandleModel->surplusPrice();
        $pay_price = "0.00";
        if ($payArray['paytimes'] == 1) {
            $pay_price = $surplusPrice;
            if ($pay_price <= 0) {
                $this->error = 1;
                $this->errortip = "您存在待支付订单或者该订单已完成";
                return false;
            }
        } elseif ($payArray['paytimes'] == 0) {
            if ($payArray['pay_price'] > $surplusPrice) {
                $this->error = 1;
                $this->errortip = "您输入的交易金额大于可生成交易的金额，最大金额不得超过{$surplusPrice}元";
                return false;
            } else {
                $pay_price = $payArray['pay_price'];
            }
        } elseif ($payArray['paytimes'] == 2) {
            $pay_price = $surplusPrice;
        }

        if ($pay_price <= 0) {
            $this->error = 1;
            $this->errortip = "请输入大于0的支付金额";
            return false;
        }

        $data['pay_price'] = sprintf("%01.2f", $pay_price);

//		if(isset($payArray['from']) && $payArray['from'] =="app" )
//		{
//			$data['pay_price'] = sprintf("%01.2f", $data['pay_price']/10000);
//		}
        $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $this->payfeeorderOne['order_pid']);
        $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
        $balanceapply_id=0;
        if ($payArray['paytype'] == 'balance') {
            if ($balanceArray) {
                $stublcOne['student_balance'] = $balanceArray['allBalance'];
                $stublcOne['student_withholdbalance'] = $balanceArray['allWithhold'];
            } else {

                $endcalcModel = new \Model\Api\ClassendcalcModel();

                $endcalcModel->fillCourseTypeBySchoolStudent($this->school_id,$this->payfeeorderOne['student_id']);

                $studentOne=$this->DataControl->getFieldOne("smc_student","student_branch","student_id='{$this->payfeeorderOne['student_id']}'");

                if($studentOne['student_branch']>='20220101000001'){
                    if($this->DataControl->getFieldOne("smc_student_registerinfo","info_id","company_id='{$this->company_id}' and student_id='{$this->payfeeorderOne['student_id']}' and coursetype_id='{$this->payfeeorderOne['coursetype_id']}' and info_status=1")){

                        $sql = "select a.coursetype_id 
                            from smc_student_balancelog as a
                            left join smc_student_trading as b on a.trading_pid=b.trading_pid
                            where a.company_id='{$this->company_id}' and a.school_id='{$this->payfeeorderOne['school_id']}' and a.student_id='{$this->payfeeorderOne['student_id']}' and b.tradingtype_code='CourseForward' and a.coursetype_id<>'{$this->payfeeorderOne['coursetype_id']}' and a.coursetype_id>0 and a.balancelog_playclass='+'
                            order by a.balancelog_time desc
                            limit 0,1
                            ";

                        if($this->DataControl->selectOne($sql)){
                            $sql = "select a.log_id 
                                from smc_student_coursebalance_log as a
                                left join smc_course as b on a.course_id=b.course_id
                                where a.school_id='{$this->payfeeorderOne['school_id']}' and a.student_id='{$this->payfeeorderOne['student_id']}' and b.coursetype_id<>'{$this->payfeeorderOne['coursetype_id']}' and a.hourstudy_id>0 and a.log_playamount>0 limit 0,1";

                            if(!$this->DataControl->selectOne($sql)){
                                $sql = "select balancelog_playclass,balancelog_playamount 
                        from smc_student_balancelog 
                        where company_id='{$this->company_id}' and student_id='{$this->payfeeorderOne['student_id']}' and coursetype_id='{$this->payfeeorderOne['coursetype_id']}' ";

                                $logList=$this->DataControl->selectClear($sql);

                                if($logList){
                                    $courseTypeBalance=0;
                                    foreach($logList as $logOne){
                                        if($logOne['balancelog_playclass']=='+'){
                                            $courseTypeBalance+=$logOne['balancelog_playamount'];
                                        }else{
                                            $courseTypeBalance-=$logOne['balancelog_playamount'];
                                        }
                                    }

                                    $sql = "select a.balanceapply_id
                            from smc_student_balanceapply as a
                            where a.student_id='{$this->payfeeorderOne['student_id']}' and a.school_id='{$this->payfeeorderOne['school_id']}' and a.balanceapply_status=1 and a.balanceapply_usestatus=0 and a.coursetype_id='{$this->payfeeorderOne['coursetype_id']}'
                            order by a.balanceapply_examinetime asc
                            limit 0,1
                            ";

                                    if($courseTypeBalance<$pay_price){

                                        $applyOne=$this->DataControl->selectOne($sql);
                                        if($applyOne){
                                            $balanceapply_id=$applyOne['balanceapply_id'];
                                        }else{
                                            $this->error = 3;
                                            $this->errortip = "结转余额支付金额不足,是否解除限制?";
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    $sql = "select a.balanceapply_id
                            from smc_student_balanceapply as a
                            where a.student_id='{$this->payfeeorderOne['student_id']}' and a.school_id='{$this->payfeeorderOne['school_id']}' and a.balanceapply_status=1 and a.balanceapply_usestatus=0 and a.coursetype_id='{$this->payfeeorderOne['coursetype_id']}'
                            order by a.balanceapply_examinetime asc
                            limit 0,1
                            ";

                                    $applyOne=$this->DataControl->selectOne($sql);
                                    if($applyOne){
                                        $balanceapply_id=$applyOne['balanceapply_id'];
                                    }else{
                                        $this->error = 3;
                                        $this->errortip = "结转余额支付金额不足,是否解除限制?";
                                        return false;
                                    }
                                }
                            }

                        }
                    }
                    else{


                        $sql = "select balancelog_playclass,balancelog_playamount 
                        from smc_student_balancelog 
                        where company_id='{$this->company_id}' and student_id='{$this->payfeeorderOne['student_id']}' and coursetype_id='{$this->payfeeorderOne['coursetype_id']}' ";

                        $logList=$this->DataControl->selectClear($sql);

                        if($logList){
                            $courseTypeBalance=0;
                            foreach($logList as $logOne){
                                if($logOne['balancelog_playclass']=='+'){
                                    $courseTypeBalance+=$logOne['balancelog_playamount'];
                                }else{
                                    $courseTypeBalance-=$logOne['balancelog_playamount'];
                                }
                            }

                            $sql = "select a.balanceapply_id
                            from smc_student_balanceapply as a
                            where a.student_id='{$this->payfeeorderOne['student_id']}' and a.school_id='{$this->payfeeorderOne['school_id']}' and a.balanceapply_status=1 and a.balanceapply_usestatus=0 and a.coursetype_id='{$this->payfeeorderOne['coursetype_id']}'
                            order by a.balanceapply_examinetime asc
                            limit 0,1
                            ";

                            if($courseTypeBalance<$pay_price){

                                $applyOne=$this->DataControl->selectOne($sql);
                                if($applyOne){
                                    $balanceapply_id=$applyOne['balanceapply_id'];
                                }else{
                                    $this->error = 3;
                                    $this->errortip = "结转余额支付金额不足,是否解除限制?";
                                    return false;
                                }
                            }
                        }
                        else{
                            $sql = "select a.balanceapply_id
                            from smc_student_balanceapply as a
                            where a.student_id='{$this->payfeeorderOne['student_id']}' and a.school_id='{$this->payfeeorderOne['school_id']}' and a.balanceapply_status=1 and a.balanceapply_usestatus=0 and a.coursetype_id='{$this->payfeeorderOne['coursetype_id']}'
                            order by a.balanceapply_examinetime asc
                            limit 0,1
                            ";

                            $applyOne=$this->DataControl->selectOne($sql);
                            if($applyOne){
                                $balanceapply_id=$applyOne['balanceapply_id'];
                            }else{
                                $this->error = 3;
                                $this->errortip = "结转余额支付金额不足,是否解除限制?";
                                return false;
                            }
                        }
                    }
                }

                if (isset($payArray['create_time']) && $payArray['create_time'] <> '') {
                    $createtime = strtotime($payArray['create_time']) + 24 * 60 * 60 - 1;
                    $balanceLogOne = $this->DataControl->selectOne("SELECT student_balance AS balancelog_finalamount
                    FROM smc_student_balance M
                    WHERE 1
                    AND student_id = '{$this->payfeeorderOne['student_id']}'
                    AND school_id = '{$this->school_id}' and M.companies_id='{$this->payfeeorderOne['companies_id']}'
                    ");
//                    -IFNULL((
//                    SELECT SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END )
//                    FROM smc_student_balancelog AS l WHERE l.balancelog_time >'{$createtime}' AND l.student_id = M.student_id
//                    AND l.school_id = M.school_id AND l.balancelog_class='0' and l.companies_id=M.companies_id),0)

                    $balanceLogTwo = $this->DataControl->selectOne("SELECT student_withholdbalance AS balancelog_finalamount
                    FROM smc_student_balance M
                    WHERE 1
                    AND student_id = '{$this->payfeeorderOne['student_id']}'
                    AND school_id = '{$this->school_id}' and M.companies_id='{$this->payfeeorderOne['companies_id']}' ");
//                    -IFNULL((
//                    SELECT SUM( CASE WHEN balancelog_playclass = '+' THEN l.balancelog_playamount ELSE - balancelog_playamount END )
//                    FROM smc_student_balancelog AS l WHERE l.balancelog_time >'{$createtime}'  AND l.student_id = M.student_id
//                    AND l.school_id = M.school_id AND l.balancelog_class='2' and l.companies_id=M.companies_id),0)

                    if (($balanceLogOne['balancelog_finalamount'] + $balanceLogTwo['balancelog_finalamount']) < $pay_price) {

                        $this->error = 1;
                        $this->errortip = "您的主体账户余额不足";
                        return false;
                    }
                }
            }

            $student_all_balance = $stublcOne['student_balance'] + $stublcOne['student_withholdbalance'];
            if (!$balanceArray) {
                if ($student_all_balance < $pay_price) {
                    $this->error = 1;
                    $this->errortip = "账户余额不足";
                    return false;
                }
            }

            if ($stublcOne['student_withholdbalance'] > '0') {
                if ($balanceArray) {
                    $paycode = 'coursebalance';
                } else {
                    $paycode = 'norebalance';
                }


                if ($stublcOne['student_withholdbalance'] >= $pay_price) {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户不可退金额'), '1', $note = $this->LgStringSwitch('不可退金额抵扣金额记录'), $paycode, $pay_price, $payArray['create_time']);
                    $OrderHandleModel->orderPaylog($paypid, $pay_price, '', 0, $this->LgStringSwitch('不可退金额抵扣金额记录'), $paycode, $payArray['create_time']);
                    $BalanceModel->reduceStuWithholdbalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $pay_price, '',$this->payfeeorderOne['companies_id'], strtotime($payArray['create_time']));
                    $pay_price = 0;
                } else {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户不可退金额'), '1', $note = $this->LgStringSwitch('不可退金额抵扣金额记录'), $paycode, $stublcOne['student_withholdbalance'], $payArray['create_time']);
                    $OrderHandleModel->orderPaylog($paypid, $stublcOne['student_withholdbalance'], '', 0, $this->LgStringSwitch('不可退金额抵扣金额记录'), $paycode, $payArray['create_time']);
                    $BalanceModel->reduceStuWithholdbalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $stublcOne['student_withholdbalance'], '',$this->payfeeorderOne['companies_id'], strtotime($payArray['create_time']));
                    $pay_price -= $stublcOne['student_withholdbalance'];
                }
            }
            if ($pay_price > '0' && $stublcOne['student_balance'] > '0') {
                if ($balanceArray) {
                    $paycode = 'coursebalance';
                } else {
                    $paycode = 'balance';
                }
                if ($stublcOne['student_balance'] >= $pay_price) {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣金额记录'), $paycode, $pay_price, $payArray['create_time']);
                    $OrderHandleModel->orderPaylog($paypid, $pay_price, '', 0, $this->LgStringSwitch('余额抵扣金额记录'), $paycode, $payArray['create_time']);
                    $BalanceModel->reduceStuBalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $pay_price, '',$this->payfeeorderOne['companies_id'], strtotime($payArray['create_time']));
                    $pay_price = 0;
                } else {
                    $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('账户余额'), '1', $note = $this->LgStringSwitch('余额抵扣金额记录'), $paycode, $stublcOne['student_balance'], $payArray['create_time']);
                    $OrderHandleModel->orderPaylog($paypid, $stublcOne['student_balance'], '', 0, $this->LgStringSwitch('余额抵扣金额记录'), $paycode, $payArray['create_time']);
                    $BalanceModel->reduceStuBalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $stublcOne['student_balance'], '',$this->payfeeorderOne['companies_id'], strtotime($payArray['create_time']));
                    $pay_price -= $stublcOne['student_balance'];
                }
            }

            if ($balanceArray && $pay_price > '0' && $balanceArray['student_catbalance'] > '0') {

                if ($balanceArray['student_catbalance'] >= $pay_price) {
                    $catbalance = $pay_price;
                } else {
                    $catbalance = $balanceArray['student_catbalance'];
                }
                $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('结转预收余额'), '1', $note = $this->LgStringSwitch('结转预收余额抵扣金额记录'), $paytype_code = "catdeposit", $catbalance, $payArray['create_time']);
                $OrderHandleModel->orderPaylog($paypid, $catbalance, '', 0, $this->LgStringSwitch('结转预收余额抵扣金额记录'), 'forward', $payArray['create_time']);
                $BalanceModel->reduceCatBalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $catbalance, $paypid, $balanceArray['coursecat_id'], 'forward', $this->LgStringSwitch('结转预收余额抵扣金额记录'), strtotime($payArray['create_time']), 0);

            }

            if($balanceapply_id>0){
                $data=array();
                $data['balanceapply_usestatus']=1;
                $data['order_pid']=$this->payfeeorderOne['order_pid'];
                $this->DataControl->updateData("smc_student_balanceapply","balanceapply_id='{$balanceapply_id}'",$data);
            }

            $this->error = 0;
            $this->errortip = "生成支付订单成功";
            return $data;

//				$pay_pid = $orderHandleModel->orderPay("余额支付", 0, $payArray['pay_note'], 'balance', $pay_price,$payArray['create_time']);
//				$data['pay_pid'] = $pay_pid;
//				if ($pay_pid) {
//					//产生交易记录
//					$balanceData = array();
//					$balanceData['company_id'] = $this->company_id;
//					$balanceData['school_id'] = $this->school_id;
//					$balanceData['stafer_id'] = $this->stafferOne['staffer_id'];
//					$balanceData['student_id'] = $this->payfeeorderOne['student_id'];
//					$balanceData['balancelog_class'] = 0;
//					$balanceData['balancelog_playname'] = "系统余额支付";
//					$balanceData['balancelog_playclass'] = "_";
//					$balanceData['balancelog_fromamount'] = $stublcOne['student_balance'];
//					$balanceData['balancelog_playamount'] = $pay_price;
//					$balanceData['balancelog_finalamount'] = $stublcOne['student_balance'] - $pay_price;
//					$balanceData['balancelog_reason'] = "系统余额支付";
//					$balanceData['balancelog_time'] = time();
//					$balancelog_id = $this->DataControl->insertData("smc_student_balancelog", $balanceData);
//					$paylog_tradeno = 'stubalacne' . $balancelog_id;
//					$this->orderPaylog($pay_pid, $paylog_tradeno, $ifee = 0, "余额支付",'',$payArray['create_time']);
//
//                    //结算账户余额
//                    $data = array();
//                    $data['student_balance'] = round($stublcOne['student_balance'] - $pay_price, 2);
//                    $this->DataControl->updateData("smc_student_balance","student_id='{$this->payfeeorderOne['student_id']}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}'",$data);
//
//					$this->error = 0;
//					$this->errortip = "生成支付订单成功";
//					return $data;
//				} else {
//					$this->error = 1;
//					$this->errortip = "生成支付订单失败";
//					return false;
//				}
        }
        elseif ($payArray['paytype'] == 'cash') {


            if($this->company_id==8888){
                $satfferOne=$this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_ismanage","staffer_id='{$this->staffer_id}'");

                if($satfferOne['staffer_ismanage']!=1){

                    $this->error = 1;
                    $this->errortip = "现金支付已关闭";
                    return $data;
                }
            }
            

            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('现金支付'), 0, $payArray['pay_note'], 'cash', $pay_price, $payArray['create_time'], 0, 'cash');
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
//                $orderHandleModel->updateOrderPay($pay_pid,1,'','');
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }
        }
        elseif ($payArray['paytype'] == 'alipay') {
            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('支付宝支付'), 0, $payArray['pay_note'], 'alipay', $pay_price, $payArray['create_time'], 0, 'cmb');
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
//				$orderHandleModel->updateOrderPay($pay_pid,1);
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }

        }
        elseif ($payArray['paytype'] == 'wechat') {
//
            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('微信支付'), 0, $payArray['pay_note'], 'wechat', $pay_price, $payArray['create_time'], 0, 'cmb');
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
//				$orderHandleModel->updateOrderPay($pay_pid,1);
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }
        }
        elseif ($payArray['paytype'] == 'bankcard') {

            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('银行卡'), 0, $payArray['pay_note'], 'parent', $pay_price, $payArray['create_time']);
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
//				$orderHandleModel->updateOrderPay($pay_pid,1);
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }
        }
        elseif ($payArray['paytype'] == 'parent') {
            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('发送家长端'), 0, $payArray['pay_note'], 'parent', $pay_price, $payArray['create_time']);
            $data['pay_pid'] = $pay_pid;

            $payurl = "https://{$this->payfeeorderOne['company_id']}.scshop.kedingdang.com/Shop/payMethod?paypid={$pay_pid}";
            $data['payqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimg?imgurl=" . base64_encode($payurl);

            //微信通知家长
            $schoolONE = $this->DataControl->selectOne("select school_shortname from smc_school WHERE school_id = '{$this->school_id}'");
            $stuONE = $this->DataControl->selectOne("select student_branch from smc_student WHERE student_id = '{$this->payfeeorderOne['student_id']}'");

            $firstnote = $this->LgStringSwitch("家长您好，您在我校交费的订单已产生，请及时通过微信支付");
            $keyword1 = $pay_pid;
            $keyword2 = $pay_price;
            $keyword3 = $this->LgStringSwitch("您在我校购买了课程或商品");
            $keyword4 = $schoolONE['school_shortname'];
            $keyword5 = $payArray['create_time'];
            $footernote = $this->LgStringSwitch("家长您好，您在我校交费的订单已产生，请及时通过微信支付，感谢您的使用！");
            $str = "branch={$stuONE['student_branch']}&firstnote={$firstnote}&keyword1={$keyword1}&keyword2={$keyword2}&keyword3={$keyword3}&keyword4={$keyword4}&keyword5={$keyword5}&footernote={$footernote}&companyid={$this->company_id}";
            request_by_curl("https://api.kidcastle.com.cn/Wechat/payOrderGoToParent", $str, "GET", array());


            if ($pay_pid) {
//				$orderHandleModel->updateOrderPay($pay_pid,1);
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }

        }
        elseif ($payArray['paytype'] == 'pos') {
            $pay_price = sprintf("%01.2f", $pay_price);
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "student_id", "order_pid='{$this->payfeeorderOne['order_pid']}'");

            if (!$orderOne) {
                $res = array('error' => 1, 'errortip' => '订单不存在', 'result' => array());
                ajax_return($res, $this->companyOne['company_language']);
            }


            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_branch", "student_id='{$orderOne['student_id']}'");
            if (!$studentOne) {
                $res = array('error' => 1, 'errortip' => '学员不存在', 'result' => array());
                ajax_return($res, $this->companyOne['company_language']);
            }

            $schoolOne = $this->DataControl->getOne("smc_school", "school_id='{$this->payfeeorderOne['school_id']}'");
            if (!$schoolOne) {
                $res = array('error' => 1, 'errortip' => '学校不存在', 'result' => array());
                ajax_return($res, $this->companyOne['company_language']);
            }
            $pay_pid = $orderHandleModel->orderPay('pos机', 0, $payArray['pay_note'], 'pos', $pay_price, $payArray['create_time'], 0, 'sdpos');
            $ewmCodeArray = array();
            $ewmCodeArray['orderNo'] = $pay_pid;
            $ewmCodeArray['paymentprice'] = $pay_price;
            $ewmCodeArray['school_id'] = $this->payfeeorderOne['school_id'];
            $ewmCodeArray['school_cnname'] = $schoolOne['school_cnname'];
            $ewmCodeArray['tokencode'] = $schoolOne['school_id'] . $schoolOne['school_branch'];
            $ewmCodeArray['cnname'] = $studentOne['student_cnname'];
            $ewmCodeArray['enname'] = $studentOne['student_enname'];
            $ewmCodeArray['branch'] = $studentOne['student_branch'];
            $ewmCodeString = json_encode($ewmCodeArray, JSON_UNESCAPED_UNICODE);

            $data['payqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($ewmCodeString));
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
//				$orderHandleModel->updateOrderPay($pay_pid,1);
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }

        }
        elseif ($payArray['paytype'] == 'norebalance') {
            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('不可退账户余额'), 0, $payArray['pay_note'], 'norebalance', $pay_price, $payArray['create_time']);
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
//				$orderHandleModel->updateOrderPay($pay_pid,1);
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }
        }
        elseif ($payArray['paytype'] == 'deposit') {
            $createtime = strtotime($payArray['create_time']) + 24 * 60 * 60 - 1;
            $balanceLogOne = $this->DataControl->selectOne("SELECT coursecatbalance_figure-IFNULL((
                    SELECT SUM( CASE WHEN log_playclass = '+' THEN l.log_playamount ELSE - log_playamount END )
                    FROM smc_student_coursecatbalance_log AS l WHERE l.log_time >'{$createtime}'  AND l.student_id = M.student_id
                    AND l.school_id = M.school_id AND l.coursetype_id='{$this->payfeeorderOne['coursetype_id']}' and l.feetype_code='Deposit'),0) AS log_finalamount
                    FROM smc_student_coursecatbalance M
                    WHERE 1
                    AND student_id = '{$this->payfeeorderOne['student_id']}'
                    AND school_id = '{$this->school_id}' 
                    and M.coursetype_id='{$this->payfeeorderOne['coursetype_id']}' 
                    and M.coursecat_id='{$this->payfeeorderOne['coursecat_id']}' 
                    and M.feetype_code='Deposit'
                    ");
            if ($balanceLogOne['log_finalamount'] <= 0) {
                $this->error = 1;
                $this->errortip = "剩余金额为0,不可支付";
                return false;
            }


            if ($balanceLogOne['log_finalamount'] < $pay_price) {
                $pay_price = $balanceLogOne['log_finalamount'];
            }

            $paypid = $OrderHandleModel->orderPay($this->LgStringSwitch('定金预收余额'), '1', $note = $this->LgStringSwitch('定金抵扣金额记录'), $paytype_code = "catdeposit", $pay_price, $payArray['create_time']);

            $OrderHandleModel->orderPaylog($paypid, $pay_price, '', 0, $this->LgStringSwitch('定金抵扣金额记录'), 'catdeposit', $payArray['create_time']);

            $BalanceModel->reduceCatBalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $pay_price, $paypid, $this->payfeeorderOne['coursecat_id'], 'Deposit', $this->LgStringSwitch('定金预收余额抵扣金额记录'), strtotime($payArray['create_time']), $this->payfeeorderOne['coursetype_id']);

            $this->error = 0;
            $this->errortip = "生成支付订单成功";
            return $data;
        }
        elseif ($payArray['paytype'] == 'feewaiver') {
            if ($this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_id", "pay_issuccess='0' and  paytype_code ='feewaiver' and order_pid='{$this->payfeeorderOne['order_pid']}'  ")) {
                $this->error = 1;
                $this->errortip = "存在未审核的订单";
                return false;
            }

            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('学费减免支付'), 0, $payArray['pay_note'], 'feewaiver', $pay_price, $payArray['create_time'], 0, '', $payArray['pay_img']);
            $this->orderTracks($this->LgStringSwitch('申请减免学费'), $this->LgStringSwitch('订单申请减免学费' . $pay_price . '元，请耐心等待财务审核'));
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }

        }
        elseif ($payArray['paytype'] == 'canceldebts') {
            if ($this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_id", "pay_issuccess='0' and  paytype_code ='canceldebts' and order_pid='{$this->payfeeorderOne['order_pid']}'  ")) {
                $this->error = 1;
                $this->errortip = "存在未审核的订单";
                return false;
            }

            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('坏账核销'), 0, $payArray['pay_note'], 'canceldebts', $pay_price, $payArray['create_time']);
            $this->orderTracks($this->LgStringSwitch('申请坏账核销'), $this->LgStringSwitch('订单申请坏账核销' . $pay_price . '元，请耐心等待财务审核'));
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }

        }
        elseif ($payArray['paytype'] == 'qrcode') {

            if($this->school_id!='1175' && $this->school_id!='1199'){

                $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('二维码支付'), 0, $payArray['pay_note'], 'qrcode', $pay_price, $payArray['create_time']);

                $paycompaniesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_chargchannel", "companies_id='{$this->payfeeorderOne['companies_id']}'");
                $data['pay_pid'] = $pay_pid;
    
                if($paycompaniesOne['companies_chargchannel'] == 'cmbbank'){
                    $data['payqrcode'] = "https://scshopapi.kedingdang.com/BoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";
                }elseif($paycompaniesOne['companies_chargchannel'] == 'cmbheadbank'){
                    $data['payqrcode'] = "https://scshopapi.kedingdang.com/HeadBoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";
                }elseif($paycompaniesOne['companies_chargchannel'] == 'cmbmergebank'){
                    $data['payqrcode'] = "https://scshopapi.kedingdang.com/MergeBoingPay/OrderPay?paypid={$pay_pid}&paymenttype=ewmpay";
                }else{
                    $this->error = 1;
                    $this->errortip = "未查询收款方式";
                    return false;
                }
    
                if ($pay_pid) {
                    $this->error = 0;
                    $this->errortip = "生成支付订单成功".SITE_URL;
                    return $data;
                } else {
                    $this->error = 1;
                    $this->errortip = "生成支付订单失败".SITE_URL;
                    return false;
                }
            }else{
                // 生成支付链接
                $payUrl = "https://faceentry.kedingdang.com/Pay/ChargeChannelLoading?order_pid=".$this->payfeeorderOne['order_pid'];
                
                // 生成二维码图片（返回base64编码）
                $qrcodeBase64 = $this->generateQRCode($payUrl, 'base64', 8, 'H');
                
                $data=array();
                $data['pay_pid']='';
                $data['payqrcode'] = $qrcodeBase64;

                return $data;
            }

            

        } elseif ($payArray['paytype'] == 'bsaocpay') {
            $pay_pid = $orderHandleModel->orderPay($this->LgStringSwitch('扫码枪支付'), 0, $payArray['pay_note'], 'bsaoc', $pay_price, $payArray['create_time'], 0, 'cmb');
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }
        }elseif($payArray['paytype'] == 'H5Pay'){

            if($payArray['paymenttype'] == 'wechat'){
                $title = $this->LgStringSwitch('微信支付');
            }elseif($payArray['paymenttype'] == 'alipay'){
                $title = $this->LgStringSwitch('支付宝支付');
            }
            
            $pay_pid = $orderHandleModel->orderPay($title, 0, $payArray['pay_note'], $payArray['paymenttype'], $pay_price, $payArray['create_time'], 0, 'zhcmb');
            $data['pay_pid'] = $pay_pid;
            if ($pay_pid) {
                $this->error = 0;
                $this->errortip = "生成支付订单成功";
                return $data;
            } else {
                $this->error = 1;
                $this->errortip = "生成支付订单失败";
                return false;
            }

        } else {
            $this->error = 1;
            $this->errortip = "请选择支付方式";
            return false;

        }
    }


//    /支付成功-支付订单
    //$bakjson支付返回Json
    function orderPaylog($pay_pid, $paylog_tradeno, $ifee, $pay_note, $paytype_code = "", $create_time = '', $from = 0, $bakjson = '', $img = '', $paychannel_code = '')
    {
        $this->publicarray['school_id'] = $this->payfeeorderOne['school_id'];
        $bools = true;
        $orderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $this->payfeeorderOne['order_pid']);
        $payOne = $this->DataControl->getOne("smc_payfee_order_pay", "pay_pid='{$pay_pid}' and  order_pid='{$this->payfeeorderOne['order_pid']}'");
        if ($from == '0') {
            if (!$payOne) {
                $this->error = 1;
                $this->errortip = "未查询到支付记录";
                return false;
            }
            if ($payOne['order_pid'] != $this->payfeeorderOne['order_pid']) {
                $this->error = 1;
                $this->errortip = "订单所属出错";
                return false;
            }

            if ($payOne['pay_issuccess'] == 1) {

                if($this->payfeeorderOne['order_paymentprice']==$payOne['pay_price'] && $this->payfeeorderOne['order_status']>0 && $this->payfeeorderOne['order_status']<4){
                    $orderHandleModel->updateOrderPay($pay_pid, 1, $paylog_tradeno, $pay_note, $paytype_code, $create_time, $paychannel_code);
                }


                $this->error = 1;
                $this->errortip = "订单已支付";
                return false;

            }

            if ($paytype_code == 'balance') {
                $stuBalanceOne = $this->DataControl->getFieldOne("smc_student_balance", "student_balance", "student_id ='{$this->payfeeorderOne['student_id']}' and school_id='{$this->school_id}' and companies_id='{$this->payfeeorderOne['companies_id']}'");
                if ($stuBalanceOne['student_balance'] < $payOne['pay_price']) {
                    $this->error = 1;
                    $this->errortip = "账户余额不足";
                    return false;
                }

                $balanceLogOne = $this->DataControl->selectOne("SELECT student_balance AS balancelog_finalamount FROM smc_student_balance M 
                    WHERE student_id = '{$this->payfeeorderOne['student_id']}' 
                    AND school_id = '{$this->school_id}' and M.companies_id='{$this->payfeeorderOne['companies_id']}'");
                if ($balanceLogOne['balancelog_finalamount'] < $payOne['pay_price']) {
                    $this->error = 1;
                    $this->errortip = "您的主体账户余额不足";
                    return false;
                }
            }

            if ($this->DataControl->getOne('smc_payfee_order_paylog', "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_pid='{$pay_pid}'")) {
                $this->error = 1;
                $this->errortip = "订单已经被支付";
                return false;
            }

            if ($payOne['pay_type'] == 2) {
                if (!$this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_id", "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess='1' and pay_type=2 and pay_price>0")) {
                    $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $this->payfeeorderOne['order_pid']);
                    $OrderHandleModel->stuTimesExpend();
                }
            }

            $bools = $orderHandleModel->orderPaylog($pay_pid, $payOne['pay_price'], $paylog_tradeno, $ifee, $pay_note, $paytype_code, $create_time, $bakjson, $img, $paychannel_code);
        }

        $numArray = array();
        if ($bools) {
            if ($paytype_code == 'balance' && $stuBalanceOne) {
                $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
                $BalanceModel->reduceStuBalance($this->payfeeorderOne['student_id'], $this->payfeeorderOne['trading_pid'], $payOne['pay_price'], '',$this->payfeeorderOne['companies_id'], strtotime($create_time));
            }

            if ($paytype_code) {
                $payname = $this->DataControl->getFieldOne('smc_code_paytype', "paytype_name", "paytype_code='{$paytype_code}'");
                $paytype_name = $payname['paytype_name'];
                $paycode = $paytype_code;
            } else {
                $paytype_name = $payOne['pay_typename'];
                $paycode = $payOne['paytype_code'];
            }
            if ($paycode == 'bankcard' || $paycode == 'cash' || $paycode == 'pos' || $paycode == 'alipay' || $paycode == 'wechat' || $paycode == 'qrcode') {
                $orderHandleModel->addIncharge($pay_pid, $paylog_tradeno, $paytype_name, $payOne['pay_price'], $pay_note);
            }
            if ($this->payfeeorderOne['order_from'] != 2) {
//				$order_pay = $this->DataControl->getOne('smc_payfee_order_paylog', "order_pid='{$this->payfeeorderOne['order_pid']}' and paylog_actualprice > 0  and  pay_pid <>'{$pay_pid}' ");

                if ($from == 1) {
                    $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
                    $sql = "select poc.*,sc.course_inclasstype
                          from smc_payfee_order_course as poc
                          left join smc_course as sc on poc.course_id=sc.course_id
                          where poc.order_pid='{$this->payfeeorderOne['order_pid']}'";
                    $orderCourseLList = $this->DataControl->selectClear($sql);

                    $pay = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where paytype_code='forward' and pay_issuccess = 1 and  order_pid ='{$this->payfeeorderOne['order_pid']}' ");

                    if ($pay['pay_price']) {
                        $number = $pay['pay_price'];
                        $total = count($orderCourseLList);
                        $divide_number = bcdiv($number, $total, 0);
                        $last_number = bcsub($number, $divide_number * ($total - 1), 0);
                        $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
                        $numArray = explode("+", $number_str);
                    } else {
                        $number = $this->payfeeorderOne['order_market_price'];
                        $total = count($orderCourseLList);
                        $divide_number = bcdiv($number, $total, 0);
                        $last_number = bcsub($number, $divide_number * ($total - 1), 0);
                        $number_str = $last_number . str_repeat("+" . $divide_number, $total - 1);
                        $numArray = explode("+", $number_str);
                    }

                    if ($orderCourseLList) {
                        foreach ($orderCourseLList as $key => $val) {
                            $sql = "select poc.ordercoupons_price,sc.coupons_type,coupons_class,coupons_playclass
								from smc_payfee_order_coupons as poc
								left join smc_payfee_order as po on po.order_pid=poc.order_pid
								left join smc_student_coupons as sc on sc.student_id=po.student_id and poc.coupons_pid =sc.coupons_pid
								where poc.order_pid='{$this->payfeeorderOne['order_pid']}' and poc.course_id='{$val['course_id']}'";
                            $orderCoup = $this->DataControl->selectClear($sql);

                            if ($orderCoup) {
                                if ($orderCoup[0]['coupons_class'] == 2) {
                                    $deductionmethod = 2;
                                } else {
                                    $deductionmethod = 0;
                                }
                            } else {
                                $deductionmethod = 0;
                            }


                            if ($val['course_inclasstype'] == 1) {

                                $playamount = $val['ordercourse_totalprice'];
                                $coursebalance_time = $val['ordercourse_buynums'];
                                $unitexpend = $val['ordercourse_unitprice'];
                                $unitrefund = $val['ordercourse_unitrefund'];


                                $sql = "select os.ordershare_id,os.ordershare_month,os.ordershare_price as sellingprice
                              from smc_payfee_order_share as os where os.order_pid='{$this->payfeeorderOne['order_pid']}'";

                                $monthList = $this->DataControl->selectClear($sql);

                                foreach ($monthList as &$monthOne) {
                                    $monthOne['price'] = $monthOne['sellingprice'];
                                    $monthOne['month'] = $monthOne['ordershare_month'];
                                }

                                if ($orderCoup) {
                                    $coupon_price = 0;
                                    foreach ($orderCoup as $orderCoupOne) {
                                        if ($orderCoupOne['coupons_type'] == 0 && $orderCoupOne['coupons_playclass'] != 1) {
                                            $coupon_price += $orderCoupOne['ordercoupons_price'];
                                        }
                                    }
                                } else {
                                    $coupon_price = 0;
                                }

                                $bool = $BalanceModel->buyCourse($this->payfeeorderOne['order_pid'], $this->payfeeorderOne['student_id'], $val['course_id'], $val['pricing_id'], $playamount, $coursebalance_time, 0, $coupon_price, 0, $unitexpend, $unitrefund, $deductionmethod, $val['class_id'], $val['ordercourse_enterclassdate'], 0, 0, $monthList);

                                if (!$bool) {
                                    $this->error = 1;
                                    $this->errortip = $BalanceModel->errortip;
                                    return false;
                                }

                            } else {


                                if (isset($val['class_id']) && $val['class_id'] != 0) {
                                    if ($pay['pay_price']) {
                                        $forward = $numArray[$key];
                                        $coupon_price = 0;//
                                    } else {
                                        $forward = 0;
                                        if ($orderCoup) {
                                            $coupon_price = 0;
                                            foreach ($orderCoup as $orderCoupOne) {
                                                if ($orderCoupOne['coupons_type'] == 0 && $orderCoupOne['coupons_playclass'] != 1) {
                                                    $coupon_price += $orderCoupOne['ordercoupons_price'];
                                                }
                                            }
                                        } else {
                                            $coupon_price = 0;
                                        }
                                    }
                                    $playamount = $val['ordercourse_totalprice'];
                                    $coursebalance_time = $val['ordercourse_buynums'];
                                    $unitexpend = $val['ordercourse_unitprice'];
                                    $unitrefund = $val['ordercourse_unitrefund'];

                                } else {
                                    $playamount = $val['ordercourse_totalprice'];
                                    $coursebalance_time = $val['ordercourse_buynums'];
                                    $unitexpend = $val['ordercourse_unitprice'];
                                    $unitrefund = $val['ordercourse_unitrefund'];

                                    if ($pay['pay_price']) {
                                        $forward = $numArray[$key];
                                        $coupon_price = 0;//优惠券优惠的金额
                                        if ($forward > 0) {
                                            $forwardpid = $orderHandleModel->orderPay($this->LgStringSwitch('账户结转余额'), '1', $note = $this->LgStringSwitch('结转余额抵扣金额记录'), $paytype_code = "forward", $forward, $create_time);
                                            $orderHandleModel->orderPaylog($forwardpid, $forward, '', $ifee = 0, $this->LgStringSwitch('结转余额抵扣金额记录'), 'forward', $create_time);
                                            $BalanceModel->reduceStuForward($this->payfeeorderOne['student_id'], $forward,$this->payfeeorderOne['companies_id'], $create_time);
                                        }

                                    } else {
                                        $forward = 0;
                                        $coupon_price = 0;
                                        if ($orderCoup) {
                                            foreach ($orderCoup as $orderCoupOne) {
                                                if ($orderCoupOne['coupons_type'] == 0 && $orderCoupOne['coupons_playclass'] != 1) {
                                                    $coupon_price += $orderCoupOne['ordercoupons_price'];
                                                }
                                            }
                                        }
                                    }
                                }

                                $from = 0;
                                if ($this->DataControl->getOne('smc_student_study', "student_id='{$this->payfeeorderOne['student_id']}' and class_id='{$val['class_id']}' and  study_isreading = 0")) {
                                    $from = 1;
                                }


                                $BalanceModel->buyCourse($this->payfeeorderOne['order_pid'], $this->payfeeorderOne['student_id'], $val['course_id'], $val['pricing_id'], $playamount, $coursebalance_time, $forward, $coupon_price, 0, $unitexpend, $unitrefund, $deductionmethod, $val['class_id'], $val['ordercourse_enterclassdate'], 0, $from);
                            }
                        }

                        $orderdata = array();
                        $orderdata['order_iscreatecourse'] = 1;
                        $this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}'", $orderdata);
                    }
                }
            }
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "支付错误";
            return false;
        }
    }
}
