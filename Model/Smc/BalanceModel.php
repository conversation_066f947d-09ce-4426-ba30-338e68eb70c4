<?php


namespace Model\Smc;

class BalanceModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        }

        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    function stuTrading($student_id, $code = "", $companies_id, $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }
        $data = array();
        do {
            $trading_pid = $this->createOrderPid('JY');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = $code;
        $data['trading_status'] = "1";
        $data['trading_createtime'] = $time;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        if ($this->DataControl->insertData("smc_student_trading", $data)) {
            return $trading_pid;
        } else {
            return false;
        }
    }

    //结转   -- 按课程金额结转
    function carryOver($student_id, $course_id, $class_id = 0, $from = 0, $time = '', $forwardNum = 0, $remark = '',$is_skip=0,$application_id=0)
    {
        if ($time == '') {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        $sql = "select cc.coursetype_refundtype,course_isforward,co.course_cnname 
              from smc_course as co,smc_code_coursetype as cc 
              where co.coursetype_id=cc.coursetype_id and co.course_id='{$course_id}' limit 0,1";

        $courseTypeOne = $this->DataControl->selectOne($sql);

        if (!$courseTypeOne) {
            $this->error = true;
            $this->errortip = "课程不存在";
            return false;
        }

        if ($courseTypeOne['course_isforward'] == 0) {
            if ($this->stafferOne['account_class'] == 0) {
                $this->error = true;
                $this->errortip = "存在不可结转课程";
                return false;
            }
        }

        $schoolOne=$this->DataControl->getFieldOne("smc_school","school_isforwardapply","school_id='{$this->school_id}'");

        if($is_skip==0 && $class_id>0 && $schoolOne['school_isforwardapply']==1){

            $sql = "select y.income_id 
                    from smc_student_hourstudy as a,smc_school_income as y 
                    where a.hourstudy_id=y.hourstudy_id and a.student_id='{$student_id}' and a.class_id='{$class_id}' and y.income_type=0 and y.income_price>0";
            $hourStudyOne = $this->DataControl->selectOne($sql);

            if($hourStudyOne){

                $sql = "select a.application_status from smc_forward_application as a where a.student_id='{$student_id}' and a.course_id='{$course_id}' and a.class_id='{$class_id}' order by a.application_id desc limit 0,1";
                $forwardApplicationOne = $this->DataControl->selectOne($sql);
                if($forwardApplicationOne){

                    if($forwardApplicationOne['application_status']==0){
                        $this->error = true;
                        $this->errortip = "存在未审核的结转申请，不可结转";
                        return false;
                    }elseif($forwardApplicationOne['application_status']==1){
                        $this->error = true;
                        $this->errortip = "存在已审核的结转申请，不可结转";
                        return false;
                    }else{
                        $this->error = true;
                        $this->errortip = "审核失败，不可结转";
                        return false;
                    }
                }else{
                    $this->error = true;
                    $this->errortip = "存在已收入课次，不可结转";
                    return false;
                }
            } 
        }

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0, $course_id);

        $allWithhold = 0;
        $allBalance = 0;
        $allTime = 0;

        $DealModel = new \Model\Smc\DealModel($this->publicarray);

        $sql = "select cs.stustatus_isenschool,scl.changelog_day,scl.changelog_id
              from smc_student_changelog as scl,smc_code_stuchange as cs 
              where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=1 and scl.student_id='{$student_id}' and scl.school_id='{$this->school_id}' 
              order by scl.changelog_day desc,scl.changelog_id desc";

        $logOne = $this->DataControl->selectOne($sql);

        if ($logOne['stustatus_isenschool'] == 1) {
            if ($time < strtotime($logOne['changelog_day'])) {
                $this->error = true;
                $this->errortip = "结转时间不能在入校之前";
                return false;
            }
        }


        $day = date("Y-m-d", $time);
        if ($class_id > 0) {

            $where = " 1 ";
            $sql = "select cl.changelog_day
                  from smc_student_changelog as cl,smc_code_stuchange as cs 
                  where cl.stuchange_code=cs.stuchange_code and cs.stuchange_type=0 and stustatus_isenclass=1 and cl.student_id='{$student_id}' and cl.class_id='{$class_id}'
                  order by cl.changelog_day desc,cl.changelog_id desc limit 0,1";

            $startLogOne = $this->DataControl->selectOne($sql);
            if ($startLogOne) {
                $where .= " and hour_day>='{$startLogOne['changelog_day']}'";
            } else {
                $startLogOne = $this->DataControl->getFieldOne("smc_student_study", "study_beginday", "student_id='{$student_id}' and class_id='{$class_id}'");

                if ($startLogOne) {
                    $where .= " and hour_day>='{$startLogOne['study_beginday']}'";
                }
            }

            $sql = "select hour_id from smc_class_hour 
                  where {$where} and class_id='{$class_id}' and hour_day<'{$day}' and hour_ischecking=0 and hour_iswarming=0 and hour_iswarming='0'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "班级考勤未完成！";
                return false;
            }

            //检查是否连续缺勤模式的课程，最后几节课要考勤
            $checkRequest = array();
            $checkRequest['class_id'] = $class_id;
            $checkRequest['student_id'] = $student_id;
            $check = $this->addReduceOrder($checkRequest);
            if ($check > 0) {
                $this->error = true;
                $this->errortip = "应先扣除在班{$check}课次再结转！";
                return false;
            }
        }

        $sql = "select ssc.coursebalance_id,s.student_id,s.student_cnname,s.student_branch,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.coursebalance_unitexpend,ssc.coursebalance_unitrefund,sc.course_classnum,scf.courseforward_price,ssc.pricing_id,fpt.tuition_refundprice,sc.course_freenums,fpt.tuition_originalprice,ssc.coursebalance_status,ssc.coursebalance_unitearning,scf.courseforward_deductionmethod,ssc.companies_id
                  ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_class_hour as ch on ch.hour_id=sh.hour_id where sh.student_id=ssc.student_id and sh.class_id='{$class_id}' and ch.hour_isfree=0) as num
                  ,(select cp.pricinglog_buytimes from smc_student_coursebalance_pricinglog as cp where cp.student_id=ssc.student_id and cp.course_id=ssc.course_id order by cp.pricinglog_id desc limit 0,1) as buynum
                  ,(select scp.pricinglog_buyprice from smc_student_coursebalance_pricinglog as scp where scp.student_id=ssc.student_id and scp.course_id=ssc.course_id order by scp.pricinglog_id desc limit 0,1) as buyprice
				  from smc_student_coursebalance as ssc
				  left join smc_student as s on s.student_id=ssc.student_id
				  left join smc_course as sc on sc.course_id=ssc.course_id
				  left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and scf.course_id=ssc.course_id
				  left join smc_fee_pricing_tuition as fpt on fpt.pricing_id=ssc.pricing_id and fpt.course_id=ssc.course_id
				  where sc.company_id='{$this->company_id}' and ssc.student_id='{$student_id}' and ssc.course_id='{$course_id}' and ssc.school_id='{$this->school_id}'
				  limit 0,1
				 ";
        $studentOne = $this->DataControl->selectOne($sql);
        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "学生信息错误！";
            return false;
        }

        $companiesOne['companies_id'] = $studentOne['companies_id'];

        $stublcOne = $this->getStuBalance($student_id, $this->company_id, $this->school_id, $companiesOne['companies_id']);

        if ($courseTypeOne['coursetype_refundtype'] == 1) {
            $studentOne['coursebalance_unitrefund'] = $studentOne['coursebalance_unitearning'];
        }

        if ($forwardNum < 0 || $forwardNum > $studentOne['coursebalance_time']) {
            $this->error = true;
            $this->errortip = "请填写正确的结转课次数！";
            return false;
        }

        if ($forwardNum == $studentOne['coursebalance_time']) {
            $forwardNum = 0;
        }

        $sql = "select sc.course_inclasstype,sc.course_minabsencenum,sc.coursecat_id,cc.coursecat_branch,sc.course_branch,sc.course_refundprice
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where c.class_id='{$class_id}'";
        $courseOne = $this->DataControl->selectOne($sql);

        $achievPrice = 0;

        if ($courseOne['course_inclasstype'] != '1') {
            $addForward = 0;
            $refundprice = 0;
            if ($studentOne['pricing_id'] == 0) {
                $pricingOne = $this->getCoursePricing($course_id, $this->company_id, $this->school_id);
                $sql = "select ssc.course_id
                ,ssc.coursebalance_time
                ,ssc.coursebalance_figure
                ,ssc.coursebalance_unitexpend
                ,ssc.coursebalance_unitrefund
                ,scf.courseforward_price
                ,sc.course_freenums
                ,s.student_branch
                ,ssc.coursebalance_unitearning
                ,scf.courseforward_deductionmethod
                ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh left join smc_class_hour as ch on ch.hour_id=sh.hour_id 
                    where sh.student_id=ssc.student_id and sh.class_id='{$class_id}' and ch.hour_isfree='0') as num
                ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id='{$class_id}' and sch.hour_isfree='0') as hour_num
                from smc_student_coursebalance as ssc
                left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and scf.course_id=ssc.course_id
                left join smc_course as sc on sc.course_id=ssc.course_id
                left join smc_student as s on s.student_id=ssc.student_id
                where ssc.student_id='{$student_id}' 
                and ssc.course_id='{$course_id}' 
                and ssc.school_id='{$this->school_id}'";
                $stuCourseOne = $this->DataControl->selectOne($sql);

                if ($courseTypeOne['coursetype_refundtype'] == 1) {
                    if ($stuCourseOne['coursebalance_unitearning'] > 0) {
                        $stuCourseOne['coursebalance_unitrefund'] = $stuCourseOne['coursebalance_unitearning'];
                    }
                }
                $studentOne = $stuCourseOne;

                if ($forwardNum == $studentOne['coursebalance_time']) {
                    $forwardNum = 0;
                }

                if ($class_id > '0') {
                    $sql = "select (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=ss.class_id and ch.hour_day>=ss.study_beginday and ch.hour_day<=ss.study_endday and ch.hour_day<='{$day}' and ch.hour_isfree='0' and ch.hour_ischecking='1') as num
                  from smc_student_study as ss
                  where ss.class_id='{$class_id}' and ss.student_id='{$student_id}'";
                    $hourOne = $this->DataControl->selectOne($sql);
                    if ($hourOne) {
                        $studentOne['num'] = $hourOne['num'];
                    }
                }
                if (($studentOne['num'] < $studentOne['course_freenums']) && $pricingOne && $forwardNum == 0) {

                    $refundprice = $pricingOne['tuition_refundprice'];
                    if ($studentOne['coursebalance_figure'] - $refundprice >= 0) {
                        $studentOne['coursebalance_figure'] = $studentOne['coursebalance_figure'] - $refundprice;
                    } else {
                        $refundprice = $studentOne['coursebalance_figure'];
                        $studentOne['coursebalance_figure'] = 0;
                    }
                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['balancelog_class'] = 2;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('手续费转账户不可退金额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
                    $balancelog_data['balancelog_playamount'] = $refundprice;
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $refundprice;

                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('手续费转账户不可退金额');
                    $balancelog_data['balancelog_time'] = $time;

                }

                if ($studentOne['hour_num'] > 0 && $forwardNum == 0) {
                    $unitexpend = ceil($pricingOne['tuition_originalprice'] / $studentOne['hour_num']);
                    if ($studentOne['num'] > 0) {
                        $addForward = $studentOne['num'] * ($unitexpend - $studentOne['coursebalance_unitexpend']);
                        if ($studentOne['coursebalance_figure'] < $addForward) {
                            $addForward = $studentOne['coursebalance_figure'];
                        }
                    }
                }
            } else {
                if ($studentOne['coursebalance_status'] == 2) {
                    $studentOne['num'] = $studentOne['buynum'] - $studentOne['coursebalance_time'];
                }

                if ($studentOne['num'] < $studentOne['course_freenums'] && $forwardNum == 0) {

                    $refundprice = $studentOne['tuition_refundprice'];
                    if ($studentOne['coursebalance_figure'] - $refundprice >= 0) {
                        $studentOne['coursebalance_figure'] = $studentOne['coursebalance_figure'] - $refundprice;
                    } else {
                        $refundprice = $studentOne['coursebalance_figure'];
                        $studentOne['coursebalance_figure'] = 0;
                    }
                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['balancelog_class'] = 2;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('手续费转账户不可退金额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
                    $balancelog_data['balancelog_playamount'] = $refundprice;
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $refundprice;

                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('手续费转账户不可退金额');
                    $balancelog_data['balancelog_time'] = $time;
//                    $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
//                    $stublcOne['student_withholdbalance']+=$refundprice;
                }

                if (($studentOne['num'] > 0) && ($studentOne['buynum']) > 0 && $forwardNum == 0) {
//                $addForward = ($studentOne['coursebalance_unitrefund']-$studentOne['coursebalance_unitexpend'])*$studentOne['num'];
                    $addForward = ($studentOne['buyprice'] - ceil($studentOne['buyprice'] / $studentOne['buynum']) * $studentOne['num']) - ($studentOne['buyprice'] - $studentOne['coursebalance_unitrefund'] * $studentOne['num']);
                    if ($studentOne['buyprice'] == 0 || is_null($studentOne['buyprice'])) {
                        $addForward = ($studentOne['coursebalance_unitrefund'] - $studentOne['coursebalance_unitexpend']) * $studentOne['num'];
                    }

                    if ($studentOne['coursebalance_figure'] < $addForward) {
                        $addForward = $studentOne['coursebalance_figure'];
                    }
                }
            }

            $courseforward_price = 0;
            $coursebalance_figure = 0;
            if ($forwardNum > 0) {
                if ($studentOne['courseforward_deductionmethod'] != 0) {
                    $this->error = true;
                    $this->errortip = "课程存在非平摊优惠,不可部分结转！";
                    return false;
                }

                if ($studentOne['courseforward_price'] > 0) {
                    if ($studentOne['coursebalance_unitearning'] > 0 && $studentOne['coursebalance_unitexpend'] > $studentOne['coursebalance_unitearning']) {
                        $courseforward_price = $studentOne['courseforward_price'] - (($studentOne['coursebalance_time'] - $forwardNum) * ($studentOne['coursebalance_unitexpend'] - $studentOne['coursebalance_unitearning']));
                    }
                }

                if ($studentOne['coursebalance_unitearning'] > 0) {
                    $coursebalance_figure = $studentOne['coursebalance_figure'] - (($studentOne['coursebalance_time'] - $forwardNum) * $studentOne['coursebalance_unitearning']);
                } else {
                    $coursebalance_figure = $studentOne['coursebalance_figure'] - (($studentOne['coursebalance_time'] - $forwardNum) * $studentOne['coursebalance_unitexpend']);
                }

                $coursebalance_figure = $coursebalance_figure >= 0 ? $coursebalance_figure : 0;
                $courseforward_price = $courseforward_price >= 0 ? $courseforward_price : 0;
            }

            if (($studentOne['courseforward_price'] + $studentOne['coursebalance_figure']) > 0) {
                if ($forwardNum == 0) {
                    if ($studentOne['coursebalance_figure'] < $addForward) {
                        $dealorder_pid = $DealModel->dealorder($student_id, 0, $studentOne['courseforward_price'] + $addForward, $course_id, $this->LgStringSwitch('创建课程结转订单'), $this->LgStringSwitch('课程结转订单提交成功，订单完成'), $remark, $time,$application_id);
                    } else {
                        $dealorder_pid = $DealModel->dealorder($student_id, $studentOne['coursebalance_figure'] - $addForward, $studentOne['courseforward_price'] + $addForward, $course_id, $this->LgStringSwitch('创建课程结转订单'), $this->LgStringSwitch('课程结转订单提交成功，订单完成'), $remark, $time,$application_id);
                    }
                } else {
                    $dealorder_pid = $DealModel->dealorder($student_id, $coursebalance_figure, $courseforward_price, $course_id, $this->LgStringSwitch('创建课程结转订单'), $this->LgStringSwitch('课程结转订单提交成功，订单完成'), $remark, $time,$application_id);
                }

                $dealOne = $this->DataControl->getFieldOne("smc_forward_dealorder", "trading_pid", "dealorder_pid='{$dealorder_pid}'");

                $tradingPid = $dealOne['trading_pid'];
            } else {
                $tradingPid = '';
            }

            if ($refundprice && $refundprice > 0) {
                $data = array();
                $data['student_id'] = $student_id;
                $data['log_class'] = 0;
                $data['course_id'] = $course_id;
                $data['school_id'] = $this->school_id;
                $data['companies_id'] = $companiesOne['companies_id'];
                $data['class_id'] = $class_id;
                $data['trading_pid'] = $tradingPid;
                $data['log_playname'] = $this->LgStringSwitch('手续费');
                $data['log_playclass'] = '-';
                $data['log_fromamount'] = $studentOne['coursebalance_figure'] + $refundprice;
                $data['log_playamount'] = $refundprice;
                $data['log_finalamount'] = $studentOne['coursebalance_figure'];

                $data['log_fromtimes'] = $studentOne['coursebalance_time'];
                $data['log_playtimes'] = 0;
                $data['log_finaltimes'] = $studentOne['coursebalance_time'];

                $data['log_reason'] = $this->LgStringSwitch('手续费');
                $data['log_time'] = $time;
                $this->DataControl->insertData("smc_student_coursebalance_log", $data);

                $balancelog_data['trading_pid'] = $tradingPid;
                $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
                $stublcOne['student_withholdbalance'] += $refundprice;
                $allWithhold += $refundprice;
            }


            if ($addForward > 0) {
                $coursebalancelog_data = array();
                $coursebalancelog_data['student_id'] = $student_id;
                $coursebalancelog_data['log_class'] = 0;
                $coursebalancelog_data['course_id'] = $course_id;
                $coursebalancelog_data['school_id'] = $this->school_id;
                $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
                $coursebalancelog_data['class_id'] = $class_id;

                $coursebalancelog_data['trading_pid'] = $tradingPid;

                $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程结转余额结转');
                $coursebalancelog_data['log_playclass'] = '-';
                $coursebalancelog_data['log_fromamount'] = $studentOne['coursebalance_figure'];
                $coursebalancelog_data['log_playamount'] = $addForward;
                $coursebalancelog_data['log_finalamount'] = $studentOne['coursebalance_figure'] - $addForward;

                $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
                $coursebalancelog_data['log_playtimes'] = 0;
                $coursebalancelog_data['log_finaltimes'] = $studentOne['coursebalance_time'];

                $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课程结转余额结转');
                $coursebalancelog_data['log_time'] = $time;
                $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);


                $balancelog_data = array();
                $balancelog_data['company_id'] = $this->company_id;
                $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                $balancelog_data['school_id'] = $this->school_id;
                $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                $balancelog_data['student_id'] = $student_id;

                $balancelog_data['trading_pid'] = $tradingPid;
                $balancelog_data['balancelog_class'] = 2;
                $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课程余额转账户不可退余额');
                $balancelog_data['balancelog_playclass'] = '+';
                $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
                $balancelog_data['balancelog_playamount'] = $addForward;
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $addForward;

                $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课程余额转账户不可退余额');
                $balancelog_data['balancelog_time'] = $time;
                $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

                if ($studentOne['coursebalance_figure'] < $addForward) {
                    $studentOne['coursebalance_figure'] = 0;
                } else {
                    $studentOne['coursebalance_figure'] = $studentOne['coursebalance_figure'] - $addForward;
                }

                $achievPrice += $addForward;
                $stublcOne['student_withholdbalance'] += $addForward;
                $allWithhold += $addForward;
            }


            $data = array();
            $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'];
            $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companiesOne['companies_id']}'", $data);


            $random = $this->create_guid();

            if ($forwardNum == 0) {
                if ($studentOne['coursebalance_figure'] >= 0) {
                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $student_id;
                    $coursebalancelog_data['log_class'] = 0;
                    $coursebalancelog_data['course_id'] = $course_id;
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['class_id'] = $class_id;
                    $coursebalancelog_data['trading_pid'] = $tradingPid;
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程余额结转');
                    $coursebalancelog_data['log_playclass'] = '-';
                    $coursebalancelog_data['log_fromamount'] = $studentOne['coursebalance_figure'];
                    $coursebalancelog_data['log_playamount'] = $studentOne['coursebalance_figure'];
                    $coursebalancelog_data['log_finalamount'] = $studentOne['coursebalance_figure'] - $studentOne['coursebalance_figure'];

                    $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_playtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_finaltimes'] = $studentOne['coursebalance_time'] - $studentOne['coursebalance_time'];

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课程余额结转');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);


                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['trading_pid'] = $tradingPid;
                    $balancelog_data['balancelog_class'] = 0;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课程余额转账户余额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
                    $balancelog_data['balancelog_playamount'] = $studentOne['coursebalance_figure'];
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $studentOne['coursebalance_figure'];
                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课程余额转账户余额');
                    $balancelog_data['balancelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

                    $achievPrice += $studentOne['coursebalance_figure'];

                }

                $student_coursebalance_data = array();
                $student_coursebalance_data['coursebalance_figure'] = 0;
                $student_coursebalance_data['coursebalance_time'] = 0;
                $student_coursebalance_data['coursebalance_status'] = 3;
                $student_coursebalance_data['coursebalance_updatatime'] = time();
                $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

                $student_courseforward_data = array();
                $student_courseforward_data['courseforward_price'] = 0;
                $student_courseforward_data['courseforward_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $student_courseforward_data);

                if ($studentOne['coursebalance_time'] > 0) {
                    $time_data = array();
                    $time_data['student_id'] = $student_id;
                    $time_data['course_id'] = $course_id;
                    $time_data['school_id'] = $this->school_id;
                    $time_data['companies_id'] = $companiesOne['companies_id'];
                    $time_data['class_id'] = $class_id;
                    $time_data['trading_pid'] = $tradingPid;
                    $time_data['log_random'] = $random;
                    $time_data['timelog_playname'] = $this->LgStringSwitch('结转课次');
                    $time_data['timelog_playclass'] = '-';
                    $time_data['timelog_fromtimes'] = $studentOne['coursebalance_time'];
                    $time_data['timelog_playtimes'] = $studentOne['coursebalance_time'];
                    $time_data['timelog_finaltimes'] = $studentOne['coursebalance_time'] - $studentOne['coursebalance_time'];
                    $time_data['timelog_reason'] = $this->LgStringSwitch('结转课次');
                    $time_data['timelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);
                }


                if ($studentOne['courseforward_price'] > 0) {

                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $student_id;
                    $coursebalancelog_data['log_class'] = 1;
                    $coursebalancelog_data['course_id'] = $course_id;
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['class_id'] = $class_id;
                    $coursebalancelog_data['trading_pid'] = $tradingPid;
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程结转余额结转');
                    $coursebalancelog_data['log_playclass'] = '-';
                    $coursebalancelog_data['log_fromamount'] = $studentOne['courseforward_price'];
                    $coursebalancelog_data['log_playamount'] = $studentOne['courseforward_price'];
                    $coursebalancelog_data['log_finalamount'] = $studentOne['courseforward_price'] - $studentOne['courseforward_price'];

                    $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_playtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_finaltimes'] = $studentOne['coursebalance_time'] - $studentOne['coursebalance_time'];

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课程结转余额结转');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['trading_pid'] = $tradingPid;
                    $balancelog_data['balancelog_class'] = 1;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课程结转余额转账户余额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
                    $balancelog_data['balancelog_playamount'] = $studentOne['courseforward_price'];
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_forwardprice'] + $studentOne['courseforward_price'];
                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课程结转余额转账户余额');
                    $balancelog_data['balancelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

                    //结算余额变更
                    $data = array();
                    $data['student_forwardprice'] = $stublcOne['student_forwardprice'] + $studentOne['courseforward_price'];
                    $data['student_updatatime'] = $time;
                    $this->DataControl->updateData("smc_student", "student_id='{$student_id}' and company_id='{$this->company_id}'", $data);
                }

                if ($studentOne['coursebalance_figure'] > 0) {
//结算账户余额
                    $data = array();
                    $data['student_balance'] = $stublcOne['student_balance'] + $studentOne['coursebalance_figure'];
                    $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'];
                    $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companiesOne['companies_id']}'", $data);
                    $allBalance = $studentOne['coursebalance_figure'];

                }
            } else {
                if ($coursebalance_figure >= 0) {
                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $student_id;
                    $coursebalancelog_data['log_class'] = 0;
                    $coursebalancelog_data['course_id'] = $course_id;
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['class_id'] = $class_id;
                    $coursebalancelog_data['trading_pid'] = $tradingPid;
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程余额结转');
                    $coursebalancelog_data['log_playclass'] = '-';
                    $coursebalancelog_data['log_fromamount'] = $studentOne['coursebalance_figure'];
                    $coursebalancelog_data['log_playamount'] = $coursebalance_figure;
                    $coursebalancelog_data['log_finalamount'] = $studentOne['coursebalance_figure'] - $coursebalance_figure;

                    $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_playtimes'] = $forwardNum;
                    $coursebalancelog_data['log_finaltimes'] = $studentOne['coursebalance_time'] - $forwardNum;

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课程余额结转');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);


                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['trading_pid'] = $tradingPid;
                    $balancelog_data['balancelog_class'] = 0;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课程余额转账户余额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
                    $balancelog_data['balancelog_playamount'] = $coursebalance_figure;
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $coursebalance_figure;
                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课程余额转账户余额');
                    $balancelog_data['balancelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
                    $achievPrice += $coursebalance_figure;
                }

                $student_coursebalance_data = array();
                $student_coursebalance_data['coursebalance_figure'] = $studentOne['coursebalance_figure'] - $coursebalance_figure;
                $student_coursebalance_data['coursebalance_time'] = $studentOne['coursebalance_time'] - $forwardNum;
                $student_coursebalance_data['coursebalance_updatatime'] = time();
                $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

                if ($courseforward_price > 0) {
                    $student_courseforward_data = array();
                    $student_courseforward_data['courseforward_price'] = $studentOne['courseforward_price'] - $courseforward_price;
                    $student_courseforward_data['courseforward_updatatime'] = $time;
                    $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $student_courseforward_data);
                }


                if ($forwardNum > 0) {
                    $time_data = array();
                    $time_data['student_id'] = $student_id;
                    $time_data['course_id'] = $course_id;
                    $time_data['school_id'] = $this->school_id;
                    $time_data['companies_id'] = $companiesOne['companies_id'];
                    $time_data['class_id'] = $class_id;
                    $time_data['trading_pid'] = $tradingPid;
                    $time_data['log_random'] = $random;
                    $time_data['timelog_playname'] = $this->LgStringSwitch('结转课次');
                    $time_data['timelog_playclass'] = '-';
                    $time_data['timelog_fromtimes'] = $studentOne['coursebalance_time'];
                    $time_data['timelog_playtimes'] = $forwardNum;
                    $time_data['timelog_finaltimes'] = $studentOne['coursebalance_time'] - $forwardNum;
                    $time_data['timelog_reason'] = $this->LgStringSwitch('结转课次');
                    $time_data['timelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);
                }


                if ($courseforward_price > 0) {

                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $student_id;
                    $coursebalancelog_data['log_class'] = 1;
                    $coursebalancelog_data['course_id'] = $course_id;
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['class_id'] = $class_id;
                    $coursebalancelog_data['trading_pid'] = $tradingPid;
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程结转余额结转');
                    $coursebalancelog_data['log_playclass'] = '-';
                    $coursebalancelog_data['log_fromamount'] = $studentOne['courseforward_price'];
                    $coursebalancelog_data['log_playamount'] = $courseforward_price;
                    $coursebalancelog_data['log_finalamount'] = $studentOne['courseforward_price'] - $courseforward_price;

                    $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_playtimes'] = $forwardNum;
                    $coursebalancelog_data['log_finaltimes'] = $studentOne['coursebalance_time'] - $forwardNum;

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课程结转余额结转');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['trading_pid'] = $tradingPid;
                    $balancelog_data['balancelog_class'] = 1;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课程结转余额转账户余额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
                    $balancelog_data['balancelog_playamount'] = $courseforward_price;
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_forwardprice'] + $courseforward_price;
                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课程结转余额转账户余额');
                    $balancelog_data['balancelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

                    //结算余额变更
                    $data = array();
                    $data['student_forwardprice'] = $stublcOne['student_forwardprice'] + $courseforward_price;
                    $data['student_updatatime'] = $time;
                    $this->DataControl->updateData("smc_student", "student_id='{$student_id}' and company_id='{$this->company_id}'", $data);
                }

                if ($coursebalance_figure > 0) {
//结算账户余额
                    $data = array();
                    $data['student_balance'] = $stublcOne['student_balance'] + $coursebalance_figure;
                    $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'];
                    $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companiesOne['companies_id']}'", $data);
                    $allBalance = $coursebalance_figure;
                }
            }

            if ($dealorder_pid) {
                $course_data = array();
                $course_data['course_id'] = $course_id;
                $course_data['dealorder_pid'] = $dealorder_pid;
                $course_data['dealcourse_figure'] = $studentOne['coursebalance_figure'];
                $course_data['dealcourse_time'] = $studentOne['coursebalance_time'];
                $course_data['dealcourse_fromforwardprice'] = $studentOne['courseforward_price'];
                $course_data['dealcourse_tobalanceprice'] = $studentOne['coursebalance_figure'];
                $course_data['dealcourse_toforwardprice'] = $studentOne['courseforward_price'];
                $this->DataControl->insertData("smc_forward_dealorder_course", $course_data);
            }

            $sql = "select po.order_pid
                    from smc_payfee_order as po
                    left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                    where poc.course_id='{$course_id}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' 
                    and po.order_status>=0 and po.companies_id='{$companiesOne['companies_id']}' order by po.order_createtime desc";

            $orderOne = $this->DataControl->selectOne($sql);

            if ($orderOne) {
                $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$orderOne['order_pid']}' and achieve_iscalculated=1");
                if ($achieveList) {

                    $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $orderOne['order_pid']);

                    foreach ($achieveList as $achieveOne) {
                        $OrderHandleModel->totalPerformance($achieveOne['staffer_id'], $achieveOne['coursetype_id'], $achieveOne['performance_id'], 0, -$achievPrice, $achieveOne['achieve_iscalculated'], 1, '扣除业绩', '课程结转扣除业绩');
                    }

                }
            }


            if ($allBalance > 0 || $allWithhold > 0) {

                $sql = "select po.order_pid
                      from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      where poc.course_id='{$course_id}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
                      and po.order_status>=0 
                        -- and po.companies_id='{$companiesOne['companies_id']}'
                      ";

                $orderOne = $this->DataControl->selectOne($sql);
                if ($orderOne) {
                    $data = array();
                    $data['paytype'] = 'balance';
                    $data['paytimes'] = '1';
                    $data['order_pid'] = $orderOne['order_pid'];
                    $data['create_time'] = date("Y-m-d H:i:s", $time);

                    $tem_data = array();
                    $tem_data['allBalance'] = $allBalance;
                    $tem_data['allWithhold'] = $allWithhold;

                    $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                    $bool = $Model->createOrderPay($data, $tem_data);

                }
            }

        } else {
            //课辅班结转
            $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_price", "coursebalance_id='{$studentOne['coursebalance_id']}'");//学员当月分摊金额

            if (!$monthOne) {
                if ($studentOne['coursebalance_figure'] > 0) {
                    $dealorder_pid = $DealModel->dealorder($student_id, $studentOne['coursebalance_figure'], 0, $course_id, '课辅课程结转', $remark, $time);

                    $dealOne = $this->DataControl->getFieldOne("smc_forward_dealorder", "trading_pid", "dealorder_pid='{$dealorder_pid}'");

                    $tradingPid = $dealOne['trading_pid'];

//                    do{
//                        $random=$this->createStuRandom($studentOne['student_branch']);
//                    }while($this->DataControl->selectOne("select log_id from smc_student_coursebalance_log where log_random='{$random}' limit 0,1"));
                    $random = $this->create_guid();

                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $student_id;
                    $coursebalancelog_data['log_class'] = 0;
                    $coursebalancelog_data['course_id'] = $course_id;
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['class_id'] = $class_id;
                    $coursebalancelog_data['trading_pid'] = $tradingPid;
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课辅课程余额结转');
                    $coursebalancelog_data['log_playclass'] = '-';
                    $coursebalancelog_data['log_fromamount'] = $studentOne['coursebalance_figure'];
                    $coursebalancelog_data['log_playamount'] = $studentOne['coursebalance_figure'];
                    $coursebalancelog_data['log_finalamount'] = 0;

                    $coursebalancelog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_playtimes'] = $studentOne['coursebalance_time'];
                    $coursebalancelog_data['log_finaltimes'] = 0;

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课辅课程余额结转');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

                    if ($studentOne['coursebalance_time'] > 0) {
                        $time_data = array();
                        $time_data['student_id'] = $student_id;
                        $time_data['course_id'] = $course_id;
                        $time_data['school_id'] = $this->school_id;
                        $time_data['companies_id'] = $companiesOne['companies_id'];
                        $time_data['class_id'] = $class_id;
                        $time_data['trading_pid'] = $tradingPid;
                        $time_data['log_random'] = $random;
                        $time_data['timelog_playname'] = $this->LgStringSwitch('课辅结转课次');
                        $time_data['timelog_playclass'] = '-';
                        $time_data['timelog_fromtimes'] = $studentOne['coursebalance_time'];
                        $time_data['timelog_playtimes'] = $studentOne['coursebalance_time'];
                        $time_data['timelog_finaltimes'] = 0;
                        $time_data['timelog_reason'] = $this->LgStringSwitch('课辅结转课次');
                        $time_data['timelog_time'] = $time;
                        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);
                    }

                    $balancelog_data = array();
                    $balancelog_data['company_id'] = $this->company_id;
                    $balancelog_data['companies_id'] = $companiesOne['companies_id'];
                    $balancelog_data['school_id'] = $this->school_id;
                    $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                    $balancelog_data['student_id'] = $student_id;
                    $balancelog_data['trading_pid'] = $tradingPid;
                    $balancelog_data['balancelog_class'] = 0;
                    $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课辅课程余额转账户余额');
                    $balancelog_data['balancelog_playclass'] = '+';
                    $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
                    $balancelog_data['balancelog_playamount'] = $studentOne['coursebalance_figure'];
                    $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $studentOne['coursebalance_figure'];
                    $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课辅课程余额转账户余额');
                    $balancelog_data['balancelog_time'] = $time;
                    $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);


                    $data = array();
                    $data['student_balance'] = $stublcOne['student_balance'] + $studentOne['coursebalance_figure'];
                    $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companiesOne['companies_id']}'", $data);

                    $student_coursebalance_data = array();
                    $student_coursebalance_data['coursebalance_figure'] = 0;
                    $student_coursebalance_data['coursebalance_time'] = 0;
                    $student_coursebalance_data['coursebalance_status'] = 3;
                    $student_coursebalance_data['coursebalance_updatatime'] = time();
                    $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

                    if ($dealorder_pid) {
                        $course_data = array();
                        $course_data['course_id'] = $course_id;
                        $course_data['dealorder_pid'] = $dealorder_pid;
                        $course_data['dealcourse_figure'] = $studentOne['coursebalance_figure'];
                        $course_data['dealcourse_time'] = $studentOne['coursebalance_time'];
                        $course_data['dealcourse_tobalanceprice'] = $studentOne['coursebalance_figure'];
                        $this->DataControl->insertData("smc_forward_dealorder_course", $course_data);
                    }

                    $sql = "select po.order_pid
                    from smc_payfee_order as po
                    left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                    where poc.course_id='{$course_id}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' 
                    and po.order_status>=0 and po.companies_id='{$companiesOne['companies_id']}' order by po.order_createtime desc";

                    $orderOne = $this->DataControl->selectOne($sql);

                    if ($orderOne) {
                        $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$orderOne['order_pid']}' and achieve_iscalculated=1");
                        if ($achieveList) {

                            $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $orderOne['order_pid']);

                            foreach ($achieveList as $achieveOne) {
                                $OrderHandleModel->totalPerformance($achieveOne['staffer_id'], $achieveOne['coursetype_id'], $achieveOne['performance_id'], 0, -$studentOne['coursebalance_figure'], $achieveOne['achieve_iscalculated'], 1, '扣除业绩', '课程结转扣除业绩');
                            }

                        }
                    }

                    if ($studentOne['coursebalance_figure'] > 0) {

                        $sql = "select po.order_pid
                      from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      where poc.course_id='{$course_id}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
                      and po.order_status>=0 and po.companies_id='{$companiesOne['companies_id']}'
                      ";

                        $orderOne = $this->DataControl->selectOne($sql);
                        if ($orderOne) {
                            $data = array();
                            $data['paytype'] = 'balance';
                            $data['paytimes'] = '1';
                            $data['order_pid'] = $orderOne['order_pid'];
                            $data['create_time'] = date("Y-m-d H:i:s", $time);

                            $tem_data = array();
                            $tem_data['allBalance'] = $studentOne['coursebalance_figure'];
                            $tem_data['allWithhold'] = 0;

                            $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                            $bool = $Model->createOrderPay($data, $tem_data);

                        }
                    }
                } else {
                    $this->error = true;
                    $this->errortip = "结转失败";
                    return false;
                }
            } else {
                if ($class_id) {
                    $sql = "select sc.*
                      from smc_student_courseshare as sc
                      where sc.coursebalance_id='{$studentOne['coursebalance_id']}' and sc.class_id='{$class_id}' and sc.courseshare_status='0'";
                    $monthList = $this->DataControl->selectClear($sql);
                    if ($monthList) {
                        foreach ($monthList as $one) {
                            $bool = $this->settlement($student_id, $class_id, $one['courseshare_month'], 2, $time);
                            if (!$bool) {
                                $this->error = true;
                                return false;
                            }
                        }
                    }
                } else {
                    $this->error = true;
                    $this->errortip = "结转失败";
                    return false;
                }

                $tradingPid = '';
            }
        }

        if (isset($class_id) && $class_id != '' && $class_id > 0 && $forwardNum == 0) {
            $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
            $TransactionModel->outClass($student_id, $class_id, $from, $time, $tradingPid);
        }

        if ($tradingPid == '') {
            $tradingPid = 'noCarry';
        }


        $sql = "select ssc.course_id
                  from smc_student_coursebalance as ssc
                  inner join smc_student as s on s.student_id=ssc.student_id
                  inner join smc_course as sc on sc.course_id=ssc.course_id
                  where sc.company_id='{$this->company_id}' and ssc.student_id='{$student_id}' and sc.main_course_id='{$course_id}' and sc.course_isfollow=1 and ssc.school_id='{$this->school_id}'
                  limit 0,1
                 ";
        $courseBalanceOne = $this->DataControl->selectOne($sql);
        if($courseBalanceOne){
            $this->carryOver($student_id, $courseBalanceOne['course_id'], 0, 2, $time,$forwardNum);
        }

        $this->error = true;
        $this->oktip = "结转成功";
        return $tradingPid;
    }

    function addReduceOrder($request)
    {
        $sql = "select a.school_id,a.course_id,b.course_inclasstype,b.course_checkingintype,b.course_checkingminday
                from smc_class a
                left join smc_course b on a.course_id=b.course_id
                where class_id='{$request['class_id']}'
                and course_inclasstype='0'
                and course_checkingintype='1'
                and course_checkingminday>0";
        $classOne = $this->DataControl->selectOne($sql);
        if (!$classOne) {
            //可能要返回数组
            return 0;
        }

        $sql = "select a.hourstudy_checkin
                ,a.hourstudy_id
                ,ifnull((select log_playtimes from smc_student_coursebalance_log 
                where student_id=a.student_id and class_id=a.class_id and hourstudy_id=a.hourstudy_id 
                and log_class=0 order by log_playtimes desc limit 0,1),0) as is_income
                from smc_student_hourstudy a
                left join smc_class_hour b on a.hour_id=b.hour_id
                where a.class_id='{$request['class_id']}'
                and a.student_id='{$request['student_id']}'
                and b.hour_isfree=0 
                order by b.hour_lessontimes DESC 
                limit 0,{$classOne['course_checkingminday']}";
        $hourstudyList = $this->DataControl->selectClear($sql);
        if (!$hourstudyList) {
            //可能要返回数组
            return 0;
        }
        $arr_checkin = array_column($hourstudyList, "hourstudy_checkin");

        $reduceTimes = 0;
        if (in_array('1', $arr_checkin) || count($arr_checkin) < $classOne['course_checkingminday']) {
            foreach ($arr_checkin as $k => $checkOne) {
                if ($checkOne['hourstudy_checkin'] == 1) {
                    break;
                } else {
                    if ($hourstudyList[$k]['is_income'] == 0) {
                        $reduceTimes += 1;
                    }
                }
            }
        } else {
            return 0;
        }

        if ($reduceTimes == 0) {
            return 0;
        }

        $sql = "select sum(log_playtimes) as reduced_times 
            from smc_student_coursebalance_log a
            where student_id='{$request['student_id']}'
            and course_id='{$classOne['course_id']}' 
            and school_id='{$classOne['school_id']}'
            and log_playclass='-'
            and log_playname='扣除课次' 
            and not exists(select 1 from smc_student_coursebalance_log 
            where student_id=a.student_id and course_id=a.course_id and school_id=a.school_id and log_id>a.log_id and log_playname<>'扣除课次')
            limit 0,1";
        $reduceTimes_yet = $this->DataControl->selectOne($sql);

        if ($reduceTimes_yet) {
            //去除存在已扣除的情况;
            $reduceTimes -= $reduceTimes_yet['reduced_times'];
        }

        return $reduceTimes;
    }

    function settlement($student_id, $class_id, $month, $status = 1, $time = '')
    {

//        $sql = "select ch.hour_id,ch.hour_day from smc_class_hour as ch
//              where ch.class_id='{$class_id}' and ch.hour_ischecking>=0 and ch.hour_day in (select h.holidays_day
//                        from smc_code_holidays as h
//                        where substring(h.holidays_day,1,7)='{$month}' and ((h.holidays_status='0' and h.school_id='{$this->school_id}')
//                        or (h.holidays_status='0' and h.company_id='{$this->company_id}' and h.school_id='0'
//                        and h.holidays_day not in (select hs.holidays_day from smc_code_holidays as hs where hs.school_id='{$this->school_id}' and hs.holidays_status='1'))))";
//        $one=$this->DataControl->selectOne($sql);
//        if ($one) {
//            $this->error = true;
//            $this->errortip = $one['hour_day']."假期存在排课,无法结算,请先修改排课";
//            return false;
//        }

        $sql = "select sc.course_id,sc.course_inclasstype,sc.course_minabsencenum,cc.coursetype_id,sc.coursecat_id,cc.coursecat_branch,sc.course_branch,sc.course_checkingintype,sc.course_checkingminday
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where c.class_id='{$class_id}'";
        $courseOne = $this->DataControl->selectOne($sql);

        $this->schoolOne = $this->DataControl->getOne("smc_school", "school_id='{$this->school_id}'");

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,scb.companies_id
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}'";
        $stuCourseOne = $this->DataControl->selectOne($sql);

        $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$class_id}' and courseshare_month='{$month}' and courseshare_status='0'");//学员当月分摊金额
        if (!$monthOne) {
            $this->error = true;
            $this->errortip = "无分摊金额";
            return false;
        }

        $sql = "select sch.hour_id,sch.hour_day,sch.hour_lessontimes
                  from smc_class_hour as sch
                  where substring(sch.hour_day,1,7)='{$month}' and sch.class_id='{$class_id}' and sch.hour_ischecking>=0";

        $hourList = $this->DataControl->selectClear($sql);//应该上课天数
        if (!$hourList) {
            $this->error = true;
            $this->errortip = "该班级未排课,不可结算";
            return false;
        }

        $hourNum = count($hourList);
        $tem_hour = array();
        foreach ($hourList as $hourOne) {
            $tem_hour[] = $hourOne['hour_day'];
        }

        $sql = "select sh.hourstudy_id,sch.hour_day,sch.hour_lessontimes
                  from smc_student_hourstudy as sh
                  left join smc_class_hour as sch on sch.hour_id=sh.hour_id
                  where substring(sch.hour_day,1,7)='{$month}' and sh.student_id='{$student_id}' and sh.hourstudy_checkin='1' and sh.class_id='{$class_id}' and sch.hour_ischecking>=0";
        $attendanceList = $this->DataControl->selectClear($sql);

        if ($courseOne['course_checkingintype'] == '1') {
            $hourList = array_column($hourList, 'hour_lessontimes');
            if ($attendanceList) {
                $attendanceList = array_column($attendanceList, 'hour_lessontimes');
            } else {
                $attendanceList = array();
            }

            $absence = array_diff($hourList, $attendanceList);
            $num = 0;
            $absent = array();

            if ($absence) {
                sort($absence);
                $i = 0;
                $k = $absence[$i];
                $num = 1;
                do {
                    if ($i == count($absence)) {
                        break;
                    }
                    $i++;
                    if ($absence[$i] == ($k + 1)) {
                        $num++;
                        $k++;
                    } else {
                        if ($num >= $courseOne['course_checkingminday']) {
                            array_push($absent, $num);
                        }
                        $k = $absence[$i];
                        $num = 1;
                    }
                } while ($k <= max($absence));
            }

            if ($absent) {
                $num = 0;
                foreach ($absent as $val) {
                    $num += $val;
                }
            } else {
                $num = 0;
            }

            if ($num > 0) {
                if ($hourNum == $num) {
                    $price = $monthOne['courseshare_price'];
                } else {
                    $price = floor($monthOne['courseshare_price'] / $hourNum) * $num;
                }
            } else {
                $price = '0';
            }
        } elseif ($courseOne['course_checkingintype'] == '2') {

            //自然周考勤
            $firstday = date('Y-m-01', strtotime($month . '-01'));
            $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));

            $dayList = $this->getDate($firstday, $lastday);//一个月的总日期

            $tem_day = array();
            $num = 0;
            foreach ($dayList as $dayOne) {
                $wk_day = date('w', strtotime(date('Y-m-01 00:00:00', strtotime($dayOne)))) ?: 7;//本月第一天周几
                $day = date('d', strtotime($dayOne)) - (8 - $wk_day);
                $week = $day <= 0 ? 1 : ceil($day / 7) + 1;
                $tem_day[$week][] = $dayOne;
            }

            $tem_nodateList = array_diff($dayList, $tem_hour);//休息日期

            foreach ($tem_day as $dayOne) {
                $sql = "select sh.hourstudy_id
                      from smc_student_hourstudy as sh
                      left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                      where sh.student_id='{$student_id}' and ch.hour_ischecking>=0 and sh.hourstudy_checkin='1' and ch.hour_day in ('" . implode("','", $dayOne) . "')";
                $studyOne = $this->DataControl->selectOne($sql);
                if (!$studyOne) {
                    $tem_data = array_diff($dayOne, $tem_nodateList);//缺勤天数
                    if ($tem_data) {
                        $num += count($tem_data);
                    }
                }
            }
            if ($num > 0) {
                if ($hourNum == $num) {
                    $price = $monthOne['courseshare_price'];
                } else {
                    $price = floor($monthOne['courseshare_price'] / $hourNum) * $num;
                }
            } else {
                $price = '0';
            }
        } elseif ($courseOne['course_checkingintype'] == '4') {
            //累计缺勤
            if ($attendanceList) {
                $attendanceNum = count($attendanceList);//上课天数
            } else {
                $attendanceNum = 0;
            }

            $num = $hourNum - $attendanceNum;

            if ($num >= $courseOne['course_minabsencenum']) {

                if ($hourNum <= 0) {
                    $price = 0;
                } else {
                    if ($hourNum == $num) {
                        $price = $monthOne['courseshare_price'];
                    } else {
                        $price = floor($monthOne['courseshare_price'] / $hourNum) * $num;
                    }
                }

            } else {
                $price = '0';
            }

        } else {
            $this->error = 1;
            $this->errortip = "课程月结方式不正确";
            return false;
        }

        //$price   是转入预收的金额

        if (!$time) {
            $time = strtotime($month . '+1 month -1day');
        }

        if (($monthOne['courseshare_price'] - $price) > 0) {
            $data = array();
            do {
                $random = $this->createOrderPid('MS');
            } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$random}' limit 0,1"));

            $data['trading_pid'] = $random;
            $data['company_id'] = $this->company_id;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $stuCourseOne['companies_id'];
            $data['student_id'] = $student_id;
            $data['tradingtype_code'] = 'MonthlyShare';
            $data['trading_status'] = "1";
            $data['trading_createtime'] = $time;
            $data['staffer_id'] = $this->staffer_id;
            $this->DataControl->insertData("smc_student_trading", $data);

            $in_data = array();
            $in_data['company_id'] = $this->company_id;
            $in_data['companies_id'] = $stuCourseOne['companies_id'];
            $in_data['school_id'] = $this->school_id;
            $in_data['income_type'] = '0';
            $in_data['student_id'] = $student_id;
            $in_data['class_id'] = $class_id;
            $in_data['course_id'] = $courseOne['course_id'];
            $in_data['trading_pid'] = $random;
            $in_data['income_price'] = $monthOne['courseshare_price'] - $price;
            $in_data['income_note'] = $this->LgStringSwitch($month . '月度结算');
            $in_data['income_confirmtime'] = (date('Y-m', $time) == date('Y-m')) ? $time : time();
            $in_data['income_audittime'] = $time;
            $in_data['income_createtime'] = time();
            $this->DataControl->insertData("smc_school_income", $in_data);
        } else {
//            do {
//                $random = $this->createStuRandom($studentOne['student_branch']);
//            } while ($this->DataControl->selectOne("select log_id from smc_student_coursebalance_log where log_random='{$random}' limit 0,1"));
            $random = $this->create_guid();
        }

        if ($price > 0) {
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $stuCourseOne['companies_id'];
            $courselog_data['course_id'] = $courseOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['trading_pid'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('月度结算余额');
            $courselog_data['log_playclass'] = '-';
            $courselog_data['log_fromamount'] = $stuCourseOne['coursebalance_figure'];
            $courselog_data['log_playamount'] = $price;
            $courselog_data['log_finalamount'] = $stuCourseOne['coursebalance_figure'] - $price;

            $courselog_data['log_fromtimes'] = $stuCourseOne['coursebalance_time'];
            $courselog_data['log_playtimes'] = 0;
            $courselog_data['log_finaltimes'] = $stuCourseOne['coursebalance_time'];

            $courselog_data['log_reason'] = $this->LgStringSwitch($month . '月度结算余额');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);
        }

        if (($monthOne['courseshare_price'] - $price) > 0) {
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $stuCourseOne['companies_id'];
            $courselog_data['course_id'] = $courseOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['trading_pid'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('月度结算收入');
            $courselog_data['log_playclass'] = '-';
            $courselog_data['log_fromamount'] = $stuCourseOne['coursebalance_figure'] - $price;
            $courselog_data['log_playamount'] = $monthOne['courseshare_price'] - $price;
            $courselog_data['log_finalamount'] = $stuCourseOne['coursebalance_figure'] - $price - ($monthOne['courseshare_price'] - $price);

            $courselog_data['log_fromtimes'] = $stuCourseOne['coursebalance_time'];
            $courselog_data['log_playtimes'] = 1;
            $courselog_data['log_finaltimes'] = $stuCourseOne['coursebalance_time'] - 1;

            $courselog_data['log_reason'] = $this->LgStringSwitch($month . '月度结算收入');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);


        }

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $stuCourseOne['companies_id'];
        $time_data['course_id'] = $courseOne['course_id'];
        $time_data['log_random'] = $random;
        $time_data['trading_pid'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('月度结算');
        $time_data['timelog_playclass'] = '-';
        $time_data['timelog_fromtimes'] = $stuCourseOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = 1;
        $time_data['timelog_finaltimes'] = $stuCourseOne['coursebalance_time'] - 1;
        $time_data['timelog_reason'] = $this->LgStringSwitch($month . '月度结算');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_time'] = $stuCourseOne['coursebalance_time'] - 1;
        $student_coursebalance_data['coursebalance_figure'] = $stuCourseOne['coursebalance_figure'] - $monthOne['courseshare_price'];
        if ($status == 2) {
            $student_coursebalance_data['coursebalance_status'] = $status;
        }
        $student_coursebalance_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$courseOne['course_id']}' and school_id='{$this->school_id}'", $student_coursebalance_data);


        if ($price > '0') {
            $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance"
                , "companies_id,coursecatbalance_figure,coursecatbalance_time", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$courseOne['coursecat_id']}' and coursetype_id='{$courseOne['coursetype_id']}' and feetype_code='Forward'");
            if ($stuCatOne) {
                $log = array();
                $log['student_id'] = $student_id;
                $log['coursetype_id'] = $courseOne['coursetype_id'];
                $log['coursecat_id'] = $courseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCourseOne['companies_id'];
                $log['staffer_id'] = $this->staffer_id;
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
                $log['log_playamount'] = $price;
                $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] + $price;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = $this->LgStringSwitch('月度结算');
                $log['log_reason'] = $this->LgStringSwitch($month . '月度结算');
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] + $price;
                $data['coursecatbalance_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_coursecatbalance", "coursetype_id='{$courseOne['coursetype_id']}' and student_id='{$student_id}' and school_id='{$this->school_id}' and feetype_code='Forward' and coursecat_id='{$courseOne['coursecat_id']}'", $data);
            } else {
                $log = array();
                $log['student_id'] = $student_id;
                $log['coursetype_id'] = $courseOne['coursetype_id'];
                $log['coursecat_id'] = $courseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCourseOne['companies_id'];
                $log['staffer_id'] = $this->staffer_id;
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = 0;
                $log['log_playamount'] = $price;
                $log['log_finalamount'] = $price;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = $this->LgStringSwitch('月度结算');
                $log['log_reason'] = $this->LgStringSwitch($month . '月度结算');
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['company_id'] = $this->company_id;
                $data['student_id'] = $student_id;
                $data['school_id'] = $this->school_id;
                $data['companies_id'] = $stuCourseOne['companies_id'];
                $data['feetype_code'] = 'Forward';
                $data['coursetype_id'] = $courseOne['coursetype_id'];
                $data['coursecat_id'] = $courseOne['coursecat_id'];
                $data['coursecat_branch'] = $courseOne['coursecat_branch'];
                $data['course_branch'] = $courseOne['course_branch'];
                $data['coursecatbalance_figure'] = $price;
                $data['coursecatbalance_createtime'] = $time;
                $this->DataControl->insertData("smc_student_coursecatbalance", $data);
            }

            $sql = "select po.order_pid
                    from smc_payfee_order as po
                    left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                    where poc.course_id='{$courseOne['course_id']}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' 
                    and po.order_status>=0 and po.companies_id='{$stuCourseOne['companies_id']}' order by po.order_createtime desc";

            $orderOne = $this->DataControl->selectOne($sql);

            if ($orderOne) {
                $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$orderOne['order_pid']}' and achieve_iscalculated=1");
                if ($achieveList) {

                    $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $orderOne['order_pid']);

                    foreach ($achieveList as $achieveOne) {
                        $OrderHandleModel->totalPerformance($achieveOne['staffer_id'], $achieveOne['coursetype_id'], $achieveOne['performance_id'], 0, -$price, $achieveOne['achieve_iscalculated'], 1, '扣除业绩', '课程结转扣除业绩');
                    }
                }
            }
        }

        $data = array();
        $data['courseshare_status'] = $status;
        $this->DataControl->updateData("smc_student_courseshare", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$class_id}' and courseshare_month='{$month}'", $data);


        if ($price > 0) {

            $sql = "select po.order_pid
                      from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      where poc.course_id='{$courseOne['course_id']}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
                      and po.order_status>=0 and po.companies_id='{$stuCourseOne['companies_id']}'
                      ";

            $orderOne = $this->DataControl->selectOne($sql);
            if ($orderOne) {
                $data = array();
                $data['paytype'] = 'balance';
                $data['paytimes'] = '1';
                $data['order_pid'] = $orderOne['order_pid'];
                $data['create_time'] = date("Y-m-d H:i:s", $time);

                $tem_data = array();
                $tem_data['allBalance'] = 0;
                $tem_data['allWithhold'] = 0;
                $tem_data['student_catbalance'] = $price;
                $tem_data['coursecat_id'] = $courseOne['coursecat_id'];

                $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                $bool = $Model->createOrderPay($data, $tem_data);

            }
        }

        return true;
    }


    function settlementInfo($student_id, $class_id, $month, $status = 1, $time = '')
    {
//        $sql = "select ch.hour_id,ch.hour_day from smc_class_hour as ch
//              where ch.class_id='{$class_id}' and ch.hour_ischecking>=0 and ch.hour_day in (select h.holidays_day
//                        from smc_code_holidays as h
//                        where substring(h.holidays_day,1,7)='{$month}' and ((h.holidays_status='0' and h.school_id='{$this->school_id}')
//                        or (h.holidays_status='0' and h.company_id='{$this->company_id}' and h.school_id='0'
//                        and h.holidays_day not in (select hs.holidays_day from smc_code_holidays as hs where hs.school_id='{$this->school_id}' and hs.holidays_status='1'))))";
//        $one=$this->DataControl->selectOne($sql);
//        if ($one) {
//            $this->error = true;
//            $this->errortip = $one['hour_day']."假期存在排课,无法结算,请先修改排课";
//            return false;
//        }

        $sql = "select share_id from smc_student_class_share where student_id='{$student_id}' and class_id='{$class_id}' and share_month='{$month}' and share_status=0";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "结算中,不可重复结算";
            return false;
        }

        $sql = "select sc.course_id,sc.course_inclasstype,sc.course_minabsencenum,cc.coursetype_id,sc.coursecat_id,cc.coursecat_branch,sc.course_branch,sc.course_checkingintype,sc.course_checkingminday
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where c.class_id='{$class_id}'";
        $courseOne = $this->DataControl->selectOne($sql);

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}'
              ";

        $stuCourseOne = $this->DataControl->selectOne($sql);

        $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_id,courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$class_id}' and courseshare_month='{$month}' and courseshare_status='0'");//学员当月分摊金额
        if (!$monthOne) {
            $this->error = true;
            $this->errortip = "无可分摊金额";
            return false;
        }

        $studyOne = $this->DataControl->getFieldOne("smc_student_study", "study_beginday", "student_id='{$student_id}' and class_id='{$class_id}'");
        if (!$studyOne) {
            $this->error = true;
            $this->errortip = "无学员在班数据";
            return false;
        }
        $sql = "select sch.hour_id,sch.hour_day,sch.hour_lessontimes
                  from smc_class_hour as sch
                  where substring(sch.hour_day,1,7)='{$month}' and sch.class_id='{$class_id}' and sch.hour_ischecking>=0 and sch.hour_day>='{$studyOne['study_beginday']}'";

        $hourList = $this->DataControl->selectClear($sql);//应该上课天数
        if (!$hourList) {
//            $this->error = true;
//            $this->errortip = "该班级未排课,不可结算";
//            return false;
            $price = $monthOne['courseshare_price'];
        } else {
            $hourNum = count($hourList);
            $tem_hour = array();
            foreach ($hourList as $hourOne) {
                $tem_hour[] = $hourOne['hour_day'];
            }

            $sql = "select sh.hourstudy_id,sch.hour_day,sch.hour_lessontimes
                  from smc_student_hourstudy as sh
                  left join smc_class_hour as sch on sch.hour_id=sh.hour_id
                  where substring(sch.hour_day,1,7)='{$month}' and sh.student_id='{$student_id}' and sh.hourstudy_checkin='1'
                  and sh.class_id='{$class_id}' and sch.hour_ischecking>=0";
            $attendanceList = $this->DataControl->selectClear($sql);

            if ($courseOne['course_checkingintype'] == '1') {
                $hourList = array_column($hourList, 'hour_lessontimes');
                if ($attendanceList) {
                    $attendanceList = array_column($attendanceList, 'hour_lessontimes');
                } else {
                    $attendanceList = array();
                }

                $absence = array_diff($hourList, $attendanceList);

                $num = 0;
                $absent = array();

                if ($absence) {
                    sort($absence);
                    $i = 0;
                    $k = $absence[$i];
                    $num = 1;
                    do {
                        if ($i == count($absence)) {
                            break;
                        }
                        $i++;
                        if ($absence[$i] == ($k + 1)) {
                            $num++;
                            $k++;
                        } else {
                            if ($num >= $courseOne['course_checkingminday']) {
                                array_push($absent, $num);
                            }
                            $k = $absence[$i];
                            $num = 1;
                        }
                    } while ($k <= max($absence));
                }

                if ($absent) {
                    $num = 0;
                    foreach ($absent as $val) {
                        $num += $val;
                    }
                } else {
                    $num = 0;
                }

                if ($num > 0) {
                    if ($hourNum == $num) {
                        $price = $monthOne['courseshare_price'];
                    } else {
                        $price = floor($monthOne['courseshare_price'] / $hourNum) * $num;
                    }
                } else {
                    $price = '0';
                }
            } elseif ($courseOne['course_checkingintype'] == '2') {

                //自然周考勤
                $firstday = date('Y-m-01', strtotime($month . '-01'));
                $lastday = date('Y-m-d', strtotime("$firstday +1 month -1 day"));

                $dayList = $this->getDate($firstday, $lastday);//一个月的总日期

                $tem_day = array();
                $num = 0;
                foreach ($dayList as $dayOne) {
                    $wk_day = date('w', strtotime(date('Y-m-01 00:00:00', strtotime($dayOne)))) ?: 7;//本月第一天周几
                    $day = date('d', strtotime($dayOne)) - (8 - $wk_day);
                    $week = $day <= 0 ? 1 : ceil($day / 7) + 1;
                    $tem_day[$week][] = $dayOne;
                }

                $tem_nodateList = array_diff($dayList, $tem_hour);//休息日期

                foreach ($tem_day as $dayOne) {
                    $sql = "select sh.hourstudy_id
                      from smc_student_hourstudy as sh
                      left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                      where sh.student_id='{$student_id}' and ch.hour_ischecking>=0 and sh.hourstudy_checkin='1' and ch.hour_day in ('" . implode("','", $dayOne) . "')";
                    $studyOne = $this->DataControl->selectOne($sql);
                    if (!$studyOne) {
                        $tem_data = array_diff($dayOne, $tem_nodateList);//缺勤天数
                        if ($tem_data) {
                            $num += count($tem_data);
                        }
                    }
                }
                if ($num > 0) {
                    if ($hourNum == $num) {
                        $price = $monthOne['courseshare_price'];
                    } else {
                        $price = floor($monthOne['courseshare_price'] / $hourNum) * $num;
                    }
                } else {
                    $price = '0';
                }
            } elseif ($courseOne['course_checkingintype'] == '4') {
                //累计缺勤
                if ($attendanceList) {
                    $attendanceNum = count($attendanceList);//上课天数
                } else {
                    $attendanceNum = 0;
                }

                $num = $hourNum - $attendanceNum;

                if ($num >= $courseOne['course_minabsencenum']) {

                    if ($hourNum <= 0) {
                        $price = 0;
                    } else {
                        if ($hourNum == $num) {
                            $price = $monthOne['courseshare_price'];
                        } else {
                            $price = floor($monthOne['courseshare_price'] / $hourNum) * $num;
                        }
                    }

                } else {
                    $price = '0';
                }

            } else {
                $this->error = 1;
                $this->errortip = "课程月结方式不正确";
                return false;
            }
        }
        $data = array();
        $data['price'] = $price;
        $data['num'] = $num;
        $data['courseshare_price'] = $monthOne['courseshare_price'];
        $data['settle_price'] = ($monthOne['courseshare_price'] - $price) > $stuCourseOne['coursebalance_figure'] ? $stuCourseOne['coursebalance_figure'] : $monthOne['courseshare_price'] - $price;
        $data['courseshare_id'] = $monthOne['courseshare_id'];
        $data['attendNum'] = $attendanceList ? count($attendanceList) : 0;
        $data['hourNum'] = $hourNum;
        $data['absenceNum'] = $hourNum ? $hourNum - $data['attendNum'] : 0;

        return $data;

    }

    function settleAction($student_id, $class_id, $month, $price = 0, $status = 1, $confirm_price = 0)
    {
        //结算已结束月份的，算在已结束的月底
        $time = strtotime(date('Y-m-01', strtotime('+1 month', strtotime($month)))) - 1;

        if ($time > time()) {
            //未到月底就结算的，算在当前时间
            $time = time();
        }

        $sql = "select sc.course_id,sc.course_inclasstype,sc.course_minabsencenum,cc.coursetype_id,sc.coursecat_id,cc.coursecat_branch,sc.course_branch,sc.course_checkingintype,sc.course_checkingminday
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where c.class_id='{$class_id}'";
        $courseOne = $this->DataControl->selectOne($sql);

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,scb.companies_id
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}'";
        $stuCourseOne = $this->DataControl->selectOne($sql);

        if ($confirm_price > 0) {
            $data = array();
            do {
                $random = $this->createOrderPid('MS');
            } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$random}' limit 0,1"));

            $data['trading_pid'] = $random;
            $data['company_id'] = $this->company_id;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $stuCourseOne['companies_id'];
            $data['student_id'] = $student_id;
            $data['tradingtype_code'] = 'MonthlyShare';
            $data['trading_status'] = "1";
            $data['trading_createtime'] = $time;
            $data['staffer_id'] = $this->staffer_id;
            $this->DataControl->insertData("smc_student_trading", $data);

            $in_data = array();
            $in_data['company_id'] = $this->company_id;
            $in_data['companies_id'] = $stuCourseOne['companies_id'];
            $in_data['school_id'] = $this->school_id;
            $in_data['income_type'] = '0';
            $in_data['student_id'] = $student_id;
            $in_data['class_id'] = $class_id;
            $in_data['course_id'] = $courseOne['course_id'];
            $in_data['trading_pid'] = $random;
            $in_data['income_price'] = $confirm_price;
            $in_data['income_note'] = $this->LgStringSwitch($month . '月度结算');
            $in_data['income_confirmtime'] = (date('Y-m', $time) == date('Y-m')) ? $time : time();
            $in_data['income_audittime'] = $time;
            $in_data['income_createtime'] = time();
            $this->DataControl->insertData("smc_school_income", $in_data);
        } else {
            $random = $this->create_guid();
        }

        if ($confirm_price > 0) {
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $stuCourseOne['companies_id'];
            $courselog_data['course_id'] = $courseOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['trading_pid'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('月度结算收入');
            $courselog_data['log_playclass'] = '-';
            $courselog_data['log_fromamount'] = $stuCourseOne['coursebalance_figure'];
            $courselog_data['log_playamount'] = $confirm_price;
            $courselog_data['log_finalamount'] = $stuCourseOne['coursebalance_figure'] - $confirm_price;

            $courselog_data['log_fromtimes'] = $stuCourseOne['coursebalance_time'];
            $courselog_data['log_playtimes'] = 1;
            $courselog_data['log_finaltimes'] = $stuCourseOne['coursebalance_time'] - 1;

            $courselog_data['log_reason'] = $this->LgStringSwitch($month . '月度结算收入');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        }

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $stuCourseOne['companies_id'];
        $time_data['course_id'] = $courseOne['course_id'];
        $time_data['log_random'] = $random;
        $time_data['trading_pid'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('月度结算');
        $time_data['timelog_playclass'] = '-';
        $time_data['timelog_fromtimes'] = $stuCourseOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = 1;
        $time_data['timelog_finaltimes'] = $stuCourseOne['coursebalance_time'] - 1;
        $time_data['timelog_reason'] = $this->LgStringSwitch($month . '月度结算');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_time'] = $stuCourseOne['coursebalance_time'] - 1;
        $student_coursebalance_data['coursebalance_figure'] = $stuCourseOne['coursebalance_figure'] - $confirm_price;
        if ($status == 2) {
            $student_coursebalance_data['coursebalance_status'] = $status;
        }
        $student_coursebalance_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$courseOne['course_id']}' and school_id='{$this->school_id}'", $student_coursebalance_data);

        $data = array();
        $data['courseshare_status'] = $status;
        $data['courseshare_price'] = $confirm_price;
        $this->DataControl->updateData("smc_student_courseshare", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$class_id}' and courseshare_month='{$month}'", $data);

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}'";

        $stuCourseBalanceOne = $this->DataControl->selectOne($sql);

        if ($stuCourseBalanceOne['coursebalance_time'] == 0 && $stuCourseBalanceOne['coursebalance_figure'] > 0) {
            $price = $stuCourseBalanceOne['coursebalance_figure'];
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $stuCourseOne['companies_id'];
            $courselog_data['course_id'] = $courseOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['trading_pid'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('月度结算余额');
            $courselog_data['log_playclass'] = '-';
            $courselog_data['log_fromamount'] = $price;
            $courselog_data['log_playamount'] = $price;
            $courselog_data['log_finalamount'] = 0;
            $courselog_data['log_fromtimes'] = 0;
            $courselog_data['log_playtimes'] = 0;
            $courselog_data['log_finaltimes'] = 0;
            $courselog_data['log_reason'] = $this->LgStringSwitch('月度结算余额');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);


            $data = array();
            $data['coursebalance_figure'] = 0;
            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$courseOne['course_id']}' and school_id='{$this->school_id}'", $data);
            $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,companies_id"
                , "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$courseOne['coursecat_id']}' and feetype_code='Forward'");

            if ($stuCatOne) {
                $log = array();
                $log['student_id'] = $student_id;
                $log['coursetype_id'] = $courseOne['coursetype_id'];
                $log['coursecat_id'] = $courseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCatOne['companies_id'];
                $log['staffer_id'] = $this->staffer_id;
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
                $log['log_playamount'] = $price;
                $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] + $price;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = $this->LgStringSwitch('月度结算');
                $log['log_reason'] = $this->LgStringSwitch('月度结算');
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['coursetype_id'] = $courseOne['coursetype_id'];
                $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] + $price;
                $data['coursecatbalance_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_coursecatbalance", "coursetype_id='{$courseOne['coursetype_id']}' and student_id='{$student_id}' and school_id='{$this->school_id}' and feetype_code='Forward' and coursecat_id='{$courseOne['coursecat_id']}'", $data);
            } else {
                $log = array();
                $log['student_id'] = $student_id;
                $log['coursetype_id'] = $courseOne['coursetype_id'];
                $log['coursecat_id'] = $courseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCourseOne['companies_id'];
                $log['staffer_id'] = $this->staffer_id;
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = 0;
                $log['log_playamount'] = $price;
                $log['log_finalamount'] = $price;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = $this->LgStringSwitch('月度结算');
                $log['log_reason'] = $this->LgStringSwitch('月度结算');
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['company_id'] = $this->company_id;
                $data['student_id'] = $student_id;
                $data['school_id'] = $this->school_id;
                $data['companies_id'] = $stuCourseOne['companies_id'];
                $data['feetype_code'] = 'Forward';
                $data['coursetype_id'] = $courseOne['coursetype_id'];
                $data['coursecat_id'] = $courseOne['coursecat_id'];
                $data['coursecat_branch'] = $courseOne['coursecat_branch'];
                $data['course_branch'] = $courseOne['course_branch'];
                $data['coursecatbalance_figure'] = $price;
                $data['coursecatbalance_createtime'] = $time;
                $this->DataControl->insertData("smc_student_coursecatbalance", $data);
            }

            if ($price > 0) {
                $sql = "select po.order_pid
                    from smc_payfee_order as po
                    left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                    where poc.course_id='{$courseOne['course_id']}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' 
                    and po.order_status>=0 and po.companies_id='{$stuCourseOne['companies_id']}' order by po.order_createtime desc";

                $orderOne = $this->DataControl->selectOne($sql);

                if ($orderOne) {
                    $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$orderOne['order_pid']}' and achieve_iscalculated=1");
                    if ($achieveList) {

                        $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $orderOne['order_pid']);

                        foreach ($achieveList as $achieveOne) {
                            $OrderHandleModel->totalPerformance($achieveOne['staffer_id'], $achieveOne['coursetype_id'], $achieveOne['performance_id'], 0, -$price, $achieveOne['achieve_iscalculated'], 1, '扣除业绩', '课程结转扣除业绩');
                        }
                    }
                }
            }


            $sql = "select po.order_pid
                      from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      where poc.course_id='{$courseOne['course_id']}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
                      and po.order_status>=0 and po.companies_id='{$stuCourseOne['companies_id']}'";

            $orderOne = $this->DataControl->selectOne($sql);
            if ($orderOne) {
                $data = array();
                $data['paytype'] = 'balance';
                $data['paytimes'] = '1';
                $data['order_pid'] = $orderOne['order_pid'];
                $data['create_time'] = date("Y-m-d H:i:s", $time);

                $tem_data = array();
                $tem_data['allBalance'] = 0;
                $tem_data['allWithhold'] = 0;
                $tem_data['student_catbalance'] = $price;
                $tem_data['coursecat_id'] = $courseOne['coursecat_id'];
                $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                $Model->createOrderPay($data, $tem_data);
            }
        }

        return true;

    }


    function settleAction_bak($student_id, $class_id, $month, $price = 0, $status = 1)
    {
        //结算已结束月份的，算在已结束的月底
        $time = strtotime(date('Y-m-01', strtotime('+1 month', strtotime($month)))) - 1;

        if ($time > time()) {
            //未到月底就结算的，算在当前时间
            $time = time();
        }

        if ($price < 0) {
            $price = 0;
        }

        $sql = "select sc.course_id,sc.course_inclasstype,sc.course_minabsencenum,cc.coursetype_id,sc.coursecat_id,cc.coursecat_branch,sc.course_branch,sc.course_checkingintype,sc.course_checkingminday
              from smc_class as c
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where c.class_id='{$class_id}'";
        $courseOne = $this->DataControl->selectOne($sql);

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,scb.companies_id
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}'";
        $stuCourseOne = $this->DataControl->selectOne($sql);

        $monthOne = $this->DataControl->getFieldOne("smc_student_courseshare", "courseshare_id,courseshare_price", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$class_id}' and courseshare_month='{$month}' and courseshare_status='0'");//学员当月分摊金额
        if (($monthOne['courseshare_price'] - $price) > 0) {
            $data = array();
            do {
                $random = $this->createOrderPid('MS');
            } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$random}' limit 0,1"));

            $data['trading_pid'] = $random;
            $data['company_id'] = $this->company_id;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $stuCourseOne['companies_id'];
            $data['student_id'] = $student_id;
            $data['tradingtype_code'] = 'MonthlyShare';
            $data['trading_status'] = "1";
            $data['trading_createtime'] = $time;
            $data['staffer_id'] = $this->staffer_id;
            $this->DataControl->insertData("smc_student_trading", $data);

            $in_data = array();
            $in_data['company_id'] = $this->company_id;
            $in_data['companies_id'] = $stuCourseOne['companies_id'];
            $in_data['school_id'] = $this->school_id;
            $in_data['income_type'] = '0';
            $in_data['student_id'] = $student_id;
            $in_data['class_id'] = $class_id;
            $in_data['course_id'] = $courseOne['course_id'];
            $in_data['trading_pid'] = $random;
            $in_data['income_price'] = $monthOne['courseshare_price'] - $price;
            $in_data['income_note'] = $this->LgStringSwitch($month . '月度结算');
            $in_data['income_confirmtime'] = (date('Y-m', $time) == date('Y-m')) ? $time : time();
            $in_data['income_audittime'] = $time;
            $in_data['income_createtime'] = time();
            $this->DataControl->insertData("smc_school_income", $in_data);
        } else {
            $random = $this->create_guid();
        }

        if (($monthOne['courseshare_price'] - $price) > 0) {
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $stuCourseOne['companies_id'];
            $courselog_data['course_id'] = $courseOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['trading_pid'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('月度结算收入');
            $courselog_data['log_playclass'] = '-';
            $courselog_data['log_fromamount'] = $stuCourseOne['coursebalance_figure'];
            $courselog_data['log_playamount'] = $monthOne['courseshare_price'] - $price;
            $courselog_data['log_finalamount'] = $stuCourseOne['coursebalance_figure'] - ($monthOne['courseshare_price'] - $price);

            $courselog_data['log_fromtimes'] = $stuCourseOne['coursebalance_time'];
            $courselog_data['log_playtimes'] = 1;
            $courselog_data['log_finaltimes'] = $stuCourseOne['coursebalance_time'] - 1;

            $courselog_data['log_reason'] = $this->LgStringSwitch($month . '月度结算收入');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        }

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $stuCourseOne['companies_id'];
        $time_data['course_id'] = $courseOne['course_id'];
        $time_data['log_random'] = $random;
        $time_data['trading_pid'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('月度结算');
        $time_data['timelog_playclass'] = '-';
        $time_data['timelog_fromtimes'] = $stuCourseOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = 1;
        $time_data['timelog_finaltimes'] = $stuCourseOne['coursebalance_time'] - 1;
        $time_data['timelog_reason'] = $this->LgStringSwitch($month . '月度结算');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_time'] = $stuCourseOne['coursebalance_time'] - 1;
        $student_coursebalance_data['coursebalance_figure'] = $stuCourseOne['coursebalance_figure'] - $monthOne['courseshare_price'] + $price;
        if ($status == 2) {
            $student_coursebalance_data['coursebalance_status'] = $status;
        }
        $student_coursebalance_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$courseOne['course_id']}' and school_id='{$this->school_id}'", $student_coursebalance_data);

        $data = array();
        $data['courseshare_status'] = $status;
        $this->DataControl->updateData("smc_student_courseshare", "coursebalance_id='{$stuCourseOne['coursebalance_id']}' and class_id='{$class_id}' and courseshare_month='{$month}'", $data);

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,scb.companies_id
              from smc_student_coursebalance as scb
              where scb.school_id='{$this->school_id}' and scb.student_id='{$student_id}' and scb.course_id='{$courseOne['course_id']}'";
        $stuCourseBalanceOne = $this->DataControl->selectOne($sql);
        if ($stuCourseBalanceOne['coursebalance_time'] == 0 && $stuCourseBalanceOne['coursebalance_figure'] > 0) {
            $price = $stuCourseBalanceOne['coursebalance_figure'];
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $stuCourseBalanceOne['companies_id'];
            $courselog_data['course_id'] = $courseOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['trading_pid'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('月度结算余额');
            $courselog_data['log_playclass'] = '-';
            $courselog_data['log_fromamount'] = $price;
            $courselog_data['log_playamount'] = $price;
            $courselog_data['log_finalamount'] = 0;

            $courselog_data['log_fromtimes'] = 0;
            $courselog_data['log_playtimes'] = 0;
            $courselog_data['log_finaltimes'] = 0;

            $courselog_data['log_reason'] = $this->LgStringSwitch('月度结算余额');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);


            $data = array();
            $data['coursebalance_figure'] = 0;
            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$courseOne['course_id']}' and school_id='{$this->school_id}'", $data);

            $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursecatbalance_figure,coursecatbalance_time,companies_id", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$courseOne['coursecat_id']}' and feetype_code='Forward'");
            if ($stuCatOne) {
                $log = array();
                $log['student_id'] = $student_id;
                $log['coursetype_id'] = $courseOne['coursetype_id'];
                $log['coursecat_id'] = $courseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCatOne['companies_id'];
                $log['staffer_id'] = $this->staffer_id;
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
                $log['log_playamount'] = $price;
                $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] + $price;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = $this->LgStringSwitch('月度结算');
                $log['log_reason'] = $this->LgStringSwitch('月度结算');
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['coursetype_id'] = $courseOne['coursetype_id'];
                $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] + $price;
                $data['coursecatbalance_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_coursecatbalance", "coursetype_id='{$courseOne['coursetype_id']}' and student_id='{$student_id}' and school_id='{$this->school_id}' and feetype_code='Forward' and coursecat_id='{$courseOne['coursecat_id']}'", $data);
            } else {
                $log = array();
                $log['student_id'] = $student_id;
                $log['coursetype_id'] = $courseOne['coursetype_id'];
                $log['coursecat_id'] = $courseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCourseBalanceOne['companies_id'];
                $log['staffer_id'] = $this->staffer_id;
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = 0;
                $log['log_playamount'] = $price;
                $log['log_finalamount'] = $price;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = $this->LgStringSwitch('月度结算');
                $log['log_reason'] = $this->LgStringSwitch('月度结算');
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['company_id'] = $this->company_id;
                $data['student_id'] = $student_id;
                $data['school_id'] = $this->school_id;
                $data['companies_id'] = $stuCourseBalanceOne['companies_id'];
                $data['feetype_code'] = 'Forward';
                $data['coursetype_id'] = $courseOne['coursetype_id'];
                $data['coursecat_id'] = $courseOne['coursecat_id'];
                $data['coursecat_branch'] = $courseOne['coursecat_branch'];
                $data['course_branch'] = $courseOne['course_branch'];
                $data['coursecatbalance_figure'] = $price;
                $data['coursecatbalance_createtime'] = $time;
                $this->DataControl->insertData("smc_student_coursecatbalance", $data);
            }

            $sql = "select po.order_pid from smc_payfee_order as po
                      left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
                      where poc.course_id='{$courseOne['course_id']}' and po.student_id='{$student_id}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
                      and po.order_status>=0 and po.companies_id='{$stuCourseBalanceOne['companies_id']}'";
            $orderOne = $this->DataControl->selectOne($sql);
            if ($orderOne) {
                $data = array();
                $data['paytype'] = 'balance';
                $data['paytimes'] = '1';
                $data['order_pid'] = $orderOne['order_pid'];
                $data['create_time'] = date("Y-m-d H:i:s", $time);

                $tem_data = array();
                $tem_data['allBalance'] = 0;
                $tem_data['allWithhold'] = 0;
                $tem_data['student_catbalance'] = $price;
                $tem_data['coursecat_id'] = $courseOne['coursecat_id'];

                $Model = new \Model\Smc\OrderPayModel($this->publicarray, $orderOne['order_pid']);
                $Model->createOrderPay($data, $tem_data);
            }
        }

        return true;

    }

    //结转   -- 按课次结转---不用
    function classTransfer($student_id, $course_id, $class_id, $surplusPrice, $oldPrice, $num, $from = 0, $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $DealModel = new \Model\Smc\DealModel($this->publicarray);
        $dealorder_pid = $DealModel->dealorder($student_id, $surplusPrice, 0, $course_id, $this->LgStringSwitch('创建课次结转订单'), $this->LgStringSwitch('课次结转订单提交成功，订单完成'), '');

        $dealOne = $this->DataControl->getFieldOne("smc_forward_dealorder", "trading_pid", "dealorder_pid='{$dealorder_pid}'");

        $trading_pid = $dealOne['trading_pid'];

        $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
        $TransactionModel->outClass($student_id, $class_id, $from, $time, $trading_pid);
//        $schoolOne = $this->DataControl->getFieldOne("smc_school","companies_id","school_id = '{$this->school_id}' and company_id='{$this->company_id}'");
        $stu_coursebalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure,coursebalance_unitrefund,coursebalance_time", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'");

        $stu_courseforwardOne = $this->DataControl->getFieldOne("smc_student_courseforward", "courseforward_price", "student_id='{$student_id}' and course_id='{$course_id}'");

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0, $course_id);

        $coursebalancelog_data = array();
        $coursebalancelog_data['student_id'] = $student_id;
        $coursebalancelog_data['school_id'] = $this->school_id;
        $coursebalancelog_data['companies_id'] = $companiesOne['companies_id'];
        $coursebalancelog_data['class_id'] = $class_id;
        $coursebalancelog_data['log_class'] = 0;
        $coursebalancelog_data['school_id'] = $this->school_id;
        $coursebalancelog_data['class_id'] = $class_id;
        $coursebalancelog_data['course_id'] = $course_id;
        $coursebalancelog_data['trading_pid'] = $trading_pid;
        $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('课程结转余额结转');
        $coursebalancelog_data['log_playclass'] = '-';
        $coursebalancelog_data['log_fromamount'] = $stu_coursebalanceOne['coursebalance_figure'];
        $coursebalancelog_data['log_playamount'] = $stu_coursebalanceOne['coursebalance_figure'];
        $coursebalancelog_data['log_finalamount'] = 0;
        $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('课程结转余额结转');
        $coursebalancelog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

        $courselog_data = array();
        $courselog_data['student_id'] = $student_id;
        $courselog_data['log_class'] = 1;
        $courselog_data['course_id'] = $course_id;
        $courselog_data['school_id'] = $this->school_id;
        $courselog_data['companies_id'] = $companiesOne['companies_id'];
        $courselog_data['class_id'] = $class_id;
        $courselog_data['trading_pid'] = $trading_pid;
        $courselog_data['log_playname'] = $this->LgStringSwitch('课程余额结转');
        $courselog_data['log_playclass'] = '-';
        $courselog_data['log_fromamount'] = $stu_courseforwardOne['courseforward_price'];
        $courselog_data['log_playamount'] = $stu_courseforwardOne['courseforward_price'];
        $courselog_data['log_finalamount'] = 0;

        $courselog_data['log_fromtimes'] = $stu_coursebalanceOne['coursebalance_time'];
        $courselog_data['log_playtimes'] = $stu_coursebalanceOne['coursebalance_time'];
        $courselog_data['log_finaltimes'] = 0;

        $courselog_data['log_reason'] = $this->LgStringSwitch('课程余额结转');
        $courselog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_figure'] = 0;
        $student_coursebalance_data['coursebalance_time'] = 0;
        $student_coursebalance_data['coursebalance_status'] = 3;
        $student_coursebalance_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

        $student_courseforward_data = array();
        $student_courseforward_data['courseforward_price'] = 0;
        $student_courseforward_data['courseforward_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $student_courseforward_data);

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['course_id'] = $course_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $companiesOne['companies_id'];
        $time_data['class_id'] = $class_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['trading_pid'] = $trading_pid;
        $time_data['timelog_playname'] = $this->LgStringSwitch('课次结转');
        $time_data['timelog_playclass'] = '-';
        $time_data['timelog_fromtimes'] = $stu_coursebalanceOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = $stu_coursebalanceOne['coursebalance_time'];
        $time_data['timelog_finaltimes'] = 0;
        $time_data['timelog_reason'] = $this->LgStringSwitch('转班时课次结转');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);


        $stublcOne = $this->getStuBalance($student_id, $this->company_id, $this->school_id, $companiesOne['companies_id']);

        $data = array();
        $data['student_balance'] = $stublcOne['student_balance'] + $surplusPrice;
        $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companiesOne['companies_id']}'", $data);

        $balancelog_data = array();
        $balancelog_data['company_id'] = $this->company_id;
        $balancelog_data['companies_id'] = $companiesOne['companies_id'];
        $balancelog_data['school_id'] = $this->school_id;
        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $balancelog_data['student_id'] = $student_id;
        $balancelog_data['trading_pid'] = $trading_pid;
        $balancelog_data['balancelog_class'] = 0;
        $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('课程余额转账户余额');
        $balancelog_data['balancelog_playclass'] = '+';
        $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
        $balancelog_data['balancelog_playamount'] = $surplusPrice;
        $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $surplusPrice;
        $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('课程余额转账户余额');
        $balancelog_data['balancelog_time'] = $time;
        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);


        $course_data = array();
        $course_data['course_id'] = $course_id;
        $course_data['dealorder_pid'] = $dealorder_pid;
        $course_data['dealcourse_figure'] = $surplusPrice;
        $course_data['dealcourse_time'] = $num;
        $course_data['dealcourse_fromforwardprice'] = 0;
        $course_data['dealcourse_tobalanceprice'] = $surplusPrice;
        $course_data['dealcourse_toforwardprice'] = 0;
        $this->DataControl->insertData("smc_forward_dealorder_course", $course_data);


        if ($surplusPrice != $oldPrice) {
            $data = array();

            $data['trading_pid'] = $trading_pid;
            $data['company_id'] = $this->company_id;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $companiesOne['companies_id'];
            $data['student_id'] = $student_id;
            $data['tradingtype_code'] = 'Subscribed';
            $data['trading_status'] = "1";
            $data['trading_createtime'] = $time;
            $data['staffer_id'] = $this->stafferOne['staffer_id'];
            $this->DataControl->insertData("smc_student_trading", $data);

            if ($surplusPrice < $oldPrice) {
                $in_data = array();
                $in_data['company_id'] = $this->company_id;
                $in_data['companies_id'] = $companiesOne['companies_id'];
                $in_data['school_id'] = $this->school_id;
                $in_data['income_type'] = '1';
                $in_data['student_id'] = $student_id;
                $in_data['trading_pid'] = $trading_pid;
                $in_data['income_price'] = $oldPrice - $surplusPrice;
                $in_data['income_note'] = $this->LgStringSwitch('课程结转认缴收入');
                $in_data['income_confirmtime'] = $time;
                $in_data['income_audittime'] = $time;
                $in_data['income_createtime'] = time();
                $this->DataControl->insertData("smc_school_income", $in_data);
            } else {
                $out_data = array();
                $out_data['company_id'] = $this->company_id;
                $out_data['companies_id'] = $companiesOne['companies_id'];
                $out_data['school_id'] = $this->school_id;
                $out_data['expend_type'] = '1';
                $out_data['student_id'] = $student_id;
                $out_data['trading_pid'] = $trading_pid;
                $out_data['expend_price'] = $surplusPrice - $oldPrice;
                $out_data['expend_note'] = $this->LgStringSwitch('课程结转认缴支出');
                $out_data['expend_confirmtime'] = $time;
                $out_data['expend_createtime'] = $time;
                $this->DataControl->insertData("smc_school_expend", $out_data);
            }
        }

        $this->error = true;
        $this->oktip = "课次结转成功";
        return true;

    }

    //购买课程

    /**
     * @param $order_pid
     * @param $student_id
     * @param $course_id
     * @param $playamount
     * @param $coursebalance_time
     * @param $forward
     * @param $coupon_price
     * @param $market_price
     * @param $unitexpend
     * @param $unitrefund
     * @param $deductionmethod
     * @return bool
     */
    function buyCourse($order_pid, $student_id, $course_id, $pricing_id, $playamount, $coursebalance_time, $forward = 0, $coupon_price = 0, $market_price = 0, $unitexpend, $unitrefund, $deductionmethod, $class_id = 0, $enterclassdate = '', $isIn = 0, $from = 0, $monthArray = array(), $time = '')
    {
        //$companiesOne = $this->getSchoolCourseCompanies($this->school_id,0,$course_id);

        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }

        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "trading_pid,companies_id", "order_pid='{$order_pid}'");
        $trading_pid = $orderOne['trading_pid'];

        if ($coursebalance_time != 0) {
            $unitexpend = ceil(($playamount + $coupon_price + $forward) / $coursebalance_time);
            $unitearning = ceil($playamount / $coursebalance_time);
        } else {
            $unitexpend = 0;
            $unitearning = 0;
        }

        $policyOne = $this->getStuDiscountPrice($this->publicarray, $student_id, $course_id);
        if ($policyOne) {
            $unitrefund = $policyOne['unitrefund'];
        }
        $studentOne = $this->DataControl->getFieldOne('smc_student', "student_branch,from_client_id", "student_id='{$student_id}' ");
        if (!$this->DataControl->selectOne("select co.coursebalance_id, s.student_branch,s.from_client_id
                                           from smc_student_coursebalance as co
				                           left join smc_student as s ON s.student_id = s.student_id
			                               where co.school_id ='{$this->school_id}' and co.student_id='{$student_id}'
			                               and s.from_client_id > 0 limit 0,1 ")) {

            $converData = array();
            $courseOne = $this->DataControl->getFieldOne('smc_course', "course_branch", "course_id='{$course_id}'");
            $converData['course_branch'] = $courseOne['course_branch'];
            $this->DataControl->updateData("crm_client_conversionlog", "student_branch='{$studentOne['student_branch']}' and client_id='{$studentOne['from_client_id']}'  ", $converData);
        }

        if ($from == 0) {
            if ($this->DataControl->selectOne("select ssc.coursebalance_id from smc_student_coursebalance as ssc,smc_course as x
where ssc.course_id=x.course_id and ssc.student_id='{$student_id}' and ssc.course_id='{$course_id}' and x.course_sellclass=0 and (ssc.coursebalance_figure > 0 or ssc.coursebalance_time>0)")) {
                $this->error = true;
                $this->errortip = "课程已购买不可再次购买";
                return false;
            }
        }

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_createtime,scb.companies_id from smc_student_coursebalance as scb
where scb.student_id='{$student_id}' and scb.school_id='{$this->school_id}' and scb.course_id='{$course_id}'";
        $courseBalanceOne = $this->DataControl->selectOne($sql);

        $sql = "select scf.courseforward_price,scf.courseforward_time from smc_student_courseforward as scf where scf.student_id='{$student_id}' and scf.course_id='{$course_id}'";
        $courseForwardOne = $this->DataControl->selectOne($sql);

        $random = $this->create_guid();
        if ($courseBalanceOne) {
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $orderOne['companies_id'];
            $courselog_data['course_id'] = $course_id;
            $courselog_data['class_id'] = $class_id;
            $courselog_data['trading_pid'] = $trading_pid;
            $courselog_data['log_random'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('购买课程增加课程余额');
            $courselog_data['log_playclass'] = '+';
            $courselog_data['log_fromamount'] = $courseBalanceOne['coursebalance_figure'];
            $courselog_data['log_playamount'] = $playamount;
            $courselog_data['log_finalamount'] = $courseBalanceOne['coursebalance_figure'] + $playamount;

            $courselog_data['log_fromtimes'] = $courseBalanceOne['coursebalance_time'];
            $courselog_data['log_playtimes'] = $coursebalance_time;
            $courselog_data['log_finaltimes'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;

            $courselog_data['log_reason'] = $this->LgStringSwitch('购买课程增加课程余额');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);
            if (($forward + $coupon_price + $market_price) > 0) {
                $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$course_id}'");

                if ($courseOne['course_inclasstype'] != '1') {
                    if (($forward + $coupon_price + $market_price) > 0) {
                        if ($courseForwardOne) {
                            $coursebalancelog_data = array();
                            $coursebalancelog_data['student_id'] = $student_id;
                            $coursebalancelog_data['school_id'] = $this->school_id;
                            $coursebalancelog_data['companies_id'] = $orderOne['companies_id'];
                            $coursebalancelog_data['class_id'] = $class_id;
                            $coursebalancelog_data['log_class'] = 1;
                            $coursebalancelog_data['course_id'] = $course_id;
                            $coursebalancelog_data['trading_pid'] = $trading_pid;
                            $coursebalancelog_data['log_random'] = $random;
                            $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('购买课程增加课程结转金额');
                            $coursebalancelog_data['log_playclass'] = '+';
                            $coursebalancelog_data['log_fromamount'] = $courseForwardOne['courseforward_price'];
                            $coursebalancelog_data['log_playamount'] = $forward + $coupon_price + $market_price;
                            $coursebalancelog_data['log_finalamount'] = $courseForwardOne['courseforward_price'] + $forward + $coupon_price + $market_price;

                            $coursebalancelog_data['log_fromtimes'] = $courseBalanceOne['coursebalance_time'];
                            $coursebalancelog_data['log_playtimes'] = $coursebalance_time;
                            $coursebalancelog_data['log_finaltimes'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;

                            $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('购买课程增加课程结转金额');
                            $coursebalancelog_data['log_time'] = $time;
                            $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

                            $data = array();
                            $data['courseforward_price'] = $courseForwardOne['courseforward_price'] + $forward + $coupon_price + $market_price;
                            $data['courseforward_updatatime'] = $time;
                            $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $data);

                        } else {
                            $coursebalancelog_data = array();
                            $coursebalancelog_data['student_id'] = $student_id;
                            $coursebalancelog_data['school_id'] = $this->school_id;
                            $coursebalancelog_data['companies_id'] = $orderOne['companies_id'];
                            $coursebalancelog_data['class_id'] = $class_id;
                            $coursebalancelog_data['log_class'] = 1;
                            $coursebalancelog_data['course_id'] = $course_id;
                            $coursebalancelog_data['trading_pid'] = $trading_pid;
                            $coursebalancelog_data['log_random'] = $random;
                            $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('购买课程增加课程结转金额');
                            $coursebalancelog_data['log_playclass'] = '+';
                            $coursebalancelog_data['log_fromamount'] = $courseForwardOne['courseforward_price'];
                            $coursebalancelog_data['log_playamount'] = $forward + $coupon_price + $market_price;
                            $coursebalancelog_data['log_finalamount'] = $courseForwardOne['courseforward_price'] + $forward + $coupon_price + $market_price;

                            $coursebalancelog_data['log_fromtimes'] = $courseBalanceOne['coursebalance_time'];
                            $coursebalancelog_data['log_playtimes'] = $coursebalance_time;
                            $coursebalancelog_data['log_finaltimes'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;

                            $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('购买课程增加课程结转金额');
                            $coursebalancelog_data['log_time'] = $time;
                            $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);
                            $data = array();
                            $data['student_id'] = $student_id;
                            $data['course_id'] = $course_id;
                            $data['courseforward_deductionmethod'] = $deductionmethod;
                            $data['courseforward_time'] = $courseForwardOne['courseforward_time'] + $coursebalance_time;
                            $data['courseforward_price'] = $courseForwardOne['courseforward_price'] + $forward + $coupon_price + $market_price;
                            $data['courseforward_createtime'] = $time;
                            $this->DataControl->insertData("smc_student_courseforward", $data);
                        }
                    }
                }
            }


            $time_data = array();
            $time_data['student_id'] = $student_id;
            $time_data['school_id'] = $this->school_id;
            $time_data['companies_id'] = $orderOne['companies_id'];
            $time_data['course_id'] = $course_id;
            $time_data['class_id'] = $class_id;
            $time_data['trading_pid'] = $trading_pid;
            $time_data['log_random'] = $random;
            $time_data['timelog_playname'] = $this->LgStringSwitch('购买课程增加课次');
            $time_data['timelog_playclass'] = '+';
            $time_data['timelog_fromtimes'] = $courseBalanceOne['coursebalance_time'];
            $time_data['timelog_playtimes'] = $coursebalance_time;
            $time_data['timelog_finaltimes'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;
            $time_data['timelog_reason'] = $this->LgStringSwitch('购买课程增加课次');
            $time_data['timelog_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

            $sql = "select coursetimes_id from smc_student_free_coursetimes where student_id='{$student_id}' and class_id='{$class_id}' and is_use='0'";

            $freeList = $this->DataControl->selectClear($sql);

            if ($freeList) {
                $num = count($freeList);
            } else {
                $num = 0;
            }
            $all_price = $courseBalanceOne['coursebalance_figure'] + $playamount + $courseForwardOne['courseforward_price'] + $forward + $coupon_price + $market_price;

            $student_coursebalance_data = array();
            $student_coursebalance_data['coursebalance_figure'] = $courseBalanceOne['coursebalance_figure'] + $playamount;
            $student_coursebalance_data['coursebalance_time'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;
            $student_coursebalance_data['pricing_id'] = $pricing_id;
            $student_coursebalance_data['companies_id'] = $orderOne['companies_id'];;

            if ($courseBalanceOne['coursebalance_time'] + $coursebalance_time - $num == 0) {
                $student_coursebalance_data['coursebalance_unitexpend'] = 0;
                $student_coursebalance_data['coursebalance_unitearning'] = 0;
            } else {
                $student_coursebalance_data['coursebalance_unitexpend'] = ceil($all_price / ($courseBalanceOne['coursebalance_time'] + $coursebalance_time - $num));//消耗单价
                $student_coursebalance_data['coursebalance_unitearning'] = ceil(($courseBalanceOne['coursebalance_figure'] + $playamount) / ($courseBalanceOne['coursebalance_time'] + $coursebalance_time - $num));
            }

            $student_coursebalance_data['coursebalance_unitrefund'] = $unitrefund;//退费单价

            if ($time < $courseBalanceOne['coursebalance_createtime']) {
                $student_coursebalance_data['coursebalance_createtime'] = $time;
            }

            $student_coursebalance_data['coursebalance_updatatime'] = time();
            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

            $coursebalance_id = $courseBalanceOne['coursebalance_id'];
        } else {
            $courselog_data = array();
            $courselog_data['student_id'] = $student_id;
            $courselog_data['log_class'] = 0;
            $courselog_data['school_id'] = $this->school_id;
            $courselog_data['companies_id'] = $orderOne['companies_id'];
            $courselog_data['class_id'] = $class_id;
            $courselog_data['course_id'] = $course_id;
            $courselog_data['trading_pid'] = $trading_pid;
            $courselog_data['log_random'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('购买课程增加课程余额');
            $courselog_data['log_playclass'] = '+';
            $courselog_data['log_fromamount'] = 0;
            $courselog_data['log_playamount'] = $playamount;
            $courselog_data['log_finalamount'] = $playamount;

            $courselog_data['log_fromtimes'] = 0;
            $courselog_data['log_playtimes'] = $coursebalance_time;
            $courselog_data['log_finaltimes'] = $coursebalance_time;

            $courselog_data['log_reason'] = $this->LgStringSwitch('购买课程增加课程余额');
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

            if (($forward + $coupon_price + $market_price) > 0) {
                $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$course_id}'");
                if ($courseOne['course_inclasstype'] != '1') {
                    $coursebalancelog_data = array();
                    $coursebalancelog_data['student_id'] = $student_id;
                    $coursebalancelog_data['school_id'] = $this->school_id;
                    $coursebalancelog_data['companies_id'] = $orderOne['companies_id'];
                    $coursebalancelog_data['class_id'] = $class_id;
                    $coursebalancelog_data['log_class'] = 1;
                    $coursebalancelog_data['course_id'] = $course_id;
                    $coursebalancelog_data['trading_pid'] = $trading_pid;
                    $coursebalancelog_data['log_random'] = $random;
                    $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('购买课程增加课程结转金额');
                    $coursebalancelog_data['log_playclass'] = '+';
                    $coursebalancelog_data['log_fromamount'] = 0;
                    $coursebalancelog_data['log_playamount'] = $forward + $coupon_price + $market_price;
                    $coursebalancelog_data['log_finalamount'] = $forward + $coupon_price + $market_price;

                    $coursebalancelog_data['log_fromtimes'] = 0;
                    $coursebalancelog_data['log_playtimes'] = $coursebalance_time;
                    $coursebalancelog_data['log_finaltimes'] = $coursebalance_time;

                    $coursebalancelog_data['log_reason'] = $this->LgStringSwitch('购买课程增加课程结转金额');
                    $coursebalancelog_data['log_time'] = $time;
                    $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

                    if ($courseForwardOne) {
                        $data = array();
                        $data['courseforward_price'] = $courseForwardOne['courseforward_price'] + $forward + $coupon_price + $market_price;
                        $data['courseforward_updatatime'] = $time;
                        $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $data);
                    } else {
                        $student_courseforward_data = array();
                        $student_courseforward_data['student_id'] = $student_id;
                        $student_courseforward_data['course_id'] = $course_id;
                        $student_courseforward_data['courseforward_price'] = $forward + $coupon_price + $market_price;
                        $student_courseforward_data['courseforward_deductionmethod'] = $deductionmethod;
                        $student_courseforward_data['courseforward_createtime'] = $time;
                        $this->DataControl->insertData("smc_student_courseforward", $student_courseforward_data);
                    }
                }
            }

            $student_coursebalance_data = array();
            $student_coursebalance_data['student_id'] = $student_id;
            $student_coursebalance_data['course_id'] = $course_id;
            $student_coursebalance_data['school_id'] = $this->school_id;
            $student_coursebalance_data['companies_id'] = $orderOne['companies_id'];
            $student_coursebalance_data['company_id'] = $this->company_id;
            $student_coursebalance_data['coursebalance_figure'] = $playamount;
            $student_coursebalance_data['pricing_id'] = $pricing_id;
            $student_coursebalance_data['coursebalance_unitexpend'] = $unitexpend;//消耗单价
            $student_coursebalance_data['coursebalance_unitrefund'] = $unitrefund;//退费单价
            $student_coursebalance_data['coursebalance_time'] = $coursebalance_time;
            $student_coursebalance_data['coursebalance_unitearning'] = $unitearning;
            $student_coursebalance_data['coursebalance_createtime'] = $time;
            $student_coursebalance_data['coursebalance_updatatime'] = time();
            $coursebalance_id = $this->DataControl->insertData("smc_student_coursebalance", $student_coursebalance_data);

            $time_data = array();
            $time_data['student_id'] = $student_id;
            $time_data['school_id'] = $this->school_id;
            $time_data['companies_id'] = $orderOne['companies_id'];
            $time_data['course_id'] = $course_id;
            $time_data['class_id'] = $class_id;
            $time_data['trading_pid'] = $trading_pid;
            $time_data['log_random'] = $random;
            $time_data['timelog_playname'] = $this->LgStringSwitch('购买课程增加课次');
            $time_data['timelog_playclass'] = '+';
            $time_data['timelog_fromtimes'] = 0;
            $time_data['timelog_playtimes'] = $coursebalance_time;
            $time_data['timelog_finaltimes'] = $coursebalance_time;
            $time_data['timelog_reason'] = $this->LgStringSwitch('购买课程增加课次');
            $time_data['timelog_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);


        }

        $newCourseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype", "course_id='{$course_id}'");
        $pricinglog = array();
        $pricinglog['student_id'] = $student_id;
        $pricinglog['school_id'] = $this->school_id;
        $pricinglog['course_id'] = $course_id;
        $pricinglog['pricinglog_starttime'] = $time;
        $pricinglog['pricing_id'] = $pricing_id;
        $pricinglog['order_pid'] = $order_pid;
        $pricinglog['pricinglog_buytimes'] = $coursebalance_time;
        $pricinglog['pricinglog_buyprice'] = $playamount;
        $pricinglog['pricinglog_unitexpend'] = $unitexpend;
        $pricinglog['pricinglog_unitrefund'] = $unitrefund;

        if ($newCourseOne['course_inclasstype'] == 2) {
            $pricinglog['pricinglog_refunddeadline'] = date("Y-m-d", ($time + 180 * 24 * 3600));
        }

        $pricinglog['pricinglog_createtime'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_pricinglog", $pricinglog);

        $data = array();
        $data['student_id'] = $student_id;
        $data['school_id'] = $this->school_id;
        $data['course_id'] = $course_id;
        $data['order_pid'] = $order_pid;
        $data['consumelog_nums'] = $coursebalance_time;
        $data['consumelog_unitexpend'] = $unitexpend;
        $data['consumelog_unitrefund'] = $unitrefund;
        $data['consumelog_createtime'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_consumelog", $data);

        if ($class_id > 0) {
            if ($monthArray) {
                foreach ($monthArray as $monthOne) {

                    $data = array();
                    $data['order_pid'] = $order_pid;
                    $data['coursebalance_id'] = $coursebalance_id;
                    $data['class_id'] = $class_id;
                    $data['courseshare_month'] = $monthOne['month'];
                    $data['courseshare_price'] = $monthOne['price'];
                    $data['courseshare_sellingprice'] = $monthOne['sellingprice'];
                    $data['courseshare_status'] = '0';
                    $data['courseshare_createtime'] = $time;
                    $this->DataControl->insertData("smc_student_courseshare", $data);
                }
            }

            if ($isIn == 0 && $from != 3) {
                $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
                $TransactionModel->entryClass($student_id, $course_id, $class_id, $enterclassdate, $from, $time);
            }
        }

        return true;
    }

    function transferCourse($student_id, $course_id, $to_course_id, $pricing_id, $class_id = 0, $to_class_id = 0, $change_reason = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        $DealModel = new \Model\Smc\DealModel($this->publicarray);
        $dealorder_pid = $DealModel->dealtimeorder($student_id, $course_id, $to_course_id, $class_id, $to_class_id, 0, 0, $change_reason, $this->LgStringSwitch('创建课次结转订单'), $this->LgStringSwitch('课次结转订单提交成功，等待审核'), $time);

        $dealOne = $this->DataControl->getFieldOne("smc_forward_dealorder", "trading_pid", "dealorder_pid='{$dealorder_pid}'");
        $trading_pid = $dealOne['trading_pid'];

        $sql = "select scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price,scf.courseforward_deductionmethod
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.student_id=scb.student_id and scf.course_id=scb.course_id
              where scb.student_id='{$student_id}' and scb.course_id='{$course_id}' and scb.school_id='{$this->school_id}'";
        $coursebalance = $this->DataControl->selectOne($sql);
        $coursebalance_time = $coursebalance['coursebalance_time'];


        if ($class_id > 0) {
            $sql = "select coursetimes_id from smc_student_free_coursetimes where student_id='{$student_id}' and school_id='{$this->school_id}' and class_id='{$class_id}' and course_id='{$course_id}' and is_use=0";
            $freeList = $this->DataControl->selectClear($sql);
            if ($freeList) {
                $coursebalance_time -= count($freeList);
            }
        }

        $random = $this->create_guid();

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0, $course_id);

        $courselog_data = array();
        $courselog_data['student_id'] = $student_id;
        $courselog_data['log_class'] = 0;
        $courselog_data['school_id'] = $this->school_id;
        $courselog_data['companies_id'] = $companiesOne['companies_id'];
        $courselog_data['course_id'] = $course_id;
        $courselog_data['trading_pid'] = $trading_pid;
        $courselog_data['log_random'] = $random;
        $courselog_data['log_playname'] = $this->LgStringSwitch('课次结转');
        $courselog_data['log_playclass'] = '-';
        $courselog_data['log_fromamount'] = $coursebalance['coursebalance_figure'];
        $courselog_data['log_playamount'] = $coursebalance['coursebalance_figure'];
        $courselog_data['log_finalamount'] = 0;
        $courselog_data['log_fromtimes'] = $coursebalance['coursebalance_time'];
        $courselog_data['log_playtimes'] = $coursebalance_time;
        $courselog_data['log_finaltimes'] = $coursebalance['coursebalance_time'] - $coursebalance_time;
        $courselog_data['log_reason'] = $this->LgStringSwitch('课次结转');
        $courselog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $companiesOne['companies_id'];
        $time_data['course_id'] = $course_id;
        $time_data['trading_pid'] = $trading_pid;
        $time_data['log_random'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('课次结转');
        $time_data['timelog_playclass'] = '-';
        $time_data['timelog_fromtimes'] = $coursebalance['coursebalance_time'];
        $time_data['timelog_playtimes'] = $coursebalance_time;
        $time_data['timelog_finaltimes'] = $coursebalance['coursebalance_time'] - $coursebalance_time;
        $time_data['timelog_reason'] = $this->LgStringSwitch('课次结转');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_time'] = $coursebalance['coursebalance_time'] - $coursebalance_time;
        $student_coursebalance_data['coursebalance_figure'] = 0;
        $student_coursebalance_data['coursebalance_status'] = 3;
        $student_coursebalance_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

        if ($coursebalance['courseforward_price'] > '0') {
            $data = array();
            $data['student_id'] = $student_id;
            $data['log_class'] = 1;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $companiesOne['companies_id'];
            $data['course_id'] = $course_id;
            $data['trading_pid'] = $trading_pid;
            $data['log_random'] = $random;
            $data['log_playname'] = $this->LgStringSwitch('课次结转');
            $data['log_playclass'] = '-';
            $data['log_fromamount'] = $coursebalance['courseforward_price'];
            $data['log_playamount'] = $coursebalance['courseforward_price'];
            $data['log_finalamount'] = 0;
            $data['log_fromtimes'] = $coursebalance['coursebalance_time'];
            $data['log_playtimes'] = $coursebalance_time;
            $data['log_finaltimes'] = $coursebalance['coursebalance_time'] - $coursebalance_time;
            $data['log_reason'] = $this->LgStringSwitch('课次结转');
            $data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $data);

            $data = array();
            $data['courseforward_price'] = 0;
            $data['courseforward_updatatime'] = $time;
            $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $data);
        }

        if ($dealorder_pid) {
            $course_data = array();
            $course_data['course_id'] = $course_id;
            $course_data['dealorder_pid'] = $dealorder_pid;
            $course_data['dealcourse_figure'] = $coursebalance['coursebalance_figure'];
            $course_data['dealcourse_time'] = $coursebalance_time;
            $course_data['dealcourse_fromforwardprice'] = $coursebalance['courseforward_price'];
            $course_data['dealcourse_tobalanceprice'] = 0;
            $course_data['dealcourse_toforwardprice'] = 0;
            $course_data['dealcourse_topricing_id'] = $pricing_id;
            $course_data['dealcourse_deductionmethod'] = $coursebalance['courseforward_deductionmethod'];
            $this->DataControl->insertData("smc_forward_dealorder_course", $course_data);

        }

//        if($class_id>0){
//            $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
//            $TransactionModel->outClass($student_id, $class_id,0,$time,$trading_pid);
//        }

        $pricingOne = $this->getCoursePricing($course_id, $this->company_id, $this->school_id);
        $topricingOne = $this->getCoursePricing($to_course_id, $this->company_id, $this->school_id);
        if (ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']) == ceil($topricingOne['tuition_sellingprice'] / $topricingOne['tuition_buypiece'])) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $dealorder_pid, 4);
            $bool = $OrderExamineModel->adoptDealOrder($this->LgStringSwitch('定价相同,自动审核'), $time);
        }

        return $dealorder_pid;
    }

    function addCourseTimes($student_id, $course_id, $class_id = 0, $num, $info, $school_id = 0, $trading_pid = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }
        if ($school_id != '') {
            $this->school_id = $school_id;
        }

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0, $course_id);
        $sql = "select scb.coursebalance_time,scb.coursebalance_figure,s.student_branch
              from smc_student_coursebalance as scb
              left join smc_student as s on s.student_id=scb.student_id
              where scb.student_id='{$student_id}' and scb.school_id='{$this->school_id}' and scb.course_id='{$course_id}'";
        $studentOne = $this->DataControl->selectOne($sql);

        $random = $this->create_guid();

        $courselog_data = array();
        $courselog_data['student_id'] = $student_id;
        $courselog_data['log_class'] = 0;
        $courselog_data['school_id'] = $this->school_id;
        $courselog_data['companies_id'] = $companiesOne['companies_id'];
        $courselog_data['course_id'] = $course_id;
        $courselog_data['trading_pid'] = $trading_pid;
        $courselog_data['log_random'] = $random;
        $courselog_data['log_playname'] = $info;
        $courselog_data['log_playclass'] = '+';
        $courselog_data['log_fromamount'] = $studentOne['coursebalance_figure'];
        $courselog_data['log_playamount'] = 0;
        $courselog_data['log_finalamount'] = $studentOne['coursebalance_figure'];
        $courselog_data['log_fromtimes'] = $studentOne['coursebalance_time'];
        $courselog_data['log_playtimes'] = $num;
        $courselog_data['log_finaltimes'] = $studentOne['coursebalance_time'] + $num;
        $courselog_data['log_reason'] = $info;
        $courselog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['course_id'] = $course_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $companiesOne['companies_id'];
        $time_data['class_id'] = $class_id;
        $time_data['log_random'] = $random;
        $time_data['trading_pid'] = $trading_pid;
        $time_data['timelog_playname'] = $info;
        $time_data['timelog_playclass'] = '+';
        $time_data['timelog_fromtimes'] = $studentOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = $num;
        $time_data['timelog_finaltimes'] = $studentOne['coursebalance_time'] + $num;
        $time_data['timelog_reason'] = $info;
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        if ($this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'")) {
            $student_coursebalance_data = array();
            $student_coursebalance_data['coursebalance_time'] = $studentOne['coursebalance_time'] + $num;
            $student_coursebalance_data['coursebalance_updatatime'] = time();
            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);
        } else {
            $data = array();
            $data['company_id'] = $this->company_id;
            $data['student_id'] = $student_id;
            $data['school_id'] = $this->school_id;
            $data['companies_id'] = $companiesOne['companies_id'];
            $data['course_id'] = $course_id;
            $data['coursebalance_time'] = $num;
            $data['coursebalance_status'] = '1';
            $data['coursebalance_createtime'] = $time;
            $data['coursebalance_updatatime'] = time();
            $this->DataControl->insertData("smc_student_coursebalance", $data);
        }

        return true;
    }

    function addStuCourseTimes($student_id, $course_id, $coursebalance_time, $school_id = 0, $trading_pid = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        if ($school_id != '') {
            $this->school_id = $school_id;
        }

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0, $course_id);
        $pricingOne = $this->getCoursePricing($course_id, $this->company_id, $this->school_id);
        $random = $this->create_guid();

        $courselog_data = array();
        $courselog_data['student_id'] = $student_id;
        $courselog_data['log_class'] = 0;
        $courselog_data['school_id'] = $this->school_id;
        $courselog_data['companies_id'] = $companiesOne['companies_id'];
        $courselog_data['course_id'] = $course_id;
        $courselog_data['log_random'] = $random;
        $courselog_data['trading_pid'] = $trading_pid;
        $courselog_data['log_playname'] = $this->LgStringSwitch('免费课次新增余额');
        $courselog_data['log_playclass'] = '+';
        $courselog_data['log_fromamount'] = 0;
        $courselog_data['log_playamount'] = 0;
        $courselog_data['log_finalamount'] = 0;
        $courselog_data['log_fromtimes'] = 0;
        $courselog_data['log_playtimes'] = $coursebalance_time;
        $courselog_data['log_finaltimes'] = $coursebalance_time;
        $courselog_data['log_reason'] = $this->LgStringSwitch('免费课次新增余额');
        $courselog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $companiesOne['companies_id'];
        $time_data['course_id'] = $course_id;
        $time_data['log_random'] = $random;
        $time_data['trading_pid'] = $trading_pid;
        $time_data['timelog_playname'] = $this->LgStringSwitch('免费课次新增课次');
        $time_data['timelog_playclass'] = '+';
        $time_data['timelog_fromtimes'] = 0;
        $time_data['timelog_playtimes'] = $coursebalance_time;
        $time_data['timelog_finaltimes'] = $coursebalance_time;
        $time_data['timelog_reason'] = $this->LgStringSwitch('免费课次新增课次');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        if ($this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'")) {
            $student_coursebalance_data['coursebalance_figure'] = 0;
            $student_coursebalance_data['pricing_id'] = $pricingOne['pricing_id'];
            $student_coursebalance_data['coursebalance_unitexpend'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);//消耗单价
            $student_coursebalance_data['coursebalance_unitrefund'] = $pricingOne['tuition_unitprice'];//退费单价
            $student_coursebalance_data['coursebalance_time'] = $coursebalance_time;
            $student_coursebalance_data['coursebalance_updatatime'] = time();
            $student_coursebalance_data['coursebalance_unitearning'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);
            $student_coursebalance_data['coursebalance_status'] = 0;

            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);
            $student_courseforward_data = array();
            $student_courseforward_data['courseforward_price'] = 0;
            $student_courseforward_data['courseforward_deductionmethod'] = 0;
            $student_courseforward_data['courseforward_updatatime'] = $time;
            $this->DataControl->updateData("smc_student_courseforward", "student_id='{$student_id}' and course_id='{$course_id}'", $student_courseforward_data);
        } else {
            $student_coursebalance_data['student_id'] = $student_id;
            $student_coursebalance_data['course_id'] = $course_id;
            $student_coursebalance_data['school_id'] = $this->school_id;
            $student_coursebalance_data['companies_id'] = $companiesOne['companies_id'];
            $student_coursebalance_data['company_id'] = $this->company_id;
            $student_coursebalance_data['coursebalance_figure'] = 0;
            $student_coursebalance_data['pricing_id'] = $pricingOne['pricing_id'];
            $student_coursebalance_data['coursebalance_unitexpend'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);//消耗单价
            $student_coursebalance_data['coursebalance_unitrefund'] = $pricingOne['tuition_unitprice'];//退费单价
            $student_coursebalance_data['coursebalance_time'] = $coursebalance_time;
            $student_coursebalance_data['coursebalance_status'] = 0;
            $student_coursebalance_data['coursebalance_unitearning'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);
            $student_coursebalance_data['coursebalance_createtime'] = $time;
            $student_coursebalance_data['coursebalance_updatatime'] = time();
            $this->DataControl->insertData("smc_student_coursebalance", $student_coursebalance_data);

            $student_courseforward_data = array();
            $student_courseforward_data['student_id'] = $student_id;
            $student_courseforward_data['course_id'] = $course_id;
            $student_courseforward_data['courseforward_price'] = 0;
            $student_courseforward_data['courseforward_deductionmethod'] = 0;
            $student_courseforward_data['courseforward_createtime'] = $time;
            $this->DataControl->insertData("smc_student_courseforward", $student_courseforward_data);
        }

        return true;

    }

    function makeUpCourse($student_id, $class_id = 0, $course_id, $coursebalance_time, $unitexpend, $trading_pid = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }
        $playamount = $coursebalance_time * $unitexpend;

        $sql = "select scb.coursebalance_figure,scb.coursebalance_time from smc_student_coursebalance as scb where scb.student_id='{$student_id}' and scb.school_id='{$this->school_id}' and scb.course_id='{$course_id}'";
        $courseBalanceOne = $this->DataControl->selectOne($sql);

        if (!$courseBalanceOne) {
            $this->error = true;
            $this->errortip = "学员不存在该课程";
            return false;
        }

        $freeList = $this->DataControl->selectClear("select coursetimes_id from smc_student_free_coursetimes where student_id='{$student_id}' and class_id='{$class_id}' and is_use='0'");

        if ($freeList) {
            $num = count($freeList);
        } else {
            $num = 0;
        }

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0, $course_id);
        $random = $this->create_guid();

        $courselog_data = array();
        $courselog_data['student_id'] = $student_id;
        $courselog_data['log_class'] = 0;
        $courselog_data['school_id'] = $this->school_id;
        $courselog_data['companies_id'] = $companiesOne['companies_id'];
        $courselog_data['course_id'] = $course_id;
        $courselog_data['class_id'] = $class_id;
        $courselog_data['trading_pid'] = $trading_pid;
        $courselog_data['log_random'] = $random;
        $courselog_data['log_playname'] = $this->LgStringSwitch('补齐学费新增余额');
        $courselog_data['log_playclass'] = '+';
        $courselog_data['log_fromamount'] = $courseBalanceOne['coursebalance_figure'];
        $courselog_data['log_playamount'] = $playamount;
        $courselog_data['log_finalamount'] = $courseBalanceOne['coursebalance_figure'] + $playamount;
        $courselog_data['log_fromtimes'] = $courseBalanceOne['coursebalance_time'];
        $courselog_data['log_playtimes'] = $coursebalance_time;
        $courselog_data['log_finaltimes'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;
        $courselog_data['log_reason'] = $this->LgStringSwitch('补齐学费新增余额');
        $courselog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        $time_data = array();
        $time_data['student_id'] = $student_id;
        $time_data['school_id'] = $this->school_id;
        $time_data['companies_id'] = $companiesOne['companies_id'];
        $time_data['course_id'] = $course_id;
        $time_data['class_id'] = $class_id;
        $time_data['trading_pid'] = $trading_pid;
        $time_data['log_random'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('补齐学费新增课次');
        $time_data['timelog_playclass'] = '+';
        $time_data['timelog_fromtimes'] = $courseBalanceOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = $coursebalance_time;
        $time_data['timelog_finaltimes'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;
        $time_data['timelog_reason'] = $this->LgStringSwitch('补齐学费新增课次');
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_figure'] = $courseBalanceOne['coursebalance_figure'] + $playamount;
        $student_coursebalance_data['coursebalance_time'] = $courseBalanceOne['coursebalance_time'] + $coursebalance_time;
        if ($student_coursebalance_data['coursebalance_time'] > '0' && $student_coursebalance_data['coursebalance_figure'] > '0') {
            $student_coursebalance_data['coursebalance_unitexpend'] = ceil($student_coursebalance_data['coursebalance_figure'] / ($student_coursebalance_data['coursebalance_time'] - $num));
        }

        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$course_id}' and school_id='{$this->school_id}'", $student_coursebalance_data);

        return true;
    }

    function reduceStuWithholdbalance($student_id, $trading_pid, $withholdbalance, $type = 0, $companies_id, $time = '')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }

        $stublcOne = $this->getStuBalance($student_id, $this->company_id, $this->school_id, $companies_id);
        if ($stublcOne['student_withholdbalance'] < $withholdbalance) {
            $this->error = true;
            $this->errortip = "结算金额不正确";
            return false;
        }
        $balancelog_data = array();
        $balancelog_data['company_id'] = $this->company_id;
        $balancelog_data['school_id'] = $this->school_id;
        $balancelog_data['companies_id'] = $companies_id;
        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $balancelog_data['student_id'] = $student_id;
        $balancelog_data['trading_pid'] = $trading_pid;
        $balancelog_data['balancelog_class'] = 2;

        $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('不可退账户余额抵扣金额');
        $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('不可退账户余额抵扣金额');

        $balancelog_data['balancelog_playclass'] = '-';
        $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
        $balancelog_data['balancelog_playamount'] = $withholdbalance;
        $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] - $withholdbalance;

        $balancelog_data['balancelog_time'] = $time;
        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

        //结算账户余额
        $data = array();
        $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'] - $withholdbalance;
        $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companies_id}'", $data);
    }

    function reduceStuTimes($student_id, $class_id, $num = 0, $info = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$class_id}'");

        $sql = "select scb.coursebalance_time,scb.companies_id
              from smc_student_coursebalance as scb
              where scb.student_id='{$student_id}' and scb.company_id='{$this->company_id}' and scb.course_id='{$classOne['course_id']}' and scb.school_id='{$this->school_id}'";
        $stuCourseOne = $this->DataControl->selectOne($sql);

        if ($stuCourseOne['coursebalance_time'] < $num) {
            $num = $stuCourseOne['coursebalance_time'];
        }

        if ($stuCourseOne['coursebalance_time'] > 0) {
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_branch", "student_id='{$student_id}'");
            do {
                $random = $this->createStuRandom($studentOne['student_branch']);
            } while ($this->DataControl->selectOne("select timelog_id from smc_student_coursebalance_timelog where log_random='{$random}' limit 0,1"));

            $lastNum = $stuCourseOne['coursebalance_time'] - $num;

            $time_data = array();
            $time_data['student_id'] = $student_id;
            $time_data['course_id'] = $classOne['course_id'];
            $time_data['school_id'] = $this->school_id;
            $time_data['companies_id'] = $stuCourseOne['companies_id'];
            $time_data['class_id'] = $class_id;
            $time_data['log_random'] = $random;
            $time_data['timelog_playname'] = $info;
            $time_data['timelog_playclass'] = '-';
            $time_data['timelog_fromtimes'] = $stuCourseOne['coursebalance_time'];
            $time_data['timelog_playtimes'] = $num;
            $time_data['timelog_finaltimes'] = $lastNum;
            $time_data['timelog_reason'] = $info;
            $time_data['timelog_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

            $data = array();
            $data['coursebalance_time'] = $stuCourseOne['coursebalance_time'] - $num;
            $data['coursebalance_updatatime'] = time();
            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$classOne['course_id']}' and school_id='{$this->school_id}' and company_id='{$this->company_id}'", $data);
        }
    }

    function reduceStuBalance($student_id, $trading_pid, $balance, $type = 0, $companies_id=0, $time = '')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }

        $stublcOne = $this->getStuBalance($student_id, $this->company_id, $this->school_id, $companies_id);

        if ($stublcOne['student_balance'] < $balance) {
            $this->error = true;
            $this->errortip = "结算金额不正确";
            return false;
        }
        $balancelog_data = array();
        $balancelog_data['company_id'] = $this->company_id;
        $balancelog_data['school_id'] = $this->school_id;
        $balancelog_data['companies_id'] = $companies_id;
        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $balancelog_data['student_id'] = $student_id;
        $balancelog_data['trading_pid'] = $trading_pid;
        $balancelog_data['balancelog_class'] = 0;
        if ($type == 1) {
            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('退费');
            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('退费');
        } else {
            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('账户余额抵扣金额');
            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('账户余额抵扣金额');
        }
        $balancelog_data['balancelog_playclass'] = '-';
        $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
        $balancelog_data['balancelog_playamount'] = $balance;
        $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] - $balance;

        $balancelog_data['balancelog_time'] = $time;
        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

        //结算账户余额
        $data = array();
        $data['student_balance'] = $stublcOne['student_balance'] - $balance;
        $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}' and companies_id='{$companies_id}'", $data);
    }


    function consumeStuBalance($student_id, $school_id, $trading_pid, $balance = 0, $withholdbalance = 0, $companies_id=0, $playname = '', $note = '', $time = '', $playclass = '-', $oldtrading_pid='')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        $stublcOne = $this->getStuBalance($student_id, $this->company_id, $school_id, $companies_id);

        if ($playclass == '+') {//针对转校拒绝的操作
            if ($balance > 0) {
                $balancelogone = $this->DataControl->selectOne(" select balancelog_playamount from smc_student_balancelog where trading_pid = '{$oldtrading_pid}' and balancelog_class= '0'  ");
                if ($balance != $balancelogone['balancelog_playamount']) {
                    $this->error = true;
                    $this->errortip = "结算金额不正确";
                    return false;
                }
            } elseif ($withholdbalance > 0) {
                $balancelogone = $this->DataControl->selectOne(" select balancelog_playamount from smc_student_balancelog where trading_pid = '{$oldtrading_pid}' and balancelog_class = '2' ");
                if ($withholdbalance != $balancelogone['balancelog_playamount']) {
                    $this->error = true;
                    $this->errortip = "结算金额不正确";
                    return false;
                }
            } else {
                return false;
            }
        } else {
            if ($stublcOne['student_balance'] < $balance || $stublcOne['student_withholdbalance'] < $withholdbalance) {
                $this->error = true;
                $this->errortip = "结算金额不正确";
                return false;
            }
        }

        if ($balance > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['school_id'] = $school_id;
            $balancelog_data['companies_id'] = $companies_id;
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['trading_pid'] = $trading_pid;
            $balancelog_data['balancelog_class'] = 0;
            $balancelog_data['balancelog_playname'] = $playname;
            $balancelog_data['balancelog_reason'] = $note;
            $balancelog_data['balancelog_playclass'] = $playclass;
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
            $balancelog_data['balancelog_playamount'] = $balance;
            if ($playclass == '+') {
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $balance;
            } else {
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] - $balance;
            }
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
        }

        if ($withholdbalance > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['school_id'] = $school_id;
            $balancelog_data['companies_id'] = $companies_id;
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['trading_pid'] = $trading_pid;
            $balancelog_data['balancelog_class'] = 2;
            $balancelog_data['balancelog_playname'] = $playname;
            $balancelog_data['balancelog_reason'] = $note;
            $balancelog_data['balancelog_playclass'] = $playclass;
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
            $balancelog_data['balancelog_playamount'] = $withholdbalance;
            if ($playclass == '+') {
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $withholdbalance;
            } else {
                $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] - $withholdbalance;
            }
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
        }

        if ($playclass == '+') {
            //结算账户余额
            $data = array();
            $data['student_balance'] = $stublcOne['student_balance'] + $balance;
            $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'] + $withholdbalance;
            $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$school_id}' and company_id='{$this->company_id}' and companies_id='{$companies_id}'", $data);
        } else {
            //结算账户余额
            $data = array();
            $data['student_balance'] = $stublcOne['student_balance'] - $balance;
            $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'] - $withholdbalance;
            $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$school_id}' and company_id='{$this->company_id}' and companies_id='{$companies_id}'", $data);
        }
    }

    function reduceStuCourseBalance($student_id, $course_id, $class_id, $figure, $trading_pid, $type = 0, $log_reason = '', $monthArray = array())
    {
        $time = time();


        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_unitexpend,scb.coursebalance_unitearning,co.coursecat_id,co.coursetype_id,co.course_branch,ct.coursecat_branch,scb.companies_id
                from smc_student_coursebalance as scb
                left join smc_course as co on co.course_id=scb.course_id
                left join smc_code_coursecat as ct on ct.coursecat_id=co.coursecat_id
                where scb.student_id='{$student_id}' and scb.course_id='{$course_id}' and scb.school_id='{$this->school_id}'";
        $stuCourseOne = $this->DataControl->selectOne($sql);

        $num = 0;//免费赠送的课次

        if ($type == 1) {

            $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursetype_id,coursecatbalance_figure,coursecatbalance_time,companies_id", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$stuCourseOne['coursecat_id']}' and coursetype_id='{$stuCourseOne['coursetype_id']}' and feetype_code='Forward'");
            if ($stuCatOne) {
                $log = array();
                $log['student_id'] = $student_id;
                $log['trading_pid'] = $trading_pid;
                $log['companies_id'] = $stuCourseOne['companies_id'];
                $log['coursetype_id'] = $stuCatOne['coursetype_id'];
                $log['coursecat_id'] = $stuCourseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['staffer_id'] = $this->stafferOne['staffer_id'];
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
                $log['log_playamount'] = $figure;
                $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] + $figure;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = '扣除课程余额';
                $log['log_reason'] = $log_reason;
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] + $figure;
                $data['coursecatbalance_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_coursecatbalance", "student_id='{$student_id}' and school_id='{$this->school_id}' and coursecat_id='{$stuCourseOne['coursecat_id']}' and coursetype_id='{$stuCourseOne['coursetype_id']}' and feetype_code='Forward'", $data);
            } else {
                $log = array();
                $log['student_id'] = $student_id;
                $log['trading_pid'] = $trading_pid;
                $log['coursetype_id'] = $stuCourseOne['coursetype_id'];
                $log['coursecat_id'] = $stuCourseOne['coursecat_id'];
                $log['feetype_code'] = 'Forward';
                $log['school_id'] = $this->school_id;
                $log['companies_id'] = $stuCourseOne['companies_id'];
                $log['staffer_id'] = $this->stafferOne['staffer_id'];
                $log['log_playclass'] = '+';
                $log['log_fromamount'] = 0;
                $log['log_playamount'] = $figure;
                $log['log_finalamount'] = $figure;
                $log['log_fromme'] = 0;
                $log['log_playme'] = 0;
                $log['log_finaltime'] = 0;
                $log['log_time'] = $time;
                $log['log_playname'] = '扣除课程余额';
                $log['log_reason'] = $log_reason;
                $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

                $data = array();
                $data['company_id'] = $this->company_id;
                $data['school_id'] = $this->school_id;
                $data['companies_id'] = $stuCourseOne['companies_id'];
                $data['student_id'] = $student_id;
                $data['coursetype_id'] = $stuCourseOne['coursetype_id'];
                $data['coursecat_id'] = $stuCourseOne['coursecat_id'];
                $data['feetype_code'] = 'Forward';
                $data['coursecat_branch'] = $stuCourseOne['coursecat_branch'];
                $data['course_branch'] = $stuCourseOne['course_branch'];
                $data['coursecatbalance_figure'] = $figure;
                $data['coursecatbalance_createtime'] = $time;
                $this->DataControl->insertData("smc_student_coursecatbalance", $data);

            }

            if ($monthArray) {
                $sql = "select * from smc_student_courseshare where coursebalance_id='{$stuCourseOne['coursebalance_id']}' and courseshare_status=0";
                $shareList = $this->DataControl->selectClear($sql);

                $shareArray = array_column($shareList, null, 'courseshare_month');

                foreach ($monthArray as $monthOne) {
                    if ($shareArray[$monthOne['share_month']]) {
                        if ($monthOne['share_price'] > 0) {
                            $data = array();
                            $data['courseshare_price'] = $shareArray[$monthOne['share_month']]['courseshare_price'] - $monthOne['share_price'];
                            $data['courseshare_updatatime'] = $time;
                            $this->DataControl->updateData("smc_student_courseshare", "courseshare_id='{$shareArray[$monthOne['share_month']]['courseshare_id']}'", $data);
                        }
                    }
                }
            }


        } else {

            $sql = "select sf.coursetimes_id 
                    from smc_student_free_coursetimes as sf
                    where sf.course_id='{$course_id}' and sf.student_id='{$student_id}' and sf.school_id='{$this->school_id}' and sf.is_use='0'";
            $freeTimes = $this->DataControl->selectClear($sql);
            $num = $freeTimes ? count($freeTimes) : 0;

            $stublcOne = $this->getStuBalance($student_id, $this->company_id, $this->school_id, $stuCourseOne['companies_id']);


            $data = array();
            $data['company_id'] = $this->company_id;
            $data['companies_id'] = $stuCourseOne['companies_id'];
            $data['school_id'] = $this->school_id;
            $data['staffer_id'] = $this->stafferOne['staffer_id'];
            $data['student_id'] = $student_id;
            $data['trading_pid'] = $trading_pid;
            $data['balancelog_playname'] = '课程余额转出';
            $data['balancelog_playclass'] = '+';
            $data['balancelog_fromamount'] = $stublcOne['student_balance'];
            $data['balancelog_playamount'] = $figure;
            $data['balancelog_finalamount'] = $stublcOne['student_balance'] + $figure;
            $data['balancelog_reason'] = $log_reason;
            $data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $data);

            $data = array();
            $data['student_balance'] = $stublcOne['student_balance'] + $figure;
            $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id='{$this->school_id}' and companies_id='{$stuCourseOne['companies_id']}'", $data);

        }

        $data = array();
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $stuCourseOne['companies_id'];
        $data['course_id'] = $course_id;
        $data['student_id'] = $student_id;
        $data['trading_pid'] = $trading_pid;
        $data['log_class'] = 0;
        $data['log_playname'] = '扣除课程余额';
        $data['log_playclass'] = '-';
        $data['log_fromamount'] = $stuCourseOne['coursebalance_figure'];
        $data['log_playamount'] = $figure;
        $data['log_finalamount'] = $stuCourseOne['coursebalance_figure'] - $figure;
        $data['log_fromtimes'] = $stuCourseOne['coursebalance_time'];
        $data['log_playtimes'] = 0;
        $data['log_finaltimes'] = $stuCourseOne['coursebalance_time'];
        $data['log_reason'] = $log_reason;
        $data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $data);

        $data = array();
        if ($stuCourseOne['coursebalance_time'] <= $num) {
            $data['coursebalance_figure'] = $stuCourseOne['coursebalance_figure'] - $figure;
            $data['coursebalance_unitexpend'] = ($stuCourseOne['coursebalance_unitexpend'] - $figure) > 0 ? $stuCourseOne['coursebalance_unitexpend'] - $figure : 0;
            $data['coursebalance_unitearning'] = ($stuCourseOne['coursebalance_unitearning'] - $figure) > 0 ? $stuCourseOne['coursebalance_unitearning'] - $figure : 0;

        } else {

            $data['coursebalance_figure'] = $stuCourseOne['coursebalance_figure'] - $figure;
            if ($num <= 0) {
                if ($stuCourseOne['coursebalance_time'] > 0) {
                    $data['coursebalance_unitexpend'] = ceil(($stuCourseOne['coursebalance_figure'] - $figure) / $stuCourseOne['coursebalance_time']);
                    $data['coursebalance_unitearning'] = ceil(($stuCourseOne['coursebalance_figure'] - $figure) / $stuCourseOne['coursebalance_time']);
                }

            } else {
                if (($stuCourseOne['coursebalance_time'] - $num) > 0) {
                    $data['coursebalance_unitexpend'] = ceil(($stuCourseOne['coursebalance_figure'] - $figure) / ($stuCourseOne['coursebalance_time'] - $num));
                    $data['coursebalance_unitearning'] = ceil(($stuCourseOne['coursebalance_figure'] - $figure) / ($stuCourseOne['coursebalance_time'] - $num));
                }
            }


        }

        $data['coursebalance_updatatime'] = time();

        $this->DataControl->updateData("smc_student_coursebalance", "coursebalance_id='{$stuCourseOne['coursebalance_id']}'", $data);

        return true;


    }

    function addStuAllBalance($student_id, $school_id, $trading_pid, $balance = 0, $withholdbalance = 0, $companies_id, $title = '', $note = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        $stublcOne = $this->getStuBalance($student_id, $this->company_id, $school_id, $companies_id);


        if ($balance > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['school_id'] = $school_id;
            $balancelog_data['companies_id'] = $companies_id;
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['trading_pid'] = $trading_pid;
            $balancelog_data['balancelog_class'] = 0;
            $balancelog_data['balancelog_playname'] = $title;
            $balancelog_data['balancelog_reason'] = $note;
            $balancelog_data['balancelog_playclass'] = '+';
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
            $balancelog_data['balancelog_playamount'] = $balance;
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $balance;
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
        }
        if ($withholdbalance > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['school_id'] = $school_id;
            $balancelog_data['companies_id'] = $companies_id;
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['trading_pid'] = $trading_pid;
            $balancelog_data['balancelog_class'] = 2;
            $balancelog_data['balancelog_playname'] = $title;
            $balancelog_data['balancelog_reason'] = $note;
            $balancelog_data['balancelog_playclass'] = '+';
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_withholdbalance'];
            $balancelog_data['balancelog_playamount'] = $withholdbalance;
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_withholdbalance'] + $withholdbalance;
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
        }


        //结算账户余额
        $data = array();
        $data['student_balance'] = $stublcOne['student_balance'] + $balance;
        $data['student_withholdbalance'] = $stublcOne['student_withholdbalance'] + $withholdbalance;
        $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$school_id}' and company_id='{$this->company_id}' and companies_id='{$companies_id}'", $data);
    }

//    function addStuBalance($student_id, $school_id, $trading_pid, $balance, $note = '', $title, $time)
//    {
//        if (!$time) {
//            $time = time();
//        } elseif ($time == strtotime(date("Y-m-d"))) {
//            $time = time();
//        }
//        $schoolOne=$this->DataControl->getFieldOne("smc_school","companies_id","school_id='{$school_id}'");
//
//        $stublcOne = $this->DataControl->getFieldOne("smc_student_balance", "student_balance", "student_id='{$student_id}' and school_id = '{$school_id}' and company_id='{$this->company_id}'");
//        $balancelog_data = array();
//        $balancelog_data['company_id'] = $this->company_id;
//        $balancelog_data['school_id'] = $school_id;
//        $balancelog_data['companies_id'] = $schoolOne['companies_id'];
//        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
//        $balancelog_data['student_id'] = $student_id;
//        $balancelog_data['trading_pid'] = $trading_pid;
//        $balancelog_data['balancelog_class'] = 0;
//        $balancelog_data['balancelog_playname'] = $note;
//        $balancelog_data['balancelog_reason'] = $note;
//        $balancelog_data['balancelog_playclass'] = '+';
//        $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
//        $balancelog_data['balancelog_playamount'] = $balance;
//        $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $balance;
//        $balancelog_data['balancelog_time'] = $time;
//        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
//
//        //结算账户余额
//        $data = array();
//        $data['student_balance'] = $stublcOne['student_balance'] + $balance;
//        $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$school_id}' and company_id='{$this->company_id}'", $data);
//    }

    function addStuIntegral($student_id, $balance = 0, $integralgoods_id = 0, $integrallog_rule = 0, $staffer_id = 0, $parenter_id = 0, $playname = '', $note = '', $time = '', $class_id, $course_id)
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }
        if ($course_id > 0) {
            $maxIntegral = $this->DataControl->getFieldOne("smc_course", "course_maxintegral,course_isintegral", "course_id = '{$course_id}'");
            if ($maxIntegral['course_isintegral'] == '1') {
                $oldIntrgral = $this->DataControl->selectOne("select sum(x.integrallog_playamount) as num 
                    from smc_student_integrallog x 
                    left join smc_code_integralrule y on x.integrallog_rule=y.integralrule_name and x.company_id=y.company_id
                    where x.student_id = '{$student_id}' and x.course_id = '{$course_id}' and x.integrallog_playclass = '+' and y.integralrule_class<>1 ");
                $newIntegral = $oldIntrgral['num'] + $balance;
                if ($newIntegral > $maxIntegral['course_maxintegral']) {
                    ajax_return(array('error' => 1, 'errortip' => "超过该课程的发放额度！", 'result' => array()));
                }
            }
        }

        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        } else {
            $time = strtotime($time);
        }

        $status = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_class,integralrule_integral,integralrule_type", "integralrule_name = '{$integrallog_rule}'");

        if ($status['integralrule_class'] == '2' || $status['integralrule_class'] == '4') {
            if ($status['integralrule_type'] == '1') {
                if ($balance > $status['integralrule_integral']) {
                    ajax_return(array('error' => 1, 'errortip' => "超过规则限制额度！", 'result' => array()));
                }
            }
        }

        $stuIntOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'");
        $stuIntOne['property_integralbalance'] = $stuIntOne['property_integralbalance'] ? $stuIntOne['property_integralbalance'] : 0;
        $cid = $this->DataControl->getFieldOne("smc_student", "company_id", "student_id = '{$student_id}'");

        if ($balance > 0) {
            $balancelog_data = array();
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['company_id'] = $cid['company_id'];
            $balancelog_data['school_id'] = $this->school_id;
            $balancelog_data['staffer_id'] = $staffer_id;
            $balancelog_data['class_id'] = $class_id;
            $balancelog_data['course_id'] = $course_id;
            $balancelog_data['parenter_id'] = $parenter_id;
            $balancelog_data['integralgoods_id'] = $integralgoods_id;

            $balancelog_data['integrallog_rule'] = $integrallog_rule;
            $balancelog_data['integrallog_playname'] = $playname ? $playname : $this->LgStringSwitch('积分增加');
            $balancelog_data['integrallog_playclass'] = '+';
            $balancelog_data['integrallog_fromamount'] = $stuIntOne['property_integralbalance'];
            $balancelog_data['integrallog_playamount'] = $balance;
            $balancelog_data['integrallog_finalamount'] = $stuIntOne['property_integralbalance'] + $balance;

            $balancelog_data['integrallog_reason'] = $note;
            $balancelog_data['integrallog_time'] = time();
            $this->DataControl->insertData("smc_student_integrallog", $balancelog_data);

            //账户余额
            if ($this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'")) {
                $data = array();
                $data['property_integralbalance'] = $stuIntOne['property_integralbalance'] + $balance;
                $this->DataControl->updateData("smc_student_virtual_property", "student_id='{$student_id}'", $data);
            } else {
                $data = array();
                $data['student_id'] = $student_id;
                $data['property_integralbalance'] = $balance;
                $this->DataControl->insertData("smc_student_virtual_property", $data);
            }
        }
        return true;
    }

    function reduceStuIntegral($student_id, $balance = 0, $integralgoods_id = 0, $integrallog_rule = 0, $staffer_id = 0, $parenter_id = 0, $playname = '', $note = '', $time = '', $class_id, $course_id)
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }
        $stuIntOne = $this->DataControl->getFieldOne("smc_student_virtual_property", "property_integralbalance", "student_id='{$student_id}'");
        $stuIntOne['property_integralbalance'] = $stuIntOne['property_integralbalance'] ? $stuIntOne['property_integralbalance'] : 0;
        $cid = $this->DataControl->getFieldOne("smc_student", "company_id", "student_id = '{$student_id}'");


        if ($stuIntOne['property_integralbalance'] < $balance || !$stuIntOne) {
            $this->error = true;
            $this->errortip = "积分不足";
            return false;
        }

        if ($balance > 0) {
            $balancelog_data = array();
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['company_id'] = $cid['company_id'];
            $balancelog_data['school_id'] = $this->school_id;
            $balancelog_data['staffer_id'] = $staffer_id;
            $balancelog_data['class_id'] = $class_id;
            $balancelog_data['course_id'] = $course_id;
            $balancelog_data['parenter_id'] = $parenter_id;
            $balancelog_data['integralgoods_id'] = $integralgoods_id;

            $balancelog_data['integrallog_rule'] = $integrallog_rule;
            $balancelog_data['integrallog_playname'] = $playname ? $playname : $this->LgStringSwitch('积分减少');
            $balancelog_data['integrallog_playclass'] = '-';
            $balancelog_data['integrallog_fromamount'] = $stuIntOne['property_integralbalance'];
            $balancelog_data['integrallog_playamount'] = $balance;
            $balancelog_data['integrallog_finalamount'] = $stuIntOne['property_integralbalance'] - $balance;

            $balancelog_data['integrallog_reason'] = $note;
            $balancelog_data['integrallog_time'] = $time;
            $logid = $this->DataControl->insertData("smc_student_integrallog", $balancelog_data);

            //账户余额
            $data = array();
            $data['property_integralbalance'] = $stuIntOne['property_integralbalance'] - $balance;
            $this->DataControl->updateData("smc_student_virtual_property", "student_id='{$student_id}'", $data);
        }


        return $logid;
    }


    function reduceCatBalance($student_id, $trading_pid, $coursecatbalance_figure, $paypid, $coursecat_id, $feetype_code, $note = '', $time = '', $coursetype_id = 0)
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }

        if ($coursetype_id == 0) {
            $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursetype_id", "coursecat_id='{$coursecat_id}'");
            $coursetype_id = $coursecatOne['coursetype_id'];
        }

        $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursetype_id,coursecatbalance_figure,coursecatbalance_time,companies_id", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$coursecat_id}' and coursetype_id='{$coursetype_id}' and feetype_code='{$feetype_code}'");
        $log = array();
        $log['student_id'] = $student_id;
        $log['trading_pid'] = $trading_pid;
        $log['coursetype_id'] = $stuCatOne['coursetype_id'];
        $log['coursecat_id'] = $coursecat_id;
        $log['feetype_code'] = $feetype_code;
        $log['school_id'] = $this->school_id;
        $log['companies_id'] = $stuCatOne['companies_id'];
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['pay_pid'] = $paypid;
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
        $log['log_playamount'] = $coursecatbalance_figure;
        $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] - $coursecatbalance_figure;
        $log['log_fromme'] = 0;
        $log['log_playme'] = 0;
        $log['log_finaltime'] = 0;
        $log['log_time'] = $time;
        $log['log_playname'] = $note;
        $log['log_reason'] = $note;
        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

        $data = array();
        $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] - $coursecatbalance_figure;
        $data['coursecatbalance_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_coursecatbalance", "coursetype_id='{$stuCatOne['coursetype_id']}' and student_id='{$student_id}' and school_id='{$this->school_id}' and feetype_code='{$feetype_code}' and coursecat_id='{$coursecat_id}'", $data);

    }

    function reduceTimesCatBalance($student_id, $trading_pid, $coursecatbalance_figure, $num, $paypid, $coursecat_id, $feetype_code, $note = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }


        $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursetype_id,coursecatbalance_figure,coursecatbalance_time,companies_id", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$coursecat_id}' and feetype_code='{$feetype_code}'");

        $log = array();
        $log['student_id'] = $student_id;
        $log['trading_pid'] = $trading_pid;
        $log['coursetype_id'] = $stuCatOne['coursetype_id'];
        $log['coursecat_id'] = $coursecat_id;
        $log['feetype_code'] = $feetype_code;
        $log['school_id'] = $this->school_id;
        $log['companies_id'] = $stuCatOne['companies_id'];
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['pay_pid'] = $paypid;
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
        $log['log_playamount'] = $coursecatbalance_figure;
        $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] - $coursecatbalance_figure;
        $log['log_fromme'] = $stuCatOne['coursecatbalance_time'];
        $log['log_playme'] = $num;
        if ($stuCatOne['coursecatbalance_time'] - $num < 0) {
            $log['log_finaltime'] = 0;
        } else {
            $log['log_finaltime'] = $stuCatOne['coursecatbalance_time'] - $num;
        }
        $log['log_time'] = $time;
        $log['log_playname'] = $note;
        $log['log_reason'] = $note;
        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);
        $data = array();
        $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] - $coursecatbalance_figure;
        if ($stuCatOne['coursecatbalance_time'] - $num < 0) {
            $data['coursecatbalance_time'] = 0;
        } else {
            $data['coursecatbalance_time'] = $stuCatOne['coursecatbalance_time'] - $num;
        }

        $data['coursecatbalance_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_coursecatbalance", "coursetype_id='{$stuCatOne['coursetype_id']}' and student_id='{$student_id}' and school_id='{$this->school_id}' and feetype_code='{$feetype_code}' and coursecat_id='{$coursecat_id}'", $data);

    }


    function reduceItemsCatBalance($student_id, $coursecatbalance_figure, $num, $paypid, $coursecat_id, $feetype_code, $note = '', $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }
        $stuCatOne = $this->DataControl->getFieldOne("smc_student_coursecatbalance", "coursetype_id,coursecatbalance_figure,coursecatbalance_time,companies_id", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and coursecat_id='{$coursecat_id}' and feetype_code='{$feetype_code}'");
        $log = array();
        $log['student_id'] = $student_id;
        $log['coursetype_id'] = $stuCatOne['coursetype_id'];
        $log['coursecat_id'] = $coursecat_id;
        $log['feetype_code'] = $feetype_code;
        $log['school_id'] = $this->school_id;
        $log['companies_id'] = $stuCatOne['companies_id'];
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['pay_pid'] = $paypid;
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $stuCatOne['coursecatbalance_figure'];
        $log['log_playamount'] = $coursecatbalance_figure;
        $log['log_finalamount'] = $stuCatOne['coursecatbalance_figure'] - $coursecatbalance_figure;
        $log['log_fromme'] = $stuCatOne['coursecatbalance_time'];
        $log['log_playme'] = $num;
        $log['log_finaltime'] = $stuCatOne['coursecatbalance_time'] - $num;
        $log['log_time'] = $time;
        $log['log_playname'] = $note;
        $log['log_reason'] = $note;
        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

        $data = array();
        $data['coursecatbalance_figure'] = $stuCatOne['coursecatbalance_figure'] - $coursecatbalance_figure;
        $data['coursecatbalance_time'] = $stuCatOne['coursecatbalance_time'] - $num;
        $data['coursecatbalance_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_coursecatbalance", "student_id='{$student_id}' and school_id='{$this->school_id}' and feetype_code='{$feetype_code}' and coursecat_id='{$coursecat_id}'", $data);

    }

    function reduceStuForward($student_id, $trading_pid, $forward, $companies_id, $time = '')
    {
        if ($time == '') {
            $time = time();
        } else {
            $time = strtotime(date("Y-m-d", $time)) + ((time() + 8 * 3600) % 86400);
        }
        if ($forward > 0) {

            $stublcOne = $this->DataControl->getFieldOne("smc_student", "student_forwardprice", "student_id='{$student_id}' and company_id='{$this->company_id}'");

            if ($stublcOne['student_forwardprice'] < $forward) {
                $this->error = true;
                $this->errortip = "结算金额不正确";
                return false;
            }
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['school_id'] = $this->school_id;
            $balancelog_data['companies_id'] = $companies_id;
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $student_id;
            $balancelog_data['trading_pid'] = $trading_pid;
            $balancelog_data['balancelog_class'] = 1;
            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('账户结转余额抵扣金额');
            $balancelog_data['balancelog_playclass'] = '-';
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
            $balancelog_data['balancelog_playamount'] = $forward;
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_forwardprice'] - $forward;
            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('账户结转余额抵扣金额');
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

            $data = array();
            $data['student_forwardprice'] = $stublcOne['student_forwardprice'] - $forward;
            $data['student_updatatime'] = $time;
            $this->DataControl->updateData("smc_student", "student_id='{$student_id}' and company_id='{$this->company_id}'", $data);
        }
    }

    function addStuForward($student_id, $forward, $trading_pid = '', $companies_id)
    {
        $stublcOne = $this->DataControl->getFieldOne("smc_student", "student_forwardprice", "student_id='{$student_id}' and company_id='{$this->company_id}'");
        $balancelog_data = array();
        $balancelog_data['company_id'] = $this->company_id;
        $balancelog_data['school_id'] = $this->school_id;
        $balancelog_data['companies_id'] = $companies_id;
        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $balancelog_data['student_id'] = $student_id;
        $balancelog_data['trading_pid'] = $trading_pid;
        $balancelog_data['balancelog_class'] = 1;
        $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('满赠获取的结转余额');
        $balancelog_data['balancelog_playclass'] = '+';
        $balancelog_data['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
        $balancelog_data['balancelog_playamount'] = $forward;
        $balancelog_data['balancelog_finalamount'] = $stublcOne['student_forwardprice'] + $forward;
        $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('满赠获取的结转余额');
        $balancelog_data['balancelog_time'] = time();
        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

        $data = array();
        $data['student_forwardprice'] = $stublcOne['student_forwardprice'] + $forward;
        $data['student_updatatime'] = time();
        $this->DataControl->updateData("smc_student", "student_id='{$student_id}' and company_id='{$this->company_id}'", $data);

    }

    function surplusAppointmentCourse($pricing_id, $course_id, $class_id, $num)
    {

        $sql = "select fpt.tuition_id,fpt.tuition_originalprice,fpt.tuition_sellingprice,fpt.tuition_unitprice,fpt.tuition_buypiece,fp.agreement_id,fp.pricing_id
          from smc_fee_pricing_tuition as fpt
          left join smc_course as sc on sc.course_id=fpt.course_id
          left join smc_class as c on c.course_id=sc.course_id
          left join smc_fee_pricing as fp on fp.pricing_id=fpt.pricing_id and fp.course_id=fpt.course_id
          where fpt.pricing_id='{$pricing_id}' and fpt.course_id='{$course_id}' and c.class_id='{$class_id}' limit 0,1
          ";

        $courseOne = $this->DataControl->selectOne($sql);
        if ($courseOne) {
            $data = array();
            $fitOne = $this->DataControl->getFieldOne("smc_fee_pricing_fit", "fit_price", "tuition_id='{$courseOne['tuition_id']}' and fit_buypiece='{$num}'");
            if ($fitOne) {
                $data['allPrice'] = $fitOne['fit_price'];
                $data['unitexpend'] = ceil($fitOne['fit_price'] / $num);
            } else {
                if ($num == $courseOne['tuition_buypiece']) {
                    $data['allPrice'] = $courseOne['tuition_sellingprice'];
                } else {
                    $data['allPrice'] = $num * ceil($courseOne['tuition_sellingprice'] / $courseOne['tuition_buypiece']);
                }
                $data['unitexpend'] = ceil($courseOne['tuition_sellingprice'] / $courseOne['tuition_buypiece']);
            }


            $data['unitrefund'] = $courseOne['tuition_unitprice'];
            $data['pricing_id'] = $courseOne['pricing_id'];
            $data['agreement_id'] = $courseOne['agreement_id'];
            $data['surplusNum'] = $num;
            return $data;
        } else {
            $this->error = true;
            $this->errortip = "无班级";
            return false;
        }
    }


    //查看一个班的剩余课程
    function surplusCourse($pricing_id, $course_id, $class_id, $starttime = '', $num = '')
    {
        if (isset($starttime) && $starttime != '') {
            $sql = "select fpt.tuition_id,fpt.tuition_originalprice,fpt.tuition_sellingprice,fpt.tuition_unitprice,fpt.tuition_buypiece,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=c.class_id and ch.hour_isfree='0' and ch.hour_ischecking='0' and hour_day>='{$starttime}') as num,fp.agreement_id,fp.pricing_id,sc.course_inclasstype
              from smc_fee_pricing_tuition as fpt
              left join smc_course as sc on sc.course_id=fpt.course_id
              left join smc_class as c on c.course_id=sc.course_id
              left join smc_fee_pricing as fp on fp.pricing_id=fpt.pricing_id and fp.course_id=fpt.course_id
              where fpt.pricing_id='{$pricing_id}' and fpt.course_id='{$course_id}' and c.class_id='{$class_id}' limit 0,1
              ";
        } else {
            $sql = "select fpt.tuition_id,fpt.tuition_originalprice,fpt.tuition_sellingprice,fpt.tuition_unitprice,fpt.tuition_buypiece,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=c.class_id and ch.hour_isfree='0' and ch.hour_ischecking='0') as num,fp.agreement_id,fp.pricing_id,sc.course_inclasstype
              from smc_fee_pricing_tuition as fpt
              left join smc_course as sc on sc.course_id=fpt.course_id
              left join smc_class as c on c.course_id=sc.course_id
              left join smc_fee_pricing as fp on fp.pricing_id=fpt.pricing_id and fp.course_id=fpt.course_id
              where fpt.pricing_id='{$pricing_id}' and fpt.course_id='{$course_id}' and c.class_id='{$class_id}' limit 0,1
              ";
        }

        $courseOne = $this->DataControl->selectOne($sql);

        $scheduleOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$class_id}'");
        if ($courseOne) {
            $data = array();
            if (!$scheduleOne) {
                $courseOne['num'] = $courseOne['tuition_buypiece'];
            }

            if ($courseOne['num'] == $courseOne['tuition_buypiece']) {
                $data['allPrice'] = $courseOne['tuition_sellingprice'];
            } else {
                $data['allPrice'] = $courseOne['num'] * ceil($courseOne['tuition_sellingprice'] / $courseOne['tuition_buypiece']);
            }
            $data['surplusNum'] = $courseOne['num'];
            if ($data['allPrice'] > 0 && $courseOne['num'] > 0) {
                $data['unitexpend'] = ceil($data['allPrice'] / $courseOne['num']);
            } else {
                $data['unitexpend'] = 0;
            }
            $data['course_inclasstype'] = $courseOne['course_inclasstype'];
            $data['unitrefund'] = $courseOne['tuition_unitprice'];
            $data['pricing_id'] = $courseOne['pricing_id'];
            $data['agreement_id'] = $courseOne['agreement_id'];
            $data['tuition_id'] = $courseOne['tuition_id'];
            $data['monthArray'] = array();
            return $data;
        } else {
            $this->error = true;
            $this->errortip = "无班级";
            return false;
        }
    }

    //查看一个期度班的剩余课程及收费
    function surplusStageCourse($pricing_id, $course_id, $class_id, $starttime = '')
    {

        if ($starttime == '') {
            $starttime = date("Y-m-d");
        }

        $sql = "select ch.hour_day from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_iswarming=0 and ch.hour_ischecking<>'-1' order by ch.hour_lessontimes desc,ch.hour_day desc limit 0,1";
        $hourOne = $this->DataControl->selectOne($sql);

        $sql = "select ch.hour_day from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_iswarming=0 and ch.hour_ischecking<>'-1' order by ch.hour_lessontimes asc,ch.hour_day asc limit 0,1";
        $hourFirstOne = $this->DataControl->selectOne($sql);

        $sql = "select fpt.tuition_originalprice,fpt.tuition_sellingprice,fpt.tuition_unitprice,fpt.tuition_buypiece,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_tuition as fpt
              left join smc_course as sc on sc.course_id=fpt.course_id
              left join smc_class as c on c.course_id=sc.course_id
              left join smc_fee_pricing as fp on fp.pricing_id=fpt.pricing_id and fp.course_id=fpt.course_id
              where fpt.pricing_id='{$pricing_id}' and fpt.course_id='{$course_id}' and c.class_id='{$class_id}' 
              limit 0,1
              ";
        $courseOne = $this->DataControl->selectOne($sql);


        $monarr = array();
        $data = array();
        if ($hourOne['hour_day'] > $starttime) {
            if ($hourFirstOne['hour_day'] > $starttime) {
                $starttime = $hourFirstOne['hour_day'];
            }

            $time1 = date("Y-m-01", strtotime($starttime));
            $time2 = $hourOne['hour_day'];

            while (date("Y-m", strtotime($time1)) <= date("Y-m", strtotime($time2))) {
                $monarr[] = date('Y-m', strtotime($time1));
                $time1 = date("Y-m-01", strtotime("+1 month", strtotime($time1)));
            }

            $data['unitrefund'] = $courseOne['tuition_sellingprice'];
            $data['pricing_id'] = $courseOne['pricing_id'];
            $data['agreement_id'] = $courseOne['agreement_id'];

            $sql = "select (select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=c.class_id and substring(ch.hour_day,1,7)='{$monarr['0']}' and ch.hour_isfree='0') as all_num
                  ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=c.class_id and substring(ch.hour_day,1,7)='{$monarr['0']}' and ch.hour_isfree='0' and ch.hour_day>='{$starttime}') as need_num
                  from smc_class as c where c.class_id='{$class_id}'";
            $classHourOne = $this->DataControl->selectOne($sql);

            $allPrice = 0;

            foreach ($monarr as $key => $monOne) {
                if (!$this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$class_id}' and substring(hour_day,1,7)='{$monOne}' and hour_ischecking=0")) {
                    unset($monarr[$key]);
                    continue;
                }

                $tem_data = array();
                $ratio = $this->getMonthdis($course_id, $monOne);

                if ($key == 0) {
                    if ($classHourOne['need_num'] == $classHourOne['all_num']) {
                        $tem_data['price'] = $courseOne['tuition_sellingprice'] * $ratio;
                        $tem_data['sellingprice'] = $courseOne['tuition_sellingprice'] * $ratio;
                    } else {
                        $tem_data['price'] = ceil($classHourOne['need_num'] * $courseOne['tuition_sellingprice'] / $classHourOne['all_num']) * $ratio;
                        $tem_data['sellingprice'] = ceil($classHourOne['need_num'] * $courseOne['tuition_sellingprice'] / $classHourOne['all_num']) * $ratio;
                    }
                } else {
                    $tem_data['price'] = $courseOne['tuition_sellingprice'] * $ratio;
                    $tem_data['sellingprice'] = $courseOne['tuition_sellingprice'] * $ratio;
                }
                $allPrice += $tem_data['price'];
                $tem_data['month'] = $monOne;
                $data['monthArray'][] = $tem_data;
            }

            $data['surplusNum'] = $monarr ? count($monarr) : 0;
            $data['allPrice'] = $allPrice;

        } else {
            $data['surplusNum'] = 0;
            $data['unitrefund'] = $courseOne['tuition_sellingprice'];
            $data['pricing_id'] = $courseOne['pricing_id'];
            $data['agreement_id'] = $courseOne['agreement_id'];
            $data['allPrice'] = 0;
            $data['monthArray'] = array();
        }
        return $data;
    }

    function getMonthdis($course_id, $month)
    {

//        $sql="select cm.monthdis_ratio,cm.monthdis_id
//              from smc_course as sc
//              left join smc_code_coursecat_monthdis as cm on cm.coursecat_id=sc.coursecat_id
//              where sc.course_id='{$course_id}' and cm.monthdis_month='{$month}'";

        $courseOne = $this->DataControl->getFieldOne("smc_course", "coursecat_id", "course_id='{$course_id}'");

        $sql = "select cm.monthdis_ratio,cm.monthdis_id 
from gmc_code_monthdis as cm 
              where cm.monthdis_month='{$month}' and cm.company_id='{$this->company_id}'
              and (cm.monthdis_applyschool=0 or (cm.monthdis_applyschool=1 and exists(select 1 from gmc_monthdis_schoolapply as sa where sa.monthdis_id=cm.monthdis_id and sa.school_id='{$this->school_id}')))
              and (cm.monthdis_applycoursecat=0 or (cm.monthdis_applycoursecat=1 and exists(select 1 from gmc_monthdis_coursecatapply as mc where mc.monthdis_id=cm.monthdis_id and mc.coursecat_id='{$courseOne['coursecat_id']}')))
              ";

        $monthdisOne = $this->DataControl->selectOne($sql);
        if ($monthdisOne) {
            return $monthdisOne['monthdis_ratio'];
        } else {
            return 1;
        }
    }

    function appointmentCourseOne($pricing_id, $course_id, $num)
    {
        $sql = "select fpt.tuition_id,fpt.tuition_originalprice,fpt.tuition_sellingprice,fpt.tuition_buypiece,fpt.tuition_unitprice
              from smc_fee_pricing_tuition as fpt
              where fpt.pricing_id='{$pricing_id}' and fpt.course_id='{$course_id}' limit 0,1
              ";

        $courseOne = $this->DataControl->selectOne($sql);

        if ($courseOne) {
            $data = array();

            $fitOne = $this->DataControl->getFieldOne("smc_fee_pricing_fit", "fit_price", "tuition_id='{$courseOne['tuition_id']}' and fit_buypiece='{$num}'");
            if ($fitOne) {
                $data['allPrice'] = $fitOne['fit_price'];
                $data['unitexpend'] = ceil($fitOne['fit_price'] / $num);
            } else {
                if ($num == $courseOne['tuition_buypiece']) {
                    $data['allPrice'] = $courseOne['tuition_sellingprice'];
                } else {
                    $data['allPrice'] = $num * ceil($courseOne['tuition_sellingprice'] / $courseOne['tuition_buypiece']);
                }
                $data['unitexpend'] = ceil($courseOne['tuition_sellingprice'] / $courseOne['tuition_buypiece']);
            }

            $data['buypiece'] = $num;
            $data['unitrefund'] = $courseOne['tuition_unitprice'];
            return $data;
        } else {
            $this->error = true;
            $this->errortip = "无班级";
            return false;
        }
    }

    //获取单个课程的信息
    function courseOne($pricing_id, $course_id)
    {
        $sql = "select fpt.tuition_id,fpt.tuition_originalprice,fpt.tuition_sellingprice,fpt.tuition_buypiece,fpt.tuition_unitprice
              from smc_fee_pricing_tuition as fpt
              where fpt.pricing_id='{$pricing_id}' and fpt.course_id='{$course_id}' limit 0,1
              ";
        $courseOne = $this->DataControl->selectOne($sql);

        if ($courseOne) {
            $data = array();
            $data['allPrice'] = $courseOne['tuition_sellingprice'];
            $data['buypiece'] = $courseOne['tuition_buypiece'];
            $data['unitexpend'] = ceil($courseOne['tuition_sellingprice'] / $courseOne['tuition_buypiece']);
            $data['unitrefund'] = $courseOne['tuition_unitprice'];
            $data['tuition_id'] = $courseOne['tuition_id'];
            return $data;
        } else {
            $this->error = true;
            $this->errortip = "无班级";
            return false;
        }
    }

    function loss($student_id, $is_empty_balance = 0, $is_empty_forward, $time = '')
    {
        if (!$time) {
            $time = time();
        } elseif ($time == strtotime(date("Y-m-d"))) {
            $time = time();
        }

        do {
            $trading_pid = $this->createOrderPid('RJ');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

        if ($is_empty_balance == 1) {

            $sql = "select * from smc_student_balance 
                  where school_id='{$this->school_id}' and student_id='{$student_id}' and (student_balance>0 or student_withholdbalance>0)";

            $balanceList = $this->DataControl->selectClear($sql);

            if ($balanceList) {
                foreach ($balanceList as $balanceOne) {
                    if ($balanceOne['student_balance'] > 0) {
                        $balancelog_data = array();
                        $balancelog_data['company_id'] = $this->company_id;
                        $balancelog_data['companies_id'] = $balanceOne['companies_id'];
                        $balancelog_data['school_id'] = $this->school_id;
                        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                        $balancelog_data['student_id'] = $student_id;
                        $balancelog_data['trading_pid'] = $trading_pid;
                        $balancelog_data['balancelog_class'] = 0;
                        $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('流失账户余额清零');
                        $balancelog_data['balancelog_playclass'] = '-';
                        $balancelog_data['balancelog_fromamount'] = $balanceOne['student_balance'];
                        $balancelog_data['balancelog_playamount'] = $balanceOne['student_balance'];
                        $balancelog_data['balancelog_finalamount'] = 0;
                        $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('流失账户余额清零');
                        $balancelog_data['balancelog_time'] = $time;
                        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
                    }

                    if ($balanceOne['student_withholdbalance'] > 0) {
                        $balancelog_data = array();
                        $balancelog_data['company_id'] = $this->company_id;
                        $balancelog_data['companies_id'] = $balanceOne['companies_id'];
                        $balancelog_data['school_id'] = $this->school_id;
                        $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
                        $balancelog_data['student_id'] = $student_id;
                        $balancelog_data['trading_pid'] = $trading_pid;
                        $balancelog_data['balancelog_class'] = 2;
                        $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('流失不可退账户余额清零');
                        $balancelog_data['balancelog_playclass'] = '-';
                        $balancelog_data['balancelog_fromamount'] = $balanceOne['student_withholdbalance'];
                        $balancelog_data['balancelog_playamount'] = $balanceOne['student_withholdbalance'];
                        $balancelog_data['balancelog_finalamount'] = 0;
                        $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('流失不可退账户余额清零');
                        $balancelog_data['balancelog_time'] = $time;
                        $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
                    }

                    if (($balanceOne['student_balance'] + $balanceOne['student_withholdbalance']) > 0) {
                        $data = array();
                        $data['trading_pid'] = $trading_pid;
                        $data['company_id'] = $this->company_id;
                        $data['school_id'] = $this->school_id;
                        $data['companies_id'] = $balanceOne['companies_id'];
                        $data['student_id'] = $student_id;
                        $data['tradingtype_code'] = 'Subscribed';
                        $data['trading_status'] = "1";
                        $data['trading_createtime'] = $time;
                        $data['staffer_id'] = $this->stafferOne['staffer_id'];
                        $this->DataControl->insertData("smc_student_trading", $data);

                        $in_data = array();
                        $in_data['company_id'] = $this->company_id;
                        $in_data['companies_id'] = $balanceOne['companies_id'];
                        $in_data['school_id'] = $this->school_id;
                        $in_data['income_type'] = '1';
                        $in_data['student_id'] = $student_id;
                        $in_data['trading_pid'] = $trading_pid;
                        $in_data['income_price'] = $balanceOne['student_balance'] + $balanceOne['student_withholdbalance'];
                        $in_data['income_note'] = $this->LgStringSwitch('学员流失认缴收入');
                        $in_data['income_confirmtime'] = $time;
                        $in_data['income_audittime'] = $time;
                        $in_data['income_createtime'] = time();
                        $this->DataControl->insertData("smc_school_income", $in_data);
                    }
                }
            }

            //结算账户余额
            $data = array();
            $data['student_balance'] = '0';
            $data['student_withholdbalance'] = '0';
            $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}'", $data);

        }

        $stublcOne = $this->DataControl->getFieldOne("smc_student", "student_forwardprice", "student_id='{$student_id}'");

        if ($is_empty_forward == 1 && $stublcOne['student_forwardprice'] > 0) {
            $balancelog = array();
            $balancelog['company_id'] = $this->company_id;
            $balancelog['school_id'] = $this->school_id;
            $balancelog['companies_id'] = $this->schoolOne['companies_id'];
            $balancelog['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog['student_id'] = $student_id;
            $balancelog['trading_pid'] = $trading_pid;
            $balancelog['balancelog_class'] = 1;
            $balancelog['balancelog_playname'] = $this->LgStringSwitch('流失账户结转余额清零');
            $balancelog['balancelog_playclass'] = '-';
            $balancelog['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
            $balancelog['balancelog_playamount'] = $stublcOne['student_forwardprice'];
            $balancelog['balancelog_finalamount'] = 0;
            $balancelog['balancelog_reason'] = $this->LgStringSwitch('流失账户结转余额清零');
            $balancelog['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog);
            //结算余额变更
            $data = array();
            $data['student_forwardprice'] = '0';
            $data['student_updatatime'] = $time;
            $this->DataControl->updateData("smc_student", "student_id='{$student_id}' and company_id='{$this->company_id}'", $data);

        }
    }

    function checkStuTimes($student_id, $class_id, $entryDay = '')
    {
        if ($entryDay == '') {
            $entryDay = date("Y-m-d");
        }

        $sql = "select scb.coursebalance_time,co.course_islimittimes,cl.class_type
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=cl.class_id and ch.hour_ischecking=0 and ch.hour_iswarming=0 and ch.hour_day>='{$entryDay}') as num
              from smc_student_coursebalance as scb,smc_class as cl,smc_course as co 
              where scb.course_id=cl.course_id and cl.course_id=co.course_id and scb.student_id='{$student_id}' and scb.school_id='{$this->school_id}' and cl.class_id='{$class_id}' ";
        $stuCourseBalance = $this->DataControl->selectOne($sql);

        if ($stuCourseBalance['course_islimittimes'] == 1 && $stuCourseBalance['class_type'] == 0) {

            if ($stuCourseBalance['coursebalance_time'] != $stuCourseBalance['num']) {
                $this->errortip = "学员剩余课次必须等于班级剩余课次";
                return false;
            } else {
                return true;
            }
        } else {
            return true;
        }
    }


}