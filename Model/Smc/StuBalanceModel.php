<?php


namespace Model\Smc;

class StuBalanceModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //账户充值
    function stuDepositOne($request)
    {
        $sql = "select a.companies_id,b.companies_issupervise
			from smc_school_coursecat_subject a
 			left JOIN gmc_code_companies b ON a.companies_id = b.companies_id 
 			where a.school_id = '{$request['school_id']}' 
 			and a.coursecat_id='{$request['coursecat_id']}' 
 			limit 0,1";
        $companiesOne = $this->DataControl->selectOne($sql);
        if (!$companiesOne) {
            return false;
        } else {
            if ($companiesOne['companies_issupervise'] == '1') {
                $this->error = true;
                $this->errortip = "充值订单失败：该主体已被监管，请按班级购买课程！";
                return false;
            }
        }

        $studentOne = $this->DataControl->getOne('smc_student', "student_id='{$request['student_id']}'");
        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "充值订单失败：未查询到学生信息！";
            return false;
        }

        $coursecatOne=$this->DataControl->getFieldOne("smc_code_coursecat","coursecat_branch","coursecat_id='{$request['coursecat_id']}'");

        if($coursecatOne['coursecat_branch']!='N'){
            $this->error = true;
            $this->errortip = "充值订单失败：只开放年费充值！";
            return false;
        }

        // if($this->company_id=='8888' && $request['coursetype_id']=='65' && $request['price']<12000){
        //     $this->error = true;
        //     $this->errortip = "充值订单失败：充值金额不可低于12000！";
        //     return false;
        // }



        if (isset($request['create_time'])) {
            $create_time = strtotime($request['create_time']);
        } else {
            $create_time = "";
        }

        $orderHandleModel = new   \Model\Smc\OrderModel($request);
        $price = $request['price'];
        $taglist = $request['taList'];

        $information = $this->LgStringSwitch("订单提交成功，待支付");
//		 $information = $this->LgStringSwitch("老生账户充值");
        //生成订单 -- 自动生成交易记录
        $order_pid = $orderHandleModel->createOrder($request['student_id'], $code = "Recharge", $price, $taglist, $order_type = 2, $coupon_price = 0, $market_price = 0, $this->LgStringSwitch("账户充值"), $information, $request['order_remarks'], 0, 1, $create_time, 0, 1, 0, $request['coursetype_id'], $request['coursecat_id']);

        return $order_pid;
    }

//	  补费订单列表getOrderList
    function getOrderList($request)
    {
        $datawhere = "";
        if ($request['staffer_id'] == '27550') {
            $datawhere .= " and po.companies_id in(8,78606) ";
        }
        $sql = "select po.order_pid,po.order_type,group_concat(cf.feeitem_cnname) as feeitem_cnname,s.staffer_cnname,po.order_updatatime,po.order_createtime,l.school_cnname,po.order_allprice,po.order_coupon_price,po.order_paidprice,po.mergeorder_pid
			from smc_payfee_order  as po
 			left JOIN smc_payfee_order_item AS oi ON oi.order_pid = po.order_pid
 			left JOIN smc_staffer as s ON s.staffer_id = po.staffer_id
 			left JOIN smc_code_feeitem as cf  ON cf.feeitem_branch =oi.feeitem_branch and cf.company_id='{$request['company_id']}'
			left JOIN smc_school as l ON l.school_id = po.school_id
 			where po.student_id = '{$request['student_id']}' 
 			and po.school_id='{$request['school_id']}' 
 			{$datawhere}
 			and (po.order_status = 1 or po.order_status = 2 or po.order_status = 3) 
 			group by po.order_pid";
        $orderList = $this->DataControl->selectClear($sql);

        if (!$orderList) {
            $orderList = array();
        } else {
            $status = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
            foreach ($orderList as &$val) {
                $val['order_type'] = $status[$val['order_type']];
            }
            foreach ($orderList as $key => $value) {
                if (empty($value['order_updatatime'])) {
                    $orderList[$key]['order_time'] = date('Y-m-d', $value['order_createtime']);
                } else {
                    $orderList[$key]['order_time'] = date('Y-m-d', $value['order_updatatime']);
                }

            }
        }
        return $orderList;
    }

    function getRefundBalance($request)
    {
        $sql = "select sb.*,cc.companies_cnname 
              from smc_student_balance as sb,gmc_code_companies as cc 
              where sb.companies_id=cc.companies_id and sb.school_id = '{$request['school_id']}' and sb.student_id='{$request['student_id']}' and sb.company_id='{$request['company_id']}'";

        $balanceList = $this->DataControl->selectClear($sql);

        return $balanceList ? $balanceList : array();
    }

    function getDepositCoursetypeBySchool($request)
    {
        $sql = "select a.coursetype_id,b.coursetype_branch,b.coursetype_cnname
            from smc_deposit a,smc_code_coursetype b 
            where 1
            and a.coursetype_id=b.coursetype_id
            and a.company_id='{$this->company_id}'
            and a.deposit_status=1
            and (a.deposit_applyschool=0 or exists(select 1 from smc_deposit_applyschool where deposit_id=a.deposit_id and school_id='{$request['school_id']}'))
            group by a.coursetype_id
            order by a.coursetype_id";

        $coursetypeList = $this->DataControl->selectClear($sql);

        if (!$coursetypeList) {
            $this->error = true;
            $this->errortip = "本校暂无定金设置，请联系集团财务添加！";
            return false;
        }
        return $coursetypeList;
    }

    function getDepositCoursecatBySchool($request)
    {
        $sql = "select a.coursecat_id,b.coursecat_branch,b.coursecat_cnname
            from smc_deposit a,smc_code_coursecat b 
            where 1
            and a.coursecat_id=b.coursecat_id
            and a.coursetype_id='{$request['coursetype_id']}'
            and a.company_id='{$this->company_id}'
            and a.deposit_status=1
            and (a.deposit_applyschool=0 or exists(select 1 from smc_deposit_applyschool where deposit_id=a.deposit_id and school_id='{$request['school_id']}'))
            group by a.coursecat_id
            order by a.coursecat_id";

        $coursetypeList = $this->DataControl->selectClear($sql);

        if (!$coursetypeList) {
            $this->error = true;
            $this->errortip = "本校暂无定金设置，请联系集团财务添加！";
            return false;
        }
        return $coursetypeList;
    }

    function getDepositPriceBySchool($request)
    {
        $sql = "select a.deposit_id,a.deposit_price
            from smc_deposit a 
            where 1
            and a.deposit_status=1
            and a.company_id='{$this->company_id}'
			and a.coursetype_id='{$request['coursetype_id']}'
			and a.coursecat_id='{$request['coursecat_id']}'
            and (a.deposit_applyschool=0 or exists(select 1 from smc_deposit_applyschool where deposit_id=a.deposit_id and school_id='{$request['school_id']}'))
			order by deposit_price desc";

        $priceList = $this->DataControl->selectClear($sql);

        if (!$priceList) {
            $this->error = true;
            $this->errortip = "本校暂无定金设置，请联系集团财务添加！";
            return false;
        }

        return $priceList;
    }

    //账户充值
    function stuDepositChargeOne($request)
    {
        $studentOne = $this->DataControl->getOne('smc_student', "student_id='{$request['student_id']}'");
        if (!$studentOne) {
            return false;
        }

        if (!isset($request['coursecat_id']) || $request['coursecat_id'] == '' || $request['coursecat_id'] == 0) {
            $this->error = true;
            $this->errortip = "请选择班种";
            return false;
        }

        $sql = "select a.companies_id,b.companies_issupervise
			from smc_school_coursecat_subject a
 			left JOIN gmc_code_companies b ON a.companies_id = b.companies_id 
 			where a.school_id = '{$request['school_id']}' 
 			and a.coursecat_id='{$request['coursecat_id']}' 
 			limit 0,1";
        $companiesOne = $this->DataControl->selectOne($sql);
        if (!$companiesOne) {
            return false;
        } else {
            if ($companiesOne['companies_issupervise'] == '1') {
                $this->error = true;
                $this->errortip = "定金充值失败：该主体已被监管，请按班级购买课程！";
                return false;
            }
        }

        $coursecatOne=$this->DataControl->getFieldOne("smc_code_coursecat","coursecat_branch","coursecat_id='{$request['coursecat_id']}'");

        if($coursecatOne['coursecat_branch']!='N'){
            $this->error = true;
            $this->errortip = "充值订单失败：只开放年费充值！";
            return false;
        }

        // if($this->company_id=='8888' && $request['coursetype_id']=='65' && $request['price']<12000){
        //     $this->error = true;
        //     $this->errortip = "充值订单失败：充值金额不可低于12000！";
        //     return false;
        // }

        if (isset($request['create_time'])) {
            $create_time = strtotime($request['create_time']);
        } else {
            $create_time = "";
        }

        $orderHandleModel = new \Model\Smc\OrderModel($request);
        $price = $request['price'];
        $taglist = $this->LgStringSwitch("定金充值");
        $coursetype_id = $request['coursetype_id'];
        $coursecat_id = $request['coursecat_id'];

        $information = $this->LgStringSwitch("订单提交成功，待支付");

        $companiesOne = $this->getSchoolCourseCompanies($this->school_id, $coursecat_id);

        //生成订单 -- 自动生成交易记录
        $order_pid = $orderHandleModel->createOrder($request['student_id'], $code = "DepositCharge", $price, $taglist, $order_type = 2, $coupon_price = 0, $market_price = 0, $this->LgStringSwitch("定金充值"), $information, $request['remark'], 0, 1, $create_time, 0, 1, 0, $coursetype_id, $coursecat_id, $companiesOne['companies_id']);

        return $order_pid;
    }

}
