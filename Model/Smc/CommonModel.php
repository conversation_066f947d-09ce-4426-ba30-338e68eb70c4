<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Smc;

class CommonModel extends modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray=array()) {
        parent::__construct ();
        if(is_array($publicarray)){
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray){
        if(isset($publicarray['company_id'])){
            $this->company_id = $publicarray['company_id'];
        }else{
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if(isset($publicarray['school_id'])){
            $this->school_id = $publicarray['school_id'];
        }else{
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if(isset($publicarray['staffer_id'])){
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id=$publicarray['staffer_id'];
        }else{
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }
    //验证订单信息
    function verdictStaffer($staffer_id){

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname,staffer_enname,staffer_mobile","staffer_id = '{$staffer_id}'");

        if(!$this->stafferOne){
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }else{
            return true;
        }
    }

    /**
     * @param $company_id
     * 用户学校切换记录
     */
    function addStafferSchoolAction($request) {
        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['school_id'] = $request['newschool_id'];
        $data['postbe_id'] = $request['re_postbe_id'];
        $data['staffer_id'] = $request['staffer_id'];
        $data['marketer_id'] = $request['marketer_id'];
        $data['schoollog_port'] = 2;
        $data['schoollog_createtime'] = time();
        $log_id = $this->DataControl->insertData('imc_staffer_schoollog',$data);
        if($log_id){
            return true;
        }else{
            return false;
        }
    }

    //区域列表
    function getDistrictApi($paramArray)
    {
        $sqlfields = " d.district_id,d.company_id,d.district_branch,d.district_cnname,d.district_sort,d.district_content ";
        $datawhere = " d.company_id = '{$paramArray['company_id']}' ";
        $orderby = " d.district_sort ASC,d.district_id ASC ";
        $districtList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM gmc_company_district as d where {$datawhere} order by {$orderby} ");;
        return $districtList;
    }
    //城市列表 -- 台湾的数据
    function getTwCityApi($paramArray)
    {
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '5005' and r.region_level='3' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $cityList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");;
        return $cityList;
    }

    //地区列表
    function getAreaApi($paramArray)
    {
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '{$paramArray['region_id']}' and r.region_level='4' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $cityList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");;
        return $cityList;
    }
    //城市列表
    function getCityApi($paramArray)
    {
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '{$paramArray['region_id']}' and r.region_level='3' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $cityList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");;
        return $cityList;
    }
    //省份列表
    function getProvinceApi($paramArray)
    {
        $sqlfields = " r.region_id,r.region_initial,r.region_iscrown,r.region_code,r.region_name,r.region_enname,r.region_shortenname ";
        $datawhere = " r.parent_id = '1' and r.region_level='2' ";
        $orderby = " r.region_initial ASC,r.region_sort ASC,r.region_id ASC ";
        $cityList = $this->DataControl->selectClear("SELECT {$sqlfields} FROM smc_code_region as r where {$datawhere} order by {$orderby} ");;
        return $cityList;
    }
    //首页 -- 月份列表日程展示
    function monthEventApi($paramArray)
    {
        $date = getthemonth($paramArray['yearMonth']);
        //当前日期
        $sdefaultDate = date("Y-m-d");

        $sql = "select event_id,event_time
                from crm_event 
                where marketer_id='{$paramArray['marketer_id']}'  and event_time between '{$date[0]}' and '{$date[1]}' 
                GROUP BY event_time";
        $mothListArray = $this->DataControl->selectClear($sql);

        if($mothListArray) {
            foreach($mothListArray as $k=>&$v) {
                $v['year'] = date('Y',strtotime($v['event_time']));
                $v['month'] = date('m',strtotime($v['event_time']));
                $v['day'] = date('d',strtotime($v['event_time']));
                $v['week'] = date('w',strtotime($v['year']-$v['month']-$v['day']));
                unset($mothListArray[$k]['event_time']);
            }
            $monthArr = array_column($mothListArray,'day');
        }

        $count = date('j',strtotime($date[1]));
        if($mothListArray) {
            for($i=1;$i<=$count;$i++) {
                if($i < 10) {
                    $i = '0'.$i;
                }
                if(!in_array($i,$monthArr)) {
                    $data['year'] = date('Y',strtotime($date[0]));
                    $data['month'] = date('m',strtotime($date[0]));
                    $data['day'] = $i;
                    $data['week'] = date('w',strtotime($data['year']."-".$data['month']."-".$data['day']));

                    if(($data['year']."-".$data['month']."-".$data['day']) == $sdefaultDate){
                        $dateWeek[$i]['isnow'] = 1;
                    }else{
                        $dateWeek[$i]['isnow'] = 0;
                    }

                    $data['is_have'] = strval(-1);
                    array_push($mothListArray,$data);
                }
                usort($mothListArray,function($a,$b){
                    if($a['day'] == $b['day']) return 0;
                    return $a['day'] > $b['day']?1:-1;
                });
            }
        } else {
            $mothListArray = array();
            for($i=1;$i<=$count;$i++) {
                if($i < 10) {
                    $i = '0'.$i;
                }
                $data = array();
                $data['year'] = date('Y',strtotime($date[0]));
                $data['month'] = date('m',strtotime($date[0]));
                $data['day'] = $i;
                $data['week'] = date('w',strtotime($data['year']."-".$data['month']."-".$data['day']));

                if(($data['year']."-".$data['month']."-".$data['day']) == $sdefaultDate){
                    $dateWeek[$i]['isnow'] = 1;
                }else{
                    $dateWeek[$i]['isnow'] = 0;
                }

                $data['is_have'] = strval(-1);
                array_push($mothListArray,$data);
            }
        }

        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first=0;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w=date('w',strtotime($sdefaultDate));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start=date('Y-m-d',strtotime("$sdefaultDate -".($w ? $w - $first : 6).' days'));
        //本周结束日期
        $week_end=date('Y-m-d',strtotime("$week_start +6 days"));
        //echo "$week_start"."$week_end";
        //组合数据
        $dateWeek = [];
        for ($i=0; $i<=6; $i++){
            $dateWeek[$i]['yearMonth'] = date('Y-m',strtotime("$week_start + $i days"));
            $dateWeek[$i]['daytime'] = date('Y-m-d',strtotime("$week_start + $i days"));
            $dateWeek[$i]['day'] = date('d',strtotime("$week_start + $i days"));
            $dateWeek[$i]['week'] = date('w',strtotime("$week_start + $i days"));
            if($dateWeek[$i]['daytime'] == $sdefaultDate){
                $dateWeek[$i]['isnow'] = 1;
            }else{
                $dateWeek[$i]['isnow'] = 0;
            }
        }

        $data = array();
        $data['week'] = $dateWeek;
        $data['mothList'] = $mothListArray;
        return $data;
    }

    //当前意向客户数
//        $princal_where = '1';
//        if (isset($request['postbe_crmuserlevel']) && ($request['postbe_crmuserlevel'] !=1 && $request['postbe_crmuserlevel'] !=2)) {
//            $princal_where .= " and p.marketer_id ='{$request['marketer_id']}' and p.principal_leave =0 ";
//        }
//        $intentionCount = $this->DataControl->selectOne("select count( DISTINCT ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs,
//crm_client_principal as p
//where cs.client_id=ct.client_id and ct.client_id = p.client_id and cs.school_id = p.school_id and cs.school_id ='{$request['school_id']}'  and ct.client_distributionstatus = '1' AND ct.client_tracestatus NOT IN (-1,-2,4)  and ct.company_id='{$request['company_id']}' and {$princal_where}  and ct.client_isgross = '0'
//and cs.is_enterstatus=1 and cs.school_id = (select cl.school_id from crm_client_principal as cl where cl.client_id =ct.client_id order by principal_createtime DESC limit 0,1 ) ");
    function getEnrollStuCount($request){
        $companyOne = $this->DataControl->selectOne("select company_id,company_isnointention from gmc_company where company_id = '{$request['company_id']}' ");

        $todaytime = date("Y-m-d");
        //待筛选毛名单
        $clientgrossnumCount = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id=ct.client_id and cs.school_id ='{$request['school_id']}' and ct.client_distributionstatus =0 and client_tracestatus =0  and ct.company_id='{$request['company_id']}' and cs.is_enterstatus=1 and ct.client_isgross = '1' and ct.client_schtmkdistributionstatus = '0' ");
        //当前有效名单数
        $clientnumCount = $this->DataControl->selectOne("select count(DISTINCT ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs 
where cs.client_id=ct.client_id and ct.client_distributionstatus =0 and cs.school_id ='{$request['school_id']}' and ct.company_id='{$request['company_id']}' and cs.is_enterstatus=1 and ct.client_isgross = '0' and (ct.client_tracestatus =0 or ct.client_tracestatus =1)  ");//and client_tracestatus =0   //ct.client_tracestatus > -1 and ct.client_tracestatus < 4 
        //当前意向客户数
        $princal_where = '1';
        if (isset($request['postbe_crmuserlevel']) && ($request['postbe_crmuserlevel'] !=1 && $request['postbe_crmuserlevel'] !=2 && $request['postbe_crmuserlevel'] !=3)) {
            $princal_where .= " and p.marketer_id ='{$request['marketer_id']}' and p.principal_leave =0 ";
        }
        $intentionCount = $this->DataControl->selectOne("select count( DISTINCT c.client_id) as client_num from crm_client as c
                Left JOIN crm_client_principal as p ON p.client_id = c.client_id 
                Left JOIN crm_client_schoolenter as s ON s.client_id=c.client_id AND s.school_id = p.school_id
                where c.company_id ='{$request['company_id']}' and p.school_id='{$request['school_id']}' and p.principal_leave = 0 and c.client_isgross = '0' and
                c.client_tracestatus > '-1' and c.client_tracestatus <> 4 and {$princal_where}  ");

//        $inviteCount = $this->DataControl->selectOne("select count( DISTINCT i.client_id) as client_num from crm_client_invite as i where i.school_id ='{$request['school_id']}' and i.invite_isvisit =0    ");
        $princal_where = '1';
        if (isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] !=1) {
            $princal_where .= " and p.marketer_id ='{$request['marketer_id']}' and p.principal_leave =0 ";
        }else{
            $princal_where .= " and p.principal_leave =0 ";
        }
//        $auditionCount = $this->DataControl->selectOne("select count(DISTINCT p.client_id) as client_num from crm_client_principal as p,crm_client as t where p.client_id = t.client_id and  p.school_id = '{$this->school_id}' and {$princal_where} and client_tracestatus<>'4' and p.client_id not in (select d.client_id from crm_client_audition as d where d.school_id =p.school_id and audition_isvisit =1  )");
//        $lossCount = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where  ct.client_id = cs.client_id and ct.client_tracestatus = '-1'and  ct.client_ischaserlapsed ='1' and  cs.school_id='{$request['school_id']}'");
//        $conversionCount = $this->DataControl->selectOne(" select count(q.client_id) as client_num  from (select ct.client_id  from  crm_client_conversionlog as ct where  ct.school_id='{$request['school_id']}' group by ct.client_id) as q ");
//        $principalCount = $this->DataControl->selectOne("select count(q.marketer_id) as marketer_num from (select p.marketer_id from crm_client_principal as p,crm_client as ct,crm_client_schoolenter as cs  where p.client_id = ct.client_id and ct.client_id =cs.client_id and principal_leave = 0 and cs.school_id='{$request['school_id']}' and client_tracestatus <>'4' and client_tracestatus <>'-1' and client_distributionstatus  ='1'  group by p.marketer_id ) as q");
//        $activityCount = $this->DataControl->selectOne("select count(sa.activity_id) as activity_num from crm_sell_activity as sa, crm_sell_activity_school as sas where sa.activity_id =sas.activity_id and sas.school_id ='{$request['school_id']}' and FROM_UNIXTIME(unix_timestamp(sa.activity_starttime),'%Y-%m-%d' ) <='{$todaytime}' and  FROM_UNIXTIME(unix_timestamp(sa.activity_endtime),'%Y-%m-%d') >='{$todaytime}'   ");
        $notrackClient =$this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id=ct.client_id and cs.school_id ='{$request['school_id']}'  and client_tracestatus =0 and client_distributionstatus =1  and ct.company_id='{$request['company_id']}' and exists (select 1 from crm_client_principal as p where p.client_id=ct.client_id and p.school_id = cs.school_id and p.principal_leave =0 ) ");
        //已分配但7日以上未被跟踪过的人数 --根据账户权限显示
        $sevendaytime = strtotime(date("Ymd",strtotime("-6 days")))+24*60*60-1 ;

        if(isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] =='2') {
            $clientarray = $this->DataControl->selectClear(" SELECT t.client_id FROM crm_client_track AS t WHERE t.school_id = '{$this->school_id}' AND t.track_isactive = '1' and t.track_isschooltmk = '1' AND t.track_createtime >= '{$sevendaytime}' ");
        }else{
            $clientarray = $this->DataControl->selectClear(" SELECT t.client_id FROM crm_client_track AS t WHERE t.school_id = '{$this->school_id}' AND t.track_isactive = '1' and t.track_isschooltmk = '0' AND t.track_createtime >= '{$sevendaytime}' ");
        }
        if($clientarray) {
            $clieninarr = array_column($clientarray, 'client_id');
            $clientNotInstr = implode(',', $clieninarr);
            $clientNotInstr = $clientNotInstr ? $clientNotInstr : 0;
        }else{
            $clientNotInstr = 0;
        }
        if(isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] =='1') {
            $seven_notrack_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.client_id) AS client_num FROM crm_client AS c, crm_client_schoolenter AS e
WHERE c.client_id = e.client_id AND e.school_id = '{$this->school_id}' AND c.client_tracestatus IN(0, 1, 2, 3) AND e.is_enterstatus = '1' and c.client_distributionstatus = '1' and c.client_isgross = '0' AND c.client_id NOT IN (  $clientNotInstr )  and not exists(select 1 from crm_client_schoolenter where client_id=c.client_id and school_id<>e.school_id and is_enterstatus = '1' and schoolenter_id>e.schoolenter_id) and EXISTS (SELECT 1 from crm_client_principal WHERE client_id = c.client_id and principal_leave = 0 and school_id = e.school_id)");
        }elseif(isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] =='2'){
            $seven_notrack_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.client_id) AS client_num FROM crm_client AS c, crm_client_schoolenter AS e
WHERE c.client_id = e.client_id AND e.school_id = '{$this->school_id}' AND c.client_tracestatus IN(0, 1, 2, 3) AND e.is_enterstatus = '1' and c.client_distributionstatus = '0' and c.client_schtmkdistributionstatus = '1' and c.client_isgross = '1' AND c.client_id NOT IN ( $clientNotInstr )  and not exists(select 1 from crm_client_schoolenter where client_id=c.client_id and school_id<>e.school_id and is_enterstatus = '1' and schoolenter_id>e.schoolenter_id) and EXISTS (SELECT 1 from crm_client_principal WHERE client_id = c.client_id and principal_leave = 0 and school_id = e.school_id)");
        }else{
            $seven_notrack_num = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.client_id) AS client_num FROM crm_client AS c, crm_client_schoolenter AS e
WHERE c.client_id = e.client_id AND e.school_id = '{$this->school_id}' AND c.client_tracestatus IN(0, 1, 2, 3) AND e.is_enterstatus = '1' and c.client_distributionstatus = '1' and c.client_isgross = '0' and exists (select 1 from crm_client_principal as p where p.client_id=c.client_id and p.school_id = e.school_id and p.principal_leave =0 and p.marketer_id = '{$request['marketer_id']}' ) AND c.client_id NOT IN ( $clientNotInstr )  and not exists(select 1 from crm_client_schoolenter where client_id=c.client_id and school_id<>e.school_id and is_enterstatus = '1' and schoolenter_id>e.schoolenter_id) and EXISTS (SELECT 1 from crm_client_principal WHERE client_id = c.client_id and principal_leave = 0 and school_id = e.school_id)");
        }
        // 未试听的意向客户
//        $no_auditionclientNum = $this->DataControl->selectOne("select count(DISTINCT p.client_id) as client_num from crm_client_principal as p,crm_client as t where p.client_id = t.client_id and  p.school_id = '{$this->school_id}' and p.principal_leave = 0 and client_tracestatus<>'4' and p.client_id not in (select d.client_id from crm_client_audition as d where d.school_id =p.school_id and audition_isvisit =1  ) ");
        // 未柜询的意向客户
//        $no_inviteclientNum = $this->DataControl->selectOne("select count(DISTINCT p.client_id) as client_num from crm_client_principal as p,crm_client as t where p.client_id = t.client_id and  p.school_id = '{$this->school_id}' and {$princal_where} and client_tracestatus<>'4' and p.client_id not in (select d.client_id from crm_client_invite as d where d.school_id =p.school_id and invite_isvisit =1  ) ");
        //待跟踪的客户数
        if (isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] ==1) {
            $notrack_principalNum = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id=ct.client_id and cs.school_id ='{$request['school_id']}'  and ct.client_tracestatus =0 and ct.client_distributionstatus =1  and ct.company_id='{$request['company_id']}' and ct.client_isgross = '0'  and exists (select 1 from crm_client_principal as p where p.client_id=ct.client_id and p.school_id = cs.school_id and p.principal_leave =0 ) ");
        }elseif(isset($request['postbe_crmuserlevel']) && $request['postbe_crmuserlevel'] == 2){
            $notrack_principalNum = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id=ct.client_id and cs.school_id ='{$request['school_id']}'  and ct.client_tracestatus ='0' and ct.client_distributionstatus = '0' and ct.client_schtmkdistributionstatus = '1' and ct.client_isgross = '1'  and ct.company_id='{$request['company_id']}' and exists (select 1 from crm_client_tmkprincipal as p where p.client_id=ct.client_id and p.school_id = cs.school_id and p.tmkprincipal_leave =0 ) ");
        }else{
            $notrack_principalNum = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id=ct.client_id and cs.school_id ='{$request['school_id']}'  and ct.client_tracestatus =0 and ct.client_distributionstatus =1  and ct.company_id='{$request['company_id']}' and ct.client_isgross = '0'  and exists (select 1 from crm_client_principal as p where p.client_id=ct.client_id and p.school_id = cs.school_id and p.principal_leave =0 and p.marketer_id = '{$request['marketer_id']}' ) ");
        }

        //月初  月末
        $monthstart_time = date( 'Y-m-01', time() );
        $mdays = date( 't', time());
        $monthend_time = date( 'Y-m-' . $mdays, time() );
        //30日内累计无意向客户
        $beforeDay = date("Y-m-d",strtotime('-30 days'));
         $beforeDayTime = strtotime($beforeDay);
//        $moth_lossNum = $this->DataControl->selectOne("select count(DISTINCT t.client_id) as client_num from crm_client_track as t,crm_client as ct where  t.client_id = ct.client_id and ct.client_tracestatus  = '-1' and t.school_id='{$this->school_id}' and t.track_followmode ='-2'  and t.track_createtime >= '{$beforeDayTime}'");
        //30日累计转正人数
//        $moth_positiveNum = $this->DataControl->selectOne("select count(DISTINCT g.client_id) as client_num from crm_client_positivelog as g where g.school_id='{$this->school_id}' and g.positivelog_time >='{$monthstart_time}' and  g.positivelog_time <='{$monthend_time}'");

        //累计无效
//        $uneffective_num = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id =ct.client_id  and cs.school_id ='{$this->school_id}' and client_tracestatus ='-2'  ");

        //all_islossNum  待审核无意向名单
        if($companyOne['company_isnointention'] == '1') {
            $all_islossNum = $this->DataControl->selectOne("select count(ct.client_id) as client_num from crm_client as ct,crm_client_schoolenter as cs where cs.client_id =ct.client_id  and cs.school_id ='{$this->school_id}' and ct.client_tracestatus ='-1' and ct.client_ischaserlapsed = '0' and ct.client_isgross = '0'  and ct.client_distributionstatus = 0  and cs.is_enterstatus =1 and cs.company_id='{$request['company_id']}'  ");
        }

        //all_isinvalidNum  待审核无效名单
        if($request['postbe_crmuserlevel'] == '1') {
            $all_isinvalidNum = $this->DataControl->selectOne("  select  count(c.client_id) as client_num from  crm_client as c Left JOIN crm_client_schoolenter as cs ON cs.client_id = c.client_id 
            where 1 and c.client_tracestatus =-2 and c.client_distributionstatus = 0  and cs.school_id='{$request['school_id']}'  and cs.is_enterstatus = 1 and cs.company_id='{$request['company_id']}' and c.client_isinvalidreview = '0'
            and not exists(select 1 from crm_client_schoolenter as h where h.client_id=c.client_id and h.school_id='{$request['school_id']}' and is_enterstatus = '1' and h.is_gmctocrmschool = '1' ) 
            ");
        }

        //moth_auditionNum 本月累计邀约试听名单
//        $moth_auditionNum = $this->DataControl->selectOne("select count(g.client_id) as client_num from crm_client_audition as g where g.school_id = '{$this->school_id}' and DATE_FORMAT(g.audition_visittime,'%Y-%m-%d') >='{$monthstart_time}' and DATE_FORMAT(g.audition_visittime,'%Y-%m-%d') <='{$monthend_time}'");//count(DISTINCT g.client_id)
//        $moth_auditionNumtwo = $this->DataControl->selectOne("select count(g.audition_id) as client_num from crm_student_audition as g where g.school_id = '{$this->school_id}' and DATE_FORMAT(g.audition_visittime,'%Y-%m-%d') >='{$monthstart_time}' and DATE_FORMAT(g.audition_visittime,'%Y-%m-%d') <='{$monthend_time}'");


        if($request['postbe_crmuserlevel'] == '1' || $request['postbe_crmuserlevel'] == '0' ) {
            //获取 跟进逾期名单 和 低频跟进预警名单
            $Model = new \Model\Crm\IntentionClientModel($request);
            $Model->getIntentionNum($request);
            $yuqiyujing = $Model->result['list'];
        }

        $countlist = array();
        $countlist['clientgross_num'] = $clientgrossnumCount['client_num'] + 0;//待筛选毛名单
        $countlist['all_islossNum'] = $all_islossNum['client_num'] + 0;//待审核无意向名单
        $countlist['all_isinvalidNum'] = $all_isinvalidNum['client_num'] + 0;//待审核 无效 名单
//        $countlist['moth_auditionNum'] = $moth_auditionNum['client_num'] + $moth_auditionNumtwo['client_num'] + 0;//本月累计邀约试听名单
        $countlist['client_num'] = $clientnumCount['client_num'] + 0;//待筛选有效名单
        $countlist['intention_num'] = $intentionCount['client_num'] + 0;
        $countlist['notrack_num'] = $notrackClient['client_num'] + 0;
        $countlist['seven_notrack_num'] = $seven_notrack_num['client_num'] + 0;
        $countlist['notrack_principalNum'] = $notrack_principalNum['client_num'] + 0;
//        $countlist['moth_positiveNum'] = $moth_positiveNum['client_num'] + 0;
        $countlist['yuqi'] = $yuqiyujing['yuqi'] + 0;
        $countlist['yujing'] = $yuqiyujing['yujing'] + 0;

//        $countlist['invite_num'] = $inviteCount['client_num'] + 0;
//        $countlist['audition_num'] = $auditionCount['client_num'] + 0;
//        $countlist['loss_num'] = $lossCount['client_num'] + 0;
//        $countlist['conversion_num'] = $conversionCount['client_num'] + 0;
//        $countlist['marketer_num'] = $principalCount['marketer_num'] + 0;
//        $countlist['activity_num'] = $activityCount['activity_num'] + 0;
//        $countlist['no_auditionclientNum'] = $no_auditionclientNum['client_num'] + 0;
//        $countlist['no_inviteclientNum'] = $no_inviteclientNum['client_num'] + 0;
//        $countlist['moth_lossNum'] = $moth_lossNum['client_num'] + 0;
//        $countlist['uneffective_num'] = $uneffective_num['client_num'] + 0;

        $countlist['companyOne'] = $companyOne;
        return $countlist;
    }

    public function __call($method, $args) {

    }
}
