<?php


namespace Model\Smc;

class TransactionModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        }

        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }

    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    //出班
    function outClass($student_id, $class_id, $from = 0, $time = '', $trading_pid = '')
    {
        if ($time == '') {
            $time = time();
        }
        $stu_classOne = $this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id='{$student_id}' and class_id='{$class_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and study_isreading='1'");
        if ($stu_classOne) {
            $data = array();
            $data['study_isreading'] = -1;
            $data['study_endday'] = date("Y-m-d", $time);
            $data['study_updatetime'] = time();
            if ($this->DataControl->updateData("smc_student_study", "student_id='{$student_id}' and class_id='{$class_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'", $data)) {
                $studyOne = $this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id='{$student_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}' and study_isreading=1");
                if (!$studyOne) {
                    $enrolled_data = array();
                    $enrolled_data['enrolled_status'] = 0;
                    $enrolled_data['enrolled_updatatime'] = $time;
                    $this->DataControl->updateData("smc_student_enrolled", "student_id='{$student_id}' and school_id='{$this->school_id}'", $enrolled_data);
                }

                if ($from != 2) {
                    if ($from == '3') {
                        $code = 'B07';
                        $change_reason = $this->LgStringSwitch('子班转出');
                    } elseif ($from == '4') {
                        $code = 'B01';
                        $change_reason = $this->LgStringSwitch('取消订单离开班级');
                    }elseif ($from == '5') {
                        $code = 'B01';
                        $change_reason = $this->LgStringSwitch('班级流失');
                    } else {
                        $code = 'B07';
                        $change_reason = $this->LgStringSwitch('结转转出');
                    }

                    $like = date("Ymd", $time);
                    $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
                    $data = array();
                    $data['company_id'] = $this->company_id;
                    $data['student_id'] = $student_id;
                    if ($changeInfo) {
                        $data['change_pid'] = $changeInfo['change_pid'] + 1;
                    } else {
                        $data['change_pid'] = $like . '000001';
                    }
                    $data['from_stuchange_code'] = $code;
                    $data['from_school_id'] = $this->school_id;
                    $data['from_class_id'] = $class_id;
                    $data['change_status'] = 1;
                    $data['change_day'] = date("Y-m-d", $time);
                    $data['change_reason'] = $change_reason;
                    $data['change_workername'] = $this->stafferOne['staffer_cnname'];
                    $data['change_createtime'] = time();
                    $this->DataControl->insertData("smc_student_change", $data);

                    $log_data = array();
                    $log_data['change_pid'] = $data['change_pid'];
                    $log_data['trading_pid'] = $trading_pid;
                    $log_data['company_id'] = $this->company_id;
                    $log_data['student_id'] = $student_id;
                    $log_data['changelog_type'] = 0;
                    $log_data['stuchange_code'] = $code;
                    $log_data['school_id'] = $this->school_id;
                    $log_data['class_id'] = $class_id;
                    $log_data['changelog_note'] = $change_reason;
                    $log_data['changelog_day'] = date("Y-m-d", $time);
                    $log_data['staffer_id'] = $this->stafferOne['staffer_id'];;
                    $log_data['changelog_createtime'] = time();
                    $this->DataControl->insertData("smc_student_changelog", $log_data);
                }

                $sql = "select c.class_id
                  from smc_class as c
                  left join smc_student_study as ss on ss.class_id=c.class_id
                  where c.father_id='{$class_id}' and ss.student_id='{$student_id}' and ss.study_isreading='1' and ss.school_id='{$this->school_id}'";
                $classList = $this->DataControl->selectClear($sql);
                if ($classList) {
                    foreach ($classList as $classOne) {
                        $this->outClass($student_id, $classOne['class_id'], '3', $time);
                    }
                }

                $sql = "select sf.coursetimes_id from smc_student_free_coursetimes as sf where sf.class_id='{$class_id}' and sf.student_id='{$student_id}' and sf.school_id='{$this->school_id}' and sf.is_use='0'";
                $freeTimes = $this->DataControl->selectClear($sql);
                if ($freeTimes) {

                    $data = array();
                    $data['is_use'] = '-1';
                    $this->DataControl->updateData("smc_student_free_coursetimes", "class_id='{$class_id}' and student_id='{$student_id}' and school_id='{$this->school_id}' and is_use='0'", $data);

                    $num = count($freeTimes);

                    $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);

                    $BalanceModel->reduceStuTimes($student_id, $class_id, $num, $this->LgStringSwitch('离班扣除免费课时次数'), $time);

                }

                $absencewhere = " ch.class_id='{$class_id}'  and sa.student_id='{$student_id}' and sah.absence_hour_status <> '-1' ";
                $absenceList = $this->DataControl->selectClear("select sah.absence_hour_id,sa.absence_id from smc_student_absence as sa,smc_student_absence_hour as sah,smc_class_hour as ch where sa.absence_id =sah.absence_id and sah.hour_id=ch.hour_id and {$absencewhere}");
                if ($absenceList) {
                    foreach ($absenceList as $absenceOne) {
                        $data = array();
                        $data['absence_hour_status'] = '-1';
                        $this->DataControl->updateData("smc_student_absence_hour", "absence_hour_id='{$absenceOne['absence_hour_id']}'", $data);
                        $track = array();
                        $track['absence_id'] = $absenceOne['absence_id'];
                        $track['track_status'] = '-2';
                        $track['track_title'] = '取消请假';
                        $track['track_applynote'] = '出班取消请假';
                        $track['track_applytime'] = time();
                        $this->DataControl->insertData("smc_student_absence_track", $track);
                    }
                }
                $this->error = true;
                $this->oktip = "出班成功";
                return true;
            } else {
                $this->error = true;
                $this->errortip = "出班失败";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "学员不在该班级";
            return false;
        }

    }

    //跨校
    function CrossSchool($student_id, $school_id, $reason_code = '', $reason = '', $from_school = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        if ($from_school != '') {
            $this->school_id = $from_school;
        }

        $data = array();
        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['to_stuchange_code'] = 'F01';
        $data['to_school_id'] = $school_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 1;
        $log_data['stuchange_code'] = 'F01';
        $log_data['school_id'] = $school_id;
        $log_data['changelog_note'] = $reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        if ($this->DataControl->getFieldOne("smc_student_enrolled", "school_id", "school_id='{$school_id}' and student_id='{$student_id}'")) {
            $en_data = array();
            $en_data['enrolled_status'] = 0;
            $en_data['enrolled_updatatime'] = $time;
            $this->DataControl->updateData("smc_student_enrolled", "school_id='{$school_id}' and student_id='{$student_id}'", $en_data);
        } else {
            $en_data = array();
            $en_data['school_id'] = $school_id;
            $en_data['student_id'] = $student_id;
            $en_data['enrolled_createtime'] = $time;
            $en_data['enrolled_status'] = 0;
            $this->DataControl->insertData("smc_student_enrolled", $en_data);
        }

        /*$schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$school_id}'");

        if (!$this->DataControl->getFieldOne("smc_student_balance", "student_id", "student_id = '{$student_id}' and school_id = '{$school_id}'")) {
            $balance = array();
            $balance['company_id'] = $this->company_id;
            $balance['companies_id'] = $schoolOne['companies_id'];
            $balance['student_id'] = $student_id;
            $balance['school_id'] = $school_id;
            $this->DataControl->insertData("smc_student_balance", $balance);
        }*/

        return true;
    }

    function stuTrading($student_id, $school_id, $code = "",$companies_id, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        do {
            $trading_pid = $this->createOrderPid('JY');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));


        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $school_id;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = $code;
        $data['trading_status'] = "1";
        $data['trading_createtime'] = $time;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        if ($this->DataControl->insertData("smc_student_trading", $data)) {
            return $trading_pid;
        } else {
            return false;
        }
    }

    function TransferToSchool($student_id, $from_school_id,$school_id, $Trading_pid, $reason_code, $reason, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = 'B05';
        $data['from_school_id'] = $from_school_id;
        $data['to_stuchange_code'] = 'A06';
        $data['to_school_id'] = $school_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = 'B05';
        $log_data['trading_pid'] = $Trading_pid;
        $log_data['school_id'] = $from_school_id;
        $log_data['changelog_note'] = $reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 1;
        $log_data['stuchange_code'] = 'A06';
        $log_data['trading_pid'] = $Trading_pid;
        $log_data['school_id'] = $school_id;
        $log_data['changelog_note'] = $reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        $out_data = array();
        $out_data['enrolled_status'] = -1;
        $out_data['enrolled_leavetime'] = $time;
        $this->DataControl->updateData("smc_student_enrolled", "student_id='{$student_id}' and school_id='{$from_school_id}'", $out_data);

        if ($this->DataControl->getFieldOne("smc_student_enrolled", "school_id", "student_id='{$student_id}' and school_id='{$school_id}'")) {
            $en_data = array();
            $en_data['enrolled_updatatime'] = $time;
            $en_data['enrolled_status'] = 0;
            $this->DataControl->updateData("smc_student_enrolled", "student_id='{$student_id}' and school_id='{$school_id}'", $en_data);
        } else {
            $en_data = array();
            $en_data['school_id'] = $school_id;
            $en_data['student_id'] = $student_id;
            $en_data['enrolled_createtime'] = $time;
            $en_data['enrolled_status'] = 0;
            $this->DataControl->insertData("smc_student_enrolled", $en_data);
        }

        /*if (!$this->DataControl->getFieldOne("smc_student_balance", "student_id", "student_id = '{$student_id}' and school_id = '{$school_id}'")) {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$school_id}'");

            $balance = array();
            $balance['company_id'] = $this->company_id;
            $balance['companies_id'] = $schoolOne['companies_id'];
            $balance['student_id'] = $student_id;
            $balance['school_id'] = $school_id;
            $this->DataControl->insertData("smc_student_balance", $balance);
        }*/
    }

    //转校
//    function TransferSchool($student_id, $school_id, $reason_code = '', $reason = '', $from_school = '', $time = '')
//    {
//        if ($time == '') {
//            $time = time();
//        }
//        if ($from_school != '') {
//            $this->school_id = $from_school;
//        }
//
//        $oldStuBalacne = $this->DataControl->getFieldOne("smc_student_balance", "student_balance,student_withholdbalance", "school_id='{$this->school_id}' and student_id='{$student_id}'");
//
//        $stuBalace = $this->DataControl->getFieldOne("smc_student_balance", "student_id,student_balance,student_withholdbalance", "student_id = '{$student_id}' and school_id = '{$school_id}'");
//        if (!$stuBalace) {
//            $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$school_id}'");
//            $balance = array();
//            $balance['company_id'] = $this->company_id;
//            $balance['companies_id'] = $schoolOne['companies_id'];
//            $balance['student_id'] = $student_id;
//            $balance['school_id'] = $school_id;
//            $balance['student_balance'] = $oldStuBalacne['student_balance'];
//            $balance['student_withholdbalance'] = $oldStuBalacne['student_withholdbalance'];
//            $this->DataControl->insertData("smc_student_balance", $balance);
//        } else {
//            $balance = array();
//            $balance['student_balance'] = $oldStuBalacne['student_balance'] + $stuBalace['student_balance'];
//            $balance['student_withholdbalance'] = $oldStuBalacne['student_withholdbalance'] + $stuBalace['student_withholdbalance'];
//            $this->DataControl->updateData("smc_student_balance", "school_id='{$school_id}' and student_id='{$student_id}'", $balance);
//        }
//        $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id = '{$school_id}' and company_id='{$this->company_id}'");
//        $oldSchoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id = '{$this->school_id}' and company_id='{$this->company_id}'");
//
//        if ($oldStuBalacne['student_balance'] > 0 || $oldStuBalacne['student_withholdbalance'] > 0) {
//            $oldTrading_pid = $this->stuTrading($student_id, $this->school_id, 'TransferOut', $time);
//            $Trading_pid = $this->stuTrading($student_id, $school_id, 'TransferIn', $time);
//        } else {
//            $oldTrading_pid = '';
//            $Trading_pid = '';
//        }
//        if ($balance['student_balance'] > 0 || $oldStuBalacne['student_withholdbalance'] > 0) {
//            $this->schoolTracks($student_id, $school_id, $oldTrading_pid, $Trading_pid, '2', '0', $balance['student_balance'], $oldStuBalacne['student_withholdbalance'], $reason, $time);
//        }
//
//        $data = array();
//        $like = date("Ymd", $time);
//        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
//        $data['company_id'] = $this->company_id;
//        $data['student_id'] = $student_id;
//        if ($changeInfo) {
//            $data['change_pid'] = $changeInfo['change_pid'] + 1;
//        } else {
//            $data['change_pid'] = $like . '000001';
//        }
//        $data['from_stuchange_code'] = 'B05';
//        $data['from_school_id'] = $this->school_id;
//        $data['to_stuchange_code'] = 'A06';
//        $data['to_school_id'] = $school_id;
//        $data['change_status'] = 1;
//        $data['change_day'] = date("Y-m-d", $time);
//        $data['reason_code'] = $reason_code;
//        $data['change_reason'] = $reason;
//        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
//        $data['change_createtime'] = time();
//        $this->DataControl->insertData("smc_student_change", $data);
//
//        $log_data = array();
//        $log_data['change_pid'] = $data['change_pid'];
//        $log_data['company_id'] = $this->company_id;
//        $log_data['student_id'] = $student_id;
//        $log_data['changelog_type'] = 0;
//        $log_data['stuchange_code'] = 'B05';
//        $log_data['trading_pid'] = $oldTrading_pid;
//        $log_data['school_id'] = $this->school_id;
//        $log_data['changelog_note'] = $reason;
//        $log_data['changelog_day'] = date("Y-m-d", $time);
//        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
//        $log_data['changelog_createtime'] = time();
//        $this->DataControl->insertData("smc_student_changelog", $log_data);
//
//        $log_data = array();
//        $log_data['change_pid'] = $data['change_pid'];
//        $log_data['company_id'] = $this->company_id;
//        $log_data['student_id'] = $student_id;
//        $log_data['changelog_type'] = 1;
//        $log_data['stuchange_code'] = 'A06';
//        $log_data['trading_pid'] = $Trading_pid;
//        $log_data['school_id'] = $school_id;
//        $log_data['changelog_note'] = $reason;
//        $log_data['changelog_day'] = date("Y-m-d", $time);
//        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
//        $log_data['changelog_createtime'] = time();
//        $this->DataControl->insertData("smc_student_changelog", $log_data);
//
//        $out_data = array();
//        $out_data['enrolled_status'] = -1;
//        $out_data['enrolled_leavetime'] = $time;
//        $this->DataControl->updateData("smc_student_enrolled", "student_id='{$student_id}' and school_id='{$this->school_id}'", $out_data);
//
//        if ($this->DataControl->getFieldOne("smc_student_enrolled", "school_id", "student_id='{$student_id}' and school_id='{$school_id}'")) {
//            $en_data = array();
//            $en_data['enrolled_updatatime'] = $time;
//            $en_data['enrolled_status'] = 0;
//            $this->DataControl->updateData("smc_student_enrolled", "student_id='{$student_id}' and school_id='{$school_id}'", $en_data);
//        } else {
//            $en_data = array();
//            $en_data['school_id'] = $school_id;
//            $en_data['student_id'] = $student_id;
//            $en_data['enrolled_createtime'] = $time;
//            $en_data['enrolled_status'] = 0;
//            $this->DataControl->insertData("smc_student_enrolled", $en_data);
//        }
//
//
//        if ($oldStuBalacne['student_balance'] > 0) {
//            $balancelog_data = array();
//            $balancelog_data['company_id'] = $this->company_id;
//            $balancelog_data['companies_id'] = $schoolOne['companies_id'];
//            $balancelog_data['school_id'] = $this->school_id;
//            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
//            $balancelog_data['student_id'] = $student_id;
//            $balancelog_data['trading_pid'] = $oldTrading_pid;
//            $balancelog_data['balancelog_class'] = 0;
//            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('资产转出');
//            $balancelog_data['balancelog_playclass'] = '-';
//            $balancelog_data['balancelog_fromamount'] = $oldStuBalacne['student_balance'];
//            $balancelog_data['balancelog_playamount'] = $oldStuBalacne['student_balance'];
//            $balancelog_data['balancelog_finalamount'] = 0;
//            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('转校转出账户余额');
//            $balancelog_data['balancelog_time'] = $time;
//            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
//
//            $balancelog_data = array();
//            $balancelog_data['company_id'] = $this->company_id;
//            $balancelog_data['companies_id'] = $schoolOne['companies_id'];
//            $balancelog_data['school_id'] = $school_id;
//            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
//            $balancelog_data['student_id'] = $student_id;
//            $balancelog_data['trading_pid'] = $Trading_pid;
//            $balancelog_data['balancelog_class'] = 0;
//            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('资产转入');
//            $balancelog_data['balancelog_playclass'] = '+';
//            $balancelog_data['balancelog_fromamount'] = $stuBalace['student_balance'];
//            $balancelog_data['balancelog_playamount'] = $oldStuBalacne['student_balance'];
//            $balancelog_data['balancelog_finalamount'] = $stuBalace['student_balance'] + $oldStuBalacne['student_balance'];
//            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('学校余额转账户余额');
//            $balancelog_data['balancelog_time'] = $time;
//            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
//
//            $oldbalance = array();
//            $oldbalance['student_balance'] = 0;
//            $this->DataControl->updateData("smc_student_balance", "school_id='{$this->school_id}' and student_id='{$student_id}'", $oldbalance);
//        }
//
//        if ($oldStuBalacne['student_withholdbalance'] > 0) {
//            $balancelog_data = array();
//            $balancelog_data['company_id'] = $this->company_id;
//            $balancelog_data['school_id'] = $this->school_id;
//            $balancelog_data['companies_id'] = $oldSchoolOne['companies_id'];
//            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
//            $balancelog_data['student_id'] = $student_id;
//            $balancelog_data['trading_pid'] = $oldTrading_pid;
//            $balancelog_data['balancelog_class'] = 2;
//            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('资产转出');
//            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('转入新学校');
//            $balancelog_data['balancelog_playclass'] = '-';
//            $balancelog_data['balancelog_fromamount'] = $oldStuBalacne['student_withholdbalance'];
//            $balancelog_data['balancelog_playamount'] = $oldStuBalacne['student_withholdbalance'];
//            $balancelog_data['balancelog_finalamount'] = 0;
//            $balancelog_data['balancelog_time'] = $time;
//            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
//
//            $balancelog_data = array();
//            $balancelog_data['company_id'] = $this->company_id;
//            $balancelog_data['school_id'] = $school_id;
//            $balancelog_data['companies_id'] = $schoolOne['companies_id'];
//            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
//            $balancelog_data['student_id'] = $student_id;
//            $balancelog_data['trading_pid'] = $Trading_pid;
//            $balancelog_data['balancelog_class'] = 2;
//            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('资产转入');
//            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('老学校转入');
//            $balancelog_data['balancelog_playclass'] = '+';
//            $balancelog_data['balancelog_fromamount'] = $stuBalace['student_withholdbalance'];
//            $balancelog_data['balancelog_playamount'] = $oldStuBalacne['student_withholdbalance'];
//            $balancelog_data['balancelog_finalamount'] = $stuBalace['student_withholdbalance'] + $oldStuBalacne['student_withholdbalance'];
//            $balancelog_data['balancelog_time'] = $time;
//            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);
//
//            $data = array();
//            $data['student_withholdbalance'] = 0;
//            $this->DataControl->updateData("smc_student_balance", "student_id='{$student_id}' and school_id = '{$this->school_id}' and company_id='{$this->company_id}'", $data);
//        }
//
//        return true;
//    }


//    function schoolTracks($student_id, $to_school_id = 0, $frompid = '', $topid = '', $status = 0, $trading_type = 0, $balance = 0, $withholdbalance = 0, $note = "", $time = '')
//    {
//        if ($time == '') {
//            $time = time();
//        }
//        $tradData = array();
//        do {
//            $trading_pid = $this->createOrderPid('ST');
//        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
//        $from_companies_id = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$this->school_id}' and company_id='{$this->company_id}'");
//        $to_companies_id = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$to_school_id}' and company_id='{$this->company_id}'");
//
//        $price = $balance + $withholdbalance;
//
//        $tradData['company_id'] = $this->company_id;
//        $tradData['trading_pid'] = $trading_pid;
//        $tradData['trading_frompid'] = $frompid;
//        $tradData['trading_topid'] = $topid;
//        $tradData['from_companies_id'] = $from_companies_id['companies_id'];
//        $tradData['from_school_id'] = $this->school_id;
//        $tradData['to_companies_id'] = $to_companies_id['companies_id'];
//        $tradData['to_school_id'] = $to_school_id;
//        $tradData['trading_type'] = $trading_type;
//        $tradData['student_id'] = $student_id;
//        $tradData['trading_price'] = $price;
//        $tradData['trading_balance'] = $balance;
//        $tradData['trading_withholdbalance'] = $withholdbalance;
//        $tradData['trading_status'] = $status;
//        $tradData['trading_note'] = $note;
//        $tradData['trading_createtime'] = $time;
//        $this->DataControl->insertData("smc_school_trading", $tradData);
//
//        $data = array();
//        $data['trading_pid'] = $trading_pid;
//        $data['tracks_title'] = $note;
//        $data['tracks_information'] = $note;
//        $data['staffer_id'] = $this->stafferOne['staffer_id'];
//        $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
//        $data['tracks_time'] = $time;
//        $this->DataControl->insertData("smc_school_trading_tracks", $data);
//
//        return $trading_pid;
//    }

    function schoolTrade($student_id, $from_school_id = 0, $to_school_id = 0, $frompid = '', $topid = '', $status = 0, $trading_type = 0, $balance = 0, $withholdbalance = 0,$from_companies_id,$to_companies_id,$title = '', $info = '', $note = "", $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $tradData = array();
        do {
            $trading_pid = $this->createOrderPid('ST');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $price = $balance + $withholdbalance;

        $tradData['company_id'] = $this->company_id;
        $tradData['trading_pid'] = $trading_pid;
        $tradData['trading_frompid'] = $frompid;
        $tradData['trading_topid'] = $topid;
        $tradData['from_companies_id'] = $from_companies_id;
        $tradData['from_school_id'] = $from_school_id;
        $tradData['to_companies_id'] = $to_companies_id;
        $tradData['to_school_id'] = $to_school_id;
        $tradData['trading_type'] = $trading_type;
        $tradData['student_id'] = $student_id;
        $tradData['trading_price'] = $price;
        $tradData['trading_balance'] = $balance;
        $tradData['trading_withholdbalance'] = $withholdbalance;
        $tradData['trading_status'] = $status;
        $tradData['trading_note'] = $note;
        $tradData['trading_createtime'] = $time;
        $this->DataControl->insertData("smc_school_trading", $tradData);

        $data = array();
        $data['trading_pid'] = $trading_pid;
        $data['tracks_title'] = $title;
        $data['tracks_information'] = $info;
        $data['tracks_note'] = $note;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $data['tracks_time'] = time();
        $this->DataControl->insertData("smc_school_trading_tracks", $data);

        return $trading_pid;
    }


    //延班
    function waitClass($student_id, $class_id, $staffer_id, $reason_code = '', $reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $this->outClass($student_id, $class_id, 2, $time);

        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_id DESC limit 0,1");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$staffer_id}' and company_id='{$this->company_id}'");

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = 'A07';
        $data['from_school_id'] = $this->school_id;
        $data['from_class_id'] = $class_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $this->LgStringSwitch('延班');
        $data['change_workername'] = $stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = 'A07';
        $log_data['school_id'] = $this->school_id;
        $log_data['class_id'] = $class_id;
        $log_data['changelog_note'] = $this->LgStringSwitch('延班');
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $staffer_id;
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$class_id}'");
        $balance_data = array();
        $balance_data['coursebalance_status'] = 2;
        $balance_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$classOne['course_id']}' and school_id='{$this->school_id}'", $balance_data);

        $this->error = true;
        $this->oktip = "延班成功";
        return true;
    }

    //转班
    function transferClass($student_id, $class_id, $to_class_id, $reason_code = '', $change_reason = '', $from = 0, $hour_day = '', $fromtrading_pid = '', $totrading_pid = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "school_id", "class_id='{$class_id}'");
        $this->school_id = $classOne['school_id'];

        $toclassOne = $this->DataControl->getFieldOne("smc_class", "class_enddate,course_id", "class_id='{$to_class_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'");

        if (!$toclassOne) {
            return false;
        }

        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }

        if ($from == 0) {
            $from_code = 'B04';
            $to_code = 'A05';
        } else {
            $from_code = 'B03';
            $to_code = 'A04';
        }

        $data['from_stuchange_code'] = $from_code;
        $data['from_school_id'] = $this->school_id;
        $data['from_class_id'] = $class_id;
        $data['to_stuchange_code'] = $to_code;
        $data['to_school_id'] = $this->school_id;
        $data['to_class_id'] = $to_class_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $change_reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = $from_code;
        $log_data['trading_pid'] = $fromtrading_pid;
        $log_data['school_id'] = $this->school_id;
        $log_data['class_id'] = $class_id;
        $log_data['changelog_note'] = $change_reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        $to_log_data = array();
        $to_log_data['change_pid'] = $data['change_pid'];
        $to_log_data['company_id'] = $this->company_id;
        $to_log_data['student_id'] = $student_id;
        $to_log_data['changelog_type'] = 1;
        $to_log_data['stuchange_code'] = $to_code;
        $to_log_data['trading_pid'] = $totrading_pid;
        $to_log_data['school_id'] = $this->school_id;
        $to_log_data['class_id'] = $to_class_id;
        $to_log_data['changelog_note'] = $change_reason;
        $to_log_data['changelog_day'] = date("Y-m-d", $time);
        $to_log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $to_log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $to_log_data);

        if ($hour_day != '') {
            $study_beginday = $hour_day;
        } else {
            $study_beginday = date("Y-m-d", $time);
        }

        $this->entryClass($student_id, '', $to_class_id, $study_beginday, 2, $time);

//		if($this->DataControl->getFieldOne("smc_student_study","study_id","school_id='{$this->school_id}' and class_id='{$to_class_id}' and student_id='{$student_id}'")){
//			$this->DataControl->updateData("smc_student_study","school_id='{$this->school_id}' and class_id='{$to_class_id}' and student_id='{$student_id}'", $to_study_data);
//		}else{
//			$to_study_data['company_id'] = $this->company_id;
//			$to_study_data['school_id'] = $this->school_id;
//			$to_study_data['student_id'] = $student_id;
//			$to_study_data['class_id'] = $to_class_id;
//			$this->DataControl->insertData("smc_student_study", $to_study_data);
//		}

    }

    //升班
    function promotionClass($student_id, $class_id, $to_class_id, $reason_code = '', $change_reason = '', $fromtrading_pid = '', $totrading_pid = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = 'B02';
        $data['from_school_id'] = $this->school_id;
        $data['from_class_id'] = $class_id;
        $data['to_stuchange_code'] = 'A03';
        $data['to_school_id'] = $this->school_id;
        $data['to_class_id'] = $to_class_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $change_reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = 'B02';
        $log_data['trading_pid'] = $fromtrading_pid;
        $log_data['school_id'] = $this->school_id;
        $log_data['class_id'] = $class_id;
        $log_data['changelog_note'] = $change_reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        $to_log_data = array();
        $to_log_data['change_pid'] = $data['change_pid'];
        $to_log_data['company_id'] = $this->company_id;
        $to_log_data['student_id'] = $student_id;
        $to_log_data['changelog_type'] = 1;
        $to_log_data['stuchange_code'] = 'A03';
        $to_log_data['trading_pid'] = $totrading_pid;
        $to_log_data['school_id'] = $this->school_id;
        $to_log_data['class_id'] = $to_class_id;
        $to_log_data['changelog_note'] = $change_reason;
        $to_log_data['changelog_day'] = date("Y-m-d", $time);
        $to_log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $to_log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $to_log_data);

//		$toclassOne = $this->DataControl->getFieldOne("smc_class", "class_enddate,course_id", "class_id='{$to_class_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'");

        $this->entryClass($student_id, '', $to_class_id, date("Y-m-d", $time), 2, $time);


//		$to_study_data = array();
//		$to_study_data['company_id'] = $this->company_id;
//		$to_study_data['school_id'] = $this->school_id;
//		$to_study_data['student_id'] = $student_id;
//		$to_study_data['study_isreading'] = 1;
//		$to_study_data['class_id'] = $to_class_id;
//		$to_study_data['study_beginday'] = date("Y-m-d",$time);
//		$to_study_data['study_endday'] = $toclassOne['class_enddate'];
//		$this->DataControl->insertData("smc_student_study", $to_study_data);

        $sql = "select sh.hourstudy_id from smc_student_hourstudy as sh
			  left join smc_class_hour as ch on ch.hour_id=sh.hour_id
			  where sh.hourstudy_id=(select sh.hourstudy_id from smc_student_hourstudy as sh where sh.student_id='{$student_id}' and sh.hour_id= (select ch.hour_id from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_ischecking>='0' order by ch.hour_lessontimes desc limit 0,1) limit 0,1) and ch.hour_lessontimes<='5'";
        $hourStudyOne = $this->DataControl->selectOne($sql);

        $sql = "select ht.staffer_id from smc_class_hour_teaching as ht where ht.class_id='{$class_id}' and ht.teaching_type='0' and ht.teaching_isdel='0' group by ht.staffer_id";
        $teacherList = $this->DataControl->selectClear($sql);
        if ($teacherList) {
            foreach ($teacherList as $val) {
                if ($val['staffer_id']) {
                    $data = array();

                    if ($hourStudyOne) {
                        $data['upgradelog_class'] = 1;
                    } else {
                        $data['upgradelog_class'] = 0;
                    }

                    $data['company_id'] = $this->company_id;
                    $data['school_id'] = $this->school_id;
                    $data['student_id'] = $student_id;
                    $data['from_class_id'] = $class_id;
                    $data['to_class_id'] = $to_class_id;
                    $data['staffer_id'] = $val['staffer_id'];
                    $data['upgradelog_status'] = 1;
                    $data['upgradelog_activatetime'] = $time;
                    $data['upgradelog_createtime'] = $time;
                    $this->DataControl->insertData("smc_class_upgradelog", $data);
                }
            }
        }
    }

    function upgradeInfo($class_id, $to_class_id, $stuList, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$class_id}'");
        $to_classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$to_class_id}'");
        $up_data = array();
        $up_data['company_id'] = $this->company_id;
        $up_data['school_id'] = $this->school_id;
        $up_data['from_class_id'] = $class_id;
        $up_data['from_course_id'] = $classOne['course_id'];
        $up_data['to_class_id'] = $to_class_id;
        $up_data['to_course_id'] = $to_classOne['course_id'];
        $up_data['upgradeorder_status'] = '1';
        $up_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $up_data['upgradeorder_workername'] = $this->stafferOne['staffer_cnname'];
        $up_data['upgradeorder_createtime'] = $time;

        $sql = "select sh.hourstudy_id
			  from smc_student_hourstudy as sh
			  left join smc_class_hour as ch on ch.hour_id=sh.hour_id
			  where sh.class_id='{$class_id}' and ch.hour_lessontimes<='5'
			  and sh.student_id in (select sh.student_id from smc_student_hourstudy as sh where sh.class_id='{$class_id}' and sh.hour_id = (select ch.hour_id from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_ischecking>='0' order by ch.hour_lessontimes desc limit 0,1))
			  group by sh.student_id
			  ";
        $effectStuList = $this->DataControl->selectClear($sql);

        $list = array();
        foreach ($stuList as $val) {
            $list[] = $val['student_id'];
        }
        $stuStr = implode(',', $list);
        $sql = "select sh.hourstudy_id
			  from smc_student_hourstudy as sh
			  left join smc_class_hour as ch on ch.hour_id=sh.hour_id
			  where sh.class_id='{$class_id}' and ch.hour_lessontimes<='5'
			  and sh.student_id in (select sh.student_id from smc_student_hourstudy as sh where sh.class_id='{$class_id}' and sh.hour_id = (select ch.hour_id from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_ischecking>='0' order by ch.hour_lessontimes desc limit 0,1)) and sh.student_id in ($stuStr)
			  group by sh.student_id
			  ";
        $upStuList = $this->DataControl->selectClear($sql);

        $sql = "select sh.hourstudy_id
			  from smc_student_hourstudy as sh
			  left join smc_class_hour as ch on ch.hour_id=sh.hour_id
			  where sh.class_id='{$class_id}' and ch.hour_lessontimes>'5'
			  and sh.student_id in (select sh.student_id from smc_student_hourstudy as sh where sh.class_id='{$class_id}' and sh.hour_id = (select ch.hour_id from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_ischecking>='0' order by ch.hour_lessontimes desc limit 0,1))
			  group by sh.student_id
			  ";
        $leaveStuList = $this->DataControl->selectClear($sql);

        $sql = "select sh.hourstudy_id
			  from smc_student_hourstudy as sh
			  left join smc_class_hour as ch on ch.hour_id=sh.hour_id
			  where sh.class_id='{$class_id}' and ch.hour_lessontimes>'5'
			  and sh.student_id in (select sh.student_id from smc_student_hourstudy as sh where sh.class_id='{$class_id}' and sh.hour_id = (select ch.hour_id from smc_class_hour as ch where ch.class_id='{$class_id}' and ch.hour_ischecking>='0' order by ch.hour_lessontimes desc limit 0,1)) and sh.student_id in ($stuStr)
			  group by sh.student_id
			  ";
        $upLeaveStuList = $this->DataControl->selectClear($sql);

        $up_data['upgrade_stayeffective'] = $effectStuList ? count($effectStuList) : 0;
        $up_data['upgrade_staynums'] = $upStuList ? count($upStuList) : 0;
        $up_data['upgrade_intonums'] = $leaveStuList ? count($leaveStuList) : 0;
        $up_data['upgrade_intostaynums'] = $upLeaveStuList ? count($upLeaveStuList) : 0;
        $this->DataControl->insertData("smc_class_upgradeorder", $up_data);
    }


    //流失复读
    function backToSchool($student_id, $reason_code = '', $change_reason = '', $from_school = '')
    {
        if ($from_school != '') {
            $this->school_id = $from_school;
        }
        $time = time();
        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['to_stuchange_code'] = 'D02';
        $data['to_school_id'] = $this->school_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $change_reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $to_log_data = array();
        $to_log_data['change_pid'] = $data['change_pid'];
        $to_log_data['company_id'] = $this->company_id;
        $to_log_data['student_id'] = $student_id;
        $to_log_data['changelog_type'] = 1;
        $to_log_data['stuchange_code'] = 'D02';
        $to_log_data['school_id'] = $this->school_id;
        $to_log_data['changelog_note'] = $change_reason;
        $to_log_data['changelog_day'] = date("Y-m-d", $time);
        $to_log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $to_log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $to_log_data);
    }

    //班级流失
    function classLoss($student_id, $class_id, $reason_code = '', $change_reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = 'B01';
        $data['from_school_id'] = $this->school_id;
        $data['from_class_id'] = $class_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $change_reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = 'B01';
        $log_data['school_id'] = $this->school_id;
        $log_data['class_id'] = $class_id;
        $log_data['changelog_note'] = $change_reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['class_id'] = $class_id;
        $data['student_id'] = $student_id;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['lapsedlog_note'] = $change_reason;
        $data['lapsedlog_createtime'] = $time;
        $this->DataControl->insertData("smc_class_lapsedlog", $data);

        $classOne = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$class_id}'");
        $data = array();
        $data['coursebalance_status'] = '4';
        $data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and school_id='{$this->school_id}' and course_id='{$classOne['course_id']}'", $data);

    }


    //编班
    function entryClass($student_id, $course_id, $class_id, $enterclassdate = '', $from = 0, $time = '', $reason_code = '', $change_reason = '编班', $limit = 0, $staffer_id = 0)
    {
        if ($time == '') {
            $time = time();
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_enddate,course_id,class_branch,class_fullnums", "class_id='{$class_id}' and company_id='{$this->company_id}' and school_id='{$this->school_id}'");

        if ($limit == 1) {
            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_limitnum", "course_id='{$classOne['course_id']}'");

            if ($courseOne['course_limitnum'] == 1) {
                $sql = "select study_id from smc_student_study where class_id='{$class_id}' and study_isreading='1'";
                $studentList = $this->DataControl->selectClear($sql);
                $num = $studentList ? count($studentList) : 0;
                if ($num >= $classOne['class_fullnums']) {
                    $this->error = true;
                    $this->errortip = "班级人数已满,不可入班";
                    return false;
                }
            }
        }

        $enrolledOne = $this->DataControl->getFieldOne("smc_student_enrolled", "enrolled_status", "school_id='{$this->school_id}' and student_id='{$student_id}'");
        if ($enrolledOne['enrolled_status'] != 1) {
            $enrolled_data = array();
            $enrolled_data['enrolled_updatatime'] = $time;
            $enrolled_data['enrolled_status'] = 1;
            $this->DataControl->updateData("smc_student_enrolled", "school_id='{$this->school_id}' and student_id='{$student_id}'", $enrolled_data);
        }

        $studyOne=$this->DataControl->getFieldOne("smc_student_study", "student_id,study_beginday", "company_id='{$this->company_id}' and school_id='{$this->school_id}' and student_id='{$student_id}' and class_id='{$class_id}'");

        if ($studyOne) {
            $study_data = array();
            $study_data['study_isreading'] = 1;
//			if($enterclassdate!='') {
//				$study_data['study_beginday'] = $enterclassdate;
//			}else{
//				$study_data['study_beginday'] = date("Y-m-d",$time);
//			}

            if($enterclassdate!='' && $enterclassdate<$studyOne['study_beginday']) {
                $study_data['study_beginday'] = $enterclassdate;
            }

            $study_data['study_endday'] = $classOne['class_enddate'];
            $study_data['study_updatetime'] = time();
            $this->DataControl->updateData("smc_student_study", "company_id='{$this->company_id}' and school_id='{$this->school_id}' and student_id='{$student_id}' and class_id='{$class_id}'", $study_data);

        } else {
            $study_data = array();
            $study_data['company_id'] = $this->company_id;
            $study_data['school_id'] = $this->school_id;
            $study_data['student_id'] = $student_id;
            $study_data['study_isreading'] = 1;
            $study_data['class_id'] = $class_id;
            $study_data['class_branch'] = $classOne['class_branch'];
            if ($enterclassdate != '') {
                $study_data['study_beginday'] = $enterclassdate;
            } else {
                $study_data['study_beginday'] = date("Y-m-d", $time);
            }
            $study_data['study_endday'] = $classOne['class_enddate'];
            $study_data['study_updatetime'] = time();
            $this->DataControl->insertData("smc_student_study", $study_data);
        }

        if ($from == 0) {

            $like = date("Ymd", $time);
            $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_id DESC limit 0,1");

            $data = array();
            $data['company_id'] = $this->company_id;
            $data['student_id'] = $student_id;
            if ($changeInfo) {
                $data['change_pid'] = $changeInfo['change_pid'] + 1;
            } else {
                $data['change_pid'] = $like . '000001';
            }
            $data['to_stuchange_code'] = 'A02';
            $data['to_school_id'] = $this->school_id;
            $data['to_class_id'] = $class_id;
            $data['change_status'] = 1;
            $data['change_day'] = $enterclassdate != '' ? $enterclassdate : date("Y-m-d", $time);
            $data['reason_code'] = $reason_code;
            $data['change_reason'] = $change_reason;
            $data['change_workername'] = $this->stafferOne['staffer_cnname'];
            $data['change_createtime'] = time();
            $this->DataControl->insertData("smc_student_change", $data);

            $log_data = array();
            $log_data['change_pid'] = $data['change_pid'];
            $log_data['company_id'] = $this->company_id;
            $log_data['student_id'] = $student_id;
            $log_data['changelog_type'] = 1;
            $log_data['stuchange_code'] = 'A02';
            $log_data['school_id'] = $this->school_id;
            $log_data['class_id'] = $class_id;
            $log_data['changelog_note'] = $change_reason;
            $log_data['changelog_day'] = $enterclassdate != '' ? $enterclassdate : date("Y-m-d", $time);
            $log_data['staffer_id'] = ($staffer_id == 0) ? $this->stafferOne['staffer_id'] : $staffer_id;
            $log_data['changelog_createtime'] = time();
            $this->DataControl->insertData("smc_student_changelog", $log_data);


        }

        $course_data = array();
        $course_data['coursebalance_status'] = 1;
        $course_data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$student_id}' and course_id='{$classOne['course_id']}' and school_id='{$this->school_id}'", $course_data);
        return true;
    }

    //入校
    function entrySchool($student_id)
    {
        if ($this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "student_id='{$student_id}' and school_id='{$this->school_id}'")) {
            $en_data = array();
            $en_data['enrolled_updatatime'] = time();
            $en_data['enrolled_status'] = 0;
            $this->DataControl->updateData("smc_student_enrolled", "student_id='{$student_id}' and school_id='{$this->school_id}'", $en_data);
        } else {
            $en_data = array();
            $en_data['school_id'] = $this->school_id;
            $en_data['student_id'] = $student_id;
            $en_data['enrolled_createtime'] = time();
            $en_data['enrolled_status'] = 0;
            $this->DataControl->insertData("smc_student_enrolled", $en_data);
        }

        $like = date("Ymd", time());
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$this->stafferOne['staffer_id']}' and company_id='{$this->company_id}'");
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['to_stuchange_code'] = 'A01';
        $data['to_school_id'] = $this->school_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", time());
//            $data['reason_code']=$reason_code;
        $data['change_reason'] = $this->LgStringSwitch('入校');
        $data['change_workername'] = $stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 1;
        $log_data['stuchange_code'] = 'A01';
        $log_data['school_id'] = $this->school_id;
        $log_data['changelog_note'] = $this->LgStringSwitch('入校');
        $log_data['changelog_day'] = date("Y-m-d", time());
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);

        /*if (!$this->DataControl->getFieldOne("smc_student_balance", "student_id", "student_id = '{$student_id}' and school_id = '{$this->school_id}'")) {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$this->school_id}'");
            $balance = array();
            $balance['company_id'] = $this->company_id;
            $balance['companies_id'] = $schoolOne['companies_id'];
            $balance['student_id'] = $student_id;
            $balance['school_id'] = $this->school_id;
            $this->DataControl->insertData("smc_student_balance", $balance);
        }*/

        $this->error = true;
        $this->oktip = "入校成功";
        return true;
    }

    function loss($student_id, $reason_code, $reason = '', $category = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = 'C02';
        $data['from_school_id'] = $this->school_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = 'C02';
        $log_data['school_id'] = $this->school_id;
        $log_data['changelog_note'] = $reason;
        $log_data['changelog_category'] = $category;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);
    }

    function courseTypeLoss($student_id, $stuchange_code = '', $coursetype_id = 0, $reason_code = '', $reason = '', $category = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = $stuchange_code;
        $data['from_school_id'] = $this->school_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['coursetype_id'] = $coursetype_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = $stuchange_code;
        $log_data['school_id'] = $this->school_id;
        $log_data['changelog_category'] = $category;
        $log_data['changelog_note'] = $reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);
    }


    function graduation($student_id, $class_id, $reason_code = '', $change_reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $like = date("Ymd", $time);
        $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$this->company_id}' order by change_pid DESC limit 0,1");
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['student_id'] = $student_id;
        if ($changeInfo) {
            $data['change_pid'] = $changeInfo['change_pid'] + 1;
        } else {
            $data['change_pid'] = $like . '000001';
        }
        $data['from_stuchange_code'] = 'C03';
        $data['from_school_id'] = $this->school_id;
        $data['from_school_id'] = $class_id;
        $data['change_status'] = 1;
        $data['change_day'] = date("Y-m-d", $time);
        $data['reason_code'] = $reason_code;
        $data['change_reason'] = $change_reason;
        $data['change_workername'] = $this->stafferOne['staffer_cnname'];
        $data['change_createtime'] = time();
        $this->DataControl->insertData("smc_student_change", $data);

        $log_data = array();
        $log_data['change_pid'] = $data['change_pid'];
        $log_data['company_id'] = $this->company_id;
        $log_data['student_id'] = $student_id;
        $log_data['changelog_type'] = 0;
        $log_data['stuchange_code'] = 'C03';
        $log_data['school_id'] = $this->school_id;
        $log_data['class_id'] = $class_id;
        $log_data['changelog_note'] = $change_reason;
        $log_data['changelog_day'] = date("Y-m-d", $time);
        $log_data['staffer_id'] = $this->stafferOne['staffer_id'];
        $log_data['changelog_createtime'] = time();
        $this->DataControl->insertData("smc_student_changelog", $log_data);
    }

}