<?php


namespace Model\Smc;

class RefundModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array(), $order_pid = 0)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
        if ($order_pid !== '0') {
            $this->verdictpayFeeOrder($order_pid);
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictpayFeeOrder($order_pid)
    {
        $this->payfeeorderOne = $this->DataControl->getOne("smc_refund_order", "refund_pid = '{$order_pid}'");
        if (!$this->payfeeorderOne) {
            $this->error = true;
            $this->errortip = "退费订单信息不存在";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        }
    }

    /**
     * @param $request
     * @param $student_id
     * @param $bank --开户行
     * @param $bankcard --银行卡号
     * @param $accountname --开户名
     * @param $price --退款金额
     * @param string $title
     * @param string $information
     * @param string $reason
     * @return bool|string
     */
    function refund($student_id, $bank, $bankcard, $accountname, $title = '', $information = '', $reason = '', $time = '', $is_clear = 1, $refund_price = 0, $specialprice, $specialreason = '',$companies_id=0,$refund_reasontype=0,$refund_bankcardurl='')
    {
        if ($time == '') {
            $time = time();
        }

        $data = array();

        $stublcOne = $this->getStuBalance($student_id,$this->company_id,$this->school_id,$companies_id);
        if ($stublcOne['student_balance'] < $refund_price) {
            $this->error = 1;
            $this->errortip = "请填写正确的金额";
            return false;
        } else {
            $stublcOne['student_balance'] = $refund_price;
        }

        if (!$stublcOne) {
            $this->error = 1;
            $this->errortip = "学员不存在";
            return false;
        }
        if ($stublcOne['student_balance'] < 1 && $specialprice <= '0') {
            $this->error = 1;
            $this->errortip = "账户余额小于1元,无法退款";
            return false;
        }

        $trading_pid = $this->stuTrading($student_id, 'Accountrefund',$stublcOne['companies_id'], $time);
        //结转余额清零
        if ($is_clear == 0) {
            $studentOne = $this->DataControl->getOne('smc_student', "student_id='{$student_id}'");
            if ($studentOne['student_forwardprice'] > 0) {

                $data['refund_forward'] = $studentOne['student_forwardprice'];
                $balanceData = array();
                $balanceData['company_id'] = $this->company_id;
                $balanceData['companies_id'] = $stublcOne['companies_id'];
                $balanceData['school_id'] = $this->school_id;
                $balanceData['staffer_id'] = $this->stafferOne['staffer_id'];
                $balanceData['student_id'] = $student_id;
                $balanceData['trading_pid'] = $trading_pid;
                $balanceData['balancelog_class'] = 1;
                $balanceData['balancelog_playname'] = $this->LgStringSwitch("退款结转余额清");
                $balanceData['balancelog_playclass'] = "-";
                $balanceData['balancelog_fromamount'] = $studentOne['student_forwardprice'];
                $balanceData['balancelog_playamount'] = $studentOne['student_forwardprice'];
                $balanceData['balancelog_finalamount'] = '0';
                $balanceData['balancelog_reason'] = $this->LgStringSwitch('账户退款,结转余额清零');
                $balanceData['balancelog_time'] = $time;
                $this->DataControl->insertData("smc_student_balancelog", $balanceData);

                $stu_data = array();
                $stu_data['student_forwardprice'] = 0;
                $stu_data['student_updatatime'] = $time;
                $this->DataControl->updateData('smc_student', "student_id='{$student_id}'", $stu_data);
            }
        }

        do {
            $order_pid = $this->creatPid();
        } while ($this->DataControl->selectOne("select refund_pid from smc_refund_order where refund_pid='{$order_pid}' limit 0,1"));

        $memberOne = $this->DataControl->getFieldOne("smc_student_family", "family_cnname,family_mobile", "student_id='{$student_id}' and family_isdefault=1");

        $data['trading_pid'] = $trading_pid;
        $data['refund_pid'] = $order_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $stublcOne['companies_id'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $student_id;
        $data['refund_from'] = 1;
        $data['refund_name'] = $memberOne['family_cnname'];
        $data['refund_mobile'] = $memberOne['family_mobile'];
        $data['refund_bank'] = $bank;
        $data['refund_accountname'] = $accountname;
        $data['refund_bankcard'] = $bankcard;
        $data['refund_reason'] = $reason;
        $data['refund_price'] = $stublcOne['student_balance'];
        $data['refund_specialprice'] = $specialprice;
        $data['refund_specialreason'] = $specialreason;
        $data['refund_reasontype'] = $refund_reasontype;
        $data['refund_bankcardurl'] = $refund_bankcardurl;
        if ($specialprice > 0) {
            $data['refund_isspecial'] = 1;
        }
        $data['refund_payprice'] = $stublcOne['student_balance'] + $specialprice;
        $data['refund_createtime'] = $time;
        if ($this->DataControl->insertData("smc_refund_order", $data)) {
            $this->refundOrderTracks($order_pid, $this->LgStringSwitch($title), $information, $reason, $time);
            $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
            $BalanceModel->reduceStuBalance($student_id, $trading_pid, $stublcOne['student_balance'], 1,$stublcOne['companies_id'], $time);

            if($this->company_id=='8888' ){
                $fanweiModel = new \Model\Smc\FanweiModel();
                $bool=$fanweiModel->createRefundApply($order_pid);
                if(!$bool){
                    $param = array();
                    $param['is_adopt'] = 0;
                    $param['refund_pid'] = $order_pid;
                    $param['reason'] = $this->LgStringSwitch('泛微流程生成失败('.$fanweiModel->errortip.'),请联系技术查看问题');
                    
                    $OrderModel = new \Model\Gmc\OrderModel($this->publicarray);
                    $res = $OrderModel->examineRefundOrder($param);
                }
            }

            $this->error = true;
            $this->errortip = "退款成功";
            return $order_pid;
        } else {
            $this->error = true;
            $this->errortip = "退款失败";
            return false;
        }
    }

    function stuTrading($student_id, $code = "",$companies_id, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        do {
            $trading_pid = $this->createOrderPid('JY');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $companies_id;
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = $code;
        $data['trading_status'] = 0;
        $data['trading_createtime'] = $time;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        if ($this->DataControl->insertData("smc_student_trading", $data)) {
            return $trading_pid;
        } else {
            return false;
        }
    }

    //退款订单跟踪
    function refundOrderTracks($order_pid, $title = '', $information = '', $note = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $TracksData = array();
        $TracksData['refund_pid'] = $order_pid;
        $TracksData['tracks_title'] = $title;
        $TracksData['tracks_information'] = $information;
        $TracksData['tracks_note'] = $note;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);
    }

    function refuseSchoolVerify($request)
    {
        if ($request['create_time'] != '') {
            $time = strtotime($request['create_time']);
        } else {
            $time = time();
        }

        $stublcOne = $this->getStuBalance($this->payfeeorderOne['student_id'],$this->company_id,$this->school_id,$this->payfeeorderOne['companies_id']);

        if ($this->payfeeorderOne['refund_forward'] > 0) {
            $balancelog = array();
            $balancelog['company_id'] = $this->company_id;
            $balancelog['school_id'] = $this->payfeeorderOne['school_id'];
            $balancelog['companies_id'] = $this->payfeeorderOne['companies_id'];
            $balancelog['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog['student_id'] = $this->payfeeorderOne['student_id'];
            $balancelog['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $balancelog['balancelog_class'] = 1;
            $balancelog['balancelog_playname'] = $this->LgStringSwitch('退款清空结转金额转账户结转金额');
            $balancelog['balancelog_playclass'] = '+';
            $balancelog['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
            $balancelog['balancelog_playamount'] = $this->payfeeorderOne['refund_forward'];
            $balancelog['balancelog_finalamount'] = $stublcOne['student_forwardprice'] + $this->payfeeorderOne['refund_forward'];
            $balancelog['balancelog_reason'] = $this->LgStringSwitch('退款清空结转金额转账户结转金额');
            $balancelog['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog);

            $data = array();
            $data['student_forwardprice'] = $stublcOne['student_forwardprice'] + $this->payfeeorderOne['refund_forward'];
            $data['student_updatatime'] = $time;
            $this->DataControl->updateData("smc_student", "student_id='{$this->payfeeorderOne['school_id']}' and company_id='{$this->company_id}'", $data);
        }

        if ($this->payfeeorderOne['refund_price'] > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $balancelog_data['school_id'] = $this->payfeeorderOne['school_id'];
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
            $balancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $balancelog_data['balancelog_class'] = 0;
            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('申请退款金额转账户余额');
            $balancelog_data['balancelog_playclass'] = '+';
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
            $balancelog_data['balancelog_playamount'] = $this->payfeeorderOne['refund_price'];
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $this->payfeeorderOne['refund_price'];
            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('申请退款金额转账户余额');
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

            $data = array();
            $data['student_balance'] = $stublcOne['student_balance'] + $this->payfeeorderOne['refund_price'];
            $this->DataControl->updateData("smc_student_balance", "student_id='{$this->payfeeorderOne['student_id']}' and school_id = '{$this->payfeeorderOne['school_id']}' and company_id='{$this->company_id}' and companies_id='{$this->payfeeorderOne['companies_id']}'", $data);

        }

        $TracksData = array();
        $TracksData['refund_pid'] = $this->payfeeorderOne['refund_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('校长审核拒绝，请查看拒绝原因');
        $TracksData['tracks_note'] = $request['reason'];
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);

        $data = array();
        $data['refund_status'] = -1;
        $data['refund_updatatime'] = time();
        $this->DataControl->updateData("smc_refund_order", "refund_pid='{$this->payfeeorderOne['refund_pid']}'", $data);

        $data = array();
        $data['trading_status'] = 1;
        $data['trading_updatatime'] = time();
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        return true;
    }


    function stuGoodsRefund($student_id, $bank, $accountname, $bankcard, $reason, $price, $erpgoods_id,$companies_id, $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $data = array();

        do {
            $order_pid = $this->creatPid();
        } while ($this->DataControl->selectOne("select refund_pid from smc_refund_order where refund_pid='{$order_pid}' limit 0,1"));

        $memberOne = $this->DataControl->getFieldOne("smc_student_family", "family_cnname,family_mobile", "student_id='{$student_id}' and family_isdefault=1");

        $data['trading_pid'] = $this->stuTrading($student_id, 'Accountrefund',$companies_id, $time);
        $data['refund_pid'] = $order_pid;
        $data['refund_tradeclass'] = '1';
        $data['companies_id'] = $companies_id;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $student_id;
        $data['refund_from'] = 1;
        $data['refund_name'] = $memberOne['family_cnname'];
        $data['refund_mobile'] = $memberOne['family_mobile'];
        $data['refund_bank'] = $bank;
        $data['refund_accountname'] = $accountname;
        $data['refund_bankcard'] = $bankcard;
        $data['refund_reason'] = addslashes($reason);
        $data['refund_price'] = $price;
        $data['refund_payprice'] = $price;
        $data['refund_createtime'] = $time;
        $this->DataControl->insertData("smc_refund_order", $data);

        $data = array();
        $data['refund_pid'] = $order_pid;
        $data['erpgoods_id'] = $erpgoods_id;
        $this->DataControl->insertData("smc_refund_order_erpgoods", $data);

        $this->refundOrderTracks($order_pid, $this->LgStringSwitch('商品退费'), $this->LgStringSwitch('商品退费'), $reason, $time);

        return $order_pid;
    }


    function stuTimesRefund($student_id, $itemtimes_number, $itemtimes_id, $bank, $accountname, $bankcard, $reason = '杂费退款', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $timesOne = $this->DataControl->getOne("smc_student_itemtimes", "itemtimes_id='{$itemtimes_id}'");
        $data = array();

        if ($itemtimes_number == $timesOne['itemtimes_number']) {
            $price = $timesOne['itemtimes_figure'];
        } else {
            $price = floor($itemtimes_number * $timesOne['itemtimes_figure'] / $timesOne['itemtimes_number']);
        }

        do {
            $order_pid = $this->creatPid();
        } while ($this->DataControl->selectOne("select refund_pid from smc_refund_order where refund_pid='{$order_pid}' limit 0,1"));

        $memberOne = $this->DataControl->getFieldOne("smc_student_family", "family_cnname,family_mobile", "student_id='{$student_id}' and family_isdefault=1");
        $trading_pid = $this->stuTrading($student_id, 'Accountrefund',$timesOne['companies_id'], $time);
        $data['trading_pid'] = $trading_pid;
        $data['refund_pid'] = $order_pid;
        $data['refund_tradeclass'] = '2';
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $timesOne['companies_id'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $student_id;
        $data['refund_from'] = 1;
        $data['refund_name'] = $memberOne['family_cnname'];
        $data['refund_mobile'] = $memberOne['family_mobile'];
        $data['refund_bank'] = $bank;
        $data['refund_accountname'] = $accountname;
        $data['refund_bankcard'] = $bankcard;
        $data['refund_reason'] = $this->LgStringSwitch($reason);
        $data['refund_price'] = $price;
        $data['refund_payprice'] = $price;
        $data['refund_createtime'] = $time;
        $this->DataControl->insertData("smc_refund_order", $data);

        $data = array();
        $data['refund_pid'] = $order_pid;
        $data['course_id'] = $timesOne['course_id'];
        $data['feeitem_id'] = $timesOne['feeitem_id'];
        $data['itemtimes_number'] = $itemtimes_number;
        $data['itemtimes_figure'] = $price;
        $this->DataControl->insertData("smc_refund_order_itemtimes", $data);

        $this->refundOrderTracks($order_pid, $this->LgStringSwitch('申请退款'), $this->LgStringSwitch('订单提交成功，等待校长审核'), $reason, $time);

        $data = array();
        $data['student_id'] = $student_id;
        $data['trading_pid'] = $trading_pid;
        $data['itemtimes_id'] = $itemtimes_id;
        $data['companies_id'] = $timesOne['companies_id'];
        $data['feeitem_id'] = $timesOne['feeitem_id'];
        $data['log_playname'] = $this->LgStringSwitch('杂费退费减少余额');
        $data['log_playclass'] = '-';
        $data['log_fromamount'] = $timesOne['itemtimes_figure'];
        $data['log_playamount'] = $price;
        $data['log_finalamount'] = $timesOne['itemtimes_figure'] - $price;
        $data['log_reason'] = $this->LgStringSwitch('杂费退费减少余额');
        $data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_itemtimes_log", $data);

        $data = array();
        $data['itemtimes_number'] = $timesOne['itemtimes_number'] - $itemtimes_number;
        $data['itemtimes_figure'] = $timesOne['itemtimes_figure'] - $price;
        $data['itemtimes_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_itemtimes", "itemtimes_id='{$itemtimes_id}'", $data);

        return $order_pid;
    }

    function subscribeStuTimes($student_id, $itemtimes_number, $itemtimes_id, $reason, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $timesOne = $this->DataControl->getOne("smc_student_itemtimes", "itemtimes_id='{$itemtimes_id}'");

        do {
            $trading_pid = $this->createOrderPid('RJ');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

        if ($itemtimes_number == $timesOne['itemtimes_number']) {
            $price = $timesOne['itemtimes_figure'];
        } else {
            $price = floor($itemtimes_number * $timesOne['itemtimes_figure'] / $timesOne['itemtimes_number']);
        }

        $data = array();
        $data['student_id'] = $student_id;
        $data['trading_pid'] = $trading_pid;
        $data['itemtimes_id'] = $itemtimes_id;
        $data['feeitem_id'] = $timesOne['feeitem_id'];
        $data['companies_id'] = $timesOne['companies_id'];
        $data['log_playname'] = $this->LgStringSwitch('认缴杂费余额');
        $data['log_playclass'] = '-';
        $data['log_fromamount'] = $timesOne['itemtimes_figure'];
        $data['log_playamount'] = $price;
        $data['log_finalamount'] = $timesOne['itemtimes_figure'] - $price;
        $data['log_reason'] = $reason;
        $data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_itemtimes_log", $data);

        $data = array();
        $data['itemtimes_number'] = $timesOne['itemtimes_number'] - $itemtimes_number;
        $data['itemtimes_figure'] = $timesOne['itemtimes_figure'] - $price;
        $data['itemtimes_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_itemtimes", "itemtimes_id='{$itemtimes_id}'", $data);

        $data = array();
        $data['trading_pid'] = $trading_pid;
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $timesOne['companies_id'];
        $data['student_id'] = $student_id;
        $data['tradingtype_code'] = 'Subscribed';
        $data['trading_status'] = "1";
        $data['trading_createtime'] = $time;
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $this->DataControl->insertData("smc_student_trading", $data);

        $in_data = array();
        $in_data['company_id'] = $this->company_id;
        $in_data['companies_id'] = $timesOne['companies_id'];
        $in_data['school_id'] = $this->school_id;
        $in_data['income_type'] = '3';
        $in_data['student_id'] = $student_id;
        $in_data['trading_pid'] = $trading_pid;
        $in_data['income_price'] = $price;
        $in_data['income_note'] = $reason;
        $in_data['income_confirmtime'] = $time;
        $in_data['income_audittime'] = $time;
        $in_data['income_createtime'] = time();
        $this->DataControl->insertData("smc_school_income", $in_data);
    }

    function stuDepositRefund($student_id, $coursecatbalance_figure, $coursecatbalance_id, $bank, $accountname, $bankcard, $reason = '定金退款', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $depositOne = $this->DataControl->getOne("smc_student_coursecatbalance", "coursecatbalance_id='{$coursecatbalance_id}'");
        $data = array();

        if ($coursecatbalance_figure == $depositOne['coursecatbalance_figure']) {
            $price = $depositOne['coursecatbalance_figure'];
        } else {
            $price = $coursecatbalance_figure;
        }

        do {
            $order_pid = $this->creatPid();
        } while ($this->DataControl->selectOne("select refund_pid from smc_refund_order where refund_pid='{$order_pid}' limit 0,1"));

        $memberOne = $this->DataControl->getFieldOne("smc_student_family", "family_cnname,family_mobile", "student_id='{$student_id}' and family_isdefault=1");

        $trading_pid = $this->stuTrading($student_id, 'Accountrefund',$depositOne['companies_id'], $time);

        $data['trading_pid'] = $trading_pid;
        $data['refund_pid'] = $order_pid;
        $data['refund_tradeclass'] = '3';
        $data['company_id'] = $this->company_id;
        $data['school_id'] = $this->school_id;
        $data['companies_id'] = $depositOne['companies_id'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['student_id'] = $student_id;
        $data['refund_from'] = 1;
        $data['refund_name'] = $memberOne['family_cnname'];
        $data['refund_mobile'] = $memberOne['family_mobile'];
        $data['refund_bank'] = $bank;
        $data['refund_accountname'] = $accountname;
        $data['refund_bankcard'] = $bankcard;
        $data['refund_reason'] = $this->LgStringSwitch($reason);
        $data['refund_price'] = $price;
        $data['refund_payprice'] = $price;
        $data['refund_createtime'] = $time;
        $this->DataControl->insertData("smc_refund_order", $data);

        $this->refundOrderTracks($order_pid, $this->LgStringSwitch('申请退款'), $this->LgStringSwitch('订单提交成功，等待校长审核'), $reason, $time);

        $log = array();
        $log['student_id'] = $student_id;
        $log['companies_id'] = $depositOne['companies_id'];
        $log['coursetype_id'] = $depositOne['coursetype_id'];
        $log['coursecat_id'] = $depositOne['coursecat_id'];
        $log['feetype_code'] = 'Deposit';
        $log['school_id'] = $this->school_id;
        $log['staffer_id'] = $this->stafferOne['staffer_id'];
        $log['trading_pid'] = $order_pid;
        $log['log_playclass'] = '-';
        $log['log_fromamount'] = $depositOne['coursecatbalance_figure'];
        $log['log_playamount'] = $price;
        $log['log_finalamount'] = $depositOne['coursecatbalance_figure'] - $price;
        $log['log_fromme'] = 0;
        $log['log_playme'] = 0;
        $log['log_finaltime'] = 0;
        $log['log_time'] = $time;
        $log['log_playname'] = $this->LgStringSwitch('定金退款');
        $log['log_reason'] = $this->LgStringSwitch('定金退款');
        $this->DataControl->insertData("smc_student_coursecatbalance_log", $log);

        $data = array();
        $data['coursecatbalance_figure'] = $depositOne['coursecatbalance_figure'] - $price;
        $data['coursecatbalance_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_coursecatbalance", "coursecatbalance_id='{$coursecatbalance_id}'", $data);

        return $order_pid;
    }

}