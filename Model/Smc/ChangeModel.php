<?php


namespace Model\Smc;

class ChangeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['school_id'])) {
            $this->school_id = $publicarray['school_id'];
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function classChange($request)
    {
        //缺少上课时间
        $datawhere = " 1 ";
        $orderwhere = " ";

        if (isset($request['order_by']) && $request['order_by'] == 'ASC') {
            $orderwhere .= " ASC";
        } else {
            $orderwhere .= " DESC";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or c.class_branch like '%{$request['keyword']}%' 
            or c.class_cnname like '%{$request['keyword']}%' 
            or sc.changelog_note like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and sc.changelog_day>='{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and sc.changelog_day<='{$request['endtime']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and sc.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and sc.student_id ='{$request['student_id']}'";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and sc.class_id ='{$request['class_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['search_school_id']) && $request['search_school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['search_school_id']}'";
        } else {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        $sql = "select sc.changelog_id,sc.class_id
                ,s.student_cnname,s.student_enname,s.student_branch
                ,cs.stuchange_name,cs.stuchange_code
                ,sch.school_cnname
                ,ifnull(c.class_cnname,'--') as class_cnname
                ,ifnull(c.class_enname,'--') as class_enname
                ,ifnull(c.class_branch,'--') as class_branch
                ,(case when sc.changelog_note='' then '--'else sc.changelog_note end) as changelog_note
                ,sc.changelog_day,cs.stustatus_isdel,sc.change_pid
                ,sc.stuchange_code,sc.student_id
                ,ifnull(cc.coursetype_cnname,'--') as coursetype_cnname
                ,ifnull(cc.coursetype_branch,'--') as coursetype_branch
				,(select s.staffer_cnname from smc_staffer as s where s.staffer_id =sc.staffer_id ) as staffer_cnname
                from smc_student_changelog as sc
                left join smc_student as s on s.student_id=sc.student_id
                left join smc_code_stuchange as cs on cs.stuchange_code=sc.stuchange_code
                left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
                left join smc_school as sch on sch.school_id=sc.school_id
                left join smc_class as c on c.class_id=sc.class_id
                WHERE {$datawhere} and sc.company_id='{$request['company_id']}'
                group by sc.changelog_id
                order by sc.changelog_day {$orderwhere},sc.changelog_id {$orderwhere}
                ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "异动类型", "异动班组名称", "异动班组编号", "异动学校", "异动班级", "异动班级别名", "异动班级编号", "异动描述", "异动日期", "执行人"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'stuchange_name', 'coursetype_cnname', 'coursetype_branch', 'school_cnname', 'class_cnname', 'class_enname', 'class_branch', 'changelog_note', 'changelog_day', 'staffer_cnname');
            $tem_name = $this->LgStringSwitch("学员异动管理表{$outexceldate[0]['student_cnname']}.xlsx");
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $changeList = $this->DataControl->selectClear($sql);

            if (!$changeList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select sc.changelog_id
                from smc_student_changelog as sc
                left join smc_student as s on s.student_id=sc.student_id
                left join smc_code_stuchange as cs on cs.stuchange_code=sc.stuchange_code
                left join smc_code_coursetype as cc on cc.coursetype_id=sc.coursetype_id
                left join smc_school as sch on sch.school_id=sc.school_id
                left join smc_class as c on c.class_id=sc.class_id
                WHERE {$datawhere} and sc.company_id='{$request['company_id']}'
                group by sc.changelog_id
                ";
                $db_nums = $this->DataControl->selectClear($count_sql);

                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $changeList;

            return $data;
        }
    }

    function changeExamineList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' )";
        }
//
//		if (isset($request['starttime']) && $request['starttime'] !== '') {
//			$datawhere .= " and sc.change_day>='{$request['starttime']}'";
//		}
//		if (isset($request['endtime']) && $request['endtime'] !== '') {
//			$datawhere .= " and sc.change_day<='{$request['endtime']}'";
//		}

        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $datawhere .= " and sch.change_day='{$request['fixedtime']}'";
        }

        if (isset($request['change_status']) && $request['change_status'] !== '') {
            $datawhere .= " and sch.change_status='{$request['change_status']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and sch.from_stuchange_code='{$request['stuchange_code']}'";
        }

        $sql = "select sch.change_id,sch.change_pid,s.student_cnname,s.student_enname,s.student_branch,st.stuchange_name,ss.school_cnname,c.class_cnname,sch.change_reason,sch.change_day,sch.change_status,sch.change_workername
				from smc_student_change as sch
				left join smc_code_stuchange as st on st.stustatus_code=sch.from_stuchange_code
				left join smc_student as s on s.student_id=sch.student_id
				left join smc_school as ss on ss.school_id=sch.from_school_id
				left join smc_class as c on c.class_id=sch.from_class_id
				where {$datawhere} and sch.company_id='{$request['company_id']}' and sch.change_status='0'
				order by sch.change_id desc
				limit {$pagestart},{$num}
				";

        $changeList = $this->DataControl->selectClear($sql);
        if (!$changeList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $tem_array = array();

        foreach ($changeList as $val) {
            $tem_sql = "select st.stuchange_name,ss.school_cnname,c.class_cnname
				from smc_student_change as sch
				left join smc_code_stuchange as st on st.stustatus_code=sch.to_stuchange_code
				left join smc_school as ss on ss.school_id=sch.to_school_id
				left join smc_class as c on c.class_id=sch.to_class_id
				where sch.company_id='{$request['company_id']}' and sch.change_pid='{$val['change_pid']}' limit 0,1
				";

            $changeOne = $this->DataControl->selectOne($tem_sql);
            $data = array();
            $data['change_id'] = $val['change_id'];
            $data['change_pid'] = $val['change_pid'];
            $data['student_cnname'] = $val['student_cnname'];
            $data['student_enname'] = $val['student_enname'];
            $data['student_branch'] = $val['student_branch'];
            $data['from_stuchange_name'] = $val['stuchange_name'] ? $val['stuchange_name'] : '--';
            $data['from_school_cnname'] = $val['school_cnname'] ? $val['school_cnname'] : '--';
            $data['from_class_cnname'] = $val['class_cnname'] ? $val['class_cnname'] : '--';
            $data['to_stuchange_name'] = $changeOne['stuchange_name'] ? $changeOne['stuchange_name'] : '--';
            $data['to_school_cnname'] = $changeOne['school_cnname'] ? $changeOne['school_cnname'] : '--';
            $data['to_class_cnname'] = $changeOne['class_cnname'] ? $changeOne['class_cnname'] : '--';
            $data['change_reason'] = $val['change_reason'];
            $data['change_day'] = $val['change_day'];
            $data['change_status'] = $val['change_status'];
            $data['change_workername'] = $val['change_workername'];
            $tem_array[] = $data;
        }
        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select sch.change_id
				from smc_student_change as sch
				left join smc_student as s on s.student_id=sch.student_id
				where {$datawhere} and sch.company_id='{$request['company_id']}' and sch.change_status='0'
                ";
            $db_nums = $this->DataControl->selectClear($count_sql);

            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $tem_array;

        return $data;

    }

    function classStudent($request)
    {
        $classOne = $this->DataControl->getFieldOne("smc_class", "father_id", "class_id='{$request['class_id']}'");
        if ($classOne['father_id'] <= 0) {
            $this->error = true;
            $this->errortip = "请选择子班班级";
            return false;
        }

        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%')";
        }

        if (isset($request['type']) && $request['type'] == '1') {
            $datawhere .= " and s.student_id in (select sst.student_id from smc_student_study as sst
			  							left join smc_class as c on c.class_id=sst.class_id
			  							where c.father_id='{$classOne['father_id']}' and sst.study_isreading='1' and sst.class_id<>'{$request['class_id']}')";
        } elseif ($request['type'] == '0') {
            $datawhere .= " and s.student_id not in (select sst.student_id from smc_student_study as sst
			  							left join smc_class as c on c.class_id=sst.class_id
			  							where c.father_id='{$classOne['father_id']}' and sst.study_isreading='1')";
        } else {
            $datawhere .= " and s.student_id in (select sst.student_id from smc_student_study as sst where sst.class_id='{$request['class_id']}' and sst.study_isreading='1')";
        }

        $datawhere .= " and s.student_id in (select sst.student_id from smc_student_study as sst where sst.class_id='{$classOne['father_id']}' and sst.study_isreading='1')";

        if (isset($request['list']) && $request['list'] != '') {
            $datawhere .= " and s.student_id not in ({$request['list']})";
        }

        $sql = "select s.student_id,s.student_branch,s.student_cnname,s.student_enname,s.student_img,s.student_sex,ss.study_beginday
			  from smc_student_study as ss
			  left join smc_student as s on ss.student_id=s.student_id
			  where {$datawhere} and ss.class_id='{$classOne['father_id']}'
			  order by s.student_id desc";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无相关学员数据";
            return false;
        }

        return $studentList;

    }

    function classCourseStuList($request)
    {
        $courseOne = $this->DataControl->getFieldOne("smc_class", "course_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and class_id='{$request['class_id']}'");
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['list']) && $request['list'] != '') {
            $datawhere .= " and s.student_id not in ({$request['list']})";
        }

        $contractOne = $this->getContract($this->company_id);

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex
                  from smc_student_enrolled as se,smc_student as s 
                  where {$datawhere} and se.student_id=s.student_id and se.school_id='{$this->school_id}' 
                  and (enrolled_status=0 or enrolled_status=1)
                  and not exists(select 1 from smc_student_study as ss,smc_class as cl where ss.class_id=cl.class_id and cl.course_id='{$courseOne['course_id']}' and ss.student_id=s.student_id and ss.study_isreading=1)
                  ";

        } else {
            $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,ssc.coursebalance_time,co.course_inclasstype
					,(select po.order_status
						from smc_payfee_order_course as poc
						left join smc_payfee_order as po on po.order_pid=poc.order_pid
						where po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.student_id=s.student_id and poc.course_id=ssc.course_id
						and poc.pricing_id=ssc.pricing_id
						order by po.order_createtime desc
						limit 0,1) as order_status
					,(select count(sss.study_id) from smc_student_study as sss left join smc_class as c on c.class_id=sss.class_id where sss.company_id='{$request['company_id']}' and sss.school_id='{$request['school_id']}' and sss.student_id=s.student_id and c.course_id=ssc.course_id and sss.study_isreading=1) as num
					,(select sc.stuchange_code from smc_student_changelog as sc where sc.class_id='{$request['class_id']}' and sc.student_id=s.student_id and sc.school_id='{$request['school_id']}' order by changelog_id desc limit 0,1) as stuchange_code
				from smc_student_coursebalance as ssc
				left join smc_student as s on s.student_id=ssc.student_id
				left join smc_student_enrolled as se on se.student_id=s.student_id
				left join smc_course as co on co.course_id=ssc.course_id
				where {$datawhere} and se.enrolled_status>=0 and ssc.course_id='{$courseOne['course_id']}' and ssc.school_id='{$request['school_id']}' and ssc.company_id='{$request['company_id']}'
				and (ssc.coursebalance_figure>0 or ssc.coursebalance_time>0)
				and not exists(select sstu.student_id from smc_student_study as sstu,smc_class as cla,smc_course as cou where sstu.class_id=cla.class_id and cla.course_id=cou.course_id and sstu.class_id='{$request['class_id']}' and sstu.student_id=ssc.student_id and cou.course_inclasstype<>'2')
				group by s.student_id
				HAVING num=0 and ((stuchange_code<>'A07' or (stuchange_code='A07' and course_inclasstype='2')) or stuchange_code is null)
				order by s.student_id desc
				";
        }

        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $data['list'] = $studentList;

        return $data;

    }

    function turnClass($request)
    {
        $list = json_decode(stripslashes($request['list']), true);
        $TransactionModel = new \Model\Smc\TransactionModel($request);


        $sql = "select c.class_fullnums,sc.course_limitnum,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as num
			  from smc_class as c
			  left join smc_course as sc on sc.course_id=c.course_id
			  where c.class_id='{$request['class_id']}'
			  ";

        $classOne = $this->DataControl->selectOne($sql);

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "班级不存在";
            return false;
        }

        if ($classOne['course_limitnum'] == 1) {
            if ((count($list) + $classOne['num']) > $classOne['class_fullnums']) {
                $this->error = true;
                $this->errortip = "入班人数已超过限定人数";
                return false;
            }
        }


        foreach ($list as $val) {
            $TransactionModel->outClass($val['student_id'], $request['class_id'], '2', strtotime($request['create_time']));

            $TransactionModel->transferClass($val['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], 0, '', '', '', strtotime($request['create_time']));
        }

        return true;
    }

    function outClass($request)
    {
        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $bool = $TransactionModel->outClass($request['student_id'], $request['class_id'], '3', strtotime($request['create_time']));
        if ($bool) {

            return true;
        } else {
            $this->error = true;
            $this->errortip = $TransactionModel->errortip;
            return false;
        }
    }

    function classEntryClass($request)
    {

        $Model = new \Model\Smc\TransactionModel($request);
        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $list = json_decode(stripslashes($request['list']), true);
        $tem_array = array();

        $sql = "select c.class_fullnums,sc.course_limitnum,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading=1) as num
			  from smc_class as c
			  left join smc_course as sc on sc.course_id=c.course_id
			  where c.class_id='{$request['class_id']}'
			  ";

        $classOne = $this->DataControl->selectOne($sql);

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "班级不存在";
            return false;
        }

        if ($classOne['course_limitnum'] == 1) {
            if ((count($list) + $classOne['num']) > $classOne['class_fullnums']) {
                $this->error = true;
                $this->errortip = "入班人数已超过限定人数";
                return false;
            }
        }

        foreach ($list as $val) {
            if (!$BalanceModel->checkStuTimes($val['student_id'], $request['class_id'], $val['entrytime'])) {
                $this->error = true;
                $this->errortip = $BalanceModel->errortip;
                return false;
            }
        }

        foreach ($list as $val) {

            $Model->entryClass($val['student_id'], '', $request['class_id'], $val['entrytime'], '', strtotime($request['create_time']), $request['reason_code'], $request['change_reason']);

            $studentOne = $this->DataControl->getOne("smc_student", "student_id='{$val['student_id']}'");
            $data = array();
            $data['student_cnname'] = $studentOne['student_cnname'];
            $data['student_enname'] = $studentOne['student_enname'];
            $data['student_branch'] = $studentOne['student_branch'];
            $data['student_sex'] = $studentOne['student_sex'];
            $data['entrytime'] = $val['entrytime'] ? $val['entrytime'] : date("Y-m-d");
            $data['change_reason'] = $request['change_reason'];

            $tem_array[] = $data;
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = $this->LgStringSwitch("姓名");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = $this->LgStringSwitch("英文名");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = $this->LgStringSwitch("学号");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "student_sex";
        $field[$k]["fieldname"] = $this->LgStringSwitch("性别");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "entrytime";
        $field[$k]["fieldname"] = $this->LgStringSwitch("入班日期");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $field[$k]["fieldstring"] = "change_reason";
        $field[$k]["fieldname"] = $this->LgStringSwitch("入班原因");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 0;
        $k++;

        $data = array();
        $data['field'] = $field;
        $data['list'] = $tem_array;

        return $data;
    }

    //跨校
    function stuCrossSchool($request)
    {
        if ($request['school_id'] == $request['to_school_id']) {
            $this->error = true;
            $this->errortip = "同校不可以操作！";
            return false;
        }
        if ($this->DataControl->selectOne(" select a.apply_id from smc_student_change_apply as a where a.company_id='{$request['company_id']}' and a.from_school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.apply_type = '2' and (a.apply_status = '0' or a.apply_status = '1') ")) {
            $this->error = true;
            $this->errortip = "已存在跨校申请！";
            return false;
        }
        //-------------- 20221122 跨校需要生成申请 ---------
        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['student_id'] = $request['student_id'];
        $data['from_school_id'] = $request['school_id'];
        $data['to_school_id'] = $request['to_school_id'];
        $data['stuchange_code'] = 'F01';
        $data['apply_reason'] = $request['reason'];
        $data['apply_remark'] = $request['remark'];
//        $data['apply_list'] = stripslashes($request['list']);
        $data['apply_type'] = 2;
        $data['apply_status'] = 0;
        $data['from_staffer_id'] = $request['staffer_id'];
        $data['apply_time'] = $request['create_time'];
        $data['apply_createtime'] = time();
        $applydata = $this->DataControl->insertData('smc_student_change_apply', $data);
        // 以下是转校的操作-----------------------

//        $TransactionModel = new \Model\Smc\TransactionModel($request);
//        $TransactionModel->CrossSchool($request['student_id'], $request['to_school_id'], $request['reason_code'], $request['reason'], $request['from_school'], strtotime($request['create_time']));
        return true;
    }

    function getForwardInfo($request)
    {
        $sql = "select sc.course_id,sss.class_id
              from smc_course as sc
              left join smc_student_coursebalance as scb on scb.course_id=sc.course_id
              left join smc_student_courseforward as scf on scf.course_id=sc.course_id
              left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
              left join smc_student_study as sss on sss.student_id=sse.student_id and sss.study_isreading=1
              left join smc_class as c on sc.course_id=c.course_id
              where sc.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and scb.coursebalance_figure>0 and scb.coursebalance_time>0 and c.class_status<>-1 and sse.enrolled_status<>-1 and scb.coursebalance_status<>3
              and sc.course_id not in (select poc.course_id from smc_payfee_order as po
                                        left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid
                                        where po.company_id='{$request['company_id']}' and po.order_status <>4 and po.order_status>0
                                        and po.student_id='{$request['student_id']}')
              group by sc.course_id
              order by scb.coursebalance_createtime DESC
              ";
        $courseList = $this->DataControl->selectClear($sql);
        $balance = 0;
        $forward = 0;
        if ($courseList) {
            foreach ($courseList as $val) {
                $stu_coursebalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure,coursebalance_unitrefund,coursebalance_time,pricing_id", "student_id='{$request['student_id']}' and course_id='{$val['course_id']}' and school_id='{$request['school_id']}'");
                $stu_courseforwardOne = $this->DataControl->getFieldOne("smc_student_courseforward", "courseforward_price", "student_id='{$request['student_id']}' and course_id='{$val['course_id']}'");

                if ($stu_coursebalanceOne['pricing_id'] > 0) {
                    $balance += $stu_coursebalanceOne['coursebalance_figure'];
                    if ($balance < 0) {
                        $balance = 0;
                    }

                    $forward += $stu_courseforwardOne['courseforward_price'];
                } else {
                    $balance += $stu_coursebalanceOne['coursebalance_figure'];
                    $forward += $stu_courseforwardOne['courseforward_price'];
                }
            }
        }

        $list = array();
        $list['balance'] = $balance;
        $list['forward'] = $forward;

        return $list;

    }

    function stuTransferSchool($request)
    {
        $studentOne = $this->DataControl->getFieldOne("smc_student", "from_client_id", "student_id='{$request['student_id']}' ");
        if ($studentOne) {
            $crminvireOne = $this->DataControl->getFieldOne("crm_client_invite", "invite_id", "client_id='{$studentOne['from_client_id']}' and invite_isvisit =0");
            $crmauditionOne = $this->DataControl->getFieldOne("crm_client_audition", "audition_id", "client_id='{$studentOne['from_client_id']}' and  audition_isvisit =0");
        }

        $invireOne = $this->DataControl->getFieldOne("crm_student_invite", "invite_id", "student_id='{$request['student_id']}' and invite_isvisit =0");
        $auditionOne = $this->DataControl->getFieldOne("crm_student_audition", "audition_id", "student_id='{$request['student_id']}' and  audition_isvisit =0");
        if ($invireOne || $crminvireOne) {
            $this->error = true;
            $this->errortip = "该学生CRM有柜询记录未确认";
            return false;
        } elseif ($auditionOne || $crmauditionOne) {
            $this->error = true;
            $this->errortip = "该学生CRM有试听记录未确认";
            return false;
        }

        $sql = "select s.student_id
              ,(select COUNT(po.order_id) from smc_payfee_order as po where  po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<>4 and po.order_status>0) as num
              from smc_student as s
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' limit 0,1";
        $studentPrice = $this->DataControl->selectOne($sql);

        if ($studentPrice['num'] > 0) {
            $this->error = true;
            $this->errortip = "学员欠费不可转校";
            return false;
        }

        $sql = "select st.trading_id
              from smc_student_trading as st
              where st.student_id='{$request['student_id']}' and st.company_id='{$request['company_id']}' and st.school_id='{$request['school_id']}' and st.trading_status=0 limit 0,1";
        $tradingOne = $this->DataControl->selectOne($sql);

        if ($tradingOne) {
            $this->error = true;
            $this->errortip = "学员存在未完成交易不可转校";
            return false;
        }

        $sql = "select scb.course_id
              from smc_student_coursebalance as scb
              where scb.company_id='{$request['company_id']}' and scb.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_time>0 or scb.coursebalance_figure>0) and scb.coursebalance_status='2'
              order by scb.course_id DESC
        ";
        $coursebalanceOne = $this->DataControl->selectOne($sql);

        if ($coursebalanceOne) {
            $this->error = true;
            $this->errortip = "学员有延班课程不可转校";
            return false;
        }

        $sql = "select se.student_id from smc_student_enrolled as se
			  where se.student_id='{$request['student_id']}' and se.school_id='{$request['to_school_id']}' and (se.enrolled_status='0' or se.enrolled_status='1' or se.enrolled_status='3')";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员已存在该学校";
            return false;
        }

        $sql = "select sc.coursecatbalance_id from smc_student_coursecatbalance as sc
				where sc.student_id='{$request['student_id']}' and sc.school_id='{$request['school_id']}' and (sc.coursecatbalance_figure>0 or sc.coursecatbalance_time>0)";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员存在预收不可转校";
            return false;
        }

        $sql = "select sc.itemtimes_id from smc_student_itemtimes as sc
			  where sc.student_id='{$request['student_id']}' and sc.school_id='{$request['school_id']}' and (sc.itemtimes_number>0 or sc.itemtimes_figure>0)";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员存在未使用杂费不可转校";
            return false;
        }

        $sql = "select sc.erpgoods_id from smc_student_erpgoods as sc where sc.student_id='{$request['student_id']}' and sc.school_id='{$request['school_id']}' and sc.erpgoods_isreceive='0' and sc.erpgoods_isrefund='0'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员存在未领用教材不可转校";
            return false;
        }

        $day = date("Y-m-d", time());
        $sql = "select hour_id from smc_class_hour where hour_day<'{$day}' and hour_ischecking=0 and hour_iswarming=0 and class_id in (select class_id from smc_student_study where study_isreading='1' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}')";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "班级考勤未完成！";
            return false;
        }

        $sql = "select study_id from smc_student_study where student_id='{$request['student_id']}' and school_id='{$request['school_id']}' and study_isreading='1'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员存在已入班课程！";
            return false;
        }

        $sql = "select scb.course_id,scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scb.student_id=scf.student_id
              left join smc_course as sc on scb.course_id=sc.course_id
              where sc.company_id='{$request['company_id']}' and scb.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}'
              and (scb.coursebalance_figure>'0' or scb.coursebalance_time>'0' or scf.courseforward_price>'0')
              order by scb.course_id DESC
        ";

        $classList = $this->DataControl->selectClear($sql);
        if ($classList) {
            $this->error = true;
            $this->errortip = "存在未结转课程,请手动结转！";
            return false;
        }
        if ($this->DataControl->selectOne(" select a.apply_id from smc_student_change_apply as a where a.company_id='{$request['company_id']}' and a.from_school_id='{$request['school_id']}' and a.student_id='{$request['student_id']}' and a.apply_type = '1' and (a.apply_status = '0' or a.apply_status = '1') ")) {
            $this->error = true;
            $this->errortip = "已存在转校申请！";
            return false;
        }

        //-------------- 20221122 转校需要生成申请，对方学校审核成功后才可以转移资金、转校等 ---------
        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['student_id'] = $request['student_id'];
        $data['from_school_id'] = $request['school_id'];
        $data['to_school_id'] = $request['to_school_id'];
        $data['stuchange_code'] = 'B05';
        $data['apply_reason'] = $request['reason'];
        $data['apply_remark'] = $request['remark'];
        $data['apply_list'] = stripslashes($request['list']);
        $data['apply_type'] = 1;
        $data['apply_status'] = 0;
        $data['from_staffer_id'] = $request['staffer_id'];
        $data['apply_time'] = $request['create_time'];
        $data['apply_createtime'] = time();
        $apply_id = $this->DataControl->insertData('smc_student_change_apply', $data);
        if ($apply_id) {
            $TransactionModel = new \Model\Smc\TransactionModel($request);
            $BalanceModel = new \Model\Smc\BalanceModel($request);
            $list = json_decode(stripslashes($request['list']), 1);
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_ismanage", " staffer_id='{$request['staffer_id']}'");
            if ($list) {
                $tradpidjson = '';
                foreach ($list as $listOne) {
                    if (!isset($listOne['to_companies_id']) || $listOne['to_companies_id'] == '') {
                        $this->error = true;
                        $this->errortip = "请选择主体！";
                        return false;
                    }
                    $a = $this->DataControl->selectClear("SELECT 
                    cc.companies_issupervise
                    FROM
                    gmc_code_companies AS cc
                    where cc.companies_id = '{$listOne['from_companies_id']}' 
                    and cc.companies_issupervise = '1'");
                    if ($a && $stafferOne['staffer_ismanage'] !== '1') {
                        $this->error = true;
                        $this->errortip = "转出主体已被监管，不可转校！";
                        return false;
                    }

                    $oldStuBalance = $this->DataControl->getFieldOne("smc_student_balance", "student_balance,student_withholdbalance", "school_id='{$this->school_id}' and student_id='{$request['student_id']}' and companies_id='{$listOne['from_companies_id']}'");

                    $oldTrading_pid = '';
                    if ($oldStuBalance['student_balance'] > 0 || $oldStuBalance['student_withholdbalance'] > 0) {
                        //生成交易订单
                        $oldTrading_pid = $TransactionModel->stuTrading($request['student_id'], $this->school_id, 'TransferOut', $listOne['from_companies_id'], strtotime($request['create_time']));
                        //单号拼接
                        if ($tradpidjson == '') {
                            $tradpidjson = $oldTrading_pid;
                        } else {
                            $tradpidjson .= ',' . $oldTrading_pid;
                        }
                        //创建让财务审核的订单  ---  申请时不需要
//                        $TransactionModel->schoolTrade($request['student_id'], $this->school_id, $request['to_school_id'], $oldTrading_pid, '', 0, 1, $oldStuBalance['student_balance'], $oldStuBalance['student_withholdbalance'], $listOne['from_companies_id'], $listOne['to_companies_id'], $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功，请耐心等待财务审核'), $request['remark'], strtotime($request['create_time']));

                        $BalanceModel->consumeStuBalance($request['student_id'], $request['school_id'], $oldTrading_pid, $oldStuBalance['student_balance'], $oldStuBalance['student_withholdbalance'], $listOne['from_companies_id'], $this->LgStringSwitch('资产转移'), $request['remark'], strtotime($request['create_time']));
                    }
                }
            }
            $updata = array();
            $updata['apply_pidjson'] = $tradpidjson;
            $this->DataControl->updateData("smc_student_change_apply", "apply_id = '{$apply_id}' ", $updata);

            return true;
        } else {
            $this->error = true;
            $this->errortip = "申请失败！";
            return false;
        }
        exit;
        // 以下是转校的操作-----------------------

//        $TransactionModel = new \Model\Smc\TransactionModel($request);
//        $BalanceModel = new \Model\Smc\BalanceModel($request);
//
//        $list = json_decode(stripslashes($request['list']), 1);
//
//        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_ismanage", " staffer_id='{$request['staffer_id']}'");
//
//        if ($list) {
//            foreach ($list as $listOne) {
//
//                if (!isset($listOne['to_companies_id']) || $listOne['to_companies_id'] == '') {
//                    $this->error = true;
//                    $this->errortip = "请选择主体！";
//                    return false;
//                }
//
//                $a = $this->DataControl->selectClear("SELECT
//                    cc.companies_issupervise
//                    FROM
//                    gmc_code_companies AS cc
//                    where cc.companies_id = '{$listOne['from_companies_id']}'
//                    and cc.companies_issupervise = '1'");
//
////                $b = $this->DataControl->selectClear("SELECT
////                    cc.companies_issupervise
////                FROM
////                    gmc_code_companies AS cc
////                    where cc.companies_id = '{$listOne['to_companies_id']}' and cc.companies_issupervise = '1'");
//
//                if ($a && $stafferOne['staffer_ismanage'] !== '1') {
//                    $this->error = true;
//                    $this->errortip = "转出主体已被监管，不可转校！";
//                    return false;
//                }
//
//
//                $oldStuBalance = $this->DataControl->getFieldOne("smc_student_balance", "student_balance,student_withholdbalance", "school_id='{$this->school_id}' and student_id='{$request['student_id']}' and companies_id='{$listOne['from_companies_id']}'");
//
//                $oldTrading_pid = '';
//                if ($oldStuBalance['student_balance'] > 0 || $oldStuBalance['student_withholdbalance'] > 0) {
//                    $oldTrading_pid = $TransactionModel->stuTrading($request['student_id'], $this->school_id, 'TransferOut', $listOne['from_companies_id'], strtotime($request['create_time']));
//
//                    $TransactionModel->schoolTrade($request['student_id'], $this->school_id, $request['to_school_id'], $oldTrading_pid, '', 0, 1, $oldStuBalance['student_balance'], $oldStuBalance['student_withholdbalance'], $listOne['from_companies_id'], $listOne['to_companies_id'], $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功，请耐心等待财务审核'), $request['remark'], strtotime($request['create_time']));
//
//                    $BalanceModel->consumeStuBalance($request['student_id'], $request['school_id'], $oldTrading_pid, $oldStuBalance['student_balance'], $oldStuBalance['student_withholdbalance'], $listOne['from_companies_id'], $this->LgStringSwitch('资产转移'), $request['remark'], strtotime($request['create_time']));
//                }
//            }
//        }
//        $TransactionModel->TransferToSchool($request['student_id'], $request['to_school_id'], $oldTrading_pid, $request['reason_code'], $request['reason'], strtotime($request['create_time']));
//        return true;
    }

    //转校 和 跨校 中待审核名单列表
    function getSchChangeApplyApi($request)
    {
        $PostOne = $this->DataControl->selectOne(" SELECT p.post_istopjob
                FROM gmc_staffer_postbe AS sp
                LEFT JOIN gmc_company_post AS p ON p.post_id = sp.post_id
                WHERE sp.postbe_id = '{$request['re_postbe_id']}'");
        if (!$PostOne || $PostOne['post_istopjob'] != '1') {
            $result = array();
            $result['list'] = [];
            return $result;
        }
        $staffOne = $this->DataControl->selectOne("select staffer_ismanage from smc_staffer where company_id = '{$request['company_id']}' and staffer_id = '{$request['staffer_id']}' limit 0,1");

        $sql = "select h.school_shortname,s.student_cnname,s.student_enname,s.student_branch,a.stuchange_code,a.apply_reason,
                a.apply_remark,a.apply_time,a.apply_createtime,a.apply_pidjson,a.apply_type,a.apply_id,
                (select g.stuchange_name from smc_code_stuchange as g where g.stuchange_code = a.stuchange_code) as stuchange_name 
				from smc_student_change_apply as a 
				left join smc_student as s ON a.student_id = s.student_id 
				left join smc_school as h ON a.from_school_id = h.school_id 
 				where  a.company_id ='{$request['company_id']}' and a.to_school_id = '{$request['school_id']}' and a.apply_status ='0' ";
        $applylist = $this->DataControl->selectClear($sql);
        if ($applylist) {
            foreach ($applylist as &$applyvar) {
                $allprice = '';
                if ($applyvar['apply_pidjson'] && $applyvar['apply_type'] == '1') {
                    $pidarray = explode(',', $applyvar['apply_pidjson']);
                    foreach ($pidarray as $pidvar) {
                        $StuBalanceArray = $this->DataControl->selectClear(" select balancelog_class,balancelog_playamount from smc_student_balancelog where trading_pid = '{$pidvar}' ");
                        if ($StuBalanceArray) {
                            foreach ($StuBalanceArray as $StuBalancevar) {
                                $allprice += $StuBalancevar['balancelog_playamount'];
                            }
                        }
                    }
                }

                $applyvar['student_name'] = $applyvar['student_cnname'] . ($applyvar['student_enname'] ? '/' . $applyvar['student_enname'] : '');
                $applyvar['apply_createtime'] = date("Y-m-d H:i:s", $applyvar['apply_createtime']);
                $applyvar['allprice'] = $allprice > 0 ? $allprice : '--';


            }
        }
        //来源几个学校
        $schoolall = $this->DataControl->selectClear(" select apply_id from smc_student_change_apply as a where a.company_id ='{$request['company_id']}' and a.to_school_id = '{$request['school_id']}' and a.apply_status ='0' group by a.from_school_id ");
        //有几个学生
        $studentall = $this->DataControl->selectClear(" select apply_id from smc_student_change_apply as a where a.company_id ='{$request['company_id']}' and a.to_school_id = '{$request['school_id']}' and a.apply_status ='0' group by a.student_id ");

        $result = array();
        $result['list'] = $applylist;
        $result['schoolnum'] = $schoolall ? count($schoolall) : 0;
        $result['studentnum'] = $studentall ? count($studentall) : 0;
        $result['staffer_ismanage'] = $staffOne['staffer_ismanage'] ? $staffOne['staffer_ismanage'] : 0;
        return $result;
    }

    //同意
    function updateAgreeChangeApplyAction($request)
    {
        $applyOne = $this->DataControl->selectOne(" select a.*,(select g.stuchange_name from smc_code_stuchange as g where g.stuchange_code = a.stuchange_code) as stuchange_name  from smc_student_change_apply as a where a.apply_id = '{$request['apply_id']}' and a.apply_status = '0' ");
        if (!$applyOne) {
            $this->error = 1;
            $this->errortip = "不存在待审核申请单！";
            return false;
        }

        $data = array();
        $data['apply_status'] = 1;
        $data['staffer_id'] = $request['staffer_id'];
        $data['apply_refusereson'] = $request['apply_refusereson'];
        $data['apply_refusetime'] = time();
        $data['apply_updatetime'] = time();
        if ($this->DataControl->updateData("smc_student_change_apply", "apply_id = '{$request['apply_id']}'", $data)) {

            if ($applyOne['apply_type'] == '2') {//跨校

                $TransactionModel = new \Model\Smc\TransactionModel($request);
                $TransactionModel->CrossSchool($applyOne['student_id'], $applyOne['to_school_id'], '0100', $applyOne['apply_reason'], $applyOne['from_school_id'], strtotime($applyOne['apply_time']));

                $this->error = 0;
                $this->errortip = "操作成功！";
                return true;
            } elseif ($applyOne['apply_type'] == '1') {//转校
                //如果转进来的学校有分配，自动取消跟踪
                $principalOne = $this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$applyOne['student_id']}' and school_id='{$applyOne['from_school_id']}' and principal_leave =0 ");
                if ($principalOne) {
                    //取消原学校的跟踪负责人
                    $StudentModel = new \Model\Crm\StudentActionModel($request);
                    $StudentModel->CancelStuTrackAction($applyOne['student_id'], $applyOne['from_school_id']);
                    //生成跟踪记录
                    $trackData = array();
                    $trackData['track_isactive'] = 0;
                    $trackData['track_note'] = "学生转校，原学校存在负责人，系统自动做取消跟踪操作！";
                    $trackData['track_state'] = -1;
                    $trackData['track_linktype'] = '取消跟踪';
                    $StudentModel->insertStudentTrackAction($applyOne['student_id'], 15, $trackData, $applyOne['from_school_id']);
                }

                //处理同意后的资金流动，学生移动
                $TransactionModel = new \Model\Smc\TransactionModel($request);
                $BalanceModel = new \Model\Smc\BalanceModel($request);

                $list = json_decode(stripslashes($applyOne['apply_list']), 1);
                if ($list) {
                    foreach ($list as $listOne) {
                        if (!isset($listOne['to_companies_id']) || $listOne['to_companies_id'] == '') {
                            $this->error = 1;
                            $this->errortip = "请选择主体！";
                            return false;
                        }
                        $a = $this->DataControl->selectClear("SELECT 
                cc.companies_issupervise
                FROM
                gmc_code_companies AS cc
                where cc.companies_id = '{$listOne['from_companies_id']}' 
                and cc.companies_issupervise = '1'");

                        if ($a) {
                            $this->error = 1;
                            $this->errortip = "转出主体已被监管，不可转校！";
                            return false;
                        }

                        $oldTrading_pid = '';
                        if ($applyOne['apply_pidjson']) {
                            $pidarray = explode(',', $applyOne['apply_pidjson']);
                            foreach ($pidarray as $pidvar) {
                                //记录一个pid，方便转校
                                $oldTrading_pid = $pidvar;
                                $StuBalanceArray = $this->DataControl->selectClear(" select balancelog_class,balancelog_playamount from smc_student_balancelog where trading_pid = '{$pidvar}' ");
                                if ($StuBalanceArray) {
                                    foreach ($StuBalanceArray as $StuBalancevar) {
                                        if ($StuBalancevar['balancelog_playamount'] > 0) {
                                            //余额转移
                                            if ($StuBalancevar['balancelog_class'] == '0') {

                                                $TransactionModel->schoolTrade($applyOne['student_id'], $applyOne['from_school_id'], $applyOne['to_school_id'], $pidvar, '', 0, 1, $StuBalancevar['balancelog_playamount'], 0, $listOne['from_companies_id'], $listOne['to_companies_id'], $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功，请耐心等待财务审核'), $applyOne['apply_remark'], strtotime($applyOne['apply_time']));

                                            } elseif ($StuBalancevar['balancelog_class'] == '2') {

                                                $TransactionModel->schoolTrade($applyOne['student_id'], $applyOne['from_school_id'], $applyOne['to_school_id'], $pidvar, '', 0, 1, 0, $StuBalancevar['balancelog_playamount'], $listOne['from_companies_id'], $listOne['to_companies_id'], $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功，请耐心等待财务审核'), $applyOne['apply_remark'], strtotime($applyOne['apply_time']));

                                            }
                                        }
                                    }
                                }

                            }
                        }

                    }
                }
                //学生异动到对应的学校
                $TransactionModel->TransferToSchool($applyOne['student_id'], $applyOne['from_school_id'], $applyOne['to_school_id'], $oldTrading_pid, $applyOne['stuchange_name'], $applyOne['apply_reason'], strtotime($applyOne['apply_time']));

                $this->error = 0;
                $this->errortip = "操作成功！";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "操作失败！";
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = "操作失败！";
            return false;
        }
    }

    //拒绝
    function updateRefuseChangeApplyAction($request)
    {
        $applyOne = $this->DataControl->selectOne(" select * from smc_student_change_apply where apply_id = '{$request['apply_id']}' and apply_status = '0' ");
        if (!$applyOne) {
            $this->error = 1;
            $this->errortip = "不存在待审核申请单！";
            return false;
        }

        $data = array();
        $data['apply_status'] = '-1';
        $data['staffer_id'] = $request['staffer_id'];
        $data['apply_refusereson'] = $request['remark'];
        $data['apply_refusetime'] = time();
        $data['apply_updatetime'] = time();
        if ($this->DataControl->updateData("smc_student_change_apply", "apply_id = '{$request['apply_id']}'", $data)) {

            if ($applyOne['apply_type'] == '1') {//转校 -- 跨校不用

                $TransactionModel = new \Model\Smc\TransactionModel($request);
                $BalanceModel = new \Model\Smc\BalanceModel($request);

                $list = json_decode(stripslashes($applyOne['apply_list']), 1);
                if ($list) {
                    foreach ($list as $listOne) {
                        if (!isset($listOne['to_companies_id']) || $listOne['to_companies_id'] == '') {
                            $this->error = 1;
                            $this->errortip = "请选择主体！";
                            return false;
                        }
                        $a = $this->DataControl->selectClear("SELECT 
                    cc.companies_issupervise
                    FROM
                    gmc_code_companies AS cc
                    where cc.companies_id = '{$listOne['from_companies_id']}' 
                    and cc.companies_issupervise = '1'");

                        if ($a) {
                            $this->error = 1;
                            $this->errortip = "转出主体已被监管，不可转校！";
                            return false;
                        }

                        if ($applyOne['apply_pidjson']) {
                            $pidarray = explode(',', $applyOne['apply_pidjson']);
                            foreach ($pidarray as $pidvar) {
                                $StuBalanceArray = $this->DataControl->selectClear(" select balancelog_class,balancelog_playamount from smc_student_balancelog where trading_pid = '{$pidvar}' ");
                                if ($StuBalanceArray) {
                                    foreach ($StuBalanceArray as $StuBalancevar) {
                                        if ($StuBalancevar['balancelog_playamount'] > 0) {

                                            //生成交易订单
                                            $oldTrading_pid = $TransactionModel->stuTrading($applyOne['student_id'], $applyOne['from_school_id'], 'TransferOut', $listOne['from_companies_id'], time());

                                            //余额转移
                                            if ($StuBalancevar['balancelog_class'] == '0') {

                                                $BalanceModel->consumeStuBalance($applyOne['student_id'], $applyOne['from_school_id'], $oldTrading_pid, $StuBalancevar['balancelog_playamount'], 0, $listOne['from_companies_id'], $this->LgStringSwitch('资产转移'), $request['remark'], time(), '+', $pidvar);

                                            } elseif ($StuBalancevar['balancelog_class'] == '2') {

                                                $BalanceModel->consumeStuBalance($applyOne['student_id'], $applyOne['from_school_id'], $oldTrading_pid, 0, $StuBalancevar['balancelog_playamount'], $listOne['from_companies_id'], $this->LgStringSwitch('资产转移'), $request['remark'], time(), '+', $pidvar);

                                            }

                                        }
                                    }
                                }

                            }
                        }

                    }
                }
            }

            $this->error = 0;
            $this->errortip = "操作成功！";
            return false;
        } else {
            $this->error = 1;
            $this->errortip = "操作失败！";
            return false;
        }
    }

    //异动申请记录
    function getChangeApplyApi($request)
    {
        $datawhere = " a.company_id ='{$request['company_id']}' and a.from_school_id = '{$request['school_id']}' ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or s.student_enname like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and a.apply_time>='{$request['start_time']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and a.apply_time<='{$request['end_time']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and a.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['apply_status']) && $request['apply_status'] !== '') {
            $datawhere .= " and a.apply_status ='{$request['apply_status']}'";
        }


        $sql = "select s.student_cnname,s.student_enname,s.student_branch,a.stuchange_code,a.apply_status,a.apply_reason,a.apply_remark,a.apply_time,a.apply_refusereson,a.from_staffer_id,
                f.staffer_cnname,f.staffer_enname
                ,(select g.stuchange_name from smc_code_stuchange as g where g.stuchange_code = a.stuchange_code) as stuchange_name 
                ,(select concat(t.staffer_cnname,(CASE WHEN ifnull(t.staffer_enname,'') = '' THEN '' ELSE concat( '/',t.staffer_enname) END) )  from smc_staffer as t where t.staffer_id = a.staffer_id) as to_staffer_name
				from smc_student_change_apply as a 
				left join smc_student as s ON a.student_id = s.student_id 
				left join smc_staffer as f ON a.from_staffer_id = f.staffer_id 
 				where {$datawhere} 
 				order by a.apply_time desc,a.apply_id desc ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    if ($dateexcelvar['apply_status'] == '0') {
                        $datearray['apply_status_name'] = $this->LgStringSwitch('待确认');
                    } elseif ($dateexcelvar['apply_status'] == '1') {
                        $datearray['apply_status_name'] = $this->LgStringSwitch('已同意');
                    } elseif ($dateexcelvar['apply_status'] == '-1') {
                        $datearray['apply_status_name'] = $this->LgStringSwitch('已拒绝');
                    }
                    $datearray['apply_reason'] = $dateexcelvar['apply_reason'];
                    $datearray['apply_time'] = $dateexcelvar['apply_time'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_cnname'] . ($dateexcelvar['staffer_enname'] ? '-' . $dateexcelvar['staffer_enname'] : '');
                    $datearray['apply_refusereson'] = $dateexcelvar['apply_refusereson'] ? $dateexcelvar['apply_refusereson'] : '--';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "异动类型", "异动状态", "异动描述", "异动日期", "执行人", "操作"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'stuchange_name', 'apply_status_name', 'apply_reason', 'apply_time', 'staffer_name', 'apply_refusereson');
            $tem_name = $this->LgStringSwitch('学员异动申请记录管理表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $changeList = $this->DataControl->selectClear($sql);

            if (!$changeList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($changeList as &$changevar) {
                $changevar['stuchange_name'] = $changevar['stuchange_name'];
                if ($changevar['apply_status'] == '0') {
                    $changevar['apply_status_name'] = $this->LgStringSwitch('待确认');
                } elseif ($changevar['apply_status'] == '1') {
                    $changevar['apply_status_name'] = $this->LgStringSwitch('已同意');
                } elseif ($changevar['apply_status'] == '-1') {
                    $changevar['apply_status_name'] = $this->LgStringSwitch('已拒绝');
                }
                $changevar['staffer_name'] = $changevar['staffer_cnname'] . ($changevar['staffer_enname'] ? '-' . $changevar['staffer_enname'] : '');
                $changevar['apply_refusereson'] = $changevar['apply_refusereson'] ? $changevar['apply_refusereson'] : '--';
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.apply_id 
				from smc_student_change_apply as a 
				left join smc_student as s ON a.student_id = s.student_id 
				left join smc_staffer as f ON a.from_staffer_id = f.staffer_id 
 				where {$datawhere} ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $changeList;
            return $data;
        }
    }


    function getNewclassOne($request)
    {
        $sql = "select c.class_id,c.class_cnname,class_branch,ce.course_classnum,ce.course_branch
 				(select  count(sh.hour_id) from  smc_class_hour as sh where  sh.class_id =c.class_id and sh.course_id = c.course_id and sh.hour_ischecking =1  ) as hour_checkingnum
				from smc_class as c
				left join smc_course  as ce On c.course_id = c.course_id
 				where  c.company_id ='{$request['company_id']}' and  c.school_id = '{$request['school_id']}'  and c.class_id ='{$request['class_id']}'";
        $classOne = $this->DataControl->selectOne($sql);

        $now = time();
        $sql = "select fp.pricing_id,fp.course_id,fp.agreement_id,fp.pricing_applytype
              from smc_fee_pricing as fp
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              where fa.agreement_startday<='{$now}' and fa.agreement_endday>='{$now}' and fa.company_id='{$request['company_id']}' and fa.agreement_status=1 and
             fp.course_id ='{$request['school_id']}' ";

        $pricingOne = $this->DataControl->selectClear($sql);
        $temArray = array();
        if ($classOne && $pricingOne) {

            $applyOne = $this->DataControl->selectOne("select * from smc_fee_pricing_apply where pricing_id='{$pricingOne['pricing_id']}' and school_id='{$request['school_id']}' limit 0,1");
            $data = array();

            if ($pricingOne['pricing_applytype'] == 0) {
                $data['agreement_id'] = $pricingOne['agreement_id'];
                $data['pricing_id'] = $pricingOne['pricing_id'];
                $data['course_id'] = $pricingOne['course_id'];
                $temArray = $data;
            } elseif ($pricingOne['pricing_applytype'] == 1) {
                if ($applyOne) {
                    $data['agreement_id'] = $pricingOne['agreement_id'];
                    $data['pricing_id'] = $pricingOne['pricing_id'];
                    $data['course_id'] = $pricingOne['course_id'];
                    $temArray = $data;
                }
            } elseif ($pricingOne['pricing_applytype'] == -1) {
                if (!$applyOne) {
                    $data['agreement_id'] = $pricingOne['agreement_id'];
                    $data['pricing_id'] = $pricingOne['pricing_id'];
                    $data['course_id'] = $pricingOne['course_id'];
                    $temArray = $data;
                }
            }
        }

        $coursepircing = $this->DataControl->selectOne("select fpt.tuition_originalprice,fpt.tuition_sellingprice from smc_fee_pricing_tuition as fpt where fpt.course_id ='{$temArray['course_id']}' and  fpt.pricing_id ='{$temArray['pricing_id']}' ");

        $BalanceModel = new BalanceModel($this->publicarray);
        $surplusCourse = $BalanceModel->surplusCourse($temArray['pricing_id'], $temArray['course_id'], $request['class_id'], '');
        $data = array();
        $data['class_cnname'] = $classOne['class_cnname'];
        $data['course_branch'] = $classOne['course_branch'];
        $data['num'] = $classOne['hour_checkingnum'] . '/' . $classOne['course_classnum'];
        $data['unfihish_num'] = $classOne['course_classnum'] - $classOne['hour_checkingnum'];
        $data['tuition_sellingprice'] = $coursepircing['tuition_sellingprice'];
        $data['course_price'] = $surplusCourse['surplusNum'] * ($coursepircing['tuition_sellingprice'] / $classOne['course_classnum']);
        $data['type'] = $this->LgStringSwitch("老生续费");

        return $data;


    }

    function getStuClassList($request)
    {
        $sql = "select c.class_id,c.course_id,ssc.pricing_id,c.class_cnname,c.class_branch,sc.course_branch,ssc.coursebalance_time,ssc.coursebalance_figure,scf.courseforward_price
			  from smc_student_study as ss
			  left join smc_class as c on c.class_id=ss.class_id
			  left join smc_student_coursebalance as ssc on ssc.student_id=ss.student_id and ssc.course_id=c.course_id
			  left join smc_student_courseforward as scf on scf.student_id=ss.student_id and scf.course_id=c.course_id
			  left join smc_course as sc on sc.course_id=c.course_id
			  WHERE ss.study_isreading='1' and c.school_id='{$request['school_id']}' and ss.student_id='{$request['student_id']}' and c.company_id='{$request['company_id']}'
              and ssc.coursebalance_status<>'3' and ssc.coursebalance_status<>'4' and c.class_type='0' and sc.course_inclasstype<>'1'
              group by ss.study_id
              order by ss.study_beginday desc
			   ";
        $classList = $this->DataControl->selectClear($sql);

        return $classList;
    }

    function getNewClassList($request)
    {
        $now = date("Y-m-d", time());

        $datawhere = " 1 ";

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and sc.class_id <> '{$request['class_id']}'";
        }

        if (isset($request['to_class_id']) && $request['to_class_id'] !== '') {
            $datawhere .= " and sc.class_id = '{$request['to_class_id']}'";
        }

        if (isset($request['student_id']) && $request['student_id'] > '0') {
            $datawhere .= " and sc.class_id not in (select ssc.class_id from smc_student_study as ssc where ssc.student_id='{$request['student_id']}' and ssc.school_id='{$request['school_id']}' and ssc.company_id='{$request['company_id']}' and ssc.study_isreading='1')";

            $datawhere .= " and sc.class_id not in (select clt.class_id from (select ss.class_id,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ss.class_id and sch.hour_day>ss.study_endday and sch.hour_ischecking='1') as hour_num from smc_student_study as ss where ss.student_id='{$request['student_id']}' and ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.study_isreading<>'1' having hour_num>'0') as clt)";
        }

//		$datawhere .=" and c.course_id not in (select scb.course_id from smc_student_coursebalance as scb where scb.company_id='{$request['company_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_figure>0 or scb.coursebalance_time>0))";
        $classOne = $this->DataControl->selectOne("select c.coursetype_id,c.course_inclasstype,c.course_id
from smc_course as c
left join smc_class as sc on sc.course_id=c.course_id
where sc.class_id='{$request['class_id']}' and sc.company_id='{$request['company_id']}' and sc.school_id='{$request['school_id']}' limit 0,1");

        if ($classOne['course_inclasstype'] == '2') {
            $datawhere .= " and c.course_id='{$classOne['course_id']}'";
        } else {
            $datawhere .= " and c.course_inclasstype<>'2'";
        }

        $sql = "SELECT
                    t.course_id,
                    p.pricing_id,
                    t.tuition_originalprice,
                    t.tuition_sellingprice,
                    t.tuition_buypiece,
                    t.tuition_unitprice,
                    a.agreement_id,
                    sc.class_id,
                    c.course_id,
                    sc.class_cnname,
                    sc.class_branch,
                    c.course_cnname,
                    c.course_branch
					,(select COUNT(ch.hour_id) from smc_class_hour as ch where ch.class_id=sc.class_id and ch.hour_ischecking='1' and ch.hour_isfree='0') as hournum
					,c.course_classnum
					,c.course_nextid
					,c.course_inclasstype
					,(select count(ssch.hour_id) from smc_class_hour as ssch where ssch.class_id=sc.class_id and ssch.hour_ischecking<>'-1' and ssch.hour_isfree='0') as hour_allnum
                FROM
                    smc_fee_pricing_tuition AS t,
                    smc_fee_pricing AS p,
                    smc_fee_agreement AS a,
                    smc_class as sc,
                    smc_course as c
                WHERE {$datawhere}
                AND t.pricing_id = p.pricing_id
                AND p.agreement_id = a.agreement_id
				AND sc.course_id=t.course_id
				AND c.course_id=sc.course_id
				AND c.coursetype_id='{$classOne['coursetype_id']}'
                AND (
                    (
                        p.pricing_applytype = '1'
                        AND p.pricing_id IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$request['school_id']}'
                        )
                    )
                    OR (
                        p.pricing_applytype = '-1'
                        AND p.pricing_id NOT IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$request['school_id']}'
                        )
                    )
                    OR (p.pricing_applytype = '0')
                )
                AND a.agreement_startday <= '{$now}'
                AND a.agreement_endday >= '{$now}'
                AND a.agreement_status = '1'
                AND a.company_id = '{$request['company_id']}'
                and sc.school_id='{$request['school_id']}'
                and sc.class_status >= '0'
                and sc.class_type='0' and c.course_inclasstype<>'1'
                and c.course_status<>'-1'
                GROUP BY
                    sc.class_id ORDER BY sc.class_id DESC";
        $pricingList = $this->DataControl->selectClear($sql);

        if ($pricingList) {
            foreach ($pricingList as &$pricingOne) {
//				if($pricingOne['course_inclasstype']=='2'){
//					$pricingOne['classInfo']=$pricingOne['hournum'].'/'.$pricingOne['hour_allnum'];
//					$pricingOne['nonum']=$pricingOne['hour_allnum']-$pricingOne['hournum'];
//				}else{
//					$pricingOne['classInfo']=$pricingOne['hournum'].'/'.$pricingOne['hour_allnum'];
//					$pricingOne['nonum']=$pricingOne['hour_allnum']-$pricingOne['hournum'];
//				}

                $pricingOne['classInfo'] = $pricingOne['hournum'] . '/' . $pricingOne['hour_allnum'];
                $pricingOne['nonum'] = $pricingOne['hour_allnum'] - $pricingOne['hournum'];
                $pricingOne['unitsellprice'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['course_classnum']);
            }
            if ($classOne['course_inclasstype'] != '2') {
                if (isset($request['is_buy_next']) && $request['is_buy_next'] == 1) {
                    $nextclassOne = $this->DataControl->selectOne("select c.course_nextid from smc_course as c left join smc_class as sc on sc.course_id=c.course_id where sc.class_id='{$request['to_class_id']}' and sc.company_id='{$request['company_id']}' and sc.school_id='{$request['school_id']}' limit 0,1");
                    $relClassOne = $this->DataControl->selectOne("select course_nextid,course_id,course_cnname,course_presentednums,course_classnum,course_branch from smc_course where company_id='{$request['company_id']}' and course_id='{$nextclassOne['course_nextid']}' limit 0,1");

                    if ($nextclassOne) {
                        $next_pricingOne = $this->getCoursePricing($nextclassOne['course_nextid'], $request['company_id'], $request['school_id']);

                        if ($next_pricingOne) {
                            $tem_data = array();
                            $tem_data['pricing_id'] = $next_pricingOne['pricing_id'];
                            $tem_data['course_id'] = $relClassOne['course_id'];
                            $tem_data['class_id'] = '--';
                            $tem_data['class_cnname'] = '--';
                            $tem_data['class_branch'] = '--';
                            $tem_data['course_nextid'] = '0';
//							$tem_data['course_cnname']=$relClassOne['course_cnname'];
                            $tem_data['course_branch'] = $relClassOne['course_branch'];
                            $tem_data['classInfo'] = '0/' . $relClassOne['course_classnum'];
                            $tem_data['nonum'] = $relClassOne['course_classnum'];
                            $tem_data['tuition_originalprice'] = $next_pricingOne['tuition_originalprice'];
                            $tem_data['tuition_sellingprice'] = $next_pricingOne['tuition_sellingprice'];
                            $tem_data['unitsellprice'] = ceil($next_pricingOne['tuition_sellingprice'] / $relClassOne['course_classnum']);
                            $pricingList[] = $tem_data;
                        }
                    }
                }
            }


            return $pricingList;

        } else {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
    }

    function createOrderData($student_id,$course_id,$pricing_id,$class_id,$surplusToCourse){

        $playamount = $surplusToCourse['allPrice'];

        $courseArray = array();

        $policyOne = $this->getStuDiscountPrice($this->publicarray, $student_id, $course_id);
        
        if ($policyOne) {
            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$course_id}' and company_id='{$this->company_id}'");

            if ($courseOne['course_classnum'] <= $surplusToCourse['surplusNum']) {
                $playamount = $policyOne['full_price'];
            } else {
                $playamount = $surplusToCourse['surplusNum'] * $policyOne['unitexpend'];
            }
        }

        $all_price = $playamount;

        $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
			  from smc_fee_pricing_products as fpp
			  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
			  left join erp_goods as g on g.goods_id=fpp.goods_id
			  where fpp.pricing_id='{$pricing_id}' and g.company_id='{$this->company_id}' and fpp.products_ismustbuy='1'
			  ";

        $goodsList = $this->DataControl->selectClear($goods_sql);
        if (!$goodsList) {
            $goodsList = array();
        } else {
            foreach ($goodsList as $val) {
                if ($val['isfree'] != 1) {
                    $all_price += $val['sellingprice'];
                }
            }
        }

        $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
			  from smc_fee_pricing_items as fpi
			  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
			  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
			  where fpi.pricing_id='{$pricing_id}' and f.company_id='{$this->company_id}' and fpi.items_ismustbuy='1'
			  group by fpi.feeitem_branch
			  ";
        $itemsList = $this->DataControl->selectClear($item_sql);

        if (!$itemsList) {
            $itemsList = array();
        } else {
            foreach ($itemsList as $itemsOne) {
                if ($itemsOne['isfree'] != 1) {
                    $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                }
            }
        }

        $courseData = array();
        $courseData['agreement_id'] = $surplusToCourse['agreement_id'];
        $courseData['pricing_id'] = $pricing_id;
        $courseData['course_id'] = $course_id;
        $courseData['sellingprice'] = $playamount;
        $courseData['num'] = $surplusToCourse['surplusNum'];
        $courseData['class_id'] = $class_id;
        if ($policyOne) {
            $courseData['is_discount'] = 1;
            $courseData['policy_id'] = $policyOne['policy_id'];
        } else {
            $courseData['is_discount'] = 0;
        }

        $courseData['starttime'] = "";
        $courseData['discount_id'] = "";
        $courseData['market_price'] = "";
        $courseData['deductionmethod'] = 2;
        $courseData['from'] = 1;
        $courseData['goodsList'] = $goodsList;
        $courseData['itemsList'] = $itemsList;
        $courseData['couponList'] = json_decode(stripslashes($request['to_couponList']), 1);
        if (isset($courseData['couponList']) && $courseData['couponList'] != '') {
            foreach ($courseData['couponList'] as $toOne) {
                if ($toOne['coupon_price'] > 0) {
                    $data['is_forward'] = 0;
                    $all_price -= $toOne['coupon_price'];
                    $courseData['sellingprice'] -= $toOne['coupon_price'];
                }
            }
        }

        return $courseData;

    }

    function transferClass($request)
    {

        if ($request['pattern'] == 0) {
            $this->error = true;
            $this->errortip = "课次结转功能已关闭";
            return false;
        }

        $courseList=json_decode(stripslashes($request['list']), 1);

        if(!$courseList){
            $this->error = true;
            $this->errortip = "请选择结算内容";
            return false;
        }

        $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);

        $surplusCourse = $BalanceModel->surplusCourse($request['pricing_id'], $request['course_id'], $request['class_id'], $request['create_time']);

        $surplusToCourse = $BalanceModel->surplusCourse($request['to_pricing_id'], $request['to_course_id'], $request['to_class_id'], $request['create_time']);
        if ($surplusToCourse['surplusNum'] <= 0) {
            $this->error = true;
            $this->errortip = "转入班级无后续课程！";
            return false;
        }

        $courseArray=array();
        $comArray=array();

        foreach($courseList as $courseOne){
            $pricingOne = $this->getCoursePricing($courseOne['to_course_id'], $this->company_id, $this->school_id);
            $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0,$courseOne['to_course_id']);

            $data=array();
            $data['pricing_id']=$courseOne['to_pricing_id'];
            $data['course_id']=$courseOne['to_course_id'];
            $data['class_id']=$courseOne['to_class_id']=='--'?0:$courseOne['to_class_id'];
            $data['companies_id']=$companiesOne['companies_id'];
            $data['agreement_id']=$pricingOne['agreement_id'];
            $data['num']=$surplusToCourse['surplusNum'];
            if($data['class_id']>0){
                $data['couponList']=$request['to_couponList'];
            }else{
                $data['couponList']=$request['next_couponList'];
            }

            $courseArray[]=$data;
            $comArray[$companiesOne['companies_id']][]=$data;

            $sql = "select a.course_id,a.course_isforward 
                    from smc_course as a 
                    where a.company_id='{$this->company_id}' and a.course_isfollow=1 and a.main_course_id='{$courseOne['to_course_id']}'
                    ";
            $followCourseOne=$this->DataControl->selectOne($sql);
            if($followCourseOne){

                $pricingOne = $this->getCoursePricing($followCourseOne['course_id'], $this->company_id, $this->school_id);
                if($pricingOne){

                    $companiesOne = $this->getSchoolCourseCompanies($this->school_id, 0,$followCourseOne['course_id']);


                    $data=array();
                    $data['pricing_id']=$pricingOne['pricing_id'];
                    $data['course_id']=$followCourseOne['course_id'];
//                    $data['class_id']=$courseOne['to_class_id']=='--'?0:$courseOne['to_class_id'];
                    $data['companies_id']=$companiesOne['companies_id'];
                    $data['agreement_id']=$pricingOne['agreement_id'];
                    $data['num']=$pricingOne['tuition_buypiece'];

                    $courseArray[]=$data;
                    $comArray[$companiesOne['companies_id']][]=$data;
                }
            }
        }

        $check_course_array=array_column($courseArray,'course_id');

        $sql = "select po.order_pid
			  from smc_payfee_order as po
			  left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
			  where poc.course_id in (".implode(',',$check_course_array).") and po.student_id='{$request['student_id']}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
			  and po.order_status>=0
			  ";

        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "课程存在欠费,不可转班";
            return false;
        }

        if ($request['create_time']) {
            $now = $request['create_time'];
        } else {
            $now = date("Y-m-d", time());
        }

        $where = " 1 ";
        $sql = "select cl.changelog_day
                  from smc_student_changelog as cl,smc_code_stuchange as cs 
                  where cl.stuchange_code=cs.stuchange_code and cs.stuchange_type=0 and stustatus_isenclass=1 and cl.student_id='{$request['student_id']}' and cl.class_id='{$request['class_id']}'
                  order by cl.changelog_day desc,cl.changelog_id desc limit 0,1";

        $startLogOne = $this->DataControl->selectOne($sql);
        if ($startLogOne) {
            $where .= " and hour_day>='{$startLogOne['changelog_day']}'";
        } else {
            $startLogOne = $this->DataControl->getFieldOne("smc_student_study", "study_beginday", "student_id='{$request['student_id']}' and class_id='{$request['class_id']}'");

            if ($startLogOne) {
                $where .= " and hour_day>='{$startLogOne['study_beginday']}'";
            }
        }

        $sql = "select hour_id 
                from smc_class_hour 
                where {$where} and class_id='{$request['class_id']}' and hour_day<'{$now}' and hour_ischecking=0 and hour_iswarming=0";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "班级考勤未完成！";
            return false;
        }

        if ($surplusCourse['course_inclasstype'] == 2 && $surplusToCourse['course_inclasstype'] == 2) {
            $TransactionModel = new \Model\Smc\TransactionModel($request);

            $bool = $TransactionModel->outClass($request['student_id'], $request['class_id'], 2, strtotime($request['create_time']));
            if (!$bool) {
                $this->error = true;
                $this->errortip = $TransactionModel->errortip;
                return false;
            }

            $TransactionModel->transferClass($request['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], $request['from'], '', '', '', strtotime($request['create_time']));
            return true;

        }else{

            $stu_coursebalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure,coursebalance_unitrefund,coursebalance_time", "student_id='{$request['student_id']}' and course_id='{$request['course_id']}' and school_id='{$request['school_id']}'");

            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_cantomore", "company_id='{$this->company_id}'");

            if ($companyOne['company_cantomore'] == 0) {
                if ($surplusCourse['surplusNum'] > $surplusToCourse['surplusNum'] || $stu_coursebalanceOne['coursebalance_time'] > $surplusToCourse['surplusNum']) {
                    $this->error = true;
                    $this->errortip = "转入班级剩余数量不足";
                    return false;
                }
            }

            $fromTradingPid = $BalanceModel->carryOver($request['student_id'], $request['course_id'], $request['class_id'], 2, strtotime($request['create_time']));

            if (!$fromTradingPid) {
                $this->error = true;
                $this->errortip = $BalanceModel->errortip;
                return false;
            }

            $Model = new \Model\Smc\RegistrationModel($this->publicarray);

            $orderArray=array();
            $orderArray['from']='registrationPay';
            $orderArray['is_balance']='1';
            $orderArray['company_id']=$this->company_id;
            $orderArray['school_id']=$this->school_id;
            $orderArray['staffer_id']=$this->staffer_id;
            $orderArray['student_id']=$request['student_id'];
            $orderArray['all_coupon_price']=$request['all_coupon_price'];
            $orderArray['all_coupon_price_list']=$request['all_coupon_price_list'];
            $orderArray['list']=json_encode($courseArray,JSON_UNESCAPED_UNICODE);

            $res = $Model->createMergeOrder($orderArray, 0);
            if($res){
                $TransactionModel = new \Model\Smc\TransactionModel($request);
                $TransactionModel->transferClass($request['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], $request['from'], '', $fromTradingPid, '', strtotime($request['create_time']));

                return true;
            }else{
                $this->error = true;
                $this->errortip = $Model->errortip;
                return false;
            }



        }
    }
    function transferClassBak($request)
    {

        if ($request['pattern'] == 0) {
            $this->error = true;
            $this->errortip = "课次结转功能已关闭";
            return false;
        }

        $data = array();
        $data['is_forward'] = 1;
        if ($request['create_time']) {
            $now = $request['create_time'];
        } else {
            $now = date("Y-m-d", time());
        }

        $sql = "select po.order_pid
			  from smc_payfee_order as po
			  left join smc_payfee_order_course as poc on po.order_pid=poc.order_pid
			  where poc.course_id='{$request['course_id']}' and po.student_id='{$request['student_id']}' and po.school_id='{$this->school_id}' and po.order_arrearageprice>0
			  and po.order_status>=0
			  ";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "课程存在欠费,不可转班";
            return false;
        }

        $where = " 1 ";
        $sql = "select cl.changelog_day
                  from smc_student_changelog as cl,smc_code_stuchange as cs 
                  where cl.stuchange_code=cs.stuchange_code and cs.stuchange_type=0 and stustatus_isenclass=1 and cl.student_id='{$request['student_id']}' and cl.class_id='{$request['class_id']}'
                  order by cl.changelog_day desc,cl.changelog_id desc limit 0,1";

        $startLogOne = $this->DataControl->selectOne($sql);
        if ($startLogOne) {
            $where .= " and hour_day>='{$startLogOne['changelog_day']}'";
        } else {
            $startLogOne = $this->DataControl->getFieldOne("smc_student_study", "study_beginday", "student_id='{$request['student_id']}' and class_id='{$request['class_id']}'");

            if ($startLogOne) {
                $where .= " and hour_day>='{$startLogOne['study_beginday']}'";
            }
        }

        $sql = "select hour_id from smc_class_hour where {$where} and class_id='{$request['class_id']}' and hour_day<'{$now}' and hour_ischecking=0 and hour_iswarming=0";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "班级考勤未完成！";
            return false;
        }

        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $RegistrationModel = new \Model\Smc\RegistrationModel($request);

        $surplusCourse = $BalanceModel->surplusCourse($request['pricing_id'], $request['course_id'], $request['class_id'], $request['create_time']);
        $surplusToCourse = $BalanceModel->surplusCourse($request['to_pricing_id'], $request['to_course_id'], $request['to_class_id'], $request['create_time']);
        if ($surplusToCourse['surplusNum'] <= 0) {
            $this->error = true;
            $this->errortip = "转入班级无后续课程！";
            return false;
        }


        if ($surplusCourse['course_inclasstype'] == 2 && $surplusToCourse['course_inclasstype'] == 2) {
            $TransactionModel = new \Model\Smc\TransactionModel($request);

            $bool = $TransactionModel->outClass($request['student_id'], $request['class_id'], 2, strtotime($request['create_time']));
            if (!$bool) {
                $this->error = true;
                $this->errortip = $TransactionModel->errortip;
                return false;
            }

            $TransactionModel->transferClass($request['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], $request['from'], '', '', '', strtotime($request['create_time']));
            return true;
        } else {

            $stu_coursebalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_figure,coursebalance_unitrefund,coursebalance_time", "student_id='{$request['student_id']}' and course_id='{$request['course_id']}' and school_id='{$request['school_id']}'");

            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_cantomore", "company_id='{$this->company_id}'");

            if ($companyOne['company_cantomore'] == 0) {
                if ($surplusCourse['surplusNum'] > $surplusToCourse['surplusNum'] || $stu_coursebalanceOne['coursebalance_time'] > $surplusToCourse['surplusNum']) {
                    $this->error = true;
                    $this->errortip = "转入班级剩余数量不足";
                    return false;
                }
            }

            $playamount = $surplusToCourse['allPrice'];
            //		$unitexpend=$surplusToCourse['unitexpend'];

            if ($request['pattern'] == 0) {//是否课次  1-结转  0-课次
                //课次

                if ($request['course_id'] == $request['to_course_id']) {
                    $TransactionModel = new \Model\Smc\TransactionModel($request);

                    $TransactionModel->outClass($request['student_id'], $request['class_id'], $request['from'], strtotime($request['create_time']));

                    $TransactionModel->transferClass($request['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], $request['from'], '', '', '', strtotime($request['create_time']));
                    return true;

                } else {
                    $request['pricing_id'] = $request['to_pricing_id'];

                    $Model = new \Model\Smc\ForwardModel($this->publicarray);
                    $res = $Model->transferCourse($request);
                    if (!$res) {
                        $this->error = true;
                        $this->errortip = $Model->errortip;
                        return false;
                    }

                    return $res;
                }

            } else {
//				$fromTradingPid='';
                $fromTradingPid = $BalanceModel->carryOver($request['student_id'], $request['course_id'], $request['class_id'], 2, strtotime($request['create_time']));

                if (!$fromTradingPid) {
                    $this->error = true;
                    $this->errortip = $BalanceModel->errortip;
                    return false;
                }
                $courseArray = array();

                $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $request['to_course_id']);
                if ($policyOne) {
                    $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$request['to_course_id']}' and company_id='{$request['company_id']}'");
                    if ($courseOne['course_classnum'] <= $surplusToCourse['surplusNum']) {
                        $playamount = $policyOne['full_price'];
                        //					$unitexpend=ceil($policyOne['full_price']/$courseOne['course_classnum']);
                    } else {
                        $playamount = $surplusToCourse['surplusNum'] * $policyOne['unitexpend'];
                        //					$unitexpend=$policyOne['unitexpend'];
                    }
                }

                $all_price = $playamount;

                $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
			  from smc_fee_pricing_products as fpp
			  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
			  left join erp_goods as g on g.goods_id=fpp.goods_id
			  where fpp.pricing_id='{$request['to_pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
			  ";

                $goodsList = $this->DataControl->selectClear($goods_sql);
                if (!$goodsList) {
                    $goodsList = array();
                } else {
                    foreach ($goodsList as $val) {
                        if ($val['isfree'] != 1) {
                            $all_price += $val['sellingprice'];
                        }
                    }
                }

                $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
			  from smc_fee_pricing_items as fpi
			  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
			  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
			  where fpi.pricing_id='{$request['to_pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
			  group by fpi.feeitem_branch
			  ";
                $to_itemsList = $this->DataControl->selectClear($item_sql);

                if (!$to_itemsList) {
                    $to_itemsList = array();
                } else {
                    foreach ($to_itemsList as $itemsOne) {
                        if ($itemsOne['isfree'] != 1) {
                            $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                        }
                    }
                }

                $courseData = array();
                $courseData['agreement_id'] = $surplusToCourse['agreement_id'];
                $courseData['pricing_id'] = $request['to_pricing_id'];
                $courseData['course_id'] = $request['to_course_id'];
                $courseData['sellingprice'] = $playamount;
                $courseData['num'] = $surplusToCourse['surplusNum'];
                $courseData['class_id'] = $request['to_class_id'];
                if ($policyOne) {
                    $courseData['is_discount'] = 1;
                    $courseData['policy_id'] = $policyOne['policy_id'];
                } else {
                    $courseData['is_discount'] = 0;
                }

                $courseData['starttime'] = "";
                $courseData['discount_id'] = "";
                $courseData['market_price'] = "";
                $courseData['deductionmethod'] = 2;
                $courseData['from'] = 1;
                $courseData['goodsList'] = $goodsList;
                $courseData['itemsList'] = $to_itemsList;
                $courseData['couponList'] = json_decode(stripslashes($request['to_couponList']), 1);
                if (isset($courseData['couponList']) && $courseData['couponList'] != '') {
                    foreach ($courseData['couponList'] as $toOne) {
                        if ($toOne['coupon_price'] > 0) {
                            $data['is_forward'] = 0;
                            $all_price -= $toOne['coupon_price'];
                            $courseData['sellingprice'] -= $toOne['coupon_price'];
                        }
                    }
                }

                $courseArray[] = $courseData;

                if (isset($request['is_buy_next']) && $request['is_buy_next'] == 1) {

                    $next_course_id = $this->DataControl->getFieldOne("smc_course", "course_nextid", "course_id='{$request['to_course_id']}' and company_id='{$request['company_id']}'");
                    $next_pricingOne = $this->getCoursePricing($next_course_id['course_nextid'], $request['company_id'], $request['school_id']);
                    if ($next_course_id && $next_pricingOne) {

                        $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and course_id='{$next_pricingOne['course_id']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                        if (!$courseBalanceOne) {
                            $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $next_pricingOne['course_id']);
                            if ($policyOne) {
                                $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$request['to_course_id']}' and company_id='{$request['company_id']}'");
                                if ($courseOne['course_classnum'] <= $next_pricingOne['tuition_buypiece']) {
                                    $next_pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                                } else {
                                    $next_pricingOne['tuition_sellingprice'] = $policyOne['unitexpend'] * $next_pricingOne['tuition_buypiece'];
                                }
                            }

                            $next_courseData = array();
                            $next_courseData['agreement_id'] = $next_pricingOne['agreement_id'];
                            $next_courseData['pricing_id'] = $next_pricingOne['pricing_id'];
                            $next_courseData['course_id'] = $next_pricingOne['course_id'];
                            $next_courseData['sellingprice'] = $next_pricingOne['tuition_sellingprice'];
                            $next_courseData['num'] = $next_pricingOne['tuition_buypiece'];
                            $next_courseData['class_id'] = "";
                            $next_courseData['starttime'] = "";
                            $next_courseData['discount_id'] = "";
                            $next_courseData['market_price'] = "";
                            $next_courseData['deductionmethod'] = 2;
                            if ($policyOne) {
                                $next_courseData['is_discount'] = 1;
                                $next_courseData['policy_id'] = $policyOne['policy_id'];
                            } else {
                                $next_courseData['is_discount'] = 0;
                            }


                            $all_price += $next_pricingOne['tuition_sellingprice'];

                            $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
			  from smc_fee_pricing_products as fpp
			  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
			  left join erp_goods as g on g.goods_id=fpp.goods_id
			  where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
			  ";

                            $next_goodsList = $this->DataControl->selectClear($sql);
                            if (!$next_goodsList) {
                                $next_goodsList = array();
                            } else {
                                foreach ($next_goodsList as $val) {
                                    if ($val['isfree'] != 1) {
                                        $all_price += $val['sellingprice'];
                                    }
                                }
                            }

                            $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
			  from smc_fee_pricing_items as fpi
			  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
			  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
			  where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
			  group by fpi.feeitem_branch
			  ";
                            $next_itemsList = $this->DataControl->selectClear($item_sql);

                            if (!$next_itemsList) {
                                $next_itemsList = array();
                            } else {
                                foreach ($next_itemsList as $itemsOne) {
                                    if ($itemsOne['isfree'] != 1) {
                                        $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                    }
                                }
                            }

                            $next_courseData['goodsList'] = $next_goodsList;
                            $next_courseData['itemsList'] = $next_itemsList;
                            $next_courseData['couponList'] = json_decode(stripslashes($request['next_couponList']), 1);

                            if (isset($next_courseData['couponList']) && $next_courseData['couponList'] != '') {
                                foreach ($next_courseData['couponList'] as $nextOne) {
                                    if ($nextOne['coupon_price'] > 0) {
                                        $data['is_forward'] = 0;
                                        $all_price -= $nextOne['coupon_price'];
                                        $next_courseData['sellingprice'] -= $nextOne['coupon_price'];
                                    }
                                }
                            }
                            $courseArray[] = $next_courseData;

                        }
                    }
                }
                //			var_dump($courseArray);

                $data['list'] = json_encode($courseArray, JSON_UNESCAPED_UNICODE);

                $data['company_id'] = $request['company_id'];
                $data['school_id'] = $request['school_id'];
                $data['staffer_id'] = $request['staffer_id'];
                $data['student_id'] = $request['student_id'];

                $data['coupon_price'] = 0;
                $data['market_price'] = 0;

                $data['giveforwardprice'] = 0;
                $data['all_market_price'] = 0;
                if (isset($request['all_coupon_price']) && $request['all_coupon_price'] > 0) {
                    $data['all_coupon_price'] = $request['all_coupon_price'];
                    $data['coupon_id'] = $request['coupon_id'];

                    $data['is_forward'] = 0;
                    $all_price -= $request['all_coupon_price'];
                }

                $data['create_time'] = $request['create_time'];
                $data['price'] = $all_price;

                //			print_r($data);exit;
                $return_data = array();
                $stuOne = $this->DataControl->getFieldOne("smc_student", "student_branch,student_cnname", "student_id='{$request['student_id']}'");
                $return_data['student_id'] = $request['student_id'];
                $return_data['student_cnname'] = $stuOne['student_cnname'];
                $return_data['student_branch'] = $stuOne['student_branch'];
                $order_pid = 0;

                if ($all_price > 0) {
                    $order_pid = $RegistrationModel->createOrder($data);
                    $orderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid='{$order_pid}'");
                    $return_data['order_pid'] = $order_pid;
                    $return_data['order_paymentprice'] = $orderOne['order_paymentprice'];
                    $return_data['discount_price'] = $orderOne['order_coupon_price'] + $orderOne['order_market_price'];
                    $return_data['order_paidprice'] = $orderOne['order_paidprice'];
                    $return_data['order_arrearageprice'] = $orderOne['order_arrearageprice'];
                } else {
                    $return_data['order_pid'] = $order_pid;
                    $return_data['order_paymentprice'] = 0;
                    $return_data['discount_price'] = 0;
                    $return_data['order_paidprice'] = 0;
                    $return_data['order_arrearageprice'] = 0;
                }

                $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "trading_pid", "order_pid='{$order_pid}'");

                $TransactionModel = new \Model\Smc\TransactionModel($request);
                $TransactionModel->transferClass($request['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], $request['from'], '', $fromTradingPid, $orderOne['trading_pid'], strtotime($request['create_time']));

                $data = array();
                $data['order_pid'] = $order_pid;
                $data['data'][] = $return_data;

                return $data;
            }
        }
    }

    function transferNewClassOne($request)
    {
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $list = json_decode(stripslashes($request['list']), 1);
        $all_price = 0;
        if ($list) {
            foreach ($list as $one) {

                $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and course_id='{$one['course_id']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                if (!$courseBalanceOne) {
                    $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $one['course_id']);
                    $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "company_id='{$request['company_id']}' and course_id='{$one['course_id']}'");
                    if ($one['class_id'] > 0) {
                        $surplusCourse = $BalanceModel->surplusCourse($one['pricing_id'], $one['course_id'], $one['class_id']);
                        if ($policyOne) {
                            if ($courseOne['course_classnum'] == $surplusCourse['surplusNum']) {
                                $surplusCourse['allPrice'] = $policyOne['full_price'];
                            } else {
                                $surplusCourse['allPrice'] = $surplusCourse['surplusNum'] * $policyOne['unitexpend'];
                            }
                        }

                        $all_price += ($surplusCourse['allPrice'] - $one['coupon_price']);
                    } else {
                        $surplusCourse = $this->getCoursePricing($one['course_id'], $request['company_id'], $request['school_id']);

                        if ($policyOne) {
                            if ($courseOne['course_classnum'] == $surplusCourse['tuition_buypiece']) {
                                $surplusCourse['tuition_sellingprice'] = $policyOne['full_price'];
                            } else {
                                $surplusCourse['tuition_sellingprice'] = $surplusCourse['tuition_buypiece'] * $policyOne['unitexpend'];
                            }
                        }
                        $all_price += ($surplusCourse['tuition_sellingprice'] - $one['coupon_price']);
                    }

                    $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$one['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";

                    $goodsList = $this->DataControl->selectClear($goods_sql);
                    if ($goodsList) {
                        foreach ($goodsList as $val) {
                            if ($val['isfree'] != 1) {
                                $all_price += $val['sellingprice'];
                            }
                        }
                    }

                    $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$one['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                    $to_itemsList = $this->DataControl->selectClear($item_sql);

                    if ($to_itemsList) {
                        foreach ($to_itemsList as $itemsOne) {
                            if ($itemsOne['isfree'] != 1) {
                                $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                            }
                        }
                    }
                }

                if ($all_price < 0) {
                    $all_price = 0;
                }
            }
        }
        $data = array();

        $price_list = json_decode(stripslashes($request['all_coupon_price_list']), 1);

        $all_coupon_price = 0;
        if (isset($price_list) && $price_list) {
            $priceArray = array();
            $rateArray = array();
            foreach ($price_list as $value) {
                if ($value['ticket_way'] == '0') {
                    $priceArray[] = $value;
                } else {
                    $rateArray[] = $value;
                }
            }
            if ($priceArray) {
                foreach ($priceArray as $val) {
                    if ($val['derateprice'] > $all_price) {
                        $val['derateprice'] = $all_price;
                    }

                    $all_coupon_price += $val['derateprice'];
                    $all_price -= $val['derateprice'];
                }
            }

            if ($rateArray) {
                foreach ($rateArray as $val) {
                    $all_coupon_price += floor(round($all_price * (1 - $val['deraterate'] / 10), 1));
                    $all_price -= floor(round($all_price * (1 - $val['deraterate'] / 10), 1));
                }
            }

            if ($all_price < 0) {
                $all_price = 0;
            }
        }

//        if (isset($request['all_coupon_price']) || $request['all_coupon_price'] != '') {
//            $all_price -= $request['all_coupon_price'];
//            if ($all_price < 0) {
//                $all_price = 0;
//            }
//        }
        $data['price'] = $all_price;
        return $data;
    }

    //拆班是结算列表
    function transferNewClassList($request)
    {
        $now = date("Y-m-d", time());
        $classOne = $this->DataControl->selectOne("select c.coursetype_id from smc_course as c left join smc_class as sc on sc.course_id=c.course_id where sc.class_id='{$request['class_id']}' and sc.company_id='{$request['company_id']}' and sc.school_id='{$request['school_id']}' limit 0,1");

        $list = json_decode(stripslashes($request['list']), 1);

        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = $this->LgStringSwitch("定价ID");
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_id";
        $field[1]["fieldname"] = $this->LgStringSwitch("课程ID");
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_id";
        $field[2]["fieldname"] = $this->LgStringSwitch("班级ID");
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "class_cnname";
        $field[3]["fieldname"] = $this->LgStringSwitch("班级名称");
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "course_cnname";
        $field[4]["fieldname"] = $this->LgStringSwitch("课程别");
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "classInfo";
        $field[5]["fieldname"] = $this->LgStringSwitch("已上/计划");
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "nonum";
        $field[6]["fieldname"] = $this->LgStringSwitch("未上课次");
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "tuition_originalprice";
        $field[7]["fieldname"] = $this->LgStringSwitch("原价");
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "tuition_sellingprice";
        $field[8]["fieldname"] = $this->LgStringSwitch("销售价");
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "unitsellprice";
        $field[9]["fieldname"] = $this->LgStringSwitch("销售单价");
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "coupon";
        $field[10]["fieldname"] = $this->LgStringSwitch("优惠券");
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;
        $field[10]["isSelectJuan"] = 1;

        $tem_array = array();

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.class_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and sc.class_id <> '{$request['class_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and c.course_id <> '{$request['course_id']}'";
        }

        if (isset($request['to_class_id']) && $request['to_class_id'] !== '') {
            $datawhere .= " and sc.class_id = '{$request['to_class_id']}'";
        }

        foreach ($list as $student) {
            $data = array();
            $all_price = 0;
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_cnname", "student_id='{$student['student_id']}'");

            $sql = "SELECT
                    t.course_id,
                    p.pricing_id,
                    t.tuition_originalprice,
                    t.tuition_sellingprice,
                    t.tuition_buypiece,
                    t.tuition_unitprice,
                    a.agreement_id,
                    sc.class_id,
                    c.course_id,
                    sc.class_cnname,
                    c.course_cnname
					,(select COUNT(ch.hour_id) from smc_class_hour as ch where ch.class_id=sc.class_id and ch.hour_ischecking=1) as hournum
					,c.course_classnum
					,c.course_nextid
                FROM
                    smc_fee_pricing_tuition AS t,
                    smc_fee_pricing AS p,
                    smc_fee_agreement AS a,
                    smc_class as sc,
                    smc_course as c
                WHERE {$datawhere}
                AND t.pricing_id = p.pricing_id
                AND p.agreement_id = a.agreement_id
				AND sc.course_id=t.course_id
				AND c.course_id=sc.course_id
				AND c.coursetype_id='{$classOne['coursetype_id']}'
                AND (
                    (
                        p.pricing_applytype = '1'
                        AND p.pricing_id IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$request['school_id']}'
                        )
                    )
                    OR (
                        p.pricing_applytype = '-1'
                        AND p.pricing_id NOT IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$request['school_id']}'
                        )
                    )
                    OR (p.pricing_applytype = '0')
                )
                AND a.agreement_startday <= '{$now}'
                AND a.agreement_endday >= '{$now}'
                AND a.agreement_status = '1'
                AND a.company_id = '{$request['company_id']}'
                and sc.school_id='{$request['school_id']}'
                and sc.class_enddate > '{$now}'
                and c.course_status<>'-1'
                GROUP BY
                    t.course_id ORDER BY sc.class_id DESC";
            $pricingList = $this->DataControl->selectClear($sql);

//			and sc.class_id not in (select ss.class_id from smc_student_study as ss where ss.student_id='{$student['student_id']}' and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}' and ss.study_isreading='1')

            if ($pricingList) {
                foreach ($pricingList as &$pricingOne) {
                    $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$student['student_id']}' and course_id='{$pricingOne['course_id']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                    $pricingOne['classInfo'] = $pricingOne['hournum'] . '/' . $pricingOne['course_classnum'];
                    $pricingOne['unitsellprice'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['course_classnum']);
                    $pricingOne['nonum'] = $pricingOne['course_classnum'] - $pricingOne['hournum'];
                    $pricingOne['coupon'] = '';

                    if (!$courseBalanceOne) {
                        $policyOne = $this->getStuDiscountPrice($request, $student['student_id'], $pricingOne['course_id']);
                        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "company_id='{$request['company_id']}' and course_id='{$pricingOne['course_id']}'");
                        if ($policyOne) {
                            if (($pricingOne['course_classnum'] - $pricingOne['hournum']) >= $courseOne['course_classnum']) {
                                $all_price += $policyOne['full_price'];
                            } else {
                                $all_price += ($pricingOne['course_classnum'] - $pricingOne['hournum']) * $policyOne['unitexpend'];
                            }
                        } else {
                            $all_price += ceil($pricingOne['tuition_sellingprice'] / $pricingOne['course_classnum']) * ($pricingOne['course_classnum'] - $pricingOne['hournum']);
                        }

                        $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";

                        $goodsList = $this->DataControl->selectClear($goods_sql);
                        if ($goodsList) {
                            foreach ($goodsList as $val) {
                                if ($val['isfree'] != 1) {
                                    $all_price += $val['sellingprice'];
                                }
                            }
                        }

                        $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                        $to_itemsList = $this->DataControl->selectClear($item_sql);

                        if ($to_itemsList) {
                            foreach ($to_itemsList as $itemsOne) {
                                if ($itemsOne['isfree'] != 1) {
                                    $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                }
                            }
                        }
                    }
                }
                if (isset($request['is_buy_next']) && $request['is_buy_next'] == 1) {
                    $nextclassOne = $this->DataControl->selectOne("select c.course_nextid from smc_course as c left join smc_class as sc on sc.course_id=c.course_id where sc.class_id='{$request['to_class_id']}' and sc.company_id='{$request['company_id']}' limit 0,1");
                    $relClassOne = $this->DataControl->selectOne("select course_nextid,course_id,course_cnname,course_presentednums,course_classnum,course_branch from smc_course where company_id='{$request['company_id']}' and course_id='{$nextclassOne['course_nextid']}' limit 0,1");

                    if ($nextclassOne) {

                        $next_pricingOne = $this->getCoursePricing($nextclassOne['course_nextid'], $request['company_id'], $request['school_id']);
                        if ($next_pricingOne) {

                            $tem_data = array();
                            $tem_data['pricing_id'] = $next_pricingOne['pricing_id'];
                            $tem_data['course_id'] = $relClassOne['course_id'];
                            $tem_data['class_id'] = '--';
                            $tem_data['class_cnname'] = '--';
                            $tem_data['course_nextid'] = '0';
                            $tem_data['course_cnname'] = $relClassOne['course_cnname'];
                            $tem_data['classInfo'] = '0/' . $relClassOne['course_classnum'];
                            $tem_data['nonum'] = $relClassOne['course_classnum'];
                            $tem_data['tuition_originalprice'] = $next_pricingOne['tuition_originalprice'];
                            $tem_data['tuition_sellingprice'] = $next_pricingOne['tuition_sellingprice'];
                            $tem_data['unitsellprice'] = ceil($next_pricingOne['tuition_sellingprice'] / $relClassOne['course_classnum']);
                            $tem_data['coupon'] = '';

                            $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$student['student_id']}' and course_id='{$nextclassOne['course_nextid']}' and (coursebalance_figure>0 or coursebalance_time>0)");
                            if (!$courseBalanceOne) {

                                $policyOne = $this->getStuDiscountPrice($request, $student['student_id'], $nextclassOne['course_nextid']);

                                if ($policyOne) {
                                    $next_pricingOne['tuition_sellingprice'] += $policyOne['full_price'];
                                }

                                $all_price += $next_pricingOne['tuition_sellingprice'];

                                $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";

                                $goodsList = $this->DataControl->selectClear($goods_sql);
                                if ($goodsList) {
                                    foreach ($goodsList as $val) {
                                        if ($val['isfree'] != 1) {
                                            $all_price += $val['sellingprice'];
                                        }
                                    }
                                }

                                $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                                $to_itemsList = $this->DataControl->selectClear($item_sql);

                                if ($to_itemsList) {
                                    foreach ($to_itemsList as $itemsOne) {
                                        if ($itemsOne['isfree'] != 1) {
                                            $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                        }
                                    }
                                }
                            }
                            $pricingList[] = $tem_data;
                        }
                    }
                }
                $data = array();
                $data['list'] = $pricingList;
                $data['field'] = $field;
                $userArray = array();
                $userArray['student_id'] = $studentOne['student_id'];
                $userArray['student_cnname'] = $studentOne['student_cnname'];
                $userArray['all_price'] = $all_price;
                $data['userInfo'] = $userArray;
                $tem_array[] = $data;
            }

        }

        return $tem_array;
    }

    function transferClassPrice($request){

        $courseList = json_decode(stripslashes($request['list']), 1);

        if(!$courseList){
            $this->error = true;
            $this->errortip = "未设置结算内容";
            return false;
        }

        $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);

        $all_price = 0;

        foreach($courseList as $courseOne){

            $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
                          from smc_fee_pricing_products as fpp
                          left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
                          left join erp_goods as g on g.goods_id=fpp.goods_id
                          where fpp.pricing_id='{$courseOne['to_pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
                          ";

            $goodsList = $this->DataControl->selectClear($goods_sql);
            if ($goodsList) {
                foreach ($goodsList as $val) {
                    if ($val['isfree'] != 1) {
                        $all_price += $val['sellingprice'];
                    }
                }
            }

            $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
                          from smc_fee_pricing_items as fpi
                          left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
                          left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
                          where fpi.pricing_id='{$courseOne['to_pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
                          group by fpi.feeitem_branch ";
            $to_itemsList = $this->DataControl->selectClear($item_sql);

            if ($to_itemsList) {
                foreach ($to_itemsList as $itemsOne) {
                    if ($itemsOne['isfree'] != 1) {
                        $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                    }
                }
            }

            if($courseOne['to_class_id']!='--' && $courseOne['to_class_id']>0){
                $surplusToCourse = $BalanceModel->surplusCourse($courseOne['to_pricing_id'], $courseOne['to_course_id'], $courseOne['to_class_id']);

                $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $courseOne['to_course_id']);
                if ($policyOne) {
                    $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$courseOne['to_course_id']}' and company_id='{$request['company_id']}'");
                    if ($courseOne['course_classnum'] <= $surplusToCourse['surplusNum']) {
                        $surplusToCourse['allPrice'] = $policyOne['full_price'];
                    } else {
                        $surplusToCourse['allPrice'] = $surplusToCourse['surplusNum'] * $policyOne['unitexpend'];
                    }
                }

                $all_price += $surplusToCourse['allPrice'];


                $sql = "select a.course_id,a.course_classnum 
                    from smc_course as a 
                    where a.company_id='{$request['company_id']}' and a.main_course_id='{$courseOne['to_course_id']}' and a.course_isfollow=1";

                $followCourseOne=$this->DataControl->selectOne($sql);
                if($followCourseOne){

                    $follow_pricingOne = $this->getCoursePricing($followCourseOne['course_id'], $request['company_id'], $request['school_id']);

                    if($follow_pricingOne){

                        $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $followCourseOne['course_id']);
                        if($policyOne){
                            if ($followCourseOne['course_classnum'] <= $surplusToCourse['surplusNum']) {
                                $follow_pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                            } else {
                                $follow_pricingOne['tuition_sellingprice'] = $surplusToCourse['surplusNum'] * $policyOne['unitexpend'];
                            }
                        }

                        $all_price += $follow_pricingOne['tuition_sellingprice'];
                    }
                }
            }else{

                $nextCourseOne = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_sellingprice,tuition_buypiece,tuition_unitprice", "pricing_id='{$courseOne['to_pricing_id']}' and course_id='{$courseOne['to_course_id']}'");

                $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $courseOne['to_course_id']);
                if ($policyOne) {
                    $newCourseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$courseOne['to_course_id']}' and company_id='{$request['company_id']}'");

                    if ($newCourseOne['course_classnum'] <= $nextCourseOne['tuition_buypiece']) {
                        $nextCourseOne['tuition_sellingprice'] = $policyOne['full_price'];
                    } else {
                        $nextCourseOne['tuition_sellingprice'] = $nextCourseOne['surplusNum'] * $policyOne['unitexpend'];
                    }
                }

                $all_price += $nextCourseOne['tuition_sellingprice'];

                $sql = "select a.course_id,a.course_classnum 
                    from smc_course as a 
                    where a.company_id='{$request['company_id']}' and a.main_course_id='{$courseOne['to_course_id']}' and a.course_isfollow=1";

                $followCourseOne=$this->DataControl->selectOne($sql);

                if($followCourseOne){

                    $follow_pricingOne = $this->getCoursePricing($followCourseOne['course_id'], $request['company_id'], $request['school_id']);

                    if($follow_pricingOne){
                        $all_price += $follow_pricingOne['tuition_sellingprice'];
                    }
                }
            }
        }

        $to_couponList = json_decode(stripslashes($request['to_couponList']), 1);
        $next_couponList = json_decode(stripslashes($request['next_couponList']), 1);

        if (isset($request['all_coupon_price']) && $request['all_coupon_price'] > 0) {
            $data['all_coupon_price'] = $request['all_coupon_price'];
            $data['coupon_id'] = $request['coupon_id'];

            $data['is_forward'] = 0;
            $all_price -= $request['all_coupon_price'];
        }

        if (isset($to_couponList) && $to_couponList != '') {
            foreach ($to_couponList as $toOne) {
                if ($toOne['coupon_price'] > 0) {
                    $data['is_forward'] = 0;
                    $all_price -= $toOne['coupon_price'];
                }
            }
        }


        if (isset($next_couponList) && $next_couponList != '') {
            foreach ($next_couponList as $nextOne) {
                if ($nextOne['coupon_price'] > 0) {
                    $data['is_forward'] = 0;
                    $all_price -= $nextOne['coupon_price'];
                }
            }
        }

        $total_data = array();
        $total_data['allprice'] = $all_price;
        return $total_data;

    }




    function transferClassPriceBak($request)
    {
        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $all_price = 0;

//		$courseBalanceOne=$this->DataControl->getFieldOne("smc_student_coursebalance","coursebalance_id","company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and course_id='{$request['to_course_id']}' and (coursebalance_figure>0 or coursebalance_time>0)");

        $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
		  from smc_fee_pricing_products as fpp
		  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
		  left join erp_goods as g on g.goods_id=fpp.goods_id
		  where fpp.pricing_id='{$request['to_pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
		  ";

        $goodsList = $this->DataControl->selectClear($goods_sql);
        if ($goodsList) {
            foreach ($goodsList as $val) {
                if ($val['isfree'] != 1) {
                    $all_price += $val['sellingprice'];
                }
            }
        }

        $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
		  from smc_fee_pricing_items as fpi
		  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
		  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
		  where fpi.pricing_id='{$request['to_pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
		  group by fpi.feeitem_branch
		  ";
        $to_itemsList = $this->DataControl->selectClear($item_sql);

        if ($to_itemsList) {
            foreach ($to_itemsList as $itemsOne) {
                if ($itemsOne['isfree'] != 1) {
                    $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                }
            }
        }

        $surplusToCourse = $BalanceModel->surplusCourse($request['to_pricing_id'], $request['to_course_id'], $request['to_class_id']);


        $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $request['to_course_id']);
        if ($policyOne) {
            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$request['to_course_id']}' and company_id='{$request['company_id']}'");
            if ($courseOne['course_classnum'] <= $surplusToCourse['surplusNum']) {
                $surplusToCourse['allPrice'] = $policyOne['full_price'];
            } else {
                $surplusToCourse['allPrice'] = $surplusToCourse['surplusNum'] * $policyOne['unitexpend'];
            }
        }
        $all_price += $surplusToCourse['allPrice'];


        if (isset($request['is_buy_next']) && $request['is_buy_next'] == 1) {
            $next_course_id = $this->DataControl->getFieldOne("smc_course", "course_nextid", "course_id='{$request['to_course_id']}' and company_id='{$request['company_id']}'");
            $next_pricingOne = $this->getCoursePricing($next_course_id['course_nextid'], $request['company_id'], $request['school_id']);
            if ($next_course_id && $next_pricingOne) {

                $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and course_id='{$next_course_id['course_nextid']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                if (!$courseBalanceOne) {

                    $nextCourseOne = $this->DataControl->getFieldOne("smc_fee_pricing_tuition", "tuition_sellingprice,tuition_buypiece,tuition_unitprice", "pricing_id='{$request['next_pricing_id']}' and course_id='{$next_course_id['course_nextid']}'");
                    $policyOne = $this->getStuDiscountPrice($request, $request['student_id'], $next_course_id['course_nextid']);
                    if ($policyOne) {
                        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum", "course_id='{$next_course_id['course_nextid']}' and company_id='{$request['company_id']}'");

                        if ($courseOne['course_classnum'] <= $nextCourseOne['tuition_buypiece']) {
                            $nextCourseOne['tuition_sellingprice'] = $policyOne['full_price'];
                        } else {
                            $nextCourseOne['tuition_sellingprice'] = $nextCourseOne['surplusNum'] * $policyOne['unitexpend'];
                        }
                    }

                    $all_price += $nextCourseOne['tuition_sellingprice'];


                    $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";

                    $next_goodsList = $this->DataControl->selectClear($sql);
                    if ($next_goodsList) {
                        foreach ($next_goodsList as $val) {
                            if ($val['isfree'] != 1) {
                                $all_price += $val['sellingprice'];
                            }
                        }
                    }

                    $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                    $to_itemsList = $this->DataControl->selectClear($item_sql);

                    if ($to_itemsList) {
                        foreach ($to_itemsList as $itemsOne) {
                            if ($itemsOne['isfree'] != 1) {
                                $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                            }
                        }
                    }
                }
            }
        }

        $to_couponList = json_decode(stripslashes($request['to_couponList']), 1);
        $next_couponList = json_decode(stripslashes($request['next_couponList']), 1);

        if (isset($request['all_coupon_price']) && $request['all_coupon_price'] > 0) {
            $data['all_coupon_price'] = $request['all_coupon_price'];
            $data['coupon_id'] = $request['coupon_id'];

            $data['is_forward'] = 0;
            $all_price -= $request['all_coupon_price'];
        }
        if (isset($to_couponList) && $to_couponList != '') {
            foreach ($to_couponList as $toOne) {
                if ($toOne['coupon_price'] > 0) {
                    $data['is_forward'] = 0;
                    $all_price -= $toOne['coupon_price'];
                }
            }
        }


        if (isset($next_couponList) && $next_couponList != '') {
            foreach ($next_couponList as $nextOne) {
                if ($nextOne['coupon_price'] > 0) {
                    $data['is_forward'] = 0;
                    $all_price -= $nextOne['coupon_price'];
                }
            }
        }

        $total_data = array();
        $total_data['allprice'] = $all_price;
        return $total_data;
    }

    function classStudentList($request)
    {
        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.pricing_id
				,(select po.order_status
						from smc_payfee_order_course as poc
						left join smc_payfee_order as po on po.order_pid=poc.order_pid
						where po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.student_id=ssc.student_id and poc.course_id=ssc.course_id
                        and poc.pricing_id=ssc.pricing_id
						order by po.order_createtime desc
						limit 0,1) as order_status
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_student_coursebalance as ssc on ssc.course_id=sc.course_id and ssc.student_id=s.student_id
              left join smc_student_enrolled as se on se.student_id=ss.student_id
              where ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and ss.study_isreading=1 and ss.student_id>0 and se.enrolled_status>0
              group by ss.student_id
              order by ss.study_beginday DESC
              ";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        return $studentList;
    }

    function classForwardList($request)
    {
        $list = json_decode(stripslashes($request['list']), true);
        $data = array();
        foreach ($list as $val) {
            $sql = "select s.student_id,s.student_cnname,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.coursebalance_unitexpend,ssc.coursebalance_unitrefund,sc.course_classnum,scf.courseforward_price
				  from smc_student_coursebalance as ssc
				  left join smc_student as s on s.student_id=ssc.student_id
				  left join smc_course as sc on sc.course_id=ssc.course_id
				  left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and scf.course_id=ssc.course_id
				  where sc.company_id='{$request['company_id']}' and ssc.student_id='{$val['student_id']}' and ssc.course_id='{$val['course_id']}'
				 ";
            $studentOne = $this->DataControl->selectOne($sql);
            $price = ($studentOne['course_classnum'] * $studentOne['coursebalance_unitexpend']) - ($studentOne['course_classnum'] - $studentOne['coursebalance_time']) * $studentOne['coursebalance_unitrefund'];
            if ($price < 0) {
                $studentOne['balance'] = 0;
            } else {
                $studentOne['balance'] = $price;
            }
            $data[] = $studentOne;
        }
        return $data;
    }

    function transferableToClass($request)
    {
        $now = date("Y-m-d", time());

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.class_cnname like '%{$request['keyword']}%' or sc.class_enname like '%{$request['keyword']}%' or sc.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and sc.class_id <> '{$request['class_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and c.course_id='{$request['course_id']}'";
        }


        $classOne = $this->DataControl->selectOne("select c.coursetype_id from smc_course as c left join smc_class as sc on sc.course_id=c.course_id where sc.class_id='{$request['class_id']}' and sc.company_id='{$request['company_id']}' and sc.school_id='{$request['school_id']}' limit 0,1");

        $sql = "SELECT t.course_id,p.pricing_id
				FROM smc_fee_pricing_tuition AS t,smc_fee_pricing AS p,smc_fee_agreement AS a,smc_course as sc
				WHERE t.pricing_id = p.pricing_id AND p.agreement_id = a.agreement_id and sc.course_id=p.course_id and sc.coursetype_id='{$classOne['coursetype_id']}'
				AND (
					(p.pricing_applytype = '1'
					AND p.pricing_id IN (
							SELECT
								pricing_id
							FROM
								smc_fee_pricing_apply AS a
							WHERE
								a.school_id = '{$request['school_id']}'
						)
					)
				OR (
					p.pricing_applytype = '-1'
					AND p.pricing_id NOT IN (
						SELECT
							pricing_id
						FROM
							smc_fee_pricing_apply AS a
						WHERE
							a.school_id = '{$request['school_id']}'
					)
				)
				OR (p.pricing_applytype = '0')
				)
				AND a.agreement_startday <= '{$now}'
				AND a.agreement_endday >= '{$now}'
				AND a.company_id = '{$request['company_id']}'
				and sc.course_status<>'-1'
				GROUP BY
					t.course_id
				";
        $pricingList = $this->DataControl->selectClear($sql);
        if ($pricingList) {
            $classList = array();
            foreach ($pricingList as $val) {
                $classwhere = "{$datawhere} and sc.class_status>=0 and sc.class_enddate>'{$now}' and fpt.pricing_id='{$val['pricing_id']}' and fpt.course_id='{$val['course_id']}' and c.company_id='{$request['company_id']}' and sc.school_id='{$request['school_id']}'";

                $tem_sql = "select sc.class_id,fpt.pricing_id,c.course_id,sc.class_cnname,c.course_cnname,sc.class_branch,sc.class_fullnums,c.course_classnum
								,(select COUNT(distinct stu.study_id) from smc_student_study as stu where stu.study_isreading=1 and stu.class_id=sc.class_id and stu.company_id='{$request['company_id']}' and stu.school_id='{$request['school_id']}') as num
								,(select COUNT(sch.hour_id) from smc_class_hour as sch where sch.class_id=sc.class_id and sch.hour_ischecking=1) as hasnum
								,fpt.tuition_originalprice
								,fpt.tuition_sellingprice,fpt.tuition_unitprice,fpt.tuition_buypiece,sc.class_enname,c.course_branch
                              from smc_class as sc
                              left join smc_course as c on c.course_id=sc.course_id
                              left join smc_fee_pricing_tuition as fpt on fpt.course_id=c.course_id
                              where {$classwhere} GROUP BY sc.class_id";
                $classArray = $this->DataControl->selectClear($tem_sql);

                if ($classArray) {
                    foreach ($classArray as $classOne) {
                        $classOne['classInfo'] = $classOne['num'] . '/' . $classOne['class_fullnums'];
                        $classOne['unitsellprice'] = ceil($classOne['tuition_sellingprice'] / $classOne['course_classnum']);
                        $classOne['nonum'] = $classOne['course_classnum'] - $classOne['hasnum'];
                        $classList[] = $classOne;
                    }
                }
            }

            return $classList;
        } else {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
    }

    function canTransferClassList($request)
    {
        $sql = "select count(ch.hour_id) as num from smc_class_hour as ch where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=0";
        $one = $this->DataControl->selectOne($sql);


        $now = date("Y-m-d", time());

        $datawhere = " 1 ";
        if (isset($request['student_id']) && $request['student_id'] > '0') {
            $datawhere .= " and c.class_id not in (select ssc.class_id from smc_student_study as ssc where ssc.student_id='{$request['student_id']}' and ssc.school_id='{$request['school_id']}' and ssc.company_id='{$request['company_id']}' and ssc.study_isreading='1')";

            $datawhere .= " and c.class_id not in (select clt.class_id from (select ss.class_id,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ss.class_id and sch.hour_day>ss.study_endday and sch.hour_ischecking='1') as hour_num from smc_student_study as ss where ss.student_id='{$request['student_id']}' and ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.study_isreading<>'1' having hour_num>'0') as clt)";
        }

        $sql = "SELECT p.course_id,p.pricing_id,c.class_id,c.class_cnname,sc.course_cnname,c.class_branch,c.class_fullnums,sc.course_classnum
					,(select COUNT(distinct stu.study_id) from smc_student_study as stu where stu.study_isreading=1 and stu.class_id=c.class_id and stu.company_id='{$request['company_id']}' and stu.school_id='{$request['school_id']}') as num
					,(select COUNT(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=1) as hasnum
					,(select COUNT(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=0) as hasNoNum
					,t.tuition_originalprice
					,t.tuition_sellingprice,t.tuition_unitprice,t.tuition_buypiece,c.class_enname,sc.course_branch
				FROM smc_fee_pricing_tuition AS t,smc_fee_pricing AS p,smc_fee_agreement AS a,smc_course as sc,smc_class as c
				WHERE t.pricing_id = p.pricing_id AND p.agreement_id = a.agreement_id and sc.course_id=p.course_id and c.course_id=sc.course_id
				and {$datawhere}
				AND (
					(p.pricing_applytype = '1'
					AND p.pricing_id IN (
							SELECT
								pricing_id
							FROM
								smc_fee_pricing_apply AS a
							WHERE
								a.school_id = '{$request['school_id']}'
						)
					)
				OR (
					p.pricing_applytype = '-1'
					AND p.pricing_id NOT IN (
						SELECT
							pricing_id
						FROM
							smc_fee_pricing_apply AS a
						WHERE
							a.school_id = '{$request['school_id']}'
					)
				)
				OR (p.pricing_applytype = '0')
				)
				AND sc.course_id='{$request['course_id']}'
				AND a.agreement_startday <= '{$now}'
				AND a.agreement_endday >= '{$now}'
				AND a.company_id = '{$request['company_id']}'
				AND c.class_status >= 0
				AND c.class_id <> '{$request['class_id']}'
				AND c.school_id='{$request['school_id']}'
				AND c.class_type='0'
				AND sc.course_status<>'-1'
				GROUP BY
					c.class_id
				HAVING hasNoNum>='{$one['num']}'
				";
        $pricingList = $this->DataControl->selectClear($sql);
        if (!$pricingList) {
            $this->error = true;
            $this->errortip = "课程不可使用";
            return false;
        }
        $classList = array();
        foreach ($pricingList as &$pricingOne) {
            $pricingOne['classInfo'] = $pricingOne['num'] . '/' . $pricingOne['class_fullnums'];
            $pricingOne['unitsellprice'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['course_classnum']);
            $pricingOne['nonum'] = $pricingOne['course_classnum'] - $pricingOne['hasnum'];
            $classList[] = $pricingOne;
        }
        return $classList;
    }


    function transferInfo($request)
    {
        $now = date("Y-m-d", time());
        $sql = "SELECT p.course_id,p.pricing_id,c.class_id,c.class_cnname,sc.course_cnname,c.class_branch,c.class_fullnums,sc.course_classnum,sc.course_branch,sc.course_cnname,sc.course_classnum
					,(select COUNT(distinct stu.study_id) from smc_student_study as stu where stu.study_isreading=1 and stu.class_id=c.class_id and stu.company_id='{$request['company_id']}' and stu.school_id='{$request['school_id']}') as num
					,(select COUNT(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=1) as hasnum
					,(select COUNT(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=0) as noNum
					,(select sch.hour_lessontimes from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=1 order by sch.hour_lessontimes desc limit 0,1) as lessontimes
					,t.tuition_originalprice
					,t.tuition_sellingprice,t.tuition_unitprice,t.tuition_buypiece,c.class_enname,sc.course_branch
				FROM smc_fee_pricing_tuition AS t,smc_fee_pricing AS p,smc_fee_agreement AS a,smc_course as sc,smc_class as c
				WHERE t.pricing_id = p.pricing_id AND p.agreement_id = a.agreement_id and sc.course_id=p.course_id and c.course_id=sc.course_id
				AND (
					(p.pricing_applytype = '1'
					AND p.pricing_id IN (
							SELECT
								pricing_id
							FROM
								smc_fee_pricing_apply AS a
							WHERE
								a.school_id = '{$request['school_id']}'
						)
					)
				OR (
					p.pricing_applytype = '-1'
					AND p.pricing_id NOT IN (
						SELECT
							pricing_id
						FROM
							smc_fee_pricing_apply AS a
						WHERE
							a.school_id = '{$request['school_id']}'
					)
				)
				OR (p.pricing_applytype = '0')
				)
				AND a.agreement_startday <= '{$now}'
				AND a.agreement_endday >= '{$now}'
				AND a.company_id = '{$request['company_id']}'
				AND c.school_id = '{$request['school_id']}'
				AND c.class_status >= 0
				AND c.class_id='{$request['to_class_id']}'
				and sc.course_status<>'-1'
				";
        $pricingOne = $this->DataControl->selectOne($sql);
        if (!$pricingOne) {
            $this->error = true;
            $this->errortip = "课程不可使用";
            return false;
        }

        $pricingOne['unitsellprice'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);

        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = $this->LgStringSwitch("定价ID");
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_id";
        $field[1]["fieldname"] = $this->LgStringSwitch("课程ID");
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[2]["fieldstring"] = "class_id";
        $field[2]["fieldname"] = $this->LgStringSwitch("班级ID");
        $field[2]["show"] = 0;
        $field[2]["custom"] = 0;

        $field[3]["fieldstring"] = "class_branch";
        $field[3]["fieldname"] = $this->LgStringSwitch("班级编号");
        $field[3]["show"] = 1;
        $field[3]["custom"] = 0;

        $field[4]["fieldstring"] = "class_cnname";
        $field[4]["fieldname"] = $this->LgStringSwitch("班级名称");
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "class_enname";
        $field[5]["fieldname"] = $this->LgStringSwitch("班级别名");
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "course_branch";
        $field[6]["fieldname"] = $this->LgStringSwitch("课程别");
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "course_cnname";
        $field[7]["fieldname"] = $this->LgStringSwitch("课程别名称");
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "num";
        $field[8]["fieldname"] = $this->LgStringSwitch("人数");
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;

        $field[9]["fieldstring"] = "noNum";
        $field[9]["fieldname"] = $this->LgStringSwitch("未上课次");
        $field[9]["show"] = 1;
        $field[9]["custom"] = 0;

        $field[10]["fieldstring"] = "tuition_originalprice";
        $field[10]["fieldname"] = $this->LgStringSwitch("原价");
        $field[10]["show"] = 1;
        $field[10]["custom"] = 0;

        $field[11]["fieldstring"] = "tuition_sellingprice";
        $field[11]["fieldname"] = $this->LgStringSwitch("销售价");
        $field[11]["show"] = 1;
        $field[11]["custom"] = 0;

        $field[12]["fieldstring"] = "unitsellprice";
        $field[12]["fieldname"] = $this->LgStringSwitch("销售单价");
        $field[12]["show"] = 1;
        $field[12]["custom"] = 0;

        $field[13]["fieldstring"] = "course_classnum";
        $field[13]["fieldname"] = $this->LgStringSwitch("总课次数");
        $field[13]["show"] = 1;
        $field[13]["custom"] = 0;


        $s_field = array();
        $s_field[0]["fieldstring"] = "student_id";
        $s_field[0]["fieldname"] = $this->LgStringSwitch("学员ID");
        $s_field[0]["show"] = 0;
        $s_field[0]["custom"] = 0;

        $s_field[1]["fieldstring"] = "student_cnname";
        $s_field[1]["fieldname"] = $this->LgStringSwitch("学员中文名");
        $s_field[1]["show"] = 1;
        $s_field[1]["custom"] = 0;

        $s_field[2]["fieldstring"] = "student_branch";
        $s_field[2]["fieldname"] = $this->LgStringSwitch("学员编号");
        $s_field[2]["show"] = 1;
        $s_field[2]["custom"] = 0;

        $s_field[3]["fieldstring"] = "coursebalance_time";
        $s_field[3]["fieldname"] = $this->LgStringSwitch("剩余课次");
        $s_field[3]["show"] = 1;
        $s_field[3]["custom"] = 0;

        $s_field[4]["fieldstring"] = "coursebalance_figure";
        $s_field[4]["fieldname"] = $this->LgStringSwitch("剩余金额");
        $s_field[4]["show"] = 1;
        $s_field[4]["custom"] = 0;

        $s_field[5]["fieldstring"] = "needNum";
        $s_field[5]["fieldname"] = $this->LgStringSwitch("欠费课次");
        $s_field[5]["show"] = 1;
        $s_field[5]["custom"] = 0;

        $s_field[6]["fieldstring"] = "all_price";
        $s_field[6]["fieldname"] = $this->LgStringSwitch("欠费金额");
        $s_field[6]["show"] = 1;
        $s_field[6]["custom"] = 0;


        $studentList = json_decode(stripslashes($request['list']), true);

        $student_array = array();
        foreach ($studentList as $studentOne) {
            $sql = "select scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_unitexpend,scb.pricing_id
				  ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=0 and sch.hour_isfree=0 limit 0,1) as hourNoNum
				  from smc_student_coursebalance as scb
				  left join smc_class as c on c.course_id=scb.course_id
				  where scb.student_id='{$studentOne['student_id']}' and scb.company_id='{$request['company_id']}' and scb.school_id='{$request['school_id']}'
				  and c.class_id='{$request['class_id']}'
				  limit 0,1
				  ";
            $balanceOne = $this->DataControl->selectOne($sql);

            $needNum = $balanceOne['hourNoNum'] - $balanceOne['coursebalance_time'];
            $all_price = 0;
            if ($needNum > 0) {
                $all_price = $needNum * $balanceOne['coursebalance_unitexpend'];
            } else {
                $needNum = 0;
            }

            $student = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch,student_cnname", "student_id='{$studentOne['student_id']}'");

            $userArray = array();
            $userArray['student_id'] = $student['student_id'];
            $userArray['student_cnname'] = $student['student_cnname'];
            $userArray['student_branch'] = $student['student_branch'];
            $userArray['coursebalance_time'] = $balanceOne['coursebalance_time'];
            $userArray['coursebalance_figure'] = $balanceOne['coursebalance_figure'];
            $userArray['needNum'] = $needNum;
            $userArray['all_price'] = $all_price;

            $student_array[] = $userArray;

        }


        $sql = "select ch.hour_lessontimes
			  from smc_class_hour as ch
			  where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=1
			  order by ch.hour_lessontimes desc limit 0,1";
        $hourOne = $this->DataControl->selectOne($sql);
        if (!$hourOne['hour_lessontimes']) {
            $hourOne['hour_lessontimes'] = 0;
        }
        $sql = "select sc.course_classnum
			  ,(select COUNT(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=1) as hasnum
			  from smc_class as c
			  left join smc_course as sc on sc.course_id=c.course_id
			  where c.class_id='{$request['class_id']}' limit 0,1";
        $classOne = $this->DataControl->selectOne($sql);
        $data = array();
        $data['class_field'] = $field;
        $data['student_field'] = $s_field;
        $data['class'][] = $pricingOne;
        $data['student'] = $student_array;
        if ($hourOne['hour_lessontimes']) {
            if ($hourOne['hour_lessontimes'] + 1 < $classOne['course_classnum']) {
                $data['lessontimes'] = $hourOne['hour_lessontimes'] + 1;
            } else {
                $data['lessontimes'] = $classOne['course_classnum'];
            }

        } else {
            $data['lessontimes'] = 1;
        }
        $data['course_classnum'] = $pricingOne['course_classnum'];
        if ($pricingOne['lessontimes']) {
            $data['minimum'] = $pricingOne['lessontimes'];
        } else {
            $data['minimum'] = 0;
        }


        if ($classOne['hasnum'] > $pricingOne['hasnum']) {
            $data['is_need_free'] = 1;
        } else {
            $data['is_need_free'] = 0;
        }

        return $data;

    }


    function dismantleClass($request)
    {

//		$sql="select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.class_id='{$request['to_class_id']}' and ss.student_id='{$request['student_id']}'";
//		$sql="select ss.hourstudy_id from smc_student_hourstudy as ss left join smc_class as c on c.class_id=ss.class_id where ss.class_id='{$request['to_class_id']}' and ss.student_id='{$request['student_id']}'";

        $sql = "select ssc.class_id from smc_student_study as ssc where ssc.student_id='{$request['student_id']}' and ssc.school_id='{$request['school_id']}' and ssc.company_id='{$request['company_id']}' and ssc.class_id='{$request['to_class_id']}' and ssc.study_isreading='1'";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员不可入正在读的班级";
            return false;
        }

        $sql = "select ss.class_id,(
				SELECT
					count(sch.hour_id)
				FROM
					smc_class_hour AS sch
				WHERE
					sch.class_id = ss.class_id
				AND sch.hour_day >= ss.study_endday
				AND sch.hour_ischecking = '1'
				AND sch.hour_day NOT IN (
					(
						SELECT
							sch.hour_day
						FROM
							smc_student_hourstudy AS sh
						LEFT JOIN smc_class_hour AS scr ON scr.hour_id = sh.hour_id
						WHERE
							sh.class_id = ss.class_id
						AND sh.student_id = ss.student_id
						AND scr.hour_day = sch.hour_day
					)
				)
			) as hour_num
			  from smc_student_study as ss
			  where ss.student_id='{$request['student_id']}' and ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.class_id='{$request['to_class_id']}' and ss.study_isreading<>'1' having hour_num>'0'";

        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "学员不可回原班级";
            return false;
        }

        $dayOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_day", "class_id='{$request['to_class_id']}' and hour_lessontimes='{$request['lessontimes']}'");
        if (!$dayOne) {
            $this->error = true;
            $this->errortip = "无该课次";
            return false;
        }

        $sql = "select scb.course_id,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_unitexpend,scb.pricing_id,scb.coursebalance_unitrefund,sc.course_branch
			  ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=c.class_id and sch.hour_ischecking=0 and sch.hour_isfree=0 limit 0,1) as hourNoNum
			  from smc_student_coursebalance as scb
			  left join smc_class as c on c.course_id=scb.course_id
			  left join smc_course as sc on sc.course_id=c.course_id
			  where scb.student_id='{$request['student_id']}' and scb.company_id='{$request['company_id']}' and scb.school_id='{$request['school_id']}'
			  and c.class_id='{$request['class_id']}'
			  limit 0,1
			  ";
        $balanceOne = $this->DataControl->selectOne($sql);

        $sql = "select ch.hour_lessontimes
			  from smc_class_hour as ch
			  where ch.class_id='{$request['class_id']}' and ch.hour_ischecking=1
			  order by ch.hour_lessontimes desc limit 0,1";
        $hourOne = $this->DataControl->selectOne($sql);

        $sql = "select ch.hour_lessontimes
			  from smc_class_hour as ch
			  where ch.class_id='{$request['to_class_id']}' and ch.hour_ischecking=1
			  order by ch.hour_lessontimes desc limit 0,1";
        $to_hourOne = $this->DataControl->selectOne($sql);

        if (!$hourOne) {
            $hourOne['hour_lessontimes'] = 0;
        }
        if (!$to_hourOne) {
            $to_hourOne['hour_lessontimes'] = 0;
        }

        if ($to_hourOne['hour_lessontimes'] >= $request['lessontimes']) {
            $this->error = true;
            $this->errortip = "不可选择以上的课次";
            return false;
        }

        if ($hourOne['hour_lessontimes'] < $to_hourOne['hour_lessontimes']) {
            $this->error = true;
            $this->errortip = "不可转入该班级";
            return false;
        }
        if (($hourOne['hour_lessontimes'] + 1) < $request['lessontimes']) {
            $this->error = true;
            $this->errortip = "不可选择未上的课次";
            return false;
        }


        if ($request['lessontimes'] <= $hourOne['hour_lessontimes']) {
            $sql = "select ch.hour_lessontimes from smc_class_hour as ch where ch.class_id='{$request['to_class_id']}' and ch.hour_lessontimes<='{$hourOne['hour_lessontimes']}' and ch.hour_lessontimes>='{$request['lessontimes']}'";
            $timesList = $this->DataControl->selectClear($sql);

            if ($timesList) {
                $OrderHandleModel = new \Model\Smc\OrderHandleModel($request);
                $order_pid = $OrderHandleModel->createFreeOrder($request['student_id'], $balanceOne['course_id'], $balanceOne['course_branch'], $request['class_id'], strtotime($request['create_time']));

                $num = 0;
                foreach ($timesList as $one) {
                    if (!$this->DataControl->getFieldOne("smc_student_free_coursetimes", "coursetimes_id", "student_id='{$request['student_id']}' and course_id='{$balanceOne['course_id']}' and hour_lessontimes='{$one['hour_lessontimes']}' and is_use=0")) {
                        $times_data = array();
                        $times_data['order_pid'] = $order_pid;
                        $times_data['class_id'] = $request['class_id'];
                        $times_data['hour_lessontimes'] = $one['hour_lessontimes'];
                        $this->DataControl->insertData("smc_freehour_ordertimes", $times_data);

                        $data = array();
                        $data['course_id'] = $balanceOne['course_id'];
                        $data['student_id'] = $request['student_id'];
                        $data['school_id'] = $request['school_id'];
                        $data['order_pid'] = $order_pid;
                        $data['class_id'] = $request['class_id'];
                        $data['hour_lessontimes'] = $one['hour_lessontimes'];
                        $data['staffer_id'] = $request['staffer_id'];
                        if (isset($request['create_time']) && $request['create_time'] != '') {
                            $data['coursetimes_createtime'] = strtotime($request['create_time']);
                        } else {
                            $data['coursetimes_createtime'] = time();
                        }

                        $this->DataControl->insertData("smc_student_free_coursetimes", $data);
                        $num++;
                    }
                }

                $order_data = array();
                $order_data['order_alltimes'] = $num;
                $this->DataControl->updateData("smc_freehour_order", "order_pid='{$order_pid}' and company_id='{$request['company_id']}'", $order_data);

                if ($num > 0) {
                    $orderOne = $this->DataControl->getFieldOne("smc_freehour_order", "trading_pid", "order_pid='{$order_pid}' and company_id='{$request['company_id']}'");
                    $BalanceModel = new \Model\Smc\BalanceModel($request);
                    $BalanceModel->addCourseTimes($request['student_id'], $balanceOne['course_id'], $request['to_class_id'], $num, '拆班新增免费课次', '', $orderOne['trading_pid'], strtotime($request['create_time']));
                }
            }
        }

        $needNum = $balanceOne['hourNoNum'] - $balanceOne['coursebalance_time'];
        $all_price = 0;
        if ($needNum > 0) {
            $all_price = $needNum * $balanceOne['coursebalance_unitexpend'];
        } else {
            $needNum = 0;
        }

        if ($all_price > 0 && $needNum > 0) {
            $OrderHandleModel = new \Model\Smc\OrderHandleModel($request);
            $BalanceModel = new \Model\Smc\BalanceModel($request);

            $orderPid = $OrderHandleModel->makeUpFees($request['student_id'], $balanceOne['course_id'], $balanceOne['pricing_id'], $needNum, $balanceOne['coursebalance_unitexpend'], $balanceOne['coursebalance_unitrefund'], strtotime($request['create_time']));
            $orderOne = $this->DataControl->getFieldOne("smc_payfee_order", "trading_pid", "order_pid='{$orderPid}'");
            $BalanceModel->makeUpCourse($request['student_id'], $request['class_id'], $balanceOne['course_id'], $needNum, $balanceOne['coursebalance_unitexpend'], $orderOne['trading_pid'], strtotime($request['create_time']));
        } else {
            $orderOne = array();
        }

        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $TransactionModel->transferClass($request['student_id'], $request['class_id'], $request['to_class_id'], $request['reason_code'], $request['change_reason'], 2, $dayOne['hour_day'], '', $orderOne['trading_pid'], strtotime($request['create_time']));

        $TransactionModel->outClass($request['student_id'], $request['class_id'], 2, strtotime($request['create_time']), $orderOne['trading_pid']);

        return true;

    }

    function promotionNewClassOne($request)
    {
        $list = json_decode(stripslashes($request['list']), 1);
        $all_price = 0;
        if ($list) {
            foreach ($list as $one) {
                $surplusCourse = $this->getCoursePricing($one['course_id'], $request['company_id'], $request['school_id']);
                $all_price += $surplusCourse['tuition_sellingprice'] - $one['coupon_price'];
                $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$surplusCourse['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";

                $goodsList = $this->DataControl->selectClear($goods_sql);
                if ($goodsList) {
                    foreach ($goodsList as $val) {
                        if ($val['isfree'] != 1) {
                            $all_price += $val['sellingprice'];
                        }
                    }
                }

                $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$surplusCourse['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                $to_itemsList = $this->DataControl->selectClear($item_sql);

                if ($to_itemsList) {
                    foreach ($to_itemsList as $itemsOne) {
                        if ($itemsOne['isfree'] != 1) {
                            $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                        }
                    }
                }
                if ($all_price < 0) {
                    $all_price = 0;
                }
            }
        }
        $data = array();
        $data['price'] = $all_price;
        return $data;
    }

    function promotionNewClassList($request)
    {

        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = $this->LgStringSwitch("定价ID");
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_id";
        $field[1]["fieldname"] = $this->LgStringSwitch("课程ID");
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[4]["fieldstring"] = "course_cnname";
        $field[4]["fieldname"] = $this->LgStringSwitch("课程别");
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "tuition_originalprice";
        $field[5]["fieldname"] = $this->LgStringSwitch("原价");
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "tuition_sellingprice";
        $field[6]["fieldname"] = $this->LgStringSwitch("销售价");
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "unitsellprice";
        $field[7]["fieldname"] = $this->LgStringSwitch("销售单价");
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "coupon";
        $field[8]["fieldname"] = $this->LgStringSwitch("优惠券");
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;
        $field[8]["isSelectJuan"] = 1;


        $tem_array = array();
        $sql = "select s.student_id,s.student_cnname,s.student_branch,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.pricing_id
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_student_coursebalance as ssc on ssc.course_id=sc.course_id and ssc.student_id=s.student_id
              left join smc_student_enrolled as se on se.student_id=ss.student_id
              where ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and ss.study_isreading=1 and ss.student_id>0 and se.enrolled_status>=0
              group by ss.student_id
              order by ss.study_beginday DESC
              ";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        foreach ($studentList as $student) {
            $data = array();
//			$studentOne=$this->DataControl->getFieldOne("smc_student","student_id,student_cnname","student_id='{$student['student_id']}'");

            $studentOne = $this->DataControl->selectOne("select s.student_id,s.student_cnname,s.student_forwardprice,b.student_balance,b.student_withholdbalance from smc_student as s,smc_student_balance as b
WHERE b.student_id = s.student_id and b.school_id = '{$request['school_id']}' and s.student_id='{$student['student_id']}' and s.company_id='{$request['company_id']}'");

            $all_price = 0;
            $pricingList = array();
            $pricingOne = $this->getCoursePricing($request['to_course_id'], $request['company_id'], $request['school_id']);

            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_branch,course_cnname,course_nextid", "course_id='{$request['to_course_id']}' and company_id='{$request['company_id']}'");
            if ($pricingOne) {

                $tem_data = array();
                $tem_data['pricing_id'] = $pricingOne['pricing_id'];
                $tem_data['course_id'] = $courseOne['course_id'];
                $tem_data['course_cnname'] = $courseOne['course_cnname'];
                $tem_data['classInfo'] = '0/' . $pricingOne['tuition_buypiece'];
                $tem_data['tuition_originalprice'] = $pricingOne['tuition_originalprice'];
                $tem_data['tuition_sellingprice'] = $pricingOne['tuition_sellingprice'];
                $tem_data['unitsellprice'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);
                $tem_data['coupon'] = '';
                $tem_data['is_having'] = 0;
                $tem_data['is_discount'] = 0;

                $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$student['student_id']}' and course_id='{$request['to_course_id']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                if (!$courseBalanceOne) {
                    $policyOne = $this->getStuDiscountPrice($request, $student['student_id'], $request['to_course_id']);
                    if ($policyOne) {
                        $pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                        $tem_data['is_discount'] = 1;
                    }

                    $all_price += $pricingOne['tuition_sellingprice'];

                    $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";

                    $goodsList = $this->DataControl->selectClear($goods_sql);
                    if ($goodsList) {
                        foreach ($goodsList as $val) {
                            if ($val['isfree'] != 1) {
                                $all_price += $val['sellingprice'];
                            }
                        }
                    }

                    $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                    $to_itemsList = $this->DataControl->selectClear($item_sql);

                    if ($to_itemsList) {
                        foreach ($to_itemsList as $itemsOne) {
                            if ($itemsOne['isfree'] != 1) {
                                $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                            }
                        }
                    }
                } else {
                    $tem_data['is_having'] = 1;
                }

                $pricingList[] = $tem_data;

                if (isset($request['is_buy_next']) && $request['is_buy_next'] == '1') {
                    $relClassOne = $this->DataControl->selectOne("select course_nextid,course_id,course_cnname,course_presentednums,course_classnum,course_branch from smc_course where company_id='{$request['company_id']}' and course_id='{$courseOne['course_nextid']}' limit 0,1");
                    if ($relClassOne) {
                        $next_pricingOne = $this->getCoursePricing($courseOne['course_nextid'], $request['company_id'], $request['school_id']);
                        if ($next_pricingOne) {

                            $tem_data = array();
                            $tem_data['pricing_id'] = $next_pricingOne['pricing_id'];
                            $tem_data['course_id'] = $relClassOne['course_id'];
                            $tem_data['course_cnname'] = $relClassOne['course_cnname'];
                            $tem_data['classInfo'] = '0/' . $next_pricingOne['tuition_buypiece'];
                            $tem_data['tuition_originalprice'] = $next_pricingOne['tuition_originalprice'];
                            $tem_data['tuition_sellingprice'] = $next_pricingOne['tuition_sellingprice'];
                            $tem_data['unitsellprice'] = ceil($next_pricingOne['tuition_sellingprice'] / $next_pricingOne['tuition_buypiece']);
                            $tem_data['coupon'] = '';
                            $tem_data['is_having'] = 0;
                            $tem_data['is_discount'] = 0;

                            $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$student['student_id']}' and course_id='{$courseOne['course_nextid']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                            if (!$courseBalanceOne) {

                                $policyOne = $this->getStuDiscountPrice($request, $student['student_id'], $courseOne['course_nextid']);
                                if ($policyOne) {
                                    $next_pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                                    $tem_data['is_discount'] = 1;
                                }

                                $all_price += $next_pricingOne['tuition_sellingprice'];
                                $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
		  from smc_fee_pricing_products as fpp
		  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
		  left join erp_goods as g on g.goods_id=fpp.goods_id
		  where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
		  ";

                                $goodsList = $this->DataControl->selectClear($goods_sql);
                                if ($goodsList) {
                                    foreach ($goodsList as $val) {
                                        if ($val['isfree'] != 1) {
                                            $all_price += $val['sellingprice'];
                                        }
                                    }
                                }

                                $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
		  from smc_fee_pricing_items as fpi
		  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
		  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
		  where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
		  group by fpi.feeitem_branch
		  ";
                                $to_itemsList = $this->DataControl->selectClear($item_sql);

                                if ($to_itemsList) {
                                    foreach ($to_itemsList as $itemsOne) {
                                        if ($itemsOne['isfree'] != 1) {
                                            $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                        }
                                    }
                                }
                            } else {
                                $tem_data['is_having'] = 1;
                            }
                            $pricingList[] = $tem_data;
                        }
                    }
                }
                if ($all_price > 0) {
                    $data = array();
                    $data['list'] = $pricingList;
                    $data['field'] = $field;
                    $userArray = array();
                    $userArray['student_id'] = $studentOne['student_id'];
                    $userArray['student_cnname'] = $studentOne['student_cnname'];
                    $userArray['all_price'] = $all_price;
                    $userArray['student_balance'] = $studentOne['student_balance'] + $studentOne['student_withholdbalance'];
                    $userArray['student_forwardprice'] = $studentOne['student_forwardprice'];
                    $data['userInfo'] = $userArray;
                } else {
                    $data = array();
                    $data['list'] = $pricingList;
                    $data['field'] = $field;
                    $userArray = array();
                    $userArray['student_id'] = $studentOne['student_id'];
                    $userArray['student_cnname'] = $studentOne['student_cnname'];
                    $userArray['all_price'] = 0;
                    $userArray['student_balance'] = $studentOne['student_balance'] + $studentOne['student_withholdbalance'];
                    $userArray['student_forwardprice'] = $studentOne['student_forwardprice'];
//						$userArray['errortip']='学员升入入班级不符';
                    $data['userInfo'] = $userArray;
                }

                $tem_array[] = $data;
            }

        }

        return $tem_array;

    }

    function promotion($request)
    {
        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $ClassModel = new \Model\Smc\ClassModel($request);
        $RegistrationModel = new \Model\Smc\RegistrationModel($request);
        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $classOne = $ClassModel->classAdd($request);
        if (!$classOne) {
            $this->error = true;
            $this->errortip = $ClassModel->errortip;
            return false;
        }
        //当前班级的课程
        $courseOne = $this->DataControl->getFieldOne("smc_class", "course_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and class_id='{$request['class_id']}'");

        $cOne = $this->DataControl->getFieldOne("smc_class", "class_cnname,class_branch", "class_id='{$classOne['class_id']}'");
        //新建班级的课程信息
        $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);
        if (!$pricingOne) {
            $this->error = true;
            $this->errortip = "选择课程不在协议内";
            return false;
        }


        $studentList = json_decode(stripslashes($request['list']), 1);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "班级内没有学员";
            return false;
        }
        $goods_all_price = 0;
        $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";
        $goodsList = $this->DataControl->selectClear($sql);
        if (!$goodsList) {
            $goodsList = array();
        } else {
            foreach ($goodsList as $goodsOne) {
                if ($goodsOne['isfree'] != 1) {
                    $goods_all_price += $goodsOne['sellingprice'];
                }
            }
        }

        $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
        $itemsList = $this->DataControl->selectClear($item_sql);
        if (!$itemsList) {
            $itemsList = array();
        } else {
            foreach ($itemsList as $itemsOne) {
                if ($itemsOne['isfree'] != 1) {
                    $goods_all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                }
            }
        }

        $tem_return_data = array();

        $data = array();
        $data['is_forward'] = 1;
        foreach ($studentList as $val) {
            $bool = $BalanceModel->carryOver($val['student_id'], $courseOne['course_id'], $request['class_id'], 2, strtotime($request['create_time']));
            $all_price = 0;
            if ($bool || $bool == 'noCarry') {
                $courseArray = array();
                $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$val['student_id']}' and course_id='{$pricingOne['course_id']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                if (!$courseBalanceOne) {
                    $courseData = array();
                    $policyOne = $this->getStuDiscountPrice($request, $val['student_id'], $request['course_id']);
                    if ($policyOne) {
                        $pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                        $courseData['is_discount'] = 1;
                    } else {
                        $courseData['is_discount'] = 0;
                    }

                    $all_price += $goods_all_price;

                    $courseData['agreement_id'] = $pricingOne['agreement_id'];
                    $courseData['pricing_id'] = $pricingOne['pricing_id'];
                    $courseData['course_id'] = $pricingOne['course_id'];
                    $courseData['sellingprice'] = $pricingOne['tuition_sellingprice'];
                    $courseData['num'] = $pricingOne['tuition_buypiece'];
                    $courseData['class_id'] = $classOne['class_id'];
                    $courseData['starttime'] = "";
                    $courseData['discount_id'] = "";
                    $courseData['market_price'] = "";
                    $courseData['deductionmethod'] = 2;
                    $courseData['from'] = 1;
                    $courseData['goodsList'] = $goodsList;
                    $courseData['itemsList'] = $itemsList;
                    $courseData['couponList'] = $val['to_couponList'];

                    if (isset($val['to_couponList']) && $val['to_couponList'] != '') {
                        foreach ($val['to_couponList'] as $toOne) {
                            if ($toOne['coupon_price'] > 0) {
                                $data['is_forward'] = 0;
                                $all_price -= $toOne['coupon_price'];
                                $courseData['sellingprice'] -= $toOne['coupon_price'];
                            }
                        }
                    }

                    $courseArray[] = $courseData;

                    $all_price += $pricingOne['tuition_sellingprice'];
                }

                if (isset($request['is_buy_next']) && $request['is_buy_next'] == 1) {
                    $next_course_id = $this->DataControl->getFieldOne("smc_course", "course_nextid", "course_id='{$pricingOne['course_id']}' and company_id='{$request['company_id']}'");
                    $next_pricingOne = $this->getCoursePricing($next_course_id['course_nextid'], $request['company_id'], $request['school_id']);
                    if ($next_course_id && $next_pricingOne) {

                        $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$val['student_id']}' and course_id='{$next_course_id['course_nextid']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                        if (!$courseBalanceOne) {
                            $next_courseData = array();
                            $policyOne = $this->getStuDiscountPrice($request, $val['student_id'], $next_course_id['course_nextid']);
                            if ($policyOne) {
                                $next_pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                                $next_courseData['is_discount'] = 1;
                            } else {
                                $next_courseData['is_discount'] = 0;
                            }


                            $next_courseData['agreement_id'] = $next_pricingOne['agreement_id'];
                            $next_courseData['pricing_id'] = $next_pricingOne['pricing_id'];
                            $next_courseData['course_id'] = $next_pricingOne['course_id'];
                            $next_courseData['sellingprice'] = $next_pricingOne['tuition_sellingprice'];
                            $next_courseData['num'] = $next_pricingOne['tuition_buypiece'];
                            $next_courseData['class_id'] = "";
                            $next_courseData['starttime'] = "";
                            $next_courseData['discount_id'] = "";
                            $next_courseData['market_price'] = "";
                            $next_courseData['deductionmethod'] = 2;

                            $all_price += $next_pricingOne['tuition_sellingprice'];

                            $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";
                            $next_goodsList = $this->DataControl->selectClear($sql);
                            if (!$next_goodsList) {
                                $next_goodsList = array();
                            } else {
                                foreach ($next_goodsList as $goodsOne) {
                                    if ($goodsOne['isfree'] != 1) {
                                        $all_price += $goodsOne['sellingprice'];
                                    }
                                }
                            }
                            $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                            $next_itemsList = $this->DataControl->selectClear($item_sql);

                            if (!$next_itemsList) {
                                $next_itemsList = array();
                            } else {
                                foreach ($next_itemsList as $itemsOne) {
                                    if ($itemsOne['isfree'] != 1) {
                                        $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                    }
                                }
                            }

                            $next_courseData['goodsList'] = $next_goodsList;
                            $next_courseData['itemsList'] = $next_itemsList;
                            $next_courseData['couponList'] = $val['next_couponList'];
                            if (isset($val['next_couponList']) && $val['next_couponList'] != '') {
                                foreach ($val['next_couponList'] as $nextOne) {
                                    if ($nextOne['coupon_price'] > 0) {
                                        $data['is_forward'] = 0;
                                        $all_price -= $nextOne['coupon_price'];
                                        $next_courseData['sellingprice'] -= $nextOne['coupon_price'];
                                    }
                                }
                            }

                            $courseArray[] = $next_courseData;
                        }
                    }
                }


                $data['company_id'] = $request['company_id'];
                $data['school_id'] = $request['school_id'];
                $data['staffer_id'] = $request['staffer_id'];
                $data['student_id'] = $val['student_id'];

                $data['coupon_price'] = 0;
                $data['market_price'] = 0;
                $data['giveforwardprice'] = 0;
                $data['all_market_price'] = 0;
                $data['create_time'] = $request['create_time'];


                if (isset($val['all_coupon_price']) && $val['all_coupon_price'] > 0) {
                    $data['all_coupon_price'] = $val['all_coupon_price'];
                    $data['coupon_id'] = $val['coupon_id'];

                    $data['is_forward'] = 0;
                    $all_price -= $val['all_coupon_price'];
                }
                $data['price'] = $all_price;

                $return_data = array();

                $stuOne = $this->DataControl->getFieldOne("smc_student", "student_branch,student_cnname", "student_id='{$val['student_id']}'");

                $return_data['student_id'] = $val['student_id'];
                $return_data['student_cnname'] = $stuOne['student_cnname'];
                $return_data['student_branch'] = $stuOne['student_branch'];
                $data['list'] = json_encode($courseArray, JSON_UNESCAPED_UNICODE);

                if ($all_price > 0) {
                    $data['list'] = json_encode($courseArray, JSON_UNESCAPED_UNICODE);
                    $order_pid = $RegistrationModel->createOrder($data);
                    $orderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid='{$order_pid}'");
                    $return_data['order_pid'] = $order_pid;
                    $return_data['order_paymentprice'] = $orderOne['order_paymentprice'];
                    $return_data['discount_price'] = $orderOne['order_coupon_price'] + $orderOne['order_market_price'];
                    $return_data['order_paidprice'] = $orderOne['order_paidprice'];
                    $return_data['order_arrearageprice'] = $orderOne['order_arrearageprice'];


                } else {

                    $return_data['order_pid'] = '--';
                    $return_data['order_paymentprice'] = '0';
                    $return_data['discount_price'] = '0';
                    $return_data['order_paidprice'] = '0';
                    $return_data['order_arrearageprice'] = '0';
                }

                $sql = "select ss.student_id
					  from smc_student_study as ss
					  left join smc_class as c on c.class_id=ss.class_id
					  where ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}' and ss.student_id='{$val['student_id']}'
					  and c.course_id='{$request['course_id']}' and ss.study_isreading=1";
                if (!$this->DataControl->selectOne($sql)) {
                    $TransactionModel->promotionClass($val['student_id'], $request['class_id'], $classOne['class_id'], $request['reason_code'], '升班', $bool, strtotime($request['create_time']));
                }
                $tem_return_data[] = $return_data;
            }
        }
        $data = array();
        $data['data'] = $tem_return_data;
        $data['class_cnname'] = $cOne['class_cnname'];
        $data['class_branch'] = $cOne['class_branch'];

        return $data;
    }

    function goBackToSchool($request)
    {
        $logOne = $this->DataControl->getFieldOne("smc_student_outlog", "outlog_id", "student_id='{$request['student_id']}'");
        if ($logOne) {
            $this->error = true;
            $this->errortip = "学员已注销,不可流失复学";
            return false;
        }
        $enrolledOne = $this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and enrolled_status='-1'");
        if (!$enrolledOne) {
            $this->error = true;
            $this->errortip = "该学员无流失数据,不可流失复学";
            return false;
        }

        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $data = array();
        $data['enrolled_status'] = 0;
        $data['enrolled_updatatime'] = time();
        if ($this->DataControl->updateData("smc_student_enrolled", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and enrolled_status='-1'", $data)) {
            $TransactionModel->backToSchool($request['student_id'], '', '', $request['from_school']);
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }

    }

    function classLoss($request)
    {
        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $sql = "select sss.study_id from smc_student_study as sss
			  left join smc_class as c on c.class_id=sss.class_id
			  left join smc_student_coursebalance as scb on scb.course_id=c.course_id and scb.student_id=sss.student_id and scb.school_id='{$request['school_id']}'
			  where sss.student_id='{$request['student_id']}' and sss.class_id='{$request['class_id']}' and (scb.coursebalance_figure>0 or scb.coursebalance_time>0)
				";
        $courseOne = $this->DataControl->selectOne($sql);

        if ($courseOne) {
            $this->error = true;
            $this->errortip = "学员班级流失条件不符";
            return false;
        }

//		$classOne=$this->DataControl->getFieldOne("smc_class","class_status","class_id='{$request['class_id']}'");
//		if($classOne['class_status']<0){
//			$this->error = true;
//			$this->errortip = "班级流失条件不符";
//			return false;
//		}
        $day = $request['create_time'] ? $request['create_time'] : date("Y-m-d");

//        $sql="select hour_id from smc_class_hour where class_id='{$request['class_id']}' and hour_day<'{$day}' and hour_ischecking=0 and hour_iswarming<>'1'";
//        if($this->DataControl->selectOne($sql)){
//            $this->error = true;
//            $this->errortip = "班级考勤未完成！";
//            return false;
//        }

        $TransactionModel->outClass($request['student_id'], $request['class_id'], 2, strtotime($request['create_time']));
        $TransactionModel->classLoss($request['student_id'], $request['class_id'], $request['reason_code'], $request['reason'], strtotime($request['create_time']));

        return true;
    }

    function stuLoss($request)
    {
        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $TransactionModel = new \Model\Smc\TransactionModel($request);

        if (!isset($request['category_note']) || $request['category_note'] == '') {
            $request['category_note'] = '暂不知情';
        }
        if ($request['category_note'] == '转校流失') {
            $sql = "select study_id 
                from smc_student_study
                where student_id='{$request['student_id']}' 
                and school_id<>'{$request['school_id']}' 
                and study_isreading=1";
            if (!$this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学生未在他校就读,不可转校流失";
                return false;
            }
        }

        if (!isset($request['stuchange_code']) || $request['stuchange_code'] == 'C02') {
            $sql = "select trading_id from smc_student_trading where trading_status='0' and tradingtype_code<>'Accountrefund' and student_id='{$request['student_id']}' and school_id='{$request['school_id']}'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学员存在未完成交易不可流失";
                return false;
            }

//		$sql="select refund_id from smc_refund_order where student_id='{$request['student_id']}' and school_id='{$request['school_id']}' and refund_status<'4' and refund_status>=0";
//		if($this->DataControl->selectOne($sql)){
//			$this->error = true;
//			$this->errortip = "学员存在未完成退费订单不可流失";
//			return false;
//		}

            $sql = "select s.student_id
              ,(select COUNT(po.order_id) from smc_payfee_order as po where  po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<>4 and po.order_status>0) as num
              from smc_student as s
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' limit 0,1";
            $studentPrice = $this->DataControl->selectOne($sql);

            if ($studentPrice['num'] > 0) {
                $this->error = true;
                $this->errortip = "学员欠费不可流失";
                return false;
            }
            $sql = "select scb.course_id
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scb.student_id=scf.student_id
              left join smc_course as sc on scb.course_id=sc.course_id
              left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
              where sc.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_time>0 or scb.coursebalance_figure>0)
              order by scb.course_id DESC
        ";

            $classList = $this->DataControl->selectClear($sql);
            if ($classList) {
                $this->error = true;
                $this->errortip = "学员存在课程";
                return false;
            }

            $sql = "select study_id from smc_student_study where student_id='{$request['student_id']}' and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and study_isreading=1";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学员存在有效班级";
                return false;
            }

            $sql = "select erpgoods_id from smc_student_erpgoods where student_id='{$request['student_id']}' and school_id='{$request['school_id']}' and erpgoods_isreceive=0 and erpgoods_isrefund=0";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学员存在未领用教材";
                return false;
            }

            $sql = "select itemtimes_id from smc_student_itemtimes where student_id='{$request['student_id']}' and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and (itemtimes_figure>'0' or itemtimes_number>'0')";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学员存在杂费余额";
                return false;
            }

            $sql = "select coursecatbalance_id from smc_student_coursecatbalance where student_id='{$request['student_id']}' and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and (coursecatbalance_figure>'0' or coursecatbalance_time>'0')";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学生存在预收余额";
                return false;
            }


            $sql = "select cs.stustatus_isenschool,scl.changelog_day,scl.changelog_id
              from smc_student_changelog as scl,smc_code_stuchange as cs 
              where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=1 and scl.student_id='{$request['student_id']}' and scl.school_id='{$this->school_id}' 
              order by scl.changelog_day desc,scl.changelog_id desc";

            $logOne = $this->DataControl->selectOne($sql);

            if ($logOne['stustatus_isenschool'] == 1) {
                $starttime = strtotime($logOne['changelog_day']);

                $sql = "select cc.coursetype_id,cc.coursetype_cnname,cc.coursetype_branch
              ,ifnull((select scl.stuchange_code from smc_student_changelog as scl,smc_code_stuchange as cs 
              where scl.stuchange_code=cs.stuchange_code and cs.stuchange_code in ('C04','D04') and scl.student_id='{$request['student_id']}' and scl.school_id='{$this->school_id}' and scl.coursetype_id=sc.coursetype_id and scl.changelog_day>='{$logOne['changelog_day']}' and scl.changelog_id>'{$logOne['changelog_id']}' order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'') as stuchange_code
              from smc_student_coursebalance as scb,smc_course as sc,smc_code_coursetype as cc 
              where scb.course_id=sc.course_id and sc.coursetype_id=cc.coursetype_id and scb.school_id='{$this->school_id}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_updatatime=0 or scb.coursebalance_updatatime>='{$starttime}') and scb.coursebalance_figure=0 and scb.coursebalance_time=0 and cc.coursetype_isregistercalc=1
              group by sc.coursetype_id
              having stuchange_code='' or stuchange_code='D04'
              ";

                $typeList = $this->DataControl->selectClear($sql);

                if ($typeList) {
                    foreach ($typeList as $val) {
                        $TransactionModel->courseTypeLoss($request['student_id'], 'C04', $val['coursetype_id'], $request['reason_code'], $request['reason'], $request['category_note'], strtotime($request['create_time']));
                    }
                }
            }

            $BalanceModel->loss($request['student_id'], $request['is_empty_balance'], $request['is_empty_forward'], strtotime($request['create_time']));

            $TransactionModel->loss($request['student_id'], $request['reason_code'], $request['reason'], $request['category_note'], strtotime($request['create_time']));

            $sql = "select fp.policy_id from smc_fee_policy_student as ps left join smc_fee_policy as fp on fp.policy_id=ps.policy_id where ps.student_id='{$request['student_id']}' and fp.policy_class='1'";
            $policyList = $this->DataControl->selectClear($sql);
            if ($policyList) {
                foreach ($policyList as $val) {
                    $this->DataControl->delData("smc_fee_policy_student", "student_id='{$request['student_id']}' and policy_id='{$val['policy_id']}'");
                }
            }

            $data = array();
            $data['enrolled_status'] = '-1';
            $data['enrolled_updatatime'] = time();
            $data['enrolled_leavetime'] = strtotime($request['create_time']) ? strtotime($request['create_time']) : time();
            $this->DataControl->updateData("smc_student_enrolled", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}'", $data);

            $data = array();
            $data['coursebalance_updatatime'] = time();
            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$request['student_id']}' and school_id='{$this->school_id}'", $data);

            return true;
        } elseif ($request['stuchange_code'] == 'C04') {
            $sql = "select ss.study_id 
                  from smc_student_study as ss,smc_class as c,smc_course as sc 
                  where ss.class_id=c.class_id and c.course_id=sc.course_id and ss.school_id='{$this->school_id}' and student_id='{$request['student_id']}' and sc.coursetype_id='{$request['coursetype_id']}' and ss.study_isreading=1";

            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学生在该班组在读,不可流失";
                return false;
            }

            $sql = "select s.student_id
              ,(select COUNT(po.order_id) from smc_payfee_order as po,smc_payfee_order_course as poc,smc_course as co  where po.order_pid=poc.order_pid and poc.course_id=co.course_id and  po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and po.order_status<>4 and po.order_status>0 and co.coursetype_id='{$request['coursetype_id']}') as num
              from smc_student as s
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' limit 0,1";
            $studentPrice = $this->DataControl->selectOne($sql);

            if ($studentPrice['num'] > 0) {
                $this->error = true;
                $this->errortip = "学员班组欠费不可流失";
                return false;
            }
            $sql = "select scb.course_id
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scb.student_id=scf.student_id
               left join smc_course as sc on scb.course_id=sc.course_id
              left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
              where sc.company_id='{$request['company_id']}' and sse.school_id='{$request['school_id']}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_time>0 or scb.coursebalance_figure>0) and sc.coursetype_id='{$request['coursetype_id']}'
              order by scb.course_id DESC
        ";

            $classList = $this->DataControl->selectClear($sql);
            if ($classList) {
                $this->error = true;
                $this->errortip = "学员存在课程,不可流失";
                return false;
            }


            $sql = "select si.itemtimes_id from smc_student_itemtimes as si,smc_course as sc 
                  where si.course_id=sc.course_id and si.student_id='{$request['student_id']}' and si.company_id='{$request['company_id']}' and si.school_id='{$request['school_id']}' and (si.itemtimes_figure>'0' or si.itemtimes_number>'0') and sc.coursetype_id='{$request['coursetype_id']}'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学员存在杂费余额,不可流失";
                return false;
            }

            $sql = "select sca.coursecatbalance_id from smc_student_coursecatbalance as sca,smc_code_coursecat as cc 
                  where sca.coursecat_id=cc.coursecat_id and sca.student_id='{$request['student_id']}' and sca.company_id='{$request['company_id']}' and sca.school_id='{$request['school_id']}' and (sca.coursecatbalance_figure>'0' or sca.coursecatbalance_time>'0') and cc.coursetype_id='{$request['coursetype_id']}'";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "学生存在预收余额,不可流失";
                return false;
            }

            $categoryOne = $this->DataControl->getFieldOne("smc_code_stuchange_category", "category_id,category_code", " stuchange_code='C04' and category_note='{$request['category_note']}'");
            if ($categoryOne && $categoryOne['category_code'] == '班组流失-009') {
                $sql = "select c.course_id,a.student_id,count(a.hourstudy_id) as study_num,c.course_classnum
                    from smc_student_hourstudy a
                    left join smc_class b on a.class_id=b.class_id
                    left join smc_course c on b.course_id=c.course_id
                    left join smc_class_hour d on d.hour_id=a.hour_id
                    where c.course_isgraduated=1 
                    and d.hour_isfree=0
                    and a.student_id='{$request['student_id']}'
                    and b.school_id='{$request['school_id']}'
                    and c.coursetype_id='{$request['coursetype_id']}'
                    group by c.course_id
                    having study_num>=course_classnum ";
                if (!$this->DataControl->selectClear($sql)) {
                    $this->error = true;
                    $this->errortip = "学生未读完毕业课程,流失原因不可选择毕业";
                    return false;
                }
            }

            $TransactionModel->courseTypeLoss($request['student_id'], $request['stuchange_code'], $request['coursetype_id'], $request['reason_code'], $request['reason'], $request['category_note'], strtotime($request['create_time']));

            return true;
        } else {
            $this->error = true;
            $this->errortip = "无该模式流失";
            return false;
        }

    }

    function studAutoLost($request)
    {
        $sql = "select m.company_id,m.school_id,m.student_id,m.coursetype_id
        ,(select ifnull(sum(student_balance+student_withholdbalance),0) from smc_student_balance where student_id=m.student_id and school_id=m.school_id) as left_balance
        ,ifnull((SELECT max( FROM_UNIXTIME( timelog_time, '%Y-%m-%d' )) FROM smc_student_coursebalance_timelog x, smc_course y 
         where x.course_id = y.course_id AND x.student_id = m.student_id AND x.school_id = m.school_id AND y.coursetype_id = m.coursetype_id ),CURDATE()) AS hour_day1
        ,ifnull((select max(hour_day) from smc_student_hourstudy x left join smc_class_hour y on x.hour_id=y.hour_id left join smc_class z on z.class_id=y.class_id left join smc_course w on w.course_id=z.course_id
         where x.student_id=m.student_id and w.coursetype_id=m.coursetype_id and z.school_id=m.school_id),'1970-01-01') as hour_day2
        ,ifnull((SELECT x.stuchange_code FROM smc_student_changelog x, smc_code_stuchange y WHERE x.stuchange_code = y.stuchange_code AND y.stuchange_type = 0 
         AND x.student_id = m.student_id AND x.school_id = m.school_id AND x.coursetype_id = m.coursetype_id ORDER BY x.changelog_day DESC, x.changelog_id DESC LIMIT 0,1),'') AS stuchange_code
        , ifnull((SELECT y.stustatus_isenschool FROM smc_student_changelog x,smc_code_stuchange y WHERE x.stuchange_code = y.stuchange_code AND y.stuchange_type = 1 
         AND x.student_id = m.student_id AND x.school_id = m.school_id ORDER BY x.changelog_day DESC, x.changelog_id DESC LIMIT 0, 1 ),'1') AS stustatus_isenschool
        from (
        select a.company_id,a.school_id,a.student_id,b.coursetype_id
        ,sum(a.coursebalance_figure) as left_price
        ,sum(a.coursebalance_time) as left_time
        from smc_student_coursebalance a
        left join smc_course b on a.course_id=b.course_id
        left join smc_school c on a.school_id=c.school_id
        left join smc_student_enrolled d on d.school_id=a.school_id and d.student_id=a.student_id
        where a.company_id=8888
        and b.coursetype_id=65
        and c.school_isclose=0
        and c.school_istest=0
        and d.enrolled_status>=0
        -- and a.student_id=181565
        group by a.school_id,a.student_id,b.coursetype_id
        having left_price=0 
        and left_time=0
        ) m
        where 1
        and not exists(select 1 from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id 
        and x.student_id=m.student_id and x.school_id=m.school_id and z.coursetype_id=m.coursetype_id and study_isreading=1)
        having left_balance<1000
        and stuchange_code <> 'C04' 
        and stustatus_isenschool = 1
        and hour_day1 <= DATE_SUB(CURDATE(),INTERVAL 22 day)
        and hour_day2 <= DATE_SUB(CURDATE(),INTERVAL 22 day)";

        $autoLostList = $this->DataControl->selectClear($sql);
        if (!$autoLostList) {
            return false;
        }

//        var_dump($autoLostList);die;

        $succ = 0;
        $fail = 0;
        $comment = array();
        foreach ($autoLostList as $lostOne) {
            if ($succ + $fail >= 80) {
                break;
            }
            $datearray = array();
            $datearray['company_id'] = $lostOne['company_id'];
            $datearray['school_id'] = $lostOne['school_id'];
            $datearray['coursetype_id'] = $lostOne['coursetype_id'];
            $datearray['student_id'] = $lostOne['student_id'];

            $sql = "select ss.study_id 
            from smc_student_study as ss,smc_class as c,smc_course as sc 
            where ss.class_id=c.class_id and c.course_id=sc.course_id 
                and ss.school_id='{$lostOne['school_id']}' and student_id='{$lostOne['student_id']}' 
                and sc.coursetype_id='{$lostOne['coursetype_id']}' and ss.study_isreading=1";

            if ($this->DataControl->selectOne($sql)) {
                $datearray['is_success'] = 0;
                $datearray['error_code'] = "学生在该班组在读,不可流失";
                $comment[] = $datearray;
                $fail++;
                continue;
            }

            $sql = "select s.student_id,
            (select COUNT(po.order_id) 
                from smc_payfee_order as po,smc_payfee_order_course as poc,smc_course as co 
                where po.order_pid=poc.order_pid and poc.course_id=co.course_id and  po.student_id=s.student_id 
                and po.company_id='{$lostOne['company_id']}' and po.school_id='{$lostOne['school_id']}' 
                and po.order_status<>4 and po.order_status>0 and co.coursetype_id='{$lostOne['coursetype_id']}') as num
            from smc_student as s
            left join smc_student_enrolled as sse on sse.student_id=s.student_id
            where s.student_id='{$lostOne['student_id']}' and s.company_id='{$lostOne['company_id']}' 
            and sse.school_id='{$lostOne['school_id']}' limit 0,1";
            $studentPrice = $this->DataControl->selectOne($sql);

            if ($studentPrice['num'] > 0) {
                $datearray['is_success'] = 0;
                $datearray['error_code'] = "学员班组欠费不可流失";
                $comment[] = $datearray;
                $fail++;
                continue;
            }

            $sql = "select scb.course_id
            from smc_student_coursebalance as scb
            left join smc_student_courseforward as scf on scf.course_id=scb.course_id and scb.student_id=scf.student_id
            left join smc_course as sc on scb.course_id=sc.course_id
            left join smc_student_enrolled as sse on sse.student_id=scb.student_id and sse.school_id=scb.school_id
            where sc.company_id='{$lostOne['company_id']}' and sse.school_id='{$lostOne['school_id']}' 
            and scb.student_id='{$lostOne['student_id']}' and sc.coursetype_id='{$lostOne['coursetype_id']}' 
            and (scb.coursebalance_time>0 or scb.coursebalance_figure>0)";

            $coursebalanceList = $this->DataControl->selectClear($sql);
            if ($coursebalanceList) {
                $datearray['is_success'] = 0;
                $datearray['error_code'] = "学员存在课程,不可流失";
                $comment[] = $datearray;
                $fail++;
                continue;
            }

            $sql = "select si.itemtimes_id from smc_student_itemtimes as si,smc_course as sc 
                where si.course_id=sc.course_id and si.student_id='{$lostOne['student_id']}' 
                  and si.company_id='{$lostOne['company_id']}' and si.school_id='{$lostOne['school_id']}' 
                  and (si.itemtimes_figure>'0' or si.itemtimes_number>'0') and sc.coursetype_id='{$lostOne['coursetype_id']}'";
            if ($this->DataControl->selectOne($sql)) {
                $datearray['is_success'] = 0;
                $datearray['error_code'] = "学员存在杂费余额,不可流失";
                $comment[] = $datearray;
                $fail++;
                continue;
            }

            $sql = "select sca.coursecatbalance_id from smc_student_coursecatbalance as sca,smc_code_coursecat as cc 
                where sca.coursecat_id=cc.coursecat_id and sca.student_id='{$lostOne['student_id']}' 
                  and sca.company_id='{$lostOne['company_id']}' and sca.school_id='{$lostOne['school_id']}' 
                  and (sca.coursecatbalance_figure>'0' or sca.coursecatbalance_time>'0') and cc.coursetype_id='{$lostOne['coursetype_id']}'";
            if ($this->DataControl->selectOne($sql)) {
                $datearray['is_success'] = 0;
                $datearray['error_code'] = "学生存在预收余额,不可流失";
                $comment[] = $datearray;
                $fail++;
                continue;
            }

            $like = date("Ymd", time());
            $changeInfo = $this->DataControl->selectOne("select change_pid from smc_student_change where change_pid like '{$like}%' and company_id='{$lostOne['company_id']}' order by change_pid DESC limit 0,1");

            $data = array();
            $data['company_id'] = $lostOne['company_id'];
            $data['student_id'] = $lostOne['student_id'];
            if ($changeInfo) {
                $data['change_pid'] = $changeInfo['change_pid'] + 1;
            } else {
                $data['change_pid'] = $like . '000001';
            }
            $data['from_stuchange_code'] = 'C04';
            $data['from_school_id'] = $lostOne['school_id'];
            $data['change_status'] = 1;
            $data['change_day'] = date("Y-m-d");
            $data['reason_code'] = '056';
            $data['change_reason'] = '班组流失';
            $data['change_workername'] = '管理员';
            $data['change_createtime'] = time();
            $this->DataControl->insertData("smc_student_change", $data);

            $log_data = array();
            $log_data['change_pid'] = $data['change_pid'];
            $log_data['company_id'] = $lostOne['company_id'];
            $log_data['student_id'] = $lostOne['student_id'];
            $log_data['coursetype_id'] = $lostOne['coursetype_id'];
            $log_data['changelog_type'] = 0;
            $log_data['stuchange_code'] = 'C04';
            $log_data['school_id'] = $lostOne['school_id'];
            $log_data['changelog_category'] = '自动流失';
            $log_data['changelog_note'] = '班组流失';
            $log_data['changelog_day'] = date("Y-m-d");
            $log_data['staffer_id'] = 2;
            $log_data['changelog_createtime'] = time();
            $this->DataControl->insertData("smc_student_changelog", $log_data);

            $succ++;
            $datearray['is_success'] = 1;
            $datearray['error_code'] = "自动班组流失成功";
            $comment[] = $datearray;
        }

        $res = array();
        $res['success'] = $succ;
        $res['failed'] = $fail;
        $res['list'] = $comment;
        return $res;
    }

    function updChange($request)
    {
//        $time = time();
        $changeInfo = $this->DataControl->selectOne("select A.change_id from smc_student_change A,smc_student_changelog B 
            where A.change_pid=B.change_pid AND A.company_id=B.company_id AND B.changelog_id='{$request['changelog_id']}' order by a.change_pid DESC limit 0,1");
        if (!$changeInfo) {
            $this->error = true;
            $this->errortip = "未查询到流失信息，不可编辑!";
            return false;
        }
        $change_id = $changeInfo['change_id'];

        $data = array();
        $data['reason_code'] = $request['reason_code'];
        $data['change_reason'] = $request['reason'];
        $this->DataControl->updateData("smc_student_change", "change_id='{$change_id}'", $data);

        $log_data = array();
        $log_data['changelog_category'] = $request['category_note'];
        $log_data['changelog_note'] = $request['reason'];
        $bool = $this->DataControl->updateData("smc_student_changelog", "changelog_id='{$request['changelog_id']}'", $log_data);
        return $bool;
    }

    function lostChange($request)
    {
        //缺少上课时间
        $datawhere = " A.company_id='{$request['company_id']}' and A.stuchange_code IN ('C02','C04') AND B.student_isdel <> '1' ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' 
            or B.student_enname like '%{$request['keyword']}%' 
            or B.student_idcard like '%{$request['keyword']}%' 
            or B.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and A.changelog_day>='{$request['start_time']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and A.changelog_day<='{$request['end_time']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and A.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['category_note']) && $request['category_note'] !== '') {
            $datawhere .= " and A.changelog_category ='{$request['category_note']}'";
        }

        if (isset($request['is_other_school']) && $request['is_other_school'] !== '') {
            if ($request['is_other_school'] == '0') {
                $datawhere .= " and not exists(select 1 from smc_student_enrolled where student_id=a.student_id and school_id<>a.school_id and enrolled_status in(0,1))";
            } else {
                $datawhere .= " and exists(select 1 from smc_student_enrolled where student_id=a.student_id and school_id<>a.school_id and enrolled_status in(0,1))";
            }
        }

        $sql = "select A.changelog_id,A.student_id,A.coursetype_id,C.stuchange_code,
                    CONCAT(B.student_cnname,(CASE B.student_enname WHEN '' THEN '' ELSE CONCAT('/',B.student_enname) END)) AS student_name,
                    B.student_branch,
                    B.student_birthday,
                    C.stuchange_name,	
                    A.changelog_day,
                    ifnull(D.coursetype_cnname,'--') as coursetype_cnname,
                    A.changelog_category,
                    A.changelog_note,
                    e.reason_code,
                    ifnull((SELECT Y.trackresult_name FROM smc_student_track AS X, smc_code_trackresult AS Y WHERE Y.trackresult_id = X.result_id AND X.student_id = A.student_id 
                        AND X.school_id=A.school_id AND X.track_classname = '流失电访' AND track_day>=date_sub(A.changelog_day,interval 22 day) ORDER BY X.track_id DESC LIMIT 0, 1 ),'--') AS connect_info,
                    (select CONCAT(staffer_cnname,(CASE ifnull(staffer_enname,'') WHEN '' THEN '' ELSE CONCAT('/',staffer_enname) end)) from smc_staffer where staffer_id =A.staffer_id ) as staffer_name,
                    FROM_UNIXTIME(A.changelog_createtime) AS create_time,
                    F.enrolled_status,
                    (select sum(student_balance+student_withholdbalance) from smc_student_balance where school_id=a.school_id and student_id=a.student_id) as balance_left,
                    ifnull(outlog_id,0) as outlog_id
                from smc_student_changelog A
                left join smc_student B on B.student_id=A.student_id
                left join smc_code_stuchange C on C.stuchange_code=A.stuchange_code
                left join smc_code_coursetype D on D.coursetype_id=A.coursetype_id
                left join smc_student_change E on a.change_pid=e.change_pid and a.company_id=e.company_id
                left join smc_student_enrolled F on F.school_id=a.school_id and F.student_id=a.student_id
                left join smc_student_outlog G on G.STUDENT_ID = A.STUDENT_ID	
                WHERE {$datawhere} 
                and A.school_id='{$request['school_id']}'
                AND NOT(A.stuchange_code IN('C02','C04') AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and stuchange_code in('A01','A06','D02','F01')))
                and not(A.stuchange_code='C04' AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and coursetype_id=A.coursetype_id and stuchange_code ='D04'))
                order by A.changelog_createtime desc
                ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_name'] = $dateexcelvar['student_name'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['changelog_category'] = $dateexcelvar['changelog_category'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $datearray['connect_info'] = $dateexcelvar['connect_info'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_name'];
                    $datearray['create_time'] = $dateexcelvar['create_time'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员姓名", "学员编号", "异动类型", "流失日期", "流失班组", "流失类型", "流失原因", "流失电访结果", "操作人", "操作时间"));
            $excelfileds = array('student_name', 'student_branch', 'stuchange_name', 'changelog_day', 'coursetype_cnname', 'changelog_category', 'changelog_note', 'connect_info', 'staffer_name', 'create_time');
            $tem_name = $this->LgStringSwitch('学员流失异动管理表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $changeList = $this->DataControl->selectClear($sql);

            if (!$changeList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.changelog_id
                from smc_student_changelog A
                left join smc_student B on B.student_id=A.student_id
                left join smc_code_stuchange C on C.stuchange_code=A.stuchange_code
                left join smc_code_coursetype D on D.coursetype_id=A.coursetype_id
                left join smc_student_enrolled F on F.school_id=a.school_id and F.student_id=a.student_id
                WHERE {$datawhere} 
                and A.school_id='{$request['school_id']}'
                AND NOT(A.stuchange_code IN('C02','C04') AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and stuchange_code in('A01','A06','D02','F01')))
                and not(A.stuchange_code='C04' AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and coursetype_id=A.coursetype_id and stuchange_code ='D04'))
                order by A.changelog_createtime desc
                ";

                $db_nums = $this->DataControl->selectClear($count_sql);

                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $changeList;

            return $data;
        }
    }

    function graduation($request)
    {
        $sql = "select ss.student_id from smc_student_study as ss where ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}' and ss.study_isreading=1 group by ss.student_id";
        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "班级无学生";
            return false;
        }
        $TransactionModel = new \Model\Smc\TransactionModel($request);

        foreach ($studentList as $studentOne) {
            $TransactionModel->outClass($studentOne['student_id'], $request['class_id'], 2, strtotime($request['create_time']));
            $TransactionModel->graduation($studentOne['student_id'], $request['class_id'], $request['reason_code'], $request['reason'], strtotime($request['create_time']));
        }
        return true;
    }

    function getStuCrossSchool($request)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_ismanage", " staffer_id='{$request['staffer_id']}'");
        if ($stafferOne['staffer_ismanage'] !== '1') {
            $this->error = true;
            $this->errortip = "暂不支持跨校";
            return false;
        }

        $sql = "select se.school_id,s.school_branch,s.school_cnname,s.school_enname
			  from smc_student_enrolled as se
			  INNER join smc_school as s on s.school_id=se.school_id
			  where se.enrolled_status<>'-1' and se.enrolled_status<>'2' 
			  and se.student_id='{$request['student_id']}' 
			  and se.school_id<>'{$request['school_id']}' 
			  and s.company_id='{$request['company_id']}'
			  order by se.school_id asc
			  ";

        $schoolList = $this->DataControl->selectClear($sql);

        if (!$schoolList) {
            $this->error = true;
            $this->errortip = "无对应学校";
            return false;
        }

        return $schoolList;
    }

    function transferBalance($request)
    {
        $stublcOne = $this->getStuBalance($request['student_id'], $request['company_id'], $request['from_school_id'], $request['from_companies_id']);

        $pass = $this->DataControl->getFieldOne("gmc_code_companies", "companies_password", "companies_id = '{$request['to_companies_id']}'");

        if ((($stublcOne['student_balance'] < $request['balance']) || $request['balance'] < 0) || (($stublcOne['student_withholdbalance'] < $request['withholdbalance']) || $request['withholdbalance'] < 0)) {
            $this->error = true;
            $this->errortip = "请填写正确的账户余额";
            return false;
        }

        if ($request['balance'] == '0' && $request['withholdbalance'] == '0') {
            $this->error = true;
            $this->errortip = "请填写正确的账户余额";
            return false;
        }

        if ($request['from_school_id'] == $request['to_school_id'] && $request['from_companies_id'] == $request['to_companies_id']) {
            $this->error = true;
            $this->errortip = "请选择转入不同主体";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_ismanage", " staffer_id='{$request['staffer_id']}'");

        if ($request['companies_password'] != $pass['companies_password'] && $stafferOne['staffer_ismanage'] !== '1') {
            $this->error = true;
            $this->errortip = "交易密码不正确，请联系财务处理";
            return false;
        }

//        $b = $this->DataControl->selectClear("
//            SELECT
//                c.companies_issupervise
//            FROM
//                gmc_code_companies AS c
//                where c.companies_id = '{$request['from_companies_id']}'
//                and c.companies_issupervise = '1'");

//        if ($b && $stafferOne['staffer_ismanage'] != '1') {
//            $this->error = true;
//            $this->errortip = "来源主体已被监管，不可申请！";
//            return false;
//        }

        $TransactionModel = new \Model\Smc\TransactionModel($request);

        $BalanceModel = new \Model\Smc\BalanceModel($request);

        $oldTrading_pid = $TransactionModel->stuTrading($request['student_id'], $request['from_school_id'], 'TransferOut', $request['from_companies_id'], strtotime($request['create_time']));

        $trading_pid = $TransactionModel->schoolTrade($request['student_id'], $request['from_school_id'], $request['to_school_id'], $oldTrading_pid, '', 0, 1, $request['balance'], $request['withholdbalance'], $request['from_companies_id'], $request['to_companies_id'], $this->LgStringSwitch('创建订单'), $this->LgStringSwitch('订单提交成功，请耐心等待财务审核'), $request['note'], strtotime($request['create_time']));

        $request['note'] = $this->LgStringSwitch('余额转移待审核,备注:') . $request['note'];

        $BalanceModel->consumeStuBalance($request['student_id'], $request['from_school_id'], $oldTrading_pid, $request['balance'], $request['withholdbalance'], $request['from_companies_id'], $this->LgStringSwitch('资产转移'), $request['note'], strtotime($request['create_time']));

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isneedexaminetrade", "company_id='{$request['company_id']}'");

        if ($request['from_school_id'] == $request['to_school_id'] && $companyOne['company_isneedexaminetrade'] == 0) {

            $tradeOne = $this->DataControl->getOne("smc_school_trading", "trading_pid='{$trading_pid}' and company_id='{$request['company_id']}'");

            if ($tradeOne) {
                $request['note'] = $this->LgStringSwitch('自动审核');

                $Trading_pid = $TransactionModel->stuTrading($tradeOne['student_id'], $tradeOne['to_school_id'], 'TransferIn', $tradeOne['to_companies_id'], strtotime($request['create_time']));

                $BalanceModel->addStuAllBalance($tradeOne['student_id'], $tradeOne['to_school_id'], $Trading_pid, $tradeOne['trading_balance'], $tradeOne['trading_withholdbalance'], $tradeOne['to_companies_id'], $this->LgStringSwitch('资产转移'), $this->LgStringSwitch("余额转移成功,备注:") . $request['note'], strtotime($request['create_time']));

                $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname", "company_id='{$request['company_id']}' and account_class=1");

                $data = array();
                $data['trading_pid'] = $tradeOne['trading_pid'];
                $data['tracks_title'] = $this->LgStringSwitch('审核订单');
                $data['tracks_information'] = $this->LgStringSwitch('订单审核通过，订单完成');
                $data['tracks_note'] = $request['note'];
                $data['staffer_id'] = $stafferOne['staffer_id'];
                $data['tracks_playname'] = $stafferOne['staffer_cnname'];
                $data['tracks_time'] = $request['create_time'] ? strtotime($request['create_time']) : time();
                $this->DataControl->insertData("smc_school_trading_tracks", $data);

                $data = array();
                $data['trading_status'] = '2';
                $data['trading_note'] = $request['note'];
                $data['trading_topid'] = $Trading_pid;
                $data['trading_updatatime'] = $request['create_time'] ? strtotime($request['create_time']) : time();

                $this->DataControl->updateData("smc_school_trading", "trading_pid='{$tradeOne['trading_pid']}'", $data);
            }
        }
        return true;
    }

    function cancelChange($request)
    {
        if ($request['create_time']) {
            $nowDay = $request['create_time'];
            $nowTime = strtotime($request['create_time']);
        } else {
            $nowDay = date("Y-m-d", time());
            $nowTime = time();
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
        $changeOne = $this->DataControl->getOne("smc_student_change", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");

        if (date("Y-m", strtotime($changeOne['change_day'])) != date("Y-m", $nowTime)) {
            $this->error = true;
            $this->errortip = "只可删除当月异动";
            return false;
        }

        $logOne = $this->DataControl->selectOne("select sc.*,c.course_id
				  from smc_student_changelog as sc
				  left join smc_class as c on c.class_id=sc.class_id
				  where sc.change_pid='{$request['change_pid']}' and sc.company_id='{$request['company_id']}'");

        if ($logOne['school_id'] != $request['school_id']) {
            $this->error = true;
            $this->errortip = "不可删除非本校异动";
            return false;
        }

        if ($request['stuchange_code'] == 'A02' || $request['stuchange_code'] == 'A03' || $request['stuchange_code'] == 'A04' || $request['stuchange_code'] == 'A05') {
            $hourstudyOne = $this->DataControl->getFieldOne("smc_student_hourstudy", "hourstudy_id", "student_id='{$changeOne['student_id']}' and class_id='{$logOne['class_id']}'");
            $tem_logOne = $this->DataControl->getOne("smc_student_changelog", "change_pid<>'{$request['change_pid']}' and company_id='{$request['company_id']}' and class_id='{$logOne['class_id']}' and student_id='{$changeOne['student_id']}' and stuchange_code not in ('A02','A03','A04','A05')");

            if ($hourstudyOne || $tem_logOne) {
                $this->error = true;
                $this->errortip = "在班级存在记录,不可删除";
                return false;
            }

            $this->DataControl->delData("smc_student_changelog", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");
            $this->DataControl->delData("smc_student_change", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");
            $data = array();
            $data['study_isreading'] = '-1';
            $data['study_endday'] = $nowDay;
            $data['study_updatetime'] = time();
            $this->DataControl->updateData("smc_student_study", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and class_id='{$logOne['class_id']}'", $data);

            $data = array();
            $data['coursebalance_status'] = '0';
            $data['coursebalance_updatatime'] = time();
            $this->DataControl->updateData("smc_student_coursebalance", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and course_id='{$logOne['course_id']}'", $data);

        } elseif ($request['stuchange_code'] == 'D02') {
            $this->error = true;
            $this->errortip = "暂不支持删除";
            return false;
        } elseif ($request['stuchange_code'] == 'F01') {
            $tem_logOne = $this->DataControl->getOne("smc_student_changelog", "change_pid<>'{$request['change_pid']}' and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}'");
            $tradingOne = $this->DataControl->getOne("smc_student_trading", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}'");

            if ($tem_logOne || $tradingOne) {
                $this->error = true;
                $this->errortip = "在学校存在记录,不可删除";
                return false;
            }
            $data = array();
            $data['enrolled_status'] = '-1';
            $data['enrolled_leavetime'] = $nowTime;
            $this->DataControl->updateData("smc_student_enrolled", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}'", $data);

            $this->DataControl->delData("smc_student_changelog", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");
            $this->DataControl->delData("smc_student_change", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");

        } elseif ($request['stuchange_code'] == 'C02') {
            $this->error = true;
            $this->errortip = "暂不支持删除";
            return false;
        } elseif ($request['stuchange_code'] == 'A07') {

            $sql = "select sc.changelog_id from smc_student_changelog as sc
				  left join smc_class as c on c.class_id=sc.class_id
				  where sc.changelog_createtime>='{$logOne['changelog_createtime']}'
				  and sc.student_id='{$logOne['student_id']}'
				  and c.course_id='{$logOne['course_id']}'
				  and sc.change_pid<>'{$request['change_pid']}'
				  and sc.company_id='{$request['company_id']}'
				  ";
            $changeLog = $this->DataControl->selectOne($sql);
            if ($changeLog) {
                $this->error = true;
                $this->errortip = "课程存在异动,不可删除";
                return false;
            }

            $sql = "select ch.hour_id from smc_class_hour as ch where ch.class_id='{$logOne['class_id']}' and ch.hour_lessontimes>(select sh.hour_id from smc_student_hourstudy as sh where sh.class_id='{$logOne['class_id']}' and sh.student_id='{$logOne['student_id']}' order by sh.hourstudy_id desc limit 0,1)";
            $hourOne = $this->DataControl->selectOne($sql);

            if ($hourOne) {
                $this->error = true;
                $this->errortip = "原班级已点名,不可删除";
                return false;
            }
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_enddate", "class_id='{$logOne['class_id']}'");
            $this->DataControl->delData("smc_student_changelog", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");
            $this->DataControl->delData("smc_student_change", "change_pid='{$request['change_pid']}' and company_id='{$request['company_id']}'");


            $data = array();
            $data['study_isreading'] = 1;
            $data['study_endday'] = $classOne['class_enddate'];
            $data['study_updatetime'] = time();
            $this->DataControl->updateData("smc_student_study", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$request['student_id']}' and class_id='{$logOne['class_id']}'", $data);
        } else {
            $this->error = true;
            $this->errortip = "该异动不可删除";
            return false;
        }

        $log_data = array();
        $log_data['company_id'] = $request['company_id'];
        $log_data['student_id'] = $request['student_id'];
        $log_data['change_pid'] = $request['change_pid'];
        $log_data['from_stuchange_code'] = $changeOne['from_stuchange_code'];
        $log_data['from_school_id'] = $changeOne['from_school_id'];
        $log_data['from_class_id'] = $changeOne['from_class_id'];
        $log_data['to_stuchange_code'] = $changeOne['to_stuchange_code'];
        $log_data['to_school_id'] = $changeOne['to_school_id'];
        $log_data['to_class_id'] = $changeOne['to_class_id'];
        $log_data['change_status'] = $changeOne['change_status'];
        $log_data['change_accden'] = $changeOne['change_accden'];
        $log_data['change_day'] = $changeOne['change_day'];
        $log_data['reason_code'] = $changeOne['reason_code'];
        $log_data['change_reason'] = $changeOne['change_reason'];
        $log_data['change_workername'] = $changeOne['change_workername'];
        $log_data['change_createtime'] = $changeOne['change_createtime'];
        $log_data['del_workername'] = $stafferOne['staffer_cnname'];
        $log_data['del_createtime'] = $nowTime;
        $log_data['del_note'] = $request['del_note'];
        $this->DataControl->insertData("smc_student_change_dellog", $log_data);

        $stuEnrolled = $this->DataControl->getFieldOne("smc_student_enrolled", "enrolled_status", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}'");
        if ($stuEnrolled['enrolled_status'] >= 0) {
            $status = $this->DataControl->selectOne("select ss.study_id from smc_student_study as ss where ss.school_id='{$request['school_id']}' and ss.student_id='{$request['student_id']}' and ss.study_isreading=1");
            if ($status) {
                $data = array();
                $data['enrolled_status'] = '1';
                $data['enrolled_updatatime'] = $nowTime;
                $this->DataControl->updateData("smc_student_enrolled", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}'", $data);
            } else {
                $data = array();
                $data['enrolled_status'] = '0';
                $data['enrolled_updatatime'] = $nowTime;
                $this->DataControl->updateData("smc_student_enrolled", "school_id='{$request['school_id']}' and student_id='{$request['student_id']}'", $data);
            }
        }
        return true;

    }


    function stuClassLossList($request)
    {
        $sql = "select ss.class_id,c.class_cnname,c.class_branch,sc.course_cnname,sc.course_branch
			  from smc_student_study as ss
			  left join smc_class as c on c.class_id=ss.class_id
			  left join smc_student_coursebalance as scb on scb.course_id=c.course_id and scb.student_id=ss.student_id
			  left join smc_course as sc on sc.course_id=scb.course_id
			  where ss.student_id='{$request['student_id']}' and ss.school_id='{$request['school_id']}' and scb.coursebalance_figure=0 and scb.coursebalance_time=0 and ss.study_isreading=1
			  group by ss.class_id
			  ";
        $list = $this->DataControl->selectClear($sql);
        if ($list) {
            $this->error = true;
            $this->errortip = "无有效班级";
            return false;
        }
        return $list;
    }

    function upgradeOrderStuList($request)
    {
//
//		$sql="select sc.course_nextid
//			  from smc_class as c
//			  left join smc_course as sc on sc.course_id=c.course_id and sc.company_id='{$request['company_id']}'
//			  where c.class_id='{$request['class_id']}'
//			  ";
//		$nextOne=$this->DataControl->selectOne($sql);
//		if(!$nextOne){
//			$this->error = true;
//			$this->errortip = "该课程无下一级别课程";
//			return false;
//		}
//		$pricingOne = $this->getCoursePricing($nextOne['course_nextid'], $request['company_id'], $request['school_id']);
        $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);

        if (!$pricingOne) {
            $this->error = true;
            $this->errortip = "课程不在协议内";
            return false;
        }
        $sql = "select hour_id from smc_class_hour where class_id='{$request['class_id']}' and hour_iswarming=0 and hour_ischecking='1' order by hour_day desc,hour_starttime desc";
        $classOne = $this->DataControl->selectOne($sql);

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "无有效课程";
            return false;
        }

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.pricing_id
				,(select scb.coursebalance_time from smc_student_coursebalance as scb where scb.student_id=ss.student_id and scb.course_id='{$request['course_id']}' and scb.school_id='{$request['school_id']}') as course_time
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_student_coursebalance as ssc on ssc.course_id=sc.course_id and ssc.student_id=s.student_id
              left join smc_student_enrolled as se on se.student_id=ss.student_id and se.school_id=ss.school_id
              where ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and se.enrolled_status in (0,1)
              and exists(select sh.hourstudy_id from smc_student_hourstudy as sh where sh.class_id=ss.class_id and sh.student_id=ss.student_id and sh.hour_id='{$classOne['hour_id']}')
              group by ss.student_id
              order by ss.study_beginday DESC
              ";
        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "班内无在读学生";
            return false;
        }
        foreach ($studentList as &$studentOne) {
            if ($studentOne['course_time']) {
                $studentOne['is_have'] = 1;
            } else {
                $studentOne['is_have'] = 0;
            }

        }

        return $studentList;
    }

    function upgradeOrderNewClassList($request)
    {

        $field = array();
        $field[0]["fieldstring"] = "pricing_id";
        $field[0]["fieldname"] = $this->LgStringSwitch("定价ID");
        $field[0]["show"] = 0;
        $field[0]["custom"] = 0;

        $field[1]["fieldstring"] = "course_id";
        $field[1]["fieldname"] = $this->LgStringSwitch("课程ID");
        $field[1]["show"] = 0;
        $field[1]["custom"] = 0;

        $field[4]["fieldstring"] = "course_cnname";
        $field[4]["fieldname"] = $this->LgStringSwitch("课程别");
        $field[4]["show"] = 1;
        $field[4]["custom"] = 0;

        $field[5]["fieldstring"] = "tuition_originalprice";
        $field[5]["fieldname"] = $this->LgStringSwitch("原价");
        $field[5]["show"] = 1;
        $field[5]["custom"] = 0;

        $field[6]["fieldstring"] = "tuition_sellingprice";
        $field[6]["fieldname"] = $this->LgStringSwitch("销售价");
        $field[6]["show"] = 1;
        $field[6]["custom"] = 0;

        $field[7]["fieldstring"] = "unitsellprice";
        $field[7]["fieldname"] = $this->LgStringSwitch("销售单价");
        $field[7]["show"] = 1;
        $field[7]["custom"] = 0;

        $field[8]["fieldstring"] = "coupon";
        $field[8]["fieldname"] = $this->LgStringSwitch("优惠券");
        $field[8]["show"] = 1;
        $field[8]["custom"] = 0;
        $field[8]["isSelectJuan"] = 1;


        $tem_array = array();
        $studentList = json_decode(stripslashes($request['list']), true);
        foreach ($studentList as $student) {
            $studentOne = $this->DataControl->selectOne("select s.student_id,s.student_cnname
            ,s.student_forwardprice
            ,ifnull((select sum(student_balance) from smc_student_balance b where student_id=s.student_id and school_id='{$request['school_id']}'),0) as student_balance
            ,ifnull((select sum(student_withholdbalance) from smc_student_balance b where student_id=s.student_id and school_id='{$request['school_id']}'),0) as student_withholdbalance
            from smc_student as s
            WHERE 1
            and s.student_id='{$student['student_id']}' 
            and s.company_id='{$request['company_id']}'");

            $all_price = 0;
            $pricingList = array();
            $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);

            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_branch,course_cnname,course_nextid,course_inclasstype,course_classnum", "course_id='{$request['course_id']}' and company_id='{$request['company_id']}'");
            if ($pricingOne) {

                $tem_data = array();
                $tem_data['pricing_id'] = $pricingOne['pricing_id'];
                $tem_data['course_id'] = $courseOne['course_id'];
                $tem_data['course_cnname'] = $courseOne['course_cnname'];
                $tem_data['course_classnum'] = $courseOne['course_classnum'];
                $tem_data['course_inclasstype'] = $courseOne['course_inclasstype'];
                $tem_data['classInfo'] = '0/' . $pricingOne['tuition_buypiece'];
                $tem_data['tuition_originalprice'] = $pricingOne['tuition_originalprice'];
                $tem_data['tuition_sellingprice'] = $pricingOne['tuition_sellingprice'];
                $tem_data['unitsellprice'] = ceil($pricingOne['tuition_sellingprice'] / $pricingOne['tuition_buypiece']);
                $tem_data['coupon'] = '';
                $tem_data['is_having'] = 0;
                $tem_data['is_discount'] = 0;

                $policyOne = $this->getStuDiscountPrice($request, $student['student_id'], $request['course_id']);
                if ($policyOne) {
                    $pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                    $tem_data['is_discount'] = 1;
                }

                $all_price += $pricingOne['tuition_sellingprice'];

                $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
		  from smc_fee_pricing_products as fpp
		  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
		  left join erp_goods as g on g.goods_id=fpp.goods_id
		  where fpp.pricing_id='{$pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
		  ";

                $goodsList = $this->DataControl->selectClear($goods_sql);
                if ($goodsList) {
                    foreach ($goodsList as $val) {
                        if ($val['isfree'] != 1) {
                            $all_price += $val['sellingprice'];
                        }
                    }
                }

                $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
		  from smc_fee_pricing_items as fpi
		  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
		  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
		  where fpi.pricing_id='{$pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
		  group by fpi.feeitem_branch
		  ";
                $to_itemsList = $this->DataControl->selectClear($item_sql);

                if ($to_itemsList) {
                    foreach ($to_itemsList as $itemsOne) {
                        if ($itemsOne['isfree'] != 1) {
                            $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                        }
                    }
                }

                $pricingList[] = $tem_data;

                if (isset($request['is_buy_next']) && $request['is_buy_next'] == '1') {
                    $next_pricingOne = $this->getCoursePricing($request['course_nextid'], $request['company_id'], $request['school_id']);
                    if ($next_pricingOne) {
                        $nextCourseOne = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_inclasstype,course_classnum", "course_id='{$request['course_nextid']}' and company_id='{$request['company_id']}'");
                        $tem_data = array();
                        $tem_data['pricing_id'] = $next_pricingOne['pricing_id'];
                        $tem_data['course_id'] = $next_pricingOne['course_id'];
                        $tem_data['course_cnname'] = $nextCourseOne['course_cnname'];
                        $tem_data['course_classnum'] = $nextCourseOne['course_classnum'];
                        $tem_data['course_inclasstype'] = $nextCourseOne['course_inclasstype'];
                        $tem_data['classInfo'] = '0/' . $next_pricingOne['tuition_buypiece'];
                        $tem_data['tuition_originalprice'] = $next_pricingOne['tuition_originalprice'];
                        $tem_data['tuition_sellingprice'] = $next_pricingOne['tuition_sellingprice'];
                        $tem_data['unitsellprice'] = ceil($next_pricingOne['tuition_sellingprice'] / $next_pricingOne['tuition_buypiece']);
                        $tem_data['coupon'] = '';
                        $tem_data['is_having'] = 0;
                        $tem_data['is_discount'] = 0;

                        $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$student['student_id']}' and course_id='{$request['course_nextid']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                        if (!$courseBalanceOne) {

                            $policyOne = $this->getStuDiscountPrice($request, $student['student_id'], $request['course_nextid']);
                            if ($policyOne) {
                                $next_pricingOne['tuition_sellingprice'] = $policyOne['full_price'];
                                $tem_data['is_discount'] = 1;
                            }

                            $all_price += $next_pricingOne['tuition_sellingprice'];
                            $goods_sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
	  from smc_fee_pricing_products as fpp
	  left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
	  left join erp_goods as g on g.goods_id=fpp.goods_id
	  where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
	  ";

                            $goodsList = $this->DataControl->selectClear($goods_sql);
                            if ($goodsList) {
                                foreach ($goodsList as $val) {
                                    if ($val['isfree'] != 1) {
                                        $all_price += $val['sellingprice'];
                                    }
                                }
                            }

                            $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
	  from smc_fee_pricing_items as fpi
	  left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
	  left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
	  where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
	  group by fpi.feeitem_branch
	  ";
                            $to_itemsList = $this->DataControl->selectClear($item_sql);

                            if ($to_itemsList) {
                                foreach ($to_itemsList as $itemsOne) {
                                    if ($itemsOne['isfree'] != 1) {
                                        $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                    }
                                }
                            }
                        } else {
                            $tem_data['is_having'] = 1;
                        }
                        $pricingList[] = $tem_data;
                    }
                }


                $data = array();
                $data['list'] = $pricingList;
                $data['field'] = $field;
                $userArray = array();
                $userArray['student_id'] = $studentOne['student_id'];
                $userArray['student_cnname'] = $studentOne['student_cnname'];
                $userArray['all_price'] = $all_price;
                $userArray['student_balance'] = $studentOne['student_balance'] + $studentOne['student_withholdbalance'];
                $userArray['student_forwardprice'] = $studentOne['student_forwardprice'];
                $data['userInfo'] = $userArray;

                $tem_array[] = $data;
            }

        }

        return $tem_array;

    }

    function upgradeStuList($request)
    {

        $contractOne = $this->getContract($this->company_id);
        $can_upgrade = 1;

        if ($contractOne && $contractOne['edition_id'] == '2') {
            $sql = "select s.student_id,s.student_cnname,s.student_branch
                  from smc_student_study as ss 
                  left join smc_student as s on ss.student_id=s.student_id
                  where ss.school_id='{$this->school_id}' and ss.class_id='{$request['class_id']}' 
                  and ss.study_isreading=1
                  ";
            $studentList = $this->DataControl->selectClear($sql);

            if (!$studentList) {
                $this->error = true;
                $this->errortip = "班内无在读学生";
                return false;
            }
        } else {
            $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);

            if (!$pricingOne) {
                $this->error = true;
                $this->errortip = "课程不在协议内";
                return false;
            }

            $sql = "select hour_id from smc_class_hour where class_id='{$request['class_id']}' and hour_iswarming=0 and hour_ischecking<>'-1' order by hour_day desc,hour_starttime desc";
            $classOne = $this->DataControl->selectOne($sql);

            $sql = "select s.student_id,s.student_cnname,s.student_branch,ssc.coursebalance_time,ssc.coursebalance_figure,ssc.pricing_id
				,(select scb.coursebalance_time from smc_student_coursebalance as scb where scb.student_id=ss.student_id and scb.course_id='{$request['course_id']}' and scb.school_id='{$request['school_id']}') as course_time
              from smc_student_study as ss
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_student_coursebalance as ssc on ssc.course_id=sc.course_id and ssc.student_id=s.student_id
              left join smc_student_enrolled as se on se.student_id=ss.student_id and se.school_id=ss.school_id
              where ss.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}' and se.enrolled_status in (0,1)
              and exists(select sh.hourstudy_id from smc_student_hourstudy as sh where sh.class_id=ss.class_id and sh.student_id=ss.student_id and sh.hour_id='{$classOne['hour_id']}')
              group by ss.student_id
              order by ss.study_beginday DESC
              ";
            $studentList = $this->DataControl->selectClear($sql);

            if (!$studentList) {
                $this->error = true;
                $this->errortip = "班内无在读学生";
                return false;
            }

            foreach ($studentList as &$studentOne) {
                if ($studentOne['course_time']) {
                    $studentOne['is_have'] = 1;
                    $studentOne['is_have_name'] = $this->LgStringSwitch('已拥有');
                } else {
                    $studentOne['is_have'] = 0;
                    $studentOne['is_have_name'] = $this->LgStringSwitch('未拥有');
                    $can_upgrade = 0;
                }

                $sql = "select ss.study_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where c.course_id='{$request['course_id']}' and ss.study_isreading=1 and ss.student_id>0 and ss.student_id='{$studentOne['student_id']}'";
                if ($this->DataControl->selectOne($sql)) {
                    $studentOne['is_have'] = 0;
                    $studentOne['is_have_name'] = $this->LgStringSwitch('已入班');
                    $can_upgrade = 0;
                }
            }
        }


        $data = array();
        $data['stu_list'] = $studentList;
        $data['can_upgrade'] = $can_upgrade;

        return $data;
    }

    function upgrade($request)
    {
        $ClassModel = new \Model\Smc\ClassModel($request);
        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $BalanceModel = new \Model\Smc\BalanceModel($request);
        $classOne = $ClassModel->classAdd($request);
        if (!$classOne) {
            $this->error = true;
            $this->errortip = $ClassModel->errortip;
            return false;
        }

        $studentList = json_decode(stripslashes($request['list']), 1);
        $contractOne = $this->getContract($request['company_id']);

        $class = $this->DataControl->getFieldOne("smc_class", "course_id", "class_id='{$request['class_id']}'");
        if ($studentList) {
            foreach ($studentList as $val) {
                if ($contractOne && $contractOne['edition_id'] == '2') {
                    $TransactionModel->outClass($val['student_id'], $request['class_id'], 2, strtotime($request['create_time']));
                    $trading_pid = '';
                } else {
                    if ($this->DataControl->getFieldOne("smc_student_study", "class_id", "student_id='{$val['student_id']}' and class_id='{$request['class_id']}' and study_isreading='1'")) {
                        $trading_pid = $BalanceModel->carryOver($val['student_id'], $class['course_id'], $request['class_id'], 2, strtotime($request['create_time']));
                    } else {
                        $trading_pid = '';
                    }
                }

                $TransactionModel->promotionClass($val['student_id'], $request['class_id'], $classOne['class_id'], $request['reason_code'], '升班', $trading_pid, '', strtotime($request['create_time']));
            }
        }

        $TransactionModel->upgradeInfo($request['class_id'], $classOne['class_id'], $studentList, strtotime($request['create_time']));

        return true;
    }

    function upgradeOrder($request)
    {

        $RegistrationModel = new \Model\Smc\RegistrationModel($this->publicarray);

        $studentList = json_decode(stripslashes($request['list']), 1);

        $success = 0;
        $failed = 0;
        $errortip = '';
        foreach ($studentList as $stuOne) {

            $courseArray = array();

            $data = array();
            $data['from'] = 'registrationPay';
            if (isset($stuOne['is_forward']) && $stuOne['is_forward'] == 1) {
                $data['is_forward'] = 1;
            } else {
                $data['is_forward'] = 0;
            }

            foreach ($stuOne['list'] as $courseOne) {
                $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$courseOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";
                $goodsList = $this->DataControl->selectClear($sql);

                $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$courseOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                $itemsList = $this->DataControl->selectClear($item_sql);


                $pricingOne = $this->getCoursePricing($courseOne['course_id'], $request['company_id'], $request['school_id']);

                $courseData = array();

                $policyOne = $this->getStuDiscountPrice($request, $stuOne['student_id'], $courseOne['course_id']);

                if ($policyOne) {
                    $courseData['is_discount'] = 1;
                    $courseData['policy_id'] = $policyOne['policy_id'];
                } else {
                    $courseData['is_discount'] = 0;
                }

                $courseData['agreement_id'] = $pricingOne['agreement_id'];
                $courseData['pricing_id'] = $pricingOne['pricing_id'];
                $courseData['course_id'] = $pricingOne['course_id'];
                $courseData['sellingprice'] = $courseOne['sellingprice'];
                $courseData['num'] = $pricingOne['tuition_buypiece'];
                $courseData['starttime'] = "";
                $courseData['discount_id'] = "";
                $courseData['market_price'] = "";
                $courseData['deductionmethod'] = 2;
                $courseData['from'] = 1;
                $courseData['goodsList'] = $goodsList ? $goodsList : array();
                $courseData['itemsList'] = $itemsList ? $itemsList : array();
                $courseData['couponList'] = $courseOne['couponList'];

                $courseArray[] = $courseData;
            }

            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];
            $data['staffer_id'] = $request['staffer_id'];
            $data['student_id'] = $stuOne['student_id'];

            $data['coupon_price'] = 0;
            $data['market_price'] = 0;
            $data['giveforwardprice'] = 0;
            $data['all_market_price'] = 0;
            $data['create_time'] = $request['create_time'];
            $data['all_coupon_price'] = $stuOne['all_coupon_price'];
            $data['all_coupon_price_list'] = json_encode($stuOne['all_coupon_price_list'], JSON_UNESCAPED_UNICODE);;
            $data['order_coupon_price'] = $stuOne['order_coupon_price'];
            $data['price'] = $stuOne['price'];
            $data['list'] = json_encode($courseArray, JSON_UNESCAPED_UNICODE);

            $buildorder = $RegistrationModel->createOrder($data);
            if ($buildorder && $buildorder != '') {
                $success++;
            } else {
                $failed++;
                $errortip .= $RegistrationModel->errortip;
            }

        }


        if ($success > 0) {
            if ($failed > 0) {
                $this->errortip = "预制升班订单部分成功:" . $errortip;
            } else {
                $this->errortip = "预制升班订单成功";
            }
            $this->error = true;
            return true;
        } else {
            $this->error = true;
            $this->errortip = "预制升班订单失败:" . $errortip;
            return false;
        }
    }


    function upgradeOrder_bak($request)
    {
        $RegistrationModel = new \Model\Smc\RegistrationModel($request);

        $pricingOne = $this->getCoursePricing($request['course_id'], $request['company_id'], $request['school_id']);

        if (!$pricingOne) {
            $this->error = true;
            $this->errortip = "选择课程不在协议内";
            return false;
        }

        $goods_all_price = 0;
        $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";
        $goodsList = $this->DataControl->selectClear($sql);
        if (!$goodsList) {
            $goodsList = array();
        } else {
            foreach ($goodsList as $goodsOne) {
                if ($goodsOne['isfree'] != 1) {
                    $goods_all_price += $goodsOne['sellingprice'];
                }
            }
        }

        $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
        $itemsList = $this->DataControl->selectClear($item_sql);
        if (!$itemsList) {
            $itemsList = array();
        } else {
            foreach ($itemsList as $itemsOne) {
                if ($itemsOne['isfree'] != 1) {
                    $goods_all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                }
            }
        }
        $studentList = json_decode(stripslashes($request['list']), 1);

        $success = 0;
        $failed = 0;
        $errortip = '';
        foreach ($studentList as $val) {
            $data = array();
            if (isset($val['is_forward']) && $val['is_forward'] == 1) {
                $data['is_forward'] = 1;
            } else {
                $data['is_forward'] = 0;
            }

            $all_price = 0;
            $courseArray = array();
            $courseData = array();
            $policyOne = $this->getStuDiscountPrice($request, $val['student_id'], $request['course_id']);
            $sellingprice = $pricingOne['tuition_sellingprice'];

            if ($policyOne) {
                $sellingprice = $policyOne['full_price'];
                $courseData['is_discount'] = 1;
                $courseData['policy_id'] = $policyOne['policy_id'];
            } else {
                $courseData['is_discount'] = 0;
            }

            $all_price += $goods_all_price;

            $courseData['agreement_id'] = $pricingOne['agreement_id'];
            $courseData['pricing_id'] = $pricingOne['pricing_id'];
            $courseData['course_id'] = $pricingOne['course_id'];
            $courseData['sellingprice'] = $sellingprice;
            $courseData['num'] = $pricingOne['tuition_buypiece'];
            $courseData['starttime'] = "";
            $courseData['discount_id'] = "";
            $courseData['market_price'] = "";
            $courseData['deductionmethod'] = 2;
            $courseData['from'] = 1;
            $courseData['goodsList'] = $goodsList;
            $courseData['itemsList'] = $itemsList;
            $courseData['couponList'] = $val['to_couponList'];
            if (isset($val['to_couponList']) && $val['to_couponList'] != '') {
                foreach ($val['to_couponList'] as $toOne) {
                    if ($toOne['coupon_price'] > 0) {
                        $data['is_forward'] = 0;
                        $all_price -= $toOne['coupon_price'];
                        $courseData['sellingprice'] -= $toOne['coupon_price'];
                    }
                }
            }

            $courseArray[] = $courseData;

            $all_price += $sellingprice;


            if (isset($request['is_buy_next']) && $request['is_buy_next'] == 1) {
                $next_pricingOne = $this->getCoursePricing($request['course_nextid'], $request['company_id'], $request['school_id']);
                if ($next_pricingOne) {

                    $courseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and student_id='{$val['student_id']}' and course_id='{$request['course_nextid']}' and (coursebalance_figure>0 or coursebalance_time>0)");

                    if (!$courseBalanceOne) {
                        $next_courseData = array();
                        $policyOne = $this->getStuDiscountPrice($request, $val['student_id'], $request['course_nextid']);
                        $next_sellingprice = $next_pricingOne['tuition_sellingprice'];
                        if ($policyOne) {
                            $next_sellingprice = $policyOne['full_price'];
                            $next_courseData['is_discount'] = 1;
                            $next_courseData['policy_id'] = $policyOne['policy_id'];
                        } else {
                            $next_courseData['is_discount'] = 0;
                        }


                        $next_courseData['agreement_id'] = $next_pricingOne['agreement_id'];
                        $next_courseData['pricing_id'] = $next_pricingOne['pricing_id'];
                        $next_courseData['course_id'] = $next_pricingOne['course_id'];
                        $next_courseData['sellingprice'] = $next_sellingprice;
                        $next_courseData['num'] = $next_pricingOne['tuition_buypiece'];
                        $next_courseData['class_id'] = "";
                        $next_courseData['starttime'] = "";
                        $next_courseData['discount_id'] = "";
                        $next_courseData['market_price'] = "";
                        $next_courseData['deductionmethod'] = 2;
                        $all_price += $next_sellingprice;

                        $sql = "select fpp.goods_id,g.goods_cnname,fpp.products_originalprice,fpp.products_sellingprice as sellingprice,fpp.products_buypiece as num,fpp.products_unitprice,fpp.products_ismustbuy,fpp.products_isfree as isfree,fp.agreement_id,fp.pricing_id
              from smc_fee_pricing_products as fpp
              left join smc_fee_pricing as fp on fp.pricing_id=fpp.pricing_id
              left join erp_goods as g on g.goods_id=fpp.goods_id
              where fpp.pricing_id='{$next_pricingOne['pricing_id']}' and g.company_id='{$request['company_id']}' and fpp.products_ismustbuy='1'
              ";
                        $next_goodsList = $this->DataControl->selectClear($sql);
                        if (!$next_goodsList) {
                            $next_goodsList = array();
                        } else {
                            foreach ($next_goodsList as $goodsOne) {
                                if ($goodsOne['isfree'] != 1) {
                                    $all_price += $goodsOne['sellingprice'];
                                }
                            }
                        }
                        $item_sql = "select fpi.items_id,fpi.feeitem_branch,f.feeitem_cnname,fpi.items_sellingprice,fpi.items_unitprice as unitprice,fpi.items_buypiece as buypiece,fpi.items_donatepiece,fpi.items_isfree as isfree,fpi.items_ismustbuy,fp.pricing_id,fp.agreement_id
              from smc_fee_pricing_items as fpi
              left join smc_fee_pricing as fp on fp.pricing_id=fpi.pricing_id
              left join smc_code_feeitem as f on f.feeitem_branch=fpi.feeitem_branch
              where fpi.pricing_id='{$next_pricingOne['pricing_id']}' and f.company_id='{$request['company_id']}' and fpi.items_ismustbuy='1'
              group by fpi.feeitem_branch
              ";
                        $next_itemsList = $this->DataControl->selectClear($item_sql);

                        if (!$next_itemsList) {
                            $next_itemsList = array();
                        } else {
                            foreach ($next_itemsList as $itemsOne) {
                                if ($itemsOne['isfree'] != 1) {
                                    $all_price += $itemsOne['buypiece'] * $itemsOne['unitprice'];
                                }
                            }
                        }

                        $next_courseData['goodsList'] = $next_goodsList;
                        $next_courseData['itemsList'] = $next_itemsList;
                        $next_courseData['couponList'] = $val['next_couponList'];

                        if (isset($val['next_couponList']) && $val['next_couponList'] != '') {
                            foreach ($val['next_couponList'] as $nextOne) {
                                if ($nextOne['coupon_price'] > 0) {
                                    $data['is_forward'] = 0;
                                    $all_price -= $nextOne['coupon_price'];
                                    $next_courseData['sellingprice'] -= $nextOne['coupon_price'];
                                }
                            }
                        }

                        $courseArray[] = $next_courseData;
                    }
                }
            }


            $data['company_id'] = $request['company_id'];
            $data['school_id'] = $request['school_id'];
            $data['staffer_id'] = $request['staffer_id'];
            $data['student_id'] = $val['student_id'];

            $data['coupon_price'] = 0;
            $data['market_price'] = 0;
            $data['giveforwardprice'] = 0;
            $data['all_market_price'] = 0;
            $data['create_time'] = $request['create_time'];

//  订单优惠开始
            $price_list = json_decode(stripslashes($request['all_coupon_price_list']), 1);

            $all_coupon_price = 0;
            if (isset($price_list) && $price_list) {
                $priceArray = array();
                $rateArray = array();
                foreach ($price_list as $value) {
                    if ($value['ticket_way'] == '0') {
                        $priceArray[] = $value;
                    } else {
                        $rateArray[] = $value;
                    }
                }
                if ($priceArray) {
                    foreach ($priceArray as $valOne) {
                        if ($valOne['derateprice'] > $all_price) {
                            $valOne['derateprice'] = $all_price;
                        }

                        $all_coupon_price += $valOne['derateprice'];
                        $all_price -= $valOne['derateprice'];
                    }
                }

                if ($rateArray) {
                    foreach ($rateArray as $valOne) {
                        $all_coupon_price += floor(round($all_price * (1 - $valOne['deraterate'] / 10), 1));
                        $all_price -= floor(round($all_price * (1 - $valOne['deraterate'] / 10), 1));
                    }
                }
            }

            $data['all_coupon_price_list'] = $request['all_coupon_price_list'];

            if ($all_coupon_price > 0) {
                $data['all_coupon_price'] = $all_coupon_price;
                $data['is_forward'] = 0;
//                $totalPrice-=$all_coupon_price;
            }

            //订单优惠结束

//            if (isset($val['all_coupon_price']) && $val['all_coupon_price'] > 0) {
//                $data['all_coupon_price'] = $val['all_coupon_price'];
//                $data['coupon_id'] = $val['coupon_id'];
//                $data['is_forward'] = 0;
//                $all_price -= $val['all_coupon_price'];
//            }

            //单个订单优惠结束
            $data['price'] = $all_price;

            $data['list'] = json_encode($courseArray, JSON_UNESCAPED_UNICODE);
            $buildorder = $RegistrationModel->createOrder($data);
            if ($buildorder && $buildorder != '') {
                $success++;
            } else {
                $failed++;
                $errortip .= $RegistrationModel->errortip;
            }
        }
        if ($success > 0) {
            if ($failed > 0) {
                $this->errortip = "预制升班订单部分成功:" . $errortip;
            } else {
                $this->errortip = "预制升班订单成功";
            }
            $this->error = true;
            return true;
        } else {
            $this->error = true;
            $this->errortip = "预制升班订单失败:" . $errortip;
            return false;
        }
    }


    function getGoodsList($request)
    {

        $data = array();
        $GoodsFeeModel = new \Model\Smc\GoodsFeeModel($this->publicarray);
        if ($request['payment_type'] == '0') {
            //课程杂费
            $data = $GoodsFeeModel->getCourseItemList($request);
        } elseif ($request['payment_type'] == '1') {
            //普通商品
            $data = $GoodsFeeModel->GoodsList($request);
        } else {
            $this->error = true;
            $this->errortip = "无对应类型";
            return false;
        }

        if (!$data) {
            $this->error = true;
            $this->errortip = "无可用商品";
            return false;
        }

        return $data;
    }


    function getClassStudent($request)
    {

        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }
        $sql = "select ss.student_id,s.student_cnname,s.student_enname,s.student_branch,sb.student_balance,sb.student_withholdbalance,s.student_forwardprice,sf.family_mobile
			  from smc_student_study as ss
			  left join smc_class as c on ss.class_id=c.class_id
			  left join smc_student as s on s.student_id=ss.student_id
			  left join smc_student_balance as sb on sb.student_id=s.student_id and sb.school_id='{$request['school_id']}'
			  left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault='1'
			  where {$datawhere} and ss.study_isreading='1' and ss.company_id='{$request['company_id']}' and ss.school_id='{$request['school_id']}' and ss.class_id='{$request['class_id']}'
			  ";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "班内无在班学生";
            return false;
        }

        $list = json_decode(stripslashes($request['list']), true);
        if (!$list) {
            $this->error = true;
            $this->errortip = "未选择任何商品";
            return false;
        }
        foreach ($studentList as &$studentOne) {
            $status = 1;
            $array = array();
            foreach ($list as $val) {
                if ($request['payment_type'] == '0') {
                    $sql = "select sit.itemtimes_id,cf.feeitem_cnname as cnname
						  from smc_student_itemtimes as sit
						  left join smc_code_feeitem as cf on cf.feeitem_id=sit.feeitem_id
						  where sit.student_id='{$studentOne['student_id']}' and cf.company_id='{$request['company_id']}' and cf.feeitem_branch='{$val['feeitem_branch']}'
						  and sit.course_id='{$val['course_id']}' and sit.itemtimes_number>0
						  group by sit.itemtimes_id";
                } elseif ($request['payment_type'] == '1') {
                    $sql = "select seg.erpgoods_id,eg.goods_cnname as cnname
						  from smc_student_erpgoods as seg
						  left join erp_goods as eg on eg.goods_id=seg.goods_id
						  where seg.student_id='{$studentOne['student_id']}' and seg.goods_id='{$val['goods_id']}' and eg.company_id='{$request['company_id']}'
						  group by seg.erpgoods_id";
                }

                $one = $this->DataControl->selectClear($sql);
                if ($one) {
                    $status = 2;
                    foreach ($one as $value) {
                        $array[] = $value['cnname'];
                    }
                }
            }

            $studentOne['balance'] = $studentOne['student_balance'] + $studentOne['student_withholdbalance'];
            if ($request['payment_type'] == '2') {
                $studentOne['status'] = '0';
                $studentOne['info'] = '--';
                $studentOne['goodStr'] = '';
            } else {
                if ($status == '2') {
                    $studentOne['status'] = '1';
                    $studentOne['info'] = $this->LgStringSwitch('已拥有');
                    $studentOne['goodStr'] = implode(",", $array);
                } else {
                    $studentOne['status'] = '0';
                    $studentOne['info'] = '未拥有';
                    $studentOne['goodStr'] = '';
                }
            }
        }

        return $studentList;

    }

    function applyFreeClass($request)
    {

        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_branch", "student_id='{$request['student_id']}'");
        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "无该申请学生";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "class_branch", "class_id='{$request['class_id']}'");

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "无该申请班级";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$request['staffer_id']}'");

        if (!$stafferOne) {
            $this->error = true;
            $this->errortip = "无该申请人";
            return false;
        }

        if (!$this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id='{$request['student_id']}' and class_id='{$request['class_id']}' and study_isreading='-1'")) {
            $this->error = true;
            $this->errortip = "该学生不符合申请条件";
            return false;
        }

        do {
            $clockorder_pid = $this->createRandom('MK');
        } while ($this->DataControl->selectOne("select clockorder_id from smc_student_clockorder where clockorder_pid='{$clockorder_pid}' limit 0,1"));

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['clockorder_pid'] = $clockorder_pid;
        $data['staffer_id'] = $request['staffer_id'];
        $data['clockorder_playname'] = $stafferOne['staffer_cnname'];
        $data['school_id'] = $request['school_id'];
        $data['class_branch'] = $request['class_branch'];
        $data['student_branch'] = $studentOne['student_branch'];
        $data['clockorder_class'] = 0;
        $data['clockorder_status'] = 0;
        $data['clockorder_reson'] = $request['reason'];
        $data['clockorder_createtime'] = time();

        if ($this->DataControl->insertData("smc_student_clockorder", $data)) {
            return true;

        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function getStuClassTypeList($request)
    {

        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $this->error = true;
            $this->errortip = "学生ID必须传";
            return false;
        }

        $sql = "select cs.stustatus_isenschool,scl.changelog_day,scl.changelog_id
              from smc_student_changelog as scl,smc_code_stuchange as cs 
              where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=1 and scl.student_id='{$request['student_id']}' and scl.school_id='{$this->school_id}' 
              order by scl.changelog_day desc,scl.changelog_id desc";

        $logOne = $this->DataControl->selectOne($sql);

        //有些学生没有入校记录所以加了$logOne['stustatus_isenschool']
        if ($logOne['stustatus_isenschool'] && $logOne['stustatus_isenschool'] != 1) {
            $this->error = true;
            $this->errortip = "学生不在校";
            return false;
        }

        $starttime = strtotime($logOne['changelog_day']);


        $sql = "select cc.coursetype_id,cc.coursetype_cnname,cc.coursetype_branch
              ,ifnull((select scl.stuchange_code from smc_student_changelog as scl,smc_code_stuchange as cs 
              where scl.stuchange_code=cs.stuchange_code and cs.stuchange_code in ('C04','D04') and scl.student_id='{$request['student_id']}' and scl.school_id='{$this->school_id}' and scl.coursetype_id=sc.coursetype_id and scl.changelog_day>='{$logOne['changelog_day']}' and scl.changelog_id>'{$logOne['changelog_id']}' order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'') as stuchange_code
              from smc_student_coursebalance as scb,smc_course as sc,smc_code_coursetype as cc 
              where scb.course_id=sc.course_id and sc.coursetype_id=cc.coursetype_id and scb.school_id='{$this->school_id}' and scb.student_id='{$request['student_id']}' and (scb.coursebalance_updatatime=0 or scb.coursebalance_updatatime>='{$starttime}') and scb.coursebalance_figure=0 and scb.coursebalance_time=0 and cc.coursetype_isregistercalc=1
              and not exists(select 1 from smc_student_coursebalance as cb,smc_course as co where cb.course_id=co.course_id and cb.student_id=scb.student_id and cb.school_id=scb.school_id and co.coursetype_id=sc.coursetype_id and (cb.coursebalance_figure>0 or cb.coursebalance_time>0))
              group by sc.coursetype_id
              having stuchange_code='' or stuchange_code='D04'
              ";
        $typeList = $this->DataControl->selectClear($sql);
        if (!$typeList) {
            $this->error = true;
            $this->errortip = "无可流失班组";
            return false;
        }

        return $typeList;


    }

    function getFreeapplyList($request)
    {
        $datawhere = " a.company_id ='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.class_cnname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['freeapply_status']) && $request['freeapply_status'] !== '') {
            $datawhere .= " and a.freeapply_status ='{$request['freeapply_status']}'";
        }

        $sql = "select a.freeapply_id,b.class_cnname,b.class_branch,a.freeapply_type,a.freeapply_day,concat(a.freeapply_starttime,'-',a.freeapply_endtime) as timerange,c.staffer_cnname as main_staffer_cnname,c.staffer_enname as main_staffer_enname,d.teachtype_name as main_teachtype_name,e.staffer_cnname as fu_staffer_cnname,e.staffer_enname as fu_staffer_enname,f.teachtype_name as fu_teachtype_name,g.classroom_cnname,a.freeapply_reason,h.staffer_cnname,FROM_UNIXTIME(a.freeapply_createtime,'%Y-%m-%d %H:%i') as freeapply_createtime,a.freeapply_status,a.freeapply_note
				from smc_class_freeapply as a 
				left join smc_class as b on b.class_id=a.class_id
				left join smc_staffer as c on c.staffer_id=a.main_staffer_id
				left join smc_code_teachtype as d on d.teachtype_code=a.main_teachtype_code and d.company_id='{$this->company_id}'
				left join smc_staffer as e on e.staffer_id=a.fu_staffer_id
				left join smc_code_teachtype as f on f.teachtype_code=a.fu_teachtype_code and f.company_id='{$this->company_id}'
				left join smc_classroom as g on g.classroom_id=a.classroom_id
				left join smc_staffer as h on h.staffer_id=a.staffer_id
 				where {$datawhere} 
 				order by a.freeapply_id desc";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $freeapplyList = $this->DataControl->selectClear($sql);

        if (!$freeapplyList) {
            $this->error = true;
            $this->errortip = "无申请数据";
            return false;
        }

        $status=$this->LgArraySwitch(array("-2"=>"已撤销","-1"=>"已拒绝","0"=>"待审核","1"=>"已通过"));
        $type=$this->LgArraySwitch(array("0"=>"暖身课","1"=>"复习课"));

        foreach ($freeapplyList as &$applyOne) {

            if($applyOne['main_staffer_cnname'] && $applyOne['main_staffer_cnname']!='null'){
                if($applyOne['main_staffer_enname'] && $applyOne['main_staffer_enname']!='null'){
                    $applyOne['main_staffer_cnname'].='-'.$applyOne['main_staffer_enname'];
                }
                if($applyOne['main_teachtype_name'] && $applyOne['main_teachtype_name']!='null'){
                    $applyOne['main_staffer_cnname'].='('.$applyOne['main_teachtype_name'].')';
                }
            }

            if($applyOne['fu_staffer_cnname'] && $applyOne['fu_staffer_cnname']!='null'){
                if($applyOne['main_staffer_enname'] && $applyOne['main_staffer_enname']!='null'){
                    $applyOne['main_staffer_cnname'].='-'.$applyOne['main_staffer_enname'];
                }
                if($applyOne['main_teachtype_name'] && $applyOne['main_teachtype_name']!='null'){
                    $applyOne['main_staffer_cnname'].='('.$applyOne['main_teachtype_name'].')';
                }
            }
            $applyOne['freeapply_status_name']=$status[$applyOne['freeapply_status']];
            $applyOne['freeapply_type_name']=$type[$applyOne['freeapply_type']];

            if($applyOne['freeapply_status']==0){
                $applyOne['freeapply_note']='--';
            }

        }

        $count_sql = "select a.freeapply_id
				from smc_class_freeapply as a 
				left join smc_class as b on b.class_id=a.class_id
 				where {$datawhere} ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $freeapplyList;
        return $data;
    }

    function revokeFreeapply($request){

        $applyOne=$this->DataControl->getFieldOne("smc_class_freeapply","freeapply_status","freeapply_id='{$request['freeapply_id']}'");

        if($applyOne['freeapply_status']!=0){
            $this->error = true;
            $this->errortip = "该审核不可撤销";
            return false;
        }

        $data=array();
        $data['freeapply_status']=-2;
        $data['freeapply_staffer_id']=$request['staffer_id'];
        $data['freeapply_updatatime']=time();
        if($this->DataControl->updateData("smc_class_freeapply","freeapply_id='{$request['freeapply_id']}'",$data)){
            return true;
        }else{
            $this->error = true;
            $this->errortip = "撤销失败";
            return false;
        }


    }

    function getAdjustapplyList($request)
    {
        $datawhere = " a.company_id ='{$request['company_id']}' and a.school_id='{$request['school_id']}' ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.class_cnname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['adjustapply_status']) && $request['adjustapply_status'] !== '') {
            $datawhere .= " and a.adjustapply_status ='{$request['adjustapply_status']}'";
        }

        if (isset($request['adjustapply_type']) && $request['adjustapply_type'] !== '') {
            $datawhere .= " and a.adjustapply_type ='{$request['adjustapply_type']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and m.school_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['adjustapply_class']) && $request['adjustapply_class'] !== '') {
            $datawhere .= " and a.adjustapply_class ='{$request['adjustapply_class']}'";
        }

        if (isset($request['difference_class']) && $request['difference_class'] !== '') {

            $datawhere .= " and a.adjustapply_type=0";

            if($request['difference_class']==0){
                $datawhere .= " and ABS(DATEDIFF(a.adjustapply_day, d.hour_day)) > 30";
            }else{
                $datawhere .= " and ABS(DATEDIFF(a.adjustapply_day, d.hour_day)) <= 30";
            }

        }

        $sql = "select a.adjustapply_id,a.class_id,b.class_cnname,b.class_branch,a.adjustapply_type,if(a.adjustapply_status=1,d.hour_formerday,d.hour_day) as hour_day,a.adjustapply_day,c.staffer_cnname,a.adjustapply_reason,FROM_UNIXTIME(a.adjustapply_createtime,'%Y-%m-%d %H:%i') as adjustapply_createtime,a.adjustapply_status,a.adjustapply_note,a.adjustapply_class,if(a.adjustapply_type=0,ABS(ABS(DATEDIFF(a.adjustapply_day, d.hour_day))-1),'--') AS days_difference,a.adjustapply_fileurl,ifnull(n.coursetype_cnname,'--') as coursetype_cnname
				from smc_class_hour_adjustapply as a 
				left join smc_class as b on b.class_id=a.class_id
				left join smc_staffer as c on c.staffer_id=a.staffer_id
				left join smc_class_hour as d on d.hour_id=a.hour_id
				left join smc_course as m on m.course_id=b.course_id
				left join smc_code_coursetype as n on n.coursetype_id=m.coursetype_id
 				where {$datawhere} 
 				order by a.adjustapply_id desc";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $adjustapplyList = $this->DataControl->selectClear($sql);

        if (!$adjustapplyList) {
            $this->error = true;
            $this->errortip = "无申请数据";
            return false;
        }

        $status=$this->LgArraySwitch(array("-2"=>"已撤销","-1"=>"已拒绝","0"=>"待审核","1"=>"已通过"));
        $type=$this->LgArraySwitch(array("0"=>"班级调课","1"=>"全校调课"));
        $class=$this->LgArraySwitch(array("0"=>"政策调课","1"=>"其他调课"));

        foreach ($adjustapplyList as &$applyOne) {

            $applyOne['adjustapply_status_name']=$status[$applyOne['adjustapply_status']];
            $applyOne['adjustapply_type_name']=$type[$applyOne['adjustapply_type']];
            $applyOne['adjustapply_class_name']=$class[$applyOne['adjustapply_class']];

            if($applyOne['adjustapply_status']==0){
                $applyOne['adjustapply_note']='--';
            }

            if($applyOne['adjustapply_type']==1){
                $applyOne['class_cnname']='--';
                $applyOne['class_branch']='--';
                $applyOne['hour_day']=$applyOne['adjustapply_day'];
                $applyOne['adjustapply_day']='--';
            }
            $applyOne['adjustapply_fileurl_name']=$applyOne['adjustapply_fileurl']!=''?$this->LgStringSwitch('查看附件'):'';

        }

        $count_sql = "select a.adjustapply_id
				from smc_class_hour_adjustapply as a 
				left join smc_class as b on b.class_id=a.class_id
				left join smc_staffer as c on c.staffer_id=a.staffer_id
				left join smc_class_hour as d on d.hour_id=a.hour_id
 				where {$datawhere}  ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $adjustapplyList;
        return $data;
    }

    function batchRevokeAdjustapply($request){
        $applyList=json_decode(stripslashes($request['apply_list']), true);

        if(!$applyList){
            $this->error = true;
            $this->errortip = "请选择审核数据";
            return false;
        }

        $t_num=0;
        $f_num=0;

        foreach($applyList as $applyOne){
            $data=array();
            $data['adjustapply_id']=$applyOne['adjustapply_id'];
            $data['staffer_id']=$this->staffer_id;

            $bool=$this->revokeAdjustapply($data);

            if($bool){
                $t_num++;
            }else{
                $f_num++;
            }
        }

        if($t_num==0){
            $this->error = true;
            $this->errortip = "撤销失败";
            return false;
        }

        $data=array();
        $data['t_num']=$t_num;
        $data['f_num']=$f_num;

        return $data;
    }

    function revokeAdjustapply($request){

        $applyOne=$this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_status,requestid","adjustapply_id='{$request['adjustapply_id']}'");

        if($applyOne['adjustapply_status']!=0){
            $this->error = true;
            $this->errortip = "该审核不可撤销";
            return false;
        }

        if($applyOne['requestid']>0){
            $Model = new \Model\Smc\FanweiModel();
            $res = $Model->cancelAdjustApply($request['adjustapply_id']);

            if(!$res){
                $this->error = true;
                $this->errortip = "泛微平台请求失败";
                return false;
            }
        }

        $data=array();
        $data['adjustapply_status']=-2;
        $data['adjustapply_staffer_id']=$request['staffer_id'];
        $data['adjustapply_updatatime']=time();
        if($this->DataControl->updateData("smc_class_hour_adjustapply","adjustapply_id='{$request['adjustapply_id']}'",$data)){
            return true;
        }else{
            $this->error = true;
            $this->errortip = "撤销失败";
            return false;
        }


    }

    function applySchAdjustapply($request){

        if($this->DataControl->getFieldOne("smc_class_hour_adjustapply","adjustapply_id","school_id='{$request['school_id']}' and adjustapply_type=1 and adjustapply_day='{$request['adjustapply_day']}' and adjustapply_status=0")){
            $this->error = true;
            $this->errortip = "存在未审核全校调课申请,不可再次申请";
            return false;
        }

        if(!isset($request['adjustapply_day']) || $request['adjustapply_day']==''){
            $this->error = true;
            $this->errortip = "请选择调课日期";
            return false;
        }


        $data=array();
        $data['company_id']=$request['company_id'];
        $data['school_id']=$request['school_id'];
        $data['adjustapply_type']=1;
        $data['adjustapply_class']=$request['adjustapply_class'];
        $data['adjustapply_day']=$request['adjustapply_day'];
        $data['adjustapply_reason']=$request['adjustapply_reason'];
        $data['adjustapply_fileurl']=$request['adjustapply_fileurl'];
        $data['staffer_id']=$request['staffer_id'];
        $data['adjustapply_createtime']=time();
        $data['is_need_process']=1;

        if($this->DataControl->insertData("smc_class_hour_adjustapply",$data)){
            $this->oktip="申请成功";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "申请失败";
            return false;
        }
    }

    function getJindieErrorList($request){

        $datawhere = " b.company_id ='{$request['company_id']}' ";

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere .= " and b.school_id = '{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.student_cnname like '%{$request['keyword']}%' 
            or c.student_branch like '%{$request['keyword']}%' or b.trading_pid like '%{$request['keyword']}%' or b.refund_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and a.create_time >= '{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and a.create_time <= '{$request['end_time']}'";
        }


        if (isset($request['is_handle']) && $request['is_handle'] !== '') {
            $datawhere .= " and a.is_handle ='{$request['is_handle']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT b.trading_pid,b.refund_pid,c.student_cnname,c.student_branch,b.refund_status,b.refund_payprice,a.error_message,a.create_time,d.staffer_cnname,a.is_handle,if(a.is_handle=1,e.staffer_cnname,'--') as handle_staffer_cnname,if(a.is_handle=1,a.update_time,'--') as handle_time
        ,c.student_img,f.school_cnname,b.student_id,a.id,c.student_sex,b.school_id
                from api_call_log as a 
                inner join smc_refund_order as b on b.refund_pid=a.pid
                inner join smc_student as c on c.student_id=b.student_id
                left join smc_staffer as d on d.staffer_id=a.user_id
                left join smc_staffer as e on d.staffer_id=a.staffer_id
                left join smc_school as f on f.school_id=b.school_id
                where {$datawhere} and a.is_success=0 and b.refund_status>=0
                order by a.id desc
                ";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $errorList = $this->DataControl->selectClear($sql);

        if (!$errorList) {
            $this->error = true;
            $this->errortip = "无申请数据";
            return false;
        }

        $status=$this->LgArraySwitch(array("0"=>"待处理","1"=>"已处理"));
        // 退款状态0-申请 1-审核通过 2-处理中 3-确定金额 4-完成退款 -1-拒绝退款
        $refundStatus=$this->LgArraySwitch(array("0"=>"申请","1"=>"审核通过","2"=>"处理中","3"=>"确定金额","4"=>"完成退款","-1"=>"拒绝退款"));

        foreach($errorList as &$errorOne){
            $errorOne['order_status_name']=$refundStatus[$errorOne['refund_status']];
            $errorOne['is_handle_name']=$status[$errorOne['is_handle']];
        }

        $count_sql = "select count(a.id) as allnum
                from api_call_log as a 
                inner join smc_refund_order as b on b.refund_pid=a.pid
                where {$datawhere} and a.is_success=0 and b.refund_status<>4
                ";
        $db_nums = $this->DataControl->selectOne($count_sql);
        if ($db_nums) {
            $allnum = $db_nums['allnum'];
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $errorList;
        return $data;

    }


    function submitErrorHandle($request){

        $errorOne=$this->DataControl->getFieldOne("api_call_log","id,is_handle,pid","id='{$request['id']}'");
        if($errorOne['is_handle']==1){
            $this->error = true;
            $this->errortip = "已处理";
            return false;
        }

        $sql="select api_name from api_call_log where pid='{$errorOne['pid']}' and api_name in ('jindie_save_expense','jindie_submit_expense') order by id desc limit 0,1";
        $apiName=$this->DataControl->selectOne($sql);

        if(!$apiName){
            $this->error = true;
            $this->errortip = "无处理数据";
            return false;
        }

        $refundOne=$this->DataControl->getFieldOne("smc_refund_order","refund_pid,staffer_id,school_id","refund_pid='{$errorOne['pid']}'");

        $JindieModel = new \Model\Smc\JindieModel();

        $request['refund_pid']=$errorOne['pid'];

        if($apiName['api_name']=='jindie_save_expense'){
            $res = $JindieModel->saveExpenseReimbursement($request);
            if(!$res){

                $data=array();
                $data['is_handle']=1;
                $data['staffer_id']=$this->staffer_id;
                $data['update_time']=date('Y-m-d H:i:s');
                $this->DataControl->updateData("api_call_log","id='{$errorOne['id']}'",$data);

                $this->error = true;
                $this->errortip = $JindieModel->errortip;
                return false;
            }

        }else{
            $res = $JindieModel->submitExpenseReimbursement($request);
            if(!$res){

                $data=array();
                $data['is_handle']=1;
                $data['staffer_id']=$this->staffer_id;
                $data['update_time']=date('Y-m-d H:i:s');
                $this->DataControl->updateData("api_call_log","id='{$errorOne['id']}'",$data);

                $this->error = true;
                $this->errortip = $JindieModel->errortip;
                return false;
            }
        }

        $data=array();
        $data['is_handle']=1;
        $data['staffer_id']=$this->staffer_id;
        $data['update_time']=date('Y-m-d H:i:s');
        if($this->DataControl->updateData("api_call_log","id='{$errorOne['id']}'",$data)){

            $publicArray=[
                'company_id'=>$this->company_id,
                'school_id'=>$refundOne['school_id'],
                'staffer_id'=>$this->staffer_id
            ];

            $Model = new \Model\Gmc\OrderModel($publicArray);

            $data=[
                'refund_pid'=>$request['refund_pid'],
                'reason'=>'金蝶报销提交成功',
                'staffer_id'=>$this->staffer_id,
                'is_adopt'=>'1',
                'is_skip'=>'1',
            ];

            $Model->examineRefundOrder($data);

            return true;
        }else{
            $this->error = true;
            $this->errortip = "处理失败";
            return false;
        }

    }



}
