<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  CrmModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //招生来源列表
    function getFrommediaList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.frommedia_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.frommedia_id,
                p.frommedia_name,
                p.frommedia_remk,
                p.frommedia_isschoolchose 
            FROM
                crm_code_frommedia AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.frommedia_id DESC    
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.frommedia_id)
            FROM
                crm_code_frommedia AS p
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        if ($postList) {
            foreach ($postList as &$postVar) {
                if ($postVar['frommedia_isschoolchose'] == '1') {
                    $postVar['frommedia_isschoolchosename'] = $this->LgStringSwitch('允许');
                } else {
                    $postVar['frommedia_isschoolchosename'] = $this->LgStringSwitch('不允许');
                }
            }
        }


        $fieldstring = array('frommedia_name ', 'frommedia_remk');
        $fieldname = $this->LgArraySwitch(array('渠道类型', '来源备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无招生渠道信息", 'result' => $result);
        }

        return $res;
    }


    function Getmedia($paramArray)
    {
        $sql = "
            SELECT
                p.frommedia_name
            FROM
                crm_code_frommedia AS p
            WHERE
                p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.frommedia_id DESC  ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["frommedia_name"] = "中文名";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '个人信息查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '个人信息查看失败', 'result' => $result);
        }
        return $res;
    }

    //添加招生来源
    function addFrommediaAction($paramArray)
    {
        $data = array();
        $data['frommedia_name'] = $paramArray['frommedia_name'];
        $data['frommedia_remk'] = $paramArray['frommedia_remk'];
        $data['company_id'] = $paramArray['company_id'];
        $data['frommedia_isschoolchose'] = 1;//是的 所有的都允许学校选择

        $field = array();
        $field['frommedia_name'] = "来源名称";
        $field['frommedia_remk'] = "来源备注";
        $field['company_id'] = "所属公司";
        $field['frommedia_isschoolchose'] = "是否允许学校选择";

        $frommedia_name = $this->DataControl->getFieldOne('crm_code_frommedia', 'frommedia_id', "frommedia_name = '{$paramArray['frommedia_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($frommedia_name) {
            ajax_return(array('error' => 1, 'errortip' => "招生来源名称已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('crm_code_frommedia', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加招生来源成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加招生来源失败', 'result' => $result);
        }
        return $res;
    }

    //编辑招生来源
    function updateFrommediaAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id", "frommedia_id = '{$paramArray['frommedia_id']}'");
        if ($postOne) {
            $data = array();
            $data['frommedia_name'] = $paramArray['frommedia_name'];
            $data['frommedia_remk'] = $paramArray['frommedia_remk'];
            $data['frommedia_isschoolchose'] = 1;

            $field = array();
            $field['frommedia_name'] = "来源名称";
            $field['frommedia_remk'] = "来源备注";
            $field['frommedia_isschoolchose'] = "是否允许学校选择";

            $frommedia_name = $this->DataControl->getFieldOne('crm_code_frommedia', 'frommedia_name', "frommedia_id = '{$paramArray['frommedia_id']}'");
            if ($paramArray['frommedia_name'] != $frommedia_name['frommedia_name']) {
                $frommedia_name = $this->DataControl->getFieldOne('crm_code_frommedia', 'frommedia_id', "frommedia_name = '{$paramArray['frommedia_name']}' and company_id = '{$paramArray['company_id']}'");
                if ($frommedia_name) {
                    ajax_return(array('error' => 1, 'errortip' => "招生来源名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_frommedia", "frommedia_id = '{$paramArray['frommedia_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "招生来源修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '招生来源修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除招生来源
    function delFrommediaAction($paramArray)
    {
        $frommediaOne = $this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id,frommedia_name", "frommedia_id = '{$paramArray['frommedia_id']}' AND company_id = '{$paramArray['company_id']}'");
        if ($frommediaOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }

            $a = $this->DataControl->getFieldOne("crm_client", "client_id", "client_source = '{$frommediaOne['frommedia_name']}' AND company_id = '{$paramArray['company_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该渠道类型已被名单使用，不可删除"), $this->companyOne['company_language']);
            }
            $b = $this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "frommedia_name = '{$frommediaOne['frommedia_name']}' AND company_id = '{$paramArray['company_id']}'");
            if ($b) {
                ajax_return(array('error' => 1, 'errortip' => "该渠道类型已被活动使用，不可删除"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->delData("crm_code_frommedia", "frommedia_id = '{$paramArray['frommedia_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除招生来源成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除招生来源失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //沟通方式列表
    function getCommodeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.commode_name like '%{$paramArray['keyword']}%' or p.commode_remk like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.commode_id,
                p.commode_name,
                p.commode_remk
            FROM
                crm_code_commode AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.commode_id DESC    
            LIMIT {$pagestart},{$num}";

        $CommodeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.commode_id)
            FROM
                crm_code_commode AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('commode_name ', 'commode_remk');
        $fieldname = $this->LgArraySwitch(array('沟通方式名称', '备注'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CommodeList) {
            $result['list'] = $CommodeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无沟通方式", 'result' => $result);
        }

        return $res;
    }

    //添加沟通方式
    function addCommodeAction($paramArray)
    {
        $data = array();
        $data['commode_name'] = $paramArray['commode_name'];
        $data['commode_remk'] = $paramArray['commode_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['commode_name'] = "方式名称";
        $field['commode_remk'] = "备注信息";
        $field['company_id'] = "所属公司";

        $commode_name = $this->DataControl->getFieldOne('crm_code_commode', 'commode_id', "commode_name = '{$paramArray['commode_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($commode_name) {
            ajax_return(array('error' => 1, 'errortip' => "沟通方式名称已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('crm_code_commode', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加沟通方式成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加沟通方式失败', 'result' => $result);
        }
        return $res;
    }

    //编辑沟通方式
    function updateCommodeAction($paramArray)
    {
        $TeachingtypeOne = $this->DataControl->getFieldOne("crm_code_commode", "commode_id", "commode_id = '{$paramArray['commode_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['commode_name'] = $paramArray['commode_name'];
            $data['commode_remk'] = $paramArray['commode_remk'];

            $field = array();
            $field['commode_name'] = "方式名称";
            $field['commode_remk'] = "备注信息";

            $commode_name = $this->DataControl->getFieldOne('crm_code_commode', 'commode_name', "commode_id = '{$paramArray['commode_id']}'");
            if ($paramArray['commode_name'] != $commode_name['commode_name']) {
                $commode_name = $this->DataControl->getFieldOne('crm_code_commode', 'commode_id', "commode_name = '{$paramArray['commode_name']}'  and company_id = '{$paramArray['company_id']}'");
                if ($commode_name) {
                    ajax_return(array('error' => 1, 'errortip' => "沟通方式名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_commode", "commode_id = '{$paramArray['commode_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "沟通方式修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '沟通方式修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除沟通方式
    function delCommodeAction($paramArray)
    {
        $CommodeOne = $this->DataControl->getFieldOne("crm_code_commode", "commode_name", "commode_id = '{$paramArray['commode_id']}'");
        if ($CommodeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("crm_client_track", "track_id", "track_linktype = '{$CommodeOne['commode_name']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该沟通方式已被使用，无法删除！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_commode", "commode_id = '{$paramArray['commode_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除沟通方式成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除沟通方式失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //变更审核列表
    function changedemo($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['changedemo_class']) && $paramArray['changedemo_class'] !== "") {
            $datawhere .= " and p.changedemo_class ='{$paramArray['changedemo_class']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.changedemo_id,
                p.changedemo_reason,
                p.changedemo_class
            FROM
                gmc_code_changedemo AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.changedemo_id DESC    
            LIMIT {$pagestart},{$num}";

        $changedemoList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "无效变更", "1" => "有效变更"));

        foreach ($changedemoList as &$val) {
            $val['changedemo_class_name'] = $status[$val['changedemo_class']];
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.changedemo_id)
            FROM
                gmc_code_changedemo AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('changedemo_class_name', 'changedemo_reason');
        $fieldname = $this->LgArraySwitch(array('审核类型', '模板内容'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($changedemoList) {
            $result['list'] = $changedemoList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无变更审核模板", 'result' => $result);
        }

        return $res;
    }

    //添加变更审核
    function addChangedemoAction($paramArray)
    {
        $data = array();
        $data['changedemo_reason'] = $paramArray['changedemo_reason'];
        $data['changedemo_class'] = $paramArray['changedemo_class'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['changedemo_reason'] = "模板内容";
        $field['changedemo_class'] = "模板类别0 无效变更 1 有效变更";
        $field['company_id'] = "所属公司";

//        $commode_name = $this->DataControl->getFieldOne('crm_code_commode', 'commode_id', "commode_name = '{$paramArray['commode_name']}' and company_id = '{$paramArray['company_id']}'");
//        if ($commode_name) {
//            ajax_return(array('error' => 1, 'errortip' => "沟通方式名称已存在!"), $this->companyOne['company_language']);
//        }

        if ($this->DataControl->insertData('gmc_code_changedemo', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加变更审核模板成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加变更审核模板失败', 'result' => $result);
        }
        return $res;
    }

    //编辑变更审核模板
    function updateChangedemoAction($paramArray)
    {
        $TeachingtypeOne = $this->DataControl->getFieldOne("gmc_code_changedemo", "changedemo_id", "changedemo_id = '{$paramArray['changedemo_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['changedemo_reason'] = $paramArray['changedemo_reason'];
            $data['changedemo_class'] = $paramArray['changedemo_class'];

            $field = array();
            $field['changedemo_reason'] = "模板内容";
            $field['changedemo_class'] = "模板类别0 无效变更 1 有效变更";

//            $commode_name = $this->DataControl->getFieldOne('crm_code_commode', 'commode_name', "commode_id = '{$paramArray['commode_id']}'");
//            if ($paramArray['commode_name'] != $commode_name['commode_name']) {
//                $commode_name = $this->DataControl->getFieldOne('crm_code_commode', 'commode_id', "commode_name = '{$paramArray['commode_name']}'  and company_id = '{$paramArray['company_id']}'");
//                if ($commode_name) {
//                    ajax_return(array('error' => 1, 'errortip' => "沟通方式名称已存在!"), $this->companyOne['company_language']);
//                }
//            }

            if ($this->DataControl->updateData("gmc_code_changedemo", "changedemo_id = '{$paramArray['changedemo_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "变更审核模板修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '变更审核模板修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除变更审核
    function delChangedemoAction($paramArray)
    {
        $CommodeOne = $this->DataControl->getFieldOne("gmc_code_changedemo", "changedemo_id", "changedemo_id = '{$paramArray['changedemo_id']}'");
        if ($CommodeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->delData("gmc_code_changedemo", "changedemo_id = '{$paramArray['changedemo_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除变更审核模板成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除变更审核模板失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //来源渠道列表
    function getChannelList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.channel_name like '%{$paramArray['keyword']}%' or p.channel_linkman like '%{$paramArray['keyword']}%' or p.channel_tel like '%{$paramArray['keyword']}%' or p.channel_code like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['commode_name']) && $paramArray['commode_name'] !== '') {
            $datawhere .= "  and p.channel_medianame='{$paramArray['commode_name']}' ";
        }

        if (isset($paramArray['channel_way']) && $paramArray['channel_way'] !== '') {
            $datawhere .= "  and p.channel_way='{$paramArray['channel_way']}' ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
        }

        if ($paramArray['dataequity'] != '1') {
            $sql = "
            SELECT
                p.channel_id,
                p.channel_board,
                p.channel_name,
                p.channel_linkman,
                p.channel_tel,
                p.channel_medianame,
                p.channel_isreferral,
                p.channel_isreferral as channel_isreferral_name,
                p.channel_isbazaar,
                p.channel_isbazaar as channel_isbazaar_name,
                p.channel_quality,
                p.channel_code,
                p.channel_createtime,
                p.channel_isuse,
                p.staffer_id,
                p.channel_way,
                p.channel_push,
                p.channel_isschoolchose,
                p.channel_istoschool,
                (select group_concat(cyo.organize_cnname) from  crm_channel_organize as  co
                  left join gmc_company_organize as cyo on cyo.organize_id = co.organize_id
                  where co.channel_id = p.channel_id ) as organize_name,
                  (
SELECT
	group_concat( cyo.organize_id ) 
FROM
	crm_channel_organize AS co
	LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
WHERE
	co.channel_id = p.channel_id 
	) AS organize_id
            FROM
                crm_code_channel AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}' having organize_id like '%{$organize_id['organize_id']}%'
            ORDER BY
                p.channel_id DESC    
            LIMIT {$pagestart},{$num}";
        } else {
            $sql = "
            SELECT
                p.channel_id,
                p.channel_board,
                p.channel_name,
                p.channel_linkman,
                p.channel_tel,
                p.channel_medianame,
                p.channel_isreferral,
                p.channel_isreferral as channel_isreferral_name,
                p.channel_isbazaar,
                p.channel_isbazaar as channel_isbazaar_name,
                p.channel_quality,
                p.channel_code,
                p.channel_createtime,
                p.channel_isuse,
                p.staffer_id,
                p.channel_way,
                p.channel_push,
                p.channel_isschoolchose,
                p.channel_istoschool,
                (select group_concat(cyo.organize_cnname) from  crm_channel_organize as  co
                  left join gmc_company_organize as cyo on cyo.organize_id = co.organize_id
                  where co.channel_id = p.channel_id ) as organize_name
            FROM
                crm_code_channel AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.channel_id DESC    
            LIMIT {$pagestart},{$num}";
        }


        $ChannelList = $this->DataControl->selectClear($sql);
        if ($ChannelList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $quality = $this->LgArraySwitch(array("0" => "默认毛名单", "1" => "默认有效名单"));

            foreach ($ChannelList as $key => &$val) {
                $ChannelList[$key]['channel_createtime'] = date('Y-m-d H:i:s', $val['channel_createtime']);
                $isaccount = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
                if ($ChannelList[$key]['staffer_id'] == $paramArray['staffer_id'] || $isaccount['account_class'] == '1') {
                    $ChannelList[$key]['isFouder'] = '1';
                } else {
                    $ChannelList[$key]['isFouder'] = '0';

                }
                if ($val['channel_way'] == 0) {
                    $ChannelList[$key]['channel_way_name'] = $this->LgStringSwitch("线上");
                } else {
                    $ChannelList[$key]['channel_way_name'] = $this->LgStringSwitch("线下");
                }
                if ($val['channel_push'] == 0) {
                    $ChannelList[$key]['channel_push_name'] = $this->LgStringSwitch("否");
                } else {
                    $ChannelList[$key]['channel_push_name'] = $this->LgStringSwitch("是");
                }
                if ($val['channel_isschoolchose'] == 1) {
                    $ChannelList[$key]['channel_isschoolchose'] = $this->LgStringSwitch("允许");
                } else {
                    $ChannelList[$key]['channel_isschoolchose'] = $this->LgStringSwitch("不允许");
                }
                $val['channel_isreferral_name'] = $status[$val['channel_isreferral_name']];
                $val['channel_isbazaar_name'] = $status[$val['channel_isbazaar_name']];
                $val['channel_quality_name'] = $quality[$val['channel_quality']];

            }
        }

        if ($paramArray['dataequity'] == '1') {
            $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.channel_id)
            FROM
                crm_code_channel AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
            $allnums = $all_num[0][0];
        } else {
            $all_num = $this->DataControl->selectClear("
            SELECT
                  (
                      SELECT
                        group_concat( cyo.organize_id ) 
                      FROM
                        crm_channel_organize AS co
                        LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
                      WHERE
                        co.channel_id = p.channel_id 
                  ) AS organize_id
            FROM
                crm_code_channel AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}' having organize_id like '%{$organize_id['organize_id']}%'");
            if ($all_num) {
                $allnums = count($all_num);
            } else {
                $allnums = 0;
            }
        }


        $fieldstring = array('channel_name ', 'channel_linkman', 'channel_tel','channel_board', 'channel_medianame', 'channel_way_name', 'organize_name', 'channel_isreferral_name', 'channel_isbazaar_name','channel_push_name', 'channel_quality_name', 'channel_createtime', 'channel_isuse');
        $fieldname = $this->LgArraySwitch(array('渠道名称', '联系人', '联系电话','渠道板块', '渠道类型', '渠道方式', '所属组织范围', '是否转介绍', '是否市场专用','是否地推专用','渠道质量', '录入时间', '是否启用'));
        $fieldcustom = array("1", "1", "1", "1", "1", '1', "1", '1', '1', '1', '1', '1', '1', '1');
        $fieldshow = array("1", "1", "1", "1", "1", '1', "1", '1', '1', '1', '1', '1', '1', '1');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == 'channel_isuse') {
                $field[$i]["isswitch"] = 1;
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ChannelList) {
            $result['list'] = $ChannelList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无渠道来源信息", 'result' => $result);
        }

        return $res;
    }

    //招生渠道变更审核列表
    function getChannellogList($paramArray)
    {
        $datawhere = "ch.company_id = '{$paramArray['company_id']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and from_unixtime(ch.channellog_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and from_unixtime(ch.channellog_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }

        if (isset($paramArray['channellog_status']) && $paramArray['channellog_status'] !== "") {
            $datawhere .= " and ch.channellog_status ='{$paramArray['channellog_status']}'";
        }

        if (isset($paramArray['channellog_level']) && $paramArray['channellog_level'] !== "") {
            $datawhere .= " and ch.channellog_level ='{$paramArray['channellog_level']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and sc.school_id ='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['to_channel_id']) && $paramArray['to_channel_id'] !== "") {
            $datawhere .= " and cc.channel_id ='{$paramArray['to_channel_id']}'";
        }

        if ($paramArray['type'] == '1') {
            if (isset($paramArray['staffer_id']) && $paramArray['staffer_id'] !== "") {
                $datawhere .= " and ch.staffer_id ='{$paramArray['staffer_id']}'";
            }
        }

        if (isset($paramArray['to_channel_medianame']) && $paramArray['to_channel_medianame'] !== "") {
            $datawhere .= " and cc.channel_medianame ='{$paramArray['to_channel_medianame']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }

        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "
            SELECT
	            h.school_cnname,
	            h.school_branch,
               (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = h.district_id) AS district_cnname,
                c.client_id,
                c.client_cnname,
                c.client_enname,
                c.client_sex,
                c.client_age,
                c.client_mobile,
                cn.channel_name AS from_channel_name,
                cn.channel_medianame AS from_channel_medianame,
                cc.channel_name AS to_channel_name,
                cc.channel_id AS to_channel_id,
                cc.channel_medianame AS to_channel_medianame,
                s.staffer_cnname,
                ch.channellog_note,
                ch.channellog_status,
                ch.channellog_status as channellog_status_name,
                ch.channellog_level,
                ch.channellog_id,
                ch.channellog_img,
                ch.channellog_sponsor,
                ch.channellog_teachername,
	            sc.school_id,
	            cn.channel_maxday,
	            cn.channel_minday,
	            from_unixtime(c.client_createtime,'%Y-%m-%d') as client_createtime,
	            from_unixtime(c.client_createtime,'%Y-%m-%d %H:%i') as createtime,
	            from_unixtime(ch.channellog_createtime,'%Y-%m-%d') as channellog_createtime,
	            f.family_relation,
	            c.client_remark
            FROM
                crm_client_channellog AS ch
                LEFT JOIN crm_client AS c ON ch.client_id = c.client_id
                LEFT JOIN crm_code_channel AS cc ON cc.channel_id = ch.to_channel_id
                LEFT JOIN crm_code_channel AS cn ON cn.channel_id = ch.from_channel_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = ch.staffer_id
                LEFT JOIN crm_client_schoolenter AS sc ON sc.client_id = ch.client_id
                LEFT JOIN smc_school AS h ON sc.school_id = h.school_id
                LEFT JOIN crm_client_family as f on f.client_id = ch.client_id
            WHERE
                {$datawhere}
            GROUP BY
	            ch.channellog_id
            ORDER BY
                ch.channellog_id DESC ";

        $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "审核通过", "-1" => "审核未通过"));

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无招生渠道变更审核列表";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];

                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];
                    $datearray['from_channel_medianame'] = $dateexcelvar['from_channel_medianame'];
                    $datearray['from_channel_name'] = $dateexcelvar['from_channel_name'];
                    $datearray['to_channel_medianame'] = $dateexcelvar['to_channel_medianame'];
                    $datearray['to_channel_name'] = $dateexcelvar['to_channel_name'];

                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['channellog_note'] = $dateexcelvar['channellog_note'];
                    $datearray['channellog_status_name'] = $status[$dateexcelvar['channellog_status_name']];
                    $datearray['channellog_createtime'] = $dateexcelvar['channellog_createtime'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelfileds = array('school_cnname','school_branch','district_cnname','client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_mobile', 'client_createtime', 'from_channel_medianame', 'from_channel_name', 'to_channel_medianame', 'to_channel_name', 'staffer_cnname', 'channellog_note', 'channellog_status_name', 'channellog_createtime');
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '所属区域','中文名', '英文名', '性别', '年龄', '手机号', '名单创建日期', '原渠道类型', '原渠道明细', '目标渠道类型', '目标渠道明细', '申请人', '更改原因', '审核状态', '申请时间'));
            $tem_name = $this->LgStringSwitch('招生渠道变更审核列表.xlsx');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $ChannellogList = $this->DataControl->selectClear($sql);

            if ($ChannellogList) {
                foreach ($ChannellogList as &$val) {
                    $val['channellog_status_name'] = $status[$val['channellog_status_name']];
                    $time = date('Y-m-d', time());
//                    $min = date("Y-m-d", strtotime($val['client_createtime'] . "+{$val['channel_minday']} day"));
                    $max = date("Y-m-d", strtotime($val['client_createtime'] . "+{$val['channel_maxday']} day"));
                    if ($val['channel_maxday'] == 0 || $time <= $max) {
                        $val['status'] = 1;
                    } else {
                        $val['status'] = 0;
                    }
                    if ($val['school_cnname'] == '') {
                        $val['school_cnname'] = '待分配';
                    }
                }
            }

            $all_num = $this->DataControl->selectClear("
            SELECT
                ch.channellog_id 
            FROM
                crm_client_channellog AS ch
                LEFT JOIN crm_client AS c ON ch.client_id = c.client_id
                LEFT JOIN crm_code_channel AS cc ON cc.channel_id = ch.to_channel_id
                LEFT JOIN crm_code_channel AS cn ON cn.channel_id = ch.from_channel_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = ch.staffer_id
                LEFT JOIN crm_client_schoolenter AS sc ON sc.client_id = ch.client_id
                LEFT JOIN smc_school AS h ON sc.school_id = h.school_id
                LEFT JOIN crm_client_family as f on f.client_id = ch.client_id
            WHERE
                {$datawhere}
            GROUP BY
	            ch.channellog_id");
            $allnums = is_array($all_num) ? count($all_num) : 0;

            $fieldstring = array('school_cnname','school_branch','district_cnname','client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_mobile', 'client_createtime', 'from_channel_medianame', 'from_channel_name', 'to_channel_medianame', 'to_channel_name', 'staffer_cnname', 'channellog_note', 'channellog_status_name', 'channellog_createtime');
            $fieldname = $this->LgArraySwitch(array('校区名称', '校区编号', '所属区域','中文名', '英文名', '性别', '年龄', '手机号', '名单创建日期', '原渠道类型', '原渠道明细', '目标渠道类型', '目标渠道明细', '申请人', '更改原因', '审核状态', '申请时间'));
            $fieldcustom = array("1", "1", "1","1", "1", "1", "1", "1", '1', "1", '1', '1', '1', '1', '1', '1', '1');
            $fieldshow = array("1", "1", "1","1", "1", "1", "1", "1", '1', "1", '1', '1', '1', '1', '1', '1', '1');

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;
            $result['all_num'] = $allnums;

            if ($ChannellogList) {
                $result['list'] = $ChannellogList;
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $result['list'] = array();
                $res = array('error' => '1', 'errortip' => "暂无招生渠道变更审核列表", 'result' => $result);
            }

            return $res;
        }
    }

    //添加沟通渠道
    function addChannelAction($paramArray)
    {
        $data = array();
        $data['channel_name'] = $paramArray['channel_name'];
        $data['channel_linkman'] = $paramArray['channel_linkman'];
        $data['channel_tel'] = $paramArray['channel_tel'];
        if($paramArray['channel_tel']){
            $data['channel_amdpswd'] = substr($paramArray['channel_tel'], -6);
        }
        $data['channel_board'] = $paramArray['channel_board'];
        $data['channel_code'] = $paramArray['channel_code'];
        $data['channel_medianame'] = $paramArray['channel_medianame'];
        $data['company_id'] = $paramArray['company_id'];
        $data['channel_note'] = $paramArray['channel_note'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['channel_way'] = $paramArray['channel_way'];
        $data['channel_push'] = $paramArray['channel_push'];
        $data['channel_maxday'] = $paramArray['channel_maxday'];
        $data['channel_minday'] = $paramArray['channel_minday'];
        $data['channel_isschoolchose'] = $paramArray['channel_isschoolchose'];//20200518戚总要求添加
        $data['channel_isreferral'] = $paramArray['channel_isreferral'];
        $data['channel_isbazaar'] = $paramArray['channel_isbazaar'];
        $data['channel_quality'] = $paramArray['channel_quality'];
        $data['channel_istoschool'] = $paramArray['channel_istoschool'];//名单渠道重新激活规则  0 进入集团  1进入学校
        $data['channel_intention_level'] = $paramArray['channel_intention_level'];
        $data['channel_updatetime'] = time();
        $data['channel_createtime'] = time();

        $field = array();
        $field['channel_name'] = "渠道名称";
        $field['channel_linkman'] = "联系人";
        $field['channel_tel'] = "联系电话";
        $field['channel_board'] = "渠道板块";
        $field['channel_code'] = "渠道编号";
        $field['channel_medianame'] = "招生来源";
        $field['str_organize'] = "所属组织";
        $field['company_id'] = "所属公司";
        $field['channel_maxday'] = "渠道保护最大天数";
        $field['channel_minday'] = "渠道保护最小天数";
        $field['channel_quality'] = "0 默认毛名单 1 默认有效名单";
        $field['channel_createtime'] = "录入时间";


        if (isset($paramArray['channel_code']) && $paramArray['channel_code'] != "") {
            $channel_code = $this->DataControl->getFieldOne('crm_code_channel', 'channel_id', "channel_code = '{$paramArray['channel_code']}' and company_id = '{$paramArray['company_id']}'");
            if ($channel_code) {
                ajax_return(array('error' => 1, 'errortip' => "渠道编号已存在!"), $this->companyOne['company_language']);
            }
        }

        $channel_name = $this->DataControl->getFieldOne('crm_code_channel', 'channel_id', "channel_name = '{$paramArray['channel_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($channel_name) {
            ajax_return(array('error' => 1, 'errortip' => "渠道名称已存在!"), $this->companyOne['company_language']);
        }

        if ($cid = $this->DataControl->insertData('crm_code_channel', $data)) {
            $str_organize = stripslashes($paramArray['str_organize']);
            if ($str_organize) {
                $arr_organize = json_decode($str_organize, true);
                if ($arr_organize) {
                    foreach ($arr_organize as $value) {
                        $organize = array();
                        $organize['channel_id'] = $cid;
                        $organize['organize_id'] = $value['organize_id'];
                        $this->DataControl->insertData('crm_channel_organize', $organize);
                    }
                }
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加沟通渠道成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加沟通渠道失败', 'result' => $result);
        }
        return $res;
    }

    //招生渠道明细
    function updateChannelAction($paramArray)
    {
        $frommediaOne = $this->DataControl->selectOne(" select * from crm_code_frommedia WHERE frommedia_name = '{$paramArray['channel_medianame']}' and company_id = '{$paramArray['company_id']}' limit 0,1 ");

        $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id", "channel_id = '{$paramArray['channel_id']}'");

        if ($frommediaOne['frommedia_isschoolchose'] == '0' && $paramArray['channel_isschoolchose'] == '1') {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '渠道类型和渠道明细是否允许校区选择设置不一致', 'result' => $result);
            return $res;
        }

        if ($channelOne) {
            $data = array();
            $data['channel_name'] = $paramArray['channel_name'];
            $data['channel_linkman'] = $paramArray['channel_linkman'];
            $data['channel_tel'] = $paramArray['channel_tel'];
            if($paramArray['channel_tel']){
                $data['channel_amdpswd'] = substr($paramArray['channel_tel'], -6);
            }
            $data['channel_board'] = $paramArray['channel_board'];
            $data['channel_code'] = $paramArray['channel_code'];
            $data['channel_medianame'] = $paramArray['channel_medianame'];
            $data['channel_note'] = $paramArray['channel_note'];
            $data['channel_way'] = $paramArray['channel_way'];
            $data['channel_push'] = $paramArray['channel_push'];
            $data['channel_maxday'] = $paramArray['channel_maxday'];
            $data['channel_minday'] = $paramArray['channel_minday'];
            $data['channel_isreferral'] = $paramArray['channel_isreferral'];
            $data['channel_isbazaar'] = $paramArray['channel_isbazaar'];
            $data['channel_quality'] = $paramArray['channel_quality'];
            $data['channel_isschoolchose'] = $paramArray['channel_isschoolchose']; //$paramArray['channel_isschoolchose'];//20200518戚总要求添加
            $data['channel_istoschool'] = $paramArray['channel_istoschool'];//名单渠道重新激活规则  0 进入集团  1进入学校
            $data['channel_intention_level'] = $paramArray['channel_intention_level'];
            $data['channel_updatetime'] = time();

            $field = array();
            $field['channel_name'] = "渠道名称";
            $field['channel_linkman'] = "联系人";
            $field['channel_tel'] = "联系电话";
            $field['channel_board'] = "渠道板块";
            $field['channel_code'] = "渠道编号";
            $field['channel_medianame'] = "招生来源";
            $field['channel_quality'] = "0 默认毛名单 1 默认有效名单";

            $channel_code = $this->DataControl->getFieldOne('crm_code_channel', 'channel_code', "channel_id = '{$paramArray['channel_id']}'");
            if (isset($paramArray['channel_code']) && $paramArray['channel_code'] != "") {
                if ($paramArray['channel_code'] != $channel_code['channel_code']) {
                    $channel_code = $this->DataControl->getFieldOne('crm_code_channel', 'channel_id', "channel_code = '{$paramArray['channel_code']}' and company_id = '{$paramArray['company_id']}'");
                    if ($channel_code) {
                        ajax_return(array('error' => 1, 'errortip' => "渠道编号已存在!"), $this->companyOne['company_language']);
                    }
                }
            }

            $channel_name = $this->DataControl->getFieldOne('crm_code_channel', 'channel_name', "channel_id = '{$paramArray['channel_id']}'");
            if ($paramArray['channel_name'] != $channel_name['channel_name']) {
                $channel_name = $this->DataControl->getFieldOne('crm_code_channel', 'channel_id', "channel_name = '{$paramArray['channel_name']}' and company_id = '{$paramArray['company_id']}'");
                if ($channel_name) {
                    ajax_return(array('error' => 1, 'errortip' => "渠道名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_channel", "channel_id = '{$paramArray['channel_id']}'", $data)) {
                $this->DataControl->delData('crm_channel_organize', "channel_id='{$paramArray['channel_id']}'");
                $str_organize = stripslashes($paramArray['str_organize']);
                if ($str_organize) {
                    $arr_organize = json_decode($str_organize, true);
                    if ($arr_organize) {
                        foreach ($arr_organize as $value) {
                            $organize = array();
                            $organize['channel_id'] = $paramArray['channel_id'];
                            $organize['organize_id'] = $value['organize_id'];
                            $this->DataControl->insertData('crm_channel_organize', $organize);
                        }
                    }
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "来源渠道修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '来源渠道修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //有效变更/无效变更
    function updateChannelApplyAction($paramArray)
    {
        $channelOne = $this->DataControl->getFieldOne("crm_client_channellog", "channellog_id,client_id,channellog_changetime,channellog_sponsor,channellog_teachername,channellog_teacherid,channellog_branch", "channellog_id = '{$paramArray['channellog_id']}'");
        if ($channelOne) {
            $data = array();
            $data['channellog_status'] = $paramArray['channellog_status'];
            $data['channellog_reason'] = $paramArray['channellog_remark'];

            $data['astaffer_id'] = $paramArray['staffer_id'];

            $field = array();
            $field['channellog_status'] = "-1无效变更1有效变更";
            $field['channellog_remark'] = "备注";

            if ($this->DataControl->updateData("crm_client_channellog", "channellog_id = '{$paramArray['channellog_id']}'", $data)) {
                $trackData = array();
                $trackData['client_id'] = $channelOne['client_id'];
                $trackData['channellog_id'] = $channelOne['channellog_id'];
                if ($paramArray['channellog_status'] == '1') {
                    $trackData['tracks_title'] = '1';
                } else {
                    $trackData['tracks_title'] = '-1';
                }
                $trackData['staffer_id'] = $paramArray['staffer_id'];
                $trackData['tracks_time'] = time();
                $this->DataControl->insertData("crm_client_channel_tracks", $trackData);

                if ($paramArray['channellog_status'] == '1') {

                    //渠道
                    $channelto = $this->DataControl->selectOne("select channel_id,channel_medianame,channel_intention_level from crm_code_channel where  channel_id = '{$paramArray['to_channel_id']}'");

                    $datas = array();
                    $datas['channel_id'] = $paramArray['to_channel_id'];
                    $datas['client_source'] = $paramArray['to_channel_medianame'];
                    $datas['client_sponsor'] = $channelOne['channellog_sponsor'];
                    $datas['client_teachername'] = $channelOne['channellog_teachername'];
                    $datas['client_teacherid'] = $channelOne['channellog_teacherid'];
                    $datas['client_stubranch'] = $channelOne['channellog_branch'];
                    if($channelto['channel_intention_level'] > 0){
                        $datas['client_intention_level'] = $channelto['channel_intention_level'];
                    }
                    $datas['client_frompage'] = '';
                    $datas['client_updatetime'] = time();
                    $datas['client_createtime'] = strtotime($channelOne['channellog_changetime']);

                    $this->DataControl->updateData("crm_client", "client_id = '{$channelOne['client_id']}'", $datas);
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "审核成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '审核失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //有效变更/无效变更   ---- 批量处理有效无效操作
    function updateSomeChannelApplyAction($paramArray)
    {
        $channellog_ids = json_decode(stripslashes($paramArray['channellog_ids']), true);

        if (is_array($channellog_ids)) {
            foreach ($channellog_ids as $channellogvar) {
                $channelOne = $this->DataControl->getFieldOne("crm_client_channellog", "channellog_id,client_id,channellog_changetime", "channellog_id = '{$channellogvar['channellog_id']}'");
                if ($channelOne) {
                    $data = array();
                    $data['channellog_status'] = $paramArray['channellog_status'];
                    $data['channellog_reason'] = $paramArray['channellog_remark'];
                    $data['astaffer_id'] = $paramArray['staffer_id'];
                    if ($this->DataControl->updateData("crm_client_channellog", "channellog_id = '{$channellogvar['channellog_id']}'", $data)) {
                        $trackData = array();
                        $trackData['client_id'] = $channelOne['client_id'];
                        $trackData['channellog_id'] = $channelOne['channellog_id'];
                        if ($paramArray['channellog_status'] == '1') {
                            $trackData['tracks_title'] = '1';
                        } else {
                            $trackData['tracks_title'] = '-1';
                        }
                        $trackData['staffer_id'] = $paramArray['staffer_id'];
                        $trackData['tracks_time'] = time();
                        $this->DataControl->insertData("crm_client_channel_tracks", $trackData);

                        if ($paramArray['channellog_status'] == '1') {
                            $tochannel = $this->DataControl->selectOne("select l.to_channel_id,c.channel_medianame as to_channel_medianame from crm_client_channellog as l left join crm_code_channel as c ON l.to_channel_id = c.channel_id where l.client_id = '{$channelOne['client_id']}' limit 0,1 ");
                            $datas = array();
                            $datas['channel_id'] = $tochannel['to_channel_id'];
                            $datas['client_source'] = $tochannel['to_channel_medianame'];
                            $datas['client_frompage'] = '';
                            $datas['client_updatetime'] = time();
                            $datas['client_createtime'] = strtotime($channelOne['channellog_changetime']);

                            $this->DataControl->updateData("crm_client", "client_id = '{$channelOne['client_id']}'", $datas);
                        }
                    }
                }
            }
            $res = array('error' => '0', 'errortip' => "审核成功", 'result' => array());
            return $res;
        } else {
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => array());
            return $res;
        }
    }

    //删除来源渠道
    function delChannelAction($paramArray)
    {
        $ChannelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id", "channel_id = '{$paramArray['channel_id']}'");
        if ($ChannelOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("crm_client", "client_id", "channel_id = '{$paramArray['channel_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该来源渠道已被使用，不可删除"), $this->companyOne['company_language']);
            }
            $b = $this->DataControl->getFieldOne("crm_sell_activity", "activity_id", "channel_id = '{$paramArray['channel_id']}'");
            if ($b) {
                ajax_return(array('error' => 1, 'errortip' => "该来源渠道已被使用，不可删除"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_channel", "channel_id = '{$paramArray['channel_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除来源渠道成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除来源渠道失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //CRM用户角色列表
    function getPostroleList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.postrole_name like '%{$paramArray['keyword']}%' or p.postrole_level like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.postrole_id,
                p.postrole_name,
                p.postrole_level,
                p.postrole_remk
            FROM
                crm_code_postrole AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.postrole_id DESC    
            LIMIT {$pagestart},{$num}";

        $PostroleList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.postrole_id)
            FROM
                crm_code_postrole AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('postrole_name ', 'postrole_level', 'postrole_remk');
        $fieldname = $this->LgArraySwitch(array('角色名称', '角色等级', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PostroleList) {
            $result['list'] = $PostroleList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加CRM用户角色
    function addPostroleAction($paramArray)
    {
        $data = array();
        $data['postrole_name'] = $paramArray['postrole_name'];
        $data['postrole_level'] = $paramArray['postrole_level'];
        $data['postrole_remk'] = $paramArray['postrole_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['postrole_name'] = "角色名称";
        $field['postrole_level'] = "角色等级";
        $field['postrole_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $postrole_level = $this->DataControl->getFieldOne('crm_code_postrole', 'postrole_id', "postrole_level = '{$paramArray['postrole_level']}'");
        if ($postrole_level) {
            ajax_return(array('error' => 1, 'errortip' => "角色等级已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('crm_code_postrole', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加CRM用户角色成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加CRM用户角色失败', 'result' => $result);
        }
        return $res;
    }

    //编辑CRM用户角色
    function updatePostroleAction($paramArray)
    {
        $channelOne = $this->DataControl->getFieldOne("crm_code_postrole", "postrole_id", "postrole_id = '{$paramArray['postrole_id']}'");
        if ($channelOne) {
            $data = array();
            $data['postrole_name'] = $paramArray['postrole_name'];
            $data['postrole_level'] = $paramArray['postrole_level'];
            $data['postrole_remk'] = $paramArray['postrole_remk'];

            $field = array();
            $field['postrole_name'] = "角色名称";
            $field['postrole_level'] = "角色等级";
            $field['postrole_remk'] = "备注";

            $postrole_level = $this->DataControl->getFieldOne('crm_code_postrole', 'postrole_level', "postrole_id = '{$paramArray['postrole_id']}'");
            if ($paramArray['postrole_level'] != $postrole_level['postrole_level']) {
                $postrole_level = $this->DataControl->getFieldOne('crm_code_postrole', 'postrole_id', "postrole_level = '{$paramArray['postrole_level']}'");
                if ($postrole_level) {
                    ajax_return(array('error' => 1, 'errortip' => "角色等级已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_postrole", "postrole_id = '{$paramArray['postrole_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "CRM用户角色修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => 'CRM用户角色修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //渠道变更记录
    function getChannelTrack($paramArray)
    {
        $sql = "
            SELECT
                t.tracks_title,
                cc.channel_name AS to_channel_name,
                cc.channel_medianame AS to_frommedia_name,
                cn.channel_name AS from_channel_name,
                cn.channel_medianame AS from_frommedia_name,
                s.staffer_cnname,
                a.staffer_cnname AS astaffer_cnname,
                c.channellog_note,
                c.channellog_changetime,
                c.channellog_client_frompage,
                c.channellog_img,
                c.channellog_reason,
                from_unixtime( t.tracks_time, '%Y-%m-%d %H:%i' ) AS createtime 
            FROM
                crm_client_channel_tracks AS t
                LEFT JOIN crm_client_channellog AS c ON t.channellog_id = c.channellog_id
                LEFT JOIN crm_code_channel AS cc ON cc.channel_id = c.to_channel_id
                LEFT JOIN crm_code_channel AS cn ON cn.channel_id = c.from_channel_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = c.staffer_id
                LEFT JOIN smc_staffer AS a ON a.staffer_id = c.astaffer_id
            WHERE
                t.client_id = '{$paramArray['client_id']}'
                group by t.tracks_id";
        $track = $this->DataControl->selectClear($sql);
        if ($track) {
            foreach ($track as &$trackvar) {
                if($trackvar['channellog_img']){
                    $trackvar['channellog_imgjson'] = explode(',', $trackvar['channellog_img']);//json_encode($channellog_img)
                }else{
                    $trackvar['channellog_imgjson'] = $trackvar['channellog_img'];

                }
            }
        }

        $num = $this->DataControl->selectOne("select count(*) as num from crm_client_channellog where client_id = '{$paramArray['client_id']}'");


        $field = array();
        $field["tracks_title"] = $this->LgStringSwitch("标题");

        $result = array();
        if ($track) {
            $result["field"] = $field;
            $result["data"] = $track;
            $result["num"] = $num['num'];
            $res = array('error' => '0', 'errortip' => '获取渠道变更记录成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取渠道变更记录失败', 'result' => $result);
        }
        return $res;
    }

    //删除CRM用户角色
    function delPostroleAction($paramArray)
    {
        $PostroleOne = $this->DataControl->getFieldOne("crm_code_postrole", "postrole_id", "postrole_id = '{$paramArray['postrole_id']}'");
        if ($PostroleOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_postrole", "postrole_id = '{$paramArray['postrole_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除CRM用户角色成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除CRM用户角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //无效内容模板列表
    function getInvalidnoteList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.invalidnote_code like '%{$paramArray['keyword']}%' or p.invalidnote_reason like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['invalidnote_isopen']) && $paramArray['invalidnote_isopen'] !== "") {
            $datawhere .= " and p.invalidnote_isopen ='{$paramArray['invalidnote_isopen']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT p.invalidnote_id, p.invalidnote_code, p.invalidnote_reason,p.invalidnote_isopen 
            FROM crm_code_invalidnote AS p
            WHERE {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY p.invalidnote_id DESC    
            LIMIT {$pagestart},{$num}";

        $PostroleList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT  COUNT(p.invalidnote_id)
            FROM  crm_code_invalidnote AS p
            WHERE  {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('invalidnote_code','invalidnote_reason','invalidnote_isopen');
        $fieldname = $this->LgArraySwitch(array( '无效编号','无效原因','是否启用'));
        $fieldcustom = array("1","1","1");
        $fieldshow = array("1","1","1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PostroleList) {
            $result['list'] = $PostroleList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无模板", 'result' => $result);
        }

        return $res;
    }

    //添加无效原因
    function addInvalidnoteAction($paramArray)
    {
        $data = array();
        $data['invalidnote_code'] = $paramArray['invalidnote_code'];
        $data['invalidnote_reason'] = $paramArray['invalidnote_reason'];
        $data['invalidnote_isopen'] = $paramArray['invalidnote_isopen'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['invalidnote_code'] = "无效b编号";
        $field['invalidnote_reason'] = "无效原因";
        $field['invalidnote_isopen'] = "是否启用  0 否 1 启用";
        $field['company_id'] = "所属公司";

        $tracenote_code = $this->DataControl->getFieldOne('crm_code_invalidnote', 'invalidnote_id', "invalidnote_code = '{$paramArray['invalidnote_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($tracenote_code) {
            ajax_return(array('error' => 1, 'errortip' => "无效编号已存在!"), $this->companyOne['company_language']);
        }
        if ($this->DataControl->insertData('crm_code_invalidnote', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加用户跟踪无效原因", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->招生相关设置", '添加无效原因', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加用户跟踪内容模板失败', 'result' => $result);
        }
        return $res;
    }

    //编辑用户跟踪内容模板无效原因
    function updateInvalidnoteAction($paramArray)
    {
        $TracenoteOne = $this->DataControl->getFieldOne("crm_code_invalidnote", "invalidnote_id,invalidnote_code", "invalidnote_id = '{$paramArray['invalidnote_id']}'");

        $haveOne = $this->DataControl->selectOne("select 1 from crm_client where company_id = '{$paramArray['company_id']}' and invalidnote_code = '{$TracenoteOne['invalidnote_code']}' limit 0,1 ");
        if ($haveOne) {
            ajax_return(array('error' => 1, 'errortip' => "无效原因已使用不能修改"), $this->companyOne['company_language']);
        }
        if ($TracenoteOne) {

            $TracenoteTwo = $this->DataControl->selectOne(" select 1 from crm_code_invalidnote where invalidnote_code = '{$paramArray['invalidnote_code']}' and invalidnote_id <> '{$paramArray['invalidnote_id']}' and company_id = '{$paramArray['company_id']}' ");
            if($TracenoteTwo){
                ajax_return(array('error' => 1, 'errortip' => "编号已存在,不能修改"), $this->companyOne['company_language']);
            }

            $data = array();
            $data['invalidnote_code'] = $paramArray['invalidnote_code'];
            $data['invalidnote_reason'] = $paramArray['invalidnote_reason'];
            $data['invalidnote_isopen'] = $paramArray['invalidnote_isopen'];

            $field = array();
            $field['invalidnote_code'] = "无效编号";
            $field['invalidnote_reason'] = "无效原因";
            $field['invalidnote_isopen'] = "是否开启";

            if ($this->DataControl->updateData("crm_code_invalidnote", "invalidnote_id = '{$paramArray['invalidnote_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "无效原因修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->招生相关设置", '编辑无效原因', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '无效原因修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //无效原因开启关闭
    function isInvalidnoteOpenAction($paramArray)
    {
        $msql = "select invalidnote_id
                from crm_code_invalidnote
                where invalidnote_id = '{$paramArray['invalidnote_id']}' and company_id = '{$paramArray['company_id']}' ";
        $merchantOne = $this->DataControl->selectOne($msql);
        if ($merchantOne) {
            if($paramArray['invalidnote_isopen'] == '1'){
                $tip = '开启';
            }else{
                $tip = '关闭';
            }

            $data = array();
            $data['invalidnote_isopen'] = $paramArray['invalidnote_isopen'];
            if($this->DataControl->updateData("crm_code_invalidnote", "invalidnote_id = '{$paramArray['invalidnote_id']}' and company_id = '{$paramArray['company_id']}' ", $data)){
                $this->error = 0;
                $this->errortip = "{$tip}成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "{$tip}失败";
                return true;
            }
        } else {
            $this->error = 1;
            $this->errortip = "未找到无效原因";
            return true;
        }
    }

    //删除用户跟踪内容模板
    function delInvalidnoteAction($paramArray)
    {
        $TracenoteOne = $this->DataControl->getFieldOne("crm_code_invalidnote", "invalidnote_id,invalidnote_code", "invalidnote_id = '{$paramArray['invalidnote_id']}'");

        $haveOne = $this->DataControl->selectOne("select 1 from crm_client where company_id = '{$paramArray['company_id']}' and invalidnote_code = '{$TracenoteOne['invalidnote_code']}' limit 0,1 ");
        if($haveOne){
            ajax_return(array('error' => 1, 'errortip' => "已使用不能删除"), $this->companyOne['company_language']);
        }
        if ($TracenoteOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_invalidnote", "invalidnote_id = '{$paramArray['invalidnote_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除用户跟踪无效原因成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->招生相关设置", '删除无效原因', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除用户跟踪无效原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }



    //用户跟踪内容模板列表
    function getTracenoteList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.tracenote_code like '%{$paramArray['keyword']}%' or p.tracenote_remk like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.tracenote_id,
                p.tracenote_code,
                p.tracenote_remk
            FROM
                crm_code_tracenote AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.tracenote_id DESC    
            LIMIT {$pagestart},{$num}";

        $PostroleList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.tracenote_id)
            FROM
                crm_code_tracenote AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('tracenote_code ', 'tracenote_remk');
        $fieldname = $this->LgArraySwitch(array('模板编号', '模板内容'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PostroleList) {
            $result['list'] = $PostroleList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无沟通模板", 'result' => $result);
        }

        return $res;
    }

    //添加用户跟踪内容模板
    function addTracenoteAction($paramArray)
    {
        $data = array();
        $data['tracenote_code'] = $paramArray['tracenote_code'];
        $data['tracenote_remk'] = $paramArray['tracenote_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['tracenote_code'] = "模板编号";
        $field['tracenote_remk'] = "模板内容";
        $field['company_id'] = "所属公司";

        $tracenote_code = $this->DataControl->getFieldOne('crm_code_tracenote', 'tracenote_id', "tracenote_code = '{$paramArray['tracenote_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($tracenote_code) {
            ajax_return(array('error' => 1, 'errortip' => "模板编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('crm_code_tracenote', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加用户跟踪内容模板成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->招生相关设置", '添加沟通模板', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加用户跟踪内容模板失败', 'result' => $result);
        }
        return $res;
    }

    //编辑用户跟踪内容模板
    function updateTracenoteAction($paramArray)
    {
        $TracenoteOne = $this->DataControl->getFieldOne("crm_code_tracenote", "tracenote_id", "tracenote_id = '{$paramArray['tracenote_id']}'");
        if ($TracenoteOne) {
            $data = array();
            $data['tracenote_code'] = $paramArray['tracenote_code'];
            $data['tracenote_remk'] = $paramArray['tracenote_remk'];

            $field = array();
            $field['tracenote_code'] = "模板编号";
            $field['tracenote_remk'] = "模板内容";

            $tracenote_code = $this->DataControl->getFieldOne('crm_code_tracenote', 'tracenote_code', "tracenote_id = '{$paramArray['tracenote_id']}'");
            if ($paramArray['tracenote_code'] != $tracenote_code['tracenote_code']) {
                $tracenote_code = $this->DataControl->getFieldOne('crm_code_tracenote', 'tracenote_id', "tracenote_code = '{$paramArray['tracenote_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($tracenote_code) {
                    ajax_return(array('error' => 1, 'errortip' => "模板编号已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_tracenote", "tracenote_id = '{$paramArray['tracenote_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "用户跟踪内容模板修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->招生相关设置", '编辑沟通模板', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '用户跟踪内容模板修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除用户跟踪内容模板
    function delTracenoteAction($paramArray)
    {
        $TracenoteOne = $this->DataControl->getFieldOne("crm_code_tracenote", "tracenote_id", "tracenote_id = '{$paramArray['tracenote_id']}'");
        if ($TracenoteOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_tracenote", "tracenote_id = '{$paramArray['tracenote_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除用户跟踪内容模板成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->招生相关设置", '删除沟通模板', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除用户跟踪内容模板失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    // 查看渠道详情crm_code_channel
    function getChannelApi($request)
    {

        $channel = $this->DataControl->selectOne("select ch.*,
        (select group_concat(co.organize_cnname) from crm_channel_organize as lo 
          left join gmc_company_organize as co ON lo.organize_id=co.organize_id
          where   lo.channel_id =ch.channel_id) as  organize_cnname
        from  crm_code_channel as ch where ch.channel_id='{$request['channel_id']}' ");

        $organizeList = $this->DataControl->selectClear("select organize_id from crm_channel_organize where channel_id= '{$request['channel_id']}'");
        if ($organizeList) {
            $channel['organize_id'] = $organizeList;
        } else {
            $channel['organize_id'] = array();
        }

        return $channel;
    }


    function isChanneLUseAction($request)
    {
        $channelOne = $this->DataControl->getFieldOne('crm_code_channel', 'channel_isuse', "channel_id='{$request['channel_id']}'");
        $data = array();
        $data['channel_isuse'] = 1 - $channelOne['channel_isuse'];
        $this->DataControl->updateData("crm_code_channel", "channel_id='{$request['channel_id']}'", $data);
        return true;

    }

    function toEffectiveAction($request)
    {
        $TmkBatchId = '';
        if(!$this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$request['client_tmkbatch']}' and company_id='{$request['company_id']}' limit 0,1")) {
            $TmkBatchId = $request['client_tmkbatch'];
            $this->DataControl->insertData('crm_client_tmkbatch',  ["company_id"=>"{$request['company_id']}","tmkbatch_number"=>"{$request['client_tmkbatch']}"]);
        }

        $data = array();
        $data['client_tracestatus'] = 0;
        $data['client_distributionstatus'] = 0;
        $data['client_tmkbatch'] = $TmkBatchId;//增加批次编号
        $bool = $this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}'", $data);

        //解除入校记录
        $schoolenter = array();
        $schoolenter['is_enterstatus'] = '-1';
        $schoolenter['schoolenter_updatetime'] = time();
        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$request['client_id']}' and company_id='{$request['company_id']}'", $schoolenter);
        //$this->DataControl->delData("crm_client_schoolenter", "client_id='{$request['client_id']}' and company_id='{$request['company_id']}'");

        return $bool;
    }

    function delNoEffectiveAction($request)
    {

        $bool = $this->DataControl->delData("crm_client", "client_id='{$request['client_id']}' and company_id='{$request['company_id']}' and client_tracestatus='-2'");

        return $bool;
    }

    //未到访原因列表
    function isvisitreasonList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.reason_note like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.reason_id,
                p.reason_note
            FROM
                crm_code_isvisitreason AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.reason_id DESC    
            LIMIT {$pagestart},{$num}";

        $CommodeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.reason_id)
            FROM
                crm_code_isvisitreason AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('reason_note');
        $fieldname = array('取消原因');
        $fieldcustom = array("1");
        $fieldshow = array("1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CommodeList) {
            $result['list'] = $CommodeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无取消原因", 'result' => $result);
        }

        return $res;
    }

    //添加未到访原因
    function addIsvisitreasonAction($paramArray)
    {
        $data = array();
        $data['reason_note'] = $paramArray['reason_note'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['reason_note'] = "未到访原因";
        $field['company_id'] = "所属公司";

        $commode_name = $this->DataControl->getFieldOne('crm_code_isvisitreason', 'reason_id', "reason_note = '{$paramArray['reason_note']}' and company_id = '{$paramArray['company_id']}'");
        if ($commode_name) {
            ajax_return(array('error' => 1, 'errortip' => "未到访原因已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('crm_code_isvisitreason', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加未到访原因成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加未到访原因失败', 'result' => $result);
        }
        return $res;
    }

    //编辑未到访原因
    function updateIsvisitreasonAction($paramArray)
    {
        $TeachingtypeOne = $this->DataControl->getFieldOne("crm_code_isvisitreason", "reason_id", "reason_id = '{$paramArray['reason_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['reason_note'] = $paramArray['reason_note'];

            $field = array();
            $field['reason_note'] = "未到访原因";

            $commode_name = $this->DataControl->getFieldOne('crm_code_isvisitreason', 'reason_note', "reason_id = '{$paramArray['reason_id']}'");
            if ($paramArray['reason_note'] != $commode_name['reason_note']) {
                $commode_name = $this->DataControl->getFieldOne('crm_code_isvisitreason', 'reason_id', "reason_note = '{$paramArray['reason_note']}'  and company_id = '{$paramArray['company_id']}'");
                if ($commode_name) {
                    ajax_return(array('error' => 1, 'errortip' => "未到访原因已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_isvisitreason", "reason_id = '{$paramArray['reason_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "未到访原因修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '未到访原因修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除未到访原因
    function delIsvisitreasonAction($paramArray)
    {
        $CommodeOne = $this->DataControl->getFieldOne("crm_code_isvisitreason", "reason_note", "reason_id = '{$paramArray['reason_id']}'");
        if ($CommodeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_isvisitreason", "reason_id = '{$paramArray['reason_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除未到访原因成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除未到访原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //附近学校管理列表
    function nearschoolList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (n.nearschool_name like '%{$paramArray['keyword']}%' or n.nearschool_address like '%{$paramArray['keyword']}%' or n.nearschool_shortname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and n.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['nearschool_status']) && $paramArray['nearschool_status'] !== "") {
            $datawhere .= " and n.nearschool_status ='{$paramArray['nearschool_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                n.nearschool_id,
                n.school_id,
                n.nearschool_name,
                n.nearschool_address,
                n.nearschool_shortname,
                n.nearschool_status,
                s.school_cnname
            FROM
                crm_code_nearschool AS n left join smc_school as s on n.school_id = s.school_id
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}'
            ORDER BY
                n.nearschool_id DESC    
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        if ($postList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($postList as &$val) {
                $val['nearschool_status'] = $status[$val['nearschool_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(n.nearschool_id)
            FROM
                crm_code_nearschool AS n
            WHERE
                {$datawhere} AND n.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('school_cnname ', 'nearschool_name', 'nearschool_shortname', 'nearschool_address', 'nearschool_status');
        $fieldname = $this->LgArraySwitch(array('校区名称', '附近校区名称', '简称', '学校地址', '开启状态'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 0, 1);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无附近学校信息", 'result' => $result);
        }

        return $res;
    }

    //启用/不启用
    function nearschoolStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_code_nearschool", "nearschool_id", "nearschool_id = '{$paramArray['nearschool_id']}'");

        if ($activityOne) {
            $data = array();
            $data['nearschool_status'] = $paramArray['nearschool_status'];

            $field = array();
            $field['nearschool_status'] = '是否启用';

            if ($this->DataControl->updateData("crm_code_nearschool", "nearschool_id = '{$paramArray['nearschool_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除附近学校
    function delnearschoolAction($paramArray)
    {
        $CommodeOne = $this->DataControl->getFieldOne("crm_code_nearschool", "nearschool_id", "nearschool_id = '{$paramArray['nearschool_id']}'");
        if ($CommodeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("crm_client", "client_id", "nearschool_id = '{$paramArray['nearschool_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该学校已被使用！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("crm_code_nearschool", "nearschool_id = '{$paramArray['nearschool_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除附近学校成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->招生相关设置", '删除附近学校', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除附近学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //添加附近学校
    function addnearschoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['near']), true);
        foreach ($schoolList as $item) {
            $data['nearschool_name'] = $item['nearschool_name'];
            $data['nearschool_shortname'] = $item['nearschool_shortname'];
            $data['nearschool_address'] = $item['nearschool_address'];
            $data['school_id'] = $paramArray['school_id'];
            $data['company_id'] = $paramArray['company_id'];
            $data['nearschool_type'] = '0';

            $a = $this->DataControl->getFieldOne('crm_code_nearschool', 'nearschool_id', "nearschool_name = '{$item['nearschool_name']}' and school_id = '{$paramArray['school_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
            }
            $this->DataControl->insertData('crm_code_nearschool', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加附近学校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->招生相关设置", '添加附近学校', dataEncode($paramArray));

        return $res;
    }

    //编辑附近学校
    function updatenearschoolAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("crm_code_nearschool", "nearschool_id", "nearschool_id = '{$paramArray['nearschool_id']}'");
        if ($postOne) {
            $data = array();
            $data['nearschool_name'] = $paramArray['nearschool_name'];
            $data['nearschool_shortname'] = $paramArray['nearschool_shortname'];
            $data['nearschool_address'] = $paramArray['nearschool_address'];
            $data['school_id'] = $paramArray['school_id'];

            $field = array();
            $field['nearschool_name'] = "校区名称";
            $field['nearschool_shortname'] = "学校简称";
            $field['nearschool_address'] = "地址";

            $frommedia_name = $this->DataControl->getFieldOne('crm_code_nearschool', 'nearschool_name', "nearschool_id = '{$paramArray['nearschool_id']}'");
            if ($paramArray['nearschool_name'] != $frommedia_name['nearschool_name']) {
                $frommedia_name = $this->DataControl->getFieldOne('crm_code_nearschool', 'nearschool_id', "nearschool_name = '{$paramArray['nearschool_name']}' and school_id = '{$paramArray['school_id']}'");
                if ($frommedia_name) {
                    ajax_return(array('error' => 1, 'errortip' => "附近校区名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("crm_code_nearschool", "nearschool_id = '{$paramArray['nearschool_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑附近学校成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->招生相关设置", '编辑附近学校', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑附近学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //渠道变更记录
    function getChannelChange($paramArray)
    {
        $sql = "
            SELECT
                c.channel_name AS from_channel_name,
                c.channel_medianame AS from_channel_medianame,
                ch.channel_name AS to_channel_name,
                ch.channel_medianame AS to_channel_medianame,
                FROM_UNIXTIME( cc.channellog_createtime, '%Y-%m-%d %H:%i' ) AS channellog_createtime,
                s.staffer_cnname as astaffer_cnname,
                st.staffer_cnname,
                cc.channellog_remark
            FROM
                crm_client_channellog AS cc
                LEFT JOIN crm_code_channel AS c ON cc.from_channel_id = c.channel_id
                LEFT JOIN crm_code_channel AS ch ON cc.to_channel_id = ch.channel_id
                left join smc_staffer as s on s.staffer_id = cc.astaffer_id
                left join smc_staffer as st on st.staffer_id = cc.staffer_id";
        $stafferDetail = $this->DataControl->selectClear($sql);

        if ($stafferDetail) {
            $status = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购", "3" => "调拨", "4" => "借调"));
            foreach ($stafferDetail as &$val) {
                $val['proorder_from'] = $status[$val['proorder_from']];
            }

        }


        $field = array();
        $field["proorder_pid"] = $this->LgStringSwitch("采购编号");
        $field["proorder_from"] = $this->LgStringSwitch("采购类型");
        $field["proogoods_buynums"] = $this->LgStringSwitch("申请采购货品数量");
        $field["proogoods_payprice"] = $this->LgStringSwitch("申请采购金额");
        $field["proorder_createtime"] = $this->LgStringSwitch("采购日期");
        $field["proorder_orderbuydate"] = $this->LgStringSwitch("预估采购申请日期");
        $field["from_school_cnname"] = $this->LgStringSwitch("借调来源学校");
        $field["to_school_cnname"] = $this->LgStringSwitch("借调去向学校");
        $field["activitybuy_name"] = $this->LgStringSwitch("活动名称");
        $field["staffer_cnname"] = $this->LgStringSwitch("采购人");

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取采购货品明细成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取采购货品明细失败', 'result' => $result);
        }
        return $res;
    }

    /**
     * 获取其他参数
     * author: ling
     * 对应接口文档 0001
     */
    function getCrmOtherSettingApi()
    {
        // 获取CRM -是否允许修改手机号
        $data = array();
        $key = 0;
        $dataOne = $this->DataControl->getFieldOne('gmc_company', "company_iscrmmoblie,company_ispositive,company_isassist,company_istrackcoursetype,company_ismusttrackcoursetype,company_isnointention", "company_id='{$this->company_id}'");
        $data[$key]['title'] = '是否可编辑CRM手机号';
        $data[$key]['field'] = 'company_iscrmmoblie';
        $data[$key]['value'] = $dataOne['company_iscrmmoblie'];
        $key++;
        $data[$key]['title'] = '是否可在crm中操作转正';
        $data[$key]['field'] = 'company_ispositive';
        $data[$key]['value'] = $dataOne['company_ispositive'];
        $key++;
        $data[$key]['title'] = '是否有协助负责人跟踪';
        $data[$key]['field'] = 'company_isassist';
        $data[$key]['value'] = $dataOne['company_isassist'];
        $key++;
        $data[$key]['title'] = '是否选择跟进班组';
        $data[$key]['field'] = 'company_istrackcoursetype';
        $data[$key]['value'] = $dataOne['company_istrackcoursetype'];
        $key++;
        $data[$key]['title'] = '跟进班组是否必填';
        $data[$key]['field'] = 'company_ismusttrackcoursetype';
        $data[$key]['value'] = $dataOne['company_ismusttrackcoursetype'];
        $key++;
        $data[$key]['title'] = '跟进类型是否开启无意向跟进';
        $data[$key]['field'] = 'company_isnointention';
        $data[$key]['value'] = $dataOne['company_isnointention'];
        return $data;
    }

    function editCrmOtherSettingAction($paramArray)
    {
        $data = array();
        if (isset($paramArray['company_iscrmmoblie']) && $paramArray['company_iscrmmoblie'] !== '') {
            $data['company_iscrmmoblie'] = $paramArray['company_iscrmmoblie'];
        }
        if (isset($paramArray['company_ispositive']) && $paramArray['company_ispositive'] !== '') {
            $data['company_ispositive'] = $paramArray['company_ispositive'];
        }
        if (isset($paramArray['company_isassist']) && $paramArray['company_isassist'] !== '') {
            $data['company_isassist'] = $paramArray['company_isassist'];
        }
        if (isset($paramArray['company_istrackcoursetype']) && $paramArray['company_istrackcoursetype'] !== '') {
            $data['company_istrackcoursetype'] = $paramArray['company_istrackcoursetype'];
        }
        if($paramArray['company_istrackcoursetype'] == '0'){
            $data['company_ismusttrackcoursetype'] = 0;
        }else{
            if (isset($paramArray['company_ismusttrackcoursetype']) && $paramArray['company_ismusttrackcoursetype'] !== '') {
                $data['company_ismusttrackcoursetype'] = $paramArray['company_ismusttrackcoursetype'];
            }
        }
        if (isset($paramArray['company_isnointention']) && $paramArray['company_isnointention'] !== '') {
            $data['company_isnointention'] = $paramArray['company_isnointention'];
        }
        $this->DataControl->updateData("gmc_company", "company_id='{$this->company_id}'", $data);

        $this->error = 0;
        $this->errortip = '修改成功';
        return true;
    }


    //客户标签列表
    function getLabelTypeList($paramArray)
    {
        $datawhere = " l.company_id = '{$paramArray['company_id']}' ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (l.labeltype_name like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['labeltype_type']) && $paramArray['labeltype_type'] != ''  && $paramArray['labeltype_type'] != '[]'){
            $tagarray = json_decode(stripslashes($paramArray['labeltype_type']),true);
            if(is_array($tagarray)) {
                $datawhere .= " and ( ";
                $alltay = count($tagarray);
                foreach ($tagarray as $key=>$tagarrayvar){
                    if($alltay == ($key+1)){
                        $datawhere .= " l.labeltype_type like '%{$tagarrayvar}%' ";
                    }else {
                        $datawhere .= " l.labeltype_type like '%{$tagarrayvar}%' or  ";
                    }
                }
                $datawhere .= " ) ";
            }else{
                $datawhere .= " and (l.labeltype_type like '%{$paramArray['labeltype_type']}%')";
            }
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                l.labeltype_id,
                l.labeltype_name,
                l.labeltype_type,
                (SELECT COUNT(cl.label_id) FROM crm_client_label AS cl WHERE cl.labeltype_id = l.labeltype_id) as label_num  
            FROM
                crm_client_labeltype AS l
            WHERE
                {$datawhere}
            ORDER BY
                l.labeltype_id DESC
            LIMIT {$pagestart},{$num}";
        $LabelTypeList = $this->DataControl->selectClear($sql);
        if ($LabelTypeList) {
            foreach ($LabelTypeList as &$val) {
                $label = $this->DataControl->selectClear("SELECT label_id,label_name FROM crm_client_label WHERE labeltype_id = '{$val['labeltype_id']}'");
                if ($label) {
                    $success = '0';
                    $fail = '0';
                    foreach ($label as &$item) {
                        $tags = $this->DataControl->getFieldOne("crm_client", "client_id", "client_tag like '%{$item['label_name']}%'");
                        $tagstu = $this->DataControl->getFieldOne("smc_student", "student_id", "student_crmtag like '%{$item['label_name']}%'");
                        if ($tags || $tagstu) {
                            $item['is_use'] = true;
                            $success++;
                        } else {
                            $item['is_use'] = false;
                            $fail++;
                        }
                    }
                    $val['label'] = $label;
                } else {
                    $val['label'] = array();
                }

                $labeltype_typename = str_replace("0","新学员",$val['labeltype_type']);
                $labeltype_typename = str_replace("1","在籍学员",$labeltype_typename);
                $labeltype_typename = str_replace("2","流失学员",$labeltype_typename);
                $labeltype_typename = str_replace(",","/",$labeltype_typename);
                $val['labeltype_typename'] = $labeltype_typename;

                if ($val['labeltype_type'] != '') {
                    $val['labeltype_type'] = explode(',', $val['labeltype_type']);
                } else {
                    $val['labeltype_type'] = array();
                }
                if($success > 0){
                    $val['is_use'] = true;
                }else{
                    $val['is_use'] = false;
                }

            }
        }

        $all_num = $this->DataControl->selectOne("SELECT
                                                       COUNT(l.labeltype_id) AS num
                                                    FROM
                                                        crm_client_labeltype AS l
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('labeltype_typename','labeltype_name', 'label_num');
        $fieldname = $this->LgArraySwitch(array('标签对象','标签类型名称', '标签类型明细'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($fieldstring[$i] == 'label_num') {
                $field[$i]["ismethod"] = 1;
            }
        }

        $result = array();
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($LabelTypeList) {
            $result['list'] = $LabelTypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无客户标签", 'result' => $result);
        }

        return $res;
    }

    //检查子项是否重复
    function checkRepeatLabelApi($paramArray)
    {
        if($paramArray['label_name'] == ''){
            $res = array('error' => '0', 'errortip' => "标签类型明细不能为空", 'result' => array());
            return $res;
        }
        $labelOne = $this->DataControl->selectOne("select label_id from crm_client_label where company_id = '{$paramArray['company_id']}' and  label_name = '{$paramArray['label_name']}' and label_id <> '{$paramArray['label_id']}' limit 0,1 ");
        if(is_array($labelOne) && $labelOne['label_id']>0){
            $res = array('error' => '1', 'errortip' => "标签类型明细重复", 'result' => array());
        }else{
            $res = array('error' => '0', 'errortip' => "标签类型明细不重复", 'result' => array());
        }
        return $res;
    }

    //标签明细列表
    function labelDetail($paramArray)
    {
        $sql = "
            SELECT
                label_id,label_name
            FROM
                crm_client_label
            WHERE
                labeltype_id = '{$paramArray['labeltype_id']}'
            ORDER BY
                label_id DESC";
        $LabelList = $this->DataControl->selectClear($sql);

        $fieldstring = array('label_name');
        $fieldname = $this->LgArraySwitch(array('标签名称'));
        $fieldcustom = array("1");
        $fieldshow = array("1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;

        if ($LabelList) {
            $result['list'] = $LabelList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无标签名称", 'result' => $result);
        }

        return $res;
    }

    //添加客户标签
    function addLabelTypeAction($paramArray)
    {
        $labeltype = $this->DataControl->getFieldOne('crm_client_labeltype', 'labeltype_id', "company_id = '{$paramArray['company_id']}' and labeltype_name = '{$paramArray['labeltype_name']}'");
        if ($labeltype) {
            ajax_return(array('error' => 1, 'errortip' => "标签类型名称已存在!"), $this->companyOne['company_language']);
        }


        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['labeltype_name'] = $paramArray['labeltype_name'];
        $data['labeltype_createtime'] = time();

        $tagarray = json_decode(stripslashes($paramArray['labeltype_typelist']),true);
        if(is_array($tagarray)) {
            $labeltype_typestr = implode(',', $tagarray);
            $data['labeltype_type'] = $labeltype_typestr;
        }else{
            $labeltype_typestr = $paramArray['labeltype_typelist'];
            $data['labeltype_type'] = $labeltype_typestr;
        }

        $field = array();
        $field['company_id'] = "所属公司";
        $field['labeltype_name'] = "标签类型名称";
        $field['labeltype_type'] = "0 crm名单  1 在读学员  2 流失学员";

        $dataid = $this->DataControl->insertData('crm_client_labeltype', $data);
        if ($dataid) {
            $label_list = json_decode(stripslashes($paramArray['label_list']), true);
            if ($label_list) {
                $list = array();
                foreach ($label_list as $val) {
                    $list['company_id'] = $paramArray['company_id'];
                    $list['labeltype_id'] = $dataid;
                    $list['label_name'] = $val['label_name'];
                    $this->DataControl->insertData("crm_client_label", $list);
                }
            }
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加客户标签成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加客户标签失败', 'result' => $result);
        }
        return $res;
    }

    //编辑客户标签
    function updateLabelTypeAction($paramArray)
    {
        $LabelTypeOne = $this->DataControl->getFieldOne("crm_client_labeltype", "labeltype_id", "labeltype_id = '{$paramArray['labeltype_id']}'");
        if ($LabelTypeOne) {
            $labeltype = $this->DataControl->getFieldOne('crm_client_labeltype', 'labeltype_id', "labeltype_id <> '{$paramArray['labeltype_id']}' and company_id = '{$paramArray['company_id']}' and labeltype_name = '{$paramArray['labeltype_name']}'");
            if ($labeltype) {
                ajax_return(array('error' => 1, 'errortip' => "标签类型名称已存在!"), $this->companyOne['company_language']);
            }

            $data = array();
            $data['labeltype_name'] = $paramArray['labeltype_name'];

            $tagarray = json_decode(stripslashes($paramArray['labeltype_typelist']),true);
            if(is_array($tagarray)) {
                $labeltype_typestr = implode(',', $tagarray);
                $data['labeltype_type'] = $labeltype_typestr;
            }else{
                $labeltype_typestr = $paramArray['labeltype_typelist'];
                $data['labeltype_type'] = $labeltype_typestr;
            }

            $field = array();
            $field['labeltype_name'] = "标签类型名称";
            $field['labeltype_type'] = "0 crm名单  1 在读学员  2 流失学员";

            if ($this->DataControl->updateData("crm_client_labeltype", "labeltype_id = '{$paramArray['labeltype_id']}'", $data)) {
                $label = $this->DataControl->selectClear("SELECT l.label_id,l.label_name FROM crm_client_label AS l WHERE l.labeltype_id = '{$paramArray['labeltype_id']}'");
                $label_list = json_decode(stripslashes($paramArray['label_list']), true);

                if ($label) {
                    foreach ($label as $item) {
                        $tags = $this->DataControl->getFieldOne("crm_client", "client_id", "client_tag like '%{$item['label_name']}%'");
                        $tagstu = $this->DataControl->getFieldOne("smc_student", "student_id", "student_crmtag like '%{$item['label_name']}%'");
                        if(!is_array($tags) && !is_array($tagstu)){
                            $this->DataControl->delData("crm_client_label", "label_id = '{$item['label_id']}'");
                        }
                    }
                    if ($label_list) {
                        $list = array();
                        foreach ($label_list as $val) {
                            $list['company_id'] = $paramArray['company_id'];
                            $list['labeltype_id'] = $paramArray['labeltype_id'];
                            $list['label_name'] = $val['label_name'];
                            $this->DataControl->insertData("crm_client_label", $list);
                        }
                    }
                } else {
                    if ($label_list) {
                        $list = array();
                        foreach ($label_list as $val) {
                            $list['company_id'] = $paramArray['company_id'];
                            $list['labeltype_id'] = $paramArray['labeltype_id'];
                            $list['label_name'] = $val['label_name'];
                            $this->DataControl->insertData("crm_client_label", $list);
                        }
                    }
                }
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "客户标签修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '客户标签修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除客户标签
    function delLabelTypeAction($paramArray)
    {
        $LabelTypeOne = $this->DataControl->getFieldOne("crm_client_labeltype", "labeltype_name", "labeltype_id = '{$paramArray['labeltype_id']}'");
        if ($LabelTypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $label = $this->DataControl->selectClear("SELECT l.label_id,l.label_name FROM crm_client_label as l WHERE l.labeltype_id = '{$paramArray['labeltype_id']}'");
            if ($label) {
                foreach ($label as $item) {
                    $tags = $this->DataControl->selectOne("SELECT REPLACE(client_tag, ',', '|') AS client_tag FROM crm_client WHERE company_id = '{$paramArray['company_id']}' AND client_tag <> '' HAVING '{$item['label_name']}' REGEXP client_tag");
                    if ($tags) {
                        ajax_return(array('error' => 1, 'errortip' => "标签类型名称【{$LabelTypeOne['labeltype_name']}】已被使用 不可删除"), $this->companyOne['company_language']);
                    }
                }
            }
            if ($this->DataControl->delData("crm_client_labeltype", "labeltype_id = '{$paramArray['labeltype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除客户标签成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除客户标签失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    /**
     * 意向星级列表
     * @param $paramArray
     * @return bool
     */
    public function intentionLevelList($paramArray)
    {
        //查询该集团下是否有意向星级注释  有则直接返回  没有则创建
        $dataList = $this->DataControl->selectClear("
        SELECT i.*
        FROM gmc_code_intentlevel i
        WHERE i.company_id='{$paramArray['company_id']}' 
        ORDER BY i.intentlevel_starnum ASC 
        ");

        if ($dataList) {
            $list = $dataList;
        } else {
            $this->DataControl->insertData('gmc_code_intentlevel', [
                'company_id' => $paramArray['company_id'],
                'intentlevel_starnum' => 1,
                'intentlevel_remark' => ''

            ]);
            $this->DataControl->insertData('gmc_code_intentlevel', [
                'company_id' => $paramArray['company_id'],
                'intentlevel_starnum' => 2,
                'intentlevel_remark' => ''

            ]);
            $this->DataControl->insertData('gmc_code_intentlevel', [
                'company_id' => $paramArray['company_id'],
                'intentlevel_starnum' => 3,
                'intentlevel_remark' => ''

            ]);
            $this->DataControl->insertData('gmc_code_intentlevel', [
                'company_id' => $paramArray['company_id'],
                'intentlevel_starnum' => 4,
                'intentlevel_remark' => ''

            ]);
            $this->DataControl->insertData('gmc_code_intentlevel', [
                'company_id' => $paramArray['company_id'],
                'intentlevel_starnum' => 5,
                'intentlevel_remark' => ''

            ]);

            $list = $this->DataControl->selectClear("
                SELECT i.*
                FROM gmc_code_intentlevel i
                WHERE i.company_id='{$paramArray['company_id']}' 
                ORDER BY i.intentlevel_starnum ASC 
                ");

        }

        if (!empty($list)) {
            foreach ($list as $key => $value) {
                $list[$key]['intentlevel_starnum'] = (int)$value['intentlevel_starnum'];

                $list[$key]['intentlevel_trackday_name'] = ($value['intentlevel_trackday']>0)?$value['intentlevel_trackday'].'天内至少追踪1次':'';
                $list[$key]['intentlevel_warningday_name'] = ($value['intentlevel_warningday']>0?$value['intentlevel_warningday'].'天前开始预警':'');
            }
        }

        $list = is_array($list) ? $list : array();

        if ($list) {
            $this->error = 0;
            $this->errortip = "意向星级列表获取成功";
            $this->result = $list;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据";
            $this->result = [];
            return false;
        }

    }

    /**
     * 编辑意向星级注释
     * @param $paramArray
     * @return bool
     */
    public function editIntentionLevel($paramArray)
    {
        $data = $this->DataControl->selectOne("
        SELECT i.* 
        FROM gmc_code_intentlevel i 
        WHERE i.intentlevel_id='{$paramArray['intentlevel_id']}' 
        ");
        if ($data) {
            $res = $this->DataControl->updateData("gmc_code_intentlevel", "intentlevel_id='{$paramArray['intentlevel_id']}'", [
                'intentlevel_remark' => $paramArray['intentlevel_remark'],
                'intentlevel_trackday' => $paramArray['intentlevel_trackday'],
                'intentlevel_warningday' => $paramArray['intentlevel_warningday'],
                'intentlevel_describe' => $paramArray['intentlevel_describe'],
                'intentlevel_updatatime' => time()
            ]);
            if ($res !== false) {
                $this->error = 0;
                $this->errortip = "意向星级注释修改成功!";
                $this->result = [];
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "操作失败!请稍微再试:(";
                $this->result = [];
                return false;
            }

        } else {
            $this->error = 1;
            $this->errortip = "未找到该条级别数据:(";
            $this->result = [];
            return false;
        }

    }




    //招生人员管理
    function getRecruiter($request)
    {
        $datawhere = "b.client_id = a.client_id and m.marketer_id = a.marketer_id and sf.staffer_id = m.staffer_id and a.school_id = '0' and a.marketer_id > 0 and b.client_tracestatus IN ( 0, 1, 2, 3 ) and a.principal_leave = 0 and b.company_id = '{$request['company_id']}' and sf.account_class = 0";

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (sf.staffer_cnname like '%{$request['keyword']}%' or sf.staffer_enname like '%{$request['keyword']}%' or sf.staffer_branch like '%{$request['keyword']}%') ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    a.marketer_id,
                    sf.staffer_cnname,
                    sf.staffer_enname,
                    sf.staffer_branch,
                    sf.staffer_leave,
                    (SELECT ps.post_name FROM gmc_company_post AS ps,gmc_staffer_postbe AS b WHERE ps.post_id = b.post_id AND b.staffer_id = sf.staffer_id AND b.school_id = 0 LIMIT 1) as post_name,
                    COUNT(IF(((SELECT st.schoolenter_id FROM crm_client_schoolenter AS st WHERE st.client_id = b.client_id AND st.company_id = b.company_id AND st.is_enterstatus = '1' AND st.is_gmctocrmschool = '1' ORDER BY st.schoolenter_id DESC LIMIT 0, 1 ) > 0 OR b.client_gmcdistributionstatus = '1'), a.client_id, NULL)) AS client_num
                FROM 
                    crm_client_principal AS a,
                    crm_client AS b,
                    crm_marketer AS m,
                    smc_staffer AS sf
                WHERE 
                    {$datawhere}
                GROUP BY 
                    a.marketer_id
                HAVING 
                    client_num > 0";

        $marketList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");

        if ($marketList) {
            $status = $this->LgArraySwitch(array('0' => '在职', '1' => '离职'));
            foreach ($marketList as &$value) {
                $value['staffer_enname'] = $value['staffer_enname'] ? $value['staffer_enname'] : '--';
                $value['post_name'] = $value['post_name'] ? $value['post_name'] : '--';
                $value['leave_name'] = $status[$value['staffer_leave']];
            }
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $marketList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];//负责人中文名
                    $datearray['staffer_enname'] = $dateexcelvar['staffer_enname'];//负责人英文名
                    $datearray['post_name'] = $dateexcelvar['post_name'];//集团职务
                    $datearray['leave_name'] = $dateexcelvar['leave_name'];//在职状态
                    $datearray['client_num'] = $dateexcelvar['client_num'];//负责人数
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('负责人中文名', '负责人英文名', '集团职务', '在职状态', '负责人数'));
            $excelfileds = array('staffer_cnname', 'staffer_enname', 'post_name', 'leave_name', 'client_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生人员管理表.xlsx"));
            exit;
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $db_nums = $this->DataControl->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $marketList;
        return $data;
    }
}
