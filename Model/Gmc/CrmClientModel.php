<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  CrmClientModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,account_class,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //在列表中没有使用
    function getStaffGmcCrm($company_id, $staffer_id)
    {
        $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel from gmc_staffer_postbe as p where p.postpart_id = '0' and p.postrole_id > '0' and p.postbe_ismianjob = '1' and p.postbe_status = '1' and p.staffer_id = '{$staffer_id}' and p.company_id = '{$company_id}' ");
        if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
            $this->error = 1;
            $this->errortip = "您的主职没有集团招生管理权限，无权查看！";
            return false;
        }
        if ($stafferPostbeOne['postbe_gmccrmlevel'] != '1') {
            $this->error = 1;
            $this->errortip = "您的主职没有集团招生管理的高管权限，无权查看！";
            return false;
        }
    }

    /**
     * 招生管理-招生名单管理
     * 取所有入校的名单
     * @param $paramArray
     * @return mixed]
     */
    function getClientList($paramArray)
    {
        $datawhere = "c.channel_id = l.channel_id AND e.client_id = c.client_id AND e.is_enterstatus = '1' AND c.company_id = '{$paramArray['company_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            if ($paramArray['keyword'] == '###') {
                $startiNums = rand(1, 9);
                $datawhere .= " AND c.client_mobile = (SELECT o.client_mobile FROM crm_client AS o WHERE o.company_id = '{$paramArray['company_id']}' AND o.client_tracestatus < '0' GROUP BY o.client_mobile HAVING COUNT(o.client_id) > 1 limit {$startiNums},1)";
            } else {
                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            }
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
        }

        if (isset($paramArray['school_istest']) && $paramArray['school_istest'] !== '') {
            $datawhere .= " and h.school_istest = '{$paramArray['school_istest']}' ";
        }

        if (isset($paramArray['channel_board']) && $paramArray['channel_board'] !== '') {
            $datawhere .= " and l.channel_board = '{$paramArray['channel_board']}' ";
        }
//        //接通状态 0未接通 1接通有效 2接通无效
//        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
//            $datawhere .= " and c.client_answerphone ='{$paramArray['client_answerphone']}'";
//        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
//            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_level >= 3";
//        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
//            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_level < 3";
//        }
        //是否接通电话
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone ='{$paramArray['client_answerphone']}'";
        }
        $having = "HAVING 1=1";
//        //跟踪状态：0待跟踪1持续跟踪2已邀约3已视听4已转正-1无意向-2无效名单  -----------  改展示逻辑了
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus = '{$paramArray['client_tracestatus']}' ";
        }

        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $datawhere .= " AND (SELECT i.invite_id FROM crm_client_invite i WHERE i.client_id = c.client_id LIMIT 0, 1 ) IS NULL";
            //$having .= " and invite_idthree is null and active_invite_id is null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $datawhere .= " AND (SELECT i.invite_id FROM crm_client_invite i WHERE i.client_id = c.client_id AND i.invite_isvisit = '0' LIMIT 0, 1 ) > 0";
            //$having .= " and invite_idthree is not null ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $datawhere .= " AND (SELECT i.invite_id FROM crm_client_invite i WHERE i.client_id = c.client_id AND i.invite_isvisit = '1' LIMIT 0, 1 ) > 0";
            //$having .= " and active_invite_id is not null ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $datawhere .= " AND (SELECT i.audition_id FROM crm_client_audition i WHERE i.client_id = c.client_id LIMIT 0, 1 ) IS NULL";
            //$having .= " and active_audition_id is null and audition_idthree is null  ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $datawhere .= " AND (SELECT i.audition_id FROM crm_client_audition i WHERE i.client_id = c.client_id AND i.audition_isvisit = '0' LIMIT 0, 1 ) > 0";
            //$having .= " and audition_idthree is not null ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $datawhere .= " AND (SELECT i.audition_id FROM crm_client_audition i WHERE i.client_id = c.client_id AND i.audition_isvisit = '1' LIMIT 0, 1 ) > 0";
            //$having .= " and active_audition_id is not null ";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " and e.school_id = '{$paramArray['school_id']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        if (isset($paramArray['commode_name']) && $paramArray['commode_name'] != '' && $paramArray['commode_name'] != '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['commode_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
        /*if (isset($paramArray['parenter_mobile']) && $paramArray['parenter_mobile'] !== '') {
            $datawhere .= " and sp.parenter_mobile like '%{$paramArray['parenter_mobile']}%'";
        }*/
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $s_starttime = strtotime($paramArray['start_time']);
            $datawhere .= " and c.client_createtime >= '{$s_starttime}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $e_endtime = strtotime($paramArray['end_time']) + 24 * 3600 - 1;
            $datawhere .= " and c.client_createtime <= '{$e_endtime}'";
        }
        //报名开始时间  和 报名结束时间  positvelog_addtime
        if (isset($paramArray['positvestart_time']) && $paramArray['positvestart_time'] != '' && isset($paramArray['positveend_time']) && $paramArray['positveend_time'] != '') {
            $positvestart_time = strtotime($paramArray['positvestart_time']);
            $positveend_time = strtotime($paramArray['positveend_time']) + 24 * 3600 - 1;
            $datawhere .= " AND c.client_id IN (SELECT p.client_id FROM crm_client_positivelog AS p
            WHERE p.company_id = '{$paramArray['company_id']}' AND p.positvelog_addtime >= '{$positvestart_time}' and p.positvelog_addtime <= '{$positveend_time}')";
        }

        //最后跟踪时间    track_createtime
        if (isset($paramArray['track_starttime']) && $paramArray['track_starttime'] != '') {
            $track_starttime = strtotime($paramArray['track_starttime']);
            $having .= " and track_lasttime >= '{$track_starttime}'";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] != '') {
            $track_endtime = strtotime($paramArray['track_endtime']) + 24 * 3600 - 1;
            $having .= " and track_lasttime <= '{$track_endtime}'";
        }

        //跟进次数   //跟进次数    track_count_type 1 为  ＜、  2 为  ≤、  0 为 ＝
        if (isset($paramArray['track_count']) && $paramArray['track_count'] != '') {
            //$having .= " and track_count <= '{$paramArray['track_count']}'";
            if($paramArray['track_count_type'] == '1'){
                $having .= " and track_count < '{$paramArray['track_count']}'";
            }elseif($paramArray['track_count_type'] == '2'){
                $having .= " and track_count <= '{$paramArray['track_count']}'";
            }elseif($paramArray['track_count_type'] == '0'){
                $having .= " and track_count = '{$paramArray['track_count']}'";
            }
        }

        ($having == "HAVING 1=1") ? $having = '' : $having = $having;

        if ($paramArray['dataequity'] != 1 && $paramArray['re_postbe_id'] > 0) {
            $postroleOne = $this->DataControl->selectOne("SELECT r.postrole_dataequity,b.organize_id
FROM gmc_company_postrole AS r,gmc_staffer_postbe AS b
WHERE r.postrole_id = b.postrole_id AND b.postbe_id = '{$paramArray['re_postbe_id']}' LIMIT 0,1");
            //判定非所有权益，仅组织权益
            if ($postroleOne['postrole_dataequity'] !== '1') {
                $datawhere .= " and l.channel_id in (SELECT o.channel_id FROM crm_channel_organize AS o WHERE o.organize_id = '{$postroleOne['organize_id']}') ";
                $datawhere .= " and e.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postroleOne['organize_id']}')";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        //统计数据总条数
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            if ($having == '') {
                $allNum = $this->DataControl->selectOne("SELECT COUNT(DISTINCT e.client_id) AS allnums
FROM crm_client c, crm_code_channel l, crm_client_schoolenter e WHERE {$datawhere}");
                if ($allNum) {
                    $data['allnums'] = $allNum['allnums'];
                } else {
                    $data['allnums'] = 0;
                }
            } else {
                $allNum = $this->DataControl->selectClear("SELECT e.client_id
, ( SELECT t.track_createtime FROM crm_client_track t WHERE t.client_id = c.client_id AND (t.track_isactive = '1' OR t.track_isgmcactive = '1') ORDER BY t.track_id DESC LIMIT 0, 1 ) AS track_lasttime
, ( SELECT count(t.track_id) FROM crm_client_track t WHERE t.client_id = c.client_id AND (t.track_isactive = '1' OR t.track_isgmcactive = '1') ) AS track_count
FROM crm_client c, crm_code_channel l, crm_client_schoolenter e WHERE {$datawhere} GROUP BY e.client_id {$having}");
                if ($allNum) {
                    $data['allnums'] = count($allNum);
                } else {
                    $data['allnums'] = 0;
                }
            }
        }
        $sqlFields = "c.client_id, c.channel_id, c.client_cnname, c.client_enname, c.client_sex ,c.client_patriarchname, c.client_age,c.client_frompage, c.client_mobile, c.client_intention_level, c.client_tag, c.client_answerphone
, FROM_UNIXTIME(c.client_createtime) AS client_createtime, c.client_address, c.client_source , c.client_tracestatus , l.channel_name, l.channel_maxday, l.channel_minday,e.school_id";
        $fieldOrder = 'e.client_id DESC';
        $sql = "SELECT {$sqlFields}
, ( SELECT sa.activity_name FROM crm_sell_activity sa WHERE c.activity_id = sa.activity_id ) AS activity_name
, ( SELECT s.school_cnname FROM smc_school s WHERE e.school_id = s.school_id limit 0,1) AS school_cnname
, ( SELECT t.track_createtime FROM crm_client_track t WHERE t.client_id = c.client_id AND (t.track_isactive = '1' OR t.track_isgmcactive = '1') ORDER BY t.track_id DESC LIMIT 0, 1 ) AS track_lasttime
, ( SELECT t.track_note FROM crm_client_track t WHERE t.client_id = c.client_id AND (t.track_isactive = '1' OR t.track_isgmcactive = '1') ORDER BY t.track_id DESC LIMIT 0, 1 ) AS track_lasttrack_note
, ( SELECT count(t.track_id) FROM crm_client_track t WHERE t.client_id = c.client_id AND (t.track_isactive = '1' OR t.track_isgmcactive = '1') ) AS track_count
,(select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname
, ( SELECT t.track_id FROM crm_client_track t WHERE t.client_id = c.client_id AND t.track_type = '1' ORDER BY t.track_id DESC LIMIT 0, 1 ) AS participate
, ( SELECT p.positvelog_addtime FROM crm_client_positivelog AS p WHERE p.client_id = c.client_id LIMIT 0,1 ) AS positivelog_time
, ifnull(( SELECT x.student_thirdbranch FROM crm_client_positivelog AS p,smc_student as x WHERE p.student_branch=x.student_branch and p.client_id = c.client_id LIMIT 0,1 ),'') AS student_thirdbranch
                FROM crm_client c, crm_code_channel l, crm_client_schoolenter e
                WHERE {$datawhere} 
                GROUP BY e.client_id  {$having} 
                ORDER BY {$fieldOrder}";

        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['track_lasttime'] = $val['track_lasttime'] ? date("Y-m-d H:i:s", $val['track_lasttime']) : '--';
                $val['client_cnname'] = $val['client_enname'] ? $val['client_cnname'] . '-' . $val['client_enname'] : $val['client_cnname'];
                $val['shengshiqu'] = ($val['area_id'] > 0) ? $val['province_name'] . '-' . $val['city_name'] . '-' . $val['area_name'] : '--';
                $val['client_intention_level'] = intval($val['client_intention_level']);
                $val['track_lasttrack_note'] = $val['track_lasttrack_note'] ? ($val['track_lasttrack_note'] . "（" . $val['track_lasttime'] . "）") : '--';
                $val['client_status'] = $clientTracestatus[$val['client_tracestatus']];
                if ($this->stafferOne['account_class'] == '1') {
                    $val['client_mobile'] = $val['client_mobile'];
                } else {
                    $val['client_mobile'] = hideNumberString($val['client_mobile']);
                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($val['client_createtime'] . "+{$val['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($val['client_createtime'] . "+{$val['channel_maxday']} day"));
                if ($val['channel_maxday'] == 0 || $time <= $max) {
                    $val['status'] = 1;
                } else {
                    $val['status'] = 0;
                }
                if ($val['channellog_id'] == null || $val['channel_id'] == '146' || $val['channel_id'] == '577') {
                    $val['apply'] = 1;
                } else {
                    $val['apply'] = 0;
                }
                if ($val['client_answerphone'] == 1) {
                    $val['client_answerphone'] = $this->LgStringSwitch("已接通");
//                    if ($val['client_intention_level'] >= 3) {
//                        $val['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                    } else {
//                        $val['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                    }
                } else {
                    $val['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];//姓名
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];//性别
                    $datearray['client_age'] = $dateexcelvar['client_age'];//年龄
                    $datearray['client_tag'] = $dateexcelvar['client_tag'];//标签
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];//标签
                    $datearray['client_patriarchname'] = $dateexcelvar['client_patriarchname'];//主要联系人姓名
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];//主要联系手机
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//所属校区
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['client_status'] = $dateexcelvar['client_status'];//客户状态
                    $datearray['client_answerphone'] = $dateexcelvar['client_answerphone'];//接通状态
                    $datearray['track_count'] = $dateexcelvar['track_count'];//跟进次数
                    $datearray['track_lasttime'] = $dateexcelvar['track_lasttime'];//最后跟进内容
                    $datearray['track_lasttrack_note'] = $dateexcelvar['track_lasttrack_note'];//最后跟进内容
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];//来源活动
                    $datearray['client_source'] = $dateexcelvar['client_source'];//渠道类型
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道明细
                    $datearray['client_address'] = $dateexcelvar['client_address'];//联系地址
                    $datearray['client_soursename'] = $dateexcelvar['client_soursename'];//渠道备注
                    $datearray['client_frompage'] = $dateexcelvar['client_frompage'];//来源页面
                    $datearray['positivelog_time'] = $dateexcelvar['positivelog_time']?date('Y-m-d',$dateexcelvar['positivelog_time']):'--';//报名时间
                    if($this->companyOne['company_isopenspecialnumber'] == '1') {
                        $datearray['student_thirdbranch'] = $dateexcelvar['student_thirdbranch'];//学号
                    }
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];//创建时间
                    $outexceldate[] = $datearray;
                }
            }
            if($this->companyOne['company_isopenspecialnumber'] == '1') {
                $excelheader = $this->LgArraySwitch(array('姓名', '性别', '年龄', '标签', '意向课程', '主要联系人姓名', '主要联系手机', '所属校区', '校区编号', '客户状态', '是否接通', '跟进次数', '最后跟进日期', '最后跟进内容', '来源活动', '渠道类型', '渠道明细', '联系地址', '渠道备注', '来源页面', '报名时间', '学号', '创建时间'));
                $excelfileds = array('client_cnname', 'client_sex', 'client_age', 'client_tag', 'course_cnname', 'client_patriarchname', 'client_mobile', 'school_cnname', 'school_branch', 'client_status', 'client_answerphone', 'track_count', 'track_lasttime', 'track_lasttrack_note', 'activity_name', 'client_source', 'channel_name', 'client_address', 'client_soursename', 'client_frompage', 'positivelog_time', 'student_thirdbranch', 'client_createtime');
            }else{
                $excelheader = $this->LgArraySwitch(array('姓名', '性别', '年龄', '标签', '意向课程', '主要联系人姓名', '主要联系手机', '所属校区', '校区编号', '客户状态', '是否接通', '跟进次数', '最后跟进日期', '最后跟进内容', '来源活动', '渠道类型', '渠道明细', '联系地址', '渠道备注', '来源页面', '报名时间', '创建时间'));
                $excelfileds = array('client_cnname', 'client_sex', 'client_age', 'client_tag', 'course_cnname', 'client_patriarchname', 'client_mobile', 'school_cnname', 'school_branch', 'client_status', 'client_answerphone', 'track_count', 'track_lasttime', 'track_lasttrack_note', 'activity_name', 'client_source', 'channel_name', 'client_address', 'client_soursename', 'client_frompage', 'positivelog_time',  'client_createtime');
            }

            $fielname = $this->LgStringSwitch("已分配校区名单");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " Limit {$pagestart},{$num} ";
            $clientList = $this->DataControl->selectClear($sql);
        }

        if ($clientList) {
            foreach ($clientList as $key => &$value) {
                $clientList[$key]['positivelog_time'] = $value['positivelog_time'] ? date("Y-m-d H:i:s", $value['positivelog_time']) : '--';
                $clientList[$key]['track_lasttime'] = $value['track_lasttime'] ? date("Y-m-d H:i:s", $value['track_lasttime']) : '--';
                $clientList[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
                $clientList[$key]['client_intention_level'] = intval($value['client_intention_level']);
                $clientList[$key]['track_lasttrack_note'] = $value['track_lasttrack_note'] ? ($value['track_lasttrack_note'] . "（" . $clientList[$key]['track_lasttime'] . "）") : '--';
                $clientList[$key]['client_status'] = $clientTracestatus[$value['client_tracestatus']];
                if ($this->stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $clientList[$key]['course_cnname'] = array();
                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                if ($value['channel_maxday'] == 0 || $time <= $max) {
                    $value['status'] = 1;
                } else {
                    $value['status'] = 0;
                }
                if ($value['channellog_id'] == null) {
                    $value['apply'] = 1;
                } else {
                    $value['apply'] = 0;
                }
                if ($value['channel_id'] == '146' || $value['channel_id'] == '577') {
                    $value['apply'] = 0;
                }
                if ($value['client_answerphone'] == 1) {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                    if ($value['client_intention_level'] >= 3) {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                    } else {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                    }
                } else {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
            }
        }
        if ($clientList) {
            $data['list'] = $clientList;
        } else {
            $data['list'] = array();
        }
        return $data;
    }

    /**
     * 招生管理-名单管理-客户基本信息
     * @param $client_id //客户id
     * @return array
     */
    function getClientOne($client_id, $company_id = '')
    {
        $client_count_list = array();
        $client_count_list['track_num'] = '1';
        $client_count_list['visit_num'] = '2';
        $client_count_list['invite_num'] = '3';
        $client_count_list['audition_num'] = '4';
        //意向课程
        $clientSource = $this->DataControl->selectClear("SELECT i.intention_id, o.coursecat_cnname, o.coursecat_id
FROM crm_client_intention AS i, smc_code_coursecat AS o
WHERE i.coursecat_id = o.coursecat_id AND i.client_id = '{$client_id}'");

        //添加默认联系人
        if (!$this->DataControl->selectOne("SELECT f.family_id FROM crm_client_family AS f WHERE f.client_id = '{$client_id}'")) {
            $parenterOne = $this->DataControl->selectOne("SELECT p.parenter_id,c.client_id, c.client_patriarchname, c.client_mobile
FROM crm_client c LEFT JOIN smc_parenter p ON c.client_mobile = p.parenter_mobile WHERE c.client_id = '{$client_id}'");
            if (!$parenterOne['parenter_id']) {
                $parenter = array();
                $parenter['parenter_cnname'] = $parenterOne['client_patriarchname'];
                $parenter['parenter_mobile'] = $parenterOne['client_mobile'];
                $parenterOne['parenter_id'] = $this->DataControl->insertData("smc_parenter", $parenter);
            }
            $family = array();
            $family['client_id'] = $parenterOne['client_id'];//
            $family['company_id'] = $company_id;
            $family['family_createtime'] = time();
            $family['parenter_id'] = $parenterOne['parenter_id'];//
            $family['family_cnname'] = $parenterOne['client_patriarchname'] == '' ? '匿名' : $parenterOne['client_patriarchname'];//
            $family['family_isdefault'] = '1';//
            $this->DataControl->insertData('crm_client_family', $family);
        }

        //家庭状况
        $ClientFamily = $this->DataControl->selectClear("SELECT f.family_id, f.family_relation, p.parenter_cnname AS family_cnname, p.parenter_mobile AS family_mobile, family_isdefault
FROM smc_parenter p, crm_client_family f WHERE p.parenter_id = f.parenter_id AND f.client_id = '{$client_id}'");
        if ($ClientFamily) {
            foreach ($ClientFamily as &$Family) {
                $Family['family_mobile'] = $Family['family_mobile'];
            }
        }


        $clientFields = 'c.client_id,c.client_cnname,c.client_img,c.client_enname,c.client_sex,client_oh_month,client_push_month,c.client_source,c.client_mobile,c.client_img,c.client_birthday,c.client_icard,c.client_intention_level,c.client_sponsor,c.client_remark,c.client_tracestatus,c.channel_id,c.client_fromtype,c.client_tag,c.client_answerphone,c.client_stubranch,c.client_fromtype,c.client_address';
        $clientOne = $this->DataControl->selectOne("select {$clientFields},n.nearschool_name,
            (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,
            (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
            (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
			    (select s.student_cnname  from smc_student as s where s.student_branch = c.client_stubranch ) as  client_stubranchname,
			    (select channel_name  from crm_code_channel as ch where ch.channel_id = c.channel_id ) as  channel_name,
			    (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) as activity_name,
				(select  m.marketer_name  from crm_client_principal  as p left JOIN  crm_marketer as m  ON m.marketer_id =p.marketer_id where p.client_id = '{$client_id}' and p.principal_ismajor =1  and p.principal_leave =0 and p.school_id <> '0' limit 0,1  ) as main_marketer_name,
				(select p.parenter_cnname FROM crm_client_family as f left JOIN smc_parenter as p  On p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault DESC limit 0,1 ) as main_family_cnname,
				(select s.school_shortname from crm_client_schoolenter as t left join smc_school as s ON t.school_id = s.school_id where t.client_id = c.client_id and t.is_enterstatus = '1' order by t.schoolenter_id desc limit 0,1) as school_shortname,
				(select r.region_name from smc_code_region as r where c.province_id = r.region_id) AS province_name,
                (select r.region_name from smc_code_region as r where c.city_id = r.region_id)     AS city_name,
                (select r.region_name from smc_code_region as r where c.area_id = r.region_id)     AS area_name,
                (select from_unixtime(t.track_createtime,'%Y-%m-%d') from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as  lasttracttime
			FROM crm_client as c left join crm_code_nearschool as n on c.nearschool_id = n.nearschool_id 
			WHERE c.client_id ='{$client_id}'");

        if ($clientOne['main_marketer_name'] == '') {
            $markerone = $this->DataControl->selectOne("select m.marketer_name  
                from  crm_client_principal  as p
				LEFT JOIN crm_marketer  as m ON  m.marketer_id = p.marketer_id
				where p.client_id = '{$clientOne['client_id']}' and  p.principal_ismajor=1 and p.principal_leave=1 
				ORDER by p.principal_createtime DESC limit 0,1");
            $clientOne['main_marketer_name'] = $markerone['marketer_name'];
        }

        if ($clientOne['client_stubranchname']) {
            $clientOne['client_stubranchname'] = $clientOne['client_stubranchname'] . ' ' . hideNumberString($clientOne['client_mobile']) . " | 就读校区：{$clientOne['school_shortname']}";
        }
        //0外部招生1内部招生2专案招生
        $arr_fromtype = $this->LgArraySwitch(array('0' => '外部招生', '1' => '内部招生', '2' => '专案招生'));
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        $clientOne['client_fromtype_name'] = $arr_fromtype[$clientOne['client_fromtype']];

        $clientOne['client_tracestatus_status'] = $clientOne['client_tracestatus'];
        $clientOne['client_tracestatus'] = $clientTracestatus[$clientOne['client_tracestatus']];

        if ($clientOne['channel_id'] == 0) {
            $clientOne['channel_id'] = "";
        }

        if ($clientOne['client_tag']) {
            $clientOne['client_tags'] = explode(',', $clientOne['client_tag']);
        }

        $clientOne['activity_name'] = is_null($clientOne['activity_name']) ? "" : $clientOne['activity_name'];
        $clientOne['client_remark'] = $clientOne['client_remark'];// . $clientOne['client_tag']
//        $clientOne['client_mobile'] = hideNumberString($clientOne['client_mobile']);
        $clientOne['client_mobile'] = $clientOne['client_mobile'];
//		副负责人
        $clientPrincipal = $this->DataControl->selectClear("select p.principal_id,m.marketer_name from crm_client_principal as p
  				left JOIN  crm_marketer as m ON m.marketer_id = p.marketer_id where p.client_id = '{$client_id}' and p.principal_ismajor = 0 and principal_leave = 0 limit 0,1");
        if ($clientPrincipal) {
            $clientOne['client_principal_name'] = array_column($clientPrincipal, 'marketer_name');
//            $clientOne['client_principal_name'] = implode("/",$clientPrincipalarray);
        } else {
            $clientOne['client_principal_name'] = array();
        }

        if ($ClientFamily) {
            $clientOne['client_family_list'] = $ClientFamily;
        } else {
            $clientOne['client_family_list'] = array();
        }

        if ($clientSource) {
            $clientOne['client_course_list'] = $clientSource;
        } else {
            $clientOne['client_course_list'] = array();
        }

        $clientOne['client_count_list'] = $client_count_list;

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isassist,company_istrackcoursetype,company_ismusttrackcoursetype", "company_id='{$company_id}'");
        $clientOne['company_istrackcoursetype'] = $companyOne['company_istrackcoursetype'];
        $clientOne['company_ismusttrackcoursetype'] = $companyOne['company_ismusttrackcoursetype'];
        return $clientOne;
    }

    //申请变更渠道
    function applyChannelAction($paramArray)
    {
        $a = $this->DataControl->getFieldOne("crm_client_channellog", "channellog_id", "client_id = '{$paramArray['client_id']}' and channellog_status = '0'");
        if ($a) {
            ajax_return(array('error' => 1, 'errortip' => "该名单有待审核的渠道更改请求，无法再次申请"), $this->companyOne['company_language']);
        }
        if ($paramArray['channellog_img']) {
            $channellog_imgarray = json_decode(stripslashes($paramArray['channellog_img']), true);
            if (is_array($channellog_imgarray)) {
                $channellog_imgstr = implode(',', $channellog_imgarray);
            } else {
                $channellog_imgstr = $paramArray['channellog_img'];
            }
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['client_id'] = $paramArray['client_id'];
        $data['from_channel_id'] = $paramArray['from_channel_id'];
        $data['to_channel_id'] = $paramArray['to_channel_id'];
        $data['channellog_note'] = $paramArray['channellog_note'];
        $data['channellog_status'] = '0';
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['channellog_level'] = $paramArray['channellog_level'];
        $data['channellog_changetime'] = $paramArray['channellog_changetime'];
        $data['channellog_img'] = $channellog_imgstr;
        $data['channellog_createtime'] = time();

        $field = array();
        $field['company_id'] = "集团id";
        $field['client_id'] = "名单id";
        $field['from_channel_id'] = "原渠道id";
        $field['to_channel_id'] = "现渠道id";
        $field['channellog_note'] = "变更原因";
        $field['channellog_level'] = "级别";

        if ($id = $this->DataControl->insertData("crm_client_channellog", $data)) {
            $trackData = array();
            $trackData['client_id'] = $paramArray['client_id'];
            $trackData['channellog_id'] = $id;
            $trackData['tracks_title'] = '0';
            $trackData['staffer_id'] = $paramArray['staffer_id'];
            $trackData['tracks_time'] = time();
            $this->DataControl->insertData("crm_client_channel_tracks", $trackData);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "申请变更渠道成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "申请变更渠道失败", 'result' => $result);
        }
        return $res;
    }

    //获取意向班种
    function getIntentCourse($company_id)
    {
        $courseList = $this->DataControl->getFieldquery('smc_code_coursecat', 'coursecat_id,coursecat_cnname,coursecat_branch', "company_id = '{$company_id}' and coursecat_iscrmadded = 1");

        if ($courseList) {
            $list = $courseList;
        } else {
            $list = array();
        }

        return $list;
    }

    //检测重复手机号 -- TMK要可以录入名单 --- 新写的不一定有用
    function checkClientMobile($paramArray, $from = "", $client_id = ""){
        $paramArray['client_cnname'] = trim($paramArray['client_cnname']);
        $paramArray['client_mobile'] = trim($paramArray['client_mobile']);

        $marketerOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer
        where staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
        if (!$marketerOne) {
            $addmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $marketerOne['staffer_id'] = $addmarkertOne['staffer_id'];
            $marketerOne['marketer_id'] = $addmarkertOne['marketer_id'];
            $marketerOne['marketer_name'] = $addmarkertOne['marketer_name'];
        }

        $where = "(
        client_mobile='{$paramArray['client_mobile']}'
        OR c.client_id IN ( SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}' )
        ) and c.company_id='{$paramArray['company_id']}'";

        if ($from != "") {
            $where = "client_cnname='{$paramArray['client_cnname']}' 
            and (
            client_mobile='{$paramArray['client_mobile']}' 
            OR c.client_id IN ( SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}' )
            ) 
            and cs.company_id ='{$paramArray['company_id']}' ";
        }
        if ($client_id != "") {
            $where .= " and (c.client_id <> '{$client_id}')";
        }
        if ($paramArray['client_id']) {
            $where .= " and (c.client_id <> '{$paramArray['client_id']}')";
        }

        $sqlfields = "c.client_id,c.client_img,c.client_cnname,c.client_enname,c.client_sex,c.client_age,c.client_isgross,c.client_distributionstatus,c.client_mobile,c.client_tracestatus,c.client_source,c.channel_id,s.school_id,s.school_cnname,'0' as issmcstu  ";
        $sqlorder = 'c.client_createtime';

        $sql = "select {$sqlfields},
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_enterstatus = '1' and st.school_id > 0 limit 0,1) as isinschool,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and c.client_id = p.client_id and p.school_id = '0' limit 0,1 ) as isgmchaveprincipal,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and c.client_id = p.client_id and p.school_id > 0 limit 0,1 ) as iscrmhaveprincipal,
                (select p.tmkprincipal_id from crm_client_tmkprincipal as p where p.tmkprincipal_leave = 0 and c.client_id = p.client_id and p.school_id > 0 limit 0,1 ) as iscrmtmkhaveprincipal,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and c.client_id = p.client_id and p.school_id = '0' and p.marketer_id = '{$marketerOne['marketer_id']}' limit 0,1 ) as megmcisprincipal,
                (select p.principal_id from crm_client_principal as p where p.principal_leave = 0 and c.client_id = p.client_id and p.school_id > '0' and p.marketer_id = '{$marketerOne['marketer_id']}' limit 0,1 ) as mecrmisprincipal,
                (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as have_invite_id,
                (select i.audition_id from crm_client_audition as i where i.client_id = c.client_id and i.audition_isvisit = '0' limit 0,1) as have_audition_id,
                
                (select st.schoolenter_id from crm_client_schoolenter as st where st.client_id = c.client_id and st.is_gmctocrmschool = '1' and st.is_enterstatus = '1' limit 0,1) as gmcIndschoolenter_id,  
                (select marketer_name from crm_client_principal as p LEFT JOIN  crm_marketer  as m  ON p.marketer_id = m.marketer_id
                 where p.client_id=c.client_id  and p.principal_ismajor = 1 and p.principal_leave = 0 order by principal_createtime Desc limit 0,1) as marketer_name,
                 
                (select m.marketer_name from crm_client_tmkprincipal as p LEFT JOIN  crm_marketer  as m  ON p.marketer_id = m.marketer_id
                 where p.client_id=c.client_id  and p.tmkprincipal_leave = 0 order by tmkprincipal_createtime Desc limit 0,1) as crmtmk_marketer_name ,
                (select h.channel_name from crm_code_channel as h where h.channel_id = c.channel_id limit 0,1) as channel_name 
                FROM crm_client as c
                LEFT JOIN crm_client_schoolenter as cs ON cs.client_id = c.client_id and cs.is_enterstatus =1
                LEFT JOIN smc_school as s ON s.school_id = cs.school_id
                WHERE {$where} ORDER BY {$sqlorder} DESC ";

        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            $clientidarray = array_column($clientList, 'client_id');
            $clientidsstr = implode(",", $clientidarray);
//            0待跟踪1持续跟踪2已柜询3已试听4已转正-1已流失-2已无效'
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => $value) {
                $clientList[$key]['isgmcIndschoolenter'] = ($value['gmcIndschoolenter_id'] > 0) ? '1' : '0';
                if (empty($value['marketer_name'])) {
                    $clientList[$key]['marketer_name'] = "--";
                }

                if($value['isinschool'] > 1){//是否在学校  >1 在
                    if($value['iscrmhaveprincipal'] > 1) {//Crm是否有负责人  >1 有
                        if($value['megmcisprincipal'] > 1) {//负责人是否有自己  >1 有
                            if($value['have_invite_id'] > 1 || $value['have_audition_id'] > 1){
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                            }else {
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                                $clientList[$key]['is_track'] = 1;
                            }
                        }else{
                            if(($value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1)){//有非自己的其他负责人 跟踪状态是  -1或者-2
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                                $clientList[$key]['is_track'] = 1;
                            }else {
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                            }
                        }
                    }else{
                        if($value['iscrmtmkhaveprincipal'] > 1 ){//是否有电销负责人
                            $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");

                            $clientList[$key]['marketer_name'] =  $clientList[$key]['crmtmk_marketer_name'];
                        }else {
                            if ($value['megmcisprincipal'] > 1) {//负责人是否有自己  >1 有
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                                $clientList[$key]['is_track'] = 1;
                            } else {
                                if (($value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1 || ($value['client_tracestatus'] == '0' && $value['client_distributionstatus'] == '0'))) {//有非自己的其他负责人 跟踪状态是  -1或者-2
                                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                                    $clientList[$key]['is_track'] = 1;
                                } else {
                                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                                }
                            }
                        }
                    }
                }else{
                    if($value['isgmchaveprincipal'] > 1){//集团是否有负责人  >1 有
                        if($value['megmcisprincipal'] > 1) {//负责人是否有自己  >1 有
                            $clientList[$key]['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                            $clientList[$key]['is_track'] = 1;
                        }else{
                            if(($value['client_tracestatus'] == -2 || $value['client_tracestatus'] == -1)){//有非自己的其他负责人 跟踪状态是  -1或者-2
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                                $clientList[$key]['is_track'] = 1;
                            }else {
                                $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                            }
                        }
                    }else{
                        $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                        $clientList[$key]['is_track'] = 1;
                    }
                }
                if($value['have_invite_id'] > 1 || $value['have_audition_id'] > 1){
                    $clientList[$key]['is_track'] = 0;
                }

                $clientList[$key]['client_tracestatus_name'] = $clientTracestatus[$value['client_tracestatus']];
            }
        } else {
            $clientList = array();
        }

        $stuwhere = " sf.family_mobile = '{$paramArray['client_mobile']}' ";
        if ($clientidsstr) {
            $stuwhere .= " and from_client_id not in ($clientidsstr) ";
        }
        if ($from != "") {
            $stuwhere .= " and student_cnname='{$paramArray['client_cnname']}' ";
        }
        $schsql = " select  s.student_id as client_id,
                        s.student_cnname as client_cnname,
                        s.student_sex as client_sex,
                        s.student_birthday,
                        h.school_cnname,
                        '--' as client_source,
                        '--' as channel_name,
                        '--' as marketer_name,
                        '已转正' as client_tracestatus_name,
                        '1' as issmcstu 
 
                        FROM smc_student_enrolled as se
                        left join smc_student as s on se.student_id=s.student_id
                        left join smc_student_family as sf on sf.student_id=s.student_id 
                        left join smc_school as h on h.school_id = se.school_id
                        where {$stuwhere} and sf.family_isdefault=1 and h.company_id = '{$paramArray['company_id']}' 
                        order by s.student_id ASC 
                        limit 0,1 ";
        $clientListTwo = $this->DataControl->selectClear($schsql);

        if ($clientListTwo) {
            foreach ($clientListTwo as &$clientVar) {
                $clientVar['client_age'] = birthdaytoage($clientVar['student_birthday']);
                $clientVar['track_stuname'] = $this->LgStringSwitch("继续跟踪");
            }
        } else {
            $clientListTwo = array();
        }

        $clientAll = array_merge($clientList, $clientListTwo);
        if (!$clientAll) {
//            '姓名', '性别', '年龄', '所属学校', '渠道类型', '渠道明细','主要负责人', '客户状态',
//            'client_cnname', 'client_sex',   'client_age', 'school_cnname','client_source','channel_name','marketer_name',  'client_tracestatus_name',

            $stuwhere = " sf.family_mobile = '{$paramArray['client_mobile']}' ";
            if ($from != "") {
                $stuwhere .= " and student_cnname='{$paramArray['client_cnname']}' ";
            }
            $schsql = " select  s.student_id as client_id,
                        s.student_cnname as client_cnname,
                        s.student_sex as client_sex,
                        s.student_birthday,
                        h.school_cnname,
                        '--' as client_source,
                        '--' as channel_name,
                        '--' as marketer_name,
                        '已转正' as client_tracestatus_name,
                        '1' as issmcstu 
 
                        FROM smc_student_enrolled as se
                        left join smc_student as s on se.student_id=s.student_id
                        left join smc_student_family as sf on sf.student_id=s.student_id 
                        left join smc_school as h on h.school_id = se.school_id
                        where {$stuwhere} and sf.family_isdefault=1 and h.company_id = '{$paramArray['company_id']}' 
                        order by s.student_id ASC 
                        limit 0,1 ";
            $clientAll = $this->DataControl->selectClear($schsql);

            if ($clientAll) {
                foreach ($clientAll as &$clientVar) {
                    $clientVar['client_age'] = birthdaytoage($clientVar['student_birthday']);
                    $clientVar['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                }
            } else {
                $clientAll = array();
            }
        }
        //一个学员可同时存在在多家校，输入学员手机号码时若有重复则提示重复学员记录，重复记录点击可查看，弹窗内检索整个集团的学员；
        //1.该学员在本校且已有其他负责人的情况下，按钮【我要跟踪】显示为灰色状态不可点击；
        //2.该学员在本校无负责人的情况下（可能是已无意向或在有效名单里），按钮显示为蓝色【我要跟踪】点击弹出弹窗进入跟进页面；
        //3.若该学员在本校且是自己正在负责，按钮显示为蓝色【继续跟踪】点击弹出弹窗进入跟进页面；
        //4.若该学员在其他学校，按钮都显示为蓝色【我要跟踪】点击弹出弹窗进入跟进页面，即转为自己的意向客户，是两个学校的有效名单；
        return $clientAll;
    }
    /**
     * @param $paramArray
     * @return bool
     * 录入名单 -- TMK要可以录入名单
     */
    function insertClientOne($paramArray)
    {
        if (!empty($paramArray['client_icard'])) {
            if (!$this->checkClientIcard($paramArray['client_icard'])) {
                $this->error = 1;
                $this->errortip = "身份证号重复,无法添加";
                return false;
            }
        }
        $checkData = array();
        $checkData['client_cnname'] = $paramArray['client_cnname'];
        $checkData['client_mobile'] = $paramArray['client_mobile'];
        $checkData['company_id'] = $paramArray['company_id'];
        $checkData['school_id'] = $paramArray['school_id'];
        $from = "add";
        if ($this->checkIntentionClient($checkData, $from)) {
            $this->error = 1;
            $this->errortip = "中文名与手机号重复,无法添加";
            return false;
        }

        if ($paramArray['company_id'] == '8888') {
            if ($this->checkIntentionClient($checkData, '') && $paramArray['channel_id'] <> '599') {
                $this->error = 1;
                $this->errortip = "手机号重复并且渠道不是【TMK同胞转介绍】,无法添加";
                $this->result = ['isTipsPhoneChannel' => '1'];
                return false;
            }
        }
        if ($paramArray['company_id'] == '1001') {
            if ($this->checkIntentionClient($checkData, '') && $paramArray['channel_id'] <> '600') {
                $this->error = 1;
                $this->errortip = "手机号重复并且渠道不是【TMK同胞转介绍】,无法添加";
                $this->result = ['isTipsPhoneChannel' => '1'];
                return false;
            }
        }

        $marketerOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer
        where staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
        if (!$marketerOne) {
            $addmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $marketerOne['staffer_id'] = $addmarkertOne['staffer_id'];
            $marketerOne['marketer_id'] = $addmarkertOne['marketer_id'];
            $marketerOne['marketer_name'] = $addmarkertOne['marketer_name'];
        }
        //渠道
        $channelOne = $this->DataControl->selectOne("select channel_id,channel_medianame,channel_intention_level from crm_code_channel where company_id = '{$paramArray['company_id']}' AND channel_id = '{$paramArray['channel_id']}'");
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['marketer_id'] = $marketerOne['marketer_id'];
        $data['activity_id'] = $paramArray['activity_id'];
        $data['client_cnname'] = trim($paramArray['client_cnname']);
        $data['client_enname'] = trim($paramArray['client_enname']);
        $data['client_img'] = $paramArray['client_img'];
        $data['client_oh_month'] = $paramArray['client_oh_month'];
        $data['client_push_month'] = $paramArray['client_push_month'];
        $data['client_fromtype'] = $paramArray['client_fromtype'];
        $data['client_age'] = birthdaytoage($paramArray['client_birthday']);
        $data['province_id'] = $paramArray['province_id'];
        $data['city_id'] = $paramArray['city_id'];
        $data['area_id'] = $paramArray['area_id'];

        $data['client_birthday'] = $paramArray['client_birthday'];
        $data['client_sex'] = $paramArray['client_sex'];
        $data['client_mobile'] = $paramArray['client_mobile'];
        $data['client_icard'] = $paramArray['client_icard'];
        $data['client_remark'] = $paramArray['client_remark'];
        $data['client_source'] = $paramArray['client_source'];
        $data['channel_id'] = $paramArray['channel_id'];
        if($channelOne['channel_intention_level'] > 0){
            $data['client_intention_level'] = $channelOne['channel_intention_level'];
            $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
        }else {
            $data['client_intention_level'] = $paramArray['client_intention_level'];
            $data['client_intention_maxlevel'] = $paramArray['client_intention_level'];
        }
        $data['client_cardnuber'] = $paramArray['client_cardnuber'];
        $data['client_nationtype'] = $paramArray['client_nationtype'];
        $data['client_publicschool'] = $paramArray['client_publicschool'];
        $data['client_primaryschool'] = $paramArray['client_primaryschool'];
        $data['nearschool_id'] = $paramArray['nearschool_id'];
        $data['client_tracestatus'] = 0;
        $family = $paramArray['client_family_list'];
        $familycount = count($family);

        $family_mobile = array();
        if ($familycount) {
            for ($k = 0; $k < $familycount; $k++) {
                if (($family[$k]['family_mobile'] != "")) {
                    $family[$k]['family_mobile'] = trim($family[$k]['family_mobile']);
                    $family_mobile[] = $family[$k]['family_mobile'];
                    //主要联系人的电话同步
                    if ($family[$k]['family_isdefault'] == 1) {
                        $data['client_mobile'] = $family[$k]['family_mobile'];
                    }
                } else {
                    $family_mobile = array();
                }
            }
            if (count($family_mobile) != count(array_unique($family_mobile))) {
                $this->error = 1;
                $this->errortip = "家长手机号重复";
                return false;
            }
        }
        $data['client_actisagree'] = 1;//20191231 导入的名单默认同意协议
        $data['client_createtime'] = time();
        $data['client_updatetime'] = time();  //加此字段好排序
        $data['client_sponsor'] = $paramArray['client_sponsor'];
        $data['client_teachername'] = $paramArray['client_teachername'];
        if (isset($paramArray['is_recommend']) && $paramArray['is_recommend'] == 1) {
            $data['client_sponsor'] = $paramArray['client_sponsor'];
            $data['client_stubranch'] = trim($paramArray['client_stubranch']);
        }

        $this->DataControl->begintransaction();    //开启事务
        //新增客户表
        if ($id = $this->DataControl->insertData("crm_client", $data)) {
            //添加名单状态记录
            $Model = new  \Model\Api\CalloutModel($paramArray);
            $Model->addClientTimerecord($paramArray['company_id'],$paramArray['school_id'],$id,1,$marketerOne['marketer_id'],"集团TMK录入名单");

            //新增跟进记录
            $trackData = array();
            $trackData['client_id'] = $id;
            $trackData['marketer_id'] = $marketerOne['marketer_id'];
            $trackData['marketer_name'] = $marketerOne['marketer_name'];
            $trackData['track_intention_level'] = $paramArray['client_intention_level'];
            $trackData['school_id'] = $paramArray['school_id'];
            $trackData['track_linktype'] = $this->LgStringSwitch("系统新增");//20200109需求修改
            $trackData['track_note'] = $this->LgStringSwitch("系统新增有效名单");//20200109需求修改
            $trackData['track_createtime'] = time();
            $trackData['track_type'] = 0;
            $trackData['track_initiative'] = 1;
            $this->DataControl->insertData('crm_client_track', $trackData);

            //家长联系人
            $family = $paramArray['client_family_list'];
            $familycount = count($family);
            if ($familycount) {
                for ($key = 0; $key < $familycount; $key++) {
                    if ($family[$key]['family_mobile'] != "") {
                        $family[$key]['family_cnname'] = trim($family[$key]['family_cnname']);
                        $family[$key]['family_relation'] = trim($family[$key]['family_relation']);
                        $family[$key]['family_mobile'] = trim($family[$key]['family_mobile']);

                        $parenter_id = $this->DataControl->getFieldOne("smc_parenter", 'parenter_id', "parenter_mobile = '{$family[$key]['family_mobile']}' ");
                        if ($parenter_id) {
                            $familydata = array();
                            $familydata['client_id'] = $id;
                            $familydata['company_id'] = $paramArray['company_id'];
                            $familydata['family_createtime'] = time();
                            $familydata['family_relation'] = $family[$key]['family_relation'];
                            $familydata['parenter_id'] = $parenter_id['parenter_id'];
                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = "新增家庭联系人失败";
                                return false;
                                //return [false,"新增家庭联系人失败"];
                            }
                        } else {
                            $parenterData = array();
                            $parenterData['parenter_cnname'] = $family[$key]['family_cnname'];
                            $parenterData['parenter_mobile'] = $family[$key]['family_mobile'];
                            $parenterData['parenter_addtime'] = time();
                            if (!$insert_id = $this->DataControl->insertData("smc_parenter", $parenterData)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = "新增家长失败";
                                return false;
                                //return [false,"新增家长失败"];
                            }
                            $familydata = array();
                            $familydata['client_id'] = $id;
                            $familydata['company_id'] = $paramArray['company_id'];
                            $familydata['family_createtime'] = time();
                            $familydata['family_relation'] = $family[$key]['family_relation'];
                            $familydata['parenter_id'] = $insert_id;
                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = "新增家庭联系人失败";
                                //return [false,"新增家庭联系人失败"];
                            }

                        }
                    }

                }
            }

            //添加意向课程
            $intention = array();
            $intention['client_id'] = $id;
            $course = $paramArray['intention_course'];
            $coursecount = count($course);
            if ($coursecount) {
                for ($i = 0; $i < $coursecount; $i++) {
                    if ($course[$i]['coursecat_id'] !== '' && $course[$i]['coursecat_id'] !== '0') {
                        $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat"
                            , "coursecat_id,coursetype_id", "coursecat_id='{$course[$i]['coursecat_id']}' AND company_id = '{$paramArray['company_id']}'");
                        if ($coursecatOne) {
                            if (!$this->DataControl->getFieldOne("crm_client_intention", "intention_id",
                                "client_id = '{$id}' AND coursecat_id = '{$coursecatOne['coursecat_id']}'")) {
                                $intention['coursetype_id'] = $coursecatOne['coursetype_id'];
                                $intention['coursecat_id'] = $coursecatOne['coursecat_id'];
                                $intention['intention_updatetime'] = time();
                                $this->DataControl->insertData("crm_client_intention", $intention);
                            }
                        }
                    }
                }
            }
        } else {

            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = "增加副分配记录失败";
            return false;
            //return [false,"新增客列表户失败"];
        }

        $this->DataControl->commit();
        $this->error = 0;
        $this->errortip = "新增成功";
        return true;
        //return [true, ""];
    }

    //录入名单 -- TMK我要跟踪
    function trackRepeatClient($paramArray)
    {
        $marketerInfo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
        if (!$marketerInfo) {
            $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $marketerInfo['marketer_id'] = $getaddmarkertOne['marketer_id'];
            $marketerInfo['marketer_name'] = $getaddmarkertOne['marketer_name'];
        }

        //符合条件的走下边的操作 -- 要在这里在补充上上边的判断

        //名单变更为 -- TMK人员负责，清楚其他的负责和分配 -- TMK我要跟踪
        $this->delTmkClientPrincipalSchool($marketerInfo['marketer_id'],$marketerInfo['marketer_name'],$paramArray['client_id']);
        //行为日志
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '添加名单，重复名单跟进', dataEncode($paramArray));

        $this->error = 0;
        $this->errortip = "分配成功";
        return true;
    }

    //名单变更为 -- TMK人员负责，清楚其他的负责和分配 -- TMK我要跟踪
    function delTmkClientPrincipalSchool($marketer_id,$marketer_name,$client_id){
        //事务开启
        $this->DataControl->begintransaction();
        //清楚学校的分配记录
        $other_Data = array();
        $other_Data['is_enterstatus'] = "-1";
        $other_Data['schoolenter_updatetime'] = time();
        if (!$this->DataControl->updateData('crm_client_schoolenter', "client_id='{$client_id}' and school_id<>'0' ", $other_Data)){
            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = "清楚学校的分配记录";
            return false;
        }
        //更改是分配状态
        $data = array();
        $data['client_distributionstatus'] = 0;

        $data['client_gmcdistributionstatus'] = 1;
        $data['client_tracestatus'] = 0;
        $data['client_ischaserlapsed'] = 0;
        $data['client_updatetime'] = time();
        if (!$this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $data)) {
            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = "更新客户状态失败";
            return false;
        }
        //分配主负责人
        if ($this->setClientPrincipal($client_id, $marketer_id, $marketer_id)) {
            //添加记录 -- 去除日志
            $marklist = $this->DataControl->selectClear(" select marketer_id from crm_client_principal where client_id = '{$client_id}' AND marketer_id <> '{$marketer_id}' ");
            if($marklist) {
                foreach ($marklist as $marklistVar) {
                    $this->addAllotLog($client_id, $marklistVar['marketer_id'], $marketer_id, 0, 1);
                }
            }
            //去除非本人的 分配记录
            $dataPrintcipal = array();
            $dataPrintcipal['principal_leave'] = 1;
            $dataPrintcipal['principal_updatatime'] = time();
            $this->DataControl->updateData('crm_client_principal', "client_id = '{$client_id}' AND marketer_id <> '{$marketer_id}' ", $dataPrintcipal);
        }else{
            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = "负责人分配失败！";
            return false;
        }
        //添加跟踪记录
        $dataTrack = array();
        $dataTrack['client_id'] = $client_id;
        $dataTrack['track_type'] = 1; //1-集团跟踪
        $dataTrack['school_id'] = 0;
        $dataTrack['track_validinc'] = 1;
        $dataTrack['marketer_id'] = $marketer_id;
        $dataTrack['marketer_name'] = $marketer_name;
        $dataTrack['track_linktype'] = $this->LgStringSwitch("系统");
        $dataTrack['track_note'] = $this->LgStringSwitch("由{$marketer_name}通过添加名单，在重复名单中进行跟进");
        $dataTrack['track_linktype'] = $this->LgStringSwitch("重复名单跟进");//20200109需求修改
        $dataTrack['track_createtime'] = time();
        $this->DataControl->insertData('crm_client_track', $dataTrack);

        $this->DataControl->commit(); //执行
    }


    //检测是否邀约 是否试听 当天
    function checkIsvisitDate($client_id, $date, $type = '', $hour = '')
    {
        if ($type == '1') {
            $inviteOne = $this->DataControl->selectOne("SELECT invite_id AS invite FROM crm_client_invite i
WHERE i.client_id = '{$client_id}' AND FROM_UNIXTIME(UNIX_TIMESTAMP(i.invite_visittime), '%Y-%m-%d') = '{$date}' AND i.invite_isvisit <> '-1' limit 0,1");
        } elseif ($type == '2') {
            $inviteOne = $this->DataControl->selectOne("SELECT a.audition_id AS invite FROM crm_client_audition a
WHERE a.client_id = '{$client_id}' AND FROM_UNIXTIME(UNIX_TIMESTAMP(a.audition_visittime), '%Y-%m-%d') = '{$date}' AND a.hour_id = '{$hour}' AND a.audition_isvisit <> '-1'  limit 0,1");
        } else {
            $inviteOne = $this->DataControl->selectOne("SELECT a.audition_id AS invite FROM crm_client_audition a
WHERE a.client_id = '{$client_id}' AND FROM_UNIXTIME(UNIX_TIMESTAMP(a.audition_visittime), '%Y-%m-%d') = '{$date}' AND a.audition_isvisit <> '-1'
UNION ALL SELECT invite_id AS invite FROM crm_client_invite i
WHERE i.client_id = '{$client_id}' AND FROM_UNIXTIME(UNIX_TIMESTAMP(i.invite_visittime), '%Y-%m-%d') = '{$date}' AND i.invite_isvisit <> '-1' limit 0,1");
        }
        if ($inviteOne) {
            return false;
        } else {
            return true;
        }
    }

    //判断今日是否有邀约记录a试听i柜询
    function checkIsinviteLog($client_id, $date, $school_id, $visitType = 'a')
    {
        if ($visitType == 'a') {
            $sql = "SELECT
                        a.audition_id
                    FROM
                        crm_client_audition a
                    WHERE
                        a.school_id = '{$school_id}'
                    AND a.client_id = '{$client_id}'
                    AND a.audition_isvisit <> '-1'
                    AND DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = DATE_FORMAT('{$date}', '%Y-%m-%d')
                    limit 0,1";
        } else {
            $sql = "SELECT
                        i.invite_id
                    FROM
                        crm_client_invite i
                    WHERE
                        i.school_id = '{$school_id}'
                    AND i.client_id = '{$client_id}'
                    AND i.invite_isvisit <> '-1'
                    AND DATE_FORMAT(i.invite_visittime, '%Y-%m-%d') = DATE_FORMAT('{$date}', '%Y-%m-%d')
                    limit 0,1";
        }
        if ($this->DataControl->selectOne($sql)) {
            return true;
        } else {
            return false;
        }
    }


    //判断今日是否有邀约记录a试听i柜询
    function checkAuditionLog($client_id, $date, $school_id, $coursetype_id)
    {
        if ($this->DataControl->selectOne("SELECT
                                                    a.audition_id
                                                FROM
                                                    crm_client_audition a
                                                WHERE
                                                    a.school_id = '{$school_id}'
                                                AND a.client_id = '{$client_id}'
                                                AND a.audition_isvisit <> '-1'
                                                AND a.coursetype_id = '{$coursetype_id}'
                                                AND DATE_FORMAT(a.audition_visittime, '%Y-%m-%d') = DATE_FORMAT('{$date}', '%Y-%m-%d') limit 0,1")) {
            return true;
        } else {
            return false;
        }
    }


    //添加客户学校记录
    function AddClientschool($client_id, $school_id, $company_id, $is_gmcdirectschool = '0')
    {
        $schoolenter_id = $this->DataControl->getFieldOne('crm_client_schoolenter', 'schoolenter_id', "client_id='{$client_id}' and school_id='{$school_id}'");
        if ($schoolenter_id) {
            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '1';
            $schoolenter['is_gmctocrmschool'] = '1';
            $schoolenter['is_gmcdirectschool'] = $is_gmcdirectschool;
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id='{$school_id}'", $schoolenter);

            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id <> '{$school_id}'", $schoolenter);
        } else {
            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id <> '{$school_id}'", $schoolenter);

            $data = array();
            $data['client_id'] = $client_id;
            $data['school_id'] = $school_id;
            $data['company_id'] = $company_id;
            $data['is_schoolenter'] = '0';
            $data['is_gmctocrmschool'] = '1';
            $data['is_gmcdirectschool'] = $is_gmcdirectschool;
            $data['schoolenter_createtime'] = time();
            $data['schoolenter_updatetime'] = time();
            $this->DataControl->insertData('crm_client_schoolenter', $data);
        }

        /**解除其他学校负责人记录    集团的要排除在外**/
        $principal = array();
        $principal['principal_leave'] = '1';
        $principal['principal_updatatime'] = time();
        $this->DataControl->updateData("crm_client_principal", "client_id='{$client_id}' AND principal_leave = '0' and school_id <> '{$school_id}' and school_id <> '0' ", $principal);

        $allotlog = array();
        $allotlog['allotlog_status'] = '0';
        $allotlog['allotlog_removetime'] = time();
        $allotlog['allotlog_note'] = '集团邀约其他学校，解除非目标校负责人信息';
        $this->DataControl->updateData("crm_client_allotlog", "client_id='{$client_id}' AND allotlog_status = '1' and school_id <> '{$school_id}' and school_id <> '0' ", $allotlog);
        return true;
    }

    /**
     * 跟进-提交
     * @param $paramArray
     * @return bool
     */
    function insertTrackClientOne($paramArray)
    {
//        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0) {
//            $data = array();
//            $data['client_id'] = $paramArray['client_id'];
//            $data['school_id'] = $paramArray['school_id'];
//            $marketerOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id = '{$paramArray['staffer_id']}' and company_id = '{$paramArray['company_id']}'");
//            if (!$marketerOne) {
//                $addmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
//                $data['marketer_id'] = $addmarkertOne['marketer_id'];
//                $data['marketer_name'] = $addmarkertOne['marketer_name'];
//            } else {
//                $data['marketer_id'] = $marketerOne['marketer_id'];
//                $data['marketer_name'] = $marketerOne['marketer_name'];
//            }
//            $data['track_note'] = $this->LgStringSwitch("未接通");
//            $data['track_isactive'] = 1;
//            $data['track_createtime'] = time();
//            if ($this->DataControl->insertData('crm_client_track', $data)) {
//                $dataClient = array();
//                $dataClient['client_answerphone'] = 0;
//                $dataClient['client_updatetime'] = time();
//                if ($this->DataControl->updateData("crm_client", "client_id = '{$paramArray['client_id']}'", $dataClient)) {
//                    $this->error = 0;
//                    $this->errortip = "集团跟进成功";
//                    return true;
//                } else {
//                    $this->error = 1;
//                    $this->errortip = "更新客户状态失败";
//                    return false;
//                }
//            } else {
//                $this->error = 1;
//                $this->errortip = "插入跟踪记录失败";
//                return false;
//            }
//        }

        if ($paramArray['track_followmode'] == "") {
            $this->error = 1;
            $this->errortip = "请选择跟进类型";
            return false;
        }
        $clientOne = $this->DataControl->selectOne("select client_cnname,client_enname,client_tracestatus,client_mobile,client_intention_maxlevel from crm_client where client_id='{$paramArray['client_id']}'");
//        if ($clientOne['client_tracestatus'] == '-1') {
//            $this->error = 1;
//            $this->errortip = "无意向名单,无法继续跟进";
//            return false;
//        }
//        if ($clientOne['client_tracestatus'] == '-2') {
//            $this->error = 1;
//            $this->errortip = "无效名单,无法继续跟进";
//            return false;
//        }

//        //产品确认：戚总说过如果这个名单有邀约柜询或者试听后（只要有邀约记录不管是否到访），名单就不可跟进为无效名单，需要限制
//        if ($paramArray['track_followmode'] == '-2') {
//            $auditionOne = $this->DataControl->selectOne("select a.audition_id from crm_client_schoolenter as s
//                    left join crm_client_audition as a ON a.client_id = s.client_id
//                    where s.client_id='{$paramArray['client_id']}' and s.is_enterstatus = '1' and a.school_id = s.school_id   ");
//            $inviteOne = $this->DataControl->selectOne("select i.invite_id from crm_client_schoolenter as s
//                    left join  crm_client_invite as i ON i.client_id = s.client_id
//                    where s.client_id='{$paramArray['client_id']}' and s.is_enterstatus = '1' and i.school_id = s.school_id  ");
//            if (is_array($auditionOne) || is_array($inviteOne)) {
//                $this->error = 1;
//                $this->errortip = "已存在柜询/试听,无法设置为无效名单";
//                return false;
//            }
//        }

        $marketerOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer
        where staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
        if (!$marketerOne) {
            $addmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $marketerOne['staffer_id'] = $addmarkertOne['staffer_id'];
            $marketerOne['marketer_id'] = $addmarkertOne['marketer_id'];
            $marketerOne['marketer_name'] = $addmarkertOne['marketer_name'];
        }

        $data = array();
        $data['client_id'] = $paramArray['client_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['coursetype_id'] = $paramArray['coursetype_id'];//班组ID
        $data['coursecat_id'] = $paramArray['coursecat_id'];//班种ID
        $data['marketer_id'] = $marketerOne['marketer_id'];
        $data['marketer_name'] = $marketerOne['marketer_name'];
        $data['track_linktype'] = $paramArray['track_linktype'];
        $data['track_validinc'] = $paramArray['track_validinc'];
        $data['track_followuptype'] = $paramArray['track_followuptype'];
        $data['track_followuptime'] = $paramArray['track_followuptime'];
        $data['track_followmode'] = $paramArray['track_followmode'];
        $data['track_intention_level'] = $paramArray['track_intention_level'];
        $data['track_note'] = $paramArray['track_note'];
        $data['object_code'] = $paramArray['object_code'];
        $data['track_invalidreason'] = $paramArray['invalidnote_reason'];
        $data['track_createtime'] = time();
        $data['track_type'] = 1;
        $data['track_isgmcactive'] = 1; //是否集团主动跟进
        if ($paramArray['track_followmode'] == -1) {
            $data['track_state'] = -1;
        } else if ($paramArray['track_followmode'] == -2) {
            $data['track_followmode'] = -3;
            $data['track_state'] = -2;
        } else if ($data['track_followmode'] == 1) {
            $data['track_visitingtime'] = $paramArray['invite_visittime'];
//            //产品确认不需要校验   20210601
            if ($this->checkIsinviteLog($paramArray['client_id'], $paramArray['invite_visittime'], $paramArray['school_id'], 'i')) {
                $this->error = 1;
                $this->errortip = "该客户今日已有本校柜询邀约记录,请选择其他日期";
                return false;
            }
        } elseif ($data['track_followmode'] == 2) {
            $data['track_visitingtime'] = $paramArray['audition_visittime'];
//            //产品确认不需要校验   限制课时  20210601
            if ($this->checkAuditionLog($paramArray['client_id'], $paramArray['audition_visittime'], $paramArray['school_id'], $paramArray['coursetype_id'])) {
                $this->error = 1;
                $this->errortip = "该客户今日已有本校同班组试听邀约记录，请选择其他日期！";
                return false;
            }
        }


        $sql = "SELECT
                    a.audition_id as visit_id,a.coursetype_id
                FROM
                    crm_client_audition a
                WHERE
                    a.client_id = '{$paramArray['client_id']}'
                AND a.audition_isvisit = '0'
                UNION ALL
                SELECT
                    i.invite_id as visit_id,i.coursetype_id
                FROM
                    crm_client_invite i
                WHERE
                    i.client_id = '{$paramArray['client_id']}'
                AND i.invite_isvisit = '0'";
        if ($data['track_followmode'] == 1) {
            if ($this->DataControl->selectOne($sql)) {//l.school_id <> '{$paramArray['school_id']}' AND
                $this->error = 1;
                $this->errortip = "客户存在柜询/试听到访待确认信息，请联系校区确认后，再进行跟进！";
                return false;
            }
        } elseif ($data['track_followmode'] == 2) {
            if (isset($paramArray['class_id']) && $paramArray['class_id'] !== '') {
                if ($this->DataControl->selectOne($sql." HAVING coursetype_id = '{$paramArray['coursetype_id']}'")) {//l.school_id <> '{$paramArray['school_id']}' AND
                    $this->error = 1;
                    $this->errortip = "客户与同班组已存在试听到访待确认信息，请联系校区确认后，再进行试听跟进！";
                    return false;
                }
            } else {
                if ($this->DataControl->selectOne($sql)) {//l.school_id <> '{$paramArray['school_id']}' AND
                    $this->error = 1;
                    $this->errortip = "客户存在柜询/试听到访待确认信息，请联系校区确认后，再进行试听跟进！";
                    return false;
                }
            }
        } elseif ($data['track_followmode'] == -1) {
            if ($this->DataControl->selectOne($sql)) {//l.school_id = '{$paramArray['school_id']}' AND
                $this->error = 1;
                $this->errortip = "客户存在柜询/试听到访待确认信息，请联系校区确认后，再进行无意向设置！";
                return false;
            }
        } elseif ($data['track_followmode'] == -3) {
            if ($this->DataControl->selectOne($sql)) {//l.school_id = '{$paramArray['school_id']}' AND
                $this->error = 1;
                $this->errortip = "客户存在柜询/试听到访待确认信息，请联系校区确认后，再进行无效设置！";
                return false;
            }
        }


        if (!$track_id = $this->DataControl->insertData('crm_client_track', $data)) {
            $this->error = 1;
            $this->errortip = "跟踪记录失败";
            return false;
        } else {
            //判断是不是当前的这个学校
            $nowSchoolenter = $this->DataControl->getFieldOne('crm_client_schoolenter', 'schoolenter_id,school_id', "client_id='{$paramArray['client_id']}' and is_enterstatus='1'");

            //判定名单是否在校区进行进行重新分配
            if ($paramArray['school_id'] > 0) {
                $this->AddClientschool($paramArray['client_id'], $paramArray['school_id'], $paramArray['company_id']);
            }

            if ($data['track_followmode'] == 1) {
                //柜询记录
                $dataLInvite = array();
                $dataLInvite['client_id'] = $paramArray['client_id'];
                $dataLInvite['marketer_id'] = $marketerOne['marketer_id'];
                $dataLInvite['company_id'] = $paramArray['company_id'];
                $dataLInvite['school_id'] = $paramArray['school_id'];
                $dataLInvite['coursetype_id'] = $paramArray['coursetype_id'];
                $dataLInvite['coursecat_id'] = $paramArray['coursecat_id'];
                $dataLInvite['invite_level'] = $paramArray['track_intention_level'];
                $dataLInvite['invite_visittime'] = $paramArray['invite_visittime'];
                $dataLInvite['receiver_name'] = $paramArray['receiver_name'];
                $dataLInvite['invite_genre'] = intval($paramArray['invite_genre']);
                $dataLInvite['invite_createtime'] = $data['track_createtime'];
                $dataLInvite['track_id'] = $track_id;
                if ($dataLInvite['invite_genre'] == '2' || $dataLInvite['invite_genre'] == '3') {
                    $dataLInvite['invite_isvisit'] = 1;
                }
                if (!$this->DataControl->insertData("crm_client_invite", $dataLInvite)) {
                    $this->error = 1;
                    $this->errortip = "柜询邀约失败";
                    return false;
                }
                if ($dataLInvite['invite_genre'] == '2' || $dataLInvite['invite_genre'] == '3') {
                    $dataTrack = array();
                    $dataTrack['school_id'] = 0;
                    $dataTrack['client_id'] = $paramArray['client_id'];
                    $dataTrack['marketer_id'] = $marketerOne['marketer_id'];
                    $dataTrack['marketer_name'] = "系统自动确认";
                    $dataTrack['track_isgmcactive'] = 1;
                    $dataTrack['track_validinc'] = 1;
                    $dataTrack['track_createtime'] = time();
                    $clientData = array();
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("确认柜询");
                    $dataTrack['track_note'] = $this->LgStringSwitch("确认柜询成功");
                    $dataTrack['track_note'] .= $this->LgStringSwitch("(集团操作)");
                    if ($clientOne['client_tracestatus'] < 2) {
                        $clientData['client_tracestatus'] = '2';
                    }
                    $this->DataControl->insertData("crm_client_track", $dataTrack);

//                    $clientData['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
                    $clientData['client_updatetime'] = time();
                    $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);
                }

                //意向课程
                if ($paramArray['coursetype_id'] !== '' && $paramArray['coursetype_id'] !== '0' && $paramArray['coursecat_id'] !== '' && $paramArray['coursecat_id'] !== '0') {
                    if (!$this->DataControl->getFieldOne("crm_client_intention", "intention_id",
                        "client_id = '{$paramArray['client_id']}' AND coursecat_id = '{$paramArray['coursecat_id']}'")) {
                        $intention = array();
                        $intention['client_id'] = $paramArray['client_id'];
                        $intention['coursetype_id'] = $paramArray['coursetype_id'];
                        $intention['coursecat_id'] = $paramArray['coursecat_id'];
                        $intention['intention_updatetime'] = time();
                        $this->DataControl->insertData("crm_client_intention", $intention);
                    }
                }

                if ($paramArray['company_id'] = '8888') {
                    //短信通知
                    $minsendModel = new \Model\Api\SmsModel($paramArray);
                    $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_cnname", "company_id = '{$paramArray['company_id']}'");
                    $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_cnname", "coursetype_id = '{$paramArray['coursetype_id']}'");
                    $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_cnname,school_address,school_phone", "school_id = '{$paramArray['school_id']}'");
                    $mistext = "{$clientOne['client_cnname']}您好！{$companyOne['company_cnname']}已为您安排{$coursetypeOne['coursetype_cnname']}课程咨询时间定于{$paramArray['invite_visittime']}，地址：{$schoolOne['school_address']}，联系电话：{$schoolOne['school_phone']}，现场请出示此邀约短信。";
                    $minsendModel->crmMisSend($clientOne['client_mobile'], $mistext, '柜询邀约', date("m-d"));

                    //通知学校
                    /**获取负责人**/
                    $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_principal AS p ,smc_staffer as s  
WHERE p.marketer_id = m.marketer_id AND p.principal_leave = '0' AND p.client_id = '{$clientOne['client_id']}' AND p.school_id = '{$paramArray['school_id']}' and m.staffer_id = s.staffer_id and s.staffer_leave = '0' ORDER BY p.principal_ismajor DESC limit 0,1");
                    if ($allotlogList) {
                        $publicarray = array();
                        $publicarray['company_id'] = $paramArray['company_id'];
                        $minsendModel = new \Model\Api\SmsModel($publicarray);
                        foreach ($allotlogList as $allotlogOne) {
                            $mistext = "您好，{$schoolOne['school_cnname']},CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$paramArray['invite_visittime']}柜询，请注意安排招待！";
                            $minsendModel->gmcMisSend($allotlogOne['marketer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                        }
                    } else {
                        /**获取校长信息**/
                        $allotlogList = $this->DataControl->selectClear("SELECT f.staffer_mobile FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS pt ON pt.post_id = p.post_id LEFT JOIN smc_staffer AS f ON f.staffer_id = p.staffer_id
WHERE p.postbe_status = '1' AND pt.post_istopjob = '1' AND p.school_id = '{$paramArray['school_id']}' AND p.company_id = '{$paramArray['company_id']}' and f.staffer_leave = '0' AND p.postbe_iscrmuser = '1' AND pt.post_name LIKE '%校长%'
GROUP BY f.staffer_mobile");
                        if ($allotlogList) {
                            $publicarray = array();
                            $publicarray['company_id'] = $paramArray['company_id'];
                            $minsendModel = new \Model\Api\SmsModel($publicarray);
                            foreach ($allotlogList as $allotlogOne) {
                                $mistext = "您好，{$schoolOne['school_cnname']},CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$paramArray['invite_visittime']}柜询，请注意安排招待！";
                                $minsendModel->gmcMisSend($allotlogOne['staffer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                            }
                        }
                    }
//                //微信推送
//                $wxSaleseModel = new \Model\Api\ThreeApiModel($paramArray);
//                $wxSaleseModel->wxSaleseClientRemind($clientOne['client_id'], $schoolOne['school_id'], $paramArray['invite_visittime']);
                }


                $dataClient = array();
                $dataClient['client_isgross'] = 0;//不是毛名单
                if ($dataLInvite['invite_genre'] != '2' && $dataLInvite['invite_genre'] != '3') {
                    if ($clientOne['client_tracestatus'] < 1 || $nowSchoolenter['school_id'] <> $paramArray['school_id']) {//其中之一  ： 更换学校
                        $dataClient['client_tracestatus'] = 1;
                    }
                }
                if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                    $dataClient['client_intention_maxlevel'] = $paramArray['track_intention_level'];
                }
                $dataClient['client_distributionstatus'] = 0;
                $dataClient['client_isnewtip'] = 1;
                $dataClient['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
                $dataClient['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id={$paramArray['client_id']}", $dataClient);
            } elseif ($data['track_followmode'] == 2) {
                //试听记录
                $dataAudition = array();
                $dataAudition['client_id'] = $paramArray['client_id'];
                $dataAudition['marketer_id'] = $marketerOne['marketer_id'];
                $dataAudition['company_id'] = $paramArray['company_id'];
                $dataAudition['school_id'] = $paramArray['school_id'];
                $dataAudition['coursetype_id'] = $paramArray['coursetype_id'];
                $dataAudition['coursecat_id'] = $paramArray['coursecat_id'];
                $dataAudition['course_id'] = $paramArray['course_id'];
                $dataAudition['class_id'] = $paramArray['class_id'];
                $dataAudition['hour_id'] = $paramArray['hour_id'];
                $dataAudition['audition_genre'] = $paramArray['audition_genre'];
                $dataAudition['audition_visittime'] = $paramArray['audition_visittime'];
                $dataAudition['receiver_name'] = $paramArray['receiver_name'];
                $dataAudition['audition_createtime'] = $data['track_createtime'];
                $dataAudition['track_id'] = $track_id;
                if (isset($paramArray['class_cnname']) && $paramArray['class_cnname'] !== "") {
                    $dataAudition['class_cnname'] = $paramArray['class_cnname'];
                } else {
                    $classOne = $this->DataControl->getFieldOne("smc_class", "class_cnname", "class_id='{$paramArray['class_id']}'");
                    if (!$classOne) {
                        $classOne = array();
                    }
                    $dataAudition['class_cnname'] = $classOne['class_cnname'];
                }
                if (!$this->DataControl->insertData("crm_client_audition", $dataAudition)) {
                    $this->error = 1;
                    $this->errortip = "插入试听记录失败";
                    return false;
                }

                //意向课程
                if ($paramArray['coursetype_id'] !== '' && $paramArray['coursetype_id'] !== '0' && $paramArray['coursecat_id'] !== '' && $paramArray['coursecat_id'] !== '0') {
                    if (!$this->DataControl->getFieldOne("crm_client_intention", "intention_id",
                        "client_id = '{$paramArray['client_id']}' AND coursecat_id = '{$paramArray['coursecat_id']}'")) {
                        $intention = array();
                        $intention['client_id'] = $paramArray['client_id'];
                        $intention['coursetype_id'] = $paramArray['coursetype_id'];
                        $intention['coursecat_id'] = $paramArray['coursecat_id'];
                        $intention['intention_updatetime'] = time();
                        $this->DataControl->insertData("crm_client_intention", $intention);
                    }
                }


                if ($paramArray['company_id'] = '8888') {
                    //试听邀约
                    $minsendModel = new \Model\Api\SmsModel($paramArray);
                    $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_cnname", "company_id = '{$paramArray['company_id']}'");
                    $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_cnname", "coursetype_id = '{$paramArray['coursetype_id']}'");
                    $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_address,school_phone", "school_id = '{$paramArray['school_id']}'");
                    $mistext = "{$clientOne['client_cnname']}您好！{$companyOne['company_cnname']}已为您安排{$coursetypeOne['coursetype_cnname']}试听课时间定于{$paramArray['audition_visittime']}，地址：{$schoolOne['school_address']}，联系电话：{$schoolOne['school_phone']}，现场请出示此邀约短信。";
                    $minsendModel->crmMisSend($clientOne['client_mobile'], $mistext, '试听邀约', date("m-d"));


                    //通知学校
                    /**获取负责人**/
                    $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_principal AS p ,smc_staffer as s 
WHERE p.marketer_id = m.marketer_id AND p.principal_leave = '0' AND p.client_id = '{$clientOne['client_id']}' AND p.school_id = '{$paramArray['school_id']}' and m.staffer_id = s.staffer_id and s.staffer_leave = '0' ORDER BY p.principal_ismajor DESC limit 0,1");
                    if ($allotlogList) {
                        $publicarray = array();
                        $publicarray['company_id'] = $paramArray['company_id'];
                        $minsendModel = new \Model\Api\SmsModel($publicarray);
                        foreach ($allotlogList as $allotlogOne) {
                            $mistext = "您好，{$schoolOne['school_cnname']}CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$paramArray['audition_visittime']}公开课，请注意安排招待！";
                            $minsendModel->gmcMisSend($allotlogOne['marketer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                        }
                    } else {
                        /**获取校长信息**/
                        $allotlogList = $this->DataControl->selectClear("SELECT f.staffer_mobile FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS pt ON pt.post_id = p.post_id LEFT JOIN smc_staffer AS f ON f.staffer_id = p.staffer_id
WHERE p.postbe_status = '1' AND pt.post_istopjob = '1' AND p.school_id = '{$paramArray['school_id']}' AND p.company_id = '{$paramArray['company_id']}' and f.staffer_leave = '0' AND p.postbe_iscrmuser = 1 AND pt.post_name LIKE '%校长%'
GROUP BY f.staffer_mobile");
                        if ($allotlogList) {
                            $publicarray = array();
                            $publicarray['company_id'] = $paramArray['company_id'];
                            $minsendModel = new \Model\Api\SmsModel($publicarray);
                            foreach ($allotlogList as $allotlogOne) {
                                $mistext = "您好，{$schoolOne['school_cnname']}CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$paramArray['audition_visittime']}公开课，请注意安排招待！";
                                $minsendModel->gmcMisSend($allotlogOne['staffer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                            }
                        }
                    }
//                //微信推送
//                $wxSaleseModel = new \Model\Api\ThreeApiModel($paramArray);
//                $wxSaleseModel->wxSaleseClientRemind($clientOne['client_id'], $schoolOne['school_id'], $paramArray['audition_visittime']);
                }


                $dataClient = array();
                $dataClient['client_isgross'] = 0;//不是毛名单
                if ($clientOne['client_tracestatus'] < 1 || $nowSchoolenter['school_id'] <> $paramArray['school_id']) {//其中之一  ： 更换学校
                    $dataClient['client_tracestatus'] = 1;
                }
                if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                    $dataClient['client_intention_maxlevel'] = $paramArray['track_intention_level'];
                }
                $dataClient['client_distributionstatus'] = 0;
                $dataClient['client_isnewtip'] = 1;
                $dataClient['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
                $dataClient['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id={$paramArray['client_id']}", $dataClient);
                if ($paramArray['class_id'] && $paramArray['hour_id']) {
                    $smcData = array();
                    $smcData['company_id'] = $paramArray['company_id'];
                    $smcData['school_id'] = $paramArray['school_id'];
                    $smcData['hour_id'] = $paramArray['hour_id'];
                    $smcData['class_id'] = $paramArray['class_id'];
                    $smcData['client_id'] = $paramArray['client_id'];
                    $smcData['audition_cnname'] = $clientOne['client_cnname'];
                    $smcData['audition_enname'] = $clientOne['client_enname'];
                    $this->DataControl->insertData('smc_class_hour_audition', $smcData);
                }
            } elseif ($data['track_followmode'] == 3) {
                $this->error = 1;
                $this->errortip = "转正操作未开放";
                return false;
            } elseif ($data['track_followmode'] == -1) {
                //流失 确认无意向
                if ($clientOne['client_tracestatus'] == 4) {
                    $this->error = 1;
                    $this->errortip = "该客户已登记学号,无法设置无意向名单";
                    return false;
                }

                $dataClient = array();
                if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                    $dataClient['client_intention_maxlevel'] = $paramArray['track_intention_level'];
                }

                //未审核不接触负责人状态
                $prinData = $this->DataControl->selectClear("select principal_id,client_id,principal_ismajor,marketer_id from crm_client_principal where principal_leave = 0 and client_id='{$paramArray['client_id']}' ");
                if ($prinData) {
                    $palData = array();
                    $palData['principal_leave'] = 1;
                    $palData['principal_updatatime'] = time();
                    foreach ($prinData as $val) {
                        $this->DataControl->updateData('crm_client_principal', "principal_id='{$val['principal_id']}'", $palData);
                        $dataAllotlog = array();
                        $dataAllotlog['allotlog_status'] = 0;
                        $dataAllotlog['allotlog_removetime'] = time();
                        $this->DataControl->updateData('crm_client_allotlog', "allotlog_status = 1 and client_id='{$paramArray['client_id']}' and allot_marketer_id='{$val['marketer_id']}'", $dataAllotlog);//and school_id='{$paramArray['school_id']}'
                    }
                }

                $dataClient['client_tracestatus'] = -1;
                $dataClient['client_gmcdistributionstatus'] = 0;
                $dataClient['client_lapsedtype'] = $paramArray['client_lapsedtype'];
//                $dataClient['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
                $dataClient['client_updatetime'] = time();
                if (!$this->DataControl->updateData("crm_client", "client_id={$paramArray['client_id']}", $dataClient)) {
                    $this->error = 1;
                    $this->errortip = "更新无意向客户状态失败";
                    return false;
                }
            } elseif ($data['track_followmode'] == -3) {
                //无效名单
                if ($clientOne['client_tracestatus'] == 4) {
                    $this->error = 1;
                    $this->errortip = "该客户已登记学号,无法设置无效名单";
                    return false;
                }

                $dataClient = array();
                $dataClient['client_tracestatus'] = -2;
                $dataClient['client_gmcdistributionstatus'] = 0;

                $dataClient['client_isinvalidreview'] = 0;
                $dataClient['invalidnote_code'] = $paramArray['invalidnote_code'];
//                $dataClient['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
                $dataClient['client_updatetime'] = time();
                if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                    $dataClient['client_intention_maxlevel'] = $paramArray['track_intention_level'];
                }

                //未审核不接触负责人状态
                $prinData = $this->DataControl->selectClear("select principal_id,client_id,principal_ismajor,marketer_id from crm_client_principal
                where principal_leave = 0 and client_id='{$paramArray['client_id']}' ");
                if ($prinData) {
                    $palData = array();
                    $palData['principal_leave'] = 1;
                    $palData['principal_updatatime'] = time();
                    foreach ($prinData as $val) {
                        $this->DataControl->updateData('crm_client_principal', "principal_id='{$val['principal_id']}'", $palData);
                        $dataAllotlog = array();
                        $dataAllotlog['allotlog_status'] = 0;
                        $dataAllotlog['allotlog_removetime'] = time();
                        $this->DataControl->updateData('crm_client_allotlog', "allotlog_status = 1 and client_id='{$paramArray['client_id']}' and allot_marketer_id='{$val['marketer_id']}'", $dataAllotlog);//and school_id='{$paramArray['school_id']}'
                    }
                }
                if (!$this->DataControl->updateData("crm_client", "client_id={$paramArray['client_id']}", $dataClient)) {
                    $this->error = 1;
                    $this->errortip = "更新无效客户状态失败";
                    return false;
                }
            } elseif ($data['track_followmode'] == 0) {
                //意向课程
                if ($paramArray['coursetype_id'] !== '' && $paramArray['coursetype_id'] !== '0' && $paramArray['coursecat_id'] !== '' && $paramArray['coursecat_id'] !== '0') {
                    if (!$this->DataControl->getFieldOne("crm_client_intention", "intention_id",
                        "client_id = '{$paramArray['client_id']}' AND coursecat_id = '{$paramArray['coursecat_id']}'")) {
                        $intention = array();
                        $intention['client_id'] = $paramArray['client_id'];
                        $intention['coursetype_id'] = $paramArray['coursetype_id'];
                        $intention['coursecat_id'] = $paramArray['coursecat_id'];
                        $intention['intention_updatetime'] = time();
                        $this->DataControl->insertData("crm_client_intention", $intention);
                    }
                }

                //持续跟踪 --普通跟进
                $dataClient = array();
                if ($clientOne['client_tracestatus'] < 1) {
                    $dataClient['client_tracestatus'] = 1;
                }
                if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                    $dataClient['client_intention_maxlevel'] = $paramArray['track_intention_level'];
                }
//                $dataClient['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
                $dataClient['client_updatetime'] = time();
                if (!$this->DataControl->updateData("crm_client", "client_id={$paramArray['client_id']}", $dataClient)) {
                    $this->error = 1;
                    $this->errortip = "更新持续跟踪客户状态失败";
                    return false;

                }
            } else {
                $this->error = 1;
                $this->errortip = "跟进模式错误";
                return false;
            }

            $leave = array();
            if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                $leave['client_intention_maxlevel'] = $paramArray['track_intention_level'];
            }
            $leave['client_isnewtip'] = '1';
            $leave['client_intention_level'] = $data['track_intention_level'];
//            $leave['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
            $leave['client_updatetime'] = time();
            $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $leave);


            if ($paramArray['track_followuptype'] == 1) {
                $remindData = array();
                $remindData['school_id'] = $paramArray['school_id'];
                $remindData['company_id'] = $paramArray['company_id'];
                $remindData['marketer_id'] = $marketerOne['marketer_id'];
                $remindData['client_id'] = $paramArray['client_id'];
                $remindData['remind_time'] = date("Y-m-d", strtotime("{$paramArray['track_followuptime']}"));
                $remindData['remind_remark'] = $this->LgStringSwitch("系统:跟进提醒");
                $remindData['remind_createtime'] = time();

                if (!$this->DataControl->insertData('gmc_client_remind', $remindData)) {
                    $this->error = 1;
                    $this->errortip = "新增提醒记录失败";
                    return false;
                }
            }

//            $crmData = array();
//            $crmData['client_isnewtip'] = '1';
//            $crmData['client_answerphone'] = 1;//是否接通电话   0 未接通  1 已接通
//            $crmData['client_updatetime'] = time();
//            if (isset($paramArray['track_intention_level'])) {
//                $crmData['client_intention_level'] = $paramArray['track_intention_level'];
//            }
//            $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $crmData);
//
//            //给名单负责人增加事件提醒
//            $marketerList = $this->DataControl->selectClear("SELECT a.school_id,a.marketer_id FROM crm_client_principal AS a
//WHERE a.client_id = '{$paramArray['client_id']}' AND a.principal_leave = '0'");
//            if ($marketerList) {
//                foreach ($marketerList as $marketerOne) {
//                    $datas = array();
//                    $datas['company_id'] = $paramArray['company_id'];
//                    $datas['school_id'] = $marketerOne['school_id'];
//                    $datas['marketer_id'] = $marketerOne['marketer_id'];
//                    $datas['client_id'] = $paramArray['client_id'];
//                    $datas['event_tag'] = '中心提醒';
//                    $datas['event_remark'] = "由集团管理中心招生人员发起招生提醒！";
//                    $datas['event_time'] = date("Y-m-d");
//                    $datas['event_createtime'] = time();
//                    $this->DataControl->insertData('crm_event', $datas);
//                }
//            }
            //$this->wxClientUpdateRemind($paramArray['client_id']);
            $this->error = 0;
            $this->errortip = "集团跟进成功";
            return true;

        }

    }

    /**
     * 集团批量跟进-提交
     * @param $paramArray track_followmode
     * @param $paramArray clients_json
     * @param $paramArray track_linktype
     * @param $paramArray track_validinc
     * @param $paramArray track_followuptype
     * @param $paramArray track_followuptime
     * @param $paramArray track_intention_level
     * @param $paramArray track_note
     * @param $paramArray object_code
     * @return bool
     */
    function followUpClients($paramArray)
    {
        if ($paramArray['track_followmode'] == "") {
            $this->error = 1;
            $this->errortip = "请选择跟进类型";
            return false;
        }

        $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
        $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$paramArray['staffer_id']}'");
        if (!$marOne) {
            $datas = array();
            $datas['company_id'] = $staffer['company_id'];
            $datas['staffer_id'] = $staffer['staffer_id'];
            $datas['postrole_id'] = $staffer['postrole_id'];
            $datas['marketer_istest'] = $staffer['staffer_istest'];
            $datas['marketer_name'] = $staffer['staffer_cnname'];
            $datas['marketer_img'] = $staffer['staffer_img'];
            $datas['marketer_mobile'] = $staffer['staffer_mobile'];
            $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
            $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
            $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
            $datas['marketer_lastip'] = $staffer['staffer_lastip'];
            $datas['marketer_createtime'] = $staffer['staffer_createtime'];
            $datas['marketer_status'] = '1';
            $id = $this->DataControl->insertData('crm_marketer', $datas);
            $marOne['marketer_id'] = $id;
        }

        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        } else {
            foreach ($clients_array as $key => $value) {
                $data = array();
                $data['client_id'] = $value['client_id'];
                $data['marketer_id'] = $marOne['marketer_id'];
                $data['marketer_name'] = $staffer['staffer_cnname'];
                $data['track_linktype'] = $paramArray['track_linktype'];
                $data['track_validinc'] = $paramArray['track_validinc'];
                $data['track_followuptype'] = $paramArray['track_followuptype'];
                $data['track_followuptime'] = $paramArray['track_followuptime'];
                $data['track_followmode'] = $paramArray['track_followmode'];
                $data['track_intention_level'] = $paramArray['track_intention_level'];
                $data['track_note'] = '集团跟踪:' . $paramArray['track_note'];
                $data['object_code'] = $paramArray['object_code'];
                $data['track_createtime'] = time();
                $data['track_type'] = 1;
                $data['track_initiative'] = 0;
                if (!$track_id = $this->DataControl->insertData('crm_client_track', $data)) {
                    $this->error = 1;
                    $this->errortip = "跟进失败!";
                    return false;
                } else {

                    $crmData = array();
                    $crmData['client_isnewtip'] = '1';
                    $crmData['client_updatetime'] = time();
                    if (isset($paramArray['track_intention_level'])) {
                        $crmData['client_intention_level'] = $paramArray['track_intention_level'];
                    }
                    $clientOne = $this->DataControl->getFieldOne("crm_client", "client_intention_maxlevel", "client_id='{$value['client_id']}'");
                    if ($paramArray['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                        $crmData['client_intention_maxlevel'] = $paramArray['track_intention_level'];
                    }
                    $this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $crmData);

                    //给名单负责人增加事件提醒
                    $marketerList = $this->DataControl->selectClear("
SELECT a.school_id,a.marketer_id 
FROM crm_client_principal AS a
WHERE a.client_id = '{$value['client_id']}' AND a.principal_leave = '0'
");
                    if ($marketerList) {
                        foreach ($marketerList as $marketerOne) {
                            $datas = array();
                            $datas['company_id'] = $paramArray['company_id'];
                            $datas['school_id'] = $marketerOne['school_id'];
                            $datas['marketer_id'] = $marketerOne['marketer_id'];
                            $datas['client_id'] = $value['client_id'];
                            $datas['event_tag'] = '中心提醒';
                            $datas['event_remark'] = "由集团管理中心招生人员发起招生提醒！";
                            $datas['event_time'] = date("Y-m-d");
                            $datas['event_createtime'] = time();
                            $this->DataControl->insertData('crm_event', $datas);

                        }

                    }
                    $this->wxClientUpdateRemind($value['client_id']);

                }

            }
            $this->error = 0;
            $this->errortip = "集团跟进成功";
            return true;

        }

    }

    /**
     * 微信提醒 -跟进提醒
     * author: ling
     * 对应接口文档 0001
     */
    function wxClientUpdateRemind($client_id)
    {
        $prinList = $this->DataControl->selectClear(
            "select p.client_id,f.staffer_id,p.school_id,t.client_cnname,t.client_mobile,t.company_id,t.client_id
            from  crm_client_principal as p
            left join crm_client as t On t.client_id=p.client_id 
            left join crm_marketer as k On p.marketer_id =k.marketer_id 
            left join smc_staffer as f ON k.staffer_id=f.staffer_id  
            where f.staffer_wxtoken <> '' and p.client_id ='{$client_id}' and principal_leave =0  "
        );
        if ($prinList) {
            $week = $this->LgArraySwitch(array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六'));
            foreach ($prinList as $value) {
                $isset = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '{$value['company_id']}' and masterplate_name = '客户跟进通知' and masterplate_class = '0'");
                if ($isset) {
                    $wxid = $isset['masterplate_wxid'];
                } else {
                    $masterplate = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_wxid", "company_id = '0' and masterplate_name = '客户跟进通知' and masterplate_class = '0'");
                    $wxid = $masterplate['masterplate_wxid'];
                }
                $wxteModel = new \Model\Api\ZjwxChatModel($value['staffer_id'], $value['school_id']);
                $firstnote = "亲爱的招生老师你好，意向客户{$value['client_cnname']}的信息已更新，请了解最新信息喔~";
                $keyword1 = $value['client_cnname'];
                $keyword2 = $value['client_mobile'];
                $keyword3 = date("Y-m-d") . ' ' . $week[date('w')] . ' ' . date("H:i");
                $footernote = "请了解最新信息，点击查看详情查看名单信息";
                $url = "https://tesc.kedingdang.com/crmIndex/IntendedCustomerDetails?client_id={$value['client_id']}";
                $wxteModel->NoticeFollow($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid);
            }

        }

    }

//下载导入模版
    function getImportApi($paramArray)
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/Clientdemo.xlsx';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        return $res;
    }

//招生券管理列表
    function enrollTicketList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.freevoucher_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['freevoucher_isuse']) && $paramArray['freevoucher_isuse'] !== "") {
            $datawhere .= " and t.freevoucher_isuse ='{$paramArray['freevoucher_isuse']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.client_id,c.client_cnname,t.freevoucher_id,t.freevoucher_pid,t.freevoucher_name,t.freevoucher_starttime,t.freevoucher_exittime,t.freevoucher_isuse,t.freevoucher_usetime,from_unixtime(t.freevoucher_usetime,'%Y-%m-%d') as freevoucher_usetime,from_unixtime(t.freevoucher_createtime,'%Y-%m-%d') as freevoucher_createtime from crm_sell_activity_freevoucher as t left join crm_client as c on c.client_id = t.client_id WHERE {$datawhere} and t.activity_id = '{$paramArray['activity_id']}' and school_id = '{$paramArray['school_id']}' order by t.freevoucher_id DESC LIMIT {$pagestart},{$num}";

        $coursetimesList = $this->DataControl->selectClear($sql);

        if ($coursetimesList) {
            foreach ($coursetimesList as &$val) {
                if ($val['freevoucher_isuse'] == '0') {
                    $val['client_cnname'] = '- - -';
                    $val['freevoucher_usetime'] = '- - -';
                } else {
                    $val['client_cnname'] = $val['client_cnname'];
                    $val['freevoucher_usetime'] = $val['freevoucher_usetime'];
                }
                $val['time'] = $val['freevoucher_starttime'] . '~' . $val['freevoucher_exittime'];
                $status = $this->LgArraySwitch(array("0" => "未使用", "1" => "已使用"));
                $val['freevoucher_isuse'] = $status[$val['freevoucher_isuse']];
            }

        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(t.freevoucher_id)
            FROM
                crm_sell_activity_freevoucher AS t
            WHERE
                {$datawhere} and t.activity_id = '{$paramArray['activity_id']}' and school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0][0];

        if ($paramArray['import'] == '1') {
            if (!$coursetimesList) {
                $this->error = true;
                $this->errortip = "无招生券数据";
                return false;
            }

            $outexceldate = array();
            if ($coursetimesList) {
                $outexceldate = array();
                foreach ($coursetimesList as $dateexcelvar) {
                    $datearray = array();
                    $datearray['freevoucher_name'] = $dateexcelvar['freevoucher_name'];
                    $datearray['freevoucher_pid'] = $dateexcelvar['freevoucher_pid'];
                    $datearray['time'] = $dateexcelvar['time'];
                    $datearray['freevoucher_isuse'] = $dateexcelvar['freevoucher_isuse'];
                    $datearray['freevoucher_usetime'] = $dateexcelvar['freevoucher_usetime'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("招生券名称", "招生券编号", "有效时间", "使用状态", "使用时间", "使用人"));
            $excelfileds = array('freevoucher_name', 'freevoucher_pid', 'time', 'freevoucher_isuse', 'freevoucher_usetime', 'client_cnname');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "招生券.xlsx");

        }

        $fieldstring = array('freevoucher_name ', 'freevoucher_pid', 'time', 'freevoucher_isuse', 'freevoucher_usetime', 'client_cnname');
        $fieldname = $this->LgArraySwitch(array('招生券名称', '招生券编号', '有效时间', '使用状态', '使用时间', '使用人'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;


        if ($coursetimesList) {
            $result['list'] = $coursetimesList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无招生券", 'result' => $result);
        }

        return $res;
    }

//招生券管理列表
    function freevoucherList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_name like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or t.freevoucher_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['freevoucher_name']) && $paramArray['freevoucher_name'] !== "") {
            $datawhere .= " and t.freevoucher_name ='{$paramArray['freevoucher_name']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.client_id,c.client_cnname,c.client_sex,c.client_age,c.client_mobile,t.freevoucher_id,t.freevoucher_pid,t.freevoucher_name,t.freevoucher_starttime,t.freevoucher_exittime,t.freevoucher_isuse,t.freevoucher_usetime,from_unixtime(t.freevoucher_usetime,'%Y-%m-%d') as freevoucher_usetime,from_unixtime(t.freevoucher_createtime,'%Y-%m-%d') as freevoucher_createtime from crm_sell_activity_freevoucher as t left join crm_client as c on c.client_id = t.client_id WHERE {$datawhere} and t.activity_id = '{$paramArray['activity_id']}' and t.freevoucher_isuse = '1' order by t.freevoucher_id DESC LIMIT {$pagestart},{$num}";

        $coursetimesList = $this->DataControl->selectClear($sql);

        if ($coursetimesList) {
            foreach ($coursetimesList as &$val) {
                $val['time'] = $val['freevoucher_starttime'] . '~' . $val['freevoucher_exittime'];
            }

        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(t.freevoucher_id)
            FROM
                crm_sell_activity_freevoucher AS t
            WHERE
                {$datawhere} and t.activity_id = '{$paramArray['activity_id']}' and t.freevoucher_isuse = '1'");
        $allnums = $all_num[0][0];

//        if ($paramArray['import'] == '1') {
//            if (!$coursetimesList) {
//                $this->error = true;
//                $this->errortip = "无招生券数据";
//                return false;
//            }
//
//            $outexceldate = array();
//            if ($coursetimesList) {
//                $outexceldate = array();
//                foreach ($coursetimesList as $dateexcelvar) {
//                    $datearray = array();
//                    $datearray['freevoucher_name'] = $dateexcelvar['freevoucher_name'];
//                    $datearray['freevoucher_pid'] = $dateexcelvar['freevoucher_pid'];
//                    $datearray['time'] = $dateexcelvar['time'];
//                    $datearray['freevoucher_isuse'] = $dateexcelvar['freevoucher_isuse'];
//                    $datearray['freevoucher_usetime'] = $dateexcelvar['freevoucher_usetime'];
//                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
//                    $outexceldate[] = $datearray;
//                }
//            }
//
//            $excelheader = array("招生券名称", "招生券编号", "有效时间", "使用状态", "使用时间", "使用人");
//            $excelfileds = array('freevoucher_name', 'freevoucher_pid', 'time', 'freevoucher_isuse', 'freevoucher_usetime', 'client_cnname');
//            query_to_excel($excelheader, $outexceldate, $excelfileds, "招生券.xlsx");
//
//        }

        $fieldstring = array('client_cnname ', 'client_sex', 'client_age', 'client_mobile', 'freevoucher_name', 'freevoucher_pid', 'time', 'freevoucher_usetime');
        $fieldname = $this->LgArraySwitch(array('姓名', '性别', '年龄', '主要联系电话', '招生券名称', '招生券编号', '有效时间', '使用时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        $result['name'] = $this->DataControl->selectClear("select DISTINCT f.freevoucher_name from crm_sell_activity_freevoucher as f WHERE f.activity_id = '{$paramArray['activity_id']}'");

        if ($coursetimesList) {
            $result['list'] = $coursetimesList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无招生名单", 'result' => $result);
        }

        return $res;
    }

    function createEnrollTicketAction($paramArray)
    {
        for ($i = 0; $i < $paramArray['num']; $i++) {
            $data = array();
            do {
                $couponspid_get = $this->createOrderPid('ZS');
            } while ($this->DataControl->getFieldOne("crm_sell_activity_freevoucher", "freevoucher_id", "freevoucher_pid='{$couponspid_get}'"));
            $data['company_id'] = $paramArray['company_id'];
            $data['activity_id'] = $paramArray['activity_id'];
            $data['freevoucher_pid'] = $couponspid_get;
            $data['freevoucher_name'] = $paramArray['ticket_name'];
            $data['freevoucher_content'] = $paramArray['ticket_note'];
            $data['freevoucher_starttime'] = $paramArray['ticket_starttime'];
            $data['freevoucher_exittime'] = $paramArray['ticket_endtime'];
            $data['freevoucher_createtime'] = time();
            $this->DataControl->insertData("crm_sell_activity_freevoucher", $data);
        }
        $res = array('error' => '0', 'errortip' => "生成招生券成功", 'result' => array());

        return $res;
    }

//招生目标 -- >> 招生活动管理
    function sellActivity($paramArray)
    {
        $datawhere = " 1 ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (a.activity_name like '%{$paramArray['keyword']}%' or m.marketer_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '' && isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $datawhere .= " and a.activity_starttime <= '{$paramArray['activity_endtime']}' and a.activity_endtime >= '{$paramArray['activity_starttime']}'";
        } elseif (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '') {
            $datawhere .= " and a.activity_endtime >= '{$paramArray['activity_starttime']}'";
        } elseif (isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $datawhere .= " and a.activity_starttime <= '{$paramArray['activity_endtime']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and (a.marketer_id = '{$paramArray['found_marketer_id']}' or  s.staffer_id  = '{$paramArray['found_marketer_id']}' )";
        }
        //活动来源  0 自己录入的  1 集团录入的
        if (isset($paramArray['activity_type']) && $paramArray['activity_type'] != '') {
            $datawhere .= " and a.activity_type = '{$paramArray['activity_type']}'";
        }
        //活动来源  0普招模式1验券模式
        if (isset($paramArray['activity_pattern']) && $paramArray['activity_pattern'] != '') {
            $datawhere .= " and a.activity_pattern = '{$paramArray['activity_pattern']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '') {
            $datawhere .= " and a.frommedia_name = '{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '') {
            $datawhere .= " and a.channel_id = '{$paramArray['channel_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(a.activity_id)  as datanum
                 FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE {$datawhere}";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }
        $sqlfields = " a.activity_id,a.activity_type,a.activity_pattern,a.activity_istemp,a.activity_tempurl,a.activitytemp_id,a.activity_sharedesc,a.activity_shareimg,a.activity_name,a.marketer_id,m.marketer_name,a.activity_starttime,a.activity_endtime,a.activity_type,s.staffer_cnname,h.school_id,a.frommedia_name,a.channel_id,(select c.channel_name from crm_code_channel as c where a.channel_id = c.channel_id) as channel_name ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_activity as a
                LEFT JOIN crm_marketer as m ON a.marketer_id = m.marketer_id
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                LEFT join crm_sell_activity_school as h ON a.activity_id = h.activity_id
                WHERE {$datawhere}
                ORDER BY activity_id DESC 
                limit {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);

        if (is_array($dataList)) {
            $list = array();
            foreach ($dataList as $datakey => $dataVar) {
                $list[$datakey]['activity_id'] = $dataVar['activity_id'];
                $list[$datakey]['activity_type'] = $dataVar['activity_type'] == '0' ? $this->LgStringSwitch('学校') : $this->LgStringSwitch('集团');
                if ($dataVar['activity_pattern'] == '0') {
                    $list[$datakey]['activity_pattern'] = $this->LgStringSwitch('普招活动');
                } elseif ($dataVar['activity_pattern'] == '1') {
                    $list[$datakey]['activity_pattern'] = $this->LgStringSwitch('招生券活动');
                }
                $list[$datakey]['activity_name'] = $dataVar['activity_name'] == null ? '' : $dataVar['activity_name'];
                $list[$datakey]['activity_starttime'] = $dataVar['activity_starttime'] . " 到 " . $dataVar['activity_endtime'];
                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                              LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and c.client_tracestatus <> '-1'");
                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'
                                                             and c.marketer_id = '{$paramArray['marketer_id']}'
                                                             and c.marketer_id <> ''");

                $actnum = $actnum['num'];
                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $list[$datakey]['clientnum'] = $clientcount;
                //活动来源的意向客户
                $list[$datakey]['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $list[$datakey]['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $list[$datakey]['percentconversion'] = (sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100) . "%";
                } else {
                    $list[$datakey]['percentconversion'] = (0) . "%";
                }

                //二维码
                if ($dataVar['activity_id'] == '89') {
                    $list[$datakey]['qrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=http://m.kidcastle.com/tbnxd/index.html";
                } else {
                    $activityOne = array();
                    if ($dataVar['activity_istemp'] == '0' && $dataVar['activity_tempurl'] != '' && $dataVar['activity_tempurl'] != '0') {//自定义
                        $activityOne['activity_SchoolUrl'] = $dataVar['activity_tempurl'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}";
                        $activityOne['activity_PersonalUrl'] = $dataVar['activity_tempurl'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&marketer_id={$dataVar['marketer_id']}";
                    } else {//模板
                        $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url", "activitytemp_id='{$dataVar['activitytemp_id']}'");
                        $activityOne['activity_SchoolUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}";
                        $activityOne['activity_PersonalUrl'] = $activitytempOne['activitytemp_url'] . "?activity_id={$dataVar['activity_id']}&school_id={$dataVar['school_id']}&marketer_id={$paramArray['marketer_id']}";
                    }

                    $list[$datakey]['qrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_SchoolUrl']);//学校二维码
                    $list[$datakey]['personalqrcode'] = "https://crmapi.kedingdang.com/Sellgoal/goalActivityshowimg?imgurl=" . base64_encode($activityOne['activity_PersonalUrl']);//个人二维码
                    $list[$datakey]['qrcodeurl'] = $activityOne['activity_SchoolUrl'];//学校二维码
                    $list[$datakey]['personalqrcodeurl'] = $activityOne['activity_PersonalUrl'];//个人二维码
                }
                //发布人
                if ($dataVar['activity_type'] == '1') {
                    $list[$datakey]['marketer_name'] = $dataVar['staffer_cnname'] == null ? '' : $dataVar['staffer_cnname'];
                } else {
                    $list[$datakey]['marketer_name'] = $dataVar['marketer_name'] == null ? '' : $dataVar['marketer_name'];
                }
                $list[$datakey]['frommedia_name'] = $dataVar['frommedia_name'];
                $list[$datakey]['channel_id'] = $dataVar['channel_id'];
                $list[$datakey]['channel_name'] = $dataVar['channel_name'];
            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //下载导入模版
    function getImportActivityOneApi()
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/单个活动名单导入模板.xlsx?v=t2';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        return $res;
    }

    //下载导入模版
    function getImportActivityChannelApi()
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/渠道招生名单导入模板.xlsx?v=t31122111';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        return $res;
    }

    //下载导入模版
    function getImportActivitysApi()
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/批量活动名单导入模板.xlsx?v=t2';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        return $res;
    }

    function ImportTrack($request, $sqlarray)
    {

        if (!$sqlarray) {
            $this->error = true;
            $this->errortip = "请确认文件是否存在数据";
            return false;
        }
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
        if (!$marketerOne) {
            $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_mobile", "staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}'");
            if (!$stafferOne) {
                $this->error = true;
                $this->errortip = "登录账号异常";
                return false;
            }
            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['staffer_id'] = $request['staffer_id'];
            $data['marketer_name'] = $stafferOne['staffer_cnname'];
            $data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $data['marketer_img'] = $stafferOne['staffer_img'];
            $data['marketer_createtime'] = time();
            $marketerOne['marketer_id'] = $this->DataControl->insertData("crm_marketer", $data);
            $marketerOne['marketer_name'] = $stafferOne['staffer_cnname'];
        }

        $t_num = 0;
        $f_num = 0;
        $f_array = array();

        foreach ($sqlarray as &$one) {

            if ($request['step'] == '1') {
                $data = array();

                if ($one['track_intention_level'] == '') {
                    $one['track_intention_level'] = 0;
                }
                $data['object_code'] = addslashes($one['object_code']);
                $data['track_intention_level'] = $one['track_intention_level'];
                $data['track_note'] = addslashes($one['track_note']);
                $data['track_createtime'] = time();

                if ($one['track_linktype'] == '') {
                    $data['track_linktype'] = addslashes($one['track_linktype']);
                } else {
                    $data['track_linktype'] = $this->LgStringSwitch('电话沟通');
                }


                $codeOne = $this->DataControl->getFieldOne("crm_code_object", "object_code", "object_name='{$data['object_code']}'");
                if ($codeOne) {
                    $data['object_code'] = $codeOne['object_code'];
                } else {
                    $data['object_code'] = 'O04';
                }

                if ($one['client_cnname'] == '' || $one['client_mobile'] == '' || $one['track_intention_level'] > '5') {
                    $data['client_cnname'] = $one['client_cnname'];
                    $data['client_mobile'] = $one['client_mobile'];
                    $data['reason'] = '';
                    if ($one['client_cnname'] == '') {
                        $data['reason'] .= $this->LgStringSwitch('姓名为空');
                    }

                    if ($one['client_mobile'] == '') {
                        $data['reason'] .= $this->LgStringSwitch('-手机号为空');
                    }

                    if ($one['track_intention_level'] > '5') {
                        $data['reason'] .= $this->LgStringSwitch('-意向星级不能大于5');
                    }
                    $f_array[] = $data;
                    $f_num++;
                } else {
                    $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_intention_maxlevel", "client_cnname='{$one['client_cnname']}' and client_mobile='{$one['client_mobile']}' and company_id='{$request['company_id']}'");
                    if ($clientOne) {
                        $data['client_id'] = $clientOne['client_id'];
                        $data['marketer_id'] = $marketerOne['marketer_id'];
                        $data['marketer_name'] = $marketerOne['marketer_name'];
                        $data['track_createtime'] = time();
                        $data['track_type'] = 1;
                        $data['track_initiative'] = 1;

                        if ($this->DataControl->insertData("crm_client_track", $data)) {

                            $tem_array = array();
                            $tem_array['client_intention_level'] = $one['track_intention_level'];
                            if ($one['track_intention_level'] > $clientOne['client_intention_maxlevel']) {
                                $tem_array['client_intention_maxlevel'] = $one['track_intention_level'];
                            }
                            $tem_array['client_updatetime'] = time();
                            $this->DataControl->updateData("crm_client", "client_id='{$clientOne['client_id']}' and company_id='{$request['company_id']}'", $tem_array);
                            $t_num++;
                        } else {
                            $data['client_cnname'] = $one['client_cnname'];
                            $data['client_mobile'] = $one['client_mobile'];
                            $data['reason'] = $this->LgStringSwitch('数据库错误');
                            $f_array[] = $data;
                            $f_num++;
                        }
                    } else {
                        $data['client_cnname'] = $one['client_cnname'];
                        $data['client_mobile'] = $one['client_mobile'];
                        $data['reason'] = $this->LgStringSwitch('客户不存在');
                        $f_array[] = $data;
                        $f_num++;
                    }
                }
            }
        }


        if ($request['step'] == '0') {
            return $sqlarray;
        } else {
            $data = array();
            $data['suc'] = $t_num;
            $data['fal'] = $f_num;
            $data['falarray'] = $f_array;
            return $data;
        }

    }


    function getImportTrack()
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/追踪记录导入模板.xlsx';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);
        return $res;
    }


    function delRepeat($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_mobile,account_class", "staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}'");
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "company_id='{$paramArray['company_id']}' and staffer_id='{$paramArray['staffer_id']}'");

        if (!$marketerOne) {
            $data = array();
            $data['company_id'] = $paramArray['company_id'];
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['marketer_name'] = $stafferOne['staffer_cnname'];
            $data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $data['marketer_img'] = $stafferOne['staffer_img'];
            $data['marketer_createtime'] = time();
            $marketerOne['marketer_id'] = $this->DataControl->insertData("crm_marketer", $data);
        }
        $paramArray['marketer_id'] = $marketerOne['marketer_id'];

        $clientOne = $this->DataControl->selectOne("SELECT c.*, e.school_id 
            FROM crm_client c, crm_client_schoolenter e
            WHERE e.client_id = c.client_id 
            AND e.is_enterstatus = '1' 
            AND c.client_id = '{$paramArray['client_id']}'");
        if ($clientOne['client_tracestatus'] == 4) {
            $this->error = '1';
            $this->errortip = '该名单已转正,请勿删除!';
            return false;
        }

        if ($clientOne) {
            $otherOne = $this->DataControl->selectOne("
              select cl.client_id 
              from crm_client as cl
              left join crm_client_schoolenter as cs On  cl.client_id=cs.client_id
              where cl.client_mobile='{$clientOne['client_mobile']}' 
              and cl.client_id <> '{$clientOne['client_id']}' 
              and cl.client_tracestatus<> '-2'
              and cl.client_tracestatus <'{$clientOne['client_tracestatus']}' 
              and cs.school_id='{$clientOne['school_id']}' 
              order by cl.client_tracestatus ASC limit 0,1");
            if ($otherOne) {// && $stafferOne['account_class'] == '0'
                $this->error = '1';
                $this->errortip = '请先删除跟踪状态较低的名单!';
                return false;
            }
            $otherclientList = $this->DataControl->selectClear("select cl.client_id,cl.client_mobile
                from crm_client as cl 
                left join  crm_client_schoolenter as  cs On  cl.client_id=cs.client_id
                where cs.school_id='{$clientOne['school_id']}' 
                and cl.client_mobile='{$clientOne['client_mobile']}'
                and cl.client_id <> '{$clientOne['client_id']}'
                ORDER BY cl.client_tracestatus DESC");
            if (!$otherclientList) {
                if ($stafferOne['account_class'] == '1') {
                    $otherClients = $this->DataControl->selectOne("select cl.client_id 
                    from crm_client as cl 
                    WHERE cl.client_mobile='{$clientOne['client_mobile']}'
                    and cl.client_id <> '{$clientOne['client_id']}' 
                    AND cl.company_id='{$paramArray['company_id']}' 
                    limit 0,1");
                    if (!$otherClients) {
                        $this->error = '1';
                        $this->errortip = '手机号唯一的名单,无法删除!';
                        return false;
                    }
                } else {
                    $this->error = '1';
                    $this->errortip = '本校唯一的名单,无法删除，只允许管理员删除!';
                    return false;
                }
            }
            $marketerOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id='{$paramArray['marketer_id']}'");

            //重复单处理
            $client = array();
            $client['client_tracestatus'] = '-2';//
            $client['client_distributionstatus'] = '0';//
            $client['client_email'] = $clientOne['client_mobile'];
            $client['client_mobile'] = "sccf" . date("ymd") . $clientOne['client_id'];
            $client['client_updatetime'] = time();
            $this->DataControl->updateData("crm_client", "client_id = '{$clientOne['client_id']}'", $client);

            //解除校区
            $choolenter = array();
            $choolenter['is_enterstatus'] = '-1';
            $choolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id = '{$clientOne['client_id']}'", $choolenter);

            //解除负责人
            $principal = array();
            $principal['principal_leave'] = '1';
            $principal['principal_updatatime'] = time();
            $this->DataControl->updateData("crm_client_principal", "client_id = '{$clientOne['client_id']}'", $principal);

            //解除负责人日志
            $allotlog = array();
            $allotlog['allotlog_status'] = '0';
            $allotlog['allotlog_note'] = '重复单删除，解除负责人！';
            $allotlog['allotlog_removetime'] = time();
            $this->DataControl->updateData("crm_client_allotlog", "client_id = '{$clientOne['client_id']}' ANd allotlog_status = '1'", $allotlog);

            //记录删除重复单情况
            if ($otherclientList) {
                foreach ($otherclientList as $otherVal) {
                    $client = array();
                    $client['client_isnewtip'] = '1';
                    $client['client_updatetime'] = time();
                    $this->DataControl->updateData("crm_client", "client_id = '{$otherVal['client_id']}'", $client);

                    $trackData = array();
                    $trackData['client_id'] = $otherVal['client_id'];
                    $trackData['marketer_id'] = $paramArray['marketer_id'];
                    $trackData['school_id'] = $clientOne['school_id'];
                    $trackData['marketer_name'] = $marketerOne['marketer_name'];
                    $trackData['track_note'] = "删除重复名单{$clientOne['client_cnname']}ID为{$paramArray['client_id']},手机号码为{$clientOne['client_mobile']}";
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 0;
                    $trackData['track_createtime'] = time();
                    $this->DataControl->insertData('crm_client_track', $trackData);
                }
            }
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "删除重复名单", '删除重复名单', "删除重复名单{$clientOne['client_cnname']}ID为{$paramArray['client_id']}手机号码为{$clientOne['client_mobile']}");

            $this->error = '0';
            $this->errortip = '删除成功!';
            return true;

            //转移其他名单跟踪记录
            /*if ($this->DataControl->updateData("crm_client_track", "client_id = '{$paramArray['client_id']}'", array("client_id" => $otherclientList[0]['client_id']))) {
                $this->DataControl->updateData("crm_client_audition", "client_id = '{$paramArray['client_id']}'", array("client_id" => $otherclientList[0]['client_id'],"audition_updatetime" => time()));
                $this->DataControl->updateData("crm_client_invite", "client_id = '{$paramArray['client_id']}'", array("client_id" => $otherclientList[0]['client_id'],"invite_updatetime" => time()));

                if ($this->DataControl->delData("crm_client", "client_id='{$paramArray['client_id']}'")) {

                    $this->error = '0';
                    $this->errortip = '删除成功!';
                    return true;
                } else {
                    $this->error = '1';
                    $this->errortip = '删除失败!';
                    return false;
                }
            } else {
                $this->error = '1';
                $this->errortip = '更新日志失败!';
                return false;
            }*/
        } else {
            $this->error = '1';
            $this->errortip = '该名单不存在';
            return false;
        }
    }

    function checkIntentionClient($paramArray, $from = "", $client_id = "")
    {
        $paramArray['client_cnname'] = trim($paramArray['client_cnname']);
        $paramArray['client_mobile'] = trim($paramArray['client_mobile']);

        $where = "(c.client_mobile='{$paramArray['client_mobile']}' OR c.client_id IN ( SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}' )) and c.company_id={$paramArray['company_id']} ";

        if ($from != "") {
            $where = "c.client_cnname='{$paramArray['client_cnname']}' and (c.client_mobile='{$paramArray['client_mobile']}' OR c.client_id IN ( SELECT f.client_id FROM crm_client_family AS f, smc_parenter AS p WHERE p.parenter_id = f.parenter_id AND p.parenter_mobile = '{$paramArray['client_mobile']}' )) and c.company_id ='{$paramArray['company_id']}'  ";// and cs.school_id = '{$paramArray['school_id']}'  //校务和crm 有点区别  不一定由学校，所以改为了有就补充的条件
        }
        if ($paramArray['school_id'] > 0) {
            $where .= " and cs.school_id = '{$paramArray['school_id']}' ";
        }
        if ($client_id != "") {
            $where .= " and (c.client_id <> '{$client_id}')";
        }
        if ($paramArray['client_id']) {
            $where .= " and (c.client_id <> '{$paramArray['client_id']}')";
        }

        $sqlfields = 'c.client_id ,c.client_img,c.client_cnname,c.client_enname,c.client_sex,c.client_age,c.client_distributionstatus,c.client_mobile,s.school_id,s.school_cnname,c.client_tracestatus ';
        $sqlorder = 'c.client_createtime';

        $sql = "select {$sqlfields},
					(select marketer_name  from  crm_client_principal  as p
					  LEFT JOIN  crm_marketer  as m  ON p.marketer_id = m.marketer_id AND p.principal_ismajor = 1 AND principal_leave = 0
					 where p.client_id=c.client_id and p.school_id=cs.school_id order by principal_createtime Desc limit 0,1) as   marketer_name,
					 (select  p.principal_id from  crm_client_principal as p where p.principal_leave=0 and c.client_id = p.client_id and  p.marketer_id = '{$paramArray['marketer_id']}' and cs.school_id=p.school_id   limit 0,1 ) as is_intention

					FROM crm_client as c
					LEFT JOIN crm_client_schoolenter as cs ON  cs.client_id= c.client_id
					LEFT JOIN smc_school as s ON s.school_id = cs.school_id
					where  {$where}  ORDER BY {$sqlorder} DESC ";


        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            foreach ($clientList as $key => $value) {

                if (empty($value['marketer_name'])) {
                    $clientList[$key]['marketer_name'] = "--";
                }
                if (!$value['is_intention'] && $value['marketer_name'] && $value['school_id'] == $paramArray['school_id']) {
                    $clientList[$key]['client_status'] = 1;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("无权限跟踪");
                } elseif ($value['is_intention'] && $value['school_id'] == $paramArray['school_id']) {
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("继续跟踪");
                } elseif ($value['school_id'] <> $paramArray['school_id'] && $value['client_tracestatus'] <> 4) {
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                } elseif ($value['school_id'] == $paramArray['school_id'] && $value['client_distributionstatus'] == 0 && $value['client_tracestatus'] <> 4) {
                    $clientList[$key]['client_status'] = 0;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                } else {
                    $clientList[$key]['client_status'] = 1;
                    $clientList[$key]['track_stuname'] = $this->LgStringSwitch("我要跟踪");
                }

            }
        }
        return $clientList;

    }

    function transferSchool($paramArray)
    {
        if (!$paramArray['re_school_id']) {
            $this->error = 1;
            $this->errortip = "请选择需要的目标学校";
            return false;
        }
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "company_id='{$paramArray['company_id']}' and staffer_id='{$paramArray['staffer_id']}'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_mobile,account_class", "staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}'");
        if (!$marketerOne) {
            $data = array();
            $data['company_id'] = $paramArray['company_id'];
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['marketer_name'] = $stafferOne['staffer_cnname'];
            $data['marketer_mobile'] = $stafferOne['staffer_mobile'];
            $data['marketer_img'] = $stafferOne['staffer_img'];
            $data['marketer_createtime'] = time();
            $marketerOne['marketer_id'] = $this->DataControl->insertData("crm_marketer", $data);
        }
        $paramArray['marketer_id'] = $marketerOne['marketer_id'];
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$paramArray['school_id']}'");
        $reSchoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname", "school_id='{$paramArray['re_school_id']}'");

        $clientOne = $this->DataControl->getOne("crm_client", "client_id='{$paramArray['client_id']}'");
        $checkData = array();
        $checkData['client_cnname'] = $clientOne['client_cnname'];
        $checkData['client_mobile'] = $clientOne['client_mobile'];
        $checkData['school_id'] = $paramArray['re_school_id'];
        $checkData['company_id'] = $paramArray['company_id'];
        $from = "transfer";

        $intent = $this->checkIntentionClient($checkData, $from, '');

        if ($intent && $stafferOne['account_class'] == '0') {
            $this->error = 1;
            $this->errortip = "目标学校中，存在中文名与手机号相同的客户,请联系管理员转校！";
            return false;
        }

        $schoolenterOne = $this->DataControl->getFieldOne('crm_client_schoolenter', "schoolenter_id", "school_id='{$paramArray['re_school_id']}' and  client_id='{$paramArray['client_id']}'");

        if (!$schoolenterOne) {
            $schoolData = array();
            $schoolData['company_id'] = $paramArray['company_id'];
            $schoolData['client_id'] = $paramArray['client_id'];
            $schoolData['school_id'] = $paramArray['re_school_id'];
            $schoolData['is_schoolenter'] = 0;
            $schoolData['schoolenter_createtime'] = time();
            $schoolData['schoolenter_updatetime'] = time();
            $this->DataControl->insertData('crm_client_schoolenter', $schoolData);

            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$paramArray['client_id']}' and school_id = '{$paramArray['school_id']}'", $schoolenter);

            //$this->DataControl->delData('crm_client_schoolenter', "school_id='{$paramArray['school_id']}' and  client_id='{$paramArray['client_id']}'");

            $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_name', "marketer_id='{$paramArray['marketer_id']}'");
            $traData = array();
            $traData['client_id'] = $paramArray['client_id'];
            $traData['marketer_id'] = $paramArray['marketer_id'];
            $traData['marketer_name'] = $markertOne['marketer_name'];
            if (empty($paramArray['note'])) {
                $paramArray['note'] = $this->LgStringSwitch("名单由{$schoolOne['school_cnname']}转到{$reSchoolOne['school_cnname']}");
            } else {
                $paramArray['note'] .= $this->LgStringSwitch(",名单由{$schoolOne['school_cnname']}转到{$reSchoolOne['school_cnname']}");
            }
            $traData['track_note'] = $paramArray['note'];
            $traData['object_code'] = "";
            $traData['school_id'] = $paramArray['school_id'];
            $traData['track_state'] = "0";
            $traData['track_createtime'] = time();
            $traData['track_type'] = 1;
            $traData['track_initiative'] = 0;
            $traData['4'] = 5;

            $this->DataControl->insertData('crm_client_track', $traData);
            $clientData = array();
            $clientData['client_updatetime'] = time();
            $clientData['client_tracestatus'] = 0;
            $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);

            $this->error = 0;
            $this->errortip = "转校成功";
            return true;
        } else {
            $this->error = 0;
            $this->errortip = "该客户已在目标学校";
            return false;
        }
    }

    function searchComClient($request)
    {
        $where = "c.company_id='{$request['company_id']}' ";

        $wheres = "s.company_id='{$request['company_id']}'";
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $keyword = str_replace(' ', '', $request['keyword']);
            $arr_keyword = explode('|', $keyword);
            if (is_array($arr_keyword)) {
                for ($i = 0; $i < count($arr_keyword); $i++) {
                    $tem[] = "'{$arr_keyword[$i]}'";
                }
            }

            $str_keyword = implode($tem, ',');
            $where .= " and (c.client_mobile in ({$str_keyword}) or c.client_cnname = '{$request['keyword']}' or c.client_enname = '{$request['keyword']}')";
            $wheres .= " and (f.family_mobile in ({$str_keyword}) or s.student_cnname = '{$request['keyword']}' or s.student_enname = '{$request['keyword']}')";

        } else {
            $this->error = 0;
            $this->errortip = "请输入手机号码";
            return array();
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $clientList = $this->DataControl->selectClear("SELECT c.client_id,c.client_cnname,c.client_enname,c.client_source,l.channel_name,client_mobile,client_distributionstatus,client_tracestatus,client_sex,
            (SELECT mk.marketer_name FROM crm_client_principal as p
			LEFT JOIN crm_marketer AS mk ON mk.marketer_id = p.marketer_id
			WHERE p.client_id = c.client_id AND p.principal_leave= 0 AND p.principal_ismajor=1  ORDER BY principal_id DESC Limit 0,1) as marketer_name,
			(SELECT pa.parenter_cnname FROM crm_client_family AS fa,smc_parenter AS pa WHERE fa.parenter_id = pa.parenter_id AND fa.client_id=c.client_id order by fa.family_isdefault DESC limit 0,1) AS parenter_cnname,
			(SELECT group_concat(s.school_cnname) FROM crm_client_schoolenter as sc
			LEFT JOIN smc_school AS s ON sc.school_id = s.school_id 
			  WHERE sc.client_id=c.client_id and sc.is_enterstatus='1') AS  school_cnname,
			  '1' as status
             FROM crm_client AS c
             LEFT JOIN crm_code_channel AS l ON  c.channel_id = l.channel_id
            WHERE {$where}
             	UNION
            SELECT
              '0' as client_id,
                s.student_cnname,
                s.student_enname,
                s.student_branch as client_source,
                ifnull((SELECT SUM(p.pay_price) FROM smc_payfee_order_pay AS p,smc_payfee_order AS o
                    WHERE p.order_pid = o.order_pid AND p.pay_issuccess = '1' AND p.paychannel_code IN ('cash','cmb','hcmb','sdpos') AND o.student_id = s.student_id),0) AS channel_name,
                f.family_mobile,
                '' as client_distributionstatus,
                '' as client_tracestatus,
                s.student_sex,
                '' as marketer_name,
                f.family_cnname,
                sc.school_cnname,
                '0' as status
            FROM
                smc_student AS s
                LEFT JOIN smc_student_family AS f ON f.student_id = s.student_id
                left join smc_student_enrolled as en on en.student_id = s.student_id
                left join smc_school as sc on sc.school_id = en.school_id
                where {$wheres}
             
             Limit {$pagestart},{$num}");

        if (isset($request['is_count']) && $request['is_count'] == '1') {
            $allnum = $this->DataControl->selectClear("SELECT c.client_id
             FROM crm_client AS c
             LEFT JOIN crm_code_channel AS l ON  c.channel_id = l.channel_id
            WHERE {$where}
             	UNION
            SELECT
              s.student_id as client_id
            FROM
                smc_student AS s
                LEFT JOIN smc_student_family AS f ON f.student_id = s.student_id
                left join smc_student_enrolled as en on en.student_id = s.student_id
                left join smc_school as sc on sc.school_id = en.school_id
                where {$wheres}");
            $result['allnum'] = count($allnum);
        } else {
            $result['allnum'] = '0';
        }

//        0待跟踪1持续跟踪2已柜询3已试听4已转正-1已流失-2已无效'
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if ($clientList) {
            foreach ($clientList as &$val) {
                $val['client_statusname'] = $clientTracestatus[$val['client_tracestatus']];

                if ($val['client_source'] == '') {
                    $val['client_source'] = '--';
                }
                if ($val['channel_name'] == '') {
                    $val['channel_name'] = '--';
                }
                if ($val['client_tracestatus'] == '') {
                    $val['client_statusname'] = '已入校';
                }
                if ($val['client_source'] == '') {
                    $val['client_source'] = '--';
                }
                if ($val['marketer_name'] == '') {
                    $val['marketer_name'] = '--';
                }
            }
        } else {
            $clientList = array();
        }
        $result['list'] = $clientList;
        $this->error = 0;
        $this->errortip = "查询成功";
        return $result;

    }

    /**
     * 待分配名单/无意向名单/
     * @param $paramArray
     * @return array
     */
    function getUnAssignmentList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND (c.client_tracestatus = '0' or c.client_tracestatus = '1') AND e.client_id IS NULL and c.client_gmcdistributionstatus = '0' ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            if ($paramArray['keyword'] == '###') {
                $datawhere .= " and c.outthree_userid = ''";
            } else {
                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or c.client_tmkbatch like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            }
        }

        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] !== '') {
            if ($paramArray['activity_id'] == "0") {
                $datawhere .= " and c.activity_id ='0'";
            } elseif ($paramArray['activity_id'] == "1") {
                $datawhere .= " and c.activity_id > '0'";
            }
        }

        //请勿改，改之前问
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }
        if ($paramArray['dataequity'] != 1 && $paramArray['re_postbe_id'] > 0) {
            $postroleOne = $this->DataControl->selectOne("SELECT r.postrole_dataequity,b.organize_id FROM gmc_company_postrole AS r,gmc_staffer_postbe AS b
WHERE r.postrole_id = b.postrole_id AND b.postbe_id = '{$paramArray['re_postbe_id']}' limit 0,1");
            //判定非所有权益，仅组织权益
            if ($postroleOne['postrole_dataequity'] !== '1') {
                $datawhere .= " and l.channel_id in (SELECT o.channel_id FROM crm_channel_organize AS o WHERE o.organize_id = '{$postroleOne['organize_id']}') ";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $field = "c.channel_id,c.client_source,c.client_gmcmarket,c.client_id,c.client_tracestatus,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_tmkbatch
        ,c.client_age,c.client_tag,c.client_birthday,c.client_sex,c.client_mobile,c.client_address,c.client_img,c.outthree_userid,c.province_id,c.city_id,c.area_id,c.client_remark,c.client_tag,c.client_frompage,
        from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,
       from_unixtime(c.client_updatetime, '%Y-%m-%d') AS client_updatetime,
       l.channel_maxday,l.channel_minday,l.channel_medianame,l.channel_name,
       (select ch.channellog_id from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1) AS channellog_id,
       (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) as activity_name,
       (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
       (select r.region_name from smc_code_region as r where c.province_id = r.region_id) AS province_name,
       (select r.region_name from smc_code_region as r where c.city_id = r.region_id) AS city_name,
       (select r.region_name from smc_code_region as r where c.area_id = r.region_id) AS area_name";
//        ", -- (select group_concat() from crm_client_intention ) as courercatstr";
        $sql = "SELECT {$field}
        FROM crm_client AS c
        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id
        LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id AND e.is_enterstatus = '1' 
        WHERE {$datawhere} ORDER BY c.client_updatetime DESC
        LIMIT {$pagestart},{$num}";
//        echo $sql;die;

        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            foreach ($clientList as $key => &$value) {
                $clientList[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $clientList[$key]['course_cnname'] = array();
                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                if ($value['channel_maxday'] == 0 || $time <= $max) {
                    $value['status'] = 1;
                } else {
                    $value['status'] = 0;
                }
                if ($value['channellog_id'] == null) {
                    $value['apply'] = '1';
                } else {
                    $value['apply'] = '0';
                }
                if ($value['channel_id'] == '146' || $value['channel_id'] == '577') {
                    $value['apply'] = '0';
                }
            }
        }

        $allNum = $this->DataControl->selectOne("SELECT COUNT(c.client_id) AS cnums FROM crm_client AS c
LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id
LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id AND e.is_enterstatus = '1' WHERE {$datawhere} LIMIT 0,1");
        $allnums = $allNum['cnums'];

        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;
    }


    //沟通类型统计
    function getComMode($paramArray)
    {
        $datawhere = " 1 and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' and s.school_istest = '0'  ";
        //沟通类型
        if (isset($paramArray['linktype']) && $paramArray['linktype'] != '') {
            $datawhere .= " and k.track_linktype like '%{$paramArray['linktype']}%' ";
        }
        //开始时间
        if (isset($paramArray['stime']) && $paramArray['stime'] != '') {
            $stime = strtotime($paramArray['stime']);
        }
        //结束时间
        if (isset($paramArray['etime']) && $paramArray['etime'] != '') {
            $etime = strtotime($paramArray['etime']) + 86399;
        }
        if (isset($stime) && $stime != '' && isset($etime) && $etime != '') {
            $datawhere .= " and k.track_createtime <= '{$etime}' and k.track_createtime >= '{$stime}'";
        } elseif (isset($stime) && $stime != '') {
            $datawhere .= " and k.track_createtime >= '{$stime}'";
        } elseif (isset($etime) && $etime != '') {
            $datawhere .= " and k.track_createtime <= '{$etime}'";
        }

        $sql = "SELECT s.school_cnname,s.school_shortname,s.school_branch,
                count(DISTINCT(k.client_id)) as allnum,
                count(DISTINCT k.client_id,IF(k.track_followmode='3',TRUE,NULL)) as zzallnum  
                FROM smc_school as s 
                LEFT JOIN crm_client_track as k ON s.school_id = k.school_id  
                WHERE {$datawhere} ";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['linktype'] = $paramArray['linktype'];//沟通类型
                $datearray['allnum'] = $dateexcelvar['allnum'];
                $datearray['zzallnum'] = $dateexcelvar['zzallnum'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '沟通类型', '客户数量', '已报名客户数量'));
        $excelfileds = array('school_shortname', 'school_branch', 'linktype', 'allnum', 'zzallnum');

        $fielname = $this->LgStringSwitch("沟通类型统计表");
        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
        exit;
    }


    /**
     * 批量分配给校区
     * @param $paramArray clients_json
     * @param $paramArray staffer_id
     * @param $paramArray company_id
     * @param $paramArray school_id
     * @return false
     */
    public function batchDistributeClientToSchool($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);

        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        } else {
            if (!isset($paramArray['school_id']) || $paramArray['school_id'] < 1 || $paramArray['school_id'] == '') {
                $this->error = 1;
                $this->errortip = "学校参数有误";
                return false;
            }
            //跟进人信息
            $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
            if ($markertOne) {
                $marketer_id = $markertOne['marketer_id'];
                $marketer_name = $markertOne['marketer_name'];
            } else {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $marketer_id = $getaddmarkertOne['marketer_id'];
                $marketer_name = $getaddmarkertOne['marketer_name'];
            }

            foreach ($clients_array as $key => $value) {
                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['marketer_id'] = $marketer_id;
                $trackData['marketer_name'] = $marketer_name;
                $trackData['school_id'] = $paramArray['school_id'];
                $trackData['track_intention_level'] = "3";
                $trackData['track_linktype'] = "集团分配校区";
                $trackData['track_note'] = "由于名单未分配校园，由集团工作人员初步筛选客户意向，分配校园进行跟踪。";
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_validinc'] = 1;
                $trackData['track_initiative'] = 0;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $channelOne = $this->DataControl->selectOne("select t.client_intention_maxlevel,c.channel_quality from crm_client as t,crm_code_channel as c where t.client_id = '{$value['client_id']}' and t.channel_id = c.channel_id limit 0,1 ");//channel_quality  0 默认毛名单 1 默认有效名单
                $clientdata = array();
                $clientdata['client_updatetime'] = time();
                if ($channelOne['channel_quality'] == '1') {
                    $clientdata['client_isgross'] = 0;//不是毛名单
                } elseif ($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != '') {
                    $clientdata['client_isgross'] = 1;//是毛名单
                } else {
                    $clientdata['client_isgross'] = 0;//不是毛名单
                }
                if ($channelOne['client_intention_maxlevel'] < 3) {
                    $clientdata['client_intention_maxlevel'] = 3;
                }
                $this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $clientdata);

                //判定名单是否在校区进行进行重新分配
                $this->AddClientschool($value['client_id'], $paramArray['school_id'], $paramArray['company_id'], 1);
            }

            $schoolOne = $this->DataControl->selectOne("select school_shortname from smc_school where school_id = '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}'");
            $allnum = count($clients_array);
            if ($schoolOne['school_shortname'] && $allnum) {
                $thetip = "{$allnum}条名单已成功分配至{$schoolOne['school_shortname']}校区";
            }

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '批量分配名单给学校', dataEncode($paramArray));
            $this->error = 0;
            $this->errortip = $thetip ? "$thetip" : "分配操作成功";
            return true;
        }
    }


    //负责人 操作人 主-1 副-0
    function addAllotLog($client_id, $main_marketer_id, $marketer_id, $school_id, $ismajor)
    {
        $dataAllotlog = array();
        $dataAllotlog['client_id'] = $client_id;
        $dataAllotlog['marketer_id'] = $marketer_id;
        $dataAllotlog['allot_marketer_id'] = $main_marketer_id;
        $dataAllotlog['allotlog_status'] = 1;
        $dataAllotlog['allotlog_ismajor'] = $ismajor; //0辅助招1主招
        $dataAllotlog['allotlog_createtime'] = time();
        $dataAllotlog['school_id'] = $school_id;
        if (!$this->DataControl->insertData('crm_client_allotlog', $dataAllotlog)) {
            return false;
        } else {
            return true;
        }
    }

    //设定名单主负责人
    public function setClientPrincipal($client_id, $tomarketer_id, $marketer_id)
    {
        if (!$this->DataControl->selectOne("SELECT principal_id FROM crm_client_principal where client_id = '{$client_id}' AND marketer_id = '{$tomarketer_id}' AND school_id = '0'")) {
            $dataPrintcipal = array();
            $dataPrintcipal['client_id'] = $client_id;
            $dataPrintcipal['marketer_id'] = $tomarketer_id;
            $dataPrintcipal['school_id'] = 0;
            $dataPrintcipal['principal_ismajor'] = 1;
            $dataPrintcipal['principal_leave'] = 0;
            $dataPrintcipal['principal_createtime'] = time();
            if (!$this->DataControl->insertData('crm_client_principal', $dataPrintcipal)) {
                $this->addAllotLog($client_id, $tomarketer_id, $marketer_id, 0, 1);
                $this->error = 1;
                $this->errortip = "增加主负责人记录失败";
                return false;
            } else {
                //增加分配记录
                if (!$this->addAllotLog($client_id, $tomarketer_id, $marketer_id, 0, 1)) {
                    $this->error = 1;
                    $this->errortip = "增加主分配记录失败";
                    return false;
                }
                return true;
            }
        } else {
            $dataPrintcipal = array();
            $dataPrintcipal['principal_ismajor'] = 1;
            $dataPrintcipal['principal_leave'] = 0;
            $dataPrintcipal['principal_updatatime'] = time();
            $this->DataControl->updateData('crm_client_principal', "client_id = '{$client_id}' AND marketer_id = '{$tomarketer_id}' AND school_id = '0'", $dataPrintcipal);

            if (!$this->addAllotLog($client_id, $tomarketer_id, $marketer_id, 0, 1)) {
                $this->error = 1;
                $this->errortip = "增加主分配记录失败";
                return false;
            }
            return true;
        }

    }

    /**
     * 批量分配名单给负责人
     * @param $paramArray clients_json
     * @param $paramArray marketer_id
     * @param $paramArray staffer_id
     * @return false
     */
    public function batchDistributeClientToMarket($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        } else {
            if (!isset($paramArray['tostaffer_id']) || $paramArray['tostaffer_id'] < 1 || $paramArray['tostaffer_id'] == '') {
                $this->error = 1;
                $this->errortip = "负责人参数有误";
                return false;
            }

            $marketerInfo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if (!$marketerInfo) {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $marketerInfo['marketer_id'] = $getaddmarkertOne['marketer_id'];
                $marketerInfo['marketer_name'] = $getaddmarkertOne['marketer_name'];
            }

            $tomarkertInfo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['tostaffer_id']}'");
            if ($tomarkertInfo) {
                $tomarketer_id = $tomarkertInfo['marketer_id'];
                $tomarketer_name = $tomarkertInfo['marketer_name'];
            } else {
                $togetaddmarkertOne = $this->addStaffMarketerOne($paramArray['tostaffer_id']);
                $tomarketer_id = $togetaddmarkertOne['marketer_id'];
                $tomarketer_name = $togetaddmarkertOne['marketer_name'];
            }
            $this->DataControl->begintransaction();
            foreach ($clients_array as $key => $value) {
                if (!$this->gmccheckClientstatus($value['client_id'])) {
                    $this->error = 1;
                    continue;
                }
                //更改是分配状态
                $data = array();
                $data['client_gmcdistributionstatus'] = 1;
                $data['client_tracestatus'] = 0;
                $data['client_ischaserlapsed'] = 0;
                $data['client_updatetime'] = time();
                if (!$this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $data)) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新客户状态失败";
                    return false;
                }

                //分配主负责人
                if (!$this->setClientPrincipal($value['client_id'], $tomarketer_id, $marketerInfo['marketer_id'])) {
                    $this->DataControl->rollback();
                }

                /*$dataPrintcipal = array();
                $dataPrintcipal['client_id'] = $value['client_id'];
                $dataPrintcipal['marketer_id'] = $tomarketer_id;
                $dataPrintcipal['school_id'] = 0;
                $dataPrintcipal['principal_ismajor'] = 1;
                $dataPrintcipal['principal_leave'] = 0;
                $dataPrintcipal['principal_createtime'] = time();
                if (!$this->DataControl->insertData('crm_client_principal', $dataPrintcipal)) {
                    $this->addAllotLog($value['client_id'], $tomarketer_id, $marketerInfo['marketer_id'], 0, 1);
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "增加主负责人记录失败";
                    return false;
                } else {
                    //增加分配记录
                    if (!$this->addAllotLog($value['client_id'], $tomarketer_id, $marketerInfo['marketer_id'], 0, 1)) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "增加主分配记录失败";
                        return false;
                    }
                }*/

                //添加跟踪记录
                $dataTrack = array();
                $dataTrack['client_id'] = $value['client_id'];
                $dataTrack['track_type'] = 1; //1-集团跟踪
                $dataTrack['school_id'] = 0;
                $dataTrack['track_validinc'] = 1;
                $dataTrack['marketer_id'] = $marketerInfo['marketer_id'];
                $dataTrack['marketer_name'] = $marketerInfo['marketer_name'];
                $dataTrack['track_linktype'] = $this->LgStringSwitch("系统");
                $dataTrack['track_note'] = $this->LgStringSwitch("由{$marketerInfo['marketer_name']}进行分配负责人,进行跟进");
                $dataTrack['track_linktype'] = $this->LgStringSwitch("主管分配");//20200109需求修改
                $dataTrack['track_createtime'] = time();
                $this->DataControl->insertData('crm_client_track', $dataTrack);

                //微信推送 --- CRM 部分有微信，集团好像没有  故 取消
//                if ($paramArray['marketer_id'] !== $paramArray['main_marketer_id']) {
//                    $this->clientCompleteAllotRemind($paramArray['main_marketer_id'], $paramArray['client_id']);
//                    if (isset($paramArray['fu_marketer_id']) && is_array($paramArray['fu_marketer_id'])) {
//                        for ($j = 0; $j < count($paramArray['fu_marketer_id']); $j++) {
//                            $this->clientCompleteAllotRemind($paramArray['fu_marketer_id'][$j], $paramArray['client_id']);
//                        }
//                    }
//                }

            }

            $allnum = count($clients_array);
            if ($paramArray['tostaffer_id'] == $paramArray['staffer_id']) {
                $thetip = "{$allnum}条名单已成功分配至您的名单";
            } else {
                $thestaff = $this->DataControl->selectOne("select staffer_cnname,staffer_enname from smc_staffer where staffer_id = '{$paramArray['tostaffer_id']}' and company_id = '{$paramArray['company_id']}'");
                $thetip = "{$allnum}条名单已成功分配至{$thestaff['staffer_cnname']}" . ($thestaff['staffer_enname'] ? "（{$thestaff['staffer_enname']}）" : '') . "负责人";
            }

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '批量分配名单给负责人', dataEncode($paramArray));
            $this->DataControl->commit();
            $this->error = 0;
            $this->errortip = $thetip ? "$thetip" : "分配操作成功";
            return true;
        }

    }

    /**
     * 批量分配名单给负责人
     * @param $paramArray clients_json
     * @param $paramArray marketer_id
     * @param $paramArray staffer_id
     * @return false
     */
    public function batchForwardClientToMarketAction($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        } else {
            $marketerInfo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if (!$marketerInfo) {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $marketerInfo['marketer_id'] = $getaddmarkertOne['marketer_id'];
                $marketerInfo['marketer_name'] = $getaddmarkertOne['marketer_name'];
            }

            $tomarkertInfo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['tostaffer_id']}'");
            if ($tomarkertInfo) {
                $tomarketer_id = $tomarkertInfo['marketer_id'];
                $tomarketer_name = $tomarkertInfo['marketer_name'];
            } else {
                $togetaddmarkertOne = $this->addStaffMarketerOne($paramArray['tostaffer_id']);
                $tomarketer_id = $togetaddmarkertOne['marketer_id'];
                $tomarketer_name = $togetaddmarkertOne['marketer_name'];
            }
            $this->DataControl->begintransaction();

            $scc = '0';
            $fail = '0';
            foreach ($clients_array as $key => $value) {
                $GmcPrincipalOne = $this->DataControl->selectOne(" select principal_id,principal_createtime,principal_updatatime  from crm_client_principal where client_id = '{$value['client_id']}' and school_id = '0' and  principal_leave = 0 order by principal_createtime desc limit 0,1 ");
                $inviteOne = $this->DataControl->selectOne(" select school_id from crm_client_invite where client_id = '{$value['client_id']}' and school_id > '0' and invite_createtime > '{$GmcPrincipalOne['principal_createtime']}' and invite_createtime > '{$GmcPrincipalOne['principal_updatatime']}' and invite_isvisit = '0' ");
                $auditionOne = $this->DataControl->selectOne(" select school_id from crm_client_audition where client_id = '{$value['client_id']}' and school_id > '0' and audition_createtime > '{$GmcPrincipalOne['principal_createtime']}'  and audition_createtime > '{$GmcPrincipalOne['principal_updatatime']}' and audition_isvisit = '0' ");
                if ($inviteOne || $auditionOne) {
                    $fail++;
                } else {
                    //负责人日志解除
                    $clientPrincipalList = $this->DataControl->selectClear(" select * from crm_client_principal where client_id = '{$value['client_id']}' and school_id = '0' and  principal_leave = 0 ");
                    if ($clientPrincipalList) {
                        $dataAllotlog = array();
                        $dataAllotlog['allotlog_status'] = 0;
                        $dataAllotlog['allotlog_removetime'] = time();
                        foreach ($clientPrincipalList as $key => $value) {
                            $this->DataControl->updateData('crm_client_allotlog', "allotlog_status =1 and client_id='{$value['client_id']}' and allot_marketer_id='{$value['marketer_id']}'", $dataAllotlog);
                        }
                    }

                    //解除负责人的负责状态  一定要先添加解除日志(陆晶备注的）
                    $dataPrincipal = array();
                    $dataPrincipal['principal_leave'] = 1;
                    $dataPrincipal['principal_updatatime'] = time();
                    if (!$this->DataControl->updateData('crm_client_principal', "client_id='{$value['client_id']}' and school_id = '0'  ", $dataPrincipal)) {
                        $this->error = 1;
                        $this->errortip = "解除负责人失败";
                        return false;
                    }

//                    //更新名单状态
//                    $dataClient = array();
////                    $dataClient['client_tracestatus'] = 0;// 20220811 更改的需求注释
//                    $dataClient['client_updatetime'] = time();
//                    if (!$this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $dataClient)) {
//                        $this->error = 1;
//                        $this->errortip = "重置名单跟踪状态失败";
//                        return false;
//                    }

                    //增加跟踪记录
                    $dataTrack = array();
                    $dataTrack['marketer_id'] = $getaddmarkertOne['marketer_id'];
                    $dataTrack['marketer_name'] = $getaddmarkertOne['marketer_name'];
                    $dataTrack['school_id'] = 0;
                    $dataTrack['track_createtime'] = time();
                    $dataTrack['track_state'] = 0;
                    $dataTrack['track_followmode'] = 0;
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("系统操作");
                    $dataTrack['track_note'] = $this->LgStringSwitch('主管操作（集团）:批量更换负责人，解除负责人状态！');
                    $dataTrack['track_note'] = "系统操作：主管操作（集团）:批量更换负责人，解除负责人状态！";
                    $dataTrack['client_id'] = $value['client_id'];
                    $dataTrack['track_type'] = 1;
                    $dataTrack['track_initiative'] = 0;
                    if (!$this->DataControl->insertData('crm_client_track', $dataTrack)) {
                        $this->error = 1;
                        $this->errortip = "增加跟踪记录失败";
                        echo '333';
                        die;
                        return false;
                    }
                    //更改是分配状态  --- 保留之前的状态

                    //分配主负责人
                    if (!$this->setClientPrincipal($value['client_id'], $tomarketer_id, $marketerInfo['marketer_id'])) {
                        $this->DataControl->rollback();
                    }

                    /*$dataPrintcipal = array();
                    $dataPrintcipal['client_id'] = $value['client_id'];
                    $dataPrintcipal['marketer_id'] = $tomarketer_id;
                    $dataPrintcipal['school_id'] = 0;
                    $dataPrintcipal['principal_ismajor'] = 1;
                    $dataPrintcipal['principal_leave'] = 0;
                    $dataPrintcipal['principal_createtime'] = time();
                    if (!$this->DataControl->insertData('crm_client_principal', $dataPrintcipal)) {
                        $this->addAllotLog($value['client_id'], $tomarketer_id, $marketerInfo['marketer_id'], 0, 1);
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "增加主负责人记录失败";
                        return false;
                    } else {
                        //增加分配记录
                        if (!$this->addAllotLog($value['client_id'], $tomarketer_id, $marketerInfo['marketer_id'], 0, 1)) {
                            $this->DataControl->rollback();
                            $this->error = 1;
                            $this->errortip = "增加主分配记录失败";
                            return false;
                        }
                    }*/


                    //添加跟踪记录
                    $dataTrack = array();
                    $dataTrack['client_id'] = $value['client_id'];
                    $dataTrack['track_type'] = 1; //1-集团跟踪
                    $dataTrack['school_id'] = 0;
                    $dataTrack['marketer_id'] = $marketerInfo['marketer_id'];
                    $dataTrack['marketer_name'] = $marketerInfo['marketer_name'];
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("系统");
                    $dataTrack['track_note'] = $this->LgStringSwitch("由{$marketerInfo['marketer_name']}进行批量转发分配负责人,进行跟进");
                    $dataTrack['track_linktype'] = $this->LgStringSwitch("主管批量转发分配");//20200109需求修改
                    $dataTrack['track_createtime'] = time();
                    $this->DataControl->insertData('crm_client_track', $dataTrack);

                    $scc++;
                }
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '批量转发分配名单给负责人', dataEncode($paramArray));

            }
            if ($fail > 0) {
                $tip = "{$scc}条名单分配负责人成功，{$fail}条名单已邀约至校区不可更换负责人";
            } else {
                $tip = '分配操作成功';
            }

            $this->DataControl->commit();
            $this->error = 0;
            $this->errortip = $tip;
            return true;
        }
    }


    /**
     * 集团名单跟进列表 -- 针对手机版本（呼叫中心)
     * @param $paramArray
     * @return array
     */
    public function getClientFollowUpMobileList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class from gmc_staffer_postbe as p where  p.postrole_id > '0'  and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id = '{$paramArray['re_postbe_id']}' ");
            if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
                $this->error = 1;
                $this->errortip = "您的职务没有集团招生管理权限，无权查看！";
                return false;
            }
        }
        //因为少了渠道出不来数据
        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  ";
        $datawheres = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%'  )";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['client_id']) && $paramArray['client_id'] !== '') {
            $datawhere .= " and c.client_id = '{$paramArray['client_id']}'";
        }

        $having = "1=1";
        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";

        $markerwhere = "1";
        $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
        //管辖的人
        if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

            if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                    $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                    $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                    $markertList = $this->DataControl->selectClear($sql);
                    $arr_marketer_id = array_column($markertList, "marketer_id");
                    $str_marketer_id = implode(',', $arr_marketer_id);

                    $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                    $datawheres .= " and p.marketer_id in ({$str_marketer_id}) ";
                    $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
                } else {
                    $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                    $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                    $datawheres .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                    $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
                }
            }
        } else {
            $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
            $markertList = $this->DataControl->selectClear($sql);
            $arr_marketer_id = array_column($markertList, "marketer_id");
            $str_marketer_id = implode(',', $arr_marketer_id);

            //包含管理员
            $accountmarker = $this->DataControl->selectClear("select m.marketer_id from smc_staffer as s 
                            left join crm_marketer as m ON s.staffer_id = m.staffer_id 
                            where s.account_class = '1' and s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' ");
            if (is_array($accountmarker)) {
                $arr_accountmarketer_id = array_column($accountmarker, "marketer_id");
                $str_accountmarketer_id = implode(',', $arr_accountmarketer_id);
                $str_marketer_id .= "," . $str_accountmarketer_id;
            }
            $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
            $datawheres .= " and p.marketer_id in ({$str_marketer_id}) ";
            $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
        }

        //智能排序
        if ($paramArray['orderbyname'] == 'asc') {
            $orderby = "order by c.client_createtime ASC  ";
        } elseif ($paramArray['orderbyname'] == 'desc') {
            $orderby = "order by track_lasttime DESC,c.client_updatetime DESC ";
        } else {
            $orderby = "order by p.principal_createtime DESC";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "c.client_id, c.client_cnname, c.client_enname, c.client_age, c.client_birthday, c.client_sex, c.client_mobile,
         c.client_img, c.client_tracestatus, c.client_patriarchname,c.province_id, c.city_id, c.area_id, c.client_remark, c.client_intention_level,
         c.channel_id, c.client_source, c.client_gmcmarket,c.client_gmcdistributionstatus,l.channel_name, 
         (select t.track_followuptime from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_followuptime, 
         (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id";
        $sql = "SELECT {$fields}
                FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
                WHERE {$datawhere} GROUP BY c.client_id  HAVING {$having} {$orderby} LIMIT {$pagestart},{$num} ";
        $clientList = $this->DataControl->selectClear($sql);

        $fieldname = array('client_id','client_img','client_cnname','client_enname','client_sex','client_age','client_birthday','client_tracestatus',
            'client_intention_level','client_mobile','client_source','channel_name','channel_id');
        $fieldstring = array('名单ID','名单头像','名单中文名','名单英文名','名单性别','名单年龄，名单生日','名单跟踪状态',
            '名单意向登记1-5','名单手机号','名单渠道类型','名单渠道明细','名单渠道明细ID');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1"  , "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1"   , "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $all_num = $this->DataControl->selectClear("SELECT c.client_id,c.client_gmcdistributionstatus, 
        (select t.track_followuptime from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_followuptime,
        (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1'  and st.is_gmctocrmschool = '1'  order by st.schoolenter_id desc limit 0,1  ) as schoolenter_id,
        (select count(t.track_id) from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') ) as  track_count
        FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
        WHERE {$datawhere} GROUP BY c.client_id  HAVING {$having}");
        $allnums = is_array($all_num) ? count($all_num) : 0;

        if ($clientList) {
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => &$value) {
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                }
                $clientList[$key]['client_tracestatusname'] = $clientTracestatus[$value['client_tracestatus']];
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                }
            }

            $result = array();
            $result["field"] = $field;
            $result['count'] = $allnums;
            $result['datalist'] = $clientList?$clientList:array();

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;

        }else{
            $result = array();
            $result["field"] = $field;
            $result['count'] = 0;
            $result['datalist'] = array();

            $this->error = 1;
            $this->errortip = "暂时没有数据";
            $this->result = $result;
            return false;
        }
    }


    //获取名单跟进 页切的统计数字
    public function getClientFollowUpNum($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class from gmc_staffer_postbe as p where  p.postrole_id > '0'  and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id = '{$paramArray['re_postbe_id']}' ");
            if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
                $this->error = 1;
                $this->errortip = "您的职务没有集团招生管理权限，无权查看！";
                return false;
            }
        }
        //因为少了渠道出不来数据
        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  ";//AND p.school_id = '0'

        $having = "1=1";
        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";
        $havingsql = '';
        $levellist = $this->DataControl->selectClear("select * from gmc_code_intentlevel where company_id = '{$paramArray['company_id']}'");
        if($levellist){
            $someHave = "";
            foreach ($levellist as $levelVar){
                if($levelVar['intentlevel_trackday'] > 0 ){
                    $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                    if($someHave){
                        $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime < '{$stime}' ) ";
                    }else{
                        $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime < '{$stime}' ) ";
                    }
                }
            }
        }
        $having .= $someHave? " and ($someHave)  ":$someHave;
        $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isgmcactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";

        $markerwhere = "1";
        $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
        //管辖的人
        if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

            if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                    $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                    $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                    $markertList = $this->DataControl->selectClear($sql);
                    $arr_marketer_id = array_column($markertList, "marketer_id");
                    $str_marketer_id = implode(',', $arr_marketer_id);

                    $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                    $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
                } else {
                    $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                    $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                    $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
                }
            }
        } else {
            $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
            $markertList = $this->DataControl->selectClear($sql);
            $arr_marketer_id = array_column($markertList, "marketer_id");
            $str_marketer_id = implode(',', $arr_marketer_id);

            //包含管理员
            $accountmarker = $this->DataControl->selectClear("select m.marketer_id from smc_staffer as s 
                            left join crm_marketer as m ON s.staffer_id = m.staffer_id 
                            where s.account_class = '1' and s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' and m.marketer_id > 0 ");
            if (is_array($accountmarker)) {
                $arr_accountmarketer_id = array_column($accountmarker, "marketer_id");
                $str_accountmarketer_id = implode(',', $arr_accountmarketer_id);
                $str_marketer_id .= "," . $str_accountmarketer_id;
            }
            $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
            $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
        }

        $fields = "c.client_id,{$havingsql}c.client_intention_level,c.client_gmcdistributionstatus, 
         (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id";
        $sql = "SELECT {$fields}
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
WHERE {$datawhere} GROUP BY c.client_id  HAVING {$having} ";
        $yuqiList = $this->DataControl->selectClear($sql);
        $yuqiNum = $yuqiList?count($yuqiList):0;


        $having = "1=1";
        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";
        $havingsql = '';
        $levellist = $this->DataControl->selectClear("select * from gmc_code_intentlevel where company_id = '{$paramArray['company_id']}'");
        if($levellist){
            $someHave = "";
            foreach ($levellist as $levelVar){
                if($levelVar['intentlevel_warningday'] > 0 ){
                    $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                    $etime = strtotime(date('Y-m-d',time()-(86400*($levelVar['intentlevel_trackday'] - $levelVar['intentlevel_warningday']))));
                    if($someHave){
                        $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                    }else{
                        $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                    }
                }
            }
        }
        $having .= $someHave? " and ($someHave)  ":$someHave;
        $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isgmcactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";

        $sql = "SELECT {$fields}
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
WHERE {$datawhere} GROUP BY c.client_id  HAVING {$having} ";
        $yujingList = $this->DataControl->selectClear($sql);
        $yujingNum = $yujingList?count($yujingList):0;

        $data = array();
        $data['yuqi'] = $yuqiNum;
        $data['yujing'] = $yujingNum;

        $field = [
            'yuqi' => '逾期',
            'yujing' => '预警',
        ];

        $result = array();
        $result['field'] = $field;
        $result['list'] = $data;

        $this->error = 0;
        $this->errortip = "统计信息！";
        $this->result = $result;
        return false;
    }


    /**
     * 集团名单跟进列表
     * @param $paramArray
     * @return array
     */
    public function getClientFollowUpList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class from gmc_staffer_postbe as p where  p.postrole_id > '0'  and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id = '{$paramArray['re_postbe_id']}' ");
            if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
                $this->error = 1;
                $this->errortip = "您的职务没有集团招生管理权限，无权查看！";
                return false;
            }
        }
        //因为少了渠道出不来数据
        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  ";//AND p.school_id = '0'
        $datawheres = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  ";//AND p.school_id = '0'
//        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  ";//AND p.school_id = '0'
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        //意向星级
        if (isset($paramArray['client_intention_level']) && $paramArray['client_intention_level'] != '') {
            $datawhere .= " and c.client_intention_level ='{$paramArray['client_intention_level']}' ";
        }

        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] >= "0") {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        //请勿改，改之前问
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
//        //接通状态 0未接通 1接通有效 2接通无效
//        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 0 && $paramArray['client_answerphone'] !== '') {
//            $datawhere .= " and c.client_answerphone ='{$paramArray['client_answerphone']}'";
//        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 1) {
//            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_level >= 3";
//        } elseif (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] == 2) {
//            $datawhere .= " and c.client_answerphone = 1 and c.client_intention_level < 3";
//        }
        //是否接通电话
        if (isset($paramArray['client_answerphone']) && $paramArray['client_answerphone'] !== '') {
            $datawhere .= " and c.client_answerphone ='{$paramArray['client_answerphone']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }

        $havingsql = '';
        $having = "1=1";
        //跟踪状态：0待跟踪1持续跟踪2已邀约3已视听4已转正-1无意向-2无效名单  -----------  改展示逻辑了
        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus ='{$paramArray['client_tracestatus']}' ";
        }

        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";
        //最后跟踪时间    track_createtime
        if (isset($paramArray['track_starttime']) && $paramArray['track_starttime'] != '') {
            $track_starttime = strtotime($paramArray['track_starttime']);
            $having .= " and track_lasttime >= '{$track_starttime}'";
        }
        if (isset($paramArray['track_endtime']) && $paramArray['track_endtime'] != '') {
            $track_endtime = strtotime($paramArray['track_endtime']) + 24 * 3600 - 1;
            $having .= " and track_lasttime <= '{$track_endtime}'";
        }
        //principal_createtime 负责人分配时间
        if (isset($paramArray['principal_starttime']) && $paramArray['principal_starttime'] != '') {
            $principal_starttime = strtotime($paramArray['principal_starttime']);
            $datawhere .= " and p.principal_createtime >= '{$principal_starttime}'";
        }
        if (isset($paramArray['principal_endtime']) && $paramArray['principal_endtime'] != '') {
            $principal_endtime = strtotime($paramArray['principal_endtime']) + 24 * 3600 - 1;
            $datawhere .= " and p.principal_createtime <= '{$principal_endtime}'";
        }
//        //跟进次数
//        if (isset($paramArray['track_count']) && $paramArray['track_count'] != '') {
//            $having .= " and track_count <= '{$paramArray['track_count']}'";
//        }
        //跟进次数    track_count_type 1 为  ＜、  2 为  ≤、  0 为 ＝
        if (isset($paramArray['track_count']) && $paramArray['track_count'] != '') {
            if($paramArray['track_count_type'] == '1'){
                $having .= " and track_count < '{$paramArray['track_count']}'";
            }elseif($paramArray['track_count_type'] == '2'){
                $having .= " and track_count <= '{$paramArray['track_count']}'";
            }elseif($paramArray['track_count_type'] == '0'){
                $having .= " and track_count = '{$paramArray['track_count']}'";
            }
            $havingsql .= " (select count(t.track_id) from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') ) as  track_count, ";
        }
        //未柜询，柜询待确认，已柜询
        /* if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
             $datawhere .= " and c.client_id NOT IN ( SELECT i.client_id FROM crm_client_invite AS i WHERE i.company_id = '{$paramArray['company_id']}' AND i.invite_isvisit <> '0')";
         }elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
             $datawhere .= " and c.client_id IN ( SELECT i.client_id FROM crm_client_invite AS i WHERE i.company_id = '{$paramArray['company_id']}' AND i.invite_isvisit = '0')";
         }elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
             $datawhere .= " and c.client_id IN ( SELECT i.client_id FROM crm_client_invite AS i WHERE i.company_id = '{$paramArray['company_id']}' AND i.invite_isvisit = '1')";
         }
         //未试听，试听待确认，已试听
         if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
             $datawhere .= " and c.client_id NOT IN ( SELECT a.client_id FROM crm_client_audition AS a WHERE a.company_id = '{$paramArray['company_id']}' AND a.audition_isvisit <> '0')";
         }elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
             $datawhere .= " and c.client_id IN ( SELECT a.client_id FROM crm_client_audition AS a WHERE a.company_id = '{$paramArray['company_id']}' AND a.audition_isvisit <> '0')";
         }elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
             $datawhere .= " and c.client_id IN ( SELECT a.client_id FROM crm_client_audition AS a WHERE a.company_id = '{$paramArray['company_id']}' AND a.audition_isvisit <> '1')";
         }*/

        //未柜询，柜询待确认，已柜询
        if (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '0') {//未柜询
            $having .= " and invite_idthree is null and active_invite_id is null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,  ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '1') {//柜询待确认
            $having .= " and invite_idthree is not null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,  ";
        } elseif (isset($paramArray['ishaveinvite']) && $paramArray['ishaveinvite'] == '2') {//已柜询
            $having .= " and active_invite_id is not null ";
            $havingsql .= " (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,   ";
        }
        //未试听，试听待确认，已试听
        if (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '0') {//未试听
            $having .= " and active_audition_id is null and audition_idthree is null  ";
            $havingsql .= " (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id, 
        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree, ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '1') {//试听待确认
            $having .= " and audition_idthree is not null ";
            $havingsql .= "  (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree, ";
        } elseif (isset($paramArray['ishaveaudition']) && $paramArray['ishaveaudition'] == '2') {//已试听
            $having .= " and active_audition_id is not null ";
            $havingsql .= " (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,";
        }

        $levellist = $this->DataControl->selectClear("select * from gmc_code_intentlevel where company_id = '{$paramArray['company_id']}'");
        //判断 提箱低频状态   overduetype  1 常规跟进  2 跟进逾期   3 低频跟进预警名单    （常规跟进+跟进逾期=所有意向客户）
        if (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '1') {
            if($levellist){
                $someHave = "";
                foreach ($levellist as $levelVar){
                    if($levelVar['intentlevel_trackday'] > 0 ){
                        $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                        if($someHave){
                            $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and (last_zhu_track_createtime >= '{$stime}' or last_zhu_track_createtime = 1) ) ";
                        }else{
                            $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and (last_zhu_track_createtime >= '{$stime}' or last_zhu_track_createtime = 1) ) ";
                        }
                    }
                }
            }
            $having .= $someHave? " and ( c.client_intention_level = '0' or $someHave)  ":$someHave;
            $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isgmcactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";
        }elseif (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '2') {
            if($levellist){
                $someHave = "";
                foreach ($levellist as $levelVar){
                    if($levelVar['intentlevel_trackday'] > 0 ){
                        $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                        if($someHave){
                            $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime < '{$stime}' ) ";
                        }else{
                            $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime < '{$stime}' ) ";
                        }
                    }
                }
            }
            $having .= $someHave? " and ($someHave)  ":$someHave;
            $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isgmcactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";
        }elseif (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '3') {
            if($levellist){
                $someHave = "";
                foreach ($levellist as $levelVar){
                    if($levelVar['intentlevel_warningday'] > 0 ){
                        $stime = strtotime(date('Y-m-d',time()-(86400*$levelVar['intentlevel_trackday'])));
                        $etime = strtotime(date('Y-m-d',time()-(86400*($levelVar['intentlevel_trackday'] - $levelVar['intentlevel_warningday']))));
                        if($someHave){
                            $someHave .= " or (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                        }else{
                            $someHave .= " (c.client_intention_level = '{$levelVar['intentlevel_starnum']}' and last_zhu_track_createtime > 1  and last_zhu_track_createtime >= '{$stime}' and last_zhu_track_createtime < '{$etime}' ) ";
                        }
                    }
                }
            }
            $having .= $someHave? " and ($someHave)  ":$someHave;
            $havingsql .= "ifnull((select x.track_createtime from crm_client_track as x where x.client_id = c.client_id and x.track_isgmcactive = '1' order by x.track_createtime desc limit 0,1 ),1) as last_zhu_track_createtime,";
        }



        //集团负责人
        if (isset($paramArray['main_staffer_id']) && $paramArray['main_staffer_id'] != '') {
            $datawhere .= " and m.staffer_id = '{$paramArray['main_staffer_id']}'";
        }

        $markerwhere = "1";
        $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
        //管辖的人
        if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

            if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                    $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                    $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                    $markertList = $this->DataControl->selectClear($sql);
                    $arr_marketer_id = array_column($markertList, "marketer_id");
                    $str_marketer_id = implode(',', $arr_marketer_id);

                    $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                    $datawheres .= " and p.marketer_id in ({$str_marketer_id}) ";
                    $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
                } else {
                    $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                    $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                    $datawheres .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                    $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
                }
            }
        } else {
            $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
            $markertList = $this->DataControl->selectClear($sql);
            $arr_marketer_id = array_column($markertList, "marketer_id");
            $str_marketer_id = implode(',', $arr_marketer_id);

            //包含管理员
            $accountmarker = $this->DataControl->selectClear("select m.marketer_id from smc_staffer as s 
                            left join crm_marketer as m ON s.staffer_id = m.staffer_id 
                            where s.account_class = '1' and s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' and m.marketer_id > 0 ");
            if (is_array($accountmarker)) {
                $arr_accountmarketer_id = array_column($accountmarker, "marketer_id");
                $str_accountmarketer_id = implode(',', $arr_accountmarketer_id);
                $str_marketer_id .= "," . $str_accountmarketer_id;
            }
            $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
            $datawheres .= " and p.marketer_id in ({$str_marketer_id}) ";
            $markerwhere = " t.marketer_id = '{$markertOne['marketer_id']}' ";
        }

        $remind_time = ' ';
        if (isset($paramArray['remind_time']) && $paramArray['remind_time'] != '') {
            $remind_time = " and t.track_followuptime = '{$paramArray['remind_time']}'";
        }else{
            $paramArray['remind_time'] = date("Y-m-d");
            $remind_time = " and t.track_followuptime = '{$paramArray['remind_time']}'";
        }

//        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '-1' limit 0,1) as invite_idtwo,
//        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '-1' limit 0,1) as audition_idtwo,
//        (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,
        $remind_num = $this->DataControl->selectClear("SELECT c.client_id,c.client_gmcdistributionstatus,c.client_intention_level,{$havingsql}
        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_lasttime,
        (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1'  and st.is_gmctocrmschool = '1'  order by st.schoolenter_id desc limit 0,1  ) as schoolenter_id, 
        (select t.track_id from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} {$remind_time} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_followuptime,
        (select t.track_id from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as the_last_track_id
        FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
        WHERE {$datawheres} GROUP BY c.client_id  HAVING {$having} and track_followuptime > 1 and track_followuptime >= the_last_track_id ");
        $remindnums = is_array($remind_num) ? count($remind_num) : 0;
        // (select t.track_followuptime from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_followuptime,  提醒数量错误

        //track_followuptype 跟进提醒
        if (isset($paramArray['track_followuptype']) && $paramArray['track_followuptype'] != '' && isset($paramArray['remind_time']) && $paramArray['remind_time'] != '' ) {
            $having .= " and track_followuptime > 1 and track_followuptime >= the_last_track_id";
        }else{
            $remind_time = ' ';
        }

        //智能排序
        if ($paramArray['orderbyname'] == 'asc') {
            $orderby = "order by c.client_createtime ASC  ";
        } elseif ($paramArray['orderbyname'] == 'desc') {
            $orderby = "order by track_lasttime DESC,c.client_updatetime DESC ";
        } else {
            //$orderby = "order by c.client_intention_level desc,track_count DESC,c.client_createtime ASC,track_lasttime ASC";
            $orderby = "order by p.principal_createtime DESC";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
//        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
//        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
//        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree,
//        (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,

        $fields = "c.client_id, c.client_cnname, c.client_enname, c.client_age, c.client_birthday, c.client_sex, c.client_mobile,{$havingsql}
         c.client_img, c.client_tracestatus, c.client_patriarchname,c.province_id, c.city_id, c.area_id, c.client_remark, c.client_intention_level,
         c.channel_id, c.client_source, c.client_gmcmarket,c.client_gmcdistributionstatus
         ,from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime
         ,from_unixtime(c.client_updatetime, '%Y-%m-%d') AS client_updatetime
         ,c.client_tag, c.client_answerphone, c.client_frompage,l.channel_maxday, l.channel_minday, l.channel_medianame, l.channel_name,p.principal_createtime,
         (select f.family_cnname from crm_client_family as f where f.client_id = c.client_id and f.family_isdefault = '1' limit 0,1 ) as client_mainfamilyname,
         (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id limit 0,1) as activity_name,
         (select r.region_name from smc_code_region as r where c.province_id = r.region_id limit 0,1) AS province_name,
         (select r.region_name from smc_code_region as r where c.city_id = r.region_id limit 0,1) AS city_name,
         (select r.region_name from smc_code_region as r where c.area_id = r.region_id limit 0,1) AS area_name,      
         (select t.track_note from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_lasttracknote,
         (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
         (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_lasttime,
         (select t.track_id from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} {$remind_time} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_followuptime,
        (select t.track_id from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as the_last_track_id,
         m.marketer_id,m.marketer_name as principal_name,
         (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id";
        $sql = "SELECT {$fields}
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
WHERE {$datawhere} GROUP BY c.client_id  HAVING {$having} {$orderby} LIMIT {$pagestart},{$num} ";
        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => &$value) {
                $alltracks = $this->DataControl->selectClear(" select t.track_createtime,t.track_linktype,t.track_followmode,t.track_note from crm_client_track as t where t.client_id = '{$value['client_id']}' AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_createtime desc ");
                if($alltracks){
                    foreach ($alltracks as &$alltracksvar) {

                        $alltracksvar['warninginfo'] = '';
                        if (isset($paramArray['overduetype']) && ($paramArray['overduetype'] == '2' || $paramArray['overduetype'] == '3') ){
                            if($levellist){
                                $trackdayArr = array_column($levellist,'intentlevel_trackday','intentlevel_starnum');
                                $warningdayArr = array_column($levellist,'intentlevel_warningday','intentlevel_starnum');

                                if (isset($paramArray['overduetype']) && $paramArray['overduetype'] == '2'){
                                    $YuQiJieDian = $alltracksvar['last_zhu_track_createtime'] + $trackdayArr[$alltracksvar['client_intention_level']]*86400;
                                    $YuQi =  time() - $YuQiJieDian;
                                    $yuqiday = ceil($YuQi/86400);
                                    $alltracksvar['warninginfo'] = "当前{$alltracksvar['client_intention_level']}星名单逾期{$yuqiday}天跟踪，被标记低跟进名单，请尽快跟进！";
                                }elseif(isset($paramArray['overduetype']) && $paramArray['overduetype'] == '3'){
                                    $YuJingJieDian = strtotime(date('Y-m-d',$alltracksvar['last_zhu_track_createtime'])) + 86399 +  ( $trackdayArr[$alltracksvar['client_intention_level']] * 86400 );
                                    $YuJing =  $YuJingJieDian - time();
                                    $YuJingday = ceil($YuJing/86400);
                                    $alltracksvar['warninginfo'] = "当前{$alltracksvar['client_intention_level']}星名单还剩{$YuJingday}天进入低跟进，请尽快跟进！";
                                }
                            }
                        }

                        if ($alltracksvar['track_createtime']) {
                            $alltracksvar['track_createtime'] = date("Y-m-d H:i:s", $alltracksvar['track_createtime']);
                        }
                        if ($alltracksvar['track_followmode'] == 0) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("普通回访");
                        } elseif ($alltracksvar['track_followmode'] == 1) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("柜询");
                        } elseif ($alltracksvar['track_followmode'] == 2) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("试听");
                        } elseif ($alltracksvar['track_followmode'] == 3) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("转正");
                        } elseif ($alltracksvar['track_followmode'] == 4) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("更新本校无意向名单");
                        } elseif ($alltracksvar['track_followmode'] == 5) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("转校");
                        } elseif ($alltracksvar['track_followmode'] == 6) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("毛名单转有效");
                        } elseif ($alltracksvar['track_followmode'] == 7) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("有效转毛名单");
                        } elseif ($alltracksvar['track_followmode'] == -1) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("无意向跟进");
                        } elseif ($alltracksvar['track_followmode'] == -2) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("主管确认流失");
                        } elseif ($alltracksvar['track_followmode'] == -3) {
                            $alltracksvar['track_followmode_name'] = $this->LgStringSwitch("无效化名单");
                        }
                    }
                }

                $takcsData = array();
                $takcsData['txt'] = $value['track_lasttracknote'] ? $value['track_lasttracknote'] : '--';
                $takcsData['list'] = $alltracks;
                $value['track_alltracknote'] = $takcsData;

                if (is_null($value['client_patriarchname']) || $value['client_patriarchname'] == '') {
                    $value['client_patriarchname'] = is_null($value['client_mainfamilyname']) ? "--" : $value['client_mainfamilyname'];
                }
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                }
                $clientList[$key]['client_tracestatusname'] = $clientTracestatus[$value['client_tracestatus']];
                if ($value['client_answerphone'] == 1) {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                    if ($value['client_intention_level'] >= 3) {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                    } else {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                    }
                } else {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $clientList[$key]['course_cnname'] = array();
                }
                if (!is_null($value['track_lasttime'])) {
                    $clientList[$key]['track_lasttime'] = date("Y-m-d H:i:s", $value['track_lasttime']);
                } else {
                    $clientList[$key]['track_lasttime'] = '--';
                }

                $clientList[$key]['principal_createtime'] = (!is_null($value['principal_createtime'])) ? date("Y-m-d H:i:s", $value['principal_createtime']) : '--';

                $clientList[$key]['track_lasttracknote'] = $value['track_lasttracknote'] ? ($value['track_lasttracknote'] . "（{$clientList[$key]['track_lasttime']}）") : '--';
                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                if ($value['channel_maxday'] == 0 || $time <= $max) {
                    $value['status'] = 1;
                } else {
                    $value['status'] = 0;
                }
                if ($value['channellog_id'] == null) {
                    $value['apply'] = '1';
                } else {
                    $value['apply'] = '0';
                }
                $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
                if ($staffer) {
                    $marketer_id = $staffer['marketer_id'];
                } else {
                    $marketer_id = 0;
                }
                if ($this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and FROM_UNIXTIME(dialrecord_createtime,'%Y-%m-%d') = CURDATE() and dialrecord_type = 0")) {
                    $value['dialrecord'] = '1';
                } else {
                    $value['dialrecord'] = '0';
                }
            }
        }

//        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '1' limit 0,1) as active_invite_id,
//        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '0' limit 0,1) as invite_idthree,
//        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '1' limit 0,1) as active_audition_id,
//        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '0' limit 0,1) as audition_idthree,
//        (select i.invite_id from crm_client_invite as i where i.client_id = c.client_id and i.invite_isvisit = '-1' limit 0,1) as invite_idtwo,
//        (select a.audition_id from crm_client_audition as a where a.client_id = c.client_id and a.audition_isvisit = '-1' limit 0,1) as audition_idtwo,
//        (select t.track_id from crm_client_track as t where t.client_id = c.client_id and (t.track_isactive = '1' or t.track_isgmcactive = '1') limit 0,1) as active_track_id,

        $all_num = $this->DataControl->selectClear("SELECT c.client_id,c.client_gmcdistributionstatus,c.client_intention_level,{$havingsql}
        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_lasttime,  
         (select t.track_id from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} {$remind_time} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as track_followuptime,
        (select t.track_id from crm_client_track as t where t.client_id = c.client_id AND {$markerwhere} AND (t.track_isgmcactive = '1' or t.track_isactive = '1') order by t.track_id desc limit 0,1) as the_last_track_id, 
         (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id,
        (select count(t.track_id) from crm_client_track as t where t.client_id = c.client_id AND (t.track_isgmcactive = '1' or t.track_isactive = '1') ) as  track_count
        FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l
        WHERE {$datawhere} GROUP BY c.client_id  HAVING {$having}");
        $allnums = is_array($all_num) ? count($all_num) : 0;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['remind_num'] = $remindnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }


    function inviteTimetable($request)
    {

        if (!isset($request['month']) || $request['month'] == '') {
            $request['month'] = date("Y-m");
        }

        $start = date("Y-m-", strtotime($request['month'])) . '01';

        $end = date("Y-m-t", strtotime($request['month']));

        $weekstart = date("w", strtotime($start));
        $weekend = date("w", strtotime($end));

        $start_num = $weekstart - 1;
        $end_num = 7 - $weekend;

        $starttime = date("Y-m-d", strtotime('-' . $start_num . ' day', strtotime($start)));
        $endtime = date("Y-m-d", strtotime('+' . $end_num . ' day', strtotime($end)));

        $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");

        $datawhere = " 1 ";
        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
        }

        if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}' ";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['course_id']) and $request['course_id'] !== "") {
            $datawhere .= " and co.course_id = '{$request['course_id']}' ";
        }

        if (isset($request['hour_way']) and $request['hour_way'] !== "") {
            $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
        }

        if (isset($request['school_id']) and $request['school_id'] !== "") {
            $datawhere .= " and c.school_id ='{$request['school_id']}'";
        } else {
            $this->error = 1;
            $this->errortip = "请选择学校";
            return false;
        }

        $sql = " select c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,co.course_inclasstype,ch.hour_way,ch.hour_number,co.course_openclasstype,c.class_appointnum,ca.coursecat_branch  
                ,if(co.course_inclasstype=3 and co.course_openclasstype=1,ifnull((select count(cb.audition_id) from view_crm_audition as cb where cb.audition_isvisit>=0 and cb.hour_id=(select h.hour_id from smc_class_hour as h where h.class_id=c.class_id and h.hour_ischecking <> '-1' order by h.hour_day asc,h.hour_lessontimes asc limit 0,1)),0),ifnull((select count(cb.audition_id) from view_crm_audition as cb where cb.hour_id=ch.hour_id and cb.audition_isvisit>=0),0)) as bookingNum
     from smc_class_hour AS ch
     LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
     left join smc_course as co on co.course_id=c.course_id
     left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
     left join smc_code_coursecat as ca on ca.coursecat_id=co.coursecat_id
     where {$datawhere}
     and ch.hour_ischecking <> '-1'
     and  ch.hour_day >='{$starttime}'  and ch.hour_day <= '{$endtime}' and c.class_status <> '-2' 
     order by ch.hour_day asc,ch.hour_starttime asc
    ";
        $hourList = $this->DataControl->selectClear($sql);

        $time_start = strtotime($starttime);
        $time_end = strtotime($endtime);
        $dateList = array();
        while ($time_start <= $time_end) {
            $dateList[] = date('Y-m-d', $time_start);
            $time_start = strtotime('+1 day', $time_start);
        }

        $data = array();
        if ($hourList) {
            foreach ($dateList as $date) {
                if ($date < $start || $date > $end) {
                    if ($date < $start) {
                        $weeknum = '1';
                    } else {
                        $weeknum = ceil((date("d", strtotime($end)) + $start_num + 1) / 7);
                    }
                } else {
                    $weeknum = ceil((date("d", strtotime($date)) + $start_num) / 7);
                }

                foreach ($hourList as $hourOne) {
                    if ($date == $hourOne['hour_day']) {
                        $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['day'] = date('d', strtotime($date)) . '日';
                        $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['month'] = date('Y', strtotime($date)) . '年' . date('m', strtotime($date)) . '月' . date('d', strtotime($date));

                        $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['is_this_month'] = date('Y-m', strtotime($date)) == $request['month'] ? 1 : 0;

                        $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['info'][] = $hourOne;
                    } else {
                        if (!isset($data[$weeknum][$enweekarray[date("w", strtotime($date))]]['info'])) {
                            $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['day'] = date('d', strtotime($date)) . '日';
                            $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['month'] = date('Y', strtotime($date)) . '年' . date('m', strtotime($date)) . '月' . date('d', strtotime($date));

                            $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['is_this_month'] = date('Y-m', strtotime($date)) == $request['month'] ? 1 : 0;

                            $data[$weeknum][$enweekarray[date("w", strtotime($date))]]['info'] = array();
                        }
                    }
                }
            }
        }

        return $data;

    }


    /**
     * 批量设置已审核
     * @param $paramArray
     * @return false
     */
    public function batchSetApproved($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "用户参数无效";
            return false;
        } else {
            $stafferInfo = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if ($stafferInfo) {
                $marketer_id = $stafferInfo['marketer_id'];
                $marketer_name = $stafferInfo['marketer_name'];
            } else {
                $marketer_id = 0;
                $marketer_name = "系统";
            }
            foreach ($clients_array as $key => $value) {
                $updateResult = $this->DataControl->updateData("crm_client", " client_id='{$value['client_id']}' ", [
                    'client_ischaserlapsed' => 1,
                    'client_updatetime' => time()
                ]);
                if ($updateResult != false) {
                    $this->DataControl->insertData("crm_client_track", [
                        'client_id' => $value['client_id'],
                        'school_id' => 0,
                        'marketer_id' => $marketer_id,
                        'marketer_name' => $marketer_name,
                        'track_intention_level' => 0,
                        'track_linktype' => "主管已审核",
                        'track_followmode' => '-2',
                        'track_state' => '-1',
                        'track_type' => 1,
                        'track_isreading' => 1,
                        "track_readtime" => time(),
                        "track_createtime" => time()

                    ]);

                }

            }
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '主管已审核', dataEncode($paramArray));
            $this->error = 0;
            $this->errortip = "设为已审核操作成功";
            return true;
        }

    }

    //可选的分配负责人列表
    function getGmccrmPersonList($paramArray)
    {
        $postbeOne = $this->DataControl->selectOne("select postbe_id,postbe_isgmccrm,postbe_gmccrmlevel,organize_id,company_id from gmc_staffer_postbe
where company_id = '{$paramArray['company_id']}' and staffer_id = '{$paramArray['staffer_id']}' AND postbe_id = '{$paramArray['re_postbe_id']}'");

        if (($postbeOne && $postbeOne['postbe_isgmccrm'] == '1') || $this->stafferOne['account_class'] == '1') {
            if ($this->stafferOne['account_class'] == '1') {
                $datawhere = " p.postbe_isgmccrm = '1' AND s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' ";
            } else {
                if ($postbeOne['postbe_gmccrmlevel'] == '1') {
                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
                    WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE postbe_id = '{$postbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";
//                $datawhere = " ( ( p.organize_id = '{$postbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') {$organizeWhere} ) ";
                    $datawhere = " ( 
                    ( p.organize_id = '{$postbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$postbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) and s.staffer_leave = '0' ";
                } else {
                    $datawhere = " ( p.organize_id = '{$postbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0' )  and s.staffer_leave = '0' ";
                }
            }
            $marketer = $this->DataControl->getFieldOne("crm_marketer", "staffer_id", "marketer_id = '{$paramArray['marketer_id']}'");
            if ($marketer['staffer_id']) {
                $datawhere .= " and s.staffer_id <> '{$marketer['staffer_id']}' ";
            }
            $sql = "SELECT p.staffer_id,s.staffer_branch,s.staffer_cnname,s.staffer_enname 
                FROM gmc_staffer_postbe AS p LEFT JOIN smc_staffer as s ON s.staffer_id = p.staffer_id WHERE {$datawhere} and s.staffer_leave = '0' and s.account_class = 0
                group by p.staffer_id";
            $stafferList = $this->DataControl->selectClear($sql);

            $field = array();
            $field['staffer_id'] = '教师ID';
            $field['staffer_branch'] = '教师编号';
            $field['staffer_cnname'] = '中文名称';
            $field['staffer_cnname'] = '英文名称';

            $result = array();
            $result['field'] = $field;

            if ($stafferList) {
                $result['list'] = $stafferList;
                $res = array('error' => '0', 'errortip' => "负责人获取成功！", 'result' => $result);
            } else {
                $result['list'] = array();
                $res = array('error' => '1', 'errortip' => "暂无负责人信息！", 'result' => array());
            }
            return $res;
        } else {
            $res = array('error' => '1', 'errortip' => "未查询到任职明细！", 'result' => array());
            return $res;
        }

    }

    /**
     * @param $client_id
     * @return bool
     *  新增一个客户的分配记录-解除记录 -由负责人表查询
     *  -1 解除所有人
     *   解除负责人状态一定要放在调用这个方法后面
     */
    //	 											负责人			操作人
    function addRemoveAllotLog($client_id, $main_marketer_id, $marketer_id, $school_id = '0')
    {
        $where = " 1 and  principal_leave = 0 ";
        if ($main_marketer_id == -1) {
            $where .= " and client_id='{$client_id}'";
        } else {
            $where .= " and client_id='{$client_id}' and marketer_id='{$main_marketer_id}' ";
        }
        $clientPrincipalList = $this->DataControl->getList("crm_client_principal", $where, "order by principal_createtime DESC");
        if ($clientPrincipalList) {
            $dataAllotlog = array();
            $dataAllotlog['allotlog_status'] = 0;
            $dataAllotlog['allotlog_removetime'] = time();
            foreach ($clientPrincipalList as $key => $value) {
                if ($this->DataControl->updateData('crm_client_allotlog', "allotlog_status =1 and client_id='{$client_id}' and allot_marketer_id='{$value['marketer_id']}'", $dataAllotlog)) {
                    $res = true;
                } else {
                    $res = false;
                }
            }
            return $res;
        } else {
            return true;
        }
    }

    //批量转为待分配名单/无意向/无效
    function changePrincipalClientApi($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        } else {
            $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if (!$markOne) {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $markOne['marketer_id'] = $getaddmarkertOne['marketer_id'];
                $markOne['marketer_name'] = $getaddmarkertOne['marketer_name'];
            }
            $TmkBatchId = '';
            if (!$this->DataControl->selectOne("select tmkbatch_id from crm_client_tmkbatch where tmkbatch_number='{$paramArray['client_tmkbatch']}' and company_id='{$paramArray['company_id']}' limit 0,1")) {
                $TmkBatchId = $paramArray['client_tmkbatch'];

                $this->DataControl->insertData('crm_client_tmkbatch', ["company_id" => "{$paramArray['company_id']}", "tmkbatch_number" => "{$paramArray['client_tmkbatch']}"]);
            }

            $scc = '0';
            $fail = '0';
            foreach ($clients_array as $key => $value) {

                $GmcPrincipalOne = $this->DataControl->selectOne(" select principal_id,principal_createtime,principal_updatatime  from crm_client_principal where client_id = '{$value['client_id']}' and school_id = '0' and  principal_leave = 0 order by principal_createtime desc limit 0,1 ");
                $inviteOne = $this->DataControl->selectOne(" select school_id from crm_client_invite where client_id = '{$value['client_id']}' and school_id > '0' and invite_createtime > '{$GmcPrincipalOne['principal_createtime']}' and invite_createtime > '{$GmcPrincipalOne['principal_updatatime']}' and invite_isvisit = '0' ");
                $auditionOne = $this->DataControl->selectOne(" select school_id from crm_client_audition where client_id = '{$value['client_id']}' and school_id > '0' and audition_createtime > '{$GmcPrincipalOne['principal_createtime']}'  and audition_createtime > '{$GmcPrincipalOne['principal_updatatime']}' and audition_isvisit = '0' ");

                if ($inviteOne || $auditionOne) {
                    $fail++;
                } else {
                    if ($paramArray['re_postbe_id'] > 0) {
                        $postbe = $this->DataControl->selectOne(" select postbe_isgmccrm,postbe_gmccrmlevel from gmc_staffer_postbe where postbe_id = '{$paramArray['re_postbe_id']}' ");
                        if ($postbe['postbe_isgmccrm'] != '1' || $postbe['postbe_gmccrmlevel'] != '1') {
                            if ($paramArray['type'] == '-1' || $paramArray['type'] == '-2') {
                                if ((!$this->gmcVerificationTrack($value['client_id'], $markOne['marketer_id'], 1)) && $this->stafferOne['account_class'] == 0) {
                                    $this->error = 1;
                                    $this->errortip = "您不是主负责人,没有流转权限";
                                    return false;
                                }
                            }
                        }
                    }

                    //更新客户状态
                    $dataClient = array();
                    $dataClient['client_tmkbatch'] = $TmkBatchId;//增加批次编号
                    $dataClient['client_updatetime'] = time();
                    if ($paramArray['type'] == -1) {
                        $dataClient['client_tracestatus'] = -1;
                    } elseif ($paramArray['type'] == -2) {
                        $dataClient['client_gmcdistributionstatus'] = 0;
                        $dataClient['client_tracestatus'] = -2;
                        $dataClient['client_distributionstatus'] = 0;
                        $dataClient['client_isinvalidreview'] = 0;
                        $dataClient['invalidnote_code'] = $paramArray['invalidnote_code'];
                    } else {
                        $dataClient['client_gmcdistributionstatus'] = 0;
                        $dataClient['client_tracestatus'] = 0;
                        $dataClient['client_distributionstatus'] = 0;

                        $dataClient['client_ischaserlapsed'] = 0;
                        $dataClient['client_isinvalidreview'] = 0;

                        $dataClient['client_invalidmarketer_id'] = 0;
                        $dataClient['client_invalidreviewtime'] = '';
                        $dataClient['invalidnote_code'] = $paramArray['invalidnote_code']?$paramArray['invalidnote_code']:'';

                        $channelOne = $this->DataControl->selectOne("select l.channel_quality from crm_client as c,crm_code_channel as l where c.client_id='{$value['client_id']}' and c.channel_id = l.channel_id ");
                        if ($channelOne['channel_quality'] == '1') {//有效
                            $dataClient['client_isgross'] = 0;
                        } elseif ($channelOne['channel_quality'] == '0') {//毛名单
                            $dataClient['client_isgross'] = 1;
                        }

                    }
                    if (!$this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $dataClient)) {
                        $this->error = 1;
                        $this->errortip = "更新客户状态失败";
                        return false;
                    }

                    //2024 看到跟进时无意向解除了负责人， 这里并没有解除，故放开
//                    if ($paramArray['type'] != '-1') {
                        //增加分配日志 解除负责人的解除日志
                        if (!$this->addRemoveAllotLog($value['client_id'], '-1', $markOne['marketer_id'], '')) {
                            $this->error = 1;
                            $this->errortip = "增加分配日志:解除日志失败";
                            return false;
                        }

                        //解除负责人的负责状态  一定要先添加解除日志(陆晶备注的）
                        $dataPrincipal = array();
                        $dataPrincipal['principal_leave'] = 1;
                        $dataPrincipal['principal_updatatime'] = time();
                        if (!$this->DataControl->updateData('crm_client_principal', "client_id='{$value['client_id']}'  ", $dataPrincipal)) {
                            $this->error = 1;
                            $this->errortip = "解除负责人失败";
                            return false;
                        }
//                    }

                    //增加跟踪记录
                    $dataTrack = array();
                    $dataTrack['marketer_id'] = $markOne['marketer_id'];
                    $dataTrack['school_id'] = 0;
                    $dataTrack['track_createtime'] = time();
                    if ($paramArray['type'] == -1) {
                        $dataTrack['track_state'] = -1;
                        $dataTrack['track_followmode'] = -1;
                        $dataTrack['track_linktype'] = $this->LgStringSwitch("系统操作");
                        $dataTrack['track_note'] = $this->LgStringSwitch('系统流转（集团）:批量流转为无意向客户');
                    } elseif ($paramArray['type'] == -2) {
                        $dataTrack['track_state'] = -2;
                        $dataTrack['track_followmode'] = -3;
                        $dataTrack['track_invalidreason'] = $paramArray['invalidnote_reason'];
                        $dataTrack['track_linktype'] = $this->LgStringSwitch("系统操作");
                        $dataTrack['track_note'] = $this->LgStringSwitch('系统流转（集团）:批量流转为无效客户');
                    } else {
                        $dataTrack['track_state'] = 0;
                        $dataTrack['track_followmode'] = 0;
                        $dataTrack['track_validinc'] = 1;
                        $dataTrack['track_linktype'] = $this->LgStringSwitch("主管操作");
                        $dataTrack['track_note'] = $this->LgStringSwitch('系统流转（集团）:批量流转为有效名单');

                        $data = array();
                        $data['is_enterstatus'] = '-1';
                        $data['schoolenter_updatetime'] = time();
                        $this->DataControl->updateData("crm_client_schoolenter", "client_id = '{$value['client_id']}'", $data);
                    }
                    if ($paramArray['track_note'] != '') {
                        $dataTrack['track_note'] = $paramArray['track_note'];
                    }
                    $dataTrack['marketer_name'] = $markOne['marketer_name'];
                    $dataTrack['client_id'] = $value['client_id'];
                    $dataTrack['track_type'] = 1;
                    $dataTrack['track_initiative'] = 0;
                    if (!$this->DataControl->insertData('crm_client_track', $dataTrack)) {
                        $this->error = 1;
                        $this->errortip = "增加跟踪记录失败";
                        return false;
                    }
                    $scc++;
                }
            }

            if ($paramArray['type'] == -1) {
                $operationname = '批量流转为无意向客户';
            } elseif ($paramArray['type'] == -2) {
                $operationname = '批量流转为无效客户';
            } else {
                $operationname = '批量流转为有效名单';
            }
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单跟踪", $operationname, dataEncode($paramArray));

            if ($fail > 0) {
                $tip = "{$scc}条名单流转成功，{$fail}条名单已邀约至校区并且未确认不可更换负责人";
            } else {
                $tip = '流转成功';
            }
            $this->error = 0;
            $this->errortip = $tip;
            return false;
        }
    }
//    /**
//     * 集团名单 获取待审核无效名单
//     * @param $paramArray
//     * @return array
//     */
//    public function getVerifyInvalidClientList($paramArray)
//    {
//        if($paramArray['school_id'] == '' && $paramArray['client_isinvalidreview'] != '0'){
//            $this->error = 1;
//            $this->errortip = "请先选择学校！";
//            return false;
//        }
//
//        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
//        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-1'  ";
//
//        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
//            if ($paramArray['keyword'] == '###') {
//                $datawhere .= " and c.outthree_userid = ''";
//            } else {
//                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
//            }
//        }
//        //意向星级
//        if (isset($paramArray['client_intention_level'])) {
//            $datawhere .= " and c.client_intention_level ='{$paramArray['client_intention_level']}' ";
//        }
//        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
//            $datawhere = " e.school_id = '{$paramArray['school_id']}' ";
//        }
//        if (isset($paramArray['client_isinvalidreview']) && $paramArray['client_isinvalidreview'] != "") {
//            $datawhere .= " and c.client_isinvalidreview ='{$paramArray['client_isinvalidreview']}'";
//        }
//        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] == "0") {
//            $datawhere .= " and c.activity_id ='0'";
//        }
//        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] > "0") {
//            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}' ";
//        }
//        //请勿改，改之前问
//        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
//            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
//            if (is_array($channelArray) && count($channelArray) > 0) {
//                $channelstr = '';
//                foreach ($channelArray as $channelvar) {
//                    $channelstr .= "'" . $channelvar . "'" . ',';
//                }
//                $channelstr = substr($channelstr, 0, -1);
//                $datawhere .= " and c.channel_id in ({$channelstr}) ";
//            }
//        }
//        //请勿改，改之前问
//        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
//            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
//            if (is_array($commodeArray) && count($commodeArray) > 0) {
//                $commodestr = '';
//                foreach ($commodeArray as $commodevar) {
//                    $commodestr .= "'" . $commodevar . "'" . ',';
//
//                }
//                $commodestr = substr($commodestr, 0, -1);
//                $datawhere .= " and c.client_source in ({$commodestr}) ";
//            }
//        }
//        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
//            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
//        }
//        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
//            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
//        }
//
//
//        $having = ' 1=1 ';
//        $havingsql = '';
//        // 名单分配来源  校区/集团    0 为集团   1 为校区
//        if (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '0') {
//            $having .= " and track_type = '1' ";
//            $havingsql .= " (select t.track_type from crm_client_track as t where t.track_followmode = '-1' and t.track_state = '-1' and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type, ";
//        } elseif (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '1') {
//            $having .= " and track_type = '0' ";
//            $havingsql .= " (select t.track_type from crm_client_track as t where t.track_followmode = '-1' and t.track_state = '-1' and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type, ";
//        }
//        //管辖的人
//        if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
//            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
//                            from gmc_staffer_postbe as p 
//                            where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");
//
//            if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
//                if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
//                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
//        WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";
//
//                    $orgdatawhere = " ( 
//                ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
//                or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
//                {$organizeWhere} 
//                ) ";
//
//                    $sql = "SELECT p.staffer_id,m.marketer_id 
//        FROM gmc_staffer_postbe AS p 
//        LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
//                    $markertList = $this->DataControl->selectClear($sql);
//                    $arr_marketer_id = array_column($markertList, "marketer_id");
//                    $str_marketer_id = implode(',', $arr_marketer_id);
//
//                    $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
//                } else {
//                    $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
//                    $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
//
//                }
//            }
//        } else {
//            $sql = "SELECT p.staffer_id,m.marketer_id 
//        FROM gmc_staffer_postbe AS p 
//        LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
//            $markertList = $this->DataControl->selectClear($sql);
//            $arr_marketer_id = array_column($markertList, "marketer_id");
//            $str_marketer_id = implode(',', $arr_marketer_id);
//
//            $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
//        }
//
//        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
//            $page = $paramArray['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
//            $num = $paramArray['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;
//
//        $sql = "SELECT c.channel_id,c.client_source,c.client_gmcmarket,c.client_id,c.client_tracestatus,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_age,c.client_birthday,c.client_sex,c.client_mobile,c.client_address,c.client_img,c.outthree_userid,c.province_id,c.city_id,c.area_id,c.client_remark,
//        c.client_intention_level,from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,from_unixtime(c.client_updatetime, '%Y-%m-%d') AS client_updatetime,l.channel_maxday,l.channel_minday,l.channel_medianame,l.channel_name,c.client_tag,c.client_frompage,sc.school_shortname,
//        {$havingsql}
//        (select ch.channellog_id  from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1)  AS channellog_id,
//        (select p.parenter_cnname FROM crm_client_family as f  LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
//        (select s.staffer_cnname from crm_client_track as t left join crm_marketer as m ON m.marketer_id = t.marketer_id left join smc_staffer as s on s.staffer_id = m.staffer_id where t.client_id = c.client_id and t.track_followmode = '-2'  order by t.track_id desc limit 0,1 ) as examine_cnname ,
//        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.track_followmode = '-2'  order by t.track_id desc limit 0,1 ) as examine_createtime , 
//        (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) as activity_name,
//        (select t.track_note from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttrack_note,
//        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttime,
//        (select r.region_name from smc_code_region as r where c.province_id = r.region_id) AS province_name,
//        (select r.region_name from smc_code_region as r where c.city_id = r.region_id)     AS city_name,
//        (select r.region_name from smc_code_region as r where c.area_id = r.region_id)     AS area_name,
//        (select m.marketer_name from crm_client_principal as p left join crm_marketer as m ON p.marketer_id = m.marketer_id where p.client_id = c.client_id and p.school_id = '0' and p.school_id = '0' and p.principal_leave = '0' order by p.principal_id desc limit 0,1) as principal_name,
//        (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname 
//        FROM crm_client AS c 
//        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
//        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1')   
//        left join smc_school as sc on sc.school_id = e.school_id
//        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
//        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
//        WHERE {$datawhere} 
//        group by c.client_id 
//        HAVING {$having} 
//        ORDER BY c.client_updatetime DESC 
//        LIMIT {$pagestart},{$num} ";
//
//        $clientList = $this->DataControl->selectClear($sql);
//        if ($clientList) {
//            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
//            foreach ($clientList as $key => &$value) {
//                $clientList[$key]['gmcschoolenter'] = $value['track_type'] == '1' ? "集团" : "校区";
//                $clientList[$key]['school_shortname'] = $value['school_shortname'] ? $value['school_shortname'] : "--";
//
//                $clientList[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
//                $clientList[$key]['client_tracestatusname'] = $clientTracestatus[$value['client_tracestatus']];
//                $clientList[$key]['track_lasttrack_note'] = $value['track_lasttrack_note'] ? ($value['track_lasttrack_note'] . "（" . date('Y-m-d H:i:s', $value['track_lasttime']) . "）") : '--';
//                if ($value['examine_createtime']) {
//                    $value['examine_createtime'] = date("Y-m-d H:i:s", $value['examine_createtime']);
//                }
//                if ($value['client_tag']) {
//                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
//                } else {
//                    $clientList[$key]['client_tag'] = array();
//                }
//                if (!is_null($value['course_cnname'])) {
//                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
//                } else {
//                    $clientList[$key]['course_cnname'] = array();
//                }
//                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
//                if ($stafferOne['account_class'] == '1') {
//                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
//                } else {
//                    $clientList[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
//                }
//                $time = date('Y-m-d', time());
//                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
//                if ($value['channel_maxday'] == 0 || $time <= $max) {
//                    $value['status'] = 1;
//                } else {
//                    $value['status'] = 0;
//                }
//                if ($value['channellog_id'] == null) {
//                    $value['apply'] = '1';
//                } else {
//                    $value['apply'] = '0';
//                }
//            }
//        }
//
//        $all_num = $this->DataControl->selectClear("SELECT {$havingsql}c.client_id 
//        FROM crm_client AS c 
//        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
//        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1') 
//        left join smc_school as sc on sc.school_id = e.school_id
//        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
//        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
//        WHERE {$datawhere} group by c.client_id  HAVING {$having}  ORDER BY c.client_id DESC ");
//        $allnums = is_array($all_num) ? count($all_num) : 0;
//
//        $result = array();
//        $result['fieldcustom'] = 0;
//        $result['all_num'] = $allnums;
//        $result['list'] = $clientList;
//
//        $this->error = 0;
//        if ($clientList) {
//            $this->errortip = "数据获取成功";
//        } else {
//            $this->errortip = "暂时没有数据";
//        }
//        return $result;
//    }
    /**
     * 集团名单 获取无意向名单
     * @param $paramArray
     * @return array
     */
    public function getLossclientList($paramArray)
    {
        if($paramArray['school_id'] == '' && $paramArray['client_ischaserlapsed'] != '0'){
            $this->error = 1;
            $this->errortip = "请先选择学校！";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
//        $datawhere = "c.company_id = '{$paramArray['company_id']}' and p.principal_leave = 0 and c.client_gmcdistributionstatus = '1' ";
//        $datawhere = "( (c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-1' AND e.schoolenter_id IS NULL AND e.client_id IS NULL and c.client_gmcdistributionstatus = '0') or
//        (c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-1' AND e.schoolenter_id IS NOT NULL and e.is_gmctocrmschool = '1' ) )";

        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-1'  ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            if ($paramArray['keyword'] == '###') {
                $datawhere .= " and c.outthree_userid = ''";
            } else {
                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%'";
//                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            }
        }

        $having = ' 1=1 ';
//        // 名单分配来源  校区/集团    0 为集团   1 为校区
//        if (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '0') {
//            $having .= " and track_type = '1' ";
//        } elseif (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '1') {
//            $having .= " and track_type = '0' ";
//        }
        //意向星级
        if (isset($paramArray['client_intention_level'])) {
            $datawhere .= " and c.client_intention_level ='{$paramArray['client_intention_level']}' ";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere = " e.school_id = '{$paramArray['school_id']}' ";
//            $datawhere .= " and c.client_id in (SELECT et.client_id FROM crm_client_schoolenter as et WHERE et.school_id = '{$paramArray['school_id']}') ";
//            $having .= " and school_id='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['client_ischaserlapsed']) && $paramArray['client_ischaserlapsed'] != "") {
            $datawhere .= " and c.client_ischaserlapsed ='{$paramArray['client_ischaserlapsed']}'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] == "0") {
            $datawhere .= " and c.activity_id ='0'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] > "0") {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}' ";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $t_stime = strtotime($paramArray['start_time']);
            $datawhere .= " and c.client_createtime >= '{$t_stime}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $t_etime = strtotime($paramArray['end_time']);
            $datawhere .= " and c.client_createtime <= '{$t_etime}'";
        }
        //请勿改，改之前问
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
//        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
//            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
//        }
//        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
//            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
//        }
        if ($paramArray['isupdomain'] == '1') {//上级功能模块
//            if($paramArray['dataequity'] != 1 && $paramArray['re_postbe_id'] > 0){
//                $postroleOne = $this->DataControl->selectOne("SELECT r.postrole_dataequity,b.organize_id FROM gmc_company_postrole AS r,gmc_staffer_postbe AS b
//    WHERE r.postrole_id = b.postrole_id AND b.postbe_id = '{$paramArray['re_postbe_id']}' limit 0,1");
//                //判定非所有权益，仅组织权益
//                if($postroleOne['postrole_dataequity'] !=='1'){
//                    $datawhere .= " and l.channel_id in (SELECT o.channel_id FROM crm_channel_organize AS o WHERE o.organize_id = '{$postroleOne['organize_id']}') ";
//                }
//            }
        } else {
            //管辖的人
            if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
                $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

                if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                    if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                        $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                        $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                        $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                        $markertList = $this->DataControl->selectClear($sql);
                        $arr_marketer_id = array_column($markertList, "marketer_id");
                        $str_marketer_id = implode(',', $arr_marketer_id);

                        $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                    } else {
                        $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                        $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";

                    }
                }
            } else {
                $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
                $markertList = $this->DataControl->selectClear($sql);
                $arr_marketer_id = array_column($markertList, "marketer_id");
                $str_marketer_id = implode(',', $arr_marketer_id);

                $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
            }
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.track_followmode = '-2'  order by t.track_id desc limit 0,1 ) as examine_createtime ,
//        (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) as activity_name,
//        (select t.track_note from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttrack_note,
//        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttime,
//        (select r.region_name from smc_code_region as r where c.province_id = r.region_id) AS province_name,
//        (select r.region_name from smc_code_region as r where c.city_id = r.region_id)     AS city_name,
//        (select r.region_name from smc_code_region as r where c.area_id = r.region_id)     AS area_name,
//        (select m.marketer_name from crm_client_principal as p left join crm_marketer as m ON p.marketer_id = m.marketer_id where p.client_id = c.client_id and p.school_id = '0' and p.school_id = '0' and p.principal_leave = '0' order by p.principal_id desc limit 0,1) as principal_name,
//        (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
//        (select p.parenter_cnname FROM crm_client_family as f  LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
//        (select s.staffer_cnname from crm_client_track as t left join crm_marketer as m ON m.marketer_id = t.marketer_id left join smc_staffer as s on s.staffer_id = m.staffer_id where t.client_id = c.client_id and t.track_followmode = '-2'  order by t.track_id desc limit 0,1 ) as examine_cnname ,
//        , (select t.track_type from crm_client_track as t where t.track_followmode = '-1' and t.track_state = '-1' and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type

        $sql = "SELECT c.channel_id,c.client_source,c.client_gmcmarket,c.client_id,c.client_tracestatus,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_age,c.client_birthday,c.client_sex,c.client_mobile,c.client_address,c.client_img,c.outthree_userid,c.province_id,c.city_id,c.area_id,c.client_remark,
        c.client_intention_level,from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,from_unixtime(c.client_updatetime, '%Y-%m-%d') AS client_updatetime,l.channel_maxday,l.channel_minday,l.channel_medianame,l.channel_name,c.client_tag,c.client_frompage,sc.school_shortname,
        (select ch.channellog_id  from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1)  AS channellog_id
        FROM crm_client AS c 
        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1')   
        left join smc_school as sc on sc.school_id = e.school_id
        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
        WHERE {$datawhere} 
        group by c.client_id 
        HAVING {$having} 
        ORDER BY c.client_updatetime DESC 
        LIMIT {$pagestart},{$num} ";

        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => &$value) {
//                $clientList[$key]['gmcschoolenter'] = $value['track_type'] == '1' ? "集团" : "校区";
                $clientList[$key]['school_shortname'] = $value['school_shortname'] ? $value['school_shortname'] : "--";

                $clientList[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                $clientList[$key]['client_tracestatusname'] = $clientTracestatus[$value['client_tracestatus']];
                $clientList[$key]['track_lasttrack_note'] = $value['track_lasttrack_note'] ? ($value['track_lasttrack_note'] . "（" . date('Y-m-d H:i:s', $value['track_lasttime']) . "）") : '--';
                if ($value['examine_createtime']) {
                    $value['examine_createtime'] = date("Y-m-d H:i:s", $value['examine_createtime']);
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
//                if (!is_null($value['course_cnname'])) {
//                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
//                } else {
//                    $clientList[$key]['course_cnname'] = array();
//                }
//                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                if ($value['channel_maxday'] == 0 || $time <= $max) {
                    $value['status'] = 1;
                } else {
                    $value['status'] = 0;
                }
                if ($value['channellog_id'] == null) {
                    $value['apply'] = '1';
                } else {
                    $value['apply'] = '0';
                }
            }
        }

//        (select t.track_type from crm_client_track as t where t.track_followmode = '-1' and t.track_state = '-1'  and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type
        $all_num = $this->DataControl->selectClear("SELECT c.client_id
        FROM crm_client AS c 
        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1') 
        left join smc_school as sc on sc.school_id = e.school_id
        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
        WHERE {$datawhere} group by c.client_id  HAVING {$having}  ORDER BY c.client_id DESC ");
        $allnums = is_array($all_num) ? count($all_num) : 0;

//        $fieldstring = array('client_cnname', 'client_sex','client_birthday', 'client_patriarchname', 'client_mobile','client_tracestatus', 'client_remark','client_frompage', 'channel_medianame', 'channel_name', 'shengshiqu','client_address', 'client_gmcmarket', 'outthree_userid', 'client_createtime', 'client_updatetime');
//        $fieldname = $this->LgArraySwitch(array('姓名', '性别','出生日期', '主要联系人', '主要联系人手机', '客户状态','最后跟踪内容', '来源活动', '渠道类型', '渠道明细','省市区', '联系地址','集团负责人', 'UserThreeId', '创建时间', '更新时间'));
//        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
//        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1","1",  "1", "0", "1", "1");
//
//        $field = array();
//        for ($i = 0; $i < count($fieldstring); $i++) {
//            $field[$i]["fieldname"] = trim($fieldstring[$i]);
//            $field[$i]["fieldstring"] = trim($fieldname[$i]);
//            $field[$i]["custom"] = trim($fieldcustom[$i]);
//            $field[$i]["show"] = trim($fieldshow[$i]);
//        }
//
//        $result = array();
//        $result['fieldcustom'] = 0;
//        $result['field'] = $field;
//        $result['all_num'] = $allnums;
//
//        if ($clientList) {
//            $result['list'] = $clientList;
//            $res = array('error' => '0', 'errortip' => "获取成功!", 'result' => $result);
//        } else {
//            $result['list'] = array();
//            $res = array('error' => '1', 'errortip' => "暂无待分配名单", 'result' => $result);
//        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;
    }

    //无效
    public function getInvalidLossclientList($paramArray)
    {
        if($paramArray['school_id'] == '' && $paramArray['client_ischaserlapsed'] != '0'){
            $this->error = 1;
            $this->errortip = "请先选择学校！";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
//        $datawhere = "c.company_id = '{$paramArray['company_id']}' and p.principal_leave = 0 and c.client_gmcdistributionstatus = '1' ";
//        $datawhere = "( (c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-1' AND e.schoolenter_id IS NULL AND e.client_id IS NULL and c.client_gmcdistributionstatus = '0') or
//        (c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-1' AND e.schoolenter_id IS NOT NULL and e.is_gmctocrmschool = '1' ) )";

        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-2'  ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            if ($paramArray['keyword'] == '###') {
                $datawhere .= " and c.outthree_userid = ''";
            } else {
                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            }
        }

        $having = ' 1=1 ';
        // 名单分配来源  校区/集团    0 为集团   1 为校区
        if (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '0') {
            $having .= " and track_type = '1' ";
        } elseif (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '1') {
            $having .= " and track_type = '0' ";
        }
        //意向星级
        if (isset($paramArray['client_intention_level'])) {
            $datawhere .= " and c.client_intention_level ='{$paramArray['client_intention_level']}' ";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere = " e.school_id = '{$paramArray['school_id']}' ";
//            $datawhere .= " and c.client_id in (SELECT et.client_id FROM crm_client_schoolenter as et WHERE et.school_id = '{$paramArray['school_id']}') ";
//            $having .= " and school_id='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['client_isinvalidreview']) && $paramArray['client_isinvalidreview'] != "") {
            $datawhere .= " and c.client_isinvalidreview ='{$paramArray['client_isinvalidreview']}'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] == "0") {
            $datawhere .= " and c.activity_id ='0'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] > "0") {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}' ";
        }
        //请勿改，改之前问
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $datawhere .= " and from_unixtime(c.client_createtime,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }
        if ($paramArray['isupdomain'] == '1') {//上级功能模块
//            if($paramArray['dataequity'] != 1 && $paramArray['re_postbe_id'] > 0){
//                $postroleOne = $this->DataControl->selectOne("SELECT r.postrole_dataequity,b.organize_id FROM gmc_company_postrole AS r,gmc_staffer_postbe AS b
//    WHERE r.postrole_id = b.postrole_id AND b.postbe_id = '{$paramArray['re_postbe_id']}' limit 0,1");
//                //判定非所有权益，仅组织权益
//                if($postroleOne['postrole_dataequity'] !=='1'){
//                    $datawhere .= " and l.channel_id in (SELECT o.channel_id FROM crm_channel_organize AS o WHERE o.organize_id = '{$postroleOne['organize_id']}') ";
//                }
//            }
        } else {
            //管辖的人
            if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
                $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

                if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                    if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                        $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                        $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                        $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                        $markertList = $this->DataControl->selectClear($sql);
                        $arr_marketer_id = array_column($markertList, "marketer_id");
                        $str_marketer_id = implode(',', $arr_marketer_id);

                        $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                    } else {
                        $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                        $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";

                    }
                }
            } else {
                $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
                $markertList = $this->DataControl->selectClear($sql);
                $arr_marketer_id = array_column($markertList, "marketer_id");
                $str_marketer_id = implode(',', $arr_marketer_id);

                $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
            }
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.channel_id,c.client_source,c.client_gmcmarket,c.client_id,c.client_tracestatus,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_age,c.client_birthday,c.client_sex,c.client_mobile,c.client_address,c.client_img,c.outthree_userid,c.province_id,c.city_id,c.area_id,c.client_remark,
        c.client_intention_level,from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,from_unixtime(c.client_updatetime, '%Y-%m-%d') AS client_updatetime,l.channel_maxday,l.channel_minday,l.channel_medianame,l.channel_name,c.client_tag,c.client_frompage,sc.school_shortname,
        (select ch.channellog_id  from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1)  AS channellog_id,
        (select p.parenter_cnname FROM crm_client_family as f  LEFT JOIN smc_parenter as p ON p.parenter_id = f.parenter_id WHERE f.client_id=c.client_id order by f.family_isdefault  DESC limit 0,1 ) as family_cnname,
        (select s.staffer_cnname from crm_client_track as t left join crm_marketer as m ON m.marketer_id = t.marketer_id left join smc_staffer as s on s.staffer_id = m.staffer_id where t.client_id = c.client_id and t.track_followmode = '-2'  order by t.track_id desc limit 0,1 ) as examine_cnname ,
        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id and t.track_followmode = '-2'  order by t.track_id desc limit 0,1 ) as examine_createtime , 
        (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) as activity_name,
        (select t.track_note from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttrack_note,
        (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttime,
        (select r.region_name from smc_code_region as r where c.province_id = r.region_id) AS province_name,
        (select r.region_name from smc_code_region as r where c.city_id = r.region_id)     AS city_name,
        (select r.region_name from smc_code_region as r where c.area_id = r.region_id)     AS area_name,
        (select r.invalidnote_reason from crm_code_invalidnote as r where c.invalidnote_code = r.invalidnote_code and c.company_id = r.company_id) AS invalidnote_reason,
        (select m.marketer_name from crm_client_principal as p left join crm_marketer as m ON p.marketer_id = m.marketer_id where p.client_id = c.client_id and p.school_id = '0' and p.school_id = '0' and p.principal_leave = '0' order by p.principal_id desc limit 0,1) as principal_name,
        (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
        (select t.track_type from crm_client_track as t where t.track_followmode = '-1' and t.track_state = '-1' and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type 
        FROM crm_client AS c 
        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1')   
        left join smc_school as sc on sc.school_id = e.school_id
        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
        WHERE {$datawhere} 
        group by c.client_id 
        HAVING {$having} 
        ORDER BY c.client_updatetime DESC 
        LIMIT {$pagestart},{$num} ";

        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => &$value) {
                $clientList[$key]['client_allname'] = $value['client_enname']?$value['client_cnname']."-".$value['client_enname']:$value['client_cnname'];
                $clientList[$key]['gmcschoolenter'] = $value['track_type'] == '1' ? "集团" : "校区";
                $clientList[$key]['school_shortname'] = $value['school_shortname'] ? $value['school_shortname'] : "--";

                $clientList[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                $clientList[$key]['client_tracestatusname'] = $clientTracestatus[$value['client_tracestatus']];
                $clientList[$key]['track_lasttrack_note'] = $value['track_lasttrack_note'] ? ($value['track_lasttrack_note'] . "（" . date('Y-m-d H:i:s', $value['track_lasttime']) . "）") : '--';
                if ($value['examine_createtime']) {
                    $value['examine_createtime'] = date("Y-m-d H:i:s", $value['examine_createtime']);
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $clientList[$key]['course_cnname'] = array();
                }
                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                if ($value['channel_maxday'] == 0 || $time <= $max) {
                    $value['status'] = 1;
                } else {
                    $value['status'] = 0;
                }
                if ($value['channellog_id'] == null) {
                    $value['apply'] = '1';
                } else {
                    $value['apply'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->selectClear("SELECT c.client_id,
       (select t.track_type from crm_client_track as t where t.track_followmode = '-1' and t.track_state = '-1'  and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type 
        FROM crm_client AS c 
        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1') 
        left join smc_school as sc on sc.school_id = e.school_id
        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
        WHERE {$datawhere} group by c.client_id  HAVING {$having}  ORDER BY c.client_id DESC ");
        $allnums = is_array($all_num) ? count($all_num) : 0;

//        $fieldstring = array('client_cnname', 'client_sex','client_birthday', 'client_patriarchname', 'client_mobile','client_tracestatus', 'client_remark','client_frompage', 'channel_medianame', 'channel_name', 'shengshiqu','client_address', 'client_gmcmarket', 'outthree_userid', 'client_createtime', 'client_updatetime');
//        $fieldname = $this->LgArraySwitch(array('姓名', '性别','出生日期', '主要联系人', '主要联系人手机', '客户状态','最后跟踪内容', '来源活动', '渠道类型', '渠道明细','省市区', '联系地址','集团负责人', 'UserThreeId', '创建时间', '更新时间'));
//        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
//        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1","1",  "1", "0", "1", "1");
//
//        $field = array();
//        for ($i = 0; $i < count($fieldstring); $i++) {
//            $field[$i]["fieldname"] = trim($fieldstring[$i]);
//            $field[$i]["fieldstring"] = trim($fieldname[$i]);
//            $field[$i]["custom"] = trim($fieldcustom[$i]);
//            $field[$i]["show"] = trim($fieldshow[$i]);
//        }
//
//        $result = array();
//        $result['fieldcustom'] = 0;
//        $result['field'] = $field;
//        $result['all_num'] = $allnums;
//
//        if ($clientList) {
//            $result['list'] = $clientList;
//            $res = array('error' => '0', 'errortip' => "获取成功!", 'result' => $result);
//        } else {
//            $result['list'] = array();
//            $res = array('error' => '1', 'errortip' => "暂无待分配名单", 'result' => $result);
//        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;
    }

    /**
     * 集团名单 获取无效名单
     * @param $paramArray
     * @return array
     */
    public function getInvalidClientList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
//        $datawhere = "c.company_id = '{$paramArray['company_id']}' and p.principal_leave = 0 and c.client_gmcdistributionstatus = '1' ";
//        $datawhere = "( (c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-2' AND e.schoolenter_id IS NULL AND e.client_id IS NULL and c.client_gmcdistributionstatus = '0') or
//        (c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-2' AND e.schoolenter_id IS NOT NULL and e.is_gmctocrmschool = '1' ) )";

        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus = '-2'  ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            if ($paramArray['keyword'] == '###') {
                $datawhere .= " and c.outthree_userid = ''";
            } else {
                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%')";
//                $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_address like '%{$paramArray['keyword']}%' or c.client_remark like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
            }
        }
        $having = ' 1=1 ';
//        // 名单分配来源  校区/集团    0 为集团   1 为校区
//        if (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '0') {
//            $having .= " and track_type = '1' ";
//        } elseif (isset($paramArray['isfromschool']) && $paramArray['isfromschool'] == '1') {
//            $having .= " and track_type = '0' ";
//        }
//        // 是否集团参与招生      0 集团未参与   1 集团参与
//        if (isset($paramArray['isgmcjoin']) && $paramArray['isgmcjoin'] == '0') {
//            $having .= " and track_idone is null ";
//        } elseif (isset($paramArray['isgmcjoin']) && $paramArray['isgmcjoin'] == '1') {
//            $having .= " and track_idone > '0' ";
//        }
        //意向星级
        if (isset($paramArray['client_intention_level'])) {
            $datawhere .= " and c.client_intention_level ='{$paramArray['client_intention_level']}' ";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " and e.school_id = '{$paramArray['school_id']}' ";
        }
        if (isset($paramArray['client_ischaserlapsed']) && $paramArray['client_ischaserlapsed'] != "") {
            $datawhere .= " and c.client_ischaserlapsed ='{$paramArray['client_ischaserlapsed']}'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] == "0") {
            $datawhere .= " and c.activity_id ='0'";
        }
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] > "0") {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}' ";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $t_stime = strtotime($paramArray['start_time']);
            $datawhere .= " and c.client_createtime >= '{$t_stime}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $t_etime = strtotime($paramArray['end_time']);
            $datawhere .= " and c.client_createtime <= '{$t_etime}'";
        }
        //请勿改，改之前问
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
//        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
//            $t_stime = strtotime($paramArray['start_time']);
//            $having .= " and track_lasttime >= '{$t_stime}'";
//        }
//        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
//            $t_etime = strtotime($paramArray['end_time']);
//            $having .= " and track_lasttime <= '{$t_etime}'";
//        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


//        (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id) as activity_name,
//        (select t.track_note from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttrack_note,
//        (select r.region_name from smc_code_region as r where c.province_id = r.region_id) AS province_name,
//        (select r.region_name from smc_code_region as r where c.city_id = r.region_id)     AS city_name,
//        (select r.region_name from smc_code_region as r where c.area_id = r.region_id)     AS area_name,
//        (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
//        , (select t.track_id from crm_client_track as t where t.track_type = '1' order by t.track_id desc limit 0,1) as track_idone
//        ,(select t.track_createtime from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttime
//        , (select t.track_type from crm_client_track as t where t.track_followmode = '-3' and t.track_state = '-2'  and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type

//        Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
//        Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id

        $sql = "SELECT c.channel_id,c.client_source,c.client_gmcmarket,c.client_id,c.client_tracestatus,c.client_patriarchname,c.client_cnname,c.client_enname,c.client_age,c.client_birthday,c.client_sex,c.client_mobile,c.client_address,c.client_img,c.outthree_userid,c.province_id,c.city_id,c.area_id,
        c.client_remark,c.client_intention_level,from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,from_unixtime(c.client_updatetime, '%Y-%m-%d') AS client_updatetime,l.channel_maxday,l.channel_minday,l.channel_medianame,l.channel_name,c.client_tag,c.client_frompage,sc.school_shortname,
        (select ch.channellog_id from crm_client_channellog as ch where ch.client_id = c.client_id and ch.channellog_status = '0' limit 0,1) AS channellog_id
        FROM crm_client AS c 
        LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}' 
        LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1' )
        left join smc_school as sc on sc.school_id = e.school_id
        WHERE {$datawhere} group by c.client_id  HAVING {$having} 
        ORDER BY c.client_updatetime DESC 
        LIMIT {$pagestart},{$num} ";

        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($clientList as $key => &$value) {
//                $clientList[$key]['gmcschoolenter'] = $value['track_type'] == '1' ? "集团" : "校区";
                $clientList[$key]['school_shortname'] = $value['school_shortname'] ? $value['school_shortname'] : "--";

                $clientList[$key]['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                $clientList[$key]['client_tracestatusname'] = $clientTracestatus[$value['client_tracestatus']];
//                $clientList[$key]['track_lasttrack_note'] = $value['track_lasttrack_note'] ? ($value['track_lasttrack_note'] . "（" . date('Y-m-d H:i:s', $value['track_lasttime']) . "）") : '--';
//                $clientList[$key]['shengshiqu'] = ($value['area_id'] > 0) ? $value['province_name'] . '-' . $value['city_name'] . '-' . $value['area_name'] : '--';
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = hideNumberString($value['client_mobile']);
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
//                if (!is_null($value['course_cnname'])) {
//                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
//                } else {
//                    $clientList[$key]['course_cnname'] = array();
//                }
                $time = date('Y-m-d', time());
//                $min = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_minday']} day"));
                $max = date("Y-m-d", strtotime($value['client_createtime'] . "+{$value['channel_maxday']} day"));
                if ($value['channel_maxday'] == 0 || $time <= $max) {
                    $value['status'] = 1;
                } else {
                    $value['status'] = 0;
                }
                if ($value['channellog_id'] == null) {
                    $value['apply'] = '1';
                } else {
                    $value['apply'] = '0';
                }
            }
        }
//        ,
//        (select t.track_type from crm_client_track as t where t.track_followmode = '-3' and t.track_state = '-2'  and t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_type,
//           (select t.track_createtime from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1) as track_lasttime,
//           (select t.track_id from crm_client_track as t where t.track_type = '1' order by t.track_id desc limit 0,1) as track_idone
        $all_num = $this->DataControl->selectOne("SELECT count( DISTINCT c.client_id) as allnum
            FROM crm_client AS c
            LEFT JOIN crm_code_channel AS l ON c.channel_id = l.channel_id AND l.company_id = '{$paramArray['company_id']}'
            LEFT JOIN crm_client_schoolenter AS e ON (c.client_id = e.client_id AND e.company_id = '{$paramArray['company_id']}' and e.is_enterstatus = '1' )
            left join smc_school as sc on sc.school_id = e.school_id
            Left JOIN crm_client_principal as  p ON p.client_id = c.client_id
            Left JOIN crm_marketer as mk ON mk.marketer_id = p.marketer_id
            WHERE {$datawhere}  HAVING {$having}  ORDER BY c.client_id DESC ");
        $allnums = $all_num['allnum'];

        $fieldstring = array('client_cnname', 'client_tag',  'client_sex', 'client_age', 'gmcschoolenter', 'school_shortname',
            'client_birthday', 'client_patriarchname', 'client_mobile',  'channel_medianame',
            'channel_name', 'client_createtime', 'client_updatetime');
        $fieldname = $this->LgArraySwitch(array('姓名', '标签', '性别', '年龄', '名单来源', '所属学校',
            '出生日期', '主要联系人', '主要联系人手机',  '渠道类型',
            '渠道明细',  '创建时间', '更新时间'));
        $fieldcustom = array("1",  "1", "1", "1", "1", "1",
            "1", "1", "1", "1",
            "1",  "1", "1");
        $fieldshow = array("1",  "1", "1", "1", "1", "1",
            "0", "1", "1",  "1",
            "1",  "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == "client_cnname") {
                $field[$i]["ismethod"] = 1;
            }
            if ($field[$i]["fieldname"] == "client_tag" || $field[$i]["fieldname"] == "course_cnname") {
                $field[$i]["istag"] = 1;
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($clientList) {
            $result['list'] = $clientList;
            $res = array('error' => '0', 'errortip' => "获取成功!", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无待分配名单", 'result' => $result);
        }
        return $res;
    }

    /**
     * @param $paramArray
     * @return array|bool
     * 编辑客户线索
     */
    function editClientOne($paramArray)
    {
        if (isset($paramArray['client_icard']) && $paramArray['client_icard'] !== "") {
            if (!$this->checkClientIcard($paramArray['client_icard'], $paramArray['client_id'])) {
                $this->error = 1;
                $this->errortip = "身份证号重复,无法添加";
                return false;
            }
        }

        $checkData = array();
        $checkData['client_cnname'] = trim($paramArray['client_cnname']);
        $checkData['client_mobile'] = trim($paramArray['client_mobile']);
        $checkData['company_id'] = $paramArray['company_id'];
        $checkData['school_id'] = $paramArray['school_id'];
        $from = "edit";
        if ($this->checkIntentionClient($checkData, $from, $paramArray['client_id'])) {
            $this->error = 1;
            $this->errortip = "中文名与手机号重复,无法添加";
            return false;
        }

        $clientOne = $this->DataControl->getOne("crm_client", "client_id='{$paramArray['client_id']}'");
        $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
        if (!$markOne) {
            $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $markOne['marketer_id'] = $getaddmarkertOne['marketer_id'];
            $markOne['marketer_name'] = $getaddmarkertOne['marketer_name'];
        }

        if ($paramArray['client_source'] == '') {
            $this->error = 1;
            $this->errortip = "为了招生效果统计，必须选择渠道类型！";
            return false;
        }
        if ($paramArray['channel_id'] == '' || $paramArray['channel_id'] == '0') {
            $this->error = 1;
            $this->errortip = "为了招生效果统计，必须选择渠道明细！";
            return false;
        }
        if ($clientOne['client_isfromgmc'] == '1' && $clientOne['client_source'] !== $paramArray['client_source']) {
            $this->error = 1;
            $this->errortip = "本名单通过集团招生管理中心导入，禁止修改招生渠道类型！";
            return false;
        }
        if ($clientOne['client_isfromgmc'] == '1' && $clientOne['channel_id'] !== $paramArray['channel_id']) {
            $this->error = 1;
            $this->errortip = "本名单通过集团招生管理中心导入，禁止修改招生渠道明细！";
            return false;
        }

        $data = array();
        if (isset($paramArray['province_id']) && $paramArray['province_id'] != '') {
            $data['province_id'] = $paramArray['province_id'];
        }
        if (isset($paramArray['city_id']) && $paramArray['city_id'] != '') {
            $data['city_id'] = $paramArray['city_id'];
        }
        if (isset($paramArray['area_id']) && $paramArray['area_id'] != '') {
            $data['area_id'] = $paramArray['area_id'];
        }
        if (isset($paramArray['client_cnname']) && $paramArray['client_cnname'] != '') {
            $data['client_cnname'] = trim($paramArray['client_cnname']);
        }
        if (isset($paramArray['client_enname'])) {
            $data['client_enname'] = trim($paramArray['client_enname']);
        }
        if (isset($paramArray['client_img']) && $paramArray['client_img'] != '') {
            $data['client_img'] = $paramArray['client_img'];
        }
        if (isset($paramArray['client_sex']) && $paramArray['client_sex'] != '') {
            $data['client_sex'] = $paramArray['client_sex'];
        }
        if (isset($paramArray['client_mobile']) && $paramArray['client_mobile'] != '') {
            $data['client_mobile'] = trim($paramArray['client_mobile']);
        }
        if (isset($paramArray['client_icard'])) {
            $data['client_icard'] = trim($paramArray['client_icard']);
        }
        if (isset($paramArray['client_remark'])) {
            $data['client_remark'] = trim($paramArray['client_remark']);
        }

        if ($clientOne['client_mobile'] != $data['client_mobile']) {
            $trackData = array();
            $trackData['client_id'] = $paramArray['client_id'];
            $trackData['school_id'] = '';
            $trackData['marketer_id'] = $markOne['marketer_id'];
            $trackData['marketer_name'] = $markOne['marketer_name'];
            $trackData['track_createtime'] = time();
            $trackData['track_note'] = "集团操作：本次修改手机号码{$clientOne['client_mobile']}至{$data['client_mobile']}";
            $this->DataControl->insertData("crm_client_track", $trackData);
        }

//        $data['client_source'] = $paramArray['client_source'];
//        $data['channel_id'] = $paramArray['channel_id'];

        if (isset($paramArray['client_birthday']) && $paramArray['client_birthday'] != '') {
            $data['client_birthday'] = $paramArray['client_birthday'];
        }
        if (isset($paramArray['client_oh_month']) && $paramArray['client_oh_month'] != '') {
            $data['client_oh_month'] = $paramArray['client_oh_month'];
        }
        if (isset($paramArray['client_push_month']) && $paramArray['client_push_month'] != '') {
            $data['client_push_month'] = $paramArray['client_push_month'];
        }
        if (isset($paramArray['nearschool_id']) && $paramArray['nearschool_id'] != '') {
            $data['nearschool_id'] = $paramArray['nearschool_id'];
        }
        if (isset($paramArray['client_intention_level']) && $paramArray['client_intention_level'] != '') {
            $data['client_intention_level'] = $paramArray['client_intention_level'];
            if ($paramArray['client_intention_level'] > $clientOne['client_intention_maxlevel']) {
                $data['client_intention_maxlevel'] = $paramArray['client_intention_level'];
            }
        }
        if (isset($paramArray['client_fromtype']) && $paramArray['client_fromtype'] != '') {
            $data['client_fromtype'] = $paramArray['client_fromtype'];
        }
        if (isset($paramArray['client_birthday']) && $paramArray['client_birthday'] != '') {
            $data['client_age'] = birthdaytoage($data['client_birthday']);
        }
        $data['client_updatetime'] = time();
        if (isset($paramArray['client_sponsor']) && $paramArray['client_sponsor'] != '') {
            $data['client_sponsor'] = $paramArray['client_sponsor'];
        }
        $data['outthree_apiid'] = '1';

        if (isset($paramArray['is_recommend']) && $paramArray['is_recommend'] == 1) {
            if (isset($paramArray['client_sponsor']) && $paramArray['client_sponsor'] != '') {
                $data['client_sponsor'] = $paramArray['client_sponsor'];
            }
            if (isset($paramArray['client_stubranch']) && $paramArray['client_stubranch'] != '') {
                $data['client_stubranch'] = $paramArray['client_stubranch'];
            }
        }

        //客户标签

        if ($paramArray['label_list']) {
            $tagarray = json_decode(stripslashes($paramArray['label_list']), true);
            if (is_array($tagarray)) {
                $tagstr = implode(',', $tagarray);
            } else {
                $tagstr = $paramArray['label_list'];
            }
        }
        if ($tagstr) {
            $data['client_tag'] = $tagstr;
        }

//        [{"family_relation":"父亲","family_mobile":"1","family_cnname":"1","family_isdefault":"1"}]

        //家长联系人
        if ($paramArray['family']) {
            $family = json_decode(stripslashes($paramArray['family']), true);
            if (is_array($family)) {
                foreach ($family as $familyvar) {
                    if (($familyvar['family_mobile'] != "")) {
                        $familyvar['family_mobile'] = trim($familyvar['family_mobile']);
                        $family_mobile[] = $familyvar['family_mobile'];
                        //主要联系人的电话同步
                        if ($familyvar['family_isdefault'] == 1) {
                            $data['client_mobile'] = $familyvar['family_mobile'];
                            if ($familyvar['family_cnname']) {
                                $data['client_patriarchname'] = $familyvar['family_cnname'];
                            }
                        }
                    } else {
                        $family_mobile = array();
                    }
                }
                if (count($family_mobile) != count(array_unique($family_mobile))) {
                    $this->error = 1;
                    $this->errortip = "家长手机号重复";
                    return false;
                }
            }
        }
        $this->DataControl->begintransaction();
        if (!$this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $data)) {
            $this->DataControl->rollback();
            $this->error = 1;
            $this->errortip = "更新客户状态失败";
            return false;
        }
        //家长联系人
        if ($paramArray['family']) {
            $family = json_decode(stripslashes($paramArray['family']), true);
            if (is_array($family)) {
                $this->DataControl->delData('crm_client_family', " client_id = '{$paramArray['client_id']}' ");
                foreach ($family as $familyvar) {
                    if ($familyvar['family_mobile'] != "") {
                        $familyvar['family_cnname'] = trim($familyvar['family_cnname']);
                        $familyvar['family_relation'] = trim($familyvar['family_relation']);
                        $familyvar['family_mobile'] = trim($familyvar['family_mobile']);
                        $parenter_id = $this->DataControl->getFieldOne("smc_parenter", 'parenter_id', "parenter_mobile = '{$familyvar['family_mobile']}'");
                        if ($parenter_id) {
                            $this->DataControl->delData('crm_client_family', " parenter_id = '{$parenter_id['parenter_id']}' ");
                            $familydata = array();
                            $familydata['client_id'] = $paramArray['client_id'];
                            $familydata['company_id'] = $paramArray['company_id'];
                            $familydata['family_createtime'] = time();
                            $familydata['family_relation'] = $familyvar['family_relation'];
                            $familydata['parenter_id'] = $parenter_id['parenter_id'];
                            $familydata['family_isdefault'] = $familyvar['family_isdefault'];
                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = "新增家庭联系人失败";
                                return false;
                            }

                            $parenterData = array();
                            $parenterData['parenter_cnname'] = $familyvar['family_cnname'];
                            $parenterData['parenter_mobile'] = $familyvar['family_mobile'];
                            $this->DataControl->updateData("smc_parenter", "parenter_id='{$parenter_id['parenter_id']}'", $parenterData);
                        } else {
                            $parenterData = array();
                            $parenterData['parenter_cnname'] = $familyvar['family_cnname'];
                            $parenterData['parenter_mobile'] = $familyvar['family_mobile'];
                            $parenterData['parenter_pass'] = md5(substr($familyvar['family_mobile'], -6));
                            $parenterData['parenter_bakpass'] = substr($familyvar['family_mobile'], -6);
                            $parenterData['parenter_addtime'] = time();
                            if (!$insert_id = $this->DataControl->insertData("smc_parenter", $parenterData)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = "新增家长失败";
                                return false;
                            }

                            $this->DataControl->delData('crm_client_family', " parenter_id = '{$insert_id}' ");
                            $familydata = array();
                            $familydata['client_id'] = $paramArray['client_id'];
                            $familydata['company_id'] = $paramArray['company_id'];
                            $familydata['family_createtime'] = time();
                            $familydata['family_relation'] = $familyvar['family_relation'];
                            $familydata['parenter_id'] = $insert_id;
                            $familydata['family_isdefault'] = $familyvar['family_isdefault'];
                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
                                $this->DataControl->rollback();
                                $this->error = 1;
                                $this->errortip = "新增家庭联系人失败";
                                return false;
                            }
                        }
                    }
                }
            }
        }


//        //家长联系人 ==
//        $family = $paramArray['client_family_list'];
//        $familycount = count($family);
//        $family_mobile = array();
//        if ($familycount) {
//            for ($k = 0; $k < $familycount; $k++) {
//                if (($family[$k]['family_mobile'] != "")) {
//                    $family[$k]['family_mobile'] = trim($family[$k]['family_mobile']);
//                    $family_mobile[] = $family[$k]['family_mobile'];
//                    //主要联系人的电话同步
//                    if ($family[$k]['family_isdefault'] == 1) {
//                        $data['client_mobile'] = $family[$k]['family_mobile'];
//                    }
//                } else {
//                    $family_mobile = array();
//                }
//            }
//            if (count($family_mobile) != count(array_unique($family_mobile))) {
//                $this->error = 1;
//                $this->errortip = "家长手机号重复";
//                return false;
//            }
//        }
//        $this->DataControl->begintransaction();
//        if (!$this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $data)) {
//            $this->DataControl->rollback();
//            $this->error = 1;
//            $this->errortip = "更新客户状态失败";
//            return false;
//        }

//        //家长联系人
//        $family = $paramArray['client_family_list'];
//        $familycount = count($family);
//        if ($familycount) {
//            for ($key = 0; $key < $familycount; $key++) {
//                if ($family[$key]['family_mobile'] != "") {
//                    $family[$key]['family_cnname'] = trim($family[$key]['family_cnname']);
//                    $family[$key]['family_relation'] = trim($family[$key]['family_relation']);
//                    $family[$key]['family_mobile'] = trim($family[$key]['family_mobile']);
//
//                    $parenter_id = $this->DataControl->getFieldOne("smc_parenter", 'parenter_id', "parenter_mobile = '{$family[$key]['family_mobile']}'");
//                    if ($parenter_id) {
//                        if ($family[$key]['family_id'] == "") {
//                            $familydata = array();
//                            $familydata['client_id'] = $paramArray['client_id'];
//                            $familydata['family_relation'] = $family[$key]['family_relation'];
//                            $familydata['parenter_id'] = $parenter_id['parenter_id'];
//                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
//                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
//                                $this->DataControl->rollback();
//                                $this->error = 1;
//                                $this->errortip = "新增家庭联系人失败";
//                                return false;
//                            }
//                        } elseif ($family[$key]['family_id'] != "") {
//                            $familydata = array();
//                            $familydata['family_relation'] = $family[$key]['family_relation'];
//                            $familydata['parenter_id'] = $parenter_id['parenter_id'];
//                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
//
//                            if (!$this->DataControl->updateData('crm_client_family', "family_id={$family[$key]['family_id']}", $familydata)) {
//                                $this->DataControl->rollback();
//                                $this->error = 1;
//                                $this->errortip = "更新家庭联系人失败";
//                                return false;
//                            }
//                            $parenterData = array();
//                            $parenterData['parenter_cnname'] = $family[$key]['family_cnname'];
//                            $parenterData['parenter_mobile'] = $family[$key]['family_mobile'];
//                            $this->DataControl->updateData("smc_parenter", "parenter_id='{$parenter_id['parenter_id']}'", $parenterData);
//                        }
//                    } else {
//                        if ($family[$key]['family_id'] == "") {
//                            $parenterData = array();
//                            $parenterData['parenter_cnname'] = $family[$key]['family_cnname'];
//                            $parenterData['parenter_mobile'] = $family[$key]['family_mobile'];
//                            $parenterData['parenter_addtime'] = time();
//                            if (!$insert_id = $this->DataControl->insertData("smc_parenter", $parenterData)) {
//                                $this->DataControl->rollback();
//                                $this->error = 1;
//                                $this->errortip = "新增家长失败";
//                                return false;
//                            }
//                            $familydata = array();
//                            $familydata['client_id'] = $paramArray['client_id'];
//                            $familydata['family_relation'] = $family[$key]['family_relation'];
//                            $familydata['parenter_id'] = $insert_id;
//                            $familydata['family_isdefault'] = $family[$key]['family_isdefault'];
//                            if (!$this->DataControl->insertData('crm_client_family', $familydata)) {
//                                $this->DataControl->rollback();
//                                $this->error = 1;
//                                $this->errortip = "新增家庭联系人失败";
//                                return false;
//                            }
//                        } else {
//                            $fa_parenter_id = $this->DataControl->getFieldOne('crm_client_family', "parenter_id", "family_id ='{$family[$key]['family_id']}'");
//                            $parenterData = array();
//                            $parenterData['parenter_cnname'] = $family[$key]['family_cnname'];
//                            $parenterData['parenter_mobile'] = $family[$key]['family_mobile'];
//                            if ($fa_parenter_id) {
//                                if (!$this->DataControl->updateData("smc_parenter", "parenter_id='{$fa_parenter_id['parenter_id']}'", $parenterData)) {
//                                    $this->DataControl->rollback();
//                                    $this->error = 1;
//                                    $this->errortip = "更新家长信息失败";
//                                    return false;
//                                } else {
//                                    continue;
//                                }
//                            } else {
//                                $this->DataControl->rollback();
//                                $this->error = 1;
//                                $this->errortip = "未查询到家长数据";
//                                return false;
//                            }
//                        }
//                    }
//                }
//            }
//        }

        //意向课程
        if ($paramArray['intention_course']) {
            $course = json_decode(stripslashes($paramArray['intention_course']), true);
            if (is_array($course)) {

                $data = array();
                $data['intention_is_delete'] = 1;
                $data['intention_updatetime'] = time();
                $this->DataControl->updateData("crm_client_intention", "client_id='{$paramArray['client_id']}'", $data);

//                $this->DataControl->delData("crm_client_intention", " client_id = '{$paramArray['client_id']}' ");
                foreach ($course as $coursevar) {
                    $intention = array();
                    $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id,coursetype_id", "coursecat_id='{$coursevar['coursecat_id']}'");
                    if ($coursecatOne) {
                        $intention['coursetype_id'] = $coursecatOne['coursetype_id'];
                        $intention['coursecat_id'] = $coursecatOne['coursecat_id'];
                    }
                    $intention['client_id'] = $paramArray['client_id'];
                    $intention['intention_updatetime'] = time();
                    if (!$this->DataControl->insertData("crm_client_intention", $intention)) {
                        $this->DataControl->rollback();
                        $this->error = 1;
                        $this->errortip = "新增意课程失败";
                        return false;
                    }
                }
            }
        }
        $this->DataControl->commit();
        $this->error = 0;
        $this->errortip = "修改成功";
        return true;
    }

    /**
     * @param $paramArray
     * @return array|bool
     * 集团招生 -- 高管审核 无意向名单
     */
    function lossExamine($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "用户参数无效";
            return false;
        } else {
            $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if (!$markOne) {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $markOne['marketer_id'] = $getaddmarkertOne['marketer_id'];
                $markOne['marketer_name'] = $getaddmarkertOne['marketer_name'];
            }
            foreach ($clients_array as $key => $value) {

                //增加分配日志 解除负责人的解除日志
                if (!$this->addRemoveAllotLog($value['client_id'], '-1', $markOne['marketer_id'], '')) {
                    $this->error = 1;
                    $this->errortip = "增加分配日志:解除日志失败";
                    return false;
                }

                //解除负责人的负责状态  一定要先添加解除日志(陆晶备注的）
                $dataPrincipal = array();
                $dataPrincipal['principal_leave'] = 1;
                $dataPrincipal['principal_updatatime'] = time();
                if (!$this->DataControl->updateData('crm_client_principal', "client_id='{$value['client_id']}'  ", $dataPrincipal)) {
                    $this->error = 1;
                    $this->errortip = "解除负责人失败";
                    return false;
                }

                $data = array();
                $data['client_ischaserlapsed'] = 1;
                $data['client_gmcdistributionstatus'] = 0;
                $data['client_tracestatus'] = '-1';
                $data['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $data);

                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['marketer_id'] = $markOne['marketer_id'];
                $trackData['marketer_name'] = $markOne['marketer_name'];
                $trackData['client_id'] = $value['client_id'];
                $trackData['track_linktype'] = $this->LgStringSwitch("系统操作");
                $trackData['track_note'] = $markOne['marketer_name'] . $this->LgStringSwitch("集团操作：主管审核无意向名单");
                $trackData['track_followmode'] = '-2';
                $trackData['track_validinc'] = 0;
                $trackData['track_state'] = -1;
                $trackData['school_id'] = '';//$paramArray['school_id']
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 0;
                $this->DataControl->insertData("crm_client_track", $trackData);
            }

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '集团操作：高管审核无意向名单', dataEncode($paramArray));
            $this->error = 0;
            $this->errortip = "已审核操作成功";
            return true;
        }
    }


    function lossInvalidExamineAction($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            $this->error = 1;
            $this->errortip = "用户参数无效";
            return false;
        } else {
            $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if (!$markOne) {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $markOne['marketer_id'] = $getaddmarkertOne['marketer_id'];
                $markOne['marketer_name'] = $getaddmarkertOne['marketer_name'];
            }
            foreach ($clients_array as $key => $value) {

                //增加分配日志 解除负责人的解除日志
                if (!$this->addRemoveAllotLog($value['client_id'], '-1', $markOne['marketer_id'], '')) {
                    $this->error = 1;
                    $this->errortip = "增加分配日志:解除日志失败";
                    return false;
                }

                //解除负责人的负责状态  一定要先添加解除日志(陆晶备注的）
                $dataPrincipal = array();
                $dataPrincipal['principal_leave'] = 1;
                $dataPrincipal['principal_updatatime'] = time();
                if (!$this->DataControl->updateData('crm_client_principal', "client_id='{$value['client_id']}'  ", $dataPrincipal)) {
                    $this->error = 1;
                    $this->errortip = "解除负责人失败";
                    return false;
                }

                $data = array();
                $data['client_distributionstatus'] = 0;
                $data['client_gmcdistributionstatus'] = 0;
                $data['client_tracestatus'] = -2;

                $data['client_isinvalidreview'] = 1;
                $data['client_invalidmarketer_id'] = $paramArray['marketer_id'];
                $data['client_invalidreviewtime'] = date("Y-m-d H:i:s",time());
                $data['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", $data);

                //解除负责人 --- 250711 补充
                $this->liftClientPrincipa('gmc',$value['client_id']);
                //解除学校
                $this->liftClientSchool('gmc',$value['client_id']);
                //解除柜询
                $this->liftClientInvite('gmc',$value['client_id']);
                //解除试听
                $this->liftClientAudition('gmc',$value['client_id']);

                $trackData = array();
                $trackData['client_id'] = $value['client_id'];
                $trackData['marketer_id'] = $markOne['marketer_id'];
                $trackData['marketer_name'] = $markOne['marketer_name'];
                $trackData['client_id'] = $value['client_id'];
                $trackData['track_linktype'] = $this->LgStringSwitch("主管审核");
                $trackData['track_note'] = $markOne['marketer_name'] . $this->LgStringSwitch("集团操作：主管审核无效名单");
                $trackData['track_followmode'] = '-4';
                $trackData['track_validinc'] = 0;
                $trackData['track_state'] = -2;
                $trackData['school_id'] = '';//$paramArray['school_id']
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 0;
                $this->DataControl->insertData("crm_client_track", $trackData);
            }

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '集团操作：高管审核无效名单', dataEncode($paramArray));
            $this->error = 0;
            $this->errortip = "已审核操作成功";
            return true;
        }
    }

    /**
     * @param $paramArray
     * @return array|bool
     * 集团招生 -- 删除无效名单
     */
    function dellossClient($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if (empty($clients_array)) {
            ajax_return([
                'error' => 1,
                'errortip' => "用户参数无效!",
                "bakfuntion" => "refreshpage"
            ]);
        } else {
            $client_id_array = array_column($clients_array, 'client_id');
            $client_idstr = implode(",", $client_id_array);

            $clientOne = $this->DataControl->selectOne("select * from crm_client where client_id in ($client_idstr) and client_tracestatus <> '-2'");
            if ($clientOne) {
                ajax_return([
                    'error' => 1,
                    'errortip' => "存在非无效名单，不可以操作!",
                    "bakfuntion" => "refreshpage"
                ]);
            }
//            $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
//            if (!$markOne) {
//                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
//                $markOne['marketer_id'] = $getaddmarkertOne['marketer_id'];
//                $markOne['marketer_name'] = $getaddmarkertOne['marketer_name'];
//            }
            foreach ($clients_array as $key => $value) {
                $this->DataControl->delData("crm_client", "client_id='{$value['client_id']}'");
            }

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团名单管理", '集团操作：删除无效名单', dataEncode($paramArray));
            ajax_return([
                'error' => 0,
                'errortip' => "删除成功!",
                "bakfuntion" => "refreshpage"
            ]);
        }
    }

    // 获取客户标签
    function getLabelApi($company_id, $client_id = '0', $paramArray)
    {
        $clientList = $this->DataControl->selectOne("SELECT client_tag from crm_client where client_id = '{$client_id}' ");
        $labelList = $clientList['client_tag'] ? explode(',', $clientList['client_tag']) : array();

        $datawhere = '';
        if (isset($paramArray['labeltype_type']) && $paramArray['labeltype_type'] != '') {
            $datawhere = " and labeltype_type like '%{$paramArray['labeltype_type']}%'  ";
        } else {
            $datawhere = " and labeltype_type like '%0%' ";
        }

        $labeltypeList = $this->DataControl->selectClear("SELECT labeltype_id,labeltype_name FROM crm_client_labeltype WHERE company_id = '{$company_id}' {$datawhere} ");
        if ($labeltypeList) {
            foreach ($labeltypeList as &$val) {
                $label = $this->DataControl->selectClear("SELECT label_id,label_name FROM crm_client_label WHERE labeltype_id = '{$val['labeltype_id']}'");
                $val['label'] = $label ? $label : array();
            }
        } else {
            $labeltypeList = array();
        }

        $data = array();
        $data['label'] = $labelList ? $labelList : array();
        $data['list'] = $labeltypeList;

        return $data;
    }

    // 获取集团活动
    function getGmcActivityApi($paramArray)
    {
        $GmcActivityList = $this->DataControl->selectClear("SELECT activity_id,activity_name from crm_sell_activity where company_id = '{$paramArray['company_id']}' and activity_type = '1' ");
        return $GmcActivityList;
    }

    //获取 全部学校
    function getComAllSchoolApi($paramArray)
    {
        $datawhere = "s.company_id = '{$paramArray['company_id']}' AND s.school_isclose <> '1'";
        if ($paramArray['district_id']) {
            $datawhere .= " AND s.district_id = '{$paramArray['district_id']}'";
        }

        $CourseCatDetail = $this->DataControl->selectClear(" SELECT s.school_id, s.school_branch,s.school_address,s.school_phone,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname FROM smc_school AS s WHERE {$datawhere} order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc");

        $field = array();
        $field["school_id"] = "学校id";
        $field["school_branch"] = "校区编号";
        $field["school_cnname"] = "校区名称";
        $field["school_address"] = "校园地址";
        $field["school_phone"] = "学校联系电话";

        $result = array();
        if ($CourseCatDetail) {
            $result["field"] = $field;
            $result["data"] = $CourseCatDetail;
            $res = array('error' => '0', 'errortip' => '获取学校列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取学校列表失败', 'result' => $result);
        }
        return $res;
    }

    //获取 全部渠道明细
    function getComAllChannelApi($request)
    {
        $where = "1 and  channel_isuse =1";

        if (isset($request['channel_medianame']) && $request['channel_medianame'] !== '' && $request['channel_medianame'] !== '[]') {
            $commodeArray = json_decode(stripslashes($request['channel_medianame']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';
                }
                $commodestr = substr($commodestr, 0, -1);
                $where .= " and c.channel_medianame in ({$commodestr}) ";
            } else {
                $where .= " and c.channel_medianame  = '{$request['channel_medianame']}'";
            }
        }
        $channelList = $this->DataControl->selectClear(" select c.channel_id,c.channel_name from crm_code_channel as c
                        LEFT JOIN crm_channel_organize as o ON c.channel_id = o.channel_id 
                        WHERE company_id = '{$request['company_id']}' and {$where}
                        GROUP BY c.channel_id ");

        if ($channelList) {
            $list = $channelList;
        } else {
            $list = array();
        }
        return $list;
    }

    /**
     * 集团柜询记录管理
     * @param $paramArray
     * @return array
     */
    public function getClientInviteList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class from gmc_staffer_postbe as p where  p.postrole_id > '0'  and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id = '{$paramArray['re_postbe_id']}' ");
            if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
                $this->error = 1;
                $this->errortip = "您的职务没有集团招生管理权限，无权查看！";
                return false;
            }
        }

        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND c.client_id = i.client_id AND p.marketer_id = m.marketer_id and p.school_id = '0' and p.marketer_id = i.marketer_id  ";// and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
//        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
//            $start_time = strtotime($paramArray['start_time']);
//            $datawhere .= " and UNIX_TIMESTAMP(i.invite_visittime) >= '{$start_time}'";
//        }
//        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
//            $end_time = strtotime($paramArray['end_time']);
//            $datawhere .= " and UNIX_TIMESTAMP(i.invite_visittime) <= '{$end_time}'";
//        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = date("Y-m-d 00:00:00",strtotime($paramArray['start_time']));
            $datawhere .= " and i.invite_visittime >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = date("Y-m-d 23:59:59",strtotime($paramArray['end_time']));
            $datawhere .= " and i.invite_visittime <= '{$end_time}'";
        }
        //请选择柜询状态
        if (isset($paramArray['invite_isvisit']) && $paramArray['invite_isvisit'] != '') {
            $datawhere .= " and i.invite_isvisit ='{$paramArray['invite_isvisit']}' ";
        }
        //请选择 校区
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and i.school_id ='{$paramArray['school_id']}' ";
        }
        //集团负责人
        if (isset($paramArray['main_staffer_id']) && $paramArray['main_staffer_id'] != '') {
            $datawhere .= " and m.staffer_id = '{$paramArray['main_staffer_id']}'";
        }
        //班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
            $somefields = " ,(select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where f.info_status=1 and d.from_client_id = c.client_id and f.school_id = i.school_id and f.coursetype_id = '{$paramArray['coursetype_id']}' limit 0,1 ) as info_id ";
        }

        $having = "1=1";
        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";
        //管辖的人
        if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

            if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                    $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                    $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                    $markertList = $this->DataControl->selectClear($sql);
                    $arr_marketer_id = array_column($markertList, "marketer_id");
                    $str_marketer_id = implode(',', $arr_marketer_id);

                    $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                } else {
                    $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                    $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                }
            }
        } else {
            $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
            $markertList = $this->DataControl->selectClear($sql);
            $arr_marketer_id = array_column($markertList, "marketer_id");
            $str_marketer_id = implode(',', $arr_marketer_id);

            //包含管理员
            $accountmarker = $this->DataControl->selectClear("select m.marketer_id from smc_staffer as s 
                            left join crm_marketer as m ON s.staffer_id = m.staffer_id 
                            where s.account_class = '1' and s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' ");
            if (is_array($accountmarker)) {
                $arr_accountmarketer_id = array_column($accountmarker, "marketer_id");
                $str_accountmarketer_id = implode(',', $arr_accountmarketer_id);
                $str_marketer_id .= "," . $str_accountmarketer_id;
            }
            $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "c.client_id,c.client_cnname,c.client_enname,c.client_age,c.client_birthday,c.client_sex,c.client_mobile,c.client_tag,c.client_tracestatus,
                c.client_patriarchname,c.client_remark,c.client_intention_level,c.client_gmcdistributionstatus,c.client_answerphone,
                from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,
                i.invite_id,i.school_id,i.invite_genre,i.invite_isvisit,i.invite_novisitreason,i.invite_createtime,i.receiver_name,i.invite_visittime as invite_visittime,
                l.channel_medianame, l.channel_name, 
                m.marketer_id,m.marketer_name as principal_name,
                (select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
                (select s.school_shortname from smc_school as s where s.school_id = i.school_id) as school_shortname,
                (select f.family_cnname from crm_client_family as f where f.client_id = c.client_id and f.family_isdefault = '1' limit 0,1 ) as client_mainfamilyname,
                (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id ";
        $sql = "SELECT {$fields}{$somefields}
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_invite i 
WHERE {$datawhere} HAVING {$having} ORDER BY i.invite_visittime DESC ";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if ($dateexcelarray) {
                $inviteisvisitstatus = $this->LgArraySwitch(array('0' => '待到访', '1' => '已到访', '-1' => '未到访', '2' => '已改约'));
                $genrestatus = $this->LgArraySwitch(array('0' => '普通柜询', '1' => '能力测试', '2' => '推带到访', '3' => '主动到访'));
                foreach ($dateexcelarray as $key => $value) {
                    if (is_null($value['client_patriarchname']) || $value['client_patriarchname'] == '') {
                        $dateexcelarray[$key]['client_patriarchname'] = is_null($value['client_mainfamilyname']) ? "--" : $value['client_mainfamilyname'];
                    }
                    if ($value['client_enname']) {
                        $dateexcelarray[$key]['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                    }
                    if ($value['client_answerphone'] == 1) {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                        if ($value['client_intention_level'] >= 3) {
//                            $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                        } else {
//                            $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                        }
                    } else {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                    }
//                    if(!is_null($value['course_cnname'])){
//                        $dateexcelarray[$key]['course_cnname'] = explode(',', $value['course_cnname']);
//                    }else{
//                        $dateexcelarray[$key]['course_cnname'] = array();
//                    }
                    if ($stafferOne['account_class'] == '1') {
                        $dateexcelarray[$key]['client_mobile'] = $value['client_mobile'];
                    } else {
                        $dateexcelarray[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                    }
                    $dateexcelarray[$key]['invite_isvisitname'] = $inviteisvisitstatus[$value['invite_isvisit']];
                    $dateexcelarray[$key]['invite_genrename'] = $genrestatus[$value['invite_genre']];

                    if($value['invite_isvisit'] == 2){
                        $dateexcelarray[$key]['invite_novisitreason_two'] = $value['invite_novisitreason'];
                        $dateexcelarray[$key]['invite_novisitreason'] = '--';
                    }else{
                        $dateexcelarray[$key]['invite_novisitreason_two'] = '--';
                    }

                    if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
                        if (isset($value['info_id']) && !is_null($value['info_id']) && $value['info_id'] != '' && $value['info_id'] > 0) {
                            $dateexcelarray[$key]['issignup'] = $this->LgStringSwitch('已报名');
                        } else {
                            $dateexcelarray[$key]['issignup'] = $this->LgStringSwitch('未报名');
                        }
                    } else {
                        $dateexcelarray[$key]['issignup'] = '--';
                    }

                    $courselist = $this->DataControl->selectClear(" select c.coursecat_cnname,c.coursecat_branch from crm_client_intention as i left join smc_code_coursecat as c ON c.coursecat_id = i.coursecat_id where i.client_id = '{$value['client_id']}' and i.coursecat_id > 0 ");
                    if (is_array($courselist)) {
                        $arr_coursecat_cnname = array_column($courselist, "coursecat_cnname");
                        $dateexcelarray[$key]['coursecat_cnname'] = implode(',', $arr_coursecat_cnname);
                    } else {
                        $dateexcelarray[$key]['coursecat_cnname'] = '--';
                    }

                    $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
                    if ($staffer) {
                        $marketer_id = $staffer['marketer_id'];
                    } else {
                        $marketer_id = 0;
                    }
                    if ($this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and dialrecord_type = 0")) {
                        $dateexcelarray[$key]['dialrecord'] = '1';
                    } else {
                        $dateexcelarray[$key]['dialrecord'] = '0';
                    }
                    //是否禁用电话按钮
                    if ($value['client_tracestatus'] == '-1' || $value['client_tracestatus'] == '-2' || $value['client_tracestatus'] == '4') {
                        $dateexcelarray[$key]['isdisable'] = '1';
                    } else {
                        $dateexcelarray[$key]['isdisable'] = '0';
                    }
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_tag'] = $dateexcelvar['client_tag'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['invite_isvisitname'] = $dateexcelvar['invite_isvisitname'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['invite_genrename'] = $dateexcelvar['invite_genrename'];
                    $datearray['invite_visittime'] = $dateexcelvar['invite_visittime'];
                    $datearray['invite_novisitreason'] = $dateexcelvar['invite_novisitreason'];
                    $datearray['invite_novisitreason_two'] = $dateexcelvar['invite_novisitreason_two'];
                    $datearray['issignup'] = $dateexcelvar['issignup'];
                    $datearray['principal_name'] = $dateexcelvar['principal_name'];
                    $datearray['receiver_name'] = $dateexcelvar['receiver_name'];
                    $datearray['client_patriarchname'] = $dateexcelvar['client_patriarchname'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('姓名', '标签', '意向课程', '性别', '年龄', '意向星级', '柜询状态', '柜询校区', '柜询类型', '柜询日期', '未到访原因', '改约原因', '报名状态', '负责人', '接待人', '主要联系人', '主要联系手机', '意向课程', '渠道类型', '渠道明细', '名单创建时间'));
            $excelfileds = array('client_cnname', 'client_tag', 'course_cnname', 'client_sex', 'client_age', 'client_intention_level', 'invite_isvisitname', 'school_shortname', 'invite_genrename', 'invite_visittime', 'invite_novisitreason', 'invite_novisitreason_two', 'issignup', 'principal_name', 'receiver_name', 'client_patriarchname', 'client_mobile', 'coursecat_cnname', 'channel_medianame', 'channel_name', 'client_createtime');

            $tem_name = $this->LgStringSwitch('柜询记录管理') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}  ";
        }

        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $inviteisvisitstatus = $this->LgArraySwitch(array('0' => '待到访', '1' => '已到访', '-1' => '未到访', '2' => '已改约'));
            $genrestatus = $this->LgArraySwitch(array('0' => '普通柜询', '1' => '能力测试', '2' => '推带到访', '3' => '主动到访'));
            foreach ($clientList as $key => &$value) {
                if (is_null($value['client_patriarchname']) || $value['client_patriarchname'] == '') {
                    $value['client_patriarchname'] = is_null($value['client_mainfamilyname']) ? "--" : $value['client_mainfamilyname'];
                }
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                }
                if ($value['client_answerphone'] == 1) {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                    if ($value['client_intention_level'] >= 3) {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                    } else {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                    }
                } else {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $clientList[$key]['course_cnname'] = array();
                }
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                }
                $clientList[$key]['invite_isvisitname'] = $inviteisvisitstatus[$value['invite_isvisit']];
                $clientList[$key]['invite_genrename'] = $genrestatus[$value['invite_genre']];

                if($value['invite_isvisit'] == 2){
                    $clientList[$key]['invite_novisitreason_two'] = $value['invite_novisitreason'];
                    $clientList[$key]['invite_novisitreason'] = '--';
                }else{
                    $clientList[$key]['invite_novisitreason_two'] = '--';
                }

                if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
                    if (isset($value['info_id']) && !is_null($value['info_id']) && $value['info_id'] != '' && $value['info_id'] > 0) {
                        $clientList[$key]['issignup'] = $this->LgStringSwitch('已报名');
                    } else {
                        $clientList[$key]['issignup'] = $this->LgStringSwitch('未报名');
                    }
                } else {
                    $clientList[$key]['issignup'] = '--';
                }

                $courselist = $this->DataControl->selectClear(" select c.coursecat_cnname,c.coursecat_branch from crm_client_intention as i left join smc_code_coursecat as c ON c.coursecat_id = i.coursecat_id where i.client_id = '{$value['client_id']}' and i.coursecat_id > 0 ");
                if (is_array($courselist)) {
                    $arr_coursecat_cnname = array_column($courselist, "coursecat_cnname");
                    $clientList[$key]['coursecat_cnname'] = implode(',', $arr_coursecat_cnname);
                } else {
                    $clientList[$key]['coursecat_cnname'] = '--';
                }

                $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
                if ($staffer) {
                    $marketer_id = $staffer['marketer_id'];
                } else {
                    $marketer_id = 0;
                }
                if ($this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and dialrecord_type = 0")) {
                    $value['dialrecord'] = '1';
                } else {
                    $value['dialrecord'] = '0';
                }
                //是否禁用电话按钮
                if ($value['client_tracestatus'] == '-1' || $value['client_tracestatus'] == '-2' || $value['client_tracestatus'] == '4') {
                    $value['isdisable'] = '1';
                } else {
                    $value['isdisable'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->selectClear("SELECT c.client_id,c.client_gmcdistributionstatus,
                (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id 
                FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_invite i 
                WHERE {$datawhere} HAVING {$having}  ");
        $allnums = is_array($all_num) ? count($all_num) : 0;


        $Schoolsql = "SELECT c.client_id,c.client_gmcdistributionstatus,i.school_id,(select s.school_shortname from smc_school as s where s.school_id = i.school_id) as school_shortname,(select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_invite i 
WHERE c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND c.client_id = i.client_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'  and i.school_id > 0 group by i.school_id HAVING {$having}  ";
        $TheSchoolList = $this->DataControl->selectClear($Schoolsql);


        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;
        $result['TheSchool'] = $TheSchoolList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }

    /**
     * 集团试听记录管理
     * @param $paramArray
     * @return array
     */
    public function getClientAuditionList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class from gmc_staffer_postbe as p where  p.postrole_id > '0'  and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id = '{$paramArray['re_postbe_id']}' ");
            if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
                $this->error = 1;
                $this->errortip = "您的职务没有集团招生管理权限，无权查看！";
                return false;
            }
        }

        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND c.client_id = a.client_id AND p.marketer_id = m.marketer_id  and p.school_id = '0'  ";// and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4'
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%' 
or (select ii.intention_id FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id and (oo.coursecat_cnname like '%{$paramArray['keyword']}%' or oo.coursecat_branch like '%{$paramArray['keyword']}%') limit 0,1 ) > 1)";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = strtotime($paramArray['start_time']);
            $datawhere .= " and UNIX_TIMESTAMP(a.audition_visittime) >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = strtotime($paramArray['end_time']) + 86399;
            $datawhere .= " and UNIX_TIMESTAMP(a.audition_visittime) <= '{$end_time}'";
        }
        //请选择柜询状态
        if (isset($paramArray['audition_isvisit']) && $paramArray['audition_isvisit'] != '') {
            $datawhere .= " and a.audition_isvisit ='{$paramArray['audition_isvisit']}' ";
        }
        //请选择 校区
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and a.school_id ='{$paramArray['school_id']}' ";
        }
        //请选择 类型
        if (isset($paramArray['audition_genre']) && $paramArray['audition_genre'] != '') {
            $datawhere .= " and a.audition_genre ='{$paramArray['audition_genre']}' ";
        }
        //请选择 班级
        if (isset($paramArray['class_id']) && $paramArray['class_id'] != '') {
            $datawhere .= " and a.class_id ='{$paramArray['class_id']}' ";
        }
        //集团负责人
        if (isset($paramArray['main_staffer_id']) && $paramArray['main_staffer_id'] != '') {
            $datawhere .= " and m.staffer_id = '{$paramArray['main_staffer_id']}'";
        }
        //班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
            $somefields = " ,(select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where f.info_status=1 and d.from_client_id = c.client_id and f.school_id = a.school_id and f.coursetype_id = '{$paramArray['coursetype_id']}' limit 0,1 ) as info_id ";
        }

        $having = "1=1";
        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";
        //管辖的人
        if ($this->stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_id,p.organize_id,p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class 
                                from gmc_staffer_postbe as p 
                                where  p.postrole_id > '0' and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id ='{$paramArray['re_postbe_id']}' ");

            if ($stafferPostbeOne['postbe_isgmccrm'] == '1') {
                if ($stafferPostbeOne['postbe_gmccrmlevel'] == '1') {
                    $organizeWhere = " or (p.organize_id in (SELECT o.organize_id FROM gmc_company_organize o
            WHERE o.father_id IN ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' )) AND p.postbe_isgmccrm = 1 ) ";

                    $orgdatawhere = " ( 
                    ( p.organize_id = '{$stafferPostbeOne['organize_id']}' AND p.postbe_isgmccrm = '1' AND p.postbe_gmccrmlevel = '0') 
                    or (p.organize_id in ( SELECT b.organize_id FROM gmc_staffer_postbe b WHERE b.postbe_id = '{$stafferPostbeOne['postbe_id']}' ) AND p.postbe_isgmccrm = 1 ) 
                    {$organizeWhere} 
                    ) ";

                    $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE {$orgdatawhere} and m.marketer_id <> '' ";
                    $markertList = $this->DataControl->selectClear($sql);
                    $arr_marketer_id = array_column($markertList, "marketer_id");
                    $str_marketer_id = implode(',', $arr_marketer_id);

                    $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
                } else {
                    $markertOne = $this->DataControl->selectOne("SELECT m.marketer_id from crm_marketer as m where m.staffer_id = '{$paramArray['staffer_id']}' ");
                    $datawhere .= " and p.marketer_id = '{$markertOne['marketer_id']}' ";
                }
            }
        } else {
            $sql = "SELECT p.staffer_id,m.marketer_id 
            FROM gmc_staffer_postbe AS p 
            LEFT JOIN crm_marketer as m ON m.staffer_id = p.staffer_id WHERE p.postbe_isgmccrm = 1  and m.marketer_id <> '' ";
            $markertList = $this->DataControl->selectClear($sql);
            $arr_marketer_id = array_column($markertList, "marketer_id");
            $str_marketer_id = implode(',', $arr_marketer_id);

            //包含管理员
            $accountmarker = $this->DataControl->selectClear("select m.marketer_id from smc_staffer as s 
                            left join crm_marketer as m ON s.staffer_id = m.staffer_id 
                            where s.account_class = '1' and s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' ");
            if (is_array($accountmarker)) {
                $arr_accountmarketer_id = array_column($accountmarker, "marketer_id");
                $str_accountmarketer_id = implode(',', $arr_accountmarketer_id);
                $str_marketer_id .= "," . $str_accountmarketer_id;
            }
            $datawhere .= " and p.marketer_id in ({$str_marketer_id}) ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "c.client_id,c.client_cnname,c.client_enname,c.client_age,c.client_birthday,c.client_sex,c.client_mobile,c.client_tag,c.client_tracestatus,
                c.client_patriarchname,c.client_remark,c.client_intention_level,c.client_gmcdistributionstatus,c.client_answerphone,
                from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,
                a.audition_id,a.school_id,a.audition_genre,a.audition_isvisit,a.audition_novisitreason,a.audition_createtime,a.receiver_name,a.class_id,a.audition_visittime AS audition_visittime,
                l.channel_medianame, l.channel_name, 
                m.marketer_id,m.marketer_name as principal_name,
(select  GROUP_CONCAT(DISTINCT oo.coursecat_branch) FROM crm_client_intention as ii LEFT JOIN  smc_code_coursecat as oo ON oo.coursecat_id=ii.coursecat_id WHERE ii.client_id=c.client_id ) as course_cnname,
                a.class_cnname,
                (select s.school_shortname from smc_school as s where s.school_id = a.school_id) as school_shortname,
                (select f.family_cnname from crm_client_family as f where f.client_id = c.client_id and f.family_isdefault = '1' limit 0,1 ) as client_mainfamilyname,
                (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id ";
        $sql = "SELECT {$fields}{$somefields}
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_audition a 
WHERE {$datawhere}  HAVING {$having}  ORDER BY a.audition_visittime DESC  ";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if ($dateexcelarray) {
                $inviteisvisitstatus = $this->LgArraySwitch(array('0' => '待试听', '1' => '已试听', '-1' => '未试听', '2' => '已改约'));
                $genrestatus = $this->LgArraySwitch(array('0' => '普通公开课试听', '1' => '插班试听', '2' => '试读公开课试听'));
                foreach ($dateexcelarray as $key => &$value) {
                    if (is_null($value['client_patriarchname']) || $value['client_patriarchname'] == '') {
                        $value['client_patriarchname'] = is_null($value['client_mainfamilyname']) ? "--" : $value['client_mainfamilyname'];
                    }
                    if ($value['client_enname']) {
                        $value['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                    }
                    if ($value['client_answerphone'] == 1) {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                        if ($value['client_intention_level'] >= 3) {
//                            $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                        } else {
//                            $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                        }
                    } else {
                        $dateexcelarray[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                    }
//                    if(!is_null($value['course_cnname'])){
//                        $dateexcelarray[$key]['course_cnname'] = explode(',', $value['course_cnname']);
//                    }else{
//                        $dateexcelarray[$key]['course_cnname'] = array();
//                    }
                    if ($stafferOne['account_class'] == '1') {
                        $dateexcelarray[$key]['client_mobile'] = $value['client_mobile'];
                    } else {
                        $dateexcelarray[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                    }
                    $dateexcelarray[$key]['audition_isvisitname'] = $inviteisvisitstatus[$value['audition_isvisit']];
                    $dateexcelarray[$key]['audition_genrename'] = $genrestatus[$value['audition_genre']];

                    if($value['audition_isvisit'] == 2){
                        $dateexcelarray[$key]['audition_novisitreason_two'] = $value['audition_novisitreason'];
                        $dateexcelarray[$key]['audition_novisitreason'] = '--';
                    }else{
                        $dateexcelarray[$key]['audition_novisitreason_two'] = '--';
                    }

                    if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
                        if (isset($value['info_id']) && !is_null($value['info_id']) && $value['info_id'] != '' && $value['info_id'] > 0) {
                            $dateexcelarray[$key]['issignup'] = $this->LgStringSwitch('已报名');
                        } else {
                            $dateexcelarray[$key]['issignup'] = $this->LgStringSwitch('未报名');
                        }
                    } else {
                        $dateexcelarray[$key]['issignup'] = '--';
                    }

                    $courselist = $this->DataControl->selectClear(" select c.coursecat_cnname,c.coursecat_branch from crm_client_intention as i left join smc_code_coursecat as c ON c.coursecat_id = i.coursecat_id where i.client_id = '{$value['client_id']}' and i.coursecat_id > 0 ");
                    if (is_array($courselist)) {
                        $arr_coursecat_cnname = array_column($courselist, "coursecat_cnname");
                        $dateexcelarray[$key]['coursecat_cnname'] = implode(',', $arr_coursecat_cnname);
                    } else {
                        $dateexcelarray[$key]['coursecat_cnname'] = '--';
                    }

                    $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
                    if ($staffer) {
                        $marketer_id = $staffer['marketer_id'];
                    } else {
                        $marketer_id = 0;
                    }
                    if ($this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and dialrecord_type = 0")) {
                        $value['dialrecord'] = '1';
                    } else {
                        $value['dialrecord'] = '0';
                    }
                    //是否禁用电话按钮
                    if ($value['client_tracestatus'] == '-1' || $value['client_tracestatus'] == '-2' || $value['client_tracestatus'] == '4') {
                        $value['isdisable'] = '1';
                    } else {
                        $value['isdisable'] = '0';
                    }
                }
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_tag'] = $dateexcelvar['client_tag'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_intention_level'] = $dateexcelvar['client_intention_level'];
                    $datearray['audition_isvisitname'] = $dateexcelvar['audition_isvisitname'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['audition_genrename'] = $dateexcelvar['audition_genrename'];
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['audition_novisitreason'] = $dateexcelvar['audition_novisitreason'];
                    $datearray['audition_novisitreason_two'] = $dateexcelvar['audition_novisitreason_two'];
                    $datearray['issignup'] = $dateexcelvar['issignup'];
                    $datearray['principal_name'] = $dateexcelvar['principal_name'];
                    $datearray['receiver_name'] = $dateexcelvar['receiver_name'];
                    $datearray['client_patriarchname'] = $dateexcelvar['client_patriarchname'];
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['channel_medianame'] = $dateexcelvar['channel_medianame'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['client_createtime'] = $dateexcelvar['client_createtime'];

                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('姓名', '标签', '意向课程', '性别', '年龄', '意向星级', '试听状态', '试听校区', '试听类型', '试听日期', '试听班级', '未到访原因', '改约原因', '报名状态', '负责人', '接待人', '主要联系人', '主要联系手机', '意向课程', '渠道类型', '渠道明细', '创建时间'));
            $excelfileds = array('client_cnname', 'client_tag', 'course_cnname', 'client_sex', 'client_age', 'client_intention_level', 'audition_isvisitname', 'school_shortname', 'audition_genrename', 'audition_visittime', 'class_cnname', 'audition_novisitreason', 'audition_novisitreason_two', 'issignup', 'principal_name', 'receiver_name', 'client_patriarchname', 'client_mobile', 'coursecat_cnname', 'channel_medianame', 'channel_name', 'client_createtime');

            $tem_name = $this->LgStringSwitch('试听记录管理') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}  ";
        }
        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $inviteisvisitstatus = $this->LgArraySwitch(array('0' => '待试听', '1' => '已试听', '-1' => '未试听', '2' => '已改约'));
            $genrestatus = $this->LgArraySwitch(array('0' => '普通公开课试听', '1' => '插班试听', '2' => '试读公开课试听'));
            foreach ($clientList as $key => &$value) {
                if (is_null($value['client_patriarchname']) || $value['client_patriarchname'] == '') {
                    $value['client_patriarchname'] = is_null($value['client_mainfamilyname']) ? "--" : $value['client_mainfamilyname'];
                }
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                }
                if ($value['client_answerphone'] == 1) {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                    if ($value['client_intention_level'] >= 3) {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                    } else {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                    }
                } else {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
                if ($value['client_tag']) {
                    $clientList[$key]['client_tag'] = explode(',', $value['client_tag']);
                } else {
                    $clientList[$key]['client_tag'] = array();
                }
                if (!is_null($value['course_cnname'])) {
                    $clientList[$key]['course_cnname'] = explode(',', $value['course_cnname']);
                } else {
                    $clientList[$key]['course_cnname'] = array();
                }
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                }
                $clientList[$key]['audition_isvisitname'] = $inviteisvisitstatus[$value['audition_isvisit']];
                $clientList[$key]['audition_genrename'] = $genrestatus[$value['audition_genre']];

                if($value['audition_isvisit'] == 2){
                    $clientList[$key]['audition_novisitreason_two'] = $value['audition_novisitreason'];
                    $clientList[$key]['audition_novisitreason'] = '--';
                }else{
                    $clientList[$key]['audition_novisitreason_two'] = '--';
                }

                if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
                    if (isset($value['info_id']) && !is_null($value['info_id']) && $value['info_id'] != '' && $value['info_id'] > 0) {
                        $clientList[$key]['issignup'] = $this->LgStringSwitch('已报名');
                    } else {
                        $clientList[$key]['issignup'] = $this->LgStringSwitch('未报名');
                    }
                } else {
                    $clientList[$key]['issignup'] = '--';
                }

                $courselist = $this->DataControl->selectClear(" select c.coursecat_cnname,c.coursecat_branch from crm_client_intention as i left join smc_code_coursecat as c ON c.coursecat_id = i.coursecat_id where i.client_id = '{$value['client_id']}' and i.coursecat_id > 0 ");
                if (is_array($courselist)) {
                    $arr_coursecat_cnname = array_column($courselist, "coursecat_cnname");
                    $clientList[$key]['coursecat_cnname'] = implode(',', $arr_coursecat_cnname);
                } else {
                    $clientList[$key]['coursecat_cnname'] = '--';
                }

                $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
                if ($staffer) {
                    $marketer_id = $staffer['marketer_id'];
                } else {
                    $marketer_id = 0;
                }
                if ($this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and dialrecord_type = 0")) {
                    $value['dialrecord'] = '1';
                } else {
                    $value['dialrecord'] = '0';
                }
                //是否禁用电话按钮
                if ($value['client_tracestatus'] == '-1' || $value['client_tracestatus'] == '-2' || $value['client_tracestatus'] == '4') {
                    $value['isdisable'] = '1';
                } else {
                    $value['isdisable'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->selectClear("SELECT c.client_id,c.client_gmcdistributionstatus,
                (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id 
                FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_audition a 
                WHERE {$datawhere} HAVING {$having}   ");
        $allnums = is_array($all_num) ? count($all_num) : 0;


        $Schoolsql = "SELECT c.client_id,c.client_gmcdistributionstatus,a.school_id,(select s.school_shortname from smc_school as s where s.school_id = a.school_id) as school_shortname,(select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_audition a 
WHERE c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND c.client_id = a.client_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4' and a.school_id > 0 group by a.school_id HAVING {$having}  ";
        $TheSchoolList = $this->DataControl->selectClear($Schoolsql);

        $Classsql = "SELECT c.client_id,c.client_gmcdistributionstatus,a.class_id, (select s.class_cnname from smc_class as s where s.class_id = a.class_id) as class_cnname,(select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id 
FROM crm_client c, crm_client_principal p, crm_marketer m, crm_code_channel l,crm_client_audition a 
WHERE c.company_id = '{$paramArray['company_id']}' AND c.client_id = p.client_id AND c.channel_id = l.channel_id AND c.client_id = a.client_id AND p.marketer_id = m.marketer_id  AND p.principal_leave = '0' and p.school_id = '0'  and  c.client_tracestatus >= '0' and  c.client_tracestatus < '4' and a.class_id > 0  group by a.class_id HAVING {$having}  ";
        $TheClasslList = $this->DataControl->selectClear($Classsql);


        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;
        $result['TheSchool'] = $TheSchoolList;
        $result['TheClass'] = $TheClasslList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }

    /**
     * 集团已登记学籍名单
     * @param $paramArray
     * @return array
     */
    public function getClientConversionList($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($stafferOne['account_class'] != '1' && $paramArray['re_postbe_id'] > 0) {
            $stafferPostbeOne = $this->DataControl->selectOne("select p.postbe_isgmccrm,p.postbe_gmccrmlevel,(select s.account_class from smc_staffer as s where s.staffer_id = p.staffer_id) as account_class from gmc_staffer_postbe as p where  p.postrole_id > '0'  and p.postbe_status = '1' and p.staffer_id = '{$paramArray['staffer_id']}' and p.company_id = '{$paramArray['company_id']}' and p.postbe_id = '{$paramArray['re_postbe_id']}' ");
            if ($stafferPostbeOne['postbe_isgmccrm'] != '1') {
                $this->error = 1;
                $this->errortip = "您的职务没有集团招生管理权限，无权查看！";
                return false;
            }
        }

        $datawhere = "c.company_id = '{$paramArray['company_id']}' AND g.client_id = c.client_id AND c.client_id = p.client_id AND p.principal_leave = '0' AND p.school_id = '0'AND p.marketer_id = m.marketer_id  AND c.channel_id = l.channel_id  ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or g.student_branch like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_tag like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $start_time = strtotime($paramArray['start_time']);
//            $datawhere .= " and UNIX_TIMESTAMP(g.conversionlog_time) >= '{$start_time}'";
            $datawhere .= " and g.conversionlog_time >= '{$start_time}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $end_time = strtotime($paramArray['end_time']) + 86399;
//            $datawhere .= " and UNIX_TIMESTAMP(g.conversionlog_time) <= '{$end_time}'";
            $datawhere .= " and g.conversionlog_time <= '{$end_time}'";
        }
        //请勿改，改之前问
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] !== '' && $paramArray['channel_id'] != '[]') {
            $channelArray = json_decode(stripslashes($paramArray['channel_id']), 1);
            if (is_array($channelArray) && count($channelArray) > 0) {
                $channelstr = '';
                foreach ($channelArray as $channelvar) {
                    $channelstr .= "'" . $channelvar . "'" . ',';
                }
                $channelstr = substr($channelstr, 0, -1);
                $datawhere .= " and c.channel_id in ({$channelstr}) ";
            }
        }
        //请勿改，改之前问
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] !== '' && $paramArray['frommedia_name'] !== '[]') {
            $commodeArray = json_decode(stripslashes($paramArray['frommedia_name']), 1);
            if (is_array($commodeArray) && count($commodeArray) > 0) {
                $commodestr = '';
                foreach ($commodeArray as $commodevar) {
                    $commodestr .= "'" . $commodevar . "'" . ',';

                }
                $commodestr = substr($commodestr, 0, -1);
                $datawhere .= " and c.client_source in ({$commodestr}) ";
            }
        }
        //集团负责人
        if (isset($paramArray['main_staffer_id']) && $paramArray['main_staffer_id'] != '') {
            $datawhere .= " and m.staffer_id = '{$paramArray['main_staffer_id']}'";
        }
        //班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
            $somefields = " ,(select f.info_id from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where f.info_status=1 and d.from_client_id = c.client_id and f.school_id = g.school_id and f.coursetype_id = '{$paramArray['coursetype_id']}' limit 0,1 ) as info_id
                            ,(select f.pay_firsttime from smc_student as d left join smc_student_registerinfo as f ON d.student_id = f.student_id where f.info_status=1 and d.from_client_id = c.client_id and f.school_id = g.school_id and f.coursetype_id = '{$paramArray['coursetype_id']}' order by f.info_id ASC limit 0,1 ) as pay_firsttime ";

//            $somefieldsone = " ,(SELECT o.order_pid,p.pay_successtime,p.pay_price FROM smc_payfee_order as o ,smc_payfee_order_pay as p ,smc_student as s
//            WHERE s.student_branch = g.student_branch and o.student_id = s.student_id and o.order_pid = p.order_pid and o.coursetype_id = '{$paramArray['coursetype_id']}' and p.pay_issuccess = '1' ORDER BY p.pay_successtime DESC limit 0,1) as paytimeone,
//            (SELECT o.order_pid,p.pay_successtime,p.pay_price FROM smc_payfee_order as o,smc_payfee_order_course as c,smc_course as u,smc_payfee_order_pay as p,smc_student as s
//WHERE s.student_branch = g.student_branch and o.student_id = s.student_id and o.order_pid = c.order_pid and c.course_id = u.course_id and o.order_pid = p.order_pid and u.coursetype_id = '{$paramArray['coursetype_id']}' and p.pay_issuccess = '1' ORDER BY p.pay_successtime DESC limit 0,1) as paytimetwo ";
        }

        $having = "1=1";
        //确定名单是集团分配出去的
        $having .= " and ((schoolenter_id > 0 and schoolenter_id is not null) or c.client_gmcdistributionstatus = '1') ";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "c.client_id,c.client_cnname,g.student_branch,c.client_sex,c.client_age,from_unixtime(g.conversionlog_time, '%Y-%m-%d') AS conversionlog_time,c.client_gmcdistributionstatus,c.client_patriarchname,c.client_mobile,c.activity_id,c.client_tracestatus,c.client_intention_level,c.client_answerphone,from_unixtime(c.client_createtime, '%Y-%m-%d') AS client_createtime,
            l.channel_medianame, l.channel_name, 
            m.marketer_id,m.marketer_name as principal_name, 
            (select a.activity_name from crm_sell_activity as a where a.activity_id = c.activity_id limit 0,1 ) as activity_name,
            (select f.family_cnname from crm_client_family as f where f.client_id = c.client_id and f.family_isdefault = '1' limit 0,1 ) as client_mainfamilyname,
            (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id  ";
        $sql = "SELECT {$fields}{$somefields}
FROM crm_client_conversionlog g ,crm_client c,crm_client_principal p,crm_marketer m,crm_code_channel l  
WHERE {$datawhere} GROUP BY c.client_id HAVING {$having} ORDER BY g.conversionlog_time DESC LIMIT {$pagestart},{$num} ";
        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            foreach ($clientList as $key => &$value) {
                if (is_null($value['client_patriarchname']) || $value['client_patriarchname'] == '') {
                    $value['client_patriarchname'] = is_null($value['client_mainfamilyname']) ? "--" : $value['client_mainfamilyname'];
                }
                $value['activity_name'] = is_null($value['activity_name']) ? "--" : $value['activity_name'];
                if ($value['client_enname']) {
                    $value['client_cnname'] = $value['client_cnname'] . '-' . $value['client_enname'];
                }
                if ($value['client_answerphone'] == 1) {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("已接通");
//                    if ($value['client_intention_level'] >= 3) {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通有效");
//                    } else {
//                        $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("接通无效");
//                    }
                } else {
                    $clientList[$key]['client_answerphone'] = $this->LgStringSwitch("未接通");
                }
                if ($stafferOne['account_class'] == '1') {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];
                } else {
                    $clientList[$key]['client_mobile'] = $value['client_mobile'];//hideNumberString($value['client_mobile']);
                }

                if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
                    if (isset($value['info_id']) && !is_null($value['info_id']) && $value['info_id'] != '' && $value['info_id'] > 0) {
                        $clientList[$key]['issignup'] = $this->LgStringSwitch('已报名');
                        $clientList[$key]['classpaytime'] = date("Y-m-d H:i:s", $value['pay_firsttime']);

//                        if(!is_null($value['paytimeone']) || !is_null($value['paytimetwo'])){
//                            if($value['paytimeone'] <= $value['paytimetwo']){
//                                $clientList[$key]['classpaytime'] = date("Y-m-d H:i:s",$value['paytimeone']);
//                            }elseif($value['paytimeone'] > $value['paytimetwo']){
//                                $clientList[$key]['classpaytime'] = date("Y-m-d H:i:s",$value['paytimetwo']);
//                            }else{
//                                $clientList[$key]['classpaytime'] = '--';
//                            }
//                        }
                    } else {
                        $clientList[$key]['issignup'] = $this->LgStringSwitch('未报名');
                        $clientList[$key]['classpaytime'] = '--';
                    }
                } else {
                    $clientList[$key]['issignup'] = '--';
                    $clientList[$key]['classpaytime'] = '--';
                }

                $courselist = $this->DataControl->selectClear(" select c.coursecat_cnname,c.coursecat_branch from crm_client_intention as i left join smc_code_coursecat as c ON c.coursecat_id = i.coursecat_id where i.client_id = '{$value['client_id']}' and i.coursecat_id > 0 ");
                if (is_array($courselist)) {
                    $arr_coursecat_cnname = array_column($courselist, "coursecat_cnname");
                    $clientList[$key]['coursecat_cnname'] = implode(',', $arr_coursecat_cnname);
                } else {
                    $clientList[$key]['coursecat_cnname'] = '--';
                }

                $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
                if ($staffer) {
                    $marketer_id = $staffer['marketer_id'];
                } else {
                    $marketer_id = 0;
                }
                if ($this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and dialrecord_type = 0")) {
                    $value['dialrecord'] = '1';
                } else {
                    $value['dialrecord'] = '0';
                }
            }
        }

        $all_num = $this->DataControl->selectClear("SELECT c.client_id,c.client_gmcdistributionstatus,
                (select st.schoolenter_id FROM crm_client_schoolenter as st WHERE st.client_id = c.client_id and st.company_id = c.company_id and st.is_enterstatus = '1' and st.is_gmctocrmschool = '1' order by st.schoolenter_id desc limit 0,1 ) as schoolenter_id 
                FROM crm_client_conversionlog g ,crm_client c,crm_client_principal p,crm_marketer m,crm_code_channel l  
                WHERE {$datawhere} GROUP BY c.client_id HAVING {$having}  ");
        $allnums = is_array($all_num) ? count($all_num) : 0;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['all_num'] = $allnums;
        $result['list'] = $clientList;

        $this->error = 0;
        if ($clientList) {
            $this->errortip = "数据获取成功";
        } else {
            $this->errortip = "暂时没有数据";
        }
        return $result;

    }

    //批量添加至待拨打
    function addClientDialRecordAction($paramArray)
    {
        $client_list = json_decode(stripslashes($paramArray['client_list']), true);
        if ($client_list) {
            $staffer = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id', "staffer_id = '{$paramArray['staffer_id']}'");
            if ($staffer) {
                $marketer_id = $staffer['marketer_id'];
            } else {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $marketer_id = $getaddmarkertOne['marketer_id'];
            }
            foreach ($client_list as $value) {
                if (!$this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$marketer_id}' and client_id = '{$value['client_id']}' and FROM_UNIXTIME(dialrecord_createtime,'%Y-%m-%d') = CURDATE() and dialrecord_type = 0")) {
                    $data = array();
                    $data['company_id'] = $paramArray['company_id'];
                    $data['marketer_id'] = $marketer_id;
                    $data['client_id'] = $value['client_id'];
                    $client = $this->DataControl->getFieldOne("crm_client", "client_mobile", "client_id = '{$value['client_id']}'");
                    $data['dialrecord_mobile'] = $client['client_mobile'];
                    $data['dialrecord_createtime'] = time();
                    $this->DataControl->insertData("crm_client_dialrecord", $data);
                }
            }

            $this->error = '0';
            $this->errortip = "添加待拨打成功";
            return true;
        } else {
            $this->error = '1';
            $this->errortip = '请选择客户';
            return false;
        }
    }

    /**
     * 获取第三方跟进记录  ---------   不适用了改版了  20230811
     * author: ling
     * 对应接口文档 0001
     */
    function getThreeTrackApi($paramArray)
    {
        $sql = "select ot.outthreetrack_id,ot.threetrack_fileurl,ot.threetrack_createtime,ot.client_id,'salesforce' as track_from,
              ot.url_ischecknull,ot.audition_id,ot.invite_id,'' as login_marketer_img,'' as login_marketer_name,'' as login_marketer_sex,
              (select sl.school_shortname from  smc_school as sl  where sl.school_id=ot.school_id limit 0,1  ) as school_cnname
              from crm_client_outthreetrack as ot,crm_client as t where ot.client_id = t.client_id and ot.client_id = '{$paramArray['client_id']}' and threetrack_fileurl <> '' and  url_ischecknull  <> -1
              union 
              select 0 as outthreetrack_id,oc.outcall_playurl as threetrack_fileurl,oc.outcall_createtime as threetrack_createtime,t.client_id,'outcall' as track_from,
              1 as url_ischecknull,0 as audition_id,0 as invite_id,'' as login_marketer_img,'' as login_marketer_name,'' as login_marketer_sex,'' as school_cnname
              from crm_client as t,gmc_outcall as oc where (t.client_mobile = oc.outcall_called or t.client_mobile = oc.outcall_caller)  and t.client_id='{$paramArray['client_id']}'
              and outcall_duration > 0
              order by  threetrack_createtime DESC
              ";
        $dataList = $this->DataControl->selectClear($sql);

        if (!$dataList) {
            return array();
        } else {
            $dataArray = array();
            foreach ($dataList as $dataOne) {
                if (strlen($dataOne['threetrack_createtime']) < 10) {
                    $dataOne['threetrack_createtime'] = "--";
                } else {
                    $dataOne['threetrack_createtime'] = date("Y-m-d H:i:s", $dataOne['threetrack_createtime']);
                }
                $dataOne['client_tag'] = '外拨记录';
                $dataOne['isPlay'] = false;
                $dataOne['currentPlayTime'] = 0;
                $dataOne['allPlayTime'] = 0;

                if ($dataOne['audition_id']) {
                    $track = $this->DataControl->selectOne("SELECT t.track_followmode,t.track_note,t.track_visitingtime,
(SELECT o.object_name FROM crm_code_object as o WHERE o.object_code = t.object_code) as object_name
FROM crm_client_audition as a
LEFT JOIN crm_client_track as t ON t.track_id = a.track_id
WHERE a.audition_id = '{$dataOne['audition_id']}'");
                    if ($track['track_followmode'] == 0) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("普通回访");
                    } elseif ($track['track_followmode'] == 1) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("柜询");
                    } elseif ($track['track_followmode'] == 2) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("试听");
                    } elseif ($track['track_followmode'] == 3) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("转正");
                    } elseif ($track['track_followmode'] == 4) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("更新本校无意向名单");
                    } elseif ($track['track_followmode'] == 5) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("转校");
                    } elseif ($track['track_followmode'] == 6) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("毛名单转有效");
                    } elseif ($track['track_followmode'] == 7) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("有效转毛名单");
                    } elseif ($track['track_followmode'] == -1) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("无意向跟进");
                    } elseif ($track['track_followmode'] == -2) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("主管确认流失");
                    } elseif ($track['track_followmode'] == -3) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("无效化名单");
                    }
                    $dataOne['object_name'] = $track['object_name'];
                    $dataOne['track_note'] = $track['track_note'];
                    $dataOne['track_visitingtime'] = $track['track_visitingtime'];
                } else {
                    $dataOne['track_followmode_name'] = '';
                    $dataOne['object_name'] = '';
                    $dataOne['track_note'] = '';
                    $dataOne['track_visitingtime'] = '';
                }
                if ($dataOne['invite_id']) {
                    $track = $this->DataControl->selectOne("SELECT t.track_followmode,t.track_note,t.track_visitingtime,
(SELECT o.object_name FROM crm_code_object as o WHERE o.object_code = t.object_code) as object_name
FROM crm_client_invite as i
LEFT JOIN crm_client_track as t ON t.track_id = i.track_id
WHERE i.invite_id = '{$dataOne['invite_id']}'");
                    if ($track['track_followmode'] == 0) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("普通回访");
                    } elseif ($track['track_followmode'] == 1) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("柜询");
                    } elseif ($track['track_followmode'] == 2) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("试听");
                    } elseif ($track['track_followmode'] == 3) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("转正");
                    } elseif ($track['track_followmode'] == 4) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("更新本校无意向名单");
                    } elseif ($track['track_followmode'] == 5) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("转校");
                    } elseif ($track['track_followmode'] == 6) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("毛名单转有效");
                    } elseif ($track['track_followmode'] == 7) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("有效转毛名单");
                    } elseif ($track['track_followmode'] == -1) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("无意向跟进");
                    } elseif ($track['track_followmode'] == -2) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("主管确认流失");
                    } elseif ($track['track_followmode'] == -3) {
                        $dataOne['track_followmode_name'] = $this->LgStringSwitch("无效化名单");
                    }
                    $dataOne['object_name'] = $track['object_name'];
                    $dataOne['track_note'] = $track['track_note'];
                    $dataOne['track_visitingtime'] = $track['track_visitingtime'];
                } else {
                    $dataOne['track_followmode_name'] = '';
                    $dataOne['object_name'] = '';
                    $dataOne['track_note'] = '';
                    $dataOne['track_visitingtime'] = '';
                }
                if ($dataOne['url_ischecknull'] == 0) {
                    if (file_urlexists($dataOne['threetrack_fileurl'])) {
                        $data = array();
                        $data['url_ischecknull'] = 1;
                        $this->DataControl->updateData("crm_client_outthreetrack", "outthreetrack_id='{$dataOne['outthreetrack_id']}'", $data);
                        $dataArray[] = $dataOne;
                    } else {
                        $data = array();
                        $data['url_ischecknull'] = -1;
                        $this->DataControl->updateData("crm_client_outthreetrack", "outthreetrack_id='{$dataOne['outthreetrack_id']}'", $data);
                    }
                } else {
                    $dataArray[] = $dataOne;
                }
            }
        }
        return $dataArray;
    }

    //获取待拨打/已通话列表
    function getDialRecord($paramArray)
    {
        $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
        $paramArray['marketer_id'] = $markOne['marketer_id'];
        if (!$markOne) {
            $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $paramArray['marketer_id'] = $getaddmarkertOne['marketer_id'];
        }

        $datawhere = "d.company_id = '{$paramArray['company_id']}' AND d.marketer_id = '{$paramArray['marketer_id']}' AND FROM_UNIXTIME(d.dialrecord_createtime,'%Y-%m-%d') = CURDATE()";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%') ";
        }

        $sql = "SELECT
                    c.client_id,c.client_cnname,c.client_enname,c.client_img,c.client_sex,
                    (SELECT st.track_createtime FROM crm_client_track as st WHERE st.client_id = c.client_id AND st.track_isgmcactive = 1 ORDER BY track_createtime DESC limit 0,1) as track_createtime
                FROM
                    crm_client_dialrecord as d
                LEFT JOIN
                    crm_client as c ON c.client_id = d.client_id
                WHERE
                    {$datawhere} AND c.client_tracestatus >= 0 AND d.dialrecord_type = '{$paramArray['dialrecord_type']}'
                ORDER BY
                    d.dialrecord_createtime DESC";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$val) {
                if ($val['client_enname']) {
                    $val['client_cnname'] = $val['client_cnname'] . '/' . $val['client_enname'];
                }
                if ($val['track_createtime']) {
                    $val['track_createtime'] = date("Y-m-d H:i", $val['track_createtime']);
                } else {
                    $val['track_createtime'] = '--';
                }
            }
        } else {
            $dataList = array();
        }

        if (isset($paramArray['dialrecord_type']) && $paramArray['dialrecord_type'] !== '') {
            $dialrecord_type = 1 - $paramArray['dialrecord_type'];
        }
        $dialrecord = $this->DataControl->selectOne("SELECT COUNT(d.client_id) as num FROM crm_client_dialrecord as d LEFT JOIN crm_client as c ON c.client_id = d.client_id WHERE {$datawhere} AND c.client_tracestatus >= 0 AND d.dialrecord_type = '{$dialrecord_type}'");

        //提醒拨打
        $nowday = date("Y-m-d", time());
        $sql = "SELECT r.remind_id,c.client_id,c.client_cnname,c.client_enname,c.client_sex,c.client_mobile,c.client_img,
                (select t.track_createtime from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as lastTrackTime 
                FROM gmc_client_remind as r 
                LEFT JOIN crm_client as c ON r.client_id = c.client_id 
                WHERE r.marketer_id = '{$markOne['marketer_id']}' and r.remind_time = '{$nowday}'  AND r.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4 
                GROUP BY c.client_id 
                order by r.remind_time DESC ";
        $reminddataList = $this->DataControl->selectClear($sql);
        if (is_array($reminddataList)) {
            foreach ($reminddataList as &$dataVar) {
                if ($dataVar['lastTrackTime'] != '' && $dataVar['lastTrackTime'] != '0') {
                    $dataVar['lastTrackTime'] = date('Y-m-d', $dataVar['lastTrackTime']);
                } else {
                    $dataVar['lastTrackTime'] = $this->LgStringSwitch('无记录');
                }
            }
        }

        $data = array();
        $data['list'] = $dataList;
        $data['allnum'] = $dataList ? count($dataList) : 0;
        $data['allnums'] = $dialrecord['num'];
        $data['remindallnum'] = $reminddataList ? count($reminddataList) : 0;
        $data["remindlist"] = is_array($reminddataList) ? $reminddataList : array();

        return $data;
    }

    //更新拨打状态
    function DialRecord($paramArray)
    {
        $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
        $paramArray['marketer_id'] = $markOne['marketer_id'];
        if (!$markOne) {
            $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
            $paramArray['marketer_id'] = $getaddmarkertOne['marketer_id'];
        }

        $dialrecord = $this->DataControl->getFieldOne("crm_client_dialrecord", "dialrecord_id", "company_id = '{$paramArray['company_id']}' and marketer_id = '{$paramArray['marketer_id']}' and client_id = '{$paramArray['client_id']}' and dialrecord_mobile = '{$paramArray['dialrecord_mobile']}' and FROM_UNIXTIME(dialrecord_createtime,'%Y-%m-%d') = CURDATE()", "order by dialrecord_createtime desc");
        if ($dialrecord) {
            $dataid = $this->DataControl->updateData("crm_client_dialrecord", "dialrecord_id = '{$paramArray['dialrecord_id']}'", array("dialrecord_type" => 1));

            //补充拨打记录 （针对  慧捷、合力、仕决） 不含 容联七陌
            if($paramArray['marketer_outtype'] != '9') {
                $record = array();
                $record['school_id'] = -1;
                $record['client_id'] = $paramArray['client_id'];
                $record['marketer_id'] = $paramArray['marketer_id'];
                $record['callrecord_type'] = $paramArray['marketer_outtype'];
                $record['callrecord_callid'] = $paramArray['callId'] ? $paramArray['callId'] : '';
                $record['callrecord_custompara'] = "TMK集团-软电话";
                $record['callrecord_createtime'] = time();
                $callrecordid = $this->DataControl->insertData("crm_client_callrecord", $record);
            }
        } else {
            $data = array();
            $data['company_id'] = $paramArray['company_id'];
            $data['school_id'] = $paramArray['school_id'];
            $data['marketer_id'] = $paramArray['marketer_id'];
            $data['client_id'] = $paramArray['client_id'];
            $data['dialrecord_mobile'] = $paramArray['dialrecord_mobile'];
            $data['dialrecord_createtime'] = time();
            $dataid = $this->DataControl->insertData("crm_client_dialrecord", $data);

            //补充拨打记录 （针对  慧捷、合力、仕决） 不含 容联七陌
            if($paramArray['marketer_outtype'] != '9') {
                $record = array();
                $record['school_id'] = -1;
                $record['client_id'] = $paramArray['client_id'];
                $record['marketer_id'] = $paramArray['marketer_id'];
                $record['callrecord_type'] = $paramArray['marketer_outtype'];
                $record['callrecord_callid'] = $paramArray['callId'] ? $paramArray['callId'] : '';
                $record['callrecord_custompara'] = "TMK集团-软电话";
                $record['callrecord_createtime'] = time();
                $callrecordid = $this->DataControl->insertData("crm_client_callrecord", $record);
            }
        }

        return $dataid;
    }

    //首页 -- 跟踪提醒
    function remindApi($paramArray)
    {
        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id = '{$paramArray['staffer_id']}'");
        $datawhere = " 1 and r.marketer_id = '{$marketerOne['marketer_id']}' ";

        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['remind_starttime']) && $paramArray['remind_starttime'] != '') {
            $datawhere .= " and r.remind_time >= '{$paramArray['remind_starttime']}'";
        }
        if (isset($paramArray['remind_endtime']) && $paramArray['remind_endtime'] != '') {
            $datawhere .= " and r.remind_time <= '{$paramArray['remind_endtime']}'";
        } else {
            $datawhere .= " and r.remind_time >= CURDATE()";
        }
        if (isset($paramArray['remind_isread']) && $paramArray['remind_isread'] != '') {
            $datawhere .= " and r.remind_isread = '{$paramArray['remind_isread']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            if ($paramArray['num'] == 3) {
                $day = date("Y-m-d", strtotime("+ 2 days"));
                $datawhere .= " and r.remind_time <= '{$day}'";
            }
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  r.remind_id 
                FROM gmc_client_remind as r 
                LEFT JOIN crm_client as c ON r.client_id = c.client_id 
                WHERE {$datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4 
                GROUP BY c.client_id 
                ";
            // AND r.school_id = '{$paramArray['school_id']}'
            $count = $this->DataControl->selectClear($sql);

            if (is_array($count)) {
                $count = count($count) + 0;
            } else {
                $count = 0;
            }
        } else {
            $count = '';
        }

        //线索 统计
        $sql = "SELECT r.remind_id,r.remind_time,r.remind_remark,c.client_id,c.client_cnname,c.client_enname,c.client_sex,c.client_birthday,c.client_mobile,c.client_tracestatus,c.client_isnewtip,c.client_intention_level,c.client_img,r.remind_isread,'crm' as remind_type,
                (select count(t.track_id) from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id and t.track_validinc = '1' ) as trackNum,
                (select t.track_createtime from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as lastTrackTime, 
                (select count(t.track_id) from crm_client_track as t where t.client_id = r.client_id and t.marketer_id =r.marketer_id and FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= r.remind_time ) as todayTrackNum,'' as stuenrolledtype 
                FROM gmc_client_remind as r 
                LEFT JOIN crm_client as c ON r.client_id = c.client_id 
                WHERE {$datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4 
                GROUP BY c.client_id 
                order by r.remind_time ASC,r.remind_id ASC
                limit {$pagestart},{$num}";
        // AND r.school_id = '{$paramArray['school_id']}'
        //and r.remind_time >= current_date
        $dataList = $this->DataControl->selectClear($sql);
        //数据
        if (is_array($dataList)) {
            foreach ($dataList as &$dataVar) {
                if ($dataVar['todayTrackNum'] > 0) {
                    $dataVar['isTrack'] = '1';
                    $dataVar['isTracknum'] = $this->LgStringSwitch('已跟踪');
                } else {
                    $dataVar['isTrack'] = '0';
                    $dataVar['isTracknum'] = $this->LgStringSwitch('未跟踪');
                }

                if ($dataVar['remind_type'] == 'crm') {
                    $dataVar['stuisloss'] = '';
                } elseif ($dataVar['remind_type'] == 'stu') {
                    if ($dataVar['stuenrolledtype'] == '-1') {
                        $dataVar['stuisloss'] = '1';
                    } else {
                        $dataVar['stuisloss'] = '0';
                    }
                }

                if ($dataVar['client_tracestatus'] == '0') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('待跟踪');
                } elseif ($dataVar['client_tracestatus'] == '1') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('持续跟踪');
                } elseif ($dataVar['client_tracestatus'] == '2') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('已柜询');
                } elseif ($dataVar['client_tracestatus'] == '3') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('已试听');
                } elseif ($dataVar['client_tracestatus'] == '4') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('已转正');
                } elseif ($dataVar['client_tracestatus'] == '-1') {
                    $dataVar['client_tracestatus'] = $this->LgStringSwitch('无意向');
                }
                if ($dataVar['lastTrackTime'] != '' && $dataVar['lastTrackTime'] != '0') {
                    $dataVar['lastTrackTime'] = date('Y-m-d', $dataVar['lastTrackTime']);
                } else {
                    $dataVar['lastTrackTime'] = $this->LgStringSwitch('无记录');
                }
                $dataVar['client_age'] = birthdaytoage($dataVar['client_birthday']);
            }
        }
        $result = array();
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }


//    //首页 -- 跟踪提醒
//    function getDialRemindApi($paramArray)
//    {
//        $marketerOne = $this->DataControl->getFieldOne("crm_marketer", "marketer_id", "staffer_id = '{$paramArray['staffer_id']}'");
//        $nowday = date("Y-m-d",time());
//        $datawhere = " 1 and r.marketer_id = '{$marketerOne['marketer_id']}' and r.remind_time = '{$nowday}'";
//
//        //关键词
//        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
//            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%')";
//        }
//
//        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
//            $page = $paramArray['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
//            $num = $paramArray['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;
//
//        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
//            $sql = "SELECT  r.remind_id
//                FROM gmc_client_remind as r
//                LEFT JOIN crm_client as c ON r.client_id = c.client_id
//                WHERE {$datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4
//                GROUP BY c.client_id
//                ";
//            $count = $this->DataControl->selectClear($sql);
//
//            if (is_array($count)) {
//                $count = count($count) + 0;
//            } else {
//                $count = 0;
//            }
//        } else {
//            $count = '';
//        }
//
//        //线索 统计
//        $sql = "SELECT r.remind_id,c.client_id,c.client_cnname,c.client_enname,c.client_sex,c.client_mobile,c.client_img,
//                (select t.track_createtime from crm_client_track as t where t.client_id = r.client_id and t.marketer_id = r.marketer_id ORDER BY t.track_id DESC limit 0,1) as lastTrackTime
//                FROM gmc_client_remind as r
//                LEFT JOIN crm_client as c ON r.client_id = c.client_id
//                WHERE {$datawhere}  AND r.company_id = '{$paramArray['company_id']}' AND c.client_tracestatus <> -2 AND c.client_tracestatus <> -1 AND c.client_tracestatus <> 4
//                GROUP BY c.client_id
//                order by r.remind_time DESC
//                limit {$pagestart},{$num}";
//        // AND r.school_id = '{$paramArray['school_id']}'
//        //and r.remind_time >= current_date
//        $dataList = $this->DataControl->selectClear($sql);
//        //数据
//        if (is_array($dataList)) {
//            foreach ($dataList as &$dataVar) {
//                if ($dataVar['lastTrackTime'] != '' && $dataVar['lastTrackTime'] != '0') {
//                    $dataVar['lastTrackTime'] = date('Y-m-d', $dataVar['lastTrackTime']);
//                } else {
//                    $dataVar['lastTrackTime'] = $this->LgStringSwitch('无记录');
//                }
//            }
//        }
//        $result = array();
//        $result["datalist"] = $dataList;
//        $result["count"] = $count;
//        return $result;
//    }


}
