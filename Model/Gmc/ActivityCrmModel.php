<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

use Model\Crm\SellgoalModel;

class ActivityCrmModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //招生活动管理
    function getShareposterList($paramArray)
    {
        $datawhere = " 1 and a.activity_type = '1'  and  a.activity_pattern = '2' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (a.activity_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '') {
            $paramArray['activity_starttime'] = $paramArray['activity_starttime'] . " 00:00:00";
            $datawhere .= " and a.activity_starttime >= '{$paramArray['activity_starttime']}'";
        }
        if (isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $paramArray['activity_endtime'] = $paramArray['activity_endtime'] . " 23:59:59";
            $datawhere .= " and a.activity_endtime <= '{$paramArray['activity_endtime']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_marketer_id']) && $paramArray['found_marketer_id'] != '') {
            $datawhere .= " and a.staffer_id = '{$paramArray['found_marketer_id']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '') {
            $datawhere .= " and a.frommedia_name = '{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '') {
            $datawhere .= " and a.channel_id = '{$paramArray['channel_id']}'";
        }
        //学校 筛选
        if (isset($paramArray['company_id']) && $paramArray['company_id'] != '') {
            $datawhere .= " and a.company_id = '{$paramArray['company_id']}'";
        }
        //学校 筛选
//        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
//            $datawhere .= " and a.school_id = '{$paramArray['school_id']}'";
//        }
        //学校 筛选
//        if(isset($paramArray['activity_pattern']) && $paramArray['activity_pattern'] != ''){
//            $datawhere .= " and a.activity_pattern = '{$paramArray['activity_pattern']}'";
//        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        if ($paramArray['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
        }


        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            if ($paramArray['dataequity'] == '1') {
                $sql = "SELECT  COUNT(a.activity_id)  as datanum FROM crm_sell_activity as a LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id WHERE {$datawhere}";
                $count = $this->DataControl->selectOne($sql);
                $count = $count['datanum'] + 0;
            } else {
                $sql = "SELECT  
	(
SELECT
	group_concat( cyo.organize_id ) 
FROM
	crm_channel_organize AS co
	LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
WHERE
	co.channel_id = a.channel_id 
	) AS organize_id
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE {$datawhere}
                having organize_id like '%{$organize_id['organize_id']}%'
                ";
                $count = $this->DataControl->selectClear($sql);
                if ($count) {
                    $count = count($count);
                } else {
                    $count = 0;
                }
            }


        } else {
            $count = '';
        }


        $sqlfields = " a.activity_id,a.activity_istemp,a.activity_tempurl,a.activitytemp_id,a.activity_name,a.staffer_id,a.activity_sharedesc,a.activity_shareimg,s.staffer_cnname,a.activity_starttime,a.activity_endtime,a.company_id ,a.frommedia_name,a.activity_type  ";

        if ($paramArray['dataequity'] == '1') {
            $sql = "SELECT  {$sqlfields},
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=a.channel_id limit 0,1) as channel_name
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE {$datawhere}
                ORDER BY activity_id DESC";
//            limit {$pagestart},{$num}
        } else {
            $sql = "SELECT  {$sqlfields},
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=a.channel_id limit 0,1) as channel_name,
	(
SELECT
	group_concat( cyo.organize_id ) 
FROM
	crm_channel_organize AS co
	LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
WHERE
	co.channel_id = a.channel_id 
	) AS organize_id
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE {$datawhere}
                having organize_id like '%{$organize_id['organize_id']}%'
                ORDER BY activity_id DESC";
//            limit {$pagestart},{$num}
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['activity_id'] = $val['activity_id'];
                $val['activity_name'] = $val['activity_name'] == null ? '' : $val['activity_name'];
                $val['activity_starttime'] = $val['activity_starttime'] . " 到 " . $val['activity_endtime'];
                $val['frommedia_name'] = $val['frommedia_name'];
                $val['channel_name'] = $val['channel_name'];

                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                             WHERE c.activity_id = '{$val['activity_id']}' and c.company_id = '{$val['company_id']}'");

                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$val['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'  ");
                $actnum = $actnum['num'];

                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $val['clientnum'] = $clientcount;
                //活动来源的意向客户
                $val['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $val['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $val['percentconversion'] = sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100 . '%';
                } else {
                    $val['percentconversion'] = 0;
                }
                //二维码
                $activity_Url = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$val['activity_id']}&company_id={$paramArray['company_id']}&language_type={$paramArray['language_type']}";
                $val['shareqrcode'] = "https://gmcapi.kedingdang.com/Crm/goalActivityshowimg?imgurl=" . base64_encode($activity_Url);//个人二维码
                $val['shareqrcodeurl'] = $activity_Url;//个人二维码链接

                //发布人
                $val['staffer_cnname'] = $val['staffer_cnname'] == null ? '' : $val['staffer_cnname'];
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['activity_name'] = $dateexcelvar['activity_name'];//活动名称
                    $datearray['activity_starttime'] = $dateexcelvar['activity_starttime'];//活动时间
                    $datearray['clientnum'] = $dateexcelvar['clientnum'];//有效名单数
                    $datearray['actnum'] = $dateexcelvar['actnum'];//活动意向客户
                    $datearray['officialnum'] = $dateexcelvar['officialnum'];//转正人数
                    $datearray['percentconversion'] = $dateexcelvar['percentconversion'];//转化率
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];//发布人
                    $datearray['activity_type'] = $dateexcelvar['activity_type'] == 1 ? '集团' : '学校';//活动来源

                    $datearray['shareqrcode'] = $dateexcelvar['shareqrcode'];//二维码
                    $datearray['shareqrcodeurl'] = $dateexcelvar['shareqrcodeurl'];//活动地址

                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];//渠道类型
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道明细
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('活动名称', '活动时间', '有效名单数', '活动意向客户', '转正人数', '转化率', '发布人', '活动来源', '二维码', '活动地址', '渠道类型', '渠道明细'));
            $excelfileds = array('activity_name', 'activity_starttime', 'clientnum', 'actnum', 'officialnum', 'percentconversion', 'staffer_cnname', 'activity_type', 'shareqrcode', 'shareqrcodeurl', 'frommedia_name', 'channel_name');

            $fielname = $this->LgStringSwitch("海报活动管理列表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
        }

        if (is_array($dataList)) {
            $list = array();
            foreach ($dataList as $datakey => $dataVar) {
                $list[$datakey]['activity_id'] = $dataVar['activity_id'];
                $list[$datakey]['activity_name'] = $dataVar['activity_name'] == null ? '' : $dataVar['activity_name'];
                $list[$datakey]['activity_starttime'] = $dataVar['activity_starttime'] . " 到 " . $dataVar['activity_endtime'];
                $list[$datakey]['frommedia_name'] = $dataVar['frommedia_name'];
                $list[$datakey]['channel_name'] = $dataVar['channel_name'];

                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' and c.company_id = '{$dataVar['company_id']}'");

                //扫描个人二维码变成意向客户
                $actnum = $this->DataControl->selectOne("SELECT count(c.client_id) as num from crm_client AS c 
                                                             LEFT join crm_client_schoolenter as s ON c.client_id = s.client_id
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' 
                                                             and s.company_id = '{$paramArray['company_id']}' 
                                                             and c.client_distributionstatus = '1'
                                                             and c.client_tracestatus <> '-1'  ");
                $actnum = $actnum['num'];

                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $list[$datakey]['clientnum'] = $clientcount;
                //活动来源的意向客户
                $list[$datakey]['actnum'] = $actnum;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(DISTINCT c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $list[$datakey]['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $list[$datakey]['percentconversion'] = sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100 . '%';
                } else {
                    $list[$datakey]['percentconversion'] = 0;
                }
                //二维码
                $activity_Url = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$dataVar['activity_id']}&company_id={$paramArray['company_id']}&language_type={$paramArray['language_type']}";
                $list[$datakey]['shareqrcode'] = "https://gmcapi.kedingdang.com/Crm/goalActivityshowimg?imgurl=" . base64_encode($activity_Url);//个人二维码
                $list[$datakey]['shareqrcodeurl'] = $activity_Url;//个人二维码链接

                //发布人
                $list[$datakey]['staffer_cnname'] = $dataVar['staffer_cnname'] == null ? '' : $dataVar['staffer_cnname'];

            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //招生活动管理
    function sellActivity($paramArray)
    {
        $datawhere = "a.activity_type = '1'  and ( a.activity_pattern = '0' or a.activity_pattern = '1')  ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (a.activity_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
        if (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '') {
            $paramArray['activity_starttime'] = $paramArray['activity_starttime'] . " 00:00:00";
            $datawhere .= " and a.activity_starttime >= '{$paramArray['activity_starttime']}'";
        }
        if (isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $paramArray['activity_endtime'] = $paramArray['activity_endtime'] . " 23:59:59";
            $datawhere .= " and a.activity_endtime <= '{$paramArray['activity_endtime']}'";
        }
        //创建人 筛选
        if (isset($paramArray['found_staffer_id']) && $paramArray['found_staffer_id'] != '') {
            $datawhere .= " and a.staffer_id = '{$paramArray['found_staffer_id']}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '') {
            $datawhere .= " and a.frommedia_name = '{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '') {
            $datawhere .= " and a.channel_id = '{$paramArray['channel_id']}'";
        }
        //学校 筛选
        if (isset($paramArray['company_id']) && $paramArray['company_id'] != '') {
            $datawhere .= " and a.company_id = '{$paramArray['company_id']}'";
        }
        //学校 筛选
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and a.school_id = '{$paramArray['school_id']}'";
        }
        //学校 筛选
        if (isset($paramArray['activity_pattern']) && $paramArray['activity_pattern'] != '') {
            $datawhere .= " and a.activity_pattern = '{$paramArray['activity_pattern']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
        }


        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            if ($paramArray['dataequity'] == '1') {
                $sql = "SELECT  COUNT(a.activity_id)  as datanum FROM crm_sell_activity as a LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id WHERE {$datawhere}";
                $count = $this->DataControl->selectOne($sql);
                $count = $count['datanum'] + 0;
            } else {
                $sql = "SELECT  
	(
SELECT
	group_concat( cyo.organize_id ) 
FROM
	crm_channel_organize AS co
	LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
WHERE
	co.channel_id = a.channel_id 
	) AS organize_id
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE {$datawhere}
                having organize_id like '%{$organize_id['organize_id']}%'
                ";
                $count = $this->DataControl->selectClear($sql);
                if ($count) {
                    $count = count($count);
                } else {
                    $count = 0;
                }
            }


        } else {
            $count = '';
        }


        $sqlfields = " a.activity_id,a.activity_istemp,a.activity_tempurl,a.activitytemp_id,a.activity_name,a.staffer_id,a.activity_sharedesc,a.activity_shareimg,s.staffer_cnname,a.activity_starttime,a.activity_endtime,a.company_id ,a.frommedia_name,a.package_branch";

        if ($paramArray['dataequity'] == '1') {
            $sql = "SELECT  {$sqlfields},
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=a.channel_id limit 0,1) as channel_name
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE {$datawhere}
                ORDER BY activity_id DESC 
                limit {$pagestart},{$num}";
        } else {
            $sql = "SELECT  {$sqlfields},
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=a.channel_id limit 0,1) as channel_name,
                ( SELECT group_concat( cyo.organize_id ) FROM crm_channel_organize AS co
                LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id WHERE co.channel_id = a.channel_id ) AS organize_id
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE {$datawhere}
                having organize_id like '%{$organize_id['organize_id']}%'
                ORDER BY activity_id DESC 
                limit {$pagestart},{$num}";
        }

        $dataList = $this->DataControl->selectClear($sql);


        $list = array();
        if (is_array($dataList)) {
            foreach ($dataList as $datakey => $dataVar) {
                $list[$datakey]['activity_id'] = $dataVar['activity_id'];
                $list[$datakey]['activity_name'] = $dataVar['activity_name'] == null ? '' : $dataVar['activity_name'];
                $list[$datakey]['activity_starttime'] = $dataVar['activity_starttime'] . " 到 " . $dataVar['activity_endtime'];
                $list[$datakey]['frommedia_name'] = $dataVar['frommedia_name'];
                $list[$datakey]['channel_name'] = $dataVar['channel_name'];

                //名单线索数
                $clientnum = $this->DataControl->selectClear("SELECT c.client_id from crm_client AS c 
                                                             WHERE c.activity_id = '{$dataVar['activity_id']}' and c.company_id = '{$dataVar['company_id']}'");
                if (is_array($clientnum)) {
                    $clientstr = '';
                    foreach ($clientnum as $clientnumVar) {
                        $clientstr .= $clientnumVar['client_id'] . ',';
                    }
                    $clientstr = substr($clientstr, 0, -1);

                    $clientcount = count($clientnum);
                } else {
                    $clientstr = '0';
                    $clientcount = '0';
                }
//                $clientcount = count($clientnum);
                $list[$datakey]['clientnum'] = $clientcount;
                //转正人数
                $officialnum = $this->DataControl->selectOne("SELECT count(DISTINCT c.client_id) as officialnum  from crm_client_conversionlog AS c 
                                                             WHERE c.client_id IN ({$clientstr}) ");
                $list[$datakey]['officialnum'] = $officialnum['officialnum'];
                //转化率
                if ($clientcount != '' && $clientcount != '0') {
                    $list[$datakey]['percentconversion'] = sprintf("%.2f", $officialnum['officialnum'] / $clientcount) * 100 . '%';
                } else {
                    $list[$datakey]['percentconversion'] = 0;
                }
                //二维码
//                $list[$datakey]['qrcode'] = "http://gmcapi.kedingdang.com/Crm/goalActivityshowimg?imgurl=http://ptc.kidcastle.com.cn/jactivity?id={$dataVar['activity_id']}&type=1";
                if ($dataVar['activity_istemp'] == '0' && $dataVar['activity_tempurl'] != '' && $dataVar['activity_tempurl'] != '0') {//自定义
                    $activity_Url = $dataVar['activity_tempurl'] . "?activity_id={$dataVar['activity_id']}&language_type=zh";//个人链接地址
                } else {//模板
                    $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url", "activitytemp_id='{$dataVar['activitytemp_id']}'");
                    if ($_SERVER['SERVER_NAME'] !== 'crmapi.kedingdang.com' && $_SERVER['SERVER_NAME'] !== 'gmcapi.kedingdang.com') {
                        $activitytempOne['activitytemp_url'] = str_replace("https://crmshare.kedingdang.com", "http://crmshare.kcclassin.com", $activitytempOne['activitytemp_url']);
                    }
                    $activity_Url = $activitytempOne['activitytemp_url'] . "?activity_id={$dataVar['activity_id']}&language_type=zh";//个人链接地址
                }
                $list[$datakey]['qrcode'] = "https://gmcapi.kedingdang.com/Crm/goalActivityshowimg?imgurl=" . base64_encode($activity_Url);//个人二维码
                $list[$datakey]['qrcodeurl'] = $activity_Url;//个人二维码链接

                $QRcodeUrl = "activity_id={$dataVar['activity_id']}&iswidth=100%";//小程序二维码链接
                $list[$datakey]['QRcodeUrl'] = "https://crmapi.kedingdang.com/Api/getQRcode?" . $QRcodeUrl;
                //发布人
                $list[$datakey]['staffer_cnname'] = $dataVar['staffer_cnname'] == null ? '' : $dataVar['staffer_cnname'];

                $promotion = $this->DataControl->selectOne("SELECT COUNT(ap.promotion_id) as num FROM crm_sell_activity_promotion as ap 
                                                                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = ap.promotion_id
                                                                LEFT JOIN crm_ground_promotion_open as op ON op.company_id = p.company_id AND op.school_id = p.school_id AND op.promotion_id = p.promotion_id
                                                                WHERE ap.activity_id = '{$dataVar['activity_id']}' AND op.open_status = 1");
                if($promotion['num']){
                    $list[$datakey]['promotion_qrcode'] = true;
                }else{
                    $list[$datakey]['promotion_qrcode'] = false;
                }
            }
        }
        $result = array();
        $result["datalist"] = $list;
        $result["count"] = $count;
        return $result;
    }

    //招生目标 -- >> 复制活动的详细情况
    function copyActivityOneApi($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_sell_activity", " * ", " activity_id = '{$paramArray['activity_id']}' ");
        $data = array();
        $data['company_id'] = $paramArray['company_id'];// 公司
        $data['staffer_id'] = $paramArray['staffer_id'];// 职工
        $data['activity_type'] = '1';// 活动来源
        $data['activity_pattern'] = $activityOne['activity_pattern'];//招生活动模式
        $data['activity_name'] = $activityOne['activity_name'] . '复制';//名称
        $data['activity_theme'] = $activityOne['activity_theme'];//活动招生标题
        $data['activity_fittype'] = $activityOne['activity_fittype'];//适用学校

        $activityOne['activity_starttime'] = date("Y-m-d H:i:s", strtotime($activityOne['activity_starttime']));
        $activityOne['activity_endtime'] = date("Y-m-d H:i:s", strtotime($activityOne['activity_endtime']));

        $data['activity_starttime'] = $activityOne['activity_starttime'];//开始时间
        $data['activity_endtime'] = $activityOne['activity_endtime'];//结束时间
        $data['activity_istemp'] = $activityOne['activity_istemp'];//是否使用招生模板
        $data['activitytemp_id'] = $activityOne['activitytemp_id'];//招生模板id
        $data['activity_tempurl'] = $activityOne['activity_tempurl'];//自定义招生模板链接
        $data['activity_contacttel'] = $activityOne['activity_contacttel'];//联系方式
        $data['activity_address'] = $activityOne['activity_address'];//活动地址
        $data['activity_img'] = $activityOne['activity_img'];//主图
        $data['activity_demoimg'] = $activityOne['activity_demoimg'];//海报活动 (
        $data['activity_content'] = $activityOne['activity_content'];//活动详情
        $data['activity_rule'] = $activityOne['activity_rule'];//活动规则
        $data['activity_aboutus'] = $activityOne['activity_aboutus'];//关于我们
        $data['activity_customcontent'] = $activityOne['activity_customcontent'];//自定义详情
        $data['activity_sharedesc'] = $activityOne['activity_sharedesc'];//课叮铛分享描述
        $data['activity_shareimg'] = $activityOne['activity_shareimg'];//微信分享小图标
        $data['frommedia_name'] = $activityOne['frommedia_name'];//来源渠道信息
        $data['channel_id'] = $activityOne['channel_id'];//来源渠道信息

        $data['activity_issex'] = $activityOne['activity_issex'];//
        $data['activity_issex_must'] = $activityOne['activity_issex_must'];//
        $data['activity_isbirthday'] = $activityOne['activity_isbirthday'];//
        $data['activity_isbirthday_must'] = $activityOne['activity_isbirthday_must'];//
        $data['activity_isaddress'] = $activityOne['activity_isaddress'];//
        $data['activity_isaddress_must'] = $activityOne['activity_isaddress_must'];//

        $data['package_branch'] = $activityOne['package_branch'];//

        $data['activity_createtime'] = time();
        $activityid = $this->DataControl->insertData('crm_sell_activity', $data);
        $data['activity_id'] = $activityid;
        if ($activityid) {

            $promotion = $this->DataControl->selectClear("SELECT promotion_id FROM crm_sell_activity_promotion WHERE activity_id = '{$paramArray['activity_id']}'");
            if($promotion){
                foreach ($promotion as $value) {
                    $datapromotion = array();
                    $datapromotion['activity_id'] = $activityid;
                    $datapromotion['promotion_id'] = $value['promotion_id'];
                    $this->DataControl->insertData('crm_sell_activity_promotion', $datapromotion);
                }
            }
            return $data;
        } else {
            return false;
        }
    }

    //招生活动 -- >> 某个活动分享的 图片和描述
    function getActivityShareApi($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("crm_sell_activity", "activity_shareimg,activity_sharedesc", " activity_id = '{$paramArray['activity_id']}' ");
        return $activityOne;
    }

    //某个活动的详细情况
    function ActivityOneApi($paramArray)
    {
        $datawhere = " 1 and a.activity_id = '{$paramArray['activity_id']}' and a.company_id = '{$paramArray['company_id']}' ";
        $sqlfields = " a.* ";
        $sql = "SELECT  {$sqlfields}
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as m ON a.staffer_id = m.staffer_id
                WHERE {$datawhere}";
        $dataOne = $this->DataControl->selectOne($sql);
        $dataOne['activity_createtime'] = date('Y-m-d H:i:s', $dataOne['activity_createtime']);

        if ($dataOne['activity_istemp'] == '0' && $dataOne['activity_tempurl'] != '' && $dataOne['activity_tempurl'] != '0') {//自定义
            $dataOne['activity_Url'] = $dataOne['activity_tempurl'] . "?activity_id={$dataOne['activity_id']}";//个人链接地址&activity_sharedesc={$dataOne['activity_sharedesc']}&activity_shareimg={$dataOne['activity_shareimg']}
        } else {//模板
            if ($dataOne['activity_pattern'] == '2') {//海报活动
                $dataOne['activity_Url'] = "https://{$paramArray['company_id']}.scshop.kedingdang.com/crmPoster/poster?activity_id={$paramArray['activity_id']}&company_id={$paramArray['company_id']}&language_type={$paramArray['language_type']}";
            } else {
                $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", "activitytemp_url,activitytemp_styleimg,activitytemp_bannerimg,activitytemp_name", "activitytemp_id='{$dataOne['activitytemp_id']}'");

                if ($_SERVER['SERVER_NAME'] != 'gmcapi.kedingdang.com') {
                    $activitytempOne['activitytemp_url'] = str_replace("https://crmshare.kedingdang.com", "http://crmshare.kcclassin.com", $activitytempOne['activitytemp_url']);
                }

                $dataOne['activity_Url'] = $activitytempOne['activitytemp_url'] . "?activity_id={$dataOne['activity_id']}";//个人链接地址&activity_sharedesc={$dataOne['activity_sharedesc']}&activity_shareimg={$dataOne['activity_shareimg']}
            }
        }
        $dataOne['activitytemp_styleimg'] = $activitytempOne['activitytemp_styleimg'] != '' ? $activitytempOne['activitytemp_styleimg'] : '';
        $dataOne['activitytemp_bannerimg'] = $activitytempOne['activitytemp_bannerimg'] != '' ? $activitytempOne['activitytemp_bannerimg'] : '';
        $dataOne['activitytemp_name'] = $activitytempOne['activitytemp_name'] != '' ? $activitytempOne['activitytemp_name'] : '';
        $dataOne['activity_appUrl'] = "https://gmcapi.kedingdang.com/Crm/goalActivityshowimg?imgurl=" . base64_encode($dataOne['activity_Url']);//个人二维码

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($dataOne['staffer_id'] == $paramArray['staffer_id']) {
            $a = '1';
        } else {
            $a = '0';
        }
        if ($stafferOne['account_class'] == '1' || $a == '1') {
            $dataOne['status'] = '1';
        } else {
            $dataOne['status'] = '0';
        }
        //是否产品有效名单
        if ($this->DataControl->getFieldOne("crm_client", "client_id", "activity_id='{$paramArray['activity_id']}' ")) {
            $dataOne['ishaveclient'] = '1';
        } else {
            $dataOne['ishaveclient'] = '0';
        }
        return $dataOne;
    }

    //添加活动
    function addActivityAction($paramArray)
    {
        $data = array();
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['company_id'] = $paramArray['company_id'];
        $data['activity_type'] = '1';
        $data['channel_id'] = $paramArray['channel_id'];
        $data['activity_pattern'] = $paramArray['activity_pattern'];
        $data['activity_name'] = $paramArray['activity_name'];//名称
        $data['activity_theme'] = $paramArray['activity_theme'];//活动招生标题
        $data['activity_fittype'] = $paramArray['activity_fittype'];//适用学校

        $paramArray['activity_starttime'] = date("Y-m-d H:i:s", strtotime($paramArray['activity_starttime']));
        $paramArray['activity_endtime'] = date("Y-m-d H:i:s", strtotime($paramArray['activity_endtime']));

        $data['activity_starttime'] = $paramArray['activity_starttime'];//开始时间
        $data['activity_endtime'] = $paramArray['activity_endtime'];//结束时间
        $data['activity_istemp'] = $paramArray['activity_istemp'];//是否使用招生模板
        $data['activitytemp_id'] = $paramArray['activitytemp_id'];//招生模板id
        $data['activity_tempurl'] = $paramArray['activity_tempurl'];//自定义招生模板链接
        $data['activity_contacttel'] = $paramArray['activity_contacttel'];//联系方式
        $data['activity_address'] = $paramArray['activity_address'];//活动地址
        $data['activity_img'] = $paramArray['activity_img'];//主图
        $data['activity_content'] = $paramArray['activity_content'];//活动详情
        $data['activity_rule'] = $paramArray['activity_rule'];//活动规则
        $data['activity_aboutus'] = $paramArray['activity_aboutus'];//关于我们
        $data['activity_customcontent'] = $paramArray['activity_customcontent'];//自定义详情
        $data['activity_sharedesc'] = $paramArray['activity_sharedesc'];//课叮铛分享描述
        $data['activity_shareimg'] = $paramArray['activity_shareimg'];//微信分享小图标
        $data['frommedia_name'] = $paramArray['frommedia_name'];//来源渠道信息
        $data['channel_id'] = $paramArray['channel_id'];//来源渠道信息
        $data['activity_ischoicecity'] = $paramArray['activity_ischoicecity'];//是否可以选择学校
        $data['activity_ischoiceschool'] = $paramArray['activity_ischoiceschool'];//是否可以选择学校
        $data['activity_demoimg'] = $paramArray['activity_demoimg'];//海报活动专用  的展示图（非镂空）

        $data['activity_issex'] = $paramArray['activity_issex'];//
        $data['activity_issex_must'] = $paramArray['activity_issex_must'];//
        $data['activity_isbirthday'] = $paramArray['activity_isbirthday'];//
        $data['activity_isbirthday_must'] = $paramArray['activity_isbirthday_must'];//
        $data['activity_isaddress'] = $paramArray['activity_isaddress'];//
        $data['activity_isaddress_must'] = $paramArray['activity_isaddress_must'];//

        $data['package_branch'] = $paramArray['package_branch'];//

        $data['activity_createtime'] = time();
        $actid = $this->DataControl->insertData('crm_sell_activity', $data);
        if ($paramArray['activity_fittype'] == '1') {
            $schoolList = $this->DataControl->selectClear("SELECT s.school_id FROM smc_school AS s 
                WHERE s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0'
                ORDER BY s.school_id DESC ");
            if ($schoolList) {
                foreach ($schoolList as $schoolVar) {
                    $data = array();
                    $data['company_id'] = $paramArray['company_id'];
                    $data['activity_id'] = $actid;
                    $data['school_id'] = $schoolVar['school_id'];
                    $this->DataControl->insertData("crm_sell_activity_school", $data);
                }
            }
        }

        if ($actid) {

            if($paramArray['promotion_json']){
                $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
                $paramArray['marketer_id'] = $markOne['marketer_id'];
                if(!$markOne){
                    $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                    $paramArray['marketer_id'] = $getaddmarkertOne['marketer_id'];
                }

                $Model = new SellgoalModel();
                $Model->addGroundPromotionAction($paramArray, $actid);
            }

            return $actid;
        } else {
            return false;
        }
    }

    //修改招生目标
    function updateActivityAction($paramArray, $account_class = '0')
    {
        $data = array();
        $data['channel_id'] = $paramArray['channel_id'];
        $data['activity_pattern'] = $paramArray['activity_pattern'];
        $data['activity_name'] = $paramArray['activity_name'];//名称
        $data['activity_theme'] = $paramArray['activity_theme'];//活动招生标题
        $data['activity_fittype'] = $paramArray['activity_fittype'];//适用学校

        $activityOne['activity_starttime'] = date("Y-m-d H:i:s", strtotime($paramArray['activity_starttime']));
        $activityOne['activity_endtime'] = date("Y-m-d H:i:s", strtotime($paramArray['activity_endtime']));

        $data['activity_starttime'] = $paramArray['activity_starttime'];//开始时间
        $data['activity_endtime'] = $paramArray['activity_endtime'];//结束时间
        $data['activity_istemp'] = $paramArray['activity_istemp'];//是否使用招生模板
        $data['activitytemp_id'] = $paramArray['activitytemp_id'];//招生模板id
        $data['activity_tempurl'] = $paramArray['activity_tempurl'];//自定义招生模板链接
        $data['activity_contacttel'] = $paramArray['activity_contacttel'];//联系方式
        $data['activity_address'] = $paramArray['activity_address'];//活动地址
        $data['activity_img'] = $paramArray['activity_img'];//主图
        $data['activity_content'] = $paramArray['activity_content'];//活动详情
        $data['activity_rule'] = $paramArray['activity_rule'];//活动规则
        $data['activity_aboutus'] = $paramArray['activity_aboutus'];//关于我们
        $data['activity_customcontent'] = $paramArray['activity_customcontent'];//自定义详情
        $data['activity_sharedesc'] = $paramArray['activity_sharedesc'];//课叮铛分享描述
        $data['activity_shareimg'] = $paramArray['activity_shareimg'];//微信分享小图标
        $data['frommedia_name'] = $paramArray['frommedia_name'];//来源渠道信息
        $data['activity_ischoicecity'] = $paramArray['activity_ischoicecity'];//是否可以选择学校
        $data['activity_ischoiceschool'] = $paramArray['activity_ischoiceschool'];//是否可以选择学校
        $data['activity_demoimg'] = $paramArray['activity_demoimg'];//海报活动专用  的展示图（非镂空）

        $data['activity_issex'] = $paramArray['activity_issex'];//
        $data['activity_issex_must'] = $paramArray['activity_issex_must'];//
        $data['activity_isbirthday'] = $paramArray['activity_isbirthday'];//
        $data['activity_isbirthday_must'] = $paramArray['activity_isbirthday_must'];//
        $data['activity_isaddress'] = $paramArray['activity_isaddress'];//
        $data['activity_isaddress_must'] = $paramArray['activity_isaddress_must'];//

        $data['package_branch'] = $paramArray['package_branch'];//

        $data['activity_updatetime'] = time();

        if ($account_class == '1') {
            $istrue = $this->DataControl->updateData("crm_sell_activity", "activity_id = '{$paramArray['activity_id']}' and company_id = '{$paramArray['company_id']}' ", $data);
        } else {
            $istrue = $this->DataControl->updateData("crm_sell_activity", "activity_id = '{$paramArray['activity_id']}' and staffer_id = '{$paramArray['staffer_id']}' and company_id = '{$paramArray['company_id']}'", $data);
        }

        $goalOne = $this->DataControl->getFieldOne("crm_sell_activity", "activity_id,activity_fittype", "activity_id = '{$paramArray['activity_id']}'");
        if ($goalOne['activity_fittype'] == '1' && $paramArray['activity_fittype'] == '0') {
            $this->DataControl->delData('crm_sell_activity_school', "company_id='{$paramArray['company_id']}' and activity_id='{$paramArray['activity_id']}' ");
        }

        if ($istrue) {
            if($paramArray['promotion_json']){
                $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
                $paramArray['marketer_id'] = $markOne['marketer_id'];
                if(!$markOne){
                    $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                    $paramArray['marketer_id'] = $getaddmarkertOne['marketer_id'];
                }

                $Model = new SellgoalModel();
                $this->DataControl->delData("crm_sell_activity_promotion", "activity_id = '{$paramArray['activity_id']}'");
                $Model->addGroundPromotionAction($paramArray, $paramArray['activity_id']);
            }
            return $data;
        } else {
            return false;
        }
    }

    //活动学校适配表
    function ActivitySchoolApi($paramArray)
    {
        $datawhere = " 1 and  s.company_id = '{$paramArray['company_id']}' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }
        //省份
        if (isset($paramArray['school_province']) && $paramArray['school_province'] != '') {
            $datawhere .= " and s.school_province = '{$paramArray['school_province']}'";
        }
        //城市
        if (isset($paramArray['school_city']) && $paramArray['school_city'] != '') {
            $datawhere .= " and s.school_city = '{$paramArray['school_city']}'";
        }
        //区
        if (isset($paramArray['school_area']) && $paramArray['school_area'] != '') {
            $datawhere .= " and s.school_area = '{$paramArray['school_area']}'";
        }
        //区域
        if (isset($paramArray['district_id']) && $paramArray['district_id'] != '') {
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //类型
        if (isset($paramArray['school_type']) && $paramArray['school_type'] != '') {
            $datawhere .= " and s.school_type = '{$paramArray['school_type']}'";
        }

        if (isset($paramArray['adaptive']) && $paramArray['adaptive'] != '') {
            $activityList = $this->DataControl->selectClear("select a.school_id from crm_sell_activity_school as a WHERE  a.activity_id = '{$paramArray['activity_id']}' and  a.company_id = '{$paramArray['company_id']}' GROUP BY a.school_id ");
            if (is_array($activityList)) {
                $schoolid = '';
                foreach ($activityList as $activityVar) {
                    $schoolid .= $activityVar['school_id'] . ',';
                }
                $schoolid = substr($schoolid, 0, -1);
            }

            $schoolid = ($schoolid == '') ? 0 : $schoolid;

            if ($paramArray['adaptive'] == '0') {
                $datawhere .= " and s.school_id not in ($schoolid)";
            } else {
                $datawhere .= " and s.school_id in ($schoolid)";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(s.school_id)  as datanum
                 FROM smc_school AS s
                 WHERE{$datawhere} ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }

        $sqlfields = " s.school_id,s.school_branch,s.school_type,s.school_cnname,s.school_enname,
                    (select d.district_cnname from gmc_company_district as d where s.district_id = d.district_id) as district_cnname,
                    (SELECT count(a.activity_id) from crm_sell_activity_school as a WHERE s.school_id = a.school_id  and a.activity_id = '{$paramArray['activity_id']}') as adaptive ";

        $sql = " SELECT {$sqlfields} FROM smc_school AS s
                WHERE {$datawhere} and s.school_isclose = '0'
                ORDER BY s.school_id DESC 
                limit {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);

        if (is_array($dataList)) {
            foreach ($dataList as &$dataListVar) {
                if ($dataListVar['school_type'] == '1') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('直营校');
                } elseif ($dataListVar['school_type'] == '2') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('直营园');
                } elseif ($dataListVar['school_type'] == '3') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('加盟校');
                } elseif ($dataListVar['school_type'] == '4') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('加盟园');
                }
            }
        }

        //区域
        $district = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district  where  company_id='{$paramArray['company_id']}'");


        $result = array();
        $result["district"] = $district;
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    //活动创建人管理
    function ActivityFounderApi($paramArray)
    {
        $datawhere = " 1 and a.company_id = '{$paramArray['company_id']}' and a.activity_type = '1'  ";
        $sql = "SELECT a.staffer_id,s.staffer_cnname,s.staffer_enname,s.staffer_branch 
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id=s.staffer_id
                WHERE {$datawhere}
                GROUP BY a.staffer_id";
        $marketerList = $this->DataControl->selectClear($sql);
        return $marketerList;
    }

    //删除某个招生活动
    function delGoalActivityAction($activity_id, $staffer_id)
    {
        if ($this->DataControl->delData('crm_sell_activity', "activity_id='{$activity_id}' and staffer_id='{$staffer_id}' ")) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }


    //活动来源未分配名单信息
    function ActivityClient($paramArray)
    {
        $datawhere = "c.company_id = '{$paramArray['company_id']}' and c.activity_id <> '' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            if ($paramArray['keyword'] == '###') {
                $datawhere .= " and c.outthree_userid = ''";
            } else {
                $datawhere .= "  and ( a.activity_name like '{$paramArray['keyword']}' or c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_gmcmarket like '%{$paramArray['keyword']}%' or c.client_tmkbatch like '%{$paramArray['keyword']}%' or p.promotion_name like '%{$paramArray['keyword']}%')";
            }
        }

        if (isset($paramArray['client_tracestatus']) && $paramArray['client_tracestatus'] !== '') {
            $datawhere .= " and c.client_tracestatus = '{$paramArray['client_tracestatus']}' ";
        }
        //开始结束时间
        if (isset($paramArray['activity_starttime']) && $paramArray['activity_starttime'] != '') {
            $activity_starttime = strtotime($paramArray['activity_starttime']);
            $datawhere .= " and c.client_updatetime >= '{$activity_starttime}'";
        }
        if (isset($paramArray['activity_endtime']) && $paramArray['activity_endtime'] != '') {
            $activity_endtime = strtotime($paramArray['activity_endtime']) + 86400;
            $datawhere .= " and c.client_updatetime <= '{$activity_endtime}'";
        }
        //开始结束时间
        if (isset($paramArray['create_starttime']) && $paramArray['create_starttime'] != '') {
            $create_starttime = strtotime($paramArray['create_starttime']);
            $datawhere .= " and c.client_createtime >= '{$create_starttime}'";
        }
        if (isset($paramArray['create_endtime']) && $paramArray['create_endtime'] != '') {
            $create_endtime = strtotime($paramArray['create_endtime']) + 86400;
            $datawhere .= " and c.client_createtime <= '{$create_endtime}'";
        }
        if (isset($paramArray['frommedia_name']) && $paramArray['frommedia_name'] != '') {
            $datawhere .= " and a.frommedia_name = '{$paramArray['frommedia_name']}'";
        }
        if (isset($paramArray['channel_id']) && $paramArray['channel_id'] != '') {
            $datawhere .= " and a.channel_id = '{$paramArray['channel_id']}'";
        }

        //创建人 筛选
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != '') {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }
        //是否报名 筛选
        if (isset($paramArray['is_signup']) && $paramArray['is_signup'] == '1') {
            $datawhere .= " and (select v.conversionlog_id from crm_client_conversionlog as v WHERE v.client_id = c.client_id and v.student_branch <> '' limit 1) > '0'";
        } elseif (isset($paramArray['is_signup']) && $paramArray['is_signup'] == '0') {
            $datawhere .= " and (select v.conversionlog_id from crm_client_conversionlog as v WHERE v.client_id = c.client_id and v.student_branch <> '' limit 1) < '1'";
        }
        //是否试听 筛选
        if (isset($paramArray['is_audition']) && $paramArray['is_audition'] == '1') {
            $datawhere .= " and (select u.audition_id from crm_client_audition as u WHERE u.client_id = c.client_id and u.audition_isvisit = '1' limit 1) > '0' ";
        } elseif (isset($paramArray['is_audition']) && $paramArray['is_audition'] == '0') {
            $datawhere .= " and (select u.audition_id from crm_client_audition as u WHERE u.client_id = c.client_id and u.audition_isvisit = '1' limit 1) < '1' ";
        }

        //筛选类型
        if (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '0') {
            $datawhere .= " and  c.client_tracestatus <> -2";
        } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '1') {
            $datawhere .= "  and (s.schoolenter_id <> '' &&  s.schoolenter_id is not null)  and c.company_id = s.company_id and c.client_tracestatus <> -2   ";
        } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '-1') {
            $datawhere .= " and (s.schoolenter_id = '' or  s.schoolenter_id is null) and c.client_tracestatus <> -2";
        } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '-2') {
            $datawhere .= "  and c.client_tracestatus ='-2'  ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
        }

        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
        if ($paramArray['is_export'] !== '1') {
            if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
                if ($paramArray['dataequity'] != '1') {
                    $sql = "SELECT  
                (select channel_name from crm_code_channel  as ch where ch.channel_id=a.channel_id limit 0,1 ) as channel_name,
                (SELECT
                    group_concat( cyo.organize_id ) 
                FROM
                    crm_channel_organize AS co
                    LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
                WHERE
                    co.channel_id = c.channel_id 
                ) AS organize_id
                FROM crm_client as c
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1'  having organize_id like '%{$organize_id['organize_id']}%'";
                    $count = $this->DataControl->selectClear($sql);
                    $count = count($count) + 0;
                } else {
                    $sql = "SELECT  c.client_id 
                 FROM crm_client as c 
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1' ";
                    $count = $this->DataControl->selectClear($sql);
                    $count = count($count) + 0;
                }


            } else {
                $count = '';
            }
            $sqlfields = " c.client_id,c.client_gmcmarket,c.channel_id,c.activity_id,c.client_cnname,c.client_enname,c.client_age,c.client_sex,c.client_source,c.client_mobile,c.client_address,c.client_remark,c.client_createtime,c.client_updatetime,c.client_tracestatus,a.activity_name,a.frommedia_name,s.schoolenter_id,c.area_id,c.client_tmkbatch
            ,(SELECT so.school_cnname FROM smc_school as so where so.school_id = s.school_id) as school_cnname
            ,(select mm.marketer_name from crm_marketer as mm where c.marketer_id = mm.marketer_id) as marketer_name
            ";
            if ($paramArray['dataequity'] != '1') {
                $sql = "SELECT  {$sqlfields},s.school_id,p.promotion_name,
                (select channel_name from crm_code_channel  as ch where ch.channel_id=a.channel_id limit 0,1 ) as channel_name,
                (SELECT
                    group_concat( cyo.organize_id ) 
                FROM
                    crm_channel_organize AS co
                    LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
                WHERE
                    co.channel_id = c.channel_id 
                ) AS organize_id,
				(select r.region_name from smc_code_region as r where c.province_id=r.region_id ) as province_name,
				(select r.region_name from smc_code_region as r where c.city_id=r.region_id ) as city_name,
				(select r.region_name from smc_code_region as r where c.area_id=r.region_id ) as area_name
                FROM crm_client as c
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1'  having organize_id like '%{$organize_id['organize_id']}%'
                ORDER BY client_id DESC 
                limit {$pagestart},{$num}";
            } else {
                $sql = "SELECT  {$sqlfields},s.school_id,p.promotion_name,
                (select channel_name from crm_code_channel  as ch where ch.channel_id=a.channel_id limit 0,1 ) as  channel_name,
				(select r.region_name  from smc_code_region as r where c.province_id=r.region_id ) as province_name,
				(select r.region_name  from smc_code_region as r where c.city_id=r.region_id ) as city_name,
				(select r.region_name  from smc_code_region as r where c.area_id=r.region_id ) as area_name
                FROM crm_client as c
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1'
                ORDER BY client_id DESC 
                limit {$pagestart},{$num}";
            }

            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                $clientstrid = '0';
                foreach ($dataList as &$dataOne) {
                    $dataOne['shengshiqu'] = ($dataOne['area_id'] > 0) ? $dataOne['province_name'] . '-' . $dataOne['city_name'] . '-' . $dataOne['area_name'] : '--';
                    if($dataOne['promotion_name']){
                        $dataOne['marketer_name'] = $dataOne['promotion_name'];
                    }else{
                        $dataOne['marketer_name'] = is_null($dataOne['marketer_name'])?'--':$dataOne['marketer_name'];
                    }

                    if (!$this->stafferOne['account_class'] == '1') {
                        $dataOne['client_mobile'] = hideNumberString($dataOne['client_mobile']);
                    }

                    //是否报名
                    if ($this->DataControl->selectOne("select c.conversionlog_id from crm_client_conversionlog as c WHERE c.client_id = '{$dataOne['client_id']}' and c.student_branch <> '' and c.school_id = '{$dataOne['school_id']}' ")) {
                        $dataOne['issignup'] = $this->LgStringSwitch('已报名');
                        $clientstrid .= "," . $dataOne['client_id'];
                    } else {
                        $dataOne['issignup'] = $this->LgStringSwitch('未报名');
                    }
                    //是否试听
                    if ($this->DataControl->selectOne("select a.marketer_id from crm_client_audition as a WHERE a.client_id = '{$dataOne['client_id']}' and a.audition_isvisit = '1' and a.school_id = '{$dataOne['school_id']}' ")) {
                        $dataOne['isaudition'] = $this->LgStringSwitch('已试听');
                    } else {
                        $dataOne['isaudition'] = $this->LgStringSwitch('未试听');
                    }
                    //负责人 crm_client_principal
                    $principallist = $this->DataControl->selectClear("select p.principal_id,m.marketer_name from crm_client_principal as p 
                            LEFT JOIN crm_marketer as m ON p.marketer_id = m.marketer_id 
                            WHERE p.client_id = '{$dataOne['client_id']}' and p.principal_leave <> '1'
                            ORDER BY p.principal_ismajor DESC ");
                    if (is_array($principallist)) {
                        $ilinkidArray = array();
                        foreach ($principallist as $principalvar) {
                            $ilinkidArray[] = $principalvar['marketer_name'];
                        }
                        $dataOne['principalstr'] = implode(",", $ilinkidArray);
                    } else {
                        $dataOne['principalstr'] = $this->LgStringSwitch('无');
                    }

                    if ($dataOne['schoolenter_id'] != '') {
                        $dataOne['isbranch'] = '1';
                    } else {
                        $dataOne['isbranch'] = '-1';
                        $dataOne['school_cnname'] = '---';
                    }
                    $dataOne['client_updatetime'] = date("Y-m-d H:i:s", ($dataOne['client_updatetime'] > 0 ? $dataOne['client_updatetime'] : $dataOne['client_createtime']));
                    $dataOne['client_createtime'] = date("Y-m-d H:i:s", $dataOne['client_createtime']);
                    $dataOne['client_status'] = $clientTracestatus[$dataOne['client_tracestatus']];
                }

            }
        } else {
            if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
                $sql = "SELECT  COUNT(c.client_id)  as datanum
                 FROM crm_client as c 
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1'  ";
                $count = $this->DataControl->selectOne($sql);
                $count = $count['datanum'] + 0;
            } else {
                $count = '';
            }
            $sqlfields = " c.client_id,c.client_gmcmarket,c.channel_id,c.activity_id,c.client_cnname,c.client_enname,c.client_age,c.client_sex,c.client_source,c.client_mobile,c.client_gmcmarket,c.client_createtime,c.client_address,c.client_remark,c.client_updatetime,c.client_tracestatus,a.activity_name,a.frommedia_name,s.schoolenter_id,client_distributionstatus,c.client_tmkbatch,
            (SELECT so.school_cnname FROM smc_school as so where so.school_id = s.school_id) as school_cnname,(SELECT so.school_branch FROM smc_school as so where so.school_id = s.school_id) as school_branch,
            (SELECT count(i.invite_id) from crm_client_invite as i where i.client_id = c.client_id) as  invitenum, 
            (SELECT iv.invite_visittime from crm_client_invite as iv where iv.client_id = c.client_id order by iv.invite_id desc limit 0,1 ) as  invite_visittime,  
            (SELECT iv.invite_isvisit from crm_client_invite as iv where iv.client_id = c.client_id order by iv.invite_id desc limit 0,1 ) as  invite_isvisit,  
            (SELECT iv.invite_novisitreason from crm_client_invite as iv where iv.client_id = c.client_id  order by iv.invite_id limit 0,1 ) as  invite_novisitreason, 
            (SELECT count(a.audition_id) from crm_client_audition as a where a.client_id = c.client_id) as  auditionnum, 
            (SELECT iv.invite_id from crm_client_invite as iv where iv.client_id = c.client_id and iv.invite_isvisit = '1' order by iv.invite_id desc limit 0,1 ) as  invite_isvisitture,  
            
            (SELECT a.audition_genre from crm_client_audition as a where a.client_id = c.client_id order by a.audition_id desc limit 0,1 ) as  audition_genre,  
            (SELECT a.audition_visittime from crm_client_audition as a where a.client_id = c.client_id order by a.audition_id desc limit 0,1 ) as  audition_visittime,  
            (SELECT a.audition_isvisit from crm_client_audition as a where a.client_id = c.client_id order by a.audition_id desc limit 0,1 ) as  audition_isvisit,   
            (SELECT a.audition_novisitreason from crm_client_audition as a where a.client_id = c.client_id  order by a.audition_id desc limit 0,1 ) as  audition_novisitreason,   
            (SELECT a.audition_id from crm_client_audition as a where a.client_id = c.client_id and audition_isvisit = '1' order by a.audition_id desc limit 0,1 ) as  audition_isvisitture,   
            
            (SELECT t.track_createtime from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1 ) as  track_createtime,  
            (SELECT t.track_note from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1 ) as  track_note,  
            (SELECT t.marketer_name from crm_client_track as t where t.client_id = c.client_id order by t.track_id desc limit 0,1 ) as  marketer_name,
            (select r.region_name  from smc_code_region as r where c.province_id=r.region_id ) as province_name,
            (select r.region_name  from smc_code_region as r where c.city_id=r.region_id ) as city_name,
            (select r.region_name  from smc_code_region as r where c.area_id=r.region_id ) as area_name,
            (select mm.marketer_name from crm_marketer as mm where c.marketer_id = mm.marketer_id) as mmmarketer_name ";

            if ($paramArray['dataequity'] != '1') {
                $sql = "SELECT  {$sqlfields},s.school_id,p.promotion_name,
                (SELECT channel_name from crm_code_channel  as ch where ch.channel_id=a.channel_id limit 0,1 ) as channel_name,
                (SELECT
                    group_concat( cyo.organize_id ) 
                FROM
                    crm_channel_organize AS co
                    LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
                WHERE
                    co.channel_id = c.channel_id 
                ) AS organize_id
                FROM crm_client as c
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1' having organize_id like '%{$organize_id['organize_id']}%'
                ORDER BY client_id DESC";
            } else {
                $sql = "SELECT  {$sqlfields},s.school_id,p.promotion_name,
                (SELECT channel_name from crm_code_channel  as ch where ch.channel_id=a.channel_id limit 0,1 ) as  channel_name
                FROM crm_client as c
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id
                LEFT JOIN crm_ground_promotion as p ON p.promotion_id = c.promotion_id
                WHERE {$datawhere} and a.activity_type = '1' 
                ORDER BY client_id DESC";
            }
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as &$dataOne) {
                    //是否报名
                    if ($this->DataControl->selectOne("select c.conversionlog_id from crm_client_conversionlog as c WHERE c.client_id = '{$dataOne['client_id']}' and c.student_branch <> '' and c.school_id = '{$dataOne['school_id']}' ")) {
                        $dataOne['issignup'] = $this->LgStringSwitch('已报名');
                    } else {
                        $dataOne['issignup'] = $this->LgStringSwitch('未报名');
                    }
                    //是否试听
                    if ($this->DataControl->selectOne("select a.marketer_id from crm_client_audition as a WHERE a.client_id = '{$dataOne['client_id']}' and a.audition_isvisit = '1' and a.school_id = '{$dataOne['school_id']}' ")) {
                        $dataOne['isaudition'] = $this->LgStringSwitch('已试听');
                    } else {
                        $dataOne['isaudition'] = $this->LgStringSwitch('未试听');
                    }
                    //负责人 crm_client_principal
                    $principallist = $this->DataControl->selectClear("select p.principal_id,m.marketer_name from crm_client_principal as p 
                            LEFT JOIN crm_marketer as m ON p.marketer_id = m.marketer_id 
                            WHERE p.client_id = '{$dataOne['client_id']}' and p.principal_leave <> '1'
                            ORDER BY p.principal_ismajor DESC ");
                    if (is_array($principallist)) {
                        $ilinkidArray = array();
                        foreach ($principallist as $principalvar) {
                            $ilinkidArray[] = $principalvar['marketer_name'];
                        }
                        $dataOne['principalstr'] = implode(",", $ilinkidArray);
                    } else {
                        $dataOne['principalstr'] = $this->LgStringSwitch('无');
                    }

                    if ($dataOne['schoolenter_id'] != '') {
                        $dataOne['isbranch'] = '1';
                    } else {
                        $dataOne['isbranch'] = '-1';
                        $dataOne['school_cnname'] = '---';
                    }

                    if($dataOne['promotion_name']){
                        $dataOne['mmmarketer_name'] = $dataOne['promotion_name'];
                    }else{
                        $dataOne['mmmarketer_name'] = is_null($dataOne['mmmarketer_name'])?'--':$dataOne['mmmarketer_name'];
                    }
                    $dataOne['client_status'] = $clientTracestatus[$dataOne['client_tracestatus']];
                }
            }
        }


        if ($paramArray['is_export'] == '1') {

            if (!$dataList) {
                ajax_return(array('error' => 1, 'errortip' => "报表内容为空，不可导出!"), $this->companyOne['company_language']);
            }

            $outexceldate = array();
            if ($dataList) {
                $outexceldate = array();
                foreach ($dataList as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['client_updatetime'] = $dateexcelvar['client_updatetime'] > 0 ? date("Y-m-d", $dateexcelvar['client_updatetime']) : date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['client_tmkbatch'] = $dateexcelvar['client_tmkbatch'];
                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    if ($this->stafferOne['account_class'] == '1') {
                        $datearray['client_mobile'] = $dateexcelvar['client_mobile'];
                    } else {
                        $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    }
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['activity_name'] = $dateexcelvar['activity_name'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['frommedia_name'] = $dateexcelvar['frommedia_name'];
                    $datearray['client_gmcmarket'] = $dateexcelvar['client_gmcmarket'];
                    $datearray['client_remark'] = $dateexcelvar['client_remark'];

//                    $datearray['isaudition'] = $dateexcelvar['isaudition'];
                    $datearray['issignup'] = $dateexcelvar['issignup'];
                    $datearray['principalstr'] = $dateexcelvar['principalstr'];
                    $datearray['client_status'] = $dateexcelvar['client_status'];

                    $datearray['invitenumtmk'] = ($dateexcelvar['invitenum'] > 0 || $dateexcelvar['auditionnum'] > 0) ? "是" : "否";  //TMK-学校是否邀约
                    $datearray['invitenumtmktrue'] = ($dateexcelvar['invite_isvisitture'] > 0 || $dateexcelvar['audition_isvisitture'] > 0) ? "是" : "否";  //TMK-学校邀约是否到访

                    $datearray['invitenumtype'] = ($dateexcelvar['invitenum'] > 0) ? "是" : "否";  //是否邀约柜询
                    $datearray['invitenum'] = $dateexcelvar['invitenum'];  //邀约柜询次数
                    $datearray['invite_visittime'] = $dateexcelvar['invite_visittime'];  //最新柜询日期
                    if ($dateexcelvar['invite_isvisit'] == '0') {
                        $datearray['invite_isvisit'] = $this->LgStringSwitch("待到访");  //最新邀约柜询状态
                    } elseif ($dateexcelvar['invite_isvisit'] == '1') {
                        $datearray['invite_isvisit'] = $this->LgStringSwitch("已到访");  //最新邀约柜询状态
                    } elseif ($dateexcelvar['invite_isvisit'] == '-1') {
                        $datearray['invite_isvisit'] = $this->LgStringSwitch("未到访");  //最新邀约柜询状态
                    } else {
                        $datearray['invite_isvisit'] = "--";
                    }
                    $datearray['invite_novisitreason'] = $dateexcelvar['invite_novisitreason'];  //最新柜询未到访原因

                    $datearray['auditionnumtype'] = ($dateexcelvar['auditionnum'] > 0) ? "是" : "否";  //是否邀约试听
                    $datearray['auditionnum'] = $dateexcelvar['auditionnum'];  // 邀约试听次数
                    $datearray['audition_genre'] = $dateexcelvar['audition_genre'];  //最新邀约试听类型
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];  //最新邀约试听日期
                    if ($dateexcelvar['audition_isvisit'] == '0') {
                        $datearray['audition_isvisit'] = $this->LgStringSwitch("待到访");  //最新邀约试听状态
                    } elseif ($dateexcelvar['audition_isvisit'] == '1') {
                        $datearray['audition_isvisit'] = $this->LgStringSwitch("已到访");  //最新邀约试听状态
                    } elseif ($dateexcelvar['audition_isvisit'] == '-1') {
                        $datearray['audition_isvisit'] = $this->LgStringSwitch("未到访");  //最新邀约试听状态
                    } else {
                        $datearray['audition_isvisit'] = "--";
                    }
                    $datearray['audition_novisitreason'] = $dateexcelvar['audition_novisitreason'];  //最新邀约试听未到访原因
                    if ($dateexcelvar['track_createtime'] != '') {
                        $datearray['track_createtime'] = date("Y-m-d", $dateexcelvar['track_createtime']);  //最后跟踪时间
                    } else {
                        $datearray['track_createtime'] = "--";
                    }

                    $datearray['track_note'] = $dateexcelvar['track_note'];  //最后跟踪备注

                    if($dateexcelvar['promotion_name']){//录入人
                        $datearray['mmmarketer_name'] = $dateexcelvar['promotion_name'];
                    }else{
                        $datearray['mmmarketer_name'] = $dateexcelvar['mmmarketer_name'];
                    }

//                    $datearray['mmmarketer_name'] = $dateexcelvar['mmmarketer_name'];  //录入人
                    $datearray['marketer_name'] = $dateexcelvar['marketer_name'];  //最后跟踪人

//                    $datearray['province_name'] = $dateexcelvar['province_name'];  //省份
//                    $datearray['city_name'] = $dateexcelvar['city_name'];  //城市
//                    $datearray['area_name'] = $dateexcelvar['area_name'];  //区域

                    $datearray['shengshiqu'] = ($dateexcelvar['area_name']) ? $dateexcelvar['province_name'] . '-' . $dateexcelvar['city_name'] . '-' . $dateexcelvar['area_name'] : '--';  //省份

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "更新日期","批次编号", "创建日期", '学员姓名', '联系方式', '年龄', "活动名称", "渠道类型", "渠道明细", "电销姓名", "客户备注", "是否成交", "接待教师", "客户状态", "TMK-学校是否邀约", "TMK-学校邀约是否到访", "是否邀约柜询", "邀约柜询次数", "最新柜询日期", "最新邀约柜询状态", "最新柜询未到访原因", "是否邀约试听", "邀约试听次数", "最新邀约试听类型", "最新邀约试听日期", "最新邀约试听状态", "最新邀约试听未到访原因", "最后跟踪时间", "最后跟踪备注", "录入人", "最后跟踪人", "省市区"));
            $excelfileds = array('school_branch', 'school_cnname', 'client_updatetime','client_tmkbatch', 'client_createtime', 'client_cnname', 'client_mobile', 'client_age', 'activity_name', 'channel_name', 'frommedia_name', 'client_gmcmarket', 'client_remark', 'issignup', 'principalstr', 'client_status', 'invitenumtmk', 'invitenumtmktrue', 'invitenumtype', 'invitenum', 'invite_visittime', 'invite_isvisit', 'invite_novisitreason', 'auditionnumtype', 'auditionnum', 'audition_genre', 'audition_visittime', 'audition_isvisit', 'audition_novisitreason', 'track_createtime', 'track_note', 'mmmarketer_name','marketer_name', 'shengshiqu');

            //筛选类型
            if (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '0') {
                $name = $this->LgStringSwitch("有效名单报表.xlsx");
            } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '1') {
                $name = $this->LgStringSwitch("已分配名单报表.xlsx");
            } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '-1') {
                $name = $this->LgStringSwitch("待分配名单报表.xlsx");
            }
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "招生管理", '导出活动招生名单', dataEncode($paramArray));
            query_to_excel($excelheader, $outexceldate, $excelfileds, $name);
        }


        $result = array();
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    //未分配的活动名单 批量分配给学校
    function batchAllotClientSchool($paramArray){
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if(empty($clients_array))
        {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        }else {
            if (is_array($clients_array)) {
                foreach ($clients_array as $clientArrayvar) {

                    $data = array();
                    $data['company_id'] = $paramArray['company_id'];
                    $data['client_id'] = $clientArrayvar['client_id'];
                    $data['school_id'] = $paramArray['school_id'];
                    $data['is_schoolenter'] = 0;
                    $data['schoolenter_createtime'] = time();
                    $data['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $data);

                    //新增跟进记录
                    $markertOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
                    if ($markertOne) {
                        $trackData = array();
                        $trackData['client_id'] = $clientArrayvar['client_id'];
                        $trackData['marketer_id'] = $markertOne['marketer_id'];
                        $trackData['marketer_name'] = $markertOne['marketer_name'];
                        $trackData['track_intention_level'] = "3";
                        $trackData['track_linktype'] = "集团分配";
                        $trackData['track_note'] = "由于名单未分配校园，由集团工作人员初步筛选客户意向，分配校园进行跟踪。";
                        $trackData['track_createtime'] = time();
                        $trackData['track_type'] = 1;
                        $trackData['track_initiative'] = 0;
                        $this->DataControl->insertData('crm_client_track', $trackData);
                    } else {
                        $trackData = array();
                        $trackData['client_id'] = $clientArrayvar['client_id'];
                        $trackData['marketer_id'] = 0;
                        $trackData['marketer_name'] = "系统";
                        $trackData['track_intention_level'] = "3";
                        $trackData['track_linktype'] = "集团分配";
                        $trackData['track_note'] = "由于名单未分配校园，由集团工作人员初步筛选客户意向，分配校园进行跟踪。";
                        $trackData['track_createtime'] = time();
                        $trackData['track_type'] = 1;
                        $trackData['track_initiative'] = 0;
                        $this->DataControl->insertData('crm_client_track', $trackData);
                    }

                    $channelOne = $this->DataControl->selectOne("select c.channel_quality from crm_client as t,crm_code_channel as c where t.client_id = '{$clientArrayvar['client_id']}' and t.channel_id = c.channel_id limit 0,1 ");//channel_quality  0 默认毛名单 1 默认有效名单
                    $clientData = array();
                    $clientData['client_updatetime'] = time();
                    if($channelOne['channel_quality'] == '1') {
                        $clientData['client_isgross'] = 0;//不是毛名单
                    }elseif($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != '') {
                        $clientData['client_isgross'] = 1;//是毛名单
                    }else{
                        $clientData['client_isgross'] = 0;//不是毛名单
                    }
                    $this->DataControl->updateData("crm_client", "client_id='{$clientArrayvar['client_id']}'", $clientData);
                }

                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "活动名单管理", '分配名单', dataEncode($paramArray));

                $this->error = 0;
                $this->errortip = "分配成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "分配名单不存在";
                return false;
            }
        }
    }

    //活动来源未分配名单信息 学校适配表
    function ActivityClientSchoolApi($paramArray)
    {
        $datawhere = " 1 and  s.company_id = '{$paramArray['company_id']}' and s.school_isclose <> '1' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }
        //省份
        if (isset($paramArray['school_province']) && $paramArray['school_province'] != '') {
            $datawhere .= " and s.school_province = '{$paramArray['school_province']}'";
        }
        //城市
        if (isset($paramArray['school_city']) && $paramArray['school_city'] != '') {
            $datawhere .= " and s.school_city = '{$paramArray['school_city']}'";
        }
        //区
        if (isset($paramArray['school_area']) && $paramArray['school_area'] != '') {
            $datawhere .= " and s.school_area = '{$paramArray['school_area']}'";
        }
        //区域
        if (isset($paramArray['district_id']) && $paramArray['district_id'] != '') {
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //类型
        if (isset($paramArray['school_type']) && $paramArray['school_type'] != '') {
            $datawhere .= " and s.school_type = '{$paramArray['school_type']}'";
        }

        //活动中 有效名单所适配的学校
        if (isset($paramArray['adaptive']) && $paramArray['adaptive'] != '') {
            //活动所适配的学校
            $activityList = $this->DataControl->selectClear("select a.school_id from crm_sell_activity_school as a WHERE  a.activity_id = '{$paramArray['activity_id']}' and  a.company_id = '{$paramArray['company_id']}' GROUP BY a.school_id ");
            if (is_array($activityList)) {
                $schoolid = '';
                foreach ($activityList as $activityVar) {
                    $schoolid .= $activityVar['school_id'] . ',';
                }
                $schoolid = substr($schoolid, 0, -1);
            }
//            $schoolid = ($schoolid=='')?0:$schoolid;
            if ($schoolid == '') {//当活动中适配的学校为空时，客户面向全部学校
                $clentList = $this->DataControl->selectClear("select a.school_id from crm_client_schoolenter as a WHERE  a.client_id = '{$paramArray['client_id']}' and  a.company_id = '{$paramArray['company_id']}' GROUP BY a.school_id ");
                if (is_array($clentList)) {
                    $clentschoolid = '';
                    foreach ($clentList as $clentVar) {
                        $clentschoolid .= $clentVar['school_id'] . ',';
                    }
                    $clentschoolid = substr($clentschoolid, 0, -1);
                }
                $clentschoolid = ($clentschoolid == '') ? 0 : $clentschoolid;

                //是否适配的学校
                if ($paramArray['adaptive'] == '0') {
                    $datawhere .= " and s.school_id not in ($clentschoolid)";
                } else {
                    $datawhere .= " and s.school_id in ($clentschoolid)";
                }
            } else {//当活动中适配的学校为空时，客户面向 局部学校
                $clentList = $this->DataControl->selectClear("select a.school_id from crm_client_schoolenter as a WHERE  a.client_id = '{$paramArray['client_id']}' and  a.company_id = '{$paramArray['company_id']}' and a.school_id in ($schoolid) GROUP BY a.school_id ");
                if (is_array($clentList)) {
                    $clentschoolid = '';
                    foreach ($clentList as $clentVar) {
                        $clentschoolid .= $clentVar['school_id'] . ',';
                    }
                    $clentschoolid = substr($clentschoolid, 0, -1);
                }
                $clentschoolid = ($clentschoolid == '') ? 0 : $clentschoolid;

                //是否适配的学校
                if ($paramArray['adaptive'] == '0') {
                    //取出来差集
                    $schoollist = explode(',', $schoolid);
                    $clentschoollist = explode(',', $clentschoolid);
                    $theschoollist = array_diff($schoollist, $clentschoollist);//前大，后小
                    //未适配的学校
                    $theschoolid = '';
                    if (is_array($theschoollist)) {
                        foreach ($theschoollist as $theschoolidvar) {
                            $theschoolid .= $theschoolidvar . ',';
                        }
                        $theschoolid = substr($theschoolid, 0, -1);
                    }
                    $theschoolid = ($theschoolid == '') ? 0 : $theschoolid;
                    $datawhere .= " and s.school_id in ($theschoolid)";
                } else {
                    $datawhere .= " and s.school_id in ($clentschoolid)";
                }
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(s.school_id)  as datanum
                 FROM smc_school AS s
                 WHERE{$datawhere} ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }

        $sqlfields = " s.school_id,s.school_branch,s.school_type,s.school_cnname,s.school_enname,
                    (select d.district_cnname from gmc_company_district as d where s.district_id = d.district_id) as district_cnname,
                    (SELECT count(a.schoolenter_id) from crm_client_schoolenter as a WHERE s.school_id = a.school_id  and a.client_id = '{$paramArray['client_id']}' and a.company_id = '{$paramArray['company_id']}') as adaptive ";

        $sql = " SELECT {$sqlfields} FROM smc_school AS s 
                WHERE {$datawhere}
                ORDER BY s.school_id DESC 
                limit {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);

        if (is_array($dataList)) {
            foreach ($dataList as &$dataListVar) {
                if ($dataListVar['school_type'] == '1') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('直营校');
                } elseif ($dataListVar['school_type'] == '2') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('直营园');
                } elseif ($dataListVar['school_type'] == '3') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('加盟校');
                } elseif ($dataListVar['school_type'] == '4') {
                    $dataListVar['school_typename'] = $this->LgStringSwitch('加盟园');
                }
            }
        }

        //区域
        $district = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district WHERE company_id = '{$paramArray['company_id']}' ");

        $result = array();
        $result["district"] = $district;
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    function ChannelClientSchoolApi($paramArray)
    {
        $datawhere = " 1 and  s.company_id = '{$paramArray['company_id']}' and s.school_isclose <> '1' ";

        $sqlfields = " s.school_id,s.school_branch,s.school_type,s.school_cnname,s.school_enname,
                    (select d.district_cnname from gmc_company_district as d where s.district_id = d.district_id) as district_cnname,
                    (SELECT count(a.schoolenter_id) from crm_client_schoolenter as a WHERE s.school_id = a.school_id  and a.client_id = '{$paramArray['client_id']}' and a.company_id = '{$paramArray['company_id']}') as adaptive ";

        $sql = " SELECT {$sqlfields} FROM smc_school AS s 
                WHERE {$datawhere}
                ORDER BY s.school_id DESC";

        $dataList = $this->DataControl->selectClear($sql);

        $result = array();
        $result["datalist"] = $dataList;
        return $result;
    }

    //为分配名单所对应的活动
    function UnClientActivity($paramArray)
    {
        $datawhere = " 1 and c.company_id = '{$paramArray['company_id']}' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (c.activity_name like '%{$paramArray['keyword']}%')";
        }
        //开始结束时间
//        if(isset($paramArray['starttime']) && $paramArray['starttime'] != ''){
//            $datawhere .= " and c.starttime >= '{$paramArray['starttime']}'";
//        }
//        if(isset($paramArray['endtime']) && $paramArray['endtime'] != ''){
//            $datawhere .= " and c.endtime <= '{$paramArray['endtime']}'";
//        }
        //创建人 筛选
        if (isset($paramArray['activity_id']) && $paramArray['activity_id'] != '') {
            $datawhere .= " and c.activity_id = '{$paramArray['activity_id']}'";
        }

        //筛选类型
        if (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '0') {
            $datawhere .= "";
        } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '1') {
            $datawhere .= "  and (s.schoolenter_id <> '' &&  s.schoolenter_id is not null) and c.company_id = s.company_id  ";
        } elseif (isset($paramArray['activity_type']) && $paramArray['activity_type'] == '-1') {
            $datawhere .= " and (s.schoolenter_id = '' or  s.schoolenter_id is null) ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(c.client_id)  as datanum
                 FROM crm_client as c 
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                WHERE {$datawhere} and a.activity_type = '1' 
                GROUP by c.activity_id
                ORDER BY c.activity_id DESC ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }
        $sqlfields = " c.activity_id,a.activity_name";
        $sql = "SELECT  {$sqlfields}
                FROM crm_client as c
                LEFT JOIN crm_sell_activity as a ON c.activity_id = a.activity_id 
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id 
                WHERE {$datawhere} and a.activity_type = '1' 
                GROUP by c.activity_id
                ORDER BY c.activity_id DESC 
                limit {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);

        $result = array();
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    //招生目标 -- 删除某个招生活动
    function delClientActivityAction($client_id)
    {
        if ($this->DataControl->delData("crm_client", "client_id='{$client_id}'")) {
            $res = true;
        } else {
            $res = false;
        }
        return $res;
    }

    //招生目标  --作废名单
    function nullifyClientActivityAction($request)
    {
        $data = array();
        $data['client_tracestatus'] = '-2';
        if ($this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}'", $data)) {
            $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$request['staffer_id']}' and company_id='{$request['company_id']}' ");
            $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$request['staffer_id']}'");
            if (!$marOne) {
                $datas = array();
                $datas['company_id'] = $staffer['company_id'];
                $datas['staffer_id'] = $staffer['staffer_id'];
                $datas['postrole_id'] = $staffer['postrole_id'];
                $datas['marketer_istest'] = $staffer['staffer_istest'];
                $datas['marketer_name'] = $staffer['staffer_cnname'];
                $datas['marketer_img'] = $staffer['staffer_img'];
                $datas['marketer_mobile'] = $staffer['staffer_mobile'];
                $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
                $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
                $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
                $datas['marketer_lastip'] = $staffer['staffer_lastip'];
                $datas['marketer_createtime'] = $staffer['staffer_createtime'];
                $datas['marketer_status'] = '1';
                $id = $this->DataControl->insertData('crm_marketer', $datas);
                $marOne['marketer_id'] = $id;
            }
            $data = array();
            $data['client_id'] = $request['client_id'];
            $data['marketer_id'] = $marOne['marketer_id'];
            $data['marketer_name'] = $staffer['staffer_cnname'];
            $data['track_linktype'] = $this->LgStringSwitch('作废名单');
            $data['track_validinc'] = '';
            $data['track_note'] = $this->LgStringSwitch($request['remark']);
            $data['object_code'] = '';
            $data['track_type'] = 1;
            $data['track_initiative'] = 0;
            $data['track_followmode'] = '-3';
            $data['track_state'] = '-2';
            $this->DataControl->insertData('crm_client_track', $data);

            return true;
        } else {
            return false;
        }

    }
    /**
     * 批量作废
     * @param $paramArray
     * @return false
     */
    public function batchDeleteClient($paramArray)
    {
        $clients_array = json_decode(stripslashes($paramArray['clients_json']), 1);
        if(empty($clients_array))
        {
            $this->error = 1;
            $this->errortip = "参数无效";
            return false;
        }else{
            $marOne = $this->DataControl->selectOne("select marketer_id,staffer_id,marketer_name from  crm_marketer where staffer_id='{$paramArray['staffer_id']}' and company_id='{$paramArray['company_id']}' ");
            $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$paramArray['staffer_id']}'");
            if (!$marOne) {
                $datas = array();
                $datas['company_id'] = $staffer['company_id'];
                $datas['staffer_id'] = $staffer['staffer_id'];
                $datas['postrole_id'] = $staffer['postrole_id'];
                $datas['marketer_istest'] = $staffer['staffer_istest'];
                $datas['marketer_name'] = $staffer['staffer_cnname'];
                $datas['marketer_img'] = $staffer['staffer_img'];
                $datas['marketer_mobile'] = $staffer['staffer_mobile'];
                $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
                $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
                $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
                $datas['marketer_lastip'] = $staffer['staffer_lastip'];
                $datas['marketer_createtime'] = $staffer['staffer_createtime'];
                $datas['marketer_status'] = '1';
                $id = $this->DataControl->insertData('crm_marketer', $datas);
                $marOne['marketer_id'] = $id;
            }

            foreach ($clients_array as $key =>$value)
            {
                if ($this->DataControl->updateData("crm_client", "client_id='{$value['client_id']}'", [
                    'client_tracestatus'=>'-2'
                ])) {
                    $data = array();
                    $data['client_id'] = $value['client_id'];
                    $data['marketer_id'] = $marOne['marketer_id'];
                    $data['marketer_name'] = $staffer['staffer_cnname'];
                    $data['track_linktype'] = $this->LgStringSwitch('作废名单');
                    $data['track_validinc'] = '';
                    $data['track_note'] = $this->LgStringSwitch($value['remark']);
                    $data['object_code'] = '';
                    $data['track_type'] = 1;
                    $data['track_initiative'] = 0;
                    $data['track_followmode'] = '-3';
                    $data['track_state'] = '-2';
                    $data['track_createtime'] = time();
                    $this->DataControl->insertData('crm_client_track', $data);

                }

            }
            return true;

        }

    }

    //招生活动 -- >> 活动模板
    function ActivitytempApi($paramArray)
    {
        if ($paramArray['activitytemp_class'] == '1') {
            if ($paramArray['language_type'] == 'tw') {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '1' and activitytemp_class = '1'  ORDER BY activitytemp_id ASC ";
            } else {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '0' and activitytemp_class = '1'  ORDER BY activitytemp_id ASC ";
            }
        } else {
            if ($paramArray['language_type'] == 'tw') {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '1' and activitytemp_class = '0'  ORDER BY activitytemp_id ASC ";
            } else {
                $sql = "SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' and activitytemp_type = '0' and activitytemp_class = '0'  ORDER BY activitytemp_id ASC ";
            }
        }
//        $sql="SELECT * FROM crm_code_activitytemp WHERE activitytemp_putaway = '1' ORDER BY activitytemp_id ASC ";
        $activitytempList = $this->DataControl->selectClear($sql);
        return $activitytempList;
    }

    //招生目标 -- >> 某个活动的信息
    function ActivitytempOneApi($paramArray)
    {
        $activitytempOne = $this->DataControl->getFieldOne("crm_code_activitytemp", " * ", " activitytemp_id = '{$paramArray['activitytemp_id']}' ");
        return $activitytempOne;
    }

    //活动班种适配表
    function ActivityCoursecatApi($paramArray)
    {
        $datawhere = " 1 and c.company_id = '{$paramArray['company_id']}' and t.coursetype_isopenclass <> '1' and c.coursecat_iscrmadded = '1' ";
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (c.coursecat_cnname like '%{$paramArray['keyword']}%' or c.coursecat_branch like '%{$paramArray['keyword']}%')";
        }
        //类型
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
            $datawhere .= " and c.coursetype_id = '{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['adaptive']) && $paramArray['adaptive'] != '') {
            $activityList = $this->DataControl->selectClear("select a.coursecat_id from crm_sell_activity_coursecat as a WHERE a.activity_id = '{$paramArray['activity_id']}' and a.company_id = '{$paramArray['company_id']}' GROUP BY a.coursecat_id ");
            if (is_array($activityList)) {
                $coursecatid = '';
                foreach ($activityList as $activityVar) {
                    $coursecatid .= $activityVar['coursecat_id'] . ',';
                }
                $coursecatid = substr($coursecatid, 0, -1);
            }
            $coursecatid = ($coursecatid == '') ? 0 : $coursecatid;
            if ($paramArray['adaptive'] == '0') {
                $datawhere .= " and c.coursecat_id not in ($coursecatid)";
            } else {
                $datawhere .= " and c.coursecat_id in ($coursecatid)";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $sql = "SELECT  COUNT(c.coursecat_id)  as datanum
                 FROM smc_code_coursecat AS c 
                 left join smc_code_coursetype as t ON c.coursetype_id = t.coursetype_id 
                 WHERE{$datawhere} ";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = '';
        }

        $sqlfields = " c.coursecat_id,c.coursecat_cnname,c.coursecat_branch,t.coursetype_cnname,t.coursetype_branch ";
        //                    (SELECT count(a.actcoursecat_id) from crm_sell_activity_coursecat as a WHERE c.coursecat_id = a.coursecat_id  and a.activity_id = '{$paramArray['activity_id']}') as adaptive

        $sql = " SELECT {$sqlfields} 
                FROM smc_code_coursecat AS c 
                left join smc_code_coursetype as t ON c.coursetype_id = t.coursetype_id 
                WHERE {$datawhere}  
                ORDER BY c.coursecat_id DESC 
                limit {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);

        $result = array();
        $result["datalist"] = $dataList;
        $result["count"] = $count;
        return $result;
    }

    /**
     * 活动班种适配表 -- 活动班种批量适配操作
     * @param $paramArray
     * @return bool
     */
    function batchActCoursecatAction($paramArray)
    {
        $CoursecatArray = json_decode(stripslashes($paramArray['coursecat_json']), 1);
        if(empty($CoursecatArray))
        {
            $this->error = 1;
            $this->errortip = "未选择班种！";
            return false;
        }else {

            $edwhere = "1";

            $chooseid = "";
            foreach ($CoursecatArray as $chick_var) {
                $chooseid .= "'{$chick_var['coursecat_id']}',";
            }
            $idrange = substr($chooseid, 0, -1);
            $edwhere .= " and coursecat_id in ({$idrange})";

            $activityOne = $this->DataControl->selectOne("SELECT * FROM crm_sell_activity WHERE activity_id = '{$paramArray['activity_id']}' and company_id = '{$paramArray['company_id']}' limit 0,1 ");
            if ($paramArray['BatchType'] == '1') {
                $copylist = $this->DataControl->getList("smc_code_coursecat", $edwhere);
                if ($copylist) {
                    foreach ($copylist as $copyvar) {
                        if (!$this->DataControl->getOne("crm_sell_activity_coursecat", "activity_id='{$paramArray['activity_id']}' and coursecat_id='{$copyvar['coursecat_id']}' and company_id = '{$paramArray['company_id']}' ")) {
                            $data = array();
                            $data['company_id'] = $paramArray['company_id'];
                            $data['activity_id'] = $activityOne['activity_id'];
                            $data['coursecat_id'] = $copyvar['coursecat_id'];
                            $this->DataControl->insertData("crm_sell_activity_coursecat", $data);
                        }
                    }
                }

                $this->error = 0;
                $this->errortip = "适配操作成功";
                return true;
            } elseif ($paramArray['BatchType'] == '2') {
                $copylist = $this->DataControl->getList("smc_code_coursecat", $edwhere);
                if ($copylist) {
                    foreach ($copylist as $copyvar) {
                        $this->DataControl->delData('crm_sell_activity_coursecat', "activity_id='{$paramArray['activity_id']}' and coursecat_id='{$copyvar['coursecat_id']}'  and company_id = '{$paramArray['company_id']}' ");
                    }
                }

                $this->error = 0;
                $this->errortip = "取消适配操作成功";
                return true;
            }
        }

    }
}
