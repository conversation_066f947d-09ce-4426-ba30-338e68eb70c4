<?php


namespace Model\Gmc;

class ReportModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function studentBalance($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or c.course_cnname like '%{$request['keyword']}%' or c.course_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursebalance_status']) && $request['coursebalance_status'] !== '') {
            $datawhere .= " and sse.coursebalance_status = '{$request['coursebalance_status']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id = '{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.course_cnname,c.course_branch,ssc.coursebalance_figure,ssc.coursebalance_time,scf.courseforward_price,b.student_balance
              from smc_student_coursebalance as ssc
              left join smc_student as s on ssc.student_id=s.student_id
              LEFT JOIN smc_student_balance as b ON b.student_id = s.student_id and b.school_id='{$request['school_id']}' and b.company_id='{$request['company_id']}'
              left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and ssc.course_id=scf.course_id
              left join smc_course as c on c.course_id=ssc.course_id
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where {$datawhere} and sse.school_id='{$request['school_id']}' and s.company_id='{$request['company_id']}'
              order by sse.enrolled_createtime desc
              limit {$pagestart},{$num}";

        $studentList = $this->DataControl->selectClear($sql);
        if (!$studentList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $count_sql = "select s.student_id
              from smc_student_coursebalance as ssc
              left join smc_student as s on ssc.student_id=s.student_id
              left join smc_student_courseforward as scf on scf.student_id=ssc.student_id and ssc.course_id=scf.course_id
              left join smc_course as c on c.course_id=ssc.course_id
              left join smc_student_enrolled as sse on sse.student_id=s.student_id
              where {$datawhere} and sse.school_id='{$request['school_id']}' and s.company_id='{$request['company_id']}'";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $data['list'] = $studentList;

        return $data;
    }

    function studentTimebalance($request)
    {
        $datawhere = "s.company_id='{$request['company_id']}' and s.student_id in (SELECT e.student_id FROM smc_student_enrolled as e WHERE e.school_id='{$request['school_id']}')";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= "l.school_id='{$request['school_id']}'";
        }

        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch
                ,(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_balance
FROM smc_student as s WHERE {$datawhere} HAVING  student_balance>0 ORDER BY s.student_id desc";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_balance'] = $dateexcelvar['student_balance'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员ID", "学员中文名", "学员英文名", "学员编号", "账户余额"));
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'student_balance');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}学员账户余额明细报表{$endqueryday}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select s.student_id,(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_balance
from smc_student as s where {$datawhere} HAVING  student_balance>0 ORDER BY s.student_id");
                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;
            return $data;
        }
    }


    function studentUnpaid($request)
    {
        $datawhere = "t.student_id = s.student_id and t.course_id = c.course_id and s.company_id='{$request['company_id']}' and s.student_id in (SELECT e.student_id FROM smc_student_enrolled as e WHERE e.school_id='{$request['school_id']}')";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= "t.school_id='{$request['school_id']}'";
        }
        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $startqueryday = $request['starttime'];
        } else {
            $startqueryday = date("Y-m-d", time());
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' and l.balancelog_class='0' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as balance,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' and l.balancelog_class='2' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_withholdbalance,
(SELECT l.log_finalamount FROM smc_student_coursebalance_log as l
WHERE l.log_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$request['school_id']}' and l.log_class='0' and l.log_playname<>'班级课程余额占位' ORDER BY l.log_time DESC,l.log_id DESC limit 0,1) as coursebalance_figure
,(SELECT l.timelog_finaltimes FROM smc_student_coursebalance_timelog as l
WHERE l.timelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$request['school_id']}' and l.timelog_playname<>'班级课程余额占位' ORDER BY l.timelog_time DESC,l.timelog_id DESC limit 0,1) as coursebalance_time,
c.course_id,c.course_cnname,c.course_branch
from smc_student as s,smc_student_coursebalance_timelog as t,smc_course as c
WHERE {$datawhere} GROUP BY t.student_id,t.course_id
            HAVING (coursebalance_figure>0 or coursebalance_time>0)
            ORDER BY s.student_id desc
            ";
//var_dump($sql);exit;

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            foreach ($dateexcelarray as &$one) {
                $one['student_balance'] = $one['balance'] + $one['student_withholdbalance'];
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_id'] = $dateexcelvar['student_id'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['student_balance'] = $dateexcelvar['student_balance'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学员ID", "学员中文名", "学员英文名", "学员编号", "课程别名称", "课程别编号", "课程余额", "剩余课程", "账户余额"));
            $excelfileds = array('student_id', 'student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'coursebalance_figure', 'coursebalance_time', 'student_balance');
            $tem_name = $schoolOne['school_cnname'] . '学员课程余额明细报表' . $endqueryday . '.xls';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $studentList = $this->DataControl->selectClear($sql);

            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            foreach ($studentList as &$one) {
                $one['student_balance'] = $one['balance'] + $one['student_withholdbalance'];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select s.student_id,s.student_cnname,s.student_enname,s.student_branch,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' and l.balancelog_class='0' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_balance,
(SELECT l.balancelog_finalamount FROM smc_student_balancelog as l
WHERE l.balancelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.school_id = '{$request['school_id']}' and l.balancelog_class='2' ORDER BY l.balancelog_time DESC,l.balancelog_id DESC limit 0,1) as student_withholdbalance,
(SELECT l.log_finalamount FROM smc_student_coursebalance_log as l
WHERE l.log_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$request['school_id']}' and l.log_class='0' and l.log_playname<>'班级课程余额占位' ORDER BY l.log_time DESC,l.log_id DESC limit 0,1) as coursebalance_figure
,(SELECT l.timelog_finaltimes FROM smc_student_coursebalance_timelog as l
WHERE l.timelog_time < '{$endqueryTimes}' and l.student_id = s.student_id and l.course_id = c.course_id and l.school_id = '{$request['school_id']}' and l.log_playname<>'班级课程余额占位' ORDER BY l.timelog_time DESC,l.timelog_id DESC limit 0,1) as coursebalance_time,
c.course_id,c.course_cnname,c.course_branch
from smc_student as s,smc_student_coursebalance_timelog as t,smc_course as c
WHERE {$datawhere} GROUP BY t.student_id,t.course_id
            HAVING (coursebalance_figure>0 or coursebalance_time>0)
            ORDER BY s.student_id desc");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;

            return $data;
        }
    }

    function classInfoList($request)
    {

        $today = date("Y-m-d");
        $time = date("Y-m-d", time());
        if (isset($request['starttime']) && $request['starttime'] !== '') {

            $time = $request['starttime'];
        }
        $week_start_day = date("Y-m-d", strtotime($time));
        $week_end_day = date("Y-m-d", strtotime("$week_start_day  +  6 days"));
        $datawhere = "1";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= "c.school_id='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.class_cnname,c.class_enname,c.class_branch,s.student_cnname
              from smc_student_study as ss
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_student as s on s.student_id=ss.student_id
              where {$datawhere} and ss.company_id='{$request['company_id']}'
               and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' and ss.study_endday>='{$week_end_day}'
              order by ss.class_id desc
        ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("班级名称", '班级别名', "班级编号", "学员名称"));
            $excelfileds = array('class_cnname', 'class_enname', 'class_branch', 'student_cnname');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}班级学员信息报表{$week_start_day}-{$week_end_day}.xlsx"));
            exit;


        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);

            if (!$classList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if (isset($request['is_count']) && $request['is_count'] == 1) {

                $count_sql = "select c.class_cnname,c.class_enname,c.class_branch,s.student_cnname
              from smc_student_study as ss
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_student as s on s.student_id=ss.student_id
             where {$datawhere} and ss.company_id='{$request['company_id']}'
               and c.class_stdate <= '{$week_end_day}' and c.class_enddate >='{$week_start_day}' and ss.study_endday>='{$week_end_day}'";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;

            }
            $data['list'] = $classList;

            return $data;
        }

    }


    function stuOrderReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or sf.family_mobile like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sf.family_cnname like '%{$request['keyword']}%' or po.order_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $endtime = strtotime($request['fixedtime'] . ' 23:59:59');
            $datawhere .= " and po.order_createtime <= '{$endtime}'";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= "po.school_id='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select po.order_pid,s.student_branch,s.student_cnname,s.student_enname,sf.family_cnname,po.order_paymentprice,po.order_status,po.order_from,po.order_type,po.order_createtime
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_student_enrolled as se on se.student_id=s.student_id
              where {$datawhere} and po.company_id='{$request['company_id']}'  and po.order_status<>'-1' and se.enrolled_status<>'-1'
              order by po.order_id desc
              ";
        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统导入"));
        $order_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['order_status'] = $order_status[$val['order_status']];
                $val['order_from'] = $order_from[$val['order_from']];
                $val['order_type'] = $order_type[$val['order_type']];
                $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
            }

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['order_status'] = $dateexcelvar['order_status'];
                    $datearray['order_from'] = $dateexcelvar['order_from'];
                    $datearray['order_type'] = $dateexcelvar['order_type'];
                    $datearray['order_createtime'] = $dateexcelvar['order_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("订单编号", "学员编号", "学员中文名", "学员英文名", "家长姓名", "订单金额", "订单状态", "订单来源", "收费类型", "下单日期"));
            $excelfileds = array('order_pid', 'student_branch', 'student_cnname', 'student_enname', 'family_cnname', 'order_paymentprice', 'order_status', 'order_from', 'order_type', 'order_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}收费日记账报表{$request['fixedtime']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $orderList = $this->DataControl->selectClear($sql);
            if (!$orderList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            $data = array();
            foreach ($orderList as &$val) {
                $val['order_status'] = $order_status[$val['order_status']];
                $val['order_from'] = $order_from[$val['order_from']];
                $val['order_type'] = $order_type[$val['order_type']];
                $val['order_createtime'] = date("Y-m-d H:i:s", $val['order_createtime']);
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select po.order_id
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_student_enrolled as se on se.student_id=s.student_id
              where {$datawhere} and po.company_id='{$request['company_id']}'  and po.order_status<>'-1' and se.enrolled_status<>'-1'";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $orderList;

            return $data;
        }
    }

    function orderUnpaidReport($request)
    {
        $datawhere = "A.company_id = '{$request['company_id']}' and A.order_arrearageprice > 0 and A.order_status > '0'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (B.student_cnname like '%{$request['keyword']}%' or B.student_enname like '%{$request['keyword']}%'
            or B.student_idcard like '%{$request['keyword']}%' or B.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
            $datawhere .= " and A.order_createtime <='{$endtime}'";
        } else {
            $endtime = time();
            $datawhere .= " and A.order_createtime <='{$endtime}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . ' 0:00:00');
        } else {
            $starttime = 0;
        }
        $datawhere .= " and A.order_createtime >= '{$starttime}'";

        $wherecondition = "";
        $havingcondition = " having 1 ";

        if (isset($request['pay_type']) && $request['pay_type'] !== '') {
            switch ($request['pay_type']) {
                case "0":
                    $wherecondition .= " ifnull((select sum(ordercourse_totalprice) from smc_payfee_order_course where order_pid=A.order_pid) ,0) as ordercourse_totalprice,
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='0'),0) as ordercourse_paidprice,";
                    $havingcondition .= " and ordercourse_totalprice-ordercourse_paidprice>0 ";
                    break;
                case "1":
                    $wherecondition .= " ifnull((select sum(ordergoods_totalprice) from smc_payfee_order_goods where order_pid=A.order_pid) ,0) as ordergoods_totalprice,
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='1'),0) as ordergoods_paidprice,";
                    $havingcondition .= " and ordergoods_totalprice-ordergoods_paidprice>0 ";
                    break;
                case "2":
                    $wherecondition .= " ifnull((select sum(item_totalprice) from smc_payfee_order_item where order_pid=A.order_pid) ,0) as orderitem_totalprice,
                     ifnull((select sum(pay_price) from smc_payfee_order_pay where order_pid=A.order_pid and pay_issuccess='1' and pay_type='2'),0) as orderitem_paidprice,";
                    $havingcondition .= " and orderitem_totalprice-orderitem_paidprice>0 ";
                    break;
                default:
                    break;
            }
        }

        $wherecondition .= " A.order_createtime,A.order_note ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select  C.school_id,C.school_branch,C.school_cnname,C.school_enname,
 B.student_id,B.student_branch,B.student_cnname,B.student_enname,
 A.order_pid,A.order_from,A.order_type,A.order_status,
 A.order_allprice,A.order_coupon_price,A.order_market_price,
 A.order_paymentprice,A.order_paidprice,A.order_arrearageprice,
 {$wherecondition}
 from smc_payfee_order A
 left join smc_student B on A.student_id=B.student_id and A.company_id=B.company_id
 left join smc_school C on A.school_id=C.school_id and A.company_id=C.company_id
 where {$datawhere}
 {$havingcondition}
 order by A.order_createtime desc";

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $order_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $order_status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待支付", "2" => "支付中", "3" => "处理中", "4" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无订单欠费数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_enname'] = $dateexcelvar['school_enname'];

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];

                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['order_from'] = $order_from[$dateexcelvar['order_from']];
                    $datearray['order_type'] = $order_type[$dateexcelvar['order_type']];
                    $datearray['order_status'] = $order_status[$dateexcelvar['order_status']];
                    $datearray['order_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['order_createtime']);
                    $datearray['order_note'] = $dateexcelvar['order_note'];

                    $datearray['order_allprice'] = $dateexcelvar['order_allprice'];
                    $datearray['order_coupon_price'] = $dateexcelvar['order_coupon_price'];
                    $datearray['order_market_price'] = $dateexcelvar['order_market_price'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                    $datearray['order_arrearageprice'] = $dateexcelvar['order_arrearageprice'];

                    if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                        switch ($request['pay_type']) {
                            case "0":
                                $datearray['ordercourse_totalprice'] = $dateexcelvar['ordercourse_totalprice'];
                                $datearray['ordercourse_paidprice'] = $dateexcelvar['ordercourse_paidprice'];
                                $datearray['ordercourse_ownprice'] = $dateexcelvar['ordercourse_totalprice'] - $dateexcelvar['ordercourse_paidprice'];
                                break;
                            case "1":
                                $datearray['ordergoods_totalprice'] = $dateexcelvar['ordergoods_totalprice'];
                                $datearray['ordergoods_paidprice'] = $dateexcelvar['ordergoods_paidprice'];
                                $datearray['ordergoods_ownprice'] = $dateexcelvar['ordergoods_totalprice'] - $dateexcelvar['ordergoods_paidprice'];
                                break;
                            case "2":
                                $datearray['orderitem_totalprice'] = $dateexcelvar['orderitem_totalprice'];
                                $datearray['orderitem_paidprice'] = $dateexcelvar['orderitem_paidprice'];
                                $datearray['orderitem_ownprice'] = $dateexcelvar['orderitem_totalprice'] - $dateexcelvar['orderitem_paidprice'];
                                break;
                            default:
                                break;
                        }
                    }

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");


            if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                switch ($request['pay_type']) {
                    case "0":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '课程应收金额', '课程已付金额', '课程欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'ordercourse_totalprice', 'ordercourse_paidprice', 'ordercourse_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费明细表-课程.xlsx");
                        break;
                    case "1":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '教材应收金额', '教材已付金额', '教材欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'ordergoods_totalprice', 'ordergoods_paidprice', 'ordergoods_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费明细表-教材.xlsx");
                        break;
                    case "2":
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额', '杂费应收金额', '杂费已付金额', '杂费欠费金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'orderitem_totalprice', 'orderitem_paidprice', 'orderitem_ownprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费明细表-杂费.xlsx");
                        break;
                    default:
                        $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额'));
                        $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice');
                        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费总表.xlsx");
                        break;
                }
            } else {
                $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", '检索代码', "学员编号", "学员中文名", "学员英文名", '订单编号', '订单来源', '订单类别', '订单状态', '下单时间', '订单备注', '订单总金额', '优惠券抵扣金额', '营销活动抵扣金额', '订单应付总金额', '订单已付总金额', '订单欠费总金额'));
                $excelfileds = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'student_branch', 'student_cnname', 'student_enname', 'order_pid', 'order_from', 'order_type', 'order_status', 'order_createtime', 'order_note', 'order_allprice', 'order_coupon_price', 'order_market_price', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice');
                query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}订单欠费总表.xlsx");
            }
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $unpaidList = $this->DataControl->selectClear($sql);
            if (!$unpaidList) {
                $this->error = true;
                $this->errortip = "无订单欠费数据";
                return false;
            }

            foreach ($unpaidList as &$var) {
                $var['order_from'] = $order_from[$var['order_from']];
                $var['order_type'] = $order_type[$var['order_type']];
                $var['order_status'] = $order_status[$var['order_status']];
                $var['order_createtime'] = date("Y-m-d H:i:s", $var['order_createtime']);

                if (isset($request['pay_type']) && $request['pay_type'] !== '') {
                    switch ($request['pay_type']) {
                        case "0":
                            $var['ordercourse_ownprice'] = $var['ordercourse_totalprice'] - $var['ordercourse_paidprice'];
                            break;
                        case "1":
                            $var['ordergoods_ownprice'] = $var['ordergoods_totalprice'] - $var['ordergoods_paidprice'];
                            break;
                        case "2":
                            $var['orderitem_ownprice'] = $var['orderitem_totalprice'] - $var['orderitem_paidprice'];
                            break;
                        default:
                            break;
                    }
                }
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.order_pid,{$wherecondition} from smc_payfee_order A
 left join smc_student B on A.student_id=B.student_id and A.company_id=B.company_id
 where {$datawhere} {$havingcondition} order by A.order_createtime desc";
                $dbCount = $this->DataControl->selectClear($count_sql);
                if ($dbCount) {
                    $allnum = count($dbCount);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $unpaidList;

            return $data;
        }
    }

    function stuRefundReport($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or sf.family_mobile like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or sf.family_cnname like '%{$request['keyword']}%' or ro.refund_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and ro.school_id='{$request['school_id']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and ro.refund_createtime <= '{$endtime}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and ro.refund_createtime >= '{$starttime}'";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ro.refund_pid,s.student_branch,s.student_cnname,s.student_enname,sf.family_cnname,ro.refund_price,ro.refund_status,ro.refund_from,ro.refund_type,ro.refund_createtime
              ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=ro.from_order_pid and pop.pay_issuccess='1' and pop.pay_isrefund='0' and pop.paytype_code<>'forward') as balance
              from smc_refund_order as ro
              left join smc_student as s on s.student_id=ro.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_student_enrolled as se on se.student_id=s.student_id
              where {$datawhere} and ro.company_id='{$request['company_id']}'  and se.enrolled_status<>'-1'
              order by ro.refund_id desc
              ";
        $refund_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统导入"));
        $refund_type = $this->LgArraySwitch(array("0" => "银行转账", "1" => "原路返还"));
        $refund_status = $this->LgArraySwitch(array("0" => "申请", "1" => "审核通过", "2" => "处理中", "3" => "确定金额", "4" => "完成退款"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            foreach ($dateexcelarray as &$val) {
                $val['refund_status'] = $refund_status[$val['refund_status']];
                $val['refund_from'] = $refund_from[$val['refund_from']];
                $val['refund_type'] = $refund_type[$val['refund_type']];
                if ($val['balance'] == 'null') {
                    $val['balance'] = 0;
                }
                $val['refund_createtime'] = date("Y-m-d H:i:s", $val['refund_createtime']);
            }

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['refund_pid'] = $dateexcelvar['refund_pid'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    $datearray['refund_price'] = $dateexcelvar['refund_price'];
                    $datearray['balance'] = $dateexcelvar['balance'];
                    $datearray['refund_status'] = $dateexcelvar['refund_status'];
                    $datearray['refund_from'] = $dateexcelvar['refund_from'];
                    $datearray['refund_createtime'] = $dateexcelvar['refund_createtime'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("订单编号", "学员编号", "学员中文名", "学员英文名", "家长姓名", "退费金额总计", '退账户余额', "订单状态", "订单来源", "退费日期"));
            $excelfileds = array('refund_pid', 'student_branch', 'student_cnname', 'student_enname', 'family_cnname', 'refund_price', 'balance', 'refund_status', 'refund_from', 'refund_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$schoolOne['school_cnname']}退费日记账报表{$request['fixedtime']}.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            $data = array();
            foreach ($refundList as &$val) {
                $val['refund_status'] = $refund_status[$val['refund_status']];
                $val['refund_from'] = $refund_from[$val['refund_from']];
                $val['refund_type'] = $refund_type[$val['refund_type']];
                $val['refund_createtime'] = date("Y-m-d H:i:s", $val['refund_createtime']);
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select ro.refund_id
              from smc_refund_order as ro
              left join smc_student as s on s.student_id=ro.student_id
              left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
              left join smc_student_enrolled as se on se.student_id=s.student_id
              where {$datawhere} and ro.company_id='{$request['company_id']}' and se.enrolled_status<>'-1'";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }
    }

    function teaTeachClass($request)
    {
        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        $sql = "
              SELECT
                    c.class_cnname,
                    c.class_enname,
                    c.class_branch,
                    co.course_cnname,
                    t.teach_type,
                    c.class_status,
                    c.class_hournums
                FROM
                    smc_class_teach AS t
                    LEFT JOIN smc_class AS c ON t.class_id = c.class_id 
                    left join smc_course as co on co.course_id = c.course_id
                WHERE
                    {$datawhere} and t.staffer_id = '{$request['worker_id']}'";
        $refundList = $this->DataControl->selectClear($sql);

        if ($refundList) {
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束', '-2' => '已删除'));
            $statuss = $this->LgArraySwitch(array('0' => '主教', '1' => '助教'));
            foreach ($refundList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
                $val['teach_type'] = $statuss[$val['teach_type']];
            }
        }


        $data['list'] = $refundList;
        if (!$refundList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        return $data;
    }

    function stuAdvanceReport($request)
    {

        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or scb.coursecat_branch like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or scb.feetype_code like '%{$request['keyword']}%' or cf.feetype_name like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and scb.school_id='{$request['school_id']}'";
        }
//        if(isset($request['endtime']) && $request['endtime'] !== ''){
//            $endtime=strtotime($request['endtime'].' 23:59:59');
//            $datawhere .= " and ro.refund_createtime <= '{$endtime}'";
//        }
//
//        if(isset($request['starttime']) && $request['starttime'] !== ''){
//            $starttime=strtotime($request['starttime']);
//            $datawhere .= " and ro.refund_createtime >= '{$starttime}'";
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_branch, s.student_cnname, s.student_enname,scb.*,cf.feetype_name
              from smc_student_coursecatbalance as scb
              left join smc_student as s on s.student_id=scb.student_id
              left join smc_code_feetype as cf on cf.feetype_code=scb.feetype_code
              where {$datawhere}  and scb.company_id='{$request['company_id']}'
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无预收余额";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['feetype_name'] = $dateexcelvar['feetype_name'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['coursecatbalance_month'] = $dateexcelvar['coursecatbalance_month'];
                    $datearray['coursecatbalance_figure'] = $dateexcelvar['coursecatbalance_figure'];
                    $datearray['coursecatbalance_time'] = $dateexcelvar['coursecatbalance_time'];
                    $datearray['coursecatbalance_unitexpend'] = $dateexcelvar['coursecatbalance_unitexpend'];
                    $datearray['coursecatbalance_unitrefund'] = $dateexcelvar['coursecatbalance_unitrefund'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", "收费类型", "班种", '管理费月份', "课程剩余余额", "课程剩余次数", "消耗单价", "退费单价"));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'feetype_name', 'coursecat_branch', 'coursecatbalance_month', 'coursecatbalance_figure', 'coursecatbalance_time', 'coursecatbalance_unitexpend', 'coursecatbalance_unitrefund');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}学员预收余额表.xlsx");
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $refundList = $this->DataControl->selectClear($sql);
            if (!$refundList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select scb.coursecatbalance_id
              from smc_student_coursecatbalance as scb
              left join smc_student as s on s.student_id=scb.student_id
              left join smc_code_feetype as cf on cf.feetype_code=scb.feetype_code
              where {$datawhere} and scb.school_id='{$request['student_id']}' and scb.company_id='{$request['company_id']}'";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $refundList;

            return $data;
        }
    }


    function stuEnrolledReport($request)
    {

        $where = "1";
        $where = "sc.school_id ='{$request['school_id']}' ";
        if (isset($request['starttime']) && $request['starttime']) {
            $where .= " and  sc.changelog_day >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $where .= " and  sc.endtime <='{$request['endtime']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $where .= " and se.school_id='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword']) {
            $where .= " and  (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select s.student_cnname,s.student_enname,s.student_branch,
				(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code,
				(select sc.changelog_day from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  changelog_day,
				(select sc.changelog_note from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  changelog_note
			   from smc_student  as s

			  left join smc_student_enrolled as  se ON  se.student_id = s.student_id
			  where  s.company_id = '{$request['company_id']}'
			  Having  stuchange_code IN ('C02','B05') ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无学院流失记录";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_note'];
                    $datearray['changelog_day'] = $dateexcelvar['stuchange_code'];

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", '异动代码', '异动备注', '异动日期'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'stuchange_code', 'changelog_note', 'changelog_day');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}学员异动学员表.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";
            $changeList = $this->DataControl->selectClear($sql);

            $num = 0;
            if ($request['is_count'] && $request['is_count'] == '1') {
                $sql = "select s.student_cnname,
				(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id and  sc.stuchange_code IN ('A01','A06','C02','B05')  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code

			   from smc_student  as s

			  left join smc_student_enrolled as  se ON  se.student_id = s.student_id
			  where  s.company_id = '{$request['company_id']}'
			  Having  stuchange_code IN ('C02','B05')  ";
                $countList = $this->DataControl->selectClear($sql);


                $num = count($countList);

            }


            $data['allnum'] = $num + 0;


            $data['list'] = $changeList == false ? array() : $changeList;
            return $data;
        }
    }

    function stuEnrolledClass($request)
    {

        $where = "1";
        $where = "s.company_id ='{$request['company_id']}' ";
        if (isset($request['starttime']) && $request['starttime']) {
            $where .= " and  sc.changelog_day >='{$request['starttime']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $where .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $where .= " and  sc.endtime <='{$request['endtime']}'";
        }

        if (isset($request['keyword']) && $request['keyword']) {
            $where .= " and  (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select  s.student_id,s.student_cnname,s.student_enname,s.student_branch,
 				(select  log_finalamount from smc_student_coursebalance_log as sc where sc.student_id =s.student_id and log_class =1  and  {$where} order by log_time DESC  limit 0,1 ) as  log_finalamount1,
		   		(select log_finalamount from smc_student_coursebalance_log as sc where sc.student_id =s.student_id  and log_class =0  and  {$where}  order by log_time DESC  limit 0,1 ) as  log_finalamount2,
		   		(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code
			  from smc_student as s

			  left join smc_student_enrolled as  sc ON  sc.student_id = s.student_id
			  where   {$where}    Having  (log_finalamount1 + log_finalamount2 + 0) > 0 and stuchange_code  NOT IN ('A02','A03','A04','A05')   ";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无学院流失记录";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['log_finalamount2'] = $dateexcelvar['log_finalamount2'];

                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", '课程余额'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'log_finalamount2');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}学员待入班表.xlsx");
            exit;
        } else {

            $sql .= "limit {$pagestart},{$num} ";
            $changeList = $this->DataControl->selectClear($sql);


            $data['allnum'] = 0;
            if ($request['is_count'] && $request['is_count'] == '1') {
                $all_num = $this->DataControl->selectClear("select
 				(select  log_finalamount from smc_student_coursebalance_log as sc where sc.student_id =s.student_id and log_class =1  and  {$where} order by log_time DESC  limit 0,1 ) as  log_finalamount1,
		   		(select log_finalamount from smc_student_coursebalance_log as sc where sc.student_id =s.student_id  and log_class =0  and  {$where}  order by log_time DESC  limit 0,1 ) as  log_finalamount2,
		   		(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code
			  from smc_student as s

			  left join smc_student_enrolled as  sc ON  sc.student_id = s.student_id
			  where   {$where}    Having  (log_finalamount1 + log_finalamount2 + 0) > 0 and stuchange_code  NOT IN ('A02','A03','A04','A05')
			  ");


                $data['allnum'] = count($all_num) + 0;
            }

            $data['list'] = $changeList == false ? array() : $changeList;
            return $data;
        }

    }


    function stuWaitClass($request)
    {

        $where = "s.company_id ='{$request['company_id']}' ";
        if (isset($request['starttime']) && $request['starttime']) {
            $where .= " and  sc.changelog_day >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $where .= " and  sc.endtime <='{$request['endtime']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $where .= " and se.school_id='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword']) {
            $where .= " and  (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select  s.student_id,s.student_cnname,s.student_enname,s.student_branch,
 			  	(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code
			  from smc_student as s
			  left join smc_student_enrolled as  sc ON  sc.student_id = s.student_id
			  where   {$where}    Having stuchange_code ='A07'  ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无学院流失记录";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $excelheader = $this->LgArraySwitch(array("学员编号", "学员中文名", "学员英文名", '异动日期', '异动原因'));
            $excelfileds = array('student_branch', 'student_cnname', 'student_enname', 'changelog_day', 'changelog_note');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$schoolOne['school_cnname']}学员延班报表.xlsx");
            exit;
        } else {

            $sql .= " limit {$pagestart},{$num} ";
            $changeList = $this->DataControl->selectClear($sql);
            $data['allnum'] = 0;
            if ($request['is_count'] && $request['is_count'] == '1') {
                $all_num = $this->DataControl->selectClear("select  s.student_id,s.student_cnname,s.student_enname,s.student_branch,
 			  	(select sc.stuchange_code from smc_student_changelog as sc where sc.student_id = s.student_id  and {$where} order by sc.changelog_id DESC limit 0,1) as  stuchange_code
			  from smc_student as s
			  left join smc_student_enrolled as  sc ON  sc.student_id = s.student_id
			  where   {$where}    Having stuchange_code ='A07' 
			  ");
                $data['allnum'] = count($all_num) + 0;
            }
            $data['list'] = $changeList == false ? array() : $changeList;
            return $data;
        }
    }

    //班级结算学员收入明细报表
    function classEndStudentIncome()
    {
        $sql = "select event_id as student_id, event_id as school_cnname, event_id as class_cnname, event_id as student_branch, event_id as duration, event_id as weekdays, event_id as student_branch, event_id as student_cnname, event_id as student_enname, event_id as status, event_id as study_beginday, event_id as charge_times, event_id as charge_price, event_id as leave_times, event_id as settlement_times, event_id as settlement_price, event_id as income_times, event_id as income_price, event_id as notyet_times, event_id as notyet_price from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //在籍新生统计表报表
    function isResidenceStudent()
    {
        $sql = "select event_id as student_id, event_id as school_cnname, event_id as student_branch, event_id as student_cnname, event_id as coursetype_cnname, event_id as coursecat_cnname, event_id as family_relation, event_id as family_mobile, event_id as order_createtime, event_id as order_paymentprice, event_id as pay_typename from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //班级实际欠费报表
    function classArrear()
    {
        $sql = "select event_id as student_id, event_id as student_branch, event_id as student_cnname, event_id as class_cnname, event_id as pay_type, event_id as pay_price, event_id as ordercourse_buynums, event_id as class_stdate, event_id as count1, event_id as price1, event_id as price2, event_id as price3, event_id as count2, event_id as date1, event_id as date2, event_id as count3, event_id as count4, event_id as count5, event_id as status from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //班级实际欠费报表
    function readingStudent()
    {
        $sql = "select event_id as student_id, event_id as school_cnname, event_id as date1, event_id as date2, event_id as date3, event_id as date4, event_id as count1, event_id as student_branch, event_id as student_cnname, event_id as student_from, event_id as coursetype_cnname, event_id as class_cnname, event_id as book_price, event_id as platform, event_id as price1, event_id as count2, event_id as price2, event_id as count3, event_id as discount_explain, event_id as remark from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //学员班级流失报表
    function studentClassOff($request)
    {
        $datawhere = " 1 ";
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and scl.changelog_day<='{$request['end_time']}'";
        }
        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and scl.changelog_day>='{$request['start_time']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] != '') {
            $datawhere .= " and c.course_id='{$request['course_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] != '') {
            $datawhere .= " and sc.coursecat_id='{$request['coursecat_id']}'";
        }

        if ($request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND scl.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and scl.school_id='{$request['school_id']}'";
        }


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,scl.changelog_day,scl.changelog_note,scl.staffer_id,scl.changelog_createtime,ch.change_reason,cc.coursecat_cnname,cc.coursecat_branch,sc.course_branch,sch.school_cnname,sch.school_branch
              from smc_student_changelog as scl
              left join smc_student_change as ch on ch.change_pid=scl.change_pid
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_school as sch on sch.school_id=scl.school_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where {$datawhere} and scl.stuchange_code='B01' and scl.company_id='{$request['company_id']}'
              order by scl.changelog_day desc,scl.changelog_createtime desc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['change_reason'] = $dateexcelvar['change_reason'];
                    $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$dateexcelvar['staffer_id']}'");
                    $datearray['staffer_cnname'] = $stafferOne['staffer_cnname'];
                    $datearray['changelog_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['changelog_createtime']);;
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "学员中文名", "学员英文名", "学员编号", "班级名称", "班级别名", "班级编号", "班种编号", "课程别编号", "异动日期", "异动原因", "操作人", "操作时间"));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'coursecat_branch', 'course_branch', 'changelog_day', 'change_reason', 'staffer_cnname', 'changelog_createtime');
            $tem_name = $this->LgStringSwitch('学员班级流失明细报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            foreach ($list as &$one) {

                $reasonOne = $this->DataControl->getFieldOne("smc_code_stuchange_reason", "reason_note", "company_id='{$request['company_id']}' and reason_code='{$one['reason_code']}'");
                $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$one['staffer_id']}'");
                $datearray['staffer_cnname'] = $stafferOne['staffer_cnname'];

                $one['changelog_createtime'] = date("Y-m-d H:i:s", $one['changelog_createtime']);
            }
            $data = array();
            $count_sql = "select s.student_id
              from smc_student_changelog as scl
              left join smc_student_change as ch on ch.change_pid=scl.change_pid
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_school as sch on sch.school_id=scl.school_id
              left join smc_code_coursecat as cc on cc.coursecat_id=sc.coursecat_id
              where {$datawhere} and scl.stuchange_code='B01' and scl.company_id='{$request['company_id']}'
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }

    }

    //分校流失报表
    function statisticsSchoolOff($request)
    {
        $datawhere = "s.company_id='{$request['company_id']}'";

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and s.changelog_day<='{$request['end_time']}'";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and s.changelog_day>='{$request['start_time']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_tagbak, s.school_cnname,s.school_id, s.school_branch, COUNT(s.student_id) as offnums,
                    COUNT(
                        CASE
                        WHEN s.connect_times <> '' THEN
                            s.connect_times
                        END
                    ) AS trackingnums
                FROM view_smc_student_offlist AS s WHERE {$datawhere} 
              and s.stuchange_code<>'C04'GROUP BY s.school_branch ORDER BY s.school_sort ASC";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['offnums'] = $dateexcelvar['offnums'];
                    $datearray['trackingnums'] = $dateexcelvar['trackingnums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "分校流失总数", "流失电访数"));
            $excelfileds = array('school_cnname', 'school_branch', 'offnums', 'trackingnums');
            $tem_name = $this->LgStringSwitch('分校流失统计报表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            $data = array();
            $count_sql = "select s.school_branch
              FROM view_smc_student_offlist AS s WHERE {$datawhere}
              and s.stuchange_code<>'C04'
              group by s.school_branch
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $list;

            return $data;
        }
    }

    //校园收入统计报表
    function schoolIncome($request)
    {

        $datawhere = " 1 ";

        $datawhere .= " and B.company_id='{$request['company_id']}'";

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['income_type']) && $request['income_type'] !== '') {
            $datawhere .= " and A.income_type='{$request['income_type']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $endtime = strtotime($request['end_time'] . ' 23:59:59');
        } else {
            $endtime = strtotime(date("Y-m-d") . ' 23:59:59');
        }
        $datawhere .= " AND A.income_confirmtime <= '{$endtime}'";

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $starttime = strtotime($request['start_time'] . '0:00:00');
        } else {
            $starttime = strtotime(date('Y-m-01', strtotime(date("Y-m-d"))) . '0:00:00');
        }
        $datawhere .= " AND  A.income_confirmtime >= '{$starttime}'";


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.school_id,B.school_branch,B.school_cnname
        ,DATE_FORMAT(FROM_UNIXTIME(A.income_confirmtime),'%Y-%m') AS income_month
        ,A.income_type
        ,count(A.income_id) AS income_total_count
        ,count(distinct student_id) AS income_student_count
        ,sum(A.income_price) AS income_total_price
        FROM smc_school_income A 
        LEFT JOIN smc_school B ON A.school_id=B.school_id AND A.company_id=B.company_id
        WHERE {$datawhere}
		GROUP BY A.school_id,B.school_branch,B.school_cnname,A.income_type,income_month
		order BY B.school_branch,income_month,A.income_type
		";


        $income_type = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "收入红冲"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $schoolOne = $this->DataControl->getFieldOne('smc_school', "school_cnname", "school_id='{$request['school_id']}'");
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_id'] = $dateexcelvar['school_id'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['income_month'] = $dateexcelvar['income_month'];
                    $datearray['income_type'] = $income_type[$dateexcelvar['income_type']];
                    $datearray['income_total_count'] = $dateexcelvar['income_total_count'];
                    $datearray['income_student_count'] = $dateexcelvar['income_student_count'];
                    $datearray['income_total_price'] = $dateexcelvar['income_total_price'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学校ID", "校区编号", "校区名称", "收入月份", "收入类型", "收入单数", "涉及学员人数", "收入总计"));
            $excelfileds = array('school_id', 'school_branch', 'school_cnname', "income_month", 'income_type', "income_total_count", "income_student_count", 'income_total_price');

            $tem_name = $schoolOne['school_cnname'] . $this->LgStringSwitch('校园收入统计表') . $request['start_time'] . '~' . $request['end_time'] . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $IncomeList = $this->DataControl->selectClear($sql);

            if (!$IncomeList) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            foreach ($IncomeList as &$var) {
                $var['income_type'] = $income_type[$var['income_type']];
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select A.school_id,A.income_type
        ,DATE_FORMAT(FROM_UNIXTIME(A.income_confirmtime),'%Y-%m') AS income_month
        FROM smc_school_income A 
        LEFT JOIN smc_school B ON A.school_id=B.school_id AND A.company_id=B.company_id
        WHERE {$datawhere}
		GROUP BY A.school_id,B.school_branch,B.school_cnname,A.income_type,income_month
		");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $IncomeList;

            return $data;
        }
    }

    //班级上课报表
    function classCourse()
    {
        $sql = "select event_id as school_cnname,event_id as school_branch,event_id as class_cnname, event_id as class_enname, event_id as class_branch, event_id as course_cnname, event_id as course_branch , event_id as date1, event_id as main_teacher, event_id as assistant_teacher , event_id as classroom_cnname , event_id as classroom_cnname, event_id as count1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //教师上课报表
    function teacherCourse()
    {
        $sql = "select event_id as school_cnname,event_id as school_branch,event_id as staffer_cnname, event_id as staffer_enname, event_id as staffer_branch, event_id as type1, event_id as date1 , event_id as class_cnname, event_id as class_enname, event_id as class_branch from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //学员考勤报表
    function studentCheck()
    {
        $sql = "select event_id as school_cnname,event_id as school_branch,event_id as staffer_cnname, event_id as staffer_enname, event_id as staffer_branch, event_id as type1, event_id as date1 , event_id as status, event_id as reason from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //借调相关报表
    function transferGoods()
    {
        $sql = "select event_id as school_cnname,event_id as school_branch,event_id as to_school_cnname, event_id as from_school_cnname, event_id as proorder_pid, event_id as goods_cnname, event_id as goods_pid , event_id as prodtype_name, event_id as goods_unit, event_id as count1, event_id as remark, event_id as date1, event_id as date2, event_id as date3 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //领用相关报表
    function receiveGoods()
    {
        $sql = "select event_id as school_cnname,event_id as school_branch,event_id as proorder_pid, event_id as staffer_cnname, event_id as staffer_enname, event_id as staffer_branch, event_id as goods_cnname , event_id as goods_pid, event_id as prodtype_name, event_id as count1, event_id as count2, event_id as remark, event_id as date1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    //活动采购报表
    function activityGoods()
    {
        $sql = "select event_id as school_cnname,event_id as school_branch,event_id as activity_name, event_id as activity_branch, event_id as goods_cnname, event_id as goods_branch, event_id as prodtype_name , event_id as count1 from gmc_event";
        $List = $this->DataControl->selectClear($sql);
        $data['list'] = $List == false ? array() : $List;
        return $data;
    }

    function inspectionReport($request)
    {
        $datawhere = "sc.company_id='{$request['company_id']}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_shortname like '%{$request['keyword']}%' or sc.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND sc.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        } else {
            $datawhere .= " and sc.school_istest='0' and sc.school_isclose='0' ";
        }

        $subwhere = " ";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $subwhere .= " and x.coursetype_id='{$request['coursetype_id']}'";
        }

        $datawhere1 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere1 .= " ch.hour_day >= '{$request['start_time']}' AND ch.hour_day <= '{$request['end_time']}'";
        }

        $datawhere2 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere2 .= " FROM_UNIXTIME(fo.order_createtime,'%Y-%m-%d') >= '{$request['start_time']}' AND FROM_UNIXTIME(fo.order_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere3 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere3 .= " FROM_UNIXTIME(ca.apply_time,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(ca.apply_time,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere4 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere4 .= " FROM_UNIXTIME(fd.dealorder_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(fd.dealorder_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere5 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere5 .= " FROM_UNIXTIME(po.order_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(po.order_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere6 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere6 .= " FROM_UNIXTIME(cr.reduceorder_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(cr.reduceorder_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere7 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere7 .= " FROM_UNIXTIME(ro.refund_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(ro.refund_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere8 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere8 .= " FROM_UNIXTIME(ro.refund_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(ro.refund_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere9 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere9 .= " FROM_UNIXTIME(pop.pay_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(pop.pay_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere10 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere10 .= " FROM_UNIXTIME(x.changelog_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(x.changelog_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere11 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere11 .= " FROM_UNIXTIME(scb.coursebalance_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(scb.coursebalance_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere12 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere12 .= " FROM_UNIXTIME(se.enrolled_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(se.enrolled_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }
        $datawhere13 = " ";
        if (isset($request['start_time']) && $request['end_time']) {
            $datawhere13 .= " FROM_UNIXTIME(c.class_createtime,'%Y-%m-%d') >= '{$request['start_time']}'
		AND FROM_UNIXTIME(c.class_createtime,'%Y-%m-%d') <= '{$request['end_time']}'";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
	sc.school_id,
	sc.school_branch,
	sc.school_cnname,
	sc.school_shortname,
	(SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = sc.school_province ) as province_name,
	(
	SELECT
		count( ch.hour_id ) 
	FROM
		smc_class_hour AS ch
		LEFT JOIN smc_class AS c ON c.class_id = ch.class_id
		LEFT JOIN smc_course AS a ON c.course_id = a.course_id
		LEFT JOIN smc_code_coursetype AS b ON a.coursetype_id = b.coursetype_id 
	WHERE
		c.school_id = sc.school_id 
		AND ch.hour_ischecking = '0' 
		AND DATEDIFF( CURDATE(), ch.hour_day )> 0 
		AND b.coursetype_isopenclass = '0' 
		AND c.class_type = '0' 
		AND {$datawhere1}
		) AS hourNum,(
	SELECT
		count( fo.order_id ) 
	FROM
		smc_freehour_order AS fo 
	WHERE
		fo.school_id = sc.school_id 
		AND fo.order_status = '0' 
		AND {$datawhere2}
		) AS freeNum,(
	SELECT
		count( ca.apply_id ) 
	FROM
		smc_student_coupons_apply AS ca 
	WHERE
		ca.school_id = sc.school_id 
		AND ca.apply_status >= '0' 
		AND ca.apply_status < '3' 
		AND {$datawhere3}
		) AS applyNum,(
	SELECT
		count( fd.dealorder_id ) 
	FROM
		smc_forward_dealorder AS fd 
	WHERE
		fd.school_id = sc.school_id 
		AND fd.dealorder_status = '0' 
		AND {$datawhere4}
		) AS dealorderNum,(
	SELECT
		count( po.order_id ) 
	FROM
		smc_payfee_order AS po 
	WHERE
		po.school_id = sc.school_id 
		AND po.order_status = '0' 
		AND {$datawhere5}
		) AS orderNum,(
	SELECT
		count( cr.reduceorder_id ) 
	FROM
		smc_course_reduceorder AS cr 
	WHERE
		cr.school_id = sc.school_id 
		AND cr.reduceorder_status = '0' 
		AND {$datawhere6}
		) AS reduceorderNum,(
	SELECT
		count( ro.refund_id ) 
	FROM
		smc_refund_order AS ro 
	WHERE
		ro.school_id = sc.school_id 
		AND ro.refund_status = '0' 
		AND {$datawhere7}
		) AS schoolRefundNum,(
	SELECT
		count( ro.refund_id ) 
	FROM
		smc_refund_order AS ro 
	WHERE
		ro.school_id = sc.school_id 
		AND ro.refund_status > '0' 
		AND ro.refund_status < '4' 
		AND {$datawhere8}
		) AS refundNum,(
	SELECT
		count( pop.pay_id ) 
	FROM
		smc_payfee_order_pay AS pop
		LEFT JOIN smc_payfee_order AS po ON po.order_pid = pop.order_pid 
	WHERE
		po.school_id = sc.school_id 
		AND pop.paytype_code = 'feewaiver' 
		AND pop.pay_issuccess = '0' 
		AND {$datawhere9}
		) AS feeNum,(
	SELECT
		count( x.changelog_id ) 
	FROM
		smc_student_changelog x,
		smc_student y 
	WHERE
		x.student_id = y.student_id 
		AND x.school_id = sc.school_id 
		AND x.stuchange_code IN ( 'C02', 'C04' ) 
		AND y.student_isdel <> '1' 
		AND x.changelog_note = '' 
		AND {$datawhere10}
		AND NOT (
			x.stuchange_code IN ( 'C02', 'C04' ) 
			AND EXISTS (
			SELECT
				1 
			FROM
				smc_student_changelog 
			WHERE
				change_pid > x.change_pid 
				AND student_id = x.student_id 
				AND school_id = x.school_id 
			AND stuchange_code IN ( 'A01', 'A06', 'D02', 'F01' ))) 
		AND NOT (
			x.stuchange_code = 'C04' 
			AND EXISTS (
			SELECT
				1 
			FROM
				smc_student_changelog 
			WHERE
				change_pid > x.change_pid 
				AND student_id = x.student_id 
				AND school_id = x.school_id 
				AND coursetype_id = x.coursetype_id 
				AND stuchange_code = 'D04' 
			))) AS lostNum 
FROM
	smc_school AS sc 
WHERE
	{$datawhere}
ORDER BY
	(
	CASE
			
			WHEN sc.school_istest = 0 
			AND sc.school_isclose = 0 THEN
				1 
				WHEN sc.school_isclose = 0 THEN
				2 
				WHEN sc.school_istest = 0 THEN
				3 ELSE 4 
			END 
			),
			sc.school_istest ASC,
			field( sc.school_sort, 0 ),
			sc.school_sort ASC,
			sc.school_createtime ASC,
			sc.school_id ASC
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无收入数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $schoolOne) {
                    $datearray = array();
                    $datearray['school_id'] = $schoolOne['school_id'];
                    $datearray['province_name'] = $schoolOne['province_name'];
                    $datearray['school_branch'] = $schoolOne['school_branch'];
                    $datearray['school_shortname'] = $schoolOne['school_shortname'];
                    $datearray['hourNum'] = $schoolOne['hourNum'];

                    $sql = "select scb.coursebalance_id
                  from smc_student_coursebalance as scb
                  where scb.school_id='{$schoolOne['school_id']}' and scb.coursebalance_status='2' and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) and {$datawhere11}
                  group by scb.student_id";
                    $forwardCourseList = $this->DataControl->selectClear($sql);
                    if ($forwardCourseList) {
                        $datearray['forwardNum'] = count($forwardCourseList);
                    } else {
                        $datearray['forwardNum'] = 0;
                    }

                    $sql = "select (select (sum(sb.student_balance)+sum(sb.student_withholdbalance)) as balacne from smc_student_balance as sb where sb.school_id=se.school_id and sb.student_id=se.student_id) as balacne
                  ,(select sum(scb.coursebalance_figure) from smc_student_coursebalance as scb where scb.school_id=se.school_id and scb.student_id=se.student_id) as coursebalance_figure
                  from smc_student_enrolled as se
                  where se.school_id='{$schoolOne['school_id']}' and (se.enrolled_status=0 or se.enrolled_status=1) and {$datawhere12}
                  HAVING ((balacne+coursebalance_figure)<2000 or (coursebalance_figure is NULL ))
                  ";
                    $balanceList = $this->DataControl->selectClear($sql);
                    if ($balanceList) {
                        $datearray['lowNum'] = count($balanceList);
                    } else {
                        $datearray['lowNum'] = 0;
                    }

                    $datearray['lostNum'] = $schoolOne['lostNum'];
                    $sql = "select ht.staffer_id 
                  from smc_class_hour as ch 
                  left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
                  left join smc_class as c on c.class_id=ch.class_id
                  where c.school_id='{$schoolOne['school_id']}' 
                  and ch.hour_ischecking>-1 and c.class_status>-2 and {$datawhere1}
                  group by ch.hour_id 
                  HAVING (staffer_id='0' or staffer_id is NULL)
                  ";
                    $stafferList = $this->DataControl->selectClear($sql);
                    if ($stafferList) {
                        $datearray['noStafferNum'] = count($stafferList);
                    } else {
                        $datearray['noStafferNum'] = 0;
                    }

                    $sql = "select (select ct.staffer_id from smc_class_teach as ct where ct.class_id=c.class_id and ct.teach_type='0' and ct.teach_status='0' limit 0,1) as staffer_id
                  from smc_class as c
                  where c.school_id='{$schoolOne['school_id']}' and {$datawhere13}
                  group by c.class_id
                  HAVING (staffer_id='0' or staffer_id is NULL)
                  ";
                    $stafferList = $this->DataControl->selectClear($sql);
                    if ($stafferList) {
                        $datearray['noStafferClassNum'] = count($stafferList);
                    } else {
                        $datearray['noStafferClassNum'] = 0;
                    }

                    $datearray['orderNum'] = $schoolOne['orderNum'];
                    $datearray['feeNum'] = $schoolOne['feeNum'];
                    $datearray['dealorderNum'] = $schoolOne['dealorderNum'];
                    $datearray['freeNum'] = $schoolOne['freeNum'];
                    $datearray['schoolRefundNum'] = $schoolOne['schoolRefundNum'];
                    $datearray['refundNum'] = $schoolOne['refundNum'];
                    $datearray['reduceorderNum'] = $schoolOne['reduceorderNum'];
                    $datearray['applyNum'] = $schoolOne['applyNum'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "学校简称", "延迟课次数", "延班人数", "低学费人数", "未填写流失原因人数", "无主教课时数", "无主教班级数", "未审订单数", "未审减免订单数", "未审结转订单数", "未审赠课订单数", "校未审退费订单数", "未审退费订单数", "未审减课订单数", "未审优惠券数"));
            $excelfileds = array('province_name', 'school_branch', 'school_shortname', "hourNum", 'forwardNum', "lowNum", 'lostNum', "noStafferNum", 'noStafferClassNum', 'orderNum', 'feeNum', 'dealorderNum', 'freeNum', 'schoolRefundNum', 'refundNum', 'reduceorderNum', 'applyNum');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('校务执行检核报表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $schoolInfoList = $this->DataControl->selectClear($sql);

            if (!$schoolInfoList) {
                $this->error = true;
                $this->errortip = "无学校数据";
                return false;
            }

            foreach ($schoolInfoList as &$schoolOne) {
                $sql = "select scb.coursebalance_id
                  from smc_student_coursebalance as scb
                  where scb.school_id='{$schoolOne['school_id']}' and scb.coursebalance_status='2' and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) and {$datawhere11}
                  group by scb.student_id";
                $forwardCourseList = $this->DataControl->selectClear($sql);
                if ($forwardCourseList) {
                    $schoolOne['forwardNum'] = count($forwardCourseList);
                } else {
                    $schoolOne['forwardNum'] = 0;
                }

                $sql = "select (select (sum(sb.student_balance)+sum(sb.student_withholdbalance)) as balacne from smc_student_balance as sb where sb.school_id=se.school_id and sb.student_id=se.student_id) as balacne
                  ,(select sum(scb.coursebalance_figure) from smc_student_coursebalance as scb where scb.school_id=se.school_id and scb.student_id=se.student_id) as coursebalance_figure
                  from smc_student_enrolled as se where se.school_id='{$schoolOne['school_id']}' and (se.enrolled_status = 0 or se.enrolled_status = 1) and {$datawhere12}
                  HAVING ((balacne+coursebalance_figure) < 2000 or (coursebalance_figure is NULL ))";
                $balanceList = $this->DataControl->selectClear($sql);
                if ($balanceList) {
                    $schoolOne['lowNum'] = count($balanceList);
                } else {
                    $schoolOne['lowNum'] = 0;
                }

                $sql = "select ht.staffer_id 
                  from smc_class_hour as ch 
                  left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
                  left join smc_class as c on c.class_id=ch.class_id
                  where c.school_id='{$schoolOne['school_id']}' 
                  and ch.hour_ischecking>-1 and c.class_status>-2 and {$datawhere1}
                  group by ch.hour_id
                  HAVING (staffer_id='0' or staffer_id is NULL)
                  ";
                $stafferList = $this->DataControl->selectClear($sql);
                if ($stafferList) {
                    $schoolOne['noStafferNum'] = count($stafferList);
                } else {
                    $schoolOne['noStafferNum'] = 0;
                }

                $sql = "select (select ct.staffer_id from smc_class_teach as ct where ct.class_id=c.class_id and ct.teach_type='0' and ct.teach_status='0' limit 0,1) as staffer_id
                  from smc_class as c
                  where c.school_id='{$schoolOne['school_id']}' and {$datawhere13}
                  group by c.class_id
                  HAVING (staffer_id='0' or staffer_id is NULL)
                  ";
                $stafferList = $this->DataControl->selectClear($sql);
                if ($stafferList) {
                    $schoolOne['noStafferClassNum'] = count($stafferList);
                } else {
                    $schoolOne['noStafferClassNum'] = 0;
                }
            }

            $data = array();

            $cuntStunums = $this->DataControl->selectClear("select sc.school_id from smc_school as sc where {$datawhere}");

            if ($cuntStunums) {
                $allnum = count($cuntStunums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;

            $data['list'] = $schoolInfoList;

            return $data;

        }
    }

    /**
     * crm检核表
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function crmInspectionReport($request)
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $datawhere = "s.company_id='{$request['company_id']}' AND s.school_istest = '0' AND s.school_isclose = '0'";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }

        if (isset($request['re_postbe_id']) && $request['re_postbe_id'] !== '' && $request['re_postbe_id'] !== '0') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or  s.school_shortname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%' )";
        }

        $post_string = "'A0101','A0102','B0101','B0102','B0103','B0104','B0105','A0103','A0104','A0105','A0106','A0107'";

        $five_daytime = strtotime(date("Ymd", strtotime("-15 day")));
        $sql = "select s.school_id,
                    s.school_branch,s.school_cnname,s.school_enname,school_shortname,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province ) as province_name,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname,
                    (select count(DISTINCT p.marketer_id) from crm_client_principal as p,crm_marketer as m,smc_staffer as f where m.marketer_id = p.marketer_id and  p.school_id = s.school_id and f.staffer_id=m.staffer_id and p.principal_leave =0 and p.principal_ismajor =1 and marketer_status = 1  and f.staffer_leave = 0) as mian_marketer_num,
                    (select count(DISTINCT sp.staffer_id) from gmc_staffer_postbe as sp,gmc_company_post as pt,smc_staffer as f where sp.post_id=pt.post_id  and sp.school_id=s.school_id and f.staffer_id=sp.staffer_id and  pt.post_code in ({$post_string}) and sp.postbe_ismianjob =1 AND f.staffer_leave = 0 and (f.staffer_wxtoken ='' or f.staffer_wxtoken is NULL) ) as post_token_num,
                    (select count(iv.invite_id) from crm_client_invite as iv where iv.school_id =s.school_id and iv.invite_isvisit = 0 and DATE_FORMAT(iv.invite_visittime, '%Y-%m-%d') < DATE_FORMAT(NOW(), '%Y-%m-%d') ) as invite_no_num,
                    (select count(au.audition_id) from crm_client_audition as au where au.school_id =s.school_id and au.audition_isvisit = 0 and DATE_FORMAT(au.audition_visittime, '%Y-%m-%d') < DATE_FORMAT(NOW(), '%Y-%m-%d') ) as audition_no_num,
                    (select count(t.client_id) from crm_client as t,crm_client_schoolenter as sr where sr.client_id =t.client_id and sr.is_enterstatus =1 and sr.school_id =s.school_id and t.client_tracestatus ='-1' and t.client_ischaserlapsed =0 ) as client_lossnum,
                    (select count(c.client_id) from crm_client as c,crm_client_schoolenter as r where c.client_id=r.client_id and c.client_distributionstatus = 1 and c.client_tracestatus <> 4 and  c.client_tracestatus <> -1 and c.client_tracestatus <> -2  
                    and  c.client_id not in ( select DISTINCT t.client_id from crm_client_track as t where t.school_id=r.school_id and t.track_isactive =1 and track_createtime >='{$five_daytime}' )
                    and c.client_id  in (select pl.client_id from crm_client_principal as pl where pl.school_id =r.school_id and pl.principal_leave =0 )
                    and r.is_enterstatus =1 and r.school_id=s.school_id  ) as tracestus_num,
                    (select count(c.client_id) from crm_client as c,crm_client_schoolenter as r where c.client_id =r.client_id and c.client_distributionstatus = 1 and c.client_tracestatus =0 and r.school_id = s.school_id  and r.is_enterstatus =1 ) as client_num,
                    ( SELECT COUNT(DISTINCT e.client_id) FROM crm_client_schoolenter AS e, crm_client AS c WHERE e.client_id = c.client_id AND e.school_id = s.school_id AND c.client_tracestatus IN (0, 1, 2, 3) AND c.client_intention_level = 0 AND e.is_enterstatus = '1') AS levelnums,
                    (SELECT COUNT(c.client_id) FROM crm_client AS c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id LEFT JOIN crm_client_intention AS t ON t.client_id = c.client_id
		            WHERE e.school_id = s.school_id AND e.is_enterstatus = '1' AND t.intention_id IS NULL AND c.client_tracestatus IN (0,1,2,3)) AS client_nopurposenum
                    FROM smc_school as s where {$datawhere}";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $schoolOne) {
                    $datearray = array();
                    $datearray['province_name'] = $schoolOne['province_name'];
                    $datearray['school_branch'] = $schoolOne['school_branch'];
                    $datearray['school_shortname'] = $schoolOne['school_shortname'];
                    $datearray['district_cnname'] = $schoolOne['district_cnname'];
                    $datearray['post_token_num'] = $schoolOne['post_token_num'];
                    $datearray['client_num'] = $schoolOne['client_num'];
                    $datearray['client_nopurposenum'] = $schoolOne['client_nopurposenum'];
                    $datearray['mian_marketer_num'] = $schoolOne['mian_marketer_num'];
                    $datearray['invite_no_num'] = $schoolOne['invite_no_num'];
                    $datearray['audition_no_num'] = $schoolOne['audition_no_num'];
                    $datearray['client_lossnum'] = $schoolOne['client_lossnum'];
                    $datearray['tracestus_num'] = $schoolOne['tracestus_num'];
                    $datearray['levelnums'] = $schoolOne['levelnums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("省份", "校区编号", "学校简称", "所属区域", '未激活微信账户', "暂未跟进名单数", "未设定意向课程名单数", "主负责人数", "柜询待确认数", "试听待确认数", "无意向待审核数", "半月内未有效跟踪数", "未设定意向星级数"));
            $excelfileds = array('province_name', 'school_branch', 'school_shortname', 'district_cnname', 'post_token_num', 'client_num', 'client_nopurposenum', 'mian_marketer_num', "invite_no_num", "audition_no_num", "client_lossnum", "tracestus_num", "levelnums");

            query_to_excel($excelheader, $outexceldate, $excelfileds, 'CRM执行检核报表.xlsx');
            exit;
        }

        $sql .= " limit {$pagestart},{$num}";
        $dataList = $this->DataControl->selectClear($sql);
        if (!$dataList) {
            $dataList = array();
        }
        $result = array();
        $result['list'] = $dataList;
        $all_num = $this->DataControl->selectOne("select count(s.school_id) as all_num from smc_school as s where {$datawhere} ");
        if ($all_num) {
            $result['all_num'] = $all_num['all_num'];
        } else {
            $result['all_num'] = 0;
        }
        return $result;
    }

    //教师类型下拉
    function getTeachtypeApi($paramArray)
    {
        $sql = "select teachtype_code,teachtype_name from smc_code_teachtype where company_id = '{$paramArray['company_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["teachtype_code"] = "教师类型编号";
        $field["teachtype_name"] = "教师类型名称";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '教师类型下拉查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '教师类型下拉查看失败', 'result' => $result);
        }
        return $res;
    }

    //学校考勤差异统计表
    function schoolMachineStuDiff($request)
    {
        $datawhere = " s.company_id='{$request['company_id']}' ";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND s.school_istest <> '1' AND s.school_isclose <> '1'";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ( s.school_branch like '%{$request['keyword']}%' 
            or s.school_cnname like '%{$request['keyword']}%' 
            or s.school_enname like '%{$request['keyword']}%' )";
        }

        $sondatawhere = ' ';
        $realitwhere = ' ';
        if (isset($request['start_time']) && $request['start_time'] !== '' && isset($request['end_time']) && $request['end_time'] !== '') {
            $startday = $request['start_time'];
            $endday = $request['end_time'];
            $sondatawhere .= " and ((cc.class_stdate <= '{$request['start_time']}' and cc.class_enddate >= '{$request['start_time']}') or (cc.class_stdate <= '{$request['end_time']}' and cc.class_enddate >= '{$request['end_time']}')) ";
            $sondatawhere .= " and ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' ";
            $realitwhere .= " and ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' ";
        }else{
            if (isset($request['start_time']) && $request['start_time'] !== '') {
                $startday = $request['start_time'];
                $sondatawhere .= " and cc.class_stdate <= '{$request['start_time']}' and cc.class_enddate >= '{$request['start_time']}' ";
            }else{
                $startday = date("Y-m-d");
            }
            if (isset($request['end_time']) && $request['end_time'] !== '') {
                $endday = $request['start_time'];
                $sondatawhere .= " and cc.class_stdate <= '{$request['end_time']}' and cc.class_enddate >= '{$request['end_time']}' ";
            }else{
                $endday = date("Y-m-d");
            }
            $sondatawhere .= " and ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' ";
            $realitwhere .= " and ch.hour_day >='{$startday}' and ch.hour_day <='{$endday}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $sondatawhere .= " and uu.coursetype_id= '{$request['coursetype_id']}'";
            $realitwhere .= " and uu.coursetype_id= '{$request['coursetype_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_id,s.school_branch,s.school_cnname,s.school_enname,
                    (SELECT r.region_name FROM smc_code_region as r WHERE r.region_id = s.school_province ) as province_name,
                    (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = s.district_id) AS district_cnname
                FROM smc_school as s  
                WHERE {$datawhere} and (select c.cardlog_id from gmc_machine_stucardlog as c where c.school_id = s.school_id limit 0,1) > 1
              ";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
//            $dateexcelarray = $this->DataControl->selectClear($sql);
//
//            if (!$dateexcelarray) {
//                $this->error = true;
//                $this->errortip = "无在籍新生";
//                return false;
//            }
//
//            $outexceldate = array();
//            if ($dateexcelarray) {
//                $outexceldate = array();
//                foreach ($dateexcelarray as $dateexcelvar) {
//                    $datearray = array();
//                    $datearray['school_id'] = $dateexcelvar['school_id'];
//                    $datearray['province_name'] = $dateexcelvar['province_name'];
//                    $outexceldate[] = $datearray;
//                }
//            }
//            $excelheader = $this->LgArraySwitch(array("学校id", "省份"));//, "就读班级信息", "发放优惠券信息"
//            $excelfileds = array('school_id', 'province_name');//, 'courses_branch', 'coupons_name'
//            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("在籍新生统计表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $NewReg = $this->DataControl->selectClear($sql);
            if (!$NewReg) {
                $this->error = true;
                $this->errortip = "无在籍新生";
                return false;
            }

            foreach ($NewReg as &$var) {
                //应出勤最大人数
                $sonsql = "  select DISTINCT(ss.student_id) as student_id 
                        from smc_class_hour as ch
                        left join smc_class as cc on cc.class_id=ch.class_id
                        left join smc_student_study as ss on ss.class_id=ch.class_id 
			            LEFT JOIN smc_course as uu ON cc.course_id = uu.course_id 
                        where ss.school_id='{$var['school_id']}'
                        and ch.hour_iswarming='0' and cc.class_type=0 
                        {$sondatawhere} ";
                $MaxStu = $this->DataControl->selectClear($sonsql);
                if ($MaxStu) {
                    //应出勤最大人数
                    $var['MaxStuNum'] = count($MaxStu);
                    //人脸采集人数最大人数
                    $MaxStuArray = array_column($MaxStu, 'student_id');
                    $MaxStuIdStr = implode(',', $MaxStuArray);
                    $MaxStuportrait = $this->DataControl->selectOne(" select count(DISTINCT(student_id)) as num from gmc_machine_stuportrait where student_id in ($MaxStuIdStr)  ");
                    $var['MaxStuportraitNum'] = $MaxStuportrait['num'] ? $MaxStuportrait['num'] : '--';
                } else {
                    $var['MaxStuNum'] = '--';
                    $var['MaxStuportraitNum'] = '--';
                }

                // 考勤完全匹配人数
                $matesql = "  select  ch.hour_day,ss.student_id,
  IF((select 1 from gmc_machine_stucardlog as g where g.school_id = cc.school_id and FROM_UNIXTIME(g.cardlog_clocktime,'%Y-%m-%d') = ch.hour_day and g.student_id = ss.student_id limit 0,1),1,0) as stucardlog,
  IF((select 1 from smc_student_hourstudy as sh where sh.hour_id = ch.hour_id and sh.student_id = ss.student_id and sh.hourstudy_checkin='1'),1,0) as hourstudylog  
                        from smc_class_hour as ch
                        left join smc_class as cc on cc.class_id=ch.class_id
                        left join smc_student_study as ss on ss.class_id=ch.class_id 
			            LEFT JOIN smc_course as uu ON cc.course_id = uu.course_id 
                        where ss.school_id='{$var['school_id']}'
                        and ch.hour_iswarming='0' and cc.class_type=0 
                        {$sondatawhere}
                        group by ch.hour_day,ss.student_id ";
                $mateStu = $this->DataControl->selectClear($matesql);
                if ($mateStu) {
                    $allMateNum = 0;//考勤完全匹配人数
                    $allNotMateNum = 0;//考勤不匹配人数
                    $RightMateNum = 0;//当天有点名出勤但没有扫脸出勤的人数
                    $LeftMateNum = 0;//当天没有点名出勤但有扫脸出勤的人数
                    foreach ($mateStu as $matesqlvar) {
                        if ($matesqlvar['stucardlog'] == 1 && $matesqlvar['hourstudylog'] == 1) {
                            $allMateNum++;
                        }
                        if ($matesqlvar['stucardlog'] == '1' && $matesqlvar['hourstudylog'] == '0') {
                            $RightMateNum++;
                            $allNotMateNum++;
                        }
                        if ($matesqlvar['stucardlog'] == '0' && $matesqlvar['hourstudylog'] == '1') {
                            $LeftMateNum++;
                            $allNotMateNum++;
                        }
                    }
                    //考勤完全匹配人数
                    $var['allMateNum'] = $allMateNum;
                    //考勤不匹配人数
                    $var['allNotMateNum'] = $allNotMateNum;
                } else {
                    //考勤完全匹配人数
                    $var['allMateNum'] = '--';
                    //考勤不匹配人数
                    $var['allNotMateNum'] = '--';
                }
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT s.school_id 
                FROM smc_school as s  
                WHERE {$datawhere} ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
            }
            $data = array();
            $data['allnum'] = $allnum;
            $data['list'] = $NewReg;
            return $data;
        }
    }

}
