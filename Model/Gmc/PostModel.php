<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  PostModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //集团职务列表
    function getPostList($paramArray)
    {

        $datawhere = "p.company_id = '{$paramArray['company_id']}' and p.post_type = '{$paramArray['post_type']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.post_name like '%{$paramArray['keyword']}%' or p.post_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['postlevel_id']) && $paramArray['postlevel_id'] !== "") {
            $datawhere .= " and p.postlevel_id ='{$paramArray['postlevel_id']}'";
        }
        if (isset($paramArray['post_istopjob']) && $paramArray['post_istopjob'] !== "") {
            $datawhere .= " and p.post_istopjob ='{$paramArray['post_istopjob']}'";
        }
        if (isset($paramArray['post_id']) && $paramArray['post_id'] !== "") {
            $datawhere .= " and p.post_id ='{$paramArray['post_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        if ($paramArray['post_type'] == '0') {
            $sql = "
                SELECT
                    p.post_id,
                    p.post_name,
                    p.post_code,
                    p.post_type,
                    p.post_remk,
                    p.postlevel_id,
                    p.postrole_id,
                    p.post_isteaching,
                    p.post_isteaching as status,
                    p.post_istopjob,
                    p.post_istopjob as post_istopjob_status,
                    po.postlevel_cnname,
                    r.postrole_name
                FROM
                    gmc_company_post AS p left join gmc_company_postlevel as po on p.postlevel_id = po.postlevel_id left join gmc_company_postrole as r on p.postrole_id  = r.postrole_id
                WHERE
                    {$datawhere}
                ORDER BY
                    p.post_id DESC 
                LIMIT {$pagestart},{$num}";
        } else {
            $sql = "
                SELECT
                    p.post_id,
                    p.post_name,
                    p.post_code,
                    p.post_type,
                    p.post_remk,
                    p.postpart_id,
                    p.postlevel_id,
                    p.post_isteaching,
                    p.post_stride,
                    p.post_stride AS post_stride_name,
                    p.post_isteaching as status,
                    p.post_isrecrparttime,
                    p.post_isrecrparttime as post_isrecrparttime_status,
                    p.post_istopjob,
                    p.post_istopjob as post_istopjob_status,
                    po.postlevel_cnname,
                    r.postpart_name
                FROM
                    gmc_company_post AS p left join gmc_company_postlevel as po on p.postlevel_id = po.postlevel_id left join smc_school_postpart as r on p.postpart_id  = r.postpart_id
                WHERE
                    {$datawhere}
                ORDER BY
                    p.post_id DESC 
                LIMIT {$pagestart},{$num}";
        }


        $postList = $this->DataControl->selectClear($sql);
        if ($postList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $statuss = $this->LgArraySwitch(array("0" => "不允许", "1" => "允许"));
            foreach ($postList as &$val) {
                $val['status'] = $status[$val['status']];
                $val['post_isrecrparttime_status'] = $statuss[$val['post_isrecrparttime_status']];
                $val['post_istopjob_status'] = $status[$val['post_istopjob_status']];
                $val['post_stride_name'] = $status[$val['post_stride_name']];
            }

        }

        $allNum = $this->DataControl->selectOne("SELECT COUNT(p.post_id) AS postnums FROM  gmc_company_post AS p WHERE {$datawhere}");
        $allnums = $allNum['postnums'];
        if ($paramArray['post_type'] == '0') {
            $fieldstring = array('post_name ', 'post_code', 'post_type', 'post_remk', 'post_istopjob_status', 'postrole_name');
            $fieldname = $this->LgArraySwitch(array('职务名称', '职务编号', '类型', '备注', '是否校园高管', '默认所属集团角色'));
            $fieldcustom = array("1", "1", "0", "1", "1", "1");
            $fieldshow = array("1", "1", "0", "1", "1", "1");
        } else {
            $fieldstring = array('post_name ', 'post_code', 'post_stride_name', 'status', 'post_isrecrparttime_status', 'postpart_name', 'post_istopjob_status', 'post_remk', 'post_type', 'post_isteaching');
            $fieldname = $this->LgArraySwitch(array('职务名称', '职务编号', '是否允许跨校', '是否教学职务', '允许招募兼职', '默认所属校园角色', '是否校园高管', '备注', '类型', '教学职务'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "0", "0");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "0", "0");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($postList) {
            $result['list'] = $postList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['post'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$paramArray['company_id']}'");

        $result['postlevel'] = $this->DataControl->selectClear("select postlevel_id,postlevel_cnname from gmc_company_postlevel where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            if ($paramArray['post_type'] == '0') {
                $res = array('error' => '1', 'errortip' => "暂无集团职务类别", 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => "暂无校园职务类别", 'result' => $result);
            }
        }


        return $res;
    }

    //添加职务
    function addPostAction($paramArray)
    {
        $data = array();
        $data['post_type'] = $paramArray['post_type'];
        $data['company_id'] = $paramArray['company_id'];
        $data['post_name'] = $paramArray['post_name'];
        $data['post_remk'] = $paramArray['post_remk'];
        $data['post_code'] = $paramArray['post_code'];
        $data['post_istopjob'] = $paramArray['post_istopjob'];
        $data['postlevel_id'] = $paramArray['postlevel_id'];
        if ($paramArray['post_type'] == '1') {
            $data['post_isteaching'] = $paramArray['post_isteaching'];
            $data['post_stride'] = $paramArray['post_stride'];
            $data['postpart_id'] = $paramArray['postpart_id'];

            $contractOne = $this->getContract($this->company_id);

            if ($contractOne && $contractOne['edition_id'] == '2') {
                $data['post_isrecrparttime'] = 1;
            } else {
                $data['post_isrecrparttime'] = $paramArray['post_isrecrparttime'];
            }

            $data['post_istopjob'] = $paramArray['post_istopjob'];
        }
        if ($paramArray['post_type'] == '0') {
            $data['postrole_id'] = $paramArray['postrole_id'];
        }
        $data['post_createtime'] = time();

        $field = array();
        $field['post_type'] = "职务类型";
        $field['company_id'] = "所属集团";
        $field['post_name'] = "职务名称";
        $field['post_remk'] = "备注";
        $field['post_code'] = "职务编号";
        $field['postlevel_id'] = "默认职等";
        if ($paramArray['post_type'] == '1') {
            $field['post_isteaching'] = "是否教学职务";
            $field['postpart_id'] = "默认校园角色";
            $field['post_isrecrparttime'] = "是否允许招募兼职";
            $field['post_istopjob'] = "是否校园最高职务";
        }
        if ($paramArray['post_type'] == '0') {
            $field['postrole_id'] = "默认集团角色";
        }
        $field['post_createtime'] = "创建时间";

        $post_code = $this->DataControl->getFieldOne('gmc_company_post', 'post_id', "post_code = '{$paramArray['post_code']}' and company_id = '{$paramArray['company_id']}' and post_type = '{$paramArray['post_type']}'");
        if ($post_code) {
            ajax_return(array('error' => 1, 'errortip' => "职务编号已存在!"), $this->companyOne['company_language']);
        }

        $post_name = $this->DataControl->getFieldOne('gmc_company_post', 'post_id', "post_name = '{$paramArray['post_name']}' and company_id = '{$paramArray['company_id']}' and post_type = '{$paramArray['post_type']}'");
        if ($post_name) {
            ajax_return(array('error' => 1, 'errortip' => "职务名称已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('gmc_company_post', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职务成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加职务', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职务失败', 'result' => $result);
        }
        return $res;
    }


    //编辑集团职务
    function updatePostAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("gmc_company_post", "post_id", "post_id = '{$paramArray['post_id']}'");
        if ($postOne) {
            $data = array();
            $data['post_type'] = $paramArray['post_type'];
            $data['company_id'] = $paramArray['company_id'];
            $data['post_name'] = $paramArray['post_name'];
            $data['post_remk'] = $paramArray['post_remk'];
            $data['post_code'] = $paramArray['post_code'];
            $data['post_istopjob'] = $paramArray['post_istopjob'];
            $data['postlevel_id'] = $paramArray['postlevel_id'];
            if ($paramArray['post_type'] == '0') {
                $data['postrole_id'] = $paramArray['postrole_id'];
            }
            if ($paramArray['post_type'] == '1') {
                $data['postpart_id'] = $paramArray['postpart_id'];
                $data['post_stride'] = $paramArray['post_stride'];
                $data['post_isteaching'] = $paramArray['post_isteaching'];

                $contractOne = $this->getContract($this->company_id);

                if ($contractOne && $contractOne['edition_id'] == '2') {
                    $data['post_isrecrparttime'] = 1;
                } else {
                    $data['post_isrecrparttime'] = $paramArray['post_isrecrparttime'];
                }

                $data['post_istopjob'] = $paramArray['post_istopjob'];
            }
            $data['post_updatetime'] = time();

            $field = array();
            $field['post_type'] = "职务类型";
            $field['company_id'] = "所属集团";
            $field['post_name'] = "职务名称";
            $field['post_remk'] = "备注";
            $field['post_code'] = "职务编号";
            $field['postlevel_id'] = "默认职等";
            if ($paramArray['post_type'] == '1') {
                $field['post_isteaching'] = "是否教学职务";
                $field['postpart_id'] = "默认校园角色";
                $field['post_isrecrparttime'] = "是否允许招募兼职：0不允许1允许";
                $field['post_istopjob'] = "是否校园最高职务";
            }
            if ($paramArray['post_type'] == '0') {
                $field['postrole_id'] = "默认集团角色";
            }
            $field['post_updatetime'] = "修改时间";

            $post_code = $this->DataControl->getFieldOne('gmc_company_post', 'post_code', "post_id = '{$paramArray['post_id']}'");
            if ($paramArray['post_code'] != $post_code['post_code']) {
                $post_code = $this->DataControl->getFieldOne('gmc_company_post', 'post_id', "post_code = '{$paramArray['post_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($post_code) {
                    ajax_return(array('error' => 1, 'errortip' => "职务编号已存在!"), $this->companyOne['company_language']);
                }
            }

            $post_name = $this->DataControl->getFieldOne('gmc_company_post', 'post_name', "post_id = '{$paramArray['post_id']}'");
            if ($paramArray['post_name'] != $post_name['post_name']) {
                $post_name = $this->DataControl->getFieldOne('gmc_company_post', 'post_id', "post_name = '{$paramArray['post_name']}' and company_id = '{$paramArray['company_id']}'");
                if ($post_name) {
                    ajax_return(array('error' => 1, 'errortip' => "职务名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("gmc_company_post", "post_id = '{$paramArray['post_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职务修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑职务', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职务修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除职务
    function delPostAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("gmc_company_post", "post_id", "post_id = '{$paramArray['post_id']}'");
        if ($postOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "post_id = '{$paramArray['post_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "职务已被使用，无法删除!"), $this->companyOne['company_language']);
            } else {
                if ($this->DataControl->delData("gmc_company_post", "post_id = '{$paramArray['post_id']}'")) {
                    $result = array();
                    $res = array('error' => '0', 'errortip' => "删除职务成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除职务', dataEncode($paramArray));

                } else {
                    $result = array();
                    $res = array('error' => '1', 'errortip' => '删除职务失败', 'result' => $result);
                }
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }

        return $res;
    }

    //职工管理 -- 添加职工
    function addStafferAction($paramArray)
    {
        $data = array();
        $data['staffer_mobile'] = $paramArray['staffer_mobile'];
        $data['staffer_cnname'] = $paramArray['staffer_cnname'];
        $data['staffer_branch'] = $paramArray['staffer_branch'];
        $data['company_id'] = $paramArray['company_id'];
        $data['post_id'] = $paramArray['post_id'];
        $data['postrole_id'] = $paramArray['postrole_id'];
        $data['postlevel_id'] = $paramArray['postlevel_id'];
        $data['staffer_birthday'] = $paramArray['staffer_birthday'];
        $data['staffer_pass'] = $paramArray['staffer_pass'];
        $data['staffer_sex'] = $paramArray['staffer_sex'];
        $data['staffer_createtime'] = time();

        $field = array();
        $field['staffer_mobile'] = "手机号";
        $field['staffer_cnname'] = "中文名称";
        $field['staffer_branch'] = "职工编号";
        $field['company_id'] = "所属集团";
        $field['post_id'] = "职务";
        $field['postrole_id'] = "角色";
        $field['postlevel_id'] = "职级";
        $field['staffer_birthday'] = "出生日期";
        $field['staffer_pass'] = "初始密码";
        $field['staffer_sex'] = "性别";
        $field['staffer_createtime'] = "创建时间";

        if ($this->DataControl->insertData("smc_staffer", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构", '添加职工', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工失败', 'result' => $result);
        }
        return $res;
    }


    //离职原因列表
    function reasonsleavingApi($paramArray)
    {
        $sql = "
            SELECT
                r.reasonsleaving_id,
                r.reasonsleaving_note,
                r.reasonsleaving_remark
            FROM
                gmc_code_reasonsleaving AS r
            WHERE
                company_id = '{$paramArray['company_id']}'";
        $reasonsleavingList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(r.reasonsleaving_id)
            FROM
                gmc_code_reasonsleaving AS r
            WHERE
                company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $field = array();
        $field["reasonsleaving_note"] = "离职原因";
        $field["reasonsleaving_remark"] = "备注";

        $result = array();


        if ($reasonsleavingList) {
            $result["field"] = $field;
            $result["data"] = $reasonsleavingList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取离职原因列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取离职原因列表失败', 'result' => $result);
        }
        return $res;
    }

    //添加离职原因
    function addReasonsleavingAction($paramArray)
    {
        $data = array();
        $data['reasonsleaving_note'] = $paramArray['reasonsleaving_note'];
        $data['reasonsleaving_remark'] = $paramArray['reasonsleaving_remark'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['reasonsleaving_note'] = "离职原因";
        $field['reasonsleaving_remark'] = "描述";
        $field['company_id'] = "所属公司";

        if ($this->DataControl->insertData('gmc_code_reasonsleaving', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加离职原因成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加离职原因', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加离职原因失败', 'result' => $result);
        }
        return $res;
    }

    //编辑离职原因
    function updateReasonsleavingAction($paramArray)
    {
        $ReasonsleavingOne = $this->DataControl->getFieldOne("gmc_code_reasonsleaving", "reasonsleaving_id", "reasonsleaving_id = '{$paramArray['reasonsleaving_id']}'");
        if ($ReasonsleavingOne) {
            $data = array();
            $data['reasonsleaving_note'] = $paramArray['reasonsleaving_note'];
            $data['reasonsleaving_remark'] = $paramArray['reasonsleaving_remark'];
            $data['company_id'] = $paramArray['company_id'];

            $field = array();
            $field['reasonsleaving_note'] = "离职原因";
            $field['reasonsleaving_remark'] = "描述";
            $field['company_id'] = "所属集团";

            if ($this->DataControl->updateData("gmc_code_reasonsleaving", "reasonsleaving_id = '{$paramArray['reasonsleaving_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "离职原因修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑离职原因', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '离职原因修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除离职原因
    function delReasonsleavingAction($paramArray)
    {
        $reasonsleavingOne = $this->DataControl->getFieldOne("gmc_code_reasonsleaving", "reasonsleaving_id", "reasonsleaving_id = '{$paramArray['reasonsleaving_id']}'");
        if ($reasonsleavingOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("gmc_code_reasonsleaving", "reasonsleaving_id = '{$paramArray['reasonsleaving_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除离职原因成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除离职原因', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除离职原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取职等列表
    function getPostlevelList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.postlevel_cnname like '%{$paramArray['keyword']}%' or p.postlevel_enname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['postlevel_id']) && $paramArray['postlevel_id'] !== "") {
            $datawhere .= " and p.postlevel_id ={$paramArray['postlevel_id']}";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.postlevel_id,
                p.postlevel_cnname,
                p.postlevel_enname,
                p.postlevel_remk
            FROM
                gmc_company_postlevel AS p 
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.postlevel_id DESC 
            LIMIT {$pagestart},{$num}";

        $postlevelList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.postlevel_id)
            FROM
                gmc_company_postlevel AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('postlevel_cnname ', 'postlevel_enname', 'postlevel_remk');
        $fieldname = $this->LgArraySwitch(array('职等中文名称', '职等英文名称', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($postlevelList) {
            $result['list'] = $postlevelList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['postlevel'] = $this->DataControl->selectClear("select postlevel_id,postlevel_cnname from gmc_company_postlevel where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无职等信息", 'result' => $result);
        }

        return $res;
    }

    //编辑职等
    function updatePostlevelAction($paramArray)
    {
        $postlevelOne = $this->DataControl->getFieldOne("gmc_company_postlevel", "postlevel_id", "postlevel_id = '{$paramArray['postlevel_id']}'");
        if ($postlevelOne) {
            $data = array();
            $data['postlevel_cnname'] = $paramArray['postlevel_cnname'];
            $data['postlevel_enname'] = $paramArray['postlevel_enname'];
            $data['postlevel_remk'] = $paramArray['postlevel_remk'];

            $field = array();
            $field['postlevel_cnname'] = "职等中文名";
            $field['postlevel_enname'] = "职等英文名";
            $field['postlevel_remk'] = "备注";

            $postlevel_cnname = $this->DataControl->getFieldOne('gmc_company_postlevel', 'postlevel_cnname', "postlevel_id = '{$paramArray['postlevel_id']}'");
            if ($paramArray['postlevel_cnname'] != $postlevel_cnname['postlevel_cnname']) {
                $postlevel_cnname = $this->DataControl->getFieldOne('gmc_company_postlevel', 'postlevel_id', "postlevel_cnname = '{$paramArray['postlevel_cnname']}' and company_id = '{$paramArray['company_id']}'");
                if ($postlevel_cnname) {
                    ajax_return(array('error' => 1, 'errortip' => "职等中文名已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("gmc_company_postlevel", "postlevel_id = '{$paramArray['postlevel_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职等修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑职等', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职等修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //添加职等
    function addPostlevelAction($paramArray)
    {
        $data = array();
        $data['postlevel_cnname'] = $paramArray['postlevel_cnname'];
        $data['postlevel_enname'] = $paramArray['postlevel_enname'];
        $data['postlevel_remk'] = $paramArray['postlevel_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['postlevel_cnname'] = "职等中文名";
        $field['postlevel_enname'] = "职等英文名";
        $field['postlevel_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $postlevel_cnname = $this->DataControl->getFieldOne('gmc_company_postlevel', 'postlevel_id', "postlevel_cnname = '{$paramArray['postlevel_cnname']}' and company_id = '{$paramArray['company_id']}'");
        if ($postlevel_cnname) {
            ajax_return(array('error' => 1, 'errortip' => "职等中文名存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('gmc_company_postlevel', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职等成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加职等', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职等失败', 'result' => $result);
        }
        return $res;
    }

    //删除职等
    function delPostlevelAction($paramArray)
    {
        $PostlevelOne = $this->DataControl->getFieldOne("gmc_company_postlevel", "postlevel_id", "postlevel_id = '{$paramArray['postlevel_id']}'");
        if ($PostlevelOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postlevel_id = '{$paramArray['postlevel_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该职等已被使用，不可删除"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("gmc_company_postlevel", "postlevel_id = '{$paramArray['postlevel_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职等成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除职等', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职等失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取职工异动类型列表
    function getWorkchangeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (w.workchange_name like '%{$paramArray['keyword']}%' or w.workchange_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                w.workchange_id,
                w.workchange_name,
                w.workchange_code,
                w.workchange_remk
            FROM
                gmc_code_workchange AS w
            WHERE
                {$datawhere} and w.company_id = '{$paramArray['company_id']}'
            ORDER BY
                w.workchange_id DESC    
            LIMIT {$pagestart},{$num}";

        $WorkchangeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(w.workchange_id)
            FROM
                gmc_code_workchange AS w
            WHERE
                {$datawhere} and w.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('workchange_name ', 'workchange_code', 'workchange_remk');
        $fieldname = $this->LgArraySwitch(array('异动名称', '异动编号', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($WorkchangeList) {
            $result['list'] = $WorkchangeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职工异动类型", 'result' => $result);
        }

        return $res;
    }

    //添加职工异动类型
    function addWorkchangeAction($paramArray)
    {
        $data = array();
        $data['workchange_code'] = $paramArray['workchange_code'];
        $data['workchange_name'] = $paramArray['workchange_name'];
        $data['workchange_remk'] = $paramArray['workchange_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['workchange_code'] = "异动编号";
        $field['workchange_name'] = "异动名称";
        $field['workchange_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $workchange_code = $this->DataControl->getFieldOne('gmc_code_workchange', 'workchange_id', "workchange_code = '{$paramArray['workchange_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($workchange_code) {
            ajax_return(array('error' => 1, 'errortip' => "异动编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('gmc_code_workchange', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工异动类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加职工异动类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工异动类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑职工异动类型
    function updateWorkchangeAction($paramArray)
    {
        $WorkchangeOne = $this->DataControl->getFieldOne("gmc_code_workchange", "workchange_id,workchange_code", "workchange_id = '{$paramArray['workchange_id']}'");
        if ($WorkchangeOne) {
            $data = array();
            $data['workchange_code'] = $paramArray['workchange_code'];
            $data['workchange_name'] = $paramArray['workchange_name'];
            $data['workchange_remk'] = $paramArray['workchange_remk'];

            $field = array();
            $field['workchange_code'] = "异动编号";
            $field['workchange_name'] = "异动名称";
            $field['workchange_remk'] = "备注";


            $workchange_code = $this->DataControl->getFieldOne('gmc_code_workchange', 'workchange_id', "workchange_code = '{$paramArray['workchange_code']}' and company_id = '{$paramArray['company_id']}' and workchange_code != '{$WorkchangeOne['workchange_code']}'");
            if ($workchange_code) {
                ajax_return(array('error' => 1, 'errortip' => "异动编号已存在!"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->updateData("gmc_code_workchange", "workchange_id = '{$paramArray['workchange_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工异动类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑职工异动类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工异动类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除职工异动类型
    function delWorkchangeAction($paramArray)
    {
        $WorkchangeOne = $this->DataControl->getFieldOne("gmc_code_workchange", "workchange_code", "workchange_id = '{$paramArray['workchange_id']}'");
        if ($WorkchangeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("gmc_staffer_postchangeslog", "postchangeslog_id", "workchange_code = '{$WorkchangeOne['workchange_code']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该职工异动类型已被使用，不可删除"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("gmc_code_workchange", "workchange_id = '{$paramArray['workchange_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工异动类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除职工异动类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工异动类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取集团角色列表
    function getPostroleList($paramArray)
    {

        $datawhere = "p.company_id = '{$paramArray['company_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.postrole_name like '%{$paramArray['keyword']}%' or p.postrole_remark like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['postrole_id']) && $paramArray['postrole_id'] !== "") {
            $datawhere .= " and p.postrole_id ={$paramArray['postrole_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.postrole_id,
                p.postpart_id,
                p.postrole_name,
                p.postrole_remark,
                p.postrole_dataequity,
                p.postrole_dataequity as postrole_dataequity_status,
                p.postpart_iscompanyuser,
                p.postpart_isgmccrm,
                p.postpart_gmccrmlevel,
                p.postpart_gmccrmlevel AS postpart_iscompanycrmuser_level,
                p.postpart_iscompanyuser as postpart_iscompanyuser_status,
                p.postpart_iscrmuser,
                p.postpart_iscrmuser as postpart_iscrmuser_status,
                p.postpart_iscmsuser,
                p.postpart_iscmsuser as postpart_iscmsuser_status,
                p.postpart_crmuserlevel,
                p.postpart_isucsuser,
                p.postpart_isucsuser as postpart_isucsuser_status,
                p.postpart_ucsuserlevel,
                p.postpart_ucsuserlevel as postpart_ucsuserlevel_status,
                t.postpart_name
            FROM
                gmc_company_postrole AS p left join smc_school_postpart as t on p.postpart_id = t.postpart_id
            WHERE {$datawhere} ORDER BY p.postrole_id DESC
            LIMIT {$pagestart},{$num}";

        $PostroleList = $this->DataControl->selectClear($sql);

        if ($PostroleList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $lecel = $this->LgArraySwitch(array("0" => "普通", "1" => "高管"));
            $statuss = $this->LgArraySwitch(array("0" => "组织数据权益", "1" => "所有数据权益"));
            $type = $this->LgArraySwitch(array("1" => "校区客服", "2" => "校长", "3" => "集团客服", "4" => "集团高管"));
            foreach ($PostroleList as &$val) {
                $val['postpart_iscompanyuser_status'] = $status[$val['postpart_iscompanyuser_status']];
                $val['postpart_iscrmuser_status'] = $status[$val['postpart_iscrmuser_status']];
                $val['postpart_iscmsuser_status'] = $status[$val['postpart_iscmsuser_status']];
                $val['postpart_isucsuser_status'] = $status[$val['postpart_isucsuser_status']];
                $val['postrole_dataequity_status'] = $statuss[$val['postrole_dataequity_status']];
                $val['postpart_iscompanycrmuser_level'] = ($val['postpart_isgmccrm'] == '1') ? $lecel[$val['postpart_iscompanycrmuser_level']] : "--";
                $val['postpart_isgmccrm_status'] = $status[$val['postpart_isgmccrm']];
                if ($val['postpart_ucsuserlevel_status']) {
                    $val['postpart_ucsuserlevel_status'] = $type[$val['postpart_ucsuserlevel_status']];
                } else {
                    $val['postpart_ucsuserlevel'] = '';
                    $val['postpart_ucsuserlevel_status'] = '';
                }
            }
        }

        $allNum = $this->DataControl->selectOne(" SELECT COUNT(p.postrole_id) AS rolenums FROM gmc_company_postrole AS p WHERE {$datawhere}");
        $allnums = $allNum['rolenums'];

        $fieldstring = array('postrole_name ', 'postrole_remark', 'postrole_dataequity_status', 'postpart_iscompanyuser_status', 'postpart_isgmccrm_status', 'postpart_iscompanycrmuser_level', 'postpart_iscrmuser_status', 'postpart_isucsuser_status', 'postpart_ucsuserlevel_status', 'postpart_iscmsuser_status', 'postpart_name', 'postpart_isgmccrm', 'postpart_gmccrmlevel', 'postpart_crmuserlevel');
        $fieldname = $this->LgArraySwitch(array('角色名称', '角色备注', '数据权益类型', '集团管理权限', '集团招生权限', '集团招生角色', '单校招生权限', '客诉系统权限', '客诉系统角色', '校务访问权限', '默认校务角色', '是否拥有集团招生权限：0无1有', '拥有集团招生权限 0普通 1高管','有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '1', '1', '1');
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", '0', '0', '0');

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PostroleList) {
            $result['list'] = $PostroleList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['postrole'] = $this->DataControl->selectClear("select postrole_id,postrole_name from gmc_company_postrole where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无集团角色信息", 'result' => $result);
        }

        return $res;
    }

    //添加集团角色
    function addPostroleAction($paramArray)
    {
        $data = array();
        $data['postrole_name'] = $paramArray['postrole_name'];
        $data['postrole_remark'] = $paramArray['postrole_remark'];
        $data['postpart_iscompanyuser'] = $paramArray['postpart_iscompanyuser'];
        $data['postpart_iscrmuser'] = $paramArray['postpart_iscrmuser'];
        $data['postpart_iscmsuser'] = $paramArray['postpart_iscmsuser'];
        $data['postpart_isucsuser'] = $paramArray['postpart_isucsuser'];
        $data['postrole_dataequity'] = $paramArray['postrole_dataequity_status'];
        $data['postpart_ucsuserlevel'] = $paramArray['postpart_ucsuserlevel'];
        $data['postpart_isgmccrm'] = $paramArray['postpart_isgmccrm'];
        $data['postpart_gmccrmlevel'] = $paramArray['postpart_gmccrmlevel'];
        $data['postpart_id'] = $paramArray['postpart_id'];
        $data['company_id'] = $paramArray['company_id'];
        $data['postpart_crmuserlevel'] = $paramArray['postpart_crmuserlevel'];

        $field = array();
        $field['postrole_name'] = "角色名称";
        $field['postrole_remark'] = "角色备注";
        $field['postpart_iscompanyuser'] = "是否拥有集团模块访问权限";
        $field['postpart_iscrmuser'] = "是否拥有CRM访问权限0不拥有1拥有";
        $field['postpart_iscmsuser'] = "是否拥有下属校务访问权限0否1是";
        $field['postpart_isucsuser'] = "是否拥有下属客诉访问权限0否1是";
        $field['postrole_dataequity'] = "数据权益";
        $field['postpart_ucsuserlevel'] = "客诉角色";
        $field['postpart_isgmccrm'] = "是否拥有集团招生权限：0无1有";
        $field['postpart_gmccrmlevel'] = "集团招生权限 0:普通 1:高管";
        $field['postpart_crmuserlevel'] = "有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场";
        $field['postpart_id'] = "校园角色id";
        $field['company_id'] = "所属公司";

        $postrole_name = $this->DataControl->getFieldOne('gmc_company_postrole', 'postrole_id', "postrole_name = '{$paramArray['postrole_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($postrole_name) {
            ajax_return(array('error' => 1, 'errortip' => "角色名称已存在!"), $this->companyOne['company_language']);
        }
        $datas = array();
        if ($postrole_id = $this->DataControl->insertData('gmc_company_postrole', $data)) {
            $moduleArray = $this->DataControl->selectClear("select module_id from imc_module WHERE module_id = '191' or module_id = '282'");
            if ($moduleArray) {
                foreach ($moduleArray as $itemOne) {
                    $datas['module_id'] = $itemOne['module_id'];
                    $datas['postrole_id'] = $postrole_id;
                    $this->DataControl->insertData('gmc_staffer_usermodule', $datas);
                }
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加集团角色成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加集团角色', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加集团角色失败', 'result' => $result);
        }
        return $res;
    }

    //编辑集团角色
    function updatePostroleAction($paramArray)
    {
        $WorkchangeOne = $this->DataControl->getFieldOne("gmc_company_postrole", "postrole_id", "postrole_id = '{$paramArray['postrole_id']}'");
        if ($WorkchangeOne) {
            $data = array();
            $data['postrole_name'] = $paramArray['postrole_name'];
            $data['postrole_remark'] = $paramArray['postrole_remark'];
            $data['postpart_iscompanyuser'] = $paramArray['postpart_iscompanyuser'];
            $data['postpart_iscrmuser'] = $paramArray['postpart_iscrmuser'];
            $data['postpart_iscmsuser'] = $paramArray['postpart_iscmsuser'];
            $data['postpart_isucsuser'] = $paramArray['postpart_isucsuser'];
            $data['postrole_dataequity'] = $paramArray['postrole_dataequity_status'];
            $data['postpart_ucsuserlevel'] = $paramArray['postpart_ucsuserlevel'];
            $data['postpart_gmccrmlevel'] = $paramArray['postpart_gmccrmlevel'];
            $data['postpart_isgmccrm'] = $paramArray['postpart_isgmccrm'];
            $data['postpart_id'] = $paramArray['postpart_id'];
            $data['postpart_crmuserlevel'] = $paramArray['postpart_crmuserlevel'];

            $field = array();
            $field['postrole_name'] = "角色名称";
            $field['postrole_remark'] = "角色备注";
            $field['postpart_iscompanyuser'] = "是否拥有集团模块访问权限";
            $field['postpart_iscrmuser'] = "是否拥有CRM访问权限0不拥有1拥有";
            $field['postpart_iscmsuser'] = "是否拥有下属校务访问权限0否1是";
            $field['postpart_isucsuser'] = "是否拥有下属客诉访问权限0否1是";
            $field['postrole_dataequity'] = "数据权益";
            $field['postpart_ucsuserlevel'] = "客诉角色";
            $field['postpart_isgmccrm'] = "是否拥有集团招生权限：0无1有";
            $field['postpart_gmccrmlevel'] = "集团招生权限 0:普通 1:高管";
            $field['postpart_crmuserlevel'] = "有crm访问权限时的权限：0普通权限1高管权限2电销权限 3市场";
            $field['postpart_id'] = "校园角色id";

            $postrole_name = $this->DataControl->getFieldOne('gmc_company_postrole', 'postrole_name', "postrole_id = '{$paramArray['postrole_id']}'");
            if ($paramArray['postrole_name'] != $postrole_name['postrole_name']) {
                $postrole_name = $this->DataControl->getFieldOne('gmc_company_postrole', 'postrole_id', "postrole_name = '{$paramArray['postrole_name']}' and company_id = '{$paramArray['company_id']}'");
                if ($postrole_name) {
                    ajax_return(array('error' => 1, 'errortip' => "角色名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("gmc_company_postrole", "postrole_id = '{$paramArray['postrole_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "集团角色修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑集团角色', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '集团角色修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除集团角色
    function delPostroleAction($paramArray)
    {
        $PostroleOne = $this->DataControl->getFieldOne("gmc_company_postrole", "postrole_id", "postrole_id = '{$paramArray['postrole_id']}'");
        if ($PostroleOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postrole_id = '{$paramArray['postrole_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该集团角色已被使用，不可删除"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("gmc_company_postrole", "postrole_id = '{$paramArray['postrole_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除集团角色成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除集团角色', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除集团角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }

        return $res;
    }

    //职工异动原因列表
    function getWorkchangereasonList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.reason_note like '%{$paramArray['keyword']}%' or p.reason_remark like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.reason_id,
                p.reason_note,
                p.reason_remark 
            FROM
                gmc_code_workchange_reason AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.reason_id DESC    
            LIMIT {$pagestart},{$num}";

        $WorkchangereasonList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.reason_id)
            FROM
                gmc_code_workchange_reason AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('reason_note ', 'reason_remark');
        $fieldname = $this->LgArraySwitch(array('异动原因', '异动备注'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($WorkchangereasonList) {
            $result['list'] = $WorkchangereasonList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职工异动原因", 'result' => $result);
        }

        return $res;
    }

    //添加职工异动原因
    function addWorkchangereasonAction($paramArray)
    {
        $data = array();
        $data['reason_note'] = $paramArray['reason_note'];
        $data['reason_remark'] = $paramArray['reason_remark'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['reason_note'] = "异动原因";
        $field['reason_remark'] = "异动备注";
        $field['company_id'] = "所属公司";

        $reason_note = $this->DataControl->getFieldOne('gmc_code_workchange_reason', 'reason_id', "reason_note = '{$paramArray['reason_note']}' and company_id = '{$paramArray['company_id']}'");
        if ($reason_note) {
            ajax_return(array('error' => 1, 'errortip' => "异动原因已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('gmc_code_workchange_reason', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工异动原因成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加职工异动原因', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工异动原因失败', 'result' => $result);
        }
        return $res;
    }

    //编辑职工异动原因
    function updateWorkchangereasonAction($paramArray)
    {
        $WorkchangereasonOne = $this->DataControl->getFieldOne("gmc_code_workchange_reason", "reason_id", "reason_id = '{$paramArray['reason_id']}'");
        if ($WorkchangereasonOne) {
            $data = array();
            $data['reason_note'] = $paramArray['reason_note'];
            $data['reason_remark'] = $paramArray['reason_remark'];

            $field = array();
            $field['reason_note'] = "异动原因";
            $field['reason_remark'] = "异动备注";

            $reason_note = $this->DataControl->getFieldOne('gmc_code_workchange_reason', 'reason_id', "reason_note = '{$paramArray['reason_note']}' and company_id = '{$paramArray['company_id']}'");
            if ($reason_note) {
                ajax_return(array('error' => 1, 'errortip' => "异动原因已存在!"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->updateData("gmc_code_workchange_reason", "reason_id = '{$paramArray['reason_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工异动原因修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑职工异动原因', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工异动原因修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除职工异动原因
    function delWorkchangereasonAction($paramArray)
    {
        $WorkchangereasonOne = $this->DataControl->getFieldOne("gmc_code_workchange_reason", "reason_id", "reason_id = '{$paramArray['reason_id']}'");
        if ($WorkchangereasonOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("gmc_code_workchange_reason", "reason_id = '{$paramArray['reason_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工异动原因成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除职工异动原因', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工异动原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //教师类型列表
    function getTeachtypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.teachtype_name like '%{$paramArray['keyword']}%' or p.teachtype_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.teachtype_id,
                p.teachtype_name,
                p.teachtype_code,
                p.teachtype_native,
                p.teachtype_remk,p.teachtype_class
            FROM
                smc_code_teachtype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.teachtype_id DESC    
            LIMIT {$pagestart},{$num}";

        $teachtypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.teachtype_id)
            FROM
                smc_code_teachtype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('teachtype_name ', 'teachtype_code', 'teachtype_class_name', 'teachtype_native_name', 'teachtype_remk');
        $fieldname = $this->LgArraySwitch(array('教师类型名称', '教师类型编号', '带班限制', '籍贯限制', '备注'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        $class=$this->LgArraySwitch(array('0'=>'主教','1'=>'助教','2'=>'主教/助教'));

        if ($teachtypeList) {
            foreach($teachtypeList as &$teachtypeOne){
                $teachtypeOne['teachtype_class_name']=$class[$teachtypeOne['teachtype_class']];
                $str = "";
                if (strpos($teachtypeOne['teachtype_native'], "0") !== false) {
                    $str .= "陆籍、";
                }
                if (strpos($teachtypeOne['teachtype_native'], "1") !== false) {
                    $str .= "外籍、";
                }
                if (strpos($teachtypeOne['teachtype_native'], "2") !== false) {
                    $str .= "港澳籍、";
                }
                if (strpos($teachtypeOne['teachtype_native'], "3") !== false) {
                    $str .= "台籍、";
                }
                $teachtypeOne['teachtype_native_name'] = trim($str, "、");
            }

            $result['list'] = $teachtypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无教师类别", 'result' => $result);
        }

        return $res;
    }

    //添加教师类型
    function addTeachtypeAction($paramArray)
    {
        $data = array();
        $data['teachtype_code'] = $paramArray['teachtype_code'];
        $data['teachtype_name'] = $paramArray['teachtype_name'];
        $data['teachtype_remk'] = $paramArray['teachtype_remk'];
        $data['teachtype_native'] = $paramArray['teachtype_native'];
        $data['teachtype_class'] = isset($paramArray['teachtype_class'])?$paramArray['teachtype_class']:2;
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['teachtype_code'] = "教师类型编号";
        $field['teachtype_name'] = "教师类型名称";
        $field['teachtype_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $teachtype_code = $this->DataControl->getFieldOne('smc_code_teachtype', 'teachtype_id', "teachtype_code = '{$paramArray['teachtype_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($teachtype_code) {
            ajax_return(array('error' => 1, 'errortip' => "教师类型编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_teachtype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加教师类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加教师类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加教师类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑教师类型
    function updateTeachtypeAction($paramArray)
    {
        $teachtypeOne = $this->DataControl->getFieldOne("smc_code_teachtype", "teachtype_id", "teachtype_id = '{$paramArray['teachtype_id']}'");
        if ($teachtypeOne) {
            $data = array();
            $data['teachtype_code'] = $paramArray['teachtype_code'];
            $data['teachtype_name'] = $paramArray['teachtype_name'];
            $data['teachtype_remk'] = $paramArray['teachtype_remk'];
            $data['teachtype_class'] = $paramArray['teachtype_class'];
            $data['teachtype_native'] = $paramArray['teachtype_native'];

            $field = array();
            $field['teachtype_code'] = "教师类型编号";
            $field['teachtype_name'] = "教师类型名称";
            $field['teachtype_remk'] = "备注";

            $teachtype_code = $this->DataControl->getFieldOne('smc_code_teachtype', 'teachtype_code', "teachtype_id = '{$paramArray['teachtype_id']}'");
            if ($paramArray['teachtype_code'] != $teachtype_code['teachtype_code']) {
                $teachtype_code = $this->DataControl->getFieldOne('smc_code_teachtype', 'teachtype_id', "teachtype_code = '{$paramArray['teachtype_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($teachtype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "教师类型编号已存在!"), $this->companyOne['company_language']);
                }
            }


            if ($this->DataControl->updateData("smc_code_teachtype", "teachtype_id = '{$paramArray['teachtype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "教师类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑教师类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '教师类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除教师类型
    function delTeachtypeAction($paramArray)
    {
        $teachtypeOne = $this->DataControl->getFieldOne("smc_code_teachtype", "teachtype_code", "teachtype_id = '{$paramArray['teachtype_id']}'");
        if ($teachtypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("smc_class_teach", "teach_id", "teachtype_code = '{$teachtypeOne['teachtype_code']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该教师类型已被使用，不可删除"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("smc_code_teachtype", "teachtype_id = '{$paramArray['teachtype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除教师类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除教师类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除教师类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //员工出勤类型列表
    function getStachecktypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.stachecktype_name like '%{$paramArray['keyword']}%' or p.stachecktype_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.stachecktype_id,
                p.stachecktype_name,
                p.stachecktype_code,
                p.stachecktype_remk
            FROM
                smc_code_stachecktype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.stachecktype_id DESC    
            LIMIT {$pagestart},{$num}";

        $teachtypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.stachecktype_id)
            FROM
                smc_code_stachecktype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('stachecktype_name ', 'stachecktype_code', 'stachecktype_remk');
        $fieldname = $this->LgArraySwitch(array('出勤名称', '出勤编号', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($teachtypeList) {
            $result['list'] = $teachtypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职工出勤类型", 'result' => $result);
        }

        return $res;
    }

    //添加员工出勤类型
    function addStachecktypeAction($paramArray)
    {
        $data = array();
        $data['stachecktype_name'] = $paramArray['stachecktype_name'];
        $data['stachecktype_code'] = $paramArray['stachecktype_code'];
        $data['stachecktype_remk'] = $paramArray['stachecktype_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['stachecktype_name'] = "出勤名称";
        $field['stachecktype_code'] = "出勤编号";
        $field['stachecktype_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $stachecktype_code = $this->DataControl->getFieldOne('smc_code_stachecktype', 'stachecktype_id', "stachecktype_code = '{$paramArray['stachecktype_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($stachecktype_code) {
            ajax_return(array('error' => 1, 'errortip' => "员工出勤类型编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_stachecktype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加员工出勤类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加员工出勤类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加员工出勤类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑员工出勤类型
    function updateStachecktypeAction($paramArray)
    {
        $teachtypeOne = $this->DataControl->getFieldOne("smc_code_stachecktype", "stachecktype_id", "stachecktype_id = '{$paramArray['stachecktype_id']}'");
        if ($teachtypeOne) {
            $data = array();
            $data['stachecktype_name'] = $paramArray['stachecktype_name'];
            $data['stachecktype_code'] = $paramArray['stachecktype_code'];
            $data['stachecktype_remk'] = $paramArray['stachecktype_remk'];

            $field = array();
            $field['stachecktype_name'] = "出勤名称";
            $field['stachecktype_code'] = "出勤编号";
            $field['stachecktype_remk'] = "备注";

            $stachecktype_code = $this->DataControl->getFieldOne('smc_code_stachecktype', 'stachecktype_code', "stachecktype_id = '{$paramArray['stachecktype_id']}'");
            if ($paramArray['stachecktype_code'] != $stachecktype_code['stachecktype_code']) {
                $stachecktype_code = $this->DataControl->getFieldOne('smc_code_stachecktype', 'stachecktype_id', "stachecktype_code = '{$paramArray['stachecktype_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($stachecktype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "员工出勤类型编号已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_code_stachecktype", "stachecktype_id = '{$paramArray['stachecktype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "员工出勤类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑员工出勤类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '员工出勤类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除员工出勤类型
    function delStachecktypeAction($paramArray)
    {
        $teachtypeOne = $this->DataControl->getFieldOne("smc_code_stachecktype", "stachecktype_id", "stachecktype_id = '{$paramArray['stachecktype_id']}'");
        if ($teachtypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("smc_code_stachecktype", "stachecktype_id = '{$paramArray['stachecktype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除员工出勤类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除员工出勤类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除员工出勤类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取校园角色列表
    function getPostpartList($paramArray)
    {

        $datawhere = "p.company_id = '{$paramArray['company_id']}' and p.school_id = '0'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.postpart_name like '%{$paramArray['keyword']}%' or p.postpart_remark like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['postrole_id']) && $paramArray['postrole_id'] !== "") {
            $datawhere .= " and p.postrole_id ={$paramArray['postrole_id']}";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and s.school_id ={$paramArray['school_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.postpart_id,
                p.postpart_name,
                p.postpart_isteregulator,
                p.postpart_isteregulator as postpart_isteregulator_name,
                p.postpart_istel,
                p.postpart_istel as postpart_istel_name,
                p.postpart_teltype,
                p.postpart_teltype as postpart_teltype_name,
                p.postpart_remark,
                p.postpart_isbeike,
                p.postpart_istraining,
                p.postpart_isregister,
                p.postpart_isucsuser,
                p.postpart_ucsuserlevel
            FROM smc_school_postpart AS p
            WHERE {$datawhere} ORDER BY p.postpart_id DESC
            LIMIT {$pagestart},{$num}";

        $PostroleList = $this->DataControl->selectClear($sql);

        if ($PostroleList) {
            $status = $this->LgArraySwitch(array("0" => "✕", "1" => "✓"));
            $statuss = $this->LgArraySwitch(array("0" => "不允许", "1" => "允许"));
            $statusss = $this->LgArraySwitch(array("0" => "代班电访", "1" => "全员电访"));
            $type = $this->LgArraySwitch(array("1" => "校区客服", "2" => "校长", "3" => "集团客服", "4" => "集团高管"));
            foreach ($PostroleList as &$val) {
                $val['postpart_isteregulator_name'] = $status[$val['postpart_isteregulator_name']];
                $val['postpart_istel_name'] = $statuss[$val['postpart_istel_name']];
                $val['postpart_teltype_name'] = $statusss[$val['postpart_teltype_name']];
                $val['postpart_isbeike_name'] = $status[$val['postpart_isbeike']];
                $val['postpart_istraining_name'] = $status[$val['postpart_istraining']];
                $val['postpart_isregister_name'] = $status[$val['postpart_isregister']];
                $val['postpart_isucsuser_name'] = $status[$val['postpart_isucsuser']];
                if ($val['postpart_ucsuserlevel']) {
                    $val['postpart_ucsuserlevel_name'] = $type[$val['postpart_ucsuserlevel']];
                } else {
                    $val['postpart_ucsuserlevel'] = '';
                    $val['postpart_ucsuserlevel_name'] = '';
                }
            }
        }

        $allNum = $this->DataControl->selectOne(" SELECT COUNT(p.postpart_id) AS  partnums FROM smc_school_postpart AS p WHERE {$datawhere}");
        $allnums = $allNum['partnums'];

        $fieldstring = array('postpart_name', 'postpart_remark', 'postpart_istel_name', 'postpart_teltype_name', 'postpart_isucsuser_name', 'postpart_ucsuserlevel_name', 'postpart_isteregulator_name', 'postpart_isbeike_name', 'postpart_istraining_name', 'postpart_isregister_name');
        $fieldname = $this->LgArraySwitch(array('角色名称', '角色备注', '是否允许电访', '电访权限类型', '客诉权限', '客诉角色', '是否教学高管', '备课管理权限', '培训管理权限', '教务登记权限'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PostroleList) {
            $result['list'] = $PostroleList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['school'] = array();
        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校务角色信息", 'result' => $result);
        }

        return $res;
    }

    //添加校园角色
    function addPostPartAction($paramArray)
    {
        $data = array();
//        $data['school_id'] = $paramArray['school_id'];
        $data['postpart_name'] = $paramArray['postpart_name'];
        $data['postpart_remark'] = $paramArray['postpart_remark'];
        $data['postpart_isteregulator'] = $paramArray['postpart_isteregulator'];
        $data['postpart_istel'] = $paramArray['postpart_istel'];
        $data['postpart_teltype'] = $paramArray['postpart_teltype'];
        $data['company_id'] = $paramArray['company_id'];
        $data['postpart_isbeike'] = $paramArray['postpart_isbeike'];
        $data['postpart_istraining'] = $paramArray['postpart_istraining'];
        $data['postpart_isregister'] = $paramArray['postpart_isregister'];
        $data['postpart_isucsuser'] = $paramArray['postpart_isucsuser'];
        $data['postpart_ucsuserlevel'] = $paramArray['postpart_ucsuserlevel'];

        $field = array();
//        $field['school_id'] = "所属校园";
        $field['postpart_name'] = "角色名称";
        $field['postpart_remark'] = "角色备注";
        $field['company_id'] = "所属公司";

        $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_id', "postpart_name = '{$paramArray['postpart_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($postpart_name) {
            ajax_return(array('error' => 1, 'errortip' => "角色名称已存在!"), $this->companyOne['company_language']);
        }

        if ($id = $this->DataControl->insertData('smc_school_postpart', $data)) {
            $module = $this->DataControl->selectClear("select module_id from imc_module WHERE module_id = '52' or module_id = '89'");
            foreach ($module as $item) {

                $datas['module_id'] = $item['module_id'];
                $datas['postpart_id'] = $id;

                $this->DataControl->insertData('smc_staffer_usermodule', $datas);
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加校园角色成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '添加校园角色', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加校园角色失败', 'result' => $result);
        }
        return $res;
    }

    //编辑校园角色
    function updatePostpartAction($paramArray)
    {
        $WorkchangeOne = $this->DataControl->getFieldOne("smc_school_postpart", "postpart_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($WorkchangeOne) {
            $data = array();
            $data['school_id'] = $paramArray['school_id'];
            $data['postpart_name'] = $paramArray['postpart_name'];
            $data['postpart_remark'] = $paramArray['postpart_remark'];
            $data['postpart_isteregulator'] = $paramArray['postpart_isteregulator'];
            $data['postpart_istel'] = $paramArray['postpart_istel'];
            $data['postpart_teltype'] = $paramArray['postpart_teltype'];
            $data['postpart_isbeike'] = $paramArray['postpart_isbeike'];
            $data['postpart_istraining'] = $paramArray['postpart_istraining'];
            $data['postpart_isregister'] = $paramArray['postpart_isregister'];
            $data['postpart_isucsuser'] = $paramArray['postpart_isucsuser'];
            $data['postpart_ucsuserlevel'] = $paramArray['postpart_ucsuserlevel'];

            $field = array();
            $field['school_id'] = "所属校园";
            $field['postpart_name'] = "角色名称";
            $field['postpart_remark'] = "角色备注";

            $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_name', "postpart_id = '{$paramArray['postpart_id']}'");
            if ($paramArray['postpart_name'] != $postpart_name['postpart_name']) {
                $postpart_name = $this->DataControl->getFieldOne('smc_school_postpart', 'postpart_id', "postpart_name = '{$paramArray['postpart_name']}' and company_id = '{$paramArray['company_id']}'");
                if ($postpart_name) {
                    ajax_return(array('error' => 1, 'errortip' => "角色名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_school_postpart", "postpart_id = '{$paramArray['postpart_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "校园角色修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '编辑校园角色', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '校园角色修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除集团角色
    function delPostpartAction($paramArray)
    {
        $PostpartOne = $this->DataControl->getFieldOne("smc_school_postpart", "postpart_id", "postpart_id = '{$paramArray['postpart_id']}'");
        if ($PostpartOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postpart_id = '{$paramArray['postpart_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该校园角色已被使用，不可删除"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("smc_school_postpart", "postpart_id = '{$paramArray['postpart_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除校园角色成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->人事相关设置", '删除校园角色', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除校园角色失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    function getCompanyidApi($paramArray)
    {

        $sql = "select s.staffer_id,s.staffer_cnname,s.staffer_branch,s.company_id,s.account_class,c.company_cnname,c.company_ismajor,c.company_iseditimportcourse,c.company_canbatchespay,c.company_isachieve,c.company_ischargepermit,c.company_isnointention 
              from smc_staffer as s 
              left join gmc_company as c on s.company_id = c.company_id 
              WHERE s.staffer_id = '{$paramArray['staffer_id']}'";

        $companyOne = $this->DataControl->selectOne($sql);
        if ($companyOne) {
            $contractOne = $this->getContract($this->company_id);

            //CRM 那边标识职工的ID
            $markOne = $this->DataControl->getFieldOne('crm_marketer', 'marketer_id,marketer_name', "staffer_id='{$paramArray['staffer_id']}'");
            if (!$markOne) {
                $getaddmarkertOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
                $markOne['marketer_id'] = $getaddmarkertOne['marketer_id'];
                $markOne['marketer_name'] = $getaddmarkertOne['marketer_name'];
            }

            $result = array();
            $result['company_id'] = $companyOne['company_id'];
            $result['staffer_id'] = $companyOne['staffer_id'];
            $result['staffer_cnname'] = $companyOne['staffer_cnname'];
            $result['staffer_branch'] = $companyOne['staffer_branch'];
            $result['marketer_id'] = $markOne['marketer_id'];
            $result['account_class'] = $companyOne['account_class'];
            $result['company_cnname'] = $companyOne['company_cnname'];
            $result['company_ismajor'] = $companyOne['company_ismajor'];
            $result['company_iseditimportcourse'] = $companyOne['company_iseditimportcourse'];
            $result['company_canbatchespay'] = $companyOne['company_canbatchespay'];
            $result['company_isachieve'] = $companyOne['company_isachieve'];
            $result['company_ischargepermit'] = $companyOne['company_ischargepermit'];
            $result['company_isnointention'] = $companyOne['company_isnointention'];
            $result['edition_code'] = $contractOne ? $contractOne['edition_code'] : '';


            $sql = "select p.postrole_id,r.postrole_dataequity,p.postbe_isgmccrm,p.postbe_gmccrmlevel from gmc_staffer_postbe as p left join gmc_company_postrole as r on r.postrole_id = p.postrole_id WHERE p.postbe_id = '{$paramArray['re_postbe_id']}' and p.school_id = '0' order by p.postbe_ismianjob DESC limit 0,1";

            $postbeOne = $this->DataControl->selectOne($sql);

            if ($companyOne['account_class'] == '1') {
                $postbeOne['postbe_isgmccrm'] = '1';
                $postbeOne['postbe_gmccrmlevel'] = '1';
            }
            $result['postrole_id'] = $postbeOne['postrole_id'];
            $result['postbe_isgmccrm'] = $postbeOne['postbe_isgmccrm'];
            $result['postbe_gmccrmlevel'] = $postbeOne['postbe_gmccrmlevel'];

            if (!$result['postrole_id']) {
                $result['dataequity'] = '1';
            } else {
                $result['dataequity'] = $postbeOne['postrole_dataequity'];
            }
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }


        return $res;
    }

    function getNameApi($paramArray)
    {

        $staffer_cnname = $this->DataControl->getFieldOne('smc_staffer', 'staffer_cnname,staffer_enname,staffer_img', "staffer_id = '{$paramArray['staffer_id']}'");
        $result = array();
        $result['staffer_cnname'] = $staffer_cnname['staffer_enname'] ? $staffer_cnname['staffer_cnname'] . '-' . $staffer_cnname['staffer_enname'] : $staffer_cnname['staffer_cnname'];
        $result['staffer_img'] = $staffer_cnname['staffer_img'];

        if ($paramArray['re_postbe_id'] == '0') {
            $messageCount = $this->DataControl->selectOne("SELECT count(n.notice_id) AS num FROM gmc_company_notice AS n
LEFT JOIN gmc_company_notice_read AS r ON n.notice_id = r.notice_id AND r.staffer_id = '{$paramArray['staffer_id']}'
WHERE n.notice_class = 0 AND n.notice_status = 1 AND n.company_id = '{$paramArray['company_id']}' AND r.read_id IS NULL limit 0,1");
            $result['mescount'] = $messageCount['num'];
        } else {
            $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $messageCount = $this->DataControl->selectClear("SELECT
                	(count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a
                	WHERE a.staffer_id = '{$paramArray['staffer_id']}'  and a.notice_class = 0)) AS num
            FROM gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                left join gmc_company_notice_postapply as p on n.notice_id = p.notice_id 
            WHERE n.notice_class = 0 and n.notice_status = 1 and n.company_id = '{$paramArray['company_id']}' and p.post_id = '{$post['post_id']}'");
            $result['mescount'] = $messageCount[0]['num'];
        }

        return $result;
    }

    function getOwnInfoApi($request)
    {

        $contractOne = $this->getContract($this->company_id);

        if (!$contractOne) {
            $res = $this->getOwnInfoApibak($request);
            return $res;
        }

        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id=1");

        if (!$promoduleList) {
            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"), $this->companyOne['company_language']);
        }

        $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_enname,
                i.info_birthday as staffer_birthday,
                s.staffer_img,
                s.account_class,
                s.staffer_branch,
                s.staffer_sex,
                (select c.post_name from gmc_staffer_postbe as p left join gmc_company_post as c on p.post_id = c.post_id where p.staffer_id = s.staffer_id and p.school_id = '0' order by p.postbe_ismianjob DESC limit 0,1) as post_name
            FROM
                smc_staffer AS s left join smc_staffer_info as i on s.staffer_id = i.staffer_id
            WHERE
                s.staffer_id = '{$request['staffer_id']}'";
        $stafferOne = $this->DataControl->selectOne($sql);
        $stafferOne['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
        $stafferOne['staffer_img'] = $stafferOne['staffer_img'] ? $stafferOne['staffer_img'].'?x-oss-process=image/resize,m_lfit,w_200,limit_0/auto-orient,1/quality,q_90' : '';
        if ($stafferOne['account_class'] == '1') {
            $datawhere = " 1 ";
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isachieve", "company_id='{$this->company_id}'");
            if ($companyOne && $companyOne['company_isachieve'] == 0) {
                $datawhere .= " and m.module_id not in (634,635,636)";
            }

            $Model = new \Model\Gmc\ModuleModel($request);

            $sql = "select m.module_id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_icon as icon,m.father_id
                    ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->company_id}' and ed.module_id=m.module_id),1) as status
                from imc_module as m 
                where {$datawhere} and m.product_id=1 
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_class asc,m.module_weight asc,m.module_id asc
                ";
            $moduleList = $this->DataControl->selectClear($sql);

            $tree = $Model->getModuleTree($moduleList, 'module_id', 'url');
            $results['children'] = $tree;
        }

        $sql = "SELECT
                    p.postbe_id, 
                    s.staffer_img, 
                    o.post_name,
                    s.staffer_cnname,
                    s.staffer_enname,
                    i.info_birthday as staffer_birthday,
                    s.staffer_branch,
                    s.account_class,
                    s.staffer_sex,
                    r.postrole_dataequity
                FROM
                    gmc_staffer_postbe AS p 
                    left join gmc_company_post as o on p.post_id = o.post_id 
                    left join smc_staffer as s on s.staffer_id = p.staffer_id 
                    left join smc_staffer_info as i on i.staffer_id = s.staffer_id 
                    left join gmc_company_postrole as r on r.postrole_id = p.postrole_id
                WHERE
                    p.postbe_id = '{$request['re_postbe_id']}' 
                    AND p.school_id = '0' 
                ORDER BY
                    p.postbe_ismianjob DESC";

        $postDetail = $this->DataControl->selectOne($sql);

        if (!$postDetail) {
            $postDetail = array();
        } else {
            $postDetail['staffer_cnname'] = $postDetail['staffer_enname'] ? $postDetail['staffer_cnname'] . '-' . $postDetail['staffer_enname'] : $postDetail['staffer_cnname'];
        }

        $company = $this->DataControl->getFieldOne("gmc_company", "company_logo,company_isvoucher,company_isassist,company_iseditstu", "company_id = '{$request['company_id']}'");

        $result = array();
        if ($stafferOne) {
            $result["company_isvoucher"] = $company['company_isvoucher'];
            $result["company_isassist"] = $company['company_isassist'];
            $result["company_iseditstu"] = $company['company_iseditstu'];
            $result["data"][] = $stafferOne;
            $result["post"][] = $postDetail;
            if ($stafferOne['account_class'] == '1') {
                $result["dataequity"] = '1';
            } else {
                $result["dataequity"] = $postDetail['postrole_dataequity'];
            }
            $result["img"] = $company['company_logo'];
            $res = array('error' => '0', 'errortip' => '个人信息查看成功', 'result' => $result, 'results' => $results);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '个人信息查看失败', 'result' => $result);
        }
        return $res;
    }

    function getOwnInfoApibak($paramArray)
    {
        $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_enname,
                i.info_birthday as staffer_birthday,
                s.staffer_img,
                s.account_class,
                s.staffer_branch,
                s.staffer_sex,
                (select c.post_name from gmc_staffer_postbe as p left join gmc_company_post as c on p.post_id = c.post_id where p.staffer_id = '{$paramArray['staffer_id']}' and p.school_id = '0' order by p.postbe_ismianjob DESC limit 0,1) as post_name
            FROM
                smc_staffer AS s left join smc_staffer_info as i on s.staffer_id = i.staffer_id
            WHERE
                s.staffer_id = '{$paramArray['staffer_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $results = array();

        $major = $this->DataControl->getFieldOne("gmc_company", "company_ismajor", "company_id = '{$paramArray['company_id']}'");
        if ($stafferDetail) {
            foreach ($stafferDetail as &$stafferOne) {
                $stafferOne['staffer_cnname'] = $stafferOne['staffer_enname'] ? $stafferOne['staffer_cnname'] . '-' . $stafferOne['staffer_enname'] : $stafferOne['staffer_cnname'];
            }
        }

        if ($stafferDetail[0]['account_class'] == '1') {
            $datawhere = " 1 ";
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isachieve", "company_id='{$this->company_id}'");
            if ($companyOne && $companyOne['company_isachieve'] == 0) {
                $datawhere .= " and module_id not in (634,635,636)";
            }


            $module_name = $this->DataControl->getFieldOne('imc_module', 'module_name', "module_class = '1'");
            $results['title'] = $module_name['module_name'];
            if ($paramArray['company_id'] == '1001') {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where {$datawhere} and module_class = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
            } else {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where {$datawhere} and module_class = '1' and module_isshow = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
            }

            $data = $this->trees($data);
            $results['children'] = $data;
        }

        $sql = "SELECT
                    p.postbe_id, 
                    o.post_name,
                    s.staffer_cnname,
                    s.staffer_enname,
                    i.info_birthday as staffer_birthday,
                    s.staffer_img,
                    s.staffer_branch,
                    s.account_class,
                    s.staffer_sex,
                    r.postrole_dataequity
                FROM
                    gmc_staffer_postbe AS p 
                    left join gmc_company_post as o on p.post_id = o.post_id 
                    left join smc_staffer as s on s.staffer_id = p.staffer_id 
                    left join smc_staffer_info as i on i.staffer_id = s.staffer_id 
                    left join gmc_company_postrole as r on r.postrole_id = p.postrole_id
                WHERE
                    p.postbe_id = '{$paramArray['re_postbe_id']}' 
                    AND p.school_id = '0' 
                ORDER BY
                    p.postbe_ismianjob DESC";

        $postDetail = $this->DataControl->selectClear($sql);

        if (!$postDetail) {
            $postDetail = array();
        } else {
            foreach ($postDetail as &$detailOne) {
                $detailOne['staffer_cnname'] = $detailOne['staffer_enname'] ? $detailOne['staffer_cnname'] . '-' . $detailOne['staffer_enname'] : $detailOne['staffer_cnname'];
            }
        }


        $field = array();
        $field["staffer_cnname"] = "中文名";
        $field["staffer_enname"] = "英文名";
        $field["staffer_birthday"] = "生日";
        $field["post_name"] = "职务";
        $field["staffer_branch"] = "编号";
        $field["staffer_img"] = "头像";
        $field["staffer_sex"] = "性别";

        $company = $this->DataControl->getFieldOne("gmc_company", "company_logo,company_isvoucher,company_isassist,company_iseditstu", "company_id = '{$paramArray['company_id']}'");

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["company_isvoucher"] = $company['company_isvoucher'];
            $result["company_isassist"] = $company['company_isassist'];
            $result["company_iseditstu"] = $company['company_iseditstu'];
            $result["data"] = $stafferDetail;
            $result["post"] = $postDetail;
            if ($stafferDetail[0]['account_class'] == '1') {
                $result["dataequity"] = '1';
            } else {
                $result["dataequity"] = $postDetail[0]['postrole_dataequity'];
            }
            $result["img"] = $company['company_logo'];
            $res = array('error' => '0', 'errortip' => '个人信息查看成功', 'result' => $result, 'results' => $results);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '个人信息查看失败', 'result' => $result);
        }
        return $res;
    }

    //获取个人职务
    function getOwnPostApi($paramArray)
    {
        $sql = "
            SELECT
                p.postbe_id,
                c.post_name,
                c.post_type,
                o.organize_cnname,
                l.postlevel_cnname,
                r.postrole_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c on p.post_id = c.post_id
                left join gmc_company_organize as o on o.organize_id = p.organize_id
                left join gmc_company_postlevel as l on l.postlevel_id = p.postlevel_id
                left join gmc_company_postrole as r on p.postrole_id = r.postrole_id
            WHERE
                p.staffer_id = '{$paramArray['staffer_id']}' 
                AND c.post_type = 0
            ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $fieldstring = array('post_name ', 'post_type', 'organize_cnname', 'postlevel_cnname', 'postrole_name');
        $fieldname = $this->LgArraySwitch(array('职务名称', '职务类型', '组织名称', '职等', '角色'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($stafferDetail) {
            $result['list'] = $stafferDetail;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //获取集团资料
    function getCompanyApi($paramArray)
    {
        $sql = "
            SELECT
                s.company_code,
                s.company_cnname,
                s.company_brief,
                s.company_name,
                s.company_position,
                s.company_mobile,
                s.company_address,
                s.company_homeurl,
                s.company_fax,
                s.company_email,
                s.company_logo,
                s.company_ucsservicecont,
                i.saleman_mobile,
                i.saleman_tephone,
                i.saleman_jobnumber,
                i.saleman_cnname
            FROM
                gmc_company AS s left join imc_sales_contract as c on c.company_id = s.company_id left join imc_saleman as i on i.saleman_id = c.saleman_id where s.company_id = '{$paramArray['company_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["company_code"] = "企业授权编号";
        $field["company_cnname"] = "企业名称";
        $field["company_brief"] = "企业简介";
        $field["company_name"] = "联系人姓名";
        $field["company_position"] = "联系人职务";
        $field["company_mobile"] = "联系人电话";
        $field["company_address"] = "所在地址";
        $field["company_homeurl"] = "企业官网";
        $field["company_fax"] = "传真";
        $field["company_email"] = "联系邮箱";
        $field["company_logo"] = "集团logo";
        $field["company_ucsservicecont"] = "客诉服务介绍";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取集团资料成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取集团资料失败', 'result' => $result);
        }
        return $res;
    }

    //获取阅读人物信息
    function getReadStafferApi($paramArray)
    {
        if ($paramArray['notice_class'] == '1') {
            $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_img,
                (case when l.school_shortname='' then l.school_cnname else l.school_shortname end) as school_cnname,
                (
            SELECT
                c.post_name
            FROM
                gmc_staffer_postbe AS d
                LEFT JOIN gmc_company_post AS c ON d.post_id = c.post_id
            WHERE
                d.staffer_id = s.staffer_id and d.school_id = r.school_id
                ) as post_name
            FROM
                gmc_company_notice_read AS r
                LEFT JOIN smc_staffer AS s ON r.staffer_id = s.staffer_id
                left join smc_school as l on l.school_id = r.school_id
            WHERE r.notice_id = '{$paramArray['notice_id']}'
            GROUP BY r.staffer_id";
        } else {
            $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_img
            FROM
                gmc_company_notice_read AS r
                LEFT JOIN smc_staffer AS s ON r.staffer_id = s.staffer_id
            WHERE r.notice_id = '{$paramArray['notice_id']}'
            GROUP BY r.staffer_id";
        }

        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_cnname"] = "中文名";
        $field["staffer_img"] = "头像";
        $field["school_cnname"] = "学校";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取阅读人物信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取阅读人物信息失败', 'result' => $result);
        }
        return $res;
    }

    //编辑集团资料
    function updateCompanyView($paramArray)
    {
        $Company = $this->DataControl->getFieldOne("gmc_company", "company_id", "company_id = '{$paramArray['company_id']}'");
        if ($Company) {
            $data = array();
            $data['company_code'] = $paramArray['company_code'];
            $data['company_cnname'] = $paramArray['company_cnname'];
            $data['company_brief'] = $paramArray['company_brief'];
            $data['company_name'] = $paramArray['company_name'];
            $data['company_position'] = $paramArray['company_position'];
            $data['company_mobile'] = $paramArray['company_mobile'];
            $data['company_address'] = $paramArray['company_address'];
            $data['company_homeurl'] = $paramArray['company_homeurl'];
            $data['company_fax'] = $paramArray['company_fax'];
            $data['company_email'] = $paramArray['company_email'];
            $data['company_logo'] = $paramArray['company_logo'];
            $data['company_ucsservicecont'] = $paramArray['company_ucsservicecont'];

            $field = array();
            $field["company_code"] = "企业授权编号";
            $field["company_cnname"] = "企业名称";
            $field["company_brief"] = "企业简介";
            $field["company_name"] = "联系人姓名";
            $field["company_position"] = "联系人职务";
            $field["company_mobile"] = "联系人电话";
            $field["company_address"] = "所在地址";
            $field["company_homeurl"] = "企业官网";
            $field["company_fax"] = "传真";
            $field["company_email"] = "联系邮箱";
            $field["company_logo"] = "集团logo";
            $field["company_ucsservicecont"] = "客诉服务介绍";

            if ($this->DataControl->updateData("gmc_company", "company_id = '{$paramArray['company_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑集团资料成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团资料", '编辑集团资料', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑集团资料失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function editCompanySet($request)
    {


        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id", "company_id='{$this->company_id}'");
        if (!$companyOne) {
            $this->error = true;
            $this->errortip = "无对应集团";
            return false;
        }

        $data = array();
        //校务相关设置
        if (isset($request['company_isinvoice']) && $request['company_isinvoice'] != '') {
            $data['company_isinvoice'] = $request['company_isinvoice'];
        }

        if (isset($request['company_iseditimportcourse']) && $request['company_iseditimportcourse'] != '') {
            $data['company_iseditimportcourse'] = $request['company_iseditimportcourse'];
        }

        if (isset($request['company_issign']) && $request['company_issign'] != '') {
            $data['company_issign'] = $request['company_issign'];
        }

        if (isset($request['company_logoutday']) && $request['company_logoutday'] != '') {
            $data['company_logoutday'] = $request['company_logoutday'];
        }

        if (isset($request['company_cantoother']) && $request['company_cantoother'] != '') {
            $data['company_cantoother'] = $request['company_cantoother'];
        }

        if (isset($request['company_iseditstu']) && $request['company_iseditstu'] != '') {
            $data['company_iseditstu'] = $request['company_iseditstu'];
        }

        if (isset($request['company_protocolupdate']) && $request['company_protocolupdate'] != '') {
            $data['company_protocolupdate'] = $request['company_protocolupdate'];
        }

        if (isset($request['company_canbatchespay']) && $request['company_canbatchespay'] != '') {
            $data['company_canbatchespay'] = $request['company_canbatchespay'];
        }

        if (isset($request['company_cantomore']) && $request['company_cantomore'] != '') {
            $data['company_cantomore'] = $request['company_cantomore'];
        }

        //微商城相关设置
        if (isset($request['company_isstock']) && $request['company_isstock'] != '') {
            $data['company_isstock'] = $request['company_isstock'];
        }

        //叮铛助教助学
        if (isset($request['company_isshowhour']) && $request['company_isshowhour'] != '') {
            $data['company_isshowhour'] = $request['company_isshowhour'];
        }

        if (isset($request['company_isshowpay']) && $request['company_isshowpay'] != '') {
            $data['company_isshowpay'] = $request['company_isshowpay'];
        }

        if (isset($request['company_isturnoutlimit']) && $request['company_isturnoutlimit'] != '') {
            $data['company_isturnoutlimit'] = $request['company_isturnoutlimit'];
        }

        if (isset($request['company_isachieve']) && $request['company_isachieve'] != '') {
            $data['company_isachieve'] = $request['company_isachieve'];
        }

        if (isset($request['company_achievelimit']) && $request['company_achievelimit'] != '') {
            $data['company_achievelimit'] = $request['company_achievelimit'];
        }

        //控制是否可以提前入班
        if (isset($request['company_canshiftinclass']) && $request['company_canshiftinclass'] != '') {
            $data['company_canshiftinclass'] = $request['company_canshiftinclass'];
        }

        //是否允许校区操作主职离职
        if (isset($request['company_iscrmoperateleave']) && $request['company_iscrmoperateleave'] != '') {
            $data['company_iscrmoperateleave'] = $request['company_iscrmoperateleave'];
        }

        if (isset($request['company_isneedexaminetrade']) && $request['company_isneedexaminetrade'] != '') {
            $data['company_isneedexaminetrade'] = $request['company_isneedexaminetrade'];
        }

        if (isset($request['company_ischargepermit']) && $request['company_ischargepermit'] != '') {
            $data['company_ischargepermit'] = $request['company_ischargepermit'];
        }

        if (isset($request['company_isdelay']) && $request['company_isdelay'] != '') {
            $data['company_isdelay'] = $request['company_isdelay'];
        }

        if (isset($request['company_canadjustcourse']) && $request['company_canadjustcourse'] != '') {
            $data['company_canadjustcourse'] = $request['company_canadjustcourse'];
        }

        if (isset($request['company_isopenspecialnumber']) && $request['company_isopenspecialnumber'] != '') {
            $data['company_isopenspecialnumber'] = $request['company_isopenspecialnumber'];
        }

        if ($this->DataControl->updateData("gmc_company", "company_id='{$this->company_id}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据错误";
            return false;
        }
    }

    function getCompanyAuthority($paramArray)
    {
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isinvoice,company_iseditimportcourse,company_issign,company_logoutday,company_cantoother,company_cantomore,company_isstock,company_isshowhour,company_isshowpay,company_iseditstu,company_protocolupdate,company_isturnoutlimit,company_canbatchespay,company_canshiftinclass,company_isachieve
        ,company_iscrmoperateleave,company_isneedexaminetrade,company_ischargepermit,company_achievelimit,company_isdelay,company_canadjustcourse,company_isopenspecialnumber", "company_id='{$this->company_id}'");
        if ($companyOne) {
            return $companyOne;
        } else {
            $this->error = true;
            $this->errortip = "无对应集团";
            return false;
        }
    }

    //获取账号资料
    function getUserlist($paramArray)
    {
        $sql = "
            SELECT
                s.staffer_branch,
                s.staffer_bakpass as staffer_pass,
                s.staffer_mobile,
                s.staffer_email
            FROM
                smc_staffer AS s 
            WHERE
                s.staffer_id = '{$paramArray['staffer_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_branch"] = "账号";
        $field["staffer_bakpass"] = "密码";
        $field["staffer_mobile"] = "手机号";
        $field["staffer_email"] = "邮箱";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取账号资料成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取账号资料失败', 'result' => $result);
        }
        return $res;
    }

    //获取集团联系人
    function getContect($paramArray)
    {
        $organize_id = $this->DataControl->selectClear("select organize_id from gmc_staffer_postbe where staffer_id = '{$paramArray['staffer_id']}' order by postbe_createtime desc limit 0,1");
        $status = $this->DataControl->getFieldOne("smc_staffer", "account_class", "staffer_id = '{$paramArray['staffer_id']}'");
        if ($status['account_class'] == '0') {
            $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_sex,
                s.staffer_mobile,
                s.staffer_img,
                s.staffer_enname,
                c.post_name
            FROM
                smc_staffer AS s
                LEFT JOIN gmc_staffer_postbe AS b ON s.staffer_id = b.staffer_id 
                left join gmc_company_post as c on c.post_id = b.post_id
            WHERE
                b.organize_id = '{$organize_id[0]['organize_id']}'
            GROUP BY
                s.staffer_id";
            $stafferDetail = $this->DataControl->selectClear($sql);
        } else {
            $sql = "
            SELECT
                s.staffer_cnname,
                s.staffer_sex,
                s.staffer_mobile,
                s.staffer_img,
                s.staffer_enname,
                c.post_name
            FROM
                smc_staffer AS s
                LEFT JOIN gmc_staffer_postbe AS b ON s.staffer_id = b.staffer_id 
                left join gmc_company_post as c on c.post_id = b.post_id
            WHERE
                s.company_id = '{$paramArray['company_id']}'
            GROUP BY
                s.staffer_id";
            $stafferDetail = $this->DataControl->selectClear($sql);
        }


        $field = array();
        $field["staffer_cnname"] = "中文名";
        $field["staffer_sex"] = "性别";
        $field["staffer_mobile"] = "手机号";
        $field["post_name"] = "职务";
        $field["staffer_img"] = "头像";
        $field["staffer_enname"] = "英文名";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取集团联系人成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取集团联系人失败', 'result' => $result);
        }
        return $res;
    }

    //获取集团通知
    function getNotice($paramArray)
    {
        if ($paramArray['pid'] == '') {
            $sql = "
           SELECT
                t.noticetype_name,
                n.notice_id,
                n.notice_connet,
                n.notice_title,
                n.notice_createtime,
                (select count(r.read_id) from gmc_company_notice_read as r WHERE staffer_id = '{$paramArray['staffer_id']}' and r.notice_id = n.notice_id) as isread
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
            WHERE n.notice_class = 0 and n.notice_status = 1 and n.company_id = '{$paramArray['company_id']}'
            order by n.notice_id DESC";
            $NoticeDetail = $this->DataControl->selectClear($sql);
        } else {
            $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "postbe_id = '{$paramArray['pid']}'");
            $sql = "
           SELECT
                t.noticetype_name,
                n.notice_id,
                n.notice_connet,
                n.notice_title,
                n.notice_createtime,
                (select count(r.read_id) from gmc_company_notice_read as r WHERE staffer_id = '{$paramArray['staffer_id']}' and r.notice_id = n.notice_id) as isread
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                left join gmc_company_notice_postapply as p on n.notice_id = p.notice_id
            WHERE n.notice_class = 0 and n.notice_status = 1 and n.company_id = '{$paramArray['company_id']}' and p.post_id = '{$post['post_id']}'
            order by n.notice_id DESC";
            $NoticeDetail = $this->DataControl->selectClear($sql);
        }


        if ($NoticeDetail) {
            foreach ($NoticeDetail as $key => &$value) {
                $value['notice_createtime'] = $this->ChangeTime($value['notice_createtime']);
                $value['noticetype_name'] = $this->LgStringSwitch($value['noticetype_name']);
            }
        }

        $field = array();
        $field["noticetype_name"] = "通知类型";
        $field["notice_title"] = "通知标题";
        $field["notice_createtime"] = "创建时间";

        $result = array();
        if ($NoticeDetail) {
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $res = array('error' => '0', 'errortip' => '获取集团资料成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取集团资料失败', 'result' => $result);
        }
        return $res;
    }

    //查看通知详情
    function getNoticeDetail($paramArray)
    {
        $sql = "
            SELECT
                n.notice_title,
                s.staffer_cnname as notice_author,
                n.notice_connet,
                n.notice_filelist,
                FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d %H:%i' ) AS notice_createtime,
                o.organize_cnname,
                t.noticetype_name
            FROM
                gmc_company_notice AS n left join gmc_company_organize as o on n.notice_authority = o.organize_id
                left join smc_staffer as s on s.staffer_id = n.staffer_id
                left join smc_code_noticetype as t on t.noticetype_id = n.noticetype_id
            where n.notice_id = '{$paramArray['notice_id']}'";
        $NoticeDetail = $this->DataControl->selectClear($sql);

        if ($NoticeDetail) {
            foreach ($NoticeDetail as &$item) {
//                $item['notice_connet'] = htmlentities($item['notice_connet']).'111111';
                $item['noticetype_name'] = $this->LgStringSwitch($item['noticetype_name']);
            }
        }


        $read = $this->DataControl->getFieldOne('gmc_company_notice_read', 'read_id', "notice_id = '{$paramArray['notice_id']}' and staffer_id = '{$paramArray['staffer_id']}'");

        if (!$read) {
            $data['notice_id'] = $paramArray['notice_id'];
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['notice_class'] = '0';
            $this->DataControl->insertData('gmc_company_notice_read', $data);
        }

        if ($paramArray['re_postbe_id'] == '0') {
            $messageCount = $this->DataControl->selectClear("
            SELECT
                	(
	count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a WHERE a.staffer_id = '{$paramArray['staffer_id']}'  and a.notice_class = 0) 
	) AS num
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
            WHERE n.notice_class = 0 and n.notice_status = 1 and n.company_id = '{$paramArray['company_id']}'");
        } else {
            $post = $this->DataControl->getFieldOne("gmc_staffer_postbe", "post_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $messageCount = $this->DataControl->selectClear("
            SELECT
                	(
	count( n.notice_id ) - ( SELECT count( a.read_id ) FROM gmc_company_notice_read AS a WHERE a.staffer_id = '{$paramArray['staffer_id']}'  and a.notice_class = 0) 
	) AS num
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN gmc_company_notice_postapply AS p ON p.notice_id = n.notice_id
            WHERE n.notice_class = 0 and n.notice_status = 1 and n.company_id = '{$paramArray['company_id']}' and p.post_id = '{$post['post_id']}'");
        }


        $field = array();
        $field["notice_title"] = "标题";
        $field["notice_author"] = "发布人";
        $field["notice_connet"] = "内容";
        $field["notice_createtime"] = "发布时间";
        $field["organize_cnname"] = "所属部门";

        $phone = $this->DataControl->getFieldOne("gmc_company","company_phone","company_id = '{$paramArray['company_id']}'");

        $result = array();
        if ($NoticeDetail) {
            $result["field"] = $field;
            $result["data"] = $NoticeDetail;
            $result['mescount'] = $messageCount[0]['num'];
            $result['phone'] = $phone['company_phone'];
            $res = array('error' => '0', 'errortip' => '查看消息详情成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看消息详情失败', 'result' => $result);
        }
        return $res;
    }

    //获取消息管理集团通知
    function getCompanyNotice($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (n.notice_title like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['noticetype_id']) && $paramArray['noticetype_id'] !== "") {
            $datawhere .= " and n.noticetype_id ={$paramArray['noticetype_id']}";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                n.notice_id,
                n.notice_title,
                n.notice_top,
                n.notice_top as status,
                t.noticetype_name,
                s.staffer_cnname,
                s.staffer_enname,
                FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d' ) as notice_createtime,
                (select count(r.read_id) from gmc_company_notice_read as r where r.notice_id = n.notice_id) as num,
                o.organize_cnname,
                ( SELECT count( p.notice_id ) FROM gmc_company_notice_postapply AS p WHERE p.notice_id = n.notice_id ) AS post_num
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN smc_staffer AS s ON n.staffer_id = s.staffer_id
                left join gmc_company_organize as o on o.organize_id = n.notice_authority
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}' and n.notice_class = 0 and n.notice_status = 1
            ORDER BY
                n.notice_top DESC,
                n.notice_id DESC 
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        $status = array("0" => "0", "1" => "100");
        if ($postList) {
            foreach ($postList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                $val['notice_top'] = $status[$val['notice_top']];

                $val['noticetype_name'] = $this->LgStringSwitch($val['noticetype_name']);
            }
        }
//
//
//        $status=array("0"=>"否", "1"=>"是");
//        if($postList){
//            foreach($postList as &$val){
//                $val['status']=$status[$val['status']];
//            }
//
//        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(n.notice_id)
            FROM
                gmc_company_notice AS n LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN smc_staffer AS s ON n.staffer_id = s.staffer_id
                left join gmc_company_organize as o on o.organize_id = n.notice_authority
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}' and n.notice_class = 0 and n.notice_status = 1");
        $allnums = $all_num[0][0];

        $fieldstring = array('notice_title ', 'noticetype_name', 'num', 'post_num', 'staffer_cnname', 'organize_cnname', 'notice_top', 'notice_createtime');
        $fieldname = $this->LgArraySwitch(array('标题', '类型', '阅读数量', '适配职务数', '发布人', '发布组织', '是否置顶', '发布时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldmethod = array(1, 0, 1, 1, 0, 0, 0, 0);
        $fieldswitch = array(0, 0, 0, 0, 0, 0, 1, 0);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldmethod[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($postList) {
            $result['list'] = $postList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['noticetype'] = $this->DataControl->selectClear("select noticetype_id,noticetype_name from smc_code_noticetype where company_id = '{$paramArray['company_id']}' and noticetype_class = '0'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无集团消息", 'result' => $result);
        }

        return $res;
    }

    //获取消息管理校园通知
    function getSchoolNotice($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (n.notice_title like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['noticetype_id']) && $paramArray['noticetype_id'] !== "") {
            $datawhere .= " and n.noticetype_id ={$paramArray['noticetype_id']}";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                n.notice_id,
                n.notice_title,
                n.notice_top,
                t.noticetype_name,
                s.staffer_cnname,
                s.staffer_enname,
                FROM_UNIXTIME( n.notice_createtime, '%Y-%m-%d' ) as notice_createtime,
                (select count(r.read_id) from gmc_company_notice_read as r where r.notice_id = n.notice_id) as num,
                o.organize_cnname,
                	( SELECT count( p.notice_id ) FROM gmc_company_notice_postapply AS p WHERE p.notice_id = n.notice_id ) AS post_num,
	( SELECT count( s.notice_id ) FROM gmc_company_notice_schoolapply AS s WHERE s.notice_id = n.notice_id ) AS school_num 
            FROM
                gmc_company_notice AS n
                LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN smc_staffer AS s ON n.staffer_id = s.staffer_id
                left join gmc_company_organize as o on o.organize_id = n.notice_authority
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}' and n.notice_class = 1 and n.notice_status = 1
            ORDER BY
                n.notice_top DESC,
                n.notice_id DESC 
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        $status = array("0" => "0", "1" => "100");
        if ($postList) {
            foreach ($postList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                $val['notice_top'] = $status[$val['notice_top']];
                $val['noticetype_name'] = $this->LgStringSwitch($val['noticetype_name']);
            }
        }

//        $status=array("0"=>"否", "1"=>"是");
//        if($postList){
//            foreach($postList as &$val){
//                $val['notice_top']=$status[$val['notice_top']];
//            }
//        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(n.notice_id)
            FROM
                gmc_company_notice AS n LEFT JOIN smc_code_noticetype AS t ON n.noticetype_id = t.noticetype_id
                LEFT JOIN smc_staffer AS s ON n.staffer_id = s.staffer_id
                left join gmc_company_organize as o on o.organize_id = n.notice_authority
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}' and n.notice_class = 1 and n.notice_status = 1");
        $allnums = $all_num[0][0];

        $fieldstring = array('notice_title ', 'noticetype_name', 'num', 'post_num', 'school_num', 'staffer_cnname', 'organize_cnname', 'notice_top', 'notice_createtime');
        $fieldname = $this->LgArraySwitch(array('标题', '类型', '阅读数量', '适配职务数', '适配校园数', '发布人', '发布组织', '是否置顶', '发布时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldmethod = array(1, 0, 1, 1, 1, 0, 0, 0, 0);
        $fieldswitch = array(0, 0, 0, 0, 0, 0, 0, 1, 0);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldmethod[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($postList) {
            $result['list'] = $postList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['noticetype'] = $this->DataControl->selectClear("select noticetype_id,noticetype_name from smc_code_noticetype where company_id = '{$paramArray['company_id']}' and noticetype_class = '1'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校园消息", 'result' => $result);
        }

        return $res;
    }

    //职务列表
    function getMessagetypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (n.noticetype_name like '%{$paramArray['keyword']}%' or n.noticetype_remk like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                n.noticetype_id,
                n.noticetype_name,
                n.noticetype_remk,
                n.noticetype_class,
                n.noticetype_class as status
            FROM
                smc_code_noticetype AS n
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}'
            ORDER BY
                n.noticetype_id DESC 
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(n.noticetype_id)
            FROM
                smc_code_noticetype AS n
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];


        $status = array("0" => "集团消息", "1" => "校园消息");
        if ($postList) {
            foreach ($postList as &$val) {
                $val['status'] = $status[$val['status']];
            }
        }


        $fieldstring = array('noticetype_name ', 'noticetype_remk', 'status', 'noticetype_class');
        $fieldname = $this->LgArraySwitch(array('类型名称', '描述', '类别', 'aaa'));
        $fieldcustom = array("1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无消息类型", 'result' => $result);
        }

        return $res;
    }

    //添加消息类型
    function addMessagetypeAction($paramArray)
    {
        $data = array();
        $data['noticetype_name'] = $paramArray['noticetype_name'];
        $data['noticetype_remk'] = $paramArray['noticetype_remk'];
        $data['noticetype_class'] = $paramArray['noticetype_class'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['noticetype_name'] = "消息类型名称";
        $field['noticetype_remk'] = "描述";
        $field['noticetype_class'] = "类型类别";
        $field['company_id'] = "所属公司";

        $stachecktype_code = $this->DataControl->getFieldOne('smc_code_noticetype', 'noticetype_id', "noticetype_name = '{$paramArray['noticetype_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($stachecktype_code) {
            ajax_return(array('error' => 1, 'errortip' => "消息类型已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_noticetype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加消息类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置", '添加消息类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加消息类型失败', 'result' => $result);
        }
        return $res;
    }


    //编辑消息类型
    function updateMessagetypeAction($paramArray)
    {
        $MessagetypeOne = $this->DataControl->getFieldOne("smc_code_noticetype", "noticetype_id", "noticetype_id = '{$paramArray['noticetype_id']}'");
        if ($MessagetypeOne) {
            $data = array();
            $data['noticetype_name'] = $paramArray['noticetype_name'];
            $data['noticetype_remk'] = $paramArray['noticetype_remk'];
            $data['noticetype_class'] = $paramArray['noticetype_class'];

            $field = array();
            $field['noticetype_name'] = "消息类别名称";
            $field['noticetype_remk'] = "描述";
            $field['noticetype_class'] = "类型类别";

            $noticetype_name = $this->DataControl->getFieldOne('smc_code_noticetype', 'noticetype_name', "noticetype_id = '{$paramArray['noticetype_id']}'");
            if ($paramArray['noticetype_name'] != $noticetype_name['noticetype_name']) {
                $noticetype_name = $this->DataControl->getFieldOne('smc_code_noticetype', 'noticetype_id', "noticetype_name = '{$paramArray['noticetype_name']}' and company_id = '{$paramArray['company_id']}'");
                if ($noticetype_name) {
                    ajax_return(array('error' => 1, 'errortip' => "消息类别名称已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_code_noticetype", "noticetype_id = '{$paramArray['noticetype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "消息类别名称修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置", '编辑消息类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '消息类别名称修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除消息类型
    function delMessagetypeAction($paramArray)
    {
        $noticetypeOne = $this->DataControl->getFieldOne("smc_code_noticetype", "noticetype_id", "noticetype_id = '{$paramArray['noticetype_id']}'");
        if ($noticetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("gmc_company_notice", "notice_id", "noticetype_id = '{$paramArray['noticetype_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "消息类型已被使用，无法删除!"), $this->companyOne['company_language']);
            } else {
                if ($this->DataControl->delData("smc_code_noticetype", "noticetype_id = '{$paramArray['noticetype_id']}'")) {
                    $result = array();
                    $res = array('error' => '0', 'errortip' => "删除消息类型成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置", '删除消息类型', dataEncode($paramArray));

                } else {
                    $result = array();
                    $res = array('error' => '1', 'errortip' => '删除消息类型失败', 'result' => $result);
                }
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //删除消息
    function delNoticeAction($paramArray)
    {
        $MessagetypeOne = $this->DataControl->getFieldOne("gmc_company_notice", "notice_id", "notice_id = '{$paramArray['notice_id']}'");
        if ($MessagetypeOne) {
            $data = array();
            $data['notice_status'] = '-1';

            $field = array();
            $field['notice_status'] = "状态";

            if ($this->DataControl->updateData("gmc_company_notice", "notice_id = '{$paramArray['notice_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "撤回消息成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置", '删除消息', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '撤回消息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //置顶消息
    function topNoticeAction($paramArray)
    {
        $MessagetypeOne = $this->DataControl->getFieldOne("gmc_company_notice", "notice_id", "notice_id = '{$paramArray['notice_id']}'");
        if ($MessagetypeOne) {
            $data = array();
            $data['notice_top'] = $paramArray['notice_top'];

            $field = array();
            $field['notice_top'] = '置顶';

            if ($this->DataControl->updateData("gmc_company_notice", "notice_id = '{$paramArray['notice_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "置顶消息成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '置顶消息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //取消置顶消息
    function downNoticeAction($paramArray)
    {
        $MessagetypeOne = $this->DataControl->getFieldOne("gmc_company_notice", "notice_id", "notice_id = '{$paramArray['notice_id']}'");
        if ($MessagetypeOne) {
            $data = array();
            $data['notice_top'] = '0';

            $field = array();
            $field['notice_top'] = "置顶";

            if ($this->DataControl->updateData("gmc_company_notice", "notice_id = '{$paramArray['notice_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "取消置顶消息成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '取消置顶消息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //发布集团通知
    function addCompanyNoticeAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['noticetype_id'] = $paramArray['noticetype_id'];
        $data['notice_class'] = '0';
        $data['staffer_id'] = $paramArray['staffer_id'];
        $organize = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'organize_id', "staffer_id = '{$paramArray['staffer_id']}' and school_id = 0");
        $data['notice_authority'] = $organize['organize_id'];
        $data['notice_title'] = $paramArray['notice_title'];
        $data['notice_filelist'] = $paramArray['notice_filelist'];
        $data['notice_connet'] = $paramArray['notice_connet'];
        $data['notice_createtime'] = time();

        $field = array();
        $field['company_id'] = "发布集团";
        $field['noticetype_id'] = "通知类型ID";
        $field['staffer_id'] = "发布人ID";
        $field['notice_authority'] = "发布组织";
        $field['notice_title'] = "通知标题";
        $field['notice_filelist'] = "附件明细";
        $field['notice_connet'] = "通知内容";
        $field['notice_createtime'] = "发布时间";

        if ($id = $this->DataControl->insertData('gmc_company_notice', $data)) {
            $datas = array();

            $postList = json_decode(stripslashes($paramArray['post']), true);
            foreach ($postList as $item) {
                $datas['notice_id'] = $id;
                $datas['post_id'] = $item;

                $a = $this->DataControl->getFieldOne('gmc_company_notice_postapply', 'notice_id', "notice_id = '{$id}' and post_id = '{$item}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
                }
                $this->DataControl->insertData('gmc_company_notice_postapply', $datas);
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加集团通知成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团消息中心", '添加集团消息', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加集团通知失败', 'result' => $result);
        }
        return $res;
    }

    //发布校园通知学校选择范围
    function getSchoolRange($paramArray)
    {
        $result = array();
        $data = $this->DataControl->selectClear("select d.district_cnname,d.district_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_id from gmc_company_district as d left join smc_school as s on s.district_id = d.district_id WHERE d.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0'");

        $datas = $this->DataControl->selectClear("select d.district_cnname,d.district_id from gmc_company_district as d left join smc_school as s on s.district_id = d.district_id WHERE d.company_id = '{$paramArray['company_id']}' and s.district_id <> '0' GROUP BY d.district_cnname");

        $a = array();
        $a['data'] = $data;
        $a['datas'] = $datas;

        $data = $this->tree($a);

        $result['children'] = $data;
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        return $res;
    }

    function tree($items)
    {
        $son = array();
        foreach ($items['datas'] as $k => $v) {
            $son[$k]['district_cnname'] = $v['district_cnname'];
            $son[$k]['district_id'] = $v['district_id'];
            foreach ($items['data'] as $key => $value) {
                if ($v['district_cnname'] == $value['district_cnname']) {
                    $son[$k]['children'][$key]['school_cnname'] = $value['school_cnname'];
                    $son[$k]['children'][$key]['school_id'] = $value['school_id'];
                }
            }
        }
        return $son;
    }

    function getCompanyPostRange($paramArray)
    {
        $post = $this->DataControl->selectClear("select p.post_name,p.post_id from gmc_company_post as p WHERE p.company_id = '{$paramArray['company_id']}' and post_type = '0'");
        $result = array();
        $result['post'] = $post;

        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        return $res;
    }

    function getSchoolPostRange($paramArray)
    {
        $post = $this->DataControl->selectClear("select p.post_name,p.post_id from gmc_company_post as p WHERE p.company_id = '{$paramArray['company_id']}' and post_type = '1'");
        $result = array();
        $result['post'] = $post;

        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
        return $res;
    }

    //获取消息类别
    function getNoticeType($paramArray)
    {
        $sql = "
            SELECT
                s.noticetype_id,
                s.noticetype_name
            FROM
                smc_code_noticetype AS s 
            WHERE
                s.company_id = '{$paramArray['company_id']}' and noticetype_class = '0'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["noticetype_name"] = "消息类别名称";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取消息类别成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取消息类别失败', 'result' => $result);
        }
        return $res;
    }

    //获取校园消息类别
    function getScNoticeType($paramArray)
    {
        $sql = "
            SELECT
                s.noticetype_id,
                s.noticetype_name
            FROM
                smc_code_noticetype AS s 
            WHERE
                s.company_id = '{$paramArray['company_id']}' and noticetype_class = '1'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["noticetype_name"] = "消息类别名称";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取消息类别成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取消息类别失败', 'result' => $result);
        }
        return $res;
    }

    //发布校园通知
    function addSchoolNoticeAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['noticetype_id'] = $paramArray['noticetype_id'];
        $data['notice_class'] = '1';
        $data['staffer_id'] = $paramArray['staffer_id'];
        $organize = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'organize_id', "staffer_id = '{$paramArray['staffer_id']}' and school_id = 0");
        $school = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'school_id', "staffer_id = '{$paramArray['staffer_id']}' and school_id > 0");
        $data['notice_authority'] = $organize['organize_id'];
        $data['school_id'] = $school['school_id'];
        $data['notice_title'] = $paramArray['notice_title'];
        $data['notice_filelist'] = $paramArray['notice_filelist'];
        $data['notice_connet'] = $paramArray['notice_connet'];
        $data['notice_createtime'] = time();

        $field = array();
        $field['company_id'] = "发布集团";
        $field['noticetype_id'] = "通知类型ID";
        $field['staffer_id'] = "发布人ID";
        $field['notice_authority'] = "发布组织";
        $field['notice_title'] = "通知标题";
        $field['notice_filelist'] = "附件明细";
        $field['notice_connet'] = "通知内容";
        $field['notice_createtime'] = "发布时间";

        if ($id = $this->DataControl->insertData('gmc_company_notice', $data)) {
            $datas = array();
            $postList = json_decode(stripslashes($paramArray['post']), true);
            foreach ($postList as $item) {
                $datas['notice_id'] = $id;
                $datas['post_id'] = $item;

                $a = $this->DataControl->getFieldOne('gmc_company_notice_postapply', 'notice_id', "notice_id = '{$id}' and post_id = '{$item}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
                }
                $this->DataControl->insertData('gmc_company_notice_postapply', $datas);
            }

            $datass = array();
            $schoolList = json_decode(stripslashes($paramArray['school']), true);
            foreach ($schoolList as $item) {
                $datass['notice_id'] = $id;
                $datass['school_id'] = $item;

                $a = $this->DataControl->getFieldOne('gmc_company_notice_schoolapply', 'notice_id', "notice_id = '{$id}' and school_id = '{$item}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
                }
                $this->DataControl->insertData('gmc_company_notice_schoolapply', $datass);
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加校园通知成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团消息中心", '发布校园消息', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加校园通知失败', 'result' => $result);
        }
        return $res;
    }

    //适用职务列表
    function getSetPost($paramArray)
    {
        $sql = "
        SELECT
            p.post_id,
            c.post_name
        FROM
            gmc_company_notice_postapply AS p
            LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
        WHERE
            p.notice_id = '{$paramArray['notice_id']}'";

        $PostList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
        SELECT
           COUNT(p.notice_id)
        FROM
            gmc_company_notice_postapply AS p
            LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
        WHERE
            p.notice_id = '{$paramArray['notice_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('post_id', 'post_name');
        $fieldname = $this->LgArraySwitch(array('职务ID', '职务名称'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PostList) {
            $result['list'] = $PostList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无通知职务信息", 'result' => $result);
        }

        return $res;
    }

    //适用学校列表
    function getSetSchool($paramArray)
    {
        $sql = "
        SELECT
            p.school_id,
            (case when c.school_shortname='' then c.school_cnname else c.school_shortname end) as school_cnname,
            c.school_branch
        FROM
            gmc_company_notice_schoolapply AS p
            LEFT JOIN smc_school AS c ON p.school_id = c.school_id
        WHERE
            p.notice_id = '{$paramArray['notice_id']}'";

        $SchoolList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
        SELECT
           COUNT(p.notice_id)
        FROM
            gmc_company_notice_schoolapply AS p
            LEFT JOIN smc_school AS c ON p.school_id = c.school_id
        WHERE
            p.notice_id = '{$paramArray['notice_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('school_id', 'school_branch', 'school_cnname');
        $fieldname = $this->LgArraySwitch(array('学校ID', '校区编号', '校区名称'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($SchoolList) {
            $result['list'] = $SchoolList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无通知校园", 'result' => $result);
        }

        return $res;
    }

    //删除适用学校
    function delSetSchoolAction($paramArray)
    {
        if ($this->DataControl->delData("gmc_company_notice_schoolapply", "notice_id = '{$paramArray['notice_id']}' and school_id = '{$paramArray['school_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除适用学校成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除适用学校失败', 'result' => $result);
        }
        return $res;
    }

    //删除适用职务
    function delSetPostAction($paramArray)
    {
        if ($this->DataControl->delData("gmc_company_notice_postapply", "notice_id = '{$paramArray['notice_id']}' and post_id = '{$paramArray['post_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除适用职务成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除适用职务失败', 'result' => $result);
        }
        return $res;
    }


    //删除消息
    function delMessageAction($paramArray)
    {
        if ($this->DataControl->delData("gmc_company_notice", "notice_id = '{$paramArray['notice_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除消息成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团消息中心", '删除消息', dataEncode($paramArray));

        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除消息失败', 'result' => $result);
        }
        return $res;
    }

    //编辑集团消息
    function updateCompanyNoticeAction($paramArray)
    {
        $NoticeOne = $this->DataControl->getFieldOne("gmc_company_notice", "notice_id", "notice_id = '{$paramArray['notice_id']}'");
        if ($NoticeOne) {
            $data = array();
            $data['noticetype_id'] = $paramArray['noticetype_id'];
            $data['notice_title'] = $paramArray['notice_title'];
            $data['notice_connet'] = $paramArray['notice_connet'];
            $data['notice_createtime'] = time();

            $field = array();
            $field['noticetype_id'] = "通知类型ID";
            $field['notice_title'] = "通知标题";
            $field['notice_connet'] = "通知内容";
            $field['notice_updatetime'] = "更改时间";

            if ($this->DataControl->updateData("gmc_company_notice", "notice_id = '{$paramArray['notice_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑集团消息成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团消息中心", '编辑集团消息', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑集团消息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取消息详情
    function getMessageDetail($paramArray)
    {
        $sql = "
            SELECT
                s.noticetype_id,
                s.notice_title,
                s.notice_connet
            FROM
                gmc_company_notice AS s
            WHERE
                s.notice_id = '{$paramArray['notice_id']}' ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["noticetype_id"] = "通知类型ID";
        $field["notice_title"] = "通知标题";
        $field["notice_connet"] = "通知内容";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取消息详情成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取消息详情失败', 'result' => $result);
        }
        return $res;
    }

    function getPostroleApi($paramArray)
    {
        $sql = "
            SELECT r.postrole_id,r.postrole_name from gmc_company_postrole as r WHERE r.company_id = '{$paramArray['company_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["postrole_id"] = "集团角色id";
        $field["postrole_name"] = "集团角色名称";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '集团角色查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '集团角色查看失败', 'result' => $result);
        }
        return $res;
    }


    function getPostpartApi($paramArray)
    {
        $sql = "
            SELECT r.postpart_id,r.postpart_name from smc_school_postpart as r WHERE r.company_id = '{$paramArray['company_id']}' and r.school_id = '0' ORDER BY r.postpart_id DESC";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["postpart_id"] = "校园角色id";
        $field["postpart_name"] = "校园角色名称";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '校园角色查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '校园角色查看失败', 'result' => $result);
        }
        return $res;
    }


    function getCoRoleApi($paramArray)
    {
        $sql = "
            SELECT r.postrole_id,r.postlevel_id,p.postpart_isgmccrm,p.postpart_gmccrmlevel 
            from gmc_company_post as r 
            left join gmc_company_postrole as p ON r.postrole_id = p.postrole_id 
            WHERE r.company_id = '{$paramArray['company_id']}' and r.post_type = '0' and r.post_id = '{$paramArray['post_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["postrole_id"] = "集团角色id";
        $field["postlevel_id"] = "默认职等";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '集团角色查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '集团角色查看失败', 'result' => $result);
        }
        return $res;
    }

    function getScPartApi($paramArray)
    {
        $sql = "
            SELECT r.postpart_id,r.postlevel_id from gmc_company_post as r WHERE r.company_id = '{$paramArray['company_id']}' and post_type = '1' and r.post_id = '{$paramArray['post_id']}'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["postpart_id"] = "校园角色id";
        $field["postlevel_id"] = "默认职等";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '校园角色查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '校园角色查看失败', 'result' => $result);
        }
        return $res;
    }

    //薪资模块管理
    function getSalarymoduleList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.salarymodule_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "
             SELECT
                s.salarymodule_id,
                s.company_id,
                s.salarymodule_name,
                s.salarymodule_remk,
                s.salarymodule_code,
                s.salarymodule_isautomatic,
                s.salarymodule_isautomatic as salarymodule_isautomatic_status,
                s.salarymodule_isenable,
                s.salarymodule_isenable as salarymodule_isenable_status,
                s.salarymodule_isfixed,
                s.salarymodule_isfixed as salarymodule_isfixed_status
            FROM
                smc_code_salarymodule AS s 
            WHERE
                {$datawhere} and (s.company_id = 0 or s.company_id = '{$paramArray['company_id']}')
            ORDER BY
                s.salarymodule_id DESC 
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        if ($postList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            foreach ($postList as &$val) {
                $val['salarymodule_isautomatic_status'] = $status[$val['salarymodule_isautomatic_status']];
                $val['salarymodule_isenable_status'] = $status[$val['salarymodule_isenable_status']];
                $val['salarymodule_isfixed_status'] = $status[$val['salarymodule_isfixed_status']];
            }

        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.salarymodule_id)
            FROM
                smc_code_salarymodule AS s 
            WHERE
                {$datawhere} and (s.company_id = 0 or s.company_id = '{$paramArray['company_id']}')");
        $allnums = $all_num[0][0];

        $fieldstring = array('salarymodule_name ', 'salarymodule_code', 'salarymodule_isautomatic_status', 'salarymodule_isenable_status', 'salarymodule_isfixed_status', 'salarymodule_remk', 'salarymodule_isautomatic', 'salarymodule_isenable', 'salarymodule_isfixed');
        $fieldname = $this->LgArraySwitch(array('模块名称', '职务编号', '是否自动计算', '是否启用', '固定模块', '模块备注', '是否自动计算', '是否启用', '固定模块'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "0", "0", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "0", "0", "0");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无薪资模块信息", 'result' => $result);
        }

        return $res;
    }

    //添加模块管理
    function addSalarymoduleAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['salarymodule_code'] = $paramArray['salarymodule_code'];
        $data['salarymodule_name'] = $paramArray['salarymodule_name'];
        $data['salarymodule_isfixed'] = $paramArray['salarymodule_isfixed'];
        $data['salarymodule_isautomatic'] = $paramArray['salarymodule_isautomatic'];
        $data['salarymodule_isenable'] = $paramArray['salarymodule_isenable'];
        $data['salarymodule_remk'] = $paramArray['salarymodule_remk'];

        $field = array();
        $field['salarymodule_code'] = "模块编号";
        $field['salarymodule_name'] = "模块名称";
        $field['salarymodule_isfixed'] = "固定模块";
        $field['salarymodule_isautomatic'] = "自动计算";
        $field['salarymodule_isenable'] = "模块名称";
        $field['salarymodule_remk'] = "模块备注";
        $field['company_id'] = "所属公司";

        $reason_note = $this->DataControl->getFieldOne('smc_code_salarymodule', 'salarymodule_id', "salarymodule_code = '{$paramArray['salarymodule_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($reason_note) {
            ajax_return(array('error' => 1, 'errortip' => "模块编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_salarymodule', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加模块管理成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加模块管理失败', 'result' => $result);
        }
        return $res;
    }

    //编辑模块管理
    function updateSalarymoduleAction($paramArray)
    {
        $NoticeOne = $this->DataControl->getFieldOne("smc_code_salarymodule", "salarymodule_id", "salarymodule_id = '{$paramArray['salarymodule_id']}'");
        if ($NoticeOne) {
            $data = array();
            $data['salarymodule_code'] = $paramArray['salarymodule_code'];
            $data['salarymodule_name'] = $paramArray['salarymodule_name'];
            $data['salarymodule_isfixed'] = $paramArray['salarymodule_isfixed'];
            $data['salarymodule_isautomatic'] = $paramArray['salarymodule_isautomatic'];
            $data['salarymodule_isenable'] = $paramArray['salarymodule_isenable'];
            $data['salarymodule_remk'] = $paramArray['salarymodule_remk'];

            $field = array();
            $field['salarymodule_code'] = "模块编号";
            $field['salarymodule_name'] = "模块名称";
            $field['salarymodule_isfixed'] = "固定模块";
            $field['salarymodule_isautomatic'] = "自动计算";
            $field['salarymodule_isenable'] = "模块名称";
            $field['salarymodule_remk'] = "模块备注";

            if ($this->DataControl->updateData("smc_code_salarymodule", "salarymodule_id = '{$paramArray['salarymodule_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑薪资模块成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑薪资模块失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除编辑模块管理
    function delSalarymoduleAction($paramArray)
    {
        if ($this->DataControl->delData("smc_code_salarymodule", "salarymodule_id = '{$paramArray['salarymodule_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除薪资模块成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除薪资模块管理', 'result' => $result);
        }
        return $res;
    }

    function trees($items)
    {
        $son = array();
        $count = -1;
        $counts = -1;

        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['module_id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    $son[$k]['icon'] = $v['module_icon'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $urls = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' order by module_weight ASC limit 0,1");
                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['module_id'] = $value['module_id'];
                            if ($urls) {
                                $son[$k]['children'][$key]['url'] = $urls[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];

                                }
                            }
                        }

                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }

    //集团操作日志
    function getComWorkLog($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname like '%{$paramArray['keyword']}%' or w.worklog_type like '%{$paramArray['keyword']}%' or w.worklog_module like '%{$paramArray['keyword']}%' or w.worklog_content like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] != '') {
            $activity_starttime = strtotime($paramArray['starttime']);
            $datawhere .= " and w.worklog_time >= '{$activity_starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] != '') {
            $activity_endtime = strtotime($paramArray['endtime']) + 86400;
            $datawhere .= " and w.worklog_time <= '{$activity_endtime}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                w.worklog_id,
                w.worklog_module,
                w.worklog_type,
                w.worklog_content,
                w.worklog_ip,
                FROM_UNIXTIME(w.worklog_time,'%Y-%m-%d %H:%i:%s') as worklog_time,
                s.staffer_cnname,
                s.staffer_enname
            FROM
                gmc_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and w.company_id = '{$paramArray['company_id']}'
            ORDER BY
                w.worklog_id DESC 
            LIMIT {$pagestart},{$num}";

        $WorkList = $this->DataControl->selectClear($sql);

        foreach ($WorkList as &$val) {
            $val['worklog_type'] = $this->LgStringSwitch($val['worklog_type']);
            $val['worklog_module'] = $this->LgStringSwitch($val['worklog_module']);
            $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(w.worklog_id)
            FROM
                gmc_staffer_worklog AS w
                 LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and w.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('worklog_module ', 'worklog_type', 'worklog_ip', 'staffer_cnname', 'worklog_time');
        $fieldname = $this->LgArraySwitch(array('操作模块', '操作类型', 'ip记录', '操作人', '操作时间'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($WorkList) {
            $result['list'] = $WorkList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无集团操作日志信息", 'result' => $result);
        }

        return $res;
    }

    //CRM操作日志
    function getCrmWorkLog($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname  like '%{$paramArray['keyword']}%' or w.worklog_module like '%{$paramArray['keyword']}%' or worklog_content like '%client_id%5B0%5D={$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and w.school_id ='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] != '') {
            $activity_starttime = strtotime($paramArray['starttime']);
            $datawhere .= " and w.worklog_time >= '{$activity_starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] != '') {
            $activity_endtime = strtotime($paramArray['endtime']) + 86400;
            $datawhere .= " and w.worklog_time <= '{$activity_endtime}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                w.worklog_id,
                w.worklog_module,
                w.worklog_type,
                w.worklog_content,
                w.worklog_ip,
                FROM_UNIXTIME(w.worklog_time,'%Y-%m-%d %H:%i:%s') as worklog_time,
                s.staffer_cnname,
                s.staffer_enname,
                (case when c.school_shortname='' then c.school_cnname else c.school_shortname end) as school_cnname
            FROM
                crm_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id
                left join smc_school as c on c.school_id = w.school_id
            WHERE
                {$datawhere} and w.company_id = '{$paramArray['company_id']}'
            ORDER BY
                w.worklog_id DESC 
            LIMIT {$pagestart},{$num}";

        $WorkList = $this->DataControl->selectClear($sql);

        foreach ($WorkList as &$val) {
            $val['worklog_type'] = $this->LgStringSwitch($val['worklog_type']);
            $val['worklog_module'] = $this->LgStringSwitch($val['worklog_module']);
            $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(w.worklog_id)
            FROM
                crm_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id
                left join smc_school as c on c.school_id = w.school_id
            WHERE
                {$datawhere} and w.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('school_cnname ', 'worklog_module ', 'worklog_type', 'worklog_ip', 'staffer_cnname', 'worklog_time');
        $fieldname = $this->LgArraySwitch(array('学校', '操作模块', '操作类型', 'ip记录', '操作人', '操作时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($WorkList) {
            $result['list'] = $WorkList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无CRM操作日志信息", 'result' => $result);
        }

        return $res;
    }

    //校务操作日志
    function getScWorkLog($paramArray)
    {

        $datawhere = "w.company_id = '{$paramArray['company_id']}' and w.worklog_id>='2085038' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.staffer_cnname  like '%{$paramArray['keyword']}%' or w.worklog_module like '%{$paramArray['keyword']}%' or w.worklog_type like '%{$paramArray['keyword']}%' or w.worklog_content like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and w.school_id ='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] != '') {
            $activity_starttime = strtotime($paramArray['starttime']);
            $datawhere .= " and w.worklog_time >= '{$activity_starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] != '') {
            $activity_endtime = strtotime($paramArray['endtime']) + 86400;
            $datawhere .= " and w.worklog_time <= '{$activity_endtime}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
           SELECT
                w.worklog_id,
                w.worklog_module,
                w.worklog_type,
                w.worklog_ip,
                FROM_UNIXTIME(w.worklog_time,'%Y-%m-%d %H:%i:%s') as worklog_time,
                s.staffer_cnname,
                s.staffer_enname,
                (case when c.school_shortname='' then c.school_cnname else c.school_shortname end) as school_cnname
            FROM
                smc_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id
                left join smc_school as c on c.school_id = w.school_id
            WHERE
                {$datawhere}
            ORDER BY
                w.worklog_id DESC 
            LIMIT {$pagestart},{$num}";

        $WorkList = $this->DataControl->selectClear($sql);

        foreach ($WorkList as &$val) {
            $val['worklog_type'] = $this->LgStringSwitch($val['worklog_type']);
            $val['worklog_module'] = $this->LgStringSwitch($val['worklog_module']);
            $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(w.worklog_id)
            FROM
                smc_staffer_worklog AS w
                LEFT JOIN smc_staffer AS s ON w.staffer_id = s.staffer_id
                left join smc_school as c on c.school_id = w.school_id
            WHERE
                {$datawhere}");
        $allnums = $all_num[0][0];

        $fieldstring = array('school_cnname ', 'worklog_module ', 'worklog_type', 'worklog_ip', 'staffer_cnname', 'worklog_time');
        $fieldname = $this->LgArraySwitch(array('学校', '操作模块', '操作类型', 'ip记录', '操作人', '操作时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($WorkList) {
            $result['list'] = $WorkList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校务操作日志信息", 'result' => $result);
        }

        return $res;
    }

    //推送消息配置列表
    function getWxSendList($paramArray)
    {

        $datawhere = " 1 ";
        $datawheres = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (m.masterplate_name  like '%{$paramArray['keyword']}%' or m.masterplate_rule like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['masterplate_class']) && $paramArray['masterplate_class'] !== "") {
            $datawhere .= " and m.masterplate_class ='{$paramArray['masterplate_class']}'";
        }

        if ($paramArray['status'] == '0') {
            if (isset($paramArray['status']) && $paramArray['status'] !== "") {
                $datawheres .= " and status_num !='{$paramArray['status']}'";
            }
        }

        if ($paramArray['status'] == '1') {
            if (isset($paramArray['status']) && $paramArray['status'] !== "") {
                $datawheres .= " and status_num <'{$paramArray['status']}'";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$paramArray['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $xid = $id['company_id'];
        } else {
            $xid = '0';
        }

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$paramArray['company_id']}' and wxchatnumber_class = '0'");
        if ($id) {
            $jid = $id['company_id'];
        } else {
            $jid = '0';
        }

        $sql = "
           SELECT
                ifnull( a.company_id, 0 ) as status,
                ifnull( a.company_id, 0 ) as status_num,
                m.masterplate_id,
                m.masterplate_class,
                m.masterplate_name,
                m.masterplate_wxid,
                m.masterplate_rule
            FROM
                gmc_company_masterplate AS m
                LEFT JOIN eas_masterplateapply AS a ON m.masterplate_id = a.masterplate_id
            WHERE
                {$datawhere} and ((m.company_id = '{$xid}' and masterplate_class = '1') or (m.company_id = '{$jid}' and masterplate_class = '0'))
            HAVING {$datawheres}
            ORDER BY
                m.masterplate_id DESC 
            LIMIT {$pagestart},{$num}";

        $WorkList = $this->DataControl->selectClear($sql);

        if ($WorkList) {
            $status = $this->LgArraySwitch(array("0" => "叮铛助教", "1" => "叮铛助学"));
            foreach ($WorkList as &$val) {
                $val['masterplate_class'] = $status[$val['masterplate_class']];
                if ($val['status'] == '0') {
                    $val['status'] = '1';
                } else {
                    $val['status'] = '0';
                }
            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*)
            FROM
               (SELECT
                ifnull( a.company_id, 0 ) as status,
                ifnull( a.company_id, 0 ) as status_num,
                m.masterplate_id,
                m.masterplate_class,
                m.masterplate_name,
                m.masterplate_wxid,
                m.masterplate_rule
            FROM
                gmc_company_masterplate AS m
                LEFT JOIN eas_masterplateapply AS a ON m.masterplate_id = a.masterplate_id
            WHERE
                {$datawhere} and ((m.company_id = '{$xid}' and masterplate_class = '1') or (m.company_id = '{$jid}' and masterplate_class = '0'))
            HAVING {$datawheres}) as a ");
        $allnums = $all_num[0][0];

        $fieldstring = array('masterplate_name ', 'masterplate_rule ', 'masterplate_class', 'status');
        $fieldname = $this->LgArraySwitch(array('模版名称', '推送规则', '所属端口', '是否启用'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");
        $fieldisswitch = array("0", "0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($WorkList) {
            $result['list'] = $WorkList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无推送消息配置列表", 'result' => $result);
        }

        return $res;
    }

    //改变推送启用状态
    function updateWxSendStatusAction($paramArray)
    {
        $data = array();
        if ($paramArray['status'] == '1') {
            if ($this->DataControl->delData("eas_masterplateapply", "masterplate_id = '{$paramArray['masterplate_id']}'")) {
                $result = array();
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变推送启用状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变推送启用状态失败', 'result' => $result);
            }
        } else {
            $data['masterplate_id'] = $paramArray['masterplate_id'];
            $data['company_id'] = $paramArray['company_id'];
            $this->DataControl->insertData("eas_masterplateapply", $data);
            $result = array();
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "改变推送启用状态成功", 'result' => $result);

        }

        return $res;
    }

    //集团操作日志JSON
    function getComWorkLogJSON($paramArray)
    {
        $sql = "
            SELECT
                w.worklog_content
            FROM
                gmc_staffer_worklog AS w
            WHERE
                w.worklog_id = '{$paramArray['worklog_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["worklog_content"] = "JSON";
        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取JSON成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取JSON失败', 'result' => $result);
        }
        return $res;
    }

    //CRM操作日志JSON
    function getCrmWorkLogJSON($paramArray)
    {
        $sql = "
            SELECT
                w.worklog_content
            FROM
                crm_staffer_worklog AS w
            WHERE
                w.worklog_id = '{$paramArray['worklog_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["worklog_content"] = "JSON";
        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取JSON成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取JSON失败', 'result' => $result);
        }
        return $res;
    }

    //校务操作日志JSON
    function getScWorkLogJSON($paramArray)
    {
        $sql = "
            SELECT
                w.worklog_content
            FROM
                smc_staffer_worklog AS w
            WHERE
                w.worklog_id = '{$paramArray['worklog_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["worklog_content"] = "JSON";
        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取JSON成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取JSON失败', 'result' => $result);
        }
        return $res;
    }

    //校园角色下拉框列表
    function getScPostpartApi($paramArray)
    {
        $sql = "
            SELECT p.postpart_id,p.postpart_name from smc_school_postpart as p WHERE p.company_id = '{$paramArray['company_id']}' and school_id = '0'";
        $postroleDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["postpart_id"] = "校园角色id";
        $field["postpart_name"] = "校园角色名称";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '校园角色查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '校园角色查看失败', 'result' => $result);
        }
        return $res;
    }

    //薪资模块管理

    function getPerformanceList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cp.performance_name like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cp.performance_id,cp.company_id,cp.performance_name,cp.performance_class,cp. performance_iscalculated
              from gmc_code_performance as cp 
              where {$datawhere} and (cp.company_id=0 or cp.company_id='{$this->company_id}')
              order by cp.performance_id asc
              limit {$pagestart},{$num}
              ";

        $performanceList = $this->DataControl->selectClear($sql);

        if (!$performanceList) {
            $this->error = true;
            $this->errortip = "无业绩明细";
            return false;
        }

        $class = $this->LgArraySwitch(array("0" => "主招", "1" => "辅招", "2" => "推荐"));

        foreach ($performanceList as &$performanceOne) {
            $performanceOne['performance_class_name'] = $class[$performanceOne['performance_class']];
        }

        $count_sql = "select cp.performance_id
              from gmc_code_performance as cp 
              where {$datawhere} and (cp.company_id=0 or cp.company_id='{$this->company_id}')";
        $db_nums = $this->DataControl->selectClear($count_sql);
        $allnum = $db_nums ? count($db_nums) : 0;

        $data = array();
        $data['allnum'] = $allnum;

        $data['list'] = $performanceList;

        return $data;

    }

    function addPerformance($request)
    {

        if ($this->DataControl->getFieldOne("gmc_code_performance", "performance_id", "(company_id='{$this->company_id}' and performance_name='{$request['performance_name']}' ) or (company_id=0 and performance_name='{$request['performance_name']}')")) {
            $this->error = true;
            $this->errortip = "业绩明细已存在";
            return false;
        }

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['performance_name'] = $request['performance_name'];
        $data['performance_class'] = 3;
        $data['performance_iscalculated'] = $request['performance_iscalculated'];

        $this->DataControl->insertData("gmc_code_performance", $data);

        $this->oktip = '添加成功';
        return true;

    }

    function editPerformance($request)
    {

        if ($this->DataControl->getFieldOne("gmc_code_performance", "performance_id", "(company_id='{$this->company_id}' and performance_name='{$request['performance_name']}' ) or (company_id=0 and performance_name='{$request['performance_name']}') and performance_id<>'{$request['performance_id']}'")) {
            $this->error = true;
            $this->errortip = "业绩明细已存在";
            return false;
        }

        $data = array();
        if (isset($request['performance_name']) && $request['performance_name'] != '') {
            $data['performance_name'] = $request['performance_name'];
        }

        if (isset($request['performance_iscalculated']) && $request['performance_iscalculated'] != '') {
            $data['performance_iscalculated'] = $request['performance_iscalculated'];
        }

        if (!$data) {
            $this->error = true;
            $this->errortip = "无参数";
            return false;
        }

        $this->DataControl->updateData("gmc_code_performance", "performance_id='{$request['performance_id']}'", $data);

        $this->oktip = '编辑成功';
        return true;

    }

    function delPerformance($request)
    {

        if ($this->DataControl->getFieldOne("smc_staffer_achieve", "achieve_id", "performance_id='{$request['performance_id']}'")) {
            $this->error = true;
            $this->errortip = "已被使用,不可删除";
            return false;
        }

        if ($this->DataControl->delData("gmc_code_performance", "performance_id='{$request['performance_id']}'")) {
            $this->oktip = '删除成功';
            return true;
        } else {
            $this->error = true;
            $this->errortip = "服务器错误";
            return false;
        }
    }

    //集团 短信 日志
    function getGmcMislog($paramArray)
    {
        $datawhere = " m.company_id = '{$paramArray['company_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (m.mislog_tilte like '%{$paramArray['keyword']}%' or m.mislog_mobile like '%{$paramArray['keyword']}%' )";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] != '') {
            $activity_starttime = strtotime($paramArray['starttime']);
            $datawhere .= " and m.mislog_time >= '{$activity_starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] != '') {
            $activity_endtime = strtotime($paramArray['endtime']) + 86400;
            $datawhere .= " and m.mislog_time <= '{$activity_endtime}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " SELECT m.mislog_id, m.company_id, m.mislog_tilte, m.mislog_mobile, m.mislog_sendcode, m.mislog_mistxt, m.mislog_time
            FROM gmc_mislog AS m 
            WHERE {$datawhere}
            ORDER BY m.mislog_id DESC 
            LIMIT {$pagestart},{$num}";

        $DataList = $this->DataControl->selectClear($sql);

        foreach ($DataList as &$val) {
            $val['mislog_time'] = date("Y-m-d H:i:s",$val['mislog_time']);
        }

        $all_num = $this->DataControl->select("
            SELECT COUNT(m.mislog_id)
            FROM gmc_mislog AS m 
            WHERE {$datawhere} ");
        $allnums = $all_num[0][0];

        $fieldstring = array('mislog_tilte ', 'mislog_mobile', 'mislog_sendcode', 'mislog_mistxt', 'mislog_time');
        $fieldname = $this->LgArraySwitch(array('标题', '手机号', '验证码', '发送内容', '发送时间'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($DataList) {
            $result['list'] = $DataList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无集团短信信息", 'result' => $result);
        }

        return $res;
    }

    //添加试用
    function addApplyAction($paramArray)
    {
        $data = array();
        $data['apply_cnname'] = $paramArray['apply_cnname'];
        $data['apply_mobile'] = $paramArray['apply_mobile'];
        $data['apply_domain'] = $paramArray['apply_domain'];
        $data['apply_companyname'] = $paramArray['apply_companyname'];
        $data['apply_address'] = $paramArray['apply_address'];
        $data['apply_createtime'] = time();

        $field = array();
        $field['apply_cnname'] = "姓名";
        $field['apply_mobile'] = "手机号";
        $field['apply_domain'] = "领域";
        $field['apply_companyname'] = "学校或机构名称";
        $field['apply_address'] = "联系地址";

        if ($this->DataControl->insertData('gmc_apply', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "申请试用成功", 'result' => $result);

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '申请试用失败', 'result' => $result);
        }
        return $res;
    }
    //改变监管
    function updateSuperviseAction($paramArray)
    {
        $data = array();
        $data['companies_issupervise'] = $paramArray['companies_issupervise'];

        if ($this->DataControl->updateData('gmc_code_companies', "companies_id = '{$paramArray['companies_id']}'", $data)) {
            $result = array();
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
        }
        return $res;
    }
}
