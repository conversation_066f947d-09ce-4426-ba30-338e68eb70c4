<?php


namespace Model\Gmc;

class PackageModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    /**
     *  获取课件包列表
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function getPackageList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ( pk.package_name  like '%{$request['keyword']}%')";
        }
        if (isset($request['class_type']) && $request['class_type'] !== '') {
            $datawhere .= " and pk.class_type = '{$request['class_type']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $dataList = $this->DataControl->selectClear("
            select pk.package_id,pk.package_name,pk.class_type,pk.lesson_num,pk.forward_lesson_num,pk.backward_lesson_num,
            (select count(course_id) from eas_coursepackage_apply as ay where ay.package_id = pk.package_id) as course_num
            from eas_coursepackage as pk
            where pk.company_id='{$this->company_id}' and {$datawhere}   order by pk.package_id DESC
            limit {$pagestart},{$num}");
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$value) {
                $value['class_type_name'] = $value['class_type'] == 1 ? "子班" : "父班";
            }
        }
        $allnum = $this->DataControl->selectOne("select count(pk.package_id) as package_num from eas_coursepackage as pk where  pk.company_id='{$this->company_id}' and {$datawhere} ");
        $data = array();
        $data['allnum'] = $allnum['package_num'];
        $data['list'] = $dataList;
        return $data;
    }

    //新增课件包
    function addPackageAction($request)
    {
        $data = array();
        $data['package_name'] = $request['package_name'];
        $data['class_type'] = $request['class_type'];
        $data['company_id'] = $this->company_id;
        $data['lesson_num'] = $request['lesson_num'];
        $data['forward_lesson_num'] = $request['forward_lesson_num'];
        $data['backward_lesson_num'] = $request['backward_lesson_num'];
        if ($id = $this->DataControl->insertData("eas_coursepackage", $data)) {
            $this->error = 0;
            $this->errortip = '新增成功';
            $data['package_id'] = $id;
            return $data;
        } else {
            $this->error = 1;
            $this->errortip = '新增失败';
            return array();
        }
    }

    //编辑课件包
    function editPackageAction($request)
    {
        $data = array();
        $data['package_name'] = $request['package_name'];
        $data['class_type'] = $request['class_type'];
        $data['company_id'] = $this->company_id;
        $data['lesson_num'] = $request['lesson_num'];
        $data['forward_lesson_num'] = $request['forward_lesson_num'];
        $data['backward_lesson_num'] = $request['backward_lesson_num'];
        if ($this->DataControl->updateData("eas_coursepackage", "package_id='{$request['package_id']}'", $data)) {
            $this->error = 0;
            $this->errortip = '新增成功';
            return true;
        } else {
            $this->error = 1;
            $this->errortip = '新增失败';
            return false;
        }

    }

    //    课件包适配班别
    function batchCourseAction($request)
    {
        if (!$request['package_id']) {
            $this->error = 1;
            $this->errortip = '请选择课件包';
            return false;
        }
        if (isset($request['course_json']) && $request['course_json']) {
            $course_array = json_decode(stripslashes($request['course_json']), true);

            if (is_array($course_array)) {
                foreach ($course_array as $value) {
                    $data = array();
                    $data['course_id'] = $value['course_id'];
                    $data['package_id'] = $request['package_id'];
                    $this->DataControl->insertData('eas_coursepackage_apply', $data);
                }
            }
            $this->error = 0;
            $this->errortip = '适配成功';
            return false;
        } else {
            $this->error = 1;
            $this->errortip = '请选择课程别';
            return false;
        }
    }

    //获取课件包的适配的课程
    function getApplyPackageCourseList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ( pk.package_name  like '%{$request['keyword']}%')";
        }
        if (isset($request['class_type']) && $request['class_type'] !== '') {
            $datawhere .= " and pk.class_type = '{$request['class_type']}'";
        }
//        if (isset($request['p']) && $request['p'] !== '') {
//            $page = $request['p'];
//        } else {
//            $page = '1';
//        }
////        if (isset($request['num']) && $request['num'] !== '') {
////            $num = $request['num'];
////        } else {
////            $num = '10';
////        }
//        $pagestart = ($page - 1) * $num;

        $dataList = $this->DataControl->selectClear("
            select c.course_cnname,c.course_branch,c.course_id,pl.package_id
            from eas_coursepackage_apply as pl
            inner join smc_course as c ON  pl.course_id=c.course_id
            where pl.package_id = '{$request['package_id']}' and {$datawhere}
            ");
        if (!$dataList) {
            $dataList = array();
        }
        $allnum = $this->DataControl->selectOne("select count(pl.apply_id)  as apply_num  from eas_coursepackage_apply as pl
            inner join smc_course as c ON  pl.course_id=c.course_id
            where pl.package_id = '{$request['package_id']}' ");
        $data = array();
        $data['allnum'] = $allnum['apply_num'];
        $data['list'] = $dataList;
        return $data;
    }

    //获取与课件包课次的对应的课程别
    function getPackageCourseList($request)
    {
        $datawhere = " c.course_status =1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ( c.course_branch  like '%{$request['keyword']}%' or c.course_cnname  like '%{$request['keyword']}%')";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and c.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and c.coursecat_id='{$request['coursecat_id']}'";
        }

//        if (isset($request['p']) && $request['p'] !== '') {
//            $page = $request['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($request['num']) && $request['num'] !== '') {
//            $num = $request['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;
        $packageOne = $this->DataControl->getFieldOne("eas_coursepackage", "lesson_num", "package_id='{$request['package_id']}' and company_id='{$this->company_id}'");
        $dataList = $this->DataControl->selectClear("select c.course_id,c.course_branch,c.course_cnname  from smc_course as c where  c.company_id = '{$this->company_id}'  and  {$datawhere} and
        c.course_id not in (select p.course_id from eas_coursepackage_apply as p where p.package_id ='{$request['package_id']}' )
      
         ");
        if (!$dataList) {
            $dataList = array();
        }

        $allnum = $this->DataControl->selectOne("select count(c.course_id) as course_num  from smc_course as c where  c.company_id = '{$this->company_id}' and c.course_classnum = '{$packageOne['lesson_num']}' and  {$datawhere} and
        c.course_id not in (select p.course_id from eas_coursepackage_apply as p where p.package_id ='{$request['package_id']}' )");

        $data = array();
        $data['allnum'] = $allnum['course_num'];
        $data['list'] = $dataList;
        return $data;
    }

    /**
     * 删除课件包的课程
     * author: ling
     * 对应接口文档 0001
     */
    function delPackageCourseAction($request)
    {
        $this->DataControl->delData("eas_coursepackage_apply", "course_id='{$request['course_id']}' and package_id='{$request['package_id']}'");
        $this->error = 0;
        $this->errortip = '移除成功';
        return false;
    }

    /**
     * 课件包课次管理
     * author: ling
     * 对应接口文档 0001
     */
    function getPackageHourList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ( ps.lesson_name  like '%{$request['keyword']}%')";
        }

        $info = array();
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $packageOne = $this->DataControl->getFieldOne("eas_coursepackage", "lesson_num,package_name", "package_id='{$request['package_id']}' and company_id='{$this->company_id}'");
        $info['package_name'] = $packageOne['package_name'];
        $lessontimeOne = $this->DataControl->selectOne("select lesson_id from eas_coursepackage_lesson as ps where ps.package_id = '{$request['package_id']}' limit 0,1");
        if (!$lessontimeOne && $packageOne['lesson_num'] > 0) {
            for ($i = 1; $i <= $packageOne['lesson_num']; $i++) {
                if ($this->DataControl->getFieldOne("eas_coursepackage_lesson", "lesson_id", "package_id='{$request['package_id']}' and lesson_sort ='{$i}' ")) {
                    continue;
                }
                $lessontimeData = array();
                $lessontimeData['package_id'] = $request['package_id'];
                $lessontimeData['lesson_name'] = "Lesson " . $i;
                $lessontimeData['lesson_sort'] = $i;
                $lessontimeData['is_synchro'] = 1;
                $this->DataControl->insertData("eas_coursepackage_lesson", $lessontimeData);
            }
        }
        $dataList = $this->DataControl->selectClear("select ps.lesson_id,ps.package_id,ps.lesson_name,ps.lesson_id,ps.lesson_sort,is_synchro, 
        (select count(word_id) from  eas_coursepackage_lessonword as  w where w.package_id = ps.package_id and w.lesson_id = ps.lesson_id) as word_num
        from eas_coursepackage_lesson as ps
        where ps.package_id='{$request['package_id']}' and {$datawhere} limit {$pagestart},{$num}");
        $lessonOne = $this->DataControl->selectOne("select lesson_sort,lesson_id from eas_coursepackage_lesson as l where l.package_id='{$request['package_id']}' order by lesson_sort DESC limit 0,1 ");
        if (!$dataList) {
            $dataList = array();
        } else {
            foreach ($dataList as &$value) {
                $value['is_synchro_name'] = $value['is_synchro'] == 1 ? '是' : '否';
                if ($value['lesson_id'] == $lessonOne['lesson_id']) {
                    $value['is_del'] = 1;
                } else {
                    $value['is_del'] = 0;
                }
            }
        }
        $allnum = $this->DataControl->selectOne("select count(ps.lesson_id) as lessontimes_num  from eas_coursepackage_lesson as ps where ps.package_id='{$request['package_id']}' and {$datawhere}");
        $info['lesson_sort'] = $lessonOne['lesson_sort'] + 1;
        $data = array();
        $data['allnum'] = $allnum['lessontimes_num'];
        $data['list'] = $dataList;
        $data['data'] = $info;
        return $data;
    }

    /**
     *  新增单节课次
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function addPackageLessonAction($request)
    {
        if (!$request['package_id']) {
            $this->error = 1;
            $this->errortip = "请选择课件包";
            return false;
        }
        $lessonOne = $this->DataControl->selectOne("select lesson_sort from eas_coursepackage_lesson as l where l.package_id='{$request['package_id']}' order by lesson_sort DESC limit 0,1 ");
        $data = array();
        $data['package_id'] = $request['package_id'];
        $data['lesson_sort'] = $lessonOne['lesson_sort'] + 1;
        $data['lesson_name'] = $request['lesson_name'];
        $data['is_synchro'] = $request['is_synchro'];
        $bool = $this->DataControl->insertData("eas_coursepackage_lesson", $data);
        if ($bool) {
            $lessonNum = $this->DataControl->selectOne("select count(lesson_id) as lesson_num from eas_coursepackage_lesson where package_id='{$request['package_id']}'");
            if ($lessonNum) {
                $lessndata = array();
                $lessndata['lesson_num'] = $lessonNum['lesson_num'];
                $this->DataControl->updateData("eas_coursepackage", "package_id='{$request['package_id']}'", $lessndata);
            }
            $this->error = 0;
            $this->errortip = "新增成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "新增失败";
            return false;
        }
    }

    /**
     * 删除课件课次
     * author: ling
     * 对应接口文档 0001
     */
    function delLessonTimeAction($request)
    {
        $lessonOne = $this->DataControl->selectOne("select lesson_sort,lesson_id from eas_coursepackage_lesson as l where l.package_id='{$request['package_id']}' order by lesson_sort DESC limit 0,1 ");
        if ($lessonOne['lesson_id'] != $request['lesson_id']) {
            $this->error = 1;
            $this->errortip = "请选择最后一节课次";
            return false;
        }
        $bool = $this->DataControl->delData("eas_coursepackage_lesson", "package_id='{$request['package_id']}' and lesson_id='{$request['lesson_id']}'");
        if ($bool) {
            $wordList = $this->DataControl->selectClear("select word_id from eas_coursepackage_lessonword where package_id='{$request['package_id']}' and lesson_id='{$request['lesson_id']}'");
            if ($wordList) {
                foreach ($wordList as $wordOne) {
                    $puttydata = array();
                    $puttydata['company_id'] = $request['company_id'];
                    $puttydata['word_id'] = $wordOne['word_id'];
                    $this->delWordAction($puttydata);
                }
            }

            $this->error = 0;
            $this->errortip = "删除成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "删除失败";
            return false;
        }
    }


    //编辑课件包课次
    function editLessontimeAction($request)
    {
        $lessontimeData = array();
        $lessontimeData['lesson_name'] = $request['lesson_name'];
        $lessontimeData['lesson_sort'] = $request['lesson_sort'];
        $lessontimeData['is_synchro'] = $request['is_synchro'];
        $this->DataControl->updateData("eas_coursepackage_lesson", "package_id='{$request['package_id']}' and  lesson_id ='{$request['lesson_id']}'", $lessontimeData);
        $this->error = 0;
        $this->errortip = '更新成功';
        return true;
    }

    /**
     *  课次课件
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function getLesstimesWords($request)
    {

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '1000';
        }

        $pagestart = ($page - 1) * $num;
        $packageList = $this->DataControl->selectClear("
            select lw.word_name,lw.word_url,lw.word_format,lw.word_size,lw.word_id,lw.package_id,lw.line_fileid,lw.word_status,lw.word_createtime
            from  eas_coursepackage_lessonword as lw  
            where lw.package_id = '{$request['package_id']}' and lesson_id='{$request['lesson_id']}'
            limit {$pagestart},{$num}
        ");
        if (!$packageList) {
            $packageList = array();
        } else {
            $TalkcloudModel = new \Model\Tlk\TalkcloudModel($request['company_id']);
            foreach ($packageList as &$val) {
                $file_putArray = array();
                $file_putArray['fileid'] = $val['line_fileid'];
                $fileLogs = $TalkcloudModel->ex_roomgetfilepath($file_putArray);
                if($fileLogs['result'] == '-1'){
                    $val['word_status'] = '已删除';
                }else{
                    $val['word_status'] = '正常';
                }

                $val['word_createtime'] = $val['word_createtime']=='0'?'--':date("Y-m-d H:i:s",$val['word_createtime']);
                $val['word_size'] = round($val['word_size'] / 1024000, 2) . 'M';
            }
        }
        $allnum = $this->DataControl->selectOne("select count(lw.word_id) as word_num from  eas_coursepackage_lessonword as lw where  lw.package_id = '{$request['package_id']}' and lesson_id='{$request['lesson_id']}'");
        $data = array();
        $data['allnum'] = $allnum['lessontimes_num'];

        $data = array();
        $data['list'] = $packageList;
        $data['allnum'] = $allnum['word_num'];
        return $data;
    }

    //删除课件
    function delWordAction($request)
    {
        $wordOne = $this->DataControl->getFieldOne("eas_coursepackage_lessonword"
            , "line_fileid", "word_id='{$request['word_id']}'");
        if ($wordOne && $wordOne['line_fileid'] > 0) {
            $TalkcloudModel = new \Model\Tlk\TalkcloudModel($request['company_id']);
            $putArray = array();
            $putArray['fileid'] = $wordOne['line_fileid'];
            $putArray['fromclass'] = 0;
            $modelArray = $TalkcloudModel->exroomDeletefile($putArray);
            if ($modelArray['result'] == '0') {
                $this->DataControl->delData("eas_coursepackage_lessonword", "word_id='{$request['word_id']}'");
            } elseif ($modelArray['result'] == '4105') {
                $this->DataControl->delData("eas_coursepackage_lessonword", "word_id='{$request['word_id']}'");
            } else {
                $this->error = 1;
                $this->errortip = '移除失败';
                return false;
            }
        }elseif($wordOne){
            $this->DataControl->delData("eas_coursepackage_lessonword", "word_id='{$request['word_id']}'");
        }else{
            $this->error = 1;
            $this->errortip = '移除失败';
            return false;
        }
        $this->error = 0;
        $this->errortip = '移除成功';
        return true;
    }

    /**
     * 上传课件
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function loadWordAction($request)
    {
        $data = array();
        $data['package_id'] = $request['package_id'];
        $data['word_name'] = $request['word_name'];
        $data['word_url'] = $request['word_url'];
        $data['word_format'] = $request['word_format'];
        $data['lesson_id'] = $request['lesson_id'];
        $data['word_size'] = $request['word_size'];
        $data['word_updatatime'] = time();
        $data['word_createtime'] = time();
        $id = $this->DataControl->insertData("eas_coursepackage_lessonword", $data);
        $this->error = 0;
        $this->errortip = '上传成功';
        return $id;
    }


    function delPackageOneAction($request)
    {
        $applyOne = $this->DataControl->getFieldOne("eas_coursepackage_apply", "apply_id", "package_id='{$request['package_id']}'");
        if ($applyOne) {
            $this->error = 1;
            $this->errortip = '该课件包已经被适配,无法删除';
            return true;
        } else {
            $this->DataControl->delData("eas_coursepackage", "package_id='{$request['package_id']}'");
            $this->error = 0;
            $this->errortip = '删除成功';
            return true;
        }
    }

    /**
     *  12/8 此方法用于获取房间的信息 并标记
     * author: ling
     * 对应接口文档 0001
     */
    function getTkl_lineroomsList()
    {
        return false;
//        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->company_id);
//        $endtimes = time();
//        $where = "o.linerooms_isdel = '0' and o.linerooms_endtime > '{$endtimes}'";
//        $dataList = $this->DataControl->selectClear("
//            select linerooms_id,linerooms_starttime,linerooms_endtime,linerooms_number from tkl_linerooms as o where {$where}
//            order by  linerooms_id ASC
//
//            limit 0,1
//        ");
//
//        if ($dataList) {
//            foreach ($dataList as $dataOne) {
//                $putArray = array();
//                $putArray['serial'] = $dataOne['linerooms_number'];
//                $putArray['serial'] = *********;
//                $result = $TalkcloudModel->ex_getroom($putArray);
//                $data['starttime'] = date("Y-m-d H:i:s", $result['starttime']);
//                $data['endtime'] = date("Y-m-d H:i:s", $result['endtime']);
//                $data['linerooms_starttime'] = date("Y-m-d H:i:s", $dataOne['linerooms_starttime']);
//                $data['linerooms_endtime'] = date("Y-m-d H:i:s", $dataOne['linerooms_endtime']);
//
//                die;
//                if (($dataOne['linerooms_starttime'] <> $result['starttime']) || ($dataOne['linerooms_endtime'] <> $result['endtime'])) {
//                    $tlk_data = array();
//                    $tlk_data['three_starttime'] = $result['starttime'];
//                    $tlk_data['three_endtime'] = $result['endtime'];
//                    $tlk_data['three_isdiff'] = 1;
//                    $this->DataControl->updateData("tkl_linerooms", "linerooms_id='{$dataOne['linerooms_id']}'", $tlk_data);
//
//                }
//            }
//        }
    }


    /***
     *  处理更新时间有问题的网课
     * author: ling
     * 对应接口文档 0001
     */
    function getLineList()
    {

        return false;
//        $where = "three_isdiff =1 and three_starttime <> ''";
//        $dataList = $this->DataControl->selectClear("
//            select * from tkl_linerooms as o where {$where}
//            order by  linerooms_id ASC
//        ");
//        $TalkcloudModel = new \Model\Tlk\TalkcloudModel($this->company_id);
//        if ($dataList) {
//            foreach ($dataList as $key => $dataVar) {
//
//                $putArray = array();
//                $putArray['roomname'] = $dataVar['linerooms_name'];
//                $putArray['serial'] = $dataVar['linerooms_number'];
//                $putArray['fromclass'] = $dataVar['linerooms_fromclass'];
//                $putArray['roomtype'] = $dataVar['linerooms_type'];
//                $putArray['starttime'] = $dataVar['linerooms_starttime'];
//                $putArray['endtime'] = $dataVar['linerooms_endtime'];
//                $putArray['chairmanpwd'] = $dataVar['linerooms_chairmanpwd'];
//                $putArray['assistantpwd'] = $dataVar['linerooms_assistantpwd'];
//                $putArray['patrolpwd'] = $dataVar['linerooms_patrolpwd'];
//                $putArray['passwordrequired'] = $dataVar['linerooms_passwordrequired'];
//                $putArray['confuserpwd'] = $dataVar['linerooms_confuserpwd'];
//                $putArray['autoopenav'] = $dataVar['linerooms_autoopenav'];
//                $putArray['maxvideo'] = $dataVar['linerooms_maxvideo'];
//                $putArray['sharedesk'] = $dataVar['linerooms_sharedesk'];
//                $putArray['sidelineuserpwd'] = rand(11111, 999999);
//                $modelArray = $TalkcloudModel->exroomModify($putArray);
//
//                $putArrayroom = array();
//                $putArrayroom['serial'] = $modelArray['serial'];
//
//                $result = $TalkcloudModel->ex_getroom($putArrayroom);
//
//                if ($result['starttime'] <> $putArray['starttime'] || $putArray['endtime'] <> $result['endtime']) {
//                    debug('fail');
//                } else {
//                    debug('yes');
//                }
//            }
//        }
    }


}