<?php


namespace Model\Gmc;

class StatisticsStudentModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function studentSurvey($request)
    {

        $datawhere = " 1 ";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-01-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $changewhere = " g.changelog_day >= '{$request['starttime']}' and g.changelog_day <='{$request['endtime']}'";

        $sql = "select count(distinct si.student_id) as num
              from smc_student_enrolled as si
              left join smc_school as sc on si.school_id=sc.school_id
              where {$datawhere} and sc.company_id='{$this->company_id}' and si.enrolled_status<>'-1' and si.enrolled_status<>'2'
              ";

        $studentList = $this->DataControl->selectOne($sql);

        $sql = "select count(distinct g.student_id) as num
                from smc_student_changelog  as g 
                left join smc_school as sc On sc.school_id = g.school_id
                where sc.company_id='{$this->company_id}' and  g.stuchange_code in ('A01','F01') and {$changewhere} and {$datawhere}
        
            ";

        $addStudentList = $this->DataControl->selectClear($sql);

        $sql = "select count(distinct si.student_id) as num
              from smc_student_changelog as si
              left join smc_school as sc on si.school_id=sc.school_id
              where {$datawhere} and si.company_id='{$this->company_id}' and si.changelog_createtime>='{$starttime}' and si.changelog_createtime<='{$endtime}'
              and si.stuchange_code in  ('A01','F01')
              ";

        $lossStudentList = $this->DataControl->selectOne($sql);

        $data = array();
        if ($studentList) {
            $data['studentNum'] = $studentList['num'];
        } else {
            $data['studentNum'] = 0;
        }

        if ($addStudentList) {
            $data['addStudentNum'] = $addStudentList['num'];
        } else {
            $data['addStudentNum'] = 0;
        }

        if ($lossStudentList) {
            $data['lossStudentNum'] = $lossStudentList['num'];
        } else {
            $data['lossStudentNum'] = 0;
        }

        return $data;
    }


    /**
     * 集团学员统计 - 学员数量趋势
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function StatisticsStudentCountYear($request)
    {
        $datawhere = " 1 ";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']<date("Y-m-d") ) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");
        if ($request['time_type'] <> 1) {
            while (date('Ym', $starttime) <= date('Ym', $endtime)) {
                $date[] = date('Y-m', $starttime);
                $starttime = strtotime('+1 month', $starttime);
                $dateTime[] = $starttime + 24 * 60 * 60 - 1;
            }

            $format_date = "%Y-%m";

        } else {
            while (date('Ymd', $starttime) <= date('Ymd', $endtime)) {
                $date[] = date('Y-m-d', $starttime);
                $starttime = strtotime('+1 day', $starttime);
                $dateTime[] = $starttime + 24 * 60 * 60 - 1;
            }

            $format_date = "%Y-%m-%d";
        }
        if (!$date || count($date) > 32) {
            $this->error = 1;
            $this->error = '计算出错';
            return array();
        }
        $study_child_sql = '';
        for ($i = 0; $i < count($date); $i++) {
            $study_child_sql .= ",sum((select count(DISTINCT st.student_id) from  smc_student_study as st where  FROM_UNIXTIME(unix_timestamp(st.study_endday),'{$format_date}') >= '{$date[$i]}' and  st.school_id =sc.school_id)) as study_num$i  ";
        $study_child_sql = trim($study_child_sql, ',');

        $sql = "
            SELECT
            (sum( CASE WHEN s.stustatus_isenschool = 1 THEN 1 ELSE 0 END ) - sum( CASE WHEN s.stustatus_isenschool = 0 THEN 1 ELSE 0 END )) as enrollnum$i 
            FROM
            smc_student_changelog AS g,
            smc_code_stuchange AS s ,
            smc_school as sc
            WHERE
            g.stuchange_code = s.stuchange_code 
            and g.school_id = sc.school_id
            AND s.stuchange_type = 1 
            AND g.company_id = '{$this->company_id}' 
            AND FROM_UNIXTIME(unix_timestamp(g.changelog_day),'{$format_date}') <= '{$date[$i]}'
            and sc.company_id= '{$this->company_id}'  and {$datawhere} 
        ";

            $enrollOne[$i] = $this->DataControl->selectOne($sql)['enrollnum'.$i];
        }

        $study_sql = "
                     select 
                     {$study_child_sql} 
                    from  smc_school as sc   
                     where sc.company_id= '{$this->company_id}'  and {$datawhere} 
                ";
        $studyOne = $this->DataControl->selectOne($study_sql);

        if ($enrollOne) {
            $enrollOne = array_values($enrollOne);
        } else {
            for ($i = 0; $i < count($date); $i++) {
                $enrollOne[] = 0;
            }
        }
        if ($studyOne) {
            $studyOne = array_values($studyOne);
        } else {
            for ($i = 0; $i < count($date); $i++) {
                $studyOne[] = 0;
            }
        }
//        $enrollStrNum = implode($enrollOne,',');
//        $studyNum = implode($studyOne,',');

        $allList = array();
        $allList[0]['name'] = $this->LgStringSwitch('在籍学员');
        $allList[0]['data'] = $enrollOne;
        $allList[1]['name'] = $this->LgStringSwitch('在读学员');
        $allList[1]['data'] = $studyOne;

        $data = array();
        $data['allList'] = $allList;
        $data['legendData'] = $this->LgArraySwitch(array('在籍学员', '在读学员'));
        $data['xAxisData'] = $date;
        return $data;
    }


    /**
     *  集团学员统计-学员数量对比
     * author: ling
     * 对应接口文档 0001
     */

    function StudentSchoolContrast($request)
    {
        $datawhere = " 1 ";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_shortname like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-01-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $enrollwhere = " se.enrolled_leavetime <= '{$endtime}'";
        $studywhere = " st.study_endday <= '{$endDay}'";
        $add_enrollwhere = "se.enrolled_createtime >='{$starttime}' and  se.enrolled_createtime <= '{$endtime}'";
        $loss_enrollwhere = "se.enrolled_leavetime >='{$starttime}' and  se.enrolled_leavetime <= '{$endtime}'";
        if (isset($request['order_by_field']) && $request['order_by_field'] != '' && isset($request['order_by']) && $request['order_by'] !== '') {
            $order_by = " {$request['order_by_field']} {$request['order_by']}";
        } else {
            $order_by = " enroll_num DESC";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (!$request['type']) {
            $request['type'] = 3;
        }
        if ($request['type'] == 1) {
            //省
            $sql = "select 
            r.region_name as organize_cnname,sum(q.enroll_num) as enroll_num, sum(q.add_enroll_num) as add_enroll_num,sum(q.loss_enroll_num) as loss_enroll_num ,sum(q.study_num) as study_num
            from ( 
            select sc.school_id,sc.school_province,
                (SELECT count(q.student_id) from (SELECT d.school_id,d.student_id,
                (SELECT s.stustatus_isenschool FROM smc_student_changelog AS g,smc_code_stuchange AS s 
                WHERE g.stuchange_code =s.stuchange_code AND s.stuchange_type = 1 AND g.student_id = d.student_id AND g.school_id = d.school_id AND g.changelog_day <= '{$endDay}' 
                ORDER BY changelog_id DESC LIMIT 0,1 ) AS stustatus_isenschool 
                 FROM smc_student_enrolled AS d ,smc_school as l  WHERE d.school_id =l.school_id and  l.company_id = '{$this->company_id}' HAVING stustatus_isenschool = 1 ) as q where q.school_id=sc.school_id ) as enroll_num,
       
            (select count(DISTINCT se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$add_enrollwhere}) as add_enroll_num ,
            (select count(DISTINCT se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$loss_enrollwhere}) as loss_enroll_num ,
            (select count( DISTINCT st.student_id) from smc_student_study as st where st.school_id = sc.school_id and {$studywhere}) as study_num 
             from smc_school as sc 
             where sc.company_id ='{$this->company_id}' and {$datawhere}
             ) as q
             left join smc_code_region as r On r.region_id = q.school_province
             where r.parent_id =1 
             group by r.region_id
             order by {$order_by}
           
              ";

        } elseif ($request['type'] == 2) {
            //市
            $sql = "select 
            r.region_name as  organize_cnname ,sum(q.enroll_num) as enroll_num, sum(q.add_enroll_num) as add_enroll_num,sum(q.loss_enroll_num)  as loss_enroll_num,sum(q.study_num) as study_num
            from ( 
            select sc.school_id,sc.school_city,
              (SELECT count(q.student_id) from (SELECT d.school_id,d.student_id,
                (SELECT s.stustatus_isenschool FROM smc_student_changelog AS g,smc_code_stuchange AS s 
                WHERE g.stuchange_code =s.stuchange_code AND s.stuchange_type = 1 AND g.student_id = d.student_id AND g.school_id = d.school_id AND g.changelog_day <= '{$endDay}' 
                ORDER BY changelog_id DESC LIMIT 0,1 ) AS stustatus_isenschool 
                 FROM smc_student_enrolled AS d ,smc_school as l  WHERE d.school_id =l.school_id and  l.company_id = '{$this->company_id}' HAVING stustatus_isenschool = 1 ) as q where q.school_id=sc.school_id ) as enroll_num,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$add_enrollwhere}) as add_enroll_num ,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$loss_enrollwhere}) as loss_enroll_num ,
            (select count(st.student_id) from smc_student_study as st where st.school_id = sc.school_id and {$studywhere}) as study_num 
             from smc_school as sc 
             where sc.company_id ='{$this->company_id}' and {$datawhere}
             ) as q
             left join smc_code_region as r On r.region_id = q.school_city
             group by r.region_id
               order by {$order_by}
               
             ";

        } elseif ($request['type'] == 3) {
            //校
            $sql = "select (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as  organize_cnname,
               (SELECT count(q.student_id) from (SELECT d.school_id,d.student_id,
                (SELECT s.stustatus_isenschool FROM smc_student_changelog AS g,smc_code_stuchange AS s 
                WHERE g.stuchange_code =s.stuchange_code AND s.stuchange_type = 1 AND g.student_id = d.student_id AND g.school_id = d.school_id AND g.changelog_day <= '{$endDay}' 
                ORDER BY changelog_id DESC LIMIT 0,1 ) AS stustatus_isenschool 
                 FROM smc_student_enrolled AS d ,smc_school as l  WHERE d.school_id =l.school_id and  l.company_id = '{$this->company_id}' HAVING stustatus_isenschool = 1 ) as q where q.school_id=sc.school_id ) as enroll_num,
        
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$add_enrollwhere}) as add_enroll_num ,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$loss_enrollwhere}) as loss_enroll_num ,
            (select count(st.student_id) from smc_student_study as st where st.school_id = sc.school_id and {$studywhere}) as study_num 
            from smc_school as sc 
            where {$datawhere}  and sc.company_id = '{$this->company_id}'  
            order by {$order_by}
             
              ";
        } elseif ($request['type'] == 4) {
            //二级机构
            $sql = "select 
            co.organize_cnname,sum(q.enroll_num) as enroll_num, sum(q.add_enroll_num) as add_enroll_num,sum(q.loss_enroll_num)  as loss_enroll_num,sum(q.study_num) as study_num
            from ( 
            select sc.school_id,
              (SELECT count(q.student_id) from (SELECT d.school_id,d.student_id,
                (SELECT s.stustatus_isenschool FROM smc_student_changelog AS g,smc_code_stuchange AS s 
                WHERE g.stuchange_code =s.stuchange_code AND s.stuchange_type = 1 AND g.student_id = d.student_id AND g.school_id = d.school_id AND g.changelog_day <= '{$endDay}' 
                ORDER BY changelog_id DESC LIMIT 0,1 ) AS stustatus_isenschool 
                 FROM smc_student_enrolled AS d ,smc_school as l  WHERE d.school_id =l.school_id and  l.company_id = '{$this->company_id}' HAVING stustatus_isenschool = 1 ) as q where q.school_id=sc.school_id ) as enroll_num,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$add_enrollwhere}) as add_enroll_num ,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$loss_enrollwhere}) as loss_enroll_num ,
            (select count(st.student_id) from smc_student_study as st where st.school_id = sc.school_id and {$studywhere}) as study_num 
             from smc_school as sc 
             where sc.company_id ='{$this->company_id}' and {$datawhere}
             ) as q
             left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
             left join gmc_company_organize as co  On co.organize_id = ol.organize_id
             where co.father_id  = 0
             group by co.organize_id
             order by {$order_by}
             
             ";

        } elseif ($request['type'] == 5) {
            //三级机构
            $sql = "select 
            co.organize_cnname ,sum(q.enroll_num) as enroll_num, sum(q.add_enroll_num) as add_enroll_num,sum(q.loss_enroll_num)  as loss_enroll_num,sum(q.study_num) as study_num
            from ( 
            select sc.school_id,sc.school_city,
             (SELECT count(q.student_id) from (SELECT d.school_id,d.student_id,
                (SELECT s.stustatus_isenschool FROM smc_student_changelog AS g,smc_code_stuchange AS s 
                WHERE g.stuchange_code =s.stuchange_code AND s.stuchange_type = 1 AND g.student_id = d.student_id AND g.school_id = d.school_id AND g.changelog_day <= '{$endDay}' 
                ORDER BY changelog_id DESC LIMIT 0,1 ) AS stustatus_isenschool 
                 FROM smc_student_enrolled AS d ,smc_school as l  WHERE d.school_id =l.school_id and  l.company_id = '{$this->company_id}' HAVING stustatus_isenschool = 1 ) as q where q.school_id=sc.school_id ) as enroll_num,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$add_enrollwhere}) as add_enroll_num ,
            (select count(se.student_id) from smc_student_enrolled as se where se.school_id = sc.school_id and {$loss_enrollwhere}) as loss_enroll_num ,
            (select count(st.student_id) from smc_student_study as st where st.school_id = sc.school_id and {$studywhere}) as study_num 
             from smc_school as sc 
             where sc.company_id ='{$this->company_id}' and {$datawhere}
             ) as q
             left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
             left join gmc_company_organize as co  On co.organize_id = ol.organize_id
             where co.father_id  > 0
             group by co.organize_id
             order by {$order_by}
             ";
        }

        $allnum = $this->DataControl->selectClear($sql);
        $list_sql = $sql . "  limit {$pagestart},{$num} ";
        $dataList = $this->DataControl->selectClear($list_sql);

        if (!$dataList) {
            $arr_school = array();
            $arr_enroll = array();
            $arr_study = array();
            $arr_add_enroll_num = array();
            $arr_loss_enroll_num = array();
            $allnum = '0';
        } else {
            $arr_school = array_column($dataList, 'organize_cnname');
            $arr_enroll = array_column($dataList, 'enroll_num');
            $arr_study = array_column($dataList, 'study_num');
            $arr_add_enroll_num = array_column($dataList, 'add_enroll_num');
            $arr_loss_enroll_num = array_column($dataList, 'loss_enroll_num');
            $allnum = count($allnum);

        }
        $y_data = array();
        $y_data[0]['name'] = $this->LgStringSwitch('在籍学员');
        $y_data[0]['data'] = $arr_enroll;
        $y_data[1]['name'] = $this->LgStringSwitch('在读学员');
        $y_data[1]['data'] = $arr_study;
        $y_data[2]['name'] = $this->LgStringSwitch('新增学员');
        $y_data[2]['data'] = $arr_add_enroll_num;
        $y_data[3]['name'] = $this->LgStringSwitch('新增流失');
        $y_data[3]['data'] = $arr_loss_enroll_num;
        $data = array();
        $data['x_data'] = $arr_school;
        $data['y_data'] = $y_data;

        $result = array();
        $result['data'] = $data;
        $result['list'] = $dataList;
        $result['allnum'] = $allnum;
        return $result;
    }

    /**
     * 学员性别分布
     * author: ling
     * 对应接口文档 0001
     */
    function StudentSexDistribution($request)
    {
        $datawhere = "sc.company_id='{$this->company_id}' AND sc.school_isclose = '0' AND sc.school_istest <> '1' AND st.student_isdel = '0' AND se.enrolled_status IN (0,1)";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        /*if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-01-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");
        $add_enrollwhere = "se.enrolled_createtime >='{$starttime}' and  se.enrolled_createtime <= '{$endtime}'";*/


        $sql = " 
            select 
            sum( CASE WHEN st.student_sex = '女'   THEN 1 ELSE 0 END ) AS female_student_num,
            sum( CASE WHEN st.student_sex <> '女'   THEN 1 ELSE 0 END ) AS male_student_num
           from smc_student_enrolled as se
           left join smc_student as st ON se.student_id = st.student_id
           left join smc_school as sc On sc.school_id = se.school_id
           where {$datawhere}";

        $dataOne = $this->DataControl->selectOne($sql);

        $allList = array();
        if ($dataOne) {
            $male_rate = round($dataOne['male_student_num'] / ($dataOne['female_student_num'] + $dataOne['male_student_num']), 4) * 100;
            $female_rate = round($dataOne['female_student_num'] / ($dataOne['female_student_num'] + $dataOne['male_student_num']), 4) * 100;

            $allList[0]['name'] = $this->LgStringSwitch("男生");
            $allList[0]['value'] = $dataOne['male_student_num'];
            $allList[0]['rate'] = $male_rate;
            $allList[1]['name'] = $this->LgStringSwitch("女生");
            $allList[1]['value'] = $dataOne['female_student_num'];
            $allList[1]['rate'] = $female_rate;
        } else {
            $allList[0]['name'] = $this->LgStringSwitch("男生");
            $allList[0]['value'] = 0;
            $allList[0]['rate'] = 0;
            $allList[1]['name'] = $this->LgStringSwitch("女生");
            $allList[1]['value'] = 1;
            $allList[1]['rate'] = 0;
        }

        $data = array();
        $data['allList'] = $allList;
        $data['legendData'] = $this->LgArraySwitch(array('男生', '女生'));

        return $data;
    }


    /**
     * 年龄分布
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function StudentAgeDistribution($request)
    {
        $datawhere = "sc.company_id='{$this->company_id}' AND sc.school_isclose = '0' AND sc.school_istest <> '1' AND st.student_isdel = '0' AND se.enrolled_status IN (0,1)";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        /*if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");
        $add_enrollwhere = "se.enrolled_createtime >='{$starttime}' and  se.enrolled_createtime <= '{$endtime}'";*/

        $all_studentNum = $this->DataControl->selectOne("select count(se.student_id) as student_allnum from smc_student_enrolled as se
            left join smc_student as st ON se.student_id = st.student_id
            left join smc_school as sc On sc.school_id = se.school_id
            where {$datawhere}   ");

        $sql = "select (CASE
                WHEN IFNULL( TIMESTAMPDIFF( YEAR,  st.student_birthday, CURDATE( ) ), 0 ) < 0 THEN
                0 ELSE IFNULL( TIMESTAMPDIFF( YEAR,  st.student_birthday, CURDATE( ) ), 0 ) 
            END 
                ) AS student_age,
                count(1) as  student_num
            from smc_student_enrolled as se
            left join smc_student as st ON se.student_id = st.student_id
            left join smc_school as sc On sc.school_id = se.school_id
            where {$datawhere}  group by student_age order by student_age ASC";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$dataOne) {
                $dataOne['rate'] = $all_studentNum['student_allnum'] > 0 ? round($dataOne['student_num'] / $all_studentNum['student_allnum'], 4) * 100 : '0';
            }
            $newage_name = array_unique(array_column($dataList, "student_age"));
            if ($newage_name) {
                for ($i = 0; $i < count($newage_name); $i++) {
                    $newage_name[$i] = $newage_name[$i] . $this->LgStringSwitch('岁');
                }
            }
            $student_agerate = array_column($dataList, "rate");
            $student_agenum = array_column($dataList, "student_num");

        } else {
            $newage_name = ['0', '0', '0', '0', '0', '0', '0', '0'];
            $student_agerate = [0, 0, 0, 0, 0, 0, 0, 0];
            $student_agenum = [0, 0, 0, 0, 0, 0, 0, 0];

        }
        $rate = array();
        $rate[0]['name'] = $this->LgStringSwitch('年龄');
        $rate[0]['data'] = $student_agenum;
        $rate[0]['data_rate'] = $student_agerate;

        $data = array();
        $data['x_data'] = $newage_name;
        $data['y_data'] = $rate;
        $data['legendData'] = $this->LgArraySwitch(array('年龄'));
        return $data;
    }


    /**
     * 考勤状况
     * author: ling
     * 对应接口文档 0001
     */
    function studentClockingin($request)
    {
        $datawhere = " 1 ";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
//        $starttime = strtotime($startDay);
//        $endtime = strtotime($endDay . " 23:59:59");
        $datawhere .= " and cg.clockinginlog_day >='{$startDay}' and  cg.clockinginlog_day <='{$endDay}' ";


        $sql = " select  
            sum(case when q.hourstudy_checkin = 1 then 1 else 0 end) as arrive_checkinNum,
            sum(case when q.hourstudy_checkin = 0 then 1 else 0 end) as noarrive_checkinNum,
             count(q.clockinginlog_id) as all_checkinNum
          from (select  
          cg.clockinginlog_day,clockinginlog_id,sh.hourstudy_checkin
          from  smc_student_clockinginlog as cg
          left join smc_student_hourstudy as sh On sh.hourstudy_id = cg.hourstudy_id
          left join smc_school as  sc ON cg.school_id = sc.school_id
          where {$datawhere} and sc.company_id='{$this->company_id}') as q
          
          ";
        $dataOne = $this->DataControl->selectOne($sql);
        if (!$dataOne) {
            $arrive_checkinNum = 0;
            $noarrive_checkinNum = 0;
            $all_checkinNum = 0;
            $rate_checkin = 0;
        } else {
            $arrive_checkinNum = $dataOne['arrive_checkinNum'] ? $dataOne['arrive_checkinNum'] : 0;
            $noarrive_checkinNum = $dataOne['noarrive_checkinNum'] ? $dataOne['noarrive_checkinNum'] : 0;
            $all_checkinNum = $dataOne['all_checkinNum'] ? $dataOne['all_checkinNum'] : 0;
            $rate_checkin = $all_checkinNum > 0 ? round($noarrive_checkinNum / $all_checkinNum, 4) * 100 : 0;
        }

        $data = array();
        $data['arrive_checkinNum'] = $arrive_checkinNum;
        $data['noarrive_checkinNum'] = $noarrive_checkinNum;
        $data['all_checkinNum'] = $all_checkinNum;
        $data['rate'] = $rate_checkin;
        return $data;
    }


    /**
     *  学员考勤趋势
     * author: ling
     * 对应接口文档 0001
     */
    function StudentClockinTrend($request)
    {
        $datawhere = " 1 ";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }
        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        if ($request['time_type'] <> 1) {
            while (date('Ym', $starttime) <= date('Ym', $endtime)) {
                $date[] = date('Y-m', $starttime);
                $starttime = strtotime('+1 month', $starttime);
                $dateTime[] = $starttime + 24 * 60 * 60 - 1;
            }
            $format_date = "%Y-%m";

        } else {
            while (date('Ymd', $starttime) <= date('Ymd', $endtime)) {
                $date[] = date('Y-m-d', $starttime);
                $starttime = strtotime('+1 day', $starttime);
                $dateTime[] = $starttime + 24 * 60 * 60 - 1;
            }
            $format_date = "%Y-%m-%d";
        }
        if (!$date || count($date) > 32) {
            $this->error = 1;
            $this->error = '计算出错';
            return array();
        }

        for ($i = 0; $i < count($date); $i++) {
            $sql = "select 
                SUM( CASE WHEN  h.hourstudy_checkin  = '1' THEN 1 ELSE 0 END )  as clockin_num,
                SUM( CASE WHEN  h.hourstudy_checkin  = '0' THEN 1 ELSE 0 END )  as no_clockin_num
            from  smc_student_clockinginlog as g
            left join smc_student_hourstudy as h ON g.hourstudy_id=h.hourstudy_id
            left join smc_school as sc ON g.school_id = sc.school_id
            where  sc.company_id ='{$this->company_id}' and FROM_UNIXTIME(unix_timestamp(g.clockinginlog_day),'{$format_date}') ='{$date[$i]}' 
            ";
            $clockinOne = $this->DataControl->selectOne($sql);
            $clokonginOne[] = $clockinOne['clockin_num'] + 0;
            $no_clokonginOne[] = $clockinOne['no_clockin_num'] + 0;
        }


        $clockin_child_sql = '';
        $no_clockin_child_sql = '';

//        for ($i = 0; $i < count($date); $i++) {
//
//            $clockin_child_sql .= ",sum((select count(sh.hourstudy_id) from smc_student_clockinginlog as cg,smc_student_hourstudy as sh where  sh.hourstudy_id = cg.hourstudy_id and  FROM_UNIXTIME(unix_timestamp(cg.clockinginlog_day),'{$format_date}')= '{$date[$i]}' and cg.school_id = sc.school_id and sh.hourstudy_checkin =1) ) as  clokonginNum$i ";
//            $no_clockin_child_sql .= ",sum((select   count(sh.hourstudy_id) from smc_student_clockinginlog as cg,smc_student_hourstudy as sh where  sh.hourstudy_id = cg.hourstudy_id and  FROM_UNIXTIME(unix_timestamp(cg.clockinginlog_day),'{$format_date}')= '{$date[$i]}' and cg.school_id = sc.school_id and sh.hourstudy_checkin =0) ) as  no_clokonginNum$i ";
//
//        }
//        $clockin_child_sql = trim($clockin_child_sql, ',');
//        $no_clockin_child_sql = trim($no_clockin_child_sql, ',');
//
//        $clock_sql = " select
//              {$clockin_child_sql}
//                   from   smc_school as  sc where sc.company_id= '{$this->company_id}'  and {$datawhere}
//              ";
//
//        $clokonginOne = $this->DataControl->selectOne($clock_sql);
//
//        $no_clock_sql = " select
//              {$no_clockin_child_sql}
//                   from   smc_school as  sc where sc.company_id= '{$this->company_id}'  and {$datawhere}
//              ";
//        $no_clokonginOne = $this->DataControl->selectOne($no_clock_sql);
        if (!$clokonginOne) {
            for ($i = 0;
                 $i < count($date);
                 $i++) {
                $clokonginOne[] = 0;
            }
        } else {
            $clokonginOne = array_values($clokonginOne);
        }
        if (!$no_clokonginOne) {
            for ($i = 0; $i < count($date); $i++) {
                $no_clokonginOne[] = 0;
            }
        } else {
            $no_clokonginOne = array_values($no_clokonginOne);
        }

        for ($i = 0; $i < count($date); $i++) {
            $clock_rate[] = ($clokonginOne[$i] + $no_clokonginOne[$i]) > 0 ? round($clokonginOne[$i] / ($clokonginOne[$i] + $no_clokonginOne[$i]), 4) * 100 : 0;
            $no_clock_rate[] = ($clokonginOne[$i] + $no_clokonginOne[$i]) > 0 ? round($no_clokonginOne[$i] / ($clokonginOne[$i] + $no_clokonginOne[$i]), 4) * 100 : 0;
        }

        $clock_data = array();
        $clock_data[0]['name'] = $this->LgStringSwitch('出勤人数');
        $clock_data[0]['data'] = $clokonginOne;
        $clock_data[1]['name'] = $this->LgStringSwitch('缺勤人数');
        $clock_data[1]['data'] = $no_clokonginOne;
        $clock_data[2]['name'] = $this->LgStringSwitch('出勤率');
        $clock_data[2]['data'] = $clock_rate;
        $clock_data[3]['name'] = $this->LgStringSwitch('缺勤率');
        $clock_data[3]['data'] = $no_clock_rate;
        $result = array();
        $result['allList'] = $clock_data;
        $result['legendData'] = $this->LgArraySwitch(array('出勤人数', '缺勤人数', '出勤率', '缺勤率'));
        $result['xAxisData'] = $date;
        return $result;
    }

    /**
     * 学员出勤红榜
     * author: ling
     * 对应接口文档 0001
     */
    function studentClockinSort($request)
    {
        $datawhere = " 1 ";
        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
//        $starttime = strtotime($startDay);
//        $endtime = strtotime($endDay . " 23:59:59");
        if (isset($request['keyword']) && $request['keyword'] != '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_shortname like '%{$request['keyword']}%')";
        }

        $datawhere .= " and  cg.clockinginlog_day >='{$startDay}' and cg.clockinginlog_day <= '{$endDay}' ";

        if (isset($request['from']) && $request['from'] == '0') {
            if (isset($request['order_by']) && $request['order_by'] == 'ASC') {
                $order_by = "no_clockingin_rate ASC";
            } else {
                $order_by = "no_clockingin_rate DESC";
            }
        } else {
            if (isset($request['order_by']) && $request['order_by'] == 'ASC') {
                $order_by = "clockingin_rate ASC";
            } else {
                $order_by = "clockingin_rate DESC";
            }
        }


        if (isset($request['order_by_field']) && $request['order_by_field'] != '' && isset($request['order_by']) && $request['order_by'] !== '') {
            $order_by = " {$request['order_by_field']} {$request['order_by']}";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (!$request['type']) {
            $request['type'] = 3;
        }

        if ($request['type'] == 1) {
            $sql = "
             select r.region_name as organize_cnname,
             sum(case when sh.hourstudy_checkin =1 then 1 else 0 end) as clockingin_num,
             sum(case when sh.hourstudy_checkin =0 then 1 else 0 end) as no_clockingin_num,
             ( round(sum(case when sh.hourstudy_checkin =1 then 1 else 0 end)/count(1),2)* 100) as clockingin_rate,
             ( round(sum(case when sh.hourstudy_checkin =0 then 1 else 0 end)/count(1),2)* 100) as no_clockingin_rate,
             count(1) as all_clockingin_num
            from smc_student_clockinginlog as  cg 
            left join smc_student_hourstudy as sh On sh.hourstudy_id = cg.hourstudy_id
            left join smc_school as  sc On sc.school_id = cg.school_id
            left join smc_code_region as r ON  r.region_id = sc.school_province
            where r.parent_id =1 and   sc.company_id = '{$this->company_id}'  and {$datawhere}
             group by r.region_id
             order by {$order_by}
           ";
        } elseif ($request['type'] == 2) {
            $sql = "
             select r.region_name as organize_cnname,
              sum(case when sh.hourstudy_checkin =1 then 1 else 0 end) as clockingin_num,
             sum(case when sh.hourstudy_checkin =0 then 1 else 0 end) as no_clockingin_num,
              ( round(sum(case when sh.hourstudy_checkin =1 then 1 else 0 end)/count(1),2)* 100) as clockingin_rate,
             ( round(sum(case when sh.hourstudy_checkin =0 then 1 else 0 end)/count(1),2)* 100) as no_clockingin_rate,
                count(1) as all_clockingin_num
            from smc_student_clockinginlog as  cg 
            left join smc_student_hourstudy as sh On sh.hourstudy_id = cg.hourstudy_id
            left join smc_school as  sc On sc.school_id = cg.school_id
            left join smc_code_region as r ON  r.region_id = sc.school_city
            where   sc.company_id = '{$this->company_id}'  and {$datawhere}
             group by r.region_id
             order by {$order_by}
           ";
        } elseif ($request['type'] == 3) {
            $sql = "
             select (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as organize_cnname ,
                sum(case when sh.hourstudy_checkin =1 then 1 else 0 end) as clockingin_num,
             sum(case when sh.hourstudy_checkin =0 then 1 else 0 end) as no_clockingin_num,
            ( round(sum(case when sh.hourstudy_checkin =1 then 1 else 0 end)/count(1),2)* 100) as clockingin_rate,
             ( round(sum(case when sh.hourstudy_checkin =0 then 1 else 0 end)/count(1),2)* 100) as no_clockingin_rate,
                count(1) as all_clockingin_num
            from smc_student_clockinginlog as  cg 
            left join smc_student_hourstudy as sh On sh.hourstudy_id = cg.hourstudy_id
            left join smc_school as  sc On sc.school_id = cg.school_id
            where   sc.company_id = '{$this->company_id}'  and {$datawhere}
             group by sc.school_id
             order by {$order_by}
           ";

        } elseif ($request['type'] == 4) {
            $sql = "
             select co.organize_cnname as organize_cnname,
              sum(case when sh.hourstudy_checkin =1 then 1 else 0 end) as clockingin_num,
             sum(case when sh.hourstudy_checkin =0 then 1 else 0 end) as no_clockingin_num,
            ( round(sum(case when sh.hourstudy_checkin =1 then 1 else 0 end)/count(1),2)* 100) as clockingin_rate,
             ( round(sum(case when sh.hourstudy_checkin =0 then 1 else 0 end)/count(1),2)* 100) as no_clockingin_rate,
                count(1) as all_clockingin_num
            from smc_student_clockinginlog as  cg 
            left join smc_student_hourstudy as sh On sh.hourstudy_id = cg.hourstudy_id
            left join smc_school as  sc On sc.school_id = cg.school_id
            left join gmc_company_organizeschool as ol On ol.school_id = sc.school_id
             left join gmc_company_organize as co  On co.organize_id = ol.organize_id
            where   sc.company_id = '{$this->company_id}'  and {$datawhere} and co.father_id  = 0
             group by sc.school_id
             order by {$order_by}
             ";

        } elseif ($request['type'] == 5) {
            $sql = "
             select co.organize_cnname as organize_cnname,
              sum(case when sh.hourstudy_checkin =1 then 1 else 0 end) as clockingin_num,
             sum(case when sh.hourstudy_checkin =0 then 1 else 0 end) as no_clockingin_num,
              ( round(sum(case when sh.hourstudy_checkin =1 then 1 else 0 end)/count(1),2)* 100) as clockingin_rate,
             ( round(sum(case when sh.hourstudy_checkin =0 then 1 else 0 end)/count(1),2)* 100) as no_clockingin_rate,
                count(1) as all_clockingin_num
            from smc_student_clockinginlog as  cg 
            left join smc_student_hourstudy as sh On sh.hourstudy_id = cg.hourstudy_id
            left join smc_school as  sc On sc.school_id = cg.school_id
            left join gmc_company_organizeschool as ol On ol.school_id = sc.school_id
             left join gmc_company_organize as co  On co.organize_id = ol.organize_id
            where   sc.company_id = '{$this->company_id}'  and {$datawhere} 
             group by sc.school_id
             order by {$order_by}
             ";
        }
        $allNum = $this->DataControl->selectClear($sql);

        if ($allNum) {
            $all_num = count($allNum);
        } else {
            $all_num = 0;
        }

        $sql .= " limit {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            $schoolName = array_column($dataList, "organize_cnname");
            $clockin_schoolrate = array_column($dataList, "clockingin_rate");
            $noclockin_schoolrate = array_column($dataList, "no_clockingin_rate");
            foreach ($dataList as $key => $dataOne) {
                $dataList[$key]['all_clockingin_num'] = $dataOne['clockingin_num'] + $dataOne['no_clockingin_num'];
                $dataList[$key]['number'] = $key + 1;
            }

        } else {
            $schoolName = array("", "", "", "");
            $clockin_schoolrate = array(0, 0, 0, 0);
            $noclockin_schoolrate = array(0, 0, 0, 0);
            $dataList = array();
        }

        if (isset($request['from']) && $request['from'] == '0') {
            $allList = array();
            $allList[0]['name'] = $this->LgStringSwitch('考勤缺勤率');
            $allList[0]['data'] = $noclockin_schoolrate;
        } else {
            $allList = array();
            $allList[0]['name'] = $this->LgStringSwitch('考勤出勤率');
            $allList[0]['data'] = $clockin_schoolrate;
        }

        $data = array();
        $data['y_data'] = $allList;
        $data['x_data'] = $schoolName;

        $reslut = array();
        $reslut['data'] = $data;
        $reslut['list'] = $dataList;
        $reslut['allnum'] = $all_num;
        return $reslut;
    }

    function paymentInfo($request)
    {
        if ($request['type'] == 0) {
            //本日
            $oldStartDay = date("Y-m-d", strtotime("-1 day"));
            $oldEndDay = date("Y-m-d", strtotime("-1 day"));
        } elseif ($request['type'] == 1) {
            //昨日
            $oldStartDay = date("Y-m-d", strtotime("-2 day"));
            $oldEndDay = date("Y-m-d", strtotime("-2 day"));
        } elseif ($request['type'] == 2) {
            //按周
            $WeekAll = GetWeekAll(date("Y-m-d", strtotime("-7 day")));
            $oldStartDay = $WeekAll['nowweek_start'];
            $oldEndDay = date("Y-m-d", strtotime($oldStartDay . "+6 day"));

        } elseif ($request['type'] == 3) {
            //按上周
            $WeekAll = GetWeekAll(date("Y-m-d", strtotime("-14 day")));
            $oldStartDay = $WeekAll['nowweek_start'];
            $oldEndDay = date("Y-m-d", strtotime($oldStartDay . "+6 day"));

        } elseif ($request['type'] == 4) {
            //本月
            $oldStartDay = date('Y-m-01', strtotime('-1 month'));
            $oldEndDay = date('Y-m-d', strtotime(date('Y-m-01') . '-1 day'));
        } elseif ($request['type'] == 5) {
            //上月
            $oldStartDay = date('Y-m-01', strtotime('-2 month'));
            $oldEndDay = date('Y-m-d', strtotime(date('Y-m-01', strtotime('-1 month')) . '-1 day'));

        } elseif ($request['type'] == 6) {
            //本季度
            $season = ceil((date('n')) / 3) - 1;
            $oldStartDay = date('Y-m-d', mktime(0, 0, 0, $season * 3 - 3 + 1, 1, date('Y')));
            $oldEndDay = date('Y-m-d', strtotime($oldStartDay . '+3 month -1day'));
        } elseif ($request['type'] == 7) {
            //上季度
            $season = ceil((date('n')) / 3) - 2;
            $oldStartDay = date('Y-m-d', mktime(0, 0, 0, $season * 3 - 3 + 1, 1, date('Y')));
            $oldEndDay = date('Y-m-d', strtotime($oldStartDay . '+3 month -1day'));
        } elseif ($request['type'] == 8) {
            //本年
            $oldStartDay = date("Y-01-01", strtotime("-1 year"));
            $oldEndDay = date('Y-m-d', strtotime(date('Y-1-1') . '-1 day'));

        } elseif ($request['type'] == 9) {
            //去年
            $oldStartDay = date("Y-01-01", strtotime("-2 year"));
            $oldEndDay = date('Y-m-d', strtotime(date('Y-01-01', strtotime('-1 year')) . '-1 day'));

        } elseif ($request['type'] == 10) {
            //按时间

        } else {
            $this->error = true;
            $this->errortip = "该模式不存在";
            return false;
        }
        $oldstarttime = strtotime($oldStartDay);
        $oldendtime = strtotime($oldEndDay . " 23:59:59");

        $datawhere = " 1 ";
        $datawhere .= " and B.company_id='{$this->company_id}' ";

        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-01-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $sql = "select B.student_id
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  inner join smc_school as sc on sc.school_id=B.school_id
                  where {$datawhere} and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and not exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id=B.school_id and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
                  group by B.student_id
              ";
        $newPayList = $this->DataControl->selectClear($sql);

        $sql = "select sum(T.pay_price) as price
              from (select A.pay_price
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  inner join smc_school as sc on sc.school_id=B.school_id
                  where {$datawhere} and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and not exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id=B.school_id and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
              ) AS T";
        $newPayPrice = $this->DataControl->selectOne($sql);

        $sql = "select sum(T.pay_price) as price
              from (select A.pay_price
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  inner join smc_school as sc on sc.school_id=B.school_id
                  where {$datawhere} and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$oldstarttime}' and A.pay_successtime<='{$oldendtime}'
                  and not exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id=B.school_id and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$oldstarttime}' )
              ) AS T";
        $oldPayPrice = $this->DataControl->selectOne($sql);


        $sql = "select B.student_id
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  inner join smc_school as sc on sc.school_id=B.school_id
                  where {$datawhere} and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id=B.school_id and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
                  group by B.student_id
              ";
        $pastPayList = $this->DataControl->selectClear($sql);

        $sql = "select sum(T.pay_price) as price
              from (select A.pay_price
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  inner join smc_school as sc on sc.school_id=B.school_id
                  where {$datawhere} and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$starttime}' and A.pay_successtime<='{$endtime}'
                  and exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id=B.school_id and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$starttime}' )
              ) AS T";
        $newPastPayPrice = $this->DataControl->selectOne($sql);

        $sql = "select sum(T.pay_price) as price
              from (select A.pay_price
                  from smc_payfee_order_pay as A
                  left join smc_payfee_order as B on A.order_pid=B.order_pid
                  left join smc_code_paytype as C on C.paytype_code=A.paytype_code
                  inner join smc_school as sc on sc.school_id=B.school_id
                  where {$datawhere} and C.paytype_ischarge='1' and A.pay_issuccess='1'
                  and A.pay_successtime>='{$oldstarttime}' and A.pay_successtime<='{$oldendtime}'
                  and exists(select 1 from smc_payfee_order_pay as A1
                                  left join smc_payfee_order as B1 on A1.order_pid=B1.order_pid
                                  left join smc_code_paytype as C1 on C1.paytype_code=A1.paytype_code
                                  where B1.school_id=B.school_id and C1.paytype_ischarge='1' and A1.pay_issuccess='1'
                                  and B1.student_id = B.student_id and A1.pay_successtime<'{$oldstarttime}' )
              ) AS T";
        $oldPastPayPrice = $this->DataControl->selectOne($sql);

        $sql = "select B.student_id
              from smc_payfee_order as B
              inner join smc_school as sc on sc.school_id=B.school_id
              where {$datawhere} and b.order_paymentprice>b.order_paidprice and B.order_createtime>='{$starttime}' and B.order_createtime<='{$endtime}'
              group by B.student_id
              ";
        $arrearsStuList = $this->DataControl->selectClear($sql);

        $sql = "select sum(b.order_paymentprice-b.order_paidprice) as price
              from smc_payfee_order as B
              inner join smc_school as sc on sc.school_id=B.school_id
              where {$datawhere} and b.order_paymentprice>b.order_paidprice and B.order_createtime>='{$starttime}' and B.order_createtime<='{$endtime}'";
        $newArrearsPrice = $this->DataControl->selectClear($sql);

        $sql = "select sum(b.order_paymentprice-b.order_paidprice) as price
              from smc_payfee_order as B
              inner join smc_school as sc on sc.school_id=B.school_id
              where {$datawhere} and b.order_paymentprice>b.order_paidprice and B.order_createtime>='{$oldstarttime}' and B.order_createtime<='{$oldendtime}'";
        $oldArrearsPrice = $this->DataControl->selectClear($sql);

        $sql = "select B.student_id
              from smc_refund_order as B
              inner join smc_school as sc on sc.school_id=B.school_id
              where {$datawhere} and B.refund_status='4' and B.refund_updatatime>='{$starttime}' and B.refund_updatatime<='{$endtime}'
              group by B.student_id
              ";
        $refundStuList = $this->DataControl->selectClear($sql);

        $sql = "select sum(B.refund_payprice) as price
              from smc_refund_order as B
              inner join smc_school as sc on sc.school_id=B.school_id
              where {$datawhere} and B.refund_status='4' and B.refund_updatatime>='{$starttime}' and B.refund_updatatime<='{$endtime}'
              group by B.student_id
              ";
        $newRefundStuPrice = $this->DataControl->selectClear($sql);

        $sql = "select sum(B.refund_payprice) as price
              from smc_refund_order as B
              inner join smc_school as sc on sc.school_id=B.school_id
              where {$datawhere} and B.refund_status='4' and B.refund_updatatime>='{$oldstarttime}' and B.refund_updatatime<='{$oldendtime}'
              group by B.student_id
              ";
        $oldRefundStuPrice = $this->DataControl->selectClear($sql);

        $data = array();
        $data['newPayNum'] = $newPayList ? count($newPayList) : 0;
        $data['newPayPrice'] = $newPayPrice['price'] ? sprintf("%.2f", $newPayPrice['price']) : 0.00;
        $data['oldPayPrice'] = $oldPayPrice['price'] ? sprintf("%.2f", $oldPayPrice['price']) : 0.00;
        $data['payPriceDif'] = sprintf("%.2f", (($newPayPrice['price'] ? $newPayPrice['price'] : 0.00) - ($oldPayPrice['price'] ? $oldPayPrice['price'] : 0.00)));

        $data['newPayNum'] = $pastPayList ? count($pastPayList) : 0;
        $data['newPastPayPrice'] = $newPastPayPrice['price'] ? sprintf("%.2f", $newPastPayPrice['price']) : 0.00;
        $data['oldPastPayPrice'] = $oldPastPayPrice['price'] ? sprintf("%.2f", $oldPastPayPrice['price']) : 0.00;
        $data['pastPayDif'] = sprintf("%.2f", (($newPastPayPrice['price'] ? $newPastPayPrice['price'] : 0.00) - ($oldPastPayPrice['price'] ? $oldPastPayPrice['price'] : 0.00)));

        $data['newPayNum'] = $arrearsStuList ? count($arrearsStuList) : 0;
        $data['newArrearsPrice'] = $newArrearsPrice['price'] ? sprintf("%.2f", $newArrearsPrice['price']) : 0.00;
        $data['oldArrearsPrice'] = $oldArrearsPrice['price'] ? sprintf("%.2f", $oldArrearsPrice['price']) : 0.00;
        $data['arrearsDif'] = sprintf("%.2f", (($newArrearsPrice['price'] ? $newArrearsPrice['price'] : 0.00) - ($oldArrearsPrice['price'] ? $oldArrearsPrice['price'] : 0.00)));

        $data['newPayNum'] = $refundStuList ? count($refundStuList) : 0;
        $data['newRefundStuPrice'] = $newRefundStuPrice['price'] ? sprintf("%.2f", $newRefundStuPrice['price']) : 0.00;
        $data['oldRefundStuPrice'] = $oldRefundStuPrice['price'] ? sprintf("%.2f", $oldRefundStuPrice['price']) : 0.00;
        $data['refundDif'] = sprintf("%.2f", (($newRefundStuPrice['price'] ? $newRefundStuPrice['price'] : 0.00) - ($oldRefundStuPrice['price'] ? $oldRefundStuPrice['price'] : 0.00)));


        return $data;
    }

    function schoolBudget($request)
    {
        $datawhere = " 1 ";

        $datawhere .= " and sc.school_id>'0'";

        if (isset($request['school_province']) && $request['school_province'] != '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] != '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] != '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }


        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and si.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $time1 = $starttime;
        $time2 = $endtime;
        $dayArr = array();

        if ($request['type'] == 0) {
            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $dayArr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select SUM(si.income_price) as income_price,FROM_UNIXTIME(si.income_confirmtime, '%Y-%m-%d') as income_createday
                  from smc_school_income as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
                  group by income_createday
                  order by income_createday asc
              ";
            $newIncomeList = $this->DataControl->selectClear($sql);

            $sql = "select SUM(si.expend_price) as expend_price,FROM_UNIXTIME(si.expend_confirmtime, '%Y-%m-%d') as expend_createday
              from smc_school_expend as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and expend_confirmtime>='{$starttime}' and expend_confirmtime<='{$endtime}'
              group by expend_createday
              order by expend_createday asc
              ";
            $newExpendList = $this->DataControl->selectClear($sql);

            $sql = "select SUM(pop.pay_price) as pay_price,FROM_UNIXTIME(pop.pay_successtime, '%Y-%m-%d') as pay_successday
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as si on si.order_pid=pop.order_pid
              left join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and pop.pay_issuccess='1' and pop.pay_successtime>='{$starttime}' and pop.pay_successtime<='{$endtime}'
              group by pay_successday
              order by pay_successday asc
              ";
            $newPayList = $this->DataControl->selectClear($sql);

            $sql = "select sum(si.refund_payprice) as refund_payprice,FROM_UNIXTIME(si.refund_updatatime, '%Y-%m-%d') as refund_updataday
                  from smc_refund_order as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and refund_status='4' and si.refund_updatatime>='{$starttime}' and si.refund_updatatime<='{$endtime}'
                  group by refund_updataday
                  order by refund_updataday asc
                  ";
            $newRefundList = $this->DataControl->selectClear($sql);

        } elseif ($request['type'] == 1) {
            while (date("Y-m", $time1) <= date("Y-m", $time2)) {
                $dayArr[] = date('Y-m', $time1);
                $time1 = strtotime("+1 month", ($time1));
            }
            $sql = "select SUM(si.income_price) as income_price,FROM_UNIXTIME(si.income_confirmtime, '%Y-%m') as income_createday
                  from smc_school_income as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
                  group by income_createday
                  order by income_createday asc
              ";
            $newIncomeList = $this->DataControl->selectClear($sql);

            $sql = "select SUM(si.expend_price) as expend_price,FROM_UNIXTIME(si.expend_confirmtime, '%Y-%m') as expend_createday
              from smc_school_expend as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and expend_confirmtime>='{$starttime}' and expend_confirmtime<='{$endtime}'
              group by expend_createday
              order by expend_createday asc
              ";
            $newExpendList = $this->DataControl->selectClear($sql);

            $sql = "select SUM(pop.pay_price) as pay_price,FROM_UNIXTIME(pop.pay_successtime, '%Y-%m') as pay_successday
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as si on si.order_pid=pop.order_pid
              left join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and pop.pay_issuccess='1' and pop.paytype_code in (select cp.paytype_code from smc_code_paytype as cp where cp.paytype_ischarge='1') and pop.pay_successtime>='{$starttime}' and pop.pay_successtime<='{$endtime}'
              group by pay_successday
              order by pay_successday asc
              ";
            $newPayList = $this->DataControl->selectClear($sql);

            $sql = "select sum(si.refund_payprice) as refund_payprice,FROM_UNIXTIME(si.refund_updatatime, '%Y-%m') as refund_updataday
                  from smc_refund_order as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and refund_status='4' and si.refund_updatatime>='{$starttime}' and si.refund_updatatime<='{$endtime}'
                  group by refund_updataday
                  order by refund_updataday asc
                  ";
            $newRefundList = $this->DataControl->selectClear($sql);
        } else {
            $this->error = true;
            $this->errortip = "无此模式";
            return false;
        }

        $tem_newIncome = array();
        if ($newIncomeList) {
            foreach ($dayArr as $dayOne) {
                foreach ($newIncomeList as $one) {
                    if ($one['income_createday'] == $dayOne) {
                        $tem_newIncome[$dayOne] = sprintf("%.2f", $one['income_price'] / 10000);
                    } else {
                        if (!$tem_newIncome[$dayOne]) {
                            $tem_newIncome[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_newIncome = array_values($tem_newIncome);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_newIncome[] = '0.00';
            }
        }

        $tem_newExpend = array();
        if ($newExpendList) {
            foreach ($dayArr as $dayOne) {
                foreach ($newExpendList as $one) {
                    if ($one['expend_createday'] == $dayOne) {
                        $tem_newExpend[$dayOne] = sprintf("%.2f", $one['expend_price'] / 10000);
                    } else {
                        if (!$tem_newExpend[$dayOne]) {
                            $tem_newExpend[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_newExpend = array_values($tem_newExpend);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_newExpend[] = '0.00';
            }
        }

        $tem_newPay = array();
        if ($tem_newPay) {
            foreach ($dayArr as $dayOne) {
                foreach ($newPayList as $one) {
                    if ($one['pay_successday'] == $dayOne) {
                        $tem_newPay[$dayOne] = sprintf("%.2f", $one['pay_price'] / 10000);
                    } else {
                        if (!$tem_newPay[$dayOne]) {
                            $tem_newPay[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_newPay = array_values($tem_newPay);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_newPay[] = '0.00';
            }
        }

        $tem_newRefund = array();
        if ($tem_newRefund) {
            foreach ($dayArr as $dayOne) {
                foreach ($newRefundList as $one) {
                    if ($one['refund_updataday'] == $dayOne) {
                        $tem_newRefund[$dayOne] = sprintf("%.2f", $one['refund_payprice'] / 10000);
                    } else {
                        if (!$tem_newRefund[$dayOne]) {
                            $tem_newRefund[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_newRefund = array_values($tem_newRefund);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_newRefund[] = '0.00';
            }
        }

        $data = array();
        $k = 0;
        $data[$k]['name'] = $this->LgStringSwitch('总收入');
        $data[$k]['data'] = $tem_newIncome;
        $k++;

        $data[$k]['name'] = $this->LgStringSwitch('总支出');
        $data[$k]['data'] = $tem_newExpend;
        $k++;

        $data[$k]['name'] = $this->LgStringSwitch('总收费');
        $data[$k]['data'] = $tem_newPay;
        $k++;

        $data[$k]['name'] = $this->LgStringSwitch('总退费');
        $data[$k]['data'] = $tem_newRefund;
        $k++;

        $legendData = $this->LgArraySwitch(array("总收入", "总支出", "总收费", "总退费", "累计总欠费"));

        $tem_data = array();
        $tem_data['allList'] = $data;
        $tem_data['legendData'] = $legendData;
        $tem_data['xAxisData'] = $dayArr;
        return $tem_data;
    }

}