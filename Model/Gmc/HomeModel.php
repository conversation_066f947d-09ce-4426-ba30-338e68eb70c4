<?php


namespace Model\Gmc;

class HomeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司\
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function enrolmentNumber($request){

        $monarr=array();
        $WeekAll = GetWeekAll(date("Y-m-d",time()));
        //本周
        $starttime=strtotime($WeekAll['nowweek_start']);
        $endtime=strtotime($WeekAll['nowweek_end'].'23:59:59');
//        $endtime=time();

        $time1=$starttime;
        $time2=$endtime;

        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $monarr[] = date('Y-m-d',$time1);
            $time1=strtotime("+1 day",$time1);
        }

        $sql="select FROM_UNIXTIME(st.trading_createtime, '%Y-%m-%d' ) as trading_createtime
              from smc_student_trading as st
          ";

        $sql.="where st.company_id='{$this->company_id}' and st.trading_status<>'-1' and st.trading_createtime>='{$starttime}' and st.trading_createtime<='{$endtime}'";

        $newSql=$sql." and st.tradingtype_code='PaynewFee'";
        $list=$this->DataControl->selectClear($newSql);
        $tem_newArray=array();
        $newArray=array();
        $new_data=array();
        if($list){
            foreach($list as $val){
                if($tem_newArray[$val['trading_createtime']]){
                    $tem_newArray[$val['trading_createtime']]+=1;
                }else{
                    $tem_newArray[$val['trading_createtime']]=1;
                }
            }

            foreach($monarr as $monOne){
                if($tem_newArray[$monOne]){
                    $new_data[$monOne]=$tem_newArray[$monOne];
                }else{
                    $new_data[$monOne]=0;
                }
            }

            foreach($new_data as $val){
                $newArray[]=$val;
            }
        }else{
            foreach($monarr as $monOne){
                if($tem_newArray[$monOne]){
                    $new_data[$monOne]=$tem_newArray[$monOne];
                }else{
                    $new_data[$monOne]=0;
                }
            }

            foreach($new_data as $val){
                $newArray[]=$val;
            }
        }

        $oldSql=$sql." and st.tradingtype_code='PayrenewFee'";
        $list=$this->DataControl->selectClear($oldSql);
        $tem_oldArray=array();
        $oldArray=array();
        $old_data=array();
        if($list){
            foreach($list as $val){
                if($tem_oldArray[$val['trading_createtime']]){
                    $tem_oldArray[$val['trading_createtime']]+=1;
                }else{
                    $tem_oldArray[$val['trading_createtime']]=1;
                }
            }
            foreach($monarr as $monOne){
                if($tem_oldArray[$monOne]){
                    $old_data[$monOne]=$tem_oldArray[$monOne];
                }else{
                    $old_data[$monOne]=0;
                }
            }

            foreach($old_data as $val){
                $oldArray[]=$val;
            }
        }else{
            foreach($monarr as $monOne){
                if($tem_oldArray[$monOne]){
                    $old_data[$monOne]=$tem_oldArray[$monOne];
                }else{
                    $old_data[$monOne]=0;
                }
            }

            foreach($old_data as $val){
                $oldArray[]=$val;
            }
        }
        $weekArray=$this->LgArraySwitch(array('0'=>'周日','1'=>'周一','2'=>'周二','3'=>'周三','4'=>'周四','5'=>'周五','6'=>'周六'));

        foreach($monarr as &$val){
            $val=$weekArray[date("w",strtotime($val))];
        }

        $data=array();
        $data['monthArray']=$monarr;
        $data['oldArray']=$oldArray;
        $data['newArray']=$newArray;

        return $data;


    }

    //集团统计
    function companyCensus($paramArray){
        $postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p WHERE p.company_id = '{$paramArray['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$paramArray['re_postbe_id']}' limit 0,1");

        $schoolwhere = "s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' AND s.school_istest = '0'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $schoolwhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $schoolNum = $this->DataControl->selectOne("select count(s.school_id) as schoolNum from smc_school as s WHERE {$schoolwhere}");

        $stafferwhere = "s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' and account_class<>'1'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $stafferwhere .= " AND s.staffer_id IN (SELECT p.staffer_id FROM gmc_staffer_postbe AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }
        $stafferNum = $this->DataControl->selectOne("select count(s.staffer_id) as stafferNum from smc_staffer as s WHERE {$stafferwhere}");

        $courseNum = $this->DataControl->selectOne("select count(s.course_id) as courseNum from smc_course as s
WHERE s.company_id = '{$paramArray['company_id']}' and s.course_status = '1'");

        $clientNum = $this->DataControl->selectOne("select count(channel_id) as clientNum from crm_code_channel where company_id='{$this->company_id}'");

        $activitywhere = "a.company_id = '{$this->company_id}'";
        $endtoday = date('Y-m-d').' 23:59:59';
        $activitywhere .= " and a.activity_endtime>='{$endtoday}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $activitywhere .= " AND a.activity_id IN (SELECT p.activity_id FROM crm_sell_activity_school AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }
        $activityNum = $this->DataControl->selectOne("select count(a.activity_id) as activityNum from crm_sell_activity as a where {$activitywhere}");

        $clientwhere = "t.company_id = '{$this->company_id}' AND t.client_tracestatus <> '4' AND t.client_tracestatus <> '-1' AND t.client_tracestatus <> '-2'";
        /*if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $clientwhere .= " AND t.client_id IN (SELECT p.client_id FROM crm_client_schoolenter AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }*/
        $rosterNum = $this->DataControl->selectOne("select count(t.client_id) as rosterNum from crm_client as t where {$clientwhere}");

        //在籍人数

        $enrolledwhere = "sc.company_id='{$this->company_id}' and se.enrolled_status<>'-1' and se.enrolled_status<>'2'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $enrolledwhere .= " AND se.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $studentNumResult = $this->DataControl->selectOne("select count(distinct se.student_id) as studentNum from smc_student_enrolled as se
              inner join smc_school as sc on sc.school_id=se.school_id where {$enrolledwhere}");

        //在读人数
        $readingwhere = "d.company_id='{$this->company_id}' and d.study_isreading='1'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $readingwhere .= " AND d.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $studyStudentResult=$this->DataControl->selectOne("select count(distinct d.student_id) as studyStuNum from smc_student_study as d where {$readingwhere}");

        //延班人数
        $changelogwhere = "scl.company_id='{$paramArray['company_id']}' and scl.stuchange_code='A07'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $changelogwhere .= " AND scl.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $delayStuResult = $this->DataControl->selectOne("select count(scl.changelog_id) as delayStuNum
              from smc_student_changelog as scl
              left join smc_student_change as sc on sc.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sc.reason_code and csr.company_id='{$this->company_id}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as cou on cou.course_id=c.course_id
              left join smc_school as l on scl.school_id=l.school_id
              where {$changelogwhere} and l.school_istest=0 and l.school_isclose=0
              and not exists(select 1 from smc_student_study X,smc_class Y,smc_course Z where X.class_id=Y.class_id AND Y.course_id=Z.course_id
                            AND Z.coursetype_id=cou.coursetype_id AND X.student_id=scl.student_id AND X.school_id=scl.school_id
							AND X.study_endday>scl.changelog_day)");

        //待开班数
        $classwhere = "c.company_id = '{$this->company_id}' and c.class_status >= '0' and c.class_stdate > CURDATE()";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $classwhere .= " AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $stayClassResult=$this->DataControl->selectOne("select count(distinct c.class_id) as stayClassNum from smc_class as c where {$classwhere}");

        $result = array();
        $result['schoolNum'] = $schoolNum['schoolNum'] ? $schoolNum['schoolNum'] : 0;
        $result['stafferNum'] = $stafferNum['stafferNum'] ? $stafferNum['stafferNum'] : 0;
        $result['courseNum'] = $courseNum['courseNum'] ? $courseNum['courseNum'] : 0;
        $result['clientNum'] = $clientNum['clientNum'] ? $clientNum['clientNum'] : 0;
        $result['activityNum'] = $activityNum['activityNum'] ? $activityNum['activityNum'] : 0;
        $result['rosterNum'] = $rosterNum['rosterNum'] ? $rosterNum['rosterNum'] : 0;
        $result['studentNum'] = $studentNumResult['studentNum'] ? $studentNumResult['studentNum'] : 0;
        $result['studyStuNum'] = $studyStudentResult['studyStuNum'] ? $studyStudentResult['studyStuNum'] : 0;
        $result['delayStuNum'] = $delayStuResult['delayStuNum'] ? $delayStuResult['delayStuNum'] : 0;
        $result['stayClassNum'] = $stayClassResult['stayClassNum'] ? $stayClassResult['stayClassNum'] : 0;

        return array('error' => 0, 'errortip' => "获取成功!", 'result' => $result);
    }

    //集团总览
    function companyAssume($paramArray){
        /*$postbeOne = $this->DataControl->selectOne("select b.organize_id,p.postrole_dataequity from gmc_staffer_postbe AS b,gmc_company_postrole as p
WHERE p.company_id = '{$paramArray['company_id']}' AND b.postrole_id = p.postrole_id and b.postbe_id = '{$paramArray['re_postbe_id']}' limit 0,1");

        $starttime=strtotime($paramArray['starttime']);
        $endtime=strtotime($paramArray['endtime'].' 23:59:59');

        $starttoday = date('Y-m-d');
        $endtoday = date('Y-m-d').' 23:59:59';

        $schoolwhere = "s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' and s.school_createtime>= '{$starttime}' and s.school_createtime <= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $schoolwhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        $newSchool = $this->DataControl->selectOne("select count(s.school_id) as newSchool from smc_school as s WHERE {$schoolwhere}");

        $stafferwhere = "s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '0' and s.account_class<>'1' and s.staffer_createtime >= '{$starttime}' and s.staffer_createtime <= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $stafferwhere .= " AND s.staffer_id IN (SELECT p.staffer_id FROM gmc_staffer_postbe AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }
        $stafferNum = $this->DataControl->selectOne("select count(s.staffer_id) as stafferNum from smc_staffer as s WHERE {$stafferwhere}");

        $leavewhere = "s.company_id = '{$paramArray['company_id']}' and s.staffer_leave = '1' and s.staffer_leavetime >= '{$starttime}' and s.staffer_leavetime <= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $leavewhere .= " AND s.staffer_id IN (SELECT p.staffer_id FROM gmc_staffer_postbe AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }
        $leaveStaffer = $this->DataControl->selectOne("select count(s.staffer_id) as leaveStaffer from smc_staffer as s WHERE {$leavewhere}");


//        $activitywhere = "a.company_id = '{$this->company_id}' and a.activity_starttime >= '{$starttime}' and a.activity_endtime <= '{$endtime}'";
        $activitywhere = "a.company_id = '{$this->company_id}' and a.activity_endtime >= '{$endtoday}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $activitywhere .= " AND a.activity_id IN (SELECT p.activity_id FROM crm_sell_activity_school AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }
        $activityNum = $this->DataControl->selectOne("select count(a.activity_id) as activityNum from crm_sell_activity as a where {$activitywhere}");


        $norderwhere = "st.tradingtype_code='PaynewFee' 
                        and st.company_id='{$this->company_id}' 
                        AND st.trading_createtime >= '{$starttime}'
                        AND st.trading_createtime <= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $norderwhere .= " AND st.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $a = $this->DataControl->selectClear("select st.student_id
                                                from smc_student_trading as st
                                                where {$norderwhere} 
                                                group by st.student_id");

        $oorderwhere = "st.tradingtype_code='PayrenewFee'
                        and st.company_id='{$this->company_id}' 
                        AND st.trading_createtime >= '{$starttime}' 
                        AND st.trading_createtime <= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $oorderwhere .= " AND st.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $b = $this->DataControl->selectClear("select st.student_id 
                                                    FROM smc_student_trading as st
                                                    where {$oorderwhere} 
                                                    group by st.student_id");

        $losewhere = "si.company_id='{$this->company_id}'
                    and si.stuchange_code in ('C02','C03','B05')
                    AND si.changelog_createtime >= '{$starttime}'
                    AND si.changelog_createtime <= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $losewhere .= " AND si.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        $c = $this->DataControl->selectClear("select si.student_id 
                                                    from smc_student_changelog as si 
                                                    where {$losewhere} 
                                                    group by si.student_id");


        $rosterwhere = "t.company_id = '{$this->company_id}' and  (client_tracestatus BETWEEN 0 and 3) AND t.client_createtime>= '{$starttime}' AND t.client_createtime<= '{$endtime}'";
        if($postbeOne && $postbeOne['postrole_dataequity'] == '0'){
            $rosterwhere .= " AND t.client_id IN (SELECT p.client_id FROM crm_client_schoolenter AS p,gmc_company_organizeschool as o
            WHERE p.school_id = o.school_id AND o.organize_id = '{$postbeOne['organize_id']}' GROUP BY p.school_id)";
        }
        $rosterNum = $this->DataControl->selectOne("select count(t.client_id) as rosterNum from crm_client as t where {$rosterwhere} ");

        $result = array();
        $result['newSchool'] = $newSchool['newSchool'];
        $result['stafferNum'] = $stafferNum['stafferNum'];
        $result['leaveStaffer'] = $leaveStaffer['leaveStaffer'];
        $result['activityNum'] = $activityNum['activityNum'];
        $result['newBook'] = $a?count($a):0;
        $result['oldBook'] = $b?count($b):0;
        $result['loseStu'] = $c?count($c):0;
        $result['rosterNum'] = $rosterNum['rosterNum'];*/

        $result = array();
        $result['newSchool'] = 0;
        $result['stafferNum'] = 0;
        $result['leaveStaffer'] = 0;
        $result['activityNum'] = 0;
        $result['newBook'] = 0;
        $result['oldBook'] = 0;
        $result['loseStu'] = 0;
        $result['rosterNum'] = 0;

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result),$this->companyOne['company_language']);


    }

    function toDo($request){

        //退费审核
        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
        }
        if ($request['dataequity'] == '1') {
            $sql = "
            SELECT
                o.refund_id
            FROM
                smc_refund_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
            WHERE
                o.refund_status > 0 and o.company_id = '{$request['company_id']}' and refund_status < '4'
            ORDER BY
                o.refund_id DESC";
        } else {
            $sql = "
            SELECT
                o.refund_id
            FROM
                smc_refund_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
            WHERE
               o.refund_status > 0 and o.company_id = '{$request['company_id']}' and o.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') and refund_status < '4'
            ORDER BY
                o.refund_id DESC";

        }
        $refundList=$this->DataControl->selectClear($sql);
        $aa = $refundList?count($refundList):0;

        $a = array();
        $a['count'] = $aa;
        $a['text'] = $this->LgStringSwitch('您有').$aa.$this->LgStringSwitch('个校园退费需要审核');
        $a['status'] = '1';
        $a['url'] = '/TradingCenter/CampusOrder/campusRefund';

        //续费订单
        if ($request['dataequity'] == '1') {
            $sql = "select po.order_id
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_school as sch on sch.school_id=po.school_id
              where po.company_id='{$request['company_id']}' and po.order_isneedaudit=1 and po.order_status = '0'
              order by po.order_createtime desc
              ";
        } else {
            $sql = "select po.order_id
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_school as sch on sch.school_id=po.school_id
              where po.company_id='{$request['company_id']}' and po.order_isneedaudit=1 and po.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') and po.order_status = '0'
              order by po.order_createtime desc
              ";
        }
        $orderList=$this->DataControl->selectClear($sql);
        $bb = $orderList?count($orderList):0;

        $b = array();
        $b['count'] = $bb;
        $b['text'] = $this->LgStringSwitch('您有').$bb.$this->LgStringSwitch('个续费订单需要审核');
        $b['status'] = '1';
        $b['url'] = '/TradingCenter/CampusOrder/renewalOrder';

        //课时扣除订单
        if ($request['dataequity'] == '1') {
            $sql = "select cr.reduceorder_id
              from smc_course_reduceorder as cr
              left join smc_student as s on s.student_id=cr.student_id
              left join smc_school as sch on sch.school_id=cr.school_id
              where cr.company_id='{$request['company_id']}' and cr.reduceorder_status = '0'
              order by cr.reduceorder_createtime desc
              ";
        } else {
            $sql = "select cr.reduceorder_id
              from smc_course_reduceorder as cr
              left join smc_student as s on s.student_id=cr.student_id
              left join smc_school as sch on sch.school_id=cr.school_id
              where cr.company_id='{$request['company_id']}' and cr.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') and cr.reduceorder_status = '0'
              order by cr.reduceorder_createtime desc
              ";
        }
        $reduceorderList=$this->DataControl->selectClear($sql);
        $cc = $reduceorderList?count($reduceorderList):0;

        $c = array();
        $c['count'] = $cc;
        $c['text'] = $this->LgStringSwitch('您有').$cc.$this->LgStringSwitch('个课时扣除订单需要审核');
        $c['status'] = '1';
        $c['url'] = '/TradingCenter/CampusOrder/classDeduction';

        //赠送课程
        if ($request['dataequity'] == '1') {
            $sql = "
            SELECT
                o.order_id
            FROM
                smc_freehour_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
                left join smc_course as sc on sc.course_id=o.course_id
            WHERE
                o.order_isneedaudit ='1' and o.company_id = '{$request['company_id']}' and o.order_status = '0'
            ORDER BY
                o.order_id DESC";
        } else {
            $sql = "
            SELECT
                o.order_id
            FROM
                smc_freehour_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
                left join smc_course as sc on sc.course_id=o.course_id
            WHERE
                o.order_isneedaudit ='1' and o.company_id = '{$request['company_id']}' and o.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') and o.order_status = '0'
            ORDER BY
                o.order_id DESC";
        }
        $freehourList=$this->DataControl->selectClear($sql);
        $dd = $freehourList?count($freehourList):0;

        $d = array();
        $d['count'] = $dd;
        $d['text'] = $this->LgStringSwitch('您有').$dd.$this->LgStringSwitch('个赠送课程需要审核');
        $d['status'] = '1';
        $d['url'] = '/TradingCenter/CampusOrder/freeGive';

        //结转课次
        if ($request['dataequity'] == '1') {
            $sql = "select fd.dealorder_id
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            left join smc_school as s on s.school_id=fd.school_id
            WHERE
                fd.company_id = '{$request['company_id']}' and fd.dealorder_status = '0'
            ORDER BY
                fd.dealorder_createtime DESC";
        } else {
            $sql = "select fd.dealorder_id
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            left join smc_school as s on s.school_id=fd.school_id
            WHERE
                fd.company_id = '{$request['company_id']}' and fd.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') and fd.dealorder_status = '0'
            ORDER BY
                fd.dealorder_createtime DESC";
        }
        $dealorderList=$this->DataControl->selectClear($sql);
        $ee = $dealorderList?count($dealorderList):0;

        $e = array();
        $e['count'] = $ee;
        $e['text'] = $this->LgStringSwitch('您有').$ee.$this->LgStringSwitch('个结转课次需要审核');
        $e['status'] = '1';
        $e['url'] = '/TradingCenter/CampusOrder/carryOver';

        //减免学费
        if ($request['dataequity'] == '1') {
            $sql = "select o.order_id
            from smc_payfee_order_pay as po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            WHERE o.company_id = '{$request['company_id']}'
            and po.pay_issuccess = '0'
            and po.paytype_code='feewaiver'
            ORDER BY po.pay_createtime DESC";
        } else {
            $sql = "select o.order_id 
            from smc_payfee_order_pay as po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            WHERE o.company_id = '{$request['company_id']}' 
            and o.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') 
            and po.pay_issuccess = '0' 
            and po.paytype_code='feewaiver'
            ORDER BY po.pay_createtime DESC";
        }
        $payList=$this->DataControl->selectClear($sql);
        $ff = $payList?count($payList):0;

        $f = array();
        $f['count'] = $ff;
        $f['text'] = $this->LgStringSwitch('您有').$ff.$this->LgStringSwitch('个减免学费需要审核');
        $f['status'] = '1';
        $f['url'] = '/TradingCenter/CampusOrder/deduction';

        //招生活动
        $todaytime = date("Y-m-d");
        if($request['dataequity'] == '1'){
            $sql="SELECT  
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=a.channel_id limit 0,1) as channel_name
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE a.company_id = '{$request['company_id']}' and a.activity_type = '1' and a.activity_status = '1'
                ORDER BY activity_id DESC";
        }else{
            $sql="SELECT
                (select ch.channel_name from crm_code_channel as ch where ch.channel_id=a.channel_id limit 0,1) as channel_name,
	(
SELECT
	group_concat( cyo.organize_id ) 
FROM
	crm_channel_organize AS co
	LEFT JOIN gmc_company_organize AS cyo ON cyo.organize_id = co.organize_id 
WHERE
	co.channel_id = a.channel_id 
	) AS organize_id
                FROM crm_sell_activity as a
                LEFT JOIN smc_staffer as s ON a.staffer_id = s.staffer_id
                WHERE a.company_id = '{$request['company_id']}' and a.activity_type = '1' and FROM_UNIXTIME(unix_timestamp(a.activity_starttime),'%Y-%m-%d' ) <='{$todaytime}' and  FROM_UNIXTIME(unix_timestamp(a.activity_endtime),'%Y-%m-%d') >='{$todaytime}'  
                having organize_id like '%{$organize_id['organize_id']}%'
                ORDER BY activity_id DESC";
        }
        $activityList=$this->DataControl->selectClear($sql);
        $gg = $activityList?count($activityList):0;

        $g = array();
        $g['count'] = $gg;
        $g['text'] = $this->LgStringSwitch('您有').$gg.$this->LgStringSwitch('个招生活动正在进行');
        $g['status'] = '0';
        $g['url'] = '/EnrollmentManage/admisActivitiesManage/activityManage';

        $contractOne = $this->getContract($this->company_id);
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$a['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['a'] = $a;
        }
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$b['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['b'] = $b;
        }
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$c['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['c'] = $c;
        }
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$d['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['d'] = $d;
        }
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$e['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['e'] = $e;
        }
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$f['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['f'] = $f;
        }
        $module = $this->DataControl->getFieldOne("imc_module","module_id,module_class,product_id","module_markurl = '{$g['url']}'");
        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$module['module_class']}'");
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");
        $powerOne = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postbeOne['postrole_id']}' and module_id = '{$module['module_id']}'");
        if ($contractOne && $promoduleList && ($powerOne || $request['re_postbe_id'] == 0)) {
            $result['g'] = $g;
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result),$this->companyOne['company_language']);

    }

    //初始化设置
    function initializationSet($paramArray){
        $initialOne = $this->DataControl->selectOne("SELECT
        (SELECT IFNULL(l.companies_id, '0') FROM gmc_code_companies AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS aa,
        (SELECT IFNULL(l.district_id, '0') FROM gmc_company_district AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS ab,
        (SELECT IFNULL(l.school_id, '0') FROM smc_school AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS ac,
        (SELECT IFNULL(l.organize_id, '0') FROM gmc_company_organize AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS ba,
        (SELECT IFNULL(l.postrole_id, '0') FROM gmc_company_postrole AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS ca,
        (SELECT IFNULL(l.postpart_id, '0') FROM smc_school_postpart AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS cb,
        (SELECT IFNULL(l.postlevel_id, '0') FROM gmc_company_postlevel AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS cc,
        (SELECT IFNULL(l.post_id, '0') FROM gmc_company_post AS l WHERE l.company_id = c.company_id and l.post_type = '0' LIMIT 0, 1) AS cd,
        (SELECT IFNULL(l.post_id, '0') FROM gmc_company_post AS l WHERE l.company_id = c.company_id and l.post_type = '1' LIMIT 0, 1) AS ce,
        (SELECT IFNULL(l.staffer_id, '0') FROM smc_staffer AS l WHERE l.company_id = c.company_id and l.account_class = '0' LIMIT 0, 1) AS cf,
        (SELECT IFNULL(l.coursetype_id, '0') FROM smc_code_coursetype AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS da,
        (SELECT IFNULL(l.coursecat_id, '0') FROM smc_code_coursecat AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS db,
        (SELECT IFNULL(l.course_id, '0') FROM smc_course AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS dc,
        (SELECT IFNULL(l.feeitem_id, '0') FROM smc_code_feeitem AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS dd,
        (SELECT IFNULL(l.coursetimes_id, '0') FROM smc_code_coursetimes AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS de,
        (SELECT IFNULL(l.agreement_id, '0') FROM smc_fee_agreement AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS df,
        (SELECT IFNULL(l.prodtype_id, '0') FROM smc_code_prodtype AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS ea,
        (SELECT IFNULL(l.goods_id, '0') FROM erp_goods AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS eb,
        (SELECT IFNULL(l.company_id, '0') FROM gmc_company AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fa,
        (SELECT IFNULL(l.reason_id, '0') FROM smc_code_stuchange_reason AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fb,
        (SELECT IFNULL(l.holidays_id, '0') FROM smc_code_holidays AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fc,
        (SELECT IFNULL(l.teachtype_id, '0') FROM smc_code_teachtype AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fd,
        (SELECT IFNULL(l.commode_id, '0') FROM crm_code_commode AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fe,
        (SELECT IFNULL(l.tracenote_id, '0') FROM crm_code_tracenote AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS ff,
        (SELECT IFNULL(l.reason_id, '0') FROM crm_code_isvisitreason AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fg,
        (SELECT IFNULL(l.nearschool_id, '0') FROM crm_code_nearschool AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fh,
        (SELECT IFNULL(l.activitybuy_id, '0') FROM gmc_company_activitybuy AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fi,
        (SELECT IFNULL(l.applytype_id, '0') FROM smc_code_couponsapplytype AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fj,
        (SELECT IFNULL(l.noticetype_id, '0') FROM smc_code_noticetype AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fk,
        (SELECT IFNULL(l.apppropermis_id, '0') FROM smc_code_apppropermis AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fl,
        (SELECT IFNULL(l.sturemarktemp_id, '0') FROM eas_code_sturemarktemp AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fm,
        (SELECT IFNULL(l.remarktemp_id, '0') FROM eas_code_remarktemp AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fn,
        (SELECT IFNULL(l.homeworktemp_id, '0') FROM eas_code_homeworktemp AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fo,
        (SELECT IFNULL(l.comtemp_id, '0') FROM eas_code_comtemp AS l WHERE l.company_id = c.company_id LIMIT 0, 1) AS fp
        FROM gmc_company AS c WHERE c.company_id = '{$paramArray['company_id']}'");

        $result = array();
        $result['aa'] = ($initialOne['aa'])?true:false;
        $result['ab'] = ($initialOne['ab'])?true:false;
        $result['ac'] = ($initialOne['ac'])?true:false;
        $result['ba'] = ($initialOne['ba'])?true:false;
        $result['ca'] = ($initialOne['ca'])?true:false;
        $result['cb'] = ($initialOne['cb'])?true:false;
        $result['cc'] = ($initialOne['cc'])?true:false;
        $result['cd'] = ($initialOne['cd'])?true:false;
        $result['ce'] = ($initialOne['ce'])?true:false;
        $result['cf'] = ($initialOne['cf'])?true:false;
        $result['da'] = ($initialOne['da'])?true:false;
        $result['db'] = ($initialOne['db'])?true:false;
        $result['dc'] = ($initialOne['dc'])?true:false;
        $result['dd'] = ($initialOne['dd'])?true:false;
        $result['de'] = ($initialOne['de'])?true:false;
        $result['df'] = ($initialOne['df'])?true:false;
        $result['ea'] = ($initialOne['ea'])?true:false;
        $result['eb'] = ($initialOne['eb'])?true:false;
        $result['fa'] = ($initialOne['fa'])?true:false;
        $result['fb'] = ($initialOne['fb'])?true:false;
        $result['fc'] = ($initialOne['fc'])?true:false;
        $result['fd'] = ($initialOne['fd'])?true:false;
        $result['fe'] = ($initialOne['fe'])?true:false;
        $result['ff'] = ($initialOne['ff'])?true:false;
        $result['fg'] = ($initialOne['fg'])?true:false;
        $result['fh'] = ($initialOne['fh'])?true:false;
        $result['fi'] = ($initialOne['fi'])?true:false;
        $result['fj'] = ($initialOne['fj'])?true:false;
        $result['fk'] = ($initialOne['fk'])?true:false;
        $result['fl'] = ($initialOne['fl'])?true:false;
        $result['fm'] = ($initialOne['fm'])?true:false;
        $result['fn'] = ($initialOne['fn'])?true:false;
        $result['fo'] = ($initialOne['fo'])?true:false;
        $result['fp'] = ($initialOne['fp'])?true:false;

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result),$this->companyOne['company_language']);
    }

    function monthStuBook($request){

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $time1 = $starttime;
        $time2 = $endtime;
        $dayArr = array();

        while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
            $dayArr[] = date('Y-m-d', $time1);
            $time1 = strtotime("+1 day", $time1);
        }

        $sql="
            SELECT
                sum( a.num ) as nums,
                a.trading_createtime 
            FROM
                (
            SELECT
                FROM_UNIXTIME( t.trading_createtime, '%Y-%m-%d' ) AS trading_createtime,
                count( t.student_id ) AS num 
            FROM
                smc_student_trading AS t
                LEFT JOIN smc_payfee_order AS o ON o.trading_pid = t.trading_pid 
            WHERE
                o.order_status > '-1' 
                AND t.company_id = '{$request['company_id']}'
                AND t.tradingtype_code = 'PaynewFee' 
                and t.trading_createtime >='{$starttime}' and t.trading_createtime <='{$endtime}'
            GROUP BY
                trading_createtime 
                ) AS a 
            GROUP BY
                a.trading_createtime
                ";
        $newStu=$this->DataControl->selectClear($sql);

        $sql="SELECT
                sum( a.num ) as nums,
                a.trading_createtime 
            FROM
                (
            SELECT
                FROM_UNIXTIME( t.trading_createtime, '%Y-%m-%d' ) AS trading_createtime,
                count( t.student_id ) AS num 
            FROM
                smc_student_trading AS t
                LEFT JOIN smc_payfee_order AS o ON o.trading_pid = t.trading_pid 
            WHERE
                o.order_status > '-1' 
                AND t.company_id = '{$request['company_id']}'
                AND t.tradingtype_code = 'PayrenewFee' 
                and t.trading_createtime >='{$starttime}' and t.trading_createtime <='{$endtime}'
            GROUP BY
                trading_createtime 
                ) AS a 
            GROUP BY
                a.trading_createtime";
        $oldStu=$this->DataControl->selectClear($sql);


        $tem_newStu=array();
        if($newStu){
            foreach($dayArr as $dayOne){
                foreach($newStu as $one){
                    if($one['trading_createtime']==$dayOne){
                        $tem_newStu[$dayOne]=$one['nums'];
                    }else{
                        if(!$tem_newStu[$dayOne]){
                            $tem_newStu[$dayOne]=0;
                        }
                    }
                }
            }
            $tem_newStu=array_values($tem_newStu);
        }else{
            foreach ($dayArr as $dayOne) {
                $tem_newStu[]=0;
            }
        }

        $tem_oldStu=array();
        if($oldStu){
            foreach($dayArr as $dayOne){
                foreach($oldStu as $one){
                    if($one['trading_createtime']==$dayOne){
                        $tem_oldStu[$dayOne]=$one['nums'];
                    }else{
                        if(!$tem_oldStu[$dayOne]){
                            $tem_oldStu[$dayOne]=0;
                        }
                    }
                }
            }
            $tem_oldStu=array_values($tem_oldStu);
        }else{
            foreach ($dayArr as $dayOne) {
                $tem_oldStu[]=0;
            }
        }

        $data=array();
        $k=0;
        $data[$k]['name']=$this->LgStringSwitch('新生报名');
        $data[$k]['data']=$tem_newStu;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('老生报名');
        $data[$k]['data']=$tem_oldStu;
        $k++;

        $legendData=$this->LgArraySwitch(array("新生报名","老生报名"));

        $tem_data=array();
        $tem_data['allList']=$data;
        $tem_data['legendData']=$legendData;
        $tem_data['xAxisData']=$dayArr;
        return $tem_data;

    }




}