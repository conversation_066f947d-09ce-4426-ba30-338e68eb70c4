<?php


namespace Model\Gmc;

class OrderExamineModel extends modelTpl
{
    public $payfeeorderOne = false;//当前处理订单
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array(), $order_pid = 0, $from = 0)
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
        if ($order_pid !== '0') {
            $this->verdictpayFeeOrder($order_pid, $from);
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictpayFeeOrder($order_pid, $from)
    {
        $order_pid = trim($order_pid);
        if ($from == 0) {
            $this->payfeeorderOne = $this->DataControl->getOne("smc_payfee_order", "order_pid = '{$order_pid}'");
        } elseif ($from == 1) {
            $this->payfeeorderOne = $this->DataControl->getOne("smc_refund_order", "refund_pid = '{$order_pid}'");
        } elseif ($from == 2) {
            $this->payfeeorderOne = $this->DataControl->getOne("smc_course_reduceorder", "reduceorder_pid = '{$order_pid}'");
        } elseif ($from == 3) {
            $this->payfeeorderOne = $this->DataControl->getOne("smc_freehour_order", "order_pid = '{$order_pid}'");
        } elseif ($from == 4) {
            $this->payfeeorderOne = $this->DataControl->getOne("smc_forward_dealorder", "dealorder_pid = '{$order_pid}'");
        } elseif ($from == 5) {
            $this->payfeeorderOne = $this->DataControl->getOne("smc_student_clockorder", "clockorder_pid = '{$order_pid}'");
        }

        if (!$this->payfeeorderOne) {
            $this->error = true;
            $this->errortip = "订单信息不存在";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function examineRenewOrder($reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();

        $sql = "select poc.course_id from smc_payfee_order_course as poc where poc.order_pid='{$this->payfeeorderOne['order_pid']}'";
        $courseList = $this->DataControl->selectClear($sql);

        if ($courseList) {
            foreach ($courseList as $courseOne) {
                if ($this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_id", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$courseOne['course_id']}' and company_id='{$this->payfeeorderOne['company_id']}' and (coursebalance_figure>0 or coursebalance_time>0)")) {
                    $this->error = true;
                    $this->errortip = "订单包含已拥有课程,不可重复购买";
                    return false;
                }

            }
        }


        $priceOne = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$this->payfeeorderOne['order_pid']}' and pay_issuccess=1");

        if ($this->payfeeorderOne['order_paymentprice'] <= $priceOne['pay_price']) {
            $data['order_status'] = 4;
        } else {
            $data['order_status'] = 2;
        }

        $data['order_note'] = $reason;
        $data['order_examinetime'] = $time;
        $this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);


        $orderPayModel = new  \Model\Smc\OrderPayModel($this->publicarray, $this->payfeeorderOne['order_pid']);

        $orderPayModel->orderPaylog('', '', '', '', '', '', '1');


        $this->orderTracks($this->LgStringSwitch('审核订单'), $this->LgStringSwitch('订单审核通过，请尽快支付~'), $reason, $time);
        return true;
    }

    function getFreeOrderItem()
    {
        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        $sql = "select ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes
              from smc_freehour_ordertimes as fot
              left join smc_class_hour as ch on ch.hour_lessontimes=fot.hour_lessontimes and ch.class_id='{$this->payfeeorderOne['class_id']}' and ch.hour_iswarming=0
              where fot.order_pid='{$this->payfeeorderOne['order_pid']}' order by fot.hour_lessontimes asc
              ";
        $hourList = $this->DataControl->selectClear($sql);
        if ($hourList) {
            foreach ($hourList as &$hourOne) {
                $week = date('w', strtotime($hourOne['hour_day']));
                $hourOne['week_day'] = $this->LgStringSwitch('周') . $weekarray[$week];
                $hourOne['lessontimes'] = $this->LgStringSwitch('第') . $hourOne['hour_lessontimes'] . $this->LgStringSwitch('课次');
            }
        }

        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch", "course_id='{$this->payfeeorderOne['course_id']}' and company_id='{$this->company_id}'");
        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_branch,student_img,student_sex", "student_id='{$this->payfeeorderOne['student_id']}'");
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_branch", "school_id='{$this->payfeeorderOne['school_id']}'");
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_cnname,class_branch", "class_id='{$this->payfeeorderOne['class_id']}'");

        $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
        $data = array();
        $data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
        $data['school_cnname'] = $schoolOne['school_cnname'];
        $data['school_branch'] = $schoolOne['school_branch'];
        $data['student_cnname'] = $studentOne['student_cnname'];
        $data['student_enname'] = $studentOne['student_enname'];
        $data['student_branch'] = $studentOne['student_branch'];
        $data['student_img'] = $studentOne['student_img'];
        $data['student_sex'] = $studentOne['student_sex'];
        $data['course_cnname'] = $courseOne['course_cnname'];
        $data['course_branch'] = $courseOne['course_branch'];
        $data['class_cnname'] = $classOne['class_cnname'];
        $data['class_branch'] = $classOne['class_branch'];
        $data['order_alltimes'] = $this->payfeeorderOne['order_alltimes'];
        $data['order_refusereason'] = $this->payfeeorderOne['order_refusereason'];
        $data['tradingtype_code'] = $this->LgStringSwitch('赠送课程');
        $data['order_status_name'] = $status[$this->payfeeorderOne['order_status']];
        $data['order_status'] = $this->payfeeorderOne['order_status'];
        $data['order_img'] = $this->payfeeorderOne['order_img'];

        $sql = "select co.course_cnname,co.course_branch,cl.class_cnname,cl.class_branch
              ,ifnull((select SUM(poc.ordercourse_buynums) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=fc.course_id and po.student_id=fc.student_id and po.school_id=fc.school_id and po.order_status>0 limit 0,1),0) as ordercourse_buynums
              ,(select scb.coursebalance_time from smc_student_coursebalance as scb where scb.student_id=fc.student_id and scb.school_id=fc.school_id and scb.course_id=fc.course_id limit 0,1) as coursebalance_time
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=fc.student_id and sf.class_id=fc.class_id and fc.course_id=sf.course_id) as allNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=fc.student_id and sf.class_id=fc.class_id and fc.course_id=sf.course_id and sf.is_use=0) as freeNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=fc.student_id and sf.class_id=fc.class_id and fc.course_id=sf.course_id and sf.is_use=1) as useNum
              ,(select count(sf.coursetimes_id) from smc_student_free_coursetimes as sf where sf.student_id=fc.student_id and sf.class_id=fc.class_id and fc.course_id=sf.course_id and sf.is_use='-1') as cancelNum
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=fc.class_id and ch.hour_ischecking<>'-1') as allHourNum
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=fc.class_id and ch.hour_ischecking='1') as hourNum
              ,(select count(sh.hourstudy_id) from smc_student_hourstudy as sh where sh.class_id=fc.class_id and sh.student_id=fc.student_id) as checkNum
              from smc_student_free_coursetimes as fc
              left join smc_class as cl on cl.class_id=fc.class_id
              left join smc_course as co on co.course_id=cl.course_id
              where fc.student_id='{$this->payfeeorderOne['student_id']}' and fc.school_id='{$this->payfeeorderOne['school_id']}'
              group by fc.class_id
              ";

        $classList = $this->DataControl->selectClear($sql);
        if ($classList) {
            foreach ($classList as &$classOne) {
                $classOne['info'] = $classOne['hourNum'] . '/' . $classOne['allHourNum'];
            }
        } else {
            $classList = array();
        }

        $sql = "select t.*,st.staffer_cnname,st.staffer_enname from smc_freehour_order_tracks as t left join smc_staffer as st on st.staffer_id=t.staffer_id
              where t.order_pid='{$this->payfeeorderOne['order_pid']}' order by t.tracks_time desc";

        $trackList = $this->DataControl->selectClear($sql);

        if ($trackList) {
            foreach ($trackList as &$trackOne) {
                $trackOne['tracks_playname'] = $trackOne['staffer_enname'] ? $trackOne['staffer_cnname'] . '-' . $trackOne['staffer_enname'] : $trackOne['staffer_cnname'];
                $trackOne['tracks_time'] = date("Y-m-d H:i:s", $trackOne['tracks_time']);
                $trackOne['tracks_note'] = $trackOne['tracks_note'] ? $trackOne['tracks_note'] : $trackOne['tracks_information'];
            }
        } else {
            $trackList = array();
        }

        $sql = "select sch.school_cnname,st.staffer_cnname,st.staffer_enname,fo.order_createtime
              from smc_freehour_order as fo
              left join smc_staffer as st on st.staffer_id=fo.staffer_id
              left join smc_school as sch on sch.school_id=fo.school_id
              where fo.order_pid='{$this->payfeeorderOne['order_pid']}'";

        $schoolInfoOne = $this->DataControl->selectOne($sql);
        if ($schoolInfoOne) {
            $schoolInfoOne['staffer_cnname'] = $schoolInfoOne['staffer_enname'] ? $schoolInfoOne['staffer_cnname'] . '-' . $schoolInfoOne['staffer_enname'] : $schoolInfoOne['staffer_cnname'];
            $schoolInfoOne['order_createtime'] = date("Y-m-d H:i:s", $schoolInfoOne['order_createtime']);
        } else {
            $schoolInfoOne = array();
        }


        $field = array();
        $k = 0;

        $field[$k]["fieldname"] = "hour_day";
        $field[$k]["fieldstring"] = $this->LgStringSwitch("上课日期");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "week_day";
        $field[$k]["fieldstring"] = $this->LgStringSwitch("上课周次");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "hour_starttime";
        $field[$k]["fieldstring"] = $this->LgStringSwitch("开始时间");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "hour_endtime";
        $field[$k]["fieldstring"] = $this->LgStringSwitch("结束时间");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "lessontimes";
        $field[$k]["fieldstring"] = $this->LgStringSwitch("课次");
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;


        $class_field = array();
        $k = 0;

        $class_field[$k]["fieldname"] = "course_cnname";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("课程别名称");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "course_branch";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("课程别编号");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "class_cnname";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("班级名称");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "class_branch";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("班级编号");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "ordercourse_buynums";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("课程购买次数");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "coursebalance_time";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("课程剩余次数");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "allNum";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("赠送总课次数");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "freeNum";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("未使用赠送课次");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "useNum";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("已使用赠送课次");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "cancelNum";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("已取消赠送课次");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "info";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("班级考勤次数");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $class_field[$k]["fieldname"] = "checkNum";
        $class_field[$k]["fieldstring"] = $this->LgStringSwitch("学员班内考勤次数");
        $class_field[$k]["show"] = 1;
        $class_field[$k]["custom"] = 1;
        $k++;

        $track_field = array();
        $k = 0;

        $track_field[$k]["fieldname"] = "tracks_id";
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch("序号");
        $track_field[$k]["show"] = 1;
        $track_field[$k]["custom"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = "tracks_title";
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch("标题");
        $track_field[$k]["show"] = 1;
        $track_field[$k]["custom"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = "tracks_note";
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch("备注信息");
        $track_field[$k]["show"] = 1;
        $track_field[$k]["custom"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = "tracks_playname";
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch("操作人");
        $track_field[$k]["show"] = 1;
        $track_field[$k]["custom"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = "tracks_time";
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch("操作时间");
        $track_field[$k]["show"] = 1;
        $track_field[$k]["custom"] = 1;
        $k++;

        $school_field = array();
        $k = 0;

        $school_field[$k]["fieldname"] = "school_cnname";
        $school_field[$k]["fieldstring"] = $this->LgStringSwitch("经办分校");
        $school_field[$k]["show"] = 1;
        $school_field[$k]["custom"] = 1;
        $k++;

        $school_field[$k]["fieldname"] = "staffer_cnname";
        $school_field[$k]["fieldstring"] = $this->LgStringSwitch("经办人");
        $school_field[$k]["show"] = 1;
        $school_field[$k]["custom"] = 1;
        $k++;

        $school_field[$k]["fieldname"] = "order_createtime";
        $school_field[$k]["fieldstring"] = $this->LgStringSwitch("经办时间");
        $school_field[$k]["show"] = 1;
        $school_field[$k]["custom"] = 1;
        $k++;

        $return = array();
        $return['info'] = $data;
        $return['field'] = $field;
        $return['class_field'] = $class_field;
        $return['track_field'] = $track_field;
        $return['school_field'] = $school_field;
        $return['list'] = $hourList;
        $return['classList'] = $classList;
        $return['trackList'] = $trackList;
        $return['schoolInfo'][0] = $schoolInfoOne;

        return $return;

    }

    function adoptRenewOrder($reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $OrderHandleModel = new \Model\Smc\OrderHandleModel($this->publicarray, $this->payfeeorderOne['order_pid']);
        $OrderHandleModel->orderProcess();
        $data = array();
        $data['order_status'] = -2;
        $data['order_note'] = $reason;
        $data['order_updatatime'] = $time;
        $data['order_examinetime'] = $time;
        $this->DataControl->updateData("smc_payfee_order", "order_pid='{$this->payfeeorderOne['order_pid']}' and company_id='{$this->company_id}' and school_id='{$this->payfeeorderOne['school_id']}'", $data);
        $this->orderTracks($this->LgStringSwitch('审核订单'), $this->LgStringSwitch('订单审核拒绝'), $reason, $time);
        return true;

    }

    function orderTracks($title, $information, $note = '', $time = '')
    {
//        if ($time == '') {
//            $time = time();
//        }
        $orderTracksData = array();
        $orderTracksData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $orderTracksData['tracks_title'] = $title;
        $orderTracksData['tracks_information'] = $information;
        $orderTracksData['tracks_note'] = $note;
        $orderTracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $orderTracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
//        $orderTracksData['tracks_time'] = $time;//戚总确认日志时间用 系统时间
        $orderTracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_payfee_order_tracks", $orderTracksData);
    }

    function adoptReduceOrder($reason, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $sql = "select
(select ss.class_id from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id
where ss.company_id='{$this->payfeeorderOne['company_id']}' and c.course_id=scb.course_id and ss.study_isreading=1 and ss.student_id=scb.student_id limit 0,1) as class_id
,scb.coursebalance_figure,scb.coursebalance_time,scb.companies_id
              from smc_student_coursebalance as scb where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.company_id='{$this->company_id}' and scb.course_id='{$this->payfeeorderOne['course_id']}' and scb.school_id='{$this->payfeeorderOne['school_id']}'";
        $courseOne = $this->DataControl->selectOne($sql);
        if ($courseOne['coursebalance_figure'] < $this->payfeeorderOne['reduceorder_figure']) {
            $this->error = true;
            $this->errortip = "金额错误";
            return false;
        }

        if ($courseOne['coursebalance_time'] < $this->payfeeorderOne['reduceorder_time']) {
            $this->error = true;
            $this->errortip = "课次错误";
            return false;
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "companies_id", "school_id='{$this->payfeeorderOne['school_id']}'");

        $random = $this->create_guid();
        $coursebalancelog_data = array();
        $coursebalancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
        $coursebalancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
        $coursebalancelog_data['log_class'] = 0;
        $coursebalancelog_data['course_id'] = $this->payfeeorderOne['course_id'];
        $coursebalancelog_data['school_id'] = $this->payfeeorderOne['school_id'];
        $coursebalancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $coursebalancelog_data['class_id'] = $courseOne['class_id'];
        $coursebalancelog_data['log_random'] = $random;
        $coursebalancelog_data['log_playname'] = $this->LgStringSwitch('扣除课次');
        $coursebalancelog_data['log_playclass'] = '-';
        $coursebalancelog_data['log_fromamount'] = $courseOne['coursebalance_figure'];
        $coursebalancelog_data['log_playamount'] = $this->payfeeorderOne['reduceorder_figure'];
        $coursebalancelog_data['log_finalamount'] = $courseOne['coursebalance_figure'] - $this->payfeeorderOne['reduceorder_figure'];

        $coursebalancelog_data['log_fromtimes'] = $courseOne['coursebalance_time'];
        $coursebalancelog_data['log_playtimes'] = $this->payfeeorderOne['reduceorder_time'];
        $coursebalancelog_data['log_finaltimes'] = $courseOne['coursebalance_time'] - $this->payfeeorderOne['reduceorder_time'];

        $coursebalancelog_data['log_reason'] = $this->LgStringSwitch($reason);
        $coursebalancelog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $coursebalancelog_data);

        $time_data = array();
        $time_data['student_id'] = $this->payfeeorderOne['student_id'];
        $time_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
        $time_data['course_id'] = $this->payfeeorderOne['course_id'];
        $time_data['school_id'] = $this->payfeeorderOne['school_id'];
        $time_data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $time_data['class_id'] = $courseOne['class_id'];
        $time_data['log_random'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('扣除课次');
        $time_data['timelog_playclass'] = '-';
        $time_data['timelog_fromtimes'] = $courseOne['coursebalance_time'];
        $time_data['timelog_playtimes'] = $this->payfeeorderOne['reduceorder_time'];
        $time_data['timelog_finaltimes'] = $courseOne['coursebalance_time'] - $this->payfeeorderOne['reduceorder_time'];
        $time_data['timelog_reason'] = $this->LgStringSwitch($reason);
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

        $data = array();
        $data['coursebalance_figure'] = $courseOne['coursebalance_figure'] - $this->payfeeorderOne['reduceorder_figure'];
        $data['coursebalance_time'] = $courseOne['coursebalance_time'] - $this->payfeeorderOne['reduceorder_time'];
        $data['coursebalance_updatatime'] = time();
        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$this->payfeeorderOne['course_id']}' and company_id='{$this->company_id}' and school_id='{$this->payfeeorderOne['school_id']}'", $data);

        //$schoolOne = $this->DataControl->getFieldOne('smc_school',"school_cnname,companies_id","school_id='{$this->payfeeorderOne['school_id']}'");
        $in_data = array();
        $in_data['company_id'] = $this->company_id;
        $in_data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $in_data['school_id'] = $this->payfeeorderOne['school_id'];
        $in_data['course_id'] = $this->payfeeorderOne['course_id'];
        $in_data['class_id'] = $courseOne['class_id'];
        $in_data['income_type'] = '1';
        $in_data['student_id'] = $this->payfeeorderOne['student_id'];
        $in_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
        $in_data['income_price'] = $this->payfeeorderOne['reduceorder_figure'];;
        $in_data['income_note'] = $this->LgStringSwitch('扣除课程余额收入');
        $in_data['income_confirmtime'] = $time;
        $in_data['income_audittime'] = $time;
        $in_data['income_createtime'] = time();
        $this->DataControl->insertData("smc_school_income", $in_data);

        $data = array();
        $data['reduceorder_status'] = 1;
        $data['reduceorder_note'] = $reason;
        $data['reduceorder_updatatime'] = $time;
        $this->DataControl->updateData("smc_course_reduceorder", "reduceorder_pid='{$this->payfeeorderOne['reduceorder_pid']}'", $data);

        $TracksData = array();
        $TracksData['reduceorder_pid'] = $this->payfeeorderOne['reduceorder_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('订单审核通过，订单完成');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_course_reduceorder_tracks", $TracksData);

        $data = array();
        $data['trading_status'] = 1;
        $data['trading_updatatime'] = time();
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        return true;

    }

    function refuseReduceOrder($reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $data = array();
        $data['reduceorder_status'] = -1;
        $data['reduceorder_note'] = $reason;
        $data['reduceorder_updatatime'] = $time;
        $this->DataControl->updateData("smc_course_reduceorder", "reduceorder_pid='{$this->payfeeorderOne['reduceorder_pid']}'", $data);

        $TracksData = array();
        $TracksData['reduceorder_pid'] = $this->payfeeorderOne['reduceorder_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('订单审核拒绝，请查看拒绝原因');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_course_reduceorder_tracks", $TracksData);

        $data = array();
        $data['trading_status'] = -1;
        $data['trading_updatatime'] = time();
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);
        return true;
    }

    function refuseRefundOrder($reason = '', $time = '',$is_skip=0)
    {
        if ($time == '') {
            $time = time();
        }

        if($this->payfeeorderOne['requestid']>0 && $is_skip==0){
            $this->error = true;
            $this->errortip = "泛微流程订单不可审核";
            return false;

        }

        if ($this->payfeeorderOne['refund_status'] == '-1') {
            $this->error = true;
            $this->errortip = "不可重复拒绝!";
            return false;
        }

        $stublcOne = $this->getStuBalance($this->payfeeorderOne['student_id'], $this->company_id, $this->payfeeorderOne['school_id'], $this->payfeeorderOne['companies_id']);

        if ($this->payfeeorderOne['refund_forward'] > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['school_id'] = $this->payfeeorderOne['school_id'];
            $balancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $balancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
            $balancelog_data['balancelog_class'] = 1;
            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('退款清空结转金额转账户结转金额');
            $balancelog_data['balancelog_playclass'] = '+';
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_forwardprice'];
            $balancelog_data['balancelog_playamount'] = $this->payfeeorderOne['refund_forward'];
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_forwardprice'] + $this->payfeeorderOne['refund_forward'];
            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('退款清空结转金额转账户结转金额');
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

            $data = array();
            $data['student_forwardprice'] = $stublcOne['student_forwardprice'] + $this->payfeeorderOne['refund_forward'];
            $data['student_updatatime'] = $time;
            $this->DataControl->updateData("smc_student", "student_id='{$this->payfeeorderOne['school_id']}' and company_id='{$this->company_id}'", $data);
        }

        if ($this->payfeeorderOne['refund_price'] > 0) {
            $balancelog_data = array();
            $balancelog_data['company_id'] = $this->company_id;
            $balancelog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $balancelog_data['school_id'] = $this->payfeeorderOne['school_id'];
            $balancelog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $balancelog_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $balancelog_data['student_id'] = $this->payfeeorderOne['student_id'];
            $balancelog_data['balancelog_class'] = 0;
            $balancelog_data['balancelog_playname'] = $this->LgStringSwitch('申请退款金额转账户余额');
            $balancelog_data['balancelog_playclass'] = '+';
            $balancelog_data['balancelog_fromamount'] = $stublcOne['student_balance'];
            $balancelog_data['balancelog_playamount'] = $this->payfeeorderOne['refund_price'];
            $balancelog_data['balancelog_finalamount'] = $stublcOne['student_balance'] + $this->payfeeorderOne['refund_price'];
            $balancelog_data['balancelog_reason'] = $this->LgStringSwitch('申请退款金额转账户余额');
            $balancelog_data['balancelog_time'] = $time;
            $this->DataControl->insertData("smc_student_balancelog", $balancelog_data);

            $data = array();
            $data['student_balance'] = $stublcOne['student_balance'] + $this->payfeeorderOne['refund_price'];
            $this->DataControl->updateData("smc_student_balance", "student_id='{$this->payfeeorderOne['student_id']}' and school_id = '{$this->payfeeorderOne['school_id']}' and company_id='{$this->company_id}' and companies_id='{$this->payfeeorderOne['companies_id']}'", $data);

        }

        $TracksData = array();
        $TracksData['refund_pid'] = $this->payfeeorderOne['refund_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('财务审核拒绝，请查看拒绝原因');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);

        $data = array();
        $data['refund_status'] = -1;
        $data['refund_updatatime'] = time();
        $this->DataControl->updateData("smc_refund_order", "refund_pid='{$this->payfeeorderOne['refund_pid']}'", $data);

        $data = array();
        $data['trading_status'] = -1;
        $data['trading_updatatime'] = time();
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        return true;
    }

    function refuseForwardApplication($application_id,$reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $sql = "select * from smc_forward_application where application_id='{$application_id}'";
        $applicationOne = $this->DataControl->selectOne($sql);

        if($applicationOne['application_status']!=0){
            $this->error = true;
            $this->errortip = "不可重复审核!";
            return false;
        }

        $data = array();
        $data['application_status'] = 2;
        $data['approval_note'] = $reason;
        $data['fanwei_status'] = 3;
        $data['approval_time'] = $time;
        $data['update_time'] = $time;
        if($this->DataControl->updateData("smc_forward_application", "application_id='{$application_id}'", $data)){

            $ForwardModel = new \Model\Smc\ForwardModel($this->publicarray);

            $ForwardModel->createApplicationTrack($application_id, '审核拒绝', '用户审核拒绝结转申请', 
                $reason, $this->stafferOne['staffer_id'], 'refuse');

            return true;

        }else{
            $this->error = true;
            $this->errortip = "结转申请不存在";
            return false;
        }
    }

    function adoptForwardApplication($application_id,$reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        $sql = "select application_status,out_class_date from smc_forward_application where application_id='{$application_id}'";
        $applicationOne = $this->DataControl->selectOne($sql);

        if($applicationOne['application_status']!=0){
            $this->error = true;
            $this->errortip = "不可重复审核!";
            return false;
        }

        $data = array();
        $data['application_status'] = 1;
        $data['approval_note'] = $reason;
        $data['fanwei_status'] = 2;
        $data['approval_time'] = $time;
        $data['update_time'] = $time;
        if($this->DataControl->updateData("smc_forward_application", "application_id='{$application_id}'", $data)){

            $ForwardModel = new \Model\Smc\ForwardModel($this->publicarray);

            $ForwardModel->createApplicationTrack($application_id, '审核通过', '用户审核通过结转申请', 
                $reason, $this->stafferOne['staffer_id'], 'adopt');

            $ForwardModel->createApplicationTask($application_id, 'auto_forward', strtotime($applicationOne['out_class_date']));

            return true;

        }else{
            $this->error = true;
            $this->errortip = "结转申请不存在";
            return false;
        }
    }

    function refuseDealOrder($reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }

        if ($this->payfeeorderOne['dealorder_status'] != '0') {
            $this->error = true;
            $this->errortip = "订单状态不可审核";
            return false;
        }

//        $studentOne=$this->DataControl->getOne("smc_student","student_id='{$this->payfeeorderOne['student_id']}'");

        $sql = "select dc.* from smc_forward_dealorder_course as dc
              where dc.dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}'";
        $orderCourseOne = $this->DataControl->selectOne($sql);

//        do{
//            $random=$this->createStuRandom($studentOne['student_branch']);
//        }while($this->DataControl->selectOne("select log_id from smc_student_coursebalance_log where log_random='{$random}' limit 0,1"));
        $random = $this->create_guid();

        $sql = "select scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.student_id=scb.student_id and scf.course_id=scb.course_id
              where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.course_id='{$orderCourseOne['course_id']}'";
        $coursebalance = $this->DataControl->selectOne($sql);

        $courselog_data = array();
        $courselog_data['student_id'] = $this->payfeeorderOne['student_id'];
        $courselog_data['log_class'] = 0;
        $courselog_data['school_id'] = $this->payfeeorderOne['school_id'];
        $courselog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $courselog_data['course_id'] = $orderCourseOne['course_id'];
        $courselog_data['log_random'] = $random;
        $courselog_data['log_playname'] = $this->LgStringSwitch('拒绝课次结转');
        $courselog_data['log_playclass'] = '+';
        $courselog_data['log_fromamount'] = $coursebalance['coursebalance_figure'];
        $courselog_data['log_playamount'] = $orderCourseOne['dealcourse_figure'];
        $courselog_data['log_finalamount'] = $coursebalance['coursebalance_figure'] + $orderCourseOne['dealcourse_figure'];

        $courselog_data['log_fromtimes'] = $coursebalance['coursebalance_time'];
        $courselog_data['log_playtimes'] = $orderCourseOne['dealcourse_time'];
        $courselog_data['log_finaltimes'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];

        $courselog_data['log_reason'] = $reason;
        $courselog_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

        $time_data = array();
        $time_data['student_id'] = $this->payfeeorderOne['student_id'];
        $time_data['school_id'] = $this->payfeeorderOne['school_id'];
        $time_data['companies_id'] = $this->payfeeorderOne['companies_id'];
        $time_data['course_id'] = $orderCourseOne['course_id'];
        $time_data['log_random'] = $random;
        $time_data['timelog_playname'] = $this->LgStringSwitch('拒绝课次结转');
        $time_data['timelog_playclass'] = '+';
        $time_data['timelog_fromtimes'] = $coursebalance['coursebalance_time'];
        $time_data['timelog_playtimes'] = $orderCourseOne['dealcourse_time'];
        $time_data['timelog_finaltimes'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];
        $time_data['timelog_reason'] = $reason;
        $time_data['timelog_time'] = $time;
        $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);


        if ($orderCourseOne['dealcourse_fromforwardprice'] > '0') {
            $data = array();
            $data['student_id'] = $this->payfeeorderOne['student_id'];
            $data['log_class'] = 1;
            $data['school_id'] = $this->payfeeorderOne['school_id'];
            $data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $data['course_id'] = $orderCourseOne['course_id'];
            $data['log_random'] = $random;
            $data['log_playname'] = $this->LgStringSwitch('拒绝课次结转');
            $data['log_playclass'] = '+';
            $data['log_fromamount'] = $coursebalance['courseforward_price'];
            $data['log_playamount'] = $orderCourseOne['dealcourse_fromforwardprice'];
            $data['log_finalamount'] = $coursebalance['courseforward_price'] + $orderCourseOne['dealcourse_fromforwardprice'];

            $data['timelog_fromtimes'] = $coursebalance['coursebalance_time'];
            $data['timelog_playtimes'] = $orderCourseOne['dealcourse_time'];
            $data['timelog_finaltimes'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];

            $data['log_reason'] = $reason;
            $data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $data);

            $data = array();
            $data['courseforward_price'] = $coursebalance['courseforward_price'] + $orderCourseOne['dealcourse_fromforwardprice'];
            $data['courseforward_updatatime'] = $time;
            $this->DataControl->updateData("smc_student_courseforward", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$orderCourseOne['course_id']}'", $data);
        }

        $student_coursebalance_data = array();
        $student_coursebalance_data['coursebalance_time'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];
        $student_coursebalance_data['coursebalance_figure'] = $coursebalance['coursebalance_figure'] + $orderCourseOne['dealcourse_figure'];
        $student_coursebalance_data['coursebalance_updatatime'] = time();
        $student_coursebalance_data['coursebalance_status'] = 1;

        $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$orderCourseOne['course_id']}' and school_id='{$this->payfeeorderOne['school_id']}'", $student_coursebalance_data);

        $TracksData = array();
        $TracksData['dealorder_pid'] = $this->payfeeorderOne['dealorder_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('课次结转订单审核拒绝，请查看拒绝原因');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_forward_dealorder_tracks", $TracksData);

        $data = array();
        $data['dealorder_status'] = '-1';
        $data['dealorder_updatatime'] = time();
        $this->DataControl->updateData("smc_forward_dealorder", "dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}'", $data);

        $data = array();
        $data['trading_status'] = '-1';
        $data['trading_cancelreason'] = $reason;
        $data['trading_updatatime'] = time();
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        return true;
    }

    function adoptDealOrder($reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        if ($this->payfeeorderOne['dealorder_status'] != '0') {
            $this->error = true;
            $this->errortip = "订单状态不可审核";
            return false;
        }

        if ($this->payfeeorderOne['to_class_id'] > 0) {
            if ($this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id='{$this->payfeeorderOne['student_id']}' and class_id='{$this->payfeeorderOne['to_class_id']}' and study_isreading='1'")) {
                $this->error = true;
                $this->errortip = "学员已经转入该班级,不可通过审核";
                return false;
            }
        }

        if ($this->payfeeorderOne['class_id'] > 0) {

            $this->publicarray['school_id'] = $this->payfeeorderOne['school_id'];

            $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
            $TransactionModel->outClass($this->payfeeorderOne['student_id'], $this->payfeeorderOne['class_id'], 0, $time, $this->payfeeorderOne['trading_pid']);
        }

        $sql = "select sc.course_sellclass,dc.* from smc_forward_dealorder_course as dc,smc_course sc 
              where dc.course_id=sc.course_id and dc.dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}'";
        $orderCourseOne = $this->DataControl->selectOne($sql);

        $sql = "select fp.course_id,pt.tuition_unitprice,fp.pricing_id,sc.course_sellclass
              from smc_fee_pricing as fp
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=fp.pricing_id
              left join smc_course sc on sc.course_id=fp.course_id
              where fp.pricing_id='{$orderCourseOne['dealcourse_topricing_id']}'";

        $pricingOne = $this->DataControl->selectOne($sql);

        $sql = "select scb.coursebalance_figure,scb.coursebalance_time,scf.courseforward_price,scf.courseforward_createtime
              from smc_student_coursebalance as scb
              left join smc_student_courseforward as scf on scf.student_id=scb.student_id and scf.course_id=scb.course_id
              where scb.student_id='{$this->payfeeorderOne['student_id']}' and scb.course_id='{$pricingOne['course_id']}' and scb.school_id='{$this->payfeeorderOne['school_id']}'";
        $coursebalance = $this->DataControl->selectOne($sql);

        if (!$orderCourseOne['dealcourse_fromforwardprice']) {
            $orderCourseOne['dealcourse_fromforwardprice'] = 0;
        }

        if ($coursebalance['coursebalance_figure'] > 0 || $coursebalance['coursebalance_time'] > 0 || $coursebalance['courseforward_price'] > 0) {

            $this->error = true;
            $this->errortip = "课程已存在余额,不可结转,请审核拒绝";
            return false;
        }

//        $pricinglog_must = !($orderCourseOne['course_sellclass'] == $coursebalance['course_sellclass']);

        $random = $this->create_guid();

        if ($coursebalance) {
            $courselog_data = array();
            $courselog_data['student_id'] = $this->payfeeorderOne['student_id'];
            $courselog_data['log_class'] = 0;
            $courselog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $courselog_data['school_id'] = $this->payfeeorderOne['school_id'];
            $courselog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $courselog_data['course_id'] = $pricingOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('课次结转');
            $courselog_data['log_playclass'] = '+';
            $courselog_data['log_fromamount'] = $coursebalance['coursebalance_figure'];
            $courselog_data['log_playamount'] = $orderCourseOne['dealcourse_figure'];
            $courselog_data['log_finalamount'] = $coursebalance['coursebalance_figure'] + $orderCourseOne['dealcourse_figure'];

            $courselog_data['log_fromtimes'] = $coursebalance['coursebalance_time'];
            $courselog_data['log_playtimes'] = $orderCourseOne['dealcourse_time'];
            $courselog_data['log_finaltimes'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];

            $courselog_data['log_reason'] = $reason;
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

            $time_data = array();
            $time_data['student_id'] = $this->payfeeorderOne['student_id'];
            $time_data['school_id'] = $this->payfeeorderOne['school_id'];
            $time_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $time_data['course_id'] = $pricingOne['course_id'];
            $time_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $time_data['log_random'] = $random;
            $time_data['timelog_playname'] = $this->LgStringSwitch('课次结转');
            $time_data['timelog_playclass'] = '+';
            $time_data['timelog_fromtimes'] = $coursebalance['coursebalance_time'];
            $time_data['timelog_playtimes'] = $orderCourseOne['dealcourse_time'];
            $time_data['timelog_finaltimes'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];
            $time_data['timelog_reason'] = $reason;
            $time_data['timelog_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

            $student_coursebalance_data = array();
            $student_coursebalance_data['coursebalance_time'] = $coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time'];
            $student_coursebalance_data['coursebalance_figure'] = $coursebalance['coursebalance_figure'] + $orderCourseOne['dealcourse_figure'];
            $student_coursebalance_data['coursebalance_unitexpend'] = ceil(($coursebalance['coursebalance_figure'] + $orderCourseOne['dealcourse_fromforwardprice'] + $orderCourseOne['dealcourse_figure']) / ($coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time']));
            $student_coursebalance_data['coursebalance_unitearning'] = ceil(($coursebalance['coursebalance_figure'] + $orderCourseOne['dealcourse_figure']) / ($coursebalance['coursebalance_time'] + $orderCourseOne['dealcourse_time']));
            $student_coursebalance_data['coursebalance_unitrefund'] = $pricingOne['tuition_unitprice'];

            if ($coursebalance['coursebalance_createtime'] > $time) {
                $student_coursebalance_data['coursebalance_createtime'] = $time;
            }

            $student_coursebalance_data['coursebalance_updatatime'] = time();

            $this->DataControl->updateData("smc_student_coursebalance", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$pricingOne['course_id']}' and school_id='{$this->payfeeorderOne['school_id']}'", $student_coursebalance_data);
        } else {
            $courselog_data = array();
            $courselog_data['student_id'] = $this->payfeeorderOne['student_id'];
            $courselog_data['log_class'] = 0;
            $courselog_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $courselog_data['school_id'] = $this->payfeeorderOne['school_id'];
            $courselog_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $courselog_data['course_id'] = $pricingOne['course_id'];
            $courselog_data['log_random'] = $random;
            $courselog_data['log_playname'] = $this->LgStringSwitch('课次结转');
            $courselog_data['log_playclass'] = '+';
            $courselog_data['log_fromamount'] = 0;
            $courselog_data['log_playamount'] = $orderCourseOne['dealcourse_figure'];
            $courselog_data['log_finalamount'] = $orderCourseOne['dealcourse_figure'];

            $courselog_data['log_fromtimes'] = 0;
            $courselog_data['log_playtimes'] = $orderCourseOne['dealcourse_time'];
            $courselog_data['log_finaltimes'] = $orderCourseOne['dealcourse_time'];

            $courselog_data['log_reason'] = $reason;
            $courselog_data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_log", $courselog_data);

            $time_data = array();
            $time_data['student_id'] = $this->payfeeorderOne['student_id'];
            $time_data['school_id'] = $this->payfeeorderOne['school_id'];
            $time_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $time_data['course_id'] = $pricingOne['course_id'];
            $time_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $time_data['log_random'] = $random;
            $time_data['timelog_playname'] = $this->LgStringSwitch('课次结转');
            $time_data['timelog_playclass'] = '+';
            $time_data['timelog_fromtimes'] = 0;
            $time_data['timelog_playtimes'] = $orderCourseOne['dealcourse_time'];
            $time_data['timelog_finaltimes'] = $orderCourseOne['dealcourse_time'];
            $time_data['timelog_reason'] = $reason;
            $time_data['timelog_time'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_timelog", $time_data);

            $student_coursebalance_data = array();
            $student_coursebalance_data['company_id'] = $this->company_id;
            $student_coursebalance_data['student_id'] = $this->payfeeorderOne['student_id'];
            $student_coursebalance_data['school_id'] = $this->payfeeorderOne['school_id'];
            $student_coursebalance_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $student_coursebalance_data['course_id'] = $pricingOne['course_id'];
            $student_coursebalance_data['pricing_id'] = $pricingOne['pricing_id'];
            $student_coursebalance_data['coursebalance_time'] = $orderCourseOne['dealcourse_time'];
            $student_coursebalance_data['coursebalance_figure'] = $orderCourseOne['dealcourse_figure'];
            $student_coursebalance_data['coursebalance_unitexpend'] = ceil(($orderCourseOne['dealcourse_figure'] + $orderCourseOne['dealcourse_fromforwardprice']) / $orderCourseOne['dealcourse_time']);
            $student_coursebalance_data['coursebalance_unitearning'] = ceil($orderCourseOne['dealcourse_figure'] / $orderCourseOne['dealcourse_time']);
            $student_coursebalance_data['coursebalance_unitrefund'] = $pricingOne['tuition_unitprice'];
            $student_coursebalance_data['coursebalance_createtime'] = $time;
            $student_coursebalance_data['coursebalance_updatatime'] = time();

            $this->DataControl->insertData("smc_student_coursebalance", $student_coursebalance_data);
        }

        if ($orderCourseOne['dealcourse_fromforwardprice'] > '0') {
            if ($coursebalance['courseforward_price'] || $coursebalance['courseforward_price'] == '0') {

                $data = array();
                $data['student_id'] = $this->payfeeorderOne['student_id'];
                $data['log_class'] = 1;
                $data['school_id'] = $this->payfeeorderOne['school_id'];
                $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                $data['course_id'] = $pricingOne['course_id'];
                $data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $data['log_random'] = $random;
                $data['log_playname'] = $this->LgStringSwitch('课次结转');
                $data['log_playclass'] = '+';
                $data['log_fromamount'] = $coursebalance['courseforward_price'];
                $data['log_playamount'] = $orderCourseOne['dealcourse_fromforwardprice'];
                $data['log_finalamount'] = $coursebalance['courseforward_price'] + $orderCourseOne['dealcourse_fromforwardprice'];

                $data['log_fromtimes'] = $coursebalance['coursebalance_time'] ? $coursebalance['coursebalance_time'] : 0;
                $data['log_playtimes'] = $orderCourseOne['dealcourse_time'];
                $data['log_finaltimes'] = ($coursebalance['coursebalance_time'] ? $coursebalance['coursebalance_time'] : 0) + $orderCourseOne['dealcourse_time'];

                $data['log_reason'] = $reason;
                $data['log_time'] = $time;
                $this->DataControl->insertData("smc_student_coursebalance_log", $data);

                $data = array();
                $data['courseforward_price'] = $coursebalance['courseforward_price'] + $orderCourseOne['dealcourse_fromforwardprice'];
                $data['courseforward_deductionmethod'] = $orderCourseOne['dealcourse_deductionmethod'];
                $data['courseforward_updatatime'] = $time;
                $this->DataControl->updateData("smc_student_courseforward", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$pricingOne['course_id']}'", $data);

            } else {
                $data = array();
                $data['student_id'] = $this->payfeeorderOne['student_id'];
                $data['log_class'] = 1;
                $data['school_id'] = $this->payfeeorderOne['school_id'];
                $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                $data['course_id'] = $pricingOne['course_id'];
                $data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $data['log_random'] = $random;
                $data['log_playname'] = $this->LgStringSwitch('课次结转');
                $data['log_playclass'] = '+';
                $data['log_fromamount'] = 0;
                $data['log_playamount'] = $orderCourseOne['dealcourse_fromforwardprice'];
                $data['log_finalamount'] = $orderCourseOne['dealcourse_fromforwardprice'];

                $data['log_fromtimes'] = $coursebalance['coursebalance_time'] ? $coursebalance['coursebalance_time'] : 0;
                $data['log_playtimes'] = $orderCourseOne['dealcourse_time'];
                $data['log_finaltimes'] = ($coursebalance['coursebalance_time'] ? $coursebalance['coursebalance_time'] : 0) + $orderCourseOne['dealcourse_time'];

                $data['log_reason'] = $reason;
                $data['log_time'] = $time;
                $this->DataControl->insertData("smc_student_coursebalance_log", $data);

                $data = array();
                $data['student_id'] = $this->payfeeorderOne['student_id'];
                $data['course_id'] = $pricingOne['course_id'];
                $data['courseforward_price'] = $coursebalance['courseforward_price'] + $orderCourseOne['dealcourse_fromforwardprice'];
                $data['courseforward_deductionmethod'] = $orderCourseOne['dealcourse_deductionmethod'];
                $data['courseforward_createtime'] = $time;
                $this->DataControl->insertData("smc_student_courseforward", $data);
            }
        }

        if ($orderCourseOne['course_sellclass'] !== $pricingOne['course_sellclass']) {
            $pricinglog = array();
            $pricinglog['student_id'] = $this->payfeeorderOne['student_id'];
            $pricinglog['school_id'] = $this->payfeeorderOne['school_id'];
            $pricinglog['course_id'] = $pricingOne['course_id'];
            $pricinglog['pricinglog_starttime'] = $time;
            $pricinglog['pricing_id'] = $pricingOne['pricing_id'];
            $pricinglog['order_pid'] = $this->payfeeorderOne['dealorder_pid'];
            $pricinglog['pricinglog_buytimes'] = $orderCourseOne['dealcourse_time'];
            $pricinglog['pricinglog_buyprice'] = $orderCourseOne['dealcourse_figure'];
            $pricinglog['pricinglog_unitexpend'] = ceil(($orderCourseOne['dealcourse_figure'] + $orderCourseOne['dealcourse_fromforwardprice']) / $orderCourseOne['dealcourse_time']);
            $pricinglog['pricinglog_unitrefund'] = $pricingOne['tuition_unitprice'];
            $pricinglog['pricinglog_createtime'] = $time;
            $this->DataControl->insertData("smc_student_coursebalance_pricinglog", $pricinglog);
        }

        $TracksData = array();
        $TracksData['dealorder_pid'] = $this->payfeeorderOne['dealorder_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('课次结转订单审核通过，订单完成');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_forward_dealorder_tracks", $TracksData);

        $data = array();
        $data['dealorder_status'] = '1';
        $data['dealorder_updatatime'] = time();
        $this->DataControl->updateData("smc_forward_dealorder", "dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}'", $data);

        $data = array();
        $data['trading_status'] = '1';
        $data['trading_updatatime'] = time();
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        if ($this->payfeeorderOne['to_class_id'] > 0) {

            $TransactionModel = new \Model\Smc\TransactionModel($this->publicarray);
            $TransactionModel->transferClass($this->payfeeorderOne['student_id'], $this->payfeeorderOne['class_id'], $this->payfeeorderOne['to_class_id'], '', $this->payfeeorderOne['dealorder_reason'], 0);
        }


        return true;

    }

    function ZhaohangRefundOrderQuery($request)
    {
        $Refund = new \Model\Scshop\BoingPayModel($this->company_id);
        $Refundref = $Refund->RefundQueryView($request['pay_pid'], $request['refund_pid']);
        if ($Refundref['status'] == 'OK') {
            return $Refundref['result'];
        } else {
            $this->error = true;
            $this->errortip = "招行反馈：" . $Refundref['message'];
            return false;
        }
    }

    function ZhaohangRefundOrder($request)
    {
        $Refund = new \Model\Scshop\BoingPayModel($this->company_id);
        $ref = $Refund->RefundView($request['pay_pid'], $this->payfeeorderOne['refund_pid']);
        if ($ref['status'] == '1') {
            $TracksData = array();
            $TracksData['refund_pid'] = $this->payfeeorderOne['refund_pid'];
            $TracksData['tracks_title'] = $this->LgStringSwitch('API退款');
            $TracksData['tracks_information'] = $this->LgStringSwitch("通过API退款触发{$request['pay_pid']}在线退款");
            $TracksData['tracks_note'] = $this->LgStringSwitch("通过API退款触发{$request['pay_pid']}在线退款");
            $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
            $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $TracksData['tracks_time'] = time();
            $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);
            return true;
        } elseif ($ref['status'] == '2') {
            $Refundref = $Refund->RefundQueryView($request['pay_pid'], $request['refund_pid']);
            if ($Refundref['result']['refund_result'] !== 'succeed') {
                $this->error = true;
                $this->errortip = "招行反馈1：" . $ref['message'];
                return false;
            }
            if ($Refundref['status'] == 'OK' && $Refundref['result']['refund_result'] == 'succeed') {
                $TracksData = array();
                $TracksData['refund_pid'] = $this->payfeeorderOne['refund_pid'];
                $TracksData['tracks_title'] = $this->LgStringSwitch('API退款');
                $TracksData['tracks_information'] = $this->LgStringSwitch("通过API退款触发{$request['pay_pid']}在线查询并退款");
                $TracksData['tracks_note'] = $this->LgStringSwitch("通过API退款触发{$request['pay_pid']}在线退款");
                $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
                $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $TracksData['tracks_time'] = time();
                $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);
                return true;
            } else {
                $this->error = true;
                $this->errortip = "招行反馈2：" . $ref['message'];
                return false;
            }
        } elseif ($ref['status'] == '3') {
            $this->error = true;
            $this->errortip = "招行反馈3：" . $ref['message'];
            return false;
        } else {
            $this->error = true;
            $this->errortip = "程序打印中";
            return false;
        }
    }


    function adoptRefundOrder($request, $time = '',$is_skip=0)
    {
        if ($time == '') {
            $time = time();
        }

        if ($this->payfeeorderOne['refund_status'] == '-1') {
            $this->error = true;
            $this->errortip = "订单状态不可审核";
            return false;
        }

        $data = array();

        if ($this->payfeeorderOne['refund_status'] == '1' || $this->payfeeorderOne['refund_status'] == '0') {

            $data['refund_status'] = '2';

            if($this->payfeeorderOne['requestid']>0 && $is_skip==0){
                $this->error = true;
                $this->errortip = "泛微流程订单不可审核";
                return false;
    
            }

        }
        if ($this->payfeeorderOne['refund_status'] == '2') {
            $data['refund_status'] = '3';
        }
        if ($this->payfeeorderOne['refund_status'] == '3') {
            $data['refund_status'] = '4';
        }
        $data['refund_updatatime'] = time();

        $order_status = $this->LgArraySwitch(array("0" => "申请", "1" => "审核通过", "2" => "确认处理", "3" => "确定金额", "4" => "完成退款", "-1" => "已拒绝"));

        if ($this->DataControl->updateData("smc_refund_order", "refund_pid = '{$this->payfeeorderOne['refund_pid']}'", $data)) {

            $TracksData = array();
            $TracksData['refund_pid'] = $this->payfeeorderOne['refund_pid'];

            if ($this->payfeeorderOne['refund_status'] == 2) {

                $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
            } else {

                $TracksData['tracks_title'] = $this->LgStringSwitch($order_status[$this->payfeeorderOne['refund_status']]);
            }

            if ($data['refund_status'] == 4) {
                $TracksData['tracks_information'] = $this->LgStringSwitch('出纳确定金额，退费完成');
            } elseif ($data['refund_status'] == 3) {
                $TracksData['tracks_information'] = $this->LgStringSwitch('财务') . $order_status[$this->payfeeorderOne['refund_status']] . '，等待出纳' . $order_status[$data['refund_status']];
            } else {
                $TracksData['tracks_information'] = $this->LgStringSwitch('财务') . $order_status[$this->payfeeorderOne['refund_status']] . '，等待财务' . $order_status[$data['refund_status']];
            }

            $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
            $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $TracksData['tracks_note'] = $request['reason'];
            $TracksData['tracks_time'] = time();
            $this->DataControl->insertData("smc_refund_order_tracks", $TracksData);

            if ($data['refund_status'] == 4) {
                $tdata = array();
                $tdata['trading_status'] = '1';
                $tdata['trading_updatatime'] = time();
                $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $tdata);
            }

            if ($this->payfeeorderOne['refund_status'] == '3' and $this->payfeeorderOne['refund_specialprice'] > 0 && $request['refund_checkpicurl'] != '') {
                $this->SureRefundAction($request, $time);
            }

            if ($this->payfeeorderOne['refund_status'] == 3) {
                if ($this->payfeeorderOne['refund_tradeclass'] == '1') {
                    $sql = "select e.erpgoods_isrestore,e.goods_id,og.ordergoods_buynums,og.ordergoods_totalprice,e.erpgoods_isreceive
                          from smc_refund_order_erpgoods as oe
                          left join smc_student_erpgoods as e on e.erpgoods_id=oe.erpgoods_id
                          left join smc_payfee_order_goods as og on og.order_pid=e.order_pid and og.goods_id=e.goods_id
                          where oe.refund_pid='{$this->payfeeorderOne['refund_pid']}'";
                    $goodOne = $this->DataControl->selectOne($sql);
                    if ($goodOne['erpgoods_isreceive'] == '1') {
                        $data = array();
                        do {
                            $trading_pid = $this->createOrderPid('RJ');
                        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));


                        $data['trading_pid'] = $trading_pid;
                        $data['company_id'] = $this->company_id;
                        $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                        $data['school_id'] = $this->payfeeorderOne['school_id'];
                        $data['student_id'] = $this->payfeeorderOne['student_id'];
                        $data['tradingtype_code'] = 'Subscribed';
                        $data['trading_status'] = "1";
                        $data['trading_createtime'] = $time;
                        $data['staffer_id'] = $this->stafferOne['staffer_id'];
                        $this->DataControl->insertData("smc_student_trading", $data);

                        $data = array();
                        $data['company_id'] = $this->company_id;
                        $data['companies_id'] = $this->payfeeorderOne['companies_id'];
                        $data['school_id'] = $this->payfeeorderOne['school_id'];
                        $data['expend_type'] = 1;
                        $data['student_id'] = $this->payfeeorderOne['student_id'];
                        $data['trading_pid'] = $trading_pid;
                        $data['expend_price'] = $this->payfeeorderOne['refund_payprice'];
                        $data['expend_note'] = $this->LgStringSwitch('商品退费支出');
                        $data['expend_confirmtime'] = $time;
                        $data['expend_createtime'] = $time;

                        $this->DataControl->insertData("smc_school_expend", $data);

                        if ($goodOne['erpgoods_isrestore'] == '1') {
                            $data = array();
                            do {
                                $beinorder_pid = $this->createOrderPid('RK');
                            } while ($this->DataControl->selectOne("select beinorder_id from smc_erp_beinorder where beinorder_pid='{$beinorder_pid}' limit 0,1"));
                            $data['beinorder_pid'] = $beinorder_pid;
                            $data['company_id'] = $this->company_id;
                            $data['school_id'] = $this->payfeeorderOne['school_id'];
                            $data['beinorder_from'] = '2';
                            $data['beinorder_status'] = '2';
                            $data['beinorder_storagetime'] = $time;
                            $data['beinorder_createtime'] = $time;
                            $this->DataControl->insertData("smc_erp_beinorder", $data);
                            $data = array();
                            $data['beinorder_pid'] = $beinorder_pid;
                            $data['goods_id'] = $goodOne['goods_id'];
                            $data['beinordergoods_buynums'] = $goodOne['ordergoods_buynums'];
                            $this->DataControl->insertData("smc_erp_beinorder_goods", $data);
                            $data = array();
                            $data['beinorder_pid'] = $beinorder_pid;
                            $data['tracks_title'] = $this->LgStringSwitch('商品退费入库');
                            $data['tracks_information'] = $this->LgStringSwitch('商品退费入库');
                            $data['staffer_id'] = $this->payfeeorderOne['staffer_id'];

                            $sOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$this->payfeeorderOne['staffer_id']}'");
                            $data['tracks_playname'] = $sOne['staffer_cnname'];
                            $data['tracks_createtime'] = $time;
                            $this->DataControl->insertData("smc_erp_beinorder_tracks", $data);

                            $schoolGood = $this->DataControl->getFieldOne("smc_erp_goods_repertory", "goods_repertory", "company_id='{$this->company_id}' and school_id='{$this->payfeeorderOne['school_id']}' and goods_id='{$goodOne['goods_id']}'");
                            $data = array();
                            $data['company_id'] = $this->company_id;
                            $data['school_id'] = $this->payfeeorderOne['school_id'];
                            $data['goods_id'] = $goodOne['goods_id'];
                            $data['staffer_id'] = $this->payfeeorderOne['staffer_id'];
                            $data['changelog_class'] = 0;
                            $data['changelog_playname'] = $this->LgStringSwitch('商品退费入库');
                            $data['changelog_playclass'] = '+';
                            $data['changelog_fromnums'] = $schoolGood['goods_repertory'];
                            $data['changelog_playnums'] = $goodOne['ordergoods_buynums'];
                            $data['changelog_finalnums'] = $schoolGood['goods_repertory'] + $goodOne['ordergoods_buynums'];
                            $data['changelog_reason'] = $this->LgStringSwitch('商品退费入库');
                            $data['changelog_createtime'] = $time;
                            $this->DataControl->insertData("smc_erp_goods_changelog", $data);

                            $data = array();
                            $data['goods_repertory'] = $schoolGood['goods_repertory'] + $goodOne['ordergoods_buynums'];
                            $this->DataControl->updateData("smc_erp_goods_repertory", "company_id='{$this->company_id}' and school_id='{$this->payfeeorderOne['school_id']}' and goods_id='{$goodOne['goods_id']}'", $data);
                        }
                    }
                }
            }

            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }

    }

    //确认金额
    function SureRefundAction($request, $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        $datas = array();
        $datas['refund_pid'] = $request['refund_pid'];
        $datas['tradelog_outnumber'] = $request['tradelog_outnumber'];
        $datas['tradelog_note'] = $request['tradelog_note'];
        $datas['tradelog_price'] = $this->payfeeorderOne['refund_payprice'];
        $datas['tradelog_successtime'] = $time;
        $this->DataControl->insertData('smc_refund_order_tradelog', $datas);

        if (isset($request['refund_checkpicurl']) && $request['refund_checkpicurl'] != '') {
            $datass = array();
            $datass['refund_checkpicurl'] = $request['refund_checkpicurl'];
            $this->DataControl->updateData("smc_refund_order", "refund_pid = '{$request['refund_pid']}'", $datass);

            if ($this->payfeeorderOne['refund_isspecial'] == 1 && $this->payfeeorderOne['refund_specialprice'] > 0) {
                $datasss = array();
                $datasss['company_id'] = $this->company_id;
                $datasss['school_id'] = $this->payfeeorderOne['school_id'];
                $datasss['companies_id'] = $this->payfeeorderOne['companies_id'];
                $datasss['expend_type'] = '0';
                $datasss['student_id'] = $this->payfeeorderOne['student_id'];
                $datasss['trading_pid'] = $this->payfeeorderOne['trading_pid'];
                $datasss['expend_price'] = $this->payfeeorderOne['refund_specialprice'];
                $datasss['expend_note'] = $this->payfeeorderOne['refund_reason'];
                $datasss['expend_confirmtime'] = $time;
                $datasss['expend_createtime'] = $time;
                $this->DataControl->insertData("smc_school_expend", $datasss);
            } else {
                $this->error = true;
                $this->errortip = "必须上传签呈截图";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "必须上传签呈截图";
            return false;
        }

        $data = array();
        $data['pay_outnumber'] = $datas['tradelog_outnumber'];
        $data['trade_successtime'] = $time;
        $data['trade_updatatime'] = $time;
        if ($this->DataControl->updateData("smc_refund_order_trade", "refund_pid = '{$request['refund_pid']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }

    }

    function refuseCanceldebts($request)
    {

        if ($this->payfeeorderOne['order_status'] < 0 || $this->payfeeorderOne['order_status'] >= 4) {
            $this->error = true;
            $this->errortip = "该订单状态不可坏账处理";
            return false;
        }

        $pay_data = array();
        $pay_data['pay_issuccess'] = '-1';
        $pay_data['pay_updatatime'] = time();
        $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_pid='{$request['pay_pid']}'", $pay_data);

        $track_data = array();
        $track_data['order_pid'] = $this->payfeeorderOne['order_pid'];
        $track_data['tracks_title'] = $this->LgStringSwitch('审核订单');
        $track_data['tracks_information'] = $this->LgStringSwitch('订单申请坏账处理，审核拒绝');
        $track_data['tracks_note'] = $request['reason'];
        $track_data['staffer_id'] = $request['staffer_id'];
        $track_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $track_data['tracks_time'] = time();
        $this->DataControl->insertData("smc_payfee_order_tracks", $track_data);

        $this->error = 0;
        $this->errortip = '已拒绝该申请';
        return true;
    }

    function adoptCanceldebts($request)
    {

        if ($this->payfeeorderOne['order_status'] < 0 || $this->payfeeorderOne['order_status'] >= 4) {
            $this->error = true;
            $this->errortip = "该订单状态不可坏账处理";
            return false;
        }

        $sql = "select ifnull(sum(pop.pay_price),0) as pay_price
              from smc_payfee_order_pay as pop
              where pop.order_pid='{$this->payfeeorderOne['order_pid']}' and pop.pay_type in ('1','2') and pop.pay_issuccess=0";

        $itemOrderOne = $this->DataControl->selectOne($sql);

        if ($this->payfeeorderOne['order_arrearageprice'] < $itemOrderOne['pay_price']) {
            $this->error = true;
            $this->errortip = "订单金额错误";
            return false;
        }

        $payOne = $this->DataControl->getFieldOne("smc_payfee_order_pay", "pay_price", "pay_pid='{$request['pay_pid']}'");
        if (!$payOne) {
            $this->error = true;
            $this->errortip = "不存在该支付记录";
            return false;
        }

//        if (($this->payfeeorderOne['order_arrearageprice'] - $itemOrderOne['pay_price']) < $payOne['pay_price']) {
//            $this->error = true;
//            $this->errortip = "坏账处理金额错误";
//            return false;
//        }

        $orderPayModel = new  \Model\Smc\OrderPayModel($request, $this->payfeeorderOne['order_pid']);

        $bool = $orderPayModel->orderPaylog($request['pay_pid'], '', '', '', 'canceldebts');

        if ($bool) {
            $pay_data = array();
            $pay_data['pay_issuccess'] = '1';
            $pay_data['pay_successtime'] = strtotime($request['create_time']) ? strtotime($request['create_time']) : time();
            $pay_data['pay_updatatime'] = strtotime($request['create_time']) ? strtotime($request['create_time']) : time();
            $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$this->payfeeorderOne['order_pid']}' and pay_pid='{$request['pay_pid']}'", $pay_data);

            $out_data = array();
            $out_data['company_id'] = $this->company_id;
            $out_data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $out_data['school_id'] = $this->payfeeorderOne['school_id'];
            $out_data['expend_type'] = '2';
            $out_data['student_id'] = $this->payfeeorderOne['student_id'];
            $out_data['trading_pid'] = $this->payfeeorderOne['trading_pid'];;
            $out_data['expend_price'] = $payOne['pay_price'];
            $out_data['expend_note'] = $this->LgStringSwitch('坏账核销');
            $out_data['expend_confirmtime'] = strtotime($request['create_time']) ? strtotime($request['create_time']) : time();
            $out_data['expend_createtime'] = strtotime($request['create_time']) ? strtotime($request['create_time']) : time();
            $this->DataControl->insertData("smc_school_expend", $out_data);

            $track_data = array();
            $track_data['order_pid'] = $this->payfeeorderOne['order_pid'];
            $track_data['tracks_title'] = $this->LgStringSwitch('审核订单');
            $track_data['tracks_information'] = $this->LgStringSwitch('订单申请坏账处理，审核通过');
            $track_data['tracks_note'] = $request['reason'];
            $track_data['staffer_id'] = $request['staffer_id'];
            $track_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $track_data['tracks_time'] = time();
            $this->DataControl->insertData("smc_payfee_order_tracks", $track_data);

            $this->error = true;
            $this->errortip = "坏账处理审核成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = $orderPayModel->errortip;
            return false;
        }

    }

    function refuseHourFreeOrder($reason = '', $time = '')
    {
        if ($time == '') {
            $time = time();
        }
        if ($this->payfeeorderOne['order_status'] != '0') {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }
        $data = array();
        $data['order_status'] = -2;
        $data['order_refusereason'] = $reason;
        $data['order_updatatime'] = $time;
        $this->DataControl->updateData("smc_freehour_order", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);

        $data = array();
        $data['trading_status'] = -1;
        $data['trading_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        $TracksData = array();
        $TracksData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('订单审核拒绝，请查看拒绝原因');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_freehour_order_tracks", $TracksData);

        if ($this->payfeeorderOne['refund_tradeclass'] == '1') {

            $sql = "select e.erpgoods_isrestore,oe.goods_id,og.ordergoods_buynums,og.ordercourse_totalprice,oe.erpgoods_id,e.student_id
                          from smc_refund_order_erpgoods as oe
                          left join smc_student_erpgoods as e on e.erpgoods_id=oe.erpgoods_id
                          left join smc_payfee_order_goods as og on og.order_pid=e.order_pid and og.goods_id=e.goods_id
                          where oe.order_pid='{$this->payfeeorderOne['order_pid']}'";

            $goodOne = $this->DataControl->selectOne($sql);
            if ($goodOne['erpgoods_isrestore'] == '1') {
                $data = array();
                $data['erpgoods_isrestore'] = 0;
                $data['erpgoods_isrefund'] = 0;
                $this->DataControl->updateData("smc_student_erpgoods", "erpgoods_id='{$goodOne['erpgoods_id']}'", $data);
            }
        } elseif ($this->payfeeorderOne['refund_tradeclass'] == '2') {
            $sql = "select oi.* from smc_refund_order_itemtimes as oi where oi.refund_pid='{$this->payfeeorderOne['refund_pid']}'";
            $timesOne = $this->DataControl->selectOne($sql);

            $one = $this->DataControl->getFieldOne("smc_student_itemtimes", "itemtimes_number,itemtimes_figure", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$timesOne['course_id']}' and feeitem_id='{$timesOne['feeitem_id']}'");

            $data = array();
            $data['student_id'] = $this->payfeeorderOne['student_id'];
            $data['trading_pid'] = $this->payfeeorderOne['trading_pid'];
            $data['feeitem_id'] = $timesOne['feeitem_id'];
            $data['companies_id'] = $this->payfeeorderOne['companies_id'];
            $data['log_playname'] = $this->LgStringSwitch('杂费退费退还');
            $data['log_playclass'] = '+';
            $data['log_fromamount'] = $one['itemtimes_figure'];
            $data['log_playamount'] = $timesOne['itemtimes_figure'];
            $data['log_finalamount'] = $one['itemtimes_figure'] + $timesOne['itemtimes_figure'];
            $data['log_reason'] = $this->LgStringSwitch('杂费退费退还');
            $data['log_time'] = $time;
            $this->DataControl->insertData("smc_student_itemtimes_log", $data);

            $data = array();
            $data['itemtimes_number'] = $one['itemtimes_number'] + $timesOne['itemtimes_number'];
            $data['itemtimes_figure'] = $one['itemtimes_figure'] + $timesOne['itemtimes_figure'];
            $data['itemtimes_updatatime'] = $time;
            $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$this->payfeeorderOne['student_id']}' and course_id='{$timesOne['course_id']}' and feeitem_id='{$timesOne['feeitem_id']}'", $data);

        }
        return true;
    }

    function adoptHourFreeOrder($reason = '',$from='')
    {
        if ($this->payfeeorderOne['order_status'] != '0') {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }

        $time = time();
        
        if ($this->payfeeorderOne['order_hastimes'] == '0') {

            $sql = "select fo.* from smc_freehour_ordertimes as fo where fo.order_pid='{$this->payfeeorderOne['order_pid']}'";
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无相关数据";
                return false;
            }

            foreach ($list as $val) {
                if ($this->DataControl->getFieldOne("smc_class_hour", "hour_id", "class_id='{$val['class_id']}' and hour_lessontimes='{$val['hour_lessontimes']}' and hour_ischecking='1' and hour_iswarming=0")) {
                    $this->error = true;
                    $this->errortip = "赠送课次存在考勤课次,不可通过审核";
                    return false;
                }

//                $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_day", "class_id='{$val['class_id']}' and hour_lessontimes='{$val['hour_lessontimes']}' and hour_iswarming=0");
//                if ($time > 0) {
//                    if (strtotime($hourOne['hour_day']) < $time) {
//                        $time = strtotime($hourOne['hour_day']);
//                    }
//                } else {
//                    $time = strtotime($hourOne['hour_day']);
//                }

            }

            foreach ($list as $val) {
                $data = array();
                $data['course_id'] = $this->payfeeorderOne['course_id'];
                $data['student_id'] = $this->payfeeorderOne['student_id'];
                $data['school_id'] = $this->payfeeorderOne['school_id'];
                $data['order_pid'] = $this->payfeeorderOne['order_pid'];
                $data['class_id'] = $val['class_id'];
                $data['hour_lessontimes'] = $val['hour_lessontimes'];
                $data['staffer_id'] = $this->payfeeorderOne['staffer_id'];
                $data['coursetimes_createtime'] = $time;

                $this->DataControl->insertData("smc_student_free_coursetimes", $data);

            }

            if (($this->payfeeorderOne['order_alltimes']) > 0) {
                $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
                $BalanceModel->addCourseTimes($this->payfeeorderOne['student_id'], $this->payfeeorderOne['course_id'], $this->payfeeorderOne['class_id'], $this->payfeeorderOne['order_alltimes'], $this->LgStringSwitch('免费课时增加课次'), $this->payfeeorderOne['school_id'], $this->payfeeorderOne['trading_pid'], $time);
            }
        } else {
            $BalanceModel = new \Model\Smc\BalanceModel($this->publicarray);
            $bool = $BalanceModel->addStuCourseTimes($this->payfeeorderOne['student_id'], $this->payfeeorderOne['course_id'], $this->payfeeorderOne['order_alltimes'], $this->payfeeorderOne['school_id'], $this->payfeeorderOne['trading_pid'], $time);
            if (!$bool) {
                $this->error = true;
                $this->errortip = $BalanceModel->errortip;
                return false;
            }
        }

        $TracksData = array();
        $TracksData['order_pid'] = $this->payfeeorderOne['order_pid'];
        $TracksData['tracks_title'] = $this->LgStringSwitch('审核订单');
        $TracksData['tracks_information'] = $this->LgStringSwitch('订单审核通过，订单完成');
        $TracksData['tracks_note'] = $reason;
        $TracksData['staffer_id'] = $this->stafferOne['staffer_id'];
        $TracksData['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $TracksData['tracks_time'] = time();
        $this->DataControl->insertData("smc_freehour_order_tracks", $TracksData);

        $data = array();
        $data['order_status'] = 1;
        $data['order_refusereason'] = $reason;
        $data['order_updatatime'] = $time;
        $this->DataControl->updateData("smc_freehour_order", "order_pid='{$this->payfeeorderOne['order_pid']}'", $data);

        $data = array();
        $data['trading_status'] = 1;
        $data['trading_updatatime'] = $time;
        $this->DataControl->updateData("smc_student_trading", "trading_pid='{$this->payfeeorderOne['trading_pid']}'", $data);

        if ($this->payfeeorderOne['class_id'] > 0 && !$this->DataControl->getFieldOne("smc_student_study", "study_id", "class_id='{$this->payfeeorderOne['class_id']}' and student_id='{$this->payfeeorderOne['student_id']}' and study_isreading='1'")) {

            $enterclassdate='';
            
            if($from==1){

                $sql = "SELECT b.hour_day 
                        from smc_freehour_ordertimes as a
                        inner join smc_class_hour as b on b.class_id=a.class_id and a.hour_lessontimes=b.hour_lessontimes 
                        where a.order_pid='{$this->payfeeorderOne['order_pid']}' and b.hour_isfree=0 and b.hour_ischecking<>-1
                        ";

                $hourOne=$this->DataControl->selectOne($sql);
                if($hourOne){
                    $enterclassdate=$hourOne['hour_day'];
                }
            
            }


            $this->publicarray['school_id'] = $this->payfeeorderOne['school_id'];
            $Model = new \Model\Smc\TransactionModel($this->publicarray);
            $Model->entryClass($this->payfeeorderOne['student_id'], $this->payfeeorderOne['course_id'], $this->payfeeorderOne['class_id'], $enterclassdate, 0, $time, '', $reason, 0, $this->payfeeorderOne['staffer_id']);
        }


        return true;
    }

    function getDealOrderInfo()
    {

        $sql = "select fd.dealorder_pid,sch.school_cnname,sch.school_branch,s.student_cnname,s.student_enname,s.student_branch,fd.dealorder_type,fd.dealorder_status,st.staffer_cnname,st.staffer_enname,FROM_UNIXTIME(fd.dealorder_createtime, '%Y-%m-%d %H:%i:%s') as dealorder_createtime
              from smc_forward_dealorder as fd
              left join smc_student as s on s.student_id=fd.student_id
              left join smc_school as sch on sch.school_id=fd.school_id
              left join smc_staffer as st on st.staffer_id=fd.staffer_id
              where fd.dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}' and fd.company_id='{$this->company_id}'
              ";
        $dealOrderOne = $this->DataControl->selectOne($sql);

        $dealOrderOne['staffer_cnname'] = $dealOrderOne['staffer_enname'] ? $dealOrderOne['staffer_cnname'] . '-' . $dealOrderOne['staffer_enname'] : $dealOrderOne['staffer_cnname'];

        $type = $this->LgArraySwitch(array('0' => '结转余额', '1' => '结转课次'));
        $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '已拒绝'));

        $dealOrderOne['dealorder_type_name'] = $type[$dealOrderOne['dealorder_type']];
        $dealOrderOne['dealorder_status_name'] = $status[$dealOrderOne['dealorder_status']];

        $sql = "select dt.tracks_id,dt.tracks_title,dt.tracks_information,dt.tracks_playname,FROM_UNIXTIME(dt.tracks_time, '%Y-%m-%d %H:%i:%s') as tracks_time,st.staffer_cnname,st.staffer_enname
              from smc_forward_dealorder_tracks as dt
              left join smc_staffer as st on st.staffer_id=dt.staffer_id
              where dt.dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}'";

        $trackList = $this->DataControl->selectClear($sql);
        if (!$trackList) {
            $trackList = array();
        } else {
            foreach ($trackList as &$trackOne) {
                $trackOne['tracks_playname'] = $trackOne['staffer_enname'] ? $trackOne['staffer_cnname'] . '-' . $trackOne['staffer_enname'] : $trackOne['staffer_cnname'];
            }
        }

        $handleData = array();
        $handleData['school_cnname'] = $dealOrderOne['school_cnname'];
        $handleData['staffer_cnname'] = $dealOrderOne['staffer_cnname'];
        $handleData['dealorder_createtime'] = $dealOrderOne['dealorder_createtime'];

        $sql = "select sc.course_id,sc.course_cnname,sc.course_branch,dc.*
              from smc_forward_dealorder_course as dc
              left join smc_course as sc on sc.course_id=dc.course_id
              where dc.dealorder_pid='{$this->payfeeorderOne['dealorder_pid']}'
              ";
        $courseOne = $this->DataControl->selectOne($sql);

        $sql = "select sc.course_id,sc.course_cnname,sc.course_branch
              from smc_fee_pricing as fp
              left join smc_course as sc on sc.course_id=fp.course_id
              where fp.pricing_id='{$courseOne['dealcourse_topricing_id']}'
              ";

        $to_coruseOne = $this->DataControl->selectOne($sql);

        $courseData = array();
        $data = array();
        $data['from_course_cnname'] = $courseOne['course_cnname'];
        $data['from_course_branch'] = $courseOne['course_branch'];
        $data['to_course_cnname'] = $to_coruseOne['course_cnname'];
        $data['to_course_branch'] = $to_coruseOne['course_branch'];
        $data['dealcourse_figure'] = $courseOne['dealcourse_figure'];
        $data['dealcourse_time'] = $courseOne['dealcourse_time'];
        $data['dealcourse_fromforwardprice'] = $courseOne['dealcourse_fromforwardprice'];

        $courseData[] = $data;

//        $data=array();
//        $data['course_cnname']=$to_coruseOne['course_cnname'];
//        $data['course_branch']=$to_coruseOne['course_branch'];
//        $data['dealcourse_figure']=$courseOne['dealcourse_figure'];
//        $data['dealcourse_time']=$courseOne['dealcourse_time'];
//        $data['dealcourse_fromforwardprice']=$courseOne['dealcourse_fromforwardprice'];
//
//        $courseData[]=$data;

        $track_field = array();
        $k = 0;
        $track_field[$k]["fieldname"] = 'tracks_id';
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch('序号');
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_title';
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch('标签');
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_information';
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch('信息');
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_playname';
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch('操作人');
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_time';
        $track_field[$k]["fieldstring"] = $this->LgStringSwitch('时间');
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;

        $handle_field = array();
        $k = 0;
        $handle_field[$k]["fieldname"] = 'school_cnname';
        $handle_field[$k]["fieldstring"] = $this->LgStringSwitch('经办分校');
        $handle_field[$k]["custom"] = 1;
        $handle_field[$k]["show"] = 1;
        $k++;

        $handle_field[$k]["fieldname"] = 'staffer_cnname';
        $handle_field[$k]["fieldstring"] = $this->LgStringSwitch('经办人');
        $handle_field[$k]["custom"] = 1;
        $handle_field[$k]["show"] = 1;
        $k++;

        $handle_field[$k]["fieldname"] = 'dealorder_createtime';
        $handle_field[$k]["fieldstring"] = $this->LgStringSwitch('经办时间');
        $handle_field[$k]["custom"] = 1;
        $handle_field[$k]["show"] = 1;

        $course_field = array();
        $k = 0;
        $course_field[$k]["fieldname"] = 'from_course_cnname';
        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('来源课程别名称');
        $course_field[$k]["custom"] = 1;
        $course_field[$k]["show"] = 1;
        $k++;

        $course_field[$k]["fieldname"] = 'from_course_branch';
        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('来源课程别编号');
        $course_field[$k]["custom"] = 1;
        $course_field[$k]["show"] = 1;
        $k++;

        $course_field[$k]["fieldname"] = 'to_course_cnname';
        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('结转课程别名称');
        $course_field[$k]["custom"] = 1;
        $course_field[$k]["show"] = 1;
        $k++;

        $course_field[$k]["fieldname"] = 'to_course_branch';
        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('结转课程别编号');
        $course_field[$k]["custom"] = 1;
        $course_field[$k]["show"] = 1;
        $k++;

        $course_field[$k]["fieldname"] = 'dealcourse_figure';
        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('课程结转金额');
        $course_field[$k]["custom"] = 1;
        $course_field[$k]["show"] = 1;
        $k++;

//        $course_field[$k]["fieldname"] = 'dealcourse_fromforwardprice';
//        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('课程结转金额');
//        $course_field[$k]["custom"] = 1;
//        $course_field[$k]["show"] = 1;
//        $k++;

        $course_field[$k]["fieldname"] = 'dealcourse_time';
        $course_field[$k]["fieldstring"] = $this->LgStringSwitch('课程结转课次');
        $course_field[$k]["custom"] = 1;
        $course_field[$k]["show"] = 1;
        $k++;

        $data = array();
        $data['info'] = $dealOrderOne;
        $data['trackList'] = $trackList;
        $data['track_field'] = $track_field;
        $data['handleData'][] = $handleData;
        $data['handle_field'] = $handle_field;
        $data['courseData'] = $courseData;
        $data['course_field'] = $course_field;

        return $data;
    }

    function getClockorderOrderInfo()
    {

        $sql = "select fd.clockorder_pid,sch.school_cnname,sch.school_branch,s.student_cnname,s.student_id,s.student_enname,s.student_branch,fd.clockorder_status,fd.clockorder_class,st.staffer_cnname,st.staffer_enname,FROM_UNIXTIME(fd.clockorder_createtime, '%Y-%m-%d %H:%i:%s') as clockorder_createtime,fd.class_branch,c.class_cnname,c.class_enname,sc.course_cnname,sc.course_branch,fd.clockorder_reason
              from smc_student_clockorder as fd
              left join smc_student as s on s.student_branch=fd.student_branch
              left join smc_school as sch on sch.school_id=fd.school_id
              left join smc_staffer as st on st.staffer_id=fd.staffer_id
              left join smc_class as c on c.class_branch=fd.class_branch
              left join smc_course as sc on sc.course_id=c.course_id
              where fd.clockorder_pid='{$this->payfeeorderOne['clockorder_pid']}' and fd.company_id='{$this->company_id}'
              ";
        $clockorderOne = $this->DataControl->selectOne($sql);
//        var_dump($sql);exit;

        $class = $this->LgArraySwitch(array('0' => '回原班免扣课次申请'));
        $status = $this->LgArraySwitch(array('0' => '未使用', '1' => '已使用'));

        $clockorderOne['clockorder_class_name'] = $class[$clockorderOne['clockorder_class']];
        $clockorderOne['clockorder_status_name'] = $status[$clockorderOne['clockorder_status']];


        $handleData = array();
        $handleData['school_cnname'] = $clockorderOne['school_cnname'];
        $handleData['staffer_cnname'] = $clockorderOne['staffer_enname'] ? $clockorderOne['staffer_cnname'] . '-' . $clockorderOne['staffer_enname'] : $clockorderOne['staffer_cnname'];
        $handleData['clockorder_createtime'] = $clockorderOne['clockorder_createtime'];

        $handle_field = array();
        $k = 0;
        $handle_field[$k]["fieldname"] = 'school_cnname';
        $handle_field[$k]["fieldstring"] = '经办分校';
        $handle_field[$k]["custom"] = 1;
        $handle_field[$k]["show"] = 1;
        $k++;

        $handle_field[$k]["fieldname"] = 'staffer_cnname';
        $handle_field[$k]["fieldstring"] = '经办人';
        $handle_field[$k]["custom"] = 1;
        $handle_field[$k]["show"] = 1;
        $k++;

        $handle_field[$k]["fieldname"] = 'clockorder_createtime';
        $handle_field[$k]["fieldstring"] = '经办时间';
        $handle_field[$k]["custom"] = 1;
        $handle_field[$k]["show"] = 1;


        $data = array();
        $data['info'] = $clockorderOne;
        $data['handleData'][] = $handleData;
        $data['handle_field'] = $handle_field;

        return $data;
    }

}
