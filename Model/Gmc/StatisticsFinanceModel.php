<?php

/**
 * 数据库查询优化说明：
 * 
 * 1. FROM_UNIXTIME函数优化：
 *    - 原问题：在WHERE条件中使用FROM_UNIXTIME会导致MySQL无法使用索引
 *    - 优化策略：将时间字符串转换为Unix时间戳进行比较
 *    - 例如：FROM_UNIXTIME(si.income_confirmtime,'%Y-%m')>='{$start}' 
 *           优化为：si.income_confirmtime>=".strtotime($start."-01")." 
 * 
 * 2. 建议创建以下索引提升查询性能：
 *    CREATE INDEX idx_income_confirmtime ON smc_school_income(company_id, income_confirmtime);
 *    CREATE INDEX idx_income_school_type ON smc_school_income(school_id, income_type, income_confirmtime);
 *    CREATE INDEX idx_expend_confirmtime ON smc_school_expend(company_id, expend_confirmtime);
 *    CREATE INDEX idx_expend_school_type ON smc_school_expend(school_id, expend_type, expend_confirmtime);
 *    CREATE INDEX idx_pay_successtime ON smc_payfee_order_pay(pay_issuccess, pay_successtime);
 *    CREATE INDEX idx_refund_time_status ON smc_refund_order(refund_status, refund_updatatime);
 * 
 * 3. GROUP BY优化：
 *    - 使用别名替代函数调用：GROUP BY income_createday 替代 GROUP BY FROM_UNIXTIME(...)
 *    - 这样可以避免重复计算，提升查询效率
 * 
 * 4. 建议启用MySQL查询缓存（如果适用）：
 *    SET GLOBAL query_cache_size = *********;
 *    SET GLOBAL query_cache_type = ON;
 */

namespace Model\Gmc;

class StatisticsFinanceModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function headInfo($request){

        if($request['type']==0){
            //本日
            $oldStartDay=date("Y-m-d",strtotime("-1 day"));
            $oldEndDay=date("Y-m-d",strtotime("-1 day"));
        }elseif($request['type']==1){
            //昨日
            $oldStartDay=date("Y-m-d",strtotime("-2 day"));
            $oldEndDay=date("Y-m-d",strtotime("-2 day"));
        }elseif($request['type']==2){
            //按周
            $WeekAll = GetWeekAll(date("Y-m-d",strtotime("-7 day")));
            $oldStartDay=$WeekAll['nowweek_start'];
            $oldEndDay=date("Y-m-d",strtotime($oldStartDay."+6 day"));

        }elseif($request['type']==3){
            //按上周
            $WeekAll = GetWeekAll(date("Y-m-d",strtotime("-14 day")));
            $oldStartDay=$WeekAll['nowweek_start'];
            $oldEndDay=date("Y-m-d",strtotime($oldStartDay."+6 day"));

        }elseif($request['type']==4){
            //本月
            $oldStartDay=date('Y-m-01',strtotime('-1 month'));
            $oldEndDay=date('Y-m-d',strtotime(date('Y-m-01').'-1 day'));
        }elseif($request['type']==5){
            //上月
            $oldStartDay=date('Y-m-01',strtotime('-2 month'));
            $oldEndDay=date('Y-m-d',strtotime(date('Y-m-01',strtotime('-1 month')).'-1 day'));

        }elseif($request['type']==6){
            //本季度
            $season = ceil((date('n'))/3)-1;
            $oldStartDay=date('Y-m-d', mktime(0, 0, 0,$season*3-3+1,1,date('Y')));
            $oldEndDay=date('Y-m-d',strtotime($oldStartDay.'+3 month -1day'));
        }elseif($request['type']==7){
            //上季度
            $season = ceil((date('n'))/3)-2;
            $oldStartDay=date('Y-m-d', mktime(0, 0, 0,$season*3-3+1,1,date('Y')));
            $oldEndDay=date('Y-m-d',strtotime($oldStartDay.'+3 month -1day'));
        }elseif($request['type']==8){
            //本年
            $oldStartDay=date("Y-01-01",strtotime("-1 year"));
            $oldEndDay=date('Y-m-d',strtotime(date('Y-1-1').'-1 day'));

        }elseif($request['type']==9){
            //去年
            $oldStartDay=date("Y-01-01",strtotime("-2 year"));
            $oldEndDay=date('Y-m-d',strtotime(date('Y-01-01',strtotime('-1 year')).'-1 day'));

        }elseif($request['type']==10){
            //按时间

        }else{
            $this->error = true;
            $this->errortip = "该模式不存在";
            return false;
        }
        $oldstarttime=strtotime($oldStartDay);
        $oldendtime=strtotime($oldEndDay." 23:59:59");

        $datawhere=" 1 ";

        $datawhere .= " and si.company_id='{$this->company_id}'";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }
        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        $sql="select SUM(si.income_price) as income_price
              from smc_school_income as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'";
        $newIncomeOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(si.income_price) as income_price
              from smc_school_income as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and income_confirmtime>='{$oldstarttime}' and income_confirmtime<='{$oldendtime}'";
        $oldIncomeOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(si.expend_price) as expend_price
              from smc_school_expend as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}'";
        $newExpendOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(si.expend_price) as expend_price
              from smc_school_expend as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.expend_confirmtime>='{$oldstarttime}' and si.expend_confirmtime<='{$oldendtime}'";
        $oldExpendOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(pop.pay_price) as pay_price
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as si on si.order_pid=pop.order_pid
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and pop.pay_issuccess='1' and pop.paytype_code in (select cp.paytype_code from smc_code_paytype as cp where cp.paytype_ischarge='1') and pop.pay_successtime>='{$starttime}' and pop.pay_successtime<='{$endtime}'";
        $newPayOne=$this->DataControl->selectOne($sql);

        $sql="select SUM(pay_price) as pay_price
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as si on si.order_pid=pop.order_pid
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and pop.pay_issuccess='1' and pop.paytype_code in (select cp.paytype_code from smc_code_paytype as cp where cp.paytype_ischarge='1') and pop.pay_successtime>='{$oldstarttime}' and pop.pay_successtime<='{$oldendtime}'";
        $oldPayOne=$this->DataControl->selectOne($sql);

        $sql="select sum(si.refund_payprice) as refund_payprice
              from smc_refund_order as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.refund_status='4' and si.refund_updatatime>='{$starttime}' and si.refund_updatatime<='{$endtime}'";
        $newRefundOne=$this->DataControl->selectOne($sql);

        $sql="select sum(si.refund_payprice) as refund_payprice
              from smc_refund_order as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.refund_status='4' and si.refund_updatatime>='{$oldstarttime}' and si.refund_updatatime<='{$oldendtime}'";
        $oldRefundOne=$this->DataControl->selectOne($sql);

        $data=array();
        $data['newIncome']=sprintf("%.2f",$newIncomeOne['income_price'])?sprintf("%.2f",$newIncomeOne['income_price']):0.00;
        $data['oldIncome']=sprintf("%.2f",$oldIncomeOne['income_price'])?sprintf("%.2f",$oldIncomeOne['income_price']):0.00;
        if($data['oldIncome']<=0){
            $data['incomeDif']=0;
        }else{
            $data['incomeDif']=round(($data['newIncome']-$data['oldIncome'])/$data['oldIncome']*100,0);
        }

        $data['newExpend']=sprintf("%.2f",$newExpendOne['expend_price'])?sprintf("%.2f",$newExpendOne['expend_price']):0.00;
        $data['oldExpend']=sprintf("%.2f",$oldExpendOne['expend_price'])?sprintf("%.2f",$oldExpendOne['expend_price']):0.00;
        if($data['oldExpend']<=0){
            $data['expendDif']=0;
        }else{
            $data['expendDif']=round(($data['newExpend']-$data['oldExpend'])/$data['oldExpend']*100,0);
        }

        $data['newPay']=sprintf("%.2f",$newPayOne['pay_price'])?sprintf("%.2f",$newPayOne['pay_price']):0.00;
        $data['oldPay']=sprintf("%.2f",$oldPayOne['pay_price'])?sprintf("%.2f",$oldPayOne['pay_price']):0.00;
        if($data['oldPay']<=0){
            $data['payDif']=0;
        }else{
            $data['payDif']=round(($data['newPay']-$data['oldPay'])/$data['oldPay']*100,0);
        }

        $data['newRefund']=sprintf("%.2f",$newRefundOne['refund_payprice'])?sprintf("%.2f",$newRefundOne['refund_payprice']):0.00;
        $data['oldRefund']=sprintf("%.2f",$oldRefundOne['refund_payprice'])?sprintf("%.2f",$oldRefundOne['refund_payprice']):0.00;
        if($data['oldRefund']<=0){
            $data['refundDif']=0;
        }else{
            $data['refundDif']=round(($data['newRefund']-$data['oldRefund'])/$data['oldRefund']*100,0);
        }

        return $data;

    }

    function schoolBudget($request){
        $datawhere=" 1 ";

        $datawhere .= " and sc.school_id>'0'";
        $datawhere .= " and si.company_id='{$this->company_id}'";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }


        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $time1 = $starttime;
        $time2 = $endtime;
        $dayArr = array();

        if($request['type']==0){
            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $dayArr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }
            $type='%Y-%m-%d';
        }elseif($request['type']==1){
            while( date("Y-m",$time1) <= date("Y-m",$time2)){
                $dayArr[] = date('Y-m',$time1);
                $time1=strtotime("+1 month",($time1));
            }
            $type='%Y-%m';
        }else{
            $this->error = true;
            $this->errortip = "无此模式";
            return false;
        }

        $sql="select SUM(si.income_price) as income_price,FROM_UNIXTIME(si.income_confirmtime, '{$type}') as income_createday
                  from smc_school_income as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'
                  group by income_createday
                  order by income_createday asc
              ";
        $newIncomeList=$this->DataControl->selectClear($sql);

        $sql="select SUM(si.expend_price) as expend_price,FROM_UNIXTIME(si.expend_confirmtime, '{$type}') as expend_createday
              from smc_school_expend as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}'
              group by expend_createday
              order by expend_createday asc
              ";
        $newExpendList=$this->DataControl->selectClear($sql);

        $sql="select SUM(pop.pay_price) as pay_price,FROM_UNIXTIME(pop.pay_successtime, '{$type}') as pay_successday
              from smc_payfee_order_pay as pop
              left join smc_payfee_order as si on si.order_pid=pop.order_pid
              left join smc_school as sc on sc.school_id=si.school_id
              left join smc_code_paytype as cp on cp.paytype_code=pop.paytype_code
              where {$datawhere} and pop.pay_issuccess='1' and cp.paytype_ischarge='1' and pop.pay_successtime>='{$starttime}' and pop.pay_successtime<='{$endtime}'
              group by pay_successday
              order by pay_successday asc
              ";
        $newPayList=$this->DataControl->selectClear($sql);

        $sql="select sum(si.refund_payprice) as refund_payprice,FROM_UNIXTIME(si.refund_updatatime, '{$type}') as refund_updataday
                  from smc_refund_order as si
                  left join smc_school as sc on sc.school_id=si.school_id
                  where {$datawhere} and refund_status='4' and si.refund_updatatime>='{$starttime}' and si.refund_updatatime<='{$endtime}'
                  group by refund_updataday
                  order by refund_updataday asc
                  ";
        $newRefundList=$this->DataControl->selectClear($sql);

        $tem_newIncome=array();
        if($newIncomeList){
            foreach($dayArr as $dayOne){
                foreach($newIncomeList as $one){
                    if($one['income_createday']==$dayOne){
                        $tem_newIncome[$dayOne]=sprintf("%.2f",$one['income_price']/10000);
                    }else{
                        if(!$tem_newIncome[$dayOne]){
                            $tem_newIncome[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newIncome=array_values($tem_newIncome);
        }else{
            foreach ($dayArr as $dayOne) {
                $tem_newIncome[]='0.00';
            }
        }

        $tem_newExpend=array();
        if($newExpendList){
            foreach($dayArr as $dayOne){
                foreach($newExpendList as $one){
                    if($one['expend_createday']==$dayOne){
                        $tem_newExpend[$dayOne]=sprintf("%.2f",$one['expend_price']/10000);
                    }else{
                        if(!$tem_newExpend[$dayOne]){
                            $tem_newExpend[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newExpend=array_values($tem_newExpend);
        }else{
            foreach ($dayArr as $dayOne) {
                $tem_newExpend[]='0.00';
            }
        }

        $tem_newPay=array();
        if($newPayList){
            foreach($dayArr as $dayOne){
                foreach($newPayList as $one){
                    if($one['pay_successday']==$dayOne){
                        $tem_newPay[$dayOne]=sprintf("%.2f",$one['pay_price']/10000);
                    }else{
                        if(!$tem_newPay[$dayOne]){
                            $tem_newPay[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newPay=array_values($tem_newPay);
        }else{
            foreach ($dayArr as $dayOne) {
                $tem_newPay[]='0.00';
            }
        }

        $tem_newRefund=array();
        if($newRefundList){
            foreach($dayArr as $dayOne){
                foreach($newRefundList as $one){
                    if($one['refund_updataday']==$dayOne){
                        $tem_newRefund[$dayOne]=sprintf("%.2f",$one['refund_payprice']/10000);
                    }else{
                        if(!$tem_newRefund[$dayOne]){
                            $tem_newRefund[$dayOne]=sprintf("%.2f",0.00);
                        }
                    }
                }
            }
            $tem_newRefund=array_values($tem_newRefund);
        }else{
            foreach ($dayArr as $dayOne) {
                $tem_newRefund[]='0.00';
            }
        }

        $data=array();
        $k=0;
        $data[$k]['name']=$this->LgStringSwitch('总收入');
        $data[$k]['data']=$tem_newIncome;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('总支出');
        $data[$k]['data']=$tem_newExpend;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('总收费');
        $data[$k]['data']=$tem_newPay;
        $k++;

        $data[$k]['name']=$this->LgStringSwitch('总退费');
        $data[$k]['data']=$tem_newRefund;
        $k++;

        $legendData=$this->LgArraySwitch(array("总收入","总支出","总收费","总退费","累计总欠费"));

        $tem_data=array();
        $tem_data['allList']=$data;
        $tem_data['legendData']=$legendData;
        $tem_data['xAxisData']=$dayArr;
        return $tem_data;
    }

    function incomeConstitute($request)
    {
        $dataArray = array();

        $datawhere=" 1 ";

        $datawhere .= " and sc.school_id>'0'";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }


        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $startDay = $request['starttime'];
        } else {
            $startDay = date("Y-m-01");
        }

        if (isset($request['endtime']) && $request['endtime']) {
            $endDay = $request['endtime'];
        } else {
            $endDay = date("Y-m-d");
        }
        $starttime = strtotime($startDay);
        $endtime = strtotime($endDay . " 23:59:59");

        $time1 = $starttime;
        $time2 = $endtime;
        $dayArr = array();

        if($request['type']==0){
            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $dayArr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select sum(si.income_price) as income_price
                    from smc_school_income as si
                    left join smc_school as sc on sc.school_id=si.school_id
                    where {$datawhere} and si.company_id='{$this->company_id}' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and si.income_type>='0'";
            $incomeOne = $this->DataControl->selectOne($sql);
            if ($incomeOne) {
                $allIncome = $incomeOne['income_price'];
            } else {
                $allIncome = 0;
            }

            $sql = "select sum(si.income_price) as income_price
                    from smc_school_income as si
                    left join smc_school as sc on sc.school_id=si.school_id
                    where {$datawhere} and si.company_id='{$this->company_id}' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'";

        }elseif($request['type']==1){
            while( date("Y-m",$time1) <= date("Y-m",$time2)){
                $dayArr[] = date('Y-m',$time1);
                $time1=strtotime("+1 month",($time1));
            }
            $start1=date("Y-m",$starttime);
            $end1=date("Y-m",$endtime);

            $sql = "select sum(si.income_price) as income_price
                    from smc_school_income as si
                    left join smc_school as sc on sc.school_id=si.school_id
                    where {$datawhere} and si.company_id='{$this->company_id}' and si.income_confirmtime>=".strtotime($start1."-01")." and si.income_confirmtime<=".strtotime($end1."-01 +1 month -1 second")." and si.income_type>='0'";
            $incomeOne = $this->DataControl->selectOne($sql);
            if ($incomeOne) {
                $allIncome = $incomeOne['income_price'];
            } else {
                $allIncome = 0;
            }

            $sql = "select sum(si.income_price) as income_price
                    from smc_school_income as si
                    left join smc_school as sc on sc.school_id=si.school_id
                    where {$datawhere} and si.company_id='{$this->company_id}' and si.income_confirmtime>=".strtotime($start1."-01")." and si.income_confirmtime<=".strtotime($end1."-01 +1 month -1 second")."";
        }else{
            $this->error = true;
            $this->errortip = "无此模式";
            return false;
        }


        $courseIncome = $this->DataControl->selectOne($sql . " and si.income_type='0'");
        $materialIncome = $this->DataControl->selectOne($sql . " and si.income_type='2'");
        $itemIncome = $this->DataControl->selectOne($sql . " and si.income_type='3'");
        $confiscateIncome = $this->DataControl->selectOne($sql . " and si.income_type='1'");

        $allRate = 100;

        $data = array();
        if ($allIncome) {
            $data['courseRate'] = sprintf("%.2f", ($courseIncome['income_price'] ? $courseIncome['income_price'] : 0) / $allIncome) * 100;
            $data['courseIncome'] = $courseIncome['income_price'] ? $courseIncome['income_price'] : 0;;
            $allRate -= $data['courseRate'];

            $data['materialRate'] = sprintf("%.2f", ($materialIncome['income_price'] ? $materialIncome['income_price'] : 0) / $allIncome) * 100;
            $data['materialIncome'] = $materialIncome['income_price'] ? $materialIncome['income_price'] : 0;;
            $allRate -= $data['materialRate'];

            $data['itemRate'] = sprintf("%.2f", ($itemIncome['income_price'] ? $itemIncome['income_price'] : 0) / $allIncome) * 100;
            $data['itemIncome'] = $itemIncome['income_price'] ? $itemIncome['income_price'] : 0;;
            $allRate -= $data['itemRate'];

            $data['confiscateRate'] = $allRate;
            $data['confiscateIncome'] = $confiscateIncome['income_price'] ? $confiscateIncome['income_price'] : 0;


        } else {
            $data['courseIncome'] = 0;
            $data['courseRate'] = 0;

            $data['materialRate'] = 0;
            $data['materialIncome'] = 0;

            $data['itemRate'] = 0;
            $data['itemIncome'] = 0;

            $data['confiscateRate'] = 0;
            $data['confiscateIncome'] = 0;

        }

        $dataArray['incomeConstitute'] = $data;

        if($request['type']==0) {
            while (date("Y-m-d", $time1) <= date("Y-m-d", $time2)) {
                $dayArr[] = date('Y-m-d', $time1);
                $time1 = strtotime("+1 day", $time1);
            }

            $sql = "select sum(si.income_price) as income_price,FROM_UNIXTIME(si.income_confirmtime,'%Y-%m-%d') AS income_createday
                    from smc_school_income as si
                    left join smc_school as sc on sc.school_id=si.school_id
                    where {$datawhere} and si.company_id='{$this->company_id}' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}'";
        }elseif($request['type']==1) {
            $start2=date("Y-m",$starttime);
            $end2=date("Y-m",$endtime);

            while (date("Y-m", $time1) <= date("Y-m", $time2)) {
                $dayArr[] = date('Y-m', $time1);
                $time1 = strtotime("+1 month", ($time1));
            }

            $sql = "select sum(si.income_price) as income_price,FROM_UNIXTIME(si.income_confirmtime,'%Y-%m') AS income_createday
                    from smc_school_income as si
                    left join smc_school as sc on sc.school_id=si.school_id
                    where {$datawhere} and si.company_id='{$this->company_id}' and si.income_confirmtime>=".strtotime($start2."-01")." and si.income_confirmtime<=".strtotime($end2."-01 +1 month -1 second")."";
        }else{
            $this->error = true;
            $this->errortip = "无此模式";
            return false;
        }

        $group = " group by income_createday order by income_createday asc";

        $courseIncomeList = $this->DataControl->selectClear($sql . " and si.income_type='0'" . $group);
        $materialIncomeList = $this->DataControl->selectClear($sql . " and si.income_type='2'" . $group);
        $itemIncomeList = $this->DataControl->selectClear($sql . " and si.income_type='3'" . $group);
        $confiscateIncomeList = $this->DataControl->selectClear($sql . " and si.income_type='1'" . $group);

        $tem_courseIncome = array();
        if ($courseIncomeList) {
            foreach ($dayArr as $dayOne) {
                foreach ($courseIncomeList as $one) {
                    if ($one['income_createday'] == $dayOne) {
                        $tem_courseIncome[$dayOne] = sprintf("%.2f", $one['income_price'] / 10000);
                    } else {
                        if (!$tem_courseIncome[$dayOne]) {
                            $tem_courseIncome[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_courseIncome = array_values($tem_courseIncome);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_courseIncome[]='0.00';
            }
        }

        $tem_materialIncome = array();
        if ($materialIncomeList) {
            foreach ($dayArr as $dayOne) {
                foreach ($materialIncomeList as $one) {
                    if ($one['income_createday'] == $dayOne) {
                        $tem_materialIncome[$dayOne] = sprintf("%.2f", $one['income_price'] / 10000);
                    } else {
                        if (!$tem_materialIncome[$dayOne]) {
                            $tem_materialIncome[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_materialIncome = array_values($tem_materialIncome);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_materialIncome[]='0.00';
            }
        }

        $tem_itemIncome = array();
        if ($itemIncomeList) {
            foreach ($dayArr as $dayOne) {
                foreach ($itemIncomeList as $one) {
                    if ($one['income_createday'] == $dayOne) {
                        $tem_itemIncome[$dayOne] = sprintf("%.2f", $one['income_price'] / 10000);
                    } else {
                        if (!$tem_itemIncome[$dayOne]) {
                            $tem_itemIncome[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_itemIncome = array_values($tem_itemIncome);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_itemIncome[]='0.00';
            }
        }

        $tem_confiscateIncome = array();
        if ($confiscateIncomeList) {
            foreach ($dayArr as $dayOne) {
                foreach ($confiscateIncomeList as $one) {
                    if ($one['income_createday'] == $dayOne) {
                        $tem_confiscateIncome[$dayOne] = sprintf("%.2f", $one['income_price'] / 10000);
                    } else {
                        if (!$tem_confiscateIncome[$dayOne]) {
                            $tem_confiscateIncome[$dayOne] = sprintf("%.2f", 0.00);
                        }
                    }
                }
            }
            $tem_confiscateIncome = array_values($tem_confiscateIncome);
        } else {
            foreach ($dayArr as $dayOne) {
                $tem_confiscateIncome[]='0.00';
            }
        }


        $data = array();
        $k = 0;
        $data[$k]['name'] = $this->LgStringSwitch('课程收入');
        $data[$k]['data'] = $tem_courseIncome;
        $k++;

        $data[$k]['name'] = $this->LgStringSwitch('教材收入');
        $data[$k]['data'] = $tem_materialIncome;
        $k++;

        $data[$k]['name'] = $this->LgStringSwitch('杂费收入');
        $data[$k]['data'] = $tem_itemIncome;
        $k++;

        $data[$k]['name'] = $this->LgStringSwitch('认缴收入');
        $data[$k]['data'] = $tem_confiscateIncome;
        $k++;

        $legendData = $this->LgArraySwitch(array("课程收入", "教材收入", "杂费收入", "认缴收入"));

        $tem_data = array();
        $tem_data['allList'] = $data;
        $tem_data['legendData'] = $legendData;
        $tem_data['xAxisData'] = $dayArr;

        $dataArray['incomeTrend'] = $tem_data;

        return $dataArray;
    }


    function incomeProportion($request){

        $datawhere=" 1 ";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }
        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        $sql="select sum(si.income_price) as income_price,si.income_type
              from smc_school_income as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.company_id='{$request['company_id']}' and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and si.income_type<>'-1'
              group by si.income_type";

        $incomeList=$this->DataControl->selectClear($sql);

        $typeArray=$this->LgArraySwitch(array("课程收入", "认缴收入", "教材收入", "杂费收入"));

        $tem_array=array();
        if($incomeList){
            foreach($incomeList as $incomeOne){
                foreach($typeArray as $key=>$one){
                    if($incomeOne['income_type']==$key){
                        $data=array();
                        $data['value']=$incomeOne['income_price']?$incomeOne['income_price']:0;
                        $data['name']=$one;
                        $tem_array[]=$data;
                    }
                }
            }

        }else{
            foreach($typeArray as $key=>$one){
                $data=array();
                $data['value']=0;
                $data['name']=$one;
                $tem_array[]=$data;
            }
        }


        $data=array();

        $data['allList']=$tem_array;
        $data['legendData']=$typeArray;

        return $data;
    }

    function expendProportion($request){
        $datawhere=" 1 ";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }
        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        $sql="select sum(si.expend_price) as expend_price,si.expend_type
              from smc_school_expend as si
              inner join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}' and si.company_id='{$this->company_id}'
              group by si.expend_type";

        $expendList=$this->DataControl->selectClear($sql);

        $typeArray=$this->LgArraySwitch(array("签呈报损", "认缴支出", "2" => "坏账核销"));

        $tem_array=array();
        if($expendList){
            foreach($expendList as $expendOne){
                foreach($typeArray as $key=>$one){
                    if($expendOne['expend_type']==$key){
                        $data=array();
                        $data['value']=$expendOne['expend_price']?$expendOne['expend_price']:0;
                        $data['name']=$one;
                        $tem_array[]=$data;
                    }
                }
            }

        }else{
            foreach($typeArray as $key=>$one){
                $data=array();
                $data['value']=0;
                $data['name']=$one;
                $tem_array[]=$data;
            }
        }


        $data=array();

        $data['allList']=$tem_array;
        $data['legendData']=$typeArray;

        return $data;
    }

    function incomeRanking($request){
        $datawhere=" 1 ";

        $datawhere .= " and si.company_id='{$this->company_id}'";
        $datawhere .= " and sc.school_id>'0'";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '5';
        }
        $pagestart = ($page - 1) * $num;

        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        if($request['sort']==1){
            $order=" order by price asc";
        }else{
            $order=" order by price desc";
        }

        if($request['type']==1){
            //省
            $sql="select sum(si.income_price) as price,cr.region_name as cnname
              from smc_school_income as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join smc_code_region as cr on cr.region_id=sc.school_province
              where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and cr.region_id>0
              group by sc.school_province
              ";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',sc.school_province asc';
            }else{
                $sql.=$order.',sc.school_province desc';
            }
        }elseif($request['type']==2){
            //市
            $sql="select sum(si.income_price) as price,cr.region_name as cnname
              from smc_school_income as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join smc_code_region as cr on cr.region_id=sc.school_city
              where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and cr.region_id>0
              group by sc.school_city
              ";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',sc.school_city asc';
            }else{
                $sql.=$order.',sc.school_city desc';
            }

        }elseif($request['type']==3){
            //校

            $sql="select sum(si.income_price) as price,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as cnname
              from smc_school_income as si
              left join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and sc.school_id>0
              group by si.school_id";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',si.school_id asc';
            }else{
                $sql.=$order.',si.school_id desc';
            }

        }elseif($request['type']==4){
            //二级机构
            $sql="select sum(si.income_price) as price,co.organize_cnname as cnname
              from smc_school_income as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join gmc_company_organize as co on co.company_id=si.company_id
              where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and co.father_id='0'
              group by co.organize_id";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',co.organize_id asc';
            }else{
                $sql.=$order.',co.organize_id desc';
            }

        }elseif($request['type']==4){
            //三级机构
            $sql="select sum(si.income_price) as price,co.organize_cnname as cnname
              from smc_school_income as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join gmc_company_organize as co on co.company_id=si.company_id
              where {$datawhere} and si.income_confirmtime>='{$starttime}' and si.income_confirmtime<='{$endtime}' and co.father_id>'0'
              group by co.organize_id";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',co.organize_id asc';
            }else{
                $sql.=$order.',co.organize_id desc';
            }

        }else{
            $this->error = true;
            $this->errortip = "无对应维度";
            return false;
        }

        $list=$this->DataControl->selectClear($sql);
        if($list){
            $allNum=count($list);
        }else{
            $allNum=0;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;


        $incomeList=$this->DataControl->selectClear($sql);
        $tem_array=array();
        $x_data=array();
        if($incomeList){
            foreach($incomeList as $incomeOne){
                $x_data[]=$incomeOne['cnname'];
                $tem_array[]=sprintf("%.2f",($incomeOne['price']?$incomeOne['price']:0)/10000);
//                $tem_array[]=$incomeOne['price']?$incomeOne['price']:0;
            }
        }
        $y_data=array();
        $y_data[0]['name']=$this->LgStringSwitch('收入');
        $y_data[0]['data']=$tem_array;

        if($request['is_all']!=1){
            $data=array();
            $data['x_data']=$x_data;
            $data['y_data']=$y_data;
            $data['legendData']=$this->LgArraySwitch(array('收入'));
            $tem_data=array();
            $tem_data['list']=$data;
            $tem_data['allNum']=$allNum;
            return $tem_data;
        }else{
            if($incomeList){
                foreach($incomeList as $key=>&$value){
                    $value['price']=sprintf("%.2f",($value['price']?$value['price']:0)/10000);
//                    $value['price']=$value['price']?$value['price']:0;
                    $value['sort']=$key+1;
                }
            }
            $data=array();
            $data['list']=$incomeList;
            $data['allNum']=$allNum;

            return $data;
        }
    }


    function expendRanking($request){
        $datawhere=" 1 ";

        $datawhere .= " and si.company_id='{$this->company_id}'";
        $datawhere .= " and sc.school_id>'0'";

        if(isset($request['school_province']) && $request['school_province']!=''){
            $datawhere.=" and sc.school_province='{$request['school_province']}'";
        }

        if(isset($request['school_city']) && $request['school_city']!=''){
            $datawhere.=" and sc.school_city='{$request['school_city']}'";
        }

        if(isset($request['school_area']) && $request['school_area']!=''){
            $datawhere.=" and sc.school_area='{$request['school_area']}'";
        }

        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        if(isset($request['school_id']) && $request['school_id']!=''){
            $datawhere.=" and si.school_id='{$request['school_id']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $startDay=$request['starttime'];
        }else{
            $startDay=date("Y-01-01");
        }

        if(isset($request['endtime']) && $request['endtime']){
            $endDay=$request['endtime'];
        }else{
            $endDay=date("Y-m-d");
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '5';
        }
        $pagestart = ($page - 1) * $num;

        $starttime=strtotime($startDay);
        $endtime=strtotime($endDay." 23:59:59");

        if($request['sort']==1){
            $order=" order by price asc";
        }else{
            $order=" order by price desc";
        }

        if($request['type']==1){
            //省
            $sql="select sum(si.expend_price) as price,cr.region_name as cnname
              from smc_school_expend as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join smc_code_region as cr on cr.region_id=sc.school_province
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}' and cr.region_id>0
              group by sc.school_province
              ";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',sc.school_province asc';
            }else{
                $sql.=$order.',sc.school_province desc';
            }
        }elseif($request['type']==2){
            //市
            $sql="select sum(si.expend_price) as price,cr.region_name as cnname
              from smc_school_expend as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join smc_code_region as cr on cr.region_id=sc.school_city
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}' and cr.region_id>0
              group by sc.school_city
              ";

            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',sc.school_city asc';
            }else{
                $sql.=$order.',sc.school_city desc';
            }

        }elseif($request['type']==3){
            //校

            $sql="select sum(si.expend_price) as price,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as cnname
              from smc_school_expend as si
              left join smc_school as sc on sc.school_id=si.school_id
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}' and sc.school_id>0
              group by si.school_id";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }

            if($request['sort']==1){
                $sql.=$order.',si.school_id asc';
            }else{
                $sql.=$order.',si.school_id desc';
            }

        }elseif($request['type']==4){
            //二级机构
            $sql="select sum(si.expend_price) as price,co.organize_cnname as cnname
              from smc_school_expend as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join gmc_company_organize as co on co.company_id=si.company_id
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}' and co.father_id='0'
              group by co.organize_id";

            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }

            if($request['sort']==1){
                $sql.=$order.',co.organize_id asc';
            }else{
                $sql.=$order.',co.organize_id desc';
            }

        }elseif($request['type']==4){
            //三级机构
            $sql="select sum(si.expend_price) as price,co.organize_cnname as cnname
              from smc_school_expend as si
              left join smc_school as sc on sc.school_id=si.school_id
              left join gmc_company_organize as co on co.company_id=si.company_id
              where {$datawhere} and si.expend_confirmtime>='{$starttime}' and si.expend_confirmtime<='{$endtime}' and co.father_id>'0'
              group by co.organize_id";
            if(isset($request['keyword']) && $request['keyword'] != ''){
                $sql .= " having cnname like '%{$request['keyword']}%'";
            }
            if($request['sort']==1){
                $sql.=$order.',co.organize_id asc';
            }else{
                $sql.=$order.',co.organize_id desc';
            }

        }else{
            $this->error = true;
            $this->errortip = "无对应维度";
            return false;
        }

        $list=$this->DataControl->selectClear($sql);
        if($list){
            $allNum=count($list);
        }else{
            $allNum=0;
        }

        $sql .= ' limit ' . $pagestart . ',' . $num;


        $incomeList=$this->DataControl->selectClear($sql);
        $tem_array=array();
        $x_data=array();
        if($incomeList){
            foreach($incomeList as $incomeOne){
                $x_data[]=$incomeOne['cnname'];
                $tem_array[]=sprintf("%.2f",$incomeOne['price']/10000);
//                $tem_array[]=$incomeOne['price'];
            }
        }
        $y_data=array();
        $y_data[0]['name']=$this->LgStringSwitch('支出');
        $y_data[0]['data']=$tem_array;

        if($request['is_all']!=1){
            $data=array();
            $data['x_data']=$x_data;
            $data['y_data']=$y_data;
            $data['legendData']=$this->LgArraySwitch(array('支出'));
            $tem_data=array();
            $tem_data['list']=$data;
            $tem_data['allNum']=$allNum;
            return $tem_data;
        }else{
            if($incomeList){
                foreach($incomeList as $key=>&$value){
                    $value['price']=sprintf("%.2f",$value['price']/10000);
                    $value['sort']=$key+1;
                }
            }
            $data=array();
            $data['list']=$incomeList;
            $data['allNum']=$allNum;

            return $data;
        }
    }

    function getOrganize($request){
        $datawhere=" 1 ";
        if(isset($request['organizeclass_id']) && $request['organizeclass_id']!=''){
            $datawhere.=" and co.organizeclass_id='{$request['organizeclass_id']}'";
        }

        if(isset($request['father_id']) && $request['father_id']!=''){
            $datawhere.=" and co.father_id='{$request['father_id']}'";
        }else{
            $datawhere.=" and co.father_id='0'";
        }

        if(isset($request['organize_id']) && $request['organize_id']!=''){
            $datawhere.=" and co.organize_id='{$request['organize_id']}'";
        }

        $sql="select co.organize_id,co.organize_cnname,co.father_id
              from gmc_company_organize as co
              where {$datawhere} and co.company_id='{$this->company_id}'
              order by co.organize_createtime desc,co.organize_id desc
              ";

        $organizeList=$this->DataControl->selectClear($sql);

        if(!$organizeList){
            $organizeList=array();
        }

        return $organizeList;
    }

    function getSchoolList($paramArray)
    {

        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['father_id']) && $paramArray['father_id'] !== "0") {
            $datawhere .= " and o.organize_id ={$paramArray['father_id']}";
        }
        if (isset($paramArray['company_id']) && $paramArray['company_id'] !== "") {
            $datawhere .= " and s.company_id ={$paramArray['company_id']}";
        }
        if (isset($paramArray['school_province']) && $paramArray['school_province'] !== "") {
            $datawhere .= " and s.school_province ={$paramArray['school_province']}";
        }
        if (isset($paramArray['school_city']) && $paramArray['school_city'] !== "") {
            $datawhere .= " and s.school_city ={$paramArray['school_city']}";
        }

        if (isset($paramArray['organize_id']) && $paramArray['organize_id'] !== "") {
            $datawhere .= " and o.organize_id ={$paramArray['organize_id']}";
        }

        $sql = "SELECT s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_enname,s.school_branch,s.school_province,s.school_city
                FROM smc_school AS s
                LEFT JOIN gmc_company_organizeschool AS o ON s.school_id = o.school_id
                WHERE {$datawhere}
                GROUP BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";
        $schoolList = $this->DataControl->selectClear($sql);
        if(!$schoolList){
            $schoolList=array();
        }

        return $schoolList;

    }


}


