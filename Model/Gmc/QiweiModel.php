<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class QiweiModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $result = array();

    function __construct()
    {
        parent::__construct();
    }

    //获取企微的主要参数
    function getQiweiKeys()
    {
        return [
            // 学前主体
            'ww8977cb1b47249e00' => [
                'wxCorpId' => 'ww8977cb1b47249e00',   // 企业微信企业ID
                'wxContactsSecret' => 'e7t6q01rNxU-HRcqDdPMFcLtzf6BhnlyjppZanyoW9w',   // 企业微信通讯录秘钥
                'wxToken' => 'easywechat',    // 企业微信token
                'wxAesKey' => '35d4687abb469072a29f1c242xx222113xxxx',   // 企业微信aesKey
                'agent' => [
                    '1000009' => [
                        'wxAgentId' => '1000009',  // 企业微信应用ID
                        'wxAgentSecret' => 'RpxeEXqoBspUnyxLS-nat6hsWgLU-Dqq8OqqAOPfJ9w',  // 企业微信应用秘钥
                    ]
                ],
            ],
            // 少儿主体
            'ww4b31850049bfae77' => [
                'wxCorpId' => 'ww4b31850049bfae77',   // 企业微信企业ID
                'wxContactsSecret' => 'M5HlW8Vauy_-SilrprWOjLrGvQuN5LPvHWNR6n8CcSg',   // 企业微信通讯录秘钥
                'wxToken' => 'easywechat',    // 企业微信token
                'wxAesKey' => '35d4687abb469072a29f1c242xx222113x111xxx',   // 企业微信aesKey
                'agent' => [
                    '1000005' => [
                        'wxAgentId' => '1000005',  // 企业微信应用ID
                        'wxAgentSecret' => 'IJUmlKSJlOplh45LIllrhvmwthD9AXI9PZPRz7XCqCc',  // 企业微信应用秘钥
                    ]
                ],
            ]

        ];
    }

    /**
     * 获取企微的 access_token
     * @param $key   readapp 读取数据  writeapp 编辑数据
     * @param $corpId
     * @param $agentId
     * @return string
     */
    function getQiweiAccessToken($keyname = 'readapp', $corpId = 'ww8977cb1b47249e00', $agentId = '1000009'){

        if($keyname != 'readapp' && $keyname != 'writeapp'){
            return '';
        }

        if ($keyname == 'readapp') {//读 权限
            $tokenOne = $this->DataControl->getFieldOne("gmc_qiwei_token", "token_failuretime,token_string"
                , "token_type = '1' AND token_site = '1'", "order by token_failuretime DESC limit 0,1");
        }elseif($keyname == 'writeapp'){
//            $tokenOne = $this->DataControl->getFieldOne("gmc_qiwei_token", "token_failuretime,token_string"
//                , "token_type = '1' AND token_site = '2'", "order by token_failuretime DESC limit 0,1");
        }

        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $access_token = $tokenOne['token_string'];
        } else {
            $QiweiKeys = $this->getQiweiKeys();

            if ($keyname == 'readapp') {//读 权限
                $param = array(
                    'corpid' => $QiweiKeys[$corpId]['wxCorpId'],
                    'corpsecret' => $QiweiKeys[$corpId]['agent'][$agentId]['wxAgentSecret'],
                );

                //获取企微 access_token
                $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/gettoken", dataEncode($param), "GET");
                $dataArray = json_decode($getBakurl, "1");
                $data = array();
                $data['token_site'] = '1';//吉的堡教育企业微信（读）
                $data['token_type'] = '1';//token类型: 1 token 2 jstoken
                $data['token_string'] = $dataArray['access_token'];
                $data['token_failuretime'] = time() + $dataArray['expires_in'] - 300;
                $this->DataControl->insertData("gmc_qiwei_token", $data);
                $access_token = $dataArray['access_token'];

            } elseif ($keyname == 'writeapp') {// 应该是写权限 目前没有用到
//                $param = array(
//                    'corpid' => $QiweiKeys[$corpId]['wxCorpId'],
//                    'corpsecret' => $QiweiKeys[$corpId]['wxContactsSecret'],
//                );
//                //获取企微 access_token
//                $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/gettoken", dataEncode($param), "GET");
//                $dataArray = json_decode($getBakurl, "1");
//                $data = array();
//                $data['token_site'] = '2';//吉的堡教育企业微信（写）
//                $data['token_type'] = '1';//token类型: 1 token 2 jstoken
//                $data['token_string'] = $dataArray['access_token'];
//                $data['token_failuretime'] = time() + $dataArray['expires_in'] - 300;
//                $this->DataControl->insertData("gmc_qiwei_token", $data);
//                $access_token = $dataArray['access_token'];
            }
        }

        return $access_token?$access_token:'';
    }


    //获取企微 通讯录部门
    public function getQwTxlDepartment()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/department/list", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }
    //获取企微 通讯录标签数据
    public function getQwTxlTag()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/tag/list", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 通讯录 部门成员 ID
    public function getQwTxlStaffId()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
            "department_id" => 100002640,//部门ID
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/user/simplelist", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 通讯录 部门成员 详情
    public function getQwTxlStaffInfo()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
            "department_id" => 100000004,//部门ID
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/user/list", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }


    //获取企微 员工的 客户 ID
    public function getQwCustomeroId()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
            "userid" => '0111810028',
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 客户 单个客户详情
    public function getQwCustomeroOneInfo()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
            "external_userid" => 'wmlr_HBwAADi76xvF8n08kn3iTB24CMQ',//接口 getQwCustomeroId 客户的 ID
            "cursor" => ''
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 客户 批量获取客户详情
    public function getQwCustomeroSomeInfo()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "userid_list" => ['0111810028'],//通讯录 员工ID
            "cursor" => '',
            "limit" => '100',
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 客户 标签数据
    public function getQwCustomeroTag()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $param = [
            "access_token" => $QWtoken,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_corp_tag_list", dataEncode($param),"GET");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 客户 群列表
    public function getQwCustomeroCrowdId()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "cursor" => '',
            "limit" => '100',
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 客户 群 单个详情
    public function getQwCustomeroCrowdOneInfo()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "chat_id" => 'wrlr_HBwAA3_5JlpesNq5XKlwtWZ0DJg',//群列表接口获取
            "need_name" => '1',
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 朋友圈 发布的记录
    public function getQwPyqList($start_time,$end_time,$creator,$filter_type,$cursor,$limit)
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "start_time" => "1749744000",//开始时间
            "end_time" => "1749916800",//结束时间
            "creator" => "",//朋友圈创建人的userid
            "filter_type" => 2,//朋友圈类型。0：企业发表 1：个人发表 2：所有，包括个人创建以及企业创建，默认情况下为所有类型
            "cursor" => "",
            "limit" => 20,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_list?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 朋友圈 企业发表的列表
    public function getQwPyqComList()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "moment_id" => "mom0lr_HBwAA_hPXrmi6mpVJE3Lr_Ay-EQ",// 仅支持企业发布的 朋友圈ID
            "cursor" => "",
            "limit" => 10,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_task?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 朋友圈 动态可见范围
    public function getQwPyqOneSee()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "moment_id" => "mom1lr_HBwAAKTA77hqvrTgi8WZT7K7GRQ",//  朋友圈ID
            "userid" => "100002411",//朋友圈 创建人
            "cursor" => "",
            "limit" => 10,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_customer_list?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 朋友圈 动态 可见客户
    public function getQwPyqOneCustomeroSee()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "moment_id" => "mom1lr_HBwAAKTA77hqvrTgi8WZT7K7GRQ",//  朋友圈ID
            "userid" => "100002411",//朋友圈 创建人
            "cursor" => "",
            "limit" => 10,
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_send_result?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 朋友圈 互动
    public function getQwPyqOneExchange()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "moment_id" => "mom1lr_HBwAAtmJHWMygl1M2bizNV1IFgw",//  朋友圈ID
            "userid" => "81100002584",//朋友圈 创建人
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_moment_comments?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 群发 记录
    public function getQwMasssendList()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "chat_type" => "single",//  默认为single，表示发送给客户，group表示发送给客户群
            "start_time" => 1749657600,//开始时间
            "end_time" => 1749874245,//结束时间
            //"creator" => "",//创建人企业账号id   空值有问题
            "filter_type" => 2,//创建人类型。0：企业发表 1：个人发表 2：所有，包括个人创建以及企业创建，默认情况下为所有类型
            "cursor" => "",//
            "limit" => 100,//
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_list_v2?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 群发 任务成员列表
    public function getQwMasssendOnetMember()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "msgid" => "msglr_HBwAAvn5aA9_XMBT5Phpht2Gwhg",// 接口 getQwMasssendList 群发消息的id
            "cursor" => "",//
            "limit" => 100,//
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_task?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }

    //获取企微 群发 群发成员执行结果
    public function getQwMasssendOnetMemberResult()
    {
        $QWtoken = $this->getQiweiAccessToken();
        if($QWtoken == ''){
            $this->error = 0;
            $this->errortip = "秘钥信息错误!";
            return false;
        }
        //提交的参数
        $paramTwo = [
            "msgid" => "msglr_HBwAAvn5aA9_XMBT5Phpht2Gwhg",// 接口 getQwMasssendList 群发消息的id
            "userid" => "81100002579",// 发送成员userid，
            "cursor" => "",//
            "limit" => 100,//
        ];
        $getBakurl = httpRequest("https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_groupmsg_send_result?access_token=".$QWtoken, json_encode($paramTwo,JSON_UNESCAPED_UNICODE),"POST");
        $dataArray = json_decode($getBakurl, "1");


        print_r($dataArray);
        die;

    }















}
