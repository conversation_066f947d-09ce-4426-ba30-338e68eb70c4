<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  OrganizeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }


    //机构管理 -- 首页
    function getCompanyList($paramArray)
    {
        $result = array();

        $company_name = $this->DataControl->getFieldOne('gmc_company','company_cnname',"company_id = '{$paramArray['company_id']}'");
        $result['title'] = $company_name['company_cnname'];
        $data = $this-> DataControl->selectClear("select organize_id,organize_cnname,organize_class,father_id from gmc_company_organize where company_id = '{$paramArray['company_id']}'");
        if($paramArray['organizeclass_id']){
            $data = $this-> DataControl->selectClear("select organize_id,organize_cnname,organize_class,father_id from gmc_company_organize where company_id = '{$paramArray['company_id']}' and organizeclass_id = '{$paramArray['organizeclass_id']}'");
        }else{
            ajax_return(array('error' => 1,'errortip' => "获取失败"),$this->companyOne['company_language']);
        }

        $data = $this->tree($data);

        $result['children'] = $data;
        return $result;
    }

    function tree($items)
    {
        $son = array();
        if(is_array($items)){
            foreach($items as $k=>&$v) {
                if($v['father_id'] == 0) {
                    $son[$k]['title'] = $v['organize_cnname'];
                    $son[$k]['organize_class'] = $v['organize_class'];
                    $son[$k]['organize_id'] = $v['organize_id'];
                    foreach ($items as $key=>$value) {
                        if ($v['organize_id'] == $value['father_id']) {
                            $son[$k]['children'][$key]['title'] = $value['organize_cnname'];
                            $son[$k]['children'][$key]['organize_id'] = $value['organize_id'];
                            $son[$k]['children'][$key]['organize_class'] = $value['organize_class'];
                        }
                    }
                }
            }
        }
        return $son;
    }

    //机构详情
    function organizeDetailApi($paramArray)
    {
        $sql = "
            SELECT
                o.organize_cnname,
                FROM_UNIXTIME( o.organize_createtime, '%Y-%m-%d' ) AS organize_createtime,
                FROM_UNIXTIME( o.organize_updatetime, '%Y-%m-%d' ) AS organize_updatetime,
                o.organize_class,
                o.father_id,
                ( SELECT count( s.school_id ) FROM gmc_company_organizeschool AS s WHERE s.organize_id = '{$paramArray['organize_id']}' ) AS num,
                group_concat((case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end)) as school_name
            FROM
                gmc_company_organize AS o left join gmc_company_organizeschool as ss on o.organize_id = ss.organize_id left join smc_school as sc on sc.school_id = ss.school_id
            WHERE
                o.organize_id = '{$paramArray['organize_id']}'
                GROUP BY o.organize_id
           ";
        $organizeDetail = $this->DataControl->selectClear($sql);
        $result = array();
        $result['organizeDetail'] = $organizeDetail;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //编辑机构
    function updateOrganizeView($paramArray){
        $Organzie = $this->DataControl->getFieldOne("gmc_company_organize","organize_id", "organize_id = '{$paramArray['organize_id']}'");
        if ($Organzie) {
            $data = array();
            $data['organize_cnname'] = $paramArray['organize_cnname'];
            $data['organize_class'] = $paramArray['organize_class'];
            $data['organize_updatetime'] = time();

            $field = array();
            $field['organize_cnname'] = "机构名称";
            $field['organize_class'] = "机构类型";
            $field['organize_updatetime'] = "修改时间";

            if ($this->DataControl->updateData("gmc_company_organize","organize_id = '{$paramArray['organize_id']}'",$data) ) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑机构成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->架构管理",'编辑机构',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑机构失败', 'result' => $result);
            }
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //机构管辖分校管理
    function getSchoolList($paramArray)
    {
        $sql = "
            SELECT 
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_province,s.school_city
            FROM
                smc_school AS s
            WHERE company_id = '{$paramArray['company_id']}'";
        $school = $this->DataControl->selectClear($sql);
        $result = array();
        $result['school'] = $school;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //创建子机构
    function createOrganizeAction($paramArray){
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        if($paramArray['organize_id'] = ''){
            $data['father_id'] = '0';
        }else{
            $data['father_id'] = $paramArray['father_id'];
        }

        $data['organize_class'] = $paramArray['organize_class'];
        $data['organizeclass_id'] = $paramArray['organizeclass_id'];
        $data['organize_cnname'] = $paramArray['organize_cnname'];
        $data['organize_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属公司";
        $field['father_id'] = "上级机构 id";
        $field['organize_class'] = "机构类型：0运营机构1职能机构";
        $field['organize_cnname'] = "机构中文名称";
        $field['organize_createtime'] = "创建时间";

        if($this->DataControl->insertData('gmc_company_organize',$data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "创建子机构成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->架构管理",'创建子机构',dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '创建子机构失败', 'result' => $result);
        }
        return $res;
    }

    //删除子机构
    function delOrganizeAction($paramArray){
        $organizeOne = $this->DataControl->getFieldOne("gmc_company_organize","organize_id,father_id", "organize_id = '{$paramArray['organize_id']}' and company_id = '{$paramArray['company_id']}'");
        if ($organizeOne) {
            $organizeschool = $this->DataControl->getList("gmc_company_organizeschool","organize_id='{$organizeOne['organize_id']}'");
            if(!$organizeschool){
                if ($this->DataControl->delData("gmc_company_organize","organize_id = '{$paramArray['organize_id']}'") ) {
                    $result = array();
                    $res = array('error' => '0', 'errortip' => "删除子机构成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->架构管理",'删除子机构',dataEncode($paramArray));
                } else {
                    $result = array();
                    $res = array('error' => '1', 'errortip' => '删除子机构失败', 'result' => $result);
                }
            }else{
                ajax_return(array('error' => 1,'errortip' => "该机构下有学校，不可删除！"),$this->companyOne['company_language']);
            }

        }else{
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //机构管辖学校列表
    function manageSchoolView($paramArray)
    {
        $sql="
            SELECT
                s.school_id, 
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname
            FROM
                smc_school AS s
                LEFT JOIN gmc_company_organizeschool AS o ON o.school_id = s.school_id 
            WHERE
                o.organize_id = '{$paramArray['organize_id']}'
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $fieldstring = array('school_cnname');
        $fieldname = $this->LgArraySwitch(array('校区名称'));
        $fieldcustom = array("1");
        $fieldshow = array("1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($stafferDetail) {
            $result['list'] = $stafferDetail;
        } else {
            $result['list'] = array();
        }

        $organize = $this->DataControl->getFieldOne('gmc_company_organize','organize_cnname',"organize_id = '{$paramArray['organize_id']}'");
        $result['organize'] = $organize['organize_cnname'];

        if($result['list']){
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => "暂无管辖分校", 'result' => $result);
        }

        return $res;
    }

    //删除机构管辖学校
    function delManageSchoolAction($paramArray){
        $organizeOne = $this->DataControl->getFieldOne("gmc_company_organizeschool","organize_id,school_id", "organize_id = '{$paramArray['organize_id']}'");
        if ($organizeOne) {
            if ($this->DataControl->delData("gmc_company_organizeschool","organize_id = '{$paramArray['organize_id']}' and school_id = '{$paramArray['school_id']}'") ) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除机构管辖学校成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->架构管理",'删除机构管辖学校',dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除机构管辖学校失败', 'result' => $result);
            }
        }else{
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    function getSchoolApi($paramArray)
    {
        $datawhere = "s.company_id ={$paramArray['company_id']} AND s.school_isclose <> '1'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['father_id']) && $paramArray['father_id'] !== "0") {
            $datawhere .= " and o.organize_id ={$paramArray['father_id']}";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id'] !== "0") {
            $datawhere .= " and s.school_id not in (select ss.school_id from gmc_company_organizeschool as ss WHERE ss.organize_id = '{$paramArray['organize_id']}')";
        }
        if (isset($paramArray['school_province']) && $paramArray['school_province'] !== "") {
            $datawhere .= " and s.school_province ={$paramArray['school_province']}";
        }
        if (isset($paramArray['school_city']) && $paramArray['school_city'] !== "") {
            $datawhere .= " and s.school_city ={$paramArray['school_city']}";
        }
        if (isset($paramArray['school_area']) && $paramArray['school_area'] !== "") {
            $datawhere .= " and s.school_area ={$paramArray['school_area']}";
        }
        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== "") {
            $datawhere .= " and s.district_id ={$paramArray['district_id']}";
        }

        $sql = "
            SELECT
                s.school_id,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                s.school_branch,
                (select r.region_name from smc_code_region as r WHERE r.region_id = s.school_province) as province,
                (select r.region_name from smc_code_region as r WHERE r.region_id = s.school_city) as city,
                s.school_province,
                s.school_city 
            FROM smc_school AS s LEFT JOIN gmc_company_organizeschool AS o ON s.school_id = o.school_id WHERE {$datawhere} GROUP BY s.school_id 
            ORDER BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $fieldstring = array('school_cnname','school_enname','school_branch', 'province', 'city');
        $fieldname = $this->LgArraySwitch(array('校园名称', '检索代码','校区编号', '省', '市'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $district = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district WHERE company_id = '{$paramArray['company_id']}' ");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($stafferDetail) {
            $result['list'] = $stafferDetail;
            $result['district'] = $district;
        } else {
            $result['list'] = array();
            $result['district'] = $district;
        }
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;

    }

    //添加分校
    function addSchoolAction($paramArray){
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['school_id']),true);
        foreach ($schoolList as $item) {
            $data['school_id'] = $item['school_id'];
            $data['organize_id'] = $item['organize_id'];

            $a = $this->DataControl->getFieldOne('gmc_company_organizeschool','organize_id',"organize_id = '{$item['organize_id']}' and school_id = '{$item['school_id']}'");
            if ($a){
                ajax_return(array('error' => 1,'errortip' => "请勿重复添加"),$this->companyOne['company_language']);
            }
            $this->DataControl->insertData('gmc_company_organizeschool',$data);
        }

        $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->架构管理",'添加机构管辖分校',dataEncode($paramArray));

        return $res;
    }

    function changeLossClientToClient($paramArray)
    {
        $dataClient = array();
        $dataClient['client_distributionstatus'] = $paramArray['client_distributionstatus'];
        $dataClient['client_tracestatus'] = $paramArray['client_tracestatus'];

        if(!is_array($paramArray['client_id']))
        {
            $paramArray['client_id'] = [$paramArray['client_id']];
        }
        for ($i = 0; $i < count($paramArray['client_id']); $i++) {
            $client_id = $paramArray['client_id'][$i];
            $dataClient['client_id'] = $paramArray['client_id'][$i];
            //更新客户状态

            $this->DataControl->begintransaction();
            if (!$this->DataControl->updateData("crm_client", "client_id={$client_id}", $dataClient)) {

                $this->DataControl->rollback();
                return false;
            }

            //增加跟踪记录
            $data = array();
            $data['marketer_id'] = $paramArray['marketer_id'];
            $data['client_id'] = $client_id;
            $data['track_linktype'] = $this->LgStringSwitch("系统操作");
            $data['track_validinc'] = 0;
            $data['track_followmode'] = 0;
            $data['track_note'] = $this->LgStringSwitch("系统流转:无意向客户转为招生线索");
            $data['track_createtime'] = time();
            $data['track_type']=1;
            $data['track_initiative']=0;
            if (!$this->DataControl->insertData('crm_client_track', $data)) {

                $this->DataControl->rollback();
                return false;
            }
        }
        $this->DataControl->commit();
        return true;
    }


    //架构管理 -- 删除机构模式 by:qyh
    function delOrganizeClassAction($paramArray)
    {
        $organizeClassOne = $this->DataControl->getFieldOne("gmc_code_organizeclass", "organizeclass_id,organizeclass_isdefault", "organizeclass_id = '{$paramArray['organizeclass_id']}' and company_id = '{$paramArray['company_id']}'");
        if ($organizeClassOne) {
            if($organizeClassOne['organizeclass_isdefault'] == '0'){
                $organizes = $this->DataControl->getList("gmc_company_organize", "organizeclass_id='{$organizeClassOne['organizeclass_id']}'");
                if (!$organizes) {

                    if ($this->DataControl->delData("gmc_code_organizeclass", "organizeclass_id = '{$paramArray['organizeclass_id']}'")) {
                        $result = array();
                        $res = array('error' => '0', 'errortip' => "删除机构模式成功", 'result' => $result);
                        $this->addGmcWorklog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->架构管理", '删除机构模式', dataEncode($paramArray));
                    } else {
                        $result = array();
                        $res = array('error' => '1', 'errortip' => '删除机构模式失败', 'result' => $result);
                    }
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "该机构模式下有组织，不可删除！"),$this->companyOne['company_language']);
                }
            }else{
                ajax_return(array('error' => 1, 'errortip' => "默认组织无法删除！"),$this->companyOne['company_language']);

            }


        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //架构管理 -- 编辑机构模式 by:qyh
    function updateOrganizeClassAction($paramArray)
    {
        $OrganizeClass = $this->DataControl->getFieldOne("gmc_code_organizeclass", "organizeclass_id", "organizeclass_id = '{$paramArray['organizeclass_id']}'");
        if ($OrganizeClass) {
            $data = array();
            $data['organizeclass_name'] = $paramArray['organizeclass_name'];
            $data['organizeclass_colour'] = $paramArray['organizeclass_colour'];
            $data['organizeclass_note'] = $paramArray['organizeclass_note'];
            $data['organizeclass_isdefault'] = $paramArray['organizeclass_isdefault'];
            $data['organizeclass_updatetime'] = time();

            if($paramArray['organizeclass_isdefault'] == '1'){
                $isset = $this->DataControl->getFieldOne("gmc_code_organizeclass","organizeclass_id","company_id = '{$paramArray['company_id']}' and organizeclass_isdefault = '1'");
                $datas = array();
                $datas['organizeclass_isdefault'] = '0';
                $datas['organizeclass_updatetime'] = time();
                $this->DataControl->updateData("gmc_code_organizeclass","organizeclass_id = '{$isset['organizeclass_id']}'",$datas);
                $data['organizeclass_isdefault'] = $paramArray['organizeclass_isdefault'];
            }else{
                $isset = $this->DataControl->getFieldOne("gmc_code_organizeclass","organizeclass_id","company_id = '{$paramArray['company_id']}' and organizeclass_isdefault = '1' and organizeclass_id = '{$paramArray['organizeclass_id']}'");
                if($isset){
                    ajax_return(array('error' => 1,'errortip' => "必须有一个默认组织"),$this->companyOne['company_language']);
                }
            }


            $field = array();
            $field['organizeclass_name'] = "机构模式名称";
            $field['organizeclass_colour'] = "机构模式颜色";
            $field['organizeclass_note'] = "备注";
            $field['organizeclass_updatetime'] = "修改时间";

            if ($this->DataControl->updateData("gmc_code_organizeclass", "organizeclass_id = '{$paramArray['organizeclass_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑机构模式成功", 'result' => $result);
                $this->addGmcWorklog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->架构管理", '编辑机构模式', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑机构模式失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //架构管理 -- 编辑机构是否默认 by:qyh
    function updateOrganizeStatusAction($paramArray)
    {
        if($paramArray['organizeclass_isdefault'] = '0'){
            ajax_return(array('error' => 1,'errortip' => "必须有一个默认组织"),$this->companyOne['company_language']);
        }else{
            $isset = $this->DataControl->getFieldOne("gmc_code_organizeclass","organizeclass_id","company_id = '{$paramArray['company_id']}' and organizeclass_isdefault = '1'");
            $data = array();
            $data['organizeclass_isdefault'] = 0;
            $data['organizeclass_updatetime'] = time();
            $this->DataControl->updateData("gmc_code_organizeclass", "organizeclass_id = '{$isset['organizeclass_id']}'", $data);

            $data = array();
            $data['organizeclass_isdefault'] = 1;
            $data['organizeclass_updatetime'] = time();

            $field = array();
            $field['organizeclass_isdefault'] = "是否默认机构0否1是";
            $field['organizeclass_updatetime'] = "修改时间";

            if ($this->DataControl->updateData("gmc_code_organizeclass", "organizeclass_id = '{$paramArray['organizeclass_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变默认状态成功", 'result' => $result);
                $this->addGmcWorklog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->架构管理", '改变默认状态', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变默认状态失败', 'result' => $result);
            }
        }

        return $res;
    }

    //架构管理 -- 创建机构模式 by:qyh
    function createOrganizeClassAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['organizeclass_name'] = $paramArray['organizeclass_name'];
        $data['organizeclass_colour'] = $paramArray['organizeclass_colour'];
        $data['organizeclass_note'] = $paramArray['organizeclass_note'];
        $data['organizeclass_createtime'] = time();
        $data['organizeclass_note'] = $paramArray['organizeclass_note'];
        if($paramArray['organizeclass_isdefault'] == '1'){
            $isset = $this->DataControl->getFieldOne("gmc_code_organizeclass","organizeclass_id","company_id = '{$paramArray['company_id']}' and organizeclass_isdefault = '1'");
            $datas = array();
            $datas['organizeclass_isdefault'] = '0';
            $datas['organizeclass_updatetime'] = time();
            $this->DataControl->updateData("gmc_code_organizeclass","organizeclass_id = '{$isset['organizeclass_id']}'",$datas);
            $data['organizeclass_isdefault'] = $paramArray['organizeclass_isdefault'];
        }

        $isset = $this->DataControl->getFieldOne("gmc_code_organizeclass","organizeclass_id","company_id = '{$paramArray['company_id']}'");
        if(!$isset){
            $data['organizeclass_isdefault'] = '1';
        }

        $field = array();
        $field['company_id'] = "所属公司";
        $field['organizeclass_name'] = "机构模式名字";
        $field['organizeclass_colour'] = "机构模式颜色";
        $field['organizeclass_note'] = "机构模式备注";
        $field['organizeclass_createtime'] = "创建时间";
        $field['organizeclass_isdefault'] = "是否默认机构0否1是";

        if ($this->DataControl->insertData('gmc_code_organizeclass', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "创建机构模式成功", 'result' => $result);
            $this->addGmcWorklog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->架构管理", '创建机构模式', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '创建机构模式失败', 'result' => $result);
        }
        return $res;
    }

    ///架构管理 -- 机构模式详情 by:qyh
    function organizeClassDetailApi($paramArray)
    {
        $sql = "
            SELECT o.organizeclass_name,organizeclass_colour,organizeclass_isdefault,organizeclass_note,FROM_UNIXTIME(organizeclass_createtime) as organizeclass_createtime,organizeclass_updatetime as organizeclass_updatetime from gmc_code_organizeclass as o where o.organizeclass_id = '{$paramArray['organizeclass_id']}' GROUP BY o.organizeclass_id DESC
           ";
        $organizeDetail = $this->DataControl->selectClear($sql);
        if(is_array($organizeDetail)){
            foreach ($organizeDetail as &$organizeDetailVar){
                $organizeDetailVar['organizeclass_updatetime'] = ($organizeDetailVar['organizeclass_updatetime']==0?$this->LgStringSwitch('暂无修改'):date('Y-m-d H:i:s',$organizeDetailVar['organizeclass_updatetime']));
            }
        }
        $result = array();
        if($organizeDetail){
            $result['organizeDetail'] = $organizeDetail;

        }else{
            $result['organizeDetail'] = array();

        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }





}
