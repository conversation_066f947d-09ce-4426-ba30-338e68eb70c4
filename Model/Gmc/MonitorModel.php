<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class MonitorModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();
    //监控的参数  账号：ym18521530218
    const clientId = '0ab63e22a9d54e4a8620528f8cc64218';
    const clientSecret = 'b2605c8b4d444619b0af5830ed16bbce';
    const grantType = 'client_credentials';
    const scope = 'app';

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }
    //get
    public function curlGet($url,$Authorization){
        $header  = array(
            'Authorization:'.$Authorization,
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
    //post
    public function curlPost($url,$post_data = array(),$Authorization){
        $header  = array(
            'Authorization:'.$Authorization,
        );
        if (is_array($post_data))
        {
            $post_data = http_build_query($post_data, null, '&');
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch , CURLOPT_URL , $url);
        curl_setopt($ch , CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch , CURLOPT_POST, 1);
        curl_setopt($ch , CURLOPT_POSTFIELDS, $post_data);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    //获取监控的 token
    function getMonitorToken(){
        $paramarray = array(
            'client_id' => self::clientId,
            'client_secret' => self::clientSecret,
            'grant_type' => self::grantType,
            'scope' => self::scope
        );
        $resultJson = request_by_curl("https://api2.hik-cloud.com/oauth/token",dataEncode($paramarray),"POST",array());
        $resultArray = json_decode($resultJson,true);
        return $resultArray;
    }

    //999013 ->监控->学校列表 -- 97
    function getDeviceSchoolList($request){
        $datawhere=" 1 and s.company_id = '{$request['company_id']}' and s.school_monitorcode <> '' and s.school_monitorcode is not null ";
        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (d.device_name like '%{$request['keyword']}%' or d.device_serial like '%{$request['keyword']}%' or d.device_model like '%{$request['keyword']}%')";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = " SELECT s.school_id,s.company_id,s.companies_id,s.school_branch,s.school_shortname,s.school_cnname,s.school_enname  
                FROM smc_school as s 
                WHERE {$datawhere} 
                ORDER BY s.school_id DESC    
                LIMIT {$pagestart},{$num}";

        $deviceList = $this->DataControl->selectClear($sql);

        if(isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(s.school_id) as datanum
                    FROM smc_school as s 
                WHERE {$datawhere}  ");
            $allnums = $all_num['datanum'] + 0;
        }else{
            $allnums = 0;
        }


        $result = array();
        $result["datalist"] = is_array($deviceList)?$deviceList:array();
        $result["count"] = $allnums;

        if($deviceList){
            $this->error = 0;
            $this->errortip = "监控学校列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无监控学校列表";
            $this->result = $result;
            return false;
        }

    }

    //999013 ->监控->监控设备列表
    function getDeviceList($request){
        $datawhere=" 1 and d.company_id = '{$request['company_id']}' ";
        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (d.device_channelname like '%{$request['keyword']}%' or d.device_deviceserial like '%{$request['keyword']}%' or d.device_channelno like '%{$request['keyword']}%' or d.device_channelid like '%{$request['keyword']}%')";
        }
        if(isset($request['school_id']) && $request['school_id'] != ''){
            $datawhere .= " and d.school_id = '{$request['school_id']}' ";
        }
        if(isset($request['device_channelstatus']) && $request['device_channelstatus'] != ''){
            $datawhere .= " and d.device_channelstatus = '{$request['device_channelstatus']}' ";
        }
        if(isset($request['position_id']) && $request['position_id'] != ''){
            $datawhere .= " and m.position_id = '{$request['position_id']}' ";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = " SELECT d.*,s.school_branch,s.school_shortname,s.school_cnname,m.position_id,p.position_name FROM gmc_monitor_device AS d
                left join smc_school as s on d.school_id=s.school_id 
                LEFT JOIN gmc_monitor_device_mate as m ON (d.device_id = m.device_id and d.company_id = m.company_id) 
                LEFT JOIN gmc_monitor_position as p ON m.position_id = p.position_id 
                WHERE {$datawhere} 
                ORDER BY d.device_id DESC    
                LIMIT {$pagestart},{$num}";

        $deviceList = $this->DataControl->selectClear($sql);

        if ($deviceList) {
            $status = $this->LgArraySwitch(array("0" => "离线", "1" => "在线" ,'-1' => "未知状态"));
            foreach ($deviceList as &$val) {
                $val['device_statusname'] = $status[$val['device_channelstatus']];
            }
        }

        if(isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(d.device_id) as datanum
                    FROM gmc_monitor_device AS d
                    left join smc_school as s on d.school_id=s.school_id 
                    LEFT JOIN gmc_monitor_device_mate as m ON (d.device_id = m.device_id and d.company_id = m.company_id) 
                    LEFT JOIN gmc_monitor_position as p ON m.position_id = p.position_id 
                    WHERE {$datawhere} ");
            $allnums = $all_num['datanum'] + 0;
        }else{
            $allnums = 0;
        }

        //上次从海康云眸更新通道（即摄像头）的时间
        if(isset($request['school_id']) && $request['school_id'] != '') {
            $deviceOne = $this->DataControl->selectOne("select device_pulltime from gmc_monitor_device WHERE company_id = '{$request['company_id']}' and school_id ='{$request['school_id']}' ORDER BY device_pulltime DESC limit 0,1");
            $pulltime = date("Y-m-d H:i:s",$deviceOne['device_pulltime']);
        }else{
            $pulltime = '';
        }

        $result = array();
        $result["datalist"] = is_array($deviceList)?$deviceList:array();
        $result["pulltime"] = $pulltime;
        $result["count"] = $allnums;

        if($deviceList){
            $this->error = 0;
            $this->errortip = "监控设备列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无监控设备列表";
            $this->result = $result;
            return false;
        }

    }

    //999014 ->监控->监控单个设备详情 -- 97
    function getDeviceOneApi($request){
        if(!isset($request['device_id']) || $request['device_id'] == '' ){
            $this->error = 1;
            $this->errortip = "设备ID不能为空！";
            $this->result = array();
            return false;
        }

        $deviceOne = $this->DataControl->selectOne("select d.*,s.school_shortname,s.school_branch,m.position_id from gmc_monitor_device as d 
                    LEFT JOIN smc_school as s ON d.school_id = s.school_id 
                    LEFT JOIN gmc_monitor_device_mate as m ON (d.device_id = m.device_id and d.company_id = m.company_id)
                    WHERE d.company_id='{$request['company_id']}' and d.device_id='{$request['device_id']}'");

        $status = $this->LgArraySwitch(array("0" => "离线", "1" => "在线" ,'-1' => "未知状态"));
        $deviceOne['device_channelstatusname'] = $status[$deviceOne['device_channelstatus']];

        if($deviceOne){
            $this->error = 0;
            $this->errortip = "设备信息获取成功！";
            $this->result = $deviceOne;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "设备信息获取失败！";
            $this->result = array();
            return false;
        }
    }

    //999014 ->监控->监控单个设备编辑 -- 97
    function editDeviceOneApi($request){
        if(!isset($request['device_id']) || $request['device_id'] == '' ){
            $this->error = 1;
            $this->errortip = "设备ID不能为空！";
            $this->result = array();
            return false;
        }
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置不能为空！";
            $this->result = array();
            return false;
        }

        $data = array();
        if(isset($request['device_remarks']) && $request['device_remarks'] != '') {
            $data['device_remarks'] = $request['device_remarks'];
        }
        $data['device_updatetime'] = time();
        $circleid = $this->DataControl->updateData('gmc_monitor_device',"device_id='{$request['device_id']}' and company_id='{$request['company_id']}'",$data);
        $data['position_id'] = $request['position_id'];

        if($this->DataControl->selectOne("select mate_id from gmc_monitor_device_mate WHERE company_id='{$request['company_id']}' and device_id='{$request['device_id']}'")){
            //修改位置
            $datamate = array();
            $datamate['position_id'] = $request['position_id'];
            $this->DataControl->updateData('gmc_monitor_device_mate',"device_id='{$request['device_id']}' and company_id='{$request['company_id']}'",$datamate);

            //查找最后一条记录
            $matelogOne = $this->DataControl->selectOne("select matelog_id from gmc_monitor_device_matelog WHERE device_id='{$request['device_id']}' and company_id='{$request['company_id']}' ORDER BY matelog_id DESC  limit 0,1 ");
            //添加位置修改记录
            $this->DataControl->updateData('gmc_monitor_device_matelog'," matelog_id='{$matelogOne['matelog_id']}' ",array('matelog_endtime'=>time()));
            //新的位置修改记录
            $datamateOne = array();
            $datamateOne['company_id'] = $request['company_id'];
            $datamateOne['device_id'] = $request['device_id'];
            $datamateOne['position_id'] = $request['position_id'];
            $datamateOne['matelog_starttime'] = time();
            $this->DataControl->insertData('gmc_monitor_device_matelog',$datamateOne);
        }else{
            $datamate = array();
            $datamate['company_id'] = $request['company_id'];
            $datamate['device_id'] = $request['device_id'];
            $datamate['position_id'] = $request['position_id'];
            $this->DataControl->insertData('gmc_monitor_device_mate',$datamate);
            //记录
            $datamate['matelog_starttime'] = time();
            $this->DataControl->insertData('gmc_monitor_device_matelog',$datamate);
        }

        if($circleid){
            $this->error = 0;
            $this->errortip = "设备编辑成功！";
            $this->result = $data;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "设备编辑失败！";
            $this->result = array();
            return false;
        }
    }

    //999014 ->监控->监控多个设备编辑 -- 97
    function editSomeDeviceOneApi($request){
        $deviceids = json_decode(stripslashes($request['deviceids']),true);
        if(!$deviceids){
            $this->error = 1;
            $this->errortip = "设备ID不能为空！";
            $this->result = array();
            return false;
        }
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置不能为空！";
            $this->result = array();
            return false;
        }

        if($deviceids){
            foreach ($deviceids as $deviceidsvar){
                $data = array();
                if(isset($request['device_remarks']) && $request['device_remarks'] != '') {
                    $data['device_remarks'] = $request['device_remarks'];
                }
                $data['device_updatetime'] = time();
                $circleid = $this->DataControl->updateData('gmc_monitor_device',"device_id='{$deviceidsvar}' and company_id='{$request['company_id']}'",$data);
                $data['position_id'] = $request['position_id'];

                if($this->DataControl->selectOne("select mate_id from gmc_monitor_device_mate WHERE company_id='{$request['company_id']}' and device_id='{$deviceidsvar}'")){
                    //修改位置
                    $datamate = array();
                    $datamate['position_id'] = $request['position_id'];
                    $this->DataControl->updateData('gmc_monitor_device_mate',"device_id='{$deviceidsvar}' and company_id='{$request['company_id']}'",$datamate);

                    //查找最后一条记录
                    $matelogOne = $this->DataControl->selectOne("select matelog_id from gmc_monitor_device_matelog WHERE device_id='{$deviceidsvar}' and company_id='{$request['company_id']}' ORDER BY matelog_id DESC  limit 0,1 ");
                    //添加位置修改记录
                    $this->DataControl->updateData('gmc_monitor_device_matelog'," matelog_id='{$matelogOne['matelog_id']}' ",array('matelog_endtime'=>time()));
                    //新的位置修改记录
                    $datamateOne = array();
                    $datamateOne['company_id'] = $request['company_id'];
                    $datamateOne['device_id'] = $deviceidsvar;
                    $datamateOne['position_id'] = $request['position_id'];
                    $datamateOne['matelog_starttime'] = time();
                    $this->DataControl->insertData('gmc_monitor_device_matelog',$datamateOne);
                }else{
                    $datamate = array();
                    $datamate['company_id'] = $request['company_id'];
                    $datamate['device_id'] = $deviceidsvar;
                    $datamate['position_id'] = $request['position_id'];
                    $this->DataControl->insertData('gmc_monitor_device_mate',$datamate);
                    //记录
                    $datamate['matelog_starttime'] = time();
                    $this->DataControl->insertData('gmc_monitor_device_matelog',$datamate);
                }
            }
        }

        if($circleid){
            $this->error = 0;
            $this->errortip = "编辑成功！";
            $this->result = $data;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "编辑失败！";
            $this->result = array();
            return false;
        }
    }

    //999015 ->监控->（海康云眸）某学校的监控设备
    function getHkSchoolDevice($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "暂时没有设备信息，请选择学校哦~！";
            $this->result = array();
            return false;
        }
        $schoolOne = $this->DataControl->selectOne("select school_branch,school_shortname,school_monitorcode from smc_school WHERE school_id = '{$request['school_id']}' and company_id = '{$request['company_id']}' limit 0,1 ");
        if($schoolOne['school_monitorcode'] == ''){
            $this->error = 1;
            $this->errortip = "学校监控设备码不能为空！";
            $this->result = array();
            return false;
        }

        //获取监控的 token
        $resultArray = $this->getMonitorToken($request);
        $Authorization = $resultArray['token_type'].$resultArray['access_token'];

        if(strpos($schoolOne['school_monitorcode'],',') !== false){
            $monitorcode = explode(",",$schoolOne['school_monitorcode']);
            $dataall = array();
            foreach ($monitorcode as $monitorcodevar){
                //POST参数  //获取设备下所有 通道（即摄像头）
                $qcode = "https://api2.hik-cloud.com/v1/customization/devices/channels/actions/listByDevSerial?deviceSerial={$monitorcodevar}&pageNo=1&pageSize=80";//连锁的开发接口
                $channelsjson = $this->curlGet($qcode, $Authorization);
                $channelsArray = json_decode($channelsjson, true);

                $dataall = array_merge($dataall,$channelsArray['data']);
            }
        }else {
            //POST参数  //获取设备下所有 通道（即摄像头）
            $qcode = "https://api2.hik-cloud.com/v1/customization/devices/channels/actions/listByDevSerial?deviceSerial={$schoolOne['school_monitorcode']}&pageNo=1&pageSize=80";//连锁的开发接口
            $channelsjson = $this->curlGet($qcode, $Authorization);
            $channelsArray = json_decode($channelsjson, true);

            $dataall = $channelsArray['data'];
        }

        $result = array();
        $dataList = array();
        if(is_array($dataall)){
            $status = $this->LgArraySwitch(array("0" => "离线", "1" => "在线" ,'-1' => "未知状态"));
            $use = $this->LgArraySwitch(array("0" => "未启用", "1" => "已启用"));
            foreach ($dataall as $channelsvar){
                $dataOne = array();
                $dataOne['school_branch'] = $schoolOne['school_branch'];
                $dataOne['school_shortname'] = $schoolOne['school_shortname'];

                $dataOne['device_deviceserial'] = $channelsvar['deviceSerial'];
                $dataOne['device_channelid'] = $channelsvar['channelId'];
                $dataOne['device_channelname'] = $channelsvar['channelName'];
                $dataOne['device_channelno'] = $channelsvar['channelNo'];
                $dataOne['device_channelstatus'] = $channelsvar['channelStatus'];
                $dataOne['device_channelstatusname'] = $status[$channelsvar['channelStatus']];
//                $dataOne['device_channeltype'] = $channelsvar['channelType'];
                $dataOne['device_isuse'] = $channelsvar['isUse'];
                $dataOne['device_isusename'] = $use[$channelsvar['isUse']];
                //数组
                $dataList[] = $dataOne;
            }
            $result["datalist"] = $dataList;
            $result["count"] = count($dataall);
        }else{
            $result["datalist"] = $dataList;
            $result["count"] = 0;
        }

        if($dataList){
            $this->error = 0;
            $this->errortip = "（海康云眸）某学校的监控设备信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无（海康云眸）某学校的监控设备";
            $this->result = $result;
            return false;
        }
    }

    //999015 ->监控->同步某个学校的监控设备 -- 97
    function pullHkSchoolDevice($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "学校ID不能为空！";
            $this->result = array();
            return false;
        }
        $schoolOne = $this->DataControl->selectOne("select school_id,school_monitorcode,school_shortname from smc_school WHERE school_id = '{$request['school_id']}' and company_id = '{$request['company_id']}' limit 0,1 ");
        if($schoolOne['school_monitorcode'] == ''){
            $this->error = 1;
            $this->errortip = "学校监控设备码不能为空！";
            $this->result = array();
            return false;
        }else {
            $theschool_monitorcode = explode(",",$schoolOne['school_monitorcode']);
            $school_monitorcodestr = '0';
            foreach ($theschool_monitorcode as $theschool_monitorcodevar){
                $school_monitorcodestr .= ",'".$theschool_monitorcodevar."'";
            }
            //删除已经删除设备码的数据
            $this->DataControl->delData("gmc_monitor_device", " school_id='{$schoolOne['school_id']}' and company_id='{$request['company_id']}' and device_deviceserial not in ($school_monitorcodestr) ");
        }

        //获取监控的 token
        $resultArray = $this->getMonitorToken($request);
        $Authorization = $resultArray['token_type'].$resultArray['access_token'];

        if(strpos($schoolOne['school_monitorcode'],',') !== false){
            $monitorcode = explode(",",$schoolOne['school_monitorcode']);
            $dataall = array();
            foreach ($monitorcode as $monitorcodevar){
                //POST参数  //获取设备下所有 通道（即摄像头）
                $qcode = "https://api2.hik-cloud.com/v1/customization/devices/channels/actions/listByDevSerial?deviceSerial={$monitorcodevar}&pageNo=1&pageSize=80";//连锁的开发接口
                $channelsjson = $this->curlGet($qcode, $Authorization);
                $channelsArray = json_decode($channelsjson, true);

                $dataall = array_merge($dataall,$channelsArray['data']);
            }
        }else {

            //POST参数  //获取设备下所有 通道（即摄像头）
            $qcode = "https://api2.hik-cloud.com/v1/customization/devices/channels/actions/listByDevSerial?deviceSerial={$schoolOne['school_monitorcode']}&pageNo=1&pageSize=80";//连锁的开发接口
            $channelsjson = $this->curlGet($qcode, $Authorization);
            $channelsArray = json_decode($channelsjson, true);

            $dataall = $channelsArray['data'];
        }

        $successnum = 0;
        $failnum = 0;
        if(is_array($dataall)){
            foreach ($dataall as $channelsvar){
                $deviceOne = $this->DataControl->selectOne("select d.device_id from gmc_monitor_device as d WHERE d.company_id = '{$request['company_id']}' and d.school_id = '{$request['school_id']}' and d.device_channelid = '{$channelsvar['channelId']}' ");

                $dataOne = array();
                $dataOne['device_deviceserial'] = $channelsvar['deviceSerial'];
                $dataOne['device_channelid'] = $channelsvar['channelId'];
                $dataOne['device_channelname'] = $channelsvar['channelName'];
                $dataOne['device_channelno'] = $channelsvar['channelNo'];
                $dataOne['device_channelstatus'] = $channelsvar['channelStatus'];
//                $dataOne['device_channeltype'] = $channelsvar['channelType'];
                $dataOne['device_isuse'] = $channelsvar['isUse'];
                $dataOne['device_pulltime'] = time();
                if($deviceOne){
                    //有设备更新数据
                    if($this->DataControl->updateData('gmc_monitor_device',"device_id='{$deviceOne['device_id']}' and company_id='{$request['company_id']}'",$dataOne)){
                        $successnum++;
                    }else{
                        $failnum++;
                    }
                }else{
                    //无设备添加数据
                    $dataOne['company_id'] = $request['company_id'];
                    $dataOne['school_id'] = $request['school_id'];
                    if($this->DataControl->insertData('gmc_monitor_device',$dataOne)){
                        $successnum++;
                    }else{
                        $failnum++;
                    }
                }
            }
        }
        //该学校共有多少通道（即摄像头）
        if($dataall) {
            $allnum = count($dataall);
        }else{
            $allnum = 0;
        }

        if($allnum == $successnum){
            $this->error = 0;
            $this->errortip = "【{$schoolOne['school_shortname']}】{$successnum}个设备同步已完成";
            $this->result = array();
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "【{$schoolOne['school_shortname']}】{$successnum}个设备同步已完成,{$failnum}个设备同步未完成";
            $this->result = array();
            return false;
        }
    }

    //999016->监控->（集团）监控位置管理列表
    function getHaveMonitorPositionList($request){
        $datawhere=" 1 and m.company_id = '{$request['company_id']}' and m.company_id = p.company_id  ";
        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (p.position_name like '%{$request['keyword']}%' )";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = " SELECT p.* 
                FROM gmc_monitor_device_mate as m 
                LEFT JOIN gmc_monitor_position AS p ON m.position_id = p.position_id  
                WHERE {$datawhere} 
                GROUP BY m.position_id  
                LIMIT {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);

        if(isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectClear("SELECT m.position_id 
                    FROM gmc_monitor_device_mate as m 
                LEFT JOIN gmc_monitor_position AS p ON m.position_id = p.position_id  
                WHERE {$datawhere} 
                GROUP BY m.position_id    ");
            if($all_num){
                $count = count($all_num);
            }else {
                $count = 0;
            }
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["count"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "位置信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无位置信息信息";
            $this->result = $result;
            return false;
        }
    }

    //999016->监控->（集团）监控位置管理列表
    function getMonitorPositionList($request){
        $datawhere=" 1 and p.company_id = '{$request['company_id']}' ";
        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (p.position_name like '%{$request['keyword']}%' )";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = " SELECT p.*,(SELECT count(*) FROM gmc_monitor_position_userfunc as u WHERE u.position_id = p.position_id) as postrolenames 
                FROM gmc_monitor_position AS p 
                WHERE {$datawhere} 
                ORDER BY p.position_id DESC    
                LIMIT {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);

        if(isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(p.position_id) as datanum
                    FROM gmc_monitor_position AS p 
                    WHERE {$datawhere}  ");
            $count = $all_num['datanum'] + 0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["count"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "位置信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无位置信息信息";
            $this->result = $result;
            return false;
        }
    }

    //999017->监控->（集团）监控位置添加 --97
    function addMonitorPositionOneApi($request){
        if(!isset($request['position_name']) || $request['position_name'] == '' ){
            $this->error = 1;
            $this->errortip = "位置名称不能为空！";
            $this->result = array();
            return false;
        }
        if($this->DataControl->selectOne("select * from gmc_monitor_position WHERE position_name = '{$request['position_name']}' and company_id = '{$request['company_id']}' ")){
            $this->error = 1;
            $this->errortip = "位置名称不能重复！";
            $this->result = array();
            return false;
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['position_name'] = $request['position_name'];
        $data['position_desc'] = $request['position_desc'];
        $positionid = $this->DataControl->insertData('gmc_monitor_position',$data);
        $data['position_id'] = $positionid;

        if($positionid){
            $this->error = 0;
            $this->errortip = "位置创建成功！";
            $this->result = $data;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "位置创建失败！";
            $this->result = array();
            return false;
        }
    }

    //999018->监控->（集团）监控位置详情 --97
    function getMonitorPositionOneApi($request){
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

        $deviceOne = $this->DataControl->selectOne("select p.*  from gmc_monitor_position as p  
                    WHERE p.company_id='{$request['company_id']}' and p.position_id='{$request['position_id']}'");

        if($deviceOne){
            $this->error = 0;
            $this->errortip = "位置信息获取成功！";
            $this->result = $deviceOne;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "位置信息获取失败！";
            $this->result = array();
            return false;
        }
    }

    //999018->监控->（集团）监控位置编辑 -- 97
    function editMonitorPositionOneApi($request){
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

        $data = array();
        if(isset($request['position_name']) && $request['position_name'] != '') {
            if($this->DataControl->selectOne("select * from gmc_monitor_position WHERE position_name = '{$request['position_name']}' and company_id = '{$request['company_id']}' and position_id <> '{$request['position_id']}' ")){
                $this->error = 1;
                $this->errortip = "位置名称不能重复！";
                $this->result = array();
                return false;
            }

            $data['position_name'] = $request['position_name'];
        }
        if(isset($request['position_desc']) && $request['position_desc'] != '') {
            $data['position_desc'] = $request['position_desc'];
        }
        $positionid = $this->DataControl->updateData('gmc_monitor_position',"position_id='{$request['position_id']}' and company_id='{$request['company_id']}'",$data);

        if($positionid){
            $this->error = 0;
            $this->errortip = "位置编辑成功！";
            $this->result = $data;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "位置编辑失败！";
            $this->result = array();
            return false;
        }
    }

    //999018->监控->（集团）监控位置删除 -- 97
    function delMonitorPositionOneAction($request){
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

        $mateOne = $this->DataControl->selectOne("select mate_id from gmc_monitor_device_mate WHERE company_id = '{$request['company_id']}' and position_id = '{$request['position_id']}' ");
        if($mateOne){
            $this->error = 1;
            $this->errortip = "位置已被使用不可删除！";
            $this->result = array();
            return false;
        }

        $positionOne = $this->DataControl->delData("gmc_monitor_position","position_id = '{$request['position_id']}' and company_id = '{$request['company_id']}' ");

        if($positionOne){
            $this->error = 0;
            $this->errortip = "位置删除成功！";
            $this->result = array();
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "位置删除失败！";
            $this->result = array();
            return false;
        }
    }

    //999019->监控->（集团）监控位置角色权限设置 -- 97
    function editPositionUserfuncAction($request){
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

        $postroleArray = json_decode(stripslashes($request['postrolejson']),true);
        $postrolestr = "'-1'";
        if(is_array($postroleArray)){
            $allnum = 0;//全部角色数
            $successnum = 0;//成功数
            $failnum = 0;//失败数
            foreach ($postroleArray as $postroleVar){
                $allnum++;
                //此次选择的角色
                $postrolestr .= ",'".$postroleVar['postpart_id']."'";

                //位置角色权限更新
                $userfuncOne = $this->DataControl->selectOne("select u.userfunc_id from gmc_monitor_position_userfunc as u WHERE u.company_id = '{$request['company_id']}' and u.postpart_id = '{$postroleVar['postpart_id']}' and u.position_id = '{$request['position_id']}' ");

                $dataOne = array();
                $dataOne['userfunc_videotype'] = $postroleVar['look'];
                if($userfuncOne){
                    //有设备更新数据
                    if($this->DataControl->updateData('gmc_monitor_position_userfunc',"userfunc_id='{$userfuncOne['userfunc_id']}' and company_id='{$request['company_id']}'",$dataOne)){
                        $successnum++;
                    }else{
                        $failnum++;
                    }
                }else{
                    //角色添加数据
                    $dataOne['company_id'] = $request['company_id'];
                    $dataOne['position_id'] = $request['position_id'];

                    $dataOne['postpart_id'] = $postroleVar['postpart_id'];
                    if($this->DataControl->insertData('gmc_monitor_position_userfunc',$dataOne)){
                        $successnum++;
                    }else{
                        $failnum++;
                    }
                }
            }
//            $postrolestr = substr($postrolestr,0,-1);
            $deluserfunc = $this->DataControl->delData("gmc_monitor_position_userfunc","position_id = '{$request['position_id']}' and company_id = '{$request['company_id']}' and postpart_id not in ({$postrolestr})  ");

            if($allnum == $successnum){
                $this->error = 0;
                $this->errortip = "角色设置完成";
                $this->result = array();
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "【角色设置】{$successnum}个已完成,{$failnum}个未完成";
                $this->result = array();
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "角色设置失败！";
            $this->result = array();
            return false;
        }
    }

    //999019->监控->校园角色（以及位置对应的权限） -- 97
    function getPostpartUserfuncApi($request){
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

        $datawhere = " 1 and p.company_id = '{$request['company_id']}' and p.school_id = '0' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (p.postpart_name like '%{$request['keyword']}%')";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " SELECT p.postpart_id,p.postpart_name,u.userfunc_videotype,u.postpart_id as ischoice
            FROM smc_school_postpart AS p 
            LEFT JOIN gmc_monitor_position_userfunc as u ON (u.company_id = p.company_id and u.postpart_id = p.postpart_id and u.position_id = '{$request['position_id']}')
            WHERE {$datawhere} 
            ORDER BY p.postpart_id DESC    
            LIMIT {$pagestart},{$num}";
        $PostroleList = $this->DataControl->selectClear($sql);
        if(is_array($PostroleList)){
            foreach ($PostroleList as &$PostroleVar){
                $PostroleVar['ischoice'] = ($PostroleVar['ischoice']>0)?'1':'0';
                if($PostroleVar['userfunc_videotype'] != ''){
                    $PostroleVar['userfunc_videotype'] = explode(',',$PostroleVar['userfunc_videotype']);
                }else{
                    $PostroleVar['userfunc_videotype'] = array();
                }
            }
        }

        if($PostroleList){
            $this->error = 0;
            $this->errortip = "校园角色列表权限获取成功";
            $this->result = $PostroleList;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无校园角色列表权限信息";
            $this->result = $PostroleList;
            return false;
        }
    }

    //999019->监控->位置校园角色权限 -- 97
    function getPositionPostpartUserfuncApi($request){
        if(!isset($request['position_id']) || $request['position_id'] == '' ){
            $this->error = 1;
            $this->errortip = "位置ID不能为空！";
            $this->result = array();
            return false;
        }

        $datawhere = " 1 and u.company_id = '{$request['company_id']}' and u.position_id = '{$request['position_id']}' and p.school_id = '0' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (p.postpart_name like '%{$request['keyword']}%')";
        }
        if(isset($request['userfunc_videotype']) && $request['userfunc_videotype'] != ''){
            $datawhere .= " and (u.userfunc_videotype = '{$request['userfunc_videotype']}' or u.userfunc_videotype like '%{$request['userfunc_videotype']}%' )";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " SELECT p.postpart_id,p.postpart_name,
                (SELECT t.position_desc FROM gmc_monitor_position as t WHERE t.position_id = u.position_id and t.company_id = u.company_id) as position_desc 
                ,u.userfunc_videotype  
            FROM gmc_monitor_position_userfunc AS u  
            LEFT JOIN smc_school_postpart as p ON (u.company_id = p.company_id and u.postpart_id = p.postpart_id )
            WHERE {$datawhere} 
            ORDER BY p.postpart_id DESC    
            LIMIT {$pagestart},{$num}";
        $PostroleList = $this->DataControl->selectClear($sql);
        if(is_array($PostroleList)){
            foreach ($PostroleList as &$PostroleVar){
                if($PostroleVar['userfunc_videotype'] != ''){
                    $videotype = explode(',',$PostroleVar['userfunc_videotype']);
                    if($videotype){
                        $data = array();
                        foreach ($videotype as $videotypevar){
                            $videotypestr = '';
                            if($videotypevar == '1'){
                                $videotypestr = '观看回访';
                            }elseif($videotypevar == '0'){
                                $videotypestr = '实时监控';
                            }
                            $data[]=$videotypestr;
                        }
                        $PostroleVar['userfunc_videotype'] = implode(" ",$data);
                    }else{
                        $PostroleVar['userfunc_videotype'] = '';
                    }
                }else{
                    $PostroleVar['userfunc_videotype'] = '';
                }
            }
        }

        if($PostroleList){
            $this->error = 0;
            $this->errortip = "位置校园角色权限获取成功";
            $this->result = $PostroleList;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无位置校园角色权限信息";
            $this->result = array();
            return false;
        }
    }

    //999020>监控->（集团）学校列表
    function getMonitorSchoolList($request){
        $datawhere=" 1 and p.company_id = '{$request['company_id']}' and p.school_istest = '0' and p.school_isclose = '0' ";

        if(!isset($request['isall']) || $request['isall'] != '1') {
            $datawhere .=" and p.school_monitorcode <> '' and p.school_monitorcode is not null ";
        }elseif($request['isall'] == '1'){
            $datawhere .=" and p.school_monitorcode = ''  ";
        }

        //关键词
        if(isset($request['keyword']) && $request['keyword'] != ''){
            $datawhere .= " and (p.school_shortname like '%{$request['keyword']}%' or p.school_cnname like '%{$request['keyword']}%' or p.school_monitorcode like '%{$request['keyword']}%' or p.school_branch like '%{$request['keyword']}%' )";
        }

        if(isset($request['p']) && $request['p'] !== ''){
            $page = $request['p'];
        }else{
            $page = '1';
        }
        if(isset($request['num']) && $request['num'] !== ''){
            $num = $request['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = " SELECT p.school_id,p.company_id,p.school_branch,p.school_shortname,p.school_cnname,p.school_monitorcode   
                FROM smc_school AS p 
                WHERE {$datawhere} 
                ORDER BY p.school_id DESC";

        if(!isset($request['isall']) || $request['isall'] != '1') {
            $sql .= " LIMIT {$pagestart},{$num}";
        }

        $dataList = $this->DataControl->selectClear($sql);

        if(isset($request['is_count']) && $request['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("SELECT count(p.school_id) as datanum
                    FROM smc_school AS p 
                    WHERE {$datawhere}   ");
            $count = $all_num['datanum'] + 0;
        }else{
            $count = 0;
        }

        $result = array();
        $result["datalist"] = is_array($dataList)?$dataList:array();
        $result["count"] = $count;

        if($dataList){
            $this->error = 0;
            $this->errortip = "学校信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "暂无学校信息信息";
            $this->result = $result;
            return false;
        }
    }

    //999021->监控->（集团）添加设备码 --97
    function addMonitorCodeAction($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "请先选择学校！";
            $this->result = array();
            return false;
        }

        $school_monitorcode = array();
        if(!isset($request['monitorcodestr']) || $request['monitorcodestr'] == '' ){
            $this->error = 1;
            $this->errortip = "设备码不能为空！";
            $this->result = array();
            return false;
        }else{
            $school_monitorcode = json_decode(stripslashes($request['monitorcodestr']),true);
        }

        if($school_monitorcode){
            $monitorcode = array();
            foreach($school_monitorcode as $ilinkcaseVar){
                $monitorcode[] = $ilinkcaseVar['school_monitorcode'];
            }
            if (count($monitorcode) != count(array_unique($monitorcode))) {
                $this->error = 1;
                $this->errortip = "监控交换机设备码有重复项，请检查后提交！";
                $this->result = array();
                return false;
            }

            foreach ($school_monitorcode as $school_monitorcodevar){
                $school_one = $this->DataControl->getFieldOne('smc_school', 'school_id,school_shortname', "school_monitorcode like '%{$school_monitorcodevar['school_monitorcode']}%'");

                $schooldata = array();
                $schooldata['school_shortname'] = $school_one['school_shortname'];
                $schooldata['school_monitorcode'] = $school_monitorcodevar['school_monitorcode'];
                if($school_one){
                    $this->error = 1;
                    $this->errortip = "该设备码已添加，不可重复添加！";
                    $this->result = $schooldata;
                    return false;
                }
            }
        }

        $school_monitorcodestr = '';
        if($school_monitorcode){
            $ilinkidArray = array();
            foreach($school_monitorcode as $school_monitorcodeVar){
                $ilinkidArray[] = $school_monitorcodeVar['school_monitorcode'];
            }
            $school_monitorcodestr = implode(",",$ilinkidArray);
        }
        $schoolone = $this->DataControl->getFieldOne('smc_school', 'school_id,school_shortname', "school_id = '{$request['school_id']}'");

        $data = array();
        $data['school_id'] = $schoolone['school_id'];
        $data['school_shortname'] = $schoolone['school_shortname'];
        $data['school_monitorcode'] = $school_monitorcodestr;
        $schoolid = $this->DataControl->updateData("smc_school", "school_id = '{$request['school_id']}'", $data);

        if($schoolid){
            $this->error = 0;
            $this->errortip = "学校设备码创建成功！";
            $this->result = $data;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "学校设备码创建失败！";
            $this->result = array();
            return false;
        }
    }

    //999022->监控->（集团）学校设备码详情 --97
    function getMonitorCodeOneApi($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "学校ID不能为空！";
            $this->result = array();
            return false;
        }

        $schoolOne = $this->DataControl->selectOne("select p.school_id,p.company_id,p.school_branch,p.school_shortname,p.school_monitorcode
                    from smc_school as p   
                    WHERE p.company_id='{$request['company_id']}' and p.school_id='{$request['school_id']}' limit 0,1 ");

        $school_monitorcodelist = explode(",",$schoolOne['school_monitorcode']);
        $monitorcodelist = array();
        if($school_monitorcodelist){
            foreach ($school_monitorcodelist as $school_monitorcodevar){
                $dataOne = $this->DataControl->selectOne("SELECT d.device_id FROM gmc_monitor_device as d WHERE d.school_id='{$request['school_id']}' and d.company_id='{$request['company_id']}' and  d.device_deviceserial = '{$school_monitorcodevar}' limit 0,1");
                $monitorcodeOne = array();
                $monitorcodeOne['school_monitorcode'] = $school_monitorcodevar;
                $monitorcodeOne['issynchro'] = ($dataOne['device_id']>0)?1:0;

                $monitorcodelist[] = $monitorcodeOne;
            }
        }
        $schoolOne['monitorcodelist'] = $monitorcodelist;

        if($schoolOne){
            $this->error = 0;
            $this->errortip = "学校设备信息获取成功！";
            $this->result = $schoolOne;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "学校设备信息获取失败！";
            $this->result = array();
            return false;
        }
    }

    //999023->监控->（集团）学校设备码编辑 -- 97
    function editMonitorCodeOneAction($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "请先选择学校！";
            $this->result = array();
            return false;
        }

        $school_monitorcode = array();
        if(!isset($request['monitorcodestr']) || $request['monitorcodestr'] == '' ){
            $this->error = 1;
            $this->errortip = "设备码不能为空！";
            $this->result = array();
            return false;
        }else{
            $school_monitorcode = json_decode(stripslashes($request['monitorcodestr']),true);
        }
        if($school_monitorcode){

            $monitorcode = array();
            foreach($school_monitorcode as $ilinkcaseVar){
                $monitorcode[] = $ilinkcaseVar['school_monitorcode'];
            }
            if (count($monitorcode) != count(array_unique($monitorcode))) {
                $this->error = 1;
                $this->errortip = "监控交换机设备码有重复项，请检查后提交！";
                $this->result = array();
                return false;
            }

            foreach ($school_monitorcode as $school_monitorcodevar){
                $school_one = $this->DataControl->getFieldOne('smc_school', 'school_id,school_shortname', "school_monitorcode like '%{$school_monitorcodevar['school_monitorcode']}%' and school_id <> '{$request['school_id']}' ");

                $schooldata = array();
                $schooldata['school_shortname'] = $school_one['school_shortname'];
                $schooldata['school_monitorcode'] = $school_monitorcodevar['school_monitorcode'];
                if($school_one){
                    $this->error = 1;
                    $this->errortip = "该设备码已添加，不可重复添加！";
                    $this->result = $schooldata;
                    return false;
                }
            }
        }

        $school_monitorcodestr = '';
        if($school_monitorcode){
            $ilinkidArray = array();
            foreach($school_monitorcode as $school_monitorcodeVar){
                $ilinkidArray[] = $school_monitorcodeVar['school_monitorcode'];
            }
            $school_monitorcodestr = implode(",",$ilinkidArray);
        }
        $schoolone = $this->DataControl->getFieldOne('smc_school', 'school_id,school_shortname', "school_id = '{$request['school_id']}'");

        $data = array();
        $data['school_id'] = $schoolone['school_id'];
        $data['school_shortname'] = $schoolone['school_shortname'];
        $data['school_monitorcode'] = $school_monitorcodestr;
        $schoolid = $this->DataControl->updateData("smc_school", "school_id = '{$request['school_id']}'", $data);

        if($schoolid){
            $this->error = 0;
            $this->errortip = "编辑成功！";
            $this->result = $data;
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "编辑失败！";
            $this->result = array();
            return false;
        }
    }

    //999024->监控->（集团）学校设备码删除 -- 97
    function delMonitorCodeOneAction($request){
        if(!isset($request['school_id']) || $request['school_id'] == '' ){
            $this->error = 1;
            $this->errortip = "请先选择学校！";
            $this->result = array();
            return false;
        }

        $mateOne = $this->DataControl->selectOne("select s.school_monitorcode,
                  (SELECT d.device_id FROM gmc_monitor_device as d WHERE s.school_monitorcode LIKE concat ('%',d.device_deviceserial,'%') limit 0,1) as device_id   
                  from smc_school as s   
                  WHERE s.school_id = '{$request['school_id']}' and s.company_id = '{$request['company_id']}' ") ;
        if($mateOne['device_id'] > 0){
            $this->error = 1;
            $this->errortip = "设备码已被使用不可删除！";
            $this->result = array();
            return false;
        }

        $positionOne = $this->DataControl->updateData("smc_school","school_id = '{$request['school_id']}' and company_id = '{$request['company_id']}' ", array('school_monitorcode'=>''));

        if($positionOne){
            $this->error = 0;
            $this->errortip = "删除成功！";
            $this->result = array();
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "删除失败！";
            $this->result = array();
            return false;
        }
    }

}
