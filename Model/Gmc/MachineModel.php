<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class MachineModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }
    //get
    public function curlGet($url,$Authorization){
        $header  = array(
            'Authorization:'.$Authorization,
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }
    //post
    public function curlPost($url,$post_data = array(),$Authorization){
        $header  = array(
            'Authorization:'.$Authorization,
        );
        if (is_array($post_data))
        {
            $post_data = http_build_query($post_data, null, '&');
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch , CURLOPT_URL , $url);
        curl_setopt($ch , CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch , CURLOPT_POST, 1);
        curl_setopt($ch , CURLOPT_POSTFIELDS, $post_data);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    //考勤机列表 -- 97
    function getMachineList($paramArray)
    {
        $datawhere=" 1 and m.company_id = '{$paramArray['company_id']}' and m.machine_state = '1' ";
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (v.list_name like '%{$paramArray['keyword']}%' or m.machine_name like '%{$paramArray['keyword']}%' or m.machine_code like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
            $datawhere .= " and m.school_id = '{$paramArray['school_id']}' ";
        }
        //是否在线 isinterval 1 在 0 不在
        $having = ' ';
        if(isset($paramArray['isinterval']) && $paramArray['isinterval'] == '1'){
            $having .= " HAVING if((UNIX_TIMESTAMP()-last_heartbeat_creattime)>360,1,0) = 1 ";
        }elseif(isset($paramArray['isinterval']) && $paramArray['isinterval'] == '0'){
            $having .= " HAVING if((UNIX_TIMESTAMP()-last_heartbeat_creattime)>360,1,0) = 0 ";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

//        $sql = " SELECT m.machine_id,m.machine_brand,m.machine_name,m.machine_code,m.machine_creattime,m.machine_state,s.school_shortname,v.list_name as machine_brand_name,
//                (select count(DISTINCT(t.student_id)) from gmc_machine_stuportrait as t where t.school_id = m.school_id ) as  portraitnum,
//                (select h.heartbeat_creattime from gmc_machine_heartbeat as h where h.school_id = m.school_id and h.machine_id = m.machine_id order by h.heartbeat_id desc limit 0,1) as last_heartbeat_creattime,
//                (select h.heartbeat_edition from gmc_machine_heartbeat as h where h.school_id = m.school_id and h.machine_id = m.machine_id order by h.heartbeat_id desc limit 0,1) as heartbeat_edition
//                FROM gmc_machine as m
//                left join smc_school as s ON s.school_id = m.school_id
//                left join cms_variablelist as v ON (v.variable_id = '4' and v.list_parameter = m.machine_brand)
//                WHERE {$datawhere}
//                {$having}
//                ORDER BY s.school_id DESC,m.machine_id desc
//                ";

        $sql = " SELECT m.machine_id,m.machine_brand,m.machine_name,m.machine_code,m.machine_creattime,m.machine_state,s.school_shortname,v.list_name as machine_brand_name,
                (select count(DISTINCT(t.student_id)) from gmc_machine_stuportrait as t where t.school_id = m.school_id ) as  portraitnum 
                ,(select h.heartbeat_creattime from gmc_machine_heartbeat as h where h.heartbeat_id=x.heartbeat_id) as last_heartbeat_creattime
                ,(select h.heartbeat_edition from gmc_machine_heartbeat as h where h.heartbeat_id=x.heartbeat_id) as heartbeat_edition 
                FROM gmc_machine as m
                left join smc_school as s ON s.school_id = m.school_id 
                left join cms_variablelist as v ON (v.variable_id = '4' and v.list_parameter = m.machine_brand) 
                left join (select school_id,machine_id,max(heartbeat_id) as heartbeat_id
                        from gmc_machine_heartbeat 
                        group by school_id,machine_id) as x on x.school_id = m.school_id and x.machine_id = m.machine_id 
                WHERE {$datawhere} 
                {$having}
                ORDER BY s.school_id DESC,m.machine_id desc    
                ";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $intervaltime = time()-$dateexcelvar['last_heartbeat_creattime'];
                    $dateexcelvar['isinterval']= ($intervaltime > 360)?1:0;//是否离线  1 离线  0 没有离线

                    if($dateexcelvar['last_heartbeat_creattime'] > 0 && $dateexcelvar['isinterval'] == '1') {
                        $fen = floor($intervaltime / 60);
                        $miao = sprintf("%.2f", ($intervaltime % 60 / 60));
                        $dateexcelvar['last_heartbeat_creattime'] = $fen + $miao . "min";
                    }else{
                        $dateexcelvar['last_heartbeat_creattime'] = '--';
                    }

                    $datearray = array();
                    $datearray['machine_brand_name'] = $dateexcelvar['machine_brand_name'];
                    $datearray['machine_name'] = $dateexcelvar['machine_name'];
                    $datearray['machine_code'] = $dateexcelvar['machine_code'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['portraitnum'] = $dateexcelvar['portraitnum'];
                    $datearray['heartbeat_edition'] = $dateexcelvar['heartbeat_edition'];
                    $datearray['isinterval'] = $dateexcelvar['isinterval'];
                    $datearray['last_heartbeat_creattime'] = $dateexcelvar['last_heartbeat_creattime'];
                    $datearray['machine_creattime'] = date("Y-m-d",$dateexcelvar['machine_creattime']);
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('考勤机品牌','考勤机名称','考勤机序列号','绑定学校名称','人脸采集数量','版本','在线状态','掉线时间','添加时间'));

            $excelfileds = array('machine_brand_name','machine_name','machine_code','school_shortname','portraitnum','heartbeat_edition','isinterval','last_heartbeat_creattime','machine_creattime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("考勤机管理列表.xlsx"));
            exit;
        }

        $sql .= " LIMIT {$pagestart},{$num} ";
        $MachineList = $this->DataControl->selectClear($sql);

        //统计总数
        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $all_num = $this->DataControl->selectClear("SELECT m.machine_id
                    ,(select h.heartbeat_creattime from gmc_machine_heartbeat as h where h.heartbeat_id=x.heartbeat_id) as last_heartbeat_creattime
                    FROM gmc_machine as m 
                    left join smc_school as s ON s.school_id = m.school_id 
                    left join cms_variablelist as v ON (v.variable_id = '4' and v.list_parameter = m.machine_brand)
                    left join (select school_id,machine_id,max(heartbeat_id) as heartbeat_id
                        from gmc_machine_heartbeat 
                        group by school_id,machine_id) as x on x.school_id = m.school_id and x.machine_id = m.machine_id 
                    WHERE {$datawhere} 
                    {$having} ");
            $allnum = is_array($all_num)?count($all_num):0;
        }else{
            $allnum = 0;
        }

        $fieldname = array('machine_brand_name','machine_name','machine_code','school_shortname','portraitnum','heartbeat_edition','last_heartbeat_creattime','machine_creattime');
        $fieldstring = array('考勤机品牌','考勤机名称','考勤机序列号','绑定学校名称','人脸采集数量','版本','在线状态，掉线时间','添加时间');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        if ($MachineList) {
            foreach ($MachineList as &$MachineVar){
                $MachineVar['machine_creattime'] = date("Y-m-d",$MachineVar['machine_creattime']);

                $intervaltime = time()-$MachineVar['last_heartbeat_creattime'];
                $MachineVar['isinterval']= ($intervaltime > 360)?1:0;//是否离线  1 离线  0 没有离线

                if($MachineVar['last_heartbeat_creattime'] > 0 && $MachineVar['isinterval'] == '1') {
                    $fen = floor($intervaltime / 60);
                    $miao = sprintf("%.2f", ($intervaltime % 60 / 60));
                    $MachineVar['last_heartbeat_creattime'] = $fen + $miao . "min";
                }else{
                    $MachineVar['last_heartbeat_creattime'] = '--';
                }
            }

            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = $allnum;
            $result['data']['datalist'] = $MachineList;

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = 0;
            $result['data']['datalist'] = array();

            $this->error = 1;
            $this->errortip = "暂未绑定考勤机";
            $this->result = $result;
            return false;
        }
    }

    //添加考勤机
    function getMachineBrand($paramArray)
    {
        $Brandlist = $this->DataControl->selectClear("select list_parameter as brand_id,list_name as brand_name from cms_variablelist where variable_id = '4' ");

        $result = array();
        $result['list'] = $Brandlist;

        $this->error = 0;
        $this->errortip = "品牌列表获取成功！";
        $this->result = $result;
        return false;
    }

    //添加考勤机
    function addMachineAction($paramArray)
    {
        if($paramArray['machine_brand'] == ''){
            $this->error = 1;
            $this->errortip = "考勤机品牌不能为空";
            return false;
        }elseif($paramArray['machine_name'] == ''){
            $this->error = 1;
            $this->errortip = "考勤机名称不能为空";
            return false;
        }elseif($paramArray['machine_code'] == ''){
            $this->error = 1;
            $this->errortip = "考勤机序列号不能为空";
            return false;
        }elseif($paramArray['school_id'] == ''){
            $this->error = 1;
            $this->errortip = "学校不能为空";
            return false;
        }elseif($this->DataControl->selectOne("select machine_id from gmc_machine where machine_code = '{$paramArray['machine_code']}' and machine_state = '1' ")){
            $this->error = 1;
            $this->errortip = "考勤机序列号已被绑定";
            return false;
        }else{
            $machinedata = array();
            $machinedata['company_id'] = $paramArray['company_id'];
            $machinedata['school_id'] = $paramArray['school_id'];
            $machinedata['machine_name'] = $paramArray['machine_name'];
            $machinedata['machine_brand'] = $paramArray['machine_brand'];
            $machinedata['machine_code'] = $paramArray['machine_code'];
            $machinedata['machine_issynchron'] = 1;//是否开启同步 0 未开启 1 已开启
            $machinedata['machine_state'] = 1;//使用状态 0 未使用 1 使用中
            $machinedata['machine_creattime'] = time();
            $machineID = $this->DataControl->insertData("gmc_machine",$machinedata);
            if($machineID > 1){
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "考勤设备管理->新增考勤机", '添加考勤机', dataEncode($paramArray));
                $this->error = 0;
                $this->errortip = "信息添加成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "信息添加失败";
                return false;
            }
        }
    }

    //解绑 和 批量解绑  (假删除）
    function editSomeMachineApi($paramArray){
        $machineids = json_decode(stripslashes($paramArray['machineids']),true);
        if(!$machineids){
            $this->error = 1;
            $this->errortip = "设备ID不能为空！";
            $this->result = array();
            return false;
        }

        if($machineids){
            foreach ($machineids as $machineidsvar){
                $data = array();
                $data['machine_state'] = 0;
                $data['machine_updatetime'] = time();
                $machineid = $this->DataControl->updateData('gmc_machine',"machine_id='{$machineidsvar}' and company_id='{$paramArray['company_id']}'",$data);
            }
        }

        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "考勤设备管理->解绑考勤机", '解绑考勤机', dataEncode($paramArray));

        $this->error = 0;
        $this->errortip = "解绑成功！";
        $this->result = array();
        return true;
    }

    //考勤机出勤记录 -- 学生的 -- 97
    function getMachineStucardlog($paramArray,$isSchool='')
    {
        $datawhere=" 1 and m.company_id = '{$paramArray['company_id']}' and m.cardlog_state = '1' and m.cardlog_type = '1' and m.student_id > 1 ";
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (t.student_cnname like '%{$paramArray['keyword']}%' or t.student_enname like '%{$paramArray['keyword']}%' or t.student_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
            $datawhere .= " and m.school_id = '{$paramArray['school_id']}' ";
        }
        if(isset($paramArray['student_branch']) && $paramArray['student_branch'] != ''){
            $datawhere .= " and t.student_branch = '{$paramArray['student_branch']}' ";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $starttime=strtotime($paramArray['starttime']);
            $datawhere .= " and m.cardlog_clocktime >= '{$starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $endtime=strtotime($paramArray['endtime'])+86399;
            $datawhere .= " and m.cardlog_clocktime <= '{$endtime}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = " SELECT t.student_cnname,t.student_enname,t.student_branch,s.school_shortname,s.school_branch,m.cardlog_creattime,m.cardlog_clocktime,m.cardlog_faceimg,m.cardlog_similarity 
                FROM gmc_machine_stucardlog as m
                left join smc_student as t ON t.student_id = m.student_id 
                left join smc_school as s ON s.school_id = m.school_id 
                WHERE {$datawhere} 
                ORDER BY m.cardlog_clocktime desc   ";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = 1;
                $this->errortip = "无数据";
                return false;
            }
            if (count($dateexcelarray) > 20000) {
                $this->error = 1;
                $this->errortip = "时间范围内数量超出两万条数据，禁止导出！";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $dateexcelvar['student_cnname'] = $dateexcelvar['student_enname']?$dateexcelvar['student_cnname'].'/'.$dateexcelvar['student_enname']:$dateexcelvar['student_cnname'];

                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
//                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    $datearray['cardlog_daytime'] = date("Y-m-d",$dateexcelvar['cardlog_clocktime']);
                    $datearray['cardlog_clocktime'] = date("H:i",$dateexcelvar['cardlog_clocktime']);
                    $datearray['cardlog_similarity'] = $dateexcelvar['cardlog_similarity']?sprintf('%.2f',$dateexcelvar['cardlog_similarity']):'--';
                    $datearray['cardlog_faceimg'] = $dateexcelvar['cardlog_faceimg']?$dateexcelvar['cardlog_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90':'';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('学员姓名', '学员编号', '校区名称', '校区编号',  '考勤日期', '考勤时间', '相似度', '考勤图片'));
            $excelfileds = array('student_cnname', 'student_branch', 'school_shortname', 'school_branch',  'cardlog_daytime', 'cardlog_clocktime','cardlog_similarity', 'cardlog_faceimg');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("学生出勤记录.xlsx"));
            exit;
        }
        $sql .= " LIMIT {$pagestart},{$num} ";
        $MachineList = $this->DataControl->selectClear($sql);

        //统计总数
        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $all_num = $this->DataControl->selectOne("
                SELECT count(1) as datanum 
                FROM gmc_machine_stucardlog as m 
                left join smc_student as t ON t.student_id = m.student_id 
                left join smc_school as s ON s.school_id = m.school_id 
                WHERE {$datawhere}");

            $allnum = $all_num['datanum'] + 0;
        }else{
            $allnum = 0;
        }

        if($isSchool == '1'){// 是否学校获取页面数据 1 学校
            $fieldstring = array('student_cnname', 'student_branch',  'cardlog_daytime', 'cardlog_clocktime','cardlog_similarity', 'cardlog_faceimg');
            $fieldname = array('学员姓名', '学员编号',  '考勤日期', '考勤时间', '相似度', '考勤图片');
            $fieldcustom = array("1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1");
        }else {
            $fieldname = array('student_cnname', 'student_branch', 'school_shortname', 'school_branch',  'cardlog_daytime', 'cardlog_clocktime','cardlog_similarity', 'cardlog_faceimg');
            $fieldstring = array('学员姓名', '学员编号', '校区名称', '校区编号',  '考勤日期', '考勤时间', '相似度', '考勤图片');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        }

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if($field[$i]["fieldstring"] == 'cardlog_faceimg' || $field[$i]["fieldname"] == 'cardlog_faceimg'){
                $field[$i]["isqrcode"] = 1;
            }
        }

        if ($MachineList) {
            foreach ($MachineList as &$MachineVar){
                $MachineVar['student_cnname'] = $MachineVar['student_enname']?$MachineVar['student_cnname'].'/'.$MachineVar['student_enname']:$MachineVar['student_cnname'];

                $MachineVar['cardlog_similarity'] = $MachineVar['cardlog_similarity']?sprintf('%.2f',$MachineVar['cardlog_similarity']):'--';

                $MachineVar['cardlog_daytime'] = date("Y-m-d",$MachineVar['cardlog_clocktime']);
                $MachineVar['cardlog_clocktime'] = date("H:i",$MachineVar['cardlog_clocktime']);
                $MachineVar['cardlog_timeTwo'] = $MachineVar['cardlog_clocktime'];
                $MachineVar['cardlog_faceimg'] = $MachineVar['cardlog_faceimg']?$MachineVar['cardlog_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90':'';
            }

            if($isSchool == '1') {// 是否学校获取页面数据 1 学校
                $result = array();
                $result["field"] = $field;
                $result['allnum'] = $allnum;
                $result['list'] = $MachineList;
            }else {
                $result = array();
                $result["field"] = $field;
                $result['data']['allnum'] = $allnum;
                $result['data']['datalist'] = $MachineList;
            }

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        }else{
            if($isSchool == '1') {// 是否学校获取页面数据 1 学校
                $result = array();
                $result["field"] = $field;
                $result['allnum'] = 0;
                $result['list'] = array();
            }else {
                $result = array();
                $result["field"] = $field;
                $result['data']['allnum'] = 0;
                $result['data']['datalist'] = array();
            }

            $this->error = 1;
            $this->errortip = "暂未获取到出勤记录";
            $this->result = $result;
            return false;
        }
    }

    //考勤机出勤记录 -- 教师的 -- 97
    function getMachineStaffercardlog($paramArray,$isSchool='')
    {
        $datawhere=" 1 and m.company_id = '{$paramArray['company_id']}' and m.cardlog_state = '1' and m.cardlog_type = '1' and m.staffer_id > 1 ";
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (t.staffer_cnname like '%{$paramArray['keyword']}%' or t.staffer_enname like '%{$paramArray['keyword']}%' or t.staffer_branch like '%{$paramArray['keyword']}%')";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
            $datawhere .= " and m.school_id = '{$paramArray['school_id']}' ";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $starttime=strtotime($paramArray['starttime']);
            $datawhere .= " and m.cardlog_clocktime >= '{$starttime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $endtime=strtotime($paramArray['endtime'])+86399;
            $datawhere .= " and m.cardlog_clocktime <= '{$endtime}'";
        }

        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

//        $sql = " SELECT t.staffer_cnname,t.staffer_enname,t.staffer_branch,s.school_shortname,s.school_branch,m.cardlog_creattime,m.cardlog_clocktime,m.cardlog_faceimg
//                FROM gmc_machine_staffcardlog as m
//                left join smc_staffer as t ON t.staffer_id = m.staffer_id
//                left join smc_school as s ON s.school_id = m.school_id
//                WHERE {$datawhere}
//                ORDER BY m.cardlog_clocktime desc  ";

        $sql = "
            SELECT * from 
            ( 
                SELECT x.*,
                    FROM_UNIXTIME(x.cardlog_clocktime, '%Y-%m-%d') as cardlog_clocktime_date 
                FROM(
                    SELECT m.staffer_id,t.staffer_cnname,t.staffer_enname,t.staffer_branch,s.school_shortname,s.school_branch,m.cardlog_creattime,m.cardlog_clocktime,m.cardlog_faceimg,m.cardlog_similarity  
                    FROM gmc_machine_staffcardlog as m 
                    left join smc_staffer as t ON t.staffer_id = m.staffer_id 
                    left join smc_school as s ON s.school_id = m.school_id 
                    WHERE {$datawhere} 
                    ORDER BY m.cardlog_clocktime ASC 
                ) as x
                group by x.staffer_id ,FROM_UNIXTIME(x.cardlog_clocktime, '%Y-%m-%d')
            
                UNION
            
                SELECT y.*,
                    FROM_UNIXTIME(y.cardlog_clocktime, '%Y-%m-%d') as cardlog_clocktime_date 
                FROM(
                    SELECT m.staffer_id,t.staffer_cnname,t.staffer_enname,t.staffer_branch,s.school_shortname,s.school_branch,m.cardlog_creattime,m.cardlog_clocktime,m.cardlog_faceimg,m.cardlog_similarity 
                    FROM gmc_machine_staffcardlog as m 
                    left join smc_staffer as t ON t.staffer_id = m.staffer_id 
                    left join smc_school as s ON s.school_id = m.school_id 
                    WHERE {$datawhere} 
                    ORDER BY m.cardlog_clocktime DESC
                ) as y
                group by y.staffer_id ,FROM_UNIXTIME(y.cardlog_clocktime, '%Y-%m-%d')
            
            ) as y ORDER BY y.cardlog_clocktime_date desc,y.cardlog_clocktime desc 
            ";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = 1;
                $this->errortip = "无数据";
                return false;
            }
            if (count($dateexcelarray) > 20000) {
                $this->error = 1;
                $this->errortip = "时间范围内数量超出两万条数据，禁止导出！";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $dateexcelvar['staffer_cnname'] = $dateexcelvar['staffer_enname']?$dateexcelvar['staffer_cnname'].'/'.$dateexcelvar['staffer_enname']:$dateexcelvar['staffer_cnname'];

                    $datearray = array();
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['staffer_branch'] = $dateexcelvar['staffer_branch'];
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['cardlog_daytime'] = date("Y-m-d",$dateexcelvar['cardlog_clocktime']);
                    $datearray['cardlog_clocktime'] = date("H:i",$dateexcelvar['cardlog_clocktime']);
                    $datearray['cardlog_similarity'] = $dateexcelvar['cardlog_similarity']?sprintf('%.2f',$dateexcelvar['cardlog_similarity']):'--';
                    $datearray['cardlog_faceimg'] = $dateexcelvar['cardlog_faceimg']?$dateexcelvar['cardlog_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90':'';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('教师姓名', '教师编号', '校区名称', '校区编号','考勤日期', '考勤时间', '相似度', '考勤图片'));
            $excelfileds = array('staffer_cnname', 'staffer_branch', 'school_shortname', 'school_branch', 'cardlog_daytime', 'cardlog_clocktime','cardlog_similarity', 'cardlog_faceimg');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("教师出勤记录.xlsx"));
            exit;
        }
        $sql .= " LIMIT {$pagestart},{$num} ";
        $MachineList = $this->DataControl->selectClear($sql);

        //统计总数
        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $countsql = "SELECT count(y.staffer_id) as datanum from 
            ( 
                SELECT x.*,
                    FROM_UNIXTIME(x.cardlog_clocktime, '%Y-%m-%d') as cardlog_clocktime_date 
                FROM(
                    SELECT m.staffer_id,t.staffer_cnname,t.staffer_enname,t.staffer_branch,s.school_shortname,s.school_branch,m.cardlog_creattime,m.cardlog_clocktime,m.cardlog_faceimg,m.cardlog_similarity  
                    FROM gmc_machine_staffcardlog as m 
                    left join smc_staffer as t ON t.staffer_id = m.staffer_id 
                    left join smc_school as s ON s.school_id = m.school_id 
                    WHERE {$datawhere} 
                    ORDER BY m.cardlog_clocktime ASC 
                ) as x
                group by x.staffer_id ,FROM_UNIXTIME(x.cardlog_clocktime, '%Y-%m-%d')
            
                UNION
            
                SELECT y.*,
                    FROM_UNIXTIME(y.cardlog_clocktime, '%Y-%m-%d') as cardlog_clocktime_date 
                FROM(
                    SELECT m.staffer_id,t.staffer_cnname,t.staffer_enname,t.staffer_branch,s.school_shortname,s.school_branch,m.cardlog_creattime,m.cardlog_clocktime,m.cardlog_faceimg,m.cardlog_similarity 
                    FROM gmc_machine_staffcardlog as m 
                    left join smc_staffer as t ON t.staffer_id = m.staffer_id 
                    left join smc_school as s ON s.school_id = m.school_id 
                    WHERE {$datawhere} 
                    ORDER BY m.cardlog_clocktime DESC
                ) as y
                group by y.staffer_id ,FROM_UNIXTIME(y.cardlog_clocktime, '%Y-%m-%d')
            
            ) as y ";

            $all_num = $this->DataControl->selectOne(  $countsql);
            $allnum = $all_num['datanum'] + 0;
        }else{
            $allnum = 0;
        }

        if($isSchool == '1'){// 是否学校获取页面数据 1 学校
            $fieldstring = array('staffer_cnname', 'staffer_branch', 'cardlog_daytime', 'cardlog_clocktime','cardlog_similarity', 'cardlog_faceimg');
            $fieldname = array('教师姓名', '教师编号','考勤日期', '考勤时间', '相似度', '考勤图片');
            $fieldcustom = array("1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1","1","1");
        }else {
            $fieldname = array('staffer_cnname', 'staffer_branch', 'school_shortname', 'school_branch', 'cardlog_daytime', 'cardlog_clocktime','cardlog_similarity', 'cardlog_faceimg');
            $fieldstring = array('教师姓名', '教师编号', '校区名称', '校区编号','考勤日期', '考勤时间', '相似度', '考勤图片');
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1","1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1","1");
        }

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if($field[$i]["fieldstring"] == 'cardlog_faceimg' || $field[$i]["fieldname"] == 'cardlog_faceimg'){
                $field[$i]["isqrcode"] = 1;
            }
        }

        if ($MachineList) {
            foreach ($MachineList as &$MachineVar){
                $MachineVar['staffer_cnname'] = $MachineVar['staffer_enname']?$MachineVar['staffer_cnname'].'/'.$MachineVar['staffer_enname']:$MachineVar['staffer_cnname'];

                $MachineVar['cardlog_similarity'] = $MachineVar['cardlog_similarity']?sprintf('%.2f',$MachineVar['cardlog_similarity']):'--';

                $MachineVar['cardlog_daytime'] = date("Y-m-d",$MachineVar['cardlog_clocktime']);
                $MachineVar['cardlog_clocktime'] = date("H:i",$MachineVar['cardlog_clocktime']);
                $MachineVar['cardlog_faceimg'] = $MachineVar['cardlog_faceimg']?$MachineVar['cardlog_faceimg'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90':'';
            }

            if($isSchool == '1') {// 是否学校获取页面数据 1 学校
                $result = array();
                $result["field"] = $field;
                $result['allnum'] = $allnum;
                $result['list'] = $MachineList;
            }else {
                $result = array();
                $result["field"] = $field;
                $result['data']['allnum'] = $allnum;
                $result['data']['datalist'] = $MachineList;
            }

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        }else{
            if($isSchool == '1') {// 是否学校获取页面数据 1 学校
                $result = array();
                $result["field"] = $field;
                $result['allnum'] = 0;
                $result['list'] = array();
            }else {
                $result = array();
                $result["field"] = $field;
                $result['data']['allnum'] = 0;
                $result['data']['datalist'] = array();
            }

            $this->error = 1;
            $this->errortip = "暂未获取到出勤记录";
            $this->result = $result;
            return false;
        }
    }

    //获取考勤机关联的学校
    function getMachineSchool($paramArray){
        $schoolList = $this->DataControl->selectClear(" select s.school_id,s.school_shortname,s.school_branch
        from gmc_machine as m,smc_school as s  
        where m.company_id = '{$paramArray['company_id']}' and m.machine_state = '1' and m.school_id = s.school_id 
        group by s.school_id ");

        $field = [
            "school_id"=>"学校ID",
            "school_shortname"=>"学校名称",
            "school_branch"=>"学校编号",
        ];

        $result = array();
        $result["field"] = $field;
        $result['list'] =$schoolList?$schoolList:array();

        $this->error = 0;
        $this->errortip = "暂未获取到出勤记录";
        $this->result = $result;
        return false;
    }


    //电话外呼厂商管理 -- 97
    function getMerchantList($paramArray)
    {
        $datawhere=" merchant_isdel = '0' and company_id = '{$paramArray['company_id']}' ";
        if(isset($paramArray['merchant_isopen']) && $paramArray['merchant_isopen'] != ''){
            $datawhere .= " and merchant_isopen = '{$paramArray['merchant_isopen']}' ";
        }
        $list = $this->DataControl->selectClear("select merchant_id,merchant_name,merchant_isopen 
                from crm_merchant 
                where {$datawhere} ");

        $fieldname = array('merchant_id','merchant_name','merchant_isopen');
        $fieldstring = array('厂商ID','厂商名称','是否开启');
        $fieldcustom = array("0", "1", "1");
        $fieldshow = array("0", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == 'merchant_isopen') {
                $field[$i]["switch"] = 1;
            }
        }

        if ($list) {
            $result = array();
            $result["field"] = $field;
            $result['data']['datalist'] = $list;

            $this->error = 0;
            $this->errortip = "电话厂商列表获取成功";
            $this->result = $result;
            return true;
        }else{
            $result = array();
            $result["field"] = $field;
            $result['data']['datalist'] = array();

            $this->error = 1;
            $this->errortip = "暂无电话厂商数据";
            $this->result = $result;
            return false;
        }
    }
    //添加电话外呼厂商 -- 97
    function addMerchantAction($paramArray)
    {
        if($paramArray['merchant_name'] == ''){
            $this->error = 1;
            $this->errortip = "厂商名称不能为空";
            return false;
        }elseif($this->DataControl->selectOne("select merchant_id from crm_merchant where merchant_name = '{$paramArray['merchant_name']}' and company_id = '{$paramArray['company_id']}' and merchant_isdel = '0' ")){
            $this->error = 1;
            $this->errortip = "厂商名称已存在";
            return false;
        }else{
            $machinedata = array();
            $machinedata['company_id'] = $paramArray['company_id'];
            $machinedata['merchant_name'] = $paramArray['merchant_name'];
            $machinedata['merchant_isopen'] = 0;//是否合作中  0 否 1 是
            $machinedata['merchant_createtime'] = time();
            $machineID = $this->DataControl->insertData("crm_merchant",$machinedata);
            if($machineID > 1){
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "坐席外呼管理->厂商管理", '添加厂商', dataEncode($paramArray));
                $this->error = 0;
                $this->errortip = "信息添加成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "信息添加失败";
                return false;
            }
        }
    }
    //删除电话外呼厂商 -- 97
    function delMerchantAction($paramArray)
    {
        if($this->DataControl->getFieldOne("crm_merchant_seats", "seats_id", "merchant_id = '{$paramArray['merchant_id']}' and company_id = '{$paramArray['company_id']}' and seats_isopen = '1' and seats_isdel = '0' ")){
        $this->error = 1;
        $this->errortip = "该厂商当前有已开启的坐席，无法删除";
        return false;
        }

        $dataOne = $this->DataControl->getFieldOne("crm_merchant", "merchant_id", "merchant_id = '{$paramArray['merchant_id']}' and company_id = '{$paramArray['company_id']}' and merchant_isdel = '0' ");
        if ($dataOne) {
            $data = array();
            $data['merchant_isdel'] = 1;
            $data['merchant_updatetime'] = time();
            $machineid = $this->DataControl->updateData('crm_merchant',"merchant_id='{$dataOne['merchant_id']}'",$data);

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "硬件管理->坐席外呼管理->厂商管理", '删除电话厂商', dataEncode($paramArray));

            $this->error = 0;
            $this->errortip = "删除成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "删除失败";
            return false;
        }
    }
    //电话外呼厂商  是否启用
    function isMerchantOpenAction($paramArray)
    {
        $merchantOne = $this->DataControl->getFieldOne("crm_merchant", "merchant_id", "merchant_id = '{$paramArray['merchant_id']}' and company_id = '{$paramArray['company_id']}' ");
        if ($merchantOne) {
            if($paramArray['merchant_isopen'] == '1'){
                $tip = '开启';
            }else{
                $tip = '关闭';
            }
            $data = array();
            $data['merchant_isopen'] = $paramArray['merchant_isopen'];
            $data['merchant_updatetime'] = time();
            if($this->DataControl->updateData("crm_merchant", "merchant_id = '{$paramArray['merchant_id']}' and company_id = '{$paramArray['company_id']}' ", $data)){
                if($paramArray['merchant_isopen'] == '0'){
                    $this->DataControl->updateData("crm_merchant_seats","merchant_id = '{$paramArray['merchant_id']}' and company_id = '{$paramArray['company_id']}' ",array("seats_isopen"=>'0',"seats_updatetime"=>time()));
                }
                $this->error = 0;
                $this->errortip = "{$tip}成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "{$tip}失败";
                return true;
            }
        } else {
            $this->error = 1;
            $this->errortip = "未找到厂商";
            return true;
        }
    }

    //电话外呼厂商 --  分机（坐席）管理 -- 97
    function getMerchantSeats($paramArray){
        $datawhere=" 1 and a.company_id = '{$paramArray['company_id']}' and a.seats_isdel = '0' and b.merchant_isdel = '0'  ";
        //关键词
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and a.seats_account like '%{$paramArray['keyword']}%' ";
        }
        if(isset($paramArray['merchant_id']) && $paramArray['merchant_id'] != ''){
            $datawhere .= " and a.merchant_id = '{$paramArray['merchant_id']}' ";
        }
        if(isset($paramArray['seats_isopen']) && $paramArray['seats_isopen'] != ''){
            $datawhere .= " and a.seats_isopen = '{$paramArray['seats_isopen']}' ";
        }
        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
            $datawhere .= " and c.school_id = '{$paramArray['school_id']}' ";
        }
        if(isset($paramArray['p']) && $paramArray['p'] !== ''){
            $page = $paramArray['p'];
        }else{
            $page = '1';
        }
        if(isset($paramArray['num']) && $paramArray['num'] !== ''){
            $num = $paramArray['num'];
        }else{
            $num = '10';
        }
        $pagestart = ($page-1)*$num;

        $sql = "select a.seats_id,a.merchant_id,a.seats_account,a.seats_isway,a.seats_concurrency,a.seats_isopen,a.seats_province,a.seats_city,a.seats_isofficial,a.seats_intentionlevel,
                    (select count(x.schoolapply_id) from crm_merchant_seats_schoolapply as x  where x.merchant_id = a.merchant_id and x.seats_id = a.seats_id) as schoolall, 
                    b.merchant_name 
                from crm_merchant_seats as a 
                left join crm_merchant as b on a.merchant_id = b.merchant_id  
                left join crm_merchant_seats_schoolapply as c ON a.merchant_id= c.merchant_id and a.seats_id= c.seats_id
                where {$datawhere}   
                group by a.seats_id
                ORDER BY a.seats_id desc   
                LIMIT {$pagestart},{$num}";
        $MachineList = $this->DataControl->selectClear($sql);
        if($MachineList){
            foreach ($MachineList as &$MachineVar){
                if($MachineVar['seats_intentionlevel']){
                    $intentionlevelstr= '';
                    $arr = explode(',',$MachineVar['seats_intentionlevel']);
                    $intentionlevelstr = implode('星 ',$arr).'星';
                    $MachineVar['seats_intentionlevelstr'] = $intentionlevelstr;
                }
            }
        }

        //统计总数
        if(isset($paramArray['is_count']) && $paramArray['is_count'] == '1') {
            $all_num = $this->DataControl->selectClear("SELECT count(a.seats_id) as datanum
                from crm_merchant_seats as a 
                left join crm_merchant as b on a.merchant_id = b.merchant_id 
                left join crm_merchant_seats_schoolapply as c ON a.merchant_id= c.merchant_id and a.seats_id= c.seats_id
                where {$datawhere} 
                group by a.seats_id");
            if($all_num) {
                $allnum = count($all_num) + 0;
            }else{
                $all_num = 0;
            }
        }else{
            $allnum = 0;
        }

        $fieldname = array('seats_id','merchant_id','seats_account','merchant_name','seats_isway_name','seats_intentionlevelstr',
            'schoolall','seats_concurrency','seats_isopen');
        $fieldstring = array('分机序号','厂商序号','分机号/账号','厂商','拨打方式','适配名单星级',
            '适配学校','坐席支持并发数','是否开启');
        $fieldcustom = array("0", "0", "1", "1", "1","1",
            "1", "1", "1");
        $fieldshow = array("0", "0", "1", "1", "1","1",
            "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($field[$i]["fieldname"] == 'seats_isopen') {
                $field[$i]["switch"] = 1;
            }
            if ($field[$i]["fieldname"] == 'schoolall') {
                $field[$i]["ismethod"] = 1;
            }
        }

        if ($MachineList) {
            foreach ($MachineList as &$MachineVar){
                if($MachineVar['seats_isway'] == '1') {
                    $MachineVar['seats_isway_name'] = '单呼';
                }elseif($MachineVar['seats_isway'] == '2'){
                    $MachineVar['seats_isway_name'] = '双呼';
                }
            }

            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = $allnum;
            $result['data']['datalist'] = $MachineList;

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = 0;
            $result['data']['datalist'] = array();

            $this->error = 1;
            $this->errortip = "暂未分机号/账号";
            $this->result = $result;
            return false;
        }
    }
    //添加电话外呼厂商 -- 97
    function addMerchantSeatsAction($paramArray){
        if($paramArray['seats_account'] == ''){
            $this->error = 1;
            $this->errortip = "分机号/账号不能为空";
            return false;
        }elseif($paramArray['merchant_id'] == ''){
            $this->error = 1;
            $this->errortip = "厂商不能为空";
            return false;
        }elseif($paramArray['seats_isway'] == ''){
            $this->error = 1;
            $this->errortip = "拨打方式不能为空";
            return false;
        }elseif($this->DataControl->selectOne("select seats_id from crm_merchant_seats where seats_account = '{$paramArray['seats_account']}' and company_id = '{$paramArray['company_id']}' and merchant_id = '{$paramArray['merchant_id']}' and seats_isdel = '0' ")){
            $this->error = 1;
            $this->errortip = "分机号/账号 已存在";
            return false;
        }else{
            $machinedata = array();
            $machinedata['company_id'] = $paramArray['company_id'];
            $machinedata['merchant_id'] = $paramArray['merchant_id'];
            $machinedata['seats_account'] = $paramArray['seats_account'];
            $machinedata['seats_isway'] = $paramArray['seats_isway'];
            $machinedata['seats_province'] = $paramArray['seats_province'];
            $machinedata['seats_city'] = $paramArray['seats_city'];
            $machinedata['seats_concurrency'] = $paramArray['seats_concurrency'];
            $machinedata['seats_isofficial'] = $paramArray['seats_isofficial'];
            $machinedata['seats_intentionlevel'] = $paramArray['seats_intentionlevel'];
            $machinedata['seats_isopen'] = 0;//是否开启  0 否 1 是
            $machinedata['seats_createtime'] = time();
            $machineID = $this->DataControl->insertData("crm_merchant_seats",$machinedata);
            if($machineID > 1){
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "坐席外呼管理->新增", '添加分机号/账号', dataEncode($paramArray));
                $this->error = 0;
                $this->errortip = "信息添加成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "信息添加失败";
                return false;
            }
        }
    }
    //编辑电话外呼厂商 -- 97
    function editMerchantSeatsAction($paramArray){
        if($this->DataControl->selectOne("select seats_id from crm_merchant_seats where seats_account = '{$paramArray['seats_account']}' and company_id = '{$paramArray['company_id']}' and merchant_id = '{$paramArray['merchant_id']}' and seats_isdel = '0' and seats_id <> '{$paramArray['seats_id']}' ")){
            $this->error = 1;
            $this->errortip = "分机号/账号 已存在";
            return false;
        }else{
            $machinedata = array();
            $machinedata['company_id'] = $paramArray['company_id'];
            if($paramArray['merchant_id']) {
                $machinedata['merchant_id'] = $paramArray['merchant_id'];
            }
            if($paramArray['seats_account']) {
                $machinedata['seats_account'] = $paramArray['seats_account'];
            }
            if($paramArray['seats_isway']) {
                $machinedata['seats_isway'] = $paramArray['seats_isway'];
            }
            if($paramArray['seats_province']) {
                $machinedata['seats_province'] = $paramArray['seats_province'];
            }
            if($paramArray['seats_city']) {
                $machinedata['seats_city'] = $paramArray['seats_city'];
            }
            if($paramArray['seats_concurrency']) {
                $machinedata['seats_concurrency'] = $paramArray['seats_concurrency'];
            }
            if($paramArray['seats_isopen']) {
                $machinedata['seats_isopen'] = $paramArray['seats_isopen'];//是否开启  0 否 1 是
            }
            $machinedata['seats_isofficial'] = $paramArray['seats_isofficial'];//是否400电话  0 否 1 是
            if($paramArray['seats_isofficial'] == '0'){
                $machinedata['seats_intentionlevel'] = '';//适配意向星级
            }else {
                $machinedata['seats_intentionlevel'] = $paramArray['seats_intentionlevel'];//适配意向星级
            }
            $machinedata['seats_updatetime'] = time();
            $machineID = $this->DataControl->updateData("crm_merchant_seats"," seats_id = '{$paramArray['seats_id']}' and company_id = '{$paramArray['company_id']}' and seats_isdel = '0' ",$machinedata);
            if(strpos($machineID,'UPDATE') == false) {
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "坐席外呼管理->编辑", '编辑分机号/账号', dataEncode($paramArray));

                $this->error = 0;
                $this->errortip = "信息修改成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "信息修改失败";
                return true;
            }
        }
    }
    //删除电话外呼厂商 -- 97
    function delMerchantSeatsAction($paramArray){
        $dataOne = $this->DataControl->selectOne("select seats_id from crm_merchant_seats where seats_id = '{$paramArray['seats_id']}' and company_id = '{$paramArray['company_id']}' and seats_isdel = '0' ");
        if ($dataOne) {
            $data = array();
            $data['seats_isdel'] = 1;
            $data['seats_updatetime'] = time();
            $this->DataControl->updateData('crm_merchant_seats',"seats_id='{$dataOne['seats_id']}' and company_id = '{$paramArray['company_id']}' and seats_isdel = '0' ",$data);

            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "硬件管理->坐席外呼管理->厂商管理", '删除电话厂商', dataEncode($paramArray));

            $this->error = 0;
            $this->errortip = "删除成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "删除失败";
            return false;
        }
    }
    //电话外呼厂商 --  分机（坐席）管理   是否启用
    function isMerchantSeatsOpenAction($paramArray)
    {
        $msql = "select a.seats_id,b.merchant_isopen
                from crm_merchant_seats as a ,crm_merchant as b 
                where a.seats_id = '{$paramArray['seats_id']}' and a.company_id = '{$paramArray['company_id']}' and a.seats_isdel = 0 
                and a.merchant_id = b.merchant_id ";
        $merchantOne = $this->DataControl->selectOne($msql);
        if ($merchantOne) {
            if($merchantOne['merchant_isopen'] != '1'){
                $this->error = 1;
                $this->errortip = "该厂商暂未开启";
                return true;
            }

            if($paramArray['seats_isopen'] == '1'){
                $tip = '开启';
            }else{
                $tip = '关闭';
            }

            $data = array();
            $data['seats_isopen'] = $paramArray['seats_isopen'];
            $data['seats_updatetime'] = time();
            if($this->DataControl->updateData("crm_merchant_seats", "seats_id = '{$paramArray['seats_id']}' and company_id = '{$paramArray['company_id']}' ", $data)){
                $this->error = 0;
                $this->errortip = "{$tip}成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "{$tip}失败";
                return true;
            }
        } else {
            $this->error = 1;
            $this->errortip = "未找到厂商分机号/账号";
            return true;
        }
    }
    //电话外呼厂商 --  分机（坐席）管理  适配学校
    function addMerchantSeatsSchoolAction($paramArray)
    {
        if (!isset($paramArray['school_json']) || $paramArray['school_json'] == '') {
            $this->error = true;
            $this->errortip = "请选择学校";
            return false;
        }
        $list = json_decode(stripslashes($paramArray['school_json']), 1);
        foreach ($list as $val) {
            if (!$this->DataControl->getFieldOne("crm_merchant_seats_schoolapply", "schoolapply_id", "school_id='{$val}' and seats_id='{$paramArray['seats_id']}' and merchant_id='{$paramArray['merchant_id']}' ")) {
                $data = array();
                $data['school_id'] = $val;
                $data['seats_id'] = $paramArray['seats_id'];
                $data['merchant_id'] = $paramArray['merchant_id'];
                $this->DataControl->insertData("crm_merchant_seats_schoolapply", $data);
            }
        }
        $this->error = 0;
        $this->errortip = "添加成功";
        return true;
    }
    //电话外呼厂商  --  分机（坐席）管理 取消适配学校
    function delMerchantSeatsSchoolAction($paramArray)
    {
        $school_list = json_decode(stripslashes($paramArray['school_json']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $schoolapply_id = implode(',', $str);
            $where = "school_id in ({$schoolapply_id}) and seats_id = '{$paramArray['seats_id']}' and merchant_id = '{$paramArray['merchant_id']}' ";
        }else{
            $where = "school_id = '{$paramArray['school_json']}' and seats_id = '{$paramArray['seats_id']}' and merchant_id = '{$paramArray['merchant_id']}' ";
        }

        if ($this->DataControl->delData("crm_merchant_seats_schoolapply", $where)) {
            $this->error = 0;
            $this->errortip = "移除成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "移除失败";
            return false;
        }
    }
    //电话外呼厂商  --  分机（坐席）管理 获取 已经 适配的学校
    function getHaveSeatsSchoolAction($paramArray)
    {
        $datawhere = " 1 and b.company_id = '{$paramArray['company_id']}' and b.school_isclose = '0'  ";
        //and a.merchant_id = '{$paramArray['merchant_id']}' and a.seats_id = '{$paramArray['seats_id']}'
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (b.school_cnname like '%{$paramArray['keyword']}%' or b.school_branch like '%{$paramArray['keyword']}%' or b.school_enname like '%{$paramArray['keyword']}%' or b.school_shortname like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['merchant_id']) && $paramArray['merchant_id'] !== '') {
            $datawhere .= " and a.merchant_id = '{$paramArray['merchant_id']}' ";
        }
        if (isset($paramArray['seats_id']) && $paramArray['seats_id'] !== '') {
            $datawhere .= " and a.seats_id = '{$paramArray['seats_id']}' ";
        }

        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== '') {
            $datawhere .= " and b.district_id = '{$paramArray['district_id']}' ";
        }
        if (isset($paramArray['school_province']) && $paramArray['school_province'] !== '') {
            $datawhere .= " and b.school_province = '{$paramArray['school_province']}' ";
        }
        if (isset($paramArray['school_city']) && $paramArray['school_city'] !== '') {
            $datawhere .= " and b.school_city = '{$paramArray['school_city']}' ";
        }
        if (isset($paramArray['school_area']) && $paramArray['school_area'] !== '') {
            $datawhere .= " and b.school_area = '{$paramArray['school_area']}' ";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select b.school_id,b.school_cnname,b.school_branch,b.school_enname,c.district_cnname
              from crm_merchant_seats_schoolapply as a 
              left join smc_school as b on a.school_id = b.school_id 
              left join gmc_company_district as c on b.district_id = c.district_id
              where {$datawhere} 
              group by b.school_id 
              ORDER BY b.school_sort DESC
              LIMIT {$pagestart},{$num}";
        $schoolList = $this->DataControl->selectClear($sql);

        $fieldname = array('school_id','school_cnname','school_branch','school_enname','district_cnname');
        $fieldstring = array('学校ID','学校名称','学校编号','检索代码','区域');
        $fieldcustom = array("0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $dsql = "select c.district_id,c.district_cnname,b.school_id 
                from crm_merchant_seats_schoolapply as a 
                left join smc_school as b on a.school_id = b.school_id 
                left join gmc_company_district as c on b.district_id = c.district_id
                where {$datawhere} 
                group by c.district_id
                order by c.district_id asc
              ";
        $districtList = $this->DataControl->selectClear($dsql);
        if (!$districtList) {
            $districtList = array();
        }

        if ($schoolList) {

            $haveSql = "select count(a.schoolapply_id) as num 
                    from crm_merchant_seats_schoolapply as a 
                    left join smc_school as b on a.school_id = b.school_id 
                    left join gmc_company_district as c on b.district_id = c.district_id 
                    group by b.school_id ";
            $db_nums = $this->DataControl->selectClear($haveSql);

            if($db_nums){
                $allnum = count($db_nums);
            }else{
                $allnum = 0;
            }

            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = $allnum;
            $result['data']['datalist'] = $schoolList;
            $result['data']['districtList'] = $districtList;

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = 0;
            $result['data']['datalist'] = array();
            $result['data']['districtList'] = $districtList;

            $this->error = 1;
            $this->errortip = "暂无适配学校";
            $this->result = $result;
            return false;
        }
    }
    //电话外呼厂商  --  分机（坐席）管理 获取 可以 适配的学校
    function getCanSeatsSchoolAction($paramArray)
    {
        $datawhere = " 1 and a.company_id = '{$paramArray['company_id']}' and a.school_isclose = '0'  ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.school_cnname like '%{$paramArray['keyword']}%' or a.school_branch like '%{$paramArray['keyword']}%' or a.school_enname like '%{$paramArray['keyword']}%' or a.school_shortname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== '') {
            $datawhere .= " and a.district_id = '{$paramArray['district_id']}' ";
        }
        if (isset($paramArray['school_province']) && $paramArray['school_province'] !== '') {
            $datawhere .= " and a.school_province = '{$paramArray['school_province']}' ";
        }
        if (isset($paramArray['school_city']) && $paramArray['school_city'] !== '') {
            $datawhere .= " and a.school_city = '{$paramArray['school_city']}' ";
        }
        if (isset($paramArray['school_area']) && $paramArray['school_area'] !== '') {
            $datawhere .= " and a.school_area = '{$paramArray['school_area']}' ";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.school_id,a.school_cnname,a.school_branch,a.school_enname,c.district_cnname,
                if((select b.schoolapply_id from crm_merchant_seats_schoolapply as b where a.school_id = b.school_id and b.merchant_id = '{$paramArray['merchant_id']}' and b.seats_id = '{$paramArray['seats_id']}' limit 0,1  ),1,0) as schoolapply_id 
              from smc_school as a  
              left join gmc_company_district as c on a.district_id = c.district_id
              where {$datawhere} 
              having schoolapply_id = 0 
              ORDER BY a.school_sort DESC
              LIMIT {$pagestart},{$num}";
        $schoolList = $this->DataControl->selectClear($sql);

        $fieldname = array('school_id','school_cnname','school_branch','school_enname','district_cnname');
        $fieldstring = array('学校ID','学校名称','学校编号','检索代码','区域');
        $fieldcustom = array("0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $dsql = "select c.district_id,c.district_cnname,
                if((select b.schoolapply_id from crm_merchant_seats_schoolapply as b where a.school_id = b.school_id and b.merchant_id = '{$paramArray['merchant_id']}' and b.seats_id = '{$paramArray['seats_id']}' limit 0,1  ),1,0) as schoolapply_id 
              from smc_school as a  
              left join gmc_company_district as c on a.district_id = c.district_id
              where {$datawhere} 
              group by c.district_id
              having schoolapply_id = 0 
              order by c.district_id asc
              ";
        $districtList = $this->DataControl->selectClear($dsql);
        if (!$districtList) {
            $districtList = array();
        }

        if ($schoolList) {

            $haveSql = "select count(a.school_id) as num,
                if((select b.schoolapply_id from crm_merchant_seats_schoolapply as b where a.school_id = b.school_id and b.merchant_id = '{$paramArray['merchant_id']}' and b.seats_id = '{$paramArray['seats_id']}' limit 0,1 ),1,0) as schoolapply_id 
              from smc_school as a  
              left join gmc_company_district as c on a.district_id = c.district_id
              where {$datawhere} 
              having schoolapply_id = 0  ";
            $db_nums = $this->DataControl->selectOne($haveSql);

            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = $db_nums['num'];
            $result['data']['datalist'] = $schoolList;
            $result['data']['districtList'] = $districtList;

            $this->error = 0;
            $this->errortip = "信息获取成功";
            $this->result = $result;
            return true;
        }else{
            $result = array();
            $result["field"] = $field;
            $result['data']['count'] = 0;
            $result['data']['datalist'] = array();
            $result['data']['districtList'] = $districtList;

            $this->error = 1;
            $this->errortip = "暂无可以操作的学校";
            $this->result = $result;
            return false;
        }
    }

}
