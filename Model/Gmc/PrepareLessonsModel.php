<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class PrepareLessonsModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //班别教务设置
    function getClassPostList($paramArray)
    {
        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.course_status = '1'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_cnname like '%{$paramArray['keyword']}%' or c.course_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ='{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id ='{$paramArray['course_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT c.course_id,c.course_cnname,c.course_branch,c.course_classnum,ec.classcode_id,ec.classcode_isopen,ec.classcode_isbeike,ec.classcode_isregister,cc.coursecat_cnname,ct.coursetype_cnname
                FROM smc_course as c LEFT JOIN eas_classcode as ec ON ec.company_id = c.company_id AND ec.classcode_branch = c.course_branch
                LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = c.coursecat_id
                LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = c.coursetype_id
                WHERE {$datawhere}
                ORDER BY cc.coursecat_id ASC,c.course_branch ASC,ec.classcode_isbeike DESC,ec.classcode_id DESC
                LIMIT {$pagestart},{$num}";

        $ClassList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(c.course_id) as num
                                                    FROM smc_course as c
                                                    LEFT JOIN eas_classcode as ec ON ec.company_id = c.company_id AND ec.classcode_branch = c.course_branch
                                                    LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = c.coursecat_id
                                                    LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = c.coursetype_id
                                                    WHERE {$datawhere} limit 0,1");
        $allnums = $all_num['num'];

        $fieldstring = array('course_id','course_cnname', 'course_branch', 'coursetype_cnname', 'coursecat_cnname', 'course_classnum', 'classcode_isopen', 'classcode_isregister', 'classcode_isbeike');
        $fieldname = $this->LgArraySwitch(array('课程别ID','课程别名称', '课程别编号', '所属班组', '所属班种', '班别课次数量', '是否开启个性化教案', '是否开启教务登记', '是否开启备课'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldismethod = array("0", "0", "0", "0", "0", "0", "0", "0", "0");
        $fieldiisswitch = array("0", "0", "0", "0", "0", "0", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldismethod[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ClassList) {
            $result['list'] = $ClassList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程别信息", 'result' => $result);
        }
        return $res;
    }

    //班别课次明细
    function ClassHourList($paramArray)
    {
        $datawhere = "t.company_id = '{$paramArray['company_id']}' and t.classcode_branch = '{$paramArray['course_branch']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.teachhour_name like '%{$paramArray['keyword']}%' or t.teachhour_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    t.*,(SELECT c.classcode_name FROM eas_classcode as c WHERE c.company_id = t.company_id AND c.classcode_branch = t.classcode_branch) as classcode_name,
                    (SELECT COUNT(tp.teachplan_id) FROM eas_teachhour_teachplan as tp WHERE tp.company_id = t.company_id AND tp.classcode_branch = t.classcode_branch AND tp.teachhour_branch = t.teachhour_branch) as teachplan_num,
                    (SELECT COUNT(tw.tempworks_id) FROM eas_teachhour_tempworks as tw WHERE tw.company_id = t.company_id AND tw.classcode_branch = t.classcode_branch AND tw.teachhour_branch = t.teachhour_branch) as tempworks_num
                FROM
                    eas_teachhour as t
                WHERE
                    {$datawhere}
                ORDER BY
                    t.teachhour_sort ASC
                LIMIT
                    {$pagestart},{$num}";

        $ClassHourList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(t.teachhour_id) as num FROM eas_teachhour as t WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('teachhour_id', 'teachhour_name', 'teachhour_branch', 'classcode_name', 'teachhour_isregister', 'teachhour_isbeike', 'teachhour_isscore', 'teachhour_isoffline', 'teachhour_isonline', 'teachhour_isonscore', 'teachplan_num', 'tempworks_num', 'teachhour_sort');
        $fieldname = $this->LgArraySwitch(array('课次ID', '课次名称', '课次编号', '课程别名称', '开启教务登记', '开启备课', '开启考试成绩登记', '开启线下作业登记', '开启网课状态登记', '开启网课分数登记', '教案个数', '教学作品个数', '课次排序'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "0", "0", "1", "1", "1", "1", "1", "1", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ClassHourList) {
            $result['list'] = $ClassHourList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无班级课次", 'result' => $result);
        }

        return $res;
    }

    //一键生成班别课次
    function addClassHour($paramArray)
    {
        $courseOne = $this->DataControl->getOne("smc_course", "company_id='{$paramArray['company_id']}' and course_branch='{$paramArray['course_branch']}'");
        if($courseOne){
            $teachhourList = $this->DataControl->selectClear("SELECT teachhour_id FROM eas_teachhour WHERE company_id='{$paramArray['company_id']}' and classcode_branch='{$paramArray['course_branch']}'");
            if(!$teachhourList){
                $data['company_id'] = $courseOne['company_id'];
                $coursecat = $this->DataControl->getFieldOne("smc_code_coursecat","coursecat_branch","coursecat_id='{$courseOne['coursecat_id']}'");
                $data['catcode_branch'] = $coursecat['coursecat_branch'];
                $data['classcode_branch'] = $courseOne['course_branch'];
                $data['classcode_name'] = $courseOne['course_cnname'];
                $data['classcode_hour'] = $courseOne['course_classnum'];
                $this->DataControl->insertData("eas_classcode", $data);
            }
            $hourList = $this->DataControl->getOne("eas_classcode", "company_id='{$paramArray['company_id']}' and classcode_branch='{$paramArray['course_branch']}'");
            if (count($teachhourList) == $hourList['classcode_hour']) {
                $result = array();
                $result["data"] = $teachhourList;
                $res = array('error' => '1', 'errortip' => '班别课时不可重复生成', 'result' => $result);
                return $res;
            }

            if ($hourList) {
                for ($hour = 1; $hour <= $hourList['classcode_hour']; $hour++) {
                    if (strstr($hourList['catcode_branch'], "060")) {
                        $hourName = "Week {$hour}";
                    } else {
                        $hourName = "Lesson {$hour}";
                    }

                    if (!$this->DataControl->getFieldOne("eas_teachhour", "teachhour_id", "company_id='{$paramArray['company_id']}' and classcode_branch='{$hourList['classcode_branch']}' and teachhour_name = '{$hourName}'")) {
                        $class = $this->DataControl->getFieldOne("eas_classcode","classcode_isregister,classcode_isbeike","company_id='{$paramArray['company_id']}' and classcode_branch='{$hourList['classcode_branch']}'");
                        $data = array();
                        $data['company_id'] = $paramArray['company_id'];
                        $data['classcode_branch'] = $hourList['classcode_branch'];
                        $data['teachhour_branch'] = $hourList['classcode_branch'] . '-' . $hour;
                        $data['teachhour_name'] = $hourName;
                        $data['teachhour_sort'] = $hour;
                        $data['teachhour_isregister'] = $class['classcode_isregister'];
                        $data['teachhour_isbeike'] = $class['classcode_isbeike'];
                        $this->DataControl->insertData("eas_teachhour", $data);
                    }
                }
                $res = array('error' => '0', 'errortip' => "生成班别课次成功");
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '生成班别课次', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '课时数为0不可以自动生成', 'result' => $result);
            }
        } else {
            $res = array('error' => '1', 'errortip' => '数据不全');
        }

        return $res;
    }

    //编辑班别课次
    function editClassHour($paramArray)
    {
        $data = array();
        $data['teachhour_name'] = $paramArray['teachhour_name'];
        $data['teachhour_scoretype'] = $paramArray['teachhour_scoretype'];
        $data['teachhour_onscoretype'] = $paramArray['teachhour_onscoretype'];
        if ($this->DataControl->updateData("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑班别课次成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '编辑班别课次', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑班别课次失败');
        }

        return $res;
    }

    //删除班别课次
    function delClassHour($paramArray)
    {
        if(!$this->DataControl->getOne("eas_teachhour","teachhour_id='{$paramArray['teachhour_id']}'")){
            $res = array('error' => '1', 'errortip' => '班别课次不存在');
            return $res;
        }

        if($this->DataControl->delData("eas_teachhour", "teachhour_id='{$paramArray['teachhour_id']}'")){
            $res = array('error' => 0,'errortip' => "删除班别课次成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '删除班别课次', dataEncode($paramArray));
        }else{
            $res = array('error' => 1,'errortip' => "删除班别课次失败!");
        }

        return $res;
    }

    //教案明细管理
    function TeachPlanList($paramArray)
    {
        $datawhere = " company_id = '{$paramArray['company_id']}' and classcode_branch = '{$paramArray['classcode_branch']}' and teachhour_branch = '{$paramArray['teachhour_branch']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (teachplan_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['teachplan_class']) && $paramArray['teachplan_class'] !== "") {
            $datawhere .= " and teachplan_class = '{$paramArray['teachplan_class']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $teachplanList = $this->DataControl->selectClear("SELECT * FROM eas_teachhour_teachplan WHERE {$datawhere} ORDER BY teachplan_id DESC LIMIT {$pagestart},{$num}");

        if ($teachplanList) {
            $status = $this->LgArraySwitch(array("0" => "文件模式", "1" => "图片模式"));
            foreach ($teachplanList as &$val) {
                $val['teachplan_class'] = $status[$val['teachplan_class']];
                $val['teachplan_createtime'] = date("Y-m-d", $val['teachplan_createtime']);
                $teachpics = $this->DataControl->selectClear("SELECT teachpics_name,teachpics_url FROM eas_teachhour_teachpics WHERE teachplan_id = '{$val['teachplan_id']}'");
                if ($teachpics) {
                    $val['img_list'] = $teachpics;
                } else {
                    $val['img_list'] = array();
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(teachplan_id) as num FROM eas_teachhour_teachplan WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('teachplan_id', 'teachplan_name', 'teachplan_class', 'teachplan_coverimg', 'teachplan_createtime');
        $fieldname = $this->LgArraySwitch(array('教案ID', '教案名称', '教案模式', '教案封面', '创建时间'));
        $fieldcustom = array("0", "1", "1", "0", "1");
        $fieldshow = array("0", "1", "1", "0", "1");
        $fieldiisswitch = array("0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
            if ($fieldstring[$i] == 'teachplan_coverimg') {
                $field[$i]["isqrcode"] = 1;
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($teachplanList) {
            $result['list'] = $teachplanList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无教案明细", 'result' => $result);
        }

        return $res;
    }

    //是否开启个性化服务
    function OpenStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getOne("eas_classcode", "classcode_id = '{$paramArray['classcode_id']}'");
        if ($activityOne && $activityOne['classcode_isbeike'] == '1') {
            $data = array();
            $data['classcode_isopen'] = $paramArray['classcode_isopen'];

            if ($this->DataControl->updateData("eas_classcode", "classcode_id = '{$paramArray['classcode_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '是否开启个性化服务', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '请先开启备课', 'result' => $result);
        }
        return $res;
    }

    //教务状态
    function AllStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getOne("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'");
        if ($activityOne && $activityOne['teachhour_isregister'] == '1') {
            $data = array();
            if($paramArray['type'] == '1'){
                $data['teachhour_isscore'] = $paramArray['status'];
            }elseif($paramArray['type'] == '2'){
                $data['teachhour_isoffline'] = $paramArray['status'];
            }elseif($paramArray['type'] == '3'){
                $data['teachhour_isonline'] = $paramArray['status'];
            }elseif($paramArray['type'] == '4'){
                $data['teachhour_isonscore'] = $paramArray['status'];
            }

            if ($this->DataControl->updateData("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '教务状态', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '请先开启小循环登记', 'result' => $result);
        }
        return $res;
    }

    //是否开启备课
    function OpenBeiKeAction($paramArray)
    {
        $courseOne = $this->DataControl->getOne("smc_course", "course_id = '{$paramArray['course_id']}'");
        if ($courseOne) {
            $classcode = $this->DataControl->getOne("eas_classcode","company_id='{$courseOne['company_id']}' and classcode_branch='{$courseOne['course_branch']}'");
            $data = array();
            if($classcode){
                $data['classcode_hour'] = $courseOne['course_classnum'];
                $data['classcode_isbeike'] = $paramArray['classcode_isbeike'];
                if($paramArray['classcode_isbeike'] == '0'){
                    $data['classcode_isopen'] = '0';
                }
                if($this->DataControl->updateData("eas_classcode", "classcode_id='{$classcode['classcode_id']}'", $data)){
                    $teachhour = $this->DataControl->selectClear("SELECT * FROM eas_teachhour WHERE company_id='{$paramArray['company_id']}' and classcode_branch='{$classcode['classcode_branch']}'");
                    if($teachhour){
                        $data = array();
                        foreach($teachhour as $v){
                            if(!$v['classcode_isbeike']){
                                $data['teachhour_isbeike'] = $paramArray['classcode_isbeike'];
                                $this->DataControl->updateData("eas_teachhour", "teachhour_id='{$v['teachhour_id']}'", $data);
                            }
                        }
                    }

                    $result = array();
                    $result["data"] = $data;
                    $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '是否开启备课', dataEncode($paramArray));
                } else {
                    $result = array();
                    $result["data"] = array();
                    $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
                }
            }else{
                $data['company_id'] = $courseOne['company_id'];
                $coursecat = $this->DataControl->getFieldOne("smc_code_coursecat","coursecat_branch","coursecat_id='{$courseOne['coursecat_id']}'");
                $data['catcode_branch'] = $coursecat['coursecat_branch'];
                $data['classcode_name'] = $courseOne['course_cnname'];
                $data['classcode_branch'] = $courseOne['course_branch'];
                $data['classcode_hour'] = $courseOne['course_classnum'];
                $data['classcode_isbeike'] = $paramArray['classcode_isbeike'];
                $this->DataControl->insertData("eas_classcode", $data);

                $result = array();
                $result["data"] = array();
                $res = array('error' => '0', 'errortip' => "开启备课成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '是否开启备课', dataEncode($paramArray));
            }
        } else {
            $res = array('error' => '1', 'errortip' => '数据不全');
        }
        return $res;
    }

    //是否开启教务登记
    function OpenCourseRegisterAction($paramArray)
    {
        $courseOne = $this->DataControl->getOne("smc_course", "course_id = '{$paramArray['course_id']}'");
        if($courseOne){
            $activityOne = $this->DataControl->getOne("eas_classcode", "classcode_id = '{$paramArray['classcode_id']}'");
            if ($activityOne) {
                $data = array();
                $data['classcode_isregister'] = $paramArray['classcode_isregister'];

                if ($this->DataControl->updateData("eas_classcode", "classcode_id = '{$paramArray['classcode_id']}'", $data)) {
                    $teachhour = $this->DataControl->selectClear("SELECT * FROM eas_teachhour WHERE company_id='{$paramArray['company_id']}' and classcode_branch='{$activityOne['classcode_branch']}'");
                    if($teachhour){
                        $data = array();
                        foreach($teachhour as $v){
                            if(!$v['teachhour_isregister']){
                                $data['teachhour_isregister'] = $paramArray['classcode_isregister'];
                                $this->DataControl->updateData("eas_teachhour", "teachhour_id='{$v['teachhour_id']}'", $data);
                            }
                        }
                    }

                    $result = array();
                    $result["data"] = $data;
                    $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '是否开启教务登记', dataEncode($paramArray));
                } else {
                    $result = array();
                    $result["data"] = array();
                    $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
                }
            } else {
                $data['company_id'] = $paramArray['company_id'];
                $coursecat = $this->DataControl->getFieldOne("smc_code_coursecat","coursecat_branch","coursecat_id='{$courseOne['coursecat_id']}'");
                $data['catcode_branch'] = $coursecat['coursecat_branch'];
                $data['classcode_name'] = $courseOne['course_cnname'];
                $data['classcode_branch'] = $courseOne['course_branch'];
                $data['classcode_hour'] = $courseOne['course_classnum'];
                $data['classcode_isregister'] = $paramArray['classcode_isregister'];
                $this->DataControl->insertData("eas_classcode", $data);

                $result = array();
                $result["data"] = array();
                $res = array('error' => '0', 'errortip' => "开启教务登记成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '是否开启教务登记', dataEncode($paramArray));
            }
        } else {
            $res = array('error' => '1', 'errortip' => '数据不全');
        }
        return $res;
    }

    //课次是否开启教务登记
    function OpenRegisterAction($paramArray)
    {
        $activityOne = $this->DataControl->getOne("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'");
        if ($activityOne) {
            $classcode = $this->DataControl->getFieldOne("eas_classcode","classcode_isregister","company_id='{$activityOne['company_id']}' and classcode_branch='{$activityOne['classcode_branch']}'");
            if(!$classcode['classcode_isregister'] && $paramArray['teachhour_isregister']){
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '您需要先开启课程别的教务登记按钮哦~', 'result' => $result);
                return $res;
            }
            $data = array();
            $field = array();
            $data['teachhour_isregister'] = $paramArray['teachhour_isregister'];
            $field['teachhour_isregister'] = '是否开启教务登记';

            if ($this->DataControl->updateData("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '课次是否开启教务登记', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //课次是否开启备课
    function HourOpenBeikeAction($paramArray)
    {
        $activityOne = $this->DataControl->getOne("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'");
        if ($activityOne) {
            $classcode = $this->DataControl->getFieldOne("eas_classcode","classcode_isbeike","company_id='{$activityOne['company_id']}' and classcode_branch='{$activityOne['classcode_branch']}'");
            if(!$classcode['classcode_isbeike'] && $paramArray['teachhour_isbeike']){
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '您需要先开启课程别的备课按钮哦~', 'result' => $result);
                return $res;
            }
            $data = array();
            $field = array();
            $data['teachhour_isbeike'] = $paramArray['teachhour_isbeike'];
            $field['teachhour_isbeike'] = '是否开启备课';

            if ($this->DataControl->updateData("eas_teachhour", "teachhour_id = '{$paramArray['teachhour_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '课次是否开启备课', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //添加教案
    function AddTeachPlanAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['classcode_branch'] = $paramArray['classcode_branch'];
        $data['teachhour_branch'] = $paramArray['teachhour_branch'];
        $data['teachplan_name'] = $paramArray['teachplan_name'];
        $data['teachplan_videourl'] = $paramArray['teachplan_videourl'];
        $data['teachplan_class'] = $paramArray['teachplan_class'];
        if ($paramArray['teachplan_class'] == '0') {
            $data['teachplan_fileurl'] = $paramArray['teachplan_fileurl'];
            $teachplan_fileurl = json_decode(stripslashes($paramArray['teachplan_fileurl']), true);
            $url = "https://idocv.kidcastle.com.cn/view/url.json?url=" . $teachplan_fileurl['url'];
            $word = $this->getCurl($url);
            $word = json_decode($word, true);
            if ($word['data']) {
                $wordlist = array();
                foreach ($word['data'] as $k => $v) {
                    $wordlist[$k] = $v['content'];
                }
                $wordstring = implode(" ", $wordlist);
                $data['teachplan_wordcontent'] = addslashes($wordstring);
            }
        }
        $data['teachplan_postil'] = $paramArray['teachplan_postil'];
        $data['teachplan_matters'] = $paramArray['teachplan_matters'];
        $data['teachplan_coverimg'] = $paramArray['teachplan_coverimg'];
        $data['teachplan_videocovering'] = $paramArray['teachplan_videocovering'];
        $data['teachplan_createtime'] = time();
        if ($id = $this->DataControl->insertData('eas_teachhour_teachplan', $data)) {
            if ($paramArray['teachplan_class'] == '1' && $paramArray['img_list']) {
                $imgList = json_decode(stripslashes($paramArray['img_list']), true);
                if ($imgList) {
                    foreach ($imgList as $k => $v) {
                        $list = array();
                        $list['company_id'] = $paramArray['company_id'];
                        $list['teachplan_id'] = $id;
                        $list['teachpics_name'] = $v['img_name'];
                        $list['teachpics_url'] = $v['img_url'];
                        $list['teachpics_sort'] = $k + 1;
                        $list['teachpics_createtime'] = time();
                        $listid = $this->DataControl->insertData("eas_teachhour_teachpics", $list);
                        if($listid){
                            $info = array();
                            $info['company_id'] = $v['company_id'];
                            $info['teachpics_id'] = $listid;
                            $this->DataControl->insertData("eas_prepare_teachpics", $info);
                        }
                    }
                }
            }

            $res = array('error' => '0', 'errortip' => "添加教案成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '添加教案', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '添加教案失败');
        }

        return $res;
    }

    //编辑教案
    function EditTeachPlanAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['classcode_branch'] = $paramArray['classcode_branch'];
        $data['teachhour_branch'] = $paramArray['teachhour_branch'];
        $data['teachplan_name'] = $paramArray['teachplan_name'];
        $data['teachplan_videourl'] = $paramArray['teachplan_videourl'];
        $data['teachplan_class'] = $paramArray['teachplan_class'];
        if ($paramArray['teachplan_class'] == '0') {
            $data['teachplan_fileurl'] = $paramArray['teachplan_fileurl'];
            $teachplan_fileurl = json_decode(stripslashes($paramArray['teachplan_fileurl']), true);
            $url = "https://idocv.kidcastle.com.cn/view/url.json?url=" . $teachplan_fileurl['url'];
            $word = $this->getCurl($url);
            $word = json_decode($word, true);
            if ($word['data']) {
                $wordlist = array();
                foreach ($word['data'] as $k => $v) {
                    $wordlist[$k] = $v['content'];
                }
                $wordstring = implode(" ", $wordlist);
                $data['teachplan_wordcontent'] = addslashes($wordstring);
            }
        }
        $data['teachplan_postil'] = $paramArray['teachplan_postil'];
        $data['teachplan_matters'] = $paramArray['teachplan_matters'];
        $data['teachplan_coverimg'] = $paramArray['teachplan_coverimg'];
        $data['teachplan_videocovering'] = $paramArray['teachplan_videocovering'];
        if ($this->DataControl->updateData('eas_teachhour_teachplan', "teachplan_id='{$paramArray['teachplan_id']}'", $data)) {
            if ($paramArray['teachplan_class'] == '1' && $paramArray['img_list']) {
                $this->DataControl->delData("eas_teachhour_teachpics", "teachplan_id='{$paramArray['teachplan_id']}'");
                $imgList = json_decode(stripslashes($paramArray['img_list']), true);
                if ($imgList) {
                    foreach ($imgList as $k => $v) {
                        $list = array();
                        $list['company_id'] = $paramArray['company_id'];
                        $list['teachplan_id'] = $paramArray['teachplan_id'];
                        $list['teachpics_name'] = $v['img_name'];
                        $list['teachpics_url'] = $v['img_url'];
                        $list['teachpics_sort'] = $k + 1;
                        $list['teachpics_createtime'] = time();
                        $listid = $this->DataControl->insertData("eas_teachhour_teachpics", $list);
                        if($listid){
                            $info = array();
                            $info['company_id'] = $v['company_id'];
                            $info['teachpics_id'] = $listid;
                            $this->DataControl->insertData("eas_prepare_teachpics", $info);
                        }
                    }
                }
            }

            $res = array('error' => '0', 'errortip' => "编辑教案成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '编辑教案', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑教案失败');
        }

        return $res;
    }

    //删除教案
    function DelTeachPlanAction($paramArray)
    {
        $teachPlanOne = $this->DataControl->getFieldOne("eas_teachhour_teachplan_collect","teachplan_id","teachplan_id='{$paramArray['teachplan_id']}'");
        if($teachPlanOne){
            $res = array('error' => '0', 'errortip' => "该教案已被收藏,无法删除");
            return $res;
        }
        $bool = $this->DataControl->delData("eas_teachhour_teachplan", "teachplan_id='{$paramArray['teachplan_id']}'");
        if($bool){
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '删除教案', dataEncode($paramArray));
            $res = array('error' => '0', 'errortip' => "删除教案成功");
        }else{
            $res = array('error' => '1', 'errortip' => '删除教案失败');
        }
        return $res;
    }

    function getCurl($url)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    //课程资料
    function CourseMaterials($paramArray)
    {
        $word = $this->DataControl->selectClear("SELECT tempfiles_id,tempfiles_name,tempfiles_url,tempfiles_size FROM eas_teachhour_tempfiles WHERE classcode_branch='{$paramArray['classcode_branch']}' AND teachhour_branch='{$paramArray['teachhour_branch']}' AND company_id='{$this->company_id}' AND tempfiles_class='0'");
        if ($word) {
            $wordnum = count($word);
            foreach ($word as &$v) {
                $v['Chakan'] = "https://idocv.kidcastle.com.cn/view/url?url=" . $v['tempfiles_url'];
            }
        } else {
            $word = array();
            $wordnum = 0;
        }

        $mp3 = $this->DataControl->selectClear("SELECT tempfiles_id,tempfiles_name,tempfiles_url,tempfiles_size FROM eas_teachhour_tempfiles WHERE classcode_branch='{$paramArray['classcode_branch']}' AND teachhour_branch='{$paramArray['teachhour_branch']}' AND company_id='{$this->company_id}' AND tempfiles_class='1'");
        if ($mp3) {
            $mp3num = count($mp3);
            foreach ($mp3 as &$v) {
                $v['Chakan'] = "https://idocv.kidcastle.com.cn/view/url?url=" . $v['tempfiles_url'];
            }
        } else {
            $mp3 = array();
            $mp3num = 0;
        }

        $ppt = $this->DataControl->selectClear("SELECT tempfiles_id,tempfiles_name,tempfiles_url,tempfiles_size FROM eas_teachhour_tempfiles WHERE classcode_branch='{$paramArray['classcode_branch']}' AND teachhour_branch='{$paramArray['teachhour_branch']}' AND company_id='{$this->company_id}' AND tempfiles_class='2'");
        if ($ppt) {
            $pptnum = count($ppt);
            foreach ($ppt as &$v) {
                $v['Chakan'] = "https://idocv.kidcastle.com.cn/view/url?url=" . $v['tempfiles_url'];
            }
        } else {
            $ppt = array();
            $pptnum = 0;
        }

        $data = array();
        $data['wordnum'] = $wordnum;
        $data['mp3num'] = $mp3num;
        $data['pptnum'] = $pptnum;
        $data['word'] = $word;
        $data['mp3'] = $mp3;
        $data['ppt'] = $ppt;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $data);

        return $res;
    }

    //上传课程资料
    function UploadFileApi($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['classcode_branch'] = $paramArray['classcode_branch'];
        $data['teachhour_branch'] = $paramArray['teachhour_branch'];
        $data['tempfiles_class'] = $paramArray['tempfiles_class'];
        $data['tempfiles_name'] = $paramArray['tempfiles_name'];
        $data['tempfiles_url'] = $paramArray['tempfiles_url'];
        $data['tempfiles_size'] = $paramArray['tempfiles_size'];
        $data['tempfiles_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_teachhour_tempfiles", $data);

        if ($dataid) {
            $res = array('error' => 0, 'errortip' => '上传成功');
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '上传课程资料', dataEncode($paramArray));
        } else {
            $res = array('error' => 1, 'errortip' => '上传失败');
        }

        return $res;
    }

    //删除课程资料
    function delCourseFileApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_teachhour_tempfiles","tempfiles_id='{$paramArray['tempfiles_id']}'")){
            $res = array('error' => '1', 'errortip' => '课程资料不存在');
            return $res;
        }

        if ($this->DataControl->delData("eas_teachhour_tempfiles","tempfiles_id='{$paramArray['tempfiles_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除课程资料成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", "删除课程资料", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除课程资料失败");
        }

        return $res;
    }

    //优秀视频管理
    function TempVideoApi($paramArray)
    {
        $datawhere = " company_id = '{$paramArray['company_id']}' and classcode_branch = '{$paramArray['classcode_branch']}' and teachhour_branch = '{$paramArray['teachhour_branch']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (tempvideo_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['tempvideo_status']) && $paramArray['tempvideo_status'] !== '') {
            $datawhere .= " and tempvideo_status = '{$paramArray['tempvideo_status']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $tempvideoList = $this->DataControl->selectClear("SELECT * FROM eas_teachhour_tempvideo WHERE {$datawhere} ORDER BY tempvideo_id DESC LIMIT {$pagestart},{$num}");

        if ($tempvideoList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已驳回"));
//            $type = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            foreach ($tempvideoList as &$val) {
                $val['tempvideo_status'] = $status[$val['tempvideo_status']];
//                $val['tempvideo_isPerfect'] = $type[$val['tempvideo_isPerfect']];
                $val['tempvideo_createtime'] = date("Y-m-d H:i:s", $val['tempvideo_createtime']);
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(tempvideo_id) as num FROM eas_teachhour_tempvideo WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('tempvideo_id ', 'tempvideo_name', 'tempvideo_author', 'tempvideo_status', 'tempvideo_createtime');
        $fieldname = $this->LgArraySwitch(array('视频ID', '视频名称', '视频作者', '审核状态', '创建时间'));
        $fieldcustom = array("0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($tempvideoList) {
            $result['list'] = $tempvideoList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无优秀视频", 'result' => $result);
        }

        return $res;

    }

    //优秀视频审核查看
    function TempVideoDetail($paramArray)
    {

        $tempvideoOne = $this->DataControl->selectOne("SELECT
                                                            t.tempvideo_id,t.tempvideo_status,t.tempvideo_name,t.tempvideo_author,t.tempvideo_videourl,t.tempvideo_rejectreason,t.tempvideo_examtime,
                                                            (SELECT s.staffer_cnname FROM smc_staffer as s WHERE s.staffer_id = t.staffer_id) as staffer_cnname
                                                        FROM
                                                            eas_teachhour_tempvideo as t
                                                        WHERE
                                                            t.tempvideo_id = '{$paramArray['tempvideo_id']}'
                                                        LIMIT 1");
        if ($tempvideoOne) {
            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已通过', '-1' => '已驳回'));
            $tempvideoOne['tempvideo_status'] = $status[$tempvideoOne['tempvideo_status']];
            if ($tempvideoOne['tempvideo_examtime']) {
                $tempvideoOne['tempvideo_examtime'] = date("Y-m-d", $tempvideoOne['tempvideo_examtime']);
            }
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $tempvideoOne);

        return $res;
    }

    //优秀视频审核
    function ExamineVideoApi($paramArray)
    {
        $tempvideoOne = $this->DataControl->getOne("eas_teachhour_tempvideo", "tempvideo_id = '{$paramArray['tempvideo_id']}'");
        if ($tempvideoOne) {
            $data = array();
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['tempvideo_status'] = $paramArray['tempvideo_status'];
            if($paramArray['tempvideo_status'] == '1'){
                $data['tempvideo_isPerfect'] = '1';
            }
            $data['tempvideo_rejectreason'] = $paramArray['tempvideo_rejectreason'];
            $data['tempvideo_examtime'] = time();

            if ($this->DataControl->updateData("eas_teachhour_tempvideo", "tempvideo_id = '{$paramArray['tempvideo_id']}'", $data)) {
                $res = array('error' => '0', 'errortip' => "优秀视频审核成功");
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '优秀视频审核', dataEncode($paramArray));
            } else {
                $res = array('error' => '1', 'errortip' => '优秀视频审核失败');
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //优秀教学作品管理
    function TempPostilApi($paramArray)
    {
        $datawhere = " w.company_id = '{$paramArray['company_id']}' and w.classcode_branch = '{$paramArray['classcode_branch']}' and w.teachhour_branch = '{$paramArray['teachhour_branch']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (w.tempworks_name like '%{$paramArray['keyword']}%' or w.tempworks_videoname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['tempworks_status']) && $paramArray['tempworks_status'] !== '') {
            $datawhere .= " and w.tempworks_status = '{$paramArray['tempworks_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    w.*,(SELECT s.staffer_cnname FROM smc_staffer as s WHERE s.staffer_branch = w.teacher_branch) as staffer_cnname
                FROM
                    eas_teachhour_tempworks as w
                WHERE
                    {$datawhere}
                ORDER BY
                    w.tempworks_id DESC
                LIMIT {$pagestart},{$num}";

        $tempvideoList = $this->DataControl->selectClear($sql);

        if ($tempvideoList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已驳回"));
            foreach ($tempvideoList as &$val) {
                $val['tempworks_status_name'] = $status[$val['tempworks_status']];
                $val['tempworks_createtime'] = date("Y-m-d", $val['tempworks_createtime']);
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(w.tempworks_id) as num FROM eas_teachhour_tempworks as w WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('tempworks_id', 'tempworks_name', 'tempworks_videoname', 'staffer_cnname', 'tempworks_status_name', 'tempworks_createtime');
        $fieldname = $this->LgArraySwitch(array('作品ID', '教案名称', '视频名称', '上传老师', '审核状态', '上传时间'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($tempvideoList) {
            $result['list'] = $tempvideoList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优秀教学作品", 'result' => $result);
        }

        return $res;

    }

    //优秀教学作品审核查看
    function TempPostilDetail($paramArray)
    {

        $tempvideoOne = $this->DataControl->selectOne("SELECT
                                                            t.tempworks_id,t.tempworks_status,t.tempworks_name,t.tempworks_class,t.tempworks_videoname,t.tempworks_videourl,t.tempworks_fileurl,t.tempworks_imgurl,t.tempworks_rejectreason,t.tempworks_examtime,t.tempworks_createtime,
                                                            (SELECT s.staffer_cnname FROM smc_staffer as s WHERE s.staffer_branch = t.teacher_branch) as staffer_cnname,
                                                            (SELECT s.staffer_cnname FROM smc_staffer as s WHERE s.staffer_id = t.staffer_id) as to_staffer_cnname
                                                        FROM
                                                            eas_teachhour_tempworks as t
                                                        WHERE
                                                            t.tempworks_id = '{$paramArray['tempworks_id']}'
                                                        LIMIT 1");
        if ($tempvideoOne) {
            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已通过', '-1' => '已驳回'));
            $tempvideoOne['tempworks_status_name'] = $status[$tempvideoOne['tempworks_status']];
            $tempvideoOne['tempworks_createtime'] = date("Y-m-d", $tempvideoOne['tempworks_createtime']);
            if ($tempvideoOne['tempworks_examtime']) {
                $tempvideoOne['tempworks_examtime'] = date("Y-m-d", $tempvideoOne['tempworks_examtime']);
            }
            $teachpics = $this->DataControl->selectClear("SELECT teachpics_name,teachpics_url FROM eas_teachhour_teachpics WHERE tempworks_id = '{$tempvideoOne['tempworks_id']}'");
            if ($teachpics) {
                $tempvideoOne['img_list'] = $teachpics;
            }
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $tempvideoOne);

        return $res;
    }

    //优秀教学作品查看教案
    function TempPostilSee($paramArray)
    {

        $tempvideoOne = $this->DataControl->selectOne("SELECT
                                                            t.tempworks_id,t.tempworks_name,t.tempworks_class,t.tempworks_postil,t.tempworks_matters,t.tempworks_fileurl,t.tempworks_wordcontent,
                                                            (SELECT p.prepare_postil FROM eas_prepare as p WHERE p.company_id = t.company_id AND p.teachhour_branch = t.teachhour_branch AND p.teacher_branch = t.teacher_branch AND p.tempworks_id = t.tempworks_id) as prepare_postil
                                                        FROM
                                                            eas_teachhour_tempworks as t
                                                        WHERE
                                                            t.tempworks_id = '{$paramArray['tempworks_id']}'
                                                        LIMIT 1");

        $teachpics = $this->DataControl->selectClear("SELECT t.teachpics_name,t.teachpics_url,t.teachpics_sort,p.prepare_isemphasis,p.prepare_isnokeep FROM eas_teachhour_teachpics as t LEFT JOIN eas_prepare_teachpics as p ON p.company_id = t.company_id AND p.teachpics_id = t.teachpics_id WHERE t.tempworks_id = '{$tempvideoOne['tempworks_id']}'");
        if ($teachpics) {
            foreach($teachpics as &$v){
                if($v['prepare_isemphasis']){
                    $v['prepare_isemphasis_name'] = '【重难点】';
                }
                if($v['prepare_isnokeep']){
                    $v['prepare_isnokeep_name'] = '【不讲】';
                }
            }
            $tempvideoOne['img_list'] = $teachpics;
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $tempvideoOne);

        return $res;
    }

    //优秀教学作品审核
    function ExaminePostilApi($paramArray)
    {
        $tempvideoOne = $this->DataControl->getOne("eas_teachhour_tempworks", "tempworks_id = '{$paramArray['tempworks_id']}'");
        if ($tempvideoOne) {
            $data = array();
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['tempworks_status'] = $paramArray['tempworks_status'];
            $data['tempworks_rejectreason'] = $paramArray['tempworks_rejectreason'];
            $data['tempworks_examtime'] = time();

            if ($this->DataControl->updateData("eas_teachhour_tempworks", "tempworks_id = '{$paramArray['tempworks_id']}'", $data)) {
                $res = array('error' => '0', 'errortip' => "优秀教学作品审核成功");
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->备课相关设置", '优秀教学作品审核', dataEncode($paramArray));
            } else {
                $res = array('error' => '1', 'errortip' => '优秀教学作品审核失败');
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }
}
