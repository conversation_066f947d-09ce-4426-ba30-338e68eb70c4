<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class EasxSettingModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //评价维度列表
    function getSturemarktempList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.sturemarktemp_title like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and t.course_id ='{$paramArray['course_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.sturemarktemp_id,
                s.sturemarktemp_title,
                s.sturemarktemp_highscore,
                s.sturemarktemp_lowscore,
                ( SELECT count( tt.sturemarktemp_id ) FROM eas_code_sturemark_tempcourse AS tt WHERE tt.sturemarktemp_id = s.sturemarktemp_id and tt.course_id > '0') as num,
                s.sturemarktemp_sort,
                s.sturemarktemp_remark,
                s.sturemarktemp_status 
            FROM
                eas_code_sturemarktemp AS s
                LEFT JOIN eas_code_sturemark_tempcourse AS t ON s.sturemarktemp_id = t.sturemarktemp_id 
            WHERE
                {$datawhere}
                AND s.company_id = '{$paramArray['company_id']}' 
            GROUP BY
                s.sturemarktemp_id
            LIMIT {$pagestart},{$num}";

        $CoursetypeList = $this->DataControl->selectClear($sql);

        if ($CoursetypeList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($CoursetypeList as &$val) {
                $val['sturemarktemp_status'] = $status[$val['sturemarktemp_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*) as a
            FROM
               (SELECT
                s.sturemarktemp_id,
                s.sturemarktemp_title,
                s.sturemarktemp_highscore,
                s.sturemarktemp_lowscore,
                s.sturemarktemp_sort,
                s.sturemarktemp_remark,
                s.sturemarktemp_status,
                s.sturemarktemp_status as sturemarktemp_status_name
            FROM
                eas_code_sturemarktemp AS s
                LEFT JOIN eas_code_sturemark_tempcourse AS t ON s.sturemarktemp_id = t.sturemarktemp_id 
            WHERE
                {$datawhere}
                AND s.company_id = '{$paramArray['company_id']}' 
            GROUP BY
                s.sturemarktemp_id) as aa");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('sturemarktemp_title ', 'sturemarktemp_lowscore', 'sturemarktemp_highscore', 'num', 'sturemarktemp_sort', 'sturemarktemp_status', 'sturemarktemp_remark');
        $fieldname = $this->LgArraySwitch(array('名称', '最低分', '最高分', '适用课程别', '显示顺序', '启用状态', '备注'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");
        $fieldismethod = array("0", "0", "0", "1", "0", "0", "0");
        $fieldiisswitch = array("0", "0", "0", "0", "0", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldismethod[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CoursetypeList) {
            $result['list'] = $CoursetypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无评价维度", 'result' => $result);
        }

        return $res;
    }

    //适用课程别列表
    function SturemarktempApply($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.sturemarktemp_title like '%{$paramArray['keyword']}%' or c.course_cnname like '%{$paramArray['keyword']}%' or c.course_branch like '%{$paramArray['keyword']}%' )";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ='{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.course_id,c.course_cnname,c.course_branch,ca.coursecat_cnname,ct.coursetype_cnname,c.course_inclasstype
            FROM
                eas_code_sturemark_tempcourse AS t
                LEFT JOIN eas_code_sturemarktemp AS s ON t.sturemarktemp_id = s.sturemarktemp_id 
                left join smc_course as c on c.course_id = t.course_id
                left join smc_code_coursecat as ca on c.coursecat_id = ca.coursecat_id
                left join smc_code_coursetype as ct on ct.coursetype_id = c.coursetype_id
            WHERE
                {$datawhere}
                AND t.sturemarktemp_id = '{$paramArray['sturemarktemp_id']}' and t.course_id > '0'
            LIMIT {$pagestart},{$num}";

        $CoursetypeList = $this->DataControl->selectClear($sql);

        if ($CoursetypeList) {
            $status = array("0" => "课次类", "1" => "期度类", "2" => "预约类");
            foreach ($CoursetypeList as &$val) {
                $val['course_inclasstype'] = $status[$val['course_inclasstype']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*) as a
            FROM
               (SELECT
                c.course_cnname,c.course_branch,ca.coursecat_cnname,ct.coursetype_cnname,c.course_inclasstype
            FROM
                eas_code_sturemark_tempcourse AS t
                LEFT JOIN eas_code_sturemarktemp AS s ON t.sturemarktemp_id = s.sturemarktemp_id 
                left join smc_course as c on c.course_id = t.course_id
                left join smc_code_coursecat as ca on c.coursecat_id = ca.coursecat_id
                left join smc_code_coursetype as ct on ct.coursetype_id = c.coursetype_id
            WHERE
                {$datawhere}
                AND t.sturemarktemp_id = '{$paramArray['sturemarktemp_id']}' and t.course_id > '0') as aa");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('course_cnname ', 'course_branch', 'coursecat_cnname', 'coursetype_cnname', 'course_inclasstype');
        $fieldname = $this->LgArraySwitch(array('课程别名称', '课程别编号', '所属班组', '所属班种', '上课方式'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CoursetypeList) {
            $result['list'] = $CoursetypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无适用课程别", 'result' => $result);
        }

        return $res;
    }

    //添加评价纬度
    function addSturemarktempAction($paramArray)
    {
        $data = array();
        $data['sturemarktemp_title'] = $paramArray['sturemarktemp_title'];
        $data['sturemarktemp_sort'] = $paramArray['sturemarktemp_sort'];
        $data['sturemarktemp_highscore'] = $paramArray['sturemarktemp_highscore'];
        $data['sturemarktemp_lowscore'] = $paramArray['sturemarktemp_lowscore'];
        $data['sturemarktemp_remark'] = $paramArray['sturemarktemp_remark'];
        $data['sturemarktemp_creatime'] = time();
        $data['company_id'] = $paramArray['company_id'];

        $a = $this->DataControl->getFieldOne("eas_code_sturemarktemp", "sturemarktemp_id", "sturemarktemp_title = '{$paramArray['sturemarktemp_title']}' and company_id = '{$paramArray['company_id']}'");
        if ($a) {
            ajax_return(array('error' => 1, 'errortip' => "维度名称不可重复！"), $this->companyOne['company_language']);
        }

        $field = array();
        $field['sturemarktemp_title'] = "名称";
        $field['sturemarktemp_sort'] = "顺序";
        $field['sturemarktemp_highscore'] = "最高分";
        $field['sturemarktemp_lowscore'] = "最低分";
        $field['sturemarktemp_remark'] = "备注";
        $field['company_id'] = "所属集团";
        $id = $this->DataControl->insertData('eas_code_sturemarktemp', $data);
        if ($id) {
            $coursedata = array();
            $courseList = json_decode(stripslashes($paramArray['course']), true);
            foreach ($courseList as $item) {
                $coursedata['course_id'] = $item['course_id'];
                $coursedata['sturemarktemp_id'] = $id;

                $a = $this->DataControl->getFieldOne('eas_code_sturemark_tempcourse', 'sturemarktemp_id', "sturemarktemp_id = '{$id}' and course_id = '{$item['course_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
                }
                $this->DataControl->insertData('eas_code_sturemark_tempcourse', $coursedata);
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加评价纬度成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '添加评价纬度', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加评价纬度失败', 'result' => $result);
        }
        return $res;
    }

    //编辑评价纬度
    function updateSturemarktempAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_sturemarktemp", "sturemarktemp_id", "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'");
        if ($coursetypeOne) {
            $data = array();
            $data['sturemarktemp_title'] = $paramArray['sturemarktemp_title'];
            $data['sturemarktemp_sort'] = $paramArray['sturemarktemp_sort'];
            $data['sturemarktemp_highscore'] = $paramArray['sturemarktemp_highscore'];
            $data['sturemarktemp_lowscore'] = $paramArray['sturemarktemp_lowscore'];
            $data['sturemarktemp_remark'] = $paramArray['sturemarktemp_remark'];

            $field = array();
            $field['sturemarktemp_title'] = "名称";
            $field['sturemarktemp_sort'] = "顺序";
            $field['sturemarktemp_highscore'] = "最高分";
            $field['sturemarktemp_lowscore'] = "最低分";
            $field['sturemarktemp_remark'] = "备注";

            if ($this->DataControl->updateData("eas_code_sturemarktemp", "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'", $data)) {
                $this->DataControl->delData('eas_code_sturemark_tempcourse', "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'");
                $coursedata = array();
                $courseList = json_decode(stripslashes($paramArray['course']), true);
                foreach ($courseList as $item) {
                    $coursedata['course_id'] = $item['course_id'];
                    $coursedata['sturemarktemp_id'] = $paramArray['sturemarktemp_id'];

                    $a = $this->DataControl->getFieldOne('eas_code_sturemark_tempcourse', 'sturemarktemp_id', "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}' and course_id = '{$item['course_id']}'");
                    if ($a) {
                        ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
                    }
                    $this->DataControl->insertData('eas_code_sturemark_tempcourse', $coursedata);
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑评价纬度成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '编辑评价纬度', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑评价纬度失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_code_sturemarktemp", "sturemarktemp_id", "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'");
        if ($activityOne) {
            $data = array();
            $data['sturemarktemp_status'] = $paramArray['sturemarktemp_status'];

            $field = array();
            $field['sturemarktemp_status'] = '是否启用';

            if ($this->DataControl->updateData("eas_code_sturemarktemp", "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '是否启用上课评星模板', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除评价纬度
    function delSturemarktempAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_sturemarktemp", "sturemarktemp_id", "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'");
        if ($coursetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("eas_code_sturemarktemp", "sturemarktemp_id = '{$paramArray['sturemarktemp_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除评价纬度成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '删除评价纬度', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除评价纬度失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //课堂评价模版列表
    function remarktemp($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['remarktemp_type_id']) && $paramArray['remarktemp_type_id'] !== "") {
            $datawhere .= " and r.remarktemp_type_id ={$paramArray['remarktemp_type_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                r.remarktemp_id,
                r.reeamrktemp_title,
                r.remarktemp_content,
                t.remarktemp_type_name,
	            r.remarktemp_status,
	            r.remarktemp_status as remarktemp_status_name,
	            r.remarktemp_type_id
            FROM
                eas_code_remarktemp AS r
                LEFT JOIN eas_code_remarktemp_type AS t ON r.remarktemp_type_id = t.remarktemp_type_id
            WHERE {$datawhere} and r.company_id = '{$paramArray['company_id']}' 
            LIMIT {$pagestart},{$num}";
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($coursecatList as &$val) {
                $val['remarktemp_status'] = $status[$val['remarktemp_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(r.remarktemp_id)
            FROM
                eas_code_remarktemp AS r
                LEFT JOIN eas_code_remarktemp_type AS t ON r.remarktemp_type_id = t.remarktemp_type_id
            WHERE
                {$datawhere} and r.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('remarktemp_type_id', 'remarktemp_type_name', 'reeamrktemp_title', 'remarktemp_content', 'remarktemp_status');
        $fieldname = $this->LgArraySwitch(array('id', '类别', '名称', '描述', '启用状态'));
        $fieldcustom = array("0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无课堂评价模版信息", 'result' => $result);
        }

        return $res;

    }

    //新增课堂评价模版
    function addRemarktempAction($paramArray)
    {
        $data = array();
        $data['remarktemp_type_id'] = $paramArray['remarktemp_type_id'];
        $data['reeamrktemp_title'] = $paramArray['reeamrktemp_title'];
        $data['remarktemp_content'] = $paramArray['remarktemp_content'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['remarktemp_type_id'] = "模板类型";
        $field['reeamrktemp_title'] = "模板标题";
        $field['remarktemp_content'] = "模板内容";
        $field['company_id'] = "所属公司";

        if ($id = $this->DataControl->insertData('eas_code_remarktemp', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增课堂评价模版成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '新增课堂评价模版', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新增课堂评价模版失败', 'result' => $result);
        }
        return $res;
    }

    //编辑课堂评价模版
    function updateRemarktempAction($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("eas_code_remarktemp", "remarktemp_id", "remarktemp_id = '{$paramArray['remarktemp_id']}'");
        if ($policyDetailOne) {
            $data = array();
            $data['remarktemp_type_id'] = $paramArray['remarktemp_type_id'];
            $data['reeamrktemp_title'] = $paramArray['reeamrktemp_title'];
            $data['remarktemp_content'] = $paramArray['remarktemp_content'];

            $field = array();
            $field['remarktemp_type_id'] = "模板类型";
            $field['reeamrktemp_title'] = "模板标题";
            $field['remarktemp_content'] = "模板内容";
            $field['company_id'] = "所属公司";

            if ($this->DataControl->updateData("eas_code_remarktemp", "remarktemp_id = '{$paramArray['remarktemp_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $result["field"] = $field;
                $res = array('error' => '0', 'errortip' => "编辑课堂评价模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '编辑课堂评价模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑课堂评价模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除课堂评价模版
    function delRemarktempAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_remarktemp", "remarktemp_id", "remarktemp_id = '{$paramArray['remarktemp_id']}'");
        if ($coursetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("eas_code_remarktemp", "remarktemp_id = '{$paramArray['remarktemp_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除课堂评价模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '删除课堂评价模版成功', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除课堂评价模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenStatusRemarkAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_code_remarktemp", "remarktemp_id", "remarktemp_id = '{$paramArray['remarktemp_id']}'");
        if ($activityOne) {
            $data = array();
            $data['remarktemp_status'] = $paramArray['remarktemp_status'];

            $field = array();
            $field['remarktemp_status'] = '是否启用';

            if ($this->DataControl->updateData("eas_code_remarktemp", "remarktemp_id = '{$paramArray['remarktemp_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '是否启用课堂评价模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //课堂评价类型列表
    function remarktempType($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.remarktemp_type_name  like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                t.remarktemp_type_id,
                t.remarktemp_type_name,
	            t.remarktemp_type_status,
	            t.remarktemp_type_status as remarktemp_type_status_name
            FROM
                eas_code_remarktemp_type AS t
            WHERE {$datawhere} and t.company_id = '{$paramArray['company_id']}' 
            LIMIT {$pagestart},{$num}";
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($coursecatList as &$val) {
                $val['remarktemp_type_status'] = $status[$val['remarktemp_type_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(t.remarktemp_type_id)
            FROM
                eas_code_remarktemp_type AS t
            WHERE
                {$datawhere} and t.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('remarktemp_type_id ', 'remarktemp_type_name ', 'remarktemp_type_status');
        $fieldname = $this->LgArraySwitch(array('id', '类别名称', '启用状态'));
        $fieldcustom = array("0", "1", "1");
        $fieldshow = array("0", "1", "1");
        $fieldiisswitch = array("0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无课堂评价模版信息", 'result' => $result);
        }

        return $res;

    }

    //新增课堂类型模版
    function addRemarktempTypeAction($paramArray)
    {
        $data = array();
        $data['remarktemp_type_name'] = $paramArray['remarktemp_type_name'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['remarktemp_type_name'] = "类型名称";
        $field['company_id'] = "所属公司";

        if ($id = $this->DataControl->insertData('eas_code_remarktemp_type', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增课堂类型模版成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '新增课堂类型模版', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新增课堂类型模版失败', 'result' => $result);
        }
        return $res;
    }

    //编辑课堂类型模版
    function updateRemarktempTypeAction($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("eas_code_remarktemp_type", "remarktemp_type_id", "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'");
        if ($policyDetailOne) {
            $data = array();
            $data['remarktemp_type_name'] = $paramArray['remarktemp_type_name'];

            $field = array();
            $field['remarktemp_type_name'] = "类型名称";

            if ($this->DataControl->updateData("eas_code_remarktemp_type", "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $result["field"] = $field;
                $res = array('error' => '0', 'errortip' => "编辑课堂类型模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '编辑课堂类型模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑课堂类型模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除课堂类型模版
    function delRemarktempTypeAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_remarktemp_type", "remarktemp_type_id", "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'");
        if ($coursetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }

            $a = $this->DataControl->getFieldOne('eas_code_remarktemp', 'remarktemp_id', "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该类型已被使用，无法删除！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("eas_code_remarktemp_type", "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除课堂类型模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '删除课堂类型模版', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除课堂类型模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenStatusRemarkTypeAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_code_remarktemp_type", "remarktemp_type_id", "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'");
        if ($activityOne) {
            $data = array();
            $data['remarktemp_type_status'] = $paramArray['remarktemp_type_status'];

            $field = array();
            $field['remarktemp_type_status'] = '是否启用';

            if ($this->DataControl->updateData("eas_code_remarktemp_type", "remarktemp_type_id = '{$paramArray['remarktemp_type_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '是否启用课堂类型', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //作业评价模版列表
    function homeworktemp($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['homeworktemp_type_id']) && $paramArray['homeworktemp_type_id'] !== "") {
            $datawhere .= " and r.homeworktemp_type_id ={$paramArray['homeworktemp_type_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                r.homeworktemp_id,
                r.homeworktemp_type_id,
                r.homeworktemp_title,
                r.homeworktemp_remark,
                t.homeworktemp_type_name,
	            r.homeworktemp_status,
	            r.homeworktemp_status as homeworktemp_status_name
            FROM
                eas_code_homeworktemp AS r
                LEFT JOIN eas_code_homeworktemp_type AS t ON r.homeworktemp_type_id = t.homeworktemp_type_id
            WHERE {$datawhere} and r.company_id = '{$paramArray['company_id']}' 
            LIMIT {$pagestart},{$num}";
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($coursecatList as &$val) {
                $val['homeworktemp_status'] = $status[$val['homeworktemp_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(r.homeworktemp_id)
            FROM
                eas_code_homeworktemp AS r
                LEFT JOIN eas_code_homeworktemp_type AS t ON r.homeworktemp_type_id = t.homeworktemp_type_id
            WHERE
                {$datawhere} and r.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('homeworktemp_type_id ', 'homeworktemp_type_name ', 'homeworktemp_title', 'homeworktemp_remark', 'homeworktemp_status');
        $fieldname = $this->LgArraySwitch(array('id', '类别', '名称', '描述', '启用状态'));
        $fieldcustom = array("0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无作业评价模版信息", 'result' => $result);
        }

        return $res;

    }

    //新增作业评价模版
    function addHomeworktempAction($paramArray)
    {
        $data = array();
        $data['homeworktemp_type_id'] = $paramArray['homeworktemp_type_id'];
        $data['homeworktemp_title'] = $paramArray['homeworktemp_title'];
        $data['homeworktemp_remark'] = $paramArray['homeworktemp_remark'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['homeworktemp_type_id'] = "模板类型";
        $field['homeworktemp_title'] = "模板标题";
        $field['homeworktemp_remark'] = "模板内容";
        $field['company_id'] = "所属公司";

        if ($id = $this->DataControl->insertData('eas_code_homeworktemp', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增作业评价模版成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '新增作业评价模版', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新增作业评价模版失败', 'result' => $result);
        }
        return $res;
    }

    //编辑作业评价模版
    function updateHomeworktempAction($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("eas_code_homeworktemp", "homeworktemp_id", "homeworktemp_id = '{$paramArray['homeworktemp_id']}'");
        if ($policyDetailOne) {
            $data = array();
            $data['homeworktemp_type_id'] = $paramArray['homeworktemp_type_id'];
            $data['homeworktemp_title'] = $paramArray['homeworktemp_title'];
            $data['homeworktemp_remark'] = $paramArray['homeworktemp_remark'];

            $field = array();
            $field['homeworktemp_type_id'] = "模板类型";
            $field['homeworktemp_title'] = "模板标题";
            $field['homeworktemp_remark'] = "模板内容";
            $field['company_id'] = "所属公司";

            if ($this->DataControl->updateData("eas_code_homeworktemp", "homeworktemp_id = '{$paramArray['homeworktemp_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $result["field"] = $field;
                $res = array('error' => '0', 'errortip' => "编辑作业评价模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '编辑作业评价模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑作业评价模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除作业评价模版
    function delHomeworktempAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_homeworktemp", "homeworktemp_id", "homeworktemp_id = '{$paramArray['homeworktemp_id']}'");
        if ($coursetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("eas_code_homeworktemp", "homeworktemp_id = '{$paramArray['homeworktemp_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除作业评价模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '删除作业评价模版', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除作业评价模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenStatusHomeworkAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_code_homeworktemp", "homeworktemp_id", "homeworktemp_id = '{$paramArray['homeworktemp_id']}'");
        if ($activityOne) {
            $data = array();
            $data['homeworktemp_status'] = $paramArray['homeworktemp_status'];

            $field = array();
            $field['homeworktemp_status'] = '是否启用';

            if ($this->DataControl->updateData("eas_code_homeworktemp", "homeworktemp_id = '{$paramArray['homeworktemp_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '是否启用作业评价模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //作业评价类型列表
    function HomeworkType($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.homeworktemp_type_name  like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                t.homeworktemp_type_id,
                t.homeworktemp_type_name,
	            t.homeworktemp_type_status,
	            t.homeworktemp_type_status as homeworktemp_type_status_name
            FROM
                eas_code_homeworktemp_type AS t
            WHERE {$datawhere} and t.company_id = '{$paramArray['company_id']}' 
            LIMIT {$pagestart},{$num}";
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($coursecatList as &$val) {
                $val['homeworktemp_type_status'] = $status[$val['homeworktemp_type_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(t.homeworktemp_type_id)
            FROM
                eas_code_homeworktemp_type AS t
            WHERE
                {$datawhere} and t.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('homeworktemp_type_name ', 'homeworktemp_type_status');
        $fieldname = $this->LgArraySwitch(array('类别名称', '启用状态'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");
        $fieldiisswitch = array("0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无课堂评价类型信息", 'result' => $result);
        }

        return $res;

    }

    //新增作业类型模版
    function addHomeworkTypeAction($paramArray)
    {
        $data = array();
        $data['homeworktemp_type_name'] = $paramArray['homeworktemp_type_name'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['homeworktemp_type_name'] = "类型名称";
        $field['company_id'] = "所属公司";

        if ($id = $this->DataControl->insertData('eas_code_homeworktemp_type', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增作业类型模版成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '新增作业类型模版', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '新增作业类型模版失败', 'result' => $result);
        }
        return $res;
    }

    //编辑作业类型模版
    function updateHomeworkTypeAction($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("eas_code_homeworktemp_type", "homeworktemp_type_id", "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'");
        if ($policyDetailOne) {
            $data = array();
            $data['homeworktemp_type_name'] = $paramArray['homeworktemp_type_name'];

            $field = array();
            $field['homeworktemp_type_name'] = "类型名称";

            if ($this->DataControl->updateData("eas_code_homeworktemp_type", "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $result["field"] = $field;
                $res = array('error' => '0', 'errortip' => "编辑作业类型模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '编辑作业类型模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑作业类型模版成功', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除作业类型模版
    function delHomeworkTypeAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_homeworktemp_type", "homeworktemp_type_id", "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'");
        if ($coursetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }

            $a = $this->DataControl->getFieldOne('eas_code_homeworktemp', 'homeworktemp_id', "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该类型已被使用，无法删除！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("eas_code_homeworktemp_type", "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除作业类型模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '删除作业类型模版', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除作业类型模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenStatusHomeworkTypeAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_code_homeworktemp_type", "homeworktemp_type_id", "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'");
        if ($activityOne) {
            $data = array();
            $data['homeworktemp_type_status'] = $paramArray['homeworktemp_type_status'];

            $field = array();
            $field['homeworktemp_type_status'] = '是否启用';

            if ($this->DataControl->updateData("eas_code_homeworktemp_type", "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '是否启用作业类型', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //学员沟通模版列表
    function comtempList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (r.comtemp_title like '%{$paramArray['keyword']}%' or r.comtemp_content like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                r.comtemp_id,
                r.comtemp_title,
                r.comtemp_status,
                r.comtemp_content, 
	            r.comtemp_status as comtemp_status_name
            FROM
                eas_code_comtemp AS r
            WHERE {$datawhere} and r.company_id = '{$paramArray['company_id']}' 
            LIMIT {$pagestart},{$num}";
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
            $status = array("0" => "0", "1" => "1");
            foreach ($coursecatList as &$val) {
                $val['comtemp_status'] = $status[$val['comtemp_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(r.comtemp_id)
            FROM
                eas_code_comtemp AS r
            WHERE
                {$datawhere} and r.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('comtemp_title', 'comtemp_content', 'comtemp_status');
        $fieldname = $this->LgArraySwitch(array('名称', '描述', '启用状态'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");
        $fieldiisswitch = array("0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无学员沟通模版", 'result' => $result);
        }

        return $res;

    }

    //添加学员沟通模版
    function addComtempAction($paramArray)
    {
        $data = array();
        $data['comtemp_title'] = $paramArray['comtemp_title'];
        $data['comtemp_content'] = $paramArray['comtemp_content'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['comtemp_title'] = "模板标题";
        $field['comtemp_content'] = "模板内容";
        $field['company_id'] = "所属公司";

        if ($id = $this->DataControl->insertData('eas_code_comtemp', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加学员沟通模版成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '添加学员沟通模版成功', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加学员沟通模版失败', 'result' => $result);
        }
        return $res;
    }

    //编辑学员沟通模版
    function updateComtempAction($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("eas_code_comtemp", "comtemp_id", "comtemp_id = '{$paramArray['comtemp_id']}'");
        if ($policyDetailOne) {
            $data = array();
            $data['comtemp_title'] = $paramArray['comtemp_title'];
            $data['comtemp_content'] = $paramArray['comtemp_content'];

            $field = array();
            $field['comtemp_title'] = "模板标题";
            $field['comtemp_content'] = "模板内容";

            if ($this->DataControl->updateData("eas_code_comtemp", "comtemp_id = '{$paramArray['comtemp_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $result["field"] = $field;
                $res = array('error' => '0', 'errortip' => "编辑学员沟通模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '编辑学员沟通模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑学员沟通模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除学员沟通模版
    function delComtempAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("eas_code_comtemp", "comtemp_id", "comtemp_id = '{$paramArray['comtemp_id']}'");
        if ($coursetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("eas_code_comtemp", "comtemp_id = '{$paramArray['comtemp_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除学员沟通模版成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '删除学员沟通模版', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除学员沟通模版失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenComtempStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("eas_code_comtemp", "comtemp_id", "comtemp_id = '{$paramArray['comtemp_id']}'");
        if ($activityOne) {
            $data = array();
            $data['comtemp_status'] = $paramArray['comtemp_status'];

            $field = array();
            $field['comtemp_status'] = '是否启用';

            if ($this->DataControl->updateData("eas_code_comtemp", "comtemp_id = '{$paramArray['comtemp_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->叮当助教相关设置", '是否启用学员沟通模版', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    function addCourseType($reqeust)
    {
        $this->error = 1;
        $this->errortip = "该功能暂停使用";
        return false;
//        $ccourseList = $this->DataControl->selectClear("select course_id from smc_course where coursetype_id='{$reqeust['coursetype_id']}' and company_id='{$reqeust['company_id']}' and course_id not in (select DISTINCT ch.course_id from smc_coursehour as ch where ch.company_id='{$reqeust['company_id']}' )");
//        if($ccourseList){
//            foreach ($ccourseList as $key => $value){
//                $data = array();
//                $data['company_id'] = $reqeust['company_id'];
//                $data['course_id'] = $value['course_id'];
//                $this->addCourseHourBranch($data);
//            }
//            $this->error = 0;
//            $this->errortip = "新增成功";
//            return true;
//        }else{
//            $this->error = 1;
//            $this->errortip = "未查询到课程";
//            return false;
//        }
    }


    function addCourseHourBranch($reqeust)
    {
        $this->error = 1;
        $this->errortip = "该功能暂停使用";
        return false;
//        $datawhere = "company_id='{$reqeust['company_id']}' and course_isuseeas =1 and course_id ='{$reqeust['course_id']}'";
//        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_classnum,course_id,course_branch", "{$datawhere}");
//        if ($courseOne) {
//            for ($i = 1; $i <= $courseOne['course_classnum']; $i++) {
//                $data = array();
//                $data['course_id'] = $courseOne['course_id'];
//                $data['company_id'] = $reqeust['company_id'];
//                $data['coursehour_isfilleas'] = '1';
//                $data['coursehour_branch'] = $courseOne['course_branch'] . '_' . $i;
//                $data['coursehour_sort'] = $i;
//
//                if ($courseHourOne = $this->DataControl->getFieldOne("smc_coursehour", "coursehour_id", "course_id='{$courseOne['course_id']}' and coursehour_branch = concat('{$courseOne['course_branch']}','-',$i) ")) {
//
//                    $this->DataControl->updateData("smc_coursehour", "coursehour_id='{$courseHourOne['coursehour_id']}'", $data);
//                } else {
//
//                    $this->DataControl->insertData("smc_coursehour", $data);
//                }
//
//
//            }
//            $this->error = 0;
//            $this->errortip = "更新课程{$courseOne['course_branch']}成功";
//            return true;
//        } else {
//            $this->error = 1;
//            $this->errortip = "未查询到课次";
//            return false;
//        }
    }


    //形容词列表
    function noun($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (n.noun_word like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['hourcomment_level']) && $paramArray['hourcomment_level'] !== "") {
            $datawhere .= " and n.hourcomment_level ='{$paramArray['hourcomment_level']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                n.noun_id,
                n.hourcomment_level,
                n.hourcomment_level as hourcomment_level_name,
                n.noun_word
            FROM
                eas_student_hourcomment_noun AS n
            WHERE {$datawhere} and n.company_id = '{$paramArray['company_id']}' 
            LIMIT {$pagestart},{$num}";
        $nounList = $this->DataControl->selectClear($sql);

        if ($nounList) {
            $status = array("0" => "非常满意", "1" => "一般", "2" => "不满意");
            foreach ($nounList as &$val) {
                $val['hourcomment_level_name'] = $status[$val['hourcomment_level_name']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(n.noun_id)
            FROM
                eas_student_hourcomment_noun AS n
            WHERE
                {$datawhere} and n.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('noun_word ', 'hourcomment_level_name');
        $fieldname = $this->LgArraySwitch(array('标签名称', '所属满意度'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($nounList) {
            $result['list'] = $nounList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无形容词", 'result' => $result);
        }

        return $res;

    }

    //添加形容词
    function addNounAction($paramArray)
    {
        $data = array();

        $nounlList = json_decode(stripslashes($paramArray['noun']), true);
        foreach ($nounlList as $item) {
            $data['company_id'] = $paramArray['company_id'];
            $data['hourcomment_level'] = $item['hourcomment_level'];
            $data['noun_word'] = $item['noun_word'];

            $a = $this->DataControl->getFieldOne('eas_student_hourcomment_noun', 'noun_id', "company_id = '{$paramArray['company_id']}' and hourcomment_level = '{$item['hourcomment_level']}' and noun_word = '{$item['noun_word']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
            }
            $this->DataControl->insertData('eas_student_hourcomment_noun', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加形容词成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->叮铛助教相关设置", '添加形容词', dataEncode($paramArray));

        return $res;
    }

    //编辑形容词
    function updateNounAction($paramArray)
    {
        $nounOne = $this->DataControl->getFieldOne("eas_student_hourcomment_noun", "noun_id", "noun_id = '{$paramArray['noun_id']}'");
        if ($nounOne) {
            $data = array();
            $data['hourcomment_level'] = $paramArray['hourcomment_level'];
            $data['noun_word'] = $paramArray['noun_word'];

            $field = array();
            $field['noun_word'] = "形容词";
            $field['hourcomment_level'] = "满意程度";

            if ($this->DataControl->updateData("eas_student_hourcomment_noun", "noun_id = '{$paramArray['noun_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $result["field"] = $field;
                $res = array('error' => '0', 'errortip' => "编辑形容词成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->叮铛助教相关设置", '编辑形容词', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑形容词失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除形容词
    function delNounAction($paramArray)
    {
        $nounOne = $this->DataControl->getFieldOne("eas_student_hourcomment_noun", "noun_id", "noun_id = '{$paramArray['noun_id']}'");
        if ($nounOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }

//            $a = $this->DataControl->getFieldOne('eas_code_homeworktemp', 'homeworktemp_id', "homeworktemp_type_id = '{$paramArray['homeworktemp_type_id']}'");
//            if ($a) {
//                ajax_return(array('error' => 1, 'errortip' => "该类型已被使用，无法删除！"), $this->companyOne['company_language']);
//            }
            if ($this->DataControl->delData("eas_student_hourcomment_noun", "noun_id = '{$paramArray['noun_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除形容词成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->叮铛助教相关设置", '删除形容词', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除形容词失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


}
