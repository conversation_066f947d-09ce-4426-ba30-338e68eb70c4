<?php


namespace Model\Gmc;

class GuildstuModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //同业学员申请列表
    function getGuildstuApply($request)
    {
        $datawhere = "1  and a.company_id = '{$this->company_id}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or a.apply_contract like '%{$request['keyword']}%' )";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id='{$request['school_id']}'";
        }

        if (isset($request['guildstutype_id']) && $request['guildstutype_id'] !== '') {
            $datawhere .= " and a.guildstutype_id='{$request['guildstutype_id']}'";
        }

        if (isset($request['apply_status']) && $request['apply_status'] !== '') {
            $datawhere .= " and a.apply_status='{$request['apply_status']}'";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and a.apply_addtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'].' 23:59:59');
            $datawhere .= " and a.apply_addtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and a.apply_statustime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'].' 23:59:59');
            $datawhere .= " and a.apply_statustime <= '{$firstday}'";
        }

        //开始结束时间
        $starttime = strtotime($request['apply_starttime']);
        $endtime = strtotime($request['apply_endtime']);
        if (isset($request['apply_starttime']) && $request['apply_starttime'] != '' && isset($request['apply_endtime']) && $request['apply_endtime'] != '') {
            $datawhere .= " and a.apply_addtime <= '{$endtime}' and a.apply_addtime >= '{$starttime}'";
        } elseif (isset($request['apply_starttime']) && $request['apply_starttime'] != '') {
            $datawhere .= " and a.apply_addtime >= '{$starttime}'";
        } elseif (isset($request['apply_endtime']) && $request['apply_endtime'] != '') {
            $datawhere .= " and a.apply_addtime <= '{$endtime}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['is_count']) && $request['is_count'] == '1') {
            $sql = "SELECT  COUNT(a.apply_id) as datanum
                FROM smc_guildstu_apply as a 
                LEFT JOIN smc_student as s ON s.student_id = a.student_id 
                LEFT JOIN smc_code_guildstutype as g ON a.guildstutype_id = g.guildstutype_id
                WHERE {$datawhere}";
            $count = $this->DataControl->selectOne($sql);
            $count = $count['datanum'] + 0;
        } else {
            $count = 0;
        }

        $sql = "SELECT a.*,s.student_cnname,s.student_enname,s.student_branch,g.guildstutype_name,h.school_cnname,st.staffer_cnname,st.staffer_enname,e.enrolled_status,e.enrolled_leavetime
                ,(select max(y.clockinginlog_day) from smc_student_clockinginlog y where y.school_id=h.school_id and y.student_id=s.student_id) as last_atte_date
                FROM  smc_guildstu_apply as a 
                LEFT JOIN smc_student as s ON s.student_id = a.student_id 
                LEFT JOIN smc_school as h ON a.school_id=h.school_id
                LEFT JOIN smc_code_guildstutype as g ON a.guildstutype_id = g.guildstutype_id
                LEFT JOIN smc_student_enrolled as e ON s.student_id=e.student_id and h.school_id=e.school_id
                left join smc_staffer as st on st.staffer_id=a.apply_staff_id 
                where {$datawhere} order by a.apply_id DESC";

        $enrolled_status = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "2" => "新生", "-1" => "已离校"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $status = $this->LgArraySwitch(array(0 => '未处理', 1 => '已通过', -1 => '已拒绝', -2 => '已取消'));
            foreach ($dateexcelarray as &$val) {
                $val['apply_status_name'] = $status[$val['apply_status']];
                $val['apply_addtime'] = date("Y-m-d", $val['apply_addtime']);
                if($val['apply_statustime']){
                    $val['apply_statustime'] = date("Y-m-d", $val['apply_statustime']);
                }else{
                    $val['apply_statustime'] = '--';
                }
                $val['enrolled_status'] = $enrolled_status[$val['enrolled_status']];
                $val['enrolled_leavetime'] = ($val['enrolled_leavetime'] == '' || $val['enrolled_leavetime'] == '0') ? '' : date("Y-m-d", $val['enrolled_leavetime']);
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员中文名
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];//学员英文名
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['guildstutype_name'] = $dateexcelvar['guildstutype_name'];//同业学员类型

                    $datearray['apply_contract'] = $dateexcelvar['apply_contract'];//培训协议编号
                    $datearray['apply_payprice'] = $dateexcelvar['apply_payprice'];//缴费金额
                    $datearray['apply_paytime'] = $dateexcelvar['apply_paytime'];//合同缴费时间
                    $datearray['apply_status_name'] = $dateexcelvar['apply_status_name'];//审核状态
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];//审核人
                    $datearray['apply_addtime'] = $dateexcelvar['apply_addtime'];//申请时间
                    $datearray['enrolled_status'] = $dateexcelvar['enrolled_status'];//学生状态
                    $datearray['enrolled_leavetime'] = $dateexcelvar['enrolled_leavetime'];//流失时间
                    $datearray['last_atte_date'] = $dateexcelvar['last_atte_date'];//最后考勤日期
                    $datearray['apply_statustime'] = $dateexcelvar['apply_statustime'];//审核时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '学员中文名', '学员英文名', '学员编号', '同业学员类型', '培训协议编号', '缴费金额', '合同缴费时间', '审核状态', '审核人', '申请时间', '学生状态', '流失日期', '最后考勤日期', '审核时间'));
            $excelfileds = array('school_cnname', 'student_cnname', 'student_enname', 'student_branch', 'guildstutype_name', 'apply_contract', 'apply_payprice', 'apply_paytime', 'apply_status_name', 'staffer_cnname', 'apply_addtime', 'enrolled_status', 'enrolled_leavetime', 'last_atte_date', 'apply_statustime');

            $fielname = $this->LgStringSwitch("公益学员审核");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $applyList = $this->DataControl->selectClear($sql);
        }

        $status = $this->LgArraySwitch(array(0 => '未处理', 1 => '已通过', -1 => '已拒绝', -2 => '已取消'));
        if ($applyList) {
            foreach ($applyList as &$applyVar) {

                $applyVar['staffer_cnname']=$applyVar['staffer_enname']?$applyVar['staffer_cnname'].'-'.$applyVar['staffer_enname']:$applyVar['staffer_cnname'];
                $applyVar['apply_status_name'] = $status[$applyVar['apply_status']];
                $applyVar['apply_addtime'] = date("Y-m-d", $applyVar['apply_addtime']);
                if($applyVar['apply_statustime']){
                    $applyVar['apply_statustime'] = date("Y-m-d", $applyVar['apply_statustime']);
                }else{
                    $applyVar['apply_statustime'] = '--';
                }
                $applyVar['enrolled_status'] = $enrolled_status[$applyVar['enrolled_status']];
                $applyVar['enrolled_leavetime'] = ($applyVar['enrolled_leavetime'] == '' || $applyVar['enrolled_leavetime'] == '0') ? '' : date("Y-m-d", $applyVar['enrolled_leavetime']);
            }
        }

        $result = array();
        $result["datalist"] = $applyList;
        $result["count"] = $count;
        return $result;
    }

    //审核通过
    function adoptGuildstuApi($request)
    {
        $data = array();
        $data['apply_staff_id'] = $request['staffer_id'];
        $data['apply_status'] = 1;
        $data['apply_statusreason'] = $request['apply_statusreason'];
        $data['apply_statustime'] = time();
        $applyOne = $this->DataControl->updateData('smc_guildstu_apply', "apply_id='{$request['apply_id']}'", $data);
        if ($applyOne) {
            return true;
        } else {
            return false;
        }
    }

    //审核 拒绝
    function refuseGuildstuApi($request)
    {
        $data = array();
        $data['apply_staff_id'] = $request['staffer_id'];
        $data['apply_status'] = '-1';
        $data['apply_statusreason'] = $request['apply_statusreason'];
        $data['apply_statustime'] = time();
        $applyOne = $this->DataControl->updateData('smc_guildstu_apply', "apply_id='{$request['apply_id']}'", $data);
        if ($applyOne) {
            return true;
        } else {
            return false;
        }
    }

    //审核 拒绝
    function cancelGuildstuApi($request)
    {
        $data = array();
        $data['apply_status'] = '-2';
        $data['apply_statusreason'] = $request['apply_statusreason'];
        $data['apply_statustime'] = time();
        $applyOne = $this->DataControl->updateData('smc_guildstu_apply', "apply_id='{$request['apply_id']}'", $data);
        if ($applyOne) {
            return true;
        } else {
            return false;
        }
    }

    //获取单挑同业学员申请资料
    function getGuildstuOneApi($request)
    {
        $sql = "SELECT a.*,s.student_cnname,s.student_enname,s.student_branch,g.guildstutype_name FROM  smc_guildstu_apply as a 
                LEFT JOIN smc_student as s ON s.student_id = a.student_id 
                LEFT JOIN smc_code_guildstutype as g ON a.guildstutype_id = g.guildstutype_id
                where a.apply_id='{$request['apply_id']}' order by a.apply_id DESC";
        $getGuildstuOne = $this->DataControl->selectClear($sql);
        $status = $this->LgArraySwitch(array(0 => '未处理', 1 => '已通过', -1 => '已拒绝', -2 => '已取消'));
        if ($getGuildstuOne) {
            foreach ($getGuildstuOne as &$getGuildstuvar) {
                $getGuildstuvar['apply_status_name'] = $status[$getGuildstuvar['apply_status']];
                $getGuildstuvar['apply_addtime'] = date("Y-m-d H:i:s", $getGuildstuvar['apply_addtime']);

                if ($getGuildstuvar['apply_imgslist']) {
                    $firstletter = substr($getGuildstuvar['apply_imgslist'], 0, 1);
                    if ($firstletter != '[') {
                        $smcData = array();
                        $smcData['imgslist'] = $getGuildstuvar['apply_imgslist'];
                        $dataa = request_by_curl("https://api.kidcastle.com.cn/Index/idToImgslist", dataEncode($smcData), "GET", array());
//                        $dataa = request_by_curl("http://api.websitejdb102.com/Index/idToImgslist",dataEncode($smcData),"GET",array());
                        $apiArray = json_decode($dataa, '1');
                        $imgslist = json_encode($apiArray['result']['list'], JSON_UNESCAPED_SLASHES);
                        $getGuildstuvar['apply_imgslist'] = $imgslist;
                    }
                }

//                if ($_SERVER['SERVER_NAME'] == 'gmcapi.kedingdang.com') {
//                    $schoolOne = $this->DataControl->selectOne("select school_branch from smc_school WHERE school_id = '{$getGuildstuvar['school_id']}'");
//                    //2.0中5月1号之前消耗的课次  --  TIMES_COUNT
//                    $sqllao = "SELECT A.STUD_NO
//                         ,TIMES_COUNT=COUNT(A.LESN_ID)
//                         FROM CM_ClassAtte_Stud A
//                         LEFT JOIN CM_ClassDesign_Time B ON A.CLASS_NO=B.CLASS_NO AND A.LESN_ID=B.LESN_ID
//                         LEFT JOIN CM_ClassMstr C ON A.CLASS_NO=C.CLASS_NO
//                         LEFT JOIN Code_Class D ON C.CODE_CLASS=D.CODE_CLASS
//                         LEFT JOIN Code_Cat E ON D.CODE_CAT=E.CODE_CAT
//                         WHERE 1=1
//                         AND E.CAT_GROUP='美语'
//                         AND B.LESN_DATE>'20190501'
//                         AND B.LESN_DATE<'20200501'
//                         AND ISNULL(A.CR_RECE_KEY_NO,'')<>''
//                         AND A.TIMES_AMT=0
//                         AND C.STATUS=1
//                         AND B.ACTV_YN='Y'
//                         AND A.STUD_NO='{$getGuildstuvar['student_branch']}'
//                         GROUP BY A.STUD_NO";
//                    $Dbmssql = new \Dbmssql();
//                    $laoOne = $Dbmssql->find($sqllao);
//                }

                $sqlold=" select student_branch,used_times_old from smc_student_policy_old where student_branch='{$getGuildstuvar['student_branch']}'";
                $oldone = $this->DataControl->selectOne($sqlold);


                //3.0
//                $sqlnew = "SELECT E.student_id,G.school_id,count(A.hourstudy_id) AS USED_COUNT
//                            FROM smc_student_hourstudy A
//                            LEFT JOIN smc_student B ON A.student_id=B.student_id
//                            LEFT JOIN smc_class_hour C ON A.class_id=C.class_id AND A.hour_id=C.hour_id
//                            LEFT JOIN smc_student E ON A.student_id=E.student_id
//                            LEFT JOIN smc_class F ON C.class_id=F.class_id
//                            LEFT JOIN smc_school G ON F.school_id=G.school_id
//                            LEFT JOIN smc_course H ON F.course_id=H.course_id AND H.company_id=F.company_id
//                            LEFT JOIN smc_code_coursetype J ON H.coursetype_id=J.coursetype_id AND H.company_id=J.company_id
//                            WHERE 1
//                            AND E.student_id='{$getGuildstuvar['student_id']}'
//                            AND G.school_id='{$getGuildstuvar['school_id']}'
//                            AND J.coursetype_cnname='美语'
//                            AND C.hour_day>='2020-05-01'
//                            AND C.hour_isfree=0
//                            AND C.hour_iswarming=0
//                            AND NOT EXISTS(SELECT 1 FROM smc_school_income
//                            WHERE student_id='{$getGuildstuvar['student_id']}'
//                            AND hourstudy_id=A.hourstudy_id AND income_price>0)
//                            GROUP BY E.student_id,G.school_id ";
                $sqlnew = "SELECT A.student_id,count(A.hourstudy_id) AS USED_COUNT
                            FROM smc_student_hourstudy A 
                            LEFT JOIN smc_class_hour C ON A.class_id=C.class_id AND A.hour_id=C.hour_id
                            LEFT JOIN smc_class S ON A.class_id = S.class_id
                            LEFT JOIN smc_course H ON C.course_id=H.course_id 
                            WHERE 1 
                            AND A.student_id='{$getGuildstuvar['student_id']}'
                            AND H.coursetype_id='65'
                            AND C.hour_day>='2020-05-01'
                            AND C.hour_isfree=0 
                            AND C.hour_iswarming=0
                            AND S.class_type = '0'
                            AND NOT EXISTS(SELECT 1 FROM smc_school_income 
                            WHERE student_id='{$getGuildstuvar['student_id']}'
                            AND hourstudy_id=A.hourstudy_id AND income_price>0)
                            GROUP BY A.student_id ";
                $newone = $this->DataControl->selectOne($sqlnew);

//                //当前剩余已申请未使用的课程
//                "SELECT E.student_id,G.school_id,count(A.hourstudy_id) AS USED_COUNT
//                FROM smc_student_hourstudy A
//                LEFT JOIN smc_student B ON A.student_id=B.student_id
//                LEFT JOIN smc_class_hour C ON A.class_id=C.class_id AND A.hour_id=C.hour_id
//                LEFT JOIN smc_student E ON A.student_id=E.student_id
//                LEFT JOIN smc_class F ON C.class_id=F.class_id
//                LEFT JOIN smc_school G ON F.school_id=G.school_id
//                LEFT JOIN smc_course H ON F.course_id=H.course_id AND H.company_id=F.company_id
//                LEFT JOIN smc_code_coursetype J ON H.coursetype_id=J.coursetype_id AND H.company_id=J.company_id
//                WHERE 1
//                AND E.student_id='81107'
//                AND G.school_id='1037'
//                AND J.coursetype_cnname='美语'
//                AND C.hour_day>='2020-05-01'
//                AND C.hour_isfree=0
//                AND C.hour_iswarming=0
//                AND NOT EXISTS(SELECT 1 FROM smc_school_income WHERE hourstudy_id=A.hourstudy_id AND income_price>0)
//                GROUP BY E.student_id,G.school_id ";

                //当前剩余课时
                $apply_toclasstimes = $getGuildstuvar['apply_toclasstimes'] == NULL ? 0 : $getGuildstuvar['apply_toclasstimes'];
                $TIMES_COUNT = $oldone['used_times_old'] == NULL ? 0 : $oldone['used_times_old'];
                $USED_COUNT = $newone['USED_COUNT'] == NULL ? 0 : $newone['USED_COUNT'];

//                echo $apply_toclasstimes.'-'.$TIMES_COUNT.'-'.$USED_COUNT;die;
                $num = $apply_toclasstimes - $TIMES_COUNT - $USED_COUNT;
                $getGuildstuvar['statisticshour'] = $this->LgStringSwitch("当前剩余课时（申请课时 - 2.0中5月1号之前消耗的课次 - 3.0中5月1号之后消耗的课次）：") . $apply_toclasstimes . "-" . $TIMES_COUNT . "-" . $USED_COUNT . " = " . $num;

//                $getGuildstuvar['statisticshour'] = "当前剩余课时（申请课时 - 2.0中5月1号之前消耗的课次 - 3.0中5月1号消耗的课次）：" ;
            }
        }
        return $getGuildstuOne;
    }

    //同业学员类型
    function getGuildstutypeApi($request)
    {
        $sql = "SELECT guildstutype_id,guildstutype_name from smc_code_guildstutype WHERE company_id = '{$request['company_id']}' ";
        $codeList = $this->DataControl->selectClear($sql);
        return $codeList;
    }

    //获取一条学员申请基本信息
    function getGuildstuinfoApi($request)
    {
        $sql = "SELECT s.student_cnname,s.student_branch,s.student_img,s.student_sex,s.student_idcard,s.student_enname,s.student_birthday,sf.family_cnname,sf.family_mobile,sf.family_relation,b.student_balance,s.student_forwardprice,sf.family_relation,s.student_idcard,b.student_withholdbalance, 
                (select count(study_id) from smc_student_study as ss where ss.company_id='{$request['company_id']}' and ss.student_id=s.student_id and ss.study_isreading=1) as classNum,
                (select count(scb.coursebalance_id) from smc_student_coursebalance as scb where scb.student_id=s.student_id and scb.company_id='{$request['company_id']}' and (scb.coursebalance_figure>0 or scb.coursebalance_time>0)) as courseNum
                ,(select SUM(po.order_paymentprice) from smc_payfee_order as po where po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.order_status<>4 and po.order_status>0) as order_paymentprice
                ,(select SUM(po.order_paidprice) from smc_payfee_order as po where po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.order_status<>4 and po.order_status>0) as order_paidprice
                from smc_student as s 
                LEFT JOIN smc_student_balance as b ON (b.student_id = s.student_id and b.company_id='{$request['company_id']}') 
                left join smc_student_family as sf on (sf.student_id=s.student_id and sf.family_isdefault=1)
                where s.student_id = '{$request['student_id']}'  
                group by s.student_id 
                ";
        $codeList = $this->DataControl->selectOne($sql);
        $codeList['arrears'] = $codeList['order_paymentprice'] - $codeList['order_paidprice'];
        $codeList['show_balance'] = $codeList['student_balance'] + $codeList['student_withholdbalance'];
        return $codeList;
    }

    //获取单个就读课程数据
    function getStuWastingClassApi($request)
    {
        $sql = " SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,t.study_beginday,t.study_endday,t.class_id,c.class_cnname,c.class_enname,c.class_branch,u.course_cnname,u.course_branch,
                (SELECT l.school_cnname FROM smc_school as l WHERE l.school_id = t.school_id) as school_cnname 
                from smc_student as s 
                LEFT JOIN smc_student_study as t ON s.student_id = t.student_id  
                LEFT JOIN smc_class as c ON t.class_id = c.class_id 
                LEFT JOIN smc_course as u ON c.course_id = u.course_id 
                WHERE s.student_id = '{$request['student_id']}' and s.company_id='{$request['company_id']}' ";
        $hourlist = $this->DataControl->selectClear($sql);

        $allstudyhour = 0;
        if ($hourlist) {
            foreach ($hourlist as &$hourvar) {
                $hourname = $this->DataControl->selectOne("select h.hour_name from smc_class_hour as h WHERE h.hour_iswarming = '0' and h.hour_ischecking >= '0' and h.class_id ='{$hourvar['class_id']}' ORDER BY hour_lessontimes ASC limit 0,1");
                $successhour = $this->DataControl->selectOne("select count(h.hour_id) as num from smc_class_hour as h WHERE h.hour_iswarming = '0' and h.hour_ischecking = '1' and h.class_id = '{$hourvar['class_id']}'");
                $allhour = $this->DataControl->selectOne("select count(h.hour_id) as num from smc_class_hour as h WHERE h.hour_iswarming = '0' and h.hour_ischecking >= '0' and h.class_id = '{$hourvar['class_id']}'");
                $studyhour = $this->DataControl->selectOne("select count(d.hourstudy_id) as num from smc_student_hourstudy as d WHERE d.student_id = '{$hourvar['student_id']}' and d.class_id = '{$hourvar['class_id']}'");

                $hourvar['hourname'] = $hourname['hour_name'];
                $hourvar['successhour'] = $successhour['num'];
                $hourvar['allhour'] = $allhour['num'];
                $hourvar['studyhour'] = $studyhour['num'];

                $allstudyhour += $hourvar['studyhour'];

            }
            $result = array();
            $result['list'] = $hourlist;
            $result['allstudyhour'] = $allstudyhour;
            return $result;
        } else {
            $result = array();
            $result['list'] = $hourlist;
            $result['allstudyhour'] = 0;
            return $result;
        }
    }


}