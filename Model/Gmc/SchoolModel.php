<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  SchoolModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile,account_class", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $paramArray
     * @return array
     *   获取校园列表
     */
    function getSchoolList($paramArray)
    {

        $datawhere = "s.company_id = '{$paramArray['company_id']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' 
            or s.school_branch like '%{$paramArray['keyword']}%' 
            or s.school_shortname like '%{$paramArray['keyword']}%' 
            or s.school_enname like '%{$paramArray['keyword']}%' 
            or s.school_address like '%{$paramArray['keyword']}%' 
            or s.school_phone like '%{$paramArray['keyword']}%' 
            or g.region_name like '%{$paramArray['keyword']}%' 
            or i.region_name like '%{$paramArray['keyword']}%' 
            or n.region_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== "") {
            $datawhere .= " and d.district_id = '{$paramArray['district_id']}'";
        }
        if (isset($paramArray['school_type']) && $paramArray['school_type'] !== "") {
            $datawhere .= " and s.school_type = '{$paramArray['school_type']}'";
        }
        if (isset($paramArray['school_istest']) && $paramArray['school_istest'] !== "") {
            $datawhere .= " and s.school_istest = '{$paramArray['school_istest']}'";
        }
        if (isset($paramArray['school_isclose']) && $paramArray['school_isclose'] !== "") {
            $datawhere .= " and s.school_isclose = '{$paramArray['school_isclose']}'";
        }
        if (isset($paramArray['school_issubject']) && $paramArray['school_issubject'] !== "") {
            $datawhere .= " and s.school_issubject = '{$paramArray['school_issubject']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                s.school_id,
                s.school_crmtobi,
                s.school_teachtobi,
                s.school_branch,
                s.school_type,
                s.school_cnname,
                s.school_enname,
                s.school_shortname,
                s.school_address,
                s.school_erpshopcode,
                s.school_phone,
                s.school_isclose,
                s.school_isclose as school_isclose_name,
                d.district_cnname,
                d.district_id,
                school_province,
                school_city,
                s.companies_id,
                s.school_cmbshopcode,
                s.school_upcshopcode,
                s.school_istwocode,
                s.school_istwocode as school_istwocode_name,
                s.school_isnewschool,
                s.school_isnewschool as school_isnewschool_name,
                s.school_ismove,
                s.school_isvoucher,
                s.school_isvoucher as school_isvoucher_name,
                s.school_ismove as school_ismove_name,
                s.school_newstart,
                s.school_isprotocol,
                s.school_newend,
                s.school_sort,
                s.school_isscan,
                s.school_opclass,
                s.school_inclass,
                s.school_remark,
                s.school_renewtime,
                s.school_changestime,
                s.school_renewsttime,
                s.school_renewentime,
                s.school_appointment,
                s.school_openclass,
                s.school_duration,
                s.school_signet,
                s.school_coursetime,
                s.school_changesentime,
                s.school_changessttime,
                s.pospattern_id,
                p.pospattern_name,
                school_area,
                school_istest,
                c.companies_cnname,
                g.region_name  as province,
                i.region_name  as city,
                n.region_name  as area,
                s.school_periodauthority,
                s.school_vouchermode,
                s.school_autochecking,s.school_iscreateroom,
                s.school_monitorcode,
                s.school_istemporaryclose,
                s.school_temporaryclosetip,
                s.school_payaftersignlimit,
                s.school_liaison,
                s.school_examine,
                s.school_register,
                s.school_permitbranch,
                s.school_permitstday,
                s.school_icp,
                s.school_licensestday,
                s.school_society,
                s.school_issubject,
                s.school_isforwardapply,
                s.school_foundtime
            FROM
                smc_school AS s LEFT JOIN gmc_company_organizeschool AS o ON o.school_id = s.school_id 
                left join gmc_company_district as d on d.district_id = s.district_id
                left join gmc_code_companies as c on c.companies_id = s.companies_id
                left join smc_code_pospattern as p on p.pospattern_id = s.pospattern_id 
                LEFT join smc_code_region as g ON s.school_province = g.region_id 
                LEFT join smc_code_region as i ON s.school_city = i.region_id 
                LEFT join smc_code_region as n ON s.school_area = n.region_id 
            WHERE {$datawhere} GROUP BY s.school_id
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";

        $status = array("0" => "否", "1" => "是");

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(DISTINCT s.school_id)
            FROM
                smc_school AS s LEFT JOIN gmc_company_organizeschool AS o ON o.school_id = s.school_id
                left join gmc_company_district as d on d.district_id = s.district_id 
                left join gmc_code_companies as c on c.companies_id = s.companies_id
                LEFT join smc_code_region as g ON s.school_province = g.region_id 
                LEFT join smc_code_region as i ON s.school_city = i.region_id 
                LEFT join smc_code_region as n ON s.school_area = n.region_id 
            WHERE {$datawhere} ");
        $allnums = $all_num[0][0];

        $schoolNum = $this->DataControl->select("
            SELECT
               COUNT(DISTINCT s.school_id)
            FROM
                smc_school AS s LEFT JOIN gmc_company_organizeschool AS o ON o.school_id = s.school_id
                left join gmc_company_district as d on d.district_id = s.district_id 
            WHERE s.company_id = '{$paramArray['company_id']}'");
        $schoolNums = $schoolNum[0][0];

        $fieldstring = array('school_cnname', 'school_enname', 'school_shortname', 'school_branch', 'district_cnname', 'province', 'city', 'area', 'school_address', 'school_phone', 'school_isclose_name', 'school_cmbshopcode', 'school_upcshopcode', 'school_erpshopcode', 'companies_cnname', 'pospattern_name', 'school_isnewschool_name', 'school_ismove_name', 'school_newstart', 'school_newend', 'school_istwocode_name', 'school_autochecking_name', 'school_monitorcode', 'school_sort', 'school_payaftersignlimit', 'school_issubject', 'school_foundtime');
        $fieldname = $this->LgArraySwitch(array('校园全称', '检索代码', '校园简称', '校园编号', '所属区域', '省', '市', '区', '校园地址', '联系电话', '是否关校', '招行门店编号', '银联门店编号', 'ERP对接编号', '所属企业', 'POS机模式', '是否新校', '是否搬校', '新校开始时间', '新校结束时间', '是否有缴费二维码', '是否开启自动考勤', '监控主机编号', '校园排序', '课程协议是否签完字才可支付', '是否学科类学校', '创校时间'));
        $fieldcustom = array("1", "1", "1", "1", "0", "0", "0", "0", "1", "1", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", '0', "0", '1', '0', '1', '1');
        $fieldshow = array("1", "1", "1", "1", "0", "0", "0", "0", "1", "1", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", '0', "0", '1', '0', '1', '1');
        $fieldswitch = array("0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", '0', "0", '0', '0', '1', '0');


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $schoolList = $this->DataControl->selectClear($sql);
            if (!$schoolList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($schoolList) {
                $outexceldate = array();
                foreach ($schoolList as $schoolvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $schoolvar['school_cnname'];
                    $datearray['school_enname'] = $schoolvar['school_enname'];
                    $datearray['school_shortname'] = $schoolvar['school_shortname'];
                    $datearray['school_branch'] = $schoolvar['school_branch'];
                    $datearray['district_cnname'] = $schoolvar['district_cnname'];
                    $datearray['province'] = $schoolvar['province'];
                    $datearray['city'] = $schoolvar['city'];
                    $datearray['area'] = $schoolvar['area'];

                    $datearray['school_address'] = $schoolvar['school_address'];
                    $datearray['school_phone'] = $schoolvar['school_phone'];
                    $datearray['school_isclose_name'] = $status[$schoolvar['school_isclose_name']];
                    $datearray['school_cmbshopcode'] = $schoolvar['school_cmbshopcode'];
                    $datearray['school_upcshopcode'] = $schoolvar['school_upcshopcode'];
                    $datearray['school_erpshopcode'] = $schoolvar['school_erpshopcode'];
                    $datearray['companies_cnname'] = $schoolvar['companies_cnname'];
                    $datearray['pospattern_name'] = $schoolvar['pospattern_name'];

                    $datearray['school_isnewschool_name'] = $status[$schoolvar['school_isnewschool_name']];
                    $datearray['school_ismove_name'] = $status[$schoolvar['school_ismove_name']];
                    $datearray['school_newstart'] = $schoolvar['school_newstart'];
                    $datearray['school_newend'] = $schoolvar['school_newend'];
                    $datearray['school_istwocode_name'] = $status[$schoolvar['school_istwocode_name']];
                    $datearray['school_autochecking_name'] = $schoolvar['school_autochecking'] == 1 ? '是' : '否';
                    $datearray['school_monitorcode'] = $schoolvar['school_monitorcode'];
                    $datearray['school_isprotocol'] = $status[$schoolvar['school_isprotocol']];
                    $datearray['school_isvoucher_name'] = $status[$schoolvar['school_isvoucher_name']];
                    $datearray['school_sort'] = $schoolvar['school_sort'];

                    $datearray['atte_times'] = $schoolvar['atte_times'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelfileds = array('school_cnname', 'school_enname', 'school_shortname', 'school_branch', 'district_cnname', 'province', 'city', 'area'
            , 'school_address', 'school_phone', 'school_isclose_name', 'school_cmbshopcode', 'school_upcshopcode', 'school_erpshopcode', 'companies_cnname', 'pospattern_name'
            , 'school_isnewschool_name', 'school_ismove_name', 'school_newstart', 'school_newend', 'school_istwocode_name', 'school_autochecking_name', 'school_monitorcode', 'school_isprotocol', 'school_isvoucher_name', 'school_sort');
            $excelheader = $this->LgArraySwitch(array('校园全称', '检索代码', '校园简称', '校园编号', '所属区域', '省', '市', '区'
            , '校园地址', '联系电话', '是否关校', '招行门店编号', '银联门店编号', 'ERP对接编号', '所属企业', 'POS机模式'
            , '是否新校', '是否搬校', '新校开始时间', '新校结束时间', '是否有缴费二维码', '是否开启自动考勤', '监控主机编号', '是否开启合同', '是否可以下载凭证', '校园排序'));

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("学校列表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $schoolList = $this->DataControl->selectClear($sql);
            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
                $field[$i]["switchVisible"] = trim($fieldswitch[$i]);
            }

            if ($schoolList) {
                foreach ($schoolList as &$val) {
                    $val['school_autochecking_name'] = $val['school_autochecking'] == 1 ? '是' : '否';
                    $val['school_isclose_name'] = $status[$val['school_isclose_name']];
                    $val['school_isnewschool_name'] = $status[$val['school_isnewschool_name']];
                    $val['school_ismove_name'] = $status[$val['school_ismove_name']];
                    $val['school_isvoucher_name'] = $status[$val['school_isvoucher_name']];
                    $val['school_istwocode_name'] = $status[$val['school_istwocode_name']];
                    $val['school_course_arr'] = array();
                    if ($val['pospattern_id'] == '0' || $val['pospattern_id'] == null) {
                        $val['pospattern_id'] = '';
                    }
                    $companies = $this->DataControl->selectClear("SELECT cc.companies_id,cc.companies_cnname,sc.companies_storenumber,companies_headstorenumber,conpanies_headuserid,companies_subStoreId FROM smc_school_companies as sc LEFT JOIN gmc_code_companies as cc ON cc.companies_id = sc.companies_id WHERE sc.school_id = '{$val['school_id']}'");
                    if ($companies) {
                        $val['companies'] = $companies;
                    } else {
                        $val['companies'] = array();
                    }
                }
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;

            if ($schoolList) {
                $result['list'] = $schoolList;
            } else {
                $result['list'] = array();
            }

            $result['district'] = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$paramArray['company_id']}'");

            $result['companies'] = $this->DataControl->selectClear("select companies_id,companies_cnname from gmc_code_companies where company_id = '{$paramArray['company_id']}'");


            $maxschoolnum = $this->DataControl->select("select contract_maxschoolnum from imc_sales_contract WHERE company_id = '{$paramArray['company_id']}'");

            $isbranch = $this->DataControl->getFieldOne("gmc_company", "company_isscbranch", "company_id = '{$paramArray['company_id']}'");

            $maxschoolnums = $maxschoolnum[0][0];
            $result['maxschoolnum'] = $maxschoolnums == null ? "未设置上线" : $maxschoolnums;

            $result['all_num'] = $allnums;

            $result['schoolNum'] = $schoolNums;

            $result['company_isscbranch'] = $isbranch['company_isscbranch'];

            if ($result['list']) {
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => "暂无校园信息", 'result' => $result);
            }
            return $res;
        }
    }


    function getSchoolCompanies($paramArray)
    {

        $sql = "select s.school_cnname,s.school_enname,s.school_shortname,s.school_branch,co.companies_cnname,co.companies_branch,B.coursecat_cnname,B.coursecat_branch,C.coursetype_cnname,C.coursetype_branch,D.companies_cnname as D_companies_cnname 
                from smc_school_coursecat_subject as A,smc_school as s,gmc_code_companies as co,smc_code_coursecat as B,smc_code_coursetype as C,gmc_code_companies as D
                where A.school_id=s.school_id and A.companies_id=co.companies_id and A.coursecat_id=B.coursecat_id and b.coursetype_id=C.coursetype_id and s.companies_id=D.companies_id and s.company_id='{$paramArray['company_id']}'
                ORDER BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc    
                ";

        $dateexcelarray = $this->DataControl->selectClear($sql);

        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                $datearray['school_enname'] = $dateexcelvar['school_enname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['D_companies_cnname'] = $dateexcelvar['D_companies_cnname'];
                $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                $outexceldate[] = $datearray;
            }
        }

        $excelheader = $this->LgArraySwitch(array("校园全称", "校园简称", "检索代码", "校园编号", "所属企业", "班种名称", "班组名称", "主体名称"));
        $excelfileds = array('school_cnname', 'school_shortname', 'school_enname', 'school_branch', 'D_companies_cnname', 'coursecat_cnname', 'coursetype_cnname', 'companies_cnname');


        query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校园主体明细表.xlsx"));
        exit;
    }

    //改变是否学科类学校
    function ChangeSchoolSubjectAction($paramArray)
    {
        $policyOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_id = '{$paramArray['school_id']}'");

        if ($policyOne) {
            $data = array();
            $data['school_issubject'] = $paramArray['school_issubject'];

            $field = array();
            $field['school_issubject'] = "是否学科类学校";

            if ($this->DataControl->updateData("smc_school", "school_id = '{$paramArray['school_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //添加分校
    function addSchoolAction($paramArray)
    {
        $data = array();

        $maxschoolnum = $this->DataControl->select("select contract_maxschoolnum from imc_sales_contract WHERE company_id = '{$paramArray['company_id']}'");

        $maxschoolnums = $maxschoolnum[0][0];


        $schoolNum = $this->DataControl->select("
            SELECT
               COUNT(DISTINCT s.school_id)
            FROM
                smc_school AS s LEFT JOIN gmc_company_organizeschool AS o ON o.school_id = s.school_id
                left join gmc_company_district as d on d.district_id = s.district_id 
            WHERE
                s.company_id = '{$paramArray['company_id']}'");
        $schoolNums = $schoolNum[0][0];

        if ($schoolNums >= $maxschoolnums) {
            ajax_return(array('error' => 1, 'errortip' => "超过可创建的学校上限!"), $this->companyOne['company_language']);
        }

        $like = substr(date("Ymd", time()), 2, 6);

        if ($paramArray['company_isscbranch'] == '1') {
            $stuInfo = $this->DataControl->selectOne("select school_branch from smc_school where school_branch like '{$like}%' order by school_branch DESC limit 0,1");
            if ($stuInfo) {
                $data['school_branch'] = $stuInfo['school_branch'] + 1;
            } else {
                $data['school_branch'] = $like . '001';
            }
        } else {
            $data['school_branch'] = $paramArray['school_branch'];
        }

        $district_cnname = $this->DataControl->getFieldOne('smc_school', 'school_id', "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($district_cnname) {
            ajax_return(array('error' => 1, 'errortip' => "校区编号已经存在!"), $this->companyOne['company_language']);
        }

//        $data['school_branch'] = $paramArray['school_branch'];
//        $data['school_type'] = $paramArray['school_type'];
        $data['companies_id'] = $paramArray['companies_id'];
        $data['school_cmbshopcode'] = $paramArray['school_cmbshopcode'];
        $data['school_upcshopcode'] = $paramArray['school_upcshopcode'];
        $data['school_cnname'] = $paramArray['school_cnname'];
        $data['school_enname'] = $paramArray['school_enname'];
        $data['school_shortname'] = $paramArray['school_shortname'];
        $data['district_id'] = $paramArray['district_id'];
        $data['company_id'] = $paramArray['company_id'];
        $data['school_province'] = $paramArray['school_province'];
        $data['school_issubject'] = $paramArray['school_issubject'];
        $data['school_city'] = $paramArray['school_city'];
        $data['school_area'] = $paramArray['school_area'];
        $data['school_istest'] = $paramArray['school_istest'];
        $data['school_address'] = $paramArray['school_address'];
        $data['school_phone'] = $paramArray['school_phone'];
        $data['school_erpshopcode'] = $paramArray['school_erpsh3opcode'];
        $data['pospattern_id'] = $paramArray['pospattern_id'];
        $data['school_isnewschool'] = $paramArray['school_isnewschool'];
        $data['school_ismove'] = $paramArray['school_ismove'];
        $data['school_newstart'] = $paramArray['school_newstart'];
        $data['school_newend'] = $paramArray['school_newend'];
        $data['school_renewtime'] = $paramArray['school_renewtime'];
        $data['school_changestime'] = $paramArray['school_changestime'];
        $data['school_renewsttime'] = $paramArray['school_renewsttime'];
        $data['school_renewentime'] = $paramArray['school_renewentime'];
        $data['school_changessttime'] = $paramArray['school_changessttime'];
        $data['school_changesentime'] = $paramArray['school_changesentime'];
        $data['school_isvoucher'] = $paramArray['school_isvoucher'];
        $data['school_istwocode'] = $paramArray['school_istwocode'];
        $data['school_signet'] = $paramArray['school_signet'];
        $data['school_coursetime'] = $paramArray['school_coursetime'];
        $data['school_appointment'] = $paramArray['school_appointment'];
        $data['school_duration'] = $paramArray['school_duration'];
        $data['school_sort'] = $paramArray['school_sort'];
        $data['school_remark'] = $paramArray['school_remark'];
        $data['school_isscan'] = $paramArray['school_isscan'];
        $data['school_iscreateroom'] = $paramArray['school_iscreateroom'];
        $data['school_vouchermode'] = $paramArray['school_vouchermode'];
        $data['school_opclass'] = $paramArray['school_opclass'];
        $data['school_inclass'] = $paramArray['school_inclass'];
        $data['school_isprotocol'] = $paramArray['school_isprotocol'];
        $data['school_payaftersignlimit'] = $paramArray['school_payaftersignlimit'];
        $data['school_liaison'] = $paramArray['school_liaison'];
        $data['school_examine'] = $paramArray['school_examine'];
        $data['school_register'] = $paramArray['school_register'];
        $data['school_permitbranch'] = $paramArray['school_permitbranch'];
        $data['school_permitstday'] = $paramArray['school_permitstday'];
        $data['school_icp'] = $paramArray['school_icp'];
        $data['school_licensestday'] = $paramArray['school_licensestday'];
        $data['school_society'] = $paramArray['school_society'];

        $data['school_crmtobi'] = $paramArray['school_crmtobi'];
        $data['school_teachtobi'] = $paramArray['school_teachtobi'];

        if (isset($paramArray['school_periodauthority']) && $paramArray['school_periodauthority'] != '') {
            $data['school_periodauthority'] = $paramArray['school_periodauthority'];
        }

        if (isset($paramArray['school_isforwardapply']) && $paramArray['school_isforwardapply'] != '') {
            $data['school_isforwardapply'] = $paramArray['school_isforwardapply'];
        }

        $data['school_foundtime'] = $paramArray['school_foundtime'];//创校时间
        $data['school_createtime'] = time();

        $field = array();
        $field['school_branch'] = "校区编号";
//        $field['school_type'] = "学校类型";
        $field['companies_id'] = "企业ID";
        $field['school_cmbshopcode'] = "招行门店编号";
        $field['school_upcshopcode'] = "银联门店编号";
        $field['school_cnname'] = "中文名";
        $field['school_enname'] = "英文名";
        $field['school_shortname'] = "校园简称";
        $field['district_id'] = "所属区域";
        $field['company_id'] = "所属公司";
        $field['school_province'] = "省";
        $field['school_city'] = "市";
        $field['school_area'] = "区";
        $field['school_istest'] = "是否测试校0不是1是";
        $field['school_address'] = "校园地址";
        $field['school_phone'] = "校园联系电话";
        $field['school_erpshopcode'] = "校园ERP对接编号";
        $field['school_createtime'] = "创建时间";

        $school_branch = $this->DataControl->getFieldOne('smc_school', 'school_id', "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($school_branch) {
            ajax_return(array('error' => 1, 'errortip' => "编号已存在!"), $this->companyOne['company_language']);
        }

        $dataid = $this->DataControl->insertData('smc_school', $data);
        if ($dataid) {

            $companiesList = json_decode(stripslashes($paramArray['companies_list']), true);
            if ($companiesList) {
                $arr = array();
                foreach ($companiesList as $val) {

                    $arr['school_id'] = $dataid;
                    $arr['companies_id'] = $val['companies_id'];
                    $arr['companies_storenumber'] = $val['companies_storenumber'];
                    $arr['companies_headstorenumber'] = $val['companies_headstorenumber'];
                    $arr['conpanies_headuserid'] = $val['conpanies_headuserid'];
                    $arr['companies_subStoreId'] = $val['companies_subStoreId'];

                    $this->DataControl->insertData("smc_school_companies", $arr);
                }
            }

            $list = array();
            $list['school_id'] = $dataid;
            $list['companies_list'] = $paramArray['companiesTwo_list'];
            $this->batchSetChargingSubjectAction($list);

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '添加分校', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加分校失败', 'result' => $result);
        }
        return $res;
    }

    function schoolDetailApi($paramArray)
    {
        $sql = "
            SELECT
                s.school_branch,
                s.school_type,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                s.school_province,
                s.school_city,
                s.school_area 
            FROM
                smc_school AS s 
            WHERE
                school_id = '{$paramArray['school_id']}'
            ORDER BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc     
            ";
        $schoolDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["school_branch"] = "校区编号";
        $field["school_type"] = "学校类型";
        $field["school_cnname"] = "中文名";
        $field["school_enname"] = "英文名";
        $field["school_province"] = "省";
        $field["school_city"] = "市";
        $field["school_area"] = "区";

        $result = array();
        if ($schoolDetail) {
            $result["field"] = $field;
            $result["data"] = $schoolDetail;
            $res = array('error' => '0', 'errortip' => '分校查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '分校查看失败', 'result' => $result);
        }
        return $res;
    }


    function districtListApi($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (district_cnname like '%{$paramArray['keyword']}%' or district_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($request['district_branch']) && $request['district_branch'] !== '') {
            $datawhere .= " and district_branch='{$request['district_branch']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                district_id,
                district_cnname,
                district_content,
                district_branch
            FROM
                gmc_company_district
            WHERE
                {$datawhere} AND company_id = '{$paramArray['company_id']}'
            ORDER BY
                district_id DESC
                ";

        $districtList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(d.district_id)
            FROM
                gmc_company_district AS d
            WHERE {$datawhere} and d.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('district_cnname', 'district_content', 'district_branch');
        $fieldname = $this->LgArraySwitch(array('区域名称', '区域描述', '区域代码'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($districtList) {
            $result['list'] = $districtList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无区域", 'result' => $result);
        }

        return $res;
    }

    //添加区域
    function addDistrictAction($paramArray)
    {
        $data = array();
        $data['district_cnname'] = $paramArray['district_cnname'];
        $data['district_branch'] = $paramArray['district_branch'];
        $data['district_content'] = $paramArray['district_content'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['district_cnname'] = "区域名称";
        $field['district_branch'] = "区域代码";
        $field['district_content'] = "区域描述";
        $field['company_id'] = "所属公司";

        $district_cnname = $this->DataControl->getFieldOne('gmc_company_district', 'district_id', "district_cnname = '{$paramArray['district_cnname']}' and company_id = '{$paramArray['company_id']}'");
        if ($district_cnname) {
            ajax_return(array('error' => 1, 'errortip' => "区域名称已存在!"));
        }
        $district_branch = $this->DataControl->getFieldOne('gmc_company_district', 'district_id', "district_branch = '{$paramArray['district_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($district_branch) {
            ajax_return(array('error' => 1, 'errortip' => "区域代码已存在!"));
        }


        if ($this->DataControl->insertData('gmc_company_district', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加区域成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '新增区域', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加区域失败', 'result' => $result);
        }
        return $res;
    }

    //编辑区域
    function updateDistrictAction($paramArray)
    {
        $districtOne = $this->DataControl->getFieldOne("gmc_company_district", "district_id", "district_id = '{$paramArray['district_id']}'");
        if ($districtOne) {
            $data = array();
            $data['district_cnname'] = $paramArray['district_cnname'];
            $data['district_branch'] = $paramArray['district_branch'];
            $data['district_content'] = $paramArray['district_content'];

            $field = array();
            $field['district_cnname'] = "区域名称";
            $field['district_branch'] = "区域代码";
            $field['district_content'] = "区域描述";

            $districtOne = $this->DataControl->getFieldOne('gmc_company_district', 'district_id'
                , "district_cnname = '{$paramArray['district_cnname']}' and company_id = '{$paramArray['company_id']}' and district_id != '{$paramArray['district_id']}'");
            if ($districtOne) {
                ajax_return(array('error' => 1, 'errortip' => "区域名称已存在!"));
            }

            $districtOne = $this->DataControl->getFieldOne('gmc_company_district', 'district_id'
                , "district_branch = '{$paramArray['district_branch']}' and company_id = '{$paramArray['company_id']}' and district_id != '{$paramArray['district_id']}'");
            if ($districtOne) {
                ajax_return(array('error' => 1, 'errortip' => "区域代码已存在!"));
            }

            if ($this->DataControl->updateData("gmc_company_district", "district_id = '{$paramArray['district_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "区域修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '编辑区域', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '区域修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '请传入区域数据ID', 'result' => $result);
        }
        return $res;
    }

    //删除区域
    function delDistrictAction($paramArray)
    {
        $districtOne = $this->DataControl->getFieldOne("gmc_company_district", "district_id", "district_id = '{$paramArray['district_id']}'");
        if ($districtOne) {
            $district = $this->DataControl->selectClear("select count(school_id) as a from smc_school WHERE district_id = '{$paramArray['district_id']}'");
            if ($district[0]['a'] == '0') {
                if ($this->DataControl->delData("gmc_company_district", "district_id = '{$paramArray['district_id']}'")) {
                    $result = array();
                    $res = array('error' => '0', 'errortip' => "删除区域成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '删除区域', dataEncode($paramArray));
                } else {
                    $result = array();
                    $res = array('error' => '1', 'errortip' => '删除区域失败', 'result' => $result);
                }
            } else {
                ajax_return(array('error' => 1, 'errortip' => "区域已被使用不可删除!"));
            }

        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 添加职工
    function addStafferAction($paramArray)
    {
        $data = array();
        $data['staffer_mobile'] = $paramArray['staffer_mobile'];
        $data['staffer_cnname'] = $paramArray['staffer_cnname'];
        $data['staffer_branch'] = $paramArray['staffer_branch'];
        $data['company_id'] = $paramArray['company_id'];
        $data['post_id'] = $paramArray['post_id'];
        $data['postrole_id'] = $paramArray['postrole_id'];
        $data['postlevel_id'] = $paramArray['postlevel_id'];
        $data['staffer_birthday'] = $paramArray['staffer_birthday'];
        $data['staffer_pass'] = $paramArray['staffer_pass'];
        $data['staffer_sex'] = $paramArray['staffer_sex'];
        $data['staffer_createtime'] = time();

        $field = array();
        $field['staffer_mobile'] = "手机号";
        $field['staffer_cnname'] = "中文名称";
        $field['staffer_branch'] = "职工编号";
        $field['company_id'] = "所属集团";
        $field['post_id'] = "职务";
        $field['postrole_id'] = "角色";
        $field['postlevel_id'] = "职级";
        $field['staffer_birthday'] = "出生日期";
        $field['staffer_pass'] = "初始密码";
        $field['staffer_sex'] = "性别";
        $field['staffer_createtime'] = "创建时间";

        if ($this->DataControl->insertData("smc_staffer", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工失败', 'result' => $result);
        }
        return $res;
    }

    //编辑学校
    function updateSchoolAction($paramArray)
    {
        if (strpos($paramArray['school_monitorcode'], '，') !== false) {
            ajax_return(array('error' => 1, 'errortip' => "请使用英文输入法下的逗号（,）!"));
        }
        if (strpos($paramArray['school_monitorcode'], ',') !== false) {
            $monitorcode = explode(",", $paramArray['school_monitorcode']);
            if (count($monitorcode) != count(array_unique($monitorcode))) {
                ajax_return(array('error' => 1, 'errortip' => "监控交换机设备码有重复项，请检查后提交!"));
            }
        }
        if (isset($paramArray['school_monitorcode']) && $paramArray['school_monitorcode'] != '') {
            $school_monitor = $this->DataControl->getFieldOne('smc_school', 'school_id', "school_monitorcode like '%{$paramArray['school_monitorcode']}%' and school_id != '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}'");
            if ($school_monitor) {
                ajax_return(array('error' => 1, 'errortip' => "监控主机编号已存在!"));
            }
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_id = '{$paramArray['school_id']}'");
        if ($schoolOne) {
            $data = array();
            $data['school_branch'] = $paramArray['school_branch'];
            $data['companies_id'] = $paramArray['companies_id'];
            $data['school_cmbshopcode'] = $paramArray['school_cmbshopcode'];
            $data['school_upcshopcode'] = $paramArray['school_upcshopcode'];
            $data['school_cnname'] = $paramArray['school_cnname'];
            $data['school_enname'] = $paramArray['school_enname'];
            $data['school_shortname'] = $paramArray['school_shortname'];
            $data['district_id'] = $paramArray['district_id'];
            $data['school_province'] = $paramArray['school_province'];
            $data['school_city'] = $paramArray['school_city'];
            $data['school_area'] = $paramArray['school_area'];
            $data['school_istest'] = $paramArray['school_istest'];
            $data['school_address'] = $paramArray['school_address'];
            $data['school_phone'] = $paramArray['school_phone'];
            $data['school_isclose'] = $paramArray['school_isclose'];
            $data['school_erpshopcode'] = $paramArray['school_erpshopcode'];
            $data['pospattern_id'] = $paramArray['pospattern_id'];
            $data['school_isnewschool'] = $paramArray['school_isnewschool'];
            $data['school_ismove'] = $paramArray['school_ismove'];
            $data['school_issubject'] = $paramArray['school_issubject'];
            $data['school_newstart'] = $paramArray['school_newstart'];
            $data['school_newend'] = $paramArray['school_newend'];
            $data['school_renewtime'] = $paramArray['school_renewtime'];
            $data['school_changestime'] = $paramArray['school_changestime'];
            $data['school_isvoucher'] = $paramArray['school_isvoucher'];
            $data['school_renewsttime'] = $paramArray['school_renewsttime'];
            $data['school_renewentime'] = $paramArray['school_renewentime'];
            $data['school_changessttime'] = $paramArray['school_changessttime'];
            $data['school_changesentime'] = $paramArray['school_changesentime'];
            $data['school_istwocode'] = $paramArray['school_istwocode'];
            $data['school_signet'] = $paramArray['school_signet'];
            $data['school_coursetime'] = $paramArray['school_coursetime'];
            $data['school_appointment'] = $paramArray['school_appointment'];
            $data['school_duration'] = $paramArray['school_duration'];
            $data['school_sort'] = $paramArray['school_sort'];
            $data['school_remark'] = $paramArray['school_remark'];
            $data['school_isscan'] = $paramArray['school_isscan'];
            $data['school_autochecking'] = $paramArray['school_autochecking'];
            $data['school_iscreateroom'] = $paramArray['school_iscreateroom'];
            $data['school_vouchermode'] = $paramArray['school_vouchermode'];
            $data['school_openclass'] = $paramArray['school_openclass'];
            $data['school_opclass'] = $paramArray['school_opclass'];
            $data['school_inclass'] = $paramArray['school_inclass'];
            $data['school_isprotocol'] = $paramArray['school_isprotocol'];
            $data['school_monitorcode'] = $paramArray['school_monitorcode'];//海康云眸对应的学校分组编号（即 监控主机编号）
            if (isset($paramArray['school_periodauthority']) && $paramArray['school_periodauthority'] != '') {
                $data['school_periodauthority'] = $paramArray['school_periodauthority'];
            }

            if (isset($paramArray['school_isforwardapply']) && $paramArray['school_isforwardapply'] != '') {
                $data['school_isforwardapply'] = $paramArray['school_isforwardapply'];
            }

            $data['school_istemporaryclose'] = $paramArray['school_istemporaryclose'];
            $data['school_temporaryclosetip'] = $paramArray['school_temporaryclosetip'];
            $data['school_payaftersignlimit'] = $paramArray['school_payaftersignlimit'];
            $data['school_liaison'] = $paramArray['school_liaison'];
            $data['school_examine'] = $paramArray['school_examine'];
            $data['school_register'] = $paramArray['school_register'];
            $data['school_permitbranch'] = $paramArray['school_permitbranch'];
            $data['school_permitstday'] = $paramArray['school_permitstday'];
            $data['school_icp'] = $paramArray['school_icp'];
            $data['school_licensestday'] = $paramArray['school_licensestday'];
            $data['school_society'] = $paramArray['school_society'];

            $data['school_crmtobi'] = $paramArray['school_crmtobi'];
            $data['school_teachtobi'] = $paramArray['school_teachtobi'];

            $data['school_foundtime'] = $paramArray['school_foundtime'];//创校时间
            $data['school_updatatime'] = time();

            $field = array();
            $field['school_branch'] = "校区编号";
            $field['companies_id'] = "企业ID";
            $field['school_cmbshopcode'] = "招行门店编号";
            $field['school_upcshopcode'] = "银联门店编号";
            $field['school_cnname'] = "中文名";
            $field['school_enname'] = "英文名";
            $field['school_shortname'] = "校园简称";
            $field['district_id'] = "所属区域";
            $field['school_province'] = "省";
            $field['school_city'] = "市";
            $field['school_area'] = "区";
            $field['school_istest'] = "是否测试校0不是1是";
            $field['school_address'] = "校园地址";
            $field['school_phone'] = "校园联系电话";
            $field['school_isclose'] = "是否关闭学校";
            $field['school_erpshopcode'] = "校园ERP对接编号";
            $field['school_updatatime'] = "更新时间";


            $school_branch = $this->DataControl->getFieldOne('smc_school', 'school_id', "school_branch = '{$paramArray['school_branch']}' and school_id != '{$paramArray['school_id']}' and company_id = '{$paramArray['company_id']}'");
            if ($school_branch) {
                ajax_return(array('error' => 1, 'errortip' => "编号已存在!"));
            }

            if ($this->DataControl->updateData("smc_school", "school_id = '{$paramArray['school_id']}'", $data)) {

                $this->DataControl->delData("smc_school_companies", "school_id = '{$paramArray['school_id']}'");
                $companiesList = json_decode(stripslashes($paramArray['companies_list']), true);
                if ($companiesList) {
                    $arr = array();
                    foreach ($companiesList as $val) {
                        $arr['school_id'] = $paramArray['school_id'];
                        $arr['companies_id'] = $val['companies_id'];
                        $arr['companies_storenumber'] = $val['companies_storenumber'];
                        $arr['companies_headstorenumber'] = $val['companies_headstorenumber'];
                        $arr['conpanies_headuserid'] = $val['conpanies_headuserid'];
                        $arr['companies_subStoreId'] = $val['companies_subStoreId'];
                        $this->DataControl->insertData("smc_school_companies", $arr);
                    }
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "学校修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '编辑学校', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '学校修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除学校
    function delSchoolAction($paramArray)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_id = '{$paramArray['school_id']}'");

        $enrolledOne = $this->DataControl->getFieldOne("smc_student_changelog", "changelog_id", "school_id='{$paramArray['school_id']}'");

        if ($schoolOne && !$enrolledOne) {
            if ($this->DataControl->delData("smc_school", "school_id = '{$paramArray['school_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除学校成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '删除学校', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '学校存在历史记录,不可删除', 'result' => $result);
        }
        return $res;
    }

    //收费主体设置
    function chargingSubject($paramArray)
    {
        $datawhere = " cc.company_id = '{$paramArray['company_id']}' and coursetype_isopenclass = '0' and coursetype_isrecruit = '1'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (cc.coursecat_cnname like '%{$paramArray['keyword']}%' or cp.companies_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] != '') {
            $datawhere .= " and cc.coursetype_id = '{$paramArray['coursetype_id']}'";
        }

        $sql = "SELECT cc.coursecat_id,cc.coursetype_id,cc.coursecat_cnname,ct.coursetype_cnname,cs.subject_id,cs.companies_id,cp.companies_cnname,cp.companies_issupervise,cs.merge_companies_id,co.companies_cnname as merge_companies_cnname
                FROM smc_code_coursecat as cc 
                LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = cc.coursetype_id 
                LEFT JOIN smc_school_coursecat_subject as cs ON cs.coursecat_id = cc.coursecat_id and cs.school_id = '{$paramArray['school_id']}'
                LEFT JOIN gmc_code_companies as cp ON cp.companies_id = cs.companies_id 
                LEFT JOIN gmc_code_companies as co ON co.companies_id = cs.merge_companies_id 
                WHERE {$datawhere}";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                if (!$value['companies_id']) {
                    $value['companies_cnname'] = '--';
                }
                if (!$value['merge_companies_id']) {
                    $value['merge_companies_cnname'] = '--';
                }
            }
        } else {
            $dataList = array();
        }

        $result = array();
        $result["list"] = $dataList;

        return $result;
    }

    //批量设置收费主体
    function batchSetChargingSubjectAction($paramArray)
    {
        $companiesList = json_decode(stripslashes($paramArray['companies_list']), true);
        if ($companiesList) {
            $data = array();
            foreach ($companiesList as $val) {
                if ($this->DataControl->getFieldOne("smc_school_coursecat_subject", "subject_id", "subject_id<>'{$val['subject_id']}' 
                    and companies_id='{$val['companies_id']}' and school_id='{$paramArray['school_id']}' and coursecat_id='{$val['coursecat_id']}'")) {
                    continue;
                }

                if(isset($val['companies_id']) && $val['companies_id']!='' ){

                    $data['companies_id'] = $val['companies_id'];
                }

                if(isset($val['merge_companies_id']) && $val['merge_companies_id']!='' ){

                    $data['merge_companies_id'] = $val['merge_companies_id'];
                }


                if ($val['subject_id']) {
                    $this->DataControl->updateData("smc_school_coursecat_subject", "subject_id = '{$val['subject_id']}'", $data);
                } else {
                    $data['school_id'] = $paramArray['school_id'];
                    $data['coursecat_id'] = $val['coursecat_id'];
                    $this->DataControl->insertData("smc_school_coursecat_subject", $data);
                }
            }
            return true;
        } else {
            return false;
        }
    }

    //获取学校省
    function getProvinceApi()
    {
        $school = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '1'");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    //获取学校市
    function getCityApi($paramArray)
    {
        $school = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$paramArray['region_id']}'");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    //获取学校区
    function getAreaApi($paramArray)
    {
        $school = $this->DataControl->selectClear("SELECT region_id,region_name from smc_code_region WHERE parent_id = '{$paramArray['region_id']}'");
        if ($school) {
            $result = array();
            $result["data"] = $school;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    function getStudentList($paramArray)
    {

        $datawhere = " s.company_id = '{$paramArray['company_id']}' AND s.student_isdel = '0'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' 
            or s.student_enname like '%{$paramArray['keyword']}%' 
            or s.student_branch like '%{$paramArray['keyword']}%' 
            or f.family_mobile like '%{$paramArray['keyword']}%' 
            )";
        }
        if (isset($paramArray['class_id']) && trim($paramArray['class_id']) !== '') {
            $datawhere .= " and exists (select 1 from smc_student_study where student_id=s.student_id 
            and study_isreading=1 and class_id ='{$paramArray['class_id']}')";
        }
        if (isset($paramArray['enrolled_status']) && trim($paramArray['enrolled_status']) !== '') {
            $datawhere .= " and e.enrolled_status ='{$paramArray['enrolled_status']}'";
        }
        if (isset($paramArray['school_id']) && trim($paramArray['school_id']) !== '') {
            $datawhere .= " and e.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (!isset($paramArray['is_export']) || $paramArray['is_export'] !== '1') {
            $sql = "SELECT
                    s.student_id,
                    e.school_id,
                    s.student_cnname,
                    s.student_enname,
                    s.student_branch,
                    s.student_sex,
                    s.student_img,
                    s.student_birthday,
                    (select group_concat(DISTINCT a.apppropermis_name) from smc_student_apppropermislog l,smc_code_apppropermis a 
                        where l.student_id = s.student_id and l.apppropermislog_endday > now() and l.apppropermis_code = a.apppropermis_code ) as apppropermis_name,
                    (select group_concat(DISTINCT l.apppropermis_authcode) from smc_student_apppropermislog l,smc_code_apppropermis a 
                        where l.student_id = s.student_id and l.apppropermislog_endday > now() and l.apppropermis_code = a.apppropermis_code ) as apppropermis_authcode,
                    f.family_cnname,
                    f.family_mobile,
                    e.enrolled_status,
                    (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
                    s.student_isdel,
                    s.student_isdel as student_isdel_name,
                    sc.school_branch,
                    ifnull((SELECT SUM(p.pay_price) FROM smc_payfee_order_pay AS p,smc_payfee_order AS o,smc_code_paytype as a
                    WHERE p.order_pid = o.order_pid and p.paytype_code=a.paytype_code AND p.pay_issuccess = '1' AND a.paytype_ischarge=1 
                    AND o.student_id = s.student_id AND o.school_id = e.school_id),0) AS  payallprice
                FROM smc_student AS s 
                    left join smc_student_enrolled as e on s.student_id = e.student_id
                    left join smc_school as sc on sc.school_id = e.school_id
                    left join smc_student_family as f on f.student_id = s.student_id and f.family_isdefault = 1
                WHERE {$datawhere}
                ORDER BY s.student_id DESC
                LIMIT {$pagestart},{$num}";
        } else {
            $sql = "
                SELECT
                    s.student_id,
                    (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
                    sc.school_branch,
                    s.student_id,
                    s.student_cnname,
                    s.student_enname,
                    s.student_branch,
                    s.student_sex,
                    s.student_img,
                    s.student_birthday,
                    s.student_isdel,
                    s.student_isdel as student_isdel_name,
                    group_concat(DISTINCT a.apppropermis_name) as apppropermis_name,
                    group_concat(DISTINCT l.apppropermis_authcode) as apppropermis_authcode,
                    f.family_cnname,
                    f.family_mobile,
                    e.enrolled_status,
                    ifnull((SELECT SUM(p.pay_price) FROM smc_payfee_order_pay AS p,smc_payfee_order AS o,smc_code_paytype as a
                    WHERE p.order_pid = o.order_pid and p.paytype_code=a.paytype_code AND p.pay_issuccess = '1' AND a.paytype_ischarge=1 
                    AND o.student_id = s.student_id AND o.school_id = e.school_id),0) AS  payallprice
                FROM smc_student AS s 
                    left join smc_student_enrolled as e on s.student_id = e.student_id
                    left join smc_school as sc on sc.school_id = e.school_id
                    left join smc_student_apppropermislog as l on l.student_id = s.student_id and l.apppropermislog_endday > now()
                    left join smc_code_apppropermis as a on l.apppropermis_code = a.apppropermis_code
                    left join smc_student_family as f on f.student_id = s.student_id and f.family_isdefault = 1
                WHERE {$datawhere}
                GROUP BY s.student_id,e.school_id
                ORDER BY s.student_id DESC";
        }


        $studentList = $this->DataControl->selectClear($sql);

        if ($studentList) {
            $status = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "-1" => "已离校"));
            $statuss = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            foreach ($studentList as &$val) {
                $val['student_isdel_name'] = $statuss[$val['student_isdel_name']];
                $val['enrolled_status'] = $status[$val['enrolled_status']];
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == '1') {
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }
            $outexceldate = array();
            if ($studentList) {
                $outexceldate = array();
                foreach ($studentList as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['enrolled_status'] = $dateexcelvar['enrolled_status'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                    $datearray['family_cnname'] = $dateexcelvar['family_cnname'];
                    if ($this->stafferOne && $this->stafferOne['account_class'] == 1) {
                        $datearray['family_mobile'] = $dateexcelvar['family_mobile'];
                    } else {
                        $datearray['family_mobile'] = $dateexcelvar['family_mobile'] ? hideNumberString($dateexcelvar['family_mobile']) : '';
                    }
                    $datearray['apppropermis_name'] = $dateexcelvar['apppropermis_name'];
                    $datearray['apppropermis_authcode'] = $dateexcelvar['apppropermis_authcode'];
                    $datearray['student_isdel_name'] = $dateexcelvar['student_isdel_name'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "学员中文名", "学员英文名", "编号", "状态", "性别", "生日", "主要联系人", "主要联系人电话", "已开通的权限", "已开通的权限授权码", "学员是否删除"));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'enrolled_status', 'student_sex', 'student_birthday', 'family_cnname', 'family_mobile', 'apppropermis_name', 'apppropermis_authcode', 'student_isdel_name');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "学员信息报表.xlsx");

        }

        $all_num = $this->DataControl->selectOne("SELECT count(s.student_id) as stunums
                FROM smc_student AS s
                left join smc_student_enrolled as e on s.student_id = e.student_id
                left join smc_student_family as f on f.student_id = s.student_id and f.family_isdefault = 1
                WHERE {$datawhere}");
        $allnums = $all_num['stunums'];


        $fieldstring = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'payallprice', 'enrolled_status', 'student_sex', 'student_birthday', 'family_cnname', 'family_mobile', 'apppropermis_name', 'apppropermis_authcode', 'student_isdel_name');
        $fieldname = $this->LgArraySwitch(array("校区名称", "校区编号", '学员中文名', '学员英文名', '学员编号', '已付款金额', '状态', '性别', '生日', '主要联系人', '主要联系人电话', '已开通的权限', '已开通的权限授权码', '学员是否删除'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            if ($fieldstring[$i] == 'student_cnname') {
                $field[$i]["ismethod"] = 1;
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        $result['all_num'] = $allnums;

        $swhere = "company_id = '{$paramArray['company_id']}' and class_status > '-2'";
        if (isset($paramArray['school_id']) && trim($paramArray['school_id']) !== '') {
            $swhere .= " and school_id ='{$paramArray['school_id']}'";
        }
//          //999029->集团架构->用户管理->学校班级列表 -- 97
//        $result['class'] = $this->DataControl->selectClear("SELECT class_id,class_branch,class_cnname FROM smc_class where {$swhere}");

        $result['list'] = $studentList;

        if ($studentList) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无学员数据信息", 'result' => $result);
        }

        return $res;
    }

    function getLossStudentList($request)
    {

        $datawhere = "sc.company_id='{$this->company_id}' and se.enrolled_status='-1'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' or p.parenter_mobile like '%{$request['keyword']}%' or p.parenter_cnname like '%{$request['keyword']}%' or st.student_id in (select fa.student_id from smc_student_family as fa where fa.family_mobile like '%{$request['keyword']}%'))";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(se.enrolled_leavetime, '%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(se.enrolled_leavetime, '%Y-%m-%d') <= '{$request['endtime']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and sc.school_id = '{$request['school_id']}'";
        }

        if (isset($request['is_readother']) && $request['is_readother'] !== '') {
            if ($request['is_readother'] == 0) {
                $datawhere .= " and not exists(select 1 from smc_student_enrolled as en where en.student_id=se.student_id and (en.enrolled_status=0 or en.enrolled_status=1) and en.school_id<>se.school_id)";
            } else {
                $datawhere .= " and exists(select 1 from smc_student_enrolled as en where en.student_id=se.student_id and (en.enrolled_status=0 or en.enrolled_status=1) and en.school_id<>se.school_id)";
            }

        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sc.school_id,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch,st.student_id,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,st.student_birthday,p.parenter_cnname,p.parenter_mobile,FROM_UNIXTIME(se.enrolled_leavetime, '%Y-%m-%d') as enrolled_leavetime
              ,(select ch.changelog_note from smc_student_changelog as ch left join smc_code_stuchange as cs on cs.stuchange_code=ch.stuchange_code where ch.student_id=se.student_id and ch.changelog_createtime<=se.enrolled_leavetime and cs.stustatus_isenschool=0 order by ch.changelog_createtime desc limit 0,1) as changelog_note
              ,ifnull((select sp.parenter_id from smc_student_family as sf,smc_parenter as sp where sf.parenter_id=sp.parenter_id and sf.student_id=se.student_id limit 0,1),0) as can_out
              from smc_student_enrolled as se
              left join smc_school as sc on sc.school_id=se.school_id
              left join smc_student as st on st.student_id=se.student_id
              left join smc_student_family as sf on sf.student_id=se.student_id and sf.family_isdefault=1
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where {$datawhere}
              order by se.school_id asc,se.enrolled_leavetime asc";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    if ($this->stafferOne && $this->stafferOne['account_class'] == 1) {
                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    } else {
                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'] ? hideNumberString($dateexcelvar['parenter_mobile']) : '';
                    }
                    $datearray['enrolled_leavetime'] = $dateexcelvar['enrolled_leavetime'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("流失校区名称", "流失校区编号", "学员中文名", "学员英文名", "学员编号", "性别", "生日", "主要联系人", "主要联系电话", "流失日期", "流失原因"));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'student_sex', 'student_birthday', 'parenter_cnname', 'parenter_mobile', 'enrolled_leavetime', 'changelog_note');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("流失学员报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;

            $stuList = $this->DataControl->selectClear($sql);

            if (!$stuList) {
                $this->error = true;
                $this->errortip = "无流失学员数据";
                return false;
            }

            $data = array();
            $count_sql = "select sc.school_id
              from smc_student_enrolled as se
              left join smc_school as sc on sc.school_id=se.school_id
              left join smc_student as st on st.student_id=se.student_id
              left join smc_student_family as sf on sf.student_id=se.student_id and sf.family_isdefault=1
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where {$datawhere}";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $stuList;


            return $data;
        }
    }

    function checklogoutStu($request)
    {
        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $this->error = true;
            $this->errortip = "学生ID参数必须传";
            return false;
        }

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_logoutday", "company_id='{$this->company_id}'");

        if (!$companyOne) {
            $this->error = true;
            $this->errortip = "无该集团";
            return false;
        }

        $logOne = $this->DataControl->getFieldOne("smc_student_outlog", "outlog_id", "student_id='{$request['student_id']}'");
        if ($logOne) {
            $this->error = true;
            $this->errortip = "学员已注销,不可重复操作";
            return false;
        }

        if (!isset($companyOne['company_logoutday']) || $companyOne['company_logoutday'] <= 0) {
            $this->error = true;
            $this->errortip = "暂未设置可注销时间，暂不可注销哦~";
            return false;
        }

        $enrolledOne = $this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "student_id='{$request['student_id']}' and enrolled_status in ('0','1','3')");

        if ($enrolledOne) {
            $this->error = true;
            $this->errortip = "该学员在其他学校就读无法注销学籍";
            return false;
        }

        $addtime = $companyOne['company_logoutday'] * 3600 * 24;

        $sql = "select student_id from smc_student_enrolled where student_id='{$request['student_id']}' and (enrolled_leavetime+{$addtime}>unix_timestamp(now()))";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "此学员未达到集团设定的注销学籍时间,暂不可注销！";
            return false;
        }

        $familyList = $this->DataControl->getList("smc_student_family", "student_id='{$request['student_id']}'");

        if (!$familyList) {
            $this->error = true;
            $this->errortip = "无绑定家长信息，无需注销学籍";
            return false;
        }

        return true;
    }

    function logoutStu($request)
    {

        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $this->error = true;
            $this->errortip = "学生ID参数必须传";
            return false;
        }

        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_logoutday", "company_id='{$this->company_id}'");

        if (!$companyOne) {
            $this->error = true;
            $this->errortip = "无该集团";
            return false;
        }

        $logOne = $this->DataControl->getFieldOne("smc_student_outlog", "outlog_id", "student_id='{$request['student_id']}'");
        if ($logOne) {
            $this->error = true;
            $this->errortip = "学员已注销,不可重复操作";
            return false;
        }

        if (!isset($companyOne['company_logoutday']) || $companyOne['company_logoutday'] <= 0) {
            $this->error = true;
            $this->errortip = "暂未设置可注销时间，暂不可注销哦~";
            return false;
        }

        $enrolledOne = $this->DataControl->getFieldOne("smc_student_enrolled", "student_id", "student_id='{$request['student_id']}' and enrolled_status in ('0','1','3')");

        if ($enrolledOne) {
            $this->error = true;
            $this->errortip = "该学员在其他学校就读无法注销学籍";
            return false;
        }

        $addtime = $companyOne['company_logoutday'] * 3600 * 24;

        $sql = "select student_id from smc_student_enrolled where student_id='{$request['student_id']}' and (enrolled_leavetime+{$addtime}>unix_timestamp(now()))";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "此学员未达到集团设定的注销学籍时间,暂不可注销！";
            return false;
        }

        $familyList = $this->DataControl->getList("smc_student_family", "student_id='{$request['student_id']}'");

        if (!$familyList) {
            $this->error = true;
            $this->errortip = "无绑定家长信息，无需注销学籍";
            return false;
        }

        $studentOne = $this->DataControl->getFieldOne("smc_student", "from_client_id", "student_id='{$request['student_id']}'");

        if ($this->DataControl->getFieldOne("crm_student_principal", "principal_id", "student_id='{$request['student_id']}' and principal_leave=0")) {
            $this->error = true;
            $this->errortip = "当前有老师正在跟踪，取消跟踪后才可注销成功";
            return false;
        }

        foreach ($familyList as $familyOne) {
            if (isset($familyOne['parenter_id']) && $familyOne['parenter_id'] > 0) {
                $data = array();
                $data['company_id'] = $this->company_id;
                $data['staffer_id'] = $this->stafferOne['staffer_id'];
                $data['student_id'] = $request['student_id'];
                $data['parenter_id'] = $familyOne['parenter_id'];
                $data['outlog_createtime'] = time();
                $this->DataControl->insertData("smc_student_outlog", $data);
            }
        }

        $this->DataControl->delData("smc_student_family", "student_id='{$request['student_id']}'");

        if ($studentOne['from_client_id'] > 0) {
            if ($this->DataControl->getFieldOne("crm_client", "client_id", "client_id='{$studentOne['from_client_id']}'")) {
                $data = array();
                $data['client_mobile'] = '';
                $data['client_updatetime'] = time();
                $this->DataControl->updateData("crm_client", "client_id='{$studentOne['from_client_id']}'", $data);

            }
        }

        return true;

    }

    function getWeekLogoutStu()
    {
        if (!$this->company_id || $this->company_id == 0) {
            $this->error = true;
            $this->errortip = "集团ID必须传";
            return false;
        }

        $starttime = strtotime(date("Y-m-d", strtotime("-7 day")));
        $sql = "select st.student_branch 
              from smc_student_outlog as so,smc_student as st
              where so.student_id=st.student_id and so.outlog_createtime>='{$starttime}' and so.company_id='{$this->company_id}'
              group by sa.student_id";
        $studentList = $this->DataControl->selectClear($sql);
        if ($studentList) {
            return $studentList;
        } else {
            $this->error = true;
            $this->errortip = "无更新数据";
            return false;
        }
    }

    function getLogoutStudentList($request)
    {

        $datawhere = "st.company_id='{$this->company_id}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' or p.parenter_mobile like '%{$request['keyword']}%' or p.parenter_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(so.outlog_createtime, '%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(so.outlog_createtime, '%Y-%m-%d') <= '{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select st.student_id,st.student_cnname,st.student_enname,st.student_branch,st.student_sex,st.student_birthday,p.parenter_cnname,p.parenter_mobile,FROM_UNIXTIME(so.outlog_createtime, '%Y-%m-%d') as outlog_createtime
              from smc_student_outlog as so
              left join smc_student as st on st.student_id=so.student_id
              left join smc_parenter as p on p.parenter_id=so.parenter_id
              where {$datawhere}";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_sex'] = $dateexcelvar['student_sex'];
                    $datearray['student_birthday'] = $dateexcelvar['student_birthday'];
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    if ($this->stafferOne && $this->stafferOne['account_class'] == 1) {
                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    } else {
                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'] ? hideNumberString($dateexcelvar['parenter_mobile']) : '';
                    }
                    $datearray['outlog_createtime'] = $dateexcelvar['outlog_createtime'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("学员中文名", "学员英文名", "学员编号", "性别", "生日", "主要联系人", "主要联系电话", "注销日期"));
            $excelfileds = array('student_cnname', 'student_enname', 'student_branch', 'student_sex', 'student_birthday', 'parenter_cnname', 'parenter_mobile', 'outlog_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("注销学员报表.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $stuList = $this->DataControl->selectClear($sql);

            if (!$stuList) {
                $this->error = true;
                $this->errortip = "无注销学员数据";
                return false;
            }
            $data = array();
            $count_sql = "SELECT st.student_id FROM smc_student_outlog as so
              left join smc_student as st on st.student_id = so.student_id
              left join smc_parenter as p on p.parenter_id = so.parenter_id where {$datawhere}";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $stuList;

            return $data;
        }

    }

    //添加产品权限
    function addAppPowerAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['apppropermislog_createtime'] = time();
        $data['class_id'] = $paramArray['class_id'];
        $data['apppropermis_code'] = $paramArray['apppropermis_code'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $staffer_cnname = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
        $data['staffer_cnname'] = $staffer_cnname['staffer_cnname'];

        if ($paramArray['authcode']) {
            $a = $this->DataControl->getFieldOne("smc_student_apppropermislog", "apppropermislog_id", "apppropermis_authcode = '{$paramArray['authcode']}' and student_id <> '{$paramArray['student_id']}'");
            if ($a) {
                $res = array('error' => 1, 'errortip' => "该授权码已被使用!");
                return $res;
            }
        }

        //获取学员编号
        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch", "student_id = '{$paramArray['student_id']}'");
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_code,company_language", "company_id = '{$paramArray['company_id']}'");
        $apppropermisOne = $this->DataControl->getFieldOne("smc_code_apppropermis", "company_id", "apppropermis_code = '{$paramArray['apppropermis_code']}'");
        if ($paramArray['company_id'] == $apppropermisOne['company_id']) {
            $data['apppropermislog_endday'] = $paramArray['apppropermislog_endday'];
            /* $district_branch=$this->DataControl->getFieldOne('smc_student_apppropermislog', 'apppropermislog_id', "student_id = '{$paramArray['student_id']}' and apppropermis_code = '{$paramArray['apppropermis_code']}'");
            if($district_branch){
                ajax_return(array('error' => 1,'errortip' => "已授权过该产品权限!"));
            }*/

            if ($this->DataControl->insertData('smc_student_apppropermislog', $data)) {
                $result = array();
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "添加权限成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '添加权限失败', 'result' => $result);
            }
        } else {
            if ($paramArray['apppropermis_code'] == 'QIQUONLINE') {

                if (!isset($paramArray['class_id']) || $paramArray['class_id'] == '') {
                    $res = array('error' => 1, 'errortip' => "在读班级为必选!");
                    return $res;
                }

                $classOne = $this->DataControl->selectOne("select co.course_branch,cl.class_branch from smc_class as cl,smc_course as co
where cl.course_id=co.course_id and cl.class_id='{$paramArray['class_id']}'");

                if (!isset($classOne['course_branch']) || $classOne['course_branch'] == '') {
                    $res = array('error' => 1, 'errortip' => "课程不存在!");
                    return $res;
                }

                $QqModel = new \Model\Api\QqonlineModel($this->companyOne['company_language']);
                $sendApiArray = $QqModel->getFrequencyToken();
                if ($sendApiArray) {
                    $paramto = array();
                    $paramto['uid'] = $sendApiArray['result']['user_id'];
                    $paramto['token'] = $sendApiArray['result']['token'];
                    $paramto['branch'] = $paramArray['authcode'];
                    $paramto['limitedcode'] = $companyOne['company_code'];
                    $paramto['course_branch'] = $classOne['course_branch'];
                    $paramto['class_branch'] = $classOne['class_branch'];
                    $codeApiArray = $QqModel->frequencyLimit($paramto);
                    if ($codeApiArray) {
                        $paramto = array();
                        $paramto['uid'] = $sendApiArray['result']['user_id'];
                        $paramto['token'] = $sendApiArray['result']['token'];
                        $paramto['branch'] = $paramArray['authcode'];
                        $paramto['limitedcode'] = $companyOne['company_code'];
                        $paramto['student_branch'] = $studentOne['student_branch'];
                        $paramto['course_branch'] = $classOne['course_branch'];
                        $paramto['class_branch'] = $classOne['class_branch'];

                        if ($endday = $QqModel->studentBinding($paramto)) {
                            $data['apppropermis_authcode'] = $paramArray['authcode'];
                            $data['apppropermislog_endday'] = $endday;
                            if ($this->DataControl->insertData('smc_student_apppropermislog', $data)) {
                                $result = array();
                                $result["data"] = $data;
                                $res = array('error' => '0', 'errortip' => "添加权限成功", 'result' => $result);
                                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '添加产品权限', dataEncode($paramArray));
                            } else {
                                $result = array();
                                $result["data"] = array();
                                $res = array('error' => '1', 'errortip' => '添加权限失败', 'result' => $result);
                            }
                        } else {
                            $result = array();
                            $result["data"] = array();
                            $res = array('error' => '1', 'errortip' => $QqModel->errortip, 'result' => $result);
                        }
                    } else {
                        $result = array();
                        $result["data"] = array();
                        $res = array('error' => '1', 'errortip' => $QqModel->errortip, 'result' => $result);
                    }
                } else {
                    $result = array();
                    $result["data"] = array();
                    $res = array('error' => '1', 'errortip' => $QqModel->errortip, 'result' => $result);
                }
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '暂不提供权限校验', 'result' => $result);
            }
        }
        return $res;
    }


    function modifyCardStatus($paramArray)
    {

        $paramto = array();
        $paramto['apiusercode'] = 'Kedingdang';
        $paramto['apiuserpswd'] = '123456';
        $paramto['apiproductbranch'] = 'QiquOnline';
        $sendApiSting = request_by_curl("https://stuapi.kidcastle.cn/ThirdPartyApi/getFrequencyToken", dataEncode($paramto), "GET", array());
        $sendApiArray = json_decode($sendApiSting, "1");
        if (is_array($sendApiArray)) {
            $paramto = array();
            $paramto['uid'] = $sendApiArray['result']['user_id'];
            $paramto['token'] = $sendApiArray['result']['token'];
            if ($paramArray['apppropermislog_isenabled'] == '0') {
                $paramto['apppropermislog_isenabled'] = 1;
            } else {
                $paramto['apppropermislog_isenabled'] = 0;
            }
            $paramto['apppropermis_authcode'] = $paramArray['apppropermis_authcode'];

            $codeApiSting = request_by_curl("https://stuapi.kidcastle.cn/ThirdPartyApi/modifyCardStatus", dataEncode($paramto), "POST", array());
            $codeApiArray = json_decode($codeApiSting, "1");
            $result = array();
            $result["data"] = array();
            if ($codeApiArray['error'] == '0') {
                $data = array();
                $data['apppropermislog_isenabled'] = $paramto['apppropermislog_isenabled'];
                $this->DataControl->updateData("smc_student_apppropermislog", "apppropermislog_id='{$paramArray['apppropermislog_id']}'", $data);
                $res = array('error' => '0', 'errortip' => '修改成功', 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '修改产品是否禁用', dataEncode($paramArray));
            } else {
                $res = array('error' => '1', 'errortip' => $codeApiArray['errortip'], 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '产品获取授权失败', 'result' => $result);
        }
        return $res;
    }

    //获取产品
    function getAppApi($paramArray)
    {
        if ($paramArray['product_type'] == '1') {
            $app = $this->DataControl->selectClear("select apppropermis_code,apppropermis_name from smc_code_apppropermis where company_id = '{$paramArray['company_id']}'");
        }
        if ($paramArray['product_type'] == '2') {
            $app = $this->DataControl->selectClear("select apppropermis_code,apppropermis_name from smc_code_apppropermis where company_id = '0'");
        }

        if ($app) {
            $result = array();
            $result["data"] = $app;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    //获取班级
    function getClassApi($paramArray)
    {
        $class = $this->DataControl->selectClear("SELECT
            s.class_id,
            c.class_cnname
        FROM
            smc_student_study AS s left join smc_class as c on s.class_id = c.class_id
        WHERE
            s.student_id = '{$paramArray['student_id']}'
            AND s.study_isreading = 1");

        if ($class) {
            $result = array();
            $result["data"] = $class;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    //获取POS机模式
    function getPospatternApi()
    {
        $class = $this->DataControl->selectClear("SELECT
            s.pospattern_id,
            s.pospattern_name
        FROM
            smc_code_pospattern AS s");

        if ($class) {
            $result = array();
            $result["data"] = $class;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => "数据不全", 'result' => $result);
        }
        return $res;
    }

    function getPowerList($paramArray)
    {

        $datawhere = "a.company_id = '{$paramArray['company_id']}'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.apppropermis_name like '%{$paramArray['keyword']}%')";
        }

        $sql = "
            SELECT
                p.apppropermis_name,
                a.apppropermislog_id,
                a.apppropermislog_endday,
                a.apppropermis_authcode,
                a.apppropermislog_isenabled,
                a.staffer_cnname 
            FROM
                smc_student_apppropermislog AS a
                LEFT JOIN smc_code_apppropermis AS p ON a.apppropermis_code = p.apppropermis_code
            WHERE
                {$datawhere} and a.student_id = '{$paramArray['student_id']}'
            ORDER BY
                a.apppropermislog_id DESC";

        $studentList = $this->DataControl->selectClear($sql);

        $fieldstring = array('apppropermis_name', 'apppropermislog_endday', 'apppropermis_authcode', 'apppropermislog_isenabled', 'staffer_cnname ');
        $fieldname = $this->LgArraySwitch(array('产品名称', '授权截止日期', '产品授权码', '是否禁用', '操作人'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 1, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        $result['list'] = $studentList;

        if ($studentList) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无产品授权信息", 'result' => $result);
        }

        return $res;
    }

    //删除产品权限
    function delAppPowerAction($paramArray)
    {
        $apppropermislogOne = $this->DataControl->getFieldOne("smc_student_apppropermislog", "apppropermislog_id",
            "apppropermislog_id = '{$paramArray['apppropermislog_id']}' AND company_id = '{$paramArray['company_id']}'");
        if ($apppropermislogOne) {
            if ($this->DataControl->delData("smc_student_apppropermislog", "apppropermislog_id = '{$paramArray['apppropermislog_id']}' AND company_id = '{$paramArray['company_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除产品权限成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '删除产品权限', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除产品权限失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '为传入完整权限ID信息', 'result' => $result);
        }
        return $res;
    }

    //删除学员
    function delStudentAction($paramArray)
    {
        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_id = '{$paramArray['student_id']}' AND company_id = '{$paramArray['company_id']}'");
        if ($studentOne) {
            $isdelete = $this->DataControl->getFieldOne("view_smc_student_delete", "student_id", "student_id = '{$paramArray['student_id']}'");
            if ($isdelete) {
                ajax_return(array('error' => 1, 'errortip' => "学员存在交易信息，及考勤数据无法删除！"), $this->companyOne['company_language']);
            }
            $data = array();
            $data['student_updatatime'] = time();
            $data['student_isdel'] = '1';
            if ($this->DataControl->updateData("smc_student", "student_id = '{$paramArray['student_id']}'", $data)) {
                $this->DataControl->delData("smc_student_enrolled", "student_id = '{$paramArray['student_id']}'");
                $this->DataControl->delData("smc_student_changelog", "student_id = '{$paramArray['student_id']}' and stuchange_code = 'A01'");
                $this->DataControl->delData("smc_student_family", "student_id = '{$paramArray['student_id']}'");
                $result = array();
                $res = array('error' => '0', 'errortip' => "学员数据删除成功，如果需要找回请联系技术支持！", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->校园管理", '删除学员', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '学员信息删除失败！', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '未检索到学员信息', 'result' => $result);
        }
        return $res;
    }


    //获取单个学员信息
    function getStudentOne($paramArray)
    {
        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,e.enrolled_status,s.student_birthday 
                from smc_student as s 
                left join smc_student_enrolled as e on s.student_id = e.student_id 
                where s.company_id = '{$paramArray['company_id']}' and s.student_id = '{$paramArray['student_id']}'
            ";
        $studentOne = $this->DataControl->selectOne($sql);

        if ($studentOne) {
            $status = $this->LgArraySwitch(array("0" => "待入班", "1" => "已入班", "-1" => "已离校"));
            $studentOne['enrolled_status'] = $status[$studentOne['enrolled_status']];

            if ($studentOne['student_birthday'] && $studentOne['student_birthday'] != '') {
                $studentOne['age'] = $this->getAge($studentOne['student_birthday']);
            } else {
                $studentOne['age'] = 0;
            }

        }


        $field = array();
        $field["student_cnname"] = "学员中文名";
        $field["student_enname"] = "学员英文名";
        $field["student_branch"] = "编号";
        $field["student_sex"] = "性别";
        $field["enrolled_status"] = "状态";
        $field["student_img"] = "头像";

        $result = array();
        if ($studentOne) {
            $result["field"] = $field;
            $result["data"][] = $studentOne;
            $res = array('error' => '0', 'errortip' => '获取学员信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取学员信息失败', 'result' => $result);
        }
        return $res;
    }


    function getParentList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (p.parenter_cnname like '%{$request['keyword']}%' or p.parenter_mobile like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and e.school_id ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $datawheres = " 1 ";

        if ($request['iswx'] == '0') {
            if (isset($request['iswx']) && $request['iswx'] !== "") {
                $datawheres .= " and wx = '{$request['iswx']}'";
            }
        }

        if ($request['iswx'] == '1') {
            if (isset($request['iswx']) && $request['iswx'] !== "") {
                $datawheres .= " and wx >='{$request['iswx']}'";
            }
        }

        $sql = "
            SELECT
                p.parenter_id,
                p.parenter_cnname,
                p.parenter_mobile,
                p.parenter_lasttime,
                FROM_UNIXTIME(p.parenter_lasttime, '%Y-%m-%d %H:%i') as time, 
                p.parenter_lasttime,
                p.parenter_lastip,
                f.family_cnname,
                IFNULL( w.wxchattoken_id, 0 ) AS wx,
                (
            SELECT
                count( a.parenter_id ) 
            FROM
                smc_student_family AS a
                LEFT JOIN smc_student AS b ON a.student_id = b.student_id 
            WHERE
                a.parenter_id = p.parenter_id AND b.company_id = '{$request['company_id']}'
            GROUP BY
                a.parenter_id 
                ) AS num
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                LEFT JOIN smc_student AS s ON f.student_id = s.student_id
                LEFT JOIN smc_parenter_wxchattoken AS w ON p.parenter_id = w.parenter_id 
                LEFT JOIN smc_student_enrolled AS e ON e.student_id = s.student_id 
            WHERE
                {$datawhere} AND s.company_id = '{$request['company_id']}'
            GROUP BY
                p.parenter_id
                having {$datawheres}";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    if ($this->stafferOne && $this->stafferOne['account_class'] == 1) {
                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    } else {
                        $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'] ? hideNumberString($dateexcelvar['parenter_mobile']) : '';
                    }
                    if ($dateexcelvar['wx'] == '0') {
                        $datearray['wx'] = '否';
                    } else {
                        $datearray['wx'] = '是';
                    }
                    $datearray['num'] = $dateexcelvar['num'];
                    if ($dateexcelvar['parenter_lasttime'] == '0') {
                        $datearray['time'] = '--';
                    } else {
                        $datearray['time'] = $dateexcelvar['time'];
                    }
                    if ($dateexcelvar['parenter_lastip'] == '') {
                        $datearray['parenter_lastip'] = '--';
                    } else {
                        $datearray['parenter_lastip'] = $dateexcelvar['parenter_lastip'];
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("家长姓名", "手机号", "是否绑定微信", "关联孩子数量", "最后登录时间", "最后登录IP"));
            $excelfileds = array('parenter_cnname', 'parenter_mobile', 'wx', 'num', 'time', 'parenter_lastip');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("家长管理.xlsx"));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $stuList = $this->DataControl->selectClear($sql);

            if (!$stuList) {
                $this->error = true;
                $this->errortip = "无家长数据";
                return false;
            } else {
                foreach ($stuList as &$val) {
                    if ($val['wx'] == '0') {
                        $val['wx'] = '0';
                    } else {
                        $val['wx'] = '1';
                    }
                    if ($val['parenter_lasttime'] == '0') {
                        $val['time'] = '--';
                    } else {
                        $val['time'] = $val['time'];
                    }
                    if ($val['parenter_lastip'] == '') {
                        $val['parenter_lastip'] = '--';
                    } else {
                        $val['parenter_lastip'] = $val['parenter_lastip'];
                    }
                    if (!$val['parenter_cnname']) {
                        $val['parenter_cnname'] = $val['family_cnname'];
                    }
                }
            }
            $data = array();
            $count_sql = "
                SELECT
                    p.parenter_id,
                    IFNULL( w.wxchattoken_id, 0 ) AS wx
                FROM
                    smc_parenter AS p
                    LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                    LEFT JOIN smc_student AS s ON f.student_id = s.student_id
                    LEFT JOIN smc_parenter_wxchattoken AS w ON p.parenter_id = w.parenter_id 
                    LEFT JOIN smc_student_enrolled AS e ON e.student_id = s.student_id 
                WHERE
                    {$datawhere} AND s.company_id = '{$request['company_id']}' 
                GROUP BY
                    p.parenter_id
                    having {$datawheres}
            ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
            $data['list'] = $stuList;

            return $data;
        }

    }

    //关联孩子信息
    function getRelevanceStudent($request)
    {
        $sql = "
            SELECT 
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_branch,
                s.student_sex,
                s.student_birthday,
                f.family_isdefault
            FROM
                smc_student AS s
                LEFT JOIN smc_student_family AS f ON s.student_id = f.student_id
            WHERE s.company_id = '{$request['company_id']}' and f.parenter_id = '{$request['parenter_id']}'";

        $stuList = $this->DataControl->selectClear($sql);

        if (!$stuList) {
            $this->error = true;
            $this->errortip = "无孩子数据";
            return false;
        } else {
            foreach ($stuList as &$val) {
                if ($val['family_isdefault'] == '0') {
                    $val['family_isdefault'] = '否';
                } else {
                    $val['family_isdefault'] = '是';
                }
            }
        }
        $data = array();
        $count_sql = "
                SELECT 
                    s.student_id
                FROM
                    smc_student AS s
                    LEFT JOIN smc_student_family AS f ON s.student_id = f.student_id
                WHERE s.company_id = '{$request['company_id']}' and f.parenter_id = '{$request['parenter_id']}'
            ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $stuList;

        return $data;

    }


    function studentClass($request)
    {

        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['class_type']) && $request['class_type'] !== '') {
            $datawhere .= " and c.class_type = '{$request['class_type']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "SELECT ss.student_id,c.class_id,c.class_branch,c.class_cnname,c.class_enname,sc.course_branch,sc.course_cnname,c.class_status,c.class_stdate,ss.study_beginday,ss.study_endday,pt.tuition_buypiece,c.class_type,h.school_cnname,h.school_branch
                ,sc.course_classnum as class_num,ss.study_isreading,sc.course_id,sc.course_inclasstype,c.school_id
                ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ss.class_id and sch.hour_ischecking<>'-1') as hour_num
                ,(select count(sch.hour_id) from smc_class_hour as sch where sch.class_id=ss.class_id and sch.hour_ischecking=1) as class_clock_num
                ,(select count(ssh.hourstudy_id) from smc_student_hourstudy as ssh where ssh.student_id=ss.student_id and ssh.class_id=ss.class_id and ssh.hourstudy_checkin=1) as stu_clock_num
                ,(select scb.coursebalance_time from smc_student_coursebalance as scb where scb.school_id='{$request['school_id']}' and scb.course_id=sc.course_id and scb.student_id=ss.student_id limit 0,1) as coursebalance_time
                ,(select scb.coursebalance_figure from smc_student_coursebalance as scb where scb.school_id='{$request['school_id']}' and scb.course_id=sc.course_id and scb.student_id=ss.student_id limit 0,1) as coursebalance_figure
                from smc_class as c
                left join smc_student_study as ss on ss.class_id=c.class_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_fee_pricing as fp on fp.course_id=sc.course_id
                left join smc_fee_pricing_tuition as pt on pt.pricing_id=fp.pricing_id
                left join smc_school as h on ss.school_id = h.school_id
                WHERE {$datawhere} and ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}'
                group by c.class_id
                order by c.class_stdate DESC
                limit {$pagestart},{$num}";
        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $class_status = $this->LgArraySwitch(array("0" => "待开班", "1" => "进行中", "-1" => "已结束", "-2" => "已删除"));
        $isreading = $this->LgArraySwitch(array("0" => "不在读", "1" => "在读", "-1" => "已结束"));
        $type = $this->LgArraySwitch(array("0" => "父班", "1" => "子班"));
        foreach ($classList as &$classOne) {
            $classOne['class_status'] = $class_status[$classOne['class_status']];
            $classOne['class_type_name'] = $type[$classOne['class_type']];
            $classOne['study_isreading_name'] = $isreading[$classOne['study_isreading']];
            $classOne['study_endday'] = $classOne['study_endday'] ? $classOne['study_endday'] : '--';
            if ($classOne['hour_num'] > 0) {
                $classOne['class_num'] = $classOne['hour_num'];
            }

        }
        $data = array();
        $count_sql = "SELECT c.class_id
                from smc_class as c
                left join smc_student_study as ss on ss.class_id=c.class_id
                left join smc_course as sc on sc.course_id=c.course_id
                left join smc_fee_pricing as fp on fp.course_id=sc.course_id
                WHERE {$datawhere} and ss.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and ss.student_id='{$request['student_id']}'
                group by c.class_id
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $classList;


        return $data;
    }

    //单个学员的考勤
    function studentAttendance($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sco.course_branch like '%{$request['keyword']}%' or sco.course_cnname like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and sch.hour_day>='{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and sch.hour_day<='{$request['endtime']}'";
        }
        if (isset($request['hourstudy_checkin']) && $request['hourstudy_checkin'] !== '') {
            $datawhere .= " and sh.hourstudy_checkin='{$request['hourstudy_checkin']}'";
        }
        if (isset($request['hour_way']) && $request['hour_way'] !== '') {
            $datawhere .= " and sch.hour_way='{$request['hour_way']}'";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sco.coursetype_id='{$request['coursetype_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sch.hour_day,sco.course_branch,sco.course_cnname,c.class_cnname,c.class_branch,sh.hourstudy_checkin,sc.clockinginlog_note,st.staffer_cnname,sc.clockinginlog_price,sch.hour_way,h.school_cnname,h.school_branch
              from smc_student_clockinginlog as sc
              left join smc_student_hourstudy as sh on sh.hourstudy_id=sc.hourstudy_id
              left join smc_class_hour as sch on sch.hour_id=sh.hour_id
              left join smc_class_hour_teaching as sct on sct.class_id=sch.class_id
              left join smc_staffer as st on st.staffer_id=sct.staffer_id
              left join smc_class as c on c.class_id=sch.class_id
              left join smc_course as sco on sco.course_id=c.course_id
              left join smc_school as h on h.school_id = sc.school_id
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.student_id='{$request['student_id']}'
              group by sc.clockinginlog_id
              order by clockinginlog_day DESC";

        $checkinArray = $this->LgArraySwitch(array("0" => "缺勤", "1" => "出勤"));


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $studentOne = $this->DataControl->getFieldOne("smc_student", "student_cnname", "student_id='{$request['student_id']}'");

            $dateexcelarray = $this->DataControl->selectClear($sql);

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['hour_way_name'] = $this->LgStringSwitch($dateexcelvar['hour_way'] == 1 ? '线上课' : '实体课');
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['hourstudy_checkin'] = $checkinArray[$dateexcelvar['hourstudy_checkin']];
                    $datearray['clockinginlog_price'] = $dateexcelvar['clockinginlog_price'];
                    $datearray['clockinginlog_note'] = $dateexcelvar['clockinginlog_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "上课时间", "课程别名称", "课程别编号", "上课方式", "班级名称", "班级编号", "出勤状态", "平摊金额", "缺勤原因", '教师'));
            $excelfileds = array('school_cnname', 'school_branch', 'hour_day', 'course_cnname', 'course_branch', 'hour_way_name', 'class_cnname', 'class_branch', 'hourstudy_checkin', 'clockinginlog_price', 'clockinginlog_note', 'staffer_cnname');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("{$studentOne['student_cnname']}学员考勤列表.xlsx"));
            exit;

        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;

            $attendanceList = $this->DataControl->selectClear($sql);

            if (!$attendanceList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($attendanceList as &$val) {
                $val['hourstudy_checkin'] = $checkinArray[$val['hourstudy_checkin']];
                $val['hour_way_name'] = $this->LgStringSwitch($val['hour_way'] == 1 ? '线上课' : '实体课');

            }

            $data = array();
            $count_sql = "select sc.clockinginlog_id
              from smc_student_clockinginlog as sc
              left join smc_student_hourstudy as sh on sh.hourstudy_id=sc.hourstudy_id
              left join smc_class_hour as sch on sch.hour_id=sh.hour_id
              left join smc_class_hour_teaching as sct on sct.class_id=sch.class_id
              left join smc_staffer as st on st.staffer_id=sct.staffer_id
              left join smc_class as c on c.class_id=sch.class_id
              left join smc_course as sco on sco.course_id=c.course_id
              where {$datawhere} and c.school_id='{$request['school_id']}' and c.company_id='{$request['company_id']}' and sc.student_id='{$request['student_id']}'
              group by sc.clockinginlog_id";
            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        $data['list'] = $attendanceList;
        return $data;
    }


    function order($request)
    {//缺少上课时间
        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or st.trading_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['tradingtype_code']) && $request['tradingtype_code'] !== '') {
            $datawhere .= " and st.tradingtype_code = '{$request['tradingtype_code']}'";

            if (isset($request['status']) && $request['status'] != '') {
                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge') {

                    $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.order_status='{$request['status']}')";


                } elseif ($request['tradingtype_code'] == 'Accountrefund') {

                    $datawhere .= " and exists (select po.trading_pid from smc_refund_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.refund_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'CourseForward') {

                    $datawhere .= " and exists (select po.trading_pid from smc_forward_dealorder as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.dealorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

                    $datawhere .= " and exists (select po.trading_pid from smc_course_reduceorder as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.reduceorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ClassGiving') {

                    $datawhere .= " and exists (select po.trading_pid from smc_freehour_order as po where po.trading_pid=st.trading_pid and po.school_id='{$request['school_id']}' and po.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferIn') {

                    $datawhere .= " and exists (select po.trading_topid from smc_school_trading as po where po.trading_topid=st.trading_pid and po.to_school_id='{$request['school_id']}' and po.trading_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferOut') {

                    $datawhere .= " and exists (select po.trading_frompid from smc_school_trading as po where po.trading_frompid=st.trading_pid and po.from_school_id='{$request['school_id']}' and po.trading_status='{$request['status']}')";

                }
            }
        }

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and s.student_id = '{$request['student_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and st.trading_createtime >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime(($request['endtime'] . ' 23:59:59'));
            $datawhere .= " and st.trading_createtime <= '{$request['endtime']}'";
        }

        $compwhere = '';
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and st.school_id='{$request['school_id']}'";
            $compwhere .= " a.school_id='{$request['school_id']}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and st.companies_id='{$request['companies_id']}'";
        }

        $tradeList = $this->DataControl->selectClear("select st.trading_pid,st.tradingtype_code
            ,s.student_cnname,s.student_enname,s.student_branch
            ,st.trading_createtime,st.tradingtype_code,ct.tradingtype_name
            ,st.student_id,st.trading_status,sc.school_cnname,sc.school_branch,st.school_id
            ,gc.companies_cnname
            from smc_student_trading as st
            left join smc_student as s on s.student_id=st.student_id
            left join smc_code_tradingtype as ct ON ct.tradingtype_code =st.tradingtype_code
            left join smc_school as sc on sc.school_id=st.school_id
            left join gmc_code_companies as gc on gc.companies_id=st.companies_id
            where {$datawhere} and st.company_id='{$request['company_id']}' and st.tradingtype_code<>'Subscribed' and st.tradingtype_code<>'MonthlyShare'
            order by st.trading_id desc
            limit {$pagestart},{$num}
            ");
//        and st.tradingtype_code<>'Subscribed' and st.tradingtype_code<>'ReduceCourse'
        if (!$tradeList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $list = array();

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $order_isinvoice = $this->LgArraySwitch(array("0" => "未开具", "1" => "已开具"));
        $porder_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $rorder_type = $this->LgArraySwitch(array("0" => "银行转账", "1" => "原路返还"));
        $porder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));
        $rorder_status = $this->LgArraySwitch(array('0' => '待审核(校园)', '1' => '待审核(集团)', '2' => '待处理', '3' => '待确定金额', '4' => '已完成', '-1' => '审核拒绝'));
        $dealorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        foreach ($tradeList as $val) {
            $data = array();
            $data['trading_pid'] = $val['trading_pid'];
            $data['student_id'] = $val['student_id'];
            $data['school_id'] = $val['school_id'];
            $data['school_cnname'] = $val['school_cnname'];
            $data['school_branch'] = $val['school_branch'];
            $data['companies_cnname'] = $val['companies_cnname'];
            $data['student_cnname'] = $val['student_cnname'];
            $data['student_enname'] = $val['student_enname'];
            $data['student_branch'] = $val['student_branch'];
            $data['tradingtype_name'] = $val['tradingtype_name'];
            $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

            if ($val['tradingtype_code'] == 'CourseForward') {
                $ForwardOne = $this->DataControl->selectOne("select dealorder_pid,dealorder_status,dealorder_balanceprice,dealorder_forwardprice from smc_forward_dealorder where trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $ForwardOne['dealorder_pid'];
                $data['order_type'] = $this->LgStringSwitch('课程结转订单');
                $data['order_status'] = $dealorder_status[$ForwardOne['dealorder_status']];
                $data['order_from'] = '--';
                $data['order_allprice'] = '--';
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['order_paymentprice'] = '--';
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = $ForwardOne['dealorder_balanceprice'];
                $data['dealorder_forwardprice'] = $ForwardOne['dealorder_forwardprice'];
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['parenter_cnname'] = '--';
                $data['parenter_mobile'] = '--';
                $data['can_debt'] = '0';


            } elseif ($val['tradingtype_code'] == 'Accountrefund') {
                $refundOne = $this->DataControl->selectOne("select refund_pid,refund_type,refund_payprice,refund_status,refund_specialprice,refund_from,refund_price,refund_class from smc_refund_order where trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $refundOne['refund_pid'];
                $data['order_type'] = $rorder_type[$refundOne['refund_type']];
                $data['order_status'] = $rorder_status[$refundOne['refund_status']];
                $data['order_from'] = $order_from[$refundOne['refund_from']];
                $data['order_allprice'] = $refundOne['refund_payprice'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['order_paymentprice'] = $refundOne['refund_payprice'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = $refundOne['refund_payprice'];
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['order_refusereason'] = '--';
                $data['can_cancel'] = 0;
                $data['parenter_cnname'] = '--';
                $data['parenter_mobile'] = '--';
                if ($refundOne['refund_class'] == 0 && $refundOne['refund_status'] == 0) {
                    $data['is_should_check'] = 1;
                } else {
                    $data['is_should_check'] = 0;
                }
                $data['can_debt'] = '0';
                $data['can_pay'] = '0';
            } elseif ($val['tradingtype_code'] == 'ReduceCourse') {
                $reduceOne = $this->DataControl->selectOne("select reduceorder_pid,reduceorder_status,reduceorder_figure from smc_course_reduceorder where trading_pid='{$val['trading_pid']}'");
                $reduceorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝', "-2" => '已取消'));

                $data['order_pid'] = $reduceOne['reduceorder_pid'];
                $data['order_type'] = '扣课订单';
                $data['order_status'] = $reduceorder_status[$reduceOne['reduceorder_status']];
                $data['order_from'] = '教师下单';
                $data['order_allprice'] = $reduceOne['reduceorder_figure'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['order_paymentprice'] = $reduceOne['reduceorder_figure'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['parenter_cnname'] = '--';
                $data['parenter_mobile'] = '--';
                $data['can_debt'] = '0';

            } elseif ($val['tradingtype_code'] == 'TransferIn') {
                $sql = "select * from smc_school_trading where trading_topid='{$val['trading_pid']}' and company_id='{$request['company_id']}'";

                $schoolTradeOne = $this->DataControl->selectOne($sql);
                $status = $this->LgArraySwitch(array('0' => '待审核', '2' => '已完成', '-1' => '审核拒绝'));
                $data['order_pid'] = '--';
                $data['order_type'] = $this->LgStringSwitch('学员余额转入');
                $data['order_status'] = $status[$schoolTradeOne['trading_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $schoolTradeOne['trading_price'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['order_paymentprice'] = $schoolTradeOne['trading_price'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['parenter_cnname'] = '--';
                $data['parenter_mobile'] = '--';
                $data['can_debt'] = '0';


            } elseif ($val['tradingtype_code'] == 'TransferOut') {
                $sql = "select * from smc_school_trading where trading_frompid='{$val['trading_pid']}' and company_id='{$request['company_id']}'";
                $schoolTradeOne = $this->DataControl->selectOne($sql);
                $status = $this->LgArraySwitch(array('0' => '待审核', '2' => '已完成', '-1' => '审核拒绝'));
                $data['order_pid'] = '--';
                $data['order_type'] = $this->LgStringSwitch('学员余额转出');
                $data['order_status'] = $status[$schoolTradeOne['trading_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $schoolTradeOne['trading_price'];
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['order_paymentprice'] = $schoolTradeOne['trading_price'];
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';
                $data['parenter_cnname'] = '--';
                $data['parenter_mobile'] = '--';
                $data['can_debt'] = '0';


            } elseif ($val['tradingtype_code'] == 'ClassGiving') {
                $orderOne = $this->DataControl->selectOne("select order_pid,order_status,order_alltimes,order_refusereason from smc_freehour_order where trading_pid='{$val['trading_pid']}'");

                $freeorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

                $data['order_pid'] = $orderOne['order_pid'];
                $data['order_type'] = $this->LgStringSwitch('课次赠送订单');
                $data['order_status'] = $freeorder_status[$orderOne['order_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = '--';
                $data['coupon_price'] = '--';
                $data['balance'] = '--';
                $data['forward'] = '--';
                $data['order_paymentprice'] = '--';
                $data['order_paidprice'] = '--';
                $data['order_arrears'] = '--';
                $data['order_isinvoice'] = '--';
                $data['dealorder_balanceprice'] = '--';
                $data['dealorder_forwardprice'] = '--';
                $data['is_should_pay'] = 0;
                $data['parenter_cnname'] = '--';
                $data['parenter_mobile'] = '--';
                $data['can_cancel'] = 0;
                $data['is_should_check'] = 0;
                $data['can_debt'] = '0';
                $data['order_refusereason'] = $orderOne['order_refusereason'];
            } else {
                $orderOne = $this->DataControl->selectOne("select po.order_pid,po.order_type,po.order_status,po.order_from,po.order_allprice,po.order_coupon_price,po.order_market_price,po.student_id,po.school_id,po.order_paymentprice,po.order_arrearageprice,parenter_cnname,parenter_mobile
              ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=1 and pop.paytype_code='balance') as balance
              ,(select sum(pop.pay_price) from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=1 and pop.paytype_code='forward') as forward
              ,ifnull((select pop.pay_id from smc_payfee_order_pay as pop where pop.order_pid=po.order_pid and pop.pay_issuccess=0 and pop.paytype_code='canceldebts'),0) as pay_id
              ,po.order_paymentprice,po.order_paidprice,po.order_isinvoice
              from smc_payfee_order as po
              left join smc_parenter as sp on sp.parenter_id=po.parenter_id
              where po.trading_pid='{$val['trading_pid']}'");

                $dealorder_balanceprice = "--";
                if ($orderOne['order_type'] == 2 && $orderOne['order_status'] == 4) {
                    $dealorder_balanceprice = $orderOne['order_paymentprice'];
                }
                if ($orderOne['order_status'] == 4 && $orderOne['order_type'] <> 2) {
                    $order_pay = $this->DataControl->selectOne("select sum(pay_price) as price_sum from smc_payfee_order_pay where  paytype_code='balance' and pay_issuccess =1 and  pay_isrefund=0 and order_pid='{$orderOne['order_pid']}' ");
                    $dealorder_balanceprice = $order_pay['price_sum'];
                }
                if ($orderOne['order_from'] == '2' || $orderOne['order_status'] < 0) {
                    $data['can_cancel'] = 0;
                } else {
                    $data['can_cancel'] = 1;
                }
                $data['order_pid'] = $orderOne['order_pid'];
                $data['order_type'] = $porder_type[$orderOne['order_type']];
                $data['order_status'] = $porder_status[$orderOne['order_status']];
                $data['order_from'] = $order_from[$orderOne['order_from']];
                $data['order_allprice'] = $orderOne['order_allprice'];
                $data['coupon_price'] = $orderOne['order_coupon_price'] + $orderOne['order_market_price'];
                if ($orderOne['balance']) {
                    $data['balance'] = $orderOne['balance'];
                } else {
                    $data['balance'] = 0;
                }
                if ($orderOne['forward']) {
                    $data['forward'] = $orderOne['forward'];
                } else {
                    $data['forward'] = 0;
                }
                $data['order_paymentprice'] = $orderOne['order_paymentprice'];
                $data['order_paidprice'] = $orderOne['order_paidprice'];
                $data['order_arrears'] = $orderOne['order_paymentprice'] - $orderOne['order_paidprice'];
                $data['order_isinvoice'] = $order_isinvoice[$orderOne['order_isinvoice']];

                $data['dealorder_balanceprice'] = $dealorder_balanceprice;
                $data['parenter_cnname'] = $orderOne['parenter_cnname'] ? $orderOne['parenter_cnname'] : '--';
                $data['parenter_mobile'] = $orderOne['parenter_mobile'] ? $orderOne['parenter_mobile'] : '--';

                $data['is_should_check'] = 0;
                $data['order_refusereason'] = '--';

                if ($orderOne['order_status'] > 0 && $orderOne['order_status'] < 4) {
                    if ($orderOne['pay_id'] > 0) {
                        $data['is_should_pay'] = '0';
                    } else {
                        $data['is_should_pay'] = '1';
                    }
                } else {
                    $data['is_should_pay'] = 0;
                }
                if ($orderOne['order_type'] == 0 && $data['order_arrears'] > 0 && $orderOne['order_status'] > 0 && $orderOne['order_arrearageprice'] > 0 && $orderOne['pay_id'] == 0) {
                    $sql = "select scb.coursebalance_id
                          from smc_payfee_order_course as poc 
                          left join smc_payfee_order as po on po.order_pid=poc.order_pid
                          left join smc_student_coursebalance as scb on scb.student_id=po.student_id and scb.course_id=poc.course_id and scb.school_id=po.school_id
                          where scb.coursebalance_figure>0 and poc.order_pid='{$orderOne['order_pid']}'";

                    if ($this->DataControl->selectOne($sql)) {
                        $data['can_debt'] = '0';
                    } else {
                        $data['can_debt'] = '1';
                    }
                } else {
                    $data['can_debt'] = '0';
                }
            }

            $list[] = $data;
        }
        $count_sql = "select st.trading_id
            from smc_student_trading as st
            left join smc_student as s on s.student_id=st.student_id
            where {$datawhere} and st.company_id='{$request['company_id']}' and st.tradingtype_code<>'Subscribed' and st.tradingtype_code<>'MonthlyShare'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $list;

        $sql = "select a.companies_id,b.companies_cnname 
            from smc_student_trading a
            left join gmc_code_companies b on a.companies_id=b.companies_id
            WHERE {$compwhere}
            and a.company_id = '{$request['company_id']}'
            and a.student_id='{$request['student_id']}' 
            group by a.school_id,a.companies_id ";
        $companiesList = $this->DataControl->selectClear($sql);
        $data['companieslist'] = $companiesList ? $companiesList : array();

        return $data;
    }

    function classChange($request)
    {
        $datawhere = " 1 ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%'  or c.class_branch like '%{$request['keyword']}%'  or c.class_cnname like '%{$request['keyword']}%'  or c.class_enname like '%{$request['keyword']}%' )";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and sc.changelog_day>='{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and sc.changelog_day<='{$request['endtime']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and sc.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and sc.student_id ='{$request['student_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and sc.school_id='{$request['school_id']}'";
        }

        $sql = "select sc.changelog_id,sc.class_id,s.student_cnname,s.student_enname,s.student_branch,cs.stuchange_name,cs.stuchange_code,sch.school_cnname,c.class_cnname,c.class_enname,c.class_branch,sc.changelog_note,sc.changelog_day,cs.stustatus_isdel,sc.change_pid,sc.stuchange_code,sc.student_id,
				(select s.staffer_cnname from smc_staffer as s where s.staffer_id =sc.staffer_id ) as staffer_cnname
                from smc_student_changelog as sc
                left join smc_student as s on s.student_id=sc.student_id
                left join smc_code_stuchange as cs on cs.stuchange_code=sc.stuchange_code
                left join smc_school as sch on sch.school_id=sc.school_id
                left join smc_class as c on c.class_id=sc.class_id
                WHERE {$datawhere} and sc.company_id='{$request['company_id']}'
                group by sc.changelog_id
                order by sc.changelog_id desc
                ";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $changeList = $this->DataControl->selectClear($sql);

        if (!$changeList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $count_sql = "select sc.changelog_id
                from smc_student_changelog as sc
                left join smc_student as s on s.student_id=sc.student_id
                left join smc_code_stuchange as cs on cs.stuchange_code=sc.stuchange_code
                left join smc_school as sch on sch.school_id=sc.school_id
                left join smc_class as c on c.class_id=sc.class_id
                WHERE {$datawhere} and sc.company_id='{$request['company_id']}'
                group by sc.changelog_id
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $changeList;

        return $data;
    }

    //单个学生的请假记录
    function studentAbsence($request)
    {
        $datawhere = " a.company_id = '{$request['company_id']}' and a.school_id = '{$request['school_id']}' ";

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and a.student_id = '{$request['student_id']}'";
        }
        if (isset($request['absence_status']) && $request['absence_status'] !== '') {
            $datawhere .= " and a.absence_status = '{$request['absence_status']}'";
        }
        if (isset($request['absence_type']) && $request['absence_type'] !== '') {
            $datawhere .= " and a.absence_type = '{$request['absence_type']}'";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and a.absence_starttime >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and a.absence_endtime <= '{$request['endtime']}'";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sd.student_cnname like '%{$request['keyword']}%' or sd.student_enname like '%{$request['keyword']}%' or sd.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    a.absence_id,a.absence_starttime,a.absence_endtime,a.absence_type,a.absence_status,a.parenter_id,a.apply_staffer_id,a.absence_reasonnote,sd.student_id,sd.student_cnname,sd.student_enname,sd.student_branch,s.school_cnname,s.school_branch,
                    (SELECT COUNT(h.hour_id) FROM smc_student_absence_hour as h WHERE h.absence_id = a.absence_id) as hour_allnum,
                    (SELECT COUNT(h.hour_id) FROM smc_student_absence_hour as h WHERE h.absence_id = a.absence_id AND h.absence_hour_status = '1') as hour_valid_num,
                    (SELECT COUNT(h.hour_id) FROM smc_student_absence_hour as h LEFT JOIN smc_class_hour as ch ON ch.hour_id = h.hour_id WHERE h.absence_id = a.absence_id AND ch.hour_ischecking = '0') as hour_num
                FROM
                    smc_student_absence as a
                LEFT JOIN
                    smc_student as sd ON sd.student_id = a.student_id
                LEFT JOIN
                    smc_school as s on s.school_id = a.school_id
                WHERE
                    {$datawhere}
                ORDER BY
                    a.absence_id DESC
                LIMIT {$pagestart},{$num}";
        $absenceList = $this->DataControl->selectClear($sql);
        if ($absenceList) {
            $type = $this->LgArraySwitch(array("0" => "事假", "1" => "病假", "2" => '其他'));
            $status = $this->LgArraySwitch(array("0" => "待审批", "1" => "已通过", "-1" => '已拒绝', "-2" => '已撤销'));
            foreach ($absenceList as &$val) {
                $val['absence_type_name'] = $type[$val['absence_type']];
                $val['absence_status_name'] = $status[$val['absence_status']];
                $val['absence_time'] = $val['absence_starttime'] . $this->LgStringSwitch('至') . $val['absence_endtime'];
                if ($val['hour_allnum'] == $val['hour_valid_num'] && $val['hour_allnum'] == $val['hour_num']) {
                    $val['is_revoke'] = true;
                } else {
                    $val['is_revoke'] = false;
                }
                if ($val['parenter_id']) {
                    $parenter = $this->DataControl->getFieldOne("smc_parenter", "parenter_cnname", "parenter_id='{$val['parenter_id']}'");
                    $val['applicant'] = $parenter['parenter_cnname'];
                } else {
                    $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$val['apply_staffer_id']}'");
                    $val['applicant'] = $staffer['staffer_cnname'];
                }
            }
        } else {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $data = array();
        $count_sql = "SELECT a.absence_id FROM smc_student_absence as a LEFT JOIN smc_student as sd ON sd.student_id = a.student_id WHERE {$datawhere}";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $absenceList;
        return $data;
    }

    //单个学员的课程别余额
    function studentCoursebalance($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.course_cnname like '%{$request['keyword']}%' or sc.course_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "SELECT sc.course_id,sc.course_cnname,sc.course_branch,pt.tuition_originalprice,pt.tuition_sellingprice,fa.agreement_cnname,ssc.coursebalance_time,ssc.coursebalance_figure,ifnull(scf.courseforward_price,0) as courseforward_price,ssc.pricing_id,ssc.coursebalance_unitexpend,ssc.coursebalance_unitearning,sc.course_inclasstype
              ,(select SUM(poc.ordercourse_buynums) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=ssc.course_id and po.student_id=ssc.student_id and po.school_id=ssc.school_id and po.order_status>0 limit 0,1) as ordercourse_buynums
              ,(select ss.study_isreading from smc_student_study as ss left join smc_class as c on c.class_id=ss.class_id where ss.student_id=ssc.student_id and c.course_id=ssc.course_id order by ss.study_beginday desc,ss.study_id desc limit 0,1) as study_isreading
              ,(select SUM(po.order_paidprice) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=ssc.course_id and po.student_id=ssc.student_id and po.school_id=ssc.school_id and po.order_status>0 limit 0,1) as order_paidprice
              ,(select SUM(po.order_coupon_price) from smc_payfee_order as po left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid where poc.course_id=ssc.course_id and po.student_id=ssc.student_id and po.school_id=ssc.school_id and po.order_status>0 limit 0,1) as order_coupon_price,h.school_cnname,h.school_branch
              ,c.class_id,c.class_cnname,sc.main_course_id
              from smc_student_coursebalance as ssc
              left join smc_student_courseforward as scf on scf.course_id=ssc.course_id and scf.student_id=ssc.student_id
              left join smc_course as sc on sc.course_id=ssc.course_id
              left join smc_fee_pricing as fp on fp.course_id=ssc.course_id and fp.pricing_id=ssc.pricing_id
              left join smc_fee_pricing_tuition as pt on pt.pricing_id=ssc.pricing_id
              left join smc_fee_agreement as fa on fa.agreement_id=fp.agreement_id
              left join smc_school as h on ssc.school_id = h.school_id
              left join smc_student_study as ss on ss.student_id=ssc.student_id and ss.school_id=ssc.school_id and ss.study_isreading=1
              left join smc_class as c on c.class_id=ss.class_id and c.course_id=ssc.course_id
              where {$datawhere} and ssc.student_id='{$request['student_id']}' and ssc.school_id='{$request['school_id']}'
              group by ssc.coursebalance_id
              order by ssc.coursebalance_createtime DESC
              limit {$pagestart},{$num}
              ";
        $coursebalance = $this->DataControl->selectClear($sql);
        if (!$coursebalance) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $isreading = $this->LgArraySwitch(array("0" => "不在读", "1" => "在读", "-1" => "已结束", "null" => "不在读"));
        foreach ($coursebalance as &$courseOne) {
            if ($courseOne['study_isreading']) {
                $courseOne['study_isreading'] = $isreading[$courseOne['study_isreading']];
            } else {
                $courseOne['study_isreading'] = $this->LgStringSwitch('未入班');
            }
            if ($courseOne['pricing_id'] == '0') {
                $courseOne['tuition_originalprice'] = '--';
                $courseOne['tuition_sellingprice'] = '--';
            }
            $courseOne['coursebalance_unitearning'] = $courseOne['coursebalance_unitearning'] == '0' ? $courseOne['coursebalance_unitexpend'] : $courseOne['coursebalance_unitearning'];

        }
        $data = array();
        $count_sql = "select ssc.coursebalance_id
              from smc_student_coursebalance as ssc
              left join smc_course as sc on sc.course_id=ssc.course_id
              where {$datawhere} and ssc.student_id='{$request['student_id']}' and ssc.school_id='{$request['school_id']}'
              group by ssc.coursebalance_id";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $coursebalance;

        return $data;

    }

    function studentBalance($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and sb.balancelog_playname like '%{$request['keyword']}%'";
        }
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and sb.balancelog_time>='{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $endtime = strtotime($request['endtime']) + '86400';
            $datawhere .= " and sb.balancelog_time<='{$endtime}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select sb.balancelog_id,sb.balancelog_playname,sb.balancelog_reason,sb.balancelog_class,sb.balancelog_playclass,sb.balancelog_fromamount,sb.balancelog_playamount,sb.balancelog_finalamount,sb.balancelog_time,s.staffer_cnname,ss.school_cnname,sb.trading_pid,st.tradingtype_code
              from smc_student_balancelog as sb
              left join smc_staffer as s on s.staffer_id=sb.staffer_id
              left join smc_school as ss on ss.school_id=sb.school_id
              left join smc_student_trading as st on st.trading_pid=sb.trading_pid
              where {$datawhere} and sb.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and sb.student_id='{$request['student_id']}' and (sb.balancelog_class='0' or sb.balancelog_class='2')
              order by sb.balancelog_time DESC,sb.balancelog_id DESC
              limit {$pagestart},{$num}
              ";
        $balance = $this->DataControl->selectClear($sql);
        if (!$balance) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        foreach ($balance as &$balanceOne) {
            $balanceOne['fromamount'] = $balanceOne['balancelog_fromamount'];
            $balanceOne['balance'] = $balanceOne['balancelog_playclass'] . $balanceOne['balancelog_playamount'];
            $balanceOne['finalamount'] = $balanceOne['balancelog_finalamount'];
            $balanceOne['balancelog_time'] = date("Y-m-d H:i", $balanceOne['balancelog_time']);
            if ($balanceOne['tradingtype_code'] == 'TransferIn' || $balanceOne['tradingtype_code'] == 'TransferOut') {
                $balanceOne['balancelog_playname'] = $this->LgStringSwitch('资产转移');
            }
        }

        $data = array();
        $count_sql = "select sb.balancelog_id
          from smc_student_balancelog as sb
          left join smc_staffer as s on s.staffer_id=sb.staffer_id
          left join smc_school as ss on ss.school_id=sb.school_id
          where {$datawhere} and sb.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and sb.student_id='{$request['student_id']}' and (sb.balancelog_class='0' or sb.balancelog_class='2')";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $balance;
        return $data;
    }

    //单个学生结转余额
    function studentForwardbalance($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and sb.balancelog_playname like '%{$request['keyword']}%'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and sb.balancelog_time>='{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $endtime = strtotime($request['endtime']) . ' 23:59:59';
            $datawhere .= " and sb.balancelog_time<='{$endtime}'";
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select sb.balancelog_id,sb.balancelog_playname,sb.balancelog_reason,sb.balancelog_class,sb.balancelog_playclass,sb.balancelog_playamount,s.staffer_cnname,ss.school_cnname,ss.school_branch
              from smc_student_balancelog as sb
              left join smc_staffer as s on s.staffer_id=sb.staffer_id
              left join smc_school as ss on ss.school_id=sb.school_id
              where {$datawhere} and sb.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and sb.student_id='{$request['student_id']}' and sb.balancelog_class='1'
              order by sb.balancelog_time DESC
              limit {$pagestart},{$num}
              ";
        $coursebalance = $this->DataControl->selectClear($sql);
        if (!$coursebalance) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $rel_courseList = array();
        if ($coursebalance) {
            foreach ($coursebalance as $balanceOne) {
                $data = array();
                $data['balancelog_id'] = $balanceOne['balancelog_id'];
                $data['balancelog_class'] = $balanceOne['balancelog_class'];
                $data['balancelog_playname'] = $balanceOne['balancelog_playname'];
                $data['balancelog_reason'] = $balanceOne['balancelog_reason'];
                $data['balance'] = $balanceOne['balancelog_playclass'] . $balanceOne['balancelog_playamount'];
                $data['staffer_cnname'] = $balanceOne['staffer_cnname'];
                $data['school_cnname'] = $balanceOne['school_cnname'];
                $data['school_branch'] = $balanceOne['school_branch'];
                $rel_courseList[] = $data;
            }
        } else {
            $rel_courseList = array();
        }

        $data = array();
        $count_sql = "select sb.balancelog_id
              from smc_student_balancelog as sb
              left join smc_staffer as s on s.staffer_id=sb.staffer_id
              left join smc_school as ss on ss.school_id=sb.school_id
              where {$datawhere} and sb.school_id='{$request['school_id']}' and ss.company_id='{$request['company_id']}' and sb.student_id='{$request['student_id']}' and sb.balancelog_class='1'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $rel_courseList;

        return $data;

    }

    //单个学员优惠券日志
    function studentCoupons($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (coupons_name like '%{$request['keyword']}%' or coupons_reason like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and coupons_usetime>='{$starttime}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $endtime = strtotime($request['endtime']) . ' 23:59:59';
            $datawhere .= " and coupons_usetime<='{$endtime}'";
        }
        if (isset($request['coupons_isuse']) && $request['coupons_isuse'] !== '') {
            $datawhere .= " and coupons_isuse = '{$request['coupons_isuse']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.coupons_class,c.coupons_id,c.coupons_isuse,c.coupons_forbid,c.coupons_forbidtime,s.school_cnname,s.school_branch
                from smc_student_coupons as c left join smc_school as s on s.school_id = c.school_id where c.company_id='{$request['company_id']}'
                and c.student_id='{$request['student_id']}' and (c.school_id=0 or c.school_id='{$request['school_id']}')
                and {$datawhere}
                limit {$pagestart},{$num}";

        $studentCouponsList = $this->DataControl->selectClear($sql);
        if (!$studentCouponsList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $coupons_class = $this->LgArraySwitch(array("0" => "营销卷", "1" => "活动券", "2" => "申请券", "3" => "微商城自领券", "4" => "活动触发券"));
        $ticket_way = $this->LgArraySwitch(array("0" => "减价模式", "1" => "折扣模式"));
        $ticket_status = $this->LgArraySwitch(array("0" => "减价模式", "1" => "折扣模式"));
        $ticket_isuse = $this->LgArraySwitch(array("0" => "未用", "1" => "已用", "-1" => "失效"));
        $tem_array = array();
        foreach ($studentCouponsList as $studentCouponsOne) {
            if ($studentCouponsOne['coupons_class'] == 0) {
                $sql = "select s.coupons_id,mt.ticket_cnname,s.coupons_class,mt.ticket_way,mtp.policy_derateprice,mtp.policy_deraterate
              ,mtp.policy_minprice,mt.ticket_status,s.coupons_usetime,s.coupons_createtime,s.coupons_reason,s.coupons_exittime,s.coupons_bindingtime,s.coupons_isuse
              from smc_student_coupons as s
              left join smc_market_ticket as mt on mt.ticket_id=s.ticket_id
              left join smc_market_ticket_policy as mtp on mtp.policy_id=s.policy_id and mtp.ticket_id=mt.ticket_id
              where s.student_id='{$request['student_id']}' and s.company_id='{$request['company_id']}' and (s.school_id=0 or s.scool_id='{$this->school_id}')
              and s.coupons_class=0 and s.coupons_id ='{$studentCouponsOne['coupons_id']}'
              order by s.coupons_id desc
              ";
                $ticketList = $this->DataControl->selectClear($sql);

                if ($ticketList) {
                    foreach ($ticketList as $ticketOne) {
                        $data = array();
                        $data['is_effective'] = 0;
                        if (($ticketOne['coupons_exittime'] >= time()) && $ticketOne['coupons_isuse'] == 0) {
                            $data['is_effective'] = 1;
                        }
                        $data['coupons_id'] = $ticketOne['coupons_id'];
                        $data['applytype_port'] = 0;
                        $data['coupons_ids'] = $ticketOne['coupons_id'];
                        $data['ticket_cnname'] = $ticketOne['ticket_cnname'];
                        $data['coupons_forbid'] = $studentCouponsOne['coupons_forbid'];
                        $data['coupons_forbidtime'] = $studentCouponsOne['coupons_forbidtime'] ? date("Y-m-d", $studentCouponsOne['coupons_forbidtime']) : '';
                        $data['coupons_reason'] = $ticketOne['coupons_reason'];
                        $data['applytype_caneditcourse'] = 0;
                        $data['coupons_class'] = $coupons_class[$ticketOne['coupons_class']];
                        $data['course'] = '--';
                        $data['ticket_way'] = $ticket_way[$ticketOne['ticket_way']];
                        $data['policy_derateprice'] = $ticketOne['policy_derateprice'] ? $ticketOne['policy_derateprice'] : '--';
                        $data['policy_deraterate'] = $ticketOne['policy_deraterate'] * 10 ? $ticketOne['policy_derateprice'] * 10 : '--';
                        $data['policy_minprice'] = $ticketOne['policy_minprice'];
                        $data['ticket_status'] = $ticket_status[$ticketOne['ticket_status']];
                        $data['coupons_exittime'] = date("Y-m-d H:i:s", $ticketOne['coupons_bindingtime']) . '~' . date("Y-m-d H:i:s", $ticketOne['coupons_exittime']);
                        $data['coupons_isuse_name'] = $ticket_isuse[$studentCouponsOne['coupons_isuse']];
                        $data['coupons_isuse'] = $studentCouponsOne['coupons_isuse'];
                        if ($ticketOne['coupons_usetime'] == 0) {
                            $data['coupons_usetime'] = '';
                        } else {
                            $data['coupons_usetime'] = date("Y-m-d", $ticketOne['coupons_usetime']);
                        }

                        $data['coupons_createtime'] = date("Y-m-d", $ticketOne['coupons_createtime']);
                        $tem_array[] = $data;
                    }
                }
            } elseif ($studentCouponsOne['coupons_class'] == 2 || $studentCouponsOne['coupons_class'] == 3 || $studentCouponsOne['coupons_class'] == 1) {

                $c_sql = "select s.coupons_id,s.coupons_class as coupons_class_num,s.coupons_name,s.coupons_class,group_concat(c.coursecat_cnname) as course,s.coupons_type,cc.applytype_port,cc.applytype_caneditcourse,s.coupons_forbid,s.coupons_forbidtime
,s.coupons_price,s.coupons_discount,s.coupons_minprice,s.coupons_usetime,s.coupons_createtime,s.coupons_reason,s.coupons_isuse,s.coupons_exittime,s.coupons_bindingtime,st.student_img,st.student_sex,st.student_cnname,st.student_enname,cc.applytype_cnname,s.coupons_playclass as apply_playclass_num,s.apply_id,a.apply_discountstype,a.apply_discountstype as apply_discountstype_status,a.apply_price,a.apply_discount,a.apply_minprice,a.apply_time,a.apply_reson,a.apply_status,a.apply_status as apply_status_num,sf.staffer_cnname,a.apply_refusetime,a.apply_refusereson,a.apply_remark,a.apply_cnname,cc.applytype_caneditcourse
                from smc_student_coupons as s
                left join smc_student as st on st.student_id = s.student_id
                left join smc_student_coupons_apply as a on a.apply_id = s.apply_id
                left join smc_staffer as sf on sf.staffer_id = a.staffer_id
                LEFT JOIN smc_code_couponsapplytype AS cc ON cc.applytype_branch = a.applytype_branch and cc.company_id = a.company_id
                left join smc_student_coupons_applycoursecat as sc on sc.apply_id=s.apply_id
                left join smc_code_coursecat as c on c.coursecat_id=sc.coursecat_id
                where s.company_id='{$request['company_id']}' and s.student_id='{$request['student_id']}' and (s.coupons_class='2' or s.coupons_class='3' or s.coupons_class='1') and s.coupons_id ='{$studentCouponsOne['coupons_id']}' and (s.school_id=0 or s.school_id='{$this->school_id}') ";

                $status2 = $this->LgArraySwitch(array("0" => "减价", "1" => "折扣"));
//            foreach ($ApplyList as &$val) {
//                $val['apply_discountstype'] = $status2[$val['apply_discountstype']];
//            }
                $status = $this->LgArraySwitch(array("0" => "项目优惠券", "1" => "订单优惠券"));

                $couponsList = $this->DataControl->selectClear($c_sql);
                if ($couponsList) {
                    foreach ($couponsList as $couponsOne) {
                        $data = array();
                        $data['is_effective'] = 0;
                        if (($couponsOne['coupons_exittime'] >= time()) && $couponsOne['coupons_isuse'] == 0) {
                            $data['is_effective'] = 1;
                        }
                        $data['coupons_id'] = $couponsOne['coupons_id'];
                        $data['student_img'] = $couponsOne['student_img'];
                        $data['student_sex'] = $couponsOne['student_sex'];
                        $data['applytype_port'] = $couponsOne['applytype_port'];
                        $data['student_cnname'] = $couponsOne['student_cnname'];
                        $data['student_enname'] = $couponsOne['student_enname'];
                        $data['applytype_cnname'] = $couponsOne['applytype_cnname'];
                        $data['applytype_caneditcourse'] = $couponsOne['applytype_caneditcourse'];
                        $data['apply_id'] = $couponsOne['apply_id'];
                        $data['apply_discountstype'] = $status2[$couponsOne['apply_discountstype']];
                        $data['apply_discountstype_status'] = $couponsOne['apply_discountstype_status'];
                        $data['apply_status_num'] = $couponsOne['apply_status_num'];
                        $data['apply_discount'] = $couponsOne['apply_discount'];
                        $data['apply_minprice'] = $couponsOne['apply_minprice'];
                        $data['apply_time'] = date("Y-m-d H:i:s", $couponsOne['apply_time']);
                        $data['apply_reson'] = $couponsOne['apply_reson'];
                        $data['apply_status'] = $couponsOne['apply_status'];
                        $data['apply_status_num'] = $couponsOne['apply_status_num'];
                        $data['staffer_cnname'] = $couponsOne['staffer_cnname'];
                        $data['apply_cnname'] = $couponsOne['apply_cnname'];
                        $data['coupons_forbid'] = $studentCouponsOne['coupons_forbid'];
                        $data['coupons_forbidtime'] = $studentCouponsOne['coupons_forbidtime'] ? date("Y-m-d", $studentCouponsOne['coupons_forbidtime']) : '';
                        $data['apply_refusetime'] = $couponsOne['apply_refusetime'] > 0 ? date("Y-m-d", $couponsOne['apply_refusetime']) : '';
                        $data['file'] = $this->DataControl->selectClear("select * from smc_student_coupons_file where apply_id = '{$couponsOne['apply_id']}'");
                        $data['apply_refusereson'] = $couponsOne['apply_refusereson'];
                        $data['apply_remark'] = $couponsOne['apply_remark'];
                        $data['ticket_cnname'] = $couponsOne['coupons_name'];
                        $data['coupons_reason'] = $couponsOne['coupons_reason'];
                        $data['apply_price'] = $couponsOne['apply_price'];
                        $data['coupons_class'] = $coupons_class[$couponsOne['coupons_class']];
                        $data['coupons_class_num'] = $couponsOne['coupons_class_num'];
                        $data['apply_playclass_num'] = $status[$couponsOne['apply_playclass_num']];
                        $data['course'] = $couponsOne['course'];
                        $data['ticket_way'] = $ticket_way[$couponsOne['coupons_type']];
                        $data['policy_derateprice'] = $couponsOne['coupons_price'] ? $couponsOne['coupons_price'] : '--';
                        $data['policy_deraterate'] = $couponsOne['coupons_discount'] * 10 ? $couponsOne['coupons_discount'] * 10 : '--';
                        $data['policy_minprice'] = $couponsOne['coupons_minprice'];
                        $data['ticket_status'] = '--';
                        $data['coupons_isuse_name'] = $ticket_isuse[$studentCouponsOne['coupons_isuse']];
                        $data['coupons_isuse'] = $studentCouponsOne['coupons_isuse'];
                        $data['coupons_exittime'] = date("Y-m-d H:i:s", $couponsOne['coupons_bindingtime']) . '~' . date("Y-m-d H:i:s", $couponsOne['coupons_exittime']);
                        if ($couponsOne['coupons_usetime'] == 0) {
                            $data['coupons_usetime'] = '';
                        } else {
                            $data['coupons_usetime'] = date("Y-m-d H:i:s", $couponsOne['coupons_usetime']);
                        }
                        $data['coupons_createtime'] = date("Y-m-d H:i:s", $couponsOne['coupons_createtime']);
                        $tem_array[] = $data;
                    }
                }
            }
        }

        $tem_data = array();
        $count_sql = "select coupons_id from smc_student_coupons where company_id='{$request['company_id']}' and student_id='{$request['student_id']}' and (school_id=0 or school_id='{$this->school_id}')";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $tem_data['allnum'] = $allnum;
        $tem_data['list'] = $tem_array;

        return $tem_data;
    }

    function apppropermis($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ca.apppropermis_name like '%{$request['keyword']}%'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ssa.apppropermislog_id,ca.apppropermis_name,ssa.apppropermis_code,ssa.apppropermis_authcode,ssa.apppropermislog_createtime,st.staffer_cnname,ssa.apppropermislog_endday,ssa.apppropermislog_isenabled,s.school_cnname,s.school_branch,co.course_cnname,co.course_branch
              from smc_student_apppropermislog as ssa
              left join smc_code_apppropermis as ca on ca.apppropermis_code=ssa.apppropermis_code and (ca.company_id = ssa.company_id or ca.company_id=0)
              left join smc_staffer as st on st.staffer_id=ssa.staffer_id
              left join smc_school as s on s.school_id = ssa.school_id
              left join smc_class as cl on cl.class_id=ssa.class_id
              left join smc_course as co on co.course_id=cl.course_id
              where {$datawhere} and ssa.company_id='{$request['company_id']}'
              and student_id='{$request['student_id']}'
              order by ssa.apppropermislog_id DESC
              limit {$pagestart},{$num}
              ";
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        foreach ($list as &$val) {
            $val['apppropermislog_createtime'] = date("Y-m-d", $val['apppropermislog_createtime']);
        }

        $tem_data = array();
        $count_sql = "select ssa.apppropermislog_id
              from smc_student_apppropermislog as ssa
              left join smc_code_apppropermis as ca on ca.apppropermis_code=ssa.apppropermis_code and (ca.company_id = ssa.company_id or ca.company_id=0)
              where {$datawhere} and ssa.company_id='{$request['company_id']}'
              and student_id='{$request['student_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $tem_data['allnum'] = $allnum;

        $tem_data['list'] = $list;
        return $tem_data;
    }

    //单个学员领用资产
    function studentGoods($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$request['keyword']}%' or se.order_pid like '%{$request['keyword']}%' or g.goods_pid like '%{$request['keyword']}%' or se.order_pid like '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['erpgoods_isreceive']) && $request['erpgoods_isreceive'] !== '') {
            $datawhere .= " and se.erpgoods_isreceive='{$request['erpgoods_isreceive']}'";
        }

        if (isset($request['student_id']) && $request['student_id'] !== '') {
            $datawhere .= " and se.student_id='{$request['student_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $type = $this->LgArraySwitch(array("0" => "未领用", "1" => "已领用", "-1" => "订单作废"));
        $isrestore = $this->LgArraySwitch(array("0" => "未归还", "1" => "已归还"));
        $isrefund = $this->LgArraySwitch(array("0" => "未退费", "1" => "已退费"));
        $isfree = $this->LgArraySwitch(array("0" => "收费", "1" => "免费"));

        $sql = "select se.student_id,se.erpgoods_id,g.goods_cnname,se.order_pid,se.erpgoods_isreceive,se.erpgoods_receivetime,se.beoutorder_pid,se.erpgoods_createtime,g.goods_pid,pog.ordergoods_buynums,pog.ordergoods_unitprice,pog.ordergoods_totalprice,se.erpgoods_isrestore,se.erpgoods_isrefund,se.erpgoods_isfree,s.student_cnname,s.student_enname,s.student_branch,se.integralgoods_id,si.integralgoods_number,sc.school_cnname,sc.school_branch
              from smc_student_erpgoods as se
              left join smc_payfee_order_goods as pog on pog.order_pid=se.order_pid and pog.goods_id=se.goods_id
              left join erp_goods as g on g.goods_id=se.goods_id
              left join smc_student as s on s.student_id=se.student_id
              left join smc_student_integralgoods as si on si.integralgoods_id=se.integralgoods_id
              left join smc_school as sc on sc.school_id = se.school_id
              where {$datawhere} and se.school_id='{$request['school_id']}'
              group by se.erpgoods_id
              order by se.erpgoods_id desc
              ";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $erpgoodsList = $this->DataControl->selectClear($sql);
        if (!$erpgoodsList) {
            $this->error = true;
            $this->errortip = "无领用数据";
            return false;
        }

        foreach ($erpgoodsList as &$goodsOne) {
            $goodsOne['isreceive'] = $type[$goodsOne['erpgoods_isreceive']];
            $goodsOne['isFromStudentGoods'] = 'true';
            if ($goodsOne['erpgoods_receivetime'] > 0) {
                $goodsOne['erpgoods_receivetime'] = date("Y-m-d H:i:s", $goodsOne['erpgoods_receivetime']);
            } else {
                $goodsOne['erpgoods_receivetime'] = '';
            }
            $goodsOne['isrestore'] = $isrestore[$goodsOne['erpgoods_isrestore']];
            $goodsOne['isrefund'] = $isrefund[$goodsOne['erpgoods_isrefund']];
            $goodsOne['isfree'] = $isfree[$goodsOne['erpgoods_isfree']];

            if ($goodsOne['integralgoods_id'] > 0) {
                $goodsOne['ordergoods_buynums'] = $goodsOne['integralgoods_number'];
            }

            $goodsOne['erpgoods_createtime'] = date("Y-m-d H:i:s", $goodsOne['erpgoods_createtime']);
        }

        $data = array();
        $count_sql = "select se.erpgoods_id
                          from smc_student_erpgoods as se
                          left join erp_goods as g on g.goods_id=se.goods_id
                          left join smc_student as s on s.student_id=se.student_id
                          where {$datawhere} and se.school_id='{$request['school_id']}'
                          group by se.erpgoods_id";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $erpgoodsList;

        return $data;
    }

    //单个学员杂费余额
    function studentCourseGoods($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cf.feeitem_cnname like '%{$request['keyword']}%' or cf.feeitem_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id = '{$request['course_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select si.itemtimes_id,cf.feeitem_cnname,cf.feeitem_branch,sc.course_cnname,sc.course_branch,si.itemtimes_figure,si.itemtimes_number,s.school_cnname,s.school_branch
              from smc_student_itemtimes as si
              left join smc_course as sc on sc.course_id=si.course_id
              left join smc_student_enrolled as se on se.student_id=si.student_id
              left join smc_code_feeitem as cf on cf.feeitem_id=si.feeitem_id
              left join smc_school as s on s.school_id = si.school_id
              where {$datawhere} and se.school_id='{$request['school_id']}' and si.student_id='{$request['student_id']}' and se.enrolled_status<>'-1'
              order by si.itemtimes_createtime DESC
              limit {$pagestart},{$num}
              ";
        $coursebalance = $this->DataControl->selectClear($sql);
        if (!$coursebalance) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        } else {
            foreach ($coursebalance as &$value) {
                $value['itemtimes_figure'] = sprintf("%.2f", $value['itemtimes_figure']);

            }

        }

        $data = array();
        $count_sql = "select si.itemtimes_id
              from smc_student_itemtimes as si
              left join smc_course as sc on sc.course_id=si.course_id
              left join smc_student_enrolled as se on se.student_id=si.student_id
              left join smc_code_feeitem as cf on cf.feeitem_id=si.feeitem_id
              where {$datawhere} and se.school_id='{$request['school_id']}' and si.student_id='{$request['student_id']}' and se.enrolled_status<>'-1'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $coursebalance;

        return $data;
    }

    function studentFreeTimes($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or sc.course_cnname like '%{$request['keyword']}%' or sc.course_branch like '%{$request['keyword']}%' or fc.order_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and ch.hour_day  >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ch.hour_day  <= '{$request['endtime']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select fc.coursetimes_id,c.class_id,c.class_cnname,c.class_enname,c.class_branch,sc.course_cnname,sc.course_branch,fc.hour_lessontimes,fc.is_use,fc.coursetimes_createtime,fc.order_pid,ch.hour_day,h.school_cnname,h.school_branch
                from smc_student_free_coursetimes as fc ,smc_class as c,smc_course as sc,smc_class_hour as ch,smc_school as h
                where {$datawhere} and c.class_id=fc.class_id and sc.course_id=c.course_id and ch.class_id=fc.class_id and ch.hour_lessontimes=fc.hour_lessontimes and fc.school_id = h.school_id and fc.student_id='{$request['student_id']}' and fc.school_id='{$request['school_id']}' and ch.hour_iswarming=0
                order by c.class_id desc,fc.hour_lessontimes asc
                limit {$pagestart},{$num}
              ";
        $freeTimesList = $this->DataControl->selectClear($sql);

        if (!$freeTimesList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        $use = $this->LgArraySwitch(array("0" => "否", "1" => "是", "-1" => '已取消'));

        foreach ($freeTimesList as &$val) {
            $val['coursetimes_createtime'] = date("Y-m-d H:i:s", $val['coursetimes_createtime']);
            $val['is_use_name'] = $use[$val['is_use']];
            $val['hour_lessontimes'] = $this->LgStringSwitch('第' . $val['hour_lessontimes'] . '课次');
        }
        $data = array();
        $count_sql = "select fc.coursetimes_id 
                from smc_student_free_coursetimes as fc ,smc_class as c,smc_course as sc,smc_class_hour as ch
                where {$datawhere} and c.class_id=fc.class_id and sc.course_id=c.course_id and ch.class_id=fc.class_id and ch.hour_lessontimes=fc.hour_lessontimes and fc.student_id='{$request['student_id']}' and fc.school_id='{$request['school_id']}' and ch.hour_iswarming=0";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $freeTimesList;
        return $data;
    }

    //单个学员缴费明细
    function studPayment($request)
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select * from (
                SELECT A.school_id,A.student_id,FROM_UNIXTIME(A.log_time,'%Y-%m-%d') as play_day,A.log_playname as play_name,A.log_playamount AS play_amount,log_reason as remk,B.school_cnname,B.school_branch
                FROM smc_student_coursebalance_log A left join smc_school B on A.school_id = B.school_id
                where  log_playname like'%2.0%'
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                SELECT A.school_id,A.student_id,FROM_UNIXTIME(A.balancelog_time,'%Y-%m-%d') as play_day,A.balancelog_playname as play_name,a.balancelog_playamount AS play_amount,a.balancelog_reason as remk,B.school_cnname,B.school_branch
                FROM smc_student_balancelog A left join smc_school B on A.school_id = B.school_id
                WHERE balancelog_playname like'%2.0%'
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                select A.school_id,A.student_id,FROM_UNIXTIME(A.log_time,'%Y-%m-%d') as play_day,A.log_playname as play_name,A.log_playamount AS play_amount,log_reason as remk,B.school_cnname,B.school_branch
                from smc_student_coursecatbalance_log A left join smc_school B on A.school_id = B.school_id
                where log_playclass='+' 
                AND staffer_id='12357'
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                SELECT A.school_id,A.student_id,FROM_UNIXTIME(A.balancelog_time,'%Y-%m-%d') as play_day,A.balancelog_playname as play_name,a.balancelog_playamount AS play_amount,A.trading_pid as remk,C.school_cnname,C.school_branch
                FROM smc_student_balancelog A,smc_student_trading B,smc_school C
                WHERE A.trading_pid=B.trading_pid 
                AND A.company_id=B.company_id 
                AND C.school_id=A.school_id 
                AND B.tradingtype_code='TransferIn' 
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                select c.school_id,c.student_id,FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d') as play_day,A.pay_typename as play_name,a.pay_price AS play_amount,c.trading_pid as remk,D.school_cnname,D.school_branch
                from smc_payfee_order_pay a
                left join smc_code_paytype b on a.paytype_code=b.paytype_code
                left join smc_payfee_order c on a.order_pid=c.order_pid 
                left join smc_school D on D.school_id=c.school_id 
                where a.pay_issuccess=1 
                and b.paytype_ischarge=1 
                and c.student_id='{$request['student_id']}'  
                and c.school_id='{$request['school_id']}'
                union all
                select b.school_id,b.student_id,FROM_UNIXTIME(A.log_time,'%Y-%m-%d') as play_day,A.log_playname as play_name,A.log_playamount AS play_amount,log_reason as remk,c.school_cnname,c.school_branch
                from  smc_student_itemtimes_log  a
                left join smc_student_itemtimes b on a.student_id=b.student_id and a.feeitem_id=b.feeitem_id and a.itemtimes_id=b.itemtimes_id
                left join smc_school c on b.school_id = c.school_id
                where a.log_playclass='+' 
                AND a.log_reason='导入课程杂费'
                and b.student_id='{$request['student_id']}'  
                and b.school_id='{$request['school_id']}'
                ) as ta
                where 1
                order by ta.play_day desc 
                limit {$pagestart},{$num}
              ";
        $studPaymentList = $this->DataControl->selectClear($sql);
        if (!$studPaymentList) {
            $this->error = true;
            $this->errortip = "无缴费明细记录";
            return false;
        }

        $data = array();
        $count_sql = "SELECT A.school_id,A.student_id,FROM_UNIXTIME(A.log_time,'%Y-%m-%d') as play_day,A.log_playname as play_name,A.log_playamount AS play_amount,log_reason as remk
                FROM smc_student_coursebalance_log A
                where  log_playname like'%2.0%'
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                SELECT A.school_id,A.student_id,FROM_UNIXTIME(A.balancelog_time,'%Y-%m-%d') as play_day,A.balancelog_playname as play_name,a.balancelog_playamount AS play_amount,a.balancelog_reason as remk
                FROM smc_student_balancelog A
                WHERE balancelog_playname like'%2.0%'
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                select A.school_id,A.student_id,FROM_UNIXTIME(A.log_time,'%Y-%m-%d') as play_day,A.log_playname as play_name,A.log_playamount AS play_amount,log_reason as remk
                from smc_student_coursecatbalance_log A 
                where log_playclass='+' 
                AND staffer_id='12357'
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                SELECT A.school_id,A.student_id,FROM_UNIXTIME(A.balancelog_time,'%Y-%m-%d') as play_day,A.balancelog_playname as play_name,a.balancelog_playamount AS play_amount,A.trading_pid as remk
                FROM smc_student_balancelog A,smc_student_trading B
                WHERE A.trading_pid=B.trading_pid 
                AND A.company_id=B.company_id 
                AND B.tradingtype_code='TransferIn' 
                and a.student_id='{$request['student_id']}'  
                and a.school_id='{$request['school_id']}'
                union all
                select c.school_id,c.student_id,FROM_UNIXTIME(a.pay_successtime,'%Y-%m-%d') as play_day,A.pay_typename as play_name,a.pay_price AS play_amount,c.trading_pid as remk
                from smc_payfee_order_pay a
                left join smc_code_paytype b on a.paytype_code=b.paytype_code
                left join smc_payfee_order c on a.order_pid=c.order_pid 
                where a.pay_issuccess=1 
                and b.paytype_ischarge=1 
                and c.student_id='{$request['student_id']}'  
                and c.school_id='{$request['school_id']}'
                union all
                select b.school_id,b.student_id,FROM_UNIXTIME(A.log_time,'%Y-%m-%d') as play_day,A.log_playname as play_name,A.log_playamount AS play_amount,log_reason as remk
                from  smc_student_itemtimes_log  a
                left join smc_student_itemtimes b on a.student_id=b.student_id and a.feeitem_id=b.feeitem_id and a.itemtimes_id=b.itemtimes_id
                where a.log_playclass='+'
                AND a.log_reason='导入课程杂费'
                and b.student_id='{$request['student_id']}'
                and b.school_id='{$request['school_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $studPaymentList;

        return $data;
    }

    //单个学员消费明细
    function stuSpending($request)
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select * from 
                (SELECT 9 AS income_type,'' AS course_id,'' AS course_branch,'' AS course_cnname,'' AS class_branch,'' AS class_enname,
                sum(a.balancelog_playamount) AS play_amount,count(a.balancelog_id) as play_times,
                FROM_UNIXTIME(A.balancelog_time,'%Y-%m-%d') as st_date,'' AS end_date,group_concat(distinct a.trading_pid) as remk,E.school_cnname,E.school_branch
                FROM smc_student_balancelog A,smc_student_trading B,smc_school E
                WHERE 1
                AND A.trading_pid=B.trading_pid 
                AND A.company_id=B.company_id 
                AND E.school_id=A.school_id 
                AND B.tradingtype_code='TransferOut' 
                and a.company_id='{$request['company_id']}'
                and a.school_id='{$request['school_id']}' 
                AND a.student_id='{$request['student_id']}' 
                GROUP BY a.trading_pid,st_date
                union all
                SELECT a.income_type,c.course_id,c.course_branch,c.course_cnname,b.class_branch,b.class_enname,sum(income_price) as play_amout,count(a.income_id) as play_times,
                FROM_UNIXTIME(min(a.income_confirmtime),'%Y-%m-%d') as st_date, FROM_UNIXTIME(max(a.income_confirmtime),'%Y-%m-%d') as end_date, group_concat(distinct income_note) as remk,E.school_cnname,E.school_branch
                FROM smc_school_income a 
                left join smc_class b on a.company_id=b.company_id and a.school_id=b.school_id and a.class_id=b.class_id
                left join smc_course c on b.company_id=c.company_id and b.course_id=c.course_id
                left join smc_school E on E.school_id = a.school_id
                WHERE 1 
                and a.company_id='{$request['company_id']}'
                and a.school_id='{$request['school_id']}' 
                AND a.student_id='{$request['student_id']}' 
                and a.income_price>0 
                and a.hourstudy_id>0
                group by a.income_type,a.class_id
                union all
                select a.income_type,c.course_id,c.course_branch,c.course_cnname,b.class_branch,b.class_enname,sum(income_price) as play_amout,count(a.income_id) as play_times,
                FROM_UNIXTIME(min(a.income_confirmtime),'%Y-%m-%d') as st_date, FROM_UNIXTIME(max(a.income_confirmtime),'%Y-%m-%d') as end_date, group_concat(distinct income_note) as remk,E.school_cnname,E.school_branch
                from smc_school_income a
                left join smc_class b on a.company_id=b.company_id and a.school_id=b.school_id and a.class_id=b.class_id
                left join smc_course c on a.company_id=c.company_id and a.course_id=c.course_id
                left join smc_school E on E.school_id = a.school_id
                where 1
                and a.company_id='{$request['company_id']}'
                and a.school_id='{$request['school_id']}' 
                AND a.student_id='{$request['student_id']}' 
                and a.income_price>0 
                and ifnull(a.hourstudy_id,0)=0
                group by a.income_type,a.course_id
                union all 
                select 8 AS income_type,'' AS course_id,'' AS course_branch,'' AS course_cnname,'' AS class_branch,'' AS class_enname,
                a.refund_payprice AS play_amount,1 as play_times,
                FROM_UNIXTIME(A.refund_createtime,'%Y-%m-%d') as st_date,
                FROM_UNIXTIME(A.refund_updatatime,'%Y-%m-%d') AS end_date,
                a.trading_pid as remk,E.school_cnname,E.school_branch
                from smc_refund_order A 
                left join smc_student B on B.student_id=A.student_id 
                left join smc_student_family C on C.student_id=B.student_id and C.family_isdefault=1 
                left join smc_student_trading D on D.trading_pid=A.trading_pid 
                left join smc_school E ON A.school_id=E.school_id 
                where 1
                and a.company_id='{$request['company_id']}'
                and a.school_id='{$request['school_id']}' 
                AND a.student_id='{$request['student_id']}' 
                and (IFNULL(A.from_order_pid,'')='' OR refund_status<>'4') 
                and A.refund_status>'-1' ) AS TA
                WHERE 1 
                order by ta.st_date desc 
                limit {$pagestart},{$num}
              ";
        $studPaymentList = $this->DataControl->selectClear($sql);
        if (!$studPaymentList) {
            $this->error = true;
            $this->errortip = "无消费明细记录";
            return false;
        }

        $income_type = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "8" => "学生退费", "9" => "分校转出"));

        foreach ($studPaymentList as &$var) {
            $var['income_type'] = $income_type[$var['income_type']];
        }

        $data = array();
        $count_sql = "SELECT 9 AS income_type,'' AS course_id,'' AS course_branch,'' AS course_cnname,'' AS class_branch,'' AS class_enname,
            sum(a.balancelog_playamount) AS play_amount,count(a.balancelog_id) as play_times,
            FROM_UNIXTIME(A.balancelog_time,'%Y-%m-%d') as st_date,'' AS end_date,group_concat(distinct a.trading_pid) as remk
            FROM smc_student_balancelog A,smc_student_trading B
            WHERE 1
            AND A.trading_pid=B.trading_pid 
            AND A.company_id=B.company_id 
            AND B.tradingtype_code='TransferOut' 
            and a.company_id='{$request['company_id']}'
            and a.school_id='{$request['school_id']}' 
            AND a.student_id='{$request['student_id']}' 
            GROUP BY a.trading_pid,st_date
            union all
            SELECT a.income_type,c.course_id,c.course_branch,c.course_cnname,b.class_branch,b.class_enname,sum(income_price) as play_amout,count(a.income_id) as play_times,
            FROM_UNIXTIME(min(a.income_confirmtime),'%Y-%m-%d') as st_date, FROM_UNIXTIME(max(a.income_confirmtime),'%Y-%m-%d') as end_date, group_concat(distinct income_note) as remk
            FROM smc_school_income a 
            left join smc_class b on a.company_id=b.company_id and a.school_id=b.school_id and a.class_id=b.class_id
            left join smc_course c on b.company_id=c.company_id and b.course_id=c.course_id
            WHERE 1 
            and a.company_id='{$request['company_id']}'
            and a.school_id='{$request['school_id']}' 
            AND a.student_id='{$request['student_id']}' 
            and a.income_price>0 
            and a.hourstudy_id>0
            group by a.income_type,a.class_id
            union all
            select a.income_type,c.course_id,c.course_branch,c.course_cnname,b.class_branch,b.class_enname,sum(income_price) as play_amout,count(a.income_id) as play_times,
            FROM_UNIXTIME(min(a.income_confirmtime),'%Y-%m-%d') as st_date, FROM_UNIXTIME(max(a.income_confirmtime),'%Y-%m-%d') as end_date, group_concat(distinct income_note) as remk
            from smc_school_income a
            left join smc_class b on a.company_id=b.company_id and a.school_id=b.school_id and a.class_id=b.class_id
            left join smc_course c on a.company_id=c.company_id and a.course_id=c.course_id
            where 1
            and a.company_id='{$request['company_id']}'
            and a.school_id='{$request['school_id']}' 
            AND a.student_id='{$request['student_id']}' 
            and a.income_price>0 
            and ifnull(a.hourstudy_id,0)=0
            group by a.income_type,a.course_id
            union all 
            select 8 AS income_type,'' AS course_id,'' AS course_branch,'' AS course_cnname,'' AS class_branch,'' AS class_enname,
            a.refund_payprice AS play_amount,1 as play_times,
            FROM_UNIXTIME(A.refund_createtime,'%Y-%m-%d') as st_date,
            FROM_UNIXTIME(A.refund_updatatime,'%Y-%m-%d') AS end_date,
            a.trading_pid as remk
            from smc_refund_order A 
            left join smc_student B on B.student_id=A.student_id 
            left join smc_student_family C on C.student_id=B.student_id and C.family_isdefault=1 
            left join smc_student_trading D on D.trading_pid=A.trading_pid 
            left join smc_school E ON A.school_id=E.school_id 
            where 1
            and a.company_id='{$request['company_id']}'
            and a.school_id='{$request['school_id']}' 
            AND a.student_id='{$request['student_id']}' 
            and (IFNULL(A.from_order_pid,'')='' OR refund_status<>'4') 
            and A.refund_status>'-1'  ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $studPaymentList;

        return $data;
    }

    //单个学员积分明细
    function stuExchangeLog($request)
    {
        $datawhere = " 1 ";

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and si.integrallog_time >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime($request['endtime'] . " 23:59:59");
            $datawhere .= " and si.integrallog_time <= '{$request['endtime']}'";
        }
        if (isset($request['integraltype_class']) && $request['integraltype_class'] !== '') {
            $datawhere .= " and ci.integraltype_class = '{$request['integraltype_class']}'";
        }
        if (isset($request['integraltype_id']) && $request['integraltype_id'] !== '') {
            $datawhere .= " and ci.integraltype_id = '{$request['integraltype_id']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select ci.integraltype_name,ci.integraltype_class,si.integrallog_id,si.integrallog_playname,si.integrallog_fromamount,si.integrallog_playamount,si.integrallog_finalamount,si.integrallog_reason,si.integrallog_time,si.staffer_id,st.staffer_cnname,si.parenter_id,p.parenter_cnname,si.integrallog_playclass,s.school_cnname,s.school_branch,si.integrallog_rule
                from smc_student_integrallog as si
                left join smc_code_integraltype as ci on ci.integraltype_id=si.integraltype_id
                left join smc_staffer as st on st.staffer_id=si.staffer_id
                left join smc_parenter as p on p.parenter_id=si.parenter_id
                left join smc_school as s on s.school_id = si.school_id
                where {$datawhere} and si.student_id='{$request['student_id']}' and si.school_id = '{$request['school_id']}'
                order by si.integrallog_time desc
                limit {$pagestart},{$num}
              ";
        $logList = $this->DataControl->selectClear($sql);
        if (!$logList) {
            $this->error = true;
            $this->errortip = "暂无积分明细哦~点击右上角即可给学员赠送积分";
            return false;
        }
        $class = $this->LgArraySwitch(array('0' => '积分扣除', '1' => '积分增加'));
        foreach ($logList as &$logOne) {
            $logOne['integrallog_time'] = date("Y-m-d H:i:s", $logOne['integrallog_time']);
            $logOne['integraltype_class_name'] = $class[$logOne['integraltype_class']];
            $logOne['staffer_cnname'] = $logOne['staffer_id'] == 0 ? $logOne['parenter_cnname'] : $logOne['staffer_cnname'];

            $logOne['integrallog_playamount'] = $logOne['integrallog_playclass'] . $logOne['integrallog_playamount'];
        }

        $data = array();
        $count_sql = "select si.integrallog_id
                from smc_student_integrallog as si
                left join smc_code_integraltype as ci on ci.integraltype_id=si.integraltype_id
                where {$datawhere} and si.student_id='{$request['student_id']}' and si.school_id = '{$request['school_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $logList;
        return $data;
    }

    function getIntegraltype($request)
    {
        $datawhere = " 1 ";
        if (isset($request['integraltype_class']) && $request['integraltype_class'] != '') {
            $datawhere .= " and ci.integraltype_class='{$request['integraltype_class']}'";
        }
        $sql = "select ci.integraltype_id,ci.integraltype_name,ci.integraltype_class
              from smc_code_integraltype as ci
              where {$datawhere} and (ci.company_id='{$request['company_id']}' or ci.company_id=0)";
        $typeList = $this->DataControl->selectClear($sql);
        if (!$typeList) {
            $this->error = true;
            $this->errortip = "无对应积分类型";
            return false;
        }

        return $typeList;
    }

    //单个学员的沟通列表
    function studentTrack($request)
    {
        $datawhere = " 1 ";

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and st.track_createtime >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime($request['endtime'] . " 23:59:59");
            $datawhere .= " and st.track_createtime <= '{$request['endtime']}'";
        }

        if (isset($request['track_classname']) && $request['track_classname'] !== '') {
            $datawhere .= " and st.track_classname = '{$request['track_classname']}'";
        }
        if (isset($request['track_linktype']) && $request['track_linktype'] !== '') {
            $datawhere .= " and st.track_linktype = '{$request['track_linktype']}'";
        }

        if (isset($request['result_id']) && $request['result_id'] !== '') {
            $datawhere .= " and st.result_id = '{$request['result_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select c.commode_id,st.track_id,st.track_createtime,st.track_classname,st.track_note,c.commode_name,s.staffer_cnname,r.trackresult_name as result_name,st.track_day,r.trackresult_id as result_id,sc.school_cnname,sc.school_branch
              from smc_student_track as st
              left join smc_staffer as s on s.staffer_id=st.staffer_id
              left join crm_code_commode as c on c.commode_id=st.track_linktype
              left join smc_code_trackresult as r on r.trackresult_id=st.result_id
              left join smc_school as sc on sc.school_id = st.school_id
              where {$datawhere} and st.student_id='{$request['student_id']}'
              order by st.track_createtime DESC
              limit {$pagestart},{$num}
              ";
        $track = $this->DataControl->selectClear($sql);
        if (!$track) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        foreach ($track as &$val) {
            if (!$val['track_day']) {
                $val['track_day'] = date("Y-m-d", $val['track_createtime']);
            }
            $val['track_createtime'] = date("Y-m-d H:i:s", $val['track_createtime']);

        }

        $data = array();
        $count_sql = "select st.track_id
              from smc_student_track as st
              left join smc_staffer as s on s.staffer_id=st.staffer_id
              where {$datawhere} and st.student_id='{$request['student_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $track;
        return $data;

    }


    //带班记录列表
    function ClassList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['class_type']) && $paramArray['class_type'] !== "") {
            if ($paramArray['class_type'] == 2) {
                $datawhere .= " and ct.coursetype_isopenclass = '1'";
            } else {
                $datawhere .= " and c.class_type = '{$paramArray['class_type']}'";
            }
        }
        if (isset($paramArray['class_status']) && $paramArray['class_status'] !== "") {
            $datawhere .= " and c.class_status = '{$paramArray['class_status']}'";
        }
        if (isset($paramArray['teaching_type']) && $paramArray['teaching_type'] !== "") {
            $datawhere .= " and t.teaching_type = '{$paramArray['teaching_type']}'";
        }

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "
            SELECT
                c.class_id,
                c.class_cnname,
                c.class_enname,
                c.class_branch,
                co.course_cnname,
                co.course_branch,
                s.staffer_cnname,
                (select count(ss.study_id) from smc_student_study as ss WHERE ss.class_id = t.class_id) as classNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id) as planNum,
                (select count(h.hour_id) from smc_class_hour as h where h.class_id = t.class_id and h.hour_ischecking = '1' and h.hour_iswarming = '0') as alreadyNum,
                c.class_stdate,
                c.class_enddate,
                c.class_status,sc.school_id,sc.school_cnname,sc.school_branch
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_code_coursetype as ct On ct.coursetype_id=co.coursetype_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
                left join smc_school as sc on sc.school_id=c.school_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['worker_id']}' and class_status > '-2' and c.company_id = '{$this->company_id}'
                group by c.class_id
            Limit {$pagestart},{$num}";
        $ClassList = $this->DataControl->selectClear($sql);

        if ($ClassList) {
            $status = $this->LgArraySwitch(array('0' => '待开班', '1' => '进行中', '-1' => '已结束'));
            foreach ($ClassList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
                $val['class_timespl'] = $val['class_stdate'] . "~" . $val['class_enddate'];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
                c.class_id
            FROM
                smc_class_hour_teaching AS t 
                left join smc_class as c on t.class_id = c.class_id
                left join smc_course as co on c.course_id = co.course_id
                left join smc_code_coursetype as ct On ct.coursetype_id=co.coursetype_id
                left join smc_staffer as s on t.staffer_id = s.staffer_id
            WHERE
                {$datawhere} and t.staffer_id = '{$paramArray['worker_id']}' and class_status > '-2' and c.company_id = '{$this->company_id}'
                group by c.class_id");
        $allnums = $all_num ? count($all_num) : 0;

        $fieldstring = array('school_cnname', 'school_branch', 'class_cnname', 'class_enname', 'class_branch', 'class_timespl', 'course_cnname', 'course_branch', 'class_status', 'planNum', 'alreadyNum', 'alreadyNum');
        $fieldname = array('校区名称', '校区编号', '班级名称', '班级别名', '班级编号', '开班时间', '课程别名称', '课程别编号', '班级状态', '全部课次', '班级上课次数', '学员考勤次数');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ClassList) {
            $result['list'] = $ClassList;
        } else {
            $result['list'] = array();
        }


        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取带班记录列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何带班信息', 'result' => $result);
        }

        return $res;
    }

    //教师课表列表
    function TeaTableList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%' or co.course_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] != '') {
            $datawhere .= " and ch.hour_way='{$paramArray['hour_way']}'";
        }
        if (isset($paramArray['hour_ischecking']) && $paramArray['hour_ischecking'] != '') {
            $datawhere .= " and ch.hour_ischecking='{$paramArray['hour_ischecking']}'";
        }

        if (isset($paramArray['class_id']) && $paramArray['class_id'] != '') {
            $datawhere .= " and c.class_id='{$paramArray['class_id']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] != '') {
            $datawhere .= " and ch.hour_day>='{$paramArray['start_time']}'";
        }

        if (isset($paramArray['teaching_type']) && $paramArray['teaching_type'] != '') {
            $datawhere .= " and ht.teaching_type='{$paramArray['teaching_type']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] != '') {
            $datawhere .= " and ch.hour_day<='{$paramArray['end_time']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ch.hour_id,ch.hour_day,c.class_id,c.class_branch,c.class_cnname,c.class_enname,sch.school_id,sch.school_cnname,sch.school_branch,cl.classroom_cnname,c.class_fullnums,ch.hour_way,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,ch.hour_name,ch.hour_number,co.course_cnname,co.course_branch,ht.teaching_type
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hourstudy_id) from smc_student_hourstudy as ho where ho.hour_id=ch.hour_id and hourstudy_checkin = '1' ) as tureNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum,
              (SELECT count(cs.sturemark_id) FROM eas_classhour_sturemark AS cs WHERE cs.hour_id = ch.hour_id ) as alreadyNum
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co on co.course_id = c.course_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$this->company_id}'
              order by sch.school_id,ch.hour_day ASC,ch.hour_starttime asc ";
        $sql .= " limit {$pagestart},{$num}";

        $ClassList = $this->DataControl->selectClear($sql);

        if ($ClassList) {
            $status = $this->LgArraySwitch(array('0' => '未上课', '1' => '已上课', '-1' => '已取消'));
            $statuss = $this->LgArraySwitch(array('0' => '主教', '1' => '助教'));
            $way = $this->LgArraySwitch(array('0' => '实体课', '1' => '线上课'));
            $week = $this->LgArraySwitch(array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'));
            foreach ($ClassList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
                $val['teaching_type'] = $statuss[$val['teaching_type']];
                $val['date'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                $val['hour_ischecking_name'] = $status[$val['hour_ischecking']];
                $val['hour_way_name'] = $way[$val['hour_way']];
                $val['week'] = $week[date('w', strtotime($val['hour_day']))];
                $val['hour_evaluate'] = 1 / 20;
                $val['score'] = $val['commentNum'] . '/' . $val['stuNum'];
                if ($val['hour_way'] == '0') {
                    $val['room'] = $val['classroom_cnname'];
                } else {
                    $val['room'] = $val['hour_number'];
                }
            }

        }

        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co on co.course_id = c.course_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$this->company_id}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnums = count($db_nums);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('school_cnname', 'school_branch', 'date', 'hour_way_name', 'teaching_type', 'class_cnname', 'class_branch', 'course_cnname', 'course_branch', 'room', 'hour_ischecking_name');
        $fieldname = array('校区名称', '校区编号', '上课时间', '上课方式', '教学类型', '班级名称', '班级编号', '课程别名称', '课程别编号', '教室', '上课状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldisShowWay = array("", "", "", "", "", "", "", "", "", "1", "");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isShowWay"] = trim($fieldisShowWay[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ClassList) {
            $result['list'] = $ClassList;
        } else {
            $result['list'] = array();
        }


        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取教师课表列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何教师课表', 'result' => $result);
        }

        return $res;
    }

    //教师上课记录列表
    function ClassTeaching($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$paramArray['keyword']}%' or c.class_enname like '%{$paramArray['keyword']}%' or c.class_branch like '%{$paramArray['keyword']}%' or co.course_cnname like '%{$paramArray['keyword']}%' or co.course_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($paramArray['hour_way']) && $paramArray['hour_way'] != '') {
            $datawhere .= " and ch.hour_way='{$paramArray['hour_way']}'";
        }

        if (isset($paramArray['class_id']) && $paramArray['class_id'] != '') {
            $datawhere .= " and c.class_id='{$paramArray['class_id']}'";
        }


        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['start_time']) && $paramArray['start_time'] != '') {
            $datawhere .= " and ch.hour_day>='{$paramArray['start_time']}'";
        }

        if (isset($paramArray['end_time']) && $paramArray['end_time'] != '') {
            $datawhere .= " and ch.hour_day<='{$paramArray['end_time']}'";
        }


        $sql = "select ch.hour_id,ch.hour_day,c.class_id,c.class_branch,c.class_cnname,c.class_enname,sch.school_cnname,sch.school_branch,cl.classroom_cnname,c.class_fullnums,ch.hour_way,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_lessontimes,st.staffer_cnname,ch.hour_name,ch.hour_number,co.course_cnname,co.course_branch
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=c.class_id and ss.study_isreading='1') as stuNum
              ,(select count(cs.hourcomment_id) from eas_student_hourcomment as cs where cs.hour_id=ht.hour_id) as commentNum
              ,(select count(ho.hourstudy_id) from smc_student_hourstudy as ho where ho.hour_id=ch.hour_id and hourstudy_checkin = '1' ) as tureNum
              ,(select count(ho.hour_id) from smc_class_hour as ho where ho.class_id=c.class_id and ho.hour_ischecking<>'-1') as hourNum,
              (SELECT count(cs.sturemark_id) FROM eas_classhour_sturemark AS cs WHERE cs.hour_id = ch.hour_id ) as alreadyNum33
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co on co.course_id = c.course_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$this->company_id}' and ch.hour_ischecking = '1'
              order by sch.school_id,ch.hour_day ASC,ch.hour_starttime asc ";
        $sql .= " limit {$pagestart},{$num}";

        $ClassList = $this->DataControl->selectClear($sql);

        if ($ClassList) {
            $status = $this->LgArraySwitch(array('0' => '未上课', '1' => '已上课', '-1' => '已取消'));
            $way = $this->LgArraySwitch(array('0' => '实体课', '1' => '线上课'));
            $week = $this->LgArraySwitch(array('0' => '周日', '1' => '周一', '2' => '周二', '3' => '周三', '4' => '周四', '5' => '周五', '6' => '周六', '7' => '周日'));
            foreach ($ClassList as &$val) {
                $val['class_status'] = $status[$val['class_status']];
                $val['date'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                $val['hour_ischecking_name'] = $status[$val['hour_ischecking']];
                $val['hour_way_name'] = $way[$val['hour_way']];
                $val['week'] = $week[date('w', strtotime($val['hour_day']))];
                $val['hour_evaluate'] = 1 / 20;
                $val['score'] = $val['commentNum'] . '/' . $val['stuNum'];
                if ($val['hour_way'] == '0') {
                    $val['room'] = $val['classroom_cnname'];
                } else {
                    $val['room'] = $val['hour_number'];
                }
                $val['arrive'] = $val['tureNum'] . '/' . $val['stuNum'];
                $val['percent'] = round($val['tureNum'] / $val['stuNum'] * 100, 2) . '%';
                $val['count'] = '1';
            }

        }

        $count_sql = "select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_school as sch on sch.school_id=c.school_id
              left join smc_course as co on co.course_id = c.course_id
              where {$datawhere} and ht.staffer_id='{$paramArray['worker_id']}' and ht.teaching_isdel='0' and c.company_id = '{$this->company_id}' and ch.hour_ischecking = '1'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);

        if ($db_nums) {
            $allnums = count($db_nums);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('school_cnname', 'school_branch', 'date', 'hour_way_name', 'class_cnname', 'class_branch', 'room', 'arrive', 'percent', 'count');
        $fieldname = array('校区名称', '校区编号', '上课时间', '上课方式', '班级名称', '班级编号', '上课教室', '实到/应到', '出勤率', '抵消次数');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldisShowWay = array("", "", "", "", "", "", "1", "", "", "");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isShowWay"] = trim($fieldisShowWay[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ClassList) {
            $result['list'] = $ClassList;
        } else {
            $result['list'] = array();
        }


        if ($ClassList) {
            $result = array();
            $result["field"] = $field;
            $result["list"] = $ClassList;
            $result["all_num"] = $allnums;
            $res = array('error' => '0', 'errortip' => '获取教师上课记录列表成功', 'result' => $result);
        } else {
            $result = array();
            $result["field"] = $field;
            $result["list"] = array();
            $result["all_num"] = 0;
            $res = array('error' => '1', 'errortip' => '暂无任何教师上课记录', 'result' => $result);
        }

        return $res;
    }

    //单个学生信息
    function studentOne($request)
    {
        $sql = "SELECT s.student_id,s.student_branch,s.student_cnname,s.student_enname,s.student_sex,s.student_birthday,s.student_img,se.enrolled_status
              ,sf.family_mobile,sf.family_cnname,b.student_balance,s.student_forwardprice,b.student_withholdbalance,ifnull(vp.property_integralbalance,0) as property_integralbalance
              ,(select SUM(po.order_paymentprice) from smc_payfee_order as po where po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and (po.order_status between 1 and 3)) as order_paymentprice
              ,(select SUM(po.order_paidprice) from smc_payfee_order as po where po.student_id=s.student_id and po.company_id='{$request['company_id']}' and po.school_id='{$request['school_id']}' and (po.order_status between 1 and 3)) as order_paidprice
                FROM smc_student as s
                LEFT JOIN smc_student_balance as b ON b.student_id = s.student_id and b.school_id='{$request['school_id']}' and b.company_id='{$request['company_id']}'
                left join smc_student_enrolled as se on se.student_id=s.student_id
                left join smc_student_family as sf on sf.student_id=s.student_id and sf.family_isdefault=1
                left join smc_student_virtual_property as vp on vp.student_id=s.student_id
                WHERE s.student_id='{$request['student_id']}' and se.school_id='{$request['school_id']}' limit 0,1";
        $studentOne = $this->DataControl->selectOne($sql);

        if ($studentOne) {
            if (strpos($studentOne['student_img'], 'x-oss-process') == false) {
                $studentOne['student_img'] = $studentOne['student_img'] ? $studentOne['student_img'] . '?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90' : '';
            }
            if ($studentOne['student_birthday'] && $studentOne['student_birthday'] != '') {
                $studentOne['age'] = date("Y", time()) - date("Y", strtotime($studentOne['student_birthday']));
                if (strlen($studentOne['student_birthday']) <= 8) {
                    $studentOne['student_birthday'] = date("Y-m-d", strtotime($studentOne['student_birthday']));
                }
            } else {
                $studentOne['age'] = 0;
                $studentOne['student_birthday'] = '';
            }
            $studentOne['arrears'] = sprintf("%01.2f", ($studentOne['order_paymentprice'] - $studentOne['order_paidprice']));
//            ($studentOne['order_paymentprice']-$studentOne['order_paidprice'],2);
            $studentOne['show_balance'] = $studentOne['student_withholdbalance'] + $studentOne['student_balance'];

        }
        return $studentOne;
    }

    function orderInfo($request)
    {
        $datawhere = " 1 ";
        if (isset($request['student_id']) && $request['student_id'] != '') {
            $datawhere .= "and student_id ='{$request['student_id']}'";
        }
        $orderAll = $this->DataControl->selectOne("select count(order_id) as num,sum(order_paymentprice) as paymentprice,sum(order_paidprice) as order_paidprice 
            from smc_payfee_order where {$datawhere} and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and order_status > 0");

        $tradeAll = $this->DataControl->selectOne("select count(trading_id) as num from smc_student_trading
            where {$datawhere} and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and tradingtype_code<>'Subscribed' and tradingtype_code<>'MonthlyShare'");

        $stuAll = $this->DataControl->selectOne("select sum(income_price) as balancelog_playamount 
            from smc_school_income where {$datawhere} and company_id='{$request['company_id']}' and school_id='{$request['school_id']}'");

        $order = $this->DataControl->selectOne("select count(order_id) as num,sum(order_paymentprice) as paymentprice,sum(order_paidprice) as order_paidprice from smc_payfee_order where {$datawhere} and company_id='{$request['company_id']}' and school_id='{$request['school_id']}' and order_status<>4 and order_status >0");


        $data = array();
        $data['num'] = $tradeAll['num'] ? $tradeAll['num'] : 0;
        $data['paymentprice'] = $orderAll['paymentprice'] ? $orderAll['paymentprice'] : 0;
        $data['paidprice'] = $orderAll['order_paidprice'] ? $orderAll['order_paidprice'] : 0;
        $data['playamount'] = $stuAll['balancelog_playamount'] ? $stuAll['balancelog_playamount'] : 0;
        $data['arrears'] = $order['paymentprice'] - $order['order_paidprice'] ? $order['paymentprice'] - $order['order_paidprice'] : 0;
        return $data;
    }


    function stuExchangeList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$request['keyword']}%' or g.goods_enname like '%{$request['keyword']}%' or g.goods_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and si.integralgoods_createtime >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime($request['endtime'] . " 23:59:59");
            $datawhere .= " and si.integralgoods_createtime <= '{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select si.integralgoods_id,g.goods_cnname,si.integralgoods_score,si.integralgoods_number,si.integralgoods_createtime,si.integralgoods_receivetime,si.integralgoods_isreceive,ifnull(p.parenter_cnname,'--') as parenter_cnname,s.school_cnname,s.school_branch
                from smc_student_integralgoods as si
                left join erp_goods as g on g.goods_id=si.goods_id
                left join smc_parenter as p on p.parenter_id=si.parenter_id
                left join smc_school as s on si.school_id = s.school_id
                where {$datawhere} and si.student_id='{$request['student_id']}' and si.school_id='{$request['school_id']}'
                order by si.integralgoods_createtime DESC
                limit {$pagestart},{$num}
              ";
        $goodsList = $this->DataControl->selectClear($sql);
        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "暂无积分兑换记录哦~";
            return false;
        }

        $status = $this->LgArraySwitch(array('0' => '未领用', '1' => '已领用', '-1' => '已取消'));

        foreach ($goodsList as &$goodsOne) {
            if ($goodsOne['integralgoods_receivetime']) {
                $goodsOne['integralgoods_receivetime'] = date("Y-m-d H:i:s", $goodsOne['integralgoods_receivetime']);
            } else {
                $goodsOne['integralgoods_receivetime'] = '--';
            }

            $goodsOne['integralgoods_createtime'] = date("Y-m-d H:i:s", $goodsOne['integralgoods_createtime']);


            $goodsOne['integralgoods_isreceive_name'] = $status[$goodsOne['integralgoods_isreceive']];
        }

        $data = array();
        $count_sql = "select si.integralgoods_id
                from smc_student_integralgoods as si
                left join erp_goods as g on g.goods_id=si.goods_id
                where {$datawhere} and si.student_id='{$request['student_id']}' and si.school_id='{$request['school_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $goodsList;
        return $data;
    }


    //查看请假记录
    function getApprovedDetail($request)
    {
        $datawhere = " ah.absence_id = '{$request['absence_id']}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or co.course_cnname like '%{$request['keyword']}%' or co.course_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and c.class_id = '{$request['class_id']}'";
        }
        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and co.course_id = '{$request['course_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    c.class_cnname,c.class_enname,c.class_branch,co.course_cnname,co.course_branch,h.hour_way,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_isfree,ah.absence_hour_status,ah.hour_day as hourday,ah.hour_starttime as hourstarttime,ah.hour_endtime as hourendtime,
                    (SELECT GROUP_CONCAT(DISTINCT CONCAT(staffer_cnname,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE CONCAT( '-', sf.staffer_enname ) END ) ) ) FROM smc_class_hour_teaching as ht LEFT JOIN smc_staffer as sf ON sf.staffer_id = ht.staffer_id WHERE ht.class_id = h.class_id AND ht.hour_id = ah.hour_id AND ht.teaching_type='0') as staffer_cnname
                FROM
                    smc_student_absence_hour as ah
                LEFT JOIN
                    smc_class_hour as h ON h.hour_id = ah.hour_id
                LEFT JOIN
                    smc_class as c ON c.class_id = h.class_id
                LEFT JOIN
                    smc_course as co ON co.course_id = h.course_id
                WHERE
                    {$datawhere}
                LIMIT {$pagestart},{$num}";
        $hourList = $this->DataControl->selectClear($sql);
        if ($hourList) {
            $type = $this->LgArraySwitch(array("0" => "是", "1" => "否"));
            $status = $this->LgArraySwitch(array("0" => "实体课", "1" => "线上课"));
            $class = $this->LgArraySwitch(array("0" => "申请", "1" => "有效", "-1" => "无效"));
            foreach ($hourList as &$val) {
                $val['hour_isfree_name'] = $type[$val['hour_isfree']];
                $val['hour_way_name'] = $status[$val['hour_way']];
                $val['hour_status_name'] = $class[$val['absence_hour_status']];
//                $val['hour_time'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                $val['hour_time'] = $val['hourday'] . ' ' . $val['hourstarttime'] . '-' . $val['hourendtime'];
            }
        } else {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $data = array();
        $count_sql = "SELECT
                          ah.hour_id
                      FROM
                          smc_student_absence_hour as ah
                      LEFT JOIN
                          smc_class_hour as h ON h.hour_id = ah.hour_id
                      LEFT JOIN
                          smc_class as c ON c.class_id = h.class_id
                      LEFT JOIN
                          smc_course as co ON co.course_id = h.course_id
                      WHERE {$datawhere}";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['absence_hournum'] = $this->DataControl->selectOne("SELECT
                                                                          COUNT(h.hour_id) as hour_num,a.absence_starttime,a.absence_endtime,
                                                                          (SELECT COUNT(ah.hour_id) FROM smc_student_absence_hour as ah WHERE ah.absence_id = '{$request['absence_id']}' AND ah.absence_hour_status = '1') as hour_valid_num,
                                                                          (SELECT COUNT(ah.hour_id) FROM smc_student_absence_hour as ah WHERE ah.absence_id = '{$request['absence_id']}' AND ah.absence_hour_status <> '1') as hour_invalid_num
                                                                       FROM
                                                                          smc_student_absence_hour as h
                                                                       LEFT JOIN
                                                                          smc_student_absence as a ON a.absence_id = h.absence_id
                                                                       WHERE
                                                                          h.absence_id = '{$request['absence_id']}'");

        $data['list'] = $hourList;
        return $data;
    }

    //交易记录
    function studentCourseTransaction($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%'  )";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and ssc.log_time>='{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime($request['endtime']);
            $datawhere .= " and ssc.log_time<='{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select ssc.log_playname,ssc.log_playclass,ssc.log_time,ssc.log_playamount,ssc.log_reason
                from smc_student_coursebalance_log as ssc
                left join smc_student_enrolled as se on se.student_id=ssc.student_id
                left join smc_course as sc on sc.course_id=ssc.course_id
                WHERE {$datawhere} and se.school_id='{$request['school_id']}' and sc.company_id='{$request['company_id']}' and ssc.course_id='{$request['course_id']}' and ssc.student_id='{$request['student_id']}'
                order by ssc.log_time DESC
                limit {$pagestart},{$num}";
        $consumeList = $this->DataControl->selectClear($sql);
        if (!$consumeList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        $tem_consumeList = array();
        foreach ($consumeList as $key => $val) {
            $tem_consumeList[$key]['log_playname'] = $val['log_playname'];
            $tem_consumeList[$key]['log_playclass'] = $val['log_playclass'];
            $tem_consumeList[$key]['log_day'] = date("Y-m-d", $val['log_time']);
            $tem_consumeList[$key]['log_playamount'] = $val['log_playamount'];
            $tem_consumeList[$key]['log_reason'] = $val['log_reason'];
            $tem_consumeList[$key]['log_frequency'] = 1;
            $tem_consumeList[$key]['log_time'] = date("Y-m-d H:i:s", $val['log_time']);
        }
        $data = array();
        $count_sql = "select ssc.log_id
                from smc_student_coursebalance_log as ssc
                left join smc_student_enrolled as se on se.student_id=ssc.student_id
                left join smc_course as sc on sc.course_id=ssc.course_id
                WHERE {$datawhere} and se.school_id='{$request['school_id']}' and sc.company_id='{$request['company_id']}' and ssc.course_id='{$request['course_id']}' and ssc.student_id='{$request['student_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $tem_consumeList;

        return $data;
    }

    function studentCourseConsume($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%'  )";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and ssc.log_time>='{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime($request['endtime']);
            $datawhere .= " and ssc.log_time<='{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select ssc.log_time,ssc.log_playamount,ssc.log_reason,ssc.log_random
                ,A.hourstudy_id,B.hour_name
                from smc_student_coursebalance_log as ssc
                left join smc_student_enrolled as se on se.student_id=ssc.student_id
                left join smc_course as sc on sc.course_id=ssc.course_id 
                left join smc_student_hourstudy A ON ssc.hourstudy_id=A.hourstudy_id 
                left join smc_class_hour B ON A.hour_id=B.hour_id 
                WHERE {$datawhere} and se.school_id='{$request['school_id']}' and sc.company_id='{$request['company_id']}' and ssc.log_class=0 and ssc.log_playclass='-'  and ssc.course_id='{$request['course_id']}' and ssc.student_id='{$request['student_id']}'
                order by ssc.log_time DESC
                limit {$pagestart},{$num}";
        $consumeList = $this->DataControl->selectClear($sql);
        if (!$consumeList) {
            $this->error = true;
            $this->errortip = "无学员数据";
            return false;
        }
        $tem_consumeList = array();
        foreach ($consumeList as $key => $val) {
            if ($val['hourstudy_id'] == '0') {
                if ($val['log_random'] != '') {
                    $logOne = $this->DataControl->getFieldOne("smc_student_coursebalance_timelog", "timelog_playtimes", "student_id='{$request['student_id']}' and log_random='{$val['log_random']}'");
                    $tem_consumeList[$key]['log_frequency'] = $logOne['timelog_playtimes'];
                } else {
                    $tem_consumeList[$key]['log_frequency'] = '1';
                }
            } else {
                $tem_consumeList[$key]['log_frequency'] = $val['hour_name'];
            }
            $tem_consumeList[$key]['log_day'] = date("Y-m-d", $val['log_time']);
            $tem_consumeList[$key]['log_playamount'] = $val['log_playamount'];
            $tem_consumeList[$key]['log_reason'] = $val['log_reason'];

            $tem_consumeList[$key]['log_time'] = date("Y-m-d H:i:s", $val['log_time']);
        }
        $data = array();
        $count_sql = "select ssc.log_id
                from smc_student_coursebalance_log as ssc
                left join smc_student_enrolled as se on se.student_id=ssc.student_id
                left join smc_course as sc on sc.course_id=ssc.course_id
                WHERE {$datawhere} and se.school_id='{$request['school_id']}' and sc.company_id='{$request['company_id']}' and ssc.log_class=0 and ssc.log_playclass='-'  and ssc.course_id='{$request['course_id']}' and ssc.student_id='{$request['student_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $tem_consumeList;

        return $data;

    }

    function apppropermisTwo($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and ca.apppropermis_name like '%{$request['keyword']}%'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ca.apppropermis_name,ssa.apppropermis_code,ssa.apppropermislog_createtime,st.staffer_cnname,ssa.apppropermislog_endday,ssa.apppropermislog_isenabled
              from smc_student_apppropermislog as ssa
              left join smc_code_apppropermis as ca on ca.apppropermis_code=ssa.apppropermis_code and (ca.company_id = ssa.company_id or ca.company_id=0)
              left join smc_staffer as st on st.staffer_id=ssa.staffer_id
              where {$datawhere} and ssa.company_id='{$request['company_id']}'
              and student_id='{$request['student_id']}'
              group by ca.apppropermis_id
              order by ssa.apppropermislog_id DESC
              limit {$pagestart},{$num}
              ";
        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $status = $this->LgArraySwitch(array('0' => '不禁用', '1' => '禁用'));

        foreach ($list as &$val) {
            $val['apppropermislog_createtime'] = date("Y-m-d", $val['apppropermislog_createtime']);
            $val['apppropermislog_isenabled'] = $status[$val['apppropermislog_isenabled']];
        }

        $tem_data = array();
        $count_sql = "select ssa.apppropermislog_id
              from smc_student_apppropermislog as ssa
              left join smc_code_apppropermis as ca on ca.apppropermis_code=ssa.apppropermis_code and (ca.company_id = ssa.company_id or ca.company_id=0)
              where {$datawhere} and ssa.company_id='{$request['company_id']}'
              and student_id='{$request['student_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $tem_data['allnum'] = $allnum;
        $tem_data['list'] = $list;
        return $tem_data;
    }

    function stuOtherSchoolBalance($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select s.school_id,s.school_branch,s.school_cnname,s.school_shortname,sb.student_balance,sb.student_withholdbalance
                from smc_student_balance as sb
                inner join smc_school as s on s.school_id=sb.school_id
                where {$datawhere} and sb.student_id='{$request['student_id']}' and sb.company_id='{$request['company_id']}' and sb.school_id = '{$request['school_id']}'
                order by sb.school_id asc
                limit {$pagestart},{$num}
              ";
        $balanceList = $this->DataControl->selectClear($sql);
        if (!$balanceList) {
            $this->error = true;
            $this->errortip = "无其他学校账户余额";
            return false;
        }

        $data = array();
        $count_sql = "select s.school_id
                from smc_student_balance as sb
                inner join smc_school as s on s.school_id=sb.school_id
                where {$datawhere} and sb.student_id='{$request['student_id']}' and sb.company_id='{$request['company_id']}' and sb.school_id = '{$request['school_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $balanceList;

        return $data;

    }

    //获取优惠券附件
    function getCouponsFile($paramArray)
    {
        if ($paramArray['applytype_isshop'] == '1') {
            if ($paramArray['applytype_branch'] == 'wscyuangong' || $paramArray['applytype_branch'] == 'wscqinqi' || $paramArray['applytype_branch'] == 'wscneibu' || $paramArray['applytype_branch'] == 'wsctuijian') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_staff", "student_prove_img", "apply_id = '{$paramArray['apply_id']}'");
            } elseif ($paramArray['applytype_branch'] == 'wsctongbao') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_siblings", "student_prove_img,siblings_prove_img", "apply_id = '{$paramArray['apply_id']}'");
            } elseif ($paramArray['applytype_branch'] == 'wscshangpin') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_procou", "procou_scoreproveimg", "apply_id = '{$paramArray['apply_id']}'");
            }
        }

        if ($paramArray['applytype_isshop'] == '0') {
            $file = $this->DataControl->selectClear("select f.file_url from smc_student_coupons_file as f where f.apply_id = '{$paramArray['apply_id']}'");
        }

        $result = array();
        if ($file) {
            $result["data"] = $file;
            $res = array('error' => '0', 'errortip' => '获取附件成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取附件失败', 'result' => $result);
        }
        return $res;
    }


    function goodsConsumeLog($request)
    {

        $datawhere = " 1 ";

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and il.log_time>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and il.log_time<='{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select cf.feeitem_class,il.log_playclass,il.log_playname,il.log_playamount,s.school_cnname,il.log_reason,il.log_time
                from smc_student_itemtimes_log as il
                left join smc_student_itemtimes as si on si.itemtimes_id=il.itemtimes_id
                left join smc_school as s on s.school_id=si.school_id
                left join smc_code_feeitem as cf on cf.feeitem_id=il.feeitem_id and cf.company_id='{$request['company_id']}'
                where {$datawhere} and il.itemtimes_id='{$request['itemtimes_id']}'
                order by il.log_time desc
                limit {$pagestart},{$num}
              ";
        $logList = $this->DataControl->selectClear($sql);
        if (!$logList) {
            $this->error = true;
            $this->errortip = "无消耗数据";
            return false;
        }

        $class = $this->LgArraySwitch(array('0' => '课程杂费', '1' => '普通杂费'));

        foreach ($logList as &$logOne) {
            $logOne['feeitem_class_name'] = $class[$logOne['feeitem_class']];
            $logOne['log_time'] = date("Y-m-d H:i:s", $logOne['log_time']);
        }


        $data = array();
        $count_sql = "select il.log_id
                from smc_student_itemtimes_log as il
                left join smc_student_itemtimes as si on si.itemtimes_id=il.itemtimes_id
                left join smc_school as s on s.school_id=si.school_id
                left join smc_code_feeitem as cf on cf.feeitem_id=il.feeitem_id and cf.company_id='{$request['company_id']}'
                where {$datawhere} and il.itemtimes_id='{$request['itemtimes_id']}'
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $logList;

        return $data;

    }


    //获取单个职工
    function getStafferOneList($paramArray)
    {
        $sql = "SELECT
	s.staffer_id AS worker_id,
	s.staffer_cnname,
	s.staffer_enname,
	s.company_id,
	s.staffer_sex,
	s.staffer_sex,
	s.staffer_branch,
	s.staffer_mobile,
	i.info_birthday AS staffer_birthday,
	s.staffer_pass,
	s.staffer_leave,
	s.staffer_isparttime,
	s.staffer_leavetime,
	s.staffer_isparttime AS staffer_isparttime_status
FROM
	smc_staffer AS s
LEFT JOIN smc_staffer_info AS i ON s.staffer_id = i.staffer_id
WHERE
	s.staffer_id = '{$paramArray['worker_id']}' AND s.company_id = '{$paramArray['company_id']}'";
        $StafferList = $this->DataControl->selectClear($sql);
        if (!$StafferList) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "未查询到用户信息", 'result' => $result);
            return $res;
        }

        if ($StafferList) {
            $status = $this->LgArraySwitch(array('0' => '在职', '1' => '已离职'));
            $statuss = $this->LgArraySwitch(array('0' => '全职', '1' => '兼职'));
            foreach ($StafferList as &$val) {
                $val['staffer_leave'] = $status[$val['staffer_leave']];
                $val['staffer_isparttime_status'] = $statuss[$val['staffer_isparttime_status']];
                if ($val['staffer_birthday']) {
                    $val['age'] = $this->birthday2($val['staffer_birthday']);
                } else {
                    $val['age'] = '0';
                }
            }

        }

        $result = array();
        $result['list'] = $StafferList;

//
//        $result['post'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$paramArray['company_id']}' and post_type = 1");
//
//        $result['postnext'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where post_isrecrparttime = '1' and company_id = '{$paramArray['company_id']}'");
//
//        if(!$result['postnext']){
//            $result['postnext'] = array();
//        }
//
//        $result['postlevel'] = $this->DataControl->selectClear("select postlevel_id,postlevel_cnname from gmc_company_postlevel where company_id = '{$paramArray['company_id']}'");
//
//        $result['postpart'] = $this->DataControl->selectClear("select postpart_id,postpart_name from smc_school_postpart where company_id = '{$paramArray['company_id']}' and (school_id = '{$paramArray['school_id']}' or school_id = '0')");

        if (!$StafferList) {
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        } else {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }
        return $res;
    }


    function birthday2($birthday)
    {
        list($year, $month, $day) = explode("-", $birthday);
        $year_diff = date("Y") - $year;
        $month_diff = date("m") - $month;
        $day_diff = date("d") - $day;
        if ($day_diff < 0 || $month_diff < 0)
            $year_diff--;
        return $year_diff;
    }


    //单个学员详细资料
    function studentOneItem($request)
    {
        $sql = "SELECT s.student_id,s.student_branch,s.student_cnname,s.student_enname,s.student_sex,s.student_birthday,s.student_idcard,s.student_img,student_isdisease,student_diseaseremarks 
              FROM smc_student as s
              WHERE s.student_id='{$request['student_id']}' limit 0,1";
        $studentItem = $this->DataControl->selectOne($sql);

        $sql = "select sf.family_cnname,sf.family_enname,sf.family_mobile,sf.family_isdefault,sf.family_relation
                ,ifnull((select pw.wxchatnumber_id from smc_parenter_wxchattoken as pw where pw.parenter_id=p.parenter_id and pw.company_id='{$this->company_id}' limit 0,1),0) as wxchatnumber_id
                from smc_student_family as sf
                inner join smc_parenter as p on p.parenter_id=sf.parenter_id
                where sf.student_id='{$request['student_id']}'
                order by sf.family_isdefault desc
                ";
        $family = $this->DataControl->selectClear($sql);

        $data = array();
        if ($studentItem) {
            if (strpos($studentItem['student_img'], 'x-oss-process') == false) {
                $studentItem['student_img'] = $studentItem['student_img'] ? $studentItem['student_img'] . '?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90' : '';
            }
            if ($studentItem['student_birthday']) {
                $studentItem['student_birthday'] = date("Y-m-d", strtotime($studentItem['student_birthday']));
            } else {
                $studentItem['student_birthday'] = '';
            }

            $data['student'] = $studentItem;
        } else {
            $data['student'] = array();
        }
        if ($family) {
            $data['family'] = $family;
        } else {
            $data['family'] = array();
        }

        return $data;
    }

    //修改学生资料
    function studentOneEditItem($request)
    {
        $state = true;
        if (isset($request['student_idcard']) && $request['student_idcard'] !== '') {
            $student = $this->DataControl->getList("smc_student", "student_idcard='{$request['student_idcard']}' and student_id!='{$request['student_id']}' and company_id='{$request['company_id']}'");
            if ($student) {
                $this->error = false;
                $this->errortip = "身份证号重复";
                return false;
            }
        }
        $student_data = array();
        $student_data['student_cnname'] = $request['student_cnname'];
        $student_data['student_enname'] = $request['student_enname'];
        $student_data['student_sex'] = $request['student_sex'];
        $student_data['student_img'] = $request['student_img'];
        $student_data['student_idcard'] = $request['student_idcard'];
        $student_data['student_birthday'] = $request['student_birthday'];
        $student_data['student_isdisease'] = $request['student_isdisease'];
        $student_data['student_diseaseremarks'] = $request['student_diseaseremarks'];
        $student_data['student_updatatime'] = time();
        $this->DataControl->updateData("smc_student", "student_id='{$request['student_id']}'", $student_data);
        
        $family = json_decode(stripslashes($request['family']), true);

        if ($family) {

            $famList=$this->DataControl->getList("smc_student_family", "student_id='{$request['student_id']}'");

            $fanArray=array_column($famList?$famList:array(), 'family_mobile');
            $editfamArray=array_column($family, 'family_mobile');

            $delArray=array_diff($fanArray, $editfamArray);

            $addArray=array_diff($editfamArray, $fanArray);

            foreach ($family as $key => $val) {
                if(in_array($val['family_mobile'], $addArray)){

                    $parentOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id", "parenter_mobile='{$val['family_mobile']}'");

                    if (!$parentOne) {
                        $data = array();
                        $data['parenter_mobile'] = trim($val['family_mobile']);
                        $data['parenter_cnname'] = $val['family_cnname'];
                        $data['parenter_pass'] = md5(substr($data['parenter_mobile'], -6));
                        $data['parenter_bakpass'] = substr($data['parenter_mobile'], -6);
                        $data['parenter_addtime'] = time();
                        $parentid = $this->DataControl->insertData("smc_parenter", $data);
                    }


                    $data = array();
                    $data['family_relation'] = $val['family_relation'];
                    $data['family_cnname'] = $val['family_cnname'];
                    $data['family_enname'] = $val['family_enname'];
                    $data['family_mobile'] = $val['family_mobile'];
                    $data['student_id'] = $request['student_id'];
                    $data['family_isdefault'] = $val['family_isdefault'];
                    if ($parentid) {
                        $data['parenter_id'] = $parentid;
                    } else {
                        $data['parenter_id'] = $parentOne['parenter_id'];
                    }
                    $this->DataControl->insertData("smc_student_family", $data);
                }else{

                    $data = array();
                    $data['family_relation'] = $val['family_relation'];
                    $data['family_cnname'] = $val['family_cnname'];
                    $data['family_enname'] = $val['family_enname'];
                    $data['family_isdefault'] = $val['family_isdefault'];
                    $this->DataControl->updateData("smc_student_family", "family_mobile='{$val['family_mobile']}' and student_id='{$request['student_id']}'", $data);

                }
            }

            if ($delArray) {
                $delStr = implode("','", $delArray);
                $this->DataControl->delData("smc_student_family", "family_mobile in ('{$delStr}') and student_id='{$request['student_id']}'");
            }

        }

        return $state;
    }

    function getAchieveTargetList($request)
    {
        $datawhere = " 1 ";

        $datawhere .= " and A.company_id='{$request['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.school_branch like '%{$request['keyword']}%' 
            or b.school_cnname like '%{$request['keyword']}%' 
            or b.school_shortname like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['start_year']) && $request['start_year'] !== '') {
            $datawhere .= " AND A.target_year >= '{$request['start_year']}'";
        }

        if (isset($request['end_year']) && $request['end_year'] !== '') {
            $datawhere .= " AND A.target_year <= '{$request['end_year']}'";
        }

        if (isset($request['school_settle']) && $request['school_settle'] !== '') {
            $datawhere .= " AND A.school_settle = '{$request['school_settle']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.target_id,a.school_id
                ,(case when b.school_shortname='' then b.school_cnname else b.school_shortname end) as school_name
                ,b.school_branch
                ,a.coursetype_id
                ,c.coursetype_cnname
                ,a.target_year
                ,a.school_settle
                ,(select count(1) from smc_achieve_monthlytarget where school_id=a.school_id and coursetype_id=a.coursetype_id and target_year=a.target_year order by monthlytarget_id desc limit 0,1) as target_settle
                ,a.register_num
                ,a.reading_num
                ,a.losing_num
                from  smc_achieve_target a
                left join smc_school b on a.school_id=b.school_id
                left join smc_code_coursetype c on a.coursetype_id=c.coursetype_id
                where {$datawhere}
                and b.school_istest<>'1'
                order by b.school_branch,c.coursetype_branch
                ";

        $settle_status = $this->LgArraySwitch(array("-1" => "未设置", "0" => "无需设置", "1" => "已设置"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无目标设定数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['target_year'] = $dateexcelvar['target_year'];
                    $datearray['school_settle_name'] = $settle_status[$dateexcelvar['school_settle']];
                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $sql = "SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                    FROM smc_student_registerinfo AS a
                    WHERE 1
                    and a.info_status=1
                    and a.school_id = '{$dateexcelvar['school_id']}'
                    and a.coursetype_id = '{$dateexcelvar['coursetype_id']}'
                    and FROM_UNIXTIME(a.pay_successtime,'%Y')='{$dateexcelvar['target_year']}'  ";

                    $regiList = $this->DataControl->selectClear($sql);
                    if ($regiList) {
                        $regi_num = count($regiList);
                    } else {
                        $regi_num = 0;
                    }
                    if ($dateexcelvar['target_year'] > date('Y')) {
                        $datearray['register_rate'] = '--';
                    } else {
                        $datearray['register_rate'] = ($dateexcelvar['register_num'] > 0) ? ceil($regi_num / $dateexcelvar['register_num'] * 100) . '%' : '--';
                    }

                    $datearray['reading_num'] = $dateexcelvar['reading_num'];
                    $today = $dateexcelvar['target_year'] . '-12-31';
                    if ($today > date("Y-m-d")) {
                        $today = date("Y-m-d");
                    }
                    $sql = "SELECT A.school_id,
                    A.student_id,
                    C.coursetype_id,
                    MIN(A.study_beginday) AS beginday,
                    MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                    FROM smc_student_study A
                    LEFT JOIN smc_class B ON A.class_id=B.class_id 
                    LEFT JOIN smc_course C ON B.course_id=C.course_id 
                    LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                    WHERE A.company_id='{$request['company_id']}'
                    and A.school_id='{$dateexcelvar['school_id']}'
                    and C.coursetype_id='{$dateexcelvar['coursetype_id']}'
                    AND B.class_type='0' 
                    AND B.class_status>'-2'
                    AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                    GROUP BY A.school_id,A.student_id,C.coursetype_id
                    HAVING beginday<='{$today}' and endday>='{$today}'  ";

                    $readList = $this->DataControl->selectClear($sql);
                    if ($readList) {
                        $read_num = count($readList);
                    } else {
                        $read_num = 0;
                    }

                    if ($dateexcelvar['target_year'] > date('Y')) {
                        $datearray['reading_rate'] = '--';
                    } else {
                        $datearray['reading_rate'] = ($dateexcelvar['reading_num'] > 0) ? ceil($read_num / $dateexcelvar['reading_num'] * 100) . '%' : '--';
                    }

                    $datearray['losing_num'] = $dateexcelvar['losing_num'];
                    $sql = "
                    select A.student_id
                    ,min(A.changelog_day) as lost_date
                    from smc_student_changelog A
                    left join smc_student_change B on B.change_pid=A.change_pid
                    where A.company_id='{$request['company_id']}'
                    and A.school_id='{$dateexcelvar['school_id']}'
                    and A.stuchange_code='C04'
                    and A.coursetype_id ='{$dateexcelvar['coursetype_id']}'
                    and year(A.changelog_day)='{$dateexcelvar['target_year']}' 
                    group by a.student_id ";
                    $lostList = $this->DataControl->selectClear($sql);
                    if ($lostList) {
                        $lost_num = count($lostList);
                    } else {
                        $lost_num = 0;
                    }
                    if ($dateexcelvar['target_year'] > date('Y')) {
                        $datearray['losing_rate'] = '--';
                    } else {
                        $datearray['losing_rate'] = ($dateexcelvar['losing_num'] > 0) ? ceil($lost_num / $dateexcelvar['losing_num'] * 100) . '%' : '--';
                    }
//                    $datearray['losing_rate'] = ($dateexcelvar['reading_num'] > 0) ? ceil($read_num / $dateexcelvar['reading_num'] * 100) . '%' : '--';

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学校名称", "学校编号", "目标所属班组", "目标年度", "目标状态", "年度招生目标", "年度招生目标达成率", "年度在读目标", "年度在读目标达成率", "年度流失限额", "年度流失限额率"));
            $excelfileds = array('school_name', 'school_branch', 'coursetype_cnname', "target_year", 'school_settle_name', "register_num", "register_rate", "reading_num", "reading_rate", 'losing_num', 'losing_rate');

            $tem_name = $this->LgStringSwitch('校园目标管理表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $targetList = $this->DataControl->selectClear($sql);

            if (!$targetList) {
                $this->error = true;
                $this->errortip = "无目标设定数据";
                return false;
            }

            foreach ($targetList as &$var) {
                $var['school_settle_name'] = $settle_status[$var['school_settle']];

                $sql = "SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                FROM smc_student_registerinfo AS a
                WHERE 1
                and a.info_status=1
                and a.school_id = '{$var['school_id']}'
                and a.coursetype_id = '{$var['coursetype_id']}'
                and FROM_UNIXTIME(a.pay_successtime,'%Y')='{$var['target_year']}'  ";

                $regiList = $this->DataControl->selectClear($sql);
                if ($regiList) {
                    $regi_num = count($regiList);
                } else {
                    $regi_num = 0;
                }

                $today = $var['target_year'] . '-12-31';
                if ($today > date("Y-m-d")) {
                    $today = date("Y-m-d");
                }
                $sql = "SELECT A.school_id,
                A.student_id,
                C.coursetype_id,
                MIN(A.study_beginday) AS beginday,
                MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                WHERE A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and C.coursetype_id='{$var['coursetype_id']}'
                AND B.class_type='0' 
                AND B.class_status>'-2'
                AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                GROUP BY A.school_id,A.student_id,C.coursetype_id
                HAVING beginday<='{$today}' and endday>='{$today}'  ";

                $readList = $this->DataControl->selectClear($sql);
                if ($readList) {
                    $read_num = count($readList);
                } else {
                    $read_num = 0;
                }

                $sql = "
                select A.student_id
                ,min(A.changelog_day) as lost_date
                from smc_student_changelog A
                left join smc_student_change B on B.change_pid=A.change_pid
                where A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and A.stuchange_code='C04'
                and A.coursetype_id='{$var['coursetype_id']}'
                and year(A.changelog_day)='{$var['target_year']}' 
                group by a.student_id ";
                $lostList = $this->DataControl->selectClear($sql);
                if ($lostList) {
                    $lost_num = count($lostList);
                } else {
                    $lost_num = 0;
                }
                if ($var['target_year'] > date('Y')) {
                    $var['register_rate'] = '--';
                    $var['reading_rate'] = '--';
                    $var['losing_rate'] = '--';
                } else {
                    $var['register_rate'] = ($var['register_num'] > 0) ? ceil($regi_num / $var['register_num'] * 100) . '%' : '--';
                    $var['reading_rate'] = ($var['reading_num'] > 0) ? ceil($read_num / $var['reading_num'] * 100) . '%' : '--';
                    $var['losing_rate'] = ($var['losing_num'] > 0) ? ceil($lost_num / $var['losing_num'] * 100) . '%' : '--';
                }
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $cuntStunums = $this->DataControl->selectClear("select 
                a.school_id
                ,a.coursetype_id
                ,a.target_year
                ,(select count(1) from smc_achieve_monthlytarget where school_id=a.school_id and coursetype_id=a.coursetype_id and target_year=a.target_year order by monthlytarget_id desc limit 0,1) as target_settle
                from smc_achieve_target a
                left join smc_school b on a.school_id=b.school_id
                left join smc_code_coursetype c on a.coursetype_id=c.coursetype_id
                where {$datawhere}
                and b.school_istest<>'1'
                ");

                if ($cuntStunums) {
                    $allnum = count($cuntStunums);
                } else {
                    $allnum = 0;
                }
            }
            $data['allnum'] = $allnum;
            $data['list'] = $targetList;

            return $data;
        }
    }

    //删除区域
    function delAchieveTarget($request)
    {
        if ($this->DataControl->delData("smc_achieve_target", "target_id = '{$request['target_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除目标成功", 'result' => $result);
            $this->addGmcWorkLog($request['company_id'], $request['staffer_id'], "财务管理->校园目标", '删除目标', dataEncode($request));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除目标失败', 'result' => $result);
        }
        return $res;
    }

    function downloadAchieveTarget($request)
    {
        $sql = "select 
                a.school_id 
                ,(case when a.school_shortname='' then a.school_cnname else a.school_shortname end) as school_name 
                ,a.school_branch 
                ,c.coursetype_cnname 
                ,b.register_num 
                ,b.reading_num 
                ,b.losing_num 
                from smc_school a 
                left join smc_achieve_target b on a.school_id=b.school_id and b.target_year='{$request['target_year']}' and b.coursetype_id='{$request['coursetype_id']}' 
                left join smc_code_coursetype c on c.coursetype_id='{$request['coursetype_id']}' 
                where 1 
                and a.company_id='{$request['company_id']}' 
                and a.school_istest<>'1' 
                and a.school_isclose<>'1' 
                order by a.school_branch 
                ";

        $dateexcelarray = $this->DataControl->selectClear($sql);
        if (!$dateexcelarray) {
            $this->error = true;
            $this->errortip = "无目标数据";
            return false;
        }

        $outexceldate = array();
        if ($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_name'] = $dateexcelvar['school_name'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                $datearray['target_year'] = $request['target_year'];
                $datearray['register_num'] = ($dateexcelvar['register_num'] == '') ? '--' : $dateexcelvar['register_num'];
                $datearray['reading_num'] = ($dateexcelvar['reading_num'] == '') ? '--' : $dateexcelvar['reading_num'];
                $datearray['losing_num'] = ($dateexcelvar['losing_num'] == '') ? '--' : $dateexcelvar['losing_num'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = $this->LgArraySwitch(array("学校名称", "学校编号", "目标所属班组", "目标年度", "年度招生目标", "年度在读目标", "年度流失限额"));
        $excelfileds = array('school_name', 'school_branch', 'coursetype_cnname', 'target_year', 'register_num', 'reading_num', 'losing_num');

        $tem_name = $this->LgStringSwitch('校园目标管理导入模板') . '.xlsx';
        query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
        exit;
    }

    function importAchieveTarget($request)
    {
        $List = json_decode(stripslashes($request['list']), true);
        $suc = 0;
        $fal = 0;
        $newlist = array();

        $sql = "select a.target_id
                ,a.school_id 
                ,b.school_branch 
                ,c.coursetype_cnname 
                ,a.school_settle
                ,a.register_num 
                ,a.reading_num 
                ,a.losing_num 
                from smc_achieve_target a 
                left join smc_school b on a.school_id=b.school_id
                left join smc_code_coursetype c on a.coursetype_id=c.coursetype_id
                where a.target_year='{$request['target_year']}' 
                and a.coursetype_id='{$request['coursetype_id']}' 
                and a.company_id='{$request['company_id']}' 
                order by b.school_branch 
                ";

        do {
            $failed_pid = $this->createOrderPid('CW');
        } while ($this->DataControl->getFieldOne("smc_achieve_target_failed", "failed_id", "failed_pid='{$failed_pid}'"));

        $oldarray = $this->DataControl->selectClear($sql);

        foreach ($List as $item) {
            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$item['school_branch']}' and company_id='{$request['company_id']}'");

            if (!$schoolOne) {
                $item['fal_reason'] = '学校信息错误';
                $item['failed_status'] = 0;
                $newlist[] = $item;
                $fal++;
                continue;
            }

            if (!is_numeric($item['register_num']) || !is_numeric($item['reading_num']) || !is_numeric($item['losing_num'])
                || !is_int($item['register_num'] * 1)
                || !is_int($item['reading_num'] * 1)
                || !is_int($item['losing_num'] * 1)) {
                $item['fal_reason'] = '目标必须为整数';
                $item['failed_status'] = 0;
                $newlist[] = $item;
                $fal++;
                continue;
            }
            if ($oldarray) {
                $old_exists = false;

                $target_change = true;
                $target_id = 0;

                $settle_change = false;
                foreach ($oldarray as $olditem) {
                    if ($olditem['school_id'] == $schoolOne['school_id']) {
                        $old_exists = true;
                        $target_id = $olditem['target_id'];
                        if ($olditem['register_num'] == $item['register_num']
                            && $olditem['reading_num'] == $item['reading_num']
                            && $olditem['losing_num'] == $item['losing_num']) {
                            $target_change = false;
                        }
                        if ($olditem['school_settle'] !== $request['school_settle']) {
                            $settle_change = true;
                        }
                    }
                }
                if (!$old_exists) {
                    $datas = array();
                    $datas['company_id'] = $request['company_id'];
                    $datas['school_id'] = $schoolOne['school_id'];
                    $datas['coursetype_id'] = $request['coursetype_id'];
                    $datas['target_year'] = $request['target_year'];
                    $datas['school_settle'] = $request['school_settle'];
                    $datas['register_num'] = $item['register_num'];
                    $datas['reading_num'] = $item['reading_num'];
                    $datas['losing_num'] = $item['losing_num'];
                    $this->DataControl->insertData('smc_achieve_target', $datas);

                    $item['fal_reason'] = '新增成功';
                    $item['failed_status'] = 1;
//                    $newlist[] = $item;
                    $suc++;
                    continue;
                } else {
                    if ($target_change) {
                        $datas = array();
                        $datas['school_settle'] = ($request['school_settle'] == '0') ? '0' : '-1';
                        $datas['register_num'] = $item['register_num'];
                        $datas['reading_num'] = $item['reading_num'];
                        $datas['losing_num'] = $item['losing_num'];
                        $this->DataControl->updateData("smc_achieve_target", "target_id='{$target_id}'", $datas);

//                        $this->DataControl->delData("smc_achieve_monthlytarget", "school_id = '{$schoolOne['school_id']}' 
//                                                and target_year= '{$request['target_year']}' and coursetype_id='{$request['coursetype_id']}' ");

                        $item['fal_reason'] = '更新成功';
                        $item['failed_status'] = 1;
//                        $newlist[] = $item;
                        $suc++;
                        continue;
                    } else {
                        if ($settle_change) {
                            $datas = array();
                            $datas['school_settle'] = ($request['school_settle'] == '0') ? '0' : '-1';
                            $this->DataControl->updateData("smc_achieve_target", "target_id='{$target_id}'", $datas);

                            $item['fal_reason'] = '目标状态变更成功';
                            $item['failed_status'] = 1;
//                            $newlist[] = $item;
                            $suc++;
                            continue;
                        } else {
                            $item['fal_reason'] = '目标未变更';
                            $item['failed_status'] = 0;
                            $newlist[] = $item;
                            continue;
                        }
                    }
                }
            } else {
                $datas = array();
                $datas['company_id'] = $request['company_id'];
                $datas['school_id'] = $schoolOne['school_id'];
                $datas['coursetype_id'] = $request['coursetype_id'];
                $datas['target_year'] = $request['target_year'];
                $datas['school_settle'] = ($request['school_settle'] == '0') ? '0' : '-1';
                $datas['register_num'] = $item['register_num'];
                $datas['reading_num'] = $item['reading_num'];
                $datas['losing_num'] = $item['losing_num'];
                $this->DataControl->insertData('smc_achieve_target', $datas);

                $item['fal_reason'] = '新增成功';
//                $item['failed_status'] = 1;
                $newlist[] = $item;
                $suc++;
                continue;
            }
        }

        foreach ($newlist as $failedOne) {
            $datas = array();
            $datas['failed_pid'] = $failed_pid;
            $datas['failed_status'] = $failedOne['failed_status'];
            $datas['school_name'] = $failedOne['school_name'];
            $datas['school_branch'] = $failedOne['school_branch'];
            $datas['coursetype_cnname'] = $failedOne['coursetype_cnname'];
            $datas['target_year'] = $failedOne['target_year'];
            $datas['register_num'] = $failedOne['register_num'];
            $datas['reading_num'] = $failedOne['reading_num'];
            $datas['losing_num'] = $failedOne['losing_num'];
            $datas['fal_reason'] = $failedOne['fal_reason'];

            $fal++;
            $this->DataControl->insertData('smc_achieve_target_failed', $datas);
        }

        $fieldstring = array('school_name', 'school_branch', 'coursetype_cnname', 'target_year', 'register_num', 'reading_num', 'losing_num', 'fal_reason');
        $fieldname = array("学校名称", "学校编号", "目标所属班组", "目标年度", "年度招生目标", "年度在读目标", "年度流失限额", '错误原因');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['suc'] = $suc;
        $result['fal'] = $fal - $suc;
        $result['field'] = $field;
        $result['failed_pid'] = $failed_pid;
        $result['newlist'] = $newlist;

        $this->errortip = "导入成功" . $suc . "条，失败" . ($fal - $suc) . "条";
        return $result;
    }

    function downloadFailedAchieveTarget($request)
    {
        if (!isset($request['failed_pid']) || $request['failed_pid'] == '') {
            $this->error = true;
            $this->errortip = "无错误代码";
            return false;
        }
        $list = $this->DataControl->selectClear("select * from smc_achieve_target_failed WHERE failed_pid = '{$request['failed_pid']}' and failed_status=0  order by failed_id");

        if (!$list) {
            $this->error = true;
            $this->errortip = "无错误数据1";
            return false;
        }
//        $list = json_decode(stripslashes($request['list']), 1);
        if (count($list) == 0) {
            $this->error = true;
            $this->errortip = "无错误数据2";
            return false;
        }

        $k = 0;
        $outexceldate = array();
        foreach ($list as $dateexcelvar) {
            if ($dateexcelvar['fal_reason'] == '新增成功') {
                continue;
            }
            $datearray = array();
            $datearray['school_name'] = $dateexcelvar['school_name'];
            $datearray['school_branch'] = $dateexcelvar['school_branch'];
            $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
            $datearray['target_year'] = $dateexcelvar['target_year'];
            $datearray['register_num'] = $dateexcelvar['register_num'];
            $datearray['reading_num'] = $dateexcelvar['reading_num'];
            $datearray['losing_num'] = $dateexcelvar['losing_num'];
            $datearray['fal_reason'] = $dateexcelvar['fal_reason'];
            $outexceldate[] = $datearray;
            $k++;
        }
        if ($k == 0) {
            $this->error = true;
            $this->errortip = "无错误数据3";
            return false;
        }
        $excelfileds = $this->LgArraySwitch(array('school_name', 'school_branch', 'coursetype_cnname', 'target_year', 'register_num', 'reading_num', 'losing_num', 'fal_reason'));
        $excelheader = array("学校名称", "学校编号", "目标所属班组", "目标年度", "年度招生目标", "年度在读目标", "年度流失限额", '错误原因');
        $fielname = $this->LgStringSwitch("导入失败明细");
        query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
        exit;
    }

    function getMonthAchieveTarget($request)
    {
        $datawhere = " 1 ";

        $datawhere .= " and A.company_id='{$request['company_id']}'";

        $datawhere .= " and A.school_id='{$request['school_id']}'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['target_year']) && $request['target_year'] !== '') {
            $datawhere .= " AND A.target_year = '{$request['target_year']}'";
        }

        $sql = "select MONTH(concat(a.target_month,'-01')) as month_sort,a.target_month,a.register_num,a.reading_num,a.losing_num,a.school_id,a.coursetype_id
                from smc_achieve_monthlytarget a
                where {$datawhere}
                order by a.target_month
                ";

        $monthList = $this->DataControl->selectClear($sql);

        if (!$monthList) {
            $this->error = true;
            $this->errortip = "无目标数据";
            return false;
        }

        foreach ($monthList as &$var) {
            if ($var['target_month'] > date('Y-m')) {
                $var['register_rate'] = '--';
                $var['reading_rate'] = '--';
                $var['losing_rate'] = '--';
                continue;
            }

            $sql = "SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                FROM smc_student_registerinfo AS a
                WHERE 1
                and a.info_status=1
                and a.school_id = '{$var['school_id']}'
                and a.coursetype_id = '{$var['coursetype_id']}'
                and FROM_UNIXTIME(a.pay_successtime,'%Y-%m')='{$var['target_month']}'  ";

            $regiList = $this->DataControl->selectClear($sql);
            if ($regiList) {
                $regi_num = count($regiList);
            } else {
                $regi_num = 0;
            }

            $today = $var['target_month'] . '-01';
            $today = date('Y-m-t', strtotime($today));
            if ($today > date("Y-m-d")) {
                $today = date("Y-m-d");
            }
            $sql = "SELECT A.school_id,
                A.student_id,
                C.coursetype_id,
                MIN(A.study_beginday) AS beginday,
                MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                WHERE A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and C.coursetype_id='{$var['coursetype_id']}'
                AND B.class_type='0' 
                AND B.class_status>'-2'
                AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                GROUP BY A.school_id,A.student_id,C.coursetype_id
                HAVING beginday<='{$today}' and endday>='{$today}'  ";

            $readList = $this->DataControl->selectClear($sql);
            if ($readList) {
                $read_num = count($readList);
            } else {
                $read_num = 0;
            }

            $sql = "select A.student_id
                ,min(A.changelog_day) as lost_date
                from smc_student_changelog A
                left join smc_student_change B on B.change_pid=A.change_pid
                where A.company_id='{$request['company_id']}'
                and A.school_id='{$var['school_id']}'
                and A.coursetype_id='{$var['coursetype_id']}'
                and A.stuchange_code='C04'
                and DATE_FORMAT((A.changelog_day),'%Y-%m')='{$var['target_month']}' 
                group by a.student_id ";
            $lostList = $this->DataControl->selectClear($sql);
            if ($lostList) {
                $lost_num = count($lostList);
            } else {
                $lost_num = 0;
            }
            $var['register_rate'] = ($var['register_num'] > 0) ? ceil($regi_num / $var['register_num'] * 100) . '%' : '--';
            $var['reading_rate'] = ($var['reading_num'] > 0) ? ceil($read_num / $var['reading_num'] * 100) . '%' : '--';
            $var['losing_rate'] = ($var['losing_num'] > 0) ? ceil($lost_num / $var['losing_num'] * 100) . '%' : '--';
        }

        $data = array();
        $data['list'] = $monthList;
        $data['allnum'] = count($monthList);

        return $data;
    }


}
