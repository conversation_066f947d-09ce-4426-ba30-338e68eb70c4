<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class TrainingModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //职业体系管理
    function CareerSystemList($paramArray)
    {

        $datawhere = " c.company_id = '{$paramArray['company_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.career_cnname like '%{$paramArray['keyword']}%' or c.career_enname like '%{$paramArray['keyword']}%' or c.career_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['careertype_id']) && $paramArray['careertype_id'] !== "") {
            $datawhere .= " and ct.careertype_id ='{$paramArray['careertype_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    c.*,ct.careertype_cnname,
                    (SELECT ec.career_cnname FROM eas_career as ec WHERE ec.career_id = c.career_nextid) as career_name,
                    (SELECT COUNT(pc.post_id) FROM eas_post_career as pc WHERE pc.career_id = c.career_id) as career_num,
                    (SELECT COUNT(cs.stage_id) FROM eas_career_stage as cs WHERE cs.career_id = c.career_id) as stage_num
                FROM
                    eas_career as c
                LEFT JOIN
                    eas_code_careertype as ct ON ct.careertype_id = c.careertype_id
                WHERE
                    {$datawhere}
                ORDER BY
                    c.career_id DESC
                LIMIT {$pagestart},{$num}";

        $CareerList = $this->DataControl->selectClear($sql);
        if($CareerList){
            foreach($CareerList as &$v){
                if(!$v['career_num']){
                    $v['career_num'] = '暂无适配';
                }
                if(!$v['stage_num']){
                    $v['stage_num'] = '暂未设置';
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT
                                                        COUNT(c.career_id) as num
                                                    FROM
                                                        eas_career as c
                                                    LEFT JOIN
                                                        eas_code_careertype as ct ON ct.careertype_id = c.careertype_id
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('careertype_cnname', 'career_cnname', 'career_branch', 'career_name', 'career_num', 'stage_num');
        $fieldname = $this->LgArraySwitch(array('所属职业类型', '职业名称', '职业编号', '下级别职业', '适配职务数量', '职业阶段数量'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $fieldismethod = array("0", "0", "0", "0", "0", "0");
        $fieldiisswitch = array("0", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldismethod[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CareerList) {
            $result['list'] = $CareerList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职业信息", 'result' => $result);
        }

        return $res;
    }

    //添加职业信息
    function addCareerApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career","company_id='{$paramArray['company_id']}' and career_branch='{$paramArray['career_branch']}'")){
            $res = array('error' => '1', 'errortip' => '职业编号重复');
            return $res;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['careertype_id'] = $paramArray['careertype_id'];
        $data['career_cnname'] = $paramArray['career_cnname'];
        $data['career_enname'] = $paramArray['career_enname'];
        $data['career_branch'] = $paramArray['career_branch'];
        $data['career_nextid'] = $paramArray['career_nextid'];
        $data['career_sort'] = $paramArray['career_sort'];
        $data['career_createtime'] = time();
        if ($this->DataControl->insertData("eas_career", $data)) {
            $res = array('error' => '0', 'errortip' => "添加职业信息成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '添加职业信息', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '添加职业信息失败');
        }

        return $res;
    }

    //编辑职业信息
    function editCareerApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career","company_id='{$paramArray['company_id']}' and career_branch='{$paramArray['career_branch']}' and career_id<>'{$paramArray['career_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业编号重复');
            return $res;
        }

        $data = array();
        $data['careertype_id'] = $paramArray['careertype_id'];
        $data['career_cnname'] = $paramArray['career_cnname'];
        $data['career_enname'] = $paramArray['career_enname'];
        $data['career_branch'] = $paramArray['career_branch'];
        $data['career_nextid'] = $paramArray['career_nextid'];
        $data['career_sort'] = $paramArray['career_sort'];
        $data['career_updatatime'] = time();
        if ($this->DataControl->updateData("eas_career", "career_id = '{$paramArray['career_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑职业信息成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '编辑职业信息', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑职业信息失败');
        }

        return $res;
    }


    //删除职业信息
    function delCareerApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_career","career_id='{$paramArray['career_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业信息不存在');
            return $res;
        }
        $stage = $this->DataControl->getOne("eas_career_stage","career_id='{$paramArray['career_id']}'");
        $course = $this->DataControl->getOne("eas_course","career_id='{$paramArray['career_id']}'");
        if($stage || $course){
            $res = array('error' => '1', 'errortip' => '职业已被使用，不可删除');
            return $res;
        }

        if($this->DataControl->delData("eas_career", "career_id='{$paramArray['career_id']}'")){
            $res = array('error' => 0,'errortip' => "删除职业信息成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '删除职业信息', dataEncode($paramArray));
        }else{
            $res = array('error' => 1,'errortip' => "删除职业信息失败!");
        }

        return $res;
    }


    //校园职务
    function SchoolPostList($paramArray)
    {
        $datawhere = " p.company_id = '{$paramArray['company_id']}' and p.post_type = '1'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.post_name like '%{$paramArray['keyword']}%' or p.post_code like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($paramArray['code']) && $paramArray['code'] == '1'){
            $sql = "SELECT
                        p.post_id,p.post_name,p.post_code
                    FROM
                        gmc_company_post as p
                    WHERE
                        {$datawhere} AND p.post_id IN (SELECT pc.post_id FROM eas_post_career as pc WHERE pc.career_id = '{$paramArray['career_id']}')
                    LIMIT {$pagestart},{$num}";

            $PostList = $this->DataControl->selectClear($sql);

            $all_num = $this->DataControl->selectOne("SELECT COUNT(p.post_id) as num FROM gmc_company_post as p WHERE {$datawhere} AND p.post_id IN (SELECT pc.post_id FROM eas_post_career as pc WHERE pc.career_id = '{$paramArray['career_id']}')");
            $allnums = $all_num['num'];
        }else {
            $sql = "SELECT
                        p.post_id,p.post_name,p.post_code
                    FROM
                        gmc_company_post as p
                    WHERE
                        {$datawhere} AND p.post_id NOT IN (SELECT pc.post_id FROM eas_post_career as pc WHERE pc.career_id = '{$paramArray['career_id']}')
                    ";

            $PostList = $this->DataControl->selectClear($sql);

            $all_num = $this->DataControl->selectOne("SELECT COUNT(p.post_id) as num FROM gmc_company_post as p WHERE {$datawhere} AND p.post_id NOT IN (SELECT pc.post_id FROM eas_post_career as pc WHERE pc.career_id = '{$paramArray['career_id']}')");
            $allnums = $all_num['num'];
        }

        $fieldstring = array('post_id', 'post_name', 'post_code');
        $fieldname = $this->LgArraySwitch(array('职务ID', '职务名称', '职务编号'));
        $fieldcustom = array("0", "1", "1");
        $fieldshow = array("0", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PostList) {
            $result['list'] = $PostList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无校园职务", 'result' => $result);
        }

        return $res;
    }

    //适配校园职务
    function addSchoolPost($paramArray)
    {
        $career_list = json_decode(stripslashes($paramArray['career_list']), true);
        if($career_list){
            foreach($career_list as $v){
                $data = array();
                $data['post_id'] = $v['post_id'];
                $data['career_id'] = $v['career_id'];
                $this->DataControl->insertData("eas_post_career", $data);
            }
            $res = array('error' => '0', 'errortip' => "适配校园职务成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '适配校园职务', dataEncode($paramArray));
            return $res;
        }else{
            $res = array('error' => '1', 'errortip' => '职务ID不存在');
            return $res;
        }
    }

    //取消适配校园职务
    function delSchoolPost($paramArray)
    {
        if(!$this->DataControl->getOne("eas_post_career","career_id='{$paramArray['career_id']}' and post_id='{$paramArray['post_id']}'")){
            $res = array('error' => '1', 'errortip' => '校园职务未适配');
            return $res;
        }

        if ($this->DataControl->delData("eas_post_career", "career_id='{$paramArray['career_id']}' and post_id='{$paramArray['post_id']}'")) {
            $res = array('error' => '0', 'errortip' => "取消适配校园职务成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '取消适配校园职务', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '取消适配校园职务失败');
        }

        return $res;
    }

    //职业阶段管理
    function CareerStageList($paramArray)
    {
        $datawhere = " s.company_id = '{$paramArray['company_id']}' and s.career_id = '{$paramArray['career_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.stage_cnname like '%{$paramArray['keyword']}%' or s.stage_enname like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    s.stage_id,s.stage_cnname,s.stage_enname,s.stage_sort,s.stage_examnum,s.stage_examtime,
                    (SELECT es.stage_id FROM eas_career_stage as es WHERE s.stage_nextid = es.stage_id) as stage_nextid,
                    (SELECT es.stage_cnname FROM eas_career_stage as es WHERE s.stage_nextid = es.stage_id) as stage_name
                FROM
                    eas_career_stage AS s
                WHERE
                    {$datawhere}
                ORDER BY
                    s.stage_sort ASC
                LIMIT {$pagestart},{$num}";

        $stageList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(s.stage_id) as num FROM eas_career_stage as s WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('stage_id', 'stage_cnname', 'stage_enname', 'stage_sort', 'stage_name', 'stage_examnum', 'stage_examtime');
        $fieldname = $this->LgArraySwitch(array('职业阶段ID', '职业阶段名称', '职业阶段英文名', '职业阶段排序', '下一职业阶段', '考核题数', '考核时长（分钟）'));
        $fieldcustom = array("0", "1", "0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "0", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $career = $this->DataControl->getFieldOne("eas_career","career_cnname","career_id = '{$paramArray['career_id']}'");

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;
        $result['career_cnname'] = $career['career_cnname'];

        if ($stageList) {
            $result['list'] = $stageList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职业阶段", 'result' => $result);
        }

        return $res;

    }

    //添加职业阶段
    function addCareerStageApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career_stage","company_id='{$paramArray['company_id']}' and career_id='{$paramArray['career_id']}' and stage_cnname='{$paramArray['stage_cnname']}'")){
            $res = array('error' => '1', 'errortip' => '职业阶段名称重复');
            return $res;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['career_id'] = $paramArray['career_id'];
        $data['stage_cnname'] = $paramArray['stage_cnname'];
        $data['stage_enname'] = $paramArray['stage_enname'];
        $data['stage_nextid'] = $paramArray['stage_nextid'];
        $data['stage_sort'] = $paramArray['stage_sort'];
        $data['stage_examnum'] = $paramArray['stage_examnum'];
        $data['stage_examtime'] = $paramArray['stage_examtime'];
        $data['stage_createtime'] = time();
        if ($this->DataControl->insertData("eas_career_stage", $data)) {
            $res = array('error' => '0', 'errortip' => "添加职业阶段成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '添加职业阶段', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '添加职业阶段失败');
        }

        return $res;
    }

    //编辑职业阶段
    function editCareerStageApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career_stage","company_id='{$paramArray['company_id']}' and career_id='{$paramArray['career_id']}' and stage_cnname='{$paramArray['stage_cnname']}' and stage_id<>'{$paramArray['stage_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业阶段名称重复');
            return $res;
        }

        $data = array();
        $data['stage_cnname'] = $paramArray['stage_cnname'];
        $data['stage_enname'] = $paramArray['stage_enname'];
        $data['stage_nextid'] = $paramArray['stage_nextid'];
        $data['stage_sort'] = $paramArray['stage_sort'];
        $data['stage_examnum'] = $paramArray['stage_examnum'];
        $data['stage_examtime'] = $paramArray['stage_examtime'];
        $data['stage_updatatime'] = time();
        if ($this->DataControl->updateData("eas_career_stage", "stage_id = '{$paramArray['stage_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑职业阶段成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '编辑职业阶段', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑职业阶段失败');
        }

        return $res;
    }

    //删除职业阶段
    function delCareerStageApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_career_stage","stage_id='{$paramArray['stage_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业阶段不存在');
            return $res;
        }
        $course = $this->DataControl->getOne("eas_course","stage_id='{$paramArray['stage_id']}'");
        if($course){
            $res = array('error' => '1', 'errortip' => '职业阶段已被使用，不可删除');
            return $res;
        }

        if ($this->DataControl->delData("eas_career_stage", "stage_id = '{$paramArray['stage_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除职业阶段成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '删除职业阶段', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '删除职业阶段失败');
        }

        return $res;
    }

    //职业课管理
    function VocationalCourses($paramArray)
    {
        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.course_type = '1' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['careertype_id']) && $paramArray['careertype_id'] !== '') {
            $datawhere .= " and ct.careertype_id = '{$paramArray['careertype_id']}'";
        }
        if (isset($paramArray['career_id']) && $paramArray['career_id'] !== '') {
            $datawhere .= " and cr.career_id = '{$paramArray['career_id']}'";
        }
        if(isset($paramArray['stage_id']) && $paramArray['stage_id'] != ''){
            $datawhere .= " and c.stage_id = '{$paramArray['stage_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    c.course_id,c.career_id,c.stage_id,c.course_name,c.course_img,c.course_intro,cr.career_cnname,ct.careertype_id,ct.careertype_cnname,
                    (SELECT s.stage_cnname FROM eas_career_stage as s WHERE s.stage_id = c.stage_id) as stage_cnname,
                    (SELECT COUNT(ca.adaptive_id) FROM eas_course_adaptive as ca WHERE ca.course_id = c.course_id) as career_num,
                    (SELECT COUNT(cc.chapter_id) FROM eas_course_chapter as cc WHERE cc.course_id = c.course_id) as chapter_num
                FROM
                    eas_course as c
                LEFT JOIN
                    eas_career as cr ON cr.career_id = c.career_id
                LEFT JOIN
                    eas_code_careertype as ct ON ct.careertype_id = cr.careertype_id
                WHERE
                    {$datawhere}
                ORDER BY
                    c.course_id DESC
                LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);
        if($courseList){
            foreach($courseList as &$v){
                if(!$v['career_num']){
                    $v['career_num'] = "暂无适配";
                }
                if(!$v['chapter_num']){
                    $v['chapter_num'] = "暂未设置";
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT
                                                        COUNT(c.course_id) as num
                                                    FROM
                                                        eas_course as c
                                                    LEFT JOIN
                                                        eas_career as cr ON cr.career_id = c.career_id
                                                    LEFT JOIN
                                                        eas_code_careertype as ct ON ct.careertype_id = cr.careertype_id
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('course_id ', 'course_name', 'career_cnname', 'careertype_cnname', 'stage_cnname', 'course_img', 'course_intro', 'career_num', 'chapter_num');
        $fieldname = $this->LgArraySwitch(array('课程ID', '课程名称', '所属职业', '所属职业类型', '所属职业阶段', '课程封面', '课程介绍', '适配职务数量', '课程章节数量'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($courseList) {
            $result['list'] = $courseList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无职业课", 'result' => $result);
        }

        return $res;
    }

    //通识课管理
    function GeneralCourses($paramArray)
    {
        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.course_type = '0' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['openclasstype_id']) && $paramArray['openclasstype_id'] !== '') {
            $datawhere .= " and o.openclasstype_id = '{$paramArray['openclasstype_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    c.course_id,c.course_name,c.course_img,c.course_intro,c.course_recommend,c.course_popular,c.openclasstype_id,o.openclasstype_cnname,
                    (SELECT COUNT(cc.chapter_id) FROM eas_course_chapter as cc WHERE cc.course_id = c.course_id) as chapter_num
                FROM
                    eas_course as c
                LEFT JOIN
                    eas_code_openclasstype as o ON o.openclasstype_id = c.openclasstype_id
                WHERE
                    {$datawhere}
                ORDER BY
                    c.course_id DESC
                LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);
        if ($courseList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            foreach ($courseList as &$val) {
                $val['course_recommend'] = $status[$val['course_recommend']];
                $val['course_popular'] = $status[$val['course_popular']];
                if(!$val['chapter_num']){
                    $val['chapter_num'] = "暂未设置";
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT
                                                        COUNT(c.course_id) as num
                                                    FROM
                                                        eas_course as c
                                                    LEFT JOIN
                                                        eas_code_openclasstype as o ON o.openclasstype_id = c.openclasstype_id
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('course_id ', 'course_name', 'openclasstype_cnname', 'course_img', 'course_intro','course_recommend','course_popular', 'chapter_num');
        $fieldname = $this->LgArraySwitch(array('课程ID', '课程名称', '所属通识课类型', '课程封面', '课程介绍','是否推荐课程','是否最热课程', '课程章节数量'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($courseList) {
            $result['list'] = $courseList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无通识课", 'result' => $result);
        }

        return $res;
    }

    //添加职业课/通识课
    function addVocationalCoursesApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course","company_id='{$paramArray['company_id']}' and course_name='{$paramArray['course_name']}'")){
            $res = array('error' => '1', 'errortip' => '课程名称重复');
            return $res;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['course_type'] = $paramArray['course_type'];
        $data['course_name'] = $paramArray['course_name'];
        $data['course_intro'] = $paramArray['course_intro'];
        $data['course_img'] = $paramArray['course_img'];
        if($paramArray['course_type'] == '0'){
            $data['openclasstype_id'] = $paramArray['openclasstype_id'];
            $data['course_recommend'] = $paramArray['course_recommend'];
            $data['course_popular'] = $paramArray['course_popular'];
            $tip = '通识课';
        }else{
            $data['career_id'] = $paramArray['career_id'];
            $data['stage_id'] = $paramArray['stage_id'];
            $data['course_career'] = $paramArray['course_career'];
            $tip = '职业课';
        }
        $data['course_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_course", $data);
        if ($dataid) {
            if($paramArray['course_type'] == '1'){
                $career = $this->DataControl->selectClear("SELECT post_id FROM eas_post_career WHERE career_id = '{$paramArray['career_id']}'");
                foreach($career as $v){
                    $list = array();
                    $list['course_id'] = $dataid;
                    $list['post_id'] = $v['post_id'];
                    $this->DataControl->insertData("eas_course_adaptive", $list);
                }
            }
            $res = array('error' => '0', 'errortip' => "添加{$tip}成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加{$tip}", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加{$tip}失败");
        }

        return $res;
    }

    //编辑职业课/通识课
    function editVocationalCoursesApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course","company_id='{$paramArray['company_id']}' and course_name='{$paramArray['course_name']}' and course_id<>'{$paramArray['course_id']}'")){
            $res = array('error' => '1', 'errortip' => '课程名称重复');
            return $res;
        }

        $data = array();
        $data['course_name'] = $paramArray['course_name'];
        $data['course_intro'] = $paramArray['course_intro'];
        $data['course_img'] = $paramArray['course_img'];
        if($paramArray['course_type'] == '0'){
            $data['openclasstype_id'] = $paramArray['openclasstype_id'];
            $data['course_recommend'] = $paramArray['course_recommend'];
            $data['course_popular'] = $paramArray['course_popular'];
            $tip = '通识课';
        }else{
            $data['career_id'] = $paramArray['career_id'];
            $data['stage_id'] = $paramArray['stage_id'];
            $data['course_career'] = $paramArray['course_career'];
            $tip = '职业课';
        }
        $data['course_updatatime'] = time();
        $course = $this->DataControl->getFieldOne("eas_course","career_id","course_id = '{$paramArray['course_id']}'");
        if($paramArray['course_type'] == '1' && $course['career_id'] != $paramArray['career_id']){
            $this->DataControl->delData("eas_course_adaptive","course_id = '{$paramArray['course_id']}'");
            $career = $this->DataControl->selectClear("SELECT post_id FROM eas_post_career WHERE career_id = '{$paramArray['career_id']}'");
            if($career){
                foreach($career as $v){
                    $list = array();
                    $list['course_id'] = $paramArray['course_id'];
                    $list['post_id'] = $v['post_id'];
                    $this->DataControl->insertData("eas_course_adaptive", $list);
                }
            }
        }
        if ($this->DataControl->updateData("eas_course", "course_id = '{$paramArray['course_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑{$tip}成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑{$tip}", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑{$tip}失败");
        }

        return $res;
    }

    //删除职业课/通识课
    function delVocationalCoursesApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_course","course_id='{$paramArray['course_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业课不存在');
            return $res;
        }

        if($paramArray['course_type'] == '0'){
            $tip = '通识课';
        }else{
            $tip = '职业课';
        }

        if ($this->DataControl->delData("eas_course","course_id='{$paramArray['course_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除{$tip}成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除{$tip}", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除{$tip}失败");
        }

        return $res;
    }

    //职业课适配职务列表
    function GetCareerList($paramArray)
    {
        $datawhere = " a.course_id = '{$paramArray['course_id']}' ";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    p.post_id,p.post_name,p.post_code,a.adaptive_id,a.career_isexam
                FROM
                    eas_course_adaptive as a
                LEFT JOIN
                    gmc_company_post as p ON p.post_id = a.post_id
                WHERE
                    {$datawhere}
                LIMIT
                    {$pagestart},{$num}";
        $CareerList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT
                                                        COUNT(p.post_id) as num
                                                    FROM
                                                        eas_course_adaptive as a
                                                    LEFT JOIN
                                                        gmc_company_post as p ON p.post_id = a.post_id
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('post_id', 'post_name', 'post_code', 'career_isexam');
        $fieldname = $this->LgArraySwitch(array('职务ID', '职务名称', '职务编号', '是否开启免考'));
        $fieldcustom = array("0", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CareerList) {
            $result['list'] = $CareerList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职业列表", 'result' => $result);
        }

        return $res;
    }

    //设置是否免考
    function IsExamApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_course_adaptive","adaptive_id='{$paramArray['adaptive_id']}'")){
            $res = array('error' => '1', 'errortip' => '校园职务不存在');
            return $res;
        }

        $data = array();
        $data['career_isexam'] = $paramArray['career_isexam'];
        if($this->DataControl->updateData("eas_course_adaptive","adaptive_id='{$paramArray['adaptive_id']}'",$data)){
            $res = array('error' => '0', 'errortip' => "设置是否免考成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '设置是否免考', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '设置是否免考失败');
        }

        return $res;
    }

    //职业课移除职务
    function delAdaptiveApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_course_adaptive","adaptive_id='{$paramArray['adaptive_id']}'")){
            $res = array('error' => '1', 'errortip' => '校园职务不存在');
            return $res;
        }

        if($this->DataControl->delData("eas_course_adaptive","adaptive_id='{$paramArray['adaptive_id']}'")){
            $res = array('error' => '0', 'errortip' => "职业课移除职务成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '职业课移除职务', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '职业课移除职务失败');
        }

        return $res;
    }

    //设置过关正确率
    function AccuracyApi($paramArray)
    {
        $CourseList = $this->DataControl->selectClear("SELECT course_id,course_accuracy FROM eas_course WHERE company_id = '{$paramArray['company_id']}' and course_type = '1'");
        if($CourseList){
            $data = array();
            foreach($CourseList as $v){
                $data['course_accuracy'] = $paramArray['course_accuracy'];
                $result = $this->DataControl->updateData("eas_course","course_id = '{$v['course_id']}'",$data);
            }
        }

        if($result){
            $res = array('error' => '0', 'errortip' => "设置过关正确率成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '设置过关正确率', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '设置过关正确率失败');
        }

        return $res;
    }

    //适配职业
    function adaptiveCareer($paramArray)
    {
        $post_list = json_decode(stripslashes($paramArray['post_list']), true);
        if($post_list){
            foreach($post_list as $v){
                $data = array();
                $data['course_id'] = $v['course_id'];
                $data['post_id'] = $v['post_id'];
                $this->DataControl->insertData("eas_course_adaptive", $data);
            }
            $res = array('error' => '0', 'errortip' => "适配职业成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '适配职业', dataEncode($paramArray));
            return $res;
        }else{
            $res = array('error' => '1', 'errortip' => '职务ID不存在');
            return $res;
        }
    }

    //取消适配职业
    function delAdaptiveCareer($paramArray)
    {
        if(!$this->DataControl->getOne("eas_course_adaptive","post_id='{$paramArray['post_id']}' and course_id='{$paramArray['course_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业未适配');
            return $res;
        }

        if ($this->DataControl->delData("eas_course_adaptive", "post_id='{$paramArray['post_id']}' and course_id='{$paramArray['course_id']}'")) {
            $res = array('error' => '0', 'errortip' => "取消适配职业成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '取消适配职业', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '取消适配职业失败');
        }

        return $res;
    }

    //课程章节管理
    function CourseChapterList($paramArray){
        $datawhere = " c.course_id='{$paramArray['course_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.chapter_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    c.chapter_id,c.chapter_name,c.chapter_sort,(SELECT co.course_name FROM eas_course as co WHERE co.course_id = c.course_id) as course_name
                FROM
                    eas_course_chapter as c
                WHERE
                    {$datawhere}
                ORDER BY
                    c.chapter_sort ASC
                LIMIT
                    {$pagestart},{$num}";
        $ChapterList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(c.chapter_id) as num FROM eas_course_chapter as c WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('chapter_id', 'course_name', 'chapter_name', 'chapter_sort');
        $fieldname = $this->LgArraySwitch(array('章节ID', '课程名称', '课程章节名称', '课程章节排序'));
        $fieldcustom = array("0", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ChapterList) {
            $result['list'] = $ChapterList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程章节", 'result' => $result);
        }

        return $res;
    }

    //添加课程章节
    function addChapterApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course_chapter","course_id='{$paramArray['course_id']}' and chapter_name='{$paramArray['chapter_name']}'")){
            $res = array('error' => '1', 'errortip' => '课程章节名称重复');
            return $res;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['course_id'] = $paramArray['course_id'];
        $data['chapter_name'] = $paramArray['chapter_name'];
        $data['chapter_sort'] = $paramArray['chapter_sort'];
        $data['chapter_createtime'] = time();
        if ($this->DataControl->insertData("eas_course_chapter", $data)) {
            $res = array('error' => '0', 'errortip' => "添加课程章节成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加课程章节", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加课程章节失败");
        }

        return $res;
    }

    //编辑课程章节
    function editChapterApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course_chapter","course_id='{$paramArray['course_id']}' and chapter_name='{$paramArray['chapter_name']}' and chapter_id<>'{$paramArray['chapter_id']}'")){
            $res = array('error' => '1', 'errortip' => '课程章节名称重复');
            return $res;
        }

        $data = array();
        $data['chapter_name'] = $paramArray['chapter_name'];
        $data['chapter_sort'] = $paramArray['chapter_sort'];
        $data['chapter_updatatime'] = time();
        if ($this->DataControl->updateData("eas_course_chapter", "chapter_id = '{$paramArray['chapter_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑课程章节成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑课程章节", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑课程章节失败");
        }

        return $res;
    }

    //删除课程章节
    function delChapterApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_course_chapter","chapter_id='{$paramArray['chapter_id']}'")){
            $res = array('error' => '1', 'errortip' => '课程章节不存在');
            return $res;
        }

        if ($this->DataControl->delData("eas_course_chapter","chapter_id='{$paramArray['chapter_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除课程章节成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除课程章节", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除课程章节失败");
        }

        return $res;
    }

    //章节内容管理
    function TrainingDataList($paramArray){
        $datawhere = " t.chapter_id='{$paramArray['chapter_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.trainhour_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['trainhour_class']) && $paramArray['trainhour_class'] !== '') {
            $datawhere .= " and t.trainhour_class = '{$paramArray['trainhour_class']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    t.trainhour_id,t.trainhour_name,t.trainhour_class,t.trainhour_coverimg,t.trainhour_fileurl
                FROM
                    eas_course_trainhour as t
                WHERE
                    {$datawhere}
                LIMIT
                    {$pagestart},{$num}";
        $TrainhourList = $this->DataControl->selectClear($sql);
        if ($TrainhourList) {
            $status = $this->LgArraySwitch(array("0" => "视频模式", "1" => "音频模式", "2" => "图片模式"));
            foreach ($TrainhourList as &$val) {
                $val['trainhour_class_name'] = $status[$val['trainhour_class']];
                if($val['trainhour_class'] == '2'){
                    $url = $this->DataControl->selectClear("SELECT pptpage_imgurl as url FROM eas_course_trainhour_pptpage WHERE trainhour_id = '{$val['trainhour_id']}'");
                    $val['trainhour_data'] = $val['trainhour_coverimg'];
                    $val['img_list'] = json_encode($url);
                }else{
                    $val['trainhour_data'] = $val['trainhour_fileurl'];
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(t.trainhour_id) as num FROM eas_course_trainhour as t WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('trainhour_id', 'trainhour_name', 'trainhour_class_name', 'trainhour_data');
        $fieldname = $this->LgArraySwitch(array('章节内容ID', '章节内容名称', '教案模式', '培训内容'));
        $fieldcustom = array("0", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1");
        $fieldiisswitch = array("0", "0", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $chapter = $this->DataControl->getFieldOne("eas_course_chapter","chapter_name","chapter_id = '{$paramArray['chapter_id']}'");

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;
        $result['chapter_name'] = $chapter['chapter_name'];

        if ($TrainhourList) {
            $result['list'] = $TrainhourList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无章节内容", 'result' => $result);
        }

        return $res;
    }

    //添加章节内容
    function addTrainingDataApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course_trainhour","chapter_id='{$paramArray['chapter_id']}' and trainhour_name='{$paramArray['trainhour_name']}' and trainhour_class='{$paramArray['trainhour_class']}'")){
            $res = array('error' => '1', 'errortip' => '章节内容重复');
            return $res;
        }

        $data = array();
        $data['chapter_id'] = $paramArray['chapter_id'];
        $data['trainhour_name'] = $paramArray['trainhour_name'];
        $data['trainhour_class'] = $paramArray['trainhour_class'];
        $data['trainhour_coverimg'] = $paramArray['trainhour_coverimg'];
        if($paramArray['trainhour_class'] == '0' || $paramArray['trainhour_class'] == '1'){
            $data['trainhour_fileurl'] = $paramArray['trainhour_fileurl'];
        }
        $data['trainhour_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_course_trainhour", $data);
        if ($dataid) {
            if($paramArray['trainhour_class'] == '2'){
                $img_list = json_decode(stripslashes($paramArray['img_list']),true);
                foreach($img_list as $k => $v){
                    $list = array();
                    $list['trainhour_id'] = $dataid;
                    $list['pptpage_name'] = $v['img_name'];
                    $list['pptpage_imgurl'] = $v['url'];
                    $list['pptpage_thumburl'] = $v['img_thumburl'];
                    $list['pptpage_sort'] = $k+1;
                    $list['pptpage_time'] = time();
                    $this->DataControl->insertData("eas_course_trainhour_pptpage", $list);
                }
            }

            $res = array('error' => '0', 'errortip' => "添加章节内容成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加章节内容", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加章节内容失败");
        }

        return $res;
    }

    //编辑章节内容
    function editTrainingDataApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course_trainhour","chapter_id='{$paramArray['chapter_id']}' and trainhour_name='{$paramArray['trainhour_name']}' and trainhour_class='{$paramArray['trainhour_class']}' and trainhour_id<>'{$paramArray['trainhour_id']}'")){
            $res = array('error' => '1', 'errortip' => '章节内容重复');
            return $res;
        }

        $data = array();
        $data['trainhour_name'] = $paramArray['trainhour_name'];
        $data['trainhour_class'] = $paramArray['trainhour_class'];
        $data['trainhour_coverimg'] = $paramArray['trainhour_coverimg'];
        if($paramArray['trainhour_class'] == '0' || $paramArray['trainhour_class'] == '1'){
            $data['trainhour_fileurl'] = $paramArray['trainhour_fileurl'];
        }
        $data['trainhour_updatatime'] = time();
        if ($this->DataControl->updateData("eas_course_trainhour", "trainhour_id = '{$paramArray['trainhour_id']}'", $data)) {
            if($paramArray['trainhour_class'] == '2'){
                $this->DataControl->delData("eas_course_trainhour_pptpage", "trainhour_id='{$paramArray['trainhour_id']}'");
                $img_list = json_decode(stripslashes($paramArray['img_list']),true);
                foreach($img_list as $k => $v){
                    $list = array();
                    $list['trainhour_id'] = $paramArray['trainhour_id'];
                    $list['pptpage_name'] = $v['img_name'];
                    $list['pptpage_imgurl'] = $v['url'];
                    $list['pptpage_thumburl'] = $v['img_thumburl'];
                    $list['pptpage_sort'] = $k+1;
                    $list['pptpage_time'] = time();
                    $this->DataControl->insertData("eas_course_trainhour_pptpage", $list);
                }
            }

            $res = array('error' => '0', 'errortip' => "编辑章节内容成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑章节内容", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑章节内容失败");
        }

        return $res;
    }

    //删除章节内容
    function delTrainingDataApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_course_trainhour","trainhour_id='{$paramArray['trainhour_id']}'")){
            $res = array('error' => '1', 'errortip' => '章节内容不存在');
            return $res;
        }

        if ($this->DataControl->delData("eas_course_trainhour","trainhour_id='{$paramArray['trainhour_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除章节内容成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除章节内容", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除章节内容失败");
        }

        return $res;
    }

    //职业类型设置 -- 职业课
    function TrainingTypeList($paramArray){
        $datawhere = " t.company_id='{$paramArray['company_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.careertype_cnname like '%{$paramArray['keyword']}%' or t.careertype_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    t.careertype_id,t.careertype_cnname,t.careertype_enname,t.careertype_branch
                FROM
                    eas_code_careertype as t
                WHERE
                    {$datawhere}
                ORDER BY
                    t.careertype_id DESC
                LIMIT
                    {$pagestart},{$num}";
        $CareertypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(t.careertype_id) as num FROM eas_code_careertype as t WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('careertype_id', 'careertype_cnname', 'careertype_branch');
        $fieldname = $this->LgArraySwitch(array('职业类型ID', '职业类型名称', '职业类型编号'));
        $fieldcustom = array("0", "1", "1");
        $fieldshow = array("0", "1", "1");
        $fieldiisswitch = array("0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CareertypeList) {
            $result['list'] = $CareertypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职业类型", 'result' => $result);
        }

        return $res;
    }

    //添加职业类型 -- 职业课
    function addTrainingTypeApi($paramArray)
    {
        if($this->DataControl->getOne("eas_code_careertype","company_id='{$paramArray['company_id']}' and careertype_branch='{$paramArray['careertype_branch']}'")){
            $res = array('error' => '1', 'errortip' => '职业类型编号重复');
            return $res;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['careertype_cnname'] = $paramArray['careertype_cnname'];
        $data['careertype_enname'] = $paramArray['careertype_enname'];
        $data['careertype_branch'] = $paramArray['careertype_branch'];
        if ($this->DataControl->insertData("eas_code_careertype", $data)) {
            $res = array('error' => '0', 'errortip' => "添加职业类型成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加职业类型", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加职业类型失败");
        }

        return $res;
    }

    //编辑职业类型 -- 职业课
    function editTrainingTypeApi($paramArray)
    {
        if($this->DataControl->getOne("eas_code_careertype","company_id='{$paramArray['company_id']}' and careertype_branch='{$paramArray['careertype_branch']}' and careertype_id<>'{$paramArray['careertype_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业类型编号重复');
            return $res;
        }

        $data = array();
        $data['careertype_cnname'] = $paramArray['careertype_cnname'];
        $data['careertype_enname'] = $paramArray['careertype_enname'];
        $data['careertype_branch'] = $paramArray['careertype_branch'];
        if ($this->DataControl->updateData("eas_code_careertype", "careertype_id = '{$paramArray['careertype_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑职业类型成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑职业类型", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑职业类型失败");
        }

        return $res;
    }

    //删除职业类型 -- 职业课
    function delTrainingTypeApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career","careertype_id='{$paramArray['careertype_id']}'")){
            $res = array('error' => '1', 'errortip' => '已使用的职业类型，不可删除');
            return $res;
        }

        if ($this->DataControl->delData("eas_code_careertype","careertype_id='{$paramArray['careertype_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除职业类型成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除职业类型", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除职业类型失败");
        }

        return $res;
    }

    //培训类型设置 -- 通识课
    function TrainingTypeTwoList($paramArray){
        $datawhere = " t.company_id='{$paramArray['company_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.openclasstype_cnname like '%{$paramArray['keyword']}%' or t.openclasstype_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    t.openclasstype_id,t.openclasstype_cnname,t.openclasstype_enname,t.openclasstype_branch
                FROM
                    eas_code_openclasstype as t
                WHERE
                    {$datawhere}
                ORDER BY
                    t.openclasstype_id DESC
                LIMIT
                    {$pagestart},{$num}";
        $CareertypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(t.openclasstype_id) as num FROM eas_code_openclasstype as t WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('openclasstype_id', 'openclasstype_cnname', 'openclasstype_branch');
        $fieldname = $this->LgArraySwitch(array('通识课类型ID', '通识课类型名称', '通识课类型编号'));
        $fieldcustom = array("0", "1", "1");
        $fieldshow = array("0", "1", "1");
        $fieldiisswitch = array("0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CareertypeList) {
            $result['list'] = $CareertypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无通识课类型", 'result' => $result);
        }

        return $res;
    }

    //添加培训类型 -- 通识课
    function addTrainingTypeTwoApi($paramArray)
    {
        if($this->DataControl->getOne("eas_code_openclasstype","company_id='{$paramArray['company_id']}' and openclasstype_branch='{$paramArray['openclasstype_branch']}'")){
            $res = array('error' => '1', 'errortip' => '通识课类型编号重复');
            return $res;
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['openclasstype_cnname'] = $paramArray['openclasstype_cnname'];
        $data['openclasstype_enname'] = $paramArray['openclasstype_enname'];
        $data['openclasstype_branch'] = $paramArray['openclasstype_branch'];
        if ($this->DataControl->insertData("eas_code_openclasstype", $data)) {
            $res = array('error' => '0', 'errortip' => "添加通识课类型成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加通识课类型", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加通识课类型失败");
        }

        return $res;
    }

    //编辑培训类型 -- 通识课
    function editTrainingTypeTwoApi($paramArray)
    {
        if($this->DataControl->getOne("eas_code_openclasstype","company_id='{$paramArray['company_id']}' and openclasstype_branch='{$paramArray['openclasstype_branch']}' and openclasstype_id<>'{$paramArray['openclasstype_id']}'")){
            $res = array('error' => '1', 'errortip' => '通识课类型编号重复');
            return $res;
        }

        $data = array();
        $data['openclasstype_cnname'] = $paramArray['openclasstype_cnname'];
        $data['openclasstype_enname'] = $paramArray['openclasstype_enname'];
        $data['openclasstype_branch'] = $paramArray['openclasstype_branch'];
        if ($this->DataControl->updateData("eas_code_openclasstype", "openclasstype_id = '{$paramArray['openclasstype_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑通识课类型成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑通识课类型", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑通识课类型失败");
        }

        return $res;
    }

    //删除培训类型 -- 通识课
    function delTrainingTypeTwoApi($paramArray)
    {
        if($this->DataControl->getOne("eas_course","openclasstype_id='{$paramArray['openclasstype_id']}'")){
            $res = array('error' => '1', 'errortip' => '已使用的通识课类型，不可删除');
            return $res;
        }

        if ($this->DataControl->delData("eas_code_openclasstype","openclasstype_id='{$paramArray['openclasstype_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除通识课类型成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除通识课类型", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除通识课类型失败");
        }

        return $res;
    }

    //职业试题设置
    function GetQuestionList($paramArray){
        $datawhere = " s.company_id = '{$paramArray['company_id']}' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (q.question_pid like '%{$paramArray['keyword']}%' or q.question_title like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['stage_id']) && $paramArray['stage_id'] !== '') {
            $datawhere .= " and q.stage_id = '{$paramArray['stage_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    q.question_id,q.question_pid,q.question_title,q.question_correct,q.stage_id,q.question_analysis,s.stage_cnname,
                    (SELECT COUNT(an.answers_id) FROM eas_career_answers as an WHERE an.question_id = q.question_id) as question_optionnum
                FROM
                    eas_career_question as q
                LEFT JOIN
                    eas_career_stage as s ON s.stage_id = q.stage_id
                WHERE
                    {$datawhere}
                ORDER BY
                    q.question_pid ASC
                LIMIT
                    {$pagestart},{$num}";
        $CareertypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(q.question_id) as num FROM eas_career_question as q LEFT JOIN eas_career_stage as s ON s.stage_id = q.stage_id WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('question_id', 'stage_cnname',  'question_pid', 'question_title', 'question_correct', 'question_optionnum');
        $fieldname = $this->LgArraySwitch(array('试题ID', '所属职业阶段', '试题编号', '试题标题', '正确答案', '选项数量'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CareertypeList) {
            $result['list'] = $CareertypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职业试题", 'result' => $result);
        }

        return $res;
    }

    //添加职业试题
    function addQuestionApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career_question","stage_id = '{$paramArray['stage_id']}' and stage_id = '{$paramArray['stage_id']}' and question_pid='{$paramArray['question_pid']}'")){
            $res = array('error' => '1', 'errortip' => '职业试题编号重复');
            return $res;
        }

        $data = array();
        $data['question_pid'] = $paramArray['question_pid'];
        $data['stage_id'] = $paramArray['stage_id'];
        $data['question_title'] = $paramArray['question_title'];
        $data['question_content'] = $paramArray['question_content'];
        $data['question_time'] = $paramArray['question_time'];
        $data['question_correct'] = $paramArray['question_correct'];
        $data['question_analysis'] = $paramArray['question_analysis'];
        $data['question_issoldout'] = $paramArray['question_issoldout'];
        $data['question_createtime'] = time();
        $dataid = $this->DataControl->insertData("eas_career_question", $data);
        if ($dataid) {
            $option_list = json_decode(stripslashes($paramArray['option_list']), true);
            if($option_list){
                foreach($option_list as &$v){
                    $v['question_id'] = $dataid;
                    $this->addQuestionOptionApi($v);
                }
            }
            $res = array('error' => '0', 'errortip' => "添加职业试题成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加职业试题", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加职业试题失败");
        }

        return $res;
    }

    //编辑职业试题
    function editQuestionApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career_question","question_pid='{$paramArray['question_pid']}' and stage_id = '{$paramArray['stage_id']}' and question_id<>'{$paramArray['question_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业试题编号重复');
            return $res;
        }

        $data = array();
        $data['question_pid'] = $paramArray['question_pid'];
        $data['stage_id'] = $paramArray['stage_id'];
        $data['question_title'] = $paramArray['question_title'];
        $data['question_content'] = $paramArray['question_content'];
        $data['question_time'] = $paramArray['question_time'];
        $data['question_correct'] = $paramArray['question_correct'];
        $data['question_analysis'] = $paramArray['question_analysis'];
        $data['question_issoldout'] = $paramArray['question_issoldout'];
        $data['question_updatatime'] = time();
        if ($this->DataControl->updateData("eas_career_question", "question_id = '{$paramArray['question_id']}'", $data)) {
            $option_list = json_decode(stripslashes($paramArray['option_list']), true);
            if($option_list){
                $option = array_column($option_list, 'answers_option');
                $answers = $this->DataControl->selectClear("SELECT answers_option FROM eas_career_answers WHERE question_id = '{$paramArray['question_id']}'");
                $answer = array_column($answers, 'answers_option');
                $arr = array_intersect($answer, $option);
                if(count($arr) !== count($answer)){
                    $this->DataControl->delData("eas_career_answers","question_id = '{$paramArray['question_id']}'");
                    foreach($option_list as &$v){
                        $v['question_id'] = $paramArray['question_id'];
                        $this->addQuestionOptionApi($v);
                    }
                }
            }
            $res = array('error' => '0', 'errortip' => "编辑职业试题成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑职业试题", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑职业试题失败");
        }

        return $res;
    }

    //删除职业试题
    function delQuestionApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_career_question","question_id='{$paramArray['question_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业试题不存在');
            return $res;
        }

        if ($this->DataControl->delData("eas_career_question","question_id='{$paramArray['question_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除职业试题成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除职业试题", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除职业试题失败");
        }

        return $res;
    }


    //适配题目
    function addStageQuestion($paramArray)
    {
        if($this->DataControl->getOne("eas_stage_question","stage_id='{$paramArray['stage_id']}' and question_id='{$paramArray['question_id']}'")){
            $res = array('error' => '1', 'errortip' => '题目已适配');
            return $res;
        }

        $data = array();
        $data['course_id'] = $paramArray['course_id'];
        $data['career_id'] = $paramArray['career_id'];
        if ($this->DataControl->insertData("eas_stage_question", $data)) {
            $res = array('error' => '0', 'errortip' => "适配题目成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '适配题目', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '适配题目失败');
        }

        return $res;
    }

    //取消适配题目
    function delStageQuestion($paramArray)
    {
        if(!$this->DataControl->getOne("eas_stage_question","stage_id='{$paramArray['stage_id']}' and question_id='{$paramArray['question_id']}'")){
            $res = array('error' => '1', 'errortip' => '题目未适配');
            return $res;
        }

        if ($this->DataControl->delData("eas_stage_question", "career_id='{$paramArray['career_id']}' and course_id='{$paramArray['course_id']}'")) {
            $res = array('error' => '0', 'errortip' => "取消适配题目成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", '取消适配题目', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '取消适配题目失败');
        }

        return $res;
    }

    //职业试题选项列表
    function QuestionOptionList($paramArray){
        $datawhere = " a.question_id = '{$paramArray['question_id']}' ";

        $sql = "SELECT
                    a.answers_id,a.answers_optionname,a.answers_option
                FROM
                    eas_career_answers as a
                WHERE
                    {$datawhere}";
        $CareertypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(a.answers_id) as num FROM eas_career_answers as a WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('answers_id', 'answers_optionname', 'answers_option');
        $fieldname = $this->LgArraySwitch(array('选项ID', '选项名称', '选项内容'));
        $fieldcustom = array("0", "1", "1");
        $fieldshow = array("0", "1", "1");
        $fieldiisswitch = array("0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldiisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CareertypeList) {
            $result['list'] = $CareertypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无职业试题选项", 'result' => $result);
        }

        return $res;
    }

    //添加职业试题选项
    function addQuestionOptionApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career_answers","question_id='{$paramArray['question_id']}' and answers_optionname='{$paramArray['answers_optionname']}'")){
            $res = array('error' => '1', 'errortip' => '职业试题选项名称重复');
            return $res;
        }

        $data = array();
        $data['question_id'] = $paramArray['question_id'];
        $data['answers_optionname'] = $paramArray['answers_optionname'];
        $data['answers_option'] = $paramArray['answers_option'];
        $data['answers_sort'] = $paramArray['answers_sort'];
        if ($this->DataControl->insertData("eas_career_answers", $data)) {
            $res = array('error' => '0', 'errortip' => "添加职业试题选项成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "添加职业试题选项", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "添加职业试题选项失败");
        }

        return $res;
    }

    //编辑职业试题选项
    function editQuestionOptionApi($paramArray)
    {
        if($this->DataControl->getOne("eas_career_answers","question_id='{$paramArray['question_id']}' and answers_optionname='{$paramArray['answers_optionname']}' and answers_id<>'{$paramArray['answers_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业试题选项名称重复');
            return $res;
        }

        $data = array();
        $data['answers_optionname'] = $paramArray['answers_optionname'];
        $data['answers_option'] = $paramArray['answers_option'];
        $data['answers_sort'] = $paramArray['answers_sort'];
        if ($this->DataControl->updateData("eas_career_answers", "answers_id = '{$paramArray['answers_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => "编辑职业试题选项成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "编辑职业试题选项", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "编辑职业试题选项失败");
        }

        return $res;
    }

    //删除职业试题选项
    function delQuestionOptionApi($paramArray)
    {
        if(!$this->DataControl->getOne("eas_career_answers","answers_id='{$paramArray['answers_id']}'")){
            $res = array('error' => '1', 'errortip' => '职业试题选项不存在');
            return $res;
        }

        if ($this->DataControl->delData("eas_career_answers","answers_id='{$paramArray['answers_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除职业试题选项成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->培训相关设置", "删除职业试题选项", dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "删除职业试题选项失败");
        }

        return $res;
    }

}
