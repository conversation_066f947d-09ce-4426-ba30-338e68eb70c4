<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  TradeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //校园资金交易记录
    function getSchoolTradeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_branch like '%{$paramArray['keyword']}%' or s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or c.companies_cnname like '%{$paramArray['keyword']}%' or cc.companies_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and (t.to_school_id ='{$paramArray['school_id']}' or t.from_school_id ='{$paramArray['school_id']}')";
        }
        if (isset($paramArray['trading_type']) && $paramArray['trading_type'] !== "") {
            $datawhere .= " and t.trading_type ='{$paramArray['trading_type']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== '') {
            $paramArray['start_time'] = strtotime($paramArray['start_time']);
            $datawhere .= " and t.trading_createtime >= '{$paramArray['start_time']}'";
        }

        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== '') {
            $paramArray['end_time'] = strtotime(($paramArray['end_time'] . ' 23:59:59'));
            $datawhere .= " and t.trading_createtime <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
        }

        if ($paramArray['dataequity'] == '1') {
            $sql = "
            SELECT
                s.student_branch,
                s.student_cnname,
                s.student_enname,
                (select c.companies_cnname from gmc_code_companies as c WHERE c.companies_id = t.to_companies_id) as to_Company,
                (select c.companies_cnname from gmc_code_companies as c WHERE c.companies_id = t.from_companies_id) as from_Company,
                (select (case when l.school_shortname='' then l.school_cnname else l.school_shortname end) as school_cnname from smc_school as l WHERE l.school_id = t.to_school_id) as to_school,
                (select (case when l.school_shortname='' then l.school_cnname else l.school_shortname end) as school_cnname from smc_school as l WHERE l.school_id = t.from_school_id) as from_school,
                t.trading_type,t.trading_price,t.trading_balance,t.trading_withholdbalance,t.trading_note,t.trading_createtime
            FROM
                smc_school_trading AS t
                LEFT JOIN smc_student AS s ON s.student_id = t.student_id
		        LEFT JOIN gmc_code_companies AS c ON c.companies_id = t.to_companies_id
		        LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = t.from_companies_id
            WHERE {$datawhere} AND t.company_id = '{$paramArray['company_id']}'
            ORDER BY
                t.trading_id DESC    
            LIMIT {$pagestart},{$num}";
        } else {
            $sql = "
            SELECT
                s.student_branch,
                s.student_cnname,
                s.student_enname,
                (select c.companies_cnname from gmc_code_companies as c WHERE c.companies_id = t.to_companies_id) as to_Company,
                (select c.companies_cnname from gmc_code_companies as c WHERE c.companies_id = t.from_companies_id) as from_Company,
                (select (case when l.school_shortname='' then l.school_cnname else l.school_shortname end) as school_cnname from smc_school as l WHERE l.school_id = t.to_school_id) as to_school,
                (select (case when l.school_shortname='' then l.school_cnname else l.school_shortname end) as school_cnname from smc_school as l WHERE l.school_id = t.from_school_id) as from_school,
                t.trading_type,t.trading_price,t.trading_balance,t.trading_withholdbalance,t.trading_note,t.trading_createtime
            FROM
                smc_school_trading AS t
                LEFT JOIN smc_student AS s ON s.student_id = t.student_id
		        LEFT JOIN gmc_code_companies AS c ON c.companies_id = t.to_companies_id
		        LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = t.from_companies_id
            WHERE {$datawhere} AND t.company_id = '{$paramArray['company_id']}' and (t.from_school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') or t.to_school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}'))
            ORDER BY
                t.trading_id DESC    
            LIMIT {$pagestart},{$num}";
        }

        $tradeList = $this->DataControl->selectClear($sql);

        if ($tradeList) {
            $status = $this->LgArraySwitch(array("0" => "转校结转", "1" => "跨校转移"));
            foreach ($tradeList as &$val) {
                $val['trading_type'] = $status[$val['trading_type']];
                $val['trading_createtime'] = date("Y-m-d H:i:s", $val['trading_createtime']);
            }

        }
        if ($paramArray['dataequity'] == '1') {
            $all_num = $this->DataControl->select("
            SELECT
               COUNT(t.trading_id)
            FROM
                smc_school_trading AS t
                LEFT JOIN smc_student AS s ON s.student_id = t.student_id
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = t.to_companies_id
		        LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = t.from_companies_id
            WHERE
                {$datawhere} AND t.company_id = '{$paramArray['company_id']}'");
        } else {
            $all_num = $this->DataControl->select("
            SELECT
               COUNT(t.trading_id)
            FROM
                smc_school_trading AS t
                LEFT JOIN smc_student AS s ON s.student_id = t.student_id
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = t.to_companies_id
		        LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = t.from_companies_id
            WHERE
                {$datawhere} AND t.company_id = '{$paramArray['company_id']}' and (t.from_school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}') or t.to_school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}'))");
        }


        $allnums = $all_num[0][0];

        $fieldstring = array('student_branch ', 'student_cnname', 'student_enname', 'from_Company', 'from_school', 'to_Company', 'to_school', 'trading_type', 'trading_price', 'trading_balance', 'trading_withholdbalance', 'trading_note', 'trading_createtime');
        $fieldname = $this->LgArraySwitch(array('学员编号', '学员中文名', '学员英文名', '来源主体', '来源学校', '去向主体', '去向学校', '交易类型', '账户结转金额', '账户可退余额', '账户不可退余额', '交易备注', '交易时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($tradeList) {
            $result['list'] = $tradeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校园资金交易", 'result' => $result);
        }

        return $res;
    }

    function getCompaniesList($paramArray)
    {
        if ($paramArray && $paramArray['companies_issupervise'] == 1) {
            $sql = "select companies_id,companies_cnname,companies_branch,companies_agencyid from gmc_code_companies where company_id='{$this->company_id}' and companies_issupervise='1' and companies_agencyid<>''";
        } else {
            $sql = "select companies_id,companies_cnname,companies_branch from gmc_code_companies where company_id='{$this->company_id}'";
        }
        $list = $this->DataControl->selectClear($sql);

        if (!$list) {
            $this->error = true;
            $this->errortip = "无主体信息";
            return false;
        }
        return $list;
    }

//主体交易明细管理
    function getMainTradeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.companies_cnname like '%{$paramArray['keyword']}%' or cc.companies_cnname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%' or st.student_cnname like '%{$paramArray['keyword']}%' or t.trading_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['trading_status']) && $paramArray['trading_status'] !== "") {
            $datawhere .= " and t.trading_status ='{$paramArray['trading_status']}'";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and (t.from_school_id ='{$paramArray['school_id']}' or t.to_school_id='{$paramArray['school_id']}')";
        }

        if (isset($paramArray['from_school_id']) && $paramArray['from_school_id'] !== "") {
            $datawhere .= " and t.from_school_id ='{$paramArray['from_school_id']}'";
        }

        if (isset($paramArray['to_school_id']) && $paramArray['to_school_id'] !== "") {
            $datawhere .= " and t.to_school_id ='{$paramArray['to_school_id']}'";
        }

        if (isset($paramArray['from_Company']) && $paramArray['from_Company'] !== "") {
            $datawhere .= " and t.from_companies_id ='{$paramArray['from_Company']}'";
        }

        if (isset($paramArray['to_Company']) && $paramArray['to_Company'] !== "") {
            $datawhere .= " and t.to_companies_id ='{$paramArray['to_Company']}'";
        }

        if (isset($paramArray['type']) && $paramArray['type'] !== "") {
            if ($paramArray['type'] == 0) {
                $datawhere .= " and t.to_school_id = t.from_school_id";
            } elseif ($paramArray['type'] == 1) {
                $datawhere .= " and t.to_school_id <> t.from_school_id";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                t.trading_id,
                t.trading_pid,st.student_branch,st.student_cnname,st.student_enname,
                t.trading_price,t.trading_balance,t.trading_withholdbalance,
                c.companies_cnname as to_Company,
                cc.companies_cnname as from_Company,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as to_school_name,
                (case when ss.school_shortname='' then ss.school_cnname else ss.school_shortname end) as from_school_name,
                t.trading_status,
                t.trading_createtime,
                t.trading_updatatime
            FROM
                smc_school_trading AS t
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = t.to_companies_id
		        LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = t.from_companies_id
		        left join smc_student as st on st.student_id=t.student_id
                LEFT JOIN smc_school AS s ON s.school_id = t.to_school_id
		        LEFT JOIN smc_school AS ss ON ss.school_id = t.from_school_id
            WHERE {$datawhere} and  t.company_id = '{$paramArray['company_id']}'
            ORDER BY
                t.trading_id DESC  ";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "报表内容为空,不可导出";
                return false;
            }

            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "处理中", "2" => "已完成", "-1" => "已取消"));
            foreach ($dateexcelarray as &$val) {
                $val['trading_status_name'] = $status[$val['trading_status']];

                if ($val['trading_createtime'] == 0) {
                    $val['trading_createtime'] = '--';
                } else {
                    $val['trading_createtime'] = date("Y-m-d H:i:s", $val['trading_createtime']);
                }
                if ($val['trading_updatatime'] == 0) {
                    $val['trading_updatatime'] = '--';
                } else {
                    $val['trading_updatatime'] = date("Y-m-d H:i:s", $val['trading_updatatime']);
                }
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];//
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];//
                    $datearray['from_Company'] = $dateexcelvar['from_Company'];//
                    $datearray['from_school_name'] = $dateexcelvar['from_school_name'];//
                    $datearray['to_Company'] = $dateexcelvar['to_Company'];//
                    $datearray['to_school_name'] = $dateexcelvar['to_school_name'];//
                    $datearray['trading_status_name'] = $dateexcelvar['trading_status_name'];//
                    $datearray['trading_price'] = $dateexcelvar['trading_price'];//
                    $datearray['trading_balance'] = $dateexcelvar['trading_balance'];//
                    $datearray['trading_withholdbalance'] = $dateexcelvar['trading_withholdbalance'];//
                    $datearray['trading_createtime'] = $dateexcelvar['trading_createtime'];//
                    $datearray['trading_updatatime'] = $dateexcelvar['trading_updatatime'];//
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('交易编号', '学员中文名', '学员编号', '学员英文名', '来源主体', '来源学校', '去向主体', '去向学校', '审核状态', '交易金额', '账户可退余额', '账户不可退余额', '操作时间', '审核时间'));
            $excelfileds = array('trading_pid', 'student_cnname', 'student_branch', 'student_enname', 'from_Company', 'from_school_name', 'to_Company', 'to_school_name', 'trading_status_name', 'trading_price', 'trading_balance', 'trading_withholdbalance', 'trading_createtime', 'trading_updatatime');

            $fielname = $this->LgStringSwitch("主体交易明细管理");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $tradeList = $this->DataControl->selectClear($sql);
        }


        if ($tradeList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "处理中", "2" => "已完成", "-1" => "已取消"));
            foreach ($tradeList as &$val) {
                $val['trading_status_name'] = $status[$val['trading_status']];

                if ($val['trading_createtime'] == 0) {
                    $val['trading_createtime'] = '--';
                } else {
                    $val['trading_createtime'] = date("Y-m-d H:i:s", $val['trading_createtime']);
                }
                if ($val['trading_updatatime'] == 0) {
                    $val['trading_updatatime'] = '--';
                } else {
                    $val['trading_updatatime'] = date("Y-m-d H:i:s", $val['trading_updatatime']);
                }
            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               t.trading_id
            FROM
                smc_school_trading AS t
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = t.to_companies_id
		        LEFT JOIN gmc_code_companies AS cc ON cc.companies_id = t.from_companies_id
		        left join smc_student as st on st.student_id=t.student_id
            WHERE
                {$datawhere} AND t.company_id = '{$paramArray['company_id']}'");
        if ($all_num) {
            $allnums = count($all_num);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('trading_pid ', 'student_cnname', 'student_branch', 'student_enname', 'from_Company', 'from_school_name', 'to_Company', 'to_school_name', 'trading_status_name', 'trading_price', 'trading_balance', 'trading_withholdbalance', 'trading_createtime', 'trading_updatatime', 'trading_status');
        $fieldname = $this->LgArraySwitch(array('交易编号', '学员中文名', '学员编号', '学员英文名', '来源主体', '来源学校', '去向主体', '去向学校', '审核状态', '交易金额', '账户可退余额', '账户不可退余额', '操作时间', '审核时间', '审核状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;
        $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}' order by (case when school_istest=0 and school_isclose=0 then 1 when school_isclose=0 then 2 when school_istest=0 then 3 else 4 end),school_istest asc,field(school_sort,0),school_sort asc,school_createtime asc");

        if ($tradeList) {
            $result['list'] = $tradeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无主体交易明细", 'result' => $result);
        }

        return $res;
    }

    function getSchoolTradeOne($request)
    {

        $sql = "select s.student_id,s.student_cnname,s.student_branch,st.trading_price,st.trading_balance,st.trading_withholdbalance,sc1.school_branch as from_school_branch,(case when sc1.school_shortname='' then sc1.school_cnname else sc1.school_shortname end) as from_school_cnname,sc2.school_branch as to_school_branch,(case when sc2.school_shortname='' then sc2.school_cnname else sc2.school_shortname end) as to_school_cnname,cc1.companies_cnname as from_companies_cnname,cc2.companies_cnname as to_companies_cnname,st.trading_note,FROM_UNIXTIME(st.trading_createtime, '%Y-%m-%d') AS trading_createtime,st.trading_status,s.student_sex,s.student_img,s.student_enname,st.trading_note
              from smc_school_trading as st
              left join smc_student as s on s.student_id=st.student_id
              left join smc_school as sc1 on sc1.school_id=st.from_school_id
              left join smc_school as sc2 on sc2.school_id=st.to_school_id
              left join gmc_code_companies as cc1 on cc1.companies_id=st.from_companies_id
              left join gmc_code_companies as cc2 on cc2.companies_id=st.to_companies_id
              where st.trading_pid='{$request['trading_pid']}' and st.company_id='{$request['company_id']}'
              ";
        $tradeOne = $this->DataControl->selectOne($sql);

        if (!$tradeOne) {
            $this->error = true;
            $this->errortip = "不存在对应资金交易";
            return false;
        }

        $status = $this->LgArraySwitch(array('0' => "待审核", '1' => "处理中", '2' => "已完成", '-1' => "已取消"));
        $tradeOne['trading_status_name'] = $status[$tradeOne['trading_status']];

        $sql = "select t.tracks_title,t.tracks_information,t.tracks_playname,FROM_UNIXTIME(t.tracks_time, '%Y-%m-%d') AS tracks_time,st.staffer_cnname,st.staffer_enname,t.tracks_note
              from smc_school_trading_tracks as t
              left join smc_staffer as st on st.staffer_id=t.staffer_id
              where t.trading_pid='{$request['trading_pid']}'
              order by tracks_id asc
              ";

        $tracksList = $this->DataControl->selectClear($sql);

        if ($tracksList) {
            foreach ($tracksList as &$tracksOne) {
                $tracksOne['tracks_playname'] = $tracksOne['staffer_enname'] ? $tracksOne['staffer_cnname'] . '-' . $tracksOne['staffer_enname'] : $tracksOne['staffer_cnname'];
            }
        }


        $tradeOne['track'] = $tracksList;
        return $tradeOne;
    }

    function examineSchoolTrade($request)
    {
        $tradeOne = $this->DataControl->getOne("smc_school_trading", "trading_pid='{$request['trading_pid']}' and company_id='{$request['company_id']}'");
        if (!$tradeOne) {
            $this->error = true;
            $this->errortip = "不存在对应资金交易";
            return false;
        }
        $TransactionModel = new \Model\Smc\TransactionModel($request);
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        if ($tradeOne['trading_status'] !== '0') {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$request['staffer_id']}'");

        if ($request['is_adopt'] == 0) {

            $Trading_pid = $TransactionModel->stuTrading($tradeOne['student_id'], $tradeOne['from_school_id'], 'TransferIn', $tradeOne['from_companies_id'], strtotime($request['create_time']));

            $BalanceModel->addStuAllBalance($tradeOne['student_id'], $tradeOne['from_school_id'], $Trading_pid, $tradeOne['trading_balance'], $tradeOne['trading_withholdbalance'], $tradeOne['from_companies_id'], $this->LgStringSwitch('资产转移'), $this->LgStringSwitch("余额转移失败,备注:") . $request['note'], strtotime($request['create_time']));

            $data = array();
            $data['trading_pid'] = $tradeOne['trading_pid'];
            $data['tracks_title'] = $this->LgStringSwitch('审核订单');
            $data['tracks_information'] = $this->LgStringSwitch('订单审核拒绝，请查看拒绝原因');
            $data['tracks_note'] = $request['note'];
            $data['staffer_id'] = $request['staffer_id'];
            $data['tracks_playname'] = $stafferOne['staffer_cnname'];
            $data['tracks_time'] = $request['create_time'] ? strtotime($request['create_time']) : time();
            $this->DataControl->insertData("smc_school_trading_tracks", $data);

            $data = array();
            $data['trading_status'] = '-1';
            $data['trading_note'] = $request['note'];
            $data['trading_topid'] = $Trading_pid;
            $data['trading_cancelreason'] = $request['note'];
            $data['trading_updatatime'] = $request['create_time'] ? strtotime($request['create_time']) : time();

            $this->DataControl->updateData("smc_school_trading", "trading_pid='{$tradeOne['trading_pid']}'", $data);

            return true;
        } elseif ($request['is_adopt'] == 1) {

            $Trading_pid = $TransactionModel->stuTrading($tradeOne['student_id'], $tradeOne['to_school_id'], 'TransferIn', $tradeOne['to_companies_id'], strtotime($request['create_time']));

            $BalanceModel->addStuAllBalance($tradeOne['student_id'], $tradeOne['to_school_id'], $Trading_pid, $tradeOne['trading_balance'], $tradeOne['trading_withholdbalance'], $tradeOne['to_companies_id'], $this->LgStringSwitch('资产转移'), $this->LgStringSwitch("余额转移成功,备注:") . $request['note'], strtotime($request['create_time']));

            $data = array();
            $data['trading_pid'] = $tradeOne['trading_pid'];
            $data['tracks_title'] = $this->LgStringSwitch('审核订单');
            $data['tracks_information'] = $this->LgStringSwitch('订单审核通过，订单完成');
            $data['tracks_note'] = $request['note'];
            $data['staffer_id'] = $request['staffer_id'];
            $data['tracks_playname'] = $stafferOne['staffer_cnname'];
            $data['tracks_time'] = $request['create_time'] ? strtotime($request['create_time']) : time();
            $this->DataControl->insertData("smc_school_trading_tracks", $data);

            $data = array();
            $data['trading_status'] = '2';
            $data['trading_note'] = $request['note'];
            $data['trading_topid'] = $Trading_pid;
            $data['trading_updatatime'] = $request['create_time'] ? strtotime($request['create_time']) : time();

            $this->DataControl->updateData("smc_school_trading", "trading_pid='{$tradeOne['trading_pid']}'", $data);

            return true;
        } else {
            $this->error = true;
            $this->errortip = "传值错误";
            return false;
        }
    }

//校园收入明细
    function getSchoolIncome($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$paramArray['keyword']}%' or t.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and i.school_id ='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['companies_id']) && $paramArray['companies_id'] !== "") {
            $datawhere .= " and c.companies_id ='{$paramArray['companies_id']}'";
        }

        if (isset($paramArray['income_type']) && $paramArray['income_type'] !== "") {
            $datawhere .= " and i.income_type ='{$paramArray['income_type']}'";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME(i.income_confirmtime, '%Y-%m-%d') >='{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME(i.income_confirmtime, '%Y-%m-%d') <='{$paramArray['endtime']}'";
        }


        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                i.income_id,
                i.income_note,
                c.companies_cnname,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                t.student_cnname,
                t.student_branch,
                i.income_type,
                i.income_price,
                i.income_confirmtime,
                ifnull(co.course_cnname,'--') as course_cnname,
                ifnull(co.course_branch,'--') as course_branch
            FROM
                smc_school_income AS i
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = i.companies_id
                left join smc_school as s on s.school_id = i.school_id
                left join smc_student as t on i.student_id = t.student_id
                left join smc_course as co on co.course_id=i.course_id
            WHERE {$datawhere} and  i.company_id = '{$paramArray['company_id']}'
            ORDER BY
                i.income_id DESC    
            ";

        $status = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "收入红冲"));


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无校园收入数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {

                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['income_type_name'] = $status[$dateexcelvar['income_type']];
                    $datearray['income_price'] = $dateexcelvar['income_price'];
                    $datearray['income_note'] = $dateexcelvar['income_note'] ? $dateexcelvar['income_note'] : '';
                    $datearray['income_confirmtime'] = date("Y-m-d H:i:s", $dateexcelvar['income_confirmtime']);

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('来源主体', '学校', '校区编号', '学员姓名', '学员编号', '课程名称', '课程编号', '收入类型', '收入金额', '备注', '收入时间'));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'course_cnname', 'course_branch', 'income_type_name', 'income_price', 'income_note', 'income_confirmtime');

            $tem_name = $this->LgStringSwitch('校园收入明细表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;

            $tradeList = $this->DataControl->selectClear($sql);


            if ($tradeList) {
                foreach ($tradeList as &$val) {
                    $val['income_type_name'] = $status[$val['income_type']];
                    $val['income_confirmtime'] = date("Y-m-d H:i:s", $val['income_confirmtime']);
                }

            }

            $all_num = $this->DataControl->select("
            SELECT
               COUNT(i.income_id)
            FROM
                smc_school_income AS i
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = i.companies_id
                left join smc_school as s on s.school_id = i.school_id
                left join smc_student as t on i.student_id = t.student_id
            WHERE
                {$datawhere} AND i.company_id = '{$paramArray['company_id']}'");
            $allnums = $all_num[0][0];

            $fieldstring = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'course_cnname', 'course_branch', 'income_type_name', 'income_price', 'income_note ', 'income_confirmtime');
            $fieldname = $this->LgArraySwitch(array('来源主体', '学校', '校区编号', '学员姓名', '学员编号', '课程名称', '课程编号', '收入类型', '收入金额', '备注', '收入时间'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;

            if ($tradeList) {
                $result['list'] = $tradeList;
            } else {
                $result['list'] = array();
            }

            $result['all_num'] = $allnums;

            $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}'");

            if ($result['list']) {
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => "暂无校园收入明细", 'result' => $result);
            }

            return $res;
        }
    }

    function writeOffIncome($request)
    {
        if ($request['create_time']) {
            $time = strtotime($request['create_time']);
        } else {
            $time = time();
        }


        if (!isset($request['income_id']) || $request['income_id'] == '' || $request['income_id'] == '0') {
            $this->error = true;
            $this->errortip = "收入ID必须传";
            return false;
        }

        $sql = "select * from smc_school_income where income_id='{$request['income_id']}'";
        $incomeOne = $this->DataControl->selectOne($sql);
        if (!$incomeOne['hourstudy_id'] || $incomeOne['income_type'] != '3' || $incomeOne['income_price'] <= '0') {
            $this->error = true;
            $this->errortip = "该收入不可操作1";
            return false;
        }

        $sql = "select * from smc_student_itemtimes_log where hourstudy_id='{$incomeOne['hourstudy_id']}' and log_playclass='-'";
        $logOne = $this->DataControl->selectOne($sql);
        if (!$logOne) {
            $this->error = true;
            $this->errortip = "该收入不可操作2";
            return false;
        }

        $studentItemOne = $this->DataControl->getOne("smc_student_itemtimes", "student_id='{$incomeOne['student_id']}' and course_id='{$incomeOne['course_id']}' and feeitem_id='{$logOne['feeitem_id']}' and school_id='{$incomeOne['school_id']}' order by itemtimes_createtime desc");

        if (!$studentItemOne) {
            $this->error = true;
            $this->errortip = "数据错误";
            return false;
        }

        $itemOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_class,feeitem_expendtype", "feeitem_id='{$logOne['feeitem_id']}'");
        if ($itemOne['feeitem_class'] != '0' || $itemOne['feeitem_expendtype'] != '0') {
            $this->error = true;
            $this->errortip = "只可操作考勤模式课程杂费";
            return false;
        }

        $log_data = array();
        $log_data['itemtimes_id'] = $studentItemOne['itemtimes_id'];
        $log_data['companies_id'] = $studentItemOne['companies_id'];
        $log_data['hourstudy_id'] = $incomeOne['hourstudy_id'];
        $log_data['student_id'] = $incomeOne['student_id'];
        $log_data['feeitem_id'] = $logOne['feeitem_id'];
        $log_data['log_playname'] = $this->LgStringSwitch('红冲取消学校杂费收入');
        $log_data['log_playclass'] = '+';
        $log_data['log_fromamount'] = $studentItemOne['itemtimes_figure'];
        $log_data['log_playamount'] = $incomeOne['income_price'];
        $log_data['log_finalamount'] = $studentItemOne['itemtimes_figure'] + $incomeOne['income_price'];
        $log_data['log_reason'] = $this->LgStringSwitch('红冲取消学校杂费收入');
        $log_data['log_time'] = $time;
        $this->DataControl->insertData("smc_student_itemtimes_log", $log_data);

        $data = array();
        $data['itemtimes_number'] = $studentItemOne['itemtimes_number'] + 1;
        $data['itemtimes_figure'] = $studentItemOne['itemtimes_figure'] + $incomeOne['income_price'];
        $data['itemtimes_createtime'] = $time;
        $this->DataControl->updateData("smc_student_itemtimes", "student_id='{$incomeOne['student_id']}' and course_id='{$incomeOne['course_id']}' and school_id='{$incomeOne['school_id']}' and feeitem_id='{$logOne['feeitem_id']}'", $data);

        $data = array();
        $data['income_price'] = 0;
        $data['income_type'] = "-1";
        $this->DataControl->updateData("smc_school_income", "income_id='{$request['income_id']}'", $data);

        return true;
    }

    function getIncomeTypeList($request)
    {

        $tem_array = $this->LgArraySwitch(array("0" => "耗课收益", "1" => "认缴收入", "2" => "教材收入", "3" => "杂费收入", "-1" => "收入红冲"));

        $result = array();
        foreach ($tem_array as $key => $val) {
            $data = array();
            $data['income_type'] = $key;
            $data['income_name'] = $val;
            $result[] = $data;
        }
        return $result;
    }

//校园支出明细
    function getSchoolExpend($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$paramArray['keyword']}%' or t.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and i.school_id ='{$paramArray['school_id']}'";
        }

        if (isset($paramArray['companies_id']) && $paramArray['companies_id'] !== "") {
            $datawhere .= " and i.companies_id ='{$paramArray['companies_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                i.expend_note,
                c.companies_cnname,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                t.student_cnname,
                t.student_branch,
                i.expend_type,
                i.expend_price,
                i.expend_confirmtime
            FROM
                smc_school_expend AS i
                LEFT JOIN gmc_code_companies AS c ON c.companies_id = i.companies_id
                left join smc_school as s on s.school_id = i.school_id
                left join smc_student as t on i.student_id = t.student_id
            WHERE {$datawhere} and  i.company_id = '{$paramArray['company_id']}'
            ORDER BY
                i.expend_id DESC    
            ";
        $status = $this->LgArraySwitch(array("0" => "签呈报损", "1" => "认缴支出", "2" => "坏账核销"));

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无校园支出数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['expend_type_name'] = $status[$dateexcelvar['expend_type']];
                    $datearray['expend_price'] = $dateexcelvar['expend_price'];
                    $datearray['expend_note'] = $dateexcelvar['expend_note'];
                    $datearray['expend_confirmtime'] = date("Y-m-d H:i:s", $dateexcelvar['expend_confirmtime']);
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('来源主体', '学校', '校区编号', '学员姓名', '学员编号', '支出类型', '损益金额', '备注', '支出时间'));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'expend_type_name', 'expend_price', 'expend_note', 'expend_confirmtime');

            $tem_name = $this->LgStringSwitch('校园支出明细表') . '.xlsx';
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;

        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $tradeList = $this->DataControl->selectClear($sql);

            if ($tradeList) {
                foreach ($tradeList as &$val) {
                    $val['expend_type'] = $status[$val['expend_type']];
                    $val['expend_confirmtime'] = date("Y-m-d H:i:s", $val['expend_confirmtime']);
                }

            }

            $all_num = $this->DataControl->select("
                SELECT
                   COUNT(i.expend_id)
                FROM
                    smc_school_expend AS i
                    LEFT JOIN gmc_code_companies AS c ON c.companies_id = i.companies_id
                    left join smc_school as s on s.school_id = i.school_id
                    left join smc_student as t on i.student_id = t.student_id
                WHERE
                    {$datawhere} AND i.company_id = '{$paramArray['company_id']}'");
            $allnums = $all_num[0][0];

            $fieldstring = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'expend_type', 'expend_price', 'expend_note ', 'expend_confirmtime');
            $fieldname = $this->LgArraySwitch(array('来源主体', '学校', '校区编号', '学员姓名', '学员编号', '支出类型', '损益金额', '备注', '支出时间'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;

            if ($tradeList) {
                $result['list'] = $tradeList;
            } else {
                $result['list'] = array();
            }

            $result['all_num'] = $allnums;

            $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}'");

            if ($result['list']) {
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => "暂无校园支出明细", 'result' => $result);
            }

            return $res;
        }
    }

    //审核
    function examineAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_school_trading", "trading_id", "trading_id = '{$paramArray['trading_id']}'");
        if ($productsOne) {
            $data = array();
            $data['trading_status'] = $paramArray['trading_status'];
            $data['trading_cancelreason'] = $paramArray['trading_cancelreason'];

            $field = array();
            $field['trading_status'] = "状态";
            $field['trading_cancelreason'] = "取消原因";

            if ($this->DataControl->updateData("smc_school_trading", "trading_id = '{$paramArray['trading_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "审核成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '审核失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑监管合同
    function updateSupervise($request)
    {
        //甲方
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");

        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$request['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");

        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject","companies_id","school_id = '{$request['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");

//        if ($request['staffer_id'] == '25721' && $course['coursecat_id']==133) {
//            $companies_id['companies_id']='78397';
//        }

        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'],strripos($companies['companies_permitstday'],"至")+3);
        $PartyA['companies_licensestday'] =  substr($companies['companies_licensestday'],strripos($companies['companies_licensestday'],"至")+3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if($school['school_phone']){
            $PartyA['school_phone'] = $school['school_phone'];
        }else{
            $PartyA['school_phone'] = '--';
        }
        if($companies['companies_liaison']){
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        }else{
            $PartyA['school_liaison'] = '--';
        }
        if($companies['companies_examine']){
            $PartyA['school_examine'] = $companies['companies_examine'];
        }else{
            $PartyA['school_examine'] = '--';
        }
        if($companies['companies_register']){
            $PartyA['school_register'] = $companies['companies_register'];
        }else{
            $PartyA['school_register'] = '--';
        }
        if($companies['companies_permitbranch']){
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        }else{
            $PartyA['school_permitbranch'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_licensestday']){
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        }else{
            $PartyA['school_licensestday'] = '--';
        }
        if($companies['companies_society']){
            $PartyA['school_society'] = $companies['companies_society'];
        }else{
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];

//        var_dump($PartyA['school_signet']);
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $PartyB['phone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//
//        }else{
        $PartyB['phone'] = $parenter['parenter_mobile'];

//        }


        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];

        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $guarder['guarderphone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//        }else{
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
//        }

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['parenter_id'] = $parenter['parenter_id'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['parenter_id'] = $parenter['parenter_id'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $guarder['urgentphone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//        }else{
//            $guarder['urgentphone'] = $parenter['parenter_mobile'];
//        }

//        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id,class_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

        $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        if($isprocat){
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
            if(!$protocolOne){
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                if(!$protocolOne){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$request['school_id']}'");
                    }
                }
            }
        }else{
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}' and t.coursecat_id = '0'");
            if(!$protocolOne){
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
            }
        }

        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $order['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];
        }


        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

//        if ($request['staffer_id'] == '25721'  && $course['coursecat_id']==133) {
//            if($protocol['protocol_nums']==26){
//                $priceInfo['sendprice'] = '¥' . $protocol['protocol_price']*0.64;
//                $courseInfo['protocol_nums']=$courseInfo['protocol_nums']*2;
//            }else{
//                $priceInfo['sendprice'] = '¥' . $protocol['protocol_price']*0.5;
//            }
//        }



        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.changelog_day 
FROM
	smc_student_changelog AS h
WHERE
	h.student_id = '{$protocol['student_id']}' 
	AND h.class_id = '{$pricing_id['class_id']}'
	AND h.stuchange_code = 'A02'");
        $date = $startday['changelog_day'];

        $endday = date('Y-m-d',strtotime("$date +90 day"));



        if($pricing_id['class_id'] == '0'){
            $startday['changelog_day'] = '';
            $endday = '';
        }



        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];

            $treatyArray['course_cnname'] = $request['course_cnname'];
            $treatyArray['course_classnum'] = $request['course_classnum'];
            $treatyArray['startday'] = $request['startday'];
            $treatyArray['endday'] = $request['endday'];
            $treatyArray['courseprice'] = $request['courseprice'];
            $treatyArray['bigprice'] = $this->convert_2_cn(intval($request['courseprice']));
            $treatyArray['ordercourse_unitprice'] = intval($request['courseprice']/$request['course_classnum']);




            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'],$treatyArray);
        }

//        $result = array();
//        $result['text'] = $protocolOne['treaty_protocol'];

        return $protocolOne['treaty_protocol'];
    }

    function convert_2_cn($num)
    {
        $convert_cn = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $repair_number = array('零仟零佰零拾零', '万万', '零仟', '零佰', '零拾');
        $unit_cn = array("拾", "佰", "仟", "万", "亿");
        $exp_cn = array("", "万", "亿");
        $max_len = 12;
        $len = strlen($num);
        if ($len > $max_len) {
            return 'outnumber';
        }
        $num = str_pad($num, 12, '-', STR_PAD_LEFT);
        $exp_num = array();
        $k = 0;
        for ($i = 12; $i > 0; $i--) {
            if ($i % 4 == 0) {
                $k++;
            }
            $exp_num[$k][] = substr($num, $i - 1, 1);
        }
        $str = '';
        foreach ($exp_num as $key => $nums) {
            if (array_sum($nums)) {
                $str = array_shift($exp_cn) . $str;
            }
            foreach ($nums as $nk => $nv) {
                if ($nv == '-') {
                    continue;
                }
                if ($nk == 0) {
                    $str = $convert_cn[$nv] . $str;
                } else {
                    $str = $convert_cn[$nv] . $unit_cn[$nk - 1] . $str;
                }
            }
        }
        $str = str_replace($repair_number, array('万', '亿', '-'), $str);
        $str = preg_replace("/-{2,}/", "", $str);
        $str = str_replace(array('零', '-'), array('', '零'), $str);
        return $str;
    }


//校园退费审核列表
    function getRefundOrderList($paramArray)
    {
        $datawhere = " o.company_id = '{$paramArray['company_id']}' ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$paramArray['keyword']}%' or o.trading_pid like '%{$paramArray['keyword']}%' or o.refund_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['refund_isspecial']) && $paramArray['refund_isspecial'] !== "") {
            $datawhere .= " and o.refund_isspecial ='{$paramArray['refund_isspecial']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                o.refund_id,
                o.trading_pid,
                o.refund_isspecial,
                o.refund_isspecial as refund_isspecial_name,
                o.refund_specialprice,
                o.refund_pid,
                o.refund_status,
                o.refund_status as refund_status_name,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                t.student_cnname,
                o.refund_name,
                o.refund_mobile,
                o.refund_bank,
                o.refund_accountname,
                o.refund_bankcard,
                o.refund_reason,
                o.refund_price
            FROM
                smc_refund_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
            WHERE
                {$datawhere} and o.refund_status <> 0
            ORDER BY
                o.refund_id DESC    
            LIMIT {$pagestart},{$num}";

        $tradeList = $this->DataControl->selectClear($sql);

        if ($tradeList) {
            $status = $this->LgArraySwitch(array("1" => "审核通过", "2" => "处理中", "3" => "确定金额", "4" => "完成退款"));
            foreach ($tradeList as &$val) {
                $val['refund_status_name'] = $status[$val['refund_status_name']];
            }
            $status = $this->LgArraySwitch(array("0" => "不含", "1" => "有特殊申请"));
            foreach ($tradeList as &$val) {
                $val['refund_isspecial_name'] = $status[$val['refund_isspecial_name']];
            }

        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(o.refund_id)
            FROM
                smc_refund_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
            WHERE
                {$datawhere} and o.refund_status >= 1");
        $allnums = $all_num[0][0];

        $fieldstring = array('refund_pid', 'school_cnname', 'school_branch', 'student_cnname', 'refund_isspecial_name', 'refund_name', 'refund_mobile', 'refund_bank', 'refund_accountname', 'refund_bankcard', 'refund_reason', 'refund_price', 'refund_specialprice', 'refund_status_name');
        $fieldname = $this->LgArraySwitch(array('退款订单编号', '所属学校', '校区编号', '学员姓名', '特殊申请', '退款人联系人', '退款人联系方式', '开户行', '开户名', '银行卡号', '退款原因', '实际退款金额', '特殊退款金额', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($tradeList) {
            $result['list'] = $tradeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无校园退费审核信息", 'result' => $result);
        }

        return $res;
    }


    function createProtocolAction($request)
    {
//        $this->ThisVerify($request);//验证账户


//        if($request['school_id'] == '1175'){
//            $PartyA = array();
//            $PartyA['school_signet'] = 'https://pic.kedingdang.com/schoolmanage/202104131840x270972325.png';
//            $a = $this->DataControl->selectOne("select alltreaty,sign from temp_result_copy1 where result_id = '{$request['protocol_id']}'");
//            $guarder = array();
//            $guarder['parenter_sign'] = $a['sign'];
//
//            $result = array();
//            $result['istable'] = '0';
//            $result['PartyA'] = $PartyA;
////            $result['student_id'] = $protocol['student_id'];
////            $result['PartyB'] = $PartyB;
//            $result['guarder'] = $guarder;
////            $result['courseInfo'] = $courseInfo;
////            $result['priceInfo'] = $priceInfo;
////            $result['order'] = $order;
////            $result['date'] = date('Y-m-d', $protocol['protocol_createtime']);
//            $result['text'] = $a['alltreaty'];
////            $result['tip'] = $protocolOne['treaty_tabletip'];
////            $result['signtime'] = substr($protocol['protocol_signtime'], 0, 10);
//
//            ajax_return(array('error' => 0, 'errortip' => "获取成功", 'result' => $result));
//        }


        //甲方
        $PartyA = array();
        $protocol = $this->DataControl->getOne("smc_student_protocol", "protocol_id = '{$request['protocol_id']}'");
        $order = $this->DataControl->selectOne("select o.order_pid,o.trading_pid,o.companies_id,p.pay_pid,p.pay_typename,p.pay_price,o.order_coupon_price,o.order_paidprice,o.order_arrearageprice,o.order_paymentprice,c.ordercourse_buynums,c.ordercourse_totalprice,c.ordercourse_unitprice from smc_payfee_order as o left join smc_payfee_order_pay as p on o.order_pid = p.order_pid left join smc_payfee_order_course as c on c.order_pid = o.order_pid where o.order_pid = '{$protocol['order_pid']}'");

        $company = $this->DataControl->getFieldOne("gmc_company", "company_cnname,company_logo", "company_id = '{$request['company_id']}'");
        $school = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_signet,school_address,school_phone,companies_id,school_liaison,school_examine,school_register,school_permitbranch,school_permitstday,school_permitendday,school_icp,school_licensestday,school_licenseendday,school_society,school_licensestatus", "school_id = '{$protocol['school_id']}'");
        $course = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_classnum,course_classtimes,coursetype_id,course_inclasstype,course_perhour,coursecat_id", "course_id = '{$protocol['course_id']}'");

        $companies_id = $this->DataControl->getFieldOne("smc_school_coursecat_subject","companies_id","school_id = '{$request['school_id']}' and coursecat_id = '{$course['coursecat_id']}'");

//        if ($request['staffer_id'] == '25721' && $course['coursecat_id']==133) {
//            $companies_id['companies_id']='78397';
//        }

        $companies = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname,companies_signet,companies_liaison,companies_examine,companies_register,companies_permitbranch,companies_permitstday,companies_permitendday,companies_icp,companies_licensestday,companies_licenseendday,companies_society,companies_licensestatus,companies_supervisebank,companies_superviseaccount,companies_settlebank,companies_settleaccount,companies_permitstday,companies_licensestday", "companies_id = '{$companies_id['companies_id']}'");

        $PartyA['companies_cnname'] = $companies['companies_cnname'];
        $PartyA['companies_permitstday'] = substr($companies['companies_permitstday'],strripos($companies['companies_permitstday'],"至")+3);
        $PartyA['companies_licensestday'] =  substr($companies['companies_licensestday'],strripos($companies['companies_licensestday'],"至")+3);
        $PartyA['companies_supervisebank'] = $companies['companies_supervisebank'];
        $PartyA['companies_superviseaccount'] = $companies['companies_superviseaccount'];
        $PartyA['companies_settlebank'] = $companies['companies_settlebank'];
        $PartyA['companies_settleaccount'] = $companies['companies_settleaccount'];
        $PartyA['school_cnname'] = $school['school_cnname'];
        $PartyA['company_cnname'] = $company['company_cnname'];
        $PartyA['school_address'] = $school['school_address'];
        $PartyA['school_phone'] = $school['school_phone'];
        if($school['school_phone']){
            $PartyA['school_phone'] = $school['school_phone'];
        }else{
            $PartyA['school_phone'] = '--';
        }
        if($companies['companies_liaison']){
            $PartyA['school_liaison'] = $companies['companies_liaison'];
        }else{
            $PartyA['school_liaison'] = '--';
        }
        if($companies['companies_examine']){
            $PartyA['school_examine'] = $companies['companies_examine'];
        }else{
            $PartyA['school_examine'] = '--';
        }
        if($companies['companies_register']){
            $PartyA['school_register'] = $companies['companies_register'];
        }else{
            $PartyA['school_register'] = '--';
        }
        if($companies['companies_permitbranch']){
            $PartyA['school_permitbranch'] = $companies['companies_permitbranch'];
        }else{
            $PartyA['school_permitbranch'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_permitstday']){
            $PartyA['school_permitstday'] = $companies['companies_permitstday'];
        }else{
            $PartyA['school_permitstday'] = '--';
        }
        if($companies['companies_licensestday']){
            $PartyA['school_licensestday'] = $companies['companies_licensestday'];
        }else{
            $PartyA['school_licensestday'] = '--';
        }
        if($companies['companies_society']){
            $PartyA['school_society'] = $companies['companies_society'];
        }else{
            $PartyA['school_society'] = '--';
        }
        $PartyA['school_signet'] = $companies['companies_signet'];

//        var_dump($PartyA['school_signet']);
        $PartyA['school_icp'] = $companies['companies_icp'];
        $PartyA['company_logo'] = $company['company_logo'];
        $PartyA['company_shortname'] = $school['company_shortname'];

        $PartyB = array();
        $student = $this->DataControl->getFieldOne("smc_student", "student_cnname,student_enname,student_birthday,student_sex,student_branch,student_idcard", "student_id = '{$protocol['student_id']}'");
        $PartyB['student_branch'] = $student['student_branch'];
        $PartyB['student_cnname'] = $student['student_cnname'];
        $PartyB['student_sex'] = $student['student_sex'];
        $PartyB['student_birthday'] = $student['student_birthday'];
        $famliy = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '1'");
        $parenter = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliy['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $PartyB['phone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//
//        }else{
        $PartyB['phone'] = $parenter['parenter_mobile'];

//        }


        $PartyB['schoolname'] = '--';
        $PartyB['address'] = '--';
        $PartyB['student_idcard'] = $student['student_idcard'];

        $guarder = array();
        $guarder['guardername'] = $parenter['family_cnname'];

        $guarder['guarderrelation'] = $parenter['family_relation'];
        $guarder['parenter_sign'] = $protocol['protocol_sign'];

//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $guarder['guarderphone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//        }else{
        $guarder['guarderphone'] = $parenter['parenter_mobile'];
//        }

        $famliys = $this->DataControl->getFieldOne("smc_student_family", "parenter_id", "student_id = '{$protocol['student_id']}' and family_isdefault = '0'");

        if ($famliys) {
            $parenters = $this->DataControl->selectOne("
            SELECT
                p.parenter_id,
                p.parenter_mobile,
                p.parenter_cnname,
                p.parenter_sign,
                f.family_relation,
                f.family_cnname,
                f.family_address 
            FROM
                smc_parenter AS p
                LEFT JOIN smc_student_family AS f ON p.parenter_id = f.parenter_id
                where p.parenter_id = '{$famliys['parenter_id']}' and f.student_id = '{$protocol['student_id']}'");
            $guarder['urgentname'] = $parenters['family_cnname'];
            $guarder['urgentphone'] = $parenters['parenter_mobile'];
            $guarder['parenter_id'] = $parenter['parenter_id'];
            $guarder['urgentrelation'] = $parenters['family_relation'];
        } else {
            $guarder['urgentname'] = $parenter['family_cnname'];
            $guarder['urgentphone'] = $parenter['parenter_mobile'];
            $guarder['parenter_id'] = $parenter['parenter_id'];
            $guarder['urgentrelation'] = $parenter['family_relation'];
        }

//        if($request['staffer_id'] == '25721' && $request['school_id']==2270){
//            $guarder['urgentphone'] = $this->getHiddenMobile($parenter['parenter_mobile']);
//        }else{
//            $guarder['urgentphone'] = $parenter['parenter_mobile'];
//        }

//        $courseInfo = array();
        $pricing_id = $this->DataControl->selectOne("select pricing_id,class_id from smc_payfee_order_course as c where c.order_pid = '{$protocol['order_pid']}'");
        $agreement = $this->DataControl->getFieldOne("smc_fee_pricing", "agreement_id", "pricing_id = '{$pricing_id['pricing_id']}'");

//        if ($protocol['protocol_isaudit'] == '1') {
//            $protocolOne = $this->DataControl->getFieldOne("smc_student_protocol", "treaty_tabletip,treaty_protocol", "protocol_id = '{$protocol['protocol_id']}'");
//        } else {
//            if($course['coursecat_id'] == '135'){
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '731' and coursetype_id = '79654' and t.treaty_applytype = '0'");
//            }else{
//                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
//                if(!$protocolOne){
//                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0'");
//                }
//            }
//        }

        $isprocat = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}'");
        if($isprocat){
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}'");
            if(!$protocolOne){
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '{$course['coursecat_id']}' and t.treaty_applytype = '0'");
                if(!$protocolOne){
                    $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '0'");
                    if(!$protocolOne){
                        $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and coursecat_id = '0' and t.treaty_applytype = '1'  and a.school_id = '{$request['school_id']}'");
                    }
                }
            }
        }else{
            $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '1' and a.school_id = '{$request['school_id']}' and t.coursecat_id = '0'");
            if(!$protocolOne){
                $protocolOne = $this->DataControl->selectOne("select t.treaty_id,t.treaty_protocol,t.treaty_tabletip from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.agreement_id = '{$agreement['agreement_id']}' and coursetype_id = '{$course['coursetype_id']}' and t.treaty_applytype = '0' and t.coursecat_id = '0'");
            }
        }

        $courseInfo['course_branch'] = $course['course_branch'];
        $courseInfo['course_cnname'] = $course['course_cnname'];
        $courseInfo['ordercourse_unitprice'] = $order['ordercourse_unitprice'];
        if ($order['ordercourse_buynums'] == $course['course_classnum']) {
            $courseInfo['type'] = '新班';
        } else {
            $courseInfo['type'] = '插班';
        }
        if ($course['course_inclasstype'] == '2' || $course['course_inclasstype'] == '0') {
            $courseInfo['course_classnum'] = $protocol['protocol_nums'];
        } elseif ($course['course_inclasstype'] == '1') {
            $courseInfo['course_classnum'] = '';
        } else {
            $courseInfo['course_classnum'] = $course['course_classnum'];
        }


        if ($course['course_inclasstype'] == '1') {
            $courseInfo['protocol_nums'] = '';
        } else {
            $courseInfo['protocol_nums'] = $protocol['protocol_nums'];
        }

        $courseInfo['course_classtimes'] = $course['course_perhour'] . 'h';

        $priceInfo = array();
        $priceInfo['protocol_pid'] = $protocol['protocol_pid'];
        $priceInfo['courseprice'] = '¥' . $order['ordercourse_totalprice'];
        $priceInfo['sendprice'] = '¥' . $protocol['protocol_price'];

//        if ($request['staffer_id'] == '25721'  && $course['coursecat_id']==133) {
//            if($protocol['protocol_nums']==26){
//                $priceInfo['sendprice'] = '¥' . $protocol['protocol_price']*0.64;
//                $courseInfo['protocol_nums']=$courseInfo['protocol_nums']*2;
//            }else{
//                $priceInfo['sendprice'] = '¥' . $protocol['protocol_price']*0.5;
//            }
//        }



        $track = $this->DataControl->getFieldOne("smc_payfee_order_tracks", "staffer_id", "order_pid = '{$order['order_pid']}' and tracks_title = '创建订单'");
        $agent = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$track['staffer_id']}'");
        $priceInfo['adviser'] = '--';
        $priceInfo['agent'] = $agent['staffer_cnname'];
        $priceInfo['principal'] = '--';

        $priceInfo['bigprice'] = $this->convert_2_cn(intval($order['ordercourse_totalprice']));

        $startday = $this->DataControl->selectOne("SELECT
	h.changelog_day 
FROM
	smc_student_changelog AS h
WHERE
	h.student_id = '{$protocol['student_id']}' 
	AND h.class_id = '{$pricing_id['class_id']}'
	AND h.stuchange_code = 'A02'");
        $date = $startday['changelog_day'];

        $endday = date('Y-m-d',strtotime("$date +90 day"));



        if($pricing_id['class_id'] == '0'){
            $startday['changelog_day'] = '';
            $endday = '';
        }


        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_tabletip'] = $this->contractTable($protocolOne['treaty_tabletip'],$treatyArray);
        }

        if($protocolOne){
            $treatyArray = array();
            $treatyArray['companies_cnname'] = $PartyA['companies_cnname'];
            $treatyArray['startday'] = $startday['changelog_day'];
            $treatyArray['endday'] = $endday;
            $treatyArray['companies_permitstday'] = $PartyA['companies_permitstday'];
            $treatyArray['companies_licensestday'] = $PartyA['companies_licensestday'];
            $treatyArray['course_classnum'] = $courseInfo['course_classnum'];
            $treatyArray['course_classtimes'] = $courseInfo['course_classtimes'];
            $treatyArray['ordercourse_unitprice'] = $courseInfo['ordercourse_unitprice'];
            $treatyArray['companies_supervisebank'] = $PartyA['companies_supervisebank'];
            $treatyArray['companies_superviseaccount'] = $PartyA['companies_superviseaccount'];
            $treatyArray['companies_settlebank'] = $PartyA['companies_settlebank'];
            $treatyArray['companies_settleaccount'] = $PartyA['companies_settleaccount'];
            $treatyArray['courseprice'] = $priceInfo['courseprice'];
            $treatyArray['bigprice'] = $priceInfo['bigprice'];
            $treatyArray['school_address'] = $PartyA['school_address'];
            $treatyArray['school_examine'] = $PartyA['school_examine'];
            $treatyArray['school_register'] = $PartyA['school_register'];
            $treatyArray['school_permitbranch'] = $PartyA['school_permitbranch'];
            $treatyArray['school_permitstday'] = $PartyA['school_permitstday'];
            $treatyArray['school_icp'] = $PartyA['school_icp'];
            $treatyArray['school_licensestday'] = $PartyA['school_licensestday'];
            $treatyArray['school_society'] = $PartyA['school_society'];
            $treatyArray['school_liaison'] = $PartyA['school_liaison'];
            $treatyArray['school_phone'] = $PartyA['school_phone'];
            $treatyArray['student_branch'] = $PartyB['student_branch'];
            $treatyArray['student_cnname'] = $PartyB['student_cnname'];
            $treatyArray['student_sex'] = $PartyB['student_sex'];
            $treatyArray['student_birthday'] = $PartyB['student_birthday'];
            $treatyArray['phone'] = $PartyB['phone'];
            $treatyArray['schoolname'] = $PartyB['schoolname'];
            $treatyArray['address'] = $PartyB['address'];
            $treatyArray['student_idcard'] = $PartyB['student_idcard'];
            $treatyArray['guardername'] = $guarder['guardername'];
            $treatyArray['guarderphone'] = $guarder['guarderphone'];
            $treatyArray['guarderrelation'] = $guarder['guarderrelation'];
            $treatyArray['urgentname'] = $guarder['urgentname'];
            $treatyArray['urgentphone'] = $guarder['urgentphone'];
            $treatyArray['urgentrelation'] = $guarder['urgentrelation'];
            $treatyArray['course_branch'] = $courseInfo['course_branch'];
            $treatyArray['course_cnname'] = $courseInfo['course_cnname'];
            $treatyArray['type'] = $courseInfo['type'];
            $treatyArray['protocol_nums'] = $courseInfo['protocol_nums'];
            $treatyArray['protocol_pid'] = $priceInfo['protocol_pid'];
            $treatyArray['sendprice'] = $priceInfo['sendprice'];
            $treatyArray['agent'] = $priceInfo['agent'];
            $protocolOne['treaty_protocol'] = $this->contractTable($protocolOne['treaty_protocol'],$treatyArray);
        }

//        $result = array();
//        $result['text'] = $protocolOne['treaty_protocol'];

        return $protocolOne['treaty_protocol'];

    }


    //合同列表
    function protocolList($paramArray)
    {
        $datawhere = " p.company_id = '{$paramArray['company_id']}' ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.protocol_pid like '%{$paramArray['keyword']}%' or p.order_pid like '%{$paramArray['keyword']}%' or st.student_cnname like '%{$paramArray['keyword']}%' or st.student_enname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ='{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and c.course_id ='{$paramArray['course_id']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and p.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['protocol_isaudit']) && $paramArray['protocol_isaudit'] !== "") {
            $datawhere .= " and p.protocol_isaudit ='{$paramArray['protocol_isaudit']}'";
        }
        if (isset($paramArray['protocol_issign']) && $paramArray['protocol_issign'] !== "") {
            $datawhere .= " and p.protocol_issign ='{$paramArray['protocol_issign']}'";
        }
        if (isset($paramArray['protocol_isinvoice']) && $paramArray['protocol_isinvoice'] !== "") {
            $datawhere .= " and i.invoice_status ='{$paramArray['protocol_isinvoice']}'";
        }
        if (isset($paramArray['protocol_issupervise']) && $paramArray['protocol_issupervise'] !== "") {
            $datawhere .= " and p.protocol_issupervise ='{$paramArray['protocol_issupervise']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_pid,
                p.protocol_issupervise,
                p.protocol_istable,
                p.order_pid,
                p.protocol_price,
                p.protocol_isaudit,
                p.protocol_issign,
                (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
                st.student_cnname,
                st.student_enname,
                st.student_branch,
                c.course_cnname,
                c.course_branch,
                p.protocol_nums,
                c.course_classnum,
                c.course_perhour * c.course_classnum AS time,
                p.protocol_nums * c.course_perhour AS times,
                o.trading_pid,
                oc.ordercourse_unitprice,
	            i.invoice_status,
	            co.company_protocolupdate
            FROM
                smc_student_protocol AS p
                LEFT JOIN smc_school AS sc ON p.school_id = sc.school_id
                LEFT JOIN smc_student AS st ON st.student_id = p.student_id
                LEFT JOIN smc_course AS c ON c.course_id = p.course_id
                LEFT JOIN smc_payfee_order as o on o.order_pid = p.order_pid
                LEFT JOIN smc_payfee_order_course AS oc ON oc.order_pid = p.order_pid 
                LEFT JOIN shop_invoice AS i ON p.protocol_id = i.protocol_id 
                left join gmc_company as co on co.company_id = sc.company_id
            WHERE
                {$datawhere} and p.protocol_isdel = '0'
            group by 
                p.protocol_id        
            ORDER BY
                p.protocol_id DESC
            LIMIT {$pagestart},{$num}";

        $tradeList = $this->DataControl->selectClear($sql);


        $status = $this->LgArraySwitch(array("0" => "未生效", "1" => "已生效"));
        $sign = $this->LgArraySwitch(array("0" => "未签字", "1" => "已签字"));
        if ($tradeList) {
            foreach ($tradeList as &$val) {
                $val['time'] = $val['course_classnum'] . '/' . $val['time'];
                $val['times'] = $val['protocol_nums'] . '/' . $val['times'];
                $val['protocol_isaudit_name'] = $status[$val['protocol_isaudit']];
                if ($val['invoice_status'] == '1') {
                    $val['invoice_status_name'] = '已开票';
                } else {
                    $val['invoice_status_name'] = '未开票';
                }
                $val['protocol_issign_name'] = $sign[$val['protocol_issign']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.protocol_id)
            FROM
                smc_student_protocol AS p
                LEFT JOIN smc_school AS sc ON p.school_id = sc.school_id
                LEFT JOIN smc_student AS st ON st.student_id = p.student_id
                LEFT JOIN smc_course AS c ON c.course_id = p.course_id
                LEFT JOIN shop_invoice AS i ON p.protocol_id = i.protocol_id 
            WHERE
                {$datawhere} and p.protocol_isdel = '0'");
        $allnums = $all_num[0][0];

        $fieldstring = array('protocol_pid', 'order_pid', 'school_cnname', 'student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'protocol_nums', 'protocol_price', 'time', 'times', 'ordercourse_unitprice', 'protocol_isaudit_name', 'invoice_status_name', 'protocol_issign_name');
        $fieldname = $this->LgArraySwitch(array('合同编号', '订单编号', '校区名称', '学员中文名', '学员英文名', '学员编号', '课程名称', '课程编号', '合同课程数', '合同金额', '标准课次/课时', '购买课次/课时', '课程单价', '是否生效', '是否开票', '是否签字'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($tradeList) {
            $result['list'] = $tradeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无合同管理信息", 'result' => $result);
        }

        return $res;
    }

    function contractTable($tabletip, $treatyArray)
    {
        $tableNote = $tabletip;
        foreach ($treatyArray as $key => $treatyOne) {

            $tableNote = str_replace("#" . $key . "#", $treatyOne, $tableNote);
        }
        return $tableNote;
    }

//处理退费
    function refundAction($paramArray)
    {
        $data = array();
        if ($paramArray['refund_status'] == '1') {
            $data['refund_status'] = '2';
        }
        if ($paramArray['refund_status'] == '2') {
            $data['refund_status'] = '3';
        }
        if ($paramArray['refund_status'] == '3') {
            $data['refund_status'] = '4';
        }
        $data['refund_updatatime'] = time();

        $field = array();
        $field['refund_status'] = "状态";
        $field['refund_updatatime'] = "更新时间";

        if ($this->DataControl->updateData("smc_refund_order", "refund_id = '{$paramArray['refund_id']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "处理成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '处理失败', 'result' => $result);
        }

        return $res;
    }

//校财务审核信息输入
    function serialAction($paramArray)
    {
        $data = array();
        $data['proorder_paynumber'] = $paramArray['proorder_paynumber'];
        $data['proorder_paytime'] = time();
        $data['proorder_paymennote'] = $paramArray['proorder_paymennote'];
        $data['proorder_paymentprice'] = $paramArray['proorder_paymentprice'];
        $data['proorder_updatetime'] = time();

        $field = array();
        $field['proorder_paynumber'] = "流水号";
        $field['proorder_paymennote'] = "备注";
        $field['proorder_paymentprice'] = "实际金额";
        $field['proorder_updatetime'] = "更新时间";

        if ($this->DataControl->updateData("erp_proorder", "proorder_id = '{$paramArray['proorder_id']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "处理成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '处理失败', 'result' => $result);
        }

        return $res;
    }


//确认金额
    function SureRefundAction($paramArray)
    {
        $datas = array();
        $datas['refund_pid'] = $paramArray['refund_pid'];
        $datas['tradelog_outnumber'] = $paramArray['tradelog_outnumber'];
        $datas['tradelog_note'] = $paramArray['tradelog_note'];
        $datas['tradelog_successtime'] = time();
        $this->DataControl->insertData('smc_refund_order_tradelog', $datas);

        if (isset($paramArray['refund_checkpicurl']) && $paramArray['refund_checkpicurl'] != '') {
            $datass = array();
            $datass['refund_checkpicurl'] = $paramArray['refund_checkpicurl'];
            $this->DataControl->updateData("smc_refund_order", "refund_pid = '{$paramArray['refund_pid']}'", $datass);
            $refundOne = $this->DataControl->getFieldOne("smc_refund_order", "school_id,student_id,company_id,trading_pid,refund_specialprice,refund_isspecial,refund_reason,companies_id", "refund_pid = '{$paramArray['refund_pid']}'");

            if ($refundOne['refund_isspecial'] == 1 && $refundOne['refund_specialprice'] > 0) {
                $data = array();
                do {
                    $trading_pid = $this->createOrderPid('RJ');
                } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));

                //$schoolOne=$this->DataControl->getFieldOne("smc_school","companies_id","school_id='{$refundOne['school_id']}'");

                $data['trading_pid'] = $trading_pid;
                $data['company_id'] = $refundOne['company_id'];
                $data['school_id'] = $refundOne['school_id'];
                $data['companies_id'] = $refundOne['companies_id'];
                $data['student_id'] = $refundOne['student_id'];
                $data['tradingtype_code'] = 'Accountrefund';
                $data['trading_status'] = "1";
                $data['trading_createtime'] = time();
                $data['staffer_id'] = $paramArray['staffer_id'];
                $this->DataControl->insertData("smc_student_trading", $data);


                $datasss = array();
                $datasss['company_id'] = $paramArray['company_id'];
                $datasss['school_id'] = $refundOne['school_id'];
                $datasss['companies_id'] = $refundOne['companies_id'];
                $datasss['expend_type'] = '1';
                $datasss['student_id'] = $refundOne['student_id'];
                $datasss['trading_pid'] = $trading_pid;
                $datasss['expend_price'] = $refundOne['refund_specialprice'];
                $datasss['expend_note'] = $refundOne['refund_reason'];
                $datasss['expend_confirmtime'] = time();
                $datasss['expend_createtime'] = time();
                $this->DataControl->insertData("smc_school_expend", $datasss);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '特殊退款金额', 'result' => $result);
                return $res;
            }
        }

        $data = array();
        $data['pay_outnumber'] = $datas['tradelog_outnumber'];

        $field = array();
        $field['refund_status'] = "状态";
        $field['refund_updatatime'] = "更新时间";

        if ($this->DataControl->updateData("smc_refund_order_trade", "refund_pid = '{$paramArray['refund_pid']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "处理成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '处理失败', 'result' => $result);
        }

        return $res;
    }

//发票管理
    function invoiceList($paramArray)
    {
        $datawhere = " i.company_id = '{$paramArray['company_id']}' ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (i.order_pid like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_cnname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['companies_id']) && $paramArray['companies_id'] !== "") {
            $datawhere .= " and i.companies_id ='{$paramArray['companies_id']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and pr.course_id ='{$paramArray['course_id']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and i.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( i.invoice_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( i.invoice_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['is_export'] == '1') {
            $sql = "
            SELECT i.*,c.course_cnname,c.course_branch,op.pay_pid,from_unixtime(i.invoice_createtime,'%Y-%m-%d') as invoice_createtime
            ,(SELECT SUM(r.refund_price) FROM smc_refund_order as r WHERE r.from_order_pid = i.order_pid) as refundPrice
            ,p.parenter_cnname, s.student_branch, s.student_cnname, s.student_enname,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch, ( SELECT p.companies_cnname FROM gmc_code_companies as p WHERE p.companies_id = i.companies_id ) AS companies_cnname, ( SELECT pp.companies_isInvoice FROM gmc_code_companies as pp WHERE pp.companies_id = i.companies_id ) AS companies_isInvoice,pr.protocol_price,i.invoice_allprice
            FROM
                shop_invoice AS i
                LEFT JOIN smc_parenter AS p ON i.parenter_id = p.parenter_id
                LEFT JOIN smc_payfee_order as o on i.order_pid = o.order_pid
                LEFT JOIN smc_payfee_order_pay as op on i.order_pid = op.order_pid
                LEFT JOIN smc_student as s on i.student_id = s.student_id
                LEFT JOIN smc_school as sc on i.school_id = sc.school_id
                LEFT JOIN smc_student_protocol as pr on i.protocol_id = pr.protocol_id
                LEFT JOIN smc_course as c on c.course_id = pr.course_id
            WHERE {$datawhere} and i.invoice_status = '{$paramArray['invoice_status']}' and i.invoice_cancel = '0' group by i.invoice_id ORDER BY invoice_createtime DESC";
        } else {
            $sql = "
            SELECT i.*,c.course_cnname,c.course_branch,op.pay_pid,from_unixtime(i.invoice_createtime,'%Y-%m-%d') as invoice_createtime
            ,(SELECT SUM(r.refund_price) FROM smc_refund_order as r WHERE r.from_order_pid = i.order_pid) as refundPrice
            ,p.parenter_cnname, s.student_branch, s.student_cnname, s.student_enname,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch, ( SELECT p.companies_cnname FROM gmc_code_companies as p WHERE p.companies_id = i.companies_id ) AS companies_cnname, ( SELECT pp.companies_isInvoice FROM gmc_code_companies as pp WHERE pp.companies_id = i.companies_id ) AS companies_isInvoice,pr.protocol_price,co.companies_isInvoice,sc.school_isvoucher,i.invoice_allprice
            FROM
                shop_invoice AS i
                LEFT JOIN smc_parenter AS p ON i.parenter_id = p.parenter_id
                LEFT JOIN smc_payfee_order as o on i.order_pid = o.order_pid
                LEFT JOIN smc_payfee_order_pay as op on i.order_pid = op.order_pid
                LEFT JOIN smc_student as s on i.student_id = s.student_id
                LEFT JOIN smc_school as sc on i.school_id = sc.school_id
                LEFT JOIN smc_student_protocol as pr on i.protocol_id = pr.protocol_id
                LEFT JOIN gmc_code_companies as co on i.companies_id = co.companies_id
                LEFT JOIN smc_course as c on c.course_id = pr.course_id
            WHERE {$datawhere} and i.invoice_status = '{$paramArray['invoice_status']}' and i.invoice_cancel = '0' group by i.invoice_id ORDER BY invoice_createtime DESC  
            LIMIT {$pagestart},{$num}";
        }


        $invoiceList = $this->DataControl->selectClear($sql);

        if ($invoiceList) {
            foreach ($invoiceList as &$val) {
                if ($val['staffer_id'] > 0) {
                    $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname,staffer_enname", "staffer_id = '{$val['staffer_id']}'");
                    $val['name'] = $name['staffer_enname'] ? $name['staffer_cnname'] . '-' . $name['staffer_enname'] : $name['staffer_cnname'];
                } else {
                    $name = $this->DataControl->getFieldOne("smc_parenter", "parenter_cnname", "parenter_id = '{$val['parenter_id']}'");
                    $val['name'] = $name['parenter_cnname'];
                }
                $val['invoice_pdfurl'] = $this->str_insert($val['invoice_pdfurl'], 4, "s");
                if ($val['companies_isInvoice'] == '0') {
                    $val['invoice_pdfurl'] = $val['invoice_voucher'];
                }

//                $allprice = $this->DataControl->selectOne("select sum(p.pay_price) as price from smc_payfee_order_pay as p where p.pay_issuccess = '1' and paytype_code in ('dcep','qrcode','bankcard','cash','pos','alipay','wechat','yinlian') and order_pid = '{$val['order_pid']}'");
                $val['protocol_price'] = $val['invoice_allprice'];
            }
        }


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $invoiceList;
            if (!$dateexcelarray) {
                ajax_return(array('error' => 1, 'errortip' => "导出数据为空!"), $this->companyOne['company_language']);
            }
            $outexceldate = array();
            if ($paramArray['invoice_status'] == '0') {
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['order_pid'] = $dateexcelvar['order_pid'];
                        $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                        $datearray['school_branch'] = $dateexcelvar['school_branch'];
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];
                        $datearray['protocol_price'] = $dateexcelvar['protocol_price'];
                        $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                        $datearray['course_branch'] = $dateexcelvar['course_branch'];
                        $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                        $datearray['invoice_title'] = $dateexcelvar['invoice_title'];
                        $datearray['invoice_email'] = $dateexcelvar['invoice_email'];
                        $datearray['invoice_createtime'] = $dateexcelvar['invoice_createtime'];
                        $datearray['name'] = $dateexcelvar['name'];
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '开票金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '邮箱', '申请时间', '申请人'));
                $excelfileds = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_email', 'invoice_createtime', 'name');
            } elseif ($paramArray['invoice_status'] == '-1') {
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['order_pid'] = $dateexcelvar['order_pid'];
                        $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                        $datearray['school_branch'] = $dateexcelvar['school_branch'];
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];
                        $datearray['protocol_price'] = $dateexcelvar['protocol_price'];
                        $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                        $datearray['course_branch'] = $dateexcelvar['course_branch'];
                        $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                        $datearray['invoice_title'] = $dateexcelvar['invoice_title'];
                        $datearray['invoice_email'] = $dateexcelvar['invoice_email'];
                        $datearray['invoice_createtime'] = $dateexcelvar['invoice_createtime'];
                        $datearray['name'] = $dateexcelvar['name'];
                        $datearray['invoice_reason'] = $dateexcelvar['invoice_reason'];
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '开票金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '邮箱', '申请时间', '申请人', '拒绝原因'));
                $excelfileds = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_email', 'invoice_createtime', 'name', 'invoice_reason');
            } elseif ($paramArray['invoice_status'] == '1') {
                if ($dateexcelarray) {
                    $outexceldate = array();
                    foreach ($dateexcelarray as $dateexcelvar) {
                        $datearray = array();
                        $datearray['order_pid'] = $dateexcelvar['order_pid'];
                        $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                        $datearray['school_branch'] = $dateexcelvar['school_branch'];
                        $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                        $datearray['student_enname'] = $dateexcelvar['student_enname'];
                        $datearray['student_branch'] = $dateexcelvar['student_branch'];
                        $datearray['protocol_price'] = $dateexcelvar['protocol_price'];
                        $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                        $datearray['course_branch'] = $dateexcelvar['course_branch'];
                        $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                        $datearray['invoice_title'] = $dateexcelvar['invoice_title'];
                        $datearray['invoice_code'] = $dateexcelvar['invoice_code'];
                        $datearray['invoice_number'] = $dateexcelvar['invoice_number'];
                        $datearray['invoice_email'] = $dateexcelvar['invoice_email'];
                        $datearray['invoice_createtime'] = $dateexcelvar['invoice_createtime'];
                        $datearray['name'] = $dateexcelvar['name'];
                        $outexceldate[] = $datearray;
                    }
                }
                $excelheader = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '开票金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '发票代码', '发票编号', '邮箱', '申请时间', '申请人'));
                $excelfileds = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_code', 'invoice_number', 'invoice_email', 'invoice_createtime', 'name');
            }
            if ($paramArray['invoice_status'] == '0') {
                $tem_name = $this->LgStringSwitch('待处理发票明细表.xls');
            } elseif ($paramArray['invoice_status'] == '-1') {
                $tem_name = $this->LgStringSwitch('已拒绝发票明细表.xls');
            } elseif ($paramArray['invoice_status'] == '1') {
                $tem_name = $this->LgStringSwitch('已开票发票明细表.xls');
            }
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }


        $all_num = $this->DataControl->select("
            SELECT
                COUNT(i.invoice_id)
            FROM
                shop_invoice AS i
                LEFT JOIN smc_parenter AS p ON i.parenter_id = p.parenter_id
                LEFT JOIN smc_payfee_order as o on i.order_pid = o.order_pid
                LEFT JOIN smc_student as s on i.student_id = s.student_id
                LEFT JOIN smc_school as sc on i.school_id = sc.school_id
                LEFT JOIN smc_student_protocol as pr on i.protocol_id = pr.protocol_id
                LEFT JOIN smc_course as c on c.course_id = pr.course_id
            WHERE {$datawhere} and i.invoice_status = '{$paramArray['invoice_status']}' and i.invoice_cancel = '0'");
        $allnums = $all_num[0][0];

        if ($paramArray['invoice_status'] == '0') {
            $fieldstring = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_email', 'invoice_createtime', 'name');
            $fieldname = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '开票金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '邮箱', '申请时间', '申请人'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        } elseif ($paramArray['invoice_status'] == '-1') {
            $fieldstring = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_email', 'invoice_createtime', 'name', 'invoice_reason');
            $fieldname = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '开票金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '邮箱', '申请时间', '申请人', '拒绝原因'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        } elseif ($paramArray['invoice_status'] == '1') {
            $fieldstring = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'protocol_price', 'course_cnname', 'course_branch', 'companies_cnname', 'invoice_title', 'invoice_email', 'invoice_createtime', 'name');
            $fieldname = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '开票金额', '课程别名称', '课程别编号', '开票公司', '发票抬头', '邮箱', '申请时间', '申请人'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($invoiceList) {
            $result['list'] = $invoiceList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无发票信息", 'result' => $result);
        }

        return $res;
    }

    function str_insert($str, $i, $substr)
    {
        for ($j = 0; $j < $i; $j++) {
            $startstr .= $str[$j];
        }
        for ($j = $i; $j < strlen($str); $j++) {
            $laststr .= $str[$j];
        }
        $str = ($startstr . $substr . $laststr);
        return $str;
    }


//支付记录
    function payList($paramArray)
    {
        $sql = "
            SELECT
                op.pay_pid,
                op.pay_typename,
                op.paytype_code,
                op.pay_issuccess,
                op.pay_price,
                op.order_pid,
                pop.paylog_tradeno,
                op.pay_note,
                op.pay_outnumber,
                pop.paylog_ifee,
                pop.paylog_img,
                from_unixtime(op.pay_successtime,'%Y-%m-%d') as pay_successtime
            FROM
                smc_payfee_order_pay AS op
                LEFT JOIN smc_payfee_order_paylog AS pop ON pop.order_pid = op.order_pid 
                AND op.pay_pid = pop.pay_pid
                LEFT JOIN smc_payfee_order AS po ON po.order_pid = op.order_pid
                LEFT JOIN smc_student AS s ON po.student_id = s.student_id 
            WHERE
                op.order_pid = '{$paramArray['order_pid']}' 
            ORDER BY
                op.pay_createtime DESC";

        $invoiceList = $this->DataControl->selectClear($sql);

        $status = array("0" => "未支付", "1" => "已支付", "-1" => "已失效");
        if ($invoiceList) {
            foreach ($invoiceList as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                if ($val['paylog_img'] == '') {
                    $val['paylog_img'] = '[]';
                }
            }
        }

        $fieldstring = array('pay_pid', 'pay_typename', 'pay_price', 'paylog_tradeno', 'pay_issuccess_name', 'paylog_ifee', 'pay_note', 'pay_successtime');
        $fieldname = $this->LgArraySwitch(array('支付请求号', '支付方式', '实际支付金额', '支付交易号', '支付状态', '手续费', '支付备注', '支付时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($invoiceList) {
            $result['list'] = $invoiceList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无支付记录", 'result' => $result);
        }

        return $res;
    }

//发票合同列表
    function getProtocolList($paramArray)
    {
        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_pid,
                p.order_pid,
                protocol_createtime,
                p.protocol_nums,
                p.protocol_price,
                p.protocol_isaudit,
                p.protocol_isaudit AS protocol_isaudit_name,
                c.course_classnum,
                c.course_classtimes,
                o.ordercourse_unitprice
            FROM
                smc_student_protocol AS p
                LEFT JOIN shop_invoice AS i ON i.protocol_id = p.protocol_id
                LEFT JOIN smc_course as c on p.course_id = c.course_id
                LEFT JOIN smc_payfee_order_course as o on o.order_pid = p.order_pid
            WHERE
                i.protocol_id = '{$paramArray['protocol_id']}' and p.protocol_isdel = '0'
            GROUP BY p.protocol_id";

        $districtList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "未审核", "1" => "已审核"));
        if ($districtList) {
            foreach ($districtList as &$val) {
                $val['protocol_isaudit_name'] = $status[$val['protocol_isaudit_name']];
                $val['standard'] = $val['course_classnum'] . '/' . ($val['course_classnum'] * $val['course_classtimes']);
                $val['buy'] = $val['protocol_nums'] . '/' . ($val['protocol_nums'] * $val['course_classtimes']);
            }
        }

        $fieldstring = array('protocol_pid', 'protocol_nums', 'protocol_price', 'standard', 'buy', 'ordercourse_unitprice');
        $fieldname = array('合同编号', '合同课程数', '合同金额', '标准课次/课时', '购买课次/课时', '课程单价');
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldstring[$i]));
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($districtList) {
            $result['list'] = $districtList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无合同", 'result' => $result);
        }

        return $res;
    }

//下载收据
    function downReceiptApi($request)
    {
        //下载收据
        $receiptOne = $this->DataControl->selectOne("select p.pay_price,p.pay_successtime,p.pay_typename,p.pay_type,s.student_cnname,s.student_enname,s.student_branch,(case when h.school_shortname='' then h.school_cnname else h.school_shortname end) as school_cnname,h.school_enname,h.school_branch, 
        (SELECT group_concat(DISTINCT(r.coursecat_cnname)) FROM smc_payfee_order_course as c LEFT JOIN smc_course as u ON c.course_id = u.course_id LEFT JOIN smc_code_coursecat as r ON u.coursecat_id = r.coursecat_id WHERE c.order_pid = p.order_pid ) as class_name
                from smc_payfee_order_pay as p 
                LEFT JOIN smc_payfee_order as o ON p.order_pid = o.order_pid 
                LEFT JOIN smc_student as s ON o.student_id = s.student_id 
                LEFT JOIN smc_school as h ON o.school_id = h.school_id 
                WHERE p.pay_pid='{$request['pay_pid']}' ");

        $receiptOne['pay_successtime'] = $receiptOne['pay_successtime'] > 0 ? date("Y-m-d", $receiptOne['pay_successtime']) : '';
        $receiptOne['staffer_cnname'] = $this->stafferOne['staffer_cnname'];
        $paytype = $this->LgArraySwitch(array("课程收费", "教材收费", "杂费收费", "账户充值"));
        $receiptOne['payremarks'] = $paytype[$receiptOne['pay_type']] . ' ' . $receiptOne['pay_typename'];

        //数字转大写
        list($qian, $hou) = explode(".", $receiptOne['pay_price'], 2);
        $yuan = str_split($qian);
        $jiao = str_split($hou);
        krsort($yuan);
        krsort($jiao);
        $cnynums = $this->LgArraySwitch(array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"));
        $pricearr = array_merge($jiao, $yuan);
        $pricearray = array();
        if ($pricearr) {
            foreach ($pricearr as &$pricearrvar) {
                $pricearray[] = $cnynums[$pricearrvar];
            }

            //补充七位数据
            $pricenum = count($pricearr);
            $cha = 7 - $pricenum;
            if ($cha > 0) {
                for ($x = 0; $x < $cha; $x++) {
                    $pricearray[] = $this->LgStringSwitch('零');
                }
            }
            krsort($pricearray);
        }
        $receiptOne['pricearray'] = $pricearray;


        $receipt = $this->DataControl->selectOne("select receipt_id from smc_payfee_order_payreceipt where pay_pid='{$request['pay_pid']}' limit 0,1");
        if (!$receipt) {
            do {
                $receipt_pid = $this->createReceiptPid('SJ');
            } while ($this->DataControl->selectOne("select receipt_id from smc_payfee_order_payreceipt where receipt_pid='{$receipt_pid}' limit 0,1"));

            $receiptarray = array();
            $receiptarray['staffer_id'] = $this->stafferOne['staffer_id'];//收据操作人
            $receiptarray['receipt_pid'] = $receipt_pid;//收据编号
            $receiptarray['order_pid'] = $this->payfeeorderOne['order_pid'];//订单编号
            $receiptarray['pay_pid'] = $request['pay_pid'];//对应的支付编号
            $receiptarray['receipt_info'] = json_encode($receiptOne);
            $receiptarray['receipt_addtime'] = time();//收据时间
            $receipt_id = $this->DataControl->insertData("smc_payfee_order_payreceipt", $receiptarray);

            $receiptOne['receipt_id'] = $receipt_id;
            $receiptOne['receipt_pid'] = $receipt_pid;
        } else {
            $receiptOne['receipt_id'] = $receipt['receipt_id'];
            $receiptOne['receipt_pid'] = $receipt['receipt_pid'];
        }

        return $receiptOne;
    }


}
