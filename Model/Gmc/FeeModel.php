<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class FeeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //收费协议列表
    function getAgreementList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.agreement_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and a.agreement_startday >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and a.agreement_endday <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.agreement_id,
                a.agreement_cnname,
                a.agreement_startday,
                a.agreement_endday,
                a.agreement_status,
                (select count(c.course_id) from smc_course as c where c.company_id = '{$paramArray['company_id']}') as num,
                from_unixtime(a.agreement_createtime,'%Y-%m-%d') as agreement_createtime
            FROM
                smc_fee_agreement AS a 
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY 
                a.agreement_status DESC
            LIMIT {$pagestart},{$num}";

        $AgreementList = $this->DataControl->selectClear($sql);

        if ($AgreementList) {
            foreach ($AgreementList as &$val) {
                $val['day'] = $val['agreement_startday'] . '至' . $val['agreement_endday'];
            }

            foreach ($AgreementList as &$val) {
                if (time() <= strtotime($val['agreement_endday']) && time() >= strtotime($val['agreement_startday'])) {
                    $val['status'] = $this->LgStringSwitch('生效中');
                } elseif (time() > strtotime($val['agreement_endday'])) {
                    $val['status'] = $this->LgStringSwitch('已失效');
                } else {
                    $val['status'] = $this->LgStringSwitch('未生效');
                }
            }
            $status = array("-1" => "0", "1" => "100");
            foreach ($AgreementList as &$val) {
                $val['agreement_status'] = $status[$val['agreement_status']];
            }

        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.agreement_id)
            FROM
                smc_fee_agreement AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('agreement_cnname', 'day', 'status', 'agreement_status', 'num', 'agreement_createtime');
        $fieldname = $this->LgArraySwitch(array('收费标准名称', '有效周期', '生效状态', '是否启用', '总课程数', '创建时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 1, 0, 0);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($AgreementList) {
            $result['list'] = $AgreementList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无集团课程收费协议", 'result' => $result);
        }

        return $res;
    }

    //添加收费协议
    function addAgreementAction($paramArray)
    {
        $data = array();
        $data['agreement_cnname'] = $paramArray['agreement_cnname'];
        $data['agreement_startday'] = $paramArray['agreement_startday'];
        $data['agreement_endday'] = $paramArray['agreement_endday'];
        $data['agreement_status'] = '-1';
        $data['agreement_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['agreement_cnname'] = "收费标准名称";
        $field['agreement_startday'] = "起效时间";
        $field['agreement_endday'] = "失效时间";
        $field['agreement_createtime'] = "创建时间";
        $field['company_id'] = "所属公司";

        if ($paramArray['agreement_startday'] > $paramArray['agreement_endday']) {
            ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
        }

        if ($id = $this->DataControl->insertData('smc_fee_agreement', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $result["agreement_id"] = $id;
            $res = array('error' => '0', 'errortip' => "添加收费标准成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '新增收费标准', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加收费标准失败', 'result' => $result);
        }
        return $res;
    }

    //复制收费协议
    function copyAgreementAction($paramArray)
    {
        $agreement = $this->DataControl->getOne('smc_fee_agreement', "agreement_id = '{$paramArray['agreement_id']}'");

        $data = array();
        $data['agreement_cnname'] = $agreement['agreement_cnname'] . $this->LgStringSwitch('（复制）');
        $data['agreement_startday'] = $agreement['agreement_startday'];
        $data['agreement_endday'] = $agreement['agreement_endday'];
        $data['agreement_status'] = 0;
        $data['agreement_createtime'] = time();
        $data['company_id'] = $agreement['company_id'];

        $field = array();
        $field['agreement_cnname'] = "收费标准名称";
        $field['agreement_createtime'] = "创建时间";
        $field['company_id'] = "所属公司";


        $priceList = $this->DataControl->selectClear("select pricing_id,pricing_name,course_id,pricing_applytype from smc_fee_pricing where agreement_id = {$paramArray['agreement_id']}");

        if ($priceList) {
            $id = $this->DataControl->insertData('smc_fee_agreement', $data);

            $treatyList = $this->DataControl->selectClear("select * from smc_fee_treaty where agreement_id = {$paramArray['agreement_id']}");
            foreach ($treatyList as $treatyOne) {
                $data = array();
                $data['agreement_id'] = $id;
                $data['coursetype_id'] = $treatyOne['coursetype_id'];
                $data['treaty_protocol'] = $treatyOne['treaty_protocol'];
                $data['treaty_refundinfo'] = $treatyOne['treaty_refundinfo'];
                $data['treaty_buyinfo'] = $treatyOne['treaty_buyinfo'];
                $data['treaty_name'] = $treatyOne['treaty_name'];
                $data['treaty_applytype'] = $treatyOne['treaty_applytype'];
                $data['coursecat_id'] = $treatyOne['coursecat_id'];
                $data['treaty_createtime'] = time();
                $tid = $this->DataControl->insertData('smc_fee_treaty', $data);
                if($treatyOne['treaty_applytype'] == '1'){
                    $school = $this->DataControl->selectClear("select * from smc_fee_treaty_apply where treaty_id = '{$treatyOne['treaty_id']}'");
                    foreach($school as $schoolOne){
                        $data = array();
                        $data['treaty_id'] = $tid;
                        $data['school_id'] = $schoolOne['school_id'];
                        $this->DataControl->insertData('smc_fee_treaty_apply', $data);
                    }

                }
            }

            foreach ($priceList as $priceOne) {
                $data = array();
                $data['pricing_name'] = $priceOne['pricing_name'];
                $data['course_id'] = $priceOne['course_id'];
                $data['pricing_oldid'] = $priceOne['pricing_id'];
                $data['pricing_applytype'] = $priceOne['pricing_applytype'];
                $data['agreement_id'] = $id;
                $data['pricing_addtime'] = time();
                $pricing_id = $this->DataControl->insertData("smc_fee_pricing", $data);

                $itemList = $this->DataControl->selectClear("select * from smc_fee_pricing_items where pricing_id = '{$priceOne['pricing_id']}'");
                if ($itemList) {
                    foreach ($itemList as $itemOne) {
                        $data = array();
                        $data['pricing_id'] = $pricing_id;
                        $data['feeitem_branch'] = $itemOne['feeitem_branch'];
                        $data['items_sellingprice'] = $itemOne['items_sellingprice'];
                        $data['items_unitprice'] = $itemOne['items_unitprice'];
                        $data['items_ismustbuy'] = $itemOne['items_ismustbuy'];
                        $data['items_buypiece'] = $itemOne['items_buypiece'];
                        $data['items_donatepiece'] = $itemOne['items_donatepiece'];
                        $data['items_isfree'] = $itemOne['items_isfree'];
                        $data['items_createtime'] = time();
                        $this->DataControl->insertData("smc_fee_pricing_items", $data);
                    }
                }

                $tuitionList = $this->DataControl->selectClear("select * from smc_fee_pricing_tuition where pricing_id = '{$priceOne['pricing_id']}'");
                $dates = array();
                $dates['tuition_originalprice'] = $tuitionList[0]['tuition_originalprice'];
                $dates['tuition_sellingprice'] = $tuitionList[0]['tuition_sellingprice'];
                $dates['course_id'] = $tuitionList[0]['course_id'];
                $dates['tuition_buypiece'] = $tuitionList[0]['tuition_buypiece'];
                $dates['tuition_donatepiece'] = $tuitionList[0]['tuition_donatepiece'];
                $dates['tuition_unitprice'] = $tuitionList[0]['tuition_unitprice'];
                $dates['tuition_addtime'] = time();
                $dates['pricing_id'] = $pricing_id;
                $this->DataControl->insertData("smc_fee_pricing_tuition", $dates);

                $schoolapply = $this->DataControl->selectClear("select * from smc_fee_pricing_apply WHERE pricing_id = '{$priceOne['pricing_id']}'");
                if ($schoolapply) {
                    foreach ($schoolapply as $schoolOne) {
                        $datess = array();
                        $datess['pricing_id'] = $pricing_id;
                        $datess['school_id'] = $schoolOne['school_id'];
                        $this->DataControl->insertData("smc_fee_pricing_apply", $datess);
                    }
                }

                $goodsapply = $this->DataControl->selectClear("select * from smc_fee_pricing_products WHERE pricing_id = '{$priceOne['pricing_id']}'");
                if ($goodsapply) {
                    foreach ($goodsapply as $goodsOne) {
                        $datesss = array();
                        $datesss['pricing_id'] = $pricing_id;
                        $datesss['products_name'] = $goodsOne['products_name'];
                        $datesss['goods_id'] = $goodsOne['goods_id'];
                        $datesss['goods_pid'] = $goodsOne['goods_pid'];
                        $datesss['products_originalprice'] = $goodsOne['products_originalprice'];
                        $datesss['products_sellingprice'] = $goodsOne['products_sellingprice'];
                        $datesss['products_unit'] = $goodsOne['products_unit'];
                        $datesss['products_ismustbuy'] = $goodsOne['products_ismustbuy'];
                        $datesss['products_buypiece'] = $goodsOne['products_buypiece'];
                        $datesss['products_donatepiece'] = $goodsOne['products_donatepiece'];
                        $datesss['products_unitprice'] = $goodsOne['products_unitprice'];
                        $datesss['products_isfree'] = $goodsOne['products_isfree'];
                        $datesss['products_createtime'] = time();
                        $this->DataControl->insertData("smc_fee_pricing_products", $datesss);
                    }
                }

            }
        } else {
            $res = array('error' => '1', 'errortip' => "收费标准内容为空");
        }


        $worehouse = $this->DataControl->selectClear("select * from smc_fee_warehouse where agreement_id = {$paramArray['agreement_id']}");

        if ($worehouse) {
            foreach ($worehouse as $worehouseOne) {
                $data = array();
                $data['warehouse_name'] = $worehouseOne['warehouse_name'];
                $data['warehouse_applytype'] = $worehouseOne['warehouse_applytype'];
                $data['warehouse_startday'] = $worehouseOne['warehouse_startday'];
                $data['warehouse_endday'] = $worehouseOne['warehouse_endday'];
                $data['warehouse_remark'] = $worehouseOne['warehouse_remark'];
                $data['agreement_id'] = $id;
                $worehouse_id = $this->DataControl->insertData("smc_fee_warehouse", $data);

                $schoolapply = $this->DataControl->selectClear("select * from smc_fee_warehouse_apply WHERE warehouse_id = '{$worehouseOne['warehouse_id']}'");

                if ($schoolapply) {
                    foreach ($schoolapply as $schoolOne) {
                        $data = array();
                        $data['warehouse_id'] = $worehouse_id;
                        $data['school_id'] = $schoolOne['school_id'];
                        $this->DataControl->insertData("smc_fee_warehouse_apply", $data);
                    }
                }

                $coursepacks = $this->DataControl->selectClear("select * from smc_fee_warehouse_coursepacks WHERE warehouse_id = '{$worehouseOne['warehouse_id']}'");
                if ($coursepacks) {
                    foreach ($coursepacks as $coursepacksOne) {
                        $data = array();
                        $data['warehouse_id'] = $worehouse_id;
                        $data['coursepacks_name'] = $coursepacksOne['coursepacks_name'];
                        $data['coursepacks_img'] = $coursepacksOne['coursepacks_img'];
                        $data['coursepacks_imglist'] = $coursepacksOne['coursepacks_imglist'];
                        $data['coursepacks_intro'] = $coursepacksOne['coursepacks_intro'];
                        $data['coursepacks_startday'] = $coursepacksOne['coursepacks_startday'];
                        $data['coursepacks_endday'] = $coursepacksOne['coursepacks_endday'];
                        $coursepacks_id = $this->DataControl->insertData("smc_fee_warehouse_coursepacks", $data);

                        $course = $this->DataControl->selectClear("select * from smc_fee_warehouse_courses where coursepacks_id = '{$coursepacksOne['coursepacks_id']}'");
                        foreach ($course as $courseOne) {
                            $data = array();
                            $data['tuition_sellingprice'] = $courseOne['tuition_sellingprice'];
                            $data['course_id'] = $courseOne['course_id'];
                            $pid = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "pricing_oldid = '{$courseOne['pricing_id']}'");
                            $data['pricing_id'] = $pid['pricing_id'];
                            $data['warehouse_id'] = $worehouse_id;
                            $data['coursepacks_id'] = $coursepacks_id;
                            $this->DataControl->insertData("smc_fee_warehouse_courses", $data);
                        }
                    }
                }
            }

            $res = array('error' => '0', 'errortip' => "复制收费标准成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '复制收费标准', dataEncode($paramArray));
        }


        return $res;
    }

    //复制收费协议
    function copyAgreementtestAction($paramArray)
    {
        $agreement = $this->DataControl->getOne('smc_fee_agreement', "agreement_id = '{$paramArray['agreement_id']}'");

        $data = array();
        $data['agreement_cnname'] = $agreement['agreement_cnname'] . $this->LgStringSwitch('（复制）');
        $data['agreement_startday'] = $agreement['agreement_startday'];
        $data['agreement_endday'] = $agreement['agreement_endday'];
        $data['agreement_status'] = 0;
        $data['agreement_createtime'] = time();
        $data['company_id'] = '78522';

        $field = array();
        $field['agreement_cnname'] = "收费标准名称";
        $field['agreement_createtime'] = "创建时间";
        $field['company_id'] = "所属公司";


        $priceList = $this->DataControl->selectClear("select pricing_id,pricing_name,course_id,pricing_applytype from smc_fee_pricing where agreement_id = {$paramArray['agreement_id']}");

        foreach ($priceList as &$val) {
            $name = $this->DataControl->getFieldOne("smc_course", "course_cnname", "course_id = '{$val['course_id']}'");
            $cid = $this->DataControl->getFieldOne("smc_course", "course_id", "course_cnname = '{$name['course_cnname']}' and company_id = '78522'");
            $val['cid'] = $cid['course_id'];
        }

        if ($priceList) {
            $id = $this->DataControl->insertData('smc_fee_agreement', $data);
            foreach ($priceList as $priceOne) {
                $date = array();
                $date['pricing_name'] = $priceOne['pricing_name'];
                $date['course_id'] = $priceOne['cid'];
                $date['pricing_applytype'] = $priceOne['pricing_applytype'];
                $date['agreement_id'] = $id;
                $date['pricing_addtime'] = time();
                $pricing_id = $this->DataControl->insertData("smc_fee_pricing", $date);


                $tuitionList = $this->DataControl->selectClear("select * from smc_fee_pricing_tuition where pricing_id = '{$priceOne['pricing_id']}'");
//                var_dump($tuitionList);die();
                $dates = array();
                $dates['tuition_originalprice'] = $tuitionList[0]['tuition_originalprice'];
                $dates['tuition_sellingprice'] = $tuitionList[0]['tuition_sellingprice'];
                $dates['course_id'] = $priceOne['cid'];
                $dates['tuition_buypiece'] = $tuitionList[0]['tuition_buypiece'];
                $dates['tuition_donatepiece'] = $tuitionList[0]['tuition_donatepiece'];
                $dates['tuition_unitprice'] = $tuitionList[0]['tuition_unitprice'];
                $dates['tuition_addtime'] = time();
                $dates['pricing_id'] = $pricing_id;
                $this->DataControl->insertData("smc_fee_pricing_tuition", $dates);

//                $schoolapply = $this->DataControl->selectClear("select * from smc_fee_pricing_apply WHERE pricing_id = '{$priceOne['pricing_id']}'");
//                if ($schoolapply) {
//                    foreach ($schoolapply as $schoolOne) {
//                        $datess = array();
//                        $datess['pricing_id'] = $pricing_id;
//                        $datess['school_id'] = $schoolOne['school_id'];
//                        $this->DataControl->insertData("smc_fee_pricing_apply", $datess);
//                    }
//                }

//                $goodsapply = $this->DataControl->selectClear("select * from smc_fee_pricing_products WHERE pricing_id = '{$priceOne['pricing_id']}'");
//                if ($goodsapply) {
//                    foreach ($goodsapply as $goodsOne) {
//                        $datesss = array();
//                        $datesss['pricing_id'] = $pricing_id;
//                        $datesss['products_name'] = $goodsOne['products_name'];
//                        $datesss['goods_id'] = $goodsOne['goods_id'];
//                        $datesss['goods_pid'] = $goodsOne['goods_pid'];
//                        $datesss['products_originalprice'] = $goodsOne['products_originalprice'];
//                        $datesss['products_sellingprice'] = $goodsOne['products_sellingprice'];
//                        $datesss['products_unit'] = $goodsOne['products_unit'];
//                        $datesss['products_ismustbuy'] = $goodsOne['products_ismustbuy'];
//                        $datesss['products_buypiece'] = $goodsOne['products_buypiece'];
//                        $datesss['products_donatepiece'] = $goodsOne['products_donatepiece'];
//                        $datesss['products_unitprice'] = $goodsOne['products_unitprice'];
//                        $datesss['products_isfree'] = $goodsOne['products_isfree'];
//                        $datesss['products_createtime'] = time();
//                        $this->DataControl->insertData("smc_fee_pricing_products", $datesss);
//                    }
//                }

            }
            $res = array('error' => '0', 'errortip' => "复制收费标准成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '复制收费标准', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "收费标准内容为空");
        }


        return $res;
    }

    //查看缴费协议
    function getAgreementDetail($paramArray)
    {
        $sql = "
            SELECT
                s.agreement_cnname,
                s.agreement_startday,
                s.agreement_endday,
                from_unixtime(s.agreement_createtime,'%Y-%m-%d') as agreement_createtime
            FROM
                smc_fee_agreement AS s 
            WHERE
                s.agreement_id = '{$paramArray['agreement_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["agreement_cnname"] = "收费标准名称";
        $field["agreement_startday"] = "生效日期";
        $field["agreement_endday"] = "失效日期";
        $field["agreement_createtime"] = "经办时间";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取缴费标准成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取缴费标准失败', 'result' => $result);
        }
        return $res;
    }

    //编辑缴费协议
    function updateAgreementDetailAction($paramArray)
    {
        $AgreementDetailOne = $this->DataControl->getFieldOne("smc_fee_agreement", "agreement_id", "agreement_id = '{$paramArray['agreement_id']}'");
        if ($AgreementDetailOne) {
            $data = array();
            $data['agreement_cnname'] = $paramArray['agreement_cnname'];
            $data['agreement_startday'] = $paramArray['agreement_startday'];
            $data['agreement_endday'] = $paramArray['agreement_endday'];
            $data['agreement_updatatime'] = time();

            $field = array();
            $field['agreement_cnname'] = "收费标准名称";
            $field['agreement_startday'] = "起效时间";
            $field['agreement_endday'] = "失效时间";
            $field['agreement_updatatime'] = "更新时间";

            if ($paramArray['agreement_startday'] > $paramArray['agreement_endday']) {
                ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->updateData("smc_fee_agreement", "agreement_id = '{$paramArray['agreement_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "缴费标准修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '编辑收费标准', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '缴费标准修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //查看课程别
    function getCourse($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_cnname like '%{$paramArray['keyword']}%' or c.course_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and t.coursetype_id ='{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and s.coursecat_id ='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['course_isfollow']) && $paramArray['course_isfollow'] !== "") {
            $datawhere .= " and c.course_isfollow ='{$paramArray['course_isfollow']}'";
        }
        $sql = "
            SELECT
                c.course_id,
                c.course_cnname,
                c.course_branch,
                c.course_classnum,c.course_minclassnum,c.course_inclasstype,
                (select count(p.pricing_id) from smc_fee_pricing as p WHERE p.course_id = c.course_id and agreement_id = '{$paramArray['agreement_id']}') as num
            FROM
                smc_course AS c
                LEFT JOIN smc_code_coursetype AS t ON c.coursetype_id = t.coursetype_id
                left join smc_code_coursecat as s on s.coursecat_id = c.coursecat_id
            WHERE {$datawhere} and c.company_id = '{$paramArray['company_id']}' and t.company_id = '{$paramArray['company_id']}' and s.company_id = '{$paramArray['company_id']}' and c.course_status = '1' and t.coursetype_isopenclass = '0'";
        $CourseList = $this->DataControl->selectClear($sql);


        if ($CourseList) {
            foreach ($CourseList as &$val) {
                $val['course_cnname'] = $val['course_cnname'] . '(' . $val['course_branch'] . ')' . '(' . $val['num'] . ')';
            }
        }

        $field = array();
        $field["course_cnname"] = "课程别名称";

        $result = array();

        $datawheres = " 1 ";
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawheres .= " and coursetype_id ='{$paramArray['coursetype_id']}'";
        }
        $result['coursetype'] = $this->DataControl->selectClear("select c.coursetype_id,c.coursetype_cnname,coursetype_branch,(select count(co.coursecat_id) from smc_code_coursecat as co WHERE co.coursetype_id = c.coursetype_id) as num,(select count(se.course_id) from smc_course as se WHERE se.coursetype_id = c.coursetype_id and se.course_status = '1') as nums from smc_code_coursetype as c where c.company_id = '{$paramArray['company_id']}' having num > '0' and nums > '0'");
        $result['coursecat'] = $this->DataControl->selectClear("select c.coursecat_id,c.coursecat_cnname,c.coursecat_branch,(select count(course_id) from smc_course as co WHERE co.coursecat_id = c.coursecat_id and co.course_status = '1') as num from smc_code_coursecat as c WHERE {$datawheres} and c.company_id = '{$paramArray['company_id']}' HAVING num <> '0'");

        $result["field"] = $field;
        $result["data"] = $CourseList;
        $res = array('error' => '0', 'errortip' => '获取查看课程别成功', 'result' => $result);

        return $res;
    }

    //缴费设定列表
    function getPriceList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.pricing_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and p.course_id ={$paramArray['course_id']}";
        }
        if (isset($paramArray['course_isfollow']) && $paramArray['course_isfollow'] !== "") {
            $datawhere .= " and co.course_isfollow ={$paramArray['course_isfollow']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.pricing_id,
                p.course_id,
                p.pricing_name,
                t.tuition_id,
                t.tuition_originalprice,
                t.tuition_sellingprice,
                t.tuition_buypiece,
                t.tuition_unitprice,
                t.tuition_refundprice,
                t.tuition_minclassnum,
                p.pricing_applytype,
                p.pricing_applytype as status,
                co.course_inclasstype,co.course_sellclass,co.course_minclassnum,
                (select count(d.products_id) from smc_fee_pricing_products as d WHERE d.pricing_id = p.pricing_id) as num,
                (select count(s.school_id) from smc_school as s left join smc_fee_pricing_apply as pa ON pa.school_id = s.school_id where pa.pricing_id = p.pricing_id and s.school_isclose = '0') as school_num,
                (select count(c.ordercourse_id) from smc_payfee_order_course as c where c.pricing_id = p.pricing_id) as isshow
            FROM
                smc_fee_pricing AS p
                inner join smc_fee_pricing_tuition AS t ON p.pricing_id = t.pricing_id
                inner join smc_course as co on co.course_id=p.course_id
            WHERE {$datawhere} and p.agreement_id = '{$paramArray['agreement_id']}'
            ORDER BY
                p.pricing_id DESC
            LIMIT {$pagestart},{$num}";

        $PriceList = $this->DataControl->selectClear($sql);


//        $status = array("0" => "全部适用", "1" => "部分适用", "-1" => "部分不适用");
        if ($PriceList) {
            foreach ($PriceList as &$val) {
                if ($val['status'] == 0) {
                    $val['status'] = $this->LgStringSwitch("全部适用");
                } elseif ($val['status'] == 1) {
                    $val['status'] = $this->LgStringSwitch("部分适用({$val['school_num']})");
                } elseif ($val['status'] == -1) {
                    $val['status'] = $this->LgStringSwitch("部分不适用({$val['school_num']})");
                }

                if ($val['course_inclasstype'] == 2 || $val['course_sellclass']==1) {
                    $fitList = $this->DataControl->getList("smc_fee_pricing_fit", "tuition_id='{$val['tuition_id']}'");
                    if ($fitList) {
                        $val['list'] = json_encode($fitList);
                    } else {
                        $val['list'] = '[]';
                    }
                }
            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.pricing_id)
            FROM
                smc_fee_pricing AS p
                inner join smc_fee_pricing_tuition AS t ON p.pricing_id = t.pricing_id
                inner join smc_course as co on co.course_id=p.course_id
            WHERE
                {$datawhere} and p.course_id = '{$paramArray['course_id']}' and p.agreement_id = '{$paramArray['agreement_id']}'");
        $allnums = $all_num[0][0];


        $fieldstring = array('pricing_name', 'tuition_originalprice', 'tuition_sellingprice ', 'tuition_buypiece', 'tuition_unitprice', 'tuition_refundprice', 'status', 'num', 'pricing_applytype');
        $fieldname = $this->LgArraySwitch(array('收费价格名称', '市场价', '销售价', '课次', '标准单价', '课程退费手续费', '适用学校', '教材数量', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldmethod = array(0, 0, 0, 0, 0, 0, 1, 1);
        $fieldalign = array("center", "center", "center", "center", "center", "center", "center", "center");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["isShow"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldmethod[$i]);
            $field[$i]["align"] = trim($fieldalign[$i]);
        }

        $result = array();
        $couseOne = $this->DataControl->getFieldOne("smc_course", "course_cnname,course_branch,course_inclasstype,course_sellclass", "course_id = '{$paramArray['course_id']}' and company_id = '{$paramArray['company_id']}'");
        $result['course_name'] = $couseOne['course_cnname'] . '(' . $couseOne['course_branch'] . ')';
        $result['course_inclasstype'] = $couseOne['course_inclasstype'];
        $result['course_sellclass'] = $couseOne['course_sellclass'];
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PriceList) {
            $result['list'] = $PriceList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程价格设置", 'result' => $result);
        }

        return $res;
    }

    //适用分校列表
    function getSetSchool($paramArray)
    {
        if ($paramArray['pricing_id'] !== '') {
            $sql = "
            SELECT
                s.school_id,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                s.school_branch,
                s.school_type,
                d.district_cnname,
                a.pricing_id
            FROM
                smc_fee_pricing_apply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                LEFT JOIN gmc_company_district AS d ON s.district_id = d.district_id
            WHERE
                a.pricing_id = '{$paramArray['pricing_id']}' and s.school_isclose = '0'";

            $SchoolList = $this->DataControl->selectClear($sql);

            if ($SchoolList) {
                $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "加盟校", "3" => "加盟校", "4" => "加盟园"));
                foreach ($SchoolList as &$val) {
                    $val['school_type'] = $status[$val['school_type']];
                }
            }

            $all_num = $this->DataControl->select("
            SELECT
               COUNT(*)
            FROM
                smc_fee_pricing_apply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                LEFT JOIN gmc_company_district AS d ON s.district_id = d.district_id
            WHERE
                a.pricing_id = '{$paramArray['pricing_id']}'");
            $allnums = $all_num[0][0];

            $fieldstring = array('school_branch', 'school_cnname ', 'school_enname', 'district_cnname');
            $fieldname = $this->LgArraySwitch(array('校区编号', '校园名称', '检索代码', '区域'));
            $fieldcustom = array("1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;
            $result['all_num'] = $allnums;
            if ($SchoolList) {
                $result['list'] = $SchoolList;
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $result['list'] = array();
                $res = array('error' => '1', 'errortip' => "暂无适用分校信息", 'result' => $result);
            }

        } else {
            $res = array('error' => '1', 'errortip' => "暂无适用分校信息", 'result' => array());
        }


        return $res;
    }

    //适用分校列表
    function getSetTreatySchool($paramArray)
    {
        if ($paramArray['treaty_id'] !== '') {
            $sql = "
            SELECT
                s.school_id,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                s.school_branch,
                s.school_type,
                d.district_cnname,
                a.treaty_id
            FROM
                smc_fee_treaty_apply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                LEFT JOIN gmc_company_district AS d ON s.district_id = d.district_id
            WHERE
                a.treaty_id = '{$paramArray['treaty_id']}' and s.school_isclose = '0'";

            $SchoolList = $this->DataControl->selectClear($sql);

            if ($SchoolList) {
                $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "加盟校", "3" => "加盟校", "4" => "加盟园"));
                foreach ($SchoolList as &$val) {
                    $val['school_type'] = $status[$val['school_type']];
                }
            }

            $all_num = $this->DataControl->select("
            SELECT
               COUNT(*)
            FROM
                smc_fee_treaty_apply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                LEFT JOIN gmc_company_district AS d ON s.district_id = d.district_id
            WHERE
                a.treaty_id = '{$paramArray['treaty_id']}'");
            $allnums = $all_num[0][0];

            $fieldstring = array('school_branch', 'school_cnname ', 'school_enname', 'district_cnname');
            $fieldname = $this->LgArraySwitch(array('校区编号', '校园名称', '检索代码', '区域'));
            $fieldcustom = array("1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;
            $result['all_num'] = $allnums;
            if ($SchoolList) {
                $result['list'] = $SchoolList;
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $result['list'] = array();
                $res = array('error' => '1', 'errortip' => "暂无适用分校信息", 'result' => $result);
            }

        } else {
            $res = array('error' => '1', 'errortip' => "暂无适用分校信息", 'result' => array());
        }


        return $res;
    }

    //删除适用学校
    function delSetSchoolAction($paramArray)
    {
        if(!isset($paramArray['school_id']) || $paramArray['school_id']=='' || $paramArray['school_id']=='0'){
            $result = array();
            $res = array('error' => '1', 'errortip' => '学校ID必须传', 'result' => $result);
            return $res;
        }
        $where = "pricing_id = '{$paramArray['pricing_id']}'";
        $school_list = json_decode(stripslashes($paramArray['school_id']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $school_id = implode(',', $str);
            $where .= " and school_id in ({$school_id})";
        }else{
            $where .= " and school_id = '{$paramArray['school_id']}'";
        }

        if ($this->DataControl->delData("smc_fee_pricing_apply", $where)) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除适用学校成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '删除协议适用学校', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除适用学校失败', 'result' => $result);
        }
        return $res;
    }

    //删除适用学校
    function delSetTreatySchoolAction($paramArray)
    {
        if(!isset($paramArray['school_id']) || $paramArray['school_id']=='' || $paramArray['school_id']=='0'){
            $this->error = true;
            $this->errortip = "学校ID必须传";
            return false;
        }
        $where = "treaty_id = '{$paramArray['treaty_id']}'";
        $school_list = json_decode(stripslashes($paramArray['school_id']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $school_id = implode(',', $str);
            $where .= " and school_id in ({$school_id})";
        }else{
            $where .= " and school_id = '{$paramArray['school_id']}'";
        }

        if ($this->DataControl->delData("smc_fee_treaty_apply", $where)) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除适用学校成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '删除协议内容适用学校', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除适用学校失败', 'result' => $result);
        }
        return $res;
    }

    //适用教材列表
    function getSetProduct($paramArray)
    {
        $sql = "
            SELECT
                d.products_id,
                d.products_name,
                d.goods_pid,
                d.products_unitprice,
                d.products_originalprice,
                d.products_sellingprice,
                d.products_ismustbuy,
                d.products_buypiece,
                d.products_donatepiece,
                d.products_isfree
            FROM
                smc_fee_pricing_products AS d
            WHERE
                d.pricing_id = '{$paramArray['pricing_id']}'";

        $ProductList = $this->DataControl->selectClear($sql);

        $status = array("0" => "0", "1" => "100");
        if ($ProductList) {
            foreach ($ProductList as &$val) {
                $val['products_ismustbuy'] = $status[$val['products_ismustbuy']];
            }
            foreach ($ProductList as &$val) {
                $val['products_isfree'] = $status[$val['products_isfree']];
            }
        }


        $fieldstring = array('products_name', 'goods_pid', 'products_unitprice ', 'products_sellingprice', 'products_buypiece', 'products_donatepiece', 'products_ismustbuy', 'products_isfree');
        $fieldname = $this->LgArraySwitch(array('教材名称', '教材编号', '收费标准单价', '收费标准总价', '销售件数', '赠送件数', '是否为必买', '是否为免费'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 0, 0, 0, 1, 1);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ProductList) {
            $result['list'] = $ProductList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无适用教材", 'result' => $result);
        }

        return $res;
    }

    //适用教材列表
    function getSetProducts($paramArray)
    {
        $sql = "
            SELECT
                d.products_id,
                d.products_name,
                d.goods_pid,
                d.products_originalprice,
                d.products_sellingprice,
                d.products_unitprice,
                d.products_buypiece,
                d.products_donatepiece,
                d.products_ismustbuy,
                d.products_isfree
            FROM
                smc_fee_pricing_products AS d
            WHERE
                d.pricing_id = '{$paramArray['pricing_id']}'";

        $ProductList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        if ($ProductList) {
            foreach ($ProductList as &$val) {
                $val['products_ismustbuy'] = $status[$val['products_ismustbuy']];
            }
            foreach ($ProductList as &$val) {
                $val['products_isfree'] = $status[$val['products_isfree']];
            }
        }


        $fieldstring = array('products_name', 'goods_pid', 'products_unitprice ', 'products_sellingprice', 'products_buypiece', 'products_donatepiece');
        $fieldname = $this->LgArraySwitch(array('教材名称', '教材编号', '标准单价', '标准总价', '销售件数', '赠送件数'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ProductList) {
            $result['list'] = $ProductList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无适用教材信息", 'result' => $result);
        }

        return $res;
    }

    //删除适用教材
    function delSetProductAction($paramArray)
    {
        if ($this->DataControl->delData("smc_fee_pricing_products", "products_id = '{$paramArray['products_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除适用教材成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '删除课程适用教材', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除适用教材失败', 'result' => $result);
        }
        return $res;
    }

    //新增收费价格
    function addPriceAction($paramArray)
    {

        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype,course_sellclass", "course_id='{$paramArray['course_id']}'");
        if (!$courseOne) {
            ajax_return(array('error' => '1', 'errortip' => "课程不存在!"), $this->companyOne['company_language']);
        }

        if ($courseOne['course_inclasstype'] == 2 || $courseOne['course_sellclass']==1) {
            $list = json_decode(stripslashes($paramArray['list']), 1);
            if (!$list) {
                ajax_return(array('error' => '1', 'errortip' => "预约类课程必须设置价格设定!"), $this->companyOne['company_language']);
            }
        }

        $data = array();
        $data['course_id'] = $paramArray['course_id'];
        $data['agreement_id'] = $paramArray['agreement_id'];
        $data['pricing_name'] = $paramArray['pricing_name'] . $paramArray['course_branch'];
        $data['pricing_applytype'] = $paramArray['pricing_applytype'];
        $data['pricing_addtime'] = time();

        $a = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "agreement_id = '{$paramArray['agreement_id']}' and course_id = '{$paramArray['course_id']}' and pricing_applytype = '0'");
        if ($a) {
            ajax_return(array('error' => '1', 'errortip' => "该课程价格设置已被全部学校适用!"), $this->companyOne['company_language']);
        }

        $a = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "agreement_id = '{$paramArray['agreement_id']}' and course_id = '{$paramArray['course_id']}'");
        if ($a && $paramArray['pricing_applytype'] == '0') {
            ajax_return(array('error' => '1', 'errortip' => "已有部分学校适用该课程，无法全部适用!"), $this->companyOne['company_language']);
        }

        $a = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "agreement_id = '{$paramArray['agreement_id']}' and course_id = '{$paramArray['course_id']}' and pricing_applytype = '-1'");
        if ($a && $paramArray['pricing_applytype'] == '-1') {
            ajax_return(array('error' => '1', 'errortip' => "该课程价格设置已被部分学校不适用!"), $this->companyOne['company_language']);
        }

        $pricing_id = $this->DataControl->insertData('smc_fee_pricing', $data);

        $datas = array();
        $datas['course_id'] = $paramArray['course_id'];
        $datas['pricing_id'] = $pricing_id;
        $datas['tuition_originalprice'] = $paramArray['tuition_originalprice'];
        $datas['tuition_unitprice'] = $paramArray['tuition_unitprice'];
        $datas['tuition_buypiece'] = $paramArray['tuition_buypiece'];
        $datas['tuition_sellingprice'] = $paramArray['tuition_sellingprice'];
        $datas['tuition_refundprice'] = $paramArray['tuition_refundprice'];
        $datas['tuition_minclassnum'] = isset($paramArray['tuition_minclassnum'])?$paramArray['tuition_minclassnum']:0;
        $datas['tuition_addtime'] = time();
        $tuition_id = $this->DataControl->insertData('smc_fee_pricing_tuition', $datas);

        if ($courseOne['course_inclasstype'] == 2 || $courseOne['course_sellclass']==1) {
            foreach ($list as $val) {
                $tem_data = array();
                $tem_data['tuition_id'] = $tuition_id;
                $tem_data['fit_buypiece'] = $val['fit_buypiece'];
                $tem_data['fit_price'] = $val['fit_price'];
                $tem_data['fit_isdefault'] = $val['fit_isdefault'];
                $this->DataControl->insertData("smc_fee_pricing_fit", $tem_data);
            }
        }


        $field = array();
        $field['course_id'] = "所属课程id";
        $field['agreement_id'] = "所属收费标准id";
        $field['pricing_name'] = "收费名称";
        $field['tuition_originalprice'] = "原价";
        $field['tuition_unitprice'] = "单价";
        $field['tuition_sellingprice'] = "销售价";
        $field['tuition_addtime'] = "创建时间";
        $field['pricing_addtime'] = "创建时间";
        $field['tuition_refundprice'] = "课程退费手续费";
        $field['pricing_applytype'] = "收费适用学校方式：0全部适用1部分适用-1部分不适用";

        $result = array();
        $result["field"] = $field;
        $result["data"] = $data;
        $result["datas"] = $datas;
        $res = array('error' => '0', 'errortip' => "新增收费价格成功", 'result' => $result);
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '新增收费价格', dataEncode($paramArray));
        return $res;
    }

    //分校列表
    function getSchool($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== "") {
            $datawhere .= " and s.district_id ={$paramArray['district_id']}";
        }
        if (isset($paramArray['school_province']) && $paramArray['school_province'] !== "") {
            $datawhere .= " and s.school_province ={$paramArray['school_province']}";
        }
        if (isset($paramArray['school_city']) && $paramArray['school_city'] !== "") {
            $datawhere .= " and s.school_city ={$paramArray['school_city']}";
        }
        if (isset($paramArray['school_area']) && $paramArray['school_area'] !== "") {
            $datawhere .= " and s.school_area ={$paramArray['school_area']}";
        }
        if (isset($paramArray['school_issubject']) && $paramArray['school_issubject'] !== "") {
            $datawhere .= " and s.school_issubject ={$paramArray['school_issubject']}";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id'] !== "") {
            $datawhere .= " and s.school_id in (select school_id from gmc_company_organizeschool where organize_id = '{$paramArray['organize_id']}')";
        }
        $sql = "
            SELECT
                s.school_id
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
                left join smc_fee_pricing_apply as p on p.school_id = s.school_id
                left join smc_fee_pricing as c on c.pricing_id = p.pricing_id and c.agreement_id = '{$paramArray['agreement_id']}' and c.course_id = '{$paramArray['course_id']}'
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' and c.pricing_applytype = '1' and s.school_id in (select p.school_id from smc_fee_pricing_apply as p where p.pricing_id = c.pricing_id ) 
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";

        $g = $this->DataControl->selectClear($sql);

        if ($g) {
            $h = array_column($g, 'school_id');
            $f = implode(",", $h);
        } else {
            $g = array();
        }
        if ($g) {
            $sqlfields = "and s.school_id not in ({$f})";
        } else {
            $sqlfields = "";
        }

        if ($paramArray['pricing_applytype'] == '1') {
            $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_type,
                s.school_enname,
                d.district_cnname
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' {$sqlfields}
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";
            $SchoolList = $this->DataControl->selectClear($sql);

            if ($SchoolList) {
                $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
                foreach ($SchoolList as &$val) {
                    $val['school_type'] = $status[$val['school_type']];
                }
            }
        } else {
            $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_type,
                s.school_enname,
                d.district_cnname
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0'
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";
            $SchoolList = $this->DataControl->selectClear($sql);

            if ($SchoolList) {
                $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
                foreach ($SchoolList as &$val) {
                    $val['school_type'] = $status[$val['school_type']];
                }
            }
        }

        $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_type,
                s.school_enname,
                d.district_cnname
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
                left join smc_fee_pricing_apply as p on p.school_id = s.school_id
                left join smc_fee_pricing as c on c.pricing_id = p.pricing_id and c.agreement_id = '{$paramArray['agreement_id']}' and c.course_id = '{$paramArray['course_id']}'
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0'
            and s.school_id in (select p.school_id from smc_fee_pricing_apply as p where p.pricing_id = c.pricing_id )
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";

        $f = $this->DataControl->selectClear($sql);

        if ($f) {
            $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
            foreach ($f as &$val) {
                $val['school_type'] = $status[$val['school_type']];
            }
        } else {
            $f = array();
        }

        $fieldstring = array('school_branch', 'school_cnname ', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('校区编号', '校园名称', '检索代码', '所在区域'));
        $fieldcustom = array("1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        if ($SchoolList) {
            $result['list'] = $SchoolList;
        } else {
            $result['list'] = array();
        }

        $result['district'] = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$paramArray['company_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'choose' => $f, 'id' => $h);

        return $res;
    }


    //分校列表
    function getTreatySchool($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== "") {
            $datawhere .= " and s.district_id ={$paramArray['district_id']}";
        }
        if (isset($paramArray['school_province']) && $paramArray['school_province'] !== "") {
            $datawhere .= " and s.school_province ={$paramArray['school_province']}";
        }
        if (isset($paramArray['school_city']) && $paramArray['school_city'] !== "") {
            $datawhere .= " and s.school_city ={$paramArray['school_city']}";
        }
        if (isset($paramArray['school_area']) && $paramArray['school_area'] !== "") {
            $datawhere .= " and s.school_area ={$paramArray['school_area']}";
        }
        if (isset($paramArray['school_issubject']) && $paramArray['school_issubject'] !== "") {
            $datawhere .= " and s.school_issubject ={$paramArray['school_issubject']}";
        }
        if (isset($paramArray['organize_id']) && $paramArray['organize_id'] !== "") {
            $datawhere .= " and s.school_id in (select school_id from gmc_company_organizeschool where organize_id = '{$paramArray['organize_id']}')";
        }
        $sql = "
            SELECT
                s.school_id
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
                left join smc_fee_treaty_apply as p on p.school_id = s.school_id
                left join smc_fee_treaty as c on c.treaty_id = p.treaty_id and c.agreement_id = '{$paramArray['agreement_id']}' and c.coursetype_id = '{$paramArray['coursetype_id']}'
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' and c.treaty_applytype = '1' and s.school_id in (select p.school_id from smc_fee_treaty_apply as p where p.treaty_id = c.treaty_id ) and c.coursecat_id = '{$paramArray['coursecat_id']}'
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";

        $g = $this->DataControl->selectClear($sql);

        if ($g) {
            $h = array_column($g, 'school_id');
            $f = implode(",", $h);
        } else {
            $g = array();
        }
        if ($g) {
            $sqlfields = "and s.school_id not in ({$f})";
        } else {
            $sqlfields = "";
        }

        $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_type,
                s.school_enname,
                d.district_cnname
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0' {$sqlfields}
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";
        $SchoolList = $this->DataControl->selectClear($sql);

        if ($SchoolList) {
            $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
            foreach ($SchoolList as &$val) {
                $val['school_type'] = $status[$val['school_type']];
            }
        }

        $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_type,
                s.school_enname,
                d.district_cnname
            FROM
                smc_school AS s
                left join gmc_company_district as d on d.district_id = s.district_id
                left join smc_fee_treaty_apply as p on p.school_id = s.school_id
                left join smc_fee_treaty as c on c.treaty_id = p.treaty_id and c.agreement_id = '{$paramArray['agreement_id']}' and c.coursetype_id = '{$paramArray['coursetype_id']}'
            WHERE {$datawhere} and (s.school_type = 1 or 3) and s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0'
            and s.school_id in (select p.school_id from smc_fee_treaty_apply as p where p.treaty_id = c.treaty_id )
            order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
            ";

        $f = $this->DataControl->selectClear($sql);

        if ($f) {
            $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
            foreach ($f as &$val) {
                $val['school_type'] = $status[$val['school_type']];
            }
        } else {
            $f = array();
        }

        $fieldstring = array('school_branch', 'school_cnname ', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('校区编号', '校园名称', '检索代码', '所在区域'));
        $fieldcustom = array("1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        if ($SchoolList) {
            $result['list'] = $SchoolList;
        } else {
            $result['list'] = array();
        }

        $result['district'] = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$paramArray['company_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result, 'choose' => $f, 'id' => $h);

        return $res;
    }

    //缴费设定添加学校
    function addSchoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['school_id']), true);

        $aa = array_column($schoolList, 'school_id');
        foreach ($schoolList as $item) {
            $data['school_id'] = $item['school_id'];
            $data['pricing_id'] = $item['pricing_id'];
            $a = $this->DataControl->getFieldOne('smc_fee_pricing_apply', 'pricing_id', "pricing_id = '{$item['pricing_id']}' and school_id = '{$item['school_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }


            if ($paramArray['pricing_applytype'] == '1') {
                $a = $this->DataControl->selectClear("SELECT pricing_id from smc_fee_pricing WHERE course_id = '{$paramArray['course_id']}' and pricing_applytype = '1' and agreement_id = '{$paramArray['agreement_id']}'");

                $c = array();
                if ($a) {
                    foreach ($a as $val) {
                        $b = $this->DataControl->selectClear("select school_id from smc_fee_pricing_apply WHERE pricing_id = '{$val['pricing_id']}'");
                        if ($b) {
                            foreach ($b as $vals) {
                                $c[] = $vals['school_id'];
                            }
                        }
                    }
                }

                $d = in_array($item['school_id'], $c);
                if ($d) {
                    ajax_return(array('error' => 1, 'errortip' => "所选学校已设置过价格！"), $this->companyOne['company_language']);
                }

                $a = $this->DataControl->selectClear("SELECT pricing_id from smc_fee_pricing WHERE course_id = '{$paramArray['course_id']}' and pricing_applytype = '-1' and agreement_id = '{$paramArray['agreement_id']}'");

                $c = array();
                if ($a) {
                    foreach ($a as $val) {
                        $b = $this->DataControl->selectClear("select school_id from smc_fee_pricing_apply WHERE pricing_id = '{$val['pricing_id']}'");
                        if ($b) {
                            foreach ($b as $vals) {
                                $c[] = $vals['school_id'];
                            }
                        }
                    }

                    $d = in_array($item['school_id'], $c);
                    if (!$d) {
                        ajax_return(array('error' => 1, 'errortip' => "所选学校已设置过价格！"), $this->companyOne['company_language']);
                    }


                }
                $this->DataControl->insertData('smc_fee_pricing_apply', $data);
            }

            if ($paramArray['pricing_applytype'] == '-1') {
                $a = $this->DataControl->selectClear("SELECT pricing_id from smc_fee_pricing WHERE course_id = '{$paramArray['course_id']}' and pricing_applytype = '1' and agreement_id = '{$paramArray['agreement_id']}'");

                $c = array();
                if ($a) {
                    foreach ($a as $val) {
                        $b = $this->DataControl->selectClear("select school_id from smc_fee_pricing_apply WHERE pricing_id = '{$val['pricing_id']}'");
                        if ($b) {
                            foreach ($b as $vals) {
                                $c[] = $vals['school_id'];
                            }
                        }
                    }
                }

                $flag = 1;
                foreach ($c as $va) {
                    if (in_array($va, $aa)) {
                        continue;
                    } else {
                        $flag = 0;
                        break;
                    }
                }

                if (!$flag) {
                    ajax_return(array('error' => 1, 'errortip' => "所选学校已设置过价格！"), $this->companyOne['company_language']);
                }

                $this->DataControl->insertData('smc_fee_pricing_apply', $data);

            }


        }

        $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '缴费设定添加学校', dataEncode($paramArray));

        return $res;
    }

    //缴费设定添加学校
    function addTreatySchoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['school_id']), true);

        foreach ($schoolList as $item) {
            $data['school_id'] = $item['school_id'];
            $data['treaty_id'] = $item['treaty_id'];
            $a = $this->DataControl->getFieldOne('smc_fee_treaty_apply', 'treaty_id', "treaty_id = '{$item['treaty_id']}' and school_id = '{$item['school_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }


            $a = $this->DataControl->selectClear("SELECT treaty_id from smc_fee_treaty WHERE coursetype_id = '{$paramArray['coursetype_id']}' and treaty_applytype = '1' and agreement_id = '{$paramArray['agreement_id']}'");

            $c = array();
            if ($a) {
                foreach ($a as $val) {
                    $b = $this->DataControl->selectClear("select school_id from smc_fee_treaty_apply WHERE treaty_id = '{$val['treaty_id']}'");
                    if ($b) {
                        foreach ($b as $vals) {
                            $c[] = $vals['school_id'];
                        }
                    }
                }
            }

            $d = in_array($item['school_id'], $c);
            if ($d) {
                ajax_return(array('error' => 1, 'errortip' => "所选学校已设置过协议！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_fee_treaty_apply', $data);

        }

        $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '协议添加学校', dataEncode($paramArray));

        return $res;
    }

    //教材列表
    function getGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {

            $paramArray['keyword'] = trim($paramArray['keyword'], '|');
            $a = explode("|", $paramArray['keyword']);
            $b = '0 > 1';
            foreach ($a as $val) {
                $b .= " or g.goods_cnname like '%{$val}%'";
                $b .= " or g.goods_outpid like '%{$val}%'";
            }

            $datawhere .= " and ($b)";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code = '{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['minpeice']) && $paramArray['minpeice'] !== "") {
            $datawhere .= " and g.goods_vipprice >= '{$paramArray['minpeice']}'";
        }
        if (isset($paramArray['maxpeice']) && $paramArray['maxpeice'] !== "") {
            $datawhere .= " and g.goods_vipprice <= '{$paramArray['maxpeice']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                g.goods_id,
                g.goods_pid,
                g.goods_cnname,
                p.prodtype_name,
                g.goods_originalprice,
                g.goods_vipprice,
                g.goods_unit,
                g.goods_outpid,
                r.goods_repertory
            FROM
                erp_goods AS g
                LEFT JOIN erp_goods_repertory AS r ON g.goods_id = r.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code and p.company_id=g.company_id
            where {$datawhere} and g.company_id = '{$paramArray['company_id']}' and g.goods_issale = '1'
            ORDER BY
                g.goods_id DESC    
            LIMIT {$pagestart},{$num}";

        $SchoolList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(g.goods_id) as a
            FROM
                erp_goods AS g
            WHERE
                {$datawhere} AND g.company_id = '{$paramArray['company_id']}' AND g.goods_issale = '1' ");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid', 'goods_cnname', 'prodtype_name', 'goods_originalprice', 'goods_vipprice', 'goods_unit');
        $fieldname = $this->LgArraySwitch(array('编号', '名称', '类型', '市场价', '协议价(单价)', '单位'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        if ($SchoolList) {
            $result['list'] = $SchoolList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_code,prodtype_name from smc_code_prodtype where company_id = '{$paramArray['company_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //选择绑定教材
    function addProductsAction($paramArray)
    {
        $data = array();

        $productList = json_decode(stripslashes($paramArray['products']), true);

        if ($productList) {
            foreach ($productList as $item) {
                $data['products_name'] = $item['goods_cnname'];
                $data['goods_id'] = $item['goods_id'];
                $data['goods_pid'] = $item['goods_pid'];
                $data['products_originalprice'] = $item['goods_originalprice'];
                $data['products_buypiece'] = $item['products_buypiece'];
                $data['products_donatepiece'] = $item['products_donatepiece'];
                $data['products_unitprice'] = $item['goods_vipprice'];
                $data['products_sellingprice'] = $item['goods_vipprice'] * $data['products_buypiece'];
                $data['pricing_id'] = $item['pricing_id'];

                $a = $this->DataControl->getFieldOne('smc_fee_pricing_products', 'products_id', "pricing_id = '{$item['pricing_id']}' and goods_id = '{$item['goods_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
                }
                $this->DataControl->insertData('smc_fee_pricing_products', $data);
            }

            $res = array('error' => '0', 'errortip' => "添加教材成功", 'result' => array());
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '缴费设定绑定教材', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "请选择要添加的教材", 'result' => array());
        }


        return $res;
    }

    //编辑收费项目
    function updatePriceAction($paramArray)
    {
        $PricelOne = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id,course_id", "pricing_id = '{$paramArray['pricing_id']}'");

        if ($PricelOne) {

            $courseOne = $this->DataControl->getFieldOne("smc_course", "course_inclasstype,course_sellclass", "course_id='{$PricelOne['course_id']}'");
            if (!$courseOne) {
                ajax_return(array('error' => '1', 'errortip' => "课程不存在!"), $this->companyOne['company_language']);
            }
            if ($courseOne['course_inclasstype'] == 2 || $courseOne['course_sellclass']==1) {
                $list = json_decode(stripslashes($paramArray['list']), 1);
                if (!$list) {
                    ajax_return(array('error' => '1', 'errortip' => "预约类课程必须设置价格设定!"), $this->companyOne['company_language']);
                }
            }

            $data = array();
            $data['pricing_name'] = $paramArray['pricing_name'];
            $data['pricing_applytype'] = $paramArray['pricing_applytype'];
            $data['pricing_edittime'] = time();
            $this->DataControl->updateData('smc_fee_pricing', "pricing_id = '{$paramArray['pricing_id']}'", $data);

            $datas = array();
            $datas['tuition_originalprice'] = $paramArray['tuition_originalprice'];
            $datas['tuition_unitprice'] = $paramArray['tuition_unitprice'];
            $datas['tuition_sellingprice'] = $paramArray['tuition_sellingprice'];
            $datas['tuition_refundprice'] = $paramArray['tuition_refundprice'];
            $datas['tuition_minclassnum'] = isset($paramArray['tuition_minclassnum'])?$paramArray['tuition_minclassnum']:0;
            $datas['tuition_edittime'] = time();
            $this->DataControl->updateData('smc_fee_pricing_tuition', "pricing_id = '{$paramArray['pricing_id']}'", $datas);

            if ($courseOne['course_inclasstype'] == 2 || $courseOne['course_sellclass']==1) {

                if (!isset($paramArray['tuition_id']) || $paramArray['tuition_id'] == '') {
                    ajax_return(array('error' => '1', 'errortip' => "价格设定必须传!"), $this->companyOne['company_language']);
                }

                $this->DataControl->delData("smc_fee_pricing_fit", "tuition_id='{$paramArray['tuition_id']}'");

                foreach ($list as $val) {
                    $tem_data = array();
                    $tem_data['tuition_id'] = $paramArray['tuition_id'];
                    $tem_data['fit_buypiece'] = $val['fit_buypiece'];
                    $tem_data['fit_price'] = $val['fit_price'];
                    $tem_data['fit_isdefault'] = $val['fit_isdefault'];
                    $this->DataControl->insertData("smc_fee_pricing_fit", $tem_data);
                }
            }

            $res = array('error' => '0', 'errortip' => "课程收费标准修改成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '编辑课程收费标准', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //删除缴费设定
    function delPriceAction($paramArray)
    {
        $PriceOne = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "pricing_id = '{$paramArray['pricing_id']}'");
        if ($PriceOne) {
            $a = $this->DataControl->getFieldOne("smc_payfee_order_course", "ordercourse_id", "pricing_id = '{$paramArray['pricing_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "缴费设定已被使用，无法删除!"), $this->companyOne['company_language']);
            } else {
                if ($this->DataControl->delData("smc_fee_pricing", "pricing_id = '{$paramArray['pricing_id']}'")) {
                    $result = array();
                    $res = array('error' => '0', 'errortip' => "删除缴费设定成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '删除缴费设定', dataEncode($paramArray));
                } else {
                    $result = array();
                    $res = array('error' => '1', 'errortip' => '删除缴费设定失败', 'result' => $result);
                }
            }

        }
        return $res;
    }

    //删除协议
    function delTreatyAction($paramArray)
    {
        $PriceOne = $this->DataControl->getFieldOne("smc_fee_treaty", "treaty_id", "treaty_id = '{$paramArray['treaty_id']}'");
        if ($PriceOne) {
            if ($this->DataControl->delData("smc_fee_treaty", "treaty_id = '{$paramArray['treaty_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除协议成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '删除协议', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除协议失败', 'result' => $result);
            }

        }
        return $res;
    }

    //删除缴费协议
    function delAgreementAction($paramArray)
    {
        if ($this->DataControl->getFieldOne("smc_fee_pricing", "course_id", "agreement_id = '{$paramArray['agreement_id']}'")) {
            $result = array();
            $res = array('error' => '1', 'errortip' => '已添加课程的课程收费标准，不可删除', 'result' => $result);
            return $res;
        }

        if ($this->DataControl->delData("smc_fee_agreement", "agreement_id = '{$paramArray['agreement_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除课程收费标准成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '删除课程收费标准', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除课程收费标准失败', 'result' => $result);
        }
        return $res;
    }

    //重新设置适配课程
    function resetCourseAction($paramArray)
    {
        if ($this->DataControl->delData("smc_student_coupons_applycoursecat", "apply_id = '{$paramArray['apply_id']}'")) {
            $coursecatList = json_decode(stripslashes($paramArray['coursecat']), true);
            foreach ($coursecatList as $coursecatOne) {
                $data['apply_id'] = $paramArray['apply_id'];
                $data['coursecat_id'] = $coursecatOne['coursecat_id'];
                $this->DataControl->insertData('smc_student_coupons_applycoursecat', $data);
            }
            $result = array();
            $res = array('error' => '0', 'errortip' => "重新设置适配课程成功", 'result' => $result);
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '重新设置适配课程失败', 'result' => $result);
        }
        return $res;
    }

    //是否必买
    function updateMustBuyAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_fee_pricing_products", "products_id", "products_id = '{$paramArray['products_id']}'");
        if ($productsOne) {
            $data = array();
            $data['products_ismustbuy'] = $paramArray['products_ismustbuy'];

            $field = array();
            $field['products_ismustbuy'] = "是否必买";

            if ($this->DataControl->updateData("smc_fee_pricing_products", "products_id = '{$paramArray['products_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "是否必买修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '修改是否必买', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '是否必买修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //是否免费
    function updateIsFreeAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_fee_pricing_products", "products_id", "products_id = '{$paramArray['products_id']}'");
        if ($productsOne) {
            $data = array();
            $data['products_isfree'] = $paramArray['products_isfree'];

            $field = array();
            $field['products_isfree'] = "是否免费";

            if ($this->DataControl->updateData("smc_fee_pricing_products", "products_id = '{$paramArray['products_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "是否免费修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '修改是否免费', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '是否免费修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //营销活动列表
    function getMarketActiveList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.activity_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and a.activity_startday >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and a.activity_endday <= '{$paramArray['end_time']}'";
        }

        if (isset($paramArray['activity_status']) && $paramArray['activity_status'] !== "") {
            $datawhere .= " and a.activity_status = '{$paramArray['activity_status']}'";
        }

        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ""){
            $datawhere.=" and ((a.activity_applyschool=1 and exists(select 1 from smc_market_activity_schoolapply as sch where sch.activity_id=a.activity_id and sch.school_id='{$paramArray['school_id']}')) or (a.activity_applyschool=0 and not exists(select 1 from smc_market_activity_schoolapply as sch where sch.activity_id=a.activity_id)))";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.activity_id,
                a.activity_cnname,
                a.activity_startday,
                a.activity_endday,
                a.activity_status,
                a.activity_status as status,
                a.activity_deductionmethod,
                a.activity_applyschool,
                a.activity_applycourse,
                a.activity_sharecoupons,
                a.activity_sharecoupons as activity_sharecoupons_name,
                from_unixtime(a.activity_createtime,'%Y-%m-%d') as activity_createtime
                ,(select count(1) from smc_market_activity_schoolapply where activity_id=a.activity_id) as school_count
            FROM
                smc_market_activity AS a
            WHERE
                {$datawhere} 
                and a.company_id = '{$paramArray['company_id']}' 
                and activity_onsaleway = '{$paramArray['activity_onsaleway']}'
            ORDER BY
                a.activity_id DESC
            LIMIT {$pagestart},{$num}";

        $MarketList = $this->DataControl->selectClear($sql);

        if ($MarketList) {
            foreach ($MarketList as &$val) {
                $val['day'] = $val['activity_startday'] . '至' . $val['activity_endday'];
                if ($val['activity_applyschool'] == '0') {
                    $val['activity_applyschool_name'] = $this->LgStringSwitch('全部适用');
                } else {
                    $val['activity_applyschool_name'] = $this->LgStringSwitch('部分适用');
                }
            }

            foreach ($MarketList as &$val) {
                if (date("Y-m-d") <= $val['activity_endday'] && date("Y-m-d") >= $val['activity_startday']) {
                    $val['status'] = $this->LgStringSwitch('生效中');
                } elseif (date("Y-m-d") > $val['activity_endday']) {
                    $val['status'] = $this->LgStringSwitch('已失效');
                } else {
                    $val['status'] = $this->LgStringSwitch('未生效');
                }
            }

            $status = array("0" => "0", "1" => "100");
            foreach ($MarketList as &$val) {
                $val['activity_status'] = $status[$val['activity_status']];
            }
            $status = array("0" => "不可用", "1" => "可用");
            foreach ($MarketList as &$val) {
                $val['activity_sharecoupons_name'] = $status[$val['activity_sharecoupons_name']];
            }
        }
        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.activity_id)
            FROM
                smc_market_activity AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}' and activity_onsaleway = '{$paramArray['activity_onsaleway']}' ");
        $allnums = $all_num[0][0];

        $fieldstring = array('activity_cnname', 'activity_sharecoupons_name', 'day', 'status', 'activity_status', 'activity_applyschool', 'activity_applyschool_name', 'activity_createtime');
        $fieldname = $this->LgArraySwitch(array('活动名称', '是否可以和优惠券共用', '有效周期', '是否有效', '是否启用', '适用学校判断', '适用学校', '创建时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "0", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "0", "1", "1");
        $fieldswitch = array(0, 0, 0, 0, 1);
        $fieldismethod = array(0, 0, 0, 0, 0, 0, 1, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
            $field[$i]["ismethod"] = (int)$fieldismethod[$i];
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($MarketList) {
            $result['list'] = $MarketList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            if ($paramArray['activity_onsaleway'] == '0') {
                $res = array('error' => '1', 'errortip' => "暂无满减活动", 'result' => $result);
            } elseif ($paramArray['activity_onsaleway'] == '1') {
                $res = array('error' => '1', 'errortip' => "暂无满折活动", 'result' => $result);
            } elseif ($paramArray['activity_onsaleway'] == '2') {
                $res = array('error' => '1', 'errortip' => "暂无满赠活动", 'result' => $result);
            }elseif ($paramArray['activity_onsaleway'] == '3') {
                $res = array('error' => '1', 'errortip' => "暂无连报活动", 'result' => $result);
            }
        }

        return $res;
    }

    //获取活动基本信息
    function getMarketActivityAction($paramArray)
    {
        $sql = "
            SELECT
                a.activity_id,
                a.activity_cnname,
                a.activity_startday,
                a.activity_endday,
                a.activity_createtime,
                a.activity_deductionmethod,
                a.activity_applyschool,
                a.activity_applycourse
            FROM
                smc_market_activity AS a
            WHERE
                a.activity_id = '{$paramArray['activity_id']}'";
        $activityDetail = $this->DataControl->selectClear($sql);

        if ($activityDetail) {
            foreach ($activityDetail as &$val) {
                $val['day'] = $val['activity_startday'] . '至' . $val['activity_endday'];
            }

            foreach ($activityDetail as &$val) {
                if (time() <= strtotime($val['activity_endday']) && time() >= strtotime($val['activity_startday'])) {
                    $val['status'] = $this->LgStringSwitch('生效中');
                } elseif (time() > strtotime($val['activity_endday'])) {
                    $val['status'] = $this->LgStringSwitch('已失效');
                } else {
                    $val['status'] = $this->LgStringSwitch('未生效');
                }
            }

            $status = $this->LgArraySwitch(array("0" => "平摊抵扣", "1" => "先抵优惠", "2" => "后抵优惠"));
            foreach ($activityDetail as &$val) {
                $val['activity_deductionmethod'] = $status[$val['activity_deductionmethod']];
            }
        }


        $field = array();
        $field["activity_cnname"] = "活动名称";
        $field["day"] = "活动时间";
//        $field[""] = "活动状态";
        $field["activity_deductionmethod"] = "抵扣方式";
        $field["status"] = "状态";

        $result = array();
        if ($activityDetail) {
            $result["field"] = $field;
            $result["data"] = $activityDetail;
            $res = array('error' => '0', 'errortip' => '获取活动基本信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取活动基本信息失败', 'result' => $result);
        }
        return $res;
    }

    //获取活动设置
    function getActivitySetAction($paramArray)
    {

        $activityOne=$this->DataControl->getFieldOne("smc_market_activity","activity_onsaleway","activity_id = '{$paramArray['activity_id']}'");

        if(!$activityOne){
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '未获取到活动', 'result' => array());
            return $res;
        }

        if($activityOne['activity_onsaleway']==3){
            $sql="select * from smc_market_activity_rules where activity_id = '{$paramArray['activity_id']}' order by rules_id asc";

        }else{
            $sql = "
            SELECT
                a.discount_id,
                a.discount_minprice,
                a.discount_derateprice,
                a.discount_deraterate * 10 as discount_deraterate,
                a.discount_giveforwardprice
            FROM
                smc_market_activity_discount AS a
            WHERE
                a.activity_id = '{$paramArray['activity_id']}'";
        }


        $activityDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["discount_minprice"] = "条件价格";
        $field["discount_derateprice"] = "减免价格";
        $field["discount_deraterate"] = "减免比率";
        $field["discount_giveforwardprice"] = "赠送结转余额";

        $result = array();
        if ($activityDetail) {
            $result["field"] = $field;
            $result["data"] = $activityDetail;
            $res = array('error' => '0', 'errortip' => '获取活动设置成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取活动设置失败', 'result' => array());
        }
        return $res;
    }

    //营销活动适用学校列表
    function getActiveSchoolList($paramArray)
    {
        $sql = "
            SELECT
                a.school_id,
                a.activity_id,
                s.school_branch,
                s.school_type,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                d.district_cnname
            FROM
                smc_market_activity_schoolapply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                left join gmc_company_district as d on s.district_id = d.district_id
            where a.activity_id = '{$paramArray['activity_id']}'
            ORDER BY s.school_sort DESC";

        $SchooltList = $this->DataControl->selectClear($sql);

        if ($SchooltList) {
            $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
            foreach ($SchooltList as &$val) {
                $val['school_type'] = $status[$val['school_type']];
            }
        }

        $fieldstring = array('school_branch', 'school_cnname', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('编号', '中文名', '英文名', '区域'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($SchooltList) {
            $result['list'] = $SchooltList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无营销活动适用学校信息", 'result' => $result);
        }

        return $res;
    }

    //营销活动适用课程列表
    function getActiveCourseList($paramArray)
    {
        $sql = "
            SELECT
                a.activity_id,
                c.course_id,
                c.course_branch,
                c.course_cnname
            FROM
                smc_market_activity_courseapply AS a
            LEFT JOIN smc_course AS c ON a.course_id = c.course_id
            where a.activity_id = '{$paramArray['activity_id']}'";

        $SchooltList = $this->DataControl->selectClear($sql);

        $fieldstring = array('course_branch', 'course_cnname');
        $fieldname = array('编号', '课程名称');
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($SchooltList) {
            $result['list'] = $SchooltList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无适用课程", 'result' => $result);
        }


        return $res;
    }

    //新增营销活动
    function addMarketActivityAction($paramArray)
    {
        $data = array();
        $data['activity_cnname'] = $paramArray['activity_cnname'];
        $data['activity_status'] = $paramArray['activity_status'];
        $data['activity_startday'] = $paramArray['activity_startday'];
        $data['activity_endday'] = $paramArray['activity_endday'];

        if($paramArray['activity_onsaleway']==3){
            $data['activity_deductionmethod'] = 2;
        }else{
            $data['activity_deductionmethod'] = $paramArray['activity_deductionmethod'];
        }

        $data['activity_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];
        $data['activity_applyschool'] = $paramArray['activity_applyschool'];
        $data['activity_applycourse'] = $paramArray['activity_applycourse'];
        $data['activity_onsaleway'] = $paramArray['activity_onsaleway'];
        $data['activity_sharecoupons'] = $paramArray['activity_sharecoupons'];

        $field = array();
        $field['activity_cnname'] = "活动名称";
        $field['activity_status'] = "是否启用";
        $field['activity_startday'] = "起效时间";
        $field['activity_endday'] = "失效时间";
        $field['activity_deductionmethod'] = "抵扣方式";
        $field['activity_createtime'] = "创建时间";
        $field['company_id'] = "所属公司";
        $field['activity_applyschool'] = "适用学校方式";
        $field['activity_applycourse'] = "适用课程方式";
        $field['activity_onsaleway'] = "优惠设定方式";
        $field['activity_sharecoupons'] = "是否可以和优惠券共用";

        if ($paramArray['activity_startday'] > $paramArray['activity_endday']) {
            ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
        }

        if($this->DataControl->getFieldOne("smc_market_activity","activity_id","company_id='{$this->company_id}' and activity_cnname='{$paramArray['activity_cnname']}'")){
            ajax_return(array('error' => 1, 'errortip' => "活动名称已存在!"), $this->companyOne['company_language']);
        }


        if ($id = $this->DataControl->insertData('smc_market_activity', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $result["activity_id"] = $id;
            $res = array('error' => '0', 'errortip' => "添加营销活动成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '新增优惠活动政策', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加营销活动失败', 'result' => $result);
        }
        return $res;
    }

    //新增营销设置
    function addActivitySetAction($paramArray)
    {
        $data = array();

        $setList = json_decode(stripslashes($paramArray['set']), true);

        $activityOne=$this->DataControl->getFieldOne("smc_market_activity","activity_onsaleway","activity_id='{$paramArray['activity_id']}'");

        if(!$activityOne){
            $res = array('error' => '1', 'errortip' => "无该营销活动");
            return $res;
        }

        if($activityOne['activity_onsaleway']==3){
            $this->DataControl->delData('smc_market_activity_rules', "activity_id = '{$paramArray['activity_id']}'");
            foreach ($setList as $item) {

                if($item['buy_coursetype_id']>0 && $item['old_coursetype_id']>0){
                    $data=array();
                    $data['activity_id']=$paramArray['activity_id'];
                    $data['buy_coursetype_id']=$item['buy_coursetype_id'];
                    $data['old_coursetype_id']=$item['old_coursetype_id'];
                    $data['rules_price']=$item['rules_price'];
                    if(!$this->DataControl->getFieldOne("smc_market_activity_rules","rules_id","buy_coursetype_id='{$item['buy_coursetype_id']}' and old_coursetype_id='{$item['old_coursetype_id']}' and activity_id = '{$paramArray['activity_id']}'")){
                        $this->DataControl->insertData("smc_market_activity_rules",$data);
                    }
                }
            }
        }else{
            $this->DataControl->delData('smc_market_activity_discount', "activity_id = '{$paramArray['activity_id']}'");
            foreach ($setList as $item) {
                $data['activity_id'] = $item['activity_id'];
                $data['discount_minprice'] = $item['discount_minprice'];

                if (isset($item['discount_derateprice'])) {
                    $data['discount_derateprice'] = $item['discount_derateprice'];
                }
                if (isset($item['discount_deraterate'])) {
                    $data['discount_deraterate'] = $item['discount_deraterate'] * '0.1';
                }
                if (isset($item['discount_giveforwardprice'])) {
                    $data['discount_giveforwardprice'] = $item['discount_giveforwardprice'];
                }
                $data['discount_addtime'] = time();

                $this->DataControl->insertData('smc_market_activity_discount', $data);
            }
        }

        if($activityOne['activity_onsaleway']==3){
            $res = array('error' => '0', 'errortip' => "新增连报活动成功");
        }else{
            $res = array('error' => '0', 'errortip' => "新增营销设置成功");
        }


        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '新增营销设置', dataEncode($paramArray));

        return $res;
    }

    //活动添加学校
    function addActivitySchoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['school_id']), true);
        foreach ($schoolList as $item) {
            $data['school_id'] = $item['school_id'];
            $data['activity_id'] = $item['activity_id'];
            $a = $this->DataControl->getFieldOne('smc_market_activity_schoolapply', 'activity_id', "activity_id = '{$item['activity_id']}' and school_id = '{$item['school_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_market_activity_schoolapply', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '新增活动适配学校', dataEncode($paramArray));

        return $res;
    }

    //活动添加课程
    function addActivityCourseAction($paramArray)
    {
        $data = array();

        $courseList = json_decode(stripslashes($paramArray['course_id']), true);
        foreach ($courseList as $item) {
            $data['course_id'] = $item['course_id'];
            $data['activity_id'] = $item['activity_id'];
            $a = $this->DataControl->getFieldOne('smc_market_activity_courseapply', 'activity_id', "activity_id = '{$item['activity_id']}' and course_id = '{$item['course_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_market_activity_courseapply', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加课程成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '新增活动适配课程', dataEncode($paramArray));

        return $res;
    }

    //课程列表
    function getCourseList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_branch like '%{$paramArray['keyword']}%' or c.course_cnname like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== '') {
            $datawhere .= " and c.coursecat_id='{$paramArray['coursecat_id']}'";
        }

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= " and c.coursetype_id='{$paramArray['coursetype_id']}'";
        }


        $sql = "
            SELECT
                c.course_id,
                c.course_branch,
                c.course_cnname,ct.coursetype_cnname,co.coursecat_cnname
            FROM
                smc_course AS c,smc_code_coursecat as co,smc_code_coursetype as ct
            WHERE {$datawhere} and c.coursecat_id=co.coursecat_id and c.coursetype_id=ct.coursetype_id and c.company_id = '{$paramArray['company_id']}' and c.course_status = '1' and c.course_inclasstype <> 3";

        $CourseList = $this->DataControl->selectClear($sql);

        $fieldstring = array('course_branch', 'course_cnname', 'coursecat_cnname', 'coursetype_cnname');
        $fieldname = $this->LgArraySwitch(array('课程编号', '课程名称', '所属班种', '所属班组'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1","1","1","1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        if ($CourseList) {
            $result['list'] = $CourseList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //删除活动适配课程
    function DelActivityCourseAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_activity_courseapply", "activity_id = '{$paramArray['activity_id']}' and course_id = '{$paramArray['course_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除活动适配课程成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '删除活动适配课程', dataEncode($paramArray));

        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除活动适配课程失败', 'result' => $result);
        }
        return $res;
    }

    //删除活动
    function DelActivityAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_activity", "activity_id = '{$paramArray['activity_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除活动成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '删除活动', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除活动失败', 'result' => $result);
        }
        return $res;
    }


    //删除活动适配学校
    function DelActivitySchoolAction($paramArray)
    {
        if(!isset($paramArray['school_id']) || $paramArray['school_id']=='' || $paramArray['school_id']=='0'){
            $this->error = true;
            $this->errortip = "学校ID必须传";
            return false;
        }
        $where = "activity_id = '{$paramArray['activity_id']}'";
        $school_list = json_decode(stripslashes($paramArray['school_id']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $school_id = implode(',', $str);
            $where .= " and school_id in ({$school_id})";
        }else{
            $where .= " and school_id = '{$paramArray['school_id']}'";
        }

        if ($this->DataControl->delData("smc_market_activity_schoolapply", $where)) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除活动适配学校成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '删除活动适配学校', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除活动适配学校失败', 'result' => $result);
        }
        return $res;
    }

    //编辑营销活动
    function updateActivityAction($paramArray)
    {
        $ActivityOne = $this->DataControl->getFieldOne("smc_market_activity", "activity_id", "activity_id = '{$paramArray['activity_id']}'");
        if ($ActivityOne) {
            $data = array();
            $data['activity_cnname'] = $paramArray['activity_cnname'];
            $data['activity_startday'] = $paramArray['activity_startday'];
            $data['activity_endday'] = $paramArray['activity_endday'];
            $data['activity_deductionmethod'] = $paramArray['activity_deductionmethod'];
            $data['activity_sharecoupons'] = $paramArray['activity_sharecoupons'];
            $data['activity_updatatime'] = time();

            if($this->DataControl->getFieldOne("smc_market_activity","activity_id","company_id='{$this->company_id}' and activity_cnname='{$paramArray['activity_cnname']}' and activity_id <> '{$paramArray['activity_id']}'")){
                ajax_return(array('error' => 1, 'errortip' => "活动名称已存在!"), $this->companyOne['company_language']);
            }

            if ($paramArray['activity_applyschool'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_market_activity_schoolapply", "activity_id", "activity_id = '{$paramArray['activity_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "有已设置的适配学校，请先删除！"), $this->companyOne['company_language']);
                } else {
                    $data['activity_applyschool'] = $paramArray['activity_applyschool'];
                }
            } else {
                $data['activity_applyschool'] = $paramArray['activity_applyschool'];
            }

            if ($paramArray['activity_applycourse'] == '0') {
                $b = $this->DataControl->getFieldOne("smc_market_activity_courseapply", "activity_id", "activity_id = '{$paramArray['activity_id']}'");
                if ($b) {
                    ajax_return(array('error' => 1, 'errortip' => "有已设置的适配课程，请先删除！"), $this->companyOne['company_language']);
                } else {
                    $data['activity_applycourse'] = $paramArray['activity_applycourse'];
                }
            } else {
                $data['activity_applycourse'] = $paramArray['activity_applycourse'];
            }

            if ($paramArray['activity_startday'] > $paramArray['activity_endday']) {
                ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
            }

            $field = array();
            $field['activity_cnname'] = "活动名称";
            $field['activity_startday'] = "起效时间";
            $field['activity_endday'] = "失效时间";
            $field['activity_deductionmethod'] = "抵扣方式";
            $field['activity_updatatime'] = "修改时间";
            $field['activity_applyschool'] = "适用学校方式";
            $field['activity_applycourse'] = "适用课程方式";
            $field['activity_sharecoupons'] = "是否可以和优惠券共用";

            if ($this->DataControl->updateData("smc_market_activity", "activity_id = '{$paramArray['activity_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑营销活动成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '编辑营销活动', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑营销活动失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑优惠方式
    function updateActivitySetAction($paramArray)
    {
        $data = array();

        $setList = json_decode(stripslashes($paramArray['set']), true);
        foreach ($setList as $item) {
            $data['discount_minprice'] = $item['discount_minprice'];

            if (isset($item['discount_derateprice'])) {
                $data['discount_derateprice'] = $item['discount_derateprice'];
            }
            if (isset($item['discount_deraterate'])) {
                $data['discount_deraterate'] = $item['discount_deraterate'];
            }
            if (isset($item['discount_giveforwardprice'])) {
                $data['discount_giveforwardprice'] = $item['discount_giveforwardprice'];
            }
            $data['discount_edittime'] = time();

            $this->DataControl->updateData('smc_market_activity_discount', "discount_id = '{$item['discount_id']}'", $data);
            $res = array('error' => '0', 'errortip' => "编辑优惠方式成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '编辑优惠方式', dataEncode($paramArray));

        }

        return $res;

    }

    //删除优惠方式
    function DelActivitySetAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_activity_discount", "discount_id = '{$paramArray['discount_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠方式成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '删除优惠方式', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠方式失败', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function OpenStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("smc_market_activity", "activity_id", "activity_id = '{$paramArray['activity_id']}'");
        if ($activityOne) {
            $data = array();
            $data['activity_status'] = $paramArray['activity_status'];

            $field = array();
            $field['activity_status'] = '是否启用';

            if ($this->DataControl->updateData("smc_market_activity", "activity_id = '{$paramArray['activity_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->优惠活动政策", '是否启用活动', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //优惠券列表
    function getTicketList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.ticket_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and t.ticket_startday >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and t.ticket_endday <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['ticket_way']) && $paramArray['ticket_way'] !== "") {
            $datawhere .= " and t.ticket_way = '{$paramArray['ticket_way']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                t.ticket_id,
                t.ticket_cnname,
                t.ticket_status,
                t.ticket_deductionmethod,
                t.ticket_way,
                t.ticket_way as status,
                t.ticket_applyschool,
                t.ticket_applycourse,
                t.ticket_useway,
                t.ticket_usecourse,
                t.ticket_usegoods,
                t.ticket_istransfer,
                t.ticket_startday,
                t.ticket_endday
            FROM
                smc_market_ticket AS t
            WHERE
                {$datawhere} and t.company_id = '{$paramArray['company_id']}'
            ORDER BY
                t.ticket_id DESC
            LIMIT {$pagestart},{$num}";

        $TicketList = $this->DataControl->selectClear($sql);

        if ($TicketList) {
            foreach ($TicketList as &$val) {
                $val['day'] = $val['ticket_startday'] . '至' . $val['ticket_endday'];
            }

            $status = array("-1" => "0", "1" => "100");
            foreach ($TicketList as &$val) {
                $val['ticket_status'] = $status[$val['ticket_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "减价抵扣", "1" => "折扣模式"));
            foreach ($TicketList as &$val) {
                $val['status'] = $status[$val['status']];
            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(t.ticket_id)
            FROM
                smc_market_ticket AS t
            WHERE
                {$datawhere} and t.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('ticket_cnname', 'day', 'status', 'ticket_status', 'ticket_way');
        $fieldname = $this->LgArraySwitch(array('优惠券名称', '生效时间', '优惠券类型', '启用状态', '类型状态'));
        $fieldcustom = array("1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "0");
        $fieldswitch = array(0, 0, 0, 1, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($TicketList) {
            $result['list'] = $TicketList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠券信息", 'result' => $result);
        }

        return $res;
    }

    //新增营销活动
    function addTicketAction($paramArray)
    {
        $data = array();
        $data['ticket_cnname'] = $paramArray['ticket_cnname'];
        $data['ticket_startday'] = $paramArray['ticket_startday'];
        $data['ticket_endday'] = $paramArray['ticket_endday'];
        $data['ticket_deductionmethod'] = $paramArray['ticket_deductionmethod'];
        $data['ticket_applyschool'] = $paramArray['ticket_applyschool'];
        $data['ticket_applycourse'] = $paramArray['ticket_applycourse'];
        $data['ticket_usecourse'] = $paramArray['ticket_usecourse'];
        $data['ticket_usegoods'] = $paramArray['ticket_usegoods'];
        $data['ticket_useway'] = $paramArray['ticket_useway'];
        $data['ticket_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['ticket_cnname'] = "活动名称";
        $field['ticket_startday'] = "起效时间";
        $field['ticket_endday'] = "失效时间";
        $field['ticket_deductionmethod'] = "抵扣方式";
        $field['ticket_applyschool'] = "适用学校";
        $field['ticket_applycourse'] = "来源课程";
        $field['ticket_usecourse'] = "营销券使用学费限制";
        $field['ticket_usegoods'] = "营销券使用物品限制";
        $field['ticket_useway'] = "营销券使用限制：0不限1限学费2限学杂";
        $field['ticket_createtime'] = "创建时间";
        $field['company_id'] = "所属公司";

        if ($paramArray['ticket_startday'] > $paramArray['ticket_endday']) {
            ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
        }

        if ($id = $this->DataControl->insertData('smc_market_ticket', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $result["ticket_id"] = $id;
            $res = array('error' => '0', 'errortip' => "添加优惠券成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '新增优惠券', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加优惠券失败', 'result' => $result);
        }
        return $res;
    }

    //优惠课程限制列表
    function getUseCourseList($paramArray)
    {
        $sql = "
            SELECT
                a.ticket_id,
                a.course_id,
                c.course_cnname,
                c.course_branch,
                r.coursecat_cnname,
                t.coursetype_cnname
            FROM
                smc_market_ticket_courseuse AS a
                LEFT JOIN smc_course AS c ON a.course_id = c.course_id
                left join smc_code_coursecat as r on r.coursecat_id = c.coursecat_id
                left join smc_code_coursetype as t on t.coursetype_id = c.coursetype_id
            where a.ticket_id = '{$paramArray['ticket_id']}'
            ";

        $TicketList = $this->DataControl->selectClear($sql);

        $fieldstring = array('course_cnname', 'course_branch', 'coursecat_cnname', 'coursetype_cnname');
        $fieldname = $this->LgArraySwitch(array('课程名称', '课程编号', '班种', '班组'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($TicketList) {
            $result['list'] = $TicketList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠券课程限制", 'result' => $result);
        }

        return $res;
    }

    //优惠券来源课程
    function getTicketFromList($paramArray)
    {
        $sql = "
            SELECT
                a.course_id,
                a.ticket_id,
                c.course_cnname,
                c.course_branch,
                r.coursecat_cnname,
                t.coursetype_cnname
            FROM
                smc_market_ticket_courseapply AS a
                LEFT JOIN smc_course AS c ON a.course_id = c.course_id
                left join smc_code_coursecat as r on r.coursecat_id = c.coursecat_id
                left join smc_code_coursetype as t on t.coursetype_id = c.coursetype_id
            where a.ticket_id = '{$paramArray['ticket_id']}'
            ";

        $TicketList = $this->DataControl->selectClear($sql);

        $fieldstring = array('course_cnname', 'course_branch', 'coursecat_cnname', 'coursetype_cnname');
        $fieldname = $this->LgArraySwitch(array('课程名称', '课程编号', '班种', '班组'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($TicketList) {
            $result['list'] = $TicketList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠券来源信息", 'result' => $result);
        }

        return $res;
    }

    //删除优惠券来源课程
    function DelTicketFromAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_ticket_courseapply", "ticket_id = '{$paramArray['ticket_id']}' and course_id = '{$paramArray['course_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠券来源课程成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '删除优惠券来源课程', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠券来源课程失败', 'result' => $result);
        }
        return $res;
    }

    //添加优惠券来源课程
    function addTicketFromCourseAction($paramArray)
    {
        $data = array();

        $courseList = json_decode(stripslashes($paramArray['course_id']), true);
        foreach ($courseList as $item) {
            $data['course_id'] = $item['course_id'];
            $data['ticket_id'] = $item['ticket_id'];
            $a = $this->DataControl->getFieldOne('smc_market_ticket_courseapply', 'ticket_id', "ticket_id = '{$item['ticket_id']}' and course_id = '{$item['course_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_market_ticket_courseapply', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加优惠券来源课程成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '添加优惠券来源课程', dataEncode($paramArray));

        return $res;
    }

    //编辑优惠券优惠方式
    function addTicketWayAction($paramArray)
    {
        $data = array();
        $data['ticket_way'] = $paramArray['ticket_way'];
        $this->DataControl->updateData("smc_market_ticket", "ticket_id = '{$paramArray['ticket_id']}'", $data);

        $setList = json_decode(stripslashes($paramArray['set']), true);
        $this->DataControl->delData('smc_market_ticket_policy', "ticket_id = '{$paramArray['ticket_id']}'");
        foreach ($setList as $item) {
            $data = array();
            $data['ticket_id'] = $item['ticket_id'];
            $data['policy_minprice'] = $item['policy_minprice'];

            if ($paramArray['ticket_way'] == '0') {
                $data['policy_derateprice'] = $item['price'];
            }
            if ($paramArray['ticket_way'] == '1') {
                $data['policy_deraterate'] = $item['price'] * '0.1';
            }
            $data['policy_addtime'] = time();
            $this->DataControl->insertData('smc_market_ticket_policy', $data);
        }
        $res = array('error' => '0', 'errortip' => "设置优惠方式成功");
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '设置优惠方式', dataEncode($paramArray));

        return $res;
    }

    //添加优惠券适用学校
    function addTicketSchoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['school_id']), true);
        foreach ($schoolList as $item) {
            $data['school_id'] = $item['school_id'];
            $data['ticket_id'] = $item['ticket_id'];
            $a = $this->DataControl->getFieldOne('smc_market_ticket_schoolapply', 'ticket_id', "ticket_id = '{$item['ticket_id']}' and school_id = '{$item['school_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_market_ticket_schoolapply', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '添加优惠券适用学校', dataEncode($paramArray));

        return $res;
    }

    //删除优惠券适用学校
    function DelTicketSchoolAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_ticket_schoolapply", "ticket_id = '{$paramArray['ticket_id']}' and school_id = '{$paramArray['school_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠券适用学校成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '删除优惠券适用学校', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠券适用学校失败', 'result' => $result);
        }
        return $res;
    }

    //优惠券适配学校列表
    function getTicketSchoolList($paramArray)
    {
        $sql = "
            SELECT
                a.school_id,
                a.ticket_id,
                s.school_branch,
                s.school_type,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                d.district_cnname
            FROM
                smc_market_ticket_schoolapply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                left join gmc_company_district as d on s.district_id = d.district_id
            where a.ticket_id = '{$paramArray['ticket_id']}'
            ORDER BY s.school_sort DESC";

        $SchooltList = $this->DataControl->selectClear($sql);

        if ($SchooltList) {
            $status = $this->LgArraySwitch(array("1" => "直营校", "2" => "直营园", "3" => "加盟校", "4" => "加盟园"));
            foreach ($SchooltList as &$val) {
                $val['school_type'] = $status[$val['school_type']];
            }
        }

        $fieldstring = array('school_branch', 'school_cnname', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('编号', '中文名', '英文名', '区域'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($SchooltList) {
            $result['list'] = $SchooltList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠券适用学校信息", 'result' => $result);
        }

        return $res;
    }

    //删除优惠券
    function DelTicketAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_ticket", "ticket_id = '{$paramArray['ticket_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠券成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '删除优惠券', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠券失败', 'result' => $result);
        }
        return $res;
    }

    //课程限制
    function CourseLimitAction($paramArray)
    {
        $data = array();

        $courseList = json_decode(stripslashes($paramArray['course_id']), true);
        foreach ($courseList as $item) {
            $data['course_id'] = $item['course_id'];
            $data['ticket_id'] = $item['ticket_id'];
            $a = $this->DataControl->getFieldOne('smc_market_ticket_courseuse', 'ticket_id', "ticket_id = '{$item['ticket_id']}' and course_id = '{$item['course_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_market_ticket_courseuse', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加课程成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '添加优惠券适用课程', dataEncode($paramArray));

        return $res;
    }

    //优惠物品限制列表
    function getUseGoodsList($paramArray)
    {
        $sql = "
            SELECT
                u.goods_id,
                u.ticket_id,
                g.goods_cnname,
                g.goods_pid,
                g.goods_originalprice,
                g.goods_vipprice
            FROM
                smc_market_ticket_goodsuse AS u
                LEFT JOIN erp_goods AS g ON u.goods_id = g.goods_id
            where u.ticket_id = '{$paramArray['ticket_id']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_cnname', 'goods_pid', 'goods_originalprice', 'goods_vipprice');
        $fieldname = $this->LgArraySwitch(array('名称', '编号', '市场价', '收费标准价'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //物品限制
    function GoodsLimitAction($paramArray)
    {
        $data = array();

        $goodsList = json_decode(stripslashes($paramArray['goods_id']), true);
        foreach ($goodsList as $item) {
            $data['goods_id'] = $item['goods_id'];
            $data['ticket_id'] = $item['ticket_id'];
            $a = $this->DataControl->getFieldOne('smc_market_ticket_goodsuse', 'ticket_id', "ticket_id = '{$item['ticket_id']}' and goods_id = '{$item['goods_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加！"), $this->companyOne['company_language']);
            }

            $this->DataControl->insertData('smc_market_ticket_goodsuse', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加物品成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '添加优惠券适用物品', dataEncode($paramArray));

        return $res;
    }

    //删除优惠适用课程
    function DelLimitCourseAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_ticket_courseuse", "ticket_id = '{$paramArray['ticket_id']}' and course_id = '{$paramArray['course_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠适用课程成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '删除优惠券适用课程', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠适用课程失败', 'result' => $result);
        }
        return $res;
    }


    //删除优惠适用物品
    function DelLimitGoodsAction($paramArray)
    {
        if ($this->DataControl->delData("smc_market_ticket_goodsuse", "ticket_id = '{$paramArray['ticket_id']}' and goods_id = '{$paramArray['goods_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠适用物品成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '删除优惠券适用物品', dataEncode($paramArray));
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠适用物品失败', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function TicketStatusAction($paramArray)
    {
        $ticketOne = $this->DataControl->getFieldOne("smc_market_ticket", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
        if ($ticketOne) {
            $data = array();
            $data['ticket_status'] = $paramArray['ticket_status'];

            $field = array();
            $field['ticket_status'] = '是否启用';

            if ($this->DataControl->updateData("smc_market_ticket", "ticket_id = '{$paramArray['ticket_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->营销券管理", '是否启用优惠券', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //查看优惠券详情
    function getTicketDetail($paramArray)
    {
        $sql = "
            SELECT
                ticket_id,
                ticket_cnname,
                ticket_startday,
                ticket_endday,
                ticket_status,
                ticket_deductionmethod,
                ticket_deductionmethod as ticket_deductionmethod_status,
                ticket_way,
                ticket_way as ticket_way_status,
                ticket_applyschool,
                ticket_usegoods,
                ticket_usegoods as ticket_usegoods_status,
                ticket_usecourse,
                ticket_usecourse as ticket_usecourse_status,
                ticket_applyschool as ticket_applyschool_status,
                ticket_applycourse,
                ticket_applycourse as ticket_applycourse_status,
                ticket_useway,
                ticket_useway as ticket_useway_status
            FROM
                smc_market_ticket
            WHERE
                ticket_id = '{$paramArray['ticket_id']}'";
        $TicketList = $this->DataControl->selectClear($sql);

        if ($TicketList) {
            foreach ($TicketList as &$val) {
                $val['day'] = $val['ticket_startday'] . '至' . $val['ticket_endday'];
            }

            $status = $this->LgArraySwitch(array("-1" => "已禁用", "1" => "生效中", "0" => "编辑中"));
            foreach ($TicketList as &$val) {
                $val['ticket_status'] = $status[$val['ticket_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "减价抵扣", "1" => "折扣模式"));
            foreach ($TicketList as &$val) {
                $val['ticket_way_status'] = $status[$val['ticket_way_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "全部学校适用", "1" => "部分学校适用"));
            foreach ($TicketList as &$val) {
                $val['ticket_applyschool_status'] = $status[$val['ticket_applyschool_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "全部课程适用", "1" => "部分课程适用"));
            foreach ($TicketList as &$val) {
                $val['ticket_applycourse_status'] = $status[$val['ticket_applycourse_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "全部课程", "1" => "部分课程"));
            foreach ($TicketList as &$val) {
                $val['ticket_usecourse_status'] = $status[$val['ticket_usecourse_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "全部物品", "1" => "部分物品"));
            foreach ($TicketList as &$val) {
                $val['ticket_useway_status'] = $status[$val['ticket_useway_status']];
            }

            $status = $this->LgArraySwitch(array("0" => "平摊抵扣", "1" => "先抵优惠", "2" => "后抵优惠"));
            foreach ($TicketList as &$val) {
                $val['ticket_deductionmethod_status'] = $status[$val['ticket_deductionmethod_status']];
            }
        }

        $field = array();
        $field["agreement_cnname"] = "收费标准名称";
        $field["agreement_startday"] = "生效日期";
        $field["agreement_endday"] = "失效日期";
        $field["agreement_createtime"] = "经办时间";

        $result = array();
        if ($TicketList) {
            $result["field"] = $field;
            $result["data"] = $TicketList;
            $res = array('error' => '0', 'errortip' => '获取课程收费标准成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取课程收费标准失败', 'result' => $result);
        }
        return $res;
    }

    //查看优惠方式
    function getTicketPolicy($paramArray)
    {
        if ($paramArray['ticket_way'] == '0') {
            $sql = "
            SELECT
                policy_minprice,
                policy_derateprice as price
            FROM
                smc_market_ticket_policy
            WHERE
                ticket_id = '{$paramArray['ticket_id']}'";
            $TicketList = $this->DataControl->selectClear($sql);


            $field = array();
            $field["policy_minprice"] = "条件金额";
            $field["policy_derateprice"] = "营销券减免价格";
        } else {
            $sql = "
            SELECT
                policy_minprice,
                policy_deraterate * 10 as price
            FROM
                smc_market_ticket_policy
            WHERE
                ticket_id = '{$paramArray['ticket_id']}'";
            $TicketList = $this->DataControl->selectClear($sql);


            $field = array();
            $field["policy_minprice"] = "条件金额";
            $field["policy_deraterate"] = "营销券减免比率";
        }

        $result = array();
        if ($TicketList) {
            $result["field"] = $field;
            $result["data"] = $TicketList;
            $res = array('error' => '0', 'errortip' => '获取课程收费标准成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取课程收费标准失败', 'result' => $result);
        }
        return $res;
    }

    //编辑营销活动
    function updateTicketAction($paramArray)
    {
        $TicketOne = $this->DataControl->getFieldOne("smc_market_ticket", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
        if ($TicketOne) {
            $data = array();
            $data['ticket_cnname'] = $paramArray['ticket_cnname'];
            $data['ticket_startday'] = $paramArray['ticket_startday'];
            $data['ticket_endday'] = $paramArray['ticket_endday'];
            $data['ticket_deductionmethod'] = $paramArray['ticket_deductionmethod'];
            if ($paramArray['ticket_useway'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_market_ticket_courseuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的课程，请先删除！"), $this->companyOne['company_language']);
                } else {
                    $data['ticket_usecourse'] = '0';
                }
                $b = $this->DataControl->getFieldOne("smc_market_ticket_goodsuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($b) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的物品，请先删除！"), $this->companyOne['company_language']);
                } else {
                    $data['ticket_usegoods'] = '0';
                }
            }
            if ($paramArray['ticket_useway'] == '1' && $paramArray['ticket_usecourse'] == '0') {
                $b = $this->DataControl->getFieldOne("smc_market_ticket_goodsuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($b) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的物品，请先删除！"), $this->companyOne['company_language']);
                }
                $a = $this->DataControl->getFieldOne("smc_market_ticket_courseuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的课程，请先删除！"), $this->companyOne['company_language']);
                }

                $data['ticket_usegoods'] = '0';
                $data['ticket_usecourse'] = $paramArray['ticket_usecourse'];
            }
            if ($paramArray['ticket_useway'] == '1' && $paramArray['ticket_usecourse'] == '1') {
                $b = $this->DataControl->getFieldOne("smc_market_ticket_goodsuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($b) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的物品，请先删除！"), $this->companyOne['company_language']);
                }

                $data['ticket_usegoods'] = '0';
                $data['ticket_usecourse'] = $paramArray['ticket_usecourse'];
            }

            if ($paramArray['ticket_useway'] == '2' && $paramArray['ticket_usegoods'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_market_ticket_courseuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的课程，请先删除！"), $this->companyOne['company_language']);
                }
                $b = $this->DataControl->getFieldOne("smc_market_ticket_goodsuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($b) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的物品，请先删除！"), $this->companyOne['company_language']);
                }

                $data['ticket_usecourse'] = '0';
                $data['ticket_usegoods'] = $paramArray['ticket_usegoods'];
            }

            if ($paramArray['ticket_useway'] == '2' && $paramArray['ticket_usegoods'] == '1') {
                $a = $this->DataControl->getFieldOne("smc_market_ticket_courseuse", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "有已限制的课程，请先删除！"), $this->companyOne['company_language']);
                }

                $data['ticket_usecourse'] = '0';
                $data['ticket_usegoods'] = $paramArray['ticket_usegoods'];
            }
            $data['ticket_useway'] = $paramArray['ticket_useway'];
            $data['ticket_updatatime'] = time();

            if ($paramArray['ticket_applyschool'] == '0') {
                $a = $this->DataControl->getFieldOne("smc_market_ticket_schoolapply", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "有已设置的适配学校，请先删除！"), $this->companyOne['company_language']);
                } else {
                    $data['ticket_applyschool'] = $paramArray['ticket_applyschool'];
                }
            } else {
                $data['ticket_applyschool'] = $paramArray['ticket_applyschool'];
            }

            if ($paramArray['ticket_applycourse'] == '0') {
                $b = $this->DataControl->getFieldOne("smc_market_ticket_courseapply", "ticket_id", "ticket_id = '{$paramArray['ticket_id']}'");
                if ($b) {
                    ajax_return(array('error' => 1, 'errortip' => "有已设置的来源课程，请先删除！"), $this->companyOne['company_language']);
                } else {
                    $data['ticket_applycourse'] = $paramArray['ticket_applycourse'];
                }
            } else {
                $data['ticket_applycourse'] = $paramArray['ticket_applycourse'];
            }

            if ($paramArray['ticket_startday'] > $paramArray['ticket_endday']) {
                ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
            }

            $field = array();
            $field['ticket_cnname'] = "营销券规则名称";
            $field['ticket_startday'] = "起效时间";
            $field['ticket_endday'] = "失效时间";
            $field['ticket_deductionmethod'] = "抵扣方式";
            $field['ticket_applyschool'] = "适用学校方式";
            $field['ticket_applycourse'] = "来源课程";
            $field['ticket_useway'] = "来源课程";
            $field['ticket_usecourse'] = "来源课程";
            $field['ticket_usegoods'] = "来源课程";
            $field['ticket_updatatime'] = "修改时间";


            if ($this->DataControl->updateData("smc_market_ticket", "ticket_id = '{$paramArray['ticket_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑营销券成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑营销券失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑杂项设置
    function addItemAction($paramArray)
    {
        $setList = json_decode(stripslashes($paramArray['set']), true);
        $this->DataControl->delData('smc_fee_pricing_items', "pricing_id = '{$paramArray['pricing_id']}'");
        foreach ($setList as $item) {
            $data = array();
            $data['pricing_id'] = $item['pricing_id'];
            $data['feeitem_branch'] = $item['feeitem_branch'];
            $data['items_sellingprice'] = $item['items_sellingprice'];
            $data['items_buypiece'] = $item['items_buypiece'];
            $data['items_donatepiece'] = $item['items_donatepiece'];
            $data['items_unitprice'] = $item['items_unitprice'];
            $data['company_id'] = $paramArray['company_id'];

            $data['items_createtime'] = time();
            $this->DataControl->insertData('smc_fee_pricing_items', $data);
        }
        $res = array('error' => '0', 'errortip' => "编辑杂项设置成功");

        return $res;
    }

    //获取收费项目列表
    function getItemPrice($paramArray)
    {
        $sql = "
            SELECT
                feeitem_branch,
                feeitem_cnname
            FROM
                smc_code_feeitem
            WHERE
                company_id = '{$paramArray['company_id']}' and feeitem_class = '0'";
        $itemDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["feeitem_branch"] = "项目编号";
        $field["feeitem_cnname"] = "项目名称";

        $result = array();
        if ($itemDetail) {
            $result["field"] = $field;
            $result["data"] = $itemDetail;
            $res = array('error' => '0', 'errortip' => '获取收费项目列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取收费项目列表失败', 'result' => $result);
        }
        return $res;
    }

    //杂项列表
    function getSetItem($paramArray)
    {
        $sql = "
            SELECT
                d.pricing_id,
                d.items_id,
                d.feeitem_branch,
                d.items_sellingprice,
                d.items_ismustbuy,
                d.items_buypiece,
                d.items_donatepiece,
                d.items_unitprice,
                d.items_isfree,
                f.feeitem_cnname,
                f.feeitem_expendtype
            FROM
                smc_fee_pricing_items AS d left join smc_code_feeitem as f on d.feeitem_branch = f.feeitem_branch
            WHERE
                d.pricing_id = '{$paramArray['pricing_id']}' and f.company_id = '{$paramArray['company_id']}'";

        $ItemList = $this->DataControl->selectClear($sql);

        $status = array("0" => "0", "1" => "100");
        $statuss = $this->LgArraySwitch(array("0" => "考勤模式", "1" => "月度模式", "2" => "一次性"));
        if ($ItemList) {
            foreach ($ItemList as &$val) {
                $val['items_ismustbuy'] = $status[$val['items_ismustbuy']];
            }
            foreach ($ItemList as &$val) {
                $val['items_isfree'] = $status[$val['items_isfree']];
            }
            foreach ($ItemList as &$val) {
                $val['feeitem_expendtype'] = $statuss[$val['feeitem_expendtype']];
            }
        }


        $fieldstring = array('feeitem_cnname', 'items_sellingprice ', 'items_buypiece', 'items_donatepiece', 'items_unitprice', 'items_ismustbuy', 'items_isfree', 'feeitem_expendtype');
        $fieldname = $this->LgArraySwitch(array('收费项目名称', '销售价', '销售件数', '赠送件数', '项目标准单价', '是否为必买', '是否为免费', '消耗类型'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 0, 0, 1, 1, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ItemList) {
            $result['list'] = $ItemList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无相关适用学杂设置", 'result' => $result);
        }

        return $res;
    }

    //查看杂项
    function getItemDetail($paramArray)
    {

        $sql = "
            SELECT
                items_sellingprice,
                items_buypiece,
                items_donatepiece,
                items_unitprice,
                i.feeitem_branch,
                f.feeitem_expendtype
                
            FROM
                smc_fee_pricing_items as i left join smc_code_feeitem as f on i.feeitem_branch = f.feeitem_branch
            WHERE
                pricing_id = '{$paramArray['pricing_id']}'
          	GROUP BY i.items_id";
        $ItemList = $this->DataControl->selectClear($sql);

        $field = array();
        $field["items_sellingprice"] = "销售价";
        $field["items_buypiece"] = "销售件数";
        $field["items_donatepiece"] = "赠送件数";
        $field["items_unitprice"] = "标准单价";
        $field["feeitem_branch"] = "收费项目编号";


        $result = array();
        if ($ItemList) {
            $result["field"] = $field;
            $result["data"] = $ItemList;
            $res = array('error' => '0', 'errortip' => '查看杂项成功1', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看杂项失败', 'result' => $result);
        }
        return $res;
    }

    //杂物是否必买
    function updateItemMustBuyAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_fee_pricing_items", "items_id", "items_id = '{$paramArray['items_id']}'");
        if ($productsOne) {
            $data = array();
            $data['items_ismustbuy'] = $paramArray['items_ismustbuy'];

            $field = array();
            $field['items_ismustbuy'] = "是否必买";

            if ($this->DataControl->updateData("smc_fee_pricing_items", "items_id = '{$paramArray['items_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "是否必买修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '是否必买修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //是否免费
    function updateItemIsFreeAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_fee_pricing_items", "items_id", "items_id = '{$paramArray['items_id']}'");
        if ($productsOne) {
            $data = array();
            $data['items_isfree'] = $paramArray['items_isfree'];

            $field = array();
            $field['items_isfree'] = "是否免费";

            if ($this->DataControl->updateData("smc_fee_pricing_items", "items_id = '{$paramArray['items_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "是否免费修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '是否免费修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //启用/不启用
    function AgreementStatusAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("smc_fee_agreement", "agreement_id,agreement_startday,agreement_endday", "agreement_id = '{$paramArray['agreement_id']}'");

        if ($activityOne) {
            $data = array();
            $data['agreement_status'] = $paramArray['agreement_status'];

            $a = $this->DataControl->getFieldOne("smc_fee_agreement", "agreement_id", "company_id = '{$paramArray['company_id']}' and ((agreement_startday >= '{$activityOne['agreement_startday']}' and agreement_startday <= '{$activityOne['agreement_endday']}') or (agreement_endday >= '{$activityOne['agreement_startday']}' and agreement_endday <= '{$activityOne['agreement_endday']}')) and agreement_status = 1");

            if ($a && $paramArray['agreement_status'] == '1') {
                ajax_return(array('error' => 1, 'errortip' => "收费标准有效周期冲突！"), $this->companyOne['company_language']);
            }

            $field = array();
            $field['agreement_status'] = '是否启用';

            if ($this->DataControl->updateData("smc_fee_agreement", "agreement_id = '{$paramArray['agreement_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //计算单价
    function UnitPriceAction($paramArray)
    {

        $data = array();
        $data['tuition_unitprice'] = round($paramArray['tuition_originalprice'] / $paramArray['tuition_buypiece'], 2);
        $result = array();
        $result["data"] = $data;

        $res = array('error' => '0', 'errortip' => "计算单价成功", 'result' => $result);

        return $res;
    }

    //优惠券审核列表
    function getCouponsList($paramArray)
    {
        $datawhere = "a.company_id = '{$paramArray['company_id']}' and (a.apply_status = '0' or a.apply_status = '-1')";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname like '%{$paramArray['keyword']}%' or d.student_enname like '%{$paramArray['keyword']}%' or d.student_branch like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and a.school_id ='{$paramArray['school_id']}'";
        } else {
            $datawhere .= " and s.school_istest != '1'";
        }


        if (isset($paramArray['apply_status']) && $paramArray['apply_status'] !== "") {
            $datawhere .= " and a.apply_status ='{$paramArray['apply_status']}'";
        }
        if (isset($paramArray['applytype_branch']) && $paramArray['applytype_branch'] !== "") {
            $datawhere .= " and a.applytype_branch ='{$paramArray['applytype_branch']}'";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and from_unixtime(a.apply_time,'%Y-%m-%d') >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and from_unixtime(a.apply_time,'%Y-%m-%d') <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.apply_id,
                a.school_id,
                a.coursetype_branch,
                a.student_id,
                a.applytype_branch,
                a.apply_discountstype,
                a.apply_discountstype as apply_discountstype_status,
                a.apply_price,
                a.apply_discount * 10 as apply_discount,
                a.apply_minprice,
                a.apply_reson,
                a.apply_playclass,
                a.apply_refusereson,
                a.apply_remark,
                a.apply_cnname,
                a.apply_playclass as apply_playclass_name,
                a.apply_status,
                a.apply_status as apply_status_num,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                d.student_cnname,
                d.student_enname,
                d.student_branch,
                d.student_img,
                d.student_sex,
                c.applytype_cnname,
                c.applytype_isshop,
                c.applytype_validitytype,
                c.applytype_validitydays,
                c.applytype_deadline,
                c.applytype_applycoursecat,
                c.applytype_applycourse,
                from_unixtime(co.coupons_exittime,'%Y-%m-%d') as coupons_exittime,
                from_unixtime(a.apply_refusetime,'%Y-%m-%d') as apply_refusetime,
                from_unixtime(a.apply_time,'%Y-%m-%d') as apply_time,
                from_unixtime(co.coupons_bindingtime,'%Y-%m-%d') as coupons_bindingtime,
                st.staffer_cnname,c.applytype_coexist
            FROM
                smc_student_coupons_apply AS a
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                LEFT JOIN smc_student AS d ON d.student_id = a.student_id
                LEFT JOIN smc_code_couponsapplytype AS c ON c.applytype_branch = a.applytype_branch and c.company_id = a.company_id
                left join smc_student_coupons as co on co.apply_id = a.apply_id
                left join smc_staffer as st on a.staffer_id = st.staffer_id
                LEFT JOIN smc_parenter AS  p ON p.parenter_id = a.parenter_id
            WHERE
                {$datawhere}
                GROUP BY a.apply_id
                order by a.apply_id desc";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $statusone = $this->LgArraySwitch(array("0" => "待审核", "1" => "校长审核", "2" => "督导审核", "3" => "财务通过", "4" => "系统审核", "5" => "业助审核", "-1" => "已拒绝"));
            $statustwo = $this->LgArraySwitch(array("0" => "减价", "1" => "折扣"));
            $status = $this->LgArraySwitch(array("0" => "项目优惠券", "1" => "订单优惠券"));

            foreach ($dateexcelarray as &$val) {
                $val['apply_status'] = $statusone[$val['apply_status']];
                $val['apply_discountstype'] = $statustwo[$val['apply_discountstype']];
                $val['apply_playclass_name'] = $status[$val['apply_playclass_name']];

                if (!$val['coupons_exittime'] || !$val['coupons_bindingtime']) {

                    $val['coupons_bindingtime'] = date("Y-m-d");

                    if ($val['applytype_validitytype'] == 0) {
                        $val['coupons_exittime'] = date("Y-m-d", strtotime("+{$val['applytype_validitydays']} day", time()));
                    } else {
                        $val['coupons_exittime'] = $val['applytype_deadline'];
                    }
                }

                if ($val['applytype_branch'] == 'wsctuijian') {
                    $sql = "select s.student_cnname,s.student_branch,(SELECT SUM(p.pay_price) FROM smc_payfee_order_pay AS p,smc_payfee_order AS o
                    WHERE p.order_pid = o.order_pid AND p.pay_issuccess = '1' AND p.paychannel_code IN ('cash','cmb','hcmb','sdpos') AND o.student_id = s.student_id) AS  payallprice,(select from_unixtime(p.pay_successtime,'%Y-%m-%d') as pay_successtime from smc_payfee_order_pay as p left join smc_payfee_order as o on p.order_pid = o.order_pid where p.pay_issuccess = 1 and p.pay_isrefund = 0 and o.student_id =s.student_id ORDER BY p.pay_successtime ASC limit 0,1  ) as pay_successtime from shop_student_coupons_apply_recommend as r left join smc_student as s on r.student_id = s.student_id where apply_id = '{$val['apply_id']}'";
                    $a = $this->DataControl->selectOne($sql);

                    $val['price'] = $a['payallprice'];
                } else {
                    $val['price'] = '--';
                }


            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];//
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//
                    $datearray['apply_status'] = $dateexcelvar['apply_status'];//
                    $datearray['applytype_cnname'] = $dateexcelvar['applytype_cnname'];//
                    $datearray['apply_price'] = $dateexcelvar['apply_price'];//
                    $datearray['apply_discount'] = $dateexcelvar['apply_discount'];//
                    $datearray['apply_minprice'] = $dateexcelvar['apply_minprice'];//
                    $datearray['apply_playclass_name'] = $dateexcelvar['apply_playclass_name'];//
                    $datearray['apply_time'] = $dateexcelvar['apply_time'];//
                    $datearray['apply_reson'] = $dateexcelvar['apply_reson'];//
                    $datearray['price'] = $dateexcelvar['price'];//
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', ' 校区编号', '学员中文名', '学员英文名', '学员编号', '审核状态', '类型', '金额', '抵扣(折)', '最低消费金额', '优惠券使用类型', '优惠券申请日期', '优惠券申请原因', '缴费金额'));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'apply_status', 'applytype_cnname', 'apply_price', 'apply_discount', 'apply_minprice', 'apply_playclass_name', 'apply_time', 'apply_reson', 'price');

            $fielname = $this->LgStringSwitch("优惠券审核表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $ApplyList = $this->DataControl->selectClear($sql);
        }

        if ($ApplyList) {
            $statusone = $this->LgArraySwitch(array("0" => "待审核", "1" => "校长审核", "2" => "督导审核", "3" => "财务通过", "4" => "系统审核", "5" => "业助审核", "-1" => "已拒绝"));
            $statustwo = $this->LgArraySwitch(array("0" => "减价", "1" => "折扣"));
            $status = $this->LgArraySwitch(array("0" => "项目优惠券", "1" => "订单优惠券"));
            foreach ($ApplyList as &$val) {

                //页面是否展示 可以修改班组  is_modifyCtype 展示（1是 0否）
                if($val['applytype_branch'] == 'wscyuangong'){
                    $val['is_modifyCtype'] = 1;
                }else{
                    $val['is_modifyCtype'] = 0;
                }

                $val['apply_status'] = $statusone[$val['apply_status']];
                $val['apply_discountstype'] = $statustwo[$val['apply_discountstype']];
                $val['apply_playclass_name'] = $status[$val['apply_playclass_name']];

                if (!$val['coupons_exittime'] || !$val['coupons_bindingtime']) {

                    $val['coupons_bindingtime'] = date("Y-m-d");

                    if ($val['applytype_validitytype'] == 0) {
                        $val['coupons_exittime'] = date("Y-m-d", strtotime("+{$val['applytype_validitydays']} day", time()));
                    } else {
                        $val['coupons_exittime'] = $val['applytype_deadline'];
                    }
                }

                if ($val['applytype_branch'] == 'wsctuijian') {
                    $sql = "select s.student_cnname,s.student_branch,(SELECT SUM(p.pay_price) FROM smc_payfee_order_pay AS p,smc_payfee_order AS o
                    WHERE p.order_pid = o.order_pid AND p.pay_issuccess = '1' AND p.paychannel_code IN ('cash','cmb','hcmb','sdpos') AND o.student_id = s.student_id) AS  payallprice,(select from_unixtime(p.pay_successtime,'%Y-%m-%d') as pay_successtime from smc_payfee_order_pay as p left join smc_payfee_order as o on p.order_pid = o.order_pid where p.pay_issuccess = 1 and p.pay_isrefund = 0 and o.student_id =s.student_id ORDER BY p.pay_successtime ASC limit 0,1  ) as pay_successtime from shop_student_coupons_apply_recommend as r left join smc_student as s on r.student_id = s.student_id where apply_id = '{$val['apply_id']}'";
                    $a = $this->DataControl->selectOne($sql);

                    $val['price'] = $a['payallprice'];
                } else {
                    $val['price'] = '--';
                }
            }
        }
        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.apply_id)
            FROM
                smc_student_coupons_apply AS a LEFT JOIN smc_student AS d ON d.student_id = a.student_id
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                LEFT JOIN smc_code_couponsapplytype AS c ON c.applytype_branch = a.applytype_branch and c.company_id = a.company_id
                left join smc_student_coupons as co on co.apply_id = a.apply_id
                left join smc_staffer as st on a.staffer_id = st.staffer_id
                LEFT JOIN smc_parenter AS  p ON p.parenter_id = a.parenter_id
            WHERE {$datawhere} ");
        $allnums = $all_num[0][0];

        $fieldstring = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'apply_status', 'applytype_cnname', 'apply_price', 'apply_discount', 'apply_minprice', 'apply_playclass_name', 'apply_time', 'apply_reson', 'price');
        $fieldname = $this->LgArraySwitch(array('校区名称', ' 校区编号', '学员中文名', '学员英文名', '学员编号', '审核状态', '类型', '金额', '抵扣(折)', '最低消费金额', '优惠券使用类型', '优惠券申请日期', '优惠券申请原因', '缴费金额'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ApplyList) {
            $result['list'] = $ApplyList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}' and school_isclose = '0' order by (case when school_istest=0 and school_isclose=0 then 1 when school_isclose=0 then 2 when school_istest=0 then 3 else 4 end),school_istest asc,field(school_sort,0),school_sort asc,school_createtime asc");

        $result['applytype'] = $this->DataControl->selectClear("select applytype_branch,applytype_cnname from smc_code_couponsapplytype where company_id = '{$paramArray['company_id']}' and applytype_port=0");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无优惠券审核信息", 'result' => $result);
        }

        return $res;
    }

    //优惠券审核列表
    function getCouponsRemarkList($paramArray)
    {
        if ($paramArray['applytype_branch'] == 'wsctuijian') {
            $sql = "select s.student_cnname,s.student_branch
                    ,(SELECT SUM(p.pay_price) FROM smc_payfee_order_pay AS p,smc_payfee_order AS o
                    WHERE p.order_pid = o.order_pid AND p.pay_issuccess = '1' AND p.paychannel_code IN ('cash','cmb','hcmb','sdpos') AND o.student_id = s.student_id) AS  payallprice
                    ,(select from_unixtime(p.pay_successtime,'%Y-%m-%d') as pay_successtime 
                    from smc_payfee_order_pay as p left join smc_payfee_order as o on p.order_pid = o.order_pid 
                    where p.pay_issuccess = 1 and p.pay_isrefund = 0 and o.student_id =s.student_id ORDER BY p.pay_successtime ASC limit 0,1  ) as pay_successtime 
                    ,(select group_concat(y.course_cnname)                     
                    from smc_payfee_order_pay as p 
                    left join smc_payfee_order as o on p.order_pid = o.order_pid 
                    left join smc_payfee_order_course x on x.order_pid=o.order_pid
                    left join smc_course y on y.course_id=x.course_id
                    left join smc_student c on c.student_id=o.student_id
                    where p.pay_issuccess = 1 
                    and p.pay_isrefund = 0 
                    and o.student_id =s.student_id 
                    and p.pay_type=0
                    group by p.pay_id                
                    ORDER BY p.pay_successtime ASC limit 0,1)as course_name
                    ,ifnull((select concat(x.client_source,'-',y.channel_name) from crm_client x,crm_code_channel y 
                        where x.channel_id=y.channel_id and x.client_id=s.from_client_id limit 0,1),'--') as from_channel
                    from shop_student_coupons_apply_recommend as r 
                    left join smc_student as s on r.student_id = s.student_id 
                    where apply_id = '{$paramArray['apply_id']}'";
            $ApplyList = $this->DataControl->selectClear($sql);

            $fieldstring = array('student_cnname', 'student_branch', 'payallprice', 'pay_successtime', 'course_name', 'from_channel');
            $fieldname = $this->LgArraySwitch(array('被推荐人姓名', ' 学号', '缴费金额', '首次缴费时间', '首报课程', '来源渠道'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1");

        } elseif ($paramArray['applytype_branch'] == 'wsctongbao') {

            $sql = "select s.student_cnname,s.student_branch,(
            select sum(a.coursebalance_figure) as coursebalance_figure
                from smc_student_coursebalance a
                left join smc_course b on a.company_id=b.company_id and a.course_id=b.course_id
                left join smc_code_coursetype c on b.company_id=c.company_id and b.coursetype_id=c.coursetype_id
                where a.student_id=s.student_id) as aa
            ,(select sum(student_balance+student_withholdbalance) as balance from smc_student_balance as b where b.student_id=s.student_id) as bb 
            ,ifnull((select concat(x.client_source,'-',y.channel_name) from crm_client x,crm_code_channel y 
                where x.channel_id=y.channel_id and x.client_id=c.from_client_id limit 0,1),'--') as from_channel
            from shop_student_coupons_apply_siblings as i 
            left join smc_student as s on i.siblings_branch = s.student_branch 
            left join smc_student as c on c.student_id = i.student_id 
            where apply_id = '{$paramArray['apply_id']}' ";
            $ApplyList = $this->DataControl->selectClear($sql);

            $fieldstring = array('student_cnname', 'student_branch', 'aa', 'bb', 'from_channel');
            $fieldname = $this->LgArraySwitch(array('同胞姓名', '同胞学号', '课程余额', '账户余额', '来源渠道'));
            $fieldcustom = array("1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1");

        } elseif ($paramArray['applytype_branch'] == 'wscyuangong') {

            $sql = "select i.staff_workerduty,i.staff_parentname,i.staff_parentmobile,t.staffer_branch 
            ,ifnull((select concat(x.client_source,'-',y.channel_name) from crm_client x,crm_code_channel y 
                where x.channel_id=y.channel_id and x.client_id=c.from_client_id limit 0,1),'--') as from_channel
                from shop_student_coupons_apply_staff as i 
                left join smc_staffer as t on t.staffer_mobile = i.staff_parentmobile 
                left join smc_student c on c.student_id=i.student_id
                where i.apply_id = '{$paramArray['apply_id']}' ";
            $ApplyList = $this->DataControl->selectClear($sql);

            $fieldstring = array('staff_parentname', 'staffer_branch', 'staff_workerduty', 'staff_parentmobile', 'from_channel');
            $fieldname = $this->LgArraySwitch(array('职工姓名', '职工编号', '职务', '手机号', '来源渠道'));
            $fieldcustom = array("1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1");

        } elseif ($paramArray['applytype_branch'] == 'wscqinqi' || $paramArray['applytype_branch'] == 'wscneibu') {

            $sql = "select i.relative_name,i.relative_mobile,i.staff_parentname,i.staff_parentmobile,i.staff_workerduty,t.staffer_branch 
            ,ifnull((select concat(x.client_source,'-',y.channel_name) from crm_client x,crm_code_channel y 
                where x.channel_id=y.channel_id and x.client_id=c.from_client_id limit 0,1),'--') as from_channel
            from shop_student_coupons_apply_staff as i 
            left join shop_coupons_apply_relative as s on i.staff_parentmobile = s.relative_workermobile 
            left join smc_staffer as t on t.staffer_mobile = i.staff_parentmobile and t.company_id = '{$paramArray['company_id']}' 
            left join smc_student c on c.student_id=i.student_id
            where apply_id = '{$paramArray['apply_id']}' ";
            $ApplyList = $this->DataControl->selectClear($sql);

            $fieldstring = array('staff_parentname', 'staffer_branch', 'staff_workerduty', 'staff_parentmobile', 'relative_name', 'relative_mobile', 'from_channel');
            $fieldname = $this->LgArraySwitch(array('职工姓名', '职工编号', '职务', '手机号', '亲戚姓名', '亲戚联系电话', '来源渠道'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1");

        } elseif ($paramArray['applytype_branch'] == 'wscshangpin') {

            $sql = "select p.procou_name,p.procou_mobile,p.procou_scoreprove 
            ,ifnull((select concat(x.client_source,'-',y.channel_name) from crm_client x,crm_code_channel y 
                where x.channel_id=y.channel_id and x.client_id=c.from_client_id limit 0,1),'--') as from_channel 
                from shop_student_coupons_apply_procou as p 
            left join smc_student c on c.student_id=p.student_id
                where apply_id = '{$paramArray['apply_id']}' ";
            $ApplyList = $this->DataControl->selectClear($sql);

            $fieldstring = array('procou_scoreprove', 'procou_name', 'procou_mobile', 'from_channel');
            $fieldname = $this->LgArraySwitch(array('学员成绩', '联系人姓名', '联系方式', '来源渠道'));
            $fieldcustom = array("1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1");

        }else{

            $sql = "select ifnull((select concat(x.client_source,'-',y.channel_name) from crm_client x,crm_code_channel y 
                where x.channel_id=y.channel_id and x.client_id=c.from_client_id limit 0,1),'--') as from_channel 
                from smc_student_coupons_apply as a 
                left join smc_student c on c.student_id=a.student_id
                where a.apply_id = '{$paramArray['apply_id']}' ";
            $ApplyList = $this->DataControl->selectClear($sql);

            $fieldstring = array('from_channel');
            $fieldname = $this->LgArraySwitch(array('来源渠道'));
            $fieldcustom = array("1");
            $fieldshow = array("1");

        }

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ApplyList) {
            $result['list'] = $ApplyList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无优惠券审核信息", 'result' => $result);
        }

        return $res;
    }

    //审核适配课程
    function getSetCourse($paramArray)
    {
        if($paramArray['coursetype_branch'] != ''){

            $coursetypearr = explode(',', $paramArray['coursetype_branch']);
            $coursetypestr = implode("','",$coursetypearr);
            $endstr = "'".$coursetypestr."'";

            $sql = " select b.coursecat_id, b.coursecat_cnname, b.coursecat_branch
                    from smc_code_coursetype as a,smc_code_coursecat as b 
                    where a.company_id = '{$paramArray['company_id']}' and a.coursetype_branch in ({$endstr}) and a.coursetype_id = b.coursetype_id  ";

        }else {
            $sql = "SELECT c.coursecat_id, c.coursecat_cnname, c.coursecat_branch
                    FROM smc_student_coupons_applycoursecat AS a 
                    inner JOIN smc_code_coursecat AS c ON a.coursecat_id = c.coursecat_id
                    WHERE a.apply_id = '{$paramArray['apply_id']}'";
        }

        $courseList = $this->DataControl->selectClear($sql);

        $allNum = $this->DataControl->selectOne("SELECT
               COUNT(a.apply_id) as allnum FROM smc_student_coupons_applycoursecat AS a
            WHERE a.apply_id = '{$paramArray['apply_id']}' limit 0,1");
        $allnums = $allNum['allnum'];

        $fieldstring = array('coursecat_branch', 'coursecat_cnname');
        $fieldname = $this->LgArraySwitch(array('班种编号', '班种名称'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }


        $sql = "SELECT d.course_cnname,d.course_branch,e.coursecat_cnname
                    FROM smc_student_coupons_apply AS a 
                    inner join smc_code_couponsapplytype as b on b.applytype_branch=a.applytype_branch and b.company_id=a.company_id
                    inner join smc_couponsapplytype_courseapply as c on c.applytype_id=b.applytype_id
                    inner join smc_course as d on d.course_id=c.course_id
                    inner join smc_code_coursecat as e on e.coursecat_id=d.coursecat_id
                    WHERE a.apply_id = '{$paramArray['apply_id']}'
                    group by c.course_id
                    ";
        $applycourseList = $this->DataControl->selectClear($sql);

        $allNum = $this->DataControl->selectOne("SELECT
               COUNT(c.course_id) as allnum FROM smc_student_coupons_apply AS a 
                    inner join smc_code_couponsapplytype as b on b.applytype_branch=a.applytype_branch and b.company_id=a.company_id
                    inner join smc_couponsapplytype_courseapply as c on c.applytype_id=b.applytype_id WHERE a.apply_id = '{$paramArray['apply_id']}' limit 0,1");
        $allnums = $allNum['allnum'];
        

        $fieldstring = array('course_cnname', 'course_branch', 'coursecat_cnname');
        $fieldname = $this->LgArraySwitch(array('课程名称', '课程编号', '所属班种'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $coursefield = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $coursefield[$i]["fieldname"] = trim($fieldstring[$i]);
            $coursefield[$i]["fieldstring"] = trim($fieldname[$i]);
            $coursefield[$i]["custom"] = trim($fieldcustom[$i]);
            $coursefield[$i]["show"] = trim($fieldshow[$i]);
        }

        $coursepacksList=array();

        if(isset($paramArray['apply_id']) && $paramArray['apply_id']!=''){

            $sql = "SELECT b.applytype_id,b.applytype_manycourse,b.applytype_applymerge
                    FROM smc_student_coupons_apply as a 
                    inner join smc_code_couponsapplytype AS b ON a.applytype_branch = b.applytype_branch and b.company_id=a.company_id
                    WHERE a.apply_id = '{$paramArray['apply_id']}'";
            $codeOne=$this->DataControl->selectOne($sql);

            if($codeOne && $codeOne['applytype_manycourse']==1 && $codeOne['applytype_applymerge']==1){

//                $sql = "select a.coursepacks_id,b.coursepacks_name,b.coursepacks_startday,b.coursepacks_endday
//                        from smc_couponsapplytype_coursepacksapply as a,smc_fee_warehouse_coursepacks as b
//                        where a.coursepacks_id=b.coursepacks_id and a.applytype_id='{$codeOne['applytype_id']}'";

                $sql = "select b.coursepacks_id,b.coursepacks_name,b.coursepacks_startday,b.coursepacks_endday
                ,ifnull((select count(distinct x.course_id) from smc_fee_warehouse_courses as x where x.coursepacks_id=a.coursepacks_id),0) as courseNum
                ,ifnull((select sum(x.tuition_sellingprice) from smc_fee_warehouse_courses as x where x.coursepacks_id=a.coursepacks_id),0) as sellingprice
              from smc_couponsapplytype_coursepacksapply as a
              inner join smc_fee_warehouse_coursepacks as b on a.coursepacks_id=b.coursepacks_id
              where  a.applytype_id='{$codeOne['applytype_id']}' ";


                $coursepacksList=$this->DataControl->selectClear($sql);

                if($coursepacksList){
                    foreach($coursepacksList as &$coursepacksOne){
                        $coursepacksOne['effectetime']=($coursepacksOne['coursepacks_startday']==''?'--':$coursepacksOne['coursepacks_startday']).'至'.($coursepacksOne['coursepacks_endday']==''?'--':$coursepacksOne['coursepacks_endday']);
                    }
                }

            }

        }

        $packs_field=array();
        $k = 0;
        $packs_field[$k]["fieldname"] = "coursepacks_id";
        $packs_field[$k]["fieldstring"] = "组合课程id";
        $packs_field[$k]["show"] = 0;
        $packs_field[$k]["custom"] = 0;
        $k++;

        $packs_field[$k]["fieldname"] = "coursepacks_name";
        $packs_field[$k]["fieldstring"] = "组合课程名称";
        $packs_field[$k]["show"] = 1;
        $packs_field[$k]["custom"] = 1;
        $k++;

        $packs_field[$k]["fieldname"] = "effectetime";
        $packs_field[$k]["fieldstring"] = "有效期";
        $packs_field[$k]["show"] = 1;
        $packs_field[$k]["custom"] = 1;
        $k++;

        $packs_field[$k]["fieldname"] = "courseNum";
        $packs_field[$k]["fieldstring"] = "课程数量";
        $packs_field[$k]["show"] = 1;
        $packs_field[$k]["custom"] = 1;
        $k++;

        $packs_field[$k]["fieldname"] = "sellingprice";
        $packs_field[$k]["fieldstring"] = "组合价格";
        $packs_field[$k]["show"] = 1;
        $packs_field[$k]["custom"] = 1;
        $k++;


        if (isset($paramArray['coupons_id']) && $paramArray['coupons_id'] != '') {
            $sql = "select cl.applytype_cnname,st.staffer_cnname,t.card_bindtime,ti.ticket_cnname
              from smc_activity_ticket_card as t
              left join smc_activity_ticket_setup as ts on t.setup_id=ts.setup_id
              left join smc_activity_ticket as ti on ti.ticket_id=ts.ticket_id
              left join smc_code_couponsapplytype as cl on cl.applytype_id=ts.applytype_id
              left join smc_staffer as st on st.staffer_id=t.staffer_id
              where t.coupons_id='{$paramArray['coupons_id']}'";

            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons"
                , "coupons_bindingtime,coupons_exittime,coupons_isuse,coupons_disablereason"
                , "coupons_id='{$paramArray['coupons_id']}'");

            $cardOne = $this->DataControl->selectOne($sql);
            if ($cardOne) {
                $cardOne['card_bindtime'] = $cardOne['card_bindtime'] > 0 ? date("Y-m-d H:i:s", $cardOne['card_bindtime']) : '';
                if ($couponsOne['coupons_bindingtime'] > time() || $couponsOne['coupons_exittime'] < time()) {
                    $cardOne['reason'] = $this->LgStringSwitch("过有效期");
                } elseif ($couponsOne['coupons_isuse'] == '-1') {
                    $cardOne['reason'] = $this->LgStringSwitch($couponsOne['coupons_disablereason']);
                } else {
                    $cardOne['reason'] = '--';
                }
            } else {
                $cardOne = array();
            }
        } else {
            $cardOne = array();
        }
        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['coursefield'] = $coursefield;
        $result['packs_field'] = $packs_field;
        $result['all_num'] = $allnums;
        $result['cardOne'] = $cardOne ? $cardOne : array();


        $result['list'] = $courseList?$courseList:array();
        $result['courseList'] = $applycourseList?$applycourseList:array();
        $result['coursepacksList'] = $coursepacksList?$coursepacksList:array();
        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

//        if ($courseList) {
//            $result['list'] = $courseList;
//            $result['coursepacksList'] = $coursepacksList;
//            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
//        } else {
//            $result['list'] = array();
//            $res = array('error' => '1', 'errortip' => "已适配全部课程", 'result' => $result);
//        }

        return $res;
    }

    //同意拒绝
    function updateStatusAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$paramArray['staffer_id']}'");
        $applyOne = $this->DataControl->getFieldOne("smc_student_coupons_apply", "apply_id,applytype_branch,apply_status,coursetype_branch", "apply_id = '{$paramArray['apply_id']}'");

        if ($paramArray['apply_status'] == $applyOne['apply_status']) {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '相同状态,不可重复提交', 'result' => $result);
            return $res;
        }

        if ($applyOne['apply_status'] == '-1') {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '审核已拒绝,不可重复拒绝', 'result' => $result);
            return $res;
        }

        if ($applyOne['apply_status'] == '4' || $applyOne['apply_status'] == '5') {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '审核已通过,不可重复通过', 'result' => $result);
            return $res;
        }

        if ($applyOne) {
            $data = array();
            $role = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            if ($role['postrole_id'] == '89') {
                if ($paramArray['apply_status'] == '-1') {
                    $data['apply_status'] = '-1';
                    $data['apply_refusereson'] = $paramArray['apply_refusereson'];
                    $data['staffer_id'] = $paramArray['staffer_id'];
                    $data['apply_refusetime'] = time();
                } else {
                    $data['apply_status'] = '5';
                    $data['apply_remark'] = $paramArray['apply_remark'];
                    $data['apply_refusetime'] = time();
                }
            } else {
                $data['apply_status'] = $paramArray['apply_status'];
                if ($paramArray['apply_status'] == '-1') {
                    $data['apply_refusereson'] = $paramArray['apply_refusereson'];
                    $data['staffer_id'] = $paramArray['staffer_id'];
                    $data['apply_refusetime'] = time();
                } else {
                    $data['apply_remark'] = $paramArray['apply_remark'];
                    $data['apply_refusetime'] = time();
                }
            }

            $field = array();
            $field['apply_status'] = "状态";

            if ($this->DataControl->updateData("smc_student_coupons_apply", "apply_id = '{$paramArray['apply_id']}'", $data)) {
                if ($paramArray['apply_status'] == '3') {
                    $result = array();
                    $result["field"] = $field;
                    $result["data"] = $data;

                    $couponsapplytypeOne = $this->DataControl->selectOne("select applytype_range from smc_code_couponsapplytype where applytype_branch = '{$applyOne['applytype_branch']}' and company_id='{$this->company_id}' limit 0,1 ");

                    //发现之前 未判断是否重复，********* 补充
                    do {
                        $coupons_pid = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                    } while ($this->DataControl->selectOne("select coupons_id from smc_student_coupons where coupons_pid='{$coupons_pid}' and company_id='{$paramArray['company_id']}' limit 0,1"));

                    $data = array();
                    $data['coupons_class'] = '2';
                    $data['coupons_range'] = $couponsapplytypeOne['applytype_range'];
                    $data['company_id'] = $paramArray['company_id'];
                    $data['student_id'] = $paramArray['student_id'];
                    $data['apply_id'] = $paramArray['apply_id'];
//                    $data['coupons_pid'] = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
                    $data['coupons_pid'] = $coupons_pid;
                    $data['coupons_type'] = $paramArray['apply_discountstype_status'];
                    $data['coupons_price'] = $paramArray['apply_price'];
                    $data['coupons_discount'] = $paramArray['apply_discount'] / 10;
                    $data['coupons_minprice'] = $paramArray['apply_minprice'];
                    $data['coupons_name'] = $paramArray['applytype_cnname'];
                    $data['coupons_reason'] = $paramArray['apply_reson'];
                    $data['coupons_playclass'] = $paramArray['apply_playclass'];
                    $data['coupons_bindingtime'] = strtotime($paramArray['coupons_bindingtime']);
                    $data['coupons_exittime'] = strtotime($paramArray['coupons_exittime']) + 60 * 60 * 24 - 1;
                    $data['coupons_createtime'] = time();

                    $this->DataControl->insertData('smc_student_coupons', $data);

                    $data = array();
                    $data['apply_price'] = $paramArray['apply_price'];
                    $data['apply_discount'] = $paramArray['apply_discount'] / 10;
                    $data['apply_minprice'] = $paramArray['apply_minprice'];
                    $data['staffer_id'] = $paramArray['staffer_id'];
                    //20240419 补充班组 -- 员工申请券
                    if($paramArray['coursetype_branch'] != '' && $paramArray['coursetype_branch'] != $applyOne['coursetype_branch']){
                        $data['coursetype_branch'] = $paramArray['coursetype_branch'];
                    }
                    $this->DataControl->updateData('smc_student_coupons_apply', "apply_id = '{$paramArray['apply_id']}'", $data);

                    //班组不一样的话，处理优惠券适配的课程
                    if($paramArray['coursetype_branch'] != '' && $paramArray['coursetype_branch'] != $applyOne['coursetype_branch']){

                        $coursetypearr = explode(',', $paramArray['coursetype_branch']);
                        $coursetypestr = implode("','",$coursetypearr);
                        $endstr = "'".$coursetypestr."'";

                        //删除之前的
                        $this->DataControl->delData("smc_student_coupons_applycoursecat", "apply_id = '{$applyOne['apply_id']}'");
//                        //适配修改后的
//                        $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_code_coursetype as p,smc_course as c
//                                    WHERE p.coursetype_branch = '{$paramArray['coursetype_branch']}' and p.company_id = '8888' and p.coursetype_id = c.coursetype_id ");
                        //适配修改后的
//                        $courselist = $this->DataControl->selectClear(" select c.course_id
//                                    from smc_code_coursetype as p,smc_course as c
//                                    WHERE p.coursetype_branch in ({$endstr}) and p.company_id = '8888' and p.coursetype_id = c.coursetype_id ");
//                        if ($courselist) {
//                            foreach ($courselist as $coursevar) {
//                                //生成审核通过的申请记录
//                                $dataotwo = array();
//                                $dataotwo['apply_id'] = $applyOne['apply_id'];//
//                                $dataotwo['course_id'] = $coursevar['course_id'];//
//                                $this->DataControl->insertData('', $dataotwo);
//                            }
//                        }

                        $courselist = $this->DataControl->selectClear(" select c.coursecat_id 
                                    from smc_code_coursetype as p,smc_code_coursecat as c 
                                    WHERE p.coursetype_branch in ({$endstr}) and p.company_id = '8888' and p.coursetype_id = c.coursetype_id ");
                        if ($courselist) {
                            foreach ($courselist as $coursevar) {
                                //生成审核通过的申请记录
                                $dataotwo = array();
                                $dataotwo['apply_id'] = $applyOne['apply_id'];//
                                $dataotwo['coursecat_id'] = $coursevar['coursecat_id'];//
                                $this->DataControl->insertData('smc_student_coupons_applycoursecat', $dataotwo);
                            }
                        }
                        if($applyOne['applytype_branch'] == 'wsctongbao'){
                            $Remark = "班组从【全部】改为【{$paramArray['coursetype_branch']}】";
                        }else {
                            $Remark = "班组从【{$applyOne['coursetype_branch']}】改为【{$paramArray['coursetype_branch']}】";
                        }
                    }

                    $data = array();
                    $data['apply_id'] = $applyOne['apply_id'];
                    $data['tracks_title'] = $this->LgStringSwitch('审核成功');
                    $data['tracks_information'] = $paramArray['apply_reson'];
                    $data['staffer_id'] = $paramArray['staffer_id'];
                    $data['tracks_playname'] = $stafferOne['staffer_cnname'];
                    $data['tracks_note'] = $Remark?$Remark:'';
                    $data['tracks_time'] = time();

                    $this->DataControl->insertData("smc_student_coupons_apply_tracks", $data);

                    $res = array('error' => '0', 'errortip' => "审核成功", 'result' => $result);
                } else {
                    $data = array();

                    $data['apply_id'] = $applyOne['apply_id'];
                    $data['tracks_title'] = $this->LgStringSwitch('拒绝成功');
                    $data['tracks_information'] = $paramArray['apply_reson'];
                    $data['staffer_id'] = $paramArray['staffer_id'];
                    $data['tracks_playname'] = $stafferOne['staffer_cnname'];
                    $data['tracks_time'] = time();

                    $this->DataControl->insertData("smc_student_coupons_apply_tracks", $data);
                    $result = array();
                    $res = array('error' => '0', 'errortip' => "拒绝成功", 'result' => $result);
                }

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '审核失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //根据班种获取班组
    function getCoursetype($paramArray)
    {
        $stafferDetail = $this->DataControl->getFieldOne("smc_code_coursecat", "coursetype_id", "coursecat_id = '{$paramArray['coursecat_id']}'");

        $field = array();
        $field["coursetype_id"] = "班组id";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail['coursetype_id'];
            $res = array('error' => '0', 'errortip' => '获取班组成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班组失败', 'result' => $result);
        }
        return $res;
    }

    //优惠政策列表
    function getPolicyList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.policy_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and p.policy_startday >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and p.policy_endday <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.policy_id,
                p.policy_name,
                p.policy_status,
                p.policy_status as policy_status_name,
                p.policy_istrial,
                p.policy_istrial as policy_istrial_name,
                p.policy_startday,
                p.policy_endday,
                from_unixtime(p.policy_createtime,'%Y-%m-%d') as policy_createtime,
                (select count(s.policy_id) from smc_fee_policy_student as s where s.policy_id = p.policy_id) as nums
            FROM
                smc_fee_policy AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}' and p.policy_class='0'
            group by p.policy_id
            ORDER BY
                p.policy_id DESC
            LIMIT {$pagestart},{$num}";

        $PolicyList = $this->DataControl->selectClear($sql);

        if ($PolicyList) {
            foreach ($PolicyList as &$val) {
                $val['day'] = $val['policy_startday'] . '至' . $val['policy_endday'];
            }

            $status = array("-1" => "0", "1" => "100");
            foreach ($PolicyList as &$val) {
                $val['policy_status_name'] = $status[$val['policy_status_name']];
            }
            $status = array("0" => "0", "1" => "100");
            foreach ($PolicyList as &$val) {
                $val['policy_istrial_name'] = $status[$val['policy_istrial_name']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               p.policy_id
            FROM
                smc_fee_policy AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}' and p.policy_class='0' group by p.policy_id");
        if ($all_num) {
            $allnums = count($all_num);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('policy_name', 'day', 'policy_status_name', 'policy_istrial_name', 'nums', 'policy_createtime');
        $fieldname = $this->LgArraySwitch(array('优惠政策名称', '有效期', '启用状态', '是否免审', '学员数', '创建时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 1, 1, 0, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PolicyList) {
            $result['list'] = $PolicyList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠政策信息", 'result' => $result);
        }

        return $res;
    }


    function getPolicyLabelList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.policy_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and p.policy_startday >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and p.policy_endday <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.policy_id,
                p.policy_name,
                p.policy_status,
                p.policy_startday,
                p.policy_endday,
                p.applytype_branch,
                p.policy_price,
                p.policy_trial_num,
                cat.applytype_cnname,
                from_unixtime(p.policy_createtime,'%Y-%m-%d') as policy_createtime,p.policy_type,
                (select count(s.policy_id) from smc_fee_policy_student as s where s.policy_id = p.policy_id) as nums
            FROM
                smc_fee_policy AS p
                left join smc_code_couponsapplytype as cat on cat.applytype_branch=p.applytype_branch and cat.company_id=p.company_id
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}' and p.policy_class='1'
            ORDER BY
                p.policy_id DESC
            LIMIT {$pagestart},{$num}";

        $PolicyList = $this->DataControl->selectClear($sql);

        if ($PolicyList) {
            $status = array("-1" => "0", "1" => "100");
            foreach ($PolicyList as &$val) {
                $val['day'] = $val['policy_startday'] . '至' . $val['policy_endday'];
                $val['policy_status_name'] = $status[$val['policy_status']];

                if($val['policy_type']==0){
                    if ($val['policy_trial_num'] == 0) {
                        $val['policy_trial_num_name'] = $this->LgStringSwitch('不限');
                    } else {
                        $val['policy_trial_num_name'] = $val['policy_trial_num'];
                    }
                    $val['policy_receive_num'] ='--';
                }else{
                    $val['policy_receive_num'] =$val['policy_trial_num'];
                    $val['policy_trial_num_name']='--';
                }

            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               p.policy_id
            FROM
                smc_fee_policy AS p 
                left join smc_code_couponsapplytype as cat on cat.applytype_branch=p.applytype_branch and cat.company_id=p.company_id
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}' and p.policy_class='1'");
        if ($all_num) {
            $allnums = count($all_num);
        } else {
            $allnums = 0;
        }

        $fieldstring = array('policy_name', 'day', 'applytype_cnname', 'policy_price', 'policy_trial_num_name', 'policy_receive_num', 'policy_status_name', 'nums', 'policy_createtime');
        $fieldname = $this->LgArraySwitch(array('优惠政策名称', '有效期', '优惠券类型', '优惠金额/折扣', '免审次数', '最大可领取次数', '启用状态', '学员数', '创建时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldswitch = array(0, 0, 0, 0, 0, 0, 1, 0, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PolicyList) {
            $result['list'] = $PolicyList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠政策信息", 'result' => $result);
        }

        return $res;
    }

    //撤销申请
    function DelCouponsApplyAction($paramArray)
    {
        $applyOne = $this->DataControl->getFieldOne("smc_student_coupons_apply", "apply_id", "apply_id = '{$paramArray['apply_id']}'");
        if ($applyOne) {
            if ($this->DataControl->delData("smc_student_coupons_apply", "apply_id = '{$paramArray['apply_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "撤销申请成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '撤销申请失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //改变优惠政策状态
    function ChangePolicyStatusAction($paramArray)
    {
        $policyOne = $this->DataControl->getFieldOne("smc_fee_policy", "policy_id,policy_type,applytype_branch", "policy_id = '{$paramArray['policy_id']}'");

        if($policyOne['policy_type']==1 && $paramArray['policy_status']==1){
            $sql="select policy_trial_num from smc_fee_policy 
                  where policy_class=1 and company_id='{$this->company_id}' and applytype_branch='{$policyOne['applytype_branch']}' and policy_status=1 and policy_startday<=CURDATE() and policy_id <> '{$paramArray['policy_id']}' and policy_endday>=CURDATE() order by policy_createtime desc limit 0,1";

            if($this->DataControl->selectOne($sql)){
                ajax_return(array('error' => 1, 'errortip' => "该优惠类型已存在可使用政策,不可开启"), $this->companyOne['company_language']);
            }
        }

        if ($policyOne) {
            $data = array();
            $data['policy_status'] = $paramArray['policy_status'];

            $field = array();
            $field['policy_status'] = "是否启用";

            if ($this->DataControl->updateData("smc_fee_policy", "policy_id = '{$paramArray['policy_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "是否启用修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '是否启用修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //改变优惠政策是否免审
    function ChangePolicyIstrialAction($paramArray)
    {
        $policyOne = $this->DataControl->getFieldOne("smc_fee_policy", "policy_id", "policy_id = '{$paramArray['policy_id']}'");
        if ($policyOne) {
            $data = array();
            $data['policy_istrial'] = $paramArray['policy_istrial'];

            $field = array();
            $field['policy_istrial'] = "是否免审";

            if ($this->DataControl->updateData("smc_fee_policy", "policy_id = '{$paramArray['policy_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "是否免审修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '是否免审修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function CreateLabelPolicy($paramArray)
    {
        $data = array();
        $data['policy_name'] = $paramArray['policy_name'];
        $data['policy_startday'] = $paramArray['policy_startday'];
        $data['policy_endday'] = $paramArray['policy_endday'];
        $data['policy_status'] = $paramArray['policy_status'];
        $data['applytype_branch'] = $paramArray['applytype_branch'];
        $data['policy_price'] = $paramArray['policy_price'];
        $data['policy_trial_num'] = $paramArray['policy_trial_num'];
        $data['policy_class'] = 1;
        $data['policy_type'] = $paramArray['policy_type'];
        $data['policy_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];

        if ($paramArray['policy_startday'] > $paramArray['policy_endday']) {
            ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
        }


        $applytypeOne=$this->DataControl->getFieldOne("smc_code_couponsapplytype","applytype_id,applytype_getscope,applytype_applyschool","company_id='{$paramArray['company_id']}' and applytype_branch='{$paramArray['applytype_branch']}'");

        if(!$applytypeOne){
            ajax_return(array('error' => 1, 'errortip' => "优惠类型不存在!"), $this->companyOne['company_language']);
        }

        if($applytypeOne['applytype_getscope']==3){
            $sql="select * from smc_fee_policy where policy_class=1 and company_id='{$paramArray['company_id']}' and policy_status=1 and applytype_branch='{$paramArray['applytype_branch']}' and policy_startday<=CURDATE() and policy_endday>=CURDATE() limit 0,1";
            if($this->DataControl->selectOne($sql)){
                ajax_return(array('error' => 1, 'errortip' => "优惠类型已被使用!"), $this->companyOne['company_language']);
            }
        }


        if ($id = $this->DataControl->insertData('smc_fee_policy', $data)) {
            $data['policy_id']=$id;

            $result = array();
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加优惠政策成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->标签优惠政策设置", '新增标签优惠政策', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加标签优惠政策失败', 'result' => $result);
        }
        return $res;
    }

    //编辑优惠政策信息
    function UpdateLabelPolicy($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("smc_fee_policy", "policy_id,policy_type,applytype_branch", "policy_id = '{$paramArray['policy_id']}'");

        if($policyDetailOne['policy_type']==1 && $paramArray['policy_status']==1){
            $sql="select policy_trial_num from smc_fee_policy 
                  where policy_class=1 and company_id='{$this->company_id}' and applytype_branch='{$policyDetailOne['applytype_branch']}' and policy_status=1 and policy_startday<=CURDATE() and policy_endday>=CURDATE() and policy_id <> '{$paramArray['policy_id']}' order by policy_createtime desc limit 0,1";

            if($this->DataControl->selectOne($sql)){
                ajax_return(array('error' => 1, 'errortip' => "该优惠类型已存在可使用政策,不可开启"), $this->companyOne['company_language']);
            }
        }

        if ($policyDetailOne) {
            $data = array();
            $data['policy_status'] = $paramArray['policy_status'];
            $data['policy_name'] = $paramArray['policy_name'];
            $data['policy_startday'] = $paramArray['policy_startday'];
            $data['policy_endday'] = $paramArray['policy_endday'];
            $data['applytype_branch'] = $paramArray['applytype_branch'];
            $data['policy_price'] = $paramArray['policy_price'];
            $data['policy_trial_num'] = $paramArray['policy_trial_num'];
            $data['policy_updatatime'] = time();

            if ($paramArray['policy_startday'] > $paramArray['policy_endday']) {
                ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->updateData("smc_fee_policy", "policy_id = '{$paramArray['policy_id']}'", $data)) {
                $result = array();
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "标签优惠政策信息修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->标签优惠政策设置", '标签优惠政策信息修改', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '标签优惠政策信息修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //新增优惠政策
    function CreatePolicyAction($paramArray)
    {
        $data = array();
        $data['policy_name'] = $paramArray['policy_name'];
        $data['policy_startday'] = $paramArray['policy_startday'];
        $data['policy_endday'] = $paramArray['policy_endday'];
        $data['policy_status'] = $paramArray['policy_status'];
        $data['policy_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['policy_name'] = "政策名称";
        $field['policy_startday'] = "起效时间";
        $field['policy_endday'] = "失效时间";
        $field['policy_status'] = "协议状态";
        $field['company_id'] = "所属公司";

        if ($paramArray['policy_startday'] > $paramArray['policy_endday']) {
            ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
        }

        if ($id = $this->DataControl->insertData('smc_fee_policy', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $result["id"] = $id;
            $res = array('error' => '0', 'errortip' => "添加优惠政策成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->优惠政策设置", '新增优惠政策', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加优惠政策失败', 'result' => $result);
        }
        return $res;
    }

    //获取班种列表
    function getCourseCat($paramArray)
    {
        $sql = "
            SELECT
                s.coursecat_id,
                s.coursecat_cnname,
                s.coursecat_branch
            FROM
                smc_code_coursecat AS s 
            WHERE
                s.company_id = '{$paramArray['company_id']}'";
        $CourseCatDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["coursecat_id"] = "班种id";
        $field["coursecat_cnname"] = "班种名称";

        $result = array();
        if ($CourseCatDetail) {
            $result["field"] = $field;
            $result["data"] = $CourseCatDetail;
            $res = array('error' => '0', 'errortip' => '获取班种列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班种列表失败', 'result' => $result);
        }
        return $res;
    }

    //同班组课程下拉
    function SameCoursetypeList($paramArray)
    {
        if ($paramArray['course_id'] != '') {
            $sql = "
            SELECT
                c.course_id,
                c.course_cnname,
                c.course_branch
            FROM
                smc_course AS c 
            WHERE
                c.course_status = '1' and coursetype_id IN ( SELECT c.coursetype_id FROM smc_course AS c WHERE c.course_id IN ({$paramArray['course_id']}) )";
        } else {
            $sql = "select c.course_id,course_cnname,course_branch from smc_course as c where c.course_status = '1' and company_id = '{$paramArray['company_id']}'";
        }

        $CourseCatDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["course_id"] = "课程id";
        $field["course_cnname"] = "课程名称";

        $result = array();
        if ($CourseCatDetail) {
            $result["field"] = $field;
            $result["data"] = $CourseCatDetail;
            $res = array('error' => '0', 'errortip' => '获取同班组课程下拉成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取同班组课程下拉失败', 'result' => $result);
        }
        return $res;
    }

    //获取班别列表
    function getCourseByCat($paramArray)
    {
        $sql = "
            SELECT
                s.course_id,
                s.course_cnname,
                s.course_branch
            FROM
                smc_course AS s 
            WHERE
                s.company_id = '{$paramArray['company_id']}' and s.coursecat_id = '{$paramArray['coursecat_id']}'";
        $CourseCatDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["course_id"] = "班别id";
        $field["course_cnname"] = "班别名称";

        $result = array();
        if ($CourseCatDetail) {
            $result["field"] = $field;
            $result["data"] = $CourseCatDetail;
            $res = array('error' => '0', 'errortip' => '获取班别列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班别列表失败', 'result' => $result);
        }
        return $res;
    }

    //添加政策售价
    function addPolicyCourseAction($paramArray)
    {
        $data = array();

        $policyList = json_decode(stripslashes($paramArray['policy']), true);
        $this->DataControl->delData('smc_fee_policy_course', "policy_id = '{$paramArray['policy_id']}'");

        foreach ($policyList as $item) {
            $data['policy_id'] = $item['policy_id'];
            $data['from_course_id'] = $item['from_course_id'];
            $data['to_course_id'] = $item['to_course_id'];
            $data['norm_unitprice'] = $item['norm_unitprice'];
            $data['sell_unitprice'] = $item['sell_unitprice'];
            $data['full_price'] = $item['full_price'];
            $a = $this->DataControl->getFieldOne("smc_course", "course_branch", "course_id = '{$item['from_course_id']}'");
            $b = $this->DataControl->getFieldOne("smc_course", "course_branch", "course_id = '{$item['to_course_id']}'");
            $data['from_course_branch'] = $a['course_branch'];
            $data['to_course_branch'] = $b['course_branch'];
            $data['company_id'] = $paramArray['company_id'];


            $c = $this->DataControl->getFieldOne('smc_fee_policy_course', 'policy_id', "policy_id = '{$item['policy_id']}' and from_course_id = '{$item['from_course_id']}' and to_course_id = '{$item['to_course_id']}'");
            if ($c) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
            }
            $this->DataControl->insertData('smc_fee_policy_course', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加政策成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->优惠政策设置", '添加政策成功', dataEncode($paramArray));

        return $res;
    }

    //获取享受政策学员列表
    function getEnjoyStudentList($paramArray)
    {

        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname  like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.student_id,
                s.student_cnname,
                s.student_enname,
                s.student_sex,
                s.student_branch,
                (case when l.school_shortname='' then l.school_cnname else l.school_shortname end) as school_cnname,
                l.school_branch,
                f.family_mobile
            FROM
                smc_fee_policy_student AS p
                LEFT JOIN smc_student AS s ON p.student_id = s.student_id
                left join smc_student_enrolled as e on e.student_id = p.student_id and e.enrolled_status > -1
                left join smc_school as l on e.school_id = l.school_id
                left join smc_student_family as f on f.student_id = p.student_id and f.family_isdefault = 1
            WHERE
                {$datawhere} and s.company_id = '{$paramArray['company_id']}' and p.policy_id = '{$paramArray['policy_id']}' and p.policy_id>0
            LIMIT {$pagestart},{$num}";

        $PolicyList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.policy_id)
            FROM
                smc_fee_policy_student AS p
                LEFT JOIN smc_student AS s ON p.student_id = s.student_id
                left join smc_student_enrolled as e on e.student_id = p.student_id and e.enrolled_status = 1
                left join smc_school as l on e.school_id = l.school_id
                left join smc_student_family as f on f.student_id = p.student_id and f.family_isdefault = 1
            WHERE
                {$datawhere} and s.company_id = '{$paramArray['company_id']}' and p.policy_id = '{$paramArray['policy_id']}' and p.policy_id>0");
        $allnums = $all_num[0][0];

        $fieldstring = array('student_cnname', 'student_enname', 'student_sex', 'student_branch', 'school_cnname', 'school_branch', 'family_mobile');
        $fieldname = $this->LgArraySwitch(array('学员中文名', '学员英文名', '性别', '学员编号', '校区名称', '校区编号', '主要联系电话'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($PolicyList) {
            $result['list'] = $PolicyList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无享受政策学员信息", 'result' => $result);
        }

        return $res;
    }

    //删除享受政策学员
    function DelEnjoyStudentAction($paramArray)
    {
        if ($this->DataControl->delData("smc_fee_policy_student", "policy_id = '{$paramArray['policy_id']}' and student_id = '{$paramArray['student_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除活享受政策学员成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团缴费设定->优惠政策设置", '删除享受政策学员', dataEncode($paramArray));

        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除享受政策学员失败', 'result' => $result);
        }
        return $res;
    }

    //删除优惠政策
    function DelPolicyAction($paramArray)
    {
        if ($this->DataControl->delData("smc_fee_policy", "policy_id = '{$paramArray['policy_id']}'")) {
            $result = array();
            $res = array('error' => '0', 'errortip' => "删除优惠政策成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团缴费设定->优惠政策设置", '删除优惠政策', dataEncode($paramArray));

        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '删除优惠政策失败', 'result' => $result);
        }
        return $res;
    }

    //新增学员
    function AddEnjoyStudentAction($paramArray)
    {
        $data = array();
        $data['policy_id'] = $paramArray['policy_id'];
        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_branch = '{$paramArray['student_branch']}'");
        if (!$studentOne) {
            ajax_return(array('error' => 1, 'errortip' => "学员编号不存在!"), $this->companyOne['company_language']);
        }

        $data['student_id'] = $studentOne['student_id'];

        $policyOne=$this->DataControl->getFieldOne("smc_fee_policy","policy_id,policy_trial_num,applytype_branch","policy_id='{$paramArray['policy_id']}'");

        if(!$policyOne){
            ajax_return(array('error' => 1, 'errortip' => "无对应政策"), $this->companyOne['company_language']);
        }

        $applytypeOne=$this->DataControl->getFieldOne("smc_code_couponsapplytype","applytype_id,applytype_applyschool","applytype_branch='{$policyOne['applytype_branch']}' and company_id='{$paramArray['company_id']}'");

        if($applytypeOne['applytype_applyschool']==1){
            $sql="select a.schoolapply_id from smc_couponsapplytype_schoolapply as a,smc_student_enrolled as b,smc_student as c where a.school_id=b.school_id and b.student_id=c.student_id and a.applytype_id='{$applytypeOne['applytype_id']}' and c.student_branch='{$paramArray['student_branch']}' and b.enrolled_status>=0 limit 0,1";

            if(!$this->DataControl->selectOne($sql)){
                ajax_return(array('error' => 1, 'errortip' => "存在他校学员"), $this->companyOne['company_language']);
            }
        }elseif($applytypeOne['applytype_applyschool']==-1){
            $sql="select a.schoolapply_id from smc_couponsapplytype_schoolapply as a,smc_student_enrolled as b,smc_student as c where a.school_id=b.school_id and b.student_id=c.student_id and a.applytype_id='{$applytypeOne['applytype_id']}' and c.student_branch='{$paramArray['student_branch']}' and b.enrolled_status>=0 limit 0,1";

            if($this->DataControl->selectOne($sql)){
                ajax_return(array('error' => 1, 'errortip' => "存在他校学员"), $this->companyOne['company_language']);
            }
        }

        $field = array();
        $field['policy_id'] = "政策id";
        $field['student_id'] = "学员id";

        $a = $this->DataControl->getFieldOne("smc_fee_policy_student", "policy_id", "policy_id = '{$data['policy_id']}' and student_id = '{$data['student_id']}'");

        if ($a) {
            ajax_return(array('error' => 1, 'errortip' => "该学员已享受过此政策!"), $this->companyOne['company_language']);
        }

        $this->DataControl->insertData('smc_fee_policy_student', $data);

        $result = array();
        $result["field"] = $field;
        $result["data"] = $data;
        $res = array('error' => '0', 'errortip' => "添加学员成功", 'result' => $result);
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->优惠政策设置", '添加学员', dataEncode($paramArray));

        return $res;
    }


    //编辑优惠政策信息
    function UpdatePolicyAction($paramArray)
    {
        $policyDetailOne = $this->DataControl->getFieldOne("smc_fee_policy", "policy_id", "policy_id = '{$paramArray['policy_id']}'");
        if ($policyDetailOne) {
            $data = array();
            $data['policy_status'] = $paramArray['policy_status'];
            $data['policy_name'] = $paramArray['policy_name'];
            $data['policy_startday'] = $paramArray['policy_startday'];
            $data['policy_endday'] = $paramArray['policy_endday'];
            $data['policy_updatatime'] = time();

            $field = array();
            $field['policy_status'] = "协议状态";
            $field['policy_name'] = "政策名称";
            $field['policy_startday'] = "起效时间";
            $field['policy_endday'] = "失效时间";
            $field['policy_updatatime'] = "更新时间";

            if ($paramArray['policy_startday'] > $paramArray['policy_endday']) {
                ajax_return(array('error' => 1, 'errortip' => "开始日期不能大于结束日期!"), $this->companyOne['company_language']);
            }

            if ($this->DataControl->updateData("smc_fee_policy", "policy_id = '{$paramArray['policy_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "优惠政策信息修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->优惠政策设置", '优惠政策信息修改', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '优惠政策信息修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取活动设置
    function getPolicySetAction($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['from_course_id']) && $paramArray['from_course_id'] !== "") {
            $datawhere .= " and a.from_course_id ='{$paramArray['from_course_id']}'";
        }
        if (isset($paramArray['to_course_id']) && $paramArray['to_course_id'] !== "") {
            $datawhere .= " and a.to_course_id ='{$paramArray['to_course_id']}'";
        }
        $sql = "
            SELECT
                a.from_course_id,
                (select coursecat_id from smc_course as c WHERE c.course_id = a.from_course_id) as from_coursecat_id,
                (select course_cnname from smc_course as c WHERE c.course_id = a.from_course_id) as from_course_name,
                (select course_cnname from smc_course as c WHERE c.course_id = a.to_course_id) as to_course_name,
                (select coursecat_cnname from smc_course as c left join smc_code_coursecat as ca on ca.coursecat_id = c.coursecat_id WHERE c.course_id = a.from_course_id) as from_coursecat_name,
                (select coursecat_cnname from smc_course as c left join smc_code_coursecat as ca on ca.coursecat_id = c.coursecat_id WHERE c.course_id = a.to_course_id) as to_coursecat_name,
                (select coursecat_id from smc_course as c WHERE c.course_id = a.to_course_id) as to_coursecat_id,
                a.to_course_id,
                a.norm_unitprice,
                a.sell_unitprice,
                a.full_price
            FROM
                smc_fee_policy_course AS a
            WHERE
                {$datawhere} and a.policy_id = '{$paramArray['policy_id']}' limit 0,10";
        $activityDetail = $this->DataControl->selectClear($sql);

        if ($activityDetail) {
            foreach ($activityDetail as &$val) {
                if ($val['from_course_id'] == '0') {
                    $val['from_course_id'] = '';
                } else {
                    $val['from_course_id'] = $val['from_course_id'];
                }
            }

        }

        $field = array();
        $field["from_course_id"] = "原班别ID";
        $field["to_course_id"] = "升级班别ID";
        $field["norm_unitprice"] = "课程标准单价（退费）";
        $field["sell_unitprice"] = "课程销售单价（销售及收入）";
        $field["full_price"] = "满课销售价格";

        $result = array();
        if ($activityDetail) {
            $result["field"] = $field;
            $result["data"] = $activityDetail;
            $res = array('error' => '0', 'errortip' => '获取活动设置成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取活动设置失败', 'result' => array());
        }
        return $res;
    }

    //获取班别列表
    function getCourseApi($paramArray)
    {
        $sql = "
            SELECT
                s.course_id,
                s.course_cnname,
                s.course_branch
            FROM
                smc_course AS s 
            WHERE
                s.company_id = '{$paramArray['company_id']}'";
        $CourseCatDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["course_id"] = "班别id";
        $field["course_cnname"] = "班别名称";
        $field["course_branch"] = "班别编号";

        $result = array();
        if ($CourseCatDetail) {
            $result["field"] = $field;
            $result["data"] = $CourseCatDetail;
            $res = array('error' => '0', 'errortip' => '获取班别列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班别列表失败', 'result' => $result);
        }
        return $res;
    }

    //下载导入模版
    function getImportApi($paramArray)
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/studentdemo.xlsx?time=20211115';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        return $res;
    }

    //获取权益学校
    function getDataequitySchoolApi($paramArray)
    {
        $datawhere = "s.company_id = '{$paramArray['company_id']}' AND s.school_isclose <> '1'";
        if ($paramArray['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$paramArray['re_postbe_id']}'");
            $datawhere .= " AND s.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}')";
        }
        if ($paramArray['district_id']) {
            $datawhere .= " AND s.district_id = '{$paramArray['district_id']}'";
        }

        $CourseCatDetail = $this->DataControl->selectClear(" SELECT s.school_id, s.school_branch,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname FROM smc_school AS s WHERE {$datawhere} order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc");

        $field = array();
        $field["school_id"] = "学校id";
        $field["school_branch"] = "校区编号";
        $field["school_cnname"] = "校区名称";

        $result = array();
        if ($CourseCatDetail) {
            $result["field"] = $field;
            $result["data"] = $CourseCatDetail;
            $res = array('error' => '0', 'errortip' => '获取学校列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取学校列表失败', 'result' => $result);
        }
        return $res;
    }


    //获取组合商品库列表
    function getWarehouseList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (w.warehouse_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and a.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
               w.warehouse_id,
               w.agreement_id,
               w.warehouse_name,
               w.warehouse_applytype,
               w.warehouse_remark,
               (select count(a.school_id) from smc_fee_warehouse_apply as a where a.warehouse_id = w.warehouse_id) as ScCount,
               (select count(c.coursepacks_id) from smc_fee_warehouse_coursepacks as c where c.warehouse_id = w.warehouse_id) as CoCount
            FROM
                smc_fee_warehouse AS w left join smc_fee_warehouse_apply as a on w.warehouse_id = a.warehouse_id
            WHERE
                {$datawhere} and w.agreement_id = '{$paramArray['agreement_id']}'
                group by w.warehouse_id
            ORDER BY 
                w.warehouse_id DESC
            LIMIT {$pagestart},{$num}";

        $WarehouseList = $this->DataControl->selectClear($sql);

        $school = $this->DataControl->selectOne("select count(s.school_id) as count from smc_school as s where s.company_id = '{$paramArray['company_id']}' and s.school_isclose = '0'");

        if ($WarehouseList) {
            foreach ($WarehouseList as &$val) {
                if ($val['warehouse_applytype'] == '0') {
                    $val['ScCount'] = $school['count'];
                }
            }

        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(*)
            FROM
               (SELECT
               w.warehouse_id,
               w.agreement_id,
               w.warehouse_name,
               w.warehouse_applytype,
               w.warehouse_remark,
               (select count(a.school_id) from smc_fee_warehouse_apply as a where a.warehouse_id = w.warehouse_id) as ScCount,
               (select count(c.coursepacks_id) from smc_fee_warehouse_coursepacks as c where c.warehouse_id = w.warehouse_id) as CoCount
            FROM
                smc_fee_warehouse AS w left join smc_fee_warehouse_apply as a on w.warehouse_id = a.warehouse_id
            WHERE
                {$datawhere} and w.agreement_id = '{$paramArray['agreement_id']}'
                group by w.warehouse_id
            ORDER BY 
                w.warehouse_id DESC) as a");
        $allnums = $all_num[0][0];

        $fieldstring = array('warehouse_name', 'ScCount', 'CoCount', 'warehouse_remark');
        $fieldname = $this->LgArraySwitch(array('组合课程库', '适用学校', '组合课程数量', '备注信息'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");
        $fieldmethod = array(0, 1, 1, 0);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldmethod[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($WarehouseList) {
            $result['list'] = $WarehouseList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无组合商品库", 'result' => $result);
        }

        return $res;
    }

    //获取组合商品库适用学校列表
    function getWarehouseSchoolList($paramArray)
    {

        $datawhere = " a.warehouse_id = '{$paramArray['warehouse_id']}' and s.school_id > 0 ";

        $sql = "
            SELECT
               s.school_id,
               s.school_branch,
               (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
               s.school_enname,
               d.district_cnname
            FROM
               smc_fee_warehouse_apply AS a
               LEFT JOIN smc_school AS s ON a.school_id = s.school_id
               LEFT JOIN gmc_company_district AS d ON s.district_id = d.district_id
            WHERE
                {$datawhere}";

        $SchoolList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.warehouse_id)
            FROM
                smc_fee_warehouse_apply AS a
                LEFT JOIN smc_school AS s ON a.school_id = s.school_id
            WHERE
                {$datawhere}");
        $allnums = $all_num[0][0];

        $fieldstring = array('school_branch', 'school_cnname', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('校区编号', '校园名称', '检索代码', '区域'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($SchoolList) {
            $result['list'] = $SchoolList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无组合商品库适用学校", 'result' => $result);
        }

        return $res;
    }

    //获取商品库课程组合列表
    function getCoursepacksList($paramArray)
    {

        $datawhere = " 1 ";

        $sql = "
            SELECT
               c.coursepacks_id,
               c.coursepacks_name,
               c.coursepacks_startday,
               c.coursepacks_endday,
               (select count(co.warehouse_id) from smc_fee_warehouse_courses as co where co.coursepacks_id = c.coursepacks_id) as CoCount,
               (select sum(cc.tuition_sellingprice) from smc_fee_warehouse_courses as cc where cc.coursepacks_id=c.coursepacks_id group by cc.coursepacks_id) as price
            FROM
               smc_fee_warehouse_coursepacks AS c left join smc_fee_warehouse as w on c.warehouse_id = w.warehouse_id
            WHERE
                {$datawhere} and w.warehouse_id = '{$paramArray['warehouse_id']}'";

        $CourseList = $this->DataControl->selectClear($sql);

        if ($CourseList) {
            foreach ($CourseList as &$val) {
                $val['time'] = $val['coursepacks_startday'] . '至' . $val['coursepacks_endday'];
            }

        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.warehouse_id)
            FROM
                smc_fee_warehouse_apply AS a
            WHERE
                {$datawhere} and a.warehouse_id = '{$paramArray['warehouse_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('coursepacks_name', 'time', 'CoCount', 'price');
        $fieldname = $this->LgArraySwitch(array('组合课程名称', '有效期', '课程数量', '组合价格'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CourseList) {
            $result['list'] = $CourseList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无商品库课程组合", 'result' => $result);
        }

        return $res;
    }

    //新增组合课程第一步
    function addWarehouseFirstAction($paramArray)
    {

        $data = array();
        $data['warehouse_name'] = $paramArray['warehouse_name'];
        $data['warehouse_remark'] = $paramArray['warehouse_remark'];
        $data['agreement_id'] = $paramArray['agreement_id'];

        $field = array();
        $field['warehouse_name'] = "商品库名称";
        $field['warehouse_remark'] = "备注";
        $field['agreement_id'] = "政策id";

        if ($paramArray['warehouse_id']) {
            if ($this->DataControl->updateData('smc_fee_warehouse', "warehouse_id = '{$paramArray['warehouse_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $result["warehouse_id"] = $paramArray['warehouse_id'];
                $res = array('error' => '0', 'errortip' => "编辑组合商品库成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑组合商品库失败', 'result' => $result);
            }
            return $res;
        } else {

            if ($id = $this->DataControl->insertData('smc_fee_warehouse', $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $result["warehouse_id"] = $id;
                $res = array('error' => '0', 'errortip' => "添加组合商品库成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '添加组合商品库失败', 'result' => $result);
            }
            return $res;
        }


    }

    //添加组合课程适用学校
    function addWarehouseSchoolAction($paramArray)
    {
        $schoolList = json_decode(stripslashes($paramArray['school']), true);
        /*$this->DataControl->delData("smc_fee_warehouse_courses","warehouse_id = '{$schoolList[0]['warehouse_id']}'");
        $this->DataControl->delData("smc_fee_warehouse_coursepacks","warehouse_id = '{$schoolList[0]['warehouse_id']}'");*/
        if (is_array($schoolList)) {
            foreach ($schoolList as $item) {
                $data = array();
                $data['school_id'] = $item['school_id'];
                $data['warehouse_id'] = $item['warehouse_id'];

                $a = $this->DataControl->getFieldOne('smc_fee_warehouse_apply', 'warehouse_id', "warehouse_id = '{$item['warehouse_id']}' and school_id = '{$item['school_id']}'");
                if ($a) {
                    ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"), $this->companyOne['company_language']);
                }
                $this->DataControl->insertData('smc_fee_warehouse_apply', $data);
            }
        }
        $res = array('error' => '0', 'errortip' => "添加分校成功", 'result' => array());

        return $res;
    }

    //添加组合课程
    function addCoursepacksAction($paramArray)
    {
        if ($paramArray['type'] == 1) {
            $data = array();
            $data['warehouse_id'] = $paramArray['warehouse_id'];
            $data['coursepacks_name'] = $paramArray['coursepacks_name'];
            $data['coursepacks_startday'] = $paramArray['coursepacks_startday'];
            $data['coursepacks_endday'] = $paramArray['coursepacks_endday'];

            $coursepacks_id = $this->DataControl->insertData('smc_fee_warehouse_coursepacks', $data);

            $courseList = json_decode(stripslashes($paramArray['course']), true);

            foreach ($courseList as $item) {
                $data = array();
                $data['coursepacks_id'] = $coursepacks_id;
                $data['warehouse_id'] = $item['warehouse_id'];
                $data['pricing_id'] = $item['pricing_id'];
                $data['course_id'] = $item['course_id'];
                $data['tuition_sellingprice'] = $item['price'];
                $data['courses_isdiscounts'] = $item['courses_isdiscounts'];

                $this->DataControl->insertData('smc_fee_warehouse_courses', $data);
            }
            $res = array('error' => '0', 'errortip' => "添加组合课程成功", 'result' => array());
        } else {


            if($this->DataControl->getFieldOne("smc_payfee_mergeorder","mergeorder_id","coursepacks_id='{$paramArray['coursepacks_id']}'")){
                ajax_return(array('error' => 1, 'errortip' => "组合已被使用,不可编辑"), $this->companyOne['company_language']);
            }

            if($this->DataControl->getFieldOne("smc_payfee_order","order_id","coursepacks_id='{$paramArray['coursepacks_id']}'")){
                ajax_return(array('error' => 1, 'errortip' => "组合已被使用,不可编辑"), $this->companyOne['company_language']);
            }


            $data = array();
            $data['coursepacks_name'] = $paramArray['coursepacks_name'];
            $data['coursepacks_startday'] = $paramArray['coursepacks_startday'];
            $data['coursepacks_endday'] = $paramArray['coursepacks_endday'];

            $this->DataControl->updateData("smc_fee_warehouse_coursepacks", "coursepacks_id = '{$paramArray['coursepacks_id']}'", $data);

            $this->DataControl->delData("smc_fee_warehouse_courses", "coursepacks_id = '{$paramArray['coursepacks_id']}'");

            $courseList = json_decode(stripslashes($paramArray['course']), true);

            foreach ($courseList as $item) {
                $data = array();
                $data['coursepacks_id'] = $paramArray['coursepacks_id'];
                $data['warehouse_id'] = $item['warehouse_id'];
                $data['pricing_id'] = $item['pricing_id'];
                $data['course_id'] = $item['course_id'];
                $data['tuition_sellingprice'] = $item['price'];
                $data['courses_isdiscounts'] = $item['courses_isdiscounts'];
                $this->DataControl->insertData('smc_fee_warehouse_courses', $data);
            }

            $res = array('error' => '0', 'errortip' => "编辑组合课程成功", 'result' => array());
        }


        return $res;
    }

    //获取单个组合课程信息
    function getCoursepacksOneApi($paramArray)
    {
        $coursepacks = $this->DataControl->selectClear("select * from smc_fee_warehouse_courses where coursepacks_id = '{$paramArray['coursepacks_id']}'");
        $course = $this->DataControl->getFieldOne("smc_fee_warehouse_coursepacks", "coursepacks_name,coursepacks_startday,coursepacks_endday", "coursepacks_id = '{$paramArray['coursepacks_id']}'");
        if ($coursepacks) {
            $result = array();
            $result['course'] = $coursepacks;
            $result['coursepacks'] = $course;
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
            return $res;
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
            return $res;

        }
    }

    //获取单个组合库
    function getWarehouseOneApi($paramArray)
    {
        $WarehouseOne = $this->DataControl->getFieldOne("smc_fee_warehouse", "warehouse_name,warehouse_startday,warehouse_endday,warehouse_remark", "warehouse_id = '{$paramArray['warehouse_id']}'");
        if ($WarehouseOne) {
            $result = array();
            $result['warehouse_name'] = $WarehouseOne['warehouse_name'];
            $result['warehouse_startday'] = $WarehouseOne['warehouse_startday'];
            $result['warehouse_endday'] = $WarehouseOne['warehouse_endday'];
            $result['warehouse_remark'] = $WarehouseOne['warehouse_remark'];
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
            return $res;
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
            return $res;

        }
    }

    //选择课程列表
    function getCourseChoiceList($paramArray)
    {
        $schoolArray = $this->DataControl->selectClear("SELECT c.school_id FROM smc_fee_warehouse_apply as c WHERE c.warehouse_id = '{$paramArray['warehouse_id']}'");
        $differenceArray = array();
        if ($schoolArray) {
            foreach ($schoolArray as $key => $schoolOne) {
                $courseArray = array();

                $courseList = $this->DataControl->selectClear("
                    SELECT
                        t.course_id,
                        t.tuition_id,
                        p.pricing_id,
                        c.course_cnname,
                        c.course_branch,
                        c.coursetype_id,
                        c.coursecat_id 
                    FROM
                        smc_fee_pricing_tuition AS t,
                        smc_fee_pricing AS p,
                        smc_fee_agreement AS a,
                        smc_course AS c 
                    WHERE
                                t.pricing_id = p.pricing_id 
                        AND p.agreement_id = a.agreement_id 
                        AND c.course_id = t.course_id 
                        AND (
                        ( p.pricing_applytype = '1' AND p.pricing_id IN ( SELECT pricing_id FROM smc_fee_pricing_apply AS a WHERE a.school_id = '{$schoolOne['school_id']}' ) ) 
                        OR ( p.pricing_applytype = '-1' AND p.pricing_id NOT IN ( SELECT pricing_id FROM smc_fee_pricing_apply AS a WHERE a.school_id = '{$schoolOne['school_id']}' ) ) 
                        OR ( p.pricing_applytype = '0' ) 
                        ) 
                        AND a.agreement_id = '{$paramArray['agreement_id']}'
                        AND c.course_status<>'-1'
                    GROUP BY
                        t.course_id");

                if ($courseList) {
                    foreach ($courseList as $courseOne) {
                        $courseArray[$courseOne['course_id']] = $courseOne['tuition_id'];
                    }
                }
                if ($key == 0) {
                    $differenceArray = $courseArray;
                } else {
                    $differenceArray = array_intersect_assoc($differenceArray, $courseArray);
                }
            }
        }

        if ($differenceArray) {
            $a = implode(',', $differenceArray);
        } else {
            $a = '1';
        }

        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {

            $paramArray['keyword'] = trim($paramArray['keyword'], '|');
            $c = explode("|", $paramArray['keyword']);
            $b = '0 > 1';
            foreach ($c as $val) {
                $b .= " or c.course_branch like '%{$val}%'";
                $b .= " or p.pricing_name like '%{$val}%'";
            }

            $datawhere .= " and ($b)";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ='{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ='{$paramArray['coursetype_id']}'";
        }

        $sql = "
           SELECT
                p.pricing_id,p.pricing_name,
                p.pricing_applytype,
                c.course_cnname,c.course_branch,c.course_id,
                t.tuition_unitprice,
                t.tuition_originalprice,
                t.tuition_sellingprice,
                c.course_classnum,
                t.tuition_refundprice,
                (select count(pr.products_id) from smc_fee_pricing_products as pr WHERE pr.pricing_id = p.pricing_id) as count
            FROM
                smc_fee_pricing AS p left join smc_course as c on p.course_id = c.course_id left join smc_fee_pricing_tuition as t on t.pricing_id = p.pricing_id
            WHERE
          
              {$datawhere} and p.agreement_id = '{$paramArray['agreement_id']}' and t.tuition_id in ({$a})";

        $PriceList = $this->DataControl->selectClear($sql);

        $fieldstring = array('pricing_name', 'course_branch', 'tuition_originalprice ', 'tuition_sellingprice', 'course_classnum', 'tuition_unitprice', 'tuition_refundprice', 'count');
        $fieldname = $this->LgArraySwitch(array('收费价格名称', '课程别', '市场价', '销售价', '课次', '标准单价', '课程退费手续费', '教材数量'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["isShow"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['field'] = $field;

        if ($PriceList) {
            $result['list'] = $PriceList;
            $res = array('error' => '0', 'errortip' => "获取选择课程列表成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无选择课程列表", 'result' => $result);
        }

        return $res;
    }

    //删除组合商品库适配学校
    function delWarehouseSchoolAction($paramArray)
    {
        $organizeOne = $this->DataControl->getFieldOne("smc_fee_warehouse_apply", "school_id,warehouse_id", "warehouse_id = '{$paramArray['warehouse_id']}'");
        if ($organizeOne) {
            $where = "warehouse_id = '{$paramArray['warehouse_id']}'";
            $school_list = json_decode(stripslashes($paramArray['school_id']), true);
            if(is_array($school_list)){
                $str = array();
                foreach ($school_list as $val) {
                    $str[] = $val;
                }
                $school_id = implode(',', $str);
                $where .= " and school_id in ({$school_id})";
            }else{
                $where .= " and school_id = '{$paramArray['school_id']}'";
            }
            if ($this->DataControl->delData("smc_fee_warehouse_apply", $where)) {
                $result = array();
//                $this->DataControl->delData("smc_fee_warehouse_courses","warehouse_id = '{$paramArray['warehouse_id']}'");
//                $this->DataControl->delData("smc_fee_warehouse_coursepacks","warehouse_id = '{$paramArray['warehouse_id']}'");
                $res = array('error' => '0', 'errortip' => "删除组合商品库适配学校成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除机构管辖学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除组合课程
    function delCoursepacksAction($paramArray)
    {
        $CoursepacksOne = $this->DataControl->getFieldOne("smc_fee_warehouse_coursepacks", "coursepacks_id", "coursepacks_id = '{$paramArray['coursepacks_id']}'");
        if ($CoursepacksOne) {
            if ($this->DataControl->delData("smc_fee_warehouse_coursepacks", "coursepacks_id = '{$paramArray['coursepacks_id']}'")) {
                $result = array();
                $a = $this->DataControl->getFieldOne("smc_payfee_order", "order_id", "coursepacks_id = '{$paramArray['coursepacks_id']}'");
                if ($a) {
                    ajax_return(array('error' => '1', 'errortip' => '组合课程已被使用，无法删除', 'result' => $result, $this->companyOne['company_language']));

                }
                $res = array('error' => '0', 'errortip' => "删除组合课程成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除组合课程失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除组合库
    function delWarehouseAction($paramArray)
    {

        $sql = "select a.coursepacks_id 
                from smc_fee_warehouse_coursepacks as a 
                where a.warehouse_id='{$paramArray['warehouse_id']}'
                limit 0,1
                ";

        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "已被使用,不可删除";
            return false;
        }

        $WarehousOne = $this->DataControl->getFieldOne("smc_fee_warehouse", "warehouse_id", "warehouse_id = '{$paramArray['warehouse_id']}'");

        if ($WarehousOne) {
            if ($this->DataControl->delData("smc_fee_warehouse", "warehouse_id = '{$paramArray['warehouse_id']}'")) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = "删除组合课程库失败";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "组合课程库不存在";
            return false;
        }
    }

    //根据收费项目编号获取消耗类型
    function getExpendtypeApi($paramArray)
    {
        $WarehouseOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_expendtype,feeitem_price", "company_id = '{$paramArray['company_id']}' and feeitem_branch = '{$paramArray['feeitem_branch']}'");
        if ($WarehouseOne) {
            $result = array();
            if ($WarehouseOne['feeitem_expendtype'] == '0') {
                $WarehouseOne['feeitem_expendtype'] = $this->LgStringSwitch('考勤模式');
                $WarehouseOne['number'] = '0';
            } elseif ($WarehouseOne['feeitem_expendtype'] == '1') {
                $WarehouseOne['feeitem_expendtype'] = $this->LgStringSwitch('月度模式');
                $WarehouseOne['number'] = '1';
            } elseif ($WarehouseOne['feeitem_expendtype'] == '2') {
                $WarehouseOne['feeitem_expendtype'] = $this->LgStringSwitch('一次性');
                $WarehouseOne['number'] = '2';
            }
            $result['feeitem_expendtype'] = $WarehouseOne['feeitem_expendtype'];
            $result['feeitem_price'] = $WarehouseOne['feeitem_price'];
            $result['number'] = $WarehouseOne['number'];
            $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
            return $res;
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
            return $res;

        }
    }


    //优惠券审核列表
    function getCouponsManageList($paramArray)
    {

        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coupons_isuse']) && $paramArray['coupons_isuse'] !== "") {
            $datawhere .= " and c.coupons_isuse ='{$paramArray['coupons_isuse']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and a.school_id ='{$paramArray['school_id']}'";
        }
        if (isset($paramArray['applytype_branch']) && $paramArray['applytype_branch'] !== "") {
            $datawhere .= " and a.applytype_branch ='{$paramArray['applytype_branch']}'";
        }
        if (isset($paramArray['coupons_exittime']) && $paramArray['coupons_exittime'] !== "" && isset($paramArray['coupons_bindingtime']) && $paramArray['coupons_bindingtime'] !== "") {
            $datawhere .= " and (FROM_UNIXTIME( c.coupons_exittime, '%Y-%m-%d' ) >= '{$paramArray['coupons_exittime']}' or (FROM_UNIXTIME( c.coupons_bindingtime, '%Y-%m-%d' ) >= '{$paramArray['coupons_bindingtime']}' and FROM_UNIXTIME( c.coupons_bindingtime, '%Y-%m-%d' ) <= '{$paramArray['coupons_exittime']}') )  ";
        }else{
            if (isset($paramArray['coupons_exittime']) && $paramArray['coupons_exittime'] !== "") {
                $datawhere .= " and FROM_UNIXTIME( c.coupons_exittime, '%Y-%m-%d' ) <= '{$paramArray['coupons_exittime']}'";
            }
            if (isset($paramArray['coupons_bindingtime']) && $paramArray['coupons_bindingtime'] !== "") {
                $datawhere .= " and FROM_UNIXTIME( c.coupons_bindingtime, '%Y-%m-%d' ) >= '{$paramArray['coupons_bindingtime']}'";
            }
        }


        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.coupons_id,
                c.apply_id,
                c.coupons_class,
                c.coupons_forbid,
                c.coupons_type,
                c.coupons_isuse,
                c.coupons_copy,
                c.order_pid,a.apply_remark,
                c.coupons_isuse as coupons_isuse_name,
                c.coupons_type as coupons_type_num,
                c.coupons_playclass,
                FROM_UNIXTIME( c.coupons_exittime, '%Y-%m-%d %H:%i:%s' ) AS coupons_exittime,
                FROM_UNIXTIME( c.coupons_bindingtime, '%Y-%m-%d %H:%i:%s' ) AS coupons_bindingtime,
                FROM_UNIXTIME( c.coupons_usetime, '%Y-%m-%d' ) AS coupons_usetime,
                FROM_UNIXTIME( c.coupons_forbidtime, '%Y-%m-%d' ) AS coupons_forbidtime,
                FROM_UNIXTIME( c.coupons_copytime, '%Y-%m-%d' ) AS coupons_copytime,
                a.apply_refusetime,a.staffer_id,
                FROM_UNIXTIME( a.apply_time, '%Y-%m-%d' ) AS apply_time,
                coupons_price,
                coupons_discount * 10 as coupons_discount,
                coupons_minprice,
                a.apply_reson,
                s.student_img,
                s.student_cnname,
                s.student_enname,
                s.student_branch,
                s.student_sex,
                (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
                sc.school_branch,
                a.apply_minprice,
                a.apply_discount,
                a.apply_discountstype,
                a.apply_refusereson,c.coupons_disablereason as apply_disablereason,
                a.apply_price,
                a.apply_status,
                a.apply_cnname,
                cc.applytype_cnname,
                cc.applytype_isshop,
                cc.applytype_branch,
                cc.applytype_isneedcheck,
                cc.applytype_applycoursecat,
                cc.applytype_applycourse,
                s.student_cnname,
                s.student_enname,
                st.staffer_cnname,
                st.staffer_enname,
                (SELECT count(apply_id) from smc_student_coupons_applycoursecat as l WHERE l.apply_id = c.apply_id) as count,
                po.trading_pid,po.order_pid,po.order_paymentprice,
                FROM_UNIXTIME( po.order_createtime, '%Y-%m-%d' ) AS order_createtime,cc.applytype_coexist
            FROM
                smc_student_coupons AS c
                left join smc_student_coupons_apply as a on c.apply_id = a.apply_id
                left join smc_student as s on s.student_id=c.student_id
                left join smc_code_couponsapplytype as cc on cc.applytype_branch=a.applytype_branch and cc.company_id = '{$paramArray['company_id']}'
                left join smc_school as sc on sc.school_id = a.school_id
                left join smc_staffer as st on st.staffer_id = a.staffer_id
                left join smc_payfee_order as po on c.order_pid=po.order_pid 
                WHERE {$datawhere} and c.company_id = '{$paramArray['company_id']}'
                group by c.coupons_id
                order by c.coupons_id desc";

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $ApplyList = $this->DataControl->selectClear($sql);
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $ApplyList = $this->DataControl->selectClear($sql);
        }

        if ($ApplyList) {
            $type = $this->LgArraySwitch(array("0" => "减价", "1" => "折扣"));
            $class = $this->LgArraySwitch(array("0" => "营销卷", "1" => "活动券", "2" => "申请券"));
            $playclass = $this->LgArraySwitch(array("0" => "项目优惠券", "1" => "订单优惠券"));
            $isuse = $this->LgArraySwitch(array("0" => "未用", "1" => "已用", "-1" => "失效"));
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "校长审核", "2" => "督导审核", "3" => "财务通过", "4" => "系统审核", "5" => "业助审核", "-1" => "拒绝"));

            foreach ($ApplyList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                $val['coupons_type'] = $type[$val['coupons_type']];
                $val['coupons_class_status'] = $val['coupons_class'];
                $val['coupons_class'] = $val['applytype_cnname'];
                $val['coupons_playclass'] = $playclass[$val['coupons_playclass']];
                $val['apply_discountstype'] = $type[$val['apply_discountstype']];
                $val['coupons_isuse_name'] = $isuse[$val['coupons_isuse_name']];
                $val['time'] = $val['coupons_bindingtime'] . '～' . $val['coupons_exittime'];
                if ($val['coupons_class_status'] == 1) {
                    $val['apply_status'] = $this->LgStringSwitch('集团发放');
                } else {
                    $val['apply_status'] = $status[$val['apply_status']];
                }
                if($val['apply_refusetime'] == '0' && $val['staffer_id'] == '0'){
                    $val['staffer_cnname'] = '--';
                    $val['apply_refusetime'] = '--';
                }elseif($val['staffer_id'] == '0' && $val['apply_refusetime'] > '0'){
                    $val['staffer_cnname'] = '系统审核';
                    $val['apply_refusetime'] = date("Y-m-d H:i:s",$val['apply_refusetime']);
                }elseif($val['apply_refusetime'] > '0'){
                    $val['apply_refusetime'] = date("Y-m-d H:i:s",$val['apply_refusetime']);
                }
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $ApplyList;

            if (!$dateexcelarray) {
                ajax_return(array('error' => 1, 'errortip' => "报表内容为空，不可导出!"), $this->companyOne['company_language']);
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];//
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//
                    $datearray['applytype_cnname'] = $dateexcelvar['applytype_cnname'];//
                    $datearray['coupons_type'] = $dateexcelvar['coupons_type'];//
                    $datearray['coupons_price'] = $dateexcelvar['coupons_price'];//
                    $datearray['coupons_discount'] = $dateexcelvar['coupons_discount'];//
                    $datearray['coupons_minprice'] = $dateexcelvar['coupons_minprice'];//
                    $datearray['time'] = $dateexcelvar['time'];//
                    $datearray['coupons_isuse_name'] = $dateexcelvar['coupons_isuse_name'];//
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];//
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];//
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];//
                    $datearray['order_createtime'] = $dateexcelvar['order_createtime'];//
                    $datearray['apply_status'] = $dateexcelvar['apply_status'];//
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];//
                    $datearray['count'] = $dateexcelvar['count'];//
                    $datearray['coupons_playclass'] = $dateexcelvar['coupons_playclass'];//
                    $datearray['apply_reson'] = $dateexcelvar['apply_reson'];//
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('所属学校', '校区编号', '学员中文名', '学员英文名', '学员编号', '优惠券类型', '方式', '金额', '折扣', '课程最低消费金额', '有效时间', '使用状态', '交易编号', '订单编号', '订单总金额', '使用时间', '审核方式', '审核人', '适用课程数量', '优惠券使用类型', '申请原因'));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'applytype_cnname', 'coupons_type', 'coupons_price', 'coupons_discount', 'coupons_minprice', 'time', 'coupons_isuse_name', 'trading_pid', 'order_pid', 'order_paymentprice', 'order_createtime', 'apply_status', 'staffer_cnname', 'count', 'coupons_playclass', 'apply_reson');

            $fielname = $this->LgStringSwitch("优惠券管理表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        }


        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(*) as a
            FROM
                (SELECT
                c.coupons_id,
                c.apply_id,
                c.coupons_class,
                c.coupons_type,
                c.coupons_playclass,
                coupons_price,
                coupons_discount * 10 as coupons_discount,
                coupons_minprice,
                a.apply_reson,
                s.student_img,
                s.student_sex,
                a.apply_minprice,
                a.apply_discount,
                a.apply_discountstype,
                a.apply_price,
                cc.applytype_cnname,
                cc.applytype_isneedcheck,
                s.student_cnname,
                s.student_enname,
                st.staffer_cnname,
                (SELECT count(apply_id) from smc_student_coupons_applycoursecat as l WHERE l.apply_id = c.apply_id) as count
            FROM
                smc_student_coupons AS c
                left join smc_student_coupons_apply as a on c.apply_id = a.apply_id
                left join smc_student as s on s.student_id=c.student_id
                left join smc_code_couponsapplytype as cc on cc.applytype_branch=a.applytype_branch and cc.company_id='{$this->company_id}'
                left join smc_school as sc on sc.school_id = a.school_id
                left join smc_staffer as st on st.staffer_id = a.staffer_id
                WHERE {$datawhere} and c.company_id = '{$paramArray['company_id']}'
                group by c.coupons_id
                order by c.coupons_id desc) as b");

        $allnums = $all_num[0]['a'];

        $fieldname = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'applytype_cnname', 'coupons_type', 'coupons_price', 'coupons_discount', 'coupons_minprice', 'time', 'coupons_isuse_name', 'trading_pid', 'order_pid', 'order_paymentprice', 'order_createtime', 'apply_status', 'staffer_cnname', 'count', 'coupons_playclass', 'apply_reson', 'apply_id', 'student_img', 'student_sex', 'applytype_isneedcheck');
        $fieldstring = $this->LgArraySwitch(array('所属学校', '校区编号', '学员中文名', '学员英文名', '学员编号', '优惠券类型', '方式', '金额', '折扣', '课程最低消费金额', '有效时间', '使用状态', '交易编号', '订单编号', '订单总金额', '使用时间', '审核方式', '审核人', '适用课程数量', '优惠券使用类型', '申请原因', '申请ID', '学员头像', '性别', '是否需要审核'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($ApplyList) {
            $result['list'] = $ApplyList;
            $result['all_num'] = $allnums;

            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "学员还未申请优惠券", 'result' => $result);
        }

        return $res;
    }

    //禁用优惠券
    function forbiddenAction($paramArray)
    {
        $policyOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,apply_id", "coupons_id = '{$paramArray['coupons_id']}'");
        if ($policyOne) {
            $data = array();
            $data['coupons_isuse'] = '-1';
            $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
            $data['coupons_forbid'] = $name['staffer_cnname'];
            $data['coupons_disablereason'] = $paramArray['apply_disablereason'];;
            $data['coupons_forbidtime'] = time();
            $field = array();
            $field['coupons_isuse'] = "是否启用";

            if ($this->DataControl->updateData("smc_student_coupons", "coupons_id = '{$paramArray['coupons_id']}'", $data)) {

                $data = array();
                $data['card_status'] = -1;
                $this->DataControl->updateData("smc_activity_ticket_card", "coupons_id='{$paramArray['coupons_id']}'", $data);


                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "禁用成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '禁用失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑优惠券
    function updateCouponsAction($paramArray)
    {
        $CouponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,apply_id", "coupons_id = '{$paramArray['coupons_id']}'");
        if ($CouponsOne) {
            $data = array();
            $data['coupons_minprice'] = $paramArray['coupons_minprice'];
            $data['coupons_exittime'] = strtotime($paramArray['coupons_exittime']) + 60 * 60 * 24 - 1;
            $data['coupons_bindingtime'] = strtotime($paramArray['coupons_bindingtime']);
            $data['coupons_price'] = $paramArray['coupons_price'];
            $data['coupons_discount'] = $paramArray['coupons_discount'] / 10;

            $field = array();
            $field['coupons_type'] = "优惠方式0-减价 1-折扣";
            $field['coupons_minprice'] = "最低消费金额";
            $field['coupons_exittime'] = "结束时间";
            $field['coupons_bindingtime'] = "开始时间";

            if ($this->DataControl->updateData("smc_student_coupons", "coupons_id = '{$paramArray['coupons_id']}'", $data)) {
                $datas = array();

                $datas['apply_minprice'] = $paramArray['coupons_minprice'];
                $datas['apply_price'] = $paramArray['coupons_price'];
                $datas['apply_discount'] = $paramArray['coupons_discount'];
                $this->DataControl->updateData("smc_student_coupons_apply", "apply_id = '{$CouponsOne['apply_id']}'", $datas);

                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑优惠券成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑优惠券修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //获取优惠券附件
    function getCouponsFile($paramArray)
    {
        if ($paramArray['applytype_isshop'] == '1') {
            if ($paramArray['applytype_branch'] == 'wscyuangong' || $paramArray['applytype_branch'] == 'wscqinqi' || $paramArray['applytype_branch'] == 'wscneibu' || $paramArray['applytype_branch'] == 'wsctuijian') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_staff", "student_prove_img", "apply_id = '{$paramArray['apply_id']}'");
            } elseif ($paramArray['applytype_branch'] == 'wsctongbao') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_siblings", "student_prove_img,siblings_prove_img", "apply_id = '{$paramArray['apply_id']}'");
            } elseif ($paramArray['applytype_branch'] == 'wscshangpin') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_procou", "procou_scoreproveimg", "apply_id = '{$paramArray['apply_id']}'");
            }
        }

        if ($paramArray['applytype_isshop'] == '0') {
            $file = $this->DataControl->selectClear("select f.file_url from smc_student_coupons_file as f where f.apply_id = '{$paramArray['apply_id']}'");
        }

        $result = array();
        if ($file) {
            $result["data"] = $file;
            $res = array('error' => '0', 'errortip' => '获取班种列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班种列表失败', 'result' => $result);
        }
        return $res;
    }

    //获取申请信息
    function getApplyInfo($paramArray)
    {
        if ($paramArray['applytype_isshop'] == '1') {
            if ($paramArray['applytype_branch'] == 'wscyuangong' || $paramArray['applytype_branch'] == 'wscqinqi' || $paramArray['applytype_branch'] == 'wscneibu' || $paramArray['applytype_branch'] == 'wsctuijian') {
                $file = array();
            } elseif ($paramArray['applytype_branch'] == 'wsctongbao') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_siblings", "student_prove_img,siblings_prove_img", "apply_id = '{$paramArray['apply_id']}'");
            } elseif ($paramArray['applytype_branch'] == 'wscshangpin') {
                $file = $this->DataControl->getFieldOne("shop_student_coupons_apply_procou", "procou_scoreproveimg", "apply_id = '{$paramArray['apply_id']}'");
            }
        }

        if ($paramArray['applytype_isshop'] == '0') {
            $file = $this->DataControl->selectClear("select f.file_url from smc_student_coupons_file as f where f.apply_id = '{$paramArray['apply_id']}'");
        }

        $result = array();
        if ($file) {
            $result["data"] = $file;
            $res = array('error' => '0', 'errortip' => '获取班种列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班种列表失败', 'result' => $result);
        }
        return $res;
    }

    //复制优惠券
    function copyCouponsAction($paramArray)
    {
        $CouponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,apply_id", "coupons_id = '{$paramArray['coupons_id']}'");
        if ($CouponsOne) {
            $apply = $this->DataControl->selectOne("select * from smc_student_coupons_apply where apply_id = '{$CouponsOne['apply_id']}'");
            $data = array();
            $data['company_id'] = $apply['company_id'];
            $data['school_id'] = $apply['school_id'];
            $data['parenter_id'] = $apply['parenter_id'];
            $data['student_id'] = $apply['student_id'];
            $data['applytype_branch'] = $apply['applytype_branch'];
            $data['apply_playclass'] = $apply['apply_playclass'];
            $data['apply_discountstype'] = $apply['apply_discountstype'];
            $data['apply_price'] = $apply['apply_price'];
            $data['apply_discount'] = $apply['apply_discount'];
            $data['apply_minprice'] = $apply['apply_minprice'];
            $data['apply_reson'] = $apply['apply_reson'];
            $data['apply_fieldjson'] = $apply['apply_fieldjson'];
            $data['apply_status'] = '4';
            $data['apply_time'] = time();
            $data['apply_isshop'] = $apply['apply_isshop'];
            $data['staffer_id'] = $paramArray['staffer_id'];
            $data['apply_remark'] = $apply['apply_remark'];
            $applyid = $this->DataControl->insertData('smc_student_coupons_apply', $data);

            $applyCourse = $this->DataControl->selectClear("select * from smc_student_coupons_applycoursecat where apply_id = '{$CouponsOne['apply_id']}'");
            if ($applyCourse) {
                foreach ($applyCourse as $item) {
                    $data = array();
                    $data['apply_id'] = $applyid;
                    $data['coursecat_id'] = $item['coursecat_id'];
                    $this->DataControl->insertData('smc_student_coupons_applycoursecat', $data);
                }
            }
            $applyGoods = $this->DataControl->selectClear("select * from smc_student_coupons_applygoods where apply_id = '{$CouponsOne['apply_id']}'");
            if ($applyGoods) {
                foreach ($applyGoods as $item) {
                    $data = array();
                    $data['apply_id'] = $applyid;
                    $data['goods_id'] = $item['goods_id'];
                    $this->DataControl->insertData('smc_student_coupons_applygoods', $data);
                }
            }

            $Coupons = $this->DataControl->selectOne("select * from smc_student_coupons where coupons_id = '{$paramArray['coupons_id']}'");
            do {
                $coupons_pid = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);
            } while ($this->DataControl->selectOne("select coupons_id from smc_student_coupons where coupons_pid='{$coupons_pid}' and company_id='{$this->companyOne['company_id']}' limit 0,1"));

            $data = array();
            $data['coupons_class'] = $Coupons['coupons_class'];
            $data['coupons_range'] = $Coupons['coupons_range'];
            $data['company_id'] = $Coupons['company_id'];
            $data['student_id'] = $Coupons['student_id'];
            $data['apply_id'] = $applyid;
            $data['ticket_id'] = $Coupons['ticket_id'];
            $data['policy_id'] = $Coupons['policy_id'];
            $data['client_id'] = $Coupons['client_id'];
            $data['couponsrules_id'] = $Coupons['couponsrules_id'];
            $data['coupons_pid'] = $coupons_pid;
            $data['coupons_name'] = $Coupons['coupons_name'];
            $data['coupons_type'] = $Coupons['coupons_type'];
            $data['coupons_playclass'] = $Coupons['coupons_playclass'];
            $data['coupons_price'] = $Coupons['coupons_price'];
            $data['coupons_reason'] = $Coupons['coupons_reason'];
            $data['coupons_discount'] = $Coupons['coupons_discount'];
            $data['coupons_minprice'] = $Coupons['coupons_minprice'];
            $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
            $data['coupons_copy'] = $name['staffer_cnname'];
            $data['coupons_copytime'] = time();
            $data['coupons_exittime'] = strtotime("next year");
            $data['coupons_bindingtime'] = time();
            $data['coupons_isuse'] = '0';
            $data['coupons_createtime'] = time();


            if ($this->DataControl->insertData('smc_student_coupons', $data)) {
                $res = array('error' => '0', 'errortip' => "复制优惠券成功");
            } else {
                $res = array('error' => '1', 'errortip' => '复制优惠券修改失败');
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //协议内容
    function getTreatyList($paramArray)
    {
        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and t.treaty_name like '%{$paramArray['keyword']}%'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and (t.treaty_applytype=0 or (t.treaty_applytype=1 and a.school_id='{$paramArray['coursetype_id']}'))";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['status']) && $paramArray['status'] == "1") {
            $datawhere .= " and t.treaty_id > 0";
        }
        if (isset($paramArray['status']) && $paramArray['status'] == "0") {
            $datawhere .= " and t.treaty_id is null";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.coursetype_id,
                t.coursecat_id,
                c.coursetype_cnname,
                c.coursetype_branch,
                t.treaty_name,
                t.treaty_id,
                t.treaty_protocol,
                t.treaty_tabletip,
                t.treaty_refundinfo,
                t.treaty_buyinfo,
                t.treaty_applytype,
                t.treaty_applytype as applystatus,
                (select count(s.school_id) from smc_school as s left join smc_fee_treaty_apply as pa ON pa.school_id = s.school_id where pa.treaty_id = t.treaty_id and s.school_isclose = '0') as school_num,
                cc.coursecat_cnname
                   
            FROM
                smc_fee_treaty AS t
                LEFT JOIN smc_code_coursetype AS c ON c.coursetype_id = t.coursetype_id
                LEFT JOIN smc_code_coursecat AS cc ON cc.coursecat_id = t.coursecat_id
                LEFT JOIN smc_fee_treaty_apply as a on a.treaty_id = t.treaty_id
            WHERE
                {$datawhere} 
                and c.company_id = '{$paramArray['company_id']}' 
                and t.agreement_id = '{$paramArray['agreement_id']}'
            group by t.treaty_id 
            order by t.treaty_id desc
            LIMIT {$pagestart},{$num}";

        $AgreementList = $this->DataControl->selectClear($sql);

        if ($AgreementList) {
            foreach ($AgreementList as &$val) {
                if ($val['treaty_id'] == null) {
                    $val['treaty_id'] = '';
                }
                if ($val['treaty_protocol'] == null) {
                    $val['treaty_protocol'] = '';
                }
                if ($val['treaty_refundinfo'] == null) {
                    $val['treaty_refundinfo'] = '';
                }
                if ($val['treaty_buyinfo'] == null) {
                    $val['treaty_buyinfo'] = '';
                }
                if ($val['applystatus'] == 0) {
                    $val['applystatus'] = $this->LgStringSwitch("全部适用");
                } elseif ($val['applystatus'] == 1) {
                    $val['applystatus'] = $this->LgStringSwitch("部分适用({$val['school_num']})");
                }
                if ($val['treaty_id'] > 0) {
                    $val['status'] = '是';
                } else {
                    $val['status'] = '否';
                }
                if ($val['coursecat_id'] == '0'){
                    $val['coursecat_id'] = '';
                }
            }
        }

        $all_num = $this->DataControl->select("SELECT
                c.coursetype_id,
                c.coursetype_cnname,
                c.coursetype_branch,
                t.treaty_name,
                t.treaty_id,
                t.treaty_protocol,
                t.treaty_refundinfo,
                t.treaty_buyinfo,
                t.treaty_applytype,
                t.treaty_applytype as applystatus,
                (select count(s.school_id) from smc_school as s left join smc_fee_treaty_apply as pa ON pa.school_id = s.school_id where pa.treaty_id = t.treaty_id and s.school_isclose = '0') as school_num
            FROM
                smc_fee_treaty AS t
                LEFT JOIN smc_code_coursetype AS c ON c.coursetype_id = t.coursetype_id
                LEFT JOIN smc_fee_treaty_apply as a on a.treaty_id = t.treaty_id
            WHERE
                {$datawhere} 
                and c.company_id = '{$paramArray['company_id']}' 
                and t.agreement_id = '{$paramArray['agreement_id']}'
            group by t.treaty_id 
            order by t.treaty_id desc");
        $allnums = is_array($all_num) ? count($all_num) : 0;

        $fieldstring = array('treaty_name', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'applystatus');
        $fieldname = $this->LgArraySwitch(array('协议名称', '班组名称', '班组编号', '班种名称', '适用学校'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldmethod = array(0, 0, 0, 0, 1);
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldmethod[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($AgreementList) {
            $result['list'] = $AgreementList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无协议内容", 'result' => $result);
        }

        return $res;
    }


    //新增协议内容
    function addTreatyAction($paramArray)
    {
        $data = array();
        $data['treaty_name'] = $paramArray['treaty_name'];
        $data['agreement_id'] = $paramArray['agreement_id'];
        $data['treaty_applytype'] = $paramArray['treaty_applytype'];
        $data['coursetype_id'] = $paramArray['coursetype_id'];
        $data['coursecat_id'] = $paramArray['coursecat_id'];
        $data['treaty_protocol'] = $paramArray['treaty_protocol'];
        $data['treaty_tabletip'] = $paramArray['treaty_tabletip'];
        $data['treaty_createtime'] = time();

        $a = $this->DataControl->getFieldOne("smc_fee_treaty", "treaty_id", "agreement_id = '{$paramArray['agreement_id']}' and coursetype_id = '{$paramArray['coursetype_id']}' and treaty_applytype = '0'");
        if ($a && !$paramArray['coursecat_id']) {
            ajax_return(array('error' => '1', 'errortip' => "该课协议内容已被全部学校适用!"), $this->companyOne['company_language']);
        }

        $a = $this->DataControl->getFieldOne("smc_fee_treaty", "treaty_id", "agreement_id = '{$paramArray['agreement_id']}' and coursetype_id = '{$paramArray['coursetype_id']}' and coursecat_id = '0'");
        if ($a && $paramArray['treaty_applytype'] == '0' && !$paramArray['coursecat_id']) {
            ajax_return(array('error' => '1', 'errortip' => "已有部分学校班组适用该课程，无法全部适用!"), $this->companyOne['company_language']);
        }

        $a = $this->DataControl->selectClear("select t.treaty_id from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.treaty_applytype = '1' and t.coursecat_id = '{$paramArray['coursecat_id']}' and t.agreement_id = '{$paramArray['agreement_id']}'");

        if ($a && $paramArray['treaty_applytype'] == '0' && $paramArray['coursecat_id']) {
            ajax_return(array('error' => '1', 'errortip' => "该班种已有学校适配，无法全部适用!"), $this->companyOne['company_language']);
        }

        $a = $this->DataControl->selectClear("select t.treaty_id from smc_fee_treaty as t left join smc_fee_treaty_apply as a on t.treaty_id = a.treaty_id where t.treaty_applytype = '0' and t.coursecat_id = '{$paramArray['coursecat_id']}' and t.agreement_id = '{$paramArray['agreement_id']}'");

        if ($a && $paramArray['treaty_applytype'] == '1' && $paramArray['coursecat_id']) {
            ajax_return(array('error' => '1', 'errortip' => "该班种已有全部适配，无法部分适用!"), $this->companyOne['company_language']);
        }

        if ($tid = $this->DataControl->insertData('smc_fee_treaty', $data)) {
            $res = array('error' => '0', 'errortip' => "新增收费价格成功", 'id' => $tid);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '新增协议', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => "新增收费价格失败");
        }

        return $res;
    }

    //编辑协议
    function updateTreatyAction($paramArray)
    {

//        ajax_return(array('error' => 1, 'errortip' => "编辑功能正式服暂不开放!"), $this->companyOne['company_language']);


        $data = array();
        $data['treaty_name'] = $paramArray['treaty_name'];
        $data['treaty_id'] = $paramArray['treaty_id'];
        $data['treaty_protocol'] = $paramArray['treaty_protocol'];
        $data['treaty_tabletip'] = $paramArray['treaty_tabletip'];
        $data['treaty_updatatime'] = time();
        if ($this->DataControl->updateData("smc_fee_treaty", "agreement_id = '{$paramArray['agreement_id']}' and treaty_id = '{$paramArray['treaty_id']}'", $data)) {
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "编辑协议成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '编辑协议', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '编辑协议失败', 'result' => $result);
        }
        return $res;
    }


    //编辑协议表格
    function updateTreatyTipAction($paramArray)
    {
        $data = array();
        $data['treaty_tabletip'] = $paramArray['treaty_tabletip'];
        $data['treaty_updatatime'] = time();
        if ($this->DataControl->updateData("smc_fee_treaty", "agreement_id = '{$paramArray['agreement_id']}' and treaty_id = '{$paramArray['treaty_id']}'", $data)) {
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "编辑协议表格成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定", '编辑协议表格', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '编辑协议表格失败', 'result' => $result);
        }
        return $res;
    }


    //获取推荐信息
    function getShopRecommend($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and ( s.student_branch like '%{$paramArray['keyword']}%' or s.student_cnname like '%{$paramArray['keyword']}%' or c.client_cnname like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['stime']) && $paramArray['stime'] != '') {
            $stime = strtotime($paramArray['stime']);
            $datawhere .= " and  c.client_createtime > '{$stime}' ";
        }
        if (isset($paramArray['etime']) && $paramArray['etime'] != '') {
            $etime = strtotime($paramArray['etime']) + 86399;
            $datawhere .= " and  c.client_createtime < '{$etime}' ";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] > '0') {
            $datawhere .= " and  v.school_id = '{$paramArray['school_id']}' ";
        }

        if (isset($paramArray['isformal']) && $paramArray['isformal'] == '1') {
            $datawhere .= " and  c.client_tracestatus = '4' ";
        } elseif (isset($paramArray['isformal']) && $paramArray['isformal'] == '2') {
            $datawhere .= " and  c.client_tracestatus <> '4' ";
        }

        if (isset($paramArray['ishave']) && $paramArray['ishave'] == '1') {
            $datawhere .= " and (SELECT r.apply_id FROM shop_student_coupons_apply_recommend as r WHERE r.recommend_cnname = c.client_cnname and r.recommend_parentmobile = c.client_mobile limit 0,1)  is NULL  and v.student_branch > 0 ";
        } elseif (isset($paramArray['ishave']) && $paramArray['ishave'] == '2') {
            $datawhere .= " and (SELECT r.apply_id FROM shop_student_coupons_apply_recommend as r WHERE r.recommend_cnname = c.client_cnname and r.recommend_parentmobile = c.client_mobile limit 0,1) > 0 and v.student_branch > 0 ";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

//        $sql = "select s.student_branch,s.student_cnname,s.student_enname,c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_teachername,c.client_tracestatus,v.student_branch as student_branch2,h.school_shortname as school_shortname2,(SELECT r.apply_id FROM shop_student_coupons_apply_recommend as r WHERE r.recommend_cnname = c.client_cnname and r.recommend_parentmobile = c.client_mobile limit 0,1) as apply_id 
//                FROM crm_client as c 
//                LEFT JOIN smc_student as s ON c.client_stubranch = s.student_branch
//                LEFT JOIN crm_client_conversionlog as v ON (c.client_id = v.client_id and v.conversionlog_id <> '')  
//                LEFT JOIN smc_school as h ON v.school_id = h.school_id 
//                WHERE c.client_stubranch <> '' and c.company_id = '{$paramArray['company_id']}' and {$datawhere}
//                GROUP BY c.client_cnname 
//                ORDER BY c.client_id DESC,c.client_stubranch DESC
//                ";
//        LIMIT {$pagestart},{$num}

//        $sql = " SELECT * FROM (
//                select s.student_branch,s.student_cnname,s.student_enname,
//                c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_teachername,c.client_tracestatus,
//                v.student_branch as student_branch2,
//                h.school_shortname as school_shortname2,
//                (SELECT r.apply_id FROM shop_student_coupons_apply_recommend as r WHERE r.recommend_cnname = c.client_cnname and r.recommend_parentmobile = c.client_mobile limit 0,1) as apply_id 
//                FROM crm_client as c 
//                LEFT JOIN smc_student as s ON c.client_stubranch = s.student_branch
//                LEFT JOIN crm_client_conversionlog as v ON (c.client_id = v.client_id and v.conversionlog_id <> '')  
//                LEFT JOIN smc_school as h ON v.school_id = h.school_id 
//                WHERE c.client_stubranch <> '' and c.company_id = '{$paramArray['company_id']}' and {$datawhere}
//                ORDER BY c.client_id DESC,c.client_stubranch DESC
//                ) as t GROUP BY t.client_cnname ";

        $sql = "
                  select s.student_branch,s.student_cnname,s.student_enname,
                c.client_id,c.client_cnname,c.client_enname,c.client_mobile,c.client_teachername,c.client_tracestatus,c.client_patriarchname,c.client_createtime,
                c.channel_id,c.client_isgetdiscount,
                (SELECT ch.channel_name FROM crm_code_channel as ch WHERE ch.channel_id = c.channel_id) as channel_name,
                (SELECT p.parenter_cnname 
                FROM crm_client_family as f 
                LEFT JOIN smc_parenter as p on f.parenter_id = p.parenter_id 
                WHERE c.client_id = f.client_id and f.family_isdefault = 1) as parenter_cnname,
                (SELECT GROUP_CONCAT(sl.school_shortname) 
                FROM crm_client_schoolenter as cs 
                LEFT JOIN smc_school as sl ON cs.school_id = sl.school_id 
                WHERE cs.client_id = c.client_id ) as school_cnnamestr, -- 名单所在校区 
                st.student_id as student_id2,st.student_createtime as student_createtime2,
                v.student_branch as student_branch2,
                h.school_shortname as school_shortname2,
                (SELECT r.apply_id FROM shop_student_coupons_apply_recommend as r,smc_student_coupons_apply as a WHERE r.recommend_cnname = c.client_cnname and r.recommend_parentmobile = c.client_mobile and r.apply_id = a.apply_id and a.apply_status > '-1' limit 0,1) as apply_id 
                FROM crm_client as c 
                LEFT JOIN smc_student as s ON c.client_stubranch = s.student_branch
                LEFT JOIN crm_client_conversionlog as v ON (c.client_id = v.client_id and v.conversionlog_id <> '')  
                LEFT JOIN smc_school as h ON v.school_id = h.school_id  
                LEFT JOIN smc_student AS st ON v.student_branch = st.student_branch   
                WHERE c.client_stubranch <> '' and c.company_id = '{$paramArray['company_id']}' and {$datawhere}  
				GROUP BY c.client_id
                ORDER BY c.client_id DESC,c.client_stubranch DESC";

        // 优惠券类型
        $iscomplete = 0;
        $couponsapplytypeone = $this->DataControl->selectOne("select c.applytype_price as couponsrules_price
                      from smc_code_couponsapplytype as c 
                      WHERE c.applytype_branch = 'wsctuijian' and c.company_id = '{$paramArray['company_id']}' limit 0,1");
//        LEFT JOIN shop_code_couponsrules as r ON c.couponsrules_id = r.couponsrules_id
        if ($couponsapplytypeone['couponsrules_price'] > 0) {
            $iscomplete = '1';
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();

//                    $datearray['client_id'] = $dateexcelvar['client_id'];//序号
//                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//推荐学员（得券人）
//                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//推荐学员编号（得券人）
//                    $datearray['client_teachername'] = $dateexcelvar['client_teachername'];//推荐老师
//                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];//被推荐学员
//                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];//被推荐学员手机号
//                    $datearray['client_tracestatusname'] = $dateexcelvar['client_tracestatusname'];//被推荐学员状态
//                    $datearray['student_branch2'] = $dateexcelvar['student_branch2'];//被推荐学员编号
//                    $datearray['school_shortname2'] = $dateexcelvar['school_shortname2'];//被推荐学员学校

                    $datearray['client_id'] = $dateexcelvar['client_id'];//序号
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//推荐学员姓名
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//推荐学员编号
                    $datearray['client_createtime'] = date("Y-m-d H:i:s", $dateexcelvar['client_createtime']);//推荐时间
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];//被推荐学员姓名
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];//被推荐人联系人
                    $datearray['client_mobile'] = $dateexcelvar['client_mobile'];//被推荐人联系方式
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];//渠道名称
                    $datearray['school_cnnamestr'] = $dateexcelvar['school_cnnamestr'];//名单所在校区
                    $datearray['isbaoming'] = $dateexcelvar['student_id2'] > 0 ? "报名" : "未报名";//报名状态
                    $datearray['student_branch2'] = $dateexcelvar['student_branch2'];//报名学员编号
                    $datearray['school_shortname2'] = $dateexcelvar['school_shortname2'];//报名学校
                    $datearray['student_createtime2'] = $dateexcelvar['student_createtime2'] > 0 ? date("Y-m-d H:i:s", $dateexcelvar['student_createtime2']) : '';//报名时间
                    $outexceldate[] = $datearray;
                }
            }

//            $excelheader = $this->LgArraySwitch(array('序号', '推荐学员（得券人）', '推荐学员编号（得券人）', '推荐老师', '被推荐学员', '被推荐学员手机号', '被推荐学员状态', '被推荐学员编号', '被推荐学员学校'));
//            $excelfileds = array('client_id', 'student_cnname', 'student_branch', 'client_teachername', 'client_cnname', 'client_mobile', 'client_tracestatusname', 'student_branch2', 'school_shortname2');

            $excelheader = $this->LgArraySwitch(array('序号', '推荐学员姓名', '推荐学员编号', '推荐时间', '被推荐学员姓名', '被推荐人联系人', '被推荐人联系方式', '渠道名称', '名单所在校区', '报名状态', '报名学员编号', '报名学校', '报名时间'));
            $excelfileds = array('client_id', 'student_cnname', 'student_branch', 'client_createtime', 'client_cnname', 'parenter_cnname', 'client_mobile', 'channel_name', 'school_cnnamestr', 'isbaoming', 'student_branch2', 'school_shortname2', 'student_createtime2');
            $fielname = $this->LgStringSwitch("推荐管理");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $AgreementList = $this->DataControl->selectClear($sql);
        }

        if ($AgreementList) {
            foreach ($AgreementList as &$AgreementVar) {
                $AgreementVar['client_teachername'] = $AgreementVar['client_teachername'] ? $AgreementVar['client_teachername'] : '--';
//                $AgreementVar['client_tracestatusname'] = ($AgreementVar['client_tracestatus'] == '4') ? '已转正' : '未转正';
                $AgreementVar['client_tracestatusname'] = ($AgreementVar['student_id2'] > '0') ? '已转正' : '未转正';

                $AgreementVar['client_createtime'] = date("Y-m-d H:i:s", $AgreementVar['client_createtime']);//推荐时间
                $AgreementVar['student_createtime2'] = $AgreementVar['student_createtime2'] > 0 ? date("Y-m-d H:i:s", $AgreementVar['student_createtime2']) : '';//报名时间
                if ($AgreementVar['apply_id'] < 1 && $AgreementVar['student_branch2'] > 0 && $AgreementVar['client_isgetdiscount'] == '0') {
                    $AgreementVar['iscan'] = 1;
                } else {
                    $AgreementVar['iscan'] = 0;
                }
                $AgreementVar['iscomplete'] = $iscomplete;//是否完整： 1  完整   0 不完整
                $AgreementVar['isrepeat'] = ($AgreementVar['sutbranchnum'] > 1) ? '重复' : '否';
            }
        }

        $Agreementarray = $this->DataControl->selectClear("select c.client_id,c.client_cnname 
                FROM crm_client as c 
                LEFT JOIN smc_student as s ON c.client_stubranch = s.student_branch
                LEFT JOIN crm_client_conversionlog as v ON (c.client_id = v.client_id and v.conversionlog_id <> '')  
                LEFT JOIN smc_school as h ON v.school_id = h.school_id  
                LEFT JOIN smc_student AS st ON v.student_branch = st.student_branch   
                WHERE c.client_stubranch <> '' and c.company_id = '{$paramArray['company_id']}' and {$datawhere} 
                GROUP BY  c.client_id");
        if ($Agreementarray) {
            $all_num = count($Agreementarray) + 0;
        } else {
            $all_num = 0;
        }
        $allnums = $all_num;

//        $fieldstring = array('client_id', 'student_cnname', 'student_branch', 'client_teachername', 'client_cnname', 'client_mobile', 'client_tracestatusname', 'student_branch2', 'school_shortname2');
//        $fieldname = $this->LgArraySwitch(array('序号', '推荐学员（得券人）', '推荐学员编号（得券人）', '推荐老师', '被推荐学员', '被推荐学员手机号', '被推荐学员状态', '被推荐学员编号', '被推荐学员学校'));

        $fieldname = $this->LgArraySwitch(array('序号', '推荐学员姓名', '推荐学员编号', '推荐时间', '被推荐学员姓名', '被推荐人联系人', '被推荐人联系方式', '渠道名称', '名单所在校区', '报名状态', '报名学员编号', '报名学校', '报名时间'));
        $fieldstring = array('client_id', 'student_cnname', 'student_branch', 'client_createtime', 'client_cnname', 'parenter_cnname', 'client_mobile', 'channel_name', 'school_cnnamestr', 'client_tracestatusname', 'student_branch2', 'school_shortname2', 'student_createtime2');

        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);

        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($AgreementList) {
            $result['list'] = $AgreementList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无推荐内容", 'result' => $result);
        }

        return $res;
    }

    //线下核销 推荐优惠券的优惠啊
    function updateOffsetDiscountAction($paramArray)
    {
        if (!$this->DataControl->selectOne("select client_id from crm_client WHERE client_id = '{$paramArray['client_id']}' and company_id = '{$paramArray['company_id']}' ")) {
            $res = array('error' => '1', 'errortip' => '被推荐有效名单的信息不存在！', 'result' => array());
            return $res;
        }
        $data = array();
        $data['client_isgetdiscount'] = "1";
        $data['client_getdiscounttime'] = time();

        if ($this->DataControl->updateData("crm_client", "client_id = '{$paramArray['client_id']}' and company_id = '{$paramArray['company_id']}'", $data)) {
            $res = array('error' => '0', 'errortip' => '线下核销成功', 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '线下核销失败', 'result' => array());
        }

        return $res;
    }

    //生成推荐申请
    function addCouponsApplyAction($paramArray)
    {
        // 优惠券类型
        $couponsapplytypeone = $this->DataControl->selectOne("select c.applytype_price as couponsrules_price
                      from smc_code_couponsapplytype as c 
                      WHERE c.applytype_branch = 'wsctuijian' and c.company_id = '{$paramArray['company_id']}' limit 0,1");
        if ($couponsapplytypeone['couponsrules_price'] <= 0) {
            $res = array('error' => '1', 'errortip' => '未设置推荐券发放金额！', 'result' => array());
            return $res;
        }

        //客户的信息
        $clientOne = $this->DataControl->selectOne("SELECT c.client_cnname, c.client_mobile,c.client_patriarchname, c.client_sex, c.client_birthday, c.client_address
, c.client_stubranch,s.student_id,s.student_branch FROM smc_student AS s,crm_client AS c WHERE s.from_client_id = c.client_id AND c.client_id = '{$paramArray['client_id']}'");
        if (!$clientOne) {
            $res = array('error' => '1', 'errortip' => '客户信息查询有误，无法发放，请联系技术！', 'result' => array());
            return $res;
        }
        //推荐学员的信息
        $stuOne = $this->DataControl->selectOne("SELECT s.company_id,s.student_id,s.student_branch,s.student_cnname,e.school_id, f.parenter_id
FROM smc_student AS s,smc_student_family AS f,smc_student_enrolled AS e,smc_school as h 
WHERE s.student_id = f.student_id AND s.student_id = e.student_id AND e.enrolled_status IN(0,1,3) AND s.student_branch = '{$clientOne['client_stubranch']}'  and e.school_id = h.school_id and h.company_id = '8888' ");
        if (!$stuOne) {
            $res = array('error' => '1', 'errortip' => '未查询到在读的推荐学员信息，无法发放优惠券', 'result' => array());
            return $res;
        }

        if ($this->DataControl->selectOne("SELECT a.apply_id FROM smc_student_coupons_apply AS a, shop_student_coupons_apply_recommend AS r WHERE a.apply_id = r.apply_id
AND a.applytype_branch = 'wsctuijian' AND a.apply_status <> '-1' AND r.student_id = '{$clientOne['student_id']}'")) {
            $res = array('error' => '1', 'errortip' => "被推荐学员已生成推荐申请，请勿重复申请！", 'result' => array());
            return $res;
        }

        $one = array();
        $one['company_id'] = $stuOne['company_id'];
        $one['school_id'] = $stuOne['school_id'];
        $one['parenter_id'] = $stuOne['parenter_id'];
        $one['student_id'] = $stuOne['student_id'];
        $one['applytype_branch'] = 'wsctuijian';
        $one['apply_reson'] = "(微商城)检测到学员推荐{$clientOne['client_cnname']}已报名成功,后台管理人员进行的操作发放，进行优惠券发放申请！";
        $one['apply_discountstype'] = '0';//
        $one['apply_status'] = '0';//
        $one['apply_playclass'] = '1';//推荐订单优惠券
        $one['apply_discountstype'] = '0';//
        $one['apply_price'] = $couponsapplytypeone['couponsrules_price'];
        $one['apply_time'] = time();
        if ($apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $one)) {
            $dataOne = array();
            $dataOne['apply_id'] = $apply_id;//
            $dataOne['student_id'] = $clientOne['student_id'];//-------
            $dataOne['recommend_cnname'] = $clientOne['client_cnname'];//
            $dataOne['recommend_parentname'] = $clientOne['client_patriarchname'];//
            $dataOne['recommend_parentmobile'] = $clientOne['client_mobile'];//
            $dataOne['recommend_sex'] = $clientOne['client_sex'];//
            $dataOne['recommend_birthday'] = $clientOne['client_birthday'];//
            $dataOne['recommend_address'] = $clientOne['client_address'];//
            $dataOne['recommend_createtime'] = time();
            $this->DataControl->insertData("shop_student_coupons_apply_recommend", $dataOne);
        }

        if ($apply_id) {
            $res = array('error' => '0', 'errortip' => '推荐申请单生成成功', 'result' => array());
        } else {
            $res = array('error' => '1', 'errortip' => '推荐申请单生成失败', 'result' => array());
        }

        return $res;
    }


    function batchaddCouponsApplyAction($paramArray)
    {
        if (isset($paramArray['client_id_list']) && count($paramArray['client_id_list']) > 0) {
            $paramArray['client_id_list'] = stripslashes($paramArray['client_id_list']);
            $paramArray['client_id_list'] = json_decode($paramArray['client_id_list'], true);

            // 优惠券类型
            $couponsapplytypeone = $this->DataControl->selectOne("select c.applytype_price as couponsrules_price
                      from smc_code_couponsapplytype as c 
                      WHERE c.applytype_branch = 'wsctuijian' and c.company_id = '{$paramArray['company_id']}' limit 0,1");
            if ($couponsapplytypeone['couponsrules_price'] <= 0) {
                $res = array('error' => '1', 'errortip' => '未设置推荐券发放金额！', 'result' => array());
                return $res;
            }

            $scc = 0;
            $fal = 0;

            foreach ($paramArray['client_id_list'] as $varid){
                //客户的信息
                $clientOne = $this->DataControl->selectOne("SELECT c.client_cnname, c.client_mobile,c.client_patriarchname, c.client_sex, c.client_birthday,c.client_address,c.client_stubranch,s.student_id,s.student_branch FROM smc_student AS s,crm_client AS c WHERE s.from_client_id = c.client_id AND c.client_id = '{$varid}'");
                if (!$clientOne) {
                    $fal++;
                    continue;
                }
                //推荐学员的信息
                $stuOne = $this->DataControl->selectOne("SELECT s.company_id,s.student_id,s.student_branch,s.student_cnname,e.school_id, f.parenter_id 
FROM smc_student AS s,smc_student_family AS f,smc_student_enrolled AS e
WHERE s.student_id = f.student_id AND s.student_id = e.student_id AND e.enrolled_status IN(0,1,3) AND s.student_branch = '{$clientOne['client_stubranch']}'");
                if (!$stuOne) {
                    $fal++;
                    continue;
                }
                if ($this->DataControl->selectOne("SELECT a.apply_id FROM smc_student_coupons_apply AS a, shop_student_coupons_apply_recommend AS r WHERE a.apply_id = r.apply_id AND a.applytype_branch = 'wsctuijian' AND a.apply_status <> '-1' AND r.student_id = '{$clientOne['student_id']}'")) {
                    $fal++;
                    continue;
                }

                $one = array();
                $one['company_id'] = $stuOne['company_id'];
                $one['school_id'] = $stuOne['school_id'];
                $one['parenter_id'] = $stuOne['parenter_id'];
                $one['student_id'] = $stuOne['student_id'];
                $one['applytype_branch'] = 'wsctuijian';
                $one['apply_reson'] = "(微商城)检测到学员推荐{$clientOne['client_cnname']}已报名成功,后台管理人员进行的操作发放，进行优惠券发放申请！";
                $one['apply_discountstype'] = '0';//
                $one['apply_status'] = '0';//
                $one['apply_playclass'] = '1';//推荐订单优惠券
                $one['apply_discountstype'] = '0';//
                $one['apply_price'] = $couponsapplytypeone['couponsrules_price'];
                $one['apply_time'] = time();
                if ($apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $one)) {
                    $scc++;
                    $dataOne = array();
                    $dataOne['apply_id'] = $apply_id;//
                    $dataOne['student_id'] = $clientOne['student_id'];//-------
                    $dataOne['recommend_cnname'] = $clientOne['client_cnname'];//
                    $dataOne['recommend_parentname'] = $clientOne['client_patriarchname'];//
                    $dataOne['recommend_parentmobile'] = $clientOne['client_mobile'];//
                    $dataOne['recommend_sex'] = $clientOne['client_sex'];//
                    $dataOne['recommend_birthday'] = $clientOne['client_birthday'];//
                    $dataOne['recommend_address'] = $clientOne['client_address'];//
                    $dataOne['recommend_createtime'] = time();
                    $this->DataControl->insertData("shop_student_coupons_apply_recommend", $dataOne);
                }else{
                    $fal++;
                }
            }

            if($scc>0 && $fal=0){
                $res = array('error' => '0', 'errortip' => '推荐申请单全部生成成功', 'result' => array());
            }elseif($scc>0 && $fal>0){
                $res = array('error' => '0', 'errortip' => "推荐申请单成功生成{$scc}个，失败{$fal}个", 'result' => array());
            }else{
                $res = array('error' => '0', 'errortip' => '推荐申请单生成成功', 'result' => array());
            }
            return $res;

        } else {
            ajax_return(array('error' => 1, 'errortip' => "未选择数据!", "bakfuntion" => "errormotify"));
        }
    }

    //生成推荐申请
    function batchaddCouponsApplyAction_bak($paramArray)
    {
//        print_r($paramArray);die;
        if (isset($paramArray['client_id_list']) && count($paramArray['client_id_list']) > 0) {
            $paramArray['client_id_list'] = stripslashes($paramArray['client_id_list']);
            $paramArray['client_id_list'] = json_decode($paramArray['client_id_list'], true);
//            print_r($paramArray);die;

            // 优惠券类型
            $couponsapplytypeone = $this->DataControl->selectOne("select c.applytype_price as couponsrules_price
                      from smc_code_couponsapplytype as c 
                      WHERE c.applytype_branch = 'wsctuijian' and c.company_id = '{$paramArray['company_id']}' limit 0,1");
            if ($couponsapplytypeone['couponsrules_price'] <= 0) {
                $res = array('error' => '1', 'errortip' => '未设置推荐券发放金额！', 'result' => array());
                return $res;
            }

            foreach ($paramArray['client_id_list'] as $varid){
//                print_r($varid);die;
                //客户的信息
                $clientOne = $this->DataControl->selectOne("SELECT c.client_cnname, c.client_mobile,c.client_patriarchname, c.client_sex, c.client_birthday,c.client_address,c.client_stubranch,s.student_id,s.student_branch FROM smc_student AS s,crm_client AS c WHERE s.from_client_id = c.client_id AND c.client_id = '{$varid}'");
                if (!$clientOne) {
//                    $res = array('error' => '1', 'errortip' => '客户信息查询有误，无法发放，请联系技术！', 'result' => array());
                    $res = array('error' => '1', 'errortip' => '客户信息存在错误数据，请单个操作！', 'result' => array());
                    return $res;
                }
                //推荐学员的信息
                $stuOne = $this->DataControl->selectOne("SELECT s.company_id,s.student_id,s.student_branch,s.student_cnname,e.school_id, f.parenter_id
FROM smc_student AS s,smc_student_family AS f,smc_student_enrolled AS e
WHERE s.student_id = f.student_id AND s.student_id = e.student_id AND e.enrolled_status IN(0,1,3) AND s.student_branch = '{$clientOne['client_stubranch']}'");
                if (!$stuOne) {
//                    $res = array('error' => '1', 'errortip' => '未查询到在读的推荐学员信息，无法发放优惠券', 'result' => array());
                    $res = array('error' => '1', 'errortip' => '存在未查询到在读的推荐学员信息，无法发放优惠券，请单个操作！', 'result' => array());
                    return $res;
                }
                if ($this->DataControl->selectOne("SELECT a.apply_id FROM smc_student_coupons_apply AS a, shop_student_coupons_apply_recommend AS r WHERE a.apply_id = r.apply_id AND a.applytype_branch = 'wsctuijian' AND a.apply_status <> '-1' AND r.student_id = '{$clientOne['student_id']}'")) {
//                    $res = array('error' => '1', 'errortip' => "被推荐学员已生成推荐申请，请勿重复申请！", 'result' => array());
                    $res = array('error' => '1', 'errortip' => "存在被推荐学员已生成推荐申请，请单个操作！", 'result' => array());
                    return $res;
                }
            }

            $scc = 0;
            $fal = 0;
            foreach ($paramArray['client_id_list'] as $varid){
//                print_r($clientOne);
//                print_r($stuOne);die;

                $one = array();
                $one['company_id'] = $stuOne['company_id'];
                $one['school_id'] = $stuOne['school_id'];
                $one['parenter_id'] = $stuOne['parenter_id'];
                $one['student_id'] = $stuOne['student_id'];
                $one['applytype_branch'] = 'wsctuijian';
                $one['apply_reson'] = "(微商城)检测到学员推荐{$clientOne['client_cnname']}已报名成功,后台管理人员进行的操作发放，进行优惠券发放申请！";
                $one['apply_discountstype'] = '0';//
                $one['apply_status'] = '0';//
                $one['apply_playclass'] = '1';//推荐订单优惠券
                $one['apply_discountstype'] = '0';//
                $one['apply_price'] = $couponsapplytypeone['couponsrules_price'];
                $one['apply_time'] = time();
                if ($apply_id = $this->DataControl->insertData('smc_student_coupons_apply', $one)) {
                    $scc++;
                    $dataOne = array();
                    $dataOne['apply_id'] = $apply_id;//
                    $dataOne['student_id'] = $clientOne['student_id'];//-------
                    $dataOne['recommend_cnname'] = $clientOne['client_cnname'];//
                    $dataOne['recommend_parentname'] = $clientOne['client_patriarchname'];//
                    $dataOne['recommend_parentmobile'] = $clientOne['client_mobile'];//
                    $dataOne['recommend_sex'] = $clientOne['client_sex'];//
                    $dataOne['recommend_birthday'] = $clientOne['client_birthday'];//
                    $dataOne['recommend_address'] = $clientOne['client_address'];//
                    $dataOne['recommend_createtime'] = time();
                    $this->DataControl->insertData("shop_student_coupons_apply_recommend", $dataOne);
                }else{
                    $fal++;
                }

                if($scc>0 && $fal=0){
                    $res = array('error' => '0', 'errortip' => '推荐申请单全部生成成功', 'result' => array());
                }elseif($scc>0 && $fal>0){
                    $res = array('error' => '0', 'errortip' => "推荐申请单成功生成{$scc}个，失败{$fal}个", 'result' => array());
                }else{
                    $res = array('error' => '0', 'errortip' => '推荐申请单生成成功', 'result' => array());
                }
                return $res;
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "未选择数据!", "bakfuntion" => "errormotify"));
        }
    }

    //获取亲戚子女信息
    function getRelativeChild($paramArray)
    {
        $datawhere = " a.company_id = '{$paramArray['company_id']}' and a.relative_unbound = '0' ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and ( a.student_branch like '%{$paramArray['keyword']}%' or a.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or a.relative_name like '%{$paramArray['keyword']}%' or a.relative_mobile like '%{$paramArray['keyword']}%'  or f.staffer_cnname like '%{$paramArray['keyword']}%'  or f.staffer_enname like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%'  )";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    a.*,p.parenter_cnname,s.student_enname,p.parenter_mobile,f.staffer_cnname,f.staffer_enname  
                FROM shop_coupons_apply_relative as a
                LEFT JOIN smc_parenter as p on p.parenter_id = a.parenter_id 
                left join smc_staffer as f on (f.staffer_mobile = p.parenter_mobile and f.company_id = a.company_id)
                LEFT JOIN smc_student as s on s.student_branch = a.student_branch 
                WHERE {$datawhere}
                GROUP BY a.parenter_id
                ORDER BY a.relative_createtime DESC
                LIMIT {$pagestart},{$num}";

        $RelativeList = $this->DataControl->selectClear($sql);

        if ($RelativeList) {
            foreach ($RelativeList as &$val) {
                if ($val['relative_updatetime']) {
                    $val['relative_updatetime'] = date("Y-m-d H:i", $val['relative_updatetime']);
                } else {
                    $val['relative_updatetime'] = '--';
                }
                if($val['staffer_enname']){
                    $val['staffer_cnname'] = $val['staffer_cnname'] . '/' . $val['staffer_enname'] ;
                }
                if($val['student_enname']){
                    $val['student_cnname'] = $val['student_cnname'] . '/' . $val['student_enname'] ;
                }

                $val['relative_createtime'] = date("Y-m-d H:i", $val['relative_createtime']);
            }
        }
// 职工姓名  职工手机号  绑定亲戚姓名  绑定亲戚联系电话  亲戚子女姓名  亲戚子女编号  学员性别  学员年龄  确认报名校园   绑定时间   更新时间
        $all_num = $this->DataControl->selectOne("SELECT COUNT(q.relative_id) as num FROM
                                                    (SELECT a.relative_id
                                                    FROM shop_coupons_apply_relative as a
                                                    LEFT JOIN smc_parenter as p on p.parenter_id = a.parenter_id 
                                                    left join smc_staffer as f on (f.staffer_mobile = p.parenter_mobile and f.company_id = a.company_id)
                                                    LEFT JOIN smc_student as s on s.student_branch = a.student_branch
                                                    WHERE {$datawhere}
                                                    GROUP BY a.parenter_id) as q");
        $allnums = $all_num['num'];

        $fieldstring = array('staffer_cnname','parenter_mobile','relative_name', 'relative_mobile', 'student_cnname', 'student_branch', 'student_sex', 'student_age', 'student_school', 'relative_createtime', 'relative_updatetime', 'relative_unbound');
        $fieldname = $this->LgArraySwitch(array('职工姓名','职工手机号','绑定亲戚姓名', '绑定亲戚联系电话', '亲戚子女姓名', '亲戚子女编号', '学员性别', '学员年龄', '确认报名校园', '绑定时间', '更新时间', '是否解绑'));
        $fieldcustom = array("1","1","1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1","1","1", "1", "1", "1", "1", "1", "1", "1", "1","0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($RelativeList) {
            $result['list'] = $RelativeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无亲戚子女", 'result' => $result);
        }

        return $res;
    }

    //获取亲戚子女信息
    function unboundRelativeChild($paramArray)
    {
        if(!isset($paramArray['relative_id']) || $paramArray['relative_id']=='' || $paramArray['relative_id']=='0'){
            $this->error = true;
            $this->errortip = "亲戚子女关系表ID必须传";
            return false;
        }

        $data=array();
        $data['relative_unbound']=1;
        $data['relative_unboundtime']=time();

        if($this->DataControl->updateData("shop_coupons_apply_relative","relative_id='{$paramArray['relative_id']}'",$data)){
            $this->error = 0;
            $this->errortip = "解绑成功";
            return true;
        }else{
            $this->error = 1;
            $this->errortip = "解绑失败";
            return false;
        }
    }

}
