<?php


namespace Model\Gmc;

class CouponsModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getCouponsActivity($request){
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.ticket_cnname like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and t.ticket_startday>='{$request['starttime']}'";
            $datawhere .= " and t.ticket_startday<='{$request['endtime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and t.ticket_endday>='{$request['starttime']}'";
            $datawhere .= " and t.ticket_endday<='{$request['endtime']}'";
        }

        if (isset($request['ticket_status']) && $request['ticket_status'] !== '') {
            $datawhere .= " and t.ticket_status='{$request['ticket_status']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere.=" and ((t.ticket_applyschool=0) or (t.ticket_id in (select sc.ticket_id from smc_activity_ticket_schoolapply as sc where sc.school_id='{$request['school_id']}')))";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "SELECT t.ticket_id,t.ticket_cnname,t.ticket_startday,t.ticket_endday,t.ticket_note,t.ticket_applyschool,t.ticket_status,t.ticket_islimitnum,t.ticket_limitnum
                ,(select count(ts.ticket_id) from smc_activity_ticket_schoolapply as ts where ts.ticket_id=t.ticket_id) as schooolNum
                ,ifnull((select b.card_id from smc_activity_ticket_setup as a,smc_activity_ticket_card as b where a.setup_id=b.setup_id and a.ticket_id=t.ticket_id limit 0,1),0) as can_export
                from smc_activity_ticket as t
                where {$datawhere} and t.company_id='{$this->company_id}'
                order by (case when t.ticket_id='434' then 1 else 2 end) asc,t.ticket_createtime desc
                limit {$pagestart},{$num}";

        $ticketList = $this->DataControl->selectClear($sql);
        if (!$ticketList) {
            $this->error = true;
            $this->errortip = "无优惠券活动";
            return false;
        }

        $status=$this->LgArraySwitch(array('0'=>'编辑中','1'=>'生效中','-1'=>'已禁用'));
        foreach ($ticketList as &$ticketOne) {
            if($ticketOne['ticket_applyschool']==0){
                $ticketOne['schooolNum']='全部';
            }
            $ticketOne['ticket_status_name']=$status[$ticketOne['ticket_status']];

            $ticketOne['validity']=$ticketOne['ticket_startday'].'-'.$ticketOne['ticket_endday'];
        }

        $data = array();
        $count_sql = "select t.ticket_id
                ,(select count(ts.ticket_id) from smc_activity_ticket_schoolapply as ts where ts.ticket_id=t.ticket_id) as schooolNum
                from smc_activity_ticket as t
                where {$datawhere} and t.company_id='{$this->company_id}'";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums?count($db_nums):0;

        $data['allnum'] = $allnum;

        $data['list'] = $ticketList;

        return $data;
    }

    function addCouponsActivity($request){
        $data=array();
        $data['ticket_cnname']=$request['ticket_cnname'];
        $data['company_id']=$this->company_id;
        $data['ticket_applyschool']=$request['ticket_applyschool'];
        $data['ticket_startday']=$request['ticket_startday'];
        $data['ticket_endday']=$request['ticket_endday'];
        $data['ticket_note']=$request['ticket_note'];
        $data['ticket_islimitnum']=$request['ticket_islimitnum'];
        $data['ticket_limitnum']=$request['ticket_limitnum'];
        $data['ticket_createtime']=time();
        $ticket_id=$this->DataControl->insertData("smc_activity_ticket",$data);
        if($ticket_id){
            return $ticket_id;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function editCouponsActivity($request){

        if(!isset($request['ticket_id']) || $request['ticket_id']=='' || $request['ticket_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }

        $data=array();
        $data['ticket_cnname']=$request['ticket_cnname'];
        $data['ticket_applyschool']=$request['ticket_applyschool'];
        $data['ticket_startday']=$request['ticket_startday'];
        $data['ticket_endday']=$request['ticket_endday'];
        $data['ticket_note']=$request['ticket_note'];

        if(!$this->DataControl->getFieldOne("smc_activity_ticket_setup","setup_id","ticket_id='{$request['ticket_id']}'")){
            $data['ticket_islimitnum']=$request['ticket_islimitnum'];
            $data['ticket_limitnum']=$request['ticket_limitnum'];
        }

        $data['ticket_updatatime']=time();

        if($this->DataControl->updateData("smc_activity_ticket","ticket_id='{$request['ticket_id']}'",$data)){

            if($request['ticket_applyschool']==0){
                $this->DataControl->delData("smc_activity_ticket_schoolapply","ticket_id='{$request['ticket_id']}'");
            }

            $setupdata=array();
            $setupdata['setup_endday']=$request['ticket_endday'];
            $this->DataControl->updateData("smc_activity_ticket_setup","ticket_id='{$request['ticket_id']}'",$setupdata);

            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function delCouponsActivity($request){

        if(!isset($request['ticket_id']) || $request['ticket_id']=='' || $request['ticket_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }


        $sql="select tc.ticket_id
              from smc_activity_ticket_card as t
              left join smc_activity_ticket_setup as tc on tc.setup_id=t.setup_id
              where tc.ticket_id='{$request['ticket_id']}'";
        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "已产生优惠券活动激活码,不可移除";
            return false;
        }

        $this->DataControl->delData("smc_activity_ticket","ticket_id='{$request['ticket_id']}'");
        return true;

    }

    function getSchoolList($request){
        if(!isset($request['ticket_id']) || $request['ticket_id']=='' || $request['ticket_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }

        $datawhere=" 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_enname like '%{$request['keyword']}%' or sc.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and sc.district_id='{$request['district_id']}'";
        }

        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] !== '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        $sql="select sc.school_id,sc.school_branch,sc.school_cnname,sc.school_enname,cd.district_cnname
              from smc_school as sc
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}'
              and sc.school_id not in (select ts.school_id from smc_activity_ticket_schoolapply as ts where ts.ticket_id='{$request['ticket_id']}')
              ORDER BY sc.school_sort DESC
              ";

        $schoolList=$this->DataControl->selectClear($sql);
        if(!$schoolList){
            $this->error = true;
            $this->errortip = "无相关学校";
            return false;
        }

        return $schoolList;

    }

    function getApplySchoolList($request){

        $datawhere=" 1 ";
        if (isset($request['ticket_id']) && $request['ticket_id'] !== '') {
            $datawhere .= " and ts.ticket_id='{$request['ticket_id']}'";
        }else{
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }

        $sql="select sc.school_id,sc.school_branch,sc.school_cnname,sc.school_enname,cd.district_cnname
              from smc_activity_ticket_schoolapply as ts
              left join smc_school as sc on sc.school_id=ts.school_id
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}'
              ORDER BY sc.school_sort DESC
              ";

        $schoolList=$this->DataControl->selectClear($sql);
        if(!$schoolList){
            $this->error = true;
            $this->errortip = "无适配学校";
            return false;
        }

        return $schoolList;

    }

    function applySchool($request){
        if(!isset($request['ticket_id']) || $request['ticket_id']=='' || $request['ticket_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }
        $schoolList = json_decode(stripslashes($request['school_list']),true);
        if($schoolList){
            foreach ($schoolList as $schoolOne) {
                $schoolapply=$this->DataControl->getFieldOne("smc_activity_ticket_schoolapply","school_id","school_id='{$schoolOne['school_id']}' and ticket_id='{$request['ticket_id']}'");
                if(!$schoolapply){
                    $data = array();
                    $data['school_id'] = $schoolOne['school_id'];
                    $data['ticket_id'] = $request['ticket_id'];
                    $this->DataControl->insertData('smc_activity_ticket_schoolapply',$data);
                }
            }
        }else{
            $this->error = true;
            $this->errortip = "请勿传入空值";
            return false;
        }

        return true;
    }

    function removeSchool($request){
        if(!isset($request['ticket_id']) || $request['ticket_id']=='' || $request['ticket_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }

        if(!isset($request['school_id']) || $request['school_id']=='' || $request['school_id']=='0'){
            $this->error = true;
            $this->errortip = "学校ID必须传";
            return false;
        }
        $where = "ticket_id='{$request['ticket_id']}'";
        $school_list = json_decode(stripslashes($request['school_id']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $school_id = implode(',', $str);
            $where .= " and school_id in ({$school_id})";
        }else{
            $where .= " and school_id = '{$request['school_id']}'";
        }

        $sql="select tc.ticket_id
              from smc_activity_ticket_card as t
              left join smc_activity_ticket_setup as tc on tc.setup_id=t.setup_id
              where tc.ticket_id='{$request['ticket_id']}'";
        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "已产生优惠券活动激活码,不可移除";
            return false;
        }

        $this->DataControl->delData("smc_activity_ticket_schoolapply", $where);

        return true;
    }

    function getTicketCoupons($request){

        $datawhere = " 1 ";

        if (isset($request['ticket_id']) && $request['ticket_id'] !== '') {
            $datawhere .= " and tc.ticket_id='{$request['ticket_id']}'";
        }else{
            $this->error = true;
            $this->errortip = "必须传入优惠券活动ID";
            return false;
        }

        if (isset($request['applytype_id']) && $request['applytype_id'] !== '') {
            $datawhere .= " and tc.applytype_id='{$request['applytype_id']}'";
        }

        if (isset($request['setup_type']) && $request['setup_type'] !== '') {
            $datawhere .= " and tc.setup_type='{$request['setup_type']}'";
        }

        if (isset($request['setup_playclass']) && $request['setup_playclass'] !== '') {
            $datawhere .= " and tc.setup_playclass='{$request['setup_playclass']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select tc.*,cl.applytype_cnname,ti.ticket_endday
                ,(select count(t.card_id) from smc_activity_ticket_card as t where t.setup_id=tc.setup_id ) as allNum
                ,(select count(t.card_id) from smc_activity_ticket_card as t where t.setup_id=tc.setup_id and t.card_status=0) as beNum
                ,(select count(t.card_id) from smc_activity_ticket_card as t where t.setup_id=tc.setup_id and t.card_status=1) as tobeNum
                ,(select count(t.card_id) from smc_activity_ticket_card as t where t.setup_id=tc.setup_id and t.card_status=2) as hasNum
                ,(select count(t.card_id) from smc_activity_ticket_card as t where t.setup_id=tc.setup_id and t.card_status='-1') as noNum
                ,(select count(t.course_id) from smc_activity_ticket_courseapply as t inner join smc_course as co on co.course_id=t.course_id where t.setup_id=tc.setup_id) as courseNum
                from smc_activity_ticket_setup as tc
                left join smc_code_couponsapplytype as cl on cl.applytype_id=tc.applytype_id
                left join smc_activity_ticket as ti on ti.ticket_id=tc.ticket_id
                where {$datawhere}
                order by tc.setup_createtime desc
                limit {$pagestart},{$num}";

        $couponsList = $this->DataControl->selectClear($sql);
        if (!$couponsList) {
            $this->error = true;
            $this->errortip = "无优惠券活动";
            return false;
        }

        $class=$this->LgArraySwitch(array('0'=>'项目优惠券','1'=>'订单优惠券'));
        $type=$this->LgArraySwitch(array('0'=>'减价','1'=>'折扣'));

        foreach ($couponsList as &$couponsOne) {
            $couponsOne['setup_playclass_name']=$class[$couponsOne['setup_playclass']];
            $couponsOne['setup_type_name']=$type[$couponsOne['setup_type']];
            $couponsOne['setup_discount']=$couponsOne['setup_discount']*10;
            if($couponsOne['courseNum']==0){
                $couponsOne['courseNum_name']='全部';
            }else{
                $couponsOne['courseNum_name']=$couponsOne['courseNum'];
            }
            $couponsOne['setup_endday']=$couponsOne['setup_endday']!=''?$couponsOne['setup_endday']:$couponsOne['ticket_endday'];
            $ticket_endday=$couponsOne['ticket_endday'];
        }

        $data = array();
        $count_sql = "select tc.setup_id
                from smc_activity_ticket_setup as tc
                where {$datawhere}";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums?count($db_nums):0;

        $data['allnum'] = $allnum;

        $data['list'] = $couponsList;
        $data['ticket_endday'] = $ticket_endday;

        return $data;
    }

    function createCoupons($request){
        if(!isset($request['ticket_id']) || $request['ticket_id']=='' || $request['ticket_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券活动ID必须传";
            return false;
        }

        if(!isset($request['applytype_id']) || $request['applytype_id']=='' || $request['applytype_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券类型ID必须传";
            return false;
        }

        if(!isset($request['setup_type']) || $request['setup_type']==''){
            $this->error = true;
            $this->errortip = "优惠方式必须传";
            return false;
        }

        $applyOne=$this->DataControl->selectOne("select applytype_playclass from smc_code_couponsapplytype where applytype_id = '{$request['applytype_id']}'");
        if(!$applyOne){
            $this->error = true;
            $this->errortip = "优惠券类型不存在";
            return false;
        }

        $ticketOne=$this->DataControl->getFieldOne("smc_activity_ticket","ticket_endday","ticket_id='{$request['ticket_id']}'");

        if(!$ticketOne){
            $this->error = true;
            $this->errortip = "无该优惠活动";
            return false;
        }

        if($ticketOne['ticket_endday']<$request['setup_endday']){
            $this->error = true;
            $this->errortip = "截止日期必须大于活动开始日期";
            return false;
        }

        $data=array();
        $data['ticket_id']=$request['ticket_id'];
        $data['setup_tagnote']=$request['setup_tagnote'];
        $data['applytype_id']=$request['applytype_id'];
        $data['setup_type']=$request['setup_type'];
        $data['setup_playclass']=$applyOne['applytype_playclass'];
        $data['setup_price']=$request['setup_price'];
        $data['setup_discount']=$request['setup_discount']/10;
        $data['setup_minprice']=$request['setup_minprice'];
        $data['setup_endday']=$request['setup_endday'];
        $data['setup_createtime']=time();
        $setup_id=$this->DataControl->insertData("smc_activity_ticket_setup",$data);

        if($setup_id){

            if(isset($request['course_list']) && $request['course_list']!=''){
                $list=explode(',',$request['course_list']);

                if($list){
                    foreach($list as $one){
                        $data=array();
                        $data['course_id']=$one;
                        $data['setup_id']=$setup_id;
                        $this->DataControl->insertData("smc_activity_ticket_courseapply",$data);
                    }
                }
            }

            if(isset($request['num']) && $request['num']>0){

                for ($x=0; $x<$request['num']; $x++) {
                    do{
                        $card_branch=$this->createRandomBranch();
                    }while($this->DataControl->selectOne("select card_id from smc_activity_ticket_card where card_branch='{$card_branch}'"));

                    $data=array();
                    $data['setup_id']=$setup_id;
                    $data['card_branch']=$card_branch;
                    $data['card_createtime']=time();
                    $this->DataControl->insertData("smc_activity_ticket_card",$data);
                }
            }

            return true;
        }else{
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }


    function increaseCard($request){

        if(!isset($request['setup_id']) || $request['setup_id']=='' || $request['setup_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券ID必须传";
            return false;
        }

        if(!isset($request['num']) || $request['num']==0 || $request['num']==''){
            $this->error = true;
            $this->errortip = "请填写正确数量";
            return false;
        }

        for ($x=0; $x<$request['num']; $x++) {
            do{
                $card_branch=$this->createRandomBranch();
            }while($this->DataControl->selectOne("select card_id from smc_activity_ticket_card where card_branch='{$card_branch}'"));
            $data=array();
            $data['setup_id']=$request['setup_id'];
            $data['card_branch']=$card_branch;
            $data['card_createtime']=time();
            $this->DataControl->insertData("smc_activity_ticket_card",$data);
        }

        return true;
    }

    function delCoupons($request){
        if(!isset($request['setup_id']) || $request['setup_id']=='' || $request['setup_id']=='0'){
            $this->error = true;
            $this->errortip = "优惠券ID必须传";
            return false;
        }

        $sql="select tc.ticket_id
              from smc_activity_ticket_card as t
              left join smc_activity_ticket_setup as tc on tc.setup_id=t.setup_id
              where tc.setup_id='{$request['setup_id']}'";
        if($this->DataControl->selectOne($sql)){
            $this->error = true;
            $this->errortip = "已产生优惠券活动激活码,不可删除";
            return false;
        }

        $this->DataControl->delData("smc_activity_ticket_setup","setup_id='{$request['setup_id']}'");

        return true;

    }

    function getCardList($request){

        $datawhere = " 1 ";

        if (isset($request['ticket_id']) && $request['ticket_id'] !== '') {
            $datawhere .= " and ti.ticket_id='{$request['ticket_id']}'";

            $sql="select t.ticket_cnname from smc_activity_ticket as t where t.ticket_id='{$request['ticket_id']}'";

            $ticketOne=$this->DataControl->selectOne($sql);
        }else{
            if (isset($request['setup_id']) && $request['setup_id'] !== '') {
                $datawhere .= " and tc.setup_id='{$request['setup_id']}'";
            }else{
                $this->error = true;
                $this->errortip = "必须传入优惠券设置ID";
                return false;
            }

            $sql="select t.ticket_cnname from smc_activity_ticket_setup as ts left join smc_activity_ticket as t on t.ticket_id=ts.ticket_id where ts.setup_id='{$request['setup_id']}'";

            $ticketOne=$this->DataControl->selectOne($sql);
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and tc.school_id='{$request['school_id']}'";
        }

        if (isset($request['card_status']) && $request['card_status'] !== '') {
            $datawhere .= " and tc.card_status='{$request['card_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;



        $sql = "select tc.card_id,tc.card_status,tc.card_branch,cl.applytype_cnname,tc.card_bindtime,s.student_cnname,s.student_enname,s.student_branch,st.staffer_cnname,sc.school_cnname,sc.school_branch,t.setup_type,t.setup_playclass,t.setup_price,t.setup_discount,t.setup_minprice,co.coupons_bindingtime,co.coupons_exittime,ti.ticket_startday,ti.ticket_endday
                ,(select count(tc.course_id) from smc_activity_ticket_courseapply as tc where tc.setup_id=t.setup_id) as courseNum
                from smc_activity_ticket_card as tc
                left join smc_activity_ticket_setup as t on t.setup_id=tc.setup_id
                left join smc_activity_ticket as ti on ti.ticket_id=t.ticket_id
                left join smc_code_couponsapplytype as cl on cl.applytype_id=t.applytype_id
                left join smc_student as s on s.student_id=tc.student_id
                left join smc_school as sc on sc.school_id=tc.school_id
                left join smc_staffer as st on st.staffer_id=tc.staffer_id
                left join smc_student_coupons as co on co.coupons_id=tc.coupons_id
                where {$datawhere}
                order by tc.card_id asc";

        $status=$this->LgArraySwitch(array('0'=>'待激活','1'=>'待使用','2'=>'已使用','-1'=>'已失效'));
        $class=$this->LgArraySwitch(array('0'=>'项目优惠券','1'=>'订单优惠券'));
        $type=$this->LgArraySwitch(array('0'=>'减价','1'=>'折扣'));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无券码数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['card_branch'] = $dateexcelvar['card_branch'];
                    $datearray['applytype_cnname'] = $dateexcelvar['applytype_cnname'];
                    $datearray['setup_playclass_name']=$class[$dateexcelvar['setup_playclass']];
                    $datearray['setup_type_name']=$type[$dateexcelvar['setup_type']];
                    $datearray['setup_discount']=($dateexcelvar['setup_discount']*10).'折';
                    $datearray['setup_price']=$dateexcelvar['setup_price'];
                    $datearray['setup_minprice']=$dateexcelvar['setup_minprice'];
                    if($dateexcelvar['courseNum']==0){
                        $datearray['courseNum']='全部';
                    }else{
                        $datearray['courseNum']=$dateexcelvar['courseNum'];
                    }

                    if($dateexcelvar['card_status']==0){
                        $datearray['time']=$dateexcelvar['ticket_startday'].'-'.$dateexcelvar['ticket_endday'];
                    }else{
                        $datearray['time']=date("Y-m-d",$dateexcelvar['coupons_bindingtime']).'-'.date("Y-m-d",$dateexcelvar['coupons_exittime']);
                    }

                    $datearray['staffer_cnname']=$dateexcelvar['staffer_cnname']?$dateexcelvar['staffer_cnname']:'--';
                    $datearray['card_bindtime']=$dateexcelvar['card_bindtime']>0?date("Y-m-d H:i:s",$dateexcelvar['card_bindtime']):'--';
                    $datearray['student_cnname']=$dateexcelvar['student_cnname']?$dateexcelvar['student_cnname']:'--';
                    $datearray['student_enname']=$dateexcelvar['student_enname']?$dateexcelvar['student_enname']:'--';
                    $datearray['student_branch']=$dateexcelvar['student_branch']?$dateexcelvar['student_branch']:'--';
                    $datearray['school_cnname']=$dateexcelvar['school_cnname']?$dateexcelvar['school_cnname']:'--';
                    $datearray['school_branch']=$dateexcelvar['school_branch']?$dateexcelvar['school_branch']:'--';
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("优惠券码", "优惠券类型", "优惠券使用类型","折扣方式","折扣","抵扣金额","最低消费金额","适用课程数量","有效时间", "激活人", "激活时间", "学员中文名", "学员英文名", "学员编号", "校区名称", "校区编号"));
            $excelfileds = array('card_branch', 'applytype_cnname', 'setup_playclass_name','setup_type_name','setup_discount','setup_price','setup_minprice','courseNum','time', 'staffer_cnname', 'card_bindtime', 'student_cnname', 'student_enname', 'student_branch', 'school_cnname', 'school_branch');
            $name=$ticketOne['ticket_cnname'].$status[$request['card_status']]."优惠券码列表.xlsx";
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $cardList = $this->DataControl->selectClear($sql);
            if (!$cardList) {
                $this->error = true;
                $this->errortip = "无相关优惠券码";
                return false;
            }


            foreach ($cardList as &$cardOne) {
                $cardOne['setup_playclass_name']=$class[$cardOne['setup_playclass']];
                $cardOne['setup_type_name']=$type[$cardOne['setup_type']];
                $cardOne['card_bindtime']=$cardOne['card_bindtime']>0?date("Y-m-d H:i:s",$cardOne['card_bindtime']):'--';
                $cardOne['student_cnname']=$cardOne['student_cnname']?$cardOne['student_cnname']:'--';
                $cardOne['student_enname']=$cardOne['student_enname']?$cardOne['student_enname']:'--';
                $cardOne['student_branch']=$cardOne['student_branch']?$cardOne['student_branch']:'--';
                $cardOne['staffer_cnname']=$cardOne['staffer_cnname']?$cardOne['staffer_cnname']:'--';
                $cardOne['school_cnname']=$cardOne['school_cnname']?$cardOne['school_cnname']:'--';
                $cardOne['school_branch']=$cardOne['school_branch']?$cardOne['school_branch']:'--';
                $cardOne['setup_discount']=($cardOne['setup_discount']*10).'折';
                if($cardOne['courseNum']==0){
                    $cardOne['courseNum']='全部';
                }

                if($cardOne['card_status']==0){
                    $cardOne['time']=$cardOne['ticket_startday'].'-'.$cardOne['ticket_endday'];
                }else{
                    $cardOne['time']=date("Y-m-d",$cardOne['coupons_bindingtime']).'-'.date("Y-m-d",$cardOne['coupons_exittime']);
                }
            }

            $data = array();
            $count_sql = "select tc.card_id
                from smc_activity_ticket_card as tc
                left join smc_student as s on s.student_id=tc.student_id
                left join smc_school as sc on sc.school_id=tc.school_id
                where {$datawhere}";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $allnum = $db_nums?count($db_nums):0;

            $data['allnum'] = $allnum;

            $data['list'] = $cardList;

            return $data;
        }
    }

    function getSetupCourse($request){

        if (!isset($request['setup_id']) || $request['setup_id'] == '' || $request['setup_id']==0) {
            $this->error = true;
            $this->errortip = "必须传入优惠券设置ID";
            return false;
        }
        $sql="select sc.course_id,sc.course_cnname,sc.course_branch
              from smc_activity_ticket_courseapply as tc
              inner join smc_course as sc on sc.course_id=tc.course_id
              where tc.setup_id='{$request['setup_id']}'
              ";

        $courseList=$this->DataControl->selectClear($sql);

        if(!$courseList){
            $this->error = true;
            $this->errortip = "已适配全部课程";
            return false;
        }

        return $courseList;
    }













}