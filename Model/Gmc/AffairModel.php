<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class AffairModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //学员出勤类型列表
    function getStuchecktypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.stuchecktype_code like '%{$paramArray['keyword']}%' or a.stuchecktype_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.stuchecktype_id,
                a.stuchecktype_code,
                a.stuchecktype_name,
                a.stuchecktype_mark,
                a.stuchecktype_isfee,
                a.stuchecktype_remk 
            FROM
                smc_code_stuchecktype AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY 
                a.stuchecktype_id DESC
            LIMIT {$pagestart},{$num}";

        $StuchecktypetList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.stuchecktype_id)
            FROM
                smc_code_stuchecktype AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('stuchecktype_code', 'stuchecktype_name', 'stuchecktype_mark ', 'stuchecktype_isfee', 'stuchecktype_remk');
        $fieldname = $this->LgArraySwitch(array('出勤编号', '出勤名称', '出勤标志', '是否消耗费用', '备注'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($StuchecktypetList) {
            $result['list'] = $StuchecktypetList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加出勤类型
    function addStuchecktypeAction($paramArray)
    {
        $data = array();
        $data['stuchecktype_code'] = $paramArray['stuchecktype_code'];
        $data['stuchecktype_name'] = $paramArray['stuchecktype_name'];
        $data['stuchecktype_mark'] = $paramArray['stuchecktype_mark'];
        $data['stuchecktype_isfee'] = $paramArray['stuchecktype_isfee'];
        $data['stuchecktype_remk'] = $paramArray['stuchecktype_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['stuchecktype_code'] = "出勤编号";
        $field['stuchecktype_name'] = "出勤名称";
        $field['stuchecktype_mark'] = "出勤标志";
        $field['stuchecktype_isfee'] = "是否消耗费用";
        $field['stuchecktype_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $stuchecktype_code = $this->DataControl->getFieldOne('smc_code_stuchecktype', 'stuchecktype_id', "stuchecktype_code = '{$paramArray['stuchecktype_code']}'");
        if ($stuchecktype_code) {
            ajax_return(array('error' => 1, 'errortip' => "出勤类型编号已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_stuchecktype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加出勤类型列表成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加出勤类型列表失败', 'result' => $result);
        }
        return $res;
    }

    //编辑出勤类型
    function updateStuchecktypeAction($paramArray)
    {
        $StuchecktypeOne = $this->DataControl->getFieldOne("smc_code_stuchecktype", "stuchecktype_id", "stuchecktype_id = '{$paramArray['stuchecktype_id']}'");
        if ($StuchecktypeOne) {
            $data = array();
            $data['stuchecktype_code'] = $paramArray['stuchecktype_code'];
            $data['stuchecktype_name'] = $paramArray['stuchecktype_name'];
            $data['stuchecktype_mark'] = $paramArray['stuchecktype_mark'];
            $data['stuchecktype_isfee'] = $paramArray['stuchecktype_isfee'];
            $data['stuchecktype_remk'] = $paramArray['stuchecktype_remk'];

            $field = array();
            $field['stuchecktype_code'] = "出勤编号";
            $field['stuchecktype_name'] = "出勤名称";
            $field['stuchecktype_mark'] = "出勤标志";
            $field['stuchecktype_isfee'] = "是否消耗费用";
            $field['stuchecktype_remk'] = "备注";

            $stuchecktype_code = $this->DataControl->getFieldOne('smc_code_stuchecktype', 'stuchecktype_code', "stuchecktype_id = '{$paramArray['stuchecktype_id']}'");
            if ($paramArray['stuchecktype_code'] != $stuchecktype_code['stuchecktype_code']) {
                $stuchecktype_code = $this->DataControl->getFieldOne('smc_code_stuchecktype', 'stuchecktype_id', "stuchecktype_code = '{$paramArray['stuchecktype_code']}'");
                if ($stuchecktype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "员工出勤类型编号已存在!"));
                }

            }

            if ($this->DataControl->updateData("smc_code_stuchecktype", "stuchecktype_id = '{$paramArray['stuchecktype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "出勤类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '出勤类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除出勤类型
    function delStuchecktypeAction($paramArray)
    {
        $AcctOne = $this->DataControl->getFieldOne("smc_code_stuchecktype", "stuchecktype_code", "stuchecktype_id = '{$paramArray['stuchecktype_id']}'");
        if ($AcctOne) {
            $a = $this->DataControl->getFieldOne("smc_student_clockinginlog", "clockinginlog_id", "stuchecktype_code = '{$AcctOne['stuchecktype_code']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该出勤类型已被使用，不可删除"));
            }
            if ($this->DataControl->delData("smc_code_stuchecktype", "stuchecktype_id = '{$paramArray['stuchecktype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除出勤类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除出勤类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //学员异动代码列表
    /*function getStuchangeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.stuchange_code like '%{$paramArray['keyword']}%' or s.stuchange_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.stuchange_id,
                s.stuchange_code,
                s.stuchange_type,
                s.stuchange_name,
                st.stustatus_name
            FROM
                smc_code_stuchange AS s LEFT JOIN smc_code_stustatus as st ON st.stustatus_code = s.stustatus_code
            WHERE
                {$datawhere} and s.company_id = '{$paramArray['company_id']}'
                ORDER BY
                s.stuchange_id DESC
            LIMIT {$pagestart},{$num}";

        $StuchangeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.stuchange_id)
            FROM
                smc_code_stuchange AS s
            WHERE
                {$datawhere} and s.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('stuchange_code', 'stuchange_type', 'stuchange_name', 'stustatus_name');
        $fieldname = array('学员异动编号', '异动类型', '学员异动名称', '状态名称');
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($StuchangeList) {
            $result['list'] = $StuchangeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['stustatus'] = $this->DataControl->selectClear("select stustatus_code,stustatus_name from smc_code_stustatus");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }*/

    //添加学员异动代码
    /*function addStuchangeAction($paramArray)
    {
        $data = array();
        $data['stuchange_code'] = $paramArray['stuchange_code'];
        $data['stuchange_type'] = $paramArray['stuchange_type'];
        $data['stuchange_name'] = $paramArray['stuchange_name'];
        $data['stustatus_code'] = $paramArray['stustatus_code'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['stuchange_code'] = "学员异动编号";
        $field['stuchange_type'] = "异动类型0班级异动1校园异动";
        $field['stuchange_name'] = "学员异动名称";
        $field['stustatus_code'] = "状态编号";
        $field['company_id'] = "所属公司";

        $stuchange_code=$this->DataControl->getFieldOne('smc_code_stuchange', 'stuchange_id', "stuchange_code = '{$paramArray['stuchange_code']}'");
        if($stuchange_code){
            ajax_return(array('error' => 1,'errortip' => "学员异动编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_stuchange', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加学员异动代码成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加学员异动代码失败', 'result' => $result);
        }
        return $res;
    }*/

    //编辑学员异动代码
    /*function updateStuchangeAction($paramArray)
    {
        $StuchangeOne = $this->DataControl->getFieldOne("smc_code_stuchange", "stuchange_id", "stuchange_id = '{$paramArray['stuchange_id']}'");
        if ($StuchangeOne) {
            $data = array();
            $data['stuchange_code'] = $paramArray['stuchange_code'];
            $data['stuchange_type'] = $paramArray['stuchange_type'];
            $data['stuchange_name'] = $paramArray['stuchange_name'];
            $data['stustatus_code'] = $paramArray['stustatus_code'];

            $field = array();
            $field['stuchange_code'] = "学员异动编号";
            $field['stuchange_type'] = "异动类型0班级异动1校园异动";
            $field['stuchange_name'] = "学员异动名称";
            $field['stustatus_code'] = "状态编号";

            $stuchange_code=$this->DataControl->getFieldOne('smc_code_stuchange', 'stuchange_code', "stuchange_id = '{$paramArray['stuchange_id']}'");
            if($paramArray['stuchange_code']!=$stuchange_code['stuchange_code'] ){
                $stuchange_code=$this->DataControl->getFieldOne('smc_code_stuchange', 'stuchange_id', "stuchange_code = '{$paramArray['stuchange_code']}'");
                if($stuchange_code){
                    ajax_return(array('error' => 1,'errortip' => "学员异动编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_stuchange", "stuchange_id = '{$paramArray['stuchange_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "学员异动代码修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '学员异动代码改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }*/

    //删除学员异动代码
    /*function delStuchangeAction($paramArray)
    {
        $StuchangeOne = $this->DataControl->getFieldOne("smc_code_stuchange", "stuchange_code", "stuchange_id = '{$paramArray['stuchange_id']}'");
        if ($StuchangeOne) {
            $a = $this->DataControl->getFieldOne("smc_student_change","change_id","from_stuchange_code = '{$StuchangeOne['stuchange_code']}' or to_stuchange_code = '{$StuchangeOne['stuchange_code']}'");
            if($a){
                ajax_return(array('error' => 1, 'errortip' => "该学员异动已被使用，不可删除"));
            }
            if ($this->DataControl->delData("smc_code_stuchange", "stuchange_id = '{$paramArray['stuchange_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除学员异动代码成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除学员异动代码失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }*/

    //学员异动原因列表
    function getStuchangeReasonList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (r.reason_code like '%{$paramArray['keyword']}%' or r.reason_note like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['stuchange_code']) && $paramArray['stuchange_code'] !== "") {
            $datawhere .= " and r.stuchange_code ='{$paramArray['stuchange_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                r.reason_id,
                r.reason_code,
                r.reason_note,
                s.stuchange_name,
                r.stuchange_code
            FROM
                smc_code_stuchange_reason AS r left join smc_code_stuchange as s on s.stuchange_code = r.stuchange_code
            WHERE
                {$datawhere} and r.company_id = '{$paramArray['company_id']}'
                ORDER BY
                r.reason_id DESC
            LIMIT {$pagestart},{$num}";

        $ReasonList = $this->DataControl->selectClear($sql);

        foreach ($ReasonList as &$val) {
            $val['stuchange_name'] = $this->LgStringSwitch($val['stuchange_name']);
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(r.reason_id)
            FROM
                smc_code_stuchange_reason AS r
            WHERE
                {$datawhere} and r.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('stuchange_name', 'reason_code', 'reason_note');
        $fieldname = $this->LgArraySwitch(array('异动类型', '原因编号', '原因内容'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        $result['stuchange'] = $this->DataControl->selectClear("select stuchange_id,stuchange_code,stuchange_name from smc_code_stuchange");

        foreach ($result['stuchange'] as &$val) {
            $val['stuchange_name'] = $this->LgStringSwitch($val['stuchange_name']);
        }

        if ($ReasonList) {
            $result['list'] = $ReasonList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无学员异动原因", 'result' => $result);
        }

        return $res;
    }

    //添加学员异动原因
    function addStuchangeReasonAction($paramArray)
    {
        $data = array();
        $data['reason_code'] = $paramArray['reason_code'];
        $data['reason_note'] = $paramArray['reason_note'];
        $data['stuchange_code'] = $paramArray['stuchange_code'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['reason_code'] = "原因编号";
        $field['reason_note'] = "原因内容";
        $field['stuchange_code'] = "异动类型编号";
        $field['company_id'] = "所属公司";

        $reason_code = $this->DataControl->getFieldOne('smc_code_stuchange_reason', 'reason_id', "reason_code = '{$paramArray['reason_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($reason_code) {
            ajax_return(array('error' => 1, 'errortip' => "学员异动原因编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_stuchange_reason', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加学员异动原因成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '添加学员异动原因', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加学员异动原因失败', 'result' => $result);
        }
        return $res;
    }

    //编辑学员异动原因
    function updateStuchangeReasonAction($paramArray)
    {
        $StuchangeReasonOne = $this->DataControl->getFieldOne("smc_code_stuchange_reason", "reason_id", "reason_id = '{$paramArray['reason_id']}'");
        if ($StuchangeReasonOne) {
            $data = array();
            $data['reason_code'] = $paramArray['reason_code'];
            $data['reason_note'] = $paramArray['reason_note'];
            $data['stuchange_code'] = $paramArray['stuchange_code'];

            $field = array();
            $field['reason_code'] = "原因编号";
            $field['reason_note'] = "原因内容";
            $field['stuchange_code'] = "异动类型编号";

            $reason_code = $this->DataControl->getFieldOne('smc_code_stuchange_reason', 'reason_code', "reason_id = '{$paramArray['reason_id']}'");
            if ($paramArray['reason_code'] != $reason_code['reason_code']) {
                $reason_code = $this->DataControl->getFieldOne('smc_code_stuchange_reason', 'reason_id', "reason_code = '{$paramArray['reason_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($reason_code) {
                    ajax_return(array('error' => 1, 'errortip' => "学员异动原因编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_stuchange_reason", "reason_id = '{$paramArray['reason_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "学员异动原因修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '编辑学员异动原因', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '学员异动原因修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除学员异动原因
    function delStuchangeReasonAction($paramArray)
    {
        $StuchangeReasonOne = $this->DataControl->getFieldOne("smc_code_stuchange_reason", "reason_code", "reason_id = '{$paramArray['reason_id']}'");
        if ($StuchangeReasonOne) {
            $a = $this->DataControl->getFieldOne("smc_student_change", "change_id", "reason_code = '{$StuchangeReasonOne['reason_code']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该原因已被使用，不可删除"));
            }
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_stuchange_reason", "reason_id = '{$paramArray['reason_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除学员异动原因成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '删除学员异动原因', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除学员异动原因失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //家长职业信息列表
    function getParentscareerList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.parentscareer_code like '%{$paramArray['keyword']}%' or p.parentscareer_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.parentscareer_id,
                p.parentscareer_code,
                p.parentscareer_name
            FROM
                smc_code_parentscareer AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.parentscareer_id DESC    
            LIMIT {$pagestart},{$num}";

        $ParentscareerList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.parentscareer_id)
            FROM
                smc_code_parentscareer AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('parentscareer_code', 'parentscareer_name');
        $fieldname = $this->LgArraySwitch(array('家长职业编号', '家长职业名称'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ParentscareerList) {
            $result['list'] = $ParentscareerList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无家长职业信息", 'result' => $result);
        }

        return $res;
    }

    //添加家长职业信息
    function addParentscareerAction($paramArray)
    {
        $data = array();
        $data['parentscareer_name'] = $paramArray['parentscareer_name'];
        $data['parentscareer_code'] = $paramArray['parentscareer_code'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['parentscareer_name'] = "家长职业名称";
        $field['parentscareer_code'] = "家长职业编号";
        $field['company_id'] = "所属公司";

        $parentscareer_code = $this->DataControl->getFieldOne('smc_code_parentscareer', 'parentscareer_id', "parentscareer_code = '{$paramArray['parentscareer_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($parentscareer_code) {
            ajax_return(array('error' => 1, 'errortip' => "家长职业编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_parentscareer', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加家长职业信息成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '添加家长职业信息', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加家长职业信息失败', 'result' => $result);
        }
        return $res;
    }

    //编辑家长职业信息
    function updateParentscareerAction($paramArray)
    {
        $BankOne = $this->DataControl->getFieldOne("smc_code_parentscareer", "parentscareer_id", "parentscareer_id = '{$paramArray['parentscareer_id']}'");
        if ($BankOne) {
            $data = array();
            $data['parentscareer_name'] = $paramArray['parentscareer_name'];
            $data['parentscareer_code'] = $paramArray['parentscareer_code'];

            $field = array();
            $field['parentscareer_name'] = "家长职业名称";
            $field['parentscareer_code'] = "家长职业编号";

            $parentscareer_code = $this->DataControl->getFieldOne('smc_code_parentscareer', 'parentscareer_code', "parentscareer_id = '{$paramArray['parentscareer_id']}'");
            if ($paramArray['parentscareer_code'] != $parentscareer_code['parentscareer_code']) {
                $parentscareer_code = $this->DataControl->getFieldOne('smc_code_parentscareer', 'parentscareer_id', "parentscareer_code = '{$paramArray['parentscareer_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($parentscareer_code) {
                    ajax_return(array('error' => 1, 'errortip' => "家长职业编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_parentscareer", "parentscareer_id = '{$paramArray['parentscareer_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "家长职业信息修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '编辑家长职业信息', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '家长职业信息修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除家长职业信息
    function delParentscareerAction($paramArray)
    {
        $ParentscareerOne = $this->DataControl->getFieldOne("smc_code_parentscareer", "parentscareer_id", "parentscareer_id = '{$paramArray['parentscareer_id']}'");
        if ($ParentscareerOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_parentscareer", "parentscareer_id = '{$paramArray['parentscareer_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除家长职业信息成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '删除家长职业信息', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除家长职业信息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //收费类型列表
    function getFeeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.acctgroup_name like '%{$paramArray['keyword']}%' or a.acctgroup_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                f.feetype_id,
                f.feetype_name,
                f.feetype_code,
                f.feetype_remk
            FROM
                smc_code_feetype AS f
            WHERE
                {$datawhere}
            ORDER BY
                f.feetype_id DESC        
            LIMIT {$pagestart},{$num}";

        $feetList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(f.feetype_id)
            FROM
                smc_code_feetype AS f
            WHERE
                {$datawhere}");
        $allnums = $all_num[0][0];

        $fieldstring = array('feetype_name', 'feetype_code', 'feetype_remk');
        $fieldname = $this->LgArraySwitch(array('收费类型名称', '收费类型编号', '收费类型备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($feetList) {
            $result['list'] = $feetList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加收费类型
    function addFeeAction($paramArray)
    {
        $data = array();
        $data['feetype_code'] = $paramArray['feetype_code'];
        $data['feetype_name'] = $paramArray['feetype_name'];
        $data['feetype_remk'] = $paramArray['feetype_remk'];

        $field = array();
        $field['feetype_code'] = "收费类型编号";
        $field['feetype_name'] = "收费类型名称";
        $field['feetype_remk'] = "收费类型备注";

        $feetype_code = $this->DataControl->getFieldOne('smc_code_feetype', 'feetype_id', "feetype_code = '{$paramArray['feetype_code']}'");
        if ($feetype_code) {
            ajax_return(array('error' => 1, 'errortip' => "收费类型编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_feetype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加收费类型成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加收费类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑收费类型
    function updateFeeAction($paramArray)
    {
        $BankOne = $this->DataControl->getFieldOne("smc_code_feetype", "feetype_id", "feetype_id = '{$paramArray['feetype_id']}'");
        if ($BankOne) {
            $data = array();
            $data['feetype_name'] = $paramArray['feetype_name'];
            $data['feetype_code'] = $paramArray['feetype_code'];
            $data['feetype_remk'] = $paramArray['feetype_remk'];

            $field = array();
            $field['feetype_name'] = "收费类型名称";
            $field['feetype_code'] = "收费类型编号";
            $field['feetype_remk'] = "收费类型备注";

            $feetype_code = $this->DataControl->getFieldOne('smc_code_feetype', 'feetype_code', "feetype_id = '{$paramArray['feetype_id']}'");
            if ($paramArray['feetype_code'] != $feetype_code['feetype_code']) {
                $feetype_code = $this->DataControl->getFieldOne('smc_code_feetype', 'feetype_id', "feetype_code = '{$paramArray['feetype_code']}'");
                if ($feetype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "收费类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_feetype", "feetype_id = '{$paramArray['feetype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "收费类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '收费类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除收费类型
    function delFeeAction($paramArray)
    {
        $feetypeOne = $this->DataControl->getFieldOne("smc_code_feetype", "feetype_id", "feetype_id = '{$paramArray['feetype_id']}'");
        if ($feetypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_feetype", "feetype_id = '{$paramArray['feetype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除收费类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除收费类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //收费支付方式列表
    function getPaytypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.acctgroup_name like '%{$paramArray['keyword']}%' or a.acctgroup_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.paytype_id,
                p.paytype_name,
                p.paytype_code,
                p.paytype_remk
            FROM
                smc_code_paytype AS p
            WHERE
                {$datawhere}
            ORDER BY
                p.paytype_id DESC
            LIMIT {$pagestart},{$num}";

        $PaytypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.paytype_id)
            FROM
                smc_code_paytype AS p
            WHERE
                {$datawhere}");
        $allnums = $all_num[0][0];

        $fieldstring = array('paytype_name', 'paytype_code', 'paytype_remk');
        $fieldname = $this->LgArraySwitch(array('支付方式名称', '支付方式编号', '支付方式备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PaytypeList) {
            $result['list'] = $PaytypeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加收费支付方式
    function addPaytypeAction($paramArray)
    {
        $data = array();
        $data['paytype_code'] = $paramArray['paytype_code'];
        $data['paytype_name'] = $paramArray['paytype_name'];
        $data['paytype_remk'] = $paramArray['paytype_remk'];

        $field = array();
        $field['paytype_code'] = "收费支付方式编号";
        $field['paytype_name'] = "收费支付方式名称";
        $field['paytype_remk'] = "收费支付方式备注";

        $paytype_code = $this->DataControl->getFieldOne('smc_code_paytype', 'paytype_id', "paytype_code = '{$paramArray['paytype_code']}'");
        if ($paytype_code) {
            ajax_return(array('error' => 1, 'errortip' => "收费支付方式编号已存在!"));
        }


        if ($this->DataControl->insertData('smc_code_paytype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加收费支付方式成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加收费支付方式失败', 'result' => $result);
        }
        return $res;
    }

    //编辑收费支付方式
    function updatePaytypeAction($paramArray)
    {
        $PaytypeOne = $this->DataControl->getFieldOne("smc_code_paytype", "paytype_id", "paytype_id = '{$paramArray['paytype_id']}'");
        if ($PaytypeOne) {
            $data = array();
            $data['paytype_name'] = $paramArray['paytype_name'];
            $data['paytype_code'] = $paramArray['paytype_code'];
            $data['paytype_remk'] = $paramArray['paytype_remk'];

            $field = array();
            $field['paytype_name'] = "收费支付方式名称";
            $field['paytype_code'] = "收费支付方式编号";
            $field['paytype_remk'] = "收费支付方式备注";

            $paytype_code = $this->DataControl->getFieldOne('smc_code_paytype', 'paytype_code', "paytype_id = '{$paramArray['paytype_id']}'");
            if ($paramArray['paytype_code'] != $paytype_code['paytype_code']) {
                $paytype_code = $this->DataControl->getFieldOne('smc_code_paytype', 'paytype_id', "paytype_code = '{$paramArray['paytype_code']}'");
                if ($paytype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "收费支付方式编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_paytype", "paytype_id = '{$paramArray['paytype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "收费支付方式修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '收费支付方式修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除收费支付方式
    function delPaytypeAction($paramArray)
    {
        $PaytypeOne = $this->DataControl->getFieldOne("smc_code_paytype", "paytype_id", "paytype_id = '{$paramArray['paytype_id']}'");
        if ($PaytypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_paytype", "paytype_id = '{$paramArray['paytype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除收费支付方式成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除收费支付方式失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //零用金列表
    function getPettycashList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.pettycash_name like '%{$paramArray['keyword']}%' or a.pettycash_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.pettycash_id,
                p.pettycash_name,
                p.pettycash_code,
                p.pettycash_remk,
                p.acct_code,
                a.acct_name
            FROM
                smc_code_pettycash AS p LEFT JOIN smc_code_acct AS a ON p.acct_code=a.acct_code
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.pettycash_id DESC    
            LIMIT {$pagestart},{$num}";

        $PaytypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.pettycash_id)
            FROM
                smc_code_pettycash AS p
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('pettycash_name', 'pettycash_code', 'pettycash_remk', 'acct_code', 'acct_name');
        $fieldname = array('零用金名称', '零用金编号', '零用金备注', '会计科目代码', '会计科目名称');
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PaytypeList) {
            $result['list'] = $PaytypeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['acct'] = $this->DataControl->selectClear("select acct_code,acct_name from smc_code_acct where company_id = '{$paramArray['company_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加零用金
    function addPettycashAction($paramArray)
    {
        $data = array();
        $data['pettycash_code'] = $paramArray['pettycash_code'];
        $data['pettycash_name'] = $paramArray['pettycash_name'];
        $data['acct_code'] = $paramArray['acct_code'];
        $data['pettycash_remk'] = $paramArray['pettycash_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['pettycash_code'] = "零用金编号";
        $field['pettycash_name'] = "零用金名称";
        $field['acct_code'] = "会计科目代码";
        $field['pettycash_remk'] = "零用金备注";
        $field['company_id'] = "所属公司";

        $pettycash_code = $this->DataControl->getFieldOne('smc_code_pettycash', 'pettycash_id', "pettycash_code = '{$paramArray['pettycash_code']}'");
        if ($pettycash_code) {
            ajax_return(array('error' => 1, 'errortip' => "零用金编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_pettycash', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加零用金成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加零用金失败', 'result' => $result);
        }
        return $res;
    }

    //编辑零用金
    function updatePettycashAction($paramArray)
    {
        $PettycashOne = $this->DataControl->getFieldOne("smc_code_pettycash", "pettycash_id", "pettycash_id = '{$paramArray['pettycash_id']}'");
        if ($PettycashOne) {
            $data = array();
            $data['pettycash_code'] = $paramArray['pettycash_code'];
            $data['pettycash_name'] = $paramArray['pettycash_name'];
            $data['acct_code'] = $paramArray['acct_code'];
            $data['pettycash_remk'] = $paramArray['pettycash_remk'];

            $field = array();
            $field['pettycash_code'] = "零用金编号";
            $field['pettycash_name'] = "零用金名称";
            $field['acct_code'] = "会计科目代码";
            $field['pettycash_remk'] = "零用金备注";

            $pettycash_code = $this->DataControl->getFieldOne('smc_code_pettycash', 'pettycash_code', "pettycash_id = '{$paramArray['pettycash_id']}'");
            if ($paramArray['pettycash_code'] != $pettycash_code['pettycash_code']) {
                $pettycash_code = $this->DataControl->getFieldOne('smc_code_pettycash', 'pettycash_id', "pettycash_code = '{$paramArray['pettycash_code']}'");
                if ($pettycash_code) {
                    ajax_return(array('error' => 1, 'errortip' => "零用金编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_pettycash", "pettycash_id = '{$paramArray['pettycash_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "零用金修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '零用金修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除零用金
    function delPettycashAction($paramArray)
    {
        $PettycashOne = $this->DataControl->getFieldOne("smc_code_pettycash", "pettycash_id", "pettycash_id = '{$paramArray['pettycash_id']}'");
        if ($PettycashOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_pettycash", "pettycash_id = '{$paramArray['pettycash_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除零用金成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除零用金失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //支出类型列表
    function getSpendingList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.spending_name like '%{$paramArray['keyword']}%' or s.spending_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.spending_id,
                s.spending_name,
                s.spending_code,
                s.spending_remk,
                s.acct_code,
                a.acct_name
            FROM
                smc_code_spending AS s LEFT JOIN smc_code_acct AS a ON s.acct_code = a.acct_code
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}'
            ORDER BY
                s.spending_id DESC
            LIMIT {$pagestart},{$num}";

        $spendingList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.spending_id)
            FROM
                smc_code_spending AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('spending_name', 'spending_code', 'spending_remk', 'acct_code', 'acct_name');
        $fieldname = array('零用金名称', '零用金编号', '零用金备注', '会计科目代码', '会计科目名称');
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($spendingList) {
            $result['list'] = $spendingList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['acct'] = $this->DataControl->selectClear("select acct_code,acct_name from smc_code_acct where company_id = '{$paramArray['company_id']}'");

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加支付类型
    function addSpendingAction($paramArray)
    {
        $data = array();
        $data['spending_code'] = $paramArray['spending_code'];
        $data['spending_name'] = $paramArray['spending_name'];
        $data['acct_code'] = $paramArray['acct_code'];
        $data['spending_remk'] = $paramArray['spending_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['spending_code'] = "支出类型编号";
        $field['spending_name'] = "支出类型名称";
        $field['acct_code'] = "会计科目代码";
        $field['spending_remk'] = "支付明细备注";
        $field['company_id'] = "所属公司";

        $spending_code = $this->DataControl->getFieldOne('smc_code_spending', 'spending_id', "spending_code = '{$paramArray['spending_code']}'");
        if ($spending_code) {
            ajax_return(array('error' => 1, 'errortip' => "支出类型编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_spending', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加支付类型成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加支付类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑支出类型
    function updateSpendingAction($paramArray)
    {
        $spendingOne = $this->DataControl->getFieldOne("smc_code_spending", "spending_id", "spending_id = '{$paramArray['spending_id']}'");
        if ($spendingOne) {
            $data = array();
            $data['spending_code'] = $paramArray['spending_code'];
            $data['spending_name'] = $paramArray['spending_name'];
            $data['acct_code'] = $paramArray['acct_code'];
            $data['spending_remk'] = $paramArray['spending_remk'];

            $field = array();
            $field['spending_code'] = "支出类型编号";
            $field['spending_name'] = "支出类型名称";
            $field['acct_code'] = "会计科目代码";
            $field['spending_remk'] = "支付明细备注";

            $spending_code = $this->DataControl->getFieldOne('smc_code_spending', 'spending_code', "spending_id = '{$paramArray['spending_id']}'");
            if ($paramArray['spending_code'] != $spending_code['spending_code']) {
                $spending_code = $this->DataControl->getFieldOne('smc_code_spending', 'spending_id', "spending_code = '{$paramArray['spending_code']}'");
                if ($spending_code) {
                    ajax_return(array('error' => 1, 'errortip' => "支出类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_spending", "spending_id = '{$paramArray['spending_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "支出类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '支出类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除支出类型
    function delSpendingAction($paramArray)
    {
        $SpendingOne = $this->DataControl->getFieldOne("smc_code_spending", "spending_id", "spending_id = '{$paramArray['spending_id']}'");
        if ($SpendingOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_spending", "spending_id = '{$paramArray['spending_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除支出类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除支出类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //节假日管理列表
    function getHolidaysList($paramArray)
    {

        $datawhere = " 1 ";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.holidays_name like '%{$paramArray['keyword']}%' or s.holidays_day like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['day']) && $paramArray['day'] !== "") {
            $datawhere .= " and s.holidays_day like '%{$paramArray['day']}%'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.holidays_id,
                s.holidays_name,
                s.holidays_day,
                s.holidays_status,
                s.holidays_status as holidays_status_note,
                s.holidays_note
            FROM
                smc_code_holidays AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}' and s.school_id = '0'
            ORDER BY
                s.holidays_id DESC
            LIMIT {$pagestart},{$num}";

        $spendingList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "放假", "1" => "上课"));
        if ($spendingList) {
            foreach ($spendingList as &$val) {
                $val['holidays_status_note'] = $status[$val['holidays_status_note']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.holidays_id)
            FROM
                smc_code_holidays AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}' and s.school_id = '0'");
        $allnums = $all_num[0][0];

        $fieldstring = array('holidays_day', 'holidays_name', 'holidays_status_note', 'holidays_note', 'holidays_status');
        $fieldname = $this->LgArraySwitch(array('日期', '假日名称', '假日类型', '备注', '假日类型'));
        $fieldcustom = array("1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($spendingList) {
            $result['list'] = $spendingList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无节假日信息", 'result' => $result);
        }

        return $res;
    }


    //新增节假日
    function addHolidaysAction($paramArray)
    {
        $data = array();
        $data['holidays_name'] = $paramArray['holidays_name'];
        $data['holidays_day'] = $paramArray['holidays_day'];
        $data['holidays_status'] = $paramArray['holidays_status'];
        $data['holidays_note'] = $paramArray['holidays_note'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['holidays_name'] = "假日名称";
        $field['holidays_day'] = "假日日期";
        $field['holidays_status'] = "假日类型";
        $field['holidays_note'] = "备注";
        $field['company_id'] = "所属公司";

        $holidays_day = $this->DataControl->getFieldOne('smc_code_holidays', 'holidays_id', "holidays_day = '{$paramArray['holidays_day']}' and company_id = '{$paramArray['company_id']}' and school_id = '0'");
        if ($holidays_day) {
            ajax_return(array('error' => 1, 'errortip' => "假日日期已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_holidays', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加假日日期成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '新增节假日', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加假日日期失败', 'result' => $result);
        }
        return $res;
    }

    //编辑节假日
    function updateHolidaysAction($paramArray)
    {
        $holidaysOne = $this->DataControl->getFieldOne("smc_code_holidays", "holidays_id", "holidays_id = '{$paramArray['holidays_id']}'");
        if ($holidaysOne) {
            $data = array();
            $data['holidays_name'] = $paramArray['holidays_name'];
            $data['holidays_day'] = $paramArray['holidays_day'];
            $data['holidays_status'] = $paramArray['holidays_status'];
            $data['holidays_note'] = $paramArray['holidays_note'];

            $field = array();
            $field['holidays_name'] = "假日名称";
            $field['holidays_day'] = "假日日期";
            $field['holidays_status'] = "假日类型";
            $field['holidays_note'] = "备注";

            $holidays_day = $this->DataControl->getFieldOne('smc_code_holidays', 'holidays_day', "holidays_id = '{$paramArray['holidays_id']}'");
            if ($paramArray['holidays_day'] != $holidays_day['holidays_day']) {
                $holidays_day = $this->DataControl->getFieldOne('smc_code_holidays', 'holidays_id', "holidays_day = '{$paramArray['holidays_day']}' and company_id = '{$paramArray['company_id']}'");
                if ($holidays_day) {
                    ajax_return(array('error' => 1, 'errortip' => "假日日期已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_holidays", "holidays_id = '{$paramArray['holidays_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "假日日期修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '编辑节假日', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '假日日期修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除节假日
    function delHolidaysAction($paramArray)
    {
        $holidaysOne = $this->DataControl->getFieldOne("smc_code_holidays", "holidays_id", "holidays_id = '{$paramArray['holidays_id']}'");
        if ($holidaysOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_holidays", "holidays_id = '{$paramArray['holidays_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除节假日成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->班务相关设置", '删除节假日', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除节假日失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getMonthList($paramArray)
    {
        $date = getthemonth($paramArray['day']);

        $sql = "select holidays_day,holidays_id,holidays_status,holidays_name,holidays_note from smc_code_holidays where company_id = '{$paramArray['company_id']}' and school_id = '0' and holidays_day between '{$date[0]}' and '{$date[1]}' group by holidays_day";

        $mothListArray = $this->DataControl->selectClear($sql);
        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['holidays_day']));
                $v['month'] = date('m', strtotime($v['holidays_day']));
                $v['day'] = date('d', strtotime($v['holidays_day']));
                unset($mothListArray[$k]['holidays_day']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));

//        var_dump($mothListArray);die();
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = $i;
                    $data['holidays_status'] = strval(-1);
                    array_push($mothListArray, $data);
                }
            }
            usort($mothListArray, function ($a, $b) {
                if ($a['day'] == $b['day']) return 0;
                return $a['day'] > $b['day'] ? 1 : -1;
            });
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = $i;
                $data['holidays_status'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $mothListArray));
    }


    //教室列表
    function getClassroomList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.classroom_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['classroom_status']) && $paramArray['classroom_status'] !== "") {
            $datawhere .= " and c.classroom_status ='{$paramArray['classroom_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.gclassroom_id,
                c.classroom_cnname,
                c.classroom_remark,
                c.classroom_status
            FROM
                gmc_code_classroom AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'
                ORDER BY
                c.gclassroom_id DESC
            LIMIT {$pagestart},{$num}";

        $ClassroomList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.gclassroom_id)
            FROM
                gmc_code_classroom AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('classroom_cnname', 'classroom_status', 'classroom_remark');
        $fieldname = $this->LgArraySwitch(array('教室名称', '是否启用', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");
        $fieldswitch = array("0", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ClassroomList) {
            $result['list'] = $ClassroomList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无教室列表", 'result' => $result);
        }

        return $res;
    }


    //添加教室
    function addClassroomAction($paramArray)
    {
        $data = array();
        $data['classroom_cnname'] = $paramArray['classroom_cnname'];
        $data['classroom_remark'] = $paramArray['classroom_remark'];
        $data['company_id'] = $paramArray['company_id'];
        $data['classroom_createtime'] = time();

        $field = array();
        $field['classroom_cnname'] = "教室名称";
        $field['classroom_remark'] = "备注";
        $field['company_id'] = "所属公司";

        $classroom_cnname = $this->DataControl->getFieldOne('gmc_code_classroom', 'gclassroom_id', "classroom_cnname = '{$paramArray['classroom_cnname']}' and company_id = '{$paramArray['company_id']}'");
        if ($classroom_cnname) {
            ajax_return(array('error' => 1, 'errortip' => "教室名称已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('gmc_code_classroom', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加教室成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加教室失败', 'result' => $result);
        }
        return $res;
    }

    //编辑教室
    function updateClassroomAction($paramArray)
    {
        $ClassroomOne = $this->DataControl->getFieldOne("gmc_code_classroom", "gclassroom_id", "gclassroom_id = '{$paramArray['gclassroom_id']}'");
        if ($ClassroomOne) {
            $data = array();
            $data['classroom_cnname'] = $paramArray['classroom_cnname'];
            $data['classroom_remark'] = $paramArray['classroom_remark'];
            $data['company_id'] = $paramArray['company_id'];
            $data['classroom_updatatime'] = time();

            $field = array();
            $field['classroom_cnname'] = "教室名称";
            $field['classroom_remark'] = "备注";
            $field['company_id'] = "所属公司";

//            $classroom_cnname=$this->DataControl->getFieldOne('gmc_code_classroom', 'classroom_cnname', "gclassroom_id = '{$paramArray['gclassroom_id']}'");
//            if($paramArray['classroom_cnname']!=$classroom_cnname['classroom_cnname'] ){
//                $classroom_cnname=$this->DataControl->getFieldOne('gmc_code_classroom', 'classroom_id', "classroom_cnname = '{$paramArray['classroom_cnname']}'");
//                if($classroom_cnname){
//                    ajax_return(array('error' => 1,'errortip' => "教室名称已存在!"));
//                }
//            }

            if ($this->DataControl->updateData("gmc_code_classroom", "gclassroom_id = '{$paramArray['gclassroom_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑教室成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑教室失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除教室
    function delClassroomAction($paramArray)
    {
        $ClassroomOne = $this->DataControl->getFieldOne("gmc_code_classroom", "gclassroom_id", "gclassroom_id = '{$paramArray['gclassroom_id']}'");
        if ($ClassroomOne) {
            $a = $this->DataControl->getFieldOne("smc_classroom", "gclassroom_id,classroom_id", "gclassroom_id = '{$ClassroomOne['gclassroom_id']}' and company_id = '{$paramArray['company_id']}'");

            $b = $this->DataControl->getFieldOne("smc_class_hour", "classroom_id", "classroom_id = '{$a['classroom_id']}'");

            if ($b && $b['classroom_id'] != '0') {
                ajax_return(array('error' => 1, 'errortip' => "该教室已被使用，不可删除"));
            }
            if ($this->DataControl->delData("gmc_code_classroom", "gclassroom_id = '{$paramArray['gclassroom_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除教室成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除教室失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //改变教室启用状态
    function updateClassroomStatusAction($paramArray)
    {
        $ClassroomOne = $this->DataControl->getFieldOne("gmc_code_classroom", "gclassroom_id", "gclassroom_id = '{$paramArray['gclassroom_id']}'");
        if ($ClassroomOne) {
            $data = array();
            if ($paramArray['classroom_status'] == '1') {
                $data['classroom_status'] = '0';
            } else {
                $data['classroom_status'] = '1';
            }

            $field = array();
            $field['classroom_status'] = "教室启用状态";

            if ($this->DataControl->updateData("gmc_code_classroom", "gclassroom_id = '{$paramArray['gclassroom_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变教室启用状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变教室启用状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //根据手机号获取集团职工信息
    function getGmcStafferInfo($paramArray)
    {
        $sql = "
            SELECT 
                s.staffer_id,b.postbe_id,b.postrole_id,r.postrole_dataequity
            FROM
                smc_staffer AS s 
                left join gmc_staffer_postbe as b on s.staffer_id = b.staffer_id
                left join gmc_company_postrole as r on b.postrole_id = r.postrole_id
            WHERE
                s.staffer_mobile = '{$paramArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' and b.school_id = '0' and b.postbe_ismianjob = '1'";
        $stafferInfo = $this->DataControl->selectOne($sql);

        $result = array();
        if ($stafferInfo) {
            $result["data"] = $stafferInfo;
            $res = array('error' => '0', 'errortip' => '获取集团职工信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取集团职工信息失败', 'result' => $result);
        }
        return $res;
    }

    function getSubtypeList($request)
    {

        $datawhere = " 1 ";


        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (subtype_name like '%{$request['keyword']}%' )";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select subtype_id,subtype_name 
                from smc_code_subtype 
                where {$datawhere} and company_id='{$this->company_id}'
                limit {$pagestart},{$num}
              ";
        $typeList = $this->DataControl->selectClear($sql);

        if (!$typeList) {
            $this->error = true;
            $this->errortip = "无认缴类别数据";
            return false;
        }

        $data = array();
        $count_sql = "select subtype_id,subtype_name 
                      from smc_code_subtype 
                      where {$datawhere} and company_id='{$this->company_id}'";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums ? count($db_nums) : 0;

        $data['allnum'] = $allnum;

        $data['list'] = $typeList;
        return $data;
    }


    function addSubtype($request)
    {

        $list = json_decode(stripslashes($request['list']), 1);

        if (!$list) {
            $this->error = true;
            $this->errortip = "请确实是否添加数据";
            return false;
        }

        $num = 0;
        foreach ($list as $one) {

            if ($this->DataControl->getFieldOne("smc_code_subtype", "subtype_id", "company_id='{$this->company_id}' and subtype_name='{$one['subtype_name']}'")) {
                $num++;
                continue;
            }

            $data = array();
            $data['company_id'] = $this->company_id;
            $data['subtype_name'] = $one['subtype_name'];

            if (!$this->DataControl->insertData("smc_code_subtype", $data)) {
                $num++;
            }
        }

        if ($num > 0) {
            $this->error = true;
            $this->errortip = $num . "条数据添加失败请确认之后再次添加";
            return false;
        }

        return true;

    }

    function editSubtype($request)
    {

        if ($this->DataControl->getFieldOne("smc_school_income", "income_id", "subtype_id='{$request['subtype_id']}'")) {
            $this->error = true;
            $this->errortip = "该认缴类别已被使用,不可编辑";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_code_subtype", "subtype_id", "company_id='{$this->company_id}' and subtype_name='{$request['subtype_name']}' and subtype_id<>'{$request['subtype_id']}'")) {

            $this->error = true;
            $this->errortip = "该认缴类别已存在";
            return false;
        }

        $data = array();
        $data['subtype_name'] = $request['subtype_name'];

        if ($this->DataControl->updateData("smc_code_subtype", "subtype_id='{$request['subtype_id']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "编辑失败";
            return false;
        }

    }

    function delSubtype($request)
    {
        if ($this->DataControl->getFieldOne("smc_school_income", "income_id", "subtype_id='{$request['subtype_id']}'")) {
            $this->error = true;
            $this->errortip = "该认缴类别已被使用,不可删除";
            return false;
        }

        if ($this->DataControl->delData("smc_code_subtype", "subtype_id='{$request['subtype_id']}'")) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "删除失败";
            return false;
        }
    }

    function getTagsTypeList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (tagstype_name like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tagstype_name,count(tags_name) as tags_name_count,sum(tags_use) as tagstype_use
                from smc_code_tags 
                where {$datawhere} 
                and company_id='{$request['company_id']}'
                group by tagstype_name
                order by tagstype_name
                limit {$pagestart},{$num} ";
        $tagstypeList = $this->DataControl->selectClear($sql);

        if (!$tagstypeList) {
            $this->error = true;
            $this->errortip = "无标签类型数据";
            return false;
        }

        $data = array();
        $count_sql = "select tagstype_name
                        from smc_code_tags 
                        where {$datawhere} 
                        and company_id='{$request['company_id']}'
                        group by tagstype_name";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums ? count($db_nums) : 0;

        $data['allnum'] = $allnum;

        $data['list'] = $tagstypeList;
        return $data;
    }

    function addTags($request)
    {
        $list = json_decode(stripslashes($request['list']), 1);

        if (!$list) {
            $this->error = true;
            $this->errortip = "请填写要添加的数据";
            return false;
        }
        if (!isset($request['tagstype_name']) || $request['tagstype_name'] == '') {
            $this->error = true;
            $this->errortip = "请填写要添加的标签类型";
            return false;
        }

        $errortip = "";
        $num = 0;
        foreach ($list as $one) {
            if ($one['tags_name'] == '') {
                $num++;
                continue;
            }

            if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "company_id='{$request['company_id']}' and tags_name='{$one['tags_name']}'")) {
                $num++;
                $errortip .= "已存在标签<'{$one['tags_name']}'>\r\n";
                continue;
            }

            $data = array();
            $data['company_id'] = $request['company_id'];
            $data['tagstype_name'] = $request['tagstype_name'];
            $data['tags_name'] = $one['tags_name'];
            $data['tags_use'] = 0;

            if (!$this->DataControl->insertData("smc_code_tags", $data)) {
                $num++;
            }
        }

        if ($num > 0) {
            $this->error = true;
            $this->errortip = $errortip . "等" . $num . "条数据添加失败,请修改之后再添加";
            return false;
        }
        return true;
    }

    function editTags($request)
    {
        if (!isset($request['tagstype_name']) || $request['tagstype_name'] == '') {
            $this->error = 1;
            $this->errortip = "请填写要编辑的标签类型";
            return false;
        }

        $list = json_decode(stripslashes($request['list']), 1);

        if (!$list) {
            $this->error = 1;
            $this->errortip = "请填写要编辑的数据";
            return false;
        }

        $errortip = "";

        $num = 0;
        $suc = 0;
        foreach ($list as $one) {
            if ($one['tags_name'] == '') {
                $num++;
                continue;
            }

            if (!isset($one['tags_id']) || $one['tags_id'] == '' || $one['tags_id'] == '0') {
                if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "company_id='{$request['company_id']}' and tags_name='{$one['tags_name']}'")) {
                    $num++;
                    $errortip .= "已存在标签<'{$one['tags_name']}'>\r\n";
                    continue;
                }

                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['tagstype_name'] = $request['tagstype_name'];
                $data['tags_name'] = $one['tags_name'];
                $data['tags_use'] = 0;

                if (!$this->DataControl->insertData("smc_code_tags", $data)) {
                    $num++;
                    $errortip .= "标签<'{$one['tags_name']}'>添加失败.\r\n";
                }
            } elseif ($one['tags_del'] == '1') {
                if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "tags_id='{$one['tags_id']}' and tags_use>0 ")) {
                    $num++;
                    $errortip .= "标签<''{$one['tags_name']}>已被使用,不可删除.\r\n";
                }

                if (!$this->DataControl->delData("smc_code_tags", "tags_id='{$one['tags_id']}'")) {
                    $num++;
                    $errortip .= "标签<''{$one['tags_name']}>删除失败.\r\n";
                }
            } else {
                if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "tags_id<>'{$one['tags_id']}' and company_id='{$request['company_id']}' and tags_name='{$one['tags_name']}'")) {
                    $num++;
                    $errortip .= "已存在标签<'{$one['tags_name']}'>。\r\n";
                    continue;
                }

                if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "tags_id='{$one['tags_id']}' and company_id='{$request['company_id']}' and tags_use>0")) {
                    $num++;
                    $errortip .= "标签<'{$one['tags_name']}'>已使用，不可修改。\r\n";
                    continue;
                }

                $data = array();
                $data['company_id'] = $request['company_id'];
                $data['tagstype_name'] = $request['tagstype_name'];
                $data['tags_name'] = $one['tags_name'];
                $data['tags_use'] = 0;

                if ($this->DataControl->updateData("smc_code_tags", "tags_id='{$one['tags_id']}'", $data)) {
                    $errortip .= "标签<'{$one['tags_name']}'>编辑成功。\r\n";
                    $suc++;
                } else {
                    $errortip .= "标签<'{$one['tags_name']}'>编辑失败。\r\n";
                }
            }
        }

        if ($suc > 0) {
            return true;
        } else {
            $this->error = 1;
            $this->errortip = $num . "条数据编辑失败,请确认之后再修改.\r\n" . $errortip;
            return false;
        }
    }

    function delTags($request)
    {
        if (!isset($request['tags_id']) || !$request['tags_id'] > 0) {
            $this->error = 1;
            $this->errortip = "没有找到要删除的标签";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "tags_id='{$request['tags_id']}' and tags_use>0 ")) {
            $this->error = 1;
            $this->errortip = "该标签已被使用,不可删除";
            return false;
        }

        if ($this->DataControl->delData("smc_code_tags", "tags_id='{$request['tags_id']}'")) {
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "删除失败";
            return false;
        }
    }

    function delTagsType($request)
    {
        if ($this->DataControl->getFieldOne("smc_code_tags", "tags_id", "company_id='{$request['company_id']}' and tagstype_name='{$request['tagstype_name']}' and tags_use>0")) {
            $this->error = true;
            $this->errortip = "标签类型已被使用,不可删除";
            return false;
        }

        if ($this->DataControl->delData("smc_code_tags", "tagstype_name='{$request['tagstype_name']}'")) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "删除失败";
            return false;
        }
    }

    function getTagsByType($request)
    {
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tags_id,tags_name,tags_use
                from smc_code_tags 
                where company_id='{$request['company_id']}'
                and tagstype_name='{$request['tagstype_name']}'
                order by tags_name
                limit {$pagestart},{$num} ";
        $tagstypeList = $this->DataControl->selectClear($sql);

        if (!$tagstypeList) {
            $this->error = true;
            $this->errortip = "无标签类型数据";
            return false;
        }

        $data = array();
        $count_sql = "select tagstype_name
                        from smc_code_tags 
                        where company_id='{$request['company_id']}'
                        and tagstype_name='{$request['tagstype_name']}'
                        group by tagstype_name";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums ? count($db_nums) : 0;

        $data['allnum'] = $allnum;

        $data['list'] = $tagstypeList;
        return $data;
    }

    function getTrackTypeList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (tracktype_name like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tracktype_id,tracktype_name,tracktype_remk,tracktype_use,tracktype_smc,tracktype_eas
                from smc_code_tracktype 
                where {$datawhere} 
                and company_id='{$request['company_id']}'
                order by tracktype_id desc 
                limit {$pagestart},{$num} ";
        $tracktypeList = $this->DataControl->selectClear($sql);

        $tracktype_smc = $this->LgArraySwitch(array("0" => "", "1" => "校务"));
        $tracktype_eas = $this->LgArraySwitch(array("0" => "", "1" => "教务"));

        if (!$tracktypeList) {
            $this->error = true;
            $this->errortip = "无沟通类型数据";
            return false;
        }

        foreach ($tracktypeList as &$val) {
            $val['tracktype_module'] = ($tracktype_smc[$val['tracktype_smc']] == '' || $tracktype_eas[$val['tracktype_eas']] == '') ? ($tracktype_smc[$val['tracktype_smc']] . $tracktype_eas[$val['tracktype_eas']]) : ($tracktype_smc[$val['tracktype_smc']] . ',' . $tracktype_eas[$val['tracktype_eas']]);
        }

        $data = array();
        $count_sql = "select tracktype_id
                        from smc_code_tracktype 
                        where {$datawhere} 
                        and company_id='{$request['company_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums ? count($db_nums) : 0;

        $data['allnum'] = $allnum;

        $data['list'] = $tracktypeList;
        return $data;
    }

    function addTrackType($request)
    {
        if (!isset($request['tracktype_name']) || $request['tracktype_name'] == '') {
            $this->error = true;
            $this->errortip = "请填写要添加的沟通类型名称";
            return false;
        }
        if ($this->DataControl->getFieldOne("smc_code_tracktype", "tracktype_id", "company_id='{$request['company_id']}' and tracktype_name='{$request['tracktype_name']}'")) {
            $this->error = true;
            $this->errortip = "已存在此沟通类型,无需重复添加";
            return false;
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['tracktype_name'] = $request['tracktype_name'];
        $data['tracktype_remk'] = $request['tracktype_remk'];
        $data['tracktype_use'] = 0;
        $data['tracktype_smc'] = $request['tracktype_smc'];
        $data['tracktype_eas'] = $request['tracktype_eas'];

        if (!$this->DataControl->insertData("smc_code_tracktype", $data)) {
            $this->error = true;
            $this->errortip = "添加失败,请修改之后再添加";
            return false;
        }
        return true;
    }

    function editTrackType($request)
    {
        if (!isset($request['tracktype_name']) || $request['tracktype_name'] == '') {
            $this->error = true;
            $this->errortip = "请填写要编辑的沟通类型名称";
            return false;
        }

        if (!isset($request['tracktype_id']) || $request['tracktype_id'] == '' || $request['tracktype_id'] == '0') {
            $this->error = true;
            $this->errortip = "获取沟通类型信息失败";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_code_tracktype", "tracktype_id", "tracktype_id<>'{$request['tracktype_id']}' and company_id='{$request['company_id']}' and tracktype_name='{$request['tracktype_name']}'")) {
            $this->error = true;
            $this->errortip = "已存在此沟通类型,请修改后再试";
            return false;
        }

        $remk_only = false;
        if ($this->DataControl->getFieldOne("smc_code_tracktype", "tracktype_id", "tracktype_id='{$request['tracktype_id']}' and company_id='{$request['company_id']}' and tracktype_use>0")) {
            $remk_only = true;
        }

        $data = array();
        if (!$remk_only) {
            $data['tracktype_name'] = $request['tracktype_name'];
        }
        $data['tracktype_remk'] = $request['tracktype_remk'];
        $data['tracktype_smc'] = $request['tracktype_smc'];
        $data['tracktype_eas'] = $request['tracktype_eas'];

        if (!$this->DataControl->updateData("smc_code_tracktype", "tracktype_id='{$request['tracktype_id']}'", $data)) {
            $this->error = true;
            $this->errortip = "编辑失败";
            return false;
        } else {
            if ($remk_only) {
                $this->errortip = "沟通类型已使用，仅修改备注信息";
            } else {
                $this->errortip = "编辑成功";
            }
            return true;
        }
    }

    function delTrackType($request)
    {
        if ($this->DataControl->getFieldOne("smc_code_tracktype", "tracktype_id", "company_id='{$request['company_id']}' and tracktype_id='{$request['tracktype_id']}' and tracktype_use>0")) {
            $this->error = true;
            $this->errortip = "沟通类型已使用,不可删除";
            return false;
        }

        if ($this->DataControl->delData("smc_code_tracktype", "company_id='{$request['company_id']}' and tracktype_id='{$request['tracktype_id']}'")) {
            $this->DataControl->delData("smc_code_trackresult", "company_id='{$request['company_id']}' and tracktype_id='{$request['tracktype_id']}'");
            return true;
        } else {
            $this->error = true;
            $this->errortip = "删除失败";
            return false;
        }
    }

    function getTrackResultList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.trackresult_name like '%{$request['keyword']}%')";
        }

        if (isset($request['tracktype_id']) && $request['tracktype_id'] !== '') {
            $datawhere .= " and a.tracktype_id='{$request['tracktype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.trackresult_id,a.trackresult_name,a.tracktype_id,b.tracktype_name,a.trackresult_remk,a.trackresult_use
                from smc_code_trackresult a
                left join smc_code_tracktype b on a.tracktype_id=b.tracktype_id
                where {$datawhere} 
                and a.company_id='{$request['company_id']}'
                order by a.trackresult_name
                limit {$pagestart},{$num} ";
        $trackresultList = $this->DataControl->selectClear($sql);

        if (!$trackresultList) {
            $this->error = true;
            $this->errortip = "无沟通类型数据";
            return false;
        }

        $data = array();
        $count_sql = "select a.trackresult_id
                from smc_code_trackresult a
                left join smc_code_tracktype b on a.tracktype_id=b.tracktype_id
                where {$datawhere} 
                and a.company_id='{$request['company_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums ? count($db_nums) : 0;

        $data['allnum'] = $allnum;

        $data['list'] = $trackresultList;
        return $data;
    }

    function addTrackResult($request)
    {
        if (!isset($request['tracktype_id']) || $request['tracktype_id'] == '' || $request['tracktype_id'] == '0') {
            $this->error = true;
            $this->errortip = "请选择沟通类型";
            return false;
        }
        if (!isset($request['trackresult_name']) || $request['trackresult_name'] == '') {
            $this->error = true;
            $this->errortip = "请填写要添加的沟通结果名称";
            return false;
        }
        if ($this->DataControl->getFieldOne("smc_code_trackresult", "trackresult_id", "company_id='{$request['company_id']}' 
                                            and trackresult_name='{$request['trackresult_name']}' and tracktype_id='{$request['tracktype_id']}'")) {
            $this->error = true;
            $this->errortip = "已存在此沟通类型,无需重复添加";
            return false;
        }

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['tracktype_id'] = $request['tracktype_id'];
        $data['trackresult_name'] = $request['trackresult_name'];
        $data['trackresult_remk'] = $request['trackresult_remk'];
        $data['trackresult_use'] = 0;

        if (!$this->DataControl->insertData("smc_code_trackresult", $data)) {
            $this->error = true;
            $this->errortip = "添加失败,请修改之后再添加";
            return false;
        }
        return true;
    }

    function editTrackResult($request)
    {
        if (!isset($request['trackresult_name']) || $request['trackresult_name'] == '') {
            $this->error = true;
            $this->errortip = "请填写要编辑的沟通结果名称";
            return false;
        }

        if (!isset($request['tracktype_id']) || $request['tracktype_id'] == '' || $request['tracktype_id'] == '0') {
            $this->error = true;
            $this->errortip = "请选择沟通类型";
            return false;
        }

        if (!isset($request['trackresult_id']) || $request['trackresult_id'] == '') {
            $this->error = true;
            $this->errortip = "获取沟通结果信息失败";
            return false;
        }

        if ($this->DataControl->getFieldOne("smc_code_trackresult", "trackresult_id", "trackresult_id<>'{$request['trackresult_id']}' and company_id='{$request['company_id']}' and trackresult_name='{$request['trackresult_name']}' and tracktype_id='{$request['tracktype_id']}'")) {
            $this->error = true;
            $this->errortip = "此沟通类型已存在相同沟通结果,请修改后再试";
            return false;
        }

        $remk_only = false;
        if ($this->DataControl->getFieldOne("smc_code_trackresult", "trackresult_id", "trackresult_id='{$request['trackresult_id']}' and company_id='{$request['company_id']}' and trackresult_use>0")) {
            $remk_only = true;
        }

        $data = array();
        if (!$remk_only) {
            $data['tracktype_id'] = $request['tracktype_id'];
            $data['trackresult_name'] = $request['trackresult_name'];
        }
        $data['trackresult_remk'] = $request['trackresult_remk'];

        if (!$this->DataControl->updateData("smc_code_trackresult", "trackresult_id='{$request['trackresult_id']}'", $data)) {
            $this->error = true;
            $this->errortip = "编辑失败";
            return false;
        } else {
            if ($remk_only) {
                $this->errortip = "沟通结果已使用，仅修改备注信息";
            } else {
                $this->errortip = "编辑成功";
            }
            return true;
        }
    }

    function delTrackResult($request)
    {
        if ($this->DataControl->getFieldOne("smc_code_trackresult", "trackresult_id", "company_id='{$request['company_id']}' and trackresult_id='{$request['trackresult_id']}' and trackresult_use>0")) {
            $this->error = true;
            $this->errortip = "沟通结果已使用,不可删除";
            return false;
        }

        if ($this->DataControl->delData("smc_code_trackresult", "trackresult_id='{$request['trackresult_id']}'")) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "删除失败";
            return false;
        }
    }

}
