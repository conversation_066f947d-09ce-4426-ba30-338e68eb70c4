<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class IntegralModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //积分类型列表
    function getIntegraltypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (i.integraltype_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['integraltype_class']) && $paramArray['integraltype_class'] !== "") {
            $datawhere .= " and i.integraltype_class ='{$paramArray['integraltype_class']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                i.integraltype_id,
                i.company_id,
                i.integraltype_name,
                i.integraltype_class,
                i.integraltype_class as integraltype_class_name,
                i.integraltype_remark
            FROM
                smc_code_integraltype AS i
            WHERE
                {$datawhere}
                AND (i.company_id = '{$paramArray['company_id']}' or i.company_id = '0')
            ORDER BY integraltype_id DESC
            LIMIT {$pagestart},{$num}";

        $IntegraltypeList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "积分扣除", "1" => "积分增加"));

        foreach($IntegraltypeList as &$val){
            $val['integraltype_class_name'] = $status[$val['integraltype_class_name']];
            $val['integraltype_name'] = $this->LgStringSwitch($val['integraltype_name']);
            if($val['integraltype_remark'] == ''){
                $val['integraltype_remark'] = '--';
            }
        }

        $all_num = $this->DataControl->selectOne("
            SELECT
               COUNT(i.integraltype_id) as a
             FROM
                smc_code_integraltype AS i
            WHERE
                {$datawhere}
                AND (i.company_id = '{$paramArray['company_id']}' or i.company_id = '0')");
        $allnums = $all_num['a'];

        $fieldstring = array('integraltype_name ', 'integraltype_class_name', 'integraltype_remark');
        $fieldname = $this->LgArraySwitch(array('积分类型名称', '所属类别', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($IntegraltypeList) {
            $result['list'] = $IntegraltypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂未添加积分类型，点击右上角新增可添加", 'result' => $result);
        }

        return $res;
    }

    //添加积分类型
    function addIntegraltypeAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['integraltype_name'] = $paramArray['integraltype_name'];
        $data['integraltype_class'] = $paramArray['integraltype_class'];
        $data['integraltype_remark'] = $paramArray['integraltype_remark'];

        $field = array();
        $field['integraltype_name'] = "积分类型名称";
        $field['integraltype_class'] = "积分类型";
        $field['integraltype_remark'] = "积分类型备注";
        $field['company_id'] = "所属集团";

        if($this->DataControl->insertData('smc_code_integraltype', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加积分类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '添加积分类型', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加失败积分类型名称重复！', 'result' => $result);
        }

        return $res;
    }

    //编辑积分类型
    function updateIntegraltypeAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integraltype", "integraltype_id", "integraltype_id = '{$paramArray['integraltype_id']}'");
        if ($IntegraltypeOne) {
            $data = array();
            $data['integraltype_name'] = $paramArray['integraltype_name'];
            $data['integraltype_class'] = $paramArray['integraltype_class'];
            $data['integraltype_remark'] = $paramArray['integraltype_remark'];

            $field = array();
            $field['integraltype_name'] = "积分类型名称";
            $field['integraltype_class'] = "积分类型";
            $field['integraltype_remark'] = "积分类型备注";

            if ($this->DataControl->updateData("smc_code_integraltype", "integraltype_id = '{$paramArray['integraltype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑积分类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '编辑积分类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑积分类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除积分类型
    function delIntegraltypeAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integraltype", "integraltype_id", "integraltype_id = '{$paramArray['integraltype_id']}'");
        if ($IntegraltypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("smc_student_integrallog","integraltype_id","integraltype_id = '{$paramArray['integraltype_id']}'");
            if($a){
                ajax_return(array('error' => 1, 'errortip' => "该积分类型已被使用，无法删除！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("smc_code_integraltype", "integraltype_id = '{$paramArray['integraltype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除积分类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '删除积分类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除积分类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }




    //积分规则列表
    function IntegralruleList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (i.integralrule_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['integralrule_class']) && $paramArray['integralrule_class'] !== "") {
            $datawhere .= " and i.integralrule_class ='{$paramArray['integralrule_class']}'";
        }
        if($paramArray['type'] == '1'){
            $datawhere .= " and (i.integralrule_class ='1' or i.integralrule_class ='2')";
        }else{
            $datawhere .= " and (i.integralrule_class ='3' or i.integralrule_class ='4')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                i.integralrule_id,
                i.company_id,
                i.integralrule_name,
                i.integralrule_class,
                i.integralrule_class as integralrule_class_name,
                i.integralrule_status,
                i.integralrule_type,
                i.integralrule_type as integralrule_type_name,
                i.integralrule_integral,
                i.integralrule_remark
            FROM
                smc_code_integralrule AS i
            WHERE
                {$datawhere}
                AND i.company_id = '{$paramArray['company_id']}'
            ORDER BY i.integralrule_id DESC
            LIMIT {$pagestart},{$num}";

        $IntegraltypeList = $this->DataControl->selectClear($sql);

        $status1 = $this->LgArraySwitch(array("1" => "自动发放", "2" => "手动发放", "3" => "自动扣除", "4" => "手动扣除"));
        $status2 = $this->LgArraySwitch(array("0" => "不限制", "1" => "限制"));

        foreach($IntegraltypeList as &$val){
            $val['integralrule_class_name'] = $status1[$val['integralrule_class_name']];
            if($val['integralrule_class'] == '1' || $val['integralrule_class'] == '3'){
                $val['integralrule_type_name'] = '--';
            }else{
                if($val['integralrule_type'] == '1'){
                    $val['integralrule_type_name'] = $status2[$val['integralrule_type_name']].'（'.$val['integralrule_integral'].'积分'.'）';
                }else{
                    $val['integralrule_type_name'] = $status2[$val['integralrule_type_name']];
                }
            }
            $val['integralrule_name'] = $this->LgStringSwitch($val['integralrule_name']);
            if($val['integralrule_remark'] == ''){
                $val['integralrule_remark'] = '--';
            }
        }

        $all_num = $this->DataControl->selectOne("
            SELECT
               COUNT(i.integralrule_id) as a
             FROM
                smc_code_integralrule AS i
            WHERE
                {$datawhere}
                AND i.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num['a'];

        $fieldstring = array('integralrule_name', 'integralrule_remark', 'integralrule_class_name', 'integralrule_type_name', 'integralrule_status');
        if($paramArray['type'] == '1'){
            $fieldname = $this->LgArraySwitch(array('积分规则名称', '规则说明', '发放类型', '是否限制发放积分', '是否启用'));
        }else{
            $fieldname = $this->LgArraySwitch(array('积分规则名称', '规则说明', '扣除类型', '是否限制扣除积分', '是否启用'));
        }
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");
        $fieldisswitch = array("0", "0", "0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($IntegraltypeList) {
            $result['list'] = $IntegraltypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂未添加积分类型，点击右上角新增可添加", 'result' => $result);
        }

        return $res;
    }

    //添加积分规则
    function addIntegralruleAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['integralrule_name'] = $paramArray['integralrule_name'];
        $data['integralrule_class'] = $paramArray['integralrule_class'];
        $data['integralrule_type'] = $paramArray['integralrule_type'];
        $data['integralrule_integral'] = $paramArray['integralrule_integral'];
        $data['integralrule_remark'] = $paramArray['integralrule_remark'];

        $field = array();
        $field['integralrule_name'] = "积分规则名称";
        $field['integralrule_class'] = "积分发放类型  1-自动发放 2-手动发放 3-自动扣除 4-手动扣除";
        $field['integralrule_type'] = "是否限制积分  0-不限制 1限制";
        $field['integralrule_integral'] = "限制积分数量";
        $field['integralrule_remark'] = "积分规则说明";
        $field['company_id'] = "所属集团";

        if($this->DataControl->insertData('smc_code_integralrule', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加积分规则成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '添加积分规则', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加失败积分规则名称重复！', 'result' => $result);
        }

        return $res;
    }

    //编辑积分规则
    function updateIntegralruleAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_id,integralrule_name", "integralrule_id = '{$paramArray['integralrule_id']}'");
        if ($IntegraltypeOne) {

            $isuse = $this->DataControl->getFieldOne("smc_student_integrallog","integrallog_id","integrallog_rule = '{$IntegraltypeOne['integralrule_name']}' and company_id = '{$paramArray['company_id']}'");

            if($isuse){
                ajax_return(array('error' => 1, 'errortip' => "规则已被使用无法编辑！"), $this->companyOne['company_language']);
            }

            $data = array();
            $data['integralrule_name'] = $paramArray['integralrule_name'];
            $data['integralrule_type'] = $paramArray['integralrule_type'];
            $data['integralrule_integral'] = $paramArray['integralrule_integral'];
            $data['integralrule_remark'] = $paramArray['integralrule_remark'];

            $field = array();
            $field['integralrule_name'] = "积分规则名称";
            $field['integralrule_type'] = "是否限制积分  0-不限制 1限制";
            $field['integralrule_integral'] = "限制积分数量";
            $field['integralrule_remark'] = "积分规则说明";

            if ($this->DataControl->updateData("smc_code_integralrule", "integralrule_id = '{$paramArray['integralrule_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑积分规则成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '编辑积分规则', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑积分规则失败，名称重复！', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //设置规则
    function updateIntegralAutoruleAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_id", "integralrule_id = '{$paramArray['integralrule_id']}'");
        if ($IntegraltypeOne) {
            $data = array();
            $data['integralrule_integral'] = $paramArray['integralrule_integral'];
            if($paramArray['integralrule_name'] == '考勤加分'){
                $data['integralrule_remark'] = '考勤1次发放'.$paramArray['integralrule_integral'].'积分';
            }
            if($paramArray['integralrule_name'] == '课消加分'){
                $data['integralrule_remark'] = '按课消金额的'.$paramArray['integralrule_integral'].'%发放积分';
            }

            $field = array();
            $field['integralrule_integral'] = "限制积分数量";

            if ($this->DataControl->updateData("smc_code_integralrule", "integralrule_id = '{$paramArray['integralrule_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑积分规则成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '编辑积分规则', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑积分规则失败，名称重复！', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除积分规则
    function delIntegralruleAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_id,integralrule_name", "integralrule_id = '{$paramArray['integralrule_id']}'");
        if ($IntegraltypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }

            $isuse = $this->DataControl->getFieldOne("smc_student_integrallog","integrallog_id","integrallog_rule = '{$IntegraltypeOne['integralrule_name']}' and company_id = '{$paramArray['company_id']}'");

            if($isuse){
                ajax_return(array('error' => 1, 'errortip' => "规则已被使用无法删除！"), $this->companyOne['company_language']);
            }


            if ($this->DataControl->delData("smc_code_integralrule", "integralrule_id = '{$paramArray['integralrule_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除积分规则成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '删除积分规则', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除积分规则失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }






    //积分区间列表
    function getIntegralsectionList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                i.integralsection_id,
                i.integralsection_begin,
                i.integralsection_end,
                i.integralsection_remark
            FROM
                smc_code_integralsection AS i
            WHERE
                {$datawhere}
                AND i.company_id = '{$paramArray['company_id']}'
            order by integralsection_id DESC
            LIMIT {$pagestart},{$num}";

        $IntegraltypeList = $this->DataControl->selectClear($sql);

        foreach($IntegraltypeList as &$val){
            $val['between'] = $val['integralsection_begin'].'-'.$val['integralsection_end'];
            if($val['integralsection_remark'] == ''){
                $val['integralsection_remark'] = '--';
            }
        }

        $all_num = $this->DataControl->selectOne("
            SELECT
               COUNT(i.integralsection_id) as a
             FROM
                smc_code_integralsection AS i
            WHERE
                {$datawhere}
                AND i.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num['a'];

        $fieldstring = array('between', 'integralsection_remark');
        $fieldname = $this->LgArraySwitch(array('积分区间', '备注'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($IntegraltypeList) {
            $result['list'] = $IntegraltypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无积分区间", 'result' => $result);
        }

        return $res;
    }

    //添加积分区间
    function addIntegralsectionAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['integralsection_begin'] = $paramArray['integralsection_begin'];
        $data['integralsection_end'] = $paramArray['integralsection_end'];
        $data['integralsection_remark'] = $paramArray['integralsection_remark'];

        $field = array();
        $field['integralsection_begin'] = "区间开始";
        $field['integralsection_end'] = "区间结束";
        $field['integralsection_remark'] = "区间备注";
        $field['company_id'] = "所属集团";

        if($this->DataControl->insertData('smc_code_integralsection', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加积分区间成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '添加积分区间', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加积分区间失败', 'result' => $result);
        }

        return $res;
    }

    //编辑积分区间
    function updateIntegralsectionAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integralsection", "integralsection_id", "integralsection_id = '{$paramArray['integralsection_id']}'");
        if ($IntegraltypeOne) {
            $data = array();
            $data['integralsection_begin'] = $paramArray['integralsection_begin'];
            $data['integralsection_end'] = $paramArray['integralsection_end'];
            $data['integralsection_remark'] = $paramArray['integralsection_remark'];

            $field = array();
            $field['integralsection_begin'] = "区间开始";
            $field['integralsection_end'] = "区间结束";
            $field['integralsection_remark'] = "区间备注";

            if ($this->DataControl->updateData("smc_code_integralsection", "integralsection_id = '{$paramArray['integralsection_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑积分区间成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '编辑积分区间', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑积分区间失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除积分区间
    function delIntegralsectionAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("smc_code_integralsection", "integralsection_id", "integralsection_id = '{$paramArray['integralsection_id']}'");
        if ($IntegraltypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("smc_code_integralsection", "integralsection_id = '{$paramArray['integralsection_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除积分区间成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '删除积分区间', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除积分区间失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //商品分类设置
    function getGoodsSortList($paramArray)
    {
        $datawhere = " c.company_id = '{$paramArray['company_id']}' ";
        if(isset($paramArray['class_type']) && $paramArray['class_type'] != ''){
            $datawhere .= " and c.class_type = '{$paramArray['class_type']}'";
        }
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (c.class_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.*,(SELECT COUNT(cc.category_id) FROM shop_code_category as cc WHERE cc.category_class = c.class_id) as category_num
            FROM
                shop_code_class as c
            WHERE
                {$datawhere}
            ORDER BY
                c.class_id ASC
            LIMIT {$pagestart},{$num}";

        $GoodsSortList = $this->DataControl->selectClear($sql);
        if(!$GoodsSortList){
            for($i = 1; $i <= 4; $i++){
                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                if($i == 1){
                    $data['class_type'] = '2';
                    $data['class_name'] = '奇瓦迪商铺';
                    $data['class_listmodel'] = '1';
                    $data['class_imgico'] = 'https://pic.kedingdang.com/schoolmanage/202004281058x756685464.png';
                }elseif($i == 2){
                    $data['class_type'] = '1';
                    $data['class_name'] = '吉的堡学院';
                    $data['class_listmodel'] = '0';
                    $data['class_imgico'] = 'https://pic.kedingdang.com/schoolmanage/202004281059x553388432.png';
                }elseif($i == 3){
                    $data['class_type'] = '3';
                    $data['class_name'] = '活动赛事';
                    $data['class_listmodel'] = '1';
                    $data['class_imgico'] = 'https://pic.kedingdang.com/schoolmanage/202004281100x964823004.png';
                }elseif($i == 4){
                    $data['class_type'] = '2';
                    $data['class_name'] = '奇趣教材';
                    $data['class_listmodel'] = '1';
                    $data['class_imgico'] = 'https://pic.kedingdang.com/schoolmanage/202004281100x534868494.png';
                }
                $this->DataControl->insertData("shop_code_class", $data);
            }
        }

        $status = $this->LgArraySwitch(array("1" => "课程销售", "2"=>"普通商品销售", "3"=>"课程杂费销售", "4" => "课程教材销售"));
        foreach($GoodsSortList as &$val){
            $val['class_type_name'] = $status[$val['class_type']];
            if(!$val['class_remark']){
                $val['class_remark'] = "--";
            }
        }

        $all_num = $this->DataControl->selectOne("
            SELECT
               COUNT(c.class_id) as a
             FROM
                shop_code_class as c
            WHERE
                {$datawhere}");
        $allnums = $all_num['a'];

        $fieldstring = array('class_name', 'class_imgico', 'class_type_name', 'category_num', 'class_isopen', 'class_remark');
        $fieldname = $this->LgArraySwitch(array('商品分类名称', '商品分类图标', '所属类型', '下级分类', '是否启用', '备注'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($GoodsSortList) {
            $result['list'] = $GoodsSortList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无商品分类", 'result' => $result);
        }

        return $res;
    }

    //编辑商品分类
    function updateGoodsSortAction($paramArray)
    {
        $ClassOne = $this->DataControl->getFieldOne("shop_code_class", "class_id", "class_id = '{$paramArray['class_id']}'");
        if ($ClassOne) {
            $data = array();
            $data['class_name'] = $paramArray['class_name'];
            $data['class_type'] = $paramArray['class_type'];
            $data['class_imgico'] = $paramArray['class_imgico'];
            $data['class_isopen'] = $paramArray['class_isopen'];
            $data['class_remark'] = $paramArray['class_remark'];

            $field = array();
            $field['class_name'] = "商品分类名称";
            $field['class_type'] = "所属类型";
            $field['class_imgico'] = "商品分类图标";
            $field['class_isopen'] = "是否启用";
            $field['class_remark'] = "备注";

            if ($this->DataControl->updateData("shop_code_class", "class_id = '{$paramArray['class_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑商品分类成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '编辑商品分类', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑商品分类失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //是否启用
    function isOpenAction($paramArray)
    {
        $ClassOne = $this->DataControl->getFieldOne("shop_code_class", "class_id", "class_id = '{$paramArray['class_id']}'");
        if ($ClassOne) {
            $data = array();
            $data['class_isopen'] = $paramArray['class_isopen'];
            if($paramArray['class_isopen']){
                $tip = '开启';
            }else{
                $tip = '关闭';
            }

            $field = array();
            $field['class_isopen'] = "是否启用";
            if($this->DataControl->updateData("shop_code_class", "class_id = '{$paramArray['class_id']}'", $data)){
                if(!$paramArray['class_isopen']){
                    $this->DataControl->updateData("shop_sellgoods","class_id = '{$paramArray['class_id']}'",array("sellgoods_issale"=>'0'));
                }
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "商品分类{$tip}成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", "商品分类{$tip}", dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => "商品分类{$tip}失败", 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //是否启用积分规则
    function isOpenRuleAction($paramArray)
    {
        $RuleOne = $this->DataControl->getFieldOne("smc_code_integralrule", "integralrule_id,integralrule_class,integralrule_name", "integralrule_id = '{$paramArray['integralrule_id']}'");
        if ($RuleOne) {
            $data = array();
            $data['integralrule_status'] = $paramArray['integralrule_status'];

            $field = array();
            $field['integralrule_status'] = "是否启用";

            $num = $this->DataControl->selectOne("select count(integralrule_id) as num from smc_code_integralrule where company_id = '{$paramArray['company_id']}' and integralrule_class = '1' and integralrule_status = '1'");

            if($RuleOne['integralrule_class'] == '1' && $paramArray['integralrule_status'] == '1'){
                if($num['num'] == '1'){
                    ajax_return(array('error' => 1, 'errortip' => "考勤加分与课消加分不能同时启用！"), $this->companyOne['company_language']);
                }
            }

            if($this->DataControl->updateData("smc_code_integralrule", "integralrule_id = '{$paramArray['integralrule_id']}'", $data)){
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", "修改积分规则启用状态", dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => "启用失败", 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //商品下级分类设置
    function getCategoryList($paramArray)
    {
        $datawhere = " c.category_class = '{$paramArray['class_id']}' ";
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (c.category_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.*
            FROM
                shop_code_category as c
            WHERE
                {$datawhere}
            ORDER BY
                c.category_sort ASC
            LIMIT {$pagestart},{$num}";

        $GoodsSortList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("
            SELECT
               COUNT(c.category_id) as a
             FROM
                shop_code_category as c
            WHERE
                {$datawhere}");
        $allnums = $all_num['a'];

        $fieldstring = array('category_name', 'category_imgico', 'category_sort', 'category_remark');
        $fieldname = $this->LgArraySwitch(array('下级分类名称', '下级分类图标', '排序',  '备注'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $class = $this->DataControl->getFieldOne("shop_code_class","class_name","class_id = '{$paramArray['class_id']}'");

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;
        $result['class_name'] = $class['class_name'];

        if ($GoodsSortList) {
            $result['list'] = $GoodsSortList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "该商品分类暂未添加下级分类，点击右上角即可新增", 'result' => $result);
        }

        return $res;
    }

    //添加商品下级分类
    function addCategoryAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['category_name'] = $paramArray['category_name'];
        $data['category_class'] = $paramArray['class_id'];
        $data['category_imgico'] = $paramArray['category_imgico'];
        $data['category_sort'] = $paramArray['category_sort'];
        $data['category_remark'] = $paramArray['category_remark'];

        $field = array();
        $field['category_name'] = "下级分类名称";
        $field['category_imgico'] = "下级分类图标";
        $field['category_sort'] = "排序";
        $field['category_remark'] = "备注";

        if($this->DataControl->insertData('shop_code_category', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加商品下级分类成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '添加商品下级分类', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加商品下级分类失败', 'result' => $result);
        }

        return $res;
    }

    //编辑商品下级分类
    function updateCategoryAction($paramArray)
    {
        if ($this->DataControl->getFieldOne("shop_code_category", "category_id", "company_id = '{$paramArray['company_id']}' and category_name = '{$paramArray['category_name']}' and category_id <> '{$paramArray['category_id']}'")) {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '下级分类名称重复', 'result' => $result);
            return $res;
        }

        $data = array();
        $data['category_name'] = $paramArray['category_name'];
        $data['category_imgico'] = $paramArray['category_imgico'];
        $data['category_sort'] = $paramArray['category_sort'];
        $data['category_remark'] = $paramArray['category_remark'];

        $field = array();
        $field['category_name'] = "下级分类名称";
        $field['category_imgico'] = "下级分类图标";
        $field['category_sort'] = "排序";
        $field['category_remark'] = "备注";

        if ($this->DataControl->updateData("shop_code_category", "category_id = '{$paramArray['category_id']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "编辑商品下级分类成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '编辑商品下级分类', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '编辑商品下级分类失败', 'result' => $result);
        }
        return $res;
    }

    //删除商品下级分类
    function delCategoryAction($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("shop_code_category", "category_id", "category_id = '{$paramArray['category_id']}'");
        if ($IntegraltypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("shop_code_category", "category_id = '{$paramArray['category_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除商品下级分类成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->微商城相关设置", '删除商品下级分类', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除商品下级分类失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }
}
