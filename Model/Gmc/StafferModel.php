<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  StafferModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }


    //人脸采集 -- 查询职工是否是集团职务  -- 20230821暂不验证
    function getStaffIsGmcApi($paramArray){
        if (!isset($paramArray['StafferBranch']) || $paramArray['StafferBranch'] == '') {
            $result = array();
            $result["field"] = array();
            $result['list'] = array();

            $this->error = 1;
            $this->errortip = "请输入您的职工编号！";
            $this->result = $result;
            return false;
        }

        $datawhere = " 1 and s.company_id in ('8888','1001') and s.staffer_leave = '0' 
        and (s.staffer_branch = '{$paramArray['StafferBranch']}' or s.staffer_employeepid = '{$paramArray['StafferBranch']}') 
        and p.school_id = '0' and p.postbe_id > '1'  ";

        $sql = " SELECT distinct(s.staffer_id),s.company_id,s.staffer_cnname,s.staffer_branch,
                (select t.stuportrait_faceimg from gmc_machine_stuportrait as t where t.main_staffer_id = s.staffer_id order by t.stuportrait_creattime desc limit 0,1) as stuportrait_faceimg
                FROM smc_staffer as s  
                left join gmc_staffer_postbe as p ON s.staffer_id = p.staffer_id
                where {$datawhere} ";
        $sataffOne = $this->DataControl->selectOne($sql);

        if(!$sataffOne){
            $result = array();
            $result["field"] = array();
            $result['list'] = array();

            $this->error = 1;
            $this->errortip = "您不在本次人脸采集的列表中，请联系管理员！";
            $this->result = $result;
            return false;
        }

        $sataffOne['stuportrait_faceimg'] = is_null($sataffOne['stuportrait_faceimg']) ? '' : $sataffOne['stuportrait_faceimg'];
        //人像采集二维码
        $sataffOne['ishaveportrait'] = $sataffOne['stuportrait_faceimg'] ? 1 : 0;
        //人像采集二维码 isstaff   0 学生  1 校职工  2 集团职工
        $nowtime = ceil(microtime(true) * 1000);
        $urlparam = base64_encode("main_staffer_branch={$sataffOne['staffer_branch']}&company_id={$sataffOne['company_id']}&isstaff=2&opentime={$nowtime}");

        if ($_SERVER['SERVER_NAME'] != 'smcapi.kedingdang.com' && $_SERVER['SERVER_NAME'] != 'gmcapi.kedingdang.com') {
            $portraiturl = "http://faceentry.kcclassin.com/?" . $urlparam;
        } else {
            $portraiturl = "https://faceentry.kedingdang.com/?" . $urlparam;
        }
        $sataffOne['portraitqrcode'] = "https://smcapi.kedingdang.com/OrderPay/urlshowimgTwo?imgurl=" . urlencode(base64_encode($portraiturl));


        $field = array();
        $field['company_id'] = "集团ID - 目前默认";
        $field['staffer_id'] = "查询出来的职工ID";
        $field['staffer_branch'] = "查询出来的职工编号";
        $field['staffer_cnname'] = "查询出来的职工姓名";
        $field['stuportrait_faceimg'] = "人脸图片";
        $field['ishaveportrait'] = "是否有人像图片 1 有 0 没有 ";
        $field['portraitqrcode'] = "人像录入二维码";

        $result = array();
        $result["field"] = $field;
        $result['list'] = $sataffOne;

        $this->error = 0;
        $this->errortip = "信息获取成功";
        $this->result = $result;
        return true;
    }


    /**
     * @param $paramArray
     * @return array
     *   获取职工列表
     */
    function getStafferList($paramArray)
    {
        $datawhere = " s.account_class = '0' AND s.company_id = '{$paramArray['company_id']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            if($paramArray['keyword'] == '###'){
                $datawhere .= " AND s.staffer_employeepid = ''";
            }else{
                $datawhere .= " AND (s.staffer_cnname like '%{$paramArray['keyword']}%' or s.staffer_enname like '%{$paramArray['keyword']}%' or s.staffer_id = '{$paramArray['keyword']}' or s.staffer_employeepid = '{$paramArray['keyword']}' or s.staffer_mobile like '%{$paramArray['keyword']}%' or s.staffer_branch like '%{$paramArray['keyword']}%')";
            }
        }
        if (isset($paramArray['post_id']) && $paramArray['post_id'] !== "") {
            $datawhere .= " and p.post_id ={$paramArray['post_id']}";
        }
        if (isset($paramArray['staffer_native']) && $paramArray['staffer_native'] !== "") {
            $datawhere .= " and s.staffer_native ={$paramArray['staffer_native']}";
        }
        if (isset($paramArray['staffer_leave']) && $paramArray['staffer_leave'] !== "") {
            $datawhere .= " and s.staffer_leave ={$paramArray['staffer_leave']}";
        }
        if (isset($paramArray['staffer_isparttime']) && $paramArray['staffer_isparttime'] !== "") {
            $datawhere .= " and s.staffer_isparttime ={$paramArray['staffer_isparttime']}";
        }
        if (isset($paramArray['postlevel_id']) && $paramArray['postlevel_id'] !== "") {
            $datawhere .= " and p.postlevel_id ={$paramArray['postlevel_id']}";
        }

        if($paramArray['school_id'] !== ''){
            if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
                $datawhere .= " and p.school_id ={$paramArray['school_id']}";
            }
        }else{
            if (isset($paramArray['organize_id']) && $paramArray['organize_id'] !== "") {
                $datawhere .= " and p.organize_id ={$paramArray['organize_id']}";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                s.staffer_id AS stafferOne_id,
                s.staffer_cnname,s.staffer_enname,
                s.staffer_branch,
                s.staffer_sex,
                s.staffer_mobile,
                s.staffer_isparttime,
                s.staffer_isparttime as staffer_isparttime_name,
                s.staffer_branch,
                s.staffer_wxtoken,
                s.staffer_email,
                s.staffer_jointime,
                s.staffer_leave,
                s.staffer_leave as staffer_leave_name,
                s.staffer_native,
                s.staffer_native as staffer_native_name,
                s.staffer_employeepid,
                s.company_id,
                (
            SELECT
                c.post_name
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c ON p.post_id = c.post_id
            WHERE
                p.staffer_id = s.staffer_id 
            ORDER BY
                p.postbe_createtime DESC 
                LIMIT 1 
                ) as post_name,
                (
            SELECT
                w.postlevel_cnname
            FROM
                gmc_staffer_postbe AS a
                LEFT JOIN gmc_company_postlevel AS w ON a.postlevel_id = w.postlevel_id
            WHERE
                a.staffer_id = s.staffer_id 
            ORDER BY
                a.postbe_createtime DESC 
                LIMIT 1 
                ) as postlevel_cnname,
                (select count(postbe_id) as a from gmc_staffer_postbe as c WHERE c.staffer_id = s.staffer_id and company_id = '{$paramArray['company_id']}' and school_id = 0 and postbe_ismianjob = 1) as status
            FROM
                smc_staffer AS s
                LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = s.staffer_id 
            WHERE
                {$datawhere}
            GROUP BY s.staffer_id
            ORDER BY
                s.staffer_id DESC
                LIMIT {$pagestart},{$num}";

        $stafferList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("
            SELECT
                COUNT(distinct s.staffer_id) as countnums
            FROM
                smc_staffer AS s LEFT JOIN gmc_staffer_postbe AS p ON p.staffer_id = s.staffer_id 
            WHERE
                {$datawhere}");
        $all_nums = $all_num['countnums'];

        $statuss = $this->LgArraySwitch(array("0" => "陆籍", "1" => "外籍", "2" => "港澳籍", "3" => "台籍"));

        if($stafferList){
            foreach($stafferList as &$stafferOne){
                $stafferOne['staffer_leave_name'] = $stafferOne['staffer_leave_name']=='1' ? $this->LgStringSwitch("已离职") : $this->LgStringSwitch("在职");
                $stafferOne['staffer_isparttime_name'] = $stafferOne['staffer_isparttime_name']=='1' ? $this->LgStringSwitch("兼职") : $this->LgStringSwitch("全职");
//                $stafferOne['staffer_jointime'] = date('Y-m-d', $stafferOne['staffer_jointime']);
                $stafferOne['staffer_native_name'] = $statuss[$stafferOne['staffer_native_name']];
                if($stafferOne['staffer_wxtoken']){
                    $stafferOne['isWx'] = '是';
                }else{
                    $stafferOne['isWx'] = '否';
                }
            }
        }

        $fieldstring = array('staffer_cnname', 'staffer_enname', 'staffer_branch', 'staffer_isparttime_name', 'staffer_sex', 'staffer_mobile', 'post_name', 'staffer_email', 'staffer_native_name', 'staffer_jointime', "staffer_leave_name", "staffer_employeepid", "isWx");
        $fieldname = $this->LgArraySwitch(array('中文名', '英文名', '教师编号', '职工类型', '性别', '电话', '职务', '职工邮箱', '籍贯', '入职时间', '在职状态', '职工编号', '是否绑定微信'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "0", "1", "0", "1", "0", "1");
        $fieldshow =   array("1", "1", "1", "1", "1", "1", "1", "0", "1", "0", "1", "0", "1");
        $fieldismethod =   array("1", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["ismethod"] = trim($fieldismethod[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($stafferList) {
            $result['list'] = $stafferList;
        } else {
            $result['list'] = array();
        }

        $datawheres = " 1 ";

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawheres .= " and b.school_id ={$paramArray['school_id']}";
        }

        if (isset($paramArray['organize_id']) && $paramArray['organize_id'] !== "") {
            $datawheres .= " and b.organize_id ={$paramArray['organize_id']}";
        }

        $result['postSchool'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$paramArray['company_id']}' AND post_type = 1");

        $result['post'] = $this->DataControl->selectClear("
            SELECT
                p.post_id,
                p.post_name 
            FROM
                gmc_company_post as p
            WHERE
                p.company_id = '{$paramArray['company_id']}'
                GROUP BY p.post_id");

        $result['postCompany'] = $this->DataControl->selectClear("select post_id,post_name from gmc_company_post where company_id = '{$paramArray['company_id']}' AND post_type = 0");

        $result['organize'] = $this->DataControl->selectClear("select organize_id,organize_cnname from gmc_company_organize where company_id = '{$paramArray['company_id']}'");

        $result['postlevel'] = $this->DataControl->selectClear("select postlevel_id,postlevel_cnname from gmc_company_postlevel where company_id = '{$paramArray['company_id']}'");

        $result['postrole'] = $this->DataControl->selectClear("select postrole_id,postrole_name,postpart_isucsuser,postpart_ucsuserlevel,postpart_isgmccrm,postpart_gmccrmlevel from gmc_company_postrole where company_id = '{$paramArray['company_id']}'");
        if($result['postrole']){
            foreach($result['postrole'] as &$item){
                if(!$item['postpart_ucsuserlevel']){
                    $item['postpart_ucsuserlevel'] = '';
                }
            }
        }
//        $result['school'] = $this->DataControl->selectClear("select s.school_cnname from gmc_company_organizeschool as o left join smc_school as s on o.school_id = s.school_id WHERE o.organize_id = '{$paramArray['organize_id']}'");

        $result['postpart'] = $this->DataControl->selectClear("select postpart_id,postpart_name from smc_school_postpart where company_id = '{$paramArray['company_id']}'");

        $school_cnname = $this->DataControl->getFieldOne('smc_school', 'school_cnname,school_shortname', "school_id = '{$paramArray['school_id']}'");
        $result['school_cnname'] = $school_cnname['school_shortname']?$school_cnname['school_shortname']:$school_cnname['school_cnname'];

        $organize_cnname = $this->DataControl->getFieldOne('gmc_company_organize', 'organize_cnname', "organize_id = '{$paramArray['organize_id']}'");
        $result['organize_cnname'] = $organize_cnname['organize_cnname'];

        $company_cnname = $this->DataControl->getFieldOne('gmc_company', 'company_cnname', "company_id = '{$paramArray['company_id']}'");
        $result['company_cnname'] = $company_cnname['company_cnname'];


        $result['all_num'] = $all_nums;

        if($result['list']){
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => "暂无职工信息", 'result' => $result);
        }

        return $res;
    }

    //职工管理 -- 修改职工信息
    function updateStafferInfoAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($stafferOne) {
            $marketer = $this->DataControl->getFieldOne("crm_marketer","marketer_id","staffer_id = '{$stafferOne['staffer_id']}'");
            if($marketer){
                $data = array();
                $data['marketer_name'] = $paramArray['staffer_cnname'];
                if(isset($paramArray['staffer_mobile'])){
                    $data['marketer_mobile'] = $paramArray['staffer_mobile'];
                }
                if(isset($paramArray['staffer_img'])){
                    $data['marketer_img'] = $paramArray['staffer_img'];
                }
                $this->DataControl->updateData("crm_marketer", "marketer_id = '{$marketer['marketer_id']}'", $data);

            }
            $data = array();

            $data['staffer_istest'] = $paramArray['staffer_istest'];
            $data['staffer_ismanage'] = $paramArray['staffer_ismanage'];
            if(isset($paramArray['staffer_mobile'])){
                $data['staffer_mobile'] = $paramArray['staffer_mobile'];
            }
            if(isset($paramArray['staffer_email'])){
                $data['staffer_email'] = $paramArray['staffer_email'];
            }
            $data['staffer_cnname'] = $paramArray['staffer_cnname'];
            $data['staffer_native'] = $paramArray['staffer_native'];
            $data['staffer_enname'] = $paramArray['staffer_enname'];
            $data['staffer_employeepid'] = $paramArray['staffer_employeepid'];
//            if(isset($paramArray['staffer_branch'])){
//                $data['staffer_branch'] = $paramArray['staffer_branch'];
//            }
            if(isset($paramArray['staffer_jointime'])){
                $data['staffer_jointime'] = $paramArray['staffer_jointime'];
            }
            $data['staffer_pass'] = md5($paramArray['staffer_pass']);
            $data['staffer_bakpass'] = $paramArray['staffer_pass'];

            if(isset($paramArray['staffer_img'])){
                $data['staffer_img'] = $paramArray['staffer_img'];
            }
            $data['staffer_sex'] = $paramArray['staffer_sex'];
            $data['staffer_updatetime'] = time();

            $data2 = array();
            $data2['staffer_id'] = $paramArray['stafferOne_id'];
            $data2['info_birthday'] = $paramArray['staffer_birthday'];

            $field = array();
            $field['staffer_mobile'] = "手机号";
            $field['staffer_email'] = "职工邮箱";
            $field['staffer_cnname'] = "中文名";
            $field['staffer_cnname'] = "英文名";
            $field['staffer_branch'] = "职工编号";
            $field['staffer_jointime'] = "入职时间";
            $field['staffer_birthday'] = "出生日期";
            $field['staffer_pass'] = "初始密码";
            $field['staffer_sex'] = "性别";
            $field['staffer_img'] = "头像";
            $field['staffer_updatetime'] = "修改时间";

//            $staffer_id=$this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_mobile = '{$paramArray['staffer_mobile']}' and staffer_id != '{$paramArray['stafferOne_id']}' and company_id = '{$paramArray['company_id']}'");
//            if($staffer_id){
//                ajax_return(array('error' => 1,'errortip' => "手机号已存在!修改职工信息失败!"),$this->companyOne['company_language']);
//            }

//            $staffer_branch=$this->DataControl->getFieldOne('smc_staffer', 'staffer_branch', "staffer_id = '{$paramArray['stafferOne_id']}'");
//            if($paramArray['staffer_branch']!=$staffer_branch['staffer_branch'] ){
//                $staffer_branch=$this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_branch = '{$paramArray['staffer_branch']}'");
//                if($staffer_branch){
//                    ajax_return(array('error' => 1,'errortip' => "编号已存在!"),$this->companyOne['company_language']);
//                }
//            }
//            $staffer_mobile=$this->DataControl->getFieldOne('smc_staffer', 'staffer_mobile', "staffer_id = '{$paramArray['stafferOne_id']}'");
//            if($paramArray['staffer_mobile']!=$staffer_mobile['staffer_mobile'] ){
//                $staffer_mobile=$this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_mobile = '{$paramArray['staffer_mobile']}'");
//                if($staffer_mobile){
//                    ajax_return(array('error' => 1,'errortip' => "手机号已存在!添加职工失败"),$this->companyOne['company_language']);
//                }
//            }

            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'", $data)) {
                if ($this->DataControl->getFieldOne("smc_staffer_info", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'")){
                    $this->DataControl->updateData("smc_staffer_info", "staffer_id = '{$paramArray['stafferOne_id']}'", $data2);
                }else{
                    $this->DataControl->insertData('smc_staffer_info', $data2);
                }
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工信息修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'修改职工信息',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工信息修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 删除职工信息
    function delStafferAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");

        $isTeaching = $this->DataControl->getFieldOne("smc_class_hour_teaching","teaching_id","staffer_id = '{$stafferOne['staffer_id']}'");

        $isCrm = $this->DataControl->getFieldOne("crm_marketer","marketer_id","staffer_id = '{$stafferOne['staffer_id']}'");

        $islog1 = $this->DataControl->getFieldOne("smc_staffer_worklog","worklog_id","staffer_id = '{$stafferOne['staffer_id']}'");
        $islog2 = $this->DataControl->getFieldOne("gmc_staffer_worklog","worklog_id","staffer_id = '{$stafferOne['staffer_id']}'");
        $islog3 = $this->DataControl->getFieldOne("crm_staffer_worklog","worklog_id","staffer_id = '{$stafferOne['staffer_id']}'");

        if($islog1 || $islog2 || $islog3){
            ajax_return(array('error' => 1,'errortip' => "有操作日志不可删除!"),$this->companyOne['company_language']);
        }

        if($isTeaching){
            ajax_return(array('error' => 1,'errortip' => "有上课记录的职工不可删除!"));
        }
        if($isCrm){
            ajax_return(array('error' => 1,'errortip' => "有CRM资料不可删除!"));
        }

        if ($stafferOne) {
            if ($this->DataControl->delData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工信息成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'删除职工',dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工信息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 删除职务
    function delPostAction($paramArray)
    {
        $PostOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postbe_id = '{$paramArray['postbe_id']}'");
        if ($PostOne) {
            if ($this->DataControl->delData("gmc_staffer_postbe", "postbe_id = '{$paramArray['postbe_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除职工职务成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'删除职工职务',dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除职工职务失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 添加职工信息
    function addStafferInfoAction($paramArray)
    {
        $data = array();

        $like=date("Ymd",time());


        $stuInfo=$this->DataControl->selectOne("select staffer_branch from smc_staffer where staffer_branch like '{$like}%' AND LENGTH(staffer_branch) = '14' order by staffer_branch DESC limit 0,1");
        if($stuInfo){
            $data['staffer_branch']=$stuInfo['staffer_branch']+1;
        }else{
            $data['staffer_branch'] =$like.'000001';
        }

        $data['staffer_istest'] = $paramArray['staffer_istest'];
        $data['staffer_ismanage'] = $paramArray['staffer_ismanage'];
        $data['staffer_mobile'] = $paramArray['staffer_mobile'];
        $data['staffer_cnname'] = $paramArray['staffer_cnname'];
        $data['staffer_enname'] = $paramArray['staffer_enname'];
        $data['staffer_email'] = $paramArray['staffer_email'];
        $data['staffer_employeepid'] = $paramArray['staffer_employeepid'];
        $data['staffer_native'] = $paramArray['staffer_native'];
        $data['staffer_jointime'] = $paramArray['staffer_jointime'];
        $data['company_id']     = $paramArray['company_id'];
        $data['staffer_pass']   = md5($paramArray['staffer_pass']);
        $data['staffer_bakpass']   = $paramArray['staffer_pass'];
        $data['staffer_sex']    = $paramArray['staffer_sex'];
        $data['staffer_createtime'] = time();

        $data2 = array();
        $data2['info_birthday'] = $paramArray['staffer_birthday'];

        $field = array();
        $field['staffer_mobile'] = "手机号";
        $field['staffer_cnname'] = "中文名";
        $field['staffer_cnname'] = "英文名";
        $field['staffer_email'] = "职工邮箱";
        $field['staffer_jointime'] = "入职时间";
        $field['staffer_branch'] = "职工编号";
        $field['company_id'] = "所属集团";
        $field['staffer_birthday'] = "出生日期";
        $field['staffer_pass'] = "初始密码";
        $field['staffer_sex'] = "性别";
        $field['staffer_createtime'] = "创建时间";

        $staffer_branch=$this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_branch = '{$data['staffer_branch']}'");
        if($staffer_branch){
            ajax_return(array('error' => 1,'errortip' => "编号已存在!添加职工失败!"));
        }
        $staffer_mobile=$this->DataControl->getFieldOne('smc_staffer', 'staffer_id', "staffer_mobile = '{$paramArray['staffer_mobile']}' and company_id='{$paramArray['company_id']}' and company_id = '{$paramArray['company_id']}'");
        if($staffer_mobile){
            ajax_return(array('error' => 1,'errortip' => "手机号已存在!添加职工失败！"));
        }

        if ($staffer_id = $this->DataControl->insertData("smc_staffer", $data)) {
            $result = array();
            $result["stafferOne_id"] = $staffer_id;
            $data2["staffer_id"] = $staffer_id;
            $this->DataControl->insertData('smc_staffer_info', $data2);
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工信息成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'添加职工',dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工信息失败', 'result' => $result);
        }
        return $res;
    }

    //查看职工信息
    function StafferInfoApi($paramArray)
    {
        $sql = "
            SELECT
                s.staffer_mobile,
                s.staffer_cnname,
                s.staffer_enname,
                s.staffer_branch,
                s.staffer_employeepid,
                s.staffer_jointime,
                s.staffer_img,
                s.staffer_email,
                s.staffer_native,
                s.staffer_istest,
                s.staffer_ismanage,
                i.info_birthday as staffer_birthday,
                s.staffer_bakpass as staffer_pass,
                s.staffer_sex
            FROM
                smc_staffer AS s left join smc_staffer_info as i on i.staffer_id = s.staffer_id
            WHERE
                s.staffer_id = '{$paramArray['stafferOne_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_mobile"] = "手机号";
        $field["staffer_cnname"] = "中文名";
        $field["staffer_enname"] = "英文名";
        $field["staffer_branch"] = "入职编号";
        $field["staffer_jointime"] = "入职时间";
        $field["staffer_email"] = "职工邮箱";
        $field["staffer_img"]    = "编号";
        $field["staffer_birthday"] = "生日";
        $field["staffer_pass"]   = "初始密码";
        $field["staffer_native"]   = "籍贯";
        $field["staffer_sex"]    = "性别";
        $field["staffer_istest"]    = "是否测试账号";
        $field["staffer_ismanage"]    = "是否技术账号";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '职工信息查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '职工信息查看失败', 'result' => $result);
        }
        return $res;
    }

    //查看职工资料建档
    function StafferDataApi($paramArray)
    {
        $stafferinfo = $this->DataControl->getFieldOne("smc_staffer_info","staffer_id","staffer_id = '{$paramArray['stafferOne_id']}'");
        if(!$stafferinfo){
            $data = array();
            $data['staffer_id'] = $paramArray['stafferOne_id'];
            $this->DataControl->insertData('smc_staffer_info',$data);
        }
        $sql = "
            SELECT
                s.info_birthday,
                s.info_idcardtype,
                s.info_idcard,
                s.info_img,
                s.info_emergcontactname,
                s.info_emergcontactphone,
                s.info_nation,
                s.info_nativeplace,
                s.info_education,
                s.info_schooltag,
                s.info_major,
                s.info_marital,
                s.info_address,
                ss.staffer_email,
                ss.staffer_sex
            FROM
                smc_staffer_info AS s
            LEFT JOIN
                smc_staffer AS ss ON ss.staffer_id = s.staffer_id
            WHERE
                s.staffer_id = '{$paramArray['stafferOne_id']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);
        if($stafferDetail){
            foreach ($stafferDetail as &$stafferVar){
                $stafferVar['info_img'] = $stafferVar['info_img']?$stafferVar['info_img'].'?x-oss-process=image/resize,m_lfit,w_300,limit_0/auto-orient,1/quality,q_90':'';
            }
        }

        $field = array();
        $field["info_birthday"] = "生日";
        $field["info_idcardtype"] = "证件类型";
        $field["info_idcard"] = "证件号";
        $field["info_img"] = "证件照";
        $field["info_emergcontactname"] = "紧急联系人";
        $field["info_emergcontactphone"] = "紧急联系电话";
        $field["info_nation"] = "民族";
        $field["info_nativeplace"] = "籍贯";
        $field["info_education"] = "最高学历";
        $field["info_schooltag"] = "毕业学校";
        $field["info_major"] = "所属专业";
        $field["info_marital"] = "婚姻状况";
        $field["info_address"] = "居住地址";
        $field["staffer_email"] = "职工邮箱";
            $field["staffer_sex"] = "性别";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '职工资料建档查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '职工资料建档查看失败', 'result' => $result);
        }
        return $res;
    }

    //查看职工集团职务
    function StafferCompanyPostApi($paramArray)
    {
        $sql = "
            SELECT
                p.postbe_id,
                c.post_name,
                p.staffer_id as stafferOne_id,
                c.post_type,
                p.organize_id,
                o.organize_cnname,
                l.postlevel_cnname,
                r.postrole_name,
                p.postrole_id,
                p.postlevel_id,
                p.postbe_ismianjob,
                p.postbe_ismianjob as postbe_ismianjob_status,
                p.postbe_isucsuser,
                p.postbe_isucsuser as postbe_isucsuser_status,
                p.postbe_ucsuserlevel,
                p.post_id,
                p.postbe_isgmccrm,
                p.postbe_gmccrmlevel 
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c on p.post_id = c.post_id
                left join gmc_company_organize as o on o.organize_id = p.organize_id
                left join gmc_company_postlevel as l on l.postlevel_id = p.postlevel_id
                left join gmc_company_postrole as r on p.postrole_id = r.postrole_id
            WHERE
                p.staffer_id = '{$paramArray['stafferOne_id']}' 
                AND c.post_type = 0
            ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        if($stafferDetail){
            $status=$this->LgArraySwitch(array("0"=>"否", "1"=>"是"));
            $mianjobstatus=$this->LgArraySwitch(array("0"=>"兼任", "1"=>"主职"));
            $type=$this->LgArraySwitch(array("1" => "校区客服", "2" => "校长", "3" => "集团客服", "4" => "集团高管"));
            $gmccrmtype=$this->LgArraySwitch(array("0" => "普通", "1" => "高管"));
            foreach($stafferDetail as &$val){
                $val['postbe_ismianjob_status']=$mianjobstatus[$val['postbe_ismianjob_status']];
                $val['postbe_isucsuser_status']=$status[$val['postbe_isucsuser_status']];
                $val['postbe_isgmccrm_status']=$status[$val['postbe_isgmccrm']];
                $val['postbe_gmccrmlevel_status']= ($val['postbe_isgmccrm'] == '1')?$gmccrmtype[$val['postbe_gmccrmlevel']]:'--';
                if($val['postbe_ucsuserlevel']){
                    $val['postbe_ucsuserlevel_name'] = $type[$val['postbe_ucsuserlevel']];
                }else{
                    $val['postbe_ucsuserlevel'] = '';
                    $val['postbe_ucsuserlevel_name'] = '';
                }
            }
        }


        $fieldstring = array('post_name ', 'organize_cnname', 'postrole_name', 'postbe_isucsuser_status', 'postbe_ucsuserlevel_name', 'postbe_ismianjob_status', 'postbe_ismianjob', 'postbe_isgmccrm_status', 'postbe_gmccrmlevel_status');
        $fieldname = $this->LgArraySwitch(array('职务名称', '组织名称', '角色', '客诉权限', '客诉角色', '是否主职', '是否主职', '集团招生权限', '集团招生角色'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "0", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "0", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($stafferDetail) {
            $result['list'] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无集团职务", 'result' => $result);
        }

        return $res;
    }

    //查看职工校园职务
    function StafferSchoolPostApi($paramArray)
    {
        $sql = "
            SELECT
                p.postbe_id,
                p.postbe_iscrmuser,
                p.postbe_iscrmuser as postbe_iscrmuser_status,
                p.postbe_isreceptionuser,
                p.postbe_isreceptionuser as postbe_isreceptionuser_status,
                p.staffer_id as stafferOne_id,
                c.post_name,
                c.post_type,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_id,
                o.organize_id,
                l.postlevel_cnname,
                pp.postpart_name,
                p.postpart_id,
                p.postlevel_id,
                p.postbe_ismianjob,
                p.postbe_ismianjob as postbe_ismianjob_status,
                p.postbe_crmuserlevel as postbe_crmuserlevel_status,
                p.postbe_crmuserlevel,
                p.postbe_isdefaultuser,
                p.postbe_isucsuser,
                p.postbe_isucsuser as postbe_isucsuser_status,
                p.postbe_ucsuserlevel,
                p.post_id
            FROM
                gmc_staffer_postbe AS p
                LEFT JOIN gmc_company_post AS c on p.post_id = c.post_id
                inner join smc_school as s on p.school_id = s.school_id
                left join smc_school_postpart as pp on pp.postpart_id = p.postpart_id
                left join gmc_company_organize as o on o.organize_id = p.organize_id
                left join gmc_company_postlevel as l on l.postlevel_id = p.postlevel_id
            WHERE
                p.staffer_id = '{$paramArray['stafferOne_id']}'
                AND c.post_type = 1
            ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        if ($stafferDetail) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $mianjobstatus=$this->LgArraySwitch(array("0"=>"跨校", "1"=>"主职"));
            $statuss = $this->LgArraySwitch(array("0" => "普通权限", "1" => "高管权限", "2" => "电销权限", "3" => "市场权限"));
            $type = $this->LgArraySwitch(array("0" => "没有", "1" => "有"));
            $types = $this->LgArraySwitch(array("1" => "校区客服", "2" => "校长", "3" => "集团客服", "4" => "集团高管"));
            foreach ($stafferDetail as &$val) {
                $val['postbe_isdefaultuser_status'] = $status[$val['postbe_isdefaultuser']];
                $val['postbe_iscrmuser_status'] = $status[$val['postbe_iscrmuser_status']];
                $val['postbe_ismianjob_status'] = $mianjobstatus[$val['postbe_ismianjob_status']];
                $val['postbe_isucsuser_status'] = $status[$val['postbe_isucsuser_status']];
                $val['postbe_crmuserlevel_status'] = $statuss[$val['postbe_crmuserlevel_status']];
                $val['postbe_isreceptionuser_status'] = $type[$val['postbe_isreceptionuser_status']];
                if($val['postbe_ucsuserlevel']){
                    $val['postbe_ucsuserlevel_name'] = $types[$val['postbe_ucsuserlevel']];
                }else{
                    $val['postbe_ucsuserlevel'] = '';
                    $val['postbe_ucsuserlevel_name'] = '';
                }
            }
        }

        $fieldstring = array('post_name', 'postpart_name', 'school_cnname', 'postbe_isucsuser_status', 'postbe_ucsuserlevel_name', 'postbe_iscrmuser_status', 'postbe_ismianjob_status', 'postbe_crmuserlevel_status', 'postbe_isreceptionuser_status','postbe_iscrmuser','postbe_crmuserlevel','postbe_isreceptionuser','postbe_isdefaultuser_status');
        $fieldname = $this->LgArraySwitch(array('职务名称', '学校角色', '校区名称', '客诉权限', '客诉角色', 'CRM权限', '是否主职', 'CRM角色', '接待权限', 'CRM权限', 'CRM角色', '接待权限', '是否默认接待人'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($stafferDetail) {
            $result['list'] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无校园职务", 'result' => $result);
        }

        return $res;
    }

    //职工管理 -- 添加职工集团职务
    function addStafferCompanyPostAction($paramArray)
    {
        $data = array();
        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe', 'postbe_id'
            , "staffer_id = '{$paramArray['stafferOne_id']}' and company_id = '{$paramArray['company_id']}' and school_id = '0' and postbe_ismianjob ='1'");
        if(!$postbeOne){
            $data['postbe_ismianjob'] = '1';
        }else{
            if($paramArray['postbe_ismianjob'] == '1'){
                $b = array();
                $b['postbe_ismianjob'] = '0';
                $this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$postbeOne['postbe_id']}'", $b);
                $data['postbe_ismianjob'] = '1';
            }else{
                $data['postbe_ismianjob'] = '0';
            }
        }

        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_id'] = $paramArray['stafferOne_id'];
        $data['post_id'] = $paramArray['post_id'];
        $data['organize_id'] = $paramArray['organize_id'];
//        $data['postlevel_id'] = $paramArray['postlevel_id'];
        $data['postrole_id'] = $paramArray['postrole_id'];
        $postroleOne = $this->DataControl->getFieldOne("gmc_company_postrole","postpart_id","postrole_id = '{$paramArray['postrole_id']}'");
        if($postroleOne){
            $data['postpart_id'] = $postroleOne['postpart_id'];
        }else{
            $data['postpart_id'] = '0';
        }
        $data['postbe_isucsuser'] = $paramArray['postbe_isucsuser'];
        $data['postbe_ucsuserlevel'] = $paramArray['postbe_ucsuserlevel'];
        $data['postbe_isgmccrm'] = $paramArray['postbe_isgmccrm'];
        $data['postbe_gmccrmlevel'] = $paramArray['postbe_gmccrmlevel'];
        $data['postbe_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属集团";
        $field['staffer_id'] = "职工id";
        $field['post_id'] = "职务id";
        $field['organize_id'] = "组织id";
//        $field['postlevel_id'] = "职等id";
        $field['postrole_id'] = "角色id";
        $field['postbe_ismianjob'] = "是否为主职";
        $field['postbe_isucsuser'] = "是否拥有客诉系统权限";
        $field['postbe_ucsuserlevel'] = "客诉权限级 1校区客服 2校长 3集团客服 4集团高管";
        $field['postbe_isgmccrm'] = "是拥有 集团招生权限  0 无  1 有";
        $field['postbe_gmccrmlevel'] = "集团招生权限  0 普通  1 高管 ";
        $field['postbe_createtime'] = "创建时间";

        if ($this->DataControl->insertData('gmc_staffer_postbe', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增职工集团职务成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'添加集团职务',dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '不可添加同职位任职信息', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 添加职工校园职务
    function addStafferSchoolPostAction($paramArray)
    {
        $a = $this->DataControl->getFieldquery('gmc_staffer_postbe', 'postbe_ismianjob,postbe_id', "staffer_id = '{$paramArray['stafferOne_id']}' and company_id = '{$paramArray['company_id']}' and school_id > '0' and postbe_ismianjob ='1'");

        $data = array();


        $b = $this->DataControl->selectClear("select count(postbe_id) as a from gmc_staffer_postbe as c WHERE c.staffer_id = '{$paramArray['stafferOne_id']}' and company_id = '{$paramArray['company_id']}' and school_id > '0' and postbe_ismianjob = 1");

        if($b[0]['a'] == '1'){
            if($paramArray['postbe_ismianjob'] == '1'){
                $b = array();
                $b['postbe_ismianjob'] = '0';
                $this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$a[0]['postbe_id']}'", $b);
                $data['postbe_ismianjob'] = '1';
            }else{
                $data['postbe_ismianjob'] = '0';
            }
        }else{
            $data['postbe_ismianjob'] = '1';
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_id'] = $paramArray['stafferOne_id'];
        $data['post_id'] = $paramArray['post_id'];
        $data['organize_id'] = $paramArray['organize_id'];
//        $data['postlevel_id'] = $paramArray['postlevel_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['postpart_id'] = $paramArray['postpart_id'];
        $data['postbe_iscrmuser'] = $paramArray['postbe_iscrmuser'];
        $data['postbe_ismianjob'] = $paramArray['postbe_ismianjob'];
        $data['postbe_crmuserlevel'] = $paramArray['postbe_crmuserlevel'];
        $data['postbe_isreceptionuser'] = $paramArray['postbe_isreceptionuser'];
        $data['postbe_isucsuser'] = $paramArray['postbe_isucsuser'];
        $data['postbe_ucsuserlevel'] = $paramArray['postbe_ucsuserlevel'];
        $data['postbe_isdefaultuser'] = $paramArray['postbe_isdefaultuser'];
        $data['postbe_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属集团";
        $field['staffer_id'] = "职工id";
        $field['post_id'] = "职务id";
        $field['organize_id'] = "组织id";
//        $field['postlevel_id'] = "职等id";
        $field['school_id'] = "学校id";
        $field['postpart_id'] = "角色id";
        $field['postbe_iscrmuser'] = "是否拥有CRM权限";
        $field['postbe_crmuserlevel'] = "CRM角色";
        $field['postbe_isreceptionuser'] = "接待权限";
        $field['postbe_isucsuser'] = "是否拥有客诉系统权限";
        $field['postbe_ucsuserlevel'] = "客诉权限级 1校区客服 2校长 3集团客服 4集团高管";
        $field['postbe_createtime'] = "创建时间";

        $a = $this->DataControl->getFieldOne("gmc_staffer_postbe","postbe_id","school_id = '{$paramArray['school_id']}' and staffer_id = '{$paramArray['stafferOne_id']}'");
        if($a){
            ajax_return(array('error' => 1,'errortip' => "职工在同一学校内只能担任一个职务!"));
        }

        if($paramArray['postbe_iscrmuser'] == '1'){
            $staffer = $this->DataControl->selectOne("select * from smc_staffer WHERE staffer_id = '{$paramArray['stafferOne_id']}'");
            $marketer = $this->DataControl->getFieldOne("crm_marketer","marketer_id","staffer_id = '{$paramArray['stafferOne_id']}'");
            if(!$marketer){
                $datas = array();
                $datas['company_id'] = $staffer['company_id'];
                $datas['staffer_id'] = $staffer['staffer_id'];
                $datas['postrole_id'] = $staffer['postrole_id'];
                $datas['marketer_istest'] = $staffer['staffer_istest'];
                $datas['marketer_name'] = $staffer['staffer_cnname'];
                $datas['marketer_img'] = $staffer['staffer_img'];
                $datas['marketer_mobile'] = $staffer['staffer_mobile'];
                $datas['marketer_tokencode'] = $staffer['staffer_tokencode'];
                $datas['marketer_tokenencrypt'] = $staffer['staffer_tokenencrypt'];
                $datas['marketer_lasttime'] = $staffer['staffer_lasttime'];
                $datas['marketer_lastip'] = $staffer['staffer_lastip'];
                $datas['marketer_createtime'] = $staffer['staffer_createtime'];
                $datas['marketer_status'] = '1';

                $this->DataControl->insertData('crm_marketer', $datas);
            }
        }

        if ($this->DataControl->insertData('gmc_staffer_postbe', $data)) {
            $this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'", array("staffer_updatetime"=>time()));
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工校园职务成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'添加校园职务',dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加职工校园职务失败', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 编辑职工集团职务
    function updateStafferCompanyPostAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postbe_id = '{$paramArray['postbe_id']}'");

        if ($stafferOne) {
            $data = array();
            $data['post_id'] = $paramArray['post_id'];
            $data['organize_id'] = $paramArray['organize_id'];
//            $data['postlevel_id'] = $paramArray['postlevel_id'];
            $data['postrole_id'] = $paramArray['postrole_id'];
            $data['postbe_isucsuser'] = $paramArray['postbe_isucsuser'];
            $data['postbe_ucsuserlevel'] = $paramArray['postbe_ucsuserlevel'];
            $data['postbe_isgmccrm'] = $paramArray['postbe_isgmccrm'];
            $data['postbe_gmccrmlevel'] = $paramArray['postbe_gmccrmlevel'];

            $b = $this->DataControl->selectClear("select count(postbe_id) as a from gmc_staffer_postbe as c WHERE c.staffer_id = '{$paramArray['stafferOne_id']}' and company_id = '{$paramArray['company_id']}' and school_id = 0 and postbe_ismianjob = 1");

            $c = $this->DataControl->getFieldOne('gmc_staffer_postbe','postbe_ismianjob',"postbe_id = '{$paramArray['postbe_id']}'");
            if($b[0]['a'] == '1'){
                if($paramArray['postbe_ismianjob'] == '1'){
                    if($c['postbe_ismianjob'] == '1'){
                        $data['postbe_ismianjob'] = '1';
                    }else{
                        $b = array();
                        $b['postbe_ismianjob'] = '0';
                        $d = $this->DataControl->getFieldOne('gmc_staffer_postbe','postbe_id',"postbe_ismianjob = '1' and company_id = '1001' and staffer_id = '{$paramArray['stafferOne_id']}' and school_id = 0");
                        $this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$d['postbe_id']}'", $b);
                        $data['postbe_ismianjob'] = '1';
                    }

                }else{
                    if($c['postbe_ismianjob'] == '1'){
                        ajax_return(array('error' => 1,'errortip' => "必须有主职"));
                    }else{
                        $data['postbe_ismianjob'] = '0';
                    }
                }
            }else{
                if($paramArray['postbe_ismianjob'] == '1'){
                    $data['postbe_ismianjob'] = '1';
                }else{
                    ajax_return(array('error' => 1,'errortip' => "必须有主职"));
                }
            }

            $field = array();
            $field['post_id'] = "职务id";
            $field['organize_id'] = "组织id";
//            $field['postlevel_id'] = "职等id";
            $field['postrole_id'] = "角色id";
            $field['postbe_ismianjob'] = "是否主职";
            $field['postbe_isucsuser'] = "是否拥有客诉系统权限";
            $field['postbe_ucsuserlevel'] = "客诉权限级 1校区客服 2校长 3集团客服 4集团高管";
            $field['postbe_isgmccrm'] = "是拥有 集团招生权限  0 无  1 有";
            $field['postbe_gmccrmlevel'] = "集团招生权限  0 普通  1 高管 ";
            if ($this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$paramArray['postbe_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工集团职务修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'编辑集团职务',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工集团职务修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工管理 -- 编辑职工校园职务
    function updateStafferSchoolPostAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "postbe_id = '{$paramArray['postbe_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['post_id'] = $paramArray['post_id'];
            $data['organize_id'] = $paramArray['organize_id'];
//            $data['postlevel_id'] = $paramArray['postlevel_id'];
            $data['school_id'] = $paramArray['school_id'];
            $data['postpart_id'] = $paramArray['postpart_id'];
            $data['postbe_iscrmuser'] = $paramArray['postbe_iscrmuser'];
            $data['postbe_crmuserlevel'] = $paramArray['postbe_crmuserlevel'];
            $data['postbe_isreceptionuser'] = $paramArray['postbe_isreceptionuser'];
            $data['postbe_isucsuser'] = $paramArray['postbe_isucsuser'];
            $data['postbe_ucsuserlevel'] = $paramArray['postbe_ucsuserlevel'];
            $data['postbe_isdefaultuser'] = $paramArray['postbe_isdefaultuser'];

            $b = $this->DataControl->selectClear("select count(postbe_id) as a from gmc_staffer_postbe as c WHERE c.staffer_id = '{$paramArray['stafferOne_id']}' and company_id = '{$paramArray['company_id']}' and school_id > '0' and postbe_ismianjob = 1");

            $c = $this->DataControl->getFieldOne('gmc_staffer_postbe','postbe_ismianjob',"postbe_id = '{$paramArray['postbe_id']}'");
            if($b[0]['a'] == '1'){
                if($paramArray['postbe_ismianjob'] == '1'){
                    if($c['postbe_ismianjob'] == '1'){
                        $data['postbe_ismianjob'] = '1';
                    }else{
                        $b = array();
                        $b['postbe_ismianjob'] = '0';
                        $d = $this->DataControl->getFieldOne('gmc_staffer_postbe','postbe_id',"postbe_ismianjob = '1' and company_id = '1001' and staffer_id = '{$paramArray['stafferOne_id']}' and school_id > '0'");
                        $this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$d['postbe_id']}'", $b);
                        $data['postbe_ismianjob'] = '1';
                    }

                }else{
                    if($c['postbe_ismianjob'] == '1'){
                        ajax_return(array('error' => 1,'errortip' => "必须有主职"));
                    }else{
                        $data['postbe_ismianjob'] = '0';
                    }
                }
            }else{
                if($paramArray['postbe_ismianjob'] == '1'){
                    $data['postbe_ismianjob'] = '1';
                }else{
                    ajax_return(array('error' => 1,'errortip' => "必须有主职"));
                }
            }

            $field = array();
            $field['post_id'] = "职务id";
            $field['organize_id'] = "组织id";
//            $field['postlevel_id'] = "职等id";
            $field['school_id'] = "学校id";
            $field['postpart_id'] = "角色id";
            $field['postbe_iscrmuser'] = "是否拥有CRM权限1可使用0不使用";
            $field['postbe_isreceptionuser'] = "接待权限";
            $field['postbe_crmuserlevel'] = "0普通权限1高管权限2电销权限3市场权限";
            $field['postbe_isucsuser'] = "是否拥有客诉系统权限";
            $field['postbe_ucsuserlevel'] = "客诉权限级 1校区客服 2校长 3集团客服 4集团高管";

            if ($this->DataControl->updateData("gmc_staffer_postbe", "postbe_id = '{$paramArray['postbe_id']}'", $data)) {
                $this->DataControl->updateData("smc_staffer", "staffer_id = '{$paramArray['stafferOne_id']}'", array("staffer_updatetime"=>time()));
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工校园职务修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'编辑校园职务',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工校园职务修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取学校
    function getSchoolApi($paramArray)
    {
        $result = array();
        $result['school'] = $this->DataControl->selectClear(
            "select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname from gmc_company_organizeschool as o 
                inner join smc_school as s on o.school_id = s.school_id 
                WHERE o.organize_id = '{$paramArray['organize_id']}'
                order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc");
        if ($result['school']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['school'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }


    //获取所属学校
    function findSchoolApi($paramArray)
    {
        $result = array();
        $result['school'] = $this->DataControl->selectClear("SELECT postpart_id,postpart_name,postpart_isucsuser,postpart_ucsuserlevel FROM smc_school_postpart WHERE company_id = '{$paramArray['company_id']}' and (school_id = '{$paramArray['school_id']}' or school_id = '0')");
        if ($result['school']) {
            foreach($result['school'] as &$item){
                if(!$item['postpart_ucsuserlevel']){
                    $item['postpart_ucsuserlevel'] = '';
                }
            }
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['school'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //职工管理 -- 添加职工资料建档
    function addStafferDataAction($paramArray)
    {
        $data = array();
        $data['staffer_id'] = $paramArray['stafferOne_id'];
        $data['info_birthday'] = $paramArray['info_birthday'];
        $data['info_idcardtype'] = $paramArray['info_idcardtype'];
        $data['info_idcard'] = $paramArray['info_idcard'];
        $data['info_emergcontactname'] = $paramArray['info_emergcontactname'];
        $data['info_emergcontactphone'] = $paramArray['info_emergcontactphone'];
        $data['info_nation'] = $paramArray['info_nation'];
        $data['info_nativeplace'] = $paramArray['info_nativeplace'];
        $data['info_education'] = $paramArray['info_education'];
        $data['info_schooltag'] = $paramArray['info_schooltag'];
        $data['info_major'] = $paramArray['info_major'];
        $data['info_marital'] = $paramArray['info_marital'];
        $data['info_address'] = $paramArray['info_address'];
        $data['staffer_email'] = $paramArray['staffer_email'];
        $data['info_createtime'] = time();

        $field = array();
        $field['staffer_id'] = "所属教师ID";
        $field['info_birthday'] = "生日";
        $field['info_idcardtype'] = "证件类型";
        $field['info_idcard'] = "证件号码";
        $field['info_emergcontactname'] = "紧急联系人";
        $field['info_emergcontactphone'] = "紧急联系电话";
        $field['info_nation'] = "民族";
        $field['info_nativeplace'] = "籍贯";
        $field['info_education'] = "最高学历";
        $field['info_schooltag'] = "毕业学校";
        $field['info_major'] = "所属专业";
        $field['info_marital'] = "婚姻状况";
        $field['info_address'] = "居住地址";
        $field['staffer_email'] = "职工邮箱";
        $field['info_createtime'] = "创建时间";

        $this->DataControl->insertData('smc_staffer_info', $data);
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工资料建档成功", 'result' => $result);
        $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'添加职工资料建档',dataEncode($paramArray));
        return $res;
    }


    //职工管理 -- 修改职工建档
    function updateStafferDataAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer_info", "staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['staffer_id'] = $paramArray['stafferOne_id'];
            $data['info_birthday'] = $paramArray['info_birthday'];
            $data['info_idcardtype'] = $paramArray['info_idcardtype'];
            $data['info_idcard'] = $paramArray['info_idcard'];
            $data['info_emergcontactname'] = $paramArray['info_emergcontactname'];
            $data['info_emergcontactphone'] = $paramArray['info_emergcontactphone'];
            $data['info_nation'] = $paramArray['info_nation'];
            $data['info_nativeplace'] = $paramArray['info_nativeplace'];
            $data['info_education'] = $paramArray['info_education'];
            $data['info_schooltag'] = $paramArray['info_schooltag'];
            $data['info_major'] = $paramArray['info_major'];
            $data['info_marital'] = $paramArray['info_marital'];
            $data['info_address'] = $paramArray['info_address'];
            $data['info_img'] = $paramArray['info_img'];
            $data['info_updatetime'] = time();

            $field = array();
            $field['staffer_id'] = "所属教师ID";
            $field['info_birthday'] = "生日";
            $field['info_idcardtype'] = "证件类型";
            $field['info_idcard'] = "证件号码";
            $field['info_emergcontactname'] = "紧急联系人";
            $field['info_emergcontactphone'] = "紧急联系电话";
            $field['info_nation'] = "民族";
            $field['info_nativeplace'] = "籍贯";
            $field['info_education'] = "最高学历";
            $field['info_schooltag'] = "毕业学校";
            $field['info_major'] = "所属专业";
            $field['info_marital'] = "婚姻状况";
            $field['info_address'] = "居住地址";
            $field['info_img'] = "头像";
            $field['info_updatetime'] = "修改时间";
            if ($this->DataControl->updateData("smc_staffer_info", "staffer_id = '{$paramArray['stafferOne_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "职工资料建档修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '职工资料建档修改失败', 'result' => $result);
            }
        } else {
            $data = array();
            $data['staffer_id'] = $paramArray['stafferOne_id'];
            $data['info_birthday'] = $paramArray['info_birthday'];
            $data['info_idcardtype'] = $paramArray['info_idcardtype'];
            $data['info_idcard'] = $paramArray['info_idcard'];
            $data['info_emergcontactname'] = $paramArray['info_emergcontactname'];
            $data['info_emergcontactphone'] = $paramArray['info_emergcontactphone'];
            $data['info_nation'] = $paramArray['info_nation'];
            $data['info_nativeplace'] = $paramArray['info_nativeplace'];
            $data['info_education'] = $paramArray['info_education'];
            $data['info_schooltag'] = $paramArray['info_schooltag'];
            $data['info_major'] = $paramArray['info_major'];
            $data['info_marital'] = $paramArray['info_marital'];
            $data['info_address'] = $paramArray['info_address'];
            $data['staffer_email'] = $paramArray['staffer_email'];
            $data['info_img'] = $paramArray['info_img'];
            $data['info_createtime'] = time();

            $field = array();
            $field['staffer_id'] = "所属教师ID";
            $field['info_birthday'] = "生日";
            $field['info_idcardtype'] = "证件类型";
            $field['info_idcard'] = "证件号码";
            $field['info_emergcontactname'] = "紧急联系人";
            $field['info_emergcontactphone'] = "紧急联系电话";
            $field['info_nation'] = "民族";
            $field['info_nativeplace'] = "籍贯";
            $field['info_education'] = "最高学历";
            $field['info_schooltag'] = "毕业学校";
            $field['info_major'] = "所属专业";
            $field['info_marital'] = "婚姻状况";
            $field['info_address'] = "居住地址";
            $field['staffer_email'] = "职工邮箱";
            $field['info_img'] = "头像";
            $field['info_createtime'] = "创建时间";

            $this->DataControl->insertData('smc_staffer_info', $data);
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加职工资料建档成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'编辑职工资料建档',dataEncode($paramArray));
        }
        return $res;
    }

    //职工离职
    function updateLeaveAction($paramArray){
        $TeachingtypeOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['staffer_leave'] = '1';
            $data['staffer_leavetime'] = $paramArray['staffer_leavetime'];
            $data['staffer_leavecause'] = $paramArray['staffer_leavecause'];
            $data['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_leavetime'] = "离职时间";
            $field['staffer_leavecause'] = "离职原因";
            if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['stafferOne_id']}'",$data) ) {
                //暂时关闭功能
                //$Model = new \Model\Crm\IntentionClientModel();
                //$Model ->IntentionToClientApi($TeachingtypeOne['staffer_id'],$paramArray['company_id']);

                $postbeList = $this->DataControl->selectClear("SELECT postbe_id FROM gmc_staffer_postbe WHERE staffer_id = '{$paramArray['stafferOne_id']}'");
                if($postbeList){
                    foreach ($postbeList as $val) {
                        $this->DataControl->delData("gmc_staffer_postbe", "postbe_id = '{$val['postbe_id']}'");
                    }
                }

                $data = array();
                $data['company_id'] = $paramArray['company_id'];
                $data['staffer_id'] = $paramArray['stafferOne_id'];
                $data['workchange_code'] = 'Z02';
                $data['postchangeslog_note'] = "集团给教师做离职操作，进行离职清算操作";
                $data['postchangeslog_day'] = date("Y-m-d",time());
                $data['postchangeslog_createtime'] = time();
                $this->DataControl->insertData("gmc_staffer_postchangeslog",$data);

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "离职成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'离职',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '离职失败', 'result' => $result);
            }
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //职工复职
    function updateJoinAction($paramArray){
        $TeachingtypeOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id", "staffer_id = '{$paramArray['stafferOne_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['staffer_leave'] = '0';
            $data['staffer_jointime'] = $paramArray['staffer_jointime'];
            $data['staffer_updatetime'] = time();

            $field = array();
            $field['staffer_jointime'] = "入职时间";

            if ($this->DataControl->updateData("smc_staffer","staffer_id = '{$paramArray['stafferOne_id']}'",$data) ) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "复职成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团架构->职工管理",'复职',dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '复职失败', 'result' => $result);
            }
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //首页 -- 某日的日程安排
    function eventOneApi($paramArray)
    {
        $datawhere = " 1 and e.event_time = '{$paramArray['event_time']}' and e.staffer_id = '{$paramArray['staffer_id']}' ";
        $sql = "SELECT  e.event_tag,e.event_remark
                FROM gmc_event as e
                WHERE {$datawhere}";
        $dataList = $this->DataControl->selectClear($sql);
        return $dataList;
    }

    //首页 -- 月份列表日程展示
    function monthEventApi($paramArray)
    {
        $date = getthemonth($paramArray['yearMonth']);
        //当前日期
        $sdefaultDate = date("Y-m-d");

        $sql = "select event_id,event_time
                from gmc_event 
                where staffer_id = '{$paramArray['staffer_id']}'  and event_time between '{$date[0]}' and '{$date[1]}' 
                GROUP BY event_time";
        $mothListArray = $this->DataControl->selectClear($sql);

        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['event_time']));
                $v['month'] = date('m', strtotime($v['event_time']));
                $v['day'] = date('d', strtotime($v['event_time']));
                $v['week'] = date('w', strtotime($v['year'] - $v['month'] - $v['day']));
                unset($mothListArray[$k]['event_time']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = $i;
                    $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                    if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                        $dateWeek[$i]['isnow'] = 1;
                    } else {
                        $dateWeek[$i]['isnow'] = 0;
                    }

                    $data['is_have'] = strval(-1);
                    array_push($mothListArray, $data);
                }
                usort($mothListArray, function ($a, $b) {
                    if ($a['day'] == $b['day']) return 0;
                    return $a['day'] > $b['day'] ? 1 : -1;
                });
            }
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = $i;
                $data['week'] = date('w', strtotime($data['year'] . "-" . $data['month'] . "-" . $data['day']));

                if (($data['year'] . "-" . $data['month'] . "-" . $data['day']) == $sdefaultDate) {
                    $dateWeek[$i]['isnow'] = 1;
                } else {
                    $dateWeek[$i]['isnow'] = 0;
                }

                $data['is_have'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first = 0;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w = date('w', strtotime($sdefaultDate));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start = date('Y-m-d', strtotime("$sdefaultDate -" . ($w ? $w - $first : 6) . ' days'));
        //本周结束日期
        $week_end = date('Y-m-d', strtotime("$week_start +6 days"));
        //echo "$week_start"."$week_end";
        //组合数据
        $dateWeek = [];
        for ($i = 0; $i <= 6; $i++) {
            $dateWeek[$i]['yearMonth'] = date('Y-m', strtotime("$week_start + $i days"));
            $dateWeek[$i]['daytime'] = date('Y-m-d', strtotime("$week_start + $i days"));
            $dateWeek[$i]['day'] = date('d', strtotime("$week_start + $i days"));
            $dateWeek[$i]['week'] = date('w', strtotime("$week_start + $i days"));
            if ($dateWeek[$i]['daytime'] == $sdefaultDate) {
                $dateWeek[$i]['isnow'] = 1;
            } else {
                $dateWeek[$i]['isnow'] = 0;
            }
        }

        $data = array();
        $data['week'] = $dateWeek;
        $data['mothList'] = $mothListArray;
        return $data;
    }

    //首页 -- 新增日程安排
    function addEventAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['event_tag'] = $paramArray['event_tag'];
        $data['event_time'] = $paramArray['event_time'];
        $data['event_remark'] = $paramArray['event_remark'];
        $data['event_createtime'] = time();

        if ($this->DataControl->insertData('gmc_event', $data)) {
            return true;
        } else {
            return false;
        }
    }

    //根据所选学校获取组织列表
    function getSchoolOrganizeApi($paramArray)
    {
        $result = array();
        if($paramArray['school_id']){
            $result['organize'] = $this->DataControl->selectClear("
            SELECT
                o.organize_id,
                o.organize_cnname 
            FROM
                gmc_company_organize AS o
                LEFT JOIN gmc_company_organizeschool AS os on o.organize_id = os.organize_id
                where os.school_id = '{$paramArray['school_id']}'");
        }else{
            $result['organize'] = $this->DataControl->selectClear("
            SELECT
                o.organize_id,
                o.organize_cnname 
            FROM
                gmc_company_organize AS o
                where o.company_id = '{$paramArray['company_id']}'");
        }

        if ($result['organize']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['organize'] = array();
            $res = array('error' => '1', 'errortip' => "获取失败", 'result' => $result);
        }

        return $res;
    }

    //查看职工对应的 容联七陌 的账号密码
    function getGmcCrmStafferOneApi($paramArray)
    {
        $markOne = $this->DataControl->getFieldOne('crm_marketer', 'staffer_id,marketer_id,marketer_name,marketer_outworknumber,marketer_outworkpasswd', "staffer_id='{$paramArray['staffer_id']}'");
        if (!$markOne) {
            $markOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
        }

        $field = array();
        $field["staffer_id"] = "职工id";
        $field["marketer_id"] = "CRM对应的职工ID";
        $field["marketer_name"] = "CRM对应的职工名称";
        $field["marketer_outworknumber"] = "电话外呼工号";
        $field["marketer_outworkpasswd"] = "电话外呼密码";

        $result = array();
        if ($markOne) {
            $result["field"] = $field;
            $result["data"] = $markOne;
            $res = array('error' => '0', 'errortip' => '职工资料建档查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '职工资料建档查看失败', 'result' => $result);
        }
        return $res;
    }

    //修改 容联七陌 的账号密码
    function updateGmcCrmStafferOneApi($paramArray)
    {
        $markOne = $this->DataControl->getFieldOne('crm_marketer', 'staffer_id,marketer_id,marketer_name,marketer_outworknumber,marketer_outworkpasswd', "staffer_id='{$paramArray['staffer_id']}'");
        if (!$markOne) {
            $markOne = $this->addStaffMarketerOne($paramArray['staffer_id']);
        }
        $data = array();
        $data['marketer_outworknumber'] = $paramArray['marketer_outworknumber'];
        $data['marketer_outworkpasswd'] = $paramArray['marketer_outworkpasswd'];
        if ($this->DataControl->updateData("crm_marketer", "marketer_id = '{$markOne['marketer_id']}' and company_id = '{$paramArray['company_id']}' ", $data)) {
            $result = array();
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "职工信息修改成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'],$paramArray['staffer_id'],"集团招生管理",'修改职工容联七陌账号密码信息',dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '职工信息修改失败', 'result' => $result);
        }

        return $res;
    }
}
