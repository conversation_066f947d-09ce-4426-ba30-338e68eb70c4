<?php


namespace Model\Gmc;

class RegistrationModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getSettleInfoList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' or st.student_id in (select fa.student_id from smc_student_family as fa where fa.family_mobile like '%{$request['keyword']}%'))";
        }

        if (isset($request['class_id']) && $request['class_id'] !== '') {
            $datawhere .= " and cl.class_id = '{$request['class_id']}'";
        }

        if (isset($request['share_status']) && $request['share_status'] !== '') {
            $datawhere .= " and cs.share_status = '{$request['share_status']}'";
        }

        if (isset($request['share_month']) && $request['share_month'] !== '') {
            $datawhere .= " and cs.share_month = '{$request['share_month']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and cs.school_id = '{$request['school_id']}'";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and cs.share_apply_time >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'] . '23:59:59');
            $datawhere .= " and cs.share_apply_time <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and cs.share_audit_time >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . '23:59:59');
            $datawhere .= " and cs.share_audit_time <= '{$firstday}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cs.share_id,sc.school_id,sc.school_cnname,sc.school_branch,st.student_id,st.student_cnname,st.student_enname,st.student_branch,cl.class_cnname,cl.class_branch,cs.share_month,cs.share_attend_times,cs.share_absent_times,cs.share_settle_price,cs.share_calc_price,cs.share_apply_price,cs.share_status,cs.share_confirm_price,sc.school_periodauthority,cs.share_apply_time,cs.share_audit_time
              from smc_student_class_share as cs
              left join smc_student as st on st.student_id=cs.student_id
              left join smc_class as cl on cl.class_id=cs.class_id
              left join smc_school as sc on sc.school_id=cs.school_id
              where {$datawhere} and cs.company_id='{$this->company_id}'
              order by cs.share_apply_time desc,cs.share_id asc
              ";

        $status = $this->LgArraySwitch(array('0' => '待处理', '1' => '已完成'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_enname'] = $dateexcelvar['student_enname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                $datearray['class_enname'] = $dateexcelvar['class_enname'];
                $datearray['class_branch'] = $dateexcelvar['class_branch'];
                $datearray['share_month'] = $dateexcelvar['share_month'];
                $datearray['should_times'] = $dateexcelvar['share_attend_times'] + $dateexcelvar['share_absent_times'];
                $datearray['share_attend_times'] = $dateexcelvar['share_attend_times'];
                $datearray['share_absent_times'] = $dateexcelvar['share_absent_times'];
                $datearray['share_settle_price'] = $dateexcelvar['share_settle_price'];

                if ($dateexcelvar['share_status'] == 0) {
                    $datearray['price'] = $dateexcelvar['share_apply_price'];
                    $datearray['surplus_price'] = $dateexcelvar['share_settle_price'] - $dateexcelvar['share_apply_price'];
                } else {

                    $datearray['price'] = $dateexcelvar['share_confirm_price'];
                    $datearray['surplus_price'] = $dateexcelvar['share_settle_price'] - $dateexcelvar['share_confirm_price'];
                }
                $datearray['share_status_name'] = $status[$dateexcelvar['share_status']];
                $datearray['share_apply_time'] = date("Y-m-d", $dateexcelvar['share_apply_time']);
                $datearray['share_audit_time'] = $dateexcelvar['share_audit_time'] ? date("Y-m-d", $dateexcelvar['share_audit_time']) : '--';

                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "学员中文名", "学员英文名", "学员编号", "班级名称", "班级别名", "班级编号", "结算月份", "应到", "实到", "缺勤", "原分摊金额", "实际分摊金额", "剩余金额", "处理状态", "申请时间", "审核时间"));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'share_month', 'should_times', 'share_attend_times', 'share_absent_times', 'share_settle_price', 'price', 'surplus_price', 'share_status_name', 'share_apply_time', 'share_audit_time');

            $tem_name = "月度结算审核列表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $stuList = $this->DataControl->selectClear($sql);

            if (!$stuList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($stuList as &$stuOne) {
                $stuOne['should_times'] = $stuOne['share_attend_times'] + $stuOne['share_absent_times'];
                if ($stuOne['share_status'] == 0) {
                    $stuOne['surplus_price'] = $stuOne['share_settle_price'] - $stuOne['share_apply_price'];
                    $stuOne['price'] = $stuOne['share_apply_price'];
                } else {
                    $stuOne['surplus_price'] = $stuOne['share_settle_price'] - $stuOne['share_confirm_price'];
                    $stuOne['price'] = $stuOne['share_confirm_price'];
                }
                $stuOne['share_status_name'] = $status[$stuOne['share_status']];
                $stuOne['share_apply_time'] = date("Y-m-d", $stuOne['share_apply_time']);
                if ($stuOne['share_audit_time']) {
                    $stuOne['share_audit_time'] = date("Y-m-d", $stuOne['share_audit_time']);
                } else {
                    $stuOne['share_audit_time'] = '--';
                }
            }

            $data = array();

            $count_sql = "select cs.share_id
              from smc_student_class_share as cs
              left join smc_student as st on st.student_id=cs.student_id
              left join smc_class as cl on cl.class_id=cs.class_id
              where {$datawhere} and cs.company_id='{$this->company_id}'";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;

            $data['list'] = $stuList;
            return $data;
        }
    }

    function confirmSettle($request)
    {

        $shareOne = $this->DataControl->getFieldOne("smc_student_class_share", "student_id,class_id,share_month,share_status,share_apply_price,school_id,share_from,share_settle_price,courseshare_id", "share_id='{$request['share_id']}'");

        if (!$shareOne || $shareOne['share_status'] != 0) {
            $this->error = true;
            $this->errortip = "该分摊不可处理";
            return false;
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_periodauthority", "school_id='{$shareOne['school_id']}'");

        if ($schoolOne['school_periodauthority'] == 1) {
            $this->error = true;
            $this->errortip = "无期度权限";
            return false;
        }

        $request['school_id'] = $shareOne['school_id'];
        $BalanceModel = new \Model\Smc\BalanceModel($request);

        if (!isset($request['confirm_price']) || $request['confirm_price'] == '') {
            $request['confirm_price'] = $shareOne['share_apply_price'];
        }

        $sql = "select scb.coursebalance_id,scb.coursebalance_figure,scb.coursebalance_time
              from smc_student_courseshare as sc
              left join smc_student_coursebalance as scb on sc.coursebalance_id=scb.coursebalance_id
              where sc.courseshare_id='{$shareOne['courseshare_id']}'
              ";

        $stuCourseBalanceOne = $this->DataControl->selectOne($sql);


        if ($stuCourseBalanceOne['coursebalance_figure'] < $request['confirm_price']) {
            $this->error = true;
            $this->errortip = "确认金额超出上限";
            return false;
        }

        if ($shareOne['share_from'] == 0) {
            $status = 1;
        } else {
            $status = 2;
        }

        $BalanceModel->settleAction($shareOne['student_id'], $shareOne['class_id'], $shareOne['share_month'], ($shareOne['share_settle_price'] - $request['confirm_price']), $status, $request['confirm_price']);

        $data = array();

        $data['share_audit_price'] = $request['confirm_price'];
        $data['share_audit_staffer_id'] = $this->stafferOne['staffer_id'];
        $data['share_audit_time'] = time();

        $data['share_confirm_price'] = $request['confirm_price'];
        $data['share_confirm_staffer_id'] = $this->stafferOne['staffer_id'];
        $data['share_confirm_time'] = time();
        $data['share_status'] = 1;
        $this->DataControl->updateData("smc_student_class_share", "share_id='{$request['share_id']}'", $data);

        $data = array();
        $data['share_id'] = $request['share_id'];
        $data['tracks_title'] = $this->LgStringSwitch('结算处理');
        $data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师处理结算信息');
        $data['tracks_note'] = $this->LgStringSwitch($request['note']);
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $data['tracks_time'] = time();

        $this->DataControl->insertData("smc_student_class_share_tracks", $data);

        return true;
    }

    function settleClassList($request)
    {

        $datawhere = " 1 ";
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and cs.school_id='{$request['school_id']}'";
        }

        $sql = "select cl.class_id,cl.class_cnname,cl.class_branch
              from smc_student_class_share as cs
              left join smc_class as cl on cl.class_id=cs.class_id
              where {$datawhere} and cs.company_id='{$this->company_id}'
              group by cs.class_id
              order by cl.class_createtime desc,cl.class_id desc
              ";
        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = true;
            $this->errortip = "无班级列表数据";
            return false;
        }

        return $classList;

    }

    function getSettleOne($request)
    {
        $sql = "select s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex,cl.class_id,cl.class_cnname,cl.class_enname,cl.class_branch
              ,cs.share_status,cs.share_month,cs.share_attend_times,cs.share_absent_times,cs.share_calc_times,cs.share_settle_price,cs.share_confirm_price,cs.share_apply_price
              ,co.course_cnname,co.course_branch,sc.school_cnname,sc.school_branch
              ,(select scb.coursebalance_figure from smc_student_coursebalance as scb where scb.student_id=cs.student_id and scb.school_id=cl.school_id and scb.course_id=cl.course_id limit 0,1) as coursebalance_figure
              from smc_student_class_share as cs
              left join smc_student as s on s.student_id=cs.student_id
              left join smc_class as cl on cl.class_id=cs.class_id
              left join smc_course as co on co.course_id=cl.course_id
              left join smc_school as sc on sc.school_id=cl.school_id
              where cs.share_id='{$request['share_id']}'
              ";
        $shareOne = $this->DataControl->selectOne($sql);

        if (!$shareOne) {
            $this->error = true;
            $this->errortip = "无对应申请分摊记录";
            return false;
        }

        $sql = "select sum(s.share_apply_price) as price from smc_student_class_share as s where s.student_id='{$shareOne['student_id']}' and s.class_id='{$shareOne['class_id']}' and s.share_id<>'{$request['share_id']}' and s.share_status=0";
        $otherOne = $this->DataControl->selectOne($sql);

        if ($otherOne) {
            $shareOne['coursebalance_figure'] -= $otherOne['price'];
        }


        $status = $this->LgArraySwitch(array('0' => '待处理', '1' => '已完成'));

        $stuInfo = array();
        $stuInfo['student_cnname'] = $shareOne['student_cnname'];
        $stuInfo['student_enname'] = $shareOne['student_enname'];
        $stuInfo['student_branch'] = $shareOne['student_branch'];
        $stuInfo['student_img'] = $shareOne['student_img'];
        $stuInfo['student_sex'] = $shareOne['student_sex'];
        $stuInfo['share_status_name'] = $status[$shareOne['share_status']];
        $stuInfo['share_status'] = $shareOne['share_status'];

        $classInfo = array();
        $classInfo['school_cnname'] = $shareOne['school_cnname'];
        $classInfo['school_branch'] = $shareOne['school_branch'];
        $classInfo['class_cnname'] = $shareOne['class_cnname'];
        $classInfo['class_enname'] = $shareOne['class_enname'];
        $classInfo['class_branch'] = $shareOne['class_branch'];
        $classInfo['course_cnname'] = $shareOne['course_cnname'];
        $classInfo['course_branch'] = $shareOne['course_branch'];

        $clField = array();

        $k = 0;
        $clField[$k]["fieldstring"] = 'school_cnname';
        $clField[$k]["fieldname"] = '校区名称';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;

        $clField[$k]["fieldstring"] = 'school_branch';
        $clField[$k]["fieldname"] = '校区编号';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;

        $clField[$k]["fieldstring"] = 'class_cnname';
        $clField[$k]["fieldname"] = '班级名称';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;

        $clField[$k]["fieldstring"] = 'class_enname';
        $clField[$k]["fieldname"] = '班级别名';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;

        $clField[$k]["fieldstring"] = 'class_branch';
        $clField[$k]["fieldname"] = '班级编号';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;

        $clField[$k]["fieldstring"] = 'course_cnname';
        $clField[$k]["fieldname"] = '课程别名称';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;

        $clField[$k]["fieldstring"] = 'course_branch';
        $clField[$k]["fieldname"] = '课程别编号';
        $clField[$k]["custom"] = 1;
        $clField[$k]["show"] = 1;
        $k++;


        $settleInfo = array();
        $settleInfo['share_month'] = $shareOne['share_month'];
        $settleInfo['share_attend_times'] = $shareOne['share_attend_times'];
        $settleInfo['share_absent_times'] = $shareOne['share_absent_times'];
        $settleInfo['share_all_times'] = $shareOne['share_attend_times'] + $shareOne['share_absent_times'];
        $settleInfo['share_calc_times'] = $shareOne['share_calc_times'];
        $settleInfo['share_settle_price'] = $shareOne['share_settle_price'];
        $settleInfo['share_price'] = $shareOne['share_confirm_price'] > 0 ? $shareOne['share_confirm_price'] : $shareOne['share_apply_price'];
        $settleInfo['share_surplus_price'] = $shareOne['share_settle_price'] - $settleInfo['share_price'];
        $settleInfo['coursebalance_figure'] = $shareOne['coursebalance_figure'];

        $seField = array();

        $k = 0;
        $seField[$k]["fieldstring"] = 'share_month';
        $seField[$k]["fieldname"] = '结算月份';
        $seField[$k]["custom"] = 1;
        $seField[$k]["show"] = 1;
        $k++;

        $seField[$k]["fieldstring"] = 'share_all_times';
        $seField[$k]["fieldname"] = '应到';
        $seField[$k]["custom"] = 1;
        $seField[$k]["show"] = 1;
        $k++;

        $seField[$k]["fieldstring"] = 'share_attend_times';
        $seField[$k]["fieldname"] = '实到';
        $seField[$k]["custom"] = 1;
        $seField[$k]["show"] = 1;
        $k++;

        $seField[$k]["fieldstring"] = 'share_absent_times';
        $seField[$k]["fieldname"] = '缺勤';
        $seField[$k]["custom"] = 1;
        $seField[$k]["show"] = 1;
        $k++;

        $seField[$k]["fieldstring"] = 'share_settle_price';
        $seField[$k]["fieldname"] = '原分摊金额';
        $seField[$k]["custom"] = 1;
        $seField[$k]["show"] = 1;
        $k++;

        $seField[$k]["fieldstring"] = 'share_price';
        $seField[$k]["fieldname"] = '实际分摊金额';
        $seField[$k]["custom"] = 1;
        $seField[$k]["show"] = 1;
        $k++;

        $sql = "select st.tracks_title,st.tracks_playname,st.tracks_information,st.tracks_note,FROM_UNIXTIME(st.tracks_time, '%Y-%m-%d %H:%i:%s' ) as tracks_time
              from smc_student_class_share_tracks as st where st.share_id='{$request['share_id']}'
              order by st.tracks_id asc";

        $trackList = $this->DataControl->selectClear($sql);
        if (!$trackList) {
            $trackList = array();
        }

        $data = array();
        $data['stuInfo'] = $stuInfo;

        $data['classInfo'][] = $classInfo;
        $data['clField'] = $clField;

        $data['settleInfo'][] = $settleInfo;
        $data['seField'] = $seField;

        $data['trackList'] = $trackList;

        return $data;

    }


    function getschoolBreakoff($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(b.breakoff_apply_time,'%Y-%m-%d' ) >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(b.breakoff_apply_time,'%Y-%m-%d' ) <= '{$request['endtime']}'";
        }

        if (isset($request['startday']) && $request['startday'] !== '') {
            $datawhere .= " and c.class_enddate >= '{$request['startday']}'";
        }
        if (isset($request['endday']) && $request['endday'] !== '') {
            $datawhere .= " and c.class_enddate <= '{$request['endday']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and co.course_id = '{$request['course_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id = '{$request['school_id']}'";
        }

        if (isset($request['class_status']) && $request['class_status'] !== '') {
            $datawhere .= " and c.class_status = '{$request['class_status']}'";
        }

        if (isset($request['breakoff_type']) && $request['breakoff_type'] !== '') {
            $datawhere .= " and b.breakoff_type = '{$request['breakoff_type']}'";
        }

        if (isset($request['breakoff_status']) && $request['breakoff_status'] !== '') {
            $datawhere .= " and b.breakoff_status = '{$request['breakoff_status']}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and b.breakoff_audit_time >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . '23:59:59');
            $datawhere .= " and b.breakoff_audit_time <= '{$firstday}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select b.breakoff_id,sch.school_cnname,sch.school_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,b.breakoff_type,c.class_fullnums,st.staffer_cnname,b.breakoff_status,co.course_cnname,co.course_branch,c.class_enddate,c.class_status,b.breakoff_apply_time,b.breakoff_audit_time
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=b.class_id and ss.study_isreading=1 limit 0,1) as stuNum
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=b.class_id and ch.hour_ischecking<>'-1') as allHourNum
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=b.class_id and ch.hour_ischecking='1') as hasHourNum
              ,(select group_concat(distinct s.staffer_cnname) from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where ct.class_id=b.class_id and ct.teach_type=0 and ct.teach_status=0) as teacher_name
              ,(select bt.tracks_note from smc_class_breakoff_track as bt where bt.breakoff_id=b.breakoff_id order by bt.tracks_time asc,bt.tracks_id asc limit 0,1) as reason
              from smc_class_breakoff as b 
              left join smc_class as c on c.class_id=b.class_id
              left join smc_staffer as st on st.staffer_id=b.breakoff_apply_staffer_id  
              left join smc_course as co on co.course_id=c.course_id    
              left join smc_school as sch on sch.school_id=b.school_id
              where {$datawhere} and b.company_id='{$this->company_id}'
              order by b.school_id asc,b.breakoff_apply_time desc
              ";

        $type = $this->LgArraySwitch(array("0" => "中途拆班", "1" => "期末拆班"));
        $status = $this->LgArraySwitch(array("0" => "待校区审核", "1" => "待集团审核", "2" => "集团审核通过", "3" => "完成", "-1" => "校区审核拒绝", "-2" => "集团审核拒绝"));

        $class_status = $this->LgArraySwitch(array("0" => "待开班", "1" => "进行中", "-1" => "已结束", "-2" => "已删除"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                $datearray['course_branch'] = $dateexcelvar['course_branch'];
                $datearray['class_enname'] = $dateexcelvar['class_enname'];
                $datearray['class_branch'] = $dateexcelvar['class_branch'];
                $datearray['number'] = $dateexcelvar['stuNum'] . '/' . $dateexcelvar['class_fullnums'];
                $datearray['class_status_name'] = $class_status[$dateexcelvar['class_status']];
                $datearray['progress'] = $dateexcelvar['hasHourNum'] . '/' . $dateexcelvar['allHourNum'];
                $datearray['class_enddate'] = $dateexcelvar['class_enddate'];
                $datearray['teacher_name'] = $dateexcelvar['teacher_name'];
                $datearray['breakoff_type_name'] = $type[$dateexcelvar['breakoff_type']];
                $datearray['reason'] = $dateexcelvar['reason'];
                $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                $datearray['breakoff_status_name'] = $status[$dateexcelvar['breakoff_status']];
                $datearray['breakoff_apply_time'] = date("Y-m-d", $dateexcelvar['breakoff_apply_time']);
                if ($dateexcelvar['breakoff_audit_time']) {
                    $datearray['breakoff_audit_time'] = date("Y-m-d", $dateexcelvar['breakoff_audit_time']);
                } else {
                    $datearray['breakoff_audit_time'] = '--';
                }

                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "课程别名称", "课程别编号", "班级别名", "班级编号", "班级人数", "班级状态", "上课进度", "结班日期", "主教老师", "结班类型", "中途结班原因", "申请人", "审核状态", "申请时间", "审核时间"));
            $excelfileds = array('school_cnname', 'school_branch', 'class_cnname', 'course_cnname', 'course_branch', 'class_enname', 'class_branch', 'number', 'class_status_name', 'progress', 'class_enddate', 'teacher_name', 'breakoff_type_name', 'reason', 'staffer_cnname', 'breakoff_status_name', 'breakoff_apply_time', 'breakoff_audit_time');

            $tem_name = "申请拆班列表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $classList = $this->DataControl->selectClear($sql);

            if (!$classList) {
                $this->error = true;
                $this->errortip = "无申请数据数据";
                return false;
            }

            foreach ($classList as &$classOne) {
                $classOne['number'] = $classOne['stuNum'] . '/' . $classOne['class_fullnums'];
                $classOne['class_status_name'] = $class_status[$classOne['class_status']];
                $classOne['progress'] = $classOne['hasHourNum'] . '/' . $classOne['allHourNum'];
                $classOne['breakoff_type_name'] = $type[$classOne['breakoff_type']];
                $classOne['breakoff_status_name'] = $status[$classOne['breakoff_status']];
                $classOne['breakoff_apply_time'] = date("Y-m-d", $classOne['breakoff_apply_time']);
                if ($classOne['breakoff_audit_time']) {
                    $classOne['breakoff_audit_time'] = date("Y-m-d", $classOne['breakoff_audit_time']);
                } else {
                    $classOne['breakoff_audit_time'] = '--';
                }
            }

            $data = array();

            $count_sql = "select b.breakoff_id
              from smc_class_breakoff as b 
              left join smc_class as c on c.class_id=b.class_id
              left join smc_staffer as st on st.staffer_id=b.breakoff_apply_staffer_id  
              left join smc_course as co on co.course_id=c.course_id
              where {$datawhere} and b.company_id='{$this->company_id}'";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;
            $data['list'] = $classList;
            return $data;
        }
    }

    function examineBreakoff($request)
    {
        $breakOne = $this->DataControl->getFieldOne("smc_class_breakoff", "breakoff_type,breakoff_status", "breakoff_id='{$request['breakoff_id']}'");

        if (!$breakOne) {
            $this->error = true;
            $this->errortip = "不存在该申请";
            return false;
        }

        if ($breakOne['breakoff_status'] != 1) {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }
        $status = $this->LgArraySwitch(array("0" => "待校区审核", "1" => "待集团审核", "2" => "集团审核通过", "3" => "完成", "-1" => "校区审核拒绝", "-2" => "集团审核拒绝"));
        $type = $this->LgArraySwitch(array("0" => "中途拆班", "1" => "期末拆班"));
        if ($request['is_adopt'] == 1) {
            $data = array();
            $data['breakoff_status'] = 2;
            $data['breakoff_audit_staffer_id'] = $this->stafferOne['staffer_id'];
            $data['breakoff_audit_time'] = time();
            $this->DataControl->updateData("smc_class_breakoff", "breakoff_id='{$request['breakoff_id']}'", $data);

            $t_data = array();
            $t_data['breakoff_id'] = $request['breakoff_id'];
            $t_data['tracks_title'] = $this->LgStringSwitch("审核" . $type[$breakOne['breakoff_type']]);
            $t_data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师审核通过,' . $status[$data['breakoff_status']]);
            $t_data['tracks_note'] = $this->LgStringSwitch($request['reason']);
            $t_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $t_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $t_data['tracks_time'] = time();
            $this->DataControl->insertData("smc_class_breakoff_track", $t_data);

            return true;

        } elseif ($request['is_adopt'] == 0) {

            $data = array();
            $data['breakoff_status'] = -2;
            $data['breakoff_audit_staffer_id'] = $this->stafferOne['staffer_id'];
            $data['breakoff_audit_time'] = time();
            $this->DataControl->updateData("smc_class_breakoff", "breakoff_id='{$request['breakoff_id']}'", $data);

            $t_data = array();
            $t_data['breakoff_id'] = $request['breakoff_id'];
            $t_data['tracks_title'] = $this->LgStringSwitch("审核" . $type[$breakOne['breakoff_type']]);
            $t_data['tracks_information'] = $this->LgStringSwitch($this->stafferOne['staffer_cnname'] . '老师审核拒绝,' . $status[$data['breakoff_status']]);
            $t_data['tracks_note'] = $this->LgStringSwitch($request['reason']);
            $t_data['staffer_id'] = $this->stafferOne['staffer_id'];
            $t_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $t_data['tracks_time'] = time();
            $this->DataControl->insertData("smc_class_breakoff_track", $t_data);

            return true;
        } else {
            $this->error = true;
            $this->errortip = "不存在该审核状态";
            return false;
        }

    }

    function getbreakoffOne($request)
    {
        $sql = "select sch.school_cnname,sch.school_branch,c.class_id,c.class_cnname,c.class_enname,c.class_branch,c.class_fullnums,sc.course_branch,b.breakoff_type,st.staffer_cnname,st.staffer_enname,FROM_UNIXTIME( b.breakoff_apply_time, '%Y-%m-%d %H:%i:%s' ) as breakoff_apply_time
              ,(select count(ss.study_id) from smc_student_study as ss where ss.class_id=b.class_id and ss.study_isreading=1 limit 0,1) as stuNum
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=b.class_id and ch.hour_ischecking<>'-1') as allHourNum
              ,(select count(ch.hour_id) from smc_class_hour as ch where ch.class_id=b.class_id and ch.hour_ischecking='1') as hasHourNum
              ,(select group_concat(distinct s.staffer_cnname) from smc_class_teach as ct left join smc_staffer as s on s.staffer_id=ct.staffer_id where ct.class_id=b.class_id and ct.teach_type=0 and ct.teach_status=0) as teacher_name
              ,(select bt.tracks_note from smc_class_breakoff_track as bt where bt.breakoff_id=b.breakoff_id order by bt.tracks_time asc,bt.tracks_id asc limit 0,1) as reason
              from smc_class_breakoff as b 
              left join smc_class as c on c.class_id=b.class_id
              left join smc_course as sc on sc.course_id=c.course_id
              left join smc_staffer as st on st.staffer_id=b.breakoff_apply_staffer_id
              left join smc_school as sch on sch.school_id=b.school_id
              where b.breakoff_id='{$request['breakoff_id']}' limit 0,1
              ";

        $breakOne = $this->DataControl->selectOne($sql);
        $type = $this->LgArraySwitch(array("0" => "中途拆班", "1" => "期末拆班"));
        if ($breakOne) {
            $breakOne['staffer_cnname'] = $breakOne['staffer_enname'] ? $breakOne['staffer_cnname'] . '-' . $breakOne['staffer_enname'] : $breakOne['staffer_cnname'];
            $breakOne['number'] = $breakOne['stuNum'] . '/' . $breakOne['class_fullnums'];
            $breakOne['progress'] = $breakOne['hasHourNum'] . '/' . $breakOne['allHourNum'];
            $breakOne['breakoff_type_name'] = $type[$breakOne['breakoff_type']];
        }

        $sql = "select FROM_UNIXTIME(tracks_time, '%Y-%m-%d %H:%i:%s' ) as tracks_time,tracks_title,tracks_information,tracks_note,tracks_playname
              from smc_class_breakoff_track where breakoff_id='{$request['breakoff_id']}'
              order by tracks_time asc,tracks_id asc
              ";

        $trackList = $this->DataControl->selectClear($sql);


        $classfield = array();

        $k = 0;
        $classfield[$k]["fieldstring"] = "school_cnname";
        $classfield[$k]["fieldname"] = "校区名称";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "school_branch";
        $classfield[$k]["fieldname"] = "校区编号";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;


        $classfield[$k]["fieldstring"] = "class_cnname";
        $classfield[$k]["fieldname"] = "班级名称";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "class_enname";
        $classfield[$k]["fieldname"] = "班级别名";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "class_branch";
        $classfield[$k]["fieldname"] = "班级编号";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "course_branch";
        $classfield[$k]["fieldname"] = "课程别名称";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "number";
        $classfield[$k]["fieldname"] = "班级人数";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "progress";
        $classfield[$k]["fieldname"] = "上课进度";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $classfield[$k]["fieldstring"] = "teacher_name";
        $classfield[$k]["fieldname"] = "主教老师";
        $classfield[$k]["show"] = 1;
        $classfield[$k]["custom"] = 1;
        $k++;

        $applyfield = array();

        $k = 0;
        $applyfield[$k]["fieldstring"] = "breakoff_type_name";
        $applyfield[$k]["fieldname"] = "拆班类型";
        $applyfield[$k]["show"] = 1;
        $applyfield[$k]["custom"] = 1;
        $k++;

        $applyfield[$k]["fieldstring"] = "reason";
        $applyfield[$k]["fieldname"] = "申请拆班原因";
        $applyfield[$k]["show"] = 1;
        $applyfield[$k]["custom"] = 1;
        $k++;

        $applyfield[$k]["fieldstring"] = "staffer_cnname";
        $applyfield[$k]["fieldname"] = "申请人";
        $applyfield[$k]["show"] = 1;
        $applyfield[$k]["custom"] = 1;
        $k++;

        $applyfield[$k]["fieldstring"] = "breakoff_apply_time";
        $applyfield[$k]["fieldname"] = "申请时间";
        $applyfield[$k]["show"] = 1;
        $applyfield[$k]["custom"] = 1;
        $k++;


        $data = array();
        $data['breakOne'][] = $breakOne;
        $data['classfield'] = $classfield;
        $data['applyfield'] = $applyfield;
        $data['trackList'] = $trackList;

        return $data;
    }


    function getRegisterChange($request)
    {
        $datawhere = "  AND A.company_id='{$request['company_id']}' ";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id='{$request['school_id']}' ";
        } else {
            $datawhere .= " AND F.school_istest <> '1' AND F.school_isclose <> '1'";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (G.student_cnname like '%{$request['keyword']}%' 
            or G.student_enname like '%{$request['keyword']}%' 
            or G.student_idcard like '%{$request['keyword']}%' 
            or G.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and A.apply_time>= '{$starttime}' ";
        }
        //结束时间
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $endtime = strtotime($request['endtime'] . ' 23:59:59');
            $datawhere .= " and A.apply_time<= '{$endtime}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and A.confirm_time >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . ' 23:59:59');
            $datawhere .= " and A.confirm_time <= '{$firstday}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and A.coursetype_id= '{$request['coursetype_id']}'";
        }

        if (isset($request['confirm_status']) && $request['confirm_status'] !== '') {
            $datawhere .= " and A.confirm_status= '{$request['confirm_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "	SELECT 
                A.info_id,
                F.school_id,
                F.school_branch,
                (case when F.school_shortname='' then F.school_cnname else F.school_shortname end) as school_cnname,
                F.school_enname,
                G.student_branch,
                G.student_cnname,
                G.student_enname,
                G.student_img,
                G.student_sex,
                A.coursetype_id,
                A.coursetype_cnname,
                A.coursetype_branch,
                A.pay_price,
                A.pay_firsttime as pay_day,
                A.pay_successtime as calc_day,
                A.apply_changetime as change_day,
                (select staffer_cnname from smc_staffer where staffer_id=A.apply_staffer_id) as apply_staffer_cnname,
                A.apply_time,
                A.apply_reason,
                (select staffer_cnname from smc_staffer where staffer_id=A.confirm_staffer_id) as confirm_staffer_cnname,
                A.confirm_time,A.confirm_status,
                A.confirm_reason
                FROM smc_student_registerinfo A 
                LEFT JOIN smc_school F ON A.school_id = F.school_id 	AND F.company_id = A.company_id
                LEFT JOIN smc_student G ON G.student_id = A.student_id 	AND G.company_id = A.company_id
                WHERE A.info_status=1 and A.confirm_status<>0 
                {$datawhere} 
                ORDER BY A.coursetype_id,A.student_id,	A.order_createtime 
              ";

        $status = $this->LgArraySwitch(array("0" => "--", "1" => "待审核", "2" => "已通过", "-2" => "已拒绝"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_enname'] = $dateexcelvar['student_enname'];

                $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                $datearray['pay_price'] = $dateexcelvar['pay_price'];
                $datearray['pay_day'] = $dateexcelvar['pay_day'] == '0' ? '--' : date('Y-m-d', $dateexcelvar['pay_day']);
//                $datearray['calc_day'] = $dateexcelvar['calc_day'] == '0' ? '--' : date('Y-m-d', $dateexcelvar['calc_day']);
                $datearray['change_day'] = $dateexcelvar['confirm_status'] > 0 ? ($dateexcelvar['change_day'] == '0' ? '--' : date('Y-m-d', $dateexcelvar['change_day'])) : "--";


                $datearray['apply_staffer_cnname'] = $dateexcelvar['apply_staffer_cnname'];
                $datearray['apply_time'] = $dateexcelvar['apply_time'] == '0' ? '--' : date('Y-m-d', $dateexcelvar['apply_time']);
                $datearray['apply_reason'] = $dateexcelvar['apply_reason'];
                $datearray['confirm_status'] = $status[$dateexcelvar['confirm_status']];
                $datearray['confirm_staffer_cnname'] = $dateexcelvar['confirm_staffer_cnname'];
                $datearray['confirm_time'] = $dateexcelvar['confirm_time'] == '0' ? '--' : date('Y-m-d', $dateexcelvar['confirm_time']);
                $datearray['confirm_reason'] = $dateexcelvar['confirm_reason'];
                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("校区编号", "校区名称", "学员编号", "学员中文名", "学员英文名", "班组名称", "班组编号", "首缴金额", "原首缴日期", "申请变更首缴日期", "申请人", "申请时间", "申请原因", "审核状态", "审核人", "审核时间", "备注"));
            $excelfileds = array('school_branch', 'school_cnname', 'student_branch', 'student_cnname', 'student_enname', 'coursetype_cnname', 'coursetype_branch', 'pay_price', 'pay_day', 'change_day', 'apply_staffer_cnname', 'apply_time', 'apply_reason', 'confirm_status', 'confirm_staffer_cnname', 'confirm_time', 'confirm_reason');

            $tem_name = "计新生业绩审核表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $NewReg = $this->DataControl->selectClear($sql);
            if (!$NewReg) {
                $this->error = true;
                $this->errortip = "无新生业绩数据";
                return false;
            }

            foreach ($NewReg as &$var) {
                $var['is_change'] = $var['confirm_status'] == '1' ? 1 : 0;
                $var['confirm_status'] = $status[$var['confirm_status']];

                $var['pay_day'] = $var['pay_day'] == '0' ? '--' : date('Y-m-d', $var['pay_day']);
                $var['calc_day'] = $var['calc_day'] == '0' ? '--' : date('Y-m-d', $var['calc_day']);
                $var['change_day'] = $var['change_day'] == '0' ? '--' : date('Y-m-d', $var['change_day']);
                $var['apply_time'] = $var['apply_time'] == '0' ? '--' : date('Y-m-d', $var['apply_time']);
                $var['confirm_time'] = $var['confirm_time'] == '0' ? '--' : date('Y-m-d', $var['confirm_time']);
            }

            $data = array();

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT A.info_id FROM smc_student_registerinfo a 
                LEFT JOIN smc_school F ON A.school_id = F.school_id 	AND F.company_id = A.company_id
                LEFT JOIN smc_student G ON G.student_id = A.student_id 	AND G.company_id = A.company_id
                WHERE A.info_status=1  and A.confirm_status<>0 {$datawhere}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $NewReg;

            return $data;
        }
    }


    function updateRegisterTimeChange($request)
    {
        $data = array();
        $result = array();

        $regOne = $this->DataControl->getFieldOne('smc_student_registerinfo', "student_id,school_id,apply_changetime", "info_id='{$request['info_id']}' and company_id='{$request['company_id']}'");
        if ($regOne) {
            $data['info_id'] = $request["info_id"];
            if ($request["confirm_status"] == '2') {
                $data['pay_successtime'] = $regOne['apply_changetime'];
            }
            $data['confirm_status'] = $request["confirm_status"];
            $data['confirm_staffer_id'] = $request["staffer_id"];
            $data['confirm_time'] = time();
            $data['confirm_reason'] = $request["confirm_reason"];
            $data['info_updatetime'] = time();
            $bool = $this->DataControl->updateData("smc_student_registerinfo", "info_id='{$request['info_id']}'", $data);
            if ($bool) {
                $result['error'] = '0';
                $result['errortip'] = '审核成功，请刷新查询';
                return $result;
            } else {
                $result['error'] = '1';
                $result['errortip'] = '审核失败，请联系管理员';
                return $result;
            }
        } else {
            $result['error'] = '1';
            $result['errortip'] = '审核失败，查询不到学生新生信息';
            return $result;
        }
        return $result;
    }

    function getCompanyBalanceApplyList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.student_cnname like '%{$request['keyword']}%' or b.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id = '{$request['school_id']}'";
        }

        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and d.district_id='{$request['district_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.balanceapply_addtime, '%Y-%m-%d' )>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.balanceapply_addtime, '%Y-%m-%d' )<='{$request['endtime']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['balanceapply_status']) && $request['balanceapply_status'] !== '') {
            $datawhere .= " and a.balanceapply_status = '{$request['balanceapply_status']}'";
        }


        if (isset($request['balanceapply_usestatus']) && $request['balanceapply_usestatus'] !== '') {
            $datawhere .= " and a.balanceapply_usestatus = '{$request['balanceapply_usestatus']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.balanceapply_id,b.student_branch,b.student_cnname,c.coursetype_cnname,a.balanceapply_imgslist,a.balanceapply_remarks,a.balanceapply_status,a.balanceapply_usestatus,a.balanceapply_statusreason,d.school_cnname,d.school_branch,FROM_UNIXTIME(a.balanceapply_addtime, '%Y-%m-%d %H:%i:%s' ) as balanceapply_addtime,if(a.balanceapply_examinetime=0,'',FROM_UNIXTIME(a.balanceapply_examinetime, '%Y-%m-%d %H:%i:%s' )) as balanceapply_examinetime,e.staffer_cnname,ifnull(f.staffer_cnname,'--') as staff_cnname
                from smc_student_balanceapply as a 
                left join smc_student as b on a.student_id=b.student_id
                left join smc_code_coursetype as c on c.coursetype_id=a.coursetype_id
                left join smc_school as d on d.school_id=a.school_id
                left join smc_staffer as e on e.staffer_id=a.balanceapply_staffer_id
                left join smc_staffer as f on f.staffer_id=a.balanceapply_staff_id
                where {$datawhere} and b.company_id='{$this->company_id}'
                order by a.balanceapply_addtime desc
              ";

        $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已拒绝", "-2" => "已取消"));
        $usestatus = $this->LgArraySwitch(array("0" => "未使用", "1" => "已使用"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                $datearray['picture'] = $dateexcelvar['balanceapply_imgslist'];
                $datearray['balanceapply_remarks'] = $dateexcelvar['balanceapply_remarks'];
                $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];

                $datearray['balanceapply_status_name'] = $status[$dateexcelvar['balanceapply_status']];
                $datearray['balanceapply_usestatus_name'] = $dateexcelvar['balanceapply_status'] == 1 ? $usestatus[$dateexcelvar['balanceapply_usestatus']] : '--';

                $datearray['balanceapply_statusreason'] = $dateexcelvar['balanceapply_statusreason'];
                $datearray['staff_cnname'] = $dateexcelvar['staff_cnname'];
                $datearray['balanceapply_addtime'] = $dateexcelvar['balanceapply_addtime'];
                $datearray['balanceapply_examinetime'] = $dateexcelvar['balanceapply_examinetime'];


                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "学员中文名", "学员编号", "班组", "申请凭证", "申请原因", "申请人", "审核状态", "使用状态", "审核备注", "审核人", "申请时间", "审核时间"));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'coursetype_cnname', 'picture', 'balanceapply_remarks', 'staffer_cnname', 'balanceapply_status_name', 'balanceapply_usestatus_name', 'balanceapply_statusreason', 'staff_cnname', 'balanceapply_addtime', 'balanceapply_examinetime');

            $tem_name = "余额支付限制班组审核表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $applyList = $this->DataControl->selectClear($sql);

            if (!$applyList) {
                $this->error = true;
                $this->errortip = "无申请数据";
                return false;
            }

            foreach ($applyList as &$applyOne) {
                $applyOne['picture'] = '点击查看';
                $applyOne['balanceapply_status_name'] = $status[$applyOne['balanceapply_status']];
                $applyOne['balanceapply_usestatus_name'] = $applyOne['balanceapply_status'] == 1 ? $usestatus[$applyOne['balanceapply_usestatus']] : '--';
            }

            $data = array();

            $count_sql = "select a.balanceapply_id
                from smc_student_balanceapply as a 
                left join smc_student as b on a.student_id=b.student_id
                left join smc_code_coursetype as c on c.coursetype_id=a.coursetype_id
                left join smc_school as d on d.school_id=a.school_id
                where {$datawhere} and b.company_id='{$this->company_id}'";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;
            $data['list'] = $applyList;

            return $data;
        }
    }

    function examineBalanceApply($request)
    {

        $applyOne = $this->DataControl->getOne("smc_student_balanceapply", "balanceapply_id='{$request['balanceapply_id']}'");

        if (!$applyOne) {
            $this->error = true;
            $this->errortip = "该申请不存在";
            return false;
        }

        if ($applyOne['balanceapply_status'] != '0') {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }


        $data = array();

        if ($request['status'] == 1) {
            $data['balanceapply_status'] = 1;
        } else {
            $data['balanceapply_status'] = -1;
        }

        $data['balanceapply_statusreason'] = $request['reason'];
        $data['balanceapply_staff_id'] = $request['staffer_id'];
        $data['balanceapply_examinetime'] = time();

        if ($this->DataControl->updateData("smc_student_balanceapply", "balanceapply_id='{$request['balanceapply_id']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "审核失败";
            return false;
        }

    }

    function getClassUncheckList($request)
    {

        $datawhere = " b.company_id='{$this->company_id}' and a.hour_ischecking=0 and a.hour_day<curdate() and d.course_inclasstype=0";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.class_cnname like '%{$request['keyword']}%' or b.class_enname like '%{$request['keyword']}%' or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and b.school_id = '{$request['school_id']}'";
        }else{
            $datawhere .= " and c.school_istest = 0 and c.school_isclose=0";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and a.hour_day>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and a.hour_day<='{$request['endtime']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.school_cnname,c.school_branch,b.class_cnname,b.class_enname,b.class_branch,d.course_cnname,a.hour_day,a.hour_lessontimes,a.hour_name,a.hour_iswarming,ifnull(e.checkinglog_text,'--') as checkinglog_text,ifnull(e.checkinglog_createtime,'') as checkinglog_createtime
                ,concat(a.hour_starttime,concat('-',a.hour_endtime)) as timeslot
                ,ifnull((select group_concat(y.staffer_cnname) from smc_class_hour_teaching as x,smc_staffer as y where x.staffer_id=y.staffer_id and x.hour_id=a.hour_id and x.teaching_type=0 and x.teaching_isdel=0),'--') as main_staffer_cnname
                from smc_class_hour as a 
                left join smc_class as b on b.class_id=a.class_id
                left join smc_school as c on c.school_id=b.school_id
                left join smc_course as d on d.course_id=b.course_id
                left join smc_auto_checkinglog as e on e.hour_id=a.hour_id
                where {$datawhere} 
                order by a.hour_id desc
              ";

        $type = $this->LgArraySwitch(array("0" => "排课课次", "1" => "暖身课次", "2" => "复习课次"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                $datearray['class_enname'] = $dateexcelvar['class_enname'];
                $datearray['class_branch'] = $dateexcelvar['class_branch'];
                $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                $datearray['hour_day'] = $dateexcelvar['hour_day'];
                $datearray['timeslot'] = $dateexcelvar['timeslot'];
                $datearray['main_staffer_cnname'] = $dateexcelvar['main_staffer_cnname'];
                $datearray['hour_name'] = $dateexcelvar['hour_name'];

                $datearray['hour_iswarming_name'] = $type[$dateexcelvar['hour_iswarming']];
                $datearray['checkinglog_text'] = $dateexcelvar['checkinglog_text'];
                $datearray['checkinglog_createtime'] = $dateexcelvar['checkinglog_createtime']==''?'--':date("Y-m-d H:i:s", $dateexcelvar['checkinglog_createtime']);
                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "班级名称", "班级别名", "班级编号", "课程别名称", "上课日期", "上课时间段", "主教", "课次", "课次类型", "考勤失败原因", "记录时间"));
            $excelfileds = array('school_cnname', 'school_branch', 'class_cnname', 'class_enname', 'class_branch', 'course_cnname', 'hour_day', 'timeslot', 'main_staffer_cnname', 'hour_name', 'hour_iswarming_name', 'checkinglog_text', 'checkinglog_createtime');

            $tem_name = "班级未考勤课时明细表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $hourList = $this->DataControl->selectClear($sql);

            if (!$hourList) {
                $this->error = true;
                $this->errortip = "无班级未考勤课时明细";
                return false;
            }

            foreach ($hourList as &$hourOne) {
                $hourOne['hour_iswarming_name'] = $type[$hourOne['hour_iswarming']];
                $hourOne['checkinglog_createtime'] = $hourOne['checkinglog_createtime']==''?'--':date("Y-m-d H:i:s", $hourOne['checkinglog_createtime']);
            }

            $data = array();

            $count_sql = "select a.hour_id
                from smc_class_hour as a 
                left join smc_class as b on b.class_id=a.class_id
                left join smc_school as c on c.school_id=b.school_id
                left join smc_course as d on d.course_id=b.course_id
                left join smc_auto_checkinglog as e on e.hour_id=a.hour_id
                where {$datawhere} ";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;
            $data['list'] = $hourList;

            return $data;
        }
    }


}