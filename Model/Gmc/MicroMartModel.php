<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class MicroMartModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //商品管理
    function GoodsList($paramArray)
    {

        $datawhere = " c.company_id = '{$paramArray['company_id']}' and g.sellgoods_type > '1'";
        $datewhere = " c.company_id = '{$paramArray['company_id']}'";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.sellgoods_name like '%{$paramArray['keyword']}%')";
            $datewhere .= " and (g.sellgoods_name like '%{$paramArray['keyword']}%' or a.agreement_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['sellgoods_type']) && $paramArray['sellgoods_type'] !== "") {
            if($paramArray['sellgoods_type'] == '1'){
                $datewhere .= " and (g.sellgoods_type = '0' OR g.sellgoods_type = '1')";
            }else{
                $datewhere .= " and g.sellgoods_type = '{$paramArray['sellgoods_type']}'";
            }
            $datawhere .= " and g.sellgoods_type = '{$paramArray['sellgoods_type']}'";
        }
        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== "") {
            $datawhere .= " and g.class_id = '{$paramArray['class_id']}'";
            $datewhere .= " and g.class_id = '{$paramArray['class_id']}'";
        }
        if (isset($paramArray['category_id']) && $paramArray['category_id'] !== "") {
            $datawhere .= " and g.category_id = '{$paramArray['category_id']}'";
            $datewhere .= " and g.category_id = '{$paramArray['category_id']}'";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and g.course_id = '{$paramArray['course_id']}'";
            $datewhere .= " and g.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['sellgoods_issale']) && $paramArray['sellgoods_issale'] !== "") {
            $datawhere .= " and g.sellgoods_issale = '{$paramArray['sellgoods_issale']}'";
            $datewhere .= " and g.sellgoods_issale = '{$paramArray['sellgoods_issale']}'";
        }
        if (isset($paramArray['sellgoods_ishot']) && $paramArray['sellgoods_ishot'] !== "") {
            $datawhere .= " and g.sellgoods_ishot = '{$paramArray['sellgoods_ishot']}'";
            $datewhere .= " and g.sellgoods_ishot = '{$paramArray['sellgoods_ishot']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
            $datewhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
            $datewhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['district_id']) && $paramArray['district_id'] !== "") {
            $datawhere .= " and ((g.sellgoods_fitarea = '0' and '{$paramArray['district_id']}' IN (SELECT d.district_id FROM gmc_company_district as d WHERE d.company_id = g.company_id)) OR (g.sellgoods_fitarea = '1' and '{$paramArray['district_id']}' IN (SELECT d.district_id FROM shop_sellgoods_district as d WHERE d.sellgoods_id = g.sellgoods_id)))";
            $datewhere .= " and ((g.sellgoods_fitarea = '0' and '{$paramArray['district_id']}' IN (SELECT d.district_id FROM gmc_company_district as d WHERE d.company_id = g.company_id)) OR (g.sellgoods_fitarea = '1' and '{$paramArray['district_id']}' IN (SELECT d.district_id FROM shop_sellgoods_district as d WHERE d.sellgoods_id = g.sellgoods_id)))";
            if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
                $datawhere .= " and ((g.sellgoods_fitschool = '0' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM smc_school as s WHERE s.company_id = g.company_id)) OR (g.sellgoods_fitschool = '1' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM shop_sellgoods_school as s WHERE s.sellgoods_id = g.sellgoods_id)))";
                $datewhere .= " and ((g.sellgoods_fitschool = '0' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM smc_school as s WHERE s.company_id = g.company_id)) OR (g.sellgoods_fitschool = '1' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM shop_sellgoods_school as s WHERE s.sellgoods_id = g.sellgoods_id)))";
            }else{
                $datawhere .= " and (g.sellgoods_fitschool = '0' OR (g.sellgoods_fitschool = '1' and (SELECT ss.school_id FROM shop_sellgoods_school as ss LEFT JOIN smc_school AS s ON s.school_id = ss.school_id WHERE ss.sellgoods_id = g.sellgoods_id AND s.district_id = '{$paramArray['district_id']}') > 0))";
                $datewhere .= " and (g.sellgoods_fitschool = '0' OR (g.sellgoods_fitschool = '1' and (SELECT ss.school_id FROM shop_sellgoods_school as ss LEFT JOIN smc_school AS s ON s.school_id = ss.school_id WHERE ss.sellgoods_id = g.sellgoods_id AND s.district_id = '{$paramArray['district_id']}') > 0))";
            }
        }else{
            if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
                $datawhere .= " and ((g.sellgoods_fitschool = '0' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM smc_school as s WHERE s.company_id = g.company_id)) OR (g.sellgoods_fitschool = '1' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM shop_sellgoods_school as s WHERE s.sellgoods_id = g.sellgoods_id)))";
                $datewhere .= " and ((g.sellgoods_fitschool = '0' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM smc_school as s WHERE s.company_id = g.company_id)) OR (g.sellgoods_fitschool = '1' and '{$paramArray['school_id']}' IN (SELECT s.school_id FROM shop_sellgoods_school as s WHERE s.sellgoods_id = g.sellgoods_id)))";
            }
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT * FROM
                (SELECT
                    g.*,co.course_id as agreement_cnname
                FROM
                    shop_sellgoods as g
                LEFT JOIN
                    gmc_company as c ON c.company_id = g.company_id
                LEFT JOIN
                    smc_course as co ON co.course_id = g.course_id
                WHERE
                    {$datawhere}
                UNION ALL
                SELECT
                    g.*,a.agreement_cnname
                FROM
                    shop_sellgoods as g
                LEFT JOIN
                    gmc_company as c ON c.company_id = g.company_id
                LEFT JOIN
                    smc_fee_pricing_tuition as t ON t.tuition_id = g.tuition_id
                LEFT JOIN
                    smc_fee_pricing as p ON p.pricing_id = t.pricing_id
                LEFT JOIN
                    smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                LEFT JOIN
                    smc_course as co ON co.course_id = g.course_id
                WHERE
                    {$datewhere} AND g.sellgoods_type = '0'
                UNION ALL
                SELECT
                    g.*,a.agreement_cnname
                FROM
                    shop_sellgoods as g
                LEFT JOIN
                    gmc_company as c ON c.company_id = g.company_id
                LEFT JOIN
                    smc_fee_warehouse_coursepacks as cs ON cs.coursepacks_id = g.coursepacks_id
                LEFT JOIN
                    smc_fee_warehouse as w ON w.warehouse_id = cs.warehouse_id
                LEFT JOIN
                    smc_fee_agreement as a ON a.agreement_id = w.agreement_id
                LEFT JOIN
                    smc_course as co ON co.course_id = g.course_id
                WHERE
                    {$datewhere} AND g.sellgoods_type = '1'
                ) as q
                ORDER BY
                    q.sellgoods_id DESC
                LIMIT {$pagestart},{$num}";

        $dataList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(*) as num FROM
                                                    (SELECT
                                                        g.sellgoods_id
                                                    FROM
                                                        shop_sellgoods as g
                                                    LEFT JOIN
                                                        gmc_company as c ON c.company_id = g.company_id
                                                    LEFT JOIN
                                                        smc_course as co ON co.course_id = g.course_id
                                                    WHERE
                                                        {$datawhere}
                                                    UNION ALL
                                                    SELECT
                                                        g.sellgoods_id
                                                    FROM
                                                        shop_sellgoods as g
                                                    LEFT JOIN
                                                        gmc_company as c ON c.company_id = g.company_id
                                                    LEFT JOIN
                                                        smc_fee_pricing_tuition as t ON t.tuition_id = g.tuition_id
                                                    LEFT JOIN
                                                        smc_fee_pricing as p ON p.pricing_id = t.pricing_id
                                                    LEFT JOIN
                                                        smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                    LEFT JOIN
                                                        smc_course as co ON co.course_id = g.course_id
                                                    WHERE
                                                        {$datewhere} AND g.sellgoods_type = '0'
                                                    UNION ALL
                                                    SELECT
                                                        g.sellgoods_id
                                                    FROM
                                                        shop_sellgoods as g
                                                    LEFT JOIN
                                                        gmc_company as c ON c.company_id = g.company_id
                                                    LEFT JOIN
                                                        smc_fee_warehouse_coursepacks as cs ON cs.coursepacks_id = g.coursepacks_id
                                                    LEFT JOIN
                                                        smc_fee_warehouse as w ON w.warehouse_id = cs.warehouse_id
                                                    LEFT JOIN
                                                        smc_fee_agreement as a ON a.agreement_id = w.agreement_id
                                                    LEFT JOIN
                                                        smc_course as co ON co.course_id = g.course_id
                                                    WHERE
                                                        {$datewhere} AND g.sellgoods_type = '1'
                                                    ) as q");
        $allnums = $all_num['num'];

        if($dataList){
            $type = $this->LgArraySwitch(array('0' => '课程商品', '1' => '课程商品', '2' => '普通商品', '3' => '课程杂费', '4' => '课程教材'));
            foreach($dataList as &$v){
                $v['sellgoods_type_name'] = $type[$v['sellgoods_type']];
                $class = $this->DataControl->getFieldOne("shop_code_class","class_name","class_id='{$v['class_id']}'");
                $v['sellgoods_class'] = $class['class_name'];
                $category = $this->DataControl->getFieldOne("shop_code_category","category_name","category_id='{$v['category_id']}'");
                if($category['category_name']){
                    $v['sellgoods_category'] = $category['category_name'];
                }else{
                    $v['sellgoods_category'] = '--';
                }
                if(!$v['agreement_cnname']){
                    $v['agreement_cnname'] = '--';
                }
                if(!$v['sellgoods_unit']){
                    $v['sellgoods_unit'] = '--';
                }
                if ($v['sellgoods_type'] == '0') {
                    $tuition = $this->DataControl->selectOne("SELECT t.tuition_originalprice,t.tuition_sellingprice,t.tuition_unitprice,t.tuition_buypiece,t.tuition_refundprice,
                                                                (SELECT f.fit_price FROM smc_fee_pricing_fit as f WHERE f.tuition_id = t.tuition_id AND f.fit_isdefault = '1' LIMIT 1) as fit_price
                                                                FROM smc_fee_pricing_tuition as t
                                                                WHERE t.tuition_id = '{$v['tuition_id']}'");
                    $v['market_price'] = $tuition['tuition_originalprice'] ? $tuition['tuition_originalprice'] : '--';
                    $v['sale_price'] = $tuition['tuition_sellingprice'] !== '0.00' ? $tuition['tuition_sellingprice'] : $tuition['fit_price'];
                    $v['course_num'] = $tuition['tuition_buypiece'];
                    $v['tuition_unitprice'] = $tuition['tuition_unitprice'];
                    $v['tuition_refundprice'] = $tuition['tuition_refundprice'];
                    $v['feeitem_cnname'] = '--';
                } elseif ($v['sellgoods_type'] == '1') {
                    $courses = $this->DataControl->selectOne("SELECT SUM(c.tuition_sellingprice) as price,COUNT(c.course_id) as course_num FROM smc_fee_warehouse_courses as c WHERE c.coursepacks_id = '{$v['coursepacks_id']}'");
                    $v['market_price'] = $courses['price'] ? $courses['price'] : '--';;
                    $v['sale_price'] = $courses['price'];
                    $v['course_num'] = $courses['course_num'];
                    $v['tuition_unitprice'] = '--';
                    $v['tuition_refundprice'] = '--';
                    $v['feeitem_cnname'] = '--';
                } elseif ($v['sellgoods_type'] == '2') {
                    $goods = $this->DataControl->getFieldOne("erp_goods","goods_originalprice,goods_vipprice","goods_id = '{$v['goods_id']}'");
                    $v['market_price'] = $goods['goods_originalprice'] ? $goods['goods_originalprice'] : '--';;
                    $v['sale_price'] = $goods['goods_vipprice'];
                    $v['course_num'] = '--';
                    $v['tuition_unitprice'] = '--';
                    $v['tuition_refundprice'] = '--';
                    $v['feeitem_cnname'] = '--';
                } elseif ($v['sellgoods_type'] == '3') {
                    $v['market_price'] = $v['feeitem_price'] ? $v['feeitem_price'] : '--';
                    $v['sale_price'] = $v['feeitem_price'];
                    $v['course_num'] = '--';
                    $v['tuition_unitprice'] = '--';
                    $v['tuition_refundprice'] = '--';
                    $feeitem = $this->DataControl->getFieldOne("smc_code_feeitem","feeitem_cnname","feeitem_branch = '{$v['feeitem_branch']}'");
                    $v['feeitem_cnname'] = $feeitem['feeitem_cnname'];
                } elseif($v['sellgoods_type'] == '4') {
                    $products = $this->DataControl->selectOne("SELECT
                                                                    pp.products_originalprice,pp.products_sellingprice,a.agreement_cnname
                                                                  FROM
                                                                      smc_fee_pricing_products AS pp
                                                                  LEFT JOIN
                                                                      smc_fee_pricing as p ON p.pricing_id = pp.pricing_id
                                                                  LEFT JOIN
                                                                      smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                                  WHERE
                                                                      pp.products_id = '{$v['products_id']}'");
                    $v['agreement_cnname'] = $products['agreement_cnname'];
                    $v['market_price'] = $products['products_originalprice'];
                    $v['sale_price'] = $products['products_sellingprice'];
                    $v['course_num'] = '--';
                    $v['tuition_unitprice'] = '--';
                    $v['tuition_refundprice'] = '--';
                    $v['feeitem_cnname'] = '--';
                }
                if($v['sellgoods_updatatime']){
                    $v['sellgoods_updatatime'] = date("Y-m-d H:i:s", $v['sellgoods_updatatime']);
                }else{
                    $v['sellgoods_updatatime'] = '--';
                }
                if($v['sellgoods_fitschool']){
                    $school = $this->DataControl->selectOne("SELECT COUNT(s.school_id) as num FROM shop_sellgoods_school as ss LEFT JOIN smc_school as s ON ss.school_id = s.school_id WHERE ss.sellgoods_id = '{$v['sellgoods_id']}' AND s.company_id = '{$v['company_id']}' AND s.school_isclose = 0");
                }else{
                    $school = $this->DataControl->selectOne("SELECT COUNT(school_id) as num FROM smc_school WHERE company_id = '{$v['company_id']}' AND school_isclose = 0");
                }
                $v['school_num'] = $school['num'];

                if($v['sellgoods_islimittime']){
                    if($v['sellgoods_limitstarttime'] <= time() && $v['sellgoods_limitendtime'] >= time()){
                        $this->DataControl->updateData("shop_sellgoods","sellgoods_id = '{$v['sellgoods_id']}'",array("sellgoods_issale"=>"1"));
                    }else{
                        $this->DataControl->updateData("shop_sellgoods","sellgoods_id = '{$v['sellgoods_id']}'",array("sellgoods_issale"=>"0"));
                    }
                }
            }
        }

        $fieldstring = array('sellgoods_id', 'sellgoods_name', 'sellgoods_type_name', 'sellgoods_class', 'sellgoods_category', 'market_price', 'sale_price', 'agreement_cnname', 'sellgoods_issale', 'sellgoods_ishot', 'school_num', 'sellgoods_updatatime', 'course_num', 'tuition_unitprice', 'tuition_refundprice', 'sellgoods_unit', 'feeitem_cnname');
        $fieldname = $this->LgArraySwitch(array('商品ID', '商品名称', '商品类型', '商品分类', '二级分类', '市场价', '销售价', '所属协议', '是否上架', '是否热销', '适用学校', '更新时间', '课次', '标注单价', '退费手续费', '商品单位', '杂费类型'));
        $fieldcustom = array("0","0", "0", "0", "0", "0", "0", "0", "0", "0", "0","0", "1", "1", "1", "1", "1");
        $fieldshow =  array("0","1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;
        $result['district'] = $this->DataControl->selectClear("SELECT district_id,district_cnname FROM gmc_company_district WHERE company_id = '{$paramArray['company_id']}'");

        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂未添加商品，点击右上角即可同步或新增商品", 'result' => $result);
        }

        return $res;
    }

    //添加商品
    function addGoodsApi($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['sellgoods_name'] = $paramArray['sellgoods_name'];
        $data['sellgoods_type'] = '3';
        $data['class_id'] = $paramArray['class_id'];
        $data['category_id'] = $paramArray['category_id'];
        $data['feeitem_branch'] = $paramArray['feeitem_branch'];
        $data['feeitem_price'] = $paramArray['feeitem_price'];
        $data['sellgoods_unit'] = $paramArray['sellgoods_unit'];
        $data['sellgoods_listimg'] = $paramArray['sellgoods_listimg'];
        $data['sellgoods_detailimg'] = $paramArray['sellgoods_detailimg'];
        $data['sellgoods_remark'] = $paramArray['sellgoods_remark'];
        $data['sellgoods_issale'] = $paramArray['sellgoods_issale'];
        $data['sellgoods_ishot'] = $paramArray['sellgoods_ishot'];
        $data['sellgoods_cartchange'] = '1';
        $data['sellgoods_fitschool'] = $paramArray['sellgoods_fitschool'];
        $data['sellgoods_islimittime'] = $paramArray['sellgoods_islimittime'];
        $data['sellgoods_limitstarttime'] = strtotime($paramArray['sellgoods_limitstarttime']);
        $data['sellgoods_limitendtime'] = strtotime($paramArray['sellgoods_limitendtime']);
        $data['sellgoods_createtime'] = time();
        $dataid = $this->DataControl->insertData("shop_sellgoods", $data);
        if ($dataid) {
            $school_list = json_decode(stripslashes($paramArray['school_list']), true);
            if($school_list){
                foreach($school_list as $v){
                    $list = array();
                    $list['sellgoods_id'] = $dataid;
                    $list['school_id'] = $v;
                    $this->DataControl->insertData("shop_sellgoods_school", $list);
                }
            }

            $result = array();
            $result['sellgoods_id'] = $dataid;
            $res = array('error' => '0', 'errortip' => "添加课程杂费商品成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '添加课程杂费商品', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '添加课程杂费商品失败');
        }

        return $res;
    }

    //编辑页面
    function editGoods($paramArray)
    {
        $sellgoods = $this->DataControl->getOne("shop_sellgoods","sellgoods_id = '{$paramArray['sellgoods_id']}'");
        if($sellgoods){
            $status = $this->LgArraySwitch(array('0' => '编辑中', '1' => '生效中', '2' => '已失效'));
            if(!$sellgoods['category_id']){
                $sellgoods['category_id'] = '';
            }
            if($sellgoods['sellgoods_islimittime']){
                $sellgoods['sellgoods_limitstarttime'] = date("Y-m-d H:i:s", $sellgoods['sellgoods_limitstarttime']);
                $sellgoods['sellgoods_limitendtime'] = date("Y-m-d H:i:s", $sellgoods['sellgoods_limitendtime']);
            }else{
                $sellgoods['sellgoods_limitstarttime'] = '';
                $sellgoods['sellgoods_limitendtime'] = '';
            }
            $sellgoods['sellgoods_remark'] = $sellgoods['sellgoods_remark'] ? $sellgoods['sellgoods_remark'] : "";
            if($sellgoods['sellgoods_type'] == '0'){
                $agreement = $this->DataControl->selectOne("SELECT
                                                                    t.tuition_originalprice,t.tuition_sellingprice,a.agreement_cnname,a.agreement_status,
                                                                    (SELECT f.fit_price FROM smc_fee_pricing_fit as f WHERE f.tuition_id = t.tuition_id AND f.fit_isdefault = '1' LIMIT 1) as fit_price
                                                                FROM
                                                                    smc_fee_pricing_tuition as t
                                                                LEFT JOIN
                                                                    smc_fee_pricing as p ON p.pricing_id = t.pricing_id
                                                                LEFT JOIN
                                                                    smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                                WHERE
                                                                    t.tuition_id = '{$sellgoods['tuition_id']}'");
                $sellgoods['agreement_cnname'] = $agreement['agreement_cnname'];
                $sellgoods['agreement_status'] = $status[$agreement['agreement_status']];
                $sellgoods['market_price'] = $agreement['tuition_originalprice'];
                $sellgoods['sale_price'] = $agreement['tuition_sellingprice'] !== '0.00' ? $agreement['tuition_sellingprice'] : $agreement['fit_price'];
                $course = $this->DataControl->selectOne("SELECT
                                                                co.course_cnname,t.coursetype_cnname,c.coursecat_cnname
                                                           FROM
                                                                smc_course as co
                                                           LEFT JOIN
                                                                smc_code_coursetype as t ON t.coursetype_id = co.coursetype_id
                                                           LEFT JOIN
                                                                smc_code_coursecat as c ON c.coursecat_id = co.coursecat_id
                                                           WHERE
                                                                co.course_id = '{$sellgoods['course_id']}'");
                $sellgoods['course_cnname'] = $course['course_cnname'];
                $sellgoods['coursetype_cnname'] = $course['coursetype_cnname'];
                $sellgoods['coursecat_cnname'] = $course['coursecat_cnname'];
            }elseif($sellgoods['sellgoods_type'] == '1'){
                $agreement = $this->DataControl->selectOne("SELECT
                                                                    a.agreement_cnname,a.agreement_status,
                                                                    (SELECT
                                                                            GROUP_CONCAT(co.course_cnname)
                                                                        FROM
                                                                            smc_fee_warehouse_courses as wc
                                                                        LEFT JOIN
                                                                            smc_course as co ON co.course_id = wc.course_id
                                                                        WHERE
                                                                            wc.coursepacks_id = cs.coursepacks_id
                                                                    ) as course_cnname,
                                                                    (SELECT
                                                                            GROUP_CONCAT(DISTINCT t.coursetype_cnname)
                                                                        FROM
                                                                            smc_fee_warehouse_courses as wc
                                                                        LEFT JOIN
                                                                            smc_course as co ON co.course_id = wc.course_id
                                                                        LEFT JOIN
                                                                            smc_code_coursetype as t ON t.coursetype_id = co.coursetype_id
                                                                        WHERE
                                                                            wc.coursepacks_id = cs.coursepacks_id
                                                                    ) as coursetype_cnname,
                                                                    (SELECT
                                                                            GROUP_CONCAT(DISTINCT c.coursecat_cnname)
                                                                        FROM
                                                                            smc_fee_warehouse_courses as wc
                                                                        LEFT JOIN
                                                                            smc_course as co ON co.course_id = wc.course_id
                                                                        LEFT JOIN
                                                                            smc_code_coursecat as c ON c.coursecat_id = co.coursecat_id
                                                                        WHERE
                                                                            wc.coursepacks_id = cs.coursepacks_id
                                                                    ) as coursecat_cnname
                                                                FROM
                                                                    smc_fee_warehouse_coursepacks as cs
                                                                LEFT JOIN
                                                                    smc_fee_warehouse as w ON w.warehouse_id = cs.warehouse_id
                                                                LEFT JOIN
                                                                    smc_fee_agreement as a ON a.agreement_id = w.agreement_id
                                                                WHERE
                                                                    cs.coursepacks_id = '{$sellgoods['coursepacks_id']}'");
                $sellgoods['agreement_cnname'] = $agreement['agreement_cnname'];
                $sellgoods['agreement_status'] = $status[$agreement['agreement_status']];
                $sellgoods['course_cnname'] = $agreement['course_cnname'];
                $sellgoods['coursetype_cnname'] = $agreement['coursetype_cnname'];
                $sellgoods['coursecat_cnname'] = $agreement['coursecat_cnname'];
                $courses = $this->DataControl->selectOne("SELECT SUM(c.tuition_sellingprice) as price FROM smc_fee_warehouse_courses as c WHERE c.coursepacks_id = '{$sellgoods['coursepacks_id']}'");
                $sellgoods['market_price'] = $courses['price'];
                $sellgoods['sale_price'] = $courses['price'];
            }elseif($sellgoods['sellgoods_type'] == '2'){
                $goods = $this->DataControl->getFieldOne("erp_goods","goods_pid,goods_originalprice,goods_vipprice","goods_id = '{$sellgoods['goods_id']}'");
                $sellgoods['market_price'] = $goods['goods_originalprice'];
                $sellgoods['sale_price'] = $goods['goods_vipprice'];
                $sellgoods['goods_pid'] = $goods['goods_pid'];
            }elseif ($sellgoods['sellgoods_type'] == '3'){
                $sellgoods['market_price'] = $sellgoods['feeitem_price'];
                $sellgoods['sale_price'] = $sellgoods['feeitem_price'];
            }elseif ($sellgoods['sellgoods_type'] == '4'){
                $products = $this->DataControl->selectOne("SELECT
                                                                  pp.products_originalprice,pp.products_sellingprice,a.agreement_cnname,a.agreement_status,co.course_cnname,t.coursetype_cnname,c.coursecat_cnname
                                                             FROM
                                                                  smc_fee_pricing_products as pp
                                                             LEFT JOIN
                                                                  smc_fee_pricing as p ON p.pricing_id = pp.pricing_id
                                                             LEFT JOIN
                                                                  smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                             LEFT JOIN
                                                                  smc_course as co ON co.course_id = p.course_id
                                                             LEFT JOIN
                                                                  smc_code_coursetype as t ON t.coursetype_id = co.coursetype_id
                                                             LEFT JOIN
                                                                  smc_code_coursecat as c ON c.coursecat_id = co.coursecat_id
                                                             WHERE
                                                                  pp.products_id = '{$sellgoods['products_id']}'");
                $sellgoods['agreement_cnname'] = $products['agreement_cnname'];
                $sellgoods['agreement_status'] = $status[$products['agreement_status']];
                $sellgoods['course_cnname'] = $products['course_cnname'];
                $sellgoods['coursetype_cnname'] = $products['coursetype_cnname'];
                $sellgoods['coursecat_cnname'] = $products['coursecat_cnname'];
                $sellgoods['market_price'] = $products['products_originalprice'];
                $sellgoods['sale_price'] = $products['products_sellingprice'];
            }
            $result = array();
            $result['list'] = $sellgoods;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        }else{
            $res = array('error' => '1', 'errortip' => '商品不存在');
        }
        return $res;
    }

    //编辑商品
    function editGoodsApi($paramArray)
    {
        if(!$this->DataControl->getOne("shop_sellgoods","sellgoods_id='{$paramArray['sellgoods_id']}'")){
            $res = array('error' => '1', 'errortip' => '商品不存在');
            return $res;
        }

        $data = array();
        $data['sellgoods_name'] = $paramArray['sellgoods_name'];
        $data['feeitem_branch'] = $paramArray['feeitem_branch'];
        $data['feeitem_price'] = $paramArray['feeitem_price'];
        $data['class_id'] = $paramArray['class_id'];
        $data['category_id'] = $paramArray['category_id'];
        $data['sellgoods_unit'] = $paramArray['sellgoods_unit'];
        $data['sellgoods_listimg'] = $paramArray['sellgoods_listimg'];
        $data['sellgoods_detailimg'] = $paramArray['sellgoods_detailimg'];
        $data['sellgoods_remark'] = $paramArray['sellgoods_remark'];
        $data['sellgoods_issale'] = $paramArray['sellgoods_issale'];
        $data['sellgoods_ishot'] = $paramArray['sellgoods_ishot'];
        if($paramArray['sellgoods_type'] == '3'){
            $data['sellgoods_fitschool'] = $paramArray['sellgoods_fitschool'];
        }
        $data['sellgoods_islimittime'] = $paramArray['sellgoods_islimittime'];
        $data['sellgoods_limitstarttime'] = strtotime($paramArray['sellgoods_limitstarttime']);
        $data['sellgoods_limitendtime'] = strtotime($paramArray['sellgoods_limitendtime']);
        $data['sellgoods_updatatime'] = time();
        if ($this->DataControl->updateData("shop_sellgoods", "sellgoods_id = '{$paramArray['sellgoods_id']}'", $data)) {
            $school_list = json_decode(stripslashes($paramArray['school_list']), true);
            if($school_list){
                $this->DataControl->delData("shop_sellgoods_school","sellgoods_id='{$paramArray['sellgoods_id']}'");
                foreach($school_list as $v){
                    $list = array();
                    $list['sellgoods_id'] = $paramArray['sellgoods_id'];
                    $list['school_id'] = $v;
                    $this->DataControl->insertData("shop_sellgoods_school", $list);
                }
            }
            $res = array('error' => '0', 'errortip' => "编辑商品成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '编辑商品', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑商品失败');
        }

        return $res;
    }


    //删除商品
    function delGoodsApi($paramArray)
    {
        if(!$this->DataControl->getOne("shop_sellgoods","sellgoods_id='{$paramArray['sellgoods_id']}'")){
            $res = array('error' => '1', 'errortip' => '商品不存在');
            return $res;
        }

        if($this->DataControl->delData("shop_sellgoods", "sellgoods_id='{$paramArray['sellgoods_id']}'")){
            $res = array('error' => 0,'errortip' => "删除商品成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '删除商品', dataEncode($paramArray));
        }else{
            $res = array('error' => 1,'errortip' => "删除商品失败!");
        }

        return $res;
    }


    //批量上下架/是否热销
    function batchOperateApi($paramArray)
    {
        $career_list = json_decode(stripslashes($paramArray['career_list']), true);
        if($career_list){
            foreach($career_list as $v){
                $data = array();
                if(isset($paramArray['sellgoods_issale']) && $paramArray['sellgoods_issale'] != ''){
                    $data['sellgoods_issale'] = $paramArray['sellgoods_issale'];
                    if($paramArray['sellgoods_issale']){
                        $class = $this->DataControl->selectOne("SELECT c.class_isopen FROM shop_sellgoods as s LEFT JOIN shop_code_class as c ON c.company_id = s.company_id AND c.class_type = s.sellgoods_type WHERE s.sellgoods_id = '{$v}'");
                        if($class['class_isopen'] == '0'){
                            $res = array('error' => '1', 'errortip' => '商品上架失败，商品所属商品分类未启用！');
                            return $res;
                        }
                        $tip = "商品上架";
                    }else{
                        $tip = "商品下架";
                    }
                }
                if(isset($paramArray['sellgoods_ishot']) && $paramArray['sellgoods_ishot'] != ''){
                    $data['sellgoods_ishot'] = $paramArray['sellgoods_ishot'];
                    if($paramArray['sellgoods_ishot']){
                        $tip = "商品设置热销";
                    }else{
                        $tip = "商品设置不热销";
                    }
                }
                $res = $this->DataControl->updateData("shop_sellgoods","sellgoods_id='{$v}'",$data);
            }
        }else{
            $res = array('error' => '1', 'errortip' => '请选择商品');
            return $res;
        }

        if($res){
            $res = array('error' => 0,'errortip' => "{$tip}成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '{$tip}', dataEncode($paramArray));
        }else{
            $res = array('error' => 1,'errortip' => "{$tip}失败!");
        }

        return $res;
    }


    //批量刪除
    function batchDelApi($paramArray)
    {
        $career_list = json_decode(stripslashes($paramArray['career_list']), true);
        if($career_list){
            foreach($career_list as $v){
                $res = $this->DataControl->delData("shop_sellgoods","sellgoods_id='{$v}'");
            }
        }else{
            $res = array('error' => '1', 'errortip' => '请选择商品');
            return $res;
        }

        if($res){
            $res = array('error' => 0,'errortip' => "商品批量删除成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '商品批量删除', dataEncode($paramArray));
        }else{
            $res = array('error' => 1,'errortip' => "商品批量删除失败!");
        }

        return $res;
    }

    //添加适用学校
    function addApplySchool($paramArray)
    {
        $school_list = json_decode(stripslashes($paramArray['school_list']), true);
        if($school_list){
            foreach($school_list as $v){
                $data = array();
                $data['sellgoods_id'] = $v['sellgoods_id'];
                $data['school_id'] = $v['school_id'];
                $this->DataControl->insertData("shop_sellgoods_school", $data);
            }
            $res = array('error' => 0,'errortip' => "添加适用学校成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '商品添加适用学校', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '请选择学校');
        }

        return $res;
    }

    //移除适用学校
    function delApplySchool($paramArray)
    {
        if(!$this->DataControl->getOne("shop_sellgoods_school","sellgoods_id='{$paramArray['sellgoods_id']}' and school_id='{$paramArray['school_id']}'")){
            $res = array('error' => '1', 'errortip' => '商品不存在');
            return $res;
        }

        if($this->DataControl->delData("shop_sellgoods_school", "sellgoods_id='{$paramArray['sellgoods_id']}' and school_id='{$paramArray['school_id']}'")){
            $res = array('error' => 0,'errortip' => "移除适用学校成功!");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '移除商品适用学校', dataEncode($paramArray));
        }else{
            $res = array('error' => 1,'errortip' => "移除适用学校失败!");
        }

        return $res;
    }

    //单课程商品
    function TuitionGoodsList($paramArray)
    {
        $datawhere = " a.company_id = '{$paramArray['company_id']}' and a.agreement_status = '1' and a.agreement_startday <= CURDATE() and a.agreement_endday >= CURDATE() ";

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and t.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and (p.pricing_applytype = '0' or (p.pricing_applytype = '1' and pa.school_id = '{$paramArray['school_id']}') or (p.pricing_applytype = '-1' and pa.school_id <> '{$paramArray['school_id']}'))";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    t.*,p.pricing_name,p.pricing_applytype,a.company_id,a.agreement_cnname,co.course_cnname,co.course_branch,ct.coursetype_cnname,cc.coursecat_cnname,
                    (SELECT COUNT(d.products_id) FROM smc_fee_pricing_products as d WHERE d.pricing_id = p.pricing_id) as products_num,
                    (SELECT f.fit_price FROM smc_fee_pricing_fit as f WHERE f.tuition_id = t.tuition_id AND f.fit_isdefault = '1' LIMIT 1) as fit_price
                FROM
                    smc_fee_pricing_tuition as t
                LEFT JOIN
                    smc_fee_pricing as p ON p.pricing_id = t.pricing_id
                LEFT JOIN
                    smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                LEFT JOIN
                    smc_course as co ON co.course_id = t.course_id
                LEFT JOIN
                    smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                LEFT JOIN
                    smc_code_coursecat as cc ON cc.coursecat_id = co.coursecat_id
                LEFT JOIN
                    smc_fee_pricing_apply AS pa ON pa.pricing_id = t.pricing_id
                WHERE
                    {$datawhere} AND t.tuition_id NOT IN (SELECT s.tuition_id FROM shop_sellgoods as s WHERE s.company_id = a.company_id AND s.sellgoods_type = '0')
                GROUP BY
                    t.tuition_id
                ORDER BY
                    t.tuition_id DESC
                LIMIT {$pagestart},{$num}";

        $TuitionList = $this->DataControl->selectClear($sql);
        if($TuitionList){
            foreach($TuitionList as &$v){
                $v['tuition_sellingprice'] = $v['tuition_sellingprice'] !== '0.00' ? $v['tuition_sellingprice'] : $v['fit_price'];
                $school = $this->DataControl->selectOne("SELECT COUNT(school_id) as num FROM smc_school WHERE company_id = '{$v['company_id']}' AND school_isclose = '0'");
                $apply  = $this->DataControl->selectOne("SELECT COUNT(s.school_id) as num FROM smc_school as s LEFT JOIN smc_fee_pricing_apply as pa ON pa.school_id = s.school_id WHERE pa.pricing_id = '{$v['pricing_id']}' AND s.school_isclose = '0'");
                if($v['pricing_applytype'] == '0'){
                    $v['school_num'] = '全部适用';
                }elseif($v['pricing_applytype'] == '1'){
                    $v['school_num'] = $apply['num'];
                }elseif($v['pricing_applytype'] == '-1'){
                    $v['school_num'] = $school['num'] - $apply['num'];
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(q.tuition_id) as num FROM
                                                    (SELECT
                                                        t.tuition_id
                                                    FROM
                                                        smc_fee_pricing_tuition as t
                                                    LEFT JOIN
                                                        smc_fee_pricing as p ON p.pricing_id = t.pricing_id
                                                    LEFT JOIN
                                                        smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                    LEFT JOIN
                                                        smc_course as co ON co.course_id = t.course_id
                                                    LEFT JOIN
                                                        smc_code_coursetype as ct ON ct.coursetype_id = co.coursetype_id
                                                    LEFT JOIN
                                                        smc_code_coursecat as cc ON cc.coursecat_id = co.coursecat_id
                                                    LEFT JOIN
                                                        smc_fee_pricing_apply AS pa ON pa.pricing_id = t.pricing_id
                                                    WHERE
                                                        {$datawhere} AND t.tuition_id NOT IN (SELECT s.tuition_id FROM shop_sellgoods as s WHERE s.company_id = a.company_id)
                                                    GROUP BY
                                                        t.tuition_id) AS q");
        $allnums = $all_num['num'];

        $fieldstring = array('tuition_id', 'agreement_cnname', 'coursetype_cnname', 'coursecat_cnname', 'course_cnname', 'course_branch', 'pricing_name', 'school_num', 'tuition_originalprice', 'tuition_sellingprice', 'tuition_buypiece', 'tuition_unitprice', 'tuition_refundprice', 'products_num');
        $fieldname = $this->LgArraySwitch(array('价格设定ID', '协议名称', '所属班组', '所属班种', '课程别名称', '课程别编号', '收费价格名称', '适用学校', '市场价', '销售价', '课次数', '标准单价', '课程退费手续费', '教材数量'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($TuitionList) {
            $result['list'] = $TuitionList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无单课程商品", 'result' => $result);
        }

        return $res;
    }

    //适用学校列表
    function applySchoolList($paramArray)
    {
        $datawhere = "s.company_id = '{$paramArray['company_id']}'";
        if(isset($paramArray['pricing_id']) && $paramArray['pricing_id'] !== ''){
            $schoolList = $this->DataControl->selectClear("SELECT school_id FROM smc_fee_pricing_apply WHERE pricing_id = '{$paramArray['pricing_id']}'");
            if($schoolList){
                $arr = array();
                foreach($schoolList as $v){
                    $arr[] = $v['school_id'];
                }
                $school = implode(",", $arr);

                if(isset($paramArray['pricing_applytype']) && $paramArray['pricing_applytype'] == '1'){
                    $datawhere .= " and s.school_id IN ({$school}) ";
                }elseif(isset($paramArray['pricing_applytype']) && $paramArray['pricing_applytype'] == '-1'){
                    $datawhere .= " and s.company_id = '{$paramArray['company_id']}' and s.school_id NOT IN ({$school}) ";
                }
            }else{
                if(isset($paramArray['pricing_applytype']) && ($paramArray['pricing_applytype'] == '0' || $paramArray['pricing_applytype'] == '-1')){
                    $datawhere .= " and s.company_id = '{$paramArray['company_id']}' ";
                }else{
                    $datawhere .= " and s.school_id = '0'";
                }
            }
        }

        if(isset($paramArray['warehouse_id']) && $paramArray['warehouse_id'] !== ''){
            $schoolList = $this->DataControl->selectClear("SELECT school_id FROM smc_fee_warehouse_apply WHERE warehouse_id = '{$paramArray['warehouse_id']}'");
            if($schoolList){
                $arr = array();
                foreach ($schoolList as $v) {
                    $arr[] = $v['school_id'];
                }
                $school = implode(",", $arr);

                $datawhere .= " and s.school_id IN ({$school}) ";
            }else{
                $datawhere .= " and s.school_id = '0'";
            }
        }

//        if(isset($paramArray['goods_id']) && $paramArray['goods_id'] !== ''){
//            $schoolList = $this->DataControl->selectClear("SELECT school_id FROM smc_erp_goods_repertory WHERE goods_id = '{$paramArray['goods_id']}'");
//            if($schoolList){
//                $arr = array();
//                foreach ($schoolList as $v) {
//                    $arr[] = $v['school_id'];
//                }
//                $school = implode(",", $arr);
//
//                $datawhere .= " and s.school_id IN ({$school}) ";
//            }else{
//                $datawhere .= " and s.company_id = '{$paramArray['company_id']}' and s.school_id = '0'";
//            }
//        }

        if(isset($paramArray['sellgoods_id']) && $paramArray['sellgoods_id'] !== ''){
            $sellgoods = $this->DataControl->getFieldOne("shop_sellgoods","sellgoods_fitschool","sellgoods_id = '{$paramArray['sellgoods_id']}'");
            if($sellgoods['sellgoods_fitschool']){
                $schoolList = $this->DataControl->selectClear("SELECT school_id FROM shop_sellgoods_school WHERE sellgoods_id = '{$paramArray['sellgoods_id']}'");
            }else{
                $schoolList = $this->DataControl->selectClear("SELECT school_id FROM smc_school WHERE company_id = '{$paramArray['company_id']}'");
            }
            if($schoolList){
                $arr = array();
                foreach ($schoolList as $v) {
                    $arr[] = $v['school_id'];
                }
                $school = implode(",", $arr);

                $datawhere .= " and s.school_id IN ({$school}) ";
            }else{
                $datawhere .= " and s.school_id = '0'";
            }
        }

        if(isset($paramArray['keyword']) && $paramArray['keyword'] !== ''){
            $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_enname like '%{$paramArray['keyword']}%' or s.school_branch like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%')";
        }
        //所属区域
        if(isset($paramArray['district_id']) && $paramArray['district_id'] !== ''){
            $datawhere .= " and s.district_id = '{$paramArray['district_id']}'";
        }
        //省
        if(isset($paramArray['province_id']) && $paramArray['province_id'] !== ''){
            $datawhere .= " and s.school_province = '{$paramArray['province_id']}'";
        }
        //市
        if(isset($paramArray['city_id']) && $paramArray['city_id'] !== ''){
            $datawhere .= " and s.school_city = '{$paramArray['city_id']}'";
        }
        //区
        if(isset($paramArray['area_id']) && $paramArray['area_id'] !== ''){
            $datawhere .= " and s.school_area = '{$paramArray['area_id']}'";
        }
        $limit = "";
        if (isset($paramArray['p']) && $paramArray['p'] !== '' && isset($paramArray['num']) && $paramArray['num'] !== '') {
            $pagestart = ($paramArray['p'] - 1) * $paramArray['num'];
            $limit = "LIMIT {$pagestart},{$paramArray['num']}";
        }

        $sql = "SELECT
                    s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_enname,s.school_branch,
                    (SELECT d.district_cnname FROM gmc_company_district as d WHERE d.district_id = s.district_id) as district_cnname
                FROM
                    smc_school AS s
                WHERE
                    {$datawhere} AND s.school_isclose = '0'
                ORDER BY
                    s.school_id ASC
                {$limit}";
//        AND s.school_istest = '0'
        $stageList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT COUNT(s.school_id) as num FROM smc_school as s WHERE {$datawhere} AND s.school_isclose = '0'");
        $allnums = $all_num['num'];

        $fieldstring = array('school_id', 'school_branch', 'school_cnname', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('学校ID', '校区编号', '校园名称', '检索代码', '所属区域'));
        $fieldcustom = array("0", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        $result['district'] = $this->DataControl->selectClear("SELECT district_id,district_cnname FROM gmc_company_district WHERE company_id = '{$this->company_id}'");

        if ($stageList) {
            $result['list'] = $stageList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无适用学校", 'result' => $result);
        }

        return $res;
    }

    //同步单课程商品
    function SyncTuitionGoodsApi($paramArray)
    {
        $goods_list = json_decode(stripslashes($paramArray['goods_list']), true);
        if($goods_list){
            foreach ($goods_list as $val) {
                $tuition = $this->DataControl->selectOne("SELECT
                                                                t.*,p.pricing_name,p.pricing_applytype,a.company_id,co.course_cnname,co.course_branch,co.course_intro,co.course_img,co.course_imglist
                                                            FROM
                                                                smc_fee_pricing_tuition as t
                                                            LEFT JOIN
                                                                smc_fee_pricing as p ON p.pricing_id = t.pricing_id
                                                            LEFT JOIN
                                                                smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                            LEFT JOIN
                                                                smc_course as co ON co.course_id = t.course_id
                                                            WHERE
                                                                t.tuition_id = '{$val}'
                                                            LIMIT 1");
                if($tuition) {
                    $data = array();
                    $data['company_id'] = $tuition['company_id'];
                    $data['sellgoods_name'] = addslashes($tuition['course_cnname']) . '(' . $tuition['course_branch'] . ')';
                    $data['tuition_id'] = $tuition['tuition_id'];
                    $data['course_id'] = $tuition['course_id'];
                    $data['sellgoods_type'] = '0';
                    $class = $this->DataControl->getFieldOne("shop_code_class","class_id","company_id = '{$paramArray['company_id']}' and class_type = '1'");
                    $data['class_id'] = $class['class_id'];
                    $data['sellgoods_fitarea'] = '0';
                    if($tuition['pricing_applytype'] == '0'){
                        $data['sellgoods_fitschool'] = '0';
                    }else{
                        $data['sellgoods_fitschool'] = '1';
                    }
                    $data['sellgoods_issale'] = '0';
                    $data['sellgoods_remark'] = $tuition['course_intro'];
                    $data['sellgoods_detailimg'] = json_encode($tuition['course_img']);
                    $data['sellgoods_listimg'] = $tuition['course_imglist'];
                    $data['sellgoods_updatatime'] = $tuition['tuition_edittime'];
                    $data['sellgoods_createtime'] = $tuition['tuition_addtime'];
                    $dataid = $this->DataControl->insertData("shop_sellgoods", $data);
                    if($dataid){
                        if($tuition['pricing_applytype'] == '1'){
                            $apply = $this->DataControl->selectClear("SELECT s.school_id FROM smc_fee_pricing_apply as a LEFT JOIN smc_school as s ON s.school_id = a.school_id WHERE a.pricing_id = '{$tuition['pricing_id']}' AND s.company_id = '{$paramArray['company_id']}' AND s.school_isclose = 0");
                            if ($apply) {
                                foreach ($apply as $v) {
                                    $list = array();
                                    $list['sellgoods_id'] = $dataid;
                                    $list['school_id'] = $v['school_id'];
                                    $this->DataControl->insertData("shop_sellgoods_school", $list);
                                }
                            }
                        }elseif($tuition['pricing_applytype'] == '-1'){
                            $apply = $this->DataControl->selectClear("SELECT school_id FROM smc_fee_pricing_apply WHERE pricing_id = '{$tuition['pricing_id']}'");
                            if($apply){
                                $arr = array();
                                foreach($apply as $v){
                                    $arr[] = $v['school_id'];
                                }
                                $schoolid = implode(",", $arr);
                                $school = $this->DataControl->selectClear("SELECT school_id FROM smc_school WHERE company_id = '{$paramArray['company_id']}' AND school_isclose = 0 AND school_id NOT IN ({$schoolid})");
                                if ($school) {
                                    foreach ($school as $v) {
                                        $list = array();
                                        $list['sellgoods_id'] = $dataid;
                                        $list['school_id'] = $v['school_id'];
                                        $this->DataControl->insertData("shop_sellgoods_school", $list);
                                    }
                                }
                            }else{
                                $this->DataControl->updateData("shop_sellgoods","sellgoods_id='{$dataid}'",array("sellgoods_fitschool"=>'0'));
                            }
                        }
                    }
                }
            }
            $res = array('error' => '0', 'errortip' => "同步单课程商品成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '同步单课程商品', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '请选择单课程商品');
            return $res;
        }

        return $res;
    }


    //组合课程商品
    function CoursepacksGoodsList($paramArray)
    {
        $datawhere = " a.company_id = '{$paramArray['company_id']}' and a.agreement_status = '1' and a.agreement_startday <= CURDATE() and a.agreement_endday >= CURDATE() ";

        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== "") {
            $datawhere .= " and wc.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and wa.school_id = '{$paramArray['school_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    c.*,a.agreement_cnname,
                    (SELECT SUM(co.tuition_sellingprice) FROM smc_fee_warehouse_courses as co WHERE co.coursepacks_id = c.coursepacks_id) as tuition_sellingprice,
                    (SELECT COUNT(co.course_id) FROM smc_fee_warehouse_courses as co WHERE co.coursepacks_id = c.coursepacks_id) as course_num,
                    (SELECT COUNT(ap.school_id) FROM smc_fee_warehouse_apply as ap LEFT JOIN smc_school as s ON s.school_id = ap.school_id WHERE ap.warehouse_id = c.warehouse_id AND s.school_id > 0) as school_num
                FROM
                    smc_fee_warehouse_coursepacks as c
               LEFT JOIN
                    smc_fee_warehouse as w ON w.warehouse_id = c.warehouse_id
                LEFT JOIN
                    smc_fee_agreement as a ON a.agreement_id = w.agreement_id
                LEFT JOIN
                    smc_fee_warehouse_courses as wc ON wc.coursepacks_id = c.coursepacks_id
                LEFT JOIN
                    smc_course as co ON co.course_id = wc.course_id
                LEFT JOIN
                    smc_fee_warehouse_apply as wa ON wa.warehouse_id = c.warehouse_id
                WHERE
                    {$datawhere} AND c.coursepacks_id NOT IN (SELECT s.coursepacks_id FROM shop_sellgoods as s WHERE s.company_id = a.company_id)
                GROUP BY
                    c.coursepacks_id
                ORDER BY c.
                    coursepacks_id DESC
                LIMIT {$pagestart},{$num}";

        $CoursepacksList = $this->DataControl->selectClear($sql);
        if($CoursepacksList){
            foreach($CoursepacksList as &$v){
                $v['time'] = $v['coursepacks_startday'] . '-' . $v['coursepacks_endday'];
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(q.coursepacks_id) as num FROM
                                                    (SELECT
                                                        c.coursepacks_id
                                                    FROM
                                                        smc_fee_warehouse_coursepacks as c
                                                    LEFT JOIN
                                                        smc_fee_warehouse as w ON w.warehouse_id = c.warehouse_id
                                                    LEFT JOIN
                                                        smc_fee_agreement as a ON a.agreement_id = w.agreement_id
                                                    LEFT JOIN
                                                        smc_fee_warehouse_courses as wc ON wc.coursepacks_id = c.coursepacks_id
                                                    LEFT JOIN
                                                        smc_course as co ON co.course_id = wc.course_id
                                                    LEFT JOIN
                                                        smc_fee_warehouse_apply as wa ON wa.warehouse_id = c.warehouse_id
                                                    WHERE
                                                        {$datawhere} AND c.coursepacks_id NOT IN (SELECT s.coursepacks_id FROM shop_sellgoods as s WHERE s.company_id = a.company_id)
                                                    GROUP BY
                                                        c.coursepacks_id) AS q");
        $allnums = $all_num['num'];

        $fieldstring = array('coursepacks_id', 'agreement_cnname', 'coursepacks_name', 'course_num', 'tuition_sellingprice', 'school_num', 'time');
        $fieldname = $this->LgArraySwitch(array('组合课程ID', '协议名称', '组合课程名称', '课程数量', '销售价',  '适用学校', '有效期'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CoursepacksList) {
            $result['list'] = $CoursepacksList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无组合课程商品", 'result' => $result);
        }

        return $res;
    }

    //适用课程列表
    function applySourseList($paramArray)
    {
        $datawhere = " wc.coursepacks_id = '{$paramArray['coursepacks_id']}' ";

        $sql = "SELECT
                    p.pricing_name,co.course_cnname,co.course_branch,t.tuition_originalprice,t.tuition_sellingprice,t.tuition_buypiece,t.tuition_unitprice,t.tuition_refundprice,wc.tuition_sellingprice as price,
                    (SELECT COUNT(d.products_id) FROM smc_fee_pricing_products as d WHERE d.pricing_id = p.pricing_id) as products_num
                FROM
                    smc_fee_warehouse_courses AS wc
                LEFT JOIN
                    smc_course AS co ON co.course_id = wc.course_id
                LEFT JOIN
                    smc_fee_pricing AS p ON p.pricing_id = wc.pricing_id
                LEFT JOIN
                    smc_fee_pricing_tuition AS t ON t.pricing_id = wc.pricing_id
                WHERE
                    {$datawhere}
                ORDER BY
                    co.course_id ASC";

        $stageList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT
                                                        COUNT(co.course_id) as num
                                                    FROM
                                                        smc_fee_warehouse_courses AS wc
                                                    LEFT JOIN
                                                        smc_course AS co ON co.course_id = wc.course_id
                                                    LEFT JOIN
                                                        smc_fee_pricing AS p ON p.pricing_id = wc.pricing_id
                                                    LEFT JOIN
                                                        smc_fee_pricing_tuition AS t ON t.pricing_id = wc.pricing_id
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('pricing_name', 'course_cnname', 'tuition_originalprice', 'tuition_sellingprice', 'tuition_buypiece', 'tuition_unitprice', 'tuition_refundprice', 'products_num', 'price');
        $fieldname = $this->LgArraySwitch(array('收费价格名称', '课程别名称', '市场价', '销售价', '课次', '标准单价', '退费手续费', '教材数量', '销售优惠价'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($stageList) {
            $result['list'] = $stageList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无适用课程", 'result' => $result);
        }

        return $res;
    }

    //同步组合课程商品
    function SyncCoursepacksGoodsApi($paramArray)
    {
        $goods_list = json_decode(stripslashes($paramArray['goods_list']), true);
        if($goods_list){
            foreach ($goods_list as $val) {
                $coursepacks = $this->DataControl->selectOne("SELECT
                                                                    c.coursepacks_id,c.warehouse_id,c.coursepacks_name,c.coursepacks_intro,c.coursepacks_img,c.coursepacks_imglist,c.coursepacks_startday,c.coursepacks_endday,a.company_id
                                                                FROM
                                                                    smc_fee_warehouse_coursepacks as c
                                                                LEFT JOIN
                                                                    smc_fee_warehouse as w ON w.warehouse_id = c.warehouse_id
                                                                LEFT JOIN
                                                                    smc_fee_agreement as a ON a.agreement_id = w.agreement_id
                                                                WHERE c.coursepacks_id = '{$val}'");
                if ($coursepacks) {
                    $data = array();
                    $data['company_id'] = $coursepacks['company_id'];
                    $data['sellgoods_name'] = addslashes($coursepacks['coursepacks_name']);
                    $data['coursepacks_id'] = $coursepacks['coursepacks_id'];
                    $data['sellgoods_type'] = '1';
                    $class = $this->DataControl->getFieldOne("shop_code_class","class_id","company_id = '{$paramArray['company_id']}' and class_type = '1'");
                    $data['class_id'] = $class['class_id'];
                    $data['sellgoods_fitarea'] = '0';
                    $data['sellgoods_fitschool'] = '1';
                    $data['sellgoods_issale'] = '0';
                    $data['sellgoods_islimittime'] = '1';
                    $data['sellgoods_remark'] = $coursepacks['coursepacks_intro'];
                    $data['sellgoods_detailimg'] = json_encode($coursepacks['coursepacks_img']);
                    $data['sellgoods_listimg'] = $coursepacks['coursepacks_imglist'];
                    $data['sellgoods_limitstarttime'] = strtotime($coursepacks['coursepacks_startday']);
                    $data['sellgoods_limitendtime'] = strtotime($coursepacks['coursepacks_endday']);
                    $data['sellgoods_createtime'] = time();
                    $dataid = $this->DataControl->insertData("shop_sellgoods", $data);
                    if($dataid){
                        $warehouse = $this->DataControl->selectClear("SELECT s.school_id FROM smc_fee_warehouse_apply as a LEFT JOIN smc_school as s ON s.school_id = a.school_id WHERE a.warehouse_id = '{$coursepacks['warehouse_id']}' AND s.company_id = '{$paramArray['company_id']}' AND s.school_isclose = 0");
                        if($warehouse){
                            foreach($warehouse as $v){
                                $list = array();
                                $list['sellgoods_id'] = $dataid;
                                $list['school_id'] = $v['school_id'];
                                $this->DataControl->insertData("shop_sellgoods_school",$list);
                            }
                        }
                    }
                }
            }
            $res = array('error' => '0', 'errortip' => "同步组合课程商品成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '同步组合课程商品', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '请选择组合课程商品');
            return $res;
        }

        return $res;
    }


    //教材商品
    function BookGoodsList($paramArray)
    {
        $datawhere = " a.company_id = '{$paramArray['company_id']}' and a.agreement_status = '1' and a.agreement_startday <= CURDATE() and a.agreement_endday >= CURDATE() ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (pp.products_name like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['course_id']) && $paramArray['course_id'] !== '') {
            $datawhere .= " and p.course_id = '{$paramArray['course_id']}'";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " and (p.pricing_applytype = '0' or (p.pricing_applytype = '1' and pa.school_id = '{$paramArray['school_id']}') or (p.pricing_applytype = '-1' and pa.school_id <> '{$paramArray['school_id']}'))";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    pp.products_id,pp.pricing_id,pp.products_name,pp.products_originalprice,pp.products_sellingprice,pp.products_buypiece,pp.products_createtime,a.company_id,a.agreement_cnname,p.pricing_applytype,g.goods_pid,g.goods_createtime
                FROM
                    smc_fee_pricing_products as pp
                LEFT JOIN
                    smc_fee_pricing as p ON p.pricing_id = pp.pricing_id
                LEFT JOIN
                    smc_course as co ON co.course_id = p.course_id
                LEFT JOIN
                    smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                LEFT JOIN
                    erp_goods as g ON g.goods_id = pp.goods_id
                LEFT JOIN
                    smc_fee_pricing_apply as pa ON pa.pricing_id = pp.pricing_id
                WHERE
                    {$datawhere} AND pp.products_id NOT IN (SELECT s.products_id FROM shop_sellgoods as s WHERE s.company_id = a.company_id)
                GROUP BY
                    pp.products_id
                ORDER BY
                    pp.products_id DESC
                LIMIT {$pagestart},{$num}";

        $GoodsList = $this->DataControl->selectClear($sql);
        if($GoodsList){
            foreach($GoodsList as &$v){
                $school = $this->DataControl->selectOne("SELECT COUNT(school_id) as num FROM smc_school WHERE company_id = '{$v['company_id']}' AND school_isclose = '0'");
                $apply = $this->DataControl->selectOne("SELECT COUNT(ap.school_id) as num FROM smc_fee_pricing_apply as ap LEFT JOIN smc_school as s ON s.school_id = ap.school_id WHERE ap.pricing_id = '{$v['pricing_id']}' AND s.company_id = '{$paramArray['company_id']}' AND s.school_id > 0");
                if($v['products_createtime']){
                    $v['products_createtime'] = date("Y-m-d H:i", $v['products_createtime']);
                }else{
                    if($v['goods_createtime']){
                        $v['products_createtime'] = date("Y-m-d H:i", $v['goods_createtime']);
                    }else{
                        $v['products_createtime'] = "--";
                    }
                }
                if(!$v['pricing_applytype'] || $v['pricing_applytype'] == '0'){
                    $v['school_num'] = '全部适用';
                }elseif($v['pricing_applytype'] == '1'){
                    $v['school_num'] = $apply['num'];
                }elseif($v['pricing_applytype'] == '-1'){
                    $v['school_num'] = $school['num'] - $apply['num'];
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(q.products_id) as num FROM
                                                    (SELECT
                                                        pp.products_id
                                                    FROM
                                                        smc_fee_pricing_products as pp
                                                    LEFT JOIN
                                                        smc_fee_pricing as p ON p.pricing_id = pp.pricing_id
                                                    LEFT JOIN
                                                        smc_course as co ON co.course_id = p.course_id
                                                    LEFT JOIN
                                                        smc_fee_agreement as a ON a.agreement_id = p.agreement_id
                                                    LEFT JOIN
                                                        erp_goods as g ON g.goods_id = pp.goods_id
                                                    LEFT JOIN
                                                        smc_fee_pricing_apply as pa ON pa.pricing_id = pp.pricing_id
                                                    WHERE
                                                        {$datawhere} AND pp.products_id NOT IN ( SELECT s.products_id FROM shop_sellgoods AS s WHERE s.company_id = a.company_id )
                                                    GROUP BY
                                                        pp.products_id) AS q LIMIT 1");
        $allnums = $all_num['num'];

        $fieldstring = array('agreement_cnname', 'products_name', 'goods_pid', 'products_originalprice', 'products_sellingprice', 'products_buypiece', 'school_num', 'products_createtime');
        $fieldname = $this->LgArraySwitch(array('协议名称', '教材名称', '教材编号', '市场价', '销售价', '销售件数', '适用学校', '添加时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($GoodsList) {
            $result['list'] = $GoodsList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无教材商品", 'result' => $result);
        }

        return $res;
    }

    //同步教材商品
    function SyncBookGoodsApi($paramArray)
    {
        $goods_list = json_decode(stripslashes($paramArray['goods_list']), true);
        if($goods_list){
            foreach ($goods_list as $val) {
                $erpgoods = $this->DataControl->selectOne("SELECT p.products_id,pc.pricing_id,pc.pricing_applytype,g.* FROM smc_fee_pricing_products as p LEFT JOIN smc_fee_pricing as pc ON pc.pricing_id = p.pricing_id LEFT JOIN erp_goods as g ON g.goods_id = p.goods_id WHERE p.products_id='{$val}'");
                if ($erpgoods) {
                    $data = array();
                    $data['company_id'] = $erpgoods['company_id'];
                    $data['sellgoods_name'] = addslashes($erpgoods['goods_cnname']);
                    $data['sellgoods_tags'] = $erpgoods['goods_tags'];
                    $data['sellgoods_type'] = '4';
                    $data['products_id'] = $erpgoods['products_id'];
                    $data['goods_id'] = $erpgoods['goods_id'];
                    $class = $this->DataControl->getFieldOne("shop_code_class","class_id","company_id = '{$paramArray['company_id']}' and class_type = '4'");
                    $data['class_id'] = $class['class_id'];
                    $data['sellgoods_unit'] = $erpgoods['goods_unit'];
                    $data['sellgoods_fitarea'] = '0';
                    $data['sellgoods_fitschool'] = $erpgoods['pricing_applytype'];
                    $data['sellgoods_issale'] = '0';
                    $data['sellgoods_cartchange'] = '1';
                    $data['sellgoods_listimg'] = $erpgoods['goods_img'];
                    $data['sellgoods_detailimg'] = json_encode($erpgoods['goods_chartimg']);
                    $data['sellgoods_sort'] = $erpgoods['goods_weight'];
                    $data['sellgoods_pageview'] = $erpgoods['goods_pageview'];
                    $data['sellgoods_updatatime'] = $erpgoods['goods_updatetime'];
                    $data['sellgoods_createtime'] = $erpgoods['goods_createtime'];
                    $dataid = $this->DataControl->insertData("shop_sellgoods", $data);
                    if($dataid){
                        if($erpgoods['pricing_applytype'] == '1'){
                            $apply = $this->DataControl->selectClear("SELECT s.school_id FROM smc_fee_pricing_apply as a LEFT JOIN smc_school as s ON s.school_id = a.school_id WHERE a.pricing_id = '{$erpgoods['pricing_id']}' AND s.company_id = '{$paramArray['company_id']}' AND s.school_isclose = 0");
                            if($apply){
                                foreach($apply as $v){
                                    $list = array();
                                    $list['sellgoods_id'] = $dataid;
                                    $list['school_id'] = $v['school_id'];
                                    $this->DataControl->insertData("shop_sellgoods_school",$list);
                                }
                            }
                        }elseif($erpgoods['pricing_applytype'] == '-1'){
                            $schoolList = $this->DataControl->selectClear("SELECT s.school_id FROM smc_fee_pricing_apply as a LEFT JOIN smc_school as s ON s.school_id = a.school_id WHERE a.pricing_id = '{$erpgoods['pricing_id']}' AND s.company_id = '{$paramArray['company_id']}' AND s.school_isclose = 0");
                            if($schoolList) {
                                $arr = array();
                                foreach ($schoolList as $v) {
                                    $arr[] = $v['school_id'];
                                }
                                $school = implode(",", $arr);
                                $schoolID = $this->DataControl->selectClear("SELECT school_id FROM smc_school WHERE company_id = '{$paramArray['company_id']}' AND school_isclose = 0 AND school_id NOT IN ({$school})");
                                if($schoolID){
                                    foreach($schoolID as $v){
                                        $list = array();
                                        $list['sellgoods_id'] = $dataid;
                                        $list['school_id'] = $v['school_id'];
                                        $this->DataControl->insertData("shop_sellgoods_school",$list);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $res = array('error' => '0', 'errortip' => "同步教材商品成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '同步教材商品', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '请选择教材商品');
            return $res;
        }

        return $res;
    }

    //普通商品
    function OrdinaryGoodsList($paramArray)
    {
        $datawhere = " c.company_id = '{$paramArray['company_id']}' ";

        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    g.goods_id,g.goods_cnname,g.goods_pid,g.goods_originalprice,g.goods_vipprice,g.goods_createtime,p.prodtype_name
                FROM
                    erp_goods as g
                LEFT JOIN
                    gmc_company as c ON c.company_id = g.company_id
                LEFT JOIN
                    smc_code_prodtype as p ON p.company_id = g.company_id AND p.prodtype_code = g.prodtype_code
                WHERE
                    {$datawhere} AND g.goods_shopgoodssale = '1' AND g.goods_id NOT IN (SELECT s.goods_id FROM shop_sellgoods as s WHERE s.company_id = g.company_id)
                GROUP BY
                    g.goods_id
                ORDER BY
                    g.goods_id DESC
                LIMIT {$pagestart},{$num}";

        $GoodsList = $this->DataControl->selectClear($sql);
        if($GoodsList){
            foreach($GoodsList as &$val){
                if($val['goods_createtime']){
                    $val['goods_createtime'] = date("Y-m-d H:i", $val['goods_createtime']);
                }else{
                    $val['goods_createtime'] = '--';
                }
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(q.goods_id) as num FROM
                                                    (SELECT
                                                        g.goods_id
                                                    FROM
                                                        erp_goods as g
                                                    LEFT JOIN
                                                        gmc_company as c ON c.company_id = g.company_id
                                                    LEFT JOIN
                                                        smc_code_prodtype as p ON p.company_id = g.company_id AND p.prodtype_code = g.prodtype_code
                                                    WHERE
                                                        {$datawhere} AND g.goods_shopgoodssale = '1' AND g.goods_id NOT IN (SELECT s.goods_id FROM shop_sellgoods as s WHERE s.company_id = g.company_id)
                                                    GROUP BY
                                                        g.goods_id) AS q");
        $allnums = $all_num['num'];

        $fieldstring = array('goods_id', 'goods_cnname', 'goods_pid', 'prodtype_name', 'goods_originalprice', 'goods_vipprice', 'goods_createtime');
        $fieldname = $this->LgArraySwitch(array('商品ID', '货品名称', '货品编号', '货品类别', '市场价', '销售价', '添加时间'));
        $fieldcustom = array("0", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("0", "1", "1",  "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($GoodsList) {
            $result['list'] = $GoodsList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无普通商品", 'result' => $result);
        }

        return $res;
    }

    //同步普通商品
    function SyncShopGoodsApi($paramArray)
    {
        $goods_list = json_decode(stripslashes($paramArray['goods_list']), true);
        if($goods_list){
            foreach ($goods_list as $val) {
                $erpgoods = $this->DataControl->selectOne("SELECT g.* FROM erp_goods as g WHERE g.goods_id='{$val}'");
                if ($erpgoods) {
                    $data = array();
                    $data['company_id'] = $erpgoods['company_id'];
                    $data['sellgoods_name'] = addslashes($erpgoods['goods_cnname']);
                    $data['sellgoods_tags'] = $erpgoods['goods_tags'];
                    $data['sellgoods_type'] = '2';
                    $data['goods_id'] = $erpgoods['goods_id'];
                    $class = $this->DataControl->getFieldOne("shop_code_class","class_id","company_id = '{$paramArray['company_id']}' and class_type = '2'");
                    $data['class_id'] = $class['class_id'];
                    $data['sellgoods_unit'] = $erpgoods['goods_unit'];
                    $data['sellgoods_suppliername'] = $erpgoods['goods_suppliername'];
                    $data['sellgoods_fitarea'] = '0';
                    $data['sellgoods_fitschool'] = '0';
                    $data['sellgoods_issale'] = '0';
                    $data['sellgoods_cartchange'] = '1';
                    $data['sellgoods_listimg'] = $erpgoods['goods_img'];
                    $data['sellgoods_detailimg'] = $erpgoods['goods_chartimg'] == '[]' ? '' : json_encode($erpgoods['goods_chartimg']);
                    $data['sellgoods_sort'] = $erpgoods['goods_weight'];
                    $data['sellgoods_pageview'] = $erpgoods['goods_pageview'];
                    $data['sellgoods_updatatime'] = $erpgoods['goods_updatetime'];
                    $data['sellgoods_createtime'] = $erpgoods['goods_createtime'];
                    $this->DataControl->insertData("shop_sellgoods", $data);
                }
            }
            $res = array('error' => '0', 'errortip' => "同步普通商品成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->商品管理", '同步普通商品', dataEncode($paramArray));
        }else{
            $res = array('error' => '1', 'errortip' => '请选择普通商品');
            return $res;
        }

        return $res;
    }

    //广告位管理
    function BannerList($paramArray)
    {
        $datawhere = " b.company_id = '{$paramArray['company_id']}' ";
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (b.banner_title like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
                    b.*
                FROM
                    shop_banner AS b
                WHERE
                    {$datawhere}
                ORDER BY
                    b.banner_id ASC
                LIMIT {$pagestart},{$num}";

        $stageList = $this->DataControl->selectClear($sql);
        if($stageList){
            foreach ($stageList as &$stageVar){
                $stageVar['banner_img'] = $stageVar['banner_img']?$stageVar['banner_img'].'?x-oss-process=image/resize,m_lfit,w_400,limit_0/auto-orient,1/quality,q_90':'';
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT COUNT(b.banner_id) as num FROM shop_banner as b WHERE {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('banner_id', 'banner_title', 'banner_img', 'banner_sort');
        $fieldname = $this->LgArraySwitch(array('广告位ID', '广告位名称', '广告图片', '广告位排序'));
        $fieldcustom = array("0", "1", "1", "1");
        $fieldshow = array("0", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($stageList) {
            $result['list'] = $stageList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂未添加广告，点击右上角即可添加", 'result' => $result);
        }

        return $res;
    }

    //添加广告位
    function addBanner($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['banner_title'] = $paramArray['banner_title'];
        $data['banner_img'] = $paramArray['banner_img'];
        $data['banner_outurl'] = $paramArray['banner_outurl'];
        $data['banner_sort'] = $paramArray['banner_sort'];
        $data['banner_createtime'] = time();
        if ($this->DataControl->insertData("shop_banner", $data)) {
            $res = array('error' => '0', 'errortip' => "添加广告位成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->广告位管理", '添加广告位', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '添加广告位失败');
        }

        return $res;
    }

    //编辑广告位
    function EditBanner($paramArray)
    {
        if(!$this->DataControl->getOne("shop_banner","banner_id='{$paramArray['banner_id']}'")){
            $res = array('error' => '1', 'errortip' => '广告位不存在');
            return $res;
        }

        $data = array();
        $data['banner_title'] = $paramArray['banner_title'];
        $data['banner_img'] = $paramArray['banner_img'];
        $data['banner_outurl'] = $paramArray['banner_outurl'];
        $data['banner_sort'] = $paramArray['banner_sort'];
        if ($this->DataControl->updateData("shop_banner", "banner_id='{$paramArray['banner_id']}'",$data)) {
            $res = array('error' => '0', 'errortip' => "编辑广告位成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->广告位管理", '编辑广告位', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '编辑广告位失败');
        }

        return $res;
    }

    //删除广告位
    function delBanner($paramArray)
    {
        if(!$this->DataControl->getOne("shop_banner","banner_id='{$paramArray['banner_id']}'")){
            $res = array('error' => '1', 'errortip' => '广告位不存在');
            return $res;
        }

        if ($this->DataControl->delData("shop_banner", "banner_id='{$paramArray['banner_id']}'")) {
            $res = array('error' => '0', 'errortip' => "删除广告位成功");
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "微商城管理中心->广告位管理", '删除广告位', dataEncode($paramArray));
        } else {
            $res = array('error' => '1', 'errortip' => '删除广告位失败');
        }

        return $res;
    }
}
