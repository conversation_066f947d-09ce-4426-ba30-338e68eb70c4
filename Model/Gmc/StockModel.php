<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  StockModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //仓库管理列表
    function getWarehouseList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.warehouse_name like '%{$paramArray['keyword']}%' or p.warehouse_code like '%{$paramArray['keyword']}%' or p.warehouse_telname like '%{$paramArray['keyword']}%' or p.warehouse_tel like '%{$paramArray['keyword']}%' or p.warehouse_address like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.warehouse_id,
                p.warehouse_name,
                p.warehouse_code,
                p.warehouse_telname,
                p.warehouse_tel,
                p.warehouse_address
            FROM
                erp_code_warehouse AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.warehouse_id DESC    
            LIMIT {$pagestart},{$num}";

        $postList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.warehouse_id)
            FROM
                erp_code_warehouse AS p
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('warehouse_name ', 'warehouse_code', 'warehouse_telname', 'warehouse_tel', 'warehouse_address');
        $fieldname = $this->LgArraySwitch(array('仓库名称', '仓库编号', '联系人', '联系电话', '仓库地址'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($postList) {
            $result['list'] = $postList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无仓库", 'result' => $result);
        }

        return $res;
    }

    //添加仓库
    function addWarehouseAction($paramArray)
    {
        $data = array();
        $data['warehouse_name'] = $paramArray['warehouse_name'];
        $data['warehouse_code'] = $paramArray['warehouse_code'];
        $data['warehouse_telname'] = $paramArray['warehouse_telname'];
        $data['warehouse_tel'] = $paramArray['warehouse_tel'];
        $data['warehouse_address'] = $paramArray['warehouse_address'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['warehouse_name'] = "仓库名称";
        $field['warehouse_code'] = "仓库编号";
        $field['warehouse_telname'] = "联系人";
        $field['warehouse_tel'] = "联系电话";
        $field['warehouse_address'] = "仓库地址";
        $field['company_id'] = "所属公司";

        $warehouse_code = $this->DataControl->getFieldOne('erp_code_warehouse', 'warehouse_id', "warehouse_code = '{$paramArray['warehouse_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($warehouse_code) {
            ajax_return(array('error' => 1, 'errortip' => "仓库编号已存在!"));
        }

        if ($this->DataControl->insertData('erp_code_warehouse', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加仓库成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '添加仓库', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加仓库失败', 'result' => $result);
        }
        return $res;
    }

    //编辑仓库
    function updateWarehouseAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("erp_code_warehouse", "warehouse_id", "warehouse_id = '{$paramArray['warehouse_id']}'");
        if ($postOne) {
            $data = array();
            $data['warehouse_name'] = $paramArray['warehouse_name'];
            $data['warehouse_code'] = $paramArray['warehouse_code'];
            $data['warehouse_telname'] = $paramArray['warehouse_telname'];
            $data['warehouse_tel'] = $paramArray['warehouse_tel'];
            $data['warehouse_address'] = $paramArray['warehouse_address'];

            $field = array();
            $field['warehouse_name'] = "仓库名称";
            $field['warehouse_code'] = "仓库编号";
            $field['warehouse_telname'] = "联系人";
            $field['warehouse_tel'] = "联系电话";
            $field['warehouse_address'] = "仓库地址";

            $warehouse_code = $this->DataControl->getFieldOne('erp_code_warehouse', 'warehouse_code', "warehouse_id = '{$paramArray['warehouse_id']}'");
            if ($paramArray['warehouse_code'] != $warehouse_code['warehouse_code']) {
                $warehouse_code = $this->DataControl->getFieldOne('erp_code_warehouse', 'warehouse_id', "warehouse_code = '{$paramArray['warehouse_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($warehouse_code) {
                    ajax_return(array('error' => 1, 'errortip' => "仓库编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("erp_code_warehouse", "warehouse_id = '{$paramArray['warehouse_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "仓库修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '编辑仓库', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '仓库修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除仓库
    function delWarehouseAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("erp_code_warehouse", "warehouse_id", "warehouse_id = '{$paramArray['warehouse_id']}'");
        if ($postOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("erp_code_warehouse", "warehouse_id = '{$paramArray['warehouse_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除仓库成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '删除仓库', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除仓库失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //货品类别列表
    function getProdtypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.prodtype_code like '%{$paramArray['keyword']}%' or p.prodtype_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.prodtype_id,
                p.prodtype_code,
                p.prodtype_name,
                p.prodclass_code,
                p.prodtype_remk,
                p.kdfclass,
                c.prodclass_name
            FROM
                smc_code_prodtype AS p left join smc_code_prodclass as c on p.prodclass_code = c.prodclass_code
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
                group by p.prodtype_id
            ORDER BY
                p.prodtype_id DESC    
            LIMIT {$pagestart},{$num}";

        $CommodeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.prodtype_id)
            FROM
                smc_code_prodtype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('prodtype_code ', 'prodtype_name', 'prodtype_remk');
        $fieldname = $this->LgArraySwitch(array('货品类别编号', '货品类别名称', '货品类别备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CommodeList) {
            $result['list'] = $CommodeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无货品类别信息", 'result' => $result);
        }

        return $res;
    }

    //添加货品类别
    function addProdtypeAction($paramArray)
    {
        $data = array();
        $data['prodtype_code'] = $paramArray['prodtype_code'];
        $data['prodtype_name'] = $paramArray['prodtype_name'];
        $data['prodtype_remk'] = $paramArray['prodtype_remk'];
        $data['prodclass_code'] = $paramArray['prodclass_code'];
        $data['kdfclass'] = $paramArray['kdfclass'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['prodtype_code'] = "货品类别编号";
        $field['prodtype_name'] = "货品类别名称";
        $field['prodtype_remk'] = "备注信息";
        $field['prodclass_code'] = "商品类型编号";
        $field['kdfclass'] = "金蝶代码";
        $field['company_id'] = "所属公司";

        $prodtype_code = $this->DataControl->getFieldOne('smc_code_prodtype', 'prodtype_id', "prodtype_code = '{$paramArray['prodtype_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($prodtype_code) {
            ajax_return(array('error' => 1, 'errortip' => "货品类别编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_prodtype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加货品类别成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '添加货品类别', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加货品类别失败', 'result' => $result);
        }
        return $res;
    }

    //销货单列表 by:qyh
    function getSalesorderList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.salesorder_pid like '%{$paramArray['keyword']}%' or s.proorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['salesorder_from']) && $paramArray['salesorder_from'] !== "") {
            $datawhere .= " and s.salesorder_from ={$paramArray['salesorder_from']}";
        }
        if (isset($paramArray['salesorder_status']) && $paramArray['salesorder_status'] !== "") {
            $datawhere .= " and s.salesorder_status ={$paramArray['salesorder_status']}";
        }
        if (isset($paramArray['month']) && $paramArray['month'] !== "") {
            $datawhere .= " and p.proorder_orderbuydate = '{$paramArray['month']}'";
        }
        if (isset($paramArray['to_school_id']) && $paramArray['to_school_id'] !== "") {
            $datawhere .= " and s.to_school_id ={$paramArray['to_school_id']}";
        }
        if (isset($paramArray['activitybuy_id']) && $paramArray['activitybuy_id'] !== "") {
            $datawhere .= " and p.activitybuy_id ={$paramArray['activitybuy_id']}";
        }
        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( salesorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( salesorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.salesorder_id,
                s.salesorder_pid,
                s.salesorder_from,
                s.to_school_id,
                s.proorder_pid,
                ( SELECT sum( pg.proogoods_buynums ) FROM erp_proorder_goods AS pg WHERE pg.proorder_pid = s.proorder_pid ) AS num,
                ( SELECT sum( sg.salesordergoods_sendnums ) FROM smc_erp_salesorder_goods AS sg WHERE sg.salesorder_pid = s.salesorder_pid ) AS sendnum,
                p.proorder_allprice,
                p.proorder_status,
                p.proorder_orderbuydate,
                FROM_UNIXTIME( s.salesorder_createtime, '%Y-%m-%d' ) AS salesorder_createtime,
                s.salesorder_status,
                s.salesorder_status as salesorder_status_name,
                (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
                sc.school_branch,
                a.activitybuy_name
            FROM
                smc_erp_salesorder AS s
                LEFT JOIN erp_proorder AS p ON s.proorder_pid = p.proorder_pid
                LEFT JOIN gmc_company_activitybuy AS a ON p.activitybuy_id = a.activitybuy_id
                LEFT JOIN smc_school as sc on sc.school_id = s.to_school_id
            where {$datawhere} and s.school_id = '0' and s.company_id = '{$paramArray['company_id']}'
            order by s.salesorder_id desc
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("4" => "自主采购", "5" => "活动采购", "6" => "预估采购"));
            $statuss = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "待审核", "1" => "待发货", "2" => "待出库", "3" => "已出库", "4" => "已完成"));
            foreach ($goodsList as &$val) {
                $val['salesorder_from'] = $status[$val['salesorder_from']];
                $val['salesorder_status_name'] = $statuss[$val['salesorder_status_name']];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(s.salesorder_id) as a
            FROM
                smc_erp_salesorder AS s
                LEFT JOIN erp_proorder AS p ON s.proorder_pid = p.proorder_pid
                LEFT JOIN gmc_company_activitybuy AS a ON p.activitybuy_id = a.activitybuy_id
                LEFT JOIN smc_school as sc on sc.school_id = s.to_school_id
            where {$datawhere} and s.school_id = '0'");
        $allnums = $all_num[0]['a'];

        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无销货单数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['all_num'] = $allnums;
        return $data;
    }

    //编辑货品类别
    function updateProdtypeAction($paramArray)
    {
        $ProdtypeOne = $this->DataControl->getFieldOne("smc_code_prodtype", "prodtype_id", "prodtype_id = '{$paramArray['prodtype_id']}'");
        if ($ProdtypeOne) {
            $data = array();
            $data['prodtype_code'] = $paramArray['prodtype_code'];
            $data['prodtype_name'] = $paramArray['prodtype_name'];
            $data['prodtype_remk'] = $paramArray['prodtype_remk'];
            $data['prodclass_code'] = $paramArray['prodclass_code'];
            $data['kdfclass'] = $paramArray['kdfclass'];

            $field = array();
            $field['prodtype_code'] = "货品类别编号";
            $field['prodtype_name'] = "货品类别名称";
            $field['prodtype_remk'] = "备注信息";
            $field['prodclass_code'] = "商品类型编号";
            $field['kdfclass'] = "金蝶代码";

            $prodtype_code = $this->DataControl->getFieldOne('smc_code_prodtype', 'prodtype_code', "prodtype_id = '{$paramArray['prodtype_id']}'");
            if ($paramArray['prodtype_code'] != $prodtype_code['prodtype_code']) {
                $prodtype_code = $this->DataControl->getFieldOne('smc_code_prodtype', 'prodtype_id', "prodtype_code = '{$paramArray['prodtype_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($prodtype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "货品类别编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_prodtype", "prodtype_id = '{$paramArray['prodtype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "货品类别修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '编辑货品类别', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '货品类别修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除货品类别
    function delProdtypeAction($paramArray)
    {
        $ProdtypeOne = $this->DataControl->getFieldOne("smc_code_prodtype", "prodtype_code", "prodtype_id = '{$paramArray['prodtype_id']}'");
        if ($ProdtypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            $ErpgoodsOne = $this->DataControl->getFieldOne("erp_goods", "prodtype_code", "prodtype_code = '{$ProdtypeOne['prodtype_code']}' and company_id = '{$paramArray['company_id']}'");
            if ($ErpgoodsOne) {
                ajax_return(array('error' => 1, 'errortip' => "货品类别已被使用，无法删除!"));
            }
            if ($this->DataControl->delData("smc_code_prodtype", "prodtype_code = '{$ProdtypeOne['prodtype_code']}' and company_id = '{$paramArray['company_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除货品类别成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '删除货品类别', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除货品类别失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //出入库类型列表
    function getWarehousetypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.warehousetype_name like '%{$paramArray['keyword']}%' or p.warehousetype_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.warehousetype_id,
                p.warehousetype_code,
                p.warehousetype_name,
                p.warehousetype_remk
            FROM
                erp_code_warehousetype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.warehousetype_id DESC    
            LIMIT {$pagestart},{$num}";

        $warehousetypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.warehousetype_id)
            FROM
                erp_code_warehousetype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('warehousetype_code ', 'warehousetype_name', 'warehousetype_remk');
        $fieldname = $this->LgArraySwitch(array('出入库类型编号', '出入库类型名称', '类型备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($warehousetypeList) {
            $result['list'] = $warehousetypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无出入库类型信息", 'result' => $result);
        }

        return $res;
    }

    //添加出入库类型
    function addWarehousetypeAction($paramArray)
    {
        $data = array();
        $data['warehousetype_code'] = $paramArray['warehousetype_code'];
        $data['warehousetype_name'] = $paramArray['warehousetype_name'];
        $data['warehousetype_remk'] = $paramArray['warehousetype_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['warehousetype_code'] = "出入库类型编号";
        $field['warehousetype_name'] = "出入库类型名称";
        $field['warehousetype_remk'] = "类型备注";
        $field['company_id'] = "所属公司";

        $warehousetype_code = $this->DataControl->getFieldOne('erp_code_warehousetype', 'warehousetype_id', "warehousetype_code = '{$paramArray['warehousetype_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($warehousetype_code) {
            ajax_return(array('error' => 1, 'errortip' => "出入库类型编号已存在!"));
        }

        if ($this->DataControl->insertData('erp_code_warehousetype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加出入库类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '添加出入库类型', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加出入库类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑出入库类型
    function updateWarehousetypeAction($paramArray)
    {
        $channelOne = $this->DataControl->getFieldOne("erp_code_warehousetype", "warehousetype_id", "warehousetype_id = '{$paramArray['warehousetype_id']}'");
        if ($channelOne) {
            $data = array();
            $data['warehousetype_code'] = $paramArray['warehousetype_code'];
            $data['warehousetype_name'] = $paramArray['warehousetype_name'];
            $data['warehousetype_remk'] = $paramArray['warehousetype_remk'];

            $field = array();
            $field['warehousetype_code'] = "出入库类型编号";
            $field['warehousetype_name'] = "出入库类型名称";
            $field['warehousetype_remk'] = "类型备注";

            $warehousetype_code = $this->DataControl->getFieldOne('erp_code_warehousetype', 'warehousetype_code', "warehousetype_id = '{$paramArray['warehousetype_id']}'");
            if ($paramArray['warehousetype_code'] != $warehousetype_code['warehousetype_code']) {
                $warehousetype_code = $this->DataControl->getFieldOne('erp_code_warehousetype', 'warehousetype_id', "warehousetype_code = '{$paramArray['warehousetype_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($warehousetype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "出入库类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("erp_code_warehousetype", "warehousetype_id = '{$paramArray['warehousetype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "出入库类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '编辑出入库类型', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '出入库类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除出入库类型
    function delWarehousetypeAction($paramArray)
    {
        $ChannelOne = $this->DataControl->getFieldOne("erp_code_warehousetype", "warehousetype_id", "warehousetype_id = '{$paramArray['warehousetype_id']}'");
        if ($ChannelOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("erp_code_warehousetype", "warehousetype_id = '{$paramArray['warehousetype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除出入库类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '删除出入库类型', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除出入库类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取货品类型列表
    function getGoodsTypeList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['prodclass_code']) && $paramArray['prodclass_code'] !== "") {
            $datawhere .= " and s.prodclass_code ='{$paramArray['prodclass_code']}'";
        }

        $sql = "
            SELECT
                s.prodtype_id,
                s.prodtype_name
            FROM
                smc_code_prodtype AS s
            WHERE
                $datawhere and s.company_id = '{$paramArray['company_id']}' ";
        $GoodsTypeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["prodtype_name"] = "货品类别名称";

        $result = array();
        if ($GoodsTypeDetail) {
            $result["field"] = $field;
            $result["data"] = $GoodsTypeDetail;
            $res = array('error' => '0', 'errortip' => '获取货品类型列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取货品类型列表失败', 'result' => $result);
        }
        return $res;
    }

    //获取货品列表
    function getGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_enname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%' or g.goods_suppliername like '%{$paramArray['keyword']}%' or g.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['goods_issale']) && $paramArray['goods_issale'] !== "") {
            $datawhere .= " and g.goods_issale ={$paramArray['goods_issale']}";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }

        if (isset($paramArray['goods_shopgoodssale']) && $paramArray['goods_shopgoodssale'] !== "") {
            $datawhere .= " and g.goods_shopgoodssale ='{$paramArray['goods_shopgoodssale']}'";
        }

        if (isset($paramArray['goods_integralsale']) && $paramArray['goods_integralsale'] !== "") {
            $datawhere .= " and g.goods_integralsale ='{$paramArray['goods_integralsale']}'";
        }

        if (isset($paramArray['goods_isintegral']) && $paramArray['goods_isintegral'] !== "") {
            $datawhere .= " and g.goods_isintegral ='{$paramArray['goods_isintegral']}'";
        }

        if (isset($paramArray['goods_ishot']) && $paramArray['goods_ishot'] !== "") {
            $datawhere .= " and g.goods_ishot ='{$paramArray['goods_ishot']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT
            g.goods_id,
            g.goods_cnname,
            g.goods_enname,
            g.goods_pid,
            g.goods_specifica,
            g.goods_suppliername,
            g.goods_unit,
            g.prodtype_code,
            g.goods_pathways,
            g.goods_barcode,
            g.goods_issale,
            g.goods_issale as status,
            g.goods_originalprice,
            g.goods_vipprice,
            g.goods_outpid,
            g.goods_class,
            g.goods_isintegral,
            g.goods_integral,
            g.goods_img,
            ifnull(g.goods_chartimg,'[]') as goods_chartimg,
            g.goods_shopgoodssale,
            g.goods_integralsale,
            p.prodtype_name,
            g.goods_isimg,
            g.goods_ishot,
            g.goods_integral,
            g.goods_introduce
        FROM
            erp_goods AS g
        left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
        WHERE
            {$datawhere} and g.company_id = '{$paramArray['company_id']}'
             GROUP BY g.goods_id
        ORDER BY
            g.goods_id DESC";

        if (isset($paramArray['import']) && $paramArray['import'] == 1) {
            $goodsList = $this->DataControl->selectClear($sql);
            $dateexcelarray = $goodsList;
            if (!$dateexcelarray) {
                ajax_return(array('error' => 1, 'errortip' => "商品数据为空!"), $this->companyOne['company_language']);
            }

            $status = $this->LgArraySwitch(array('0' => '下架', '1' => '上架'));
            $hot = array("0" => "否", "1" => "是");

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['goods_pid'] = $dateexcelvar['goods_pid'];
                    $datearray['goods_cnname'] = $dateexcelvar['goods_cnname'];
                    $datearray['goods_unit'] = $dateexcelvar['goods_unit'];
                    $datearray['prodtype_name'] = $dateexcelvar['prodtype_name'];
                    $datearray['goods_ishot'] = $hot[$dateexcelvar['goods_ishot']];
                    $datearray['goods_isintegral'] = $hot[$dateexcelvar['goods_isintegral']];
                    $datearray['goods_originalprice'] = $dateexcelvar['goods_originalprice'];
                    $datearray['goods_vipprice'] = $dateexcelvar['goods_vipprice'];
                    $datearray['goods_integral'] = $dateexcelvar['goods_integral'];
                    $datearray['status'] = $status[$dateexcelvar['status']];
                    $datearray['goods_shopgoodssale'] = $status[$dateexcelvar['goods_shopgoodssale']];
                    $datearray['goods_integralsale'] = $status[$dateexcelvar['goods_integralsale']];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('货品编号', '货品名称', '单位', '类别', '是否热门商品', '是否积分商品', '成本价', '协议价', '兑换积分', '是否上架', '微商城是否上架', '积分商城是否上架'));
            $excelfileds = array('goods_pid', 'goods_cnname', 'goods_unit', 'prodtype_name', 'goods_ishot', 'goods_isintegral', 'goods_originalprice', 'goods_vipprice', 'goods_integral', 'status', 'goods_shopgoodssale', 'goods_integralsale');

            $tem_name = $this->LgStringSwitch('货品明细表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        }

        $sql .= " LIMIT {$pagestart},{$num}";
        $goodsList = $this->DataControl->selectClear($sql);

        $status = array("0" => "0", "1" => "100");
        $hot = array("0" => "否", "1" => "是");
        if ($goodsList) {
            foreach ($goodsList as &$val) {
                if (!$val['prodtype_name']) {
                    $val['prodtype_name'] = '暂无分类';
                }
                $val['status'] = $status[$val['status']];
                $val['goods_ishot_name'] = $hot[$val['goods_ishot']];
                $val['goods_isintegral_name'] = $hot[$val['goods_isintegral']];
                $val['goods_integral'] = $val['goods_isintegral'] == 1 ? $val['goods_integral'] : '--';
            }
        }

        $goodsCount = $this->DataControl->selectOne("SELECT COUNT(g.goods_id) as goodsnums FROM erp_goods AS g WHERE {$datawhere} and g.company_id = '{$paramArray['company_id']}' limit 0,1");
        $allnums = $goodsCount['goodsnums'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_unit', 'prodtype_name', 'goods_ishot_name', 'goods_isintegral_name', 'goods_originalprice', 'goods_vipprice', 'goods_integral', 'goods_issale', 'goods_shopgoodssale', 'goods_integralsale', 'goods_issale');
        $fieldname = $this->LgArraySwitch(array('货品编号', '货品名称', '单位', '类别', '是否热门商品', '是否积分商品', '成本价', '协议价', '兑换积分', '是否上架', '微商城是否上架', '积分商城是否上架', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "0");
        $fieldswitch = array(0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $iserp = $this->DataControl->getFieldOne("gmc_company", "company_iserp", "company_id = '{$paramArray['company_id']}'");
        $result['company_iserp'] = $iserp['company_iserp'];

        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_id,prodtype_code,prodtype_name from smc_code_prodtype where company_id = '{$paramArray['company_id']}' and prodtype_name <> ''");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无货品信息", 'result' => $result);
        }

        return $res;
    }

    //获取货品列表
    function getGoodsLists($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_enname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%' or g.goods_suppliername like '%{$paramArray['keyword']}%' or g.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['goods_issale']) && $paramArray['goods_issale'] !== "") {
            $datawhere .= " and g.goods_issale ={$paramArray['goods_issale']}";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['goods_ishot']) && $paramArray['goods_ishot'] !== "") {
            $datawhere .= " and g.goods_ishot ='{$paramArray['goods_ishot']}'";
        }

        if ($paramArray['import'] !== '1') {
            if (isset($paramArray['p']) && $paramArray['p'] !== '') {
                $page = $paramArray['p'];
            } else {
                $page = '1';
            }
            if (isset($paramArray['num']) && $paramArray['num'] !== '') {
                $num = $paramArray['num'];
            } else {
                $num = '10';
            }
        }

        $pagestart = ($page - 1) * $num;

        if ($paramArray['import'] !== '1') {
            $sql = "SELECT
                g.goods_id,
                g.goods_cnname,
                g.goods_enname,
                g.goods_pid,
                g.goods_specifica,
                g.goods_suppliername,
                g.goods_unit,
                g.prodtype_code,
                g.goods_pathways,
                g.goods_barcode,
                g.goods_issale,
                g.goods_issale as status,
                g.goods_originalprice,
                g.goods_vipprice,
                g.goods_outpid,
                g.goods_class,
                g.goods_ishot,
                p.prodtype_name
            FROM
                erp_goods AS g left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
            WHERE
                {$datawhere} and g.company_id = '{$paramArray['company_id']}'
                 GROUP BY g.goods_id
            ORDER BY
                g.goods_id DESC    
            LIMIT {$pagestart},{$num}";
        } else {
            $sql = "SELECT
                g.goods_id,
                g.goods_cnname,
                g.goods_enname,
                g.goods_pid,
                g.goods_specifica,
                g.goods_suppliername,
                g.goods_unit,
                g.prodtype_code,
                g.goods_pathways,
                g.goods_barcode,
                g.goods_issale,
                g.goods_issale as status,
                g.goods_originalprice,
                g.goods_vipprice,
                g.goods_outpid,
                g.goods_class,
                g.goods_ishot,
                p.prodtype_name
            FROM
                erp_goods AS g left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
            WHERE
                {$datawhere} and g.company_id = '{$paramArray['company_id']}'
                 GROUP BY g.goods_id
            ORDER BY
                g.goods_id DESC";
        }


        $goodsList = $this->DataControl->selectClear($sql);

        if ($paramArray['import'] == '1') {

            if (!$goodsList) {
                $this->error = true;
                $this->errortip = "无货品数据";
                return false;
            }

            $outexceldate = array();
            if ($goodsList) {
                $outexceldate = array();
                foreach ($goodsList as $dateexcelvar) {
                    $datearray = array();
                    $datearray['goods_outpid'] = $dateexcelvar['goods_outpid'];
                    $datearray['goods_cnname'] = $dateexcelvar['goods_cnname'];
                    $datearray['goods_enname'] = $dateexcelvar['goods_enname'];
                    $datearray['goods_barcode'] = $dateexcelvar['goods_barcode'];
                    $datearray['goods_issale'] = $dateexcelvar['goods_issale'];
                    $datearray['goods_originalprice'] = $dateexcelvar['goods_originalprice'];
                    $datearray['goods_vipprice'] = $dateexcelvar['goods_vipprice'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("K3货号", "中文名称", "英文名称", "条形码", "是否上架", "市场价", "协议价"));
            $excelfileds = array('goods_outpid', 'goods_cnname', 'goods_enname', 'goods_barcode', 'goods_issale', 'goods_originalprice', 'goods_vipprice');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "货品信息报表.xlsx");

        }

        $status = array("0" => "0", "1" => "100");
        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['status'] = $status[$val['status']];
            }
        }

        $goodsCount = $this->DataControl->selectOne("SELECT COUNT(g.goods_id) as goodsnums FROM erp_goods AS g WHERE {$datawhere} and g.company_id = '{$paramArray['company_id']}' limit 0,1");
        $allnums = $goodsCount['goodsnums'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_enname', 'goods_barcode', 'status', 'goods_originalprice', 'goods_vipprice', 'goods_issale');
        $fieldname = $this->LgArraySwitch(array('货品编号', '中文名称', '英文名称', '条形码', '是否上架', '市场价', '协议价', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "0");
        $fieldswitch = array(0, 0, 0, 0, 1, 0, 0, 0);

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $iserp = $this->DataControl->getFieldOne("gmc_company", "company_iserp", "company_id = '{$paramArray['company_id']}'");
        $result['company_iserp'] = $iserp['company_iserp'];

        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_id,prodtype_code,prodtype_name from smc_code_prodtype where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无货品信息", 'result' => $result);
        }

        return $res;
    }

    //添加货品
    function addGoodsAction($paramArray)
    {
        $data = array();
        $data['goods_cnname'] = $paramArray['goods_cnname'];
        $data['goods_enname'] = $paramArray['goods_enname'];
        $data['goods_pid'] = $paramArray['goods_pid'];
        $data['prodtype_code'] = $paramArray['prodtype_code'];
        $data['company_id'] = $paramArray['company_id'];
        $data['goods_specifica'] = $paramArray['goods_specifica'];
        $data['goods_suppliername'] = $paramArray['goods_suppliername'];
        $data['goods_unit'] = $paramArray['goods_unit'];
        $data['goods_barcode'] = $paramArray['goods_barcode'];
        $data['goods_issale'] = $paramArray['goods_issale'];
        $data['goods_originalprice'] = $paramArray['goods_originalprice'];
        $data['goods_vipprice'] = $paramArray['goods_vipprice'];
        $data['goods_isintegral'] = $paramArray['goods_isintegral'];
        $data['goods_integral'] = $paramArray['goods_integral'];
        $data['goods_img'] = $paramArray['goods_img'];
        $data['goods_shopgoodssale'] = $paramArray['goods_shopgoodssale'];
        $data['goods_integralsale'] = $paramArray['goods_integralsale'];
        $data['goods_chartimg'] = $paramArray['goods_chartimg'];
        $data['goods_class'] = '1';

        $data['goods_isimg'] = $paramArray['goods_isimg'];
        $data['goods_introduce'] = $paramArray['goods_introduce'];
        $data['goods_ishot'] = $paramArray['goods_ishot'];

        $data['goods_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属公司";
        $field['goods_cnname'] = "中文名";
        $field['goods_enname'] = "英文名";
        $field['goods_pid'] = "编号";
        $field['goods_specifica'] = "规格";
        $field['goods_suppliername'] = "品牌";
        $field['goods_unit'] = "单位";
        $field['goods_barcode'] = "条形码";
        $field['goods_issale'] = "是否上架";
        $field['goods_originalprice'] = "市场价";
        $field['goods_vipprice'] = "协议价";
        $field['goods_createtime'] = "创建时间";

        $prodtype_code = $this->DataControl->getFieldOne('erp_goods', 'goods_id', "goods_pid = '{$paramArray['goods_pid']}'");

        if ($prodtype_code) {
            ajax_return(array('error' => 1, 'errortip' => "货品编号已存在!"));
        }

//        $goods_barcode = $this->DataControl->getFieldOne('erp_goods', 'goods_id', "goods_barcode = '{$paramArray['goods_barcode']}'");
//        if ($goods_barcode) {
//            ajax_return(array('error' => 1, 'errortip' => "条形码已存在!"));
//        }

        if ($this->DataControl->insertData('erp_goods', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加货品成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->货品管理", '添加货品', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加货品失败', 'result' => $result);
        }
        return $res;
    }

    //编辑商品
    function updateGoodsAction($paramArray)
    {
        $goodsOne = $this->DataControl->getFieldOne("erp_goods", "goods_id,goods_originalprice,goods_vipprice", "goods_id = '{$paramArray['goods_id']}'");
        if ($goodsOne) {
            $data = array();
            $data['goods_cnname'] = $paramArray['goods_cnname'];
            $data['goods_enname'] = $paramArray['goods_enname'];
            $data['goods_pid'] = $paramArray['goods_pid'];
            $data['goods_specifica'] = $paramArray['goods_specifica'];
            $data['goods_suppliername'] = $paramArray['goods_suppliername'];
            $data['goods_unit'] = $paramArray['goods_unit'];
            $data['goods_barcode'] = $paramArray['goods_barcode'];
            $data['goods_originalprice'] = $paramArray['goods_originalprice'];
            $data['goods_vipprice'] = $paramArray['goods_vipprice'];
            $data['prodtype_code'] = $paramArray['prodtype_code'];

            $data['goods_isintegral'] = $paramArray['goods_isintegral'];
            $data['goods_integral'] = $paramArray['goods_integral'];
            $data['goods_img'] = $paramArray['goods_img'];
            $data['goods_shopgoodssale'] = $paramArray['goods_shopgoodssale'];
            $data['goods_integralsale'] = $paramArray['goods_integralsale'];
            $data['goods_chartimg'] = $paramArray['goods_chartimg'];
            $data['goods_isimg'] = $paramArray['goods_isimg'];
            $data['goods_introduce'] = $paramArray['goods_introduce'];
            $data['goods_ishot'] = $paramArray['goods_ishot'];

            $data['goods_updatetime'] = time();

            $field = array();
            $field['goods_cnname'] = "中文名";
            $field['goods_enname'] = "英文名";
            $field['goods_pid'] = "编号";
            $field['goods_specifica'] = "规格";
            $field['goods_suppliername'] = "品牌";
            $field['goods_unit'] = "单位";
            $field['goods_barcode'] = "条形码";
            $field['goods_originalprice'] = "市场价";
            $field['goods_vipprice'] = "协议价";
            $field['goods_createtime'] = "修改时间";

            $goods_pid = $this->DataControl->getFieldOne('erp_goods', 'goods_id', "goods_pid = '{$paramArray['goods_pid']}' and goods_id != '{$paramArray['goods_id']}' and company_id = '{$paramArray['company_id']}'");
            if ($goods_pid) {
                ajax_return(array('error' => 1, 'errortip' => "货品编号已存在!"));
            }

            if ($this->DataControl->updateData("erp_goods", "goods_id = '{$paramArray['goods_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;

                if ($goodsOne['goods_originalprice'] !== $paramArray['goods_originalprice'] || $goodsOne['goods_vipprice'] !== $paramArray['goods_vipprice']) {
                    $datas = array();
                    $datas['company_id'] = $paramArray['company_id'];
                    $datas['goods_id'] = $goodsOne['goods_id'];
                    $datas['worker_id'] = $paramArray['staffer_id'];
                    $datas['pricelog_oriprice'] = $paramArray['goods_originalprice'];
                    $datas['pricelog_price'] = $paramArray['goods_vipprice'];
                    $datas['pricelog_updatatime'] = time();
                    $this->DataControl->insertData('erp_goods_pricelog', $datas);
                }

                $res = array('error' => '0', 'errortip' => "商品修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->货品管理", '编辑货品', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '商品修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //删除商品
    function delGoodsAction($paramArray)
    {
        $ChannelOne = $this->DataControl->getFieldOne("erp_goods", "goods_id", "goods_id = '{$paramArray['goods_id']}'");
        if ($ChannelOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            $a = $this->DataControl->getFieldOne("smc_fee_pricing_products", "products_id", "goods_id = '{$paramArray['goods_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该商品已被协议绑定，无法删除!"));
            }

            $b = $this->DataControl->getFieldOne("smc_student_integralgoods", "integralgoods_id", "goods_id = '{$paramArray['goods_id']}'");
            if ($b) {
                ajax_return(array('error' => 1, 'errortip' => "该商品已被使用，无法删除!"));
            }
            if ($this->DataControl->delData("erp_goods", "goods_id = '{$paramArray['goods_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除商品成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->货品管理", '删除货品', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除商品失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //上/下架商品
    function topGoodsAction($paramArray)
    {
        $goodsOne = $this->DataControl->getFieldOne("erp_goods", "goods_id", "goods_id = '{$paramArray['goods_id']}'");
        if ($goodsOne) {
            $data = array();
            $data['goods_issale'] = $paramArray['goods_issale'];

            $field = array();
            $field['goods_issale'] = '上/下架';

            if ($this->DataControl->updateData("erp_goods", "goods_id = '{$paramArray['goods_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                if ($data['goods_issale'] == '1') {
                    $res = array('error' => '0', 'errortip' => "上架成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->货品管理", '上架货品', dataEncode($paramArray));
                } else {
                    $res = array('error' => '0', 'errortip' => "下架成功", 'result' => $result);
                    $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->货品管理", '下架货品', dataEncode($paramArray));
                }
            } else {
                $result = array();
                if ($data['goods_issale'] == '1') {
                    $result["data"] = array();
                    $res = array('error' => '1', 'errortip' => '下架失败', 'result' => $result);
                } else {
                    $result["data"] = array();
                    $res = array('error' => '1', 'errortip' => '上架失败', 'result' => $result);
                }
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function updateShopSale($paramArray)
    {
        $goodsOne = $this->DataControl->getFieldOne("erp_goods", "goods_shopgoodssale", "goods_id = '{$paramArray['goods_id']}'");
        if ($goodsOne) {
            $data = array();
            if ($goodsOne['goods_shopgoodssale'] == 0) {
                $data['goods_shopgoodssale'] = 1;
            } elseif ($goodsOne['goods_shopgoodssale'] == 1) {
                $data['goods_shopgoodssale'] = 0;
            } else {
                $this->error = true;
                $this->errortip = "上架状态错误";
                return false;
            }

            $data['goods_updatetime'] = time();

            $this->DataControl->updateData("erp_goods", "goods_id = '{$paramArray['goods_id']}'", $data);

            $status = $this->LgArraySwitch(array('0' => '下架成功', '1' => '上架成功'));

            $this->oktip = $status[$data['goods_shopgoodssale']];
            return true;
        } else {
            $this->error = true;
            $this->errortip = "无对应商品";
            return false;
        }
    }

    function updateIntegralSale($paramArray)
    {
        $goodsOne = $this->DataControl->getFieldOne("erp_goods", "goods_integralsale", "goods_id = '{$paramArray['goods_id']}'");
        if ($goodsOne) {
            $data = array();
            if ($goodsOne['goods_integralsale'] == 0) {
                $data['goods_integralsale'] = 1;
            } elseif ($goodsOne['goods_integralsale'] == 1) {
                $data['goods_integralsale'] = 0;
            } else {
                $this->error = true;
                $this->errortip = "上架状态错误";
                return false;
            }

            $data['goods_updatetime'] = time();

            $this->DataControl->updateData("erp_goods", "goods_id = '{$paramArray['goods_id']}'", $data);

            $status = $this->LgArraySwitch(array('0' => '下架成功', '1' => '上架成功'));

            $this->oktip = $status[$data['goods_integralsale']];
            return true;
        } else {
            $this->error = true;
            $this->errortip = "无对应商品";
            return false;
        }
    }

    //货品类型列表
    function prodclassList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.prodclass_code like '%{$paramArray['keyword']}%' or p.prodclass_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.prodclass_id,
                p.prodclass_code,
                p.prodclass_name,
                p.prodclass_remk
            FROM
                smc_code_prodclass AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.prodclass_id DESC    
            LIMIT {$pagestart},{$num}";

        $CommodeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.prodclass_id)
            FROM
                smc_code_prodclass AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('prodclass_code ', 'prodclass_name', 'prodclass_remk');
        $fieldname = $this->LgArraySwitch(array('货品类型编号', '货品类型名称', '货品类型备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CommodeList) {
            $result['list'] = $CommodeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无货品类型信息", 'result' => $result);
        }

        return $res;
    }

    //添加货品类型
    function addProdclassAction($paramArray)
    {
        $data = array();
        $data['prodclass_code'] = $paramArray['prodclass_code'];
        $data['prodclass_name'] = $paramArray['prodclass_name'];
        $data['prodclass_remk'] = $paramArray['prodclass_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['prodclass_code'] = "货品类型编号";
        $field['prodclass_name'] = "货品类型名称";
        $field['prodclass_remk'] = "备注信息";
        $field['company_id'] = "所属公司";

        $prodtype_code = $this->DataControl->getFieldOne('smc_code_prodclass', 'prodclass_id', "prodclass_code = '{$paramArray['prodclass_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($prodtype_code) {
            ajax_return(array('error' => 1, 'errortip' => "货品类型编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_prodclass', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加货品类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->库存相关设置->货品类型", '添加货品类型', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加货品类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑货品类型
    function updateProdclassAction($paramArray)
    {
        $ProdtypeOne = $this->DataControl->getFieldOne("smc_code_prodclass", "prodclass_id", "prodclass_id = '{$paramArray['prodclass_id']}'");
        if ($ProdtypeOne) {
            $data = array();
            $data['prodclass_code'] = $paramArray['prodclass_code'];
            $data['prodclass_name'] = $paramArray['prodclass_name'];
            $data['prodclass_remk'] = $paramArray['prodclass_remk'];

            $field = array();
            $field['prodtype_code'] = "货品类型编号";
            $field['prodtype_name'] = "货品类型名称";
            $field['prodtype_remk'] = "备注信息";

            $prodtype_code = $this->DataControl->getFieldOne('smc_code_prodclass', 'prodclass_code', "prodclass_id = '{$paramArray['prodclass_id']}'");
            if ($paramArray['prodclass_code'] != $prodtype_code['prodclass_code']) {
                $prodtype_code = $this->DataControl->getFieldOne('smc_code_prodclass', 'prodclass_id', "prodclass_code = '{$paramArray['prodclass_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($prodtype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "货品类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_prodclass", "prodclass_id = '{$paramArray['prodclass_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "货品类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->库存相关设置->货品类型", '编辑货品类型', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '货品类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除货品类型
    function delProdclassAction($paramArray)
    {
        $ProdtypeOne = $this->DataControl->getFieldOne("smc_code_prodclass", "prodclass_code", "prodclass_id = '{$paramArray['prodclass_id']}'");
        if ($ProdtypeOne) {
            $ErpgoodsOne = $this->DataControl->getFieldOne("smc_code_prodtype", "prodtype_id", "prodclass_code = '{$ProdtypeOne['prodclass_code']}'");
            if ($ErpgoodsOne) {
                if ($paramArray['re_postbe_id'] !== '0') {
                    ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
                }
                ajax_return(array('error' => 1, 'errortip' => "货品类型已被使用，无法删除!"));
            }
            if ($this->DataControl->delData("smc_code_prodclass", "prodclass_id = '{$paramArray['prodclass_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除货品类别成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->库存相关设置->货品类型", '删除货品类型', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除货品类别失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getProdClass($paramArray)
    {
        $sql = "
            SELECT prodclass_code,prodclass_name from smc_code_prodclass WHERE  company_id = '{$paramArray['company_id']}'
            ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["prodclass_code"] = "货品类型编号";
        $field["prodclass_name"] = "货品类型名称";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看失败', 'result' => $result);
        }
        return $res;
    }

    //获取集团商务产品列表
    function getApppropermisList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.apppropermis_name like '%{$paramArray['keyword']}%' or a.apppropermis_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.apppropermis_id,
                a.apppropermis_code,
                a.apppropermis_remk,
                a.apppropermis_name 
            FROM
                smc_code_apppropermis AS a 
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY
                a.apppropermis_id DESC    
            LIMIT {$pagestart},{$num}";

        $apppropermisList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(a.apppropermis_id) as a
            FROM
                smc_code_apppropermis AS a
            WHERE
                {$datawhere} AND a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('apppropermis_name ', 'apppropermis_code', 'apppropermis_remk');
        $fieldname = $this->LgArraySwitch(array('产品权限名称', '产品权限代码', '产品权限备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($apppropermisList) {
            $result['list'] = $apppropermisList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无集团商务产品信息", 'result' => $result);
        }

        return $res;
    }

    //添加集团商务产品
    function addApppropermisAction($paramArray)
    {
        $data = array();
        $data['apppropermis_name'] = $paramArray['apppropermis_name'];
        $data['apppropermis_code'] = $paramArray['apppropermis_code'];
        $data['apppropermis_remk'] = $paramArray['apppropermis_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['apppropermis_name'] = "产品权限代码";
        $field['apppropermis_code'] = "产品权限名称";
        $field['apppropermis_remk'] = "产品权限备注";
        $field['company_id'] = "所属公司";

        $apppropermis_code = $this->DataControl->getFieldOne('smc_code_apppropermis', 'apppropermis_id', "apppropermis_code = '{$paramArray['apppropermis_code']}'");
        if ($apppropermis_code) {
            ajax_return(array('error' => 1, 'errortip' => "产品权限代码已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_apppropermis', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加集团商务产品成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置->集团商务产品", '添加集团商务产品', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加集团商务产品失败', 'result' => $result);
        }
        return $res;
    }

    //编辑集团商务产品
    function updateApppropermisAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("smc_code_apppropermis", "apppropermis_id", "apppropermis_id = '{$paramArray['apppropermis_id']}'");
        if ($postOne) {
            $data = array();
            $data['apppropermis_name'] = $paramArray['apppropermis_name'];
            $data['apppropermis_code'] = $paramArray['apppropermis_code'];
            $data['apppropermis_remk'] = $paramArray['apppropermis_remk'];

            $field = array();
            $field['apppropermis_name'] = "产品权限代码";
            $field['apppropermis_code'] = "产品权限名称";
            $field['apppropermis_remk'] = "产品权限备注";

            $apppropermis_code = $this->DataControl->getFieldOne('smc_code_apppropermis', 'apppropermis_code', "apppropermis_id = '{$paramArray['apppropermis_id']}'");
            if ($paramArray['apppropermis_code'] != $apppropermis_code['apppropermis_code']) {
                $apppropermis_code = $this->DataControl->getFieldOne('smc_code_apppropermis', 'apppropermis_id', "apppropermis_code = '{$paramArray['apppropermis_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($apppropermis_code) {
                    ajax_return(array('error' => 1, 'errortip' => "产品权限代码已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_apppropermis", "apppropermis_id = '{$paramArray['apppropermis_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑集团商务产品成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置->集团商务产品", '编辑集团商务产品', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑集团商务产品失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除集团商务产品
    function delApppropermisAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("smc_code_apppropermis", "apppropermis_id", "apppropermis_id = '{$paramArray['apppropermis_id']}'");
        if ($postOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_apppropermis", "apppropermis_id = '{$paramArray['apppropermis_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除集团商务产品成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置->集团商务产品", '删除集团商务产品', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除集团商务产品失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //获取企业信息列表
    function getCompaniesList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.companies_cnname like '%{$paramArray['keyword']}%' or a.companies_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.companies_id,
                a.companies_cnname,
                a.companies_password,
                a.companies_branch,
                a.companies_cmbappid,
                a.companies_upcappid,
                a.companies_isInvoice,
                a.companies_supervisebank,
                a.companies_settlebank,
                a.companies_invoice_appid,
                a.companies_invoice_payee,
                a.companies_invoice_reviewer,
                a.companies_invoice_drawer,
                a.companies_invoice_appid,
                a.companies_invoice_ird,
                a.companies_invoice_machineno,
                a.companies_invoice_key,
                a.companies_invoice_address,
                a.companies_invoice_phone,
                a.companies_invoice_bankname,
                a.companies_invoice_bankcard,
                a.companies_invoice_payee,
                a.companies_invoice_reviewer,
                a.companies_signet,
                a.companies_liaison,
                a.companies_examine,
                a.companies_register,
                a.companies_permitbranch,
                a.companies_permitstday,
                a.companies_icp,
                a.companies_licensestday,
                a.companies_issupervise,
                a.companies_society,
                a.companies_kidbranch,
                a.companies_agencyid,
                a.companies_superviseaccount,
                a.companies_invoice_chl,
                a.companies_rate,
                a.companies_pricetype,
                a.companies_pricecode,
                a.companies_subMerId,
                a.companies_settleaccount,b.companies_appid,b.companies_secret,b.companies_publicKey,b.companies_privateKey,a.companies_chargchannel,a.companies_cmbheadappid
            FROM
                gmc_code_companies AS a
            left join gmc_code_companies_seting as b on a.companies_id=b.companies_id
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY
                a.companies_id DESC  ";
        $status = $this->LgArraySwitch(array("0" => "纸质发票", "1" => "电子发票"));


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['companies_branch'] = $dateexcelvar['companies_branch'];
                    $datearray['companies_cmbappid'] = $dateexcelvar['companies_cmbappid'];
                    $datearray['companies_upcappid'] = $dateexcelvar['companies_upcappid'];
                    $datearray['companies_isInvoice_name'] = $status[$dateexcelvar['companies_isInvoice']];
                    $datearray['companies_password'] = $dateexcelvar['companies_password'];
                    $datearray['companies_issupervise'] = $dateexcelvar['companies_issupervise'];
                    $datearray['companies_licensestday'] = $dateexcelvar['companies_licensestday'];
                    $datearray['companies_society'] = $dateexcelvar['companies_society'];

                    $datearray['companies_examine'] = $dateexcelvar['companies_examine'];//审批机关
                    $datearray['companies_register'] = $dateexcelvar['companies_register'];//登记注册机关
                    $datearray['companies_permitbranch'] = $dateexcelvar['companies_permitbranch'];//办学许可证编号
                    $datearray['companies_permitstday'] = $dateexcelvar['companies_permitstday'];//办学许可证有效期
                    $datearray['companies_icp'] = $dateexcelvar['companies_icp'];//线上机构ICP备案号
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array('企业名称', '营业执照编号', '招行商户应用ID', '银联商户应用ID', '发票开具类型', '交易密码', '是否监管', '民非登记证/营业执照有效期', '统一社会信用代码','审批机关','登记注册机关','办学许可证编号','办学许可证有效期','线上机构ICP备案号'));
            $excelfileds = array('companies_cnname', 'companies_branch', 'companies_cmbappid', 'companies_upcappid', 'companies_isInvoice_name', 'companies_password', 'companies_issupervise', 'companies_licensestday', 'companies_society','companies_examine','companies_register','companies_permitbranch','companies_permitstday','companies_icp');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("主体信息明细表.xlsx"));
            exit;
        } else {
            $sql .= " LIMIT {$pagestart},{$num}";
            $companiesList = $this->DataControl->selectClear($sql);
            if ($companiesList) {
                foreach ($companiesList as &$val) {
                    $val['companies_isInvoice_name'] = $status[$val['companies_isInvoice']];
                }
            }

            $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(a.companies_id) as a
            FROM
                gmc_code_companies AS a
            WHERE
                {$datawhere} AND a.company_id = '{$paramArray['company_id']}'");
            $allnums = $all_num[0]['a'];

            $fieldstring = array('companies_cnname', 'companies_branch', 'companies_cmbappid', 'companies_upcappid', 'companies_isInvoice_name', 'companies_password', 'companies_issupervise');
            $fieldname = $this->LgArraySwitch(array('企业名称', '营业执照编号', '招行商户应用ID', '银联商户应用ID', '发票开具类型', '交易密码', '是否监管'));
            $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "1", "1", "1", "1", "1");
            $fieldisswich = array("0", "0", "0", "0", "0", "0", "1");

            $field = array();
            for ($i = 0; $i < count($fieldstring); $i++) {
                $field[$i]["fieldname"] = trim($fieldstring[$i]);
                $field[$i]["fieldstring"] = trim($fieldname[$i]);
                $field[$i]["custom"] = trim($fieldcustom[$i]);
                $field[$i]["show"] = trim($fieldshow[$i]);
                $field[$i]["switch"] = trim($fieldisswich[$i]);
            }

            $result = array();
            $result['fieldcustom'] = 0;
            $result['field'] = $field;
            $result['all_num'] = $allnums;

            if ($companiesList) {
                $result['list'] = $companiesList;
                $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
            } else {
                $result['list'] = array();
                $res = array('error' => '1', 'errortip' => "暂无企业主体信息管理", 'result' => $result);
            }

            return $res;
        }
    }

    //添加企业信息
    function addCompaniesAction($paramArray)
    {

        $companies_cnname = $this->DataControl->getFieldOne('gmc_code_companies', 'companies_id', "companies_cnname = '{$paramArray['companies_cnname']}' and company_id = '{$paramArray['company_id']}'");
        if ($companies_cnname) {
            ajax_return(array('error' => 1, 'errortip' => "企业名称已存在!"));
        }

        $data = array();
        $data['companies_cnname'] = $paramArray['companies_cnname'];
        $data['companies_supervisebank'] = $paramArray['companies_supervisebank'];
        $data['companies_settlebank'] = $paramArray['companies_settlebank'];
        $data['companies_branch'] = $paramArray['companies_branch'];
        $data['companies_upcappid'] = $paramArray['companies_upcappid'];
        $data['companies_invoice_appid'] = $paramArray['companies_invoice_appid'];
        $data['companies_invoice_key'] = $paramArray['companies_invoice_key'];
        $data['companies_invoice_machineno'] = $paramArray['companies_invoice_machineno'];
        $data['companies_invoice_ird'] = $paramArray['companies_invoice_ird'];
        $data['companies_invoice_address'] = $paramArray['companies_invoice_address'];
        $data['companies_invoice_phone'] = $paramArray['companies_invoice_phone'];
        $data['companies_invoice_bankname'] = $paramArray['companies_invoice_bankname'];
        $data['companies_invoice_bankcard'] = $paramArray['companies_invoice_bankcard'];
        $data['companies_invoice_drawer'] = $paramArray['companies_invoice_drawer'];
        $data['companies_invoice_payee'] = $paramArray['companies_invoice_payee'];
        $data['companies_invoice_reviewer'] = $paramArray['companies_invoice_reviewer'];
        $data['companies_isInvoice'] = $paramArray['companies_isInvoice'];
        $data['companies_signet'] = $paramArray['companies_signet'];
        $data['companies_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];
        $data['companies_liaison'] = $paramArray['companies_liaison'];
        $data['companies_examine'] = $paramArray['companies_examine'];
        $data['companies_register'] = $paramArray['companies_register'];
        $data['companies_permitbranch'] = $paramArray['companies_permitbranch'];
        $data['companies_permitstday'] = $paramArray['companies_permitstday'];
        $data['companies_icp'] = $paramArray['companies_icp'];
        $data['companies_licensestday'] = $paramArray['companies_licensestday'];
        $data['companies_society'] = $paramArray['companies_society'];
        $data['companies_issupervise'] = $paramArray['companies_issupervise'];
        $data['companies_agencyid'] = $paramArray['companies_agencyid'];
        $data['companies_kidbranch'] = $paramArray['companies_kidbranch'];
        $data['companies_password'] = $paramArray['companies_password'];
        $data['companies_superviseaccount'] = $paramArray['companies_superviseaccount'];
        $data['companies_settleaccount'] = $paramArray['companies_settleaccount'];
        $data['companies_rate'] = $paramArray['companies_rate'];
        $data['companies_pricecode'] = $paramArray['companies_pricecode'];
        $data['companies_pricetype'] = $paramArray['companies_pricetype'];
        $data['companies_invoice_chl'] = $paramArray['companies_invoice_chl'];
        $data['companies_subMerId'] = $paramArray['companies_subMerId'];

        if(isset($paramArray['companies_chargchannel']) && $paramArray['companies_chargchannel']!='' && $paramArray['companies_chargchannel']=='cmbheadbank'){

            $data['companies_chargchannel'] = 'cmbheadbank';
            $data['companies_cmbheadappid'] = $paramArray['companies_cmbheadappid'];
        }elseif(isset($paramArray['companies_chargchannel']) && $paramArray['companies_chargchannel']!='' && $paramArray['companies_chargchannel']=='cmbmergebank'){
            $data['companies_chargchannel'] = 'cmbmergebank';
        }else{
            $data['companies_chargchannel'] = 'cmbbank';
            $data['companies_cmbappid'] = $paramArray['companies_cmbappid'];
        }

        $field = array();
        $field['companies_cnname'] = "企业名称";
        $field['companies_branch'] = "商户编号";
        $field['companies_cmbappid'] = "招行商户应用ID";
        $field['companies_upcappid'] = "银联商户应用ID";
        $field['companies_invoice_appid'] = "发票-商户密码";
        $field['companies_invoice_drawer'] = "发票-开票人";
        $field['companies_invoice_payee'] = "发票-收款人";
        $field['companies_invoice_reviewer'] = "发票-复核人";
        $field['companies_createtime'] = "发布时间";
        $field['company_id'] = "所属公司";


//        $companies_branch=$this->DataControl->getFieldOne('gmc_code_companies', 'companies_id', "companies_branch = '{$paramArray['companies_branch']}' and company_id = '{$paramArray['company_id']}'");
//        if($companies_branch){
//            ajax_return(array('error' => 1,'errortip' => "企业编号已存在!"));
//        }

        if ($companies_id=$this->DataControl->insertData('gmc_code_companies', $data)) {

            if(isset($paramArray['companies_chargchannel']) && $paramArray['companies_chargchannel']!='' && $paramArray['companies_chargchannel']=='cmbheadbank'){

                $tem_data=array();
                $tem_data['companies_id']=$companies_id;
                $tem_data['companies_appid']=$paramArray['companies_appid'];
                $tem_data['companies_secret']=$paramArray['companies_secret'];
                $tem_data['companies_publicKey']=$paramArray['companies_publicKey'];
                $tem_data['companies_privateKey']=$paramArray['companies_privateKey'];
                $this->DataControl->insertData("gmc_code_companies_seting",$tem_data);
            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加企业信息成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置->企业主体信息管理", '添加企业主体信息', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加企业信息失败', 'result' => $result);
        }
        return $res;
    }

    //编辑企业信息
    function updateCompaniesAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_id", "companies_id = '{$paramArray['companies_id']}'");
        if ($postOne) {

            $companies_cnname = $this->DataControl->getFieldOne('gmc_code_companies', 'companies_cnname', "companies_id = '{$paramArray['companies_id']}'");
            if ($paramArray['companies_cnname'] != $companies_cnname['companies_cnname']) {
                $companies_cnname = $this->DataControl->getFieldOne('gmc_code_companies', 'companies_id', "companies_cnname = '{$paramArray['companies_cnname']}' and company_id = '{$paramArray['company_id']}'");
                if ($companies_cnname) {
                    ajax_return(array('error' => 1, 'errortip' => "企业名称已存在!"));
                }
            }

            $data = array();
            $data['companies_cnname'] = $paramArray['companies_cnname'];
            $data['companies_branch'] = $paramArray['companies_branch'];
            $data['companies_supervisebank'] = $paramArray['companies_supervisebank'];
            $data['companies_settlebank'] = $paramArray['companies_settlebank'];
            $data['companies_upcappid'] = $paramArray['companies_upcappid'];
            $data['companies_invoice_appid'] = $paramArray['companies_invoice_appid'];
            $data['companies_invoice_drawer'] = $paramArray['companies_invoice_drawer'];
            $data['companies_invoice_payee'] = $paramArray['companies_invoice_payee'];
            $data['companies_invoice_reviewer'] = $paramArray['companies_invoice_reviewer'];
            $data['companies_isInvoice'] = $paramArray['companies_isInvoice'];
            $data['companies_invoice_key'] = $paramArray['companies_invoice_key'];
            $data['companies_invoice_machineno'] = $paramArray['companies_invoice_machineno'];
            $data['companies_invoice_ird'] = $paramArray['companies_invoice_ird'];
            $data['companies_invoice_address'] = $paramArray['companies_invoice_address'];
            $data['companies_invoice_phone'] = $paramArray['companies_invoice_phone'];
            $data['companies_invoice_bankname'] = $paramArray['companies_invoice_bankname'];
            $data['companies_invoice_bankcard'] = $paramArray['companies_invoice_bankcard'];
            $data['companies_signet'] = $paramArray['companies_signet'];
            $data['companies_updatatime'] = time();
            $data['company_id'] = $paramArray['company_id'];
            $data['companies_liaison'] = $paramArray['companies_liaison'];
            $data['companies_examine'] = $paramArray['companies_examine'];
            $data['companies_register'] = $paramArray['companies_register'];
            $data['companies_permitbranch'] = $paramArray['companies_permitbranch'];
            $data['companies_permitstday'] = $paramArray['companies_permitstday'];
            $data['companies_icp'] = $paramArray['companies_icp'];
            $data['companies_licensestday'] = $paramArray['companies_licensestday'];
            $data['companies_society'] = $paramArray['companies_society'];
            $data['companies_issupervise'] = $paramArray['companies_issupervise'];
            $data['companies_agencyid'] = $paramArray['companies_agencyid'];
            $data['companies_kidbranch'] = $paramArray['companies_kidbranch'];
            $data['companies_password'] = $paramArray['companies_password'];
            $data['companies_superviseaccount'] = $paramArray['companies_superviseaccount'];
            $data['companies_settleaccount'] = $paramArray['companies_settleaccount'];
            $data['companies_rate'] = $paramArray['companies_rate'];
            $data['companies_pricecode'] = $paramArray['companies_pricecode'];
            $data['companies_pricetype'] = $paramArray['companies_pricetype'];
            $data['companies_invoice_chl'] = $paramArray['companies_invoice_chl'];

            if(isset($paramArray['companies_chargchannel']) && $paramArray['companies_chargchannel']!='' && $paramArray['companies_chargchannel']=='cmbheadbank'){

                $data['companies_chargchannel'] = 'cmbheadbank';
                $data['companies_cmbheadappid'] = $paramArray['companies_cmbheadappid'];
            }elseif(isset($paramArray['companies_chargchannel']) && $paramArray['companies_chargchannel']!='' && $paramArray['companies_chargchannel']=='cmbmergebank'){
                $data['companies_chargchannel'] = 'cmbmergebank';
            }else{
                $data['companies_chargchannel'] = 'cmbbank';
                $data['companies_cmbappid'] = $paramArray['companies_cmbappid'];
            }

            $data['companies_subMerId'] = $paramArray['companies_subMerId'];

            $field = array();
            $field['companies_cnname'] = "企业名称";
            $field['companies_branch'] = "商户编号";
            $field['companies_cmbappid'] = "招行商户应用ID";
            $field['companies_upcappid'] = "银联商户应用ID";
            $field['companies_invoice_appid'] = "发票-商户密码";
            $field['companies_invoice_drawer'] = "发票-开票人";
            $field['companies_invoice_payee'] = "发票-收款人";
            $field['companies_invoice_reviewer'] = "发票-复核人";
            $field['companies_updatatime'] = "发布时间";
            $field['company_id'] = "所属公司";


//            $companies_branch=$this->DataControl->getFieldOne('gmc_code_companies', 'companies_branch', "companies_id = '{$paramArray['companies_id']}'");
//            if($paramArray['companies_branch']!=$companies_branch['companies_branch'] ){
//                $companies_branch=$this->DataControl->getFieldOne('gmc_code_companies', 'companies_id', "companies_branch = '{$paramArray['companies_branch']}' and company_id = '{$paramArray['company_id']}'");
//                if($companies_branch){
//                    ajax_return(array('error' => 1,'errortip' => "企业编号已存在!"));
//                }
//            }

            if ($this->DataControl->updateData("gmc_code_companies", "companies_id = '{$paramArray['companies_id']}'", $data)) {

                if(isset($paramArray['companies_chargchannel']) && $paramArray['companies_chargchannel']!='' && $paramArray['companies_chargchannel']=='cmbheadbank'){

                    if($this->DataControl->getFieldOne("gmc_code_companies_seting","seting_id","companies_id='{$paramArray['companies_id']}'")){
                        $tem_data=array();
                        $tem_data['companies_appid']=$paramArray['companies_appid'];
                        $tem_data['companies_secret']=$paramArray['companies_secret'];
                        $tem_data['companies_publicKey']=$paramArray['companies_publicKey'];
                        $tem_data['companies_privateKey']=$paramArray['companies_privateKey'];
                        $this->DataControl->updateData("gmc_code_companies_seting","companies_id='{$paramArray['companies_id']}'",$tem_data);
                    }else{
                        $tem_data=array();
                        $tem_data['companies_id']=$paramArray['companies_id'];
                        $tem_data['companies_appid']=$paramArray['companies_appid'];
                        $tem_data['companies_secret']=$paramArray['companies_secret'];
                        $tem_data['companies_publicKey']=$paramArray['companies_publicKey'];
                        $tem_data['companies_privateKey']=$paramArray['companies_privateKey'];
                        $this->DataControl->insertData("gmc_code_companies_seting",$tem_data);
                    }
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑企业信息成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置->企业主体信息管理", '编辑企业主体信息', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑企业信息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除企业信息
    function delCompaniesAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_id", "companies_id = '{$paramArray['companies_id']}'");
        if ($postOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("gmc_code_companies", "companies_id = '{$paramArray['companies_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除企业信息成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团相关设置->企业主体信息管理", '删除企业主体信息', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除企业信息失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }
//
//    //获取库存查询列表
//    function getRepertoryList($paramArray)
//    {
//
//        $datawhere = " 1 ";
//
//        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
//            $datawhere .= " and (g.goods_outpid like '%{$paramArray['keyword']}%' or g.goods_cnname like '%{$paramArray['keyword']}%')";
//        }
//        if (isset($paramArray['warehouse_id']) && $paramArray['warehouse_id'] !== "") {
//            $datawhere .= " and w.warehouse_id ='{$paramArray['warehouse_id']}'";
//        }
//        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
//            $page = $paramArray['p'];
//        } else {
//            $page = '1';
//        }
//        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
//            $num = $paramArray['num'];
//        } else {
//            $num = '10';
//        }
//        $pagestart = ($page - 1) * $num;
//
//        $sql = "
//            SELECT
//                r.repertory_id,
//                r.goods_repertory,
//                r.goods_originalprice,
//                r.goods_price,
//                w.warehouse_name,
//                g.goods_cnname,
//                g.goods_outpid
//            FROM
//                erp_goods_repertory AS r
//                LEFT JOIN erp_code_warehouse AS w ON r.warehouse_id = w.warehouse_id
//                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
//            WHERE
//                {$datawhere} and w.company_id = '{$paramArray['company_id']}'
//            ORDER BY
//                r.repertory_id DESC
//            LIMIT {$pagestart},{$num}";
//
//        $repertoryList = $this->DataControl->selectClear($sql);
//
//        $all_num = $this->DataControl->selectClear("
//            SELECT
//               COUNT(r.repertory_id) as a
//            FROM
//                erp_goods_repertory AS r
//                LEFT JOIN erp_code_warehouse AS w ON r.warehouse_id = w.warehouse_id
//                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
//            WHERE
//                {$datawhere} AND w.company_id = '{$paramArray['company_id']}'");
//        $allnums = $all_num[0]['a'];
//
//        $fieldstring = array('goods_outpid ', 'goods_cnname', 'warehouse_name', 'goods_originalprice', 'goods_price', 'goods_repertory');
//        $fieldname = array('K3货号', '货品名称', '所属仓库', '原价', '销售价', '库存');
//        $fieldcustom = array("1", "1", "1", "1", "1", "1");
//        $fieldshow = array("1", "1", "1", "1", "1", "1");
//
//        $field = array();
//        for ($i = 0; $i < count($fieldstring); $i++) {
//            $field[$i]["fieldname"] = trim($fieldstring[$i]);
//            $field[$i]["fieldstring"] = trim($fieldname[$i]);
//            $field[$i]["custom"] = trim($fieldcustom[$i]);
//            $field[$i]["show"] = trim($fieldshow[$i]);
//        }
//
//        $result = array();
//        $result['fieldcustom'] = 0;
//        $result['field'] = $field;
//
//        if ($repertoryList) {
//            $result['list'] = $repertoryList;
//        } else {
//            $result['list'] = array();
//        }
//
//        $result['all_num'] = $allnums;
//
//        $result['warehouse'] = $this->DataControl->selectClear("select warehouse_id,warehouse_name from erp_code_warehouse where company_id = '{$paramArray['company_id']}'");
//
//        if ($result['list']) {
//            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
//        } else {
//            $res = array('error' => '1', 'errortip' => "暂无库存信息", 'result' => $result);
//        }
//
//        return $res;
//    }

    //采购管理列表（校业助）
    function getScAssistantList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.proorder_pid like '%{$paramArray['keyword']}%' or st.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and t.school_id ={$paramArray['school_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.proorder_id,
                t.school_id,
                p.proorder_pid,
                p.erpstatus_id,
                p.proorder_from,
                p.proorder_from as proorder_from_name,
                ( SELECT sum( proogoods_buynums ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = t.proorder_pid ) AS nums,
                ( SELECT sum( proogoods_payprice ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = t.proorder_pid ) AS price,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
	            e.erpstatus_gmcname,
	            (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
	            st.staffer_cnname
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_to AS t ON t.proorder_pid = p.proorder_pid
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
                left join smc_school as s on s.school_id = t.school_id
                left join smc_staffer as st on st.staffer_id = p.staffer_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']}
            ORDER BY
                p.proorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "系统自订", "1" => "教师下单"));
            foreach ($goodsList as &$val) {
                $val['proorder_from_name'] = $status[$val['proorder_from_name']];
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.proorder_id) as a
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_to AS t ON t.proorder_pid = p.proorder_pid
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
                left join smc_school as s on s.school_id = t.school_id
                left join smc_staffer as st on st.staffer_id = p.staffer_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']}");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('proorder_pid ', 'school_cnname ', 'proorder_from_name', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'erpstatus_gmcname');
        $fieldname = $this->LgArraySwitch(array('采购编号', '采购分校名称', '采购类型', '采购货品数量', '采购货品金额', '订购申请日期', '执行人', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['school'] = $this->DataControl->selectClear("select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}' and school_isclose = '0'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无采购订单信息", 'result' => $result);
        }

        return $res;
    }


    //审核学校采购订单
    function ExamineScAction($paramArray)
    {
        $proorderOne = $this->DataControl->getFieldOne("erp_proorder", "proorder_pid", "proorder_id = '{$paramArray['proorder_id']}'");
        if ($proorderOne) {
            $data = array();
            if ($paramArray['erpstatus_id'] == '4') {
                $data['erpstatus_id'] = '3';
            }
            if ($paramArray['erpstatus_id'] == '3') {
                $data['erpstatus_id'] = '6';
            }
            if ($paramArray['erpstatus_id'] == '6') {
                $data['erpstatus_id'] = '5';
            }
            if ($paramArray['erpstatus_id'] == '5') {
                $data['erpstatus_id'] = '7';
            }
            if ($paramArray['erpstatus_id'] == '7') {
                $data['erpstatus_id'] = '8';
            }


            $field = array();
            $field['erpstatus_id'] = "ERP订单最新状态ID";

            if ($this->DataControl->updateData("erp_proorder", "proorder_id = '{$paramArray['proorder_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;

                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");

                $dataTracks = array();
                if ($paramArray['erpstatus_id'] == '7') {
                    $dataTracks['tracks_title'] = $this->LgStringSwitch('发货');
                } else {
                    $dataTracks['tracks_title'] = $this->LgStringSwitch('审核订单');
                }
                if ($paramArray['erpstatus_id'] == '4') {
                    $dataTracks['tracks_information'] = $this->LgStringSwitch('校业助审核订单');
                }
                if ($paramArray['erpstatus_id'] == '3') {
                    $dataTracks['tracks_information'] = $this->LgStringSwitch('校财务审核订单');
                }
                if ($paramArray['erpstatus_id'] == '6') {
                    $dataTracks['tracks_information'] = $this->LgStringSwitch('集团财务审核订单');
                }
                if ($paramArray['erpstatus_id'] == '5') {
                    $dataTracks['tracks_information'] = $this->LgStringSwitch('集团业助审核订单');
                }
                if ($paramArray['erpstatus_id'] == '7') {
                    $dataTracks['tracks_information'] = $this->LgStringSwitch('集团业务助理发货');
                }
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['proorder_pid'] = $proorderOne['proorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_proorder_tracks', $dataTracks);

                $res = array('error' => '0', 'errortip' => "订单最新状态修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '订单最新状态修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //采购货品明细
    function getGoodsDetailList($paramArray)
    {
        $datawhere = "1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.goods_cnname like '%{$paramArray['keyword']}%' or s.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                s.goods_pid,
                s.goods_cnname,
                s.goods_enname,
                s.goods_unit,
                s.goods_vipprice,
                p.prodtype_name,
                g.proogoods_buynums,
                g.proogoods_unitprice
            FROM
                erp_proorder_goods AS g
                LEFT JOIN erp_proorder_to AS t ON g.proorder_pid = t.proorder_pid
                left join erp_goods as s on g.goods_id = s.goods_id
                left join smc_code_prodtype as p on s.prodtype_code = p.prodtype_code
            WHERE {$datawhere} and g.proorder_pid = '{$paramArray['proorder_pid']}'
            ORDER BY
                g.proogoods_id DESC
                limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['price'] = $val['proogoods_buynums'] * $val['goods_vipprice'];
            }

        }

        $all_num = count($goodsList);

        $fieldstring = array('goods_pid ', 'goods_cnname', 'prodtype_name', 'goods_unit', 'goods_vipprice', 'proogoods_buynums', 'price');
        $fieldname = $this->LgArraySwitch(array('货品编号', '货品名称', '货品类别', '单位', '协议价', '调拨数量', '金额小计'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $all_num;


        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //采购单跟踪记录
    function getGoodsTracksList($paramArray)
    {
        $sql = "
            SELECT
                s.tracks_title,
                s.tracks_information,
                s.tracks_playname,
                FROM_UNIXTIME(s.tracks_createtime,'%Y-%m-%d %H:%i:%s') as tracks_createtime
            FROM
                erp_proorder_tracks AS s
            WHERE s.proorder_pid = '{$paramArray['proorder_pid']}'
            ORDER BY
                s.tracks_id DESC";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('tracks_title ', 'tracks_playname', 'tracks_information', 'tracks_createtime');
        $fieldname = $this->LgArraySwitch(array('操作内容', '操作人', '备注', '操作时间'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //创建入库单
    function CreateBeinorderAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['beinorder_from'] = $paramArray['proorder_from'];
        $data['beinorder_outpid'] = $paramArray['proorder_pid'];
        $data['beinorder_pid'] = str_pad(mt_rand(*********, *********), 9, "0", STR_PAD_BOTH);;
        $data['beinorder_status'] = '0';
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['beinorder_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属公司";
        $field['school_id'] = "校园ID";

        if ($id = $this->DataControl->insertData('smc_erp_beinorder', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $goodsList = $this->DataControl->selectClear("select goods_id,proogoods_buynums from erp_proorder_goods WHERE proorder_pid = '{$paramArray['proorder_pid']}'");

//            var_dump($goodsList);die();
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['beinordergoods_buynums'] = $item['proogoods_buynums'];
                $datas['beinorder_pid'] = $data['beinorder_pid'];

                $this->DataControl->insertData('smc_erp_beinorder_goods', $datas);
            }

            $datass = array();
            $datass['beinorder_pid'] = $data['beinorder_pid'];

            $this->DataControl->updateData("erp_proorder_to", "proorder_pid = '{$paramArray['proorder_pid']}'", $datass);


            $res = array('error' => '0', 'errortip' => "创建订单成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => "创建订单失败", 'result' => $result);
        }
        return $res;
    }

    //校财务输入流水号
    function paynumberAction($paramArray)
    {
        $data = array();
        $data['proorder_paymennote'] = $data['proorder_paymennote'];
        $data['proorder_paynumber'] = $data['proorder_paynumber'];
        $data['proorder_paytime'] = time();

        $field = array();
        $field['proorder_paymennote'] = "备注";
        $field['proorder_paynumber'] = "支付流水号";
        $field['proorder_paytime'] = "支付时间";

        if ($this->DataControl->updateData("erp_proorder", "proorder_id = '{$paramArray['proorder_id']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "处理成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '处理失败', 'result' => $result);
        }

        return $res;
    }

    //调拨分校列表
    function getApplytypeApi($paramArray)
    {
        $sql = "select school_id,(case when school_shortname='' then school_cnname else school_shortname end) as school_cnname from smc_school where company_id = '{$paramArray['company_id']}' 
                ORDER BY (case when school_istest=0 and school_isclose=0 then 1 when school_isclose=0 then 2 when school_istest=0 then 3 else 4 end),school_istest asc,field(school_sort,0),school_sort asc,school_createtime asc";
        $ApplytypeDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["school_cnname"] = "校区名称";
        $result = array();
        if ($ApplytypeDetail) {
            $result["field"] = $field;
            $result["data"] = $ApplytypeDetail;
            $res = array('error' => '0', 'errortip' => '调拨分校列表查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '调拨分校列表查看失败', 'result' => $result);
        }
        return $res;
    }

    //学校库存列表
    function getChangeGoodsList($paramArray)
    {

        $datawhere = "1";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or  g.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ={$paramArray['prodtype_code']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_cnname,
                g.goods_pid,
                g.goods_unit,
                g.goods_originalprice,
                g.goods_vipprice,
                r.goods_repertory,
                p.prodtype_name,
                r.goods_id
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
                WHERE {$datawhere} and r.school_id = '{$paramArray['school_id']}'
            ORDER BY
                r.repertory_id DESC 
                LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        foreach ($goodsList as &$val) {
            $val['number'] = '1';
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(r.repertory_id) as a
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                left join smc_code_prodtype as p on p.prodtype_code = g.prodtype_code
            WHERE
                {$datawhere} and r.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_unit', 'prodtype_name', 'goods_originalprice', 'goods_vipprice', 'goods_repertory', 'number');
        $fieldname = $this->LgArraySwitch(array('货品编号', '货品名称', '单位', '类别', '成本价', '协议价', '本校库存', '调拨数量'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldisInputText = array(false, false, false, false, false, false, false, true);


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isInputNumber"] = trim($fieldisInputText[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_id,prodtype_name,prodtype_code from smc_code_prodtype where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无调拨货品信息", 'result' => $result);
        }

        return $res;
    }

    //调拨商品
    function ChangeGoodsAction($paramArray)
    {
        $data2 = array();
        $data2['company_id'] = $paramArray['company_id'];
        $data2['from_school_id'] = $paramArray['from_school_id'];
        $data2['to_school_id'] = $paramArray['to_school_id'];
        $data2['staffer_id'] = $paramArray['staffer_id'];
        $data2['proorder_allprice'] = $paramArray['price'];
        $data2['proorder_from'] = '3';
        $data2['proorder_status'] = '0';
        $data2['proorder_pid'] = $this->createOrderPid('DB');;
        $data2['proorder_createtime'] = time();

        $field = array();
        $field['company_id'] = "所属公司";
        $field['from_school_id'] = "调出学校";
        $field['to_school_id'] = "调入学校";
        $field['staffer_id'] = "操作人";
        $field['proorder_allprice'] = "总价";

        $this->DataControl->insertData('erp_proorder', $data2);

        $datas = array();

        $goodsList = json_decode(stripslashes($paramArray['goods']), true);
        foreach ($goodsList as $item) {
            $datas['proorder_pid'] = $data2['proorder_pid'];
            $datas['goods_id'] = $item['goods_id'];
            $datas['proogoods_buynums'] = $item['count'];
            $price = $this->DataControl->getFieldOne("erp_goods", "goods_vipprice", "goods_id = '{$item['goods_id']}'");
            $datas['proogoods_unitprice'] = $price['goods_vipprice'];
            $datas['proogoods_payprice'] = $price['goods_vipprice'] * $item['count'];

            $this->DataControl->insertData('erp_proorder_goods', $datas);
        }
        $result = array();
        $result['field'] = $field;
        $result['date'] = $data2;

        $datass = array();
        $datass['salesorder_pid'] = $this->createOrderPid('XH');
        $datass['company_id'] = $paramArray['company_id'];
        $datass['school_id'] = $paramArray['from_school_id'];
        $datass['to_school_id'] = $paramArray['to_school_id'];
        $datass['proorder_pid'] = $data2['proorder_pid'];
        $datass['salesorder_from'] = '0';
        $datass['salesorder_createtime'] = time();
        $this->DataControl->insertData('smc_erp_salesorder', $datass);

        $datasss = array();
        foreach ($goodsList as $items) {
            $datasss['proorder_pid'] = $data2['proorder_pid'];
            $datasss['salesorder_pid'] = $datass['salesorder_pid'];
            $datasss['goods_id'] = $items['goods_id'];
            $datasss['salesordergoods_buynums'] = $items['count'];

            $this->DataControl->insertData('smc_erp_salesorder_goods', $datasss);
        }

        $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
        $dataTracks = array();
        $dataTracks['tracks_title'] = $this->LgStringSwitch('生成销货单');
        $dataTracks['tracks_information'] = $this->LgStringSwitch('--');
        $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
        $dataTracks['salesorder_pid'] = $datass['salesorder_pid'];
        $dataTracks['tracks_createtime'] = time();

        $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

        $res = array('error' => '0', 'errortip' => "调拨商品成功", 'result' => $result);
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->调拨管理", '新增调拨', dataEncode($paramArray));
        return $res;
    }

    //创建采购活动
    function addActivityBuyAction($paramArray)
    {
        $data = array();
        $data['activitybuy_name'] = $paramArray['activitybuy_name'];
        $data['activitybuy_stdate'] = $paramArray['activitybuy_stdate'];
        $data['activitybuy_enddate'] = $paramArray['activitybuy_enddate'];
        $data['activitybuy_apply'] = $paramArray['activitybuy_apply'];
        $data['company_id'] = $paramArray['company_id'];
        $data['activitybuy_createtime'] = time();

        $field = array();
        $field['activitybuy_name'] = "活动名称";
        $field['activitybuy_stdate'] = "活动开始日期";
        $field['activitybuy_enddate'] = "活动结束日期";
        $field['activitybuy_apply'] = "适用学校";
        $field['company_id'] = "所属公司";

        if ($id = $this->DataControl->insertData('gmc_company_activitybuy', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "创建采购活动成功", 'result' => $result, 'id' => $id);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->库存相关设置", '创建采购活动', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '创建采购活动失败', 'result' => $result);
        }
        return $res;
    }

    //获取活动商品列表
    function getActivityBuyGoodsList($paramArray)
    {
        $sql = "
            SELECT
                g.goods_id,
                g.goods_pid,
                g.goods_cnname,
                g.goods_vipprice 
            FROM
                erp_goods AS g
                LEFT JOIN gmc_activitybuy_goodsapply AS a ON g.goods_id = a.goods_id 
            WHERE
                a.activitybuy_id = '{$paramArray['activitybuy_id']}'";

        $companiesList = $this->DataControl->selectClear($sql);

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_vipprice', 'goods_id');
        $fieldname = $this->LgArraySwitch(array('K3货号', '商品名称', '商品单价', 'id'));
        $fieldcustom = array("1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($companiesList) {
            $result['list'] = $companiesList;
            $res = array('error' => '0', 'errortip' => "获取活动商品列表成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无活动商品列表", 'result' => $result);
        }

        return $res;
    }

    //选择货品列表
    function getChooseGoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or  g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ={$paramArray['prodtype_code']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_id,
                g.goods_cnname,
                g.goods_pid,
                g.goods_vipprice
            FROM
                erp_goods AS g
            WHERE 
                {$datawhere} and g.company_id = '{$paramArray['company_id']}'
            ORDER BY
                g.goods_id DESC 
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
                COUNT(g.goods_id) as a
            FROM
                erp_goods AS g
            WHERE
                {$datawhere} and g.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_vipprice', 'goods_id');
        $fieldname = $this->LgArraySwitch(array('K3货号', '中文名称', '学员售价', 'id'));
        $fieldcustom = array("1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "0");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['prodtype'] = $this->DataControl->selectClear("select prodtype_code,prodtype_name from smc_code_prodtype where company_id = '{$paramArray['company_id']}'");

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无货品信息", 'result' => $result);
        }

        return $res;
    }

    //添加活动商品
    function addActivityBuyGoodsAction($paramArray)
    {
        $data = array();

        $goodsList = json_decode(stripslashes($paramArray['goods']), true);
        foreach ($goodsList as $item) {
            $data['activitybuy_id'] = $item['activitybuy_id'];
            $data['goods_id'] = $item['goods_id'];

            $a = $this->DataControl->getFieldOne('gmc_activitybuy_goodsapply', 'goods_id', "goods_id = '{$item['goods_id']}' and activitybuy_id = '{$item['activitybuy_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"));
            }
            $this->DataControl->insertData('gmc_activitybuy_goodsapply', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加活动商品成功");
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->采购活动管理", '添加活动商品', dataEncode($paramArray));

        return $res;
    }

    //获取活动适用学校列表
    function getActivityBuySchoolList($paramArray)
    {
        $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_enname,
                d.district_cnname 
            FROM
                smc_school AS s
                LEFT JOIN gmc_activitybuy_schoolapply AS a ON a.school_id = s.school_id
                LEFT JOIN gmc_company_district AS d ON d.district_id = s.district_id 
            WHERE
                a.activitybuy_id = '{$paramArray['activitybuy_id']}'
            ORDER BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";

        $companiesList = $this->DataControl->selectClear($sql);

        $fieldstring = array('school_branch ', 'school_cnname', 'school_enname', 'district_cnname');
        $fieldname = $this->LgArraySwitch(array('校区编号', '校园名称', '检索代码', '区域'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $count = $this->DataControl->selectOne("select count(school_id) as a from gmc_activitybuy_schoolapply where activitybuy_id = '{$paramArray['activitybuy_id']}'");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['count'] = $count['a'];

        if ($companiesList) {
            $result['list'] = $companiesList;
            $res = array('error' => '0', 'errortip' => "获取活动适用学校列表成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无活动适用学校列表", 'result' => $result);
        }

        return $res;
    }

    //添加活动学校
    function addActivityBuySchoolAction($paramArray)
    {
        $data = array();

        $schoolList = json_decode(stripslashes($paramArray['school']), true);
        foreach ($schoolList as $item) {
            $data['activitybuy_id'] = $item['activitybuy_id'];
            $data['school_id'] = $item['school_id'];

            $a = $this->DataControl->getFieldOne('gmc_activitybuy_schoolapply', 'school_id', "school_id = '{$item['school_id']}' and activitybuy_id = '{$item['activitybuy_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "请勿重复添加"));
            }
            $this->DataControl->insertData('gmc_activitybuy_schoolapply', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加活动学校成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团架构->采购活动管理", '添加活动学校', dataEncode($paramArray));

        return $res;
    }

    //删除活动商品
    function delActivityBuyGoodsAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("gmc_activitybuy_goodsapply", "activitybuy_id", "activitybuy_id = '{$paramArray['activitybuy_id']}' and goods_id = '{$paramArray['goods_id']}'");
        if ($postOne) {
            if ($this->DataControl->delData("gmc_activitybuy_goodsapply", "activitybuy_id = '{$paramArray['activitybuy_id']}' and goods_id = '{$paramArray['goods_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除活动商品成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->库存相关设置", '删除活动商品', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除活动商品失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除活动学校
    function delActivityBuySchoolAction($paramArray)
    {
        $postOne = $this->DataControl->getFieldOne("gmc_activitybuy_schoolapply", "activitybuy_id", "activitybuy_id = '{$paramArray['activitybuy_id']}' and school_id = '{$paramArray['school_id']}'");
        if ($postOne) {
            if ($this->DataControl->delData("gmc_activitybuy_schoolapply", "activitybuy_id = '{$paramArray['activitybuy_id']}' and school_id = '{$paramArray['school_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除活动学校成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->库存相关设置", '删除活动学校', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除活动学校失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取采购活动列表
    function getActivityBuyList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.activitybuy_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.activitybuy_id,
                a.activitybuy_apply,
                a.activitybuy_name,
                a.activitybuy_stdate,
                a.activitybuy_enddate,
                ( select count( ga.activitybuy_id) from gmc_activitybuy_goodsapply as ga WHERE ga.activitybuy_id = a.activitybuy_id ) AS goodsnum,
                ( select count( sa.activitybuy_id) from gmc_activitybuy_schoolapply as sa WHERE sa.activitybuy_id = a.activitybuy_id ) AS schoolnum
            FROM
                gmc_company_activitybuy AS a
            WHERE 
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY
                a.activitybuy_id DESC 
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);
        $schoolall = $this->DataControl->selectOne("select count(*) as a from smc_school WHERE company_id = '{$paramArray['company_id']}'");

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                if ($val['activitybuy_apply'] == '0') {
                    $val['schoolnum'] = $schoolall['a'];
                }
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
                COUNT(a.activitybuy_id) as a
            FROM
                gmc_company_activitybuy AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('activitybuy_name ', 'activitybuy_stdate', 'activitybuy_enddate', 'goodsnum', 'schoolnum', 'activitybuy_apply');
        $fieldname = $this->LgArraySwitch(array('活动名称', '活动开始时间', '活动结束时间', '采购货品数量', '适配学校数量', '是否全部适用'));
        $fieldcustom = array("1", "1", "1", "1", "1", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "0");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取采购活动列表成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无采购活动信息", 'result' => $result);
        }

        return $res;
    }

    //删除采购活动
    function delActivityBuyAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("gmc_company_activitybuy", "activitybuy_id", "activitybuy_id = '{$paramArray['activitybuy_id']}'");
        if ($activityOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("gmc_company_activitybuy", "activitybuy_id = '{$paramArray['activitybuy_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除采购活动成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->库存相关设置", '删除采购活动', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除采购活动失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑采购活动
    function updateActivityBuyAction($paramArray)
    {
        $data = array();
        $data['activitybuy_name'] = $paramArray['activitybuy_name'];
        $data['activitybuy_stdate'] = $paramArray['activitybuy_stdate'];
        $data['activitybuy_enddate'] = $paramArray['activitybuy_enddate'];
        $data['activitybuy_apply'] = $paramArray['activitybuy_apply'];
        $data['activitybuy_updatetime'] = time();
        $field = array();
        $field['activitybuy_name'] = "活动名称";
        $field['activitybuy_stdate'] = "活动开始日期";
        $field['activitybuy_enddate'] = "活动结束日期";
        $field['activitybuy_apply'] = "适用学校";
        if ($this->DataControl->updateData("gmc_company_activitybuy", "activitybuy_id = '{$paramArray['activitybuy_id']}'", $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "编辑采购活动成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->集团设置->采购活动", '编辑采购活动', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '编辑采购活动失败', 'result' => $result);
        }
        return $res;
    }

    //获取学校列表
    function getSchoollist($paramArray)
    {
        if ($paramArray['type'] == '1') {
            $datawhere = " 1 ";
            if (isset($paramArray['student_id']) && $paramArray['student_id'] != '') {
                $datawhere .= " and en.student_id='{$paramArray['student_id']}'";
            }

            $sql = "select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_branch,s.school_shortname 
              from smc_student_enrolled as en,smc_school as s,smc_student as st 
              where {$datawhere} and en.school_id=s.school_id and en.student_id=st.student_id 
              and s.company_id='{$this->company_id}'
              group by s.school_id
              ORDER BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc
              ";

            $schoolList = $this->DataControl->selectClear($sql);

            $field = array();
            $field["school_branch"] = "校园编号";
            $field["school_cnname"] = "校园名称";

            $result = array();
            if ($schoolList) {
                $result["field"] = $field;
                $result["data"] = $schoolList;
                $res = array('error' => '0', 'errortip' => '获取学校列表成功', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '获取学校列表失败', 'result' => $result);
            }
        } else {
            $sql = "
            SELECT
                s.school_id,
                s.school_branch,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_shortname
            FROM
                smc_school AS s 
            WHERE
                s.company_id = '{$paramArray['company_id']}' 
                ORDER BY (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";
            $stafferDetail = $this->DataControl->selectClear($sql);

            $field = array();
            $field["school_branch"] = "校园编号";
            $field["school_cnname"] = "校园名称";

            $result = array();
            if ($stafferDetail) {
                $result["field"] = $field;
                $result["data"] = $stafferDetail;
                $res = array('error' => '0', 'errortip' => '获取学校列表成功', 'result' => $result);
            } else {
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '获取学校列表失败', 'result' => $result);
            }
        }

        return $res;
    }

    //获取学校 里的班级
    function getSchoolClasslist($paramArray)
    {
        $datawhere = " c.company_id = '{$paramArray['company_id']}' and c.class_status <> '-2'  ";
        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
            $datawhere .= " and c.school_id='{$paramArray['school_id']}'";
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '请选择学校后筛选！', 'result' => $result);
            return $res;
        }

        $sql = "select c.class_id,c.class_branch,c.class_cnname,c.class_enname,c.class_status,c.school_id  
                from smc_class as c 
                where {$datawhere} ";
        $classlList = $this->DataControl->selectClear($sql);
        if ($classlList) {
            $status = array("0" => "待开班", "1" => "已开班", "-1" => "已结束", "-2" => "已删除");
            foreach ($classlList as &$classlVar) {
                $classlVar['class_status_name'] = $status[$classlVar['class_status']];
            }
        }

        $field = array();
        $field["school_id"] = "学校ID";
        $field["class_id"] = "班级自增ID";
        $field["class_branch"] = "班级编号";
        $field["class_cnname"] = "班级中文名";
        $field["class_enname"] = "班级英文名";
        $field["class_status"] = "班级状态";
        $field["class_status_name"] = "班级状态名称";

        $result = array();
        if ($classlList) {
            $result["field"] = $field;
            $result["data"] = $classlList;
            $res = array('error' => '0', 'errortip' => '获取班级列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取班级列表失败', 'result' => $result);
        }

        return $res;
    }

    //学校库存列表
    function getRepertoryList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or  g.goods_pid like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                g.goods_cnname,
                g.goods_pid,
                g.goods_vipprice,
                r.goods_repertory,
                r.goods_id
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
                WHERE {$datawhere} and r.school_id = '{$paramArray['school_id']}'
            ORDER BY
                r.repertory_id DESC 
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(r.repertory_id) as a
            FROM
                smc_erp_goods_repertory AS r
                LEFT JOIN erp_goods AS g ON r.goods_id = g.goods_id
            WHERE
                {$datawhere} and r.school_id = '{$paramArray['school_id']}'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('goods_pid ', 'goods_cnname', 'goods_vipprice', 'goods_repertory');
        $fieldname = $this->LgArraySwitch(array('K3货号', '货品名称', '价格', '库存'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无库存信息", 'result' => $result);
        }

        return $res;
    }


    //采购管理列表
    function getProGoodsList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.proorder_pid like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['proorder_from']) && $paramArray['proorder_from'] !== "") {
            $datawhere .= " and p.proorder_from ={$paramArray['proorder_from']}";
        }
        if (isset($paramArray['proorder_status']) && $paramArray['proorder_status'] !== "") {
            $datawhere .= " and p.proorder_status ={$paramArray['proorder_status']}";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and p.to_school_id ={$paramArray['school_id']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.proorder_id,
                p.erpstatus_id,
                p.proorder_pid,
                p.proorder_status,
                p.proorder_from,
                ( SELECT sum( proogoods_buynums ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = p.proorder_pid ) AS nums,
                ( SELECT sum( proogoods_payprice ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = p.proorder_pid ) AS price,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                s.staffer_cnname,
                s.staffer_enname,
                (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname
            FROM
                erp_proorder AS p
                LEFT JOIN smc_school AS sc ON sc.school_id = p.to_school_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
             WHERE {$datawhere} and p.company_id = {$paramArray['company_id']}
             ORDER BY p.proorder_id DESC    
             LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购", "3" => "调拨", "4" => "借调"));
            $statuss = $this->LgArraySwitch(array("-1" => "已取消", "0" => "待发货", "1" => "待出库", "2" => "待入库", "3" => "已完成", "4" => "待归还", "5" => "归还待出库", "6" => "归还待入库"));
            foreach ($goodsList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                $val['proorder_from'] = $status[$val['proorder_from']];
                $val['proorder_status'] = $statuss[$val['proorder_status']];
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.proorder_id) as a
            FROM
                erp_proorder AS p
                LEFT JOIN smc_school AS sc ON sc.school_id = p.to_school_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']}");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('proorder_pid ', 'school_cnname', 'proorder_from', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'proorder_status');
        $fieldname = $this->LgArraySwitch(array('采购编号', '采购校园名称', '采购类型', '采购货品数量', '采购货品金额', '采购申请日期', '执行人', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无采购信息", 'result' => $result);
        }

        return $res;
    }

    //采购管理列表
    function getProGoodsLists($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.proorder_pid like '%{$paramArray['keyword']}%' or s.staffer_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['proorder_from']) && $paramArray['proorder_from'] !== "") {
            $datawhere .= " and p.proorder_from ={$paramArray['proorder_from']}";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and t.school_id ={$paramArray['school_id']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['erpstatus_tosmcname']) && $paramArray['erpstatus_tosmcname'] !== "") {
            $datawhere .= " and e.erpstatus_tosmcname ='{$paramArray['erpstatus_tosmcname']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.proorder_id,
                p.erpstatus_id,
                p.proorder_pid,
                p.proorder_from,
                ( SELECT sum( proogoods_buynums ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = t.proorder_pid ) AS nums,
                ( SELECT sum( proogoods_payprice ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = t.proorder_pid ) AS price,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                s.staffer_cnname,
                s.staffer_enname,
	            e.erpstatus_tosmcname,
	            (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_to AS t ON t.proorder_pid = p.proorder_pid
                left join smc_school as sc on sc.school_id = t.school_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']}
            ORDER BY
                (case when sc.school_istest=0 and sc.school_isclose=0 then 1 when sc.school_isclose=0 then 2 when sc.school_istest=0 then 3 else 4 end),sc.school_istest asc,field(sc.school_sort,0),sc.school_sort asc,sc.school_createtime asc,p.proorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购"));
            foreach ($goodsList as &$val) {
                $val['proorder_from'] = $status[$val['proorder_from']];
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.proorder_id) as a
            FROM
                erp_proorder AS p
                LEFT JOIN erp_proorder_to AS t ON t.proorder_pid = p.proorder_pid
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']}");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('proorder_pid ', 'school_cnname', 'proorder_from', 'nums', 'price', 'proorder_createtime', 'staffer_cnname', 'erpstatus_tosmcname');
        $fieldname = $this->LgArraySwitch(array('采购编号', '采购校园名称', '采购类型', '采购货品数量', '采购货品金额', '采购申请日期', '执行人', '状态'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }


        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;
        $result['allnum'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        if (!$goodsList) {
            $res = array('error' => '1', 'errortip' => "暂无采购信息", 'result' => $result);
        }

        return $res;
    }

    //采购货品明细
    function getGoodsDetailOne($paramArray)
    {
        $sql = "
            SELECT
                p.proorder_pid,
                p.proorder_from,
                p.proorder_status,
                p.erpstatus_id,
                sum(g.proogoods_buynums) as proogoods_buynums,
                sum(g.proogoods_payprice) as proogoods_payprice,
                FROM_UNIXTIME(p.proorder_createtime,'%Y-%m-%d') as proorder_createtime,
                s.staffer_cnname,
                s.staffer_enname,
	            (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
	            (case when scc.school_shortname='' then scc.school_cnname else scc.school_shortname end) as from_school_cnname
            FROM
                erp_proorder AS p 
                left join erp_proorder_goods as g on p.proorder_pid = g.proorder_pid
                left join smc_school as sc on sc.school_id = p.to_school_id
                left join smc_school as scc on scc.school_id = p.from_school_id
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
                LEFT JOIN erp_code_erpstatus AS e ON p.erpstatus_id = e.erpstatus_id
            WHERE
                p.proorder_pid = '{$paramArray['proorder_pid']}'";
        $stafferDetail = $this->DataControl->selectClear($sql);

        if ($stafferDetail) {
            $status = $this->LgArraySwitch(array("0" => "自主采购", "1" => "预估采购", "2" => "活动采购"));
            $statuss = $this->LgArraySwitch(array("-1" => "已取消", "0" => "待发货", "1" => "待出库", "2" => "待入库", "3" => "已完成", "4" => "待归还", "5" => "归还待出库", "6" => "归还待入库"));

            foreach ($stafferDetail as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                $val['proorder_from'] = $status[$val['proorder_from']];
                $val['erpstatus_tosmcname'] = $statuss[$val['proorder_status']];
            }

        }


        $field = array();
        $field["proorder_pid"] = "采购编号";
        $field["school_cnname"] = "采购校园名称";
        $field["proogoods_buynums"] = "采购货品数量";
        $field["proogoods_payprice"] = "采购货品金额";
        $field["proorder_createtime"] = "采购申请日期";
        $field["staffer_cnname"] = "申请人";
        $field["erpstatus_tosmcname"] = "状态";

        $result = array();
        if ($stafferDetail) {
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '获取采购货品明细成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取采购货品明细失败', 'result' => $result);
        }
        return $res;
    }

    //下载导入模版
    function getImportApi($paramArray)
    {
        $result = 'https://gmcapi.kedingdang.com/importexcel/教材商品导入模板(2020).xlsx';

        $res = array('error' => '0', 'errortip' => '下载导入模版成功', 'result' => $result);

        return $res;
    }

    //调拨列表 by:qyh
    function getAllotList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.proorder_pid like '%{$paramArray['keyword']}%')";
        }
//        if (isset($paramArray['proorder_from']) && $paramArray['proorder_from'] !== "") {
//            $datawhere .= " and p.proorder_from ='{$paramArray['proorder_from']}'";
//        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) >= '{$paramArray['starttime']}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) <= '{$paramArray['endtime']}'";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and (p.to_school_id ='{$paramArray['school_id']}' or p.from_school_id ='{$paramArray['school_id']}')";
        }
        if (isset($paramArray['proorder_status']) && $paramArray['proorder_status'] !== "") {
            $datawhere .= " and p.proorder_status ='{$paramArray['proorder_status']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.proorder_pid,
                ( SELECT sum( proogoods_buynums ) FROM erp_proorder_goods AS g WHERE g.proorder_pid = p.proorder_pid ) AS nums,
                ( SELECT (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname FROM smc_school AS s WHERE s.school_id = p.to_school_id ) AS to_school_cnname,
                ( SELECT s.school_branch FROM smc_school AS s WHERE s.school_id = p.to_school_id ) AS to_school_branch,
                ( SELECT (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname FROM smc_school AS s WHERE s.school_id = p.from_school_id ) AS from_school_cnname,
                ( SELECT s.school_branch FROM smc_school AS s WHERE s.school_id = p.from_school_id ) AS from_school_branch,
                FROM_UNIXTIME( p.proorder_createtime, '%Y-%m-%d' ) AS proorder_createtime,
                p.proorder_status 
            FROM
                erp_proorder AS p 
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']} and p.proorder_from = '3'
            ORDER BY
                p.proorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $statuss = $this->LgArraySwitch(array("-1" => "待审核", "0" => "待发货", "1" => "待出库", "2" => "待入库", "3" => "已完成"));
            foreach ($goodsList as &$val) {
                $val['proorder_status'] = $statuss[$val['proorder_status']];
            }
        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(p.proorder_id) as a
            FROM
                erp_proorder AS p
                LEFT JOIN smc_staffer AS s ON s.staffer_id = p.staffer_id
            WHERE {$datawhere} and p.company_id = {$paramArray['company_id']} and p.proorder_from = '1'");
        $allnums = $all_num[0]['a'];

        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无调拨数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['allnum'] = $allnums;
        return $data;
    }


    /**
     * 库存管理 -- 获取单个销货单信息
     * @by qyh
     * @param $paramArray
     * @return array
     */
    function getSalesorderOneApi($paramArray)
    {
        $sql = "
            SELECT
                s.salesorder_id,
                s.salesorder_pid,
                s.salesorder_from,
                s.salesorder_from as salesorder_from_name,
                s.proorder_pid,
                ( SELECT sum( pg.proogoods_buynums ) FROM erp_proorder_goods AS pg WHERE pg.proorder_pid = s.proorder_pid ) AS num,
                p.proorder_allprice,
                FROM_UNIXTIME( s.salesorder_createtime, '%Y-%m-%d' ) AS salesorder_createtime,
                FROM_UNIXTIME( s.salesorder_reviewtime, '%Y-%m-%d' ) AS salesorder_reviewtime,
                s.salesorder_status,
                s.salesorder_status as salesorder_status_num,
                (case when c.school_shortname='' then c.school_cnname else c.school_shortname end) as to_school_cnname,
                (case when cc.school_shortname='' then cc.school_cnname else cc.school_shortname end) as from_school_cnname,
                t.staffer_cnname,
                sum(beoutorder_buynums) as outnum
            FROM
                smc_erp_salesorder AS s
                LEFT JOIN erp_proorder AS p ON s.proorder_pid = p.proorder_pid 
                left join smc_school as c on p.to_school_id = c.school_id
                left join smc_school as cc on p.from_school_id = cc.school_id
                left join smc_staffer as t on p.staffer_id = t.staffer_id
                left join smc_erp_beoutorder as b on b.beoutorder_outpid = s.salesorder_pid
                left join smc_erp_beoutorder_goods as bg on bg.beoutorder_pid = b.beoutorder_pid
            WHERE
                s.salesorder_pid = '{$paramArray['salesorder_pid']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        if ($goodsList) {
            $status = $this->LgArraySwitch(array("0" => "调拨", "1" => "借调", "2" => "领用", "3" => "报损", "4" => "自主采购", "5" => "活动采购", "6" => "预估采购"));
            $statuss = $this->LgArraySwitch(array("-1" => "已拒绝", "0" => "待审核", "1" => "待发货", "2" => "待出库", "3" => "已出库", "4" => "已完成"));
            foreach ($goodsList as &$val) {
                $val['salesorder_from_name'] = $status[$val['salesorder_from_name']];
                $val['salesorder_status'] = $statuss[$val['salesorder_status']];
            }
        }

        $field = array();
        $field["salesorder_pid"] = "销货单号";
        $field["salesorder_from_name"] = "销货类型";
        $field["salesorder_from"] = "销货类型的数字";
        $field["proorder_pid"] = "采购单号";
        $field["num"] = "销货货品数量";
        $field["proorder_allprice"] = "销货金额";
        $field["to_school_cnname"] = "调入园所";
        $field["from_school_cnname"] = "调出园所";
        $field["salesorder_createtime"] = "申请日期";
        $field["salesorder_reviewtime"] = "审核日期";
        $field["staffer_cnname"] = "申请人";
        $field["salesorder_status"] = "状态";

        $result = array();
        if ($goodsList) {
            $result["field"] = $field;
            $result["data"] = $goodsList;
            $res = array('error' => '0', 'errortip' => '获取单个销货单信息成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取单个销货单信息失败', 'result' => $result);
        }
        return $res;
    }


    //销货货品明细 by:qyh
    function getSalesorderGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (g.goods_cnname like '%{$paramArray['keyword']}%' or g.goods_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and g.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                g.goods_id,
                g.goods_pid,
                g.goods_cnname,
                g.goods_unit,
                g.goods_vipprice,
                p.prodtype_name,
                sg.salesordergoods_id,
                sg.salesordergoods_buynums,
                sg.salesordergoods_sendnums
            FROM
                smc_erp_salesorder_goods AS sg
                LEFT JOIN erp_goods AS g ON sg.goods_id = g.goods_id
                LEFT JOIN smc_code_prodtype AS p ON g.prodtype_code = p.prodtype_code
            where {$datawhere} and  sg.salesorder_pid = '{$paramArray['salesorder_pid']}'
            group by g.goods_pid
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = count($goodsList);

        if ($goodsList) {
            foreach ($goodsList as &$val) {
                $val['price'] = $val['salesordergoods_buynums'] * $val['goods_vipprice'];
                $val['goodsNum'] = 0;
            }
        }


        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无货品数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['all_num'] = $all_num;
        return $data;
    }

    function getSalesorderTracks($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (t.tracks_title like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                t.tracks_id,
                t.tracks_title,
                t.tracks_playname,
                t.tracks_information,
                FROM_UNIXTIME( t.tracks_createtime, '%Y-%m-%d' ) AS tracks_createtime
            FROM
                erp_salesorder_tracks AS t
            where {$datawhere} and t.salesorder_pid = '{$paramArray['salesorder_pid']}'
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $allnum = count($goodsList);

        if (!$goodsList) {
            $this->error = true;
            $this->errortip = "无跟踪数据";
            return false;
        }

        $data['list'] = $goodsList;
        $data['all_num'] = $allnum;
        return $data;
    }

    function getGoodsPriceLog($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['start_time']) && $paramArray['start_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.pricelog_updatatime, '%Y-%m-%d' ) >= '{$paramArray['start_time']}'";
        }
        if (isset($paramArray['end_time']) && $paramArray['end_time'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( p.pricelog_updatatime, '%Y-%m-%d' ) <= '{$paramArray['end_time']}'";
        }

        $sql = "
            SELECT
                p.pricelog_id,
                p.pricelog_oriprice,
                p.pricelog_price,
                FROM_UNIXTIME( p.pricelog_updatatime, '%Y-%m-%d' ) AS pricelog_updatatime
            FROM
                erp_goods_pricelog AS p
            where {$datawhere} and p.goods_id = '{$paramArray['goods_id']}' and p.company_id = '{$paramArray['company_id']}'";

        $goodsList = $this->DataControl->selectClear($sql);

        $fieldstring = array('pricelog_oriprice', 'pricelog_price', 'pricelog_updatatime');
        $fieldname = $this->LgArraySwitch(array('成本价', '协议价', '生效时间'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无价格变动信息", 'result' => $result);
        }

        return $res;
    }

    function salesorderStatusAction($paramArray)
    {
        $ClassTypeOne = $this->DataControl->getFieldOne("smc_erp_salesorder", "salesorder_pid", "salesorder_pid = '{$paramArray['salesorder_pid']}'");

        if ($ClassTypeOne) {
            $data = array();
            $data['salesorder_status'] = $paramArray['salesorder_status'];
            $data['salesorder_remark'] = $paramArray['salesorder_remark'];
            $data['salesorder_updatetime'] = time();
            $data['salesorder_reviewtime'] = time();

            $field = array();
            $field['salesorder_status'] = $this->LgStringSwitch('状态');
            $field['salesorder_remark'] = $this->LgStringSwitch('备注');

            if ($this->DataControl->updateData("smc_erp_salesorder", "salesorder_pid = '{$paramArray['salesorder_pid']}'", $data)) {

                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $dataTracks = array();
                if ($paramArray['salesorder_status'] == '1') {
                    $dataTracks['tracks_title'] = $this->LgStringSwitch('销货单审核通过');
                } else {
                    $dataTracks['tracks_title'] = $this->LgStringSwitch('销货单审核拒绝');
                }
                $dataTracks['tracks_information'] = $this->LgStringSwitch($paramArray['salesorder_remark']);
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['salesorder_pid'] = $paramArray['salesorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "审核销货单成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->销货单管理", '审核销货单', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '审核销货单失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function sendGoodsAction($paramArray)
    {
        $ClassTypeOne = $this->DataControl->getFieldOne("smc_erp_salesorder", "salesorder_pid,salesorder_from,proorder_pid", "salesorder_pid = '{$paramArray['salesorder_pid']}'");

        if ($ClassTypeOne) {

            $datas = array();
            $goodsList = json_decode(stripslashes($paramArray['goods']), true);
            foreach ($goodsList as $item) {
                $datas['salesordergoods_sendnums'] = $item['salesordergoods_sendnums'] + $item['sendnum'];
                $this->DataControl->updateData("smc_erp_salesorder_goods", "salesordergoods_id = '{$item['salesordergoods_id']}'", $datas);
            }

            $data = array();
            $num = $this->DataControl->selectOne("select sum(sg.salesordergoods_buynums - sg.salesordergoods_sendnums) as num from smc_erp_salesorder_goods as sg WHERE sg.salesorder_pid = '{$paramArray['salesorder_pid']}'");
            if ($num['num'] == '0') {
                $data['salesorder_status'] = '2';
            } else {
                $data['salesorder_status'] = '1';
            }
            $data['salesorder_updatetime'] = time();

            $field = array();
            $field['salesorder_status'] = $this->LgStringSwitch('状态');

            if ($this->DataControl->updateData("smc_erp_salesorder", "salesorder_pid = '{$paramArray['salesorder_pid']}'", $data)) {


                $datass = array();
                $datass['beoutorder_pid'] = $this->createOrderPid('CK');
                $datass['company_id'] = $paramArray['company_id'];
                $datass['to_school_id'] = $paramArray['to_school_id'];
                $datass['staffer_id'] = $paramArray['staffer_id'];
                $datass['salesorder_pid'] = $paramArray['salesorder_pid'];
                $datass['proorder_pid'] = $paramArray['proorder_pid'];
                $datass['beoutorder_createtime'] = time();
                $this->DataControl->insertData("smc_erp_beoutorder", $datass);

                $datasss = array();
                $goodsList = json_decode(stripslashes($paramArray['goods']), true);
                foreach ($goodsList as $items) {
                    $datasss['beoutorder_pid'] = $datass['beoutorder_pid'];
                    $datasss['goods_id'] = $items['goods_id'];
                    $datasss['beoutorder_buynums'] = $items['sendnum'];
                    $this->DataControl->insertData("smc_erp_beoutorder_goods", $datasss);
                }

                $data = array();
                $data['beoutorder_pid'] = $datass['beoutorder_pid'];
                $data['staffer_id'] = $paramArray['staffer_id'];
                $name = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $data['tracks_playname'] = $name['staffer_cnname'];
                $data['tracks_information'] = $this->LgStringSwitch('生成出库单');
                $data['tracks_createtime'] = time();
                $this->DataControl->insertData("smc_erp_beoutorder_tracks", $data);

                $datapro = array();
                $datapro['proorder_status'] = '1';
                $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$paramArray['proorder_pid']}'", $datapro);


                $staffer = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id = '{$paramArray['staffer_id']}'");
                $dataTracks = array();
                $dataTracks['tracks_title'] = $this->LgStringSwitch('销货单货品发货');
                $dataTracks['tracks_information'] = $this->LgStringSwitch('销货单货品发货，出库单号：' . $datass['beoutorder_pid']);
                $dataTracks['tracks_playname'] = $staffer['staffer_cnname'];
                $dataTracks['salesorder_pid'] = $paramArray['salesorder_pid'];
                $dataTracks['tracks_createtime'] = time();

                $this->DataControl->insertData('erp_salesorder_tracks', $dataTracks);

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "发货成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->销货单管理", '发货', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '发货失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getBeoutorderList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (b.beoutorder_pid like '%{$paramArray['keyword']}%' or b.salesorder_pid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['salesorder_from']) && $paramArray['salesorder_from'] !== "") {
            $datawhere .= " and sa.salesorder_from ={$paramArray['salesorder_from']}";
        }
        if (isset($paramArray['beoutorder_status']) && $paramArray['beoutorder_status'] !== "") {
            $datawhere .= " and b.beoutorder_status ={$paramArray['beoutorder_status']}";
        }
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== "") {
            $datawhere .= " and b.to_school_id ={$paramArray['school_id']}";
        }
        if (isset($paramArray['startTime']) && $paramArray['startTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) >= '{$paramArray['startTime']}'";
        }
        if (isset($paramArray['endTime']) && $paramArray['endTime'] !== "") {
            $datawhere .= " and FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) <= '{$paramArray['endTime']}'";
        }
        if (isset($paramArray['beoutorder_status']) && $paramArray['beoutorder_status'] !== "") {
            $datawhere .= " and b.beoutorder_status ={$paramArray['beoutorder_status']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                b.beoutorder_pid,
                b.beoutorder_from,
                b.beoutorder_status,
                b.beoutorder_status as beoutorder_status_name,
                s.staffer_cnname,
                (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,
                sc.school_branch,
                (select sum(bg.beoutorder_buynums) from smc_erp_beoutorder_goods as bg where bg.beoutorder_pid = b.beoutorder_pid) as outnum,
                b.salesorder_pid,
                (select sum(sg.salesordergoods_buynums) from smc_erp_salesorder_goods as sg where sg.salesorder_pid = b.salesorder_pid) as salenum,
                sa.salesorder_from,
                FROM_UNIXTIME( sa.salesorder_createtime, '%Y-%m-%d' ) AS salesorder_createtime,
                FROM_UNIXTIME( b.beoutorder_storagetime, '%Y-%m-%d' ) AS beoutorder_storagetime
            FROM
                smc_erp_beoutorder AS b
                left join smc_staffer as s on b.staffer_id = s.staffer_id
                left join smc_school as sc on sc.school_id = to_school_id
                left join smc_erp_salesorder as sa on sa.salesorder_pid = b.salesorder_pid
            WHERE {$datawhere} and b.school_id = '0' and s.company_id = '{$paramArray['company_id']}'
            ORDER BY
                b.beoutorder_id DESC    
            LIMIT {$pagestart},{$num}";

        $beinorderList = $this->DataControl->selectClear($sql);

        if ($beinorderList) {
            $status = $this->LgArraySwitch(array("0" => "调拨", "1" => "借调", "2" => "领用", "3" => "报损", "4" => "自主采购", "5" => "活动采购", "6" => "预估采购"));
            foreach ($beinorderList as &$val) {
                $val['salesorder_from'] = $status[$val['salesorder_from']];
            }
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待出库", "2" => "已出库"));
            foreach ($beinorderList as &$val) {
                $val['beoutorder_status_name'] = $status[$val['beoutorder_status_name']];
                if ($val['beoutorder_storagetime'] == '1970-01-01') {
                    $val['beoutorder_storagetime'] = '--';
                }
            }

        }

        $all_num = $this->DataControl->selectClear("
            SELECT
               COUNT(b.beoutorder_id) as a
            FROM
                smc_erp_beoutorder AS b
                left join smc_staffer as s on b.staffer_id = s.staffer_id
                left join smc_school as sc on sc.school_id = to_school_id
                left join smc_erp_salesorder as sa on sa.salesorder_pid = b.salesorder_pid
           WHERE {$datawhere} and b.school_id = '0'");
        $allnums = $all_num[0]['a'];

        $fieldstring = array('school_cnname ', 'school_branch', 'beoutorder_pid', 'outnum', 'salesorder_pid', 'salenum', 'salesorder_from', 'salesorder_createtime', 'beoutorder_storagetime', 'beoutorder_status_name');
        $fieldname = array('校区名称', '校区编号', '出库单号', '出库货品数量', '销货单号', '销货货品数量', '销货类型', '销货申请日期', '出库日期', '状态');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldstring[$i]));
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($beinorderList) {
            $result['list'] = $beinorderList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        if (!$beinorderList) {
            $res = array('error' => '1', 'errortip' => "暂无出库单信息", 'result' => $result);
        }

        return $res;
    }


    //出库单资料
    function getBeoutorderInfo($paramArray)
    {
        $sql = "
            SELECT
                b.beoutorder_pid,
                b.beoutorder_from,
                b.beoutorder_status,
                b.salesorder_pid,
                b.beoutorder_status as beoutorder_status_name,
                s.staffer_cnname,
                FROM_UNIXTIME(b.beoutorder_storagetime,'%Y-%m-%d') as beoutorder_createtime,
                (select sum(salesordergoods_buynums) from smc_erp_salesorder_goods as sg where sg.salesorder_pid = b.salesorder_pid) as salenum,
                (select sum(salesordergoods_sendnums) from smc_erp_salesorder_goods as sgg where sgg.salesorder_pid = b.salesorder_pid) as sendnum,
                sum(g.beoutorder_buynums) as nums,
                (case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname
            FROM
                smc_erp_beoutorder AS b left join smc_erp_beoutorder_goods as g on b.beoutorder_pid = g.beoutorder_pid left join smc_staffer as s on s.staffer_id = b.staffer_id left join smc_school as sc on sc.school_id = b.to_school_id
            WHERE
                b.beoutorder_pid = '{$paramArray['beoutorder_pid']}'";
        $beinorderList = $this->DataControl->selectClear($sql);

        if ($beinorderList) {
            $status = $this->LgArraySwitch(array("0" => "系统自订", "1" => "教师下单"));
            foreach ($beinorderList as &$val) {
                $val['beoutorder_from'] = $status[$val['beoutorder_from']];
            }
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "待出库", "2" => "已出库"));
            foreach ($beinorderList as &$val) {
                $val['beoutorder_status_name'] = $status[$val['beoutorder_status_name']];
                if ($val['beoutorder_createtime'] == '1970-01-01') {
                    $val['beoutorder_createtime'] = '--';
                }
            }

        }

        $field = array();
        $field["beoutorder_pid"] = $this->LgStringSwitch("出库单号");
        $field["beoutorder_from"] = $this->LgStringSwitch("购买方式");
        $field["beoutorder_status"] = $this->LgStringSwitch("订单状态");
        $field["staffer_cnname"] = $this->LgStringSwitch("执行人");
        $field["nums"] = $this->LgStringSwitch("总数量");
        $field["beoutorder_createtime"] = $this->LgStringSwitch("生成时间");

        $result = array();
        if ($beinorderList) {
            $result["field"] = $field;
            $result["data"] = $beinorderList;
            $res = array('error' => '0', 'errortip' => '获取出库单资料成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '获取出库单资料失败', 'result' => $result);
        }
        return $res;
    }


    //出库货品明细
    function getOutGoodsList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.goods_cnname like '%{$paramArray['keyword']}%' or s.goods_outpid like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['prodtype_code']) && $paramArray['prodtype_code'] !== "") {
            $datawhere .= " and s.prodtype_code ='{$paramArray['prodtype_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "
            SELECT
                g.beoutorder_pid,
                s.goods_cnname,
                s.goods_pid,
                s.goods_unit,
                s.goods_vipprice,
                g.beoutorder_buynums,
                p.prodtype_name,
                (select proogoods_buynums from erp_proorder_goods as pg where pg.proorder_pid = b.proorder_pid and pg.goods_id = g.goods_id) as buynum
            FROM
                smc_erp_beoutorder_goods AS g
                left join erp_goods as s on g.goods_id = s.goods_id
                left join smc_erp_beoutorder as b on b.beoutorder_pid = g.beoutorder_pid
                left join smc_code_prodtype as p on p.prodtype_code = s.prodtype_code and p.company_id = '{$paramArray['company_id']}'
            WHERE {$datawhere} and g.beoutorder_pid = '{$paramArray['beoutorder_pid']}'
            ORDER BY
                g.beoutordergoods_id DESC
            limit {$pagestart},{$num}";

        $goodsList = $this->DataControl->selectClear($sql);

        $all_num = count($goodsList);

        $fieldstring = array('goods_pid ', 'goods_cnname', 'prodtype_name', 'goods_unit', 'goods_vipprice', 'buynum', 'beoutorder_buynums');
        $fieldname = array('货品编号', '货品名称', '货品类别', '单位', '价格', '采购数量', '本次出库数量');
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1");


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldstring[$i]));
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($goodsList) {
            $result['list'] = $goodsList;
            $result['all_num'] = $all_num;
        } else {
            $result['list'] = array();
            $result['all_num'] = 0;
        }

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //审核出库单
    function ExamineOutorderAction($paramArray)
    {
        $proorderOne = $this->DataControl->getFieldOne("smc_erp_beoutorder", "salesorder_pid,proorder_pid,to_school_id,beoutorder_pid,beoutorder_outpid", "beoutorder_pid = '{$paramArray['beoutorder_pid']}'");

        if ($proorderOne) {
            $data = array();
            if ($paramArray['beoutorder_status'] == '0') {
                $data['beoutorder_status'] = '1';
            }
            if ($paramArray['beoutorder_status'] == '1') {
                $data['beoutorder_status'] = '2';
            }

            $datass = array();
            $datass['company_id'] = $paramArray['company_id'];
            $datass['school_id'] = $proorderOne['to_school_id'];
            $datass['proorder_pid'] = $proorderOne['proorder_pid'];
            $datass['beinorder_from'] = '0';
            $datass['beoutorder_pid'] = $paramArray['beoutorder_pid'];
            $datass['beinorder_pid'] = $this->createOrderPid('RK');
            $datass['beinorder_status'] = '0';
            $datass['staffer_id'] = $paramArray['staffer_id'];
            $datass['beinorder_createtime'] = time();

            $field = array();
            $field['company_id'] = $this->LgStringSwitch("所属公司");
            $field['school_id'] = $this->LgStringSwitch("校园ID");

            $this->DataControl->insertData('smc_erp_beinorder', $datass);
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;

            $datas = array();

            $goodsList = $this->DataControl->selectClear("select * from smc_erp_beoutorder_goods WHERE beoutorder_pid = '{$proorderOne['beoutorder_pid']}'");
            foreach ($goodsList as $item) {
                $datas['goods_id'] = $item['goods_id'];
                $datas['beinorder_pid'] = $datass['beinorder_pid'];
                $datas['beinordergoods_buynums'] = $item['beoutorder_buynums'];

                $this->DataControl->insertData('smc_erp_beinorder_goods', $datas);
            }


            $field = array();
            $field['beoutorder_status'] = $this->LgStringSwitch("ERP订单最新状态ID");

            $data = array();
            $data['beoutorder_status'] = '2';
            $data['beoutorder_storagetime'] = time();

            if ($this->DataControl->updateData("smc_erp_beoutorder", "beoutorder_pid = '{$paramArray['beoutorder_pid']}'", $data)) {
                $data = array();
                $data['proorder_status'] = 2;
                $this->DataControl->updateData("erp_proorder", "proorder_pid = '{$proorderOne['proorder_pid']}'", $data);

                $num = $this->DataControl->selectOne("select sum(sg.salesordergoods_buynums - sg.salesordergoods_sendnums) as num from smc_erp_salesorder_goods as sg WHERE sg.salesorder_pid = '{$proorderOne['salesorder_pid']}'");

                $data = array();
                if ($num['num'] == '0') {
                    $data['salesorder_status'] = '3';
                    $this->DataControl->updateData("smc_erp_salesorder", "salesorder_pid = '{$proorderOne['salesorder_pid']}'", $data);
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "出库单最新状态修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团订购管理->出库管理", '确认出库', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '出库单最新状态修改失败', 'result' => $result);
            }

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


}
