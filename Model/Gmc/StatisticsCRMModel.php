<?php


namespace Model\Gmc;

class StatisticsCRMModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    /**
     * 集团招生统计 -招生统计状况
     * author: ling
     * 对应接口文档 0001
     */
    function CountOverCRM()
    {
        $starttoday = date('Y-m-d');
        $endtoday = date('Y-m-d').' 23:59:59';

        $sql = "select  
            (select count(f.frommedia_id) from crm_code_frommedia as f where f.company_id=c.company_id) as  frommedia_num,
            (select count(h.channel_id) from crm_code_channel as h where h.company_id=c.company_id and h.channel_isuse = 1) as  channel_num,
            (select count(a.activity_id) from crm_sell_activity as a where a.company_id=c.company_id and a.activity_starttime <= '{$starttoday}' and a.activity_endtime>='{$endtoday}') as  activity_num,
            (select count( DISTINCT p.marketer_id) from crm_client_principal as p,smc_school as s where p.school_id=s.school_id and s.company_id=c.company_id ) as  marketer_num,
            (select count(t.client_id) from crm_client as t where t.company_id=c.company_id and t.client_tracestatus <>'-1' and t.client_tracestatus <>'-2' ) as  client_num,
            (select count(t.client_id) from crm_client as t where t.company_id=c.company_id and t.client_tracestatus in (1,2,3) ) as  client_intentionNum,
            (select count(t.client_id) from crm_client as t where t.company_id=c.company_id and t.client_id  not in (select cs.client_id from crm_client_schoolenter as cs where cs.client_id=t.client_id ) ) as  client_noallotnum
            from gmc_company as c 
            where c.company_id='{$this->company_id}'";
        $dataList = $this->DataControl->selectOne($sql);
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    /**
     * 招生客户状况
     * author: ling
     * 对应接口文档 0001
     */
    function StatisticsClient($request)
    {
        $datawhere = " 1 and l.company_id = '{$this->company_id}'";
        $pr_datawhere = " 1 and l.company_id = '{$this->company_id}'";
        $in_datawhere = " 1 and l.company_id = '{$this->company_id}'";
        $au_datawhere = " 1 and l.company_id = '{$this->company_id}'";
        $track_where = " 1 and l.company_id = '{$this->company_id}'";
        $positive_where = " 1 and l.company_id = '{$this->company_id}'";

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $stime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >= '{$stime}'";
            $datawhere .= " and c.client_createtime >= '{$stime}'";
            $pr_datawhere .= " and p.principal_createtime >= '{$stime}'";
            $in_datawhere .= " and  invite_visittime >= '{$request['starttime']} 00:00:00'";
            $au_datawhere .= " and  audition_visittime >= '{$request['starttime']} 00:00:00'";
            $track_where .= " and  t.track_createtime >= '{$stime}'";
            $positive_where .= " and  g.positivelog_time >= '{$request['starttime']}'";

//            $datawhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $datawhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $pr_datawhere .= " and FROM_UNIXTIME(p.principal_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $in_datawhere .= " and  invite_visittime >= '{$request['starttime']} 00:00:00'";
//            $au_datawhere .= " and  audition_visittime >= '{$request['starttime']} 00:00:00'";
//            $track_where .= " and  FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
//            $positive_where .= " and  g.positivelog_time >= '{$request['starttime']}'";

        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
            $etime = strtotime($request['endtime'])+86399;
            $datawhere .= " and c.client_createtime <= '{$etime}'";
            $pr_datawhere .= " and p.principal_createtime <= '{$etime}'";
            $in_datawhere .= " and  invite_visittime <= '{$request['endtime']} 59:59:59'";
            $au_datawhere .= " and  audition_visittime <= '{$request['endtime']} 59:59:59'";
            $track_where .= " and  t.track_createtime <= '{$etime}'";
            $positive_where .= " and  g.positivelog_time <= '{$request['endtime']}'";

//            $datawhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
//            $pr_datawhere .= " and FROM_UNIXTIME(p.principal_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
//            $in_datawhere .= " and  invite_visittime <= '{$request['endtime']} 59:59:59'";
//            $au_datawhere .= " and  audition_visittime <= '{$request['endtime']} 59:59:59'";
//            $track_where .= " and  FROM_UNIXTIME(t.track_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
//            $positive_where .= " and  g.positivelog_time <= '{$request['endtime']}'";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $datawhere .= " and r.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $pr_datawhere .= " and p.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $in_datawhere .= " and i.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $au_datawhere .= " and a.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $track_where .= " and t.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $positive_where .= " and g.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] !== '') {
            $datawhere .= " and r.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
            $pr_datawhere .= " and p.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
            $in_datawhere .= " and i.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
            $au_datawhere .= " and a.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
            $track_where .= " and t.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
            $positive_where .= " and g.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and r.school_id  ='{$request['school_id']}' ";
            $pr_datawhere .= " and p.school_id ='{$request['school_id']}'";
            $in_datawhere .= " and i.school_id ='{$request['school_id']}'";
            $au_datawhere .= " and a.school_id ='{$request['school_id']}'";
            $track_where .= " and t.school_id ='{$request['school_id']}'";
            $positive_where .= " and g.school_id ='{$request['school_id']}'";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and  l.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and l.school_city  ='{$request['school_city']}' ";
        }


        $addNewClient = $this->DataControl->selectOne("select count(c.client_id) as client_num from crm_client as c,crm_client_schoolenter as r,smc_school as l where r.school_id = l.school_id and c.client_id=r.client_id and {$datawhere} and c.client_tracestatus <>-2 ");
        $addNewPriClient = $this->DataControl->selectOne("select count(DISTINCT p.client_id) as client_num from crm_client_principal as p, smc_school as l where p.school_id = l.school_id   and {$pr_datawhere} ");
        $inv_addNewClient = $this->DataControl->selectOne("select count(DISTINCT i.client_id) as client_num from crm_client_invite as i,smc_school as l where  i.school_id = l.school_id  and {$in_datawhere} and invite_isvisit =1");

        $aud_addNewClient = $this->DataControl->selectOne("select count(DISTINCT a.client_id) as client_num from crm_client_audition as a,smc_school as l where a.school_id =l.school_id and {$au_datawhere} and a.audition_isvisit =1");
        $loss_addNewClient = $this->DataControl->selectOne("select count(DISTINCT t.client_id) as client_num from crm_client_track as t,crm_client as c,smc_school as l where l.school_id =t.school_id and c.client_id=t.client_id and t.track_state=-1  and c.client_ischaserlapsed =1  and {$track_where}  ");
        $positive_addNewClient = $this->DataControl->selectOne("select count(client_id) as client_num from crm_client_positivelog as g,smc_school as l  where l.school_id = g.school_id and {$positive_where}    ");

        $dataArray = array();
        $dataArray["addClient"] = $addNewClient['client_num'] + 0;
        $dataArray["intentionClient"] = $addNewPriClient['client_num'] + 0;
        $dataArray["inviteClient"] = $inv_addNewClient['client_num'] + 0;
        $dataArray["auditionClient"] = $aud_addNewClient['client_num'] + 0;
        $dataArray["loseClient"] = $loss_addNewClient['client_num'] + 0;
        $dataArray["officialClient"] = $positive_addNewClient['client_num'] + 0;


//
//        $sql = "SELECT c.client_id,c.client_distributionstatus,c.client_tracestatus
//                FROM crm_client as c
//                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id and c.company_id = s.company_id
//                LEFT JOIN crm_client_allotlog as a ON c.client_id = a.client_id
//                LEFT JOIN smc_school as l ON l.school_id = s.school_id
//                WHERE {$datawhere}
//                GROUP BY c.client_id";
//        $clientList = $this->DataControl->selectClear($sql);
//        if (is_array($clientList)) {
//            $addClient = 0;//新增
//            $intentionClient = 0;//意向
//            $loseClient = 0;//流失
//            $inviteClient = 0;//柜询
//            $auditionClient = 0;//试听
//            $officialClient = 0;//正式
//            foreach ($clientList as $clientVar) {
//                //新增
//                ++$addClient;
//                //意向
//                if ($clientVar['client_distributionstatus'] == '1') {
//                    ++$intentionClient;
//                }
//                //流失
//                if ($clientVar['client_distributionstatus'] == '0' && $clientVar['client_tracestatus'] == '-1') {
//                    ++$loseClient;
//                }
//                //柜询
//                if ($clientVar['client_distributionstatus'] == '1' && $clientVar['client_tracestatus'] == '2') {
//                    ++$inviteClient;
//                }
//                //试听
//                if ($clientVar['client_distributionstatus'] == '1' && $clientVar['client_tracestatus'] == '3') {
//                    ++$auditionClient;
//                }
//                //正式
//                if ($clientVar['client_distributionstatus'] == '0' && $clientVar['client_tracestatus'] == '4') {
//                    ++$officialClient;
//                }
//            }
//        }
//
//        $dataArray = array();
//        $dataArray["addClient"] = $addClient + 0;
//        $dataArray["intentionClient"] = $intentionClient + 0;
//        $dataArray["loseClient"] = $loseClient + 0;
//        $dataArray["inviteClient"] = $inviteClient + 0;
//        $dataArray["auditionClient"] = $auditionClient + 0;
//        $dataArray["officialClient"] = $officialClient + 0;
        //转化率
        $addClient = $dataArray["addClient"];
        $intentionClient = $dataArray["intentionClient"];
        $inviteClient = $dataArray["inviteClient"];
        $auditionClient = $dataArray["auditionClient"];
        $officialClient = $dataArray["officialClient"];
        $loseClient = $dataArray["loseClientRate"];

        if (($addClient + 0) > 0) {
            $dataArray["intentionClientRate"] = sprintf("%.2f", ($intentionClient + 0) / ($addClient + 0)) * 100;
            $dataArray["inviteClientRate"] = sprintf("%.2f", ($inviteClient + 0) / ($addClient + 0)) * 100;
            $dataArray["auditionClientRate"] = sprintf("%.2f", ($auditionClient + 0) / ($addClient + 0)) * 100;
            $dataArray["officialClientRate"] = sprintf("%.2f", ($officialClient + 0) / ($addClient + 0)) * 100;
            $dataArray["loseClientRate"] = sprintf("%.2f", ($loseClient + 0) / ($addClient + 0)) * 100;
        } else {
            $dataArray["intentionClientRate"] = 0;
            $dataArray["inviteClientRate"] = 0;
            $dataArray["auditionClientRate"] = 0;
            $dataArray["officialClientRate"] = 0;
            $dataArray["loseClientRate"] = 0;
        }
        return $dataArray;
    }

    /**
     * 招生趋势-按年按月按季度
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function CountClientByYear($request)
    {
        $time_start = strtotime(date('Y-m-01'));
        $time_end = strtotime(date('Y-m-t')) + 24 * 3600 - 1;
        $datawhere = '1';


        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $time_start = strtotime($request['starttime']);
        }
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $time_end = strtotime($request['endtime']) + 24 * 3600 - 1;
        }

        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] !== '') {
            $datawhere .= " and s.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and l.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and l.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and l.school_city  ='{$request['school_city']}' ";
        }


        if (isset($request['type']) && $request['type'] != '') {
            if ($request['type'] == 0) {
                $format_date = '%Y-%m-%d';
            } elseif ($request['type'] == 1) {
                $format_date = '%Y-%m';
            } elseif ($request['type'] == 2) {
                $format_date = '%Y-%m';
            }
        } else {
            $request['type'] = 0;
            $format_date = '%Y-%m-%d';
        }

        $datawhere .= " and  c.company_id='{$this->company_id}' and c.client_createtime >= '{$time_start}' and  c.client_createtime <='{$time_end}' ";

        if ($request['type'] != 0) {
            while (date('Ym', $time_start) <= date('Ym', $time_end)) {
                $date[] = date('Y-m', $time_start);
                $time_start = strtotime('+1 month', $time_start);
            }
        } else {
            while (date('Ymd', $time_start) <= date('Ymd', $time_end)) {
                $date[] = date('Y-m-d', $time_start);
                $time_start = strtotime('+1 day', $time_start);
            }
        }
        $sql = "SELECT 
        FROM_UNIXTIME( client_createtime, '{$format_date}' ) AS current_day,COUNT( DISTINCT  c.client_id ) AS client_num 
        FROM crm_client as c 
        left  join crm_client_schoolenter as s ON c.client_id = s.client_id
        left join smc_school as l ON l.school_id = s.school_id
        WHERE 
       {$datawhere}
        GROUP BY current_day
        ORDER BY current_day ASC  
    ";

        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            $arr_client_day = array_column($clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_client_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_client_num[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_client_num[$key] = '0';
            }
        }
        $allList[0]['name'] = $this->LgStringSwitch('新增有效名单');
        $allList[0]['data'] = $arr_client_num;


        // 新增意向客户
        $intention_sql = $sql = "SELECT 
                FROM_UNIXTIME( client_createtime, '{$format_date}' ) AS current_day,COUNT( DISTINCT c.client_id ) AS client_num 
                FROM crm_client as c 
                left join crm_client_schoolenter as s ON c.client_id = s.client_id
                left join smc_school as l ON l.school_id = s.school_id
                WHERE {$datawhere} and client_distributionstatus = 1
                GROUP BY current_day
                ORDER BY current_day ASC  LIMIT 0,10  ";
        $intent_clientList = $this->DataControl->selectClear($intention_sql);
        if ($intent_clientList) {
            $arr_client_day = array_column($intent_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_intentclient_num = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_intentclient_num[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_intentclient_num[$key] = '0';
            }
        }

        $allList[1]['name'] = $this->LgStringSwitch('新增意向客户');
        $allList[1]['data'] = $arr_intentclient_num;

        // 新增转正人数
        $postive_sql = "SELECT 
                FROM_UNIXTIME( client_createtime, '{$format_date}' ) AS current_day,COUNT(DISTINCT  c.client_id ) AS client_num 
                FROM crm_client as c 
                LEFT JOIN crm_client_schoolenter as s On s.client_id=c.client_id
                   left join smc_school as l ON l.school_id = s.school_id
                WHERE {$datawhere} and client_distributionstatus = '1' and client_tracestatus =4
                GROUP BY current_day
                ORDER BY current_day ASC  LIMIT 0,10  ";
        $postive_clientList = $this->DataControl->selectClear($postive_sql);
        if ($postive_clientList) {
            $arr_client_day = array_column($postive_clientList, 'client_num', 'current_day');
        } else {
            $arr_client_day = array();
        }
        $arr_postivet_clientList = array();
        foreach ($date as $key => $dateOne) {
            if ($arr_client_day[$dateOne]) {
                $arr_postivet_clientList[$key] = $arr_client_day[$dateOne];
            } else {
                $arr_postivet_clientList[$key] = '0';
            }
        }
        $allList[2]['name'] = $this->LgStringSwitch('新增转正客户');
        $allList[2]['data'] = $arr_postivet_clientList;
        $data = array();
        $data['allList'] = $allList;
        $data['legendData'] = $this->LgArraySwitch(array('新增有效名单', '新增意向客户', '新增转正客户'));
        $data['xAxisData'] = $date;
        return $data;
    }


    //----------------------------------------------------渠道业绩---------------------

    /**
     * 招生渠道新增
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function CountClientSource($request)
    {
        $activity_where = $datawhere = " 1";

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $datawhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
            $datawhere .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $datawhere .= " and l.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $activity_where .= " and l.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] !== '') {
            $datawhere .= " and l.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";

            $activity_where .= " and l.school_id  in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and l.school_id  ='{$request['school_id']}' ";
            $activity_where .= " and l.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and l.school_province  ='{$request['school_province']}' ";
            $activity_where .= " and l.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and l.school_city  ='{$request['school_city']}' ";
            $activity_where .= " and l.school_city  ='{$request['school_city']}' ";
        }


        //渠道类型数量
        $from_media_sql = "select count(frommedia_id) as frommedia_num  from  crm_code_frommedia as cf where cf.company_id='{$this->company_id}'  ";
        $from_media = $this->DataControl->selectOne($from_media_sql);
        if ($from_media) {
            $media_count = $from_media['frommedia_num'];
        } else {
            $media_count = 0;
        }

        //渠道明细数量
        $channel_sql = "select count(cf.channel_id) as channel_num  from  crm_code_channel as cf where cf.company_id='{$this->company_id}'  ";
        $channel = $this->DataControl->selectOne($channel_sql);
        if ($channel) {
            $channel_count = $channel['channel_num'];
        } else {
            $channel_count = 0;
        }

        //活动数量
        $activity_sql = "select count(DISTINCT sa.activity_id )  as activity_num  from crm_sell_activity as sa
            left join crm_sell_activity_school as sal On sa.activity_id =sal.activity_id 
            left join smc_school as l  ON l.school_id =sal.school_id
            where {$activity_where} and sa.company_id = '{$this->company_id}';
               ";
        $activity = $this->DataControl->selectOne($activity_sql);
        if ($activity) {
            $activity_count = $activity['activity_num'];
        } else {
            $activity_count = 0;
        }


        $client_sql = "SELECT c.client_id,c.client_distributionstatus,c.client_tracestatus
                FROM crm_client as c
                LEFT JOIN crm_client_schoolenter as s ON c.client_id = s.client_id and c.company_id = s.company_id
                LEFT JOIN smc_school as l ON l.school_id = s.school_id
                WHERE {$datawhere}  and  c.client_source <> '' and  c.company_id = '{$this->company_id}'
                GROUP BY c.client_id
                ";

        $clientList = $this->DataControl->selectClear($client_sql);
        $client_all_count = 0;
        $client_sucess_count = 0;
        if ($clientList) {
            foreach ($clientList as &$clientOne) {
                $client_all_count++;
                if ($clientOne['client_tracestatus'] == 4) {
                    $client_sucess_count++;
                }
            }
        }

        $data = array();
        $data['media_count'] = $media_count;
        $data['channel_count'] = $channel_count;
        $data['activity_count'] = $activity_count;
        $data['client_all_count'] = $client_all_count;
        $data['client_sucess_count'] = $client_sucess_count;
        return $data;
    }


    /**
     * 招生渠道转化对比
     * author: ling
     * 对应接口文档 0001
     * @return array
     */
    function ClientChannel($request)
    {
        $frommedia_where = '1';
        $client_where = '1';
        $client_datawhere = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
//            $starttime = strtotime($request['starttime']);
            $client_where .= " and FROM_UNIXTIME(t.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
//            $endtime = strtotime($request['starttime']) + 3600 * 24 - 1;
            $client_where .= " and FROM_UNIXTIME(t.client_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";

        }
        $client_datawhere = $client_where;
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      where co.organize_id  = '{$request['organize_id']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                left join gmc_company_organizeschool as col ON col.school_id = s.school_id
                where col.organize_id = '{$request['organize_id']}' )";
        }
        if (isset($reqeust['organizeclass_id']) && $request['organizeclass_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left  join gmc_company_organize as  g ON g.organize_id = co.organize_id
                      where g.organizeclass_id  = '{$request['organizeclass_id']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                left join gmc_company_organizeschool as col ON col.school_id = s.school_id
                 left  join gmc_company_organize as  g ON g.organize_id = col.organize_id
                where g.organizeclass_id = '{$request['organizeclass_id']}' )";
        }

        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $frommedia_where = " 1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                      left  join smc_school as s On s.school_id=ol.school_id
                      where s.school_province  = '{$request['school_province']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                where s.school_province = '{$request['school_province']}' )";

        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $frommedia_where = "1  and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                        left  join smc_school as s On s.school_id=ol.school_id
                      where s.school_city  = '{$request['school_city']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                where s.school_city = '{$request['school_city']}' )";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                      where ol.school_id  = '{$request['school_id']}' )  ";
            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                where s.school_id = '{$request['school_id']}' )";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $frommedia_where .= " and cf.frommedia_name like '%{$request['keyword']}%'";
        }

        if (isset($request['order_by']) && $request['order_by'] != '' && (isset($request['order_by_field']) && $request['order_by_field'] != '')) {
            $order_by = $request['order_by_field'] . ' ' . $request['order_by'];
            if ($request['order_by_field'] == 'rate') {
                $order_by = 'client_positivenum/client_num  ' . $request['order_by'];
            }
        } else {
            $order_by = "client_positivenum/client_num DESC";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "select cf.frommedia_name , 
            (select count(cl.channel_id) from crm_code_channel as cl where cl.channel_medianame =cf.frommedia_name and cl.company_id = cf.company_id) as channel_num,
            (select count(t.client_id) from crm_client as t where t.client_source =cf.frommedia_name and t.company_id = cf.company_id  and t.client_tracestatus  <> -2 and {$client_datawhere}) as client_num,
            (select count(t.client_id) from crm_client as t where t.client_source =cf.frommedia_name and t.company_id = cf.company_id and t.client_tracestatus in (1,2,3) and {$client_datawhere}) as client_intentionnum,
             (select count( DISTINCT t.client_id) from crm_client as t,crm_client_invite as i where i.client_id = t.client_id and t.client_source =cf.frommedia_name and t.company_id = cf.company_id and {$client_datawhere}) as client_invitenum,
             (select count(DISTINCT t.client_id) from crm_client as t,crm_client_audition as a where a.client_id = t.client_id and t.client_source =cf.frommedia_name and t.company_id = cf.company_id and {$client_datawhere}) as client_auditionnum,
             (select count(t.client_id) from crm_client as t where t.client_source =cf.frommedia_name and t.company_id = cf.company_id and t.client_tracestatus = -1 and {$client_datawhere}) as client_lossnum,
             (select count(t.client_id) from crm_client as t where t.client_source =cf.frommedia_name and t.company_id = cf.company_id and t.client_tracestatus =4 and {$client_datawhere}) as client_positivenum
            from crm_code_frommedia as cf 
            where cf.company_id ='{$this->company_id}' and  {$frommedia_where}
            order by  {$order_by}
            limit {$pagestart},{$num}
             ";

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$dataOne) {
                $dataOne['rate'] = $dataOne['client_num'] > 0 ? round($dataOne['client_positivenum'] / $dataOne['client_num'], 4) * 100 . '%' : '0.00%';
            }
        } else {
            $dataList = array();
        }

        $allNum = $this->DataControl->selectOne("select count(frommedia_id) as  all_num from crm_code_frommedia as cf where cf.company_id ='{$this->company_id}' and  {$frommedia_where}");

        $result = array();
        $result['list'] = $dataList;
        $result['allnum'] = $allNum['all_num'];
        return $result;
    }


    /**
     * 转正来源渠道对比
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function PositiveChannel($request)
    {
        $frommedia_where = '1';
        $client_datawhere = '1';
        $client_where = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
//            $starttime = strtotime($request['starttime']);
            $client_where .= " and FROM_UNIXTIME(t.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
//            $endtime = strtotime($request['starttime']) + 3600 * 24 - 1;
            $client_where .= " and FROM_UNIXTIME(t.client_createtime,'%Y-%m-%d') <= '{$request['starttime']}'";

        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      where co.organize_id  = '{$request['organize_id']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                left join gmc_company_organizeschool as col ON col.school_id = s.school_id
                where col.organize_id = '{$request['organize_id']}' )";
        }
        if (isset($reqeust['organizeclass_id']) && $request['organizeclass_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left  join gmc_company_organize as  g ON g.organize_id = co.organize_id
                      where g.organizeclass_id  = '{$request['organizeclass_id']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                left join gmc_company_organizeschool as col ON col.school_id = s.school_id
                 left  join gmc_company_organize as  g ON g.organize_id = col.organize_id
                where g.organizeclass_id = '{$request['organizeclass_id']}' )";
        }

        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $frommedia_where = " 1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                      left  join smc_school as s On s.school_id=ol.school_id
                      where s.school_province  = '{$request['school_province']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                where s.school_province = '{$request['school_province']}' )";

        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $frommedia_where = "1  and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                        left  join smc_school as s On s.school_id=ol.school_id
                      where s.school_city  = '{$request['school_city']}' )  ";

            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                where s.school_city = '{$request['school_city']}' )";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                      where ol.school_id  = '{$request['school_id']}' )  ";
            $client_datawhere = $client_where . "  and  t.client_id  in (select cs.client_id from  crm_client_schoolenter as  cs
                left join smc_school as s  On cs.school_id = s.school_id
                where s.school_id = '{$request['school_id']}' )";
        }

        $sql = "select  cf.frommedia_name,
           (select count(t.client_id) from crm_client as t where t.client_source =cf.frommedia_name and t.company_id = cf.company_id and t.client_tracestatus  = 4 and {$client_datawhere}) as client_positivenum,
           (select sum(t.client_id) from crm_client as t where t.client_source =cf.frommedia_name and t.company_id = cf.company_id and t.client_tracestatus  <> -2 and {$client_datawhere}) as client_allnum
        from crm_code_frommedia as  cf 
        where cf.company_id='{$this->company_id}'  and  {$frommedia_where} ";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => &$dataOne) {
                $dataOne['rate'] = $dataOne['client_allnum'] > 0 ? round($dataOne['client_positivenum'] / $dataOne['client_allnum'], 4) * 100 . '%' : '0.00%';
            }
            $arr_frommedia = array_column($dataList, "frommedia_name");
            $arr_positivenum = array_column($dataList, "client_positivenum");
            $arr_rate = array_column($dataList, "rate");

        } else {
            $arr_frommedia = array();
            $arr_positivenum = array();
            $arr_rate = array();
        }

        $data = array();
        $data['x_data'] = $arr_frommedia;
        $y_data = array();
        $y_data['name'] = $this->LgStringSwitch('渠道类型');
        $y_data['data'] = $arr_positivenum;
        $y_data['rate_data'] = $arr_rate;
        $data['y_data'] = $y_data;
        return $data;
    }

    /**
     * 招生渠道比例
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array
     */
    function ChannelRate($request)
    {
        $activity_time_where = '1';
        $frommedia_where = '1';
        $channel_where = '1';
        $activity_where = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $starttime = strtotime($request['starttime']);
//            $activity_where .= " and sa.activity_starttime >= '{$starttime}'";

        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d H:i:s', strtotime($request['endtime']));
            $activity_time_where .= " and  sa.activity_endtime <= '{$request['endtime']}'";

        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      where co.organize_id  = '{$request['organize_id']}' )  ";

            $channel_where .= " and  cl.channel_id in ( select co.channel_id from crm_channel_organize as  co where  co.organize_id ='{$request['organize_id']}')";

            $activity_where = $activity_time_where . " and  sa.activity_id in ( select  
               sal.activity_id from  crm_sell_activity_school as sal
             left join gmc_company_organizeschool as co On sal.school_id = co.school_id
             where   co.organize_id ='{$request['organize_id']}')";
        }

        if (isset($reqeust['organizeclass_id']) && $request['organizeclass_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left  join gmc_company_organize as  g ON g.organize_id = co.organize_id
                      where g.organizeclass_id  = '{$request['organizeclass_id']}' )  ";

            $channel_where .= " and  cl.channel_id in ( 
                    select co.channel_id 
                    from crm_channel_organize as  co
                    left join gmc_company_organize as g On  g.organize_id = co.organize_id
                    where  g.organizeclass_id ='{$request['organizeclass_id']}')";

            $activity_where = $activity_time_where . " and  sa.activity_id in ( select 
               sal.activity_id from  crm_sell_activity_school as sal
             left join gmc_company_organizeschool as co On sal.school_id = co.school_id
             left join gmc_company_organize as g ON g.organize_id = co.organize_id
             where   g.organizeclass_id ='{$request['organizeclass_id']}')";
        }

        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $frommedia_where = " 1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                      left  join smc_school as s On s.school_id=ol.school_id
                      where s.school_province  = '{$request['school_province']}' )  ";

            $channel_where = "  cl.channel_id in ( select co.channel_id from crm_channel_organize as  co
                left join gmc_company_organizeschool as  col On col.organize_id = co.organize_id
                left join smc_school as s On s.school_id = col.school_id
                 where  s.school_province  ='{$request['school_province']}' 
                 )";

            $activity_where = $activity_time_where . " and  sa.activity_id in ( select 
               sal.activity_id from  crm_sell_activity_school as sal
             left join gmc_company_organizeschool as co On sal.school_id = co.school_id
             left join smc_school as s ON s.school_id = sal.school_id
             where   s.school_province ='{$request['school_province']}')";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $frommedia_where = "1  and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                        left  join smc_school as s On s.school_id=ol.school_id
                      where s.school_city  = '{$request['school_city']}' )  ";

            $activity_where = $activity_time_where . " and  sa.activity_id in ( select sal.activity_id from  crm_sell_activity_school as sal
             left join gmc_company_organizeschool as co On sal.school_id = co.school_id
             left join smc_school as s ON s.school_id = sal.school_id
             where   s.school_city ='{$request['school_city']}')";

            $channel_where = " cl.channel_id in ( select co.channel_id from crm_channel_organize as  co
                left join gmc_company_organizeschool as  col On col.organize_id = co.organize_id
                left join smc_school as s On s.school_id = col.school_id
                 where  s.school_province  ='{$request['school_province']}' 
                 )";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $frommedia_where = "1 and cf.frommedia_name in (select cl.channel_medianame from crm_code_channel as cl
                      left join  crm_channel_organize as co on cl.channel_id = co.channel_id
                      left join gmc_company_organizeschool as ol on ol.organize_id = co.organize_id
                      where ol.school_id  = '{$request['school_id']}' )  ";

            $activity_where = $activity_time_where . " and  sa.activity_id in ( select sal.activity_id from  crm_sell_activity_school as sal
             left join gmc_company_organizeschool as co On sal.school_id = co.school_id
             left join smc_school as s ON s.school_id = sal.school_id
             where  s.school_id ='{$request['school_id']}')";

            $channel_where = " cl.channel_id in ( select co.channel_id from crm_channel_organize as  co
                left join gmc_company_organizeschool as  col On col.organize_id = co.organize_id
                left join smc_school as s On s.school_id = col.school_id
                 where  s.school_province  ='{$request['school_province']}' 
                 )";
        }


        $sql = "select  cf.frommedia_name,
            (select count(cl.channel_id) from crm_code_channel as cl where cl.channel_medianame =cf.frommedia_name and cl.company_id = cf.company_id and {$channel_where}) as channel_num,
            (select count(cl.channel_id) from crm_code_channel as cl where  cl.company_id = cf.company_id) as channel_allnum,
            (select count(sa.activity_id) from crm_sell_activity as sa where sa.frommedia_name = cf.frommedia_name and sa.company_id = cf.company_id and  {$activity_where}) as  activity_num
        from crm_code_frommedia as cf
        where cf.company_id='{$this->company_id}' and  {$frommedia_where} ";
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$dataOne) {
                $dataOne['rate'] = $dataOne['channel_allnum'] > 0 ? round($dataOne['channel_num'] / $dataOne['channel_allnum'], 4) * 100 . '%' : '0.00%';
            }
        } else {
            $dataList = array();
        }
        return $dataList;
    }

    /**
     * 集团活动转化排行
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return mixed
     */
    function CompanyActivityPositive($request)
    {
        $activity_datawhere = "1";
        $client_where = '1';

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
//            $starttime = strtotime($request['starttime']);
            $client_where .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
//            $endtime = strtotime($request['starttime']) + 3600 * 24 - 1;
            $client_where .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $activity_datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $activity_datawhere .= " and s.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $activity_datawhere .= " and s.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $activity_datawhere .= " and s.school_city  ='{$request['school_city']}' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $activity_datawhere .= " and sa.activity_name like '%{$request['keyword']}%'";
        }

        if (isset($request['order_by']) && $request['order_by'] != '' && (isset($request['order_by_field']) && $request['order_by_field'] != '')) {
            $order_by = $request['order_by_field'] . ' ' . $request['order_by'];
            if ($request['order_by_field'] == 'rate') {
                $order_by = 'client_positivenum/client_allnum  ' . $request['order_by'];
            }
        } else {
            $order_by = "client_positivenum/client_allnum DESC";
        }

        $sql = "select   sa.activity_name ,
            (select count(c.client_id) from  crm_client as  c where c.activity_id = sa.activity_id and c.client_tracestatus <> '-2'  and {$client_where}) as client_allnum,
            (select count(c.client_id) from  crm_client as  c where c.activity_id = sa.activity_id and c.client_tracestatus ='-1'  and {$client_where} ) as client_lossnum,
            (select count(c.client_id) from  crm_client as  c where c.activity_id = sa.activity_id and c.client_tracestatus ='4'   and {$client_where} ) as client_positivenum
            from  crm_sell_activity  as sa 
            left join crm_sell_activity_school as sat ON sa.activity_id =sat.activity_id
            left join smc_school as s ON sat.school_id =s.school_id
            where sa.company_id='{$this->company_id}' and  sa.activity_type ='1' and {$activity_datawhere}
            group by  sa.activity_id 
            order by {$order_by}
            limit {$pagestart},{$num}
            ";


        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => &$dataOne) {
                $dataOne['rate'] = $dataOne['client_allnum'] > 0 ? round($dataOne['client_positivenum'] / $dataOne['client_allnum'], 4) * 100 . '%' : '0.00%';
            }
        } else {
            $dataList = array();
        }

        $allNum = $this->DataControl->selectOne("select count(DISTINCT  sa.activity_id ) as  all_num   from  crm_sell_activity  as sa 
            left join crm_sell_activity_school as sat ON sa.activity_id =sat.activity_id
            left join smc_school as s ON sat.school_id =s.school_id
            where sa.company_id='{$this->company_id}' and  sa.activity_type ='1' and {$activity_datawhere}
             ");

        $result = array();
        $result['list'] = $dataList;
        $result['allnum'] = $allNum['all_num'] + 0;
        return $result;
    }

    /**
     * 校园活动转化排行
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return mixed
     */
    function SchoolActivityPositive($request)
    {
        $activity_datawhere = "1";
        $client_where = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
//            $starttime = strtotime($request['starttime']);
            $client_where .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
//            $endtime = strtotime($request['starttime']) + 3600 * 24 - 1;
            $client_where .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $activity_datawhere .= " and l.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $activity_datawhere .= " and l.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $activity_datawhere .= " and l.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $activity_datawhere .= " and l.school_city  ='{$request['school_city']}' ";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $activity_datawhere .= " and sa.activity_name like '%{$request['keyword']}%'";
        }

        if (isset($request['order_by']) && $request['order_by'] != '' && (isset($request['order_by_field']) && $request['order_by_field'] != '')) {
            $order_by = $request['order_by_field'] . ' ' . $request['order_by'];
            if ($request['order_by_field'] == 'rate') {
                $order_by = 'client_positivenum/client_allnum  ' . $request['order_by'];
            }
        } else {
            $order_by = "client_positivenum/client_allnum DESC";
        }

        $sql = "select  sa.activity_name ,
            (select count(c.client_id) from  crm_client as  c where c.activity_id = sa.activity_id and c.client_tracestatus <> '-2' and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as  c where c.activity_id = sa.activity_id and c.client_tracestatus ='-1' and {$client_where} ) as client_lossnum,
            (select count(c.client_id) from  crm_client as  c where c.activity_id = sa.activity_id and c.client_tracestatus ='4' and {$client_where} ) as client_positivenum
            from  crm_sell_activity  as sa 
            left join crm_sell_activity_school as  s ON s.activity_id = sa.activity_id
            left join smc_school as l ON  l.school_id =s.school_id
            where sa.company_id='{$this->company_id}' and  sa.activity_type ='0' and  {$activity_datawhere}
            group by  sa.activity_id 
            order by {$order_by}
            limit {$pagestart},{$num}
            ";


        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => &$dataOne) {
                $dataOne['rate'] = $dataOne['client_allnum'] > 0 ? round($dataOne['client_positivenum'] / $dataOne['client_allnum'], 4) * 100 . '%' : '0.00%';
            }
        } else {
            $dataList = array();
        }
        $allNum = $this->DataControl->selectOne("select count(DISTINCT sa.activity_id ) as  all_num   from  crm_sell_activity  as sa 
            left join crm_sell_activity_school as sat ON sa.activity_id =sat.activity_id
            left join smc_school as l ON sat.school_id =l.school_id
            where sa.company_id='{$this->company_id}' and  sa.activity_type ='1' and {$activity_datawhere}
             ");

        $result = array();
        $result['list'] = $dataList;
        $result['allnum'] = $allNum['all_num'] + 0;
        return $result;

    }

    //---------------------------------------招生业绩------------------------------


    /**
     * 招生业绩统计
     * author: ling
     * 对应接口文档 0001
     * @return array
     */
    function CountClientPositive($request)
    {
        $track_datawhere = '1';
        $audtion_datawhere = '1';
        $client_where = '1';
        $invite_where = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $starttime = strtotime($request['starttime']);
            $track_datawhere .= " and k.track_createtime >= '{$starttime}'";
            $audtion_datawhere .= " and a.audition_visittime >= '{$request['starttime']}'";
            $invite_where .= " and i.invite_visittime >= '{$request['starttime']}'";
            $client_where .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
            $endtime = strtotime($request['endtime']) + 3600 * 24 - 1;
            $track_datawhere .= " and k.track_createtime <= '{$endtime}'";
            $audtion_datawhere .= " and a.audition_visittime <= '{$request['endtime']}'";
            $invite_where .= " and i.invite_visittime <= '{$request['endtime']}'";
            $client_where .= " and FROM_UNIXTIME(c.client_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $track_datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $audtion_datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $invite_where .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
            $client_where .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $client_where .= " and s.school_id  ='{$request['school_id']}' ";
            $track_datawhere .= " and s.school_id  ='{$request['school_id']}' ";
            $audtion_datawhere .= " and s.school_id  ='{$request['school_id']}' ";
            $invite_where .= " and s.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $client_where .= " and s.school_province  ='{$request['school_province']}' ";
            $track_datawhere .= " and s.school_province  ='{$request['school_province']}' ";
            $audtion_datawhere .= " and s.school_province  ='{$request['school_province']}' ";
            $invite_where .= " and s.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $track_datawhere .= " and s.school_city  ='{$request['school_city']}' ";
            $client_where .= " and s.school_city  ='{$request['school_city']}' ";
            $audtion_datawhere .= " and s.school_city  ='{$request['school_city']}' ";
            $invite_where .= " and s.school_city  ='{$request['school_city']}' ";
        }

        $sql = " select DISTINCT k.client_id
        from  crm_client_track as k
        left join smc_school as  s On k.school_id = s.school_id
        left  join crm_client as t On t.client_id=k.client_id
        where k.track_isactive =1 and t.client_tracestatus <> '-2'
        and {$track_datawhere}
         and s.company_id='{$this->company_id}'";

        $trackNum = $this->DataControl->selectClear($sql);
        if ($trackNum) {
            $track_clientnum = count($trackNum);
        } else {
            $track_clientnum = 0;
        }

        $sql = " select  k.client_id
        from  crm_client_track as k
        left join smc_school as  s On k.school_id = s.school_id
        where k.track_isactive =1 and k.track_validinc =1
        and {$track_datawhere}
         and s.company_id='{$this->company_id}'";

        $trackNum = $this->DataControl->selectClear($sql);
        if ($trackNum) {
            $track_clienttracknum = count($trackNum);
        } else {
            $track_clienttracknum = 0;
        }

        //柜询预约数,到访数
        $invite_sql = "
            select count(i.invite_id) as invite_num
            from crm_client_invite as i
            left join smc_school as s ON i.school_id = s.school_id
            where s.company_id='{$this->company_id}' and {$invite_where}
        ";
        $inviteNum = $this->DataControl->selectOne($invite_sql);
        $invite_num = $inviteNum['invite_num'] + 0;

        $inviteclient_sql = "
            select count(i.invite_id) as invite_clientnum
            from crm_client_invite as i
            left join smc_school as s ON i.school_id = s.school_id
            where s.company_id='{$this->company_id}' and {$invite_where} and i.invite_isvisit =1
        ";
        $inviteclientNum = $this->DataControl->selectOne($inviteclient_sql);
        $invite_clientnum = $inviteclientNum['invite_clientnum'] + 0;


//        $sql = " select  k.track_id
//        from  crm_client_track as k
//        left join smc_school as  s On k.school_id = s.school_id
//        where k.track_isactive =1
//         and {$track_datawhere}
//         and s.company_id='{$this->company_id}'";
//       ;
//        $track = $this->DataControl->selectClear($sql);
//        if ($track) {
//            $track_num = count($track);
//        } else {
//            $track_num = 0;
//        }

        $sql = " select a.audition_id ,a.audition_genre,a.audition_isvisit
        from  crm_client_audition as a
        left join smc_school as  s On s.school_id = a.school_id
        where
         {$audtion_datawhere} 
        and s.company_id ='{$this->company_id}'  ";

        $auditionList = $this->DataControl->selectClear($sql);
        $oh_num = 0;
        $oh_arrivenum = 0;
        $class_au_num = 0;
        $class_au_arrivenum = 0;
        if ($auditionList) {
            foreach ($auditionList as &$auditionOne) {
                if ($auditionOne['audition_genre'] == 0) {
                    $oh_num++;
                } else {
                    $class_au_num++;
                }
                if ($auditionOne['audition_genre'] == 0 && $auditionOne['audition_isvisit'] == 1) {
                    $oh_arrivenum++;
                } elseif ($auditionOne['audition_genre'] == 1 && $auditionOne['audition_isvisit'] == 1) {
                    $class_au_arrivenum++;
                }
            }
        }
        $sql = "select  c.client_id,c.client_tracestatus
            from crm_client as c 
            left join crm_client_schoolenter as st  On c.client_id = st.client_id
            left  join smc_school as s On s.school_id = st.school_id
            where c.company_id='{$this->company_id}'
             and {$client_where}
             group by c.client_id 
            ";
        $clientList = $this->DataControl->selectClear($sql);

        $positive_num = 0;
        $uneffective_num = 0;
        if ($clientList) {
            foreach ($clientList as &$clientOne) {
                if ($clientOne['client_tracestatus'] == '-2') {
                    $uneffective_num++;
                } elseif ($clientOne['client_tracestatus'] == '4') {
                    $positive_num++;
                }
            }
        }
        $result = array();
        $result['track_clientnum'] = $track_clientnum;
        $result['track_num'] = $track_clienttracknum;
        $result['invite_num'] = $invite_num;
        $result['invite_clientnum'] = $invite_clientnum;
        $result['oh_num'] = $oh_num;
        $result['oh_arrivenum'] = $oh_arrivenum;
        $result['class_au_num'] = $class_au_num;
        $result['class_au_arrivenum'] = $class_au_arrivenum;
        $result['uneffective_num'] = $uneffective_num;
        $result['positive_num'] = $positive_num;
        return $result;
    }

    /**
     * 招生跟进状况
     * author: ling
     * 对应接口文档 0001
     * @return array
     */
    function ClientTrackLog($request)
    {
        $track_datawhere = '1';
        $audtion_datawhere = '1';
        $invite_datawhere = '1';
        $datawhere = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $starttime = strtotime($request['starttime']);
            $track_datawhere .= " and k.track_createtime >= '{$starttime}'";
            $audtion_datawhere .= " and a.audition_visittime >= '{$request['starttime']}'";
            $invite_datawhere .= " and i.invite_visittime >= '{$request['starttime']}'";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d H:i:s', strtotime($request['endtime']));
            $endtime = strtotime($request['endtime']) + 3600 * 24 - 1;
            $track_datawhere .= " and k.track_createtime <= '{$endtime}'";
            $audtion_datawhere .= " and a.audition_visittime <= '{$request['endtime']}'";
            $invite_datawhere .= " and i.invite_visittime <= '{$request['endtime']}'";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and  s.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and  s.school_city  ='{$request['school_city']}' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%')";
        }

        if (isset($request['order_by']) && $request['order_by'] != '' && (isset($request['order_by_field']) && $request['order_by_field'] != '')) {
            $order_by = $request['order_by_field'] . ' ' . $request['order_by'];
        } else {
            $order_by = "client_num DESC";
        }
        if (!$request['type']) {
            $request['type'] = 3;
        }

        if ($request['type'] == 1) {
            $sql = "
            select  r.region_name,sum(client_num) as client_num,sum(track_num) as track_num,sum(invite_num) as invite_num,sum(invite_arr_num) as invite_arr_num,sum(oh_audition_num) as oh_audition_num,sum(oh_audition_arr_num) as oh_audition_arr_num, sum(class_audition_num) as class_audition_num, sum(class_audition_arr_num) as class_audition_arr_num
            from  (
            select s.school_id,s.school_province,
            (select count(DISTINCT k.client_id) from crm_client_track as k where k.school_id = s.school_id AND k.track_isactive = 1 AND {$track_datawhere}) as  client_num,
            (select count(k.track_id)  from  crm_client_track as k where k.school_id =s.school_id  and  k.track_isactive =1 AND {$track_datawhere}) as  track_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id AND {$invite_datawhere} ) as  invite_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id and invite_isvisit =1  AND {$invite_datawhere}) as  invite_arr_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0 AND {$audtion_datawhere}) as  oh_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0  and a.audition_isvisit =1  AND {$audtion_datawhere}) as  oh_audition_arr_num,
             (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere} ) as  class_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere}  and a.audition_isvisit =1 ) as  class_audition_arr_num
            from smc_school as s
            where  {$datawhere} and  s.company_id='{$this->company_id}'
            ) as q
            left join smc_code_region as r On r.region_id = q.school_province
            where r.parent_id =1 and  q.school_province > 0    
            group by  r.region_id 
            order by {$order_by}
            limit {$pagestart},{$num}
            ";


        } elseif ($request['type'] == 2) {
            $sql = "
            select  r.region_name,sum(client_num) as client_num,sum(track_num) as track_num,sum(invite_num) as invite_num,sum(invite_arr_num) as invite_arr_num,sum(oh_audition_num) as oh_audition_num,sum(oh_audition_arr_num) as oh_audition_arr_num, sum(class_audition_num) as class_audition_num, sum(class_audition_arr_num) as class_audition_arr_num
            from  (
            select s.school_id,s.school_city,
            (select count(DISTINCT k.client_id) from crm_client_track as k where k.school_id = s.school_id AND k.track_isactive = 1 AND {$track_datawhere}) as  client_num,
            (select count(k.track_id)  from  crm_client_track as k where k.school_id =s.school_id  and  k.track_isactive =1 AND {$track_datawhere}) as  track_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id AND {$invite_datawhere} ) as  invite_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id and invite_isvisit =1  AND {$invite_datawhere}) as  invite_arr_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0 AND {$audtion_datawhere}) as  oh_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0  and a.audition_isvisit =1  AND {$audtion_datawhere}) as  oh_audition_arr_num,
             (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere} ) as  class_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere}  and a.audition_isvisit =1 ) as  class_audition_arr_num
            from smc_school as s
            where  {$datawhere} and  s.company_id='{$this->company_id}'
            ) as q
            left join smc_code_region as r On r.region_id = q.school_city
            where r.parent_id =2 and  q.school_province > 0    
            group by  r.region_id 
             order by {$order_by}
            limit {$pagestart},{$num}
            ";

        } elseif ($request['type'] == 3) {
            $sql = "select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
            (select count(DISTINCT k.client_id) from crm_client_track as k where k.school_id = s.school_id AND k.track_isactive = 1 AND {$track_datawhere}) as  client_num,
            (select count(k.track_id)  from  crm_client_track as k where k.school_id =s.school_id  and  k.track_isactive =1 AND {$track_datawhere}) as  track_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id AND {$invite_datawhere} ) as  invite_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id and invite_isvisit =1  AND {$invite_datawhere}) as  invite_arr_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0 AND {$audtion_datawhere}) as  oh_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0  and a.audition_isvisit =1  AND {$audtion_datawhere}) as  oh_audition_arr_num,
             (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere} ) as  class_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere}  and a.audition_isvisit =1 ) as  class_audition_arr_num
            from smc_school as s
            where  {$datawhere} and  s.company_id='{$this->company_id}'
            order by {$order_by}
            limit {$pagestart},{$num}
            ";

        } elseif ($request['type'] == 4) {
            $sql = "
            select  co.organize_cnname,sum(client_num) as client_num,sum(track_num) as track_num,sum(invite_num) as invite_num,sum(invite_arr_num) as invite_arr_num,sum(oh_audition_num) as oh_audition_num,sum(oh_audition_arr_num) as oh_audition_arr_num, sum(class_audition_num) as class_audition_num, sum(class_audition_arr_num) as class_audition_arr_num
            from  (
            select s.school_id,s.school_province,
            (select count(DISTINCT k.client_id) from crm_client_track as k where k.school_id = s.school_id AND k.track_isactive = 1 AND {$track_datawhere}) as  client_num,
            (select count(k.track_id)  from  crm_client_track as k where k.school_id =s.school_id  and  k.track_isactive =1 AND {$track_datawhere}) as  track_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id AND {$invite_datawhere} ) as  invite_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id and invite_isvisit =1  AND {$invite_datawhere}) as  invite_arr_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0 AND {$audtion_datawhere}) as  oh_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0  and a.audition_isvisit =1  AND {$audtion_datawhere}) as  oh_audition_arr_num,
             (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere} ) as  class_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere}  and a.audition_isvisit =1 ) as  class_audition_arr_num
            from smc_school as s
            where  {$datawhere} and  s.company_id='{$this->company_id}'
            ) as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
            group by  co.organize_id 
             order by {$order_by}
            limit {$pagestart},{$num}
            ";

        } elseif ($request['type'] == 5) {
            $sql = "
            select  co.organize_cnname,sum(client_num) as client_num,sum(track_num) as track_num,sum(invite_num) as invite_num,sum(invite_arr_num) as invite_arr_num,sum(oh_audition_num) as oh_audition_num,sum(oh_audition_arr_num) as oh_audition_arr_num, sum(class_audition_num) as class_audition_num, sum(class_audition_arr_num) as class_audition_arr_num
            from  (
            select s.school_id,s.school_province,
            (select count(DISTINCT k.client_id) from crm_client_track as k where k.school_id = s.school_id AND k.track_isactive = 1 AND {$track_datawhere}) as  client_num,
            (select count(k.track_id)  from  crm_client_track as k where k.school_id =s.school_id  and  k.track_isactive =1 AND {$track_datawhere}) as  track_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id AND {$invite_datawhere} ) as  invite_num,
            (select count(i.invite_id)  from  crm_client_invite as i where i.school_id =s.school_id and invite_isvisit =1  AND {$invite_datawhere}) as  invite_arr_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0 AND {$audtion_datawhere}) as  oh_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =0  and a.audition_isvisit =1  AND {$audtion_datawhere}) as  oh_audition_arr_num,
             (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere} ) as  class_audition_num,
            (select count(a.audition_id)  from  crm_client_audition as a where a.school_id =s.school_id  and a.audition_genre =1 AND {$audtion_datawhere}  and a.audition_isvisit =1 ) as  class_audition_arr_num
            from smc_school as s
            where  {$datawhere} and  s.company_id='{$this->company_id}'
            ) as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id >0  and co.company_id='{$this->company_id}'
            group by  co.organize_id 
             order by {$order_by}
            limit {$pagestart},{$num}
            ";
        }


        $dataList = $this->DataControl->selectClear($sql);

        if (!$dataList) {
            $dataList = array();
        }

        $allNum = $this->DataControl->selectOne("select count(s.school_id) as all_num from smc_school as s where  {$datawhere} and  s.company_id='{$this->company_id}' ");
        $result = array();
        $result['list'] = $dataList;
        $result['allnum'] = $allNum['all_num'];
        return $result;
    }

    /**
     * 招生转化排行
     * author: ling
     * 对应接口文档 0001
     */
    function ClientPosivite($request)
    {
        $client_where = '1';
        $datawhere = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $starttime = strtotime($request['starttime']);
            $client_where .= " and client_createtime >='{$starttime}' ";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
            $endtime = strtotime($request['starttime']) + 3600 * 24 - 1;
            $client_where .= " and client_createtime <='{$endtime}' ";
        }

        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['second_organize_id']) && $request['second_organize_id'] !== '') {
            $datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['second_organize_id']}') ";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and  s.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and  s.school_city  ='{$request['school_city']}' ";
        }


        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%')";
        }

        if (isset($request['order_by']) && $request['order_by'] !== 'DESC') {
            $order_by = "client_positivenum/client_allnum ASC";

        } else {
            $order_by = "client_positivenum/client_allnum DESC";
        }

        if (!$request['type']) {
            $request['type'] = 1;
        }
        if ($request['type'] == 1) {
            $sql = "select  r.region_name,sum(q.client_allnum) as client_allnum, sum(q.uneffective_num) as uneffective_num, sum(q.client_positivenum) as client_positivenum
            from (select  s.school_id ,s.school_province,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join smc_code_region as r On r.region_id = q.school_province
            where r.parent_id =1 and  q.school_province > 0    
            group by  r.region_id 
             order  by {$order_by} 
            limit {$pagestart},{$num}
             ";

            $allNum = $this->DataControl->selectOne("select  count(DISTINCT r.region_id) as all_num
            from (select  s.school_id ,s.school_province,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join smc_code_region as r On r.region_id = q.school_province
            where r.parent_id =1 and  q.school_province > 0    
             ");


        } elseif ($request['type'] == 2) {
            $sql = "select  r.region_name,sum(q.client_allnum) as client_allnum, sum(q.uneffective_num) as uneffective_num, sum(q.client_positivenum) as client_positivenum
            from (select  s.school_id ,s.school_city,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join smc_code_region as r On r.region_id = q.school_city
            where r.parent_id =2 and  q.school_province > 0    
            group by  r.region_id 
             order  by {$order_by} 
            limit {$pagestart},{$num}
             ";

            $allNum = $this->DataControl->selectOne("select count(DISTINCT r.region_id) as all_num
            from (select  s.school_id ,s.school_city,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join smc_code_region as r On r.region_id = q.school_city
            where r.parent_id =2 and  q.school_province > 0    
             ");

        } elseif ($request['type'] == 3) {
            $sql = "select  (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as region_name,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}'
             order  by {$order_by} 
            limit {$pagestart},{$num}
             ";
            $allNum = $this->DataControl->selectOne("select  count(s.school_id) as all_num
             from  smc_school  as s
             where {$datawhere} and s.company_id='{$this->company_id}'
             ");


        } elseif ($request['type'] == 4) {
            $sql = "select  co.organize_cnname as region_name,sum(q.client_allnum) as client_allnum, sum(q.uneffective_num) as uneffective_num, sum(q.client_positivenum) as client_positivenum
            from (select  s.school_id ,s.school_city,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
            group by  co.organize_id 
             order  by {$order_by} 
            limit {$pagestart},{$num}
             ";

            $allNum = $this->DataControl->selectOne("select  count(DISTINCT co.organize_id ) as all_num 
            from (select  s.school_id ,s.school_city,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
             ");

        } elseif ($request['type'] == 5) {
            $sql = "select  co.organize_cnname as region_name,sum(q.client_allnum) as client_allnum, sum(q.uneffective_num) as uneffective_num, sum(q.client_positivenum) as client_positivenum
            from (select  s.school_id ,s.school_city,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
            group by  co.organize_id 
             order  by {$order_by} 
            limit {$pagestart},{$num}
             ";


            $allNum = $this->DataControl->selectOne("select  count(DISTINCT co.organize_id) as all_num
            from (select  s.school_id ,s.school_city,
            (select count(c.client_id) from  crm_client as  c,crm_client_schoolenter as t where c.client_id = t.client_id  and t.school_id = s.school_id  and {$client_where} ) as client_allnum,
            (select count(c.client_id) from  crm_client as c,crm_client_schoolenter as t  where c.client_id = t.client_id and t.school_id = s.school_id and  c.client_tracestatus <> '-2' and {$client_where} ) as uneffective_num,
            (select count(c.client_id) from  crm_client as  c ,crm_client_schoolenter as t where  c.client_id = t.client_id and t.school_id = s.school_id and c.client_tracestatus ='4'  and {$client_where} ) as client_positivenum
             from  smc_school  as s
            where {$datawhere} and s.company_id='{$this->company_id}') as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
             ");
        }

        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => &$dataOne) {
                $dataOne['number'] = $key + 1;
                $dataOne['rate'] = $dataOne['client_allnum'] > 0 ? round($dataOne['client_positivenum'] / $dataOne['client_allnum'], 4) * 100 . '%' : '0.00%';
            }
        } else {
            $dataList = array();
        }


        $result = array();
        $result['list'] = $dataList;
        $result['allnum'] = $allNum['all_num'] + 0;
        return $result;
    }

    /**
     * 招生转正数量排行
     * author: ling
     * 对应接口文档 0001
     */
    function schoolPositive($request)
    {
        $client_where = '1';
        $datawhere = '1';
        if (isset($request['starttime']) && $request['starttime'] != '') {
            $request['starttime'] = date('Y-m-d', strtotime($request['starttime']));
            $starttime = strtotime($request['starttime']);
            $client_where .= " and client_createtime >='{$starttime}' ";
        }
        if (isset($request['endtime']) && $request['endtime'] != '') {
            $request['endtime'] = date('Y-m-d', strtotime($request['endtime']));
            $endtime = strtotime($request['starttime']) + 3600 * 24 - 1;
            $client_where .= " and client_createtime <='{$endtime}' ";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== '') {
            $datawhere .= " and s.school_id in (select ol.school_id from  gmc_company_organizeschool as ol where ol.organize_id='{$request['organize_id']}') ";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id  ='{$request['school_id']}' ";
        }
        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and  s.school_province  ='{$request['school_province']}' ";
        }
        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and  s.school_city  ='{$request['school_city']}' ";
        }

        if (isset($request['order_by']) && $request['order_by'] !== 'DESC') {
            $order_by = "client_positive ASC";

        } else {
            $order_by = "client_positive DESC";
        }

        if (!$request['type']) {
            $request['type'] = 1;
        }
        if ($request['type'] == 1) {
            $sql = "select r.region_name, sum(q.client_positive) as client_positive
            from (select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_province
            (select  count(t.client_id) from  crm_client as t,crm_client_schoolenter as sr  where sr.school_id=s.school_id  and t.client_id =sr.client_id  and t.client_tracestatus =4  and {$client_where} ) as client_positive
            from  smc_school  as s 
            where s.company_id='{$this->company_id}' and {$datawhere}) as q
            left join smc_code_region as r On r.region_id = q.school_province
            where r.parent_id =1 and  q.school_province > 0    
            order by {$order_by}
            limit 0,10
             ";

        } elseif ($request['type'] == 2) {
            $sql = "select r.region_name, sum(q.client_positive) as client_positive
            from (select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_city
            (select  count(t.client_id) from  crm_client as t,crm_client_schoolenter as sr  where sr.school_id=s.school_id and t.client_id =sr.client_id  and t.client_tracestatus =4  and {$client_where} ) as client_positive
            from  smc_school  as s 
            where s.company_id='{$this->company_id}' and {$datawhere}) as q
            left join smc_code_region as r On r.region_id = q.school_city
            where r.parent_id =2 and  q.school_province > 0    
            order by {$order_by}
            limit 0,10
             ";
        } elseif ($request['type'] == 3) {
            $sql = "select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname ,
            (select count(t.client_id) from  crm_client as t,crm_client_schoolenter as sr  where sr.school_id=s.school_id and t.client_id =sr.client_id and t.client_tracestatus =4  and {$client_where} ) as client_positive
            from  smc_school  as s 
            where s.company_id='{$this->company_id}' and {$datawhere}
            order by {$order_by}
            limit 0,10
             ";


        } elseif ($request['type'] == 4) {
            $sql = "select co.organize_cnname, sum(q.client_positive) as client_positive
            from (select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_city
            (select  count(t.client_id) from  crm_client as t,crm_client_schoolenter as sr  where sr.school_id=s.school_id and t.client_id =sr.client_id  and t.client_tracestatus =4  and {$client_where} ) as client_positive
            from  smc_school  as s 
            where s.company_id='{$this->company_id}' and {$datawhere}) as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
            group by  co.organize_id 
             order  by {$order_by} 
            limit 0,10
             ";
        } elseif ($request['type'] == 5) {
            $sql = "select co.organize_cnname, sum(q.client_positive) as client_positive
            from (select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_city
            (select  count(t.client_id) from  crm_client as t,crm_client_schoolenter as s  where s.school_id=s.school_id and t.client_tracestatus =4  and {$client_where} ) as client_positive
            from  smc_school  as s 
            where s.company_id='{$this->company_id}' and {$datawhere}) as q
            left join gmc_company_organizeschool as ol On ol.school_id = q.school_id
            left join gmc_company_organize as  co On co.organize_id = ol.organize_id
            where co.father_id =0  and co.company_id='{$this->company_id}'
            group by  co.organize_id 
             order  by {$order_by} 
            limit 0,10
             ";
        }
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            if ($request['type'] == 1 || $request['type'] == 2) {
                $arr_school = array_column($dataList, 'region_name');
            } elseif ($request['type'] == 3) {
                $arr_school = array_column($dataList, 'school_cnname');
            } elseif ($request['type'] == 4 || $request['type'] == 5) {
                $arr_school = array_column($dataList, 'organize_cnname');
            }

            $arr_positive = array_column($dataList, 'client_positive');
        } else {
            $arr_school = array();
            $arr_positive = array();
        }

        $data = array();
        $data['x_data'] = $arr_school;
        $y_data = array();
        $y_data['name'] = '校区名称';
        $y_data['data'] = $arr_positive;
        $data['y_data'] = $y_data;
        return $data;
    }


}