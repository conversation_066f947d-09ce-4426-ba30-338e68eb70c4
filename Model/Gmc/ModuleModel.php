<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  ModuleModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();
    public $moduleOne = array();


    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getModuleList($request){

        $contractOne = $this->getContract($this->company_id);

        if (!$contractOne) {
            $this->getModuleListbak($request);
//            ajax_return(array('error' => 1, 'errortip' => "无可用合同!"),$this->companyOne['company_language']);
        }

        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$request['module_class']}'");

        if (!$promoduleList) {
            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"),$this->companyOne['company_language']);
        }

        $datawhere=" 1 ";
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isachieve", "company_id='{$this->company_id}'");
        if ($companyOne && $companyOne['company_isachieve'] == 0) {
            $datawhere .= " and m.module_id not in (634,635,636)";
        }

        $sql = "select m.module_id as id,m.module_name as label,m.module_level,m.module_markurl,m.module_icon,m.father_id
                ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->company_id}' and ed.module_id=m.module_id),1) as status
                from imc_module as m 
                where {$datawhere} and m.product_id='{$request['module_class']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
        $moduleList = $this->DataControl->selectClear($sql);


        if($request['status'] == '1'){
            $tree=$this->getModuleTree($moduleList,'id','module_markurl');
            $this->getArray($tree,'id','children');
        }else{
            $tree=$this->getModuleTree($moduleList,'module_id','url');
            $this->getArray($tree,'module_id','children');
        }



        $module_id=array();

        if(isset($request['url']) && $request['url']!=''){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$request['url']}'");

            if($request['re_postbe_id'] != 0){
                $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$request['re_postbe_id']}'");

                $sql="select u.module_id 
                  from gmc_staffer_usermodule as u 
                  where u.postrole_id ='{$postrole['postrole_id']}' and u.module_id ='{$module['module_id']}'
                  and u.module_id in (".implode(",",$this->moduleOne).")";

                $powerOne = $this->DataControl->selectOne($sql);
                if(!$powerOne){
                    ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
                }
            }

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];
        }

        $result = array();
        $result['children'] = $tree;

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);
    }

    function getModuleTree($moduleList,$field='id',$url='url'){
        $tree=$this->getTree($moduleList,$field,'father_id','children');

        foreach($tree as $k=>$v){
            if($v['father_id']!=0){
                unset($tree[$k]);
            }
        }

        if($tree){
            $mum=0;
            foreach($tree as $key=>$val){
                if (!isset($val['children'])) {
                    unset($tree[$key]);
                }else{
                    if(!$val[$url] || $val['module_level']==2){
                        $tree[$key][$url]=$val['children'][0][$url];
                    }
                    $tree[$key]['index']=$mum;

                    $m=0;
                    if($val['children']){
                        foreach($val['children'] as $k=>$v){
                            if(!$v[$url] || $v['module_level']==2){
                                if($v['children'][0][$url] && isset($v['children'][0][$url])){
                                    $tree[$key]['children'][$k][$url]=$v['children'][0][$url];
                                }
                            }
                            $tree[$key]['children'][$k]['activeIndex']=$mum.'-'.($m+1);
                            $m++;
                        }

                        $mum++;
                    }
                }
            }
        }

        return $tree;
    }

    function getModuleTreebak($moduleList,$moduleArray,$field='id',$url='url'){
        $tree=$this->getTree($moduleList,$field,'father_id','children');

        foreach($tree as $k=>$v){
            if($v['father_id']!=0){
                unset($tree[$k]);
            }
        }

        if($tree){
            $mum=0;
            foreach($tree as $key=>$val){
                if (!in_array($val[$field], $moduleArray) || !isset($val['children'])) {
                    unset($tree[$key]);
                }else{
                    if(!$val[$url] || $val['module_level']==2){
                        $tree[$key][$url]=$val['children'][0][$url];
                    }
                    $tree[$key]['index']=$mum;

                    $m=0;
                    if($val['children']){
                        foreach($val['children'] as $k=>$v){
                            if (!in_array($v[$field], $moduleArray)) {
                                unset($tree[$key]['children'][$k]);
                            }else{
                                if(!$v[$url] || $v['module_level']==2){
                                    if($v['children'][0][$url] && isset($v['children'][0][$url])){
                                        $tree[$key]['children'][$k][$url]=$v['children'][0][$url];
                                    }
                                }
                                $tree[$key]['children'][$k]['activeIndex']=$mum.'-'.($m+1);
                                $m++;
                            }
                        }

                        $mum++;
                    }
                }
            }
        }

        return $tree;
    }

    function getArray($arr,$field,$child){
        foreach ($arr as $v) {
            if ($v[$field]) {
                $this->moduleOne[]=$v[$field];
                if($v[$child]){
                    $this->getArray($v[$child],$field,$child);
                }
            }
        }
    }

    //机构管理 -- 首页
    function getModuleListbak($paramArray)
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module', 'module_name', "module_class = '{$paramArray['module_class']}'");
        $result['title'] = $module_name['module_name'];

        $major = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$paramArray['company_id']}'");
        if ($paramArray['company_id'] == '1001') {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
        } else {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_isshow = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
        }

       /* if($major['company_ismajor'] == '1'){
            if ($paramArray['company_id'] == '1001') {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' order by module_weight ASC");
            } else {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_isshow = '1' order by module_weight ASC");
            }
        }else{
            if ($paramArray['company_id'] == '1001') {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_ismajor = '0' order by module_weight ASC");
            } else {
                $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '{$paramArray['module_class']}' and module_isshow = '1' and module_ismajor = '0' order by module_weight ASC");
            }
        }*/

        if($paramArray['url']){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$paramArray['url']}'");
            $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$paramArray['re_postbe_id']}'");
            $a = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postrole['postrole_id']}' and module_id = '{$module['module_id']}'");

            if(!$a && $paramArray['re_postbe_id'] != '0'){
                ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
            }

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];

            $data = $this->treesss($data);

            $result['children'] = $data;
        }else{
            $data = $this->tree($data);

            $result['children'] = $data;
        }

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);

    }

    function tree($items)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['label'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    $son[$k]['module_icon'] = $v['module_icon'];
                    if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                        $son[$k]['disabled'] = true;
                    } else {
                        $son[$k]['disabled'] = false;
                    }
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $url = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
//                            $url = $this->DataControl->getFieldOne("imc_module","module_markurl","father_id = '{$value['module_id']}'");

                            $son[$k]['children'][$key]['label'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                                $son[$k]['children'][$key]['disabled'] = true;
                            } else {
                                $son[$k]['children'][$key]['disabled'] = false;
                            }
                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['module_icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['label'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_icon'] = $values['module_icon'];
                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }




    function treesss($items)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;
                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['module_id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    $son[$k]['icon'] = $v['module_icon'];
                    if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                        $son[$k]['disabled'] = true;
                    } else {
                        $son[$k]['disabled'] = false;
                    }
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $url = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
//                            $url = $this->DataControl->getFieldOne("imc_module","module_markurl","father_id = '{$value['module_id']}'");

                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['module_id'] = $value['module_id'];
                            if ($v['module_id'] == '191' || $v['module_id'] == '192' || $v['module_id'] == '52' || $v['module_id'] == '89') {
                                $son[$k]['children'][$key]['disabled'] = true;
                            } else {
                                $son[$k]['children'][$key]['disabled'] = false;
                            }
                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }



    function getPowerList($request){

        $contractOne = $this->getContract($this->company_id);

        if (!$contractOne) {
            $this->getPowerListbak($request);
//            ajax_return(array('error' => 1, 'errortip' => "无可用合同!"),$this->companyOne['company_language']);
        }

        $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id='{$request['module_class']}'");

        if (!$promoduleList) {
            ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"),$this->companyOne['company_language']);
        }
        $datawhere=" 1 ";
        $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isachieve", "company_id='{$this->company_id}'");
        if ($companyOne && $companyOne['company_isachieve'] == 0) {
            $datawhere .= " and m.module_id not in (634,635,636)";
        }

        $postbeOne = $this->DataControl->getFieldOne('gmc_staffer_postbe','school_id,postrole_id,postpart_id',"postbe_id = '{$request['re_postbe_id']}'");

        if($request['re_postbe_id'] != 0){
            $sql = "select m.module_id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id
                ,ifnull((select 1 from gmc_staffer_commonmodule as co where co.module_id=m.module_id and co.staffer_id='{$request['staffer_id']}'),0) as isselect
                ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->company_id}' and ed.module_id=m.module_id),1) as status
                from imc_module as m 
                inner join gmc_staffer_usermodule as u on u.module_id=m.module_id
                where {$datawhere} and m.product_id='{$request['module_class']}' and u.postrole_id = '{$postbeOne['postrole_id']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
        }else{
            $sql = "select m.module_id,m.module_name as title,m.module_level,m.module_markurl as url,m.module_img,m.module_icon as icon,m.father_id
                ,ifnull((select 1 from gmc_staffer_commonmodule as co where co.module_id=m.module_id and co.staffer_id='{$request['staffer_id']}'),0) as isselect
                ,ifnull((select ed.module_status from gmc_editionpro_module as ed where ed.company_id='{$this->company_id}' and ed.module_id=m.module_id),1) as status
                from imc_module as m 
                where {$datawhere} and m.product_id='{$request['module_class']}'
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                having status=1
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
        }


        $moduleList = $this->DataControl->selectClear($sql);

        $tree=$this->getModuleTree($moduleList,'module_id','url');
        $this->getArray($tree,'module_id','children');

        $module_id=array();

        if(isset($request['url']) && $request['url']!=''){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id,module_markurl","module_markurl = '{$request['url']}' and module_class = '{$request['module_class']}'");

            if($request['re_postbe_id'] != 0){

                $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$request['re_postbe_id']}'");
                $sql="select u.module_id 
                    from gmc_staffer_usermodule as u 
                    where u.postrole_id ='{$postrole['postrole_id']}' and u.module_id ='{$module['module_id']}'
                    and u.module_id in (".implode(",",$this->moduleOne).")";
                $powerOne = $this->DataControl->selectOne($sql);
                if($request['status'] == '1'){
                    $fid = $this->DataControl->getFieldOne("imc_module","father_id","module_id ='{$module['module_id']}'");
                    $mid = $this->DataControl->selectClear("select m.module_id from imc_module as m where m.father_id = '{$fid['father_id']}'");
                    $array = array_column($mid,'module_id');
                    $implode = implode(",",$array);

                    $module = $this->DataControl->selectOne("select u.module_id,i.module_markurl from gmc_staffer_usermodule as u left join imc_module as i on i.module_id = u.module_id where u.module_id in ($implode) and u.postrole_id ='{$postrole['postrole_id']}'");
                    $module_id = array();
                    $module_id['module_id'] = $module['module_id'];
                    $module_id['url'] = $module['module_markurl'];
                    $module_id['father_id'] = $fid['father_id'];
                }else{
                    if(!$powerOne){
                        ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
                    }
                    $module_id = array();
                    $module_id['module_id'] = $module['module_id'];
                    $module_id['father_id'] = $module['father_id'];
                    $module_id['url'] = $module['module_markurl'];
                }


            }else{
                $module_id = array();
                $module_id['module_id'] = $module['module_id'];
                $module_id['father_id'] = $module['father_id'];
                $module_id['url'] = $module['module_markurl'];
            }


        }

        $result = array();
        $result['children'] = $tree;

        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);

    }

    function getPowerListbak($paramArray)
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module', 'module_name', "module_class = '{$paramArray['module_class']}'");
        $result['title'] = $module_name['module_name'];

        $major = $this->DataControl->getFieldOne("gmc_company","company_ismajor","company_id = '{$paramArray['company_id']}'");

        if($paramArray['re_postbe_id'] == '0'){
            if($paramArray['company_id'] == '1001'){
                $data = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_img from imc_module where module_class = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
            }else{
                $data = $this-> DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id,module_img from imc_module where module_class = '1' and module_isshow = '1' and module_ismajor <= '{$major['company_ismajor']}' order by module_weight ASC");
            }
        }else{
            if ($paramArray['company_id'] == '1001') {
                $data = $this->DataControl->selectClear("
            SELECT u.module_id, u.postrole_id, m.father_id, m.module_markurl, m.module_icon, m.module_name, m.module_img
            FROM gmc_staffer_usermodule AS u LEFT JOIN imc_module AS m ON u.module_id = m.module_id LEFT JOIN gmc_staffer_postbe AS p ON p.postrole_id = u.postrole_id
            WHERE p.postbe_id = '{$paramArray['postbe_id']}' and m.module_ismajor <= '{$major['company_ismajor']}' and m.module_class = '{$paramArray['module_class']}'
            order by module_weight ASC");
            } else {
                $data = $this->DataControl->selectClear("
            SELECT u.module_id, u.postrole_id, m.father_id, m.module_markurl, m.module_icon, m.module_name, m.module_img
            FROM gmc_staffer_usermodule AS u  LEFT JOIN imc_module AS m ON u.module_id = m.module_id LEFT JOIN gmc_staffer_postbe AS p ON p.postrole_id = u.postrole_id
            WHERE p.postbe_id = '{$paramArray['postbe_id']}' and m.module_ismajor <= '{$major['company_ismajor']}' and m.module_class = '{$paramArray['module_class']}' and m.module_isshow = '1'
            order by module_weight ASC");
            }
        }

        $data = $this->powerTree($data, $paramArray);

        $result['children'] = $data;

        if($paramArray['url']){
            $module = $this->DataControl->getFieldOne("imc_module","module_id,father_id","module_markurl = '{$paramArray['url']}' and module_class = '1'");

            $postrole = $this->DataControl->getFieldOne("gmc_staffer_postbe","postrole_id","postbe_id = '{$paramArray['re_postbe_id']}'");

            $a = $this->DataControl->getFieldOne("gmc_staffer_usermodule","module_id","postrole_id = '{$postrole['postrole_id']}' and module_id = '{$module['module_id']}'");

            if(!$a && $paramArray['re_postbe_id'] != '0'){
                ajax_return(array('error' => 1, 'errortip' => "您没有权限!"),$this->companyOne['company_language']);
            }

            $module_id = array();
            $module_id['module_id'] = $module['module_id'];
            $module_id['father_id'] = $module['father_id'];
        }
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $result, 'module' =>$module_id),$this->companyOne['company_language'],1);
    }

    function powerTree($items, $paramArray)
    {
        $son = array();
        $count = -1;
        $counts = -1;
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['father_id'] == 0) {
                    $counts++;

                    $a = $this->DataControl->getFieldOne("gmc_staffer_commonmodule","module_id","staffer_id = '{$paramArray['staffer_id']}' and module_id = '{$v['module_id']}'");
                    if($a){
                        $son[$k]['isselect'] = '1';
                    }else{
                        $son[$k]['isselect'] = '0';
                    }

                    $son[$k]['title'] = $v['module_name'];
                    $son[$k]['index'] = $counts;
                    $son[$k]['module_id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    $son[$k]['postrole_id'] = $v['postrole_id'];
                    $son[$k]['icon'] = $v['module_icon'];
                    $son[$k]['module_img'] = $v['module_img'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $count++;
                            $a = $this->DataControl->getFieldOne("gmc_staffer_commonmodule","module_id","staffer_id = '{$paramArray['staffer_id']}' and module_id = '{$value['module_id']}'");
                            if($a){
                                $son[$k]['children'][$key]['isselect'] = '1';
                            }else{
                                $son[$k]['children'][$key]['isselect'] = '0';
                            }

                            $postrole_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postrole_id", "postbe_id = '{$paramArray['postbe_id']}'");
                            $url = $this->DataControl->selectClear("SELECT
                                    m.module_markurl
                                FROM
                                    imc_module AS m
                                    LEFT JOIN gmc_staffer_usermodule AS u ON m.module_id = u.module_id 
                                WHERE
                                    m.father_id = '{$value['module_id']}' and u.postrole_id = '{$postrole_id['postrole_id']}'
                                GROUP BY
                                    m.module_id");
                            $son[$k]['children'][$key]['title'] = $value['module_name'];
                            $son[$k]['children'][$key]['index'] = $count;
                            $son[$k]['children'][$key]['activeIndex'] = $counts . '-' . ($count + 1);
                            $son[$k]['children'][$key]['module_id'] = $value['module_id'];
                            $son[$k]['children'][$key]['postrole_id'] = $value['postrole_id'];
                            $son[$k]['children'][$key]['module_img'] = $value['module_img'];
                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            $son[$k]['children'][$key]['icon'] = $value['module_icon'];
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $a = $this->DataControl->getFieldOne("gmc_staffer_commonmodule","module_id","staffer_id = '{$paramArray['staffer_id']}' and module_id = '{$values['module_id']}'");
                                    if($a){
                                        $son[$k]['children'][$key]['children'][$keys]['isselect'] = '1';
                                    }else{
                                        $son[$k]['children'][$key]['children'][$keys]['isselect'] = '0';
                                    }

                                    $son[$k]['children'][$key]['children'][$keys]['title'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['postrole_id'] = $values['postrole_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    $son[$k]['children'][$key]['children'][$keys]['icon'] = $values['module_icon'];
                                    $son[$k]['children'][$key]['children'][$keys]['module_img'] = $values['module_img'];

                                }
                            }
                        }
                    }
                    $count = -1;
                }
            }
        }
        return $son;
    }

    //添加权限
    function addPowerAction($paramArray)
    {
        $PowerList = json_decode(stripslashes($paramArray['power']), true);

        $this->DataControl->delData('gmc_staffer_usermodule', "postrole_id = '{$paramArray['postrole_id']}'");

        foreach ($PowerList as $item) {
            $data = array();
            $data['module_id'] = $item['module_id'];
            $data['postrole_id'] = $item['postrole_id'];
            $data['company_id'] = $paramArray['company_id'];

            $b = $this->DataControl->getFieldOne('imc_module', 'father_id', "module_id = '{$item['module_id']}'");

            if ($b['father_id'] !== '0') {
                $c = $this->DataControl->getFieldOne('gmc_staffer_usermodule', 'module_id', "module_id = '{$b['father_id']}' and postrole_id = '{$item['postrole_id']}'");

                if (!$c) {
                    $datas = array();
                    $datas['module_id'] = $b['father_id'];
                    $datas['postrole_id'] = $item['postrole_id'];
                    $datas['company_id'] = $paramArray['company_id'];

                    $this->DataControl->insertData('gmc_staffer_usermodule', $datas);
                }

                $d = $this->DataControl->getFieldOne('imc_module', 'father_id', "module_id = '{$b['father_id']}'");

                if ($d['father_id'] !== '0') {
                    $e = $this->DataControl->getFieldOne('gmc_staffer_usermodule', 'module_id', "module_id = '{$d['father_id']}' and postrole_id = '{$item['postrole_id']}'");

                    if (!$e) {
                        $datas = array();
                        $datas['module_id'] = $d['father_id'];
                        $datas['postrole_id'] = $item['postrole_id'];
                        $datas['company_id'] = $paramArray['company_id'];

                        $this->DataControl->insertData('gmc_staffer_usermodule', $datas);
                    }
                }
            }

            $this->DataControl->insertData('gmc_staffer_usermodule', $data);
        }

        $res = array('error' => '0', 'errortip' => "权限设置成功", 'result' => array());

        return $res;
    }

    function getPostrolePowerApi($paramArray)
    {
        $a = $this->DataControl->selectClear("select father_id from imc_module as m WHERE m.module_class = 1 and module_level = 3 GROUP BY father_id");

        foreach ($a as $v) {
            $v = join(",", $v); //可以用implode将一维数组转换为用逗号连接的字符串，join是别名
            $temp[] = $v;
        }
        foreach ($temp as $v) {
            $t .= $v . ",";
        }

        $t = substr($t, 0, -1); //利用字符串截取函数消除最后一个逗号

        $sql = "
            SELECT
                u.module_id as id,
                m.module_name as label
            FROM
                gmc_staffer_usermodule AS u
                LEFT JOIN imc_module AS m ON m.module_id = u.module_id 
            WHERE
                u.postrole_id = '{$paramArray['postrole_id']}' and m.father_id > '0' and m.module_id not in ({$t})";
        $postroleDetail = $this->DataControl->selectClear($sql);

        if ($postroleDetail) {
            foreach ($postroleDetail as &$val) {
                if ($val['id'] == '191' || $val['id'] == '282') {
                    $val['disabled'] = true;
                } else {
                    $val['disabled'] = false;
                }
            }
        }

        $field = array();
        $field["module_id"] = "模块id";
        $field["module_name"] = "模块名称";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '权限查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '权限查看失败', 'result' => $result);
        }
        return $res;
    }

    function getPostpartPowerApi($paramArray)
    {
        $sql = "
            SELECT
                u.module_id as id,
                m.module_name as label
            FROM
                smc_staffer_usermodule AS u
                LEFT JOIN imc_module AS m ON m.module_id = u.module_id 
            WHERE
                u.postpart_id = '{$paramArray['postpart_id']}' and m.father_id > '0' and m.module_id not in (select i.father_id from imc_module as i)";
        $postroleDetail = $this->DataControl->selectClear($sql);

        if ($postroleDetail) {
            foreach ($postroleDetail as &$val) {
                if ($val['id'] == '52' || $val['id'] == '89') {
                    $val['disabled'] = true;
                } else {
                    $val['disabled'] = false;
                }
            }
        }

        $field = array();
        $field["module_id"] = "模块id";
        $field["module_name"] = "模块名称";
        $result = array();
        if ($postroleDetail) {
            $result["field"] = $field;
            $result["data"] = $postroleDetail;
            $res = array('error' => '0', 'errortip' => '权限查看成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '权限查看失败', 'result' => $result);
        }
        return $res;
    }


    //添加权限
    function addScPowerAction($paramArray)
    {
        $data = array();

        $PowerList = json_decode(stripslashes($paramArray['power']), true);

        $this->DataControl->delData('smc_staffer_usermodule', "postpart_id = '{$paramArray['postpart_id']}'");
        foreach ($PowerList as $item) {

            $data['module_id'] = $item['module_id'];
            $data['postpart_id'] = $item['postpart_id'];
            $data['company_id'] = $paramArray['company_id'];

            $b = $this->DataControl->getFieldOne('imc_module', 'father_id', "module_id = '{$item['module_id']}'");

            if ($b['father_id'] !== '0') {
                $c = $this->DataControl->getFieldOne('smc_staffer_usermodule', 'module_id', "module_id = '{$b['father_id']}' and postpart_id = '{$item['postpart_id']}'");

                if (!$c) {
                    $datas = array();
                    $datas['module_id'] = $b['father_id'];
                    $datas['postpart_id'] = $item['postpart_id'];
                    $datas['company_id'] = $paramArray['company_id'];

                    $this->DataControl->insertData('smc_staffer_usermodule', $datas);
                }

                $d = $this->DataControl->getFieldOne('imc_module', 'father_id', "module_id = '{$b['father_id']}'");

                if ($d['father_id'] !== '0') {
                    $e = $this->DataControl->getFieldOne('smc_staffer_usermodule', 'module_id', "module_id = '{$d['father_id']}' and postpart_id = '{$item['postpart_id']}'");

                    if (!$e) {
                        $datas = array();
                        $datas['module_id'] = $d['father_id'];
                        $datas['postpart_id'] = $item['postpart_id'];
                        $datas['company_id'] = $paramArray['company_id'];

                        $this->DataControl->insertData('smc_staffer_usermodule', $datas);
                    }
                }
            }

            $this->DataControl->insertData('smc_staffer_usermodule', $data);
        }

        $res = array('error' => '0', 'errortip' => "权限设置成功", 'result' => array());

        return $res;
    }

    //设置
    function getSetModuleList($paramArray)
    {
        $result = array();
        $module_name = $this->DataControl->getFieldOne('imc_module', 'module_name', "module_class = '1'");
        $result['title'] = $module_name['module_name'];
        if ($paramArray['company_id'] == '1001') {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '1' and module_id not in (156,157,159,160,261,110,161,162,163) order by module_weight ASC");
        } else {
            $data = $this->DataControl->selectClear("select module_id,module_name,module_markurl,module_icon,father_id from imc_module where module_class = '1' and module_id not in (156,157,159,160,261,110,161,162,163) and module_isshow = '1' order by module_weight ASC");
        }
        $company_id = $paramArray['company_id'];

        $data = $this->trees($data, $company_id);


        $result['children'] = $data;
        ajax_return(array('error' => 0, 'errortip' => "获取成功!", 'result' => $data),$this->companyOne['company_language']);
    }

    function trees($items, $company_id)
    {
        $son = array();
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['module_id'] == 94) {
                    $son[$k]['label'] = $v['module_name'];
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $url = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
                            $son[$k]['children'][$key]['label'] = $value['module_name'];
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($value['module_id'] == '44') {
                                $v1 = $this->DataControl->getFieldOne("gmc_company", "company_id", "company_id = '{$company_id}'");
                                if ($v1) {
                                    $son[$k]['children'][$key]['status'] = 0;
                                } else {
                                    $son[$k]['children'][$key]['status'] = 1;
                                }
                            } elseif ($value['module_id'] == '45') {
                                $v2 = $this->DataControl->getFieldOne("smc_code_coursetype", "company_id", "company_id = '{$company_id}'");
                                $v3 = $this->DataControl->getFieldOne("smc_code_coursecat", "company_id", "company_id = '{$company_id}'");
                                $v4 = $this->DataControl->getFieldOne("smc_course", "company_id", "company_id = '{$company_id}'");
                                $v5 = $this->DataControl->getFieldOne("smc_code_feeitem", "company_id", "company_id = '{$company_id}'");
                                $v6 = $this->DataControl->getFieldOne("smc_code_coursetimes", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v2 ? 0 : 1) + ($v3 ? 0 : 1) + ($v4 ? 0 : 1) + ($v5 ? 0 : 1) + ($v6 ? 0 : 1);
                            } elseif ($value['module_id'] == '46') {
                                $v7 = $this->DataControl->getFieldOne("smc_code_stuchange_reason", "company_id", "company_id = '{$company_id}'");
                                $v8 = $this->DataControl->getFieldOne("smc_code_parentscareer", "company_id", "company_id = '{$company_id}'");
                                $v9 = $this->DataControl->getFieldOne("smc_code_holidays", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v7 ? 0 : 1) + ($v8 ? 0 : 1) + ($v9 ? 0 : 1);
                            } elseif ($value['module_id'] == '47') {
                                $v10 = $this->DataControl->getFieldOne("gmc_company_post", "company_id", "company_id = '{$company_id}' and post_type = '0'");
                                $v11 = $this->DataControl->getFieldOne("gmc_company_post", "company_id", "company_id = '{$company_id}' and post_type = '1'");
                                $v12 = $this->DataControl->getFieldOne("gmc_company_postlevel", "company_id", "company_id = '{$company_id}'");
                                $v13 = $this->DataControl->getFieldOne("gmc_company_postrole", "company_id", "company_id = '{$company_id}'");
                                $v14 = $this->DataControl->getFieldOne("smc_school_postpart", "company_id", "company_id = '{$company_id}'");
                                $v15 = $this->DataControl->getFieldOne("smc_code_teachtype", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v10 ? 0 : 1) + ($v11 ? 0 : 1) + ($v12 ? 0 : 1) + ($v13 ? 0 : 1) + ($v14 ? 0 : 1) + ($v15 ? 0 : 1);
                            } elseif ($value['module_id'] == '48') {
                                $v16 = $this->DataControl->getFieldOne("crm_code_commode", "company_id", "company_id = '{$company_id}'");
                                $v17 = $this->DataControl->getFieldOne("crm_code_tracenote", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v16 ? 0 : 1) + ($v17 ? 0 : 1);
                            } elseif ($value['module_id'] == '49') {
                                $v18 = $this->DataControl->getFieldOne("smc_code_prodtype", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v18 ? 0 : 1);
                            } elseif ($value['module_id'] == '50') {
                                $v19 = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v19 ? 0 : 1);
                            } elseif ($value['module_id'] == '51') {
                                $v20 = $this->DataControl->getFieldOne("smc_code_noticetype", "company_id", "company_id = '{$company_id}'");
                                $v21 = $this->DataControl->getFieldOne("gmc_code_companies", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v20 ? 0 : 1) + ($v21 ? 0 : 1);
                            }

                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['label'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    if ($values['module_id'] == '141') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v2 ? 1 : 0;
                                    } elseif ($values['module_id'] == '142') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v3 ? 1 : 0;
                                    } elseif ($values['module_id'] == '143') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v4 ? 1 : 0;
                                    } elseif ($values['module_id'] == '145') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v5 ? 1 : 0;
                                    } elseif ($values['module_id'] == '146') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v6 ? 1 : 0;
                                    } elseif ($values['module_id'] == '115') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v7 ? 1 : 0;
                                    } elseif ($values['module_id'] == '116') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v8 ? 1 : 0;
                                    } elseif ($values['module_id'] == '117') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v9 ? 1 : 0;
                                    } elseif ($values['module_id'] == '151') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v10 ? 1 : 0;
                                    } elseif ($values['module_id'] == '152') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v11 ? 1 : 0;
                                    } elseif ($values['module_id'] == '153') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v12 ? 1 : 0;
                                    } elseif ($values['module_id'] == '154') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v13 ? 1 : 0;
                                    } elseif ($values['module_id'] == '155') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v14 ? 1 : 0;
                                    } elseif ($values['module_id'] == '158') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v15 ? 1 : 0;
                                    } elseif ($values['module_id'] == '131') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v16 ? 1 : 0;
                                    } elseif ($values['module_id'] == '132') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v17 ? 1 : 0;
                                    } elseif ($values['module_id'] == '136') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v18 ? 1 : 0;
                                    } elseif ($values['module_id'] == '140') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v19 ? 1 : 0;
                                    } elseif ($values['module_id'] == '109') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v20 ? 1 : 0;
                                    } elseif ($values['module_id'] == '113') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v21 ? 1 : 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return $son;
    }

    function treess($items, $company_id)
    {
        $son = array();
        if (is_array($items)) {
            foreach ($items as $k => &$v) {
                if ($v['module_id'] == 94) {
                    $son[$k]['label'] = $v['module_name'];
                    $son[$k]['id'] = $v['module_id'];
                    $son[$k]['url'] = $v['module_markurl'];
                    foreach ($items as $key => $value) {
                        if ($v['module_id'] == $value['father_id']) {
                            $url = $this->DataControl->selectClear("select module_markurl from imc_module WHERE father_id = '{$value['module_id']}' limit 0,1");
                            $son[$k]['children'][$key]['label'] = $value['module_name'];
                            $son[$k]['children'][$key]['id'] = $value['module_id'];
                            if ($value['module_id'] == '44') {
                                $v1 = $this->DataControl->getFieldOne("gmc_company", "company_id", "company_id = '{$company_id}'");
                                if ($v1) {
                                    $son[$k]['children'][$key]['status'] = 0;
                                } else {
                                    $son[$k]['children'][$key]['status'] = 1;
                                }
                            } elseif ($value['module_id'] == '45') {
                                $v2 = $this->DataControl->getFieldOne("smc_code_coursetype", "company_id", "company_id = '{$company_id}'");
                                $v3 = $this->DataControl->getFieldOne("smc_code_coursecat", "company_id", "company_id = '{$company_id}'");
                                $v4 = $this->DataControl->getFieldOne("smc_course", "company_id", "company_id = '{$company_id}'");
                                $v5 = $this->DataControl->getFieldOne("smc_code_feeitem", "company_id", "company_id = '{$company_id}'");
                                $v6 = $this->DataControl->getFieldOne("smc_code_coursetimes", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v2 ? 0 : 1) + ($v3 ? 0 : 1) + ($v4 ? 0 : 1) + ($v5 ? 0 : 1) + ($v6 ? 0 : 1);
                            } elseif ($value['module_id'] == '46') {
                                $v7 = $this->DataControl->getFieldOne("smc_code_stuchange_reason", "company_id", "company_id = '{$company_id}'");
                                $v8 = $this->DataControl->getFieldOne("smc_code_parentscareer", "company_id", "company_id = '{$company_id}'");
                                $v9 = $this->DataControl->getFieldOne("smc_code_holidays", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v7 ? 0 : 1) + ($v8 ? 0 : 1) + ($v9 ? 0 : 1);
                            } elseif ($value['module_id'] == '47') {
                                $v10 = $this->DataControl->getFieldOne("gmc_company_post", "company_id", "company_id = '{$company_id}' and post_type = '0'");
                                $v11 = $this->DataControl->getFieldOne("gmc_company_post", "company_id", "company_id = '{$company_id}' and post_type = '1'");
                                $v12 = $this->DataControl->getFieldOne("gmc_company_postlevel", "company_id", "company_id = '{$company_id}'");
                                $v13 = $this->DataControl->getFieldOne("gmc_company_postrole", "company_id", "company_id = '{$company_id}'");
                                $v14 = $this->DataControl->getFieldOne("smc_school_postpart", "company_id", "company_id = '{$company_id}'");
                                $v15 = $this->DataControl->getFieldOne("smc_code_teachtype", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v10 ? 0 : 1) + ($v11 ? 0 : 1) + ($v12 ? 0 : 1) + ($v13 ? 0 : 1) + ($v14 ? 0 : 1) + ($v15 ? 0 : 1);
                            } elseif ($value['module_id'] == '48') {
                                $v16 = $this->DataControl->getFieldOne("crm_code_commode", "company_id", "company_id = '{$company_id}'");
                                $v17 = $this->DataControl->getFieldOne("crm_code_tracenote", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v16 ? 0 : 1) + ($v17 ? 0 : 1);
                            } elseif ($value['module_id'] == '49') {
                                $v18 = $this->DataControl->getFieldOne("smc_code_prodtype", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v18 ? 0 : 1);
                            } elseif ($value['module_id'] == '50') {
                                $v19 = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v19 ? 0 : 1);
                            } elseif ($value['module_id'] == '51') {
                                $v20 = $this->DataControl->getFieldOne("smc_code_noticetype", "company_id", "company_id = '{$company_id}'");
                                $v21 = $this->DataControl->getFieldOne("gmc_code_companies", "company_id", "company_id = '{$company_id}'");
                                $son[$k]['children'][$key]['status'] = ($v20 ? 0 : 1) + ($v21 ? 0 : 1);
                            }

                            if ($url) {
                                $son[$k]['children'][$key]['url'] = $url[0]['module_markurl'];
                            } else {
                                $son[$k]['children'][$key]['url'] = $value['module_markurl'];
                            }
                            foreach ($items as $keys => $values) {
                                if ($value['module_id'] == $values['father_id']) {
                                    $son[$k]['children'][$key]['children'][$keys]['label'] = $values['module_name'];
                                    $son[$k]['children'][$key]['children'][$keys]['id'] = $values['module_id'];
                                    $son[$k]['children'][$key]['children'][$keys]['url'] = $values['module_markurl'];
                                    if ($values['module_id'] == '141') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v2 ? 1 : 0;
                                    } elseif ($values['module_id'] == '142') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v3 ? 1 : 0;
                                    } elseif ($values['module_id'] == '143') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v4 ? 1 : 0;
                                    } elseif ($values['module_id'] == '145') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v5 ? 1 : 0;
                                    } elseif ($values['module_id'] == '146') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v6 ? 1 : 0;
                                    } elseif ($values['module_id'] == '115') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v7 ? 1 : 0;
                                    } elseif ($values['module_id'] == '116') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v8 ? 1 : 0;
                                    } elseif ($values['module_id'] == '117') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v9 ? 1 : 0;
                                    } elseif ($values['module_id'] == '151') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v10 ? 1 : 0;
                                    } elseif ($values['module_id'] == '152') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v11 ? 1 : 0;
                                    } elseif ($values['module_id'] == '153') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v12 ? 1 : 0;
                                    } elseif ($values['module_id'] == '154') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v13 ? 1 : 0;
                                    } elseif ($values['module_id'] == '155') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v14 ? 1 : 0;
                                    } elseif ($values['module_id'] == '158') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v15 ? 1 : 0;
                                    } elseif ($values['module_id'] == '131') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v16 ? 1 : 0;
                                    } elseif ($values['module_id'] == '132') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v17 ? 1 : 0;
                                    } elseif ($values['module_id'] == '136') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v18 ? 1 : 0;
                                    } elseif ($values['module_id'] == '140') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v19 ? 1 : 0;
                                    } elseif ($values['module_id'] == '109') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v20 ? 1 : 0;
                                    } elseif ($values['module_id'] == '113') {
                                        $son[$k]['children'][$key]['children'][$keys]['status'] = $v21 ? 1 : 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return $son;
    }

    //模块对应的 文档
    function getModuleHandbookApi($paramArray){
        $datawhere = "1";
//        $paramArray['module_id'] = '1';
        //模块 id
        if(isset($paramArray['module_id']) && $paramArray['module_id'] != ''){
            $datawhere .= " and h.module_id = '{$paramArray['module_id']}'";
        }
        if(isset($paramArray['module_type']) && $paramArray['module_type'] == '0'){//文本
            $sqlfields = "H.handbook_id,H.module_id,H.handbook_name,H.handbook_note";
        }elseif(isset($paramArray['module_type']) && $paramArray['module_type'] == '1'){//视频
            $sqlfields = "H.handbook_id,H.module_id,H.handbook_name,H.handbook_videourl ";
        }
        $sql="SELECT {$sqlfields} FROM imc_module_handbook as h WHERE {$datawhere} ORDER BY h.handbook_weight DESC ,h.handbook_id DESC ";
        $dataList = $this->DataControl->selectClear($sql);
        return $dataList;
    }


    //添加员工常用功能
    function addCommonmoduleAction($paramArray){
        $data = array();
        $this->DataControl->delData("gmc_staffer_commonmodule","staffer_id = '{$paramArray['staffer_id']}'");

        $schoolList = json_decode(stripslashes($paramArray['list']),true);
        foreach ($schoolList as $item) {
            $data['staffer_id'] = $item['staffer_id'];
            $data['module_id'] = $item['module_id'];

            $a = $this->DataControl->getFieldOne('gmc_staffer_commonmodule','staffer_id',"staffer_id = '{$item['staffer_id']}' and module_id = '{$item['module_id']}'");
            if ($a){
                ajax_return(array('error' => 1,'errortip' => "请勿重复添加"),$this->companyOne['company_language']);
            }
            $this->DataControl->insertData('gmc_staffer_commonmodule',$data);
        }

        $res = array('error' => '0', 'errortip' => "修改员工常用功能成功");

        return $res;
    }

    //查看员工常用功能
    function getCommonmodule($paramArray){

        $contractOne = $this->getContract($this->company_id);

        $datawhere=" 1 ";
        if ($contractOne) {
            $promoduleList = $this->DataControl->getList("imc_editpromodule", "edition_id='{$contractOne['edition_id']}' and product_id=1");

            if (!$promoduleList) {
                ajax_return(array('error' => 1, 'errortip' => "请先设置产品模块!"),$this->companyOne['company_language']);
            }
            $datawhere=" 1 ";
            $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_isachieve", "company_id='{$this->company_id}'");
            if ($companyOne && $companyOne['company_isachieve'] == 0) {
                $datawhere .= " and m.module_id not in (634,635,636)";
            }

            $sql = "select m.module_id as id,m.module_name as label,m.module_level,m.module_markurl,m.module_icon,m.father_id
                from imc_module as m 
                where {$datawhere} and m.product_id=1
                and exists(select 1 from imc_editpromodule as ie where ie.module_id=m.module_id and ie.product_id=m.product_id and ie.edition_id='{$contractOne['edition_id']}')
                order by m.module_class asc,m.module_weight asc,m.module_id asc";
            $moduleList = $this->DataControl->selectClear($sql);

            $tree=$this->getModuleTree($moduleList,'id','module_markurl');
            $this->getArray($tree,'id','children');
            $datawhere.=" and s.module_id in (".implode(",",$this->moduleOne).")";
        }

        $sql="
            SELECT
                s.staffer_id,
                s.module_id,	
                m.module_name as title,
	            m.module_img,
	            m.module_markurl
            FROM
                gmc_staffer_commonmodule AS s left join imc_module as m on s.module_id = m.module_id
            WHERE
                {$datawhere} and s.staffer_id = '{$paramArray['staffer_id']}' ";
        $stafferDetail = $this->DataControl->selectClear($sql);

        $field = array();
        $field["staffer_id"] = "staffer_id";
        $field["module_id"] = "module_id";

        $result = array();
        if($stafferDetail){
            $result["field"] = $field;
            $result["data"] = $stafferDetail;
            $res = array('error' => '0', 'errortip' => '查看员工常用功能成功', 'result' => $result);
        }else{
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '查看员工常用功能失败', 'result' => $result);
        }
        return $res;
    }

}
