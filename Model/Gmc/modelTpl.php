<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn 
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;


class modelTpl extends \Model\modelTpl{

    public function __construct(){
        parent::__construct();
    }

    function ChangeTime($time){
        $time = time() - $time;
        if(is_numeric($time)){
            $value = array(
                "years" => 0, "days" => 0, "hours" => 0,
                "minutes" => 0, "seconds" => 0,
            );
            if($time >= 31556926){
                $value["years"] = floor($time/31556926);
                $time = ($time%31556926);
                $t = $value["years"].'年前';
            }
            elseif(31556926 >$time && $time >= 86400){
                $value["days"] = floor($time/86400);
                $time = ($time%86400);
                $t = $value["days"].'天前';
            }
            elseif(86400 > $time && $time >= 3600){
                $value["hours"] = floor($time/3600);
                $time = ($time%3600);
                $t = $value["hours"].'小时前';
            }
            elseif(3600 > $time && $time >= 60){
                $value["minutes"] = floor($time/60);
                $time = ($time%60);
                $t = $value["minutes"].'分钟前';
            }else{
                $t = $time.'秒前';
            }
            return $t;
        }else{
            return date('Y-m-d H:i:s',time());
        }
    }

    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    public function addGmcWorkLog($company_id,$staffer_id,$module,$type,$content)
    {
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");

        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('gmc_staffer_worklog', $logData);
    }

    public function addCrmWorkLog($company_id,$school_id,$marketer_id,$module,$type,$content,$module_id=0){
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");
//    	$module = $this->DataControl->selectClear("select module_name from  imc_module where  module_id ='{$module_id}' ");

        $stafferOne = $this->DataControl->getFieldOne("crm_marketer","staffer_id","marketer_id='{$marketer_id}'");
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['school_id'] =$school_id;
        $logData['staffer_id'] =$stafferOne['staffer_id'];
        $logData['worklog_module'] =$module;
        $logData['worklog_type'] =$type;
        $logData['worklog_content'] =$content;
        $logData['worklog_ip'] =real_ip();
        $logData['worklog_time'] =time();
        $this->DataControl->insertData('crm_staffer_worklog',$logData);
    }

    //生成收据序列号
    function createReceiptPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    function createStuRandom($student_branch){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = substr(date("ymdHis",time()),2);
        $Random = $student_branch.$rangtime.$rangtr;
        return $Random;
    }

    function createRandomBranch(){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $Str_num="1234567890";
        $date=date("Ymd",time());
        $randStr= $Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str_num[rand(0,9)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].substr($date,2).$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)].$Str[rand(0,25)];
        return $randStr;
    }

    function getTree($arr = array(), $pk = 'id', $upid = 'pid', $child = 'child')
    {
        $items = array();
        foreach ($arr as $val) {
            $items[$val[$pk]] = $val;
        }
        $tree = array();
        foreach ($items as $k => $val) {
            if (isset($items[$val[$upid]])) {
                $items[$val[$upid]][$child][] =& $items[$k];
            } else {
                $tree[] = &$items[$k];
            }
        }
        return $tree;
    }

    function getAge($birthday){
        //格式化出生时间年月日
        $byear=date('Y',strtotime($birthday));
        $bmonth=date('m',strtotime($birthday));
        $bday=date('d',strtotime($birthday));

        //格式化当前时间年月日
        $tyear=date('Y');
        $tmonth=date('m');
        $tday=date('d');

        //开始计算年龄
        $age=$tyear-$byear;
        if($bmonth>$tmonth || $bmonth==$tmonth && $bday>$tday){
            $age--;
        }
        return $age;
    }

    function getContract($company_id){
        $sql = "select sc.edition_id,ie.edition_code
              from imc_sales_contract as sc,imc_edition as ie
              where sc.edition_id=ie.edition_id and sc.company_id='{$company_id}' and sc.contract_starttime<=CURDATE() and sc.contract_endtime>=CURDATE() 
              order by sc.contract_createtime desc,sc.contract_id asc limit 0,1";

        $contractOne = $this->DataControl->selectOne($sql);

        return $contractOne;
    }

    //如果职工不存在 CRM 账号就创建 CRM 账号
    function addStaffMarketerOne($staffer_id){
        $stafferOne = $this->DataControl->selectOne("select company_id,staffer_id,staffer_istest,staffer_cnname,staffer_img,staffer_mobile
from smc_staffer WHERE staffer_id = '{$staffer_id}'");

        $datas = array();
        $datas['company_id'] = $stafferOne['company_id'];
        $datas['staffer_id'] = $stafferOne['staffer_id'];
        $datas['marketer_istest'] = $stafferOne['staffer_istest'];
        $datas['marketer_name'] = $stafferOne['staffer_cnname'];
        $datas['marketer_img'] = $stafferOne['staffer_img'];
        $datas['marketer_mobile'] = $stafferOne['staffer_mobile'];
        $datas['marketer_lasttime'] = time();
        $datas['marketer_lastip'] = real_ip();
        $datas['marketer_createtime'] =  time();
        $datas['marketer_status'] = '1';
        $datas['marketer_id'] = $this->DataControl->insertData('crm_marketer', $datas);

        return $datas;
    }

    /**
     * @param $client_id
     * @return bool
     * 检测用户状态 是否没有被分配  1-false被分配
     */
    function gmccheckClientstatus($client_id,$school_id=0)
    {
        if (!$client_id) {
            return false;
        }
        $principalOne = $this->DataControl->selectOne("SELECT p.principal_id FROM crm_client_principal p
WHERE p.school_id = '0' AND p.principal_leave = '0' AND p.principal_ismajor = '1' AND client_id = '{$client_id}'");
        if ($principalOne) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $client_id
     * @param $marketer_id
     * @param $ismajor
     * @return bool
     *  验证是否为负责人 --跟进时验证 --传0
     * 验证为否为主负责人 ---流失时验证 -- 传1
     */
    function gmcVerificationTrack($client_id, $marketer_id, $ismajor = 0)
    {
        $where = "marketer_id='{$marketer_id}'  and client_id='{$client_id}' and principal_leave =0";
        if ($ismajor == 1) {
            $where .= " and  principal_ismajor=1";
        }
        $principalOne = $this->DataControl->getOne('crm_client_principal', $where);
        if ($principalOne) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param $icard
     * @return bool
     * 检测用户身份证是否冲重复 ,重复不能添加
     * 新增时 不用 传$client_id
     * 编辑时 传 $client_id
     */
    function  checkClientIcard($icard,$client_id=0){
        $datawhere = " 1 and client_icard ='{$icard}' ";
        if(isset($client_id) && $client_id !==0 ){
            $datawhere .= " and client_id <> '{$client_id}' ";
        }
        $clientOne = $this->DataControl->selectOne("select client_id from crm_client where {$datawhere} ");

        if($clientOne){
            return false;   //有重复的
        }else{
            return true;    //没重复
        }
    }


    //清楚名单负责人
    function liftClientPrincipa($from='',$client_id,$school_id){
        $principalOne = $this->DataControl->selectOne("select 1 from crm_client_principal where client_id = '{$client_id}' and principal_leave = '0' limit 0,1 ");
        if($principalOne){
            //解除负责人分配日志
            $this->DataControl->updateData('crm_client_allotlog', "client_id='{$client_id}' and allotlog_status = '1' ", ["allotlog_status"=>'0',"allotlog_removetime"=>time()]);

            $pdata = array();
            $pdata['principal_leave'] = 1;
            $pdata['principal_updatatime'] = time();
            $this->DataControl->updateData('crm_client_principal', "client_id='{$client_id}' and principal_leave = '0' ", $pdata);
        }
    }
    //清楚名单负责人
    function liftClientSchool($from='',$client_id,$school_id){
        $schOne = $this->DataControl->selectOne("select 1 from crm_client_schoolenter where client_id = '{$client_id}' and is_enterstatus = '1' limit 0,1 ");
        if($schOne){
            $pdata = array();
            $pdata['is_enterstatus'] = -1;
            $pdata['schoolenter_updatetime'] = time();
            $this->DataControl->updateData('crm_client_schoolenter', "client_id='{$client_id}' and is_enterstatus = '1' ", $pdata);
        }
    }
    //清楚名单柜询
    function liftClientInvite($from='',$client_id,$school_id){
        $inviteOne = $this->DataControl->selectOne("select 1 from crm_client_invite where client_id = '{$client_id}' and invite_isvisit = '0' limit 0,1 ");
        if($inviteOne){
            $pdata = array();
            $pdata['invite_isvisit'] = -1;
            $pdata['invite_novisitreason'] = "(集团)主管审核无效名单，清楚未确认的柜询";
            $pdata['invite_updatetime'] = time();
            $this->DataControl->updateData('crm_client_invite', "client_id='{$client_id}' and invite_isvisit = '0' ", $pdata);
        }
    }
    //清楚名单试听
    function liftClientAudition($from='',$client_id,$school_id){
        $audOne = $this->DataControl->selectOne("select 1 from crm_client_audition where client_id = '{$client_id}' and audition_isvisit = '0' limit 0,1 ");
        if($audOne){
            $pdata = array();
            $pdata['audition_isvisit'] = -1;
            $pdata['audition_novisitreason'] = "(集团)主管审核无效名单，清楚未确认的柜询";
            $pdata['audition_updatetime'] = time();
            $this->DataControl->updateData('crm_client_audition', "client_id='{$client_id}' and audition_isvisit = '0' ", $pdata);
        }
    }


    public function __call($method, $args) {
        echo "unknown method " . $method;
        return false;

    }
}
