<?php


namespace Model\Gmc;

class StatisticsInstituteModel extends modelTpl
{
    public $error = false;
    public $errortip = false;//错误提示
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {

                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证教师
    function verdictStaffer($staffer_id)
    {

        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //集团统计中心 - 校所概况
    function InstituteSurveyApi($paramArray){
        $datawhere = "1";

        if(isset($paramArray['school_id']) && $paramArray['school_id'] != ''){
            $datawhere .= " and s.school_id = '{$paramArray['school_id']}'";
        }

        //校所分布城市
        $regionList = $this->DataControl->selectClear("SELECT DISTINCT r.region_name FROM smc_school as s LEFT JOIN smc_code_region as r ON r.region_id = s.school_city WHERE s.company_id = '{$this->company_id}' AND {$datawhere} AND r.region_name IS NOT NULL ");
        if($regionList){
            $regionNum = count($regionList);
        }else{
            $regionNum = 0;
        }

        //校所数量
        $schoolList = $this->DataControl->selectClear("SELECT count(s.school_id) as num FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND {$datawhere}");
        if($schoolList){
            $schoolNum = $schoolList['num'];
        }else{
            $schoolNum = 0;
        }

        //在籍学员
        $residenceStudentList = $this->DataControl->selectClear("SELECT count(distinct e.student_id) as num FROM smc_student_enrolled as e LEFT JOIN smc_school as s ON s.school_id = e.school_id WHERE {$datawhere} AND s.company_id = '{$this->company_id}' AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2'");
        if($residenceStudentList){
            $residenceNum = $residenceStudentList['num'];
        }else{
            $residenceNum = 0;
        }

        //在读学员
        $studyStudentList = $this->DataControl->selectClear("SELECT count(distinct s.student_id) as num FROM smc_student_study as s WHERE s.company_id = '{$this->company_id}' AND {$datawhere} AND s.study_isreading = '1' ");
        if($studyStudentList){
            $studyNum = $studyStudentList['num'];
        }else{
            $studyNum = 0;
        }

        //职工人数
        $stafferList = $this->DataControl->selectClear("SELECT count(s.staffer_id) as num FROM smc_staffer as s WHERE s.company_id = '{$this->company_id}' AND {$datawhere} and s.staffer_leave = '0' and s.account_class = '0'");
        if($stafferList){
            $stafferNum = $stafferList['num'];
        }else{
            $stafferNum = 0;
        }

        $data = array();
        $data['regionNum'] = $regionNum;
        $data['schoolNum'] = $schoolNum;
        $data['residenceNum'] = $residenceNum;
        $data['studyNum'] = $studyNum;
        $data['stafferNum'] = $stafferNum;

        return $data;
    }

    //集团统计中心 - 校所分布数量
    function InstituteApi($paramArray){
        $datawhere = "1";
        $datewhere = "s.company_id = '{$this->company_id}'";

//        if (isset($paramArray['school_id']) && $paramArray['school_id'] != '') {
//            $datawhere .= " and c.school_id = '{$paramArray['school_id']}'";
//        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
            $datawhere .= " and (r.region_name like '%{$paramArray['keyword']}%')";
            $datewhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%')";
        }
        $sql = "SELECT r.region_id,r.region_name,
                (SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.school_province = r.region_id AND {$datewhere}) as schoolNum,
                (SELECT COUNT(q.student_id) FROM (SELECT a.student_id,s.company_id,s.school_province,s.school_cnname,s.school_shortname
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                LEFT JOIN smc_code_coursetype c ON c.coursetype_id = b.coursetype_id
                LEFT JOIN smc_student as st on st.student_id=a.student_id
                LEFT JOIN smc_school AS s ON s.school_id = a.school_id
                WHERE a.coursebalance_figure > 0
                GROUP BY a.school_id, a.student_id, b.coursetype_id) as q WHERE q.company_id='{$this->company_id}' AND q.school_province = r.region_id AND (q.school_cnname like '%{$paramArray['keyword']}%' or q.school_shortname like '%{$paramArray['keyword']}%')) as residenceNum,
                (SELECT COUNT(DISTINCT d.student_id)
                FROM smc_student AS sd
                LEFT JOIN smc_student_study AS d ON sd.student_id = d.student_id
                LEFT JOIN smc_school AS s ON d.school_id = s.school_id
                LEFT JOIN smc_class AS c ON d.class_id = c.class_id
                LEFT JOIN smc_course AS r ON c.course_id = r.course_id
                LEFT JOIN smc_code_coursetype AS cc ON cc.coursetype_id = r.coursetype_id
                WHERE {$datewhere} AND s.school_province = r.region_id AND d.study_beginday <= CURDATE() AND d.study_endday >= CURDATE()) as studyNum,
                (SELECT COUNT(p.postbe_id) FROM smc_school as s LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' WHERE {$datewhere} AND s.school_province = r.region_id) as stafferNum
                FROM smc_code_region as r
                WHERE (r.region_name like '%{$paramArray['province']}%') AND r.region_level = '2'";
        $regionOne = $this->DataControl->selectOne($sql);
        if($regionOne){
            $regionOne['list'] = $this->DataControl->selectClear("SELECT r.region_id,r.region_name,(SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.school_city = r.region_id AND {$datewhere}) as schoolNum FROM smc_code_region as r WHERE r.parent_id = '{$regionOne['region_id']}' AND {$datawhere} HAVING schoolNum > 0");
            if(!$regionOne['list']){
                $regionOne['list'] = $this->DataControl->selectClear("SELECT r.region_id,r.region_name,(SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.school_city = r.region_id AND {$datewhere}) as schoolNum FROM smc_code_region as r WHERE r.parent_id = '{$regionOne['region_id']}' HAVING schoolNum > 0");

            }
            if($regionOne['list']){
                foreach($regionOne['list'] as &$v){
                    $v['list'] = $this->DataControl->selectClear("SELECT s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_address,s.school_phone,
                                                                        (SELECT COUNT(DISTINCT c.class_id) FROM smc_class as c WHERE c.school_id = s.school_id AND class_status>='0' AND class_stdate<=CURDATE() AND (class_enddate>=CURDATE() OR class_enddate='')) as classNum,
                                                                        (SELECT COUNT(q.student_id) FROM (SELECT a.student_id,a.school_id
                                                                        FROM smc_student_coursebalance AS a
                                                                        LEFT JOIN smc_course b ON a.course_id = b.course_id
                                                                        LEFT JOIN smc_code_coursetype c ON c.coursetype_id = b.coursetype_id
                                                                        LEFT JOIN smc_student as st on st.student_id=a.student_id
                                                                        WHERE a.coursebalance_figure > 0
                                                                        GROUP BY a.school_id, a.student_id, b.coursetype_id) as q WHERE q.school_id=s.school_id) as residenceNum,
                                                                        (SELECT COUNT(DISTINCT d.student_id)
                                                                        FROM smc_student AS s
                                                                        LEFT JOIN smc_student_study AS d ON s.student_id = d.student_id
                                                                        LEFT JOIN smc_school AS l ON d.school_id = l.school_id
                                                                        LEFT JOIN smc_class AS c ON d.class_id = c.class_id
                                                                        LEFT JOIN smc_course AS r ON c.course_id = r.course_id
                                                                        LEFT JOIN smc_code_coursetype AS cc ON cc.coursetype_id = r.coursetype_id
                                                                        WHERE d.school_id = s.school_id AND d.study_beginday <= CURDATE() AND d.study_endday >= CURDATE()) as studyNum,
                                                                        (SELECT COUNT(p.postbe_id) FROM gmc_staffer_postbe as p WHERE p.school_id = s.school_id AND p.postbe_status='1' AND postbe_ismianjob='1') as stafferNum
                                                                        FROM smc_school as s WHERE s.school_city = '{$v['region_id']}' AND {$datewhere}");

                    if(!$v['list']){
                        $v['list'] = array();
                    }
                }
            }else{
                $regionOne['list'] = array();
            }
        } else {
            $regionOne = array();
        }

        return $regionOne;
    }

    //集团统计中心 - 校所分布数量
    function DistributionApi($paramArray){
        $datawhere = "1";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];

        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($paramArray['type']) && $paramArray['type'] ==  '1') {

            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (r.region_name like '%{$paramArray['keyword']}%')";
            }
            $sql = "SELECT r.region_id,r.region_name,
                    (SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as schoolNum,
                    (SELECT COUNT(e.student_id) FROM smc_school as s LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2' WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as residenceNum,
                    (SELECT COUNT(ss.study_id) FROM smc_school as s LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1' WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as studyNum,
                    (SELECT COUNT(p.postbe_id) FROM smc_school as s LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as stafferNum
                    FROM smc_code_region as r WHERE {$datawhere} and r.region_level = '2' LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);

        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '2') {

            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (r.region_name like '%{$paramArray['keyword']}%')";
            }
            $sql = "SELECT r.region_id,r.region_name,
                    (SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as schoolNum,
                    (SELECT COUNT(e.student_id) FROM smc_school as s LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2' WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as residenceNum,
                    (SELECT COUNT(ss.study_id) FROM smc_school as s LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1' WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as studyNum,
                    (SELECT COUNT(p.postbe_id) FROM smc_school as s LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as stafferNum
                    FROM smc_code_region as r WHERE {$datawhere} and r.region_level = '3' and r.region_id NOT IN (33,35,107,270) LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);

        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '3') {
            $datawhere .= " and s.company_id = '{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%')";
            }

            $sql = "SELECT s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                    (SELECT COUNT(e.student_id) FROM smc_student_enrolled as e WHERE e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2') as residenceNum,
                    (SELECT COUNT(ss.study_id) FROM smc_student_study as ss WHERE ss.school_id = s.school_id AND ss.study_isreading = '1') as studyNum,
                    (SELECT COUNT(p.postbe_id) FROM gmc_staffer_postbe as p WHERE p.school_id = s.school_id AND p.postbe_status='1') as stafferNum
                    FROM smc_school as s WHERE {$datawhere} LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if($dataList){
                foreach($dataList as &$v){
                    $v['schoolNum'] = 1;
                }
            }
        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '4') {
            $datawhere .= " and co.company_id = '{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (co.organize_cnname like '%{$paramArray['keyword']}%')";
            }
            if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id'] != '') {
                $datawhere .= " and co.organizeclass_id = '{$paramArray['organizeclass_id']}'";
            }

            $sql = "SELECT co.organize_id,co.organize_cnname,
                    (SELECT COUNT(s.school_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        WHERE os.organize_id = co.organize_id
                    ) as schoolNum,
                    (SELECT COUNT(e.student_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2'
                        WHERE os.organize_id = co.organize_id
                    ) as residenceNum,
                    (SELECT COUNT(ss.study_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1'
                        WHERE os.organize_id = co.organize_id
                    ) as studyNum,
                    (SELECT COUNT(p.postbe_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1'
                        WHERE os.organize_id = co.organize_id
                    ) as stafferNum
                    FROM gmc_company_organize as co
                    WHERE {$datawhere} AND co.father_id = '0' LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '5') {
            $datawhere .= " and co.company_id = '{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (co.organize_cnname like '%{$paramArray['keyword']}%')";
            }
            if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id'] != '') {
                $datawhere .= " and co.organizeclass_id = '{$paramArray['organizeclass_id']}'";
            }

            $sql = "SELECT co.organize_id,co.organize_cnname,
                    (SELECT COUNT(s.school_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        WHERE os.organize_id = co.organize_id
                    ) as schoolNum,
                    (SELECT COUNT(e.student_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2'
                        WHERE os.organize_id = co.organize_id
                    ) as residenceNum,
                    (SELECT COUNT(ss.study_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1'
                        WHERE os.organize_id = co.organize_id
                    ) as studyNum,
                    (SELECT COUNT(p.postbe_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1'
                        WHERE os.organize_id = co.organize_id
                    ) as stafferNum
                    FROM gmc_company_organize as co
                    WHERE {$datawhere} AND co.father_id <> '0' LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
        }
        if($dataList){
            if (isset($paramArray['orderby']) && $paramArray['orderby'] == '1') {
                $sort_arr = [];
                foreach ($dataList as $val) {
                    $sort_arr[] = $val['schoolNum'];
                }
                array_multisort($sort_arr, SORT_ASC, $dataList);
            } elseif (isset($paramArray['orderby']) && $paramArray['orderby'] == '2') {
                $sort_arr = [];
                foreach ($dataList as $val) {
                    $sort_arr[] = $val['schoolNum'];
                }
                array_multisort($sort_arr, SORT_DESC, $dataList);
            }
            if(isset($paramArray['type']) && ($paramArray['type'] ==  '1' || $paramArray['type'] ==  '2')){
                $name = array_column($dataList, "region_name");
            }elseif(isset($paramArray['type']) && $paramArray['type'] ==  '3'){
                $name = array_column($dataList, "school_cnname");
            }elseif(isset($paramArray['type']) && ($paramArray['type'] ==  '4' || $paramArray['type'] ==  '5')){
                $name = array_column($dataList, "organize_cnname");
            }
            $num = array();
            $num[0]['name'] = $this->LgStringSwitch('校所数量');
            $num[0]['data'] = array_column($dataList, "schoolNum");
            $num[1]['name'] = $this->LgStringSwitch('在籍生数量');
            $num[1]['data'] = array_column($dataList, "residenceNum");
            $num[2]['name'] = $this->LgStringSwitch('在读学员');
            $num[2]['data'] = array_column($dataList, "studyNum");
            $num[3]['name'] = $this->LgStringSwitch('教职工数量');
            $num[3]['data'] = array_column($dataList, "stafferNum");
        }else{
            $num = array();
            $num[0]['name'] = $this->LgStringSwitch('校所数量');
            $num[1]['name'] = $this->LgStringSwitch('在籍生数量');
            $num[2]['name'] = $this->LgStringSwitch('在读学员');
            $num[3]['name'] = $this->LgStringSwitch('教职工数量');
        }

        $data = array();
        $data['x_data'] = $name ? $name : array();
        $data['y_data'] = $num;

        $data['legendData'] = $this->LgArraySwitch(array("校所数量","在籍生数量","在读学员","教职工数量"));
        return $data;
    }

    //集团统计中心 - 校所概况 - 查看全部
    function SchoolTotalApi($paramArray){
        $datawhere = "1";

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($paramArray['order_by']) && $paramArray['order_by'] != '' && isset($paramArray['order_by_field']) && $paramArray['order_by_field'] != ''){
            $orderby = 'ORDER BY ' . $paramArray['order_by_field'] . ' ' . $paramArray['order_by'];
        }

        $data = array();
        if (isset($paramArray['type']) && $paramArray['type'] ==  '1') {

            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (r.region_name like '%{$paramArray['keyword']}%')";
            }
            $sql = "SELECT r.region_id,r.region_name,
                    (SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as schoolNum,
                    (SELECT COUNT(e.student_id) FROM smc_school as s LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2' WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as residenceNum,
                    (SELECT COUNT(DISTINCT ss.student_id) FROM smc_school as s LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1' WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as studyNum,
                    (SELECT COUNT(p.postbe_id) FROM smc_school as s LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' AND p.postbe_ismianjob='1' WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as stafferNum
                    FROM smc_code_region as r WHERE {$datawhere} AND r.region_level = '2' HAVING schoolNum > 0 {$orderby} LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);

            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.region_id) as num FROM
                                                            (SELECT region_id,(SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND s.school_province = r.region_id) as schoolNum
                                                            FROM smc_code_region as r
                                                            WHERE {$datawhere} AND r.region_level = '2' HAVING schoolNum > 0) as q");

                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '2') {

            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (r.region_name like '%{$paramArray['keyword']}%')";
            }
            $sql = "SELECT r.region_id,r.region_name,
                    (SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as schoolNum,
                    (SELECT COUNT(e.student_id) FROM smc_school as s LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2' WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as residenceNum,
                    (SELECT COUNT(DISTINCT ss.student_id) FROM smc_school as s LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1' WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as studyNum,
                    (SELECT COUNT(p.postbe_id) FROM smc_school as s LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' AND p.postbe_ismianjob='1' WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as stafferNum
                    FROM smc_code_region as r WHERE {$datawhere} AND r.region_level = '3' AND r.region_id NOT IN (33,35,107,270) HAVING schoolNum > 0 {$orderby} LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);

            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(q.region_id) as num FROM
                                                            (SELECT region_id,(SELECT COUNT(s.school_id) FROM smc_school as s WHERE s.company_id = '{$this->company_id}' AND s.school_city = r.region_id) as schoolNum
                                                            FROM smc_code_region as r WHERE {$datawhere} AND r.region_level = '3' AND r.region_id NOT IN (33,35,107,270) HAVING schoolNum > 0) AS q");

                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '3') {
            $datawhere .= " and s.company_id = '{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (s.school_cnname like '%{$paramArray['keyword']}%' or s.school_shortname like '%{$paramArray['keyword']}%')";
            }

            $sql = "SELECT s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                    (SELECT COUNT(e.student_id) FROM smc_student_enrolled as e WHERE e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2') as residenceNum,
                    (SELECT COUNT(DISTINCT ss.student_id) FROM smc_student_study as ss WHERE ss.school_id = s.school_id AND ss.study_isreading = '1') as studyNum,
                    (SELECT COUNT(p.postbe_id) FROM gmc_staffer_postbe as p WHERE p.school_id = s.school_id AND p.postbe_status='1' AND p.postbe_ismianjob='1') as stafferNum
                    FROM smc_school as s WHERE {$datawhere} {$orderby} LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if($dataList){
                foreach($dataList as &$v){
                    $v['schoolNum'] = 1;
                }
            }

            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(s.school_id) as num FROM smc_school as s WHERE {$datawhere}");

                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '4') {
            $datawhere .= " and co.company_id = '{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (co.organize_cnname like '%{$paramArray['keyword']}%')";
            }
            if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id'] != '') {
                $datawhere .= " and co.organizeclass_id = '{$paramArray['organizeclass_id']}'";
            }

            $sql = "SELECT co.organize_id,co.organize_cnname,
                    (SELECT COUNT(s.school_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        WHERE os.organize_id = co.organize_id
                    ) as schoolNum,
                    (SELECT COUNT(e.student_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2'
                        WHERE os.organize_id = co.organize_id
                    ) as residenceNum,
                    (SELECT COUNT(DISTINCT ss.student_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1'
                        WHERE os.organize_id = co.organize_id
                    ) as studyNum,
                    (SELECT COUNT(p.postbe_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' AND p.postbe_ismianjob='1'
                        WHERE os.organize_id = co.organize_id
                    ) as stafferNum
                    FROM gmc_company_organize as co
                    WHERE {$datawhere} AND co.father_id = '0' {$orderby} LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);

            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(co.organize_id) as num FROM gmc_company_organize as co WHERE {$datawhere} AND co.father_id = '0'");

                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

        } elseif (isset($paramArray['type']) && $paramArray['type'] ==  '5') {
            $datawhere .= " and co.company_id = '{$this->company_id}'";
            if (isset($paramArray['keyword']) && $paramArray['keyword'] != '') {
                $datawhere .= " and (co.organize_cnname like '%{$paramArray['keyword']}%')";
            }
            if (isset($paramArray['organizeclass_id']) && $paramArray['organizeclass_id'] != '') {
                $datawhere .= " and co.organizeclass_id = '{$paramArray['organizeclass_id']}'";
            }

            $sql = "SELECT co.organize_id,co.organize_cnname,
                    (SELECT COUNT(s.school_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        WHERE os.organize_id = co.organize_id
                    ) as schoolNum,
                    (SELECT COUNT(e.student_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_enrolled as e ON e.school_id = s.school_id AND e.enrolled_status<>'-1' AND e.enrolled_status<>'2'
                        WHERE os.organize_id = co.organize_id
                    ) as residenceNum,
                    (SELECT COUNT(DISTINCT ss.student_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN smc_student_study as ss ON ss.school_id = s.school_id AND ss.study_isreading = '1'
                        WHERE os.organize_id = co.organize_id
                    ) as studyNum,
                    (SELECT COUNT(p.postbe_id)
                        FROM gmc_company_organizeschool as os
                        LEFT JOIN smc_school as s ON s.school_id = os.school_id
                        LEFT JOIN gmc_staffer_postbe as p ON p.school_id = s.school_id AND p.postbe_status='1' AND p.postbe_ismianjob='1'
                        WHERE os.organize_id = co.organize_id
                    ) as stafferNum
                    FROM gmc_company_organize as co
                    WHERE {$datawhere} AND co.father_id <> '0' {$orderby} LIMIT {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);

            if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
                $db_nums = $this->DataControl->selectOne("SELECT COUNT(co.organize_id) as num FROM gmc_company_organize as co WHERE {$datawhere} AND co.father_id <> '0'");

                if ($db_nums) {
                    $allnum = $db_nums['num'];
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
        }

        if (!$dataList) {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

}