<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

use Model\Smc\ClassModel;

class CourseModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //获取课程类型列表
    function getCoursetypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.coursetype_cnname like '%{$paramArray['keyword']}%' or s.coursetype_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.coursetype_id,
                s.coursetype_cnname,
                s.coursetype_branch,
                s.coursetype_isopenclass,
                s.coursetype_isname,
                s.coursetype_isname as coursetype_isname_name,
                c.company_iseditimportcourse,
                s.coursetype_ischeckwork,
                s.coursetype_refundtype,
                s.coursetype_isimport,
                s.coursetype_isrecruit,
                s.coursetype_newchecked
            FROM
                smc_code_coursetype AS s
                left join gmc_company as c on c.company_id=s.company_id
            WHERE
                {$datawhere} and s.company_id = '{$paramArray['company_id']}'
            ORDER BY
                s.coursetype_id DESC    
            LIMIT {$pagestart},{$num}";

//                s.coursetype_protocol,
//                s.coursetype_refundinfo,
//                s.coursetype_buyinfo,

        $CoursetypeList = $this->DataControl->selectClear($sql);

        if ($CoursetypeList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $recruitstatus = $this->LgArraySwitch(array("0" => "暂不招生", "1" => "招生课程"));
            $statuss = $this->LgArraySwitch(array("0" => "优先显示课程英文名", "1" => "优先显示课程中文名"));
            $type = $this->LgArraySwitch(array("0" => "原价扣除", "1" => "实缴实退"));
            foreach ($CoursetypeList as &$val) {
                $val['coursetype_isopenclass'] = $status[$val['coursetype_isopenclass']];
                $val['coursetype_isname_name'] = $statuss[$val['coursetype_isname_name']];
                $val['coursetype_refundtype_name'] = $type[$val['coursetype_refundtype']];
                $val['coursetype_isrecruit_name'] = $recruitstatus[$val['coursetype_isrecruit']];
                if ($val['company_iseditimportcourse'] == 0 && $val['coursetype_isimport'] == 1) {
                    $val['can_edit'] = 0;
                } else {
                    $val['can_edit'] = 1;//是否可编辑  0-不可以  1-可以
                }
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.coursetype_id)
            FROM
                smc_code_coursetype AS s
            WHERE
                {$datawhere} and s.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('coursetype_cnname ', 'coursetype_branch', 'coursetype_isopenclass', 'coursetype_refundtype_name', 'coursetype_isname_name', 'coursetype_isrecruit_name');
        $fieldname = $this->LgArraySwitch(array('课程类型名称', '课程类型编号', '是否公开课', '退费模式', '优先显示班级信息', '是否招生课程'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CoursetypeList) {
            $result['list'] = $CoursetypeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程类型(班组)", 'result' => $result);
        }

        return $res;
    }

    //添加课程类型
    function addCoursetypeAction($paramArray)
    {
        $data = array();
        $data['coursetype_cnname'] = $paramArray['coursetype_cnname'];
        $data['coursetype_branch'] = $paramArray['coursetype_branch'];
        $data['coursetype_isopenclass'] = $paramArray['coursetype_isopenclass'];
//        $data['coursetype_protocol'] = $paramArray['coursetype_protocol'];
//        $data['coursetype_refundinfo'] = $paramArray['coursetype_refundinfo'];
//        $data['coursetype_buyinfo'] = $paramArray['coursetype_buyinfo'];
        $data['coursetype_isname'] = $paramArray['coursetype_isname'];
        $data['coursetype_ischeckwork'] = $paramArray['coursetype_ischeckwork'];
        $data['coursetype_refundtype'] = $paramArray['coursetype_refundtype'];
        $data['coursetype_isrecruit'] = $paramArray['coursetype_isrecruit'];
        $data['coursetype_newchecked'] = $paramArray['coursetype_newchecked'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['coursetype_cnname'] = "课程类型名称";
        $field['coursetype_branch'] = "课程类型编号";
        $field['coursetype_isopenclass'] = "是否公开课";
        $field['coursetype_isrecruit'] = "是否招生课程";
        $field['coursetype_newchecked'] = "新生勾稽类型";
        $field['company_id'] = "所属集团";

        $coursetype_branch = $this->DataControl->getFieldOne('smc_code_coursetype', 'coursetype_id', "coursetype_branch = '{$paramArray['coursetype_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($coursetype_branch) {
            ajax_return(array('error' => 1, 'errortip' => "课程类型编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_coursetype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加课程类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '添加课程类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加课程类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑课程类型
    function updateCoursetypeAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "coursetype_id = '{$paramArray['coursetype_id']}'");
        if ($coursetypeOne) {
            $data = array();
            $data['coursetype_cnname'] = $paramArray['coursetype_cnname'];
            $data['coursetype_branch'] = $paramArray['coursetype_branch'];
            $data['coursetype_isopenclass'] = $paramArray['coursetype_isopenclass'];
//            $data['coursetype_protocol'] = $paramArray['coursetype_protocol'];
//            $data['coursetype_refundinfo'] = $paramArray['coursetype_refundinfo'];
//            $data['coursetype_buyinfo'] = $paramArray['coursetype_buyinfo'];
            $data['coursetype_isname'] = $paramArray['coursetype_isname'];
            $data['coursetype_ischeckwork'] = $paramArray['coursetype_ischeckwork'];
            $data['coursetype_refundtype'] = $paramArray['coursetype_refundtype'];
            $data['coursetype_isrecruit'] = $paramArray['coursetype_isrecruit'];
            $data['coursetype_newchecked'] = $paramArray['coursetype_newchecked'];

            $field = array();
            $field['coursetype_cnname'] = "课程类型名称";
            $field['coursetype_branch'] = "课程类型编号";
            $field['coursetype_isopenclass'] = "是否公开课";
            $field['coursetype_isrecruit'] = "是否招生课程";
            $field['coursetype_newchecked'] = "新生勾稽类型";

            $coursetype_branch = $this->DataControl->getFieldOne('smc_code_coursetype', 'coursetype_branch', "coursetype_id = '{$paramArray['coursetype_id']}'");
            if ($paramArray['coursetype_branch'] != $coursetype_branch['coursetype_branch']) {
                $coursetype_branch = $this->DataControl->getFieldOne('smc_code_coursetype', 'coursetype_id', "coursetype_branch = '{$paramArray['coursetype_branch']}' and company_id = '{$paramArray['company_id']}'");
                if ($coursetype_branch) {
                    ajax_return(array('error' => 1, 'errortip' => "课程类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_coursetype", "coursetype_id = '{$paramArray['coursetype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "课程类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '编辑课程类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '课程类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除课程类型
    function delCoursetypeAction($paramArray)
    {
        $coursetypeOne = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_id", "coursetype_id = '{$paramArray['coursetype_id']}'");
        if ($coursetypeOne) {
            $a = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "coursetype_id = '{$paramArray['coursetype_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该课程类型下包含班种，不可删除"));
            }

            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_coursetype", "coursetype_id = '{$paramArray['coursetype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除课程类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '删除课程类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除课程类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //班种列表
    function coursecatView($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.coursecat_cnname like '%{$paramArray['keyword']}%' or c.coursecat_branch like '%{$paramArray['keyword']}%' or t.coursetype_cnname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ={$paramArray['coursetype_id']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.coursecat_id,
                c.coursecat_cnname,
                c.coursecat_branch,
                c.coursetype_id,
                c.coursecat_isediting,
                c.coursecat_iscrmadded,
                c.coursecat_iscrmadded as coursecat_iscrmadded_name,
                c.coursecat_ismonth,
                c.coursecat_ismonth as coursecat_ismonth_name,
                t.coursetype_cnname,
                t.coursetype_isrecruit,
                c.coursecat_isimport,
                c.coursecat_istreaty,
                c.coursecat_istreaty as coursecat_istreaty_name,
                co.company_iseditimportcourse
            FROM
                smc_code_coursecat AS c
                LEFT JOIN smc_code_coursetype AS t ON c.coursetype_id = t.coursetype_id
                left join gmc_company as co on co.company_id=c.company_id
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'
            ORDER BY
                c.coursecat_id DESC    
            LIMIT {$pagestart},{$num}";
//        c.coursecat_isrecruit,
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
//            $recruitstatus = $this->LgArraySwitch(array("0" => "暂不招生", "1" => "招生课程"));
            $status = $this->LgArraySwitch(array("0" => "暂不招生", "1" => "招生课程"));
            $statuss = $this->LgArraySwitch(array("0" => "不允许", "1" => "允许"));
            $statusss = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            foreach ($coursecatList as &$val) {
                $val['coursecat_iscrmadded_name'] = $status[$val['coursecat_iscrmadded_name']];
                $val['coursecat_istreaty_name'] = $statusss[$val['coursecat_istreaty_name']];
                $val['coursecat_ismonth_name'] = $statuss[$val['coursecat_ismonth_name']];
//                $val['coursecat_isrecruit_name'] = $recruitstatus[$val['coursecat_isrecruit']];
                if ($val['company_iseditimportcourse'] == 0 && $val['coursecat_isimport'] == 1) {
                    $val['can_edit'] = 0;
                } else {
                    $val['can_edit'] = 1;//是否可编辑  0-不可以  1-可以
                }

            }

        }

        $all_num = $this->DataControl->select("
            SELECT
                COUNT(c.coursecat_id)
            FROM
                smc_code_coursecat AS c
                LEFT JOIN smc_code_coursetype AS t ON c.coursetype_id = t.coursetype_id
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('coursecat_cnname ', 'coursecat_branch', 'coursetype_cnname', 'coursecat_iscrmadded_name', 'coursecat_istreaty_name', 'coursetype_isrecruit'); //, 'coursecat_isrecruit_name'
        $fieldname = $this->LgArraySwitch(array('班种名称', '班种编号', '所属班组', '是否招生课程', '是否开启合同', '是否招生课程（班组）   0 不是  1 是'));//, '是否招生课程'
        $fieldcustom = array("1", "1", "1", "1", "1", "0");//, "1"
        $fieldshow = array("1", "1", "1", "1", "1", "0");//, "1"

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $result['coursetype'] = $this->DataControl->selectClear("select coursetype_id,coursetype_cnname,coursetype_branch,coursetype_isrecruit from smc_code_coursetype where company_id = '{$paramArray['company_id']}'");

//        var_dump( $result['list']);die();

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无班种信息", 'result' => $result);
        }

        return $res;

    }

    //班种特殊月份设置列表
    function coursecatMonth($paramArray)
    {
        $sql = "
            SELECT
                md.monthdis_id,
                md.monthdis_month,
                md.monthdis_ratio,
                md.monthdis_remark 
            FROM
                smc_code_coursecat_monthdis AS md 
            WHERE
                md.company_id = '{$paramArray['company_id']}' 
                AND md.coursecat_id = '{$paramArray['coursecat_id']}'";
        $coursecatList = $this->DataControl->selectClear($sql);

        if ($coursecatList) {
            foreach ($coursecatList as &$val) {
                $val['monthdis_ratio_name'] = $val['monthdis_ratio'] * 10 . '折';
                $val['monthdis_ratio'] = $val['monthdis_ratio'] * 10;
            }
        }

        $fieldstring = array('monthdis_month', 'monthdis_ratio_name', 'monthdis_remark');
        $fieldname = $this->LgArraySwitch(array('优惠月份', '折扣', '备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($coursecatList) {
            $result['list'] = $coursecatList;
        } else {
            $result['list'] = array();
        }

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无特殊月份设置信息", 'result' => $result);
        }

        return $res;

    }

    //添加特殊月份设置
    function addCoursecatMonthAction($paramArray)
    {
        $data = array();
        $schoolList = json_decode(stripslashes($paramArray['month']), true);
        foreach ($schoolList as $item) {
            $data['monthdis_month'] = $item['monthdis_month'];
            $data['monthdis_ratio'] = $item['monthdis_ratio'] / 10;
            $data['monthdis_remark'] = $item['monthdis_remark'];
            $data['coursecat_id'] = $paramArray['coursecat_id'];
            $data['coursecat_branch'] = $paramArray['coursecat_branch'];
            $data['company_id'] = $paramArray['company_id'];
            $data['monthdis_createtime'] = time();

            $a = $this->DataControl->getFieldOne('smc_code_coursecat_monthdis', 'monthdis_id', "monthdis_month = '{$item['monthdis_month']}' and coursecat_id = '{$paramArray['coursecat_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "'{$item['monthdis_month']}'已设置，请勿重复添加"));
            }
            $this->DataControl->insertData('smc_code_coursecat_monthdis', $data);
        }

        $res = array('error' => '0', 'errortip' => "添加特殊月份设置成功", 'result' => array());
        $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '添加特殊月份设置', dataEncode($paramArray));

        return $res;
    }

    //添加班种
    function addCoursecatAction($paramArray)
    {
        $data = array();
        $data['coursecat_cnname'] = $paramArray['coursecat_cnname'];
        $data['coursecat_branch'] = $paramArray['coursecat_branch'];
        $data['coursetype_id'] = $paramArray['coursetype_id'];
        $data['coursecat_iscrmadded'] = $paramArray['coursecat_iscrmadded'];
        $data['coursecat_ismonth'] = $paramArray['coursecat_ismonth'];
        $data['coursecat_istreaty'] = $paramArray['coursecat_istreaty'];
        $data['company_id'] = $paramArray['company_id'];
//        $data['coursecat_isrecruit'] = $paramArray['coursecat_isrecruit'];

        $field = array();
        $field['coursecat_cnname'] = "课程分类名称";
        $field['coursecat_branch'] = "课程分类编号";
        $field['coursetype_id'] = "所属班组";
        $field['coursecat_iscrmadded'] = "是否招生课程";
        $field['coursecat_ismonth'] = "是否允许特殊月份收费";
        $field['company_id'] = "所属公司";
//        $field['coursecat_isrecruit'] = "是否招生课程";

        $coursecat_branch = $this->DataControl->getFieldOne('smc_code_coursecat', 'coursecat_id', "coursecat_branch = '{$paramArray['coursecat_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($coursecat_branch) {
            ajax_return(array('error' => 1, 'errortip' => "课程分类编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_coursecat', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加班种成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '添加班种', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加班种失败', 'result' => $result);
        }
        return $res;
    }

    //编辑班种
    function updateCoursecatAction($paramArray)
    {
        $CoursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "coursecat_id = '{$paramArray['coursecat_id']}'");
        if ($CoursecatOne) {
            $data = array();
            $data['coursecat_cnname'] = $paramArray['coursecat_cnname'];
            $data['coursecat_branch'] = $paramArray['coursecat_branch'];
            $data['coursecat_iscrmadded'] = $paramArray['coursecat_iscrmadded'];
            $data['coursecat_ismonth'] = $paramArray['coursecat_ismonth'];
            $data['coursetype_id'] = $paramArray['coursetype_id'];
            $data['coursecat_isediting'] = $paramArray['coursecat_isediting'];
            $data['coursecat_istreaty'] = $paramArray['coursecat_istreaty'];
//            $data['coursecat_isrecruit'] = $paramArray['coursecat_isrecruit'];

            $field = array();
            $field['coursecat_cnname'] = "班种名称";
            $field['coursecat_branch'] = "班种编号";
            $field['coursecat_iscrmadded'] = "是否招生课程";
            $field['coursecat_ismonth'] = "是否允许特殊月份收费";
            $field['coursetype_id'] = "所属班组";
//            $field['coursecat_isrecruit'] = "是否招生课程";

            $coursecat_branch = $this->DataControl->getFieldOne('smc_code_coursecat', 'coursecat_branch', "coursecat_id = '{$paramArray['coursecat_id']}'");
            if ($paramArray['coursecat_branch'] != $coursecat_branch['coursecat_branch']) {
                $coursecat_branch = $this->DataControl->getFieldOne('smc_code_coursecat', 'coursecat_id', "coursecat_branch = '{$paramArray['coursecat_branch']}' and company_id = '{$paramArray['company_id']}'");
                if ($coursecat_branch) {
                    ajax_return(array('error' => 1, 'errortip' => "班种编号已存在!"));
                }
            }
            $datas = array();
            $datas['coursetype_id'] = $paramArray['coursetype_id'];

            if ($this->DataControl->updateData("smc_code_coursecat", "coursecat_id = '{$paramArray['coursecat_id']}'", $data) && $this->DataControl->updateData("smc_course", "coursecat_id = '{$paramArray['coursecat_id']}'", $datas)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "班种修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '编辑班种', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '班种修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑特殊月份设置
    function updateCoursecatMonthAction($paramArray)
    {
        $CoursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat_monthdis", "monthdis_id", "monthdis_id = '{$paramArray['monthdis_id']}'");
        if ($CoursecatOne) {
            $data = array();
            $data['monthdis_month'] = $paramArray['monthdis_month'];
            $data['monthdis_ratio'] = $paramArray['monthdis_ratio'] / 10;
            $data['monthdis_remark'] = $paramArray['monthdis_remark'];

            $field = array();
            $field['monthdis_month'] = "月份";
            $field['monthdis_ratio'] = "折扣";
            $field['monthdis_remark'] = "备注";

            $coursecat_branch = $this->DataControl->getFieldOne('smc_code_coursecat_monthdis', 'monthdis_month', "monthdis_id = '{$paramArray['monthdis_id']}'");
            if ($paramArray['monthdis_month'] != $coursecat_branch['monthdis_month']) {
                $coursecat_branch = $this->DataControl->getFieldOne('smc_code_coursecat_monthdis', 'monthdis_id', "monthdis_month = '{$paramArray['monthdis_month']}' and coursecat_id = '{$paramArray['coursecat_id']}'");
                if ($coursecat_branch) {
                    ajax_return(array('error' => 1, 'errortip' => "月份已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_coursecat_monthdis", "monthdis_id = '{$paramArray['monthdis_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑特殊月份设置成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '编辑特殊月份设置', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑特殊月份设置失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除班种
    function delCoursecatAction($paramArray)
    {
        $CoursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat", "coursecat_id", "coursecat_id = '{$paramArray['coursecat_id']}'");
        if ($CoursecatOne) {
            $a = $this->DataControl->getFieldOne("smc_course", "course_id", "coursecat_id = '{$paramArray['coursecat_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该班种下包含课程，不可删除"));
            }

            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_coursecat", "coursecat_id = '{$paramArray['coursecat_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除班种成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '删除班种', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除班种失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除特殊月份设置
    function delCoursecatMonthAction($paramArray)
    {
        $CoursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat_monthdis", "monthdis_id", "monthdis_id = '{$paramArray['monthdis_id']}'");
        if ($CoursecatOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_coursecat_monthdis", "monthdis_id = '{$paramArray['monthdis_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除特殊月份设置成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '特殊月份设置', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除特殊月份设置失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //课程期收类型列表
    function coursetermView($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.courseterm_name like '%{$paramArray['keyword']}%' or c.courseterm_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.courseterm_id,
                c.courseterm_name,
                c.courseterm_code,
                c.courseterm_remk
            FROM
                smc_code_courseterm AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'
            ORDER BY
                c.courseterm_id DESC    
            LIMIT {$pagestart},{$num}";

        $coursetermList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.courseterm_id)
            FROM
                smc_code_courseterm AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('courseterm_name ', 'courseterm_code', 'courseterm_remk');
        $fieldname = array('课程期收名称', '课程期收编号', '课程期收备注');
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($coursetermList) {
            $result['list'] = $coursetermList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程期收类型信息", 'result' => $result);
        }
        return $res;
    }

    //添加课程期收类型
    function addCoursetermAction($paramArray)
    {
        $data = array();
        $data['courseterm_name'] = $paramArray['courseterm_name'];
        $data['courseterm_code'] = $paramArray['courseterm_code'];
        $data['courseterm_remk'] = $paramArray['courseterm_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['courseterm_name'] = "课程期收名称";
        $field['courseterm_code'] = "课程期收编号";
        $field['courseterm_remk'] = "课程期收备注";
        $field['company_id'] = "所属集团";

        $courseterm_code = $this->DataControl->getFieldOne('smc_code_courseterm', 'courseterm_id', "courseterm_code = '{$paramArray['courseterm_code']}' and company_id = '{$paramArray['company_id']}'");
        if ($courseterm_code) {
            ajax_return(array('error' => 1, 'errortip' => "课程期收编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_courseterm', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加课程期收类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '添加课程期收类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加课程期收类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑课程期收类型
    function updateCoursetermAction($paramArray)
    {
        $CoursetermOne = $this->DataControl->getFieldOne("smc_code_courseterm", "courseterm_id", "courseterm_id = '{$paramArray['courseterm_id']}'");
        if ($CoursetermOne) {
            $data = array();
            $data['courseterm_name'] = $paramArray['courseterm_name'];
            $data['courseterm_code'] = $paramArray['courseterm_code'];
            $data['courseterm_remk'] = $paramArray['courseterm_remk'];

            $field = array();
            $field['courseterm_name'] = "课程期收名称";
            $field['courseterm_code'] = "课程期收编号";
            $field['courseterm_remk'] = "课程期收备注";

            $courseterm_code = $this->DataControl->getFieldOne('smc_code_courseterm', 'courseterm_code', "courseterm_id = '{$paramArray['courseterm_id']}'");
            if ($paramArray['courseterm_code'] != $courseterm_code['courseterm_code']) {
                $courseterm_code = $this->DataControl->getFieldOne('smc_code_courseterm', 'courseterm_id', "courseterm_code = '{$paramArray['courseterm_code']}' and company_id = '{$paramArray['company_id']}'");
                if ($courseterm_code) {
                    ajax_return(array('error' => 1, 'errortip' => "课程期收编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_courseterm", "courseterm_id = '{$paramArray['courseterm_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["$courseterm_code"] = $data;
                $res = array('error' => '0', 'errortip' => "课程期收类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '修改课程期收类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '课程期收类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除课程期收类型
    function delCoursetermAction($paramArray)
    {
        $CoursetermOne = $this->DataControl->getFieldOne("smc_code_courseterm", "courseterm_id", "courseterm_id = '{$paramArray['courseterm_id']}'");
        if ($CoursetermOne) {
            if ($this->DataControl->delData("smc_code_courseterm", "courseterm_id = '{$paramArray['courseterm_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除课程期收类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '删除课程期收类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除课程期收类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //课程管理列表
    function course($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.course_cnname like '%{$paramArray['keyword']}%' or c.course_branch like '%{$paramArray['keyword']}%'  or c.course_shortname like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== "") {
            $datawhere .= " and c.coursetype_id ={$paramArray['coursetype_id']}";
        }
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== "") {
            $datawhere .= " and c.coursecat_id ={$paramArray['coursecat_id']}";
        }
        if (isset($paramArray['course_status']) && $paramArray['course_status'] !== "") {
            $datawhere .= " and c.course_status ={$paramArray['course_status']}";
        }
        if (isset($paramArray['course_isopensonclass']) && $paramArray['course_isopensonclass'] !== "") {
            $datawhere .= " and c.course_isopensonclass ={$paramArray['course_isopensonclass']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $pagestart = 0;
            $num = 10000;
        }


        $sql = "
            SELECT
                c.course_id,c.course_islimittimes,c.course_issesson,
                c.course_upgradesection,
                c.course_cnname,
                c.course_branch,
                c.coursetype_id,
                c.course_isprepare,
                c.coursecat_id,c.course_sellclass,
                c.course_freenums,
                t.coursetype_cnname,
                p.coursecat_cnname,
                c.course_inclasstype,
                c.course_inclasstype as course_inclasstype_name,
                c.course_openclasstype,
                c.course_classnum,
                c.course_presentednums,
                c.course_checkingintype,
                c.course_checkingintype as course_checkingintype_name,
                c.course_checkingminday,
                c.course_selltype,
                c.course_selltype as course_selltype_name,
                c.course_status as course_status_name,
                c.course_status,
                c.course_schedule,
                c.course_schedule as course_schedule_name,
                c.course_isrenew,
                c.course_isrenew as course_isrenew_name,
                c.course_isopensonclass,
                c.course_isopensonclass as course_isopensonclass_name,
                c.course_nextid,
                c.course_islimitamout,
                c.course_limitamout,
                c.course_updatatime,
                c.course_warningnums,
                c.course_presentednums,
                c.course_minabsencenum,
                c.course_classtimes,
                c.course_protocolnums,
                c.course_limitnum,
                c.course_isrenewprice,
                c.course_issubscribe,
                c.course_isintegral,
                c.course_maxintegral,
                c.course_perhour,c.course_monthlyset,
                cc.course_cnname as nextname,
                co.company_iseditimportcourse,
                c.course_isimport,t.coursetype_isopenclass,
                c.course_opensonmode,
                c.course_islimittime,
                c.course_limittime,
                c.course_weeklimittime,
                c.course_weekstandardnum,
                c.course_offline_main_percentage,
                c.course_offline_sub_percentage,
                c.course_online_main_percentage,
                c.course_online_sub_percentage,
                c.course_earlynum,
                c.course_laternum,
                c.course_islimitweeks,
                c.course_issupervise
                ,c.course_isforward
                ,c.course_minclassnum
                ,c.course_isbuildclass
                ,c.course_isgraduated
                ,c.course_isfollow
                ,c.main_course_id
                ,c.course_isopenwarm
                ,c.course_warmnum
                ,c.course_canapplywarm
                ,c.course_isopenreview
                ,c.course_reviewnum
                ,c.course_canapplyreview
                ,c.course_isneedconfirm
                ,c.course_ismustreview
                ,c.course_islimitopencycle
                ,c.course_limitopencyclenum
                ,c.course_islimitwarmtime
                ,c.course_warmtimerange
                ,c.course_islimitreviewtime
                ,c.course_reviewtimerange
                ,c.course_shortname
                ,c.course_isforward_examine
            FROM
                smc_course as c
                left join smc_code_coursecat as p on p.coursecat_id = c.coursecat_id
                left join smc_code_coursetype as t on t.coursetype_id = p.coursetype_id
                left join smc_course as cc on cc.course_id = c.course_nextid
                left join gmc_company as co on co.company_id=c.company_id
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'
            ORDER BY
                c.course_id DESC    
            LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if ($courseList) {
            $status1 = $this->LgArraySwitch(array("0" => "内部销售", "1" => "外部销售", "2" => "同步销售"));
            $status2 = $this->LgArraySwitch(array("-1" => "已下架", "0" => "编辑中", "1" => "生效中"));
            $status3 = $this->LgArraySwitch(array("0" => "课次类课程", "1" => "期度类课程", "2" => "预约类课程", "3" => '公开课课程'));
            $status4 = $this->LgArraySwitch(array("0" => "允许", "1" => "不允许"));
            $status5 = $this->LgArraySwitch(array("0" => "不允许", "1" => "允许"));
            $status6 = $this->LgArraySwitch(array("0" => "课次考勤-缺勤计费", "1" => "连续缺勤", "2" => "自然周考勤", "4" => "累计缺勤", "5" => "课次考勤-缺勤不计费"));
            $status7 = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $status8 = $this->LgArraySwitch(array("0" => "普通公开课", "1" => "试读公开课"));

            foreach ($courseList as &$val) {
                $val['course_selltype_name'] = $status1[$val['course_selltype_name']];
                $val['course_status_name'] = $status2[$val['course_status_name']];
                $val['course_inclasstype_name'] = $status3[$val['course_inclasstype_name']];

                $val['course_openclasstype_name'] = $val['course_inclasstype'] == 3 ? $status8[$val['course_openclasstype']] : '--';

                $val['course_schedule_name'] = $status4[$val['course_schedule_name']];
                $val['course_isopensonclass_name'] = $status5[$val['course_isopensonclass_name']];
                $val['course_checkingintype_name'] = $status6[$val['course_checkingintype_name']];
                $val['course_isrenew_name'] = $status7[$val['course_isrenew_name']];
                if ($val['course_nextid']) {
                    $val['course_nextid'] = explode(",", $val['course_nextid']);
                } else {
                    $val['course_nextid'] = array();
                }
                if ($val['company_iseditimportcourse'] == 0 && $val['course_isimport'] == 1) {
                    $val['can_edit'] = 0;
                } else {
                    $val['can_edit'] = 1;//是否可编辑  0-不可以  1-可以
                }
                $course = $this->DataControl->getFieldOne("smc_class", "class_id", "course_id = '{$val['course_id']}'");
                if ($course) {
                    $val['isbranch'] = '1';
                } else {
                    $val['isbranch'] = '0';
                }

                if ($val['course_limittime']) {
                    $val['course_limittime'] = explode(",", $val['course_limittime']);
                } else {
                    $val['course_limittime'] = array();
                }

                if ($val['coursetype_isopenclass'] == 1) {
                    $val['course_presentednums'] = '--';
                    $val['course_selltype_name'] = '--';
                    $val['course_isrenew_name'] = '--';
                    $val['course_isopensonclass_name'] = '--';
                    $val['course_freenums'] = '--';
                    $val['course_checkingintype_name'] = '--';
                    $val['course_checkingminday'] = '--';
                    $val['course_minabsencenum'] = '--';
                    $val['course_classtimes'] = '--';
                    $val['course_protocolnums'] = '--';
                }

            }
        }


        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.course_id)
            FROM
                smc_course AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('course_cnname', 'course_branch', 'course_shortname', 'coursetype_cnname', 'coursecat_cnname', 'course_inclasstype_name', 'course_openclasstype_name', 'course_classnum', 'course_earlynum', 'course_laternum', 'course_selltype_name', 'course_status_name', 'course_schedule_name', 'course_isrenew_name', 'course_isopensonclass_name', 'course_freenums', 'course_checkingintype_name', 'course_checkingminday', 'course_minabsencenum', 'course_classtimes', 'course_weekstandardnum', 'course_protocolnums', 'course_isneedconfirm', 'course_issupervise');
        $fieldname = $this->LgArraySwitch(array('课程名称', '课程编号', '课程简称', '所属班组', '所属班种', '销售种类', '公开课类型', '实际课次', '前期赠送', '后期赠送', '销售方式', '课程状态', '能否重新排课', '是否留续班', '是否允许创建子班级', '免手续费课时数', '考勤方式', '连续缺勤多少次计费', '累计缺勤多少次计费', '单课次课时数', '周标准课次', '合同数', '是否需教学主管确认开班', '是否监管'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldswitch = array("0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "1", "1");

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            if (!$courseList) {
                $this->error = true;
                $this->errortip = "无课程别数据";
                return false;
            }

            $outexceldate = array();
            if ($courseList) {
                $outexceldate = array();
                foreach ($courseList as $dateexcelvar) {
                    $datearray = array();
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_shortname'] = $dateexcelvar['course_shortname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['course_inclasstype_name'] = $dateexcelvar['course_inclasstype_name'];

                    $datearray['course_openclasstype_name'] = $dateexcelvar['course_inclasstype'] == 3 ? $status8[$dateexcelvar['course_openclasstype']] : '--';
                    $datearray['course_classnum'] = $dateexcelvar['course_classnum'];
                    $datearray['course_earlynum'] = $dateexcelvar['course_earlynum'];
                    $datearray['course_laternum'] = $dateexcelvar['course_laternum'];
                    $datearray['course_selltype_name'] = $dateexcelvar['course_selltype_name'];
                    $datearray['course_status_name'] = $dateexcelvar['course_status_name'];
                    $datearray['course_schedule_name'] = $dateexcelvar['course_schedule_name'];
                    $datearray['course_isrenew_name'] = $dateexcelvar['course_isrenew_name'];
                    $datearray['course_isopensonclass_name'] = $dateexcelvar['course_isopensonclass_name'];
                    $datearray['course_freenums'] = $dateexcelvar['course_freenums'];
                    $datearray['course_checkingintype_name'] = $dateexcelvar['course_checkingintype_name'];
                    $datearray['course_checkingminday'] = $dateexcelvar['course_checkingminday'];
                    $datearray['course_minabsencenum'] = $dateexcelvar['course_minabsencenum'];
                    $datearray['course_classtimes'] = $dateexcelvar['course_classtimes'];
                    $datearray['course_weekstandardnum'] = $dateexcelvar['course_weekstandardnum'];
                    $datearray['course_protocolnums'] = $dateexcelvar['course_protocolnums'];
                    $datearray['course_issupervise'] = $dateexcelvar['course_issupervise'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $fieldname;
            $excelfileds = $fieldstring;
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课程别表.xlsx"));
            exit;
        }

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
            if ($i < 2) {
                $field[$i]["fixed"] = '1';
            } else {
                $field[$i]["fixed"] = '0';
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($courseList) {
            $result['list'] = $courseList;
        } else {
            $result['list'] = array();
        }

        $result['coursetype'] = $this->DataControl->selectClear("select coursetype_id,coursetype_cnname,coursetype_branch from smc_code_coursetype where company_id = '{$paramArray['company_id']}'");

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $result['coursecat'] = $this->DataControl->selectClear("select coursecat_id,coursecat_cnname,coursecat_branch from smc_code_coursecat as cc where company_id = '{$paramArray['company_id']}' and coursetype_id = '{$paramArray['coursetype_id']}'");
        } else {
            $result['coursecat'] = $this->DataControl->selectClear("select coursecat_id,coursecat_cnname,coursecat_branch from smc_code_coursecat as cc where company_id = '{$paramArray['company_id']}'");
        }

        $result['all_num'] = $allnums;

        if ($result['list']) {
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $res = array('error' => '1', 'errortip' => "暂无课程(班别)信息", 'result' => $result);
        }

        return $res;
    }

    //改变监管
    function updateSuperviseAction($paramArray)
    {
        $CourseOne = $this->DataControl->getFieldOne("smc_course", "course_issupervise", "course_id = '{$paramArray['course_id']}'");

        if (!$CourseOne || $CourseOne['course_issupervise'] == '1') {
            if ($paramArray['course_issupervise'] == '0') {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败:关闭请联系管理员', 'result' => $result);
                return $res;
            }
        }
        $data = array();
        $data['course_issupervise'] = $paramArray['course_issupervise'];

        if ($this->DataControl->updateData('smc_course', "course_id = '{$paramArray['course_id']}'", $data)) {
            $result = array();
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "开启成功", 'result' => $result);

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
        }
        return $res;
    }

    function changeIsNeedConfirm($request){

        $courseOne=$this->DataControl->getFieldOne("smc_course","course_isneedconfirm","course_id='{$request['course_id']}'");

        if(!$courseOne){
            $this->error = true;
            $this->errortip = "无对应课程";
            return false;
        }

        $data=array();
        $data['course_isneedconfirm']=1-$courseOne['course_isneedconfirm'];
        $data['course_updatatime']=time();
        if($this->DataControl->updateData("smc_course","course_id='{$request['course_id']}'",$data)){
            $this->oktip = "修改成功";
            return true;
        }else{
            $this->error = true;
            $this->errortip = "修改失败";
            return false;
        }


    }

    function getCourseOne($paramArray)
    {

        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_islimitweeks", "course_id='{$paramArray['course_id']}'");

        if (!$courseOne) {
            $this->error = true;
            $this->errortip = "无对应课程";
            return false;
        }

        $weeksList = $this->DataControl->getList("smc_course_weeks", "course_id='{$courseOne['course_id']}'");

        return $weeksList ? $weeksList : array();

    }

    //编辑课程
    function updateCourseAction($paramArray)
    {
        $CourseOne = $this->DataControl->getFieldOne("smc_course", "course_id,course_inclasstype,course_openclasstype,course_issupervise,course_classnum,main_course_id", "course_id = '{$paramArray['course_id']}'");
        if ($CourseOne) {
            $data = array();
            $data['course_cnname'] = $paramArray['course_cnname'];
            $data['course_shortname'] = $paramArray['course_shortname'];
            $data['course_branch'] = $paramArray['course_branch'];
            $data['coursetype_id'] = $paramArray['coursetype_id'];
            $data['coursecat_id'] = $paramArray['coursecat_id'];
            $data['course_inclasstype'] = $paramArray['course_inclasstype'];

            $data['course_isprepare'] = $paramArray['course_isprepare'];

            if ($paramArray['course_inclasstype'] == 0) {
                $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "course_id='{$paramArray['course_id']}'");
                if ($classOne) {
                    if ($CourseOne['course_classnum'] != $paramArray['course_classnum']) {
                        ajax_return(array('error' => 1, 'errortip' => "存在已使用的班级,不可修改实际课次!"));
                    }
                }
            }

            if (isset($paramArray['course_openclasstype']) && $paramArray['course_openclasstype'] != '') {
                $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "course_id='{$paramArray['course_id']}'");

                if (!$classOne) {
                    $data['course_openclasstype'] = $paramArray['course_openclasstype'];
                } else {
                    if ($CourseOne['course_openclasstype'] != $paramArray['course_openclasstype']) {
                        ajax_return(array('error' => 1, 'errortip' => "存在已使用的班级,不可修改公开课类型!"));
                    }
                }
            }

            if (!$CourseOne || $CourseOne['course_issupervise'] == '1') {
                if ($paramArray['course_issupervise'] == '0') {
                    ajax_return(array('error' => 1, 'errortip' => "改变监管状态失败:关闭请联系管理员!"));
                }
            } else {
                $data['course_issupervise'] = $paramArray['course_issupervise'];
            }

            if ($paramArray['course_limittime'] && $paramArray['course_limittime'] !== '') {
                $limitlist = explode(",", $paramArray['course_limittime']);

                $newlimitlist = array_unique($limitlist);
                $newlimitstr = implode(",", $newlimitlist);
                $paramArray['course_limittime'] = $newlimitstr;
            }

            $data['course_classnum'] = $paramArray['course_classnum'];
            $data['course_upgradesection'] = $paramArray['course_upgradesection'];
            $data['course_presentednums'] = $paramArray['course_presentednums'];
            $data['course_selltype'] = $paramArray['course_selltype'];
            $data['course_sellclass'] = $paramArray['course_sellclass'];
            $data['course_nextid'] = $paramArray['course_nextid'];
            $data['course_warningnums'] = $paramArray['course_warningnums'];
            $data['course_refundprice'] = $paramArray['course_refundprice'];
            $data['course_status'] = $paramArray['course_status'];
            $data['course_schedule'] = $paramArray['course_schedule'];
            $data['course_freenums'] = $paramArray['course_freenums'];
            $data['course_checkingintype'] = $paramArray['course_checkingintype'];
            $data['course_checkingminday'] = $paramArray['course_checkingminday'];
            $data['course_classtimes'] = $paramArray['course_classtimes'];
            $data['course_isopensonclass'] = $paramArray['course_isopensonclass'];
            $data['course_minabsencenum'] = $paramArray['course_minabsencenum'];
            $data['course_protocolnums'] = $paramArray['course_protocolnums'];
            $data['course_limitnum'] = $paramArray['course_limitnum'];
            $data['course_isrenewprice'] = $paramArray['course_isrenewprice'];
            $data['course_issubscribe'] = $paramArray['course_issubscribe'];
            $data['course_isrenew'] = $paramArray['course_isrenew'];
            $data['course_isinterval'] = $paramArray['course_isinterval'];
            $data['course_islimitamout'] = $paramArray['course_islimitamout'];
            $data['course_limitamout'] = $paramArray['course_limitamout'];
            $data['course_opensonmode'] = $paramArray['course_opensonmode'];
            $data['course_islimittime'] = $paramArray['course_islimittime'];
            $data['course_limittime'] = $paramArray['course_limittime'];
            $data['course_weeklimittime'] = $paramArray['course_weeklimittime'];
            $data['course_weekstandardnum'] = $paramArray['course_weekstandardnum'];
            $data['course_offline_main_percentage'] = $paramArray['course_offline_main_percentage'];
            $data['course_offline_sub_percentage'] = $paramArray['course_offline_sub_percentage'];
            $data['course_online_main_percentage'] = $paramArray['course_online_main_percentage'];
            $data['course_online_sub_percentage'] = $paramArray['course_online_sub_percentage'];
            $data['course_perhour'] = $paramArray['course_perhour'];
            $data['course_earlynum'] = $paramArray['course_earlynum'];
            $data['course_laternum'] = $paramArray['course_laternum'];
            $data['course_islimitweeks'] = $paramArray['course_islimitweeks'];
            $data['course_isintegral'] = $paramArray['course_isintegral'];
            $data['course_maxintegral'] = $paramArray['course_maxintegral'];
            $data['course_islimittimes'] = $paramArray['course_islimittimes'];
            $data['course_issesson'] = $paramArray['course_issesson'];

            if (isset($paramArray['course_monthlyset']) && $paramArray['course_monthlyset'] != '') {
                $data['course_monthlyset'] = $paramArray['course_monthlyset'];
            }
            if (isset($paramArray['course_isforward']) && $paramArray['course_isforward'] != '') {
                $data['course_isforward'] = $paramArray['course_isforward'];
                if ($paramArray['course_isforward']==1 && isset($paramArray['course_isforward_examine']) && $paramArray['course_isforward_examine'] != '') {
                    $data['course_isforward_examine'] = $paramArray['course_isforward_examine'];
                }
            }
            if (isset($paramArray['course_minclassnum']) && $paramArray['course_minclassnum'] != '') {
                $data['course_minclassnum'] = $paramArray['course_minclassnum'];
            }

            if (isset($paramArray['course_isopenwarm']) && $paramArray['course_isopenwarm'] != '') {
                $data['course_isopenwarm'] = $paramArray['course_isopenwarm'];
            }

            if (isset($paramArray['course_warmnum']) && $paramArray['course_warmnum'] != '') {
                $data['course_warmnum'] = $paramArray['course_warmnum'];
            }

            if (isset($paramArray['course_canapplywarm']) && $paramArray['course_canapplywarm'] != '') {
                $data['course_canapplywarm'] = $paramArray['course_canapplywarm'];
            }

            if (isset($paramArray['course_isopenreview']) && $paramArray['course_isopenreview'] != '') {
                $data['course_isopenreview'] = $paramArray['course_isopenreview'];
            }

            if(isset($paramArray['course_ismustreview']) && $paramArray['course_ismustreview'] != ''  && $paramArray['course_ismustreview']==1){
                if (isset($paramArray['course_reviewnum']) && $paramArray['course_reviewnum'] != '' && $paramArray['course_reviewnum']>0) {
                    $data['course_ismustreview'] = $paramArray['course_ismustreview'];
                }else{
                    ajax_return(array('error' => 1, 'errortip' => "请填写复习课次数!"));
                }
            }

            if (isset($paramArray['course_reviewnum']) && $paramArray['course_reviewnum'] != '') {
                $data['course_reviewnum'] = $paramArray['course_reviewnum'];
            }

            if (isset($paramArray['course_canapplyreview']) && $paramArray['course_canapplyreview'] != '') {
                $data['course_canapplyreview'] = $paramArray['course_canapplyreview'];
            }

            if (isset($paramArray['course_isneedconfirm']) && $paramArray['course_isneedconfirm'] != '') {
                $data['course_isneedconfirm'] = $paramArray['course_isneedconfirm'];
            }

            if (isset($paramArray['course_islimitopencycle']) && $paramArray['course_islimitopencycle'] != '' && $paramArray['course_islimitopencycle']==1) {
                $data['course_islimitopencycle'] = $paramArray['course_islimitopencycle'];
                if (isset($paramArray['course_limitopencyclenum']) && $paramArray['course_limitopencyclenum'] != '' && $paramArray['course_limitopencyclenum']>0) {
                    $data['course_limitopencyclenum'] = $paramArray['course_limitopencyclenum'];
                }else{
                    ajax_return(array('error' => 1, 'errortip' => "请填写开班最大天数!"));
                }
            }

            if($CourseOne['main_course_id']==0){

                if (isset($paramArray['course_isfollow']) && $paramArray['course_isfollow'] != '') {
                    $data['course_isfollow'] = $paramArray['course_isfollow'];
                }

                if (isset($paramArray['main_course_id']) && $paramArray['main_course_id'] != '') {

                    if($paramArray['main_course_id']==$paramArray['course_id']){
                        ajax_return(array('error' => 1, 'errortip' => "主课程id不可和当前课程重复"));
                    }else{
                        $data['main_course_id'] = $paramArray['main_course_id'];
                    }
                }
            }

            if (isset($paramArray['course_islimitwarmtime']) && $paramArray['course_islimitwarmtime'] != '' && $paramArray['course_islimitwarmtime']==1) {

                if (isset($paramArray['course_warmtimerange']) && $paramArray['course_warmtimerange'] != '') {

                    $data['course_warmtimerange'] = $paramArray['course_warmtimerange'];
                }else{
                    ajax_return(array('error' => 1, 'errortip' => "请填写排课分钟数范围"));
                }

                $data['course_islimitwarmtime']=1;
            }else{
                $data['course_warmtimerange'] = '';
            }

            if (isset($paramArray['course_islimitreviewtime']) && $paramArray['course_islimitreviewtime'] != '' && $paramArray['course_islimitreviewtime']==1) {

                if (isset($paramArray['course_reviewtimerange']) && $paramArray['course_reviewtimerange'] != '') {

                    $data['course_reviewtimerange'] = $paramArray['course_reviewtimerange'];
                }else{
                    ajax_return(array('error' => 1, 'errortip' => "请填写排课分钟数范围"));
                }

                $data['course_islimitreviewtime']=1;
            }else{
                $data['course_reviewtimerange'] = '';
            }

            $data['course_isbuildclass'] = $paramArray['course_isbuildclass'];
            $data['course_isgraduated'] = $paramArray['course_isgraduated'];

            $data['course_updatatime'] = time();

            $field = array();
            $field['course_cnname'] = "课程名称";
            $field['course_branch'] = "课程编号";
            $field['coursetype_id'] = "所属课程科目";
            $field['coursecat_id'] = "所属课程分类";
            $field['course_inclasstype'] = "上课方式";
            $field['course_classnum'] = "实际课时数";
            $field['course_presentednums'] = "赠送课时数";
            $field['course_selltype'] = "销售方式";
            $field['course_nextid'] = "下一级别课程";
            $field['course_warningnums'] = "课程是否预警课程余额数";
            $field['course_refundprice'] = "退费手续费";
            $field['course_status'] = "课程状态";
            $field['course_schedule'] = "是否允许重新排课";
            $field['course_freenums'] = "免手续费课时数";
            $field['course_checkingintype'] = "考勤方式";
            $field['course_checkingminday'] = "连续缺勤多少次计费";
            $field['course_classtimes'] = "单课次课时数";
            $field['course_isopensonclass'] = "是否允许创建子班";
            $field['course_opensonmode'] = "子班上课模式限制";
            $field['course_islimittime'] = "是否限制上课时长";
            $field['course_limittime'] = "上课时长";
            $field['course_weeklimittime'] = '周最大排课总分钟数';
            $field['course_weekstandardnum'] = '周排课标准课次';
            $field['course_offline_main_percentage'] = "实体课主教课时比例";
            $field['course_offline_sub_percentage'] = "实体课助教课时比例";
            $field['course_online_main_percentage'] = "线上课主教课时比例";
            $field['course_online_sub_percentage'] = "线上课助教课时比例";
            $field['course_updatatime'] = "更新时间";
            $field['course_isbuildclass'] = "是否允许建班";

            $course_branch = $this->DataControl->getFieldOne('smc_course', 'course_branch', "course_id = '{$paramArray['course_id']}'");
            if ($paramArray['course_branch'] != $course_branch['course_branch']) {
                $course_branch = $this->DataControl->getFieldOne('smc_course', 'course_id', "course_branch = '{$paramArray['course_branch']}' and company_id = '{$paramArray['company_id']}'");
                if ($course_branch) {
                    ajax_return(array('error' => 1, 'errortip' => "课程编号已存在!"));
                }
            }
            //var_dump($data);die;
            if ($this->DataControl->updateData("smc_course", "course_id = '{$paramArray['course_id']}'", $data)) {

                if (isset($paramArray['course_islimitweeks']) && $paramArray['course_islimitweeks'] == 1) {

                    $weeksList = json_decode(stripslashes($paramArray['weeksList']), 1);

                    if ($weeksList) {
                        $this->DataControl->delData("smc_course_weeks", "course_id='{$paramArray['course_id']}'");
                        foreach ($weeksList as $weeksOne) {
                            $tem_data = array();
                            $tem_data['course_id'] = $paramArray['course_id'];
                            $tem_data['weeks_branch'] = $weeksOne['weeks_branch'];
                            $this->DataControl->insertData("smc_course_weeks", $tem_data);
                        }
                    }
                }

                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "课程修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '编辑课程', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '课程修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //添加课程
    function addCourseAction($paramArray)
    {
        $data = array();
        $data['course_isprepare'] = $paramArray['course_isprepare'];
        $data['course_cnname'] = $paramArray['course_cnname'];
        $data['course_shortname'] = $paramArray['course_shortname'];
        $data['course_upgradesection'] = $paramArray['course_upgradesection'];
        $data['course_branch'] = $paramArray['course_branch'];
        $data['coursecat_id'] = $paramArray['coursecat_id'];
        $data['course_inclasstype'] = $paramArray['course_inclasstype'];
        $data['course_openclasstype'] = $paramArray['course_openclasstype'];
        $data['course_classnum'] = $paramArray['course_classnum'];
        $data['course_selltype'] = $paramArray['course_selltype'];
        $data['course_sellclass'] = $paramArray['course_sellclass'];
        $data['course_status'] = $paramArray['course_status'];
        $data['course_nextid'] = $paramArray['course_nextid'];
        $data['course_presentednums'] = $paramArray['course_presentednums'];
        $data['course_warningnums'] = $paramArray['course_warningnums'];
        $data['course_refundprice'] = $paramArray['course_refundprice'];
        $data['course_schedule'] = $paramArray['course_schedule'];
        $data['course_freenums'] = $paramArray['course_freenums'];
        $data['course_checkingintype'] = $paramArray['course_checkingintype'];
        $data['course_checkingminday'] = $paramArray['course_checkingminday'];
        $data['course_classtimes'] = $paramArray['course_classtimes'];
        $data['course_isopensonclass'] = $paramArray['course_isopensonclass'];
        $data['course_minabsencenum'] = $paramArray['course_minabsencenum'];
        $data['course_protocolnums'] = $paramArray['course_protocolnums'];
        $data['course_issupervise'] = $paramArray['course_issupervise'];
        $data['course_isrenew'] = $paramArray['course_isrenew'];
        $data['course_limitnum'] = $paramArray['course_limitnum'];
        $data['course_isrenewprice'] = $paramArray['course_isrenewprice'];
        $data['course_issubscribe'] = $paramArray['course_issubscribe'];
        $data['course_isinterval'] = $paramArray['course_isinterval'];
        $data['course_islimitamout'] = $paramArray['course_islimitamout'];
        $data['course_limitamout'] = $paramArray['course_limitamout'];
        $data['course_opensonmode'] = $paramArray['course_opensonmode'];
        $data['course_islimittime'] = $paramArray['course_islimittime'];
        $data['course_limittime'] = $paramArray['course_limittime'];
        $data['course_weeklimittime'] = $paramArray['course_weeklimittime'];
        $data['course_weekstandardnum'] = $paramArray['course_weekstandardnum'];
        $data['course_offline_main_percentage'] = $paramArray['course_offline_main_percentage'];
        $data['course_offline_sub_percentage'] = $paramArray['course_offline_sub_percentage'];
        $data['course_online_main_percentage'] = $paramArray['course_online_main_percentage'];
        $data['course_online_sub_percentage'] = $paramArray['course_online_sub_percentage'];
        $data['course_perhour'] = $paramArray['course_perhour'];
        $data['course_earlynum'] = $paramArray['course_earlynum'];
        $data['course_laternum'] = $paramArray['course_laternum'];
        $data['course_islimitweeks'] = $paramArray['course_islimitweeks'];
        $data['course_isintegral'] = $paramArray['course_isintegral'];
        $data['course_maxintegral'] = $paramArray['course_maxintegral'];
        $data['course_islimittimes'] = $paramArray['course_islimittimes'];
        $data['course_issesson'] = $paramArray['course_issesson'];
        if (isset($paramArray['course_monthlyset']) && $paramArray['course_monthlyset'] != '') {
            $data['course_monthlyset'] = $paramArray['course_monthlyset'];
        }
        if (isset($paramArray['course_isforward']) && $paramArray['course_isforward'] != '') {
            $data['course_isforward'] = $paramArray['course_isforward'];

            if ($paramArray['course_isforward']==1 && isset($paramArray['course_isforward_examine']) && $paramArray['course_isforward_examine'] != '') {
                $data['course_isforward_examine'] = $paramArray['course_isforward_examine'];
            }
    

        }
        if (isset($paramArray['course_minclassnum']) && $paramArray['course_minclassnum'] != '') {
            $data['course_minclassnum'] = $paramArray['course_minclassnum'];
        }

        if (isset($paramArray['course_isfollow']) && $paramArray['course_isfollow'] != '') {
            $data['course_isfollow'] = $paramArray['course_isfollow'];
        }

        if (isset($paramArray['main_course_id']) && $paramArray['main_course_id'] != '') {
            $data['main_course_id'] = $paramArray['main_course_id'];
        }

        if (isset($paramArray['course_isopenwarm']) && $paramArray['course_isopenwarm'] != '') {
            $data['course_isopenwarm'] = $paramArray['course_isopenwarm'];
        }

        if (isset($paramArray['course_warmnum']) && $paramArray['course_warmnum'] != '') {
            $data['course_warmnum'] = $paramArray['course_warmnum'];
        }

        if (isset($paramArray['course_canapplywarm']) && $paramArray['course_canapplywarm'] != '') {
            $data['course_canapplywarm'] = $paramArray['course_canapplywarm'];
        }

        if (isset($paramArray['course_isopenreview']) && $paramArray['course_isopenreview'] != '') {
            $data['course_isopenreview'] = $paramArray['course_isopenreview'];
        }

        if(isset($paramArray['course_ismustreview']) && $paramArray['course_ismustreview'] != '' && $paramArray['course_ismustreview'] ==1){
            if (isset($paramArray['course_reviewnum']) && $paramArray['course_reviewnum'] != '' && $paramArray['course_reviewnum']>0) {
                $data['course_ismustreview'] = $paramArray['course_ismustreview'];
            }else{
                ajax_return(array('error' => 1, 'errortip' => "请填写复习课次数!"));
            }
        }

        if (isset($paramArray['course_reviewnum']) && $paramArray['course_reviewnum'] != '') {
            $data['course_reviewnum'] = $paramArray['course_reviewnum'];
        }

        if (isset($paramArray['course_canapplyreview']) && $paramArray['course_canapplyreview'] != '') {
            $data['course_canapplyreview'] = $paramArray['course_canapplyreview'];
        }

        if (isset($paramArray['course_isneedconfirm']) && $paramArray['course_isneedconfirm'] != '') {
            $data['course_isneedconfirm'] = $paramArray['course_isneedconfirm'];
        }

        if (isset($paramArray['course_islimitopencycle']) && $paramArray['course_islimitopencycle'] != '' && $paramArray['course_islimitopencycle']==1) {
            $data['course_islimitopencycle'] = $paramArray['course_islimitopencycle'];
            if (isset($paramArray['course_limitopencyclenum']) && $paramArray['course_limitopencyclenum'] != '' && $paramArray['course_limitopencyclenum']>0) {
                $data['course_limitopencyclenum'] = $paramArray['course_limitopencyclenum'];
            }else{
                ajax_return(array('error' => 1, 'errortip' => "请填写开班最大天数!"));
            }
        }

        if (isset($paramArray['course_islimitwarmtime']) && $paramArray['course_islimitwarmtime'] != '' && $paramArray['course_islimitwarmtime']==1) {

            if (isset($paramArray['course_warmtimerange']) && $paramArray['course_warmtimerange'] != '') {

                $data['course_warmtimerange'] = $paramArray['course_warmtimerange'];
            }else{
                ajax_return(array('error' => 1, 'errortip' => "请填写排课分钟数范围"));
            }

            $data['course_islimitwarmtime']=1;
        }

        if (isset($paramArray['course_islimitreviewtime']) && $paramArray['course_islimitreviewtime'] != '' && $paramArray['course_islimitreviewtime']==1) {

            if (isset($paramArray['course_reviewtimerange']) && $paramArray['course_reviewtimerange'] != '') {

                $data['course_reviewtimerange'] = $paramArray['course_reviewtimerange'];
            }else{
                ajax_return(array('error' => 1, 'errortip' => "请填写排课分钟数范围"));
            }

            $data['course_islimitreviewtime']=1;
        }



        $data['course_createtime'] = time();
        $data['company_id'] = $paramArray['company_id'];
        $coursetype_id = $this->DataControl->getFieldOne('smc_code_coursecat', 'coursetype_id', "coursecat_id = '{$paramArray['coursecat_id']}'");
        $data['coursetype_id'] = $coursetype_id['coursetype_id'];

        $data['course_isbuildclass'] = $paramArray['course_isbuildclass'];//是否允许建班   0 否 1 是 230220
        $data['course_isgraduated'] = $paramArray['course_isgraduated'];//是否允许建班   0 否 1 是 230220

        $field = array();
        $field['course_cnname'] = "课程名称";
        $field['course_branch'] = "课程编号";
        $field['coursetype_id'] = "所属课程科目";
        $field['coursecat_id'] = "所属课程分类";
        $field['course_inclasstype'] = "上课方式";
        $field['course_classnum'] = "实际课时数";
        $field['course_selltype'] = "销售方式";
        $field['course_status'] = "课程状态";
        $field['course_nextid'] = "下一级别课程";
        $field['course_presentednums'] = "赠送课时数";
        $field['course_warningnums'] = "课程是否预警课程余额数";
        $field['course_refundprice'] = "退费手续费";
        $field['course_schedule'] = "是否允许重新排课";
        $field['course_freenums'] = "免手续费课时数";
        $field['course_checkingintype'] = "考勤方式";
        $field['course_checkingminday'] = "连续缺勤多少次计费";
        $field['course_classtimes'] = "单课次课时数";
        $field['course_createtime'] = "创建时间";
        $field['course_isopensonclass'] = "是否允许创建子班";
        $field['course_opensonmode'] = "子班上课模式限制";
        $field['course_islimittime'] = "是否限制上课时长";
        $field['course_limittime'] = "上课时长";
        $field['course_weeklimittime'] = "周最大排课总分钟数";
        $field['course_weekstandardnum'] = "周排课标准课次";
        $field['course_offline_main_percentage'] = "实体课主教课时比例";
        $field['course_offline_sub_percentage'] = "实体课助教课时比例";
        $field['course_online_main_percentage'] = "线上课主教课时比例";
        $field['course_online_sub_percentage'] = "线上课助教课时比例";
        $field['company_id'] = "所属集团";
        $field['course_isbuildclass'] = "是否允许建班";
        $field['course_isgraduated'] = "是否为毕业班";
        $field['course_isprepare'] = "是否允许设置预备班";

        $course_branch = $this->DataControl->getFieldOne('smc_course', 'course_id', "course_branch = '{$paramArray['course_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($course_branch) {
            ajax_return(array('error' => 1, 'errortip' => "课程编号已存在!"));
        }

        $course_id = $this->DataControl->insertData('smc_course', $data);

        if ($course_id) {

            if (isset($paramArray['course_islimitweeks']) && $paramArray['course_islimitweeks'] == 1) {

                $weeksList = json_decode(stripslashes($paramArray['weeksList']), 1);
                if (!$this->DataControl->getOne("smc_course_weeks", "course_id='{$course_id}'")) {
                    if ($weeksList) {
                        foreach ($weeksList as $weeksOne) {
                            $tem_data = array();
                            $tem_data['course_id'] = $course_id;
                            $tem_data['weeks_branch'] = $weeksOne['weeks_branch'];
                            $this->DataControl->insertData("smc_course_weeks", $tem_data);
                        }
                    }
                }

            }

            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加课程成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '添加课程', dataEncode($paramArray));
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加课程失败', 'result' => $result);
        }
        return $res;
    }

    //删除课程
    function delCourseAction($paramArray)
    {
        $courseOne = $this->DataControl->getFieldOne("smc_course", "course_id", "course_id = '{$paramArray['course_id']}'");
        if ($courseOne) {
            $a = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "course_id = '{$paramArray['course_id']}'");
            $b = $this->DataControl->getFieldOne("smc_class", "class_id", "course_id = '{$paramArray['course_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该课程有协议，不可删除"));
            }
            if ($b) {
                ajax_return(array('error' => 1, 'errortip' => "该课程已安排班级，不可删除"));
            }
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_course", "course_id = '{$paramArray['course_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除课程成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '删除课程', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除课程失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //根据班种获取班组
    function getCoursetypeApi($paramArray)
    {
        $coursetype = $this->DataControl->getFieldOne("smc_code_coursecat", "coursetype_id", "coursecat_id = '{$paramArray['coursecat_id']}'");
        $coursetypename = $this->DataControl->getFieldOne("smc_code_coursetype", "coursetype_cnname,coursetype_isopenclass", "coursetype_id = '{$coursetype['coursetype_id']}'");

        $result = array();
        $result['coursetype_id'] = $coursetype['coursetype_id'];
        $result['coursetype_cnname'] = $coursetypename['coursetype_cnname'];
        $result['coursetype_isopenclass'] = $coursetypename['coursetype_isopenclass'];
        $res = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);

        return $res;
    }

    //根据班种获取班级
    function getCoursecatApi($paramArray)
    {

        $courseOne = $this->DataControl->getFieldOne("smc_course", "coursetype_id", "course_id='{$paramArray['course_id']}'");

        if (!$courseOne) {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '无该班别', 'result' => $result);
            return $res;
        }

        $courseList = $this->DataControl->selectClear("SELECT c.course_cnname,c.course_id,c.course_branch from smc_course as c
            WHERE c.coursetype_id = '{$courseOne['coursetype_id']}' and c.course_id <> '{$paramArray['course_id']}' and c.company_id ='{$paramArray['company_id']}'");

        if ($courseList) {
            foreach ($courseList as $key => $val) {
                $courseList[$key]['course_cnname'] = $val['course_cnname'] . '(' . $val['course_branch'] . ')';
            }
        }
        $result = array();
        if ($courseList) {
            $result["data"] = $courseList;
            $res = array('error' => '0', 'errortip' => '根据班种获取班级成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '根据班种获取班级失败', 'result' => $result);
        }
        return $res;
    }

    //课时时间设置列表
    function coursetimesList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.coursetimes_cnname like '%{$paramArray['keyword']}%' or c.coursetimes_nums like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                c.coursetimes_id,
                c.coursetimes_cnname,
                c.coursetimes_nums
            FROM
                smc_code_coursetimes AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'
            ORDER BY
                c.coursetimes_id DESC    
            LIMIT {$pagestart},{$num}";

        $coursetimesList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(c.coursetimes_id)
            FROM
                smc_code_coursetimes AS c
            WHERE
                {$datawhere} and c.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('coursetimes_cnname ', 'coursetimes_nums');
        $fieldname = $this->LgArraySwitch(array('课时时间分数名称', '分钟数'));
        $fieldcustom = array("1", "1");
        $fieldshow = array("1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($coursetimesList) {
            $result['list'] = $coursetimesList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程时数类型", 'result' => $result);
        }

        return $res;
    }

    /// 获取排课的时间限制
    function getCourseLimitTimeApi($paramArray)
    {

        $dataList = $this->DataControl->selectClear("select coursetimes_cnname,coursetimes_nums from smc_code_coursetimes where company_id='{$paramArray['company_id']}' ");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    //添加课时时间设置
    function addCoursetimesAction($paramArray)
    {
        $data = array();
        $data['coursetimes_cnname'] = $paramArray['coursetimes_cnname'];
        $data['coursetimes_nums'] = $paramArray['coursetimes_nums'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['coursetimes_cnname'] = "课时时间分数名称";
        $field['coursetimes_nums'] = "分钟数";
        $field['company_id'] = "所属集团";

        if ($this->DataControl->insertData('smc_code_coursetimes', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加课时时间设置成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '添加课时时间设置', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加课时时间设置失败', 'result' => $result);
        }
        return $res;
    }

    //编辑课时时间设置
    function updateCoursetimesAction($paramArray)
    {
        $CoursetermOne = $this->DataControl->getFieldOne("smc_code_coursetimes", "coursetimes_id", "coursetimes_id = '{$paramArray['coursetimes_id']}'");
        if ($CoursetermOne) {
            $data = array();
            $data['coursetimes_cnname'] = $paramArray['coursetimes_cnname'];
            $data['coursetimes_nums'] = $paramArray['coursetimes_nums'];

            $field = array();
            $field['coursetimes_cnname'] = "课时时间分数名称";
            $field['coursetimes_nums'] = "分钟数";

            if ($this->DataControl->updateData("smc_code_coursetimes", "coursetimes_id = '{$paramArray['coursetimes_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "课时时间设置修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '编辑课时时间', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '课时时间设置修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除课时时间设置
    function delCoursetimesAction($paramArray)
    {
        $CoursetermOne = $this->DataControl->getFieldOne("smc_code_coursetimes", "coursetimes_id", "coursetimes_id = '{$paramArray['coursetimes_id']}'");
        if ($CoursetermOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_coursetimes", "coursetimes_id = '{$paramArray['coursetimes_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除课时时间设置成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->课程相关设置", '删除课时时间', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除课时时间设置失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除游戏时间
    function getCourseAction()
    {
        $this->DataControl->getFieldOne("", "", "");

    }

    //启用/不启用
    function OpenOnClassAction($paramArray)
    {
        $activityOne = $this->DataControl->getFieldOne("smc_course", "course_id", "course_id = '{$paramArray['course_id']}'");
        if ($activityOne) {
            $data = array();
            $data['course_isopensonclass'] = $paramArray['course_isopensonclass'];

            $field = array();
            $field['course_isopensonclass'] = '是否允许创建子班：0不允许1允许';

            if ($this->DataControl->updateData("smc_course", "course_id = '{$paramArray['course_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "改变状态成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '改变状态失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //获取电访课次区间列表
    function getIntervalList($paramArray)
    {
        $sql = "SELECT * FROM eas_course_interval WHERE course_id = '{$paramArray['course_id']}'";

        $datalist = $this->DataControl->selectClear($sql);
        if ($datalist) {
            foreach ($datalist as &$v) {
                $interval_scope = json_decode($v['interval_scope'], true);
                $first = current($interval_scope);
                $end = end($interval_scope);
                $v['scope_name'] = $first['hour_lessontimes'] . '至' . $end['hour_lessontimes'];
            }
        }

        $hourList = $this->DataControl->selectClear("SELECT hour_id,hour_lessontimes FROM smc_class_hour WHERE course_id = '{$paramArray['course_id']}'");

        $result = array();
        if ($datalist) {
            $result["hour"] = $hourList;
            $result["data"] = $datalist;
            $res = array('error' => '0', 'errortip' => '获取电访课次区间列表成功', 'result' => $result);
        } else {
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '暂无电访课次区间列表数据', 'result' => $result);
        }
        return $res;
    }

    //添加电访课次区间
    function AddIntervalAction($paramArray)
    {

        $data = array();
        $data['course_id'] = $paramArray['course_id'];
        $data['interval_name'] = $paramArray['interval_name'];
        $data['interval_scope'] = $paramArray['interval_scope'];
        $data['interval_note'] = $paramArray['interval_note'];
        $data['interval_addtime'] = time();

        $result = array();
        if ($this->DataControl->insertData("eas_course_interval", $data)) {
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加成功", 'result' => $result);
        } else {
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加成功", 'result' => $result);
        }
        return $res;
    }

    //编辑电访课次区间
    function EditIntervalAction($paramArray)
    {
        $intervalOne = $this->DataControl->getFieldOne("eas_course_interval", "interval_id", "interval_id = '{$paramArray['interval_id']}'");
        if ($intervalOne) {
            $data = array();
            $data['course_id'] = $paramArray['course_id'];
            $data['interval_name'] = $paramArray['interval_name'];
            $data['interval_scope'] = $paramArray['interval_scope'];
            $data['interval_note'] = $paramArray['interval_note'];

            $result = array();
            if ($this->DataControl->updateData("eas_course_interval", "interval_id = '{$paramArray['interval_id']}'", $data)) {
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑成功", 'result' => $result);
            } else {
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑成功", 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除电访课次区间
    function DelIntervalAction($paramArray)
    {
        $intervalOne = $this->DataControl->getFieldOne("eas_course_interval", "interval_id", "interval_id = '{$paramArray['interval_id']}'");
        if ($intervalOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            $result = array();
            if ($this->DataControl->delData("eas_course_interval", "interval_id = '{$paramArray['interval_id']}'")) {
                $res = array('error' => '0', 'errortip' => "删除电访课次区间成功", 'result' => $result);
            } else {
                $res = array('error' => '1', 'errortip' => '删除电访课次区间失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //班外课时类型列表
    function outclasstype($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.outclasstype_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['outclasstype_code']) && $paramArray['outclasstype_code'] !== '') {
            $datawhere .= " and p.outclasstype_code='{$paramArray['outclasstype_code']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.outclasstype_id,
                p.outclasstype_name,
                p.outclasstype_code,
                p.outclasstype_rate as outclasstype_rate_name,
                p.outclasstype_remk,
                p.outclasstype_rate
            FROM
                smc_code_outclasstype AS p
            WHERE
                {$datawhere} and p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.outclasstype_id DESC    
            LIMIT {$pagestart},{$num}";

        $CommodeList = $this->DataControl->selectClear($sql);
        $outclasstype_code = $this->LgArraySwitch(array("0" => "教学时数", "1" => "其它时数"));

        if ($CommodeList) {
            foreach ($CommodeList as &$val) {
                if (!$val['outclasstype_remk']) {
                    $val['outclasstype_remk'] = '--';
                }
                $val['outclasstype_code'] = $outclasstype_code[$val['outclasstype_code']];
                $val['outclasstype_rate'] = $val['outclasstype_rate'];
                $val['outclasstype_rate_name'] = '1：' . $val['outclasstype_rate_name'];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT COUNT(p.outclasstype_id)
            FROM smc_code_outclasstype AS p
            WHERE {$datawhere} 
            and p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('outclasstype_name', 'outclasstype_code', 'outclasstype_rate_name', 'outclasstype_remk');
        $fieldname = $this->LgArraySwitch(array('课时类型', '课时分类', '课时比例', '备注'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($CommodeList) {
            $result['list'] = $CommodeList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无班外课时类型", 'result' => $result);
        }

        return $res;
    }

    //添加班外课时类型
    function addOutclasstypeAction($paramArray)
    {
        $data = array();
        $data['outclasstype_name'] = $paramArray['outclasstype_name'];
        $data['outclasstype_code'] = $paramArray['outclasstype_code'];
        $data['outclasstype_rate'] = $paramArray['outclasstype_rate'];
        $data['outclasstype_remk'] = $paramArray['outclasstype_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['outclasstype_name'] = "课时类型";
        $field['outclasstype_code'] = "课时分类";
        $field['outclasstype_rate'] = "课时比例";
        $field['outclasstype_remk'] = "备注";

        $commode_name = $this->DataControl->getFieldOne('smc_code_outclasstype', 'outclasstype_id', "outclasstype_name = '{$paramArray['outclasstype_name']}' and company_id = '{$paramArray['company_id']}'");
        if ($commode_name) {
            ajax_return(array('error' => 1, 'errortip' => "班外课时类型已存在!"), $this->companyOne['company_language']);
        }

        if ($this->DataControl->insertData('smc_code_outclasstype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加班外课时类型成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加班外课时类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑班外课时类型
    function updateOutclasstypeAction($paramArray)
    {
        $TeachingtypeOne = $this->DataControl->getFieldOne("smc_code_outclasstype", "outclasstype_id", "outclasstype_id = '{$paramArray['outclasstype_id']}'");
        if ($TeachingtypeOne) {
            $data = array();
            $data['outclasstype_name'] = $paramArray['outclasstype_name'];
            $data['outclasstype_code'] = $paramArray['outclasstype_code'];
            $data['outclasstype_rate'] = $paramArray['outclasstype_rate'];
            $data['outclasstype_remk'] = $paramArray['outclasstype_remk'];

            $field = array();
            $field['outclasstype_name'] = "课时类型";
            $field['outclasstype_code'] = "课时类别";
            $field['outclasstype_rate'] = "课时比例";
            $field['outclasstype_remk'] = "备注";

            $commode_name = $this->DataControl->getFieldOne('smc_code_outclasstype', 'outclasstype_name', "outclasstype_id = '{$paramArray['outclasstype_id']}'");
            if ($paramArray['outclasstype_name'] != $commode_name['outclasstype_name']) {
                $commode_name = $this->DataControl->getFieldOne('smc_code_outclasstype', 'outclasstype_id', "outclasstype_name = '{$paramArray['outclasstype_name']}'  and company_id = '{$paramArray['company_id']}'");
                if ($commode_name) {
                    ajax_return(array('error' => 1, 'errortip' => "班外课时类型已存在!"), $this->companyOne['company_language']);
                }
            }

            if ($this->DataControl->updateData("smc_code_outclasstype", "outclasstype_id = '{$paramArray['outclasstype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "班外课时类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '班外课时类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除班外课时类型
    function delOutclasstypeAction($paramArray)
    {
        $CommodeOne = $this->DataControl->getFieldOne("smc_code_outclasstype", "outclasstype_id", "outclasstype_id = '{$paramArray['outclasstype_id']}'");
        if ($CommodeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("smc_outclass_hour", "hour_id", "outclasstype_id = '{$CommodeOne['outclasstype_id']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该班外课时类型已被使用，无法删除！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("smc_code_outclasstype", "outclasstype_id = '{$paramArray['outclasstype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除班外课时类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除班外课时类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    function getMonthdisList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['monthdis_month']) && $request['monthdis_month'] !== '') {
            $datawhere .= " and (cm.monthdis_month ='{$request['monthdis_month']}')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and ((cm.monthdis_applyschool=1 and exists(select sc.schoolapply_id from gmc_monthdis_schoolapply as sc where sc.monthdis_id=cm.monthdis_id and sc.school_id='{$request['school_id']}')) or cm.monthdis_applyschool=0)";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and (exists(select co.coursecatapply_id from gmc_monthdis_coursecatapply as co where co.monthdis_id=cm.monthdis_id and co.coursecat_id='{$request['coursecat_id']}') or cm.monthdis_applycoursecat=0)";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cm.*
                ,ifnull((select count(mc.coursecatapply_id) from gmc_monthdis_coursecatapply as mc where mc.monthdis_id=cm.monthdis_id),0) as coursecatNum
                ,ifnull((select count(ms.schoolapply_id) from gmc_monthdis_schoolapply as ms where ms.monthdis_id=cm.monthdis_id),0) as schoolNum
                from gmc_code_monthdis as cm
                where {$datawhere} and cm.company_id='{$this->company_id}'
                order by cm.monthdis_month desc,cm.monthdis_ratio desc
                limit {$pagestart},{$num}
                ";

        $monthList = $this->DataControl->selectClear($sql);
        if (!$monthList) {
            $this->error = true;
            $this->errortip = "无期度特殊月份设置数据";
            return false;
        }

        $status = $this->LgArraySwitch(array("0" => "全部适用", "1" => "部分适用"));

        foreach ($monthList as &$monthOne) {
            $monthOne['schoolName'] = $status[$monthOne['monthdis_applyschool']] . ($monthOne['monthdis_applyschool'] == 0 ? '' : ' (' . $monthOne['schoolNum'] . ')');

            $monthOne['coursecatName'] = $status[$monthOne['monthdis_applycoursecat']] . ($monthOne['monthdis_applycoursecat'] == 0 ? '' : ' (' . $monthOne['coursecatNum'] . ')');

            $monthOne['monthdis_ratio_name'] = 10 * $monthOne['monthdis_ratio'] . '折';
            $monthOne['monthdis_ratio'] = 10 * $monthOne['monthdis_ratio'];
            $monthOne['monthdis_remark'] = $monthOne['monthdis_remark'] ? $monthOne['monthdis_remark'] : '--';
        }

        $data = array();
        $count_sql = "select cm.monthdis_id
                from gmc_code_monthdis as cm
                where {$datawhere} and cm.company_id='{$this->company_id}'";
        $db_nums = $this->DataControl->selectClear($count_sql);

        $allnum = $db_nums ? count($db_nums) : 0;

        $data['allnum'] = $allnum;
        $data['list'] = $monthList;
        return $data;
    }

    function addMonthdis($request)
    {

        if (!isset($request['monthdis_month']) || $request['monthdis_month'] == '') {
            $this->error = true;
            $this->errortip = "特殊月份必须设置";
            return false;
        }

        if ($this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_id", "monthdis_month='{$request['monthdis_month']}' and monthdis_ratio='{$request['monthdis_ratio']}' and company_id='{$this->company_id}'")) {
            $this->error = true;
            $this->errortip = $request['monthdis_month'] . "优惠折扣已设置，请勿重复设置";
            return false;
        }

        if ($this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_id", "monthdis_month='{$request['monthdis_month']}' and company_id='{$this->company_id}' and monthdis_applyschool=0")) {
            $this->error = true;
            $this->errortip = $request['monthdis_month'] . "已存在全部适用学校设置,无法新增设置";
            return false;
        }

        if ($this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_id", "monthdis_month='{$request['monthdis_month']}' and company_id='{$this->company_id}' and monthdis_applycoursecat=0")) {
            $this->error = true;
            $this->errortip = $request['monthdis_month'] . "已存在全部适用班种设置,无法新增设置";
            return false;
        }

        $sql = "select ms.school_id
              from gmc_monthdis_schoolapply as ms,gmc_code_monthdis as cm 
              where ms.monthdis_id=cm.monthdis_id and cm.monthdis_month='{$request['monthdis_month']}' and cm.company_id='{$this->company_id}' limit 0,1";

        $applyschoolOne = $this->DataControl->selectOne($sql);

        if ($applyschoolOne && $request['monthdis_applyschool'] == 0) {
            $this->error = true;
            $this->errortip = $request['monthdis_month'] . "已存在部分适用学校设置,无法新增设置";
            return false;
        }

        $sql = "select ms.coursecat_id
              from gmc_monthdis_coursecatapply as ms,gmc_code_monthdis as cm 
              where ms.monthdis_id=cm.monthdis_id and cm.monthdis_month='{$request['monthdis_month']}' and cm.company_id='{$this->company_id}' limit 0,1";

        $applycourseCatOne = $this->DataControl->selectOne($sql);
        if ($applycourseCatOne && $request['monthdis_applycoursecat'] == 0) {
            $this->error = true;
            $this->errortip = $request['monthdis_month'] . "已存在部分适用班种设置,无法新增设置";
            return false;
        }

        if (!isset($request['monthdis_ratio']) || $request['monthdis_ratio'] == '') {
            $this->error = true;
            $this->errortip = "请设置优惠比例";
            return false;
        }

        if ($request['monthdis_ratio'] < 0 || $request['monthdis_ratio'] > 1) {
            $this->error = true;
            $this->errortip = "请设置正确的优惠比例";
            return false;
        }

        $data = array();
        $data['company_id'] = $this->company_id;
        $data['monthdis_month'] = $request['monthdis_month'];
        $data['monthdis_ratio'] = $request['monthdis_ratio'];
        $data['monthdis_applyschool'] = $request['monthdis_applyschool'];
        $data['monthdis_applycoursecat'] = $request['monthdis_applycoursecat'];
        $data['monthdis_remark'] = $request['monthdis_remark'];
        $data['monthdis_createtime'] = time();

        $monthdis_id = $this->DataControl->insertData("gmc_code_monthdis", $data);
        if ($monthdis_id) {
            return $monthdis_id;
        } else {
            $this->error = true;
            $this->errortip = "数据异常";
            return false;
        }
    }

    function editMonthdis($request)
    {

        if (!isset($request['monthdis_month']) || $request['monthdis_month'] == '') {
            $this->error = true;
            $this->errortip = "特殊月份必须设置";
            return false;
        }

        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '') {
            $this->error = true;
            $this->errortip = "请选择内容";
            return false;
        }

        if ($this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_id", "monthdis_month='{$request['monthdis_month']}' and monthdis_ratio='{$request['monthdis_ratio']}' and company_id='{$this->company_id}' and monthdis_id<>'{$request['monthdis_id']}'")) {
            $this->error = true;
            $this->errortip = $request['monthdis_month'] . "优惠折扣已设置，请勿重复设置";
            return false;
        }

        $monthdisOne = $this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_month,monthdis_applyschool,monthdis_applycoursecat", "monthdis_id='{$request['monthdis_id']}'");
        if (!$monthdisOne) {
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }

        $sql = "select ms.school_id
              from gmc_monthdis_schoolapply as ms,gmc_code_monthdis as cm 
              where ms.monthdis_id=cm.monthdis_id and cm.monthdis_month='{$monthdisOne['monthdis_month']}' and cm.company_id='{$this->company_id}' limit 0,1";

        $applyschoolOne = $this->DataControl->selectOne($sql);

        if ($monthdisOne['monthdis_applyschool'] == 0 && $request['monthdis_applyschool'] == 1 && $applyschoolOne) {
            $this->error = true;
            $this->errortip = "全部适用不可转为部分适用";
            return false;
        }

        $sql = "select ms.coursecat_id
              from gmc_monthdis_coursecatapply as ms,gmc_code_monthdis as cm 
              where ms.monthdis_id=cm.monthdis_id and cm.monthdis_month='{$monthdisOne['monthdis_month']}' and cm.company_id='{$this->company_id}' limit 0,1";

        $applycourseCatOne = $this->DataControl->selectOne($sql);

        if ($monthdisOne['monthdis_applycoursecat'] == 0 && $request['monthdis_applycoursecat'] == 1 && $applycourseCatOne) {
            $this->error = true;
            $this->errortip = "全部适用不可转为部分适用";
            return false;
        }

        $data = array();
        $data['monthdis_month'] = $request['monthdis_month'];
        $data['monthdis_ratio'] = $request['monthdis_ratio'] / 10;
        $data['monthdis_applyschool'] = $request['monthdis_applyschool'];
        $data['monthdis_applycoursecat'] = $request['monthdis_applycoursecat'];
        $data['monthdis_remark'] = $request['monthdis_remark'];
        $data['monthdis_updatatime'] = time();

        if ($this->DataControl->updateData("gmc_code_monthdis", "monthdis_id='{$request['monthdis_id']}'", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据异常";
            return false;
        }
    }

    function delMonthdis($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '') {
            $this->error = true;
            $this->errortip = "请选择内容";
            return false;
        }

        if ($this->DataControl->delData("gmc_code_monthdis", "monthdis_id='{$request['monthdis_id']}'")) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据异常";
            return false;
        }
    }

    function getSchoolList($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '' || $request['monthdis_id'] == '0') {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }

        $monthdisOne = $this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_month,monthdis_ratio", "monthdis_id='{$request['monthdis_id']}'");

        if (!$monthdisOne) {
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }

        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_enname like '%{$request['keyword']}%' or sc.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and sc.district_id='{$request['district_id']}'";
        }

        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] !== '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organizeclass_id']) && $request['organizeclass_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organizeclass_id='{$request['organizeclass_id']}' and g.company_id='{$this->company_id}')";
        }

        if (isset($request['organize_id']) && $request['organize_id'] != '') {
            $datawhere .= " and sc.school_id in (select co.school_id from gmc_company_organizeschool as co left join gmc_company_organize as g on g.organize_id=co.organize_id where g.organize_id='{$request['organize_id']}' and g.company_id='{$this->company_id}')";
        }

        $sql = "select sc.school_id,sc.school_branch,sc.school_cnname,sc.school_enname,cd.district_cnname
              from smc_school as sc
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}'
              and not exists(select 1 from gmc_monthdis_schoolapply as ms,gmc_code_monthdis as cm 
              where ms.monthdis_id=cm.monthdis_id and cm.company_id='{$this->company_id}' 
              and cm.monthdis_month='{$monthdisOne['monthdis_month']}' and cm.monthdis_ratio='{$monthdisOne['monthdis_ratio']}' and ms.school_id=sc.school_id)
              ORDER BY sc.school_sort DESC
              ";

        $schoolList = $this->DataControl->selectClear($sql);
        if (!$schoolList) {
            $this->error = true;
            $this->errortip = "无相关学校";
            return false;
        }

        $data = array();
        $data['district'] = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$this->company_id}'");
        $data['schoolList'] = $schoolList;

        return $data;
    }

    function getApplySchoolList($request)
    {

        $datawhere = " 1 ";
        if (isset($request['monthdis_id']) && $request['monthdis_id'] !== '') {
            $datawhere .= " and ts.monthdis_id='{$request['monthdis_id']}'";
        } else {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }

        $sql = "select sc.school_id,sc.school_branch,sc.school_cnname,sc.school_enname,cd.district_cnname
              from gmc_monthdis_schoolapply as ts
              left join smc_school as sc on sc.school_id=ts.school_id
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}'
              ORDER BY sc.school_sort DESC
              ";

        $schoolList = $this->DataControl->selectClear($sql);
        if (!$schoolList) {
            $this->error = true;
            $this->errortip = "无适配学校";
            return false;
        }

        return $schoolList;

    }

    function applySchool($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '' || $request['monthdis_id'] == '0') {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }
        $schoolList = json_decode(stripslashes($request['school_list']), true);
        if ($schoolList) {
            foreach ($schoolList as $schoolOne) {
                $schoolapply = $this->DataControl->getFieldOne("gmc_monthdis_schoolapply", "schoolapply_id", "school_id='{$schoolOne['school_id']}' and monthdis_id='{$request['monthdis_id']}'");
                if (!$schoolapply) {
                    $data = array();
                    $data['school_id'] = $schoolOne['school_id'];
                    $data['monthdis_id'] = $request['monthdis_id'];
                    $this->DataControl->insertData('gmc_monthdis_schoolapply', $data);
                }
            }
        } else {
            $this->error = true;
            $this->errortip = "请勿传入空值";
            return false;
        }

        return true;
    }

    function removeSchool($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '' || $request['monthdis_id'] == '0') {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }

        if (!isset($request['school_id']) || $request['school_id'] == '' || $request['school_id'] == '0') {
            $this->error = true;
            $this->errortip = "学校ID必须传";
            return false;
        }

        $where = "monthdis_id='{$request['monthdis_id']}'";
        $school_list = json_decode(stripslashes($request['school_id']), true);
        if (is_array($school_list)) {
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $school_id = implode(',', $str);
            $where .= " and school_id in ({$school_id})";
        } else {
            $where .= " and school_id = '{$request['school_id']}'";
        }

        $this->DataControl->delData("gmc_monthdis_schoolapply", $where);

        return true;
    }

    function getAllCourseCat($request)
    {

        $datawhere = " 1 ";

        if (isset($request['course_inclasstype']) && $request['course_inclasstype'] != '') {
            $datawhere .= " and sc.course_inclasstype='{$request['course_inclasstype']}'";
        }

        $sql = "select cc.coursecat_id,cc.coursecat_cnname,cc.coursecat_branch 
              from smc_course as sc,smc_code_coursecat as cc 
              where {$datawhere} and sc.coursecat_id=cc.coursecat_id 
              and sc.company_id='{$this->company_id}' and cc.company_id='{$this->company_id}'
              group by cc.coursecat_id
              order by cc.coursecat_id asc
              ";

        $catList = $this->DataControl->selectClear($sql);

        if (!$catList) {
            $this->error = true;
            $this->errortip = "无班种信息";
            return false;
        }

        return $catList;

    }

    function getCoursecatList($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '' || $request['monthdis_id'] == '0') {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }

        $monthdisOne = $this->DataControl->getFieldOne("gmc_code_monthdis", "monthdis_id,monthdis_month,monthdis_ratio", "monthdis_id='{$request['monthdis_id']}'");

        if (!$monthdisOne) {
            $this->error = true;
            $this->errortip = "请选择正确的月份";
            return false;
        }

        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (co.coursecat_cnname like '%{$request['keyword']}%' or co.coursecat_branch like '%{$request['keyword']}%')";
        }

        $sql = "select co.coursecat_id,co.coursecat_cnname,co.coursecat_branch 
              from smc_code_coursecat as co,smc_course as sc 
              where co.coursecat_id=sc.coursecat_id and {$datawhere} and co.company_id='{$this->company_id}' and sc.course_inclasstype='1' 
              and not exists(select 1 from gmc_monthdis_coursecatapply as ms,gmc_code_monthdis as cm
              where ms.monthdis_id=cm.monthdis_id and cm.company_id='{$this->company_id}'
              and cm.monthdis_month='{$monthdisOne['monthdis_month']}' 
              and cm.monthdis_id='{$monthdisOne['monthdis_id']}' and ms.coursecat_id=co.coursecat_id)
              and not exists(select 1 from gmc_monthdis_coursecatapply as ms,gmc_code_monthdis as cm
              where ms.monthdis_id=cm.monthdis_id and cm.company_id='{$this->company_id}'
              and cm.monthdis_month='{$monthdisOne['monthdis_month']}' 
              and cm.monthdis_id='{$monthdisOne['monthdis_id']}' and cm.monthdis_applycoursecat=0)
              group by co.coursecat_id
              ORDER BY co.coursecat_id ASC
              ";


        $coursecatList = $this->DataControl->selectClear($sql);
//        if($coursecatList){
//            foreach($coursecatList as $key=>$val){
//
//                $sql="select 1 from gmc_code_monthdis as m
//                      where m.monthdis_month='{$monthdisOne['monthdis_month']}' and m.company_id='{$this->company_id}'
//                      and (m.monthdis_applycoursecat=0
//                        or (m.monthdis_applycoursecat=1 and exists(select 1 from gmc_monthdis_coursecatapply gc where gc.monthdis_id=m.monthdis_id and gc.coursecat_id='{$val['coursecat_id']}'))
//                           ) limit 0,1";
//                if($this->DataControl->selectOne($sql)){
//                    unset($coursecatList[$key]);
//                }
//            }
//        }

        if (!$coursecatList) {
            $this->error = true;
            $this->errortip = "无相关班种";
            return false;
        }


        return $coursecatList;
    }

    function getApplyCoursecatList($request)
    {

        $datawhere = " 1 ";
        if (isset($request['monthdis_id']) && $request['monthdis_id'] !== '') {
            $datawhere .= " and mc.monthdis_id='{$request['monthdis_id']}'";
        } else {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }

        $sql = "select co.coursecat_id,co.coursecat_cnname,co.coursecat_branch
              from gmc_monthdis_coursecatapply as mc
              inner join smc_code_coursecat as co on co.coursecat_id=mc.coursecat_id
              where {$datawhere} and co.company_id='{$this->company_id}' 
              group by co.coursecat_id
              ORDER BY co.coursecat_id ASC
              ";

        $courseList = $this->DataControl->selectClear($sql);
        if (!$courseList) {
            $this->error = true;
            $this->errortip = "无适配课程";
            return false;
        }

        return $courseList;

    }

    function applyCoursecat($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '' || $request['monthdis_id'] == '0') {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }
        $coursecatList = json_decode(stripslashes($request['coursecat_list']), true);
        if ($coursecatList) {
            foreach ($coursecatList as $coursecatOne) {
                $courseapply = $this->DataControl->getFieldOne("gmc_monthdis_coursecatapply", "coursecatapply_id", "coursecat_id='{$coursecatOne['coursecat_id']}' and monthdis_id='{$request['monthdis_id']}'");
                if (!$courseapply) {
                    $data = array();
                    $data['coursecat_id'] = $coursecatOne['coursecat_id'];
                    $data['monthdis_id'] = $request['monthdis_id'];
                    $this->DataControl->insertData('gmc_monthdis_coursecatapply', $data);
                }
            }
        } else {
            $this->error = true;
            $this->errortip = "请勿传入空值";
            return false;
        }

        return true;
    }

    function removeCoursecat($request)
    {
        if (!isset($request['monthdis_id']) || $request['monthdis_id'] == '' || $request['monthdis_id'] == '0') {
            $this->error = true;
            $this->errortip = "特殊月份ID必须传";
            return false;
        }

        if (!isset($request['coursecat_id']) || $request['coursecat_id'] == '' || $request['coursecat_id'] == '0') {
            $this->error = true;
            $this->errortip = "课程ID必须传";
            return false;
        }

        $this->DataControl->delData("gmc_monthdis_coursecatapply", "monthdis_id='{$request['monthdis_id']}' and coursecat_id='{$request['coursecat_id']}'");

        return true;
    }


    //添加班外课时
    function classScheduleAction($paramArray)
    {
        $data = array();
        $data['outclasstype_id'] = $paramArray['outclasstype_id'];
        $data['hour_scname'] = $paramArray['hour_scname'];
        $data['hour_remk'] = $paramArray['hour_remk'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['company_id'] = $paramArray['company_id'];
        $data['hour_createtime'] = time();

        if ($paramArray['type'] == '1') {
            $data['hour_day'] = $paramArray['hour_day'];
            $data['hour_starttime'] = $paramArray['hour_starttime'];
            $data['hour_endtime'] = $paramArray['hour_endtime'];

            $this->DataControl->insertData('smc_outclass_hour', $data);
        } else {

            $time_start = strtotime($paramArray['start_day']);
            $time_end = strtotime($paramArray['end_day']);
            $date = array();

            while ($time_start <= $time_end) {
                $dateOne = date('Y-m-d', $time_start);

                if (in_array(date('N', strtotime($dateOne)), json_decode(stripslashes($paramArray['json_week']), 1))) {

                    $date[] = $dateOne;
                }

                $time_start = strtotime('+1 day', $time_start);
            }

            if (count($date) <= 0) {
                return 111112;
            }

            for ($i = 0; $i < count($date); $i++) {
                if ($paramArray['is_skipweek'] == 1) {
                    $addnum = $i * 7;
                } else {
                    $addnum = $i;
                }
                if ($addnum > count($date)) {
                    continue;
                }
                $data['hour_day'] = $date[$addnum];
                $data['hour_starttime'] = $paramArray['hour_starttime'];
                $data['hour_endtime'] = $paramArray['hour_endtime'];
                $this->DataControl->insertData('smc_outclass_hour', $data);
            }
        }

        $result = array();
        $result["data"] = $data;
        $res = array('error' => '0', 'errortip' => "添加班外课时类型成功", 'result' => $result);
        return $res;
    }

    private function AverageDivisionNumber($allnumber, $total)
    {
        if ($allnumber <= 0 || $total <= 0 || ($allnumber < $total)) {
            $this->error = 1;
            $this->error = '请检查排课设置';
            return false;
        }

        $divide_number = bcdiv($allnumber, $total, 0);
        $mod_number = bcsub($allnumber, $divide_number * $total, 0);
        $number_str = trim(str_repeat($divide_number . '+', $total), '+');
        $numArray = explode("+", $number_str);
        if ($mod_number > 0) {
            for ($i = 0; $i < $mod_number; $i++) {
                $numArray[$i] = $numArray[$i] + 1;
            }
        }
        return $numArray;
    }

    public static $WORK_DAY = [
        1 => ['en' => 'Monday', 'cn' => '周一'],
        2 => ['en' => 'Tuesday', 'cn' => '周二'],
        3 => ['en' => 'Wednesday', 'cn' => '周三'],
        4 => ['en' => 'Thursday', 'cn' => '周四'],
        5 => ['en' => 'Friday', 'cn' => '周五'],
        6 => ['en' => 'Saturday', 'cn' => '周六'],
        7 => ['en' => 'Sunday', 'cn' => '周日']
    ];


}
