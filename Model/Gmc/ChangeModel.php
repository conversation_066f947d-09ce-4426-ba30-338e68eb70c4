<?php


namespace Model\Gmc;

use Model\Smc\BalanceModel;
use Model\Smc\modelTpl;

class ChangeModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $school_id = 0;//操作学校
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }

        if (isset($publicarray['staffer_id'])) {
            $this->verdictStaffer($publicarray['staffer_id']);
            $this->staffer_id = $publicarray['staffer_id'];
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");

        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getFreeapplyList($request)
    {
        $datawhere = " a.company_id ='{$request['company_id']}' ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.class_cnname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id ='{$request['school_id']}'";
        }

        if (isset($request['freeapply_status']) && $request['freeapply_status'] !== '') {
            $datawhere .= " and a.freeapply_status ='{$request['freeapply_status']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.freeapply_createtime,'%Y-%m-%d') >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.freeapply_createtime,'%Y-%m-%d') <='{$request['endtime']}'";
        }

        $sql = "select a.freeapply_id,b.class_cnname,b.class_branch,a.freeapply_type,a.freeapply_day,concat(a.freeapply_starttime,'-',a.freeapply_endtime) as timerange,c.staffer_cnname as main_staffer_cnname,c.staffer_enname as main_staffer_enname,d.teachtype_name as main_teachtype_name,e.staffer_cnname as fu_staffer_cnname,e.staffer_enname as fu_staffer_enname,f.teachtype_name as fu_teachtype_name,g.classroom_cnname,a.freeapply_reason,h.staffer_cnname,FROM_UNIXTIME(a.freeapply_createtime,'%Y-%m-%d %H:%i') as freeapply_createtime,a.freeapply_status,a.freeapply_note,i.school_cnname,j.district_cnname,k.staffer_cnname as freeapply_staffer_cnname,FROM_UNIXTIME(a.freeapply_updatatime,'%Y-%m-%d %H:%i') as freeapply_updatatime
				from smc_class_freeapply as a 
				left join smc_school as i on i.school_id=a.school_id
				left join smc_class as b on b.class_id=a.class_id
				left join smc_staffer as c on c.staffer_id=a.main_staffer_id
				left join smc_code_teachtype as d on d.teachtype_code=a.main_teachtype_code and d.company_id='{$this->company_id}'
				left join smc_staffer as e on e.staffer_id=a.fu_staffer_id
				left join smc_code_teachtype as f on f.teachtype_code=a.fu_teachtype_code and f.company_id='{$this->company_id}'
				left join smc_classroom as g on g.classroom_id=a.classroom_id
				left join smc_staffer as h on h.staffer_id=a.staffer_id
				left join gmc_company_district as j on j.district_id=i.district_id
				left join smc_staffer as k on k.staffer_id=a.freeapply_staffer_id
 				where {$datawhere} 
 				order by a.freeapply_id desc";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $freeapplyList = $this->DataControl->selectClear($sql);

        if (!$freeapplyList) {
            $this->error = true;
            $this->errortip = "无申请数据";
            return false;
        }

        $status=$this->LgArraySwitch(array("-2"=>"已撤销","-1"=>"已拒绝","0"=>"待审核","1"=>"已通过"));
        $type=$this->LgArraySwitch(array("0"=>"暖身课","1"=>"复习课"));

        foreach ($freeapplyList as &$applyOne) {

            if($applyOne['main_staffer_cnname'] && $applyOne['main_staffer_cnname']!='null'){
                if($applyOne['main_staffer_enname'] && $applyOne['main_staffer_enname']!='null'){
                    $applyOne['main_staffer_cnname'].='-'.$applyOne['main_staffer_enname'];
                }
                if($applyOne['main_teachtype_name'] && $applyOne['main_teachtype_name']!='null'){
                    $applyOne['main_staffer_cnname'].='('.$applyOne['main_teachtype_name'].')';
                }
            }

            if($applyOne['fu_staffer_cnname'] && $applyOne['fu_staffer_cnname']!='null'){
                if($applyOne['main_staffer_enname'] && $applyOne['main_staffer_enname']!='null'){
                    $applyOne['main_staffer_cnname'].='-'.$applyOne['main_staffer_enname'];
                }
                if($applyOne['main_teachtype_name'] && $applyOne['main_teachtype_name']!='null'){
                    $applyOne['main_staffer_cnname'].='('.$applyOne['main_teachtype_name'].')';
                }
            }
            $applyOne['freeapply_status_name']=$status[$applyOne['freeapply_status']];
            $applyOne['freeapply_type_name']=$type[$applyOne['freeapply_type']];

            if($applyOne['freeapply_status']==0){
                $applyOne['freeapply_note']='--';
                $applyOne['freeapply_updatatime']='--';
                $applyOne['freeapply_staffer_cnname']='--';
            }

        }

        $count_sql = "select a.freeapply_id
				from smc_class_freeapply as a 
				left join smc_class as b on b.class_id=a.class_id
 				where {$datawhere} ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $freeapplyList;
        return $data;
    }

    function batchExamineFreeapply($request){

        $applyList=json_decode(stripslashes($request['apply_list']), true);

        if(!$applyList){
            $this->error = true;
            $this->errortip = "请选择审核数据";
            return false;
        }

        $t_num=0;
        $f_num=0;

        foreach($applyList as $applyOne){
            $data=array();
            $data['freeapply_id']=$applyOne['freeapply_id'];
            $data['is_adopt']=$request['is_adopt'];
            $data['freeapply_note']=$request['freeapply_note'];

            $bool=$this->examineFreeapply($data);

            if($bool){
                $t_num++;
            }else{
                $f_num++;
            }
        }

        if($t_num==0){
            $this->error = true;
            $this->errortip = "审核失败";
            return false;
        }

        $data=array();
        $data['t_num']=$t_num;
        $data['f_num']=$f_num;

        return $data;


    }

    function examineFreeapply($request){

        $applyOne=$this->DataControl->getOne("smc_class_freeapply","freeapply_id='{$request['freeapply_id']}'");

        if($applyOne['freeapply_status']!=0){
            $this->error = true;
            $this->errortip = "该审核不可审核";
            return false;
        }


        if($request['is_adopt']==1){

            $data=array();
            $data['company_id']=$applyOne['company_id'];
            $data['school_id']=$applyOne['school_id'];
            $data['class_id']=$applyOne['class_id'];
            $data['classroom_id']=$applyOne['classroom_id'];
            $data['staffer_id']=$applyOne['staffer_id'];
            $data['hour_day']=$applyOne['freeapply_day'];
            $data['hour_starttime']=$applyOne['freeapply_starttime'];
            $data['hour_endtime']=$applyOne['freeapply_endtime'];
            $data['main_staffer_id']=$applyOne['main_staffer_id'];
            $data['main_teachtype_code']=$applyOne['main_teachtype_code'];
            $data['fu_staffer_id']=$applyOne['fu_staffer_id'];
            $data['fu_teachtype_code']=$applyOne['fu_teachtype_code'];
            $data['is_skip']=1;

            if($applyOne['freeapply_type']==0){
                $data['hour_iswarming']=1;
            }else{
                $data['hour_iswarming']=2;
            }

            $Model = new \Model\Smc\CourseModel($data);
            $bool=$Model->addWarmHourOne($data);

            if($bool){

                $data=array();
                $data['freeapply_status']=1;
                $data['freeapply_note']=$request['freeapply_note'];
                $data['freeapply_staffer_id']=$request['staffer_id'];
                $data['freeapply_updatatime']=time();
                if($this->DataControl->updateData("smc_class_freeapply","freeapply_id='{$request['freeapply_id']}'",$data)){
                    return true;
                }else{
                    $this->error = true;
                    $this->errortip = '审核失败';
                    return false;
                }


            }else{
                $this->error = true;
                $this->errortip = $Model->errortip;
                return false;
            }

        }else{

            $data=array();
            $data['freeapply_status']=-1;
            $data['freeapply_note']=$request['freeapply_note'];
            $data['freeapply_staffer_id']=$request['staffer_id'];
            $data['freeapply_updatatime']=time();
            if($this->DataControl->updateData("smc_class_freeapply","freeapply_id='{$request['freeapply_id']}'",$data)){
                return true;
            }else{
                $this->error = true;
                $this->errortip = '审核失败';
                return false;
            }
        }

    }

    function getAdjustapplyList($request)
    {
        $datawhere = " a.company_id ='{$request['company_id']}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.class_cnname like '%{$request['keyword']}%' 
            or b.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['adjustapply_status']) && $request['adjustapply_status'] !== '') {
            $datawhere .= " and a.adjustapply_status ='{$request['adjustapply_status']}'";
        }

        if (isset($request['adjustapply_type']) && $request['adjustapply_type'] !== '') {
            $datawhere .= " and a.adjustapply_type ='{$request['adjustapply_type']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id ='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and m.school_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.adjustapply_createtime,'%Y-%m-%d') >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.adjustapply_createtime,'%Y-%m-%d') <='{$request['endtime']}'";
        }

        if (isset($request['adjustapply_class']) && $request['adjustapply_class'] !== '') {
            $datawhere .= " and a.adjustapply_class ='{$request['adjustapply_class']}'";
        }

        if (isset($request['difference_class']) && $request['difference_class'] !== '') {

            $datawhere .= " and a.adjustapply_type=0";

            if($request['difference_class']==0){
                $datawhere .= " and ABS(DATEDIFF(a.adjustapply_day, d.hour_day)) > 30";
            }else{
                $datawhere .= " and ABS(DATEDIFF(a.adjustapply_day, d.hour_day)) <= 30";
            }

        }

        $sql = "select a.adjustapply_id,b.class_cnname,b.class_branch,a.adjustapply_type,if(a.adjustapply_status=1,d.hour_formerday,d.hour_day) as hour_day,a.adjustapply_day,c.staffer_cnname,a.adjustapply_reason,FROM_UNIXTIME(a.adjustapply_createtime,'%Y-%m-%d %H:%i') as adjustapply_createtime,a.adjustapply_status,a.adjustapply_note,i.school_cnname,j.district_cnname,k.staffer_cnname as adjustapply_staffer_cnname,FROM_UNIXTIME(a.adjustapply_updatatime,'%Y-%m-%d %H:%i') as adjustapply_updatatime,a.adjustapply_class,if(a.adjustapply_type=0,ABS(ABS(DATEDIFF(a.adjustapply_day, d.hour_day))-1),'--') AS days_difference,a.adjustapply_fileurl,ifnull(n.coursetype_cnname,'--') as coursetype_cnname
				from smc_class_hour_adjustapply as a 
				left join smc_class as b on b.class_id=a.class_id
				left join smc_staffer as c on c.staffer_id=a.staffer_id
				left join smc_class_hour as d on d.hour_id=a.hour_id
				left join smc_school as i on i.school_id=a.school_id
				left join gmc_company_district as j on j.district_id=i.district_id
				left join smc_staffer as k on k.staffer_id=a.adjustapply_staffer_id
				left join smc_course as m on m.course_id=b.course_id
				left join smc_code_coursetype as n on n.coursetype_id=m.coursetype_id
 				where {$datawhere} 
 				order by a.adjustapply_id desc";

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $adjustapplyList = $this->DataControl->selectClear($sql);

        if (!$adjustapplyList) {
            $this->error = true;
            $this->errortip = "无申请数据";
            return false;
        }

        $status=$this->LgArraySwitch(array("-2"=>"已撤销","-1"=>"已拒绝","0"=>"待审核","1"=>"已通过"));
        $type=$this->LgArraySwitch(array("0"=>"班级调课","1"=>"全校调课"));
        $class=$this->LgArraySwitch(array("0"=>"政策调课","1"=>"其他调课"));

        foreach ($adjustapplyList as &$applyOne) {

            $applyOne['adjustapply_status_name']=$status[$applyOne['adjustapply_status']];
            $applyOne['adjustapply_type_name']=$type[$applyOne['adjustapply_type']];
            $applyOne['adjustapply_class_name']=$class[$applyOne['adjustapply_class']];

            if($applyOne['adjustapply_status']==0){
                $applyOne['adjustapply_note']='--';
                $applyOne['adjustapply_staffer_cnname']='--';
                $applyOne['adjustapply_updatatime']='--';
            }

            if($applyOne['adjustapply_type']==1){
                $applyOne['class_cnname']='--';
                $applyOne['class_branch']='--';
                $applyOne['hour_day']=$applyOne['adjustapply_day'];
                $applyOne['adjustapply_day']='--';
            }


            $applyOne['adjustapply_fileurl_name']=$applyOne['adjustapply_fileurl']!=''?$this->LgStringSwitch('查看附件'):'';

        }

        $count_sql = "select a.adjustapply_id
				from smc_class_hour_adjustapply as a 
				left join smc_class as b on b.class_id=a.class_id
				left join smc_staffer as c on c.staffer_id=a.staffer_id
				left join smc_class_hour as d on d.hour_id=a.hour_id
 				where {$datawhere}  ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $adjustapplyList;
        return $data;
    }


    function batchExamineAdjustapply($request){

        $applyList=json_decode(stripslashes($request['apply_list']), true);

        if(!$applyList){
            $this->error = true;
            $this->errortip = "请选择审核数据";
            return false;
        }

        $t_num=0;
        $f_num=0;

        foreach($applyList as $applyOne){
            $data=array();
            $data['adjustapply_id']=$applyOne['adjustapply_id'];
            $data['is_adopt']=$request['is_adopt'];
            $data['adjustapply_note']=$request['adjustapply_note'];

            $bool=$this->examineAdjustapply($data);

            if($bool){
                $t_num++;
            }else{
                $f_num++;
            }
        }

        if($t_num==0){
            $this->error = true;
            $this->errortip = "审核失败";
            return false;
        }

        $data=array();
        $data['t_num']=$t_num;
        $data['f_num']=$f_num;

        return $data;


    }


    function examineAdjustapply($request,$from=0){

        $applyOne=$this->DataControl->getOne("smc_class_hour_adjustapply","adjustapply_id='{$request['adjustapply_id']}'");

        if($applyOne['adjustapply_status']!=0){
            $this->error = true;
            $this->errortip = "该审核不可审核";
            return false;
        }

        if(($applyOne['requestid']>0 && $from==0) || ($applyOne['is_need_process']==1 && $from==0)){
            if($this->company_id== '8888' && SITE_URL=='kedingdang.com'){
                $this->error = true;
                $this->errortip = "泛微流程,平台审核关闭";
                return false;
            }
        }


        if($request['is_adopt']==1){

            if($applyOne['adjustapply_type']==1){

                $data=array();
                $data['adjustapply_status']=1;
                $data['adjustapply_note']=$request['adjustapply_note'];
                $data['adjustapply_staffer_id']=$request['staffer_id'];
                $data['adjustapply_updatatime']=time();
                if($this->DataControl->updateData("smc_class_hour_adjustapply","adjustapply_id='{$request['adjustapply_id']}'",$data)){
                    return true;
                }else{
                    $this->error = true;
                    $this->errortip = '审核失败';
                    return false;
                }

            }else{
                $data=array();
                $data['company_id']=$applyOne['company_id'];
                $data['school_id']=$applyOne['school_id'];
                $data['class_id']=$applyOne['class_id'];
                $data['staffer_id']=$applyOne['staffer_id'];
                $data['hour_id']=$applyOne['hour_id'];
                $data['hour_day']=$applyOne['adjustapply_day'];
                $data['starttime']=$applyOne['adjustapply_starttime'];
                $data['endtime']=$applyOne['adjustapply_endtime'];

                $data['z_staffer_id']=$applyOne['adjustapply_main_staffer_id'];
                $data['z_teachtype_code']=$applyOne['adjustapply_main_teachtype_code'];
                $data['f_staffer_id']=$applyOne['adjustapply_sub_staffer_id'];
                $data['f_teachtype_code']=$applyOne['adjustapply_sub_teachtype_code'];
                $data['classroom_id']=$applyOne['adjustapply_classroom_id'];

                $data['is_skip']=1;
                $data['type']=2;

                $Model = new \Model\Smc\AdjustmentModel($data);
                $bool= $Model->adjustCourse($data);

                if($bool){

                    $data=array();
                    $data['adjustapply_status']=1;
                    $data['adjustapply_note']=$request['adjustapply_note'];
                    $data['adjustapply_staffer_id']=$request['staffer_id'];
                    $data['adjustapply_updatatime']=time();
                    if($this->DataControl->updateData("smc_class_hour_adjustapply","adjustapply_id='{$request['adjustapply_id']}'",$data)){

                        $sql = "call RefreshClass('{$applyOne['class_id']}')";
                        $this->DataControl->query($sql);

                        return true;
                    }else{
                        $this->error = true;
                        $this->errortip = '审核失败';
                        return false;
                    }


                }else{
                    $this->error = true;
                    $this->errortip = $Model->errortip;
                    return false;
                }
            }



        }else{

            $data=array();
            $data['adjustapply_status']=-1;
            $data['adjustapply_note']=$request['adjustapply_note'];
            $data['adjustapply_staffer_id']=$request['staffer_id'];
            $data['adjustapply_updatatime']=time();
            if($this->DataControl->updateData("smc_class_hour_adjustapply","adjustapply_id='{$request['adjustapply_id']}'",$data)){
                return true;
            }else{
                $this->error = true;
                $this->errortip = '审核失败';
                return false;
            }
        }

    }

    
    function getApplicationList($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.student_cnname like '%{$request['keyword']}%' or b.student_enname like '%{$request['keyword']}%' or b.student_idcard like '%{$request['keyword']}%' or b.student_branch like '%{$request['keyword']}%')
            ";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(a.apply_time,'%Y-%m-%d')>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and  FROM_UNIXTIME(a.apply_time,'%Y-%m-%d')<='{$request['endtime']}'";
        }

        if (isset($request['application_status']) && $request['application_status'] != '') {
            $datawhere .= " and a.application_status='{$request['application_status']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and a.school_id='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT a.student_id,b.student_cnname,b.student_branch,c.course_cnname,c.course_branch,d.class_cnname,d.class_branch,e.staffer_cnname,a.application_type,a.out_class_date,a.back_class_date,a.forward_reason,a.attachment_url,a.application_status,a.apply_time,a.approval_note,f.school_cnname,a.remaining_amount,a.forward_amount
                ,ifnull((select x.course_name from smc_forward_application_course as x where x.application_id=a.application_id and x.course_type=2),'--') as sub_course_name
                from smc_forward_application as a
                inner join smc_student as b on b.student_id=a.student_id
                inner join smc_course as c on c.course_id=a.course_id
                inner join smc_class as d on d.class_id=a.class_id
                inner join smc_staffer as e on e.staffer_id=a.apply_staffer_id
                inner join smc_school as f on f.school_id=a.school_id
                where {$datawhere}
                order by a.application_id DESC
                limit {$pagestart},{$num}
        ";

        $applicationList = $this->DataControl->selectClear($sql);

        if (!$applicationList) {
            $this->error = true;
            $this->errortip = "无课程结转申请";
            return false;
        }

        $application_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '审核通过', '2' => '审核拒绝', '3' => '已取消', '4' => '已完成'));
        $application_type = $this->LgArraySwitch(array('1' => '申请退费', '2' => '申请转班', '3' => '申请暑期长期保留', '4' => '申请拆并班'));
        foreach ($applicationList as &$val) {
            $val['apply_time'] = date("Y-m-d", $val['apply_time']);
            $val['application_status_name'] = $application_status[$val['application_status']];
            $val['application_type_name'] = $application_type[$val['application_type']];
            $val['attachment_url_name'] = $val['attachment_url']!=''?$this->LgStringSwitch('查看附件'):'';
        }

        $count_sql = "select a.application_id
                from smc_forward_application as a
                inner join smc_student as b on b.student_id=a.student_id
                where {$datawhere}
                ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
        $data['allnum'] = $allnum;
        $data['list'] = $applicationList;
        return $data;

    }

    function cancelApplication($request){

        $applicationOne=$this->DataControl->selectOne("select application_id,application_status,requestid from smc_forward_application where application_id='{$request['application_id']}' and school_id='{$this->school_id}'");
        if(!$applicationOne){
            $this->error = true;
            $this->errortip = "结转申请不存在";
            return false;
        }

        if($applicationOne['application_status']!=0){
            $this->error = true;
            $this->errortip = "结转申请状态不正确";
            return false;
        }

        $FanweiModel=new \Model\Smc\FanweiModel();
        $FanweiModel->cancelApplication($applicationOne['requestid']);

        if($this->DataControl->updateData("smc_forward_application","application_id='{$request['application_id']}'",array("application_status"=>3))){
            return true;
        }else{
            $this->error = true;
            $this->errortip = "取消失败";
            return false;
        }

    }

}
