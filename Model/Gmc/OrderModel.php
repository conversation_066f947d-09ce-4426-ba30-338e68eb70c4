<?php


namespace Model\Gmc;

class OrderModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }

    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function renewOrderList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or po.trading_pid like '%{$request['keyword']}%' or po.order_pid like '%{$request['keyword']}%')";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and po.order_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'] . ' 23:59:59');
            $datawhere .= " and po.order_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and po.order_examinetime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . ' 23:59:59');
            $datawhere .= " and po.order_examinetime <= '{$firstday}'";
        }

        if (isset($request['order_status']) && $request['order_status'] !== '') {
            if ($request['order_status'] == 1) {
                $datawhere .= " and po.order_status >= '1'";
            } else {
                $datawhere .= " and po.order_status = '{$request['order_status']}'";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and sch.school_id = '{$request['school_id']}'";
        }

        if ($request['dataequity'] == '1') {
            $sql = "select po.order_pid,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,sch.school_branch,s.student_cnname,s.student_branch,po.order_paymentprice,po.order_status,po.order_note,po.order_createtime,po.order_updatatime,po.order_examinetime 
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_school as sch on sch.school_id=po.school_id
              where {$datawhere} and po.company_id='{$request['company_id']}' and po.order_isneedaudit=1
              order by po.order_createtime desc 
              ";
        } else {
            $sql = "select po.order_pid,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,sch.school_branch,s.student_cnname,s.student_branch,po.order_paymentprice,po.order_status,po.order_note,po.order_createtime,po.order_updatatime,po.order_examinetime 
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_school as sch on sch.school_id=po.school_id
              where {$datawhere} and po.company_id='{$request['company_id']}' and po.order_isneedaudit=1 and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = po.school_id)
              order by po.order_createtime desc 
              ";
        }
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];//订单编号
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];//实际支付金额
                    $datearray['order_note'] = $dateexcelvar['order_note'];//订单备注
                    $datearray['order_createtime'] = date("Y-m-d", $dateexcelvar['order_createtime']);//申请时间
                    $datearray['order_updatatime'] = $dateexcelvar['order_updatatime'] ? date("Y-m-d", $dateexcelvar['order_updatatime']) : '--';//审核时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '学员名称', '学员编号', '实际支付金额', '订单备注', '申请时间', '审核时间'));
            $excelfileds = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'order_paymentprice', 'order_note', 'order_createtime', 'order_updatatime');

            $fielname = $this->LgStringSwitch("续费订单审核表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }

        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        } else {
            foreach ($orderList as &$val) {
                $val['order_createtime'] = date("Y-m-d", $val['order_createtime']);
                if ($val['order_examinetime']) {
                    $val['order_examinetime'] = date("Y-m-d", $val['order_examinetime']);
                } else {
                    $val['order_examinetime'] = '--';
                }
            }
        }

        $data = array();
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            if ($request['dataequity'] == '1') {
                $count_sql = "select po.order_id
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_school as sch on sch.school_id=po.school_id
              where {$datawhere} and po.company_id='{$request['company_id']}' and po.order_isneedaudit=1";
            } else {
                $count_sql = "select po.order_id
              from smc_payfee_order as po
              left join smc_student as s on s.student_id=po.student_id
              left join smc_school as sch on sch.school_id=po.school_id
              where {$datawhere} and po.company_id='{$request['company_id']}' and po.order_isneedaudit=1 and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = po.school_id)";
            }

            $db_nums = $this->DataControl->selectClear($count_sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }
        $schoolList = $this->DataControl->selectClear("select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname from smc_school as s left join smc_payfee_order as po on po.school_id=s.school_id where po.company_id='{$this->company_id}' and po.order_isneedaudit=1 group by s.school_id");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['list'] = $orderList;
        $data['school'] = $schoolList;

        return $data;
    }

    function getOrderList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or st.trading_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['tradingtype_code']) && $request['tradingtype_code'] !== '') {
            $datawhere .= " and st.tradingtype_code in ('" . implode("','", explode(',', $request['tradingtype_code'])) . "')";

            if (isset($request['status']) && $request['status'] != '') {

                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge') {

                    $datawhere .= " and exists (select po.trading_pid from smc_payfee_order as po where po.trading_pid=st.trading_pid and po.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'Accountrefund') {

                    $datawhere .= " and exists (select po.trading_pid from smc_refund_order as po where po.trading_pid=st.trading_pid and po.refund_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'CourseForward') {

                    $datawhere .= " and exists (select po.trading_pid from smc_forward_dealorder as po where po.trading_pid=st.trading_pid and po.dealorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

                    $datawhere .= " and exists (select po.trading_pid from smc_course_reduceorder as po where po.trading_pid=st.trading_pid and po.reduceorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ClassGiving') {

                    $datawhere .= " and exists (select po.trading_pid from smc_freehour_order as po where po.trading_pid=st.trading_pid and po.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferIn') {

                    $datawhere .= " and exists (select po.trading_topid from smc_school_trading as po where po.trading_topid=st.trading_pid and po.trading_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferOut') {

                    $datawhere .= " and exists (select po.trading_frompid from smc_school_trading as po where po.trading_frompid=st.trading_pid and po.trading_status='{$request['status']}')";

                }
            }
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and st.school_id = '{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $request['starttime'] = strtotime($request['starttime']);
            $datawhere .= " and st.trading_createtime >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $request['endtime'] = strtotime(($request['endtime'] . ' 23:59:59'));
            $datawhere .= " and st.trading_createtime <= '{$request['endtime']}'";
        }

        $having = "1=1";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $having .= " and coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $having .= " and coursecat_id = '{$request['coursecat_id']}'";
        }

        $sql = "select st.company_id,st.trading_pid,st.tradingtype_code,s.student_cnname,s.student_enname,s.student_branch,st.trading_createtime,st.tradingtype_code,ct.tradingtype_name,st.student_id,
                     st.trading_status,sc.school_id,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as school_cnname,sc.school_branch,sc.school_isprotocol,
                     (select cct.coursetype_cnname from smc_forward_dealorder as fd,smc_forward_dealorder_course as dc,smc_course as co ,smc_code_coursetype as cct
                     where fd.dealorder_pid = dc.dealorder_pid and dc.course_id = co.course_id and fd.trading_pid = st.trading_pid and co.coursetype_id=cct.coursetype_id limit 1) as coursetype_cnname,
                     (select ccc.coursecat_cnname from smc_forward_dealorder as fd,smc_forward_dealorder_course as dc,smc_course as co ,smc_code_coursecat as ccc
                     where fd.dealorder_pid = dc.dealorder_pid and dc.course_id = co.course_id and fd.trading_pid = st.trading_pid and co.coursecat_id=ccc.coursecat_id limit 1) as coursecat_cnname,
                     (select co.coursetype_id from smc_forward_dealorder as fd,smc_forward_dealorder_course as dc,smc_course as co 
                     where fd.dealorder_pid = dc.dealorder_pid and dc.course_id = co.course_id and fd.trading_pid = st.trading_pid limit 1) as coursetype_id,
                     (select co.coursecat_id from smc_forward_dealorder as fd,smc_forward_dealorder_course as dc,smc_course as co 
                     where fd.dealorder_pid = dc.dealorder_pid and dc.course_id = co.course_id and fd.trading_pid = st.trading_pid limit 1) as coursecat_id
            from smc_student_trading as st
            left join smc_student as s on s.student_id=st.student_id
            left join smc_code_tradingtype as ct ON ct.tradingtype_code =st.tradingtype_code
            left join smc_school as sc on sc.school_id=st.school_id
            where {$datawhere} and st.company_id='{$request['company_id']}' and st.tradingtype_code<>'Subscribed' and st.tradingtype_code<>'MonthlyShare'
            having {$having}
            order by st.trading_id desc
            ";

        if ($request['is_export'] && $request['is_export'] == '1') {
            $tradeList = $this->DataControl->selectClear($sql);
        } else {
            $tradeList = $this->DataControl->selectClear($sql . " limit {$pagestart},{$num}");
        }

        if (!$tradeList) {
            $tradeList = array();
        }

        $list = array();

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $order_isinvoice = $this->LgArraySwitch(array("0" => "未开具", "1" => "已开具"));
        $porder_type = $this->LgArraySwitch(array("0" => "课程缴费订单", "1" => "普通收费订单", "2" => "充值类订单"));
        $rorder_type = $this->LgArraySwitch(array("0" => "银行转账", "1" => "原路返还"));
        $porder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));
        $rorder_status = $this->LgArraySwitch(array('0' => '待审核(校园)', '1' => '待审核(集团)', '2' => '待处理', '3' => '待确定金额', '4' => '已完成', '-1' => '审核拒绝'));
        $dealorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        $field = array();
        $tem_name = '';
        $excelheader = array();
        $excelfileds = array();
        $outexceldata = array();

        $typeList = explode(',', $request['tradingtype_code']);

        if ($typeList) {
            foreach ($typeList as $val) {
                if ($val == 'PaynewFee' || $val == 'PayrenewFee' || $val == 'CourseMakeUp' || $val == 'PayitemFee'
                    || $val == 'Recharge' || $val == 'Accountrefund' || $val == 'DepositCharge') {
                    $field = array();
                    $k = 0;
                    $field[$k]["fieldname"] = "school_cnname";
                    $field[$k]["fieldstring"] = "校区名称";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "school_branch";
                    $field[$k]["fieldstring"] = "校区编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_pid";
                    $field[$k]["fieldstring"] = "交易编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "tradingtype_name";
                    $field[$k]["fieldstring"] = "交易类型";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

//                    $field[$k]["fieldname"] = "coursetype_cnname";
//                    $field[$k]["fieldstring"] = "班组名称";
//                    $field[$k]["show"] = 0;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldname"] = "coursecat_cnname";
//                    $field[$k]["fieldstring"] = "班种名称";
//                    $field[$k]["show"] = 0;
//                    $field[$k]["custom"] = 1;
//                    $k++;

                    $field[$k]["fieldname"] = "student_cnname";
                    $field[$k]["fieldstring"] = "中文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_enname";
                    $field[$k]["fieldstring"] = "英文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_branch";
                    $field[$k]["fieldstring"] = "学员编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_status";
                    $field[$k]["fieldstring"] = "订单状态";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_from";
                    $field[$k]["fieldstring"] = "订单来源";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_allprice";
                    $field[$k]["fieldstring"] = "订单总额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_paymentprice";
                    $field[$k]["fieldstring"] = "实付金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_paidprice";
                    $field[$k]["fieldstring"] = "已付金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_arrearageprice";
                    $field[$k]["fieldstring"] = "欠费金额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_createtime";
                    $field[$k]["fieldstring"] = "下单时间";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                } elseif ($val == 'TransferIn' || $val == 'TransferOut') {
                    $field = array();
                    $k = 0;

                    $field[$k]["fieldname"] = "trading_pid";
                    $field[$k]["fieldstring"] = "交易编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "tradingtype_name";
                    $field[$k]["fieldstring"] = "交易类型";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

//                    $field[$k]["fieldname"] = "coursetype_cnname";
//                    $field[$k]["fieldstring"] = "班组名称";
//                    $field[$k]["show"] = 0;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldname"] = "coursecat_cnname";
//                    $field[$k]["fieldstring"] = "班种名称";
//                    $field[$k]["show"] = 0;
//                    $field[$k]["custom"] = 1;
//                    $k++;

                    $field[$k]["fieldname"] = "to_school_cnname";
                    $field[$k]["fieldstring"] = "转入学校";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "to_school_branch";
                    $field[$k]["fieldstring"] = "转入校区编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "from_school_cnname";
                    $field[$k]["fieldstring"] = "转出学校";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "from_school_branch";
                    $field[$k]["fieldstring"] = "转出校区编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_cnname";
                    $field[$k]["fieldstring"] = "中文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_enname";
                    $field[$k]["fieldstring"] = "英文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_branch";
                    $field[$k]["fieldstring"] = "学员编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_status";
                    $field[$k]["fieldstring"] = "订单状态";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_from";
                    $field[$k]["fieldstring"] = "订单来源";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_allprice";
                    $field[$k]["fieldstring"] = "订单总额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_createtime";
                    $field[$k]["fieldstring"] = "下单时间";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                } elseif ($val == 'ClassGiving' || $val == 'ReduceCourse') {
                    $field = array();
                    $k = 0;
                    $field[$k]["fieldname"] = "school_cnname";
                    $field[$k]["fieldstring"] = "校区名称";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "school_branch";
                    $field[$k]["fieldstring"] = "校区编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_pid";
                    $field[$k]["fieldstring"] = "交易编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "tradingtype_name";
                    $field[$k]["fieldstring"] = "交易类型";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

//                    $field[$k]["fieldname"] = "coursetype_cnname";
//                    $field[$k]["fieldstring"] = "班组名称";
//                    $field[$k]["show"] = 0;
//                    $field[$k]["custom"] = 1;
//                    $k++;
//
//                    $field[$k]["fieldname"] = "coursecat_cnname";
//                    $field[$k]["fieldstring"] = "班种名称";
//                    $field[$k]["show"] = 0;
//                    $field[$k]["custom"] = 1;
//                    $k++;

                    $field[$k]["fieldname"] = "student_cnname";
                    $field[$k]["fieldstring"] = "中文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_enname";
                    $field[$k]["fieldstring"] = "英文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_branch";
                    $field[$k]["fieldstring"] = "学员编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_status";
                    $field[$k]["fieldstring"] = "订单状态";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_from";
                    $field[$k]["fieldstring"] = "订单来源";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_allprice";
                    $field[$k]["fieldstring"] = "订单总额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_createtime";
                    $field[$k]["fieldstring"] = "下单时间";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                } elseif ($val == 'CourseForward') {
                    $field = array();
                    $k = 0;
                    $field[$k]["fieldname"] = "school_cnname";
                    $field[$k]["fieldstring"] = "校区名称";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "school_branch";
                    $field[$k]["fieldstring"] = "校区编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_pid";
                    $field[$k]["fieldstring"] = "交易编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "tradingtype_name";
                    $field[$k]["fieldstring"] = "交易类型";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "coursetype_cnname";
                    $field[$k]["fieldstring"] = "班组名称";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "coursecat_cnname";
                    $field[$k]["fieldstring"] = "班种名称";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_cnname";
                    $field[$k]["fieldstring"] = "中文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_enname";
                    $field[$k]["fieldstring"] = "英文名";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "student_branch";
                    $field[$k]["fieldstring"] = "学员编号";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_status";
                    $field[$k]["fieldstring"] = "订单状态";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_from";
                    $field[$k]["fieldstring"] = "订单来源";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "order_allprice";
                    $field[$k]["fieldstring"] = "订单总额";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;

                    $field[$k]["fieldname"] = "trading_createtime";
                    $field[$k]["fieldstring"] = "下单时间";
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                }
                break;
            }
        }

        if ($request['is_export'] && $request['is_export'] == '1') {
            foreach ($tradeList as $val) {
                if ($val['tradingtype_code'] == 'CourseForward') {
                    $ForwardOne = $this->DataControl->selectOne("select dealorder_pid,dealorder_status,dealorder_balanceprice,dealorder_forwardprice 
                    from smc_forward_dealorder where trading_pid='{$val['trading_pid']}'");

                    $data = array();
                    $data['school_cnname'] = $val['school_cnname'];
                    $data['school_branch'] = $val['school_branch'];
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['coursetype_cnname'] = $val['coursetype_cnname'];
                    $data['coursecat_cnname'] = $val['coursecat_cnname'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
                    $data['order_status'] = $dealorder_status[$ForwardOne['dealorder_status']];
                    $data['order_from'] = '--';
                    $data['order_allprice'] = $ForwardOne['dealorder_balanceprice'] + $ForwardOne['dealorder_forwardprice'];
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "学员异动交易订单";
                } elseif ($val['tradingtype_code'] == 'Recharge' || $val['tradingtype_code'] == 'DepositCharge') {
                    $orderOne = $this->DataControl->selectOne("select po.order_pid,po.order_type,po.order_status,po.order_from
                    ,po.order_allprice,po.order_paymentprice,po.order_arrearageprice,po.order_paidprice
                    from smc_payfee_order as po
                    where po.trading_pid='{$val['trading_pid']}'");

                    $data = array();
                    $data['school_cnname'] = $val['school_cnname'];
                    $data['school_branch'] = $val['school_branch'];
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
                    $data['order_status'] = $porder_status[$orderOne['order_status']];
                    $data['order_from'] = $order_from[$orderOne['order_from']];
                    $data['order_allprice'] = $orderOne['order_allprice'];
//                    $data['order_pid'] = $orderOne['order_pid'];
//                    $data['order_type'] = $porder_type[$orderOne['order_type']];
                    $data['order_paymentprice'] = $orderOne['order_paymentprice'];
                    $data['order_paidprice'] = $orderOne['order_paidprice'];
                    $data['order_arrearageprice'] = $orderOne['order_arrearageprice'];
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "学员账户交易订单";
                } elseif ($val['tradingtype_code'] == 'Accountrefund') {
                    $refundOne = $this->DataControl->selectOne("select refund_pid,refund_type,refund_payprice,refund_status,refund_specialprice,refund_from,refund_price,refund_class from smc_refund_order where trading_pid='{$val['trading_pid']}'");

                    $data = array();
                    $data['school_cnname'] = $val['school_cnname'];
                    $data['school_branch'] = $val['school_branch'];
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
//                    $data['order_pid'] = $refundOne['refund_pid'];
//                    $data['order_type'] = $rorder_type[$refundOne['refund_type']];
                    $data['order_status'] = $rorder_status[$refundOne['refund_status']];
                    $data['order_from'] = $order_from[$refundOne['refund_from']];
                    $data['order_allprice'] = $refundOne['refund_payprice'];
                    $data['order_paymentprice'] = $refundOne['refund_payprice'];
                    $data['order_paidprice'] = '--';
                    $data['order_arrearageprice'] = '--';
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "学员账户交易订单";
                } elseif ($val['tradingtype_code'] == 'ReduceCourse') {
                    $reduceOne = $this->DataControl->selectOne("select reduceorder_pid,reduceorder_status,reduceorder_figure 
                        from smc_course_reduceorder 
                        where trading_pid='{$val['trading_pid']}'");
                    $reduceorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝', "-2" => '已取消'));

                    $data = array();
                    $data['school_cnname'] = $val['school_cnname'];
                    $data['school_branch'] = $val['school_branch'];
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
//                    $data['order_pid'] = $reduceOne['reduceorder_pid'];
//                    $data['order_type'] = $this->LgStringSwitch('扣课订单');
                    $data['order_status'] = $reduceorder_status[$reduceOne['reduceorder_status']];
                    $data['order_from'] = $this->LgStringSwitch('教师下单');
                    $data['order_allprice'] = $reduceOne['reduceorder_figure'];
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "课次交易订单";
                } elseif ($val['tradingtype_code'] == 'ClassGiving') {
                    $orderOne = $this->DataControl->selectOne("select order_pid,order_status,order_alltimes,order_refusereason from smc_freehour_order where trading_pid='{$val['trading_pid']}'");

                    $freeorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

                    $data = array();
                    $data['school_cnname'] = $val['school_cnname'];
                    $data['school_branch'] = $val['school_branch'];
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
//                    $data['order_pid'] = $orderOne['order_pid'];
//                    $data['order_type'] = $this->LgStringSwitch('课次赠送订单');
                    $data['order_status'] = $freeorder_status[$orderOne['order_status']];
                    $data['order_from'] = $this->LgStringSwitch('教师下单');
                    $data['order_allprice'] = '--';
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "课次交易订单";
                } elseif ($val['tradingtype_code'] == 'TransferIn') {
                    $sql = "select st.*,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as from_school_cnname,sc.school_branch as from_school_branch
                      ,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as to_school_cnname,sch.school_branch as to_school_branch
                      from smc_school_trading as st
                      left join smc_school as sc on sc.school_id=st.from_school_id
                      left join smc_school as sch on sch.school_id=st.to_school_id
                      where trading_topid='{$val['trading_pid']}' and st.company_id='{$val['company_id']}'";

                    $schoolTradeOne = $this->DataControl->selectOne($sql);
                    $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

                    $data = array();
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['to_school_cnname'] = $schoolTradeOne['to_school_cnname'];
                    $data['to_school_branch'] = $schoolTradeOne['to_school_branch'];
                    $data['from_school_cnname'] = $schoolTradeOne['from_school_cnname'];
                    $data['from_school_branch'] = $schoolTradeOne['from_school_branch'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
//                    $data['order_type'] = $this->LgStringSwitch('学员余额转出');
                    $data['order_status'] = $status[$val['trading_status']];
                    $data['order_from'] = $this->LgStringSwitch('教师下单');
                    $data['order_allprice'] = $schoolTradeOne['trading_price'];
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "校异动交易订单";
                } elseif ($val['tradingtype_code'] == 'TransferOut') {
                    $sql = "select st.*,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as from_school_cnname,sc.school_branch as from_school_branch
                      ,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as to_school_cnname,sch.school_branch as to_school_branch
                      from smc_school_trading as st
                      left join smc_school as sc on sc.school_id=st.from_school_id
                      left join smc_school as sch on sch.school_id=st.to_school_id
                      where trading_frompid='{$val['trading_pid']}' and st.company_id='{$val['company_id']}'";
                    $schoolTradeOne = $this->DataControl->selectOne($sql);
                    $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

                    $data = array();
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['to_school_cnname'] = $schoolTradeOne['to_school_cnname'];
                    $data['to_school_branch'] = $schoolTradeOne['to_school_branch'];
                    $data['from_school_cnname'] = $schoolTradeOne['from_school_cnname'];
                    $data['from_school_branch'] = $schoolTradeOne['from_school_branch'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
//                    $data['order_type'] = $this->LgStringSwitch('学员余额转出');
                    $data['order_status'] = $status[$val['trading_status']];
                    $data['order_from'] = $this->LgStringSwitch('教师下单');
                    $data['order_allprice'] = $schoolTradeOne['trading_price'];
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    $tem_name = "校异动交易订单";
                } elseif ($val['tradingtype_code'] == 'PaynewFee' || $val['tradingtype_code'] == 'PayrenewFee'
                    || $val['tradingtype_code'] == 'CourseMakeUp' || $val['tradingtype_code'] == 'PayitemFee') {
                    $orderOne = $this->DataControl->selectOne("select po.order_pid,po.order_type,po.order_status,po.order_from
                    ,po.order_allprice,po.order_paymentprice,po.order_arrearageprice,po.order_paidprice
                    from smc_payfee_order as po
                    where po.trading_pid='{$val['trading_pid']}'");

                    $data = array();
                    $data['school_cnname'] = $val['school_cnname'];
                    $data['school_branch'] = $val['school_branch'];
                    $data['trading_pid'] = $val['trading_pid'];
                    $data['tradingtype_name'] = $val['tradingtype_name'];
                    $data['student_cnname'] = $val['student_cnname'];
                    $data['student_enname'] = $val['student_enname'];
                    $data['student_branch'] = $val['student_branch'];
//                    $data['order_pid'] = $orderOne['order_pid'];
//                    $data['order_type'] = $porder_type[$orderOne['order_type']];
                    $data['order_status'] = $porder_status[$orderOne['order_status']];
                    $data['order_from'] = $order_from[$orderOne['order_from']];
                    $data['order_allprice'] = $orderOne['order_allprice'];
                    $data['order_paymentprice'] = $orderOne['order_paymentprice'];
                    $data['order_paidprice'] = $orderOne['order_paidprice'];
                    $data['order_arrearageprice'] = $orderOne['order_arrearageprice'];
                    $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);

                    if ($val['tradingtype_code'] == 'PayitemFee') {
                        $tem_name = "教材杂费订单";
                    } else {
                        $tem_name = "课程缴费订单";
                    }
                }
                $outexceldata[] = $data;
            }

            foreach ($field as $key => $fieldOne) {
                if ($fieldOne['show'] == 1) {
                    $excelheader[$key] = $fieldOne['fieldstring'];
                    $excelfileds[$key] = $fieldOne['fieldname'];
                }
            }
            query_to_excel($excelheader, $outexceldata, $excelfileds, $tem_name . '.xlsx');
            exit;
        }


        foreach ($tradeList as $val) {
            $data = array();
            $data['trading_pid'] = $val['trading_pid'];
            $data['student_id'] = $val['student_id'];
            $data['student_cnname'] = $val['student_cnname'];
            $data['student_enname'] = $val['student_enname'];
            $data['student_branch'] = $val['student_branch'];
            $data['school_cnname'] = $val['school_cnname'];
            $data['school_branch'] = $val['school_branch'];
            $data['school_isprotocol'] = $val['school_isprotocol'];
            $data['tradingtype_name'] = $val['tradingtype_name'];
            $data['trading_createtime'] = date("Y-m-d", $val['trading_createtime']);
            $data['coursetype_cnname'] = $val['coursetype_cnname'];
            $data['coursecat_cnname'] = $val['coursecat_cnname'];

            if ($val['tradingtype_code'] == 'CourseForward') {
                $ForwardOne = $this->DataControl->selectOne("select dealorder_pid,dealorder_status,dealorder_balanceprice,dealorder_forwardprice 
                    from smc_forward_dealorder where trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $ForwardOne['dealorder_pid'];
                $data['order_type'] = $this->LgStringSwitch('课程结转订单');
                $data['order_status'] = $dealorder_status[$ForwardOne['dealorder_status']];
                $data['order_from'] = '--';
                $data['order_allprice'] = $ForwardOne['dealorder_balanceprice'] + $ForwardOne['dealorder_forwardprice'];
            } elseif ($val['tradingtype_code'] == 'Recharge' || $val['tradingtype_code'] == 'DepositCharge') {
                $orderOne = $this->DataControl->selectOne("select po.order_pid,po.order_type,po.order_status,po.order_from,po.order_allprice,po.order_paymentprice,po.order_arrearageprice,po.order_paidprice
                from smc_payfee_order as po
                where po.trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $orderOne['order_pid'];
                $data['order_type'] = $porder_type[$orderOne['order_type']];
                $data['order_status'] = $porder_status[$orderOne['order_status']];
                $data['order_from'] = $order_from[$orderOne['order_from']];
                $data['order_allprice'] = $orderOne['order_allprice'];
                $data['order_paymentprice'] = $orderOne['order_paymentprice'];
                $data['order_paidprice'] = $orderOne['order_paidprice'];
                $data['order_arrearageprice'] = $orderOne['order_arrearageprice'];
            } elseif ($val['tradingtype_code'] == 'Accountrefund') {
                $refundOne = $this->DataControl->selectOne("select refund_pid,refund_type,refund_payprice,refund_status,refund_specialprice,refund_from,refund_price,refund_class from smc_refund_order where trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $refundOne['refund_pid'];
                $data['order_type'] = $rorder_type[$refundOne['refund_type']];
                $data['order_status'] = $rorder_status[$refundOne['refund_status']];
                $data['order_from'] = $order_from[$refundOne['refund_from']];
                $data['order_allprice'] = $refundOne['refund_payprice'];
                $data['order_paymentprice'] = $refundOne['refund_payprice'];
                $data['order_paidprice'] = '--';
                $data['order_arrearageprice'] = '--';
            } elseif ($val['tradingtype_code'] == 'ReduceCourse') {
                $reduceOne = $this->DataControl->selectOne("select reduceorder_pid,reduceorder_status,reduceorder_figure from smc_course_reduceorder where trading_pid='{$val['trading_pid']}'");
                $reduceorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝', "-2" => '已取消'));

                $data['order_pid'] = $reduceOne['reduceorder_pid'];
                $data['order_type'] = $this->LgStringSwitch('扣课订单');
                $data['order_status'] = $reduceorder_status[$reduceOne['reduceorder_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $reduceOne['reduceorder_figure'];
            } elseif ($val['tradingtype_code'] == 'ClassGiving') {
                $orderOne = $this->DataControl->selectOne("select order_pid,order_status,order_alltimes,order_refusereason from smc_freehour_order where trading_pid='{$val['trading_pid']}'");

                $freeorder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

                $data['order_pid'] = $orderOne['order_pid'];
                $data['order_type'] = $this->LgStringSwitch('课次赠送订单');
                $data['order_status'] = $freeorder_status[$orderOne['order_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = '--';
            } elseif ($val['tradingtype_code'] == 'TransferIn') {
                $sql = "select st.*,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as from_school_cnname,sc.school_branch as from_school_branch
                      ,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as to_school_cnname,sch.school_branch as to_school_branch
                      from smc_school_trading as st
                      left join smc_school as sc on sc.school_id=st.from_school_id
                      left join smc_school as sch on sch.school_id=st.to_school_id
                      where trading_topid='{$val['trading_pid']}' and st.company_id='{$val['company_id']}'";

                $schoolTradeOne = $this->DataControl->selectOne($sql);
                $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));
                $data['order_type'] = $this->LgStringSwitch('学员余额转出');
                $data['order_status'] = $status[$val['trading_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $schoolTradeOne['trading_price'];
                $data['from_school_cnname'] = $schoolTradeOne['from_school_cnname'];
                $data['from_school_branch'] = $schoolTradeOne['from_school_branch'];
                $data['to_school_cnname'] = $schoolTradeOne['to_school_cnname'];
                $data['to_school_branch'] = $schoolTradeOne['to_school_branch'];
            } elseif ($val['tradingtype_code'] == 'TransferOut') {
                $sql = "select st.*,(case when sc.school_shortname='' then sc.school_cnname else sc.school_shortname end) as from_school_cnname,sc.school_branch as from_school_branch
                      ,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as to_school_cnname,sch.school_branch as to_school_branch
                      from smc_school_trading as st
                      left join smc_school as sc on sc.school_id=st.from_school_id
                      left join smc_school as sch on sch.school_id=st.to_school_id
                      where trading_frompid='{$val['trading_pid']}' and st.company_id='{$val['company_id']}'";
                $schoolTradeOne = $this->DataControl->selectOne($sql);
                $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

                $data['order_type'] = $this->LgStringSwitch('学员余额转出');
                $data['order_status'] = $status[$val['trading_status']];
                $data['order_from'] = $this->LgStringSwitch('教师下单');
                $data['order_allprice'] = $schoolTradeOne['trading_price'];
                $data['from_school_cnname'] = $schoolTradeOne['from_school_cnname'];
                $data['from_school_branch'] = $schoolTradeOne['from_school_branch'];
                $data['to_school_cnname'] = $schoolTradeOne['to_school_cnname'];
                $data['to_school_branch'] = $schoolTradeOne['to_school_branch'];
            } elseif ($val['tradingtype_code'] == 'PaynewFee' || $val['tradingtype_code'] == 'PayrenewFee' || $val['tradingtype_code'] == 'CourseMakeUp' || $val['tradingtype_code'] == 'PayitemFee') {
                $orderOne = $this->DataControl->selectOne("select po.order_pid,po.order_type,po.order_status,po.order_from,po.order_allprice,po.order_paymentprice,po.order_arrearageprice,po.order_paidprice
                from smc_payfee_order as po
                where po.trading_pid='{$val['trading_pid']}'");

                $data['order_pid'] = $orderOne['order_pid'];
                $data['order_type'] = $porder_type[$orderOne['order_type']];
                $data['order_status'] = $porder_status[$orderOne['order_status']];
                $data['order_from'] = $order_from[$orderOne['order_from']];
                $data['order_allprice'] = $orderOne['order_allprice'];
                $data['order_paymentprice'] = $orderOne['order_paymentprice'];
                $data['order_paidprice'] = $orderOne['order_paidprice'];
                $data['order_arrearageprice'] = $orderOne['order_arrearageprice'];
            }
            $list[] = $data;
        }

        $data = array();
        $count_sql = "select st.trading_id,
                     (select co.coursetype_id from smc_forward_dealorder as fd,smc_forward_dealorder_course as dc,smc_course as co 
                     where fd.dealorder_pid = dc.dealorder_pid and dc.course_id = co.course_id and fd.trading_pid = st.trading_pid limit 1) as coursetype_id,
                     (select co.coursecat_id from smc_forward_dealorder as fd,smc_forward_dealorder_course as dc,smc_course as co 
                     where fd.dealorder_pid = dc.dealorder_pid and dc.course_id = co.course_id and fd.trading_pid = st.trading_pid limit 1) as coursecat_id
            from smc_student_trading as st
            left join smc_student as s on s.student_id=st.student_id
            where {$datawhere} 
            and st.company_id='{$request['company_id']}' 
            and st.tradingtype_code<>'Subscribed' 
            and st.tradingtype_code<>'MonthlyShare'
            having {$having}";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $list;
        $data['field'] = $field;

        return $data;
    }

    function getSanOrderList($request)
    {
        $datawhere = " a.company_id='{$this->company_id}' and a.trading_status=1 and a.tradingtype_code in ('PaynewFee','PayrenewFee','Recharge')";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (d.student_cnname like '%{$request['keyword']}%' or d.student_enname like '%{$request['keyword']}%' or d.student_idcard like '%{$request['keyword']}%' or d.student_branch like '%{$request['keyword']}%' or a.trading_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['tradingtype_code']) && $request['tradingtype_code'] !== '') {
            $datawhere .= " and a.tradingtype_code in ('" . implode("','", explode(',', $request['tradingtype_code'])) . "')";

            if (isset($request['status']) && $request['status'] != '') {

                if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'PayitemFee' || $request['tradingtype_code'] == 'CourseMakeUp' || $request['tradingtype_code'] == 'Recharge') {

                    $datawhere .= " and exists (select x.trading_pid from smc_payfee_order as x where x.trading_pid=a.trading_pid and x.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'Accountrefund') {

                    $datawhere .= " and exists (select x.trading_pid from smc_refund_order as x where x.trading_pid=a.trading_pid and x.refund_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'CourseForward') {

                    $datawhere .= " and exists (select x.trading_pid from smc_forward_dealorder as po where x.trading_pid=a.trading_pid and x.dealorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

                    $datawhere .= " and exists (select x.trading_pid from smc_course_reduceorder as po where x.trading_pid=a.trading_pid and x.reduceorder_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'ClassGiving') {

                    $datawhere .= " and exists (select x.trading_pid from smc_freehour_order as po where x.trading_pid=a.trading_pid and x.order_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferIn') {

                    $datawhere .= " and exists (select x.trading_topid from smc_school_trading as po where x.trading_topid=a.trading_pid and x.trading_status='{$request['status']}')";

                } elseif ($request['tradingtype_code'] == 'TransferOut') {

                    $datawhere .= " and exists (select x.trading_frompid from smc_school_trading as po where x.trading_frompid=a.trading_pid and x.trading_status='{$request['status']}')";

                }
            }
        }

        if (isset($request['order_status']) && $request['order_status'] !== '') {
            $datawhere .= " and b.order_status = '{$request['order_status']}'";
        }

        if (isset($request['is_merge']) && $request['is_merge'] !== '') {
            if($request['is_merge']==1){
                $datawhere .= " and b.mergeorder_pid <> ''";
            }else{
                $datawhere .= " and b.mergeorder_pid = ''";
            }

        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id = '{$request['school_id']}'";
        }

        if (isset($request['info_iscontinuepay']) && $request['info_iscontinuepay'] !== '') {
            $datawhere .= " and b.info_iscontinuepay = '{$request['info_iscontinuepay']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.trading_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.trading_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
        }

        $sql = "select b.order_id as info_id,c.school_cnname,c.school_branch,b.trading_pid,d.student_cnname,d.student_enname,d.student_branch,b.order_status,b.order_from,b.order_allprice,b.order_paymentprice,b.order_paidprice,b.order_arrearageprice,FROM_UNIXTIME(b.order_createtime,'%Y-%m-%d %H:%i') as order_createtime,b.info_iscontinuepay
            from smc_student_trading as a 
            inner join smc_payfee_order as b on b.trading_pid=a.trading_pid
            left join smc_school as c on c.school_id=a.school_id
            left join smc_student as d on d.student_id=a.student_id
            where {$datawhere}
            order by b.order_id desc
            ";

        if ($request['is_export'] && $request['is_export'] == '1') {
            $tradeList = $this->DataControl->selectClear($sql);
        } else {
            $tradeList = $this->DataControl->selectClear($sql . " limit {$pagestart},{$num}");
        }

        if (!$tradeList) {
            $tradeList = array();
        }

        $list = array();

        $order_from = $this->LgArraySwitch(array("0" => "家长自订", "1" => "教师下单", "2" => "系统同步"));
        $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        $porder_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

        $tem_name = '';
        $excelheader = array();
        $excelfileds = array();
        $outexceldata = array();

        $field = array();
        $k = 0;
        $field[$k]["fieldname"] = "school_cnname";
        $field[$k]["fieldstring"] = "校区名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "school_branch";
        $field[$k]["fieldstring"] = "校区编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "trading_pid";
        $field[$k]["fieldstring"] = "交易编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_cnname";
        $field[$k]["fieldstring"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_enname";
        $field[$k]["fieldstring"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "student_branch";
        $field[$k]["fieldstring"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_status_name";
        $field[$k]["fieldstring"] = "订单状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_from_name";
        $field[$k]["fieldstring"] = "订单来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_allprice";
        $field[$k]["fieldstring"] = "订单总额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_paymentprice";
        $field[$k]["fieldstring"] = "实付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_paidprice";
        $field[$k]["fieldstring"] = "已付金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_arrearageprice";
        $field[$k]["fieldstring"] = "欠费金额";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldname"] = "order_createtime";
        $field[$k]["fieldstring"] = "下单时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['is_export'] && $request['is_export'] == '1') {
            foreach ($tradeList as $val) {
                $data = array();
                $data['school_cnname'] = $val['school_cnname'];
                $data['school_branch'] = $val['school_branch'];
                $data['trading_pid'] = $val['trading_pid'];
                $data['student_cnname'] = $val['student_cnname'];
                $data['student_enname'] = $val['student_enname'];
                $data['student_branch'] = $val['student_branch'];
                $data['order_status_name'] = $porder_status[$val['order_status']];
                $data['order_from_name'] = $order_from[$val['order_from']];
                $data['order_allprice'] = $val['order_allprice'];
                $data['order_paymentprice'] = $val['order_paymentprice'];
                $data['order_paidprice'] = $val['order_paidprice'];
                $data['order_arrearageprice'] = $val['order_arrearageprice'];
                $data['order_createtime'] = $val['order_createtime'];
                $data['info_iscontinuepay'] = $status[$val['info_iscontinuepay']];

                $outexceldata[] = $data;
            }

            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '交易编号', '中文名', '英文名', '学员编号', '订单状态', '订单来源', '订单总额', '实付金额', '已付金额', '欠费金额', '下单时间', '是否三期连缴'));
            $excelfileds = array('school_cnname', 'school_branch', 'trading_pid', 'student_cnname', 'student_enname', 'student_branch', 'order_status_name', 'order_from_name', 'order_allprice', 'order_paymentprice', 'order_paidprice', 'order_arrearageprice', 'order_createtime', 'info_iscontinuepay');

            query_to_excel($excelheader, $outexceldata, $excelfileds, $tem_name . '三期连缴名单.xlsx');
            exit;
        }

        foreach($tradeList as &$tradeOne){

            $tradeOne['order_status_name'] = $porder_status[$tradeOne['order_status']];
            $tradeOne['order_from_name'] = $order_from[$tradeOne['order_from']];
        }

        $data = array();
        $count_sql = "select b.order_id
            from smc_student_trading as a 
            inner join smc_payfee_order as b on b.trading_pid=a.trading_pid
            left join smc_school as c on c.school_id=a.school_id
            left join smc_student as d on d.student_id=a.student_id
            where {$datawhere}";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $tradeList;
        $data['field'] = $field;

        return $data;
    }

    function changeRegisterStatus($request){

        $orderOne=$this->DataControl->getFieldOne("smc_payfee_order","info_iscontinuepay","order_id='{$request['info_id']}'");

        if(!$orderOne){
            $this->error = true;
            $this->errortip = "订单数据不存在";
            return false;
        }

        $data=array();
        $data['info_iscontinuepay']=1-$orderOne['info_iscontinuepay'];
        $data['order_updatatime']=time();

        if($this->DataControl->updateData("smc_payfee_order","order_id='{$request['info_id']}'",$data)){
            $this->oktip = '操作成功';
            return true;
        }else{
            $this->error = true;
            $this->errortip = "变更失败";
            return false;
        }



    }

    function orderStatusList($request)
    {
        if (!isset($request['tradingtype_code']) || $request['tradingtype_code'] == '') {
            $this->error = true;
            $this->errortip = "请先选择交易类型";
            return false;
        }

        $data = array();
        if ($request['tradingtype_code'] == 'PaynewFee' || $request['tradingtype_code'] == 'PayrenewFee' || $request['tradingtype_code'] == 'CourseMakeUp') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'PayitemFee') {

            $status = $this->LgArraySwitch(array('1' => '待支付', '2' => '支付中', '4' => '已完成', '-1' => '已取消'));

        } elseif ($request['tradingtype_code'] == 'Recharge') {

            $status = $this->LgArraySwitch(array('1' => '待支付', '4' => '已完成', '-1' => '已取消'));

        } elseif ($request['tradingtype_code'] == 'Accountrefund') {

            $status = $this->LgArraySwitch(array('0' => '待审核(校园)', '1' => '待审核(集团)', '2' => '待处理', '3' => '待确定金额', '4' => '已完成', '-1' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'CourseForward') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'ReduceCourse') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'ClassGiving') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-2' => '审核拒绝'));

        } elseif ($request['tradingtype_code'] == 'TransferIn' || $request['tradingtype_code'] == 'TransferOut') {

            $status = $this->LgArraySwitch(array('0' => '待审核', '2' => '已完成', '-1' => '审核拒绝'));

        } else {
            $this->error = true;
            $this->errortip = "该类型无状态需求";
            return false;
        }

        if ($status) {
            foreach ($status as $key => $val) {
                $tem_data = array();
                $tem_data['status'] = $key;
                $tem_data['name'] = $val;
                $data[] = $tem_data;
            }
        }

        return $data;
    }

    function getProtocolList($request)
    {
        $datawhere = " p.company_id = '{$request['company_id']}' AND p.order_pid = '{$request['order_pid']}' and p.protocol_isdel = '0'";
        $sql = "
            SELECT
                p.protocol_id,
                p.protocol_pid,
                p.order_pid,
                c.course_cnname,
                protocol_createtime,
                p.protocol_nums,
                p.protocol_price,
                p.protocol_issign,
                p.protocol_isaudit,
                p.protocol_isaudit AS protocol_isaudit_name,
                p.protocol_isinvoice,
                p.protocol_isinvoice AS protocol_isinvoice_name,
                i.invoice_pdfurl,
                i.invoice_voucher,
                i.invoice_type,
                i.invoice_status
            FROM
                smc_student_protocol AS p
                LEFT JOIN smc_course AS c ON p.course_id = c.course_id
                LEFT JOIN shop_invoice AS i ON i.protocol_id = p.protocol_id
            WHERE {$datawhere} GROUP BY p.protocol_id";

        $districtList = $this->DataControl->selectClear($sql);

        $status = $this->LgArraySwitch(array("0" => "未生效", "1" => "已生效"));
        $statuss = $this->LgArraySwitch(array("0" => "未开票", "1" => "已开票"));
        $statusss = $this->LgArraySwitch(array("0" => "否", "1" => "是"));

        $sign = $this->DataControl->getFieldOne("gmc_company", "company_issign", "company_id = '{$request['company_id']}'");

        if ($districtList) {
            foreach ($districtList as &$val) {
                $val['protocol_isaudit_name'] = $status[$val['protocol_isaudit_name']];
                $val['protocol_isinvoice_name'] = $statuss[$val['protocol_isinvoice_name']];
                if ($val['invoice_status'] == '1') {
                    $val['invoice_status_name'] = '已开票';
                } else {
                    $val['invoice_status_name'] = '未开票';
                }
                if ($val['invoice_type'] == '1') {
                    $val['invoice_pdfurl'] = $val['invoice_voucher'];
                }
                if ($sign['company_issign'] == '1') {
                    $val['protocol_issign'] = $statusss[$val['protocol_issign']];
                }
            }
        }

        if ($sign['company_issign'] == '1') {
            $fieldstring = array('protocol_pid', 'course_cnname', 'order_pid', 'protocol_nums', 'protocol_price', 'protocol_isaudit_name', 'protocol_issign', 'invoice_status_name');
            $fieldname = array('合同编号', '课程名称', '订单编号', '合同课程数', '合同价格', '是否生效', '是否签字', '是否开票');
            $fieldcustom = array("1", "1", "0", "1", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "0", "1", "1", "1", "1", "1");
        } else {
            $fieldstring = array('protocol_pid', 'course_cnname', 'order_pid', 'protocol_nums', 'protocol_price', 'protocol_isaudit_name', 'invoice_status_name');
            $fieldname = array('合同编号', '课程名称', '订单编号', '合同课程数', '合同价格', '是否审核', '是否开票');
            $fieldcustom = array("1", "1", "0", "1", "1", "1", "1");
            $fieldshow = array("1", "1", "0", "1", "1", "1", "1");
        }


        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = $this->LgStringSwitch(trim($fieldname[$i]));
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($districtList) {
            $result['list'] = $districtList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无合同", 'result' => $result);
        }

        return $res;
    }

    function reduceOrderList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_idcard like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or cr.trading_pid like '%{$request['keyword']}%' or cr.reduceorder_pid like '%{$request['keyword']}%')";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and cr.reduceorder_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time']);
            $datawhere .= " and cr.reduceorder_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and cr.reduceorder_updatatime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time']);
            $datawhere .= " and cr.reduceorder_updatatime <= '{$firstday}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and sch.school_id = '{$request['school_id']}'";
        }
        if (isset($request['reduceorder_status']) && $request['reduceorder_status'] !== '') {
            $datawhere .= " and cr.reduceorder_status = '{$request['reduceorder_status']}'";
        }
        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
        }
        if ($request['dataequity'] == '1') {
            $sql = "select cr.reduceorder_pid,sch.school_id,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,sch.school_branch
            ,sc.course_branch,sc.course_cnname,s.student_cnname,s.student_branch,cr.reduceorder_time,cr.reduceorder_figure,cr.reduceorder_status,cr.reduceorder_note,cr.reduceorder_status,cr.reduceorder_createtime,cr.reduceorder_updatatime
            from smc_course_reduceorder as cr
            left join smc_student as s on s.student_id=cr.student_id
            left join smc_school as sch on sch.school_id=cr.school_id
            left join smc_course as sc on sc.course_id=cr.course_id
            where {$datawhere} and cr.company_id='{$request['company_id']}'
            order by cr.reduceorder_createtime desc 
            ";
        } else {
            $sql = "select cr.reduceorder_pid,sch.school_id,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,sch.school_branch
            ,sc.course_branch,sc.course_cnname,s.student_cnname,s.student_branch,cr.reduceorder_time,cr.reduceorder_figure,cr.reduceorder_status,cr.reduceorder_note,cr.reduceorder_status,cr.reduceorder_createtime,cr.reduceorder_updatatime
            from smc_course_reduceorder as cr
            left join smc_student as s on s.student_id=cr.student_id
            left join smc_school as sch on sch.school_id=cr.school_id
            left join smc_course as sc on sc.course_id=cr.course_id
            where {$datawhere} and cr.company_id='{$request['company_id']}' 
            and cr.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}')
            order by cr.reduceorder_createtime desc 
            ";
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $status = array('0' => '申请中', '1' => '通过申请', '-1' => '已拒绝', '-2' => '已取消');

            foreach ($dateexcelarray as &$val) {
                $val['reduceorder_status_name'] = $status[$val['reduceorder_status']];
                $val['reduceorder_createtime'] = date("Y-m-d", $val['reduceorder_createtime']);
                if ($val['reduceorder_updatatime']) {
                    $val['reduceorder_updatatime'] = date("Y-m-d", $val['reduceorder_updatatime']);
                } else {
                    $val['reduceorder_updatatime'] = '--';
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['reduceorder_pid'] = $dateexcelvar['reduceorder_pid'];//订单编号
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];//学员名称
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];//学员编号
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['reduceorder_time'] = $dateexcelvar['reduceorder_time'];//减少课程次数
                    $datearray['reduceorder_figure'] = $dateexcelvar['reduceorder_figure'];//减少课程金额
                    $datearray['reduceorder_status_name'] = $dateexcelvar['reduceorder_status_name'];//订单状态
                    $datearray['reduceorder_note'] = $dateexcelvar['reduceorder_note'];//备注
                    $datearray['reduceorder_createtime'] = $dateexcelvar['reduceorder_createtime'];//申请时间
                    $datearray['reduceorder_updatatime'] = $dateexcelvar['reduceorder_updatatime'];//审核时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('订单编号', '校区名称', '校区编号', '课程别名称', '课程别编号', '学员名称', '学员编号', '减少课程次数', '减少课程金额', '订单状态', '备注', '申请时间', '审核时间'));
            $excelfileds = array('reduceorder_pid', 'school_cnname', 'school_branch', 'course_cnname', 'course_branch', 'student_cnname', 'student_branch', 'reduceorder_time', 'reduceorder_figure', 'reduceorder_status_name', 'reduceorder_note', 'reduceorder_createtime', 'reduceorder_updatatime');
            $fielname = $this->LgStringSwitch("课时扣除订单审核");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }

        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }

        $status = array('0' => '申请中', '1' => '通过申请', '-1' => '已拒绝', '-2' => '已取消');

        foreach ($orderList as &$orderOne) {
            $orderOne['reduceorder_status_name'] = $status[$orderOne['reduceorder_status']];
            if ($orderOne['reduceorder_createtime']) {
                $orderOne['reduceorder_createtime'] = date("Y-m-d", $orderOne['reduceorder_createtime']);
            } else {
                $orderOne['reduceorder_createtime'] = '--';
            }
            if ($orderOne['reduceorder_updatatime']) {
                $orderOne['reduceorder_updatatime'] = date("Y-m-d", $orderOne['reduceorder_updatatime']);
            } else {
                $orderOne['reduceorder_updatatime'] = '--';
            }
        }

        $data = array();
        if ($request['dataequity'] == '1') {
            $count_sql = "select cr.reduceorder_id
          from smc_course_reduceorder as cr
          left join smc_student as s on s.student_id=cr.student_id
          left join smc_school as sch on sch.school_id=cr.school_id
          where {$datawhere} and cr.company_id='{$request['company_id']}'";
        } else {
            $count_sql = "select cr.reduceorder_id
          from smc_course_reduceorder as cr
          left join smc_student as s on s.student_id=cr.student_id
          left join smc_school as sch on sch.school_id=cr.school_id
          where {$datawhere} and cr.company_id='{$request['company_id']}' and cr.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}')";
        }

        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $schoolList = $this->DataControl->selectClear("select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname from smc_school as s left join smc_course_reduceorder as cr on cr.school_id=s.school_id where cr.company_id='{$this->company_id}' and cr.reduceorder_status<>-2 group by s.school_id");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['list'] = $orderList;
        $data['school'] = $schoolList;

        return $data;

    }

    function refundOrderList($request)
    {
        $datawhere = " 1 ";
        $datawhere .= " and o.company_id = '{$this->company_id}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or o.trading_pid like '%{$request['keyword']}%' or o.refund_pid like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$request['school_id']}'";
        }
        if (isset($request['order_status']) && $request['order_status'] !== "") {
            $datawhere .= " and o.order_status ='{$request['order_status']}'";
        }
        if (isset($request['refund_status']) && $request['refund_status'] !== "") {
            $datawhere .= " and o.refund_status in ({$request['refund_status']})";
        }
        if (isset($request['refund_isspecial']) && $request['refund_isspecial'] !== "") {
            $datawhere .= " and o.refund_isspecial ='{$request['refund_isspecial']}'";
        }
        if (isset($request['refund_tradeclass']) && $request['refund_tradeclass'] !== "") {
            $datawhere .= " and o.refund_tradeclass ='{$request['refund_tradeclass']}'";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and o.refund_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time']);
            $datawhere .= " and o.refund_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and o.refund_updatatime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time']);
            $datawhere .= " and o.refund_updatatime <= '{$firstday}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = o.school_id)";
        }

        $sql = "
            SELECT
                o.refund_id,
                o.trading_pid,
                o.refund_isspecial,
                o.refund_specialprice,
                o.refund_pid,
                o.refund_status,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                t.student_cnname,
                t.student_branch,
                o.refund_name,
                o.refund_mobile,
                o.refund_bank,
                o.refund_accountname,
                o.refund_bankcard,
                o.refund_reason,
                o.refund_price,
                o.refund_payprice,
                o.refund_tradeclass,
                o.refund_createtime,
                o.refund_updatatime
            FROM
                smc_refund_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
            WHERE
                {$datawhere} and o.refund_status <> 0
            ORDER BY
                o.refund_id DESC ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $statuso = $this->LgArraySwitch(array("1" => "审核通过", "2" => "处理中", "3" => "确定金额", "4" => "完成退款", "-1" => "已拒绝"));
            $tradeclass = $this->LgArraySwitch(array("0" => "普通退费订单", "1" => "教材退费订单", "2" => "杂项退费订单", "3" => '定金退费订单'));
            $status = $this->LgArraySwitch(array("0" => "不含", "1" => "有特殊申请"));

            foreach ($dateexcelarray as &$val) {
                $val['refund_status_name'] = $statuso[$val['refund_status']];
                $val['refund_tradeclass_name'] = $tradeclass[$val['refund_tradeclass']];
                $val['refund_isspecial_name'] = $status[$val['refund_isspecial']];
                $val['refund_createtime'] = date("Y-m-d", $val['refund_createtime']);
                if ($val['refund_updatatime']) {
                    $val['refund_updatatime'] = date("Y-m-d", $val['refund_updatatime']);
                } else {
                    $val['refund_updatatime'] = '--';
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['refund_pid'] = $dateexcelvar['refund_pid'];//退款订单编号
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['refund_isspecial_name'] = $dateexcelvar['refund_isspecial_name'];//特殊申请
                    $datearray['refund_name'] = $dateexcelvar['refund_name'];//退款人联系人
                    $datearray['refund_mobile'] = $dateexcelvar['refund_mobile'];//退款人联系方式
                    $datearray['refund_bank'] = $dateexcelvar['refund_bank'];//开户行
                    $datearray['refund_accountname'] = $dateexcelvar['refund_accountname'];//开户名
                    $datearray['refund_bankcard'] = $dateexcelvar['refund_bankcard'];//银行卡号
                    $datearray['refund_reason'] = $dateexcelvar['refund_reason'];//退款原因
                    $datearray['refund_payprice'] = $dateexcelvar['refund_payprice'];//实际退款金额
                    $datearray['refund_specialprice'] = $dateexcelvar['refund_specialprice'];//特殊退款金额
                    $datearray['refund_tradeclass_name'] = $dateexcelvar['refund_tradeclass_name'];//退费订单类型
                    $datearray['refund_status_name'] = $dateexcelvar['refund_status_name'];//状态
                    $datearray['refund_createtime'] = $dateexcelvar['refund_createtime'];//申请时间
                    $datearray['refund_updatatime'] = $dateexcelvar['refund_updatatime'];//审核时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('退款订单编号', '校区名称', '校区编号', '学员名称', '学员编号', '特殊申请', '退款人联系人', '退款人联系方式', '开户行', '开户名', '银行卡号', '退款原因', '实际退款金额', '特殊退款金额', '退费订单类型', '状态', '申请时间', '审核时间'));
            $excelfileds = array('refund_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'refund_isspecial_name', 'refund_name', 'refund_mobile', 'refund_bank', 'refund_accountname', 'refund_bankcard', 'refund_reason', 'refund_payprice', 'refund_specialprice', 'refund_tradeclass_name', 'refund_status_name', 'refund_createtime', 'refund_updatatime');

            $fielname = $this->LgStringSwitch("校园退费审核表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $tradeList = $this->DataControl->selectClear($sql);
        }

        if (!$tradeList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }

        if ($tradeList) {
            $status = $this->LgArraySwitch(array("1" => "审核通过", "2" => "处理中", "3" => "确定金额", "4" => "完成退款", "-1" => "已拒绝"));
            $tradeclass = $this->LgArraySwitch(array("0" => "普通退费订单", "1" => "教材退费订单", "2" => "杂项退费订单", "3" => '定金退费订单'));
            $special = $this->LgArraySwitch(array("0" => "不含", "1" => "有特殊申请"));
            foreach ($tradeList as &$val) {
                $val['refund_status_name'] = $status[$val['refund_status']];
                $val['refund_tradeclass_name'] = $tradeclass[$val['refund_tradeclass']];
                $val['refund_isspecial_name'] = $special[$val['refund_isspecial']];
                $val['refund_createtime'] = date("Y-m-d", $val['refund_createtime']);
                if ($val['refund_updatatime']) {
                    $val['refund_updatatime'] = date("Y-m-d", $val['refund_updatatime']);
                } else {
                    $val['refund_updatatime'] = '--';
                }
            }
        }

        $all_num = $this->DataControl->select("
        SELECT
           o.refund_id
        FROM
            smc_refund_order AS o
            LEFT JOIN smc_school AS s ON s.school_id = o.school_id
            left join smc_student as t on o.student_id = t.student_id
        WHERE
            {$datawhere} and o.refund_status <> 0");


        if ($all_num) {
            $allnum = count($all_num);
        } else {
            $allnum = 0;
        }

        $data = array();
        $schoolList = $this->DataControl->selectClear("select s.school_id,s.school_cnname from smc_school as s left join smc_refund_order as cr on cr.school_id=s.school_id where cr.company_id='{$this->company_id}' and cr.refund_status<>0 group by s.school_id");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['allnum'] = $allnum;
        $data['list'] = $tradeList;
        $data['school'] = $schoolList;

        return $data;
    }

    function getPerformanceList($request)
    {
        $sql = "select performance_id,performance_name,performance_class,performance_iscalculated 
              from gmc_code_performance 
              where (company_id=0 or company_id='{$this->company_id}')
              order by company_id asc
              ";

        $list = $this->DataControl->selectClear($sql);

        return $list ? $list : array();

    }

    function getCompanyachieveList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (po.order_pid like '%{$request['keyword']}%' or po.trading_pid like '%{$request['keyword']}%' or st.staffer_cnname like '%{$request['keyword']}%' or st.staffer_branch like '%{$request['keyword']}%' or s.student_cnname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and sa.school_id = '{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sa.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['achieve_type']) && $request['achieve_type'] !== '') {
            $datawhere .= " and sa.achieve_type = '{$request['achieve_type']}'";
        }

        if (isset($request['performance_id']) && $request['performance_id'] !== '') {
            $datawhere .= " and cp.performance_id = '{$request['performance_id']}'";
        }

        if (isset($request['achieve_iscalculated']) && $request['achieve_iscalculated'] !== '') {
            $datawhere .= " and sa.achieve_iscalculated = '{$request['achieve_iscalculated']}'";
        }

        if (isset($request['order_status']) && $request['order_status'] !== '') {
            $datawhere .= " and po.order_status = '{$request['order_status']}'";
        }

        if (isset($request['achieve_status']) && $request['achieve_status'] !== '') {
            $datawhere .= " and sa.achieve_status = '{$request['achieve_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sa.achieve_id,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,cc.coursetype_cnname,cc.coursetype_branch,sch.school_id,sa.achieve_type
,sch.school_branch,st.staffer_id,concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )) as staffer_cnname,st.staffer_branch,cp.performance_name,sa.achieve_number,sa.achieve_price,sa.achieve_iscalculated,sa.achieve_note,sa.achieve_status,po.order_pid,s.student_cnname,s.student_branch,po.order_status,po.student_id,po.order_pid,po.trading_pid
              from smc_staffer_achieve as sa,smc_staffer as st,gmc_code_performance as cp,smc_payfee_order as po,smc_student as s,smc_school as sch,smc_code_coursetype as cc
              where {$datawhere} and sa.staffer_id=st.staffer_id and sa.performance_id=cp.performance_id and sa.order_pid=po.order_pid and sa.school_id=sch.school_id and sa.coursetype_id=cc.coursetype_id
              and po.student_id=s.student_id and sa.company_id='{$this->company_id}'
              order by (case when sch.school_istest=0 and sch.school_isclose=0 then 1 when sch.school_isclose=0 then 2 when sch.school_istest=0 then 3 else 4 end),sch.school_istest asc,field(sch.school_sort,0),sch.school_sort asc,sch.school_createtime asc,sa.achieve_id desc
              ";

        $status = $this->LgArraySwitch(array("0" => "待校区审核", "1" => "待集团审核", "2" => "审核通过", "-1" => "已拒绝", "-2" => "已撤销"));
        $order_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));
        $iscalculated = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        $type = $this->LgArraySwitch(array("0" => "续费", "1" => "招新", "2" => "扩科"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                $datearray['school_branch'] = $dateexcelvar['school_branch'];
                $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                $datearray['performance_name'] = $dateexcelvar['performance_name'];
                $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                $datearray['achieve_price'] = $dateexcelvar['achieve_price'];
                $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                $datearray['student_branch'] = $dateexcelvar['student_branch'];
                $datearray['achieve_note'] = $dateexcelvar['achieve_note'];
                $datearray['order_status_name'] = $order_status[$dateexcelvar['order_status']];
                $datearray['achieve_status_name'] = $dateexcelvar['order_status'] <> '4' ? '--' : $status[$dateexcelvar['achieve_status']];
                $datearray['achieve_iscalculated_name'] = $iscalculated[$dateexcelvar['achieve_iscalculated']];

                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "业绩教师", "业绩明细", "所属班组", "交易编号", "业绩金额", "学员姓名", "学员编号", "备注", "订单状态", "审核状态", "是否计业绩"));
            $excelfileds = array('school_cnname', 'school_branch', 'staffer_cnname', 'performance_name', 'coursetype_cnname', 'trading_pid', 'achieve_price', 'student_cnname', 'student_branch', 'achieve_note', 'order_status_name', 'achieve_status_name', 'achieve_iscalculated_name');

            $tem_name = "教师业绩审核列表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->error = true;
                $this->errortip = "无业绩记录";
                return false;
            }

            foreach ($list as &$listOne) {

                if ($listOne['order_status'] <> '4') {
                    $listOne['achieve_status_name'] = '--';
                } else {
                    $listOne['achieve_status_name'] = $status[$listOne['achieve_status']];
                }
                $listOne['achieve_type_name'] = $type[$listOne['achieve_type']];
                $listOne['achieve_iscalculated_name'] = $iscalculated[$listOne['achieve_iscalculated']];
                $listOne['order_status_name'] = $order_status[$listOne['order_status']];

            }

            $count_sql = "select sa.achieve_id
                  from smc_staffer_achieve as sa,smc_staffer as st,gmc_code_performance as cp,smc_payfee_order as po,smc_student as s
                  where {$datawhere} and sa.staffer_id=st.staffer_id and sa.performance_id=cp.performance_id and sa.order_pid=po.order_pid 
                  and po.student_id=s.student_id and sa.company_id='{$this->company_id}'";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $allnum = $db_nums ? count($db_nums) : 0;

            $data = array();
            $data['allnum'] = $allnum;

            $data['list'] = $list;

            return $data;
        }

    }

    function getachieveOne($request)
    {
        $sql = "select sa.achieve_id,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,sch.school_branch,st.staffer_id,concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )) as staffer_cnname,st.staffer_branch,cp.performance_name,sa.achieve_number,sa.achieve_price,sa.achieve_iscalculated,sa.achieve_note,sa.achieve_status,sa.achieve_type,po.order_pid,s.student_cnname,s.student_branch,po.order_status
              from smc_staffer_achieve as sa,smc_staffer as st,gmc_code_performance as cp,smc_payfee_order as po,smc_student as s,smc_school as sch
              where sa.staffer_id=st.staffer_id and sa.performance_id=cp.performance_id and sa.order_pid=po.order_pid and sa.school_id=sch.school_id
              and po.student_id=s.student_id and sa.achieve_id='{$request['achieve_id']}'
              order by sa.achieve_id desc";

        $achieveOne = $this->DataControl->selectOne($sql);

        if (!$achieveOne) {
            $this->error = true;
            $this->errortip = "无对应业绩";
            return false;
        }

        $status = $this->LgArraySwitch(array("0" => "待校区审核", "1" => "待集团审核", "2" => "已审核", "-1" => "已拒绝", "-2" => "已撤销"));
        $order_status = $this->LgArraySwitch(array('0' => '待审核', '1' => '待支付', '2' => '支付中', '3' => '处理中', '4' => '已完成', '-1' => '已取消', '-2' => '审核拒绝'));
        $iscalculated = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        $type = $this->LgArraySwitch(array("0" => "招新", "1" => "扩科"));

        $achieveOne['achieve_status_name'] = $status[$achieveOne['achieve_status']];
        $achieveOne['achieve_iscalculated_name'] = $iscalculated[$achieveOne['achieve_iscalculated']];
        $achieveOne['order_status_name'] = $order_status[$achieveOne['order_status']];

        $sql = "select FROM_UNIXTIME(t.tracks_time,'%Y-%m-%d %H:%i:%s') as tracks_time,t.tracks_information,t.tracks_note
              from smc_staffer_achieve_track as t where t.achieve_id='{$achieveOne['achieve_id']}'
              order by t.tracks_id desc
              ";

        $trackList = $this->DataControl->selectClear($sql);

        $sql = "select pay_typename,FROM_UNIXTIME(pay_successtime,'%Y-%m-%d %H:%i:%s') as pay_successtime,pay_pid,pay_price from smc_payfee_order_pay where order_pid='{$achieveOne['order_pid']}' and pay_issuccess=1";
        $payList = $this->DataControl->selectClear($sql);

        $achieveOne['trackList'] = $trackList ? $trackList : array();
        $achieveOne['payList'] = $payList ? $payList : array();

        $data = array();
        $data['staffer_cnname'] = $achieveOne['staffer_cnname'];
        $data['staffer_branch'] = $achieveOne['staffer_branch'];
        $data['student_cnname'] = $achieveOne['student_cnname'];
        $data['student_branch'] = $achieveOne['student_branch'];
        $data['performance_name'] = $achieveOne['performance_name'];
        $data['achieve_number'] = $achieveOne['achieve_number'];

        $achieveOne['info'][] = $data;


        return $achieveOne;
    }

    function getOrderachieveList($request)
    {

        $sql = "select st.staffer_id,concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )) as staffer_cnname,st.staffer_branch,cp.performance_name,sa.achieve_number,sa.achieve_price,sa.achieve_iscalculated,sa.achieve_note,sa.achieve_status,sa.performance_id,po.order_status
              from smc_staffer_achieve as sa,smc_staffer as st,gmc_code_performance as cp,smc_payfee_order as po
              where sa.staffer_id=st.staffer_id and sa.performance_id=cp.performance_id and po.order_pid=sa.order_pid and sa.order_pid='{$request['order_pid']}'
              order by sa.achieve_id asc
              ";

        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无业绩记录";
            return false;
        }

        $status = $this->LgArraySwitch(array("0" => "待校区审核", "1" => "待集团审核", "2" => "已审核", "-1" => "已拒绝", "-2" => "已撤销"));
        $iscalculated = $this->LgArraySwitch(array("0" => "否", "1" => "是"));


        foreach ($list as &$listOne) {

            if ($listOne['order_status'] <> '4') {
                $listOne['achieve_status_name'] = '--';
            } else {
                $listOne['achieve_status_name'] = $status[$listOne['achieve_status']];
            }
            $listOne['achieve_iscalculated_name'] = $iscalculated[$listOne['achieve_iscalculated']];

        }

        return $list;

    }

    function handleachieve($request)
    {
        $achieveOne = $this->DataControl->getFieldOne("smc_staffer_achieve", "achieve_id,achieve_status,order_pid,achieve_iscalculated,achieve_note", "achieve_id='{$request['achieve_id']}'");

        if (!$achieveOne) {
            $this->error = true;
            $this->errortip = "无对应业绩";
            return false;
        }

        if ($achieveOne['achieve_status'] != 1) {
            $this->error = true;
            $this->errortip = "该状态集团不可操作";
            return false;
        }


        if ($request['achieve_status'] == 2) {
            $str = ($this->stafferOne['staffer_enname'] ? $this->stafferOne['staffer_cnname'] . '-' . $this->stafferOne['staffer_enname'] : $this->stafferOne['staffer_cnname']) . '集团审核通过,备注信息:' . $request['achieve_note'];
            $this->oktip = '审核成功';

        } elseif ($request['achieve_status'] == '-1') {
            $str = ($this->stafferOne['staffer_enname'] ? $this->stafferOne['staffer_cnname'] . '-' . $this->stafferOne['staffer_enname'] : $this->stafferOne['staffer_cnname']) . '集团审核拒绝,备注信息:' . $request['achieve_note'];
            $this->oktip = '拒绝成功';

        } else {
            $this->error = true;
            $this->errortip = "审核状态错误";
            return false;
        }

        $data = array();
        $data['achieve_status'] = $request['achieve_status'];
        $data['achieve_updatatime'] = time();

        $this->DataControl->updateData("smc_staffer_achieve", "achieve_id='{$request['achieve_id']}'", $data);

        $data = array();
        $data['achieve_id'] = $request['achieve_id'];
        $data['tracks_title'] = $this->LgStringSwitch('集团审核');
        $data['tracks_information'] = $this->LgStringSwitch($str);
        $data['tracks_note'] = $request['achieve_note'];
        $data['staffer_id'] = $this->stafferOne['staffer_id'];
        $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
        $data['tracks_time'] = time();
        $this->DataControl->insertData("smc_staffer_achieve_track", $data);

        return true;

    }

    function hourFreeOrderList($request)
    {
        $datawhere = " 1 ";
        $datawhere .= " and o.company_id = '{$this->company_id}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or o.trading_pid like '%{$request['keyword']}%' or o.order_pid like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$request['school_id']}'";
        }
        if (isset($request['order_status']) && $request['order_status'] !== "") {
            $datawhere .= " and o.order_status ='{$request['order_status']}'";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and o.order_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'] . ' 23:59:59');
            $datawhere .= " and o.order_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and o.order_updatatime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . ' 23:59:59');
            $datawhere .= " and o.order_updatatime <= '{$firstday}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
        }
        if ($request['dataequity'] == '1') {
            $sql = "
            SELECT
                o.order_id,
                o.trading_pid,
                o.order_pid,
                o.order_status,
                o.course_branch,
                sc.course_cnname,
                o.order_alltimes,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                t.student_cnname,
                t.student_branch,
                o.order_hastimes,
                o.order_createtime,
                o.order_updatatime
            FROM
                smc_freehour_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
                left join smc_course as sc on sc.course_id=o.course_id
            WHERE
                {$datawhere} and o.order_isneedaudit ='1'
            ORDER BY
                o.order_id DESC ";
        } else {
            $sql = "
            SELECT
                o.order_id,
                o.trading_pid,
                o.order_pid,
                o.order_status,
                o.course_branch,
                sc.course_cnname,
                o.order_alltimes,
                (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                s.school_branch,
                t.student_cnname,
                t.student_branch,
                o.order_hastimes,
                o.order_createtime,
                o.order_updatatime
            FROM
                smc_freehour_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
                left join smc_course as sc on sc.course_id=o.course_id
            WHERE
                {$datawhere} and o.order_isneedaudit ='1' and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = o.school_id)
            ORDER BY
                o.order_id DESC ";
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
            foreach ($dateexcelarray as &$val) {
                $val['order_status_name'] = $status[$val['order_status']];
                $val['order_createtime'] = date("Y-m-d", $val['order_createtime']);
                if ($val['order_updatatime']) {
                    $val['order_updatatime'] = date("Y-m-d", $val['order_updatatime']);
                } else {
                    $val['order_updatatime'] = '--';
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];//课程赠送订单编号
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员名称
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];//课程别名称
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];//课程别编号
                    $datearray['order_alltimes'] = $dateexcelvar['order_alltimes'];//赠送课次数
                    $datearray['order_status_name'] = $dateexcelvar['order_status_name'];//订单状态
                    $datearray['order_createtime'] = $dateexcelvar['order_createtime'];//订单状态
                    $datearray['order_updatatime'] = $dateexcelvar['order_updatatime'];//订单状态
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('课程赠送订单编号', '校区名称', '校区编号', '学员名称', '学员编号', '课程别名称', '课程别编号', '赠送课次数', '订单状态', '申请时间', '审核时间'));
            $excelfileds = array('order_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'course_cnname', 'course_branch', 'order_alltimes', 'order_status_name', 'order_createtime', 'order_updatatime');

            $fielname = $this->LgStringSwitch("赠送课程审核");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $tradeList = $this->DataControl->selectClear($sql);
        }


        if (!$tradeList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }

        if ($tradeList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已完成", "-1" => "已取消", "-2" => "已拒绝"));
            foreach ($tradeList as &$val) {
                $val['order_status_name'] = $status[$val['order_status']];
                $val['order_createtime'] = date("Y-m-d", $val['order_createtime']);
                if ($val['order_updatatime']) {
                    $val['order_updatatime'] = date("Y-m-d", $val['order_updatatime']);
                } else {
                    $val['order_updatatime'] = '--';
                }
            }
        }
        if ($request['dataequity'] == '1') {
            $all_num = $this->DataControl->select("
           SELECT
                o.order_id
            FROM
                smc_freehour_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
                left join smc_course as sc on sc.course_id=o.course_id
            WHERE
                {$datawhere} and o.order_isneedaudit ='1'");
        } else {
            $all_num = $this->DataControl->select("
           SELECT
                o.order_id
            FROM
                smc_freehour_order AS o
                LEFT JOIN smc_school AS s ON s.school_id = o.school_id
                left join smc_student as t on o.student_id = t.student_id
                left join smc_course as sc on sc.course_id=o.course_id
            WHERE
                {$datawhere} and o.order_isneedaudit ='1' and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = o.school_id)");
        }


        if ($all_num) {
            $allnum = count($all_num);
        } else {
            $allnum = 0;
        }


        $data = array();
        $schoolList = $this->DataControl->selectClear("select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname from smc_school as s left join smc_freehour_order as cr on cr.school_id=s.school_id where cr.company_id='{$this->company_id}' and cr.order_status<>0 group by s.school_id");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['allnum'] = $allnum;
        $data['list'] = $tradeList;
        $data['school'] = $schoolList;

        return $data;
    }

    function dealOrder($request)
    {

        $datawhere = " 1 ";
        $datawhere .= " and fd.company_id = '{$this->company_id}' and fd.dealorder_type='1'";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (t.student_cnname like '%{$request['keyword']}%' or fd.trading_pid like '%{$request['keyword']}%' or fd.dealorder_pid like '%{$request['keyword']}%' or t.student_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and fd.school_id ='{$request['school_id']}'";
        }
        if (isset($request['dealorder_status']) && $request['dealorder_status'] !== "") {
            $datawhere .= " and fd.dealorder_status ='{$request['dealorder_status']}'";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and fd.dealorder_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'] . ' 23:59:59');
            $datawhere .= " and fd.dealorder_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and fd.dealorder_updatatime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . ' 23:59:59');
            $datawhere .= " and fd.dealorder_updatatime <= '{$firstday}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
        }

        if ($request['dataequity'] == '1') {
            $sql = "select fd.dealorder_id,fd.dealorder_pid,sc.course_branch,sc.course_cnname,fd.dealorder_balanceprice,fd.dealorder_forwardprice,fd.dealorder_status,fd.dealorder_createtime,fd.dealorder_updatatime,dc.dealcourse_time,dc.dealcourse_figure,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,t.student_cnname,t.student_enname,t.student_branch,dc.dealcourse_fromforwardprice
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            left join smc_school as s on s.school_id=fd.school_id
            WHERE
                {$datawhere}
            ORDER BY
                fd.dealorder_createtime DESC ";
        } else {
            $sql = "select fd.dealorder_id,fd.dealorder_pid,sc.course_branch,sc.course_cnname,fd.dealorder_balanceprice,fd.dealorder_forwardprice,fd.dealorder_status,fd.dealorder_createtime,fd.dealorder_updatatime,dc.dealcourse_time,dc.dealcourse_figure,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,t.student_cnname,t.student_enname,t.student_branch,dc.dealcourse_fromforwardprice
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            left join smc_school as s on s.school_id=fd.school_id
            WHERE
                {$datawhere} and fd.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}')
            ORDER BY
                fd.dealorder_createtime DESC ";
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已完成", "-1" => "已拒绝"));
            foreach ($dateexcelarray as &$val) {
                $val['dealorder_status_name'] = $status[$val['dealorder_status']];
                $val['dealorder_createtime'] = date("Y-m-d", $val['dealorder_createtime']);
                if ($val['dealorder_updatatime']) {
                    $val['dealorder_updatatime'] = date("Y-m-d", $val['dealorder_updatatime']);
                } else {
                    $val['dealorder_updatatime'] = '--';
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['dealorder_pid'] = $dateexcelvar['dealorder_pid'];//结转订单编号
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员中文名
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];//学员英文名
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];//课程名称
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];//课程编号
                    $datearray['dealcourse_figure'] = $dateexcelvar['dealcourse_figure'];//结转金额
                    $datearray['dealcourse_time'] = $dateexcelvar['dealcourse_time'];//结转课次
                    $datearray['dealcourse_fromforwardprice'] = $dateexcelvar['dealcourse_fromforwardprice'];//结转结转金额
                    $datearray['dealorder_status_name'] = $dateexcelvar['dealorder_status_name'];//订单状态
                    $datearray['dealorder_createtime'] = $dateexcelvar['dealorder_createtime'];//申请时间
                    $datearray['dealorder_updatatime'] = $dateexcelvar['dealorder_updatatime'];//审核时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('结转订单编号', '校区名称', '学员中文名', '学员英文名', '学员编号', '课程名称', '课程编号', '结转金额', '结转课次', '结转结转金额', '订单状态', '申请时间', '审核时间'));
            $excelfileds = array('dealorder_pid', 'school_cnname', 'student_cnname', 'student_enname', 'student_branch', 'course_cnname', 'course_branch', 'dealcourse_figure', 'dealcourse_time', 'dealcourse_fromforwardprice', 'dealorder_status_name', 'dealorder_createtime', 'dealorder_updatatime');

            $fielname = $this->LgStringSwitch("结转课次审核");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }


        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }

        if ($orderList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已完成", "-1" => "已拒绝"));
            foreach ($orderList as &$val) {
                $val['dealorder_status_name'] = $status[$val['dealorder_status']];
                $val['dealorder_createtime'] = date("Y-m-d", $val['dealorder_createtime']);
                if ($val['dealorder_updatatime']) {
                    $val['dealorder_updatatime'] = date("Y-m-d", $val['dealorder_updatatime']);
                } else {
                    $val['dealorder_updatatime'] = '--';
                }
            }
        }
        if ($request['dataequity'] == '1') {
            $all_num = $this->DataControl->select("select fd.dealorder_id
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            WHERE
                {$datawhere}
                ");
        } else {
            $all_num = $this->DataControl->select("select fd.dealorder_id
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            WHERE
                {$datawhere} and fd.school_id in (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}')
                ");
        }


        if ($all_num) {
            $allnum = count($all_num);
        } else {
            $allnum = 0;
        }


        $data = array();
        $schoolList = $this->DataControl->selectClear("select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname
            from smc_forward_dealorder as fd
            left join smc_forward_dealorder_course as dc on dc.dealorder_pid=fd.dealorder_pid
            left join smc_student as t on fd.student_id = t.student_id
            left join smc_course as sc on sc.course_id=dc.course_id
            left join smc_school as s on s.school_id=fd.school_id
            WHERE
                {$datawhere}
                group by s.school_id
                ");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['allnum'] = $allnum;
        $data['list'] = $orderList;
        $data['school'] = $schoolList;

        return $data;
    }

    function examineRenewOrder($request)
    {
        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['order_pid'], 0);
            $bool = $OrderExamineModel->adoptRenewOrder($request['reason'], strtotime($request['create_time']));

            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['order_pid'], 0);
            $bool = $OrderExamineModel->examineRenewOrder($request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = "不存在该状态";
            return false;
        }
    }

    function examineReduceOrder($request)
    {
        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['reduceorder_pid'], 2);
            $bool = $OrderExamineModel->refuseReduceOrder($request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['reduceorder_pid'], 2);
            $bool = $OrderExamineModel->adoptReduceOrder($request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = "不存在该状态";
            return false;
        }
    }

    function examineCanceldebts($request)
    {

        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['order_pid'], 0);
            $bool = $OrderExamineModel->refuseCanceldebts($request);
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['order_pid'], 0);
            $bool = $OrderExamineModel->adoptCanceldebts($request);
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = '不存在该状态';
            return false;
        }
    }

    //招行推送
    function ZhaohangRefundOrder($request)
    {
        $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['refund_pid'], 1);
        $bool = $OrderExamineModel->ZhaohangRefundOrder($request);
        if ($bool) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = $OrderExamineModel->errortip;
            return false;
        }
    }

    //招行推送
    function ZhaohangRefundOrderQuery($request)
    {
        $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['refund_pid'], 1);
        $bool = $OrderExamineModel->ZhaohangRefundOrderQuery($request);
        if ($bool) {
            return $bool;
        } else {
            $this->error = true;
            $this->errortip = $OrderExamineModel->errortip;
            return false;
        }
    }

    function examineRefundOrder($request)
    {
        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['refund_pid'], 1);
            $bool = $OrderExamineModel->refuseRefundOrder($request['reason'], strtotime($request['create_time']),$request['is_skip']);
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['refund_pid'], 1);
            $bool = $OrderExamineModel->adoptRefundOrder($request, strtotime($request['create_time']),$request['is_skip']);
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = '不存在该状态';
            return false;
        }
    }

    function examineForwardApplication($request)
    {
        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray);
            $bool = $OrderExamineModel->refuseForwardApplication($request['application_id'],$request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray);
            $bool = $OrderExamineModel->adoptForwardApplication($request['application_id'],$request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = '不存在该状态';
            return false;
        }
    }

    function examineDealOrder($request)
    {
        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['dealorder_pid'], 4);
            $bool = $OrderExamineModel->refuseDealOrder($request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['dealorder_pid'], 4);
            $bool = $OrderExamineModel->adoptDealOrder($request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = '不存在该状态';
            return false;
        }
    }

    function examineHourFreeOrder($request)
    {
        if ($request['is_adopt'] == 0) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['order_pid'], 3);
            $bool = $OrderExamineModel->refuseHourFreeOrder($request['reason'], strtotime($request['create_time']));
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }
        } elseif ($request['is_adopt'] == 1) {
            $OrderExamineModel = new \Model\Gmc\OrderExamineModel($this->publicarray, $request['order_pid'], 3);
            $bool = $OrderExamineModel->adoptHourFreeOrder($request['reason'],$request['from']);
            if ($bool) {
                return true;
            } else {
                $this->error = true;
                $this->errortip = $OrderExamineModel->errortip;
                return false;
            }

        } else {
            $this->error = true;
            $this->errortip = '不存在该状态';
            return false;
        }
    }

    /**
     * 减免学费 订单的审核  实质是审核支付记录
     * author: ling
     * 对应接口文档 0001
     */
    function FeeWaiverOrderPay($request)
    {
        $datawhere = " 1 ";
        $datawhere .= " and o.company_id = '{$this->company_id}' and  po.paytype_code='feewaiver'";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%'  
                or s.student_enname like '%{$request['keyword']}%'  
                or s.student_branch like '%{$request['keyword']}%'  
                or o.order_pid like '%{$request['keyword']}%' 
                or o.trading_pid like '%{$request['keyword']}%' 
                or po.pay_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$request['school_id']}'";
        }
        if (isset($request['status']) && $request['status'] !== "") {
            $datawhere .= " and po.pay_issuccess ='{$request['status']}'";
        }else{
            $datawhere .= " and po.pay_issuccess ='0'";

        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and po.pay_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'] . '23:59:59');
            $datawhere .= " and po.pay_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and po.pay_updatatime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . '23:59:59');
            $datawhere .= " and po.pay_updatatime <= '{$firstday}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
        }
        if ($request['dataequity'] == '1') {
            $sql = "select s.student_cnname,s.student_enname,s.student_branch,po.order_pid,po.pay_pid,po.pay_note
            ,pay_price,po.pay_issuccess,po.pay_createtime,po.pay_updatatime
            ,(case when sl.school_shortname='' then sl.school_cnname else sl.school_shortname end) as school_cnname,sl.school_branch
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_student as s ON o.student_id = s.student_id
            left join smc_school as sl On sl.school_id = o.school_id
            WHERE {$datawhere}
            ORDER BY po.pay_createtime DESC";
        } else {
            $sql = "select s.student_cnname,s.student_enname,s.student_branch,po.order_pid,po.pay_pid,po.pay_note
            ,pay_price,po.pay_issuccess,po.pay_createtime,po.pay_updatatime
            ,(case when sl.school_shortname='' then sl.school_cnname else sl.school_shortname end) as school_cnname,sl.school_branch
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_student as s ON o.student_id = s.student_id
            left join smc_school as sl On sl.school_id = o.school_id
            WHERE {$datawhere} 
            and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = o.school_id)
            ORDER BY po.pay_createtime DESC";
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已审核", "-1" => "已拒绝"));
            foreach ($dateexcelarray as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                $val['pay_createtime'] = date("Y-m-d", $val['pay_createtime']);
                if ($val['pay_updatatime']) {
                    $val['pay_updatatime'] = date("Y-m-d", $val['pay_updatatime']);
                } else {
                    $val['pay_updatatime'] = '--';
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//校区编号
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];//订单编号
                    $datearray['pay_pid'] = $dateexcelvar['pay_pid'];//减免编号
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员中文名
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];//学员英文名
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员编号
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];//减免金额
                    $datearray['pay_note'] = $dateexcelvar['pay_note'];//减免原因
                    $datearray['pay_issuccess_name'] = $dateexcelvar['pay_issuccess_name'];//订单状态
                    $datearray['pay_createtime'] = $dateexcelvar['pay_createtime'];//申请时间
                    $datearray['pay_updatatime'] = $dateexcelvar['pay_updatatime'];//审核时间
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '订单编号', '减免编号', '学员中文名', '学员英文名', '学员编号', '减免金额', '减免原因', '订单状态', '申请时间', '审核时间'));
            $excelfileds = array('school_cnname', 'school_branch', 'order_pid', 'pay_pid', 'student_cnname', 'student_enname', 'student_branch', 'pay_price', 'pay_note', 'pay_issuccess_name', 'pay_createtime', 'pay_updatatime');

            $fielname = $this->LgStringSwitch("减免学费审核");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }

        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }
        if ($orderList) {
            $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已审核", "-1" => "已拒绝"));
            foreach ($orderList as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                $val['pay_createtime'] = date("Y-m-d", $val['pay_createtime']);
                if ($val['pay_updatatime']) {
                    $val['pay_updatatime'] = date("Y-m-d", $val['pay_updatatime']);
                } else {
                    $val['pay_updatatime'] = '--';
                }
            }
        }
        if ($request['dataequity'] == '1') {
            $all_num = $this->DataControl->selectOne("
            select count(po.pay_id) as  all_num
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_student as s ON o.student_id = s.student_id
            left join smc_school as sl On sl.school_id = o.school_id
            WHERE {$datawhere}
            ");

        } else {
            $all_num = $this->DataControl->selectOne("
            select count(po.pay_id) as  all_num
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_student as s ON o.student_id = s.student_id
            left join smc_school as sl On sl.school_id = o.school_id
            WHERE {$datawhere} 
            AND exists (SELECT os.school_id FROM gmc_company_organizeschool AS os 
            WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = o.school_id)
            ");
        }
        if ($all_num) {
            $allnum = $all_num['all_num'];
        } else {
            $allnum = 0;
        }
        $data = array();
        $schoolList = $this->DataControl->selectClear("SELECT DISTINCT s.school_id
                ,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_shortname
                FROM smc_school AS s  
                INNER JOIN smc_payfee_order AS x ON x.school_id = s.school_id
                INNER JOIN smc_payfee_order_pay AS y ON y.order_pid = x.order_pid
                WHERE s.company_id = '{$this->company_id}'
                AND y.paytype_code = 'feewaiver'
                ");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['allnum'] = $allnum;
        $data['list'] = $orderList;
        $data['school'] = $schoolList;
        $data['all_num'] = $allnum;

        return $data;
    }

    /**
     * 审核减免学费订单
     * author: ling
     * 对应接口文档 0001
     */
    function ChangeFeeWaiverOrderAction($request)
    {
        $OrderOne = $this->DataControl->selectOne(
            "select o.company_id,o.school_id,o.order_pid,op.pay_price,op.pay_pid,o.order_arrearageprice,o.student_id,op.order_pid,o.order_allprice,o.order_arrearageprice,o.order_paymentprice,o.student_id,o.order_status,o.trading_pid,op.pay_issuccess,o.order_isneedaudit,o.order_iscreatecourse,o.companies_id,o.mergeorder_pid
            from  smc_payfee_order_pay  as op
            left join smc_payfee_order as o On o.order_pid = op.order_pid
            where op.pay_pid = '{$request['pay_pid']}'
            "
        );

        if (!$OrderOne) {
            $this->error = 1;
            $this->errortip = '请选择订单';
            return false;
        }

        if ($OrderOne['pay_issuccess'] == 1) {
            $this->error = 1;
            $this->errortip = '该审核记录已完成,请勿重复审核';
            return false;
        }

        if ($request['status'] == '1') {

            if ($OrderOne['order_status'] == '-1') {
                $this->error = 1;
                $this->errortip = '该订单已取消,请直接拒绝';
                return false;
            }

            if ($OrderOne['pay_price'] > $OrderOne['order_arrearageprice']) {
                $this->error = 1;
                $this->errortip = '减免金额,超出可结算金额';
                return false;
            }

            if ($OrderOne['order_status'] == '0' && $OrderOne['order_isneedaudit'] == '1' && $OrderOne['order_iscreatecourse'] == '0') {

                $otherOne = $this->DataControl->selectOne("select sum(pay_price) as pay_price from smc_payfee_order_pay where order_pid='{$OrderOne['order_pid']}' and pay_issuccess =0  and paytype_code<>'feewaiver' and (pay_type=2 or pay_type=1 ) ");
                if (!$otherOne) {
                    $otherOne['pay_price'] = 0;
                }

                if ($OrderOne['pay_price'] > ($OrderOne['order_arrearageprice'] - $otherOne['pay_price'])) {
                    $this->error = 1;
                    $this->errortip = '减免金额,超出订单金额';
                    return false;
                }

                $sql = "select pay_id from smc_payfee_order_pay where order_pid='{$OrderOne['order_pid']}' and pay_type=2 and pay_issuccess=1 and pay_price>0 limit 0,1";
                $payItemOne = $this->DataControl->selectOne($sql);
                if ($payItemOne) {
                    $sql = "select oi.*,p.course_id from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  where oi.order_pid='{$OrderOne['order_pid']}'
                  ";
                } else {
                    $sql = "select oi.*,p.course_id
                  from smc_payfee_order_item as oi
                  left join smc_fee_pricing_items as fp on fp.items_id=oi.items_id
                  left join smc_fee_pricing as p on p.pricing_id=fp.pricing_id
                  left join smc_code_feeitem as cf on cf.feeitem_branch=oi.feeitem_branch and cf.company_id='{$OrderOne['company_id']}'
                  where oi.order_pid='{$OrderOne['order_pid']}' and cf.feeitem_expendtype<>'2'
                  ";
                }

                $itemsCourse = $this->DataControl->selectClear($sql);

                if ($itemsCourse) {
                    foreach ($itemsCourse as $one) {
                        $sql = "select scb.itemtimes_id from smc_student_itemtimes as scb
                      left join smc_code_feeitem as cf on cf.feeitem_id=scb.feeitem_id
                      where scb.student_id='{$OrderOne['student_id']}' and scb.course_id='{$one['course_id']}' and cf.feeitem_branch='{$one['feeitem_branch']}'
                      and (scb.itemtimes_figure<'{$one['item_totalprice']}' or scb.itemtimes_number<'{$one['item_buynums']}')";

                        if ($this->DataControl->selectOne($sql)) {
                            $this->error = true;
                            $this->errortip = "杂费已有消耗,不可审核通过";
                            return false;
                        }
                    }
                }

                $sql = "select scb.coursebalance_id
                      from smc_payfee_order_course as poc
                      left join smc_payfee_order as po on po.order_pid=poc.order_pid
                      left join smc_student_coursebalance as scb on scb.course_id=poc.course_id and scb.school_id=po.school_id and scb.student_id=po.student_id 
                      left join smc_course as sc on sc.course_id=poc.course_id
                      where poc.order_pid='{$OrderOne['order_pid']}' and sc.course_sellclass=0 and (scb.coursebalance_figure>0 or scb.coursebalance_time>0) limit 0,1";

                $coursebalanceOne = $this->DataControl->selectOne($sql);

                if ($coursebalanceOne) {
                    $this->error = 1;
                    $this->errortip = '课程已存在不可重复购买';
                    return false;
                }

                $sql = "select poc.ordercourse_id,sc.course_inclasstype,poc.ordercourse_totalprice,poc.ordercourse_unitprice,poc.ordercourse_unitrefund,poc.ordercourse_buynums
                      from smc_payfee_order_course as poc
                      left join smc_course as sc on sc.course_id=poc.course_id
                      where poc.order_pid='{$OrderOne['order_pid']}'
                      ";
                $courseList = $this->DataControl->selectClear($sql);

                if (!$courseList) {
                    $this->error = 1;
                    $this->errortip = '该减免不存在课程,请联系技术';
                    return false;
                }

                $courseNum = count($courseList);

                $all_price = $OrderOne['pay_price'];

                $sql = "select sum(ordercourse_totalprice) as allPrice from smc_payfee_order_course where order_pid='{$OrderOne['order_pid']}'";
                $coursePriceOne = $this->DataControl->selectOne($sql);

                $courseAllPrice = $coursePriceOne['allPrice'];

                foreach ($courseList as $key => $courseOne) {

                    if ($courseOne['course_inclasstype'] == 1) {

                        $sql = "select os.ordershare_id,os.ordershare_month,os.ordershare_price
                              from smc_payfee_order_share as os where os.order_pid='{$OrderOne['order_pid']}'";

                        $monthList = $this->DataControl->selectClear($sql);

                        if (!$monthList) {
                            $this->error = 1;
                            $this->errortip = '期度数据错误,请联系技术';
                            return false;
                        }

                        if ($courseNum > 1) {
                            $this->error = 1;
                            $this->errortip = '订单错误,请联系技术';
                            return false;
                        }

                        $monthNum = count($monthList);

                        foreach ($monthList as $k => $monthOne) {

                            if ($k == ($monthNum - 1)) {
                                $diff = $all_price;
                            } else {
                                $diff = ceil(($monthOne['ordershare_price'] / $courseAllPrice) * $OrderOne['pay_price']);
                                $all_price -= $diff;
                            }

                            $data = array();
                            $data['ordershare_price'] = $monthOne['ordershare_price'] - $diff;
                            $this->DataControl->updateData("smc_payfee_order_share", "ordershare_id='{$monthOne['ordershare_id']}'", $data);
                        }

                        $data = array();
                        $data['ordercourse_unitprice'] = ceil(($courseOne['ordercourse_totalprice'] - $OrderOne['pay_price']) / $courseOne['ordercourse_buynums']);
                        $data['ordercourse_totalprice'] = $courseOne['ordercourse_totalprice'] - $OrderOne['pay_price'];

                        $this->DataControl->updateData("smc_payfee_order_course", "ordercourse_id='{$courseOne['ordercourse_id']}'", $data);

                    } else {
                        if ($key == ($courseNum - 1)) {
                            $diff = $all_price;
                        } else {
                            $diff = ceil(($courseOne['ordercourse_totalprice'] / $courseAllPrice) * $OrderOne['pay_price']);
                            $all_price -= $diff;
                        }

                        $data = array();
                        $data['ordercourse_unitprice'] = ceil(($courseOne['ordercourse_totalprice'] - $diff) / $courseOne['ordercourse_buynums']);

                        $data['ordercourse_totalprice'] = $courseOne['ordercourse_totalprice'] - $diff;

                        $this->DataControl->updateData("smc_payfee_order_course", "ordercourse_id='{$courseOne['ordercourse_id']}'", $data);

                    }

                }
                $request['school_id'] = $OrderOne['school_id'];

                $orderPayModel = new  \Model\Smc\OrderPayModel($request, $OrderOne['order_pid']);

                $bool = $orderPayModel->orderPaylog('', '', '', '', '', '', '1');

                if ($bool) {
                    $pay_data = array();
                    $pay_data['pay_issuccess'] = '1';
                    $pay_data['pay_successtime'] = time();
                    $pay_data['pay_updatatime'] = time();
                    $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$OrderOne['order_pid']}' and pay_pid='{$OrderOne['pay_pid']}'", $pay_data);

                    $OrderOne = $this->DataControl->selectOne(
                        "select o.company_id,o.school_id,o.order_pid,op.pay_price,op.pay_pid,o.order_arrearageprice,o.student_id,op.order_pid,o.order_allprice,o.order_arrearageprice,o.order_paymentprice,o.student_id,o.order_status,o.trading_pid,op.pay_issuccess,o.order_isneedaudit,o.order_iscreatecourse,o.mergeorder_pid
            from  smc_payfee_order_pay  as op
            left join smc_payfee_order as o On o.order_pid = op.order_pid
            where op.pay_pid = '{$request['pay_pid']}'
            "
                    );

                    $data = array();
                    $data['achieve_price'] = $OrderOne['order_paymentprice'] - $OrderOne['pay_price'];
                    $this->DataControl->updateData("smc_staffer_achieve", "order_pid='{$OrderOne['order_pid']}'", $data);


                    if($OrderOne['mergeorder_pid']!=''){
                        $sql = "select a.order_id 
                from smc_payfee_order as a where a.mergeorder_pid='{$OrderOne['mergeorder_pid']}' and a.order_status<>4 and a.order_status>=0";

                        if(!$this->DataControl->selectOne($sql)){
                            $data=array();
                            $data['mergeorder_status']=1;
                            $data['mergeorder_updatatime']=time();
                            $this->DataControl->updateData("smc_payfee_mergeorder","mergeorder_pid='{$OrderOne['mergeorder_pid']}'",$data);
                        }
                    }
                } else {
                    $this->error = 1;
                    $this->errortip = $orderPayModel->errortip;
                    return false;
                }

            } elseif ($OrderOne['order_iscreatecourse'] == '1' && $OrderOne['order_status'] != '0') {

                $orderCourseList = $this->DataControl->selectClear(
                    "select poc.course_id,sc.coursebalance_figure,sc.coursebalance_id,sc.coursebalance_time,coursebalance_unitexpend,coursebalance_unitearning,o.student_id,o.school_id,s.student_branch, sc.course_id,poc.ordercourse_totalprice,s.student_id,co.course_inclasstype,o.trading_pid
              from smc_payfee_order_course as  poc
              left join smc_payfee_order as o  on poc.order_pid = o.order_pid
              left join smc_student_coursebalance as  sc on sc.course_id = poc.course_id and o.student_id = sc.student_id and o.school_id = sc.school_id
              left join smc_student as s On s.student_id = o.student_id
              left join smc_course as co On co.course_id = poc.course_id
              where poc.order_pid='{$OrderOne['order_pid']}'
              order by sc.coursebalance_figure ASC
               ");
                if ($orderCourseList) {
                    $courseOne = $this->DataControl->selectOne("select sum(sc.coursebalance_figure) as total_price
              from smc_payfee_order_course as  poc
              left join smc_payfee_order as o  on poc.order_pid = o.order_pid
              left join smc_student_coursebalance as  sc on sc.course_id = poc.course_id   and o.student_id = sc.student_id and o.school_id = sc.school_id
              where poc.order_pid='{$OrderOne['order_pid']}'
            ");
                    if ($OrderOne['pay_price'] > $courseOne['total_price']) {
                        $this->error = 1;
                        $this->errortip = '减免金额,超出课程余额';
                        return false;
                    }
                    if ($courseOne['total_price'] <= 0) {
                        $this->error = 1;
                        $this->errortip = '课程总价错误';
                        return false;
                    }
                    $num = count($orderCourseList);
                    $alreay_price = 0;
                    foreach ($orderCourseList as $key => $val) {

                        $studentfreeTimeOne = $this->DataControl->selectOne("select count(coursetimes_id) as coursetimes_num  from  smc_student_free_coursetimes where  student_id='{$val['student_id']}' and course_id='{$val['course_id']}' and  is_use = 0 ");
                        if ($studentfreeTimeOne) {
                            $freetimenum = $studentfreeTimeOne['coursetimes_num'];
                        } else {
                            $freetimenum = 0;
                        }
                        $re_price = floor(($val['coursebalance_figure'] / $courseOne['total_price']) * $OrderOne['pay_price']);
                        if ($key + 1 == $num) {
                            $re_price = $OrderOne['pay_price'] - $alreay_price;
                        }

                        $stuData = array();
                        $order_course_data = array();
                        $stuData['coursebalance_figure'] = $val['coursebalance_figure'] - $re_price;
                        $stuData['coursebalance_updatatime'] = time();

                        if ($stuData['coursebalance_figure'] >= 0) {
                            if ($val['coursebalance_time'] > 0) {
                                if (($val['coursebalance_time'] - $freetimenum) <= 0) {
                                    $this->error = 1;
                                    $this->errortip = '课次不足';
                                    return false;
                                }
                                $un_price = floor($re_price / ($val['coursebalance_time'] - $freetimenum));
                                if ($un_price < 0) {
                                    $this->error = 1;
                                    $this->errortip = '计算错误,请联系技术团队';
                                    return false;
                                }
                                $stuData['coursebalance_unitexpend'] = ceil(($val['coursebalance_unitexpend'] - $un_price) > 0 ? ceil($val['coursebalance_unitexpend'] - $un_price) : $val['coursebalance_unitexpend']);
                                $stuData['coursebalance_unitearning'] = ceil(($val['coursebalance_unitearning'] - $un_price) > 0 ? ceil($val['coursebalance_unitearning'] - $un_price) : $val['coursebalance_unitearning']);

                                $pricinglogOne = $this->DataControl->selectOne("select pricinglog_id from smc_student_coursebalance_pricinglog where  student_id='{$OrderOne['student_id']}' and course_id='{$val['course_id']}' and school_id='{$OrderOne['school_id']}' order by pricinglog_id DESC ");
                                $pricinglog_data = array();
                                $pricinglog_data['pricinglog_unitexpend'] = $stuData['coursebalance_unitexpend'];
                                $pricinglog_data['pricinglog_updatatime'] = time();
                                $this->DataControl->updateData('smc_student_coursebalance_pricinglog', "pricinglog_id='{$pricinglogOne['pricinglog_id']}'", $pricinglog_data);
                                $order_course_data['ordercourse_unitprice'] = $stuData['coursebalance_unitearning'];
                            }
                            $this->DataControl->updateData("smc_student_coursebalance", "coursebalance_id='{$val['coursebalance_id']}'", $stuData);
                            $order_course_data['ordercourse_totalprice'] = ($val['ordercourse_totalprice'] - $re_price) > 0 ? ($val['ordercourse_totalprice'] - $re_price) : 0;
                            $this->DataControl->updateData('smc_payfee_order_course', "order_pid='{$OrderOne['order_pid']}' and course_id='{$val['course_id']}'", $order_course_data);

                            $random = $this->create_guid();

                            $balancelog = array();
                            $balancelog['student_id'] = $val['student_id'];
                            $balancelog['log_random'] = $random;
                            $balancelog['course_id'] = $val['course_id'];
                            $balancelog['school_id'] = $val['school_id'];
                            $balancelog['companies_id'] = $OrderOne['companies_id'];
                            $balancelog['log_class'] = 0;
                            $balancelog['log_playname'] = $this->LgStringSwitch('减免学费');
                            $balancelog['log_playclass'] = '-';
                            $balancelog['log_fromamount'] = $val['coursebalance_figure'];
                            $balancelog['log_playamount'] = $re_price;
                            $balancelog['log_finalamount'] = $stuData['coursebalance_figure'];
                            $balancelog['log_reason'] = $this->LgStringSwitch('减免学费，扣除课程余额');
                            $balancelog['log_fromtimes'] = $val['coursebalance_time'];
                            $balancelog['log_playtimes'] = '0';
                            $balancelog['log_finaltimes'] = $val['coursebalance_time'];
                            $balancelog['trading_pid'] = $val['trading_pid'];
                            $balancelog['log_time'] = time();
                            $this->DataControl->insertData("smc_student_coursebalance_log", $balancelog);
                            $timeslog = array();
                            $timeslog['student_id'] = $val['student_id'];
                            $timeslog['log_random'] = $random;
                            $timeslog['course_id'] = $val['course_id'];
                            $timeslog['companies_id'] = $OrderOne['companies_id'];
                            $timeslog['school_id'] = $val['school_id'];
                            $timeslog['timelog_playname'] = $this->LgStringSwitch('减免学费');
                            $timeslog['timelog_playclass'] = '-';
                            $timeslog['timelog_fromtimes'] = $val['coursebalance_time'];
                            $timeslog['timelog_playtimes'] = '0';
                            $timeslog['timelog_finaltimes'] = $val['coursebalance_time'];
                            $timeslog['trading_pid'] = $val['trading_pid'];
                            $timeslog['timelog_reason'] = $this->LgStringSwitch('减免学费');
                            $timeslog['timelog_time'] = time();
                            $this->DataControl->insertData('smc_student_coursebalance_timelog', $timeslog);
                        }
                        $alreay_price += $re_price;
                        if ($val['course_inclasstype'] == 1) {
                            $sum_coursebalance = $this->DataControl->selectOne("select sum(ch.courseshare_price) as  sum_courseshare_price from  smc_student_courseshare as ch,smc_student_coursebalance as cb where cb.coursebalance_id = ch.coursebalance_id and ch.courseshare_status = 0 and cb.student_id='{$val['student_id']}' and cb.course_id ='{$val['course_id']}' and cb.school_id ='{$val['school_id']}' ");
                            $coursebalanceList = $this->DataControl->selectClear("select ch.courseshare_id ,ch.courseshare_price from  smc_student_courseshare as ch,smc_student_coursebalance as cb where cb.coursebalance_id = ch.coursebalance_id and ch.courseshare_status = 0 and cb.student_id='{$val['student_id']}' and cb.course_id ='{$val['course_id']}' and cb.school_id ='{$val['school_id']}'  ");
                            $all_coursebalance = count($coursebalanceList);
                            $re_coursebalanceprice = $re_price;
                            if ($coursebalanceList && $sum_coursebalance['sum_courseshare_price'] > 0) {
                                foreach ($coursebalanceList as $j => $coursebalanceOne) {
                                    $courseshare_price = ceil(($coursebalanceOne['courseshare_price'] / $sum_coursebalance['sum_courseshare_price']) * $re_price);
                                    $courseshare_Data = array();
                                    $courseshare_Data['courseshare_price'] = $coursebalanceOne['courseshare_price'] - $courseshare_price;
                                    if ($j + 1 == $all_coursebalance) {
                                        $courseshare_Data['courseshare_price'] = $coursebalanceOne['courseshare_price'] - $re_coursebalanceprice;
                                    }
                                    $courseshare_Data['courseshare_updatatime'] = time();
                                    $this->DataControl->updateData("smc_student_courseshare", "courseshare_id='{$coursebalanceOne['courseshare_id']}'", $courseshare_Data);
                                    $re_coursebalanceprice -= $courseshare_price;
                                }
                            } else {
                                $this->error = 1;
                                $this->errortip = '期度类,减免失败,没有可用的分摊记录';
                                return false;
                            }
                        }
                    }
                }

                $data_paylog = array();
                $data_paylog['order_pid'] = $OrderOne['order_pid'];
                $data_paylog['pay_pid'] = $OrderOne['pay_pid'];
                $data_paylog['paylog_actualprice'] = $OrderOne['pay_price'];
                $data_paylog['paylog_paytime'] = time();
                $data_paylog['paylog_tradeno'] = "";
                $data_paylog['paylog_addtime'] = time();
                if ($this->DataControl->insertData("smc_payfee_order_paylog", $data_paylog)) {
                    $pay_data = array();
                    $pay_data['pay_issuccess'] = '1';
                    $pay_data['pay_successtime'] = time();
                    $pay_data['pay_updatatime'] = time();
                    $this->DataControl->updateData("smc_payfee_order_pay", "order_pid='{$OrderOne['order_pid']}' and pay_pid='{$OrderOne['pay_pid']}'", $pay_data);
                }

            } else {
                $this->error = 1;
                $this->errortip = '该减免错误,请联系技术查看';
                return false;
            }

            $order_data = array();
            $order_data['order_paymentprice'] = $OrderOne['order_paymentprice'] - $OrderOne['pay_price'];
            $order_data['order_updatatime'] = time();
            $order_data['order_status'] = '2';
            if ($OrderOne['pay_price'] == $OrderOne['order_arrearageprice']) {
                $order_data['order_status'] = '4';
                $student_tradingData = array();
                $student_tradingData['trading_status'] = '1';
                $this->DataControl->updateData("smc_student_trading", "trading_pid='{$OrderOne['trading_pid']}'", $student_tradingData);
            }
            $this->DataControl->updateData("smc_payfee_order", "order_pid='{$OrderOne['order_pid']}'", $order_data);

            $track_data = array();
            $track_data['order_pid'] = $OrderOne['order_pid'];
            $track_data['tracks_title'] = $this->LgStringSwitch('审核订单');
            $track_data['tracks_information'] = $this->LgStringSwitch('订单申请减免学费，审核通过');
            $track_data['tracks_note'] = $request['reason'];
            $track_data['tracks_url'] = $request['tracks_url'];
            $track_data['staffer_id'] = $this->publicarray['staffer_id'];
            $track_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
            $track_data['tracks_time'] = time();
            $this->DataControl->insertData("smc_payfee_order_tracks", $track_data);

            if($OrderOne['mergeorder_pid']!=''){
                $sql = "select a.order_id 
                from smc_payfee_order as a where a.mergeorder_pid='{$OrderOne['mergeorder_pid']}' and a.order_status<>4 and a.order_status>=0";

                if(!$this->DataControl->selectOne($sql)){
                    $data=array();
                    $data['mergeorder_status']=1;
                    $data['mergeorder_updatatime']=time();
                    $this->DataControl->updateData("smc_payfee_mergeorder","mergeorder_pid='{$OrderOne['mergeorder_pid']}'",$data);
                }
            }





            $this->error = 0;
            $this->errortip = '已通过该申请';
            return true;




        } elseif ($request['status'] == '-1') {

            if($OrderOne['mergeorder_pid']!=''){

                $sql = "select b.*,a.pay_pid 
                        from smc_payfee_order_pay as a,smc_payfee_order as b 
                        where a.order_pid=b.order_pid and b.mergeorder_pid='{$OrderOne['mergeorder_pid']}' and a.paytype_code='feewaiver' and a.pay_issuccess=0";

                $payList=$this->DataControl->selectClear($sql);

                if($payList){
                    foreach($payList as $payOne){
                        $data = array();
                        $data['pay_issuccess'] = '-1';
                        $data['pay_updatatime'] = time();
                        $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$payOne['pay_pid']}'", $data);

                        if ($payOne['order_status'] == '0' && $payOne['order_isneedaudit'] == '1' && $payOne['order_iscreatecourse'] == '0') {

                            $couponsList = $this->DataControl->selectClear("select coupons_pid from smc_payfee_order_coupons where order_pid='{$payOne['order_pid']}'");
                            if ($couponsList) {
                                foreach ($couponsList as $couponsOne) {
                                    $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,coupons_class", "student_id='{$payOne['student_id']}' and coupons_pid='{$couponsOne['coupons_pid']}'");
                                    if ($couponsOne) {
                                        $coupons_data = array();
                                        $coupons_data['coupons_isuse'] = 0;
                                        $coupons_data['order_pid'] = '';
                                        $coupons_data['coupons_usetime'] = '0';
                                        $this->DataControl->updateData("smc_student_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $coupons_data);

                                        if ($couponsOne['coupons_class'] == 1) {
                                            $data = array();
                                            $data['card_status'] = 1;
                                            $this->DataControl->updateData("smc_activity_ticket_card", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                                        }
                                    }
                                }
                            }


                            $data = array();
                            $data['trading_status'] = '-1';
                            $data['trading_updatatime'] = time();
                            $this->DataControl->updateData("smc_student_trading", "trading_pid='{$payOne['trading_pid']}'", $data);
                        }

                        $track_data = array();
                        $track_data['order_pid'] = $payOne['order_pid'];
                        $track_data['tracks_title'] = $this->LgStringSwitch('审核订单');
                        $track_data['tracks_information'] = $this->LgStringSwitch('订单申请减免学费，审核拒绝');
                        $track_data['tracks_note'] = $request['reason'];
                        $track_data['tracks_url'] = $request['tracks_url'];
                        $track_data['staffer_id'] = $this->publicarray['staffer_id'];
                        $track_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                        $track_data['tracks_time'] = time();
                        $this->DataControl->insertData("smc_payfee_order_tracks", $track_data);


                        $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$payOne['order_pid']}'");

                        if ($achieveList) {
                            $data = array();
                            $data['achieve_status'] = -1;
                            $data['achieve_updatatime'] = time();

                            $this->DataControl->updateData("smc_staffer_achieve", "order_pid='{$payOne['order_pid']}'", $data);

                            $str = '订单审核拒绝,业绩失效';

                            foreach ($achieveList as $achieveOne) {
                                $data = array();
                                $data['achieve_id'] = $achieveOne['achieve_id'];
                                $data['tracks_title'] = $this->LgStringSwitch('减免订单审核拒绝');
                                $data['tracks_information'] = $this->LgStringSwitch($str);
                                $data['staffer_id'] = $this->stafferOne['staffer_id'];
                                $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                                $data['tracks_time'] = time();
                                $this->DataControl->insertData("smc_staffer_achieve_track", $data);
                            }
                        }
                    }
                }

                $public=array();
                $public['company_id']=$OrderOne['company_id'];
                $public['school_id']=$OrderOne['school_id'];
                $public['staffer_id']=$this->staffer_id;
                $public['mergeorder_pid']=$OrderOne['mergeorder_pid'];

                $OrderModel = new \Model\Smc\OrderModel($public);
                $bool=$OrderModel->cancelMergeOrder($public);
                if(!$bool){
                    $this->errortip=$OrderModel->errortip;
                    return false;
                }

            }else{
                $data = array();
                $data['pay_issuccess'] = '-1';
                $data['pay_updatatime'] = time();
                $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$OrderOne['pay_pid']}'", $data);

                if ($OrderOne['order_status'] == '0' && $OrderOne['order_isneedaudit'] == '1' && $OrderOne['order_iscreatecourse'] == '0') {

                    $couponsList = $this->DataControl->selectClear("select coupons_pid from smc_payfee_order_coupons where order_pid='{$OrderOne['order_pid']}'");
                    if ($couponsList) {
                        foreach ($couponsList as $couponsOne) {
                            $couponsOne = $this->DataControl->getFieldOne("smc_student_coupons", "coupons_id,coupons_class", "student_id='{$OrderOne['student_id']}' and coupons_pid='{$couponsOne['coupons_pid']}'");
                            if ($couponsOne) {
                                $coupons_data = array();
                                $coupons_data['coupons_isuse'] = 0;
                                $coupons_data['order_pid'] = '';
                                $coupons_data['coupons_usetime'] = '0';
                                $this->DataControl->updateData("smc_student_coupons", "coupons_id='{$couponsOne['coupons_id']}'", $coupons_data);

                                if ($couponsOne['coupons_class'] == 1) {
                                    $data = array();
                                    $data['card_status'] = 1;
                                    $this->DataControl->updateData("smc_activity_ticket_card", "coupons_id='{$couponsOne['coupons_id']}'", $data);
                                }
                            }
                        }
                    }


                    $data = array();
                    $data['pay_issuccess'] = '-1';
                    $data['pay_updatatime'] = time();
                    $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$OrderOne['pay_pid']}'", $data);

                    $data = array();
                    $data['order_status'] = '-2';
                    $data['order_updatatime'] = time();
                    $this->DataControl->updateData("smc_payfee_order", "order_pid='{$OrderOne['order_pid']}'", $data);

                    $data = array();
                    $data['trading_status'] = '-1';
                    $data['trading_updatatime'] = time();
                    $this->DataControl->updateData("smc_student_trading", "trading_pid='{$OrderOne['trading_pid']}'", $data);
                }

                $track_data = array();
                $track_data['order_pid'] = $OrderOne['order_pid'];
                $track_data['tracks_title'] = $this->LgStringSwitch('审核订单');
                $track_data['tracks_information'] = $this->LgStringSwitch('订单申请减免学费，审核拒绝');
                $track_data['tracks_note'] = $request['reason'];
                $track_data['tracks_url'] = $request['tracks_url'];
                $track_data['staffer_id'] = $this->publicarray['staffer_id'];
                $track_data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                $track_data['tracks_time'] = time();
                $this->DataControl->insertData("smc_payfee_order_tracks", $track_data);


                $achieveList = $this->DataControl->getList("smc_staffer_achieve", "order_pid='{$OrderOne['order_pid']}'");

                if ($achieveList) {
                    $data = array();
                    $data['achieve_status'] = -1;
                    $data['achieve_updatatime'] = time();

                    $this->DataControl->updateData("smc_staffer_achieve", "order_pid='{$OrderOne['order_pid']}'", $data);

                    $str = '订单审核拒绝,业绩失效';

                    foreach ($achieveList as $achieveOne) {
                        $data = array();
                        $data['achieve_id'] = $achieveOne['achieve_id'];
                        $data['tracks_title'] = $this->LgStringSwitch('减免订单审核拒绝');
                        $data['tracks_information'] = $this->LgStringSwitch($str);
                        $data['staffer_id'] = $this->stafferOne['staffer_id'];
                        $data['tracks_playname'] = $this->stafferOne['staffer_cnname'];
                        $data['tracks_time'] = time();
                        $this->DataControl->insertData("smc_staffer_achieve_track", $data);
                    }
                }
            }



            $this->error = 0;
            $this->errortip = '已拒绝该申请';
            return true;


        } else {
            $this->error = 1;
            $this->errortip = '请选择是否通过';
            return false;
        }
    }

    /**
     * 减免订单的详情
     * author: ling
     * 对应接口文档 0001
     */
    function getFeeWaiverOrder($request)
    {
        $sql = "
            select pop.order_pid,pop.pay_pid,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,s.school_branch,st.student_cnname,st.student_branch,st.student_enname,pop.pay_issuccess,pop.pay_img,pay_price,pay_note,po.school_id,po.staffer_id,FROM_UNIXTIME(pop.pay_createtime, '%Y-%m-%d %H:%i:%s') as pay_createtime,po.order_createtime,po.order_allprice
            from smc_payfee_order_pay as pop
            left join  smc_payfee_order as po ON pop.order_pid = po.order_pid
            left join  smc_school as s ON s.school_id = po.school_id
            left join  smc_student as st ON st.student_id = po.student_id
            where pop.pay_pid ='{$request['pay_pid']}'
        
              ";


        $payOrderOne = $this->DataControl->selectOne($sql);

        $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '已完成', '-1' => '已拒绝'));

        $payOrderOne['dealorder_type_name'] = $this->LgStringSwitch("减免学费");
        $payOrderOne['pay_issuccess_name'] = $status[$payOrderOne['pay_issuccess']];

        $order['order'] = $payOrderOne;


        $sql = "select co.course_cnname,co.course_branch,poc.ordercourse_buynums,ifnull(cl.class_cnname,'--') as class_cnname,ifnull(poc.ordercourse_enterclassdate,'--') as ordercourse_enterclassdate,ifnull(poc.class_id,0) as class_id
                from smc_payfee_order_course as poc 
                left join smc_payfee_order_pay as pop on poc.order_pid=pop.order_pid
                left join smc_course as co on co.course_id=poc.course_id
                left join smc_class as cl on cl.class_id=poc.class_id
                where pop.pay_pid ='{$request['pay_pid']}'
                ";

        $waiver = $this->DataControl->selectClear($sql);

        if ($waiver) {

            foreach ($waiver as &$one) {
                if (!$one['class_id'] || $one['class_id'] <= 0) {
                    $one['ordercourse_enterclassdate'] = '--';
                }
            }

        }


        $order['waiver'] = $waiver;
        $waiver_field = array();
        $k = 0;
        $waiver_field[$k]["fieldname"] = 'course_cnname';
        $waiver_field[$k]["fieldstring"] = '课程名称';
        $waiver_field[$k]["custom"] = 1;
        $waiver_field[$k]["show"] = 1;
        $k++;
        $waiver_field[$k]["fieldname"] = 'course_branch';
        $waiver_field[$k]["fieldstring"] = '课程编号';
        $waiver_field[$k]["custom"] = 1;
        $waiver_field[$k]["show"] = 1;
        $k++;
        $waiver_field[$k]["fieldname"] = 'ordercourse_buynums';
        $waiver_field[$k]["fieldstring"] = '购买课次';
        $waiver_field[$k]["custom"] = 1;
        $waiver_field[$k]["show"] = 1;

        $k++;
        $waiver_field[$k]["fieldname"] = 'class_cnname';
        $waiver_field[$k]["fieldstring"] = '班级名称';
        $waiver_field[$k]["custom"] = 1;
        $waiver_field[$k]["show"] = 1;

        $k++;
        $waiver_field[$k]["fieldname"] = 'ordercourse_enterclassdate';
        $waiver_field[$k]["fieldstring"] = '入班日期';
        $waiver_field[$k]["custom"] = 1;
        $waiver_field[$k]["show"] = 1;


        //缴费订单跟踪记录
        $sql = "select t.*,st.staffer_cnname,st.staffer_enname 
                from smc_payfee_order_tracks as t 
                left join smc_staffer as st on t.staffer_id=st.staffer_id 
                where t.order_pid='{$payOrderOne['order_pid']}'";

        $tradingList = $this->DataControl->selectClear($sql);

        $fileList = array();

        if ($tradingList) {
            foreach ($tradingList as $key => $value) {
                $tradingList[$key]['tracks_playname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '-' . $value['staffer_enname'] : $value['staffer_cnname'];
                $tradingList[$key]['tracks_time'] = date("Y-m-d H:i:s", $value['tracks_time']);

                if ($value['tracks_url'] != '') {
                    $fileList[] = array('tracks_url' => $value['tracks_url']);
                }
            }
        }

        $order['trading'] = $tradingList;
        $track_field = array();
        $k = 0;
        $track_field[$k]["fieldname"] = 'tracks_id';
        $track_field[$k]["fieldstring"] = '序号';
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_title';
        $track_field[$k]["fieldstring"] = '标签';
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_information';
        $track_field[$k]["fieldstring"] = '信息';
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_playname';
        $track_field[$k]["fieldstring"] = '操作人';
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        $k++;

        $track_field[$k]["fieldname"] = 'tracks_time';
        $track_field[$k]["fieldstring"] = '时间';
        $track_field[$k]["custom"] = 1;
        $track_field[$k]["show"] = 1;
        //        经办信息
        $info = $this->DataControl->selectClear("select (case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname,
                (select t.staffer_cnname from  smc_staffer as t where  t.staffer_id ='{$payOrderOne['staffer_id']}') as staffer_cnname,(select t.staffer_enname from  smc_staffer as t where  t.staffer_id ='{$payOrderOne['staffer_id']}') as staffer_enname
                from smc_school as  s  where s.school_id ='{$payOrderOne['school_id']}' ");

        $info[0]['create_time'] = date('Y-m-d H:i:s', $payOrderOne['order_createtime']);
        $info[0]['staffer_cnname'] = $info[0]['staffer_enname'] ? $info[0]['staffer_cnname'] . '-' . $info[0]['staffer_enname'] : $info[0]['staffer_cnname'];;

        $k++;
        $info_field[$k]["fieldstring"] = '经办分校';
        $info_field[$k]["fieldname"] = 'school_cnname';
        $info_field[$k]["custom"] = 1;
        $info_field[$k]["show"] = 1;
        $k++;
        $info_field[$k]["fieldstring"] = '经办人';
        $info_field[$k]["fieldname"] = 'staffer_cnname';
        $info_field[$k]["custom"] = 1;
        $info_field[$k]["show"] = 1;
        $k++;
        $info_field[$k]["fieldstring"] = '经办时间';
        $info_field[$k]["fieldname"] = 'create_time';
        $info_field[$k]["custom"] = 1;
        $info_field[$k]["show"] = 1;

        $order['info'] = $info;
        $order['fileList'] = $fileList;

        $order['track_field'] = $track_field;
        $order['info_field'] = $info_field;
        $order['waiver_field'] = $waiver_field;
        return $order;

    }

    function trialFreeList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' or s.student_enname like '%{$request['keyword']}%' or s.student_branch like '%{$request['keyword']}%' or cr.clockorder_pid like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and cr.school_id = '{$request['school_id']}'";
        }

        if (isset($request['clockorder_class']) && $request['clockorder_class'] !== '') {
            $datawhere .= " and cr.clockorder_class = '{$request['clockorder_class']}'";
        }

        $sql = "select cr.clockorder_id,cr.clockorder_pid,(case when sch.school_shortname='' then sch.school_cnname else sch.school_shortname end) as school_cnname,sch.school_branch,s.student_cnname,s.student_branch,cr.clockorder_createtime,cr.class_branch,c.class_cnname,c.class_enname,cr.clockorder_class,cr.clockorder_status,cr.clockorder_reason,cr.clockorder_playname
              from smc_student_clockorder as cr
              left join smc_student as s on s.student_branch=cr.student_branch
              left join smc_school as sch on sch.school_id=cr.school_id
              left join smc_class as c on c.class_branch=cr.class_branch
              where {$datawhere} and cr.company_id='{$request['company_id']}'
              order by cr.clockorder_createtime desc
              limit {$pagestart},{$num}
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "报表内容为空,不可导出";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['clockorder_pid'] = $dateexcelvar['clockorder_pid'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['clockorder_class_name'] = $dateexcelvar['clockorder_class_name'];
                    $datearray['clockorder_status_name'] = $dateexcelvar['clockorder_status_name'];
                    $datearray['clockorder_reason'] = $dateexcelvar['clockorder_reason'];
                    $datearray['clockorder_playname'] = $dateexcelvar['clockorder_playname'];
                    $datearray['clockorder_createtime'] = $dateexcelvar['clockorder_createtime'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("异动免审单号", "校区名称", "校区编号", "学员名称", "学员编号", '班级名称', "班级编号", "异动免审类型", "使用状态", "异动免审原因", "申请人名称", "创建时间"));
            $excelfileds = array('clockorder_pid', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'class_cnname', 'class_branch', 'clockorder_class_name', 'clockorder_status_name', 'clockorder_reason', 'clockorder_playname', 'clockorder_createtime');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "异动免审单.xlsx");
            exit;
        }

        $orderList = $this->DataControl->selectClear($sql);
        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无异动免审单数据";
            return false;
        }

        $class = $this->LgArraySwitch(array('0' => '回原班免扣课次申请'));
        $status = $this->LgArraySwitch(array('0' => '未使用', '1' => '已使用'));

        foreach ($orderList as &$orderOne) {
            $orderOne['clockorder_class_name'] = $class[$orderOne['clockorder_class']];
            $orderOne['clockorder_status_name'] = $status[$orderOne['clockorder_status']];
            $orderOne['clockorder_createtime'] = date("Y-m-d H:i:s", $orderOne['clockorder_createtime']);
        }


        $data = array();
        $count_sql = "select cr.clockorder_id
          from smc_student_clockorder as cr
          left join smc_student as s on s.student_branch=cr.student_branch
          left join smc_school as sch on sch.school_id=cr.school_id
          left join smc_class as c on c.class_branch=cr.class_branch
          where {$datawhere} and cr.company_id='{$request['company_id']}'";


        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $schoolList = $this->DataControl->selectClear("select s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname from smc_school as s left join smc_student_clockorder as cr on cr.school_id=s.school_id where cr.company_id='{$this->company_id}' group by s.school_id");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['list'] = $orderList;
        $data['school'] = $schoolList;

        return $data;
    }

    function classOpenApplyList($request)
    {
        $datawhere = " a.company_id='{$this->company_id}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' )";
        }
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id = '{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.openapply_createtime,'%Y-%m-%d') >= '{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.openapply_createtime,'%Y-%m-%d') <= '{$request['endtime']}'";
        }

        if (isset($request['openapply_status']) && $request['openapply_status'] !== '') {
            $datawhere .= " and a.openapply_status = '{$request['openapply_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select a.openapply_id,b.school_tagbak,a.school_id,b.school_cnname,b.school_branch,c.class_cnname,c.class_branch,a.openapply_studynum,a.openapply_minclassnum,a.openapply_status,a.openapply_checkpicurl,a.openapply_note,a.openapply_createtime,a.openapply_updatatime,d.staffer_cnname,ifnull(e.staffer_cnname,'--') as exam_staffer_cnname,a.exam_openapply_note,c.course_id
                from smc_class_openapply as a
                left join smc_school as b on a.school_id=b.school_id
                left join smc_class as c on c.class_id=a.class_id
                left join smc_staffer as d on d.staffer_id=a.staffer_id
                left join smc_staffer as e on e.staffer_id=a.exam_staffer_id
                where {$datawhere}
                order by a.openapply_createtime desc
              ";

        $status = $this->LgArraySwitch(array('0' => '待审核', '1' => '审核通过', '-1' => '审核拒绝'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);

            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "申请开班列表内容为空,不可导出";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $pricingOne = $this->getCoursePricing($dateexcelvar['course_id'], $this->company_id, $dateexcelvar['school_id']);
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_tagbak'] = $dateexcelvar['school_tagbak'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['tuition_minclassnum'] = $pricingOne ? $pricingOne['tuition_minclassnum'] : 0;
                    $datearray['openapply_studynum'] = $dateexcelvar['openapply_studynum'];
                    $datearray['openapply_note'] = $dateexcelvar['openapply_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['openapply_createtime'] = date("Y-m-d H:i", $dateexcelvar['openapply_createtime']);
                    $datearray['openapply_status_name'] = $status[$dateexcelvar['openapply_status']];
                    $datearray['exam_staffer_cnname'] = $dateexcelvar['exam_staffer_cnname'];
                    $datearray['openapply_updatatime'] = $dateexcelvar['openapply_updatatime'] > 0 ? date("Y-m-d H:i", $dateexcelvar['openapply_updatatime']) : '--';
                    $datearray['exam_openapply_note'] = $dateexcelvar['exam_openapply_note'] != '' ? $dateexcelvar['exam_openapply_note'] : '--';
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "督导区域", "班级名称", "班级编号", "最低开班限制", '当前开班人数', "申请原因", "申请人", "申请时间", "审核状态", "审核人", "审核时间", "审核备注"));
            $excelfileds = array('school_cnname', 'school_branch', 'school_tagbak', 'class_cnname', 'class_branch', 'tuition_minclassnum', 'openapply_studynum', 'openapply_note', 'staffer_cnname', 'openapply_createtime', 'openapply_status_name', 'exam_staffer_cnname', 'openapply_updatatime', 'exam_openapply_note');
            query_to_excel($excelheader, $outexceldate, $excelfileds, "申请开班审核列表.xlsx");
            exit;
        }

        $sql .= " limit {$pagestart},{$num}";
        $openList = $this->DataControl->selectClear($sql);
        if (!$openList) {
            $this->error = true;
            $this->errortip = "无申请开班审核数据";
            return false;
        }
        foreach ($openList as &$openOne) {
            $pricingOne = $this->getCoursePricing($openOne['course_id'], $this->company_id, $openOne['school_id']);
            $openOne['tuition_minclassnum'] = $pricingOne ? $pricingOne['tuition_minclassnum'] : 0;
            $openOne['openapply_createtime'] = date("Y-m-d H:i", $openOne['openapply_createtime']);
            $openOne['openapply_status_name'] = $status[$openOne['openapply_status']];
            $openOne['openapply_updatatime'] = $openOne['openapply_updatatime'] > 0 ? date("Y-m-d H:i", $openOne['openapply_updatatime']) : '--';
            $openOne['exam_openapply_note'] = $openOne['exam_openapply_note'] != '' ? $openOne['exam_openapply_note'] : '--';
        }

        $data = array();
        $count_sql = "select a.openapply_id
                from smc_class_openapply as a
                left join smc_school as b on a.school_id=b.school_id
                left join smc_class as c on c.class_id=a.class_id
                where {$datawhere}";

        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $openList;

        return $data;
    }

    function examOpenClass($request)
    {

        $openOne = $this->DataControl->getFieldOne("smc_class_openapply", "openapply_status", "openapply_id='{$request['openapply_id']}'");

        if (!$openOne) {
            $this->error = true;
            $this->errortip = "无申请开班审核数据";
            return false;
        }

        if ($openOne['openapply_status'] != 0) {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }

        if ($request['is_adopt'] == 0) {

            $data = array();
            $data['openapply_status'] = -1;
            $data['exam_staffer_id'] = $request['staffer_id'];
            $data['exam_openapply_note'] = $request['note'];
            $data['openapply_updatatime'] = time();

            $this->DataControl->updateData("smc_class_openapply", "openapply_id='{$request['openapply_id']}'", $data);

            return true;

        } elseif ($request['is_adopt'] == 1) {
            $data = array();
            $data['openapply_status'] = 1;
            $data['exam_staffer_id'] = $request['staffer_id'];
            $data['exam_openapply_note'] = $request['note'];
            $data['openapply_updatatime'] = time();

            $this->DataControl->updateData("smc_class_openapply", "openapply_id='{$request['openapply_id']}'", $data);

            return true;

        } else {
            $this->error = true;
            $this->errortip = "不存在该状态";
            return false;
        }
    }

    function batchExamOpenClass($request)
    {


        $openList = json_decode(stripslashes($request['openList']), 1);

        if (!$openList) {
            $this->error = true;
            $this->errortip = "请选择数据";
            return false;
        }

        $t_num = 0;
        $f_num = 0;
        foreach ($openList as $openOne) {

            $data = array();
            $data['openapply_id'] = $openOne['openapply_id'];
            $data['is_adopt'] = $openOne['is_adopt'];
            $data['note'] = $openOne['note'];
            $data['staffer_id'] = $request['staffer_id'];

            $bool = $this->examOpenClass($data);
            if ($bool) {
                $t_num++;
            } else {
                $f_num++;
            }
        }

        $this->oktip = '批量成功' . $t_num . '条';
        return true;
    }

    function getCoursePricing($course_id, $company_id, $school_id)
    {
        $day = date("Y-m-d", time());
        $sql = "SELECT
                    t.course_id,
                    p.pricing_id,
                    t.tuition_id,
                    t.tuition_originalprice,
                    t.tuition_sellingprice,
                    t.tuition_buypiece,
                    t.tuition_unitprice,
                    a.agreement_id,
                    t.tuition_addtime,
                    t.tuition_refundprice,sc.coursetype_id,t.tuition_minclassnum
                FROM
                    smc_fee_pricing_tuition AS t,
                    smc_fee_pricing AS p,
                    smc_fee_agreement AS a,
                    smc_course as sc    
                WHERE
                    t.pricing_id = p.pricing_id
                AND p.agreement_id = a.agreement_id
                AND sc.course_id = p.course_id
                AND t.course_id = '{$course_id}'
                AND (
                    (
                        p.pricing_applytype = '1'
                        AND p.pricing_id IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$school_id}'
                        )
                    )
                    OR (
                        p.pricing_applytype = '-1'
                        AND p.pricing_id NOT IN (
                            SELECT
                                pricing_id
                            FROM
                                smc_fee_pricing_apply AS a
                            WHERE
                                a.school_id = '{$school_id}'
                        )
                    )
                    OR (p.pricing_applytype = '0')
                )
                AND a.agreement_startday <= '{$day}'
                AND a.agreement_endday >= '{$day}'
                AND a.agreement_status = '1'
                AND a.company_id = '{$company_id}'
                GROUP BY
                    t.course_id";
        $pricingOne = $this->DataControl->selectOne($sql);
        if ($pricingOne) {
            return $pricingOne;
        } else {
            return array();
        }
    }

    function canceldebtsPay($request)
    {
        $datawhere = " 1 ";
        $datawhere .= " and o.company_id = '{$this->company_id}' and  po.paytype_code='canceldebts'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%'  or s.student_enname like '%{$request['keyword']}%'  or s.student_branch like '%{$request['keyword']}%'  or o.order_pid like '%{$request['keyword']}%' or po.pay_pid like '%{$request['keyword']}%' or o.trading_pid like '%{$request['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and o.school_id ='{$request['school_id']}'";
        }
        if (isset($request['status']) && $request['status'] !== "") {
            $datawhere .= " and po.pay_issuccess ='{$request['status']}'";
        }
        //申请时间
        if (isset($request['apply_start_time']) && $request['apply_start_time'] !== '') {
            $lastday = strtotime($request['apply_start_time']);
            $datawhere .= " and po.pay_createtime >= '{$lastday}'";
        }
        if (isset($request['apply_end_time']) && $request['apply_end_time'] !== '') {
            $firstday = strtotime($request['apply_end_time'] . '23:59:59');
            $datawhere .= " and po.pay_createtime <= '{$firstday}'";
        }
        //审核时间
        if (isset($request['audit_start_time']) && $request['audit_start_time'] !== '') {
            $lastday = strtotime($request['audit_start_time']);
            $datawhere .= " and po.pay_updatatime >= '{$lastday}'";
        }
        if (isset($request['audit_end_time']) && $request['audit_end_time'] !== '') {
            $firstday = strtotime($request['audit_end_time'] . '23:59:59');
            $datawhere .= " and po.pay_updatatime <= '{$firstday}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($request['dataequity'] != '1') {
            $organize_id = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " and exists (SELECT os.school_id FROM gmc_company_organizeschool AS os WHERE os.organize_id = '{$organize_id['organize_id']}' and os.school_id = o.school_id) ";
        }

        $sql = "select s.student_cnname,s.student_enname,s.student_branch,po.order_pid,po.pay_pid,po.pay_note,po.pay_price,po.pay_issuccess,po.pay_createtime,po.pay_updatatime,(case when sl.school_shortname='' then sl.school_cnname else sl.school_shortname end) as school_cnname,sl.school_branch,o.trading_pid,o.order_paidprice,o.order_paymentprice,o.order_arrearageprice,o.order_allprice
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_student as s ON o.student_id = s.student_id
            left join smc_school as sl On sl.school_id = o.school_id
            WHERE
                {$datawhere}
            ORDER BY
                po.pay_createtime DESC";

        $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已拒绝"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);


            foreach ($dateexcelarray as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                $val['pay_createtime'] = date("Y-m-d", $val['pay_createtime']);
                if ($val['pay_updatatime']) {
                    $val['pay_updatatime'] = date("Y-m-d", $val['pay_updatatime']);
                } else {
                    $val['pay_updatatime'] = '--';
                }
            }
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $datearray['order_allprice'] = $dateexcelvar['order_allprice'];
                    $datearray['order_paidprice'] = $dateexcelvar['order_paidprice'];
                    $datearray['order_arrearageprice'] = $dateexcelvar['order_arrearageprice'];
                    $datearray['order_paymentprice'] = $dateexcelvar['order_paymentprice'];
                    $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    $datearray['pay_note'] = $dateexcelvar['pay_note'];
                    $datearray['pay_issuccess_name'] = $dateexcelvar['pay_issuccess_name'];
                    $datearray['pay_createtime'] = $dateexcelvar['pay_createtime'];
                    $datearray['pay_updatatime'] = $dateexcelvar['pay_updatatime'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '学员中文名', '学员英文名', '学员编号', '交易编号', '订单总额', '已付金额', '欠费金额', '实付金额', '坏账处理金额', '坏账处理原因', '审核状态', '申请时间', '审核时间'));
            $excelfileds = array('school_cnname', 'school_branch', 'student_cnname', 'student_enname', 'student_branch', 'trading_pid', 'order_allprice', 'order_paidprice', 'order_arrearageprice', 'order_paymentprice', 'pay_price', 'pay_note', 'pay_issuccess_name', 'pay_createtime', 'pay_updatatime');

            $fielname = $this->LgStringSwitch("坏账处理审核");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $orderList = $this->DataControl->selectClear($sql);
        }

        if (!$orderList) {
            $this->error = true;
            $this->errortip = "无订单数据";
            return false;
        }
        if ($orderList) {

            foreach ($orderList as &$val) {
                $val['pay_issuccess_name'] = $status[$val['pay_issuccess']];
                $val['pay_createtime'] = date("Y-m-d", $val['pay_createtime']);
                if ($val['pay_updatatime']) {
                    $val['pay_updatatime'] = date("Y-m-d", $val['pay_updatatime']);
                } else {
                    $val['pay_updatatime'] = '--';
                }
            }
        }

        $all_num = $this->DataControl->selectOne("
        select count(po.pay_id) as  all_num
        from smc_payfee_order_pay as  po
        left join smc_payfee_order as o ON po.order_pid = o.order_pid
        left join smc_student as s ON o.student_id = s.student_id
        left join smc_school as sl On sl.school_id = o.school_id
        WHERE {$datawhere}");

        if ($all_num) {
            $allnum = $all_num['all_num'];
        } else {
            $allnum = 0;
        }
        $data = array();
        $schoolList = $this->DataControl->selectClear("
            select  DISTINCT s.school_id,(case when s.school_shortname='' then s.school_cnname else s.school_shortname end) as school_cnname
            from smc_payfee_order_pay as  po
            left join smc_payfee_order as o ON po.order_pid = o.order_pid
            left join smc_school as s on s.school_id=o.school_id
            WHERE
                s.company_id='{$this->company_id}' and po.paytype_code='canceldebts'
                group by s.school_id
                ");
        if (!$schoolList) {
            $schoolList = array();
        }
        $data['allnum'] = $allnum;
        $data['list'] = $orderList;
        $data['school'] = $schoolList;

        return $data;
    }

    function studentLossClass($request)
    {
        $sql = "select ss.class_id,c.class_cnname,c.class_enname,c.class_branch
			  from smc_student_study as ss
			  left join smc_class as c on c.class_id=ss.class_id
			  where ss.student_id='{$request['student_id']}' and ss.study_isreading='-1' and ss.school_id='{$request['school_id']}'
			  order by ss.class_id desc
			  ";

        $classList = $this->DataControl->selectClear($sql);

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无可申请班级";
            return false;
        }

        return $classList;
    }

    function createRandom($initial)
    {
        $Str = "0123456789";
        $rangtr = $Str[rand(0, 9)] . $Str[rand(0, 9)] . $Str[rand(0, 9)] . $Str[rand(0, 9)] . $Str[rand(0, 9)] . $Str[rand(0, 9)] . $Str[rand(0, 9)] . $Str[rand(0, 9)];
        $Random = $initial . $rangtr;
        return $Random;
    }

    function applyFreeClass($request)
    {

        if (isset($request['create_time']) && $request['create_time'] != '') {
            $time = strtotime($request['create_time']);
        } else {
            $time = time();
        }

        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_branch", "student_id='{$request['student_id']}'");
        if (!$studentOne) {
            $this->error = true;
            $this->errortip = "无该申请学员";
            return false;
        }

        $classOne = $this->DataControl->getFieldOne("smc_class", "class_branch", "class_id='{$request['class_id']}'");

        if (!$classOne) {
            $this->error = true;
            $this->errortip = "无该申请班级";
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_cnname", "staffer_id='{$request['staffer_id']}'");

        if (!$stafferOne) {
            $this->error = true;
            $this->errortip = "无该申请人";
            return false;
        }

        if (!$this->DataControl->getFieldOne("smc_student_study", "study_id", "student_id='{$request['student_id']}' and class_id='{$request['class_id']}' and study_isreading='-1'")) {
            $this->error = true;
            $this->errortip = "该学员不符合申请条件";
            return false;
        }

        do {
            $clockorder_pid = $this->createRandom('MK');
        } while ($this->DataControl->selectOne("select clockorder_id from smc_student_clockorder where clockorder_pid='{$clockorder_pid}' limit 0,1"));

        $data = array();
        $data['company_id'] = $request['company_id'];
        $data['clockorder_pid'] = $clockorder_pid;
        $data['staffer_id'] = $request['staffer_id'];
        $data['clockorder_playname'] = $stafferOne['staffer_cnname'];
        $data['school_id'] = $request['school_id'];
        $data['class_branch'] = $classOne['class_branch'];
        $data['student_branch'] = $studentOne['student_branch'];
        $data['clockorder_class'] = $request['clockorder_class'];
        $data['clockorder_status'] = 0;
        $data['clockorder_reason'] = $request['reason'];
        $data['clockorder_createtime'] = $time;

        if ($this->DataControl->insertData("smc_student_clockorder", $data)) {
            return true;
        } else {
            $this->error = true;
            $this->errortip = "数据库错误";
            return false;
        }
    }

    function autoLossList($request)
    {

        $datawhere = " 1 ";
        $having = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (e.student_cnname like '%{$request['keyword']}%' or e.student_enname like '%{$request['keyword']}%' or e.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}'";
        } else {
            $this->error = true;
            $this->errortip = "请选择学校";
            return false;
        }

        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $having .= " and (hour_day <= '{$request['fixedtime']}' and hour_day<>'--')";
        }

        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and c.district_id = '{$request['district_id']}'";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and b.coursetype_id = '{$request['coursetype_id']}'";
        } else {
            $this->error = true;
            $this->errortip = "请选择班组";
            return false;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select c.school_branch,c.school_cnname,b.coursetype_id,d.coursetype_cnname,e.student_id,e.student_cnname,e.student_branch,sum(a.coursebalance_figure) as coursebalance_figure,sum(a.coursebalance_time) as coursebalance_time
                ,ifnull((SELECT r.district_cnname FROM gmc_company_district AS r WHERE r.district_id = c.district_id),'--') as district_cnname
                ,ifnull((select scl.changelog_day from smc_student_changelog as scl,smc_code_stuchange as cs where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=0 and scl.student_id=a.student_id and scl.school_id=a.school_id and cs.stustatus_isenclass=0 order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'--') as changelog_day
                ,ifnull((select max(FROM_UNIXTIME(timelog_time,'%Y-%m-%d')) from smc_student_coursebalance_timelog x,smc_course y where x.course_id=y.course_id and x.student_id=a.student_id and x.school_id=a.school_id and y.coursetype_id=b.coursetype_id),'--') as hour_day
                ,(select sum(sb.student_balance+sb.student_withholdbalance) from smc_student_balance as sb where sb.student_id=a.student_id and sb.school_id=a.school_id) as balance
                ,ifnull((select scl.stuchange_code from smc_student_changelog as scl,smc_code_stuchange as cs where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=0 and scl.student_id=a.student_id and scl.school_id=a.school_id and scl.coursetype_id=b.coursetype_id order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'') as stuchange_code
                ,ifnull((select cs.stustatus_isenschool from smc_student_changelog as scl,smc_code_stuchange as cs where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=1 and scl.student_id=a.student_id and scl.school_id=a.school_id order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'') as stustatus_isenschool
                ,ifnull((select x.coursetypeloss_id from smc_student_coursetypeloss as x where x.student_id=a.student_id and x.school_id=a.school_id and x.coursetype_id=b.coursetype_id),0) as coursetypeloss_id
                from smc_student_coursebalance as a,smc_course as b,smc_school as c,smc_code_coursetype as d,smc_student as e 
                where {$datawhere} and a.course_id=b.course_id and a.school_id=c.school_id and b.coursetype_id=d.coursetype_id and a.student_id=e.student_id and not exists(select 1 from smc_student_study as x,smc_class as y,smc_course as z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=a.student_id and z.coursetype_id=b.coursetype_id and x.study_isreading=1 and x.school_id=a.school_id limit 0,1) 
                group by a.student_id,b.coursetype_id
                having {$having} and stuchange_code<>'C04' 
                and coursebalance_figure=0 
                and balance < 1000 
                and coursebalance_time=0  
                and (stustatus_isenschool=1 or stustatus_isenschool='')
                 order by (case when c.school_istest=0 and c.school_isclose=0 then 1 when c.school_isclose=0 then 2 when c.school_istest=0 then 3 else 4 end),c.school_istest asc,field(c.school_sort,0),c.school_sort asc,c.school_createtime asc
              ";

        // ,ifnull((select x.coursetypeloss_id from smc_student_coursetypeloss as x left join (select aa.* from smc_student_coursetypeloss_apply as aa where aa.student_id=a.student_id and aa.school_id=a.school_id and aa.coursetype_id=b.coursetype_id order by aa.apply_createtime desc limit 0,1) as y on x.student_id=y.student_id and x.school_id=y.school_id and x.coursetype_id=y.coursetype_id where x.student_id=a.student_id and x.school_id=a.school_id and x.coursetype_id=b.coursetype_id and ((y.apply_status=1 and FROM_UNIXTIME(y.apply_updatetime,'%Y-%m-%d') < DATE_SUB(NOW(),INTERVAL 180 DAY)) or y.apply_id is null)),0) as coursetypeloss_id
        $today = strtotime(date("Y-m-d"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $datearray['hour_day'] = $dateexcelvar['hour_day'];

                    $lastdate = strtotime($dateexcelvar['hour_day'] != '--' ? $dateexcelvar['hour_day'] : date("Y-m-d"));
                    $days = round(($today - $lastdate) / 3600 / 24);
                    $datearray['warning_days'] = $days > 0 ? $days : '--';
                    $datearray['balance'] = $dateexcelvar['balance'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区编号', '校区名称', '所属区域', '学员编号', '学员中文名', '班组', '最后出班日期', '最后耗课日期', '预警天数', '账户余额', '班组剩余课程余额'));
            $excelfileds = array('school_branch', 'school_cnname', 'district_cnname', 'student_branch', 'student_cnname', 'coursetype_cnname', 'changelog_day', 'hour_day', 'warning_days', 'balance', 'coursebalance_figure');

            $fielname = $this->LgStringSwitch("班级自动流失明细表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num}";

            $studentList = $this->DataControl->selectClear($sql);
            if (!$studentList) {
                $this->error = true;
                $this->errortip = "无学员数据";
                return false;
            }

            foreach ($studentList as &$studentOne) {
                $lastdate = strtotime($studentOne['hour_day'] != '--' ? $studentOne['hour_day'] : date("Y-m-d"));
                $days = round(($today - $lastdate) / 3600 / 24);
                $studentOne['warning_days'] = $days > 0 ? $days : '--';
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select c.school_branch,c.school_cnname,b.coursetype_id,d.coursetype_cnname,e.student_id,e.student_cnname,e.student_branch,sum(a.coursebalance_figure) as coursebalance_figure,sum(a.coursebalance_time) as coursebalance_time
                ,ifnull((select max(FROM_UNIXTIME(timelog_time,'%Y-%m-%d')) from smc_student_coursebalance_timelog x,smc_course y where x.course_id=y.course_id and x.student_id=a.student_id and x.school_id=a.school_id and y.coursetype_id=b.coursetype_id),'--') as hour_day
                ,ifnull((select scl.stuchange_code from smc_student_changelog as scl,smc_code_stuchange as cs where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=0 and scl.student_id=a.student_id and scl.school_id=a.school_id and scl.coursetype_id=b.coursetype_id order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'') as stuchange_code
                ,(select sum(sb.student_balance+sb.student_withholdbalance) from smc_student_balance as sb where sb.student_id=a.student_id and sb.school_id=a.school_id) as balance
                ,ifnull((select cs.stustatus_isenschool from smc_student_changelog as scl,smc_code_stuchange as cs where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type=1 and scl.student_id=a.student_id and scl.school_id=a.school_id order by scl.changelog_day desc,scl.changelog_id desc limit 0,1),'') as stustatus_isenschool
                from smc_student_coursebalance as a,smc_course as b,smc_school as c,smc_code_coursetype as d,smc_student as e 
                where {$datawhere} and a.course_id=b.course_id and a.school_id=c.school_id and b.coursetype_id=d.coursetype_id and a.student_id=e.student_id and not exists(select 1 from smc_student_study as x,smc_class as y,smc_course as z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=a.student_id and z.coursetype_id=b.coursetype_id and x.study_isreading=1 and x.school_id=a.school_id limit 0,1) 
                group by a.student_id,b.coursetype_id
                having {$having} and stuchange_code<>'C04' 
                and coursebalance_figure=0
                and balance<1000 
                and coursebalance_time=0 
                and (stustatus_isenschool=1 or stustatus_isenschool='')                 
              ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $studentList;

            return $data;
        }
    }

    //异动申请记录
    function getGmcChangeApplyApi($request)
    {
        $datawhere = " a.company_id ='{$request['company_id']}' ";
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.student_cnname like '%{$request['keyword']}%' 
            or s.student_enname like '%{$request['keyword']}%' 
            or s.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and a.apply_time>='{$request['start_time']}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and a.apply_time<='{$request['end_time']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.from_school_id='{$request['school_id']}'";
        }

        if (isset($request['stuchange_code']) && $request['stuchange_code'] !== '') {
            $datawhere .= " and a.stuchange_code='{$request['stuchange_code']}'";
        }

        if (isset($request['apply_status']) && $request['apply_status'] !== '') {
            $datawhere .= " and a.apply_status ='{$request['apply_status']}'";
        }


        $sql = "select s.student_cnname,s.student_enname,s.student_branch,a.stuchange_code,a.apply_status,a.apply_reason,a.apply_remark,a.apply_time,a.apply_refusereson,a.from_staffer_id,
                f.staffer_cnname,f.staffer_enname
                ,(select g.stuchange_name from smc_code_stuchange as g where g.stuchange_code = a.stuchange_code) as stuchange_name 
                ,h.school_shortname as from_school_shortname,h.school_branch as from_school_branch
                ,(select l.school_shortname from smc_school as l where l.school_id = a.to_school_id) as to_school_shortname 
                ,(select concat(t.staffer_cnname,(CASE WHEN ifnull(t.staffer_enname,'') = '' THEN '' ELSE concat( '/',t.staffer_enname) END) )  from smc_staffer as t where t.staffer_id = a.staffer_id) as to_staffer_name
				from smc_student_change_apply as a 
				left join smc_student as s ON a.student_id = s.student_id 
				left join smc_staffer as f ON a.from_staffer_id = f.staffer_id 
				left join smc_school as h ON  h.school_id = a.from_school_id
 				where {$datawhere} 
 				order by a.apply_time desc,a.apply_id desc ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无异动数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['from_school_shortname'] = $dateexcelvar['from_school_shortname'];
                    $datearray['from_school_branch'] = $dateexcelvar['from_school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_enname'] = $dateexcelvar['student_enname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['stuchange_name'] = $dateexcelvar['stuchange_name'];
                    if ($dateexcelvar['apply_status'] == '0') {
                        $datearray['apply_status_name'] = $this->LgStringSwitch('待确认');
                    } elseif ($dateexcelvar['apply_status'] == '1') {
                        $datearray['apply_status_name'] = $this->LgStringSwitch('已同意');
                    } elseif ($dateexcelvar['apply_status'] == '-1') {
                        $datearray['apply_status_name'] = $this->LgStringSwitch('已拒绝');
                    }
                    $datearray['to_school_shortname'] = $dateexcelvar['to_school_shortname'];
                    $datearray['apply_reason'] = $dateexcelvar['apply_reason'];
                    $datearray['apply_time'] = $dateexcelvar['apply_time'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_cnname'] . ($dateexcelvar['staffer_enname'] ? '-' . $dateexcelvar['staffer_enname'] : '');
                    $datearray['to_staffer_name'] = $dateexcelvar['to_staffer_name'];
                    $datearray['apply_refusereson'] = $dateexcelvar['apply_refusereson'] ? $dateexcelvar['apply_refusereson'] : '--';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("学校名称", "学校编号", "学员中文名", "学员英文名", "学员编号", "异动类型", "异动状态", "异动学校", "异动描述", "异动日期", "执行人", "审批人", "操作"));
            $excelfileds = array('from_school_shortname', 'from_school_branch', 'student_cnname', 'student_enname', 'student_branch', 'stuchange_name', 'apply_status_name', 'to_school_shortname', 'apply_reason', 'apply_time', 'staffer_name', 'to_staffer_name', 'apply_refusereson');
            $tem_name = $this->LgStringSwitch('学员异动申请记录管理表.xls');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $changeList = $this->DataControl->selectClear($sql);

            if (!$changeList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($changeList as &$changevar) {
                if ($changevar['apply_status'] == '0') {
                    $changevar['apply_status_name'] = $this->LgStringSwitch('待确认');
                } elseif ($changevar['apply_status'] == '1') {
                    $changevar['apply_status_name'] = $this->LgStringSwitch('已同意');
                } elseif ($changevar['apply_status'] == '-1') {
                    $changevar['apply_status_name'] = $this->LgStringSwitch('已拒绝');
                }
                $changevar['staffer_name'] = $changevar['staffer_cnname'] . ($changevar['staffer_enname'] ? '-' . $changevar['staffer_enname'] : '');
                $changevar['apply_refusereson'] = $changevar['apply_refusereson'] ? $changevar['apply_refusereson'] : '--';
            }

            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.apply_id 
				from smc_student_change_apply as a 
				left join smc_student as s ON a.student_id = s.student_id 
				left join smc_staffer as f ON a.from_staffer_id = f.staffer_id 
 				where {$datawhere} ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $data['list'] = $changeList;
            return $data;
        }
    }

    function getSignStuLossApplyList($request)
    {

        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.student_cnname like '%{$request['keyword']}%' or b.student_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id = '{$request['school_id']}'";
        }

        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and d.district_id='{$request['district_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.apply_createtime, '%Y-%m-%d' )>='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(a.apply_createtime, '%Y-%m-%d' )<='{$request['endtime']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['apply_status']) && $request['apply_status'] !== '') {
            $datawhere .= " and a.apply_status = '{$request['apply_status']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.apply_id,a.apply_status,a.apply_note,b.student_branch,b.student_cnname,c.coursetype_cnname,d.school_cnname,d.school_branch,FROM_UNIXTIME(a.apply_createtime, '%Y-%m-%d %H:%i:%s' ) as apply_createtime,if(a.apply_refusetime=0,'--',FROM_UNIXTIME(a.apply_refusetime, '%Y-%m-%d %H:%i:%s' )) as apply_refusetime,e.staffer_cnname,a.apply_reason
                ,ifnull((SELECT x.district_cnname FROM gmc_company_district AS x WHERE x.district_id = d.district_id),'--') as district_cnname
                ,ifnull((select y.hour_day
						from smc_student_hourstudy x,smc_class_hour y,smc_class z,smc_course w 
						where x.hour_id=y.hour_id 
						and y.class_id=z.class_id 
						and z.course_id=w.course_id 
						and x.student_id=a.student_id
						and z.school_id=a.school_id
						and w.coursetype_id=a.coursetype_id
						order by y.hour_day desc limit 0,1),'--') as last_atte_date
                ,ifnull((select x.study_endday from smc_student_study as x,smc_class as y,smc_course as z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=a.student_id and x.school_id=a.school_id and z.coursetype_id=a.coursetype_id order by x.study_endday desc limit 0,1),'--') as study_endday
                ,FROM_UNIXTIME((select max(timelog_time) from smc_student_coursebalance_timelog x,smc_course y 
				where x.course_id=y.course_id and x.student_id=a.student_id and x.school_id=a.school_id and y.coursetype_id=a.coursetype_id),'%Y-%m-%d') as last_timelog_time
                ,(select sum(x.student_balance+x.student_withholdbalance) from smc_student_balance x where x.school_id=a.school_id and x.student_id=a.student_id ) as left_balance
                ,ifnull((select sum(x.coursebalance_figure) from smc_student_coursebalance as x,smc_course as y where x.course_id=y.course_id and x.student_id=a.student_id and x.school_id=a.school_id and y.coursetype_id=a.coursetype_id),0) as left_amount
                from smc_student_coursetypeloss_apply as a 
                left join smc_student as b on a.student_id=b.student_id
                left join smc_code_coursetype as c on c.coursetype_id=a.coursetype_id
                left join smc_school as d on d.school_id=a.school_id
                left join smc_staffer as e on e.staffer_id=a.staffer_id
                where {$datawhere} and b.company_id='{$this->company_id}'
                order by a.apply_createtime desc
              ";

        $status = $this->LgArraySwitch(array("0" => "待审核", "1" => "已通过", "-1" => "已拒绝"));


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];//订单编号
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];//校区名称
                    $datearray['district_cnname'] = $dateexcelvar['district_cnname'];//校区编号
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];//学员名称
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];//学员编号
                    $datearray['apply_status_name'] = $status[$dateexcelvar['apply_status']];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];//订单备注
                    $datearray['apply_note'] = $dateexcelvar['apply_note'];
                    $datearray['staffer_cnname'] = $dateexcelvar['staffer_cnname'];
                    $datearray['apply_reason'] = $dateexcelvar['apply_reason'];
                    $datearray['apply_createtime'] = $dateexcelvar['apply_createtime'];
                    $datearray['apply_refusetime'] = $dateexcelvar['apply_refusetime'];
                    $datearray['study_endday'] = $dateexcelvar['study_endday'];
                    $datearray['last_atte_date'] = $dateexcelvar['last_atte_date'];

                    $today = strtotime(date("Y-m-d"));
                    $lastdate = strtotime($dateexcelvar['last_timelog_time'] ? $dateexcelvar['last_timelog_time'] : date("Y-m-d"));
                    $days = round(($today - $lastdate) / 3600 / 24);

                    $datearray['warning_days'] = $days > 0 ? $days : '--';
                    $datearray['left_balance'] = $dateexcelvar['left_balance'];
                    $datearray['left_amount'] = $dateexcelvar['left_amount'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区编号', '校区名称', '所属区域', '学员编号', '学员中文名', '审批状态', '班组', '申请原因', '申请人', '审核备注', '申请时间', '审核时间', '最后出班日期', '最后耗课日期', '预警天数', '账户余额', '班组剩余课程余额'));
            $excelfileds = array('school_branch', 'school_cnname', 'district_cnname', 'student_branch', 'student_cnname', 'apply_status_name', 'coursetype_cnname', 'apply_note', 'staffer_cnname', 'apply_reason', 'apply_createtime', 'apply_refusetime', 'study_endday', 'last_atte_date', 'warning_days', 'left_balance', 'left_amount');

            $fielname = $this->LgStringSwitch("逾期延班申请审批表");

            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}.xlsx");
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $applyList = $this->DataControl->selectClear($sql);

            if (!$applyList) {
                $this->error = true;
                $this->errortip = "无申请数据";
                return false;
            }

            foreach ($applyList as &$applyOne) {
                $applyOne['apply_status_name'] = $status[$applyOne['apply_status']];

                $today = strtotime(date("Y-m-d"));
                $lastdate = strtotime($applyOne['last_timelog_time'] ? $applyOne['last_timelog_time'] : date("Y-m-d"));
                $days = round(($today - $lastdate) / 3600 / 24);
                $applyOne['warning_days'] = $days > 0 ? $days : '--';
            }

            $data = array();

            $count_sql = "select a.apply_id
                from smc_student_coursetypeloss_apply as a 
                left join smc_student as b on a.student_id=b.student_id
                left join smc_code_coursetype as c on c.coursetype_id=a.coursetype_id
                left join smc_school as d on d.school_id=a.school_id
                left join smc_staffer as e on e.staffer_id=a.staffer_id
                where {$datawhere} and b.company_id='{$this->company_id}'
                order by a.apply_createtime desc";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;
            $data['list'] = $applyList;

            return $data;
        }


    }

    function examSignStuLoss($request)
    {

        $applyOne = $this->DataControl->getOne("smc_student_coursetypeloss_apply", "apply_id='{$request['apply_id']}'");

        if (!$applyOne) {
            $this->error = true;
            $this->errortip = "无申请数据";
            return false;
        }

        if ($applyOne['apply_status'] != 0) {
            $this->error = true;
            $this->errortip = "该状态不可审核";
            return false;
        }

        if ($request['is_adopt'] == 0) {

            $data = array();
            $data['apply_status'] = -1;
            $data['apply_reason'] = $request['note'];
            $data['apply_refusetime'] = time();
            $data['apply_updatetime'] = time();

            $this->DataControl->updateData("smc_student_coursetypeloss_apply", "apply_id='{$request['apply_id']}'", $data);

            return true;

        } elseif ($request['is_adopt'] == 1) {
            $data = array();
            $data['apply_status'] = 1;
            $data['apply_reason'] = $request['note'];
            $data['apply_updatetime'] = time();

            if ($this->DataControl->updateData("smc_student_coursetypeloss_apply", "apply_id='{$request['apply_id']}'", $data)) {
                $data = array();
                $data['company_id'] = $applyOne['company_id'];
                $data['school_id'] = $applyOne['school_id'];
                $data['student_id'] = $applyOne['student_id'];
                $data['coursetype_id'] = $applyOne['coursetype_id'];

                $this->DataControl->insertData("smc_student_coursetypeloss", $data);

                return true;
            } else {
                $this->error = true;
                $this->errortip = "审核失败";
                return false;
            }


        } else {
            $this->error = true;
            $this->errortip = "不存在该状态";
            return false;
        }
    }


}