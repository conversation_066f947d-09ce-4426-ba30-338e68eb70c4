<?php


namespace Model\Gmc;

class SuperviseModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();//操作人
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = true;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证订单信息
    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function arraySort($arr, $keys, $type = 'asc')
    {
        $keysvalue = $new_array = array();
        foreach ($arr as $k => $v) {
            $keysvalue[$k] = $v[$keys];
        }
        $type == 'asc' ? asort($keysvalue) : arsort($keysvalue);
        reset($keysvalue);
        foreach ($keysvalue as $k => $v) {
            $new_array[$k] = $arr[$k];
        }
        return $new_array;
    }

    function getTransferBill($request)
    {
//        if (!isset($request['agencyId']) || $request['agencyId'] == '') {
//            $this->error = true;
//            $this->errortip = "请选择主体";
//            return false;
//        }
//
//        if (!isset($request['start_time']) || $request['start_time'] == '' || !isset($request['end_time']) || $request['end_time'] == '') {
//            $this->error = true;
//            $this->errortip = "请选择时间";
//            return false;
//        }

        $datawhere = " 1 ";
        if (isset($request['agencyId']) && $request['agencyId'] !== '') {
            $datawhere .= " and A.agencyId='{$request['agencyId']}' ";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and A.transferDate >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and A.transferDate <='{$request['end_time']}'";
        }

        if (isset($request['batch_pid']) && $request['batch_pid'] !== '') {
            $datawhere .= " and a.transferId='{$request['batch_pid']}' ";
        }

        $sql = "select *
                from cmb_financial_transferlog a
                where {$datawhere} 
                order by transferDate desc";

        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = "无划拨数据";
            return false;
        }

        $status = $this->LgArraySwitch(array("-1" => "失败", "0" => "待同步", "1" => "同步中", "2" => "已同步"));
        $tem_array = array();
        foreach ($list as $listOne) {
            $data = array();
            $data['companies_cnname'] = $listOne['companies_cnname'];
            $data['transferId'] = $listOne['transferId'];
            $data['transferClassNum'] = $listOne['transferClassNum'];
            $data['sumTransferAmt'] = $listOne['sumTransferAmt'] / 100;
            $data['transferDate'] = $listOne['transferDate'];
            $data['tranSerial'] = $listOne['tranSerial'];
            $data['transferlog_status'] = $status[$listOne['transferlog_status']];
            $tem_array[] = $data;
        }

        if (!$tem_array) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        $tem_array = $this->arraySort($tem_array, 'transferDate', 'desc');

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $tem_array;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['transferId'] = $dateexcelvar['transferId'];
                    $datearray['transferClassNum'] = $dateexcelvar['transferClassNum'];
                    $datearray['sumTransferAmt'] = $dateexcelvar['sumTransferAmt'] / 100;
                    $datearray['transferDate'] = $dateexcelvar['transferDate'];
                    $datearray['tranSerial'] = $dateexcelvar['tranSerial'];
                    $datearray['transferlog_status'] = $status[$dateexcelvar['transferlog_status']];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "划拨编号", "划拨课程总数", "批次划拨总金额", "划拨完成日期", "银行划拨流水", "划拨状态"));
            $excelfileds = array('companies_cnname', 'transferId', 'transferClassNum', 'sumTransferAmt', 'transferDate', 'tranSerial', 'transferlog_status');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('监管账户资金划拨明细表.xlsx'));
            exit;
        } else {
            return $tem_array;
        }

    }

    function saveTransferlog($request)
    {
        $datawhere = " ";
        if (isset($request['agencyId']) && $request['agencyId'] !== '') {
            $datawhere .= " and a.agency_id='{$request['agencyId']}' ";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and b.income_date >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and b.income_date <='{$request['end_time']}'";
        }

        if (isset($request['batch_pid']) && $request['batch_pid'] !== '') {
            $datawhere .= " and a.batch_pid='{$request['batch_pid']}' ";
        }

        $sql = "select b.companies_id,b.companies_cnname,a.transfer_status
                ,a.agency_id,a.batch_pid,max(a.income_date) as income_date
                ,(select transferlog_id from cmb_financial_transferlog where transferId=a.batch_pid) as transferlog_id
                from cmb_trans_transfer a
                left join gmc_code_companies b on a.companies_id=b.companies_id
                where 1 {$datawhere}
                and a.batch_pid<>''
                and a.batch_pid not in (select transferId from cmb_financial_transferlog where transferlog_status=2)
                group by a.batch_pid 
                order by income_date
                -- limit 0,10";

        $batchList = $this->DataControl->selectClear($sql);
        if (!$batchList) {
            $this->error = true;
            $this->errortip = "无更新数据";
            return false;
        }

        foreach ($batchList as $batchOne) {
            $Model = new \Model\Api\CmbTransModel($batchOne['agency_id']);
            $listOne = $Model->queryTransferBill($batchOne['batch_pid']);
            if ($listOne) {
                $data = array();
                $data['companies_id'] = $batchOne['companies_id'];
                $data['companies_cnname'] = $batchOne['companies_cnname'];
                $data['agencyId'] = $batchOne['agency_id'];
                $data['transferId'] = $listOne['transferId'];
                $data['transferClassNum'] = $listOne['transferClassNum'];
                $data['sumTransferAmt'] = $listOne['sumTransferAmt'];
                $data['transferDate'] = $listOne['transferDate'];
                $data['tranSerial'] = $listOne['tranSerial'];
                $data['transferlog_status'] = 2;
                $data['transferlog_finishtime'] = strtotime($listOne['transferDate']) + 86399;
                if ($batchOne['transferlog_id'] > 0) {
                    $data['transferlog_updatetime'] = time();
                    $this->DataControl->updateData("cmb_financial_transferlog", " transferlog_id='{$batchOne['transferlog_id']}' ", $data);
                } else {
                    $data['transferlog_createtime'] = time();
                    $this->DataControl->insertData("cmb_financial_transferlog", $data);
                }
            } else {

            }
        }
        return true;
    }

    function saveAccountlog($request)
    {
        $sql = "select companies_id,companies_cnname,companies_agencyid,concat(FROM_UNIXTIME(companies_supervisetime,'%Y-%m'),'-01') AS start_date
            from gmc_code_companies 
            where company_id='8888' 
            and companies_id not in(9,78627)
            and companies_issupervise=1";
        $comList = $this->DataControl->selectClear($sql);
        if (!$comList) {
            $this->error = true;
            $this->errortip = "请选择时间";
            return false;
        }

        foreach ($comList as $comOne) {
            $now_date = date("Y-m-d");
            $start_date = date("Y-m-d", strtotime("-1 months", strtotime($now_date))); //同步最近1月
//            $start_date = $comOne['start_date']; //同步所有
            $end_date = $start_date;
            do {
                $start_date = $end_date;
                $end_date = date("Y-m-d", strtotime("+1 months", strtotime($start_date)));
                $Model = new \Model\Api\CmbTransModel($comOne['companies_agencyid']);
                $list = $Model->queryHostDetail($start_date, $end_date);

                if (!$list) {
                    continue;
                }

                foreach ($list as $listOne) {
                    $logOne = $this->DataControl->getFieldOne("cmb_financial_accountlog", "accountlog", "tranSerial='{$listOne['tranSerial']}'");
                    if ($logOne) {
                        continue;
                    }

                    $data = array();
                    $data['companies_id'] = $comOne['companies_id'];
                    $data['companies_cnname'] = $comOne['companies_cnname'];
                    $data['agencyId'] = $comOne['companies_agencyid'];
                    $data['tranStatus'] = $listOne['tranStatus'];
                    $data['regAccNo'] = $listOne['regAccNo'];
                    $data['regAccNo'] = $listOne['regAccNo'];
                    $data['transferId'] = $listOne['transferId'];
                    $data['tranSerial'] = $listOne['tranSerial'];
                    $data['transSequence'] = $listOne['transSequence'];
                    $data['tranAmt'] = $listOne['tranAmt'] / 100;
                    $data['tag'] = $listOne['tag'];
                    $data['tranDir'] = $listOne['tranDir'];
                    $data['tranDate'] = $listOne['tranDate'];
                    $data['tranTime'] = $listOne['tranTime'];
                    $data['currency'] = $listOne['currency'];
                    $data['rcvPayAcc'] = $listOne['rcvPayAcc'];
                    $data['rcvPayName'] = $listOne['rcvPayName'];
                    $data['rcvPayEbk'] = $listOne['rcvPayEbk'];
                    $data['rcvPayEbb'] = $listOne['rcvPayEbb'];
                    $data['accountlog_createtime'] = time();

                    $this->DataControl->insertData("cmb_financial_accountlog", $data);
                }
            } while ($start_date <= $now_date);
        }

        return true;
    }

    function saveBatchTransfer($request)
    {
        $datawhere = " ";

        $sql = "select a.*,b.companies_cnname
            from cmb_trans_log a
            left join gmc_code_companies b on a.companies_id=b.companies_id
            where 1
            and a.log_url='/pfs/head/addBatchTransfer'
            and not exists(select 1 from cmb_financial_batchlog where transferId=a.batch_pid)
            order by a.log_id";
        $logList = $this->DataControl->selectClear($sql);
        if (!$logList) {
            $this->error = true;
            $this->errortip = "暂无新增";
            return false;
        }

        foreach ($logList as $logOne) {
            $requestList = json_decode($logOne['log_requet_json'], 1);
            $backList = json_decode($logOne['log_back_json'], 1);

            $failClassArray = array();

            if ($backList && $backList['failList']) {
                foreach ($backList['failList'] as $failOne) {
                    $failClassArray[] = $failOne['classId'];
                }
            }

            $batchlog = array();
            $batchlog['companies_id'] = $logOne['companies_id'];
            $batchlog['companies_cnname'] = $logOne['companies_cnname'];
            $batchlog['agencyId'] = $logOne['agency_id'];
            $batchlog['transferId'] = $logOne['batch_pid'];
            $batchlog['transferNum'] = $requestList['transferNum'];
            $batchlog['applyDate'] = $requestList['applyDate'];
            $batchlog['sumTransferAmt'] = $requestList['sumTransferAmt'];
            $batchlog['log_id'] = $logOne['log_id'];
            $batchlog['batchlog_applytime'] = $logOne['log_time'];

            if ($requestList['transferList']) {
                foreach ($requestList['transferList'] as $transferOne) {
                    $batchlog['classId'] = $transferOne['classId'];
                    $batchlog['class_id'] = $transferOne['class_id'];
                    $batchlog['income_date'] = $transferOne['income_date'];
                    $batchlog['eliminateClassHour'] = $transferOne['eliminateClassHour'];
                    $batchlog['transferAmt'] = $transferOne['transferAmt'];
                    $batchlog['subTransferId'] = $transferOne['subTransferId'];
                    if ($failClassArray && in_array($transferOne['classId'], $failClassArray)) {
                        $batchlog['batchlog_status'] = '0';
                    } else {
                        $batchlog['batchlog_status'] = '1';
                    }
                    $batchlog['batchlog_createtime'] = time();
                    $this->DataControl->insertData("cmb_financial_batchlog", $batchlog);
                }
            }
        }
        return true;
    }

    function getHostDetail($request)
    {

        if (!isset($request['agencyId']) || $request['agencyId'] == '') {
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        if (!isset($request['start_time']) || $request['start_time'] == '' || !isset($request['end_time']) || $request['end_time'] == '') {
            $this->error = true;
            $this->errortip = "请选择时间";
            return false;
        }

        $Model = new \Model\Api\CmbTransModel($request['agencyId']);
        $sql = "select * 
            from cmb_financial_accountlog a 
            where a.agencyId='{$request['agencyId']}'
            and tranDate>='{$request['start_time']}'
            and tranDate<='{$request['end_time']}'";

        $list = $this->DataControl->selectClear($sql);
        if (!$list) {
            $this->error = true;
            $this->errortip = '无账户动账记录';
            return false;
        }

        $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname", "companies_agencyid='{$request['agencyId']}'");

        foreach ($list as &$listOne) {
            $listOne['companies_cnname'] = $companiesOne['companies_cnname'];
            $listOne['currency'] = '人民币';
            $listOne['tranAmt'] = $listOne['tranAmt'] / 100;
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $list;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['regAccNo'] = $dateexcelvar['regAccNo'];
                    $datearray['tranStatus'] = $dateexcelvar['tranStatus'];
                    $datearray['transferId'] = $dateexcelvar['transferId'];
                    $datearray['tranSerial'] = $dateexcelvar['tranSerial'];
                    $datearray['transSequence'] = $dateexcelvar['transSequence'];
                    $datearray['tranAmt'] = $dateexcelvar['tranAmt'] / 100;
                    $datearray['tag'] = $dateexcelvar['tag'];
                    $datearray['tranDir'] = $dateexcelvar['tranDir'];
                    $datearray['tranDate'] = $dateexcelvar['tranDate'];
                    $datearray['tranTime'] = $dateexcelvar['tranTime'];
                    $datearray['currency'] = $dateexcelvar['currency'];
                    $datearray['rcvPayAcc'] = $dateexcelvar['rcvPayAcc'];
                    $datearray['rcvPayName'] = $dateexcelvar['rcvPayName'];
                    $datearray['rcvPayEbk'] = $dateexcelvar['rcvPayEbk'];
                    $datearray['rcvPayEbb'] = $dateexcelvar['rcvPayEbb'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "监管户户口号", "交易状态", "申请划拨批次号", "交易流水", "财务流水号", "交易金额", "清算摘要", "借贷标志", "清算日期", "清算时间", "币种", "收付方帐号", "收付方名称", "收付方开户行", "收付方开户行联行号"));
            $excelfileds = array('companies_cnname', 'regAccNo', 'tranStatus', 'transferId', 'tranSerial', 'transSequence', 'tranAmt', 'tag', 'tranDir', 'tranDate', 'tranTime', 'currency', 'rcvPayAcc', 'rcvPayName', 'rcvPayEbk', 'rcvPayEbb');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('监管账户余额明细表.xlsx'));
            exit;
        } else {
            return $list;
        }

    }

    function getTransferInfo($request)
    {

        $datawhere = " tr.company_id='{$this->company_id}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and tr.income_date >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and tr.income_date <='{$request['end_time']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and tr.school_id ='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and tr.course_id ='{$request['course_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and tr.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and tr.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['income_isconfirm']) && $request['income_isconfirm'] !== '') {
            $datawhere .= " and tr.income_isconfirm ='{$request['income_isconfirm']}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and tr.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select tr.*,gcc.companies_cnname,sch.school_cnname,sch.school_branch,cl.class_cnname,cl.class_branch,st.student_cnname,st.student_branch,cho.hour_name,tr.confirm_type 
                from cmb_trans_transfer as tr
                left join smc_school as sch on tr.school_id=sch.school_id 
                left join smc_class as cl on cl.class_id=tr.class_id
                left join smc_student as st on st.student_id=tr.student_id
                left join smc_course as co on co.course_id=cl.course_id
                left join smc_student_hourstudy as sh on sh.hourstudy_id=tr.hourstudy_id
                left join smc_class_hour as cho on cho.hour_id=sh.hour_id
                left join gmc_code_companies as gcc on gcc.companies_id=tr.companies_id
                where {$datawhere} and tr.income_id>0";

        $type = $this->LgArraySwitch(array("0" => "自动确认", "1" => "主动确认"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['confirm_phone'] = $dateexcelvar['confirm_phone'];
                    $datearray['batch_pid'] = $dateexcelvar['batch_pid'];
                    $datearray['income_price'] = $dateexcelvar['income_price'];
                    $datearray['hour_name'] = $dateexcelvar['hour_name'];
                    $datearray['income_times'] = $dateexcelvar['income_times'];
                    $datearray['income_isconfirm'] = $dateexcelvar['income_isconfirm'] == 1 ? '已确认' : '未确认';
                    $datearray['confirm_type_name'] = $dateexcelvar['income_isconfirm'] == 1 ? $type[$dateexcelvar['confirm_type']] : '--';

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "校区名称", "校区编号", "班级名称", "班级编号", "学员名称", "学员编号", "订单编号", "手机号码", "用户销课流水号", "销课金额", "销课课时", "销课课时数", "销课状态", "确认类型"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'class_cnname', 'class_branch', 'student_cnname', 'student_branch', 'order_pid', 'confirm_phone', 'batch_pid', 'income_price', 'hour_name', 'income_times', 'income_isconfirm', 'confirm_type_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('销课状态学生确认明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $transList = $this->DataControl->selectClear($sql);
            if (!$transList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($transList as &$transOne) {
                $transOne['confirm_type_name'] = $transOne['income_isconfirm'] == 1 ? $type[$transOne['confirm_type']] : '--';
                $transOne['income_isconfirm'] = $transOne['income_isconfirm'] == 1 ? '已确认' : '未确认';
                $transOne['confirm_ip'] = $transOne['confirm_ip'] == '0.0.0.0' ? '' : $transOne['confirm_ip'];
                $transOne['confirm_createtime'] = $transOne['confirm_createtime'] > 0 ? date("Y-m-d H:i:s", $transOne['confirm_createtime']) : '';
            }


            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select tr.transfer_id
                        from cmb_trans_transfer as tr
                        left join smc_school as sch on tr.school_id=sch.school_id 
                        left join smc_class as cl on cl.class_id=tr.class_id
                        left join smc_student as st on st.student_id=tr.student_id
                        left join smc_course as co on co.course_id=cl.course_id
                        left join smc_student_hourstudy as sh on sh.hourstudy_id=tr.hourstudy_id
                        left join smc_class_hour as cho on cho.hour_id=sh.hour_id
                        where {$datawhere} and tr.income_id>0";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $transList;
            return $data;
        }
    }

    function getOrderInfo($request)
    {
        $datawhere = " B.company_id='{$this->company_id}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (D.student_cnname like '%{$request['keyword']}%' or D.student_branch like '%{$request['keyword']}%' or A.order_pid like '%{$request['keyword']}%')";
        }

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and A.order_date >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and A.order_date <='{$request['end_time']}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and F.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and A.school_id ='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and E.course_id ='{$request['course_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and E.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and E.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['is_confirm']) && $request['is_confirm'] !== '') {
            $datawhere .= " and A.is_confirm ='{$request['is_confirm']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.order_id,B.companies_cnname,C.school_branch,C.school_cnname,D.student_cnname,D.student_branch,E.course_cnname,E.course_branch,F.order_pid,A.order_amt,A.order_num,A.is_confirm,A.order_type,F.trading_pid
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=F.order_pid and y.paytype_ischarge=1 and x.pay_issuccess=1),0) as order_payamt
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=F.order_pid and y.paytype_ischarge=0 and x.pay_issuccess=1 and y.paytype_code<>'feewaiver' and y.paytype_code<>'canceldebts'),0) as order_disamt    
                ,ifnull((select x.ordercourse_totalprice from smc_payfee_order_course as x where x.order_pid=F.order_pid and x.course_id=A.course_id limit 0,1),0) as ordercourse_totalprice,A.is_confirm
                ,ifnull((select x.ordercourse_totalprice from smc_payfee_order_course as x where x.order_pid=F.order_pid and x.course_id=A.course_id),0) as ordercourse_totalprice
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y,smc_payfee_order as z where x.paytype_code=y.paytype_code and x.order_pid=z.order_pid and z.student_id=A.student_id and z.companies_id=A.companies_id and y.paytype_ischarge=1 and x.pay_issuccess=1 and x.pay_successtime>=B.companies_supervisetime),0) as all_amt
                ,(CASE WHEN A.is_confirm=0 THEN '未确认' WHEN A.is_confirm=-1 THEN '无需确认' WHEN A.is_confirm is null THEN '无需确认' else '已确认' end ) AS is_confirm_name
                from cmb_trans_order_change as F
                left join cmb_trans_order as A on A.order_pid=F.order_pid
                left join gmc_code_companies as B on F.companies_id=B.companies_id
                left join smc_school as C on F.school_id=C.school_id
                left join smc_student as D on F.student_id=D.student_id
                left join smc_course as E on F.course_id=E.course_id
                where {$datawhere} 
                having (order_id is null) or (order_type='P' and all_amt>0) or (order_type='R')
                order by A.order_date desc
                ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['order_pid'] = $dateexcelvar['order_pid'];
                    $datearray['ordercourse_totalprice'] = $dateexcelvar['ordercourse_totalprice'];
                    $datearray['order_amt'] = $dateexcelvar['order_amt'];
                    $datearray['all_amt'] = $dateexcelvar['all_amt'];
                    $datearray['order_payamt'] = $dateexcelvar['order_payamt'];
                    $datearray['order_disamt'] = $dateexcelvar['order_disamt'];
                    $datearray['sharePrice'] = ceil($dateexcelvar['ordercourse_totalprice'] / $dateexcelvar['order_num']);
                    $datearray['order_num'] = $dateexcelvar['order_num'];
                    $datearray['is_confirm_name'] = $dateexcelvar['is_confirm_name'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "学校名称", "校区编号", "学员姓名", "学员编号", "课程名称", "课程编号", "订单编号", "订单总额(元)", "确认总额(元)", "同主体累计金额(元)", "银行卡支付(元)", "余额支付(元)", "每课次平摊金额", "购买课次", "是否确认"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'course_cnname', 'course_branch', 'order_pid', 'ordercourse_totalprice', 'order_amt', 'all_amt', 'order_payamt', 'order_disamt', 'sharePrice', 'order_num', 'is_confirm_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('订单确认明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $orderList = $this->DataControl->selectClear($sql);
            if (!$orderList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            foreach ($orderList as &$orderOne) {
                if ($orderOne['order_num'] > 0) {
                    $orderOne['sharePrice'] = ceil($orderOne['ordercourse_totalprice'] / $orderOne['order_num']);
                } else {
                    $orderOne['sharePrice'] = 0;
                }
            }


            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select A.order_id,A.order_type
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=F.order_pid and y.paytype_ischarge=1 and x.pay_issuccess=1),0) as order_payamt
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=F.order_pid and y.paytype_ischarge=0 and x.pay_issuccess=1 and y.paytype_code<>'feewaiver' and y.paytype_code<>'canceldebts'),0) as order_disamt
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y,smc_payfee_order as z where x.paytype_code=y.paytype_code and x.order_pid=z.order_pid and z.student_id=A.student_id and z.companies_id=A.companies_id and y.paytype_ischarge=1 and x.pay_issuccess=1 and x.pay_successtime>=B.companies_supervisetime),0) as all_amt
                from cmb_trans_order_change as F
                left join cmb_trans_order as A on A.order_pid=F.order_pid
                left join gmc_code_companies as B on F.companies_id=B.companies_id
                left join smc_school as C on F.school_id=C.school_id
                left join smc_student as D on F.student_id=D.student_id
                left join smc_course as E on F.course_id=E.course_id
                where {$datawhere}
                having (order_id is null) or (order_type='P' and all_amt>0) or (order_type='R')
                ";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $orderList;
            return $data;
        }
    }

    function getTransBalanceStatistics($request)
    {
        $datawhere = " a.company_id='{$this->company_id}' ";


        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and a.companies_id ='{$request['companies_id']}'";
        } else {
            $this->error = true;
            $this->errortip = "必须传入主体";
            return false;
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id ='{$request['school_id']}'";
        }

        $having = " 1 ";

        if (isset($request['is_equal']) && $request['is_equal'] !== '') {
            if ($request['is_equal'] == 0) {
                $having .= " (coursebalance_figure+hastransprice+willtransprice)<>payprice";

            } elseif ($request['is_equal'] == 1) {
                $having .= " (coursebalance_figure+hastransprice+willtransprice)=payprice";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT d.school_cnname,d.school_branch,e.companies_cnname,
                    c.student_branch,
                    c.student_cnname,
                    sum( a.coursebalance_figure ) AS coursebalance_figure,
                    ifnull((
                        SELECT
                            sum(
                                zz.income_price - ifnull((
                                    SELECT
                                        x.reduce_price 
                                    FROM
                                        cmb_trans_transfer_reduce AS x 
                                    WHERE
                                        x.order_pid = zz.order_pid 
                                        AND x.course_id = zz.course_id 
                                        AND x.hour_lessontimes = xx.hour_lessontimes 
                                        ),
                                    0 
                                ) 
                            ) 
                        FROM
                            cmb_trans_transfer AS zz,
                            smc_student_hourstudy AS yy,
                            smc_class_hour AS xx 
                        WHERE
                            zz.hourstudy_id = yy.hourstudy_id 
                            AND yy.hour_id = xx.hour_id 
                            AND zz.companies_id = a.companies_id 
                            AND zz.student_id = a.student_id and zz.transfer_status=2
                            ),
                        0 
                    ) AS hastransprice,
                    ifnull((
                        SELECT
                            sum(
                                zz.income_price - ifnull((
                                    SELECT
                                        x.reduce_price 
                                    FROM
                                        cmb_trans_transfer_reduce AS x 
                                    WHERE
                                        x.order_pid = zz.order_pid 
                                        AND x.course_id = zz.course_id 
                                        AND x.hour_lessontimes = xx.hour_lessontimes 
                                        ),
                                    0 
                                ) 
                            ) 
                        FROM
                            cmb_trans_transfer AS zz,
                            smc_student_hourstudy AS yy,
                            smc_class_hour AS xx 
                        WHERE
                            zz.hourstudy_id = yy.hourstudy_id 
                            AND yy.hour_id = xx.hour_id 
                            AND zz.companies_id = a.companies_id 
                            AND zz.student_id = a.student_id and zz.transfer_status in (0,1)
                            ),
                        0 
                    ) AS willtransprice,
                    ifnull((
                        SELECT
                            (
                            SELECT
                                sum( v.pay_price ) 
                            FROM
                                smc_payfee_order_pay AS v,
                                smc_code_paytype AS o 
                            WHERE
                                v.paytype_code = o.paytype_code 
                                AND v.order_pid = x.order_pid 
                                AND o.paytype_ischarge = 1 
                                AND v.pay_issuccess = 1 
                            ) 
                        FROM
                            smc_payfee_order AS x,
                            smc_payfee_order_course AS y
                        WHERE
                            x.order_pid = y.order_pid
                            AND x.student_id = a.student_id 
                            AND x.order_status = 4 
                            AND e.companies_supervisetime <= x.order_createtime 
                            AND x.companies_id = a.companies_id 
                        ORDER BY
                            x.order_createtime DESC,
                            x.order_id DESC 
                            LIMIT 0,
                            1 
                            ),
                        0 
                    ) AS payprice
                    ,ifnull((select sum(x.refund_payprice) from smc_refund_order as x where x.school_id=a.school_id and x.student_id=a.student_id and x.companies_id=a.companies_id and x.refund_createtime>=e.companies_supervisetime and x.refund_status=4),0) as refundprice
                FROM
                    smc_student_coursebalance AS a,
                    smc_course AS b,
                    smc_student AS c,smc_school as d,gmc_code_companies as e 
                WHERE {$datawhere}
                    AND a.course_id = b.course_id 
                    AND a.student_id = c.student_id and a.school_id=d.school_id and a.companies_id=e.companies_id
                    AND a.coursebalance_issupervise = 1 and e.companies_issupervise=1
                GROUP BY
                    a.student_id,
                    a.companies_id
                    -- HAVING (coursebalance_figure+transprice<>payprice)
                having {$having}    
                ";

        $sql .= ' limit ' . $pagestart . ',' . $num;

        $balanceList = $this->DataControl->selectClear($sql);

        $count_sql = "SELECT
                    sum( a.coursebalance_figure ) AS coursebalance_figure,
                    ifnull((
                        SELECT
                            sum(
                                zz.income_price - ifnull((
                                    SELECT
                                        x.reduce_price 
                                    FROM
                                        cmb_trans_transfer_reduce AS x 
                                    WHERE
                                        x.order_pid = zz.order_pid 
                                        AND x.course_id = zz.course_id 
                                        AND x.hour_lessontimes = xx.hour_lessontimes 
                                        ),
                                    0 
                                ) 
                            ) 
                        FROM
                            cmb_trans_transfer AS zz,
                            smc_student_hourstudy AS yy,
                            smc_class_hour AS xx 
                        WHERE
                            zz.hourstudy_id = yy.hourstudy_id 
                            AND yy.hour_id = xx.hour_id 
                            AND zz.companies_id = a.companies_id 
                            AND zz.student_id = a.student_id and zz.transfer_status=2
                            ),
                        0 
                    ) AS hastransprice,
                    ifnull((
                        SELECT
                            sum(
                                zz.income_price - ifnull((
                                    SELECT
                                        x.reduce_price 
                                    FROM
                                        cmb_trans_transfer_reduce AS x 
                                    WHERE
                                        x.order_pid = zz.order_pid 
                                        AND x.course_id = zz.course_id 
                                        AND x.hour_lessontimes = xx.hour_lessontimes 
                                        ),
                                    0 
                                ) 
                            ) 
                        FROM
                            cmb_trans_transfer AS zz,
                            smc_student_hourstudy AS yy,
                            smc_class_hour AS xx 
                        WHERE
                            zz.hourstudy_id = yy.hourstudy_id 
                            AND yy.hour_id = xx.hour_id 
                            AND zz.companies_id = a.companies_id 
                            AND zz.student_id = a.student_id and zz.transfer_status in (0,1)
                            ),
                        0 
                    ) AS willtransprice,
                    ifnull((
                        SELECT
                            (
                            SELECT
                                sum( v.pay_price ) 
                            FROM
                                smc_payfee_order_pay AS v,
                                smc_code_paytype AS o 
                            WHERE
                                v.paytype_code = o.paytype_code 
                                AND v.order_pid = x.order_pid 
                                AND o.paytype_ischarge = 1 
                                AND v.pay_issuccess = 1 
                            ) 
                        FROM
                            smc_payfee_order AS x,
                            smc_payfee_order_course AS y
                        WHERE
                            x.order_pid = y.order_pid
                            AND x.student_id = a.student_id 
                            AND x.order_status = 4 
                            AND e.companies_supervisetime <= x.order_createtime 
                            AND x.companies_id = a.companies_id 
                        ORDER BY
                            x.order_createtime DESC,
                            x.order_id DESC 
                            LIMIT 0,
                            1 
                            ),
                        0 
                    ) AS payprice
                FROM
                    smc_student_coursebalance AS a,
                    smc_course AS b,
                    smc_student AS c,smc_school as d,gmc_code_companies as e 
                WHERE {$datawhere}
                    AND a.course_id = b.course_id 
                    AND a.student_id = c.student_id and a.school_id=d.school_id and a.companies_id=e.companies_id
                    AND a.coursebalance_issupervise = 1 and e.companies_issupervise=1
                GROUP BY
                    a.student_id,
                    a.companies_id
                    -- HAVING (coursebalance_figure+transprice<>payprice)
                having {$having}    
                ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $balanceList;
        return $data;
    }

    function confirmOrder($request)
    {
        $orderOne = $this->DataControl->getFieldOne("cmb_trans_order", "is_confirm,order_pid,course_id,order_amt,order_type", "order_id='{$request['order_id']}'");

        if ($orderOne['is_confirm'] == 1) {
            $this->error = true;
            $this->errortip = "订单已经确认,不可重复确认";
            return false;
        }

        if ($orderOne['order_type'] == 'R') {
            $data = array();
            $data['is_confirm'] = 1;
            $this->DataControl->updateData("cmb_trans_order", "order_id='{$request['order_id']}'", $data);

            $data = array();
            $data['is_confirm'] = 1;
            $data['transfer_updatetime'] = time();
            $this->DataControl->updateData("cmb_trans_transfer", "order_pid='{$orderOne['order_pid']}' and course_id='{$orderOne['course_id']}'", $data);
        } else {
            $courseOne = $this->DataControl->getFieldOne("smc_payfee_order_course", "ordercourse_totalprice", "order_pid='{$orderOne['order_pid']}' and course_id='{$orderOne['course_id']}'");
            if (!$courseOne) {
                $this->error = true;
                $this->errortip = "课程不存在";
                return false;
            }


            $sql = "select ifnull(sum(reduce_price),0) as reduce_price from cmb_trans_transfer_reduce where order_pid='{$orderOne['order_pid']}' and course_id='{$orderOne['course_id']}'
        ";

            $reduceInfo = $this->DataControl->selectOne($sql);


            $data = array();
            $data['is_confirm'] = 1;
            $data['order_amt'] = $reduceInfo ? ($courseOne['ordercourse_totalprice'] - $reduceInfo['reduce_price']) : $courseOne['ordercourse_totalprice'];
            $this->DataControl->updateData("cmb_trans_order", "order_id='{$request['order_id']}'", $data);

            $data = array();
            $data['is_confirm'] = 1;
            $data['transfer_updatetime'] = time();
            $this->DataControl->updateData("cmb_trans_transfer", "order_pid='{$orderOne['order_pid']}' and course_id='{$orderOne['course_id']}'", $data);
        }


        return true;
    }

    function unConfirmOrder($request)
    {
        $orderOne = $this->DataControl->getFieldOne("cmb_trans_order", "is_confirm,order_pid,course_id,order_amt", "order_id='{$request['order_id']}'");

        if ($orderOne['is_confirm'] != 0) {
            $this->error = true;
            $this->errortip = "该订单不可无需确认";
            return false;
        }

        $data = array();
        $data['is_confirm'] = -1;
        $this->DataControl->updateData("cmb_trans_order", "order_id='{$request['order_id']}'", $data);

        return true;
    }

    function getOrderOneInfo($request)
    {
        $orderOne = $this->DataControl->getFieldOne("cmb_trans_order", "order_pid,student_id,course_id,school_id", "order_id='{$request['order_id']}'");

        if (!$orderOne) {
            $this->error = true;
            $this->errortip = "无该订单";
            return false;
        }

        $sql = "select c.hour_lessontimes,b.class_id 
            from smc_student_study as a
            left join smc_class as b on a.class_id=b.class_id
            left join smc_class_hour as c on c.class_id=a.class_id
            where a.student_id='{$orderOne['student_id']}' and b.course_id='{$orderOne['course_id']}' and a.school_id='{$orderOne['school_id']}' and c.hour_day>=a.study_beginday
            order by c.hour_day asc,c.hour_lessontimes asc
            ";

        $hourOne = $this->DataControl->selectOne($sql);

        if (!$hourOne) {
            $this->error = true;
            $this->errortip = "该学生不存在已读课程";
            return false;
        }

        $hourList = $this->DataControl->getList("cmb_trans_transfer_reduce", "order_pid='{$orderOne['order_pid']}' and course_id='{$orderOne['course_id']}'", "order by hour_lessontimes asc");

        $data = array();
        $data['hourOne'] = $hourOne;
        $data['list'] = $hourList ? $hourList : array();

        return $data;

    }

    function recordReducePrice($request)
    {
        $orderOne = $this->DataControl->getFieldOne("cmb_trans_order", "student_id,course_id,school_id,order_pid", "order_id='{$request['order_id']}'");

        if (!$orderOne) {
            $this->error = true;
            $this->errortip = "无该订单";
            return false;
        }

        $list = json_decode(stripslashes($request['list']), true);
        if (!$list) {
            $this->error = true;
            $this->errortip = "请填写金额";
            return false;
        }

        $cancel_pid = $this->create_guid();
        $cancel_data = array();
        $cancel_data['order_pid'] = $cancel_pid;
        $cancel_data['reduce_updatetime'] = time();

        $this->DataControl->updateData("cmb_trans_transfer_reduce", "order_pid='{$request['order_pid']}' and course_id='{$orderOne['course_id']}'", $cancel_data);

        foreach ($list as $listOne) {
            $data = array();
            $data['student_id'] = $orderOne['student_id'];
            $data['order_pid'] = $orderOne['order_pid'];
            $data['class_id'] = $request['class_id'];
            $data['course_id'] = $orderOne['course_id'];
            $data['hour_lessontimes'] = $listOne['hour_lessontimes'];
            $data['reduce_price'] = $listOne['reduce_price'];
            $data['reduce_createtime'] = time();

            $this->DataControl->insertData("cmb_trans_transfer_reduce", $data);
        }
        return true;
    }

    function getStuCourseBalance($request)
    {
        $datawhere = "scb.company_id='{$this->company_id}' and sc.course_issupervise=1";
        $having = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%' )";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and scb.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and scb.school_id ='{$request['school_id']}'";
        }

        if (isset($request['course_id']) && $request['course_id'] !== '') {
            $datawhere .= " and sc.course_id ='{$request['course_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and sc.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and sc.coursecat_id ='{$request['coursecat_id']}'";
        }

        if (isset($request['booking_status']) && $request['booking_status'] !== '') {
            $having .= " and booking_status ='{$request['booking_status']}'";
        }

        if (isset($request['coursebalance_issupervise']) && $request['coursebalance_issupervise'] !== '') {
            $datawhere .= " and scb.coursebalance_issupervise ='{$request['coursebalance_issupervise']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select scb.coursebalance_id,cs.companies_cnname,sch.school_cnname,sch.school_branch,st.student_cnname,st.student_branch,sc.course_cnname,sc.course_branch,scb.coursebalance_figure,scb.coursebalance_time,scb.coursebalance_issupervise,co.coursecat_branch 
                ,ifnull((select cl.class_cnname from smc_student_study as ss,smc_class as cl where ss.class_id=cl.class_id and ss.school_id=scb.school_id and cl.course_id=scb.course_id and ss.student_id=scb.student_id ORDER BY ss.study_endday DESC limit 0,1),'') as class_cnname
                ,ifnull((select cl.class_branch from smc_student_study as ss,smc_class as cl where ss.class_id=cl.class_id and ss.school_id=scb.school_id and cl.course_id=scb.course_id and ss.student_id=scb.student_id ORDER BY ss.study_endday DESC limit 0,1),'') as class_branch      
                ,ifnull((select 1 from smc_student_family as x,smc_parenter_wxchattoken as y where x.student_id=scb.student_id and x.parenter_id=y.parenter_id and y.wxchatnumber_id=6 and y.booking_status=1 limit 0,1),0) as booking_status
                from smc_student_coursebalance as scb
                inner join gmc_code_companies as cs on cs.companies_id=scb.companies_id and cs.companies_issupervise=1
                left join smc_school as sch on sch.school_id=scb.school_id
                left join smc_student as st on st.student_id=scb.student_id
                left join smc_course as sc on sc.course_id=scb.course_id
                left join smc_code_coursecat as co on co.coursecat_id=sc.coursecat_id
                where {$datawhere} 
                having {$having}
                ";//and sc.course_issupervise=1

        $status = $this->LgArraySwitch(array('0' => '未监管', '1' => '监管中'));
        $b_status = $this->LgArraySwitch(array('0' => '否', '1' => '是'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['coursecat_branch'] = $dateexcelvar['coursecat_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['coursebalance_figure'] = $dateexcelvar['coursebalance_figure'];
                    $datearray['coursebalance_time'] = $dateexcelvar['coursebalance_time'];
                    $datearray['coursebalance_issupervise_name'] = $status[$dateexcelvar['coursebalance_issupervise']];
                    $datearray['booking_status_name'] = $b_status[$dateexcelvar['booking_status']];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "校区名称", "校区编号", "学员名称", "学员编号", "课程名称", "课程编号", "班种编号", "班级名称", "班级编号", "课程余额", "剩余课次", "监管状态", "是否绑定小程序"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'student_cnname', 'student_branch', 'course_cnname', 'course_branch', 'coursecat_branch', 'class_cnname', 'class_branch', 'coursebalance_figure', 'coursebalance_time', 'coursebalance_issupervise_name', 'booking_status_name');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('学员课程监管明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;

            $transList = $this->DataControl->selectClear($sql);
            if (!$transList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }


            foreach ($transList as &$transOne) {
                $transOne['coursebalance_issupervise_name'] = $status[$transOne['coursebalance_issupervise']];
                $transOne['booking_status_name'] = $b_status[$transOne['booking_status']];
            }


            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select scb.coursebalance_id,ifnull((select 1 from smc_student_family as x,smc_parenter_wxchattoken as y where x.student_id=scb.student_id and x.parenter_id=y.parenter_id and y.wxchatnumber_id=6 and y.booking_status=1 limit 0,1),0) as booking_status
                from smc_student_coursebalance as scb
                inner join gmc_code_companies as cs on cs.companies_id=scb.companies_id and cs.companies_issupervise=1
                left join smc_school as sch on sch.school_id=scb.school_id
                left join smc_student as st on st.student_id=scb.student_id
                left join smc_course as sc on sc.course_id=scb.course_id
                where {$datawhere} 
                having {$having}";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $transList;
            return $data;
        }
    }

    function changeSuperviseStatus($request)
    {
        $stuCourseBalanceOne = $this->DataControl->getFieldOne("smc_student_coursebalance", "coursebalance_issupervise", "coursebalance_id='{$request['coursebalance_id']}'");

        if (!$stuCourseBalanceOne) {
            $this->error = true;
            $this->errortip = "无监管项目";
            return false;
        }

        $data = array();
        $data['coursebalance_issupervise'] = $stuCourseBalanceOne['coursebalance_issupervise'] == 0 ? 1 : 0;
        if ($this->DataControl->updateData("smc_student_coursebalance", "coursebalance_id='{$request['coursebalance_id']}'", $data)) {

            return true;
        } else {
            $this->error = true;
            $this->errortip = "监管失败";
            return false;
        }

    }

    function getAccountBalance($request)
    {
        $datawhere = " 1 ";

        if (isset($request['agencyId']) && $request['agencyId'] != '') {
            $datawhere .= " and A.companies_agencyid='{$request['agencyId']}' ";
        }

        $sql = "select A.companies_agencyid,A.companies_cnname 
                from gmc_code_companies as A
                where {$datawhere} and A.company_id='{$this->company_id}' and A.companies_agencyid<>'' and A.companies_issupervise=1
                ";

        $companiesList = $this->DataControl->selectClear($sql);

        if (!$companiesList) {
            $this->error = true;
            $this->errortip = "监管户不存在";
            return false;
        }

        $tem_array = array();

        foreach ($companiesList as &$companiesOne) {

            $Model = new \Model\Api\CmbTransModel($companiesOne['companies_agencyid']);

            $listOne = $Model->queryAccountBalance();

            if ($listOne) {
                $data = array();
                $data['companies_agencyid'] = $companiesOne['companies_agencyid'];
                $data['companies_cnname'] = $companiesOne['companies_cnname'];
                $data['regAccNo'] = $listOne['regAccNo'];
                $data['frozenAmt'] = $listOne['frozenAmt'] / 100;
                $data['transferredAmt'] = $listOne['transferredAmt'] / 100;
                $data['lastEndBalance'] = $listOne['lastEndBalance'] / 100;

                $logOne = $this->DataControl->getFieldOne("cmb_trans_log", "batch_pid,log_time", "agency_id='{$companiesOne['companies_agencyid']}' and log_url='/pfs/head/addBatchTransfer' order by log_id desc");

                if ($logOne) {
                    $tem_data = array();
                    $tem_data['agencyId'] = $companiesOne['companies_agencyid'];
                    $tem_data['transferId'] = $logOne['batch_pid'];
                    $returnData = $Model->transfer($tem_data, '/pfs/head/queryTransferBill');

                    if (!$returnData) {
                        $data['tip'] = $Model->errortip;
                    } else {
                        $data['tip'] = '已审核';
                    }
                    $data['time'] = date("Y-m-d H:i:s", $logOne['log_time']);
                } else {
                    $data['tip'] = '未开始划拨';
                    $data['time'] = '--';
                }

                $tem_array[] = $data;
            }
        }

        if (!$tem_array) {
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $tem_array;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['regAccNo'] = $dateexcelvar['regAccNo'];
                    $datearray['frozenAmt'] = $dateexcelvar['frozenAmt'];
                    $datearray['transferredAmt'] = $dateexcelvar['transferredAmt'];
                    $datearray['lastEndBalance'] = $dateexcelvar['lastEndBalance'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "监管户户口号", "账户实时余额", "已划拨金额", "日终余额"));
            $excelfileds = array('companies_cnname', 'regAccNo', 'frozenAmt', 'transferredAmt', 'lastEndBalance');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('监管账户余额明细表.xlsx'));
            exit;
        } else {
            return $tem_array;
        }

    }

    function getTransBathLog($request)
    {
        $datawhere = " a.companies_id=b.companies_id ";

        if (isset($request['agencyId']) && $request['agencyId'] != '') {
            $datawhere .= " and a.agency_id='{$request['agencyId']}' ";
        }

        if (isset($request['start_time']) && $request['start_time'] != '') {
            $datawhere .= " and FROM_UNIXTIME(a.log_time,'%Y-%m-%d' ) >= '{$request['start_time']}' ";
        } else {
            $datawhere .= " and FROM_UNIXTIME(a.log_time,'%Y-%m-%d' ) >= CURDATE()";
        }

        if (isset($request['end_time']) && $request['end_time'] != '') {
            $datawhere .= " and FROM_UNIXTIME(a.log_time,'%Y-%m-%d' ) <= '{$request['end_time']}' ";
        } else {
            $datawhere .= " and FROM_UNIXTIME(a.log_time,'%Y-%m-%d' ) <= CURDATE()";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select a.log_requet_json,a.log_back_json,a.batch_pid,b.companies_cnname,a.companies_id,b.companies_agencyid,FROM_UNIXTIME(a.log_time,'%Y-%m-%d %H:%i:%s' ) as log_time
                from cmb_trans_log as a,gmc_code_companies as b 
                where {$datawhere} and a.log_url='/pfs/head/addBatchTransfer'
                order by a.companies_id asc,a.log_time desc
                ";

        $db_nums = $this->DataControl->selectClear($sql);
        $allnum = $db_nums ? count($db_nums) : 0;

        if (!isset($request['is_export']) || $request['is_export'] == 0) {
            $sql .= ' limit ' . $pagestart . ',' . $num;
        }


        $logList = $this->DataControl->selectClear($sql);

        if (!$logList) {
            $this->error = true;
            $this->errortip = "该日期未产生划拨";
            return false;
        }

        foreach ($logList as &$logOne) {

            $requestList = json_decode($logOne['log_requet_json'], 1);
            $backList = json_decode($logOne['log_back_json'], 1);

            $allPrice = 0;
            $allClassHour = 0;
            $lastPrice = 0;
            $lastClassHour = 0;

            $failClassArray = array();

            if ($backList && $backList['failList']) {
                foreach ($backList['failList'] as $failOne) {
                    $failClassArray[] = $failOne['classId'];
                }
            }

            if ($requestList) {
                $logOne['date']=$requestList['date'];
                $allPrice += $requestList['sumTransferAmt'];
                $lastPrice += $requestList['sumTransferAmt'];

                if ($requestList['transferList']) {
                    foreach ($requestList['transferList'] as $transferOne) {
                        if ($failClassArray && in_array($transferOne['classId'], $failClassArray)) {
                            $lastPrice -= $transferOne['transferAmt'];
                        } else {
                            $lastClassHour += $transferOne['eliminateClassHour'];
                        }
                        $allClassHour += $transferOne['eliminateClassHour'];
                    }
                }
            }

//            debug($logOne);exit;

            $logOne['allClassHour'] = $allClassHour;//申请划拨课时数
            $logOne['allPrice'] = $allPrice / 100;//申请划拨金额
            $logOne['lastClassHour'] = $lastClassHour;//申请银行划拨课时数
            $logOne['lastPrice'] = $lastPrice / 100;//申请银行划拨金额

            $Model = new \Model\Api\CmbTransModel($logOne['companies_agencyid']);

            $tem_data = array();
            $tem_data['agencyId'] = $logOne['companies_agencyid'];
            $tem_data['transferId'] = $logOne['batch_pid'];
            $returnData = $Model->transfer($tem_data, '/pfs/head/queryTransferBill');
            if ($returnData) {
                $logOne['sumTransferAmt'] = $returnData['sumTransferAmt'] / 100;//银行划拨金额
                $logOne['transferDate'] = $returnData['transferDate'];
                $logOne['state'] = '已划拨';
            } else {
                $logOne['sumTransferAmt'] = '--';
                $logOne['transferDate'] = '--';
                $logOne['state'] = '划拨中';
            }

            unset($logOne['log_requet_json']);
            unset($logOne['log_back_json']);

        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $logList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['allPrice'] = $dateexcelvar['allPrice'];
                    $datearray['allClassHour'] = $dateexcelvar['allClassHour'];
                    $datearray['log_time'] = $dateexcelvar['log_time'];
                    $datearray['lastPrice'] = $dateexcelvar['lastPrice'];
                    $datearray['lastClassHour'] = $dateexcelvar['lastClassHour'];
                    $datearray['sumTransferAmt'] = $dateexcelvar['sumTransferAmt'];
                    $datearray['transferDate'] = $dateexcelvar['transferDate'];
                    $datearray['state'] = $dateexcelvar['state'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "申请划拨金额", "申请划拨课时数", "申请划拨时间", "申请银行划拨金额", "申请银行划拨课时数", "银行划拨金额", "划拨日期", "划拨状态"));
            $excelfileds = array('companies_cnname', 'allPrice', 'allClassHour', 'log_time', 'lastPrice', 'lastClassHour', 'sumTransferAmt', 'transferDate', 'state');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('划拨记录明细.xlsx'));
            exit;
        } else {
            $data = array();
            $data['list'] = $logList;
            $data['allnum'] = $allnum;
            return $data;
        }

    }

    function getClassBalance($request)
    {
        if (!isset($request['agencyId']) || $request['agencyId'] == '') {
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        //班级编号组成的json字符串
        if (!isset($request['class_list']) || $request['class_list'] == '' || $request['class_list'] == '[]') {
            $this->error = 1;
            $this->errortip = "请选择需要查询的班级";
            return false;
        }

        $Model = new \Model\Api\CmbTransModel($request['agencyId']);

        $listOne = $Model->queryClassBalance($request['class_list']);

        if (!$listOne) {
            $this->error = 1;
            $this->errortip = $Model->errortip;
            return false;
        }

        $classList = $listOne['classList'];

        $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname", "companies_agencyid='{$request['agencyId']}'");

        foreach ($classList as &$classOne) {

            $sql = "select sch.school_cnname,sch.school_branch,cl.class_cnname,cl.class_branch
                    from smc_class as cl,smc_school as sch  
                    where cl.school_id=sch.school_id and cl.class_branch='{$classOne['classId']}'";

            $one = $this->DataControl->selectOne($sql);
            $classOne['transferredAmt'] = $classOne['transferredAmt'] / 100;
            $classOne['surplusAmt'] = $classOne['surplusAmt'] / 100;
            $classOne['totalAmt'] = $classOne['totalAmt'] / 100;
            $classOne['companies_cnname'] = $companiesOne['companies_cnname'];
            $classOne['school_cnname'] = $one['school_cnname'];
            $classOne['school_branch'] = $one['school_branch'];
            $classOne['class_cnname'] = $one['class_cnname'];
            $classOne['class_branch'] = $one['class_branch'];

        }

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $classList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_cnname'] = $dateexcelvar['class_cnname'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['totalAmt'] = $dateexcelvar['totalAmt'];
                    $datearray['transferredAmt'] = $dateexcelvar['transferredAmt'];
                    $datearray['transferredTimes'] = $dateexcelvar['transferredTimes'];
                    $datearray['surplusAmt'] = $dateexcelvar['surplusAmt'];
                    $datearray['totalEliminateHour'] = $dateexcelvar['totalEliminateHour'];

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "监管户户口号", "账户实时余额", "已划拨金额", "日终余额"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'class_cnname', 'class_branch', 'totalAmt', 'transferredAmt', 'transferredTimes', 'surplusAmt', 'totalEliminateHour');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('班级拨付余额明细表.xlsx'));
            exit;
        } else {
            return $classList;
        }
    }

    function getCmbClass($request)
    {
        $sql = "select b.class_id,b.class_cnname,b.class_branch 
                from cmb_trans_class as A,smc_class as B 
                where A.class_id=B.class_id and B.company_id='{$this->company_id}'";


        $classList = $this->DataControl->selectClear($sql);
        if (!$classList) {
            $this->error = 1;
            $this->errortip = "无可查询班级";
            return false;
        }

        return $classList;

    }

    function getTransError($request)
    {
        $datawhere = " 1 ";

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $start_time = strtotime($request['start_time']);
            $datawhere .= " and A.faillog_time >='{$start_time}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $end_time = strtotime($request['end_time']) + 86399;
            $datawhere .= " and A.faillog_time <='{$end_time}'";
        }

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and A.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and d.school_id ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select e.companies_branch,e.companies_cnname,
            d.school_branch,d.school_cnname,
            c.class_branch,c.class_enname,a.hourstudy_id,
            a.faillog_url,a.batch_pid,
            a.failCode,a.failMessage,
            FROM_UNIXTIME(a.faillog_time,'%Y-%m-%d') as faillog_time,
            (case a.faillog_status when 1 then '已处理' else '未处理' end) as faillog_status
            from cmb_trans_faillog a
            left join smc_student_hourstudy b on a.hourstudy_id=b.hourstudy_id
            left join smc_class c on a.class_branch=c.class_branch or c.class_id=b.class_id
            left join smc_school d on c.school_id=d.school_id
            left join gmc_code_companies e on a.companies_id=e.companies_id
            where {$datawhere}
            and a.faillog_status=0
            order by A.faillog_id asc";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['hourstudy_id'] = $dateexcelvar['hourstudy_id'];
                    $datearray['faillog_url'] = $dateexcelvar['faillog_url'];
                    $datearray['batch_pid'] = $dateexcelvar['batch_pid'];
                    $datearray['failCode'] = $dateexcelvar['failCode'];
                    $datearray['failMessage'] = $dateexcelvar['failMessage'];
                    $datearray['faillog_time'] = $dateexcelvar['faillog_time'];
                    $datearray['faillog_status'] = $dateexcelvar['faillog_status'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "学校名称", "学校编号", "班级编号", "班级别名", "考勤序号", "接口地址", "批次号", "错误代码", "错误信息", "报错时间", "是否处理"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'class_branch', 'class_enname', 'hourstudy_id', 'faillog_url', 'batch_pid', 'failCode', 'failMessage', 'faillog_time', 'faillog_status');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('错误日志明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $orderList = $this->DataControl->selectClear($sql);
            if (!$orderList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.faillog_id
                    from cmb_trans_faillog a
                    left join smc_student_hourstudy b on a.hourstudy_id=b.hourstudy_id
                    left join smc_class c on a.class_branch=c.class_branch or c.class_id=b.class_id
                    left join smc_school d on c.school_id=d.school_id
                    left join gmc_code_companies e on a.companies_id=e.companies_id
                    where {$datawhere} 
                    and a.faillog_status=0";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $orderList;
            return $data;
        }
    }

    function getUnTransfer($request)
    {
        $datawhere = "  ";

        if (isset($request['start_time']) && $request['start_time'] !== '') {
            $datawhere .= " and t.income_date >='{$request['start_time']}'";
        }

        if (isset($request['end_time']) && $request['end_time'] !== '') {
            $datawhere .= " and t.income_date <='{$request['end_time']}'";
        }

        if (isset($request['agencyId']) && $request['agencyId'] !== '') {
            $datawhere .= " and t.agency_id ='{$request['agencyId']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and c.school_id ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT (select companies_cnname from gmc_code_companies where companies_id=t.companies_id) as companies_cnname,
             (SELECT s.school_branch FROM smc_school AS s WHERE s.school_id = t.school_id) AS school_branch,
             (SELECT s.school_cnname FROM smc_school AS s WHERE s.school_id = t.school_id) AS school_cnname,
             t.class_id,
             c.class_branch,
             c.class_cnname,
             c.class_enname,
             t.income_date,
             GROUP_CONCAT(hourstudy_id) as hourstudy_ids,
             GROUP_CONCAT(distinct d.student_cnname) as student_cnnames,
             COUNT(income_id) as income_count,
             sum(income_price) as price_all
            FROM cmb_trans_transfer AS t,smc_class AS c,smc_student d 
            WHERE t.class_id = c.class_id and t.student_id=d.student_id
            AND t.transfer_status <> '2'
            AND t.income_isconfirm = '1'
            AND t.income_price > 0
            and t.is_confirm=1
            {$datawhere}
            GROUP BY t.school_id, t.class_id,t.income_date
            order by t.income_date";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['class_branch'] = $dateexcelvar['class_branch'];
                    $datearray['class_enname'] = $dateexcelvar['class_enname'];
                    $datearray['student_cnnames'] = $dateexcelvar['student_cnnames'];
                    $datearray['hourstudy_ids'] = $dateexcelvar['hourstudy_ids'];
                    $datearray['income_date'] = $dateexcelvar['income_date'];
                    $datearray['income_count'] = $dateexcelvar['income_count'];
                    $datearray['price_all'] = $dateexcelvar['price_all'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "学校名称", "学校编号", "班级编号", "班级别名", "学生姓名", "考勤序号", "课销日期", "课销条数", "课销总金额"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'class_branch', 'class_enname', 'student_cnnames', 'hourstudy_ids', 'income_date', 'income_count', 'price_all');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('班级课耗监管待划拨明细表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $orderList = $this->DataControl->selectClear($sql);
            if (!$orderList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "SELECT t.school_id, t.class_id,t.income_date
                    FROM cmb_trans_transfer AS t,smc_class AS c
                    WHERE t.class_id = c.class_id
                    AND t.transfer_status <> '2'
                    AND t.income_isconfirm = '1'
                    AND t.income_price > 0
                    and t.is_confirm=1
                    {$datawhere}
                    GROUP BY t.school_id, t.class_id,t.income_date";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $orderList;
            return $data;
        }
    }

    function getTransChange($request)
    {
        $datawhere = " 1 ";

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and a.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and a.school_id ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select e.companies_cnname,
            b.school_branch,b.school_cnname,
            c.course_branch,c.course_cnname,
            d.student_branch,d.student_cnname,
            a.trading_pid
            from cmb_trans_order_change a
            left join smc_school b on a.school_id=b.school_id
            left join smc_course c on a.course_id=c.course_id
            left join smc_student d on a.student_id=d.student_id
            left join gmc_code_companies e on a.companies_id=e.companies_id
            where {$datawhere} 
            and change_status=0 
            order by a.change_id asc";

        if (isset($request['is_export']) && $request['is_export'] == 1) {

            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['companies_cnname'] = $dateexcelvar['companies_cnname'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['student_cnname'] = $dateexcelvar['student_cnname'];
                    $datearray['trading_pid'] = $dateexcelvar['trading_pid'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("主体名称", "学校名称", "学校编号", "课程编号", "课程中文名", "学生编号", "学生中文名", "交易编号"));
            $excelfileds = array('companies_cnname', 'school_cnname', 'school_branch', 'course_branch', 'course_cnname', 'student_branch', 'student_cnname', 'trading_pid');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch('学生处理异动表.xlsx'));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $orderList = $this->DataControl->selectClear($sql);
            if (!$orderList) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $data = array();
            if (isset($request['is_count']) && $request['is_count'] == 1) {
                $count_sql = "select a.change_id
                    from cmb_trans_order_change a
                    where {$datawhere}
                    and change_status=0";
                $db_nums = $this->DataControl->selectClear($count_sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }

            $data['list'] = $orderList;
            return $data;
        }
    }

    function withdrawProvision($request){

        if(!isset($request['companies_id']) || $request['companies_id']=='' || $request['companies_id']<=0){
            $this->error = true;
            $this->errortip = "请选择主体";
            return false;
        }

        if(!isset($request['applyAmt']) || $request['applyAmt']=='' || $request['applyAmt']<=0){
            $this->error = true;
            $this->errortip = "请输入申请金额";
            return false;
        }

        if(!isset($request['desc']) || $request['desc']==''){
            $this->error = true;
            $this->errortip = "请输入申请备注";
            return false;
        }

        $companiesOne=$this->DataControl->getFieldOne("gmc_code_companies","companies_agencyid","companies_id='{$request['companies_id']}'");

        if(!$companiesOne){
            $this->error = true;
            $this->errortip = "无该主体";
            return false;
        }

        $sql = "select a.transferStatus,a.batch_pid,a.agency_id
                from cmb_trans_provision as a
                where a.companies_id='{$request['companies_id']}'
                order by a.provision_time desc
                limit 0,1";
        $logOne=$this->DataControl->selectOne($sql);
        if($logOne['transferStatus']=='A'){
            $Model = new \Model\Api\CmbTransModel($logOne['agency_id']);

            $tem_data=array();
            $tem_data['agencyId'] = $logOne['agency_id'];
            $tem_data['transferId'] = $logOne['batch_pid'];
            $returnData = $Model->transfer($tem_data, '/pfs/head/queryWithdrawProvision');

            if($returnData){
                $data=array();
                $data['transferStatus']=$returnData['transferStatus'];
                $data['provision_desc']=$returnData['desc'];
                $data['tranSerial']=$returnData['tranSerial'];
                $this->DataControl->updateData("cmb_trans_provision","batch_pid='{$logOne['batch_pid']}'",$data);
                $logOne['transferStatus']=$returnData['transferStatus'];

            }
        }

        if($logOne['transferStatus']=='A'){
            $this->error = true;
            $this->errortip = "该主体存在申请中的退款,请完成后继续申请";
            return false;
        }

        $Model = new \Model\Api\CmbTransModel($companiesOne['companies_agencyid']);

        $batch_pid=$this->create_guid();

        $data=array();
        $data['agencyId']=$companiesOne['companies_agencyid'];
        $data['transferId']=$batch_pid;
        $data['applyAmt']=(int)(bcmul($request['applyAmt'] , 100));
        $data['applyReason']=$request['desc'];
        $data['applyType']='00';

        $returnData = $Model->transfer($data, '/pfs/head/withdrawProvision',$batch_pid);

        if($returnData){
            $data=array();
            $data['companies_id']=$request['companies_id'];
            $data['agency_id']=$companiesOne['companies_agencyid'];
            $data['batch_pid']=$batch_pid;
            $data['applyAmt']=$request['applyAmt'];
            $data['applyType']='00';
            $data['transferStatus']=$returnData['transferStatus'];
            $data['reason']=$request['desc'];
            $data['provision_desc']=$returnData['desc'];
            $data['tranSerial']=$returnData['tranSerial'];
            $data['provision_time']=time();
            $this->DataControl->insertData("cmb_trans_provision",$data);

            return true;
        }else{
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }

    }

    function getWithdrawProvisionList($request){

        $datawhere = " 1 ";

        if (isset($request['companies_id']) && $request['companies_id'] !== '') {
            $datawhere .= " and a.companies_id ='{$request['companies_id']}'";
        }

        if (isset($request['transferStatus']) && $request['transferStatus'] !== '') {
            $datawhere .= " and a.transferStatus ='{$request['transferStatus']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select b.companies_cnname,b.companies_agencyid,a.batch_pid,a.applyAmt,a.applyType,a.transferStatus,a.provision_desc,a.tranSerial,from_unixtime(a.provision_time,'%Y-%m-%d') as provision_time,a.reason
                from cmb_trans_provision as a
                left join gmc_code_companies as b on a.companies_id=b.companies_id
                where {$datawhere} and b.company_id='{$request['company_id']}'
                order by a.provision_time desc
                ";

        $status=array("A"=>"审核中","D"=>"驳回","S"=>"成功","F"=>"失败","P"=>"未知");

        $sql .= ' limit ' . $pagestart . ',' . $num;
        $logList = $this->DataControl->selectClear($sql);
        if (!$logList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }

        foreach($logList as &$logOne){

            if($logOne['transferStatus']=='A'){
                $Model = new \Model\Api\CmbTransModel($logOne['companies_agencyid']);

                $tem_data=array();
                $tem_data['agencyId'] = $logOne['companies_agencyid'];
                $tem_data['transferId'] = $logOne['batch_pid'];
                $returnData = $Model->transfer($tem_data, '/pfs/head/queryWithdrawProvision');

                if($returnData){
                    $data=array();
                    $data['transferStatus']=$returnData['transferStatus'];
                    $data['provision_desc']=$returnData['desc'];
                    $data['tranSerial']=$returnData['tranSerial'];
                    $this->DataControl->updateData("cmb_trans_provision","batch_pid='{$logOne['batch_pid']}'",$data);
                    $logOne['transferStatus']=$returnData['transferStatus'];
                    $logOne['provision_desc']=$returnData['desc'];
                    $logOne['tranSerial']=$returnData['tranSerial'];
                }
            }

            $logOne['transferStatus_name']=$status[$logOne['transferStatus']];

        }

        $data = array();
        $count_sql = "select a.provision_id 
        from cmb_trans_provision as a
        left join gmc_code_companies as b on a.companies_id=b.companies_id
        where {$datawhere} and b.company_id='{$request['company_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $logList;
        return $data;

    }

    function getSuperviseStuList($request){







    }


    function queryStudentOrder()
    {
        $agencyid = '2022012065699299';

        $Model = new \Model\Api\CmbTransModel($agencyid);

        $tem_data = array();
        $tem_data['agencyId'] = $agencyid;
        $tem_data['date'] = '2022-02-25';
        $tem_data['classId'] = '20211212000021';
        $returnData = $Model->transfer($tem_data, '/pfs/head/queryStudentOrder');

        debug($returnData);
        exit;
    }

    function queryClassEliminate()
    {
        $agencyid = '2022022067269522';

        $Model = new \Model\Api\CmbTransModel($agencyid);

        $tem_data = array();
        $tem_data['agencyId'] = $agencyid;
        $tem_data['classId'] = '20220217000027';
        $returnData = $Model->transfer($tem_data, '/pfs/head/queryClassEliminate');

        debug($returnData);
        exit;
    }
    

}