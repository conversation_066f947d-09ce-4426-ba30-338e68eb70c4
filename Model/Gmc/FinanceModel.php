<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class  FinanceModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $paramArray
     * @return array
     *   获取会计科目列表
     */
    function getAcctList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.acct_name like '%{$paramArray['keyword']}%' or a.acct_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT distinct
                a.acct_id,
                a.acct_code,
                a.acct_name,
                a.acctgroup_code,
                a.accttype_code,
                g.acctgroup_name,
                t.accttype_name,
                a.acct_remk 
            FROM
                smc_code_acct AS a
                LEFT JOIN smc_code_acctgroup AS g ON a.acctgroup_code = g.acctgroup_code
                LEFT JOIN smc_code_accttype AS t ON a.accttype_code = t.accttype_code 
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY
                a.acct_id DESC
            LIMIT {$pagestart},{$num}";

        $acctList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.acct_id)
            FROM
                smc_code_acct AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('acct_code', 'acct_name', 'acctgroup_name ', 'accttype_name', 'acct_remk');
        $fieldname = $this->LgArraySwitch(array('会记科目代码', '会记科目名称', '会计科目分组名称', '会记科目类型名称', '备注'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($acctList) {
            $result['list'] = $acctList;
        } else {
            $result['list'] = array();
        }

        $result['acctgroup'] = $this->DataControl->selectClear("select acctgroup_code,acctgroup_name from smc_code_acctgroup where company_id = '{$paramArray['company_id']}'");

        $result['accttype'] = $this->DataControl->selectClear("select accttype_code,accttype_name from smc_code_accttype where company_id = '{$paramArray['company_id']}'");

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加会计科目
    function addAcctAction($paramArray)
    {
        $data = array();
        $data['acct_code'] = $paramArray['acct_code'];
        $data['acct_name'] = $paramArray['acct_name'];
        $data['acctgroup_code'] = $paramArray['acctgroup_code'];
        $data['accttype_code'] = $paramArray['accttype_code'];
        $data['acct_remk'] = $paramArray['acct_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['acct_code'] = "会计科目代码";
        $field['acct_name'] = "会计科目名称";
        $field['acctgroup_code'] = "会计科目分组代码";
        $field['accttype_code'] = "会计科目类型代码";
        $field['acct_remk'] = "备注";
        $field['company_id'] = "所属公司";

        $acct_code = $this->DataControl->getFieldOne('smc_code_acct', 'acct_id', "acct_code = '{$paramArray['acct_code']}'");
        if ($acct_code) {
            ajax_return(array('error' => 1, 'errortip' => "会计科目代码已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_acct', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加会计科目成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加会计科目失败', 'result' => $result);
        }
        return $res;
    }

    //编辑会计科目
    function updateAcctAction($paramArray)
    {
        $stafferOne = $this->DataControl->getFieldOne("smc_code_acct", "acct_id", "acct_id = '{$paramArray['acct_id']}'");
        if ($stafferOne) {
            $data = array();
            $data['acct_code'] = $paramArray['acct_code'];
            $data['acct_name'] = $paramArray['acct_name'];
            $data['acctgroup_code'] = $paramArray['acctgroup_code'];
            $data['accttype_code'] = $paramArray['accttype_code'];
            $data['acct_remk'] = $paramArray['acct_remk'];

            $field = array();
            $field['acct_code'] = "会计科目代码";
            $field['acct_name'] = "会计科目名称";
            $field['acctgroup_code'] = "会计科目分组代码";
            $field['accttype_code'] = "会计科目类型代码";
            $field['acct_remk'] = "备注";

            $acct_code = $this->DataControl->getFieldOne('smc_code_acct', 'acct_code', "acct_id = '{$paramArray['acct_id']}'");
            if ($paramArray['acct_code'] != $acct_code['acct_code']) {
                $acct_code = $this->DataControl->getFieldOne('smc_code_acct', 'acct_id', "acct_code = '{$paramArray['acct_code']}'");
                if ($acct_code) {
                    ajax_return(array('error' => 1, 'errortip' => "会计科目代码已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_acct", "acct_id = '{$paramArray['acct_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "会计科目修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '会计科目修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除会计科目
    function delAcctAction($paramArray)
    {
        $AcctOne = $this->DataControl->getFieldOne("smc_code_acct", "acct_code", "acct_id = '{$paramArray['acct_id']}'");
        if ($AcctOne) {
            $a = $this->DataControl->getFieldOne("smc_code_pettycash", "pettycash_id", "acct_code = '{$AcctOne['acct_code']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该会计科目已被使用，不可删除"));
            }
            $b = $this->DataControl->getFieldOne("smc_code_spending", "spending_id", "acct_code = '{$AcctOne['acct_code']}'");
            if ($b) {
                ajax_return(array('error' => 1, 'errortip' => "该会计科目已被使用，不可删除"));
            }
            $c = $this->DataControl->getFieldOne("smc_fee_pricing", "pricing_id", "acct_code = '{$AcctOne['acct_code']}'");
            if ($c) {
                ajax_return(array('error' => 1, 'errortip' => "该会计科目已被使用，不可删除"));
            }
            if ($this->DataControl->delData("smc_code_acct", "acct_id = '{$paramArray['acct_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除会计科目成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除会计科目失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //会计科目分组列表
    function getAcctgroupList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.acctgroup_name like '%{$paramArray['keyword']}%' or a.acctgroup_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                a.acctgroup_id,
                a.acctgroup_name,
                a.acctgroup_code,
                a.acctgroup_remk
            FROM
                smc_code_acctgroup AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY
                a.acctgroup_id DESC    
            LIMIT {$pagestart},{$num}";

        $acctList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.acctgroup_id)
            FROM
                smc_code_acctgroup AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('acctgroup_name', 'acctgroup_code', 'acctgroup_remk');
        $fieldname = $this->LgArraySwitch(array('会计科目分组名称', '会计科目分组代码', '会计科目分组备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($acctList) {
            $result['list'] = $acctList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加会计科目分组
    function addAcctgroupAction($paramArray)
    {
        $data = array();
        $data['acctgroup_name'] = $paramArray['acctgroup_name'];
        $data['acctgroup_code'] = $paramArray['acctgroup_code'];
        $data['acctgroup_remk'] = $paramArray['acctgroup_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['acctgroup_name'] = "会计科目分组名称";
        $field['acctgroup_code'] = "会计科目分组代码";
        $field['acctgroup_remk'] = "会计科目分组备注";
        $field['company_id'] = "所属公司";

        $acctgroup_code = $this->DataControl->getFieldOne('smc_code_acctgroup', 'acctgroup_id', "acctgroup_code = '{$paramArray['acctgroup_code']}'");
        if ($acctgroup_code) {
            ajax_return(array('error' => 1, 'errortip' => "会计科目分组代码已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_acctgroup', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加会计科目分组成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加会计科目分组失败', 'result' => $result);
        }
        return $res;
    }

    //编辑会计科目分组
    function updateAcctgroupAction($paramArray)
    {
        $AcctgrouptOne = $this->DataControl->getFieldOne("smc_code_acctgroup", "acctgroup_id", "acctgroup_id = '{$paramArray['acctgroup_id']}'");
        if ($AcctgrouptOne) {
            $data = array();
            $data['acctgroup_name'] = $paramArray['acctgroup_name'];
            $data['acctgroup_code'] = $paramArray['acctgroup_code'];
            $data['acctgroup_remk'] = $paramArray['acctgroup_remk'];

            $field = array();
            $field['acctgroup_name'] = "会计科目分组名称";
            $field['acctgroup_code'] = "会计科目分组代码";
            $field['acctgroup_remk'] = "会计科目分组备注";

            $acctgroup_code = $this->DataControl->getFieldOne('smc_code_acctgroup', 'acctgroup_code', "acctgroup_id = '{$paramArray['acctgroup_id']}'");
            if ($paramArray['acctgroup_code'] != $acctgroup_code['acctgroup_code']) {
                $acctgroup_code = $this->DataControl->getFieldOne('smc_code_acctgroup', 'acctgroup_id', "acctgroup_code = '{$paramArray['acctgroup_code']}'");
                if ($acctgroup_code) {
                    ajax_return(array('error' => 1, 'errortip' => "会计科目分组代码已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_acctgroup", "acctgroup_id = '{$paramArray['acctgroup_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "会计科目分组修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '会计科目分组修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除会计科目分组
    function delAcctgroupAction($paramArray)
    {
        $AcctgroupOne = $this->DataControl->getFieldOne("smc_code_acctgroup", "acctgroup_id,acctgroup_code", "acctgroup_id = '{$paramArray['acctgroup_id']}'");
        if ($AcctgroupOne) {
            $AccttypeOne = $this->DataControl->getFieldOne("smc_code_accttype", "accttype_id", "acctgroup_code = '{$AcctgroupOne['acctgroup_code']}'");
            $AcctOne = $this->DataControl->getFieldOne("smc_code_acct", "acct_id", "acctgroup_code = '{$AcctgroupOne['acctgroup_code']}'");
            if ($AccttypeOne || $AcctOne) {
                ajax_return(array('error' => 1, 'errortip' => "会计科目分组已被使用，无法删除!"));
            }
            if ($this->DataControl->delData("smc_code_acctgroup", "acctgroup_id = '{$paramArray['acctgroup_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除会计科目分组成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除会计科目分组失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //会计科目类型列表
    function getAccttypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (a.accttype_name like '%{$paramArray['keyword']}%' or a.accttype_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['acctgroup_code']) && $paramArray['acctgroup_code'] !== "") {
            $datawhere .= " and a.acctgroup_code ='{$paramArray['acctgroup_code']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT distinct
                a.accttype_id,
                a.accttype_name,
                a.accttype_code,
                a.acctgroup_code,
                a.accttype_remk,
                ac.acctgroup_name
            FROM
                smc_code_accttype AS a LEFT JOIN smc_code_acctgroup AS ac ON a.acctgroup_code = ac.acctgroup_code
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'
            ORDER BY
                a.accttype_id DESC    
            LIMIT {$pagestart},{$num}";

        $acctList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(a.accttype_id)
            FROM
                smc_code_accttype AS a
            WHERE
                {$datawhere} and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('accttype_name', 'accttype_code', 'accttype_remk', 'acctgroup_name',);
        $fieldname = $this->LgArraySwitch(array('会计科目类型名称', '会计科目类型代码', '会计科目类型备注', '会计科目分组名称'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($acctList) {
            $result['list'] = $acctList;
        } else {
            $result['list'] = array();
        }

        $result['acctgroup'] = $this->DataControl->selectClear("select acctgroup_code,acctgroup_name from smc_code_acctgroup where company_id = '{$paramArray['company_id']}'");

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加会计科目类型
    function addActtypeAction($paramArray)
    {
        $data = array();
        $data['accttype_name'] = $paramArray['accttype_name'];
        $data['accttype_code'] = $paramArray['accttype_code'];
        $data['accttype_remk'] = $paramArray['accttype_remk'];
        $data['acctgroup_code'] = $paramArray['acctgroup_code'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['accttype_name'] = "会计科目类型名称";
        $field['accttype_code'] = "会计科目类型代码";
        $field['accttype_remk'] = "会计科目类型备注";
        $field['acctgroup_code'] = "会计科目分组代码";
        $field['company_id'] = "所属公司";

        $accttype_code = $this->DataControl->getFieldOne('smc_code_accttype', 'accttype_id', "accttype_code = '{$paramArray['accttype_code']}'");
        if ($accttype_code) {
            ajax_return(array('error' => 1, 'errortip' => "会计科目类型代码已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_accttype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加会计科目类型成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加会计科目类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑会计科目类型
    function updateActtypeAction($paramArray)
    {
        $AcctgrouptOne = $this->DataControl->getFieldOne("smc_code_accttype", "accttype_id", "accttype_id = '{$paramArray['accttype_id']}'");
        if ($AcctgrouptOne) {
            $data = array();
            $data['accttype_name'] = $paramArray['accttype_name'];
            $data['accttype_code'] = $paramArray['accttype_code'];
            $data['accttype_remk'] = $paramArray['accttype_remk'];
            $data['acctgroup_code'] = $paramArray['acctgroup_code'];

            $field = array();
            $field['accttype_name'] = "会计科目类型名称";
            $field['accttype_code'] = "会计科目类型代码";
            $field['accttype_remk'] = "会计科目类型备注";
            $field['acctgroup_code'] = "会计科目分组代码";

            $accttype_code = $this->DataControl->getFieldOne('smc_code_accttype', 'accttype_code', "accttype_id = '{$paramArray['accttype_id']}'");
            if ($paramArray['accttype_code'] != $accttype_code['accttype_code']) {
                $accttype_code = $this->DataControl->getFieldOne('smc_code_accttype', 'accttype_id', "accttype_code = '{$paramArray['accttype_code']}'");
                if ($accttype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "会计科目类型代码已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_accttype", "accttype_id = '{$paramArray['accttype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "会计科目类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '会计科目类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除会计科目类型
    function delActtypeAction($paramArray)
    {
        $ActtypeOne = $this->DataControl->getFieldOne("smc_code_accttype", "accttype_code", "accttype_id = '{$paramArray['accttype_id']}'");
        if ($ActtypeOne) {
            $a = $this->DataControl->getFieldOne("smc_code_acct", "acct_id", "accttype_code = '{$ActtypeOne['accttype_code']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该会计科目类型已被使用，不可删除"));
            }
            if ($this->DataControl->delData("smc_code_accttype", "accttype_id = '{$paramArray['accttype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除会计科目类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除会计科目类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //银行列表
    function getBankList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (b.bank_name like '%{$paramArray['keyword']}%' or b.bank_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                b.bank_id,
                b.bank_name,
                b.bank_code,
                b.bank_remk
            FROM
                smc_code_bank AS b
            WHERE
                {$datawhere} and b.company_id = '{$paramArray['company_id']}'
            ORDER BY
                b.bank_id DESC    
            LIMIT {$pagestart},{$num}";

        $acctList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(b.bank_id)
            FROM
                smc_code_bank AS b
            WHERE
                {$datawhere} and b.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('bank_name', 'bank_code', 'bank_remk');
        $fieldname = $this->LgArraySwitch(array('银行名称', '银行编号', '银行备注'));
        $fieldcustom = array("1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($acctList) {
            $result['list'] = $acctList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无银行信息", 'result' => $result);
        }

        return $res;
    }

    //添加银行
    function addBankAction($paramArray)
    {
        $data = array();
        $data['bank_name'] = $paramArray['bank_name'];
        $data['bank_code'] = $paramArray['bank_code'];
        $data['bank_remk'] = $paramArray['bank_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['bank_name'] = "银行名称";
        $field['bank_code'] = "银行编号";
        $field['bank_remk'] = "银行备注";
        $field['company_id'] = "所属公司";

        $bank_code = $this->DataControl->getFieldOne('smc_code_bank', 'bank_id', "bank_code = '{$paramArray['bank_code']}'");
        if ($bank_code) {
            ajax_return(array('error' => 1, 'errortip' => "银行编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_bank', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加银行成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '添加银行', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加银行失败', 'result' => $result);
        }
        return $res;
    }

    //编辑银行
    function updateBankAction($paramArray)
    {
        $BankOne = $this->DataControl->getFieldOne("smc_code_bank", "bank_id", "bank_id = '{$paramArray['bank_id']}'");
        if ($BankOne) {
            $data = array();
            $data['bank_name'] = $paramArray['bank_name'];
            $data['bank_code'] = $paramArray['bank_code'];
            $data['bank_remk'] = $paramArray['bank_remk'];

            $field = array();
            $field['bank_name'] = "银行名称";
            $field['bank_code'] = "银行编号";
            $field['bank_remk'] = "银行备注";

            $bank_code = $this->DataControl->getFieldOne('smc_code_bank', 'bank_code', "bank_id = '{$paramArray['bank_id']}'");
            if ($paramArray['bank_code'] != $bank_code['bank_code']) {
                $bank_code = $this->DataControl->getFieldOne('smc_code_bank', 'bank_id', "bank_code = '{$paramArray['bank_code']}'");
                if ($bank_code) {
                    ajax_return(array('error' => 1, 'errortip' => "银行编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_bank", "bank_id = '{$paramArray['bank_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "银行修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '编辑银行', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '银行修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除银行
    function delBankAction($paramArray)
    {
        $BankOne = $this->DataControl->getFieldOne("smc_code_bank", "bank_id", "bank_id = '{$paramArray['bank_id']}'");
        if ($BankOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_code_bank", "bank_id = '{$paramArray['bank_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除银行成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '删除银行', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除银行失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //收费类型列表
    function getFeeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (f.feeitem_cnname like '%{$paramArray['keyword']}%' or f.feeitem_branch like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                f.feeitem_id,
                f.feeitem_branch,
                f.feeitem_cnname,
                f.feeitem_expendtype,
                f.feeitem_expendtype as status,
                f.feeitem_unit,
                f.feeitem_class,
                f.feeitem_price,
                f.feeitem_class as feeitem_class_status,
                f.feeitem_remk
            FROM
                smc_code_feeitem AS f
            WHERE
                {$datawhere} and company_id = '{$paramArray['company_id']}'
            ORDER BY
                f.feeitem_id DESC    
            LIMIT {$pagestart},{$num}";

        $feetList = $this->DataControl->selectClear($sql);

        if ($feetList) {
            $status = $this->LgArraySwitch(array("0" => "考勤模式", "1" => "月度模式", "2" => "一次性模式"));
            foreach ($feetList as &$val) {
                $val['status'] = $status[$val['status']];
            }
            $status = $this->LgArraySwitch(array("0" => "课程杂费", "1" => "普通杂费"));
            foreach ($feetList as &$val) {
                $val['feeitem_class_status'] = $status[$val['feeitem_class_status']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(f.feeitem_id)
            FROM
                smc_code_feeitem AS f
            WHERE
                {$datawhere} and company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('feeitem_cnname', 'feeitem_branch', 'status', 'feeitem_class_status', 'feeitem_price', 'feeitem_unit', 'feeitem_remk', 'feeitem_expendtype', 'feeitem_class');
        $fieldname = $this->LgArraySwitch(array('收费项目名称', '收费项目编号', '消耗类型', '杂费类型', '单价', '销售单位', '收费项目备注', '消耗类型', '杂费类型'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "0", "0");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($feetList) {
            $result['list'] = $feetList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无课程收费项目", 'result' => $result);
        }

        return $res;
    }

    //添加收费类型
    function addFeeAction($paramArray)
    {
        $data = array();
        $data['feeitem_cnname'] = $paramArray['feeitem_cnname'];
        $data['feeitem_branch'] = $paramArray['feeitem_branch'];
        $data['feeitem_expendtype'] = $paramArray['feeitem_expendtype'];
        $data['feeitem_class'] = $paramArray['feeitem_class'];
        $data['feeitem_unit'] = $paramArray['feeitem_unit'];
        $data['feeitem_remk'] = $paramArray['feeitem_remk'];
        $data['feeitem_price'] = $paramArray['feeitem_price'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['feeitem_cnname'] = "收费项目名称";
        $field['feeitem_branch'] = "收费项目编号";
        $field['feeitem_expendtype'] = "消耗类型";
        $field['feeitem_class'] = "课程类型";
        $field['feeitem_unit'] = "销售单位";
        $field['feeitem_remk'] = "收费项目备注";
        $field['feeitem_price'] = "单价";
        $field['company_id'] = "所属公司";

        $feetype_code = $this->DataControl->getFieldOne('smc_code_feeitem', 'feeitem_id', "feeitem_branch = '{$paramArray['feeitem_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($feetype_code) {
            ajax_return(array('error' => 1, 'errortip' => "收费类型编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_feeitem', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加收费类型成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加收费类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑收费类型
    function updateFeeAction($paramArray)
    {
        $BankOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_id", "feeitem_id = '{$paramArray['feeitem_id']}'");
        if ($BankOne) {
            $data = array();
            $data['feeitem_cnname'] = $paramArray['feeitem_cnname'];
            $data['feeitem_branch'] = $paramArray['feeitem_branch'];
            $data['feeitem_unit'] = $paramArray['feeitem_unit'];
            $data['feeitem_class'] = $paramArray['feeitem_class'];
            $data['feeitem_expendtype'] = $paramArray['feeitem_expendtype'];
            $data['feeitem_price'] = $paramArray['feeitem_price'];
            $data['feeitem_remk'] = $paramArray['feeitem_remk'];

            $field = array();
            $field['feeitem_cnname'] = "收费项目名称";
            $field['feeitem_branch'] = "收费项目编号";
            $field['feeitem_class'] = "消耗类型";
            $field['feeitem_expendtype'] = "杂费类型";
            $field['feeitem_unit'] = "销售单位";
            $field['feeitem_price'] = "单价";
            $field['feeitem_remk'] = "收费项目备注";

            $feetype_code = $this->DataControl->getFieldOne('smc_code_feeitem', 'feeitem_branch', "feeitem_id = '{$paramArray['feeitem_id']}'");
            if ($paramArray['feeitem_branch'] != $feetype_code['feeitem_branch']) {
                $feetype_code = $this->DataControl->getFieldOne('smc_code_feeitem', 'feeitem_id', "feeitem_branch = '{$paramArray['feeitem_branch']}' and company_id = '{$paramArray['company_id']}'");
                if ($feetype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "收费类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_feeitem", "feeitem_id = '{$paramArray['feeitem_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "收费类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '收费类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除收费类型
    function delFeeAction($paramArray)
    {
        $feetypeOne = $this->DataControl->getFieldOne("smc_code_feeitem", "feeitem_branch", "feeitem_id = '{$paramArray['feeitem_id']}'");

        if ($feetypeOne) {
            $a = $this->DataControl->getFieldOne("smc_fee_pricing_items", "items_id", "feeitem_branch = '{$feetypeOne['feeitem_branch']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该收费类型被课程收费标准使用，不可删除"));
            }
            if ($this->DataControl->delData("smc_code_feeitem", "feeitem_id = '{$paramArray['feeitem_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除收费类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除收费类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //收费支付方式列表
    function getPaytypeList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.paytype_name like '%{$paramArray['keyword']}%' or p.paytype_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                p.paytype_id,
                p.paytype_name,
                p.paytype_code,
                p.paytype_remk
            FROM
                smc_code_paytype AS p
            WHERE
                {$datawhere}
            ORDER BY
                p.paytype_id DESC    
            LIMIT {$pagestart},{$num}";

        $PaytypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.paytype_id)
            FROM
                smc_code_paytype AS p
            WHERE
                {$datawhere}");
        $allnums = $all_num[0][0];

        $fieldstring = array('paytype_name', 'paytype_code', 'paytype_remk');
        $fieldname = $this->LgArraySwitch(array('支付方式名称', '支付方式编号', '支付方式备注'));
        $fieldcustom = array("1", "1", "1");
        $fieldshow = array("1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PaytypeList) {
            $result['list'] = $PaytypeList;
        } else {
            $result['list'] = array();
        }

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加收费支付方式
    function addPaytypeAction($paramArray)
    {
        $data = array();
        $data['paytype_code'] = $paramArray['paytype_code'];
        $data['paytype_name'] = $paramArray['paytype_name'];
        $data['paytype_remk'] = $paramArray['paytype_remk'];

        $field = array();
        $field['paytype_code'] = "收费支付方式编号";
        $field['paytype_name'] = "收费支付方式名称";
        $field['paytype_remk'] = "收费支付方式备注";

        $paytype_code = $this->DataControl->getFieldOne('smc_code_paytype', 'paytype_id', "paytype_code = '{$paramArray['paytype_code']}'");
        if ($paytype_code) {
            ajax_return(array('error' => 1, 'errortip' => "收费支付方式编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_paytype', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加收费支付方式成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加收费支付方式失败', 'result' => $result);
        }
        return $res;
    }

    //编辑收费支付方式
    function updatePaytypeAction($paramArray)
    {
        $PaytypeOne = $this->DataControl->getFieldOne("smc_code_paytype", "paytype_id", "paytype_id = '{$paramArray['paytype_id']}'");
        if ($PaytypeOne) {
            $data = array();
            $data['paytype_name'] = $paramArray['paytype_name'];
            $data['paytype_code'] = $paramArray['paytype_code'];
            $data['paytype_remk'] = $paramArray['paytype_remk'];

            $field = array();
            $field['paytype_name'] = "收费支付方式名称";
            $field['paytype_code'] = "收费支付方式编号";
            $field['paytype_remk'] = "收费支付方式备注";

            $paytype_code = $this->DataControl->getFieldOne('smc_code_paytype', 'paytype_code', "paytype_id = '{$paramArray['paytype_id']}'");
            if ($paramArray['paytype_code'] != $paytype_code['paytype_code']) {
                $paytype_code = $this->DataControl->getFieldOne('smc_code_paytype', 'paytype_id', "paytype_code = '{$paramArray['paytype_code']}'");
                if ($paytype_code) {
                    ajax_return(array('error' => 1, 'errortip' => "收费支付方式编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_paytype", "paytype_id = '{$paramArray['paytype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "收费支付方式修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '收费支付方式修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除收费支付方式
    function delPaytypeAction($paramArray)
    {
        $PaytypeOne = $this->DataControl->getFieldOne("smc_code_paytype", "paytype_id", "paytype_id = '{$paramArray['paytype_id']}'");
        if ($PaytypeOne) {
            if ($this->DataControl->delData("smc_code_paytype", "paytype_id = '{$paramArray['paytype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除收费支付方式成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除收费支付方式失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //零用金列表
    function getPettycashList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (p.pettycash_name like '%{$paramArray['keyword']}%' or p.pettycash_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['acct_code']) && $paramArray['acct_code'] !== "") {
            $datawhere .= " and p.acct_code ={$paramArray['acct_code']}";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT distinct
                p.pettycash_id,
                p.pettycash_name,
                p.pettycash_code,
                p.pettycash_remk,
                p.acct_code,
                a.acct_name
            FROM
                smc_code_pettycash AS p LEFT JOIN smc_code_acct AS a ON p.acct_code=a.acct_code
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'
            ORDER BY
                p.pettycash_id DESC    
            LIMIT {$pagestart},{$num}";

        $PaytypeList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(p.pettycash_id)
            FROM
                smc_code_pettycash AS p
            WHERE
                {$datawhere} AND p.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('pettycash_name', 'pettycash_code', 'pettycash_remk', 'acct_code', 'acct_name');
        $fieldname = $this->LgArraySwitch(array('零用金名称', '零用金编号', '零用金备注', '会计科目代码', '会计科目名称'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($PaytypeList) {
            $result['list'] = $PaytypeList;
        } else {
            $result['list'] = array();
        }

        $result['acct'] = $this->DataControl->selectClear("select acct_code,acct_name from smc_code_acct where company_id = '{$paramArray['company_id']}'");

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加零用金
    function addPettycashAction($paramArray)
    {
        $data = array();
        $data['pettycash_code'] = $paramArray['pettycash_code'];
        $data['pettycash_name'] = $paramArray['pettycash_name'];
        $data['acct_code'] = $paramArray['acct_code'];
        $data['pettycash_remk'] = $paramArray['pettycash_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['pettycash_code'] = "零用金编号";
        $field['pettycash_name'] = "零用金名称";
        $field['acct_code'] = "会计科目代码";
        $field['pettycash_remk'] = "零用金备注";
        $field['company_id'] = "所属公司";

        $pettycash_code = $this->DataControl->getFieldOne('smc_code_pettycash', 'pettycash_id', "pettycash_code = '{$paramArray['pettycash_code']}'");
        if ($pettycash_code) {
            ajax_return(array('error' => 1, 'errortip' => "零用金编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_pettycash', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加零用金成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加零用金失败', 'result' => $result);
        }
        return $res;
    }

    //编辑零用金
    function updatePettycashAction($paramArray)
    {
        $PettycashOne = $this->DataControl->getFieldOne("smc_code_pettycash", "pettycash_id", "pettycash_id = '{$paramArray['pettycash_id']}'");
        if ($PettycashOne) {
            $data = array();
            $data['pettycash_code'] = $paramArray['pettycash_code'];
            $data['pettycash_name'] = $paramArray['pettycash_name'];
            $data['acct_code'] = $paramArray['acct_code'];
            $data['pettycash_remk'] = $paramArray['pettycash_remk'];

            $field = array();
            $field['pettycash_code'] = "零用金编号";
            $field['pettycash_name'] = "零用金名称";
            $field['acct_code'] = "会计科目代码";
            $field['pettycash_remk'] = "零用金备注";

            $pettycash_code = $this->DataControl->getFieldOne('smc_code_pettycash', 'pettycash_code', "pettycash_id = '{$paramArray['pettycash_id']}'");
            if ($paramArray['pettycash_code'] != $pettycash_code['pettycash_code']) {
                $pettycash_code = $this->DataControl->getFieldOne('smc_code_pettycash', 'pettycash_id', "pettycash_code = '{$paramArray['pettycash_code']}'");
                if ($pettycash_code) {
                    ajax_return(array('error' => 1, 'errortip' => "零用金编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_pettycash", "pettycash_id = '{$paramArray['pettycash_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "零用金修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '零用金修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除零用金
    function delPettycashAction($paramArray)
    {
        $PettycashOne = $this->DataControl->getFieldOne("smc_code_pettycash", "pettycash_id", "pettycash_id = '{$paramArray['pettycash_id']}'");
        if ($PettycashOne) {
            if ($this->DataControl->delData("smc_code_pettycash", "pettycash_id = '{$paramArray['pettycash_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除零用金成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除零用金失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //支出类型列表
    function getSpendingList($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.spending_name like '%{$paramArray['keyword']}%' or s.spending_code like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT distinct
                s.spending_id,
                s.spending_name,
                s.spending_code,
                s.spending_remk,
                s.acct_code,
                a.acct_name
            FROM
                smc_code_spending AS s LEFT JOIN smc_code_acct AS a ON s.acct_code = a.acct_code
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}'
            ORDER BY
                s.spending_id DESC    
            LIMIT {$pagestart},{$num}";

        $spendingList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.spending_id)
            FROM
                smc_code_spending AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('spending_name', 'spending_code', 'spending_remk', 'acct_code', 'acct_name');
        $fieldname = $this->LgArraySwitch(array('支出类型名称', '支出类型编号', '支付明细备注', '会计科目代码', '会计科目名称'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;

        if ($spendingList) {
            $result['list'] = $spendingList;
        } else {
            $result['list'] = array();
        }

        $result['acct'] = $this->DataControl->selectClear("select acct_code,acct_name from smc_code_acct where company_id = '{$paramArray['company_id']}'");

        $result['all_num'] = $allnums;

        $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);

        return $res;
    }

    //添加支付类型
    function addSpendingAction($paramArray)
    {
        $data = array();
        $data['spending_code'] = $paramArray['spending_code'];
        $data['spending_name'] = $paramArray['spending_name'];
        $data['acct_code'] = $paramArray['acct_code'];
        $data['spending_remk'] = $paramArray['spending_remk'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['spending_code'] = "支出类型编号";
        $field['spending_name'] = "支出类型名称";
        $field['acct_code'] = "会计科目代码";
        $field['spending_remk'] = "支付明细备注";
        $field['company_id'] = "所属公司";

        $spending_code = $this->DataControl->getFieldOne('smc_code_spending', 'spending_id', "spending_code = '{$paramArray['spending_code']}'");
        if ($spending_code) {
            ajax_return(array('error' => 1, 'errortip' => "支出类型编号已存在!"));
        }

        if ($this->DataControl->insertData('smc_code_spending', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加支付类型成功", 'result' => $result);
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加支付类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑支出类型
    function updateSpendingAction($paramArray)
    {
        $spendingOne = $this->DataControl->getFieldOne("smc_code_spending", "spending_id", "spending_id = '{$paramArray['spending_id']}'");
        if ($spendingOne) {
            $data = array();
            $data['spending_code'] = $paramArray['spending_code'];
            $data['spending_name'] = $paramArray['spending_name'];
            $data['acct_code'] = $paramArray['acct_code'];
            $data['spending_remk'] = $paramArray['spending_remk'];

            $field = array();
            $field['spending_code'] = "支出类型编号";
            $field['spending_name'] = "支出类型名称";
            $field['acct_code'] = "会计科目代码";
            $field['spending_remk'] = "支付明细备注";

            $spending_code = $this->DataControl->getFieldOne('smc_code_spending', 'spending_code', "spending_id = '{$paramArray['spending_id']}'");
            if ($paramArray['spending_code'] != $spending_code['spending_code']) {
                $spending_code = $this->DataControl->getFieldOne('smc_code_spending', 'spending_id', "spending_code = '{$paramArray['spending_code']}'");
                if ($spending_code) {
                    ajax_return(array('error' => 1, 'errortip' => "支出类型编号已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_spending", "spending_id = '{$paramArray['spending_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "支出类型修改成功", 'result' => $result);
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '支出类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除支出类型
    function delSpendingAction($paramArray)
    {
        $SpendingOne = $this->DataControl->getFieldOne("smc_code_spending", "spending_id", "spending_id = '{$paramArray['spending_id']}'");
        if ($SpendingOne) {
            if ($this->DataControl->delData("smc_code_spending", "spending_id = '{$paramArray['spending_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除支出类型成功", 'result' => $result);
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除支出类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //优惠券申请类型列表
    function couponsapplytype($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.applytype_cnname like '%{$paramArray['keyword']}%' or s.applytype_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['applytype_isneedcheck']) && $paramArray['applytype_isneedcheck'] !== '') {
            $datawhere .= " and s.applytype_isneedcheck='{$paramArray['applytype_isneedcheck']}'";
        }

        if (isset($paramArray['applytype_isschool']) && $paramArray['applytype_isschool'] !== '') {
            $datawhere .= " and s.applytype_isschool='{$paramArray['applytype_isschool']}'";
        }

        if (isset($paramArray['applytype_status']) && $paramArray['applytype_status'] !== '') {
            $datawhere .= " and s.applytype_status='{$paramArray['applytype_status']}'";
        }

        if (isset($paramArray['applytype_playclass']) && $paramArray['applytype_playclass'] !== '') {
            $datawhere .= " and s.applytype_playclass='{$paramArray['applytype_playclass']}'";
        }

        if (isset($paramArray['applytype_getscope']) && $paramArray['applytype_getscope'] !== '') {
            $datawhere .= " and s.applytype_getscope='{$paramArray['applytype_getscope']}'";
        }


        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.applytype_id,
                s.applytype_cnname,
                s.applytype_branch,
                s.couponsrules_id,
                s.applytype_playclass,
                s.applytype_isshop,
                s.applytype_status,
                s.applytype_playclass,
                s.applytype_isneedcheck,
                s.applytype_caneditcourse,
                s.applytype_isquota,
                s.applytype_maxprice,
                s.applytype_maxdiscount,
                s.applytype_minprice,
                s.applytype_minprice,
                s.applytype_isschool,
                s.applytype_applyschool,
                s.applytype_validitytype,
                s.applytype_validitydays,
                s.applytype_limitterm,
                s.applytype_limittype,
                s.applytype_coexist,
                s.applytype_deadline,
                s.applytype_manycourse,
                s.applytype_applymerge,s.applytype_ischannel,s.applytype_applycoursecat,s.applytype_getscope 
                ,(select (count(cs.schoolapply_id)) from smc_couponsapplytype_schoolapply as cs 
                where cs.applytype_id=s.applytype_id) as schoolNum
            FROM
                smc_code_couponsapplytype AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}' and s.applytype_port=0
            ORDER BY
                s.applytype_isshop ASC,s.applytype_id DESC LIMIT {$pagestart},{$num}";

        $spendingList = $this->DataControl->selectClear($sql);

        if ($spendingList) {
            $status = $this->LgArraySwitch(array("0" => "项目优惠券", "1" => "订单优惠券"));
            $isneedcheck = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $isquota = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            $term = $this->LgArraySwitch(array("0" => "限定最大申请额度", "1" => "固定优惠额度"));
            $coexist = $this->LgArraySwitch(array("0" => "否", "1" => "是", "2" => "豁免"));
//            $statuss = array("0" => "0", "1" => "1");

            foreach ($spendingList as &$val) {
                $val['applytype_playclass_name'] = $status[$val['applytype_playclass']];
                $val['applytype_isneedcheck_name'] = $isneedcheck[$val['applytype_isneedcheck']];
                $val['applytype_isquota_name'] = $isquota[$val['applytype_isquota']];
                $val['applytype_isschool_name'] = $isquota[$val['applytype_isschool']];
                $val['applytype_coexist_name'] = $coexist[$val['applytype_coexist']];
//                $val['applytype_status'] = $statuss[$val['applytype_status']];

                if ($val['applytype_applyschool'] == 0) {
                    $val['applytype_applyschool_name'] = '全部';
                } else {
                    $val['applytype_applyschool_name'] = $val['schoolNum'];
                }
                if ($val['applytype_validitytype'] == 0) {
                    $val['applytype_eftime'] = '领取后' . $val['applytype_validitydays'] . '天';
                } else {
                    $val['applytype_eftime'] = '截止至' . $val['applytype_deadline'];
                }

                $val['limit_conditions'] = $val['applytype_isquota'] == 1 ? $term[$val['applytype_limitterm']] : '--';
                $val['applytype_caneditcourse_name'] = $isquota[$val['applytype_caneditcourse']];
            }

        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.applytype_id)
            FROM
                smc_code_couponsapplytype AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}' and s.applytype_port=0");
        $allnums = $all_num[0][0];

        $fieldstring = array('applytype_cnname', 'applytype_branch', 'applytype_playclass_name', 'applytype_applyschool_name', 'applytype_isneedcheck_name', 'applytype_isschool_name', 'applytype_isquota_name', 'limit_conditions', 'applytype_status', 'applytype_eftime', 'applytype_maxprice', 'applytype_maxdiscount', 'applytype_minprice', 'applytype_caneditcourse_name', 'applytype_coexist_name');
        $fieldname = $this->LgArraySwitch(array('优惠券申请类型名称', '优惠券申请类型编号', '优惠券使用类型', '适用学校', '是否需要审核', '是否校区审核', '是否限额', '限额条件', '是否启用', '领取后有效期', '申请额度', '申请折扣', '最低消费金额', '是否允许校区修改适用课程', '能否和其他优惠券共用'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1", "1");
        $fieldswitch = array("0", "0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($spendingList) {
            $result['list'] = $spendingList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无优惠券类型信息", 'result' => $result);
        }

        return $res;
    }

    //新增优惠券申请类型
    function addcouponsapplytypeAction($paramArray)
    {
        $data = array();
        $data['applytype_cnname'] = $paramArray['applytype_cnname'];
        $data['applytype_branch'] = $paramArray['applytype_branch'];
        $data['applytype_playclass'] = $paramArray['applytype_playclass'];
        $data['applytype_minprice'] = $paramArray['applytype_minprice'];
        $data['applytype_isschool'] = $paramArray['applytype_isschool'];
        $data['couponsrules_id'] = $paramArray['couponsrules_id'];
        $data['company_id'] = $paramArray['company_id'];

        $data['applytype_limitterm'] = $paramArray['applytype_limitterm'];
        $data['applytype_limittype'] = $paramArray['applytype_limittype'];
        $data['applytype_validitytype'] = $paramArray['applytype_validitytype'];
        $data['applytype_validitydays'] = $paramArray['applytype_validitydays'];
        if (isset($paramArray['applytype_deadline']) && $paramArray['applytype_deadline'] != '') {
            $data['applytype_deadline'] = $paramArray['applytype_deadline'];
        }

        if (isset($paramArray['applytype_isgetdouble']) && $paramArray['applytype_isgetdouble'] != '') {
            $data['applytype_isgetdouble'] = $paramArray['applytype_isgetdouble'];
        }

        $data['applytype_manycourse'] = $paramArray['applytype_manycourse'];//组合课程是否适用  0-否 1-是 20230913
        $data['applytype_applymerge'] = $paramArray['applytype_applymerge'];//组合课程是否适用  0-否 1-是 20230913
        $data['applytype_ischannel'] = $paramArray['applytype_ischannel'];//适用渠道  0 全部  1 部分
        $data['applytype_getscope'] = $paramArray['applytype_getscope'];//领取范围
//        $data['applytype_applycoursecat'] = $paramArray['applytype_applycoursecat'];//适配班种

        $data['applytype_isneedcheck'] = $paramArray['applytype_isneedcheck'];
        $data['applytype_caneditcourse'] = $paramArray['applytype_caneditcourse'];
        $data['applytype_coexist'] = $paramArray['applytype_coexist'];
        if ($paramArray['applytype_isquota'] == '1') {
            $data['applytype_isquota'] = '1';
            $data['applytype_maxprice'] = $paramArray['applytype_maxprice'];
            $data['applytype_maxdiscount'] = $paramArray['applytype_maxdiscount'];
        } else {
            $data['applytype_isquota'] = '0';
        }

        $field = array();
        $field['applytype_cnname'] = "优惠券申请类型名称";
        $field['applytype_branch'] = "优惠券申请类型编号";
        $field['applytype_playclass'] = "优惠券使用类型";
        $field['applytype_minprice'] = "最低消费金额";
        $field['company_id'] = "所属公司";

        $spending_code = $this->DataControl->getFieldOne('smc_code_couponsapplytype', 'applytype_id', "applytype_branch = '{$paramArray['applytype_branch']}' and company_id = '{$paramArray['company_id']}'");
        if ($spending_code) {
            ajax_return(array('error' => 1, 'errortip' => "优惠券申请类型编号已存在!"));
        }

        $applytype_cnname = $this->DataControl->getFieldOne('smc_code_couponsapplytype', 'applytype_id', "applytype_cnname = '{$paramArray['applytype_cnname']}' and company_id = '{$paramArray['company_id']}'");
        if ($applytype_cnname) {
            ajax_return(array('error' => 1, 'errortip' => "优惠券申请类型名称已存在!"));
        }

        $applytype_id = $this->DataControl->insertData('smc_code_couponsapplytype', $data);

        if ($applytype_id) {

            $result = array();
            $result["field"] = $field;
            $result["data"] = $applytype_id;
            $res = array('error' => '0', 'errortip' => "添加优惠券申请类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '添加优惠券申请类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加优惠券申请类型失败', 'result' => $result);
        }
        return $res;
    }


    //是否启用优惠券类型
    function updateStatusAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_id", "applytype_id = '{$paramArray['applytype_id']}'");
        if ($productsOne) {
            $data = array();
            $data['applytype_status'] = $paramArray['applytype_status'];

            $field = array();
            $field['applytype_status'] = "是否启用";

            if ($this->DataControl->updateData("smc_code_couponsapplytype", "applytype_id = '{$paramArray['applytype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "财务相关设置->优惠券类型", '修改是否启用', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //编辑优惠券申请类型
    function updatecouponsapplytypeAction($paramArray)
    {
        $spendingOne = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_id", "applytype_id = '{$paramArray['applytype_id']}'");
        if ($spendingOne) {
            $data = array();
            $data['applytype_cnname'] = $paramArray['applytype_cnname'];
            $data['applytype_branch'] = $paramArray['applytype_branch'];
            $data['couponsrules_id'] = $paramArray['couponsrules_id'];
            $data['applytype_isschool'] = $paramArray['applytype_isschool'];
            $data['applytype_playclass'] = $paramArray['applytype_playclass'];

            $data['applytype_validitytype'] = $paramArray['applytype_validitytype'];
            $data['applytype_validitydays'] = $paramArray['applytype_validitydays'];
            if (isset($paramArray['applytype_deadline']) && $paramArray['applytype_deadline'] != '') {
                $data['applytype_deadline'] = $paramArray['applytype_deadline'];
            }

            $data['applytype_isneedcheck'] = $paramArray['applytype_isneedcheck'];
            $data['applytype_isquota'] = $paramArray['applytype_isquota'];

            $data['applytype_limitterm'] = $paramArray['applytype_limitterm'];

            $data['applytype_limittype'] = $paramArray['applytype_limittype'];

            if ($paramArray['applytype_limittype'] == 0) {
                $data['applytype_maxprice'] = $paramArray['applytype_maxprice'];
                $data['applytype_maxdiscount'] = 0;
            } elseif ($paramArray['applytype_limittype'] == 1) {
                $data['applytype_maxprice'] = 0;
                $data['applytype_maxdiscount'] = $paramArray['applytype_maxdiscount'];
            } else {
                $data['applytype_maxprice'] = $paramArray['applytype_maxprice'];
                $data['applytype_maxdiscount'] = $paramArray['applytype_maxdiscount'];
            }

            $data['applytype_minprice'] = $paramArray['applytype_minprice'];
            $data['applytype_applyschool'] = $paramArray['applytype_applyschool'];
            $data['applytype_caneditcourse'] = $paramArray['applytype_caneditcourse'];
            $data['applytype_coexist'] = $paramArray['applytype_coexist'];

            $data['applytype_manycourse'] = $paramArray['applytype_manycourse'];//组合课程是否适用  0-否 1-是 20230913
            $data['applytype_applymerge'] = $paramArray['applytype_applymerge'];//适配组合方式:   0全部适用 1部分适用 -1部分不适用
            $data['applytype_ischannel'] = $paramArray['applytype_ischannel'];//适用渠道  0 全部  1 部分
            $data['applytype_getscope'] = $paramArray['applytype_getscope'];//领取范围
//            $data['applytype_applycoursecat'] = $paramArray['applytype_applycoursecat'];//适配班种

            $field = array();
            $field['applytype_cnname'] = "优惠券申请类型名称";
            $field['applytype_branch'] = "优惠券申请类型编号";
            $field['applytype_playclass'] = "优惠券使用类型";
            $field['applytype_minprice'] = "最低消费金额";

            $spending_code = $this->DataControl->getFieldOne('smc_code_couponsapplytype', 'applytype_branch', "applytype_id = '{$paramArray['applytype_id']}'");
            if ($paramArray['applytype_branch'] != $spending_code['applytype_branch']) {
                $spending_code = $this->DataControl->getFieldOne('smc_code_couponsapplytype', 'applytype_id', "applytype_branch = '{$paramArray['applytype_branch']}' and company_id = '{$paramArray['company_id']}'");
                if ($spending_code) {
                    ajax_return(array('error' => 1, 'errortip' => "优惠券申请类型已存在!"));
                }
            }

            $applytype_cnname = $this->DataControl->getFieldOne('smc_code_couponsapplytype', 'applytype_cnname', "applytype_id = '{$paramArray['applytype_id']}'");
            if ($paramArray['applytype_cnname'] != $applytype_cnname['applytype_cnname']) {
                $applytype_cnname = $this->DataControl->getFieldOne('smc_code_couponsapplytype', 'applytype_id', "applytype_cnname = '{$paramArray['applytype_cnname']}' and company_id = '{$paramArray['company_id']}'");
                if ($applytype_cnname) {
                    ajax_return(array('error' => 1, 'errortip' => "优惠券申请名称已存在!"));
                }
            }

            if ($this->DataControl->updateData("smc_code_couponsapplytype", "applytype_id = '{$paramArray['applytype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "优惠券申请类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '修改优惠券申请类型', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '优惠券申请类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除优惠券申请类型
    function delcouponsapplytypeAction($paramArray)
    {
        $SpendingOne = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_id,applytype_branch", "applytype_id = '{$paramArray['applytype_id']}'");
        if ($SpendingOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            $a = $this->DataControl->getFieldOne("smc_student_coupons_apply", "apply_id", "applytype_branch = '{$SpendingOne['applytype_branch']}'");
            if ($a) {
                ajax_return(array('error' => 1, 'errortip' => "该类型已被使用，无法删除！"));
            }
            if ($this->DataControl->delData("smc_code_couponsapplytype", "applytype_id = '{$paramArray['applytype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除优惠券申请类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '删除优惠券申请类型', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除优惠券申请类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function couponsapplytypeList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (cr.applytype_cnname like '%{$request['keyword']}%' or cr.applytype_branch like '%{$request['keyword']}%')";
        }
        if (isset($request['starttime']) && $request['starttime'] !== '' && isset($request['endtime']) && $request['endtime'] !== '') {
            $datawhere .= " and ((cr.applytype_startday>='{$request['starttime']}' and cr.applytype_startday<='{$request['endtime']}') or (cr.applytype_endday>='{$request['starttime']}' and cr.applytype_endday<='{$request['endtime']}'))";
        }

        if (isset($request['applytype_getscope']) && $request['applytype_getscope'] !== '') {
            $datawhere .= " and cr.applytype_getscope = '{$request['applytype_getscope']}'";
        }

        if (isset($request['applytype_playclass']) && $request['applytype_playclass'] !== '') {
            $datawhere .= " and cr.applytype_playclass = '{$request['applytype_playclass']}'";
        }

        if (isset($request['applytype_buildtype']) && $request['applytype_buildtype'] !== '') {
            $datawhere .= " and cr.applytype_buildtype = '{$request['applytype_buildtype']}'";
        }

        if (isset($request['applytype_getscope']) && $request['applytype_getscope'] !== '') {
            $datawhere .= " and cr.applytype_getscope = '{$request['applytype_getscope']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cr.applytype_id,cr.applytype_cnname,cr.applytype_branch,cr.applytype_playclass,cr.applytype_startday,cr.applytype_endday,cr.applytype_type,cr.applytype_price,cr.applytype_discount,cr.applytype_getscope,cr.applytype_minprice,cr.applytype_play,cr.applytype_get,cr.applytype_remark,cr.applytype_applyschool,cr.applytype_applycourse,cr.applytype_buildtype,cr.applytype_validitytype,cr.applytype_validitydays,cr.applytype_deadline,cr.applytype_coexist,cr.applytype_isgetdouble,cr.applytype_caneditcourse,cr.applytype_manycourse,cr.jointcourse_id,cr.applytype_applymerge,cr.applytype_applycoursecat,cr.applytype_ischannel 
              ,(select (count(cc.coursecatapply_id)) from smc_couponsapplytype_coursecatapply as cc where cc.applytype_id=cr.applytype_id) as courseNum
              ,(select (count(cc.courseapply_id)) from smc_couponsapplytype_courseapply as cc where cc.applytype_id=cr.applytype_id) as applycourseNum
              ,(select (count(cc.coursepacks_id)) from smc_couponsapplytype_coursepacksapply as cc where cc.applytype_id=cr.applytype_id) as coursepacksNum
              ,(select (count(cs.schoolapply_id)) from smc_couponsapplytype_schoolapply as cs where cs.applytype_id=cr.applytype_id) as schoolNum
              ,(select ca.apply_id from smc_student_coupons_apply as ca where ca.applytype_branch=cr.applytype_branch and ca.company_id=cr.company_id limit 0,1) as apply_id
              from smc_code_couponsapplytype as cr
              where {$datawhere} and cr.company_id='{$this->company_id}' and cr.applytype_port=1 and cr.applytype_status=1
              order by cr.applytype_id desc
              ";
        $type = $this->LgArraySwitch(array("0" => "减价", "1" => "折扣"));
        $playclass = $this->LgArraySwitch(array("0" => "项目优惠券", "1" => "订单优惠券"));
        $class = $this->LgArraySwitch(array("0" => "不限", "1" => "新学员", "2" => "老学员", "3" => "标签学员", "4" => "班组新生"));
        $build = $this->LgArraySwitch(array("0" => "集团创建", "1" => "系统创建"));
        $can = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        $coexist = $this->LgArraySwitch(array("0" => "否", "1" => "是", "2" => "豁免"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['applytype_cnname'] = $dateexcelvar['applytype_cnname'];
                $datearray['applytype_branch'] = $dateexcelvar['applytype_branch'];
                $datearray['applytype_playclass_name'] = $playclass[$dateexcelvar['applytype_playclass']];
                $datearray['effecttime'] = $dateexcelvar['applytype_startday'] . '至' . $dateexcelvar['applytype_endday'];
                $datearray['applytype_coexist_name'] = $coexist[$dateexcelvar['applytype_coexist']];
                $datearray['applytype_type_name'] = $type[$dateexcelvar['breakoff_type']];
                $datearray['applytype_price'] = $dateexcelvar['applytype_price'] ? $dateexcelvar['applytype_price'] : '--';
                $datearray['applytype_discount'] = $dateexcelvar['applytype_discount'] ? 10 * $dateexcelvar['applytype_discount'] . '折' : '--';
                $datearray['applytype_getscope_name'] = $class[$dateexcelvar['applytype_getscope']];

                if ($dateexcelvar['applytype_applyschool'] == 0) {
                    $datearray['applytype_applyschool_name'] = '全部';
                } else {
                    $datearray['applytype_applyschool_name'] = $dateexcelvar['schoolNum'];
                }

                if ($dateexcelvar['applytype_applycoursecat'] == 0) {
                    $datearray['applytype_applycourse_name'] = '全部';
                } else {
                    $datearray['applytype_applycourse_name'] = $dateexcelvar['courseNum'];
                }

                if ($dateexcelvar['applytype_applycourse'] == 0) {
                    $datearray['applytype_course_name'] = '全部';
                } else {
                    $datearray['applytype_course_name'] = $dateexcelvar['applycourseNum'];
                }

                if($dateexcelvar['applytype_manycourse']==1){
                    if ($dateexcelvar['applytype_applymerge'] == 0) {
                        $datearray['coursepacksNum_name'] = '全部';
                    } else {
                        $datearray['coursepacksNum_name'] = $dateexcelvar['coursepacksNum'];
                    }
                }else{
                    $datearray['coursepacksNum_name']='--';
                }

                if ($dateexcelvar['applytype_minprice'] > 0) {
                    $datearray['is_limitPrice'] = '是';
                } else {
                    $datearray['is_limitPrice'] = '否';
                }

                $datearray['applytype_minprice'] = $dateexcelvar['applytype_minprice'];
                $datearray['applytype_play'] = $dateexcelvar['applytype_play'];
                $datearray['applytype_get'] = $dateexcelvar['applytype_get'];
                $datearray['applytype_remark'] = $dateexcelvar['applytype_remark'];

                $outexceldate[] = $datearray;
            }

            $excelheader = $this->LgArraySwitch(array("优惠券类型名称", "优惠券类型编号", "优惠券使用类型", "优惠券生效期", "是否和其他优惠券共用", "使用方式", "优惠金额", "优惠折扣", "适用学员", "适用学校", "适用班种", "适用课程", "适用组合课程", "是否限制最低消费金额", "最低消费金额", "适用规则", "领取规则", "备注"));
            $excelfileds = array('applytype_cnname', 'applytype_branch', 'applytype_playclass_name', 'effecttime', 'applytype_coexist_name', 'applytype_type_name', 'applytype_price', 'applytype_discount', 'applytype_getscope_name', 'applytype_applyschool_name', 'applytype_applycourse_name', 'applytype_course_name', 'coursepacksNum_name', 'is_limitPrice', 'applytype_minprice', 'applytype_play', 'applytype_get', 'applytype_note');

            $tem_name = "领取优惠券列表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $typeList = $this->DataControl->selectClear($sql);

            if (!$typeList) {
                $this->error = true;
                $this->errortip = "无领取优惠券数据";
                return false;
            }

            foreach ($typeList as &$typeOne) {
                $typeOne['jointcourse_id'] = $typeOne['jointcourse_id']==0?'':$typeOne['jointcourse_id'];

                $typeOne['applytype_type_name'] = $type[$typeOne['applytype_type']];
                $typeOne['applytype_getscope_name'] = $class[$typeOne['applytype_getscope']];
                $typeOne['applytype_playclass_name'] = $playclass[$typeOne['applytype_playclass']];
                $typeOne['applytype_buildtype_name'] = $build[$typeOne['applytype_buildtype']];
                $typeOne['applytype_coexist_name'] = $coexist[$typeOne['applytype_coexist']];

                $typeOne['applytype_price'] = $typeOne['applytype_price'] ? $typeOne['applytype_price'] : '--';
                $typeOne['applytype_discount'] = $typeOne['applytype_discount'] ? 10 * $typeOne['applytype_discount'] . '折' : '--';
                $typeOne['effecttime'] = $typeOne['applytype_startday'] . '至' . $typeOne['applytype_endday'];
                if ($typeOne['applytype_minprice'] > 0) {
                    $typeOne['is_limitPrice'] = '是';
                } else {
                    $typeOne['is_limitPrice'] = '否';
                }

                $typeOne['is_manycourse'] = $typeOne['applytype_manycourse'] == '1' ? '是' : '否';//组合课程是否适用  0-否 1-是

                if ($typeOne['applytype_validitytype'] == 0) {
                    $typeOne['applytype_eftime'] = '领取后' . $typeOne['applytype_validitydays'] . '天';
                } else {
                    $typeOne['applytype_eftime'] = '截止至' . $typeOne['applytype_deadline'];
                }

                if ($typeOne['applytype_applyschool'] == 0) {
                    $typeOne['applytype_applyschool_name'] = '全部';
                } else {
                    $typeOne['applytype_applyschool_name'] = $typeOne['schoolNum'];
                }
                $typeOne['applytype_isgetdouble_name'] = $can[$typeOne['applytype_isgetdouble']];
                $typeOne['applytype_caneditcourse_name'] = $can[$typeOne['applytype_caneditcourse']];

                if ($typeOne['applytype_applycoursecat'] == 0) {
                    $typeOne['applytype_applycourse_name'] = '全部';
                } else {
                    $typeOne['applytype_applycourse_name'] = $typeOne['courseNum'];
                }

                if ($typeOne['applytype_applycourse'] == 0) {
                    $typeOne['applytype_course_name'] = '全部';
                } else {
                    $typeOne['applytype_course_name'] = $typeOne['applycourseNum'];
                }

                if($typeOne['applytype_manycourse']==1){
                    if ($typeOne['applytype_applymerge'] == 0) {
                        $typeOne['coursepacksNum_name'] = '全部';
                    } else {
                        $typeOne['coursepacksNum_name'] = $typeOne['coursepacksNum'];
                    }
                }else{
                    $typeOne['coursepacksNum_name']='--';
                }


                if ($typeOne['applytype_applycoursecat'] == 1) {
                    $sql = "select coursecat_id from smc_couponsapplytype_coursecatapply where applytype_id='{$typeOne['applytype_id']}'";

                    $courseList = $this->DataControl->selectClear($sql);
                    if ($courseList) {
                        $typeOne['coursecatStr'] = json_encode($courseList);
                    } else {
                        $typeOne['coursecatStr'] = '';
                    }
                } else {
                    $typeOne['coursecatStr'] = '';
                }
            }

            $data = array();

            $count_sql = "select cr.applytype_id
              from smc_code_couponsapplytype as cr
              where {$datawhere} and cr.company_id='{$this->company_id}' and cr.applytype_port=1 and cr.applytype_status=1
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);
            $allnum = $db_nums ? count($db_nums) : 0;
            $data['allnum'] = $allnum;
            $data['list'] = $typeList;
            return $data;
        }
    }

    function addApplySchool($request)
    {

        if (!isset($request['school_json']) || $request['school_json'] == '') {
            $this->error = true;
            $this->errortip = "请选择学校";
            return false;
        }

//        $sql = "select ca.apply_id from smc_student_coupons_apply as ca
//              left join smc_code_couponsapplytype as cs on cs.applytype_branch=ca.applytype_branch and cs.company_id='{$this->company_id}'
//              where cs.applytype_id='{$request['applytype_id']}' limit 0,1";
//        if ($this->DataControl->selectOne($sql)) {
//            $this->error = true;
//            $this->errortip = "已被使用,不可适配";
//            return false;
//        }

        $list = json_decode(stripslashes($request['school_json']), 1);

        foreach ($list as $val) {
            if (!$this->DataControl->getFieldOne("smc_couponsapplytype_schoolapply", "schoolapply_id", "school_id='{$val}' and applytype_id='{$request['applytype_id']}'")) {

                $data = array();
                $data['school_id'] = $val;
                $data['applytype_id'] = $request['applytype_id'];

                $this->DataControl->insertData("smc_couponsapplytype_schoolapply", $data);
            }
        }

        $this->error = true;
        $this->oktip = "添加成功";
        return true;
    }

    function delApplySchool($request)
    {

        $sql = "select ca.apply_id from smc_student_coupons_apply as ca 
              left join smc_code_couponsapplytype as cs on cs.applytype_branch=ca.applytype_branch and cs.company_id='{$this->company_id}'
              where cs.applytype_id='{$request['applytype_id']}' limit 0,1";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "已被使用,不可移除";
            return false;
        }

        $school_list = json_decode(stripslashes($request['schoolapply_id']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $schoolapply_id = implode(',', $str);
            $where = "schoolapply_id in ({$schoolapply_id})";
        }else{
            $where = "schoolapply_id = '{$request['schoolapply_id']}'";
        }

        if ($this->DataControl->delData("smc_couponsapplytype_schoolapply", $where)) {
            $this->error = true;
            $this->oktip = "移除成功";
            return true;
        } else {
            $this->error = true;
            $this->errortip = "移除失败";
            return false;
        }
    }

    function allSchoolList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_branch like '%{$request['keyword']}%' or sc.school_enname like '%{$request['keyword']}%' or sc.school_shortname like '%{$request['keyword']}%')";
        }

        if ($request['district_id'] && $request['district_id'] != '') {
            $datawhere .= " and sc.district_id='{$request['district_id']}'";
        }

        if ($request['province_id'] && $request['province_id'] != '') {
            $datawhere .= " and sc.school_province='{$request['province_id']}'";
        }

        if ($request['city_id'] && $request['city_id'] != '') {
            $datawhere .= " and sc.school_city='{$request['city_id']}'";
        }

        if ($request['area_id'] && $request['area_id'] != '') {
            $datawhere .= " and sc.school_area='{$request['area_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sc.school_id,sc.school_cnname,sc.school_enname,sc.school_shortname,sc.school_branch,ifnull(cd.district_cnname,'') as district_cnname
              from smc_school as sc
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}' and sc.school_isclose=0
              and sc.school_id not in (select cs.school_id from smc_couponsapplytype_schoolapply as cs where cs.applytype_id='{$request['applytype_id']}') 
              ORDER BY sc.school_sort DESC
              LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $courseList = array();
        }
        $data = array();
        $count_sql = "select sc.school_id
              from smc_school as sc
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}' and sc.school_isclose=0
              and sc.school_id not in (select cs.school_id from smc_couponsapplytype_schoolapply as cs where cs.applytype_id='{$request['applytype_id']}') 
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $sql = "select cd.district_id,cd.district_cnname
              from smc_school as sc
              inner join gmc_company_district as cd on cd.district_id=sc.district_id
              where sc.company_id='{$this->company_id}' and sc.school_isclose=0
              group by cd.district_id
              order by cd.district_id asc
              ";

        $districtList = $this->DataControl->selectClear($sql);
        if (!$districtList) {
            $districtList = array();
        }

        $data['allnum'] = $allnum;
        $data['list'] = $courseList;
        $data['districtList'] = $districtList;

        return $data;
    }

    function applytypeSchoolList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' or sc.school_branch like '%{$request['keyword']}%' or sc.school_enname like '%{$request['keyword']}%' or sc.school_shortname like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cs.schoolapply_id,sc.school_id,sc.school_cnname,sc.school_enname,sc.school_shortname,sc.school_branch,ifnull(cd.district_cnname,'') as district_cnname
              from smc_couponsapplytype_schoolapply as cs
              inner join smc_school as sc on sc.school_id=cs.school_id
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and cs.applytype_id='{$request['applytype_id']}' and sc.company_id='{$this->company_id}'
              ORDER BY sc.school_sort DESC
              LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $this->error = true;
            $this->errortip = "暂无适用学校消息，点击右上角即可添加哦~";
            return false;
        }
        $data = array();
        $count_sql = "select sc.school_id
              from smc_couponsapplytype_schoolapply as cs
              inner join smc_school as sc on sc.school_id=cs.school_id
              where {$datawhere} and cs.applytype_id='{$request['applytype_id']}' and sc.company_id='{$this->company_id}'
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $courseList;

        return $data;
    }

    function applytypeCoursepacksList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.coursepacks_name like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select b.coursepacks_id,b.coursepacks_name,b.coursepacks_startday,b.coursepacks_endday
                ,ifnull((select count(distinct x.course_id) from smc_fee_warehouse_courses as x where x.coursepacks_id=a.coursepacks_id),0) as courseNum
                ,ifnull((select sum(x.tuition_sellingprice) from smc_fee_warehouse_courses as x where x.coursepacks_id=a.coursepacks_id),0) as sellingprice
              from smc_couponsapplytype_coursepacksapply as a
              inner join smc_fee_warehouse_coursepacks as b on a.coursepacks_id=b.coursepacks_id
              where {$datawhere} and a.applytype_id='{$request['applytype_id']}' 
              LIMIT {$pagestart},{$num}";

        $coursepacksList = $this->DataControl->selectClear($sql);

        if (!$coursepacksList) {
            $this->error = true;
            $this->errortip = "暂无适用组合课程，点击右上角即可添加哦~";
            return false;
        }

        foreach($coursepacksList as &$coursepacksOne){
            $coursepacksOne['effectetime']=($coursepacksOne['coursepacks_startday']==''?'--':$coursepacksOne['coursepacks_startday']).'至'.($coursepacksOne['coursepacks_endday']==''?'--':$coursepacksOne['coursepacks_endday']);
        }

        $data = array();
        $count_sql = "select a.coursepacks_id
              from smc_couponsapplytype_coursepacksapply as a
              inner join smc_fee_warehouse_coursepacks as b on a.coursepacks_id=b.coursepacks_id
              where {$datawhere} and a.applytype_id='{$request['applytype_id']}'
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $coursepacksList;

        return $data;
    }



    function addApplyCoursepacks($request)
    {

        if (!isset($request['coursepacks_json']) || $request['coursepacks_json'] == '') {
            $this->error = true;
            $this->errortip = "请选择组合课程";
            return false;
        }

        $list = json_decode(stripslashes($request['coursepacks_json']), 1);

        foreach ($list as $val) {
            if (!$this->DataControl->getFieldOne("smc_couponsapplytype_coursepacksapply", "applytype_id", "coursepacks_id='{$val}' and applytype_id='{$request['applytype_id']}'")) {

                $data = array();
                $data['coursepacks_id'] = $val;
                $data['applytype_id'] = $request['applytype_id'];

                $this->DataControl->insertData("smc_couponsapplytype_coursepacksapply", $data);
            }
        }

        $this->error = true;
        $this->oktip = "添加成功";
        return true;
    }

    function delApplyCoursepacks($request)
    {

        if(!isset($request['applytype_id']) || $request['applytype_id']==''){
            $this->error = true;
            $this->oktip = "请选择优惠券";
            return true;
        }

        if(!isset($request['coursepacks_id']) || $request['coursepacks_id']==''){
            $this->error = true;
            $this->oktip = "请选择组合课程";
            return true;
        }

        $sql = "select ca.apply_id 
                from smc_student_coupons_apply as ca 
                left join smc_code_couponsapplytype as cs on cs.applytype_branch=ca.applytype_branch and cs.company_id='{$this->company_id}'
              where cs.applytype_id='{$request['applytype_id']}' limit 0,1";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "已被使用,不可移除";
            return false;
        }

        if ($this->DataControl->delData("smc_couponsapplytype_coursepacksapply", "applytype_id='{$request['applytype_id']}' and coursepacks_id='{$request['coursepacks_id']}'")) {
            $this->error = true;
            $this->oktip = "移除成功";
            return true;
        } else {
            $this->error = true;
            $this->errortip = "移除失败";
            return false;
        }
    }

    function applytypeCourseList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.coursecat_cnname like '%{$request['keyword']}%' or b.coursecat_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and a.coursecat_id = '{$request['coursecat_id']}'";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and b.coursetype_id = '{$request['coursetype_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.coursecat_id,b.coursecat_cnname,b.coursecat_branch,c.coursetype_cnname,c.coursetype_branch
              from smc_couponsapplytype_coursecatapply as a
              left join smc_code_coursecat as b on a.coursecat_id=b.coursecat_id
              left join smc_code_coursetype as c on c.coursetype_id=b.coursetype_id
              where {$datawhere} and a.applytype_id='{$request['applytype_id']}'
              LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $courseList = array();
        }
        $data = array();
        $count_sql = "select a.coursecat_id
              from smc_couponsapplytype_coursecatapply as a
              left join smc_code_coursecat as b on a.coursecat_id=b.coursecat_id
              left join smc_code_coursetype as c on c.coursetype_id=b.coursetype_id
              where {$datawhere} and a.applytype_id='{$request['applytype_id']}'";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $courseList;

        return $data;

    }

    function getCoursepacksList($request)
    {
        $datawhere = " c.company_id='{$request['company_id']}' ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.coursepacks_name like '%{$request['keyword']}%' )";
        }

        if(isset($request['applytype_id']) && $request['applytype_id'] !== ''){
            $datawhere .= " and not exists(select 1 from smc_couponsapplytype_coursepacksapply as x where x.coursepacks_id=a.coursepacks_id and x.applytype_id='{$request['applytype_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.coursepacks_id,a.coursepacks_name,a.coursepacks_startday,a.coursepacks_endday
                from smc_fee_warehouse_coursepacks as a
                left join smc_fee_warehouse as b on b.warehouse_id=a.warehouse_id
                left join smc_fee_agreement as c on c.agreement_id=b.agreement_id
                where {$datawhere} and (b.warehouse_startday<=curdate() or b.warehouse_startday='' ) and (b.warehouse_endday>=curdate() or b.warehouse_endday='' ) and (a.coursepacks_startday<=curdate() or a.coursepacks_startday='' ) and (a.coursepacks_endday>=curdate() or a.coursepacks_endday='' )
                and c.agreement_status=1
              LIMIT {$pagestart},{$num}";

        $coursepacksList = $this->DataControl->selectClear($sql);

        if (!$coursepacksList) {
            $this->error = true;
            $this->errortip = "无可用组合课程";
            return false;
        }

        $data = array();
        $count_sql = "select a.coursepacks_id
              from smc_fee_warehouse_coursepacks as a
                left join smc_fee_warehouse as b on b.warehouse_id=a.warehouse_id
                left join smc_fee_agreement as c on c.agreement_id=b.agreement_id
                where {$datawhere} and (b.warehouse_startday<=curdate() or b.warehouse_startday='' ) and (b.warehouse_endday>=curdate() or b.warehouse_endday='' ) and (a.coursepacks_startday<=curdate() or a.coursepacks_startday='' ) and (a.coursepacks_endday>=curdate() or a.coursepacks_endday='' )
                and c.agreement_status=1";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $coursepacksList;

        return $data;

    }

    //适配渠道
    function addApplyChannel($request)
    {

        if (!isset($request['channel_json']) || $request['channel_json'] == '') {
            $this->error = true;
            $this->errortip = "请选择渠道";
            return false;
        }

        $list = json_decode(stripslashes($request['channel_json']), 1);

        foreach ($list as $val) {
            if (!$this->DataControl->getFieldOne("smc_couponsapplytype_channelapply", "channelapply_id", "channel_id='{$val}' and applytype_id='{$request['applytype_id']}'")) {
                $data = array();
                $data['channel_id'] = $val;
                $data['applytype_id'] = $request['applytype_id'];
                $this->DataControl->insertData("smc_couponsapplytype_channelapply", $data);
            }
        }

        $this->error = true;
        $this->oktip = "添加成功";
        return true;
    }
    //删除渠道
    function delApplychannel($request)
    {

        $sql = "select ca.apply_id from smc_student_coupons_apply as ca 
              left join smc_code_couponsapplytype as cs on cs.applytype_branch=ca.applytype_branch and cs.company_id='{$this->company_id}'
              where cs.applytype_id='{$request['applytype_id']}' limit 0,1";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "已被使用,不可移除";
            return false;
        }

        $school_list = json_decode(stripslashes($request['channelapply_id']), true);
        if(is_array($school_list)){
            $str = array();
            foreach ($school_list as $val) {
                $str[] = $val;
            }
            $channelapply_id = implode(',', $str);
            $where = "channelapply_id in ({$channelapply_id})";
        }else{
            $where = "channelapply_id = '{$request['channelapply_id']}'";
        }

        if ($this->DataControl->delData("smc_couponsapplytype_channelapply", $where)) {
            $this->error = true;
            $this->oktip = "移除成功";
            return true;
        } else {
            $this->error = true;
            $this->errortip = "移除失败";
            return false;
        }
    }

    //获取全部渠道
    function allchannelList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.channel_name like '%{$request['keyword']}%' )";
        }

        if ($request['channel_medianame'] && $request['channel_medianame'] != '') {
            $datawhere .= " and sc.channel_medianame='{$request['channel_medianame']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sc.channel_id,sc.channel_name,sc.channel_medianame 
              from crm_code_channel as sc 
              where {$datawhere} and sc.company_id='{$this->company_id}' 
              and sc.channel_id not in (select cs.channel_id from smc_couponsapplytype_channelapply as cs where cs.applytype_id='{$request['applytype_id']}') 
              ORDER BY sc.channel_id DESC
              LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $courseList = array();
        }
        $data = array();
        $count_sql = "select sc.channel_id
              from crm_code_channel as sc 
              where {$datawhere} and sc.company_id='{$this->company_id}' 
              and sc.channel_id not in (select cs.channel_id from smc_couponsapplytype_channelapply as cs where cs.applytype_id='{$request['applytype_id']}') 
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $sql = "select sc.channel_medianame 
              from crm_code_channel as sc  
              where sc.company_id='{$this->company_id}' 
              group by sc.channel_medianame 
              ";

        $frommediaList = $this->DataControl->selectClear($sql);
        if (!$frommediaList) {
            $frommediaList = array();
        }

        $data['allnum'] = $allnum;
        $data['list'] = $courseList;
        $data['frommediaList'] = $frommediaList;

        return $data;
    }

    //获取适配的渠道数据
    function applytypeChannelList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.channel_name like '%{$request['keyword']}%' )";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sc.channel_id,sc.channel_name,sc.channel_medianame,cs.channelapply_id 
              from smc_couponsapplytype_channelapply as cs
              inner join crm_code_channel as sc on sc.channel_id=cs.channel_id  
              where {$datawhere} and cs.applytype_id='{$request['applytype_id']}' and sc.company_id='{$this->company_id}'
              ORDER BY sc.channel_id DESC
              LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $this->error = true;
            $this->errortip = "暂无适用学校消息，点击右上角即可添加哦~";
            return false;
        }
        $data = array();
        $count_sql = "select sc.channel_id
              from smc_couponsapplytype_channelapply as cs
              inner join crm_code_channel as sc on sc.channel_id=cs.channel_id  
              where {$datawhere} and cs.applytype_id='{$request['applytype_id']}' and sc.company_id='{$this->company_id}'
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $courseList;

        return $data;
    }

    //新增及编辑领取优惠券
    function editapplytype($request)
    {
        $data = array();
        $data['applytype_cnname'] = $request['applytype_cnname'];
        $data['applytype_branch'] = $request['applytype_branch'];
        $data['applytype_playclass'] = $request['applytype_playclass'];
        $data['applytype_startday'] = $request['applytype_startday'];
        $data['applytype_endday'] = $request['applytype_endday'];
        $data['applytype_type'] = $request['applytype_type'];
        $data['applytype_price'] = $request['applytype_price'];
        $data['applytype_discount'] = $request['applytype_discount'] > 0 ? $request['applytype_discount'] / 10 : 0;
        $data['applytype_getscope'] = $request['applytype_getscope'];//领取范围
        $data['applytype_minprice'] = $request['applytype_minprice'];
        $data['applytype_play'] = $request['applytype_play'];
        $data['applytype_get'] = $request['applytype_get'];
        $data['applytype_remark'] = $request['applytype_remark'];
        $data['applytype_validitytype'] = $request['applytype_validitytype'];
        $data['applytype_validitydays'] = $request['applytype_validitydays'];
        $data['applytype_deadline'] = $request['applytype_deadline'];
        $data['applytype_applycoursecat'] = $request['applytype_applycoursecat'];
        $data['applytype_applycourse'] = $request['applytype_applycourse'];
        $data['applytype_applyschool'] = $request['applytype_applyschool'];
        $data['applytype_coexist'] = $request['applytype_coexist'];
        $data['applytype_caneditcourse'] = $request['applytype_caneditcourse'];
        $data['applytype_isgetdouble'] = isset($request['applytype_isgetdouble'])?$request['applytype_isgetdouble']:0;

        $data['applytype_manycourse'] = $request['applytype_manycourse'];
        $data['applytype_applymerge'] = $request['applytype_applymerge'];
        $data['applytype_ischannel'] = $request['applytype_ischannel'];//适用渠道  0 全部  1 部分

        // if($request['jointcourse_id']) {
        //     $data['jointcourse_id'] = $request['jointcourse_id'];//适配销售组合
        //     $data['applytype_applycoursecat'] = 1;//适配销售组合
        // }else{
        //    if($request['applytype_applycoursecat'] && $request['applytype_applycoursecat']!=0 && (!isset($request['coursecatStr']) || $request['coursecatStr'])==''){
        //        $this->error = true;
        //        $this->errortip = "请选择适配班种";
        //        return false;
        //    }
        // }

        if($request['applytype_applycoursecat'] && $request['applytype_applycoursecat']!=0 && (!isset($request['coursecatStr']) || $request['coursecatStr'])==''){
            $this->error = true;
            $this->errortip = "请选择适配班种";
            return false;
        }

        
        if ($request['applytype_startday'] > $request['applytype_endday']) {
            $this->error = true;
            $this->errortip = "开始时间不可大于结束时间";
            return false;
        }

        if (isset($request['applytype_id']) && $request['applytype_id'] > 0) {
            $couponsOne = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_buildtype", "applytype_id='{$request['applytype_id']}'");
            if ($couponsOne['applytype_buildtype'] == 1) {
                $this->error = true;
                $this->errortip = "系统创建,不可编辑";
                return false;
            }

            $sql = "select ca.apply_id from smc_student_coupons_apply as ca 
              left join smc_code_couponsapplytype as cs on cs.applytype_branch=ca.applytype_branch and cs.company_id='{$this->company_id}'
              where cs.applytype_id='{$request['applytype_id']}' limit 0,1";
            if ($this->DataControl->selectOne($sql)) {
                $this->error = true;
                $this->errortip = "已被使用,不可编辑";
                return false;
            }

            if ($this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_id", "company_id='{$this->company_id}' and applytype_branch='{$request['applytype_branch']}' and applytype_id<>'{$request['applytype_id']}' and applytype_status>=0")) {
                $this->error = true;
                $this->errortip = "编号已存在,编辑失败";
                return false;
            }

            if ($this->DataControl->updateData("smc_code_couponsapplytype", "applytype_id='{$request['applytype_id']}'", $data)) {

                //是否存在  适配销售组合
                // if($request['jointcourse_id']>1){

                //     $courseList = $this->DataControl->selectClear(" select b.coursecat_id from smc_code_jointcourse_applys as a,smc_course as b where a.course_id=b.course_id and jointcourse_id = '{$request['jointcourse_id']}' 
                //         group by b.coursecat_id
                //     ");

                //     if($courseList){
                //         $coursecatList=array_column($courseList,'coursecat_id');
                //     }

                // }else {
                //     $coursecatList = array_column(json_decode(stripslashes($request['coursecatStr']), 1),'coursecat_id');
                // }

                $coursecatList = array_column(json_decode(stripslashes($request['coursecatStr']), 1),'coursecat_id');


                if($coursecatList){
                    $applyCoursecatList = $this->DataControl->selectClear("select coursecat_id from smc_couponsapplytype_coursecatapply where applytype_id='{$request['applytype_id']}'");

                    $applyCoursecatList=$applyCoursecatList?array_column($applyCoursecatList,'coursecat_id'):array();

                    $addArray=array_diff($coursecatList,$applyCoursecatList);
                    $delArray=array_diff($applyCoursecatList,$coursecatList);

                    if($addArray){
                        foreach($addArray as $addOne){
                            $data=array();
                            $data['applytype_id']=$request['applytype_id'];
                            $data['coursecat_id']=$addOne;
                            $this->DataControl->insertData("smc_couponsapplytype_coursecatapply",$data);
                        }
                    }

                    if($delArray){
                        foreach($delArray as $delOne){
                            $this->DataControl->delData("smc_couponsapplytype_coursecatapply","applytype_id='{$request['applytype_id']}' and coursecat_id='{$delOne}'");
                        }
                    }
                }

                // if(isset($request['applytype_applycourse']) && $request['applytype_applycourse']!='' && $request['courseStr']!=''){
                    
                //     if($request['applytype_applycourse']!=0){
                        
                //         $courseList = array_column(json_decode(stripslashes($request['courseStr']), 1),'course_id');

                //         $applyCourseList = $this->DataControl->selectClear("select course_id from smc_couponsapplytype_courseapply where applytype_id='{$request['applytype_id']}'");

                //         $applyCourseList=$applyCourseList?array_column($applyCourseList,'course_id'):array();

                //         $addArray=array_diff($courseList,$applyCourseList);
                //         $delArray=array_diff($applyCourseList,$courseList);

                //         if($addArray){
                //             foreach($addArray as $addOne){
                //                 $data=array();
                //                 $data['applytype_id']=$request['applytype_id'];
                //                 $data['course_id']=$addOne;
                //                 $this->DataControl->insertData("smc_couponsapplytype_courseapply",$data);
                //             }
                //         }

                //         if($delArray){
                //             foreach($delArray as $delOne){
                //                 $this->DataControl->delData("smc_couponsapplytype_courseapply","applytype_id='{$request['applytype_id']}' and course_id='{$delOne}'");
                //             }
                //         }
                //     }
                // }
                


//                $mergeList = array_column(json_decode(stripslashes($request['mergeStr']), 1),'coursepacks_id');
//
//                if($mergeList){
//                    $this->DataControl->delData("smc_couponsapplytype_coursepacksapply","applytype_id='{$request['applytype_id']}'");
//
//                    foreach($mergeList as $mergeOne){
//                        $data=array();
//                        $data['applytype_id']=$request['applytype_id'];
//                        $data['coursepacks_id']=$mergeOne;
//                        $this->DataControl->insertData("smc_couponsapplytype_coursepacksapply",$data);
//                    }
//
//                }


//                if ($courseList != $applyCourseList) {
//                    if ($courseList) {
//                        $this->DataControl->delData("", "applytype_id='{$request['applytype_id']}'");
//                        foreach ($courseList as $courseOne) {
//                            $tem_data = array();
//                            $tem_data['course_id'] = $courseOne['course_id'];
//                            $tem_data['applytype_id'] = $request['applytype_id'];
//                            $this->DataControl->insertData("", $tem_data);
//                        }
//                    }
//                }
                $this->oktip = "编辑成功";
                return $request['applytype_id'];
            } else {
                $this->error = true;
                $this->errortip = "编辑失败";
                return false;
            }
        } else {
            if ($this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_id", "company_id='{$this->company_id}' and applytype_branch='{$request['applytype_branch']}' and applytype_status>=0")) {
                $this->error = true;
                $this->errortip = "编号已存在,添加失败";
                return false;
            }

            $data['applytype_port'] = 1;
            $data['company_id'] = $this->company_id;

            $applytype_id = $this->DataControl->insertData("smc_code_couponsapplytype", $data);

            if ($applytype_id) {

                $coursecatList = json_decode(stripslashes($request['coursecatStr']), 1);

                if($coursecatList){

                    foreach($coursecatList as $coursecatOne){
                        $data=array();
                        $data['applytype_id']=$applytype_id;
                        $data['coursecat_id']=$coursecatOne['coursecat_id'];
                        $this->DataControl->insertData("smc_couponsapplytype_coursecatapply",$data);
                    }

                }

                // if(isset($request['courseStr']) && $request['courseStr']!=''){
                //     $courseList = json_decode(stripslashes($request['courseStr']), 1);

                //     if($courseList){

                //         foreach($courseList as $courseOne){
                //             $data=array();
                //             $data['applytype_id']=$applytype_id;
                //             $data['course_id']=$courseOne['course_id'];
                //             $this->DataControl->insertData("smc_couponsapplytype_courseapply",$data);
                //         }
                //     }

                // }
                

//                $mergeList = array_column(json_decode(stripslashes($request['mergeStr']), 1),'coursepacks_id');
//
//                if($mergeList){
//
//                    foreach($mergeList as $mergeOne){
//                        $data=array();
//                        $data['applytype_id']=$applytype_id;
//                        $data['coursepacks_id']=$mergeOne;
//                        $this->DataControl->insertData("smc_couponsapplytype_coursepacksapply",$data);
//                    }
//                }

//
//
//                //是否存在  适配销售组合
//                if($request['jointcourse_id']>1){
//                    $courseList = $this->DataControl->selectClear(" select course_id from smc_code_jointcourse_applys where jointcourse_id = '{$request['jointcourse_id']}' ");
//                }else {
//                    $courseList = json_decode(stripslashes($request['courseStr']), 1);
//                }
//                if ($courseList) {
//                    foreach ($courseList as $courseOne) {
//                        $tem_data = array();
//                        $tem_data['course_id'] = $courseOne['course_id'];
//                        $tem_data['applytype_id'] = $applytype_id;
//                        $this->DataControl->insertData("", $tem_data);
//                    }
//                }

                $this->oktip = "添加成功";
                return $applytype_id;
            } else {
                $this->error = true;
                $this->errortip = "添加失败";
                return false;
            }

        }
    }

    function delapplytype($request)
    {
//        条件未设置
        $couponsOne = $this->DataControl->getFieldOne("smc_code_couponsapplytype", "applytype_buildtype", "applytype_id='{$request['applytype_id']}'");
        if ($couponsOne['applytype_buildtype'] == 1) {
            $this->error = true;
            $this->errortip = "系统创建,不可删除";
            return false;
        }


        $sql = "select ca.apply_id from smc_student_coupons_apply as ca 
              left join smc_code_couponsapplytype as cs on cs.applytype_branch=ca.applytype_branch and cs.company_id='{$this->company_id}'
              where cs.applytype_id='{$request['applytype_id']}' limit 0,1";
        if ($this->DataControl->selectOne($sql)) {
            $this->error = true;
            $this->errortip = "已被使用,不可删除";
            return false;
        }

        $data = array();
        $data['applytype_status'] = '-1';

        $this->DataControl->updateData("smc_code_couponsapplytype", "applytype_id='{$request['applytype_id']}'", $data);

        $this->oktip = "删除成功";
        return true;
    }

    //领取优惠券规则 -- 复制功能
    function copyApplytype($request)
    {
        $oldApplyOne = $this->DataControl->selectOne(" select * from smc_code_couponsapplytype where applytype_id = '{$request['applytype_id']}' and company_id = '{$request['company_id']}' and applytype_branch = '{$request['applytype_branch']}' limit 0,1 ");
        if(!$oldApplyOne){
            $this->error = 1;
            $this->errortip = "参数有误，未找到要复制的对象！";
            return false;
        }
        $countApply = $this->DataControl->selectOne(" select count(applytype_id) as num from smc_code_couponsapplytype where company_id = '{$request['company_id']}' and applytype_branch like '%{$request['applytype_branch']}%' limit 0,1 ");
        $allcount = 1 + $countApply['num'];

        $dataOne = array();
        $dataOne['applytype_cnname'] = $oldApplyOne['applytype_cnname']."（副本）";//复制拼接
        $dataOne['applytype_branch'] = $oldApplyOne['applytype_branch'].$allcount;//复制拼接
        $dataOne['company_id'] = $oldApplyOne['company_id'];
        $dataOne['couponsrules_id'] = $oldApplyOne['couponsrules_id'];
        $dataOne['jointcourse_id'] = $oldApplyOne['jointcourse_id'];
        $dataOne['applytype_port'] = $oldApplyOne['applytype_port'];
        $dataOne['applytype_range'] = $oldApplyOne['applytype_range'];
        $dataOne['applytype_playclass'] = $oldApplyOne['applytype_playclass'];
        $dataOne['applytype_isneedcheck'] = $oldApplyOne['applytype_isneedcheck'];
        $dataOne['applytype_isquota'] = $oldApplyOne['applytype_isquota'];
        $dataOne['applytype_limitterm'] = $oldApplyOne['applytype_limitterm'];
        $dataOne['applytype_limittype'] = $oldApplyOne['applytype_limittype'];
        $dataOne['applytype_maxprice'] = $oldApplyOne['applytype_maxprice'];
        $dataOne['applytype_maxdiscount'] = $oldApplyOne['applytype_maxdiscount'];
        $dataOne['applytype_minprice'] = $oldApplyOne['applytype_minprice'];
        $dataOne['applytype_validitytype'] = $oldApplyOne['applytype_validitytype'];
        $dataOne['applytype_validitydays'] = $oldApplyOne['applytype_validitydays'];
        $dataOne['applytype_deadline'] = $oldApplyOne['applytype_deadline'];
        $dataOne['applytype_rule'] = $oldApplyOne['applytype_rule'];
        $dataOne['applytype_isshop'] = $oldApplyOne['applytype_isshop'];
        $dataOne['applytype_status'] = $oldApplyOne['applytype_status'];
        $dataOne['applytype_isschool'] = $oldApplyOne['applytype_isschool'];
        $dataOne['applytype_caneditcourse'] = $oldApplyOne['applytype_caneditcourse'];
        $dataOne['applytype_type'] = $oldApplyOne['applytype_type'];
        $dataOne['applytype_startday'] = $oldApplyOne['applytype_startday'];
        $dataOne['applytype_endday'] = $oldApplyOne['applytype_endday'];
        $dataOne['applytype_price'] = $oldApplyOne['applytype_price'];
        $dataOne['applytype_discount'] = $oldApplyOne['applytype_discount'];
        $dataOne['applytype_remark'] = $oldApplyOne['applytype_remark'];
        $dataOne['applytype_play'] = $oldApplyOne['applytype_play'];
        $dataOne['applytype_get'] = $oldApplyOne['applytype_get'];
        $dataOne['applytype_isgetdouble'] = $oldApplyOne['applytype_isgetdouble'];
        $dataOne['applytype_applyschool'] = $oldApplyOne['applytype_applyschool'];
        $dataOne['applytype_getscope'] = $oldApplyOne['applytype_getscope'];
        $dataOne['applytype_buildtype'] = $oldApplyOne['applytype_buildtype'];
        $dataOne['applytype_coexist'] = $oldApplyOne['applytype_coexist'];
        $dataOne['applytype_manycourse'] = $oldApplyOne['applytype_manycourse'];
        $dataOne['applytype_applymerge'] = $oldApplyOne['applytype_applymerge'];
        $dataOne['applytype_applycoursecat'] = $oldApplyOne['applytype_applycoursecat'];
        $dataOne['applytype_applycourse'] = $oldApplyOne['applytype_applycourse'];
        $dataOne['applytype_ischannel'] = $oldApplyOne['applytype_ischannel'];//适用渠道  0 全部  1 部分
        $newAid = $this->DataControl->insertData('smc_code_couponsapplytype', $dataOne);

        if(!$newAid){
            $this->error = 1;
            $this->errortip = "复制失败！";
            return false;
        }

        //同步适配渠道
        $this->DataControl->query("
                        insert into smc_couponsapplytype_channelapply(applytype_id,channel_id) 
                        select '{$newAid}',c.channel_id 
                        from smc_couponsapplytype_channelapply as c 
                        WHERE c.applytype_id = '{$oldApplyOne['applytype_id']}' 
                    ");

        //同步适配学校
        $this->DataControl->query("
                        insert into smc_couponsapplytype_schoolapply(applytype_id,school_id) 
                        select '{$newAid}',c.school_id 
                        from smc_couponsapplytype_schoolapply as c 
                        WHERE c.applytype_id = '{$oldApplyOne['applytype_id']}' 
                    ");

        //同步适配班种
        $this->DataControl->query("
                        insert into smc_couponsapplytype_coursecatapply(applytype_id,coursecat_id) 
                        select '{$newAid}',c.coursecat_id 
                        from smc_couponsapplytype_coursecatapply as c 
                        WHERE c.applytype_id = '{$oldApplyOne['applytype_id']}' 
                    ");     

        //同步适配课程
        $this->DataControl->query("
                        insert into smc_couponsapplytype_courseapply(applytype_id,course_id) 
                        select '{$newAid}',c.course_id 
                        from smc_couponsapplytype_courseapply as c 
                        WHERE c.applytype_id = '{$oldApplyOne['applytype_id']}' 
                    ");

        //同步适配组合课程
        $this->DataControl->query("
                        insert into smc_couponsapplytype_coursepacksapply(applytype_id,coursepacks_id) 
                        select '{$newAid}',c.coursepacks_id 
                        from smc_couponsapplytype_coursepacksapply as c 
                        WHERE c.applytype_id = '{$oldApplyOne['applytype_id']}' 
                    ");

        $this->error = 0;
        $this->errortip = "复制成功！";
        return true;
    }

    //商品优惠券申请规则列表
    function couponsrules($paramArray)
    {

        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (s.couponsrules_name like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT 
                s.couponsrules_id,
                s.company_id,
                s.couponsrules_isshop,
                s.couponsrules_name,
                s.couponsrules_starttimes,
                s.couponsrules_stoptimes,
                s.couponsrules_price,
                s.couponsrules_remark,
                s.couponsrules_play,
                s.couponsrules_get
            FROM
                shop_code_couponsrules AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}'
            ORDER BY
                s.couponsrules_id DESC    
            LIMIT {$pagestart},{$num}";

        $spendingList = $this->DataControl->selectClear($sql);

        if ($spendingList) {
            $status = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
            foreach ($spendingList as &$val) {
                $val['couponsrules_isshop'] = $status[$val['couponsrules_isshop']];
            }
        }

        $all_num = $this->DataControl->select("
            SELECT
               COUNT(s.couponsrules_id)
            FROM
                shop_code_couponsrules AS s
            WHERE
                {$datawhere} AND s.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('couponsrules_name', 'couponsrules_isshop', 'couponsrules_starttimes', 'couponsrules_stoptimes', 'couponsrules_price', 'couponsrules_remark', 'couponsrules_play', 'couponsrules_get');
        $fieldname = $this->LgArraySwitch(array('商品优惠券规则名称', '是否微商城优惠券规则', '开始时间', '结束时间', '优惠金额', '商品优惠券规则备注', '使用规则', '领取规则'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($spendingList) {
            $result['list'] = $spendingList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无商品优惠券申请规则", 'result' => $result);
        }

        return $res;
    }

    //新增优惠券申请类型
    function addouponsrulesAction($paramArray)
    {
        $data = array();
        $data['couponsrules_name'] = $paramArray['couponsrules_name'];
        $data['couponsrules_isshop'] = $paramArray['couponsrules_isshop'];
        $data['couponsrules_starttimes'] = $paramArray['couponsrules_starttimes'];
        $data['couponsrules_stoptimes'] = $paramArray['couponsrules_stoptimes'];
        $data['couponsrules_price'] = $paramArray['couponsrules_price'];
        $data['couponsrules_remark'] = $paramArray['couponsrules_remark'];
        $data['couponsrules_play'] = $paramArray['couponsrules_play'];
        $data['couponsrules_get'] = $paramArray['couponsrules_get'];
        $data['company_id'] = $paramArray['company_id'];

        $field = array();
        $field['couponsrules_name'] = "商品优惠券规则名称";
        $field['couponsrules_isshop'] = "是否微商城优惠券规则0否1是";
        $field['couponsrules_starttimes'] = "开始时间";
        $field['couponsrules_stoptimes'] = "结束时间";
        $field['company_id'] = "所属公司";
        $field['couponsrules_price'] = "优惠金额";
        $field['couponsrules_remark'] = "商品优惠券规则备注";
        $field['couponsrules_play'] = "使用规则";
        $field['couponsrules_get'] = "领取规则";

        if ($this->DataControl->insertData('shop_code_couponsrules', $data)) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "新增优惠券申请类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '新增优惠券申请类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加优惠券申请类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑商品优惠券申请规则
    function updatecouponsrulesAction($paramArray)
    {
        $spendingOne = $this->DataControl->getFieldOne("shop_code_couponsrules", "couponsrules_id", "couponsrules_id = '{$paramArray['couponsrules_id']}'");
        if ($spendingOne) {
            $data = array();
            $data['couponsrules_name'] = $paramArray['couponsrules_name'];
            $data['couponsrules_isshop'] = '0';
            $data['couponsrules_starttimes'] = $paramArray['couponsrules_starttimes'];
            $data['couponsrules_stoptimes'] = $paramArray['couponsrules_stoptimes'];
            $data['couponsrules_price'] = $paramArray['couponsrules_price'];
            $data['couponsrules_remark'] = $paramArray['couponsrules_remark'];
            $data['couponsrules_play'] = $paramArray['couponsrules_play'];
            $data['couponsrules_get'] = $paramArray['couponsrules_get'];
            $data['company_id'] = $paramArray['company_id'];

            $field = array();
            $field['couponsrules_name'] = "商品优惠券规则名称";
            $field['couponsrules_starttimes'] = "开始时间";
            $field['couponsrules_stoptimes'] = "结束时间";
            $field['company_id'] = "所属公司";
            $field['couponsrules_price'] = "优惠金额";
            $field['couponsrules_remark'] = "商品优惠券规则备注";
            $field['couponsrules_play'] = "使用规则";
            $field['couponsrules_get'] = "领取规则";

            if ($this->DataControl->updateData("shop_code_couponsrules", "couponsrules_id = '{$paramArray['couponsrules_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑商品优惠券申请规则成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '编辑商品优惠券申请规则', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑商品优惠券申请规则失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除商品优惠券申请规则
    function delcouponsrulesAction($paramArray)
    {
        $SpendingOne = $this->DataControl->getFieldOne("shop_code_couponsrules", "couponsrules_id", "couponsrules_id = '{$paramArray['couponsrules_id']}'");
        if ($SpendingOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("shop_code_couponsrules", "couponsrules_id = '{$paramArray['couponsrules_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除商品优惠券申请规则成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团设置->财务相关设置", '删除商品优惠券申请规则', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除商品优惠券申请规则失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //定金类型列表
    function getDepositList($paramArray)
    {
        $datawhere = " 1 ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (b.coursetype_cnname like '%{$paramArray['keyword']}%' 
                or b.coursetype_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " and (a.deposit_applyschool=0 
                or exists(select 1 from smc_deposit_applyschool where school_id='{$paramArray['school_id']}' and deposit_id=a.deposit_id)) ";
        }

        if (isset($paramArray['deposit_status']) && $paramArray['deposit_status'] !== '') {
            $datawhere .= " and a.deposit_status='{$paramArray['deposit_status']}'";
        }

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== '') {
            $datawhere .= " and a.coursecat_id='{$paramArray['coursecat_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "
            select a.deposit_id,
                a.coursetype_id,
                a.coursecat_id,
                b.coursetype_cnname,
                b.coursetype_branch,
                c.coursecat_cnname,
                c.coursecat_branch,
                a.deposit_status,
                a.deposit_price,
                a.deposit_applyschool,
                (select count(applyschool_id) from smc_deposit_applyschool where deposit_id=a.deposit_id) as school_num,
                a.deposit_remark
            from smc_deposit a 
            left join smc_code_coursetype b on a.coursetype_id=b.coursetype_id
            left join smc_code_coursecat c on c.coursecat_id=a.coursecat_id
            where {$datawhere}
            and a.company_id = '{$paramArray['company_id']}'
            order by a.coursetype_id,a.coursecat_id,a.deposit_price 
            limit {$pagestart},{$num}";

        $depositList = $this->DataControl->selectClear($sql);

        if ($depositList) {
            foreach ($depositList as &$val) {
                if ($val['deposit_applyschool'] == 0) {
                    $val['school_num'] = '全部';
                }
            }
        }

        $all_num = $this->DataControl->select("
            select count(a.deposit_id)
            from smc_deposit a 
            left join smc_code_coursetype b on a.coursetype_id=b.coursetype_id
            where {$datawhere}
            and a.company_id = '{$paramArray['company_id']}'");
        $allnums = $all_num[0][0];

        $fieldstring = array('deposit_id', 'coursetype_id', 'coursecat_id', 'coursetype_cnname', 'coursetype_branch', 'coursecat_cnname', 'coursecat_branch', 'deposit_status', 'deposit_price', 'deposit_applyschool', 'school_num', 'deposit_remark');
        $fieldname = $this->LgArraySwitch(array('定金id', '班组id', '班种id', '班组名称', '班组编号', '班种名称', '班种编号', '是否启用', '定金金额', '适用学校', '适用学校', '备注'));
        $fieldcustom = array("0", "0", "0", "1", "1", "1", "1", "1", "1", "0", "1", "1");
        $fieldshow = array("0", "0", "0", "1", "1", "1", "1", "1", "1", "0", "1", "1");
        $fieldswitch = array("0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldstring[$i]);
            $field[$i]["fieldstring"] = trim($fieldname[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["switch"] = trim($fieldswitch[$i]);
            if ($field[$i]["fieldname"] == 'school_num') {
                $field[$i]["ismethod"] = 1;
            }
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($depositList) {
            $result['list'] = $depositList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无定金类型信息", 'result' => $result);
        }
        return $res;
    }

    //新增定金类型
    function addDepositAction($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['coursetype_id'] = $paramArray['coursetype_id'];
        $data['coursecat_id'] = $paramArray['coursecat_id'];
        $data['deposit_price'] = $paramArray['deposit_price'];
        $data['deposit_applyschool'] = $paramArray['deposit_applyschool'];
        $data['deposit_remark'] = $paramArray['deposit_remark'];
        $data['create_staffer_id'] = $paramArray['staffer_id'];
        $data['create_time'] = time();

        $field = array();
        $field['company_id'] = "所属公司";
        $field['coursetype_id'] = "班组";
        $field['coursecat_id'] = "班种";
        $field['deposit_price'] = "定金金额";
        $field['deposit_applyschool'] = "适用学校";
        $field['deposit_remark'] = "备注";

        $spending_code = $this->DataControl->getFieldOne('smc_deposit', 'deposit_id', "(deposit_price-'{$paramArray['deposit_price']}')=0 and coursetype_id='{$paramArray['coursetype_id']}'and coursecat_id='{$paramArray['coursecat_id']}' and company_id = '{$paramArray['company_id']}'");
        if ($spending_code) {
            ajax_return(array('error' => 1, 'errortip' => "定金类型已存在!"));
        }
        if($paramArray['deposit_price']<=0){
            ajax_return(array('error' => 1, 'errortip' => "定金金额需大于0!"));
        }

        $deposit_id = $this->DataControl->insertData('smc_deposit', $data);

        if ($deposit_id) {
            $result = array();
            $result["field"] = $field;
            $result["data"] = $deposit_id;
            $res = array('error' => '0', 'errortip' => "添加定金类型成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定->定金设置", '添加定金类型', dataEncode($paramArray));

        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加定金类型失败', 'result' => $result);
        }
        return $res;
    }

    //编辑定金类型
    function updateDepositAction($paramArray)
    {
        $spendingOne = $this->DataControl->getFieldOne("smc_deposit", "deposit_id", "deposit_id = '{$paramArray['deposit_id']}'");
        if ($spendingOne) {
            $data = array();
            $data['company_id'] = $paramArray['company_id'];
            $data['coursetype_id'] = $paramArray['coursetype_id'];
            $data['coursecat_id'] = $paramArray['coursecat_id'];
            $data['deposit_price'] = $paramArray['deposit_price'];
            $data['deposit_applyschool'] = $paramArray['deposit_applyschool'];
            $data['deposit_remark'] = $paramArray['deposit_remark'];
            $data['update_staffer_id'] = $paramArray['staffer_id'];
            $data['update_time'] = time();

            $field = array();
            $field['company_id'] = "所属公司";
            $field['coursetype_id'] = "班组";
            $field['coursecat_id'] = "班种";
            $field['deposit_price'] = "定金金额";
            $field['deposit_applyschool'] = "适用学校";
            $field['deposit_remark'] = "备注";

            $spending_code = $this->DataControl->getFieldOne('smc_deposit', 'deposit_id', "(deposit_price-'{$paramArray['deposit_price']}')=0 
                                                                                            and coursetype_id='{$paramArray['coursetype_id']}' 
                                                                                            and coursecat_id='{$paramArray['coursecat_id']}' 
                                                                                            and company_id = '{$paramArray['company_id']}' 
                                                                                            and deposit_id<>'{$paramArray['deposit_id']}'");
            if ($spending_code) {
                ajax_return(array('error' => 1, 'errortip' => "定金类型已存在!"));
            }
            if($paramArray['deposit_price']<=0){
                ajax_return(array('error' => 1, 'errortip' => "定金金额需大于0!"));
            }

            if ($this->DataControl->updateData("smc_deposit", "deposit_id = '{$paramArray['deposit_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "定金类型修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定->定金设置", '修改定金类型', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '定金类型修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //是否启用定金类型
    function updateDepositStatusAction($paramArray)
    {
        $productsOne = $this->DataControl->getFieldOne("smc_deposit", "deposit_id", "deposit_id = '{$paramArray['deposit_id']}'");
        if ($productsOne) {
            $data = array();
            $data['deposit_status'] = $paramArray['deposit_status'];
            $data['update_staffer_id'] = $paramArray['staffer_id'];
            $data['update_time'] = time();

            $field = array();
            $field['deposit_status'] = "是否启用";

            if ($this->DataControl->updateData("smc_deposit", "deposit_id = '{$paramArray['deposit_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "修改成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定->定金设置", '修改是否启用', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '修改失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除定金类型
    function deleteDepositAction($paramArray)
    {
        $SpendingOne = $this->DataControl->getFieldOne("smc_deposit", "deposit_id,deposit_price", "deposit_id = '{$paramArray['deposit_id']}'");
        if ($SpendingOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"));
            }
            if ($this->DataControl->delData("smc_deposit", "deposit_id = '{$paramArray['deposit_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除定金类型成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团课程收费->课程收费设定->定金设置", '删除定金类型', dataEncode($paramArray));
            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除定金类型失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    function getDepositSchoolList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' 
                                or sc.school_branch like '%{$request['keyword']}%' 
                                or sc.school_enname like '%{$request['keyword']}%' 
                                or sc.school_shortname like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cs.applyschool_id,sc.school_id,sc.school_cnname,sc.school_enname,sc.school_shortname,sc.school_branch,ifnull(cd.district_cnname,'') as district_cnname
              from smc_deposit_applyschool as cs
              inner join smc_school as sc on sc.school_id=cs.school_id
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and cs.deposit_id='{$request['deposit_id']}' and sc.company_id='{$this->company_id}'
              ORDER BY sc.school_sort DESC
              LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $this->error = true;
            $this->errortip = "暂无适用学校消息~";
            return false;
        }
        $data = array();
        $count_sql = "select sc.school_id
              from smc_deposit_applyschool as cs
              inner join smc_school as sc on sc.school_id=cs.school_id
              where {$datawhere} and cs.deposit_id='{$request['deposit_id']}' and sc.company_id='{$this->company_id}'
              ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;
        $data['list'] = $courseList;

        return $data;
    }

    function addDepositSchool($request)
    {
        if (!isset($request['school_json']) || $request['school_json'] == '') {
            $this->error = true;
            $this->errortip = "请选择学校";
            return false;
        }

        $list = json_decode(stripslashes($request['school_json']), 1);

        foreach ($list as $val) {
            if (!$this->DataControl->getFieldOne("smc_deposit_applyschool", "applyschool_id", "school_id='{$val}' and deposit_id='{$request['deposit_id']}'")) {
                $data = array();
                $data['school_id'] = $val;
                $data['deposit_id'] = $request['deposit_id'];
                $data['create_staffer_id'] = $request['staffer_id'];
                $data['create_time'] = time();

                $this->DataControl->insertData("smc_deposit_applyschool", $data);
            }
        }

        $this->error = true;
        $this->oktip = "添加成功";
        return true;
    }

    function deleteDepositSchool($request)
    {
        if ($this->DataControl->delData("smc_deposit_applyschool", "applyschool_id='{$request['applyschool_id']}'")) {
            $this->error = true;
            $this->oktip = "移除成功";
            return true;
        } else {
            $this->error = true;
            $this->errortip = "移除失败";
            return false;
        }
    }

    function getActiveCoursetypeList($request)
    {
        $datawhere = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.coursetype_cnname like '%{$request['keyword']}%' 
                                or a.coursetype_branch like '%{$request['keyword']}%')";
        }

        $sql = "select coursetype_id,coursetype_cnname,coursetype_branch,company_id
              from smc_code_coursetype a
              where {$datawhere} 
              and a.company_id='{$this->company_id}'
              ORDER BY a.coursetype_id ";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $this->error = true;
            $this->errortip = "暂无可用班组，请先添加班组设置";
            return false;
        }

        return $courseList;
    }

    function getSchoolList($request)
    {
        $datawhere = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.school_cnname like '%{$request['keyword']}%' 
            or sc.school_enname like '%{$request['keyword']}%' 
            or sc.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['district_id']) && $request['district_id'] !== '') {
            $datawhere .= " and sc.district_id='{$request['district_id']}'";
        }

        if (isset($request['school_province']) && $request['school_province'] !== '') {
            $datawhere .= " and sc.school_province='{$request['school_province']}'";
        }

        if (isset($request['school_city']) && $request['school_city'] !== '') {
            $datawhere .= " and sc.school_city='{$request['school_city']}'";
        }

        if (isset($request['school_area']) && $request['school_area'] !== '') {
            $datawhere .= " and sc.school_area='{$request['school_area']}'";
        }

        if (isset($request['organize_id']) && $request['organize_id'] !== "") {
            $datawhere .= " and sc.school_id in (select school_id from gmc_company_organizeschool where organize_id = '{$request['organize_id']}')";
        }
        
        $sql = "select sc.school_id,sc.school_branch,sc.school_cnname,sc.school_enname,cd.district_cnname
              from smc_school as sc
              left join gmc_company_district as cd on cd.district_id=sc.district_id
              where {$datawhere} and sc.company_id='{$this->company_id}' and sc.school_isclose=0 
              and not exists(select 1 from smc_deposit_applyschool where deposit_id='{$request['deposit_id']}' and school_id=sc.school_id)
              ORDER BY sc.school_branch DESC
              ";

        $schoolList = $this->DataControl->selectClear($sql);
        if (!$schoolList) {
            $this->error = true;
            $this->errortip = "无相关学校";
            return false;
        }

        $data=array();
        $data['district'] = $this->DataControl->selectClear("select district_id,district_cnname from gmc_company_district where company_id = '{$this->company_id}'");
        $data['schoolList']=$schoolList;

        return $data;
    }

    function applyCourseList($request){


        $datawhere = " b.company_id='{$this->company_id}' and a.applytype_id='{$request['applytype_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.course_cnname like '%{$request['keyword']}%' or b.course_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and b.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and b.coursecat_id='{$request['coursecat_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT a.courseapply_id,b.course_cnname,b.course_branch,c.coursecat_cnname,d.coursetype_cnname
                from smc_couponsapplytype_courseapply as a
                left join smc_course as b on b.course_id=a.course_id
                left join smc_code_coursecat as c on c.coursecat_id=b.coursecat_id
                left join smc_code_coursetype as d on d.coursetype_id=b.coursetype_id
                where {$datawhere} 
                ORDER BY
                    a.courseapply_id asc
                LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $this->error = true;
            $this->errortip = "暂无适用课程消息~";
            return false;
        }

        $count_sql = "SELECT a.course_id
                    from smc_couponsapplytype_courseapply as a
                    left join smc_course as b on b.course_id=a.course_id
                    left join smc_code_coursecat as c on c.coursecat_id=b.coursecat_id
                    left join smc_code_coursetype as d on d.coursetype_id=b.coursetype_id
                    where {$datawhere} ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $data=array();
        $data['allnum'] = $allnum;
        $data['list']=$courseList;

        return $data;


    }

    

    function applyAllCourseList($request){


        $datawhere = " b.company_id='{$this->company_id}'";
        $datawhere .= " and not exists(select 1 from smc_couponsapplytype_courseapply as x where x.applytype_id='{$request['applytype_id']}' and x.course_id=b.course_id) ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (b.course_cnname like '%{$request['keyword']}%' or b.course_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and b.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['coursecat_id']) && $request['coursecat_id'] !== '') {
            $datawhere .= " and b.coursecat_id='{$request['coursecat_id']}'";
        }

        if(isset($request['applytype_id']) && $request['applytype_id']!=''){

            $applyTypeOne=$this->DataControl->getFieldOne("smc_code_couponsapplytype","applytype_id,applytype_applycourse,applytype_applycoursecat","applytype_id='{$request['applytype_id']}'");

            if($applyTypeOne['applytype_applycoursecat']==1){
                $datawhere .= " and b.coursecat_id in (select x.coursecat_id from smc_couponsapplytype_coursecatapply as x where x.applytype_id='{$request['applytype_id']}')";
            }
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT b.course_id,b.course_cnname,b.course_branch,c.coursecat_cnname,d.coursetype_cnname
                from  smc_course as b 
                left join smc_code_coursecat as c on c.coursecat_id=b.coursecat_id
                left join smc_code_coursetype as d on d.coursetype_id=b.coursetype_id
                where {$datawhere} 
                LIMIT {$pagestart},{$num}";

        $courseList = $this->DataControl->selectClear($sql);

        if (!$courseList) {
            $this->error = true;
            $this->errortip = "暂无适用课程消息~";
            return false;
        }

        $count_sql = "SELECT b.course_id
                    from  smc_course as b 
                    left join smc_code_coursecat as c on c.coursecat_id=b.coursecat_id
                    left join smc_code_coursetype as d on d.coursetype_id=b.coursetype_id
                    where {$datawhere} ";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }

        $data=array();
        $data['allnum'] = $allnum;
        $data['list']=$courseList;

        return $data;


    }

    function addApplyCourse($request){

        if(isset($request['courseStr']) && $request['courseStr']!='' && isset($request['applytype_id']) && $request['applytype_id']!=''){
                    
            $applytypeOne=$this->DataControl->getFieldOne("smc_code_couponsapplytype","applytype_id,applytype_applycourse","applytype_id='{$request['applytype_id']}'");

            if($applytypeOne['applytype_applycourse']!=0){

                $courseList = array_column(json_decode(stripslashes($request['courseStr']), 1),'course_id');

                $applyCourseList = $this->DataControl->selectClear("select course_id from smc_couponsapplytype_courseapply where applytype_id='{$request['applytype_id']}'");

                $applyCourseList=$applyCourseList?array_column($applyCourseList,'course_id'):array();

                $addArray=array_diff($courseList,$applyCourseList);
                // $delArray=array_diff($applyCourseList,$courseList);

                if($addArray){
                    foreach($addArray as $addOne){
                        $data=array();
                        $data['applytype_id']=$request['applytype_id'];
                        $data['course_id']=$addOne;
                        $this->DataControl->insertData("smc_couponsapplytype_courseapply",$data);
                    }
                }

                // if($delArray){
                //     foreach($delArray as $delOne){
                //         $this->DataControl->delData("smc_couponsapplytype_courseapply","applytype_id='{$request['applytype_id']}' and course_id='{$delOne}'");
                //     }
                // }
                
            }

            return true;
        }else{
            $this->error = true;
            $this->errortip = "请选择课程";
            return false;
        }

    }

    function removeApplyCourse($request){

        if($this->DataControl->delData("smc_couponsapplytype_courseapply","courseapply_id='{$request['courseapply_id']}'")){
            $this->error = true;
            $this->oktip = "移除成功";
            return true;
        }else{
            $this->error = true;
            $this->errortip = "移除失败";
            return false;
        }
    }

}
