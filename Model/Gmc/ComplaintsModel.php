<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Gmc;

class ComplaintsModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->company_id = $publicarray['company_id'];
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
        if (isset($publicarray['staffer_id'])) {
            if (!$this->verdictStaffer($publicarray['staffer_id'])) {
                $this->error = 0;
                $this->errortip = "操作人不存在!";
                return false;
            }
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    function verdictStaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_mobile", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //投诉分类管理
    function getComplaintsTypeList($paramArray)
    {
        $datawhere = " (f.company_id = '{$paramArray['company_id']}' or f.company_id = '0') ";

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (f.feedbacktype_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                f.feedbacktype_id,
                f.feedbacktype_name,
                f.feedbacktype_catgory,
                f.feedbacktype_belongs,
                f.feedbacktype_note,
                f.feedbacktype_status,
                f.feedbacktype_createtime
            FROM
                ucs_code_feedbacktype AS f
            WHERE
                {$datawhere}
            ORDER BY feedbacktype_id DESC
            LIMIT {$pagestart},{$num}";

        $ComplaintsList = $this->DataControl->selectClear($sql);

        if($ComplaintsList){
            $type = $this->LgArraySwitch(array("1" => "集团", "2" => "校区"));
            $status = $this->LgArraySwitch(array("1" => "投诉", "2" => "建议", "3" => "表扬", "4" => "其他"));
            foreach($ComplaintsList as &$val){
                $val['feedbacktype_belongs_name'] = $type[$val['feedbacktype_belongs']];
                $val['feedbacktype_catgory_name'] = $status[$val['feedbacktype_catgory']];
                $val['feedbacktype_createtime'] = date("Y-m-d H:i", $val['feedbacktype_createtime']);
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT
                                                       COUNT(f.feedbacktype_id) as num
                                                     FROM
                                                        ucs_code_feedbacktype AS f
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('feedbacktype_name', 'feedbacktype_catgory_name', 'feedbacktype_belongs_name', 'feedbacktype_status', 'feedbacktype_createtime');
        $fieldname = $this->LgArraySwitch(array('分类名称', '投诉类型', '分类所属', '是否启用', '录入时间'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");
        $fieldisswitch = array("0", "0", "0", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ComplaintsList) {
            $result['list'] = $ComplaintsList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂未添加投诉分类，点击右上角新增可添加", 'result' => $result);
        }

        return $res;
    }

    //添加投诉分类
    function addComplaintsType($paramArray)
    {
        $status = $this->LgArraySwitch(array("1" => "投诉", "2" => "建议", "3" => "表扬", "4" => "其他"));
        if($paramArray['feedbacktype_name'] == $status[$paramArray['feedbacktype_catgory']]){
            ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉分类"), $this->companyOne['company_language']);
        }
        if($this->DataControl->getFieldOne("ucs_code_feedbacktype","feedbacktype_id","company_id='{$paramArray['company_id']}' and feedbacktype_name='{$paramArray['feedbacktype_name']}' and feedbacktype_catgory='{$paramArray['feedbacktype_catgory']}'")){
            ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉分类"), $this->companyOne['company_language']);
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['feedbacktype_name'] = $paramArray['feedbacktype_name'];
        $data['feedbacktype_belongs'] = $paramArray['feedbacktype_belongs'];
        $data['feedbacktype_catgory'] = $paramArray['feedbacktype_catgory'];
        $data['feedbacktype_note'] = $paramArray['feedbacktype_note'];
        $data['feedbacktype_createtime'] = time();

        $field = array();
        $field['feedbacktype_name'] = "分类名称";
        $field['feedbacktype_belongs'] = "分类所属";
        $field['feedbacktype_catgory'] = "投诉类型";
        $field['feedbacktype_note'] = "投诉分类备注";
        $field['company_id'] = "所属集团";

        if($this->DataControl->insertData('ucs_code_feedbacktype', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加投诉分类成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '添加投诉分类', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加投诉分类失败！', 'result' => $result);
        }

        return $res;
    }

    //编辑投诉分类
    function updateComplaintsType($paramArray)
    {
        $ComplaintsOne = $this->DataControl->getFieldOne("ucs_code_feedbacktype", "feedbacktype_id,feedbacktype_catgory", "feedbacktype_id = '{$paramArray['feedbacktype_id']}'");
        if ($ComplaintsOne) {
            $status = $this->LgArraySwitch(array("1" => "投诉", "2" => "建议", "3" => "表扬", "4" => "其他"));
            if($paramArray['feedbacktype_name'] == $status[$ComplaintsOne['feedbacktype_catgory']]){
                ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉分类"), $this->companyOne['company_language']);
            }
            if($this->DataControl->getFieldOne("ucs_code_feedbacktype","feedbacktype_id","feedbacktype_id <> '{$paramArray['feedbacktype_id']}' and company_id='{$paramArray['company_id']}' and feedbacktype_name='{$paramArray['feedbacktype_name']}' and feedbacktype_catgory='{$paramArray['feedbacktype_catgory']}'")){
                ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉分类"), $this->companyOne['company_language']);
            }

            $data = array();
            $data['feedbacktype_name'] = $paramArray['feedbacktype_name'];
            $data['feedbacktype_belongs'] = $paramArray['feedbacktype_belongs'];
            $data['feedbacktype_catgory'] = $paramArray['feedbacktype_catgory'];
            $data['feedbacktype_note'] = $paramArray['feedbacktype_note'];

            $field = array();
            $field['feedbacktype_name'] = "分类名称";
            $field['feedbacktype_belongs'] = "分类所属";
            $field['feedbacktype_catgory'] = "投诉类型";
            $field['feedbacktype_note'] = "投诉分类备注";

            if ($this->DataControl->updateData("ucs_code_feedbacktype", "feedbacktype_id = '{$paramArray['feedbacktype_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑投诉分类成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '编辑投诉分类', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑投诉分类失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除投诉分类
    function delComplaintsType($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("ucs_code_feedbacktype", "feedbacktype_id", "feedbacktype_id = '{$paramArray['feedbacktype_id']}'");
        if ($IntegraltypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            $a = $this->DataControl->getFieldOne("ucs_code_feedbacktheme","feedbacktype_id","feedbacktype_id = '{$paramArray['feedbacktype_id']}'");
            if($a){
                ajax_return(array('error' => 1, 'errortip' => "该分类已被使用，无法删除！"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("ucs_code_feedbacktype", "feedbacktype_id = '{$paramArray['feedbacktype_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除投诉分类成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '删除投诉分类', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除投诉分类失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }


    //是否启用投诉分类
    function isOpenComplaints($paramArray)
    {
        $ClassOne = $this->DataControl->getFieldOne("ucs_code_feedbacktype", "feedbacktype_id", "feedbacktype_id = '{$paramArray['feedbacktype_id']}'");
        if ($ClassOne) {
            $data = array();
            $data['feedbacktype_status'] = $paramArray['feedbacktype_status'];
            if($paramArray['feedbacktype_status']){
                $tip = '开启';
            }else{
                $tip = '关闭';
            }

            $field = array();
            $field['feedbacktype_status'] = "是否启用";
            if($this->DataControl->updateData("ucs_code_feedbacktype", "feedbacktype_id = '{$paramArray['feedbacktype_id']}'", $data)){
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "投诉分类{$tip}成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", "投诉分类{$tip}", dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => "投诉分类{$tip}失败", 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //投诉主题管理
    function getComplaintsTheme($paramArray)
    {
        $datawhere = " f.company_id = '{$paramArray['company_id']}' ";
        if(isset($paramArray['feedbacktype_id']) && $paramArray['feedbacktype_id'] !== ''){
            $datawhere .= " and f.feedbacktype_id = '{$paramArray['feedbacktype_id']}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (fb.feedbacktype_name like '%{$paramArray['keyword']}%' or f.feedbacktheme_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                f.feedbacktheme_id,
                f.feedbacktype_id,
                f.feedbacktheme_name,
                f.feedbacktheme_rolelist,
                f.feedbacktheme_note,
                f.feedbacktheme_createtime,
                fb.feedbacktype_name,
                fb.feedbacktype_catgory
            FROM
                ucs_code_feedbacktheme AS f
            LEFT JOIN
                ucs_code_feedbacktype AS fb ON fb.feedbacktype_id = f.feedbacktype_id
            WHERE
                {$datawhere}
            order by f.feedbacktheme_id DESC
            LIMIT {$pagestart},{$num}";

        $ComplaintsList = $this->DataControl->selectClear($sql);

        if($ComplaintsList){
            $status = $this->LgArraySwitch(array("1" => "投诉", "2" => "建议", "3" => "表扬", "4" => "其他"));
            $role = $this->LgArraySwitch(array("1" => "校区客服", "2" => "校长", "3" => "集团客服", "4" => "集团高管"));
            foreach($ComplaintsList as &$val){
                if($val['feedbacktheme_rolelist']){
                    $rolelist = json_decode($val['feedbacktheme_rolelist'],true);
                    $arr = array();
                    foreach($rolelist as $item){
                        $arr[] = $role[$item];
                    }
                    $rolename = implode(",", $arr);
                    $val['feedbacktheme_rolelist_name'] = $rolename;
                }else{
                    $val['feedbacktheme_rolelist_name'] = '';
                }
                $val['feedbacktype_catgory_name'] = $status[$val['feedbacktype_catgory']];
                $val['feedbacktheme_createtime'] = date("Y-m-d H:i", $val['feedbacktheme_createtime']);
            }
        }

        $all_num = $this->DataControl->selectOne("SELECT
                                                       COUNT(f.feedbacktheme_id) as num
                                                    FROM
                                                        ucs_code_feedbacktheme AS f
                                                    LEFT JOIN
                                                        ucs_code_feedbacktype AS fb ON fb.feedbacktype_id = f.feedbacktype_id
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('feedbacktheme_name', 'feedbacktype_name', 'feedbacktype_catgory_name', 'feedbacktheme_rolelist_name', 'feedbacktheme_createtime');
        $fieldname = $this->LgArraySwitch(array('投诉主题名称', '投诉分类', '投诉类型', '适配角色', '录入时间'));
        $fieldcustom = array("1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($ComplaintsList) {
            $result['list'] = $ComplaintsList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无投诉主题，点击右上角新增可添加", 'result' => $result);
        }

        return $res;
    }

    //添加投诉主题
    function addComplaintsTheme($paramArray)
    {
        $feedbacktype = $this->DataControl->getFieldOne("ucs_code_feedbacktype","feedbacktype_name","feedbacktype_id='{$paramArray['feedbacktype_id']}'");
        if($paramArray['feedbacktheme_name'] == $feedbacktype['feedbacktype_name']){
            ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉主题"), $this->companyOne['company_language']);
        }
        if($this->DataControl->getFieldOne("ucs_code_feedbacktheme","feedbacktheme_id","company_id='{$paramArray['company_id']}' and feedbacktype_id='{$paramArray['feedbacktype_id']}' and feedbacktheme_name='{$paramArray['feedbacktheme_name']}'")){
            ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉主题"), $this->companyOne['company_language']);
        }

        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['feedbacktheme_name'] = $paramArray['feedbacktheme_name'];
        $data['feedbacktype_id'] = $paramArray['feedbacktype_id'];
        $data['feedbacktheme_rolelist'] = $paramArray['feedbacktheme_rolelist'];
        $data['feedbacktheme_note'] = $paramArray['feedbacktheme_note'];
        $data['feedbacktheme_createtime'] = time();

        $field = array();
        $field['feedbacktheme_name'] = "投诉主题名称";
        $field['feedbacktype_id'] = "投诉分类";
        $field['feedbacktheme_rolelist'] = "适配角色";
        $field['feedbacktheme_note'] = "备注";
        $field['company_id'] = "所属集团";

        if($this->DataControl->insertData('ucs_code_feedbacktheme', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加投诉主题成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '添加投诉主题', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加投诉主题失败', 'result' => $result);
        }

        return $res;
    }

    //编辑投诉主题
    function updateComplaintsTheme($paramArray)
    {
        $ComplaintsOne = $this->DataControl->getFieldOne("ucs_code_feedbacktheme", "feedbacktheme_id", "feedbacktheme_id = '{$paramArray['feedbacktheme_id']}'");
        if ($ComplaintsOne) {
            $feedbacktype = $this->DataControl->getFieldOne("ucs_code_feedbacktype","feedbacktype_name","feedbacktype_id='{$paramArray['feedbacktype_id']}'");
            if($paramArray['feedbacktheme_name'] == $feedbacktype['feedbacktype_name']){
                ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉主题"), $this->companyOne['company_language']);
            }
            if($this->DataControl->getFieldOne("ucs_code_feedbacktheme","feedbacktheme_id","feedbacktheme_id <> '{$paramArray['feedbacktheme_id']}' and company_id='{$paramArray['company_id']}' and feedbacktype_id='{$paramArray['feedbacktype_id']}' and feedbacktheme_name='{$paramArray['feedbacktheme_name']}'")){
                ajax_return(array('error' => 1, 'errortip' => "已存在重复的投诉主题"), $this->companyOne['company_language']);
            }

            $data = array();
            $data['feedbacktheme_name'] = $paramArray['feedbacktheme_name'];
            $data['feedbacktype_id'] = $paramArray['feedbacktype_id'];
            $data['feedbacktheme_rolelist'] = $paramArray['feedbacktheme_rolelist'];
            $data['feedbacktheme_note'] = $paramArray['feedbacktheme_note'];

            $field = array();
            $field['feedbacktheme_name'] = "投诉主题名称";
            $field['feedbacktype_id'] = "投诉分类";
            $field['feedbacktheme_rolelist'] = "适配角色";
            $field['feedbacktheme_note'] = "备注";

            if ($this->DataControl->updateData("ucs_code_feedbacktheme", "feedbacktheme_id = '{$paramArray['feedbacktheme_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑投诉主题成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '编辑投诉主题', dataEncode($paramArray));

            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑投诉主题失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除投诉主题
    function delComplaintsTheme($paramArray)
    {
        $ComplaintsOne = $this->DataControl->getFieldOne("ucs_code_feedbacktheme", "feedbacktheme_id", "feedbacktheme_id = '{$paramArray['feedbacktheme_id']}'");
        if ($ComplaintsOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("ucs_code_feedbacktheme", "feedbacktheme_id = '{$paramArray['feedbacktheme_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除投诉主题成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '删除投诉主题', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除投诉主题失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //话术管理
    function getSpeechcraft($paramArray)
    {
        $datawhere = " s.company_id = '{$paramArray['company_id']}' AND s.staffer_id=0 ";
        if(isset($paramArray['feedbacktype_id']) && $paramArray['feedbacktype_id'] != ''){
            $datawhere .= " and s.feedbacktype_id = '{$paramArray['feedbacktype_id']}'";
        }
        if(isset($paramArray['feedbacktheme_id']) && $paramArray['feedbacktheme_id'] != ''){
            $datawhere .= " and s.feedbacktheme_id = '{$paramArray['feedbacktheme_id']}'";
        }
        if(isset($paramArray['keyword']) && $paramArray['keyword'] != ''){
            $datawhere .= " and (s.speechcraft_name like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                s.speechcraft_id,
                s.feedbacktype_id,
                s.feedbacktheme_id,
                s.speechcraft_name,
                s.speechcraft_content,
                s.speechcraft_status,
                FROM_UNIXTIME(s.speechcraft_createtime,'%Y-%m-%d %H:%i') as speechcraft_createtime,
                (SELECT f.feedbacktype_name FROM ucs_code_feedbacktype as f WHERE f.feedbacktype_id = s.feedbacktype_id) as feedbacktype_name,
                (SELECT fb.feedbacktheme_name FROM ucs_code_feedbacktheme as fb WHERE fb.feedbacktheme_id = s.feedbacktheme_id) as feedbacktheme_name
            FROM
                ucs_speechcraft as s
            WHERE
                {$datawhere}
            ORDER BY
                s.speechcraft_id DESC
            LIMIT {$pagestart},{$num}";

        $speechcraftList = $this->DataControl->selectClear($sql);

        $all_num = $this->DataControl->selectOne("SELECT
                                                       COUNT(s.speechcraft_id) as num
                                                     FROM
                                                        ucs_speechcraft as s
                                                    WHERE
                                                        {$datawhere}");
        $allnums = $all_num['num'];

        $fieldstring = array('feedbacktype_name', 'feedbacktheme_name', 'speechcraft_name', 'speechcraft_content', 'speechcraft_status', 'speechcraft_createtime');
        $fieldname = $this->LgArraySwitch(array('投诉分类', '投诉主题', '话术名称', '话术内容', '是否发布', '发布时间'));
        $fieldcustom = array("1", "1", "1", "1", "1", "1");
        $fieldshow = array("1", "1", "1", "1", "1", "1");
        $fieldisswitch = array("0", "0", "0", "0", "1", "0");

        $field = array();
        for ($i = 0; $i < count($fieldstring); $i++) {
            $field[$i]["fieldname"] = trim($fieldname[$i]);
            $field[$i]["fieldstring"] = trim($fieldstring[$i]);
            $field[$i]["custom"] = trim($fieldcustom[$i]);
            $field[$i]["show"] = trim($fieldshow[$i]);
            $field[$i]["isswitch"] = trim($fieldisswitch[$i]);
        }

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['all_num'] = $allnums;

        if ($speechcraftList) {
            $result['list'] = $speechcraftList;
            $res = array('error' => '0', 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => '1', 'errortip' => "暂无话术，点击右上角新增可添加", 'result' => $result);
        }

        return $res;
    }

    //添加话术
    function addSpeechcraft($paramArray)
    {
        $data = array();
        $data['company_id'] = $paramArray['company_id'];
        $data['staffer_id'] = 0;
        $data['feedbacktype_id'] = $paramArray['feedbacktype_id'];
        $data['feedbacktheme_id'] = $paramArray['feedbacktheme_id'];
        $data['speechcraft_name'] = $paramArray['speechcraft_name'];
        $data['speechcraft_content'] = $paramArray['speechcraft_content'];
        $data['speechcraft_status'] = $paramArray['speechcraft_status'];
        $data['speechcraft_createtime'] = time();

        $field = array();
        $field['feedbacktype_id'] = "投诉分类";
        $field['feedbacktheme_id'] = "投诉主题";
        $field['speechcraft_name'] = "话术名称";
        $field['speechcraft_content'] = "话术内容";
        $field['speechcraft_status'] = "是否发布 0不发布1发布";
        $field['company_id'] = "所属集团";
        $field['staffer_id'] = "归属职员";

        if($this->DataControl->insertData('ucs_speechcraft', $data)){
            $result = array();
            $result["field"] = $field;
            $result["data"] = $data;
            $res = array('error' => '0', 'errortip' => "添加话术成功", 'result' => $result);
            $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '添加话术', dataEncode($paramArray));
        }else{
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '添加话术失败', 'result' => $result);
        }

        return $res;
    }

    //编辑话术
    function updateSpeechcraft($paramArray)
    {
        $ClassOne = $this->DataControl->getFieldOne("ucs_speechcraft", "speechcraft_id", "speechcraft_id = '{$paramArray['speechcraft_id']}'");
        if ($ClassOne) {
            $data = array();
            $data['feedbacktype_id'] = $paramArray['feedbacktype_id'];
            $data['feedbacktheme_id'] = $paramArray['feedbacktheme_id'];
            $data['speechcraft_name'] = $paramArray['speechcraft_name'];
            $data['speechcraft_content'] = $paramArray['speechcraft_content'];
            $data['speechcraft_status'] = $paramArray['speechcraft_status'];

            $field = array();
            $field['feedbacktype_id'] = "投诉分类";
            $field['feedbacktheme_id'] = "投诉主题";
            $field['speechcraft_name'] = "话术名称";
            $field['speechcraft_content'] = "话术内容";
            $field['speechcraft_status'] = "是否发布 0不发布1发布";

            if ($this->DataControl->updateData("ucs_speechcraft", "speechcraft_id = '{$paramArray['speechcraft_id']}'", $data)) {
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "编辑话术成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '编辑话术', dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => '编辑话术失败', 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //是否启用话术
    function isOpenSpeechcraft($paramArray)
    {
        $ClassOne = $this->DataControl->getFieldOne("ucs_speechcraft", "speechcraft_id", "speechcraft_id = '{$paramArray['speechcraft_id']}'");
        if ($ClassOne) {
            $data = array();
            $data['speechcraft_status'] = $paramArray['speechcraft_status'];
            if($paramArray['speechcraft_status']){
                $tip = '开启';
            }else{
                $tip = '关闭';
            }

            $field = array();
            $field['speechcraft_status'] = "是否启用";
            if($this->DataControl->updateData("ucs_speechcraft", "speechcraft_id = '{$paramArray['speechcraft_id']}'", $data)){
                $result = array();
                $result["field"] = $field;
                $result["data"] = $data;
                $res = array('error' => '0', 'errortip' => "话术{$tip}成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", "话术{$tip}", dataEncode($paramArray));
            } else {
                $result = array();
                $result["data"] = array();
                $res = array('error' => '1', 'errortip' => "话术{$tip}失败", 'result' => $result);
            }
        } else {
            $result = array();
            $result["data"] = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }

    //删除话术
    function delSpeechcraft($paramArray)
    {
        $IntegraltypeOne = $this->DataControl->getFieldOne("ucs_speechcraft", "speechcraft_id", "speechcraft_id = '{$paramArray['speechcraft_id']}'");
        if ($IntegraltypeOne) {
            if ($paramArray['re_postbe_id'] !== '0') {
                ajax_return(array('error' => 1, 'errortip' => "没有删除权限"), $this->companyOne['company_language']);
            }
            if ($this->DataControl->delData("ucs_speechcraft", "speechcraft_id = '{$paramArray['speechcraft_id']}'")) {
                $result = array();
                $res = array('error' => '0', 'errortip' => "删除话术成功", 'result' => $result);
                $this->addGmcWorkLog($paramArray['company_id'], $paramArray['staffer_id'], "集团相关设置->客诉相关设置", '删除话术', dataEncode($paramArray));

            } else {
                $result = array();
                $res = array('error' => '1', 'errortip' => '删除话术失败', 'result' => $result);
            }
        } else {
            $result = array();
            $res = array('error' => '1', 'errortip' => '数据不全', 'result' => $result);
        }
        return $res;
    }
}
