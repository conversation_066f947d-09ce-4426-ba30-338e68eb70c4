<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/27
 * Time: 17:28
 */

namespace Model\Api;
use FG\ASN1\ASNObject;
use Rtgm\sm\RtSm2;
require_once BASEDIR."/Core/Tools/Mdanter/Random/random.php";
require_once BASEDIR . "/Core/Tools/autoload.php";


class MergeboingPayModel extends \Model\modelTpl{
    public $u;
    public $t;
    public $c;
//    public $apiUlr = "https://api.cmburl.cn:8065/hou/";//测试地址
    public $apiUlr = "https://api.cmbchina.com/ptn/";//正式地址
    public $error = 0;
    public $errortip = '';
    public $oktip = false;
    public $companyOne = false;
    public $payfeeOrderOne = false;
    public $BoingPay;
    protected $config;

    function __construct($company_id ='0') {
        parent::__construct ();
        if($company_id !== '0'){
            $this->verdictcompany($company_id);
        }

        $this->BoingPayStart();
    }

    function BoingPayStart(){

        $config = array();
        $config['use_sandbox'] = true;
        $config['userId'] = '1234567812345678';//国密用户ID
        //$config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE1xPq3B3Cw2U+t+R7Fb0JCJvy87/LDbUDFilGjkQU89VLl57pbUPLKUwP2jnAyOEKmJS9USsz+VwXNd4/bjdIFA==';// 招行公钥
        $config['publicHead'] = '3059301306072A8648CE3D020106082A811CCF5501822D03420004';// 招行公钥头
        $config['notify_url'] = 'https://scshopapi.kedingdang.com/MergeBoingPay/OrderBak';//异步回调地址


        $config['mer_id'] = $this->companyOne['company_merId'];
        $config['user_id'] = $this->companyOne['company_userId'];


        $settingOne=$this->DataControl->getOne("gmc_code_companies_seting","companies_id='9'");



//        $config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
//        $config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
//        $config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
//        $config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $config['publicKey'] = $settingOne['companies_publicKey'];//招行验签公钥
        $config['privateKey'] = $settingOne['companies_privateKey'];//商户私钥
        $config['appid'] = $settingOne['companies_appid'];//appid
        $config['secret'] = $settingOne['companies_secret'];//秘钥


        $this->config = $config;
    }

    function verdictcompany($company_id){
        $this->companyOne = $this->DataControl->getFieldOne("gmc_company","company_id,company_cnname,company_boningcode,company_merId,company_userId,company_termId","company_id = '{$company_id}'");
        if(!$this->companyOne){
            $this->error = true;
            $this->errortip = "集团信息不存在";
        }
    }

    function OrderPaytestbak($result){
        $this->config['mer_id'] = '3089991727301QS';
        $this->config['user_id'] = 'V078950484';//门店收费人ID
        $this->config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
        $this->config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
        $this->config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
        $this->config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $order_pid=$result['order_pid'];

        $data=array();
        $data[0]['subMerId']='3089991727301QX';
        $data[0]['subStoreId']='3089991727301QX0001';
        $data[0]['subOrderId']=$order_pid.'_1';
        $data[0]['txnAmt']='2';

        $data[1]['subMerId']='3089991727301QX';
        $data[1]['subStoreId']='3089991727301QX0001';
        $data[1]['subOrderId']=$order_pid.'_2';
        $data[1]['txnAmt']='3';

        $data[2]['subMerId']='3089991727301QU';
        $data[2]['subStoreId']='3089991727301QU0001';
        $data[2]['subOrderId']=$order_pid.'_3';
        $data[2]['txnAmt']='1';

        $data[3]['subMerId']='3089991727301QU';
        $data[3]['subStoreId']='3089991727301QU0001';
        $data[3]['subOrderId']=$order_pid.'_4';
        $data[3]['txnAmt']='4';


        $orderInfo = [
            'orderId'    => $order_pid,
            'payValidTime'=> 3600*24*30,
            'txnAmt' => 10,//交易金额，单位为分（必传）
            "body"=> "测试支付,总计需支付0.1元",
            "termId"=> 'HZT000S8',
            "subOrderList"=> json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
        ];

        $result = $this->qrcodeapply($orderInfo);

        if($result){

            $result['biz_content']=str_replace("qr.95516.com","payment-uat.cs.cmburl.cn",$result['biz_content']);

            header('Content-Type:image/png');
//            $updata = array();
//            $updata['pay_order_no'] = json_decode($result['biz_content'],1)['cmbOrderId'];
//            $updata['pay_outpidjson'] = $result['biz_content'];
//            $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$pay_pid}'", $updata);
            $codeUrl = json_decode($result['biz_content'],1)['qrCode'];
            require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
            $QRcode = new \QRcode();
            $errorCorrectionLevel = 'H';//容错级别
            $matrixPointSize = 8;//生成图片大小
            echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
        }else{
            $this->error = true;
            echo "无法生成支付二维码".$this->errortip;
        }


    }

    function OrderStatusQuery($result){

        $this->config['mer_id'] = '3089991727301QS';
        $this->config['user_id'] = 'V078950484';//门店收费人ID
        $this->config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
        $this->config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
        $this->config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
        $this->config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $order_pid=$result['order_pid'];

        $orderInfo = [
            'orderId'    => $order_pid
        ];

        $result = $this->orderquery($orderInfo);

        debug($result);exit;


    }

    function cancelOrder($result){

        $this->config['mer_id'] = '3089991727301QS';
        $this->config['user_id'] = 'V078950484';//门店收费人ID
        $this->config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
        $this->config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
        $this->config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
        $this->config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $order_pid=$result['order_pid'];

        $orderInfo = [
            'origOrderId'    => $order_pid
        ];

        $result = $this->closeOrder($orderInfo);

        debug($result);exit;


    }

    function refundOrderMore($result){

        $this->config['mer_id'] = '3089991727301QS';
        $this->config['user_id'] = 'V078950484';//门店收费人ID
        $this->config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
        $this->config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
        $this->config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
        $this->config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $order_pid=$result['order_pid'];


        $data=array();
        $data[0]['subMerId']='3089991727301QX';
        $data[0]['subStoreId']='3089991727301QX0001';
        $data[0]['subOrigOrderId']=$order_pid.'_1';
        $data[0]['subOrderId']='RT_'.$order_pid.'_1';
        $data[0]['subRefundAmt']='2';
        $data[0]['subTxnAmt']='2';

        $data[1]['subMerId']='3089991727301QX';
        $data[1]['subStoreId']='3089991727301QX0001';
        $data[1]['subOrigOrderId']=$order_pid.'_2';
        $data[1]['subOrderId']='RT_'.$order_pid.'_2';
        $data[1]['subRefundAmt']='3';
        $data[1]['subTxnAmt']='3';

        $data[2]['subMerId']='3089991727301QU';
        $data[2]['subStoreId']='3089991727301QU0001';
        $data[2]['subOrigOrderId']=$order_pid.'_3';
        $data[2]['subOrderId']='RT_'.$order_pid.'_3';
        $data[2]['subRefundAmt']='1';
        $data[2]['subTxnAmt']='1';

        $data[3]['subMerId']='3089991727301QU';
        $data[3]['subStoreId']='3089991727301QU0001';
        $data[3]['subOrigOrderId']=$order_pid.'_4';
        $data[3]['subOrderId']='RT_'.$order_pid.'_4';
        $data[3]['subRefundAmt']='4';
        $data[3]['subTxnAmt']='4';



        $orderInfo = [
            'orderId'    => 'RT_'.$order_pid,
            'origOrderId'    => $order_pid,
            'txnAmt'    => 10,
            'refundAmt'    => 10,
            "refundSubOrderList"=> json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
        ];

        $result = $this->refundMore($orderInfo);

        debug($result);exit;


    }

    function refundOrder($result){

        $this->config['mer_id'] = '3089991727301QS';
        $this->config['user_id'] = 'V078950484';//门店收费人ID
        $this->config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
        $this->config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
        $this->config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
        $this->config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $order_pid=$result['order_pid'];

        $orderInfo = [
            'orderId'    => 'RT_'.$order_pid,
            'origOrderId'    => $order_pid,
            'txnAmt'    => 10,
            'refundAmt'    => 10
        ];

        $result = $this->refund($orderInfo);

        debug($result);exit;


    }

    function refundqueryOrder($result){

        $this->config['mer_id'] = '3089991727301QS';
        $this->config['user_id'] = 'V078950484';//门店收费人ID
        $this->config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';//招行验签公钥
        $this->config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';//商户私钥
        $this->config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';//appid
        $this->config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';//秘钥

        $order_pid=$result['order_pid'];

        $orderInfo = [
            'orderId'    => 'RT_'.$order_pid,
        ];

        $result = $this->refundquery($orderInfo);

        debug($result);exit;


    }


    //收款码支付入口逻辑处理
    function OrderPay($pay_pid){

        $sql = "select b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

        $orderPay = $this->DataControl->selectOne($sql);

        if($this->companyOne['company_merId']=='' || $this->companyOne['company_userId']=='' || $this->companyOne['company_termId']==''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $result = array();
            $result['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }


        if($orderPay['mergepay_order_no'] != '' && $orderPay['mergepay_backjson'] != ''){

            $bool=$this->mergeOrderStatusQuery($pay_pid);

            $mergePayOne=$this->DataControl->getFieldOne("smc_payfee_mergeorder_mergepay","mergepay_issuccess","mergepay_pid='{$pay_pid}'");


            if($mergePayOne['mergepay_issuccess']==1){
                $this->error = true;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '支付已完成！';
                $result = array();
                $result['errortip'] = '支付已完成！';
                $bakresult['result'] = $result;
                return $bakresult;
            }elseif($mergePayOne['mergepay_issuccess']==-1){
                $this->error = true;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '支付已取消！';
                $result = array();
                $result['errortip'] = '支付已取消！';
                $bakresult['result'] = $result;
                return $bakresult;
            }

            header('Content-Type:image/png');
            $outpidarray = json_decode($orderPay['mergepay_backjson'], true);
            $codeUrl = $outpidarray['qrCode'];
            require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
            $QRcode = new \QRcode();
            $errorCorrectionLevel = 'H';//容错级别
            $matrixPointSize = 8;//生成图片大小
            echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            exit;
        }

        if($orderPay['mergepay_issuccess'] == '0'){

            $sql = "select a.order_pid,a.order_arrearageprice,b.companies_subMerId,c.companies_subStoreId
                    from smc_payfee_order as a
                    inner join gmc_code_companies as b on b.companies_id=a.companies_id
                    inner join smc_school_companies as c on c.companies_id=a.companies_id and c.school_id=a.school_id
                    where a.mergeorder_pid='{$orderPay['mergeorder_pid']}' and a.order_arrearageprice>0
                    group by a.order_pid
                    ";

            $orderList=$this->DataControl->selectClear($sql);

            if($orderList){

                $payOrderArray=array();

                foreach($orderList as $orderOne){

                    if($orderOne['companies_subMerId']=='' || $orderOne['companies_subStoreId']==''){
                        $this->error = true;
                        $bakresult = array();
                        $bakresult['error'] = '1';
                        $bakresult['errortip'] = '未设置组合支付子单商户号或子单门店号！';
                        $result = array();
                        $result['errortip'] = '未设置组合支付子单商户号或子单门店号！';
                        $bakresult['result'] = $result;
                        return $bakresult;
                    }

                    do {
                        $paypid = $this->createOrderPid('HDZF');
                    } while ($this->DataControl->selectOne("select mergepayitem_id from smc_payfee_mergeorder_mergepayitem where mergepayitem_pid='{$paypid}' limit 0,1"));

                    $data=array();
                    $data['mergeorder_pid']=$orderPay['mergeorder_pid'];
                    $data['mergepay_pid']=$pay_pid;
                    $data['mergepayitem_pid']=$paypid;
                    $data['order_pid']=$orderOne['order_pid'];
                    $data['mergepayitem_price']=$orderOne['order_arrearageprice'];
                    $data['mergepayitem_createtime']=time();
                    $this->DataControl->insertData("smc_payfee_mergeorder_mergepayitem",$data);


                    $data=array();
                    $data['subMerId']=$orderOne['companies_subMerId'];
                    $data['subStoreId']=$orderOne['companies_subStoreId'];
                    $data['subOrderId']=$paypid;
                    $data['txnAmt']=$orderOne['order_arrearageprice']*100;

                    $payOrderArray[]=$data;
                }
            }

            $orderInfo = [
                'orderId'    => $pay_pid,
                'payValidTime'=> 3600*24*30,
                'txnAmt' => $orderPay['mergepay_price']*100,//交易金额，单位为分（必传）
                "body"=> "组合支付,总计需支付{$orderPay['mergepay_price']}元",
                "termId"=> $this->companyOne['company_termId'],
                "subOrderList"=> json_encode($payOrderArray,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
            ];

            $result = $this->qrcodeapply($orderInfo);

            if($result){

//                $result['biz_content']=str_replace("qr.95516.com","payment-uat.cs.cmburl.cn",$result['biz_content']);

                header('Content-Type:image/png');
                $updata = array();
                $updata['mergepay_order_no'] = json_decode($result['biz_content'],1)['cmbOrderId'];
                $updata['mergepay_backjson'] = $result['biz_content'];
                $this->DataControl->updateData("smc_payfee_mergeorder_mergepay", "mergepay_pid='{$pay_pid}'", $updata);

                $codeUrl = json_decode($result['biz_content'],1)['qrCode'];
                require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
                $QRcode = new \QRcode();
                $errorCorrectionLevel = 'H';//容错级别
                $matrixPointSize = 8;//生成图片大小
                echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            }else{
                $this->error = true;
                echo "无法生成支付二维码".$this->errortip;
            }
        }else{
            $this->error = true;
            echo "订单支付错误";
        }

    }

    function onlineOrderPay($pay_pid,$paymenttype,$token='')
    {
        if($paymenttype == 'wechat'){
            $PayResult = $this->WxPay($pay_pid,$token);
            
        }elseif($paymenttype == 'alipay'){ 
            $PayResult = $this->aliPay($pay_pid,$token);
        
        }

        return $PayResult['result'];
    }

    

    function ordinaryOnlineOrderPay($pay_pid,$paymenttype,$token='')
    {
        if($paymenttype == 'wechat'){
            $PayResult = $this->ordinaryWxPay($pay_pid,$token);
            
        }elseif($paymenttype == 'alipay'){
            $PayResult = $this->ordinaryAliPay($pay_pid,$token);
            
        }

        return $PayResult['result'];
    }

    function ordinaryOrderPay($pay_pid){

        $sql = "select b.company_id,b.school_id,a.pay_order_no,a.pay_outpidjson,a.pay_issuccess,a.pay_price,a.pay_pid,b.order_pid 
                from smc_payfee_order_pay as a,smc_payfee_order as b 
                where b.order_pid=a.order_pid and a.pay_pid='{$pay_pid}'";

        $orderPay = $this->DataControl->selectOne($sql);

        if($this->companyOne['company_merId']=='' || $this->companyOne['company_userId']=='' || $this->companyOne['company_termId']==''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $result = array();
            $result['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }


        if($orderPay['pay_order_no'] != '' && $orderPay['pay_outpidjson'] != ''){

            $bool=$this->mergeOrdinaryOrderStatusQuery($pay_pid);

            $OrderPayOne=$this->DataControl->getFieldOne("smc_payfee_order_pay","pay_issuccess","pay_pid='{$pay_pid}'");


            if($OrderPayOne['pay_issuccess']==1){
                $this->error = true;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '支付已完成！';
                $result = array();
                $result['errortip'] = '支付已完成！';
                $bakresult['result'] = $result;
                return $bakresult;
            }elseif($OrderPayOne['pay_issuccess']==-1){
                $this->error = true;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '支付已取消！';
                $result = array();
                $result['errortip'] = '支付已取消！';
                $bakresult['result'] = $result;
                return $bakresult;
            }


            header('Content-Type:image/png');
            $outpidarray = json_decode($orderPay['pay_outpidjson'], true);
            $codeUrl = $outpidarray['qrCode'];
            require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
            $QRcode = new \QRcode();
            $errorCorrectionLevel = 'H';//容错级别
            $matrixPointSize = 8;//生成图片大小
            echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            exit;
        }

        if($orderPay['pay_issuccess'] == '0'){

            $sql = "select a.order_pid,a.order_arrearageprice,b.companies_subMerId,c.companies_subStoreId
                    from smc_payfee_order as a
                    inner join gmc_code_companies as b on b.companies_id=a.companies_id
                    inner join smc_school_companies as c on c.companies_id=a.companies_id and c.school_id=a.school_id
                    where a.order_pid='{$orderPay['order_pid']}' and a.order_arrearageprice>0
                    group by a.order_pid
                    ";

            $orderList=$this->DataControl->selectClear($sql);

            if($orderList){

                $payOrderArray=array();

                foreach($orderList as $orderOne){

                    if($orderOne['companies_subMerId']=='' || $orderOne['companies_subStoreId']==''){
                        $this->error = true;
                        $bakresult = array();
                        $bakresult['error'] = '1';
                        $bakresult['errortip'] = '未设置组合支付子单商户号或子单门店号！';
                        $result = array();
                        $result['errortip'] = '未设置组合支付子单商户号或子单门店号！';
                        $bakresult['result'] = $result;
                        return $bakresult;
                    }

                    $data=array();
                    $data['subMerId']=$orderOne['companies_subMerId'];
                    $data['subStoreId']=$orderOne['companies_subStoreId'];
                    $data['subOrderId']='ZH'.$pay_pid;
                    $data['txnAmt']=$orderOne['order_arrearageprice']*100;

                    $payOrderArray[]=$data;
                }
            }

            $orderInfo = [
                'orderId'    => $pay_pid,
                'payValidTime'=> 3600*24*30,
                'txnAmt' => $orderPay['pay_price']*100,//交易金额，单位为分（必传）
                "body"=> "组合支付,总计需支付{$orderPay['pay_price']}元",
                "termId"=> $this->companyOne['company_termId'],
                "subOrderList"=> json_encode($payOrderArray,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES)
            ];

            $result = $this->qrcodeapply($orderInfo);

            if($result){

//                $result['biz_content']=str_replace("qr.95516.com","payment-uat.cs.cmburl.cn",$result['biz_content']);

                header('Content-Type:image/png');
                $updata = array();
                $updata['pay_order_no'] = json_decode($result['biz_content'],1)['cmbOrderId'];
                $updata['pay_outpidjson'] = $result['biz_content'];
                $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$pay_pid}'", $updata);

                $codeUrl = json_decode($result['biz_content'],1)['qrCode'];
                require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
                $QRcode = new \QRcode();
                $errorCorrectionLevel = 'H';//容错级别
                $matrixPointSize = 8;//生成图片大小
                echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            }else{
                $this->error = true;
                echo "无法生成支付二维码".$this->errortip;
            }
        }else{
            $this->error = true;
            echo "订单支付错误";
        }
    }

    function createOrderPid($initial){
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)].$Str[rand(0,34)];
        $rangtime = date("ymdHis",time());
        $rangnum = rand(10000,99999);
        $OrderPID = $initial.$rangtr.$rangtime.$rangnum;
        return $OrderPID;
    }

    //支付回调处理
    function BoingPayBak($REQUESTARRAY){

        $eventArray = json_decode($REQUESTARRAY['biz_content'],1);

        $sql = "select b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid,a.mergepay_pid,b.staffer_id
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$eventArray['orderId']}'";

        $orderPay=$this->DataControl->selectOne($sql);

        // $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderPay['company_id']}' AND account_class = '1'");

        if($orderPay && $orderPay['mergepay_issuccess'] == '0'){

            $publicArray=array();
            $publicArray['company_id']=$orderPay['company_id'];
            $publicArray['school_id']=$orderPay['school_id'];
            $publicArray['staffer_id']=$orderPay['staffer_id'];

            $Model = new \Model\Smc\MergeOrderPayModel($publicArray);

            $data=array();
            $data['mergeorder_pid']=$orderPay['mergeorder_pid'];
            $data['mergepay_pid']=$orderPay['mergepay_pid'];
            $data['mergepaylog_actualprice']=$orderPay['mergepay_price'];
            $data['eventArray']=$eventArray;

            $Model->successPayMergeOrder($data);

        }
        exit(1);

    }

    function ordinaryBoingPayBak($REQUESTARRAY){

        $eventArray = json_decode($REQUESTARRAY['biz_content'],1);

        $sql = "select b.company_id,b.school_id,a.pay_order_no,a.pay_outpidjson,a.pay_issuccess,a.pay_price,a.order_pid,a.pay_pid,b.staffer_id
                from smc_payfee_order_pay as a,smc_payfee_order as b 
                where b.order_pid=a.order_pid and a.pay_pid='{$eventArray['orderId']}'";

        $orderPay=$this->DataControl->selectOne($sql);

        // $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderPay['company_id']}' AND account_class = '1'");

        if($orderPay && $orderPay['pay_issuccess'] == '0'){

            $publiclist = array();
            $publiclist['company_id'] = $orderPay['company_id'];
            $publiclist['school_id'] = $orderPay['school_id'];
            $publiclist['staffer_id'] = $orderPay['staffer_id'];
            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderPay['order_pid']);
//            $createtime = date("Y-m-d H:i:s",strtotime($eventArray['txnTime']));
            $createtime = date("Y-m-d H:i:s",strtotime($eventArray['endDate'].$eventArray['endTime']));

            if($eventArray['payType'] == 'ZF'){
                $paytype_code = 'alipay';
            }elseif($eventArray['payType'] == 'WX'){
                $paytype_code = 'wechat';
            }elseif($eventArray['payType'] == 'YL'){
                $paytype_code = 'yinlian';
            }elseif($eventArray['payType'] == 'EC'){
                $paytype_code = 'dcep';
            }else{
                $paytype_code='';
            }

            $bakjson=addslashes($REQUESTARRAY['biz_content']);

            $orderPayModel->orderPaylog( $orderPay['pay_pid'],$eventArray['thirdOrderId'],0,'',$paytype_code,$createtime,'0',$bakjson,'','zhcmb');

        }
        exit(1);

    }

    //取消或者失效的订单 对招行进行撤销操作
    function OrderPayCancel($pay_pid){

        $sql = "select b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

        $orderPay = $this->DataControl->selectOne($sql);

        if($this->companyOne['company_merId']=='' || $this->companyOne['company_userId']=='' || $this->companyOne['company_termId']==''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $result = array();
            $result['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }


        if($orderPay['mergepay_issuccess'] == '-1' ){

            $orderInfo = [
                'origOrderId'    => $pay_pid
            ];

            $result = $this->closeOrder($orderInfo);

            if($result['respCode']=='SUCCESS'){
                $this->error = true;
                $bakresult = array();
                $bakresult['error'] = '0';
                $bakresult['errortip'] = '对应订单撤销成功！';
                $bakresult['result'] = $result;
                return $bakresult;
            }else{
                $this->error = false;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '对应订单撤销失败！';
                $result = array();
                $result['errortip'] = '对应订单撤销失败！';
                $bakresult['result'] = $result;
                return $bakresult;
            }
        }
    }

    function ordinaryOrderPayCancel($pay_pid){

        $sql = "select b.company_id,b.school_id,a.pay_order_no,a.pay_outpidjson,a.pay_issuccess,a.pay_price,a.order_pid,a.pay_pid
                from smc_payfee_order_pay as a,smc_payfee_order as b 
                where b.order_pid=a.order_pid and a.pay_pid='{$pay_pid}'";

        $orderPay=$this->DataControl->selectOne($sql);

        if($this->companyOne['company_merId']=='' || $this->companyOne['company_userId']=='' || $this->companyOne['company_termId']==''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $result = array();
            $result['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }


        if($orderPay['pay_issuccess'] == '-1'){

            $orderInfo = [
                'origOrderId'    => $pay_pid
            ];
            $result = $this->closeOrder($orderInfo);

            if($result['respCode']=='SUCCESS'){
                $this->error = true;
                $bakresult = array();
                $bakresult['error'] = '0';
                $bakresult['errortip'] = '对应订单撤销成功！';
                $bakresult['result'] = $result;
                return $bakresult;
            }else{
                $this->error = false;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '对应订单撤销失败！';
                $result = array();
                $result['errortip'] = '对应订单撤销失败！';
                $bakresult['result'] = $result;
                return $bakresult;
            }
        }
    }

    function mergeOrderStatusQuery($pay_pid){

        if($this->companyOne['company_merId']=='' || $this->companyOne['company_userId']=='' || $this->companyOne['company_termId']==''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $result = array();
            $result['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        $orderInfo = [
            'orderId'    => $pay_pid
        ];

        $result = $this->orderquery($orderInfo);
        if($result['returnCode']=='SUCCESS'){

            $eventArray=json_decode($result["biz_content"],1);
            if ($eventArray["tradeState"] == "S") {

                $sql = "select b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid,a.mergepay_pid 
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b 
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

                $orderPay = $this->DataControl->selectOne($sql);

                $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$orderPay['company_id']}' AND account_class = '1'");

                $publicArray=array();
                $publicArray['company_id']=$orderPay['company_id'];
                $publicArray['school_id']=$orderPay['school_id'];
                $publicArray['staffer_id']=$stafferOne['staffer_id'];

                $MergeOrderPayModel = new \Model\Smc\MergeOrderPayModel($publicArray);

                $data=array();
                $data['mergeorder_pid']=$orderPay['mergeorder_pid'];
                $data['mergepay_pid']=$orderPay['mergepay_pid'];
                $data['mergepaylog_actualprice']=$orderPay['mergepay_price'];
                $data['eventArray']=$eventArray;

                if($orderPay){
                    if ($orderPay['mergepay_issuccess'] == 0) {

                        $MergeOrderPayModel->successPayMergeOrder($data);
    
                    }elseif($orderPay['mergepay_issuccess'] == 1){

                        $MergeOrderPayModel->successPayOrder($data);
    
                    }
                
                    $this->error=0;
                    return true;
                }else{

                    $this->error=1;
                    return false;
                }
            }else{
                $this->error=1;
                return false;
            }
        }else{
            $this->error=1;
            return false;

        }



    }

    function mergeOrdinaryOrderStatusQuery($pay_pid){

        if($this->companyOne['company_merId']=='' || $this->companyOne['company_userId']=='' || $this->companyOne['company_termId']==''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $result = array();
            $result['errortip'] = '未设置组合支付商户号或收银员或终端号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        $orderInfo = [
            'orderId'    => $pay_pid
        ];

        $result = $this->orderquery($orderInfo);

        if($result['returnCode']=='SUCCESS'){

            $eventArray=json_decode($result["biz_content"],1);
            if ($eventArray["tradeState"] == "S") {

                $sql = "select b.company_id,b.school_id,a.mergepay_order_no,a.mergepay_backjson,a.mergepay_issuccess,a.mergepay_price,a.mergeorder_pid,a.mergepay_pid
                from smc_payfee_mergeorder_mergepay as a,smc_payfee_mergeorder as b
                where b.mergeorder_pid=a.mergeorder_pid and a.mergepay_pid='{$pay_pid}'";

                $orderPay = $this->DataControl->selectOne($sql);

                $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$orderPay['company_id']}' AND account_class = '1'");

                if ($orderPay && $orderPay['mergepay_issuccess'] == 0) {

                    $publiclist = array();
                    $publiclist['company_id'] = $orderPay['company_id'];
                    $publiclist['school_id'] = $orderPay['school_id'];
                    $publiclist['staffer_id'] = $stafferOne['staffer_id'];
                    $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderPay['order_pid']);
//            $createtime = date("Y-m-d H:i:s",strtotime($eventArray['txnTime']));
                    $createtime = date("Y-m-d H:i:s",strtotime($eventArray['endDate'].$eventArray['endTime']));

                    if($eventArray['payType'] == 'ZF'){
                        $paytype_code = 'alipay';
                    }elseif($eventArray['payType'] == 'WX'){
                        $paytype_code = 'wechat';
                    }elseif($eventArray['payType'] == 'YL'){
                        $paytype_code = 'yinlian';
                    }elseif($eventArray['payType'] == 'EC'){
                        $paytype_code = 'dcep';
                    }else{
                        $paytype_code='';
                    }

                    $bakjson=addslashes($result['biz_content']);

                    $orderPayModel->orderPaylog( $orderPay['pay_pid'],$eventArray['thirdOrderId'],0,'',$paytype_code,$createtime,'0',$bakjson,'','zhcmb');

                }
                $this->error=0;
                return true;
            }else{
                $this->error=1;
                return false;
            }
        }else{
            $this->error=1;
            return false;

        }



    }


    function url(){
        /*收款码申请：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/qrcodeapply
        支付结果查询：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/orderquery
        退款申请：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/refund
        退款结果查询：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/refundquery
        付款码收款：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/pay
        微信统一下单：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/onlinepay
        付款码收款撤销：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/cancel
        关闭订单：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/close
        支付宝服务窗支付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/servpay
        支付宝native支付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/zfbqrcode
        微信native支付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/wxqrcode
        对账单下载地址获取：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/statementurl
        秘钥设置：
        https://api.cmburl.cn:8065/polypay/v1.0/mchkey/keyset
        订单二维码申请
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/orderqrcodeapply
        微信小程序下单：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/MiniAppOrderApply
        银联云闪付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/cloudpay
        数字人民币统一下单
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/unifiedOrder
        数字人民币统一支付
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/unifiedPayment
        数字人民币子钱包支付
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/subwalletpay
        数字人民币子钱包支付-带合约
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/contractsubwalletpay


        因交易结果通知是银行主动通知给商户的，故不提供商户主动调用的URL。

        生产接入地址：
        收款码申请：
        https://api.cmbchina.com/polypay/v1.0/mchorders/qrcodeapply
        支付结果查询：
        https://api.cmbchina.com/polypay/v1.0/mchorders/orderquery
        退款申请：
        https://api.cmbchina.com/polypay/v1.0/mchorders/refund
        退款结果查询：
        https://api.cmbchina.com/polypay/v1.0/mchorders/refundquery
        付款码收款：
        https://api.cmbchina.com/polypay/v1.0/mchorders/pay
        微信统一下单：
        https://api.cmbchina.com/polypay/v1.0/mchorders/onlinepay
        付款码收款撤销：
        https://api.cmbchina.com/polypay/v1.0/mchorders/cancel
        关闭订单：
        https://api.cmbchina.com/polypay/v1.0/mchorders/close
        支付宝服务窗支付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/servpay
        支付宝native支付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/zfbqrcode
        微信native支付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/wxqrcode
        对账单下载地址获取：
        https://api.cmbchina.com/polypay/v1.0/mchorders/statementurl
        秘钥设置：
        https://api.cmbchina.com/polypay/v1.0/mchkey/keyset
        订单二维码申请：
        https://api.cmbchina.com/polypay/v1.0/mchorders/orderqrcodeapply
        微信小程序下单：
        https://api.cmbchina.com/polypay/v1.0/mchorders/MiniAppOrderApply
        银联云闪付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/cloudpay
        数字人民币统一下单
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/unifiedOrder
        数字人民币统一支付
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/unifiedPayment
        数字人民币子钱包支付
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/subwalletpay
        数字人民币子钱包支付-带合约
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/contractsubwalletpay*/
    }

    /**招行加密验签测试**/
    function testVery(){
        // 待加密的数据
        $data = json_decode('{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1"}');
        $params =  urldecode(http_build_query($data));
        $params = 'biz_content={"termId":"term0525","orderId":"2018060616435200002","notifyUrl":"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify","merId":"20180524200752LWW","payValidTime":"100000","currencyCode":"156","userId":"N111555974","txnAmt":"134","body":"收款测试0606"}&encoding=UTF-8&signMethod=01&version=0.0.1';
        //生成签名开始
        $sm2    = new RtSm2("base64");
        //将用户id填充到16个字节
        $userId = '1234567812345678';
        //使用rsa的私钥生成签名(注意这里是私钥!私钥!私钥!)
        $sign   = $sm2->doSign($params, $this->config['privateKey'], $userId);
        var_dump($sign);

        $signString =  '{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1","sign":"' . $sign . '"}';
        var_dump($signString);

        echo "\n---------明文密钥验签---------------------------\n";
        $publicKey = strtoupper($this->base64Hex($this->config['publicKey']));
        var_dump($publicKey);
        var_dump($this->config['publicHead']);
        $publicKey = str_replace($this->config['publicHead'],"",$publicKey);
        var_dump($publicKey);

        $verify = $sm2->verifySign( $params, $sign, $publicKey, $userId );
        var_dump($verify);
    }

    /**
     * 收款码申请
     *
     **/
    function qrcodeapply($params){
        return $this->handler('api/scanPay/order', $params,1);
    }

    /**
     * 支付结果查询
     *
     **/
    function orderquery($params){
        return $this->handler('api/scanPay/orderStatusQuery', $params,0);
    }

    /**
     * 退款申请
     *
     **/
    function refund($params){
        return $this->handler('api/scanPay/refund', $params,0);
    }
    /**
     * 多笔子订单退款申请
     *
     **/
    function refundMore($params){
        return $this->handler('api/combine/synrefund', $params,0);
    }

    /**
     * 退款查询
     *
     **/
    function refundquery($params){
        return $this->handler('api/scanPay/refundStatusQuery', $params,0);
    }

    /**
     * 关闭订单
     *
     **/
    function closeOrder($params){
        return $this->handler('api/scanPay/close', $params,0);
    }

    /**
     * 付款码收款
     *
     **/
    function pay($params){
        return $this->handler('mchorders/pay', $params,0);
    }

    /**
     * 付款码收款撤销
     *
     **/
    function cancelPay($params){
        return $this->handler('mchorders/cancel', $params,0);
    }

    /**
     * 微信统一下单
     *
     **/
    function onlinepay($params){
        return $this->handler('mchorders/onlinepay', $params,0);
    }

    /**
     * 微信小程序下单
     *
     **/
    function MiniAppOrderApply($params){
        return $this->handler('api/weixinPay/order', $params,1);
    }

    
    /**
     * 支付宝服务号下单
     *
     **/
    function AliPayOrderApply($params){
        return $this->handler('api/aliPay/order', $params,1);
    }

    /**
     * 对账单下载地址获取
     *
     **/
    function statementurl($params,$companies_id = 0){
        if($companies_id < 1){
            $this->error = true;
            $this->errortip = '主体ID不对';
            return false;
        }
        $sql = "SELECT c.companies_cmbheadappid,se.companies_appid,se.companies_secret,se.companies_publicKey,se.companies_privateKey
                FROM gmc_code_companies AS c,gmc_code_companies_seting as se 
                WHERE c.companies_id = '{$companies_id}' and c.companies_id=se.companies_id ";
        $storeOne = $this->DataControl->selectOne($sql);

        $this->config['mer_id'] = $storeOne['companies_cmbheadappid'];
        $this->config['publicKey'] = $storeOne['companies_publicKey'];//招行验签公钥
        $this->config['privateKey'] = $storeOne['companies_privateKey'];//商户私钥
        $this->config['appid'] = $storeOne['companies_appid'];//appid
        $this->config['secret'] = $storeOne['companies_secret'];//秘钥

        return $this->handlerDzd('mchorders/statementurl', $params,0);
    }

    //入参数据排序处理
    protected function bizContent($params,$has_notifyUrl=1){
//        $bizContent = [
//            'merId' => $this->config['mer_id'],
//            'userId' => $this->config['user_id'],
//            'notifyUrl' => $this->config['notify_url']
//        ];

        if($has_notifyUrl==1){
            $bizContent = [
                'merId' => $this->config['mer_id'],
                'userId' => $this->config['user_id'],
                'notifyUrl' => $this->config['notify_url']
            ];
        }else{
            $bizContent = [
                'merId' => $this->config['mer_id'],
                'userId' => $this->config['user_id'],
            ];
        }
//        $params = array_filter(array_merge($bizContent, $params), 'strlen');
        $params = array_merge($bizContent, $params);

        ksort($params);

        return  json_encode($params,JSON_UNESCAPED_UNICODE);
    }


    //公共调用方法
    protected function handler($pageurl, $params,$has_notifyUrl=1){
        $data = [
            'biz_content' => $this->bizContent($params,$has_notifyUrl),
            'encoding'   => 'UTF-8',//编码方式，固定为UTF-8(必传)
            'signMethod' => '02', //签名方法，固定为01，表示签名方式为RSA2(必传)
            'version'    => '0.0.1',//版本号，固定为0.0.1(必传字段)
        ];

        $apidata=array();
        $apidata['apilog_url']=$this->apiUlr.$pageurl;

        $sign = $this->datasign($data);
        $data['sign'] = $sign;

        $apidata['apilog_requet_json']=addslashes(json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));

        $result = $this->curl_post($this->apiUlr.$pageurl,$this->headers($sign),$data);

        $apidata['apilog_result']=json_decode($result, true)['returnCode'];
        $apidata['apilog_back_json']=addslashes($result);
        $apidata['apilog_time']=time();
        $this->DataControl->insertData("smc_hcmb_apilog",$apidata);

        if (false !== stripos($result, 'returnCode')) {
            if($apiResult = json_decode($result, true)){
                if($this->verifySign($apiResult)){
                    if($apiResult['returnCode'] == 'SUCCESS'){
                        return $apiResult;
                    }else{
                        $this->error = true;
                        $this->errortip = $apiResult['respMsg'];
                        return false;
                    }
                }else{
                    $this->error = true;
                    $this->errortip = "返回信息验签失败！";
                    return false;
                }
            }else{
                $this->error = true;
                $this->errortip = "返回信息无法解析Json字符，返回值：！".$result;;
                return false;
            }
        }else{
            $this->errortip = "返回信息有误，返回值：！".$result;
            return false;
        }
    }

    //返回值验签
    function verifySign($apiResult){
        $publicKey = strtoupper($this->base64Hex($this->config['publicKey']));
        $publicKey = str_replace($this->config['publicHead'],"",$publicKey);

        //剔除Sign键值
        $sign = $apiResult['sign'];
        unset($apiResult['sign']);
        //var_dump($apiResult);
        ksort($apiResult);
        //var_dump($apiResult);
        $urlparams = http_build_query($apiResult);
        //var_dump($urlparams);
        $params =  urldecode($urlparams);
        //var_dump($params);

        $sm2  = new RtSm2("base64");
        return $sm2->verifySign( $params, $sign, $publicKey, $this->config['userId']);
    }


    //参数加密
    private function datasign($params)
    {
        $sm2  = new RtSm2("base64");
        //$preparams =  json_encode($params,JSON_UNESCAPED_UNICODE);
        //var_dump($preparams);
        $urlparams = http_build_query($params);
        $params =  urldecode($urlparams);
        //var_dump($params);
        //var_dump(stripslashes($params));
        //var_dump($this->config);
        $sign = $sm2->doSign($params, $this->config['privateKey'], $this->config['userId']);
        //print_r($sign);
        return $sign;
    }

    //头部设置
    private function headers($sign)
    {

        $appid = $this->config['appid'];
        $secret = $this->config['secret'];
        $timestamp = time();
        $apisign = md5('appid='.$appid.'&secret='.$secret.'&sign='.$sign.'&timestamp='.$timestamp);
        $data =  [
            'appid'       => $appid,
            'timestamp'   => $timestamp,
            'apisign'     => $apisign
        ];
        return  $data;
    }


    function base64Hex($base64)
    {
        return unpack("H*", base64_decode($base64))[1];
    }

    /**  参数排序拼接
     * @param $array
     * @return string
     */
    function ToUrlParams(array $array){
        $buff = "";
        foreach ($array as $k => $v)
        {
            if($v != "" && !is_array($v)){
                $buff .= $k . "=" . $v . "&";
            }
        }
        $buff = trim($buff, "&");
        return $buff;
    }

    private function curl_post($url,$headers,$data)
    {
        $header= ["Content-type: application/json"];
        foreach ($headers as $key => $value) {
            $header[]=$key.':'. $value;
        }
        $data=json_encode($data,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }

        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }


    //公共调用方法 --- 招行对账单子使用
    protected function handlerDzd($pageurl, $params,$has_notifyUrl=1){
        $data = [
            'biz_content' => $this->bizContentDzd($params,$has_notifyUrl),
            'encoding'   => 'UTF-8',//编码方式，固定为UTF-8(必传)
            'signMethod' => '02', //签名方法，固定为01，表示签名方式为RSA2(必传)
            'version'    => '0.0.1',//版本号，固定为0.0.1(必传字段)
        ];

        $apidata=array();
        $apidata['apilog_url']=$this->apiUlr.$pageurl;
        $apidata['apilog_requet_json']=addslashes($data['biz_content']);

        $sign = $this->datasign($data);
        $data['sign'] = $sign;
        $result = $this->curl_post($this->apiUlr.$pageurl,$this->headers($sign),$data);

        $apidata['apilog_result']=json_decode($result, true)['returnCode'];
        $apidata['apilog_back_json']=addslashes($result);
        $apidata['apilog_time']=time();
//        $this->DataControl->insertData("smc_hcmb_apilog",$apidata);

        if (false !== stripos($result, 'returnCode')) {
            if($apiResult = json_decode($result, true)){
                $aa = $this->verifySign($apiResult);
                if($aa){
                    if($apiResult['returnCode'] == 'SUCCESS'){
                        return $apiResult;
                    }else{
                        $this->error = true;
                        $this->errortip = $apiResult['respMsg'];
                        return false;
                    }
                }else{
                    $this->error = true;
                    $this->errortip = "返回信息验签失败！";
                    return false;
                }
            }else{
                $this->error = true;
                $this->errortip = "返回信息无法解析Json字符，返回值：！".$result;;
                return false;
            }
        }else{
            $this->errortip = "返回信息有误，返回值：！".$result;
            return false;
        }
    }
    //入参数据排序处理
    protected function bizContentDzd($params,$has_notifyUrl=1){
        if($has_notifyUrl==1){
            $bizContent = [
                'merId' => $this->config['mer_id'],
                'notifyUrl' => $this->config['notify_url']
            ];
        }else{
            $bizContent = [
                'merId' => $this->config['mer_id']
            ];
        }

        $params = array_filter(array_merge($bizContent, $params), 'strlen');
        ksort($params);
        return  json_encode($params,JSON_UNESCAPED_UNICODE);
    }

    function WxPay($pay_pid, $token) {
        return $this->processOnlinePayment($pay_pid, $token, 'wechat', false);
    }

    function ordinaryWxPay($pay_pid, $token) {
        return $this->processOnlinePayment($pay_pid, $token, 'wechat', true);
    }

    function aliPay($pay_pid, $token) {
        return $this->processOnlinePayment($pay_pid, $token, 'alipay', false);
    }

    function ordinaryAliPay($pay_pid, $token) {
        return $this->processOnlinePayment($pay_pid, $token, 'alipay', true);
    }

    /**
     * 统一的在线支付处理方法
     * @param string $pay_pid 支付订单ID
     * @param string $token 授权token
     * @param string $paymentType 支付类型：'wechat' 或 'alipay'
     * @param bool $isOrdinary 是否为普通订单
     * @return array
     */
    private function processOnlinePayment($pay_pid, $token, $paymentType, $isOrdinary) {
        // 验证授权token
        if (empty($token)) {
            $errorMsg = $paymentType === 'wechat' ? '未获取微信授权！' : '未获取支付宝授权！';
            return $this->createErrorResponse($errorMsg);
        }

        // 查询订单状态
        $orderQueryResult = $this->queryOrderStatus($pay_pid);

        if ($orderQueryResult['success']) {
            // 根据支付类型选择不同的处理方法，都通过 isOrdinary 参数区分订单类型
            if ($paymentType === 'wechat') {
                $handleResult = $this->handleWxExistingOrder($pay_pid, $orderQueryResult['data'], $isOrdinary);
            } else {
                $handleResult = $this->handleAliExistingOrder($pay_pid, $orderQueryResult['data'], $isOrdinary);
            }
            
            // 如果处理结果不是 false，说明已经处理完成（成功支付或其他错误）
            if ($handleResult !== false) {
                // 如果返回的是新的支付编号（字符串），则使用新编号创建订单
                if (is_string($handleResult) && $handleResult !== $pay_pid) {
                    return $this->createNewOrder($handleResult, $token, $paymentType, $isOrdinary);
                }
                return $handleResult;
            }
            // 如果返回 false，说明需要创建新订单，继续到下面的逻辑
        } elseif (isset($orderQueryResult['orderNotExist'])) {
            // 订单不存在，直接创建新订单
            // 这里什么都不做，继续到下面创建新订单
        }

        // 创建新的支付订单
        return $this->createNewOrder($pay_pid, $token, $paymentType, $isOrdinary);
    }

    /**
     * 统一的创建新订单方法
     * @param string $pay_pid 支付订单ID
     * @param string $token 授权token
     * @param string $paymentType 支付类型：'wechat' 或 'alipay'
     * @param bool $isOrdinary 是否为普通订单
     * @return array
     */
    private function createNewOrder($pay_pid, $token, $paymentType, $isOrdinary) {
        if ($paymentType === 'wechat') {
            return $this->createNewWxPaymentOrder($pay_pid, $token, $isOrdinary);
        } else {
            return $this->createNewPaymentOrder($pay_pid, $token, $isOrdinary);
        }
    }

    /**
     * 查询订单状态 - 支付宝和微信通用
     * @param string $pay_pid 支付订单ID
     * @return array
     */
    private function queryOrderStatus($pay_pid) {
        $orderInfo = ['orderId' => $pay_pid];
        $result = $this->orderquery($orderInfo);

        if (isset($result['errCode']) && $result['errCode'] == 'ORDERID_NOT_EXIST') {
            return ['success' => false, 'orderNotExist' => true];
        } elseif ($result['returnCode'] == 'SUCCESS') {
            $bizContent = json_decode($result["biz_content"], true);
            $resultData = isset($bizContent['payData']) ? $bizContent['payData'] : $bizContent;
            $eventArray = $bizContent;
            
            return [
                'success' => true,
                'data' => [
                    'resultData' => $resultData,
                    'eventArray' => $eventArray
                ]
            ];
        }
        
        return ['success' => false];
    }

    /**
     * 处理已存在的订单 - 按照 WxPay 方法的逻辑
     * @param string $pay_pid 支付订单ID
     * @param array $orderData 订单数据
     * @return mixed
     */
    private function handleExistingOrder($pay_pid, $orderData) {
        $eventArray = $orderData['eventArray'];
        
        // 检查 tradeState 字段是否存在，如果不存在说明是新格式，直接返回 false 让其创建新订单
        if (!isset($eventArray["tradeState"])) {
            return false;
        }
        
        // 按照 WxPay 方法的逻辑进行状态判断
        if ($eventArray["tradeState"] == "S") {
            // 订单已成功支付
            return $this->handleSuccessfulOrder($pay_pid, $eventArray);
            
        } elseif ($eventArray["tradeState"] == "P") {
            // 订单待支付，需要取消并允许重新创建
            return $this->handlePendingOrder($pay_pid);
            
        } else {
            // 其他情况按照 WxPay 逻辑，设置错误信息
            $this->error = true;
            $this->errortip = isset($eventArray['errMsg']) ? $eventArray['errMsg'] : '订单状态异常';
            return false;
        }
    }

    /**
     * 处理成功支付的订单
     * @param string $pay_pid 支付订单ID
     * @param array $eventArray 事件数据
     * @return bool
     */
    private function handleSuccessfulOrder($pay_pid, $eventArray) {
        $orderPay = $this->getOrderPayInfo($pay_pid);
        if (!$orderPay) {
            $this->error = 1;
            return false;
        }

        $stafferOne = $this->DataControl->getFieldOne(
            "smc_staffer", 
            "staffer_id", 
            "company_id='{$orderPay['company_id']}' AND account_class = '1'"
        );

        $publicArray = [
            'company_id' => $orderPay['company_id'],
            'school_id' => $orderPay['school_id'],
            'staffer_id' => $stafferOne['staffer_id']
        ];

        $MergeOrderPayModel = new \Model\Smc\MergeOrderPayModel($publicArray);

        $data = [
            'mergeorder_pid' => $orderPay['mergeorder_pid'],
            'mergepay_pid' => $orderPay['mergepay_pid'],
            'mergepaylog_actualprice' => $orderPay['mergepay_price'],
            'eventArray' => $eventArray
        ];

        if ($orderPay['mergepay_issuccess'] == 0) {
            $MergeOrderPayModel->successPayMergeOrder($data);
        } elseif ($orderPay['mergepay_issuccess'] == 1) {
            $MergeOrderPayModel->successPayOrder($data);
        }

        $this->error = 0;
        return true;
    }

    /**
     * 处理待支付订单
     * @param string $pay_pid 支付订单ID
     * @return bool 返回 false 表示需要继续创建新订单
     */
    private function handlePendingOrder($pay_pid) {
        $data = [
            'mergepay_issuccess' => -1,
            'mergepay_tradeState' => 'D',
            'mergepay_updatatime' => time()
        ];
        
        $this->DataControl->updateData(
            "smc_payfee_mergeorder_mergepay",
            "mergepay_pid='{$pay_pid}' and mergepay_issuccess=0",
            $data
        );

        $cancelResult = $this->OrderPayCancel($pay_pid);
        if (!$cancelResult) {
            $this->error = true;
            $this->errortip = '取消待支付订单失败';
            return $this->createErrorResponse('取消待支付订单失败');
        }
        
        // 成功取消待支付订单，返回 false 表示需要继续创建新订单
        return false;
    }

    /**
     * 创建新的支付订单
     * @param string $pay_pid 支付订单ID
     * @param string $token 支付宝授权token
     * @param bool $isOrdinary 是否为普通订单（默认false）
     * @return array
     */
    private function createNewPaymentOrder($pay_pid, $token, $isOrdinary = false) {
        if ($isOrdinary) {
            // 普通订单：使用普通订单查询方法
            $orderPay = $this->getWxOrdinaryOrderPayInfo($pay_pid);
            if (!$orderPay) {
                return $this->createErrorResponse('订单信息不存在1！');
            }
            
            // 构建普通订单的子订单列表
            $subOrderResult = $this->buildSubOrderList($orderPay['order_pid'], $pay_pid, true);
            $totalAmount = $orderPay['pay_price'];
            
        } else {
            // 合并订单：使用合并订单查询方法
            $orderPay = $this->getOrderPayInfo($pay_pid);
            if (!$orderPay) {
                return $this->createErrorResponse('订单信息不存在1！');
            }
            
            // 构建合并订单的子订单列表
            $subOrderResult = $this->buildSubOrderList($orderPay['mergeorder_pid'], $pay_pid, false);
            $totalAmount = $orderPay['mergepay_price'];
        }

        if (!$subOrderResult['success']) {
            return $subOrderResult['error_response'];
        }

        // 构建支付请求参数
        $orderInfo = [
            'orderId' => $pay_pid,
            'buyerId' => $token,
            'tradeScene' => 'OFFLINE',
            'payValidTime' => 3600 * 24 * 30,
            'txnAmt' => $totalAmount * 100, // 交易金额，单位为分
            'body' => "吉的堡",
            'termId' => $this->companyOne['company_termId'],
            'subOrderList' => json_encode($subOrderResult['data'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
        ];

        // 调用支付宝接口
        $result = $this->AliPayOrderApply($orderInfo);

        if ($result['returnCode'] == 'SUCCESS') {
            $resultData = json_decode($result['biz_content'], true);
            // 设置 error 为 0 表示成功
            $this->error = 0;
            return [
                'result' => [
                    'payInfo' => $resultData['payInfo']
                ]
            ];
        }

        return $this->createErrorResponse('订单支付生成失败！');
    }

    /**
     * 获取订单支付信息 - 支付宝和微信通用
     * @param string $pay_pid 支付订单ID
     * @return array|false
     */
    private function getOrderPayInfo($pay_pid) {
        $sql = "SELECT b.company_id, b.school_id, a.mergepay_order_no, a.mergepay_backjson, 
                       a.mergepay_issuccess, a.mergepay_price, a.mergeorder_pid, a.mergepay_pid, b.staffer_id 
                FROM smc_payfee_mergeorder_mergepay AS a, smc_payfee_mergeorder AS b 
                WHERE b.mergeorder_pid = a.mergeorder_pid AND a.mergepay_pid = '{$pay_pid}'";
        
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 构建子订单列表 - 支付宝和微信通用
     * @param string $mergeorder_pid 合并订单ID (普通订单时为order_pid)
     * @param string $pay_pid 支付订单ID
     * @param bool $isOrdinary 是否为普通订单（默认false）
     * @return array
     */
    private function buildSubOrderList($mergeorder_pid, $pay_pid, $isOrdinary = false) {
        if ($isOrdinary) {
            // 普通订单：根据单个order_pid查询
            $sql = "SELECT a.order_pid, a.order_arrearageprice, b.companies_subMerId, c.companies_subStoreId
                    FROM smc_payfee_order AS a
                    INNER JOIN gmc_code_companies AS b ON b.companies_id = a.companies_id
                    INNER JOIN smc_school_companies AS c ON c.companies_id = a.companies_id AND c.school_id = a.school_id
                    WHERE a.order_pid = '{$mergeorder_pid}' AND a.order_arrearageprice > 0
                    GROUP BY a.order_pid";
        } else {
            // 合并订单：根据mergeorder_pid查询
            $sql = "SELECT a.order_pid, a.order_arrearageprice, b.companies_subMerId, c.companies_subStoreId
                    FROM smc_payfee_order AS a
                    INNER JOIN gmc_code_companies AS b ON b.companies_id = a.companies_id
                    INNER JOIN smc_school_companies AS c ON c.companies_id = a.companies_id AND c.school_id = a.school_id
                    WHERE a.mergeorder_pid = '{$mergeorder_pid}' AND a.order_arrearageprice > 0
                    GROUP BY a.order_pid";
        }

        $orderList = $this->DataControl->selectClear($sql);
        
        if (!$orderList) {
            return ['success' => true, 'data' => []];
        }

        $payOrderArray = [];
        foreach ($orderList as $orderOne) {
            // 验证子商户信息
            if (empty($orderOne['companies_subMerId']) || empty($orderOne['companies_subStoreId'])) {
                return [
                    'success' => false,
                    'error_response' => $this->createErrorResponse('未设置组合支付子单商户号或子单门店号！')
                ];
            }

            // 根据订单类型处理子订单ID
            if ($isOrdinary) {
                // 普通订单：使用固定格式的ID，不保存记录
                $subOrderId = 'ZH' . $pay_pid;
            } else {
                // 标准订单：生成唯一ID并保存记录
                $subOrderId = $this->generateUniquePayPid();
                $this->saveSubOrderRecord($mergeorder_pid, $pay_pid, $orderOne, $subOrderId);
            }

            // 构建子订单数据
            $payOrderArray[] = [
                'subMerId' => $orderOne['companies_subMerId'],
                'subStoreId' => $orderOne['companies_subStoreId'],
                'subOrderId' => $subOrderId,
                'txnAmt' => $orderOne['order_arrearageprice'] * 100
            ];
        }

        return ['success' => true, 'data' => $payOrderArray];
    }

    /**
     * 生成唯一的支付ID
     * @return string
     */
    private function generateUniquePayPid() {
        do {
            $paypid = $this->createOrderPid('HDZF');
        } while ($this->DataControl->selectOne(
            "SELECT mergepayitem_id FROM smc_payfee_mergeorder_mergepayitem WHERE mergepayitem_pid='{$paypid}' LIMIT 0,1"
        ));
        
        return $paypid;
    }

    /**
     * 保存子订单记录
     * @param string $mergeorder_pid 合并订单ID
     * @param string $pay_pid 支付订单ID
     * @param array $orderInfo 订单信息
     * @param string $paypid 支付ID
     */
    private function saveSubOrderRecord($mergeorder_pid, $pay_pid, $orderInfo, $paypid) {
        $data = [
            'mergeorder_pid' => $mergeorder_pid,
            'mergepay_pid' => $pay_pid,
            'mergepayitem_pid' => $paypid,
            'order_pid' => $orderInfo['order_pid'],
            'mergepayitem_price' => $orderInfo['order_arrearageprice'],
            'mergepayitem_createtime' => time()
        ];
        
        $this->DataControl->insertData("smc_payfee_mergeorder_mergepayitem", $data);
    }

    /**
     * 创建统一的错误响应
     * @param string $errorMessage 错误消息
     * @return array
     */
    private function createErrorResponse($errorMessage) {
        $this->error = 1;
        $this->errortip = $errorMessage;
        
        return [
            'error' => '1',
            'errortip' => $errorMessage,
            'result' => [
                'errortip' => $errorMessage
            ]
        ];
    }

    

    // =========================== 微信支付相关私有方法 ===========================

    /**
     * 处理已存在的微信订单
     * @param string $pay_pid 支付订单ID
     * @param array $orderData 订单数据
     * @param bool $isOrdinary 是否为普通订单（默认false）
     * @return mixed
     */
    private function handleWxExistingOrder($pay_pid, $orderData, $isOrdinary = false) {
        $eventArray = $orderData['eventArray'];
        
        if ($eventArray["tradeState"] == "S") {
            // 订单已成功支付
            return $this->handleWxSuccessfulOrder($pay_pid, $eventArray, $isOrdinary);
            
        } elseif ($eventArray["tradeState"] == "P") {
            // 订单待支付，需要取消并允许重新创建
            return $this->handleWxPendingOrder($pay_pid, $isOrdinary);
            
        } else {
            // 其他情况设置错误信息
            $this->error = true;
            $this->errortip = isset($eventArray['errMsg']) ? $eventArray['errMsg'] : '订单状态异常';
            return false;
        }
    }

    /**
     * 处理成功支付的微信订单
     * @param string $pay_pid 支付订单ID
     * @param array $eventArray 事件数据
     * @param bool $isOrdinary 是否为普通订单（默认false）
     * @return bool
     */
    private function handleWxSuccessfulOrder($pay_pid, $eventArray, $isOrdinary = false) {
        if ($isOrdinary) {
            // 普通订单处理逻辑
            $orderPay = $this->getWxOrdinaryOrderPayInfo($pay_pid);
            if (!$orderPay) {
                $this->error = 1;
                return false;
            }

            $publiclist = [
                'company_id' => $orderPay['company_id'],
                'school_id' => $orderPay['school_id'],
                'staffer_id' => $orderPay['staffer_id']
            ];
            
            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderPay['order_pid']);
            
            $createtime = date("Y-m-d H:i:s", strtotime($eventArray['endDate'] . $eventArray['endTime']));
            
            // 支付类型映射
            $paytypeMapping = [
                'ZF' => 'alipay',
                'WX' => 'wechat', 
                'YL' => 'yinlian',
                'EC' => 'dcep'
            ];
            $paytype_code = isset($paytypeMapping[$eventArray['payType']]) ? $paytypeMapping[$eventArray['payType']] : '';
            
            $bakjson = addslashes(json_encode($eventArray));
            $orderPayModel->orderPaylog($orderPay['pay_pid'], $eventArray['thirdOrderId'], 0, '', $paytype_code, $createtime, '0', $bakjson, '', 'zhcmb');
            
            $this->error = 0;
            return true;
            
        } else {
            // 合并订单处理逻辑  
            $orderPay = $this->getOrderPayInfo($pay_pid);
            if (!$orderPay) {
                $this->error = 1;
                return false;
            }

            $stafferOne = $this->DataControl->getFieldOne(
                "smc_staffer", 
                "staffer_id", 
                "company_id='{$orderPay['company_id']}' AND account_class = '1'"
            );

            $publicArray = [
                'company_id' => $orderPay['company_id'],
                'school_id' => $orderPay['school_id'],
                'staffer_id' => $stafferOne['staffer_id']
            ];

            $MergeOrderPayModel = new \Model\Smc\MergeOrderPayModel($publicArray);

            $data = [
                'mergeorder_pid' => $orderPay['mergeorder_pid'],
                'mergepay_pid' => $orderPay['mergepay_pid'],
                'mergepaylog_actualprice' => $orderPay['mergepay_price'],
                'eventArray' => $eventArray
            ];

            if ($orderPay['mergepay_issuccess'] == 0) {
                $MergeOrderPayModel->successPayMergeOrder($data);
            } elseif ($orderPay['mergepay_issuccess'] == 1) {
                $MergeOrderPayModel->successPayOrder($data);
            }

            $this->error = 0;
            return true;
        }
    }

    /**
     * 处理待支付的微信订单
     * @param string $pay_pid 支付订单ID
     * @param bool $isOrdinary 是否为普通订单（默认false）
     * @return mixed 返回 false 表示需要继续创建新订单，返回字符串表示新生成的支付编号
     */
    private function handleWxPendingOrder($pay_pid, $isOrdinary = false) {
        if ($isOrdinary) {
            // 普通订单更新数据
            $data = [
                'pay_issuccess' => -1,
                'pay_updatatime' => time()
            ];
            $this->DataControl->updateData(
                "smc_payfee_order_pay",
                "pay_pid='{$pay_pid}' and pay_issuccess=0",
                $data
            );
            
            $cancelResult = $this->ordinaryOrderPayCancel($pay_pid);
            
            if (!$cancelResult) {
                $this->error = true;
                $this->errortip = '取消待支付订单失败';
                return $this->createErrorResponse('取消待支付订单失败');
            }
            
            // 成功取消订单后，使用OrderPayModel创建新的支付订单
            $originalOrderPayInfo = $this->getWxOrdinaryOrderPayInfo($pay_pid);
            if ($originalOrderPayInfo) {
                $publicarray = array();
                $publicarray['company_id'] = $originalOrderPayInfo['company_id'];
                $publicarray['school_id'] = $originalOrderPayInfo['school_id'];
                $publicarray['staffer_id'] = $originalOrderPayInfo['staffer_id'];
                $Model = new \Model\Smc\OrderPayModel($publicarray, $originalOrderPayInfo['order_pid']);

                $payArray = array();
                $payArray['paytype'] = 'H5Pay';
                $payArray['paytimes'] = '1';
                $payArray['paymenttype'] = 'wechat'; // 微信支付
                $payArray['order_pid'] = $originalOrderPayInfo['order_pid'];

                $data = $Model->createOrderPay($payArray);

                if($data){
                    // 返回新的支付编号
                    return $data['pay_pid'];
                }else{
                    $this->error = true;
                    $this->errortip = $Model->errortip;
                    return $this->createErrorResponse($Model->errortip);
                }
            }
            
        } else {
            // 合并订单更新数据
            $data = [
                'mergepay_issuccess' => -1,
                'mergepay_tradeState' => 'D',
                'mergepay_updatatime' => time()
            ];
            $this->DataControl->updateData(
                "smc_payfee_mergeorder_mergepay",
                "mergepay_pid='{$pay_pid}' and mergepay_issuccess=0",
                $data
            );
            
            $cancelResult = $this->OrderPayCancel($pay_pid);
            
            if (!$cancelResult) {
                $this->error = true;
                $this->errortip = '取消待支付订单失败';
                return $this->createErrorResponse('取消待支付订单失败');
            }
            
            // 成功取消订单后，使用MergeOrderPayModel创建新的支付订单
            $originalOrderPayInfo = $this->getOrderPayInfo($pay_pid);
            if ($originalOrderPayInfo) {
                $publicarray = array();
                $publicarray['company_id'] = $originalOrderPayInfo['company_id'];
                $publicarray['school_id'] = $originalOrderPayInfo['school_id'];
                $publicarray['staffer_id'] = $originalOrderPayInfo['staffer_id'];
                $OrderModel = new \Model\Smc\MergeOrderPayModel($publicarray, $originalOrderPayInfo['mergeorder_pid']);

                $payArray = array();
                $payArray['mergeorder_pid'] = $originalOrderPayInfo['mergeorder_pid'];
                $payArray['paymenttype'] = 'wechat'; // 微信支付

                $data = $OrderModel->mergeOrderPayH5($payArray);

                if($data){
                    // 返回新的支付编号
                    return $data['mergepay_pid'];
                }else{
                    $this->error = true;
                    $this->errortip = $OrderModel->errortip;
                    return $this->createErrorResponse($OrderModel->errortip);
                }
            }
        }
        
        // 成功取消待支付订单，返回 false 表示需要继续创建新订单（仅用于合并订单）
        return false;
    }

    /**
     * 创建新的微信支付订单
     * @param string $pay_pid 支付订单ID
     * @param string $token 微信授权token
     * @param bool $isOrdinary 是否为普通订单（默认false）
     * @return array
     */
    private function createNewWxPaymentOrder($pay_pid, $token, $isOrdinary = false) {
        if ($isOrdinary) {
            $orderPay = $this->getWxOrdinaryOrderPayInfo($pay_pid);
            if (!$orderPay) {
                return $this->createErrorResponse('订单信息不存在2！');
            }
            
            // 构建普通订单的子订单列表
            $subOrderResult = $this->buildSubOrderList($orderPay['order_pid'], $pay_pid, true);
            $totalAmount = $orderPay['pay_price'];
            
        } else {
            $orderPay = $this->getOrderPayInfo($pay_pid);
            if (!$orderPay) {
                return $this->createErrorResponse('订单信息不存在3！');
            }
            
            // 构建合并订单的子订单列表
            $subOrderResult = $this->buildSubOrderList($orderPay['mergeorder_pid'], $pay_pid, false);
            $totalAmount = $orderPay['mergepay_price'];
        }

        if (!$subOrderResult['success']) {
            return $subOrderResult['error_response'];
        }

        // 构建微信支付请求参数
        $orderInfo = [
            'orderId' => $pay_pid,
            'tradeType' => 'JSAPI',
            'tradeScene' => 'OFFLINE',
            'payValidTime' => 3600 * 24 * 30,
            'txnAmt' => $totalAmount * 100, // 交易金额，单位为分
            'body' => "吉的堡",
            'subAppId' => 'wx7378534679eeecdf', // 公众号id
            'subOpenId' => $token,
            'openId' => $token,
            'termId' => $this->companyOne['company_termId'],
            'spbillCreateIp' => real_ip(),
            'subOrderList' => json_encode($subOrderResult['data'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
        ];

        // 调用微信接口
        $result = $this->MiniAppOrderApply($orderInfo);

        if ($result['returnCode'] == 'SUCCESS') {
            return $this->createWxPayResponse($result);
        }

        return $this->createErrorResponse('订单支付生成失败！');
    }

    /**
     * 获取微信普通订单支付信息
     * @param string $pay_pid 支付订单ID
     * @return array|false
     */
    private function getWxOrdinaryOrderPayInfo($pay_pid) {
        $sql = "SELECT b.company_id, b.school_id, a.pay_order_no, a.pay_outpidjson, 
                       a.pay_issuccess, a.pay_price, a.pay_pid, b.order_pid, b.staffer_id 
                FROM smc_payfee_order_pay AS a, smc_payfee_order AS b 
                WHERE b.order_pid = a.order_pid AND a.pay_pid = '{$pay_pid}'";
        
        return $this->DataControl->selectOne($sql);
    }

    /**
     * 创建微信支付响应
     * @param array $result 支付接口返回结果
     * @return array
     */
    private function createWxPayResponse($result) {
        $resultData = json_decode($result['biz_content'], true)['payData'];

        $sysParams = [
            "bank_wx_app_id" => (string)$resultData['appId'],
            "appId" => (string)$resultData['appId'],
            "version" => (string)$result['version'],
            "timestamp" => (string)time(),
            "nonce_str" => (string)$resultData['nonceStr'],
            "prepay_id" => (string)substr($resultData['package'], strpos($resultData['package'], 'prepay_id=') + strlen('prepay_id=')),
            "sign_type" => (string)$resultData['signType'],
            "pay_sign" => (string)$resultData['paySign'],
            "package" => (string)$resultData['package'],
            "status" => 'OK'
        ];

        // 设置 error 为 0 表示成功
        $this->error = 0;
        return ['result' => $sysParams];
    }

    /**
     * 处理待支付的普通订单
     * @param string $pay_pid 支付订单ID
     * @return mixed 返回新的支付编号或错误响应
     */
    private function handleOrdinaryPendingOrder($pay_pid) {
        // 普通订单更新数据
        $data = [
            'pay_issuccess' => -1,
            'pay_updatatime' => time()
        ];
        $this->DataControl->updateData(
            "smc_payfee_order_pay",
            "pay_pid='{$pay_pid}' and pay_issuccess=0",
            $data
        );
        
        $cancelResult = $this->ordinaryOrderPayCancel($pay_pid);
        
        if (!$cancelResult) {
            $this->error = true;
            $this->errortip = '取消待支付订单失败';
            return $this->createErrorResponse('取消待支付订单失败');
        }
        
        // 成功取消订单后，使用OrderPayModel创建新的支付订单
        $originalOrderPayInfo = $this->getWxOrdinaryOrderPayInfo($pay_pid);
        if ($originalOrderPayInfo) {
            $publicarray = array();
            $publicarray['company_id'] = $originalOrderPayInfo['company_id'];
            $publicarray['school_id'] = $originalOrderPayInfo['school_id'];
            $publicarray['staffer_id'] = $originalOrderPayInfo['staffer_id'];
            $Model = new \Model\Smc\OrderPayModel($publicarray, $originalOrderPayInfo['order_pid']);

            $payArray = array();
            $payArray['paytype'] = 'H5Pay';
            $payArray['paytimes'] = '1';
            $payArray['paymenttype'] = 'alipay'; // 支付宝支付
            $payArray['order_pid'] = $originalOrderPayInfo['order_pid'];

            $data = $Model->createOrderPay($payArray);

            if($data){
                // 返回新的支付编号
                return $data['pay_pid'];
            }else{
                $this->error = true;
                $this->errortip = $Model->errortip;
                return $this->createErrorResponse($Model->errortip);
            }
        }
        
        return false;
    }

    /**
     * 处理已存在的合并订单
     * @param string $pay_pid 支付订单ID
     * @param array $orderData 订单数据
     * @return mixed
     */
    private function handleMergeExistingOrder($pay_pid, $orderData) {
        $eventArray = $orderData['eventArray'];
        
        // 检查 tradeState 字段是否存在，如果不存在说明是新格式，直接返回 false 让其创建新订单
        if (!isset($eventArray["tradeState"])) {
            return false;
        }
        
        // 按照 WxPay 方法的逻辑进行状态判断
        if ($eventArray["tradeState"] == "S") {
            // 订单已成功支付 - 处理合并订单的成功支付
            return $this->handleSuccessfulOrder($pay_pid, $eventArray);
            
        } elseif ($eventArray["tradeState"] == "P") {
            // 订单待支付，需要取消并生成新的支付编号
            return $this->handleMergePendingOrder($pay_pid);
            
        } else {
            // 其他情况设置错误信息
            $this->error = true;
            $this->errortip = isset($eventArray['errMsg']) ? $eventArray['errMsg'] : '订单状态异常';
            return false;
        }
    }

    /**
     * 处理待支付的合并订单
     * @param string $pay_pid 支付订单ID
     * @return mixed 返回新的支付编号或错误响应
     */
    private function handleMergePendingOrder($pay_pid) {
        // 合并订单更新数据
        $data = [
            'mergepay_issuccess' => -1,
            'mergepay_tradeState' => 'D',
            'mergepay_updatatime' => time()
        ];
        $this->DataControl->updateData(
            "smc_payfee_mergeorder_mergepay",
            "mergepay_pid='{$pay_pid}' and mergepay_issuccess=0",
            $data
        );
        
        $cancelResult = $this->OrderPayCancel($pay_pid);
        
        if (!$cancelResult) {
            $this->error = true;
            $this->errortip = '取消待支付订单失败';
            return $this->createErrorResponse('取消待支付订单失败');
        }
        
        // 成功取消订单后，使用MergeOrderPayModel创建新的支付订单
        $originalOrderPayInfo = $this->getOrderPayInfo($pay_pid);
        if ($originalOrderPayInfo) {
            $publicarray = array();
            $publicarray['company_id'] = $originalOrderPayInfo['company_id'];
            $publicarray['school_id'] = $originalOrderPayInfo['school_id'];
            $publicarray['staffer_id'] = $originalOrderPayInfo['staffer_id'];
            $OrderModel = new \Model\Smc\MergeOrderPayModel($publicarray, $originalOrderPayInfo['mergeorder_pid']);

            $payArray = array();
            $payArray['mergeorder_pid'] = $originalOrderPayInfo['mergeorder_pid'];
            $payArray['paymenttype'] = 'alipay'; // 支付宝支付

            $data = $OrderModel->mergeOrderPayH5($payArray);

            if($data){
                // 返回新的支付编号
                return $data['mergepay_pid'];
            }else{
                $this->error = true;
                $this->errortip = $OrderModel->errortip;
                return $this->createErrorResponse($OrderModel->errortip);
            }
        }
        
        return false;
    }



    /**
     * 处理已存在的普通订单
     * @param string $pay_pid 支付订单ID
     * @param array $orderData 订单数据
     * @return mixed
     */
    private function handleOrdinaryExistingOrder($pay_pid, $orderData) {
        $eventArray = $orderData['eventArray'];
        
        // 检查 tradeState 字段是否存在，如果不存在说明是新格式，直接返回 false 让其创建新订单
        if (!isset($eventArray["tradeState"])) {
            return false;
        }
        
        // 按照 WxPay 方法的逻辑进行状态判断
        if ($eventArray["tradeState"] == "S") {
            // 订单已成功支付 - 处理普通订单的成功支付
            return $this->handleOrdinarySuccessfulOrder($pay_pid, $eventArray);
            
        } elseif ($eventArray["tradeState"] == "P") {
            // 订单待支付，需要取消并生成新的支付编号
            return $this->handleOrdinaryPendingOrder($pay_pid);
            
        } else {
            // 其他情况设置错误信息
            $this->error = true;
            $this->errortip = isset($eventArray['errMsg']) ? $eventArray['errMsg'] : '订单状态异常';
            return false;
        }
    }

    /**
     * 处理成功支付的普通订单
     * @param string $pay_pid 支付订单ID
     * @param array $eventArray 事件数据
     * @return bool
     */
    private function handleOrdinarySuccessfulOrder($pay_pid, $eventArray) {
        $orderPay = $this->getWxOrdinaryOrderPayInfo($pay_pid);
        if (!$orderPay) {
            $this->error = 1;
            return false;
        }

        $publiclist = [
            'company_id' => $orderPay['company_id'],
            'school_id' => $orderPay['school_id'],
            'staffer_id' => $orderPay['staffer_id']
        ];
        
        $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderPay['order_pid']);
        
        $createtime = date("Y-m-d H:i:s", strtotime($eventArray['endDate'] . $eventArray['endTime']));
        
        // 支付类型映射
        $paytypeMapping = [
            'ZF' => 'alipay',
            'WX' => 'wechat', 
            'YL' => 'yinlian',
            'EC' => 'dcep'
        ];
        $paytype_code = isset($paytypeMapping[$eventArray['payType']]) ? $paytypeMapping[$eventArray['payType']] : '';
        
        $bakjson = addslashes(json_encode($eventArray));
        $orderPayModel->orderPaylog($orderPay['pay_pid'], $eventArray['thirdOrderId'], 0, '', $paytype_code, $createtime, '0', $bakjson, '', 'zhcmb');
        
        $this->error = 0;
        return true;
    }

    /**
     * 处理已存在的支付宝订单 - 统一处理普通订单和合并订单
     * @param string $pay_pid 支付订单ID
     * @param array $orderData 订单数据
     * @param bool $isOrdinary 是否为普通订单
     * @return mixed
     */
    private function handleAliExistingOrder($pay_pid, $orderData, $isOrdinary) {
        if ($isOrdinary) {
            // 普通订单处理
            return $this->handleOrdinaryExistingOrder($pay_pid, $orderData);
        } else {
            // 合并订单处理
            return $this->handleMergeExistingOrder($pay_pid, $orderData);
        }
    }

}