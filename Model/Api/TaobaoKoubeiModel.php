<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/12
 * Time: 22:59
 */

namespace Model\Api;


class TaobaoKoubeiModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    public $app_key = '34776802';
    public $app_secret = '3f9424ada90d05992dcc60c120388baa';

    function __construct()
    {
        parent::__construct();
    }

    //获取口碑 sign 签名
    function getSign($paramto){
        //按ASCII顺序排序
        ksort($paramto);
//        print_r($paramto);
        //拼接参数名与参数值 jiami
        $paramstr = '';
        foreach ($paramto as $key=>$paramtoVar){
            $paramstr .= $key.$paramtoVar;
        }
        //把拼装好的字符串采用utf-8编码
        $paramstr8 = utf8_encode($paramstr);
//        echo $paramstr.'----';
        //获取 MD5 方式的签名 (说明：MD5和HMAC_MD5都是128位长度的摘要算法，用16进制表示，一个十六进制的字符能表示4个位，所以签名后的字符串长度固定为32个十六进制字符。)
        $sign = md5($this->app_secret.$paramstr8.$this->app_secret);
        $sign = strtoupper($sign);
        return $sign;
    }
//    //获取加密值 sign 签名
//    function getToken($company_id){
//        $dpingcodeOne = $this->DataControl->getFieldOne("gmc_company_dpingcode","dpingcode_json,dpingcode_createtime,dpingcode_expiresin"
//            ,"company_id = '{$company_id}'","Order BY dpingcode_id DESC limit 0,1");
//        if($dpingcodeOne){
//            if(($dpingcodeOne['dpingcode_createtime']+$dpingcodeOne['dpingcode_expiresin']) < time()+600){
//                return $this->Refreshtoken($company_id);
//            }else{
//                $dpingcodeArray = json_decode($dpingcodeOne['dpingcode_json'], "1");
//                return $dpingcodeArray['access_token'];
//            }
//        }else{
//            return $this->getSign($company_id);
//        }
//
//    }

    //淘宝 饿了么口碑 获取客资信息
    function getTaobaoLeadsApi($request,$page_no=1,$page_size=20,$gmt_start='',$gmt_end=''){

        $somepar = array();
        $somepar['gmt_start'] = $gmt_start?$gmt_start:date("Y-m-d H:i:s",time()-602);
        $somepar['gmt_end'] = $gmt_end?$gmt_end:date("Y-m-d H:i:s",time());
//        $somepar['gmt_start'] = $gmt_start?$gmt_start:"2024-08-23 15:58:00";
//        $somepar['gmt_end'] = $gmt_end?$gmt_end:"2024-08-23 15:59:00";
        $somepar['page_no'] = $page_no;
        $somepar['page_size'] = $page_size;
        $someparjson = json_encode($somepar,JSON_UNESCAPED_UNICODE);

//        echo time().'===';
//        echo $somepar['gmt_start'].'===';
//        echo $somepar['gmt_end'];
//        die;

        $paramto = array();
        $paramto['method'] = "alibaba.alsc.kb.leads.record.pagequery";
        $paramto['app_key'] = $this->app_key;
        //$paramto['session'] = ""; //非必填
        $paramto['timestamp'] = date("Y-m-d H:i:s",time());
        $paramto['v'] = '2.0';//API协议版本，可选值：2.0
        $paramto['sign_method'] = 'md5';//签名的摘要算法，可选值为：hmac，md5，hmac-sha256。 --------
//        $paramto['sign'] = '2.0';//API输入参数签名结果，签名算法参照下面的介绍。 --------
        $paramto['format'] = 'json';//返回内容响应格式。不传默认为xml格式，可选值：xml，json。
        //$paramto['simplify'] = 'false';//是否采用精简JSON返回格式，仅当format=json时有效，可选值：false，true，不传为false。

        //客资查询参数
        $paramto['leads_query_request'] = $someparjson;

        //签名
        $paramto['sign'] = $this->getSign($paramto);
        $sendApiSting = request_by_curl("https://eco.taobao.com/router/rest",dataEncode($paramto), "POST", array());

        //https://gw.api.taobao.com/router/rest
//        print_r($sendApiSting);die;

        if($sendApiArray = json_decode($sendApiSting, "1")){

            if($sendApiArray['alibaba_alsc_kb_leads_record_pagequery_response']['is_success'] == '1'){

                $totalCount = $sendApiArray['alibaba_alsc_kb_leads_record_pagequery_response']['data']['total_count'];
                if( $totalCount >= '1'){

                    $detaInfo = $sendApiArray['alibaba_alsc_kb_leads_record_pagequery_response']['data']['leads_order_info_list']['leads_detail_info'];
                    if($detaInfo){
                        foreach ($detaInfo as $detaInfoVar) {

//                            print_r($detaInfoVar);

                            $haveOne = $this->DataControl->selectOne(" select 1 from gmc_company_gdkoubeileads where gdkoubeilead_record_id = '{$detaInfoVar['reservation_record_id']}' limit 0,1 ");
                            if (!$haveOne) {
                                $log = array();
                                $log['company_id'] = '8888';
                                $log['gdkoubeilead_type'] = $detaInfoVar['biz_type'];
                                $log['gdkoubeilead_channel'] = $detaInfoVar['channel'];
                                $log['gdkoubeilead_mobile'] = $detaInfoVar['contact_mobile'];
                                $log['gdkoubeilead_name'] = $detaInfoVar['contact_name'];
                                $log['gdkoubeilead_create_time'] = $detaInfoVar['create_time'];
                                $log['gdkoubeilead_info'] = $detaInfoVar['ext_info'];
                                $log['gdkoubeilead_is_answer'] = $detaInfoVar['is_answer'];
                                $log['gdkoubeilead_is_x_phone'] = $detaInfoVar['is_x_phone'];
                                $log['gdkoubeilead_record_id'] = $detaInfoVar['reservation_record_id'];
                                $log['gdkoubeilead_source'] = $detaInfoVar['scene_source'];
                                $log['gdkoubeilead_shop_city'] = $detaInfoVar['shop_city'];
                                $log['gdkoubeilead_shop_id'] = $detaInfoVar['shop_id'];
                                $log['gdkoubeilead_shop_name'] = $detaInfoVar['shop_name'];
                                $log['gdkoubeilead_status'] = $detaInfoVar['status'];
                                $log['gdkoubeilead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
                                $log['gdkoubeilead_createtime'] = time();
                                if ($leadsId = $this->DataControl->insertData('gmc_company_gdkoubeileads', $log)) {

                                    //判断名单是否是 校务系统
                                    $needle = "成长中心";
                                    $position = strpos($detaInfoVar['shop_name'], $needle);

                                    //名单的主要信息
                                    $params = array();
                                    $params['company_id'] = 8888;
                                    $params['mobile'] = $detaInfoVar['contact_mobile'];
                                    $params['buy_time'] = strtotime($detaInfoVar['create_time']);
                                    $params['remark'] = "阿里口碑店铺：来源{$detaInfoVar['shop_name']}门店";

                                    if ($position !== false) {
                                        //走 校务系统的名单处理
                                        $resultApi = $this->addClient($params);

                                        $logone = array();
                                        $logone['gdkoubeilead_client_status'] = $resultApi['status'];
                                        $logone['gdkoubeilead_errortip'] = $resultApi['errtip'];
                                        $this->DataControl->updateData('gmc_company_gdkoubeileads', "gdkoubeilead_id = '{$leadsId}'", $logone);
                                    } else {
                                        $params['gdkoubeilead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
                                        //走 园务系统的名单处理
                                        request_by_curl("https://kmcapi.kedingdang.com/Api/cmsKoubeiLeads", dataEncode($params), "POST");
//                                        $aa = request_by_curl("http://kmcapi.kidmanageapi102.com/Api/cmsKoubeiLeads", dataEncode($params), "POST");
//                                        print_r($aa);die;
                                        $logone = array();
                                        $logone['gdkoubeilead_client_status'] = '-1';
                                        $logone['gdkoubeilead_errortip'] = '直营园名单异步通知中！';
                                        $this->DataControl->updateData('gmc_company_gdkoubeileads', "gdkoubeilead_id = '{$leadsId}'", $logone);
                                    }
                                }
                            }
                        }
                    }

                    //判断总数是否大于每次能查到的最大数量
                    if($totalCount > $page_no*$page_size){
                        $p = $page_no+1;
                        $num = $page_size;
                        $this->getTaobaoLeadsApi($request,$p,$num,$somepar['gmt_start'],$somepar['gmt_end']);
                    }

                }else{
                    $this->error = 1;
                    $this->errortip = "暂无数据";
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "数据获取失败";
                return false;
            }

//            print_r($sendApiArray);die;

        }else{
            $this->error = 1;
            $this->errortip = "数据获取失败";
            return false;
        }



        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    //添加来源名单
    function addClient($paramArray){
//        company_id
//        mobile
//        school_branch
//        remark
//        buy_time
        $channelOne = $this->DataControl->getFieldOne("crm_code_channel","channel_id,channel_medianame,channel_intention_level","company_id = '{$paramArray['company_id']}' AND channel_name = '口碑店铺'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname","company_id = '{$paramArray['company_id']}' AND account_class = '1'");
        if($channelOne){
            $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            if ($familyOne) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "检测到在校生家长信息,学员姓名：{$familyOne['student_cnname']}，编号：{$familyOne['student_branch']}，无法添加CRM学员信息！";
                return $result;
            }
            if (!checkMobile($paramArray['mobile'])) {
                $result = array();
                $result['status'] = "0";
                $result['errtip'] = "手机号码加密，无法获取准确手机号，暂不处理！";
                return $result;
            }

            if($paramArray['school_branch'] !=''){
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}'");
//                if(!$schoolOne){
//                    $result = array();
//                    $result['status'] = "0";
//                    $result['errtip'] = "校区编号{{$paramArray['school_branch']}}不存在，请检查校区编号！";
//                    return $result;
//                }
                $schoolOne['school_id'] = 0;
            }else{
                $schoolOne['school_id'] = 0;
            }

            $impotParam = array();
            $impotParam['company_id'] = $paramArray['company_id'];
            if(isset($schoolOne['school_id'])){
                $impotParam['school_id'] = $schoolOne['school_id'];
            }

            $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
            $paramOne['company_id'] = $paramArray['company_id'];
            $paramOne['client_cnname'] = '口碑店铺用户';
            $paramOne['channel_id'] = $channelOne['channel_id'];
            $paramOne['client_source'] = $channelOne['channel_medianame'];
            $paramOne['client_remark'] = $paramArray['remark'];
            $paramOne['client_isfromgmc'] = '1';
            $paramOne['client_mobile'] = $paramArray['mobile'];
            $paramOne['client_isnewtip'] = '1';
            $paramOne['client_updatetime'] = $paramArray['buy_time'];
            $paramOne['client_createtime'] = $paramArray['buy_time'];
            if (!$ClientVerify->CrmClientVerify($paramOne)) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = $ClientVerify->errortip;
                return $result;
            }

            //客户不存在
            if(!$this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}'")){
                $data = array();
                $data['client_cnname'] = '口碑店铺用户';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['company_id'] = $paramArray['company_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_remark'] = $paramArray['remark'];
                $data['client_isfromgmc'] = '1';
                $data['client_mobile'] = $paramArray['mobile'];
                if($channelOne['channel_intention_level'] > 0){
                    $data['client_intention_level'] = $channelOne['channel_intention_level'];
                    $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
                }
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $data['client_createtime'] = $paramArray['buy_time'];
                $client_id = $this->DataControl->insertData("crm_client", $data);
                //添加名单状态记录
                $Model = new  \Model\Api\CalloutModel($paramOne);
                $Model->addClientTimerecord($paramArray['company_id'],$schoolOne['school_id'],$client_id,1,-1,"口碑三方对接录入");

                if ($schoolOne['school_id'] > 0 ) {
                    $datas = array();
                    $datas['client_id'] = $client_id;
                    $datas['school_id'] = $schoolOne['school_id'];
                    $datas['company_id'] = $paramArray['company_id'];
                    $datas['schoolenter_createtime'] = time();
                    $datas['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $datas);
                }
                $trackData = array();
                $trackData['client_id'] = $client_id;
                $trackData['marketer_id'] = '0';
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入（口碑店铺用户）,系统新建客户信息;";
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单创建成功！";
                return $result;
            }

            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "名单通过校验，但未正常激活！";
            return $result;
        }else{
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "未检测到阿里口碑店铺渠道无法处理";
            return $result;
        }
    }

}