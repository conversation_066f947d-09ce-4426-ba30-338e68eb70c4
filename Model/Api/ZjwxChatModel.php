<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/3
 * Time: 15:21
 */

namespace Model\Api;

class ZjwxChatModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $appId = 'wx2a66618e4feffded';
    public $appSecret = '30983395129d5ee5ee76d27a79cea406';
    public $stafferOne = array();
    public $data_id = array();
    public $school_id = array();

    function __construct($staffer_id = 0, $school_id = 0, $data_id = 0)
    {
        parent::__construct();
        if ($staffer_id !== '0') {
            $this->verdictstaffer($staffer_id);
        } else {
            $this->error = true;
            $this->errortip = '请传入教师ID';
        }
        $this->data_id = $data_id;
        $this->school_id = $school_id;
    }

    //验证员工账户
    function verdictstaffer($staffer_id)
    {
        $this->stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id,staffer_cnname,staffer_enname,staffer_wxtoken,staffer_lastip,company_id", "staffer_id = '{$staffer_id}'");
        if (!$this->stafferOne) {
            $this->error = true;
            $this->errortip = "教师信息不存在";
        }
    }

    //获取微信token
    function getWeixinToken()
    {
        $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            ,"company_id = '{$this->stafferOne['company_id']}' AND wxchatnumber_class = '0'");
        if(!$wxchatnumberOne){
            $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
                ,"company_id = '0' AND wxchatnumber_class = '0'");
        }

        $tokenOne = $this->DataControl->getFieldOne("eas_weixin_token", "token_failuretime,token_string"
            , "token_type = '1' AND wxchatnumber_id = '{$wxchatnumberOne['wxchatnumber_id']}'", "order by token_failuretime DESC limit 0,1");
        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $wxtoken = array();
            $wxtoken['access_token'] = $tokenOne['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            $paramarray = array(
                'appid' => $wxchatnumberOne['wxchatnumber_appid'],
                'secret' => $wxchatnumberOne['wxchatnumber_appsecret'],
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['wxchatnumber_id'] = $wxchatnumberOne['wxchatnumber_id'];
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'];
            $this->DataControl->insertData("eas_weixin_token", $data);
            return $dataArray;
        }
    }

    /**
     *
     * author: ling
     * 对应接口文档 0001
     * @param $data
     * @param string $log_type
     * @param int $from 0 校务推送 1 crm推送
     * @return bool
     */
    function SendWeixinMis($data, $log_type = '', $from = 0)
    {
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        $json_play = new \Webjson();
        $retueninfo = $json_play->decode($tmpInfo, "1");
        if ($retueninfo['errmsg'] == 'ok') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = $log_type;
            $date['log_status'] = 1;
            $date['log_day'] = date("Y-m-d");
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            if ($from == 0) {
                $this->DataControl->insertData("eas_wxsend_log", $date);
            } else {
                $this->DataControl->insertData("crm_wxsend_log", $date);
            }
            return true;
        } else {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = $log_type;
            $date['log_status'] = 0;
            $date['log_day'] = date("Y-m-d");
            $date['log_errmsg'] = $tmpInfo;
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            if ($from == 0) {
                $this->DataControl->insertData("eas_wxsend_log", $date);
            } else {
                $this->DataControl->insertData("crm_wxsend_log", $date);
            }
            $this->error = true;
            $this->errortip = '微信接口未走通！';
            return false;
        }
    }

    //第一个学员点评通知
    function StuComFirst($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';


        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '点评完成通知' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }


        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'StuComFirst';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("eas_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "StuComFirst");
    }

    //该作业第一个提交通知
    function StuHomFirst($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '作业完成通知' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'StuHomFirst';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("eas_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "StuHomFirst");
    }

    //家校沟通提醒
    function TePaCommu($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '家校沟通提醒' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'TePaCommu';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("eas_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "TePaCommu");
    }

    //待分配名单提醒
    function ToDistribute($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '学员待分配提醒' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'ToDistribute';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("eas_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "ToDistribute", 1);
    }

    //名单已分配提醒
    function Distribute($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '分配提醒' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'Distribute';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("crm_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "Distribute", 1);
    }

    //招生跟踪提醒
    function StuTrack($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '客户跟进提醒' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'StuTrack';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("crm_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "StuTrack", 1);
    }

    //柜询试听接待提醒
    function Reception($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '接待提醒' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'Reception';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("crm_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "Reception", 1);
    }

    //招生日报
    function Daily($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '报告生成通知' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'Daily';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("crm_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "Daily", 1);
    }

    //客户跟进通知
    function NoticeFollow($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber","company_id","company_id = '{$this->stafferOne['company_id']}' and wxchatnumber_class = '0'");
        if($id){
            $cid = $id['company_id'];
        }else{
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate","masterplate_id","company_id = '{$cid}' and masterplate_name = '客户跟进通知' and masterplate_class = '0'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply","company_id","company_id = '{$this->stafferOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if($status){
            return false;
        }

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'NoticeFollow';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("crm_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "NoticeFollow", 1);
    }

    //客户跟进提醒
    function RemindFollow($firstnote, $keyword1, $keyword2, $footernote, $url)
    {
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"NZRarCjbyAfdFfK7ynKAgPq9s9tFbklaAhQ99Jligas",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->stafferOne['company_id'];
            $date['staffer_id'] = $this->stafferOne['staffer_id'];
            $date['log_type'] = 'RemindFollow';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '教师微信Token为空，无法给此教师推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $date['log_data_id'] = $this->data_id;
            $date['school_id'] = $this->school_id;
            $this->DataControl->insertData("crm_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "RemindFollow", 1);
    }

    //账户登录提醒
    function LoginNotice($wxtempId)
    {
        $loginTime = date("Y-m-d H:i:s");
        $loginIp = real_ip();
        if($this->stafferOne['staffer_lastip'] !== real_ip()){
            $loginIpaddress = "与上次不一致";
        }else{
            $loginIpaddress = "与上次一致";
        }
        $data = '{
			 "touser":"' . $this->stafferOne['staffer_wxtoken'] . '",
			 "template_id":"'.$wxtempId.'",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"感谢您登录校务系统账户，请关注账户安全！",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $this->stafferOne['staffer_cnname'] . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $loginTime . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $loginIpaddress . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $loginIp . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"如您的账户有风险登录，请及时去修改密码！",
						 "color":"#173177"
					 }
			 }
		 }';
        if ($this->stafferOne['staffer_wxtoken'] == '') {
            $this->error = true;
            $this->errortip = '教师WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "UserLogin", 0);
    }
}