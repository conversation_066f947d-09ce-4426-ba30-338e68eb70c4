<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/8/13
 * Time: 13:29
 */

namespace Model\Api;


class SmsModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $publicarray = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
            $this->publicarray = $publicarray;
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['company_id'])) {
            $this->companyOne = $this->DataControl->getFieldOne("gmc_company","company_id,company_language,company_misopen,company_missign","company_id = '{$publicarray['company_id']}'");
        } else {
            $this->error = true;
            $this->errortip = "企业ID必须传入";
            return false;
        }
    }

    function gmcMisSend($mobile,$mistxt,$tilte,$sendcode){

        if($this->companyOne['company_misopen'] == '1'){
            if(Sendmis($mobile,$mistxt,$this->companyOne['company_missign'])){
                $date = array();
                $date['company_id'] = $this->companyOne['company_id'];
                $date['mislog_tilte'] = $tilte;
                $date['mislog_mobile'] = $mobile;
                $date['mislog_sendcode'] = $sendcode;
                $date['mislog_mistxt'] = $mistxt;
                $date['mislog_sendinc'] = "1";
                $date['mislog_time'] = time();
                $this->DataControl->insertData('gmc_mislog',$date);
                return true;
            }else{
                $date = array();
                $date['company_id'] = $this->companyOne['company_id'];
                $date['mislog_tilte'] = $tilte;
                $date['mislog_mobile'] = $mobile;
                $date['mislog_sendcode'] = $sendcode;
                $date['mislog_mistxt'] = $mistxt;
                $date['mislog_sendinc'] = "0";
                $date['mislog_time'] = time();
                $this->DataControl->insertData('gmc_mislog',$date);
                return false;
            }
        }else{
            return false;
        }
    }

    function crmMisSend($mobile,$mistxt,$tilte,$sendcode){
        if($this->companyOne['company_misopen'] == '1'){
            if(Sendmis($mobile,$mistxt,$this->companyOne['company_missign'])){
                $date = array();
                $date['company_id'] = $this->companyOne['company_id'];
                $date['mislog_tilte'] = $tilte;
                $date['mislog_mobile'] = $mobile;
                $date['mislog_sendcode'] = $sendcode;
                $date['mislog_mistxt'] = $mistxt;
                $date['mislog_sendinc'] = "1";
                $date['mislog_time'] = time();
                $this->DataControl->insertData('crm_mislog',$date);
                return true;
            }else{
                $date = array();
                $date['company_id'] = $this->companyOne['company_id'];
                $date['mislog_tilte'] = $tilte;
                $date['mislog_mobile'] = $mobile;
                $date['mislog_sendcode'] = $sendcode;
                $date['mislog_mistxt'] = $mistxt;
                $date['mislog_sendinc'] = "0";
                $date['mislog_time'] = time();
                $this->DataControl->insertData('crm_mislog',$date);
                return false;
            }
        }else{
            return false;
        }
    }

    function tklMisSend($mobile,$mistxt,$tilte,$sendcode,$mislog_type,$student_id,$hourrooms_id){
        if($this->companyOne['company_misopen'] == '1'){
            if(Sendmis($mobile,$mistxt,$this->companyOne['company_missign'])){
                $date = array();
//                $date['company_id'] = $this->companyOne['company_id'];
                $date['student_id'] = $student_id;
                $date['hourrooms_id'] = $hourrooms_id;
                $date['mislog_type'] = $mislog_type;
                $date['mislog_tilte'] = $tilte;
                $date['mislog_mobile'] = $mobile;
                $date['mislog_sendcode'] = $sendcode;
                $date['mislog_mistxt'] = $mistxt;
                $date['mislog_sendinc'] = "1";
                $date['mislog_time'] = time();
                $this->DataControl->insertData('tkl_mislog',$date);
                return true;
            }else{
                $date = array();
//                $date['company_id'] = $this->companyOne['company_id'];
                $date['student_id'] = $student_id;
                $date['hourrooms_id'] = $hourrooms_id;
                $date['mislog_type'] = $mislog_type;
                $date['mislog_tilte'] = $tilte;
                $date['mislog_mobile'] = $mobile;
                $date['mislog_sendcode'] = $sendcode;
                $date['mislog_mistxt'] = $mistxt;
                $date['mislog_sendinc'] = "0";
                $date['mislog_time'] = time();
                $this->DataControl->insertData('tkl_mislog',$date);
                return false;
            }
        }else{
            return false;
        }
    }
}