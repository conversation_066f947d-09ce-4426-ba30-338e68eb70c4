<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/12
 * Time: 22:59
 */

namespace Model\Api;


class WeChatStoreModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    public $app_id = 'wx1600bded442ff505';//企微小店 Appid（小店id）
    public $app_secret = '2acd62b276067eb65d6010cd472dee97';//企微小店 AppSecret(小程序密钥)

    function __construct()
    {
        parent::__construct();
    }

    //获取 AccessToken
    function getStoreToken(){

        $this->error = 1;
        $this->errortip = '园务在用这个token了，校务要用请从园务那边获取,数据表也是测试服有，正式没有';
        $this->result = '';
        exit;
        return false;

        $nowtime = time();
        $tokenOne = $this->DataControl->selectOne(" select * from gmc_company_wxstore_token where token_type = 1 and token_failuretime > '{$nowtime}' order by token_failuretime desc ");
        if($tokenOne['token_string']){
            $nowtoken = $tokenOne['token_string'];
        }else {
            $nowtoken = '';
            $param = array(
                'appid' => $this->app_id, //"wx1600bded442ff505"
                'secret' => $this->app_secret, //"2acd62b276067eb65d6010cd472dee97"
                'grant_type' => "client_credential", //
            );

            //POST参数 RAW中JSON方式传值获取结果
            $getBackJson = httpRequest("https://api.weixin.qq.com/cgi-bin/token", dataEncode($param), "GET");
            $bakData = json_decode($getBackJson, true);
            //print_r($bakData);
            //die;
            if($bakData['access_token']){
                //存表
                $record = array();
                $record['token_type'] = 1;
                $record['token_string'] = $bakData['access_token'];
                $record['token_failuretime'] = time() + $bakData['expires_in'] - 2;
                $this->DataControl->insertData("gmc_company_wxstore_token", $record);

                $nowtoken = $bakData['access_token'];
            }
        }

        $this->error = 0;
        $this->errortip = '获取';
        $this->result = $nowtoken;
        return $nowtoken;
    }

    //微信小店获取订单信息
    function getStoreOrderApi($request,$gmt_start='',$gmt_end='',$next_key=''){

//        $gmt_start = 1743955200;
//        $gmt_end = 1744041600;

        //获取微信 token
        $StoreToken = $this->getStoreToken();
//echo $StoreToken;die;
        if($StoreToken){
            //获取订单列表
            $somepar = array();
            $somepar['start_time'] = $gmt_start?$gmt_start:(time()-602);
            $somepar['end_time'] = $gmt_end?$gmt_end:time();

            $paramto = array();
            //$paramto['create_time_range'] = $somepar; //订单创建时间范围
            $paramto['update_time_range'] = $somepar; //订单更新时间范围
            $paramto['page_size'] = 100;
            $paramto['next_key'] = $next_key?$next_key:'';

            $sendApiSting = request_by_curl("https://api.weixin.qq.com/channels/ec/order/list/get?access_token=".$StoreToken,json_encode($paramto), "POST", array());
            $sendApiArray = json_decode($sendApiSting, "1");

            //处理获取到的数据
            if($sendApiArray['order_id_list']){
                foreach ($sendApiArray['order_id_list'] as $listVar){
                    $orderOne = $this->DataControl->selectOne(" select wxstoreorder_id from gmc_company_wxstoreorder where order_id = {$listVar}  ");
                    if(!$orderOne) {
                        $orderdata = array();
                        $orderdata['company_id'] = 8888;
                        $orderdata['order_id'] = $listVar;
                        $orderdata['wxstoreorder_createtime'] = time();
                        $this->DataControl->insertData("gmc_company_wxstoreorder", $orderdata);
                    }else{
                        $orderdata = array();
                        $orderdata['wxstoreorder_updatetime'] = time();
                        $this->DataControl->updateData("gmc_company_wxstoreorder", "wxstoreorder_id = '{$orderOne['wxstoreorder_id']}'",$orderdata);
                    }
                }

                if($sendApiArray['has_more']){
                    $this->getStoreOrderApi($request,$somepar['start_time'],$somepar['end_time'],$sendApiArray['next_key']);
                }
            }
            echo "订单列表获取成功，并以存储";
        }else{
            echo 'token获取失败';
        }

        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    //微信小店获取订单中收货地址中用户手机号
    function getStoreOrderAddressInfoApi(){
        //获取微信 token
        $StoreToken = $this->getStoreToken();
die;
        if($StoreToken){
            $orderList = $this->DataControl->selectClear("select wxstoreorder_id,order_id from gmc_company_wxstoreorder where wxstoreorder_client_status = '0' limit 0,10 ");
            if($orderList){
                foreach ($orderList as $orderVar){

                    //获取订单详情
                    $paramto = array();
                    $paramto['order_id'] = $orderVar['order_id'];
                    $paramto['encode_sensitive_info'] = true;
                    $sendApiSting = request_by_curl("https://api.weixin.qq.com/channels/ec/order/get?access_token=".$StoreToken,json_encode($paramto), "POST", array());
                    $sendApiArray = json_decode($sendApiSting, "1");
//print_r($sendApiArray);die;
                    if($sendApiArray['errcode'] == '0' && $sendApiArray['order']){

                        //更新订单详情信息
                        $oddata = array();
                        $oddata['create_time'] = $sendApiArray['order']['create_time'];
                        $oddata['update_time'] = $sendApiArray['order']['update_time'];
                        $oddata['status'] = $sendApiArray['order']['status'];
                        $oddata['openid'] = $sendApiArray['order']['openid'];
                        $oddata['unionid'] = $sendApiArray['order']['unionid']?$sendApiArray['order']['unionid']:'';

                        $oddata['product_id'] = $sendApiArray['order']['order_detail']['product_infos'][0]['product_id'];
                        $oddata['sku_id'] = $sendApiArray['order']['order_detail']['product_infos'][0]['sku_id'];
                        $oddata['title'] = $sendApiArray['order']['order_detail']['product_infos'][0]['title'];

                        $oddata['pay_time'] = $sendApiArray['order']['order_detail']['pay_info']['prepay_time'];

                        $oddata['user_name'] = $sendApiArray['order']['order_detail']['delivery_info']['address_info']['user_name'];

                        if($sendApiArray['order']['order_detail']['delivery_info']['deliver_method']=='1'){
                            $oddata['tel_number'] = $sendApiArray['order']['order_detail']['delivery_info']['address_info']['virtual_order_tel_number'];
                        }else{
                            $oddata['tel_number'] = $sendApiArray['order']['order_detail']['delivery_info']['address_info']['tel_number'];
                        }
                        $oddata['alljson'] = json_encode($sendApiArray['order'], JSON_UNESCAPED_UNICODE);
                        $oddata['wxstoreorder_createtime'] = time();
                        $this->DataControl->updateData("gmc_company_wxstoreorder", "wxstoreorder_id = '{$orderVar['wxstoreorder_id']}'",$oddata);

                        if($oddata['tel_number']) {
                            //名单的主要信息
                            $params = array();
                            $params['company_id'] = 8888;
                            $params['mobile'] = $oddata['tel_number'];
                            $params['buy_time'] = $oddata['pay_time']>1?$oddata['pay_time']:$oddata['create_time'];
                            $params['remark'] = "微信小店：来源商品订单用户信息";

                            //走 校务系统的名单处理
                            $resultApi = $this->addClient($params);

                            $logone = array();
                            $logone['wxstoreorder_client_status'] = $resultApi['status'];
                            $logone['wxstoreorder_errortip'] = $resultApi['errtip'];
                            $logone['wxstoreorder_client_updatetime'] = time();
                            $this->DataControl->updateData('gmc_company_wxstoreorder', "wxstoreorder_id = '{$orderVar['wxstoreorder_id']}'", $logone);
                        }

                    }else{
                        echo "订单信息不存在";
                    }
                }
            }
            echo "已处理";
        }else{
            echo "token失效";
        }
    }


    //添加来源名单
    function addClient($paramArray){
//        company_id
//        mobile
//        school_branch
//        remark
//        buy_time
        $channelOne = $this->DataControl->getFieldOne("crm_code_channel","channel_id,channel_medianame,channel_intention_level","company_id = '{$paramArray['company_id']}' AND channel_name = '微信小店'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname","company_id = '{$paramArray['company_id']}' AND account_class = '1'");
        if($channelOne){
            $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            if ($familyOne) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "检测到在校生家长信息,学员姓名：{$familyOne['student_cnname']}，编号：{$familyOne['student_branch']}，无法添加CRM学员信息！";
                return $result;
            }
            if (!checkMobile($paramArray['mobile'])) {
                $result = array();
                $result['status'] = "0";
                $result['errtip'] = "手机号码加密，无法获取准确手机号，暂不处理！";
                return $result;
            }

            if($paramArray['school_branch'] !=''){
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}'");
                if(!$schoolOne){
                    $result = array();
                    $result['status'] = "0";
                    $result['errtip'] = "校区编号{{$paramArray['school_branch']}}不存在，请检查校区编号！";
                    return $result;
                }
            }else{
                $schoolOne['school_id'] = 0;
            }

            $impotParam = array();
            $impotParam['company_id'] = $paramArray['company_id'];
            if(isset($schoolOne['school_id'])){
                $impotParam['school_id'] = $schoolOne['school_id'];
            }

            $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
            $paramOne['company_id'] = $paramArray['company_id'];
            $paramOne['client_cnname'] = '企微小店用户';
            $paramOne['channel_id'] = $channelOne['channel_id'];
            $paramOne['client_source'] = $channelOne['channel_medianame'];
            $paramOne['client_remark'] = $paramArray['remark'];
            $paramOne['client_isfromgmc'] = '1';
            $paramOne['client_mobile'] = $paramArray['mobile'];
            $paramOne['client_isnewtip'] = '1';
            $paramOne['client_updatetime'] = $paramArray['buy_time'];
            $paramOne['client_createtime'] = $paramArray['buy_time'];
            if (!$ClientVerify->CrmClientVerify($paramOne)) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = $ClientVerify->errortip;
                return $result;
            }

            //客户不存在
            if(!$this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}'")){
                $data = array();
                $data['client_cnname'] = '口碑店铺用户';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['company_id'] = $paramArray['company_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_remark'] = $paramArray['remark'];
                $data['client_isfromgmc'] = '1';
                $data['client_mobile'] = $paramArray['mobile'];
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $data['client_createtime'] = $paramArray['buy_time'];
                $client_id = $this->DataControl->insertData("crm_client", $data);
                if ($schoolOne['school_id'] > 0 ) {
                    $datas = array();
                    $datas['client_id'] = $client_id;
                    $datas['school_id'] = $schoolOne['school_id'];
                    $datas['company_id'] = $paramArray['company_id'];
                    $datas['schoolenter_createtime'] = time();
                    $datas['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $datas);
                }
                $trackData = array();
                $trackData['client_id'] = $client_id;
                $trackData['marketer_id'] = '0';
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入（微信小店订单用户）,系统新建客户信息;";
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单创建成功！";
                return $result;
            }

            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "名单通过校验，但未正常激活！";
            return $result;
        }else{
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "未检测到--渠道无法处理";
            return $result;
        }
    }

}