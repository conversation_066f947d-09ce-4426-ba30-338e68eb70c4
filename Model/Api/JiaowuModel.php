<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model\Api;

class JiaowuModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $site_url;

    function __construct() {
        parent::__construct();

        if ($_SERVER['SERVER_NAME'] != 'crmapi.kedingdang.com' && $_SERVER['SERVER_NAME'] != 'gmcapi.kedingdang.com') {
            $this->site_url = "http://crmapi.kcclassin.com";
        } else {
            $this->site_url = "https://crmapi.kedingdang.com";
        }
    }


    //获取班外课时列表
    function getOutClassHour($paramArray)
    {
        $datawhere = "h.company_id = '{$paramArray['company_id']}' and h.school_id = '{$paramArray['school_id']}' and h.hour_iscancel = '0'";

        //检核执行状态 0待确认 1已执行 2未执行
        if (isset($paramArray['check_status']) && $paramArray['check_status'] !== '') {
            if ($paramArray['check_status'] == '0') {
                $datawhere .= " and not exists (select hc.hourcheck_id from smc_outclass_hourcheck as hc where hc.hour_id = h.hour_id)";
            } elseif ($paramArray['check_status'] == '2') {
                $datawhere .= " and exists (select hc.hourcheck_id from smc_outclass_hourcheck as hc where hc.hour_id = h.hour_id and hc.hourcheck_isexecute = '0')";
            } else {
                $datawhere .= " and exists (select hc.hourcheck_id from smc_outclass_hourcheck as hc where hc.hour_id = h.hour_id and hc.hourcheck_isexecute = '1')";
            }
        }
        //开始日期
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= "  and h.hour_day >= '{$paramArray['starttime']}'";
        }
        //结束日期
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= "  and h.hour_day <= '{$paramArray['endtime']}'";
        }
        //职务
        if (isset($paramArray['post_id']) && $paramArray['post_id'] !== '') {
            $datawhere .= "  and (SELECT p.post_id FROM gmc_staffer_postbe AS p WHERE p.staffer_id = h.staffer_id AND p.school_id = h.school_id limit 1 ) = '{$paramArray['post_id']}'";
        }
        //在职状态 0在职 1离职
        if (isset($paramArray['staffer_leave']) && $paramArray['staffer_leave'] !== '') {
            $datawhere .= "  and sf.staffer_leave = '{$paramArray['staffer_leave']}'";
        }
        //是否主职 0跨校 1主职
        if (isset($paramArray['postbe_ismianjob']) && $paramArray['postbe_ismianjob'] !== '') {
            $datawhere .= "  and (SELECT p.postbe_ismianjob FROM gmc_staffer_postbe AS p WHERE p.staffer_id = h.staffer_id AND p.school_id = h.school_id limit 1 ) = '{$paramArray['postbe_ismianjob']}'";
        }
        //课时类型
        if (isset($paramArray['outclasstype_id']) && $paramArray['outclasstype_id'] !== '') {
            $datawhere .= "  and t.outclasstype_id = '{$paramArray['outclasstype_id']}'";
        }
        //课时分类
        if (isset($paramArray['outclasstype_code']) && $paramArray['outclasstype_code'] !== '') {
            $datawhere .= "  and t.outclasstype_code = '{$paramArray['outclasstype_code']}'";
        }
        //关键词
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (sf.staffer_cnname like '%{$paramArray['keyword']}%' or sf.staffer_enname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '0';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '0';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT h.hour_id,h.hour_day,CONCAT(h.hour_starttime,'-',h.hour_endtime) as hour_time,h.hour_classtimes,h.hour_scname,h.hour_remk,t.outclasstype_name,
                       (CASE t.outclasstype_code WHEN 0 THEN '教学时数' ELSE '其它时数' END) as outclasstype_code,'4' as lessonplan_type,
                       sf.staffer_cnname,sf.staffer_enname,sf.staffer_branch,sf.staffer_isparttime,sf.staffer_leave,sf.staffer_native,
                       IFNULL((select hc.hourcheck_id from smc_outclass_hourcheck as hc where hc.hour_id = h.hour_id LIMIT 1),'0') as hourcheck_id,
                       (SELECT p.postbe_ismianjob FROM gmc_staffer_postbe AS p WHERE p.staffer_id = h.staffer_id AND p.school_id = h.school_id limit 1 ) as postbe_ismianjob,
                       IFNULL((SELECT g.post_name FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS g ON p.post_id = g.post_id
                            WHERE p.staffer_id = h.staffer_id and g.post_type = 1 and p.school_id = h.school_id ORDER BY p.postbe_createtime DESC LIMIT 0,1 ),'--') as staffer_postbe
                FROM smc_outclass_hour as h
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = h.staffer_id
                LEFT JOIN smc_code_outclasstype as t ON t.outclasstype_id = h.outclasstype_id
                WHERE {$datawhere}
                ORDER BY h.hour_day ASC,h.hour_starttime ASC,h.hour_endtime ASC";

        //统计数量
        $data = array();
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear($sql);
            $allnums = count($all_num);
            if ($all_num) {
                $data['allnum'] = $allnums;
            } else {
                $data['allnum'] = 0;
            }
        }

        if ($page == '0' && $num == '0') {
            $dataList = $this->DataControl->selectClear($sql);
        } else {
            $dataList = $this->DataControl->selectClear($sql." LIMIT {$pagestart},{$num}");
        }
        if ($dataList) {
            $type = $this->LgArraySwitch(array("0" => "跨校", "1" => "主职"));
            $leave = $this->LgArraySwitch(array("0" => "在职", "1" => "离职"));
            $status = $this->LgArraySwitch(array("0" => "全职", "1" => "兼职"));
            $statussss = $this->LgArraySwitch(array("0" => "陆籍", "1" => "外籍", "2" => "港澳籍", "3" => "台籍"));
            foreach ($dataList as &$value) {
                $value['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '/' . $value['staffer_enname'] : $value['staffer_cnname'];
                $value['staffer_leave'] = $leave[$value['staffer_leave']];
                $value['staffer_native'] = $statussss[$value['staffer_native']];
                $value['staffer_isparttime'] = $status[$value['staffer_isparttime']];
                $value['postbe_ismianjob'] = $type[$value['postbe_ismianjob']];
                if (!$value['hour_remk']) {
                    $value['hour_remk'] = '--';
                }
                if (!empty($paramArray['check_status'])) {
                    $hourcheck = $this->DataControl->selectOne("SELECT hc.hourcheck_examtime,hc.hourcheck_examremk,sf.staffer_cnname,sf.staffer_enname FROM smc_outclass_hourcheck as hc,smc_staffer as sf WHERE sf.staffer_id = hc.staffer_id and hc.hour_id = '{$value['hour_id']}'");
                    $value['exam_staffer_cnname'] = $hourcheck['staffer_enname'] ? $hourcheck['staffer_cnname'] . '/' . $hourcheck['staffer_enname'] : $hourcheck['staffer_cnname'];
                    $value['hourcheck_examtime'] = $hourcheck['hourcheck_examtime'];
                    $value['hourcheck_examremk'] = $hourcheck['hourcheck_examremk'] ? $hourcheck['hourcheck_examremk'] : '--';
                }
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //获取教师带班课时信息
    function getStafferClassHour($paramArray)
    {
        $datawhere = "e.school_branch = '{$paramArray['school_branch']}'  and f.staffer_branch = '{$paramArray['staffer_branch']}'";
        //月份
        if (isset($paramArray['month']) && $paramArray['month'] !== '') {
            $days = GetTheMonth($paramArray['month']);
            $datawhere .= " and b.hour_day >= '{$days[0]}' and b.hour_day <= '{$days[1]}'";
        }

        $sql = "SELECT  
                    b.hour_id,
                    b.hour_day,
                    b.hour_starttime,
                    b.hour_endtime,
                    b.hour_classtimes,
                    a.teaching_type,
                    a.teachtype_code,
                    t.teachtype_name,
                    g.coursecat_branch,
                    d.course_branch,
                    d.course_inclasstype,
                    c.class_branch,
                    c.class_cnname,
                    c.class_enname,
                    b.hour_isfree,
                    b.hour_ischecking,
                    b.hour_lessontimes
                FROM
                    smc_class_hour_teaching a
                    LEFT JOIN smc_class_hour b ON b.hour_id = a.hour_id
                    LEFT JOIN smc_class c ON c.class_id = b.class_id
                    LEFT JOIN smc_course d ON d.course_id = c.course_id
                    LEFT JOIN smc_school e ON e.school_id = c.school_id
                    LEFT JOIN smc_staffer f ON f.staffer_id = a.staffer_id
                    LEFT JOIN smc_code_coursecat g ON g.coursecat_id = d.coursecat_id
                    LEFT JOIN smc_code_teachtype t ON t.company_id = c.company_id and t.teachtype_code = a.teachtype_code
                WHERE
                    {$datawhere} and b.hour_ischecking >= 0 and a.teaching_isdel = 0";

        //课时类型
        if (isset($paramArray['type']) && strpos($paramArray['type'], '1') !== false) {
            $HourList = $this->DataControl->selectClear($sql." and d.course_inclasstype IN (0,1,2)");
        }
        if (isset($paramArray['type']) && strpos($paramArray['type'], '2') !== false) {
            $OHList = $this->DataControl->selectClear($sql." and d.course_inclasstype = '3'");
        }
        if (isset($paramArray['type']) && strpos($paramArray['type'], '3') !== false) {
            $sqls = "SELECT
                        b.hour_id,
                        b.hour_day,
                        b.hour_starttime,
                        b.hour_endtime,
                        b.hour_classtimes,
                        b.outclasstype_id,
                        b.hour_scname,
                        b.hour_remk
                    FROM
                        smc_outclass_hour b
                    LEFT JOIN smc_school e ON e.school_id = b.school_id
                    LEFT JOIN smc_staffer f ON f.staffer_id = b.staffer_id
                    WHERE
                        {$datawhere} and b.hour_iscancel = 0";
            $OutList = $this->DataControl->selectClear($sqls);
        }
        if ($HourList) {
            if ($OHList) {
                $data['list'] = array_merge($HourList, $OHList);
            } else {
                $data['list'] = $HourList;
            }
        } elseif ($OHList) {
            $data['list'] = $OHList;
        } else {
            $data['list'] = array();
        }
        if ($OutList) {
            $data['outlist'] = $OutList;
        } else {
            $data['outlist'] = array();
        }

        return $data;

    }


    //执行班外课时
    function executeOutClassHourAction($paramArray)
    {
        $data = array();
        $data['hour_id'] = $paramArray['hour_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['hourcheck_examtime'] = date('Y-m-d H:i');
        $data['hourcheck_examremk'] = $paramArray['hourcheck_examremk'];
        $data['hourcheck_isexecute'] = $paramArray['hourcheck_isexecute'];
        if ($this->DataControl->insertData("smc_outclass_hourcheck", $data)) {
            $this->error = 0;
            $this->errortip = '执行成功';
            return true;
        } else {
            $this->error = 1;
            $this->errortip = '执行失败';
            return false;
        }
    }


    //学员电访记录
    function getStuCatitrack($paramArray)
    {
        $where = "c.company_id = '{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}'";
        $datawhere = "c.company_id = '{$paramArray['company_id']}' and c.school_id = '{$paramArray['school_id']}'";
        if (isset($paramArray['track_from']) && $paramArray['track_from'] !== '') {
            $datawhere .= "  and c.track_from = '{$paramArray['track_from']}'";
        }

        if (isset($paramArray['times_id']) && $paramArray['times_id'] !== '') {
            if ($paramArray['track_list']) {
                $arr = array();
                foreach ($paramArray['track_list'] as $value) {
                    $arr[] = $value['track_id'];
                }
                $trackstring = implode(',', $arr);
                $datawhere .= " and c.track_id in ({$trackstring})";
            } else {
                $datawhere .= " and c.track_id = 0";
            }
        }
        //学员
        if (isset($paramArray['student_id']) && $paramArray['student_id'] !== '') {
            $where .= "  and c.student_id = '{$paramArray['student_id']}'";
            $datawhere .= "  and c.student_id = '{$paramArray['student_id']}'";
        }
        //班组
        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] !== '') {
            $datawhere .= "  and c.coursetype_id = '{$paramArray['coursetype_id']}'";
        }
        //班种
        if (isset($paramArray['coursecat_id']) && $paramArray['coursecat_id'] !== '') {
            $datawhere .= "  and c.coursecat_id = '{$paramArray['coursecat_id']}'";
        }
        //班级
        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== '') {
            $datawhere .= "  and c.class_id = '{$paramArray['class_id']}'";
        }
        //沟通方式
        if (isset($paramArray['commode_id']) && $paramArray['commode_id'] !== '') {
            $datawhere .= "  and c.track_linktype = '{$paramArray['commode_id']}'";
        }
        //沟通结果
        if (isset($paramArray['trackresult_id']) && $paramArray['trackresult_id'] !== '') {
            $datawhere .= "  and c.result_id = '{$paramArray['trackresult_id']}'";
        }
        //电访类型
        if (isset($paramArray['tracktype_id']) && $paramArray['tracktype_id'] !== '') {
            $datawhere .= "  and c.tracktype_id IN ({$paramArray['tracktype_id']})";
        } else {
            $datawhere .= "  and c.track_classname like '%流失电访'";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= "  and c.track_day >= '{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= "  and c.track_day <= '{$paramArray['endtime']}'";
        }

        $sql = "SELECT c.track_id,c.coursetype_id,c.track_day,c.track_note,c.track_callid,c.track_translation,c.track_AIabstract,c.track_picturejson,c.track_createtime,sf.staffer_cnname,sf.staffer_enname,sf.staffer_branch,
                    st.student_id,st.student_cnname,st.student_enname,st.student_branch,p.parenter_cnname,p.parenter_mobile,cl.class_cnname,cl.class_enname,cl.class_branch,
                    CONCAT(sf.staffer_cnname,(CASE sf.staffer_enname WHEN '' THEN '' ELSE CONCAT('/',sf.staffer_enname) END)) AS staffer_name,
                    (SELECT coursetype_cnname from smc_code_coursetype WHERE coursetype_id = c.coursetype_id) as coursetype_cnname,
                    (SELECT coursecat_cnname from smc_code_coursecat WHERE coursecat_id = c.coursecat_id) as coursecat_cnname,
                    ifnull((SELECT ct.tracktype_name FROM smc_code_tracktype as ct WHERE ct.tracktype_id = c.tracktype_id),'--') as tracktype_name,
                    ifnull((SELECT ct.trackresult_name FROM smc_code_trackresult as ct WHERE ct.trackresult_id = c.result_id),'--') as trackresult_name,
                    ifnull((SELECT cc.commode_name FROM crm_code_commode as cc WHERE cc.commode_id = c.track_linktype),'--') as commode_name,
                    ifnull((SELECT co.object_name FROM crm_code_object as co WHERE co.object_code = c.track_code),'--') as object_name,
                    ifnull((SELECT g.post_name FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS g ON p.post_id = g.post_id
                            WHERE p.staffer_id = c.staffer_id and g.post_type = 1 and p.school_id = c.school_id ORDER BY p.postbe_createtime DESC LIMIT 0,1 ),'--') as staffer_postbe
                FROM smc_student_track as c
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = c.staffer_id
                LEFT JOIN smc_class as cl ON cl.class_id = c.class_id
                LEFT JOIN smc_student as st on st.student_id = c.student_id
                LEFT JOIN smc_student_family as f on f.student_id = st.student_id and f.family_isdefault = 1
                LEFT JOIN smc_parenter as p on p.parenter_id = f.parenter_id
                WHERE {$datawhere}
                ORDER BY c.track_createtime DESC";
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_enname'] ? $dateexcelvar['student_cnname'] . '-' . $dateexcelvar['student_enname'] : $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    $datearray['track_day'] = $dateexcelvar['track_day'];
                    if ($dateexcelvar['tracktype_name'] == '流失电访') {
                        $datearray['tracktype_name'] = '班组流失电访';
                    } else {
                        $datearray['tracktype_name'] = $dateexcelvar['tracktype_name'];
                    }
                    $datearray['commode_name'] = $dateexcelvar['commode_name'];
                    $datearray['object_name'] = $dateexcelvar['object_name'];
                    if ($dateexcelvar['track_callid']) {
                        if (WaitingTime($dateexcelvar['track_createtime'], time())['day'] < 365) {
                            $datearray['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$dateexcelvar['track_callid']}";
                        } else {
                            $datearray['track_callid'] = "";
                        }
                    } else {
                        $datearray['track_callid'] = "";
                    }
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['trackresult_name'] = $dateexcelvar['trackresult_name'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_name'];
                    $datearray['staffer_postbe'] = $dateexcelvar['staffer_postbe'];
                    $datearray['track_createtime'] = date('Y-m-d H:i:s', $dateexcelvar['track_createtime']);
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("姓名", "学员编号", "主要联系电话", "电访时间", "电访类型", "沟通方式", "沟通对象", "外呼录音", "电访内容", "跟进班组", "跟进班种", "电访结果", "电访教师", "教师职务", "提交时间"));
            $excelfileds = array('student_cnname', 'student_branch', 'parenter_mobile', 'track_day', 'tracktype_name', 'commode_name', 'object_name', 'track_callid', 'track_note', 'coursetype_cnname', 'coursecat_cnname', 'trackresult_name', 'staffer_name', 'staffer_postbe', 'track_createtime');
            $data['list']['outexceldate'] = $outexceldate;
            $data['list']['excelheader'] = $excelheader;
            $data['list']['excelfileds'] = $excelfileds;
            $data['list']['tem_name'] = $this->LgStringSwitch("流失学员跟进记录报表.xlsx");
            return $data;
        } else {
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as &$value) {
                    $value['track_createtime'] = date("Y-m-d H:i", $value['track_createtime']);
                    $value['staffer_cnname'] = $value['staffer_enname'] ? $value['staffer_cnname'] . '/' . $value['staffer_enname'] : $value['staffer_cnname'];
                    if (!$value['track_picturejson'] || $value['track_picturejson'] == '[]') {
                        $value['track_picturejson'] = '--';
                    }
                    if ($value['tracktype_name'] == '流失电访') {
                        $value['tracktype_name'] = '班组流失电访';
                    }
                    if ($value['track_callid']) {
                        if (WaitingTime($value['track_createtime'], time())['day'] < 365) {
                            $value['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$value['track_callid']}";
                        } else {
                            $value['track_callid'] = "";
                        }
                    }
                }
            } else {
                $dataList = array();
            }
        }

        $sql = "SELECT ct.coursetype_branch,ct.coursetype_cnname
                FROM smc_student_track as c
                LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = c.coursetype_id
                WHERE {$where}
                GROUP BY ct.coursetype_branch";
        $coursetypeList = $this->DataControl->selectClear($sql);

        $data = array();
        $data['list'] = $dataList;
        $data['coursetypelist'] = $coursetypeList;
        return $data;
    }



    //添加学员电访记录
    function addStuCatitrackAction($paramArray)
    {
        $data = array();
        $data['track_from'] = '1';
        $data['company_id'] = $paramArray['company_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['class_id'] = $paramArray['class_id'];
        $data['student_id'] = $paramArray['student_id'];
        $data['coursetype_id'] = $paramArray['coursetype_id'];
        $data['coursecat_id'] = $paramArray['coursecat_id'];
        $data['tracktype_id'] = $paramArray['tracktype_id'];
        $data['staffer_id'] = $paramArray['staffer_id'];
        $data['track_code'] = $paramArray['object_code'];
        $data['commode_id'] = $paramArray['commode_id'];
        $data['track_linktype'] = $paramArray['commode_id'];
        $tracktypeOne = $this->DataControl->getFieldOne("smc_code_tracktype", "tracktype_name", "tracktype_id = '{$paramArray['tracktype_id']}'");
        $data['track_classname'] = $tracktypeOne['tracktype_name'];
        $data['result_id'] = $paramArray['trackresult_id'];
        $data['track_day'] = $paramArray['track_day'];
        $data['track_note'] = $paramArray['track_note'];
        $data['track_score'] = $paramArray['track_score'];
        $data['track_picturejson'] = stripslashes($paramArray['track_picturejson']);
        $data['track_translation'] = $paramArray['track_translation'];
        $data['track_AIabstract'] = $paramArray['track_AIabstract'];
        $data['track_callid'] = $paramArray['track_callid'];
        $data['track_createtime'] = time();
        $dataid = $this->DataControl->insertData("smc_student_track", $data);
        if ($dataid) {
            $this->DataControl->query("update smc_code_tracktype set tracktype_use = tracktype_use + 1 where tracktype_id = '{$paramArray['tracktype_id']}'");
            $this->error = 0;
            $this->errortip = '添加电访成功';
            return $dataid;
        } else {
            $this->error = 1;
            $this->errortip = '添加电访失败';
            return false;
        }
    }




    //获取客户试听列表
    function getClientauditionList($paramArray)
    {
        $datawhere = "ci.school_id = '{$paramArray['school_id']}' and ci.company_id = '{$paramArray['company_id']}' and ci.audition_genre = '1'";
        $stu_datawhere = "ci.school_id = '{$paramArray['school_id']}' and ci.company_id = '{$paramArray['company_id']}' and ci.audition_genre = '1'";
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $paramArray['keyword'] = addslashes($paramArray['keyword']);
            $datawhere .= " and (c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%')";
            $stu_datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== "") {
            $datawhere .= " and ci.audition_visittime >= '{$paramArray['starttime']}'";
            $stu_datawhere .= " and ci.audition_visittime >= '{$paramArray['starttime']}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== "") {
            $datawhere .= " and ci.audition_visittime <= '{$paramArray['endtime']} 23:59:59'";
            $stu_datawhere .= " and ci.audition_visittime <= '{$paramArray['endtime']} 23:59:59'";
        }

        if (isset($paramArray['class_id']) && $paramArray['class_id'] !== "") {
            $datawhere .= " and ci.class_id ='{$paramArray['class_id']}'";
            $stu_datawhere .= " and ci.class_id ='{$paramArray['class_id']}'";
        }
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        //统计数量
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear("
				select ci.audition_id
				from crm_client_audition as ci
				LEFT JOIN crm_client as c ON ci.client_id = c.client_id
				where {$datawhere}
				union 
				select ci.audition_id
				from crm_student_audition as ci
				LEFT JOIN smc_student as s ON ci.student_id = s.student_id
				left join smc_student_family as f ON f.student_id = s.student_id and f.family_isdefault = 1
                left join smc_parenter as pt On pt.parenter_id = f.parenter_id 
				WHERE {$stu_datawhere}
				 ");
            $allnums = count($all_num);
            if ($all_num) {
                $data['allnum'] = $allnums;
            } else {
                $data['allnum'] = 0;
            }
        }
        $sqlfields = "c.client_id,c.client_cnname,c.client_enname,'--' as client_branch,c.client_tracestatus,c.client_sex,'--' as client_birthday,c.client_mobile,ci.audition_visittime,
                      (SELECT CONCAT(rc.pay_price,'-',rc.coursetype_cnname,'-',rc.pay_successtime) FROM smc_student_registerinfo AS rc,smc_student AS st WHERE rc.student_id = st.student_id AND st.from_client_id = c.client_id LIMIT 0,1) as pay_price";

        $stu_sqlfield = "s.student_id as client_id,s.student_cnname as client_cnname,s.student_enname as client_enname,s.student_branch as client_branch,'' as client_tracestatus,
                         s.student_sex as client_sex,s.student_birthday as client_birthday,pt.parenter_mobile as client_mobile,ci.audition_visittime,
                         (SELECT CONCAT(rc.pay_price,'-',rc.coursetype_cnname,'-',rc.pay_successtime) FROM smc_student_registerinfo AS rc WHERE rc.student_id = s.student_id LIMIT 0,1) as pay_price";

        $sql = "select {$sqlfields}
				from crm_client_audition as ci
				LEFT JOIN crm_client as c ON ci.client_id = c.client_id
				WHERE {$datawhere}
				union 
				select {$stu_sqlfield}
				from crm_student_audition as ci
				LEFT JOIN smc_student as s ON ci.student_id = s.student_id
				LEFT JOIN smc_student_family as f ON f.student_id = s.student_id and f.family_isdefault = 1
                LEFT JOIN smc_parenter as pt On pt.parenter_id = f.parenter_id
				WHERE {$stu_datawhere}
				order by audition_visittime DESC
				";

        $auditionList = $this->DataControl->selectClear($sql." LIMIT {$pagestart},{$num}");
        if ($auditionList) {
            $status = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));
            foreach ($auditionList as &$value) {
                $value['client_cnname'] = $value['client_enname'] ? $value['client_cnname'] . '-' . $value['client_enname'] : $value['client_cnname'];
                $value['client_tracestatus'] = $status[$value['client_tracestatus']];
                if ($value['pay_price']) {
                    $priceOne = explode("-", $value['pay_price']);
                    $value['pay_price'] = trim($priceOne['0']);
                    $value['coursetype_cnname'] = trim($priceOne['1']);
                    $value['pay_successtime'] = date("Y-m-d H:i", $priceOne['2']);
                } else {
                    $value['pay_price'] = '--';
                    $value['coursetype_cnname'] = '--';
                    $value['pay_successtime'] = '--';
                }
            }
            $data['list'] = $auditionList;
        } else {
            $data['list'] = array();
        }
        return $data;
    }

    //分校流失学员
    function getLossStudentList($paramArray)
    {
        $datawhere = "st.company_id='{$paramArray['company_id']}' and se.school_id='{$paramArray['school_id']}' and se.enrolled_status='-1'";

        if (isset($paramArray['account_class']) && $paramArray['account_class'] != '1') {
            $datawhere .= " and exists (select ss.study_id from smc_student_study as ss where ss.student_id = se.student_id and ss.study_isreading = '1' and ss.class_id IN (select ct.class_id from smc_class_teach as ct where ct.staffer_id = '{$paramArray['staffer_id']}' and ct.teach_status = '0'))";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_enname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%' or p.parenter_cnname like '%{$paramArray['keyword']}%' or st.student_id in (select fa.student_id from smc_student_family as fa where fa.family_mobile like '%{$paramArray['keyword']}%'))";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(se.enrolled_leavetime, '%Y-%m-%d') >= '{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= " and FROM_UNIXTIME(se.enrolled_leavetime, '%Y-%m-%d') <= '{$paramArray['endtime']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select se.school_id,st.student_id,st.student_cnname,st.student_enname,st.student_branch,st.student_birthday,p.parenter_cnname,p.parenter_mobile
              ,(CASE WHEN IFNULL(TIMESTAMPDIFF(YEAR, st.student_birthday, CURDATE()), 0) < 0 THEN 0 ELSE IFNULL(TIMESTAMPDIFF(YEAR, st.student_birthday, CURDATE()), 0) END) AS student_age,if(st.student_sex='男',1,2) as student_sex
              ,(select t.changelog_day from smc_student_changelog as t where t.school_id=se.school_id and t.student_id=se.student_id order by t.changelog_day desc,t.changelog_id desc limit 0,1) as changelog_day
              ,(select t.changelog_note from smc_student_changelog as t where t.school_id=se.school_id and t.student_id=se.student_id order by t.changelog_day desc,t.changelog_id desc limit 0,1) as changelog_note
              ,(SELECT count(t.track_id) FROM smc_student_track AS t WHERE t.school_id = se.school_id and t.student_id = se.student_id and t.track_classname = '分校流失电访') AS track_num
              ,ifnull((select FROM_UNIXTIME(y.pay_firsttime,'%Y-%m-%d') from smc_student_registerinfo y where y.company_id=st.company_id and y.school_id=se.school_id and y.student_id=se.student_id limit 1), '--') as pay_firsttime
              from smc_student_enrolled as se
              left join smc_student as st on st.student_id=se.student_id
              left join smc_student_family as sf on sf.student_id=se.student_id and sf.family_isdefault=1
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where {$datawhere}
              ";

        $data = array();
        //统计数量
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear($sql);
            $allnums = count($all_num);
            if ($all_num) {
                $data['allnum'] = $allnums;
            } else {
                $data['allnum'] = 0;
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql." order by se.enrolled_leavetime desc");

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_enname'] ? $dateexcelvar['student_cnname'] . '-' . $dateexcelvar['student_enname'] : $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $trackOne = $this->DataControl->selectOne("SELECT t.coursetype_id,t.track_note,t.track_callid,t.track_createtime,ct.coursetype_branch,ct.coursetype_cnname,cc.coursecat_cnname,sf.staffer_cnname,sf.staffer_enname
                                                                    FROM smc_student_track as t 
                                                                    LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = t.coursetype_id
                                                                    LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = t.coursecat_id
                                                                    LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                                    WHERE t.school_id = '{$dateexcelvar['school_id']}' and t.student_id = '{$dateexcelvar['student_id']}' and t.track_classname = '分校流失电访'
                                                                    ORDER BY t.track_id DESC LIMIT 0,1");
                    if ($trackOne) {
                        if ($trackOne['track_callid']) {
                            if (WaitingTime($trackOne['track_createtime'], time())['day'] < 365) {
                                $datearray['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$trackOne['track_callid']}";
                            } else {
                                $datearray['track_callid'] = "";
                            }
                        } else {
                            $datearray['track_callid'] = "";
                        }
                        $datearray['track_note'] = $trackOne['track_note'];
                        $datearray['coursetype_cnname'] = $trackOne['coursetype_cnname'];
                        $datearray['coursecat_cnname'] = $trackOne['coursecat_cnname'];
                        $datearray['staffer_name'] = $trackOne['staffer_enname'] ? $trackOne['staffer_cnname'] . '-' . $trackOne['staffer_enname'] : $trackOne['staffer_cnname'];
                    } else {
                        $datearray['staffer_name'] = $datearray['coursecat_cnname'] = $datearray['coursetype_cnname'] = $datearray['track_note'] = $datearray['track_callid'] = '--';
                    }
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $classOne = $this->DataControl->selectOne("select c.class_id,c.class_cnname,c.class_branch,t.coursecat_branch,y.clockinginlog_day
from smc_student_clockinginlog as y,smc_student_hourstudy as h,smc_class as c,smc_course as co,smc_code_coursecat as t
where y.hourstudy_id = h.hourstudy_id and h.class_id = c.class_id and c.course_id = co.course_id and co.coursecat_id = t.coursecat_id
and y.school_id = '{$dateexcelvar['school_id']}' and y.student_id = '{$dateexcelvar['student_id']}'
order by y.clockinginlog_day desc limit 1");
                    $datearray['last_atte_date'] = $classOne['clockinginlog_day'] ?: '';
                    $datearray['last_atte_class'] = $classOne['class_cnname'] ?: '';
                    $datearray['pay_firsttime'] = $dateexcelvar['pay_firsttime'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("姓名", "学员编号", "主要联系电话", "跟踪次数", "外呼录音", "最后流失电访内容", "跟进班组", "跟进班种", "最后跟进人", "流失日期", "最后考勤日期", "最后考勤班级", "首次缴费日期", "流失原因"));
            $excelfileds = array('student_cnname', 'student_branch', 'parenter_mobile', 'track_num', 'track_callid', 'track_note', 'coursetype_cnname', 'coursecat_cnname', 'staffer_name', 'changelog_day', 'last_atte_date', 'last_atte_class', 'pay_firsttime', 'changelog_note');
            $data['list']['outexceldate'] = $outexceldate;
            $data['list']['excelheader'] = $excelheader;
            $data['list']['excelfileds'] = $excelfileds;
            $data['list']['tem_name'] = $this->LgStringSwitch("分校流失学员统计报表.xlsx");
            return $data;
        } elseif (isset($paramArray['is_export']) && $paramArray['is_export'] == 2) {
            $dateexcelarray = $this->DataControl->selectClear("SELECT st.student_id,st.student_cnname,st.student_enname,st.student_branch,p.parenter_cnname,p.parenter_mobile,t.track_day,t.track_note,t.track_callid,t.track_createtime
                                                            ,cy.coursetype_cnname,ct.coursecat_cnname,CONCAT(sf.staffer_cnname,(CASE sf.staffer_enname WHEN '' THEN '' ELSE CONCAT('/',sf.staffer_enname) END)) AS staffer_name
                                                            ,ifnull((SELECT cc.commode_name FROM crm_code_commode as cc WHERE cc.commode_id = t.track_linktype),'--') as commode_name
                                                            ,ifnull((SELECT co.object_name FROM crm_code_object as co WHERE co.object_code = t.track_code),'--') as object_name
                                                            ,ifnull((SELECT ct.tracktype_name FROM smc_code_tracktype as ct WHERE ct.tracktype_id = t.tracktype_id),'--') as tracktype_name
                                                            ,ifnull((SELECT ct.trackresult_name FROM smc_code_trackresult as ct WHERE ct.trackresult_id = t.result_id),'--') as trackresult_name
                                                            ,ifnull((SELECT g.post_name FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS g ON p.post_id = g.post_id
                                                                WHERE p.staffer_id = t.staffer_id and g.post_type = 1 and p.school_id = t.school_id ORDER BY p.postbe_createtime DESC LIMIT 0,1 ),'--') as staffer_postbe
                                                            FROM smc_student_track as t 
                                                            LEFT JOIN smc_code_coursetype as cy ON cy.coursetype_id = t.coursetype_id
                                                            LEFT JOIN smc_code_coursecat as ct ON ct.coursecat_id = t.coursecat_id
                                                            LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                            LEFT JOIN smc_student as st on st.student_id = t.student_id
                                                            LEFT JOIN smc_student_family as f on f.student_id = st.student_id and f.family_isdefault = 1
                                                            LEFT JOIN smc_parenter as p on p.parenter_id = f.parenter_id
                                                            WHERE t.track_classname = '分校流失电访' and exists ({$sql} and se.school_id = t.school_id and se.student_id = t.student_id)
                                                            ORDER BY t.student_id DESC,t.track_id DESC");
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_enname'] ? $dateexcelvar['student_cnname'] . '-' . $dateexcelvar['student_enname'] : $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    $datearray['track_day'] = $dateexcelvar['track_day'];
                    $datearray['commode_name'] = $dateexcelvar['commode_name'];
                    $datearray['object_name'] = $dateexcelvar['object_name'];
                    if ($dateexcelvar['track_callid']) {
                        if (WaitingTime($dateexcelvar['track_createtime'], time())['day'] < 365) {
                            $datearray['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$dateexcelvar['track_callid']}";
                        } else {
                            $datearray['track_callid'] = "";
                        }
                    } else {
                        $datearray['track_callid'] = "";
                    }
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['trackresult_name'] = $dateexcelvar['trackresult_name'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_name'];
                    $datearray['staffer_postbe'] = $dateexcelvar['staffer_postbe'];
                    $datearray['track_createtime'] = date('Y-m-d H:i:s', $dateexcelvar['track_createtime']);
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("姓名", "学员编号", "主要联系电话", "电访时间", "沟通方式", "沟通对象", "外呼录音", "电访内容", "跟进班组", "跟进班种", "电访结果", "电访教师", "教师职务", "提交时间"));
            $excelfileds = array('student_cnname', 'student_branch', 'parenter_mobile', 'track_day', 'commode_name', 'object_name', 'track_callid', 'track_note', 'coursetype_cnname', 'coursecat_cnname', 'trackresult_name', 'staffer_name', 'staffer_postbe', 'track_createtime');
            $data['list']['outexceldate'] = $outexceldate;
            $data['list']['excelheader'] = $excelheader;
            $data['list']['excelfileds'] = $excelfileds;
            $data['list']['tem_name'] = $this->LgStringSwitch("分校流失学员明细报表.xlsx");
            return $data;
        } else {
            $stuList = $this->DataControl->selectClear($sql." order by se.enrolled_leavetime desc LIMIT {$pagestart},{$num}");
            if ($stuList) {
                foreach ($stuList as &$value) {
                    $value['student_cnname'] = $value['student_enname'] ? $value['student_cnname'] . '-' . $value['student_enname'] : $value['student_cnname'];
                    $trackOne = $this->DataControl->selectOne("SELECT t.coursetype_id,t.track_note,t.track_callid,t.track_createtime,ct.coursetype_branch,ct.coursetype_cnname,t.track_picturejson,cc.coursecat_branch,cc.coursecat_cnname,sf.staffer_cnname,sf.staffer_enname
                                                        FROM smc_student_track as t 
                                                        LEFT JOIN smc_code_coursetype as ct ON ct.coursetype_id = t.coursetype_id
                                                        LEFT JOIN smc_code_coursecat as cc ON cc.coursecat_id = t.coursecat_id
                                                        LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                        WHERE t.school_id = '{$value['school_id']}' and t.student_id = '{$value['student_id']}' and t.track_classname = '分校流失电访'
                                                        ORDER BY t.track_id DESC LIMIT 0,1");
                    if ($trackOne) {
                        $value['track_note'] = $trackOne['track_note'];
                        if ($trackOne['track_callid']) {
                            if (WaitingTime($trackOne['track_createtime'], time())['day'] < 365) {
                                $value['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$trackOne['track_callid']}";
                            } else {
                                $value['track_callid'] = "";
                            }
                        }
                        $value['track_picturejson'] = $trackOne['track_picturejson'];
                        $value['coursetype_branch'] = $trackOne['coursetype_branch'];
                        $value['coursetype_cnname'] = $trackOne['coursetype_cnname'];
                        $value['coursecat_branch'] = $trackOne['coursecat_branch'];
                        $value['coursecat_cnname'] = $trackOne['coursecat_cnname'];
                        $value['staffer_name'] = $trackOne['staffer_enname'] ? $trackOne['staffer_cnname'] . '-' . $trackOne['staffer_enname'] : $trackOne['staffer_cnname'];
                    } else {
                        $value['track_callid'] = $value['coursecat_branch'] = $value['coursetype_branch'] = '';
                        $value['track_note'] = $value['track_picturejson'] = $value['coursetype_cnname'] = $value['coursecat_cnname'] = $value['staffer_name'] = '--';
                    }
                    $classOne = $this->DataControl->selectOne("select c.class_id,c.class_cnname,c.class_branch,ct.coursetype_branch,cc.coursecat_branch,y.clockinginlog_day
from smc_student_clockinginlog y,smc_student_hourstudy as h,smc_class as c,smc_course as co,smc_code_coursetype as ct,smc_code_coursecat as cc
where y.hourstudy_id = h.hourstudy_id and h.class_id = c.class_id and y.school_id = '{$value['school_id']}' and co.course_id = c.course_id
and ct.coursetype_id = co.coursetype_id and cc.coursecat_id = co.coursecat_id and y.student_id = '{$value['student_id']}'
order by y.clockinginlog_day desc limit 1");
                    if ($classOne) {
                        $value['class_id'] = $classOne['class_id'];
                        $value['class_cnname'] = $classOne['class_cnname'];
                        $value['class_branch'] = $classOne['class_branch'];
                        if (!$value['coursetype_branch']) {
                            $value['coursetype_branch'] = $classOne['coursetype_branch'];
                        }
                        if (!$value['coursecat_branch']) {
                            $value['coursecat_branch'] = $classOne['coursecat_branch'];
                        }
                        $value['last_atte_date'] = $classOne['clockinginlog_day'];
                    } else {
                        $value['class_id'] = 0;
                        $value['last_atte_date'] = $value['class_branch'] = $value['class_cnname'] = '--';
                        $value['coursecat_branch'] = '';
                    }
                    $value['create_time'] = '--';
                    $sql = "select f.family_mobile as member_mobile,f.family_isdefault as isdefault,if(f.family_relation<>'',f.family_relation,'--') as relation_sex
                            from smc_student_family as f
                            where f.student_id = '{$value['student_id']}' and (f.family_mobile<>'' or f.family_cnname<>'')
                            ORDER BY f.family_relation DESC";
                    $family_list = $this->DataControl->selectClear($sql);
                    if ($family_list) {
                        $value['memberList'] = $family_list;
                    } else {
                        $value['memberList'] = array();
                    }
                }
            } else {
                $stuList = array();
            }

            $data['list'] = $stuList;
            return $data;
        }
    }

    //班组流失学员
    function getCourseTypeLossStudentList($paramArray)
    {
        $datawhere = "sc.company_id='{$paramArray['company_id']}' and sc.school_id='{$paramArray['school_id']}' and sc.coursetype_id > 0 and sc.stuchange_code in ('C02', 'C04')";
        // $datawhere .= " and exists (select 1 from smc_student_enrolled as e where e.school_id=sc.school_id and e.student_id=sc.student_id and e.enrolled_status <> '-1')";
        $datawhere .= " and not exists (select 1 from smc_student_changelog as cl,smc_student_study as ss,smc_class as c,smc_course as co where cl.school_id=sc.school_id and cl.student_id=sc.student_id and cl.school_id=ss.school_id and cl.student_id=ss.student_id 
        and ss.class_id=c.class_id and c.course_id=co.course_id and ss.study_isreading='1' and ss.study_endday>=cl.changelog_day and co.coursetype_id=sc.coursetype_id and cl.changelog_id>sc.changelog_id)";// and cl.stuchange_code in('D02', 'D04')

        if (isset($paramArray['coursetype_id']) && $paramArray['coursetype_id'] > 0) {
            $datawhere .= " and sc.coursetype_id='{$paramArray['coursetype_id']}'";
        }

        if (isset($paramArray['account_class']) && $paramArray['account_class'] != '1') {
            $datawhere .= " and exists (select ss.study_id from smc_student_study as ss where ss.student_id = sc.student_id and ss.study_isreading = '1' and ss.class_id IN (select ct.class_id from smc_class_teach as ct where ct.staffer_id = '{$paramArray['staffer_id']}' and ct.teach_status = '0'))";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$paramArray['keyword']}%' or st.student_enname like '%{$paramArray['keyword']}%' or st.student_branch like '%{$paramArray['keyword']}%' or p.parenter_mobile like '%{$paramArray['keyword']}%' or p.parenter_cnname like '%{$paramArray['keyword']}%' or st.student_id in (select fa.student_id from smc_student_family as fa where fa.family_mobile like '%{$paramArray['keyword']}%'))";
        }

        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $datawhere .= " and sc.changelog_day >= '{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $datawhere .= " and sc.changelog_day <= '{$paramArray['endtime']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sc.changelog_id,sc.school_id,sc.coursetype_id,st.student_id,st.student_cnname,st.student_enname,st.student_branch,st.student_birthday,p.parenter_cnname,p.parenter_mobile,sc.changelog_type,sc.stuchange_code,sc.changelog_day,sc.changelog_category,sc.changelog_note,ct.coursetype_branch,ct.coursetype_cnname
              ,(CASE WHEN IFNULL(TIMESTAMPDIFF(YEAR, st.student_birthday, CURDATE()), 0) < 0 THEN 0 ELSE IFNULL(TIMESTAMPDIFF(YEAR, st.student_birthday, CURDATE()), 0) END) AS student_age,if(st.student_sex='男',1,2) as student_sex
              ,(SELECT count(t.track_id) FROM smc_student_track AS t WHERE t.school_id = sc.school_id and t.student_id = sc.student_id and t.track_classname = '流失电访' and t.coursetype_id = sc.coursetype_id) AS track_num
              from smc_student_changelog as sc
              left join smc_code_coursetype as ct on ct.coursetype_id=sc.coursetype_id
              left join smc_student as st on st.student_id=sc.student_id
              left join smc_student_family as sf on sf.student_id=st.student_id and sf.family_isdefault=1
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where {$datawhere}";
        $by = " group by sc.coursetype_id,sc.student_id order by sc.changelog_day desc,sc.changelog_id desc";

        $data = array();
        //统计数量
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear($sql.$by);
            $allnums = count($all_num);
            if ($all_num) {
                $data['allnum'] = $allnums;
            } else {
                $data['allnum'] = 0;
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql.$by);

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_enname'] ? $dateexcelvar['student_cnname'] . '-' . $dateexcelvar['student_enname'] : $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    $datearray['track_num'] = $dateexcelvar['track_num'];
                    $trackOne = $this->DataControl->selectOne("SELECT t.track_note,t.track_callid,t.track_createtime,sf.staffer_cnname,sf.staffer_enname
                                                                    FROM smc_student_track as t 
                                                                    LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                                    WHERE t.school_id = '{$dateexcelvar['school_id']}' and t.student_id = '{$dateexcelvar['student_id']}'
                                                                    and t.track_classname = '流失电访' and t.coursetype_id = '{$dateexcelvar['coursetype_id']}' ORDER BY t.track_id DESC LIMIT 0,1");
                    if ($trackOne['track_callid']) {
                        if (WaitingTime($trackOne['track_createtime'], time())['day'] < 365) {
                            $datearray['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$trackOne['track_callid']}";
                        } else {
                            $datearray['track_callid'] = "";
                        }
                    } else {
                        $datearray['track_callid'] = "";
                    }
                    $datearray['track_note'] = $trackOne['track_note'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['changelog_category'] = $dateexcelvar['changelog_category'];
                    $datearray['staffer_name'] = $trackOne['staffer_enname'] ? $trackOne['staffer_cnname'] . '-' . $trackOne['staffer_enname'] : $trackOne['staffer_cnname'];
                    $datearray['changelog_day'] = $dateexcelvar['changelog_day'];
                    $classOne = $this->DataControl->selectOne("select c.class_cnname,y.clockinginlog_day
from smc_student_clockinginlog y,smc_student_hourstudy as h,smc_class as c,smc_course as co,smc_code_coursecat as t
where y.hourstudy_id = h.hourstudy_id and h.class_id = c.class_id and c.course_id = co.course_id and co.coursecat_id = t.coursecat_id
and y.school_id = '{$dateexcelvar['school_id']}' and y.student_id = '{$dateexcelvar['student_id']}' and t.coursetype_id = '{$dateexcelvar['coursetype_id']}'
order by y.clockinginlog_day desc limit 1");
                    $datearray['last_atte_date'] = $classOne['clockinginlog_day'];
                    $datearray['last_atte_class'] = $classOne['class_cnname'];
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("姓名", "学员编号", "主要联系电话", "跟踪次数", "外呼录音", "最后流失电访内容", "流失班组", "流失类型", "最后跟进人", "流失日期", "最后考勤日期", "最后考勤班级", "流失原因"));
            $excelfileds = array('student_cnname', 'student_branch', 'parenter_mobile', 'track_num', 'track_callid', 'track_note', 'coursetype_cnname', 'changelog_category', 'staffer_name', 'changelog_day', 'last_atte_date', 'last_atte_class', 'changelog_note');
            $data['list']['outexceldate'] = $outexceldate;
            $data['list']['excelheader'] = $excelheader;
            $data['list']['excelfileds'] = $excelfileds;
            $data['list']['tem_name'] = $this->LgStringSwitch("班组流失学员统计报表.xlsx");
            return $data;
        } elseif (isset($paramArray['is_export']) && $paramArray['is_export'] == 2) {
            $dateexcelarray = $this->DataControl->selectClear("SELECT st.student_id,st.student_cnname,st.student_enname,st.student_branch,p.parenter_cnname,p.parenter_mobile,t.track_day,t.track_note,t.track_callid,t.track_createtime
                                                            ,cy.coursetype_cnname,ct.coursecat_cnname,CONCAT(sf.staffer_cnname,(CASE sf.staffer_enname WHEN '' THEN '' ELSE CONCAT('/',sf.staffer_enname) END)) AS staffer_name
                                                            ,ifnull((SELECT cc.commode_name FROM crm_code_commode as cc WHERE cc.commode_id = t.track_linktype),'--') as commode_name
                                                            ,ifnull((SELECT co.object_name FROM crm_code_object as co WHERE co.object_code = t.track_code),'--') as object_name
                                                            ,ifnull((SELECT ct.tracktype_name FROM smc_code_tracktype as ct WHERE ct.tracktype_id = t.tracktype_id),'--') as tracktype_name
                                                            ,ifnull((SELECT ct.trackresult_name FROM smc_code_trackresult as ct WHERE ct.trackresult_id = t.result_id),'--') as trackresult_name
                                                            ,ifnull((SELECT g.post_name FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS g ON p.post_id = g.post_id
                                                                WHERE p.staffer_id = t.staffer_id and g.post_type = 1 and p.school_id = t.school_id ORDER BY p.postbe_createtime DESC LIMIT 0,1 ),'--') as staffer_postbe
                                                            FROM smc_student_track as t 
                                                            LEFT JOIN smc_code_coursetype as cy ON cy.coursetype_id = t.coursetype_id
                                                            LEFT JOIN smc_code_coursecat as ct ON ct.coursecat_id = t.coursecat_id
                                                            LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                            LEFT JOIN smc_student as st on st.student_id = t.student_id
                                                            LEFT JOIN smc_student_family as f on f.student_id = st.student_id and f.family_isdefault = 1
                                                            LEFT JOIN smc_parenter as p on p.parenter_id = f.parenter_id
                                                            WHERE t.track_classname = '流失电访' and exists ({$sql} and sc.school_id = t.school_id and sc.student_id = t.student_id)
                                                            ORDER BY t.student_id DESC,t.track_id DESC");
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_enname'] ? $dateexcelvar['student_cnname'] . '-' . $dateexcelvar['student_enname'] : $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $datearray['parenter_mobile'] = $dateexcelvar['parenter_mobile'];
                    $datearray['track_day'] = $dateexcelvar['track_day'];
                    $datearray['commode_name'] = $dateexcelvar['commode_name'];
                    $datearray['object_name'] = $dateexcelvar['object_name'];
                    if ($dateexcelvar['track_callid']) {
                        if (WaitingTime($dateexcelvar['track_createtime'], time())['day'] < 365) {
                            $datearray['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$dateexcelvar['track_callid']}";
                        } else {
                            $datearray['track_callid'] = "";
                        }
                    } else {
                        $datearray['track_callid'] = "";
                    }
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursecat_cnname'] = $dateexcelvar['coursecat_cnname'];
                    $datearray['trackresult_name'] = $dateexcelvar['trackresult_name'];
                    $datearray['staffer_name'] = $dateexcelvar['staffer_name'];
                    $datearray['staffer_postbe'] = $dateexcelvar['staffer_postbe'];
                    $datearray['track_createtime'] = date('Y-m-d H:i:s', $dateexcelvar['track_createtime']);
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("姓名", "学员编号", "主要联系电话", "电访时间", "沟通方式", "沟通对象", "外呼录音", "电访内容", "跟进班组", "跟进班种", "电访结果", "电访教师", "教师职务", "提交时间"));
            $excelfileds = array('student_cnname', 'student_branch', 'parenter_mobile', 'track_day', 'commode_name', 'object_name', 'track_callid', 'track_note', 'coursetype_cnname', 'coursecat_cnname', 'trackresult_name', 'staffer_name', 'staffer_postbe', 'track_createtime');
            $data['list']['outexceldate'] = $outexceldate;
            $data['list']['excelheader'] = $excelheader;
            $data['list']['excelfileds'] = $excelfileds;
            $data['list']['tem_name'] = $this->LgStringSwitch("班组流失学员明细报表.xlsx");
            return $data;
        } else {
            $stuList = $this->DataControl->selectClear($sql.$by." LIMIT {$pagestart},{$num}");
            if ($stuList) {
                foreach ($stuList as &$value) {
                    $value['student_cnname'] = $value['student_enname'] ? $value['student_cnname'] . '-' . $value['student_enname'] : $value['student_cnname'];
                    $trackOne = $this->DataControl->selectOne("SELECT t.track_note,t.track_callid,t.track_createtime,sf.staffer_cnname,sf.staffer_enname
                                                        FROM smc_student_track as t 
                                                        LEFT JOIN smc_staffer as sf ON sf.staffer_id = t.staffer_id
                                                        WHERE t.school_id = '{$value['school_id']}' and t.student_id = '{$value['student_id']}'
                                                        and t.track_classname = '流失电访' and t.coursetype_id = '{$value['coursetype_id']}' ORDER BY t.track_id DESC LIMIT 0,1");
                    if ($trackOne) {
                        $value['track_note'] = $trackOne['track_note'];
                        if ($trackOne['track_callid']) {
                            if (WaitingTime($trackOne['track_createtime'], time())['day'] < 365) {
                                $value['track_callid'] = $this->site_url."/Intentionclient/getHuijieCallidRecordingFile?callid={$trackOne['track_callid']}";
                            } else {
                                $value['track_callid'] = "";
                            }
                        }
                        $value['staffer_name'] = $trackOne['staffer_enname'] ? $trackOne['staffer_cnname'] . '-' . $trackOne['staffer_enname'] : $trackOne['staffer_cnname'];
                    } else {
                        $value['track_callid'] = '';
                        $value['staffer_name'] = $value['track_note'] = '--';
                    }
                    $classOne = $this->DataControl->selectOne("select c.class_id,c.class_cnname,c.class_branch,t.coursecat_branch,y.clockinginlog_day
from smc_student_clockinginlog y,smc_student_hourstudy as h,smc_class as c,smc_course as co,smc_code_coursecat as t
where y.hourstudy_id = h.hourstudy_id and h.class_id = c.class_id and y.school_id = '{$value['school_id']}' and co.course_id = c.course_id
and t.coursecat_id = co.coursecat_id and y.student_id = '{$value['student_id']}' and t.coursetype_id = '{$value['coursetype_id']}'
order by y.clockinginlog_day desc limit 1");
                    if ($classOne) {
                        $value['class_id'] = $classOne['class_id'];
                        $value['class_cnname'] = $classOne['class_cnname'];
                        $value['class_branch'] = $classOne['class_branch'];
                        $value['coursecat_branch'] = $classOne['coursecat_branch'];
                        $value['last_atte_date'] = $classOne['clockinginlog_day'];
                    } else {
                        $value['class_id'] = 0;
                        $value['last_atte_date'] = $value['class_branch'] = $value['class_cnname'] = '--';
                        $value['coursecat_branch'] = '';
                    }

                    $value['create_time'] = '--';
                    $sql = "select f.family_mobile as member_mobile,f.family_isdefault as isdefault,if(f.family_relation<>'',f.family_relation,'--') as relation_sex
                            from smc_student_family as f
                            where f.student_id = '{$value['student_id']}' and (f.family_mobile<>'' or f.family_cnname<>'')
                            ORDER BY f.family_relation DESC";
                    $family_list = $this->DataControl->selectClear($sql);
                    if ($family_list) {
                        $value['memberList'] = $family_list;
                    } else {
                        $value['memberList'] = array();
                    }
                }
            } else {
                $stuList = array();
            }

            $data['list'] = $stuList;
            return $data;
        }
    }

    //流失回读学员
    function getLossReadBackStuApi($paramArray)
    {
        $datawhere = "a.company_id='{$paramArray['company_id']}' and a.school_id='{$paramArray['school_id']}' and (a.stuchange_code ='D02' or (a.stuchange_code='D04' and a.coursetype_id=65))";

        if (isset($paramArray['coursecat_branch']) && $paramArray['coursecat_branch'] !== "") {
            $datawhere .= " and (select v.coursecat_branch
from smc_student_hourstudy x,smc_class_hour y,smc_class z,smc_course w,smc_code_coursecat v
where x.hour_id=y.hour_id and x.student_id=a.student_id and x.class_id=z.class_id and z.school_id=a.school_id 
and z.course_id=w.course_id and w.coursecat_id=v.coursecat_id and y.hour_day<a.changelog_day ORDER BY y.hour_day desc limit 0,1) = '{$paramArray['coursecat_branch']}'";
        }

        $having = "lost_date is not null and loss_data is not null and readback_data is not null";
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== '') {
            $having .= " and study_beginday >= '{$paramArray['starttime']}'";
        }

        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== '') {
            $having .= " and study_beginday <= '{$paramArray['endtime']}'";
        }

        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== '') {
            $datawhere .= " and (c.student_cnname like '%{$paramArray['keyword']}%' or c.student_enname like '%{$paramArray['keyword']}%' or c.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select 
                     a.student_id
                    ,a.company_id
                    ,a.school_id
                    ,b.school_branch
                    ,b.school_cnname
                    ,c.student_branch
                    ,c.student_cnname
                    ,c.student_enname
                    ,a.changelog_day
                    ,(case when ifnull(TIMESTAMPDIFF(year,c.student_birthday, CURDATE()),0) < 0 then 0 else ifnull(TIMESTAMPDIFF(year,c.student_birthday, CURDATE()),0) end) as student_age,if(c.student_sex='男',1,2) as student_sex
                    ,(select if(changelog_note<>'',changelog_note,'--') from smc_student_changelog where student_id=a.student_id and school_id=a.school_id and changelog_day<a.changelog_day and stuchange_code in('c02','c04') ORDER BY changelog_day desc limit 0,1) as changelog_note
                    ,(select changelog_day from smc_student_changelog where student_id=a.student_id and school_id=a.school_id and changelog_day<a.changelog_day and stuchange_code in('c02','c04') ORDER BY changelog_day desc limit 0,1) as lost_date
                    ,(select concat(v.coursecat_cnname,'|',y.hour_day,'|',z.class_branch,'|',z.class_enddate,'|'
                        ,ifnull((select concat(f.staffer_cnname,(case f.staffer_enname when '' then '' else CONCAT('/',f.staffer_enname) end)) from smc_class_lessonplan h,smc_staffer as f where h.staffer_id = f.staffer_id and h.class_id = x.class_id and h.teachtype_code = '05CM' limit 0,1),'--')) 
                        from smc_student_hourstudy x,smc_class_hour y,smc_class z,smc_course w,smc_code_coursecat v
                        where x.hour_id=y.hour_id and x.student_id=a.student_id and x.class_id=z.class_id and z.school_id=a.school_id 
                        and z.course_id=w.course_id and w.coursecat_id=v.coursecat_id and y.hour_day<a.changelog_day ORDER BY y.hour_day desc limit 0,1) as loss_data
                    ,(select concat(v.coursecat_cnname,'|',z.class_branch,'|',ifnull((select concat(f.staffer_cnname,(case f.staffer_enname when '' then '' else CONCAT('/',f.staffer_enname) end)) from smc_class_lessonplan h,smc_staffer as f where h.staffer_id = f.staffer_id and h.class_id = x.class_id and h.teachtype_code = '05CM' limit 0,1),'--'),'|'
                        ,ifnull((select o.order_paymentprice from smc_payfee_order o where o.company_id = z.company_id and o.school_id = z.school_id and o.coursetype_id = w.coursetype_id and o.coursecat_id = w.coursecat_id and o.student_id = x.student_id and o.order_status = '4' and FROM_UNIXTIME(o.order_createtime,'%Y-%m-%d') >= a.changelog_day order by o.order_createtime desc limit 0,1),'--'))
                        from smc_student_hourstudy x,smc_class_hour y,smc_class z,smc_course w,smc_code_coursecat v
                        where x.hour_id=y.hour_id and x.student_id=a.student_id and x.class_id=z.class_id and z.school_id=a.school_id 
                        and z.course_id=w.course_id and w.coursecat_id=v.coursecat_id and y.hour_day>=a.changelog_day ORDER BY y.hour_day asc limit 0,1) as readback_data
                    ,ifnull((select x.study_beginday
                        from smc_student_study x,smc_class z,smc_course w
                        where x.student_id=a.student_id and x.class_id=z.class_id and z.school_id=a.school_id 
                        and z.course_id=w.course_id and x.study_beginday>=a.changelog_day ORDER BY x.study_beginday asc limit 0,1),'--') as study_beginday
                from smc_student_changelog a
                left join smc_school b on a.school_id=b.school_id
                left join smc_student c ON a.student_id=c.student_id
                where {$datawhere}
                having {$having}
                order by study_beginday desc
              ";

        $data = array();
        //统计数量
        if (isset($paramArray['is_count']) && $paramArray['is_count'] == 1) {
            $all_num = $this->DataControl->selectClear($sql);
            $allnums = count($all_num);
            if ($all_num) {
                $data['allnum'] = $allnums;
            } else {
                $data['allnum'] = 0;
            }
        }

        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['student_cnname'] = $dateexcelvar['student_enname'] ? $dateexcelvar['student_cnname'] . '-' . $dateexcelvar['student_enname'] : $dateexcelvar['student_cnname'];
                    $datearray['student_branch'] = $dateexcelvar['student_branch'];
                    $loss_data = explode('|', $dateexcelvar['loss_data']);
                    if ($loss_data) {
                        $datearray['loss_coursecat_cnname'] = $loss_data[0];
                        $datearray['loss_hour_day'] = $loss_data[1];
                        $datearray['loss_class_branch'] = $loss_data[2];
                        $datearray['loss_class_enddate'] = $loss_data[3];
                        $datearray['loss_staffer_name'] = $loss_data[4];
                        $order = $this->DataControl->selectOne("select count(r.refund_id) as num from smc_refund_order r 
                        where r.company_id = '{$dateexcelvar['company_id']}' and r.school_id = '{$dateexcelvar['school_id']}' and r.student_id = '{$dateexcelvar['student_id']}' and r.refund_status >= 0
                        and FROM_UNIXTIME(r.refund_createtime,'%Y-%m-%d') >= '{$loss_data[1]}' and FROM_UNIXTIME(r.refund_createtime,'%Y-%m-%d') <= '{$dateexcelvar['changelog_day']}'");
                        $datearray['is_refundloss'] = $order['num'] ? '是' : '否';
                    } else {
                        $datearray['is_refundloss'] = $datearray['loss_staffer_name'] = $datearray['loss_class_enddate'] = $datearray['loss_class_branch'] = $datearray['loss_hour_day'] = $datearray['loss_coursecat_cnname'] = '--';
                    }
                    $datearray['changelog_note'] = $dateexcelvar['changelog_note'];
                    $readback_data = explode('|', $dateexcelvar['readback_data']);
                    if ($readback_data) {
                        $datearray['readback_coursecat_cnname'] = $readback_data[0];
                        $datearray['readback_study_beginday'] = $dateexcelvar['study_beginday'];
                        $datearray['readback_class_branch'] = $readback_data[1];
                        $datearray['readback_staffer_name'] = $readback_data[2];
                        $datearray['readback_order_paymentprice'] = $readback_data[3];
                    } else {
                        $datearray['readback_order_paymentprice'] = $datearray['readback_staffer_name'] = $datearray['readback_class_branch'] = $datearray['readback_study_beginday'] = $datearray['readback_coursecat_cnname'] = '--';
                    }
                    $outexceldate[] = $datearray;
                }
            }

            $excelheader = $this->LgArraySwitch(array("姓名", "学员编号", "流失班种", "流失最后耗课日期", "流失班级编号", "流失班级结班日期", "流失班级中籍主教", "是否退费流失", "流失原因", "回读班种", "回读入班日期", "回读班级编号", "回读中籍主教", "回读缴费金额"));
            $excelfileds = array('student_cnname', 'student_branch', 'loss_coursecat_cnname', 'loss_hour_day', 'loss_class_branch', 'loss_class_enddate', 'loss_staffer_name', 'is_refundloss', 'changelog_note', 'readback_coursecat_cnname', 'readback_study_beginday', 'readback_class_branch', 'readback_staffer_name', 'readback_order_paymentprice');
            $data['list']['outexceldate'] = $outexceldate;
            $data['list']['excelheader'] = $excelheader;
            $data['list']['excelfileds'] = $excelfileds;
            $data['list']['tem_name'] = $this->LgStringSwitch("流失回读学员报表.xlsx");
            return $data;
        } else {
            $stuList = $this->DataControl->selectClear($sql." LIMIT {$pagestart},{$num}");
            if ($stuList) {
                foreach ($stuList as &$value) {
                    $value['student_cnname'] = $value['student_enname'] ? $value['student_cnname'] . '-' . $value['student_enname'] : $value['student_cnname'];
                    $loss_data = explode('|', $value['loss_data']);
                    if ($loss_data) {
                        $value['loss_coursecat_cnname'] = $loss_data[0];
                        $value['loss_hour_day'] = $loss_data[1];
                        $value['loss_class_branch'] = $loss_data[2];
                        $value['loss_class_enddate'] = $loss_data[3];
                        $value['loss_staffer_name'] = $loss_data[4];
                        $order = $this->DataControl->selectOne("select count(r.refund_id) as num from smc_refund_order r 
                        where r.company_id = '{$value['company_id']}' and r.school_id = '{$value['school_id']}' and r.student_id = '{$value['student_id']}' and r.refund_status >= 0
                        and FROM_UNIXTIME(r.refund_createtime,'%Y-%m-%d') >= '{$loss_data[1]}' and FROM_UNIXTIME(r.refund_createtime,'%Y-%m-%d') <= '{$value['changelog_day']}'");
                        $value['is_refundloss'] = $order['num'] ? '是' : '否';
                    } else {
                        $value['is_refundloss'] = $value['loss_staffer_name'] = $value['loss_class_enddate'] = $value['loss_class_branch'] = $value['loss_hour_day'] = $value['loss_coursecat_cnname'] = '--';
                    }
                    $readback_data = explode('|', $value['readback_data']);
                    if ($readback_data) {
                        $value['readback_coursecat_cnname'] = $readback_data[0];
                        $value['readback_class_branch'] = $readback_data[1];
                        $value['readback_staffer_name'] = $readback_data[2];
                        $value['readback_order_paymentprice'] = $readback_data[3];
                        $value['readback_study_beginday'] = $value['study_beginday'];
                    } else {
                        $value['readback_order_paymentprice'] = $value['readback_staffer_name'] = $value['readback_class_branch'] = $value['readback_study_beginday'] = $value['readback_coursecat_cnname'] = '--';
                    }
                    $sql = "select f.family_mobile as member_mobile,f.family_isdefault as isdefault,if(f.family_relation<>'',f.family_relation,'--') as relation_sex
                            from smc_student_family as f
                            where f.student_id = '{$value['student_id']}' and (f.family_mobile<>'' or f.family_cnname<>'')
                            ORDER BY f.family_relation DESC";
                    $family_list = $this->DataControl->selectClear($sql);
                    if ($family_list) {
                        $value['memberList'] = $family_list;
                    } else {
                        $value['memberList'] = array();
                    }
                }
            } else {
                $stuList = array();
            }

            $data['list'] = $stuList;
            return $data;
        }
    }


    /**
     * 转介绍招生明细报表
     * author: qizhugong
     * 对应接口文档 0001
     */
    function referralClientlist($request)
    {
        $datawhere = "s.client_id = c.client_id AND c.channel_id = l.channel_id AND l.channel_isreferral = '1' AND h.school_id = s.school_id AND h.school_istest = '0' AND h.school_isclose = '0' AND c.company_id = '{$request['company_id']}'";
        $regwhere = "rc.student_id = st.student_id AND st.from_client_id = c.client_id";
        if (isset($request['starttime']) && $request['starttime']) {
            $stattime = strtotime($request['starttime']);
            $datawhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['endtime']) && $request['endtime']) {
            $endime = date('Y-m-d H:i:s', strtotime($request['endtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_createtime <='{$endime}'";
        }

        if (isset($request['visit_starttime']) && $request['visit_starttime'] && isset($request['visit_endtime']) && $request['visit_endtime']) {
            $stattime = date('Y-m-d H:i:s', strtotime($request['visit_starttime']));
            $endime = date('Y-m-d H:i:s', strtotime($request['visit_endtime']) + 60 * 60 * 24 - 1);
            $datawhere .= " and c.client_id IN (SELECT v.client_id FROM view_crm_visitlist AS v WHERE v.company_id = '{$request['company_id']}' AND v.visittime >='{$stattime}' AND v.visittime <= '{$endime}')";
        }

        if (isset($request['bookstarttime']) && $request['bookstarttime'] && isset($request['bookendtime']) && $request['bookendtime']) {
            $stattime = strtotime($request['bookstarttime']);
            $endime = date('Y-m-d H:i:s', strtotime($request['bookendtime']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endime);
            $datawhere .= " and c.client_id IN (SELECT st.from_client_id FROM smc_student_registerinfo AS r,smc_student AS st
            WHERE r.student_id = st.student_id AND st.company_id = '{$request['company_id']}' AND st.from_client_id <> '0' and r.info_status = '1' and r.info_type = '0'  
            AND r.pay_successtime >='{$stattime}' AND r.pay_successtime <='{$endime}')";

            $regwhere .= " AND rc.pay_successtime >='{$stattime}' AND rc.pay_successtime <='{$endime}'";
        }

        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $datawhere .= " and s.school_id ='{$request['school_id']}'";
        }
        if (isset($request['schoolBranch']) && $request['schoolBranch'] != "") {
            $datawhere .= " and h.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        }

        if (isset($request['class_id']) && $request['class_id'] !== "") {
            $datawhere .= " and c.client_id IN (SELECT st.from_client_id FROM smc_student_study AS ss,smc_student AS st
            WHERE ss.student_id = st.student_id AND st.company_id = '{$request['company_id']}' AND ss.class_id = '{$request['class_id']}' AND ss.study_isreading = '1')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] !== '1') {
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }
        if (isset($request['organize_id']) && $request['organize_id'] !== "") {
            $datawhere .= " and s.school_id in (SELECT o.school_id FROM gmc_company_organizeschool as o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (c.client_cnname  like '%{$request['keyword']}%' or l.channel_name like '%{$request['keyword']}%' or c.client_mobile like '%{$request['keyword']}%'  ) ";
        }

        if (isset($request['client_tracestatus']) && $request['client_tracestatus'] !== "") {
            $datawhere .= " and c.client_tracestatus ='{$request['client_tracestatus']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== "") {
            if (isset($request['coursecat_id']) && $request['coursecat_id'] !== "") {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}' AND cit.coursecat_id = '{$request['coursecat_id']}')";
            } else {
                $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursetype_id = '{$request['coursetype_id']}')";
            }
        } elseif (isset($request['coursecat_id']) && $request['coursecat_id'] != "") {
            $datawhere .= " AND c.client_id IN (SELECT cit.client_id FROM crm_client_intention as cit WHERE cit.coursecat_id = '{$request['coursecat_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $fields = "h.school_cnname,h.school_branch,
        (select r.region_name from smc_code_region as r where r.region_id = h.school_province ) as province_name,
        (SELECT d.district_cnname FROM gmc_company_district AS d WHERE d.district_id = h.district_id) AS district_cnname,
        c.client_cnname,c.client_enname,c.client_mobile,c.client_gmcmarket,c.client_tracestatus,l.channel_name,c.client_stubranch,c.client_createtime,c.client_age,c.client_sex,
        (SELECT concat(k.marketer_name,(CASE WHEN ifnull( sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  )
         FROM crm_client_principal as p,crm_marketer as k ,smc_staffer as sf  WHERE  sf.staffer_id=k.staffer_id and k.marketer_id = p.marketer_id and p.client_id =c.client_id and principal_ismajor = 1 and principal_leave =0 order by p.principal_createtime DESC limit 0,1 ) AS zhu_marketer_name,
		(SELECT r.student_branch FROM smc_student as r WHERE r.company_id = c.company_id AND r.from_client_id = c.client_id limit 0,1) as student_branch,c.client_teachername,
		(SELECT rc.pay_price FROM smc_student_registerinfo AS rc,smc_student AS st WHERE {$regwhere} LIMIT 0,1) as pay_price,
		(SELECT rc.coursetype_cnname FROM smc_student_registerinfo AS rc,smc_student AS st WHERE {$regwhere} LIMIT 0,1) as coursetype_cnname,
		(SELECT rc.pay_successtime FROM smc_student_registerinfo AS rc,smc_student AS st WHERE {$regwhere} LIMIT 0,1) as pay_successtime";
        $sql = "SELECT {$fields} FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h, crm_code_channel AS l
WHERE {$datawhere} and s.is_enterstatus=1 GROUP BY c.client_id order by pay_successtime desc";

        //跟踪状态：0待跟踪1持续跟踪2已柜询3已试听4已转正-1已无意向
        $clientTracestatus = $this->LgArraySwitch(array('0' => '待跟踪', '1' => '持续跟踪', '2' => '已柜询', '3' => '已试听', '4' => '已转正', '-1' => '无意向', '-2' => '无效名单'));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            if (count($dateexcelarray) > 5000) {
                $this->error = true;
                $this->errortip = "导出数据量大于5千条,请筛选条件后进行导出";
                return false;
            }
            foreach ($dateexcelarray as &$val) {
                $val['client_tracestatus'] = $clientTracestatus[$val['client_tracestatus']];
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['client_createtime'] = date("Y-m-d", $dateexcelvar['client_createtime']);
                    $datearray['province_name'] = $dateexcelvar['province_name'];
                    $datearray['school_cnname'] = $dateexcelvar['school_cnname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'] . ($dateexcelvar['client_enname'] != '' ? '-' . $dateexcelvar['client_enname'] : '');
                    $datearray['student_branch'] = trim($dateexcelvar['student_branch']) ? $dateexcelvar['student_branch'] : '--';
//                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['client_tracestatus'] = $dateexcelvar['client_tracestatus'];

                    if ($datearray['student_branch'] !== '--' && $dateexcelvar['pay_price']) {
                        $datearray['pay_successtime'] = date("Y-m-d", $dateexcelvar['pay_successtime']);
                        $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                        $datearray['pay_price'] = $dateexcelvar['pay_price'];
                    } else {
                        $datearray['pay_successtime'] = '--';
                        $datearray['coursetype_cnname'] = '--';
                        $datearray['pay_price'] = '--';
                    }


                    $datearray['zhu_marketer_name'] = $dateexcelvar['zhu_marketer_name'];
                    $datearray['channel_name'] = $dateexcelvar['channel_name'];
                    $datearray['rom_branch'] = '--';
                    $datearray['rom_cnname'] = '--';
                    $datearray['romclass_branch'] = '--';
                    $datearray['romclass_enname'] = '--';
                    $datearray['romstaffer_branch'] = '--';
                    $datearray['romstaffer_cnname'] = '--';

                    if ($datearray['client_stubranch'] !== '') {
                        $romstudentOne = $this->DataControl->selectOne("SELECT CONCAT( t.student_branch, '#',t.student_cnname,'#',t.student_enname, '#', t.class_branch, '#', t.class_enname, '#', t.staffer_branch, '#', t.staffer_cnname,'/',t.staffer_enname) AS stuteach
FROM view_smc_student_teach AS t WHERE t.company_id = '{$request['company_id']}' AND t.student_branch = '{$dateexcelvar['client_stubranch']}'
AND t.study_beginday <= '{$datearray['client_createtime']}' AND t.study_endday >= '{$datearray['client_createtime']}' GROUP BY t.class_branch LIMIT 0, 1");
                        if ($romstudentOne) {
                            $stuteachArray = explode("#", $romstudentOne['stuteach']);
                            $datearray['rom_branch'] = $stuteachArray[0];
                            $datearray['rom_cnname'] = $stuteachArray[1] . ($stuteachArray[2] != '' ? $stuteachArray[2] : '');
                            $datearray['romclass_branch'] = $stuteachArray[3];
                            $datearray['romclass_enname'] = $stuteachArray[4];
                            $datearray['romstaffer_branch'] = $stuteachArray[5];
                            $datearray['romstaffer_cnname'] = $stuteachArray[6];
                        } else {
                            $romstudentOne = $this->DataControl->selectOne("SELECT s.student_branch,s.student_cnname,s.student_enname FROM smc_student AS s
WHERE s.student_branch = '{$dateexcelvar['client_stubranch']}' AND s.company_id = '{$request['company_id']}' LIMIT 0, 1");
                            if ($romstudentOne) {
                                $datearray['rom_branch'] = $romstudentOne['student_branch'];
                                $datearray['rom_cnname'] = $romstudentOne['student_cnname'] . ($romstudentOne['student_enname'] != '' ? '-' . $romstudentOne['student_enname'] : '');
                            }
                        }
                    }

                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('名单创建时间', '省份', '校区名称', '校区编号', '被推荐学员姓名', '被推荐学员学员编号', '性别', '年龄', '联系方式', '客户状态', '报名日期', '报名班组', '首缴金额', '被推荐学员主要负责人', '招生渠道明细', '推荐学员编号', '推荐学员姓名', '推荐班级编号', '推荐班级别名', '推荐班级主教编号', '推荐班级主教姓名'));
            $excelfileds = array('client_createtime', 'province_name', 'school_cnname', 'school_branch', 'client_cnname', 'student_branch', 'client_sex', 'client_age', 'client_mobile', 'client_tracestatus', 'pay_successtime', 'coursetype_cnname', 'pay_price', 'zhu_marketer_name', 'channel_name', 'rom_branch', 'rom_cnname', 'romclass_branch', 'romclass_enname', 'romstaffer_branch', 'client_teachername');
            $fielname = $this->LgStringSwitch("转介绍招生明细表");
            query_to_excel($excelheader, $outexceldate, $excelfileds, "{$fielname}{$request['starttime']}-{$request['endtime']}.xlsx");
            exit;
        } else {

            $sql .= "  limit {$pagestart},{$num}";
            $dataList = $this->DataControl->selectClear($sql);
            if ($dataList) {
                foreach ($dataList as &$dataOne) {
                    $dataOne['client_createtime'] = $dataOne['client_createtime'] == '' ? '--' : date("Y-m-d", $dataOne['client_createtime']);
                    $dataOne['client_cnname'] = $dataOne['client_cnname'] . ($dataOne['client_enname'] != '' ? '-' . $dataOne['client_enname'] : '');
                    $dataOne['student_branch'] = trim($dataOne['student_branch']) ? $dataOne['student_branch'] : '--';
                    if ($dataOne['student_branch'] !== '--' && $dataOne['pay_price']) {
                        $dataOne['pay_successtime'] = date("Y-m-d", $dataOne['pay_successtime']);
                    } else {
                        $dataOne['pay_price'] = '--';
                        $dataOne['coursetype_cnname'] = '--';
                        $dataOne['pay_successtime'] = '--';
                    }
                    $dataOne['client_tracestatus'] = $clientTracestatus[$dataOne['client_tracestatus']];
                    $dataOne['client_mobile'] = hideNumberString($dataOne['client_mobile']);

                    $dataOne['rom_branch'] = '--';
                    $dataOne['rom_cnname'] = '--';
                    $dataOne['romclass_branch'] = '--';
                    $dataOne['romclass_cnname'] = '--';
                    $dataOne['romclass_enname'] = '--';
                    $dataOne['romstaffer_branch'] = '--';
                    $dataOne['romstaffer_cnname'] = '--';
                    if ($dataOne['client_stubranch'] !== '') {//&& !$dataOne['client_teachername']
                        $romstudentOne = $this->DataControl->selectOne("SELECT CONCAT( t.student_branch, '#',t.student_cnname,'#',t.student_enname, '#', t.class_branch, '#', t.class_cnname, '#', t.class_enname, '#', t.staffer_branch, '#', t.staffer_cnname) AS stuteach
FROM view_smc_student_teach AS t WHERE t.company_id = '{$request['company_id']}' AND t.student_branch = '{$dataOne['client_stubranch']}'
AND t.study_beginday <= '{$dataOne['client_createtime']}' AND t.study_endday >= '{$dataOne['client_createtime']}' GROUP BY t.class_branch LIMIT 0, 1");
                        if ($romstudentOne) {
                            $stuteachArray = explode("#", $romstudentOne['stuteach']);
                            $dataOne['rom_branch'] = $stuteachArray[0];
                            $dataOne['rom_cnname'] = $stuteachArray[1] . ($stuteachArray[2] != '' ? $stuteachArray[2] : '');
                            $dataOne['romclass_branch'] = $stuteachArray[3];
                            $dataOne['romclass_cnname'] = $stuteachArray[4];
                            $dataOne['romclass_enname'] = $stuteachArray[5];
                            $dataOne['romstaffer_branch'] = $stuteachArray[6];
                            $dataOne['client_teachername'] = $dataOne['client_teachername'] ? $dataOne['client_teachername'] : $stuteachArray[7];
                        } else {
                            $romstudentOne = $this->DataControl->selectOne("SELECT s.student_branch,s.student_cnname,s.student_enname FROM smc_student AS s
WHERE s.student_branch = '{$dataOne['client_stubranch']}' AND s.company_id = '{$request['company_id']}' LIMIT 0, 1");
                            if ($romstudentOne) {
                                $dataOne['rom_branch'] = $romstudentOne['student_branch'];
                                $dataOne['rom_cnname'] = $romstudentOne['student_cnname'] . ($romstudentOne['student_enname'] != '' ? '-' . $romstudentOne['student_enname'] : '');
                            }
                        }
                    }
                }
            } else {
                $dataList = array();
            }
            $data['list'] = $dataList;

        }
        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $allNum = $this->DataControl->selectOne("SELECT COUNT(DISTINCT c.client_id) AS allnum FROM crm_client AS c, crm_client_schoolenter AS s, smc_school AS h, crm_code_channel AS l
WHERE {$datawhere} and s.is_enterstatus=1  limit 0,1");
            if ($allNum) {
                $data['allnum'] = $allNum['allnum'];
            } else {
                $data['allnum'] = 0;
            }
        }
        return $data;
    }

    //获取班级复习课次
    function classReviewTime($request)
    {
        $datawhere = "c.school_id='{$request['school_id']}' and c.class_id='{$request['class_id']}'and c.company_id='{$request['company_id']}' and h.hour_iswarming='2'";
        if (isset($request['hour_ischecking']) && $request['hour_ischecking'] !== '') {
            $datawhere .= " and h.hour_ischecking  = '{$request['hour_ischecking']}' ";
        } else {
            $datawhere .= "  and h.hour_ischecking <> '-1' ";
        }

        $sql = "SELECT 
                    h.hour_lessontimes,h.hour_ischecking,CONCAT(h.hour_day,' ',h.hour_starttime,'~',h.hour_endtime) as hour_timesection,r.classroom_cnname,
                    ifnull((select count(1) from smc_student_hourstudy as hs where hs.class_id=h.class_id and hs.hour_id = h.hour_id),0) as study_stunum,
                    ifnull((select count(1) from smc_student_hourstudy as hs where hs.class_id=h.class_id and hs.hour_id = h.hour_id and hs.hourstudy_checkin = 1),'--') as attendance_stunum,
                    ifnull((select count(1) from smc_student_hourstudy as hs,smc_student_clockinginlog as sc 
                            where hs.hourstudy_id = sc.hourstudy_id and hs.student_id = sc.student_id and hs.hour_id = h.hour_id and hs.hourstudy_checkin = 0),0) as absence_stunum
                FROM smc_class_hour as h
                LEFT JOIN smc_class as c on c.class_id = h.class_id
                LEFT JOIN smc_classroom as r on r.classroom_id = h.classroom_id
                WHERE {$datawhere}
                ORDER BY h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_lessontimes";
        $hourList = $this->DataControl->selectClear($sql);
        if (!$hourList) {
            $this->error = true;
            $this->errortip = "暂无数据";
            return false;
        }
        $type = $this->LgArraySwitch(array("0" => "否", "1" => "是"));
        foreach ($hourList as &$val) {
            $val['hour_ischecking_name'] = $type[$val['hour_ischecking']];
        }

        $data['list'] = $hourList;
        return $data;
    }


    //获取教师一周的课表
    function getTeacherWeekTimetable($request)
    {

        $startTime = mktime(0, 0, 0, date("m"), date("d") - date("w") + 7 - 6, date("Y"));

        $weekarray = $this->LgArraySwitch(array("日", "一", "二", "三", "四", "五", "六"));

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $enweekarray = array("0", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday");

            $datawhere = "c.school_id='{$request['school_id']}' and t.staffer_id='{$request['staffer_id']}' and ch.hour_ischecking <> '-1' and c.class_status <> '-2'";
            if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
                $startday = $request['hour_startday'];
            } else {
                $startday = date('Y-m-d', $startTime);
            }
            $datawhere .= " and ch.hour_day >='{$startday}'";

            if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
                $endday = $request['hour_endday'];
            } else {
                $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
            }
            $datawhere .= " and ch.hour_day <= '{$endday}'";

            if (isset($request['keyword']) and $request['keyword'] !== "") {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
            }
            if (isset($request['classroom_id']) and $request['classroom_id'] !== "") {
                $datawhere .= " and ch.classroom_id = '{$request['classroom_id']}' ";
            }

            if (isset($request['hour_way']) and $request['hour_way'] !== "") {
                $datawhere .= " and ch.hour_way ='{$request['hour_way']}'";
            }

            if (isset($request['course_id']) and $request['course_id'] !== "") {
                $datawhere .= " and co.course_id ='{$request['course_id']}'";
            }

            if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
                $datawhere .= " and co.coursecat_id ='{$request['coursecat_id']}'";
            }

            if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
                $datawhere .= " and co.coursetype_id ='{$request['coursetype_id']}'";
            }

            $sql = "select s.staffer_id,s.staffer_cnname,s.staffer_enname,c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,
                           co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,t.teaching_type,co.course_openclasstype,
                           ifnull((select po.post_id from gmc_staffer_postbe as po where po.staffer_id=s.staffer_id and po.school_id<>c.school_id limit 0,1),0) as is_cross
                    from smc_class_hour as ch
                    left join smc_class as c on c.class_id=ch.class_id
                    left join smc_course as co on co.course_id=c.course_id
                    left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
                    left join smc_class_hour_teaching as t on t.hour_id=ch.hour_id
                    left join smc_staffer as s on s.staffer_id=t.staffer_id
                    where {$datawhere}
                    order by ch.hour_day ASC ,ch.hour_starttime ASC";

            $hourList = $this->DataControl->selectClear($sql);

            if (!$hourList) {
                $this->error = true;
                $this->errortip = "无考勤列表数据";
                return false;
            }
            $type = $this->LgArraySwitch(array("0" => "主教", "1" => "助教"));
            $way = $this->LgArraySwitch(array("0" => "实体课", "1" => "线上课"));

            foreach ($hourList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];

                if (isset($request['class_pattern']) and $request['class_pattern'] == 1) {
                    $val['class_cnname'] = $val['class_enname'];
                }

                $val['teaching_type_name'] = $type[$val['teaching_type']];
                $val['hour_way_name'] = $way[$val['hour_way']];
            }

            $timeArray = json_decode(stripslashes(stripslashes($request['time_list'])), 1);
            if (!$timeArray) {
                $this->error = true;
                $this->errortip = "时间规划必须传";
                return false;
            }

            foreach ($timeArray as &$one) {
                $one['time'] = str_replace(".", ":", $one['time']) * 100;
            }

            $noon = array_column($timeArray, 'time');
            $noon_name = array_column($timeArray, 'name');
            $tem_noon = $this->customsort(array_flip($noon));
            $data = array();
            if ($hourList) {
                foreach ($hourList as $key => $hourOne) {
                    $week = date('w', strtotime($hourOne['hour_day']));
                    if ($week == 0) {
                        $week = 7;
                    }
                    $hour_starttime = str_replace(".", ":", $hourOne['hour_starttime']) * 100;

                    $num = (int)$this->gettimeKey($tem_noon, $hour_starttime);

                    if ($num < 0) {
                        continue;
                    }

                    $value = $noon[$num];
                    $data[$hourOne['staffer_id']]['name'] = $hourOne['staffer_cnname'];
                    $data[$hourOne['staffer_id']]['en_name'] = $hourOne['staffer_enname'];
                    $data[$hourOne['staffer_id']]['post_name'] = $hourOne['post_name'];
                    $data[$hourOne['staffer_id']]['postbe_ismianjob'] = $hourOne['postbe_ismianjob'];
                    $data[$hourOne['staffer_id']]['is_cross'] = $hourOne['is_cross'];
                    foreach ($timeArray as $timeOne) {
                        foreach ($enweekarray as $daykey => $day) {
                            if ($week == $daykey) {
                                if ($timeOne['time'] == $value) {
                                    if ($daykey == 0) {
                                        $data[$hourOne['staffer_id']]['list'][$num]['jiedian'] = $noon_name[$num];
                                    } else {
                                        $data[$hourOne['staffer_id']]['list'][$num][$day][] = $hourOne;
                                    }
                                }
                            } else {
                                if ($daykey == 0) {
                                    $data[$hourOne['staffer_id']]['list'][$num]['jiedian'] = $noon_name[$num];
                                } else {
                                    $data[$hourOne['staffer_id']]['list'][$num][$day]['-1'] = '';
                                }
                            }
                        }
                    }
                }
            }

            if ($data) {
                foreach ($data as $k => $dataArray) {
                    foreach ($dataArray['list'] as $key => $dataOne) {
                        foreach ($timeArray as $timeOne) {
                            if ($key != $tem_noon[$timeOne['time']]) {
                                foreach ($enweekarray as $daykey => $day) {
                                    if ($daykey == 0) {
                                        $data[$k]['list'][$tem_noon[$timeOne['time']]]['jiedian'] = $timeOne['name'];
                                    } else {
                                        $data[$k]['list'][$tem_noon[$timeOne['time']]][$day]['-1'] = '';
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $data = $this->get_arr($data);
            $data = array_values($data);

            foreach ($data as &$dataOne) {
                ksort($dataOne['list']);
            }

            $time_start = strtotime($startday);
            $time_end = strtotime($endday);
            $date = array();
            while ($time_start <= $time_end) {
                $date[] = date('Y-m-d', $time_start);
                $time_start = strtotime('+1 day', $time_start);
            }

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = 'jiedian';
            $field[$k]["fieldname"] = '日期/节点';
            $field[$k]["custom"] = 1;
            $field[$k]["show"] = 1;

            foreach ($date as $datevalue) {
                $week = date('w', strtotime($datevalue));
                $k++;
                $field[$k]["fieldstring"] = self::$WORK_DAY[$week]['en'];
                $field[$k]["fieldname"] = $datevalue;
                $field[$k]["fieldcnname"] = self::$WORK_DAY[$week]['cn'];
                $field[$k]["custom"] = 1;
                $field[$k]["show"] = 1;
            }

            $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_cnname,school_shortname", "school_id='{$request['school_id']}'");
            $tem_data = array();
            $tem_data['list'] = $data;
            $tem_data['field'] = $field;

            $schoolName = $schoolOne['school_shortname'] ? $schoolOne['school_shortname'] : $schoolOne['school_cnname'];

            $tem_data['name'] = $schoolName . ' ' . $startday . '至' . $endday . ' 教师课程表';

            return $tem_data;

        } else {

            $enweekarray = array("Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday");

            $datawhere = " c.school_id='{$request['school_id']}' and t.staffer_id='{$request['staffer_id']}' and ch.hour_ischecking <> '-1' and c.class_status <> '-2' ";
            if (isset($request['hour_startday']) and $request['hour_startday'] !== "") {
                $startday = $request['hour_startday'];
            } else {
                $startday = date('Y-m-d', $startTime);
            }
            $datawhere .= " and ch.hour_day >='{$startday}'";

            if (isset($request['hour_endday']) and $request['hour_endday'] !== "") {
                $endday = $request['hour_endday'];
            } else {
                $endday = date('Y-m-d', strtotime('+6 day', strtotime($startday)));
            }
            $datawhere .= " and ch.hour_day <= '{$endday}'";

            if (isset($request['keyword']) and $request['keyword'] !== "") {
                $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
            }
            if (isset($request['classroom_id']) and $request['classroom_id'] !== "") {
                $datawhere .= " and ch.classroom_id = '{$request['classroom_id']}' ";
            }
            if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
                $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}' ";
            }

            if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
                $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}' ";
            }

            if (isset($request['course_id']) and $request['course_id'] !== "") {
                $datawhere .= " and co.course_id = '{$request['course_id']}' ";
            }

            $where = "1";
            if (isset($request['hour_way']) and $request['hour_way'] !== "") {
                $where .= " and ch.hour_way = '{$request['hour_way']}'";
            }

            $arr_staffer = $this->DataControl->selectClear("
				  select t.staffer_id,s.staffer_cnname,s.staffer_enname
				  from smc_staffer as s
				  left JOIN smc_class_hour_teaching as t  ON t.staffer_id  = s.staffer_id and t.teaching_type =0
				  left JOIN smc_class_hour as ch  ON ch.class_id = t.class_id and t.hour_id = ch.hour_id
				  left JOIN smc_class as c ON c.class_id  = t.class_id
		          left join smc_course as co on co.course_id=c.course_id
				  left JOIN smc_classroom as cl ON cl.classroom_id  = ch.classroom_id
				  where {$datawhere}
				  group by t.staffer_id ");
            if ($arr_staffer) {
                foreach ($arr_staffer as &$arrOne) {
                    $arrOne['staffer_cnname'] = $arrOne['staffer_enname'] ? $arrOne['staffer_cnname'] . '-' . $arrOne['staffer_enname'] : $arrOne['staffer_cnname'];
                }


                $arr_staffer_id = array_column($arr_staffer, 'staffer_id');
                $arr_staffercnname = array_column($arr_staffer, 'staffer_cnname', 'staffer_id');
                $arr_stafferenname = array_column($arr_staffer, 'staffer_enname', 'staffer_id');

            } else {
                return array();
            }

            $sql = " select s.staffer_id,s.staffer_cnname,s.staffer_enname,c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,
                            co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,t.teaching_type
                     from smc_staffer as s
                     left join smc_class_hour_teaching as t On s.staffer_id = t.staffer_id
                     left join smc_class_hour AS ch ON ch.hour_id = t.hour_id
                     left join smc_class AS c ON c.class_id = ch.class_id
                     left join smc_course as co on co.course_id = c.course_id
                     left join smc_classroom as cl on cl.classroom_id = ch.classroom_id
                     where {$datawhere} and {$where}
                     order by ch.hour_day asc,ch.hour_starttime asc";
            $weekList = $this->DataControl->selectClear($sql);
            if ($weekList) {
                $type = $this->LgArraySwitch(array("0" => "主教", "1" => "助教"));
                $way = $this->LgArraySwitch(array("0" => "实体课", "1" => "线上课"));
                foreach ($weekList as &$val) {
                    $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                    if ($val['hour_way'] == 1) {
                        $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                        $val['classroom_cnname'] = $val['classroom_brnach'] = $val['hour_number'];
                        $val['classroom_iscloud'] = "1";
                    }
                    $val['teaching_type_name'] = $type[$val['teaching_type']];
                    $val['hour_way_name'] = $way[$val['hour_way']];
                    if ($val['hour_ischecking'] == 0) {
                        $val['hour_clocking'] = '0';
                    } else {
                        $val['hour_clocking'] = '1';
                    }
                }
            }
            $time_start = strtotime($startday);
            $time_end = strtotime($endday);
            $date = array();
            while ($time_start <= $time_end) {
                $date[] = date('Y-m-d', $time_start);
                $time_start = strtotime('+1 day', $time_start);
            }

            $data = array();
            if ($weekList) {
                foreach ($weekList as &$val) {
                    foreach ($arr_staffer_id as $k => $v) {
                        $data[$v]['staffer_cnname']['staffer_cnname'] = $arr_staffercnname[$v];
                        $data[$v]['staffer_cnname']['staffer_enname'] = $arr_stafferenname[$v];

                        if ($val['staffer_id'] == $v) {
                            foreach ($date as $datekey => $datevalue) {
                                $week = date('w', strtotime($datevalue));
                                $data['week_date'][$datekey]['weekcnday'] = $this->LgStringSwitch("星期" . $weekarray[$week]);
                                $data['week_date'][$datekey]['weekday'] = $datevalue;
                                $data['week_date'][$datekey]['week_day'] = $enweekarray[$week];
                                if ($val['hour_day'] == $datevalue) {
                                    $data[$v][$enweekarray[$week]][] = $val;
                                } else {
                                    $data[$v][$enweekarray[$week]]['-1'] = '';
                                }
                            }
                        }
                    }
                }
            }
            $data = $this->get_arr($data);
            return $data;
        }
    }

    //获取教师课表 -- 移动端
    function StafferTimetableOne($request)
    {
        $datawhere = " c.school_id='{$request['school_id']}' and t.staffer_id='{$request['staffer_id']}' and ch.hour_ischecking <> '-1' and c.class_status <> '-2' ";
        if (isset($request['hour_day']) and $request['hour_day'] !== "") {
            $startday = $request['hour_day'];
        } else {
            $startday = date('Y-m-d');
        }
        $datawhere .= " and ch.hour_day = '{$startday}'";

        if (isset($request['keyword']) and $request['keyword'] !== "") {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%' or cl.classroom_cnname like '%{$request['keyword']}%' )";
        }
        if (isset($request['classroom_id']) and $request['classroom_id'] !== "") {
            $datawhere .= " and ch.classroom_id = '{$request['classroom_id']}' ";
        }
        if (isset($request['coursetype_id']) and $request['coursetype_id'] !== "") {
            $datawhere .= " and co.coursetype_id = '{$request['coursetype_id']}' ";
        }

        if (isset($request['coursecat_id']) and $request['coursecat_id'] !== "") {
            $datawhere .= " and co.coursecat_id = '{$request['coursecat_id']}' ";
        }

        if (isset($request['course_id']) and $request['course_id'] !== "") {
            $datawhere .= " and co.course_id = '{$request['course_id']}' ";
        }

        if (isset($request['class_id']) and $request['class_id'] !== "") {
            $datawhere .= " and c.class_id = '{$request['class_id']}' ";
        }

        $sql = " select s.staffer_id,s.staffer_cnname,s.staffer_enname,c.class_id,c.class_cnname,c.class_enname,ch.hour_id,ch.hour_name,ch.hour_day,ch.hour_ischecking,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,
                            co.course_cnname,co.course_branch,cl.classroom_id,cl.classroom_cnname,s.staffer_id,co.course_inclasstype,ch.hour_way,ch.hour_number,t.teaching_type
                        ,ifnull((select count(x.hour_id) from smc_class_hour as x where x.class_id=c.class_id and x.hour_ischecking<>-1),0) as hourNum
                     from smc_staffer as s
                     left join smc_class_hour_teaching as t On s.staffer_id = t.staffer_id
                     left join smc_class_hour AS ch ON ch.hour_id = t.hour_id
                     left join smc_class AS c ON c.class_id = ch.class_id
                     left join smc_course as co on co.course_id = c.course_id
                     left join smc_classroom as cl on cl.classroom_id = ch.classroom_id
                     where {$datawhere}
                     order by ch.hour_day asc,ch.hour_starttime asc";
        $weekList = $this->DataControl->selectClear($sql);
        if ($weekList) {
            $type = $this->LgArraySwitch(array("0" => "主教", "1" => "助教"));
            $way = $this->LgArraySwitch(array("0" => "实体课", "1" => "线上课"));
            foreach ($weekList as &$val) {
                $val['staffer_cnname'] = $val['staffer_enname'] ? $val['staffer_cnname'] . '-' . $val['staffer_enname'] : $val['staffer_cnname'];
                if ($val['hour_way'] == 1) {
                    $val['classroom_cnname'] = $this->LgStringSwitch("云教室");
                    $val['classroom_cnname'] = $val['classroom_brnach'] = $val['hour_number'];
                    $val['classroom_iscloud'] = "1";
                }
                $val['teaching_type_name'] = $type[$val['teaching_type']];
                $val['hour_way_name'] = $way[$val['hour_way']];
                if ($val['hour_ischecking'] == 0) {
                    $val['hour_clocking'] = '0';
                } else {
                    $val['hour_clocking'] = '1';
                }

                $val['hourStr']=$val['hour_lessontimes'].'/'.$val['hourNum'];
            }
        }

        return $weekList;
    }

    //获取课程详情
    function getCourseOne($paramArray)
    {
        $course = $this->DataControl->selectOne(
            " select h.hour_id,h.class_id,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_ischecking,h.hour_way,h.hour_number,cs.course_cnname,cs.course_branch,c.class_cnname,c.class_enname,c.class_branch,co.classroom_cnname,s.staffer_id,st.staffer_id as re_staffer_id,
                    concat(s.staffer_cnname,(CASE WHEN ifnull( s.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', s.staffer_enname ) END )) as staffer_cnname,s.staffer_enname,concat(st.staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )) as re_staffer_cnname
  					from smc_class_hour as h
 					left JOIN smc_class_hour_teaching as t ON t.hour_id = h.hour_id and t.teaching_type=0 and t.teaching_isdel=0
 					left JOin smc_staffer as s ON s.staffer_id = t.staffer_id
 					left JOIN smc_class_hour_teaching as rt ON rt.hour_id = h.hour_id and rt.teaching_type=1 and rt.teaching_isdel=0
 					left JOin smc_staffer as st ON st.staffer_id = rt.staffer_id
 					left JOin smc_classroom as co ON h.classroom_id = co.classroom_id
  					left join smc_course as cs ON cs.course_id = h.course_id
  				    left join smc_class as c ON c.class_id = h.class_id
   					where h.hour_id='{$paramArray['hour_id']}' limit 0,1");

        $ischeck = array("0" => "待上课", "1" => "已上课", "-1" => "已取消");

        if ($course) {

            $course['hour_checking_name'] = $this->LgStringSwitch($ischeck[$course['hour_ischecking']]);

            if ($course['hour_way'] == 1) {
                $course['classroom_cnname'] = $this->LgStringSwitch("云教室");
                $course['classroom_cnname'] = $course['classroom_branch'] = $course['hour_number'];
                $course['classroom_iscloud'] = "1";
            }
            if ($course['staffer_id'] == $paramArray['staffer_id']) {
                $course['staffer_cnname'] = $course['staffer_cnname'] . '(我)';
            }
            if ($course['re_staffer_id'] == $paramArray['staffer_id']) {
                $course['re_staffer_cnname'] = $course['re_staffer_cnname'] . '(我)';
            }
            $course['hour_way_name'] = $this->LgStringSwitch($course['hour_way'] == 1 ? '线上课' : '实体课');

            $list = $course;
        } else {
            $list = array();
        }
        return $list;
    }


    function get_arr($arr)
    {
        foreach ($arr as $k => $v) {
            if (is_array($arr[$k])) {
                $arr[$k] = $this->get_arr($arr[$k]);
            } else {
                if ($k == -1) {
                    unset($arr[$k]);
                }
            }
        }
        return $arr;
    }

    function gettimeKey($ranks, $rank_point)
    {
        $value = '';
        $k = 0;
        foreach ($ranks as $key => $val) {
            $k++;
            $value = $val;
            if ($rank_point >= $key) {
                break;
            } else {
                if ($k == count($ranks)) {
                    return -1;
                }
            }
        }
        return $value;
    }

    function customsort($arr, $orderby = 'desc')
    {
        $new_array = array();
        $new_sort = array();
        foreach ($arr as $key => $value) {
            $new_array[] = $value;
        }
        if ($orderby == 'asc') {
            asort($new_array);
        } else {
            arsort($new_array);
        }
        foreach ($new_array as $k => $v) {
            foreach ($arr as $key => $value) {
                if ($v == $value) {
                    $new_sort[$key] = $value;
                    unset($arr[$key]);
                    break;
                }
            }
        }
        return $new_sort;
    }
}