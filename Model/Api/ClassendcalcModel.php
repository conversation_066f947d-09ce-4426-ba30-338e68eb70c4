<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/3/30
 * Time: 22:55
 */

namespace Model\Api;


class ClassendcalcModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct()
    {
        parent::__construct();
    }

    /**生成结班结算清单**/
    function createSettleOrder($paramArray)
    {
        $sql = "SELECT c.company_id,c.class_id,c.school_id,c.from_class_id
                ,c.class_stdate,c.class_enddate
                ,(SELECT h.hour_day FROM smc_class_hour AS h WHERE h.class_id = c.class_id and h.hour_ischecking>=0 and h.hour_iswarming=0 AND h.hour_isfree=0 AND h.hour_lessontimes=1 
                    ORDER BY h.hour_ischecking DESC limit 0,1) AS class_begindate
                ,u.coursetype_id,u.coursecat_id,u.course_id
                ,u.course_isrenew
                ,c.class_status
                ,u.course_isgraduated
                FROM smc_class c
                left join smc_course u on u.course_id=c.course_id
                left join smc_school s on c.school_id=s.school_id
                WHERE c.course_id = u.course_id
                AND c.company_id='{$paramArray['company_id']}' 
                AND c.class_enddate<=curdate()
                AND c.class_enddate>='2025-01-01' 
                AND C.CLASS_ENDDATE<>'' 
                AND c.class_type='0' 
                AND (u.coursetype_id in('65','79660','79661') or u.course_id in (92360,92361,92362,92363,92364,92365,92366,92367,92368,92369,92370,92371,92707,93087,93088,93089,93784,93785,93786,93787,93788))
                and s.school_istest=0
                and s.school_isclose=0
                and not exists(select 1 from smc_class_endcalc where class_id=c.class_id and endcalc_issettle=1)
                ORDER BY c.class_id";
        $itemArray = $this->DataControl->selectClear($sql);

        if (!$itemArray) {
            $this->error = true;
            $this->errortip = "未查询到任何结班信息";
            return false;
        }

        foreach ($itemArray as $itemOne) {
            $old_calc = $this->DataControl->getFieldOne("smc_class_endcalc", "endcalc_id,endcalc_issettle", "class_id = '{$itemOne['class_id']}'");
            if (!$old_calc) {
                if ($itemOne['class_status'] == '-2') {
                    continue;
                }
                $endcalc = array();
                $endcalc['company_id'] = $itemOne['company_id'];
                $endcalc['school_id'] = $itemOne['school_id'];
                $endcalc['coursetype_id'] = $itemOne['coursetype_id'];
                $endcalc['coursecat_id'] = $itemOne['coursecat_id'];
                $endcalc['course_id'] = $itemOne['course_id'];
                $endcalc['course_isrenew'] = $itemOne['course_isrenew'];
                $endcalc['class_id'] = $itemOne['class_id'];
                if ($itemOne['from_class_id'] == '0') {
                    $endcalc['endcalc_pid'] = 'C-' . $itemOne['school_id'] . '-' . $itemOne['class_id'];
                    $endcalc['endcalc_sort'] = 1;
                    $endcalc['endcalc_startdate'] = $itemOne['class_stdate'];
                    $endcalc['endcalc_begindate'] = $itemOne['class_begindate'];
                    $endcalc['endcalc_enddate'] = $itemOne['class_enddate'];
                    $endcalc['endcalc_fromlocus'] = $itemOne['class_id'];
                } else {
                    $endcalcOne = $this->DataControl->getFieldOne("smc_class_endcalc", "endcalc_pid,endcalc_sort,course_isrenew,endcalc_begindate,endcalc_startdate,endcalc_fromlocus", "class_id = '{$itemOne['from_class_id']}'");
                    if ($endcalcOne && $endcalcOne['course_isrenew'] <> '1') {
                        $endcalc['endcalc_pid'] = $endcalcOne['endcalc_pid'];
                        $endcalc['endcalc_sort'] = $endcalcOne['endcalc_sort'] + 1;
                        $endcalc['endcalc_begindate'] = $endcalcOne['endcalc_begindate'];
                        $endcalc['endcalc_startdate'] = $endcalcOne['endcalc_startdate'];
                        $endcalc['endcalc_enddate'] = $itemOne['class_enddate'];
                        $endcalc['endcalc_fromlocus'] = $endcalcOne['endcalc_fromlocus'] . ",{$itemOne['class_id']}";
                    } else {
                        $endcalc['endcalc_pid'] = 'C-' . $itemOne['school_id'] . '-' . $itemOne['class_id'];
                        $endcalc['endcalc_sort'] = 1;
                        $endcalc['endcalc_startdate'] = $itemOne['class_stdate'];
                        $endcalc['endcalc_begindate'] = $itemOne['class_begindate'];
                        $endcalc['endcalc_enddate'] = $itemOne['class_enddate'];
                        $endcalc['endcalc_fromlocus'] = $itemOne['class_id'];
                    }
                }

                $sql = "select ht.staffer_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch 
                          where ht.teachtype_code=ct.teachtype_code and ht.hour_id=ch.hour_id 
                          and ct.teachtype_code='05CM' 
                          and ct.company_id='{$itemOne['company_id']}' 
                          and ht.class_id='{$itemOne['class_id']}' 
                          and ht.teaching_type=0 
                          and ht.teaching_isdel=0 
                          and ch.hour_ischecking>=0
                          and exists(select 1 from smc_class_teach as ct where ct.class_id=ht.class_id and ct.staffer_id=ht.staffer_id)
                          order by ch.hour_day desc,ch.hour_starttime desc,ch.hour_id desc";

                $hourList = $this->DataControl->selectClear($sql);
                if ($hourList) {
                    $endcalc['endcalc_staffer_id'] = $hourList[0]['staffer_id'];

                    $times = 0;
                    foreach ($hourList as &$hourOne) {
                        if ($hourOne['staffer_id'] == $endcalc['endcalc_staffer_id']) {
                            $times++;
                        }
                    }
                    $endcalc['endcalc_staffer_times'] = $times;
                }

                $sql = "select ht.hour_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch 
                          where ht.teachtype_code=ct.teachtype_code 
                          and ht.hour_id=ch.hour_id 
                          and ct.teachtype_code='05CM' 
                          and ct.company_id='{$itemOne['company_id']}' 
                          and ht.class_id='{$itemOne['class_id']}' 
                          and ht.teaching_type=0 
                          and ht.teaching_isdel=0 
                          and ch.hour_ischecking>=0
                          group by ht.hour_id";
                $hourList = $this->DataControl->selectClear($sql);

                $endcalc['endcalc_chn_times'] = $hourList ? count($hourList) : 0;

                $endcalc['endcalc_createtime'] = time();
                $endcalc['endcalc_updatatime'] = time();
                $this->DataControl->insertData('smc_class_endcalc', $endcalc);


                //毕业班屏蔽班级
                if ($itemOne['course_isgraduated'] == '1') {
                    $data = array();
                    $data['class_isnotrenew'] = 1;
                    $data['class_updatatime'] = time();
                    $this->DataControl->updateData("smc_class", "class_id='{$itemOne['class_id']}'", $data);
                }
            } else if ($old_calc['endcalc_issettle'] <= '0') {
                if ($itemOne['class_status'] == '-2') {
                    $endcalc = array();
                    $endcalc['endcalc_issettle'] = -1;
                    $endcalc['endcalc_updatatime'] = time();
                    $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$old_calc['endcalc_id']}'", $endcalc);
                    continue;
                }
                if ($old_calc['endcalc_issettle'] < '0' && $itemOne['class_status'] >= '-2') {
                    $endcalc = array();
                    $endcalc['endcalc_issettle'] = 0;
                    $endcalc['endcalc_updatatime'] = time();
                    $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$old_calc['endcalc_id']}'", $endcalc);
                }

                $endcalc = array();
                $endcalc['endcalc_issettle'] = 0;
                if ($itemOne['from_class_id'] == '0') {
                    $endcalc['endcalc_pid'] = 'C-' . $itemOne['school_id'] . '-' . $itemOne['class_id'];
                    $endcalc['endcalc_sort'] = 1;
                    $endcalc['endcalc_startdate'] = $itemOne['class_stdate'];
                    $endcalc['endcalc_begindate'] = $itemOne['class_begindate'];
                    $endcalc['endcalc_enddate'] = $itemOne['class_enddate'];
                    $endcalc['endcalc_fromlocus'] = $itemOne['class_id'];
                } else {
                    $endcalcOne = $this->DataControl->getFieldOne("smc_class_endcalc", "endcalc_pid,endcalc_sort,course_isrenew,endcalc_begindate,endcalc_startdate,endcalc_fromlocus", "class_id = '{$itemOne['from_class_id']}'");
                    if ($endcalcOne && $endcalcOne['course_isrenew'] <> '1') {
                        $endcalc['endcalc_pid'] = $endcalcOne['endcalc_pid'];
                        $endcalc['endcalc_sort'] = $endcalcOne['endcalc_sort'] + 1;
                        $endcalc['endcalc_begindate'] = $endcalcOne['endcalc_begindate'];
                        $endcalc['endcalc_startdate'] = $endcalcOne['endcalc_startdate'];
                        $endcalc['endcalc_enddate'] = $itemOne['class_enddate'];
                        $endcalc['endcalc_fromlocus'] = $endcalcOne['endcalc_fromlocus'] . ",{$itemOne['class_id']}";
                    } else {
                        $endcalc['endcalc_pid'] = 'C-' . $itemOne['school_id'] . '-' . $itemOne['class_id'];
                        $endcalc['endcalc_sort'] = 1;
                        $endcalc['endcalc_startdate'] = $itemOne['class_stdate'];
                        $endcalc['endcalc_begindate'] = $itemOne['class_begindate'];
                        $endcalc['endcalc_enddate'] = $itemOne['class_enddate'];
                        $endcalc['endcalc_fromlocus'] = $itemOne['class_id'];
                    }
                }
                $sql = "select ht.staffer_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch
                          where ht.teachtype_code=ct.teachtype_code and ht.hour_id=ch.hour_id
                          and ct.teachtype_code='05CM'
                          and ct.company_id='{$itemOne['company_id']}'
                          and ht.class_id='{$itemOne['class_id']}'
                          and ht.teaching_type=0
                          and ht.teaching_isdel=0
                          and ch.hour_ischecking>=0
                          and exists(select 1 from smc_class_teach as ct where ct.class_id=ht.class_id and ct.staffer_id=ht.staffer_id)
                          order by ch.hour_day desc,ch.hour_starttime desc,ch.hour_id desc";

                $hourList = $this->DataControl->selectClear($sql);
                if ($hourList) {
                    $endcalc['endcalc_staffer_id'] = $hourList[0]['staffer_id'];

                    $times = 0;
                    foreach ($hourList as &$hourOne) {
                        if ($hourOne['staffer_id'] == $endcalc['endcalc_staffer_id']) {
                            $times++;
                        }
                    }
                    $endcalc['endcalc_staffer_times'] = $times;
                }

                $sql = "select ht.hour_id
                          from smc_class_hour_teaching as ht,smc_code_teachtype as ct,smc_class_hour as ch
                          where ht.teachtype_code=ct.teachtype_code 
                          and ht.hour_id=ch.hour_id 
                          and ct.teachtype_code='05CM' 
                          and ct.company_id='{$itemOne['company_id']}' 
                          and ht.class_id='{$itemOne['class_id']}' 
                          and ht.teaching_type=0 
                          and ht.teaching_isdel=0 
                          and ch.hour_ischecking>=0
                          group by ht.hour_id";
                $hourList = $this->DataControl->selectClear($sql);

                $endcalc['endcalc_chn_times'] = $hourList ? count($hourList) : 0;

                $endcalc['endcalc_updatatime'] = time();
                $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$old_calc['endcalc_id']}'", $endcalc);
            }

            //拆并班处理
            $breakOne = $this->DataControl->getFieldOne("smc_class_breakoff", "breakoff_id,class_id", "class_id = '{$itemOne['class_id']}' and breakoff_status>=0 and breakoff_type=0 order by breakoff_status desc");
            if ($breakOne && $itemOne['class_enddate'] >= '2023-05-29') {
                $sql = "select count(a.hour_id) as hour_num,max(b.course_classnum) as course_classnum
                    from smc_class_hour a
                    left join smc_course b on a.course_id=b.course_id
                    where a.class_id= '{$itemOne['class_id']}'
                    and a.hour_ischecking>0
                    and a.hour_isfree=0
                    group by a.class_id
                    limit 0,1";
                $hourinfo = $this->DataControl->selectOne($sql);
                if ($hourinfo && $hourinfo['hour_num'] >= $hourinfo['course_classnum']) {
                    //强制改为期末拆并班
                    $data = array();
                    $data['breakoff_type'] = 1;
                    $data['breakoff_confirm_staffer_id'] = '12357';
                    $data['breakoff_confirm_time'] = time();
                    $this->DataControl->updateData("smc_class_breakoff", "breakoff_id='{$breakOne['breakoff_id']}'", $data);
                }
            }
        }

        $sql = "SELECT c.company_id,c.class_id,c.school_id,c.from_class_id
                ,c.class_stdate,c.class_enddate
                ,(SELECT h.hour_day FROM smc_class_hour AS h WHERE h.class_id = c.class_id and h.hour_ischecking>=0 and h.hour_iswarming=0 AND h.hour_isfree=0 AND h.hour_lessontimes=1 
                    ORDER BY h.hour_ischecking DESC limit 0,1) AS class_begindate
                ,u.coursetype_id,u.coursecat_id,u.course_id
                ,u.course_isrenew
                ,c.class_status
                FROM smc_class c
                left join smc_course u on u.course_id=c.course_id
                left join smc_school s on c.school_id=s.school_id
                WHERE c.course_id = u.course_id
                AND c.company_id='{$paramArray['company_id']}' 
                AND c.class_enddate>curdate()
                AND c.class_type='0' 
                AND (u.coursetype_id in('65','79660','79661') or u.course_id in (92360,92361,92362,92363,92364,92365,92366,92367,92368,92369,92370,92371,92707,93087,93088,93089,93784,93785,93786,93787,93788))
                and s.school_istest=0
                and s.school_isclose=0
                and exists(select 1 from smc_class_endcalc where class_id=c.class_id)
                ORDER BY c.class_id";
        $changeArray = $this->DataControl->selectClear($sql);
        if ($changeArray) {
            foreach ($changeArray as $changeOne) {
                $oldCalc = $this->DataControl->getFieldOne("smc_class_endcalc", "endcalc_id", "class_id = '{$changeOne['class_id']}'");
                if ($oldCalc) {
                    $data = array();
                    $data['endcalc_issettle'] = '-1';
                    $data['endcalc_enddate'] = $changeOne['class_enddate'];
                    $data['endcalc_updatatime'] = time();
                    $this->DataControl->updateData("smc_class_endcalc", "endcalc_id='{$oldCalc['endcalc_id']}'", $data);
                    $detldata = array();
                    $detldata['study_iscalculate'] = '-1';
                    $detldata['study_updatetime'] = time();
                    $this->DataControl->updateData("smc_class_endcalc_study", "endcalc_id='{$oldCalc['endcalc_id']}'", $detldata);
                }
            }
        }
        return true;
    }

    function classSettle($paramArray)
    {
        $kkk = 0;
        $datawhere = "c.company_id = '{$paramArray['company_id']}' 
        AND (c.coursetype_id in('65','79660','79661') or c.course_id in (92360,92361,92362,92363,92364,92365,92366,92367,92368,92369,92370,92371,92707,93087,93088,93089,93784,93785,93786,93787,93788))";
        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " AND c.school_id = '{$paramArray['school_id']}'";
        }
        if (isset($paramArray['endcalc_id']) && $paramArray['endcalc_id'] !== '') {
            $datawhere .= " AND c.endcalc_id = '{$paramArray['endcalc_id']}'";
        }
        $itemOne = $this->DataControl->selectOne("SELECT c.endcalc_id,c.school_id,c.company_id,c.class_id,c.course_isrenew,c.coursetype_id
            ,c.endcalc_begindate,c.endcalc_enddate,c.endcalc_fromlocus,c.endcalc_issettle 
            FROM smc_class_endcalc AS c
            WHERE {$datawhere} 
            and c.endcalc_issettle<=0
            ORDER BY c.endcalc_issettle ASC,c.endcalc_updatatime ASC,c.endcalc_id ASC limit 0,1");
        if ($itemOne) {
            $endcalc = array();
            $endcalc['endcalc_issettle'] = 1;
            $endcalc['endcalc_updatatime'] = time();
            $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);

            $studentArray = $this->DataControl->selectClear("SELECT s.student_id,s.student_branch,s.student_cnname,
MIN(d.study_beginday) AS study_indate,Max(d.study_endday) AS study_outdate,MAX(d.class_id) AS class_id FROM smc_student AS s,smc_student_study AS d
WHERE s.student_id = d.student_id AND d.class_id IN ({$itemOne['endcalc_fromlocus']}) AND s.student_id IN (SELECT d.student_id FROM smc_student_hourstudy AS d WHERE d.class_id IN ({$itemOne['endcalc_fromlocus']})) GROUP BY s.student_id");
            if ($studentArray) {
                foreach ($studentArray as $studentOne) {
                    $study = array();
                    //入级时间小于等于七次课，离级日期大于等于班级结束日期为持续在班学生
                    if ($studentOne['study_indate'] <= $itemOne['endcalc_begindate'] && $studentOne['study_outdate'] >= $itemOne['endcalc_enddate']) {
//                        if ($this->DataControl->selectOne("SELECT i.* FROM smc_school_income AS i ,smc_course AS c WHERE
//i.course_id = c.course_id AND c.coursetype_id = '{$itemOne['coursetype_id']}' AND i.income_type = '0' AND i.income_price > 0
//AND i.student_id = '{$studentOne['student_id']}' AND i.school_id NOT IN ({$itemOne['school_id']})
//AND FROM_UNIXTIME(i.income_confirmtime, '%Y-%m-%d') >= '{$studentOne['study_outdate']}' limit 0,1")
//                        ) {
//                            $study['study_outtype'] = 2;//有他级班级耗课记录
//                        } else {
                        $study['study_outtype'] = 0;
//                        }
                    } else {
                        //判断是否期初内离开年级
                        if ($studentOne['study_outdate'] < $itemOne['endcalc_begindate']) {
                            $study['study_outtype'] = -1;
                        } else {
                            //判断是否中途离开年级
                            if ($studentOne['study_outdate'] < $itemOne['endcalc_enddate']) {
                                //判断是否离级后，有到同班组耗课行为
                                if ($this->DataControl->selectOne("SELECT i.* FROM smc_school_income AS i ,smc_course AS c WHERE
i.course_id = c.course_id AND c.coursetype_id = '{$itemOne['coursetype_id']}' AND i.income_type = '0' AND i.income_price > 0
AND i.student_id = '{$studentOne['student_id']}' AND i.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
AND FROM_UNIXTIME(i.income_confirmtime, '%Y-%m-%d') >= '{$studentOne['study_outdate']}' limit 0,1")
                                ) {
                                    $study['study_outtype'] = 2;//有他级班级耗课记录
                                } else {
                                    //班组流失
                                    if ($this->DataControl->selectOne("SELECT e.school_id FROM smc_student_enrolled AS e
WHERE e.school_id = '{$itemOne['school_id']}' AND e.student_id = '{$studentOne['student_id']}' AND e.enrolled_status = '-1'")
                                    ) {
                                        $study['study_outtype'] = 4;//离班后分校流失
                                    } else {
                                        $sql = "select cs.stustatus_isenschool,scl.changelog_day,scl.changelog_id
              from smc_student_changelog as scl,smc_code_stuchange as cs 
              where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type= '1' and scl.student_id='{$studentOne['student_id']}' and scl.school_id='{$itemOne['school_id']}'
              order by scl.changelog_day desc,scl.changelog_id desc";
                                        $logOne = $this->DataControl->selectOne($sql);
                                        if ($logOne['stustatus_isenschool'] != 1) {
                                            $study['study_outtype'] = 4;//离班后流失
                                        } else {
                                            $sql = "SELECT l.changelog_id, l.changelog_day, l.stuchange_code FROM smc_student_changelog l
WHERE l.school_id = '{$itemOne['school_id']}' AND l.student_id = '{$studentOne['student_id']}' AND ((l.stuchange_code = 'C04' AND l.coursetype_id = '{$itemOne['coursetype_id']}')
OR (l.stuchange_code = 'D04' AND l.coursetype_id = '{$itemOne['coursetype_id']}') OR l.stuchange_code = 'D02')
ORDER BY l.changelog_day DESC,l.changelog_id DESC LIMIT 0, 1";

                                            $typeOne = $this->DataControl->selectOne($sql);
                                            if ($typeOne && $typeOne['stuchange_code'] == 'C04') {
                                                $study['study_outtype'] = 4;//班组流失
                                            } else {
                                                $study['study_outtype'] = 3;//离班后未流失
                                            }
                                        }
                                    }
                                }
                            } else {
                                //中途插入且随班结束
                                $study['study_outtype'] = 1;
                            }
                        }
                    }

                    $kkk++;
                    $cousepriceOne = $this->DataControl->selectOne("SELECT (
                                    (SELECT sum(b.coursebalance_figure)
                                    FROM smc_student_coursebalance AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}')
                                    +
                                    (SELECT ifnull(SUM(b.log_playamount),0)
                                    FROM smc_student_coursebalance_log AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND b.hourstudy_id <> '0'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}'
                                    AND b.log_class = '0'
                                    AND b.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
                                    AND FROM_UNIXTIME(b.log_time, '%Y-%m-%d') > '{$studentOne['study_outdate']}')
                                    -
                                    (SELECT ifnull(SUM(f.order_arrearageprice), 0)
                                    FROM (
                                        SELECT x.order_arrearageprice FROM smc_payfee_order x, smc_payfee_order_course y, smc_course c
                                        WHERE x.order_pid = y.order_pid AND y.course_id = c.course_id AND x.student_id = '{$studentOne['student_id']}'
                                        AND order_status > -1 AND c.coursetype_id = '{$itemOne['coursetype_id']}'
                                        GROUP BY x.order_id ) f
                                    )
                                    ) AS allcouseprice
                                    ,(
                                    (SELECT sum(b.coursebalance_time)
                                    FROM smc_student_coursebalance AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}')
                                    +
                                    (SELECT ifnull(SUM(b.log_playtimes),0)
                                    FROM smc_student_coursebalance_log AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND b.hourstudy_id <> '0'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}'
                                    AND b.log_class = '0'
                                    AND b.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
                                    AND FROM_UNIXTIME(b.log_time, '%Y-%m-%d') >= '{$studentOne['study_outdate']}')
                                    -ifnull((select sum(unpaid_times) from smc_student_unpaid
                                    where school_id='{$itemOne['school_id']}' and student_id='{$studentOne['student_id']}'
                                    and coursetype_id='{$itemOne['coursetype_id']}'),0)
                                    ) AS allcousetimes
                                    ,(
                                    (SELECT sum(b.coursebalance_time*c.course_classtimerates)
                                    FROM smc_student_coursebalance AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}')
                                    +
                                    (SELECT ifnull(SUM(b.log_playtimes*c.course_classtimerates),0)
                                    FROM smc_student_coursebalance_log AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND b.hourstudy_id <> '0'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}'
                                    AND b.log_class = '0'
                                    AND b.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
                                    AND FROM_UNIXTIME(b.log_time, '%Y-%m-%d') >= '{$studentOne['study_outdate']}')
                                    -ifnull((select sum(unpaid_timesrate) from smc_student_unpaid
                                    where school_id='{$itemOne['school_id']}' and student_id='{$studentOne['student_id']}'
                                    and coursetype_id='{$itemOne['coursetype_id']}'),0)
                                    +
                                    (SELECT ifnull(SUM(plus_rate),0)
                                    FROM smc_class_endcalc_plus
                                    WHERE old_class_id='{$itemOne['class_id']}'
                                    AND student_id = '{$studentOne['student_id']}'
                                    AND plus_status=1)
                                    ) AS allcourserate");
                    $study['study_nextprice'] = $cousepriceOne['allcouseprice'];//代表未续费
                    $study['study_nexttimes'] = $cousepriceOne['allcousetimes'];//代表未续费
                    $study['study_upgraderate'] = $cousepriceOne['allcourserate'];//代表续费进度
                    $study['study_upgradeprice'] = $cousepriceOne['allcourserate'];//20241225处理三期联缴进度

                    if ($study['study_upgraderate'] < 100) {
                        $sql = "select c.course_classtimerates
                            ,c.course_upgradesection
                            ,(select count(1) from smc_class_hour 
                            where class_id=b.class_id and hour_ischecking>=0 and hour_isfree=0 and hour_day>=a.study_beginday and hour_day<=a.study_endday) as left_hour_times
                            from smc_student_study a 
                            left join smc_class b on a.class_id=b.class_id
                            left join smc_course c on b.course_id=c.course_id
                            where a.student_id='{$studentOne['student_id']}'
                            and c.coursetype_id='{$itemOne['coursetype_id']}'
                            and b.class_type=0
                            and b.class_status>-2
                            and a.study_endday>'{$studentOne['study_outdate']}' 
                            having left_hour_times>0
                            order by a.study_id";
                        $studynext = $this->DataControl->selectOne($sql);
                        if ($studynext && $studynext['left_hour_times'] >= $studynext['course_upgradesection']
                            && $study['study_upgraderate'] >= $studynext['left_hour_times'] * $studynext['course_classtimerates']) {
                            $study['study_upgraderate'] += (100 - $study['study_upgraderate']);
                        }
                    }

                    //处理出班日期问题，写入实际出班日期--为了解决公益学生耗课完移除公益标签的问题
                    $study_outdate = $studentOne['study_outdate'];
                    $studyall = $this->DataControl->selectClear("select hour_day as real_out_day,c.class_status,c.class_enddate
                    from smc_student_hourstudy a
                    left join smc_class_hour b on a.hour_id=b.hour_id
                    left join smc_class c on a.class_id=c.class_id
                    where a.student_id='{$studentOne['student_id']}'
                    and a.class_id='{$studentOne['class_id']}' 
                    order by hour_day desc ");

                    if ($studyall) {
                        $studyOne = $studyall[0];

                        if ($studyOne['class_status'] == '-1') {
                            //-1结班表示排课已考勤完，查询最大的考勤日期为出班日期
                            $study_outdate = $studyOne['real_out_day'];
                        } elseif ($studentOne['study_outdate'] == $studyOne['class_enddate']) {
                            //出班日期与班级结束日期一致的，取班级结束日期
                            $study_outdate = $studyOne['class_enddate'];
                        } else {
                            $unAtteOne = $this->DataControl->selectClear("SELECT hour_day as real_out_day 
                                FROM smc_class_hour a
                                where class_id = '{$studentOne['class_id']}' 
                                and hour_ischecking = 0 
                                and hour_isfree = 0
                                and hour_day >= '{$studentOne['study_indate']}' 
                                and hour_day < '{$studentOne['study_outdate']}' 
                                order by hour_day desc ");
                            if ($unAtteOne) {
                                //在未结班的情况下，如果有未考勤课次在出班日期之前，则以出版日期前最后排课日期作为实际出班日期
                                $study_outdate = $unAtteOne[0]['real_out_day'];
                            } else {
                                //否则表示出班日期前的排课均已考勤，还取之前查到的已考勤的最大日期为出班日期
                                $study_outdate = $studyOne['real_out_day'];
                            }
                        }
                    }

                    $study['study_iscalculate'] = 1;

                    if (!$this->DataControl->getFieldOne("smc_class_endcalc_study", "study_id", "student_id = '{$studentOne['student_id']}' AND endcalc_id = '{$itemOne['endcalc_id']}'")) {
                        $study['company_id'] = $itemOne['company_id'];
                        $study['school_id'] = $itemOne['school_id'];
                        $study['endcalc_id'] = $itemOne['endcalc_id'];
                        $study['class_id'] = $studentOne['class_id'];
                        $study['student_id'] = $studentOne['student_id'];
                        $study['study_indate'] = $studentOne['study_indate'];
                        $study['study_outdate'] = $study_outdate;
                        $study['study_createtime'] = time();
                        $this->DataControl->insertData('smc_class_endcalc_study', $study);
                    } else {
                        $study['class_id'] = $studentOne['class_id'];
                        $study['study_indate'] = $studentOne['study_indate'];
                        $study['study_outdate'] = $study_outdate;
                        $study['study_updatetime'] = time();
                        $this->DataControl->updateData('smc_class_endcalc_study', "student_id = '{$studentOne['student_id']}' AND endcalc_id = '{$itemOne['endcalc_id']}'", $study);
                    }
                }
//                    -------------------------------------------------------------------
                $endcalc = array();
                $endcalc['endcalc_issettle'] = 0;//非最终结算，状态为0
                $endcalc['endcalc_updatatime'] = time();
                $xxx = $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);
//                    -------------------------------------------------------------------
            } else {
//                    -------------------------------------------------------------------
                $endcalc = array();
                $endcalc['endcalc_issettle'] = -1;//非最终结算，状态为0
                $endcalc['endcalc_updatatime'] = time();
                $xxx = $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);
//                    -------------------------------------------------------------------
            }
            $this->oktip = "{$kkk}班级ID{$itemOne['class_id']}结算完毕";
            return true;
        } else {
            $this->error = true;
            $this->errortip = "未查询到任何待结班信息";
            return false;
        }
    }

    function classNewSettle($paramArray)
    {
        $nowdate = date("Y-m-d");
        $kkk = 0;
        $datawhere = "a.company_id = '{$paramArray['company_id']}' 
        AND (a.coursetype_id in('65','79660','79661') or a.course_id in (92360,92361,92362,92363,92364,92365,92366,92367,92368,92369,92370,92371,92707,93087,93088,93089,93784,93785,93786,93787,93788))";

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " AND a.school_id = '{$paramArray['school_id']}'";
        }

        $plus = " and not exists(select 1 from smc_class_endcalc_study where endcalc_id=a.endcalc_id 
                            and (study_createtime>UNIX_TIMESTAMP(CURDATE()) or study_updatetime>UNIX_TIMESTAMP(CURDATE())))";
        if (isset($paramArray['endcalc_id']) && $paramArray['endcalc_id'] !== '') {
            $datawhere .= " AND a.endcalc_id = '{$paramArray['endcalc_id']}'";
            $plus = "";
        }

        $sql = "SELECT a.endcalc_id
                ,a.company_id
                ,a.school_id
                ,a.class_id
                ,a.course_isrenew
                ,a.coursetype_id
                ,a.endcalc_begindate
                ,a.endcalc_enddate
                ,a.endcalc_fromlocus
                ,a.endcalc_issettle 
                ,b.class_id as next_class_id
                ,b.course_id as next_course_id
                ,c.class_status
                ,c.from_class_id
                ,c.course_id
                ,ifnull((select x.clockinginlog_createtime from smc_student_clockinginlog x 
                left join smc_student_hourstudy y on x.student_id=y.student_id and x.hourstudy_id=y.hourstudy_id 
                left join smc_class_hour z on y.class_id=z.class_id and y.hour_id=z.hour_id
                where y.class_id=b.class_id and z.hour_isfree=0 and z.hour_iswarming=0 and z.hour_ischecking>=0
                order by x.clockinginlog_createtime asc limit 0,1),0) as next_checking_time 
                from smc_class_endcalc a
                left join smc_class b on b.from_class_id=a.class_id and b.class_type=0
                left join smc_class c on a.class_id=c.class_id
                where {$datawhere}
                and a.endcalc_issettle=0
                and a.endcalc_enddate>='2025-01-01'
                {$plus}
                order by a.endcalc_id
                limit 0,1";
        $itemOne = $this->DataControl->selectOne($sql);
        //无班级或者班级无排课
        if (!$itemOne) {
            $this->error = true;
            $this->errortip = "未查询到任何待结班信息1";
            return false;
        }
        if ($itemOne['next_checking_time'] == '0' || $itemOne['endcalc_begindate'] == '') {
            $paramArray['company_id'] = $itemOne['company_id'];
            $paramArray['school_id'] = $itemOne['school_id'];
            $paramArray['endcalc_id'] = $itemOne['endcalc_id'];
            $xxx = $this->classSettle($paramArray);
            if ($xxx) {
                $breakOne = $this->DataControl->getFieldOne("smc_class_breakoff", "breakoff_id,class_id", "class_id = '{$itemOne['class_id']}' and breakoff_status>=0 order by breakoff_status desc");
                if ($breakOne && $itemOne['class_status'] == -1) {
                    $endcalc = array();
                    $endcalc['endcalc_issettle'] = 1;
                    $endcalc['endcalc_updatatime'] = time();
                    $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);
                }
                $this->oktip = "临时结算班级ID{$itemOne['class_id']}结算完毕";
                return true;
            } else {
                $this->error = true;
                $this->errortip = "临时结算失败";
                return false;
            }
        }

        //首先标记为已结算，使其它线程避开此班级
        if ($this->DataControl->getFieldOne("smc_class_endcalc", "endcalc_id", " class_id='{$itemOne['class_id']}' AND endcalc_issettle>0 ")) {
            $this->error = true;
            $this->errortip = "已结算，跳过";
            return false;
        }

        $firstdate = date("Y-m-d", $itemOne['next_checking_time']);
        $firsttime = strtotime($firstdate) + (3600 * 24);
        $endcalc = array();
        $endcalc['endcalc_issettle'] = $itemOne['endcalc_issettle'] + 1;
        $endcalc['endcalc_updatatime'] = time();
        $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);

        $sql = "select min(b.hour_day) as class_firstday
            ,max(b.hour_day) as class_lastday
            from smc_class a
            left join smc_class_hour b on a.class_id=b.class_id
            where 1
            and a.class_id IN ({$itemOne['endcalc_fromlocus']}) 
            and b.hour_ischecking=1
            and b.hour_isfree=0 
            and b.hour_iswarming=0
            limit 0,1";
        $hourArray = $this->DataControl->selectOne($sql);

        $sql = "select a.student_id
            ,min(b.hour_day) as study_indate
            ,max(b.hour_day) as study_outdate
            ,MAX(a.class_id) AS class_id 
            from smc_student_hourstudy a
            left join smc_class_hour b on a.class_id=b.class_id and a.hour_id=b.hour_id
            where 1
            and a.class_id IN ({$itemOne['endcalc_fromlocus']}) 
            and b.hour_ischecking=1
            and b.hour_isfree=0 
            and b.hour_iswarming=0
            group by a.student_id";
        $studentArray = $this->DataControl->selectClear($sql);

        if (!$studentArray || !$hourArray) {
            $endcalc = array();
            $endcalc['endcalc_issettle'] = -1;
            $endcalc['endcalc_updatatime'] = time();
            $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);
            $this->error = true;
            $this->errortip = "无效班级1，跳过";
            return false;
        }

        foreach ($studentArray as $studentOne) {
            $study = array();

            //判断学生出入班状态
            if ($studentOne['study_indate'] <= $hourArray['class_firstday'] && $studentOne['study_outdate'] >= $hourArray['class_lastday']) {
                $study['study_outtype'] = 0;//期初期末在班
            } else {
                //判断是否期初内离开年级
                if ($studentOne['study_outdate'] < $hourArray['class_firstday']) {
                    $study['study_outtype'] = -1;
                } else {
                    //判断是否中途离开年级
                    if ($studentOne['study_outdate'] < $hourArray['class_lastday']) {
                        //判断是否离级后，有到同班组耗课行为
                        if ($this->DataControl->selectOne("SELECT i.* FROM smc_school_income AS i ,smc_course AS c 
                        WHERE i.course_id = c.course_id 
                        AND c.coursetype_id = '{$itemOne['coursetype_id']}' 
                        AND i.income_type = '0' 
                        AND i.income_price > 0
                        AND i.student_id = '{$studentOne['student_id']}' 
                        AND i.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
                        AND FROM_UNIXTIME(i.income_confirmtime, '%Y-%m-%d') >= '{$studentOne['study_outdate']}' 
                        limit 0,1")) {
                            $study['study_outtype'] = 2;//出班后有其它级班级耗课记录
                        } else {
                            //班组流失
                            if ($this->DataControl->selectOne("SELECT e.school_id 
                                FROM smc_student_enrolled AS e
                                WHERE e.school_id = '{$itemOne['school_id']}' 
                                AND e.student_id = '{$studentOne['student_id']}' 
                                AND e.enrolled_status = '-1'")) {
                                $study['study_outtype'] = 4;//离班后分校流失
                            } else {
                                $sql = "select cs.stustatus_isenschool,scl.changelog_day,scl.changelog_id
                                          from smc_student_changelog as scl,smc_code_stuchange as cs 
                                          where scl.stuchange_code=cs.stuchange_code and cs.stuchange_type= '1' 
                                          and scl.student_id='{$studentOne['student_id']}' 
                                          and scl.school_id='{$itemOne['school_id']}'
                                          order by scl.changelog_id desc";
                                $logOne = $this->DataControl->selectOne($sql);
                                if ($logOne['stustatus_isenschool'] != 1) {
                                    $study['study_outtype'] = 4;//离班后流失
                                } else {
                                    $sql = "SELECT l.changelog_id, l.changelog_day, l.stuchange_code 
                                            FROM smc_student_changelog l
                                            WHERE l.school_id = '{$itemOne['school_id']}' 
                                            AND l.student_id = '{$studentOne['student_id']}' 
                                            AND ((l.stuchange_code = 'C04' AND l.coursetype_id = '{$itemOne['coursetype_id']}')
                                                OR (l.stuchange_code = 'D04' AND l.coursetype_id = '{$itemOne['coursetype_id']}') 
                                                OR l.stuchange_code = 'D02')
                                            ORDER BY l.changelog_day DESC,l.changelog_id DESC LIMIT 0,1";

                                    $typeOne = $this->DataControl->selectOne($sql);
                                    if ($typeOne && $typeOne['stuchange_code'] == 'C04') {
                                        $study['study_outtype'] = 4;//班组流失
                                    } else {
                                        $study['study_outtype'] = 3;//离班后未流失
                                    }
                                }
                            }
                        }
                    } else {
                        //中途插入且随班结束
                        $study['study_outtype'] = 1;
                    }
                }
            }

            $kkk++;
            $next_price = 0;
            $next_times = 0;
            $next_rates = 0;
//            $pass = 0;
            $student_exists = $this->DataControl->getFieldOne("smc_class_endcalc_study", "study_id"
                , "student_id = '{$studentOne['student_id']}' AND endcalc_id = '{$itemOne['endcalc_id']}'");

            $sql = "select a.student_id
            ,c.course_id as next_course_id
            ,d.log_fromamount
            ,d.log_fromtimes
            ,b.hour_day
            ,e.course_upgradesection
            ,e.course_classnum
            ,e.course_classtimerates
            ,f.class_id
            ,f.course_id as second_course_id
            from smc_student_hourstudy a
            left join smc_class_hour b on a.class_id=b.class_id and a.hour_id=b.hour_id
            left join smc_class c on a.class_id=c.class_id
            left join smc_student_coursebalance_log d on c.school_id=d.school_id and c.course_id=d.course_id and a.student_id=d.student_id and a.hourstudy_id=d.hourstudy_id 
            left join smc_course e on c.course_id=e.course_id
            left join smc_class f on f.from_class_id=c.class_id and f.class_status>-2 and f.class_type=0
            where 1
            and a.class_id ='{$itemOne['next_class_id']}'
            and a.student_id='{$studentOne['student_id']}'
            and b.hour_ischecking=1
            and e.course_isfollow=0
            and b.hour_isfree=0 
            and b.hour_iswarming=0
            order by d.log_fromtimes desc
            limit 0,1";

            $hourstudyOne = $this->DataControl->selectOne($sql);
            if ($hourstudyOne) {
                if ($hourstudyOne['hour_day'] <= $firstdate) {
                    $next_price += $hourstudyOne['log_fromamount'];
                    $next_times += $hourstudyOne['log_fromtimes'];
                    $next_rates += $hourstudyOne['log_fromtimes'] * $hourstudyOne['course_classtimerates'];

                    //如果实时课次小于课程标准课次且大于课程最小留班课次，则默认留班，补全个人留班率
                    if ($hourstudyOne['log_fromtimes'] >= $hourstudyOne['course_upgradesection'] && $hourstudyOne['log_fromtimes'] < $hourstudyOne['course_classnum']) {
                        $next_rates += (100 - $next_rates);
                    } else if ($hourstudyOne['log_fromtimes'] < $hourstudyOne['course_upgradesection']) {
                        //查下一个课程
                        $sql = "select sum(coursebalance_figure-plus_figure) as after_gifure
						,sum(coursebalance_time-plus_times) as after_times
						,sum((coursebalance_time-plus_times)*course_classtimerates) as after_rates
						from(
						select a.coursebalance_figure
						,a.coursebalance_time
						,ifnull((SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursebalance_log 
							WHERE student_id=A.student_id and school_id=A.school_id and course_id=a.course_id and log_reason<>'取消订单'
							AND log_time>='{$firsttime}' and log_class='0'),0) AS plus_figure
						,ifnull((SELECT sum(case when log_playclass='+' THEN log_playtimes ELSE -log_playtimes END) FROM smc_student_coursebalance_log 
							WHERE student_id=A.student_id and school_id=A.school_id and course_id=a.course_id and log_reason<>'取消订单'
							AND log_time>='{$firsttime}' and log_class='0'),0) AS plus_times 
						,b.course_classtimerates
						from smc_student_coursebalance a
						left join smc_course b on a.course_id=b.course_id
						where 1
						and a.student_id='{$studentOne['student_id']}'
						and a.course_id<>'{$itemOne['course_id']}'
						and b.coursetype_id='{$itemOne['coursetype_id']}'
						and a.coursebalance_createtime<'{$firsttime}'
						and b.course_isfollow=0
						)ta";
                        $afterOne = $this->DataControl->selectOne($sql);
                        if ($afterOne && $afterOne['after_rates'] >= 100) {
                            $next_price += $afterOne['after_gifure'];
                            $next_times += $afterOne['after_times'];
                            $next_rates += $afterOne['after_rates'];
                        }
                    }
                } else {
                    //第一节课没上，当作实时未续费
                }
            } else {
                // 20230423非升班学生是否入班30天内开班班级
                $sql = "select sum(all_price-pay_price) as unpaid_gifure,sum(unpaid_times) as unpaid_times,sum(unpaid_timesrate) as unpaid_rates
                from smc_student_unpaid
                where student_id='{$studentOne['student_id']}'
                and coursetype_id='{$itemOne['coursetype_id']}'
                ";

                $UnpaidOne = $this->DataControl->selectOne($sql);

                if ($UnpaidOne) {
                    $next_price -= $UnpaidOne['unpaid_gifure'];
                    $next_times -= $UnpaidOne['unpaid_times'];
                    $next_rates -= $UnpaidOne['unpaid_rates'];
                }

                $sql = "select * 
                    from smc_student_study a
                    left join smc_class b on a.class_id=b.class_id
                    left join smc_course c on c.course_id=b.course_id
                    where 1
                    and a.student_id='{$studentOne['student_id']}'
                    and c.coursetype_id='{$itemOne['coursetype_id']}'
                    and c.course_id<>'{$itemOne['course_id']}'
                    and a.class_id<>'{$itemOne['next_class_id']}'
                    and b.class_type=0
                    and b.class_status>=0
                    and a.study_isreading=1
                    and ifnull(b.class_enddate,'')<>''
                    and c.course_isfollow=0
                    and b.class_stdate>'{$studentOne['study_outdate']}'
                    and b.class_stdate<=DATE_SUB('{$studentOne['study_outdate']}', INTERVAL -30 DAY)
                    limit 0,1";
                $keepOne = $this->DataControl->selectOne($sql);

                $begintime = strtotime($hourArray['class_lastday']);
                $sql = "select sum(coursebalance_figure-plus_figure) as after_gifure
						,sum(coursebalance_time-plus_times) as after_times
						,sum((coursebalance_time-plus_times)*course_classtimerates) as after_rates
						from(
						select a.coursebalance_figure
						,a.coursebalance_time
						,ifnull((SELECT sum(case when log_playclass='+' THEN log_playamount ELSE -log_playamount END) FROM smc_student_coursebalance_log 
							WHERE student_id=A.student_id and school_id=A.school_id and course_id=a.course_id and log_reason<>'取消订单'
							AND ((log_time>='{$begintime}' and log_playclass='-') or log_time>='{$firsttime}') and log_class='0'),0) AS plus_figure
						,ifnull((SELECT sum(case when log_playclass='+' THEN log_playtimes ELSE -log_playtimes END) FROM smc_student_coursebalance_log 
							WHERE student_id=A.student_id and school_id=A.school_id and course_id=a.course_id and log_reason<>'取消订单'
							AND ((log_time>='{$begintime}' and log_playclass='-') or log_time>='{$firsttime}') and log_class='0'),0) AS plus_times 
						,b.course_classtimerates
						from smc_student_coursebalance a
						left join smc_course b on a.course_id=b.course_id
						where 1
						and a.student_id='{$studentOne['student_id']}'
						and a.course_id<>'{$itemOne['course_id']}'
						and a.course_id<>'{$itemOne['next_course_id']}'
						and b.coursetype_id='{$itemOne['coursetype_id']}'
						and b.course_isfollow=0
						and a.coursebalance_createtime<'{$firsttime}'
						)ta";
                $changeOne = $this->DataControl->selectOne($sql);
                if ($changeOne && $keepOne) {
                    $next_price += $changeOne['after_gifure'];
                    $next_times += $changeOne['after_times'];
                    $next_rates += $changeOne['after_rates'];
                }
            }
            //加上手工补充的smc_class_endcalc_plus

            //20241225处理三期联缴的部分
            $cousepriceOne = $this->DataControl->selectOne("SELECT (
                                    (SELECT sum(b.coursebalance_time*c.course_classtimerates)
                                    FROM smc_student_coursebalance AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    and c.course_isfollow=0
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}')
                                    +
                                    (SELECT ifnull(SUM(b.log_playtimes*c.course_classtimerates),0)
                                    FROM smc_student_coursebalance_log AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}'
                                    AND b.hourstudy_id <> '0'
                                    AND b.log_class = '0'
                                    and c.course_isfollow=0
                                    AND b.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
                                    AND FROM_UNIXTIME(b.log_time, '%Y-%m-%d') >= '{$studentOne['study_outdate']}')
                                    -ifnull((select sum(unpaid_timesrate) from smc_student_unpaid
                                    where school_id='{$itemOne['school_id']}' and student_id='{$studentOne['student_id']}'
                                    and coursetype_id='{$itemOne['coursetype_id']}'),0)
                                    ) AS allcourserate");
            $renewalrate = $cousepriceOne['allcourserate'];//三期联缴进度

            if ($next_rates < 100) {
                //查询上一条数据有效数据<100则
                $sql = "select a.study_iscalculate 
						from smc_class_endcalc_study a
						left join smc_class_endcalc b on a.endcalc_id=b.endcalc_id
						where study_iscalculate>=0 
						and a.student_id='{$studentOne['student_id']}'
						and b.coursetype_id='{$itemOne['coursetype_id']}'
						and b.class_id<>'{$itemOne['class_id']}'
						and a.study_outdate<=''
						order by a.study_outdate desc
						limit 0,1";
                $lastOne = $this->DataControl->selectOne($sql);

                if ($lastOne && $lastOne['study_iscalculate'] == -1) {
                    $study['study_iscalculate'] = -1;
                } else {
                    $study['study_iscalculate'] = 1;
                }
            } else {
                $study['study_iscalculate'] = 1;
            }
            $study['study_nextprice'] = $next_price;
            $study['study_nexttimes'] = $next_times;
            $study['study_upgraderate'] = $next_rates;
            $study['study_upgradeprice'] = $renewalrate;//暂不用//20241225处理三期联缴的部分
            $study['class_id'] = $studentOne['class_id'];
            $study['study_indate'] = $studentOne['study_indate'];
            $study['study_outdate'] = $studentOne['study_outdate'];

            if (!$student_exists) {
                $study['company_id'] = $itemOne['company_id'];
                $study['school_id'] = $itemOne['school_id'];
                $study['endcalc_id'] = $itemOne['endcalc_id'];
                $study['student_id'] = $studentOne['student_id'];
                $study['study_createtime'] = time();
                $xxx = $this->DataControl->insertData('smc_class_endcalc_study', $study);
            } else {
                $study['study_updatetime'] = time();
                $this->DataControl->updateData('smc_class_endcalc_study'
                    , "student_id = '{$studentOne['student_id']}' AND endcalc_id = '{$itemOne['endcalc_id']}'", $study);
            }
        }

        $this->oktip = "{$kkk}班级ID{$itemOne['class_id']}结算完毕";
        return true;
    }

    function classTempSettle($paramArray)
    {
        $nowdate = date("Y-m-d");
        $kkk = 0;
        $datawhere = "a.company_id = '{$paramArray['company_id']}' 
        AND (a.coursetype_id in('65','79660','79661') or a.course_id in (92360,92361,92362,92363,92364,92365,92366,92367,92368,92369,92370,92371,92707,93087,93088,93089,93784,93785,93786,93787,93788))";

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " AND a.school_id = '{$paramArray['school_id']}'";
        }

        $plus = " and not exists(select 1 from smc_class_endcalc_study where endcalc_id=a.endcalc_id 
                            and (study_createtime>UNIX_TIMESTAMP(CURDATE()) or study_updatetime>UNIX_TIMESTAMP(CURDATE())))";
        if (isset($paramArray['endcalc_id']) && $paramArray['endcalc_id'] !== '') {
            $datawhere .= " AND a.endcalc_id = '{$paramArray['endcalc_id']}'";
            $plus = "";
        }

        $sql = "SELECT a.endcalc_id
                ,a.company_id
                ,a.school_id
                ,a.class_id
                ,a.course_isrenew
                ,a.coursetype_id
                ,a.endcalc_begindate
                ,a.endcalc_enddate
                ,a.endcalc_fromlocus
                ,a.endcalc_issettle 
                ,b.class_id as next_class_id
                ,b.course_id as next_course_id
                ,c.from_class_id
                ,c.course_id
                ,ifnull((select x.clockinginlog_createtime from smc_student_clockinginlog x 
                left join smc_student_hourstudy y on x.student_id=y.student_id and x.hourstudy_id=y.hourstudy_id 
                left join smc_class_hour z on y.class_id=z.class_id and y.hour_id=z.hour_id
                where y.class_id=b.class_id and z.hour_isfree=0 and z.hour_iswarming=0 and z.hour_ischecking>=0
                order by x.clockinginlog_createtime asc limit 0,1),0) as next_checking_time 
                from smc_class_endcalc a
                left join smc_class b on b.from_class_id=a.class_id and b.class_type=0
                left join smc_class c on a.class_id=c.class_id
                where {$datawhere}
                and a.endcalc_issettle>0
                and a.endcalc_enddate>='2024-07-01'
                {$plus}
                order by a.endcalc_id
                limit 0,1";
        $itemOne = $this->DataControl->selectOne($sql);
        //无班级或者班级无排课
        if (!$itemOne) {
            $this->error = true;
            $this->errortip = "未查询到任何待结班信息1";
            return false;
        }
//        if ($itemOne['next_checking_time'] == '0' || $itemOne['endcalc_begindate'] == '') {
//            $paramArray['company_id'] = $itemOne['company_id'];
//            $paramArray['school_id'] = $itemOne['school_id'];
//            $paramArray['endcalc_id'] = $itemOne['endcalc_id'];
//            $xxx = $this->classSettle($paramArray);
//            if ($xxx) {
//                $this->oktip = "临时结算班级ID{$itemOne['class_id']}结算完毕";
//                return true;
//            } else {
//                $this->error = true;
//                $this->errortip = "临时结算失败";
//                return false;
//            }
//        }

        $endcalc = array();
        $endcalc['endcalc_updatatime'] = time();
        $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);

        $sql = "select min(b.hour_day) as class_firstday
            ,max(b.hour_day) as class_lastday
            from smc_class a
            left join smc_class_hour b on a.class_id=b.class_id
            where 1
            and a.class_id IN ({$itemOne['endcalc_fromlocus']}) 
            and b.hour_ischecking=1
            and b.hour_isfree=0 
            and b.hour_iswarming=0
            limit 0,1";
        $hourArray = $this->DataControl->selectOne($sql);

        $sql = "select a.student_id
            ,min(b.hour_day) as study_indate
            ,max(b.hour_day) as study_outdate
            ,MAX(a.class_id) AS class_id 
            from smc_student_hourstudy a
            left join smc_class_hour b on a.class_id=b.class_id and a.hour_id=b.hour_id
            where 1
            and a.class_id IN ({$itemOne['endcalc_fromlocus']}) 
            and b.hour_ischecking=1
            and b.hour_isfree=0 
            and b.hour_iswarming=0
            group by a.student_id";
        $studentArray = $this->DataControl->selectClear($sql);

        if (!$studentArray || !$hourArray) {
            $endcalc = array();
            $endcalc['endcalc_issettle'] = -1;
            $endcalc['endcalc_updatatime'] = time();
            $this->DataControl->updateData('smc_class_endcalc', "endcalc_id = '{$itemOne['endcalc_id']}'", $endcalc);
            $this->error = true;
            $this->errortip = "无效班级1，跳过";
            return false;
        }

        foreach ($studentArray as $studentOne) {
            $study = array();

            $kkk++;
            $student_exists = $this->DataControl->getFieldOne("smc_class_endcalc_study", "study_id"
                , "student_id = '{$studentOne['student_id']}' AND endcalc_id = '{$itemOne['endcalc_id']}'");

            //20241225处理三期联缴的部分
            $cousepriceOne = $this->DataControl->selectOne("SELECT (
                                    (SELECT sum(b.coursebalance_time*c.course_classtimerates)
                                    FROM smc_student_coursebalance AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    and c.course_isfollow=0
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}')
                                    +
                                    (SELECT ifnull(SUM(b.log_playtimes*c.course_classtimerates),0)
                                    FROM smc_student_coursebalance_log AS b,smc_course AS c
                                    WHERE b.course_id = c.course_id
                                    AND b.student_id = '{$studentOne['student_id']}'
                                    AND c.coursetype_id = '{$itemOne['coursetype_id']}'
                                    AND b.hourstudy_id <> '0'
                                    AND b.log_class = '0'
                                    and c.course_isfollow=0
                                    AND b.class_id NOT IN ({$itemOne['endcalc_fromlocus']})
                                    AND FROM_UNIXTIME(b.log_time, '%Y-%m-%d') >= '{$studentOne['study_outdate']}')
                                    -ifnull((select sum(unpaid_timesrate) from smc_student_unpaid
                                    where school_id='{$itemOne['school_id']}' and student_id='{$studentOne['student_id']}'
                                    and coursetype_id='{$itemOne['coursetype_id']}'),0)
                                    ) AS allcourserate");
            $renewalrate = $cousepriceOne['allcourserate'];//三期联缴进度

            $study['study_upgradeprice'] = $renewalrate;//暂不用//20241225处理三期联缴的部分

            if ($student_exists) {
                $study['study_updatetime'] = time();
                $this->DataControl->updateData('smc_class_endcalc_study', "student_id = '{$studentOne['student_id']}' AND endcalc_id = '{$itemOne['endcalc_id']}'", $study);
            }
        }

        $this->oktip = "{$kkk}班级ID{$itemOne['class_id']}结算完毕";
        return true;
    }

    function breakoffRefresh($paramArray)
    {
        $nowdate = date("Y-m-d");
        $kkk = 0;
        $datawhere = "a.company_id = '{$paramArray['company_id']}' 
        AND (a.coursetype_id in('65','79660','79661') or a.course_id in (92360,92361,92362,92363,92364,92365,92366,92367,92368,92369,92370,92371,92707,93087,93088,93089,93784,93785,93786,93787,93788))";

        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
            $datawhere .= " AND a.school_id = '{$paramArray['school_id']}'";
        }

        $plus = " and not exists(select 1 from smc_class_endcalc_study where endcalc_id=a.endcalc_id 
                            and (study_createtime>UNIX_TIMESTAMP(CURDATE()) or study_updatetime>UNIX_TIMESTAMP(CURDATE())))";
        if (isset($paramArray['endcalc_id']) && $paramArray['endcalc_id'] !== '') {
            $datawhere .= " AND a.endcalc_id = '{$paramArray['endcalc_id']}'";
            $plus = "";
        }

        $sql = "SELECT a.endcalc_id
                ,a.company_id
                ,a.school_id
                ,a.class_id
                from smc_class_endcalc a
                left join smc_class b on b.from_class_id=a.class_id and b.class_type=0
                left join smc_class c on a.class_id=c.class_id
                where {$datawhere}
                and a.endcalc_issettle=0
                and a.endcalc_enddate>=(CURDATE()-INTERVAL 6 MONTH)
                order by a.endcalc_id";
        $itemList = $this->DataControl->selectClear($sql);
//无班级或者班级无排课
        if (!$itemList) {
            $this->error = true;
            $this->errortip = "未查询到任何待结班信息1";
            return false;
        }
        foreach ($itemList as $itemOne) {
            //拆并班处理
            $breakOne = $this->DataControl->getFieldOne("smc_class_breakoff", "breakoff_id,class_id", "class_id = '{$itemOne['class_id']}' and breakoff_status>=0 and breakoff_type=0 order by breakoff_status desc");
            if ($breakOne) {
                $sql = "select count(a.hour_id) as hour_num,max(b.course_classnum) as course_classnum
                    from smc_class_hour a
                    left join smc_course b on a.course_id=b.course_id
                    where a.class_id= '{$itemOne['class_id']}'
                    and a.hour_ischecking>0
                    and a.hour_isfree=0
                    group by a.class_id
                    limit 0,1";
                $hourinfo = $this->DataControl->selectOne($sql);
                if ($hourinfo && $hourinfo['hour_num'] >= $hourinfo['course_classnum']) {
                    //强制改为期末拆并班
                    $data = array();
                    $data['breakoff_type'] = 1;
                    $data['breakoff_confirm_staffer_id'] = '12357';
                    $data['breakoff_confirm_time'] = time();
                    $this->DataControl->updateData("smc_class_breakoff", "breakoff_id='{$breakOne['breakoff_id']}'", $data);
                }
            }
        }
        return true;
    }

    function editOriClassId()
    {
        $sql = "select a.endcalc_id,b.class_id,b.from_class_id 
                from smc_class_endcalc a,smc_class b 
                where a.class_id=b.class_id and ori_class_id=0 
                order by a.endcalc_id ";
        $endcalcList = $this->DataControl->selectClear($sql);
        if ($endcalcList) {
            foreach ($endcalcList as $endcalcOne) {
                if ($endcalcOne['from_class_id'] == 0) {
                    $data = array();
                    $data['ori_class_id'] = $endcalcOne['class_id'];
                    $data['endcalc_updatatime'] = time();
                    $this->DataControl->updateData("smc_class_endcalc", "endcalc_id='{$endcalcOne['endcalc_id']}'", $data);
                    continue;
                }
                $ori_class_id = $endcalcOne['from_class_id'];
                $last_ori_class_id = $ori_class_id;
                for ($i = 0; $i < 20; $i++) {
                    $ori_class_id = $this->getOriClassId($ori_class_id);
                    if ($ori_class_id > 0) {
                        $last_ori_class_id = $ori_class_id;
                    } else {
                        break;
                    }
                }
                $data = array();
                $data['ori_class_id'] = $last_ori_class_id;
                $data['endcalc_updatatime'] = time();
                $this->DataControl->updateData("smc_class_endcalc", "endcalc_id='{$endcalcOne['endcalc_id']}'", $data);
            }
        }
        return true;
    }

    function getOriClassId($class_id)
    {
        $from_class = $this->DataControl->getFieldOne("smc_class", "from_class_id", "class_id = '{$class_id}'");
        $from_class_id = $from_class['from_class_id'];
        return $from_class_id;
    }

//学员续课包结算表
    function studentSettle($paramArray)
    {
        $datawhere = " 1 ";
//        if (isset($paramArray['school_id']) && $paramArray['school_id'] !== '') {
//            $datawhere .= " AND d.school_id = '{$paramArray['school_id']}'";
//        }

        $studentArray = $this->DataControl->selectClear("SELECT
         a.student_id
         ,a.school_id
         ,a.course_id
         ,a.pricinglog_id
         ,a.pricinglog_buytimes
         ,c.coursetype_id
        FROM
         smc_student_coursebalance_pricinglog AS a
         left join smc_payfee_order AS b on a.order_pid = b.order_pid 
         left join smc_course AS c on a.course_id = c.course_id 
         left join smc_school d on a.school_id=d.school_id
         left join smc_forward_dealorder e on a.order_pid=e.dealorder_pid and e.dealorder_type=1 
        WHERE {$datawhere}
        AND d.company_id = '{$paramArray['company_id']}' 
        AND c.course_sellclass = 1 
        and a.school_id=1175
        AND (b.order_status = '4' or e.dealorder_status='1')
        order by a.pricinglog_createtime");

        if ($studentArray) {
            foreach ($studentArray as $studentOne) {
                if ($this->DataControl->query("UPDATE temp_smc_hourplan 
                    SET pricinglog_id = '{$studentOne['pricinglog_id']}',hour_isbuy = '1'
                    WHERE student_id = '{$studentOne['student_id']}' 
                    AND school_id = '{$studentOne['school_id']}' 
                    AND coursetype_id = '{$studentOne['coursetype_id']}' 
                    AND (pricinglog_id = '0' OR pricinglog_id = '{$studentOne['pricinglog_id']}')
                    ORDER BY hour_day ASC,hourplan_rank ASC LIMIT {$studentOne['pricinglog_buytimes']}")) {
                    $this->DataControl->query("UPDATE temp_smc_hourplan 
                    SET hour_isnode = '1'
                    WHERE student_id = '{$studentOne['student_id']}' 
                    AND school_id = '{$studentOne['school_id']}' 
                    AND coursetype_id = '{$studentOne['coursetype_id']}' 
                    AND pricinglog_id = '{$studentOne['pricinglog_id']}'
                    ORDER BY hourplan_rank DESC LIMIT 1");
                    echo "{$studentOne['student_id']}结算完成<br />";
                }
            }
        }
    }

    function fillCourseType($paramArray)
    {
        // 预处理一部分可以确认的数据
        $sql = "update 
            smc_student_balancelog a 
            left join smc_student_trading b on a.trading_pid=b.trading_pid
            left join smc_course_reduceorder c on b.trading_pid=c.trading_pid
            left join smc_course d on d.course_id=c.course_id
            set a.coursetype_id=d.coursetype_id
            where a.company_id=8888
            and a.coursetype_id=0 
            and a.trading_pid like 'JY%'
            and b.tradingtype_code in('ReduceCourse')
            and d.coursetype_id>0
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)";
        $this->DataControl->selectClear($sql);

        $sql = "update 
            smc_student_balancelog a 
            left join smc_student_trading b on a.trading_pid=b.trading_pid
            left join smc_forward_dealorder c on b.trading_pid=c.trading_pid
            left join smc_forward_dealorder_course e on c.dealorder_pid=e.dealorder_pid
            left join smc_course d on d.course_id=e.course_id
            set a.coursetype_id=d.coursetype_id
            where a.company_id=8888
            and a.coursetype_id=0 
            and a.trading_pid like 'JY%'
            and b.tradingtype_code in('CourseForward')
            and d.coursetype_id>0
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)";
        $this->DataControl->selectClear($sql);

        $sql = "update 
            smc_student_balancelog a 
            left join smc_student_trading b on a.trading_pid=b.trading_pid
            left join smc_payfee_order c on b.trading_pid=c.trading_pid
            set a.coursetype_id=c.coursetype_id
            where a.company_id=8888
            and a.coursetype_id=0 
            and a.trading_pid like 'JY%'
            and b.tradingtype_code in('PayrenewFee','PaynewFee','CourseMakeUp')
            and c.coursetype_id>0
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)";
        $this->DataControl->selectClear($sql);

        //只处理在校生
        $sql = "select a.school_id,a.student_id
            from smc_student_balancelog a
            left join smc_student_enrolled b on a.school_id=b.school_id and a.student_id=b.student_id
            where a.company_id=8888
            and a.coursetype_id=0
            and a.balancelog_playamount<>0
            and b.enrolled_status<>-1
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)
            group by a.school_id,a.student_id
            order by a.school_id,a.student_id";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = true;
            $this->errortip = "未查询到任何待补全信息1";
            return false;
        }

        foreach ($studentList as $studentOne) {
            $this->fillCourseTypeBySchoolStudent($studentOne['school_id'], $studentOne['student_id']);
        }
    }

    function fillCourseTypeBySchoolStudent($school_id, $student_id)
    {
        //清除脏数据
        $sql = "delete a
            from smc_student_balancelog a 
            where a.school_id='{$school_id}' 
            and a.student_id='{$student_id}' 
            and a.balancelog_playamount=0 ";
        $this->DataControl->selectClear($sql);


        // 预处理一部分可以确认的数据
        $sql = "update 
            smc_student_balancelog a 
            left join smc_student_trading b on a.trading_pid=b.trading_pid
            left join smc_course_reduceorder c on b.trading_pid=c.trading_pid
            left join smc_course d on d.course_id=c.course_id
            set a.coursetype_id=d.coursetype_id
            where a.school_id='{$school_id}' 
            and a.student_id='{$student_id}'
            and a.coursetype_id=0 
            and a.trading_pid like 'JY%'
            and b.tradingtype_code in('ReduceCourse')
            and d.coursetype_id>0
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)";
        $this->DataControl->selectClear($sql);

        $sql = "update 
            smc_student_balancelog a 
            left join smc_student_trading b on a.trading_pid=b.trading_pid
            left join smc_forward_dealorder c on b.trading_pid=c.trading_pid
            left join smc_forward_dealorder_course e on c.dealorder_pid=e.dealorder_pid
            left join smc_course d on d.course_id=e.course_id
            set a.coursetype_id=d.coursetype_id
            where a.school_id='{$school_id}' 
            and a.student_id='{$student_id}'
            and a.coursetype_id=0 
            and a.trading_pid like 'JY%'
            and b.tradingtype_code in('CourseForward')
            and d.coursetype_id>0
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)";
        $this->DataControl->selectClear($sql);

        $sql = "update 
            smc_student_balancelog a 
            left join smc_student_trading b on a.trading_pid=b.trading_pid
            left join smc_payfee_order c on b.trading_pid=c.trading_pid
            set a.coursetype_id=c.coursetype_id
            where a.school_id='{$school_id}' 
            and a.student_id='{$student_id}'
            and a.coursetype_id=0 
            and a.trading_pid like 'JY%'
            and b.tradingtype_code in('PayrenewFee','PaynewFee','CourseMakeUp')
            and c.coursetype_id>0
            and a.balancelog_time> (UNIX_TIMESTAMP(now())-86400*15)";
        $this->DataControl->selectClear($sql);

//
//        //
//        $sql = "select a.*
//            from smc_student_balancelog a
//            left join smc_student_trading b on a.trading_pid=b.trading_pid
//            where a.school_id='{$school_id}'
//            and a.student_id='{$student_id}'
//            and a.coursetype_id=0
//            and a.trading_pid like 'JY%'
//            and b.
//            order by balancelog_id";
//        $itemList = $this->DataControl->selectClear($sql);
//
//        $num=count($itemList);

        return;
    }

    function autoGrantCoupons($paramArray)
    {
        //查询要发券的信息
        $sql = "select c.study_id,a.school_id,a.class_id,c.student_id,a.endcalc_enddate
            ,a.coursetype_id
            ,a.coursecat_id
            ,a.course_id
            ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1),'%Y-%m-%d') as pay_date
            ,(select x.pay_price from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1 order by x.pay_successtime desc limit 0,1) as pay_price
            ,(select y.order_allprice from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1 order by x.pay_successtime desc limit 0,1) as order_allprice
            ,(select y.order_pid from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1 order by x.pay_successtime desc limit 0,1) as order_pid
            from smc_class_endcalc a
            left join smc_school b on a.school_id=b.school_id
            left join smc_class_endcalc_study c on a.endcalc_id=c.endcalc_id
            where b.company_id=8888
            and b.school_isclose=0
            and b.school_istest=0
            and b.school_city<>107
            and c.study_outtype in('0','1','3','4') 
            and c.study_upgraderate>=100
            and a.endcalc_issettle=1
            and a.coursetype_id in(61,65)
            and a.endcalc_enddate>=DATE_SUB(CURDATE(),INTERVAL 2 WEEK)
            and not exists(select 1 from smc_student_coupons_grant where study_id=c.study_id)
            having order_allprice>0
            and pay_price/order_allprice>0.5
            and DATEDIFF(a.endcalc_enddate,pay_date)>=15 
            and DATEDIFF(a.endcalc_enddate,pay_date)<=70";
        $studyList = $this->DataControl->selectClear($sql);

        $coursearray = [92710, 92711, 92712, 92713, 92714, 92715, 92716, 92717, 92718, 92719, 92720, 92721];
        if ($studyList) {
            foreach ($studyList as $studyOne) {
                $newdata = array();
                $newdata['school_id'] = $studyOne['school_id'];
                $newdata['class_id'] = $studyOne['class_id'];
                $newdata['student_id'] = $studyOne['student_id'];
                $newdata['endcalc_id'] = $studyOne['endcalc_id'];
                $newdata['study_id'] = $studyOne['study_id'];
                $newdata['endcalc_enddate'] = $studyOne['endcalc_enddate'];
                $newdata['order_pid'] = $studyOne['order_pid'];
                $newdata['coursetype_id'] = $studyOne['coursetype_id'];
                $newdata['coursecat_id'] = $studyOne['coursecat_id'];
                $newdata['course_id'] = $studyOne['course_id'];
                $newdata['is_wki'] = in_array($studyOne['course_id'], $coursearray) ? 1 : 0;
                $newdata['pay_date'] = $studyOne['pay_date'];
                $newdata['pay_price'] = $studyOne['pay_price'];
                $newdata['order_allprice'] = $studyOne['order_allprice'];
                $newdata['grant_status'] = 0;
                $newdata['grant_createtime'] = time();
                $this->DataControl->insertData("smc_student_coupons_grant", $newdata);
            }
        }

        $sql = "select *,
        DATEDIFF(endcalc_enddate,pay_date) as diff_day,
        DATE_ADD(CURRENT_DATE,INTERVAL 1 YEAR) as end_day 
        from smc_student_coupons_grant 
        where grant_status=0 
        order by grant_id ";
        $grantList = $this->DataControl->selectClear($sql);
        if ($grantList) {
            foreach ($grantList as $grantOne) {
                if ($grantOne['coursetype_id'] == '61') {
                    if ($grantOne['diff_day'] >= 30 && $grantOne['diff_day'] <= 70) {
                        //100元券
                        $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 100, 1500, $grantOne['end_day']);
                    } else if ($grantOne['diff_day'] >= 15 && $grantOne['diff_day'] < 30) {
                        //50元券
                        $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 50, 1500, $grantOne['end_day']);
                    } else {
                        $grant_one = array();
                        $grant_one['grant_remk'] = "不符合自动发放条件";
                        $grant_one['grant_status'] = -1;
                        $grant_one['grant_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grantOne['grant_id']}'", $grant_one);
                    }
                }
                if ($grantOne['coursetype_id'] == '65') {
                    if (in_array($grantOne['course_id'], $coursearray)) {
                        if ($grantOne['diff_day'] >= 30 && $grantOne['diff_day'] <= 70) {
                            //250元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 250, 1500, $grantOne['end_day']);
                        } else if ($grantOne['diff_day'] >= 15 && $grantOne['diff_day'] < 30) {
                            //100元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 100, 1500, $grantOne['end_day']);
                        } else {
                            $grant_one = array();
                            $grant_one['grant_remk'] = "不符合自动发放条件";
                            $grant_one['grant_status'] = -1;
                            $grant_one['grant_updatetime'] = time();
                            $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grantOne['grant_id']}'", $grant_one);
                        }
                    } else {
                        if ($grantOne['diff_day'] >= 30 && $grantOne['diff_day'] <= 70) {
                            //150元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 150, 1500, $grantOne['end_day']);
                        } else if ($grantOne['diff_day'] >= 15 && $grantOne['diff_day'] < 30) {
                            //50元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 50, 1500, $grantOne['end_day']);
                        } else {
                            $grant_one = array();
                            $grant_one['grant_remk'] = "不符合自动发放条件";
                            $grant_one['grant_status'] = -1;
                            $grant_one['grant_updatetime'] = time();
                            $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grantOne['grant_id']}'", $grant_one);
                        }
                    }
                }
            }
        }
        return true;
    }

    function autoClassGrantCoupons($paramArray)
    {
        //查询要发券的信息-
        $sql = "select c.study_id,a.school_id,a.class_id,c.student_id,a.endcalc_enddate
            ,a.coursetype_id
            ,a.coursecat_id
            ,a.course_id
            ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1),'%Y-%m-%d') as pay_date
            ,(select x.pay_price from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1 order by x.pay_successtime desc limit 0,1) as pay_price
            ,(select y.order_allprice from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1 order by x.pay_successtime desc limit 0,1) as order_allprice
            ,(select y.order_pid from smc_payfee_order_pay x,smc_payfee_order y,smc_code_paytype z 
            where x.order_pid=y.order_pid and x.paytype_code=z.paytype_code and z.paytype_ischarge=1 and y.school_id=a.school_id and y.student_id=c.student_id and y.order_type=0 and y.coursetype_id=a.coursetype_id and y.coursecat_id=a.coursecat_id and x.pay_issuccess=1 order by x.pay_successtime desc limit 0,1) as order_pid
            from smc_class_endcalc a
            left join smc_school b on a.school_id=b.school_id
            left join smc_class_endcalc_study c on a.endcalc_id=c.endcalc_id
            left join smc_class d on a.class_id=d.class_id
            where b.company_id=8888
            and b.school_isclose=0
            and b.school_istest=0
            and b.school_city<>107
            and c.study_outtype in('0','1','3','4') 
            and c.study_upgraderate>=100
            and a.endcalc_issettle=1
            and a.coursetype_id in(61,65)
            and d.class_branch='{$paramArray['class_branch']}'
            and not exists(select 1 from smc_student_coupons_grant where study_id=c.study_id)
            having order_allprice>0
            and pay_price/order_allprice>0.5
            and DATEDIFF(a.endcalc_enddate,pay_date)>=15 
            and DATEDIFF(a.endcalc_enddate,pay_date)<=70";
        $studyList = $this->DataControl->selectClear($sql);


        $coursearray = [92710, 92711, 92712, 92713, 92714, 92715, 92716, 92717, 92718, 92719, 92720, 92721];
        if ($studyList) {
            foreach ($studyList as $studyOne) {
                $newdata = array();
                $newdata['school_id'] = $studyOne['school_id'];
                $newdata['class_id'] = $studyOne['class_id'];
                $newdata['student_id'] = $studyOne['student_id'];
                $newdata['endcalc_id'] = $studyOne['endcalc_id'];
                $newdata['study_id'] = $studyOne['study_id'];
                $newdata['endcalc_enddate'] = $studyOne['endcalc_enddate'];
                $newdata['order_pid'] = $studyOne['order_pid'];
                $newdata['coursetype_id'] = $studyOne['coursetype_id'];
                $newdata['coursecat_id'] = $studyOne['coursecat_id'];
                $newdata['course_id'] = $studyOne['course_id'];
                $newdata['is_wki'] = in_array($studyOne['course_id'], $coursearray) ? 1 : 0;
                $newdata['pay_date'] = $studyOne['pay_date'];
                $newdata['pay_price'] = $studyOne['pay_price'];
                $newdata['order_allprice'] = $studyOne['order_allprice'];
                $newdata['grant_status'] = 0;
                $newdata['grant_createtime'] = time();
                $this->DataControl->insertData("smc_student_coupons_grant", $newdata);
            }
        }

        $sql = "select *,
        DATEDIFF(endcalc_enddate,pay_date) as diff_day,
        DATE_ADD(CURRENT_DATE,INTERVAL 1 YEAR) as end_day 
        from smc_student_coupons_grant 
        where grant_status=0 
        order by grant_id ";
        $grantList = $this->DataControl->selectClear($sql);
        if ($grantList) {
            foreach ($grantList as $grantOne) {
                if ($grantOne['coursetype_id'] == '61') {
                    if ($grantOne['diff_day'] >= 30 && $grantOne['diff_day'] <= 70) {
                        //100元券
                        $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 100, 1500, $grantOne['end_day']);
                    } else if ($grantOne['diff_day'] >= 15 && $grantOne['diff_day'] < 30) {
                        //50元券
                        $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 50, 1500, $grantOne['end_day']);
                    } else {
                        $grant_one = array();
                        $grant_one['grant_remk'] = "不符合自动发放条件";
                        $grant_one['grant_status'] = -1;
                        $grant_one['grant_updatetime'] = time();
                        $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grantOne['grant_id']}'", $grant_one);
                    }
                }
                if ($grantOne['coursetype_id'] == '65') {
                    if (in_array($grantOne['course_id'], $coursearray)) {
                        if ($grantOne['diff_day'] >= 30 && $grantOne['diff_day'] <= 70) {
                            //250元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 250, 1500, $grantOne['end_day']);
                        } else if ($grantOne['diff_day'] >= 15 && $grantOne['diff_day'] < 30) {
                            //100元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 100, 1500, $grantOne['end_day']);
                        } else {
                            $grant_one = array();
                            $grant_one['grant_remk'] = "不符合自动发放条件";
                            $grant_one['grant_status'] = -1;
                            $grant_one['grant_updatetime'] = time();
                            $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grantOne['grant_id']}'", $grant_one);
                        }
                    } else {
                        if ($grantOne['diff_day'] >= 30 && $grantOne['diff_day'] <= 70) {
                            //150元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 150, 1500, $grantOne['end_day']);
                        } else if ($grantOne['diff_day'] >= 15 && $grantOne['diff_day'] < 30) {
                            //50元券
                            $this->grantCoupons($grantOne['grant_id'], $grantOne['school_id'], $grantOne['student_id'], $grantOne['coursetype_id'], 50, 1500, $grantOne['end_day']);
                        } else {
                            $grant_one = array();
                            $grant_one['grant_remk'] = "不符合自动发放条件";
                            $grant_one['grant_status'] = -1;
                            $grant_one['grant_updatetime'] = time();
                            $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grantOne['grant_id']}'", $grant_one);
                        }
                    }
                }
            }
        }
        return true;
    }

    function grantCoupons($grant_id, $school_id, $student_id, $coursetype_id, $coupons_price, $min_price = 1500, $end_date)
    {
        $applytype_branch = 'tiqianAUTO';
        $coupons_name = '提前续费券（自动版）';
        $coupons_pid = $this->create_guid();

        $apply_one = array();
        $apply_one['company_id'] = '8888';
        $apply_one['school_id'] = $school_id;
        $apply_one['student_id'] = $student_id;
        $apply_one['applytype_branch'] = $applytype_branch;
        $apply_one['apply_playclass'] = 1;
        $apply_one['apply_price'] = $coupons_price;
        $apply_one['apply_minprice'] = $min_price;
        $apply_one['apply_reson'] = '自动发放';
        $apply_one['apply_status'] = '4';
        $apply_one['apply_time'] = time();
        $apply_one['apply_cnname'] = '管理员';
        $apply_id = $this->DataControl->insertData("smc_student_coupons_apply", $apply_one);

        $coupons_one = array();
        $coupons_one['coupons_class'] = '3';
        $coupons_one['coupons_range'] = 0;
        $coupons_one['company_id'] = '8888';
        $coupons_one['student_id'] = $student_id;
        $coupons_one['apply_id'] = $apply_id;
        $coupons_one['coupons_pid'] = $coupons_pid;
        $coupons_one['coupons_name'] = $coupons_name;
        $coupons_one['coupons_type'] = 0;
        $coupons_one['coupons_playclass'] = 1;
        $coupons_one['coupons_price'] = $coupons_price;
        $coupons_one['coupons_minprice'] = $min_price;
        $coupons_one['coupons_reason'] = '自动发放';
        $coupons_one['coupons_discount'] = 0;
        $coupons_one['coupons_exittime'] = strtotime($end_date);
        $coupons_one['coupons_bindingtime'] = time();
        $coupons_one['coupons_createtime'] = time();
        $coupons_id = $this->DataControl->insertData("smc_student_coupons", $coupons_one);

//        $sql = " insert into xxx(apply_id,course_id)
//            select '{$apply_id}' as apply_id,course_id
//            from smc_course
//            where company_id='8888'
//            and course_status=1
//            and coursetype_id='{$coursetype_id}'";
//        $this->DataControl->selectClear($sql);

        $sql = " insert into smc_student_coupons_applycoursecat(apply_id,coursecat_id) 
            select '{$apply_id}' as apply_id,coursecat_id
            from smc_code_coursecat
            where company_id='8888'
            and coursetype_id='{$coursetype_id}'";
        $this->DataControl->selectClear($sql);

        $grant_one = array();
        $grant_one['apply_id'] = $apply_id;
        $grant_one['coupons_id'] = $coupons_id;
        $grant_one['grant_remk'] = "自动发放{$coupons_price}元券";
        $grant_one['grant_status'] = 1;
        $grant_one['grant_updatetime'] = time();
        $this->DataControl->updateData("smc_student_coupons_grant", "grant_id='{$grant_id}'", $grant_one);
    }

    function singleStudentRecharge($request)
    {
        do {
            $trading_pid = $this->createOrderPid('JY');
        } while ($this->DataControl->selectOne("select trading_id from smc_student_trading where trading_pid='{$trading_pid}' limit 0,1"));
        $orderdata = array();
        $orderdata['trading_pid'] = $trading_pid;
        $orderdata['company_id'] = 8888;
        $orderdata['school_id'] = $request['school_id'];
        $orderdata['companies_id'] = $request['companies_id'];
        $orderdata['student_id'] = $request['student_id'];
        $orderdata['tradingtype_code'] = 'Recharge';
        $orderdata['trading_status'] = '0';
        $orderdata['trading_createtime'] = time();
        $orderdata['staffer_id'] = '12357';
        $this->DataControl->insertData("smc_student_trading", $orderdata);

        do {
            $orderPid = $this->createOrderPid('SC');
        } while ($this->DataControl->selectOne("select order_id from smc_payfee_order where order_pid='{$orderPid}' and company_id='8888' limit 0,1"));

        $tradeData = array();
        $tradeData['order_pid'] = $orderPid;
        $tradeData['company_id'] = 8888;
        $tradeData['companies_id'] = $request['companies_id'];
        $tradeData['trading_pid'] = $trading_pid;
        $tradeData['mergeorder_pid'] = '';
        $tradeData['school_id'] = $request['school_id'];
        $tradeData['staffer_id'] = 12357;
        $tradeData['coursepacks_id'] = 0;
        $tradeData['student_id'] = $request['student_id'];
        $tradeData['order_from'] = 1;
        $tradeData['order_type'] = 2;
        $tradeData['order_status'] = 2;
        $tradeData['order_isneedaudit'] = 0;
        $tradeData['order_iscreatecourse'] = 0;
        $tradeData['order_taglist'] = '';
        $tradeData['order_allprice'] = 1500;
        $tradeData['order_paidprice'] = 0;
        $tradeData['order_coupon_price'] = 0;
        $tradeData['order_market_price'] = 0;
        $tradeData['order_note'] = '定金导入';
        $tradeData['parenter_id'] = 0;
        $tradeData['coursetype_id'] = 65;
        $tradeData['coursecat_id'] = 11902;
        $tradeData['order_paymentprice'] = 1500;
        $tradeData['order_createtime'] = time();
        $tradeData['order_updatatime'] = time();
        $this->DataControl->insertData("smc_payfee_order", $tradeData);

//        do {
//            $paypid = $this->createOrderPid('ZF');
//        } while ($this->DataControl->selectOne("select pay_id from smc_payfee_order_pay where pay_pid='{$paypid}' limit 0,1"));
//
//        $paydata = array();
//        $paydata['order_pid'] = $orderPid;
//        $paydata['pay_pid'] = $paypid;
//        $paydata['companies_id'] = $request['companies_id'];
//        $paydata['paychannel_code'] = 'cash';
//        $paydata['paytype_code'] = 'cash';
//        $paydata['pay_typename'] = '现金支付';
//        $paydata['pay_price'] = 1500;
//        $paydata['pay_type'] = 3;
//        $paydata['pay_issuccess'] = 1;
//        $paydata['pay_successtime'] = time();
//        $paydata['pay_createtime'] = time();
//        $paydata['pay_updatatime'] = time();
//        $paydata['pay_note'] = '导入寒暑假定金';
//        $this->DataControl->insertData("smc_payfee_order_pay", $paydata);
//
//        $paylogdata = array();
//        $paylogdata['order_pid'] = $orderPid;
//        $paylogdata['pay_pid'] = $paypid;
//        $paylogdata['paylog_actualprice'] = 1500;
//        $paylogdata['paylog_paytime'] = time();
//        $paylogdata['paylog_addtime'] = time();
//        $this->DataControl->insertData("smc_payfee_order_paylog", $paylogdata);
//
//        $sql = "select student_withholdbalance,student_balance
//            from smc_student_balance
//            WHERE company_id='8888'
//            and school_id = '{$request['school_id']}'
//            and student_id='{$request['student_id']}'
//            and companies_id='{$request['companies_id']}'";
//        $stublcOne = $this->DataControl->selectOne($sql);
//
//        $data = array();
//        $data['company_id'] = 8888;
//        $data['companies_id'] = $request['companies_id'];
//        $data['school_id'] = $request['school_id'];
//        $data['student_id'] = $request['student_id'];
//        if (!$stublcOne) {
//            $oriprice = 0;
//            $data['student_balance'] = 1500;
//            $data['student_withholdbalance'] = 0;
//            $this->DataControl->insertData("smc_student_balance", $data);
//        } else {
//            $oriprice = $stublcOne['student_balance'];
//            $data['student_balance'] = $stublcOne['student_balance'] + 1500;
//            $this->DataControl->updateData("smc_student_balance", " student_id='{$request['student_id']}' and school_id='{$request['school_id']}' and companies_id='{$request['companies_id']}'", $data);
//        }
//
//        $balancedata = array();
//        $balancedata['company_id'] = 8888;
//        $balancedata['companies_id'] = $request['companies_id'];
//        $balancedata['school_id'] = $request['school_id'];
//        $balancedata['staffer_id'] = 12357;
//        $balancedata['student_id'] = $request['student_id'];
//        $balancedata['trading_pid'] = $trading_pid;
//        $balancedata['coursetype_id'] = 65;
//        $balancedata['balancelog_class'] = 0;
//        $balancedata['balancelog_playname'] = '账户充值';
//        $balancedata['balancelog_playclass'] = '+';
//        $balancedata['balancelog_fromamount'] = $oriprice;
//        $balancedata['balancelog_playamount'] = 1500;
//        $balancedata['balancelog_finalamount'] = $oriprice + 1500;
//        $balancedata['balancelog_reason'] = '账户充值';
//        $balancedata['balancelog_time'] = time();
//        $this->DataControl->insertData("smc_student_balancelog", $balancedata);

        return true;
    }

    function createOrderPid($initial)
    {
        $Str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        $rangtr = $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)] . $Str[rand(0, 34)];
        $rangtime = date("ymdHis", time());
        $rangnum = rand(10000, 99999);
        $OrderPID = $initial . $rangtr . $rangtime . $rangnum;
        return $OrderPID;
    }

    function create_guid()
    {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $uuid = substr($charid, 0, 8)
            . substr($charid, 8, 4)
            . substr($charid, 12, 4)
            . substr($charid, 16, 4)
            . substr($charid, 20, 12);
        return $uuid;
    }

}