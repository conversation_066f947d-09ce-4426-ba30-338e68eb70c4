<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/9/15
 * Time: 23:59
 */

namespace Model\Api;


class JdbssoModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $aeskey = 'jdiebao095%C4567';
    public $pubfile = "Core/Tools/threeca/8888/sso_public_key.pem";
    public $prifile = "Core/Tools/threeca/8888/sso_private_key.pem";

    function __construct() {
        parent::__construct ();
    }

    function encryptCipher($dataArray){
        $dataString = json_encode($dataArray,JSON_UNESCAPED_UNICODE);
        $aes = new \Aesencdec($this->aeskey,'','AES-128-ECB');
        $ciphertext = $aes->encrypt($dataString);
        return $ciphertext;
    }


    //获得SIGN签名
    function getPostSign($ciphertext){
        $URLciphertext = urlencode($ciphertext);
        //通过URLciphertext和私钥rsaPrivateKey得到签名sing_byte，对sing_byte进行URL编码得到url_sing_byte。
        $RsaTo = new \Rsaencdec($this->pubfile, $this->prifile);
        $sing_byte = $RsaTo->sign($URLciphertext);
        $url_sing_byte = urlencode($sing_byte);
        return $url_sing_byte;
    }

    function getPostData($ciphertext){
        $URLciphertext = urlencode($ciphertext);
        return $URLciphertext;
    }



    //接受报文验签
    function getDataVerify($datacode,$datasign){
        $datacode = urlencode($datacode);
        $RsaTo = new \Rsaencdec($this->pubfile, $this->prifile);
        $singbyte = $RsaTo->verify($datacode,$datasign);
        return $singbyte;
    }


    //接受报文解密
    function getDataToInfp($datacode){
        $aes = new \Aesencdec($this->aeskey,'','AES-128-ECB');
        $decrypted = $aes->decrypt($datacode);
        $logArray = $this->parseJsonArray($decrypted);
        return $logArray;
    }

    function parseJsonArray($jsonString){
        $jsonString = htmlspecialchars_decode($jsonString);
        if($arr = json_decode($jsonString, true)){
            return $arr;
        }else{
            return false;
        }
    }
}