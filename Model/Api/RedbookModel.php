<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/12
 * Time: 22:59
 */

namespace Model\Api;


use FG\ASN1\Universal\Null;

class RedbookModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

//    public $app_secret = 'debac1091256dd89e36c53bb8df1f1b3';//小红书 私信api对接 的token

    //小红书 -- 吉的堡英语 -- 投放账号ID 7670374
    public $app_secret = 'de023440220c75616bbc59397177e1bc';//小红书 私信api对接 的token

    function __construct()
    {
        parent::__construct();
    }

    //获取口碑 sign 签名
    function getSign($data){
        $sign = hash_hmac('sha1',$data,$this->app_secret);
        return $sign;
    }

    //小红书 获取客资信息
    function addRedbookLeadsApi($request){
        //获取给的 header 中的数据
        $headers = getallheaders();
        $SignatureData = explode('=',$headers['X-Red-Signature']);

        //签名
        $RedSign = $this->getSign($request);
        if($SignatureData[1] != $RedSign){
            $this->error = 1;
            $this->errortip = "不是小红书渠道数据";
            return false;
        }

        //json数据转数组
        $readData = json_decode($request,true);
        $readOne = $readData['data'];

        if($readOne['phone_num'] == '' || !isset($readOne['phone_num']) || $readOne['phone_num'] == null){
            $log = array();
            $log['company_id'] = '8888';

            $log['redbookleads_xhsid'] = $readOne['id'];//私信留资 ID
            $log['redbookleads_red_id'] = $readOne['red_id'];//小红书 id
            $log['redbookleads_type'] = $readOne['type'];//类型，0:新增,1:更新内容，2：更新归因数据（计划 id/单元 id/创意 id/笔记）3: 同时更新内容和归因数据
            $log['redbookleads_time'] = $readOne['time'];// 私信线索创建时间
            $log['redbookleads_name'] = $readOne['nick_name'];//用户昵称
            $log['redbookleads_area'] = $readOne['area'];//省份地区
            $log['redbookleads_leads_tag'] = $readOne['leads_tag'];//线索标签，跟进中/留客资/高潜成交/已成单/无意向
            $log['redbookleads_mobile'] = $readOne['phone_num'];//电话号码
            $log['redbookleads_wechat'] = $readOne['wechat'];//微信号
            $log['redbookleads_remark'] = $readOne['remark'];//备注信息
            $log['redbookleads_ad_account'] = $readOne['ad_account'];//投放账户名
            $log['redbookleads_info_status'] = $readOne['info_status'];//是否留资，未留资/已留资
            $log['redbookleads_auto_recognize'] = $readOne['auto_recognize'];//是否自动识别，否/是
            $log['redbookleads_note_link'] = $readOne['note_link'];//笔记链接
            $log['redbookleads_unit_name'] = $readOne['unit_name'];//广告单元名称，非广告来源时，默认值为 ""
            $log['redbookleads_creativity_id'] = $readOne['creativity_id'];//广告创意 ID，非广告来源时，默认值为 -1
            $log['redbookleads_alljson'] = $request;
            $log['redbookleads_createtime'] = time();
            $this->DataControl->insertData('gmc_company_redbookleads_fail', $log);

            $this->error = 0;
            $this->errortip = "无手机号名单存储";
            return true;
        }

        $haveOne = $this->DataControl->selectOne(" select 1 from gmc_company_redbookleads where redbookleads_xhsid = '{$readOne['id']}' and redbookleads_mobile = '{$readOne['phone_num']}' and redbookleads_mobile <> ''  limit 0,1 ");
        if(!$haveOne){
            $haveOne = $this->DataControl->selectOne(" select 1 from gmc_company_redbookleads where redbookleads_xhsid = '{$readOne['id']}' and redbookleads_wechat = '{$readOne['wechat']}' and redbookleads_wechat <> '' and redbookleads_mobile = '{$readOne['phone_num']}'  limit 0,1 ");
        }

        if (!$haveOne) {
            $log = array();
            $log['company_id'] = '8888';

            $log['redbookleads_xhsid'] = $readOne['id'];//私信留资 ID
            $log['redbookleads_red_id'] = $readOne['red_id'];//小红书 id
            $log['redbookleads_type'] = $readOne['type'];//类型，0:新增,1:更新内容，2：更新归因数据（计划 id/单元 id/创意 id/笔记）3: 同时更新内容和归因数据
            $log['redbookleads_time'] = $readOne['time'];// 私信线索创建时间
            $log['redbookleads_name'] = $readOne['nick_name'];//用户昵称
            $log['redbookleads_area'] = $readOne['area'];//省份地区
            $log['redbookleads_leads_tag'] = $readOne['leads_tag'];//线索标签，跟进中/留客资/高潜成交/已成单/无意向
            $log['redbookleads_mobile'] = $readOne['phone_num'];//电话号码
            $log['redbookleads_wechat'] = $readOne['wechat'];//微信号
            $log['redbookleads_remark'] = $readOne['remark'];//备注信息
            $log['redbookleads_ad_account'] = $readOne['ad_account'];//投放账户名
            $log['redbookleads_info_status'] = $readOne['info_status'];//是否留资，未留资/已留资
            $log['redbookleads_auto_recognize'] = $readOne['auto_recognize'];//是否自动识别，否/是
            $log['redbookleads_note_link'] = $readOne['note_link'];//笔记链接
            $log['redbookleads_unit_name'] = $readOne['unit_name'];//广告单元名称，非广告来源时，默认值为 ""
            $log['redbookleads_creativity_id'] = $readOne['creativity_id'];//广告创意 ID，非广告来源时，默认值为 -1
            $log['redbookleads_alljson'] = $request;
            $log['redbookleads_createtime'] = time();
            $leadsId = $this->DataControl->insertData('gmc_company_redbookleads', $log);

            if ($leadsId) {

                if (!isset($readOne['phone_num']) || $readOne['phone_num'] == '' || $readOne['phone_num'] == 'NULL') {
                    $logone = array();
                    $logone['redbookleads_client_status'] = 2;
                    $logone['redbookleads_errortip'] = '手机号不存在';
                    $this->DataControl->updateData('gmc_company_redbookleads', "redbookleads_id = '{$leadsId}'", $logone);

                    $this->error = 1;
                    $this->errortip = "手机号不存在或者加密";
                    return false;
                }

                //名单的主要信息
                $params = array();
                $params['company_id'] = 8888;
                $params['mobile'] = $readOne['phone_num'];
                $params['nick_name'] = $readOne['nick_name'];
                $params['add_time'] = strtotime($readOne['time']);
                $rek = '';
                if($readOne['unit_name']){
                    $rek .= "【".$readOne['unit_name']."】";
                }
                if($readOne['creativity_id']){
                    $rek .= "【广告创意ID：".$readOne['creativity_id']."】";
                }
                if($readOne['remark']){
                    $rek .= "【".$readOne['remark']."】";
                }
                $params['remark'] = "小红书来源".$rek;

                //走 校务系统的名单处理
                $resultApi = $this->addClient($params);

                $logone = array();
                $logone['redbookleads_client_status'] = $resultApi['status'];
                $logone['redbookleads_errortip'] = $resultApi['errtip'];
                $this->DataControl->updateData('gmc_company_redbookleads', "redbookleads_id = '{$leadsId}'", $logone);

                $this->error = 0;
                $this->errortip = "名单已处理，回调成功";
                return true;
            }else{
                $log = array();
                $log['company_id'] = '8888';

                $log['redbookleads_xhsid'] = $readOne['id'];//私信留资 ID
                $log['redbookleads_red_id'] = $readOne['red_id'];//小红书 id
                $log['redbookleads_type'] = $readOne['type'];//类型，0:新增,1:更新内容，2：更新归因数据（计划 id/单元 id/创意 id/笔记）3: 同时更新内容和归因数据
                $log['redbookleads_time'] = $readOne['time'];// 私信线索创建时间
                $log['redbookleads_name'] = $readOne['nick_name'];//用户昵称
                $log['redbookleads_area'] = $readOne['area'];//省份地区
                $log['redbookleads_leads_tag'] = $readOne['leads_tag'];//线索标签，跟进中/留客资/高潜成交/已成单/无意向
                $log['redbookleads_mobile'] = $readOne['phone_num'];//电话号码
                $log['redbookleads_wechat'] = $readOne['wechat'];//微信号
                $log['redbookleads_remark'] = $readOne['remark'];//备注信息
                $log['redbookleads_ad_account'] = $readOne['ad_account'];//投放账户名
                $log['redbookleads_info_status'] = $readOne['info_status'];//是否留资，未留资/已留资
                $log['redbookleads_auto_recognize'] = $readOne['auto_recognize'];//是否自动识别，否/是
                $log['redbookleads_note_link'] = $readOne['note_link'];//笔记链接
                $log['redbookleads_unit_name'] = $readOne['unit_name'];//广告单元名称，非广告来源时，默认值为 ""
                $log['redbookleads_creativity_id'] = $readOne['creativity_id'];//广告创意 ID，非广告来源时，默认值为 -1
                $log['redbookleads_alljson'] = $request;
                $log['redbookleads_createtime'] = time();
                $this->DataControl->insertData('gmc_company_redbookleads_fail', $log);

                $this->error = 1;
                $this->errortip = "名单登记失败！";
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "名单已经登记过了";
            return false;
        }
    }

    //添加来源名单
    function addClient($paramArray){
        $channelOne = $this->DataControl->getFieldOne("crm_code_channel","channel_id,channel_medianame,channel_intention_level","company_id = '{$paramArray['company_id']}' AND channel_name = '小红书'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname","company_id = '{$paramArray['company_id']}' AND account_class = '1'");
        if($channelOne){
            $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            if ($familyOne) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "检测到在校生家长信息,学员姓名：{$familyOne['student_cnname']}，编号：{$familyOne['student_branch']}，无法添加CRM学员信息！";
                return $result;
            }
            if (!checkMobile($paramArray['mobile'])) {
                $result = array();
                $result['status'] = "0";
                $result['errtip'] = "手机号码加密，无法获取准确手机号，暂不处理！";
                return $result;
            }

            if($paramArray['school_branch'] !=''){
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}' and school_isclose = '0' and school_istest = '0' ");
                if(!$schoolOne){
//                    $result = array();
//                    $result['status'] = "0";
//                    $result['errtip'] = "校区编号{{$paramArray['school_branch']}}不存在，请检查校区编号！";
//                    return $result;
                    $schoolOne['school_id'] = 0;
                }
            }else{
                $schoolOne['school_id'] = 0;
            }

            $impotParam = array();
            $impotParam['company_id'] = $paramArray['company_id'];
            if(isset($schoolOne['school_id'])){
                $impotParam['school_id'] = $schoolOne['school_id'];
            }
            $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
            $paramOne['company_id'] = $paramArray['company_id'];
            $paramOne['client_cnname'] = $paramArray['nick_name']?$paramArray['nick_name']:'小红书用户';
            $paramOne['channel_id'] = $channelOne['channel_id'];
            $paramOne['client_source'] = $channelOne['channel_medianame'];
            $paramOne['client_remark'] = $paramArray['remark'];
            $paramOne['client_isfromgmc'] = '1';
            $paramOne['client_mobile'] = $paramArray['mobile'];
            $paramOne['client_isnewtip'] = '1';
            $paramOne['client_updatetime'] = $paramArray['add_time'];
            $paramOne['client_createtime'] = $paramArray['add_time'];
            if (!$ClientVerify->CrmClientVerify($paramOne)) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = $ClientVerify->errortip;
                return $result;
            }

            //客户不存在
            if(!$this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}'")){
                $data = array();
                $data['client_cnname'] = $paramArray['nick_name']?$paramArray['nick_name']:'小红书用户';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['company_id'] = $paramArray['company_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_remark'] = $paramArray['remark'];
                $data['client_isfromgmc'] = '1';
                $data['client_mobile'] = $paramArray['mobile'];
                if($channelOne['channel_intention_level'] > 0){
                    $data['client_intention_level'] = $channelOne['channel_intention_level'];
                    $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
                }
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['add_time'];
                $data['client_createtime'] = $paramArray['add_time'];
                $client_id = $this->DataControl->insertData("crm_client", $data);

                //添加名单状态记录
                $Model = new  \Model\Api\CalloutModel($paramOne);
                $Model->addClientTimerecord($paramArray['company_id'],$schoolOne['school_id'],$client_id,1,-1,"小红书三方对接录入");

                if ($schoolOne['school_id'] > 0 ) {
                    $datas = array();
                    $datas['client_id'] = $client_id;
                    $datas['school_id'] = $schoolOne['school_id'];
                    $datas['company_id'] = $paramArray['company_id'];
                    $datas['schoolenter_createtime'] = time();
                    $datas['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $datas);
                }
                $trackData = array();
                $trackData['client_id'] = $client_id;
                $trackData['marketer_id'] = '0';
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入（小红书用户）,系统新建客户信息;";
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单创建成功！";
                return $result;
            }

            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "名单通过校验，但未正常激活！";
            return $result;
        }else{
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "未检测到小红书渠道无法处理";
            return $result;
        }
    }


    //小红书 表单推送的数据 -- 20250211
    function addRedbookFormApi($request){
        //获取给的 header 中的数据
        $headers = getallheaders();
        $SignatureData = explode('=',$headers['X-Red-Signature']);

        $temp = array();
        $temp['formlog_json'] = $request;
        $temp['formlog_createtime'] = time();
        $this->DataControl->insertData('gmc_company_redbookleads_form_log', $temp);

        //签名1
        $RedSign = $this->getSign($request);
        if($SignatureData[1] != $RedSign){
            $this->error = 1;
            $this->errortip = "不是小红书渠道数据";
            return false;
        }
//        echo '通过签名';
//        print_r(json_decode($request,true));
//        die;

        //json数据转数组
        $readData = json_decode($request,true);
        $readOne = $readData['data'];
        $somedata = array();
        if($readOne['data']) {
            $somedata = array_column($readOne['data'], 'value', 'label');
        }
        $haveOne = $this->DataControl->selectOne(" select 1 from gmc_company_redbookleads_form where form_leads_id = '{$readOne['leads_id']}' and form_mobile = '{$readOne['手机号']}' and form_mobile <> ''  limit 0,1 ");
        if (!$haveOne) {
            $log = array();
            $log['company_id'] = '8888';

            $log['form_account_id'] = $readOne['account_id'];//
            $log['form_leads_id'] = $readOne['leads_id'];//
            $log['form_account_name'] = $readOne['account_name'];//
            $log['form_subtime'] = strtotime($readOne['submitted_time']);//

            $log['form_fname'] = $somedata['姓名'];//
            $log['form_mobile'] = $somedata['手机号'];//
            $log['form_email'] = $somedata['邮箱'];//

            $log['form_campaign_id'] = $readOne['campaign_id'];//
            $log['form_campaign_name'] = $readOne['campaign_name'];//
            $log['form_unit_id'] = $readOne['unit_id'];//
            $log['form_unit_name'] = $readOne['unit_name'];//
            $log['form_alljson'] = $request;
            $log['form_createtime'] = time();
            $leadsId = $this->DataControl->insertData('gmc_company_redbookleads_form', $log);

            if ($leadsId) {

                if (!isset($somedata['手机号']) || $somedata['手机号'] == '' || $somedata['手机号'] == 'NULL') {
                    $logone = array();
                    $logone['form_client_status'] = 2;
                    $logone['form_errortip'] = '手机号不存在';
                    $logone['form_status_time'] = time();
                    $this->DataControl->updateData('gmc_company_redbookleads_form', "form_id = '{$leadsId}'", $logone);

                    $this->error = 1;
                    $this->errortip = "手机号不存在或者加密";
                    return false;
                }

                //名单的主要信息
                $params = array();
                $params['company_id'] = 8888;
                $params['mobile'] = $somedata['手机号'];
                $params['nick_name'] = $somedata['姓名'];
                $params['add_time'] = strtotime($readOne['submitted_time']);
                $rek = '';
                if($readOne['unit_name']){
                    $rek .= "【".$readOne['unit_name']."】";
                }
                if($readOne['creativity_id']){
                    $rek .= "【广告创意ID：".$readOne['creativity_id']."】";
                }
                if($readOne['remark']){
                    $rek .= "【".$readOne['remark']."】";
                }
                $params['remark'] = "小红书表单推送来源".$rek;

                //走 校务系统的名单处理
                $resultApi = $this->addClient($params);

                $logone = array();
                $logone['form_client_status'] = $resultApi['status'];
                $logone['form_errortip'] = $resultApi['errtip'];
                $logone['form_status_time'] = time();
                $this->DataControl->updateData('gmc_company_redbookleads_form', "form_id = '{$leadsId}'", $logone);

                $this->error = 0;
                $this->errortip = "名单已处理，回调成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "名单登记失败！";
                return false;
            }

        }else{
            $this->error = 1;
            $this->errortip = "名单已经登记过了";
            return false;
        }

    }

}