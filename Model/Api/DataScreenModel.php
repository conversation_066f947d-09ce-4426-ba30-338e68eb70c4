<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 15:21
 */

namespace Model\Api;

class DataScreenModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
//    public $aeskey = 'jidebaosmc%C0515';
//    public $aesiv = 'jdbCuBC7orQtDhTO';
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function api1_1($request)
    {
        $datawhere = " A.company_id='{$request['company_id']}' ";
        $today = date("Y-m-d");
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
        }

        $data = array();

        $sql = "SELECT a.student_id
                FROM smc_student_coursebalance A
                left join smc_student B on A.student_id=B.student_id
                LEFT JOIN smc_course C ON A.course_id = C.course_id
                WHERE {$datawhere} 
                and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
                and a.coursebalance_figure > 0
                GROUP BY a.school_id,a.student_id,C.coursetype_id ";

        $residenceStudentList = $this->DataControl->selectClear($sql);
        if ($residenceStudentList) {
            $residenceNum = count($residenceStudentList);
        } else {
            $residenceNum = 0;
        }

        $sql = "SELECT A.school_id,
                A.student_id,
                C.coursetype_id,
                MIN(A.study_beginday) AS beginday,
                MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                WHERE {$datawhere}
                AND B.class_type='0' 
                AND B.class_status>'-2'
                and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
                AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END)>= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                GROUP BY A.school_id,A.student_id,C.coursetype_id
                HAVING beginday<='{$today}' and endday>='{$today}' 
            ";

        $studyStudentList = $this->DataControl->selectClear($sql);
        if ($studyStudentList) {
            $studyNum = count($studyStudentList);
        } else {
            $studyNum = 0;
        }

        //在开班级
        $sql = "select a.class_id
              from smc_class A
              left join smc_course C on C.course_id=a.course_id
              where {$datawhere} 
              and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
              and a.class_status>='0' 
              and a.class_type='0' 
              and a.class_stdate<=CURDATE() 
              and (a.class_enddate>=CURDATE() or a.class_enddate='') 
              group by a.class_id";
        $classList = $this->DataControl->selectClear($sql);
        if ($classList) {
            $classNum = count($classList);
        } else {
            $classNum = 0;
        }

        array_push($data, $residenceNum, $studyNum, $classNum);

        return $data;
    }

    function api1_2($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' ";
        $today = date("Y-m-d");
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and b.coursetype_id='{$request['coursetype_id']}'";
        }


        $sql = "select age
            ,count(if(student_sex='男',true,null)) as boy_nums 
            ,count(if(student_sex='女',true,null)) as girl_nums 
            from(
            SELECT a.school_id, a.student_id, b.coursetype_id,st.student_sex,TIMESTAMPDIFF(YEAR, st.student_birthday, CURDATE()) as age
            FROM smc_student_coursebalance AS a
            LEFT JOIN smc_course b ON a.course_id = b.course_id
            left join smc_student as st on st.student_id=a.student_id
            WHERE {$datawhere} 
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.coursebalance_figure > 0
            and st.student_birthday<>''
            GROUP BY a.school_id, a.student_id, b.coursetype_id
            having age>=4 and age<=12
            )ta 
            group by age
            order by age";


        $ageList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($ageList as $key => $val) {
//            $data[$val['age'].'岁'] = array($val['boy_nums'],$val['girl_nums']);
            $data[$val['age'] . '岁'][0] = $val['boy_nums'];
            $data[$val['age'] . '岁'][1] = $val['girl_nums'];
        }

        return $data;
    }

    function api1_3($request)
    {
        $datawhere = " a.company_id='{$request['company_id']}' ";
        $today = date("Y-m-d");
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}'";
        }

        $sql = "SELECT
                b.school_shortname,
                count(1) as reg_num
            FROM
                smc_student_registerinfo AS a
                LEFT JOIN smc_school b ON a.school_id = b.school_id
            WHERE {$datawhere} 
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.info_status=1
            and a.pay_successtime<= unix_timestamp(now())
            and a.pay_successtime>= UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            GROUP BY a.school_id
            order by reg_num DESC
            limit 0,5";

        $regList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($regList as $key => $val) {
//            $data[$val['age'].'岁'] = array($val['boy_nums'],$val['girl_nums']);
            $data[$val['school_shortname']] = $val['reg_num'];
        }

        return $data;
    }

    function api1_4($request)
    {
        $datawhere = " A.company_id='{$request['company_id']}' ";
        $today = date("Y-m-d");
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
        }

        $sql = "SELECT tb.school_shortname,ta.school_id,count(ta.student_id) as regi_num
            from(
            SELECT
                A.school_id,
                A.student_id,
                C.coursetype_id 
            FROM
                smc_student_coursebalance A
                LEFT JOIN smc_student B ON B.student_id = a.student_id 
                LEFT JOIN smc_course C ON A.course_id = C.course_id
            WHERE {$datawhere} 
                and A.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
                AND A.coursebalance_figure > 0 
            GROUP BY
                A.school_id,
                A.student_id,
                C.coursetype_id
            )ta 
            left join smc_school tb on ta.school_id=tb.school_id 
            group by ta.school_id
            order by regi_num desc";


        $regiList = $this->DataControl->selectClear($sql);

        $sql = "SELECT ta.school_id,count(ta.student_id) as read_num
                from(
                SELECT A.school_id,
                    A.student_id,
                    C.coursetype_id,
                    MIN(A.study_beginday) AS beginday,
                    MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                    FROM smc_student_study A
                    LEFT JOIN smc_class B ON A.class_id=B.class_id 
                    LEFT JOIN smc_course C ON C.course_id=B.course_id 
                    LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                    WHERE {$datawhere}
                    AND B.class_type='0' 
                    AND B.class_status>'-2'
                    and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
                    AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) >= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                    GROUP BY A.school_id,A.student_id,C.coursetype_id
                    HAVING beginday<='{$today}' and endday>='{$today}'
                )ta
                group by ta.school_id";

        $readList = $this->DataControl->selectClear($sql);

        $sql = "SELECT B.school_id,COUNT(A.staffer_id) AS emp_num                
            FROM smc_staffer A 
            LEFT JOIN gmc_staffer_postbe B ON A.staffer_id = B.staffer_id
            WHERE A.company_id='{$request['company_id']}' 
            and B.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and B.postbe_status='1' 
            and B.postbe_ismianjob='1' 
            AND A.staffer_leave=0
            GROUP BY B.school_id ";
        $empList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($regiList as $key => $val) {
            $school_id = $val['school_id'];
            $data[$key][0] = $val['school_shortname'];
            $data[$key][1] = $val['regi_num'];
            foreach ($readList as $readOne) {
                if ($readOne['school_id'] == $school_id) {
                    $data[$key][2] = $readOne['read_num'];
                }
            }

            foreach ($empList as $empOne) {
                if ($empOne['school_id'] == $school_id) {
                    $data[$key][3] = $empOne['emp_num'];
                }
            }
        }
        $res = array();
        $res['list'] = $data;

        $map = [
            ['name' => '宝山区', 'value' => 1522],
            ['name' => '金山区', 'value' => 2622],
            ['name' => '长宁区', 'value' => 1220],
            ['name' => '静安区', 'value' => 522],
            ['name' => '普陀区', 'value' => 722],
            ['name' => '虹口区', 'value' => 1022],
            ['name' => '杨浦区', 'value' => 892],
            ['name' => '闵行区', 'value' => 1722],
            ['name' => '宝山区', 'value' => 2201],
            ['name' => '嘉定区', 'value' => 1782],
            ['name' => '浦东新区', 'value' => 1232],
            ['name' => '金山区', 'value' => 1022],
            ['name' => '松江区', 'value' => 1782],
            ['name' => '青浦区', 'value' => 1232],
            ['name' => '奉贤区', 'value' => 1782],
            ['name' => '崇明区', 'value' => 1232],
        ];
        $res['map'] = $map;
        return $res;
    }

    function api1_5($request)
    {
        $datawhere = " C.company_id='{$request['company_id']}' ";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and D.coursetype_id='{$request['coursetype_id']}'";
        }


        $sql = "SELECT 
            count(if(b.hour_day>=DATE_ADD(curdate(),interval -day(curdate())+1 day) and hourstudy_checkin=1,true,null)) as this_month_atte,
            count(if(b.hour_day>=DATE_ADD(curdate(),interval -day(curdate())+1 day),true,null)) as this_month_all,
            count(if(b.hour_day>=date_add(curdate()-day(curdate())+1,interval -1 month) and b.hour_day<=last_day(date_sub(now(),interval 1 month)) and hourstudy_checkin=1,true,null)) as last_month_atte,
            count(if(b.hour_day>=date_add(curdate()-day(curdate())+1,interval -1 month) and b.hour_day<=last_day(date_sub(now(),interval 1 month)),true,null)) as last_month_all,
            count(if(hourstudy_checkin=1,true,null)) as three_month_atte,
            count(1) as three_month_all
            FROM smc_student_hourstudy A 
            LEFT JOIN smc_class_hour B ON A.hour_id=B.hour_id 
            LEFT JOIN smc_class C ON C.class_id = B.class_id 
            LEFT JOIN smc_course D ON D.course_id = C.course_id 
            WHERE {$datawhere}
            and C.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and B.hour_day>=DATE_SUB(CURDATE(),INTERVAL 90 day)
            and B.hour_day<=CURDATE() ";


        $atteList = $this->DataControl->selectOne($sql);

        $data = array();
        if ($atteList) {
            $this_month_atte = $atteList['this_month_atte'];
            $this_month_all = $atteList['this_month_all'];
            $last_month_atte = $atteList['last_month_atte'];
            $last_month_all = $atteList['last_month_all'];
            $three_month_atte = $atteList['three_month_atte'];
            $three_month_all = $atteList['three_month_all'];
            $data[0][0] = ceil($this_month_atte / $this_month_all * 100);
            $data[0][1] = 100 - $data[0][0];
            $data[1][0] = ceil($last_month_atte / $last_month_all * 100);
            $data[1][1] = 100 - $data[1][0];
            $data[2][0] = ceil($three_month_atte / $three_month_all * 100);
            $data[2][1] = 100 - $data[2][0];

        } else {
            $data = [
                [82, 18], //第一个饼状图
                [68, 32], //第二个饼状图
                [88, 12], //第三个饼状图
            ];
        }

        return $data;
    }

    function api1_6($request)
    {
        $datawhere = " A.company_id='{$request['company_id']}' ";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and A.coursetype_id='{$request['coursetype_id']}'";
        }
//        $today = date('Y-m-d');
        $today = date("Ymd");
//        var_dump($today);
//        die;

        $data = array();

        $sql = "select CONCAT( DATE_FORMAT(ta.regi_date, '%Y%m'),(case when dayofmonth( ta.regi_date ) < 16 then '01' else '16' end) ) as halfmonth
            ,count( student_id ) as regi_num
            from
            (
                SELECT student_id,FROM_UNIXTIME(A.pay_successtime,'%Y-%m-%d') AS regi_date
                FROM smc_student_registerinfo AS a
                WHERE {$datawhere}
                  and a.info_status=1
                  and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}') 
                  and a.pay_successtime<= unix_timestamp(now()) 
                  and a.pay_successtime>= UNIX_TIMESTAMP('2020-12-16') 
            )ta
            group by halfmonth
            order by halfmonth";

        $regiList = $this->DataControl->selectClear($sql);

        $sql = "select CONCAT( DATE_FORMAT(ta.hour_day, '%Y%m'),(case when dayofmonth( ta.hour_day ) < 16 then '01' else '16' end) ) as halfmonth
            ,count( student_id ) as read_num 
            from(
            SELECT A.student_id,MIN(B.hour_day) AS hour_day 
            FROM smc_student_hourstudy A 
            LEFT JOIN smc_class_hour B ON A.hour_id=B.hour_id 
            LEFT JOIN smc_class C ON C.class_id = B.class_id 
            LEFT JOIN smc_course D ON D.course_id = C.course_id 
            WHERE 1
            AND C.company_id='{$request['company_id']}'
            and C.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')    
            AND D.coursetype_id='{$request['coursetype_id']}'
            GROUP BY A.student_id 
            HAVING hour_day>='2020-12-16' 
            AND hour_day<=CURDATE() 
            )ta
            group by halfmonth
            order by halfmonth
            ";

        $readList = $this->DataControl->selectClear($sql);

        $sql = "select CONCAT( DATE_FORMAT(ta.lost_date, '%Y%m'),(case when dayofmonth( ta.lost_date ) < 16 then '01' else '16' end) ) as halfmonth
                ,count( student_id ) as lost_num
                from(
                select A.student_id
                ,min(A.changelog_day) as lost_date
                from smc_student_changelog A
                left join smc_student_change B on B.change_pid=A.change_pid
                where A.company_id='{$request['company_id']}'
                and A.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')  
                and A.stuchange_code in('C02','C04')
                and A.coursetype_id in('0','{$request['coursetype_id']}')
                and A.changelog_day>='2020-12-16' 
                and A.changelog_day<=CURDATE() 
                group by a.student_id
                )ta
                group by halfmonth
                order by halfmonth";
        $lostList = $this->DataControl->selectClear($sql);
//var_dump($sql);die;
        $data = array();

        $last_regi_num = 0;
        $last_read_num = 0;
        $last_lost_num = 0;
        $last_decade = '2020-12-16';
        foreach ($regiList as $key => $val) {
            $decade = $val['halfmonth'];
            $read_exists = 0;
            $lost_exists = 0;

            if ($key > 0) {
                $data[$decade . " "][0] = $last_regi_num;
                $data[$decade . " "][1] = $last_read_num;
                $data[$decade . " "][2] = $last_lost_num;
            }

            if ($readList) {
                foreach ($readList as $readOne) {
                    if ($readOne['halfmonth'] == $decade) {
                        $last_read_num = $readOne['read_num'];
                        $read_exists = 1;
                    }
                }
            }

            if ($lostList) {
                foreach ($lostList as $lostOne) {
                    if ($lostOne['halfmonth'] == $decade) {
                        $last_lost_num = $lostOne['lost_num'];
                        if ($last_lost_num > 500) {
                            $last_lost_num -= 500;
                        }
                        $lost_exists = 1;
                    }
                }
            }

            if ($read_exists == 0) {
                $last_read_num = 0;
            }
            if ($lost_exists == 0) {
                $last_lost_num = 0;
            }
            $last_regi_num = $val['regi_num'];
        }

        if ($decade !== $today) {
            $data[$today . " "][0] = $last_regi_num;
            $data[$today . " "][1] = $last_read_num;
            $data[$today . " "][2] = $last_lost_num;
        } else {
            $data['至今'][0] = $last_regi_num;
            $data['至今'][1] = $last_read_num;
            $data['至今'][2] = $last_lost_num;
        }

        if (count($data) < 9) {
            $data['至今'][0] = 0;
            $data['至今'][1] = 0;
            $data['至今'][2] = 0;
        }

        $data=array_slice($data,-9);
        return $data;
    }

    function api2_1($request)
    {
        $datawhere = " A.company_id='{$request['company_id']}' ";
        $today = date("Y-m-d");
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
        }

        //在/待开班级
        $sql = "select count(if(a.class_stdate<=CURDATE(),true,null)) as now_class_num
              ,count(if(a.class_stdate>CURDATE(),true,null)) as will_class_num
              from smc_class A
              left join smc_course C on C.course_id=a.course_id
              where {$datawhere} 
              and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
              and a.class_status>='0' 
              and a.class_type='0' 
              and (a.class_enddate>=CURDATE() or a.class_enddate='') ";
        $classList = $this->DataControl->selectClear($sql);
        if ($classList) {
            $now_class_num = $classList[0]['now_class_num'];
            $will_class_num = $classList[0]['will_class_num'];
        } else {
            $now_class_num = 0;
            $will_class_num = 0;
        }

        //班组待入班
        $sql = "SELECT A.school_id
            ,C.coursecat_id
            ,A.student_id
            FROM smc_student_coursebalance A
            LEFT JOIN smc_course C ON A.course_id=C.course_id AND A.company_id=C.company_id
            WHERE {$datawhere} 
            and A.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND A.coursebalance_time>0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id
            AND Z.coursetype_id=C.coursetype_id AND X.study_isreading=1
            AND X.study_endday>=CURDATE())
            GROUP BY A.school_id,A.student_id,C.coursetype_id";

        $unReadList = $this->DataControl->selectClear($sql);//待入班数量

        if ($unReadList) {
            $unread_num = count($unReadList);
        } else {
            $unread_num = 0;
        }


        //代课老师
        $sql = "SELECT
                    count( IF ( staffer_native <> 1, TRUE, NULL ) ) AS native_emp_num,
                    count( IF ( staffer_native = 1, TRUE, NULL ) ) AS foreign_emp_num 
                FROM
                    (
                SELECT
                    s.staffer_id,
                    s.staffer_native 
                FROM
                    smc_class_hour_teaching AS t
                    LEFT JOIN smc_class_hour AS h ON t.hour_id = h.hour_id
                    LEFT JOIN smc_class AS c ON h.class_id = c.class_id
                    LEFT JOIN smc_staffer AS s ON s.staffer_id = t.staffer_id
                    LEFT JOIN gmc_staffer_postbe p ON p.staffer_id = s.staffer_id 
                    AND p.school_id = c.school_id
                    LEFT JOIN gmc_company_post q ON p.company_id = q.company_id 
                    AND p.post_id = q.post_id
                    LEFT JOIN smc_course AS cs ON c.course_id = cs.course_id 
                    AND c.company_id = cs.company_id 
                WHERE
                    1 
                    AND C.company_id='{$request['company_id']}'
                    AND c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
                    and CS.coursetype_id='{$request['coursetype_id']}'
                    AND h.hour_ischecking >- 1 
                    AND c.class_status >- 2 
                    AND t.staffer_id <> '0' 
                    AND t.teaching_type = '0' 
                    AND s.staffer_leave = '0' 
                    AND c.class_enddate >= CURDATE( ) 
                GROUP BY
                    s.staffer_id,
                    s.staffer_native 
                    ) ta";

        $empList = $this->DataControl->selectClear($sql);//待入班数量

        if ($empList) {
            $native_emp_num = $empList[0]['native_emp_num'];
            $foreign_emp_num = $empList[0]['foreign_emp_num'];
        } else {
            $native_emp_num = 0;
            $foreign_emp_num = 0;
        }

        //开设课程
        $sql = "select c.course_id
              from smc_class A
              left join smc_course C on C.course_id=a.course_id
              where {$datawhere} 
              and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
              and a.class_status>='0' 
              and a.class_type='0' 
              and (a.class_enddate>=CURDATE() or a.class_enddate='') 
              group by c.course_id";
        $courseList = $this->DataControl->selectClear($sql);
        if ($courseList) {
            $course_num = count($courseList);
        } else {
            $course_num = 0;
        }


        $data = array();
        array_push($data, $now_class_num, $will_class_num, $unread_num, $native_emp_num, $foreign_emp_num, $course_num);

        return $data;
    }

    function api2_2($request)
    {
        $datawhere = " A.company_id='{$request['company_id']}' ";
        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
        }

        $sql = "select c.coursecat_branch,sum(a.income_price) as cat_price
            from smc_school_income a
            left join smc_course b on a.course_id=b.course_id
            left join smc_code_coursecat c on b.coursecat_id=c.coursecat_id
            where {$datawhere}
            and a.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.income_confirmtime>=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day)) 
            and a.income_type='0'
            and c.coursecat_branch<>'AE'
            group by b.coursecat_id 
            ORDER BY cat_price";

        $catList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($catList as $key => $catOne) {
            $data[$key]['value'] = $catOne['cat_price'];
            $data[$key]['name'] = $catOne['coursecat_branch'];
        }
        return $data;
    }

    function api2_3($request)
    {
        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}' ";
        }
        if (isset($request['school_ids']) && $request['school_ids'] !== '') {
            $datawhere .= " and a.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        $sql = "select 
            a.school_id,a.school_branch,s.school_shortname,
            count(a.student_id) as total_count, 
            count(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费',true,null)) as renewal_estimate_count 
            from smc_student_course_estimate a,smc_school AS s
            WHERE {$datawhere}				
            AND a.school_id = s.school_id
            AND a.course_isrenew=1
            AND a.class_enddate >= date_add(curdate()-day(curdate())+1,interval 1 month) 
            AND a.class_enddate <= last_day(date_sub(now(),interval -1 month))
            AND a.channel_name=''
            AND a.class_num=a.course_class_num
            group by a.school_id 
            order by ceil(count(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')) or connect_times='续费',true,null))/count(a.student_id)*100) desc,total_count desc
            limit 0,5
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($mainDataArray as $key => $val) {
            $data[$val['school_shortname']] = round($val['renewal_estimate_count'] / $val['total_count'], 2) * 100;
        }

        return $data;
    }

    function api2_4($request)
    {
        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and C.company_id='{$request['company_id']}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and E.coursetype_id='{$request['coursetype_id']}' ";
        }
        if (isset($request['school_ids']) && $request['school_ids'] !== '') {
            $datawhere .= " and C.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        $sql = "SELECT
                A.staffer_id,
                staffer_cnname as staffer_name,
                sum(B.hour_classtimes )/3600 AS total_hours
                FROM smc_class_hour_teaching A
                left join smc_class_hour B on B.hour_id=A.hour_id 
                left join smc_class C on B.class_id=C.class_id 
                left join smc_staffer D on D.staffer_id = A.staffer_id  
                left join smc_course E on C.course_id=E.course_id  
                WHERE {$datawhere}
                AND D.staffer_native=1
                AND B.hour_ischecking >- 1 
                AND C.class_status >- 2 
                AND A.staffer_id not in('24480','0')                
                AND B.hour_day >=date_add(curdate(), interval - day(curdate()) + 1 day)
                AND B.hour_day <=last_day(curdate())
                GROUP BY A.staffer_id 
                order by sum(B.hour_classtimes ) desc
                limit 0,5
        ";//concat(staffer_cnname,(CASE WHEN ifnull(D.staffer_enname,'') ='' THEN '' ELSE concat('-',D.staffer_enname)END))
        $mainDataArray = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($mainDataArray as $key => $val) {
            $data[$key][0] = $val['staffer_name'];
            $data[$key][1] = round($val['total_hours'], 0);
        }

        return $data;
    }

    function api2_5($request)
    {
        $datawhere = " 1  ";
        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and a.company_id='{$request['company_id']}' ";
        }
        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $datawhere .= " and a.coursetype_id='{$request['coursetype_id']}' ";
        }
        if (isset($request['school_ids']) && $request['school_ids'] !== '') {
            $datawhere .= " and a.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        $sql = "select 
            a.school_id,a.school_branch,s.school_shortname,
            count(a.student_id) as total_count, 
            count(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')),true,null)) as renewal_real_count 
            from smc_student_course_estimate a,smc_school AS s
            WHERE {$datawhere}				
            AND a.school_id = s.school_id
            AND a.course_isrenew=1
            AND a.class_enddate >=date_add(curdate()-day(curdate())+1,interval -1 month)
            AND a.class_enddate <=last_day(date_sub(now(),interval 1 month))
            AND a.channel_name=''
            AND a.class_num=a.course_class_num
            group by a.school_id 
            order by ceil(count(if(((renewal_times>=a.coursetype_renewtimes and a.class_enddate<'2022-01-01') or(renewal_rates>=100 and a.class_enddate>='2022-01-01')),true,null))/count(a.student_id)*100) desc,total_count desc
            limit 0,5
        ";
        $mainDataArray = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($mainDataArray as $key => $val) {
            $data[$val['school_shortname']] = round($val['renewal_real_count'] / $val['total_count'], 2) * 100;
        }

        return $data;
    }

    function api2_6($request)
    {
        $datawhere = " 1";

        $today = date("Y-m-d");

        if (isset($request['company_id']) && $request['company_id'] !== '') {
            $datawhere .= " and A.company_id='{$request['company_id']}' ";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and C.coursetype_id='{$request['coursetype_id']}'";
        }

        if (isset($request['school_ids']) && $request['school_ids'] !== '') {
            $datawhere .= " and A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')";
        }

        $sql = "select course_branch ,count(student_id) as read_num
            from (
            SELECT ta.*,(select z.course_branch from smc_student_study x,smc_class y,smc_course z 
            where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=TA.student_id 
            AND x.school_id=ta.school_id and z.coursetype_id=ta.coursetype_id and x.study_beginday<='{$today}' 
            and class_type='0' AND class_status>'-2' order by x.study_endday desc limit 0,1) as course_branch
              from(
              SELECT A.school_id,
                  A.student_id,
                  C.coursetype_id,
                  MIN(A.study_beginday) AS beginday,
                  MAX(CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) AS endday
                  FROM smc_student_study A
                  LEFT JOIN smc_class B ON A.class_id=B.class_id 
                  LEFT JOIN smc_course C ON C.course_id=B.course_id 
                  LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                  WHERE {$datawhere}
                  AND B.class_type='0' 
                  AND B.class_status>'-2'
                  AND (CASE WHEN A.study_endday='' THEN '2029-12-31'ELSE A.study_endday END) >= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                  GROUP BY A.school_id,A.student_id,C.coursetype_id
                  HAVING beginday<='{$today}' and endday>='{$today}'
              )ta
            )tb
            group by tb.course_branch
            order by read_num desc
            limit 0,10";

        $readList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($readList as $key => $val) {
            $data[$key][0] = $val['course_branch'];
            $data[$key][1] = $val['read_num'];
        }

        return $data;
    }

    function api3_1($request)
    {
        $data = array();

        $sql = "select sum(pay_price) as pay_price 
            from smc_payfee_order_pay A,smc_payfee_order B,smc_code_paytype C
            where A.order_pid=B.order_pid
            and a.paytype_code=c.paytype_code
            and B.company_id='{$request['company_id']}'
            and b.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.pay_issuccess=1
            and a.pay_successtime>=UNIX_TIMESTAMP(date_add(curdate(), interval - day(curdate()) + 1 day))
            and a.pay_successtime<UNIX_TIMESTAMP(last_day(curdate()))+86400
            and c.paytype_ischarge=1";

        $payList = $this->DataControl->selectClear($sql);
        if ($payList) {
            $pay_price = $payList[0]['pay_price'];
        } else {
            $pay_price = 0;
        }

        $sql = "select sum(a.income_price) as income_price 
            from smc_school_income a
            where 1
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.income_confirmtime>=UNIX_TIMESTAMP(date_add(curdate(), interval - day(curdate()) + 1 day))
            and a.income_confirmtime<UNIX_TIMESTAMP(last_day(curdate()))+86400";

        $incomeList = $this->DataControl->selectClear($sql);
        if ($incomeList) {
            $income_price = $incomeList[0]['income_price'];
        } else {
            $income_price = 0;
        }

        $sql = "select sum(a.refund_payprice) as refund_price 
            from smc_refund_order a
            where 1
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.refund_status=4
            and a.refund_createtime>=UNIX_TIMESTAMP(date_add(curdate(), interval - day(curdate()) + 1 day))
            and a.refund_createtime<UNIX_TIMESTAMP(last_day(curdate()))+86400";
        $refundList = $this->DataControl->selectClear($sql);
        if ($refundList) {
            $refund_price = $refundList[0]['refund_price'];
        } else {
            $refund_price = 0;
        }

        $sql = "SELECT 
            SUM(a.order_alltimes) AS free_times
            FROM smc_freehour_order a
            WHERE 1
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND a.order_status>=0 
            and a.order_createtime>=UNIX_TIMESTAMP(date_add(curdate(), interval - day(curdate()) + 1 day))
            and a.order_createtime<UNIX_TIMESTAMP(last_day(curdate()))+86400";

        $freeList = $this->DataControl->selectClear($sql);
        if ($freeList) {
            $free_times = $freeList[0]['free_times'];
        } else {
            $free_times = 0;
        }

        $sql = "SELECT sum(a.order_coupon_price) as coupon_price
            FROM smc_payfee_order a, smc_student_coupons b 
            WHERE 1
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND a.order_coupon_price > 0 
            AND a.order_status >= 0
            and a.order_createtime>=UNIX_TIMESTAMP(date_add(curdate(), interval - day(curdate()) + 1 day))
            and a.order_createtime<UNIX_TIMESTAMP(last_day(curdate()))+86400
            AND a.order_pid = b.order_pid";

        $couponList = $this->DataControl->selectClear($sql);
        if ($couponList) {
            $coupon_price = $couponList[0]['coupon_price'];
        } else {
            $coupon_price = 0;
        }

        array_push($data, $pay_price, $income_price, $refund_price, $free_times, $coupon_price);

        return $data;
    }

    function api3_2($request)
    {
        $sql = "select 
             sum(a.income_price) as income_price 
            ,sum(if(a.income_type=0,a.income_price,0)) as income_price_course
            ,sum(if(a.income_type=1,a.income_price,0)) as income_price_fills
            ,sum(if(a.income_type=2,a.income_price,0)) as income_price_books
            ,sum(if(a.income_type=3,a.income_price,0)) as income_price_items
            from smc_school_income a
            where 1
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.income_confirmtime>=UNIX_TIMESTAMP(date_add(curdate(), interval - day(curdate()) + 1 day))
            and a.income_confirmtime<UNIX_TIMESTAMP(last_day(curdate()))+86400";

        $incomeList = $this->DataControl->selectOne($sql);

        $data = array();
        if ($incomeList) {
            $income_price = $incomeList['income_price'];
            $income_price_course = $incomeList['income_price_course'];
            $income_price_books = $incomeList['income_price_books'];
            $income_price_items = $incomeList['income_price_items'];
            $income_price_fills = $incomeList['income_price_fills'];
            $data[0][0] = round($income_price_course / $income_price, 4) * 100;
            $data[0][1] = 100 - $data[0][0];
            $data[0][2] = $income_price_course;
            $data[1][0] = round($income_price_books / $income_price, 4) * 100;
            $data[1][1] = 100 - $data[1][0];
            $data[1][2] = $income_price_books;
            $data[2][0] = round($income_price_items / $income_price, 4) * 100;
            $data[2][1] = 100 - $data[2][0];
            $data[2][2] = $income_price_items;
            $data[3][0] = round($income_price_fills / $income_price, 4) * 100;
            $data[3][1] = 100 - $data[3][0];
            $data[3][2] = $income_price_fills;
        }
        return $data;
    }

    function api3_3($request)
    {
        $today = date("Ymd");
        $sql = "select CONCAT( DATE_FORMAT(ta.income_date, '%Y%m'),(case when dayofmonth( ta.income_date ) < 16 then '01' else '16' end) ) as halfmonth
            ,sum(income_price) as income_price_course
            from(
                SELECT FROM_UNIXTIME(a.income_confirmtime) AS income_date,a.income_price
                FROM smc_school_income A 
                WHERE 1
                and a.company_id='{$request['company_id']}'
                and a.income_type=0
                and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
                and a.income_confirmtime>=UNIX_TIMESTAMP('2020-12-16') 
                and a.income_confirmtime<=unix_timestamp(now()) 
            )ta
            group by halfmonth
            order by halfmonth";

        $incomeList = $this->DataControl->selectClear($sql);

        $sql = "select CONCAT( DATE_FORMAT(ta.pay_date, '%Y%m'),(case when dayofmonth( ta.pay_date ) < 16 then '01' else '16' end) ) as halfmonth
            ,sum(pay_price) as pay_price_course
            from(
            select FROM_UNIXTIME(a.pay_successtime) AS pay_date,a.pay_price
            from smc_payfee_order_pay A,smc_payfee_order B,smc_code_paytype C
            where A.order_pid=B.order_pid
            and a.paytype_code=c.paytype_code
            and B.company_id='{$request['company_id']}'
            and b.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.pay_issuccess=1
            and a.pay_successtime>=UNIX_TIMESTAMP('2020-12-16') 
            and a.pay_successtime<=unix_timestamp(now()) 
            and c.paytype_ischarge=1
            )ta
            group by halfmonth
            order by halfmonth";

        $payList = $this->DataControl->selectClear($sql);

        $sql = "select CONCAT( DATE_FORMAT(ta.refund_date, '%Y%m'),(case when dayofmonth( ta.refund_date ) < 16 then '01' else '16' end) ) as halfmonth
            ,sum(refund_payprice) as refund_payprice
            from(
            select FROM_UNIXTIME(a.refund_createtime) AS refund_date,a.refund_payprice
            from smc_refund_order a
            where 1
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.refund_status=4
            and a.refund_createtime>=UNIX_TIMESTAMP('2020-12-16') 
            and a.refund_createtime<unix_timestamp(now())
            )ta
            group by halfmonth
            order by halfmonth";

        $refundList = $this->DataControl->selectClear($sql);

        $data = array();

        $income_price_course = 0;
        $pay_price_course = 0;
        $refund_payprice = 0;
        $last_decade = '2020-12-16';
        foreach ($incomeList as $key => $val) {
            $decade = $val['halfmonth'];
            $pay_exists = 0;
            $refund_exists = 0;

            if ($key > 0) {
                $data[$decade . " "][0] = $income_price_course;
                $data[$decade . " "][1] = $pay_price_course;
                $data[$decade . " "][2] = $refund_payprice;
            }

            if ($payList) {
                foreach ($payList as $payOne) {
                    if ($payOne['halfmonth'] == $decade) {
                        $pay_price_course = $payOne['pay_price_course'];
                        $pay_exists = 1;
                    }
                }
            }

            if ($refundList) {
                foreach ($refundList as $refundOne) {
                    if ($refundOne['halfmonth'] == $decade) {
                        $refund_payprice = $refundOne['refund_payprice'];
                        $refund_exists = 1;
                    }
                }
            }

            if ($pay_exists == 0) {
                $pay_price_course = 0;
            }
            if ($refund_exists == 0) {
                $refund_payprice = 0;
            }
            $income_price_course = $val['income_price_course'];
        }

        if ($decade !== $today) {
            $data[$today . " "][0] = $income_price_course;
            $data[$today . " "][1] = $pay_price_course;
            $data[$today . " "][2] = $refund_payprice;
        } else {
            $data['至今'][0] = $income_price_course;
            $data['至今'][1] = $pay_price_course;
            $data['至今'][2] = $refund_payprice;
        }

        if (count($data) < 9) {
            $data['至今'][0] = 0;
            $data['至今'][1] = 0;
            $data['至今'][2] = 0;
        }

        $data=array_slice($data,-9);
        return $data;
    }

    function api3_4($request)
    {
        $sql = "select a.school_id,b.school_shortname,sum(a.income_price) as income_sum 
            from smc_school_income a,smc_school b 
            where a.school_id=b.school_id
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.income_confirmtime<= unix_timestamp(now())
            and a.income_confirmtime>= UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            group by a.school_id
            order by income_sum desc
            limit 0,5";

        $incomeList = $this->DataControl->selectClear($sql);
        $data = array();
        foreach ($incomeList as $key => $val) {
            $data[$key][0] = $val['school_shortname'];
            $data[$key][1] = $val['income_sum'];
        }

        return $data;
    }

    function api3_5($request)
    {
        $sql = "
            select b.school_id,D.school_shortname,sum(pay_price) as payprice_sum
            from smc_payfee_order_pay A,smc_payfee_order B,smc_code_paytype C,smc_school D 
            where A.order_pid=B.order_pid
            and a.paytype_code=c.paytype_code
            and b.school_id=d.school_id
            and B.company_id='{$request['company_id']}'
            and b.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.pay_issuccess=1
            and a.pay_successtime<= unix_timestamp(now())
            and a.pay_successtime>= UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            and c.paytype_ischarge=1
            group by B.school_id
            order by payprice_sum desc
            limit 0,5";

        $paypriceList = $this->DataControl->selectClear($sql);
        $data = array();
        foreach ($paypriceList as $key => $val) {
            $data[$key][0] = $val['school_shortname'];
            $data[$key][1] = $val['payprice_sum'];
        }

        return $data;
    }

    function api3_6($request)
    {
        $sql = "select a.school_id,b.school_shortname,sum(a.refund_payprice) as refund_price 
            from smc_refund_order a,smc_school b 
            where a.school_id=b.school_id
            and a.company_id='{$request['company_id']}'
            and a.school_id in(SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and a.refund_status=4
            and a.refund_createtime<= unix_timestamp(now())
            and a.refund_createtime>= UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            group by B.school_id
            order by refund_price desc
            limit 0,5";

        $paypriceList = $this->DataControl->selectClear($sql);
        $data = array();
        foreach ($paypriceList as $key => $val) {
            $data[$key][0] = $val['school_shortname'];
            $data[$key][1] = $val['refund_price'];
        }

        return $data;
    }

    function api4_1($request)
    {

        $sql = "select count(A.client_id) AS online_channel_num,count(if(a.client_tracestatus <> '-2',true,null)) as online_channel_num_valid
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
        WHERE 1
        and C.channel_way=0
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())";

        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $online_channel_num = $clientList[0]['online_channel_num'];
            $online_channel_num_valid = $clientList[0]['online_channel_num_valid'];
        } else {
            $online_channel_num = 0;
            $online_channel_num_valid = 0;
        }

        $sql = "select COUNT(A.student_id) as regi_num
        FROM smc_student_registerinfo A
        left join smc_student B ON A.student_id = B.student_id 
        left join crm_client C ON B.from_client_id=C.client_id
        left join crm_code_channel D ON C.channel_id=D.channel_id
        where 1
        and A.info_status=1
        and D.channel_way=0
        and A.company_id ='{$request['company_id']}'
        and A.coursetype_id ='{$request['coursetype_id']}'
        and A.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.pay_successtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.pay_successtime <=UNIX_TIMESTAMP(now())";

        $regiList = $this->DataControl->selectClear($sql);
        if ($regiList) {
            $regi_num = $regiList[0]['regi_num'];
        } else {
            $regi_num = 0;
        }

        $data = array();
        array_push($data, $online_channel_num, $online_channel_num_valid, $regi_num);

        return $data;
    }

    function api4_2($request)
    {
        $sql = "select c.channel_name,count(A.client_id) AS online_channel_num
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
        WHERE 1
        and C.channel_way=0
        and a.client_tracestatus <> '-2'
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())
        group by c.channel_id 
        order by online_channel_num desc 
        limit 0,5";

        $clientList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($clientList as $key => $channelOne) {
            $data[$key]['value'] = $channelOne['online_channel_num'];
            $data[$key]['name'] = $channelOne['channel_name'];
        }
        return $data;
    }

    function api4_3($request)
    {
        $sql = "select c.channel_name,count(A.client_id) AS online_channel_num
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
        WHERE 1
        and C.channel_way=0
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())
        group by c.channel_id 
        order by online_channel_num desc 
        limit 0,5";

        $clientList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($clientList as $key => $channelOne) {
            $data[$key]['value'] = $channelOne['online_channel_num'];
            $data[$key]['name'] = $channelOne['channel_name'];
        }
        return $data;
    }

    function api4_4($request)
    {
        $sql = "select D.school_shortname,COUNT(A.client_id) AS school_num
            FROM crm_client A
            LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
            left join crm_code_channel C ON A.channel_id=C.channel_id
            left join smc_school D ON B.school_id=D.school_id
            WHERE 1
            and C.channel_way=0
            and a.client_tracestatus <> '-2'
            and A.company_id ='{$request['company_id']}'
            and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            and A.client_createtime <=unix_timestamp(now())
            GROUP BY B.school_id
            ORDER BY school_num DESC ";

        $clientList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($clientList as $key => $val) {
            $data[$val['school_shortname']] = $val['school_num'];
        }

        return $data;
    }

    function api4_5($request)
    {
        $today = date("Ymd");
        $sql = "select CONCAT( DATE_FORMAT(ta.client_date, '%Y%m'),(case when dayofmonth( ta.client_date ) < 16 then '01' else '16' end) ) as halfmonth
            ,count(if(channel_way=0,true,null)) as client_num
            ,count(if(channel_way=0 and ta.client_tracestatus <> '-2',true,null)) as client_num_valid
            from (
            select A.client_id,A.client_tracestatus,FROM_UNIXTIME(A.client_createtime,'%Y%m%d') as client_date,C.channel_way
            FROM crm_client A
            LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
            left join crm_code_channel C ON A.channel_id=C.channel_id
            WHERE 1
            and A.company_id ='{$request['company_id']}'
            and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and A.client_createtime >=UNIX_TIMESTAMP('2020-12-16')
            and A.client_createtime <=unix_timestamp(now())
            )ta
            group by halfmonth
            order by halfmonth";

        $clientList = $this->DataControl->selectClear($sql);

        $sql = "select CONCAT( DATE_FORMAT(ta.client_date, '%Y%m'),(case when dayofmonth( ta.client_date ) < 16 then '01' else '16' end) ) as halfmonth
        ,count(student_id) as regi_num
        from (
        select A.student_id,FROM_UNIXTIME(A.pay_successtime,'%Y%m%d') as client_date
        FROM smc_student_registerinfo A
        left join smc_student B ON A.student_id = B.student_id 
        left join crm_client C ON B.from_client_id=C.client_id
        left join crm_code_channel D ON C.channel_id=D.channel_id
        where 1
        and A.info_status=1
        and D.channel_way=0
        and A.company_id ='{$request['company_id']}'
        and A.coursetype_id ='{$request['coursetype_id']}'
        and A.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.pay_successtime >=UNIX_TIMESTAMP('2020-12-16')
        and A.pay_successtime <=UNIX_TIMESTAMP(now())
        )ta
        group by halfmonth
        order by halfmonth";

        $regiList = $this->DataControl->selectClear($sql);

        $data = array();

        $client_num = 0;
        $valid_num = 0;
        $regi_num = 0;
        foreach ($clientList as $key => $val) {
            $decade = $val['halfmonth'];
            $regi_exists = 0;

            if ($key > 0) {
                $data[$decade . " "][0] = $client_num;
                $data[$decade . " "][1] = $valid_num;
                $data[$decade . " "][2] = $regi_num;
            }

            if ($regiList) {
                foreach ($regiList as $regiOne) {
                    if ($regiOne['halfmonth'] == $decade) {
                        $regi_num = $regiOne['regi_num'];
                        $regi_exists = 1;
                    }
                }
            }

            if ($regi_exists == 0) {
                $regi_num = 0;
            }
            $client_num = $val['client_num'];
            $valid_num = $val['client_num_valid'];
        }

        if ($decade !== $today) {
            $data[$today . " "][0] = $client_num;
            $data[$today . " "][1] = $valid_num;
            $data[$today . " "][2] = $regi_num;
        } else {
            $data['至今'][0] = $client_num;
            $data['至今'][1] = $valid_num;
            $data['至今'][2] = $regi_num;
        }

        if (count($data) < 9) {
            $data['至今'][0] = 0;
            $data['至今'][1] = 0;
            $data['至今'][2] = 0;
        }

        $data=array_slice($data,-9);
        return $data;
    }

    function api5_1($request)
    {
        $sql = "select c.channel_name,count(A.client_id) AS offline_channel_num
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
        WHERE 1
        and C.channel_way=1
        and A.client_source<>'转介绍'
        and a.client_tracestatus <> '-2'
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())
        group by c.channel_id 
        order by offline_channel_num desc 
        limit 0,5";

        $channelList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($channelList as $key => $channelOne) {
            $data[$key]['value'] = $channelOne['offline_channel_num'];
            $data[$key]['name'] = $channelOne['channel_name'];
        }
        return $data;
    }

    function api5_2($request)
    {
        $sql = "select * from (select c.channel_name
        ,count(A.client_id) AS offline_channel_num
        ,count(IF(a.client_tracestatus <> '-2',TRUE,NULL)) AS offline_channel_num_valid
        ,count(IF(a.client_tracestatus <> '-2' and e.student_id is not null,TRUE,NULL)) AS offline_channel_num_postive
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
				LEFT JOIN smc_student D ON A.client_id=D.from_client_id
				LEFT JOIN smc_student_registerinfo E ON D.student_id=E.student_id AND B.school_id=E.school_id AND E.info_status=1 AND E.coursetype_id='{$request['coursetype_id']}'
        WHERE 1
        and C.channel_way=1
        and A.client_source<>'转介绍'
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())
        group by c.channel_id
        )ta 
        order by (case when offline_channel_num_valid=0 then 0 else offline_channel_num_postive/offline_channel_num_valid end) desc 
        limit 0,5";

        $channelList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($channelList as $key => $val) {
            $data[$val['channel_name']][0] = $val['offline_channel_num'] > 0 ? (round($val['offline_channel_num_valid'] / $val['offline_channel_num'], 2) * 100) : 0;
            $data[$val['channel_name']][1] = $val['offline_channel_num_valid'] > 0 ? (round($val['offline_channel_num_postive'] / $val['offline_channel_num_valid'], 2) * 100) : 0;
        }
        return $data;
    }

    function api5_3($request)
    {
        $sql = "select count(A.client_id) AS offline_channel_num
        ,count(if(a.client_tracestatus <> '-2',true,null)) as offline_channel_num_valid
        ,count(if(a.client_source='转介绍',true,null)) as offline_channel_num_trans
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
        WHERE 1
        and C.channel_way=1
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())";

        $clientList = $this->DataControl->selectClear($sql);
        if ($clientList) {
            $offline_channel_num = $clientList[0]['offline_channel_num'];
            $offline_channel_num_valid = $clientList[0]['offline_channel_num_valid'];
            $offline_channel_num_trans = $clientList[0]['offline_channel_num_trans'];
        } else {
            $offline_channel_num = 0;
            $offline_channel_num_valid = 0;
            $offline_channel_num_trans = 0;
        }

        $sql = "select COUNT(A.student_id) as regi_num,count(if(c.client_source='转介绍',true,null)) as regi_num_trans
        FROM smc_student_registerinfo A
        left join smc_student B ON A.student_id = B.student_id 
        left join crm_client C ON B.from_client_id=C.client_id
        left join crm_code_channel D ON C.channel_id=D.channel_id
        where 1
        and A.info_status=1
        and D.channel_way=1
        and A.company_id ='{$request['company_id']}'
        and A.coursetype_id ='{$request['coursetype_id']}'
        and A.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.pay_successtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.pay_successtime <=UNIX_TIMESTAMP(now())";

        $regiList = $this->DataControl->selectClear($sql);
        if ($regiList) {
            $regi_num = $regiList[0]['regi_num'];
            $regi_num_trans = $regiList[0]['regi_num_trans'];
        } else {
            $regi_num = 0;
            $regi_num_trans = 0;
        }

        $data = array();
        array_push($data, $offline_channel_num, $offline_channel_num_valid, $regi_num, $offline_channel_num_trans, $regi_num_trans);

        return $data;
    }

    function api5_4($request)
    {

        $sql = "select c.channel_name
        ,count(A.client_id) AS offline_channel_num
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
				LEFT JOIN smc_student D ON A.client_id=D.from_client_id
				LEFT JOIN smc_student_registerinfo E ON D.student_id=E.student_id AND B.school_id=E.school_id AND E.info_status=1 AND E.coursetype_id=65
        WHERE 1
        and C.channel_way=1
        and a.client_source='转介绍'
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())
        group by c.channel_id ";

        $channelList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($channelList as $key => $channelOne) {
            $data[$key]['value'] = $channelOne['offline_channel_num'];
            $data[$key]['name'] = $channelOne['channel_name'];
        }
        return $data;
    }

    function api5_5($request)
    {
        $sql = "select E.school_shortname,COUNT(A.student_id) as regi_num
        FROM smc_student_registerinfo A
        left join smc_student B ON A.student_id = B.student_id 
        left join crm_client C ON B.from_client_id=C.client_id
        left join crm_code_channel D ON C.channel_id=D.channel_id
		left join smc_school E on E.school_id=A.school_id
        where 1
        and A.info_status=1
        and D.channel_way=1
		and C.client_source='转介绍'
        and A.company_id ='{$request['company_id']}'
        and A.coursetype_id ='{$request['coursetype_id']}'
        and A.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.pay_successtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.pay_successtime <=UNIX_TIMESTAMP(now())
		GROUP BY A.school_id
		ORDER BY regi_num DESC
		LIMIT 0,5 ";

        $schoolList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($schoolList as $key => $schoolOne) {
            $data[$schoolOne['school_shortname']] = $schoolOne['regi_num'];
        }
        return $data;
    }

    function api5_6($request)
    {
        $sql = "select FROM_UNIXTIME(A.client_createtime,'%m') as calc_month,count(A.client_id) AS offline_channel_num
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
        left join crm_code_channel C ON A.channel_id=C.channel_id
        WHERE 1
        and C.channel_way=1
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(date_add(curdate()-day(curdate())+1,interval -11 month))
        and A.client_createtime <=unix_timestamp(now())
        group by FROM_UNIXTIME(A.client_createtime,'%Y-%m')
        order by FROM_UNIXTIME(A.client_createtime,'%Y-%m')";

        $monthList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($monthList as $key => $monthOne) {
            $data[$monthOne['calc_month'] . '月'] = $monthOne['offline_channel_num'];
        }
        return $data;
    }

    function api5_7($request)
    {
        $sql = "
        select staffer_cnname,count(ta.client_stubranch) as trans_num
        from(
            select  
             a.client_stubranch
            ,d.staffer_cnname
            ,d.staffer_branch
            FROM crm_client A
            LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id 
            left join crm_code_channel C ON A.channel_id=C.channel_id
            left join view_smc_student_teach D on A.client_stubranch=D.student_branch AND D.coursetype_id='{$request['coursetype_id']}'
                AND D.study_beginday<=FROM_UNIXTIME(A.client_createtime) AND D.study_endday >= FROM_UNIXTIME(A.client_createtime)
            WHERE 1
            and C.channel_way=1
            and a.client_source='转介绍'
            and A.company_id ='{$request['company_id']}'
            and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND A.client_id IN (SELECT client_id FROM crm_client_intention WHERE coursetype_id = '{$request['coursetype_id']}')
            and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            and A.client_createtime <=unix_timestamp(now())
            GROUP BY D.student_branch,d.class_branch
        )ta where ta.staffer_branch is not null
        GROUP BY staffer_branch
        order by trans_num desc
        LIMIT 0,5 ";
//        AND A.client_id IN (SELECT client_id FROM crm_client_intention WHERE coursetype_id = '{$request['coursetype_id']}')

        $empList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($empList as $key => $empOne) {
            $data[$empOne['staffer_cnname']] = $empOne['trans_num'];
        }
        return $data;
    }

    function api6_1($request)
    {
        $sql = "select f.staffer_cnname,COUNT(A.student_id) as regi_num,SUM(A.pay_price) as pay_price
            FROM smc_student_registerinfo A
						left join smc_student B ON A.student_id=B.student_id
						left join crm_client C on b.from_client_id=c.client_id
						left join crm_client_conversionlog d on c.client_id=d.client_id and a.school_id=d.school_id and d.conversionlog_ismajor=1
						left join crm_marketer e on d.marketer_id=e.marketer_id
						left join smc_staffer f on e.staffer_id=f.staffer_id
            where 1
            and A.info_status=1
            and ifnull(f.staffer_id,'0')<>'0'
            and A.company_id ='8888'
            and A.coursetype_id ='65'
            and A.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and A.pay_successtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            and A.pay_successtime <=UNIX_TIMESTAMP(now())
            GROUP BY f.staffer_id
            ORDER BY pay_price DESC
            limit 0,5";

        $stafferList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($stafferList as $key => $val) {
            $data[$val['staffer_cnname']] = $val['pay_price'];
        }

        return $data;
    }

    function api6_2($request)
    {
        $sql = "
            select ttc.school_shortname
            ,count(tta.client_id) as all_num
            ,count(if(tta.is_coming=1,true,null)) as come_num
            ,count(if(ttb.conversionlog_id is not null,true,null)) as regi_num
            from (
            select  school_id,client_id,max(is_come) AS is_coming
            from 
            (
            SELECT  school_id,client_id,audition_isvisit as is_come
            FROM crm_client_audition X 
            WHERE 1
            AND X.company_id ='{$request['company_id']}'
            AND X.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND X.audition_visittime>= DATE_SUB(CURDATE(),INTERVAL 90 day)
            AND X.audition_visittime<=CURDATE()
            union all
            SELECT school_id,client_id,invite_isvisit as is_come
            FROM crm_client_invite X 
            WHERE 1
            AND X.company_id ='{$request['company_id']}'
            AND X.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND X.invite_visittime>= DATE_SUB(CURDATE(),INTERVAL 90 day)
            AND X.invite_visittime<=CURDATE()
            )ta group by school_id,client_id
            )tta 
            left join crm_client_conversionlog ttb on tta.client_id=ttb.client_id and tta.school_id=ttb.school_id and ttb.conversionlog_ismajor=1
            left join smc_school ttc on tta.school_id=ttc.school_id
            group by ttc.school_id
            order by (count(if(tta.is_coming=1,true,null))/count(tta.client_id)) desc
            limit 0,5";

        $mainDataArray = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($mainDataArray as $key => $val) {
            $data[$val['school_shortname']][0] = $val['all_num'] > 0 ? (round($val['come_num'] / $val['all_num'], 2) * 100) : 0;
            $data[$val['school_shortname']][1] = $val['come_num'] > 0 ? (round($val['regi_num'] / $val['come_num'], 2) * 100) : 0;
        }

        return $data;
    }

    function api6_3($request)
    {
        $sql = "select B.school_shortname,COUNT(A.student_id) as regi_num,SUM(A.pay_price)/COUNT(A.student_id) as pay_price
            FROM smc_student_registerinfo A
            left join smc_school B on B.school_id=A.school_id
            where 1
            and A.info_status=1
            and A.company_id ='{$request['company_id']}'
            and A.coursetype_id ='{$request['coursetype_id']}'
            and A.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            and A.pay_successtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
            and A.pay_successtime <=UNIX_TIMESTAMP(now())
            GROUP BY A.school_id
            ORDER BY regi_num DESC";

        $schoolList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($schoolList as $key => $schoolOne) {
//            $data[$key][0] = $key + 1;
            $data[$key][0] = $schoolOne['school_shortname'];
            $data[$key][1] = $schoolOne['regi_num'];
            $data[$key][2] = round($schoolOne['pay_price'], 0);
        }
        return $data;
    }

    function api6_4($request)
    {
        $sql = "select 
        count(A.client_id) AS all_num
        ,count(if(tta.client_id is not null,true,null)) as invite_num
        ,count(if(tta.client_id is not null and is_coming=1,true,null)) as come_num
        ,count(if(ttb.client_id is not null,true,null)) as regi_num
        FROM crm_client A
        LEFT JOIN crm_client_schoolenter B ON A.client_id = B.client_id
        LEFT JOIN (
            select  school_id,client_id,max(is_come) AS is_coming
            from 
            (
            SELECT  school_id,client_id,audition_isvisit as is_come
            FROM crm_client_audition X 
            WHERE 1
            AND X.company_id ='{$request['company_id']}'
            AND X.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND X.audition_visittime>= DATE_SUB(CURDATE(),INTERVAL 90 day)
            AND X.audition_visittime<=CURDATE()
            union all
            SELECT school_id,client_id,invite_isvisit as is_come
            FROM crm_client_invite X 
            WHERE 1
            AND X.company_id ='{$request['company_id']}'
            AND X.school_id in (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            AND X.invite_visittime>= DATE_SUB(CURDATE(),INTERVAL 90 day)
            AND X.invite_visittime<=CURDATE()
            )ta group by client_id
        )TTA ON A.client_id = TTA.client_id and tta.school_id=B.school_id
        left join crm_client_conversionlog ttb on A.client_id=ttb.client_id and B.school_id=ttb.school_id and ttb.conversionlog_ismajor=1
        WHERE 1
        AND A.client_tracestatus <> '-2'
        and A.company_id ='{$request['company_id']}'
        and B.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.client_createtime >=UNIX_TIMESTAMP(DATE_SUB(CURDATE(),INTERVAL 90 day))
        and A.client_createtime <=unix_timestamp(now())
        ";

        $clientList = $this->DataControl->selectClear($sql);

        if ($clientList) {
            $all_num = $clientList[0]['all_num'];
            $invite_num = $clientList[0]['invite_num'];
            $come_num = $clientList[0]['come_num'];
            $regi_num = $clientList[0]['regi_num'];
        } else {
            $all_num = 0;
            $invite_num = 0;
            $come_num = 0;
            $regi_num = 0;
        }

        $data = array();

        array_push($data, $all_num, $invite_num, $come_num, $regi_num);

        return $data;
    }

    function api6_5($request)
    {
        $sql = "select a.activity_name
            ,count(b.client_id) as client_num 
            from crm_sell_activity a
            left join crm_client b ON a.activity_id=b.activity_id
            left join crm_client_schoolenter c ON b.client_id=c.client_id
            where 1
            and a.company_id='{$request['company_id']}'
            and c.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
            group by a.activity_id 
            order by client_num desc 
            limit 0,5";

        $regList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($regList as $key => $val) {
            $data[$val['activity_name']] = $val['client_num'];
        }

        return $data;
    }

    function api6_6($request)
    {
        $sql = "select FROM_UNIXTIME(A.track_createtime,'%m') as calc_month,count(A.track_id) AS track_num
        FROM crm_client_track A
        WHERE 1
				and a.track_isactive=1
        and A.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$request['organize_id']}')
        and A.track_createtime >=UNIX_TIMESTAMP(date_add(curdate()-day(curdate())+1,interval -11 month))
        and A.track_createtime <=unix_timestamp(now())
        group by FROM_UNIXTIME(A.track_createtime,'%Y-%m')
        order by FROM_UNIXTIME(A.track_createtime,'%Y-%m')";

        $monthList = $this->DataControl->selectClear($sql);

        $data = array();
        foreach ($monthList as $key => $monthOne) {
            $data[$monthOne['calc_month'] . '月'] = $monthOne['track_num'];
        }
        return $data;
    }
}