<?php

namespace Model\Api;

class CmbDataprocessModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $companies_id = 0;//操作公司
    public $starttime = '';


    function __construct($agencyId)
    {
        parent::__construct();

        if ($agencyId && $agencyId != '') {
            $this->agencyId = $agencyId;
            $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_id,company_id,companies_signature,companies_secretkey,companies_supervisetime", "companies_agencyid='{$this->agencyId}'");
            if ($companiesOne) {
                $this->company_id = $companiesOne['company_id'];
                $this->companies_id = $companiesOne['companies_id'];
                $this->starttime = $companiesOne['companies_supervisetime'];
                //$this->aeskey = $companiesOne['companies_secretkey'];
                //$this->secretkey = $companiesOne['companies_signature'];

                $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_cmbbranch,company_signature,company_secretkey", "company_id='{$companiesOne['company_id']}'");
                if ($companyOne && $companyOne['company_cmbbranch'] != '') {
                    $this->aeskey = $companyOne['company_secretkey'];
                    $this->secretkey = $companyOne['company_signature'];
                    $this->cmbbranch = $companyOne['company_cmbbranch'];
                }
            } else {
                $this->error = 1;
                $this->errortip = "请选择请看是否设置机构代码";
                return false;

                /*$companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_cmbbranch,company_signature,company_secretkey", "company_cmbbranch='{$agencyId}'");

                $this->starttime = '1645027200';

                if ($companyOne && $companyOne['company_cmbbranch'] != '') {
                    $this->company_id = $companyOne['company_id'];
                    $this->aeskey = $companyOne['company_secretkey'];
                    $this->secretkey = $companyOne['company_signature'];
                    $this->cmbbranch = $companyOne['company_cmbbranch'];
                }else{
                }*/
            }
        } else {
            $this->error = 1;
            $this->errortip = "请选择机构";
            return false;
        }
    }


    /**
     * 监管账户学生总控表
     * 需要加上充值
     **/
    function recordCmbOrderCharge(){
        $sql = "select a.companies_id,a.school_id,a.student_id,b.course_id,c.pay_price,a.order_pid,c.pay_pid,c.pay_successtime 
                from smc_payfee_order as a
                left join smc_payfee_order_course as b on a.order_pid=b.order_pid
                left join smc_payfee_order_pay as c on a.order_pid=c.order_pid
                left join smc_code_paytype as d on c.paytype_code=d.paytype_code
                left join gmc_code_companies as e on a.companies_id=e.companies_id
                where a.order_status = 4 and e.companies_issupervise = 1 and e.companies_supervisetime<=c.pay_successtime and a.company_id='8888' and d.paytype_ischarge=1 and c.pay_issuccess=1 and d.paytype_code<>'cash'
                AND NOT EXISTS ( SELECT 1 FROM cmb_trans_charge AS x WHERE x.pay_pid = c.pay_pid limit 0,1) 
                order by c.pay_successtime ASC limit 0,100";
        $payList=$this->DataControl->selectClear($sql);

        $num=0;
        if($payList){
            foreach($payList as $payOne){
                $data=array();
                $data['companies_id']=$payOne['companies_id'];
                $data['school_id']=$payOne['school_id'];
                $data['student_id']=$payOne['student_id'];
                $data['charge_type']=0;
                $data['course_id']=$payOne['course_id'];
                $data['pay_price']=$payOne['pay_price'];
                $data['order_pid']=$payOne['order_pid'];
                $data['pay_pid']=$payOne['pay_pid'];
                $data['pay_successtime']=$payOne['pay_successtime'];
                $this->DataControl->insertData("cmb_trans_charge",$data);
                $num++;
            }
        }

        $sql = "select  a.companies_id,a.school_id,a.student_id,a.refund_pid,a.refund_payprice,a.refund_createtime 
                from smc_refund_order as a,gmc_code_companies as b 
                where a.companies_id=b.companies_id and a.refund_createtime>=b.companies_supervisetime and b.companies_issupervise = 1
                and a.refund_status=4 and a.company_id='8888'
                and EXISTS(select 1 from cmb_trans_charge as x where x.companies_id=a.companies_id and x.student_id=a.student_id and x.school_id=a.school_id and x.charge_type=0 limit 0,1)  
                and not exists(SELECT 1 FROM cmb_trans_charge AS x WHERE x.order_pid = a.refund_pid limit 0,1)
                order by a.refund_createtime ASC limit 0,100";
        $refundList=$this->DataControl->selectClear($sql);

        if($refundList){
            foreach($refundList as $refundOne){
                $data=array();
                $data['companies_id']=$refundOne['companies_id'];
                $data['school_id']=$refundOne['school_id'];
                $data['student_id']=$refundOne['student_id'];
                $data['charge_type']=1;
                $data['pay_price']=$refundOne['refund_payprice'];
                $data['order_pid']=$refundOne['refund_pid'];
                $data['is_refundpay']=0;
                $data['pay_successtime']=$refundOne['refund_createtime'];
                $this->DataControl->insertData("cmb_trans_charge",$data);
                $num++;

            }
        }
        return $num;
    }


    //批量修改学生账户监管状态
    function autoOpen(){

        $sql = "update 
                smc_student_coursebalance a
                LEFT JOIN gmc_code_companies b ON a.companies_id = b.companies_id
                LEFT JOIN smc_course c ON a.course_id = c.course_id
                LEFT JOIN (
                SELECT
                    y.companies_id,
                    y.school_id,
                    y.student_id,
                    o.coursecat_id,
                    min( x.pay_successtime ) AS supervisetime 
                FROM
                    smc_payfee_order_pay x
                    LEFT JOIN smc_payfee_order y ON x.order_pid = y.order_pid
                    LEFT JOIN smc_payfee_order_course z ON z.order_pid = y.order_pid
                    LEFT JOIN smc_course o ON o.course_id = z.course_id
                    LEFT JOIN smc_code_paytype p ON p.paytype_code = x.paytype_code
                    LEFT JOIN gmc_code_companies q ON y.companies_id = q.companies_id 
                WHERE
                    x.pay_issuccess = 1 
                    AND x.pay_successtime >= q.companies_supervisetime 
                    AND p.paytype_ischarge = 1 
                    AND y.companies_id = '{$this->companies_id}'
                    AND y.order_type = 0 
                    AND o.course_issupervise = 1 
                GROUP BY
                    y.companies_id,
                    y.school_id,
                    y.student_id,
                    o.coursecat_id 
                ) ta ON ta.school_id = a.school_id 
                AND ta.student_id = a.student_id 
                AND ta.coursecat_id = c.coursecat_id 
                AND ta.companies_id = b.companies_id 
                set a.coursebalance_issupervise=1,coursebalance_updatatime=UNIX_TIMESTAMP(now())
            WHERE
                a.companies_id = '{$this->companies_id}'
                AND c.course_issupervise = 1 
                AND a.coursebalance_issupervise = 0 
                AND a.coursebalance_createtime >= b.companies_supervisetime 
                AND a.coursebalance_createtime > ta.supervisetime 
                AND EXISTS (
                SELECT
                    1 
                FROM
                    smc_student_hourstudy AS h,
                    smc_class AS cl 
                WHERE
                    h.class_id = cl.class_id 
                    AND h.student_id = a.student_id 
                AND cl.school_id = a.school_id 
                AND cl.course_id = a.course_id)
             ";
        $num=$this->DataControl->selectClear($sql);

        return $num?count($num):0;

    }


    function autoConfigOrderItem($orderOne){

        $sql = "select ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y,smc_payfee_order as z where x.paytype_code=y.paytype_code and x.order_pid=z.order_pid and z.student_id='{$orderOne['student_id']}' and z.companies_id=a.companies_id and y.paytype_ischarge=1 
		AND y.paytype_code <> 'cash' and x.pay_issuccess=1 and x.pay_successtime>=a.companies_supervisetime),0) as all_amt
                    ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.student_id='{$orderOne['student_id']}' and x.companies_id=a.companies_id and x.is_confirm=1 and x.order_type='P'),0) as orderPrice
                    ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.student_id='{$orderOne['student_id']}' and x.companies_id=a.companies_id and x.is_confirm=1 and x.order_type='R'),0) as refundPrice
                    from gmc_code_companies as a 
                    where a.companies_id='{$orderOne['companies_id']}'";
        $infoOne=$this->DataControl->selectOne($sql);
        if($infoOne){
            if($infoOne['all_amt'] > 0){
                //已确认监管
                $configOkPrice = $infoOne['orderPrice']-$infoOne['refundPrice'];
                if($infoOne['all_amt'] > $configOkPrice){
                    //剩余监管余额
                    $shengyuOkPrice = $infoOne['all_amt']-$configOkPrice;
                    if( $shengyuOkPrice >= $orderOne['order_amt']){
                        $data=array();
                        $data['is_confirm'] = 1;
                        $this->DataControl->updateData("cmb_trans_order","order_id='{$orderOne['order_id']}'",$data);
                    }else{
                        $data=array();
                        $data['is_confirm'] = 6;//先临时放一个地方
                        $this->DataControl->updateData("cmb_trans_order","order_id='{$orderOne['order_id']}'",$data);


                        /*if($shengyuOkPrice > 0){
                            $data=array();
                            $data['is_confirm'] = 1;
                            $data['order_amt'] = $shengyuOkPrice;
                            //$this->DataControl->updateData("cmb_trans_order","order_id='{$orderOne['order_id']}'",$data);
                        }*/
                    }
                }else{
                    $data=array();
                    $data['is_confirm'] = 8;//先临时放一个地方
                    $this->DataControl->updateData("cmb_trans_order","order_id='{$orderOne['order_id']}'",$data);
                }
            }else{
                $data=array();
                $data['is_confirm'] = 7;//先临时放一个地方
                $this->DataControl->updateData("cmb_trans_order","order_id='{$orderOne['order_id']}'",$data);

                /*$data=array();
                $data['is_confirm'] = -2;//监管金额小于0
                //$this->DataControl->updateData("cmb_trans_order","order_id='{$orderOne['order_id']}'",$data);*/
            }
        }

        return true;
    }

    function autoConfigOrder($orderOne=array()){
        $changeOne = $this->DataControl->getFieldOne("cmb_trans_order_change","trading_pid","order_pid = '{$orderOne['order_pid']}'");
        //定义：原订单入班的班级ID为A,现在在B班耗课，错误的把A班订单作为B班下单传过来
        $buylogOne = $this->DataControl->getFieldOne("smc_student_coursebalance_log","log_id,student_id,school_id,course_id,class_id,log_finalamount","student_id = '{$orderOne['student_id']}' AND trading_pid = '{$changeOne['trading_pid']}' AND log_playclass = '+'");
        //取学生在此订单购课后A班级是否耗课，如果已耗课打印出来，找之前代码错误
        if(!$this->DataControl->getFieldOne("smc_student_coursebalance_log","log_id","class_id = '{$orderOne['yuan_class_id']}' AND student_id = '{$orderOne['student_id']}' AND hourstudy_id > 0 AND log_playamount > 0 AND log_id > '{$buylogOne['log_id']}'")){
            $sql = "SELECT c.trading_pid
                    FROM smc_student_coursebalance_log AS c
                    WHERE c.student_id = '{$orderOne['student_id']}'
                    AND c.course_id = '{$buylogOne['course_id']}'
                    AND c.school_id = '{$buylogOne['school_id']}'
                    AND c.log_id > '{$buylogOne['log_id']}'
                    AND c.hourstudy_id = '0' 
                    AND c.trading_pid <> '' 
                    AND c.log_playclass = '-' and c.log_playamount='{$buylogOne['log_finalamount']}'
                    ORDER BY c.log_id ASC 
                    LIMIT 0,1";
            $forwardOne=$this->DataControl->selectOne($sql);
            if($forwardOne){
                //取结转订单生成到order
                $sql = "select b.order_pid,b.is_confirm,b.class_id,a.trading_pid,b.order_status_all 
                        from cmb_trans_order_change as a left join cmb_trans_order as b on a.order_pid = b.order_pid
                                              where a.trading_pid = '{$forwardOne['trading_pid']}'";
                $changeOrderOne=$this->DataControl->selectOne($sql);
                if($changeOrderOne && $changeOrderOne['order_pid'] && ($changeOrderOne['is_confirm'] <> 1 || $changeOrderOne['class_id'] <> $orderOne['class_id'])){
                    //存在结转订单或者结转订单的班级id不一致那么就更新
                    $data=array();
                    $data['class_id'] = $orderOne['class_id'];
                    $data['is_confirm'] = 1;
                    $data['is_errorpaly'] = 1;
                    $this->DataControl->updateData("cmb_trans_order","order_pid='{$changeOrderOne['order_pid']}'",$data);

                    $data=array();
                    $data['is_errorpaly'] = 1;
                    $this->DataControl->updateData("cmb_trans_order","order_pid='{$orderOne['order_pid']}'",$data);

                    $data=array();
                    $data['change_status']=1;
                    $this->DataControl->updateData("cmb_trans_order_change","trading_pid='{$changeOrderOne['trading_pid']}' and course_id='{$orderOne['course_id']}'",$data);
                    return true;
                }elseif($changeOrderOne && $changeOrderOne['trading_pid'] && $changeOrderOne['order_pid'] == null){
                    $sql = "SELECT l.trading_pid,l.log_time,l.log_playclass
                                ,b.dealorder_pid as order_pid
                                ,b.dealorder_status
                                ,l.log_playtimes as order_num
                                ,l.log_playamount as order_amt
                                ,0 as order_fee
                                ,FROM_UNIXTIME(b.dealorder_createtime,'%Y-%m-%d') as order_date
                                FROM smc_student_coursebalance_log AS l
                                inner join smc_forward_dealorder b on l.trading_pid=b.trading_pid
                                WHERE l.student_id = '{$orderOne['student_id']}' AND l.course_id = '{$orderOne['course_id']}' AND l.hourstudy_id = '0' AND l.trading_pid <> '' and l.log_playclass='-' and b.dealorder_status=1 and l.log_playtimes>0
                                HAVING trading_pid='{$changeOrderOne['trading_pid']}'
                                ORDER BY l.log_time ASC";

                    $refundOne = $this->DataControl->selectOne($sql);

                    if($refundOne){
                        $data = array();
                        $data['company_id'] = $orderOne['company_id'];
                        $data['companies_id'] = $orderOne['companies_id'];
                        $data['agency_id'] = $this->agencyId;
                        $data['school_id'] = $orderOne['school_id'];
                        $data['coursetype_id'] = $orderOne['coursetype_id'];
                        $data['coursecat_id'] = $orderOne['coursecat_id'];
                        $data['course_id'] = $orderOne['course_id'];
                        $data['class_id'] = $orderOne['class_id'];
                        $data['student_id'] = $orderOne['student_id'];
                        $data['parent_mobile'] = $orderOne['parent_mobile'];
                        $data['order_type'] = 'R';
                        $data['order_pid'] = $refundOne['order_pid'];
                        $data['order_amt'] = $refundOne['order_amt'];
                        $data['order_fee'] = $refundOne['order_fee'];
                        $data['order_num'] = $refundOne['order_num'];
                        $data['settle_date'] = $refundOne['order_date'];

                        $transOrderOne=$this->DataControl->getFieldOne("cmb_trans_order","order_amt","order_pid='{$orderOne['order_pid']}' and is_confirm=1 and order_amt >= '{$refundOne['order_amt']}'");
                        if($transOrderOne){
                            $data['is_confirm']=1;
                        }
                        $data['from_order_pid']=$orderOne['order_pid'];
                        $data['order_date'] = $refundOne['order_date'];
                        $data['is_errorpaly'] = 1;

                        $this->DataControl->insertData('cmb_trans_order', $data);
                        $data=array();
                        $data['change_status']=1;
                        $this->DataControl->updateData("cmb_trans_order_change","trading_pid='{$changeOrderOne['trading_pid']}' and course_id='{$orderOne['course_id']}'",$data);

                        $data=array();
                        $data['is_errorpaly'] = 1;
                        $this->DataControl->updateData("cmb_trans_order","order_pid='{$orderOne['order_pid']}'",$data);

                    }else{
                        $this->error = 1;
                        $this->errortip = "结转订单不符合要求";
                        return false;
                    }
                }elseif($changeOrderOne && $changeOrderOne['order_pid'] && ($changeOrderOne['is_confirm'] = 1 || $changeOrderOne['class_id'] = $orderOne['class_id'])){
                    $data=array();
                    $data['is_errorpaly'] = 1;
                    $this->DataControl->updateData("cmb_trans_order","order_pid='{$changeOrderOne['order_pid']}'",$data);

                    $data=array();
                    $data['is_errorpaly'] = 1;
                    $this->DataControl->updateData("cmb_trans_order","order_pid='{$orderOne['order_pid']}'",$data);
                    return true;
                }else{
                    $this->error = 1;
                    $this->errortip = "暂不处理";
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "结转订单不存在";
                return false;
            }


        }else{
            $this->error = 1;
            $this->errortip = "无考勤";
            return false;
        }


//        $sql = "select c.log_id,c.log_finalamount,b.*
//                ,ifnull((select x.hourstudy_id from smc_student_coursebalance_log as x where x.log_id>c.log_id and x.class_id=c.class_id and x.student_id=a.student_id and x.hourstudy_id>0 and x.log_playamount>0 order by x.log_id asc limit 0,1),0) as hourstudy_id
//                from cmb_trans_order_change as a,cmb_trans_order as b,smc_student_coursebalance_log as c
//                where a.order_pid=b.order_pid and a.trading_pid=c.trading_pid and c.log_class=0 and a.order_pid='{$order_pid}'
//                ";
//
//        //$orderOne=$this->DataControl->selectOne($sql);
//        exit;
//
//        if($orderOne && $orderOne['hourstudy_id']==0){
//
//
//        }else{
//            return $order_pid;
//        }

        //取B入班订单，判断金额是否和结转和A班下单一致，如下单一致，自动确认，课耗分摊



    }


    //处理班级缓存表
    function addCmbClassInfo()
    {
        $sql = "SELECT
	c.class_id,
	b.companies_id,
	p.companies_agencyid AS agency_id,
	c.class_branch,
	c.class_cnname,
	c.class_stdate,
	c.class_enddate,
	(
		SELECT
			count(ch.hour_id)
		FROM
			smc_class_hour AS ch
		WHERE
			ch.class_id = c.class_id
		AND ch.hour_ischecking <> - 1
	) AS lessons
FROM
	smc_class AS c,
	smc_student_study AS d,
	smc_student_coursebalance AS b,
	gmc_code_companies AS p
WHERE
	c.class_id = d.class_id
AND d.student_id = b.student_id
AND c.course_id = b.course_id
AND c.school_id = b.school_id
AND b.companies_id = p.companies_id
AND b.companies_id = '{$this->companies_id}'
AND c.class_enddate >= FROM_UNIXTIME(
	p.companies_supervisetime,
	'%Y-%m-%d'
)
AND b.coursebalance_issupervise = '1'
AND (not exists(select 1 from cmb_trans_class as x where x.class_id=c.class_id and x.updatetime > c.class_updatatime)
    OR (not exists(select 1 from cmb_trans_class as x where x.class_id=c.class_id) and c.class_updatatime > 0)) 
GROUP BY c.class_id
HAVING lessons > 0 limit 10";

        $classArray = $this->DataControl->selectClear($sql);
        if ($classArray) {
            foreach ($classArray as $k => $classOne) {
                $data = array();
                $data['agency_id'] = $classOne['agency_id'];
                $data['companies_id'] = $classOne['companies_id'];
                $data['class_branch'] = $classOne['class_branch'];
                $data['class_stdate'] = $classOne['class_stdate'];
                $data['class_enddate'] = $classOne['class_enddate'];
                $data['update_status'] = 0;
                $data['updatetime'] = time();
                if ($this->DataControl->selectOne("select id from cmb_trans_class where class_id='{$classOne['class_id']}'")) {
                    $updateclass = $this->DataControl->updateData('cmb_trans_class', "class_id = '{$classOne['class_id']}'", $data);
                    if (!$updateclass) {
                        $this->addTemplogInfo($classOne['companies_id'], $classOne['agency_id'], '0', 'update', 'cmb_trans_class', $data);
                    }
                } else {
                    $data['is_confirm'] = 0;
                    $data['class_id'] = $classOne['class_id'];
                    $insertclass = $this->DataControl->insertData('cmb_trans_class', $data);
                    if (!$insertclass) {
                        $this->addTemplogInfo($classOne['companies_id'], $classOne['agency_id'], '0', 'insert', 'cmb_trans_class', $data);
                    }
                }
            }
        } else {
            return 0;
        }
        return count($classArray);
    }

    //缓存班级课时
    function addCmbLessonInfo($class_branch='')
    {
        $time = time();

        $datawhere=" a.companies_id='{$this->companies_id}' ";
        if($class_branch!=''){
            $datawhere .= " and a.class_branch='{$class_branch}'";
        }
        $sql = "select a.class_id
        ,ifnull((select max(lesson_updatetime) from cmb_trans_lesson where class_id=a.class_id),0) as cmb_updatetime
        ,ifnull((select max(hour_updatatime) from smc_class_hour where class_id=a.class_id),0) as smc_updatetime
        from cmb_trans_class a
        left join smc_class b on a.class_id=b.class_id
        where {$datawhere} and  a.update_status=1 and a.class_enddate>='2024-01-01'
        -- and a.update_status=1
        having (smc_updatetime>cmb_updatetime OR cmb_updatetime = 0) and cmb_updatetime=0 
        limit 10
        ";
        $classArray = $this->DataControl->selectClear($sql);
        $count = 0;
        if ($classArray) {
            foreach ($classArray as $k => $classOne) {
                $sqlHour = "select ifnull(lesson_id,-1) as lesson_id
                ,g.companies_agencyid as agency_id
                ,b.school_id
                ,e.coursetype_id
                ,e.coursecat_id
                ,e.course_id
                ,a.class_id
                ,c.hour_id
                ,c.hour_lessontimes
                ,c.hour_name
                ,c.hour_day
                ,c.hour_ischecking
                from cmb_trans_class a
                left join smc_class b on a.class_id=b.class_id
                left join smc_class_hour c on b.class_id=c.class_id and hour_isfree=0
                left join cmb_trans_lesson d on a.class_id=d.class_id and d.hour_id=c.hour_id
                left join smc_course e on b.course_id=e.course_id
                left join smc_school_coursecat_subject f on f.coursecat_id=e.coursecat_id and f.school_id=b.school_id
                left join gmc_code_companies g on g.companies_id=f.companies_id
                where a.companies_id='{$this->companies_id}'
                -- and a.update_status=1 
                  AND a.class_id = '{$classOne['class_id']}'
                and (ifnull(lesson_id,-1)<=0
                or c.hour_day<>ifnull(d.hour_day,'')
                or c.hour_lessontimes<>ifnull(d.hour_lessontimes,0)
		        OR (c.hour_updatatime > d.lesson_updatetime )
                or c.hour_name<>ifnull(d.hour_name,'') or (d.hour_status<>(case when c.hour_ischecking='-1' then 0 else 1 end)))";
                $hourArray = $this->DataControl->selectClear($sqlHour);
                if (!$hourArray) {
                    continue;
                }
                foreach ($hourArray as $hourOne) {
                    $data = array();
                    $data['agency_id'] = $hourOne['agency_id'];
                    $data['school_id'] = $hourOne['school_id'];
                    $data['coursetype_id'] = $hourOne['coursetype_id'];
                    $data['coursecat_id'] = $hourOne['coursecat_id'];
                    $data['course_id'] = $hourOne['course_id'];
                    $data['class_id'] = $hourOne['class_id'];
                    $data['hour_id'] = $hourOne['hour_id'];
                    $data['hour_lessontimes'] = $hourOne['hour_lessontimes'];
                    $data['hour_name'] = $hourOne['hour_name'];
                    $data['hour_day'] = $hourOne['hour_day'];
                    $data['hour_status'] = ($hourOne['hour_ischecking'] == -1) ? 0 : 1;
                    $data['lesson_status'] = 0;
                    $data['lesson_updatetime'] = $time;
                    if ($hourOne['lesson_id'] <= 0) {
                        $insertlesson = $this->DataControl->insertData('cmb_trans_lesson', $data);
                        if (!$insertlesson) {
                            $this->addTemplogInfo($hourOne['companies_id'], $hourOne['agency_id'], '0', 'insert', 'cmb_trans_lesson', $data);
                        }
                    } else {
                        $updatelesson = $this->DataControl->updateData('cmb_trans_lesson', "lesson_id = '{$hourOne['lesson_id']}'", $data);
                        if (!$updatelesson) {
                            $this->addTemplogInfo($hourOne['companies_id'], $hourOne['agency_id'], '0', 'update', 'cmb_trans_lesson', $data);
                        }
                    }
                    $count++;
                }
            }
        }
        return $count;
    }

    //处理学生订单异动缓存表
    function addCmbOrderChangeInfo()
    {
        $sql = "SELECT o.order_pid AS pid, o.student_id, o.school_id, o.companies_id,c.course_id,o.trading_pid
FROM smc_payfee_order AS o, smc_payfee_order_course AS c, smc_payfee_order_pay AS p, smc_student_coursebalance AS u 
WHERE c.order_pid = o.order_pid AND o.order_pid = p.order_pid AND o.student_id = u.student_id AND o.school_id = u.school_id 
AND c.course_id = u.course_id AND o.companies_id = '{$this->companies_id}' AND p.pay_issuccess = '1' AND o.order_status = '4' AND u.coursebalance_issupervise = '1' 
AND p.pay_successtime >= '{$this->starttime}' 
AND NOT EXISTS ( SELECT 1 FROM cmb_trans_order_change AS ch WHERE ch.order_pid = o.order_pid AND o.school_id = ch.school_id ) 
and exists(select 1 from cmb_order as x where x.trading_pid=o.order_pid)
GROUP BY o.order_pid 
UNION ALL SELECT o.dealorder_pid AS pid, o.student_id, o.school_id, o.companies_id,c.course_id,o.trading_pid
FROM smc_forward_dealorder AS o, smc_forward_dealorder_course AS c, smc_student_coursebalance AS u 
WHERE o.dealorder_pid = c.dealorder_pid AND o.student_id = u.student_id 
AND o.school_id = u.school_id AND c.course_id = u.course_id AND o.companies_id = '{$this->companies_id}' AND o.dealorder_status = '1'
AND u.coursebalance_issupervise = '1' AND o.dealorder_createtime >= '{$this->starttime}'  
AND NOT EXISTS ( SELECT 1 FROM cmb_trans_order_change AS ch WHERE ch.order_pid = o.dealorder_pid AND o.school_id = ch.school_id ) 
GROUP BY o.dealorder_pid limit 50";

        $studyList = $this->DataControl->selectClear($sql);
        if (!$studyList) {
            return 0;
        }
        $count = 0;
        foreach ($studyList as $studyOne) {
            if(!$this->DataControl->getFieldOne("cmb_trans_order_change", "change_id", "order_pid = '{$studyOne['pid']}'")){
                $data = array();
                $data['order_pid'] = $studyOne['pid'];
                $data['companies_id'] = $studyOne['companies_id'];
                $data['student_id'] = $studyOne['student_id'];
                $data['school_id'] = $studyOne['school_id'];
                $data['course_id'] = $studyOne['course_id'];
                $data['trading_pid'] = $studyOne['trading_pid'];
                $this->DataControl->insertData('cmb_trans_order_change', $data);
                $count++;
            }
        }
        return $count;
    }

    //处理学生订单缓存表
    function addCmbOrderInfo($student_id=0)
    {
        $datawhere = "d.class_id = c.class_id AND d.student_id = b.student_id AND c.course_id = b.course_id AND b.school_id = c.school_id and co.course_id=b.course_id
                AND b.coursebalance_issupervise = '1' AND b.companies_id = '{$this->companies_id}'
                AND EXISTS ( SELECT 1 FROM cmb_trans_order_change AS ch,smc_school_income as x WHERE ch.school_id=x.school_id and ch.student_id=x.student_id 
                and ch.course_id=x.course_id and ch.student_id = d.student_id AND ch.companies_id = b.companies_id AND ch.school_id = d.school_id 
                AND ch.course_id = c.course_id AND ch.change_status = '0') 
                AND EXISTS ( SELECT 1 FROM smc_school_income AS i WHERE i.student_id = d.student_id AND i.school_id = c.school_id AND i.class_id = d.class_id )";

        if($student_id !== 0){
            $datawhere .= " AND d.student_id = '{$student_id}'";
        }

        $sql = "SELECT d.class_id,d.student_id,d.study_beginday,b.companies_id,c.course_id,b.company_id,d.school_id,co.coursetype_id,co.coursecat_id,c.class_stdate,c.class_enddate
                FROM smc_student_study AS d, smc_class AS c, smc_student_coursebalance AS b,smc_course as co
                WHERE {$datawhere}
                GROUP BY d.class_id desc,d.student_id desc 
                limit 0,10";
        $studyList = $this->DataControl->selectClear($sql);
        if (!$studyList) {
            return 0;
        }
        $count = 0;
        foreach ($studyList as $key=>$studyOne) {
            $sql="select b.parenter_mobile from smc_student_family as a,smc_parenter as b where a.parenter_id=b.parenter_id and a.family_isdefault=1 and a.student_id='{$studyOne['student_id']}'";
            $familyOne=$this->DataControl->selectOne($sql);

            $sql = "SELECT l.trading_pid,l.log_time,l.log_playclass,o.order_pid
                    ,(select ordercourse_buynums from smc_payfee_order_course where order_pid=o.order_pid and course_id=l.course_id) as order_num
                    ,(select ordercourse_totalprice from smc_payfee_order_course where order_pid=o.order_pid and course_id=l.course_id) as order_amt
                    ,o.order_paymentprice,o.order_status,0 as order_fee
                    ,FROM_UNIXTIME((select max(pay_successtime) from smc_payfee_order_pay where order_pid=o.order_pid and pay_issuccess=1),'%Y-%m-%d') as order_date
                    ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=o.order_pid and y.paytype_ischarge=1 and x.pay_issuccess=1),0) as order_payamt
                    ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=o.order_pid and y.paytype_ischarge=0 and x.pay_issuccess=1 and y.paytype_code<>'feewaiver' and y.paytype_code<>'canceldebts'),0) as order_disamt
                    ,ifnull(( SELECT c.log_id FROM smc_student_coursebalance_log AS c WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id AND c.log_id > l.log_id and c.class_id='{$studyOne['class_id']}' AND c.hourstudy_id > '0' ORDER BY c.log_id ASC LIMIT 0, 1),0) as hourstudylog_id
                    ,ifnull(( SELECT c.log_id FROM smc_student_coursebalance_log AS c WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id AND c.log_id > l.log_id AND c.hourstudy_id = '0' AND c.trading_pid <> '' and c.log_playclass='-' and c.log_playtimes>0 ORDER BY c.log_id ASC LIMIT 0, 1),0) as forward_id
                    FROM smc_student_coursebalance_log AS l,smc_payfee_order AS o
                    WHERE l.trading_pid=o.trading_pid and l.student_id = '{$studyOne['student_id']}' AND l.course_id = '{$studyOne['course_id']}' AND l.hourstudy_id = '0' AND l.trading_pid <> '' and l.log_playclass='+' and o.order_status=4 AND (l.class_id = '{$studyOne['class_id']}' OR l.class_id = '0')
                    and exists(select 1 from cmb_trans_order_change as x where x.trading_pid=l.trading_pid and x.change_status=0)
                    having hourstudylog_id>0 and (forward_id=0 or forward_id>hourstudylog_id)
                    ORDER BY l.log_time ASC";

            $orderList=$this->DataControl->selectClear($sql);

            if($orderList){
                foreach($orderList as $orderOne){
                    $data = array();
                    $data['company_id'] = $studyOne['company_id'];
                    $data['companies_id'] = $studyOne['companies_id'];
                    $data['agency_id'] = $this->agencyId;
                    $data['school_id'] = $studyOne['school_id'];
                    $data['coursetype_id'] = $studyOne['coursetype_id'];
                    $data['coursecat_id'] = $studyOne['coursecat_id'];
                    $data['course_id'] = $studyOne['course_id'];
                    $data['class_id'] = $studyOne['class_id'];
                    $data['student_id'] = $studyOne['student_id'];
                    $data['parent_mobile'] = $familyOne?$familyOne['parenter_mobile']:'';
                    $data['order_type'] = 'P';
                    $data['order_pid'] = $orderOne['order_pid'];
                    $data['order_amt'] = $orderOne['order_amt'];
                    $data['order_fee'] = $orderOne['order_fee'];
                    $data['order_num'] = $orderOne['order_num'];
                    $data['settle_date'] = $orderOne['order_date'];
                    $data['order_date'] = ($orderOne['order_date'] >= $studyOne['class_stdate']) ? (($orderOne['order_date'] >= $studyOne['class_enddate']) ? $studyOne['class_enddate'] : $orderOne['order_date']) : $studyOne['class_stdate'];

                    if($orderOne['order_paymentprice'] == $orderOne['order_payamt']){
                        $data['is_confirm']=1;
                    }

                    if(!$this->DataControl->getFieldOne("cmb_trans_order","order_id","companies_id='{$studyOne['companies_id']}' and order_pid='{$orderOne['order_pid']}' and class_id='{$studyOne['class_id']}'")){

                        $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                        if (!$insertorder) {
                            $this->addTemplogInfo($studyOne['companies_id'], $studyOne['agency_id'], '0', 'insert', 'cmb_trans_order', $data);
                        }else{
                            $data=array();
                            $data['change_status']=1;
                            $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$orderOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);

                            $count++;
                        }
                    }else{
                        $data=array();
                        $data['change_status']=1;
                        $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$orderOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);
                    }
                }
            }



            $sql = "SELECT l.trading_pid,l.log_time,l.log_playclass
                    ,b.dealorder_pid as order_pid
                    ,b.dealorder_status
                    ,l.log_playtimes as order_num
                    ,l.log_playamount as order_amt
                    ,0 as order_fee
                    ,FROM_UNIXTIME(b.dealorder_createtime,'%Y-%m-%d') as order_date
                    ,ifnull(( SELECT c.log_id FROM smc_student_coursebalance_log AS c WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id AND c.log_id < l.log_id and c.class_id='{$studyOne['class_id']}' AND c.hourstudy_id > '0' ORDER BY c.log_id desc LIMIT 0, 1),0) as hourstudylog_id
                    ,ifnull(( SELECT c.log_id FROM smc_student_coursebalance_log AS c WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id AND c.log_id < l.log_id AND c.hourstudy_id = '0' AND c.trading_pid <> '' and c.log_playclass='+' AND c.log_playamount > 0 ORDER BY c.log_id desc LIMIT 0, 1),0) as orderbuy_id
                    ,ifnull(( SELECT c.trading_pid FROM smc_student_coursebalance_log AS c WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id AND c.log_id < l.log_id AND c.hourstudy_id = '0' AND c.trading_pid <> '' and c.log_playclass='+' AND c.log_playamount > 0 ORDER BY c.log_id desc LIMIT 0, 1),'') as from_trading_pid
                    FROM smc_student_coursebalance_log AS l
                    inner join smc_forward_dealorder b on l.trading_pid=b.trading_pid
                    WHERE l.student_id = '{$studyOne['student_id']}' AND l.course_id = '{$studyOne['course_id']}' AND l.hourstudy_id = '0' AND l.trading_pid <> '' and l.log_playclass='-' and b.dealorder_status=1 and l.log_playtimes>0
                    and exists(select 1 from cmb_trans_order_change as x where x.trading_pid=l.trading_pid and x.change_status=0)
                    HAVING orderbuy_id > 0 AND ( (hourstudylog_id > 0 AND orderbuy_id < hourstudylog_id) OR EXISTS(SELECT 1 FROM cmb_trans_order as cto,cmb_trans_order_change as coc WHERE cto.order_pid = coc.order_pid AND coc.trading_pid = from_trading_pid AND cto.order_status = '1' AND cto.order_status_all = '1'))
                    ORDER BY l.log_time ASC";

            $refundList = $this->DataControl->selectClear($sql);

            if($refundList){
                foreach($refundList as $refundOne){
                    $data = array();
                    $data['company_id'] = $studyOne['company_id'];
                    $data['companies_id'] = $studyOne['companies_id'];
                    $data['agency_id'] = $this->agencyId;
                    $data['school_id'] = $studyOne['school_id'];
                    $data['coursetype_id'] = $studyOne['coursetype_id'];
                    $data['coursecat_id'] = $studyOne['coursecat_id'];
                    $data['course_id'] = $studyOne['course_id'];
                    $data['class_id'] = $studyOne['class_id'];
                    $data['student_id'] = $studyOne['student_id'];
                    $data['parent_mobile'] = $familyOne['parenter_mobile'];
                    $data['order_type'] = 'R';
                    $data['order_pid'] = $refundOne['order_pid'];
                    $data['order_amt'] = $refundOne['order_amt'];
                    $data['order_fee'] = $refundOne['order_fee'];
                    $data['order_num'] = $refundOne['order_num'];
                    $data['settle_date'] = $refundOne['order_date'];

                    $from_trading_pid=$refundOne['from_trading_pid'];

                    if($from_trading_pid && $from_trading_pid!=''){
                        $changeOne=$this->DataControl->getFieldOne("cmb_trans_order_change","order_pid","trading_pid='{$from_trading_pid}'");

                        if($changeOne){
                            $transOrderOne=$this->DataControl->getFieldOne("cmb_trans_order","order_amt","order_pid='{$changeOne['order_pid']}' and is_confirm=1 and order_amt >= '{$refundOne['order_fee']}'");
                            if($transOrderOne){
                                $data['is_confirm']=1;
                            }

                            $data['from_order_pid']=$changeOne['order_pid'];
                            $data['order_date'] = ($refundOne['order_date'] >= $studyOne['class_stdate']) ? (($refundOne['order_date'] >= $studyOne['class_enddate']) ? $studyOne['class_enddate'] : $refundOne['order_date']) : $studyOne['class_stdate'];
                            $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                            if (!$insertorder) {
                                $this->addTemplogInfo($studyOne['companies_id'], $studyOne['agency_id'], '0', 'insert', 'cmb_trans_order', $data);
                            }else{
                                $data=array();
                                $data['change_status']=1;
                                $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$refundOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);

                                $count++;
                            }
                        }else{
                            $data=array();
                            $data['change_status']=-3;
                            $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$refundOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);
                        }
                    }else{
                        $data=array();
                        $data['change_status']=-2;
                        $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$refundOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);
                    }
                }
            }

            if(!$refundList && !$orderList && $key==count($studyList)-1){
                $data=array();
                $data['change_status']=-1;
                $this->DataControl->updateData("cmb_trans_order_change","student_id='{$studyOne['student_id']}' and course_id='{$studyOne['course_id']}' and change_status=0",$data);
            }
        }

        return $count;
    }

    //处理学生订单缓存表
    function addCmbOrderInfobak()
    {
        $sql = "SELECT d.class_id,d.student_id,d.study_beginday,b.companies_id,c.course_id,b.company_id,d.school_id,co.coursetype_id,co.coursecat_id,c.class_stdate,c.class_enddate
                FROM smc_student_study AS d, smc_class AS c, smc_student_coursebalance AS b,smc_course as co
                WHERE d.class_id = c.class_id AND d.student_id = b.student_id AND c.course_id = b.course_id AND b.school_id = c.school_id and co.course_id=b.course_id
                AND b.coursebalance_issupervise = '1' AND b.companies_id = '{$this->companies_id}'
                AND EXISTS ( SELECT 1 FROM cmb_trans_order_change AS ch,smc_school_income as x WHERE ch.school_id=x.school_id and ch.student_id=x.student_id and ch.course_id=x.course_id and ch.student_id = d.student_id AND ch.companies_id = b.companies_id AND ch.school_id = d.school_id AND ch.course_id = c.course_id AND ch.change_status = '0') 
                AND EXISTS ( SELECT 1 FROM smc_school_income AS i WHERE i.student_id = d.student_id AND i.school_id = c.school_id AND i.class_id = d.class_id )
                GROUP BY d.class_id,d.student_id 
                limit 0,10";

        $studyList = $this->DataControl->selectClear($sql);

        if (!$studyList) {
            return 0;
        }
        $count = 0;
        foreach ($studyList as $studyOne) {

            $sql="select b.parenter_mobile from smc_student_family as a,smc_parenter as b where a.parenter_id=b.parenter_id and a.family_isdefault=1 and a.student_id='{$studyOne['student_id']}'";
            $familyOne=$this->DataControl->selectOne($sql);

            $sql = "SELECT l.trading_pid,l.log_time,l.log_playclass
                    FROM smc_student_coursebalance_log AS l
                    WHERE l.student_id = '{$studyOne['student_id']}' AND l.course_id = '{$studyOne['course_id']}' AND l.hourstudy_id = '0' AND l.trading_pid <> '' 
                    and exists(select 1 from cmb_trans_order_change as x where x.trading_pid=l.trading_pid and x.change_status=0)
                    HAVING (( SELECT c.class_id FROM smc_student_coursebalance_log AS c
                                WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id
                                AND c.log_id > l.log_id AND c.hourstudy_id > '0'
                                ORDER BY c.log_id ASC
                                LIMIT 0, 1
                             ) = '{$studyOne['class_id']}' and log_playclass='+') or (( SELECT c.class_id FROM smc_student_coursebalance_log AS c
                                WHERE c.student_id = l.student_id AND c.course_id = l.course_id AND c.school_id = l.school_id
                                AND c.log_id < l.log_id AND c.hourstudy_id > '0'
                                ORDER BY c.log_id DESC
                                LIMIT 0, 1
                             ) = '{$studyOne['class_id']}' and log_playclass='-') 
                    ORDER BY l.log_time ASC";

            $tradingList=$this->DataControl->selectClear($sql);

            if($tradingList){

                $tradingStr=implode("','",array_column($tradingList,'trading_pid'));

                $sql = "SELECT o.order_pid,c.ordercourse_buynums as order_num,c.ordercourse_totalprice as order_amt
                ,o.order_paymentprice,o.order_status,0 as order_fee
                ,FROM_UNIXTIME((select max(pay_successtime) from smc_payfee_order_pay where order_pid=o.order_pid and pay_issuccess=1),'%Y-%m-%d') as order_date
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=o.order_pid and y.paytype_ischarge=1 and x.pay_issuccess=1),0) as order_payamt
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=o.order_pid and y.paytype_ischarge=0 and x.pay_issuccess=1 and y.paytype_code<>'feewaiver' and y.paytype_code<>'canceldebts'),0) as order_disamt
                FROM smc_payfee_order AS o,smc_payfee_order_course AS c,smc_payfee_order_pay AS p
                WHERE c.order_pid = o.order_pid AND o.order_pid = p.order_pid AND o.student_id = '{$studyOne['student_id']}'
                AND c.course_id = '{$studyOne['course_id']}' AND o.school_id = '{$studyOne['school_id']}' AND p.pay_issuccess = '1'
                AND o.trading_pid IN ('".$tradingStr."')
                group by o.order_pid,c.course_id
                order by p.pay_successtime asc";

                $orderList=$this->DataControl->selectClear($sql);

                if($orderList){
                    foreach($orderList as $orderOne){

                        $data = array();
                        $data['company_id'] = $studyOne['company_id'];
                        $data['companies_id'] = $studyOne['companies_id'];
                        $data['agency_id'] = $this->agencyId;
                        $data['school_id'] = $studyOne['school_id'];
                        $data['coursetype_id'] = $studyOne['coursetype_id'];
                        $data['coursecat_id'] = $studyOne['coursecat_id'];
                        $data['course_id'] = $studyOne['course_id'];
                        $data['class_id'] = $studyOne['class_id'];
                        $data['student_id'] = $studyOne['student_id'];
                        $data['parent_mobile'] = $familyOne?$familyOne['parenter_mobile']:'';
                        $data['order_type'] = 'P';
                        $data['order_pid'] = $orderOne['order_pid'];
                        $data['order_amt'] = $orderOne['order_amt'];
                        $data['order_fee'] = $orderOne['order_fee'];
                        $data['order_num'] = $orderOne['order_num'];
                        $data['settle_date'] = $orderOne['order_date'];
                        $data['order_date'] = ($orderOne['order_date'] >= $studyOne['class_stdate']) ? (($orderOne['order_date'] >= $studyOne['class_enddate']) ? $studyOne['class_enddate'] : $orderOne['order_date']) : $studyOne['class_stdate'];

                        if($orderOne['order_paymentprice']==$orderOne['order_payamt']){
                            $data['is_confirm']=1;
                        }

                        if(!$this->DataControl->getFieldOne("cmb_trans_order","order_id","companies_id='{$studyOne['companies_id']}' and order_pid='{$orderOne['order_pid']}' and class_id='{$studyOne['class_id']}'")){

                            $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                            if (!$insertorder) {
                                $this->addTemplogInfo($studyOne['companies_id'], $studyOne['agency_id'], '0', 'insert', 'cmb_trans_order', $data);
                            }else{
                                $data=array();
                                $data['change_status']=1;
                                $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$orderOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);

                                $count++;
                            }
                        }
                    }
                }

                $sql = "select b.dealorder_pid as order_pid,b.trading_pid
                ,b.dealorder_status
                ,c.log_playtimes as order_num
                ,c.log_playamount as order_amt
                ,0 as order_fee
                ,FROM_UNIXTIME(b.dealorder_createtime,'%Y-%m-%d') as order_date
                from smc_forward_dealorder_course a
                inner join smc_forward_dealorder b on a.dealorder_pid=b.dealorder_pid
                inner join smc_student_coursebalance_log as c on b.student_id=c.student_id and b.school_id=c.school_id and a.course_id=c.course_id and b.trading_pid=c.trading_pid
                where b.companies_id='{$studyOne['companies_id']}'
                and b.school_id='{$studyOne['school_id']}'
                and b.student_id='{$studyOne['student_id']}'
                and a.course_id='{$studyOne['course_id']}'
                and b.dealorder_status>=0
                and b.dealorder_type=0 AND b.trading_pid IN ('".$tradingStr."')
                order by b.dealorder_createtime asc";
                $refundList = $this->DataControl->selectClear($sql);

                if($refundList){
                    foreach($refundList as $refundOne){
                        $data = array();
                        $data['company_id'] = $studyOne['company_id'];
                        $data['companies_id'] = $studyOne['companies_id'];
                        $data['agency_id'] = $this->agencyId;
                        $data['school_id'] = $studyOne['school_id'];
                        $data['coursetype_id'] = $studyOne['coursetype_id'];
                        $data['coursecat_id'] = $studyOne['coursecat_id'];
                        $data['course_id'] = $studyOne['course_id'];
                        $data['class_id'] = $studyOne['class_id'];
                        $data['student_id'] = $studyOne['student_id'];
                        $data['parent_mobile'] = $familyOne['parenter_mobile'];
                        $data['order_type'] = 'R';
                        $data['order_pid'] = $refundOne['order_pid'];
                        $data['order_amt'] = $refundOne['order_amt'];
                        $data['order_fee'] = $refundOne['order_fee'];
                        $data['order_num'] = $refundOne['order_num'];
                        $data['settle_date'] = $refundOne['order_date'];

                        foreach($tradingList as $tradOne){
                            if($tradOne['trading_pid']==$refundOne['trading_pid']){
                                break;
                            }
                            $from_trading_pid = $tradOne['trading_pid'];
                        }

                        if($from_trading_pid && $from_trading_pid!=''){
                            $changeOne=$this->DataControl->getFieldOne("cmb_trans_order_change","order_pid","trading_pid='{$from_trading_pid}'");
                            if($changeOne){
                                $data['from_order_pid']=$changeOne['order_pid'];
                                $data['order_date'] = ($refundOne['order_date'] >= $studyOne['class_stdate']) ? (($refundOne['order_date'] >= $studyOne['class_enddate']) ? $studyOne['class_enddate'] : $refundOne['order_date']) : $studyOne['class_stdate'];
                                $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                                if (!$insertorder) {
                                    $this->addTemplogInfo($studyOne['companies_id'], $studyOne['agency_id'], '0', 'insert', 'cmb_trans_order', $data);
                                }else{
                                    $data=array();
                                    $data['change_status']=1;
                                    $this->DataControl->updateData("cmb_trans_order_change","order_pid='{$refundOne['order_pid']}' and course_id='{$studyOne['course_id']}'",$data);

                                    $count++;
                                }
                            }
                        }
                    }
                }

            }else{
                $data=array();
                $data['change_status']=-1;
                $this->DataControl->updateData("cmb_trans_order_change","student_id='{$studyOne['student_id']}' and course_id='{$studyOne['course_id']}' and change_status=0",$data);
            }
        }
        return $count;
    }

    //生成课耗收入缓存表
    function addCmbTransferInfo()
    {
        $sql = "select a.company_id
        ,a.companies_id
        ,c.companies_agencyid as agency_id
        ,a.school_id
        ,b.coursetype_id
        ,b.coursecat_id
        ,b.course_id
        ,a.class_id
        ,a.student_id
        ,'' as order_pid
        ,a.hourstudy_id
        ,a.income_id
        ,a.income_price
        ,1 as income_times
        ,FROM_UNIXTIME(a.income_confirmtime,'%Y-%m-%d') as income_date
        from smc_school_income a
        left join smc_course b on a.course_id=b.course_id
        left join gmc_code_companies c on a.companies_id=c.companies_id
        left join smc_student_coursebalance d on a.school_id=d.school_id and a.course_id=d.course_id and a.student_id=d.student_id
	    LEFT JOIN cmb_trans_transfer e ON a.hourstudy_id = e.hourstudy_id
        where 1
        and a.companies_id='{$this->companies_id}'
        and a.income_type = 0
        and a.hourstudy_id > 0
        and d.coursebalance_issupervise=1
        and c.companies_issupervise=1
        and b.course_issupervise=1 
        and a.income_confirmtime >= c.companies_supervisetime
        AND (e.hourstudy_id IS NULL OR (e.is_confirm = '0' AND (e.order_pid = '' or EXISTS(SELECT 1 FROM cmb_trans_order as o WHERE o.order_pid = e.order_pid AND o.is_confirm = '1' and e.class_id=o.class_id))))
        order by a.income_id desc
        ";

        $sql.=" limit 0,50";


        $incomeList = $this->DataControl->selectClear($sql);

        if (!$incomeList) {
            return 0;
        }
        $count = 0;

        foreach ($incomeList as $incomeOne) {
            $isset = $this->DataControl->getFieldOne("cmb_trans_transfer", "transfer_id,transfer_status,order_pid", "hourstudy_id = '{$incomeOne['hourstudy_id']}' and student_id = '{$incomeOne['student_id']}'");
            $data = array();
            $data['company_id'] = $incomeOne['company_id'];
            $data['companies_id'] = $incomeOne['companies_id'];
            $data['agency_id'] = $incomeOne['agency_id'];
            $data['school_id'] = $incomeOne['school_id'];
            $data['coursetype_id'] = $incomeOne['coursetype_id'];
            $data['coursecat_id'] = $incomeOne['coursecat_id'];
            $data['course_id'] = $incomeOne['course_id'];
            $data['class_id'] = $incomeOne['class_id'];
            $data['student_id'] = $incomeOne['student_id'];
            $data['hourstudy_id'] = $incomeOne['hourstudy_id'];
            $data['income_id'] = $incomeOne['income_id'];
            $data['income_price'] = $incomeOne['income_price'];
            $data['income_times'] = $incomeOne['income_times'];
            $data['income_date'] = $incomeOne['income_date'];

            if ($isset) {
                if ($isset['transfer_status'] == '2') {
                    continue;
                } else {
                    $sql = "select ch.order_pid 
                            from cmb_trans_order_change as ch 
                            where ch.trading_pid=(select (select x.trading_pid from smc_student_coursebalance_log as x where x.school_id=a.school_id and x.student_id=a.student_id and x.course_id=a.course_id and x.log_id<=a.log_id and x.trading_pid<>'' and x.log_playclass='+' and x.log_playamount > 0  order by x.log_time desc,x.log_id desc limit 0,1) as trading_pid
                            from smc_student_coursebalance_log as a 
                            where a.hourstudy_id='{$incomeOne['hourstudy_id']}' limit 0,1)
                            ";

                    $orderOne=$this->DataControl->selectOne($sql);

                    $data['order_pid']=$orderOne?$orderOne['order_pid']:'';

                    $orderOne = $this->DataControl->getFieldOne("cmb_trans_order", "is_confirm", "order_pid = '{$data['order_pid']}' and class_id = '{$incomeOne['class_id']}' and order_type='P' order by order_date desc");

                    if($orderOne && $orderOne['is_confirm']==1){
                        $data['is_confirm']=1;
                    }

                    $data['transfer_updatetime'] = time();
                    $updatetransfer = $this->DataControl->updateData('cmb_trans_transfer', "transfer_id = '{$isset['transfer_id']}'", $data);
                    if (!$updatetransfer) {
                        $this->addTemplogInfo($incomeOne['companies_id'], $incomeOne['agency_id'], '0', 'update', 'cmb_trans_transfer', $data);
                    }

                    $count++;
                }
            } else {
                $orderOne = $this->DataControl->getFieldOne("cmb_trans_order", "order_pid,is_confirm", "student_id = '{$incomeOne['student_id']}' and class_id = '{$incomeOne['class_id']}' and order_type='P' order by order_date desc");

                $sql = "select ch.order_pid 
                        from cmb_trans_order_change as ch 
                        where ch.trading_pid=(select (select x.trading_pid from smc_student_coursebalance_log as x where x.school_id=a.school_id and x.student_id=a.student_id and x.course_id=a.course_id and x.log_id<=a.log_id and x.trading_pid<>'' and x.log_playclass='+' and x.log_playamount > 0 order by x.log_time desc,x.log_id desc limit 0,1) as trading_pid
                        from smc_student_coursebalance_log as a 
                        where a.hourstudy_id='{$incomeOne['hourstudy_id']}' limit 0,1)";

                $logOne=$this->DataControl->selectOne($sql);

                $data['order_pid']=$logOne?$logOne['order_pid']:$orderOne['order_pid'];

                if($orderOne && $orderOne['is_confirm']==1){
                    $data['is_confirm']=1;
                }
                $data['transfer_createtime'] = time();
                $inserttransfer = $this->DataControl->insertData('cmb_trans_transfer', $data);
                if (!$inserttransfer) {
                    $this->addTemplogInfo($incomeOne['companies_id'], $incomeOne['agency_id'], '0', 'insert', 'cmb_trans_transfer', $data);
                }
                $count++;
            }
        }
        return $count;
    }

    //处理学生订单缓存（无效）
    /*function addCmbOrderInfoBAK()
    {
        $sql = "select a.company_id
        ,d.companies_id
        ,f.companies_agencyid as agency_id
        ,a.school_id
        ,c.coursetype_id
        ,c.coursecat_id
        ,c.course_id
        ,a.class_id
        ,a.student_id
        ,h.parenter_mobile
        ,m.class_stdate
        ,m.class_enddate,a.study_beginday
        from cmb_trans_class m
        left join smc_student_study a on a.class_id=m.class_id
        left join smc_class b on a.class_id=b.class_id
        left join smc_course c on b.course_id=c.course_id
        left join smc_student_coursebalance d on a.school_id=d.school_id and a.student_id=d.student_id and c.course_id=d.course_id
        left join smc_school_coursecat_subject e on e.school_id=a.school_id and e.coursecat_id=c.coursecat_id
        left join gmc_code_companies f on f.companies_id=e.companies_id
        left join smc_student_family g on g.family_isdefault=1 and g.student_id=a.student_id
        left join smc_parenter h on h.parenter_id=g.parenter_id
        where 1
        and d.companies_id='{$this->companies_id}'
        and d.companies_id=f.companies_id
        and f.companies_issupervise=1
        and d.coursebalance_issupervise=1
        and c.course_issupervise=1
        ";

        $studyList = $this->DataControl->selectClear($sql);
        if (!$studyList) {
            return 0;
        }
        $count = 0;
        foreach ($studyList as $studyOne) {

            $ispayfee = $this->DataControl->getFieldOne("cmb_trans_order", "order_id", "class_id = '{$studyOne['class_id']}' and student_id = '{$studyOne['student_id']}' and order_type='P'");
            if (!$ispayfee) {
                $sql = "select a.order_pid,b.order_paymentprice
                ,b.order_status
                ,a.pricinglog_buytimes as order_num
                ,a.pricinglog_buyprice as order_amt
                ,0 as order_fee
                ,FROM_UNIXTIME((select max(pay_successtime) from smc_payfee_order_pay where order_pid=b.order_pid and pay_issuccess=1),'%Y-%m-%d') as order_date
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=b.order_pid and y.paytype_ischarge=1 and x.pay_issuccess=1),0) as order_payamt
                ,ifnull((select sum(x.pay_price) from smc_payfee_order_pay as x,smc_code_paytype as y where x.paytype_code=y.paytype_code and x.order_pid=b.order_pid and y.paytype_ischarge=0 and x.pay_issuccess=1 and y.paytype_code<>'feewaiver' and y.paytype_code<>'canceldebts'),0) as order_disamt
                from smc_student_coursebalance_pricinglog a
                left join smc_payfee_order b on a.order_pid=b.order_pid
                where 1
                and b.companies_id='{$studyOne['companies_id']}'
                and a.school_id='{$studyOne['school_id']}'
                and a.student_id='{$studyOne['student_id']}'
                and a.course_id='{$studyOne['course_id']}'
                and b.order_status>=0 and FROM_UNIXTIME(b.order_createtime,'%Y-%m-%d')<='{$studyOne['study_beginday']}'
                order by a.pricinglog_starttime desc
                limit 0,1 ";//(select sum(y.paylog_ifee) from smc_payfee_order_pay x,smc_payfee_order_paylog y where x.pay_pid=y.pay_pid and x.order_pid=b.order_pid and x.pay_issuccess=1)

                $payfeeOne = $this->DataControl->selectOne($sql);
                if ($payfeeOne && $payfeeOne['order_status'] == '4') {
                    $data = array();
                    $data['company_id'] = $studyOne['company_id'];
                    $data['companies_id'] = $studyOne['companies_id'];
                    $data['agency_id'] = $studyOne['agency_id'];
                    $data['school_id'] = $studyOne['school_id'];
                    $data['coursetype_id'] = $studyOne['coursetype_id'];
                    $data['coursecat_id'] = $studyOne['coursecat_id'];
                    $data['course_id'] = $studyOne['course_id'];
                    $data['class_id'] = $studyOne['class_id'];
                    $data['student_id'] = $studyOne['student_id'];
                    $data['parent_mobile'] = $studyOne['parenter_mobile'];
                    $data['order_type'] = 'P';
                    $data['order_pid'] = $payfeeOne['order_pid'];
                    $data['order_amt'] = $payfeeOne['order_amt'];
                    $data['order_fee'] = $payfeeOne['order_fee'];
                    $data['order_num'] = $payfeeOne['order_num'];
                    $data['settle_date'] = $payfeeOne['order_date'];
                    $data['order_date'] = ($payfeeOne['order_date'] >= $studyOne['class_stdate']) ? (($payfeeOne['order_date'] >= $studyOne['class_enddate']) ? $studyOne['class_enddate'] : $payfeeOne['order_date']) : $studyOne['class_stdate'];

                    if($payfeeOne['order_paymentprice']==$payfeeOne['order_payamt']){
                        $data['is_confirm']=1;
                    }

                    $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                    if (!$insertorder) {
                        $this->addTemplogInfo($studyOne['companies_id'], $studyOne['agency_id'], '0', 'insert', 'cmb_trans_order', $data);
                    }
                    $count++;
                }
            }

            $isrefund = $this->DataControl->getFieldOne("cmb_trans_order", "order_id", "class_id = '{$studyOne['class_id']}' and student_id = '{$studyOne['student_id']}' and order_type='R'");
            if (!$isrefund) {
                $ispayfee = $this->DataControl->getFieldOne("cmb_trans_order", "order_id,is_confirm", "class_id = '{$studyOne['class_id']}' and student_id = '{$studyOne['student_id']}' and order_type='P'");
                $sql = "select b.dealorder_pid as order_pid
                ,b.dealorder_status
                ,a.dealcourse_time as order_num
                ,a.dealcourse_figure as order_amt
                ,0 as order_fee
                ,FROM_UNIXTIME(b.dealorder_createtime,'%Y-%m-%d') as order_date
                from smc_forward_dealorder_course a
                left join smc_forward_dealorder b on a.dealorder_pid=b.dealorder_pid
                where 1
                and b.companies_id='{$studyOne['companies_id']}'
                and b.school_id='{$studyOne['school_id']}'
                and b.student_id='{$studyOne['student_id']}'
                and a.course_id='{$studyOne['course_id']}'
                and b.dealorder_status>=0
                and b.dealorder_type=0
                order by b.dealorder_createtime desc
                limit 0,1";
                $refundOne = $this->DataControl->selectOne($sql);

                if ($ispayfee && $refundOne && $refundOne['dealorder_status'] == '1') {
                    $data = array();
                    $data['company_id'] = $studyOne['company_id'];
                    $data['companies_id'] = $studyOne['companies_id'];
                    $data['agency_id'] = $studyOne['agency_id'];
                    $data['school_id'] = $studyOne['school_id'];
                    $data['coursetype_id'] = $studyOne['coursetype_id'];
                    $data['coursecat_id'] = $studyOne['coursecat_id'];
                    $data['course_id'] = $studyOne['course_id'];
                    $data['class_id'] = $studyOne['class_id'];
                    $data['student_id'] = $studyOne['student_id'];
                    $data['parent_mobile'] = $studyOne['parenter_mobile'];
                    $data['order_type'] = 'R';
                    $data['order_pid'] = $refundOne['order_pid'];
                    $data['from_order_pid'] = $ispayfee['order_pid'];
                    $data['order_amt'] = $refundOne['order_amt'];
                    $data['order_fee'] = $refundOne['order_fee'];
                    $data['order_num'] = $refundOne['order_num'];
                    $data['settle_date'] = $refundOne['order_date'];
                    $data['order_date'] = ($refundOne['order_date'] >= $studyOne['class_stdate']) ? (($refundOne['order_date'] >= $studyOne['class_enddate']) ? $studyOne['class_enddate'] : $refundOne['order_date']) : $studyOne['class_stdate'];

                    if($ispayfee['is_confirm'] == '1'){
                        $data['is_confirm']=1;
                    }

                    $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                    if (!$insertorder) {
                        $this->addTemplogInfo($studyOne['companies_id'], $studyOne['agency_id'], '0', 'insert', 'cmb_trans_order', $data);
                    }
                    $count++;
                }
            }
        }
        return $count;
    }*/

    function synchroAllOwnInfo(){
        $data = array();
        $data['start_time'] = $this->starttime;
        $data['begin_date'] = date("Y-m-d",$this->starttime);

        $this->addCmbClassInfo($data);//插入班级数据
        $this->addCmbOrderInfo($data);//插入订单数据
        //$this->addCmbLessonInfo($data);//插入课时数据
        //$this->addCmbTransferInfo($data);//插入销课数据
        return true;
    }

    //内部数据处理日志
    function addTemplogInfo($companies_id, $agency_id, $action_status, $action_type, $table_name, $request_info)
    {
        $tempdata = array();
        $tempdata['companies_id'] = $companies_id;
        $tempdata['agency_id'] = $agency_id;
        $tempdata['templog_status'] = $action_status;
        $tempdata['templog_type'] = $action_type;
        $tempdata['templog_table'] = $table_name;
        $tempdata['templog_info'] = json_encode($request_info);
        $tempdata['templog_time'] = time();
        $this->DataControl->insertData('cmb_trans_templog', $tempdata);
    }
}