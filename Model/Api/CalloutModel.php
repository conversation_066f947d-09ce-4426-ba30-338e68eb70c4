<?php

namespace Model\Api;


class CalloutModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $three_ip = '';

    function __construct()
    {
        parent::__construct();
        $this->three_ip = '**************';
    }


    /**
     *  更新取外播记录  有第三方进行推送
     * author: ling
     * 对应接口文档 0001
     */
    function UpdateOutCall($paramArray = array())
    {
        $result = file_get_contents("php://input");
        if ($result) {
            $outCall = json_decode($result, 1);
            $data = array();
            if($outCall){
                $data['outcall_caller'] = $outCall['caller'];
                $data['outcall_called'] = $outCall['called'];
                $data['outcall_begin_time'] = $outCall['obegin_time'];
                $data['outcall_end_time'] = $outCall['end_time'];
                $data['outcall_duration'] = $outCall['duration'];
                $data['outcall_call_type'] = $outCall['call_type'];
                $data['outcall_trunk'] = $outCall['trunk'];
                $data['outcall_uuid'] = $outCall['uuid'];
                $data['outcall_sitecode'] = $outCall['site_code'];
                if($outCall['site_code'] == '124.71.167.58'){
                    $data['outcall_playurl'] = "https://crmcalltatwo.kidcastle.com.cn/atstar/index.php/cdr-cdrplay/mp3play?path=".$outCall['record_path'];
                }else{
                    $data['outcall_playurl'] = "https://crmcalltape.kidcastle.com.cn/atstar/index.php/cdr-cdrplay/mp3play?path=".$outCall['record_path'];
                }
                $data['outcall_feedback_score'] = $outCall['feedback_score'];
                $data['outcall_hangupcause'] = $outCall['hangupcause'];
                $data['outcall_ref_id'] = $outCall['ref_id'];
                $data['outcall_cdr_type'] = $outCall['cdr_type'];
                $data['outcall_fax_result_code'] = $outCall['fax_result_code'];
                $data['outcall_fax_file'] = $outCall['fax_file'];
                $data['outcall_fax_success'] = $outCall['fax_success'];
                $data['fax_image_pixel_size'] = $outCall['fax_image_pixel_size'];
                $data['fax_transferred_pages'] = $outCall['fax_transferred_pages'];
                $data['fax_total_pages'] = $outCall['fax_total_pages'];
                $data['fax_transfer_rate'] = $outCall['fax_transfer_rate'];
//                $data['outcall_json'] = $result;
                $data['outcall_createtime'] = time();
                if ($soundlogID = $this->DataControl->insertData("gmc_outcall", $data)) {
                    $jsondata = array();
                    $jsondata['outcall_id'] = $soundlogID;
                    $jsondata['outcall_json'] = $result;
                    $jsondata['json_createtime'] = time();
                    $this->DataControl->insertData("gmc_outcall_json", $jsondata);

                    return true;
                }else{
                    $data = array();
                    $data['outcall_json'] ='数据库错误.'.$result['outcall_json'];
                    $this->DataControl->insertData("gmc_outcall", $data);
                    return true;
                }
            }else{
                $data = array();
                $data['outcall_json'] = 'json为空';
                $this->DataControl->insertData("gmc_outcall", $data);
                return true;
            }
        }else{
            $data = array();
            $data['outcall_json'] = '参数为空';
            $this->DataControl->insertData("gmc_outcall", $data);
            return true;
        }
    }


    /**
     * 拨号
     * author: ling
     * 对应接口文档 0001
     */
    function sendOutCall($request)
    {
//        $url = "http://{$this->three_ip}/atstar/index.php/status-op?op=dial&ext_no=8001&dia_num=015225968861";

        $url = "http://{$this->three_ip}/atstar/index.php/status-opV2/";
        debug($url);
        $data = array();
        $data['op'] = 'dial';
        $data['ext_no'] = $request['ext_no'];
        $data['dia_num'] = $request['dia_num'];
        $data['dial_uuid'] = $request['dial_uuid'];
//        $data['ext_no'] = '8001';
//        $data['dia_num'] = '015225968861';
        $data = request_by_curl($url, dataEncode($data), 'get');

        var_dump($data);
    }


}

