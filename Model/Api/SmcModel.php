<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 15:21
 */

namespace Model\Api;

class SmcModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $aeskey = 'jidebaosmc%C0515';
    public $aesiv = 'jdbCuBC7orQtDhTO';
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function ThreeVerify($paramArray)
    {
        if(isset($paramArray['is_skip']) && $paramArray['is_skip']==1){
            $data=array();
            if(isset($paramArray['school_branch']) && $paramArray['school_branch']!=''){
                $schoolOne=$this->DataControl->getFieldOne("smc_school","company_id","school_branch='{$paramArray['school_branch']}'");
                $data['company_id']=$schoolOne['company_id'];
            }else{
                $data['company_id']='8888';
            }
            return $data;
        }else{
            if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
                $this->errortip = "请传入授权时间";
                $this->error = true;
                return false;
            }
            /*if($paramArray['timesteps']+60*5 < time() || $paramArray['timesteps']-60 > time()){
                $maxtimes = date("Y-m-d H:i:s",$paramArray['timesteps']+60*5);
                $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性,{$timesteps}--{$jmsting}";
                $this->error = true;
                return false;
            }*/

            $aes = new \Aesencdec($this->aeskey, $this->aesiv);

            $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
            $paramJson = json_decode($xssting, 1);//转化为数组

            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }

            $companyOne = $this->DataControl->selectOne("select company_id from gmc_company
WHERE company_id = '{$paramJson['company_id']}' and company_code = '{$paramJson['company_code']}' limit 0,1");
            if ($companyOne) {
                return $companyOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        }
    }

    function getCompanyProvince($request){

        $sql="select r.region_code,r.region_name,r.region_enname,r.region_shortenname
              from smc_school as s
              left join smc_code_region as r on s.school_province=r.region_id
              where s.company_id='{$request['company_id']}' and s.school_province>0
              group by s.school_province
              order by s.school_province asc
              ";

        $provinceList=$this->DataControl->selectClear($sql);
        if(!$provinceList){
            $this->errortip = '无对应省份信息';
            $this->error = true;
            return false;
        }

        return $provinceList;
    }


    function getCompanyCity($request){

        $datawhere=" 1 ";
        if(isset($request['province_code']) && $request['province_code']!=''){
            $datawhere.=" and r.region_code='{$request['province_code']}'";
        }

        $sql="select r1.region_code,r1.region_name,r1.region_enname,r1.region_shortenname
              from smc_school as s
              left join smc_code_region as r on s.school_province=r.region_id
              left join smc_code_region as r1 on s.school_city=r1.region_id
              where {$datawhere} and s.company_id='{$request['company_id']}' and s.school_city>0
              group by s.school_city
              order by s.school_province asc,s.school_city asc
              ";

        $cityList=$this->DataControl->selectClear($sql);
        if(!$cityList){
            $this->errortip = '无对应城市信息';
            $this->error = true;
            return false;
        }

        return $cityList;
    }

    function getCompanyArea($request){
        $datawhere=" 1 ";
        if(isset($request['city_code']) && $request['city_code']!=''){
            $datawhere.=" and r.region_code='{$request['city_code']}'";
        }

        $sql="select r1.region_code,r1.region_name,r1.region_enname,r1.region_shortenname
              from smc_school as s
              left join smc_code_region as r on s.school_city=r.region_id
              left join smc_code_region as r1 on s.school_area=r1.region_id
              where {$datawhere} and s.company_id='{$request['company_id']}' and s.school_area>0
              group by s.school_area
              order by s.school_city asc,s.school_area asc
              ";

        $areaList=$this->DataControl->selectClear($sql);
        if(!$areaList){
            $this->errortip = '无对应区域信息';
            $this->error = true;
            return false;
        }

        return $areaList;
    }

    function getCompanySchool($request){
        $datawhere="s.company_id='{$request['company_id']}'";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_branch like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_cnname like '%{$request['keyword']}%' or s.school_enname like '%{$request['keyword']}%')";
        }
        if(isset($request['province_code']) && $request['province_code']!=''){
            $datawhere.=" and r1.region_code='{$request['province_code']}'";
        }
        if(isset($request['city_code']) && $request['city_code']!=''){
            $datawhere.=" and r2.region_code='{$request['city_code']}'";
        }
        if(isset($request['area_code']) && $request['area_code']!=''){
            $datawhere.=" and r3.region_code='{$request['area_code']}'";
        }
        if(isset($request['school_istest']) && $request['school_istest']!=''){
            $datawhere.=" and s.school_istest='{$request['school_istest']}'";
        }
        if(isset($request['school_isclose']) && $request['school_isclose']!=''){
            $datawhere.=" and s.school_isclose='{$request['school_isclose']}'";
        }

        $sql="select s.school_branch,s.school_shortname,s.school_cnname,s.school_enname,s.school_address
              from smc_school as s
              left join smc_code_region as r1 on s.school_province=r1.region_id
              left join smc_code_region as r2 on s.school_city=r2.region_id
              left join smc_code_region as r3 on s.school_area=r3.region_id
              where {$datawhere} group by s.school_id order by s.school_id asc";
        $schoolList=$this->DataControl->selectClear($sql);
        if(!$schoolList){
            $this->errortip = '无对应学校信息';
            $this->error = true;
            return false;
        }

        return $schoolList;
    }

    function getCompanyCourse($request){
        $datawhere = "sc.company_id='{$request['company_id']}' and t.coursetype_id=sc.coursetype_id AND c.coursecat_id=sc.coursecat_id";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (sc.course_branch like '%{$request['keyword']}%' or sc.course_cnname like '%{$request['keyword']}%')";
        }
        if(isset($request['coursetype_branch']) && $request['coursetype_branch']!=''){
            $datawhere.=" and t.coursetype_branch='{$request['coursetype_branch']}'";
        }

        if(isset($request['coursecat_branch']) && $request['coursecat_branch']!=''){
            $datawhere.=" and c.coursecat_branch='{$request['coursecat_branch']}'";
        }

        $courseList=$this->DataControl->selectClear("select c.coursecat_branch,sc.course_branch,sc.course_cnname
              FROM smc_course as sc,smc_code_coursecat AS c,smc_code_coursetype AS t
              WHERE {$datawhere} order by sc.coursetype_id ASC,sc.coursecat_id ASC,sc.course_branch ASC");
        if(!$courseList){
            $this->errortip = '无对应班别信息';
            $this->error = true;
            return false;
        }

        return $courseList;
    }

    function getCompanyCoursecat($request){

        $datawhere=" 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.coursecat_branch like '%{$request['keyword']}%' or c.coursecat_cnname like '%{$request['keyword']}%')";
        }
        if(isset($request['coursetype_branch']) && $request['coursetype_branch']!=''){
            $datawhere.=" and t.coursetype_branch='{$request['coursetype_branch']}'";
        }

        $sql="select c.coursecat_branch,c.coursecat_cnname
              from smc_code_coursecat as c
              left join smc_code_coursetype as t on t.coursetype_id=c.coursetype_id
              where {$datawhere} and c.company_id='{$request['company_id']}'
              order by c.coursecat_id asc
              ";

        $catList=$this->DataControl->selectClear($sql);
        if(!$catList){
            $this->errortip = '无对应班种信息';
            $this->error = true;
            return false;
        }

        return $catList;
    }


    function getCompanyClass($request){
        $datawhere="c.company_id='{$request['company_id']}' AND ct.coursetype_isopenclass <> '1'";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_branch like '%{$request['keyword']}%' or c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%')";
        }

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and h.school_branch='{$request['school_branch']}'";
        }else{
            $this->errortip = '校区编号必须传';
            $this->error = true;
            return false;
        }
        if(isset($request['starttime']) && $request['starttime']!=''){
            $datawhere.=" and c.class_stdate>='{$request['starttime']}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $datawhere.=" and c.class_stdate<='{$request['endtime']}'";
        }

        if(isset($request['course_inclasstype']) && $request['course_inclasstype']!=''){
            $datawhere.=" and sc.course_inclasstype='{$request['course_inclasstype']}'";
        }

        if(isset($request['class_type']) && $request['class_type']!=''){
            $datawhere.=" and c.class_type='{$request['class_type']}'";
        }else{
            $datawhere.=" and c.class_type='0'";
        }

        if(isset($request['is_delete']) && $request['is_delete']!=''){
            if($request['is_delete']=='1'){
                $datawhere.=" and c.class_status='-2'";
            }else{
                $datawhere.=" and c.class_status<>'-2'";
            }
        }

        if(isset($request['coursecat_branch']) && $request['coursecat_branch']!=''){
            $datawhere.=" and co.coursecat_branch='{$request['coursecat_branch']}'";
        }

        if(isset($request['course_branch']) && $request['course_branch']!=''){
            $datawhere.=" and sc.course_branch='{$request['course_branch']}'";
        }

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }

        $sql="select c.class_branch,c.class_cnname,c.class_enname,co.coursecat_branch,co.coursecat_cnname,sc.course_branch,sc.course_cnname,c.class_stdate,c.class_enddate,sc.course_classnum
              from smc_class as c
              left join smc_course as sc on c.course_id = sc.course_id
              left join smc_code_coursecat as co on co.coursecat_id=sc.coursecat_id
              left join smc_code_coursetype as ct on ct.coursetype_id=sc.coursetype_id
              left join smc_school as h on h.school_id = c.school_id
              where {$datawhere} ORDER BY c.class_stdate ASC,c.class_enddate ASC";

        $classList=$this->DataControl->selectClear($sql);
        if(!$classList){
            $this->errortip = '无对应班级信息';
            $this->error = true;
            return false;
        }

        return $classList;
    }

    function getCompanyClassStu($request){
        $datawhere=" 1 ";

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }else{
            $this->errortip = '班级编号必须传';
            $this->error = true;
            return false;
        }

        $sql="select s.student_branch,s.student_cnname,s.student_enname,ss.study_beginday,ss.study_endday,ss.study_isreading
              from smc_student_study as ss
              left join smc_student as s on ss.student_id=s.student_id
              left join smc_class as c on c.class_id=ss.class_id
              where {$datawhere} and ss.company_id='{$request['company_id']}'
              order by ss.study_id desc
              ";

        $courseList=$this->DataControl->selectClear($sql);
        if(!$courseList){
            $this->errortip = '无对应班别信息';
            $this->error = true;
            return false;
        }

        return $courseList;
    }

    function getCompanyClassTeacher($request){

        $datawhere=" 1 ";

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }else{
            $this->errortip = '班级编号必须传';
            $this->error = true;
            return false;
        }

        $sql="select st.staffer_branch,st.staffer_cnname,st.staffer_enname,t.teachtype_code,t.teachtype_name,ct.teach_status,ct.teach_type
              from smc_class_teach as ct
              left join smc_class as c on c.class_id=ct.class_id
              left join smc_staffer as st on st.staffer_id=ct.staffer_id
              left join smc_code_teachtype as t on t.teachtype_code=ct.teachtype_code and t.company_id='{$request['company_id']}'
              where {$datawhere} and c.company_id='{$request['company_id']}'
              order by ct.teach_id desc
              ";

        $teacherList=$this->DataControl->selectClear($sql);
        if(!$teacherList){
            $this->errortip = '无对应教师信息';
            $this->error = true;
            return false;
        }

        return $teacherList;

    }

    function getCompanySchoolTeacher($request){
        $datawhere=" 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sh.school_branch='{$request['school_branch']}'";
        }else{
            $this->errortip = '校区编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['postbe_status']) && $request['postbe_status']!=''){
            $datawhere.=" and sp.postbe_status='{$request['postbe_status']}'";
        }

        $sql="select st.staffer_branch,st.staffer_cnname,st.staffer_enname,st.staffer_sex,st.staffer_mobile,st.staffer_pass,sp.postbe_status
              from gmc_staffer_postbe as sp
              left join smc_school as sh on sh.school_id=sp.school_id
              left join smc_staffer as st on st.staffer_id=sp.staffer_id
              where {$datawhere} and sp.company_id='{$request['company_id']}'
              group by sp.staffer_id
              order by postbe_id desc
              ";

        $teacherList=$this->DataControl->selectClear($sql);
        if(!$teacherList){
            $this->errortip = '无对应教师信息';
            $this->error = true;
            return false;
        }

        return $teacherList;
    }

    function getCompanyTeacherClass($request){
        $datawhere=" 1 ";

        if(isset($request['staffer_branch']) && $request['staffer_branch']!=''){
            $datawhere.=" and st.staffer_branch='{$request['staffer_branch']}'";
        }else{
            $this->errortip = '教师编号必须传';
            $this->error = true;
            return false;
        }

        $sql="select st.staffer_branch,st.staffer_cnname,st.staffer_enname,c.class_branch,c.class_cnname,c.class_enname
              from smc_class_hour_teaching as ct
              left join smc_class as c on c.class_id=ct.class_id
              left join smc_staffer as st on st.staffer_id=ct.staffer_id
              where {$datawhere} and c.company_id='{$request['company_id']}'
              group by c.class_id
              order by c.class_id desc
              ";

        $teacherList=$this->DataControl->selectClear($sql);
        if(!$teacherList){
            $this->errortip = '无对应教师代课信息';
            $this->error = true;
            return false;
        }

        return $teacherList;
    }

    function getStafferOne($request){
        $datawhere=" 1 ";

        if($request['identity']=='1'){
            if(isset($request['staffer_branch']) && $request['staffer_branch']!=''){
                $datawhere.=" and st.staffer_branch='{$request['staffer_branch']}'";
            }else{
                $this->errortip = '教师编号必须传';
                $this->error = true;
                return false;
            }

            $sql="select st.staffer_branch,st.staffer_img,st.staffer_cnname,st.staffer_enname,st.staffer_sex,st.staffer_mobile
              from smc_staffer as st where {$datawhere} and st.company_id='{$request['company_id']}'
              limit 0,1";
        }elseif($request['identity']=='2'){
            if(isset($request['mobile']) && $request['mobile']!=''){
                $datawhere.=" and p.parenter_mobile='{$request['mobile']}'";
            }else{
                $this->errortip = '教师编号必须传';
                $this->error = true;
                return false;
            }

            $sql="select p.parenter_cnname,p.parenter_enname,p.parenter_mobile
              from smc_parenter as p where {$datawhere}
              limit 0,1";
        }else{
            $this->errortip = '请选择正确的身份';
            $this->error = true;
            return false;
        }

        $stafferOne=$this->DataControl->selectOne($sql);

        if(!$stafferOne){
            $this->errortip = '无对应教师信息';
            $this->error = true;
            return false;
        }

        return $stafferOne;

    }

    function getCompanyTeacherHour($request){
        $datawhere = "c.company_id = '{$request['company_id']}' and ch.hour_ischecking <> '-1'";
        if(isset($request['staffer_branch']) && $request['staffer_branch']!=''){
            $datawhere.=" and st.staffer_branch='{$request['staffer_branch']}'";
        }else{
            $this->errortip = '教师编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sch.school_branch='{$request['school_branch']}'";
        }

        if(!isset($request['starttime']) && $request['starttime']=='' && !isset($request['endtime']) && $request['endtime']==''){
            $request['starttime'] = date("Y-m-d");
            $request['endtime'] = date("Y-m-d");
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $datawhere.=" and ch.hour_day >= '{$request['starttime']}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $datawhere.=" and ch.hour_day <= '{$request['endtime']}'";
        }

        if(isset($request['hour_ischecking']) && $request['hour_ischecking']!=''){
            $datawhere.=" and ch.hour_ischecking = '{$request['hour_ischecking']}'";
        }

        if(isset($request['course_branch']) && $request['course_branch']!=''){
            $datawhere.=" and co.course_branch = '{$request['course_branch']}'";
        }

        if(isset($request['hour_number']) && $request['hour_number']!=''){
            $datawhere.=" and ch.hour_number = '{$request['hour_number']}'";
        }

        if(isset($request['hour_way']) && $request['hour_way']!=''){
            $datawhere.=" and ch.hour_way = '{$request['hour_way']}'";
        }

        $sql="select ch.hour_id,ch.hour_lessontimes,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,co.course_branch,co.course_cnname
,sch.school_branch,sch.school_cnname,sch.school_shortname,c.class_cnname,c.class_enname
,c.class_branch,ch.hour_way,ch.hour_number,cl.classroom_branch,cl.classroom_cnname,cl.classroom_enname
              from smc_class_hour_teaching as ht
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_course as co on co.course_id=ch.course_id
              left join smc_school as sch on sch.school_id=c.school_id
              where {$datawhere} order by ch.hour_day asc,ch.hour_starttime asc";

        $hourList=$this->DataControl->selectClear($sql);
        if(!$hourList){
            $this->errortip = '无对应教师课表信息';
            $this->error = true;
            return false;
        }else{
            $LineClassModel = new \Model\Smc\LineClassModel($request);
            foreach($hourList as &$hourOne){
                if($hourOne['hour_way'] == '1'){
                    $ClassModel = $LineClassModel->getLineThreenumber($hourOne['hour_id']);
                    if($ClassModel){
                        $hourOne['linerooms_password']=$ClassModel['chairmanpwd'];
                        $hourOne['linerooms_threenumber']=$ClassModel['threenumber'];
                    }else{
                        $hourOne['linerooms_password']='';
                        $hourOne['linerooms_threenumber']=$LineClassModel->errortip;
                    }
                }
            }
        }
        return $hourList;
    }

    function getCompanyClassHour($request){
        $datawhere=" 1 ";

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }else{
            $this->errortip = '班级编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['hour_number']) && $request['hour_number']!=''){
            $datawhere.=" and ch.hour_number='{$request['hour_number']}'";
        }

        if(isset($request['hour_way']) && $request['hour_way']!=''){
            $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
        }

        $sql="select ch.hour_lessontimes,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_way,ch.hour_number,cl.classroom_branch,cl.classroom_cnname,cl.classroom_enname,st.staffer_branch,st.staffer_cnname,st.staffer_enname
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0' and ht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              where {$datawhere} and c.company_id='{$request['company_id']}' and ch.hour_ischecking<>'-1'
              order by ch.hour_lessontimes asc
              ";

        $hourList=$this->DataControl->selectClear($sql);
        if(!$hourList){
            $this->errortip = '无对应班级排课信息';
            $this->error = true;
            return false;
        }

        return $hourList;
    }

    function getCompanyStuClass($request){
        $paramjson = http_build_query($request);
        $data=array();
        $data['querytime'] = "{$request['u']}/{$request['t']}";
        $data['querysql'] = addslashes($paramjson);
        $data['querytime']=time();
        $this->DataControl->insertData("cms_sqlquerytime",$data);

        $datawhere="ss.company_id='{$request['company_id']}'";

        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $datawhere.=" and s.student_branch='{$request['student_branch']}'";
        }else{
            $this->errortip = '学员编号必须传';
            $this->error = true;
            return false;
        }

        $sql="select c.class_branch,c.class_cnname,c.class_enname,ss.study_beginday,ss.study_endday,ss.study_isreading,sc.school_branch,sc.school_cnname,co.course_branch,co.course_cnname
              from smc_student_study as ss
              left join smc_student as s on ss.student_id=s.student_id
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_school as sc on sc.school_id=ss.school_id
              left join smc_course as co on co.course_id=c.course_id
              where {$datawhere} order by ss.study_id desc  ";
        $classList=$this->DataControl->selectClear($sql);


        $roomwhere="s.company_id='{$request['company_id']}'";

        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $roomwhere.=" and s.student_branch='{$request['student_branch']}'";
        }

        $sql="select c.lineclass_branch as class_branch,c.lineclass_cnname as class_cnname,c.lineclass_enname as class_enname
                ,c.lineclass_stdate as study_beginday,c.lineclass_enddate as study_endday,'1' as study_isreading,
                '888888' as school_branch,'吉的堡网校' as school_cnname,co.course_branch,co.course_cnname
              from tkl_lineclass_hourroomstudy as ss
              left join smc_student as s on ss.student_id=s.student_id
              left join tkl_lineclass as c on c.lineclass_id=ss.lineclass_id
              left join smc_course as co on co.course_id=c.course_id
              where {$roomwhere} GROUP BY c.lineclass_id order by ss.hourroomstudy_id desc";
        $roomclassList = $this->DataControl->selectClear($sql);
        if($roomclassList){
            foreach ($roomclassList as $roomclassOne){
                $classList[] = $roomclassOne;
            }
        }

        if(!$classList){
            $this->errortip = '无对应班别信息';
            $this->error = true;
            return false;
        }
        return $classList;
    }

    function getStuEnroll($request){

        $datawhere = "sc.company_id='{$request['company_id']}'";

        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $datawhere.=" and s.student_branch='{$request['student_branch']}'";
        }else{
            $this->errortip = '学生编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql="select sc.school_cnname,sc.school_branch,se.enrolled_status,FROM_UNIXTIME(se.enrolled_createtime, '%Y-%m-%d' ) as enrolled_createtime,se.enrolled_leavetime
              from smc_student_enrolled as se,smc_student as s,smc_school as sc 
              where {$datawhere} and se.student_id=s.student_id and se.school_id=sc.school_id
              group by se.school_id
              ";

        $stuList=$this->DataControl->selectClear($sql);
        if(!$stuList){
            $this->errortip = '无对应学员信息';
            $this->error = true;
            return false;
        }

        foreach($stuList as &$stuOne){
            $stuOne['enrolled_leavetime']=$stuOne['enrolled_leavetime']>0?date("Y-m-d",$stuOne['enrolled_leavetime']):'';
        }

        return $stuList;
    }


    function getCourseList($request){
        $paramjson = http_build_query($request);
        $data=array();
        $data['querytime'] = "{$request['u']}/{$request['t']}";
        $data['querysql'] = addslashes($paramjson);
        $data['querytime']=time();
        $this->DataControl->insertData("cms_sqlquerytime",$data);

        $datawhere=" 1 ";
        if($request['identity']=='1'){
            if(isset($request['staffer_branch']) && $request['staffer_branch']!=''){
                $datawhere.=" and st.staffer_branch='{$request['staffer_branch']}'";
            }else{
                $this->errortip = '教师编号必须传';
                $this->error = true;
                return false;
            }

            $sql="select co.course_branch,co.course_cnname
              from smc_class_hour_teaching as ct
              left join smc_class as c on c.class_id=ct.class_id
              left join smc_staffer as st on st.staffer_id=ct.staffer_id
              left join smc_course as co on co.course_id=c.course_id
              where {$datawhere} and c.company_id='{$request['company_id']}' and ct.teaching_isdel=0
              group by co.course_id
              order by st.staffer_id desc
              ";

            $courseList=$this->DataControl->selectClear($sql);
            if(!$courseList){
                $this->errortip = '无对应课程别信息';
                $this->error = true;
                return false;
            }
        }
        elseif($request['identity']=='2'){
            if(isset($request['student_branch']) && $request['student_branch']!=''){
                $datawhere.=" and s.student_branch='{$request['student_branch']}'";
            }else{
                $this->errortip = '学员编号必须传';
                $this->error = true;
                return false;
            }

            $sql="select co.course_branch,co.course_cnname
              from smc_student_study as ss
              left join smc_student as s on ss.student_id=s.student_id
              left join smc_class as c on c.class_id=ss.class_id
              left join smc_course as co on co.course_id=c.course_id
              where {$datawhere} and ss.company_id='{$request['company_id']}'
              order by ss.study_id desc";
            $courseList = $this->DataControl->selectClear($sql);

            $sql="select co.course_branch,co.course_cnname
              from tkl_lineclass_hourroomstudy as ss
              left join smc_student as s on ss.student_id=s.student_id
              left join tkl_lineclass as c on c.lineclass_id=ss.lineclass_id
              left join smc_course as co on co.course_id=c.course_id
              where {$datawhere} and s.company_id='{$request['company_id']}'
              GROUP BY c.course_id order by ss.hourroomstudy_id desc ";
            $roomcourseList = $this->DataControl->selectClear($sql);
            if($roomcourseList){
                foreach ($roomcourseList as $roomcourseOne){
                    $courseList[] = $roomcourseOne;
                }
            }

            if(!$courseList){
                $this->errortip = '无对应课程别信息';
                $this->error = true;
                return false;
            }
        }else{
            $this->errortip = '请选择正确的身份';
            $this->error = true;
            return false;
        }

        return $courseList;
    }

    function getCompanyStuHour($request){
        $paramjson = http_build_query($request);
        $data=array();
        $data['querytime'] = "{$request['u']}/{$request['t']}";
        $data['querysql'] = addslashes($paramjson);
        $data['querytime']=time();
        $this->DataControl->insertData("cms_sqlquerytime",$data);

        $datawhere = "ss.company_id = '{$request['company_id']}' and ss.study_isreading = '1' and ch.hour_ischecking <> '-1' and ss.study_beginday <= ch.hour_day and ss.study_endday >= ch.hour_day";

        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $datawhere.=" and s.student_branch='{$request['student_branch']}'";
        }else{
            $this->errortip = '学员编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sch.school_branch='{$request['school_branch']}'";
        }

        if(!isset($request['starttime']) && $request['starttime']=='' && !isset($request['endtime']) && $request['endtime']==''){
            $request['starttime']=date("Y-m-d");
            $request['endtime']=date("Y-m-d");
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $datawhere.=" and ch.hour_day>='{$request['starttime']}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $datawhere.=" and ch.hour_day<='{$request['endtime']}'";
        }

        if(isset($request['hour_ischecking']) && $request['hour_ischecking']!=''){
            $datawhere.=" and ch.hour_ischecking='{$request['hour_ischecking']}'";
        }

        if(isset($request['course_branch']) && $request['course_branch']!=''){
            $courseOne=$this->DataControl->getFieldOne("smc_course","course_id","company_id='{$request['company_id']}' and course_branch='{$request['course_branch']}'");
            $datawhere.=" and c.course_id='{$courseOne['course_id']}'";
        }

        if(isset($request['hour_number']) && $request['hour_number']!=''){
            $datawhere.=" and ch.hour_number='{$request['hour_number']}'";
        }

        if(isset($request['hour_way']) && $request['hour_way']!=''){
            $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
        }


        $sql="select ch.hour_id,sch.school_branch,sch.school_cnname,sch.school_shortname,c.class_branch,c.class_cnname,c.class_enname,ch.hour_lessontimes,
ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_ischecking,ch.hour_way,ch.hour_number,cl.classroom_branch,cl.classroom_cnname,
cl.classroom_enname,ch.hour_ischecking,st.staffer_branch,st.staffer_cnname,st.staffer_enname,sc.course_cnname,sc.course_branch
              from smc_class_hour as ch
              left join smc_class as c on c.class_id=ch.class_id
              left join smc_student_study as ss on ss.class_id=c.class_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              left join smc_class_hour_teaching as cht on cht.hour_id=ch.hour_id and cht.teaching_type='0' and cht.teaching_isdel='0'
              left join smc_staffer as st on st.staffer_id=cht.staffer_id
              left join smc_student as s on s.student_id=ss.student_id
              left join smc_school as sch on sch.school_id=ss.school_id
              left join smc_course as sc on sc.course_id=c.course_id
              where {$datawhere}
              group by ch.hour_id
              order by ch.hour_lessontimes ASC";

        $hourList=$this->DataControl->selectClear($sql);

        if($hourList){
            $LineClassModel = new \Model\Smc\LineClassModel($request);
            foreach($hourList as &$hourOne){
                if($hourOne['hour_way'] == '1'){
                    $ClassModel = $LineClassModel->getLineThreenumber($hourOne['hour_id']);
                    if($ClassModel){
                        $hourOne['linerooms_password']=$ClassModel['confuserpwd'];
                        $hourOne['linerooms_threenumber']=$ClassModel['threenumber'];
                    }else{
                        $hourOne['linerooms_password']='';
                        $hourOne['linerooms_threenumber']=$LineClassModel->errortip;
                    }
                }
            }
        }

        //负载风咏网校
        if(isset($request['hour_way']) && $request['hour_way'] == '1'){
            $tklwhere = "s.company_id = '{$request['company_id']}' and h.hourrooms_threenumber <> '' ";

            if(isset($request['student_branch']) && $request['student_branch']!=''){
                $tklwhere.=" and s.student_branch='{$request['student_branch']}'";
            }else{
                $this->errortip = '学员编号必须传';
                $this->error = true;
                return false;
            }

            if(!isset($request['starttime']) && $request['starttime']=='' && !isset($request['endtime']) && $request['endtime']==''){
                $request['starttime'] = date("Y-m-d");
                $request['endtime'] = date("Y-m-d");
            }

            if(isset($request['starttime']) && $request['starttime']!=''){
                $tklwhere.=" and from_unixtime( h.hourrooms_starttime, '%Y-%m-%d' ) >='{$request['starttime']}'";
            }

            if(isset($request['endtime']) && $request['endtime']!=''){
                $tklwhere.=" and from_unixtime( h.hourrooms_starttime, '%Y-%m-%d' ) <='{$request['endtime']}'";
            }

            if(isset($request['hour_ischecking']) && $request['hour_ischecking']!=''){
                $tklwhere.=" and h.hourrooms_ischecking='{$request['hour_ischecking']}'";
            }

            if(isset($request['course_branch']) && $request['course_branch']!=''){
                $courseOne=$this->DataControl->getFieldOne("smc_course","course_id","company_id='{$request['company_id']}' and course_branch='{$request['course_branch']}'");
                $tklwhere.=" and c.course_id='{$courseOne['course_id']}'";
            }

            if(isset($request['class_branch']) && $request['class_branch']!=''){
                $tklwhere.=" and c.lineclass_branch='{$request['class_branch']}'";
            }

            $sql="select 
    '吉的堡网校' as school_cnname, '888888' as school_branch
    ,CONCAT('LC',c.lineclass_branch) as class_branch,c.lineclass_cnname as class_cnname,c.lineclass_enname as class_enname
     ,u.course_cnname,u.course_branch
    ,h.hourrooms_lessontimes as hour_lessontimes,h.hourrooms_threenumber,h.hourrooms_confuserpwd,h.hourrooms_ischecking as hour_ischecking
    ,from_unixtime( h.hourrooms_starttime, '%Y-%m-%d' ) as hour_day,from_unixtime( h.hourrooms_starttime, '%H:%i') as hour_starttime,from_unixtime( h.hourrooms_endtime, '%H:%i') as hour_endtime
              from tkl_lineclass_hourrooms as h
              left join tkl_lineclass as c on h.lineclass_id = c.lineclass_id
              left join smc_course as u on u.course_id = c.course_id
              left join tkl_lineclass_hourroomstudy as d on d.hourrooms_id = h.hourrooms_id
              left join smc_student as s on s.student_id = d.student_id
              where {$tklwhere} order by h.lineclass_id ASC,h.hourrooms_lessontimes ASC";

            $hourroomsList=$this->DataControl->selectClear($sql);
            if(!$hourroomsList){
                $this->errortip = '无对应学员排课信息';
                $this->error = true;
                return false;
            }else{
                foreach ($hourroomsList as $hourroomsOne){
                    $hourroomsOne['linerooms_password'] = $hourroomsOne['hourrooms_confuserpwd'];
                    $hourroomsOne['linerooms_threenumber'] = $hourroomsOne['hourrooms_threenumber'];
                    $hourList[] = $hourroomsOne;
                }
            }
        }

        if(!$hourList){
            $this->errortip = '无对应学员排课信息';
            $this->error = true;
            return false;
        }

        return $hourList;
    }

    function getCompanyStuInfo($request){
        $datawhere="s.student_id = e.student_id AND e.enrolled_isnovalid = '0' AND s.company_id = '{$request['company_id']}'";
        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $datawhere.=" and s.student_branch='{$request['student_branch']}'";
        }else{
            $this->errortip = '学员编号必须传';
            $this->error = true;
            return false;
        }

        $sql="select s.student_branch,s.student_cnname,s.student_enname,s.student_sex,s.student_birthday
from smc_student as s,smc_student_enrolled AS e where {$datawhere} order by s.student_id asc limit 0,1";
        $studentList=$this->DataControl->selectOne($sql);
        if(!$studentList){
            $this->errortip = '无对应家长信息';
            $this->error = true;
            return false;
        }
        return $studentList;
    }

    function getCompanyStuParent($request){
        $datawhere=" 1 ";

        if(isset($request['student_branch']) && $request['student_branch']!=''){
            $datawhere.=" and s.student_branch='{$request['student_branch']}'";
        }else{
            $this->errortip = '学员编号必须传';
            $this->error = true;
            return false;
        }

        $sql="select p.parenter_cnname,p.parenter_enname,p.parenter_mobile,p.parenter_pass,p.parenter_wxtoken,sf.family_relation,sf.family_isdefault
              from smc_student_family as sf
              left join smc_student as s on s.student_id=sf.student_id
              left join smc_parenter as p on p.parenter_id=sf.parenter_id
              where {$datawhere}
              order by p.parenter_id asc
              ";

        $parentList=$this->DataControl->selectClear($sql);
        if(!$parentList){
            $this->errortip = '无对应家长信息';
            $this->error = true;
            return false;
        }
        return $parentList;
    }

    function getCRMStu($request)
    {

        $datawhere = " 1 ";

        $schoolOne=$this->DataControl->getFieldOne("smc_school","school_id","school_branch='{$request['school_branch']}'");

        if(!$schoolOne){
            $this->error = true;
            $this->errortip = "请输入正确校区编号";
            return false;
        }

        $request['school_id']=$schoolOne['school_id'];

        if (isset($request['mobile']) && $request['mobile'] !== '') {
            $request['mobile']=trim($request['mobile']);
            $datawhere .= " and c.client_mobile='{$request['mobile']}'";
        }else{
            $this->error = true;
            $this->errortip = "请输入手机号码";
            return false;
        }

        $datawhere .= " and ccs.school_id='{$request['school_id']}'";

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select sch.school_id,sch.school_cnname,c.client_id,c.client_cnname,c.client_img,c.client_sex,p.parenter_cnname,c.client_mobile as parenter_mobile,c.client_intention_level
              ,(select group_concat(sc.coursecat_cnname) from crm_client_intention as ci  left join smc_code_coursecat as sc on sc.coursecat_id=ci.coursecat_id where ci.client_id=c.client_id group by ci.client_id) as course_cnname
              ,c.client_icard,c.client_img,cf.family_relation,c.client_enname,c.client_birthday
              from crm_client as c
              left join crm_client_family as cf on cf.client_id=c.client_id and cf.family_isdefault = 1
              left join smc_parenter as p on p.parenter_id=cf.parenter_id
              left join crm_client_schoolenter as ccs on ccs.client_id=c.client_id
              left join smc_school as sch on sch.school_id=ccs.school_id
              where {$datawhere} and c.client_tracestatus <> '-1' and c.client_tracestatus <> '4' and c.client_tracestatus <> '-2'
              and c.client_id NOT IN (select s.from_client_id
                                          from smc_student_enrolled as se
                                          left join smc_student as s on s.student_id=se.student_id
                                          where se.school_id='{$request['school_id']}' and s.from_client_id>'0') and c.company_id='{$request['company_id']}'
              group by c.client_id
              order by (case when ccs.school_id='{$request['school_id']}' then 1 else 2 end),c.client_createtime desc
              limit {$pagestart},{$num}
              ";
        $clientList = $this->DataControl->selectClear($sql);
        if (!$clientList) {
            $this->error = true;
            $this->errortip = "无数据";
            return false;
        }
        foreach ($clientList as &$val) {
            $val['client_intention_level'] = (int)$val['client_intention_level'];
            if ($val['school_id'] != $request['school_id']) {
                if ($val['parenter_mobile']) {
                    $val['parenter_mobile_name'] = substr_replace($val['parenter_mobile'], '****', 3, 4);
                }
            } else {
                $val['parenter_mobile_name'] = $val['parenter_mobile'];
            }
        }
        $data = array();
        $count_sql = "select c.client_id
          from crm_client as c
          left join crm_client_family as cf on cf.client_id=c.client_id and cf.family_isdefault=1
          left join smc_parenter as p on p.parenter_id=cf.parenter_id
          left join crm_client_intention as ci on ci.client_id=c.client_id
          left join smc_code_coursecat as sc on sc.coursecat_id=ci.coursecat_id
          left join crm_client_schoolenter as ccs on ccs.client_id=c.client_id
          where {$datawhere} and c.client_tracestatus<>'-1' and c.client_tracestatus<>'4' and c.client_tracestatus<>'-2'
          and c.client_id NOT IN (select s.from_client_id
                                      from smc_student_enrolled as se
                                      left join smc_student as s on s.student_id=se.student_id
                                      where se.school_id='{$request['school_id']}' and s.from_client_id>'0') and c.company_id='{$request['company_id']}'
                                      group by c.client_id";
        $db_nums = $this->DataControl->selectClear($count_sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $data['list'] = $clientList;
        return $data;
    }

    function checkCanEntryClass($request){
        $paramjson = http_build_query($request);
        $data=array();
        $data['querytime'] = "{$request['u']}/{$request['t']}";
        $data['querysql'] = addslashes($paramjson);
        $data['querytime']=time();
        $this->DataControl->insertData("cms_sqlquerytime",$data);

        if(isset($request['class_branch']) && $request['class_branch']!=''){
        }else{
            $this->errortip = '班级编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['hour_lessontimes']) && $request['hour_lessontimes']!=''){
        }else{
            $this->errortip = '课次必须传';
            $this->error = true;
            return false;
        }

        $sql="select ch.hour_day,ch.hour_starttime,ch.hour_endtime
              from smc_class_hour as ch
              left join smc_class as cl on cl.class_id=ch.class_id
              where cl.class_branch='{$request['class_branch']}' and ch.hour_lessontimes='{$request['hour_lessontimes']}'  limit 0,1";
        $hourOne=$this->DataControl->selectOne($sql);
        if(!$hourOne){
            $class_branch = str_replace('LC', '', $request['class_branch']);;
            $sql="select from_unixtime( ch.hourrooms_starttime, '%Y-%m-%d' ) as hour_day,from_unixtime( ch.hourrooms_starttime, '%H:%i') as hour_starttime,from_unixtime( ch.hourrooms_endtime, '%H:%i') as hour_endtime
        from tkl_lineclass_hourrooms as ch
              left join tkl_lineclass as cl on cl.lineclass_id=ch.lineclass_id
              where cl.lineclass_branch ='{$class_branch}' and ch.hourrooms_lessontimes='{$request['hour_lessontimes']}'  limit 0,1";
            $hourOne=$this->DataControl->selectOne($sql);
        }
        if(!$hourOne){
            $this->errortip = '不存在该课时';
            $this->error = true;
            return false;
        }

        $starttime=strtotime($hourOne['hour_day'].' '.$hourOne['hour_starttime']);
        $endtime=strtotime($hourOne['hour_day'].' '.$hourOne['hour_endtime']);

        if($starttime>(time()+1200)){
            $this->errortip = '课程暂未到达上课时间，开课前20分钟可进入教室';
            $this->error = true;
            return false;
        }

        if(time()>$endtime){
            $this->errortip = '请在课程结束前进入教室';
            $this->error = true;
            return false;
        }

        return true;
    }

    function addNewStudent($request)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school","school_id","school_branch='{$request['school_branch']}'");
        if(!$schoolOne){
            $this->error = true;
            $this->errortip = "请输入正确校区编号";
            return false;
        }

        $request['school_id'] = $schoolOne['school_id'];
        if(!isset($request['client_id']) || $request['client_id']==''){
            $this->error = false;
            $this->errortip = "请从CRM学员中选择";
            return false;
        }

        if (isset($request['client_id']) && $request['client_id'] != '') {
            $studentOne = $this->DataControl->selectOne("select student_id from smc_student  where from_client_id='{$request['client_id']}'");
            if ($studentOne) {
                $this->error = true;
                $this->errortip = "CRM学员已进存在学员名单中，请检查学员转正状态！";
                return false;
            }

            $sql="select sf.parenter_id
                  from smc_student_family as sf
                  left join smc_parenter as p on p.parenter_id=sf.parenter_id
                  left join smc_student as st on st.student_id=sf.student_id
                  where p.parenter_mobile='{$request['family_mobile']}' and st.student_cnname='{$request['student_cnname']}' and st.company_id='{$request['company_id']}'
                  ";
            if($this->DataControl->selectOne($sql)){
                $this->error = true;
                $this->errortip = "学员信息已存在，不可以重复增加学员！";
                return false;
            }
        }
        $student_data = array();
        if (isset($request['student_idcard']) && $request['student_idcard'] != '') {
            $stuOne = $this->DataControl->getFieldOne("smc_student", "student_id", "student_idcard='{$request['student_idcard']}' and company_id='{$request['company_id']}'");
            if ($stuOne) {
                $this->error = false;
                $this->errortip = "此身份证已存在，请勿重复添加学员！";
                return false;
            }
        }

        $like = date("Ymd", time());
        $stuInfo = $this->DataControl->selectOne("select student_branch from smc_student
where student_branch like '{$like}%' AND LENGTH(student_branch) = '14' order by student_branch DESC limit 0,1");
        if ($stuInfo) {
            $student_data['student_branch'] = $stuInfo['student_branch'] + 1;
        } else {
            $student_data['student_branch'] = $like . '000001';
        }
        if (isset($request['client_id']) && $request['client_id'] != '') {
            $student_data['from_client_id'] = $request['client_id'];
        }
        $student_data['student_cnname'] = $request['student_cnname'];
        $student_data['student_enname'] = $request['student_enname'];
        $student_data['student_img'] = $request['student_img'];
        $student_data['student_sex'] = $request['student_sex'];
        $student_data['student_idcard'] = trim($request['student_idcard']);
        $student_data['company_id'] = $request['company_id'];
        $student_data['student_birthday'] = $request['student_birthday'];
        $student_data['student_updatatime'] = time();
        $student_data['student_createtime'] = time();
        $student_id = $this->DataControl->insertData("smc_student", $student_data);

        $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id","parenter_mobile='{$request['family_mobile']}'");

        if($parentOne){
            $parenter_id=$parentOne['parenter_id'];
        }else{
            $data=array();
            $data['parenter_mobile']=trim($request['family_mobile']);
            $data['parenter_cnname']=$request['family_cnname'];
            $data['parenter_pass'] = md5(substr($data['parenter_mobile'],-6));
            $data['parenter_bakpass'] = substr($data['parenter_mobile'],-6);
            $data['parenter_addtime']=time();
            $parenter_id=$this->DataControl->insertData("smc_parenter",$data);
        }

        if ($student_id) {
            $data = array();
            $data['student_id'] = $student_id;
            $data['parenter_id'] = $parenter_id;
            $data['family_relation'] = $request['family_relation'];
            $data['family_mobile'] = $request['family_mobile'];
            $data['family_cnname'] = $request['family_cnname'];
            $data['family_isdefault'] = 1;
            $this->DataControl->insertData("smc_student_family", $data);

            $Model = new \Model\Smc\TransactionModel($request);
            $Model->entrySchool($student_id);

            if (isset($request['client_id']) && $request['client_id'] != '') {
                $clientOne = $this->DataControl->getFieldOne("crm_client","client_cnname,client_fromtype","client_id='{$request['client_id']}' and company_id='{$request['company_id']}'");
                $marketerList = $this->DataControl->selectClear("select marketer_id,principal_ismajor from crm_client_principal where client_id='{$request['client_id']}' and school_id='{$request['school_id']}' and  principal_leave=0 group by marketer_id ");
                if ($marketerList) {
                    foreach ($marketerList as $marketerOne) {
                        $data = array();
                        $data['marketer_id'] = $marketerOne['marketer_id'];
                        $data['school_id'] = $request['school_id'];
                        $data['client_id'] = $request['client_id'];
                        $data['student_branch'] = $student_data['student_branch'];
                        $data['conversionlog_ismajor'] = $marketerOne['principal_ismajor'];
                        $data['conversionlog_time'] = time();
                        $this->DataControl->insertData("crm_client_conversionlog", $data);
                    }
                } else {
                    $data = array();
                    $data['client_id'] = $request['client_id'];
                    $data['school_id'] = $request['school_id'];
                    $data['student_branch'] = $student_data['student_branch'];
                    $data['conversionlog_time'] = time();
                    $this->DataControl->insertData("crm_client_conversionlog", $data);
                }

                $stafferOne = $this->DataControl->getFieldOne('smc_staffer', 'staffer_cnname,staffer_id', "staffer_id='{$request['staffer_id']}'");
                $data_track = array();
                $data_track['client_id'] = $request['client_id'];
                $data_track['school_id'] = $request['school_id'];
                $data_track['track_validinc'] = '1';
                $data_track['track_linktype'] = $this->LgStringSwitch('校务系统转正');
                $data_track['track_followmode'] = '3';
                $data_track['track_note'] = $this->LgStringSwitch('由校务系统转正,操作人:' . $stafferOne['staffer_cnname'] . 'ID为' . $stafferOne['staffer_id']);
                $data_track['track_createtime'] = time();
                $this->DataControl->insertData('crm_client_track', $data_track);

                $positiveData = array();
                $positiveData['company_id'] = $request['company_id'];
                $positiveData['school_id'] = $request['school_id'];
                $positiveData['client_id'] = $request['client_id'];
                $positiveData['marketer_name'] = $stafferOne['marketer_name'];
                $positiveData['student_branch'] = $student_data['student_branch'];
                $positiveData['positivelog_time'] = date('Y-m-d');
                $positiveData['positvelog_addtime'] = time();
                $positiveData['positvelog_note'] = $this->LgStringSwitch("从校务系统转正");

                $this->DataControl->insertData('crm_client_positivelog', $positiveData);

                if($clientOne['client_fromtype'] ==1){
                    $data =array();
                    $data['company_id'] = $request['company_id'];
                    $data['tags_class'] = '0';
                    $data['student_id'] = $student_id;
                    $data['tags_name'] =  "园展校";
                    $data['tags_createtime'] =time();
                    $this->DataControl->insertData("smc_student_tags",$data);
                }


                $tem_data = array();
                if($clientOne['client_cnname']!=$request['student_cnname']){
                    $tem_data['client_cnname'] = $request['student_cnname'];
                }
                $tem_data['client_tracestatus'] = 4;
                $tem_data['client_distributionstatus'] = 0;
                $this->DataControl->updateData("crm_client", "client_id='{$request['client_id']}' and company_id='{$request['company_id']}'", $tem_data);
            }
            /*$stuBalcne = $this->DataControl->getOne("smc_student_balance", "student_id='{$student_id}' and school_id='{$request['school_id']}' and company_id='{$request['company_id']}'");
            if (!$stuBalcne) {
                $stuBaData = array();
                $stuBaData['student_id'] = $student_id;
                $stuBaData['school_id'] = $request['school_id'];
                $stuBaData['company_id'] = $request['company_id'];
                $this->DataControl->insertData("smc_student_balance", $stuBaData);
            }*/

            $this->error = true;
            $this->oktip = "学员创建成功";
            return $student_data;
        } else {
            $this->error = false;
            $this->errortip = "学员创建失败";
            return false;
        }
    }


    function getCompanyParentStu($request){
        $datawhere = "sch.company_id='{$request['company_id']}'";

        if(isset($request['mobile']) && $request['mobile']!=''){
            $datawhere.=" and p.parenter_mobile='{$request['mobile']}'";
        }else{
            $this->errortip = '手机号必须传';
            $this->error = true;
            return false;
        }
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sch.school_branch='{$request['school_branch']}'";
        }

        $stuList=$this->DataControl->selectClear("select s.student_branch,s.student_cnname,s.student_enname,s.student_idcard,s.student_sex,s.student_birthday
              ,group_concat(sch.school_branch separator '/') as school_branch,group_concat(sch.school_cnname separator '/') as school_cnname,p.parenter_mobile,s.student_img
              FROM  smc_student_family as sf
              inner join smc_parenter as p on p.parenter_id=sf.parenter_id
              inner join smc_student as s on s.student_id=sf.student_id
              inner join smc_student_enrolled as se on se.student_id=s.student_id
              inner join smc_school as sch on sch.school_id=se.school_id
              where {$datawhere}
              group by s.student_id
              order by sf.family_id ASC");
        if(!$stuList){
            $this->errortip = '无对应学员信息';
            $this->error = true;
            return false;
        }
        return $stuList;
    }

    function getCompanyBrief($request){

        $sql="select company_logo,company_brief
              from gmc_company
              where company_id='{$request['company_id']}'";

        $companyOne=$this->DataControl->selectOne($sql);
        if(!$companyOne){
            $this->errortip = '无对应集团信息';
            $this->error = true;
            return false;
        }

        return $companyOne;
    }

    function getSchoolClassroom($request){
        $datawhere=" 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sch.school_branch='{$request['school_branch']}'";
        }else{
            $this->errortip = '校区编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['classroom_status']) && $request['classroom_status']!=''){
            $datawhere.=" and cl.classroom_status='{$request['classroom_status']}'";
        }

        $sql="select cl.classroom_id,cl.classroom_branch,cl.classroom_cnname
              from smc_classroom as cl
              inner join smc_school as sch on sch.school_id=cl.school_id
              where {$datawhere}";

        $classroomList=$this->DataControl->selectClear($sql);
        if(!$classroomList){
            $this->errortip = '无对应教室信息';
            $this->error = true;
            return false;
        }

        return $classroomList;
    }

    function getSchoolTeacher($request){
        $datawhere=" 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }else{
            $this->errortip = '校区编号必须传';
            $this->error = true;
            return false;
        }

        if(isset($request['staffer_branch']) && $request['staffer_branch']!=''){
            $datawhere.=" and st.staffer_branch='{$request['staffer_branch']}'";
        }

        $sql="select st.staffer_branch,st.staffer_cnname,st.staffer_enname,st.staffer_img,st.staffer_sex,cp.post_name,sc.school_cnname
              from gmc_staffer_postbe as sp
              inner join smc_staffer as st on st.staffer_id=sp.staffer_id
              inner join smc_school as sc on sc.school_id=sp.school_id
              inner join gmc_company_post as cp on cp.post_id=sp.post_id
              where {$datawhere}
              group by st.staffer_id
              ";

        $stafferList=$this->DataControl->selectClear($sql);
        if(!$stafferList){
            $this->errortip = '无对应教师信息';
            $this->error = true;
            return false;
        }

        return $stafferList;
    }

    function getStuCalendar($request){

        if(!isset($request['student_branch']) || $request['student_branch']==''){
            $this->errortip = '学生编号必须传';
            $this->error = true;
            return false;
        }

        if(!isset($request['month']) || $request['month']==''){
            $this->errortip = '月份必须传';
            $this->error = true;
            return false;
        }

        $start=date("Y-m-",strtotime($request['month'])).'01';

        $end=date("Y-m-t",strtotime($request['month']));

        $weekstart=date("w",strtotime($start));
        $weekend=date("w",strtotime($end));

        $start_num=$weekstart;
        $end_num=6-$weekend;

        $starttime=strtotime('-'.$start_num.' day',strtotime($start));
        $endtime=strtotime('+'.$end_num.' day',strtotime($end));

        $time1=$starttime;
        $time2=$endtime;

        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $monarr[] = date('Y-m-d',$time1);

            $time1=strtotime("+1 day",$time1);
        }

        $tem_data=array();

        foreach($monarr as $day){

            $sql="select sh.hourstudy_id
                  from smc_student_hourstudy as sh
                  left join smc_class_hour as ch on ch.hour_id=sh.hour_id
                  left join smc_class as sc on sc.class_id=sh.class_id
                  left join smc_student as st on st.student_id=sh.student_id
                  where sc.company_id='{$request['company_id']}' and ch.hour_day='{$day}' and st.student_branch='{$request['student_branch']}' and ch.hour_way=1
                  ";

            $hourList=$this->DataControl->selectClear($sql);

            $data=array();
            $data['timestr']=$day;
            $data['year']=date("Y",strtotime($day));
            $data['month']=date("m",strtotime($day));
            $data['day']=date("d",strtotime($day));
            $data['num']=$hourList?count($hourList):0;

            if($day<$start || $day>$end){
                if($day<$start){
                    $week='1';
                }else{
                    $week=ceil((date("d",strtotime($end))+$start_num)/7);
                }

            }else{
                $week=ceil((date("d",strtotime($day))+$start_num)/7);
            }

            $tem_data[$week][]=$data;

        }

        return $tem_data;
    }

    function getStafferCalendar($request){
        if(!isset($request['staffer_branch']) || $request['staffer_branch']==''){
            $this->errortip = '职工编号必须传';
            $this->error = true;
            return false;
        }

        if(!isset($request['month']) || $request['month']==''){
            $this->errortip = '月份必须传';
            $this->error = true;
            return false;
        }

        $start=date("Y-m-",strtotime($request['month'])).'01';

        $end=date("Y-m-t",strtotime($request['month']));

        $weekstart=date("w",strtotime($start));
        $weekend=date("w",strtotime($end));

        $start_num=$weekstart;
        $end_num=6-$weekend;

        $starttime=strtotime('-'.$start_num.' day',strtotime($start));
        $endtime=strtotime('+'.$end_num.' day',strtotime($end));

//        $starttime=strtotime(date("Y-m",strtotime($request['month'])).'-01');
//        $endtime=strtotime(date('Y-m-t', strtotime($request['month'])));

        $time1=$starttime;
        $time2=$endtime;

        while( date("Y-m-d",$time1) <= date("Y-m-d",$time2)){
            $monarr[] = date('Y-m-d',$time1);

            $time1=strtotime("+1 day",$time1);
        }

        $tem_data=array();

        foreach($monarr as $day){

            $sql="select ch.hour_id
              from smc_class_hour_teaching as ht
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_class as c on c.class_id=ht.class_id
              left join smc_class_hour as ch on ch.hour_id=ht.hour_id
              where c.company_id='{$request['company_id']}' and st.staffer_branch='{$request['staffer_branch']}' and ch.hour_day='{$day}' and ch.hour_ischecking>='0' and ch.hour_way=1
              ";
            $hourList=$this->DataControl->selectClear($sql);

            $data=array();
            $data['timestr']=$day;
            $data['year']=date("Y",strtotime($day));
            $data['month']=date("m",strtotime($day));
            $data['day']=date("d",strtotime($day));
            $data['num']=$hourList?count($hourList):0;

            if($day<$start || $day>$end){
                if($day<$start){
                    $week='1';
                }else{
                    $week=ceil((date("d",strtotime($end))+$start_num)/7);
                }

            }else{
                $week=ceil((date("d",strtotime($day))+$start_num)/7);
            }

            $tem_data[$week][]=$data;
        }

        return $tem_data;
    }

    function changePwd($request){
        // old_pwd-旧密码  new_pwd-新密码  conf_pwd-确认密码

        if($request['new_pwd']!=$request['conf_pwd']){
            $this->errortip = '新密码与确认密码不符！';
            $this->error = true;
            return false;
        }

        if($request['identity']==1){
            $stafferOne=$this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_pass","staffer_mobile='{$request['mobile']}' and company_id='{$request['company_id']}'");

            if(!$stafferOne){
                $this->errortip = '教师信息不存在！';
                $this->error = true;
                return false;
            }

            if(md5($request['old_pwd'])!=$stafferOne['staffer_pass']){
                $this->errortip = '旧密码错误！';
                $this->error = true;
                return false;
            }

            $data=array();
            $data['staffer_pass']=md5($request['new_pwd']);
            $data['staffer_bakpass']=$request['new_pwd'];
            $data['staffer_updatetime'] = time();

            if($this->DataControl->updateData("smc_staffer","staffer_id='{$stafferOne['staffer_id']}'",$data)){
                return true;
            }else{
                $this->errortip = '数据库错误！';
                $this->error = true;
                return false;
            }

        }elseif($request['identity']==2){
            $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_pass","parenter_mobile='{$request['mobile']}'");

            if(!$parentOne){
                $this->errortip = '家长信息不存在！';
                $this->error = true;
                return false;
            }

            if(md5($request['old_pwd'])!=$parentOne['parenter_pass']){
                $this->errortip = '旧密码错误！';
                $this->error = true;
                return false;
            }

            $data=array();
            $data['parenter_pass']=md5($request['new_pwd']);
            $data['parenter_bakpass']=$request['new_pwd'];

            if($this->DataControl->updateData("smc_parenter","parenter_id='{$parentOne['parenter_id']}'",$data)){
                return true;
            }else{
                $this->errortip = '数据库错误！';
                $this->error = true;
                return false;
            }
        }else{
            $this->errortip = '请选择身份！';
            $this->error = true;
            return false;
        }

    }

    function verifychangePwd($request){
        //new_pwd-新密码

        if($request['identity']==1){
            $stafferOne=$this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_pass","staffer_mobile='{$request['mobile']}' and company_id='{$request['company_id']}'");

            if(!$stafferOne){
                $this->errortip = '教师信息不存在！';
                $this->error = true;
                return false;
            }

            $mobile = trim($request['mobile']);
            $verifycode = trim($request['L_verifycode']);
            $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '网课系统修改密码'", "order by mislog_time DESC");
            if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                $this->errortip = '短信验证码错误！';
                $this->error = true;
                return false;
            }

            $data=array();
            $data['staffer_pass']=md5($request['new_pwd']);
            $data['staffer_bakpass']=$request['new_pwd'];
            $data['staffer_updatetime'] = time();

            if($this->DataControl->updateData("smc_staffer","staffer_id='{$stafferOne['staffer_id']}'",$data)){
                return true;
            }else{
                $this->errortip = '数据库错误！';
                $this->error = true;
                return false;
            }

        }elseif($request['identity']==2){
            $parentOne=$this->DataControl->getFieldOne("smc_parenter","parenter_id,parenter_pass","parenter_mobile='{$request['mobile']}'");

            if(!$parentOne){
                $this->errortip = '家长信息不存在！';
                $this->error = true;
                return false;
            }

            $mobile = trim($request['mobile']);
            $verifycode = trim($request['L_verifycode']);
            $sendrz = $this->DataControl->getFieldOne('gmc_mislog', "mislog_sendcode", "mislog_mobile='{$mobile}' and mislog_tilte = '网课系统修改密码'", "order by mislog_time DESC");
            if (!$sendrz || $sendrz['mislog_sendcode'] !== $verifycode) {
                $this->errortip = '短信验证码错误！';
                $this->error = true;
                return false;
            }

            $data=array();
            $data['parenter_pass']=md5($request['new_pwd']);
            $data['parenter_bakpass']=$request['new_pwd'];

            if($this->DataControl->updateData("smc_parenter","parenter_id='{$parentOne['parenter_id']}'",$data)){
                return true;
            }else{
                $this->errortip = '数据库错误！';
                $this->error = true;
                return false;
            }
        }else{
            $this->errortip = '请选择身份！';
            $this->error = true;
            return false;
        }

    }

    function getSchoolClassHour($request){
        $datawhere=" 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }else{
            $this->errortip = '校区编号必须传';
            $this->error = true;
            return false;
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.class_cnname like '%{$request['keyword']}%' or c.class_enname like '%{$request['keyword']}%' or c.class_branch like '%{$request['keyword']}%')";
        }

        if(isset($request['staffer_branch']) && $request['staffer_branch']!=''){
            $datawhere.=" and st.staffer_branch='{$request['staffer_branch']}'";
        }

        if(isset($request['starttime']) && $request['starttime']!=''){
            $datawhere.=" and ch.hour_day>='{$request['starttime']}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $datawhere.=" and ch.hour_day<='{$request['endtime']}'";
        }

        if(isset($request['fixedtime']) && $request['fixedtime']!=''){
            $datawhere.=" and ch.hour_day='{$request['fixedtime']}'";
        }

        if(isset($request['hour_ischecking']) && $request['hour_ischecking']!=''){
            $datawhere.=" and ch.hour_ischecking='{$request['hour_ischecking']}'";
        }

        if(isset($request['classroom_id']) && $request['classroom_id']!=''){
            $datawhere.=" and cl.classroom_id='{$request['classroom_id']}'";
        }

        if(isset($request['course_inclasstype']) && $request['course_inclasstype']!=''){
            $datawhere.=" and co.course_inclasstype='{$request['course_inclasstype']}'";
        }

        if(isset($request['hour_number']) && $request['hour_number']!=''){
            $datawhere.=" and ch.hour_number='{$request['hour_number']}'";
        }

        if(isset($request['hour_way']) && $request['hour_way']!=''){
            $datawhere.=" and ch.hour_way='{$request['hour_way']}'";
        }

        if(isset($request['class_type']) && $request['class_type']!=''){
            $datawhere.=" and c.class_type='{$request['class_type']}'";
        }

        if(isset($request['course_branch']) && $request['course_branch']!=''){
            $datawhere.=" and co.course_branch='{$request['course_branch']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql="select c.class_branch,c.class_cnname,ch.hour_day,ch.hour_starttime,ch.hour_endtime,ch.hour_lessontimes,cl.classroom_branch,cl.classroom_cnname,st.staffer_cnname,co.course_branch,co.course_cnname,c.class_type
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=ch.class_id and ss.study_beginday<=ch.hour_day and ss.study_endday>=ch.hour_day) as ClassSize
              from smc_class_hour as ch
              inner join smc_class as c on c.class_id=ch.class_id
              inner join smc_school as sc on sc.school_id=c.school_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              inner join smc_course as co on co.course_id=c.course_id
              where {$datawhere} and ch.hour_ischecking<>'-1'
              having ClassSize>0
              limit {$pagestart},{$num}
              ";

        $hourList=$this->DataControl->selectClear($sql);

        if(!$hourList){
            $this->errortip = '暂无班级信息哦~';
            $this->error = true;
            return false;
        }

        $sql="select ch.hour_id
              ,(select count(ss.student_id) from smc_student_study as ss where ss.class_id=ch.class_id and ss.study_beginday<=ch.hour_day and ss.study_endday>=ch.hour_day) as ClassSize
              from smc_class_hour as ch
              inner join smc_class as c on c.class_id=ch.class_id
              inner join smc_school as sc on sc.school_id=c.school_id
              left join smc_class_hour_teaching as ht on ht.hour_id=ch.hour_id and ht.teaching_type='0'
              left join smc_staffer as st on st.staffer_id=ht.staffer_id
              left join smc_classroom as cl on cl.classroom_id=ch.classroom_id
              inner join smc_course as co on co.course_id=c.course_id
              where {$datawhere} and ch.hour_ischecking<>'-1'
              having ClassSize>0";
        $numList=$this->DataControl->selectClear($sql);
        $data=array();
        $data['allNum']=$numList?count($numList):0;
        $data['list']=$hourList;

        return $data;
    }

    function getSchoolStuHour($request){

        $datawhere=" 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }else{
            $this->errortip = '校区编号必须传';
            $this->error = true;
            return false;
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (st.student_cnname like '%{$request['keyword']}%' or st.student_enname like '%{$request['keyword']}%' or st.student_branch like '%{$request['keyword']}%')";
        }

        if(isset($request['class_branch']) && $request['class_branch']!=''){
            $datawhere.=" and c.class_branch='{$request['class_branch']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if(isset($request['fixedtime']) && $request['fixedtime']!=''){
            $datawhere.=" and ss.study_beginday<='{$request['fixedtime']}' and ss.study_endday>='{$request['fixedtime']}' and ch.hour_day='{$request['fixedtime']}'";
        }else{
            $datawhere.=" and ss.study_beginday<=CURDATE() and ss.study_endday>=CURDATE() and ch.hour_day=CURDATE()";
        }

        if(isset($request['course_inclasstype']) && $request['course_inclasstype']!=''){
            $datawhere.=" and co.course_inclasstype='{$request['course_inclasstype']}'";
        }

        if(isset($request['hour_lessontimes']) && $request['hour_lessontimes']!=''){
            $datawhere.=" and ch.hour_lessontimes='{$request['hour_lessontimes']}'";
        }

        $sql="select st.student_cnname,st.student_branch,st.student_img,st.student_sex,c.class_cnname,c.class_branch,ch.hour_lessontimes,co.course_branch,co.course_cnname
              from smc_student_study as ss
              left join smc_class_hour as ch on ch.class_id=ss.class_id
              inner join smc_class as c on c.class_id=ch.class_id
              inner join smc_school as sc on sc.school_id=c.school_id
              inner join smc_student as st on st.student_id=ss.student_id
              inner join smc_course as co on co.course_id=c.course_id
              where {$datawhere}
              limit {$pagestart},{$num}
              ";

        $stuList=$this->DataControl->selectClear($sql);

        if(!$stuList){
            $this->errortip = '暂无学员信息哦~';
            $this->error = true;
            return false;
        }

        $sql="select st.student_id
              from smc_student_study as ss
              left join smc_class_hour as ch on ch.class_id=ss.class_id
              inner join smc_class as c on c.class_id=ch.class_id
              inner join smc_school as sc on sc.school_id=c.school_id
              inner join smc_student as st on st.student_id=ss.student_id
              inner join smc_course as co on co.course_id=c.course_id
              where {$datawhere}";

        $numList=$this->DataControl->selectClear($sql);
        $data=array();
        $data['allNum']=$numList?count($numList):0;
        $data['list']=$stuList;

        return $data;
    }































}