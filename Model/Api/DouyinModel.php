<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/12
 * Time: 22:59
 */

namespace Model\Api;


class DouyinModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

//    //老的账号违规了 更换新的
    public $account_id_old = '7233721564745697337';//抖音来客品牌账户
    public $app_key_old = 'awowpd36m49u50dd';//抖音开放平台 ak
    public $app_secret_old = '896e9088299b0b5283ed69d65b2af49c';//抖音开放平台 sk

//    //新的
    public $account_id = '7501898438489983028';//抖音来客品牌账户
    public $app_key = 'awb7lusub5ou48j1';//抖音开放平台 ak
    public $app_secret = 'cce48c7a998b4325c72680f079171be9';//抖音开放平台 sk

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['accBranch'])) {
            $accOne = $this->DataControl->selectOne("select * from crm_company_douyin_account where company_id = '8888' and account_branch = '{$publicarray['accBranch']}' limit 0,1 ");
            if($accOne) {
                $this->account_id = $accOne['account_accountid'];
                $this->app_key = $accOne['account_appkey'];
                $this->app_secret = $accOne['account_appsecret'];
            }else{
                $this->error = true;
                $this->errortip = "参数信息不存在";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "账号主体编号必传";
            return false;
        }
    }

    //解密抖音加密手机号的名单
    function decryptDyMobile($mobile){
        $base64mobile = base64_decode($mobile);
        $key = $this->app_secret;
        $iv = mb_substr($this->app_secret, -16, 16);

        $decrypted = openssl_decrypt($base64mobile, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        if($decrypted){
            return $decrypted;
        }else{
            return false;
        }
    }

    //生成 抖音名单获取token  client_token
    function getDyClientToken(){
        $nowtime = time();
        $tokenOne = $this->DataControl->selectOne(" select * from crm_company_douyin_token where token_type = 1 and token_accountid = '{$this->account_id}' and token_failuretime > '{$nowtime}' order by token_failuretime desc ");
        if($tokenOne['token_string']){
            $nowtoken = $tokenOne['token_string'];
        }else {
            $param = array(
                'client_key' => $this->app_key, //
                'client_secret' => $this->app_secret, //
                'grant_type' => "client_credential", //
            );
            $header = array();
            $header[] = "Content-type: application/json";

            //POST参数 RAW中JSON方式传值获取结果
//            $getBackJson = request_by_curl("https://open.douyin.com/oauth/client_token/", json_encode($param), "POST", $header);  --- 失败了
            $getBackJson = httpRequest("https://open.douyin.com/oauth/client_token/", json_encode($param), "POST", $header);
//            print_r($getBackJson);die;
            $bakData = json_decode($getBackJson, true);

            if($bakData['message'] == 'success' && $bakData['data']['access_token']){
                //存表
                $record = array();
                $record['token_type'] = 1;
                $record['token_accountid'] = $this->account_id;
                $record['token_string'] = $bakData['data']['access_token'];
                $record['token_failuretime'] = time()+7000;
                $this->DataControl->insertData("crm_company_douyin_token",$record);

                $nowtoken = $bakData['data']['access_token'];
            }
        }
        $this->error = 0;
        $this->errortip = '获取';
        $this->result = $nowtoken;
        return $nowtoken;
    }

    //抖音获取线索（客资）信息
    function getDouyinLeadsApi($request,$page_no=1,$page_size=100,$gmt_start='',$gmt_end=''){
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }

//        $gmt_start = "2024-10-24 13:00:30";
//        $gmt_end = "2024-10-24 14:00:30";

        $somepar = array();
        $somepar['account_id'] = $this->account_id;
        $somepar['start_time'] = $gmt_start?$gmt_start:date("Y-m-d H:i:s",time()-1210); //
        $somepar['end_time'] = $gmt_end?$gmt_end:date("Y-m-d H:i:s",time()-600); //
        $somepar['page'] = $page_no;
        $somepar['page_size'] = $page_size;

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/open_api/crm/clue/query/", dataEncode($somepar), "GET", $header);

        if($bakData = json_decode($getBackJson, true,'512',JSON_BIGINT_AS_STRING)){

            $totalCount = $bakData['data']['page']['total'];
            if($bakData['data']['page']['total'] >= '1' && $bakData['data']['description'] == 'success'){

                $detaInfo = $bakData['data']['clue_data'];
                if($detaInfo){

                    $cluetypeName = array("0"=>"预约表单","1"=>"私信","2"=>"智能电话","7"=>"团购留资");
                    foreach ($detaInfo as $detaInfoVar){

                        //手机号解密
                        $decryptmobile = $this->decryptDyMobile($detaInfoVar['telephone']);
                        $douyinlead_telephone = $decryptmobile?$decryptmobile:$detaInfoVar['telephone'];

                        //是否存储过了
                        $haveOne = $this->DataControl->selectOne(" select 1 from gmc_company_douyinleads where douyinlead_clue_id = '{$detaInfoVar['clue_id']}' limit 0,1 ");
                        if (!$haveOne) {
                            $log = array();
                            $log['company_id'] = '8888';

                            $log['douyinlead_clue_id'] = $detaInfoVar['clue_id'];
                            $log['douyinlead_telephone'] = $douyinlead_telephone;
                            $log['douyinlead_clue_owner_name'] = $detaInfoVar['clue_owner_name'];
                            $log['douyinlead_clue_type'] = $detaInfoVar['clue_type'];
                            $log['douyinlead_action_type'] = $detaInfoVar['action_type'];
                            $log['douyinlead_effective_state'] = $detaInfoVar['effective_state'];
                            $log['douyinlead_create_time_detail'] = $detaInfoVar['create_time_detail'];
                            $log['douyinlead_intention_poi_id'] = $detaInfoVar['intention_poi_id'];
                            $log['douyinlead_intention_life_account_name'] = $detaInfoVar['intention_life_account_name'];
                            $log['douyinlead_follow_poi_id'] = $detaInfoVar['follow_poi_id'];
                            $log['douyinlead_leads_page'] = $detaInfoVar['leads_page'];
                            $log['douyinlead_order_id'] = (string)$detaInfoVar['order_id'];
                            $log['douyinlead_product_id'] = $detaInfoVar['product_id'];
                            $log['douyinlead_product_name'] = $detaInfoVar['product_name'];
                            $log['douyinlead_age'] = $detaInfoVar['age'];
                            $log['douyinlead_gender'] = $detaInfoVar['gender'];
                            $log['douyinlead_address'] = $detaInfoVar['address'];

                            $log['douyinlead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
                            $log['douyinlead_createtime'] = time();
                            if ($leadsId = $this->DataControl->insertData('gmc_company_douyinleads', $log)) {

                                $xw_school_branch = '';
                                if(trim($detaInfoVar['intention_poi_id']) !==''){
                                    $douyinshopOne = $this->DataControl->getFieldOne("gmc_company_douyinshop", "school_branch,douyinshop_poi_name"
                                        , "douyinshop_poi_id = '{$detaInfoVar['intention_poi_id']}' AND school_branch <> '' and company_id = '8888'");
                                    $xw_school_branch = $douyinshopOne['school_branch'];
                                }

                                //名单的主要信息
                                $params = array();
                                $params['company_id'] = 8888;
                                $params['mobile'] = $douyinlead_telephone;
                                $params['buy_time'] = strtotime($detaInfoVar['create_time_detail']);
                                $params['remark'] = "留资组件: {$cluetypeName[$detaInfoVar['clue_type']]};订单编号：{$detaInfoVar['order_id']}；购买的商品： ".$detaInfoVar['product_name'];
                                $params['school_branch'] = $xw_school_branch;
                                $params['action_type'] = $detaInfoVar['action_type'];

                                $school_one = $this->DataControl->selectOne(" select 1 as ishave from smc_school where company_id = '8888' and school_branch = '{$xw_school_branch}' and school_isclose = '0' and school_istest = '0' ");

                                if ($xw_school_branch && $school_one['ishave'] == '1') {//校务系统
                                    //走 校务系统的名单处理
                                    $resultApi = $this->addClient($params);
//                                    print_r($resultApi);

                                    $logone = array();
                                    $logone['douyinlead_client_status'] = $resultApi['status'];
                                    $logone['douyinlead_errortip'] = $resultApi['errtip'];
                                    $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);

                                } elseif($xw_school_branch  && $school_one['ishave'] != '1') {//园务系统

                                    $params['douyinlead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
                                    //走 园务系统的名单处理
                                    request_by_curl("https://kmcapi.kedingdang.com/Api/cmsDouyinLeads", dataEncode($params), "POST");
//                                        $aa = request_by_curl("http://kmcapi.kidmanageapi102.com/Api/cmsDouyinLeads", dataEncode($params), "POST");
//                                        print_r($aa);die;
                                    $logone = array();
                                    $logone['douyinlead_client_status'] = '-2';
                                    $logone['douyinlead_errortip'] = '直营园名单异步通知中！';
                                    $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);

                                }else{//对不上学校 走 校务系统

                                    if($this->account_id == '7501898438489983028'){//主要针对园

                                        $params['douyinlead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
                                        //走 园务系统的名单处理
                                        request_by_curl("https://kmcapi.kedingdang.com/Api/cmsDouyinLeads", dataEncode($params), "POST");
//                                        $aa = request_by_curl("http://kmcapi.kidmanageapi102.com/Api/cmsDouyinLeads", dataEncode($params), "POST");
//                                        print_r($aa);die;
                                        $logone = array();
                                        $logone['douyinlead_client_status'] = '-2';
                                        $logone['douyinlead_errortip'] = '直营园名单异步通知中！';
                                        $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);

                                    }elseif($this->account_id == '7495567362670872602'){//早教和成长
                                        //走 校务系统的名单处理
                                        $resultApi = $this->addClient($params);

                                        $logone = array();
                                        $logone['douyinlead_client_status'] = $resultApi['status'];
                                        $logone['douyinlead_errortip'] = $resultApi['errtip'];
                                        $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);
                                    }else {
                                        //走 校务系统的名单处理
                                        $resultApi = $this->addClient($params);

                                        $logone = array();
                                        $logone['douyinlead_client_status'] = $resultApi['status'];
                                        $logone['douyinlead_errortip'] = $resultApi['errtip'];
                                        $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);
                                    }
                                }
                            }
                        }
                    }

                }

                //判断总数是否大于每次能查到的最大数量
                if($totalCount > $page_no*$page_size){
                    $p = $page_no+1;
                    $num = $page_size;
                    $this->getDouyinLeadsApi($request,$p,$num,$somepar['start_time'],$somepar['end_time']);
                }

            }else{
                $this->error = 1;
                $this->errortip = "暂无数据";
                return false;
            }

        }else{
            $this->error = 1;
            $this->errortip = "数据获取失败";
            return false;
        }

        $this->error = 0;
        $this->errortip = "回调成功！";
        return true;
    }

    //添加来源名单
    function addClient($paramArray){
//        company_id
//        mobile
//        school_branch
//        remark
//        buy_time


        if($paramArray['action_type'] == '2'){
            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame,channel_intention_level", "company_id = '{$paramArray['company_id']}' AND channel_name = '抖音直播间'");
        }else{
            $channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_medianame,channel_intention_level", "company_id = '{$paramArray['company_id']}' AND channel_name = '抖音'");
        }
//        $channelOne = $this->DataControl->getFieldOne("crm_code_channel","channel_id,channel_medianame,channel_intention_level","company_id = '{$paramArray['company_id']}' AND channel_name = '抖音'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname","company_id = '{$paramArray['company_id']}' AND account_class = '1'");
        if($channelOne){
            $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            if ($familyOne) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "检测到在校生家长信息,学员姓名：{$familyOne['student_cnname']}，编号：{$familyOne['student_branch']}，无法添加CRM学员信息！";
                return $result;
            }
            if (!checkMobile($paramArray['mobile'])) {
                $result = array();
                $result['status'] = "0";
                $result['errtip'] = "手机号码加密，无法获取准确手机号，暂不处理！";
                return $result;
            }

            if($paramArray['school_branch'] !=''){
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}' and school_isclose = '0' and school_istest = '0' ");
                if(!$schoolOne){
//                    $result = array();
//                    $result['status'] = "0";
//                    $result['errtip'] = "校区编号{{$paramArray['school_branch']}}不存在，请检查校区编号！";
//                    return $result;
                    $schoolOne['school_id'] = 0;
                }
            }else{
                $schoolOne['school_id'] = 0;
            }

            $impotParam = array();
            $impotParam['company_id'] = $paramArray['company_id'];
            if(isset($schoolOne['school_id'])){
                $impotParam['school_id'] = $schoolOne['school_id'];
            }

            $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
            $paramOne['company_id'] = $paramArray['company_id'];
            $paramOne['client_cnname'] = '抖音店铺用户';
            $paramOne['channel_id'] = $channelOne['channel_id'];
            $paramOne['client_source'] = $channelOne['channel_medianame'];
            $paramOne['client_remark'] = $paramArray['remark'];
            $paramOne['client_isfromgmc'] = '1';
            $paramOne['client_mobile'] = $paramArray['mobile'];
            $paramOne['client_isnewtip'] = '1';
            $paramOne['client_updatetime'] = $paramArray['buy_time'];
            $paramOne['client_createtime'] = $paramArray['buy_time'];
            $paramOne['school_id'] = $schoolOne['school_id']?$schoolOne['school_id']:'0';
            if (!$ClientVerify->CrmClientVerify($paramOne)) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = $ClientVerify->errortip;
                return $result;
            }

            //客户不存在
            if(!$this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}'")){
                $data = array();
                $data['client_cnname'] = '抖音店铺用户';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['company_id'] = $paramArray['company_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_remark'] = $paramArray['remark'];
                $data['client_isfromgmc'] = '1';
                $data['client_mobile'] = $paramArray['mobile'];
                if($channelOne['channel_intention_level'] > 0){
                    $data['client_intention_level'] = $channelOne['channel_intention_level'];
                    $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
                }
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $data['client_createtime'] = $paramArray['buy_time'];
                $client_id = $this->DataControl->insertData("crm_client", $data);

                //添加名单状态记录
                $Model = new  \Model\Api\CalloutModel($paramOne);
                $Model->addClientTimerecord($paramArray['company_id'],$schoolOne['school_id'],$client_id,1,-1,"抖音三方对接录入");

                if ($schoolOne['school_id'] > 0 ) {
                    $datas = array();
                    $datas['client_id'] = $client_id;
                    $datas['school_id'] = $schoolOne['school_id'];
                    $datas['company_id'] = $paramArray['company_id'];
                    $datas['schoolenter_createtime'] = time();
                    $datas['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $datas);
                }
                $trackData = array();
                $trackData['client_id'] = $client_id;
                $trackData['marketer_id'] = '0';
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入（抖音店铺用户）,系统新建客户信息;";
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单创建成功！";
                return $result;
            }

            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "名单通过校验，但未正常激活！";
            return $result;
        }else{
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "未检测到阿里口碑店铺渠道无法处理";
            return $result;
        }
    }

    //获取抖音商户 门店信息
    function upDouyinShopApi($request,$page_no=1,$page_size=100){
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }

        $param = array(
            'account_id' => $this->account_id, //
            'page' => $page_no, //
            'size' => $page_size, //
        );
        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
//        $getBackJson = request_by_curl("https://open.douyin.com/goodlife/v1/open_api/crm/clue/query/", dataEncode($param), "GET", $header);
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/shop/poi/query/", dataEncode($param), "GET", $header);

        if($bakData = json_decode($getBackJson, true)){

            $totalCount = $bakData['data']['total'];
            if($bakData['data']['total'] >= '1' && $bakData['data']['description'] == 'success') {
                $detaInfo = $bakData['data']['pois'];

                if ($detaInfo) {

                    foreach($detaInfo as $douyinOne){
                        if(!$this->DataControl->getFieldOne("gmc_company_douyinshop","douyinshop_id","douyinshop_poi_id = '{$douyinOne['poi']['poi_id']}'")){
                            $log = array();
                            $log['company_id'] = '8888';
                            $log['douyinshop_account_id'] = $douyinOne['root_account']['account_id'];
                            $log['douyinshop_account_name'] = $douyinOne['root_account']['account_name'];
                            $log['douyinshop_account_type'] = $douyinOne['root_account']['account_type'];

                            $log['douyinshop_poi_id'] = $douyinOne['poi']['poi_id'];
                            $log['douyinshop_poi_name'] = $douyinOne['poi']['poi_name'];
                            $log['douyinshop_address'] = $douyinOne['poi']['address'];
                            $log['douyinshop_latitude'] = $douyinOne['poi']['latitude'];
                            $log['douyinshop_longitude'] = $douyinOne['poi']['longitude'];
                            $this->DataControl->insertData('gmc_company_douyinshop', $log);
                        }else{
                            $log = array();
                            $log['douyinshop_account_id'] = $douyinOne['root_account']['account_id'];
                            $log['douyinshop_account_name'] = $douyinOne['root_account']['account_name'];
                            $log['douyinshop_account_type'] = $douyinOne['root_account']['account_type'];

                            $log['douyinshop_poi_name'] = $douyinOne['poi']['poi_name'];
                            $log['douyinshop_address'] = $douyinOne['poi']['address'];
                            $log['douyinshop_latitude'] = $douyinOne['poi']['latitude'];
                            $log['douyinshop_longitude'] = $douyinOne['poi']['longitude'];
                            $this->DataControl->updateData('gmc_company_douyinshop',"douyinshop_poi_id = '{$douyinOne['poi']['poi_id']}'", $log);
                        }
                    }

                }

                //判断总数是否大于每次能查到的最大数量
                if($totalCount > $page_no*$page_size){
                    $p = $page_no+1;
                    $num = $page_size;
                    $this->upDouyinShopApi($request,$p,$num);
                }

                $this->error = 0;
                $this->errortip = '同步完成';
                return true;
            }else{
                $this->error = 1;
                $this->errortip = $bakData['data']['description'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }

    }

    //获取抖音订单信息
    function getDouyinOrderApi($request,$page_no=1,$page_size=100,$gmt_start='',$gmt_end='',$isupda=1){
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }

//        $gmt_start = "**********";//"2024-11-13 15:45:00"; **********
//        $gmt_end = "**********";//"2024-11-13 15:45:50"; **********

        $somepar = array();
        $somepar['account_id'] = $this->account_id;
        $somepar['page_num'] = $page_no;
        $somepar['page_size'] = $page_size;

        if(isset($request['order_order_id']) && $request['order_order_id'] != '' ){//---------- 更新数据
            $somepar['order_id'] = $request['order_order_id']; //单个订单查询
        }elseif(isset($request['isupdate']) && $request['isupdate'] == '1' ){//---------- 更新数据
            $somepar['update_order_start_time'] = $gmt_start?$gmt_start:time()-605; //
            $somepar['update_order_end_time'] = $gmt_end?$gmt_end:time(); //
        }else{
            $somepar['create_order_start_time'] = $gmt_start?$gmt_start:time()-605; //
            $somepar['create_order_end_time'] = $gmt_end?$gmt_end:time(); //
        }

//        $somepar['create_order_start_time'] = $gmt_start?$gmt_start:time()-1201; //
//        $somepar['create_order_end_time'] = $gmt_end?$gmt_end:time()-600; //

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/trade/order/query/", dataEncode($somepar), "GET", $header);
//        print_r($getBackJson);die;

//        //单个订单详情打印
//        if(isset($request['order_order_id']) && $request['order_order_id'] != '' ) {
////            print_r($getBackJson);die;
//        }
        if($bakData = json_decode($getBackJson, true)){
            //总数
            $totalCount = $bakData['data']['page']['total'];

            if($bakData['data']['error_code'] == '0' && $bakData['data']['page']['total'] >= '1') {
                $detaInfo = $bakData['data']['orders'];
                if ($detaInfo) {
                    foreach($detaInfo as $douyinOne){
                        //0初始化、100待支付、101订单关闭 这些不获取
                        if($douyinOne['order_status'] == '0' || $douyinOne['order_status'] == '100' || $douyinOne['order_status'] == '101'){
                            continue;
                        }

                        $haveOne = $this->DataControl->getFieldOne("gmc_company_douyin_order","order_id","order_order_id = '{$douyinOne['order_id']}'");
                        if(!$haveOne){
                            $log = array();
                            $log['company_id'] = $request['company_id']?$request['company_id']:'8888';
                            $log['channel_branch'] = 'douyin';//1 抖音
                            $log['order_accountid'] = $this->account_id;//

                            $log['order_open_id'] = $douyinOne['open_id'];
                            $log['order_contacts_phone'] = $douyinOne['contacts'][0]['phone'];
                            $log['order_order_id'] = $douyinOne['order_id'];
                            $log['order_count'] = $douyinOne['count'];

                            $log['order_discount_amount'] = $douyinOne['discount_amount'];//------  折扣金额，单位分
                            $log['order_payment_discount'] = $douyinOne['payment_discount'];//--- 支付优惠。本字段将于2024-03-31 日服务商进行下线。
                            $log['order_pay_amount'] = $douyinOne['pay_amount'];//--- 实付金额，单位分。本字段将于2024-03-31 日对服务商进行下线。
                            $log['order_receipt_amount'] = $douyinOne['receipt_amount'];//------ 订单实收金额，即pay_amount+payment_discount

                            $log['order_pay_time'] = $douyinOne['pay_time'];
                            $log['order_create_order_time'] = $douyinOne['create_order_time'];
                            $log['order_update_order_time'] = $douyinOne['update_order_time'];

                            $log['order_order_status'] = $douyinOne['order_status'];
                            $log['order_order_type'] = $douyinOne['order_type'];

                            $log['order_original_amount'] = $douyinOne['original_amount'];
                            $log['order_sku_id'] = $douyinOne['sku_id'];
                            $log['order_sku_name'] = $douyinOne['sku_name'];

                            $log['order_createtime'] = time();
                            $orderID = $this->DataControl->insertData('gmc_company_douyin_order', $log);

                            if($orderID){
                                $loga = array();
                                $loga['order_id'] = $orderID;
                                $loga['json_json'] = json_encode($douyinOne, JSON_UNESCAPED_UNICODE);
                                $loga['json_createtime'] = time();
                                $orderJsonID = $this->DataControl->insertData('gmc_company_douyin_order_json', $loga);
                            }

                            if($orderID && $douyinOne['certificate']){
                                foreach($douyinOne['certificate'] as $certificateVar){
                                    $cer_log = array();
                                    $cer_log['company_id'] = $request['company_id']?$request['company_id']:'8888';

                                    $cer_log['order_id'] = $orderID;
                                    $cer_log['order_order_id'] = $douyinOne['order_id'];
                                    $cer_log['channel_branch'] = 'douyin';

                                    if($certificateVar['item_status'] == '100'){
                                        $cer_log['certificate_status'] = 1;//未使用
                                    }elseif($certificateVar['item_status'] == '200' || $certificateVar['item_status'] == '201' || $certificateVar['item_status'] == '400' || $certificateVar['item_status'] == '401'){
                                        $cer_log['certificate_status'] = 2;//已使用
                                    }elseif($certificateVar['item_status'] == '300' || $certificateVar['item_status'] == '301'){
                                        $cer_log['certificate_status'] = 4;//退款成功
                                    }else{
                                        $cer_log['certificate_status'] = 0;//初始状态
                                    }

                                    $cer_log['certificate_certificate_id'] = $certificateVar['certificate_id'];
                                    $cer_log['certificate_item_status'] = $certificateVar['item_status'];
                                    $cer_log['certificate_order_item_id'] = $certificateVar['order_item_id'];
                                    $cer_log['certificate_combination_id'] = $certificateVar['combination_id'];

                                    $cer_log['certificate_refund_time'] = $certificateVar['refund_time'];
                                    $cer_log['certificate_refund_amount'] = $certificateVar['refund_amount'];
                                    $cer_log['certificate_item_update_time'] = $certificateVar['item_update_time'];

                                    $cer_log['certificate_createtime'] = time();
                                    $certificateID = $this->DataControl->insertData('gmc_company_douyin_order_certificate', $cer_log);

                                    if($certificateID){
                                        $loga = array();
                                        $loga['certificate_id'] = $certificateID;
                                        $loga['json_json'] = json_encode($certificateVar, JSON_UNESCAPED_UNICODE);
                                        $loga['json_createtime'] = time();
                                        $ordercjsonID = $this->DataControl->insertData('gmc_company_douyin_order_certificate_json', $loga);
                                    }
                                }
                            }
                        }else{
                            if($isupda) {
                                $log = array();
                                $log['order_open_id'] = $douyinOne['open_id'];
                                $log['order_contacts_phone'] = $douyinOne['contacts'][0]['phone'];
                                $log['order_count'] = $douyinOne['count'];

                                $log['order_discount_amount'] = $douyinOne['discount_amount'];//------  折扣金额，单位分
                                $log['order_payment_discount'] = $douyinOne['payment_discount'];//--- 支付优惠。本字段将于2024-03-31 日服务商进行下线。
                                $log['order_pay_amount'] = $douyinOne['pay_amount'];//--- 实付金额，单位分。本字段将于2024-03-31 日对服务商进行下线。
                                $log['order_receipt_amount'] = $douyinOne['receipt_amount'];//------ 订单实收金额，即pay_amount+payment_discount

                                $log['order_pay_time'] = $douyinOne['pay_time'];
                                $log['order_create_order_time'] = $douyinOne['create_order_time'];
                                $log['order_update_order_time'] = $douyinOne['update_order_time'];

                                $log['order_order_status'] = $douyinOne['order_status'];
                                $log['order_order_type'] = $douyinOne['order_type'];

                                $log['order_original_amount'] = $douyinOne['original_amount'];
                                $log['order_sku_id'] = $douyinOne['sku_id'];
                                $log['order_sku_name'] = $douyinOne['sku_name'];
                                $log['order_updatetime'] = time();
                                $this->DataControl->updateData('gmc_company_douyin_order', "order_id = '{$haveOne['order_id']}'", $log);

                                if ($haveOne && $douyinOne['certificate']) {
                                    foreach ($douyinOne['certificate'] as $certificateVar) {
                                        //判断券有没有
                                        $cid = $this->DataControl->selectOne(" select certificate_id from gmc_company_douyin_order_certificate where company_id = '8888' and certificate_certificate_id = '{$certificateVar['certificate_id']}' ");
                                        if ($cid > 1) {
                                            $cer_log = array();
                                            $cer_log['order_id'] = $haveOne['order_id'];
                                            $cer_log['order_order_id'] = $douyinOne['order_id'];

                                            if ($certificateVar['item_status'] == '100') {
                                                $cer_log['certificate_status'] = 1;//未使用
                                            } elseif ($certificateVar['item_status'] == '200' || $certificateVar['item_status'] == '201' || $certificateVar['item_status'] == '400' || $certificateVar['item_status'] == '401') {
                                                $cer_log['certificate_status'] = 2;//已使用
                                            } elseif ($certificateVar['item_status'] == '300' || $certificateVar['item_status'] == '301') {
                                                $cer_log['certificate_status'] = 4;//退款成功
                                            } else {
                                                $cer_log['certificate_status'] = 0;//初始状态
                                            }

                                            $cer_log['certificate_certificate_id'] = $certificateVar['certificate_id'];
                                            $cer_log['certificate_item_status'] = $certificateVar['item_status'];
                                            $cer_log['certificate_order_item_id'] = $certificateVar['order_item_id'];
                                            $cer_log['certificate_combination_id'] = $certificateVar['combination_id'];

                                            $cer_log['certificate_refund_time'] = $certificateVar['refund_time'];
                                            $cer_log['certificate_refund_amount'] = $certificateVar['refund_amount'];
                                            $cer_log['certificate_item_update_time'] = $certificateVar['item_update_time'];

                                            $cer_log['certificate_updatetime'] = time();
                                            $this->DataControl->updateData('gmc_company_douyin_order_certificate', "certificate_certificate_id = '{$certificateVar['certificate_id']}'", $cer_log);
                                        } else {
                                            $cer_log = array();
                                            $cer_log['company_id'] = $request['company_id'] ? $request['company_id'] : '8888';

                                            $cer_log['order_id'] = $haveOne['order_id'];
                                            $cer_log['order_order_id'] = $douyinOne['order_id'];
                                            $cer_log['channel_branch'] = 'douyin';

                                            if ($certificateVar['item_status'] == '100') {
                                                $cer_log['certificate_status'] = 1;//未使用
                                            } elseif ($certificateVar['item_status'] == '200' || $certificateVar['item_status'] == '201' || $certificateVar['item_status'] == '400' || $certificateVar['item_status'] == '401') {
                                                $cer_log['certificate_status'] = 2;//已使用
                                            } elseif ($certificateVar['item_status'] == '300' || $certificateVar['item_status'] == '301') {
                                                $cer_log['certificate_status'] = 4;//退款成功
                                            } else {
                                                $cer_log['certificate_status'] = 0;//初始状态
                                            }

                                            $cer_log['certificate_certificate_id'] = $certificateVar['certificate_id'];
                                            $cer_log['certificate_item_status'] = $certificateVar['item_status'];
                                            $cer_log['certificate_order_item_id'] = $certificateVar['order_item_id'];
                                            $cer_log['certificate_combination_id'] = $certificateVar['combination_id'];

                                            $cer_log['certificate_refund_time'] = $certificateVar['refund_time'];
                                            $cer_log['certificate_refund_amount'] = $certificateVar['refund_amount'];
                                            $cer_log['certificate_item_update_time'] = $certificateVar['item_update_time'];

                                            $cer_log['certificate_createtime'] = time();
                                            $certificateID = $this->DataControl->insertData('gmc_company_douyin_order_certificate', $cer_log);

                                            if ($certificateID) {
                                                $loga = array();
                                                $loga['certificate_id'] = $certificateID;
                                                $loga['json_json'] = json_encode($certificateVar, JSON_UNESCAPED_UNICODE);
                                                $loga['json_createtime'] = time();
                                                $ordercjsonID = $this->DataControl->insertData('gmc_company_douyin_order_certificate_json', $loga);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //判断总数是否大于每次能查到的最大数量
                if($totalCount > $page_no*$page_size){
                    $p = $page_no+1;
                    $num = $page_size;
                    $this->getDouyinOrderApi($request,$p,$num,$somepar['create_order_start_time'],$somepar['create_order_end_time']);
                }

            }else{
                $this->error = 1;
                $this->errortip = $bakData['data']['description'].'-'.$bakData['data']['error_code'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }

        $this->error = 0;
        $this->errortip = '同步完成';
        return true;
    }

    //获取抖音订单 券码准备接口
    function upDouyinCertificatePlanApi($request){
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }
//        $request['code'] = '103749297716546';
//        $request['encrypted_url'] = 'https://v.douyin.com/iAVmsa11/';//'https://v.douyin.com/iAVPS8jA/';//https://v.douyin.com/iA9KJFXU/

//        $code = '';
//        $encrypted_data = '';
        if($request['encrypted_url'] != '' && $request['encrypted_url'] != '0'){
            $getUrlJson = request_by_curl($request['encrypted_url'], '', "GET");
            if($getUrlJson){
                $getUrlJsonOne = explode('object_id=',$getUrlJson);
                $getUrlJsonTwo = explode('&',$getUrlJsonOne['1']);
                $encrypted_data = $getUrlJsonTwo['0'];
            } //I2tnUHVwdkpYTlBxbTBScU4xS0JZVFJEMkhDZ05jOXdqSkNjdWFJY25XL1RWcWhIOGZYU2o4UDZ2d0hOeUxsOWQ0OFRFQjZiQmNHbE55blExUkFmblRyL2ZkZDdYZ2RBZTBWKzlaeUtvRHhLeDllNVZrOTAzN0UvWXpNSGtQSUlhNG9LMXVXVGpHMEkyY3BMQm5VeXdPSnR0L0xzcmhyV1BLWXNrT2h3VWhTbUFTdz09
        }elseif($request['code'] != '' && $request['code'] != '0' ){
            $code = $request['code'];
        }else{
            $this->error = 1;
            $this->errortip = " 券码信息必须上传一种";
            return false;
        }

        $somepar = array();
        $somepar['code'] = $code;//本字段券码明文    权码ID： 7436666830934196233   券码明文：103749297716546
        $somepar['encrypted_data'] = $encrypted_data;//从二维码解析出来的标识 (传参前需要先进行URL编码，注意不要有空格) -- 二维码扫出来的短链：https://v.douyin.com/iA9KJFXU/
        $somepar['poi_id'] = "";//7019677913964349477

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/fulfilment/certificate/prepare/", dataEncode($somepar), "GET", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true,'512',JSON_BIGINT_AS_STRING)) {
            if($bakData['data']['error_code'] == '0' && $bakData['data']['description'] == 'success') {
                if($bakData['data']['verify_token'] && $bakData['data']['order_id']){

                    $order_id = $bakData['data']['order_id'];
                    $verify_token = $bakData['data']['verify_token'];
                    $encrypted_code = [];
//                print_r($bakData['data']['certificates']);die;
                    //券信息
                    if($bakData['data']['certificates']){
                        foreach ($bakData['data']['certificates'] as $key=>$certificatesVar){
                            $encrypted_code[$key] = $certificatesVar['encrypted_code'];
                        }
                    }

                    $result = array();
                    $result['order_id'] = $order_id;
                    $result['verify_token'] = $verify_token;
                    $result['encrypted_codes'] = $encrypted_code;
                    $result['certificate_certificate_id'] = $bakData['data']['certificates'][0]['certificate_id'];
                    $result['certificates_json'] = json_encode($bakData['data']['certificates'], JSON_UNESCAPED_UNICODE);
//print_r($result);die;
                    $this->error = "0";
                    $this->errortip = "准备成功";
                    return $result;
                }else{
                    $this->error = "1";
                    $this->errortip = "准备失败";
                    return false;
                }
            }else{
                $this->error = "1";
                $this->errortip = "准备失败";
                return false;
            }
        }else{
            $this->error = "1";
            $this->errortip = "准备失败";
            return false;
        }

    }

    //抖音获取 核销 - 验券
    function getDouyinCertificateVerifyApi($request){
        if(!$request['school_branch']){
            $this->error = 1;
            $this->errortip = "请选择学校编号";
            return false;
        }
        //！！！！！！！
        $shopOne = $this->DataControl->selectOne(" select douyinshop_poi_id as shop_poi_id from gmc_company_douyinshop where school_branch = '{$request['school_branch']}' ");
        if(!$shopOne['shop_poi_id']){
            $this->error = 1;
            $this->errortip = "请选择先设置门店和学校的对照关系";
            return false;
        }
        if(!$request['feeitem_id']){
            $this->error = 1;
            $this->errortip = "请传入收费项目";
            return false;
        }
        if(!$request['student_cnname']){
            $this->error = 1;
            $this->errortip = "请传入幼儿姓名";
            return false;
        }
        if(!$request['student_branch']){
            $this->error = 1;
            $this->errortip = "请传入幼儿编号";
            return false;
        }
        if(!$request['code'] && !$request['encrypted_url']){
            $this->error = 1;
            $this->errortip = "用户端券码信息必须传一个";
            return false;
        }
        if(!$request['verify_num']){
            $request['verify_num'] = 1;
        }

        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }
        //验券准备
        $PlanData = $this->upDouyinCertificatePlanApi($request);//code  encrypted_url
        if(!$PlanData || !$PlanData['encrypted_codes'] || $PlanData['verify_token'] == ''){
            $this->error = 1;
            $this->errortip = "验券准备失败（参数操作）";
            return false;
        }

        //看核销几张券
        $encrypted_codes = array_slice($PlanData['encrypted_codes'],0,$request['verify_num']);

        $somepar = array();
        $somepar['verify_token'] = $PlanData['verify_token'];
        $somepar['encrypted_codes'] = $encrypted_codes;
        $somepar['poi_id'] = $shopOne['shop_poi_id'];

//        print_r($PlanData);
//        print_r($somepar);
//        die;

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/fulfilment/certificate/verify/", json_encode($somepar,JSON_UNESCAPED_UNICODE), "POST", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true)){

            if($bakData['data']['error_code'] == '0' && $bakData['data']['description'] == 'success') {

                $detaInfo = $bakData['data']['verify_results'];
                if ($detaInfo) {
                    foreach($detaInfo as $douyinOne){
                        $log = array();
                        $log['company_id'] = $request['company_id']?$request['company_id']:'8888';

                        $log['verify_prepare_order_id'] = $PlanData['order_id'];
                        $log['verify_prepare_verify_token'] = $PlanData['verify_token'];
                        $log['verify_prepare_encrypted_code'] = json_encode($PlanData['encrypted_codes'], JSON_UNESCAPED_UNICODE);
                        $log['verify_prepare_json'] = $PlanData['certificates_json'];

                        if($douyinOne['result'] == '0'){//||
                            $log['verify_state'] = 1;//成功
                        }elseif($douyinOne['result'] == '1208' || $douyinOne['result'] == '2'){
                            $log['verify_state'] = 2;// 重复验券成功
                        } else{
                            $log['verify_state'] = 3;//失败
                        }

                        $log['verify_poi_id'] = $shopOne['shop_poi_id'];
                        $log['verify_code'] = $douyinOne['code'];
                        $log['verify_verify_id'] = $douyinOne['verify_id'];
                        $log['verify_certificate_id'] = $douyinOne['certificate_id'];
                        $log['verify_order_id'] = $douyinOne['order_id'];
                        $log['verify_json'] = json_encode($douyinOne, JSON_UNESCAPED_UNICODE);

                        $log['verify_user_code'] = $request['encrypted_url']?$request['encrypted_url']:$request['code'];
                        $log['school_id'] = $request['school_id'];
                        $log['school_branch'] = $request['school_branch'];
                        $log['student_cnname'] = $request['student_cnname'];
                        $log['student_branch'] = $request['student_branch'];
                        $log['student_id'] = $request['student_id'];
                        $log['feeitem_id'] = $request['feeitem_id'];

                        $log['verify_notes'] = "";
                        $log['verify_createtime'] = time();
                        $verifyID = $this->DataControl->insertData('gmc_company_douyin_order_certificate_verify', $log);

                        //修改券的状态
                        if($log['verify_state'] == '1' || $log['verify_state'] == '2'){
                            $cerone = array();
                            $cerone['verify_id'] = $verifyID;
                            $cerone['school_id'] = $request['school_id'];
                            $cerone['school_branch'] = $request['school_branch'];
                            $cerone['student_id'] = $request['student_id'];
                            $cerone['student_cnname'] = $request['student_cnname'];
                            $cerone['student_branch'] = $request['student_branch'];
                            $cerone['feeitem_id'] = $request['feeitem_id'];
                            $cerone['certificate_status'] = 2;//已使用
                            $cerone['certificate_updatetime'] = time();
                            $this->DataControl->updateData('gmc_company_douyin_order_certificate', " order_order_id = '{$douyinOne['order_id']}' and certificate_certificate_id = '{$douyinOne['certificate_id']}' ", $cerone);
                        }
                    }
                }
            }else{
                $this->error = 1;
                $this->errortip = $bakData['data']['description'].'-'.$bakData['data']['error_code'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }

        $this->error = 0;
        $this->errortip = '核销完成';
        return true;
    }
    function getDouyinCertificateVerifyApiBak($request){
        if(!$request['poi_id']){
            $this->error = 1;
            $this->errortip = "请选择核销门店";
            return false;
        }
        if(!$request['verify_num']){
            $request['verify_num'] = 1;
        }

        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }
        //验券准备
        $PlanData = $this->upDouyinCertificatePlanApi($request);//code  encrypted_url
        if(!$PlanData || !$PlanData['encrypted_codes'] || $PlanData['verify_token'] == ''){
            $this->error = 1;
            $this->errortip = "验券准备失败（参数操作）";
            return false;
        }

        //看核销几张券
        $encrypted_codes = array_slice($PlanData['encrypted_codes'],0,$request['verify_num']);

        $somepar = array();
        $somepar['verify_token'] = $PlanData['verify_token'];
        $somepar['encrypted_codes'] = $encrypted_codes;
        $somepar['poi_id'] = $request['poi_id'];

//        print_r($PlanData);
//        print_r($somepar);
//        die;

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/fulfilment/certificate/verify/", json_encode($somepar,JSON_UNESCAPED_UNICODE), "POST", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true)){

            if($bakData['data']['error_code'] == '0' && $bakData['data']['description'] == 'success') {

                $detaInfo = $bakData['data']['verify_results'];
                if ($detaInfo) {
                    foreach($detaInfo as $douyinOne){
                        $log = array();
                        $log['company_id'] = '8888';

                        $log['verify_prepare_order_id'] = $PlanData['order_id'];
                        $log['verify_prepare_verify_token'] = $PlanData['verify_token'];
                        $log['verify_prepare_encrypted_code'] = json_encode($PlanData['encrypted_codes'], JSON_UNESCAPED_UNICODE);
                        $log['verify_prepare_json'] = $PlanData['certificates_json'];

                        if($douyinOne['result'] == '0'){//||
                            $log['verify_state'] = 1;//成功
                        }elseif($douyinOne['result'] == '1208' || $douyinOne['result'] == '2'){
                            $log['verify_state'] = 2;// 重复验券成功
                        } else{
                            $log['verify_state'] = 3;//失败
                        }

                        $log['verify_poi_id'] = $request['poi_id'];
                        $log['verify_code'] = $douyinOne['code'];
                        $log['verify_verify_id'] = $douyinOne['verify_id'];
                        $log['verify_certificate_id'] = $douyinOne['certificate_id'];
                        $log['verify_order_id'] = $douyinOne['order_id'];
                        $log['verify_json'] = json_encode($douyinOne, JSON_UNESCAPED_UNICODE);

                        $log['verify_notes'] = "";
                        $log['verify_createtime'] = time();
                        $orderID = $this->DataControl->insertData('gmc_company_douyin_order_certificate_verify', $log);

                        //修改券的状态
                        if($log['verify_state'] == '1' || $log['verify_state'] == '2'){
                            $cerone = array();
                            $cerone['certificate_status'] = 1;//未使用
                            $cerone['certificate_updatetime'] = time();
                            $this->DataControl->updateData('gmc_company_douyin_order_certificate', " order_order_id = '{$douyinOne['order_id']}' and certificate_certificate_id = '{$douyinOne['certificate_id']}' ", $cerone);
                        }
                    }
                }
            }else{
                $this->error = 1;
                $this->errortip = $bakData['data']['description'].'-'.$bakData['data']['error_code'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }

        $this->error = 0;
        $this->errortip = '核销完成';
        return true;
    }

    //抖音获取 核销 - 验券 -- 撤销
    function getDouyinCertificateVerifyRevokeApi($request){
        if(!$request['verify_id']){
            $this->error = 1;
            $this->errortip = "请选择核销成功的ID";
            return false;
        }

        $request['company_id'] = $request['company_id']?$request['company_id']:'8888';

        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }
        $verifyData = $this->DataControl->selectOne("select * from gmc_company_douyin_order_certificate_verify where verify_id = '{$request['verify_id']}' and company_id = '{$request['company_id']}' ");
//        print_r($verifyData);
//        die;
        $somepar = array();
        $somepar['certificate_id'] = $verifyData['verify_certificate_id'];
        $somepar['verify_id'] =  $verifyData['verify_verify_id'];
//        $somepar['certificate_id'] = '7439998285324140544';
//        $somepar['verify_id'] =  '7440001802726131763';

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/fulfilment/certificate/cancel/", json_encode($somepar,JSON_UNESCAPED_UNICODE), "POST", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true,'512',JSON_BIGINT_AS_STRING)){

            if($bakData['extra']['error_code'] == '0' && $bakData['data']['error_code'] == '0') {

                $detaInfo = $bakData['data'];
                if ($detaInfo) {
                    $log = array();
                    $log['company_id'] = $request['company_id']?$request['company_id']:'8888';
                    $log['verify_id'] = $request['verify_id'];

                    if($detaInfo['error_code'] == '0'){//||
                        $log['cancel_state'] = 1;//成功
                    } else{
                        $log['cancel_state'] = 2;//失败
                    }

                    $log['cancel_verify_id'] = $somepar['verify_id'];
                    $log['cancel_certificate_id'] = $somepar['certificate_id'];
                    $log['cancel_json'] = json_encode($detaInfo, JSON_UNESCAPED_UNICODE);
                    $log['cancel_notes'] = "";
                    $log['cancel_createtime'] = time();
                    $orderID = $this->DataControl->insertData('gmc_company_douyin_order_certificate_cancel', $log);

                    //修改券的状态
                    if($log['cancel_state'] == '1'){
                        $cerone = array();
                        $cerone['verify_id'] = 0;
                        $cerone['certificate_status'] = 1;//未使用

                        $cerone['student_id'] = 0;//未使用
                        $cerone['student_branch'] = '';//未使用
                        $cerone['student_cnname'] = '';//未使用
                        $cerone['school_id'] = 0;//未使用
                        $cerone['school_branch'] = '';//未使用
                        $cerone['feeitem_id'] = 0;//未使用

                        $cerone['certificate_updatetime'] = time();
                        $this->DataControl->updateData('gmc_company_douyin_order_certificate', " order_order_id = '{$verifyData['verify_order_id']}' and certificate_certificate_id = '{$verifyData['verify_certificate_id']}' ", $cerone);
                    }
                }
            }else{
                $this->error = 1;
                $this->errortip = $bakData['data']['description'].'-'.$bakData['data']['error_code'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }

        $this->error = 0;
        $this->errortip = '撤销完成';
        return true;
    }
    function getDouyinCertificateVerifyRevokeApiBak($request){
        if(!$request['verify_id']){
            $this->error = 1;
            $this->errortip = "请选择核销成功的ID";
            return false;
        }

        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }
        $verifyData = $this->DataControl->selectOne("select * from gmc_company_douyin_order_certificate_verify where verify_id = '{$request['verify_id']}' and company_id = '8888' ");
//print_r($verifyData);
//die;
        $somepar = array();
        $somepar['certificate_id'] = $verifyData['verify_certificate_id'];
        $somepar['verify_id'] =  $verifyData['verify_verify_id'];

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/fulfilment/certificate/cancel/", json_encode($somepar,JSON_UNESCAPED_UNICODE), "POST", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true)){

            if($bakData['extra']['error_code'] == '0' && $bakData['data']['error_code'] == '0') {

                $detaInfo = $bakData['data'];
                if ($detaInfo) {
                        $log = array();
                        $log['company_id'] = '8888';
                        $log['verify_id'] = $request['verify_id'];

                        if($detaInfo['error_code'] == '0'){//||
                            $log['cancel_state'] = 1;//成功
                        } else{
                            $log['cancel_state'] = 2;//失败
                        }

                        $log['cancel_verify_id'] = $somepar['verify_id'];
                        $log['cancel_certificate_id'] = $somepar['certificate_id'];
                        $log['cancel_json'] = json_encode($detaInfo, JSON_UNESCAPED_UNICODE);
                        $log['cancel_notes'] = "";
                        $log['cancel_createtime'] = time();
                        $orderID = $this->DataControl->insertData('gmc_company_douyin_order_certificate_cancel', $log);

                        //修改券的状态
                        if($log['cancel_state'] == '1'){
                            $cerone = array();
                            $cerone['certificate_status'] = 2;//已使用
                            $cerone['certificate_updatetime'] = time();
                            $this->DataControl->updateData('gmc_company_douyin_order_certificate', " order_order_id = '{$verifyData['verify_order_id']}' and certificate_certificate_id = '{$verifyData['verify_certificate_id']}' ", $cerone);
                        }
                }
            }else{
                $this->error = 1;
                $this->errortip = $bakData['data']['description'].'-'.$bakData['data']['error_code'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }

        $this->error = 0;
        $this->errortip = '核销完成';
        return true;
    }

    //抖音获取 券状态查询
    function getDouyinCertificateStateApi($request){
        if(!$request['verify_id']){
            $this->error = 1;
            $this->errortip = "请选择核销记录的ID";
            return false;
        }
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }
        $verifyData = $this->DataControl->selectOne("select * from gmc_company_douyin_order_certificate_verify where verify_id = '{$request['verify_id']}' and company_id = '8888' ");

        if(!$verifyData['verify_code']){
            $this->error = 1;
            $this->errortip = "核销记录的数据不完整";
            return false;
        }

        $somepar = array();
        $somepar['encrypted_code'] = $request['encrypted_code'];

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/fulfilment/certificate/get/", dataEncode($somepar), "GET", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true)){
            if($bakData['extra']['error_code'] == '0') {
                if($bakData['data']['certificate']['certificate_id'] && $bakData['data']['certificate']['status']) {
                    $certificate_id = $bakData['data']['certificate']['certificate_id'];
                    $status = $bakData['data']['certificate']['status'];

                    $cerone = array();
                    $cerone['certificate_status'] = $status;//未使用
                    $cerone['certificate_updatetime'] = time();
                    $this->DataControl->updateData('gmc_company_douyin_order_certificate', " certificate_certificate_id = '{$certificate_id}' ", $cerone);

                    $this->error = 0;
                    $this->errortip = "券码状态查询修改成功";
                    return false;
                }else{
                    $this->error = 1;
                    $this->errortip = $bakData['data']['description']?$bakData['data']['description']:'券码状态查询失败';
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = $bakData['extra']['description'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "券码状态查询失败";
            return false;
        }

    }

    //抖音获取 线上商品数据列表
    function getDouyinGoodOnlineApi($request,$page_size=10,$next_cursor=0){
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }

        $somepar = array();
        $somepar['account_id'] = $this->account_id;
        if($next_cursor>0){
            $somepar['cursor'] = $next_cursor;
        }
        $somepar['count'] = $page_size;
        $somepar['status'] = 1;

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/goods/product/online/query/", dataEncode($somepar), "GET", $header);
//        print_r($getBackJson);
//        die;
        if($bakData = json_decode($getBackJson, true)){
            if($bakData['BaseResp']['StatusCode'] == '0') {

                if($bakData['data']['error_code'] == '0') {

                    $detaInfo = $bakData['data']['products'];

                    if ($detaInfo) {
                        foreach($detaInfo as $douyinOne){
                            $goodsID = $this->DataControl->getFieldOne("gmc_company_douyin_goods","goods_id","goods_pid = '{$douyinOne['product']['product_id']}'");
                            if(!$goodsID){
                                $log = array();
                                $log['company_id'] = $request['company_id']?$request['company_id']:'8888';
                                $log['channel_branch'] = 'douyin';
                                $log['goods_accountid'] = $this->account_id;;

                                $log['goods_cnname'] = $douyinOne['product']['product_name'];
                                $log['goods_pid'] = $douyinOne['product']['product_id'];
                                $log['goods_saleprice'] = $douyinOne['sku']['origin_amount']/100;
                                $log['goods_actualprice'] = $douyinOne['sku']['actual_amount']/100;
                                $log['goods_createtime'] = time();
                                $orderID = $this->DataControl->insertData('gmc_company_douyin_goods', $log);

                                if($orderID){
                                    $loga = array();
                                    $loga['goods_id'] = $orderID;
                                    $loga['json_json'] = addslashes(json_encode($douyinOne, JSON_UNESCAPED_UNICODE));
                                    $loga['json_createtime'] = time();
                                    $orderjsonID = $this->DataControl->insertData('gmc_company_douyin_goods_json', $loga);
                                }
                            }else{
                                $log = array();
                                $log['goods_cnname'] = $douyinOne['product']['product_name'];
                                $log['goods_pid'] = $douyinOne['product']['product_id'];
                                $log['goods_saleprice'] = $douyinOne['sku']['origin_amount']/100;
                                $log['goods_actualprice'] = $douyinOne['sku']['actual_amount']/100;
                                $log['goods_updatetime'] = time();
                                $this->DataControl->updateData('gmc_company_douyin_goods',"goods_pid = '{$douyinOne['product']['product_id']}'", $log);
                                if($goodsID){
                                    $loga = array();
                                    $loga['json_json'] =  addslashes(json_encode($douyinOne, JSON_UNESCAPED_UNICODE));
                                    $loga['json_createtime'] = time();
                                    $this->DataControl->updateData('gmc_company_douyin_goods_json',"goods_id = '{$goodsID}'", $loga);
                                }
                            }
                        }
                    }

                    //判断总数是否大于每次能查到的最大数量
                    if($bakData['data']['has_more'] == true){
                        $this->getDouyinGoodOnlineApi($request,$page_size,$bakData['data']['next_cursor']);
                    }

                    $this->error = 0;
                    $this->errortip = '同步完成';
                    return true;
                }else{
                    $this->error = 1;
                    $this->errortip = $bakData['data']['description']?$bakData['data']['description']:'数据查询失败';
                    return false;
                }

            }else{
                $this->error = 1;
                $this->errortip = $bakData['BaseResp']['StatusMessage']?$bakData['BaseResp']['StatusMessage']:"参数错误";
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "券码状态查询失败";
            return false;
        }

    }
    function getDouyinGoodOnlineApiBak($request,$page_no=1,$page_size=50,$next_cursor=0){
        $DyClientToke = $this->getDyClientToken();
        if(!$DyClientToke){
            $this->error = 1;
            $this->errortip = "token失效";
            return false;
        }

        $somepar = array();
        $somepar['account_id'] = $this->account_id;
        if($next_cursor>0){
            $somepar['next_cursor'] = $next_cursor;
        }
        $somepar['count'] = $page_size;
        $somepar['status'] = 1;
        $somepar['product_namestring'] = "【门店新客】吉的堡成长中心 1节语言提能体验（限首次体验1次）";

        $header = array();
        $header[] = "Content-type:application/json";
        $header[] = "access-token:".$DyClientToke;

        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/goods/product/online/query/", dataEncode($somepar), "GET", $header);
        print_r($getBackJson);
        die;
        if($bakData = json_decode($getBackJson, true)){
            if($bakData['extra']['error_code'] == '0') {
                if($bakData['data']['certificate']['certificate_id'] && $bakData['data']['certificate']['status']) {
                    $certificate_id = $bakData['data']['certificate']['certificate_id'];
                    $status = $bakData['data']['certificate']['status'];

                    $cerone = array();
                    $cerone['certificate_status'] = $status;//未使用
                    $cerone['certificate_updatetime'] = time();
                    $this->DataControl->updateData('gmc_company_douyin_order_certificate', " certificate_certificate_id = '{$certificate_id}' ", $cerone);

                    $this->error = 0;
                    $this->errortip = "券码状态查询修改成功";
                    return false;
                }else{
                    $this->error = 1;
                    $this->errortip = $bakData['data']['description']?$bakData['data']['description']:'券码状态查询失败';
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = $bakData['extra']['description'];
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "券码状态查询失败";
            return false;
        }

    }





    //------------------------ 老的账号同步客资信息   开始 ---------------------------
//    //解密抖音加密手机号的名单
//    function decryptDyMobileOld($mobile){
//        $base64mobile = base64_decode($mobile);
//        $key = $this->app_secret_old;
//        $iv = mb_substr($this->app_secret_old, -16, 16);
//
//        $decrypted = openssl_decrypt($base64mobile, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
//        if($decrypted){
//            return $decrypted;
//        }else{
//            return false;
//        }
//    }
//    //生成 抖音名单获取token  client_token
//    function getDyClientTokenOld(){
//        $nowtime = time();
//        $tokenOne = $this->DataControl->selectOne(" select * from crm_company_douyin_token where token_type = 1 and token_accountid = '{$this->account_id_old}' and token_failuretime > '{$nowtime}' order by token_failuretime desc ");
//        if($tokenOne['token_string']){
//            $nowtoken = $tokenOne['token_string'];
//        }else {
//            $param = array(
//                'client_key' => $this->app_key_old, //
//                'client_secret' => $this->app_secret_old, //
//                'grant_type' => "client_credential", //
//            );
//            $header = array();
//            $header[] = "Content-type: application/json";
//
//            //POST参数 RAW中JSON方式传值获取结果
////            $getBackJson = request_by_curl("https://open.douyin.com/oauth/client_token/", json_encode($param), "POST", $header);  --- 失败了
//            $getBackJson = httpRequest("https://open.douyin.com/oauth/client_token/", json_encode($param), "POST", $header);
////            print_r($getBackJson);die;
//            $bakData = json_decode($getBackJson, true);
//
//            if($bakData['message'] == 'success' && $bakData['data']['access_token']){
//                //存表
//                $record = array();
//                $record['token_type'] = 1;
//                $record['token_accountid'] = $this->account_id_old;
//                $record['token_string'] = $bakData['data']['access_token'];
//                $record['token_failuretime'] = time()+7000;
//                $this->DataControl->insertData("crm_company_douyin_token",$record);
//
//                $nowtoken = $bakData['data']['access_token'];
//            }
//        }
//        $this->error = 0;
//        $this->errortip = '获取';
//        $this->result = $nowtoken;
//        return $nowtoken;
//    }
//    //抖音获取线索（客资）信息
//    function getDouyinLeadsOldApi($request,$page_no=1,$page_size=100,$gmt_start='',$gmt_end=''){
//        $DyClientToke = $this->getDyClientTokenOld();
//        if(!$DyClientToke){
//            $this->error = 1;
//            $this->errortip = "token失效";
//            return false;
//        }
//
////        $gmt_start = "2024-10-24 13:00:30";
////        $gmt_end = "2024-10-24 14:00:30";
//
//        $somepar = array();
//        $somepar['account_id'] = $this->account_id_old;
//        $somepar['start_time'] = $gmt_start?$gmt_start:date("Y-m-d H:i:s",time()-1201); //
//        $somepar['end_time'] = $gmt_end?$gmt_end:date("Y-m-d H:i:s",time()-600); //
//        $somepar['page'] = $page_no;
//        $somepar['page_size'] = $page_size;
//
//        $header = array();
//        $header[] = "Content-type:application/json";
//        $header[] = "access-token:".$DyClientToke;
//
//        //POST参数 RAW中JSON方式传值获取结果
//        $getBackJson = httpRequest("https://open.douyin.com/goodlife/v1/open_api/crm/clue/query/", dataEncode($somepar), "GET", $header);
//
//        if($bakData = json_decode($getBackJson, true,'512',JSON_BIGINT_AS_STRING)){
//
//            $totalCount = $bakData['data']['page']['total'];
//            if($bakData['data']['page']['total'] >= '1' && $bakData['data']['description'] == 'success'){
//
//                $detaInfo = $bakData['data']['clue_data'];
//                if($detaInfo){
//
//                    $cluetypeName = array("0"=>"预约表单","1"=>"私信","2"=>"智能电话","7"=>"团购留资");
//                    foreach ($detaInfo as $detaInfoVar){
//
//                        //手机号解密
//                        $decryptmobile = $this->decryptDyMobileOld($detaInfoVar['telephone']);
//                        $douyinlead_telephone = $decryptmobile?$decryptmobile:$detaInfoVar['telephone'];
//
//                        //是否存储过了
//                        $haveOne = $this->DataControl->selectOne(" select 1 from gmc_company_douyinleads where douyinlead_clue_id = '{$detaInfoVar['clue_id']}' limit 0,1 ");
//                        if (!$haveOne) {
//                            $log = array();
//                            $log['company_id'] = '8888';
//
//                            $log['douyinlead_clue_id'] = $detaInfoVar['clue_id'];
//                            $log['douyinlead_telephone'] = $douyinlead_telephone;
//                            $log['douyinlead_clue_owner_name'] = $detaInfoVar['clue_owner_name'];
//                            $log['douyinlead_clue_type'] = $detaInfoVar['clue_type'];
//                            $log['douyinlead_effective_state'] = $detaInfoVar['effective_state'];
//                            $log['douyinlead_create_time_detail'] = $detaInfoVar['create_time_detail'];
//                            $log['douyinlead_intention_poi_id'] = $detaInfoVar['intention_poi_id'];
//                            $log['douyinlead_intention_life_account_name'] = $detaInfoVar['intention_life_account_name'];
//                            $log['douyinlead_follow_poi_id'] = $detaInfoVar['follow_poi_id'];
//                            $log['douyinlead_leads_page'] = $detaInfoVar['leads_page'];
//                            $log['douyinlead_order_id'] = (string)$detaInfoVar['order_id'];
//                            $log['douyinlead_product_id'] = $detaInfoVar['product_id'];
//                            $log['douyinlead_product_name'] = $detaInfoVar['product_name'];
//                            $log['douyinlead_age'] = $detaInfoVar['age'];
//                            $log['douyinlead_gender'] = $detaInfoVar['gender'];
//                            $log['douyinlead_address'] = $detaInfoVar['address'];
//
//                            $log['douyinlead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
//                            $log['douyinlead_createtime'] = time();
//                            if ($leadsId = $this->DataControl->insertData('gmc_company_douyinleads', $log)) {
//
//                                $xw_school_branch = '';
//                                if(trim($detaInfoVar['intention_poi_id']) !==''){
//                                    $douyinshopOne = $this->DataControl->getFieldOne("gmc_company_douyinshop", "school_branch,douyinshop_poi_name"
//                                        , "douyinshop_poi_id = '{$detaInfoVar['intention_poi_id']}' AND school_branch <> '' and company_id = '8888'");
//                                    $xw_school_branch = $douyinshopOne['school_branch'];
//                                }
//
//                                //名单的主要信息
//                                $params = array();
//                                $params['company_id'] = 8888;
//                                $params['mobile'] = $douyinlead_telephone;
//                                $params['buy_time'] = strtotime($detaInfoVar['create_time_detail']);
//                                $params['remark'] = "留资组件: {$cluetypeName[$detaInfoVar['clue_type']]};订单编号：{$detaInfoVar['order_id']}；购买的商品： ".$detaInfoVar['product_name'];
//                                $params['school_branch'] = $xw_school_branch;
//
//                                $school_one = $this->DataControl->selectOne(" select 1 as ishave from smc_school where company_id = '8888' and school_branch = '{$xw_school_branch}' ");
//
//                                if ($xw_school_branch && $school_one['ishave'] == '1') {//校务系统
//                                    //走 校务系统的名单处理
//                                    $resultApi = $this->addClient($params);
////                                    print_r($resultApi);
//
//                                    $logone = array();
//                                    $logone['douyinlead_client_status'] = $resultApi['status'];
//                                    $logone['douyinlead_errortip'] = $resultApi['errtip'];
//                                    $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);
//
//                                } elseif($xw_school_branch  && $school_one['ishave'] != '1') {//园务系统
//
//                                    $params['douyinlead_alljson'] = json_encode($detaInfoVar, JSON_UNESCAPED_UNICODE);
//                                    //走 园务系统的名单处理
//                                    request_by_curl("https://kmcapi.kedingdang.com/Api/cmsDouyinLeads", dataEncode($params), "POST");
////                                        $aa = request_by_curl("http://kmcapi.kidmanageapi102.com/Api/cmsDouyinLeads", dataEncode($params), "POST");
////                                        print_r($aa);die;
//                                    $logone = array();
//                                    $logone['douyinlead_client_status'] = '-2';
//                                    $logone['douyinlead_errortip'] = '直营园名单异步通知中！';
//                                    $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);
//
//                                }else{//对不上学校 走 校务系统
//
//                                    //走 校务系统的名单处理
//                                    $resultApi = $this->addClient($params);
//
//                                    $logone = array();
//                                    $logone['douyinlead_client_status'] = $resultApi['status'];
//                                    $logone['douyinlead_errortip'] = $resultApi['errtip'];
//                                    $this->DataControl->updateData('gmc_company_douyinleads', "douyinlead_id = '{$leadsId}'", $logone);
//
//                                }
//                            }
//                        }
//                    }
//
//                }
//
//                //判断总数是否大于每次能查到的最大数量
//                if($totalCount > $page_no*$page_size){
//                    $p = $page_no+1;
//                    $num = $page_size;
//                    $this->getDouyinLeadsOldApi($request,$p,$num,$somepar['start_time'],$somepar['end_time']);
//                }
//
//            }else{
//                $this->error = 1;
//                $this->errortip = "暂无数据";
//                return false;
//            }
//
//        }else{
//            $this->error = 1;
//            $this->errortip = "数据获取失败";
//            return false;
//        }
//
//        $this->error = 0;
//        $this->errortip = "回调成功！";
//        return true;
//    }
    //------------------------ 老的账号同步客资信息   结束 ---------------------------


}