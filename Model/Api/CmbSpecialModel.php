<?php


namespace Model\Api;

class CmbSpecialModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct()
    {
        parent::__construct();
    }

    function specialTransSubject($request){

        //获取家长信息
        $sql="select b.parenter_mobile from smc_student_family as a,smc_parenter as b where a.parenter_id=b.parenter_id and a.family_isdefault=1 and a.student_id='{$request['student_id']}'";
        $familyOne=$this->DataControl->selectOne($sql);

        $sql = "select (select y.order_pid from smc_student_coursebalance_log as x,smc_payfee_order as y where x.trading_pid=y.trading_pid and x.student_id=a.student_id and x.course_id=a.course_id and x.log_id<a.log_id and x.hourstudy_id=0 and x.companies_id=a.companies_id and x.log_playclass='+' order by x.log_time desc limit 0,1) as order_pid,a.log_playamount,a.companies_id,a.class_id,a.hourstudy_id,a.course_id,c.company_id,a.school_id,b.coursetype_id,b.coursecat_id,a.student_id,c.income_id,c.income_price,1 as income_times,FROM_UNIXTIME(c.income_confirmtime,'%Y-%m-%d') as income_date
                from smc_student_coursebalance_log as a
                left join smc_course as b on b.course_id=a.course_id
                left join smc_school_income as c on c.hourstudy_id=a.hourstudy_id and c.school_id=a.school_id
                where a.student_id='{$request['student_id']}' and a.school_id='{$request['school_id']}' and a.companies_id='{$request['to_companies_id']}' and a.hourstudy_id>0 and FROM_UNIXTIME(a.log_time,'%Y-%m-%d')>='{$request['apply_date']}'
                order by a.log_time asc";

        $logList=$this->DataControl->selectClear($sql);

        if(!$logList){
            $this->error = 1;
            $this->errortip = "无已耗课记录";
            return false;
        }

        $fromCompaniesOne=$this->DataControl->getFieldOne("gmc_code_companies","companies_agencyid","companies_id='{$request['from_companies_id']}'");

        $orderArray=array();
        $balanceLogList=array();
        foreach($logList as $logOne) {
            $orderArray[$logOne['order_pid']] = $logOne;
            $balanceLogList[$logOne['order_pid']][] = $logOne;
        }

        $allPrice=$request['coursebalance'];//总监管金额
        $transPrice=0;//已经确认的金额
        $hastransPrice=0;//本次记录金额

        foreach($orderArray as $orderArrayOne){

            $classOne=$this->DataControl->getOne("smc_class","class_id='{$orderArrayOne['class_id']}'");

            //更新/插入 班级信息
            $data = array();
            $data['agency_id'] = $fromCompaniesOne['companies_agencyid'];
            $data['companies_id'] = $request['from_companies_id'];
            $data['class_branch'] = $classOne['class_branch'];
            $data['class_stdate'] = $classOne['class_stdate'];
            $data['class_enddate'] = $classOne['class_enddate'];
            $data['update_status'] = 0;
            $data['updatetime'] = time();

            $cmbClassOne=$this->DataControl->getOne("cmb_trans_class","class_id='{$classOne['class_id']}'");
            if(!$cmbClassOne || $cmbClassOne['is_confirm']!=1){
                if (!$cmbClassOne){
                    $data['is_confirm'] = 1;
                    $data['class_id'] = $classOne['class_id'];
                    $this->DataControl->insertData('cmb_trans_class', $data);
                }else{
                    $data['is_confirm'] = 1;
                    $this->DataControl->updateData('cmb_trans_class',"class_id='{$classOne['class_id']}'", $data);
                }
            }
            $sql = "select b.*,d.coursetype_id,d.coursecat_id,c.ordercourse_buynums as order_num,c.ordercourse_totalprice
                ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1),'%Y-%m-%d') as order_date
                from smc_payfee_order as b
                left join smc_payfee_order_course as c on c.order_pid=b.order_pid
                left join smc_course d on d.course_id=c.course_id
                where b.order_pid='{$orderArrayOne['order_pid']}' and c.course_id='{$orderArrayOne['course_id']}'";

            $orderOne=$this->DataControl->selectOne($sql);
            if($allPrice<=$orderOne['ordercourse_totalprice']){
                $orderOne['ordercourse_totalprice']=$allPrice;
            }

            if(($orderOne['ordercourse_totalprice']+$transPrice)>$allPrice){
                $orderOne['ordercourse_totalprice']=$allPrice-$transPrice;
            }

            $data = array();
            $data['company_id'] = $orderOne['company_id'];
            $data['companies_id'] = $request['from_companies_id'];
            $data['agency_id'] = $fromCompaniesOne['companies_agencyid'];
            $data['school_id'] = $orderOne['school_id'];
            $data['coursetype_id'] = $orderOne['coursetype_id'];
            $data['coursecat_id'] = $orderOne['coursecat_id'];
            $data['course_id'] = $orderArrayOne['course_id'];
            $data['class_id'] = $orderArrayOne['class_id'];
            $data['student_id'] = $orderOne['student_id'];
            $data['parent_mobile'] = $familyOne?$familyOne['parenter_mobile']:'';
            $data['order_type'] = 'P';
            $data['order_pid'] = $orderOne['order_pid'];
            $data['order_amt'] = $orderOne['ordercourse_totalprice'];
            $data['order_fee'] = 0;
            $data['order_num'] = $orderOne['order_num'];
            $data['settle_date'] = $orderOne['order_date'];
            $data['order_date'] = ($orderOne['order_date'] >= $classOne['class_stdate']) ? (($orderOne['order_date'] >= $classOne['class_enddate']) ? $classOne['class_enddate'] : $orderOne['order_date']) : $classOne['class_stdate'];
            $data['is_confirm']=1;

            //根据订单信息判断是否插入或者修改订单信息
            $cmbOrderOne=$this->DataControl->getFieldOne("cmb_trans_order","order_id,is_confirm","order_pid='{$orderOne['order_pid']}'");
            if(!$cmbOrderOne) {

                 $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);

            }else{
                if($cmbOrderOne['is_confirm']==1){
                    $insertorder=$cmbOrderOne['order_id'];
                }else{
                    $insertorder = $this->DataControl->updateData('cmb_trans_order', "order_pid='{$orderOne['order_pid']}'",$data);
                }
            }

            if($insertorder){
                $hourstudyStr=implode(',',array_column($balanceLogList[$orderArrayOne['order_pid']],'hourstudy_id'));

                $sql = "select a.*
                from cmb_trans_transfer as a 
                where a.hourstudy_id in (" . $hourstudyStr . ")
                order by a.hourstudy_id asc
                ";

                //获取已经同步到transfer表的课耗
                $transList=$this->DataControl->selectClear($sql);

                $confirmPrice=0;

                if($transList){
                    foreach($transList as $transOne){
                        if($transOne['is_confirm']==1){
                            $confirmPrice+=$transOne['income_price'];
                        }
                    }

                    $transListArray=array_column($transList,null,"hourstudy_id");
                }else{

                    $confirmPrice=0;
                    $transListArray=array();
                }

                //赋值还可以确认划拨总金额
                $Price=$orderOne['ordercourse_totalprice']-$confirmPrice;
                $hastransPrice+=$confirmPrice;
                $allPrice-=$confirmPrice;

                foreach($balanceLogList[$orderArrayOne['order_pid']] as $balanceLogOne){

                    if($Price>0){
                        //判断已经存在的transfer
                        if($transListArray[$balanceLogOne['hourstudy_id']]){

                            //如果已经同步的课耗是未确认的,则修改为已经确认,并修改剩余金额
                            if($transListArray[$balanceLogOne['hourstudy_id']]['is_confirm']==0){

                                if($balanceLogOne['income_price']<=$Price){

                                    $Price-=$balanceLogOne['income_price'];

                                    $income_price=$balanceLogOne['income_price'];
                                }else{
                                    $income_price=$Price;
                                    $Price=0;
                                }
                                $allPrice-=$income_price;
                                $transPrice+=$income_price;
                                $hastransPrice+=$income_price;
                                $data = array();
                                $data['company_id'] = $balanceLogOne['company_id'];
                                $data['companies_id'] = $request['from_companies_id'];
                                $data['agency_id'] = $fromCompaniesOne['companies_agencyid'];
                                $data['order_pid'] = $orderArrayOne['order_pid'];
                                $data['school_id'] = $balanceLogOne['school_id'];
                                $data['coursetype_id'] = $balanceLogOne['coursetype_id'];
                                $data['coursecat_id'] = $balanceLogOne['coursecat_id'];
                                $data['course_id'] = $balanceLogOne['course_id'];
                                $data['class_id'] = $balanceLogOne['class_id'];
                                $data['student_id'] = $balanceLogOne['student_id'];
                                $data['hourstudy_id'] = $balanceLogOne['hourstudy_id'];
                                $data['income_id'] = $balanceLogOne['income_id'];
                                $data['income_price'] = $income_price;
                                $data['income_times'] = $balanceLogOne['income_times'];
                                $data['income_date'] = $balanceLogOne['income_date'];
                                $data['is_confirm'] = 1;
                                $data['transfer_updatetime'] = time();

                                $this->DataControl->updateData("cmb_trans_transfer","hourstudy_id='{$balanceLogOne['hourstudy_id']}'",$data);

                            }

                        }else{

                            if($balanceLogOne['income_price']<=$Price){

                                $Price-=$balanceLogOne['income_price'];

                                $income_price=$balanceLogOne['income_price'];
                            }else{
                                $income_price=$Price;
                                $Price=0;
                            }
                            $allPrice-=$income_price;
                            $transPrice+=$income_price;
                            $hastransPrice+=$income_price;

                            $data = array();
                            $data['company_id'] = $balanceLogOne['company_id'];
                            $data['companies_id'] = $request['from_companies_id'];
                            $data['agency_id'] = $fromCompaniesOne['companies_agencyid'];
                            $data['order_pid'] = $orderArrayOne['order_pid'];
                            $data['school_id'] = $balanceLogOne['school_id'];
                            $data['coursetype_id'] = $balanceLogOne['coursetype_id'];
                            $data['coursecat_id'] = $balanceLogOne['coursecat_id'];
                            $data['course_id'] = $balanceLogOne['course_id'];
                            $data['class_id'] = $balanceLogOne['class_id'];
                            $data['student_id'] = $balanceLogOne['student_id'];
                            $data['hourstudy_id'] = $balanceLogOne['hourstudy_id'];
                            $data['income_id'] = $balanceLogOne['income_id'];
                            $data['income_price'] = $income_price;
                            $data['income_times'] = $balanceLogOne['income_times'];
                            $data['income_date'] = $balanceLogOne['income_date'];
                            $data['is_confirm'] = 1;
                            $data['transfer_createtime'] = time();
                            $this->DataControl->insertData("cmb_trans_transfer",$data);
                        }
                    }else{
                        break;
                    }
                }

            }else{
                $this->error = 1;
                $this->errortip = "订单更新失败";
                return false;
            }
        }

        return $hastransPrice;

    }

    function specialTrans()
    {

        $sql = "SELECT
	a.student_id,
	a.companies_id,
	b.companies_agencyid,a.pay_successtime,
	sum( a.pay_price ) AS allPirce,
	(
	SELECT
		sum( x.income_price )
	FROM
		cmb_trans_transfer AS x
	WHERE
		x.student_id = a.student_id
		AND x.companies_id = x.companies_id
		AND x.transfer_status IN ( 1, 2 )
	) AS transPrice,
	(
	SELECT
		sum( x.income_price )
	FROM
		cmb_trans_transfer AS x,
		cmb_trans_order AS y
	WHERE
		x.order_pid = y.order_pid
		AND x.student_id = a.student_id
		AND x.companies_id = x.companies_id
		AND y.is_confirm = 1
		AND x.is_confirm = 1
	) AS confirmPrice,
	(
	SELECT
		sum( x.log_playamount )
	FROM
		smc_student_coursebalance_log AS x
	WHERE
		x.student_id = a.student_id
		AND x.companies_id = a.companies_id
		AND x.hourstudy_id > 0
		AND x.log_time > a.pay_successtime
	) AS log_playamount
FROM
	cmb_trans_charge AS a,
	gmc_code_companies AS b
WHERE
	a.companies_id = b.companies_id
	AND a.charge_type = 0
	AND b.companies_supervisetime > 0
	AND b.companies_agencyid <> '' 
GROUP BY
	a.student_id,
	a.companies_id
HAVING
	( allPirce > transPrice )
	AND ( transPrice < log_playamount )
	AND ( allPirce > confirmPrice ) and (confirmPrice<log_playamount)
ORDER BY
	transPrice ASC ";

//        $sql="select *
//                from cmb_trans_speciallog";

        $studentList = $this->DataControl->selectClear($sql);

        if (!$studentList) {
            $this->error = 1;
            $this->errortip = "无可划拨学生";
            return false;
        }

        foreach ($studentList as $studentOne) {
            $sql="select b.parenter_mobile from smc_student_family as a,smc_parenter as b where a.parenter_id=b.parenter_id and a.family_isdefault=1 and a.student_id='{$studentOne['student_id']}'";
            $familyOne=$this->DataControl->selectOne($sql);

            $payPrice = $studentOne['allPirce'];
            //获取还没有划拨完成的订单
            $sql = "select a.*
                    ,ifnull((select sum(x.income_price) from cmb_trans_transfer as x where x.order_pid=a.order_pid and x.student_id=a.student_id and x.transfer_status>=1),0) as transPrice
                    from cmb_trans_order as a 
                    where a.student_id='{$studentOne['student_id']}' and a.is_confirm=1 
                    and a.order_status_all=1
                    order by (order_amt-transPrice) asc
                    ";
            $cmbOrderList = $this->DataControl->selectClear($sql);
            //存在已经传递给招行的订单
            if ($cmbOrderList) {
                foreach ($cmbOrderList as $cmbOrderOne) {

                    $classOne = $this->DataControl->getOne("smc_class", "class_id='{$cmbOrderOne['class_id']}'");

                    //更新/插入 班级信息
                    $data = array();
                    $data['agency_id'] = $cmbOrderOne['companies_agencyid'];
                    $data['companies_id'] = $cmbOrderOne['companies_id'];
                    $data['class_branch'] = $classOne['class_branch'];
                    $data['class_stdate'] = $classOne['class_stdate'];
                    $data['class_enddate'] = $classOne['class_enddate'];
                    $data['update_status'] = 0;
                    $data['updatetime'] = time();

                    $cmbClassOne=$this->DataControl->getOne("cmb_trans_class","class_id='{$classOne['class_id']}'");
                    if(!$cmbClassOne || $cmbClassOne['is_confirm']!=1){
                        if (!$cmbClassOne){
                            $data['is_confirm'] = 1;
                            $data['class_id'] = $classOne['class_id'];
                            $this->DataControl->insertData('cmb_trans_class', $data);
                        }else{
                            $data['is_confirm'] = 1;
                            $this->DataControl->updateData('cmb_trans_class',"class_id='{$classOne['class_id']}'", $data);
                        }
                    }


                    $sql = "select b.*,d.coursetype_id,d.coursecat_id,c.ordercourse_buynums as order_num,c.ordercourse_totalprice
                        ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1 limit 0,1),'%Y-%m-%d') as order_date
                        ,ifnull((select sum(x.income_price) from cmb_trans_transfer as x where x.order_pid=b.order_pid and x.companies_id = x.companies_id AND x.is_confirm=1),0) as comfirmPrice
                        ,ifnull((select sum(x.log_playamount) from smc_student_coursebalance_log as x,smc_school_income as y where x.hourstudy_id=y.hourstudy_id and x.school_id=y.school_id and x.student_id=b.student_id and x.companies_id=b.companies_id and FROM_UNIXTIME(x.log_time,'%Y-%m-%d')>='{$cmbOrderOne['settle_date']}' and x.log_class=0 and x.course_id=c.course_id),0) as logAllPrice
                        from smc_payfee_order as b
                        left join smc_payfee_order_course as c on c.order_pid=b.order_pid
                        left join smc_course d on d.course_id=c.course_id
                        where b.order_pid='{$cmbOrderOne['order_pid']}' and c.course_id='{$cmbOrderOne['course_id']}'";

                    $orderOne = $this->DataControl->selectOne($sql);
                    if ($cmbOrderOne['order_amt'] == $cmbOrderOne['transPrice']) {
                        $payPrice -= $cmbOrderOne['transPrice'];
                    } else {

                        if ($orderOne['ordercourse_totalprice'] >= $payPrice) {
                            if ($orderOne['logAllPrice'] >= $payPrice) {
                                $order_amt = $payPrice;
                            } else {
                                $order_amt = $orderOne['logAllPrice'];
                            }

                        }else{
                            if ($orderOne['logAllPrice'] >= $payPrice) {
                                $order_amt = $orderOne['ordercourse_totalprice'];
                            } else {
                                $order_amt = $orderOne['logAllPrice'];
                            }
                        }

                        $payPrice -= $order_amt;

                        $data = array();
                        $data['company_id'] = $orderOne['company_id'];
                        $data['companies_id'] = $studentOne['companies_id'];
                        $data['order_status'] = 0;
                        $data['order_status_all'] = 0;
                        $data['agency_id'] = $studentOne['companies_agencyid'];
                        $data['school_id'] = $orderOne['school_id'];
                        $data['coursetype_id'] = $orderOne['coursetype_id'];
                        $data['coursecat_id'] = $orderOne['coursecat_id'];
                        $data['course_id'] = $cmbOrderOne['course_id'];
                        $data['class_id'] = $cmbOrderOne['class_id'];
                        $data['student_id'] = $orderOne['student_id'];
                        $data['parent_mobile'] = $familyOne ? $familyOne['parenter_mobile'] : '';
                        $data['order_type'] = 'P';
                        $data['order_pid'] = $orderOne['order_pid'];
                        $data['order_amt'] = $order_amt;
                        $data['order_fee'] = 0;
                        $data['order_num'] = $orderOne['order_num'];
                        $data['settle_date'] = $orderOne['order_date'];
                        $data['order_date'] = ($orderOne['order_date'] >= $classOne['class_stdate']) ? (($orderOne['order_date'] >= $classOne['class_enddate']) ? $classOne['class_enddate'] : $orderOne['order_date']) : $classOne['class_stdate'];
                        $data['is_confirm'] = 1;

                        //根据订单信息判断是否插入或者修改订单信息
                        $cmbOrderOne = $this->DataControl->getOne("cmb_trans_order", "order_pid='{$orderOne['order_pid']}'");

                        if (!$cmbOrderOne) {
                            $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                        } else {

                            if ($order_amt <= $cmbOrderOne['order_amt'] && $cmbOrderOne['is_confirm'] == 1 && $cmbOrderOne['order_status_all'] == 1) {
                                $insertorder = $cmbOrderOne['order_id'];
                            } else {
                                $insertorder = $this->DataControl->updateData('cmb_trans_order', "order_pid='{$orderOne['order_pid']}'", $data);
                            }
                        }

                        if ($insertorder > 0) {

                            $canTransPrice = $order_amt;

                            $sql = "select a.log_playamount,a.companies_id,a.class_id,a.hourstudy_id,a.course_id,c.company_id,a.school_id,b.coursetype_id,b.coursecat_id,a.student_id,c.income_id,c.income_price,1 as income_times,FROM_UNIXTIME(c.income_confirmtime,'%Y-%m-%d') as income_date
                                from smc_student_coursebalance_log as a
                                inner join smc_school_income as c on c.hourstudy_id=a.hourstudy_id and c.school_id=a.school_id
                                left join smc_course as b on b.course_id=a.course_id
                                where a.student_id='{$studentOne['student_id']}' and a.companies_id='{$studentOne['companies_id']}' and a.hourstudy_id>0 and FROM_UNIXTIME(a.log_time,'%Y-%m-%d')>'{$cmbOrderOne['settle_date']}' and a.log_class=0
                                order by a.log_time asc";

                            $balanceLogList = $this->DataControl->selectClear($sql);

                            if ($balanceLogList) {
                                foreach ($balanceLogList as $balanceLogOne) {
                                    if ($canTransPrice > 0) {
                                        $sql = "select * 
                                                from cmb_trans_transfer as a 
                                                where a.order_pid='{$cmbOrderOne['order_pid']}'
                                                order by a.hourstudy_id asc
                                                ";
                                        $transList = $this->DataControl->selectClear($sql);
                                        if ($transList) {
                                            $transListArray = array_column($transList, null, 'hourstudy_id');
                                        } else {
                                            $transListArray = array();
                                        }
                                        //判断已经存在的transfer
                                        if ($transListArray[$balanceLogOne['hourstudy_id']]) {

                                            //如果已经同步的课耗是未确认的,则修改为已经确认,并修改剩余金额
                                            if ($transListArray[$balanceLogOne['hourstudy_id']]['is_confirm'] <= 0) {
                                                if ($balanceLogOne['income_price'] >= $canTransPrice) {

                                                    $income_price = $canTransPrice;
                                                } else {
                                                    $income_price = $balanceLogOne['income_price'];
                                                }

                                                $data = array();
                                                $data['company_id'] = $balanceLogOne['company_id'];
                                                $data['companies_id'] = $studentOne['companies_id'];
                                                $data['agency_id'] = $studentOne['companies_agencyid'];
                                                $data['order_pid'] = $cmbOrderOne['order_pid'];
                                                $data['school_id'] = $balanceLogOne['school_id'];
                                                $data['coursetype_id'] = $balanceLogOne['coursetype_id'];
                                                $data['coursecat_id'] = $balanceLogOne['coursecat_id'];
                                                $data['course_id'] = $balanceLogOne['course_id'];
                                                $data['class_id'] = $balanceLogOne['class_id'];
                                                $data['student_id'] = $balanceLogOne['student_id'];
                                                $data['hourstudy_id'] = $balanceLogOne['hourstudy_id'];
                                                $data['income_id'] = $balanceLogOne['income_id'];
                                                $data['income_price'] = $income_price;
                                                $data['income_times'] = $balanceLogOne['income_times'];
                                                $data['income_date'] = $balanceLogOne['income_date'];
                                                $data['is_confirm'] = 1;
                                                $data['transfer_updatetime'] = time();

                                                $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$balanceLogOne['hourstudy_id']}'", $data);

                                            }

                                        } else {


                                            if ($balanceLogOne['income_price'] >= $canTransPrice) {
                                                $income_price = $canTransPrice;
                                            } else {
                                                $income_price = $balanceLogOne['income_price'];
                                            }

                                            $data = array();
                                            $data['company_id'] = $balanceLogOne['company_id'];
                                            $data['companies_id'] = $studentOne['companies_id'];
                                            $data['agency_id'] = $studentOne['companies_agencyid'];
                                            $data['order_pid'] = $cmbOrderOne['order_pid'];
                                            $data['school_id'] = $balanceLogOne['school_id'];
                                            $data['coursetype_id'] = $balanceLogOne['coursetype_id'];
                                            $data['coursecat_id'] = $balanceLogOne['coursecat_id'];
                                            $data['course_id'] = $balanceLogOne['course_id'];
                                            $data['class_id'] = $balanceLogOne['class_id'];
                                            $data['student_id'] = $balanceLogOne['student_id'];
                                            $data['hourstudy_id'] = $balanceLogOne['hourstudy_id'];
                                            $data['income_id'] = $balanceLogOne['income_id'];
                                            $data['income_price'] = $income_price;
                                            $data['income_times'] = $balanceLogOne['income_times'];
                                            $data['income_date'] = $balanceLogOne['income_date'];
                                            $data['is_confirm'] = 1;
                                            $data['transfer_createtime'] = time();
                                            if($this->DataControl->getFieldOne("cmb_trans_transfer","transfer_id","hourstudy_id='{$balanceLogOne['hourstudy_id']}'")){
                                                $this->DataControl->updateData("cmb_trans_transfer","hourstudy_id='{$balanceLogOne['hourstudy_id']}'", $data);
                                            }else{
                                                $this->DataControl->insertData("cmb_trans_transfer", $data);
                                            }

                                        }

                                        $canTransPrice -= $income_price;
                                    } else {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //还没有传给招行的数据
            if ($payPrice > 0) {

                if($cmbOrderList){
                    $str=implode("','",array_column($cmbOrderList,'order_pid'));
                }else{
                    $str='';
                }

                $sql = "select a.log_playamount,a.companies_id,a.class_id,a.hourstudy_id,a.course_id,c.company_id,a.school_id,b.coursetype_id,b.coursecat_id,a.student_id,c.income_id,c.income_price,1 as income_times,FROM_UNIXTIME(c.income_confirmtime,'%Y-%m-%d') as income_date
                ,(select y.order_pid from smc_student_coursebalance_log as x,smc_payfee_order as y where x.trading_pid=y.trading_pid and x.student_id=a.student_id and x.course_id=a.course_id and x.log_id<a.log_id and x.hourstudy_id=0 and x.companies_id=a.companies_id and x.log_playclass='+' order by x.log_time desc limit 0,1) as order_pid
                from smc_student_coursebalance_log as a
                inner join smc_school_income as c on c.hourstudy_id=a.hourstudy_id and c.school_id=a.school_id
                left join smc_course as b on b.course_id=a.course_id
                where a.student_id='{$studentOne['student_id']}' and a.companies_id='{$studentOne['companies_id']}' and a.hourstudy_id>0 and a.log_time>'{$studentOne['pay_successtime']}'
                -- and not exists(select 1 from cmb_trans_transfer as x,cmb_trans_order as y where x.order_pid=y.order_pid and x.hourstudy_id=a.hourstudy_id and x.is_confirm=1 and y.is_confirm=1)   
                having order_pid not in ('".$str."')
                order by a.log_time asc
                ";

                $logList = $this->DataControl->selectClear($sql);

                foreach ($logList as $logOne) {
                    $orderArray[$logOne['order_pid']] = $logOne;
                    $balanceLogList[$logOne['order_pid']][] = $logOne;
                }

                if ($orderArray) {

                    foreach ($orderArray as $cmbOrderOne) {

                        $classOne = $this->DataControl->getOne("smc_class", "class_id='{$cmbOrderOne['class_id']}'");

                        //更新/插入 班级信息
                        $data = array();
                        $data['agency_id'] = $cmbOrderOne['companies_agencyid'];
                        $data['companies_id'] = $cmbOrderOne['companies_id'];
                        $data['class_branch'] = $classOne['class_branch'];
                        $data['class_stdate'] = $classOne['class_stdate'];
                        $data['class_enddate'] = $classOne['class_enddate'];
                        $data['update_status'] = 0;
                        $data['updatetime'] = time();

                        $cmbClassOne=$this->DataControl->getOne("cmb_trans_class","class_id='{$classOne['class_id']}'");
                        if(!$cmbClassOne || $cmbClassOne['is_confirm']!=1){
                            if (!$cmbClassOne){
                                $data['is_confirm'] = 1;
                                $data['class_id'] = $classOne['class_id'];
                                $this->DataControl->insertData('cmb_trans_class', $data);
                            }else{
                                $data['is_confirm'] = 1;
                                $this->DataControl->updateData('cmb_trans_class',"class_id='{$classOne['class_id']}'", $data);
                            }
                        }


                        $sql = "select b.*,d.coursetype_id,d.coursecat_id,c.ordercourse_buynums as order_num,c.ordercourse_totalprice
                        ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1 limit 0,1),'%Y-%m-%d') as order_date
                        ,ifnull((select sum(x.income_price) from cmb_trans_transfer as x where x.order_pid=b.order_pid and x.companies_id = x.companies_id AND x.is_confirm=1),0) as comfirmPrice
                        ,ifnull((select sum(x.log_playamount) from smc_student_coursebalance_log as x,smc_school_income as y where x.hourstudy_id=y.hourstudy_id and x.school_id=y.school_id and x.student_id=b.student_id and x.companies_id=b.companies_id and FROM_UNIXTIME(x.log_time,'%Y-%m-%d')>='{$cmbOrderOne['settle_date']}' and x.log_class=0 and x.course_id=c.course_id),0) as logAllPrice
                        from smc_payfee_order as b
                        left join smc_payfee_order_course as c on c.order_pid=b.order_pid
                        left join smc_course d on d.course_id=c.course_id
                        where b.order_pid='{$cmbOrderOne['order_pid']}' and c.course_id='{$cmbOrderOne['course_id']}'";

                        $orderOne = $this->DataControl->selectOne($sql);

                        if ($orderOne['ordercourse_totalprice'] >= $payPrice) {
                            if ($orderOne['logAllPrice'] >= $payPrice) {
                                $order_amt = $payPrice;
                            } else {
                                $order_amt = $orderOne['logAllPrice'];
                            }

                        }else{
                            if ($orderOne['logAllPrice'] >= $payPrice) {
                                $order_amt = $orderOne['ordercourse_totalprice'];
                            } else {
                                $order_amt = $orderOne['logAllPrice'];
                            }
                        }

                        $payPrice -= $order_amt;

                        $data = array();
                        $data['company_id'] = $orderOne['company_id'];
                        $data['companies_id'] = $studentOne['companies_id'];
                        $data['order_status'] = 0;
                        $data['order_status_all'] = 0;
                        $data['agency_id'] = $studentOne['companies_agencyid'];
                        $data['school_id'] = $orderOne['school_id'];
                        $data['coursetype_id'] = $orderOne['coursetype_id'];
                        $data['coursecat_id'] = $orderOne['coursecat_id'];
                        $data['course_id'] = $cmbOrderOne['course_id'];
                        $data['class_id'] = $cmbOrderOne['class_id'];
                        $data['student_id'] = $orderOne['student_id'];
                        $data['parent_mobile'] = $familyOne ? $familyOne['parenter_mobile'] : '';
                        $data['order_type'] = 'P';
                        $data['order_pid'] = $orderOne['order_pid'];
                        $data['order_amt'] = $order_amt;
                        $data['order_fee'] = 0;
                        $data['order_num'] = $orderOne['order_num'];
                        $data['settle_date'] = $orderOne['order_date'];
                        $data['order_date'] = ($orderOne['order_date'] >= $classOne['class_stdate']) ? (($orderOne['order_date'] >= $classOne['class_enddate']) ? $classOne['class_enddate'] : $orderOne['order_date']) : $classOne['class_stdate'];
                        $data['is_confirm'] = 1;

                        //根据订单信息判断是否插入或者修改订单信息
                        $cmbOrderOne = $this->DataControl->getOne("cmb_trans_order", "order_pid='{$orderOne['order_pid']}'");
                        if (!$cmbOrderOne) {
                            $insertorder = $this->DataControl->insertData('cmb_trans_order', $data);
                        } else {

                            if ($order_amt <= $cmbOrderOne['order_amt'] && $cmbOrderOne['is_confirm'] == 1 && $cmbOrderOne['order_status_all'] == 1) {
                                $insertorder = $cmbOrderOne['order_id'];
                            } else {
                                $insertorder = $this->DataControl->updateData('cmb_trans_order', "order_pid='{$orderOne['order_pid']}'",$data);
                            }
                        }

                        if ($insertorder > 0) {

                            $canTransPrice = $order_amt;

                            $sql = "select a.log_playamount,a.companies_id,a.class_id,a.hourstudy_id,a.course_id,c.company_id,a.school_id,b.coursetype_id,b.coursecat_id,a.student_id,c.income_id,c.income_price,1 as income_times,FROM_UNIXTIME(c.income_confirmtime,'%Y-%m-%d') as income_date
                                from smc_student_coursebalance_log as a
                                inner join smc_school_income as c on c.hourstudy_id=a.hourstudy_id and c.school_id=a.school_id
                                left join smc_course as b on b.course_id=a.course_id
                                where a.student_id='{$studentOne['student_id']}' and a.companies_id='{$studentOne['companies_id']}' and a.hourstudy_id>0 and FROM_UNIXTIME(a.log_time,'%Y-%m-%d')>'{$cmbOrderOne['order_date']}' and a.log_class=0
                                order by a.log_time asc";

                            $balanceLogList = $this->DataControl->selectClear($sql);

                            if ($balanceLogList) {
                                foreach ($balanceLogList as $balanceLogOne) {
                                    if ($canTransPrice > 0) {
                                        $sql = "select * 
                                                from cmb_trans_transfer as a 
                                                where a.order_pid='{$cmbOrderOne['order_pid']}'
                                                order by a.hourstudy_id asc
                                                ";
                                        $transList = $this->DataControl->selectClear($sql);

                                        if ($transList) {
                                            $transListArray = array_column($transList, null, 'hourstudy_id');
                                        } else {
                                            $transListArray = array();
                                        }

                                        //判断已经存在的transfer
                                        if ($transListArray[$balanceLogOne['hourstudy_id']]) {

                                            //如果已经同步的课耗是未确认的,则修改为已经确认,并修改剩余金额
                                            if ($transListArray[$balanceLogOne['hourstudy_id']]['is_confirm'] <= 0) {

                                                if ($balanceLogOne['income_price'] >= $canTransPrice) {

                                                    $income_price = $canTransPrice;
                                                } else {
                                                    $income_price = $balanceLogOne['income_price'];
                                                }

                                                $data = array();
                                                $data['company_id'] = $balanceLogOne['company_id'];
                                                $data['companies_id'] = $studentOne['companies_id'];
                                                $data['agency_id'] = $studentOne['companies_agencyid'];
                                                $data['order_pid'] = $cmbOrderOne['order_pid'];
                                                $data['school_id'] = $balanceLogOne['school_id'];
                                                $data['coursetype_id'] = $balanceLogOne['coursetype_id'];
                                                $data['coursecat_id'] = $balanceLogOne['coursecat_id'];
                                                $data['course_id'] = $balanceLogOne['course_id'];
                                                $data['class_id'] = $balanceLogOne['class_id'];
                                                $data['student_id'] = $balanceLogOne['student_id'];
                                                $data['hourstudy_id'] = $balanceLogOne['hourstudy_id'];
                                                $data['income_id'] = $balanceLogOne['income_id'];
                                                $data['income_price'] = $income_price;
                                                $data['income_times'] = $balanceLogOne['income_times'];
                                                $data['income_date'] = $balanceLogOne['income_date'];
                                                $data['is_confirm'] = 1;
                                                $data['transfer_updatetime'] = time();

                                                $this->DataControl->updateData("cmb_trans_transfer","hourstudy_id='{$balanceLogOne['hourstudy_id']}'",$data);

                                            }

                                        } else {

                                            if ($balanceLogOne['income_price'] >= $canTransPrice) {
                                                $income_price = $canTransPrice;
                                            } else {
                                                $income_price = $balanceLogOne['income_price'];
                                            }

                                            $data = array();
                                            $data['company_id'] = $balanceLogOne['company_id'];
                                            $data['companies_id'] = $studentOne['companies_id'];
                                            $data['agency_id'] = $studentOne['companies_agencyid'];
                                            $data['order_pid'] = $cmbOrderOne['order_pid'];
                                            $data['school_id'] = $balanceLogOne['school_id'];
                                            $data['coursetype_id'] = $balanceLogOne['coursetype_id'];
                                            $data['coursecat_id'] = $balanceLogOne['coursecat_id'];
                                            $data['course_id'] = $balanceLogOne['course_id'];
                                            $data['class_id'] = $balanceLogOne['class_id'];
                                            $data['student_id'] = $balanceLogOne['student_id'];
                                            $data['hourstudy_id'] = $balanceLogOne['hourstudy_id'];
                                            $data['income_id'] = $balanceLogOne['income_id'];
                                            $data['income_price'] = $income_price;
                                            $data['income_times'] = $balanceLogOne['income_times'];
                                            $data['income_date'] = $balanceLogOne['income_date'];
                                            $data['is_confirm'] = 1;
                                            $data['transfer_createtime'] = time();
                                            if($this->DataControl->getFieldOne("cmb_trans_transfer","transfer_id","hourstudy_id='{$balanceLogOne['hourstudy_id']}'")){
                                                $this->DataControl->updateData("cmb_trans_transfer","hourstudy_id='{$balanceLogOne['hourstudy_id']}'", $data);
                                            }else{
                                                $this->DataControl->insertData("cmb_trans_transfer", $data);
                                            }

                                        }

                                        $canTransPrice -= $income_price;
                                    } else {
                                        break;
                                    }
                                }
                            }
                        }

                    }
                }
            }

//            $data=array();
//            $data['is_handle']=1;
//            $this->DataControl->updateData("cmb_trans_speciallog","speciallog_id='{$studentOne['speciallog_id']}'",$data);
        }





    }

    function specialTransOrder(){

        $sql = "SELECT
                    a.student_id,a.class_id,a.course_id,a.companies_id,a.agency_id,
                    c.student_cnname,
                    c.student_branch,
                    a.order_pid,
                    sum( a.income_price ) AS price 
                FROM
                    cmb_trans_transfer AS a
                    LEFT JOIN cmb_trans_order AS b ON a.order_pid = b.order_pid
                    LEFT JOIN smc_student AS c ON c.student_id = a.student_id 
                WHERE
                    a.order_pid <> '' 
                    AND b.order_id IS NULL 
                    AND EXISTS ( SELECT 1 FROM cmb_trans_charge AS x WHERE x.student_id = a.student_id AND a.companies_id = x.companies_id ) 
                GROUP BY
                    a.student_id,
                    a.order_pid";

        $orderList=$this->DataControl->selectClear($sql);

        if($orderList){
            foreach($orderList as $transOrderOne){

                $sql="select b.parenter_mobile from smc_student_family as a,smc_parenter as b where a.parenter_id=b.parenter_id and a.family_isdefault=1 and a.student_id='{$transOrderOne['student_id']}'";
                $familyOne=$this->DataControl->selectOne($sql);

                $classOne = $this->DataControl->getOne("smc_class", "class_id='{$transOrderOne['class_id']}'");


                $sql = "select b.*,d.coursetype_id,d.coursecat_id,c.ordercourse_buynums as order_num,c.ordercourse_totalprice
                        ,FROM_UNIXTIME((select max(x.pay_successtime) from smc_payfee_order_pay as x where x.order_pid=b.order_pid and x.pay_issuccess=1 limit 0,1),'%Y-%m-%d') as order_date
                        from smc_payfee_order as b
                        left join smc_payfee_order_course as c on c.order_pid=b.order_pid
                        left join smc_course d on d.course_id=c.course_id
                        where b.order_pid='{$transOrderOne['order_pid']}' and c.course_id='{$transOrderOne['course_id']}'";

                $orderOne = $this->DataControl->selectOne($sql);
                if($orderOne){
                    $data = array();
                    $data['company_id'] = $orderOne['company_id'];
                    $data['companies_id'] = $transOrderOne['companies_id'];
                    $data['order_status'] = 0;
                    $data['order_status_all'] = 0;
                    $data['agency_id'] = $transOrderOne['agency_id'];
                    $data['school_id'] = $orderOne['school_id'];
                    $data['coursetype_id'] = $orderOne['coursetype_id'];
                    $data['coursecat_id'] = $orderOne['coursecat_id'];
                    $data['course_id'] = $transOrderOne['course_id'];
                    $data['class_id'] = $transOrderOne['class_id'];
                    $data['student_id'] = $orderOne['student_id'];
                    $data['parent_mobile'] = $familyOne ? $familyOne['parenter_mobile'] : '';
                    $data['order_type'] = 'P';
                    $data['order_pid'] = $orderOne['order_pid'];
                    $data['order_amt'] = $orderOne['ordercourse_totalprice'];
                    $data['order_fee'] = 0;
                    $data['order_num'] = $orderOne['order_num'];
                    $data['settle_date'] = $orderOne['order_date'];
                    $data['order_date'] = ($orderOne['order_date'] >= $classOne['class_stdate']) ? (($orderOne['order_date'] >= $classOne['class_enddate']) ? $classOne['class_enddate'] : $orderOne['order_date']) : $classOne['class_stdate'];
                    $data['is_confirm'] = 1;

                    //根据订单信息判断是否插入或者修改订单信息
                    $cmbOrderOne = $this->DataControl->getOne("cmb_trans_order", "order_pid='{$orderOne['order_pid']}'");
                    if (!$cmbOrderOne) {
                        $this->DataControl->insertData('cmb_trans_order', $data);
                    } else {

                        if ($orderOne['ordercourse_totalprice'] <= $cmbOrderOne['order_amt'] && $cmbOrderOne['is_confirm'] == 1 && $cmbOrderOne['order_status_all'] == 1) {
                            $insertorder = $cmbOrderOne['order_id'];
                        } else {
                            $this->DataControl->updateData('cmb_trans_order', "order_pid='{$orderOne['order_pid']}'",$data);
                        }
                    }
                }

            }
        }
    }
}