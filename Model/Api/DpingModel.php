<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/10/12
 * Time: 22:59
 */

namespace Model\Api;


class DpingModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    public $SignKeyMt = '86064q5ye4ecbse3';

    public $app_key = 'a4181ad88e60a9a2';
    public $app_secret = '9f2db69e548d081888ebc940279aed1520303741';

    function __construct()
    {
        parent::__construct();
    }

    //点评就接口
    function customer($open_shop_uuid,$message){
        if($sendApiArray = json_decode(stripslashes($message), "1")){
            $log = array();
            $log['company_id'] = '8888';
            $log['dpingnewleads_json'] = $message;
            $log['dpingnewleads_shopuuid'] = $open_shop_uuid;
            $log['dpingnewleads_createtime'] = time();
            if($leadsId = $this->DataControl->insertData('gmc_company_dpingnewleads', $log)){
                if(checkMobile($sendApiArray['mobile'])){
                    $paramArray = array();
                    $paramArray['company_id'] = '8888';
                    $paramArray['mobile'] = $sendApiArray['mobile'];
                    if(trim($open_shop_uuid) !==''){
                        $dpingshopOne = $this->DataControl->getFieldOne("gmc_company_dpingshop", "school_branch,dpingshop_branchname,dpingshop_isenterschool"
                            , "dpingshop_uuid = '{$open_shop_uuid}' AND school_branch <> '' and company_id = '8888'");
                        if($dpingshopOne){
                            $paramArray['school_branch'] = $dpingshopOne['school_branch'];
                            $paramArray['isenterschool'] = $dpingshopOne['dpingshop_isenterschool'];
                        }
                    }else{
                        $paramArray['school_branch'] = '';
                        $paramArray['isenterschool'] = '1';
                    }
                    if(trim($open_shop_uuid) == ''){
                        $paramArray['buy_time'] = strtotime($sendApiArray['leads_time']);
                        $paramArray['remark'] = "{$sendApiArray['platform']}咨询留下客资信息，客资备注{$sendApiArray['remark']}；";
                        $resultApi = $this->addClient($paramArray);
                        $log = array();
                        $log['dpingnewleads_client_status'] = $resultApi['status'];
                        $log['dpingnewleads_errortip'] = $resultApi['errtip'];
                        $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                    }else{
                        if($dpingshopOne){
                            $schoolOne = $this->DataControl->getFieldOne("smc_school"
                                , "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}' and school_isclose = '0' ");
                            if($schoolOne){
                                $paramArray['buy_time'] = strtotime($sendApiArray['leads_time']);
                                if($dpingshopOne['dpingshop_branchname'] !==''){
                                    $paramArray['remark'] = "{$sendApiArray['platform']}用户在{$dpingshopOne['dpingshop_branchname']}咨询留下客资信息，客资备注{$sendApiArray['remark']}；";
                                }else{
                                    $paramArray['remark'] = "{$sendApiArray['platform']}咨询留下客资信息，客资备注{$sendApiArray['remark']}；";
                                }
                                $resultApi = $this->addClient($paramArray);
                                $log = array();
                                $log['dpingnewleads_client_status'] = $resultApi['status'];
                                $log['dpingnewleads_errortip'] = $resultApi['errtip'];
                                $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                            }else{
                                request_by_curl("https://kmcapi.kedingdang.com/Api/cmsLeads", dataEncode($paramArray),"POST");
                                $log = array();
                                $log['dpingnewleads_client_status'] = '-1';
                                $log['dpingnewleads_errortip'] = '直营园名单异步通知中！';
                                $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                            }
                        }else{
                            $log = array();
                            $log['dpingnewleads_client_status'] = '-1';
                            $log['dpingnewleads_errortip'] = '非直营校区客资信息，无需处理！';
                            $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                        }
                    }
                }else{
                    $log = array();
                    $log['dpingnewleads_client_status'] = '-1';
                    $log['dpingnewleads_errortip'] = '客户手机号不正确';
                    $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                }
                return true;
            }else{
                $log = array();
                $log['company_id'] = '8888';
                $log['dpingnewleads_json'] = "插入失败";
                $log['dpingnewleads_shopuuid'] = "插入失败";
                $log['dpingnewleads_createtime'] = time();
                $this->DataControl->insertData('gmc_company_dpingnewleads', $log);
                $this->errortip = "顾客信息记录错误";
                return false;
            }
        }else{
            $log = array();
            $log['company_id'] = '8888';
            $log['dpingnewleads_json'] = "API记录错误";
            $log['dpingnewleads_shopuuid'] = "错误记录";
            $log['dpingnewleads_createtime'] = time();
            $this->DataControl->insertData('gmc_company_dpingnewleads', $log);
            $this->errortip = "顾客信息返回信息不是Json格式";
            return false;
        }
    }

    //点评新接口
    function customercenter($open_shop_uuid,$message){
        if($sendApiArray = json_decode(stripslashes($message), "1")){
            $log = array();
            $log['company_id'] = '8888';
            $log['dpingnewleads_json'] = $message;
            $log['dpingnewleads_shopuuid'] = $open_shop_uuid;
            $log['dpingnewleads_createtime'] = time();
            if($leadsId = $this->DataControl->insertData('gmc_company_dpingnewleads', $log)){
                if(checkMobile($sendApiArray['phone'])){
                    $paramArray = array();
                    $paramArray['company_id'] = '8888';
                    $paramArray['mobile'] = $sendApiArray['phone'];
                    if(trim($open_shop_uuid) !==''){
                        $dpingshopOne = $this->DataControl->getFieldOne("gmc_company_dpingshop", "school_branch,dpingshop_branchname,dpingshop_isenterschool"
                            , "dpingshop_uuid = '{$open_shop_uuid}' AND school_branch <> '' and company_id = '8888'");
                        if($dpingshopOne){
                            $paramArray['school_branch'] = $dpingshopOne['school_branch'];
                            $paramArray['isenterschool'] = $dpingshopOne['dpingshop_isenterschool'];
                        }
                    }else{
                        $paramArray['school_branch'] = '';
                        $paramArray['isenterschool'] = '1';
                    }
                    if(trim($open_shop_uuid) == ''){
//                        $paramArray['buy_time'] = strtotime($sendApiArray['lastLeadsAddTime']/1000);
                        $paramArray['buy_time'] = ceil($sendApiArray['lastLeadsAddTime']/1000);//241111 修改
                        if($sendApiArray['platform']=='1'){
                            $platform = "点评";
                        }else{
                            $platform = "美团";
                        }
                        $paramArray['remark'] = "News:{$platform}咨询留下客资信息，客资备注{$sendApiArray['merchantRemark']}；";
                        $resultApi = $this->addClient($paramArray);
                        $log = array();
                        $log['dpingnewleads_client_status'] = $resultApi['status'];
                        $log['dpingnewleads_errortip'] = $resultApi['errtip'];
                        $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                    }else{
                        if($dpingshopOne){
                            $schoolOne = $this->DataControl->getFieldOne("smc_school"
                                , "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}'");
                            if($schoolOne){
//                                $paramArray['buy_time'] = strtotime($sendApiArray['lastLeadsAddTime']/1000);
                                $paramArray['buy_time'] = ceil($sendApiArray['lastLeadsAddTime']/1000);//241111 修改
                                if($sendApiArray['platform']=='1'){
                                    $platform = "点评";
                                }else{
                                    $platform = "美团";
                                }
                                if($dpingshopOne['dpingshop_branchname'] !==''){
                                    $paramArray['remark'] = "News:{$platform}用户在{$dpingshopOne['dpingshop_branchname']}咨询留下客资信息，客资备注{$sendApiArray['merchantRemark']}；";
                                }else{
                                    $paramArray['remark'] = "News:{$platform}咨询留下客资信息，客资备注{$sendApiArray['merchantRemark']}；";
                                }
                                $resultApi = $this->addClient($paramArray);
                                $log = array();
                                $log['dpingnewleads_client_status'] = $resultApi['status'];
                                $log['dpingnewleads_errortip'] = $resultApi['errtip'];
                                $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                            }else{
                                request_by_curl("https://kmcapi.kedingdang.com/Api/cmsLeads", dataEncode($paramArray),"POST");
                                $log = array();
                                $log['dpingnewleads_client_status'] = '-1';
                                $log['dpingnewleads_errortip'] = '直营园名单异步通知中！';
                                $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                            }
                        }else{
                            $log = array();
                            $log['dpingnewleads_client_status'] = '-1';
                            $log['dpingnewleads_errortip'] = '非直营校区客资信息，无需处理！';
                            $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                        }
                    }
                }else{
                    $log = array();
                    $log['dpingnewleads_client_status'] = '-1';
                    $log['dpingnewleads_errortip'] = '客户手机号不正确';
                    $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$leadsId}'", $log);
                }
                return true;
            }else{
                $log = array();
                $log['company_id'] = '8888';
                $log['dpingnewleads_json'] = "插入失败";
                $log['dpingnewleads_shopuuid'] = "插入失败";
                $log['dpingnewleads_createtime'] = time();
                $this->DataControl->insertData('gmc_company_dpingnewleads', $log);
                $this->errortip = "顾客信息记录错误";
                return false;
            }
        }else{
            $log = array();
            $log['company_id'] = '8888';
            $log['dpingnewleads_json'] = "API记录错误";
            $log['dpingnewleads_shopuuid'] = "错误记录";
            $log['dpingnewleads_createtime'] = time();
            $this->DataControl->insertData('gmc_company_dpingnewleads', $log);
            $this->errortip = "顾客信息返回信息不是Json格式";
            return false;
        }
    }


    function dpingLeads(){
        $newleadsArray = $this->DataControl->selectClear("select l.* FROM gmc_company_dpingnewleads AS l
WHERE l.company_id = '8888' AND l.dpingnewleads_client_status = '0'");
        if($newleadsArray){
            foreach($newleadsArray AS $newleadsOne){
                $sendApiArray = json_decode(stripslashes($newleadsOne['dpingnewleads_json']), "1");
                if(checkMobile($sendApiArray['mobile'])){
                    $paramArray = array();
                    $paramArray['company_id'] = '8888';
                    $paramArray['mobile'] = $sendApiArray['mobile'];
                    if(trim($newleadsOne['dpingnewleads_shopuuid']) !==''){
                        $schoolOne = $this->DataControl->getFieldOne("gmc_company_dpingshop", "school_branch,dpingshop_branchname"
                            , "dpingshop_uuid = '{$newleadsOne['dpingnewleads_shopuuid']}' AND school_branch <> '' and company_id = '8888'");
                        if($schoolOne){
                            $paramArray['school_branch'] = $schoolOne['school_branch'];
                        }
                    }else{
                        $paramArray['school_branch'] = '';
                    }
                    if(trim($newleadsOne['dpingnewleads_shopuuid']) == '' || $schoolOne){
                        $paramArray['buy_time'] = strtotime($sendApiArray['leads_time']);
                        if($schoolOne['dpingshop_branchname'] !==''){
                            $paramArray['remark'] = "{$sendApiArray['platform']}用户在{$schoolOne['dpingshop_branchname']}咨询留下客资信息，客资备注{$sendApiArray['remark']}；";
                        }else{
                            $paramArray['remark'] = "{$sendApiArray['platform']}咨询留下客资信息，客资备注{$sendApiArray['remark']}；";
                        }
                        $resultApi = $this->addClient($paramArray);
                        $log = array();
                        $log['dpingnewleads_client_status'] = $resultApi['status'];
                        $log['dpingnewleads_errortip'] = $resultApi['errtip'];
                        $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$newleadsOne['dpingnewleads_id']}'", $log);
                    }else{
                        $log = array();
                        $log['dpingnewleads_client_status'] = '-1';
                        $log['dpingnewleads_errortip'] = '非直营校区客资信息，无需处理！';
                        $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$newleadsOne['dpingnewleads_id']}'", $log);
                    }
                }else{
                    $log = array();
                    $log['dpingnewleads_client_status'] = '-1';
                    $log['dpingnewleads_errortip'] = '客户手机号不正确';
                    $this->DataControl->updateData('gmc_company_dpingnewleads',"dpingnewleads_id = '{$newleadsOne['dpingnewleads_id']}'", $log);
                }
            }
        }
    }

    //获取点评Token
    function Authcode($authCode){
        $paramto = array();
        $paramto['app_key'] = $this->app_key;
        $paramto['app_secret'] = $this->app_secret;
        $paramto['auth_code'] = $authCode;
        $paramto['grant_type'] = "authorization_code";
        $paramto['redirect_url'] = "https://crmapi.kedingdang.com/Api/dpingAuthcode";
        $sendApiSting = request_by_curl("https://openapi.dianping.com/router/oauth/token",dataEncode($paramto), "POST", array());
        if($sendApiArray = json_decode($sendApiSting, "1")){
            if($sendApiArray['code'] == '200'){
                $log = array();
                $log['company_id'] = '8888';
                $log['dpingcode_json'] = addslashes($sendApiSting);
                $log['dpingcode_expiresin'] = $sendApiArray['expires_in'];
                $log['dpingcode_createtime'] = time();
                if($this->DataControl->insertData('gmc_company_dpingcode', $log)){
                    return $sendApiArray['access_token'];
                }else{
                    $this->errortip = "授权信息记录错误";
                    return false;
                }
            }else{
                $this->errortip = "授权信息返回信息错误";
                return false;
            }
        }else{
            $this->errortip = "授权信息返回信息不是Json格式";
            return false;
        }
    }
    //刷新点评token
    function Refreshtoken($company_id){
        $dpingcodeOne = $this->DataControl->getFieldOne("gmc_company_dpingcode","dpingcode_json","company_id = '{$company_id}'","Order BY dpingcode_id DESC limit 0,1");
        $dpingcodeArray = json_decode($dpingcodeOne['dpingcode_json'], "1");

        $paramto = array();
        $paramto['app_key'] = $this->app_key;
        $paramto['app_secret'] = $this->app_secret;
        $paramto['refresh_token'] = $dpingcodeArray['refresh_token'];
        $paramto['grant_type'] = "refresh_token";
        $sendApiSting = request_by_curl("https://openapi.dianping.com/router/oauth/token",dataEncode($paramto), "POST", array());
        if($sendApiArray = json_decode($sendApiSting, "1")){
            if($sendApiArray['code'] == '200'){
                $log = array();
                $log['company_id'] = '8888';
                $log['dpingcode_json'] = addslashes($sendApiSting);
                $log['dpingcode_expiresin'] = $sendApiArray['expires_in'];
                $log['dpingcode_createtime'] = time();
                if($this->DataControl->insertData('gmc_company_dpingcode', $log)){
                    return $sendApiArray['access_token'];
                }else{
                    $this->errortip = "授权刷新信息记录错误";
                    return false;
                }
            }else{
                $this->errortip = "授权刷新信息返回信息错误";
                return false;
            }
        }else{
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }
    }

    function getnewToken($company_id){
        $dpingcodeOne = $this->DataControl->getFieldOne("gmc_company_dpingcode","dpingcode_json,dpingcode_createtime,dpingcode_expiresin"
            ,"company_id = '{$company_id}'","Order BY dpingcode_id DESC limit 0,1");
        if($dpingcodeOne){
            if(($dpingcodeOne['dpingcode_createtime']+$dpingcodeOne['dpingcode_expiresin']) < time()+600){
                return $this->Refreshtoken($company_id);
            }else{
                $dpingcodeArray = json_decode($dpingcodeOne['dpingcode_json'], "1");
                return $dpingcodeArray['access_token'];
            }
        }else{
            return $this->Authcode($company_id);
        }

    }


    //查询授权范围
    function Sessionquery($company_id){
        $paramto = array();
        $paramto['app_key'] = $this->app_key;
        $paramto['session'] = $this->getnewToken($company_id);
        $paramto['sign_method'] = 'MD5';
        $paramto['timestamp'] = date("Y-m-d H:i:s");
        $paramto['v'] = "1";
        $paramto['format'] = "json";
        $paramto['sign'] = $this->getsign($paramto);
        $sendApiSting = request_by_curl("https://openapi.dianping.com/router/oauth/session/query",dataEncode($paramto), "GET", array());
        if($sendApiArray = json_decode($sendApiSting, "1")){
            if($sendApiArray['code'] == '200'){
                return $sendApiArray;
            }else{
                $this->errortip = $sendApiArray['msg'];
                return false;
            }
        }else{
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }
    }

    //获取店铺
    function Sessionscope($company_id,$offset = 0){
        $dpingcodeOne = $this->DataControl->getFieldOne("gmc_company_dpingcode","dpingcode_json","company_id = '{$company_id}'","Order BY dpingcode_id DESC limit 0,1");
        $dpingcodeArray = json_decode($dpingcodeOne['dpingcode_json'], "1");
        $paramto = array();
        $paramto['app_key'] = $this->app_key;
        $paramto['session'] = $this->getnewToken($company_id);
        $paramto['bid'] = $dpingcodeArray['bid'];
        $paramto['offset'] = $offset;
        $paramto['limit'] = "400";
        $paramto['sign_method'] = 'MD5';
        $paramto['timestamp'] = date("Y-m-d H:i:s");
        $paramto['v'] = "1";
        $paramto['format'] = "json";
        $paramto['sign'] = $this->getsign($paramto);
        $sendApiSting = request_by_curl("https://openapi.dianping.com/router/oauth/session/scope",dataEncode($paramto), "GET", array());
        if($sendApiArray = json_decode($sendApiSting, "1")){
            if($sendApiArray['code'] == '200'){
                foreach($sendApiArray['data'] as $dpingshopOne){
                    if(!$this->DataControl->getFieldOne("gmc_company_dpingshop","dpingshop_id","dpingshop_uuid = '{$dpingshopOne['open_shop_uuid']}'")){
                        $log = array();
                        $log['company_id'] = '8888';
                        $log['dpingshop_uuid'] = $dpingshopOne['open_shop_uuid'];
                        $log['dpingshop_name'] = $dpingshopOne['shopname'];
                        $log['dpingshop_branchname'] = $dpingshopOne['branchname'];
                        $log['dpingshop_address'] = $dpingshopOne['shopaddress'];
                        $log['dpingshop_cityname'] = $dpingshopOne['cityname'];
                        $this->DataControl->insertData('gmc_company_dpingshop', $log);
                    }else{
                        $log = array();
                        $log['dpingshop_name'] = $dpingshopOne['shopname'];
                        $log['dpingshop_branchname'] = $dpingshopOne['branchname'];
                        $log['dpingshop_address'] = $dpingshopOne['shopaddress'];
                        $log['dpingshop_cityname'] = $dpingshopOne['cityname'];
                        $this->DataControl->updateData('gmc_company_dpingshop',"dpingshop_uuid = '{$dpingshopOne['open_shop_uuid']}'", $log);
                    }
                }
                return $sendApiArray;
            }else{
                $this->errortip = $sendApiArray['msg'];
                return false;
            }
        }else{
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }
    }

    function Queryorder($company_id,$p=1){
        $paramto = array();
        $paramto['session'] = $this->getnewToken($company_id);
        $paramto['app_key'] = $this->app_key;
        $paramto['timestamp'] = date("Y-m-d H:i:s");
        $paramto['format'] = "json";
        $paramto['v'] = "1";
        $paramto['sign_method'] = 'MD5';

        $paramto['order_type'] = '1';//date("Y-m-d",strtotime("-1 day"))
        $paramto['buy_success_time_from'] = $this->get_data_format(date("Y-m-d",strtotime("-1 day")).' 00:00:01');
        $paramto['buy_success_time_to'] = $this->get_data_format(date("Y-m-d") . ' 23:59:59');
        $paramto['page_no'] = $p;
        $paramto['limit'] = '50';
        $paramto['sign'] = $this->getsign($paramto);
        $sendApiSting = request_by_curl("https://openapi.dianping.com/router/order/queryorder",dataEncode($paramto), "GET", array());
        if($sendApiArray = json_decode($sendApiSting, "1")){
            if($sendApiArray['code'] == '200'){
                if(is_array($sendApiArray['data'])){
                    foreach($sendApiArray['data'] as $dpingorderOne){
                        $orderOne = $this->DataControl->getFieldOne("gmc_company_dpingorder","dpingorder_id,dpingorder_client_status","dpingorder_id = '{$dpingorderOne['order_id']}'");
                        if(!$orderOne){
                            $log = array();
                            $log['company_id'] = '8888';
                            $log['dpingorder_id'] = $dpingorderOne['order_id'];
                            $log['dpingorder_shopuuid'] = $dpingorderOne['open_shop_uuid'];
                            $log['dpingorder_shopname'] = $dpingorderOne['shop_name'];
                            $log['dpingorder_totalamount'] = $dpingorderOne['total_amount'];
                            $log['dpingorder_mobile'] = $dpingorderOne['mobile'];
                            $log['dpingorder_productname'] = $dpingorderOne['product_name'];
                            $log['dpingorder_buy_time'] = $dpingorderOne['buy_success_time']/1000;
                            $log['dpingorder_status'] = $dpingorderOne['status'];//订单状态：0:可使用 1:已完成（已全部使用/退款）  2:已过期（取决于业务方是否有该状态
                            $log['dpingorder_refund_status'] = $dpingorderOne['refund_status'];//退款状态：-1:非退款订单 1:退款中 2:退款成功 3:退款失败
                            $log['dpingorder_createtime'] = time();
                            $orderOne = array();
                            $orderOne['dpingorder_id'] = $this->DataControl->insertData('gmc_company_dpingorder', $log);
                            $orderOne['dpingorder_client_status'] = '0';
                        }else{
                            $log = array();
                            $log['dpingorder_shopuuid'] = $dpingorderOne['open_shop_uuid'];
                            $log['dpingorder_shopname'] = $dpingorderOne['shop_name'];
                            $log['dpingorder_status'] = $dpingorderOne['status'];
                            $log['dpingorder_mobile'] = $dpingorderOne['mobile'];
                            $log['dpingorder_refund_status'] = $dpingorderOne['refund_status'];
                            $this->DataControl->updateData('gmc_company_dpingorder',"dpingorder_id = '{$orderOne['dpingorder_id']}'", $log);
                        }

                        if($orderOne['dpingorder_client_status'] == '0' && checkMobile($dpingorderOne['mobile'])){
                            $paramArray = array();
                            $paramArray['company_id'] = '8888';
                            $paramArray['mobile'] = $dpingorderOne['mobile'];
                            if(trim($dpingorderOne['open_shop_uuid']) !==''){
                                $schoolOne = $this->DataControl->getFieldOne("gmc_company_dpingshop", "school_branch"
                                    , "dpingshop_uuid = '{$dpingorderOne['open_shop_uuid']}' AND school_branch <> '' and company_id = '8888'");
                                if($schoolOne){
                                    $paramArray['school_branch'] = $schoolOne['school_branch'];
                                }
                            }else{
                                $paramArray['school_branch'] = '';
                            }
                            if(trim($dpingorderOne['open_shop_uuid']) == '' || $schoolOne){
                                $paramArray['buy_time'] = $dpingorderOne['buy_success_time']/1000;
                                $paramArray['remark'] = "大众点评在{$dpingorderOne['shop_name']}购买{$dpingorderOne['product_name']}，消费金额{$dpingorderOne['total_amount']}；";
                                $resultApi = $this->addClient($paramArray);
                                $log = array();
                                $log['dpingorder_client_status'] = $resultApi['status'];
                                $log['dpingorder_errortip'] = $resultApi['errtip'];
                                $this->DataControl->updateData('gmc_company_dpingorder',"dpingorder_id = '{$orderOne['dpingorder_id']}'", $log);
                            }else{
                                $log = array();
                                $log['dpingorder_client_status'] = '-1';
                                $log['dpingorder_errortip'] = '非直营校区客资信息，不处理';
                                $this->DataControl->updateData('gmc_company_dpingorder',"dpingorder_id = '{$orderOne['dpingorder_id']}'", $log);
                            }
                        }
                    }
                    return count($sendApiArray['data']);
                }else{
                    return 0;
                }
            }else{
                $this->errortip = $sendApiArray['msg'];
                return false;
            }
        }else{
            $this->errortip = "授权刷新信息返回信息不是Json格式";
            return false;
        }
    }

    //大众点评参数
    function addClient($paramArray){

        $paramArray['buy_time'] = (($paramArray['buy_time']>time())?time():$paramArray['buy_time']);
        $paramArray['buy_time'] = ($paramArray['buy_time']?$paramArray['buy_time']:time());

        $channelOne = $this->DataControl->getFieldOne("crm_code_channel","channel_id,channel_medianame,channel_intention_level","company_id = '{$paramArray['company_id']}' AND channel_name = '大众点评'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id,staffer_cnname","company_id = '{$paramArray['company_id']}' AND account_class = '1'");
        if($channelOne){
            $familyOne = $this->DataControl->selectOne("SELECT f.family_id,s.student_cnname,s.student_branch FROM smc_student_family AS f ,smc_student AS s
 WHERE f.student_id = s.student_id AND f.family_mobile = '{$paramArray['mobile']}' and s.company_id = '{$paramArray['company_id']}' ORDER BY s.student_branch DESC limit 0,1");
            if ($familyOne) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "检测到在校生家长信息,学员姓名：{$familyOne['student_cnname']}，编号：{$familyOne['student_branch']}，无法添加CRM学员信息！";
                return $result;
            }
            if (!checkMobile($paramArray['mobile'])) {
                $result = array();
                $result['status'] = "0";
                $result['errtip'] = "手机号码加密，无法获取准确手机号，暂不处理！";
                return $result;
            }

            if($paramArray['school_branch'] !==''){
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch = '{$paramArray['school_branch']}' and company_id = '{$paramArray['company_id']}' and school_isclose = '0' and school_istest = '0' ");
                if(!$schoolOne){
//                    $result = array();
//                    $result['status'] = "-1";
//                    $result['errtip'] = "校区编号{{$paramArray['school_branch']}}不存在，请检查校区编号！";
//                    return $result;
                    $schoolOne['school_id'] = 0;
                }
            }else{
                $schoolOne['school_id'] = 0;
            }

            /*if($this->DataControl->getFieldOne("crm_client", "client_id,channel_id"
                , "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}' and channel_id = '{$channelOne['channel_id']}'")){
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "渠道名单信息已存在，禁止重复激活！";
                return $result;
            }*/

            $impotParam = array();
            $impotParam['company_id'] = $paramArray['company_id'];
            if(isset($schoolOne['school_id'])){
                $impotParam['school_id'] = $schoolOne['school_id'];
            }

            $ClientVerify = new \Model\Crm\ClientCreatedModel($impotParam);
            $paramOne['company_id'] = $paramArray['company_id'];
            $paramOne['client_cnname'] = '点评用户';
            $paramOne['channel_id'] = $channelOne['channel_id'];
            $paramOne['client_source'] = $channelOne['channel_medianame'];
            $paramOne['client_remark'] = $paramArray['remark'];
            $paramOne['client_isfromgmc'] = '1';
            $paramOne['client_mobile'] = $paramArray['mobile'];
            $paramOne['client_isnewtip'] = '1';
            $paramOne['client_updatetime'] = $paramArray['buy_time'];
            $paramOne['client_createtime'] = $paramArray['buy_time'];
            if (!$ClientVerify->CrmClientVerify($paramOne)) {
                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = $ClientVerify->errortip;
                return $result;
            }

            //客户不存在
            if(!$this->DataControl->getFieldOne("crm_client", "client_id,client_tracestatus", "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}'")){
                $data = array();
                $data['client_cnname'] = '点评用户';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['company_id'] = $paramArray['company_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_remark'] = $paramArray['remark'];
                $data['client_isfromgmc'] = '1';
                $data['client_mobile'] = $paramArray['mobile'];
                if($channelOne['channel_intention_level'] > 0){
                    $data['client_intention_level'] = $channelOne['channel_intention_level'];
                    $data['client_intention_maxlevel'] = $channelOne['channel_intention_level'];
                }
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $data['client_createtime'] = $paramArray['buy_time'];
                $client_id = $this->DataControl->insertData("crm_client", $data);

                //添加名单状态记录
                $Model = new  \Model\Api\CalloutModel($paramOne);
                $Model->addClientTimerecord($paramArray['company_id'],$schoolOne['school_id'],$client_id,1,-1,"大众点评三方对接录入");

                if ($schoolOne['school_id'] > 0 && $paramArray['isenterschool'] !== '0') {
                    $datas = array();
                    $datas['client_id'] = $client_id;
                    $datas['school_id'] = $schoolOne['school_id'];
                    $datas['company_id'] = $paramArray['company_id'];
                    $datas['schoolenter_createtime'] = time();
                    $datas['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $datas);
                }
                $trackData = array();
                $trackData['client_id'] = $client_id;
                $trackData['marketer_id'] = '0';
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入,系统新建客户信息,直接入校：{$paramArray['isenterschool']}条;";
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单创建成功！";
                return $result;
            }
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "名单通过校验，但未正常激活！";
            return $result;
        }else{
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "未检测大众点评渠道无法处理";
            return $result;
        }
    }

    //20250218 把之前的注释拿下来了
    /*$isSchoolexit = $this->DataControl->getFieldOne("crm_client", "client_id,channel_id"
        , "client_mobile = '{$paramArray['mobile']}' and company_id = '{$paramArray['company_id']}' and client_tracestatus >= '0' AND client_tracestatus < '4'");
    if($isSchoolexit){
        if($this->DataControl->getFieldOne("crm_client_schoolenter", "school_id", "client_id = '{$isSchoolexit['client_id']}' AND is_enterstatus = '1' and school_id = '{$schoolOne['school_id']}'")){
            //1.5个月
            $endtimes = $paramArray['buy_time'] - 3600*24*45;
            if(!$this->DataControl->getFieldOne("crm_client_track", "track_id", "client_id = '{$isSchoolexit['client_id']}' and school_id = '{$schoolOne['school_id']}' AND track_createtime > '{$endtimes}'")){
                $data = array();
                $data['client_isfromgmc'] = '1';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $data['client_createtime'] = $paramArray['buy_time'];
                if($this->DataControl->updateData("crm_client", "client_id = '{$isSchoolexit['client_id']}'", $data)){
                    //渠道变更信息
                    $channello = array();
                    $channello['company_id'] = $paramArray['company_id'];
                    $channello['client_id'] = $isSchoolexit['client_id'];
                    $channello['from_channel_id'] = $isSchoolexit['channel_id'];
                    $channello['to_channel_id'] = $channelOne['channel_id'];
                    $channello['channellog_note'] = "名单在1.5个月内校区未跟踪，直接变更渠道！";
                    $channello['channellog_createtime'] = time();
                    $this->DataControl->insertData("crm_client_channellog", $channello);

                    //名单入校时间激活
                    $data = array();
                    $data['schoolenter_updatetime'] = time();
                    $data['schoolenter_createtime'] = time();
                    $this->DataControl->updateData("crm_client_schoolenter", "client_id = '{$isSchoolexit['client_id']}' and school_id = '{$schoolOne['school_id']}'", $data);
                }

                $trackData = array();
                $trackData['client_id'] = $isSchoolexit['client_id'];
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_id'] = '0';
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入，老名单被渠道激活,渠道名单备注：".$paramArray['remark'];
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单留客资信息成功！";
                return $result;
            }else{
                $data = array();
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $this->DataControl->updateData("crm_client", "client_id = '{$isSchoolexit['client_id']}'", $data);

                $trackData = array();
                $trackData['client_id'] = $isSchoolexit['client_id'];
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_id'] = '0';
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入，老名单在渠道当前校留信息,渠道备注：".$paramArray['remark'];
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "当前客户在校区正常沟通，无法变更渠道信息,旧渠道{$isSchoolexit['channel_id']}！";
                return $result;
            }
        }else{
            if($this->DataControl->getFieldOne("crm_client_schoolenter", "school_id", "client_id = '{$isSchoolexit['client_id']}' AND is_enterstatus = '1'")){
                $data = array();
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $this->DataControl->updateData("crm_client", "client_id = '{$isSchoolexit['client_id']}'", $data);

                $trackData = array();
                $trackData['client_id'] = $isSchoolexit['client_id'];
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_id'] = '0';
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入，老名单在渠道其他校留信息,渠道备注：".$paramArray['remark'];
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "-1";
                $result['errtip'] = "当前客户在其他校区正常沟通，无法变更渠道信息,旧渠道{$isSchoolexit['channel_id']}！";
                return $result;
            }else{
                $data = array();
                $data['client_tracestatus'] = '0';
                $data['client_distributionstatus'] = '0';
                $data['client_isfromgmc'] = '1';
                $data['channel_id'] = $channelOne['channel_id'];
                $data['client_source'] = $channelOne['channel_medianame'];
                $data['client_isnewtip'] = '1';
                $data['client_updatetime'] = $paramArray['buy_time'];
                $data['client_createtime'] = $paramArray['buy_time'];
                if($this->DataControl->updateData("crm_client", "client_id = '{$isSchoolexit['client_id']}'", $data)){
                    //渠道变更信息
                    $channello = array();
                    $channello['company_id'] = $paramArray['company_id'];
                    $channello['client_id'] = $isSchoolexit['client_id'];
                    $channello['from_channel_id'] = $isSchoolexit['channel_id'];
                    $channello['to_channel_id'] = $channelOne['channel_id'];
                    $channello['channellog_note'] = "系统将历史名单重新激活，直接变更渠道！";
                    $channello['channellog_createtime'] = time();
                    $this->DataControl->insertData("crm_client_channellog", $channello);
                }


                if ($schoolOne['school_id'] > 0 && $paramArray['isenterschool'] !=='0') {
                    if(!$this->DataControl->getFieldOne("crm_client_schoolenter", "school_id", "client_id = '{$isSchoolexit['client_id']}' and school_id = '{$schoolOne['school_id']}'")){
                        $datas = array();
                        $datas['client_id'] = $isSchoolexit['client_id'];
                        $datas['school_id'] = $schoolOne['school_id'];
                        $datas['company_id'] = $paramArray['company_id'];
                        $datas['schoolenter_createtime'] = time();
                        $datas['schoolenter_updatetime'] = time();
                        $this->DataControl->insertData('crm_client_schoolenter', $datas);
                    }else{
                        $schoolenter = array();
                        $schoolenter['is_enterstatus'] = '1';
                        $schoolenter['schoolenter_updatetime'] = time();
                        $schoolenter['schoolenter_createtime'] = time();//重启激活入校时间
                        $this->DataControl->updateData('crm_client_schoolenter', "client_id = '{$isSchoolexit['client_id']}'", $schoolenter);
                    }
                }

                $trackData = array();
                $trackData['client_id'] = $isSchoolexit['client_id'];
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_id'] = '0';
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入，系统将历史名单重新激活，直接入校：{$paramArray['isenterschool']}条,名单备注：".$paramArray['remark'];
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单重新激活成功！";
                return $result;
            }
        }
    }else{
        $isSchollnews = $this->DataControl->selectOne("select c.client_id from crm_client as c
where c.client_mobile = '{$paramArray['mobile']}' and c.company_id = '{$paramArray['company_id']}' and c.client_tracestatus < '0'");
        if($isSchollnews){
            $data = array();
            $data['client_tracestatus'] = '0';
            $data['client_distributionstatus'] = '0';
            $data['client_isfromgmc'] = '1';
            $data['channel_id'] = $channelOne['channel_id'];
            $data['client_source'] = $channelOne['channel_medianame'];
            $data['client_isnewtip'] = '1';
            $data['client_updatetime'] = $paramArray['buy_time'];
            $data['client_createtime'] = $paramArray['buy_time'];
            if($this->DataControl->updateData("crm_client", "client_id = '{$isSchollnews['client_id']}'", $data)){
                //渠道变更信息
                $channello = array();
                $channello['company_id'] = $paramArray['company_id'];
                $channello['client_id'] = $isSchoolexit['client_id'];
                $channello['from_channel_id'] = $isSchoolexit['channel_id'];
                $channello['to_channel_id'] = $channelOne['channel_id'];
                $channello['channellog_note'] = "名单在无意向/无效，重新激活，直接变更渠道！";
                $channello['channellog_createtime'] = time();
                $this->DataControl->insertData("crm_client_channellog", $channello);
            }
            if(!$schoolOne){
                $trackData = array();
                $trackData['client_id'] = $isSchollnews['client_id'];
                $trackData['school_id'] = $schoolOne['school_id'];
                $trackData['marketer_id'] = '0';
                $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                $trackData['track_validinc'] = 1;
                $trackData['track_followmode'] = 4;
                $trackData['track_linktype'] = '名单导入';
                $trackData['track_note'] = "API导入，系统自动将无意向客户转为招生有效名单,名单备注：".$paramArray['remark'];
                $trackData['track_createtime'] = time();
                $trackData['track_type'] = 1;
                $trackData['track_initiative'] = 1;
                $this->DataControl->insertData('crm_client_track', $trackData);

                $result = array();
                $result['status'] = "1";
                $result['errtip'] = "名单激活成功！";
                return $result;
            }else{
                if(!$this->DataControl->getFieldOne("crm_client_schoolenter", "school_id", "client_id = '{$isSchollnews['client_id']}' and school_id = '{$schoolOne['school_id']}'")){
                    $schoolenter = array();
                    $schoolenter['is_enterstatus'] = '-1';
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->updateData('crm_client_schoolenter', "client_id = '{$isSchollnews['client_id']}'", $schoolenter);

                    $schoolenter = array();
                    $schoolenter['company_id'] = $paramArray['company_id'];
                    $schoolenter['client_id'] = $isSchollnews['client_id'];
                    $schoolenter['school_id'] = $schoolOne['school_id'];
                    $schoolenter['is_enterstatus'] = '1';
                    $schoolenter['schoolenter_createtime'] = time();
                    $schoolenter['schoolenter_updatetime'] = time();
                    $this->DataControl->insertData('crm_client_schoolenter', $schoolenter);

                    $trackData = array();
                    $trackData['client_id'] = $isSchollnews['client_id'];
                    $trackData['school_id'] = $schoolOne['school_id'];
                    $trackData['marketer_id'] = '0';
                    $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                    $trackData['track_validinc'] = 1;
                    $trackData['track_followmode'] = 4;
                    $trackData['track_linktype'] = '名单导入';
                    $trackData['track_note'] = "API导入，系统自动将无意向客户转为招生有效名单（转校）,名单备注：".$paramArray['remark'];
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 1;
                    $this->DataControl->insertData('crm_client_track', $trackData);

                    $result = array();
                    $result['status'] = "1";
                    $result['errtip'] = "名单激活成功！";
                    return $result;
                }else{
                    $trackData = array();
                    $trackData['client_id'] = $isSchollnews['client_id'];
                    $trackData['school_id'] = $schoolOne['school_id'];
                    $trackData['marketer_id'] = '0';
                    $trackData['marketer_name'] = $stafferOne['staffer_cnname'];
                    $trackData['track_validinc'] = 1;
                    $trackData['track_followmode'] = 4;
                    $trackData['track_linktype'] = '名单导入';
                    $trackData['track_note'] = "API导入，系统自动将无意向客户转为招生有效名单（同校）,名单备注：".$paramArray['remark'];
                    $trackData['track_createtime'] = time();
                    $trackData['track_type'] = 1;
                    $trackData['track_initiative'] = 1;
                    $this->DataControl->insertData('crm_client_track', $trackData);

                    $result = array();
                    $result['status'] = "1";
                    $result['errtip'] = "名单激活成功！";
                    return $result;
                }
            }
        }
        else{
            $result = array();
            $result['status'] = "-1";
            $result['errtip'] = "客资信息已转正，无法转换渠道！";
            return $result;
        }
    }*/

    function get_data_format($time)
    {
        list($usec, $sec) = explode(".", $time);
        $date = strtotime($usec);
        $return_data = str_pad($date.$sec,13,"0",STR_PAD_RIGHT); //不足13位。右边补0
        return $return_data;
    }

    function getsign($paramArray){
        unset($paramArray['sign']);
        ksort($paramArray);
        $string = '';
        foreach($paramArray as $key=>$paramOne){
            $string .= $key.$paramOne;
        }
        $string = utf8_encode($string);
        $string = $this->app_secret.$string.$this->app_secret;
        $string = md5($string);
        //echo $string. "<br>";
        //$string = bin2hex($string);
        //echo pack("H*",bin2hex($string)) . "<br>";
        return $string;
    }


    // ------------------------- 切换到 美团技术服务合作中心 ------------ 开始 ------------------


    //美团技术服务合作中心 后台配置的地址 --- 业务授权码回调地址
    function getMtDpingAuthcode($paramArray){
        $alljson = json_encode($paramArray, JSON_UNESCAPED_UNICODE);

        $jsonarr = array();
        $jsonarr['lsjson_type'] = 1;
        $jsonarr['company_id'] = 8888;
        $jsonarr['lsjson_json'] = $alljson;
        $jsonarr['lsjson_createtime'] = time();
        $logjsonId = $this->DataControl->insertData('gmc_company_dping_lsjson', $jsonarr);

        if(isset($paramArray['code']) && $paramArray['code'] != ''){
            $paramto = array();
            $paramto['businessId'] = $paramArray['businessId'];//59;
            $paramto['charset'] = "UTF-8";
            $paramto['code'] = $paramArray['code'];// "30733861bc7e8716b88cc6a680edf0a7";
            $paramto['developerId'] = 114356;
            $paramto['grantType'] = "authorization_code";
            $paramto['timestamp'] = time();
            $signn = $this->getMtDpingSign($paramto);
            $paramto['sign'] = $signn;
            $header = array();
            $header[] = "Content-type: application/x-www-form-urlencoded";
            //POST参数 RAW中JSON方式传值获取结果
            $getBackJson = httpRequest("https://api-open-cater.meituan.com/oauth/token", dataEncode($paramto), "POST", $header);

            if($sendApiArray = json_decode($getBackJson, "1")){
                if($sendApiArray['code'] == 0){
                    //储 授权后，换算出来的 token    相关记录
                    $jsonarr = array();
                    $jsonarr['lsjson_type'] = 3;//
                    $jsonarr['company_id'] = 8888;
                    $jsonarr['lsjson_json'] = $getBackJson;
                    $jsonarr['lsjson_createtime'] = time();
                    $logjsonId = $this->DataControl->insertData('gmc_company_dping_lsjson', $jsonarr);

                    //存储 授权后，换算出来的 token
                    $log = array();
                    $log['company_id'] = '8888';
                    if($sendApiArray['data']['scope'] == 'dingdan'){
                        $log['mttoken_type'] = '1';
                    }elseif($sendApiArray['data']['scope'] == 'customercenter'){
                        $log['mttoken_type'] = '2';
                    }else{
                        $log['mttoken_type'] = '0';
                    }
                    $log['mttoken_json'] = json_encode($sendApiArray['data'],JSON_UNESCAPED_UNICODE);
                    $log['mttoken_expiresin'] = $sendApiArray['data']['expireIn'];
                    $log['mttoken_createtime'] = time();
                    $this->DataControl->insertData('gmc_company_dping_mttoken', $log);

                    $this->error = 0;
                    $this->errortip = "换算token -- 成功";
                    return false;
                }else{
                    $this->error = 1;
                    $this->errortip = "换算token -- 异常：".$sendApiArray['code'].$sendApiArray['msg'].$sendApiArray['traceId'];
                    return false;
                }
            }else{
                $this->error = 1;
                $this->errortip = "业务授权码回调地址 -- 用code请求接口失败";
                return false;
            }
        }else{
            $this->error = 1;
            $this->errortip = "业务授权码回调地址 -- code返回数据为空";
            return false;
        }
    }

    //美团技术服务合作中心 后台配置的地址 --- 客资中心-推送顾客线索消息
    function getMtDpingClient($paramArray){
        $alljson = json_encode(urldecode($paramArray), JSON_UNESCAPED_UNICODE);

        $jsonarr = array();
        $jsonarr['lsjson_type'] = 2;
        $jsonarr['company_id'] = 8888;
        $jsonarr['lsjson_json'] = $alljson;
        $jsonarr['lsjson_createtime'] = time();
        $logjsonId = $this->DataControl->insertData('gmc_company_dping_lsjson', $jsonarr);

        //数据处理
        $urlParamArray = array();
        $urlParamArray1 = explode('&',urldecode($paramArray));
        if($urlParamArray1){
            foreach ($urlParamArray1 as $value) {
                $one = explode('=', $value);
                $urlParamArray[$one[0]] = $one[1];
            }
        }

        if($urlParamArray['message']){
            $arrMessage = json_decode($urlParamArray['message'], "1");

            $lasttime = ($arrMessage['lastLeadsAddTime']>1)?$arrMessage['lastLeadsAddTime']/1000:0;
            $oldlead = $this->DataControl->selectOne("select 1 from gmc_company_dping_newlead where consumerId = '{$arrMessage['consumerId']}' and phone = '{$arrMessage['phone']}' and lastLeadsaddtime = '{$lasttime}' ");
            if($oldlead){
                $log = array();
                $log['company_id'] = '8888';
                $log['consumerId'] = $arrMessage['consumerId'];
                $log['phone'] = $arrMessage['phone'];
                $log['lastLeadsaddtime'] = $lasttime;
                $log['newlead_json'] = $urlParamArray['message'];
                $log['newlead_opbizcode'] = $urlParamArray['opBizCode'];
                $log['newlead_createtime'] = time();
                $log['newlead_client_status'] = '-1';
                $log['newlead_errortip'] = '名单推送过（美团重复推送的）';
                $this->DataControl->insertData('gmc_company_dping_newlead_fail', $log);

                $this->error = -99;
                $this->errortip = "返回的数据记录信息失败";
                return false;
            }


            $log = array();
            $log['company_id'] = '8888';
            $log['consumerId'] = $arrMessage['consumerId'];
            $log['phone'] = $arrMessage['phone'];
            $log['lastLeadsaddtime'] = (($arrMessage['lastLeadsAddTime']>1)?$arrMessage['lastLeadsAddTime']/1000:0);
            $log['newlead_json'] = $urlParamArray['message'];
            $log['newlead_opbizcode'] = $urlParamArray['opBizCode'];
            $log['newlead_createtime'] = time();
            if($leadsId = $this->DataControl->insertData('gmc_company_dping_newlead', $log)){

                if(checkMobile($arrMessage['phone'])){
                    $paramArr = array();
                    $paramArr['company_id'] = '8888';
                    $paramArr['mobile'] = $arrMessage['phone'];
                    if(trim($urlParamArray['opBizCode']) !=''){
                        $dpingshopOne = $this->DataControl->getFieldOne("gmc_company_dping_newshop", "school_branch,newshop_name,newshop_isenterschool"
                            , "newshop_opoiid = '{$urlParamArray['opBizCode']}' AND school_branch <> '' and company_id = '8888'");
                        if($dpingshopOne){
                            $paramArr['school_branch'] = $dpingshopOne['school_branch'];
                            $paramArr['isenterschool'] = $dpingshopOne['dpingshop_isenterschool'];
                        }
                    }else{
                        $paramArr['school_branch'] = '';
                        $paramArr['isenterschool'] = '1';
                    }

                    if(trim($urlParamArray['opBizCode']) == ''){
                        $paramArr['buy_time'] = ceil($arrMessage['lastLeadsAddTime']/1000);
                        if($arrMessage['platform']=='1'){//	留资平台（1为点评、2为美团 ） 注：400电话来源的客资不区分平台，留资平台为0
                            $platform = "点评";
                        }else{
                            $platform = "美团";
                        }
                        $paramArr['remark'] = "News:{$platform}咨询留下客资信息，客资备注{$arrMessage['merchantRemark']}；";
                        $resultApi = $this->addClient($paramArr);
                        $log = array();
                        $log['newlead_client_status'] = $resultApi['status'];
                        $log['newlead_errortip'] = $resultApi['errtip'];
                        $this->DataControl->updateData('gmc_company_dping_newlead',"newlead_id = '{$leadsId}'", $log);
                    }else{
                        if($dpingshopOne){
                            $schoolOne = $this->DataControl->getFieldOne("smc_school"
                                , "school_id", "school_branch = '{$paramArr['school_branch']}' and school_isclose = '0' and school_istest = '0' and company_id = '{$paramArr['company_id']}'");
                            if($schoolOne){
                                $paramArr['buy_time'] = ceil($arrMessage['lastLeadsAddTime']/1000);
                                if($arrMessage['platform']=='1'){//	留资平台（1为点评、2为美团 ） 注：400电话来源的客资不区分平台，留资平台为0
                                    $platform = "点评";
                                }else{
                                    $platform = "美团";
                                }
                                if($dpingshopOne['newshop_name'] !==''){
                                    $paramArr['remark'] = "News:{$platform}用户在{$dpingshopOne['newshop_name']}咨询留下客资信息，客资备注{$arrMessage['merchantRemark']}；";
                                }else{
                                    $paramArr['remark'] = "News:{$platform}咨询留下客资信息，客资备注{$arrMessage['merchantRemark']}；";
                                }
                                $resultApi = $this->addClient($paramArr);
                                $log = array();
                                $log['newlead_client_status'] = $resultApi['status'];
                                $log['newlead_errortip'] = $resultApi['errtip'];
                                $this->DataControl->updateData('gmc_company_dping_newlead',"newlead_id = '{$leadsId}'", $log);
                            }else{
                                //名单数据 同步到园的时候 接口没有变
                                request_by_curl("https://kmcapi.kedingdang.com/Api/addNewClient", dataEncode($paramArr),"POST");
                                $log = array();
                                $log['newlead_client_status'] = '-1';
                                $log['newlead_errortip'] = '直营园名单异步通知中！';
                                $this->DataControl->updateData('gmc_company_dping_newlead',"newlead_id = '{$leadsId}'", $log);
                            }
                        }else{
                            $log = array();
                            $log['newlead_client_status'] = '-1';
                            $log['newlead_errortip'] = '非直营校区客资信息，无需处理！';
                            $this->DataControl->updateData('gmc_company_dping_newlead',"newlead_id = '{$leadsId}'", $log);
                        }
                    }

                    $this->error = 0;
                    $this->errortip = "success";
                    return false;
                }else{
                    $log = array();
                    $log['newlead_client_status'] = '-1';
                    $log['newlead_errortip'] = '客户手机号不正确';
                    $this->DataControl->updateData('gmc_company_dping_newlead',"newlead_id = '{$leadsId}'", $log);

                    $this->error = -99;
                    $this->errortip = "手机号格式不对";
                    return false;
                }
            }else{
                $log = array();
                $log['company_id'] = '8888';
                $log['consumerId'] = $arrMessage['consumerId'];
                $log['phone'] = $arrMessage['phone'];
                $log['lastLeadsaddtime'] = (($arrMessage['lastLeadsAddTime']>1)?$arrMessage['lastLeadsAddTime']/1000:0);
                $log['newlead_json'] = "插入失败";
                $log['newlead_opbizcode'] = "插入失败";
                $log['newlead_createtime'] = time();
                $log['newlead_client_status'] = '-1';
                $log['newlead_errortip'] = '插入失败！';
                $this->DataControl->insertData('gmc_company_dping_newlead_fail', $log);

                $this->error = -99;
                $this->errortip = "返回的数据记录信息失败";
                return false;
            }
        }else{
            $this->error = -99;
            $this->errortip = "数据信息不全";
            return false;
        }
    }

    //美团技术服务合作中心 获取签名算法
    function getMtDpingSign($data) {
        if ($data == null || $data == '') {
            return false;
        }
        ksort($data);
        $result_str = "";
        foreach ($data as $key => $val) {
            if ($key != "sign" && $val != null && $val != "") {
                $result_str = $result_str . $key . $val;
            }
        }
//        echo $result_str.'---';
        $result_str = $this->SignKeyMt . $result_str;
        $ret = bin2hex(sha1($result_str, true));
        return $ret;
    }

    //美团技术服务合作中心 业务授权码回调给的 Code 获取 access_token  ---- 初始化 第一次获取
    function getMtDpingCodeToToken(){
        $paramto = array();
        $paramto['businessId'] = 59;
        $paramto['charset'] = "UTF-8";
        $paramto['code'] = "30733861bc7e8716b88cc6a680edf0a7";
        $paramto['developerId'] = 114356;
        $paramto['grantType'] = "authorization_code";
        $paramto['timestamp'] = time();
        $signn = $this->getMtDpingSign($paramto);
        $paramto['sign'] = $signn;
        $header = array();
        $header[] = "Content-type: application/x-www-form-urlencoded";
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://api-open-cater.meituan.com/oauth/token", dataEncode($paramto), "POST", $header);
        print_r($getBackJson);die;
    }

    //美团技术服务合作中心  获取转化后的 access_token 数据 即（appAuthToken）
    function getMtDpingToken($company_id){
        $dpingcodeOne = $this->DataControl->selectOne("select * from gmc_company_dping_mttoken where company_id = '{$company_id}' and mttoken_type = '1' and mttoken_refresh = '0'  order by mttoken_id desc limit 0,1 ");
        if($dpingcodeOne){
            if(($dpingcodeOne['mttoken_createtime']+$dpingcodeOne['mttoken_expiresin']) < time()-600){
                //快过期的时候 更新 token 值
                $dpingcodeArray = json_decode($dpingcodeOne['mttoken_json'], "1");

                $paramto = array();
                $paramto['timestamp'] = time();
                $paramto['scope'] = "all";
                $paramto['refreshToken'] = $dpingcodeArray['refreshToken'];
                $paramto['grantType'] = "refresh_token";
                $paramto['developerId'] = 114356;
                $paramto['charset'] = "UTF-8";
                $paramto['businessId'] = 59;
                $signn = $this->getMtDpingSign($paramto);
                $paramto['sign'] = $signn;
                //header 设置
                $header = array();
                $header[] = "Content-type: application/x-www-form-urlencoded";
                //POST参数 RAW中JSON方式传值获取结果
                $getBackJson = httpRequest("https://api-open-cater.meituan.com/oauth/refresh", dataEncode($paramto), "POST", $header);
                $sendApiArray = json_decode($getBackJson, "1");

                if($sendApiArray['code'] == '0'){
                    $log = array();
                    $log['company_id'] = '8888';
                    $log['mttoken_type'] = '1';
                    $log['mttoken_json'] = json_encode($sendApiArray['data'],JSON_UNESCAPED_UNICODE);
                    $log['mttoken_expiresin'] = $sendApiArray['data']['expireIn'];
                    $log['mttoken_createtime'] = time();
                    if($this->DataControl->insertData('gmc_company_dping_mttoken', $log)){
                        $log = array();
                        $log['mttoken_refresh'] = 1;
                        $this->DataControl->updateData('gmc_company_dping_mttoken',"mttoken_id = '{$dpingcodeOne['mttoken_id']}'", $log);
                        return $sendApiArray['data']['accessToken'];
                    }else{
                        return false;
                    }
                }else{
                    return false;
                }

            }else{
                $dpingcodeArray = json_decode($dpingcodeOne['mttoken_json'], "1");
                return $dpingcodeArray['accessToken'];
            }
        }
        return false;
    }

    //美团技术服务合作中心 --- 查询订单数据
    function getMtDpingOrderApi($paramArray,$page_no=1,$page_size=10,$gmt_start='',$gmt_end='',$isupda=1){
        //获取  access_token 数据 即（appAuthToken）
        $MtDpingToken = $this->getMtDpingToken($paramArray['company_id']);

//        $gmt_start = 1735725300;
//        $gmt_end = 1735725360;

        //传参数据
        $bizdata = array();
        $bizdata['orderType'] = 1;
        $bizdata['pageNo'] = $page_no;
        $bizdata['pageSize'] = $page_size;

        $bizdata['buySuccessTimeFrom'] = $gmt_start?$gmt_start*1000:(time()-605)*1000 ; //;
        $bizdata['buySuccessTimeTo'] = $gmt_end?$gmt_end*1000:time()*1000;;

//        $bizdata['buySuccessTimeFrom'] = 1735660800000;
//        $bizdata['buySuccessTimeTo'] = 1737561600000;

        //接口公共参数 必须要有
        $paramto = array();
        $paramto['appAuthToken'] = $MtDpingToken;//不是必填项
        $paramto['timestamp'] = time();
        $paramto['charset'] = "UTF-8";
        $paramto['developerId'] = 114356;
        $paramto['version'] = 2;
        $paramto['businessId'] = 59;
        //请求不同接口的传参不一样
        $paramto['biz'] = json_encode($bizdata);
        $paramto['sign'] = $this->getMtDpingSign($paramto);

        //header 设置
        $header = array();
        $header[] = "Content-type: application/x-www-form-urlencoded";
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://api-open-cater.meituan.com/ddzhkh/dingdan/queryorder", dataEncode($paramto), "POST", $header);

//        print_r($getBackJson);
//        die;
//        die;

        if($sendApiArray = json_decode($getBackJson, "1")){
            if($sendApiArray['code'] == 'OP_SUCCESS'){
                if(is_array($sendApiArray['data']['result']) && !empty($sendApiArray['data']['result'])){
                    foreach($sendApiArray['data']['result'] as $dpingorderOne){
//                        if( $dpingorderOne['orderId'] != '4981005567486128817'){
//                            continue;
//                        }
                        $orderOne = $this->DataControl->getFieldOne("gmc_company_dping_neworder","neworder_orderid,neworder_client_status","neworder_orderid = '{$dpingorderOne['orderId']}'");
                        if(!$orderOne){
                            $log = array();
                            $log['company_id'] = '8888';
                            $log['neworder_orderid'] = $dpingorderOne['orderId'];
                            $log['neworder_ordertype'] = $dpingorderOne['orderType'];
                            $log['neworder_buysuccesstime'] = $dpingorderOne['buySuccessTime']/1000;
                            $log['neworder_expiretime'] = $dpingorderOne['expireTime']/1000;
                            $log['neworder_productname'] = $dpingorderOne['productName'];
                            $log['neworder_productitemid'] = $dpingorderOne['productItemId'];
                            $log['neworder_status'] = $dpingorderOne['status'];
                            $log['neworder_refundstatus'] = $dpingorderOne['refundStatus'];
                            $log['neworder_mobile'] = $dpingorderOne['mobile'];
                            $log['neworder_shopname'] = $dpingorderOne['shopName'];
                            $log['neworder_oppoiid'] = $dpingorderOne['opPoiId'];
                            $log['neworder_channel'] = $dpingorderOne['channel'];
                            $log['neworder_totalamount'] = $dpingorderOne['totalAmount'];
                            $log['neworder_shopamount'] = $dpingorderOne['shopAmount'];
                            $log['neworder_spugid'] = $dpingorderOne['spugId'];
                            $log['neworder_createtime'] = time();
                            $this->DataControl->insertData('gmc_company_dping_neworder', $log);
                            $orderOne = array();
                            $orderOne['neworder_orderid'] = $dpingorderOne['orderId'];
                            $orderOne['neworder_client_status'] = '0';
                        }else{
                            $log = array();
                            $log['neworder_oppoiid'] = $dpingorderOne['opPoiId'];
                            $log['neworder_shopname'] = $dpingorderOne['shopName'];
                            $log['neworder_status'] = $dpingorderOne['status'];
                            $log['neworder_mobile'] = $dpingorderOne['mobile'];
                            $log['neworder_refundstatus'] = $dpingorderOne['refundStatus'];
                            $this->DataControl->updateData('gmc_company_dping_neworder',"neworder_orderid = '{$orderOne['neworder_orderid']}'", $log);
                        }

                        if($orderOne['neworder_client_status'] == '0' && checkMobile($dpingorderOne['mobile'])){
                            $paramArr = array();
                            $paramArr['company_id'] = '8888';
                            $paramArr['mobile'] = $dpingorderOne['mobile'];
                            if(trim($dpingorderOne['opPoiId']) != ''){
                                $schoolOne = $this->DataControl->getFieldOne("gmc_company_dping_newshop", "school_branch,school_type"
                                    , "newshop_opoiid = '{$dpingorderOne['opPoiId']}' AND school_branch <> '' and company_id = '8888'");
                                if($schoolOne){
                                    $paramArr['school_branch'] = $schoolOne['school_branch'];
                                }
                            }else{
                                $paramArr['school_branch'] = '';
                            }

                            if(trim($dpingorderOne['opPoiId']) == '' || $schoolOne){

                                if($schoolOne['school_type'] != '2') {
                                    $paramArr['buy_time'] = $dpingorderOne['buy_success_time'] / 1000;
                                    $paramArr['remark'] = "大众点评在{$dpingorderOne['shopName']}购买{$dpingorderOne['productName']}，消费金额{$dpingorderOne['totalAmount']}；【新】";
                                    $resultApi = $this->addClient($paramArr);

                                    $log = array();
                                    $log['neworder_client_status'] = $resultApi['status'];
                                    $log['neworder_errortip'] = $resultApi['errtip'];
                                    $this->DataControl->updateData('gmc_company_dping_neworder', "neworder_orderid = '{$orderOne['neworder_orderid']}'", $log);
                                }else{
                                    //名单数据 同步到园的时候 接口没有变
                                    request_by_curl("https://kmcapi.kedingdang.com/Api/addNewClient", dataEncode($paramArr),"POST");
                                    $log = array();
                                    $log['neworder_client_status'] = '-1';
                                    $log['neworder_errortip'] = '直营园名单异步通知中！';
                                    $this->DataControl->updateData('gmc_company_dping_neworder',"neworder_orderid = '{$orderOne['neworder_orderid']}'", $log);
                                }

                            }else{
                                $log = array();
                                $log['neworder_client_status'] = '-1';
                                $log['neworder_errortip'] = '非直营校区客资信息，不处理';
                                $this->DataControl->updateData('gmc_company_dping_neworder',"neworder_orderid = '{$orderOne['neworder_orderid']}'", $log);
                            }
                        }else{
                            $log = array();
                            $log['neworder_client_status'] = '-1';
                            $log['neworder_errortip'] = '手机号可能有问题，不处理';
                            $this->DataControl->updateData('gmc_company_dping_neworder',"neworder_orderid = '{$orderOne['neworder_orderid']}'", $log);
                        }
                    }
//echo count($sendApiArray['data']['result']);die;
                    $totalCount = (count($sendApiArray['data']['result']) == $page_size)?true:false;
                    //判断总数是否大于每次能查到的最大数量
                    if($totalCount){
                        $p = $page_no+1;
                        $num = $page_size;
                        $this->getMtDpingOrderApi($paramArray,$p,$num,$bizdata['buySuccessTimeFrom'],$bizdata['buySuccessTimeTo']);
                    }

                    $this->errortip = '已处理';
                    return count($sendApiArray['data']);
                }else{
                    $this->errortip = '数据为空';
                    return 0;
                }
            }else{
                $this->errortip = $sendApiArray['msg'];
                return false;
            }
        }else{
            $this->errortip = "第三方数据获取失败";
            return false;
        }

    }

    //美团技术服务合作中心 --- 店铺数据
    function getMtDpingStoreApi($paramArray){
        //获取  access_token 数据 即（appAuthToken）
        $MtDpingToken = $this->getMtDpingToken($paramArray['company_id']);

        //传参数据
        $bizdata = array();
        $bizdata['limit'] = 1000;
        $bizdata['offset'] = 0;

        //接口公共参数 必须要有
        $paramto = array();
        $paramto['appAuthToken'] = $MtDpingToken;//不是必填项
        $paramto['timestamp'] = time();
        $paramto['charset'] = "UTF-8";
        $paramto['developerId'] = 114356;
        $paramto['version'] = 2;
        $paramto['businessId'] = 59;
        //请求不同接口的传参不一样
        $paramto['biz'] = json_encode($bizdata);
        $paramto['sign'] = $this->getMtDpingSign($paramto);

        //header 设置
        $header = array();
        $header[] = "Content-type: application/x-www-form-urlencoded";
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://api-open-cater.meituan.com/ddzhkh/auth/token/pageQueryPoiList", dataEncode($paramto), "POST", $header);

//        print_r($getBackJson);die;
//        die;

        if($sendApiArray = json_decode($getBackJson, "1")){
            if($sendApiArray['code'] == 'OP_SUCCESS'){
                if($sendApiArray['data']['total'] > '1') {
                    foreach ($sendApiArray['data']['poiInfoList'] as $dpingshopOne) {
                        if (!$this->DataControl->getFieldOne("gmc_company_dping_newshop", "newshop_id", "newshop_opoiid = '{$dpingshopOne['opPoiId']}'")) {
                            $log = array();
                            $log['company_id'] = '8888';
                            $log['newshop_opoiid'] = $dpingshopOne['opPoiId'];
                            $log['newshop_name'] = $dpingshopOne['name'];
                            $log['newshop_address'] = $dpingshopOne['address'];
                            $log['newshop_cityname'] = $dpingshopOne['cityName'];
                            $this->DataControl->insertData('gmc_company_dping_newshop', $log);
                        } else {
                            $log = array();
                            $log['newshop_name'] = $dpingshopOne['name'];
                            $log['newshop_address'] = $dpingshopOne['address'];
                            $log['newshop_cityname'] = $dpingshopOne['cityName'];
                            $this->DataControl->updateData('gmc_company_dping_newshop', "newshop_opoiid = '{$dpingshopOne['opPoiId']}'", $log);
                        }
                    }
                }
                $this->errortip = '已经更新';
                return $sendApiArray;
            }else{
                $this->errortip = $sendApiArray['msg'];
                return false;
            }
        }else{
            $this->errortip = "第三方数据获取失败";
            return false;
        }

    }

    //美团技术服务合作中心 --- 店铺数据 --- 对应客户门店ID映射关系 --- 没啥用处
    function getBjxToMtDpingStoreApi($paramArray){
        //获取  access_token 数据 即（appAuthToken）
        $MtDpingToken = $this->getMtDpingToken($paramArray['company_id']);

        //传参数据
        $bizdata = array();
        $bizdata['poiIds'] = ["*********"];//美团的ID

        //接口公共参数 必须要有
        $paramto = array();
        $paramto['appAuthToken'] = $MtDpingToken;//不是必填项
        $paramto['timestamp'] = time();
        $paramto['charset'] = "UTF-8";
        $paramto['developerId'] = 114356;
        $paramto['version'] = 2;
        $paramto['businessId'] = 59;
        //请求不同接口的传参不一样
        $paramto['biz'] = json_encode($bizdata);
        $paramto['sign'] = $this->getMtDpingSign($paramto);

//        print_r($paramto);
//        echo '<br>';
//        echo "https://api-open-cater.meituan.com/ddzhkh/auth/token/queryPoiMapping";
//        echo '<br>';

        //header 设置
        $header = array();
        $header[] = "Content-type: application/x-www-form-urlencoded";
        //POST参数 RAW中JSON方式传值获取结果
        $getBackJson = httpRequest("https://api-open-cater.meituan.com/ddzhkh/auth/token/queryPoiMapping", dataEncode($paramto), "POST", $header);

        print_r($getBackJson);die;
        die;

//        if($sendApiArray = json_decode($getBackJson, "1")){
//            if($sendApiArray['code'] == 'OP_SUCCESS'){
//                if($sendApiArray['data']['total'] > '1') {
//                    foreach ($sendApiArray['data']['poiInfoList'] as $dpingshopOne) {
//                        if (!$this->DataControl->getFieldOne("gmc_company_dping_newshop", "newshop_id", "newshop_opoiid = '{$dpingshopOne['opPoiId']}'")) {
//                            $log = array();
//                            $log['company_id'] = '8888';
//                            $log['newshop_opoiid'] = $dpingshopOne['opPoiId'];
//                            $log['newshop_name'] = $dpingshopOne['name'];
//                            $log['newshop_address'] = $dpingshopOne['address'];
//                            $log['newshop_cityname'] = $dpingshopOne['cityName'];
//                            $this->DataControl->insertData('gmc_company_dping_newshop', $log);
//                        } else {
//                            $log = array();
//                            $log['newshop_name'] = $dpingshopOne['name'];
//                            $log['newshop_address'] = $dpingshopOne['address'];
//                            $log['newshop_cityname'] = $dpingshopOne['cityName'];
//                            $this->DataControl->updateData('gmc_company_dping_newshop', "newshop_opoiid = '{$dpingshopOne['opPoiId']}'", $log);
//                        }
//                    }
//                }
//                $this->errortip = '已经更新';
//                return $sendApiArray;
//            }else{
//                $this->errortip = $sendApiArray['msg'];
//                return false;
//            }
//        }else{
//            $this->errortip = "第三方数据获取失败";
//            return false;
//        }

    }

    //美团技术服务合作中心  定时更新 access_token 数据 即（appAuthToken） 的新值
    function getMtDpingTokenToRefresh($paramArray,$company_id='8888'){
        $nowtime = time();
        $nowOneDay = time()+86400;
        $sqlDOne = "select * 
                    from gmc_company_dping_mttoken 
                    where company_id = '{$company_id}' 
                        and (mttoken_createtime + mttoken_expiresin - 86400) < '{$nowtime}'  
                        and (mttoken_createtime + mttoken_expiresin) > '{$nowtime}' 
                        and mttoken_refresh = 0 
                    order by mttoken_id desc 
                    limit 0,10 ";
        $dpingcodeArr = $this->DataControl->selectClear($sqlDOne);
        if($dpingcodeArr){///$dpingcodeOne
            $succ = 0;
            $fail = 0;
            foreach ($dpingcodeArr as $dpingcodeOne){
                //快过期的时候 更新 token 值
                $dpingcodeArray = json_decode($dpingcodeOne['mttoken_json'], "1");

                $paramto = array();
                $paramto['timestamp'] = time();
                $paramto['scope'] = "all";
                $paramto['refreshToken'] = $dpingcodeArray['refreshToken'];
                $paramto['grantType'] = "refresh_token";
                $paramto['developerId'] = 114356;
                $paramto['charset'] = "UTF-8";
                $paramto['businessId'] = 59;
                $signn = $this->getMtDpingSign($paramto);
                $paramto['sign'] = $signn;
                //header 设置
                $header = array();
                $header[] = "Content-type: application/x-www-form-urlencoded";
                //POST参数 RAW中JSON方式传值获取结果
                $getBackJson = httpRequest("https://api-open-cater.meituan.com/oauth/refresh", dataEncode($paramto), "POST", $header);
                $sendApiArray = json_decode($getBackJson, "1");

                if($sendApiArray['code'] == '0'){
                    $log = array();
                    $log['company_id'] = '8888';
                    $log['mttoken_type'] = $dpingcodeOne['mttoken_type'];
                    $log['mttoken_json'] = json_encode($sendApiArray['data'],JSON_UNESCAPED_UNICODE);
                    $log['mttoken_expiresin'] = $sendApiArray['data']['expireIn'];
                    $log['mttoken_createtime'] = time();
                    if($this->DataControl->insertData('gmc_company_dping_mttoken', $log)){

                        $log = array();
                        $log['mttoken_refresh'] = 1;
                        $this->DataControl->updateData('gmc_company_dping_mttoken',"mttoken_id = '{$dpingcodeOne['mttoken_id']}'", $log);

                        $succ++;
                    }else{
                        $fail++;
                    }
                }else{
                    $fail++;
                }
            }

            $this->error = 0;
            $this->errortip = "已处理，成功 $succ 个，失败 $fail 个。";
            return true;
        }
        $this->error = 1;
        $this->errortip = "暂无需要更新的数据";
        return false;
    }



}