<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 15:21
 */

namespace Model\Api;

class HrmReportModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
//    public $aeskey = 'jidebaosmc%C0515';
//    public $aesiv = 'jdbCuBC7orQtDhTO';
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

//    function stringReplace($string)
//    {
//        $datacode = trim(str_replace('"', "", $string));
//        $datacode = urldecode(urldecode($datacode));
//        $datacode = str_replace(' ', "+", $datacode);
//        return $datacode;
//    }

    //第三方授权访问权限校验
//    function ThreeVerify($paramArray)
//    {
//        if (isset($paramArray['is_skip']) && $paramArray['is_skip'] == 1) {
//            $data = array();
//            if (isset($paramArray['school_branch']) && $paramArray['school_branch'] != '') {
//                $schoolOne = $this->DataControl->getFieldOne("smc_school", "company_id", "school_branch='{$paramArray['school_branch']}'");
//                $data['company_id'] = $schoolOne['company_id'];
//            } else {
//                $data['company_id'] = '8888';
//            }
//            return $data;
//        } else {
//            if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
//                $this->errortip = "请传入授权时间";
//                $this->error = true;
//                return false;
//            }
//            /*if($paramArray['timesteps']+60*5 < time() || $paramArray['timesteps']-60 > time()){
//                $maxtimes = date("Y-m-d H:i:s",$paramArray['timesteps']+60*5);
//                $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性,{$timesteps}--{$jmsting}";
//                $this->error = true;
//                return false;
//            }*/
//
//            $aes = new \Aesencdec($this->aeskey, $this->aesiv);
//
//            $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
//            $paramJson = json_decode($xssting, 1);//转化为数组
//
//            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
//                $this->errortip = '授权时间和连接时间不一致';
//                $this->error = true;
//                return false;
//            }
//
//            $companyOne = $this->DataControl->selectOne("select company_id from gmc_company
//WHERE company_id = '{$paramJson['company_id']}' and company_code = '{$paramJson['company_code']}' limit 0,1");
//            if ($companyOne) {
//                return $companyOne;
//            } else {
//                $this->errortip = '你的授权集团编号错误，请确认编号正确';
//                $this->error = true;
//                return false;
//            }
//        }
//    }

    /**
     * 校务追踪大表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     */
    function getSchClientTrackReport($request)
    {
        $datawhere = "s.company_id = '{$request['company_id']}' AND s.school_isclose = '0' AND s.school_istest <> '1'";
        if (isset($request['school_id']) && $request['school_id'] !== '') {
            $datawhere .= " and s.school_id='{$request['school_id']}'";
        }
        if (isset($request['str_school_id']) && $request['str_school_id'] !== '') {
            $datawhere .= " and sc.school_id in ('" . implode("','", $request['str_school_id']) . "')";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and S.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }

        if (isset($request['school_type']) && $request['school_type'] !== '') {
            $datawhere .= " and s.school_type='{$request['school_type']}'";
        }
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (s.school_cnname like '%{$request['keyword']}%' or s.school_shortname like '%{$request['keyword']}%' or s.school_branch like '%{$request['keyword']}%')";
        }

        if (isset($request['dataequity']) && $request['dataequity'] != '1') {
            $postbeOne = $this->AnalyzeControl->getFieldOne("gmc_staffer_postbe", "organize_id", "postbe_id = '{$request['re_postbe_id']}'");
            $datawhere .= " AND s.school_id IN (SELECT o.school_id FROM gmc_company_organizeschool AS o WHERE o.organize_id = '{$postbeOne['organize_id']}')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        if (isset($request['fixedtime']) && $request['fixedtime'] !== '') {
            $today = date("Y-m-d", strtotime($request['fixedtime']));
            $todaytime = strtotime($request['fixedtime']) + 24 * 60 * 60 - 1;
        } else {
            $today = date("Y-m-d");
            $todaytime = time();
        }

        $thisWeek = GetWeekAll($today);

        $week_startdate = date("Y-m-d", strtotime("-6 days", strtotime($today)));
        $month_startdate = date("Y-m-d", strtotime("-29 days", strtotime($today)));
        $quarter_startdate = date("Y-m-d", strtotime("-90 days", strtotime($today)));
        $sixmoth_startdate = date("Y-m-d", strtotime("-180 days", strtotime($today)));

        $week_starttime = strtotime($week_startdate);
        $month_starttime = strtotime($month_startdate);
        $history_starttime = strtotime('2020-06-01');
        $quarter_starttime = strtotime($quarter_startdate);
        $sixmoth_starttime = strtotime($sixmoth_startdate);

        $register_week_where = "r.pay_successtime >= '{$week_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_month_where = "r.pay_successtime >= '{$month_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_history_where = "r.pay_successtime >= '{$history_starttime}' and r.pay_successtime <='{$todaytime}' and r.info_status =1";
        $register_absentee_where = "e.school_id = s.school_id";
        $register_reading_where = " ta.school_id = s.school_id AND ta.beginday <= '{$today}' AND ta.endday >= '{$today}'";
        $register_feereading_where = "d.class_id = c.class_id AND c.course_id = r.course_id AND d.school_id = ac.school_id AND d.student_id = ac.student_id
        AND c.course_id = ac.course_id AND d.school_id = s.school_id AND ac.coursebalance_figure = '0' AND d.study_beginday <= '{$today}' AND d.study_endday >= '{$today}'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] !== '') {
            $register_week_where .= " and r.coursetype_id='{$request['coursetype_id']}'";
            $register_month_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_history_where .= " and r.coursetype_id='{$request['coursetype_id']}' ";
            $register_absentee_where .= " and e.coursetype_id='{$request['coursetype_id']}'";
            $register_reading_where .= " and ta.coursetype_id='{$request['coursetype_id']}'";
            $register_feereading_where .= " and r.coursetype_id='{$request['coursetype_id']}'";
        }


        $client_week_where = "t.client_createtime >='{$week_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_month_where = "t.client_createtime >='{$month_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_quarter_where = "t.client_createtime >='{$quarter_starttime}' and t.client_createtime <='{$todaytime}'";
        $client_sixmoth_where = "t.client_createtime >='{$sixmoth_starttime}' and t.client_createtime <='{$todaytime}'";

        $track_week_where = "k.track_createtime >='{$week_starttime}' and k.track_createtime <='{$todaytime}' and k.track_isactive =1";
        $track_month_where = "k.track_createtime >='{$month_starttime}' and k.track_createtime <='{$todaytime}' and k.track_isactive =1";

        $invite_week_where = "i.invite_visittime >='{$week_startdate}' and i.invite_visittime <= '{$today}' ";
        $invite_month_where = "i.invite_visittime >='{$month_startdate}' and i.invite_visittime <= '{$today}'";

        $audition_lastweek_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$thisWeek['lastweek_start']}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$thisWeek['lastweek_end']}'";
        $audition_curweek_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$thisWeek['nowweek_start']}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$thisWeek['nowweek_end']}'";
        $audition_week_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$week_startdate}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$today}' ";
        $audition_month_where = "DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$month_startdate}' and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <= '{$today}'";


        $sql = " select s.school_shortname,s.school_branch,s.school_id,
         

         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_week_where} ) as positivelog_week_num,
         (SELECT COUNT(r.info_id) from smc_student_registerinfo as r where r.school_id = s.school_id and {$register_month_where} ) as positivelog_month_num,
  
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_week_where} and h.channel_way =1 and t.client_intention_level>=3) as client_under_weeknum,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_week_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_weeknum,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and h.channel_way =1 and t.client_intention_level>=3) as client_under_monthnum,
        (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and h.channel_way =0 and t.client_intention_level>=3) as client_up_monthnum,
          (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_month_where} and t.client_intention_level>=3) as client_monthnum,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id   and  {$client_quarter_where} and t.client_intention_level>=3) as client_up_quarternum,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus IN (0,1,2,3) and t.client_intention_level>=3) as client_all_num,
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_stubranch <> '' and {$client_month_where} ) as recomend_clientnum,
 
        (select count(t.client_id) from crm_client as t,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_distributionstatus = 0  and client_tracestatus = 0  ) as client_noallot_num,
        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_week_where} ) as track_client_num ,
        (select count(DISTINCT k.client_id) from crm_client_track as  k where k.school_id=s.school_id and {$track_month_where} ) as track_client_monthnum ,
      
        (select count( k.track_id) from crm_client_track as  k where k.school_id=s.school_id and  {$track_week_where} and track_linktype ='电话沟通') as track_week_tephonenum ,
        (select count(k.track_id) from crm_client_track as  k where k.school_id=s.school_id  and  {$track_month_where} and track_linktype ='电话沟通') as track_month_tephonenum ,
     (select count(i.invite_id) from  crm_client_invite as i where i.school_id=s.school_id and {$invite_month_where} and i.invite_isvisit=0 ) as invite_no_confirm,
      (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_isvisit=0 and  {$audition_month_where} ) as audition_no_confirm,
        (select count(i.invite_id) from  crm_client_invite as i where i.school_id=s.school_id and {$invite_week_where} ) as invite_week_num,
        (select count(i.invite_id) from  crm_client_invite as i where i.school_id=s.school_id and {$invite_month_where} ) as invite_month_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and {$audition_curweek_where} ) as ohaudition_curweek_num,
       
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and {$audition_lastweek_where} ) as ohaudition_lastweek_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and audition_isvisit =1 and {$audition_lastweek_where} ) as ohaudition_lastweek_postnum,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and {$audition_week_where} ) as ohaudition_week_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and  {$audition_month_where} ) as ohaudition_month_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0  and a.audition_isvisit =1 and {$audition_week_where} ) as ohaudition_week_arrnum,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and a.audition_isvisit =1 and  {$audition_month_where} ) as ohaudition_month_arrnum,
        (select count(a.audition_id) from  crm_client_audition as a,crm_client_positivelog as  pg where a.school_id=s.school_id and a.audition_genre=0 and a.audition_isvisit =1 and  pg.client_id = a.client_id and a.school_id = pg.school_id and pg.positivelog_time = DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') and {$audition_month_where} ) as ohpostive_month_arrnum,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and {$audition_week_where} ) as audition_week_num,
        (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id  and  {$audition_month_where} ) as audition_month_num,
        (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus <> '-2' and client_fromtype ='0' and t.client_createtime <='{$todaytime}') as client_outnum,
        (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus <> '-2' and client_fromtype ='1' and t.client_createtime <='{$todaytime}') as client_innernum,
        (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus <> '-2' and client_fromtype ='2'and t.client_createtime <='{$todaytime}') as client_casenum,
           (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id and t.client_tracestatus =-1) as client_nointention_num,
              (select count(t.client_id) from crm_client as t ,crm_client_schoolenter as cs where cs.client_id = t.client_id and cs.school_id = s.school_id  and t.client_tracestatus =-2) as client_noeffctive_num
        FROM smc_school as s where {$datawhere} 
        order by (case when s.school_istest=0 and s.school_isclose=0 then 1 when s.school_isclose=0 then 2 when s.school_istest=0 then 3 else 4 end),s.school_istest asc,field(s.school_sort,0),s.school_sort asc,s.school_createtime asc";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['absenteenums'] = $dateexcelvar['absenteenums'];
                    $datearray['readingnums'] = $dateexcelvar['readingnums'];
                    $datearray['positivelog_history_num'] = $dateexcelvar['positivelog_history_num'];
                    $datearray['positivelog_week_num'] = $dateexcelvar['positivelog_week_num'];
                    $datearray['positivelog_month_num'] = $dateexcelvar['positivelog_month_num'];
                    $datearray['client_under_weeknum'] = $dateexcelvar['client_under_weeknum'];
                    $datearray['client_under_monthnum'] = $dateexcelvar['client_under_monthnum'];
                    $datearray['client_up_weeknum'] = $dateexcelvar['client_up_weeknum'];
                    $datearray['client_up_monthnum'] = $dateexcelvar['client_up_monthnum'];
                    $datearray['client_up_quarternum'] = $dateexcelvar['client_up_quarternum'];
                    $datearray['recomend_clientnum'] = $dateexcelvar['recomend_clientnum'];
                    $datearray['threemoth_clientnum'] = $dateexcelvar['threemoth_clientnum'];
                    $datearray['sixmoth_clientnum'] = $dateexcelvar['sixmoth_clientnum'];
                    $datearray['client_all_num'] = $dateexcelvar['client_all_num'];
                    $datearray['client_noallot_num'] = $dateexcelvar['client_noallot_num'];
                    $datearray['track_client_num'] = $dateexcelvar['track_client_num'];
                    $datearray['track_tracknum'] = $dateexcelvar['track_tracknum'];
                    $datearray['track_week_tephonenum'] = $dateexcelvar['track_week_tephonenum'];
                    $datearray['track_month_tephonenum'] = $dateexcelvar['track_month_tephonenum'];
                    $datearray['no_confirm'] = $dateexcelvar['audition_no_confirm'] + $dateexcelvar['invite_no_confirm'];
                    $datearray['invite_week_num'] = $dateexcelvar['invite_week_num'];
                    $datearray['invite_month_num'] = $dateexcelvar['invite_month_num'];
                    $datearray['ohaudition_curweek_num'] = $dateexcelvar['ohaudition_curweek_num'];
                    $datearray['jmcaudition_curweek_num'] = $dateexcelvar['jmcaudition_curweek_num'];
                    $datearray['ohaudition_lastweek_num'] = $dateexcelvar['ohaudition_lastweek_num'];
                    $datearray['ohaudition_lastweek_postnum'] = $dateexcelvar['ohaudition_lastweek_postnum'];
                    $datearray['ohaudition_week_num'] = $dateexcelvar['ohaudition_week_num'];
                    $datearray['ohaudition_month_num'] = $dateexcelvar['ohaudition_month_num'];
                    $datearray['ohaudition_week_arrnum'] = $dateexcelvar['ohaudition_week_arrnum'];
                    $datearray['ohaudition_month_arrnum'] = $dateexcelvar['ohaudition_month_arrnum'];
                    $datearray['audition_week_num'] = $dateexcelvar['audition_week_num'];
                    $datearray['audition_month_num'] = $dateexcelvar['audition_month_num'];
                    $datearray['ohpostive_month_arrnum'] = $dateexcelvar['ohpostive_month_arrnum'];
                    if ($dateexcelvar['ohaudition_month_arrnum']) {
                        $datearray['ohpostive_month_rate'] = round($dateexcelvar['ohpostive_month_arrnum'] / $dateexcelvar['ohaudition_month_arrnum'], 4) * 100;
                    } else {
                        $datearray['ohpostive_month_rate'] = 0;
                    }
                    $datearray['client_innernum'] = $dateexcelvar['client_innernum'];
                    $datearray['client_outnum'] = $dateexcelvar['client_outnum'];
                    $datearray['client_casenum'] = $dateexcelvar['client_casenum'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '在籍', '在读', '6月至今总招生', '报名/周', '报名/月', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/季', '推荐名单/月', '3个月内有效名单', '6个月内有效名单', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪人次/周', "电询/周", "电询/月", '未确认邀约数/月', '柜询/周', '柜询/月', 'OH邀约/当周', '插班邀约/当周', 'OH邀约/上周', 'OH邀约到访/上周', 'OH邀约/自然周', 'OH邀约/自然月', "OH到访/自然周", "OH到访/自然月", "试听/周", "试听/月", "OH转正数/月", "OH转正率/月", '内招名单数/月', '外招名单数/月', "专案名单数/月"));

            $excelfileds = array('school_shortname', 'school_branch', 'absenteenums', 'readingnums', 'positivelog_history_num', 'positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'recomend_clientnum', 'threemoth_clientnum', 'sixmoth_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_tracknum', 'track_week_tephonenum', 'track_month_tephonenum', 'no_confirm', 'invite_week_num', 'invite_month_num', 'ohaudition_curweek_num', 'jmcaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_lastweek_postnum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum', 'ohpostive_month_rate', 'client_innernum', 'client_outnum', 'client_casenum');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校务招生追踪大表-截止日期{$today}.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);
            $allNum = $this->AnalyzeControl->selectOne("select count(s.school_id)  as  allnum from smc_school as s where {$datawhere}");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            } else {
                foreach ($dataList as $key => $dataOne) {
                    if ($dataOne['ohaudition_month_arrnum']) {
                        $dataList[$key]['ohpostive_month_rate'] = round($dataOne['ohpostive_month_arrnum'] / $dataOne['ohaudition_month_arrnum'], 4) * 100;
                    } else {
                        $dataList[$key]['ohpostive_month_rate'] = 0;
                    }
                    $dataList[$key]['no_confirm'] = $dataOne['audition_no_confirm'] + $dataOne['invite_no_confirm'];
                }
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = $allNum['allnum'];
            return $result;
        }
    }

    /**
     * 渠道分析表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     * @param $request
     * @return mixed
     */
    function channelClientReport($request)
    {
        $datawhere = "cc.company_id='{$request['company_id']}'";
        $clientwhere = "c.company_id ='{$request['company_id']}'";
        $positivewhere = "c.company_id ='{$request['company_id']}' AND e.client_id = c.client_id";
        if (isset($request['keyword']) && $request['keyword'] !== "") {
            $datawhere .= " and (cc.frommedia_name like '%{$request['keyword']}%' )";
        }
        if (isset($request['start_time']) && $request['start_time'] !== "") {
            $stattime = strtotime($request['start_time']);
            $positivewhere .= " and DATE_FORMAT(e.positivelog_time,'%Y-%m-%d') >='{$request['start_time']}'";
            $clientwhere .= " and c.client_createtime >='{$stattime}'";
        }
        if (isset($request['end_time']) && $request['end_time'] !== "") {
            $endtime = date('Y-m-d H:i:s', strtotime($request['end_time']) + 60 * 60 * 24 - 1);
            $endime = strtotime($endtime);
            $positivewhere .= " and DATE_FORMAT(e.positivelog_time,'%Y-%m-%d') <= '{$request['end_time']}'";
            $clientwhere .= " and c.client_createtime <='{$endime}'";
        }
        if (isset($request['school_id']) && $request['school_id'] !== "") {
            $positivewhere .= " and e.school_id ='{$request['school_id']}'";
            $clientwhere .= " and e.school_id ='{$request['school_id']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $schoolwhere = " select group_concat(s.school_id) as school_ids from smc_school  as s where 1 and s.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
            $school_str = $this->DataControl->selectOne($schoolwhere);

            $positivewhere .= " and e.school_id in ({$school_str['school_ids']})";
            $clientwhere .= " and e.school_id in ({$school_str['school_ids']})";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }

        if (isset($request['channel_way']) && $request['channel_way'] != '') {
            $datawhere .= " and (select h.channel_id from crm_code_channel as h where h.company_id = cc.company_id and h.channel_medianame = cc.frommedia_name and h.channel_way = '{$request['channel_way']}' limit 0,1 ) > 0 ";
            $positivewhere .= " and l.channel_way = '{$request['channel_way']}'";
            $clientwhere .= " and l.channel_way = '{$request['channel_way']}'";
            $channelwhere = " and l.channel_way = '{$request['channel_way']}'";
        }
        if (isset($request['arr_frommedia_name']) && $request['arr_frommedia_name'] != "" && $request['arr_frommedia_name'] !== "[]") {
            $datawhere .= " and cc.frommedia_name = '{$request['arr_frommedia_name']}' ";
            $positivewhere .= " and c.client_source = '{$request['arr_frommedia_name']}'";
            $clientwhere .= " and c.client_source = '{$request['arr_frommedia_name']}'";
        }
        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select cc.frommedia_name
,(SELECT count(l.channel_id) FROM crm_code_channel AS l WHERE l.company_id = cc.company_id and l.channel_medianame = cc.frommedia_name and  exists (select t.client_id from crm_client as t where t.channel_id =l.channel_id ) {$channelwhere}) AS channel_num
from crm_code_frommedia as cc  
where {$datawhere} order by cc.frommedia_sort ASC,cc.frommedia_id ASC";
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->AnalyzeControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['frommedia_name'] = $val['frommedia_name'];
                    $datearray['channel_num'] = $val['channel_num'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newaddnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $datearray['client_newaddnums'] = $clientCount['client_newaddnums'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newvalidnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $datearray['client_newvalidnums'] = $clientCount['client_newvalidnums'];
                    $positivenum = $this->DataControl->selectOne("select count(e.client_id) as nums from crm_client_positivelog  as e,crm_client as c,crm_code_channel as l where e.client_id = c.client_id and c.client_source = '{$val['frommedia_name']}' and c.channel_id = l.channel_id and {$positivewhere}");
                    $datearray['positivenum'] = $positivenum['nums'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('渠道类型名称', '渠道明细数', '新增毛名单', '新增有效名单', '到访名单数', '到访率', '柜询邀约名单数', '柜询邀约到访名单数', 'OH邀约名单数', 'OH邀约到访名单数', "插班试听邀约名单数", '插班试听邀约到访名单数', '报名名单数'));
            $excelfileds = array('frommedia_name', 'channel_num', 'client_newaddnums', 'client_newvalidnums', 'inv_aud_arrivenum', 'inv_aud_rate', 'invite_num', 'invite_arrivenum', 'OH_audition_num', 'OH_audition_arrivenum', 'Class_audition_num', 'Class_audition_arrivenum', 'positivenum');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("渠道招生统计报表{$request['start_time']}-{$request['end_time']}.xlsx"));
            exit;
        } else {
            $sql .= "  limit {$pagestart},{$num}";
            $datalist = $this->DataControl->selectClear($sql);
            if ($datalist) {
                foreach ($datalist as &$val) {
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newaddnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id LEFT JOIN crm_code_channel as l ON c.channel_id = l.channel_id WHERE {$clientwhere} AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $val['client_newaddnums'] = $clientCount['client_newaddnums'];
                    $clientCount = $this->DataControl->selectOne("select count(c.client_id) AS client_newvalidnums FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id LEFT JOIN crm_code_channel as l ON c.channel_id = l.channel_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $val['client_newvalidnums'] = $clientCount['client_newvalidnums'];
                    $princal_num = $this->DataControl->selectOne("select count(c.client_id) AS princal_track_num FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id LEFT JOIN crm_code_channel as l ON c.channel_id = l.channel_id WHERE {$clientwhere} AND c.client_tracestatus <> '-2' and  c.client_tracestatus <> '-1' and c.client_distributionstatus =1 AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $val['princal_track_num'] = $princal_num['princal_track_num'] + 0;
                    $uneffective = $this->DataControl->selectOne("select count(c.client_id) AS uneffective_num FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id LEFT JOIN crm_code_channel as l ON c.channel_id = l.channel_id WHERE {$clientwhere} AND c.client_tracestatus = '-2'  AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $val['uneffective_num'] = $uneffective['uneffective_num'] + 0;
                    $no_intention = $this->DataControl->selectOne("select count(c.client_id) AS no_intention FROM crm_client as c LEFT JOIN crm_client_schoolenter AS e ON c.client_id = e.client_id LEFT JOIN crm_code_channel as l ON c.channel_id = l.channel_id WHERE {$clientwhere} AND c.client_tracestatus = '-1'  AND c.client_source = '{$val['frommedia_name']}' LIMIT 0,1");
                    $val['no_intention'] = $no_intention['no_intention'] + 0;
                    $positivenum = $this->DataControl->selectOne("select count(e.client_id) as nums from crm_client_positivelog  as e,crm_client as c,crm_code_channel as l  where e.client_id = c.client_id and c.client_source = '{$val['frommedia_name']}' and c.channel_id = l.channel_id and {$positivewhere}");
                    $val['positivenum'] = $positivenum['nums'];
                    $val['positive_rate'] = $val['client_newaddnums'] > 0 ? ($val['nums'] / $val['client_newaddnums']) * 100 . '%' : '0%';


                }
            }
            if (!$datalist) {
                $datalist = array();
            }
            $data['list'] = $datalist;
        }

        if (isset($request['is_count']) && $request['is_count'] == 1) {
            $mediaCount = $this->AnalyzeControl->selectOne("
                SELECT COUNT(cc.frommedia_id) AS countnums 
                FROM crm_code_frommedia AS cc 
                WHERE {$datawhere} 
                LIMIT 0,1 
                ");
            if (!empty($mediaCount)) {
                $data['allnums'] = $mediaCount['countnums'];
            } else {
                $data['allnums'] = 0;
            }
        } else {
            $data['allnums'] = 0;
        }
//        $data['positivewhere']=$school_str;
//        $data['clientwhere']=$clientwhere;

        return $data;

    }

    function customerTrack($request)
    {

        $datawhere = " 1 ";

        $having = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $having .= " and (client_cnname like '%{$request['keyword']}%' or client_enname like '%{$request['keyword']}%' or parenter_cnname like '%{$request['keyword']}%' or client_mobile like '%{$request['keyword']}%' or auxilStaffer like '%{$request['keyword']}%' or mainStaffer like '%{$request['keyword']}%')";
        }
        $time = date("Y-m", time());

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') <='{$request['endtime']}'";
        }

        if (isset($request['minnum']) && $request['minnum'] != '') {
            $having .= " and trackNum<='{$request['minnum']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and sc.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cl.client_id,sc.school_id,sc.school_shortname as school_cnname,sc.school_branch,cl.client_cnname,cl.client_enname,cl.client_sex,cl.client_age,cl.client_mobile,ifnull(p.parenter_cnname,'--') as parenter_cnname
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=1 and cp.principal_leave=0),'--') as mainStaffer
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=0 and cp.principal_leave=0),'--') as auxilStaffer
              ,(select count(tt.track_id) from crm_client_track as tt where tt.client_id=cl.client_id and tt.track_linktype='电话沟通' ) as trackNum
              from crm_client as cl
              left join crm_client_track as ct on ct.client_id=cl.client_id and ct.track_linktype='电话沟通' 
              inner join smc_school as sc on sc.school_id=ct.school_id 
              left join smc_parenter as p on p.parenter_mobile=cl.client_mobile
              where {$datawhere} and cl.company_id='{$request['company_id']}'
              group by ct.client_id,ct.school_id
              having {$having}
              order by sc.school_sort asc,cl.client_id asc,ct.track_createtime asc,ct.track_id asc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['mainStaffer'] = $dateexcelvar['mainStaffer'];
                    $datearray['auxilStaffer'] = $dateexcelvar['auxilStaffer'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    $datearray['trackNum'] = $dateexcelvar['trackNum'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "主要负责人", "协助负责人", "学员中文名", "学员英文名", "性别", "年龄", "联系电话", "家长姓名", "电访总次数"));
            $excelfileds = array('school_cnname', 'school_branch', 'mainStaffer', 'auxilStaffer', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_mobile', 'parenter_cnname', 'trackNum');

            $tem_name = "客户跟踪汇总表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);
            if (!$list) {
                $this->errortip = '无客户跟踪记录';
                $this->error = true;
                return false;
            }

            foreach ($list as &$listOne) {
                $listOne['client_mobile'] = hideNumberString($listOne['client_mobile']);
            }

            $count_sql = "select cl.client_cnname,cl.client_enname,cl.client_sex,cl.client_age,cl.client_mobile,ifnull(p.parenter_cnname,'--') as parenter_cnname
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=1 and cp.principal_leave=0),'--') as mainStaffer
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=0 and cp.principal_leave=0),'--') as auxilStaffer
              ,(select count(tt.track_id) from crm_client_track as tt where tt.client_id=cl.client_id and tt.track_linktype='电话沟通' ) as trackNum
              from crm_client as cl
              left join crm_client_track as ct on ct.client_id=cl.client_id and ct.track_linktype='电话沟通' 
              inner join smc_school as sc on sc.school_id=ct.school_id 
              left join smc_parenter as p on p.parenter_mobile=cl.client_mobile
              where {$datawhere}
              group by ct.client_id,ct.school_id
              having {$having}";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $data = array();
            $data['allnum'] = $db_nums ? count($db_nums) : 0;
            $data['list'] = $list;

            return $data;
        }

    }

    function customerTrackItem($request)
    {
        $datawhere = " 1 ";

        $having = " 1 ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $having .= " and (client_cnname like '%{$request['keyword']}%' or client_enname like '%{$request['keyword']}%' or parenter_cnname like '%{$request['keyword']}%' or client_mobile like '%{$request['keyword']}%' or mainStaffer like '%{$request['keyword']}%' or auxilStaffer like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') <='{$request['endtime']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and sc.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }

        if (isset($request['client_id']) && $request['client_id'] != '') {
            $datawhere .= " and cl.client_id='{$request['client_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select ct.track_id,cl.client_id,sc.school_id,sc.school_shortname as school_cnname,sc.school_branch,cl.client_cnname,cl.client_enname,cl.client_sex,cl.client_age,cl.client_mobile,ifnull(p.parenter_cnname,'--') as parenter_cnname,FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') as track_createtime,ct.track_note
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=1 and cp.principal_leave=0),'--') as mainStaffer
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=0 and cp.principal_leave=0),'--') as auxilStaffer
              from crm_client_track as ct 
              inner join crm_client as cl on ct.client_id=cl.client_id
              inner join smc_school as sc on sc.school_id=ct.school_id 
              left join smc_parenter as p on p.parenter_mobile=cl.client_mobile
              where {$datawhere} and cl.company_id='{$request['company_id']}' and ct.track_linktype='电话沟通'
              having {$having}
              order by sc.school_sort asc,cl.client_id asc,ct.track_createtime asc,ct.track_id asc
              ";


        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $key => $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['mainStaffer'] = $dateexcelvar['mainStaffer'];
                    $datearray['auxilStaffer'] = $dateexcelvar['auxilStaffer'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];

                    $sql = "select track_id from crm_client_track 
                      where client_id='{$dateexcelvar['client_id']}' and track_linktype='电话沟通'
                      order by track_createtime asc";
                    $trackList = $this->DataControl->selectClear($sql);

                    $array = array_flip(array_column($trackList, 'track_id'));

                    $datearray['times'] = "第" . ($array[$dateexcelvar['track_id']] + 1) . "次电访";

                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "主要负责人", "协助负责人", "学员中文名", "学员英文名", "性别", "年龄", "联系电话", "家长姓名", "电访次序", "电访日期", "电访内容"));
            $excelfileds = array('school_cnname', 'school_branch', 'mainStaffer', 'auxilStaffer', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_mobile', 'parenter_cnname', 'times', 'track_createtime', 'track_note');

            $tem_name = "客户跟踪明细表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);

            if (!$list) {
                $this->errortip = '无客户跟踪记录';
                $this->error = true;
                return false;
            }

            foreach ($list as &$val) {
                $val['client_mobile'] = hideNumberString($val['client_mobile']);

                $sql = "select track_id from crm_client_track 
                      where client_id='{$val['client_id']}' and track_linktype='电话沟通'
                      order by track_createtime asc";
                $trackList = $this->DataControl->selectClear($sql);

                $array = array_flip(array_column($trackList, 'track_id'));

                $val['times'] = "第" . ($array[$val['track_id']] + 1) . "次电访";
            }

            $count_sql = "select cl.client_cnname,cl.client_enname,cl.client_mobile,ifnull(p.parenter_cnname,'--') as parenter_cnname
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=1 and cp.principal_leave=0),'--') as mainStaffer
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=0 and cp.principal_leave=0),'--') as auxilStaffer
              from crm_client_track as ct 
              inner join crm_client as cl on ct.client_id=cl.client_id
              inner join smc_school as sc on sc.school_id=ct.school_id 
              left join smc_parenter as p on p.parenter_mobile=cl.client_mobile
              where {$datawhere} and cl.company_id='{$request['company_id']}' and ct.track_linktype='电话沟通'
              having {$having}
              ";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $data = array();
            $data['allnum'] = $db_nums ? count($db_nums) : 0;
            $data['list'] = $list;

            return $data;

        }
    }

    function counterInquiryList($request)
    {
        $datawhere = " 1 ";

        $having = " 1 ";

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $having .= " and (client_cnname like '%{$request['keyword']}%' or client_enname like '%{$request['keyword']}%' or parenter_cnname like '%{$request['keyword']}%' or client_mobile like '%{$request['keyword']}%' or mainStaffer like '%{$request['keyword']}%' or auxilStaffer like '%{$request['keyword']}%')";
        }

        if (isset($request['starttime']) && $request['starttime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') >='{$request['starttime']}'";
        }

        if (isset($request['endtime']) && $request['endtime'] != '') {
            $datawhere .= " and FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') <='{$request['endtime']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and sc.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }

        if (isset($request['client_id']) && $request['client_id'] != '') {
            $datawhere .= " and cl.client_id='{$request['client_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }

        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select cl.client_id,sc.school_id,sc.school_shortname as school_cnname,sc.school_branch,cl.client_cnname,cl.client_enname,cl.client_enname,cl.client_sex,cl.client_age,cl.client_mobile,ifnull(p.parenter_cnname,'--') as parenter_cnname,FROM_UNIXTIME(ct.track_createtime,'%Y-%m-%d') as track_createtime,ct.track_note
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=1 and cp.principal_leave=0),'--') as mainStaffer
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=0 and cp.principal_leave=0),'--') as auxilStaffer
              ,(select group_concat(sc.coursecat_cnname) from crm_client_intention as i inner join smc_code_coursecat as sc on sc.coursecat_id=i.coursecat_id where i.client_id=cl.client_id group by i.client_id) as course_cnname
              ,ifnull((select ca.audition_visittime from crm_client_audition as ca where ca.client_id=ci.client_id and ca.audition_isvisit=1 and ca.audition_genre=0 order by ca.audition_id desc limit 0,1),'--') as audition_visittime
              ,ifnull((select ca.audition_visittime from crm_client_audition as ca where ca.client_id=ci.client_id and ca.audition_isvisit=1 and ca.audition_genre=1 order by ca.audition_id desc limit 0,1),'--') as audition_visittime2
              from crm_client_invite as ci    
              inner join crm_client_track as ct on ci.track_id=ct.track_id
              inner join crm_client as cl on ct.client_id=cl.client_id
              inner join smc_school as sc on sc.school_id=ct.school_id and sc.company_id='{$request['company_id']}'
              left join smc_parenter as p on p.parenter_mobile=cl.client_mobile
              where {$datawhere} and cl.company_id='{$request['company_id']}' 
              -- and ct.track_linktype in ('柜询沟通','第三方柜询','能力测试')
              having {$having}
              order by sc.school_sort asc,cl.client_id,ct.track_createtime asc,ct.track_id asc
              ";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "暂无数据";
                return false;
            }

            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $key => $dateexcelvar) {
                    $datearray = array();
                    $datearray['school_cnname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['mainStaffer'] = $dateexcelvar['mainStaffer'];
                    $datearray['auxilStaffer'] = $dateexcelvar['auxilStaffer'];
                    $datearray['client_cnname'] = $dateexcelvar['client_cnname'];
                    $datearray['client_enname'] = $dateexcelvar['client_enname'];
                    $datearray['client_sex'] = $dateexcelvar['client_sex'];
                    $datearray['client_age'] = $dateexcelvar['client_age'];
                    $datearray['client_mobile'] = hideNumberString($dateexcelvar['client_mobile']);
                    $datearray['parenter_cnname'] = $dateexcelvar['parenter_cnname'];
                    $datearray['course_cnname'] = $dateexcelvar['course_cnname'];
                    $datearray['track_createtime'] = $dateexcelvar['track_createtime'];
                    $datearray['audition_visittime'] = $dateexcelvar['audition_visittime'];
                    $datearray['audition_visittime2'] = $dateexcelvar['audition_visittime2'];
                    $datearray['track_note'] = $dateexcelvar['track_note'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array("校区名称", "校区编号", "主要负责人", "协助负责人", "学员中文名", "学员英文名", "性别", "年龄", "联系电话", "家长姓名", "意向报名课程", "柜询日期", "公开课试听时间", "插班试听时间", "电访内容"));
            $excelfileds = array('school_cnname', 'school_branch', 'mainStaffer', 'auxilStaffer', 'client_cnname', 'client_enname', 'client_sex', 'client_age', 'client_mobile', 'parenter_cnname', 'course_cnname', 'track_createtime', 'audition_visittime', 'audition_visittime2', 'track_note');

            $tem_name = "客户跟踪明细表.xls";

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch($tem_name));
            exit;
        } else {
            $sql .= ' limit ' . $pagestart . ',' . $num;
            $list = $this->DataControl->selectClear($sql);

            if (!$list) {
                $this->errortip = '无柜询名单记录';
                $this->error = true;
                return false;
            }

            foreach ($list as &$listOne) {
                $listOne['client_mobile'] = hideNumberString($listOne['client_mobile']);
            }

            $count_sql = "select cl.client_cnname,cl.client_enname,cl.client_enname,cl.client_sex,cl.client_age,cl.client_mobile,ifnull(p.parenter_cnname,'--') as parenter_cnname
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=1 and cp.principal_leave=0),'--') as mainStaffer
              ,ifnull((select group_concat(concat(staffer_cnname,(CASE WHEN ifnull( st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END ))) from crm_client_principal as cp,crm_marketer as cm,smc_staffer as st where cp.marketer_id=cm.marketer_id and cm.staffer_id=st.staffer_id and cp.client_id=cl.client_id and cp.school_id=sc.school_id and cp.principal_ismajor=0 and cp.principal_leave=0),'--') as auxilStaffer
              from crm_client_invite as ci    
              inner join crm_client_track as ct on ci.track_id=ct.track_id
              inner join crm_client as cl on ct.client_id=cl.client_id
              inner join smc_school as sc on sc.school_id=ct.school_id 
              left join smc_parenter as p on p.parenter_mobile=cl.client_mobile 
              where {$datawhere} and cl.company_id='{$request['company_id']}' 
              -- and ct.track_linktype in ('柜询沟通','第三方柜询','能力测试')
              having {$having}
              order by cl.client_id,ct.track_createtime asc,ct.track_id asc";
            $db_nums = $this->DataControl->selectClear($count_sql);

            $data = array();
            $data['allnum'] = $db_nums ? count($db_nums) : 0;
            $data['list'] = $list;

            return $data;

        }
    }

    function enrollmentApi($paramArray)
    {
        $datawhere = "sf.company_id = '{$paramArray['company_id']}' and s.school_id > 0 and sf.account_class = 0 and sf.staffer_leave = 0";
        $clientwhere = "1";
        $invitenwhere = "1";
        $auditionwhere = "1";
        $positivewhere = "1";
        if (isset($paramArray['str_school_id']) && $paramArray['str_school_id'] !== '') {
            $datawhere .= " and s.school_branch in ({$paramArray['str_school_id']})";
//            $clientwhere .= " and s.school_branch in ({$paramArray['str_school_id']})";
//            $invitenwhere .= " and s.school_branch in ({$paramArray['str_school_id']})";
//            $auditionwhere .= " and s.school_branch in ({$paramArray['str_school_id']})";
//            $positivewhere .= " and s.school_branch in ({$paramArray['str_school_id']})";
        }
        if (isset($paramArray['post_id']) && $paramArray['post_id'] !== '') {
            $datawhere .= " and sf.post_id = '{$paramArray['post_id']}'";
        }
        if (isset($paramArray['starttime']) && $paramArray['starttime'] !== "") {
            $stattime = strtotime($paramArray['starttime']);
            $datawhere .= " and cp.principal_createtime >= '{$stattime}'";
            $positivewhere .= " and e.conversionlog_time >= '{$stattime}'";
            $auditionwhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >= '{$paramArray['starttime']}'";
            $invitenwhere .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') >= '{$paramArray['starttime']}'";
            $clientwhere .= " and p.principal_createtime >= '{$stattime}'";
        }
        if (isset($paramArray['endtime']) && $paramArray['endtime'] !== "") {
            $endtime = strtotime($paramArray['endtime'] . " 23:59:59");
            $datawhere .= " and cp.principal_createtime <= '{$endtime}'";
            $positivewhere .= " and e.conversionlog_time <= '{$endtime}'";
            $auditionwhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <= '{$paramArray['endtime']}'";
            $invitenwhere .= " and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') <= '{$paramArray['endtime']}'";
            $clientwhere .= " and p.principal_createtime <= '{$endtime}'";
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== "") {
            $datawhere .= " and (sf.staffer_cnname like '%{$paramArray['keyword']}%' or sf.staffer_enname like '%{$paramArray['keyword']}%' or sf.staffer_branch like '%{$paramArray['keyword']}%')";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT s.school_id,s.school_shortname as school_cnname,s.school_branch,sf.staffer_id,sf.staffer_cnname,sf.staffer_enname,sf.staffer_branch,m.marketer_id
                FROM crm_client_principal as cp
                LEFT JOIN crm_marketer as m ON m.marketer_id = cp.marketer_id
                LEFT JOIN smc_staffer as sf ON sf.staffer_id = m.staffer_id
                LEFT JOIN smc_school as s ON s.school_id = cp.school_id
                WHERE {$datawhere}
                GROUP BY cp.marketer_id,cp.school_id";

        if (!isset($paramArray['is_export']) || $paramArray['is_export'] != 1) {
            $sql .= " LIMIT {$pagestart},{$num}";
        }
        $dataList = $this->DataControl->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$val) {
                $sql = "SELECT
(SELECT COUNT(DISTINCT c.client_id)FROM crm_client_principal as p LEFT JOIN crm_client as c ON c.client_id = p.client_id WHERE p.marketer_id = m.marketer_id AND c.client_tracestatus <> 4 AND p.principal_leave = 0 AND p.school_id = '{$val['school_id']}' AND {$clientwhere}) as channel_num,
((SELECT COUNT(DISTINCT i.client_id) FROM crm_client_invite as i WHERE {$invitenwhere} AND i.marketer_id = m.marketer_id AND i.school_id = '{$val['school_id']}')
+ (SELECT COUNT(DISTINCT a.client_id) FROM crm_client_audition as a WHERE {$auditionwhere} AND a.marketer_id = m.marketer_id AND a.school_id = '{$val['school_id']}'
AND a.client_id NOT IN (SELECT i.client_id FROM crm_client_invite as i WHERE i.client_id = a.client_id AND {$invitenwhere} AND i.marketer_id = a.marketer_id AND i.invite_isvisit = '1') )) as inv_aud_num,
(SELECT COUNT(DISTINCT a.client_id) FROM crm_client_audition as a WHERE {$auditionwhere} AND a.marketer_id = m.marketer_id AND a.audition_genre = '1' AND a.school_id = '{$val['school_id']}' ) as audition_num,
(SELECT COUNT(DISTINCT i.client_id) FROM crm_client_invite as i WHERE {$invitenwhere} AND i.marketer_id = m.marketer_id AND i.school_id = '{$val['school_id']}' ) as invite_num,
(SELECT COUNT(DISTINCT a.client_id) FROM crm_client_audition as a WHERE {$auditionwhere} AND a.marketer_id = m.marketer_id AND a.audition_genre = '0' ) as OH_audition_num,
((SELECT COUNT(DISTINCT i.client_id) FROM crm_client_invite as i WHERE {$invitenwhere} AND i.invite_isvisit = '1' AND i.marketer_id = m.marketer_id AND i.school_id = '{$val['school_id']}')
+ (SELECT COUNT(DISTINCT a.client_id) FROM crm_client_audition as a WHERE {$auditionwhere} AND a.audition_isvisit = '1' AND a.marketer_id = m.marketer_id AND a.school_id = '{$val['school_id']}'
AND a.client_id NOT IN (SELECT i.client_id FROM crm_client_invite as i WHERE i.client_id = a.client_id AND {$invitenwhere} AND i.marketer_id = m.marketer_id AND i.invite_isvisit = '1') )) as visitnum,
((SELECT COUNT(DISTINCT c.client_id) FROM crm_client_invite as i,crm_client as c,crm_client_positivelog as p WHERE p.client_id=c.client_id and p.school_id=i.school_id  and DATE_FORMAT(i.invite_visittime,'%Y-%m-%d') =DATE_FORMAT(p.positivelog_time,'%Y-%m-%d') and  i.client_id = c.client_id and i.marketer_id = m.marketer_id  and {$invitenwhere} AND p.school_id = '{$val['school_id']}' AND i.invite_isvisit = '1')
 + (SELECT COUNT(DISTINCT a.client_id) FROM crm_client_audition as a,crm_client as c,crm_client_positivelog as p WHERE p.client_id =c.client_id and p.school_id = a.school_id and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') =DATE_FORMAT(p.positivelog_time,'%Y-%m-%d') and  a.client_id=c.client_id and a.marketer_id = m.marketer_id and {$auditionwhere} AND p.school_id = '{$val['school_id']}' AND a.audition_isvisit = '1'
  and a.client_id not in (SELECT i.client_id FROM crm_client_invite as i WHERE i.client_id = a.client_id and {$invitenwhere} AND i.marketer_id = m.marketer_id AND i.invite_isvisit = '1' ) ) ) as positivenum,
(SELECT cp.post_name FROM gmc_staffer_postbe as sp LEFT JOIN gmc_company_post AS cp ON sp.post_id = cp.post_id WHERE sp.staffer_id = '{$val['staffer_id']}' ORDER BY sp.postbe_createtime DESC LIMIT 1) as post_name
FROM crm_marketer as m WHERE m.marketer_id ='{$val['marketer_id']}'";
                $marketOne = $this->DataControl->selectOne($sql);
                $val['post_name'] = $marketOne['post_name'];
                $val['client_num'] = $marketOne['channel_num'];
                $val['inv_aud_num'] = $marketOne['inv_aud_num'];
                $val['audition_num'] = $marketOne['audition_num'];
                $val['invite_num'] = $marketOne['invite_num'];
                $val['OH_audition_num'] = $marketOne['OH_audition_num'];
                $val['visitnum'] = $marketOne['visitnum'];
                $val['positivenum'] = $marketOne['positivenum'];
                $val['positive_rate'] = $marketOne['visitnum'] > 0 ? round($marketOne['positivenum'] / $marketOne['visitnum'], 2) * 100 . '%' : '0%';
            }
        } else {
            $dataList = array();
        }
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $dataList;
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['school_cnname'] = $val['school_cnname'];
                    $datearray['school_branch'] = $val['school_branch'];
                    $datearray['staffer_cnname'] = $val['staffer_cnname'];
                    $datearray['staffer_enname'] = $val['staffer_enname'];
                    $datearray['staffer_branch'] = $val['staffer_branch'];
                    $datearray['post_name'] = $val['post_name'];
                    $datearray['channel_num'] = $val['channel_num'];
                    $datearray['inv_aud_num'] = $val['inv_aud_num'];
                    $datearray['visitnum'] = $val['visitnum'];
                    $datearray['audition_num'] = $val['audition_num'];
                    $datearray['invite_num'] = $val['invite_num'];
                    $datearray['OH_audition_num'] = $val['OH_audition_num'];
                    $datearray['positivenum'] = $val['positivenum'];
                    $datearray['positive_rate'] = $val['positive_rate'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '教师中文名', '教师英文名', '教师编号', '职务', '总名单数', '邀约总名单数', '邀约到访总名单数', '邀约试听名单数', '邀约柜询名单数', "OH邀约名单数", '报名名单数', '报名率'));
            $excelfileds = array('school_cnname', 'school_branch', 'staffer_cnname', 'staffer_enname', 'staffer_branch', 'post_name', 'client_num', 'inv_aud_num', 'visitnum', 'audition_num', 'invite_num', 'OH_audition_num', 'positivenum', 'positive_rate');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("招生工作统计表{$paramArray['starttime']}-{$paramArray['endtime']}.xlsx"));
            exit;
        }

        $data = array();
        $Count = $this->DataControl->selectOne("SELECT COUNT(q.staffer_id) as nums FROM
                                                  (SELECT sf.staffer_id
                                                  FROM crm_client_principal as cp
                                                  LEFT JOIN crm_marketer as m ON m.marketer_id = cp.marketer_id
                                                  LEFT JOIN smc_staffer as sf ON sf.staffer_id = m.staffer_id
                                                  LEFT JOIN smc_school as s ON s.school_id = cp.school_id
                                                  WHERE {$datawhere}
                                                  GROUP BY cp.marketer_id,cp.school_id) as q LIMIT 1");
        $data['allnums'] = $Count['nums'];
        $data['list'] = $dataList;
        return $data;
    }

    function openClassOperateApi($paramArray)
    {
        $datawhere = "cl.company_id = '{$paramArray['company_id']}'";
        $stafferwhere = " 1 ";

        if (isset($paramArray['schoolBranch']) && $paramArray['schoolBranch'] != '') {
            $datawhere .= " and sc.school_branch in ('" . implode("','", explode(',', $paramArray['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== "") {
            $stafferwhere .= " and (main_teacher like '%{$paramArray['keyword']}%' or less_teacher like '%{$paramArray['keyword']}%')";
        }
        if (isset($paramArray['start_day']) && $paramArray['start_day'] !== "") {
            $datawhere .= " AND h.hour_day >= '{$paramArray['start_day']}'";
        }
        if (isset($paramArray['end_day']) && $paramArray['end_day'] !== "") {
            $datawhere .= " AND h.hour_day <= '{$paramArray['end_day']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                sc.school_cnname,
                sc.school_branch,
                h.hour_id,
                h.hour_day,
                h.hour_starttime,
                h.hour_endtime,
                cl.class_id,
                ( SELECT GROUP_CONCAT(st.staffer_cnname,(CASE WHEN ifnull(st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )  ) FROM smc_class_teach AS t LEFT JOIN smc_staffer AS st ON t.staffer_id = st.staffer_id WHERE t.teach_type = '0' AND t.class_id = cl.class_id ) AS main_teacher,
                ( SELECT GROUP_CONCAT(st.staffer_cnname,(CASE WHEN ifnull(st.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', st.staffer_enname ) END )  ) FROM smc_class_teach AS t LEFT JOIN smc_staffer AS st ON t.staffer_id = st.staffer_id WHERE t.teach_type = '1' AND t.class_id = cl.class_id ) AS less_teacher,
                ( SELECT count( DISTINCT ( a.client_id )) FROM crm_client_audition AS a WHERE a.hour_id = h.hour_id AND a.audition_genre = '0' ) AS allnum,
                ( SELECT count( DISTINCT ( a.client_id )) FROM crm_client_audition AS a WHERE a.hour_id = h.hour_id AND a.audition_genre = '0' and audition_isvisit = '1' ) AS arrivenum,
                ( SELECT count( DISTINCT ( a.client_id )) FROM crm_client_audition AS a left join crm_client as i on i.client_id = a.client_id WHERE a.hour_id = h.hour_id AND a.audition_genre = '0' and i.client_tracestatus = '4') AS fullnum 
            FROM
                smc_class_hour AS h
                LEFT JOIN smc_class AS cl ON h.class_id = cl.class_id
                LEFT JOIN smc_course AS c ON cl.course_id = c.course_id 
                left join smc_school as sc on sc.school_id = cl.school_id
            WHERE
                {$datawhere} and c.course_inclasstype = '3'
	        HAVING {$stafferwhere} ";
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['time'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                    $datearray['main_teacher'] = $val['main_teacher'];
                    $datearray['less_teacher'] = $val['less_teacher'];
                    $datearray['allnum'] = $val['allnum'];
                    $datearray['smcnum'] = $val['allnum'];
                    $datearray['gmcnum'] = '--';
                    $datearray['arrivenum'] = $val['arrivenum'];
                    $datearray['arriverate'] = sprintf("%.2f", $val['arrivenum'] / $val['allnum'] * 100) . '%';
                    $datearray['gmcnum'] = '--';
                    $datearray['gmcnum'] = '--';
                    $datearray['fullnum'] = $val['fullnum'];
                    $datearray['fullrate'] = sprintf("%.2f", $val['fullnum'] / $val['allnum'] * 100) . '%';
                    $datearray['gmcnum'] = '--';
                    $datearray['gmcnum'] = '--';
                    $datearray['fullnum'] = $val['fullnum'];
                    $datearray['fullrate'] = sprintf("%.2f", $val['fullnum'] / $val['allnum'] * 100) . '%';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('公开课时间', '主教老师', '助教老师', '邀约总人数', '学校邀约人数', '集团邀约人数', '学校邀约到访人数', '学校邀约到访率', '集团邀约到访人数', '集团邀约到访率', "学校邀约报名数", '学校邀约报名率', '集团邀约报名数', '集团邀约报名率', '报名总人数', '报名率'));
            $excelfileds = array('time', 'main_teacher', 'less_teacher', 'allnum', 'smcnum', 'gmcnum', 'arrivenum', 'arriverate', 'gmcnum', 'gmcnum', 'fullnum', 'fullrate', 'gmcnum', 'gmcnum', 'fullnum', 'fullrate');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("公开课运作表.xlsx"));
            exit;
        } else {
            $dataList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");
            if ($dataList) {
                foreach ($dataList as &$val) {
                    $val['smcnum'] = $val['allnum'];
                    $val['gmcnum'] = '--';
                    $val['arriverate'] = sprintf("%.2f", $val['arrivenum'] / $val['allnum'] * 100) . '%';
                    $val['fullrate'] = sprintf("%.2f", $val['fullnum'] / $val['allnum'] * 100) . '%';
                    $val['time'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];

                }
            } else {
                $dataList = array();
            }
        }

        $data = array();

        $Count = $this->AnalyzeControl->selectOne("SELECT COUNT(h.hour_id) as nums FROM
                smc_class_hour AS h
                LEFT JOIN smc_class AS cl ON h.class_id = cl.class_id
                LEFT JOIN smc_course AS c ON cl.course_id = c.course_id 
                left join smc_school as sc on sc.school_id = cl.school_id
            WHERE
                {$datawhere} and c.course_inclasstype = '3'");
        if ($Count) {
            $data['allnums'] = $Count['nums'];
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $dataList;
        return $data;
    }

    function openClassListApi($paramArray)
    {
        $datawhere = "a.company_id = '{$paramArray['company_id']}'";
        if (isset($paramArray['schoolBranch']) && $paramArray['schoolBranch'] != '') {
            $datawhere .= " and s.school_branch in ('" . implode("','", explode(',', $paramArray['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($paramArray['keyword']) && $paramArray['keyword'] !== "") {
            $datawhere .= " and (m.marketer_name like '%{$paramArray['keyword']}%' or mm.marketer_name like '%{$paramArray['keyword']}%' or c.client_mobile like '%{$paramArray['keyword']}%' or c.client_cnname like '%{$paramArray['keyword']}%' or c.client_enname like '%{$paramArray['keyword']}%')";
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and ci.school_id ='{$request['school_id']}'";
        }
        if (isset($paramArray['start_day']) && $paramArray['start_day'] !== "") {
            $datawhere .= " AND ho.hour_day >= '{$paramArray['start_day']}'";
        }
        if (isset($paramArray['end_day']) && $paramArray['end_day'] !== "") {
            $datawhere .= " AND ho.hour_day <= '{$paramArray['end_day']}'";
        }
        if (isset($paramArray['main_id']) && $paramArray['main_id'] !== "") {
            $datawhere .= " and m.marketer_id = '{$paramArray['main_id']}'";
        }
        if (isset($paramArray['less_id']) && $paramArray['less_id'] !== "") {
            $datawhere .= " and mm.marketer_id = '{$paramArray['marketer_id']}'";
        }

        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            SELECT
                    c.client_id,
                    ho.hour_id,
                    ho.hour_day,
                    ho.hour_starttime,
                    ho.hour_endtime,
                    s.school_shortname as school_cnname,
                    s.school_branch,
                    c.client_cnname,
                    c.client_enname,
                    GROUP_CONCAT(sf.staffer_cnname,(CASE WHEN ifnull(sf.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sf.staffer_enname ) END )  ) as marketer_name,
                    GROUP_CONCAT(sff.staffer_cnname,(CASE WHEN ifnull(sff.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', sff.staffer_enname ) END )  ) as fu_marketer_name,
                    c.client_sex,
                    c.client_age,
                    c.client_mobile,
                    (
                    SELECT
                        p.parenter_cnname 
                    FROM
                        crm_client_family AS f
                        LEFT JOIN smc_parenter AS p ON p.parenter_id = f.parenter_id 
                    WHERE
                        f.client_id = c.client_id 
                    ORDER BY
                        f.family_isdefault DESC 
                        LIMIT 0,
                        1 
                    ) AS family_cnname,
                    ci.receiver_name,
                    ci.invite_isvisit,
                    c.client_remark 
                FROM
                    crm_client_audition AS a
	LEFT JOIN smc_class_hour AS ho ON a.hour_id = ho.hour_id
	LEFT JOIN crm_client AS c ON a.client_id = c.client_id
	LEFT JOIN crm_client_invite AS ci ON ci.client_id = c.client_id
	LEFT JOIN smc_school AS s ON s.school_id = a.school_id
	LEFT JOIN crm_client_principal AS p ON p.client_id = a.client_id 
	AND p.principal_ismajor = 1 
	AND p.principal_leave = 0 
	AND a.school_id = p.school_id 
	LEFT JOIN crm_client_principal AS pp ON pp.client_id = a.client_id 
	AND pp.principal_ismajor = 0 
	AND pp.principal_leave = 0 
	AND a.school_id = pp.school_id
	LEFT JOIN crm_marketer AS m ON m.marketer_id = p.marketer_id
	LEFT JOIN crm_marketer AS mm ON mm.marketer_id = pp.marketer_id
	LEFT JOIN crm_marketer AS mr ON mr.marketer_id = c.marketer_id
	LEFT JOIN smc_staffer AS sf ON sf.staffer_id = m.staffer_id
	LEFT JOIN smc_staffer AS sff ON sff.staffer_id = mm.staffer_id
	LEFT JOIN crm_code_nearschool AS nl ON nl.nearschool_id = c.nearschool_id
                WHERE
                    {$datawhere}
                    AND a.hour_id > '0'
                    GROUP BY c.client_id";
//        var_dump($sql);
        if (isset($paramArray['is_export']) && $paramArray['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as $val) {
                    $datearray = array();
                    $datearray['time'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                    $datearray['main_teacher'] = $val['main_teacher'];
                    $datearray['less_teacher'] = $val['less_teacher'];
                    $datearray['allnum'] = $val['allnum'];
                    $datearray['smcnum'] = $val['allnum'];
                    $datearray['gmcnum'] = '--';
                    $datearray['arrivenum'] = $val['arrivenum'];
                    $datearray['arriverate'] = $val['arrivenum'] / $val['allnum'] * 100 . '%';
                    $datearray['gmcnum'] = '--';
                    $datearray['gmcnum'] = '--';
                    $datearray['fullnum'] = $val['fullnum'];
                    $datearray['fullrate'] = $val['fullnum'] / $val['allnum'] * 100 . '%';
                    $datearray['gmcnum'] = '--';
                    $datearray['gmcnum'] = '--';
                    $datearray['fullnum'] = $val['fullnum'];
                    $datearray['fullrate'] = $val['fullnum'] / $val['allnum'] * 100 . '%';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('公开课时间', '主教老师', '助教老师', '邀约总人数', '学校邀约人数', '集团邀约人数', '学校邀约到访人数', '学校邀约到访率', '集团邀约到访人数', '集团邀约到访率', "学校邀约报名数", '学校邀约报名率', '集团邀约报名数', '集团邀约报名率', '报名总人数', '报名率'));
            $excelfileds = array('time', 'main_teacher', 'less_teacher', 'allnum', 'smcnum', 'gmcnum', 'arrivenum', 'arriverate', 'gmcnum', 'gmcnum', 'fullnum', 'fullrate', 'gmcnum', 'gmcnum', 'fullnum', 'fullrate');

            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("公开课运作表.xlsx"));
            exit;
        } else {
            $countList = $this->DataControl->selectClear($sql);
            $dataList = $this->DataControl->selectClear($sql . " LIMIT {$pagestart},{$num}");
            if ($dataList) {
                $status = $this->LgArraySwitch(array("0" => "待试听", "1" => "已到访", "2" => "未到访"));

                foreach ($dataList as &$val) {
                    $val['time'] = $val['hour_day'] . ' ' . $val['hour_starttime'] . '-' . $val['hour_endtime'];
                    $val['invite_isvisit'] = $status[$val['invite_isvisit']];
                    $val['client_mobile'] = hideNumberString($val['client_mobile']);
                }
            } else {
                $dataList = array();
            }
        }

        $data = array();

        $Count = count($countList);
        if ($Count) {
            $data['allnums'] = $Count;
        } else {
            $data['allnums'] = 0;
        }
        $data['list'] = $dataList;
        return $data;
    }

    /**
     * 渠道月度分析表
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/23 0023
     * @param $request
     */
    function channelMonthReport($request)
    {
        if (isset($request['year_start_month']) && $request['year_start_month'] != '') {
            $year_start_day = $request['year_start_month'] . '-01';
        } else {
            $year_start_day = date("Y-01-01");
        }
        $first_sec = strtotime($year_start_day);

        if (isset($request['year_end_month']) && $request['year_end_month'] != '') {
            $year_end_day = date('Y-m-d', strtotime("+1 month", strtotime($request['year_end_month'])));
        } else {
            $year_end_day = date("Y-m-d", strtotime("+1 month", strtotime(date("Y-m-01"))));
        }
        $last_sec = strtotime($year_end_day);

        $datawhere = " ct.client_createtime >='{$first_sec}'  and  ct.client_createtime <'{$last_sec}'";

        if (isset($request['channel_way']) && $request['channel_way'] != '') {
            $datawhere .= " and l.channel_way = '{$request['channel_way']}'";
        }

        if (isset($request['arr_frommedia_name']) && $request['arr_frommedia_name'] !== "" && $request['arr_frommedia_name'] !== "[]") {
//            $commodeArray = json_decode(stripslashes($request['arr_frommedia_name']), 1);
//            if (is_array($commodeArray) && count($commodeArray) > 0) {
//                $commodestr = '';
//                foreach ($commodeArray as $commodevar) {
//                    $commodestr .= "'" . $commodevar . "'" . ',';
//                }
//                $commodestr = substr($commodestr, 0, -1);
//                $datawhere .= " and f.frommedia_name in ({$commodestr}) ";
//            } 
            $datawhere .= " and f.frommedia_name = '{$request['arr_frommedia_name']}' ";
        }

        $sql = "
            select  f.frommedia_id,f.frommedia_name,
            count(1) as client_allnum,
            FROM_UNIXTIME(ct.client_createtime,'%Y-%m') as client_month
            from crm_code_frommedia as f 
            left join crm_code_channel as l ON l.channel_medianame =f.frommedia_name and l.company_id = f.company_id 
            left join crm_client as ct ON l.channel_id=ct.channel_id and ct.company_id=l.company_id
            where f.company_id = '{$request['company_id']}' and {$datawhere}
            group by f.frommedia_id,client_month
            order by client_month,f.frommedia_id
        ";
//        sum( case when ct.client_tracestatus =4 then 1 else 0 end) as posistive_num,
        $dataList = $this->DataControl->selectClear($sql);
//var_dump($dataList);die;
        $sql = "
            select FROM_UNIXTIME(ct.client_createtime,'%Y-%m') as client_month
            from crm_code_frommedia as f 
            left join crm_code_channel as l ON l.channel_medianame =f.frommedia_name and l.company_id = f.company_id 
            left join crm_client as ct ON l.channel_id=ct.channel_id and ct.company_id=l.company_id
            where f.company_id = '{$request['company_id']}' and {$datawhere}
            group by client_month
            order by client_month
        ";
        $monthList = $this->DataControl->selectClear($sql);

        $sql = "
            select f.frommedia_id,f.frommedia_name
            from crm_code_frommedia as f 
            left join crm_code_channel as l ON l.channel_medianame =f.frommedia_name and l.company_id = f.company_id 
            left join crm_client as ct ON l.channel_id=ct.channel_id and ct.company_id=l.company_id
            where f.company_id = '{$request['company_id']}' and {$datawhere}
            group by f.frommedia_id
            order by f.frommedia_id
        ";
        $mediaList = $this->DataControl->selectClear($sql);

        $k = 0;
        $field = array();
        $field[$k]["fieldstring"] = "client_month";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "month_total_num";
        $field[$k]["fieldname"] = "总名单数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $res_list = array();
        if ($dataList) {
            foreach ($mediaList as $mediakey => $mediaOne) {
                $field[$k]["fieldstring"] = "media_" . $mediaOne['frommedia_id'];
                $field[$k]["fieldname"] = $mediaOne['frommedia_name'];
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $k++;
            }

            $montharray = array();
            foreach ($monthList as $monthkey => $monthOne) {

                array_push($montharray, $monthOne['client_month']);

                $rowOne = array();
                $rowOne['client_month'] = $monthOne['client_month'];
                $rowOne['month_total_num'] = 0;

                foreach ($mediaList as $mediaOne) {
                    $mediastring = "media_" . $mediaOne['frommedia_id'];
                    $rowOne[$mediastring] = 0;
                }
                $res_list[] = $rowOne;
            }

            foreach ($dataList as $dataOne) {
                $monthkey = array_search($dataOne['client_month'], $montharray);
                $mediastring = "media_" . $dataOne['frommedia_id'];

                $res_list[$monthkey]['month_total_num'] += $dataOne['client_allnum'];
                $res_list[$monthkey][$mediastring] = $dataOne['client_allnum'];
            }
        }

        $result = array();
        $result['field'] = $field;
        $result['list'] = $res_list;
        $result['allnum'] = count($montharray);
        return $result;
    }

    /**
     * 获取学校信息
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/27 0027
     * @param $request
     * @return array|bool
     */
    function getSchoolApi($request)
    {
        $dataList = $this->DataControl->selectClear("select school_id,school_cnname,school_branch,school_shortname from smc_school where  company_id='{$request['company_id']}'");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    /**
     * 获取职工
     * author: 瞿
     * @param $request
     * @return array|bool
     */
    function getMarketerApi($request)
    {
        $dataList = $this->DataControl->selectClear("select marketer_id,marketer_name from crm_marketer where  company_id='{$request['company_id']}'");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    /**
     * 获取职工
     * author: 瞿
     * @param $request
     * @return array|bool
     */
    function getStafferApi($request)
    {
        $dataList = $this->DataControl->selectClear("select staffer_id,staffer_cnname from smc_staffer where  company_id='{$request['company_id']}' and staffer_leave = '0'");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    /**
     * 获取班组
     * author: ling
     * 对应接口文档 0001
     * Date 2021/2/27 0027
     * @param $request
     * @return array|bool
     */
    function getCourseTypeApi($request)
    {
        $dataList = $this->DataControl->selectClear("
select coursetype_id,coursetype_cnname,coursetype_branch 
from smc_code_coursetype 
where  company_id='{$request['company_id']}'
");
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    function studentAuditionOperateReport($request)
    {
        $datawhere = " and C.company_id = '{$request['company_id']}' AND C.school_isclose = '0' AND C.school_istest <> '1'";

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and C.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and C.school_branch ='{$request['school_id']}'";
        }
        if (isset($request['audition_genre']) && $request['audition_genre'] != '') {
            $datawhere .= " and A.audition_genre ='{$request['audition_genre']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== "") {
            $starttime = strtotime($request['starttime']);
            $datawhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$request['starttime']}' ";
        } else {
            $request['starttime'] = date("Y-m-01");
        }
        $datawhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$request['starttime']}' ";

        if (isset($request['endtime']) && $request['endtime'] !== "") {
            $endtime = strtotime($request['endtime']);
        } else {
            $request['endtime'] = date("Y-m-d");
        }
        $datawhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$request['endtime']}' ";

        $havingwhere = ' group by a.audition_id having 1 ';
        if (isset($request['keyword']) && $request['keyword'] !== '') {
//            $havingwhere .= " and (B.client_cnname like '%{$request['keyword']}%'
//            or B.client_enname like '%{$request['keyword']}%'
//            or D.parenter_cnname like '%{$request['keyword']}%'
//            or D.parenter_cnname like '%{$request['keyword']}%'
//            or D.parenter_cnname like '%{$request['keyword']}%'
//            or B.client_mobile like '%{$request['keyword']}%')";
            $havingwhere .= " and (client_cnname like '%{$request['keyword']}%' 
            or client_enname like '%{$request['keyword']}%' 
            or parenter_cnname like '%{$request['keyword']}%' 
            or main_principal like '%{$request['keyword']}%' 
            or sub_principal like '%{$request['keyword']}%' 
            or client_mobile like '%{$request['keyword']}%')";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = " select  C.school_id,(case when C.school_shortname='' then C.school_cnname else C.school_shortname end) as school_cnname,C.school_branch,
            B.client_cnname,B.client_enname,B.client_sex,B.client_age,B.client_mobile,
            D.parenter_cnname,
            (select GROUP_CONCAT(Z.staffer_cnname,(CASE WHEN ifnull(Z.staffer_enname, '' ) = '' THEN '' ELSE concat( '-',Z.staffer_enname ) END )  ) from crm_client_principal X,crm_marketer Y ,smc_staffer Z
            WHERE X.marketer_id=Y.marketer_id AND Y.staffer_id=Z.staffer_id AND principal_ismajor=1 and X.school_id=A.school_id AND X.client_id=A.client_id and principal_leave=0) AS main_principal,
            (select GROUP_CONCAT(Z.staffer_cnname,(CASE WHEN ifnull(Z.staffer_enname, '' ) = '' THEN '' ELSE concat( '-',Z.staffer_enname ) END )  ) from crm_client_principal X,crm_marketer Y ,smc_staffer Z
            WHERE X.marketer_id=Y.marketer_id AND Y.staffer_id=Z.staffer_id AND principal_ismajor=0 and X.school_id=A.school_id AND X.client_id=A.client_id and principal_leave=0) AS sub_principal,
            A.audition_genre,A.receiver_name,A.class_cnname,E.course_cnname,E.course_branch,
            (SELECT COUNT(1) FROM smc_student_study WHERE class_id=A.class_id) AS study_num,
            (SELECT GROUP_CONCAT(y.staffer_cnname,(CASE WHEN ifnull( y.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', y.staffer_enname ) END )  )from smc_class_hour_teaching x,smc_staffer y
            where x.staffer_id=y.staffer_id and x.hour_id=A.hour_id and x.teaching_type=0 and x.hour_id>0) AS main_staffer,
            (SELECT GROUP_CONCAT(y.staffer_cnname,(CASE WHEN ifnull( y.staffer_enname, '' ) = '' THEN '' ELSE concat( '-', y.staffer_enname ) END )  )from smc_class_hour_teaching x,smc_staffer y
            where x.staffer_id=y.staffer_id and x.hour_id=A.hour_id and x.teaching_type=1 and x.hour_id>0) AS sub_staffer,
            A.audition_visittime,A.audition_isvisit,
            (SELECT MIN(audition_visittime) FROM crm_client_audition X WHERE X.client_id=A.client_id AND X.school_id=A.school_id)AS first_visittime,
            (SELECT COUNT(1) FROM crm_client_audition X WHERE X.client_id=A.client_id AND X.school_id=A.school_id and X.audition_visittime<=A.audition_visittime and x.audition_id<a.audition_id)+1 AS audition_times,
            (SELECT positivelog_time FROM crm_client_positivelog WHERE school_id=A.school_id AND client_id=A.client_id) AS register_date,
            B.client_remark 
            from crm_client_audition A 
            LEFT JOIN crm_client B ON A.client_id=B.client_id 
            LEFT JOIN smc_school C ON A.school_id=C.school_id 
            LEFT JOIN crm_client_family F ON F.client_id=B.client_id and f.family_isdefault =1
            LEFT JOIN smc_parenter D ON D.parenter_id = F.parenter_id
            LEFT JOIN smc_course E ON A.course_id=E.course_id 
            WHERE 1 {$datawhere}
            {$havingwhere}
            order by C.school_sort asc,A.audition_visittime";
        $audition_genre = $this->LgArraySwitch(array("0" => "公开课试听", "1" => "插班试听"));
        $audition_isvisit = $this->LgArraySwitch(array("0" => "待试听", "1" => "已到访", "-1" => "未到访"));
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_shortname'] = $dateexcelvar['school_shortname'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '在籍', '在读', '6月至今总招生', '报名/周', '报名/月', '新增线下名单/周', '新增线下名单/月', '新增线上名单/周', '新增线上名单/月', '新增名单/季', '推荐名单/月', '3个月内有效名单', '6个月内有效名单', '总有效名单', '待分配名单数', '追踪名单数/周', '追踪人次/周', "电询/周", "电询/月", '未确认邀约数/月', '柜询/周', '柜询/月', 'OH邀约/当周', '插班邀约/当周', 'OH邀约/上周', 'OH邀约到访/上周', 'OH邀约/自然周', 'OH邀约/自然月', "OH到访/自然周", "OH到访/自然月", "试听/周", "试听/月", "OH转正数/月", "OH转正率/月", '内招名单数/月', '外招名单数/月', "专案名单数/月"));

            $excelfileds = array('school_shortname', 'school_branch', 'absenteenums', 'readingnums', 'positivelog_history_num', 'positivelog_week_num', 'positivelog_month_num', 'client_under_weeknum', 'client_under_monthnum', 'client_up_weeknum', 'client_up_monthnum', 'client_up_quarternum', 'recomend_clientnum', 'threemoth_clientnum', 'sixmoth_clientnum', 'client_all_num', 'client_noallot_num', 'track_client_num', 'track_tracknum', 'track_week_tephonenum', 'track_month_tephonenum', 'no_confirm', 'invite_week_num', 'invite_month_num', 'ohaudition_curweek_num', 'jmcaudition_curweek_num', 'ohaudition_lastweek_num', 'ohaudition_lastweek_postnum', 'ohaudition_week_num', 'ohaudition_month_num', 'ohaudition_week_arrnum', 'ohaudition_month_arrnum', 'audition_week_num', 'audition_month_num', 'ohpostive_month_arrnum', 'ohpostive_month_rate', 'client_innernum', 'client_outnum', 'client_casenum');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("校务招生追踪大表-截止日期.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {

                foreach ($dataList as &$val) {
                    $val['client_mobile'] = hideNumberString($val['client_mobile']);
                    $val['audition_genre'] = $audition_genre[$val['audition_genre']];
                    $val['audition_isvisit'] = $audition_isvisit[$val['audition_isvisit']];
                }
            } else {
                $dataList = array();
            }

            $allNum = $this->AnalyzeControl->selectClear("select B.client_cnname,B.client_enname,B.client_mobile,
            D.parenter_cnname,
            (select GROUP_CONCAT(Z.staffer_cnname,(CASE WHEN ifnull(Z.staffer_enname, '' ) = '' THEN '' ELSE concat( '-',Z.staffer_enname ) END )  ) from crm_client_principal X,crm_marketer Y ,smc_staffer Z
            WHERE X.marketer_id=Y.marketer_id AND Y.staffer_id=Z.staffer_id AND principal_ismajor=1 and X.school_id=A.school_id AND X.client_id=A.client_id and principal_leave=0) AS main_principal,
            (select GROUP_CONCAT(Z.staffer_cnname,(CASE WHEN ifnull(Z.staffer_enname, '' ) = '' THEN '' ELSE concat( '-',Z.staffer_enname ) END )  ) from crm_client_principal X,crm_marketer Y ,smc_staffer Z
            WHERE X.marketer_id=Y.marketer_id AND Y.staffer_id=Z.staffer_id AND principal_ismajor=0 and X.school_id=A.school_id AND X.client_id=A.client_id and principal_leave=0) AS sub_principal           
            from crm_client_audition A 
            LEFT JOIN crm_client B ON A.client_id=B.client_id
            LEFT JOIN smc_school C ON A.school_id=C.school_id
            LEFT JOIN crm_client_family F ON F.client_id=B.client_id and f.family_isdefault =1
            LEFT JOIN smc_parenter D ON D.parenter_id = F.parenter_id
            LEFT JOIN smc_course E ON A.course_id=E.course_id
            WHERE 1 {$datawhere}
            {$havingwhere}");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }

    }

    function afterClassInfoReport($request)
    {
        $datawhere = " and C.company_id = '{$request['company_id']}' AND C.school_isclose = '0' AND C.school_istest <> '1'";

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and C.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and C.school_branch ='{$request['school_id']}'";
        }

//        if (isset($request['starttime']) && $request['starttime'] !== "") {
//            $starttime = strtotime($request['starttime']);
//            $datawhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$request['starttime']}' ";
//        } else {
//            $request['starttime'] = date("Y-m-01");
//        }
//        $datawhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') >='{$request['starttime']}' ";
//
//        if (isset($request['endtime']) && $request['endtime'] !== "") {
//            $endtime = strtotime($request['endtime']);
//        } else {
//            $request['endtime'] = date("Y-m-d");
//        }
//        $datawhere .= " and DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') <='{$request['endtime']}' ";

//        if (isset($request['keyword']) && $request['keyword'] !== '') {
//            $datawhere .= " and (client_cnname like '%{$request['keyword']}%'
//            or client_enname like '%{$request['keyword']}%'
//            or parenter_cnname like '%{$request['keyword']}%'
//            or main_principal like '%{$request['keyword']}%'
//            or sub_principal like '%{$request['keyword']}%'
//            or client_mobile like '%{$request['keyword']}%')";
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select school_name,school_branch,
                MAX(register_num) AS register_num,
                SUM(reading_num) AS reading_num,
                SUM(classroom_num) AS classroom_num,
                count(class_id) AS class_num,
                ROUND(SUM(reading_num)/count(class_id),2) as class_avg_num
                ,new_regi_num
                from(
                SELECT C.school_id,
                C.school_shortname as school_name,
                C.school_branch,
                A.class_id,
                (SELECT count(distinct x.student_id) FROM smc_student_coursebalance x,smc_course y 
                WHERE x.course_id = y.course_id and x.school_id=C.school_id and y.coursetype_id=b.coursetype_id and x.coursebalance_time > 0 ) AS register_num,
                (SELECT COUNT(1) FROM smc_student_study WHERE class_id=A.class_id and study_beginday<=CURDATE() and study_endday>=CURDATE()) AS reading_num,
                (SELECT COUNT(DISTINCT classroom_id) FROM smc_class_hour WHERE class_id=A.class_id AND hour_ischecking<>-1) AS classroom_num,
                (select count(1) from smc_student_registerinfo where school_id=a.school_id and coursetype_id=B.coursetype_id and info_status=1 
                    and FROM_UNIXTIME(pay_successtime)>='2020-11-01') as new_regi_num
                FROM smc_class A 
                LEFT JOIN smc_course B ON A.course_id=B.course_id 
                LEFT JOIN smc_school C ON C.school_id=A.school_id 
                WHERE 1
                {$datawhere}
                AND B.coursetype_id='64'
                AND A.class_type=0
                AND A.class_status>=0
                AND A.class_stdate<=CURDATE()
                AND A.class_enddate>=CURDATE()
                )TA
                where 1
                group by TA.school_id
                ORDER BY school_branch";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $datearray['reading_num'] = $dateexcelvar['reading_num'];
                    $datearray['classroom_num'] = $dateexcelvar['classroom_num'];
                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['class_avg_num'] = $dateexcelvar['class_avg_num'];
                    $datearray['new_regi_num'] = $dateexcelvar['new_regi_num'] ;
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '在籍人数', '在读人数', '教室使用数', '在开班级数', '班平均', '本学期新生累计人数'));

            $excelfileds = array('school_name', 'school_branch', 'register_num', 'reading_num', 'classroom_num', 'class_num', 'class_avg_num', 'new_regi_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课辅班运作表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

//            if ($dataList) {
//
//                foreach ($dataList as &$val) {
//                    $val['client_mobile'] = hideNumberString($val['client_mobile']);
//                    $val['audition_genre'] = $audition_genre[$val['audition_genre']];
//                    $val['audition_isvisit'] = $audition_isvisit[$val['audition_isvisit']];
//                }
//            } else {
//                $dataList = array();
//            }

            $allNum = $this->AnalyzeControl->selectClear("select school_id
                from(
                SELECT C.school_id,
                C.school_shortname as school_name,
                C.school_branch,
                A.class_id,
                (SELECT count(distinct x.student_id) FROM smc_student_coursebalance x,smc_course y 
                WHERE x.course_id = y.course_id and x.school_id=C.school_id and y.coursetype_id=b.coursetype_id and x.coursebalance_time > 0 ) AS register_num,
                (SELECT COUNT(1) FROM smc_student_study WHERE class_id=A.class_id and study_beginday<=CURDATE() and study_endday>=CURDATE()) AS reading_num,
                (SELECT COUNT(DISTINCT classroom_id) FROM smc_class_hour WHERE class_id=A.class_id AND hour_ischecking<>-1) AS classroom_num
                FROM smc_class A 
                LEFT JOIN smc_course B ON A.course_id=B.course_id 
                LEFT JOIN smc_school C ON C.school_id=A.school_id 
                WHERE 1
                {$datawhere}
                AND B.coursetype_id='64'
                AND A.class_type=0
                AND A.class_status>=0
                AND A.class_stdate<=CURDATE()
                AND A.class_enddate>=CURDATE()
                )TA
                where 1
                group by TA.school_id");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }

    }

    function teachAndStudyInfoReport($request)
    {
        if (!isset($request['company_id']) || $request['company_id'] == '') {
            $request['company_id'] = '8888';
        }

        $datawhere = " and A.company_id = '{$request['company_id']}' AND A.school_isclose = '0' AND A.school_istest <> '1'";

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and B.coursetype_id ='{$request['coursetype_id']}'";
        }

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and A.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and A.school_branch ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select a.school_id,a.school_shortname as school_name,a.school_branch,
                b.coursetype_id,b.coursetype_branch,b.coursetype_cnname,
                (SELECT count(distinct x.student_id) FROM smc_student_coursebalance x,smc_course y 
                WHERE x.course_id = y.course_id and x.school_id=C.school_id and y.coursetype_id=b.coursetype_id and x.coursebalance_time > 0 ) AS register_num,
                sum(c.study_num) as reading_num,
                count(c.class_id) as class_num,
                ifnull(d.losing_num,'--') as limit_losing_num,
                ifnull(d.reading_num,'--') as limit_reading_num
                from smc_school a 
                INNER JOIN smc_code_coursetype b on b.company_id='{$request['company_id']}' and b.coursetype_isregistercalc=1 
                left join (SELECT x.school_id,z.coursetype_id,x.class_id,count(x.student_id) as study_num
                                FROM smc_student_study x
                                LEFT JOIN smc_class y ON x.class_id=y.class_id 
                                LEFT JOIN smc_course z ON y.course_id=z.course_id 
                                WHERE x.company_id = '{$request['company_id']}'
                                AND y.class_type='0' AND y.class_status>'-2'
                                AND x.study_endday>= CURDATE()
                                AND x.study_beginday<= CURDATE()
                                GROUP BY x.school_id,z.coursetype_id,x.class_id
                ) c on a.school_id=c.school_id and b.coursetype_id=c.coursetype_id 
                left join smc_achieve_target d on a.school_id=d.school_id and b.coursetype_id=d.coursetype_id and d.target_year= year(CURDATE())
                where 1 {$datawhere}
                group by a.school_id,b.coursetype_id
                having (reading_num+register_num)>0
                order by a.school_branch,b.coursetype_id";
//var_dump($sql);
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $datearray['reading_num'] = $dateexcelvar['reading_num'];
                    $datearray['classroom_num'] = $dateexcelvar['classroom_num'];
                    $datearray['class_num'] = $dateexcelvar['class_num'];
                    $datearray['class_avg_num'] = $dateexcelvar['class_avg_num'];
                    $datearray['new_regi_num'] = '--';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '在籍人数', '在读人数', '教室使用数', '在开班级数', '班平均', '本学期新生累计人数'));

            $excelfileds = array('school_name', 'school_branch', 'register_num', 'reading_num', 'classroom_num', 'class_num', 'class_avg_num', 'new_regi_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课辅班运作表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                foreach ($dataList as &$val) {
                    $willNum = $this->AnalyzeControl->selectClear("SELECT A.school_id,C.coursetype_id,A.student_id
                    FROM smc_student_coursebalance A
                    LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id
                    left join smc_code_coursetype as C on C.coursetype_id = B.coursetype_id
                    WHERE A.company_id='{$request['company_id']}' 
                                and a.school_id='{$val['school_id']}' 
                                and C.coursetype_id='{$val['coursetype_id']}' 
                                and C.coursetype_isregistercalc=1 
                    AND A.coursebalance_time>0
                    AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z
                    WHERE X.student_id=A.student_id AND X.company_id=A.company_id
                    AND X.class_id=Y.class_id AND Y.company_id=A.company_id
                    AND Y.course_id=Z.course_id AND Z.company_id=A.company_id
                    AND Z.coursetype_id=B.coursetype_id AND X.study_isreading=1
                    AND X.study_endday>=CURDATE())
                    GROUP BY A.school_id,C.coursetype_id,A.student_id");
                    $val['will_study_num'] = count($willNum);

                    $val['class_avg_num'] = $val['class_num'] > 0 ? round($val['reading_num'] / $val['class_num'], 2) : '--';
                    $val['year_reading_percatage'] = ($val['limit_reading_num'] != '--' && $val['limit_reading_num'] > 0) ? round($val['reading_num'] / $val['limit_reading_num'], 2) : '--';

                    $losingNum = $this->AnalyzeControl->selectOne("select 
                    d.school_id,
                    c.coursetype_id,
                    count(1) as year_losing_num,
                    count(if(a.changelog_day>=date_add(curdate(), interval - day(curdate()) + 1 day),true,null)) as month_losing_num,
                    count(if(a.changelog_day>=date_sub(curdate(),INTERVAL WEEKDAY(curdate()) DAY),true,null)) as week_losing_num
                    from smc_student_changelog a
                    left join smc_student b on b.student_id=a.student_id
                    left join smc_code_coursetype c on c.coursetype_id=a.coursetype_id
                    left join smc_school d on d.school_id=a.school_id
                    WHERE a.company_id='{$request['company_id']}' 
                    and a.school_id='{$val['school_id']}'
                    and a.coursetype_id='{$val['coursetype_id']}' 
                    and a.stuchange_code='C04'
                    AND A.changelog_day>=DATE_SUB(CURDATE(),INTERVAL dayofyear(now())-1 DAY)
                    AND A.changelog_day<=CURDATE()
                    group by d.school_id,c.coursetype_id");
                    if($losingNum){
                        $val['year_losing_num'] = $losingNum['year_losing_num'];
                        $val['month_losing_num'] = $losingNum['month_losing_num'];
                        $val['week_losing_num'] = $losingNum['week_losing_num'];
                    }else{
                        $val['year_losing_num'] = 0;
                        $val['month_losing_num'] = 0;
                        $val['week_losing_num'] = 0;
                    }

                    $keepNum = $this->AnalyzeControl->selectClear("select student_id from
                    (select a.student_id,
                    (select MAX(changelog_day) from smc_student_changelog x where x.school_id=a.school_id and x.student_id=a.student_id and x.stuchange_code='A07') AS changelog_day,
                    ifnull((SELECT MAX(hour_day) FROM smc_student_hourstudy X,smc_class_hour Y ,smc_class Z,smc_course W WHERE X.hour_id=Y.hour_id AND X.class_id=Z.class_id AND Z.course_id=w.course_id
                    and w.coursetype_id=b.coursetype_id and x.student_id=a.student_id and z.school_id=a.school_id),'2000-01-01') as hour_day
                    from smc_student_coursebalance a
                    left join smc_course b on a.course_id=b.course_id
                    where a.company_id='{$request['company_id']}' 
                    and a.school_id='{$val['school_id']}'
                    and b.coursetype_id='{$val['coursetype_id']}' 
                    and a.coursebalance_figure>0
                    and not exists(select 1 from smc_student_study x,smc_class y,smc_course z 
                    where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=a.student_id and x.school_id=a.school_id and z.coursetype_id=b.coursetype_id and x.study_isreading=1)
                    and exists(select 1 from smc_student_changelog x,smc_class y ,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and z.coursetype_id=b.coursetype_id and x.school_id=a.school_id and x.student_id=a.student_id and x.stuchange_code='A07')
                    having changelog_day>=hour_day
                    )ta group by ta.student_id");

                    if($keepNum){
                        $val['keep_num'] = count($keepNum);
                    }else{
                        $val['keep_num'] = 0;
                    }
                }
            }

            $allNum = $this->AnalyzeControl->selectClear("select a.school_id,a.school_shortname as school_name,a.school_branch,
                b.coursetype_id,b.coursetype_branch,b.coursetype_cnname,
                (SELECT count(distinct x.student_id) FROM smc_student_coursebalance x,smc_course y 
                WHERE x.course_id = y.course_id and x.school_id=C.school_id and y.coursetype_id=b.coursetype_id and x.coursebalance_time > 0 ) AS register_num,
                sum(c.study_num) as reading_num,
                count(c.class_id) as class_num,
                d.losing_num as limit_losing_num,
                d.reading_num as limit_reading_num
                from smc_school a 
                INNER JOIN smc_code_coursetype b on b.company_id='{$request['company_id']}' and b.coursetype_isregistercalc=1 
                left join (SELECT x.school_id,z.coursetype_id,x.class_id,count(x.student_id) as study_num
                                    FROM smc_student_study x
                                    LEFT JOIN smc_class y ON x.class_id=y.class_id 
                                    LEFT JOIN smc_course z ON y.course_id=z.course_id 
                                    WHERE x.company_id = '{$request['company_id']}'
                                    AND y.class_type='0' AND y.class_status>'-2'
                                    AND x.study_endday>= CURDATE()
                                    AND x.study_beginday<= CURDATE()
                                    GROUP BY x.school_id,z.coursetype_id,x.class_id
                ) c on a.school_id=c.school_id and b.coursetype_id=c.coursetype_id 
                left join smc_achieve_target d on a.school_id=d.school_id and b.coursetype_id=d.coursetype_id and d.target_year= year(CURDATE())
                where 1 {$datawhere}
                group by a.school_id,b.coursetype_id
                having (reading_num+register_num)>0");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }
    }

    function schoolStudLostInfoReport($request)
    {
        $datawhere = " and d.company_id = '{$request['company_id']}' AND d.school_isclose = '0' AND d.school_istest <> '1'";

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and d.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and d.school_branch ='{$request['school_id']}'";
        }

        if (isset($request['starttime']) && $request['starttime'] !== "") {
        } else {
            $request['starttime'] = date("Y-m-01");
        }
        $datawhere .= " and A.changelog_day>='{$request['starttime']}' ";

        if (isset($request['endtime']) && $request['endtime'] !== "") {
        } else {
            $request['endtime'] = date("Y-m-d");
        }
        $datawhere .= " and A.changelog_day<='{$request['endtime']}' ";

//        if (isset($request['keyword']) && $request['keyword'] !== '') {
//            $datawhere .= " and (client_cnname like '%{$request['keyword']}%'
//            or client_enname like '%{$request['keyword']}%'
//            or parenter_cnname like '%{$request['keyword']}%'
//            or main_principal like '%{$request['keyword']}%'
//            or sub_principal like '%{$request['keyword']}%'
//            or client_mobile like '%{$request['keyword']}%')";
//        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select school_id,school_name,school_branch,
                coursetype_id,coursetype_cnname,coursetype_branch,
                course_branch,count(changelog_id) as lost_num,
                group_concat(concat(ta.student_branch,'-',ta.student_cnname)) as students
                from(
                select a.changelog_id,
                d.school_id,d.school_shortname as school_name,d.school_branch,
                c.coursetype_id,c.coursetype_cnname,c.coursetype_branch,
                b.student_id,b.student_cnname,b.student_enname,b.student_branch,
                a.changelog_day,
                (select z.course_branch from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=a.student_id and x.school_id=a.school_id 
                and y.class_type=0 and y.class_status>-2 and z.coursetype_id=c.coursetype_id and x.study_beginday<=a.changelog_day and x.study_endday<=a.changelog_day -- and x.study_endday>=date_sub(a.changelog_day,interval 1 month) 
                order by x.study_endday desc limit 0,1) as course_branch
                from smc_student_changelog a
                left join smc_student b on b.student_id=a.student_id
                left join smc_code_coursetype c on c.coursetype_id=a.coursetype_id
                left join smc_school d on d.school_id=a.school_id
                WHERE a.stuchange_code='C04'
                {$datawhere}
                )ta 
                group by ta.school_id,ta.coursetype_id,ta.course_branch
                order by ta.school_branch,ta.coursetype_id,ta.course_branch";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['coursetype_cnname'] = $dateexcelvar['coursetype_cnname'];
                    $datearray['coursetype_branch'] = $dateexcelvar['coursetype_branch'];
                    $datearray['course_branch'] = $dateexcelvar['course_branch'];
                    $datearray['lost_num'] = $dateexcelvar['lost_num'];
                    $datearray['students'] = $dateexcelvar['students'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '班组名称', '班组编号', '流失课程别', '流失人数', '流失学生'));

            $excelfileds = array('school_name', 'school_branch', 'coursetype_cnname', 'coursetype_branch', 'course_branch', 'lost_num', 'students');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课辅班运作表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

//            if ($dataList) {
//
//                foreach ($dataList as &$val) {
//                    $val['client_mobile'] = hideNumberString($val['client_mobile']);
//                    $val['audition_genre'] = $audition_genre[$val['audition_genre']];
//                    $val['audition_isvisit'] = $audition_isvisit[$val['audition_isvisit']];
//                }
//            } else {
//                $dataList = array();
//            }

            $allNum = $this->AnalyzeControl->selectClear("select ta.school_id,ta.coursetype_id,ta.course_branch
                from(
                select a.changelog_id,
                d.school_id,d.school_shortname as school_name,d.school_branch,
                c.coursetype_id,c.coursetype_cnname,c.coursetype_branch,
                b.student_id,b.student_cnname,b.student_enname,b.student_branch,
                a.changelog_day,
                (select z.course_branch from smc_student_study x,smc_class y,smc_course z where x.class_id=y.class_id and y.course_id=z.course_id and x.student_id=a.student_id and x.school_id=a.school_id 
                and y.class_type=0 and y.class_status>-2 and z.coursetype_id=c.coursetype_id and x.study_beginday<=a.changelog_day and x.study_endday<=a.changelog_day -- and x.study_endday>=date_sub(a.changelog_day,interval 1 month) 
                order by x.study_endday desc limit 0,1) as course_branch
                from smc_student_changelog a
                left join smc_student b on b.student_id=a.student_id
                left join smc_code_coursetype c on c.coursetype_id=a.coursetype_id
                left join smc_school d on d.school_id=a.school_id
                WHERE a.stuchange_code='C04'
                {$datawhere}
                )ta 
                group by ta.school_id,ta.coursetype_id,ta.course_branch");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }

    }

    function achieveProcessReport($request)
    {
        $datawhere = " and B.company_id = '{$request['company_id']}' AND B.school_isclose = '0' AND B.school_istest <> '1'";

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and B.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and B.school_branch ='{$request['school_id']}'";
        }

        if (isset($request['coursetype_id']) && $request['coursetype_id'] != '') {
            $datawhere .= " and A.coursetype_id ='{$request['coursetype_id']}'";
        } else {
            $datawhere .= " and A.coursetype_id ='65' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select A.school_id,
            B.school_shortname AS school_name,
            B.school_branch,
            (SELECT register_num FROM smc_achieve_target WHERE school_id=A.school_id AND target_year=YEAR(CURDATE()) AND coursetype_id=A.coursetype_id) AS year_target,
            COUNT(IF(DATEDIFF(CURDATE(),FROM_UNIXTIME(A.pay_successtime))<7,TRUE,NULL)) AS regi_num_week,
            COUNT(IF(DATEDIFF(CURDATE(),FROM_UNIXTIME(A.pay_successtime))<30,TRUE,NULL)) AS regi_num_month,
            COUNT(IF(FROM_UNIXTIME(A.pay_successtime)>='2020-11-01' and FROM_UNIXTIME(A.pay_successtime)<='2021-06-30',TRUE,NULL)) AS regi_num_firsthalf,
            COUNT(IF(FROM_UNIXTIME(A.pay_successtime)>='2021-01-01' and FROM_UNIXTIME(A.pay_successtime)<='2021-12-31',TRUE,NULL)) AS regi_num_year,
            COUNT(IF(FROM_UNIXTIME(A.pay_successtime)>='2020-11-01',TRUE,NULL)) AS regi_num_all
            from smc_student_registerinfo A
            LEFT JOIN smc_school B ON A.school_id=B.school_id
            WHERE 1 and A.info_status=1
            {$datawhere}
            GROUP BY A.school_id
            order by b.school_branch";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['year_target'] = ($dateexcelvar['year_target'] && $dateexcelvar['year_target'] > 0) ? $dateexcelvar['year_target'] : '--';
                    $datearray['regi_num_week'] = $dateexcelvar['regi_num_week'];
                    $datearray['regi_num_month'] = $dateexcelvar['regi_num_month'];
                    $datearray['regi_num_firsthalf'] = $dateexcelvar['regi_num_firsthalf'];
                    $datearray['regi_num_year'] = $dateexcelvar['regi_num_year'];
                    $datearray['achieve_process'] = $datearray['year_target'] == '--' ? '--' : round($dateexcelvar['regi_num_all'] / $datearray['year_target'], 2) * 100 . '%';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '年度目标', '周招生人数', '月招生人数', '上半年招生人数', '自然年度招生人数', '招生达成率'));
            $excelfileds = array('school_name', 'school_branch', 'year_target', 'regi_num_week', 'regi_num_month', 'regi_num_firsthalf', 'regi_num_year', 'achieve_process');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课辅班运作表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                foreach ($dataList as &$val) {
                    $val['year_target'] = ($val['year_target'] && $val['year_target'] > 0) ? $val['year_target'] : '--';
                    $val['achieve_process'] = $val['year_target'] == '--' ? '--' : round($val['regi_num_all'] / $val['year_target'], 2) * 100 . '%';
                }
            } else {
                $dataList = array();
            }

            $allNum = $this->AnalyzeControl->selectClear("select A.school_id 
                from smc_student_registerinfo A
                LEFT JOIN smc_school B ON A.school_id=B.school_id
                WHERE 1 and A.info_status=1
                {$datawhere}
                GROUP BY A.school_id
                order by b.school_branch");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }
    }

    function positiveProcessReport($request)
    {
        if (!isset($request['coursetype_id']) || $request['coursetype_id'] == '') {
            $request['coursetype_id'] = '65';
        }

        if (!isset($request['company_id']) || $request['company_id'] == '') {
            $request['company_id'] = '8888';
        }

        $datawhere = " and A.company_id = '{$request['company_id']}' AND c.school_isclose = '0' AND c.school_istest <> '1'";

        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and c.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_branch ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "
            select a.school_id,
            c.school_shortname as school_name,
            c.school_branch,
            (SELECT count(distinct x.student_id) FROM smc_student_coursebalance x,smc_course y 
            WHERE x.course_id = y.course_id and x.school_id=C.school_id and y.coursetype_id=65 and x.coursebalance_time > 0 ) AS register_num,
            e.reading_num,
            count(if(d.info_id is null,true,null))as deposit_only_num,
            count(if(year(FROM_UNIXTIME(a.order_updatatime))=year(CURDATE()),true,null)) as deposit_year_num,
            count(if(year(FROM_UNIXTIME(d.pay_successtime))=year(CURDATE()),true,null)) as register_year_num,
            count(IF(DATEDIFF(CURDATE(),FROM_UNIXTIME(a.order_updatatime))<7,true,null)) as deposit_week_num,
            count(if(DATEDIFF(CURDATE(),FROM_UNIXTIME(d.pay_successtime))<7,true,null)) as register_week_num,
            count(IF(DATEDIFF(CURDATE(),FROM_UNIXTIME(a.order_updatatime))<30,true,null)) as deposit_month_num,
            count(if(DATEDIFF(CURDATE(),FROM_UNIXTIME(d.pay_successtime))<30,true,null)) as register_month_num
            from smc_payfee_order a
            left join smc_payfee_order_course b on a.order_pid=b.order_pid and b.course_id in('73160','73162')
            left join smc_school c on a.school_id=c.school_id
            left join smc_student_registerinfo d on a.school_id=d.school_id and a.student_id=d.student_id and d.info_status=1 and d.coursetype_id='{$request['coursetype_id']}'
            left join (select school_id,count(student_id) as reading_num
            from (SELECT x.school_id,x.student_id,z.coursetype_id,
                    MIN(x.study_beginday) AS beginday,MAX(CASE WHEN x.study_endday='' THEN '2029-12-31'ELSE x.study_endday END) AS endday
                            FROM smc_student_study x 
                            LEFT JOIN smc_class y ON x.class_id=y.class_id 
                            LEFT JOIN smc_course z ON y.course_id=z.course_id 
                            LEFT JOIN smc_code_coursetype w ON z.coursetype_id=w.coursetype_id 
                            WHERE x.company_id='{$request['company_id']}' and z.coursetype_id='{$request['coursetype_id']}'
                            AND y.class_type='0' AND y.class_status>'-2'
                            AND (CASE WHEN x.study_endday='' THEN '2029-12-31'ELSE x.study_endday END) >= DATE_SUB(CURDATE(), INTERVAL w.coursetype_intervaltime DAY) 
                            GROUP BY x.school_id,x.student_id,z.coursetype_id
                            HAVING beginday<=CURDATE() and endday>=CURDATE())ta
                            group by school_id
            ) e on a.school_id=e.school_id
            where 1 {$datawhere}
            and a.order_status=4
            and ((a.coursetype_id='{$request['coursetype_id']}' and a.order_type=2) or b.ordercourse_id is not null)
            group by a.school_id
            order by c.school_branch";

        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['register_num'] = $dateexcelvar['register_num'];
                    $datearray['reading_num'] = $dateexcelvar['reading_num'];
                    $datearray['deposit_only_num'] = $dateexcelvar['deposit_only_num'];

                    $datearray['deposit_year_num'] = $dateexcelvar['deposit_year_num'];
                    $datearray['register_year_num'] = $dateexcelvar['register_year_num'];
                    $datearray['deposit_week_num'] = $dateexcelvar['deposit_week_num'];
                    $datearray['register_week_num'] = $dateexcelvar['register_week_num'];
                    $datearray['deposit_month_num'] = $dateexcelvar['deposit_month_num'];
                    $datearray['register_month_num'] = $dateexcelvar['register_month_num'];
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '在籍人数', '在读人数', '当前定金数', '年度缴定金总数', '年度定转全总数', '本周新增定金数', '本周定转全总数', '本月新增定金数', '本月定转全总数'));
            $excelfileds = array('school_name', 'school_branch', 'register_num', 'reading_num', 'deposit_only_num', 'deposit_year_num', 'register_year_num', 'deposit_week_num', 'register_week_num', 'deposit_month_num', 'register_month_num');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课辅班运作表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

            $allNum = $this->AnalyzeControl->selectClear("
            select a.order_pid 
            from smc_payfee_order a
            left join smc_payfee_order_course b on a.order_pid=b.order_pid and b.course_id in('73160','73162')
            left join smc_school c on a.school_id=c.school_id
            where 1 {$datawhere}
            and a.order_status=4
            and ((a.coursetype_id='{$request['coursetype_id']}' and a.order_type=2) or b.ordercourse_id is not null)
            group by a.school_id
            order by c.school_branch");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }
    }

    function recruitInfoReport($request)
    {
        if (!isset($request['company_id']) || $request['company_id'] == '') {
            $request['company_id'] = '8888';
        }

        $datawhere = " and s.company_id = '{$request['company_id']}' AND s.school_isclose = '0' AND s.school_istest <> '1'";

        $thisWeek = GetWeekAll(date("Y-m-d"));
        if (isset($request['schoolBranch']) && $request['schoolBranch'] != '') {
            $datawhere .= " and s.school_branch in ('" . implode("','", explode(',', $request['schoolBranch'])) . "')";
        } else {
            $this->error = true;
            $this->errortip = "请传入学校";
            return false;
        }
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and s.school_branch ='{$request['school_id']}'";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "select s.school_shortname as school_name,
            s.school_branch,
            s.school_id,
            s.school_tagbak,
            (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id 
            and cs.school_id = s.school_id and DATEDIFF(CURDATE(),FROM_UNIXTIME(t.client_createtime))<7 and h.channel_way =1 and t.client_intention_level>=3) as offline_week_num,
            (select count(t.client_id) from crm_client as t,crm_code_channel as h,crm_client_schoolenter as cs where t.channel_id = h.channel_id and cs.client_id = t.client_id 
            and cs.school_id = s.school_id and DATEDIFF(CURDATE(),FROM_UNIXTIME(t.client_createtime))<7 and h.channel_way =0 and t.client_intention_level>=3) as online_week_num,
            (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 
            and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)<7 and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)>=0) as invite_curweek_num,
            (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 
            and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)<14 and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)>=7) as invite_lastweek_num,
            (select count(a.audition_id) from  crm_client_audition as a where a.school_id=s.school_id and a.audition_genre=0 and audition_isvisit =1 
            and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)<14 and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)>=7) as visit_lastweek_num,
            (select count(a.audition_id) from  crm_client_audition as a,crm_client_positivelog as  pg where a.school_id=s.school_id and a.audition_genre=0 and a.audition_isvisit =1 
            and pg.client_id = a.client_id and a.school_id = pg.school_id and pg.positivelog_time = DATE_FORMAT(a.audition_visittime,'%Y-%m-%d') 
            and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)<14 and DATEDIFF('{$thisWeek['nowweek_end']}',a.audition_visittime)>=7) as positive_lastweek_num
            FROM smc_school as s 
            where 1 {$datawhere}
            order by s.school_branch";
//var_dump($sql);
        if (isset($request['is_export']) && $request['is_export'] == 1) {
            $dateexcelarray = $this->DataControl->selectClear($sql);
            if (!$dateexcelarray) {
                $this->error = true;
                $this->errortip = "无数据";
                return false;
            }
            $outexceldate = array();
            if ($dateexcelarray) {
                $outexceldate = array();
                foreach ($dateexcelarray as &$dateexcelvar) {
                    $datearray = array();
                    $datearray['school_name'] = $dateexcelvar['school_name'];
                    $datearray['school_branch'] = $dateexcelvar['school_branch'];
                    $datearray['offline_week_num'] = $dateexcelvar['offline_week_num'];
                    $datearray['online_week_num'] = $dateexcelvar['online_week_num'];

                    $datearray['invite_curweek_num'] = $dateexcelvar['invite_curweek_num'];
                    $datearray['invite_lastweek_num'] = $dateexcelvar['invite_lastweek_num'];
                    $datearray['visit_lastweek_num'] = $dateexcelvar['visit_lastweek_num'];
                    $datearray['visit_lastweek_percent'] = $dateexcelvar['invite_lastweek_num'] == 0 ? '--' : round($dateexcelvar['visit_lastweek_num'] / $dateexcelvar['invite_lastweek_num'], 2) * 100 . '%';
                    $datearray['positive_lastweek_num'] = $dateexcelvar['positive_lastweek_num'];
                    $datearray['positive_lastweek_percent'] = $dateexcelvar['visit_lastweek_num'] == 0 ? '--' : round($dateexcelvar['positive_lastweek_num'] / $dateexcelvar['positive_lastweek_num'], 2) * 100 . '%';
                    $outexceldate[] = $datearray;
                }
            }
            $excelheader = $this->LgArraySwitch(array('校区名称', '校区编号', '近7天线下新增有效名单', '近7天线上新增有效名单', '当周OH邀约人数', '上周OH邀约人数', '上周OH到访人数', '上周OH到访率', '上周OH报名人数', '上周OH报名率'));
            $excelfileds = array('school_name', 'school_branch', 'offline_week_num', 'online_week_num', 'invite_curweek_num', 'invite_lastweek_num', 'visit_lastweek_num', 'visit_lastweek_percent', 'positive_lastweek_num', 'positive_lastweek_percent');
            query_to_excel($excelheader, $outexceldate, $excelfileds, $this->LgStringSwitch("课辅班运作表.xlsx"));
            exit;
        } else {
            $sql .= " limit {$pagestart},{$num} ";
            $dataList = $this->DataControl->selectClear($sql);

            if ($dataList) {
                foreach ($dataList as &$val) {
                    $val['visit_lastweek_percent'] = $val['invite_lastweek_num'] == 0 ? '--' : round($val['visit_lastweek_num'] / $val['invite_lastweek_num'], 2) * 100 . '%';
                    $val['positive_lastweek_percent'] = $val['visit_lastweek_num'] == 0 ? '--' : round($val['positive_lastweek_num'] / $val['positive_lastweek_num'], 2) * 100 . '%';
                }
            } else {
                $dataList = array();
            }
            $allNum = $this->AnalyzeControl->selectClear("select s.school_id
            FROM smc_school as s 
            where 1 {$datawhere} ");

            if (!$dataList) {
                $dataList = array();
                $allNum['all_num'] = 0;
            }
            $result = array();
            $result['list'] = $dataList;
            $result['allnum'] = count($allNum);
            return $result;
        }
    }

}