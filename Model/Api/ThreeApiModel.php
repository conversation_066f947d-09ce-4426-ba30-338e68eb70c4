<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/3
 * Time: 15:21
 */

namespace Model\Api;

class ThreeApiModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $aeskey = 'jidebaocrm%C0515';
    public $aesiv = 'jdbCuBC7orQtDhTO';
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function ThreeVerify($paramArray)
    {
        //http://api.kcclassin.com/Crm/getSchoolAreaApi?timesteps=1589309974&veytoken=eEGNmE0Io0kTzV3gAehF5gcqicxMupdMb8l+m8gYcLd8w/4JiatqB230w1inQg2fCSdlZ1b6bR4TbissYLZpv6AqrNYKsQE/W/zOJMI1Bd4=
        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }
        /*if($paramArray['timesteps']+60*5 < time() || $paramArray['timesteps']-60 > time()){
            $maxtimes = date("Y-m-d H:i:s",$paramArray['timesteps']+60*5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性,{$timesteps}--{$jmsting}";
            $this->error = true;
            return false;
        }*/

        $aes = new \Aesencdec($this->aeskey, $this->aesiv);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        $paramJson = json_decode($xssting, 1);//转化为数组

        if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
            $this->errortip = '授权时间和连接时间不一致';
            $this->error = true;
            return false;
        }

        $companyOne = $this->DataControl->selectOne("select company_id from gmc_company 
WHERE company_id = '{$paramJson['company_id']}' and company_code = '{$paramJson['company_code']}' limit 0,1");
        if ($companyOne) {
            return $companyOne;
        } else {
            $this->errortip = '你的授权集团编号错误，请确认编号正确';
            $this->error = true;
            return false;
        }
    }

    /**
     * 获取校区公开课信息
     * author: ling
     * 对应接口文档 0001
     */
    function getSchoolClassHour($request)
    {
        $datawhere = "1 and cs.company_id='{$request['company_id']}' and  (cp.coursetype_branch='OH' or cp.coursetype_branch='E' or cp.coursetype_branch='A' )  ";

        if (isset($request['hour_startday']) && $request['hour_startday'] != '') {
            $datawhere .= " and h.hour_day >='{$request['hour_startday']}' ";
        }
        if (isset($request['hour_endday']) && $request['hour_endday'] != '') {
            $datawhere .= " and h.hour_day <='{$request['hour_endday']}'";
        }
        if (isset($request['updata_starttime']) && $request['updata_starttime'] != '') {
            $request['updata_starttime'] = strtotime($request['updata_starttime']);
            $datawhere .= "  and h.hour_ischecking <>1  and if(h.hour_updatatime = 0,h.hour_createtime >='{$request['updata_starttime']}',h.hour_updatatime >='{$request['updata_starttime']}' ) ";
        }
        if (isset($request['updata_endtime']) && $request['updata_endtime'] != '') {
            $request['updata_endtime'] = strtotime($request['updata_endtime']);
            $datawhere .= " and if(h.hour_updatatime = 0,h.hour_createtime <='{$request['updata_endtime']}',h.hour_updatatime <='{$request['updata_endtime']}' ) ";
        }
        if (isset($request['is_openclass']) && $request['is_openclass'] == 1) {
            $datawhere .= " and cp.coursetype_isopenclass =1";
        } elseif (isset($request['is_openclass']) && $request['is_openclass'] == 2) {
            $datawhere .= " and cp.coursetype_isopenclass = 0";
        }
        if (isset($request['school_branch']) && $request['school_branch'] != '') {
            $datawhere .= " and s.school_branch ='{$request['school_branch']}'";
        }
        if(isset($request['hour_startday']) && $request['hour_startday'] != '' && isset($request['hour_endday']) && $request['hour_endday'] != '' && $request['is_openclass'] == '0'){
            $datawhere .= " and h.hour_ischecking <> '-1' and cs.class_status <> '-2' and cs.class_status <> '-1' ";
        }

        $sql = "select s.school_branch,cs.class_branch,cs.class_cnname,h.hour_id,h.hour_name,h.hour_day,h.hour_starttime,h.hour_endtime,h.hour_classtimes,h.hour_way,cp.coursetype_isopenclass,h.hour_ischecking,cs.class_enname,cs.class_status,cp.coursetype_branch,
          (select f.staffer_cnname from smc_class_hour_teaching as ht,smc_staffer as f where  f.staffer_id = ht.staffer_id and ht.hour_id = h.hour_id and ht.class_id = h.class_id and ht.teaching_type =0 limit 0,1) as staffer_cnname
           from smc_class_hour as h
           left join smc_class as cs ON h.class_id = cs.class_id
           left join smc_course as ce ON ce.course_id = cs.course_id
           left join smc_code_coursetype as cp ON cp.coursetype_id = ce.coursetype_id
           left join smc_school as s ON s.school_id = cs.school_id
           where  {$datawhere}  ";
//echo $sql;die;
//        0待考勤1已考勤-1已取消
        $array_checking = array('0' => '待考勤', '1' => '已考勤', '-1' => '已取消');

        $hourList = $this->DataControl->selectClear($sql);
        if ($hourList) {
            foreach ($hourList as $key => $value) {
                $hourList[$key]['is_ohhour'] = $value['coursetype_isopenclass'] == 1 ? '是' : '否';
                if($value['coursetype_branch'] =='A'){
                    $hourList[$key]['class_color'] = '#7d4bf7';
                }else{
                    $hourList[$key]['class_color'] = $value['coursetype_isopenclass'] == 1 ? '#13a0ec' : '#99CCFF';
                }
                $hourList[$key]['hour_way'] = $value['hour_way'] == 1 ? '线上课' : '实体课';
                $hourList[$key]['hour_ischeckingname'] = $array_checking[$value['hour_ischecking']];
                if ($value['class_status'] == -2) {
                    $hourList[$key]['hour_ischecking'] = '-1';
                    $hourList[$key]['hour_ischeckingname'] = '已取消';
                }
                $hourList[$key]['hour_starttime'] = date("H:i", strtotime($value['hour_day'] . '' . $value['hour_starttime']));
                $hourList[$key]['hour_endtime'] = date("H:i", strtotime($value['hour_day'] . '' . $value['hour_endtime']));
            }
        } else {
            $hourList = array();
        }
        $this->error = 0;
        $this->error = '获取成功';
        return $hourList;
    }

    /**
     * 预约学校公开课-试听课预约
     * author: ling
     * 对应接口文档 0001
     * @param $request
     */
    function bookClassHourApi($request)
    {
        return false;
        /*if (!$request['school_branch'] || !$request['hour_id'] || !$request['outuserId']) {
            $this->error = 1;
            $this->errortip = '缺少必填的参数';
            return array();
        }
        if (!$request['outbookId'] || !$request['trackjson']) {
            $this->error = 1;
            $this->errortip = '请传入试听编号或跟进记录';
            return array();
        }
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_branch,school_cnname", "school_branch='{$request['school_branch']}' and  company_id='{$request['company_id']}'");
        if (!$schoolOne) {
            $this->error = 1;
            $this->errortip = "未查询到学校";
            return array();
        }

        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_cnname,client_enname,client_mobile", "outthree_userid='{$request['outuserId']}' and company_id='{$request['company_id']}' ");
        if (!$clientOne) {
            $this->error = 1;
            $this->errortip = "未查询到该客户";
            return array();
        }
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_day,hour_starttime,class_id", "hour_id='{$request['hour_id']}'");
        if (!$hourOne) {
            $this->error = 1;
            $this->errortip = "该课次不存在";
            return array();
        }
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,class_cnname,course_id", "class_id='{$hourOne['class_id']}' ");

        if ($this->DataControl->getFieldOne("crm_client_audition", "audition_id", "outthree_bookid='{$request['outbookId']}'")) {
            $rebackData = $this->updateBookClassHourApi($request);
            if ($rebackData) {
                $this->error = 0;
                $this->errortip = "更新成功";
                return $rebackData;
            } else {
                $this->error = 1;
                $this->errortip = "更新失败";
                return $rebackData;
            }
        } else {
            $courseTypeOne = $this->DataControl->selectOne("select cp.coursetype_isopenclass,cp.coursetype_id
FROM smc_course as co,smc_code_coursetype as cp where co.coursetype_id=cp.coursetype_id and co.course_id='{$classOne['course_id']}'   ");

            //以下为校务系统的跟踪记录
            $track = array();
            $track['client_id'] = $clientOne['client_id'];
            $track['school_id'] = $schoolOne['school_id'];
            $track['track_linktype'] = '第三方试听';
            $track['track_followmode'] = 2;
            $track['track_visitingtime'] = date("Y-m-d H:i:s", strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']));
            if (isset($request['DescriptionForPhone']) && $request['DescriptionForPhone'] !== '') {
                $track['track_note'] = "saleforce预约试听," . $request['DescriptionForPhone'];
            } else {
                $track['track_note'] = "saleforce预约试听,此试听记录由saleforce平台进行试听记录";
            }
            $track['is_update'] = '1';
            $track_id = $this->AddCLienttrack($track);

            $aution_data = array();
            $aution_data['company_id'] = $request['company_id'];
            $aution_data['school_id'] = $schoolOne['school_id'];
            $aution_data['client_id'] = $clientOne['client_id'];
            $aution_data['hour_id'] = $request['hour_id'];
            $aution_data['coursetype_id'] = $courseTypeOne['coursetype_id'];
            $aution_data['class_id'] = $classOne['class_id'];
            $aution_data['course_id'] = $classOne['course_id'];
            $aution_data['class_cnname'] = $classOne['class_cnname'];
            $aution_data['audition_genre'] = 1 - $courseTypeOne['coursetype_isopenclass'];
            $aution_data['audition_visittime'] = date("Y-m-d H:i:s", strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']));
            $aution_data['track_id'] = $track_id;
            $aution_data['audition_createtime'] = time();
            $aution_data['outthree_bookid'] = $request['outbookId'];
            if ($audition_id = $this->DataControl->insertData("crm_client_audition", $aution_data)) {
                if(!$this->DataControl->selectOne("select p.principal_id FROM crm_client_principal AS p
WHERE p.client_id = '{$clientOne['client_id']}' and p.school_id='0'")){
                    $datas = array();
                    $datas['client_id'] = $clientOne['client_id'];
                    $datas['marketer_id'] = '201';
                    $datas['principal_ismajor'] = '1';
                    $datas['principal_leave'] = '0';
                    $datas['principal_createtime'] = time();
                    $datas['principal_updatatime'] = time();
                    $this->DataControl->insertData('crm_client_principal', $datas);
                }


                $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_principal AS p ,smc_staffer as s 
WHERE p.marketer_id = m.marketer_id AND p.principal_leave = '0' AND p.client_id = '{$clientOne['client_id']}' AND p.school_id = '{$schoolOne['school_id']}' and m.staffer_id = s.staffer_id and s.staffer_leave = '0' ORDER BY p.principal_ismajor DESC limit 0,1");
                if ($allotlogList) {
                    $publicarray = array();
                    $publicarray['company_id'] = $request['company_id'];
                    $minsendModel = new \Model\Api\SmsModel($publicarray);
                    foreach ($allotlogList as $allotlogOne) {
                        $mistext = "您好，{$schoolOne['school_cnname']}CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$aution_data['audition_visittime']}公开课，请注意安排招待！";
                        $minsendModel->gmcMisSend($allotlogOne['marketer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                    }
                } else {
                    $allotlogList = $this->DataControl->selectClear("SELECT f.staffer_mobile FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS pt ON pt.post_id = p.post_id LEFT JOIN smc_staffer AS f ON f.staffer_id = p.staffer_id
WHERE p.postbe_status = '1' AND pt.post_istopjob = '1' AND p.school_id = '{$schoolOne['school_id']}' AND p.company_id = '8888' and f.staffer_leave = '0' AND p.postbe_iscrmuser = 1 AND pt.post_name LIKE '%校长%'
GROUP BY f.staffer_mobile");
                    if ($allotlogList) {
                        $publicarray = array();
                        $publicarray['company_id'] = $request['company_id'];
                        $minsendModel = new \Model\Api\SmsModel($publicarray);
                        foreach ($allotlogList as $allotlogOne) {
                            $mistext = "您好，{$schoolOne['school_cnname']}CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$aution_data['audition_visittime']}公开课，请注意安排招待！";
                            $minsendModel->gmcMisSend($allotlogOne['staffer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                        }
                    }
                }
                //微信推送
                $this->wxSaleseClientRemind($clientOne['client_id'], $schoolOne['school_id'], $aution_data['audition_visittime']);

                $clasAuditionData = array();
                $clasAuditionData['company_id'] = $request['company_id'];
                $clasAuditionData['school_id'] = $schoolOne['school_id'];
                $clasAuditionData['hour_id'] = $request['hour_id'];
                $clasAuditionData['class_id'] = $classOne['class_id'];
                $clasAuditionData['client_id'] = $clientOne['client_id'];
                $clasAuditionData['audition_cnname'] = $clientOne['client_cnname'];
                $clasAuditionData['audition_enname'] = $clientOne['client_enname'];
                $clasAuditionData['audition_createtime'] = time();
                $clasAuditionData['outthree_bookid'] = $request['outbookId'];
                $this->DataControl->insertData('smc_class_hour_audition', $clasAuditionData);

                if ($request['trackjson']) {
                    $trackjson = json_decode(stripslashes($request['trackjson']), 1);
                    if (is_array($trackjson) && $trackjson) {
                        foreach ($trackjson as $key => $trackOne) {
                            if ($this->DataControl->getFieldOne("crm_client_outthreetrack", "outthreetrack_id", "three_trackid='{$trackOne['trackId']}' and company_id='{$request['company_id']}'")) {
                                continue;
                            } else {
                                $threetrackData = array();
                                $threetrackData['company_id'] = $request['company_id'];
                                $threetrackData['client_id'] = $clientOne['client_id'];
                                $threetrackData['school_id'] = $schoolOne['school_id'];
                                $threetrackData['threetrack_name'] = $trackOne['threetrack_name'];
                                $threetrackData['threetrack_fileurl'] = str_replace("http://*************/", "https://tmkrecording.kidcastle.cn/", $trackOne['threetrack_fileurl']);
                                $threetrackData['threetrack_note'] = $trackOne['threetrack_note'];
                                $threetrackData['threetrack_createtime'] = strtotime($trackOne['threetrack_createtime']);
                                $threetrackData['three_trackid'] = $trackOne['trackId'];
                                $threetrackData['createtime'] = time();
                                $threetrackData['audition_id'] = $audition_id;
                                $this->DataControl->insertData('crm_client_outthreetrack', $threetrackData);
                            }
                        }
                    }
                }
                $rebackData = array();
                $autionOne = $this->DataControl->selectOne("select 
                    au.client_id,au.audition_id,cs.class_cnname,ch.hour_day,ch.hour_name,ch.hour_starttime,ch.hour_endtime,au.school_id
                    from crm_client_audition as au
                    left join smc_class_hour as ch ON au.hour_id = ch.hour_id 
                    left join smc_class as cs ON cs.class_id = ch.class_id
                where au.audition_id='{$audition_id}'   ");
                $rebackData['audition_id'] = $autionOne['audition_id'];
                $rebackData['client_id'] = $autionOne['client_id'];
                $rebackData['class_cnname'] = $autionOne['class_cnname'];
                $rebackData['hour_name'] = $autionOne['hour_name'];
                $rebackData['hour_day'] = $autionOne['hour_day'];
                $rebackData['hour_starttime'] = $autionOne['hour_starttime'];
                $rebackData['hour_endtime'] = $autionOne['hour_endtime'];
                $this->error = 0;
                $this->error = "预约成功";
                $this->AddClientschool($autionOne['client_id'], $autionOne['school_id'], '8888');


                return $rebackData;
            } else {
                $this->error = 1;
                $this->error = "预约失败";
                return array();
            }
        }*/
    }

    //
    private function updateBookClassHourApi($updateData)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_branch", "school_branch='{$updateData['school_branch']}' and  company_id='{$updateData['company_id']}'");
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_day,class_id", "hour_id='{$updateData['hour_id']}'");
        $courseTypeOne = $this->DataControl->selectOne("select cp.coursetype_isopenclass from smc_course as co,smc_code_coursetype as cp where co.coursetype_id=cp.coursetype_id and co.course_id='{$updateData['course_id']}'   ");
        $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,class_cnname,course_id", "class_id='{$hourOne['class_id']}' ");
        $autdition_data = array();
        $autdition_data['school_id'] = $schoolOne['school_id'];
        $autdition_data['hour_id'] = $hourOne['hour_id'];
        $autdition_data['class_id'] = $classOne['class_id'];
        $autdition_data['course_id'] = $classOne['course_id'];
        $autdition_data['class_cnname'] = $classOne['class_cnname'];
        $autdition_data['audition_genre'] = 1 - $courseTypeOne['coursetype_isopenclass'];
        $autdition_data['audition_visittime'] = date("Y-m-d H:i:s", strtotime($hourOne['hour_day'] . ' ' . $hourOne['hour_starttime']));
        $autdition_data['audition_updatetime'] = time();
        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_cnname", "outthree_userid='{$updateData['outuserId']}' and company_id='{$updateData['company_id']}' ");

        if ($this->DataControl->updateData("crm_client_audition", "outthree_bookid='{$updateData['outbookId']}' and  client_id='{$clientOne['client_id']}'", $autdition_data)) {
            $auditionOne = $this->DataControl->getFieldOne("crm_client_audition", 'audition_id,track_id', "outthree_bookid='{$updateData['outbookId']}' and  client_id='{$clientOne['client_id']}'");
            $trackData = array();
            $trackData['track_visitingtime'] = $autdition_data['audition_visittime'];
            $this->DataControl->updateData("crm_client_track", "track_id='{$auditionOne['track_id']}'", $trackData);
            if ($updateData['trackjson']) {
                $trackjson = json_decode(stripslashes($updateData['trackjson']), 1);

                if (is_array($trackjson) && $trackjson) {
                    foreach ($trackjson as $key => $trackOne) {
                        if ($this->DataControl->getFieldOne("crm_client_outthreetrack", "outthreetrack_id", "three_trackid='{$trackOne['trackId']}' and company_id='{$updateData['company_id']}'")) {
                            continue;
                        } else {
                            $threetrackData = array();
                            $threetrackData['company_id'] = $updateData['company_id'];
                            $threetrackData['client_id'] = $clientOne['client_id'];
                            $threetrackData['school_id'] = $schoolOne['school_id'];
                            $threetrackData['threetrack_name'] = $trackOne['threetrack_name'];
                            $threetrackData['threetrack_fileurl'] = str_replace("http://*************/", "https://tmkrecording.kidcastle.cn/", $trackOne['threetrack_fileurl']);
                            $threetrackData['threetrack_note'] = $trackOne['threetrack_note'];
                            $threetrackData['threetrack_createtime'] = strtotime($trackOne['threetrack_createtime']);
                            $threetrackData['three_trackid'] = $trackOne['trackId'];
                            $threetrackData['audition_id'] = $auditionOne['audition_id'];
                            $threetrackData['createtime'] = time();
                            $this->DataControl->insertData('crm_client_outthreetrack', $threetrackData);
                        }
                    }
                }
            }
            $rebackData = array();
            $rebackData['audition_id'] = $auditionOne['audition_id'];
            $rebackData['client_id'] = $clientOne['client_id'];
            $rebackData['class_cnname'] = $classOne['class_cnname'];
            $rebackData['hour_name'] = $hourOne['hour_name'];
            $rebackData['hour_day'] = $hourOne['hour_day'];
            $rebackData['hour_starttime'] = $hourOne['hour_starttime'];
            $rebackData['hour_endtime'] = $hourOne['hour_endtime'];
            return $rebackData;
        } else {
            return array();
        }

    }

    /**
     *  取消预约课
     */
    function cancelClassHourApi($request)
    {
        if (!$request['hour_id']) {
            $this->error = 1;
            $this->errortip = "请选择需要取消的课时";
            return false;
        }
        if (!$request['audition_id'] && !$request['outbookId']) {
            $this->error = 1;
            $this->errortip = "请输入校务预约的编号或者第三方的预约编号";
            return false;
        }

        $auditionOne = $this->DataControl->selectOne("select audition_id,client_id,hour_id,school_id,audition_visittime from crm_client_audition where hour_id='{$request['hour_id']}' and (audition_id = '{$request['audition_id']}' or outthree_bookid='{$request['outbookId']}' ) ");

        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_cnname,client_mobile", "client_id='{$auditionOne['client_id']}'");
        if (!$auditionOne) {
            $this->error = 1;
            $this->errortip = "未查到预约记录";
            return false;
        }
        $audData = array();
        $audData['audition_isvisit'] = '-1';
        $audData['audition_novisitreason'] = $request['cancelnote'];
        $audData['audition_updatetime'] = time();
        $this->DataControl->updateData("crm_client_audition", "audition_id='{$auditionOne['audition_id']}'", $audData);
        $classAuData = array();
        $classAuData['audition_isvisit'] = '-1';
        $classAuData['audition_novisitreason'] = $request['cancelnote'];
        $this->DataControl->updateData("smc_class_hour_audition", "client_id='{$auditionOne['client_id']}' and  hour_id='{$auditionOne['hour_id']}' and school_id='{$auditionOne['school_id']}' ", $classAuData);

        /**获取负责人**/
        //  $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_allotlog AS a WHERE a.marketer_id = m.marketer_id AND a.allotlog_status = '1' AND a.client_id = '{$auditionOne['client_id']}'  ORDER BY a.allotlog_id DESC limit 0,1");

        $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_principal AS p  
      WHERE p.marketer_id = m.marketer_id AND p.principal_leave = '0' AND p.client_id = '{$auditionOne['client_id']}' AND p.school_id='{$auditionOne['school_id']}' ORDER BY p.principal_ismajor DESC limit 0,1");

        if ($allotlogList) {
            $publicarray = array();
            $publicarray['company_id'] = $request['company_id'];
            $minsendModel = new \Model\Api\SmsModel($publicarray);
            foreach ($allotlogList as $allotlogOne) {
                $mistext = "您好，CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK取消预约{$auditionOne['audition_visittime']}公开课，提醒您注意！";
                $minsendModel->gmcMisSend($allotlogOne['marketer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
            }
        }


        //以下为校务系统的跟踪记录
        $track = array();
        $track['client_id'] = $auditionOne['client_id'];
        $track['school_id'] = $auditionOne['school_id'];
        $track['track_linktype'] = '取消试听';
        $track['track_followmode'] = 0;
        $track['track_note'] = "取消试听,此试听记录由saleforce平台进行取消试听记录";
        $track['is_update'] = '0';
        $this->AddCLienttrack($track);

        $this->error = 0;
        $this->errortip = "取消成功";
        return true;
    }

    /**
     *  预约学校柜询
     */
    function bookSchoolvisitApi($request)
    {
        exit;
       /* if (!$request['school_branch'] || !$request['outuserId']) {
            $this->error = 1;
            $this->errortip = '缺少必填的参数校区编号/第三方客户ID';
            return array();
        }
        if (!$request['outbookId'] || !$request['trackjson']) {
            $this->error = 1;
            $this->errortip = '请传入第三方邀约编号或跟进记录';
            return array();
        }
        if (!$request['Visitday'] || !$request['Visittime']) {
            $this->error = 1;
            $this->errortip = '请传入邀约的日期和时间';
            return array();
        }
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_cnname", "school_branch='{$request['school_branch']}' and  company_id='{$request['company_id']}'");
        if (!$schoolOne) {
            $this->error = 1;
            $this->errortip = "未查询到学校";
            return array();
        }
        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_cnname,client_mobile", "outthree_userid='{$request['outuserId']}' and company_id='{$request['company_id']}' ");
        if (!$clientOne) {
            $this->error = 1;
            $this->errortip = "未查询到该客户";
            return array();
        }
        if ($this->DataControl->getFieldOne("crm_client_invite", "invite_id", "outthree_bookid='{$request['outbookId']}' and client_id='{$clientOne['client_id']}' and  school_id='{$schoolOne['school_id']}'")) {
            if ($rebackData = $this->updateBookSchoolvisitApi($request)) {
                $this->error = 0;
                $this->errortip = "更新成功";
                return $rebackData;
            } else {
                $this->error = 1;
                $this->errortip = "更新失败";
                return $rebackData;
            }
        } else {
            //以下为校务系统的跟踪记录
            $track = array();
            $track['client_id'] = $clientOne['client_id'];
            $track['school_id'] = $schoolOne['school_id'];
            $track['track_linktype'] = '第三方柜询';
            $track['track_followmode'] = 1;
            $track['track_visitingtime'] = date("Y-m-d H:i:s", strtotime($request['Visitday'] . ' ' . $request['Visittime']));
            if (isset($request['DescriptionForPhone']) && $request['DescriptionForPhone'] !== '') {
                $track['track_note'] = "saleforce预约柜询," . $request['DescriptionForPhone'];
            } else {
                $track['track_note'] = "saleforce预约柜询,此试听记录由saleforce平台进行预约柜询记录";
            }
            $track['is_update'] = '1';
            $track_id = $this->AddCLienttrack($track);
            $inviteData = array();
            $inviteData['company_id'] = $request['company_id'];
            $inviteData['school_id'] = $schoolOne['school_id'];
            $inviteData['client_id'] = $clientOne['client_id'];
            $inviteData['invite_genre'] = $request['invite_genre'];
            $inviteData['invite_visittime'] = date("Y-m-d H:i:s", strtotime($request['Visitday'] . ' ' . $request['Visittime']));
            $inviteData['track_id'] = $track_id;
            $inviteData['invite_createtime'] = time();
            $inviteData['outthree_bookid'] = $request['outbookId'];
            if ($invite_id = $this->DataControl->insertData("crm_client_invite", $inviteData)) {
                if(!$this->DataControl->selectOne("select p.principal_id FROM crm_client_principal AS p
WHERE p.client_id = '{$clientOne['client_id']}' and p.school_id='0'")){
                    $datas = array();
                    $datas['client_id'] = $clientOne['client_id'];
                    $datas['marketer_id'] = '201';
                    $datas['principal_ismajor'] = '1';
                    $datas['principal_leave'] = '0';
                    $datas['principal_createtime'] = time();
                    $datas['principal_updatatime'] = time();
                    $this->DataControl->insertData('crm_client_principal', $datas);
                }

                $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_principal AS p ,smc_staffer as s  
WHERE p.marketer_id = m.marketer_id AND p.principal_leave = '0' AND p.client_id = '{$clientOne['client_id']}' AND p.school_id = '{$schoolOne['school_id']}' and m.staffer_id = s.staffer_id and s.staffer_leave = '0' ORDER BY p.principal_ismajor DESC limit 0,1");
                if ($allotlogList) {
                    $publicarray = array();
                    $publicarray['company_id'] = $request['company_id'];
                    $minsendModel = new \Model\Api\SmsModel($publicarray);
                    foreach ($allotlogList as $allotlogOne) {
                        $mistext = "您好，{$schoolOne['school_cnname']},CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$inviteData['invite_visittime']}柜询，请注意安排招待！";
                        $minsendModel->gmcMisSend($allotlogOne['marketer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                    }
                } else {
                    $allotlogList = $this->DataControl->selectClear("SELECT f.staffer_mobile FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS pt ON pt.post_id = p.post_id LEFT JOIN smc_staffer AS f ON f.staffer_id = p.staffer_id
WHERE p.postbe_status = '1' AND pt.post_istopjob = '1' AND p.school_id = '{$schoolOne['school_id']}' AND p.company_id = '8888' and f.staffer_leave = '0' AND p.postbe_iscrmuser = '1' AND pt.post_name LIKE '%校长%'
GROUP BY f.staffer_mobile");
                    if ($allotlogList) {
                        $publicarray = array();
                        $publicarray['company_id'] = $request['company_id'];
                        $minsendModel = new \Model\Api\SmsModel($publicarray);
                        foreach ($allotlogList as $allotlogOne) {
                            $mistext = "您好，{$schoolOne['school_cnname']},CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK预约{$inviteData['invite_visittime']}柜询，请注意安排招待！";
                            $minsendModel->gmcMisSend($allotlogOne['staffer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
                        }
                    }
                }
                //微信推送
                $this->wxSaleseClientRemind($clientOne['client_id'], $schoolOne['school_id'], $inviteData['invite_visittime']);

                if ($request['trackjson']) {
                    $trackjson = json_decode(stripslashes($request['trackjson']), 1);
                    if (is_array($trackjson) && $trackjson) {
                        foreach ($trackjson as $key => $trackOne) {
                            if ($this->DataControl->getFieldOne("crm_client_outthreetrack", "outthreetrack_id", "three_trackid='{$trackOne['trackId']}' and company_id='{$request['company_id']}'")) {
                                continue;
                            } else {
                                $threetrackData = array();
                                $threetrackData['company_id'] = $request['company_id'];
                                $threetrackData['client_id'] = $clientOne['client_id'];
                                $threetrackData['school_id'] = $schoolOne['school_id'];
                                $threetrackData['threetrack_name'] = $trackOne['threetrack_name'];
                                $threetrackData['threetrack_fileurl'] = str_replace("http://*************/", "https://tmkrecording.kidcastle.cn/", $trackOne['threetrack_fileurl']);
                                $threetrackData['threetrack_note'] = $trackOne['threetrack_note'];
                                $threetrackData['threetrack_createtime'] = strtotime($trackOne['threetrack_createtime']);
                                $threetrackData['three_trackid'] = $trackOne['trackId'];
                                $threetrackData['invite_id'] = $invite_id;
                                $threetrackData['createtime'] = time();
                                $this->DataControl->insertData('crm_client_outthreetrack', $threetrackData);
                            }
                        }
                    }
                }
                $rebackData = array();
                $rebackData['invite_id'] = $invite_id;
                $rebackData['client_id'] = $clientOne['client_id'];
                $rebackData['invite_visittime'] = $request['Visitday'];
                $this->error = 0;
                $this->errortip = "预约成功";
                $this->AddClientschool($clientOne['client_id'], $schoolOne['school_id'], '8888');
                return $rebackData;
            } else {
                $this->error = 1;
                $this->errortip = "预约失败";
                return array();
            }
        }*/
    }

    // 更新柜询信息
    private function updateBookSchoolvisitApi($updateData)
    {
        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_branch", "school_branch='{$updateData['school_branch']}' and  company_id='{$updateData['company_id']}'");
        $inviteData = array();
        $inviteData['school_id'] = $schoolOne['school_id'];
        $inviteData['invite_genre'] = $updateData['invite_genre'];
        $inviteData['invite_visittime'] = date("Y-m-d H:i:s", strtotime($updateData['Visitday'] . ' ' . $updateData['Visittime']));
        $inviteData['invite_updatetime'] = time();

        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_cnname", "outthree_userid='{$updateData['outuserId']}' and company_id='{$updateData['company_id']}' ");

        if ($this->DataControl->updateData("crm_client_invite", "outthree_bookid='{$updateData['outbookId']}' and  client_id='{$clientOne['client_id']}'", $inviteData)) {
            $inviteOne = $this->DataControl->getFieldOne("crm_client_invite", 'invite_id,track_id', "outthree_bookid='{$updateData['outbookId']}' and  client_id='{$clientOne['client_id']}'");
            $trackData = array();
            $trackData['track_visitingtime'] = $inviteData['invite_visittime'];
            $this->DataControl->updateData("crm_client_track", "track_id='{$inviteOne['track_id']}'", $trackData);
            if ($updateData['trackjson']) {
                $trackjson = json_decode(stripslashes($updateData['trackjson']), 1);

                if (is_array($trackjson) && $trackjson) {
                    foreach ($trackjson as $key => $trackOne) {
                        if ($this->DataControl->getFieldOne("crm_client_outthreetrack", "outthreetrack_id", "three_trackid='{$trackOne['trackId']}' and company_id='{$updateData['company_id']}'")) {
                            continue;
                        } else {
                            $threetrackData = array();
                            $threetrackData['company_id'] = $updateData['company_id'];
                            $threetrackData['client_id'] = $clientOne['client_id'];
                            $threetrackData['school_id'] = $schoolOne['school_id'];
                            $threetrackData['threetrack_name'] = $trackOne['threetrack_name'];
                            $threetrackData['threetrack_fileurl'] = str_replace("http://*************/", "https://tmkrecording.kidcastle.cn/", $trackOne['threetrack_fileurl']);
                            $threetrackData['threetrack_note'] = $trackOne['threetrack_note'];
                            $threetrackData['threetrack_createtime'] = strtotime($trackOne['threetrack_createtime']);
                            $threetrackData['three_trackid'] = $trackOne['trackId'];
                            $threetrackData['invite_id'] = $inviteOne['invite_id'];
                            $threetrackData['createtime'] = time();
                            $this->DataControl->insertData('crm_client_outthreetrack', $threetrackData);
                        }
                    }
                }
            }
            $rebackData = array();
            $rebackData['invite_id'] = $inviteOne['invite_id'];
            $rebackData['client_id'] = $clientOne['client_id'];
            $rebackData['invite_visittime'] = $inviteData['invite_visittime'];
            return $rebackData;
        } else {
            return array();
        }
    }


    /**
     * 取消预约记录
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return bool
     */
    function cancelSchoolvisitApi($request)
    {
        if (!$request['school_branch'] || !$request['outbookId']) {
            $this->error = 1;
            $this->errortip = "请选择学校/或者第三方的柜询编号";
            return false;
        }

        $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id", "school_branch='{$request['school_branch']}' and  company_id='{$request['company_id']}'");

        if ($schoolOne) {
            $datawhere = "school_id='{$schoolOne['school_id']}' and  outthree_bookid='{$request['outbookId']}'";
        } else {
            if (!$request['invite_id']) {
                $this->error = 1;
                $this->errortip = "请输入校务柜询的编号";
                return false;
            }
            $datawhere = "invite_id='{$request['invite_id']}'";
        }

        $inviteOne = $this->DataControl->selectOne("select invite_id,client_id,school_id,invite_visittime from crm_client_invite where {$datawhere} ");
        if (!$inviteOne) {
            $this->error = 1;
            $this->errortip = "未查询柜询记录";
            return false;
        }

        $data = array();
        $data['invite_isvisit'] = '-1';
        $data['invite_novisitreason'] = $request['invite_novisitreason'];
        $data['invite_updatetime'] = time();
        $this->DataControl->updateData("crm_client_invite", "invite_id='{$inviteOne['invite_id']}'", $data);

        $clientOne = $this->DataControl->getFieldOne("crm_client", "client_id,client_cnname,client_mobile", "client_id='{$inviteOne['client_id']}' and company_id='{$request['company_id']}' ");

        /**获取负责人**/
        //  $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_allotlog AS a WHERE a.marketer_id = m.marketer_id AND a.allotlog_status = '1' AND a.client_id = '{$inviteOne['client_id']}'  ORDER BY a.allotlog_id DESC limit 0,1");

        $allotlogList = $this->DataControl->selectClear("SELECT m.marketer_mobile FROM crm_marketer AS m, crm_client_principal AS p  
      WHERE p.marketer_id = m.marketer_id AND p.principal_leave = '0' AND p.client_id = '{$inviteOne['client_id']}' AND p.school_id='{$inviteOne['school_id']}' ORDER BY p.principal_ismajor DESC limit 0,1");
        if ($allotlogList) {
            $publicarray = array();
            $publicarray['company_id'] = $request['company_id'];
            $minsendModel = new \Model\Api\SmsModel($publicarray);
            foreach ($allotlogList as $allotlogOne) {
                $mistext = "您好，CRM学员{$clientOne['client_cnname']}{$clientOne['client_mobile']}TMK取消预约{$inviteOne['invite_visittime']}柜询，提醒您注意！";
                $minsendModel->gmcMisSend($allotlogOne['marketer_mobile'], $mistext, 'TMK邀约提醒', date("m-d"));
            }
        }

        //以下为校务系统的跟踪记录
        $track = array();
        $track['client_id'] = $inviteOne['client_id'];
        $track['school_id'] = $inviteOne['school_id'];
        $track['track_linktype'] = '取消柜询';
        $track['track_followmode'] = 0;
        $track['track_note'] = "取消柜询,此取消记录由saleforce平台进行取消柜询记录";
        $track['is_update'] = '0';
        $this->AddCLienttrack($track);
        $this->error = 0;
        $this->errortip = "取消柜询成功";
        return true;
    }

    /**
     * 获取集团招生课程
     * author: ling
     * 对应接口文档 0001
     */
    function getComCourseApi($request)
    {
        $dataList = $this->DataControl->selectClear(
            "select co.course_cnname,co.course_branch,ct.coursecat_branch,co.course_classtimes,co.course_img from  smc_course as co ,smc_code_coursecat as ct where co.coursecat_id=ct.coursecat_id and ct.coursecat_iscrmadded =1 and co.company_id='{$request['company_id']}' limit 0,2"
        );
        if (!$dataList) {
            $dataList = array();
        }
        return $dataList;
    }

    /**
     * 更新客户跟踪记录
     * author: ling
     * 对应接口文档 0001
     * @param $request
     * @return array|bool
     */
    function updataClientTrackApi($request)
    {

        $ClientOne = $this->DataControl->getFieldOne("crm_client", "client_id", "outthree_userid='{$request['outuserId']}'");

        if (!$request['client_cnname'] || !$request['client_mobile']) {
            $this->error = 1;
            $this->errortip = '请选择客户中文名或手机号';
            return array();
        }
        if (!$request['outuserId'] || !$request['trackjson']) {
            $this->error = 1;
            $this->errortip = '请传入第三方客户编号或跟进信息';
            return array();
        }

        if (!$ClientOne) {
            if (!$request['medianame'] || !$request['channel_name']) {
                $this->error = 1;
                $this->errortip = '请输入第一渠道和第二渠道';
                return array();
            }

            $clientData = array();
            $clientData['client_cnname'] = $request['client_cnname'];
            $clientData['client_enname'] = $request['client_enname'];
            $clientData['client_mobile'] = $request['client_mobile'];
            if (!$this->DataControl->getFieldOne("crm_code_frommedia", "frommedia_id", "frommedia_name='{$request['medianame']}'")) {
                $this->error = 1;
                $this->errortip = "没有对应的渠道类型";
                return false;
            }
            if (!$channelOne = $this->DataControl->getFieldOne("crm_code_channel", "channel_id,channel_quality", "channel_name='{$request['channel_name']}'")) {
                $this->error = 1;
                $this->errortip = "没有对应的渠道明细";
                return false;
            }
            $clientData['client_source'] = $request['medianame'];
            $clientData['channel_id'] = $channelOne['channel_id'];
            //加渠道判断
            if($channelOne['channel_quality'] == '1'){
                $clientData['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }elseif($channelOne['channel_quality'] == '0' && $channelOne['channel_quality'] != ''){
                $clientData['client_isgross'] = 1;//是毛名单
            }else{
                $clientData['client_isgross'] = 0;//不是毛名单 -- 即 有效
            }

            $clientData['client_address'] = $request['client_address'];
            $clientData['outthree_userid'] = $request['outuserId'];
            if ($client_id = $this->DataControl->insertData("crm_client", $clientData)) {
                if ($clientData['intention_coursecat'] !== '') {
                    $coursecatOne = $this->DataControl->getFieldOne("smc_code_coursecat"
                        , "coursecat_id,coursetype_id", "coursecat_branch='{$request['intention_coursecat']}' AND company_id = '{$request['company_id']}'");
                    if($coursecatOne){
                        $intentData = array();
                        $intentData['client_id'] = $client_id;
                        $intentData['coursetype_id'] = $coursecatOne['coursetype_id'];
                        $intentData['coursecat_id'] = $coursecatOne['coursecat_id'];
                        $intentData['intention_updatetime'] = time();
                        $this->DataControl->insertData("crm_client_intention", $intentData);
                    }
                }
                $trackData = array();
                $trackData['client_id'] = $client_id;
                $trackData['track_type'] = 2;
                $trackData['track_createtime'] = time();
                $trackData['track_note'] = "由第三方进入,saleforce更新";
                $this->DataControl->insertData('crm_client_track', $trackData);

            } else {
                $this->error = 1;
                $this->errortip = "新增客户失败";
                return false;
            }
        } else {
            $client_id = $ClientOne['client_id'];
        }

        if ($request['trackjson']) {
            $trackjson = json_decode(stripslashes($request['trackjson']), 1);
            if (is_array($trackjson) && $trackjson) {
                foreach ($trackjson as $key => $trackOne) {
                    if ($this->DataControl->getFieldOne("crm_client_outthreetrack", "outthreetrack_id", "three_trackid='{$trackOne['trackId']}' and company_id='{$request['company_id']}'")) {
                        continue;
                    } else {
                        $threetrackData = array();
                        $threetrackData['company_id'] = $request['company_id'];
                        $threetrackData['client_id'] = $client_id;
                        $threetrackData['school_id'] = $trackOne['school_id'];
                        $threetrackData['threetrack_name'] = $trackOne['threetrack_name'];
                        $threetrackData['threetrack_fileurl'] = str_replace("http://*************/", "https://tmkrecording.kidcastle.cn/", $trackOne['threetrack_fileurl']);
                        $threetrackData['threetrack_note'] = $trackOne['threetrack_note'];
                        $threetrackData['threetrack_createtime'] = strtotime($trackOne['threetrack_createtime']);
                        $threetrackData['three_trackid'] = $trackOne['trackId'];
                        $threetrackData['createtime'] = time();
                        $this->DataControl->insertData('crm_client_outthreetrack', $threetrackData);
                    }
                }
            }
        }

        $this->error = 0;
        $this->errortip = "更新成功";
        return true;
    }

    /**
     *  增加由saleforce产生的跟进记录
     */
    private function AddCLienttrack($paramArray)
    {
        $clientData = array();
        $cont = '';
        if ($paramArray['is_update'] == 1) {
            $clientOne = $this->DataControl->getFieldOne("crm_client", "client_tracestatus", "client_id='{$paramArray['client_id']}'");
            $clientData = array();
            if ($clientOne['client_tracestatus'] == -2 || $clientOne['client_tracestatus'] == -1) {
                $clientData['client_tracestatus'] = 0;
                $cont = ",并将名单转为有效名单.";
            }
        }

        $data = array();
        $data['client_id'] = $paramArray['client_id'];
        $data['school_id'] = $paramArray['school_id'];
        $data['track_linktype'] = $paramArray['track_linktype'];
        $data['track_followmode'] = $paramArray['track_followmode'];
        $data['track_visitingtime'] = $paramArray['track_visitingtime'];
        $data['track_note'] = $paramArray['track_note'] . $cont;
        $data['track_createtime'] = time();
        $track_id = $this->DataControl->insertData('crm_client_track', $data);

        $clientData['client_isnewtip'] = '1';
        $clientData['client_updatetime'] = time();
        $this->DataControl->updateData("crm_client", "client_id='{$paramArray['client_id']}'", $clientData);
        return $track_id;
    }

    //添加客户学校记录
    private function AddClientschool($client_id, $school_id, $company_id = '8888')
    {
        $schoolenter_id = $this->DataControl->getFieldOne('crm_client_schoolenter', 'schoolenter_id', "client_id='{$client_id}' and school_id='{$school_id}'");
        if ($schoolenter_id) {
            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id='{$school_id}'", $schoolenter);

            $schoolenter = array();
            $schoolenter['is_enterstatus'] = '-1';
            $schoolenter['schoolenter_updatetime'] = time();
            $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}' and school_id <> '{$school_id}'", $schoolenter);
            return false;
        }
        /**解除原学校负责人记录**/
        $principal = array();
        $principal['principal_leave'] = '1';
        $principal['principal_updatatime'] = time();
        if ($this->DataControl->updateData("crm_client_principal", "client_id='{$client_id}' AND principal_leave = '0' and school_id <> '{$school_id}'", $principal)) {
            $client_data = array();
            $client_data['client_distributionstatus'] = '0';
            $client_data['client_tracestatus'] = '0';
            $client_data['client_updatetime'] = time();
            $this->DataControl->updateData("crm_client", "client_id='{$client_id}'", $client_data);
        }

        $schoolenter = array();
        $schoolenter['is_enterstatus'] = '-1';
        $schoolenter['schoolenter_updatetime'] = time();
        $this->DataControl->updateData("crm_client_schoolenter", "client_id='{$client_id}'", $schoolenter);

        $allotlog = array();
        $allotlog['allotlog_status'] = '0';
        $allotlog['allotlog_removetime'] = time();
        $allotlog['allotlog_note'] = 'TMK邀约其他学校，解除非目标校负责人信息';
        $this->DataControl->updateData("crm_client_allotlog", "client_id='{$client_id}' AND allotlog_status = '1' and school_id <> '{$school_id}'", $allotlog);

        $data = array();
        $data['client_id'] = $client_id;
        $data['school_id'] = $school_id;
        $data['company_id'] = $company_id;
        $data['schoolenter_createtime'] = time();
        $data['schoolenter_updatetime'] = time();
        $this->DataControl->insertData('crm_client_schoolenter', $data);
    }

    /**
     * 微信推送->名单更新提醒 saleseforce 预约
     * author: ling
     * 对应接口文档 0001
     */
    function wxSaleseClientRemind($client_id, $school_id, $date)
    {
        $dataList = $this->DataControl->selectClear("
            select f.staffer_id,p.school_id,t.client_cnname,t.client_id
             from crm_client_principal as p 
             left join crm_client as t On t.client_id = p.client_id
            left join crm_marketer as m  On p.marketer_id = m.marketer_id 
            left join smc_staffer as f On f.staffer_id = m.staffer_id 
            where p.client_id = '{$client_id}' and p.school_id='{$school_id}' and f.staffer_wxtoken <>''
            and p.principal_leave = 0 and t.company_id ='8888'
        ");
        $weekarray = array("日", "一", "二", "三", "四", "五", "六");
        if ($dataList) {
            foreach ($dataList as $value) {
                $wxteModel = new \Model\Api\ZjwxChatModel($value['staffer_id'], $value['school_id']);
                $firstnote = "亲爱的招生老师你好，集团招生人员已预约客户{$value['client_cnname']}，请了解最新信息喔~";
                $keyword1 = $value['client_cnname'];
                $keyword2 = $date . ' 周' . $weekarray[date('w', strtotime($date))];
                $footernote = '请了解最新信息，点击查看详情查看客户信息';
                $url = "http://tesc.kedingdang.com/crmIndex/IntendedCustomerDetails?client_id={$value['client_id']}";
                $wxteModel->RemindFollow($firstnote, $keyword1, $keyword2, $footernote, $url);
            }
        } else {
            $dataList = $this->DataControl->selectClear("SELECT f.staffer_id,p.school_id FROM gmc_staffer_postbe AS p LEFT JOIN gmc_company_post AS pt ON pt.post_id = p.post_id LEFT JOIN smc_staffer AS f ON f.staffer_id = p.staffer_id
WHERE p.postbe_status = '1' AND pt.post_istopjob = '1' AND p.school_id = '{$school_id}' AND p.company_id = '8888' AND p.postbe_iscrmuser = '1' AND pt.post_name LIKE '%校长%'
AND  f.staffer_wxtoken <>'' ");
            $clientOne = $this->DataControl->getFieldOne("crm_client", "client_cnname", "client_id='{$client_id}'");
            if ($dataList) {
                foreach ($dataList as $value) {
                    $wxteModel = new \Model\Api\ZjwxChatModel($value['staffer_id'], $value['school_id']);
                    $firstnote = "亲爱的招生老师你好，集团招生人员已预约客户{$value['client_cnname']}，请了解最新信息喔~";
                    $keyword1 = $clientOne['client_cnname'];
                    $keyword2 = $date . ' 周' . $weekarray[date('w', strtotime($date))];
                    $footernote = '请了解最新信息，点击查看详情查看客户信息';
                    $url = "https://tesc.kedingdang.com/crmIndex/IntendedCustomerDetails?client_id={$client_id}";
                    $wxteModel->RemindFollow($firstnote, $keyword1, $keyword2, $footernote, $url);
                }
            }
        }
    }
}