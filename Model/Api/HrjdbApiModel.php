<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/3
 * Time: 15:21
 */

namespace Model\Api;

class HrjdbApiModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $result = array();

    function __construct()
    {
        parent::__construct();
        $this->JdbBsControl = new \Dbsqlplay("rm-bp126753724k3331mwo.mysql.rds.aliyuncs.com", "kdd", "kdd20240826!", "eduappv2");
    }

    function updateBsOrgaize()
    {
        $sql = "select `code`as organize_pid,
            (select code from t_beisen_bumen where oId=a.pOIdOrgAdmin and shanchu=0 and stdIsDeleted=0 and status=1) as father_pid,
            `name` as organize_name,
            `shortName` as short_name,
            `changmingcheng` as long_name,
            `oId` as old_id,
            `pOIdOrgAdmin` as old_father_id,
            `pOIdOrgAdmin_TreeLevel` as old_level
            from t_beisen_bumen a
            where 1
            and shanchu=0
            and stdIsDeleted=0
            and status=1
            -- and (code like'SR%' or code like 'MBQ01%')
            and pOIdOrgAdmin_TreePath like '*********/1640567/1636044/%'
            and pOIdOrgAdmin_TreeLevel in(4,5,6)
            and changmingcheng like '吉的堡教育集团_少儿事业群%'
            order by old_level,organize_pid";
        $orgList = $this->JdbBsControl->selectClear($sql);
        if ($orgList) {
            foreach ($orgList as $orgOne) {
                if ($orgOne['old_level'] == '4') {
                    $org_old = $this->DataControl->getFieldOne("gmc_company_organize", "organize_id", "organize_pid = '{$orgOne['organize_pid']}' AND organizeclass_id = '480'");
                    if (!$org_old) {
                        $organizeOne = array();
                        $organizeOne['company_id'] = '8888';
                        $organizeOne['organizeclass_id'] = '480';
                        $organizeOne['father_id'] = '0';
                        $organizeOne['organize_pid'] = $orgOne['organize_pid'];
                        $organizeOne['organize_class'] = '1';
                        $organizeOne['organize_cnname'] = $orgOne['organize_name'];
                        $organizeOne['organize_enname'] = $orgOne['long_name'];
                        $organizeOne['organize_updatetime'] = time();
                        $organizeOne['organize_createtime'] = time();
                        $this->DataControl->insertData("gmc_company_organize", $organizeOne);
                        echo "4级组织{$orgOne['long_name']}-添加成功\n";
                    } else {
                        $organizeOne = array();
                        $organizeOne['father_id'] = '0';
                        $organizeOne['organize_pid'] = $orgOne['organize_pid'];
                        $organizeOne['organize_cnname'] = $orgOne['organize_name'];
                        $organizeOne['organize_enname'] = $orgOne['long_name'];
                        $organizeOne['organize_updatetime'] = time();
                        $this->DataControl->updateData("gmc_company_organize", "organize_id = '{$org_old['organize_id']}' AND organizeclass_id = '480'", $organizeOne);
                        echo "4级组织{$orgOne['long_name']}-已存在\n";
                    }
                } else {
                    $fatherOne = $this->DataControl->getFieldOne("gmc_company_organize", "organize_id", "organize_pid = '{$orgOne['father_pid']}' AND organizeclass_id = '480'");
                    $org_old = $this->DataControl->getFieldOne("gmc_company_organize", "organize_id", "organize_pid = '{$orgOne['organize_pid']}' AND organizeclass_id = '480'");
                    if (!$org_old) {
                        if ($fatherOne) {
                            $organizeOne = array();
                            $organizeOne['company_id'] = '8888';
                            $organizeOne['organizeclass_id'] = '480';
                            $organizeOne['father_id'] = $fatherOne['organize_id'];
                            $organizeOne['organize_pid'] = $orgOne['organize_pid'];
                            $organizeOne['organize_class'] = '1';
                            $organizeOne['organize_cnname'] = $orgOne['organize_name'];
                            $organizeOne['organize_enname'] = $orgOne['long_name'];
                            $organizeOne['organize_updatetime'] = time();
                            $organizeOne['organize_createtime'] = time();
                            $this->DataControl->insertData("gmc_company_organize", $organizeOne);
                            echo "5级组织{$orgOne['long_name']}-添加成功\n";
                        }
                    } else {
                        $organizeOne = array();
                        $organizeOne['father_id'] = $fatherOne['organize_id'];
                        $organizeOne['organize_pid'] = $orgOne['organize_pid'];
                        $organizeOne['organize_cnname'] = $orgOne['organize_name'];
                        $organizeOne['organize_enname'] = $orgOne['long_name'];
                        $organizeOne['organize_updatetime'] = time();
                        $this->DataControl->updateData("gmc_company_organize", "organize_id = '{$org_old['organize_id']}' AND organizeclass_id = '480'", $organizeOne);
                        echo "5级组织{$orgOne['long_name']}-已存在\n";
                    }
                }
            }
        }
    }

    function updateBsOrgaizeSchool()
    {
        $sql = "select `code`as organize_pid,
            (select code from t_beisen_bumen where oId=a.pOIdOrgAdmin and shanchu=0 and stdIsDeleted=0 and status=1 ) as father_pid,
            `name` as organize_name,
            `shortName` as short_name,
            `changmingcheng` as long_name,
            `extyewuxitongbianma` as school_branch,
            `oId` as old_id,
            `pOIdOrgAdmin` as old_father_id,
            `pOIdOrgAdmin_TreeLevel` as old_level
            from t_beisen_bumen a
            where 1
            and shanchu=0
            and stdIsDeleted=0
            and status=1
            -- and (code like'SR%' or code like 'MBQ01%')
            and pOIdOrgAdmin_TreePath like '*********/1640567/1636044/%'
            and pOIdOrgAdmin_TreeLevel = 7
            and changmingcheng like '吉的堡教育集团_少儿事业群%'
            order by old_level,organize_pid";
        $schoolList = $this->JdbBsControl->selectClear($sql);
        if ($schoolList) {
            foreach ($schoolList as $schOne) {
                $schoolOne = $this->DataControl->getFieldOne("smc_school", "school_id,school_branch,school_cnname", "school_branch = '{$schOne['school_branch']}' AND company_id = '8888'");
                if ($schoolOne) {
                    $dataOne = array();
                    $dataOne['school_erpshopcode'] = $schOne['organize_pid'];
                    $dataOne['school_updatatime'] = time();
                    $this->DataControl->updateData("smc_school", "school_id = '{$schoolOne['school_id']}'", $dataOne);

                    $organizeOne = $this->DataControl->selectOne("SELECT
                            o.organize_id AS one_id,
                            o.organize_cnname AS one_cnname,
                            t.organize_id AS two_id,
                            t.organize_cnname AS two_cnname,
                            s.organize_id as ori_id,
                            s.organize_cnname as ori_cnname
                        FROM
                            gmc_company_organize AS t,
                            gmc_company_organize AS o,
                            gmc_company_organize s
                        WHERE t.father_id = o.organize_id and o.father_id=s.organize_id
                        AND o.organizeclass_id = '480'
                        AND t.father_id <> '0'
                        AND t.organize_pid = '{$schOne['father_pid']}'");
                    if ($organizeOne) {
                        //一级组织
                        if (!$this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$organizeOne['one_id']}' AND school_id = '{$schoolOne['school_id']}'")) {
                            $orOne = array();
                            $orOne['organize_id'] = $organizeOne['one_id'];
                            $orOne['school_id'] = $schoolOne['school_id'];
                            $this->DataControl->insertData("gmc_company_organizeschool", $orOne);
                        }
                        //二级组织
                        if (!$this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$organizeOne['two_id']}' AND school_id = '{$schoolOne['school_id']}'")) {
                            $orOne = array();
                            $orOne['organize_id'] = $organizeOne['two_id'];
                            $orOne['school_id'] = $schoolOne['school_id'];
                            $this->DataControl->insertData("gmc_company_organizeschool", $orOne);
                        }
                        //顶级组织
                        if (!$this->DataControl->getFieldOne("gmc_company_organizeschool", "organize_id", "organize_id = '{$organizeOne['ori_id']}' AND school_id = '{$schoolOne['school_id']}'")) {
                            $orOne = array();
                            $orOne['organize_id'] = $organizeOne['ori_id'];
                            $orOne['school_id'] = $schoolOne['school_id'];
                            $this->DataControl->insertData("gmc_company_organizeschool", $orOne);
                        }
                        echo "北森组织{$schOne['organize_cnname']}所属{$schoolOne['school_cnname']}-添加成功\n";
                    }
                } else {
                    echo "北森组织{$schOne['organize_cnname']}所属{$schOne['school_branch']}-不存在校区\n";
                }
            }
        }
    }

    function getBsWorkerBranch()
    {
        $sql = "select a.jobNumber
        from t_beisen_yuangong_renzhi a
        left join t_beisen_bumen b on a.oIdDepartment=b.oId
        where b.changmingcheng LIKE '吉的堡教育集团_少儿事业群_%'
        and LENGTH(a.jobNumber)>=10
        and a.jobNumber<>'YUAN-00001'
        and a.stopDate>=now()
        and a.stdIsDeleted=0
        and a.employType in(0,2)
        and a.employeestatus in(2,3,4,5)
        and ((a.isCurrentRecord=1 and a.serviceType=0) or a.serviceType=1)
        and b.status=1
        and b.shanchu=0
        and b.stdIsDeleted=0
        group by a.jobNumber
        order by a.businessModifiedTime desc
        limit 0,500";
        $workerList = $this->JdbBsControl->selectClear($sql);
        if ($workerList) {
            foreach ($workerList as $workerOne) {
                $orOne = array();
                $orOne['company_id'] = '8888';
                $orOne['workerhr_branch'] = $workerOne['jobNumber'];
                $orOne['workerhr_createtime'] = time();
                if (!$this->DataControl->insertData("smc_staffer_workerhr", $orOne)) {
                    $orOne = array();
                    $orOne['workerhr_error'] = 0;
                    $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch='{$workerOne['jobNumber']}'", $orOne);
                }
            }
        }
    }

    function getBsPostCode()
    {
        $sql = "select a.name,a.code
            from t_beisen_zhiwu a
            where a.stdIsDeleted='0'
            and a.status=1
            and a.code not like 'TW-%'
            and exists(select 1 from t_beisen_yuangong_renzhi x,t_beisen_bumen y 
            where x.oIdJobPost=a.oId 
            -- and x.isCurrentRecord=1 
            and x.oIdDepartment=y.oId 
            and y.changmingcheng LIKE '吉的堡教育集团_少儿事业群_%' )
            order by id";
        $postList = $this->JdbBsControl->selectClear($sql);
        if ($postList > 0) {
            foreach ($postList as $postOne) {
                $postDo = $this->DataControl->getFieldOne("gmc_company_post", "post_id", " company_id='8888' and post_name = '{$postOne['name']}'");
                if ($postDo) {
                    $dataOne = array();
                    $dataOne['post_code_bs'] = $postOne['code'];
                    $dataOne['post_updatetime'] = time();
                    $this->DataControl->updateData("gmc_company_post", "post_id = '{$postDo['post_id']}'", $dataOne);
                } else {
                    $dataOne = array();
                    $dataOne['company_id'] = '8888';
                    $dataOne['post_code'] = $postOne['code'];
                    $dataOne['post_code_bs'] = $postOne['code'];
                    $dataOne['post_name'] = $postOne['name'];
                    $dataOne['post_createtime'] = time();
                    $dataOne['post_updatetime'] = time();
                    $this->DataControl->insertData("gmc_company_post", $dataOne);
                }
            }
        }
    }

    //获取Hr的个人信息
    function getWorkerNew($hrOne)
    {
        $sql = "select b.jobNumber as worker_branch
            ,a.name as worker_cnname
            ,a.Name_en_US as worker_enname
            ,a.gender+1 as worker_sex
            ,a.mobilePhone as worker_mobile
            ,CONVERT(a.birthday, DATE) AS worker_birthday
            ,a.iDNumber as worker_code
            ,min(b.serviceStatus) as worker_statename
            ,a.stdIsDeleted+a.shanchu as worker_statecode
            ,DATE_FORMAT(a.latestEntryDate,'%Y-%m-%d') as staffer_jointime
            from t_beisen_yuangong a
            left join t_beisen_yuangong_renzhi b on a.userID=b.userID and b.isCurrentRecord=1 and b.stdIsDeleted=0 
            where b.jobNumber='{$hrOne['workerhr_branch']}' 
            group by b.jobNumber
            limit 0,1";
        $workerOne = $this->JdbBsControl->selectOne($sql);

        if ($workerOne) {
            $stafferDo = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_employeepid = '{$hrOne['workerhr_branch']}' AND company_id = '8888'");
            if ($stafferDo) {
                $apilog = array();
                $apilog['company_id'] = '8888';
                $apilog['staffer_mobile'] = $workerOne['worker_mobile'];
                $apilog['staffer_employeepid'] = $workerOne['worker_branch'];
                $apilog['hrlog_errornote'] = '检测职工编号已存在，编号错误需要手工处理！';
                $apilog['hrlog_time'] = time();
                if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                    $dataOne = array();
                    $dataOne['workerhr_error'] = '1';
                    $dataOne['workerhr_updatetime'] = time();
                    $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$hrOne['workerhr_branch']}'", $dataOne);
                }
                exit;
            } else {
                $stafferData = array();
                $stafferData['company_id'] = '8888';
                $like = date("Ymd", time());
                $staffInfo = $this->DataControl->selectOne("select staffer_branch from smc_staffer where staffer_branch like '{$like}%' AND LENGTH(staffer_branch) = '14' order by staffer_branch DESC limit 0,1");
                if ($staffInfo) {
                    $stafferData['staffer_branch'] = $staffInfo['staffer_branch'] + 1;
                } else {
                    $stafferData['staffer_branch'] = $like . '000001';
                }
                $stafferData['staffer_cnname'] = $workerOne['worker_cnname'];
                $stafferData['staffer_enname'] = $workerOne['worker_enname'];
                $stafferData['staffer_employeepid'] = $workerOne['worker_branch'];
                $stafferData['staffer_sex'] = $workerOne['worker_sex'] == '1' ? "男" : "女";
                $stafferData['staffer_mobile'] = $workerOne['worker_mobile'];
                $stafferData['staffer_jointime'] = $workerOne['staffer_jointime'];
                $stafferData['staffer_bakpass'] = substr($workerOne['worker_mobile'], -6);
                $stafferData['staffer_pass'] = md5(substr($workerOne['worker_mobile'], -6));
                $stafferData['staffer_updatetime'] = time();
                $stafferData['staffer_createtime'] = time();
                if (!$this->DataControl->insertData("smc_staffer", $stafferData)) {
                    $apilog = array();
                    $apilog['company_id'] = '8888';
                    $apilog['staffer_mobile'] = $workerOne['worker_mobile'];
                    $apilog['staffer_employeepid'] = $workerOne['worker_branch'];
                    $apilog['hrlog_errornote'] = '检测生成员工信息SQL执行错误，需要手工处理！' . $this->DataControl->errorsql;
                    $apilog['hrlog_time'] = time();
                    if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                        $dataOne = array();
                        $dataOne['workerhr_error'] = '1';
                        $dataOne['workerhr_updatetime'] = time();
                        $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$hrOne['workerhr_branch']}'", $dataOne);
                    }
                    exit;
                }
            }
            echo "{$workerOne['worker_cnname']},{$workerOne['worker_branch']}更新{$workerOne['worker_updatatype']}完毕";
        } else {
            $apilog = array();
            $apilog['company_id'] = '8888';
            $apilog['staffer_employeepid'] = $hrOne['workerhr_branch'];
            $apilog['hrlog_errornote'] = 'HR接口未检测到此员工信息，无法进行正常更新！1';
            $apilog['hrlog_time'] = time();
            $this->DataControl->insertData("smc_api_hrlog", $apilog);
            $dataOne = array();
            $dataOne['workerhr_error'] = '1';
            $dataOne['workerhr_updatetime'] = time();
            $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$hrOne['workerhr_branch']}'", $dataOne);
            echo "{$hrOne['workerhr_branch']}暂无API数据";
            exit;
        }
    }

    //获取Hr的个人信息
    function getWorkerInfoFromBs($request)
    {
        $stafferOne = $this->DataControl->getOne("smc_staffer", "staffer_employeepid = '{$request['staffer_employeepid']}'");
        $sql = "select b.jobNumber as worker_branch
            ,a.name as worker_cnname
            ,a.Name_en_US as worker_enname
            ,a.gender+1 as worker_sex
            ,a.mobilePhone as worker_mobile
            ,CONVERT(a.birthday, DATE) AS worker_birthday
            ,a.iDNumber as worker_code
            ,min(b.serviceStatus)  as worker_statename
            ,a.stdIsDeleted+a.shanchu as worker_statecode
            ,min(b.employType) as worker_employType
            ,DATE_FORMAT(a.latestEntryDate,'%Y-%m-%d') as staffer_jointime
            from t_beisen_yuangong a
            left join t_beisen_yuangong_renzhi b on a.userID=b.userID and b.isCurrentRecord=1 and b.stdIsDeleted=0 
            where b.jobNumber='{$request['staffer_employeepid']}' 
            group by b.jobNumber
            limit 0,1";
        $workerOne = $this->JdbBsControl->selectOne($sql);

        if ($workerOne) {
            if ($stafferOne && $workerOne['worker_cnname'] !== $stafferOne['staffer_cnname']) {
                $apilog = array();
                $apilog['company_id'] = '8888';
                $apilog['staffer_mobile'] = $stafferOne['staffer_mobile'];
                $apilog['staffer_employeepid'] = $stafferOne['staffer_employeepid'];
                $apilog['hrlog_errornote'] = '检测员工姓名信息不一致，无法进行正常更新！';
                $apilog['hrlog_time'] = time();
                if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                    $dataOne = array();
                    $dataOne['workerhr_error'] = '1';
                    $dataOne['workerhr_updatetime'] = time();
                    $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$request['staffer_employeepid']}'", $dataOne);
                }
                return "{$stafferOne['staffer_cnname']},{$stafferOne['staffer_mobile']}检测员工姓名信息不一致";
            } else {
                $workerDo = $this->DataControl->getFieldOne("smc_staffer_worker", "worker_id,worker_updatatype", "worker_branch = '{$request['staffer_employeepid']}'");
                if (!$workerDo) {
                    $workerData = array();
                    $workerData['company_id'] = '8888';
                    $workerData['staffer_id'] = $stafferOne['staffer_id'];
                    $workerData['worker_branch'] = $request['staffer_employeepid'];
                    $workerData['worker_updatatype'] = '1';
                    $workerData['worker_updatetime'] = time();
                    $workerData['worker_createtime'] = time();
                    $this->DataControl->insertData("smc_staffer_worker", $workerData);
                    $workerDo = $this->DataControl->getFieldOne("smc_staffer_worker", "worker_id,worker_updatatype", "worker_branch = '{$request['staffer_employeepid']}'");
                }

                $workerData = array();
                $workerData['staffer_id'] = $stafferOne['staffer_id'];
                $workerData['worker_branch'] = $workerOne['worker_branch'];
                $workerData['worker_cnname'] = addslashes($workerOne['worker_cnname']);
                $workerData['worker_enname'] = addslashes($workerOne['worker_enname']);
                $workerData['worker_sex'] = $workerOne['worker_sex'];
                $workerData['worker_mobile'] = $workerOne['worker_mobile'];
                $workerData['worker_birthday'] = $workerOne['worker_birthday'];
                $workerData['worker_code'] = $workerOne['worker_code'];
                $workerData['worker_statename'] = $workerOne['worker_statename'];
                $workerData['worker_statecode'] = $workerOne['worker_statecode'];
                if (isset($workerOne['worker_statename']) && ($workerOne['worker_statename'] > 0)) {
                    $workerData['worker_leave'] = "1";
                } else {
                    $workerData['worker_leave'] = "0";
                }
                $workerData['worker_updatatype'] = $workerDo['worker_updatatype'] + 1;
                $workerData['worker_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer_worker", "worker_id = '{$workerDo['worker_id']}'", $workerData);

                //更新职工信息
                $stafferData = array();
                $stafferData['staffer_cnname'] = addslashes($workerOne['worker_cnname']);
                $stafferData['staffer_enname'] = addslashes($workerOne['worker_enname']);
                $stafferData['staffer_sex'] = $workerOne['worker_sex'] == '1' ? "男" : "女";
                $stafferData['staffer_jointime'] = $workerOne['staffer_jointime'];
                if (isset($workerOne['worker_employType']) && $workerOne['worker_employType'] == '1') {
                    $stafferData['staffer_isparttime'] = "1";
                } else {
                    $stafferData['staffer_isparttime'] = "0";
                }
                $stafferData['staffer_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer", "staffer_employeepid = '{$workerOne['worker_branch']}'", $stafferData);

                $stafferOne = $this->updataWork($workerOne['worker_branch']);

                //判断职工是否离职
                if (isset($workerOne['worker_statename']) && ($workerOne['worker_statename'] > 0)) {
                    $this->hrLeave($stafferOne);
                } else {
                    //进行职位信息更新
                    $sql = "select b.`name` as group_name
                    ,b.`pOIdOrgAdmin_TreeLevel` as old_level
                    ,b.`code` as organize_pid
                    ,(select code from t_beisen_bumen where oId=b.pOIdOrgAdmin and shanchu=0 and stdIsDeleted=0 and status=1) as father_organize_pid
                    ,c.`code` as post_code
                    ,c.`name` as post_name
                    ,a.startDate as begindate
                    ,a.stopDate as enddate
                    ,1-a.serviceType as is_main_job
                    ,1-a.serviceStatus as job_status
                    ,a.isCurrentRecord as is_current
                    from t_beisen_yuangong_renzhi a 
                    left join t_beisen_bumen b on a.oIdDepartment=b.oId
                    left join t_beisen_zhiwu c on a.oIdJobPost=c.oId
                    where b.changmingcheng LIKE '吉的堡教育集团_少儿事业群_%'
                    and b.status=1
                    and b.shanchu=0
                    and b.stdIsDeleted=0
                    and c.id is not null
                    and a.jobNumber='{$workerOne['worker_branch']}' 
                    and a.stdIsDeleted=0
                    and a.oIdJobPost is not null
                    -- and a.stopDate>=now()
                    and a.stdIsDeleted=0
                    and a.employType in(0,2)
                    and a.employeestatus in(2,3,4,5)
                    and ((a.isCurrentRecord=1 and a.serviceType=0) or a.serviceType=1)
                    order by a.stopDate";

                    $jobList = $this->JdbBsControl->selectClear($sql);
                    if ($jobList) {
                        foreach ($jobList as $jobOne) {
                            $this->bsPostUpdates($stafferOne, $jobOne);
                        }
                    }
                }
            }
            return "{$workerOne['worker_cnname']}/{$workerOne['worker_enname']},{$stafferOne['staffer_employeepid']}更新完毕" . $this->errortip;
        } else {
            $apilog = array();
            $apilog['company_id'] = '8888';
            $apilog['staffer_mobile'] = $stafferOne['staffer_mobile'];
            $apilog['staffer_employeepid'] = $stafferOne['staffer_employeepid'];
            $apilog['hrlog_errornote'] = 'HR接口未检测到此员工信息，无法进行正常更新！2';
            $apilog['hrlog_time'] = time();
            if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                $dataOne = array();
                $dataOne['updatatype'] = '1';
                $dataOne['staffer_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", $dataOne);
                $dataOne = array();
                $dataOne['workerhr_error'] = 1;
                $dataOne['workerhr_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$request['staffer_employeepid']}'", $dataOne);
            }
            return "{$stafferOne['staffer_cnname']},{$stafferOne['staffer_mobile']}暂无API数据";
        }
    }

    //职工信息更新
    function updataWork($worker_branch)
    {
        $workerOne = $this->DataControl->getOne("smc_staffer_worker", "worker_branch = '{$worker_branch}'");
        if ($this->DataControl->getFieldOne("smc_staffer", "staffer_id"
            , "company_id = '8888' AND staffer_mobile = '{$workerOne['worker_mobile']}' AND staffer_mobile <> '' 
            AND staffer_employeepid <> '{$workerOne['worker_branch']}' AND staffer_employeepid <> '' ")) {
            $apilog = array();
            $apilog['company_id'] = '8888';
            $apilog['staffer_mobile'] = $workerOne['worker_mobile'];
            $apilog['staffer_employeepid'] = $workerOne['worker_branch'];
            $apilog['hrlog_errornote'] = '检测员工手机号码已存在，编号错误需要手工处理！';
            $apilog['hrlog_time'] = time();
            if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                $dataOne = array();
                $dataOne['workerhr_error'] = '1';
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
            }
            return false;
        } else if ($this->DataControl->getFieldOne("smc_staffer", "staffer_id"
            , "staffer_mobile = '{$workerOne['worker_mobile']}' AND staffer_employeepid=''
             AND staffer_cnname='{$workerOne['worker_cnname']}' AND company_id = '8888'")) {
            $stafferDo = $this->DataControl->getFieldOne("smc_staffer", "staffer_id"
                , "staffer_mobile = '{$workerOne['worker_mobile']}' AND staffer_employeepid=''
             AND staffer_cnname='{$workerOne['worker_cnname']}' AND company_id = '8888'");
            $stafferData = array();
            $stafferData['staffer_cnname'] = addslashes($workerOne['worker_cnname']);
            $stafferData['staffer_enname'] = addslashes($workerOne['worker_enname']);
            $stafferData['staffer_employeepid'] = $workerOne['worker_branch'];
            $stafferData['staffer_sex'] = $workerOne['worker_sex'] == '1' ? "男" : "女";
            if (isset($workerOne['worker_statename']) && ($workerOne['worker_statename'] > 0)) {
                $stafferData['staffer_leave'] = '1';
                $stafferData['staffer_leavetime'] = date("Y-m-d");
            } else {
                $stafferData['staffer_leave'] = '0';
                $stafferData['staffer_leavetime'] = '';
            }
            $stafferData['staffer_updatetime'] = time();
            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferDo['staffer_id']}'", $stafferData)) {
                $dataOne = array();
                $dataOne['workerhr_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
                $dataOne = array();
                $dataOne['marketer_status'] = 1 - $stafferData['staffer_leave'] * 2;
                $dataOne['marketer_updatetime'] = time();
                $this->DataControl->updateData("crm_marketer", "staffer_id = '{$stafferDo['staffer_id']}'", $dataOne);
                return $stafferDo;
            } else {
                return false;
            }
        } else if ($this->DataControl->getFieldOne("smc_staffer", "staffer_id"
            , "staffer_mobile = '{$workerOne['worker_mobile']}' AND staffer_employeepid=''
             AND staffer_cnname<>'{$workerOne['worker_cnname']}' AND company_id = '8888'")) {
            $apilog = array();
            $apilog['company_id'] = '8888';
            $apilog['staffer_mobile'] = $workerOne['worker_mobile'];
            $apilog['staffer_employeepid'] = $workerOne['worker_branch'];
            $apilog['hrlog_errornote'] = '检测员工姓名不匹配，错误需要手工处理！';
            $apilog['hrlog_time'] = time();
            if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                $dataOne = array();
                $dataOne['workerhr_error'] = '1';
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
            }
            return false;
        } else if ($this->DataControl->getFieldOne("smc_staffer", "staffer_id"
            , "staffer_employeepid='{$workerOne['worker_branch']}' 
             AND staffer_cnname='{$workerOne['worker_cnname']}' AND company_id = '8888'")) {
            $stafferDo = $this->DataControl->getFieldOne("smc_staffer", "staffer_id"
                , "staffer_employeepid='{$workerOne['worker_branch']}' 
             AND staffer_cnname='{$workerOne['worker_cnname']}' AND company_id = '8888'");
            $stafferData = array();
            $stafferData['staffer_cnname'] = addslashes($workerOne['worker_cnname']);
            $stafferData['staffer_enname'] = addslashes($workerOne['worker_enname']);
            $stafferData['staffer_employeepid'] = $workerOne['worker_branch'];
            $stafferData['staffer_sex'] = $workerOne['worker_sex'] == '1' ? "男" : "女";
            $stafferData['staffer_mobile'] = $workerOne['worker_mobile'];
            if (isset($workerOne['worker_statename']) && ($workerOne['worker_statename'] > 0)) {
                $stafferData['staffer_leave'] = '1';
                $stafferData['staffer_leavetime'] = date("Y-m-d");
            } else {
                $stafferData['staffer_leave'] = '0';
                $stafferData['staffer_leavetime'] = '';
            }
            $stafferData['staffer_updatetime'] = time();
            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferDo['staffer_id']}'", $stafferData)) {
                $dataOne = array();
                $dataOne['workerhr_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
                $dataOne = array();
                $dataOne['marketer_status'] = 1 - $stafferData['staffer_leave'] * 2;
                $dataOne['marketer_updatetime'] = time();
                $this->DataControl->updateData("crm_marketer", "staffer_id = '{$stafferDo['staffer_id']}'", $dataOne);
                return $stafferDo;
            } else {
                return false;
            }
        }

        $stafferDo = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_employeepid = '{$workerOne['worker_branch']}'");
        if (!$stafferDo) {
            $stafferData = array();
            $stafferData['company_id'] = '8888';
            $like = date("Ymd", time());
            $stuInfo = $this->DataControl->selectOne("select s.staffer_branch from smc_staffer as s where s.staffer_branch like '{$like}%' AND LENGTH(staffer_branch) = '14' order by s.staffer_branch DESC limit 0,1");
            if ($stuInfo) {
                $stafferData['staffer_branch'] = number_format($stuInfo['staffer_branch'] + 1, 0, '', '');
            } else {
                $stafferData['staffer_branch'] = $like . '000001';
            }
            $stafferData['staffer_cnname'] = addslashes($workerOne['worker_cnname']);
            $stafferData['staffer_enname'] = addslashes($workerOne['worker_enname']);
            $stafferData['staffer_employeepid'] = $workerOne['worker_branch'];
            $stafferData['staffer_sex'] = $workerOne['worker_sex'] == '1' ? "男" : "女";
            $stafferData['staffer_mobile'] = $workerOne['worker_mobile'];
            $stafferData['staffer_bakpass'] = substr($workerOne['worker_mobile'], -6);
            $stafferData['staffer_pass'] = md5(substr($workerOne['worker_mobile'], -6));
            $stafferData['staffer_updatetime'] = time();
            $stafferData['staffer_createtime'] = time();

            if (!$this->DataControl->insertData("smc_staffer", $stafferData)) {
                $apilog = array();
                $apilog['company_id'] = '8888';
                $apilog['staffer_mobile'] = $workerOne['worker_mobile'];
                $apilog['staffer_employeepid'] = $workerOne['worker_branch'];
                $apilog['hrlog_errornote'] = '检测SQL执行错误需要手工处理！' . $this->DataControl->errorsql;
                $apilog['hrlog_time'] = time();
                if ($this->DataControl->insertData("smc_api_hrlog", $apilog)) {
                    $dataOne = array();
                    $dataOne['workerhr_error'] = '1';
                    $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
                }
                return false;
            } else {
                $stafferDo = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "staffer_employeepid = '{$workerOne['worker_branch']}'");
                $dataOne = array();
                $dataOne['workerhr_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
                return $stafferDo;
            }
        } else {
            $stafferData = array();
            $stafferData['staffer_cnname'] = addslashes($workerOne['worker_cnname']);
            $stafferData['staffer_enname'] = addslashes($workerOne['worker_enname']);
            if (isset($workerOne['worker_statename']) && ($workerOne['worker_statename'] > 0)) {
                $stafferData['staffer_leave'] = '1';
                $stafferData['staffer_leavetime'] = date("Y-m-d");
            } else {
                $stafferData['staffer_leave'] = '0';
                $stafferData['staffer_leavetime'] = '';
            }
            $stafferData['staffer_updatetime'] = time();
            if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferDo['staffer_id']}'", $stafferData)) {
                $dataOne = array();
                $dataOne['workerhr_updatetime'] = time();
                $this->DataControl->updateData("smc_staffer_workerhr", "workerhr_branch = '{$workerOne['worker_branch']}'", $dataOne);
                $dataOne = array();
                $dataOne['marketer_status'] = 1 - $stafferData['staffer_leave'] * 2;
                $dataOne['marketer_updatetime'] = time();
                $this->DataControl->updateData("crm_marketer", "staffer_id = '{$stafferDo['staffer_id']}'", $dataOne);
                return $stafferDo;
            } else {
                return false;
            }
        }
    }

    //职工职位更新
    function bsPostUpdates($stafferOne, $jobOne)
    {
        $postOne = $this->DataControl->getOne("gmc_company_post", "company_id = '8888' AND post_code_bs = '{$jobOne['post_code']}' ");
        if (!$postOne) {
            $this->errortip = "职务代码{$jobOne['post_code']}信息不存在\n";
            $this->error = true;
            return false;
        }

        $schoolList = array();
        //如果是学校职务
        if ($postOne['post_type'] == '1') {
            $post_type = 1;
            //先看是不是学校组织
            $schoolOne = $this->DataControl->getOne("smc_school", "company_id = '8888' AND (school_erpshopcode = '{$jobOne['organize_pid']}' or school_erpshopcode = '{$jobOne['father_organize_pid']}')");

            if ($schoolOne) {
                $organizeOne = $this->DataControl->selectOne("SELECT a.* FROM gmc_company_organize a,gmc_company_organizeschool b
            WHERE a.organize_id = b.organize_id AND a.organizeclass_id = '480' and b.school_id='{$schoolOne['school_id']}' order by a.organize_id desc limit 0,1");
            } else {
                $organizeOne = $this->DataControl->getOne("gmc_company_organize", "company_id = '8888' AND organize_pid = '{$jobOne['organize_pid']}' order by organize_id desc ");
                if (!$organizeOne) {
                    $organizeOne = $this->DataControl->getOne("gmc_company_organize", "company_id = '8888' AND organize_pid = '{$jobOne['father_organize_pid']}' order by organize_id desc ");
                    if ($organizeOne) {
                        $schoolList = $this->DataControl->getList("gmc_company_organizeschool", "organize_id = '{$organizeOne['organize_id']}'");
                    }
                } else {
                    $schoolList = $this->DataControl->getList("gmc_company_organizeschool", "organize_id = '{$organizeOne['organize_id']}'");
                    if ($schoolList && count($schoolList) > 0) {
                    } else {
                        $organizeOne = $this->DataControl->getOne("gmc_company_organize", "company_id = '8888' AND organize_pid = '{$jobOne['father_organize_pid']}' order by organize_id desc ");
                        if ($organizeOne) {
                            $schoolList = $this->DataControl->getList("gmc_company_organizeschool", "organize_id = '{$organizeOne['organize_id']}'");
                        }
                    }
                }
            }
        } else {
            $post_type = 0;
            $organizeOne = $this->DataControl->getOne("gmc_company_organize", "company_id = '8888' AND organize_pid = '{$jobOne['organize_pid']}' order by organize_id desc ");

            if (!$organizeOne) {
                $organizeOne = $this->DataControl->getOne("gmc_company_organize", "company_id = '8888' AND organize_pid = '{$jobOne['father_organize_pid']}' order by organize_id desc ");
            } else {
                $schoolList = $this->DataControl->getList("gmc_company_organizeschool", "organize_id = '{$organizeOne['organize_id']}'");
                if ($schoolList && count($schoolList) > 0) {
                } else {
                    $organizeOne = $this->DataControl->getOne("gmc_company_organize", "company_id = '8888' AND organize_pid = '{$jobOne['father_organize_pid']}' order by organize_id desc ");
                }
            }
        }

        if (!$organizeOne) {
            $this->errortip = "组织代码{$jobOne['organize_pid']}信息不存在\n";
            $this->error = true;
            return false;
        }

        $postpartOne = 0;
        $postroleOne = 0;

        if ($post_type == 1) {//单校职务
            $postpartOne = $this->DataControl->getOne("smc_school_postpart", "postpart_id = '{$postOne['postpart_id']}'");
            if (count($schoolList) > 0) {
                foreach ($schoolList as $school) {
                    $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id = '{$school['school_id']}'");
                    if (!$postbeOne) {
                        $postbe = array();
                        $postbe['company_id'] = '8888';
                        $postbe['postbe_fromsystem'] = '1';
                        $postbe['school_id'] = $school['school_id'];
                        $postbe['organize_id'] = $organizeOne['organize_id'];
                        $postbe['postlevel_id'] = $postOne['postlevel_id'];
                        $postbe['post_id'] = $postOne['post_id'];
                        $postbe['staffer_id'] = $stafferOne['staffer_id'];
                        $postbe['postpart_id'] = $postOne['postpart_id'];
                        $postbe['postrole_id'] = $postOne['postrole_id'];
                        $postbe['postbe_ismianjob'] = $jobOne['is_main_job'];
                        $postbe['postbe_status'] = $jobOne['job_status'];
                        if ($postpartOne['postpart_iscrmuser'] == '1' || $postroleOne['postpart_iscrmuser'] == '1') {
                            $postbe['postbe_iscrmuser'] = 1;
                            $postbe['postbe_isreceptionuser'] = 1;
                            if (strstr($postOne['post_name'], "校长")) {
                                $postbe['postbe_crmuserlevel'] = 1;
                            }
                        }
                        if ($jobOne['job_status'] == '0') {
                            $postbe['postbe_losetime'] = strtotime($jobOne['enddate']);
                        }
                        $postbe['postbe_createtime'] = time();
                        if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                            $this->oktip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}添加成功\n";
                        } else {
                            $this->errortip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}添加失败\n";
                            $this->error = true;
                        }
                    } else {
                        $postbe = array();
                        $postbe['postbe_fromsystem'] = '1';
                        $postbe['school_id'] = $school['school_id'];
                        $postbe['organize_id'] = $organizeOne['organize_id'];
                        $postbe['postlevel_id'] = $postOne['postlevel_id'];
                        $postbe['post_id'] = $postOne['post_id'];
                        $postbe['staffer_id'] = $stafferOne['staffer_id'];
                        $postbe['postpart_id'] = $postOne['postpart_id'];
                        $postbe['postrole_id'] = $postOne['postrole_id'];
                        $postbe['postbe_ismianjob'] = $jobOne['is_main_job'];
                        $postbe['postbe_status'] = $jobOne['job_status'];
                        if ($postpartOne['postpart_iscrmuser'] == '1' || $postroleOne['postpart_iscrmuser'] == '1') {
                            $postbe['postbe_iscrmuser'] = 1;
                            $postbe['postbe_isreceptionuser'] = 1;
                            if (strstr($postOne['post_name'], "校长")) {
                                $postbe['postbe_crmuserlevel'] = 1;
                            }
                        }
                        if ($jobOne['job_status'] == '0') {
                            $postbe['postbe_losetime'] = strtotime($jobOne['enddate']);
                        }
                        if ($this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' AND postbe_id = '{$postbeOne['postbe_id']}'", $postbe)) {
                            $this->oktip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}更新成功\n";
                        } else {
                            $this->errortip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}更新失败\n";
                            $this->error = true;
                        }
                    }
                }
            } else {
                $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id = '{$schoolOne['school_id']}'");
                if (!$postbeOne) {
                    $postbe = array();
                    $postbe['company_id'] = '8888';
                    $postbe['postbe_fromsystem'] = '1';
                    $postbe['school_id'] = $schoolOne['school_id'];
                    $postbe['organize_id'] = $organizeOne['organize_id'];
                    $postbe['postlevel_id'] = $postOne['postlevel_id'];
                    $postbe['post_id'] = $postOne['post_id'];
                    $postbe['staffer_id'] = $stafferOne['staffer_id'];
                    $postbe['postpart_id'] = $postOne['postpart_id'];
                    $postbe['postrole_id'] = $postOne['postrole_id'];
                    $postbe['postbe_ismianjob'] = $jobOne['is_main_job'];
                    $postbe['postbe_status'] = $jobOne['job_status'];
                    if ($postpartOne['postpart_iscrmuser'] == '1' || $postroleOne['postpart_iscrmuser'] == '1') {
                        $postbe['postbe_iscrmuser'] = 1;
                        $postbe['postbe_isreceptionuser'] = 1;
                        if (strstr($postOne['post_name'], "校长")) {
                            $postbe['postbe_crmuserlevel'] = 1;
                        }
                    }
                    if ($jobOne['job_status'] == '0') {
                        $postbe['postbe_losetime'] = strtotime($jobOne['enddate']);
                    }
                    $postbe['postbe_createtime'] = time();
                    if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                        $this->oktip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}添加成功\n";
                    } else {
                        $this->errortip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}添加失败\n";
                        $this->error = true;
                    }
                } else {
                    $postbe = array();
                    $postbe['postbe_fromsystem'] = '1';
                    $postbe['school_id'] = $schoolOne['school_id'];
                    $postbe['organize_id'] = $organizeOne['organize_id'];
                    $postbe['postlevel_id'] = $postOne['postlevel_id'];
                    $postbe['post_id'] = $postOne['post_id'];
                    $postbe['staffer_id'] = $stafferOne['staffer_id'];
                    $postbe['postpart_id'] = $postOne['postpart_id'];
                    $postbe['postrole_id'] = $postOne['postrole_id'];
                    $postbe['postbe_ismianjob'] = $jobOne['is_main_job'];
                    $postbe['postbe_status'] = $jobOne['job_status'];
                    if ($postpartOne['postpart_iscrmuser'] == '1' || $postroleOne['postpart_iscrmuser'] == '1') {
                        $postbe['postbe_iscrmuser'] = 1;
                        $postbe['postbe_isreceptionuser'] = 1;
                        if (strstr($postOne['post_name'], "校长")) {
                            $postbe['postbe_crmuserlevel'] = 1;
                        }
                    }
                    if ($jobOne['job_status'] == '0') {
                        $postbe['postbe_losetime'] = strtotime($jobOne['enddate']);
                    }
                    if ($this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' AND postbe_id = '{$postbeOne['postbe_id']}'", $postbe)) {
                        $this->oktip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}更新成功\n";
                    } else {
                        $this->errortip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}更新失败\n";
                        $this->error = true;
                    }
                }
            }
        } else {//集团职务
            $postroleOne = $this->DataControl->getOne("gmc_company_postrole", "postrole_id = '{$postOne['postrole_id']}'");
            $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id=0 AND organize_id = '{$organizeOne['organize_id']}'");
            if (!$postbeOne) {
                $postbe = array();
                $postbe['company_id'] = '8888';
                $postbe['postbe_fromsystem'] = '1';
                $postbe['school_id'] = 0;
                $postbe['organize_id'] = $organizeOne['organize_id'];
                $postbe['postlevel_id'] = $postOne['postlevel_id'];
                $postbe['post_id'] = $postOne['post_id'];
                $postbe['staffer_id'] = $stafferOne['staffer_id'];
                $postbe['postpart_id'] = $postOne['postpart_id'];
                $postbe['postrole_id'] = $postOne['postrole_id'];
                $postbe['postbe_ismianjob'] = $jobOne['is_main_job'];
                $postbe['postbe_status'] = $jobOne['job_status'];
                if ($postpartOne['postpart_iscrmuser'] == '1' || $postroleOne['postpart_iscrmuser'] == '1') {
                    $postbe['postbe_iscrmuser'] = 1;
                    $postbe['postbe_isreceptionuser'] = 1;
                    if (strstr($postOne['post_name'], "校长")) {
                        $postbe['postbe_crmuserlevel'] = 1;
                    }
                }
                if ($jobOne['job_status'] == '0') {
                    $postbe['postbe_losetime'] = strtotime($jobOne['enddate']);
                }
                $postbe['postbe_createtime'] = time();
                if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                    $this->oktip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}添加成功\n";
                } else {
                    $this->errortip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}添加失败\n";
                    $this->error = true;
                }
            } else {
                $postbe = array();
                $postbe['postbe_fromsystem'] = '1';
                $postbe['school_id'] = 0;
                $postbe['organize_id'] = $organizeOne['organize_id'];
                $postbe['postlevel_id'] = $postOne['postlevel_id'];
                $postbe['post_id'] = $postOne['post_id'];
                $postbe['staffer_id'] = $stafferOne['staffer_id'];
                $postbe['postpart_id'] = $postOne['postpart_id'];
                $postbe['postrole_id'] = $postOne['postrole_id'];
                $postbe['postbe_ismianjob'] = $jobOne['is_main_job'];
                $postbe['postbe_status'] = $jobOne['job_status'];
                if ($postpartOne['postpart_iscrmuser'] == '1' || $postroleOne['postpart_iscrmuser'] == '1') {
                    $postbe['postbe_iscrmuser'] = 1;
                    $postbe['postbe_isreceptionuser'] = 1;
                    if (strstr($postOne['post_name'], "校长")) {
                        $postbe['postbe_crmuserlevel'] = 1;
                    }
                }
                if ($jobOne['job_status'] == '0') {
                    $postbe['postbe_losetime'] = strtotime($jobOne['enddate']);
                }
                if ($this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' AND postbe_id = '{$postbeOne['postbe_id']}'", $postbe)) {
                    $this->oktip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}更新成功\n";
                } else {
                    $this->errortip .= "{$stafferOne['staffer_employeepid']}职务{$jobOne['post_name']}更新失败\n";
                    $this->error = true;
                }
            }
        }
        return true;
    }

    //职工职位更新
    function hrpostUpdates($stafferOne, $jobOne)
    {
        if ($jobOne['FLevel'] == '3') {
            //集团一级职务
            $postOne = $this->DataControl->getOne("gmc_company_post", "company_id = '8888' AND post_code = '{$jobOne['JOB_Number']}' AND post_type = '0'");
            $organizeOne = $this->DataControl->getOne("gmc_company_organize", "organize_pid = '{$jobOne['FID']}' AND organizeclass_id = '480' AND father_id = '0'");
            if ($postOne && $organizeOne) {
                $postroleOne = $this->DataControl->getOne("gmc_company_postrole", "postrole_id = '{$postOne['postrole_id']}'");
                //集团组织获取
                if (!$this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND organize_id = '{$organizeOne['organize_id']}'")) {
                    $postbe = array();
                    $postbe['company_id'] = '8888';
                    $postbe['postbe_fromsystem'] = '1';
                    $postbe['organize_id'] = $organizeOne['organize_id'];
                    $postbe['postlevel_id'] = $postOne['postlevel_id'];
                    $postbe['staffer_id'] = $stafferOne['staffer_id'];
                    $postbe['post_id'] = $postOne['post_id'];
                    $postbe['postrole_id'] = $postOne['postrole_id'];
                    $postbe['postpart_id'] = $postroleOne['postpart_id'];
                    if ($jobOne['ISMAINJOB'] == '1') {
                        $postbe['postbe_ismianjob'] = '1';
                    } else {
                        $postbe['postbe_ismianjob'] = '0';
                    }
                    $postbe['postbe_status'] = '1';
                    $postbe['postbe_iscrmuser'] = $postroleOne['postpart_iscrmuser'];
                    $postbe['postbe_createtime'] = time();
                    if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                        $this->oktip = "集团一级职务添加成功<br />";
                    } else {
                        $this->errortip = "集团一级添加失败<br />";
                        $this->error = true;
                    }
                } else {
                    $this->errortip = "集团一级已增加<br />";
                    $this->error = true;
                }
            } else {
                $this->errortip = "集团一级不存在<br />";
                $this->error = true;
            }
        } elseif ($jobOne['FLevel'] == '4') {
            //集团二级职务
            $postOne = $this->DataControl->getOne("gmc_company_post", "company_id = '8888' AND post_code = '{$jobOne['JOB_Number']}' AND post_type = '0'");
            $organizeOne = $this->DataControl->getOne("gmc_company_organize", "organize_pid = '{$jobOne['FID']}' AND organizeclass_id = '480' AND father_id <> '0'");
            if ($postOne && $organizeOne) {
                $postroleOne = $this->DataControl->getOne("gmc_company_postrole", "postrole_id = '{$postOne['postrole_id']}'");
                //集团组织获取
                if (!$this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND organize_id = '{$organizeOne['organize_id']}'")) {
                    $postbe = array();
                    $postbe['company_id'] = '8888';
                    $postbe['postbe_fromsystem'] = '1';
                    $postbe['organize_id'] = $organizeOne['organize_id'];
                    $postbe['postlevel_id'] = $postOne['postlevel_id'];
                    $postbe['staffer_id'] = $stafferOne['staffer_id'];
                    $postbe['post_id'] = $postOne['post_id'];
                    $postbe['postrole_id'] = $postOne['postrole_id'];
                    $postbe['postpart_id'] = $postroleOne['postpart_id'];
                    if ($jobOne['ISMAINJOB'] == '1') {
                        $postbe['postbe_ismianjob'] = '1';
                    } else {
                        $postbe['postbe_ismianjob'] = '0';
                    }
                    $postbe['postbe_status'] = '1';
                    $postbe['postbe_iscrmuser'] = $postroleOne['postpart_iscrmuser'];
                    $postbe['postbe_createtime'] = time();
                    if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                        $this->oktip = "集团二级职务添加成功<br />";
                    } else {
                        $this->errortip = "集团二级职务添加失败<br />";
                        $this->error = true;
                    }
                } else {
                    $this->errortip = "集团二级职务已增加<br />";
                    $this->error = true;
                }
            } else {
                $this->errortip = "集团二级职务不存在<br />";
                $this->error = true;
            }
        } elseif ($jobOne['FLevel'] == '5') {
            //校园二级职务
            $postOne = $this->DataControl->getOne("gmc_company_post", "company_id = '8888' AND post_code = '{$jobOne['JOB_Number']}' AND post_type = '1'");
            if ($postOne) {
                $schoolOne = $this->DataControl->getOne("smc_school", "company_id = '8888' AND binary school_erpshopcode = '{$jobOne['FID']}'");
                $organizeOne = $this->DataControl->selectOne("SELECT o.organize_id FROM gmc_company_organize AS o,gmc_company_organizeschool AS e,smc_school AS s
WHERE o.organize_id = e.organize_id AND e.school_id = s.school_id AND o.organizeclass_id = '480' AND o.father_id = '0'
AND s.school_erpshopcode = '{$jobOne['FID']}' limit 0,1");
                if ($organizeOne) {
                    $postpartOne = $this->DataControl->getOne("smc_school_postpart", "postpart_id = '{$postOne['postpart_id']}'");
                    //集团组织获取
                    $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id = '{$schoolOne['school_id']}'");
                    if (!$postbeOne) {
                        $postbe = array();
                        $postbe['company_id'] = '8888';
                        $postbe['school_id'] = $schoolOne['school_id'];
                        $postbe['postbe_fromsystem'] = '1';
                        $postbe['organize_id'] = $organizeOne['organize_id'];
                        $postbe['postlevel_id'] = $postOne['postlevel_id'];
                        $postbe['post_id'] = $postOne['post_id'];
                        $postbe['staffer_id'] = $stafferOne['staffer_id'];
                        $postbe['postpart_id'] = $postOne['postpart_id'];
                        if ($jobOne['ISMAINJOB'] == '1') {
                            $postbe['postbe_ismianjob'] = '1';
                        } else {
                            $postbe['postbe_ismianjob'] = '0';
                        }
                        $postbe['postbe_status'] = '1';
                        if ($postpartOne['postpart_iscrmuser'] == '1') {
                            $postbe['postbe_iscrmuser'] = 1;
                            if (strstr($postOne['post_name'], "校长")) {
                                $postbe['postbe_crmuserlevel'] = 1;
                            }
                            $postbe['postbe_isreceptionuser'] = 1;
                        }
                        $postbe['postbe_createtime'] = time();
                        if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                            $this->oktip = "校区一级职务添加成功<br />";
                        } else {
                            $this->errortip = "校区一级职务添加失败<br />";
                            $this->error = true;
                        }
                    } else {
                        if ($postbeOne['post_id'] !== $postOne['post_id']) {
                            $postbe = array();
                            $postbe['postbe_fromsystem'] = '1';
                            $postbe['organize_id'] = $organizeOne['organize_id'];
                            $postbe['postlevel_id'] = $postOne['postlevel_id'];
                            $postbe['post_id'] = $postOne['post_id'];
                            $postbe['postpart_id'] = $postOne['postpart_id'];
                            if ($jobOne['ISMAINJOB'] == '1') {
                                $postbe['postbe_ismianjob'] = '1';
                            } else {
                                $postbe['postbe_ismianjob'] = '0';
                            }
                            $postbe['postbe_status'] = '1';
                            if ($postpartOne['postpart_iscrmuser'] == '1') {
                                $postbe['postbe_iscrmuser'] = 1;
                                if (strstr($postOne['post_name'], "校长")) {
                                    $postbe['postbe_crmuserlevel'] = 1;
                                }
                                $postbe['postbe_isreceptionuser'] = 1;
                            }
                            if ($this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id = '{$schoolOne['school_id']}'", $postbe)) {
                                $this->oktip = "校区一级职务更新成功<br />";
                            } else {
                                $this->errortip = "校区一级职务更新失败<br />";
                                $this->error = true;
                            }
                        } else {
                            $this->errortip = "校区一级职务相似不用更新<br />";
                            $this->error = true;
                        }
                    }
                } else {
                    $this->errortip = "校区一级组织信息不存在<br />";
                    $this->error = true;
                }
            } else {
                $postOne = $this->DataControl->getOne("gmc_company_post", "company_id = '8888' AND post_code = '{$jobOne['JOB_Number']}' AND post_type = '0'");
                if (!$postOne) {
                    $this->errortip = "区域职务不存在<br />";
                    $this->error = true;
                } else {
                    $parentOne = $this->DataControl->getOne("gmc_company_organize", "organize_pid = '{$jobOne['FParentID']}' AND organizeclass_id = '480' AND father_id <> '0'");
                    $organizeOne = $this->DataControl->getOne("gmc_company_organize", "organize_id = '{$parentOne['father_id']}' AND organizeclass_id = '480'");
                    if ($parentOne && $organizeOne) {
                        $postroleOne = $this->DataControl->getOne("gmc_company_postrole", "postrole_id = '{$postOne['postrole_id']}'");
                        //集团组织获取
                        if (!$this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND organize_id = '{$organizeOne['organize_id']}'")) {
                            $postbe = array();
                            $postbe['company_id'] = '8888';
                            $postbe['postbe_fromsystem'] = '1';
                            $postbe['organize_id'] = $organizeOne['organize_id'];
                            $postbe['postlevel_id'] = $postOne['postlevel_id'];
                            $postbe['staffer_id'] = $stafferOne['staffer_id'];
                            $postbe['post_id'] = $postOne['post_id'];
                            $postbe['postrole_id'] = $postOne['postrole_id'];
                            $postbe['postpart_id'] = $postroleOne['postpart_id'];
                            if ($jobOne['ISMAINJOB'] == '1') {
                                $postbe['postbe_ismianjob'] = '1';
                            } else {
                                $postbe['postbe_ismianjob'] = '0';
                            }
                            $postbe['postbe_status'] = '1';
                            $postbe['postbe_iscrmuser'] = $postroleOne['postpart_iscrmuser'];
                            $postbe['postbe_createtime'] = time();
                            if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                                $this->oktip = "集团二级职务添加成功<br />";
                            } else {
                                $this->errortip = "集团二级职务添加失败<br />";
                                $this->error = true;
                            }
                        } else {
                            $this->errortip = "集团二级职务已增加<br />";
                            $this->error = true;
                        }
                    } else {
                        $this->errortip = "集团二级职务信息不存在<br />";
                        $this->error = true;
                    }
                }
            }
        } elseif ($jobOne['FLevel'] == '6') {
            //校园二级职务
            $schoolOne = $this->DataControl->getOne("smc_school", "company_id = '8888' AND binary school_erpshopcode = '{$jobOne['FParentID']}'");
            $postOne = $this->DataControl->getOne("gmc_company_post", "company_id = '8888' AND post_code = '{$jobOne['JOB_Number']}' AND post_type = '1'");
            $organizeOne = $this->DataControl->selectOne("SELECT o.organize_id FROM gmc_company_organize AS o,gmc_company_organizeschool AS e,smc_school AS s
WHERE o.organize_id = e.organize_id AND e.school_id = s.school_id AND o.organizeclass_id = '480' AND o.father_id = '0'
AND s.school_erpshopcode = '{$jobOne['FParentID']}' limit 0,1");
            if ($postOne && $organizeOne) {
                $postpartOne = $this->DataControl->getOne("smc_school_postpart", "postpart_id = '{$postOne['postpart_id']}'");
                //集团组织获取
                $postbeOne = $this->DataControl->getFieldOne("gmc_staffer_postbe", "postbe_id", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id = '{$schoolOne['school_id']}'");
                if (!$postbeOne) {
                    $postbe = array();
                    $postbe['company_id'] = '8888';
                    $postbe['school_id'] = $schoolOne['school_id'];
                    $postbe['postbe_fromsystem'] = '1';
                    $postbe['organize_id'] = $organizeOne['organize_id'];
                    $postbe['postlevel_id'] = $postOne['postlevel_id'];
                    $postbe['post_id'] = $postOne['post_id'];
                    $postbe['staffer_id'] = $stafferOne['staffer_id'];
                    $postbe['postpart_id'] = $postOne['postpart_id'];
                    if ($jobOne['ISMAINJOB'] == '1') {
                        $postbe['postbe_ismianjob'] = '1';
                    } else {
                        $postbe['postbe_ismianjob'] = '0';
                    }
                    $postbe['postbe_status'] = '1';
                    if ($postpartOne['postpart_iscrmuser'] == '1') {
                        $postbe['postbe_iscrmuser'] = 1;
                        if (strstr($postOne['post_name'], "校长")) {
                            $postbe['postbe_crmuserlevel'] = 1;
                        }
                        $postbe['postbe_isreceptionuser'] = 1;
                    }
                    $postbe['postbe_createtime'] = time();
                    if ($this->DataControl->insertData("gmc_staffer_postbe", $postbe)) {
                        $this->oktip = "校区二级职务添加成功<br />";
                    } else {
                        $this->errortip = "校区二级职务添加失败<br />";
                        $this->error = true;
                    }
                } else {
                    if ($postbeOne['post_id'] !== $postOne['post_id']) {
                        $postbe = array();
                        $postbe['postbe_fromsystem'] = '1';
                        $postbe['organize_id'] = $organizeOne['organize_id'];
                        $postbe['postlevel_id'] = $postOne['postlevel_id'];
                        $postbe['post_id'] = $postOne['post_id'];
                        $postbe['postpart_id'] = $postOne['postpart_id'];
                        if ($jobOne['ISMAINJOB'] == '1') {
                            $postbe['postbe_ismianjob'] = '1';
                        } else {
                            $postbe['postbe_ismianjob'] = '0';
                        }
                        $postbe['postbe_status'] = '1';
                        if ($postpartOne['postpart_iscrmuser'] == '1') {
                            $postbe['postbe_iscrmuser'] = 1;
                            if (strstr($postOne['post_name'], "校长")) {
                                $postbe['postbe_crmuserlevel'] = 1;
                            }
                            $postbe['postbe_isreceptionuser'] = 1;
                        }
                        if ($this->DataControl->updateData("gmc_staffer_postbe", "staffer_id = '{$stafferOne['staffer_id']}' AND school_id = '{$schoolOne['school_id']}'", $postbe)) {
                            $this->oktip = "校区二级职务更新成功<br />";
                        } else {
                            $this->errortip = "校区二级职务更新失败<br />";
                            $this->error = true;
                        }
                    } else {
                        $this->errortip = "校区二级职务相似不用更新<br />";
                        $this->error = true;
                    }
                }
            } else {
                $this->errortip = "校区二级职务信息不存在<br />";
                $this->error = true;
            }
        }
        return true;
    }

    //职工离职操作
    function hrLeave($stafferOne)
    {
        $data = array();
        $data['staffer_leave'] = '1';
        $data['staffer_leavetime'] = date("Y-m-d");
        $data['staffer_updatetime'] = time();
        $data['staffer_leavecause'] = "系统已检测HR人资信息离职，进行离职操作！";
        if ($this->DataControl->updateData("smc_staffer", "staffer_id = '{$stafferOne['staffer_id']}'", $data)) {
            $data = array();
            $data['company_id'] = $stafferOne['company_id'];
            $data['staffer_id'] = $stafferOne['staffer_id'];
            $data['workchange_code'] = 'Z02';
            $data['postchangeslog_note'] = "集团给教师做离职操作，进行离职清算操作";
            $data['postchangeslog_day'] = date("Y-m-d", time());
            $data['postchangeslog_createtime'] = time();
            $this->DataControl->insertData("gmc_staffer_postchangeslog", $data);
            $this->addGmcWorkLog($stafferOne['company_id'], '12357', "集团架构->职工管理", '系统已检测HR人资信息离职，进行离职操作！', dataEncode($stafferOne));
        }
    }

    //职工操作日志
    function addGmcWorkLog($company_id, $staffer_id, $module, $type, $content)
    {
        $logData = array();
        $logData['company_id'] = $company_id;
        $logData['staffer_id'] = $staffer_id;
        $logData['worklog_module'] = $module;
        $logData['worklog_type'] = $type;
        $logData['worklog_content'] = $content;
        $logData['worklog_ip'] = real_ip();
        $logData['worklog_time'] = time();
        $this->DataControl->insertData('gmc_staffer_worklog', $logData);
    }

    public function sample(Request $request)
    {
        $microtime = microtime(true);
        $client = new \GuzzleHttp\Client();

        $yzjAppId = '*********';
        $yzjAppSecret = 'vCQ6LM30lYZXgI2dEFz1';
        $yzjResGroupSecret = 'YJpK1LHI20HGkEBrXU3xEDxrB47Dmsg0';

        try {

            $ticket = $request->input('ticket', '');

            if (empty($ticket)) throw new \Exception('ticket is empty');

            $response = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => [
                        'timestamp' => intval($microtime * 1000),
                        'appId' => $yzjAppId,
                        'secret' => $yzjAppSecret,
                        'scope' => 'app',
                        'eid' => '17916472',
                    ]
                ]
            );
            $accessToken = '';
            $tokenContent = $response->getBody()->getContents();
            $tokenContent = json_decode($tokenContent, true);
            if ($tokenContent['errorCode'] == 0) {
                $accessToken = $tokenContent['data']['accessToken'];
            }

            if (empty($accessToken)) throw new \Exception('accessToken is empty');

            $ticketResponse = $client->request('POST', 'https://yunzhijia.com/gateway/ticket/user/acquirecontext?accessToken=' . $accessToken, [
                'headers' => ['Content-Type' => 'application/json'],
                'json' => [
                    'eid' => '17916472',
                    'ticket' => $ticket,
                    'appid' => $yzjAppId,
                ]
            ]);

            $openId = '';
            $ticketResponse = $ticketResponse->getBody()->getContents();
            $ticketResponse = json_decode($ticketResponse, true);
            if ($ticketResponse['errorCode'] == 0) {
                $openId = $ticketResponse['data']['openid'];
            }
            if (empty($openId)) throw new \Exception('openId is empty');

            $resGroupTokenResponse = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => [
                        'timestamp' => intval($microtime * 1000),
                        'appId' => $yzjAppId,
                        'secret' => $yzjResGroupSecret,
                        'scope' => 'resGroupSecret',
                        'eid' => '17916472',
                    ]
                ]
            );
            $resGroupToken = '';
            $resGroupTokenResponse = $resGroupTokenResponse->getBody()->getContents();
            $resGroupTokenResponse = json_decode($resGroupTokenResponse, true);
            if ($resGroupTokenResponse['errorCode'] == 0) {
                $resGroupToken = $resGroupTokenResponse['data']['accessToken'];
            }
            if (empty($resGroupToken)) throw new \Exception('resGroupToken is empty');

            $userResponse = request_by_curl('POST', 'https://yunzhijia.com/gateway/openimport/open/person/get?accessToken=' . $resGroupToken, [
                'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                'form_params' => [
                    'eid' => '17916472',
                    'data' => json_encode(['type' => 1, 'array' => [$openId]]),
                ],
            ]);

            $phone = '';
            $userResponse = json_decode($userResponse->getBody()->getContents(), true);
            if ($userResponse['success'] == true) {
                $phone = $userResponse['data'][0]['phone'];
            }
            if (empty($phone)) throw new \Exception('phone is empty');
        } catch (\Exception $e) {

        }
    }

    //获取云之家token
    function getAccessTokenFromYZJ($scope = 0)
    {
        $url = 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken';
        $time = round(microtime(true) * 1000, 0);

        //固定参数
        $scope_level = array(0 => 'app', 1 => 'team', 2 => 'resGroupSecret');
        $yzjEId = '17916472';
        $yzjResGroupSecret = 'YJpK1LHI20HGkEBrXU3xEDxrB47Dmsg0';

        $yzjAppId = '500672631';
        $yzjAppSecret = 'q9XqBU6wtzmCTeUCDkEB';
        $yzjSignKey = '7QHu5vGdx488lBq9hYm2';

        $form_params = array();
        $form_params['appId'] = $yzjAppId;
        $form_params['eid'] = $yzjEId;
        $form_params['secret'] = $scope == 0 ? $yzjAppSecret : $yzjResGroupSecret;
        $form_params['timestamp'] = $time;
        $form_params['scope'] = $scope_level[$scope];

        $response = request_by_curl($url, dataEncode($form_params), "post");
        $deresponse = json_decode($response, 1);

        if ($deresponse['success']) {
            return $deresponse['data'];
        } else {
            return false;
        }
    }

    //根据ticket解析用户身份-没跑通
    function getAcquireContextFromYZJ()
    {
        $accessToken = $this->getAccessTokenFromYZJ(0);
        $url = 'https://yunzhijia.com/gateway/ticket/user/acquirecontext?accessToken=' . $accessToken['accessToken'];

        $yzjEId = '17916472';

        //外部获取轻应用的参数轻应用的参数
        $yzjAppId = '500672631';
        $yzjAppSecret = 'q9XqBU6wtzmCTeUCDkEB';
        $yzjSignKey = '7QHu5vGdx488lBq9hYm2';
        $ticket = 'APPURLWITHTICKETd5a3b5ce415b6bdb9d5aa64bf0e1d6c1';


        $form_params = array();
        $form_params['eid'] = $yzjEId;
        $form_params['appid'] = $yzjAppId;
        $form_params['ticket'] = $ticket;

        $response = request_by_curl($url, json_encode($form_params), "post", ['Content-Type' => 'application/json']);
        $deresponse = json_decode($response, 1);
        var_dump($deresponse);
        die;

        if ($deresponse['success']) {
            return $deresponse['data'];
        } else {
            return false;
        }
    }

    //查询指定人员信息
    function getPersonInfoFromYZJ()
    {
        $accessToken = $this->getAccessTokenFromYZJ(2);
        $url = 'https://yunzhijia.com/gateway/openimport/open/person/get?accessToken=' . $accessToken['accessToken'];

        //固定参数
        $yzjEId = '17916472';
        $yzjResGroupSecret = 'YJpK1LHI20HGkEBrXU3xEDxrB47Dmsg0';

        //外部获取轻应用的参数
        $yzjAppId = '500672631';
        $yzjSecret = 'q9XqBU6wtzmCTeUCDkEB';
        $signKey = '7QHu5vGdx488lBq9hYm2';
        $ticket = 'APPURLWITHTICKETd5a3b5ce415b6bdb9d5aa64bf0e1d6c1';

        //获取open_id
        $acquireContext = $this->getAcquireContextFromYZJ();
        $openId = $acquireContext['openid'];

        $data = array();
        $data['type'] = 1;
        $data['array'] = array($openId);

        $form_params = array();
        $form_params['accessToken'] = $accessToken['accessToken'];
        $form_params['appid'] = $yzjAppId;
        $form_params['eid'] = $yzjEId;
        $form_params['data'] = dataEncode($data);

        $response = request_by_curl($url, json_encode($form_params), "post", ['Content-Type' => 'application/json']);
        $deresponse = json_decode($response, 1);
        var_dump($deresponse);
        die;

        if ($deresponse['success']) {
            return $deresponse;
        } else {
            return false;
        }
    }

}