<?php


namespace Model\Api;

class CmbTransModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $companies_id = 0;//操作公司
    public $aeskey = '';//加密密钥
    public $secretkey = '';//签名密钥
    public $cmbbranch = '';//平台编号
    public $xssting = '';
    public $tstr = '';
    public $signature = '';
    public $agencyId = '';
    public $starttime = '';
    public $code = '';
//    public $zs_url = 'https://openmain-st.park.cmbchina.biz';
//    public $zs_url = 'https://sks-ext.srv.cmbchina.biz';
//    public $zs_url = 'https://pfsgw.paas.cmbchina.com';
    public $zs_url = 'https://api.cmbchina.com/eduzjjg/openapi';

    function __construct($agencyId)
    {
        parent::__construct();

        if ($agencyId && $agencyId != '') {
            $this->agencyId = $agencyId;
            $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_id,company_id,companies_signature,companies_secretkey,companies_supervisetime", "companies_agencyid='{$this->agencyId}'");
            if ($companiesOne) {
                $this->company_id = $companiesOne['company_id'];
                $this->companies_id = $companiesOne['companies_id'];
                $this->starttime = $companiesOne['companies_supervisetime'];
                //$this->aeskey = $companiesOne['companies_secretkey'];
                //$this->secretkey = $companiesOne['companies_signature'];

                $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_cmbbranch,company_signature,company_secretkey", "company_id='{$companiesOne['company_id']}'");
                if ($companyOne && $companyOne['company_cmbbranch'] != '') {
                    $this->aeskey = $companyOne['company_secretkey'];
                    $this->secretkey = $companyOne['company_signature'];
                    $this->cmbbranch = $companyOne['company_cmbbranch'];
                }
            } else {
//                $this->error = 1;
//                $this->errortip = "请选择请看是否设置机构代码";
//                return false;

                $companyOne = $this->DataControl->getFieldOne("gmc_company", "company_id,company_cmbbranch,company_signature,company_secretkey", "company_cmbbranch='{$agencyId}'");

                $this->starttime = '1645027200';

                if ($companyOne && $companyOne['company_cmbbranch'] != '') {
                    $this->company_id = $companyOne['company_id'];
                    $this->aeskey = $companyOne['company_secretkey'];
                    $this->secretkey = $companyOne['company_signature'];
                    $this->cmbbranch = $companyOne['company_cmbbranch'];
                }else{
                    $this->error = 1;
                    $this->errortip = "机构信息错误";
                    return false;
                }
            }
        } else {
            $this->error = 1;
            $this->errortip = "请选择机构";
            return false;
        }
    }

    function getUnixTimestamp()
    {
        list($s1, $s2) = explode(' ', microtime());
        $time = sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);

        return $time * 1;
    }

    function encrypt($data)
    {
        $aes = new \Aesencdec($this->aeskey, '', 'AES-128-ECB');

        $this->xssting = $aes->encrypt($data);//加密

        $this->tstr = $this->getUnixTimestamp();

        $this->signature = hash_hmac('sha256', $this->xssting . $this->tstr, $this->secretkey);

    }

    function decrypt($data)
    {

        $aes = new \Aesencdec($this->aeskey, '', 'AES-128-ECB');
        $xssting = $aes->decrypt($data);//解密

        $paramJson = json_decode($xssting, 1);//转化为数组
        return $paramJson;
    }


    function synchroAllInfo()
    {
        $data = array();
        $data['start_time'] = '1645027200';
        $data['begin_date'] = '2022-02-17';

        $this->synchroClassInfo();//班级信息同步(C)

        $this->synchroClassHourInfo();//排课信息同步(C)

        $this->synchroOrderItemInfo();//售退课汇总信息同步(C)
        $this->synchroOrderInfo();//班级购课明细同步(C)


        $this->synchroPushInfo();//销课明细推送(C)
        $this->synchroProposePushInfo();//提出划拨(C)

    }


    function synchroClassHourInfo()
    {
        $sql = "select D.class_branch as classId,C.hour_lessontimes as lessonIndex,C.hour_name as lessonName
                ,C.hour_day as lessonDate,hour_status as classHour,A.lesson_id
                from cmb_trans_lesson as A,smc_class_hour as C,cmb_trans_class as D
                where D.class_id=A.class_id AND C.hour_id=A.hour_id AND A.lesson_status=0 and A.agency_id='{$this->agencyId}' and D.update_status=1
                order by A.class_id limit 50";
        $hourList = $this->DataControl->selectClear($sql);

        if ($hourList) {

            $tem_array = array();
            $tem_data = array();
            $num = 0;
            foreach ($hourList as $k => $hourOne) {
                $num++;
                $tem_data[] = $hourOne;

                if ($num == 500 || ($k + 1) == count($hourList)) {
                    $tem_array[] = $tem_data;
                    $tem_data = array();
                    $num = 0;
                }
            }

            foreach ($tem_array as $temList) {

                $batch_pid = $this->create_guid();

                $data = array();
                $data['agency_id'] = $this->agencyId;
                $data['company_id'] = $this->company_id;
                $data['companies_id'] = $this->companies_id;
                $data['batch_pid'] = $batch_pid;
                $data['batch_date'] = date("Y-m-d");
                $data['batch_num'] = count($temList);
                $this->DataControl->insertData("cmb_trans_class_batch", $data);

                $data = array();
                $data['agencyId'] = $this->agencyId;
                $data['lessonNum'] = count($temList);
                $data['lessonList'] = $temList;

                $returnData = $this->transfer($data, '/pfs/head/addClassLesson', $batch_pid);

                if ($returnData && $returnData['success'] == 1) {
                    foreach ($temList as $hourOne) {
                        $data = array();
                        $data['batch_pid'] = $batch_pid;
                        $data['lesson_status'] = 1;
                        $data['lesson_updatetime'] = time();
                        $this->DataControl->updateData("cmb_trans_lesson", "lesson_id='{$hourOne['lesson_id']}'", $data);
                        $num++;
                    }

                    $data = array();
                    $data['batch_status'] = 2;
                    $this->DataControl->updateData("cmb_trans_class_batch", "batch_pid='{$batch_pid}'", $data);

                }
            }

            return $num;
        } else {
            $this->error = 1;
            $this->errortip = "无需同步班级排课";
            return false;
        }

    }


    function synchroGatherOrderItemInfo($class_branch = '')
    {
        $datewhere = "A.company_id='{$this->company_id}' and A.companies_id='{$this->companies_id}' and A.agency_id='{$this->agencyId}' and A.order_date < CURDATE() and A.is_confirm=1 and D.update_status=1
                and exists(select 1 from cmb_trans_order as x where x.class_id=A.class_id and x.order_status_all in (-1,0) and x.is_confirm=1) ";
        if ($class_branch !== '') {
            $datewhere .= " AND D.class_branch = '{$class_branch}'";
        }

//        $classOne=$this->DataControl->getFieldOne("cmb_trans_class","class_stdate","class_branch='{$class_branch}'");

        $sql = "select D.class_branch as classId,A.order_date
                ,sum(IF(A.order_type='P',A.order_num,0)) as enrollClassHour
                ,count(IF(A.order_type='P',true,null)) as payNum
                ,sum(IF(A.order_type='P',A.order_amt,0)) as payAmt
                ,sum(IF(A.order_type='P',A.order_fee,0)) as payFee
                ,sum(IF(A.order_type='R',A.order_num,0)) as dropClassHour
                ,count(IF(A.order_type='R',true,null)) as refundNum
                ,sum(IF(A.order_type='R',A.order_amt,0)) as refundAmt
                ,sum(IF(A.order_type='R',A.order_fee,0)) as refundFee
                ,ifnull((select sum(x.order_num) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalEnrollClassHour
                ,ifnull((select sum(x.order_num) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='R' and x.is_confirm=1 and x.order_amt>0),0) as totalDropClassHour
                ,ifnull((select count(x.order_id) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalPayNum
                ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalPayAmt
                ,ifnull((select sum(x.order_fee) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalPayFee
                ,ifnull((select count(x.order_id) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='R' and x.is_confirm=1 and x.order_amt>0),0) as totalRefundNum
                ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='R' and x.is_confirm=1 and x.order_amt>0),0) as totalRefundAmt
                ,ifnull((select sum(x.order_fee) from cmb_trans_order as x where x.class_id=D.class_id and x.companies_id=A.companies_id and x.order_date<=A.order_date and x.order_type='R' and x.is_confirm=1 and x.order_amt>0),0) as totalRefundFee
                from cmb_trans_order as A 
                inner join cmb_trans_class as D on A.class_id=D.class_id
                where {$datewhere} and A.order_amt>0
                and (A.order_type='P' or (A.order_type='R' and A.from_order_pid<>''))
                group by A.class_id,A.order_date,A.companies_id
                order by A.order_date asc,A.class_id asc
                limit 0,50
                ";
//debug($sql);exit;
        $List = $this->DataControl->selectClear($sql);

        $num = 0;
        if ($List) {
            $endtime = 0;
            $starttime = 0;
            $tem_list = array();
            foreach ($List as $k => $listOne) {
                $tem_list[$listOne['order_date']][] = $listOne;
                if ($k == 0) {
                    $starttime = $listOne['order_date'];
                }
                $endtime = $listOne['order_date'];
            }
//            $starttime='2022-03-27';
            $dateArray = array();
            while ($starttime <= $endtime) {
                array_push($dateArray, $starttime);
                $starttime = date("Y-m-d", strtotime('+1 day', strtotime($starttime)));
            }

            foreach ($dateArray as $dateOne) {

                $date = $dateOne;

                if ($tem_list[$dateOne]) {
                    $temArray = $tem_list[$dateOne];

                    foreach ($temArray as &$temOne) {
                        $temOne['enrollClassHour'] = (int)$temOne['enrollClassHour'];
                        $temOne['payNum'] = (int)$temOne['payNum'];
                        $temOne['payAmt'] = (int)(bcmul($temOne['payAmt'], 100));
                        $temOne['payFee'] = (int)(bcmul($temOne['payFee'], 100));
                        $temOne['dropClassHour'] = (int)$temOne['dropClassHour'];
                        $temOne['refundNum'] = (int)$temOne['refundNum'];
                        $temOne['refundAmt'] = (int)(bcmul($temOne['refundAmt'], 100));
                        $temOne['refundFee'] = (int)(bcmul($temOne['refundFee'], 100));
                        $temOne['totalEnrollClassHour'] = (int)$temOne['totalEnrollClassHour'];
                        $temOne['totalDropClassHour'] = (int)$temOne['totalDropClassHour'];
                        $temOne['totalPayNum'] = (int)$temOne['totalPayNum'];
                        $temOne['totalPayAmt'] = (int)(bcmul($temOne['totalPayAmt'], 100));
                        $temOne['totalPayFee'] = (int)(bcmul($temOne['totalPayFee'], 100));
                        $temOne['totalRefundAmt'] = (int)(bcmul($temOne['totalRefundAmt'], 100));
                        $temOne['totalRefundFee'] = (int)(bcmul($temOne['totalRefundFee'], 100));
                        $temOne['totalRefundNum'] = (int)$temOne['totalRefundNum'];
                    }

                    $tem_array = array();
                    $tem_data = array();
                    $num = 0;
                    $allNum = 0;
                    foreach ($temArray as $k => $One) {
                        $allNum++;
                        $num++;
                        $tem_data[] = $One;

                        if ($num == 500 || ($k + 1) == count($temArray)) {
                            $tem_array[] = $tem_data;
                            $tem_data = array();
                            $num = 0;
                        }
                    }

                    foreach ($tem_array as $temList) {

                        $data = array();
                        $data['agencyId'] = $this->agencyId;
                        $data['classNum'] = $allNum;
                        $data['batchClassNum'] = $temList ? count($temList) : 0;
                        $data['date'] = $date;
                        $data['classList'] = $temList;
                        $batch_pid = $this->create_guid();

                        $returnData = $this->transfer($data, '/pfs/head/addClassIncrement', $batch_pid);

                        if ($returnData) {
                            foreach ($returnData as $returnOne) {
                                $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch='{$returnOne['classId']}'");
                                if ($returnOne['success']) {

                                    $data = array();
                                    $data['batch_pid_all'] = $batch_pid;
                                    $data['order_status_all'] = 1;

                                    $this->DataControl->updateData("cmb_trans_order", "class_id='{$classOne['class_id']}' and order_date='{$date}' and is_confirm=1 and order_status<>1 and order_status_all<>1", $data);
                                    $num++;
                                } else {
                                    $data = array();
                                    $data['batch_pid_all'] = $batch_pid;
                                    $data['order_status_all'] = -1;

                                    $this->DataControl->updateData("cmb_trans_order", "class_id='{$classOne['class_id']}' and order_date='{$date}' and is_confirm=1 and order_status<>1 and order_status_all<>1", $data);

                                    $data = array();
                                    $data['company_id'] = $this->company_id;
                                    $data['companies_id'] = $this->companies_id;
                                    $data['faillog_url'] = '/pfs/head/addClassIncrement';
                                    $data['batch_pid'] = $batch_pid;
                                    $data['agency_id'] = $this->agencyId;
                                    $data['class_branch'] = $returnOne['classId'];
                                    $data['failMessage'] = $returnOne['desc'];
                                    $data['faillog_time'] = time();
                                    $this->DataControl->insertData("cmb_trans_faillog", $data);
                                }
                            }
                        }
                    }


                } else {
                    $temList = array();

                    $data = array();
                    $data['classId'] = $class_branch;
                    $data['order_date'] = $date;
                    $data['enrollClassHour'] = 0;
                    $data['payNum'] = 0;
                    $data['payAmt'] = 0;
                    $data['payFee'] = 0;
                    $data['dropClassHour'] = 0;
                    $data['refundNum'] = 0;
                    $data['refundAmt'] = 0;
                    $data['refundFee'] = 0;
                    $data['totalEnrollClassHour'] = 0;
                    $data['totalDropClassHour'] = 0;

                    $data['totalPayNum'] = (int)$temOne['totalPayNum'];
                    $data['totalPayAmt'] = (int)(bcmul($temOne['totalPayAmt'], 1));
                    $data['totalPayFee'] = (int)(bcmul($temOne['totalPayFee'], 1));
                    $data['totalRefundAmt'] = (int)(bcmul($temOne['totalRefundAmt'], 1));
                    $data['totalRefundFee'] = (int)(bcmul($temOne['totalRefundFee'], 1));
                    $data['totalRefundNum'] = (int)$temOne['totalRefundNum'];


                    $temList[] = $data;

                    $data = array();
                    $data['agencyId'] = $this->agencyId;
                    $data['classNum'] = 1;
                    $data['batchClassNum'] = $temList ? count($temList) : 0;
                    $data['date'] = $date;
                    $data['classList'] = $temList;

                    $batch_pid = $this->create_guid();
                    $returnData = $this->transfer($data, '/pfs/head/addClassIncrement', $batch_pid);
                }

            }

            $data = array();
            $data['is_confirm'] = 1;
            $this->DataControl->updateData("cmb_trans_class_gatherorder", "classId='{$class_branch}'", $data);

            return $num;
        } else {
            $data = array();
            $data['is_confirm'] = 1;
            $this->DataControl->updateData("cmb_trans_class_gatherorder", "classId='{$class_branch}'", $data);

            $this->error = 1;
            $this->errortip = "无需同步订单";
            return false;
        }

    }

    function synchroOrderItemInfo($class_branch = '')
    {
        $datewhere = "A.company_id='{$this->company_id}' and A.companies_id='{$this->companies_id}' and A.agency_id='{$this->agencyId}' and A.order_date < CURDATE() and A.is_confirm=1 and D.update_status=1
                and exists(select 1 from cmb_trans_order as x where x.class_id=A.class_id and x.order_status_all=0 and x.order_status=0 and x.is_confirm=1) ";

        if ($class_branch !== '') {
            $datewhere .= " AND D.class_branch = '{$class_branch}'";
        }

        $datewhere.=" and D.class_enddate<'2024-01-01'";

        $sql = "select D.class_branch as classId,A.order_date
                ,sum(IF(A.order_type='P',A.order_num,0)) as enrollClassHour
                ,count(IF(A.order_type='P',true,null)) as payNum
                ,sum(IF(A.order_type='P',A.order_amt,0)) as payAmt
                ,sum(IF(A.order_type='P',A.order_fee,0)) as payFee
                ,sum(IF(A.order_type='R',A.order_num,0)) as dropClassHour
                ,count(IF(A.order_type='R',true,null)) as refundNum
                ,sum(IF(A.order_type='R',A.order_amt,0)) as refundAmt
                ,sum(IF(A.order_type='R',A.order_fee,0)) as refundFee
                ,ifnull((select sum(x.order_num) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalEnrollClassHour
                ,ifnull((select sum(x.order_num) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='R' and x.from_order_pid<>'' and x.is_confirm=1 and x.order_amt>0),0) as totalDropClassHour
                ,ifnull((select count(x.order_id) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalPayNum
                ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalPayAmt
                ,ifnull((select sum(x.order_fee) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='P' and x.is_confirm=1 and x.order_amt>0),0) as totalPayFee
                ,ifnull((select count(x.order_id) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='R' and x.from_order_pid<>'' and x.is_confirm=1 and x.order_amt>0),0) as totalRefundNum
                ,ifnull((select sum(x.order_amt) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='R' and x.from_order_pid<>'' and x.is_confirm=1 and x.order_amt>0),0) as totalRefundAmt
                ,ifnull((select sum(x.order_fee) from cmb_trans_order as x where x.class_id=D.class_id and x.order_date<=A.order_date and x.order_type='R' and x.from_order_pid<>'' and x.is_confirm=1 and x.order_amt>0),0) as totalRefundFee
                from cmb_trans_order as A 
                inner join cmb_trans_class as D on A.class_id=D.class_id
                where {$datewhere} and A.order_amt>0
                and (A.order_type='P' or (A.order_type='R' and A.from_order_pid<>''))
                group by A.class_id,A.order_date,A.companies_id
                order by A.class_id asc,A.order_date asc
                limit 0,200
                ";

        $List = $this->DataControl->selectClear($sql);
        $num = 0;
        if ($List) {
            $tem_list = array();
            foreach ($List as $listOne) {
                $tem_list[$listOne['order_date']][] = $listOne;
            }

            ksort($tem_list);

            foreach ($tem_list as $date => $temArray) {
                foreach ($temArray as &$temOne) {
                    $temOne['enrollClassHour'] = (int)$temOne['enrollClassHour'];
                    $temOne['payNum'] = (int)$temOne['payNum'];
                    $temOne['payAmt'] = (int)(bcmul($temOne['payAmt'], 100));
                    $temOne['payFee'] = (int)(bcmul($temOne['payFee'], 100));
                    $temOne['dropClassHour'] = (int)$temOne['dropClassHour'];
                    $temOne['refundNum'] = (int)$temOne['refundNum'];
                    $temOne['refundAmt'] = (int)(bcmul($temOne['refundAmt'], 100));
                    $temOne['refundFee'] = (int)(bcmul($temOne['refundFee'], 100));
                    $temOne['totalEnrollClassHour'] = (int)$temOne['totalEnrollClassHour'];
                    $temOne['totalDropClassHour'] = (int)$temOne['totalDropClassHour'];
                    $temOne['totalPayNum'] = (int)$temOne['totalPayNum'];
                    $temOne['totalPayAmt'] = (int)(bcmul($temOne['totalPayAmt'], 100));
                    $temOne['totalPayFee'] = (int)(bcmul($temOne['totalPayFee'], 100));
                    $temOne['totalRefundAmt'] = (int)(bcmul($temOne['totalRefundAmt'], 100));
                    $temOne['totalRefundFee'] = (int)(bcmul($temOne['totalRefundFee'], 100));
                    $temOne['totalRefundNum'] = (int)$temOne['totalRefundNum'];
                }

                $tem_array = array();
                $tem_data = array();
                $num = 0;
                $allNum = 0;
                foreach ($temArray as $k => $One) {
                    $allNum++;
                    $num++;
                    $tem_data[] = $One;

                    if ($num == 500 || ($k + 1) == count($temArray)) {
                        $tem_array[] = $tem_data;
                        $tem_data = array();
                        $num = 0;
                    }
                }

                foreach ($tem_array as $temList) {

                    $data = array();
                    $data['agencyId'] = $this->agencyId;
                    $data['classNum'] = $allNum;
                    $data['batchClassNum'] = $temList ? count($temList) : 0;
                    $data['date'] = $date;
                    $data['classList'] = $temList;

                    $batch_pid = $this->create_guid();
                    $returnData = $this->transfer($data, '/pfs/head/addClassIncrement', $batch_pid);

                    if ($returnData) {
                        foreach ($returnData as $returnOne) {
                            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id", "class_branch='{$returnOne['classId']}'");
                            if ($returnOne['success']) {

                                $data = array();
                                $data['batch_pid_all'] = $batch_pid;
                                $data['order_status_all'] = 1;

                                $this->DataControl->updateData("cmb_trans_order", "class_id='{$classOne['class_id']}' and order_date='{$date}' and is_confirm=1 and order_status<>1 and order_status_all<>1", $data);
                                $num++;
                            } else {
                                $data = array();
                                $data['batch_pid_all'] = $batch_pid;
                                $data['order_status_all'] = -1;

                                $this->DataControl->updateData("cmb_trans_order", "class_id='{$classOne['class_id']}' and order_date='{$date}' and is_confirm=1 and order_status<>1 and order_status_all<>1", $data);

                                $data = array();
                                $data['company_id'] = $this->company_id;
                                $data['companies_id'] = $this->companies_id;
                                $data['faillog_url'] = '/pfs/head/addClassIncrement';
                                $data['batch_pid'] = $batch_pid;
                                $data['agency_id'] = $this->agencyId;
                                $data['class_branch'] = $returnOne['classId'];
                                $data['failMessage'] = $returnOne['desc'];
                                $data['faillog_time'] = time();
                                $this->DataControl->insertData("cmb_trans_faillog", $data);
                            }
                        }
                    }
                }
            }

            return $num;
        } else {
            $this->error = 1;
            $this->errortip = "无需同步订单";
            return false;
        }

    }


    function synchroOrderInfo()
    {

        $sql = "select A.student_id as stuId,A.parent_mobile as stuCellphone,B.student_cnname as stuName,A.settle_date as settleTime
            ,(CASE WHEN B.student_sex='男' THEN 1 ELSE 0 END) as sex
            ,A.order_type as tranType,A.order_pid as orderNo,A.from_order_pid as assOrderNo,A.order_amt as orderAmt,A.order_fee as feeAmt,A.class_id,A.school_id,D.class_branch,A.batch_pid,A.order_date as createTime,A.coursetype_id,A.coursecat_id,A.course_id
            ,A.order_num as lessonNum
            ,A.order_num as classHour
            from cmb_trans_order as A 
            inner join smc_student as B on A.student_id=B.student_id
            inner join cmb_trans_class as D on A.class_id=D.class_id
            where A.company_id='{$this->company_id}' and A.companies_id='{$this->companies_id}' and A.order_status=0 and A.agency_id='{$this->agencyId}' and A.order_date<CURDATE() and A.is_confirm=1 and A.order_status_all=1 and A.batch_pid_all<>'' and D.class_enddate<'2024-01-01'
            and (A.order_type='P' or (A.order_type='R' and A.from_order_pid<>'')) and A.order_amt>0
            limit 20
            ";


        $List = $this->DataControl->selectClear($sql);
        $num = 0;
        if ($List) {
            $classList = array();

            foreach ($List as $listOne) {
                $classList[$listOne['class_branch']][] = $listOne;
            }

            foreach ($classList as $key => $classArray) {

                $batch_pid = $this->create_guid();

                $data = array();
                $data['agency_id'] = $this->agencyId;
                $data['company_id'] = $this->company_id;
                $data['companies_id'] = $this->companies_id;
                $data['batch_pid'] = $batch_pid;
                $data['batch_date'] = date("Y-m-d");
                $data['school_id'] = $classArray[0]['school_id'];
                $data['coursetype_id'] = $classArray[0]['coursetype_id'];
                $data['coursecat_id'] = $classArray[0]['coursecat_id'];
                $data['course_id'] = $classArray[0]['course_id'];
                $data['class_id'] = $classArray[0]['class_id'];;
                $data['batch_order_num'] = count($classArray);
                $data['batch_createtime'] = time();
                $data['batch_payfee_amt'] = 0;
                $data['batch_payfee_fee'] = 0;
                $data['batch_refund_amt'] = 0;
                $data['batch_refund_fee'] = 0;

                foreach ($classArray as &$classOne) {

                    $classOne['orderAmt'] = (int)(bcmul($classOne['orderAmt'], 100));
                    $classOne['feeAmt'] = (int)(bcmul($classOne['feeAmt'], 100));

                    if ($classOne['tranType'] == 'P') {
                        $data['batch_payfee_amt'] += $classOne['orderAmt'];
                        $data['batch_payfee_fee'] += $classOne['feeAmt'];
                    } elseif ($classOne['tranType'] == 'R') {
                        $data['batch_refund_amt'] += $classOne['orderAmt'];
                        $data['batch_refund_fee'] += $classOne['feeAmt'];
                    }

                }

                $this->DataControl->insertData("cmb_trans_order_batch", $data);

                $tem_data = array();
                $tem_data['agencyId'] = $this->agencyId;
                $tem_data['classId'] = $key;
                $tem_data['orderNum'] = count($List);
                $tem_data['totalOrderAmt'] = $data['batch_payfee_amt'];
                $tem_data['totalFeeAmt'] = $data['batch_payfee_fee'];
                $tem_data['totalRefundAmt'] = $data['batch_refund_amt'];
                $tem_data['totalRefundFeeAmt'] = $data['batch_refund_fee'];
                $tem_data['orderList'] = $classArray;
                $returnData = $this->transfer($tem_data, '/pfs/head/addStudentOrder', $batch_pid);

                if ($returnData) {
                    $falseNum = 0;
                    foreach ($returnData as $returnOne) {
                        if ($returnOne['success']) {
                            $data = array();

                            $data['batch_pid'] = $batch_pid;
                            $data['update_date'] = date("Y-m-d");
                            $data['order_status'] = 1;

                            $this->DataControl->updateData("cmb_trans_order", "order_pid='{$returnOne['orderNo']}' and order_status=0 and order_status_all=1 and batch_pid_all<>''and order_date<CURDATE()", $data);
                            $num++;
                        } else {

                            $data = array();
                            $data['batch_pid'] = $batch_pid;
                            $data['update_date'] = date("Y-m-d");
                            $data['order_status'] = -1;

                            $this->DataControl->updateData("cmb_trans_order", "order_pid='{$returnOne['orderNo']}' and order_status=0 and order_status_all=1 and batch_pid_all<>''and order_date<CURDATE()", $data);

                            $data = array();
                            $data['company_id'] = $this->company_id;
                            $data['companies_id'] = $this->companies_id;
                            $data['faillog_url'] = '/pfs/head/addStudentOrder';
                            $data['batch_pid'] = $batch_pid;
                            $data['agency_id'] = $this->agencyId;
                            $data['class_branch'] = $key;
                            $data['failMessage'] = $returnOne['desc'];
                            $data['faillog_time'] = time();
                            $this->DataControl->insertData("cmb_trans_faillog", $data);

                            $falseNum++;
                        }
                    }
                    $data = array();
                    if ($falseNum > 0) {
                        $data['batch_status'] = 1;
                    } else {
                        $data['batch_status'] = 2;
                    }

                    $data['batch_updatetime'] = time();
                    $this->DataControl->updateData("cmb_trans_order_batch", "batch_pid='{$batch_pid}'", $data);


                } else {

                    foreach ($classArray as $orderOne) {
                        $data = array();
                        $data['batch_pid'] = $batch_pid;
                        $data['update_date'] = date("Y-m-d");
                        $data['order_status'] = -1;

                        $this->DataControl->updateData("cmb_trans_order", "order_pid='{$orderOne['orderNo']}'", $data);

                    }

                    $data = array();
                    $data['batch_status'] = '-2';
                    $data['batch_updatetime'] = time();
                    $this->DataControl->updateData("cmb_trans_order_batch", "batch_pid='{$batch_pid}'", $data);

                }
            }
        }

        return $num;
    }

    function synchroPushInfo()

    {

        $time = time();

        $sql = "update cmb_trans_transfer
                set income_isconfirm=1,confirm_ip='0.0.0.0',confirm_createtime='{$time}'
                where agency_id='{$this->agencyId}' and income_isconfirm=0 and order_pid<>'' and income_date < DATE_SUB(NOW(),INTERVAL 10 DAY)";

        $this->DataControl->selectClear($sql);

//        $sql = "update
//                    cmb_trans_transfer AS a
//                    INNER JOIN cmb_trans_mapping_log AS b ON b.eliminateId = a.hourstudy_id
//                    INner join cmb_trans_class_trans as c on c.classId=b.classId
//                    set a.transfer_status=0
//                WHERE
//                    a.transfer_status = 2 and a.agency_id='{$this->agencyId}'
//                    AND b.transferStatus <> 'S' and c.surplusAmt>0
//                    AND b.transferId NOT IN (
//                    SELECT
//                        x.batch_pid
//                    FROM
//                        cmb_trans_log AS x
//                    WHERE
//                    x.log_url = '/pfs/head/addBatchTransfer' and x.agency_id='{$this->agencyId}'
//                    AND x.log_transerStatus = 0)";
//
//        $this->DataControl->selectClear($sql);


        $lastdate = '2024-01-01';

        $sql = "select A.student_id as stuId,B.class_branch as classId,A.order_pid as orderNo,A.confirm_phone as stuCellphone,B.class_branch,A.hourstudy_id as eliminateId,A.income_price as eliminateAmt,'1' as consumeHours
                -- ,concat(YEAR(A.income_date),'-12-31') as income_date
                ,date_sub(A.income_date, INTERVAL WEEKDAY(A.income_date) - 6 DAY ) as income_date
                -- ,last_day(A.income_date) as income_date
                ,ifnull((select x.reduce_price from cmb_trans_transfer_reduce as x where x.order_pid=A.order_pid and x.course_id=A.course_id and x.hour_lessontimes=D.hour_lessontimes),0) as reduce_price
                from cmb_trans_transfer as A,cmb_trans_class as B,smc_student_hourstudy as C,smc_class_hour as D,cmb_trans_order as E
                where C.hourstudy_id=A.hourstudy_id and C.hour_id=D.hour_id and A.order_pid=E.order_pid and E.class_id=B.class_id and A.course_id=E.course_id and A.agency_id='{$this->agencyId}' and A.income_isconfirm=1 and A.order_pid<>'' and A.is_confirm=1 and E.is_confirm=1 and E.order_status=1
                and A.transfer_status=0
                and A.income_date>='{$lastdate}' 
                and date_sub(A.income_date, INTERVAL WEEKDAY(A.income_date) - 6 DAY ) < DATE_SUB(NOW(),INTERVAL 15 DAY) 
                and A.income_date<>''
                ";

        if(in_array($this->agencyId,array("2022012065699298","2022012065699302","2022012065699301"))){
            $sql.=" and B.class_enddate>'2024-05-01'";
        }

        $sql.=" order by A.income_date asc limit 450";


        $incomeList = $this->DataControl->selectClear($sql);

        $num = 0;
        if ($incomeList) {
            $tem_date = array();

            foreach ($incomeList as $incomeOne) {

                $tem_date[$incomeOne['income_date']][] = $incomeOne;

            }

            if (!$tem_date) {

                $this->error = 1;

                $this->errortip = "无需同步划拨消课信息";

                return false;
            }

            foreach ($tem_date as $key => $dateArray) {

                $tem_array = array();

                $tem_data = array();

                $num = 0;

                $allNum = 0;

                foreach ($dateArray as $k => $One) {

                    $allNum++;

                    $num++;

                    $tem_data[] = $One;


                    if ($num == 500 || ($k + 1) == count($dateArray)) {

                        $tem_array[] = $tem_data;

                        $tem_data = array();

                        $num = 0;

                    }

                }


                foreach ($tem_array as $temList) {

                    $sumTransferAmt = 0;

                    $sumTransferNum = 0;


                    foreach ($temList as &$dataOne) {


                        $dataOne['eliminateAmt'] -= $dataOne['reduce_price'];


                        $dataOne['eliminateAmt'] = (int)(bcmul($dataOne['eliminateAmt'], 100));


                        $sumTransferAmt += $dataOne['eliminateAmt'];

                        $sumTransferNum++;

                    }


                    $batch_pid = $this->create_guid();


                    $data = array();

                    $data['agencyId'] = $this->agencyId;

                    $data['totalEliminateHour'] = $sumTransferNum;

                    $data['totalEliminateAmt'] = $sumTransferAmt;

                    $data['date'] = $key;

                    $data['eliminateClassList'] = $temList;

                    $num += $sumTransferNum;


                    $returnData = $this->transfer($data, '/pfs/head/addClassEliminate', $batch_pid);

                    if ($returnData) {

                        foreach ($returnData as $returnOne) {

                            if ($returnOne['success']) {

                                $data = array();

                                $data['transfer_status'] = 1;

                                $data['transfer_updatetime'] = time();


                                $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$returnOne['eliminateId']}'", $data);

                                $num++;

                            } else {

                                if($returnOne['desc']=="接收失败:1008:该销课记录已被申请划拨校验，无法再次同步"){
                                    $data = array();
                                    $data['transfer_status'] = 2;
                                    $data['transfer_updatetime'] = time();

                                    $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$returnOne['eliminateId']}'", $data);
                                }else{
                                    $data = array();

                                    $data['transfer_status'] = -2;

                                    $data['transfer_updatetime'] = time();


                                    $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$returnOne['eliminateId']}'", $data);


                                    $data = array();

                                    $data['company_id'] = $this->company_id;

                                    $data['companies_id'] = $this->companies_id;

                                    $data['faillog_url'] = '/pfs/head/addClassEliminate';

                                    $data['batch_pid'] = $batch_pid;

                                    $data['agency_id'] = $this->agencyId;

                                    $data['class_branch'] = $dateArray['class_branch'];

                                    $data['hourstudy_id'] = $returnOne['eliminateId'];

                                    $data['failMessage'] = $returnOne['desc'];

                                    $data['faillog_time'] = time();

                                    $this->DataControl->insertData("cmb_trans_faillog", $data);
                                }
                            }
                        }
                    }
                }
            }
            return $num;

        } else {
            $this->error = 1;
            $this->errortip = "无需同步划拨消课信息";
            return false;
        }

    }

    function synchroMonthAllPushInfo()
    {
        $time = time();

        $sql = "update cmb_trans_transfer
                set income_isconfirm=1,confirm_ip='0.0.0.0',confirm_createtime='{$time}'
                where agency_id='{$this->agencyId}' and income_isconfirm=0 and order_pid<>'' and income_date < DATE_SUB(NOW(),INTERVAL 10 DAY)";
        $this->DataControl->selectClear($sql);

        $startdate = '2022-01-01';
        $enddate = '2024-01-01';

        $sql = "select A.student_id as stuId,B.class_branch as classId,A.order_pid as orderNo,A.confirm_phone as stuCellphone,B.class_branch,A.hourstudy_id as eliminateId,A.income_price as eliminateAmt,'1' as consumeHours
                ,last_day(B.class_enddate) as income_date
                ,ifnull((select x.reduce_price from cmb_trans_transfer_reduce as x where x.order_pid=A.order_pid and x.course_id=A.course_id and x.hour_lessontimes=D.hour_lessontimes),0) as reduce_price
                from cmb_trans_transfer as A
                   ,cmb_trans_class as B
                   ,smc_student_hourstudy as C
                   ,smc_class_hour as D
                   ,cmb_trans_order as E
                where C.hourstudy_id=A.hourstudy_id and C.hour_id=D.hour_id and A.order_pid=E.order_pid and E.class_id=B.class_id and A.course_id=E.course_id and A.agency_id='{$this->agencyId}' and A.income_isconfirm=1 and A.order_pid<>'' and A.is_confirm=1 and E.is_confirm=1 and E.order_status=1
                and A.transfer_status=0
                and A.income_date>='{$startdate}' 
                and A.income_date<'{$enddate}' and B.class_enddate<'{$enddate}'
                and last_day(B.class_enddate) < DATE_SUB(NOW(),INTERVAL 15 DAY) 
                and last_day(B.class_enddate) < '{$enddate}'
                and A.income_date<>''
                ";

        if(in_array($this->agencyId,array("2022012065699298","2022012065699302","2022012065699301"))){
            $sql.=" and B.class_enddate>'2024-05-01'";
        }

        $sql.=" order by A.income_date asc limit 450";

        $incomeList = $this->DataControl->selectClear($sql);

        $num = 0;
        if ($incomeList) {
            $tem_date = array();

            foreach ($incomeList as $incomeOne) {
                $tem_date[$incomeOne['income_date']][] = $incomeOne;
            }

            if (!$tem_date) {
                $this->error = 1;
                $this->errortip = "无需同步划拨消课信息";
                return false;
            }

            foreach ($tem_date as $key => $dateArray) {

                $tem_array = array();
                $tem_data = array();
                $num = 0;
                $allNum = 0;
                foreach ($dateArray as $k => $One) {
                    $allNum++;
                    $num++;
                    $tem_data[] = $One;

                    if ($num == 500 || ($k + 1) == count($dateArray)) {
                        $tem_array[] = $tem_data;
                        $tem_data = array();
                        $num = 0;
                    }
                }

                foreach ($tem_array as $temList) {
                    $sumTransferAmt = 0;
                    $sumTransferNum = 0;

                    foreach ($temList as &$dataOne) {

                        $dataOne['eliminateAmt'] -= $dataOne['reduce_price'];

                        $dataOne['eliminateAmt'] = (int)(bcmul($dataOne['eliminateAmt'], 100));

                        $sumTransferAmt += $dataOne['eliminateAmt'];
                        $sumTransferNum++;
                    }

                    $batch_pid = $this->create_guid();

                    $data = array();
                    $data['agencyId'] = $this->agencyId;
                    $data['totalEliminateHour'] = $sumTransferNum;
                    $data['totalEliminateAmt'] = $sumTransferAmt;
                    $data['date'] = $key;
                    $data['eliminateClassList'] = $temList;
                    $num += $sumTransferNum;

                    $returnData = $this->transfer($data, '/pfs/head/addClassEliminate', $batch_pid);
                    if ($returnData) {
                        foreach ($returnData as $returnOne) {
                            if ($returnOne['success']) {
                                $data = array();
                                $data['transfer_status'] = 1;
                                $data['transfer_updatetime'] = time();

                                $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$returnOne['eliminateId']}'", $data);
                                $num++;
                            } else {
                                if($returnOne['desc']=="接收失败:1008:该销课记录已被申请划拨校验，无法再次同步"){
                                    $data = array();
                                    $data['transfer_status'] = 2;
                                    $data['transfer_updatetime'] = time();

                                    $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$returnOne['eliminateId']}'", $data);
                                }else{
                                    $data = array();
                                    $data['transfer_status'] = -2;
                                    $data['transfer_updatetime'] = time();

                                    $this->DataControl->updateData("cmb_trans_transfer", "hourstudy_id='{$returnOne['eliminateId']}'", $data);

                                    $data = array();
                                    $data['company_id'] = $this->company_id;
                                    $data['companies_id'] = $this->companies_id;
                                    $data['faillog_url'] = '/pfs/head/addClassEliminate';
                                    $data['batch_pid'] = $batch_pid;
                                    $data['agency_id'] = $this->agencyId;
                                    $data['class_branch'] = $dateArray['class_branch'];
                                    $data['hourstudy_id'] = $returnOne['eliminateId'];
                                    $data['failMessage'] = $returnOne['desc'];
                                    $data['faillog_time'] = time();
                                    $this->DataControl->insertData("cmb_trans_faillog", $data);
                                }
                            }
                        }
                    }
                }
            }
            return $num;
        } else {
            $this->error = 1;
            $this->errortip = "无需同步划拨消课信息";
            return false;
        }
    }

    function synchroMonthAllProposePushInfo(){

        if($this->DataControl->getFieldOne("cmb_trans_log","log_id","agency_id='{$this->agencyId}' and log_url='/pfs/head/addBatchTransfer' and log_transerStatus=0 ")){
            $this->error = 1;
            $this->errortip = "存在划拨,不可重复提交";
            return false;
        }

        $time=time();

        $sql = "update cmb_trans_transfer
                set income_isconfirm=1,confirm_ip='0.0.0.0',confirm_createtime='{$time}'
                where agency_id='{$this->agencyId}' and income_isconfirm=0 and order_pid<>'' and income_date<DATE_SUB(NOW(), INTERVAL 5 DAY)";
        $this->DataControl->selectClear($sql);

        $sql = "SELECT
                    mapping_date,
                    keyvalue,
                    mapping_hasnum 
                FROM
                    cmb_trans_mapping 
                WHERE
                     agency_id='{$this->agencyId}' and mapping_allnum > mapping_hasnum 
                ORDER BY
                    mapping_date ASC";


        if($this->DataControl->selectOne($sql)){
            $this->errortip='数据更新未完成';
            $this->error = 1;
            return false;
        }


        $lastdate='2022-01-01';
        $enddate='2024-01-01';

        $fixedDay=15;

        $sql =" SELECT
                    x.classId,
                    sum( x.income_price - x.reduce_price ) AS transferAmt,
                    last_day(x.class_enddate) as income_date,
                    x.class_id,
                    sum( x.income_times ) AS eliminateClassHour 
                FROM
                    (
                    SELECT
                        B.class_branch AS classId,
                        B.class_enddate,
                        A.income_price,
                        A.income_date,
                        B.class_id,
                        A.income_times,
                        ifnull((
                            SELECT
                                x.reduce_price 
                            FROM
                                cmb_trans_transfer_reduce AS x 
                            WHERE
                                x.order_pid = A.order_pid 
                                AND x.course_id = A.course_id 
                                AND x.hour_lessontimes = D.hour_lessontimes 
                                ),
                            0 
                        ) AS reduce_price 
                    FROM
                        cmb_trans_transfer AS A,smc_class AS B,smc_student_hourstudy AS C,
                        smc_class_hour AS D,cmb_trans_order as E,cmb_trans_class as F,view_trans_class_balance as G 
                    WHERE
                        A.class_id = B.class_id 
                        AND C.hourstudy_id = A.hourstudy_id 
                        AND C.hour_id = D.hour_id 
                        and A.order_pid=E.order_pid
                        and F.class_id=A.class_id and G.classId=F.class_branch
                        AND A.agency_id = '{$this->agencyId}' 
                        AND A.transfer_status = 1 and F.is_trans_now=0 and F.is_exceed=0 and G.willtransPrice*100<G.surplusAmt
                        AND A.income_isconfirm = 1 and F.update_status=1
                        AND A.order_pid <> '' 
                        AND A.is_confirm = 1 and E.is_confirm=1 and E.order_status=1 and A.income_date < CURDATE() and A.income_date>='{$lastdate}'and A.income_date<'{$enddate}'
                        and last_day(F.class_enddate) < DATE_SUB(NOW(),INTERVAL '{$fixedDay}' DAY)
                        and last_day(F.class_enddate) <'{$enddate}'
                    ) AS x 
                    where 1 
                    ";

//        concat(YEAR(x.income_date),'-12-31')< '{$lastdate}'

        if($this->agencyId=='2022012065699298' || $this->agencyId=='2022012065699302' || $this->agencyId=='2022012065699301'){
            $sql.=" and x.class_enddate>'2024-05-01'";
        }

        $sql.=" GROUP BY x.class_id,last_day(x.class_enddate)
	            order by transferAmt desc,x.income_date asc
	            ";

        $incomeList = $this->DataControl->selectClear($sql);

        $num=0;

        if ($incomeList) {

            $tem_date = array();

            foreach ($incomeList as $One) {

                $tem_date[$One['income_date']][] = $One;

            }

            $tem_data=array();

            foreach($tem_date as $k=>$v){

                foreach($v as $vv){

                    if($tem_data[$k]['price']){

                        $tem_data[$k]['price']+=$vv['transferAmt'];

                    }else{

                        $tem_data[$k]['price']=$vv['transferAmt'];

                    }

                    $tem_data[$k]['date']=$k;
                }
            }

            $a=array_column($tem_data,'price');

            array_multisort($a,SORT_DESC,$tem_data);

            $date='';

            foreach($tem_data as $dateValue){

                $date=$dateValue['date'];

                break;

            }

            $tem_a[$date]=$tem_date[$date];

            if (!$tem_date || $date=='' || !$tem_a) {
                $this->error = 1;
                $this->errortip = "无需同步划拨消课信息";
                return false;
            }
            $tem_date=$tem_a;

            $dateArray=$tem_date[$date];

            $tem_array=array();

            $tem_data=array();

            $tnum=0;

            $allNum=0;

            foreach($dateArray as $k=>$One){
                $allNum++;
                $tnum++;
                $tem_data[]=$One;

                if($tnum==500 || ($k+1)==count($dateArray)){
                    $tem_array[]=$tem_data;
                    $tem_data=array();
                    $tnum=0;
                }
            }

            foreach($tem_array as $temList){

                $sumTransferAmt = 0;

                $sumTransferNum = 0;

                $batch_pid = $this->create_guid();

                $subTransferId = $this->create_shortguid();

                $data = array();

                $data['agencyId'] = $this->agencyId;

                $data['classIdList'] = array_column($temList,'classId');

                $returnData = $this->transfer($data, '/pfs/head/queryClassBalance');

                if(!$returnData){
                    return 0;
                }
                if($returnData){
                    $classArray=array_column($returnData['classList'],null,'classId');
                }else{
                    return 0;
                }
                foreach ($temList as $temkey=>&$dataOne) {

                    $dataOne['transferAmt'] = (int)(bcmul($dataOne['transferAmt'] , 100));

                    $dataOne['subTransferId'] = $dataOne['class_id'] . $subTransferId;

                    $infoOne=$classArray[$dataOne['classId']];

                    if(($infoOne['totalAmt']-$infoOne['transferredAmt'])<$dataOne['transferAmt'] || ($infoOne['totalEnrollClassHour']-$infoOne['totalDropClassHour']-$infoOne['totalEliminateHour'])<$dataOne['eliminateClassHour']) {

                        $sql = "UPDATE cmb_trans_transfer AS x
                            INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                            AND x.course_id = y.course_id
                            INNER JOIN cmb_trans_class as z on z.class_id=x.class_id
                            SET x.transfer_status = '-3'
                            WHERE
                                x.agency_id = '{$this->agencyId}'
                                AND x.transfer_status = 1
                                AND x.income_isconfirm = 1
                                AND x.order_pid <> ''
                                AND x.is_confirm = 1
                                AND y.is_confirm = 1
                                AND y.order_status = 1
                                AND x.transfer_status = 1 and x.class_id = '{$dataOne['class_id']}'
                                AND last_day(z.class_enddate) = '{$date}'";


                        $this->DataControl->selectClear($sql);

                        $data=array();
                        $data['is_exceed']=1;
                        $this->DataControl->updateData("cmb_trans_class","class_id = '{$dataOne['class_id']}'",$data);

                        unset($temList[$temkey]);
                    }else{
                        $sumTransferNum++;
                        $sumTransferAmt += $dataOne['transferAmt'];
                    }
                }

//                $data = array();
//                $data['agencyId'] = $this->agencyId;
//                $returnData = $this->transfer($data, '/pfs/head/queryAccountBalance');
//
//                if($returnData['frozenAmt']<$sumTransferAmt){
//                    $this->errortip='划拨账户余额不足';
//                    $this->error = 1;
//                    return false;
//                }

                if(!$temList){
                    $this->errortip='划拨失败';
                    $this->error = 1;
                    return false;
                }

                $data = array();
                $data['agencyId'] = $this->agencyId;
                $data['transferId'] = $batch_pid;
                $data['transferNum'] = $sumTransferNum;
                $data['sumTransferAmt'] = $sumTransferAmt;
                $data['date'] = $date;
                $data['applyDate'] = date("Y-m-d");
                $data['transferList'] = (array)$temList;

                $returnData = $this->transfer($data, '/pfs/head/addBatchTransfer', $batch_pid);

                if ($returnData) {
                    $classArray=array_column($data['transferList'],'class_id');
                    if($classArray){
                        $sql = "update cmb_trans_class as a
                            set a.is_trans_now=1 
                            where a.class_id in (" . implode(",",$classArray ) . ")";
                        $this->DataControl->selectClear($sql);

                    }

                    $sql = "UPDATE cmb_trans_transfer AS x
                            INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                            AND x.course_id = y.course_id
                            INNER JOIN cmb_trans_class as z on z.class_id=x.class_id
                            SET x.batch_pid = '{$batch_pid}',
                            x.batch_date = '{$date}',
                            x.transfer_status = '3',x.subTransferId=CONCAT(x.class_id,'{$subTransferId}') 
                            WHERE
                                x.agency_id = '{$this->agencyId}'
                                AND x.transfer_status = 1
                                AND x.income_isconfirm = 1
                                AND x.order_pid <> ''
                                AND x.is_confirm = 1
                                AND y.is_confirm = 1
                                AND y.order_status = 1
                                AND x.transfer_status = 1 and x.class_id in (" . implode(",",$classArray ) . ")
                                AND last_day(z.class_enddate) = '{$date}'";

                    $this->DataControl->selectClear($sql);

                    $data = array();
                    $data['agency_id'] = $this->agencyId;
                    $data['company_id'] = $this->company_id;
                    $data['companies_id'] = $this->companies_id;
                    $data['batch_pid'] = $batch_pid;
                    $data['batch_date'] = $date;
                    $data['income_total_price'] = $sumTransferAmt;
                    $data['income_total_times'] = $sumTransferNum;
                    $data['batch_status'] = 2;

                    $this->DataControl->insertData("cmb_trans_transfer_batch", $data);

                    $num++;
                }

                return $num;
            }

            return $num;

        } else {

            $this->error = 2;

            $this->errortip = "无需同步划拨消课信息";

            return false;

        }
    }

    function synchroProposePushInfo()

    {

//        $sql = "update
//                    cmb_trans_transfer AS a
//                    INNER JOIN cmb_trans_mapping_log AS b ON b.eliminateId = a.hourstudy_id
//                    INner join cmb_trans_class_trans as c on c.classId=b.classId
//                    set a.transfer_status=0,a.transfer_updatetime = UNIX_TIMESTAMP()
//                WHERE
//                    a.transfer_status = 2 and a.agency_id='{$this->agencyId}'
//                    AND b.transferStatus <> 'S' and c.surplusAmt>0
//                    AND b.transferId NOT IN (
//                    SELECT
//                        x.batch_pid
//                    FROM
//                        cmb_trans_log AS x
//                    WHERE
//                    x.log_url = '/pfs/head/addBatchTransfer' and x.agency_id='{$this->agencyId}'
//                    AND x.log_transerStatus = 0)";
//
//        $this->DataControl->selectClear($sql);


        $sql = "select a.log_id,a.batch_pid,a.log_time
                from cmb_trans_log as a 
                where a.agency_id='{$this->agencyId}' and a.log_url='/pfs/head/addBatchTransfer' and a.log_transerStatus=0 order by a.log_id asc";

        $logList=$this->DataControl->selectClear($sql);

        if($logList){
            foreach($logList as $logOne){
                $data = array();

                $data['agencyId'] = $this->agencyId;

                $data['transferId'] = $logOne['batch_pid'];

                $returnData = $this->transfer($data, '/pfs/head/queryTransferBill');

                if(!$returnData || $returnData==''){

                    if($this->code=='1006'){
                        $data=array();
                        $data['log_transerStatus']=-1;
                        $this->DataControl->updateData("cmb_trans_log","log_id='{$logOne['log_id']}'",$data);
                    }

                    if($this->code=='1021' && $this->errortip!='本次划拨尚未完成，请稍后查询'){
                        $data=array();
                        $data['log_transerStatus']=-2;
                        $this->DataControl->updateData("cmb_trans_log","log_id='{$logOne['log_id']}'",$data);
                    }

                    if(date('Y-m-d', strtotime('+10 day',$logOne['log_time']))<date("Y-m-d")){
                        if(!$this->DataControl->getFieldOne('gmc_mislog', "mislog_time", "mislog_mobile='15618319071' and mislog_tilte = '监管通知' and FROM_UNIXTIME(mislog_time,'%Y-%m-%d')=CURDATE()")){
                            $publicarray = array();
                            $publicarray['company_id'] = 8888;
                            $minsendModel = new \Model\Api\SmsModel($publicarray);

                            $mistxt='存在监管超过10天未划拨的主体,请及时查看';
                            $tilte = "监管通知";
                            $minsendModel->gmcMisSend('15618319071',$mistxt,$tilte,'');
                        }
                    }

                }else{
                    $data=array();
                    $data['log_transerStatus']=1;
                    $this->DataControl->updateData("cmb_trans_log","log_id='{$logOne['log_id']}'",$data);

                }
            }
            $this->error = 1;
            return false;
        }

        $lastdate='2024-03-01';
        $enddate=date("Y-m-d");


//        if(date("w")==2){
//            $lastdate='2023-01-01';
//            $enddate=date("Y-m-d");
//        }elseif(date("w")==3 || date("w")==4 || date("w")==5){
//            $lastdate='2023-01-01';
//            $enddate='2024-01-01';
//        }else{
//            $this->errortip='不可划拨';
//            $this->error = 1;
//            return false;
//        }

        $fixedDay=15;

//        $limitArray=array('2022022067269522','2022082082743977','2022082082743976','2023012095505241','2022022067269524','2022022067269527','2022012065699299','2022022067269530');
//
//        if(!in_array($this->agencyId,$limitArray)){
//            $fixedDay=8;
//        }else{
//            $fixedDay=20;
//        }

        $sql = "SELECT
                    mapping_date,
                    keyvalue,
                    mapping_hasnum 
                FROM
                    cmb_trans_mapping 
                WHERE
                     agency_id='{$this->agencyId}' and mapping_allnum > mapping_hasnum 
                ORDER BY
                    mapping_date ASC";


        if($this->DataControl->selectOne($sql)){
            $this->errortip='数据更新未完成';
            $this->error = 1;
            return false;
        }

        $sql="SELECT
	x.classId,
	sum( x.income_price - x.reduce_price ) AS transferAmt,
	date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date) - 6 DAY ) as income_date,
	x.class_id,
	sum( x.income_times ) AS eliminateClassHour 
FROM
	(
	SELECT
		B.class_branch AS classId,
		B.class_enddate,
		A.income_price,
		A.income_date,
		B.class_id,
		A.income_times,
		ifnull((
			SELECT
				x.reduce_price 
			FROM
				cmb_trans_transfer_reduce AS x 
			WHERE
				x.order_pid = A.order_pid 
				AND x.course_id = A.course_id 
				AND x.hour_lessontimes = D.hour_lessontimes 
				),
			0 
		) AS reduce_price 
	FROM
		cmb_trans_transfer AS A,
		smc_class AS B,
		smc_student_hourstudy AS C,
		smc_class_hour AS D,cmb_trans_order as E 
	WHERE
		A.class_id = B.class_id 
		AND C.hourstudy_id = A.hourstudy_id 
		AND C.hour_id = D.hour_id and A.order_pid=E.order_pid
		AND A.agency_id = '{$this->agencyId}' 
		AND A.transfer_status = 1
		AND A.income_isconfirm = 1 
		AND A.order_pid <> '' 
		AND A.is_confirm = 1 and E.is_confirm=1 and E.order_status=1 and A.income_date < CURDATE() and A.income_date>='{$lastdate}'and A.income_date<'{$enddate}'
	    and date_sub(A.income_date, INTERVAL WEEKDAY(A.income_date) - 6 DAY ) < DATE_SUB(NOW(),INTERVAL '{$fixedDay}' DAY) 
	) AS x 
    where 1 
	";

//        concat(YEAR(x.income_date),'-12-31')< '{$lastdate}'

        if($this->agencyId=='2022012065699298' || $this->agencyId=='2022012065699302' || $this->agencyId=='2022012065699301'){
            $sql.=" and x.class_enddate>'2024-05-01'";
        }

        $sql.=" GROUP BY x.class_id,date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date) - 6 DAY )
	            order by transferAmt desc,x.income_date asc
	            ";

        $incomeList = $this->DataControl->selectClear($sql);

        $num=0;

        if ($incomeList) {

            $tem_date = array();

            foreach ($incomeList as $One) {

                $tem_date[$One['income_date']][] = $One;

            }

            $tem_data=array();

            foreach($tem_date as $k=>$v){

                foreach($v as $vv){

                    if($tem_data[$k]['price']){

                        $tem_data[$k]['price']+=$vv['transferAmt'];

                    }else{

                        $tem_data[$k]['price']=$vv['transferAmt'];

                    }

                    $tem_data[$k]['date']=$k;
                }
            }

            $a=array_column($tem_data,'price');

            array_multisort($a,SORT_DESC,$tem_data);

            $date='';

            foreach($tem_data as $dateValue){

                $date=$dateValue['date'];

                break;

            }

            $tem_a[$date]=$tem_date[$date];

            if (!$tem_date || $date=='' || !$tem_a) {
                $this->error = 1;
                $this->errortip = "无需同步划拨消课信息";
                return false;
            }
            $tem_date=$tem_a;

            $dateArray=$tem_date[$date];

            $tem_array=array();

            $tem_data=array();

            $tnum=0;

            $allNum=0;

            foreach($dateArray as $k=>$One){
                $allNum++;
                $tnum++;
                $tem_data[]=$One;

                if($tnum==500 || ($k+1)==count($dateArray)){
                    $tem_array[]=$tem_data;
                    $tem_data=array();
                    $tnum=0;
                }
            }

            foreach($tem_array as $temList){

                $sumTransferAmt = 0;

                $sumTransferNum = 0;

                $batch_pid = $this->create_guid();

                $subTransferId = $this->create_shortguid();

                $data = array();

                $data['agencyId'] = $this->agencyId;

                $data['classIdList'] = array_column($temList,'classId');

                $returnData = $this->transfer($data, '/pfs/head/queryClassBalance');

                if(!$returnData){
                    return 0;
                }
                if($returnData){
                    $classArray=array_column($returnData['classList'],null,'classId');
                }else{
                    return 0;
                }

                foreach ($temList as $temkey=>&$dataOne) {

                    $dataOne['transferAmt'] = (int)(bcmul($dataOne['transferAmt'] , 100));

                    $dataOne['subTransferId'] = $dataOne['class_id'] . $subTransferId;

                    $infoOne=$classArray[$dataOne['classId']];

                    if(($infoOne['totalAmt']-$infoOne['transferredAmt'])<$dataOne['transferAmt'] || ($infoOne['totalEnrollClassHour']-$infoOne['totalDropClassHour']-$infoOne['totalEliminateHour'])<$dataOne['eliminateClassHour']){

                        $sql = "update cmb_trans_transfer as x
                                    inner join cmb_trans_order as y on x.order_pid=y.order_pid and x.course_id=y.course_id
                                    set x.transfer_status='-3'
                                    where x.agency_id = '{$this->agencyId}' AND x.transfer_status = 1 AND x.income_isconfirm = 1 AND x.order_pid <> '' AND x.is_confirm = 1 and y.is_confirm = 1 and y.order_status = 1 and x.transfer_status = 1 and x.class_id = '{$dataOne['class_id']}' and date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$dataOne['income_date']}'";

                        $this->DataControl->selectClear($sql);
                    }
                    $sumTransferNum++;
                    $sumTransferAmt += $dataOne['transferAmt'];

                }

                $data = array();
                $data['agencyId'] = $this->agencyId;
                $returnData = $this->transfer($data, '/pfs/head/queryAccountBalance');

                if($returnData['frozenAmt']<$sumTransferAmt){
                    $this->errortip='划拨账户余额不足';
                    $this->error = 1;
                    return false;
                }

                $data = array();

                $data['agencyId'] = $this->agencyId;

                $data['transferId'] = $batch_pid;

                $data['transferNum'] = $temList ? count($temList) : 0;

                $data['sumTransferAmt'] = $sumTransferAmt;

                $data['date'] = $date;

                $data['applyDate'] = date("Y-m-d");

                $data['transferList'] = $temList;

                    $returnData = $this->transfer($data, '/pfs/head/addBatchTransfer', $batch_pid);

                    if ($returnData) {

                        sleep(2);

                        $trueNum = 0;

                        $falseNum = 0;

                        if ($returnData['success']) {

                            $date=date("Y-m-d");

                            if ($returnData['checkSuccessNum'] > 0) {

                                if ($returnData['checkFailNum'] > 0) {

                                    $falseNum+=$returnData['checkFailNum'];

                                    foreach($returnData['failList'] as $failOne){

                                        $data=array();
                                        $data['company_id']=$this->company_id;
                                        $data['companies_id']=$this->companies_id;
                                        $data['faillog_url']='/pfs/head/addBatchTransfer';
                                        $data['batch_pid']=$batch_pid;
                                        $data['agency_id']=$this->agencyId;
                                        $data['class_branch']=$failOne['classId'];
                                        $data['failCode']=$failOne['failCode'];
                                        $data['failMessage']=$failOne['failMessage'];
                                        $data['faillog_time']=time();
                                        $this->DataControl->insertData("cmb_trans_faillog",$data);
                                    }

                                    if ($returnData['failList']) {
                                        $failList = array_column($returnData['failList'], null, 'classId');
                                    }

                                    foreach ($dateArray as $incomeOne) {

                                        if (!$failList[$incomeOne['classId']]) {

                                            $id=$incomeOne['class_id'] . $subTransferId;

                                            $sql= " UPDATE cmb_trans_transfer AS x
                                                    INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                                                    AND x.course_id = y.course_id
                                                    SET x.batch_pid = '{$batch_pid}',
                                                    x.batch_date = '{$date}',
                                                    x.transfer_status = '2',
                                                    x.subTransferId = '{$id}'
                                                    WHERE
                                                        x.agency_id = '{$this->agencyId}'
                                                        AND x.transfer_status = 1
                                                        AND x.income_isconfirm = 1
                                                        AND x.order_pid <> ''
                                                        AND x.is_confirm = 1
                                                        AND y.is_confirm = 1
                                                        AND y.order_status = 1
                                                        AND x.transfer_status = 1
                                                        AND x.class_id = '{$incomeOne['class_id']}'
                                                        AND date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$incomeOne['income_date']}'";

                                            $this->DataControl->selectClear($sql);

                                            $num++;

                                        }else{

                                            $id=$incomeOne['class_id'] . $subTransferId;

                                            $sql = "UPDATE cmb_trans_transfer AS x
                                                    INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                                                    AND x.course_id = y.course_id
                                                    SET x.batch_pid = '{$batch_pid}',
                                                    x.batch_date = '{$date}',
                                                    x.transfer_status = '-2',
                                                    x.subTransferId = '{$id}'
                                                    WHERE
                                                        x.agency_id = '{$this->agencyId}'
                                                        AND x.transfer_status = 1
                                                        AND x.income_isconfirm = 1
                                                        AND x.order_pid <> ''
                                                        AND x.is_confirm = 1
                                                        AND y.is_confirm = 1
                                                        AND y.order_status = 1
                                                        AND x.transfer_status = 1
                                                        AND x.class_id = '{$incomeOne['class_id']}'
                                                        AND date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$incomeOne['income_date']}'";

                                            $this->DataControl->selectClear($sql);

                                        }
                                    }

                                } else {

                                    $trueNum++;

                                    foreach ($dateArray as $incomeOne) {

                                        $id=$incomeOne['class_id'] . $subTransferId;

                                        $sql = "UPDATE cmb_trans_transfer AS x
                                                INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                                                AND x.course_id = y.course_id
                                                SET x.batch_pid = '{$batch_pid}',
                                                x.batch_date = '{$date}',
                                                x.transfer_status = '2',
                                                x.subTransferId = '{$id}'
                                                WHERE
                                                    x.agency_id = '{$this->agencyId}'
                                                    AND x.transfer_status = 1
                                                    AND x.income_isconfirm = 1
                                                    AND x.order_pid <> ''
                                                    AND x.is_confirm = 1
                                                    AND y.is_confirm = 1
                                                    AND y.order_status = 1
                                                    AND x.transfer_status = 1
                                                    AND x.class_id = '{$incomeOne['class_id']}'
                                                    AND date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$incomeOne['income_date']}'";

                                        $this->DataControl->selectClear($sql);

                                        $num++;

                                    }

                                }

                            }else{

                                foreach($returnData['failList'] as $failOne){

                                    $data=array();

                                    $data['company_id']=$this->company_id;

                                    $data['companies_id']=$this->companies_id;

                                    $data['faillog_url']='/pfs/head/addBatchTransfer';

                                    $data['batch_pid']=$batch_pid;

                                    $data['agency_id']=$this->agencyId;

                                    $data['class_branch']=$failOne['classId'];

                                    $data['failCode']=$failOne['failCode'];

                                    $data['failMessage']=$failOne['failMessage'];

                                    $data['faillog_time']=time();

                                    $this->DataControl->insertData("cmb_trans_faillog",$data);

                                }

                                foreach ($dateArray as $incomeOne) {

                                    $id=$incomeOne['class_id'] . $subTransferId;

                                    $sql = "UPDATE cmb_trans_transfer AS x
                                            INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                                            AND x.course_id = y.course_id
                                            SET x.batch_pid = '{$batch_pid}',
                                            x.batch_date = '{$date}',
                                            x.transfer_status = '-2',
                                            x.subTransferId = '{$id}'
                                            WHERE
                                                x.agency_id = '{$this->agencyId}'
                                                AND x.transfer_status = 1
                                                AND x.income_isconfirm = 1
                                                AND x.order_pid <> ''
                                                AND x.is_confirm = 1
                                                AND y.is_confirm = 1
                                                AND y.order_status = 1
                                                AND x.transfer_status = 1
                                                AND x.class_id = '{$incomeOne['class_id']}'
                                                AND date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$incomeOne['income_date']}'";

                                    $this->DataControl->selectClear($sql);
                                }
                            }
                        }


                        $data = array();
                        $data['agency_id'] = $this->agencyId;
                        $data['company_id'] = $this->company_id;
                        $data['companies_id'] = $this->companies_id;
                        $data['batch_pid'] = $batch_pid;

                        $data['batch_date'] = $date;

                        $data['income_total_price'] = $sumTransferAmt;

                        $data['income_total_times'] = $sumTransferNum;

                        if ($trueNum > 0 && $falseNum==0) {
                            $data['batch_status'] = 2;
                        } else {
                            $data['batch_status'] = 1;
                        }

                        $this->DataControl->insertData("cmb_trans_transfer_batch", $data);

                        if($data['batch_status'] ==1){

                            $this->error = 1;

                            $this->errortip = "同步失败";

                            return false;

                        }
                    }

                return $num;
            }

            return $num;

        } else {

            $this->error = 2;

            $this->errortip = "无需同步划拨消课信息";

            return false;

        }
    }
    function synchroProposePushInfoTwo(){
        $this->error = 1;
        $this->errortip = "划拨未开始";
        return false;

        if($this->DataControl->getFieldOne("cmb_trans_log","log_id","agency_id='{$this->agencyId}' and log_url='/pfs/head/addBatchTransfer' and log_transerStatus=0 ")){
            $this->error = 1;
            $this->errortip = "存在划拨,不可重复提交";
            return false;
        }

        if(date("w")!=1){
            $this->error = 1;
            $this->errortip = "划拨未开始";
            return false;
        }


        $time=time();

        $sql = "update cmb_trans_transfer
                set income_isconfirm=1,confirm_ip='0.0.0.0',confirm_createtime='{$time}'
                where agency_id='{$this->agencyId}' and income_isconfirm=0 and order_pid<>'' and income_date<DATE_SUB(NOW(), INTERVAL 5 DAY)";
        $this->DataControl->selectClear($sql);


        $sql = "SELECT
                    mapping_date,
                    keyvalue,
                    mapping_hasnum 
                FROM
                    cmb_trans_mapping 
                WHERE
                     agency_id='{$this->agencyId}' and mapping_allnum > mapping_hasnum 
                ORDER BY
                    mapping_date ASC";


        if($this->DataControl->selectOne($sql)){
            $this->errortip='数据更新未完成';
            $this->error = 1;
            return false;
        }


        $lastdate='2024-01-01';
        $enddate=date("Y-m-d");

        $fixedDay=15;


        $sql =" SELECT
                    x.classId,
                    sum( x.income_price - x.reduce_price ) AS transferAmt,
                    date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date) - 6 DAY ) as income_date,
                    x.class_id,
                    sum( x.income_times ) AS eliminateClassHour 
                FROM
                    (
                    SELECT
                        B.class_branch AS classId,
                        B.class_enddate,
                        A.income_price,
                        A.income_date,
                        B.class_id,
                        A.income_times,
                        ifnull((
                            SELECT
                                x.reduce_price 
                            FROM
                                cmb_trans_transfer_reduce AS x 
                            WHERE
                                x.order_pid = A.order_pid 
                                AND x.course_id = A.course_id 
                                AND x.hour_lessontimes = D.hour_lessontimes 
                                ),
                            0 
                        ) AS reduce_price 
                    FROM
                        cmb_trans_transfer AS A,smc_class AS B,smc_student_hourstudy AS C,
                        smc_class_hour AS D,cmb_trans_order as E,cmb_trans_class as F 
                    WHERE
                        A.class_id = B.class_id 
                        AND C.hourstudy_id = A.hourstudy_id 
                        AND C.hour_id = D.hour_id 
                        and A.order_pid=E.order_pid
                        and F.class_id=A.class_id
                        AND A.agency_id = '{$this->agencyId}' 
                        AND A.transfer_status = 1 and F.is_trans_now=0
                        AND A.income_isconfirm = 1 and F.update_status=1
                        AND A.order_pid <> '' 
                        AND A.is_confirm = 1 and E.is_confirm=1 and E.order_status=1 and A.income_date < CURDATE() and A.income_date>='{$lastdate}'and A.income_date<'{$enddate}'
                        and date_sub(A.income_date, INTERVAL WEEKDAY(A.income_date) - 6 DAY ) < DATE_SUB(NOW(),INTERVAL '{$fixedDay}' DAY)
                    ) AS x 
                    where 1 
                    ";

//        concat(YEAR(x.income_date),'-12-31')< '{$lastdate}'

        if($this->agencyId=='2022012065699298' || $this->agencyId=='2022012065699302' || $this->agencyId=='2022012065699301'){
            $sql.=" and x.class_enddate>'2024-05-01'";
        }

        $sql.=" GROUP BY x.class_id,date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date) - 6 DAY )
	            order by transferAmt desc,x.income_date asc
	            ";

        $incomeList = $this->DataControl->selectClear($sql);

        $num=0;

        if ($incomeList) {

            $tem_date = array();

            foreach ($incomeList as $One) {

                $tem_date[$One['income_date']][] = $One;

            }

            $tem_data=array();

            foreach($tem_date as $k=>$v){

                foreach($v as $vv){

                    if($tem_data[$k]['price']){

                        $tem_data[$k]['price']+=$vv['transferAmt'];

                    }else{

                        $tem_data[$k]['price']=$vv['transferAmt'];

                    }

                    $tem_data[$k]['date']=$k;
                }
            }

            $a=array_column($tem_data,'price');

            array_multisort($a,SORT_DESC,$tem_data);

            $date='';

            foreach($tem_data as $dateValue){

                $date=$dateValue['date'];

                break;

            }

            $tem_a[$date]=$tem_date[$date];

            if (!$tem_date || $date=='' || !$tem_a) {
                $this->error = 1;
                $this->errortip = "无需同步划拨消课信息";
                return false;
            }
            $tem_date=$tem_a;

            $dateArray=$tem_date[$date];

            $tem_array=array();

            $tem_data=array();

            $tnum=0;

            $allNum=0;

            foreach($dateArray as $k=>$One){
                $allNum++;
                $tnum++;
                $tem_data[]=$One;

                if($tnum==500 || ($k+1)==count($dateArray)){
                    $tem_array[]=$tem_data;
                    $tem_data=array();
                    $tnum=0;
                }
            }

            foreach($tem_array as $temList){

                $sumTransferAmt = 0;

                $sumTransferNum = 0;

                $batch_pid = $this->create_guid();

                $subTransferId = $this->create_shortguid();

                $data = array();

                $data['agencyId'] = $this->agencyId;

                $data['classIdList'] = array_column($temList,'classId');

                $returnData = $this->transfer($data, '/pfs/head/queryClassBalance');

                if(!$returnData){
                    return 0;
                }
                if($returnData){
                    $classArray=array_column($returnData['classList'],null,'classId');
                }else{
                    return 0;
                }
                foreach ($temList as $temkey=>&$dataOne) {

                    $dataOne['transferAmt'] = (int)(bcmul($dataOne['transferAmt'] , 100));

                    $dataOne['subTransferId'] = $dataOne['class_id'] . $subTransferId;

                    $infoOne=$classArray[$dataOne['classId']];

                    if(($infoOne['totalAmt']-$infoOne['transferredAmt'])<$dataOne['transferAmt'] || ($infoOne['totalEnrollClassHour']-$infoOne['totalDropClassHour']-$infoOne['totalEliminateHour'])<$dataOne['eliminateClassHour']){

                        $sql = "update cmb_trans_transfer as x
                                    inner join cmb_trans_order as y on x.order_pid=y.order_pid and x.course_id=y.course_id
                                    set x.transfer_status='-3'
                                    where x.agency_id = '{$this->agencyId}' AND x.transfer_status = 1 AND x.income_isconfirm = 1 AND x.order_pid <> '' AND x.is_confirm = 1 and y.is_confirm = 1 and y.order_status = 1 and x.transfer_status = 1 and x.class_id = '{$dataOne['class_id']}' and date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$dataOne['income_date']}'";

                        $this->DataControl->selectClear($sql);

                        $data=array();
                        $data['is_exceed']=1;
                        $this->DataControl->updateData("cmb_trans_class","class_id = '{$dataOne['class_id']}'",$data);

                        unset($temList[$temkey]);
                    }else{
                        $sumTransferNum++;
                        $sumTransferAmt += $dataOne['transferAmt'];
                    }
                }

                if(!$temList){
                    $this->errortip='划拨失败';
                    $this->error = 1;
                    return false;
                }

                $lastTemArray=array();

                foreach($temList as $temOne){
                    $lastTemArray[]=$temOne;
                }

                $data = array();
                $data['agencyId'] = $this->agencyId;
                $data['transferId'] = $batch_pid;
                $data['transferNum'] = $sumTransferNum;
                $data['sumTransferAmt'] = $sumTransferAmt;
                $data['date'] = $date;
                $data['applyDate'] = date("Y-m-d");
                $data['transferList'] = (array)$lastTemArray;

                $returnData = $this->transfer($data, '/pfs/head/addBatchTransfer', $batch_pid);

                if ($returnData) {
                    $classArray=array_column($data['transferList'],'class_id');
                    if($classArray){
                        $sql = "update cmb_trans_class as a
                            set a.is_trans_now=1 
                            where a.class_id in (" . implode(",",$classArray ) . ")";
                        $this->DataControl->selectClear($sql);

                    }

                    $sql = "UPDATE cmb_trans_transfer AS x
                            INNER JOIN cmb_trans_order AS y ON x.order_pid = y.order_pid
                            AND x.course_id = y.course_id
                            SET x.batch_pid = '{$batch_pid}',
                            x.batch_date = '{$date}',
                            x.transfer_status = '3',x.subTransferId=CONCAT(x.class_id,'{$subTransferId}') 
                            WHERE
                                x.agency_id = '{$this->agencyId}'
                                AND x.transfer_status = 1
                                AND x.income_isconfirm = 1
                                AND x.order_pid <> ''
                                AND x.is_confirm = 1
                                AND y.is_confirm = 1
                                AND y.order_status = 1
                                AND x.transfer_status = 1 and x.class_id in (" . implode(",",$classArray ) . ")
                                AND date_sub( x.income_date, INTERVAL WEEKDAY(x.income_date)-6 DAY ) = '{$date}'";

                    $this->DataControl->selectClear($sql);

                    $data = array();
                    $data['agency_id'] = $this->agencyId;
                    $data['company_id'] = $this->company_id;
                    $data['companies_id'] = $this->companies_id;
                    $data['batch_pid'] = $batch_pid;
                    $data['batch_date'] = $date;
                    $data['income_total_price'] = $sumTransferAmt;
                    $data['income_total_times'] = $sumTransferNum;
                    $data['batch_status'] = 2;

                    $this->DataControl->insertData("cmb_trans_transfer_batch", $data);

                    $num++;
                }

                return $num;
            }

            return $num;

        } else {

            $this->error = 2;

            $this->errortip = "无需同步划拨消课信息";

            return false;

        }
    }


    function updateSynchroProposePushInfoStatus(){

//        $data = array();
//        $data['agencyId'] = $this->agencyId;
//        $data['transferId'] = '0976997512ED4315892F53B61E7FA6C3';
//
//        $returnData = $this->transfer($data, '/pfs/head/queryTransferBill');
//debug($returnData);exit;
        $sql = "select a.log_id,a.batch_pid,a.log_time
                from cmb_trans_log as a 
                where a.agency_id='{$this->agencyId}' and a.log_url='/pfs/head/addBatchTransfer' 
                and a.log_transerStatus=0
                order by a.log_id asc
                limit 0,5
                ";

        $logList=$this->DataControl->selectClear($sql);

        if($logList){
            foreach($logList as $logOne){
                $data = array();
                $data['agencyId'] = $this->agencyId;
                $data['transferId'] = $logOne['batch_pid'];

                $returnData = $this->transfer($data, '/pfs/head/queryTransferBill');

                if($returnData && $returnData['transferList']){
                    $data = array();
                    $data['agencyId'] = $this->agencyId;
                    $data['classIdList'] = array_column($returnData['transferList'],'classId');
                    $classInfo = $this->transfer($data, '/pfs/head/queryClassBalance');
                    $classTransArray=array_column($classInfo['classList'],null,'classId');

                    foreach($returnData['transferList'] as $transferOne){
                        $data=array();
                        $data['transfer_status']=$transferOne['transferStatus']=='S'?2:0;
                        $this->DataControl->updateData("cmb_trans_transfer","subTransferId='{$transferOne['subTransferId']}'",$data);

                        if($transferOne['transferStatus']=='S'){
                            $data=array();
                            $data['is_trans_now']=0;
                            $this->DataControl->updateData("cmb_trans_class","class_branch='{$transferOne['classId']}'",$data);
                        }

                        $data=array();
                        $data['transferredAmt']=$classTransArray[$transferOne['classId']]['transferredAmt'];
                        $data['surplusAmt']=$classTransArray[$transferOne['classId']]['surplusAmt'];
                        $data['totalAmt']=$classTransArray[$transferOne['classId']]['totalAmt'];
                        $data['transferredTimes']=$classTransArray[$transferOne['classId']]['transferredTimes'];
                        $data['totalEnrollClassHour']=$classTransArray[$transferOne['classId']]['totalEnrollClassHour'];
                        $data['totalDropCLassHour']=$classTransArray[$transferOne['classId']]['totalDropCLassHour'];
                        $data['totalEliminateHour']=$classTransArray[$transferOne['classId']]['totalEliminateHour'];

                        $this->DataControl->updateData("cmb_trans_class_trans","classId='{$transferOne['classId']}'",$data);
                    }

                    $data=array();
                    $data['log_transerStatus']=1;
                    $this->DataControl->updateData("cmb_trans_log","log_id='{$logOne['log_id']}'",$data);

                    $data=array();
                    $data['batch_isconfig']=1;
                    $this->DataControl->updateData("cmb_trans_transfer_batch","batch_pid='{$logOne['batch_pid']}'",$data);
                }
            }
        }

        return true;

    }

    function getSuccessTransInfo($batchOne){

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['transferId'] = $batchOne['batch_pid'];

        $returnData = $this->transfer($data, '/pfs/head/queryTransferBill');

        $num=0;
        if($returnData && $returnData['transferList']){

        }else{
            $data=array();
            $data['batch_isconfig']=0;
            $this->DataControl->updateData("cmb_trans_transfer_batch","batch_pid='{$batchOne['batch_pid']}'",$data);
        }

        return $num;
    }


    function handelClassExceedInfo(){

        $sql = "select a.class_id,a.class_branch
                ,ifnull((select x.order_pid from cmb_trans_order as x where x.class_id=a.class_id and x.is_confirm=1 order by x.order_date desc limit 0,1),'') as order_pid
                from cmb_trans_class as a 
                where a.agency_id='{$this->agencyId}' and a.is_exceed=1
                having order_pid<>''
                ";

        $classList=$this->DataControl->selectClear($sql);

        if($classList){

            foreach($classList as $classOne){
                $data=array();
                $data['order_status']=0;
                $data['order_status_all']=0;
                $this->DataControl->updateData("cmb_trans_order","order_pid='{$classOne['order_pid']}'",$data);
            }
        }

        return true;


    }

    function updateDayTransInfo($date,$agencyId){

        $this->autoHandleErrorInfo(array(),$date);

        $sql = "SELECT
                    mapping_date,
                    keyvalue,
                    mapping_hasnum,mapping_allnum 
                FROM
                    cmb_trans_mapping 
                WHERE
                    mapping_date='{$date}' and agency_id='{$this->agencyId}'
                ORDER BY
                    mapping_date ASC";

        $mappingOne=$this->DataControl->selectOne($sql);

        if($mappingOne){
            if($mappingOne['mapping_hasnum']<$mappingOne['mapping_allnum']){

                $num=ceil($mappingOne['mapping_allnum']/500);

                for ($i = 0; $i <= $num; $i++) {
                    $this->autoSynchroTrans($date);
                }
            }
        }

//        $sql = "update
//                cmb_trans_mapping_log A
//                LEFT JOIN cmb_trans_transfer B ON A.eliminateId=B.hourstudy_id and a.orderNo=b.order_pid
//                LEFT JOIN cmb_financial_batchlog C ON C.transferId=A.transferId and c.class_id=b.class_id
//                set b.batch_pid=a.transferId,b.subTransferId=c.subTransferId,b.batch_date=c.applyDate,b.transfer_status=2,transfer_updatetime=UNIX_TIMESTAMP(now())
//                WHERE A.transferStatus='S'
//                and b.transfer_status<>2";
//
//        $this->DataControl->query($sql);

    }

    function queryHostDetail($beginDate, $endDate)
    {

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['beginDate'] = $beginDate;
        $data['endDate'] = $endDate;

        $returnData = $this->transfer($data, '/pfs/head/queryHostDetail');
//        debug($returnData);
        if ($returnData) {
            return $returnData;
        } else {
            return false;
        }
    }

    function recordHostDetail($fixedtime)
    {

        $transList = $this->queryHostDetail($fixedtime, $fixedtime);

        if ($transList) {
            foreach ($transList as $transOne) {
                $data = array();
                $data['agency_id'] = $this->agencyId;
                $data['regAccNo'] = $transOne['regAccNo'];
                $data['tranStatus'] = $transOne['tranStatus'];
                $data['tranSerial'] = $transOne['tranSerial'];
                $data['tranAmt'] = $transOne['tranAmt'];
                $data['tag'] = $transOne['tag'];
                $data['tranDir'] = $transOne['tranDir'];
                $data['transferId'] = $transOne['transferId'];
                $data['tranDate'] = $transOne['tranDate'];
                $data['tranTime'] = $transOne['tranTime'];
                $data['currency'] = $transOne['currency'];
                $data['rcvPayAcc'] = $transOne['rcvPayAcc'];
                $data['rcvPayName'] = $transOne['rcvPayName'];
                $data['rcvPayEbk'] = $transOne['rcvPayEbk'];
                $data['rcvPayEbb'] = $transOne['rcvPayEbb'];
                $this->DataControl->insertData("cmb_trans_bill", $data);
            }

            return true;

        } else {
            return false;
        }
    }

    function queryAccountBalance()
    {
        $data = array();
        $data['agencyId'] = $this->agencyId;

        $returnData = $this->transfer($data, '/pfs/head/queryAccountBalance');

        if ($returnData) {
            return $returnData;
        } else {
            return false;
        }

    }

    function queryClassBalance($list)
    {

        $data = array();
        $data['agencyId'] = $this->agencyId;

        $classList = json_decode(stripslashes($list), 1);

        if (!isset($classList) || $classList == '') {
            $this->error = 1;
            $this->errortip = "请选择需要查询的班级";
            return false;
        }


        $data['classIdList'] = $classList;

        $returnData = $this->transfer($data, '/pfs/head/queryClassBalance');

        if ($returnData) {
            return $returnData;
        } else {
            return false;
        }
    }

    function queryTransferBill($transferId)
    {

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['transferId'] = $transferId;

        $returnData = $this->transfer($data, '/pfs/head/queryTransferBill');

        if ($returnData) {
            return $returnData;
        } else {
            return false;
        }

    }

    //查询机构订单信息
    function queryStudentOrder($dayVar = '', $classId = '')
    {
        $data = array();
        $data['agencyId'] = $this->agencyId;
        if ($classId !== '') {
            $data['classId'] = $classId;
        }
        if ($dayVar !== '') {
            $data['date'] = $dayVar;
        }
        $returnData = $this->transfer($data, '/pfs/head/queryStudentOrder');

        if ($returnData) {
            return $returnData;
        } else {
            return false;
        }
    }

    function queryClassEliminate($dayVar = '', $classId = '',$nextKeyValue='')
    {
        $data = array();
        $data['agencyId'] = $this->agencyId;
        if ($classId !== '') {
            $data['classId'] = $classId;
        }
        if ($dayVar !== '') {
            $data['date'] = $dayVar;
        }
        if($nextKeyValue!=''){
            $data['nextKeyValue'] = $nextKeyValue;
        }

        $returnData = $this->transfer($data, '/pfs/head/queryClassEliminate');

        if ($returnData) {
            return $returnData;
        } else {
            return false;
        }
    }


    function addTemplogInfo($companies_id, $agency_id, $action_status, $action_type, $table_name, $request_info)
    {
        $tempdata = array();
        $tempdata['companies_id'] = $companies_id;
        $tempdata['agency_id'] = $agency_id;
        $tempdata['templog_status'] = $action_status;
        $tempdata['templog_type'] = $action_type;
        $tempdata['templog_table'] = $table_name;
        $tempdata['templog_info'] = json_encode($request_info);
        $tempdata['templog_time'] = time();
        $this->DataControl->insertData('cmb_trans_templog', $tempdata);
    }

    function autoSynchroTrans($day='')
    {

        $keyValue = '';

        $datawhere = " 1 ";

        if($day!==''){
            $datawhere .= " and mapping_date='{$day}'";
        }

        $sql = "select mapping_date,keyvalue,mapping_hasnum 
                from cmb_trans_mapping 
                where {$datawhere} and agency_id='{$this->agencyId}' and mapping_allnum>mapping_hasnum
                order by mapping_date asc
                ";

        $mappingOne = $this->DataControl->selectOne($sql);


        if ($mappingOne) {

            $date = $mappingOne['mapping_date'];
            $keyValue = $mappingOne['keyvalue'];

        } else {

            $sql = "select mapping_date,keyvalue 
                from cmb_trans_mapping 
                where agency_id='{$this->agencyId}'
                order by mapping_date desc
                ";
            $mappingOne = $this->DataControl->selectOne($sql);

            if ($mappingOne) {
                $date = date("Y-m-d", strtotime("+1 day", strtotime($mappingOne['mapping_date'])));
            } else {
                $date = '2022-01-01';
            }
        }

        if ($date >= date("Y-m-d", strtotime("-1 day"))) {
            $this->error = 1;
            $this->errortip = "已完成";
            return false;
        }

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['date'] = $date;
        if ($keyValue && $keyValue != 'N') {
            $data['nextKeyValue'] = $keyValue;
        }
        $returnData = $this->transfer($data, '/pfs/head/queryClassEliminate');

        if ($returnData) {

            if ($returnData['eliminateNum'] > 0) {
                $num = 0;
                foreach ($returnData['eliminateList'] as $eliminateOne) {

                    $oldEliminateOne=$this->DataControl->getFieldOne("cmb_trans_mapping_log", "log_id,transferStatus", "eliminateId='{$eliminateOne['eliminateId']}' and date='{$eliminateOne['date']}'");

                    if($oldEliminateOne){
                        if($oldEliminateOne['transferStatus']!='S'){
                            $data = array();
                            $data['companies_id'] = $this->companies_id;
                            $data['agency_id'] = $this->agencyId;
                            $data['date'] = $eliminateOne['date'];
                            $data['classId'] = $eliminateOne['classId'];
                            $data['eliminateAmt'] = $eliminateOne['eliminateAmt'];
                            $data['orderNo'] = $eliminateOne['orderNo'];
                            $data['transferStatus'] = $eliminateOne['transferStatus'];
                            $data['stuId'] = $eliminateOne['stuId'];
                            $data['eliminateId'] = $eliminateOne['eliminateId'];
                            $data['transferId'] = $eliminateOne['transferId'];
                            $data['consumeHours'] = $eliminateOne['consumeHours'];
                            $data['id'] = $eliminateOne['id'];

                            $this->DataControl->updateData("cmb_trans_mapping_log", "eliminateId='{$eliminateOne['eliminateId']}' and date='{$eliminateOne['date']}'", $data);
                        }

                    }else{
                        $data = array();
                        $data['companies_id'] = $this->companies_id;
                        $data['agency_id'] = $this->agencyId;
                        $data['date'] = $eliminateOne['date'];
                        $data['classId'] = $eliminateOne['classId'];
                        $data['eliminateAmt'] = $eliminateOne['eliminateAmt'];
                        $data['orderNo'] = $eliminateOne['orderNo'];
                        $data['transferStatus'] = $eliminateOne['transferStatus'];
                        $data['stuId'] = $eliminateOne['stuId'];
                        $data['eliminateId'] = $eliminateOne['eliminateId'];
                        $data['transferId'] = $eliminateOne['transferId'];
                        $data['consumeHours'] = $eliminateOne['consumeHours'];
                        $data['id'] = $eliminateOne['id'];
                        $this->DataControl->insertData("cmb_trans_mapping_log", $data);
                    }

                    $num++;
                }

                if ($this->DataControl->getFieldOne("cmb_trans_mapping", "mapping_id", "agency_id='{$this->agencyId}' and mapping_date='{$date}'")) {

                    $data = array();
                    $data['mapping_allnum'] = $returnData['eliminateNum'];
                    $data['mapping_hasnum'] = $mappingOne['mapping_hasnum'] > 0 ? ($mappingOne['mapping_hasnum'] + $num) : $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->updateData("cmb_trans_mapping", "agency_id='{$this->agencyId}' and mapping_date='{$date}'", $data);

                } else {
                    $data = array();
                    $data['companies_id'] = $this->companies_id;
                    $data['agency_id'] = $this->agencyId;
                    $data['mapping_date'] = $date;
                    $data['mapping_allnum'] = $returnData['eliminateNum'];
                    $data['mapping_hasnum'] = $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->insertData("cmb_trans_mapping", $data);
                }

            } else {
                $data = array();
                $data['companies_id'] = $this->companies_id;
                $data['agency_id'] = $this->agencyId;
                $data['mapping_date'] = $date;
                $data['mapping_allnum'] = 0;
                $data['mapping_hasnum'] = 0;

                $this->DataControl->insertData("cmb_trans_mapping", $data);

            }

        } else {
            $data = array();
            $data['companies_id'] = $this->companies_id;
            $data['agency_id'] = $this->agencyId;
            $data['mapping_date'] = $date;
            $data['mapping_allnum'] = 0;
            $data['mapping_hasnum'] = 0;

            $this->DataControl->insertData("cmb_trans_mapping", $data);

        }
        return 1;

    }

    function autoHandleErrorInfo($logOne,$day='')
    {

        if($day!==''){
            $date=$day;
        }else{
            $requestArray = json_decode($logOne['log_requet_json'], 1);

            if ($requestArray) {
                $date = $requestArray['date'];
            } else {
                return $logOne['log_time'];
            }

        }

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['date'] = $date;

        $returnData = $this->transfer($data, '/pfs/head/queryClassEliminate');

        if ($returnData) {

            if ($returnData['eliminateNum'] > 0) {
                $num = 0;
                foreach ($returnData['eliminateList'] as $eliminateOne) {

                    $oldEliminateOne=$this->DataControl->getFieldOne("cmb_trans_mapping_log", "log_id,transferStatus", "eliminateId='{$eliminateOne['eliminateId']}' and date='{$eliminateOne['date']}'");

                    if($oldEliminateOne){
                        if($oldEliminateOne['transferStatus']!='S'){
                            $data = array();
                            $data['companies_id'] = $this->companies_id;
                            $data['agency_id'] = $this->agencyId;
                            $data['date'] = $eliminateOne['date'];
                            $data['classId'] = $eliminateOne['classId'];
                            $data['eliminateAmt'] = $eliminateOne['eliminateAmt'];
                            $data['orderNo'] = $eliminateOne['orderNo'];
                            $data['transferStatus'] = $eliminateOne['transferStatus'];
                            $data['stuId'] = $eliminateOne['stuId'];
                            $data['eliminateId'] = $eliminateOne['eliminateId'];
                            $data['transferId'] = $eliminateOne['transferId'];
                            $data['consumeHours'] = $eliminateOne['consumeHours'];
                            $data['id'] = $eliminateOne['id'];

                            $this->DataControl->updateData("cmb_trans_mapping_log", "eliminateId='{$eliminateOne['eliminateId']}' and date='{$eliminateOne['date']}'", $data);
                        }

                    }else{
                        $data = array();
                        $data['companies_id'] = $this->companies_id;
                        $data['agency_id'] = $this->agencyId;
                        $data['date'] = $eliminateOne['date'];
                        $data['classId'] = $eliminateOne['classId'];
                        $data['eliminateAmt'] = $eliminateOne['eliminateAmt'];
                        $data['orderNo'] = $eliminateOne['orderNo'];
                        $data['transferStatus'] = $eliminateOne['transferStatus'];
                        $data['stuId'] = $eliminateOne['stuId'];
                        $data['eliminateId'] = $eliminateOne['eliminateId'];
                        $data['transferId'] = $eliminateOne['transferId'];
                        $data['consumeHours'] = $eliminateOne['consumeHours'];
                        $data['id'] = $eliminateOne['id'];
                        $this->DataControl->insertData("cmb_trans_mapping_log", $data);
                    }

                    $num++;
                }

                if ($this->DataControl->getFieldOne("cmb_trans_mapping", "mapping_id", "agency_id='{$this->agencyId}' and mapping_date='{$date}'")) {

                    $data = array();
                    $data['mapping_allnum'] = $returnData['eliminateNum'];
                    $data['mapping_hasnum'] = $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->updateData("cmb_trans_mapping", "agency_id='{$this->agencyId}' and mapping_date='{$date}'", $data);

                } else {
                    $data = array();
                    $data['companies_id'] = $this->companies_id;
                    $data['agency_id'] = $this->agencyId;
                    $data['mapping_date'] = $date;
                    $data['mapping_allnum'] = $returnData['eliminateNum'];
                    $data['mapping_hasnum'] = $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->insertData("cmb_trans_mapping", $data);
                }

                if($data['mapping_hasnum']<$data['mapping_allnum']){

                    $num=ceil($data['mapping_allnum']/500);

                    for ($i = 0; $i <= $num; $i++) {
                        $this->autoSynchroTrans($date);
                    }
                }



            } else {
                $data = array();
                $data['companies_id'] = $this->companies_id;
                $data['agency_id'] = $this->agencyId;
                $data['mapping_date'] = $date;
                $data['mapping_allnum'] = 0;
                $data['mapping_hasnum'] = 0;

                $this->DataControl->insertData("cmb_trans_mapping", $data);

            }

        } else {
            $data = array();
            $data['companies_id'] = $this->companies_id;
            $data['agency_id'] = $this->agencyId;
            $data['mapping_date'] = $date;
            $data['mapping_allnum'] = 0;
            $data['mapping_hasnum'] = 0;

            $this->DataControl->insertData("cmb_trans_mapping", $data);

        }

        return $logOne['log_time'];
    }

    function updatePlanTransInfo(){


//        if(date("w")==2 || date("w")==1){
//            $lastdate='2024-01-01';
//            $enddate=date("Y-m-d");
//        }elseif(date("w")==3 || date("w")==4 || date("w")==5){
//            $lastdate='2023-01-01';
//            $enddate='2024-01-01';
//        }

        $lastdate='2024-03-01';
        $enddate=date("Y-m-d");

        $sql = "select ty.companies_id,ty.companies_cnname,rank,sum(transferAmt) as total_price,income_date 
                from 
                    (
                    select ta.companies_id,ta.income_date,ta.transferAmt
                    ,@rownum:=@rownum+1 
                    ,if(@pdept=ta.companies_id,@rank:=@rank+1,@rank:=1) as rank
                    ,@pdept:=ta.companies_id
                    from 
                    (SELECT
                     x.companies_id,
                     x.classId,
                     sum( x.income_price - x.reduce_price ) AS transferAmt,
                     x.income_date,
                     x.class_id,
                     sum( x.income_times ) AS eliminateClassHour 
                    FROM
                     (
                     SELECT
                        B.class_branch AS classId,
                        A.income_price,
                        date_sub(A.income_date, INTERVAL WEEKDAY(A.income_date) - 6 DAY ) as income_date,
                        B.class_id,
                        A.income_times,
                        A.companies_id,
                        ifnull((SELECT x.reduce_price 
                         FROM cmb_trans_transfer_reduce AS x 
                         WHERE x.order_pid = A.order_pid AND x.course_id = A.course_id AND x.hour_lessontimes = D.hour_lessontimes 
                         ),0) AS reduce_price 
                     FROM
                        cmb_trans_transfer AS A,
                        smc_class AS B,
                        smc_student_hourstudy AS C,
                        smc_class_hour AS D,
                        cmb_trans_order as E,cmb_trans_class as F 
                     WHERE A.class_id = B.class_id 
                        AND C.hourstudy_id = A.hourstudy_id 
                        AND C.hour_id = D.hour_id 
                        and A.order_pid=E.order_pid and F.class_branch=B.class_branch and A.agency_id='{$this->agencyId}'
                        AND A.transfer_status = 1 AND F.is_trans_now=0 AND F.update_status=1
                        AND A.income_isconfirm = 1 
                        AND A.order_pid <> '' 
                        AND A.is_confirm = 1 
                        and E.is_confirm=1 
                        and E.order_status=1 and A.income_date>='{$lastdate}' and A.income_date<'{$enddate}'
                     ) AS x 
                    GROUP BY x.companies_id,
                     x.income_date
                     order by x.companies_id,transferAmt desc) ta,
                     (select @rownum :=0 , @pdept := null ,@rank:=0) R  
                     where 1 
                     order by ta.companies_id,tA.transferAmt desc 
                     ) tx 
                 ,gmc_code_companies ty 
                 where tx.companies_id=ty.companies_id
                 group by tx.companies_id,rank 
                 HAVING rank=1
                 order by tx.companies_id,rank
                ";

        $planOne=$this->DataControl->selectClear($sql);

        if($planOne){
            $this->updateDayTransInfo($planOne['income_date'],$this->agencyId);
        }

        return true;

    }

    function autoRecordOrder()
    {

        $keyValue = '';

        $sql = "select a.class_id,a.class_branch,b.keyvalue,b.mapping_allnum,b.mapping_hasnum 
                from cmb_trans_class as a
                left join cmb_trans_order_mapping as b on a.class_id=b.class_id
                where a.agency_id='{$this->agencyId}' and a.is_confirm=1 and a.update_status=1
                and not exists(select 1 from cmb_trans_order_mapping as x where x.class_id=a.class_id and x.mapping_allnum=x.mapping_hasnum)";

        $mappingOne = $this->DataControl->selectOne($sql);

        if (!$mappingOne) {
            $this->error = 1;
            $this->errortip = "已完成";
            return false;
        }

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['classId'] = $mappingOne['class_branch'];
        if ($mappingOne['keyvalue'] && $mappingOne['keyvalue'] != 'N') {
            $data['nextKeyValue'] = $keyValue;
        }

        $returnData = $this->transfer($data, '/pfs/head/queryStudentOrder');

        if ($returnData) {

            if ($returnData['orderNum'] > 0) {
                $num = 0;
                foreach ($returnData['orderList'] as $orderOne) {

                    $data = array();
                    $data['date'] = $orderOne['date'];
                    $data['classId'] = $orderOne['classId'];
                    $data['stuId'] = $orderOne['stuId'];
                    $data['orderNo'] = $orderOne['orderNo'];
                    $data['classHour'] = $orderOne['classHour'];
                    $data['orderAmt'] = $orderOne['orderAmt'];
                    $data['feeAmt'] = $orderOne['feeAmt'];
                    $data['tranType'] = $orderOne['tranType'];

                    if ($this->DataControl->getFieldOne("cmb_trans_order_mapping_log", "log_id", "orderNo='{$orderOne['orderNo']}'")) {

                        $this->DataControl->updateData("cmb_trans_order_mapping_log", "orderNo='{$orderOne['orderNo']}'", $data);
                    } else {
                        $this->DataControl->insertData("cmb_trans_order_mapping_log", $data);
                    }
                    $num++;
                }

                if ($this->DataControl->getFieldOne("cmb_trans_order_mapping", "mapping_id", "agency_id='{$this->agencyId}' and class_id='{$mappingOne['class_id']}'")) {

                    $data = array();
                    $data['mapping_allnum'] = $returnData['orderNum'];
                    $data['mapping_hasnum'] = $mappingOne['mapping_hasnum'] > 0 ? ($mappingOne['mapping_hasnum'] + $num) : $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->updateData("cmb_trans_order_mapping", "agency_id='{$this->agencyId}' and class_id='{$mappingOne['class_id']}'", $data);

                } else {
                    $data = array();
                    $data['companies_id'] = $this->companies_id;
                    $data['agency_id'] = $this->agencyId;
                    $data['class_id'] = $mappingOne['class_id'];
                    $data['mapping_allnum'] = $returnData['orderNum'];
                    $data['mapping_hasnum'] = $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->insertData("cmb_trans_order_mapping", $data);
                }

            } else {
                $data = array();
                $data['companies_id'] = $this->companies_id;
                $data['agency_id'] = $this->agencyId;
                $data['class_id'] = $mappingOne['class_id'];
                $data['mapping_allnum'] = 0;
                $data['mapping_hasnum'] = 0;

                $this->DataControl->insertData("cmb_trans_order_mapping", $data);

            }

        } else {
            $data = array();
            $data['companies_id'] = $this->companies_id;
            $data['agency_id'] = $this->agencyId;
            $data['class_id'] = $mappingOne['class_id'];
            $data['mapping_allnum'] = 0;
            $data['mapping_hasnum'] = 0;

            $this->DataControl->insertData("cmb_trans_order_mapping", $data);

        }

        return 1;

    }


    function autoHandleOrderErrorInfo($logOne)
    {

        $requestArray = json_decode($logOne['log_requet_json'], 1);

        if (!$requestArray) {
            return $logOne['log_time'];
        }

        $classOne = $this->DataControl->getFieldOne("cmb_trans_class", "class_id", "class_branch='{$requestArray['classId']}'");

        if (!$classOne) {
            return $logOne['log_time'];
        }

        $data = array();
        $data['agencyId'] = $requestArray['agencyId'];
        $data['classId'] = $requestArray['classId'];

        $returnData = $this->transfer($data, '/pfs/head/queryStudentOrder');


        if ($returnData) {

            if ($returnData['orderNum'] > 0) {
                $num = 0;
                foreach ($returnData['orderList'] as $orderOne) {

                    $data = array();
                    $data['date'] = $orderOne['date'];
                    $data['classId'] = $orderOne['classId'];
                    $data['stuId'] = $orderOne['stuId'];
                    $data['orderNo'] = $orderOne['orderNo'];
                    $data['classHour'] = $orderOne['classHour'];
                    $data['orderAmt'] = $orderOne['orderAmt'];
                    $data['feeAmt'] = $orderOne['feeAmt'];
                    $data['tranType'] = $orderOne['tranType'];

                    if ($this->DataControl->getFieldOne("cmb_trans_order_mapping_log", "log_id", "orderNo='{$orderOne['orderNo']}'")) {

                        $this->DataControl->updateData("cmb_trans_order_mapping_log", "orderNo='{$orderOne['orderNo']}'", $data);
                    } else {
                        $this->DataControl->insertData("cmb_trans_order_mapping_log", $data);
                    }
                    $num++;
                }

                if ($this->DataControl->getFieldOne("cmb_trans_order_mapping", "mapping_id", "agency_id='{$this->agencyId}' and class_id='{$classOne['class_id']}'")) {

                    $data = array();
                    $data['mapping_allnum'] = $returnData['orderNum'];
                    $data['mapping_hasnum'] = $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->updateData("cmb_trans_order_mapping", "agency_id='{$this->agencyId}' and class_id='{$classOne['class_id']}'", $data);

                } else {
                    $data = array();
                    $data['companies_id'] = $this->companies_id;
                    $data['agency_id'] = $this->agencyId;
                    $data['class_id'] = $classOne['class_id'];
                    $data['mapping_allnum'] = $returnData['orderNum'];
                    $data['mapping_hasnum'] = $num;
                    $data['keyvalue'] = $returnData['hasNext'] == 'N' ? 'N' : $returnData['nextKeyValue'];

                    $this->DataControl->insertData("cmb_trans_order_mapping", $data);
                }

            } else {
                $data = array();
                $data['companies_id'] = $this->companies_id;
                $data['agency_id'] = $this->agencyId;
                $data['class_id'] = $classOne['class_id'];
                $data['mapping_allnum'] = 0;
                $data['mapping_hasnum'] = 0;

                $this->DataControl->insertData("cmb_trans_order_mapping", $data);

            }

        } else {
            $data = array();
            $data['companies_id'] = $this->companies_id;
            $data['agency_id'] = $this->agencyId;
            $data['class_id'] = $classOne['class_id'];
            $data['mapping_allnum'] = 0;
            $data['mapping_hasnum'] = 0;

            $this->DataControl->insertData("cmb_trans_order_mapping", $data);

        }

        return $logOne['log_time'];
    }


    function getLastSubmitResult()
    {
        $logOne = $this->DataControl->getFieldOne("cmb_trans_log", "batch_pid", "agency_id='{$this->agencyId}' and log_url='/pfs/head/addBatchTransfer' order by log_id desc");

        $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies", "companies_cnname", "companies_agencyid='{$this->agencyId}'");
        $tem_data = array();
        $tem_data['agencyId'] = $this->agencyId;
        $tem_data['companies_cnname'] = $companiesOne['companies_cnname'];
        if ($logOne) {
            $data = array();
            $data['agencyId'] = $this->agencyId;
            $data['transferId'] = $logOne['batch_pid'];
            $returnData = $this->transfer($data, '/pfs/head/queryTransferBill');

            if (!$returnData) {
                $data['tip'] = $this->errortip;
            } else {
                $data['tip'] = '已审核';
            }
        } else {
            $data['tip'] = '可划拨';
        }

        return $data;
    }


    function getClassInfo($classId)
    {
        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['classIdList'] = array('0' => $classId);

        $returnData = $this->transfer($data, '/pfs/head/queryClassBalance');
        debug($returnData);
        exit;
        return $returnData;
    }


    //更新班级至招行
    function synchroClassInfo()
    {
        $sql = "select A.class_branch as classId
                ,B.class_cnname as className
                ,A.class_stdate as beginDate
                ,A.class_enddate as endDate
                ,(select count(ch.hour_id) from cmb_trans_lesson as ch where ch.class_id=A.class_id and ch.hour_status = 1) as lessons
                ,'A' as trainingForms
                ,'01' as classType
                ,A.batch_pid
                from cmb_trans_class as A,smc_class as B 
                where A.class_id=b.class_id and A.update_status=0 and A.agency_id='{$this->agencyId}' limit 0,10";

        $classList = $this->DataControl->selectClear($sql);
        $num = 0;
        if ($classList) {
            $data = array();
            $data['agencyId'] = $this->agencyId;
            $data['classNum'] = count($classList);
            $data['classList'] = $classList;
            $batch_pid = $this->create_guid();

            $returnData = $this->transfer($data, '/pfs/head/addBatchClass', $batch_pid);

            if ($returnData) {
                foreach ($returnData as $returnOne) {
                    $data = array();
                    $data['batch_pid'] = $batch_pid;
                    $data['is_confirm'] = 1;
                    if ($returnOne['success']) {
                        $data['update_status'] = 1;
                        $num++;
                    } else {
                        $data['update_status'] = 0;
                    }

                    $data['updatetime'] = time();
                    $this->DataControl->updateData("cmb_trans_class", "class_branch='{$returnOne['classId']}' and agency_id='{$this->agencyId}'", $data);
                }

                return $num;

            } else {

                return false;
            }

        } else {
            $this->error = 1;
            $this->errortip = "无需监控班级";
            return false;
        }
    }


    //机构注册方法
    function addAgency($request)
    {
        $data = array();
        $data['mallId'] = $request['mallId'];
        $data['agencyName'] = $request['agencyName'];
        $data['uscc'] = $request['uscc'];
        $data['establishTime'] = $request['establishTime'];
        $data['detailedAddress'] = $request['detailedAddress'];
        $data['province'] = $request['province'];
        $data['city'] = $request['city'];
        $data['region'] = $request['region'];
        $data['adminName'] = $request['adminName'];
        $data['adminTelNo'] = $request['adminTelNo'];
        $data['agencyType'] = '2';

        $returnData = $this->transfer($data, '/pfs/head/addAgency');
        if ($returnData) {
            return $returnData;
        }
    }


    //公共入参
    function transfer($data, $url, $batch_pid = '')
    {
        $this->encrypt(json_encode($data, JSON_UNESCAPED_UNICODE));

        $d = array();
        $d['t'] = $this->tstr;
        $d['data'] = $this->xssting;
        $d['agencyId'] = $this->cmbbranch == '' ? $this->agencyId : $this->cmbbranch;//平台编号
        $d['signature'] = $this->signature;
        $return = request_by_curl($this->zs_url . $url, json_encode($d), "post");
        $bool = json_decode($return, 1);
        if ($bool['code'] == '200') {
            $this->code = '200';
            $decrypt = $this->decrypt($bool['data']);
            $this->recordLog($batch_pid, json_encode($data, JSON_UNESCAPED_UNICODE), json_encode($decrypt, JSON_UNESCAPED_UNICODE), $url, 1);
            return $decrypt;
        } else {
            $this->recordLog($batch_pid, json_encode($data, JSON_UNESCAPED_UNICODE), $return, $url, 0);
            $this->error = 1;
            $this->errortip = $bool['message'];
            $this->code = $bool['code'];
            return false;
        }
    }


    //接口日志
    function recordLog($batch_pid, $requet_json, $back_json, $url = '', $type = 0)
    {
        $data = array();
        $data['company_id'] = $this->company_id;
        $data['companies_id'] = $this->companies_id;
        $data['batch_pid'] = $batch_pid;
        $data['agency_id'] = $this->agencyId;
        $data['log_requet_json'] = $requet_json;//入参
        $data['log_back_json'] = $back_json;//出参
        $data['log_type'] = $type;
        $data['log_url'] = $url;
        $data['log_time'] = time();
        $this->DataControl->insertData("cmb_trans_log", $data);
    }

    function autoRecordClassTransInfo()
    {

        $sql = "select a.class_id,a.class_branch
                from cmb_trans_class as a
                where a.agency_id='{$this->agencyId}' and a.is_confirm=1 and a.update_status=1
                and not exists(select 1 from cmb_trans_class_trans as x where x.classId=a.class_branch)
                limit 0,100
                ";

//        $sql = "select a.batch_pid
//                from cmb_trans_log as a
//                where a.agency_id='{$this->agencyId}' AND a.log_transerStatus=0 and a.log_url='/pfs/head/addClassIncrement'
//                order by a.log_id ASC
//                limit 0,3";
//
//        $logList=$this->DataControl->selectClear($sql);
//
//        $datawhere=" a.log_time>'1719128282' ";
//
//        if($logList){
//            $datawhere.= " AND a.batch_pid_all in ('".implode("','",array_column($logList,'batch_pid'))."')";
//        }
//
//        $datawhere.=" and b.class_enddate>='2023-01-01'";
//
//        $sql = "select b.class_id,b.class_branch
//                from cmb_trans_order as a
//                inner join cmb_trans_class as b on b.class_id=a.class_id
//                where {$datawhere} and a.agency_id='{$this->agencyId}'
//                group by a.class_id
//                ";

        $classList = $this->DataControl->selectClear($sql);

        if (!$classList) {
            $this->error = 1;
            $this->errortip = "无需更新班级";
            return false;
        }

        $num = 0;

        $data = array();
        $data['agencyId'] = $this->agencyId;
        $data['classIdList'] = array_column($classList, 'class_branch');

        $returnData = $this->transfer($data, '/pfs/head/queryClassBalance');

        if ($returnData && $returnData['classList']) {

            foreach ($returnData['classList'] as $classOne) {

                $data = array();
                $data['classId'] = $classOne['classId'];
                $data['transferredAmt'] = $classOne['transferredAmt'];
                $data['surplusAmt'] = $classOne['surplusAmt'];
                $data['totalAmt'] = $classOne['totalAmt'];
                $data['transferredTimes'] = $classOne['transferredTimes'];
                $data['totalEnrollClassHour'] = $classOne['totalEnrollClassHour'];
                $data['totalDropCLassHour'] = $classOne['totalDropCLassHour'];
                $data['totalEliminateHour'] = $classOne['totalEliminateHour'];
                $data['createtime'] = time();

                if($this->DataControl->getFieldOne("cmb_trans_class_trans","classId","classId='{$classOne['classId']}'")){
                    $this->DataControl->updateData("cmb_trans_class_trans","classId='{$classOne['classId']}'", $data);
                }else{
                    $this->DataControl->insertData("cmb_trans_class_trans", $data);
                }

                $num++;
            }

            return $num;
        } else {
            return 0;
        }
    }

    function reduceClassBalance($companies_id){

        $num=0;
        $sql = "select a.class_id,b.surplusAmt,a.class_enddate
                from cmb_trans_class as a 
                inner join cmb_trans_class_trans as b on b.classId=a.class_branch
                where a.agency_id='{$this->agencyId}' and a.class_enddate<'2023-01-01' and b.surplusAmt>0 and a.is_handle=0
                limit 0,5
                ";
        $classList=$this->DataControl->selectClear($sql);

        if(!$classList){
            $this->error = 1;
            $this->errortip = "无班级";
            return false;
        }
        foreach($classList as $classOne){
            $data=array();
            $data['is_handle']=1;
            $this->DataControl->updateData("cmb_trans_class","class_id='{$classOne['class_id']}'",$data);
            $classAllPrice=$classOne['surplusAmt'];

            $sql = "select a.*,ifnull((select sum(x.eliminateAmt) from cmb_trans_mapping_log as x where x.orderNo=a.order_pid and x.transferStatus='S'),0) as transPrice
                    ,ifnull((select count(x.log_id) from cmb_trans_mapping_log as x where x.orderNo=a.order_pid and x.transferStatus='S'),0) as transNum
                    from cmb_trans_order as a 
                    where a.class_id='{$classOne['class_id']}' and a.is_confirm=1 and a.order_status=1 and a.order_status_all=1 and a.order_type='P' and a.order_amt>0
                    and not exists(select 1 from cmb_trans_order as x where x.from_order_pid=a.order_pid)
                    having order_amt>(transPrice/100) 
                    ";
            $orderList=$this->DataControl->selectClear($sql);

            if(!$orderList){
                continue;
            }

            foreach($orderList as $orderOne){

                $orderPrice=$orderOne['order_amt']*100-$orderOne['transPrice'];

                if($classAllPrice<$orderPrice){
                    $orderPrice=$classAllPrice;
                }


                $data=array();
                $data['company_id']=$orderOne['company_id'];
                $data['companies_id']=$orderOne['companies_id'];
                $data['agency_id']=$orderOne['agency_id'];
                $data['school_id']=$orderOne['school_id'];
                $data['coursetype_id']=$orderOne['coursetype_id'];
                $data['coursecat_id']=$orderOne['coursecat_id'];
                $data['course_id']=$orderOne['course_id'];
                $data['class_id']=$orderOne['class_id'];
                $data['student_id']=$orderOne['student_id'];
                $data['parent_mobile']=$orderOne['parent_mobile'];
                $data['order_type']='R';
                $data['from_order_pid']=$orderOne['order_pid'];
                $data['order_pid']='JZ'.substr($orderOne['order_pid'],2);
                $data['order_amt']=$orderPrice/100;
                $data['order_fee']=0;
                $data['order_num']=($orderOne['order_num']-$orderOne['transNum'])>0?($orderOne['order_num']-$orderOne['transNum']):0;
                $data['order_date']=$classOne['class_enddate'];
                $data['settle_date']=$classOne['class_enddate'];
                $data['is_confirm']=1;

                $this->DataControl->insertData("cmb_trans_order",$data);
                $num++;
                $classAllPrice-=$orderPrice;

                if($classAllPrice<=0){
                    break;
                }
            }
        }

        return $num;


    }


}