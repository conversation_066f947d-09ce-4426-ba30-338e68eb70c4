<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 15:21
 */

namespace Model\Api;

class HrmWarnTempModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function getWaitAttendanceNum($request){
        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql="select ch.hour_id 
              from smc_class_hour as ch,smc_class as cl,smc_school as sc,smc_course as co,smc_code_coursetype as cc 
              where {$datawhere} and ch.class_id=cl.class_id and cl.school_id=sc.school_id and cl.course_id=co.course_id and cc.coursetype_id=co.coursetype_id
              and sc.company_id='{$request['company_id']}'
              and ch.hour_day<=CURDATE() and ch.hour_ischecking=0 and cl.class_status>='0' and cc.coursetype_isopenclass=0 and cl.class_type=0
              ";

        $hourList=$this->DataControl->selectClear($sql);

        return $hourList?count($hourList):0;

    }

    function getArrearsNum($request){

        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql = "select po.order_id,po.order_paymentprice,po.order_paidprice
                  ,(select sum(ssc.coursebalance_figure)
                  from smc_payfee_order_course as spoc
                  left join smc_student_coursebalance as ssc on ssc.course_id=spoc.course_id
                  where spoc.order_pid=po.order_pid and ssc.student_id=po.student_id and ssc.coursebalance_status<>3 and ssc.school_id=po.school_id
                  ) as coursebalance_figure
				  from smc_payfee_order as po
                  left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid
                  left join smc_student as s on s.student_id=po.student_id
                  left join smc_school as sc on sc.school_id=po.school_id
                  where {$datawhere} and po.company_id='{$request['company_id']}' and (po.order_status between 1 and 3)
                  GROUP BY po.order_pid
                  HAVING coursebalance_figure<=(po.order_paymentprice-po.order_paidprice)
                  order by po.order_createtime desc";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;

    }

    function getWillArrearsNum($request){
        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql = "select po.order_id,po.order_paymentprice,po.order_paidprice
                  ,(select sum(ssc.coursebalance_figure) as coursebalance_figure
                  from smc_payfee_order_course as spoc
                  left join smc_student_coursebalance as ssc on ssc.course_id=spoc.course_id
                  where spoc.order_pid=po.order_pid and ssc.student_id=po.student_id and ssc.coursebalance_status<>3 and ssc.school_id=po.school_id
                  ) as coursebalance_figure
				  from smc_payfee_order as po
                  left join smc_payfee_order_course as poc on poc.order_pid=po.order_pid
                  left join smc_student as s on s.student_id=po.student_id
                  left join smc_school as sc on sc.school_id=po.school_id
                  where {$datawhere} and po.company_id='{$request['company_id']}' and (po.order_status between 1 and 3)
                  GROUP BY po.order_pid
                  HAVING (coursebalance_figure>(po.order_paymentprice-po.order_paidprice) or coursebalance_figure=null)
                  order by po.order_createtime desc";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getCouponWillApprovedNum($request){

        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql="select A.apply_id 
              from smc_student_coupons_apply as A,smc_school as sc
              where {$datawhere} and a.school_id=sc.school_id and A.company_id='{$request['company_id']}'  and A.apply_status=0
              
              ";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getRefundOrderWillApprovedNum($request){
        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql="select A.refund_id 
              from smc_refund_order as A,smc_school as sc
              where {$datawhere} and A.school_id=sc.school_id and A.company_id='{$request['company_id']}'  and A.refund_status=0
              
              ";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getStayInClassNum($request){
        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and E.school_branch='{$request['school_branch']}'";
        }
        $sql = "SELECT E.school_id 
            ,D.coursecat_id 
            ,C.student_id 
            FROM smc_student_coursebalance A  
            LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id 
            LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id 
            LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id 
            LEFT JOIN smc_school E ON A.school_id=E.school_id AND A.company_id=E.company_id
            LEFT JOIN smc_code_coursetype F ON F.coursetype_id=D.coursetype_id AND F.company_id=D.company_id 
            WHERE {$datawhere} 
            AND A.company_id='{$request['company_id']}' 
            AND B.course_inclasstype in (0,1) 
            AND A.coursebalance_time>0 
            -- AND A.coursebalance_status=0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z 
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id 
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id  
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id  
            AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1  
            AND X.study_endday>=CURDATE()) 
            GROUP BY E.school_id,C.student_id,D.coursecat_id 
            ORDER BY E.school_id,D.coursecat_id,C.student_id 
              ";

        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getBreakClassWillApprovedNum($request){
        $datawhere=" 1 ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql="select A.breakoff_id 
              from smc_class_breakoff as A,smc_school as sc
              where {$datawhere} and A.school_id=sc.school_id and A.company_id='{$request['company_id']}'  and A.breakoff_status=0
              
              ";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getStudentCourseWarningNum($request){
        $datawhere=" 1 and cs.company_id ='{$request['company_id']}' ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sch.school_branch='{$request['school_branch']}'";
        }

        $sql = "select s.student_id
            from smc_student_coursebalance as sc
            LEFT JOIN smc_student_study AS ss ON ss.student_id = sc.student_id
            LEFT JOIN smc_class AS cs ON cs.class_id = ss.class_id and cs.course_id=sc.course_id
            left join smc_school as sch on sch.school_id=sc.school_id
            LEFT JOIN smc_course AS co ON co.course_id = cs.course_id
            LEFT JOIN smc_student AS s ON s.student_id = sc.student_id
            LEFT JOIN smc_code_coursetype AS cc ON co.coursetype_id = cc.coursetype_id
            where {$datawhere} and ss.study_isreading = 1 and co.course_warningnums > 0  and  co.course_inclasstype = 0 and sc.coursebalance_time <=co.course_warningnums";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;

    }

    function getClassUpWarningNum($request){
        $datawhere=" 1 and c.company_id ='{$request['company_id']}' ";
        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql = "select c.class_id,ce.course_warningnums
              ,(select count(ch.hour_id) from smc_class_hour as ch WHERE ch.course_id = c.course_id and ch.class_id = c.class_id and ch.hour_ischecking = 0 limit 0,1) as hour_num
              from smc_class as c
              left join smc_course as ce ON ce.course_id = c.course_id
              left join smc_school as sc on sc.school_id=c.class_id
              where {$datawhere} and c.class_status=1  and ce.course_nextid > 0 and ce.course_inclasstype =0
              and EXISTS(select 1 from smc_class_hour as ch WHERE ch.class_id = c.class_id)
              having hour_num <= course_warningnums";

        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getStuConsumeNum($request){

        $endqueryday = date("Y-m-d");
        if (isset($request['endtime']) && $request['endtime'] !== '') {
            $endqueryday = $request['endtime'];
        }
        $endqueryTimes = strtotime($endqueryday) + (3600 * 24);

        $datawhere = "1";
        $datahaving = " 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql="select al.alert_time,al.alert_status from smc_school_arrears_alert as al,smc_school as sc where al.school_id=sc.school_id and sc.company_id='{$request['company_id']}' and sc.school_branch='{$request['school_branch']}'";

        $schoolOne = $this->DataControl->selectOne($sql);
        if ($schoolOne && $schoolOne['alert_status'] == 1) {
            $datahaving .= " and coursebalance_time<='{$schoolOne['alert_time']}'";
        } else {
            $datahaving .= " and coursebalance_time<hour_nochecknum";
        }

        $sql = "select ss.student_id,
				(select count(ch.hour_id) from  smc_class_hour as ch  where ch.class_id = ss.class_id AND ch.hour_isfree = '0' and ch.hour_ischecking='0' and ch.hour_iswarming='0' and  ch.hour_day >='{$endqueryday}') as hour_nochecknum,
				(select IF(timelog_finaltimes is null,0,timelog_finaltimes)  from smc_student_coursebalance_timelog  as sc where ss.student_id = sc.student_id and sc.timelog_time < '{$endqueryTimes}' and c.course_id  = sc.course_id and sc.school_id='{$request['school_id']}' Order by sc.timelog_id desc limit 0,1) as coursebalance_time
				FROM  smc_student_study  as ss
	 			inner JOIN smc_class AS c  ON c.class_id = ss.class_id
				inner JOIN smc_student AS s ON s.student_id = ss.student_id
				inner JOIN smc_course as co on co.course_id=c.course_id
                inner join smc_school as sc on sc.school_id=c.school_id
				where {$datawhere} and ss.company_id = '{$request['company_id']}' and co.course_inclasstype=0 and c.class_type=0
				and c.class_stdate <='{$endqueryday}' and ss.study_isreading=1 AND c.class_enddate >='{$endqueryday}' AND ss.study_endday >='{$endqueryday}'
				Having {$datahaving}
				";
        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;

    }

    function getLostWarningNum($request){

        $datawhere=" 1 and a.company_id ='{$request['company_id']}' ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql = "select a.school_id
				,a.student_id
				,d.coursetype_id
				,sum(coursebalance_figure) as left_amount
				,sum(coursebalance_time) as left_times
				,FROM_UNIXTIME((select max(log_time) from smc_student_coursebalance_log x,smc_course y 
					where x.course_id=y.course_id and x.student_id=b.student_id and x.school_id=b.school_id and y.coursetype_id=d.coursetype_id),'%Y-%m-%d') as last_log_time
				,FROM_UNIXTIME((select max(timelog_time) from smc_student_coursebalance_timelog x,smc_course y 
					where x.course_id=y.course_id and x.student_id=b.student_id and x.school_id=b.school_id and y.coursetype_id=d.coursetype_id),'%Y-%m-%d') as last_timelog_time
				from smc_student_coursebalance a
				left join smc_student_enrolled b on a.student_id=b.student_id and a.school_id=b.school_id
				left join smc_course c on a.course_id=c.course_id
				left join smc_school as sc on sc.school_id=a.school_id    
				left join smc_code_coursetype d on c.coursetype_id=d.coursetype_id
				left join smc_student e on a.student_id=e.student_id
				left join smc_student_balance f on b.school_id=f.school_id and b.student_id=f.student_id
				where {$datawhere}
				and a.company_id = '{$request['company_id']}'
				and b.enrolled_status in(0,1)
				and d.coursetype_isregistercalc=1
				group by a.student_id,d.coursetype_id
				having left_amount=0 
				and left_times=0 
				and not exists(select 1 from smc_student_changelog x where x.student_id=a.student_id and x.school_id=a.school_id and x.stuchange_code='C04' 
				and coursetype_id=d.coursetype_id and changelog_day>=last_log_time and changelog_day>=last_timelog_time)
				order by e.student_branch,d.coursetype_branch ";

        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }

    function getlostChangeNum($request){
        $datawhere=" 1 and A.company_id ='{$request['company_id']}' ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }

        $sql = "select A.changelog_id
                from smc_student_changelog A
                left join smc_student B on B.student_id=A.student_id
                left join smc_code_stuchange C on C.stuchange_code=A.stuchange_code
                left join smc_code_coursetype D on D.coursetype_id=A.coursetype_id
                left join smc_student_enrolled F on F.school_id=a.school_id and F.student_id=a.student_id
				left join smc_school as sc on sc.school_id=A.school_id    
                WHERE {$datawhere} 
                AND NOT(A.stuchange_code IN('C02','C04') AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and stuchange_code in('A01','A06','D02','F01')))
                and not(A.stuchange_code='C04' AND exists(select 1 from smc_student_changelog where change_pid>a.change_pid and student_id=a.student_id and school_id=a.school_id and coursetype_id=A.coursetype_id and stuchange_code ='D04'))";

        $List=$this->DataControl->selectClear($sql);

        return $List?count($List):0;
    }




}