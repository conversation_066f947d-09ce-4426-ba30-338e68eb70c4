<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/27
 * Time: 17:28
 */

namespace Model\Api;
use FG\ASN1\ASNObject;
use Rtgm\sm\RtSm2;
require_once BASEDIR."/Core/Tools/Mdanter/Random/random.php";
require_once BASEDIR . "/Core/Tools/autoload.php";


class HeadboingPayModel extends \Model\modelTpl{
    public $u;
    public $t;
    public $c;
//    public $apiUlr = "https://api.cmburl.cn:8065/polypay/v1.0/";//测试地址
    public $apiUlr = "https://api.cmbchina.com/polypay/v1.0/";//正式地址
    public $error = false;
    public $errortip = false;
    public $oktip = false;
    public $companyOne = false;
    public $payfeeOrderOne = false;
    public $BoingPay;
    protected $config;

    function __construct($company_id ='0') {
        parent::__construct ();
        if($company_id !== '0'){
            $this->verdictcompany($company_id);
        }

        $this->BoingPayStart();
    }

    function BoingPayStart(){
        //商户名：上海吉的堡教育软件开发有限公司
        //商户号：3089991727300KL
        //收银员：V063557725
        //微信侧sid：*********
        //支付宝侧sid：2088520341588556、2088520343533835

        //测试环境报文头MD5加签秘钥（生产环境在聚合收款服务平台获取）：
        //测试环境APP ID：8ab74856-8772-45c9-96db-54cb30ab9f74
        //测试环境APP SECRET：5b96f20a-011f-4254-8be8-9a5ceb2f317f


        //测试环境报文体SM2加签秘钥（生产环境在聚合收款服务平台设置和获取）：
        //商户私钥（商户端联调测试加签使用）：
        //D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38

        //招行公钥（商户端联调测试验签使用）：
        //MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==

        //秘钥案例：
        //私钥：D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38
        //SM2标准公钥头：3059301306072A8648CE3D020106082A811CCF5501822D03420004

        //SM2裸公钥：
        //X：E90F9F92DB2763D3853FE2E9491E5475BC5FE731C214ED0F98E2A514D4F10C81
        //Y：A5F23B0F6DB07FF444F6DCD57E69C4B3E05124CC3EF8B16DA288D54744B88A1E

        //SM2 ASN1格式标准公钥：
        //3059301306072A8648CE3D020106082A811CCF5501822D03420004E90F9F92DB2763D3853FE2E9491E5475BC5FE731C214ED0F98E2A514D4F10C81A5F23B0F6DB07FF444F6DCD57E69C4B3E05124CC3EF8B16DA288D54744B88A1E

        //BASE64公钥：
        //MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==

        $config = array();
        $config['use_sandbox'] = true;
        $config['userId'] = '1234567812345678';//国密用户ID
        //$config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE1xPq3B3Cw2U+t+R7Fb0JCJvy87/LDbUDFilGjkQU89VLl57pbUPLKUwP2jnAyOEKmJS9USsz+VwXNd4/bjdIFA==';// 招行公钥
        $config['publicHead'] = '3059301306072A8648CE3D020106082A811CCF5501822D03420004';// 招行公钥头
        $config['notify_url'] = 'https://scshopapi.kedingdang.com/HeadBoingPay/OrderBak';//异步回调地址

//        $config['mer_id'] = '3089991727300KL';// 商户 ID
//        $config['user_id'] = 'V063557725';// 收银员 ID
//        $config['publicKey'] = 'MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6Q+fktsnY9OFP+LpSR5Udbxf5zHCFO0PmOKlFNTxDIGl8jsPbbB/9ET23NV+acSz4FEkzD74sW2iiNVHRLiKHg==';// 招行验签公钥
//        $config['privateKey'] = 'D5F2AFA24E6BA9071B54A8C9AD735F9A1DE9C4657FA386C09B592694BC118B38';// 商户私钥
//        $config['appid'] = '8ab74856-8772-45c9-96db-54cb30ab9f74';
//        $config['secret'] = '5b96f20a-011f-4254-8be8-9a5ceb2f317f';
        $this->config = $config;
    }

    function verdictcompany($company_id){
        $this->companyOne = $this->DataControl->getFieldOne("gmc_company","company_id,company_cnname,company_boningcode","company_id = '{$company_id}'");
        if(!$this->companyOne){
            $this->error = true;
            $this->errortip = "员工信息不存在";
        }
    }

    //支付调用入口
    function OrderPay($pay_pid,$paymenttype,$openid='')
    {
        if($paymenttype == 'ewmpay'){
            return $this->Ewmpay($pay_pid);
        }elseif($paymenttype == 'bsaocpay'){
            return $this->Bsaocpay($pay_pid,$openid);
        }elseif($paymenttype == 'wxpay'){
            $PayResult = $this->WxPay($pay_pid,$openid);
            ajax_return($PayResult['result']);
        }
    }

    //收款码支付入口逻辑处理
    function Ewmpay($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");

        $sql = "SELECT c.companies_cmbheadappid, s.companies_headstorenumber AS school_cmbshopcode,s.conpanies_headuserid,se.companies_appid,se.companies_secret,se.companies_publicKey,se.companies_privateKey
                FROM gmc_code_companies AS c,smc_school_companies AS s,gmc_code_companies_seting as se 
                WHERE s.companies_id = c.companies_id and c.companies_id=se.companies_id AND s.school_id = '{$orderOne['school_id']}'
                AND c.companies_id = '{$orderPay['companies_id']}'";

        $storeOne = $this->DataControl->selectOne($sql);

        if(!$storeOne || $storeOne['companies_cmbheadappid'] == '' || $storeOne['school_cmbshopcode'] == '' || $storeOne['conpanies_headuserid'] == '' || $storeOne['companies_appid'] == '' || $storeOne['companies_secret'] == '' || $storeOne['companies_publicKey'] == '' || $storeOne['companies_privateKey'] == ''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
        $this->config['mer_id'] = $storeOne['companies_cmbheadappid'];
        $this->config['user_id'] = $storeOne['conpanies_headuserid'];//门店收费人ID
        $this->config['publicKey'] = $storeOne['companies_publicKey'];//招行验签公钥
        $this->config['privateKey'] = $storeOne['companies_privateKey'];//商户私钥
        $this->config['appid'] = $storeOne['companies_appid'];//appid
        $this->config['secret'] = $storeOne['companies_secret'];//秘钥

        if($orderPay['pay_order_no'] != '' && $orderPay['pay_outpidjson'] != ''){
            header('Content-Type:image/png');
            $outpidarray = json_decode($orderPay['pay_outpidjson'], true);
            $codeUrl = $outpidarray['qrCode'];
            require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
            $QRcode = new \QRcode();
            $errorCorrectionLevel = 'H';//容错级别
            $matrixPointSize = 8;//生成图片大小
            echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            exit;
        }

        if($orderPay['pay_issuccess'] == '0'){
            $orderInfo = [
                'orderId'    => $orderPay['pay_pid'],
                'payValidTime'=> 3600*24*30,
                'txnAmt' => $orderPay['pay_price']*100,//交易金额，单位为分（必传）
                "body"=> "{$this->companyOne['company_cnname']}微商城商品支付,总计需支付{$orderPay['pay_price']}元"
            ];

            $result = $this->qrcodeapply($orderInfo);
            if($result){

//                $result['biz_content']=str_replace("qr.95516.com","payment-uat.cs.cmburl.cn",$result['biz_content']);

                header('Content-Type:image/png');
                $updata = array();
                $updata['pay_order_no'] = json_decode($result['biz_content'],1)['cmbOrderId'];
                $updata['pay_outpidjson'] = $result['biz_content'];
                $this->DataControl->updateData("smc_payfee_order_pay", "pay_pid='{$pay_pid}'", $updata);
                $codeUrl = json_decode($result['biz_content'],1)['qrCode'];
                require(ROOT_PATH . 'Core/Classlib/Webqrcode.php');//二维码生成
                $QRcode = new \QRcode();
                $errorCorrectionLevel = 'H';//容错级别
                $matrixPointSize = 8;//生成图片大小
                echo $QRcode->png($codeUrl, false, $errorCorrectionLevel, $matrixPointSize, 2);
            }else{
                $this->error = true;
                echo "无法生成支付二维码".$this->errortip;
            }
        }
    }

    //微信公众号支付
    function WxPay($pay_pid,$openid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");

        $sql = "SELECT c.companies_cmbheadappid, s.companies_headstorenumber AS school_cmbshopcode,s.conpanies_headuserid,se.companies_appid,se.companies_secret,se.companies_publicKey,se.companies_privateKey
                FROM gmc_code_companies AS c,smc_school_companies AS s,gmc_code_companies_seting as se 
                WHERE s.companies_id = c.companies_id and c.companies_id=se.companies_id AND s.school_id = '{$orderOne['school_id']}'
                AND c.companies_id = '{$orderPay['companies_id']}'";

        $storeOne = $this->DataControl->selectOne($sql);

        if(!$storeOne || $storeOne['companies_cmbheadappid'] == '' || $storeOne['school_cmbshopcode'] == '' || $storeOne['conpanies_headuserid'] == '' || $storeOne['companies_appid'] == '' || $storeOne['companies_secret'] == '' || $storeOne['companies_publicKey'] == '' || $storeOne['companies_privateKey'] == ''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
        $this->config['mer_id'] = $storeOne['companies_cmbheadappid'];
        $this->config['user_id'] = $storeOne['conpanies_headuserid'];//门店收费人ID
        $this->config['publicKey'] = $storeOne['companies_publicKey'];//招行验签公钥
        $this->config['privateKey'] = $storeOne['companies_privateKey'];//商户私钥
        $this->config['appid'] = $storeOne['companies_appid'];//appid
        $this->config['secret'] = $storeOne['companies_secret'];//秘钥

        if($openid == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未获取微信授权，请选择二维码支付！';
            $result = array();
            $result['errortip'] = '未获取微信授权，请选择二维码支付！';
            $bakresult['result'] = $result;
            return $bakresult;
        }

        if($orderPay['pay_issuccess'] == '0') {
            $orderInfo = [
                'deviceInfo'    => "WEB",//公众号
                'subAppId'    => "wx7378534679eeecdf",//子公众账号ID
                'orderId'    => $orderPay['pay_pid'],
                'tradeType'    => "JSAPI",//交易类型 APP支付：APP  公众号支付：JSAPI  小程序支付：JSAPI
                'txnAmt' => $orderPay['pay_price']*100,//交易金额，单位为分（必传）
                "body"=> "{$this->companyOne['company_cnname']}微商城商品支付,总计需支付{$orderPay['pay_price']}元",
                'openId'    => $openid,//公众号微信用户唯一码
                'spbillCreateIp'=> real_ip()
            ];

            $result = $this->onlinepay($orderInfo);

            $bakresult = array();
            $bakresult['error'] = '0';
            $bakresult['errortip'] = '支付信息获取成功！';
            $bakresult['result'] = $result;
            return $bakresult;
        }else{
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '订单已支付请勿重复提交！';
            $result = array();
            $result['errortip'] = '订单已支付请勿重复提交！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
    }

    //支付回调处理
    function BoingPayBak($REQUESTARRAY){

        $eventArray = json_decode($REQUESTARRAY['biz_content'],1);

        $orderPay = $this->DataControl->getFieldOne("smc_payfee_order_pay","pay_id,order_pid,pay_pid,pay_issuccess","pay_pid='{$eventArray['orderId']}'");
        $orderOne = $this->DataControl->getFieldOne("smc_payfee_order","company_id,school_id,order_pid","order_pid='{$orderPay['order_pid']}'");
        $stafferOne = $this->DataControl->getFieldOne("smc_staffer","staffer_id","company_id='{$orderOne['company_id']}' AND account_class = '1'");

        if($orderPay && $orderPay['pay_issuccess'] !== '1'){
            $publiclist = array();
            $publiclist['company_id'] = $orderOne['company_id'];
            $publiclist['school_id'] = $orderOne['school_id'];
            $publiclist['staffer_id'] = $stafferOne['staffer_id'];
            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);

//            $createtime = date("Y-m-d H:i:s",strtotime($eventArray['txnTime']));
            $createtime = date("Y-m-d H:i:s",strtotime($eventArray['endDate'].$eventArray['endTime']));

            if($eventArray['payType'] == 'ZF'){
                $paytype_code = 'alipay';
            }elseif($eventArray['payType'] == 'WX'){
                $paytype_code = 'wechat';
            }elseif($eventArray['payType'] == 'YL'){
                $paytype_code = 'yinlian';
            }elseif($eventArray['payType'] == 'EC'){
                $paytype_code = 'dcep';
            }else{
                $paytype_code='';
            }

            $bakjson=addslashes($REQUESTARRAY['biz_content']);

            $orderPayModel->orderPaylog( $orderPay['pay_pid'],$eventArray['thirdOrderId'],0,'',$paytype_code,$createtime,'0',$bakjson,'','hcmb');
        }
        exit(1);

    }

    //取消或者失效的订单 对招行进行撤销操作
    function OrderPayCancel($pay_pid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");

        $sql = "SELECT c.companies_cmbheadappid, s.companies_headstorenumber AS school_cmbshopcode,s.conpanies_headuserid,se.companies_appid,se.companies_secret,se.companies_publicKey,se.companies_privateKey
                FROM gmc_code_companies AS c,smc_school_companies AS s,gmc_code_companies_seting as se 
                WHERE s.companies_id = c.companies_id and c.companies_id=se.companies_id AND s.school_id = '{$orderOne['school_id']}'
                AND c.companies_id = '{$orderPay['companies_id']}'";

        $storeOne = $this->DataControl->selectOne($sql);

        if(!$storeOne || $storeOne['companies_cmbheadappid'] == '' || $storeOne['school_cmbshopcode'] == '' || $storeOne['conpanies_headuserid'] == '' || $storeOne['companies_appid'] == '' || $storeOne['companies_secret'] == '' || $storeOne['companies_publicKey'] == '' || $storeOne['companies_privateKey'] == ''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
        $this->config['mer_id'] = $storeOne['companies_cmbheadappid'];
        $this->config['user_id'] = $storeOne['conpanies_headuserid'];//门店收费人ID
        $this->config['publicKey'] = $storeOne['companies_publicKey'];//招行验签公钥
        $this->config['privateKey'] = $storeOne['companies_privateKey'];//商户私钥
        $this->config['appid'] = $storeOne['companies_appid'];//appid
        $this->config['secret'] = $storeOne['companies_secret'];//秘钥

        //继续补充

        if($orderPay['pay_issuccess'] == '-1' && $orderPay['pay_order_no'] != ''){

            $orderInfo = [
                'origOrderId'    => $pay_pid,//原交易商户订单号
                'origCmbOrderId'    => $orderPay['pay_order_no']//原交易商户订单号
            ];

            $result = $this->closeOrder($orderInfo);

            if($result['respCode']=='SUCCESS'){
                $bakresult = array();
                $bakresult['error'] = '0';
                $bakresult['errortip'] = '对应订单撤销成功！';
                $bakresult['result'] = $result;
                return $bakresult;
            }else{
                $this->error = false;
                $bakresult = array();
                $bakresult['error'] = '1';
                $bakresult['errortip'] = '对应订单撤销失败！';
                $result = array();
                $result['errortip'] = '对应订单撤销失败！';
                $bakresult['result'] = $result;
                return $bakresult;
            }
        }
    }

    //退款入口  --   API修改
    function RefundView($pay_pid,$refund_pid){
        $payOrder = $this->DataControl->selectOne("SELECT p.pay_pid,p.pay_price,l.paylog_bakjson
FROM smc_payfee_order as o,smc_payfee_order_pay AS p,smc_payfee_order_paylog AS l
WHERE o.order_pid = p.order_pid AND p.pay_pid = l.pay_pid AND p.pay_pid = '{$pay_pid}' limit 0,1");

        $bakjson = str_replace('"[',"[",$payOrder['paylog_bakjson']);
        $bakjson = str_replace(']"',"]",$bakjson);
        $payhsOrder = json_decode($bakjson,1);

        $orderInfo = [
            'orderId'    => $refund_pid,//退款订单号
            'origCmbOrderId'    => $payhsOrder['orderId'],//退款订单号
            'origOrderId'    => $payOrder['pay_pid'],//原交易商户订单号
            'txnAmt'    => $payOrder['pay_price']*100,//原交易金额
            'refundAmt'    => $payOrder['pay_price'] * 100,//退款金额
            'refundReason'    => "吉的堡API退款，手工唤起API退款流程！"//退款原因
        ];
        $this->config['mer_id'] = $payhsOrder['merId'];
        // $this->config['user_id'] = $payhsOrder['school_cmbshopcode'];//操作员ID
        $result = $this->refund($orderInfo);
        if ($result["respCode"] == "SUCCESS") {
            return array('status'=>1,'message'=>'退款成功');  //退款成功
        } elseif ($result["respCode"] == "FAIL") {
            var_dump($this->RefundQueryView($pay_pid,$refund_pid));
        }
    }

    //退款入口--未曾修改
    function RefundQueryView($pay_pid,$refund_pid){
        $payOrder = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $companiesOne = $this->DataControl->getFieldOne("gmc_code_companies","companies_cmbappid","companies_id='{$payOrder['companies_id']}'");
        $paylogOrder = $this->DataControl->getOne("smc_payfee_order_paylog","pay_pid='{$pay_pid}'");
        $bakjson = str_replace('"[',"[",$paylogOrder['paylog_bakjson']);
        $bakjson = str_replace(']"',"]",$bakjson);
        $payhsOrder = json_decode($bakjson,1);

        $orderInfo = [
            'orderId'    => $refund_pid//退款订单号
        ];

        $this->config['mer_id'] = $payhsOrder['merId'];
        // $this->config['user_id'] = $payhsOrder['school_cmbshopcode'];//操作员ID
        $result = $this->refundquery($orderInfo);
        if ($result["respCode"] == "SUCCESS") {
            return $result;
        } elseif ($result["respCode"] == "FAIL") {

        }
    }


    /**B扫C触发支付
     *$openid 是扫码枪获得用户的支付编号
     **/
    function Bsaocpay($pay_pid,$openid){
        $orderPay = $this->DataControl->getOne("smc_payfee_order_pay","pay_pid='{$pay_pid}'");
        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$orderPay['order_pid']}'");

        $sql = "SELECT c.companies_cmbheadappid, s.companies_headstorenumber AS school_cmbshopcode,s.conpanies_headuserid,se.companies_appid,se.companies_secret,se.companies_publicKey,se.companies_privateKey
                FROM gmc_code_companies AS c,smc_school_companies AS s,gmc_code_companies_seting as se 
                WHERE s.companies_id = c.companies_id and c.companies_id=se.companies_id AND s.school_id = '{$orderOne['school_id']}'
                AND c.companies_id = '{$orderPay['companies_id']}'";

        $storeOne = $this->DataControl->selectOne($sql);

        if(!$storeOne || $storeOne['companies_cmbheadappid'] == '' || $storeOne['school_cmbshopcode'] == '' || $storeOne['conpanies_headuserid'] == '' || $storeOne['companies_appid'] == '' || $storeOne['companies_secret'] == '' || $storeOne['companies_publicKey'] == '' || $storeOne['companies_privateKey'] == ''){
            $this->error = true;
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $result = array();
            $result['errortip'] = '未设置支付总行APPID或适配主体对应门店号！';
            $bakresult['result'] = $result;
            return $bakresult;
        }
        $this->config['mer_id'] = $storeOne['companies_cmbheadappid'];
        $this->config['user_id'] = $storeOne['conpanies_headuserid'];//门店收费人ID
        $this->config['publicKey'] = $storeOne['companies_publicKey'];//招行验签公钥
        $this->config['privateKey'] = $storeOne['companies_privateKey'];//商户私钥
        $this->config['appid'] = $storeOne['companies_appid'];//appid
        $this->config['secret'] = $storeOne['companies_secret'];//秘钥

        if(!isset($openid) || trim($openid) == ''){
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '请传入用户收款码编号！';
            return $bakresult;
        }

        if($orderPay['pay_issuccess'] == '0') {
            $orderInfo = [
                'orderId'    => $orderPay['pay_pid'],
                'authCode'    => $openid,//授权码?
                'txnAmt' => $orderPay['pay_price']*100,//交易金额，单位为分（必传）
                "body"=> "{$this->companyOne['company_cnname']}微商城商品支付,总计需支付{$orderPay['pay_price']}元"
            ];

            $result = $this->pay($orderInfo);
            if($result){
                //用户已支付
                if($result['respCode'] == 'SUCCESS'){

                    $contentArray = json_decode($result['biz_content'],1);

                    if ($contentArray["tradeState"] == "S") {
                        $stafferOne = $this->DataControl->getFieldOne("smc_staffer", "staffer_id", "company_id='{$orderOne['company_id']}' AND account_class = '1'");
                        if ($orderPay && $orderPay['pay_issuccess'] !=1) {
                            $publiclist = array();
                            $publiclist['company_id'] = $orderOne['company_id'];//门店编号
                            $publiclist['school_id'] = $orderOne['school_id'];//门店编号
                            $publiclist['staffer_id'] = $stafferOne['staffer_id'];//操作员ID
                            $orderPayModel = new \Model\Smc\OrderPayModel($publiclist, $orderOne['order_pid']);
//                            $createtime = date("Y-m-d H:i:s",strtotime($contentArray['txnTime']));
                            $createtime = date("Y-m-d H:i:s",strtotime($contentArray['endDate'].$contentArray['endTime']));

                            if($contentArray['payType'] == 'ZF'){
                                $paytype_code = 'alipay';
                            }elseif($contentArray['payType'] == 'WX'){
                                $paytype_code = 'wechat';
                            }elseif($contentArray['payType'] == 'YL'){
                                $paytype_code = 'yinlian';
                            }elseif($contentArray['payType'] == 'EC'){
                                $paytype_code = 'dcep';
                            }

                            $bakjson = addslashes($result["biz_content"]);

                            $orderPayModel->orderPaylog($orderPay['pay_pid'], $contentArray['thirdOrderId'], 0, '', $paytype_code, $createtime, '0', $bakjson, '', 'hcmb');
                        }
                    }elseif ($contentArray["tradeState"] == "P") {
                        // echo '<br>支付状态未知（可能需要用户输入支付密码）';
                    }elseif ($contentArray["tradeState"] == "F") {
                        // echo '<br>支付失败';
                    }
                } elseif ($result["respCode"] == "FAIL") {
                    // echo '<br>支付中，请调用查询接口确认结果';
                } elseif ($result["respCode"] == "SYSTERM_ERROR") {
                    // echo '<br>表示本次请求处理异常，并非交易结果是失败状态，需要商户再次发起查询确认';
                }
            }else{
                $this->error = true;
                echo "支付信息获取失败".$this->errortip;
            }

            $bakresult = array();
            $bakresult['error'] = '0';
            $bakresult['errortip'] = '支付信息获取成功！';
            $bakresult['result'] = $result;
            return $bakresult;
        }else{
            $bakresult = array();
            $bakresult['error'] = '1';
            $bakresult['errortip'] = '订单已支付请勿重复提交！';
            return $bakresult;
        }
    }

    function url(){
        /*收款码申请：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/qrcodeapply
        支付结果查询：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/orderquery
        退款申请：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/refund
        退款结果查询：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/refundquery
        付款码收款：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/pay
        微信统一下单：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/onlinepay
        付款码收款撤销：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/cancel
        关闭订单：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/close
        支付宝服务窗支付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/servpay
        支付宝native支付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/zfbqrcode
        微信native支付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/wxqrcode
        对账单下载地址获取：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/statementurl
        秘钥设置：
        https://api.cmburl.cn:8065/polypay/v1.0/mchkey/keyset
        订单二维码申请
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/orderqrcodeapply
        微信小程序下单：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/MiniAppOrderApply
        银联云闪付：
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/cloudpay
        数字人民币统一下单
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/unifiedOrder
        数字人民币统一支付
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/unifiedPayment
        数字人民币子钱包支付
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/subwalletpay
        数字人民币子钱包支付-带合约
        https://api.cmburl.cn:8065/polypay/v1.0/mchorders/ecny/contractsubwalletpay


        因交易结果通知是银行主动通知给商户的，故不提供商户主动调用的URL。

        生产接入地址：
        收款码申请：
        https://api.cmbchina.com/polypay/v1.0/mchorders/qrcodeapply
        支付结果查询：
        https://api.cmbchina.com/polypay/v1.0/mchorders/orderquery
        退款申请：
        https://api.cmbchina.com/polypay/v1.0/mchorders/refund
        退款结果查询：
        https://api.cmbchina.com/polypay/v1.0/mchorders/refundquery
        付款码收款：
        https://api.cmbchina.com/polypay/v1.0/mchorders/pay
        微信统一下单：
        https://api.cmbchina.com/polypay/v1.0/mchorders/onlinepay
        付款码收款撤销：
        https://api.cmbchina.com/polypay/v1.0/mchorders/cancel
        关闭订单：
        https://api.cmbchina.com/polypay/v1.0/mchorders/close
        支付宝服务窗支付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/servpay
        支付宝native支付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/zfbqrcode
        微信native支付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/wxqrcode
        对账单下载地址获取：
        https://api.cmbchina.com/polypay/v1.0/mchorders/statementurl
        秘钥设置：
        https://api.cmbchina.com/polypay/v1.0/mchkey/keyset
        订单二维码申请：
        https://api.cmbchina.com/polypay/v1.0/mchorders/orderqrcodeapply
        微信小程序下单：
        https://api.cmbchina.com/polypay/v1.0/mchorders/MiniAppOrderApply
        银联云闪付：
        https://api.cmbchina.com/polypay/v1.0/mchorders/cloudpay
        数字人民币统一下单
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/unifiedOrder
        数字人民币统一支付
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/unifiedPayment
        数字人民币子钱包支付
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/subwalletpay
        数字人民币子钱包支付-带合约
        https://api.cmbchina.com/polypay/v1.0/mchorders/ecny/contractsubwalletpay*/
    }

    /**招行加密验签测试**/
    function testVery(){
        // 待加密的数据
        $data = json_decode('{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1"}');
        $params =  urldecode(http_build_query($data));
        $params = 'biz_content={"termId":"term0525","orderId":"2018060616435200002","notifyUrl":"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify","merId":"20180524200752LWW","payValidTime":"100000","currencyCode":"156","userId":"N111555974","txnAmt":"134","body":"收款测试0606"}&encoding=UTF-8&signMethod=01&version=0.0.1';
        //生成签名开始
        $sm2    = new RtSm2("base64");
        //将用户id填充到16个字节
        $userId = '1234567812345678';
        //使用rsa的私钥生成签名(注意这里是私钥!私钥!私钥!)
        $sign   = $sm2->doSign($params, $this->config['privateKey'], $userId);
        var_dump($sign);

        $signString =  '{"biz_content":"{\"termId\":\"term0525\",\"orderId\":\"2018060616435200002\",\"notifyUrl\":\"http://wlhost1.paas.cmbchina.cn/mch_notify/api/notify\",\"merId\":\"20180524200752LWW\",\"payValidTime\":\"100000\",\"currencyCode\":\"156\",\"userId\":\"N111555974\",\"txnAmt\":\"134\",\"body\":\"收款测试0606\"}","encoding":"UTF-8","signMethod":"01","version":"0.0.1","sign":"' . $sign . '"}';
        var_dump($signString);

        echo "\n---------明文密钥验签---------------------------\n";
        $publicKey = strtoupper($this->base64Hex($this->config['publicKey']));
        var_dump($publicKey);
        var_dump($this->config['publicHead']);
        $publicKey = str_replace($this->config['publicHead'],"",$publicKey);
        var_dump($publicKey);

        $verify = $sm2->verifySign( $params, $sign, $publicKey, $userId );
        var_dump($verify);
    }

    /**
     * 收款码申请
     *
     **/
    function qrcodeapply($params){
        return $this->handler('mchorders/qrcodeapply', $params);
    }

    /**
     * 支付结果查询
     *
     **/
    function orderquery($params){
        return $this->handler('mchorders/orderquery', $params,0);
    }

    /**
     * 退款申请
     *
     **/
    function refund($params){
        return $this->handler('mchorders/refund', $params,0);
    }

    /**
     * 退款查询
     *
     **/
    function refundquery($params){
        return $this->handler('mchorders/refundquery', $params,0);
    }

    /**
     * 关闭订单
     *
     **/
    function closeOrder($params){
        return $this->handler('mchorders/close', $params,0);
    }

    /**
     * 付款码收款
     *
     **/
    function pay($params){
        return $this->handler('mchorders/pay', $params,0);
    }

    /**
     * 付款码收款撤销
     *
     **/
    function cancelPay($params){
        return $this->handler('mchorders/cancel', $params,0);
    }

    /**
     * 微信统一下单
     *
     **/
    function onlinepay($params){
        return $this->handler('mchorders/onlinepay', $params,0);
    }

    /**
     * 微信小程序下单
     *
     **/
    function MiniAppOrderApply($params){
        return $this->handler('mchorders/MiniAppOrderApply', $params,0);
    }

    /**
     * 对账单下载地址获取
     *
     **/
    function statementurl($params,$companies_id = 0){
        if($companies_id < 1){
            $this->error = true;
            $this->errortip = '主体ID不对';
            return false;
        }
        $sql = "SELECT c.companies_cmbheadappid,se.companies_appid,se.companies_secret,se.companies_publicKey,se.companies_privateKey
                FROM gmc_code_companies AS c,gmc_code_companies_seting as se 
                WHERE c.companies_id = '{$companies_id}' and c.companies_id=se.companies_id ";
        $storeOne = $this->DataControl->selectOne($sql);

        $this->config['mer_id'] = $storeOne['companies_cmbheadappid'];
        $this->config['publicKey'] = $storeOne['companies_publicKey'];//招行验签公钥
        $this->config['privateKey'] = $storeOne['companies_privateKey'];//商户私钥
        $this->config['appid'] = $storeOne['companies_appid'];//appid
        $this->config['secret'] = $storeOne['companies_secret'];//秘钥

        return $this->handlerDzd('mchorders/statementurl', $params,0);
    }

    //入参数据排序处理
    protected function bizContent($params,$has_notifyUrl=1){
//        $bizContent = [
//            'merId' => $this->config['mer_id'],
//            'userId' => $this->config['user_id'],
//            'notifyUrl' => $this->config['notify_url']
//        ];

        if($has_notifyUrl==1){
            $bizContent = [
                'merId' => $this->config['mer_id'],
                'userId' => $this->config['user_id'],
                'notifyUrl' => $this->config['notify_url']
            ];
        }else{
            $bizContent = [
                'merId' => $this->config['mer_id'],
                'userId' => $this->config['user_id'],
            ];
        }


        $params = array_filter(array_merge($bizContent, $params), 'strlen');
        ksort($params);
        return  json_encode($params,JSON_UNESCAPED_UNICODE);
    }


    //公共调用方法
    protected function handler($pageurl, $params,$has_notifyUrl=1){
        $data = [
            'biz_content' => $this->bizContent($params,$has_notifyUrl),
            'encoding'   => 'UTF-8',//编码方式，固定为UTF-8(必传)
            'signMethod' => '02', //签名方法，固定为01，表示签名方式为RSA2(必传)
            'version'    => '0.0.1',//版本号，固定为0.0.1(必传字段)
        ];

        $apidata=array();
        $apidata['apilog_url']=$this->apiUlr.$pageurl;
        $apidata['apilog_requet_json']=addslashes($data['biz_content']);

        $sign = $this->datasign($data);
        $data['sign'] = $sign;
        $result = $this->curl_post($this->apiUlr.$pageurl,$this->headers($sign),$data);

        $apidata['apilog_result']=json_decode($result, true)['returnCode'];
        $apidata['apilog_back_json']=addslashes($result);
        $apidata['apilog_time']=time();
        $this->DataControl->insertData("smc_hcmb_apilog",$apidata);

        if (false !== stripos($result, 'returnCode')) {
            if($apiResult = json_decode($result, true)){
                if($this->verifySign($apiResult)){
                    if($apiResult['returnCode'] == 'SUCCESS'){
                        return $apiResult;
                    }else{
                        $this->error = true;
                        $this->errortip = $apiResult['respMsg'];
                        return false;
                    }
                }else{
                    $this->error = true;
                    $this->errortip = "返回信息验签失败！";
                    return false;
                }
            }else{
                $this->error = true;
                $this->errortip = "返回信息无法解析Json字符，返回值：！".$result;;
                return false;
            }
        }else{
            $this->errortip = "返回信息有误，返回值：！".$result;
            return false;
        }
    }

    //返回值验签
    function verifySign($apiResult){
        $publicKey = strtoupper($this->base64Hex($this->config['publicKey']));
        $publicKey = str_replace($this->config['publicHead'],"",$publicKey);

        //剔除Sign键值
        $sign = $apiResult['sign'];
        unset($apiResult['sign']);
        //var_dump($apiResult);
        ksort($apiResult);
        //var_dump($apiResult);
        $urlparams = http_build_query($apiResult);
        //var_dump($urlparams);
        $params =  urldecode($urlparams);
        //var_dump($params);

        $sm2  = new RtSm2("base64");
        return $sm2->verifySign( $params, $sign, $publicKey, $this->config['userId']);
    }


    //参数加密
    private function datasign($params)
    {
        $sm2  = new RtSm2("base64");
        //$preparams =  json_encode($params,JSON_UNESCAPED_UNICODE);
        //var_dump($preparams);
        $urlparams = http_build_query($params);
        $params =  urldecode($urlparams);
        //var_dump($params);
        //var_dump(stripslashes($params));
        //var_dump($this->config);
        $sign = $sm2->doSign($params, $this->config['privateKey'], $this->config['userId']);
        //print_r($sign);
        return $sign;
    }

    //头部设置
    private function headers($sign)
    {

        $appid = $this->config['appid'];
        $secret = $this->config['secret'];
        $timestamp = time();
        $apisign = md5('appid='.$appid.'&secret='.$secret.'&sign='.$sign.'&timestamp='.$timestamp);
        $data =  [
            'appid'       => $appid,
            'timestamp'   => $timestamp,
            'apisign'     => $apisign
        ];
        return  $data;
    }


    function base64Hex($base64)
    {
        return unpack("H*", base64_decode($base64))[1];
    }

    /**  参数排序拼接
     * @param $array
     * @return string
     */
    function ToUrlParams(array $array){
        $buff = "";
        foreach ($array as $k => $v)
        {
            if($v != "" && !is_array($v)){
                $buff .= $k . "=" . $v . "&";
            }
        }
        $buff = trim($buff, "&");
        return $buff;
    }

    private function curl_post($url,$headers,$data)
    {
        $header= ["Content-type: application/json"];
        foreach ($headers as $key => $value) {
            $header[]=$key.':'. $value;
        }
        $data=json_encode($data,JSON_UNESCAPED_SLASHES);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }

        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }


    //公共调用方法 --- 招行对账单子使用
    protected function handlerDzd($pageurl, $params,$has_notifyUrl=1){
        $data = [
            'biz_content' => $this->bizContentDzd($params,$has_notifyUrl),
            'encoding'   => 'UTF-8',//编码方式，固定为UTF-8(必传)
            'signMethod' => '02', //签名方法，固定为01，表示签名方式为RSA2(必传)
            'version'    => '0.0.1',//版本号，固定为0.0.1(必传字段)
        ];

        $apidata=array();
        $apidata['apilog_url']=$this->apiUlr.$pageurl;
        $apidata['apilog_requet_json']=addslashes($data['biz_content']);

        $sign = $this->datasign($data);
        $data['sign'] = $sign;
        $result = $this->curl_post($this->apiUlr.$pageurl,$this->headers($sign),$data);

        $apidata['apilog_result']=json_decode($result, true)['returnCode'];
        $apidata['apilog_back_json']=addslashes($result);
        $apidata['apilog_time']=time();
//        $this->DataControl->insertData("smc_hcmb_apilog",$apidata);

        if (false !== stripos($result, 'returnCode')) {
            if($apiResult = json_decode($result, true)){
                $aa = $this->verifySign($apiResult);
                if($aa){
                    if($apiResult['returnCode'] == 'SUCCESS'){
                        return $apiResult;
                    }else{
                        $this->error = true;
                        $this->errortip = $apiResult['respMsg'];
                        return false;
                    }
                }else{
                    $this->error = true;
                    $this->errortip = "返回信息验签失败！";
                    return false;
                }
            }else{
                $this->error = true;
                $this->errortip = "返回信息无法解析Json字符，返回值：！".$result;;
                return false;
            }
        }else{
            $this->errortip = "返回信息有误，返回值：！".$result;
            return false;
        }
    }
    //入参数据排序处理
    protected function bizContentDzd($params,$has_notifyUrl=1){
        if($has_notifyUrl==1){
            $bizContent = [
                'merId' => $this->config['mer_id'],
                'notifyUrl' => $this->config['notify_url']
            ];
        }else{
            $bizContent = [
                'merId' => $this->config['mer_id']
            ];
        }

        $params = array_filter(array_merge($bizContent, $params), 'strlen');
        ksort($params);
        return  json_encode($params,JSON_UNESCAPED_UNICODE);
    }
}