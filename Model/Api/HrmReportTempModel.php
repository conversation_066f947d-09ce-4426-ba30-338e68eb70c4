<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/27
 * Time: 15:21
 */

namespace Model\Api;

class HrmReportTempModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }


    function numberReport($request){

        $today = date("Y-m-d");
        $datawhere=" 1 ";

        if(isset($request['school_branch']) && $request['school_branch']!=''){
            $datawhere.=" and sc.school_branch='{$request['school_branch']}'";
        }else{
            return false;
        }

        if(isset($request['coursetype_branch']) && $request['coursetype_branch']!=''){
            $datawhere.=" and cc.coursetype_branch='{$request['coursetype_branch']}'";
        }

        $timewhere=" 1 ";

        if(isset($request['starttime']) && $request['starttime']!=''){
            $timewhere.=" and scl.changelog_day>='{$request['starttime']}'";
        }else{
            $starttime= date("Y-m-d", strtotime("-1 month"));
            $timewhere.=" and scl.changelog_day>='{$starttime}'";
        }

        if(isset($request['endtime']) && $request['endtime']!=''){
            $timewhere.=" and scl.changelog_day<='{$request['endtime']}'";
        }else{
            $timewhere.=" and scl.changelog_day<=CURDATE()";
        }

        $sql = "SELECT COUNT(DISTINCT a.student_id) as count
                FROM smc_student_coursebalance AS a
                LEFT JOIN smc_course b ON a.course_id = b.course_id
                LEFT JOIN smc_code_coursetype cc ON cc.coursetype_id = b.coursetype_id
                left join smc_student as st on st.student_id=a.student_id
                left join smc_school as sc on sc.school_id=a.school_id
                WHERE {$datawhere} and a.coursebalance_figure > 0";

        $residenceStudentList = $this->DataControl->selectOne($sql);
        $residenceNum = $residenceStudentList['count'] ? $residenceStudentList['count'] : 0;

        $sql = "SELECT COUNT(DISTINCT A.student_id) as count
                FROM smc_student_study A
                LEFT JOIN smc_class B ON A.class_id=B.class_id 
                LEFT JOIN smc_course C ON B.course_id=C.course_id 
                LEFT JOIN smc_code_coursetype cc ON C.coursetype_id=cc.coursetype_id 
                LEFT JOIN smc_school as sc on sc.school_id=A.school_id
                WHERE {$datawhere}
                AND B.class_type='0' AND B.class_status>'-2'
                AND A.study_endday >= DATE_SUB('{$today}', INTERVAL cc.coursetype_intervaltime DAY) 
                AND A.study_beginday<='{$today}' and A.study_endday>='{$today}'
            ";

        $studyStudentList = $this->DataControl->selectOne($sql);
        $studyNum = $studyStudentList['count'] ? $studyStudentList['count'] : 0;

        $sql="select count(distinct s.student_id) as count
              from smc_student_changelog as scl
              left join smc_student_change as sca on sca.change_pid=scl.change_pid
              left join smc_code_stuchange_reason as csr on csr.reason_code=sca.reason_code and csr.company_id='{$request['company_id']}'
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_class as c on c.class_id=scl.class_id
              left join smc_course as cou on cou.course_id=c.course_id
              left join smc_school as sc on scl.school_id=sc.school_id
              LEFT JOIN smc_code_coursetype cc ON cc.coursetype_id = cou.coursetype_id
              where {$datawhere} and scl.stuchange_code='A07' and scl.company_id='{$request['company_id']}'
              and not exists(select 1 from smc_student_study X,smc_class Y,smc_course Z where X.class_id=Y.class_id AND Y.company_id=Z.company_id
							AND Y.course_id=Z.course_id AND Z.coursetype_id=cou.coursetype_id AND X.student_id=scl.student_id AND X.school_id=scl.school_id
							AND X.study_endday>scl.changelog_day)";

        $extendedList=$this->DataControl->selectOne($sql);
        $extendedNum = $extendedList['count'] ? $extendedList['count'] : 0;

        $sql = "SELECT COUNT(DISTINCT C.student_id) as count
            FROM smc_student_coursebalance A
            LEFT JOIN smc_course B ON A.course_id=B.course_id AND A.company_id=B.company_id
            LEFT JOIN smc_student C ON A.student_id=C.student_id AND A.company_id=C.company_id
            LEFT JOIN smc_code_coursecat D ON B.coursecat_id=D.coursecat_id AND A.company_id=D.company_id
            LEFT JOIN smc_school sc ON A.school_id=sc.school_id AND A.company_id=sc.company_id
            left join smc_code_coursetype as cc on cc.coursetype_id = B.coursetype_id
            WHERE {$datawhere} AND A.company_id='{$request['company_id']}'
            AND B.course_inclasstype in (0,1)
            AND A.coursebalance_time>0
            AND NOT EXISTS(SELECT 1 FROM smc_student_study X,smc_class Y,smc_course Z
            WHERE X.student_id=A.student_id AND X.company_id=A.company_id
            AND X.class_id=Y.class_id AND Y.company_id=A.company_id
            AND Y.course_id=Z.course_id AND Z.company_id=A.company_id
            AND Z.coursecat_id=B.coursecat_id AND X.study_isreading=1
            AND X.study_endday>=CURDATE())";

        $stayInClassList = $this->DataControl->selectOne($sql);
        $stayInClassNum=$stayInClassList['count'] ? $stayInClassList['count'] : 0;

        $sql = "select count(scl.changelog_id) as count
              from smc_student_changelog as scl
              left join smc_code_coursetype as cc on cc.coursetype_id=scl.coursetype_id
              left join smc_student as s on s.student_id=scl.student_id
              left join smc_staffer as st on st.staffer_id=scl.staffer_id
              left join smc_school as sc on scl.school_id=sc.school_id
              where {$datawhere} and {$timewhere} and scl.company_id='{$request['company_id']}'
              ";

        $lossList=$this->DataControl->selectOne($sql);
        $lossNum=$lossList['count'] ? $lossList['count'] : 0;

        $sql = "select count(cl.class_id) as count
              from smc_class as cl
              left join smc_course as co on co.course_id=cl.course_id
              left join smc_school as sc on cl.school_id=sc.school_id
              left join smc_code_coursetype as cc on cc.coursetype_id=co.coursetype_id
              where {$datawhere} and cl.company_id='{$request['company_id']}' 
              and cl.class_status>='0' and cl.class_stdate<=CURDATE() 
              and (cl.class_enddate>=CURDATE() or cl.class_enddate='')";
        $classList = $this->DataControl->selectOne($sql);
        $classNum = $classList['count'] ? $classList['count'] : 0;

        $starttime=date( 'Y-m-d', strtotime('-1 week') );

        $sql="select count(distinct se.student_id) as count
              from smc_student_registerinfo as se,smc_school as sc,smc_code_coursetype as cc
              where {$datawhere} and se.school_id=sc.school_id and se.coursetype_id=cc.coursetype_id
              and se.company_id='{$request['company_id']}' and se.info_status=1
              and FROM_UNIXTIME(se.pay_successtime, '%Y-%m-%d')>='{$starttime}' and FROM_UNIXTIME(se.pay_successtime, '%Y-%m-%d')<=CURDATE()
              ";
        $weekList=$this->DataControl->selectOne($sql);
        $weekNum=$weekList['count'] ? $weekList['count'] : 0;

        $starttime=date( 'Y-m-d', strtotime('-1 month') );

        $sql="select count(distinct se.student_id) as count
              from smc_student_registerinfo as se,smc_school as sc,smc_code_coursetype as cc
              where {$datawhere} and se.school_id=sc.school_id and se.coursetype_id=cc.coursetype_id
              and se.company_id='{$request['company_id']}' and se.info_status=1
              and FROM_UNIXTIME(se.pay_successtime, '%Y-%m-%d')>='{$starttime}' and FROM_UNIXTIME(se.pay_successtime, '%Y-%m-%d')<=CURDATE()
              ";
        $monthList=$this->DataControl->selectOne($sql);
        $monthNum=$monthList['count'] ? $monthList['count'] : 0;

        $starttime=date( 'Y-m-d', strtotime('-3 month') );

        $sql="select count(distinct se.student_id) as count
              from smc_student_registerinfo as se,smc_school as sc,smc_code_coursetype as cc
              where {$datawhere} and se.school_id=sc.school_id and se.coursetype_id=cc.coursetype_id
              and se.company_id='{$request['company_id']}' and se.info_status=1
              and FROM_UNIXTIME(se.pay_successtime, '%Y-%m-%d')>='{$starttime}' and FROM_UNIXTIME(se.pay_successtime, '%Y-%m-%d')<=CURDATE()
              ";
        $quarterList=$this->DataControl->selectOne($sql);
        $quarterNum=$quarterList['count'] ? $quarterList['count'] : 0;

        $data=array();
        $data['str_1']=$residenceNum;
        $data['str_2']=$studyNum;
        $data['str_3']=$extendedNum;
        $data['str_4']=$stayInClassNum;
//        $data['str_5']=5;
        $data['str_6']=$lossNum;
        $data['str_7']=$classNum;
        $data['str_8']=$classNum>0?floor($studyNum/$classNum):'--';
        $data['str_9']=$weekNum;
        $data['str_10']=$monthNum;
        $data['str_11']=$quarterNum;

        $tem_data=array();
        $tem_data[]=$data;

        return $tem_data;

    }







}