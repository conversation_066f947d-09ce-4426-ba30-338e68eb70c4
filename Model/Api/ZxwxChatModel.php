<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/3
 * Time: 15:21
 */

namespace Model\Api;

class ZxwxChatModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $parenterOne = array();
    public $studentOne = array();
    public $wxtoken = array();

    function __construct($parenter_id = 0, $student_id = 0)
    {
        parent::__construct();
        if ($student_id !== '0') {
            $this->verdictstudent($student_id);
        } else {
            $this->error = true;
            $this->errortip = '请传入学员ID';
        }
        if ($parenter_id !== '0') {
            $this->verdictparenter($parenter_id);
        } else {
            $this->error = true;
            $this->errortip = '请传入家长ID';
        }
    }

    //验证学员账户
    function verdictstudent($student_id)
    {
        $this->studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_cnname,student_enname,company_id"
            , "student_id = '{$student_id}'");
        if (!$this->studentOne) {
            $this->error = true;
            $this->errortip = "学员信息不存在";
        }
    }


    //验证家长账户
    function verdictparenter($parenter_id)
    {
        $this->parenterOne = $this->DataControl->getFieldOne("smc_parenter", "parenter_id,parenter_cnname,parenter_enname,parenter_wxtoken", "parenter_id = '{$parenter_id}'");
        $this->wxtoken = $this->DataControl->selectOne("select w.parenter_wxtoken FROM smc_parenter_wxchattoken as w
LEFT JOIN gmc_company_wxchatnumber as n on n.wxchatnumber_id = w.wxchatnumber_id
WHERE n.wxchatnumber_class = '1' and w.parenter_id = '{$parenter_id}' and w.company_id = '{$this->studentOne['company_id']}'");
        if (!$this->parenterOne) {
            $this->error = true;
            $this->errortip = "家长信息不存在";
        }
    }

    //获取微信token
    function getWeixinToken()
    {
        $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
            , "company_id = '{$this->studentOne['company_id']}' AND wxchatnumber_class = '1'");
        if (!$wxchatnumberOne) {
            $wxchatnumberOne = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "wxchatnumber_id,wxchatnumber_appid,wxchatnumber_appsecret"
                , "company_id = '0' AND wxchatnumber_class = '1'");
        }

        $tokenOne = $this->DataControl->getFieldOne("scptc_weixin_token", "token_failuretime,token_string"
            , "token_type = '1' AND wxchatnumber_id = '{$wxchatnumberOne['wxchatnumber_id']}'", "order by token_failuretime DESC limit 0,1");

        if ($tokenOne && $tokenOne['token_failuretime'] > time()) {
            $wxtoken = array();
            $wxtoken['access_token'] = $tokenOne['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            $paramarray = array(
                'appid' => $wxchatnumberOne['wxchatnumber_appid'],
                'secret' => $wxchatnumberOne['wxchatnumber_appsecret'],
                'grant_type' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $dataArray = $json_play->decode($getBakurl, "1");

            $data = array();
            $data['wxchatnumber_id'] = $wxchatnumberOne['wxchatnumber_id'];
            $data['token_type'] = '1';
            $data['token_string'] = $dataArray['access_token'];
            $data['token_failuretime'] = time() + $dataArray['expires_in'];
            $this->DataControl->insertData("scptc_weixin_token", $data);

            return $dataArray;
        }
    }

    function SendWeixinMis($data, $log_type = '')
    {
        $tokenArray = $this->getWeixinToken();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$tokenArray['access_token']}");
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $tmpInfo = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Errno' . curl_error($ch);
        }
        curl_close($ch);
        $json_play = new \Webjson();
        $retueninfo = $json_play->decode($tmpInfo, "1");


        if ($retueninfo['errmsg'] == 'ok') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = $log_type;
            $date['log_status'] = 1;
            $date['log_day'] = date("Y-m-d");
            $date['log_content'] = addslashes($data);
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date, 1);

            return true;
        } else {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = $log_type;
            $date['log_status'] = 0;
            $date['log_errmsg'] = $tmpInfo;
            $date['log_content'] = addslashes($data);
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);
            $this->error = true;
            $this->errortip = '微信接口未走通！';
            return false;
        }
    }


    //学员到校通知测试
    function ToinformTip($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'HourPreTip';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "HourPreTip");
    }


    //学员上课时间前一天16：30推送
    function HourPreTip($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        if ($this->studentOne['company_id'] == '10001') {
            return false;
        }

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '上课提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'HourPreTip';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "HourPreTip");
    }

    //学员上课时间前半小时推送
    function HourDayTip($firstnote, $keyword1, $keyword2, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        if ($this->studentOne['company_id'] == '10001') {
            return false;
        }

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '上课提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'HourDayTip';
            $date['log_day'] = date('Y-m-d');
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "HourDayTip");
    }

    //课程时间调整后即时推送
    function ChangeHour($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        if ($this->studentOne['company_id'] == '10001') {
            return false;
        }

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '课程变动提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'ChangeHour';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "ChangeHour");
    }

    //教师发布作业后即时推送
    function SendHomework($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '作业提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'SendHomework';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "SendHomework");
    }

    //课程取消后即时推送
    function CancelHour($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        if ($this->studentOne['company_id'] == '10001') {
            return false;
        }

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '课程取消通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'CancelHour';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "CancelHour");
    }

    //教师发布作业后24小时内未读的学员即推送（晚上八点后不推送，早上八点再推送）
//    function HomeworkRemind($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url)
//    {
//        $data = '{
//			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
//			 "template_id":"MHPJAmGwHEgqM11UVvMUmFe9P79dHry9nf9tifhT828",
//			 "url":"' . $url . '",
//			 "topcolor":"#FF0000",
//			 "data":{
//					 "first": {
//						 "value":"' . $firstnote . '",
//						 "color":"#173177"
//					 },
//					 "keyword1":{
//						 "value":"' . $keyword1 . '",
//						 "color":"#173177"
//					 },
//					 "keyword2": {
//						 "value":"' . $keyword2 . '",
//						 "color":"#173177"
//					 },
//					 "keyword3": {
//						 "value":"' . $keyword3 . '",
//						 "color":"#173177"
//					 },
//					 "remark":{
//						 "value":"' . $footernote . '",
//						 "color":"#173177"
//					 }
//			 }
//		 }';
//
//        if($this->wxtoken['parenter_wxtoken'] == ''){
//            $date = array();
//            $date['company_id'] = $this->studentOne['company_id'];
//            $date['student_id'] = $this->studentOne['student_id'];
//            $date['parenter_id'] = $this->parenterOne['parenter_id'];
//            $date['log_type'] = 'HomeworkRemind';
//            $date['log_status'] = 0;
//            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
//            $date['log_content'] = $data;
//            $date['log_createtime'] = time();
//            $this->DataControl->insertData("scptc_wxsend_log", $date);
//
//            $this->error = true;
//            $this->errortip = '家长WXtoken为空，未授权！';
//            return false;
//        }
//        return $this->SendWeixinMis($data, "HomeworkRemind");
//    }

    //教师给作业评分后即推送
    function TeScore($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '作业批阅提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'TeScore';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "TeScore");
    }

    //学员账户充值成功后推送
    function StuInvest($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '充值成功通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'StuInvest';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "StuInvest");
    }

    //学员购课缴费成功后推送
    function StuPay($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $keyword5, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "keyword5": {
						 "value":"' . $keyword5 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '学生缴费确认通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'StuPay';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "StuPay");
    }

    //教师给学员上课点评后即推送(课消提醒)
    function TeComment($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '消课完成提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'TeComment';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "TeComment");
    }

    //老师给学生点评以后推送
    function Comment($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '点评完成通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'Comment';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "Comment");
    }

    //教师给学员上课点评后即推送(考勤提醒)
    function TeCommentTwo($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '学员考勤通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'TeCommentTwo';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "TeCommentTwo");
    }

    //教师发布班级通知后即时推送
    function ClassMessage($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '班级通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'ClassMessage';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "ClassMessage");
    }

    //积分兑换成功通知
    function IntegralExchange($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $keyword5, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "keyword5": {
						 "value":"' . $keyword5 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '积分兑换成功通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'IntegralExchange';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "IntegralExchange");
    }

    //积分扣除提醒
    function IntegralDeduct($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '积分扣除提醒' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'IntegralDeduct';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "IntegralDeduct");
    }

    //积分转存成功通知
    function IntegralLayIn($firstnote, $keyword1, $keyword2, $keyword3, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '积分转存成功通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'IntegralLayIn';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "IntegralLayIn");
    }

    //积分兑换取消通知
    function IntegralCancel($firstnote, $keyword1, $keyword2, $keyword3, $keyword4, $keyword5, $footernote, $url, $wxid)
    {
        $data = '{
			 "touser":"' . $this->wxtoken['parenter_wxtoken'] . '",
			 "template_id":"' . $wxid . '",
			 "url":"' . $url . '",
			 "topcolor":"#FF0000",
			 "data":{
					 "first": {
						 "value":"' . $firstnote . '",
						 "color":"#173177"
					 },
					 "keyword1":{
						 "value":"' . $keyword1 . '",
						 "color":"#173177"
					 },
					 "keyword2": {
						 "value":"' . $keyword2 . '",
						 "color":"#173177"
					 },
					 "keyword3": {
						 "value":"' . $keyword3 . '",
						 "color":"#173177"
					 },
					 "keyword4": {
						 "value":"' . $keyword4 . '",
						 "color":"#173177"
					 },
					 "keyword5": {
						 "value":"' . $keyword5 . '",
						 "color":"#173177"
					 },
					 "remark":{
						 "value":"' . $footernote . '",
						 "color":"#173177"
					 }
			 }
		 }';

        $id = $this->DataControl->getFieldOne("gmc_company_wxchatnumber", "company_id", "company_id = '{$this->studentOne['company_id']}' and wxchatnumber_class = '1'");
        if ($id) {
            $cid = $id['company_id'];
        } else {
            $cid = '0';
        }
        $mid = $this->DataControl->getFieldOne("gmc_company_masterplate", "masterplate_id", "company_id = '{$cid}' and masterplate_name = '积分兑换取消通知' and masterplate_class = '1'");
        $status = $this->DataControl->getFieldOne("eas_masterplateapply", "company_id", "company_id = '{$this->studentOne['company_id']}' and masterplate_id = '{$mid['masterplate_id']}'");
        if ($status) {
            return false;
        }

        if ($this->wxtoken['parenter_wxtoken'] == '') {
            $date = array();
            $date['company_id'] = $this->studentOne['company_id'];
            $date['student_id'] = $this->studentOne['student_id'];
            $date['parenter_id'] = $this->parenterOne['parenter_id'];
            $date['log_type'] = 'IntegralCancel';
            $date['log_status'] = 0;
            $date['log_errmsg'] = '家长微信Token为空，无法给此家长推送任何消息！';
            $date['log_content'] = $data;
            $date['log_createtime'] = time();
            $this->DataControl->insertData("scptc_wxsend_log", $date);

            $this->error = true;
            $this->errortip = '家长WXtoken为空，未授权！';
            return false;
        }
        return $this->SendWeixinMis($data, "IntegralCancel");
    }

}