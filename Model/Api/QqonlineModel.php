<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/7/15
 * Time: 10:17
 */

namespace Model\Api;


class QqonlineModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $apiusercode = 'Kedingdang';
    public $apiuserpswd = "123456";
    public $apiproductbranch = "QiquOnline";
    public $apiUrl = "";

    function __construct($Lag='zh') {
        parent::__construct ();
        if($Lag == 'zh'){
            $this->apiUrl = "https://stuapi.kidcastle.cn/";
        }else{
            $this->apiUrl = "https://stuapi.kidcastleapp.tw/";
        }
    }


    function getFrequencyToken(){
        $paramto = array();
        $paramto['apiusercode'] = $this->apiusercode;
        $paramto['apiuserpswd'] = $this->apiuserpswd;
        $paramto['apiproductbranch'] = $this->apiproductbranch;
        $sendApiSting = request_by_curl($this->apiUrl."ThirdPartyApi/getFrequencyToken", dataEncode($paramto), "GET", array());
        if ($sendApiArray = json_decode($sendApiSting, "1")) {
            return $sendApiArray;
        }else{
            $this->errortip = "权限核验:连接超时";
            return false;
        }
    }

    function frequencyLimit($paramArray){
        $paramto = array();
        $paramto['uid'] = $paramArray['uid'];
        $paramto['token'] = $paramArray['token'];
        $paramto['branch'] = $paramArray['branch'];
        $paramto['limitedcode'] = $paramArray['limitedcode'];
        $paramto['course_branch'] = $paramArray['course_branch'];
        $codeApiSting = request_by_curl($this->apiUrl."ThirdPartyApi/frequencyLimit", dataEncode($paramto), "POST", array());
        if ($codeApiArray = json_decode($codeApiSting, "1")){
            if ($codeApiArray['result'] == '1') {
                return true;
            }else{
                $this->errortip = "卡号校验:".$codeApiArray['errortip'];
                return false;
            }
        }else{
            $this->errortip = "卡号校验:连接超时";
            return false;
        }
    }

    function studentBinding($paramArray){
        $paramto = array();
        $paramto['uid'] = $paramArray['uid'];
        $paramto['token'] = $paramArray['token'];
        $paramto['branch'] = $paramArray['branch'];
        $paramto['limitedcode'] = $paramArray['limitedcode'];
        $paramto['student_branch'] = $paramArray['student_branch'];
        $paramto['course_branch'] = $paramArray['course_branch'];
        $paramto['class_branch'] = $paramArray['class_branch'];
        $codeApiSting = request_by_curl($this->apiUrl."ThirdPartyApi/studentBinding", dataEncode($paramto), "POST", array());
        if ($codeApiArray = json_decode($codeApiSting, "1")){
            if ($codeApiArray['error'] == '0') {
                return $codeApiArray['result']['carditem_invalidtime'];
            }else{
                $this->errortip = "卡号绑定:".$codeApiArray['errortip'];
                return false;
            }
        }else{
            $this->errortip = "卡号绑定:连接超时";
            return false;
        }
    }

    function getCardDeadline($maString){
        $paramarray = array(
            'maString' => $maString
        );
        $codeApiSting = request_by_curl($this->apiUrl."Api/getCardDeadline", dataEncode($paramarray), "POST", array());

        if ($codeApiArray = json_decode($codeApiSting, "1")){
            if ($codeApiArray['error'] == '0') {
                return $codeApiArray['result'];
            }else{
                $this->errortip = "卡号获取:".$codeApiArray['result'];
                return false;
            }
        }else{
            $this->errortip = "卡号获取:连接超时";
            return false;
        }
    }



}