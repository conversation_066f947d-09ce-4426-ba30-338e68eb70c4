<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model;

class InvoiceModel extends modelTpl{

    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $company_id = 0;//操作公司
    public $staffer_id = 0;//操作人
    public $publicarray = array();
    public $stafferOne = array();

    function __construct($publicarray = array())
    {
        parent::__construct();
    }


    public function encrypt($str, $key){
        $screct_key = $key;
        //$screct_key = base64_decode($screct_key);
        $str = trim($str);
        $str = $this->addPKCS7Padding($str);
        $iv = mcrypt_create_iv(mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128,MCRYPT_MODE_ECB),MCRYPT_RAND);
        $encrypt_str =  mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $screct_key, $str, MCRYPT_MODE_ECB, $iv);
        return base64_encode($encrypt_str);
    }

    /**
     * 解密方法
     * @param string $str
     * @return string
     */
    public function decrypt($str, $key){
        $screct_key = $key;
        $str = base64_decode($str);
        //$screct_key = base64_decode($screct_key);
        $iv = mcrypt_create_iv(mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128,MCRYPT_MODE_ECB),MCRYPT_RAND);
        $encrypt_str =  mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $screct_key, $str, MCRYPT_MODE_ECB, $iv);
        //$encrypt_str = trim($encrypt_str);
        $encrypt_str = $this->stripPKSC7Padding($encrypt_str);
        return $encrypt_str;
    }

    /**
     * 填充算法
     * @param string $source
     * @return string
     */
    function addPKCS7Padding($source){
        $source = trim($source);
        $block = mcrypt_get_block_size('rijndael-128', 'ecb');
        $pad = $block - (strlen($source) % $block);
        if ($pad <= $block) {
            $char = chr($pad);
            $source .= str_repeat($char, $pad);
        }
        return $source;
    }
    /**
     * 移去填充算法
     * @param string $source
     * @return string
     */
    function stripPKSC7Padding($source){
        $source = trim($source);
        $char = substr($source, -1);
        $num = ord($char);
        if($num==62)return $source;
        $source = substr($source,0,-$num);
        return $source;
    }

    /**
     * 将stdClass Object转换成array格式
     * @param  $array 需要转换的对象
     * @return array
     */
    function object_array($array) {
        if(is_object($array)) {
            $array = (array)$array;
        } if(is_array($array)) {
            foreach($array as $key=>$value) {
                $array[$key] = $this->object_array($value);
            }
        }
        return $array;
    }



    function getSendToTaxXML($appId,$contentPassword,$interfaceCode,$contentData){
        $str = '';
        $str .= "<?xml version='1.0' encoding='UTF-8' ?>";
        $str .= "<interface xmlns=\"\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:schemaLocation=\"http://www.chinatax.gov.cn/tirip/dataspec/interfaces.xsd\" version=\""
            . "DZFPQZ0.2" . "\"> ";
        $str .= "<globalInfo>";
        $str .="<appId>".$appId."</appId><interfaceId></interfaceId>";
        $str .="<interfaceCode>".$interfaceCode."</interfaceCode>"."<requestCode>DZFPQZ</requestCode>";
        $str .="<requestTime>".date("Y-m-d h:i:s")."</requestTime>"."<responseCode>Ds</responseCode>";
        $str .="<dataExchangeId>DZFPQZ".$interfaceCode.date("Y-m-d").rand(*********, *********). "</dataExchangeId>";
        $str .= "</globalInfo>";
        $str .= "<returnStateInfo>";
        $str .= "<returnCode></returnCode>";
        $str .= "<returnMessage></returnMessage>";
        $str .= "</returnStateInfo>";
        $str .= "<Data>";
        $str .= "<dataDescription>";
        $str .= "<zipCode>0</zipCode>";
        $str .= "</dataDescription>";
        $str .= "<content>";
        $content = preg_replace('# #','',base64_encode($contentData));//base64加密请求报文并去空格
        $str .=  $content;
        $str .= "</content>";
        $str .= "<contentKey>";
        $contentKey = $this->encrypt(md5($content), $contentPassword);
        $str .= $contentKey;
        $str .= "</contentKey>";
        $str .= "</Data>";
        $str .= "</interface>";
        return $str;
    }

    /**
     * parserXMLToArray
     * XML Conversion to Arrays
     * @param string $resp
     * @param bool   $format 默认object返回对象，需要返回数组请传入array
     * @return bool|mixed|\SimpleXMLElement
     * <AUTHOR>
     * @DateTime 2018/12/19  9:58
     */
    function parserXMLToArray($resp, $format = 'object')
    {
        /*if(!parserXML($resp)){
            return false;
        }*/
        $disableLibxmlEntityLoader = libxml_disable_entity_loader(true);
        // 如果希望使用多个libxml选项，可以使用管道将它们分开，如下所示
        // 如果不加 LIBXML_NOERROR 选项的话传入错误的xml字符串会抛出错误，加上这个选项会返回 false。或者在函数前面加上 @ 操作符也会返回false
        // 也可以直接调用上面判断是否是一个标准的xml格式
        $respObject                = simplexml_load_string($resp, 'SimpleXMLElement', LIBXML_NOCDATA | LIBXML_NOERROR);
        libxml_disable_entity_loader($disableLibxmlEntityLoader);
        if (false === $respObject) {
            return false;
        }
        if ($format === 'array') {
            return json_decode(json_encode($respObject), true);
        }
        return $respObject;
    }

    function Invoice($param)
    {
        //填写发票详细信息

        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$param['order_pid']}'");
        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$param['invoice_id']}'");
        $companiesOne = $this->DataControl->getOne('gmc_code_companies',"companies_id='{$invoiceOne['companies_id']}'");
//        $refundPrice = $this->DataControl->selectOne("SELECT sum(r.refund_price) as refund FROM ptc_shop_refund as r WHERE r.order_pid = '{$param['order_pid']}'");


        if($companiesOne['companies_invoice_chl'] == '国信发票'){
            $memberMobile = $invoiceOne['invoice_mobile'];
            $rate = $companiesOne['companies_rate']/100;

            $fp = 'JDB'.date('YmdHis').mt_rand(10,99);
            $invoiceAllprice = round(($invoiceOne['invoice_allprice']) / ($rate + 1),2);
            $hejishuie = round($invoiceAllprice * $rate,2);
            $jiashuiheji = round($invoiceAllprice + $hejishuie,2);

            $a = array();
            $a['companies_invoice_ird'] = $companiesOne['companies_invoice_ird'];
            $a['companies_invoice_machineno'] = $companiesOne['companies_cnname'];
            $a['companies_invoice_address'] = $companiesOne['companies_invoice_address'].' '.$companiesOne['companies_invoice_phone'];
            $a['companies_invoice_bankname'] = $companiesOne['companies_invoice_bankname'].' '.$companiesOne['companies_invoice_bankcard'];

            if($rate == '0'){
                $contentData = "<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>



<FPQQLSH>{$fp}</FPQQLSH>
<KPLX>0</KPLX>
<ZSFS>0</ZSFS>
<XSF_NSRSBH>{$companiesOne['companies_invoice_ird']}</XSF_NSRSBH>
<XSF_MC>{$companiesOne['companies_cnname']}</XSF_MC>
<XSF_DZDH>{$companiesOne['companies_invoice_address']} {$companiesOne['companies_invoice_phone']}</XSF_DZDH>
<XSF_YHZH>{$companiesOne['companies_invoice_bankname']} {$companiesOne['companies_invoice_bankcard']}</XSF_YHZH>
<GMF_NSRSBH>{$invoiceOne['invoice_taxpayernum']}</GMF_NSRSBH>
<GMF_MC>{$invoiceOne['invoice_title']}</GMF_MC>
<GMF_DZDH></GMF_DZDH>
<GMF_YHZH></GMF_YHZH>
<GMF_SJH>$memberMobile</GMF_SJH>
<GMF_DZYX>{$invoiceOne['invoice_email']}</GMF_DZYX>
<FPT_ZH></FPT_ZH>
<WX_OPENID></WX_OPENID>
<KPR>{$companiesOne['companies_invoice_drawer']}</KPR>
<SKR>{$companiesOne['companies_invoice_payee']}</SKR>
<FHR>{$companiesOne['companies_invoice_reviewer']}</FHR>
<YFP_DM></YFP_DM>
<YFP_HM></YFP_HM>
<JSHJ>{$jiashuiheji}</JSHJ>
<HJJE>{$invoiceAllprice}</HJJE>
<HJSE>{$hejishuie}</HJSE>
<KCE></KCE>
<BZ>商户订单号：{$orderOne['order_pid']}</BZ>
<HYLX>行业类型</HYLX>
<BY1>备用字段1</BY1><BY2>备用字段2</BY2><BY3>备用字段3</BY3>
<BY4>备用字段4</BY4><BY5>备用字段5</BY5><BY6>备用字段6</BY6>
<BY7>备用字段7</BY7><BY8>备用字段8</BY8><BY9>备用字段9</BY9>
<BY10>备用字段10</BY10>
<COMMON_FPKJ_XMXXS class='COMMON_FPKJ_XMXX' size='1'>
<COMMON_FPKJ_XMXX>
<FPHXZ>0</FPHXZ>
<SPBM>3070201020000000000</SPBM>
<ZXBM></ZXBM>
<YHZCBS>1</YHZCBS>
<LSLBS>1</LSLBS>
<ZZSTSGL>免税</ZZSTSGL>
<XMMC>培训费</XMMC>
<GGXH></GGXH>
<DW></DW>
<XMSL></XMSL>
<XMDJ></XMDJ>
<XMJE>{$invoiceAllprice}</XMJE>
<SL>0</SL>
<SE>{$hejishuie}</SE>
<BY1>备用字段1</BY1>
<BY2>备用字段2</BY2>
<BY3>备用字段3</BY3>
<BY4>备用字段4</BY4>
<BY5>备用字段5</BY5>
</COMMON_FPKJ_XMXX>
</COMMON_FPKJ_XMXXS></REQUEST_COMMON_FPKJ>";
            }else{
                $contentData = "<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>



<FPQQLSH>{$fp}</FPQQLSH>
<KPLX>0</KPLX>
<ZSFS>0</ZSFS>
<XSF_NSRSBH>{$companiesOne['companies_invoice_ird']}</XSF_NSRSBH>
<XSF_MC>{$companiesOne['companies_cnname']}</XSF_MC>
<XSF_DZDH>{$companiesOne['companies_invoice_address']} {$companiesOne['companies_invoice_phone']}</XSF_DZDH>
<XSF_YHZH>{$companiesOne['companies_invoice_bankname']} {$companiesOne['companies_invoice_bankcard']}</XSF_YHZH>
<GMF_NSRSBH>{$invoiceOne['invoice_taxpayernum']}</GMF_NSRSBH>
<GMF_MC>{$invoiceOne['invoice_title']}</GMF_MC>
<GMF_DZDH></GMF_DZDH>
<GMF_YHZH></GMF_YHZH>
<GMF_SJH>$memberMobile</GMF_SJH>
<GMF_DZYX>{$invoiceOne['invoice_email']}</GMF_DZYX>
<FPT_ZH></FPT_ZH>
<WX_OPENID></WX_OPENID>
<KPR>{$companiesOne['companies_invoice_drawer']}</KPR>
<SKR>{$companiesOne['companies_invoice_payee']}</SKR>
<FHR>{$companiesOne['companies_invoice_reviewer']}</FHR>
<YFP_DM></YFP_DM>
<YFP_HM></YFP_HM>
<JSHJ>{$jiashuiheji}</JSHJ>
<HJJE>{$invoiceAllprice}</HJJE>
<HJSE>{$hejishuie}</HJSE>
<KCE></KCE>
<BZ>商户订单号：{$orderOne['order_pid']}</BZ>
<HYLX>行业类型</HYLX>
<BY1>备用字段1</BY1><BY2>备用字段2</BY2><BY3>备用字段3</BY3>
<BY4>备用字段4</BY4><BY5>备用字段5</BY5><BY6>备用字段6</BY6>
<BY7>备用字段7</BY7><BY8>备用字段8</BY8><BY9>备用字段9</BY9>
<BY10>备用字段10</BY10>
<COMMON_FPKJ_XMXXS class='COMMON_FPKJ_XMXX' size='1'>
<COMMON_FPKJ_XMXX>
<FPHXZ>0</FPHXZ>
<SPBM>3070201020000000000</SPBM>
<ZXBM></ZXBM>
<YHZCBS>0</YHZCBS>
<LSLBS></LSLBS>
<ZZSTSGL></ZZSTSGL>
<XMMC>培训费</XMMC>
<GGXH></GGXH>
<DW></DW>
<XMSL></XMSL>
<XMDJ></XMDJ>
<XMJE>{$invoiceAllprice}</XMJE>
<SL>{$rate}</SL>
<SE>{$hejishuie}</SE>
<BY1>备用字段1</BY1>
<BY2>备用字段2</BY2>
<BY3>备用字段3</BY3>
<BY4>备用字段4</BY4>
<BY5>备用字段5</BY5>
</COMMON_FPKJ_XMXX>
</COMMON_FPKJ_XMXXS></REQUEST_COMMON_FPKJ>";
            }



            $interfaceCode = "DFXJ1001";//初始化接口文档
            $reqXML = $this->getSendToTaxXML($companiesOne['companies_invoice_appid'],$companiesOne['companies_invoice_key'],$interfaceCode,$contentData);
            libxml_disable_entity_loader(false);
            $opts = array(
                'ssl'   => array(
                    'verify_peer'          => false
                ),
                'https' => array(
                    'curl_verify_ssl_peer'  => false,
                    'curl_verify_ssl_host'  => false
                )
            );
            $streamContext = stream_context_create($opts);


            //软证书配置变量，此处软证书配置可以不填写
            if($invoiceOne['companies_id'] == '8'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/8/yuanzhuo.pem', //上海圆桌进修学院
                    'passphrase'	=>'20699X', //上海圆桌进修学院
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '9'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/9/testISSUE.pem', //测试
                    'passphrase'	=>'123456', //测试
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '10'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/10/weixun.pem', //上海闵行区炜讯教育中心
                    'passphrase'	=>'944972', //上海闵行区炜讯教育中心
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '11'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/11/weiken.pem', //上海宝山区吉的堡维肯培训学校
                    'passphrase'	=>'088918', //上海宝山区吉的堡维肯培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '12'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/12/qidi.pem', //上海黄浦区吉的堡奇弟培训学校
                    'passphrase'	=>'06699G', //上海黄浦区吉的堡奇弟培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '13'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/13/yingqun.pem', //上海普陀区吉的堡英群培训学校
                    'passphrase'	=>'32508E', //上海普陀区吉的堡英群培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '14'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/14/yingzi.pem', //上海吉的堡英姿教育培训有限公司
                    'passphrase'	=>'8D4R9B', //上海吉的堡英姿教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78267'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78267/zhaoyue.pem', //上海吉的堡兆悦教育培训有限公司
                    'passphrase'	=>'CMYR6B', //上海吉的堡兆悦教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78273'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78273/quansheng.pem', //上海吉的堡权胜教育培训有限公司
                    'passphrase'	=>'NYGN16', //上海吉的堡权胜教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78272'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78272/yingfei.pem', //上海吉的堡菲悦教育培训有限公司
                    'passphrase'	=>'WP217X', //上海吉的堡菲悦教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78281'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78281/yingyou.pem', //上海吉的堡英宥英语培训学校有限公司
                    'passphrase'	=>'14050J', //上海吉的堡英宥英语培训学校有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78324'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78324/yingchi.pem', //上海吉的堡英驰文化传播有限公司
                    'passphrase'	=>'4D0R4G', //上海吉的堡英驰文化传播有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78338'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78338/shenghao.pem', //上海吉的堡胜颢文化传播有限公司
                    'passphrase'	=>'BBNG13', //上海吉的堡胜颢文化传播有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78353'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78353/yinghui.pem', //上海吉的堡英惠文化传播有限公司
                    'passphrase'	=>'DL6R39', //上海吉的堡英惠文化传播有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78382'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78382/huizhe.pem', //上海吉的堡惠哲英语培训有限公司
                    'passphrase'	=>'BFH46N', //上海吉的堡惠哲英语培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78402'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78402/yuwei.pem', //上海吉的堡育唯英语培训学校有限公司
                    'passphrase'	=>'50YU5R', //上海吉的堡育唯英语培训学校有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78597'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78597/shengxuan.pem', //上海胜萱教育科技有限公司
                    'passphrase'	=>'50138G', //上海胜萱教育科技有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78604'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78604/yingsong.pem', //上海虹口区吉的堡英颂培训学校
                    'passphrase'	=>'943083', //上海虹口区吉的堡英颂培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78415'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78415/yingfan.pem', //上海吉的堡英芃文化艺术发展有限公司
                    'passphrase'	=>'60E99Q', //上海吉的堡英芃文化艺术发展有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78598'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78598/jiatang.pem', //上海吉的堡佳棠文化科技发展有限公司
                    'passphrase'	=>'U92B28', //上海吉的堡佳棠文化科技发展有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78602'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78602/jiaheng.pem', //上海静安区吉的堡嘉恒培训学校
                    'passphrase'	=>'51340E', //上海静安区吉的堡嘉恒培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78605'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78605/yinghui.pem', //上海金山区吉的堡英惠培训学校
                    'passphrase'	=>'65824E', //上海金山区吉的堡英惠培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78573'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78573/caifan.pem', //上海彩芃教育科技有限公司
                    'passphrase'	=>'EK3E36', //上海彩芃教育科技有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78574'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78574/caiyou.pem', //上海彩宥文化艺术发展有限公司
                    'passphrase'	=>'FHH22W', //上海彩宥文化艺术发展有限公司
                    'stream_context'    => $streamContext
                );
            }

            try{
                $client = new \SoapClient($companiesOne['companies_url'], $cerarr);
                //调用doService方法
                $result = $client->doService([
                    'in0' => $reqXML
                ]);
            }catch(Exception $e){
                print_r($e);die;
            }
            //将stdClass Object转换成array格式
            $str = $this->object_array($result, true);
            $fa_info = $str['out'];

            //将xml格式转换为array格式
            $data = $this->parserXMLToArray($fa_info,'array');
            $returnCode = $data['returnStateInfo']['returnCode'];
            $returnMessage = $data['returnStateInfo']['returnMessage'];

            if($returnCode == '0000'){
                //base64解密
                $xml_info = base64_decode($data['Data']['content']);

                //下载地址
                preg_match_all('/\<PDF_URL\>(.*)\<\/PDF_URL\>/',$xml_info,$pdf_url);
                $xzurl = substr($pdf_url[1][0],9,-3);

                //微信打开地址
                preg_match_all('/\<SP_URL\>(.*)\<\/SP_URL\>/',$xml_info,$sp_url);
                $wxurl = substr($sp_url[1][0],9,-3);

                //识别号等其他信息
                $contentxml = simplexml_load_string($xml_info);
                $contentdata = json_decode(json_encode($contentxml),true);

                $invoiceData = array();
                $invoiceData['invoice_pdfurl'] = $xzurl;
                $invoiceData['invoice_wxurl'] = $wxurl;
                $invoiceData['invoice_code'] = $contentdata['FP_DM'];
                $invoiceData['invoice_number'] = $contentdata['FP_HM'];
                $invoiceData['invoice_serialnumber'] = $contentdata['FPQQLSH'];
                $invoiceData['invoice_maketime'] = time();
                return $invoiceData;
            }else{
                $res = array('error' => '1', 'errortip' => "{$data['returnStateInfo']['returnMessage']}", 'lsh' => "{$fp}");
                $json_play = new \Webjson();
                exit($json_play->encode($res));
            }
        }

        if($companiesOne['companies_invoice_chl'] == '易发票'){

            header("content-type:application/json;charset:utf-8");

            $memberMobile = $invoiceOne['invoice_mobile'];

            $rate = $companiesOne['companies_rate']/100;

            $invoiceAllprice = round(($invoiceOne['invoice_allprice']) / ($rate + 1),2);
            $hejishuie = round((($invoiceOne['invoice_allprice']) / ($rate + 1)) * $rate,2);
            $jiashuiheji = round((($invoiceOne['invoice_allprice']) / ($rate + 1)) + (($invoiceOne['invoice_allprice']) / ($rate + 1) * $rate),2);

            $today = date("Ymd");

            if(!$companiesOne['companies_pricetype']){
                $companiesOne['companies_pricetype'] = '非学历教育服务';
                $companiesOne['companies_pricename'] = '培训费';
                $companiesOne['companies_pricecode'] = '3070201020000000000';
            }

            if($companiesOne['companies_pricetype'] == '文化服务'){
                $companiesOne['companies_pricename'] = '服务费';
                $companiesOne['companies_pricecode'] = '3070101000000000000';
            }


            if($companiesOne['companies_rate'] == '3'){
                $param = '{
                    "storeNo": "' . $companiesOne['companies_invoice_key'] . '",
                    "posNo": "1",
                    "transNo": "' . $invoiceOne['invoice_id'] . '",
                    "date":"' . $today . '",
                    "price": ' . $jiashuiheji . ',
                    "remark": "",
                    "buyerTitle": "' . $invoiceOne['invoice_title'] . '",
                    "buyerTaxNo": "",
                    "buyerBankAccount": "",
                    "buyerAddressPhone": "",
                    "buyerMobile": "' . $memberMobile . '",
                    "buyerEmail": "' . $invoiceOne['invoice_email'] . '",
                    "items": [{
                        "name": "' . $companiesOne['companies_pricename'] . '",
                        "publicCode": "' . $companiesOne['companies_pricecode'] . '",
                        "publicName": "' . $companiesOne['companies_pricetype'] . '",
                        "zzstsgl": "简易征收",
                        "spec": "",
                        "unit": "",
                        "num": 1,
                        "price": ' . $invoiceAllprice . ',
                        "amount": ' . $invoiceAllprice . ',
                        "taxRate": ' . $rate . ',
                        "tax": ' . $hejishuie . ',
                        "discount": 0
                    }]
                }';
            }else{
                $param = '{
                    "storeNo": "' . $companiesOne['companies_invoice_key'] . '",
                    "posNo": "1",
                    "transNo": "' . $invoiceOne['invoice_id'] . '",
                    "date":"' . $today . '",
                    "price": ' . $jiashuiheji . ',
                    "remark": "",
                    "buyerTitle": "' . $invoiceOne['invoice_title'] . '",
                    "buyerTaxNo": "",
                    "buyerBankAccount": "",
                    "buyerAddressPhone": "",
                    "buyerMobile": "' . $memberMobile . '",
                    "buyerEmail": "' . $invoiceOne['invoice_email'] . '",
                    "items": [{
                        "name": "' . $companiesOne['companies_pricename'] . '",
                        "publicCode": "' . $companiesOne['companies_pricecode'] . '",
                        "publicName": "' . $companiesOne['companies_pricetype'] . '",
                        "spec": "",
                        "unit": "",
                        "num": 1,
                        "price": ' . $invoiceAllprice . ',
                        "amount": ' . $invoiceAllprice . ',
                        "taxRate": ' . $rate . ',
                        "tax": ' . $hejishuie . ',
                        "discount": 0
                    }]
                }';
            }




            $getBackurl = request_by_curl("https://wxp.easyfapiao.com/v2/haizhouJiaotong/invoice", $param, "POST");

            if($getBackurl){
                $data=array();
                $data['log_url']="https://wxp.easyfapiao.com/v2/haizhouJiaotong/invoice";
                $data['log_requestjson']=addslashes(json_encode($param,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES));
                $data['log_bakjson']=addslashes($getBackurl);
                $data['log_createtime']=time();
                $this->DataControl->insertData("fp_visit_log",$data);
            }

            $List = json_decode($getBackurl, true);

            if($List['errMsg'] == 'success'){
                $invoiceData = array();
//                $invoiceData['invoice_pdfurl'] = $xzurl;
                $invoiceData['invoice_code'] = $List['body']['invoiceCode'];
                $invoiceData['invoice_number'] = $List['body']['invoiceNo'];
                $invoiceData['invoice_serialnumber'] = $List['body']['invoiceSn'];
                $invoiceData['invoice_maketime'] = $List['body']['timestamp'];
                return $invoiceData;
            }else{
                $res = array('error' => '1', 'errortip' => "{$List['errMsg']}", 'lsh' => "{$fp}");
                $json_play = new \Webjson();
                exit($json_play->encode($res));
            }

        }


    }

    function InvoiceMore($param)
    {
        //填写发票详细信息

        $orderOne = $this->DataControl->getOne("smc_payfee_order","order_pid='{$param['order_pid']}'");
        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$param['invoice_id']}'");
        $companiesOne = $this->DataControl->getOne('gmc_code_companies',"companies_id='{$invoiceOne['companies_id']}'");
//        $refundPrice = $this->DataControl->selectOne("SELECT sum(r.refund_price) as refund FROM ptc_shop_refund as r WHERE r.order_pid = '{$param['order_pid']}'");

        $memberMobile = $invoiceOne['invoice_mobile'];
        $rate = $companiesOne['companies_rate']/100;

        $fp = 'JDB'.date('YmdHis').mt_rand(10,99);
        $invoiceAllprice = round(($invoiceOne['invoice_allprice']) / ($rate + 1),2);
        $hejishuie = round($invoiceAllprice * $rate,2);
        $jiashuiheji = round($invoiceAllprice + $hejishuie,2);

        $a = array();
        $a['companies_invoice_ird'] = $companiesOne['companies_invoice_ird'];
        $a['companies_invoice_machineno'] = $companiesOne['companies_cnname'];
        $a['companies_invoice_address'] = $companiesOne['companies_invoice_address'].' '.$companiesOne['companies_invoice_phone'];
        $a['companies_invoice_bankname'] = $companiesOne['companies_invoice_bankname'].' '.$companiesOne['companies_invoice_bankcard'];

        if($rate == '0'){
            $contentData = "<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>



<FPQQLSH>{$fp}</FPQQLSH>
<KPLX>0</KPLX>
<ZSFS>0</ZSFS>
<XSF_NSRSBH>{$companiesOne['companies_invoice_ird']}</XSF_NSRSBH>
<XSF_MC>{$companiesOne['companies_cnname']}</XSF_MC>
<XSF_DZDH>{$companiesOne['companies_invoice_address']} {$companiesOne['companies_invoice_phone']}</XSF_DZDH>
<XSF_YHZH>{$companiesOne['companies_invoice_bankname']} {$companiesOne['companies_invoice_bankcard']}</XSF_YHZH>
<GMF_NSRSBH>{$invoiceOne['invoice_taxpayernum']}</GMF_NSRSBH>
<GMF_MC>{$invoiceOne['invoice_title']}</GMF_MC>
<GMF_DZDH></GMF_DZDH>
<GMF_YHZH></GMF_YHZH>
<GMF_SJH>$memberMobile</GMF_SJH>
<GMF_DZYX>{$invoiceOne['invoice_email']}</GMF_DZYX>
<FPT_ZH></FPT_ZH>
<WX_OPENID></WX_OPENID>
<KPR>{$companiesOne['companies_invoice_drawer']}</KPR>
<SKR>{$companiesOne['companies_invoice_payee']}</SKR>
<FHR>{$companiesOne['companies_invoice_reviewer']}</FHR>
<YFP_DM></YFP_DM>
<YFP_HM></YFP_HM>
<JSHJ>{$jiashuiheji}</JSHJ>
<HJJE>{$invoiceAllprice}</HJJE>
<HJSE>{$hejishuie}</HJSE>
<KCE></KCE>
<BZ>商户订单号：{$orderOne['order_pid']}</BZ>
<HYLX>行业类型</HYLX>
<BY1>备用字段1</BY1><BY2>备用字段2</BY2><BY3>备用字段3</BY3>
<BY4>备用字段4</BY4><BY5>备用字段5</BY5><BY6>备用字段6</BY6>
<BY7>备用字段7</BY7><BY8>备用字段8</BY8><BY9>备用字段9</BY9>
<BY10>备用字段10</BY10>
<COMMON_FPKJ_XMXXS class='COMMON_FPKJ_XMXX' size='1'>
<COMMON_FPKJ_XMXX>
<FPHXZ>0</FPHXZ>
<SPBM>3070201020000000000</SPBM>
<ZXBM></ZXBM>
<YHZCBS>1</YHZCBS>
<LSLBS>1</LSLBS>
<ZZSTSGL>免税</ZZSTSGL>
<XMMC>培训费</XMMC>
<GGXH></GGXH>
<DW></DW>
<XMSL></XMSL>
<XMDJ></XMDJ>
<XMJE>{$invoiceAllprice}</XMJE>
<SL>0</SL>
<SE>{$hejishuie}</SE>
<BY1>备用字段1</BY1>
<BY2>备用字段2</BY2>
<BY3>备用字段3</BY3>
<BY4>备用字段4</BY4>
<BY5>备用字段5</BY5>
</COMMON_FPKJ_XMXX>
</COMMON_FPKJ_XMXXS></REQUEST_COMMON_FPKJ>";
        }else{
            $contentData = "<REQUEST_COMMON_FPKJ class='REQUEST_COMMON_FPKJ'>



<FPQQLSH>{$fp}</FPQQLSH>
<KPLX>0</KPLX>
<ZSFS>0</ZSFS>
<XSF_NSRSBH>{$companiesOne['companies_invoice_ird']}</XSF_NSRSBH>
<XSF_MC>{$companiesOne['companies_cnname']}</XSF_MC>
<XSF_DZDH>{$companiesOne['companies_invoice_address']} {$companiesOne['companies_invoice_phone']}</XSF_DZDH>
<XSF_YHZH>{$companiesOne['companies_invoice_bankname']} {$companiesOne['companies_invoice_bankcard']}</XSF_YHZH>
<GMF_NSRSBH>{$invoiceOne['invoice_taxpayernum']}</GMF_NSRSBH>
<GMF_MC>{$invoiceOne['invoice_title']}</GMF_MC>
<GMF_DZDH></GMF_DZDH>
<GMF_YHZH></GMF_YHZH>
<GMF_SJH>$memberMobile</GMF_SJH>
<GMF_DZYX>{$invoiceOne['invoice_email']}</GMF_DZYX>
<FPT_ZH></FPT_ZH>
<WX_OPENID></WX_OPENID>
<KPR>{$companiesOne['companies_invoice_drawer']}</KPR>
<SKR>{$companiesOne['companies_invoice_payee']}</SKR>
<FHR>{$companiesOne['companies_invoice_reviewer']}</FHR>
<YFP_DM></YFP_DM>
<YFP_HM></YFP_HM>
<JSHJ>{$jiashuiheji}</JSHJ>
<HJJE>{$invoiceAllprice}</HJJE>
<HJSE>{$hejishuie}</HJSE>
<KCE></KCE>
<BZ>商户订单号：{$orderOne['order_pid']}</BZ>
<HYLX>行业类型</HYLX>
<BY1>备用字段1</BY1><BY2>备用字段2</BY2><BY3>备用字段3</BY3>
<BY4>备用字段4</BY4><BY5>备用字段5</BY5><BY6>备用字段6</BY6>
<BY7>备用字段7</BY7><BY8>备用字段8</BY8><BY9>备用字段9</BY9>
<BY10>备用字段10</BY10>
<COMMON_FPKJ_XMXXS class='COMMON_FPKJ_XMXX' size='1'>
<COMMON_FPKJ_XMXX>
<FPHXZ>0</FPHXZ>
<SPBM>3070201020000000000</SPBM>
<ZXBM></ZXBM>
<YHZCBS>0</YHZCBS>
<LSLBS></LSLBS>
<ZZSTSGL></ZZSTSGL>
<XMMC>培训费</XMMC>
<GGXH></GGXH>
<DW></DW>
<XMSL></XMSL>
<XMDJ></XMDJ>
<XMJE>{$invoiceAllprice}</XMJE>
<SL>{$rate}</SL>
<SE>{$hejishuie}</SE>
<BY1>备用字段1</BY1>
<BY2>备用字段2</BY2>
<BY3>备用字段3</BY3>
<BY4>备用字段4</BY4>
<BY5>备用字段5</BY5>
</COMMON_FPKJ_XMXX>
</COMMON_FPKJ_XMXXS></REQUEST_COMMON_FPKJ>";
        }

        $interfaceCode = "DFXJ1001";//初始化接口文档
        $reqXML = $this->getSendToTaxXML($companiesOne['companies_invoice_appid'],$companiesOne['companies_invoice_key'],$interfaceCode,$contentData);


        libxml_disable_entity_loader(false);
        $opts = array(
            'ssl'   => array(
                'verify_peer'          => false
            ),
            'https' => array(
                'curl_verify_ssl_peer'  => false,
                'curl_verify_ssl_host'  => false
            )
        );
        $streamContext = stream_context_create($opts);


        //软证书配置变量，此处软证书配置可以不填写
        if($invoiceOne['companies_id'] == '8'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/8/yuanzhuo.pem', //上海圆桌进修学院
                'passphrase'	=>'20699X', //上海圆桌进修学院
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '9'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/9/testISSUE.pem', //测试
                'passphrase'	=>'123456', //测试
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '10'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/10/weixun.pem', //上海闵行区炜讯教育中心
                'passphrase'	=>'944972', //上海闵行区炜讯教育中心
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '11'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/11/weiken.pem', //上海宝山区吉的堡维肯培训学校
                'passphrase'	=>'088918', //上海宝山区吉的堡维肯培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '12'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/12/qidi.pem', //上海黄浦区吉的堡奇弟培训学校
                'passphrase'	=>'06699G', //上海黄浦区吉的堡奇弟培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '13'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/13/yingqun.pem', //上海普陀区吉的堡英群培训学校
                'passphrase'	=>'32508E', //上海普陀区吉的堡英群培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '14'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/14/yingzi.pem', //上海吉的堡英姿教育培训有限公司
                'passphrase'	=>'8D4R9B', //上海吉的堡英姿教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78267'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78267/zhaoyue.pem', //上海吉的堡英姿教育培训有限公司
                'passphrase'	=>'CMYR6B', //上海吉的堡英姿教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78273'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78273/quansheng.pem', //上海吉的堡权胜教育培训有限公司
                'passphrase'	=>'NYGN16', //上海吉的堡权胜教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78272'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78272/yingfei.pem', //上海吉的堡菲悦教育培训有限公司
                'passphrase'	=>'WP217X', //上海吉的堡菲悦教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78281'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78281/yingyou.pem', //上海吉的堡英宥英语培训学校有限公司
                'passphrase'	=>'14050J', //上海吉的堡英宥英语培训学校有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78324'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78324/yingchi.pem', //上海吉的堡英驰文化传播有限公司
                'passphrase'	=>'4D0R4G', //上海吉的堡英驰文化传播有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78338'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78338/shenghao.pem', //上海吉的堡胜颢文化传播有限公司
                'passphrase'	=>'BBNG13', //上海吉的堡胜颢文化传播有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78353'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78353/yinghui.pem', //上海吉的堡英惠文化传播有限公司
                'passphrase'	=>'DL6R39', //上海吉的堡英惠文化传播有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78382'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78382/huizhe.pem', //上海吉的堡惠哲英语培训有限公司
                'passphrase'	=>'BFH46N', //上海吉的堡惠哲英语培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78402'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78402/yuwei.pem', //上海吉的堡育唯英语培训学校有限公司
                'passphrase'	=>'50YU5R', //上海吉的堡育唯英语培训学校有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78597'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78597/shengxuan.pem', //上海胜萱教育科技有限公司
                'passphrase'	=>'50138G', //上海胜萱教育科技有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78604'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78604/yingsong.pem', //上海虹口区吉的堡英颂培训学校
                'passphrase'	=>'943083', //上海虹口区吉的堡英颂培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78415'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78415/yingfan.pem', //上海吉的堡英芃文化艺术发展有限公司
                'passphrase'	=>'60E99Q', //上海吉的堡英芃文化艺术发展有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78598'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78598/jiatang.pem', //上海吉的堡佳棠文化科技发展有限公司
                'passphrase'	=>'U92B28', //上海吉的堡佳棠文化科技发展有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78602'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78602/jiaheng.pem', //上海静安区吉的堡嘉恒培训学校
                'passphrase'	=>'51340E', //上海静安区吉的堡嘉恒培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78605'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78605/yinghui.pem', //上海金山区吉的堡英惠培训学校
                'passphrase'	=>'65824E', //上海金山区吉的堡英惠培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78605'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78605/yinghui.pem', //上海金山区吉的堡英惠培训学校
                'passphrase'	=>'65824E', //上海金山区吉的堡英惠培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78573'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78573/caifan.pem', //上海彩芃教育科技有限公司
                'passphrase'	=>'EK3E36', //上海彩芃教育科技有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78574'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78574/caiyou.pem', //上海彩宥文化艺术发展有限公司
                'passphrase'	=>'FHH22W', //上海彩宥文化艺术发展有限公司
                'stream_context'    => $streamContext
            );
        }

        try{
            $client = new \SoapClient($companiesOne['companies_url'], $cerarr);
            //调用doService方法
            $result = $client->doService([
                'in0' => $reqXML
            ]);
        }catch(Exception $e){
            print_r($e);die;
        }
        //将stdClass Object转换成array格式
        $str = $this->object_array($result, true);
        $fa_info = $str['out'];

        //将xml格式转换为array格式
        $data = $this->parserXMLToArray($fa_info,'array');
        $returnCode = $data['returnStateInfo']['returnCode'];
        $returnMessage = $data['returnStateInfo']['returnMessage'];

        if($returnCode == '0000'){
            //base64解密
            $xml_info = base64_decode($data['Data']['content']);

            //下载地址
            preg_match_all('/\<PDF_URL\>(.*)\<\/PDF_URL\>/',$xml_info,$pdf_url);
            $xzurl = substr($pdf_url[1][0],9,-3);

            //微信打开地址
            preg_match_all('/\<SP_URL\>(.*)\<\/SP_URL\>/',$xml_info,$sp_url);
            $wxurl = substr($sp_url[1][0],9,-3);

            //识别号等其他信息
            $contentxml = simplexml_load_string($xml_info);
            $contentdata = json_decode(json_encode($contentxml),true);

            $invoiceData = array();
            $invoiceData['invoice_pdfurl'] = $xzurl;
            $invoiceData['invoice_wxurl'] = $wxurl;
            $invoiceData['invoice_code'] = $contentdata['FP_DM'];
            $invoiceData['invoice_number'] = $contentdata['FP_HM'];
            $invoiceData['invoice_serialnumber'] = $contentdata['FPQQLSH'];
            $invoiceData['invoice_maketime'] = time();
            return $invoiceData;
        }else{
            $invoiceData = array();
            $invoiceData['note'] = $data['returnStateInfo']['returnMessage'];
            $invoiceData['invoice_pdfurl'] = '';
            return $invoiceData;
        }
    }

    function InvoiceMores($param)
    {
        //填写发票详细信息
        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$param['invoice_id']}'");
        $companiesOne = $this->DataControl->getOne('gmc_code_companies',"companies_id='{$invoiceOne['companies_id']}'");

        $contentData = "
<REQUEST_COMMON_FPKSHC class='REQUEST_COMMON_FPKSHC'>
    <FPQQLSH>发票请求流水号</FPQQLSH>     
    <XSF_NSRSBH>销售方纳税人识别号</XSF_NSRSBH>
    <XSF_MC>销售方名称</XSF_MC>   
    <YFP_DM>原发票代码</YFP_DM>
    <YFP_HM>原发票号码</YFP_HM>
</REQUEST_COMMON_FPKSHC>";

        $interfaceCode = "DFXJ1008";//初始化接口文档
        $reqXML = $this->getSendToTaxXML($companiesOne['companies_invoice_appid'],$companiesOne['companies_invoice_key'],$interfaceCode,$contentData);
        libxml_disable_entity_loader(false);
        $opts = array(
            'ssl'   => array(
                'verify_peer'          => false
            ),
            'https' => array(
                'curl_verify_ssl_peer'  => false,
                'curl_verify_ssl_host'  => false
            )
        );
        $streamContext = stream_context_create($opts);


        //软证书配置变量，此处软证书配置可以不填写
        if($invoiceOne['companies_id'] == '8'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/8/yuanzhuo.pem', //上海圆桌进修学院
                'passphrase'	=>'20699X', //上海圆桌进修学院
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '9'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/9/testISSUE.pem', //测试
                'passphrase'	=>'123456', //测试
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '10'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/10/weixun.pem', //上海闵行区炜讯教育中心
                'passphrase'	=>'944972', //上海闵行区炜讯教育中心
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '11'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/11/weiken.pem', //上海宝山区吉的堡维肯培训学校
                'passphrase'	=>'088918', //上海宝山区吉的堡维肯培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '12'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/12/qidi.pem', //上海黄浦区吉的堡奇弟培训学校
                'passphrase'	=>'06699G', //上海黄浦区吉的堡奇弟培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '13'){
            $cerarr = array(
                'local_cert'	=>'Core/8888/13/yingqun.pem', //上海普陀区吉的堡英群培训学校
                'passphrase'	=>'32508E', //上海普陀区吉的堡英群培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '14'){
            $cerarr = array(
                'local_cert'	=>'Core/8888/14/yingzi.pem', //上海吉的堡英姿教育培训有限公司
                'passphrase'	=>'8D4R9B', //上海吉的堡英姿教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78267'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78267/zhaoyue.pem', //上海吉的堡英姿教育培训有限公司
                'passphrase'	=>'CMYR6B', //上海吉的堡英姿教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78273'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78273/quansheng.pem', //上海吉的堡权胜教育培训有限公司
                'passphrase'	=>'NYGN16', //上海吉的堡权胜教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78272'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78272/yingfei.pem', //上海吉的堡菲悦教育培训有限公司
                'passphrase'	=>'WP217X', //上海吉的堡菲悦教育培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78281'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78281/yingyou.pem', //上海吉的堡英宥英语培训学校有限公司
                'passphrase'	=>'14050J', //上海吉的堡英宥英语培训学校有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78324'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78324/yingchi.pem', //上海吉的堡英驰文化传播有限公司
                'passphrase'	=>'4D0R4G', //上海吉的堡英驰文化传播有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78338'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78338/shenghao.pem', //上海吉的堡胜颢文化传播有限公司
                'passphrase'	=>'BBNG13', //上海吉的堡胜颢文化传播有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78353'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78353/yinghui.pem', //上海吉的堡英惠文化传播有限公司
                'passphrase'	=>'DL6R39', //上海吉的堡英惠文化传播有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78382'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78382/huizhe.pem', //上海吉的堡惠哲英语培训有限公司
                'passphrase'	=>'BFH46N', //上海吉的堡惠哲英语培训有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78402'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78402/yuwei.pem', //上海吉的堡育唯英语培训学校有限公司
                'passphrase'	=>'50YU5R', //上海吉的堡育唯英语培训学校有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78597'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78597/shengxuan.pem', //上海胜萱教育科技有限公司
                'passphrase'	=>'50138G', //上海胜萱教育科技有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78604'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78604/yingsong.pem', //上海虹口区吉的堡英颂培训学校
                'passphrase'	=>'943083', //上海虹口区吉的堡英颂培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78415'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78415/yingfan.pem', //上海吉的堡英芃文化艺术发展有限公司
                'passphrase'	=>'60E99Q', //上海吉的堡英芃文化艺术发展有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78598'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78598/jiatang.pem', //上海吉的堡佳棠文化科技发展有限公司
                'passphrase'	=>'U92B28', //上海吉的堡佳棠文化科技发展有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78602'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78602/jiaheng.pem', //上海静安区吉的堡嘉恒培训学校
                'passphrase'	=>'51340E', //上海静安区吉的堡嘉恒培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78605'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78605/yinghui.pem', //上海金山区吉的堡英惠培训学校
                'passphrase'	=>'65824E', //上海金山区吉的堡英惠培训学校
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78573'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78573/caifan.pem', //上海彩芃教育科技有限公司
                'passphrase'	=>'EK3E36', //上海彩芃教育科技有限公司
                'stream_context'    => $streamContext
            );
        }elseif($invoiceOne['companies_id'] == '78574'){
            $cerarr = array(
                'local_cert'	=>'Invoicexrf/8888/78574/caiyou.pem', //上海彩宥文化艺术发展有限公司
                'passphrase'	=>'FHH22W', //上海彩宥文化艺术发展有限公司
                'stream_context'    => $streamContext
            );
        }

        try{
            $client = new \SoapClient($companiesOne['companies_url'], $cerarr);
            //调用doService方法
            $result = $client->doService([
                'in0' => $reqXML
            ]);
        }catch(Exception $e){
            print_r($e);die;
        }
        //将stdClass Object转换成array格式
        $str = $this->object_array($result, true);
        $fa_info = $str['out'];

        //将xml格式转换为array格式
        $data = $this->parserXMLToArray($fa_info,'array');
        $returnCode = $data['returnStateInfo']['returnCode'];
        $returnMessage = $data['returnStateInfo']['returnMessage'];

        var_dump($returnMessage);die();

        if($returnCode == '0000'){
            return $invoiceData;
        }else{
            return $invoiceData;
        }
    }

    function Red($param)
    {
        $invoiceOne = $this->DataControl->getOne("shop_invoice","invoice_id='{$param['invoice_id']}'");
        $companiesOne = $this->DataControl->getOne('gmc_code_companies',"companies_id='{$invoiceOne['companies_id']}'");

        if($companiesOne['companies_invoice_chl'] == '国信发票'){
            $fp = 'JDB'.date('YmdHis').mt_rand(10,99);

            $contentData = "
<REQUEST_COMMON_FPKSHC class='REQUEST_COMMON_FPKSHC'>
    <FPQQLSH>{$fp}</FPQQLSH>     
    <XSF_NSRSBH>{$companiesOne['companies_invoice_ird']}</XSF_NSRSBH>
    <XSF_MC>{$companiesOne['companies_cnname']}</XSF_MC>   
    <YFP_DM>{$invoiceOne['invoice_code']}</YFP_DM>
    <YFP_HM>{$invoiceOne['invoice_number']}</YFP_HM>
</REQUEST_COMMON_FPKSHC>";

            $interfaceCode = "DFXJ1008";//初始化接口文档
            $reqXML = $this->getSendToTaxXML($companiesOne['companies_invoice_appid'],$companiesOne['companies_invoice_key'],$interfaceCode,$contentData);
            libxml_disable_entity_loader(false);
            $opts = array(
                'ssl'   => array(
                    'verify_peer'          => false
                ),
                'https' => array(
                    'curl_verify_ssl_peer'  => false,
                    'curl_verify_ssl_host'  => false
                )
            );
            $streamContext = stream_context_create($opts);

            //软证书配置变量，此处软证书配置可以不填写
            if($invoiceOne['companies_id'] == '8'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/8/yuanzhuo.pem', //上海圆桌进修学院
                    'passphrase'	=>'20699X', //上海圆桌进修学院
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '9'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/9/testISSUE.pem', //测试
                    'passphrase'	=>'123456', //测试
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '10'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/10/weixun.pem', //上海闵行区炜讯教育中心
                    'passphrase'	=>'944972', //上海闵行区炜讯教育中心
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '11'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/11/weiken.pem', //上海宝山区吉的堡维肯培训学校
                    'passphrase'	=>'088918', //上海宝山区吉的堡维肯培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '12'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/12/qidi.pem', //上海黄浦区吉的堡奇弟培训学校
                    'passphrase'	=>'06699G', //上海黄浦区吉的堡奇弟培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '13'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/13/yingqun.pem', //上海普陀区吉的堡英群培训学校
                    'passphrase'	=>'32508E', //上海普陀区吉的堡英群培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '14'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/14/yingzi.pem', //上海吉的堡英姿教育培训有限公司
                    'passphrase'	=>'8D4R9B', //上海吉的堡英姿教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78267'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78267/zhaoyue.pem', //上海吉的堡英姿教育培训有限公司
                    'passphrase'	=>'CMYR6B', //上海吉的堡英姿教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78273'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78273/quansheng.pem', //上海吉的堡权胜教育培训有限公司
                    'passphrase'	=>'NYGN16', //上海吉的堡权胜教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78272'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78272/yingfei.pem', //上海吉的堡菲悦教育培训有限公司
                    'passphrase'	=>'WP217X', //上海吉的堡菲悦教育培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78281'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78281/yingyou.pem', //上海吉的堡英宥英语培训学校有限公司
                    'passphrase'	=>'14050J', //上海吉的堡英宥英语培训学校有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78324'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78324/yingchi.pem', //上海吉的堡英驰文化传播有限公司
                    'passphrase'	=>'4D0R4G', //上海吉的堡英驰文化传播有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78338'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78338/shenghao.pem', //上海吉的堡胜颢文化传播有限公司
                    'passphrase'	=>'BBNG13', //上海吉的堡胜颢文化传播有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78353'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78353/yinghui.pem', //上海吉的堡英惠文化传播有限公司
                    'passphrase'	=>'DL6R39', //上海吉的堡英惠文化传播有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78382'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78382/huizhe.pem', //上海吉的堡惠哲英语培训有限公司
                    'passphrase'	=>'BFH46N', //上海吉的堡惠哲英语培训有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78402'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78402/yuwei.pem', //上海吉的堡育唯英语培训学校有限公司
                    'passphrase'	=>'50YU5R', //上海吉的堡育唯英语培训学校有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78597'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78597/shengxuan.pem', //上海胜萱教育科技有限公司
                    'passphrase'	=>'50138G', //上海胜萱教育科技有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78604'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78604/yingsong.pem', //上海虹口区吉的堡英颂培训学校
                    'passphrase'	=>'943083', //上海虹口区吉的堡英颂培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78415'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78415/yingfan.pem', //上海吉的堡英芃文化艺术发展有限公司
                    'passphrase'	=>'60E99Q', //上海吉的堡英芃文化艺术发展有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78598'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78598/jiatang.pem', //上海吉的堡佳棠文化科技发展有限公司
                    'passphrase'	=>'U92B28', //上海吉的堡佳棠文化科技发展有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78602'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78602/jiaheng.pem', //上海静安区吉的堡嘉恒培训学校
                    'passphrase'	=>'51340E', //上海静安区吉的堡嘉恒培训学校
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78573'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78573/caifan.pem', //上海彩芃教育科技有限公司
                    'passphrase'	=>'EK3E36', //上海彩芃教育科技有限公司
                    'stream_context'    => $streamContext
                );
            }elseif($invoiceOne['companies_id'] == '78574'){
                $cerarr = array(
                    'local_cert'	=>'Invoicexrf/8888/78574/caiyou.pem', //上海彩宥文化艺术发展有限公司
                    'passphrase'	=>'FHH22W', //上海彩宥文化艺术发展有限公司
                    'stream_context'    => $streamContext
                );
            }

            try{
                $client = new \SoapClient($companiesOne['companies_url'], $cerarr);
                //调用doService方法
                $result = $client->doService([
                    'in0' => $reqXML
                ]);
            }catch(Exception $e){
                print_r($e);die;
            }
            //将stdClass Object转换成array格式
            $str = $this->object_array($result, true);
            $fa_info = $str['out'];

            //将xml格式转换为array格式
            $data = $this->parserXMLToArray($fa_info,'array');
            $returnCode = $data['returnStateInfo']['returnCode'];
            $returnMessage = $data['returnStateInfo']['returnMessage'];

            if($returnCode == '0000'){
                $invoiceData = 1;
                return $invoiceData;
            }else{
                $res = array('error' => '1', 'errortip' => "{$data['returnStateInfo']['returnMessage']}");
                $json_play = new \Webjson();
                exit($json_play->encode($res));
            }
        }

        if($companiesOne['companies_invoice_chl'] == '易发票'){
            header("content-type:application/json;charset:utf-8");

            $param = '{
    "storeNo": "' . $companiesOne['companies_invoice_key'] . '",
    "invoiceSn": "' . $invoiceOne['invoice_serialnumber'] . '"
}
';

            $getBackurl = request_by_curl("https://wxp.easyfapiao.com/v2/haizhouJiaotong/cancelInvoice", $param, "POST");

            $List = json_decode($getBackurl, true);

            if($List['errMsg'] == 'success'){
                $invoiceData = 1;
                return $invoiceData;
            }else{
                $res = array('error' => '1', 'errortip' => "{$List['errMsg']}");
                $json_play = new \Webjson();
                exit($json_play->encode($res));
            }

        }






        }
}
