<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/22
 * Time: 17:49
 */

namespace Model\Imc;


class EditOperaModel extends \Model\modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct()
    {
        parent::__construct();
    }


    /**
     *  修改结转订单时间
     * author: ling
     * 对应接口文档 0001
     */
    function EidtDealOrderTime($trading_pid, $date)
    {
        $this->error = 1;
        $this->error = "禁用";
        return false;
        $day_retime = date('Y-m-d', strtotime($date));

        if (!$date || $day_retime == '1970-01-01') {
            $this->error = 1;
            $this->errortip = "请选择正确的时间";
            return false;
        }

        $day_time = strtotime($date);
        debug($day_time);

        $sql = "select  *  from  smc_forward_dealorder where trading_pid ='{$trading_pid}'  ";
        $tradingOne = $this->DataControl->selectOne($sql);
        if ($tradingOne) {
            $data = array();
            $data['dealorder_createtime'] = $day_time;
            if( $this->DataControl->updateData("smc_forward_dealorder"," trading_pid ='{$trading_pid}' ",$data)){

                $student_data= array();
                $student_data['trading_createtime']= $day_time;
                $this->DataControl->updateData("smc_student_trading"," trading_pid ='{$trading_pid}'" ,$student_data);

                $deal_track = array();
                $deal_track['tracks_time'] = time();
                $this->DataControl->updateData('smc_forward_dealorder_tracks',"dealorder_pid='{$tradingOne['dealorder_pid']}'",$deal_track);

                $coursebalance_log = array();
                $coursebalance_log['log_time'] = time();
               $stuCourseList =   $this->DataControl->selectClear("select  *  from  smc_student_coursebalance_log where log_class =1 and  student_id='{$tradingOne['student_id']}' ");
                if(count($stuCourseList) == 1){
                    $stuCourseOne =   $stuCourseList[0] ;
                    $this->DataControl->updateData('smc_student_coursebalance_log',"log_class=1  and  log_id = '{$stuCourseOne['log_id']}' ",$coursebalance_log);
                }else{
                     $this->error = 0 ;
                    var_dump($tradingOne['student_id']);
                     $this->errortip = '有多条记录smc_student_coursebalance_log' ;
                    return  false;
                }

                $coursebalancetime_log = array();
                $coursebalancetime_log['timelog_id'] = $day_time;
                $stuCoursetimeList =   $this->DataControl->selectClear("select  *  from  smc_student_coursebalance_timelog where    student_id='{$tradingOne['student_id']}' ");
                if(count($stuCoursetimeList) == 1){
                    $coursebalancetimeOne =   $stuCoursetimeList[0] ;

                    $this->DataControl->updateData('smc_student_coursebalance_timelog'," timelog_id = '{$coursebalancetimeOne['timelog_id']}' ",$coursebalancetime_log);
                }else{
                    $this->error = 0 ;
                    var_dump($tradingOne['student_id']);
                    $this->errortip = '有多条记录smc_student_coursebalance_log' ;
                    return  false;
                }

                $balance = array();
                $balance['balancelog_time'] = array();

                    $balanceList =   $this->DataControl->selectClear("select * from smc_student_balancelog  where student_id='{$tradingOne['student_id']}' and   balancelog_class = 1  ");
                if(count($balanceList) >   1 ){
                    $this->error = 0 ;
                    $this->errortip = '有多条记录smc_student_balancelog' ;
                  return  false;
                } else{
                    $this->DataControl->updateData("smc_student_balancelog","student_id='{$tradingOne['student_id']}' and   balancelog_class = 1 ",$balance);
                }
                $this->error = 0 ;
                $this->errortip = '请更改主表' ;
                var_dump($tradingOne['student_id']);
                return  true;
            }
        }

    }


    /**
     * 更新减免订单时间
     * author: ling
     * 对应接口文档 0001
     */
    function EditReduceOrderTime($trading_pid, $date)
    {
        $this->error = 1;
        $this->error = "禁用";
        return false;
        $day_retime = date('Y-m-d', strtotime($date));

        if (!$date || $day_retime == '1970-01-01') {
            $this->error = 1;
            $this->errortip = "请选择正确的时间";
            return false;
        } else {
            $day_time = strtotime($date);
            $orderOne = $this->DataControl->selectOne("select  *  from smc_payfee_order as  po  where  trading_pid = '{$trading_pid}' ");
            if ($orderOne) {
                $order_pay = $this->DataControl->selectClear("select  *  from  smc_payfee_order_pay where  order_pid ='{$orderOne['order_pid']}' and paytype_code='feewaiver'  ");
                if (count($order_pay) <> 1) {
                    $this->error = 1;
                    $this->errortip = "拥有多条支付记录,请检查";
                    return false;
                } else {
                    $order_payOne = $order_pay[0];
                }
                $arrrcoursebalance = $arrrcoursetime = $arr_order = $arr_pay = $arr_pay_log = $arr_tracks = $arr_trading = $arr_balancelog = array();
                $arr_order['order_createtime'] = $arr_pay['pay_createtime'] = $arr_pay['pay_successtime'] = $arr_pay_log['paylog_addtime'] = $arr_pay_log['paylog_paytime'] = $day_time;
                $arr_tracks['tracks_time'] = $arr_trading['trading_createtime'] = $arr_balancelog['balancelog_time'] = $day_time;
                $arrrcoursetime['timelog_time'] = $day_time;
                $arrrcoursebalance['log_time'] = $day_time;

            }
            $bool = $this->DataControl->updateData("smc_payfee_order_pay", "pay_id ='{$order_payOne['pay_id']}'", $arr_pay);
            if (!$bool) {
                $this->DataControl->rollback();
                $this->error = 1;
                $this->errortip = "更新支付记录失败";
                return false;
            }
            $bool = $this->DataControl->updateData("smc_payfee_order_paylog", "paylog_id ='{$order_payOne['pay_id']}'", $arr_pay_log);
            if (!$bool) {
                $this->DataControl->rollback();
                $this->error = 1;
                $this->errortip = "更新支付记录日志";
                return false;
            }
            $bool = $this->DataControl->updateData("smc_payfee_order_tracks", "order_pid ='{$orderOne['order_pid']}' and tracks_title='减免学费'", $arr_tracks);
            if (!$bool) {
                $this->DataControl->rollback();
                $this->error = 1;
                $this->errortip = "更新smc_payfee_order_tracks失败";
                return false;
            }


            $timeLog = $this->DataControl->selectClear("select * from  smc_student_coursebalance_timelog where  student_id='{$orderOne['student_id']}' and  timelog_playname='减免学费' ");
            $balanceLog = $this->DataControl->selectClear("select * from  smc_student_coursebalance_log where  student_id='{$orderOne['student_id']}' and  log_playname='减免学费' ");
            if (count($timeLog) <> 1 || count($balanceLog) <> 1) {
                debug($orderOne['student_id']);
                $this->error = 1;
                $this->errortip = "减免记录多条";
                return false;
            } else {
                $timeLogOne = $timeLog[0];
                $balanceLogOne = $balanceLog[0];
            }
            $bool = $this->DataControl->updateData("smc_student_coursebalance_timelog", "timelog_id ='{$timeLogOne['timelog_id']}' ", $arrrcoursetime);
            if (!$bool) {
                $this->DataControl->rollback();
                $this->error = 1;
                $this->errortip = "smc_student_coursebalance_timelog";
                return false;
            }
            $bool = $this->DataControl->updateData("smc_student_coursebalance_log", "log_id ='{$balanceLogOne['log_id']}' ", $arrrcoursebalance);
            if (!$bool) {
                $this->DataControl->rollback();
                $this->error = 1;
                $this->errortip = "更新smc_student_coursebalance_log失败";
                return false;
            }
            $this->DataControl->commit();
            $this->error = 0;
            $this->errortip = "成功";
            return false;


        }

    }


    function delStudentHourStudy($student_id, $class_id, $start_day, $end_day, $from = 0)
    {
        $this->error = 1;
        $this->error = "禁用";
        return false;

        $classOne = $this->DataControl->getOne('smc_class', "class_id='{$class_id}'");
        $sql = " select * from   smc_student_coursebalance_log where student_id='{$student_id}' and  class_id='{$classOne['course_id']}' order by log_id DESC  limit 0,1 
 ";
        $timeLogOne = $this->DataControl->selectOne($sql);


        $hourStudyOne = $this->DataControl->getOne('smc_student_hourstudy', "student_id='{$student_id}' and class_id='{$class_id}' ", "order by  hourstudy_id DESC");

        if ($timeLogOne && $hourStudyOne) {
            if ($timeLogOne['hourstudy_id'] <> $hourStudyOne['hourstudy_id']) {
                $this->error = 1;
                $this->errortip = "请检查考勤";
                return false;
            }
        } else {
            $hourList = $this->DataControl->selectClear("
                        select  h.hour_id,h.hour_day  from  smc_class_hour  as h  
                        left  join  smc_student_hourstudy as hy  ON  hy.student_id = '{$student_id}' and  hy.hour_id=h.hour_id
                        where  h.hour_day>='{$start_day}'  and  h.hour_day <='{$end_day}'  and  h.class_id='{$class_id}'  
                      
                ");
        }

        if ($hourList) {

            if ($from == 0) {
                debug($hourList);
                exit;
            }
            if ($from == 1) {
                $arr_hour_id = array_column($hourList, 'hour_id');
                if (!$arr_hour_id) {
                    $this->error = 1;
                    $this->error = "未有课次";
                    return false;
                } else {
                    $str_hour_id = implode($arr_hour_id, ',');

                    $sql = "update 
smc_student_coursebalance a 
inner join 
(
select y.school_id,y.course_id,x.student_id,count(x.hourstudy_id) as minus_times,ifnull(sum(z.income_price),0) as minus_price
from smc_student_hourstudy x  
left join smc_class y on x.class_id=y.class_id  
left join smc_school_income z on x.hourstudy_id=z.hourstudy_id and income_type=0
where x.class_id='{$class_id}' and hour_id in({$str_hour_id}) and x.student_id='{$student_id}'
group by y.school_id,y.course_id,x.student_id
) b on a.school_id=b.school_id and a.course_id=b.course_id and a.student_id=b.student_id
 set a.coursebalance_figure=a.coursebalance_figure+b.minus_price,a.coursebalance_time=a.coursebalance_time+b.minus_times
where b.student_id='{$student_id}'";

                    if ($this->DataControl->query($sql)) {
                        $sql_one = "DELETE from smc_student_coursebalance_timelog where hourstudy_id in (select hourstudy_id from smc_student_hourstudy where class_id='{$class_id}' and hour_id in({$str_hour_id})) and student_id='{$student_id}'";

                        $sql_two = "DELETE from smc_student_coursebalance_log where hourstudy_id in (select hourstudy_id from smc_student_hourstudy where class_id='{$class_id}' and hour_id in({$str_hour_id})) and student_id='{$student_id}'";

                        $sql_three = "DELETE from smc_school_income where hourstudy_id in (select hourstudy_id from smc_student_hourstudy where class_id='{$class_id}' and hour_id in({$str_hour_id})) and student_id='{$student_id}'";

                        $sql_foure = "DELETE from smc_student_hourstudy where class_id='{$class_id}' and hour_id in({$str_hour_id}) and student_id='{$student_id}';";

                        var_dump($this->DataControl->query($sql_one));
                        var_dump($this->DataControl->query($sql_two));
                        var_dump($this->DataControl->query($sql_three));
                        var_dump($this->DataControl->query($sql_foure));

                        $this->error = 0;
                        $this->errortip = "删除成功";
                        return true;
                        exit;

                    } else {
                        $this->error = 1;
                        $this->error = "更新主表失败";
                        return false;
                    }


                }
            }
        } else {
            $this->error = 1;
            $this->error = "未有课次";
            return false;
        }


    }


    /**
     * @param $paramArray
     * @return bool
     * 修正课次考勤信息数据
     */
    function EditHourDay($hour_id, $hour_day)
    {
        $hourOne = $this->DataControl->getFieldOne("smc_class_hour", "hour_id,hour_day,hour_ischecking", "hour_id = '{$hour_id}'");
        if ($hourOne) {
            if ($hourOne['hour_ischecking'] == '1') {
                if ($hour_day > date("Y-m-d", time())) {
                    $this->error = true;
                    $this->errortip = "不可以调整今天的课时";
                    return false;
                }
                if ($hourOne['hour_day'] == $hour_day) {
                    $this->error = true;
                    $this->errortip = "您需要调整的课时于实际课时一样，无需调整";
                    return false;
                }

                //调整上课日期
                $this->DataControl->query("UPDATE smc_class_hour SET hour_day = '{$hour_day}' WHERE hour_id = '{$hour_id}'");
                //调整收入时间
                $this->DataControl->query("UPDATE smc_school_income SET income_confirmtime = UNIX_TIMESTAMP('{$hour_day}')
WHERE hourstudy_id IN (
                    SELECT
			hourstudy_id
		FROM
			smc_student_hourstudy
		WHERE
			hour_id = '{$hour_id}'
	)");
                //调整学员考勤支出明细
                $this->DataControl->query("UPDATE smc_student_coursebalance_log SET log_time = UNIX_TIMESTAMP({$hour_day}) WHERE hourstudy_id IN (
		SELECT
			hourstudy_id
		FROM
			smc_student_hourstudy
		WHERE
			hour_id = '{$hour_id}'
	)");
                //调整学员考勤课次消费明细
                $this->DataControl->query("UPDATE smc_student_coursebalance_timelog SET timelog_time = UNIX_TIMESTAMP('{$hour_day}') WHERE hourstudy_id IN (
		SELECT
			hourstudy_id
		FROM
			smc_student_hourstudy
		WHERE
			hour_id = '{$hour_id}'
	)");
                //调整学员考勤明细
                $this->DataControl->query("UPDATE smc_student_clockinginlog SET clockinginlog_day = '{$hour_day}' WHERE hourstudy_id IN (
		SELECT
			hourstudy_id
		FROM
			smc_student_hourstudy
		WHERE
			hour_id = '{$hour_id}'
	)");

                $this->oktip = "课次信息修改时间成功";
                return true;
            } else {
                $this->error = true;
                $this->errortip = "课次未考勤无需修改";
                return false;
            }
        } else {
            $this->error = true;
            $this->errortip = "课次信息不存在";
            return false;
        }
    }


    /**
     * @param $paramArray
     * @return array
     * 修正课次考勤信息数据
     */
    function EditStudenEnrolleClassDay($student_branch, $class_branch, $enrolledDay)
    {
        $studentOne = $this->DataControl->getFieldOne("smc_student", "student_id,student_branch", "student_id = '{$student_branch}'");
        if ($studentOne) {
            $classOne = $this->DataControl->getFieldOne("smc_class", "class_id,class_branch", "class_branch = '{$class_branch}'");
            if (!$classOne) {
                $this->error = true;
                $this->errortip = "班级信息不存在";
                return false;
            }

            //修改入班时间
            $this->DataControl->query("UPDATE smc_student_study SET study_beginday = '{$enrolledDay}'
WHERE student_id = '{$studentOne['student_id']}' AND class_id = '{$classOne['class_id']}'");
            //修改异动时间
            $this->DataControl->query("UPDATE smc_student_change SET change_day = '{$enrolledDay}'
WHERE student_id = '{$studentOne['student_id']}' AND to_class_id = '{$classOne['class_id']}'");
            //修改异动时间
            $this->DataControl->query("UPDATE smc_student_changelog SET changelog_day = '{$enrolledDay}'
WHERE student_id = '{$studentOne['student_id']}' AND class_id = '{$classOne['class_id']}'");

            $this->oktip = "学员入班时间修改成功";
            return true;
        } else {
            $this->error = true;
            $this->errortip = "学员新不存在";
            return false;
        }
    }

    /**
     * 修正充值订单的时间
     * author: ling
     * 对应接口文档 0001
     * @param $trading_pid
     * @param $student
     */
    function EditStuOrdertCreateTime($trading_pid, $date)
    {
        $day_retime = date('Y-m-d', strtotime($date));

        if (!$date || $day_retime == '1970-01-01') {
            $this->error = 1;
            $this->errortip = "请选择正确的时间";
            return false;
        } else {
            $day_time = strtotime($date);
            $orderOne = $this->DataControl->selectOne("select  *  from smc_payfee_order as  po  where  trading_pid = '{$trading_pid}' ");
            if ($orderOne['order_type'] == 2) {
                $order_pay = $this->DataControl->selectClear("select  *  from  smc_payfee_order_pay where  order_pid ='{$orderOne['order_pid']}' ");
                $order_paylog = $this->DataControl->selectClear("select  *  from  smc_payfee_order_paylog where  order_pid ='{$orderOne['order_pid']}' ");
                if (count($order_pay) <> 1 || count($order_paylog) <> 1) {
                    $this->error = 1;
                    $this->errortip = "拥有多条支付记录,请检查";
                    return false;
                } else {
                    $order_payOne = $order_pay[0];
                    $order_paylogOne = $order_paylog[0];
                }
                $trackOne = $this->DataControl->selectOne("select  *  from  smc_payfee_order_tracks where  order_pid ='{$orderOne['order_pid']}' and tracks_title='账户充值'");
                if (!$trackOne) {
                    $this->error = 1;
                    $this->errortip = "没有track记录,请检查";
                    return false;
                }
                $stuTracking = $this->DataControl->selectOne("select  *  from  smc_student_trading where  trading_pid ='{$trading_pid}'");
                if (!$stuTracking) {
                    $this->error = 1;
                    $this->errortip = "没有学员记录记录smc_student_trading,请检查";
                    return false;
                }

                $stuBalanceOne = $this->DataControl->selectOne("select  *  from  smc_student_balancelog where  school_id ='{$orderOne['school_id']}' and student_id='{$orderOne['student_id']}' and trading_pid ='{$trading_pid}'  ");
                if (!$stuBalanceOne) {
                    $this->error = 1;
                    $this->errortip = "没有学员账户记录smc_student_balancelog";
                    return false;
                }

                //更新时间
                $arr_order = $arr_pay = $arr_pay_log = $arr_tracks = $arr_trading = $arr_balancelog = array();
                $arr_order['order_createtime'] = $arr_pay['pay_createtime'] = $arr_pay['pay_successtime'] = $arr_pay_log['paylog_addtime'] = $arr_pay_log['paylog_paytime'] = $day_time;
                $arr_tracks['tracks_time'] = $arr_trading['trading_createtime'] = $arr_balancelog['balancelog_time'] = $day_time;

                $this->DataControl->begintransaction();
                $bool = $this->DataControl->updateData("smc_payfee_order", "order_pid ='{$orderOne['order_pid']}'", $arr_order);
                if (!$bool) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新主表失败";
                    return false;
                }
                $bool = $this->DataControl->updateData("smc_payfee_order_pay", "pay_id ='{$order_payOne['pay_id']}'", $arr_pay);
                if (!$bool) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新支付记录失败";
                    return false;
                }
                $bool = $this->DataControl->updateData("smc_payfee_order_paylog", "paylog_id ='{$order_paylogOne['pay_id']}'", $arr_pay_log);
                if (!$bool) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新支付记录日志";
                    return false;
                }
                $bool = $this->DataControl->updateData("smc_payfee_order_tracks", "order_pid ='{$orderOne['order_pid']}'", $arr_tracks);
                if (!$bool) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新smc_payfee_order_tracks失败";
                    return false;
                }
                $bool = $this->DataControl->updateData("smc_student_trading", "trading_id ='{$stuTracking['trading_id']}'", $arr_trading);
                if (!$bool) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新smc_student_trading失败";
                    return false;
                }

                $bool = $this->DataControl->updateData("smc_student_balancelog", "balancelog_id ='{$stuBalanceOne['balancelog_id']}'", $arr_balancelog);
                if (!$bool) {
                    $this->DataControl->rollback();
                    $this->error = 1;
                    $this->errortip = "更新smc_student_balancelog失败";
                    return false;
                }
                $this->DataControl->commit();
                $this->error = 0;
                $this->errortip = "更新成功";
                return false;

            } else {
                $this->error = 1;
                $this->errortip = "非充值类订单";
                return false;
            }
        }
    }
}











