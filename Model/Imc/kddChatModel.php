<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/3
 * Time: 15:21
 */
namespace Model\Api;

class kddChatModel extends \Model\modelTpl{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $appId = 'wx2a66618e4feffded';
    public $appSecret = '30983395129d5ee5ee76d27a79cea406';
    public $stafferOne = array();

    function __construct() {
        parent::__construct ();
    }

    //获取微信令牌（component_access_token）是第三方平台接口的调用凭据
    function getComponentAccessToken()
    {
        $token = $this->DataControl->getFieldOne("imc_weixin_token", "token_failuretime,token_string", "token_type = '0'", "order by token_failuretime DESC limit 0,1");
        if ($token && $token['token_failuretime'] > time()) {
            $wxtoken = array();
            $wxtoken['access_token'] = $token['token_string'];
            $wxtoken['expires_in'] = 7200;
            return $wxtoken;
        } else {
            $paramarray = array(
                'component_appid' => $this->appId,
                'component_appsecret' => $this->appSecret,
                'component_verify_ticket' => "client_credential"
            );
            $getBakurl = request_by_curl("https://api.weixin.qq.com/cgi-bin/component/api_component_token", dataEncode($paramarray), "GET");
            $json_play = new \Webjson();
            $cardarray = $json_play->decode($getBakurl, "1");
            $data = array();
            $data['token_site'] = '1';
            $data['token_type'] = '1';
            $data['token_string'] = $cardarray['access_token'];
            $data['token_failuretime'] = time() + $cardarray['expires_in'];
            $this->DataControl->insertData("imc_weixin_token", $data);
            return $cardarray;
        }
    }


}