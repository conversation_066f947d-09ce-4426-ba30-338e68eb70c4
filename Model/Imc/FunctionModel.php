<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 1:07
 */

namespace Model\Imc;

class  FunctionModel extends modelTpl
{
	public $m;
	function __construct()
	{
		parent::__construct();
	}
	

	/**
	 * @param $paramArray
	 * @return array
	 * 获取客户线索列表
	 */
	function getFunctionField($funcString)
	{
		$functionOne = $this->DataControl->getFieldOne("imc_module_function","function_id,function_fieldjson","function_markstring='{$funcString}'");
        if($functionOne){
            $fieldArray = json_decode($functionOne['function_fieldjson'],true);
            return $fieldArray;
        }else{
            return false;
        }
	}
	
}